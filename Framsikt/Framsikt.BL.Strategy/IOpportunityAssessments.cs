using Framsikt.BL.Helpers;
using Framsikt.BL.Strategy.Helpers;
using Newtonsoft.Json.Linq;
using KeyValuePair = Framsikt.BL.Helpers.KeyValuePair;

namespace Framsikt.BL.Strategy
{
    public interface IOpportunityAssessments
    {
        List<ServiceIdValue> getServiceIdDropDownData(string userId, string orgId, int budgetYear, int orgIdlevelNo);

        List<AssessmentDropdownHelper> GetAssessmentDropdown(string userId, string orgId, int orgLevel, int budgetYear, string serviceId, int serviceLevel);

        AssessmentAreaDescriptionObject getAssessmentAreaDescription(string userId, AssessmentAreaContentHelper assessmentAreaSpecificData);

        void saveAssessmentAreaDescription(string userId, AssessmentAreaContentHelper assessmentAreaSpecificData);

        void saveAssessmentAreaActionPopupData(string userId, SaveAssessmentAreaActionHeaderPopupHelper inputObj);

        void DeleteOpportunityAssessmentAreaAction(string userId, AssessmentAreaActionDeleteHelper assessmentAreaActionDeleteInput);

        List<KeyValuePair> getColumnSelectorForAssessmentAreaActionDynamicGrid(string userId, int budgetYear, List<GridColumnHelper> allColumns);

        void saveColumnSelectorForAssessmentAreaActionDynamicGrid(string userId, List<KeyValuePair> invCols, int budgetYear, bool isTenantSpecificSave);

        AssessmentSummeryGridHelper GetAssessmentSummeryData(string userId, int budgetYear, Guid assessmentId);

        Task<AssessmentSummeryGridHelper> GetAssessmentActionSummeryData(string userId, int budgetYear, Guid assessmentId);

        List<KeyValueInt> GetAssessmentStatusDropdown(string userId);

        void SaveAssessmentStatus(string userId, GetAssessmentContentHelper inputObj);

        void SaveAssessmentVisibility(string userId, GetAssessmentContentHelper inputObj);

        Task<AssessmentGridData> GetAssessmentData(string userId, string orgId, int orgLevel, int budgetYear, string serviceId, int serviceLevel, Guid assessmentId);

        Task<AssessmentDescriptionHelper> GetAssessmentDescription(string userId, string orgId, int orgLevel, int budgetYear, string serviceId, int serviceLevel, Guid assessmentId);

        Task<AssessmentDescriptionHelper> GetAssessmentOwnerDescription(string userId, string orgId, int orgLevel, int budgetYear, string serviceId, int serviceLevel, Guid assessmentId);

        Task SaveAssessmentDescription(string userId, AssessmentDescriptionSaveHelper inputObj);

        Task<List<AssessmentAreaActionDataGrid>> getAssessmentAreaData(string userId, AssessmentAreaContentHelper assessmentAreaSpecificData);

        Task<AssessmentAreaActionHeaderPopupHelper> getAssessmentAreaActionPopupData(string userId, AssessmentAreaActionInputPopupHelper inputObj);

        bool ShowOpportunityAssessmentSummeryTab(string userId);

        Task<JObject> GetDelegatedAssessmentsDescriptionsData(string userId, AssessmentOrgInputHelper input);

        List<KeyValue> GetOrgDropdownValues(string userId, int budgetYear);

        JObject ShowAssesmentContent(string userId, int orgLevel, string orgId, int isVisible);

        Task<List<AssessmentDelegatedOrgHelper>> GetAssessmentDelegatedOrgList(string userId, Guid assessmentId, int budgetYear, int? status);

        AssessmentPageTabHelper GetAssessmentDataDocTab(string userId, string orgId, int orgLevel, int budgetYear);

        void DeletePublishWebsite(string name, int configId, string type);
    }
}