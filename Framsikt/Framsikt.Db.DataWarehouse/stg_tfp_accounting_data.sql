CREATE TABLE [dbo].[stg_tfp_accounting_data]
(
	[pk_id] [int] NOT NULL,
	[fk_tenant_id] [int] NULL,
	[fk_account_code] [nvarchar](25) NULL,
	[department_code] [nvarchar](25) NULL DEFAULT '',
	[fk_function_code] [nvarchar](25) NULL DEFAULT '',
	[fk_project_code] [nvarchar](25) NULL DEFAULT '',
	[asset_code] [nvarchar](25) NULL DEFAULT '',
	[free_dim_1] [nvarchar](25) NULL DEFAULT '',
	[free_dim_2] [nvarchar](25) NULL DEFAULT '',
	[free_dim_3] [nvarchar](25) NULL DEFAULT '',
	[free_dim_4] [nvarchar](25) NULL DEFAULT '',
	[description] [nvarchar](255) NULL DEFAULT '',
	[gl_year] [int] NULL,
	[period] [int] NULL,
	[amount] [decimal](18, 2) NULL,
	[fk_prog_code] [nvarchar](25) NULL DEFAULT '',
	[sys_change_version] BIGINT NULL,
    [sys_change_operation] VARCHAR(5) NULL
)
WITH
(
    DISTRIBUTION = ROUND_ROBIN,
    CLUSTERED COLUMNSTORE INDEX
)
GO

