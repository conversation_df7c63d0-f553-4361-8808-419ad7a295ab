CREATE PROC [dbo].[delta_tbu_trans_detail] @db_name [NVARCHAR](25),@environment [NVARCHAR](25) AS

--Calculate table with change values
PRINT 'Start procedure - calculate delta and insert into temp table'  + convert(varchar(400),GETDATE())

DECLARE @sql_code1 NVARCHAR(4000) = (SELECT
'
select b.*
INTO #temp_tbu_in_dw
from [dbo].[delta_'+@db_name+'_tbu_trans_detail] a
JOIN [dbo].[full_'+@db_name+'_tbu_trans_detail] b on a.c_pk_id = b.pk_id

select *
INTO #temp_tbu_delta
from [dbo].[delta_'+@db_name+'_tbu_trans_detail] 
where pk_id IS NOT NULL

select *
INTO #temp_delta_calc
FROM
(
	select
				[fk_tenant_id]
			   ,[fk_account_code]
			   ,[department_code]
			   ,[fk_function_code]
			   ,[fk_project_code]
			   ,[free_dim_1]
			   ,[free_dim_2]
			   ,[free_dim_3]
			   ,[free_dim_4]
			   ,[resource_id]
			   ,[fk_employment_id]
			   ,[description]
			   ,[budget_year]
			   ,[period]
			   ,[fk_key_id]
			   ,[fk_action_id]
			   ,[fk_adjustment_code]
			   ,[fk_alter_code]
			   ,amount_year_1_delta = SUM(amount_year_1)
			   ,amount_year_1_old = 0
	from #temp_tbu_delta
	GROUP BY [fk_tenant_id],[fk_account_code],[department_code],[fk_function_code],[fk_project_code],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4]
			   ,[resource_id],[fk_employment_id],[description],[budget_year],[period],[fk_key_id],[fk_action_id],[fk_adjustment_code],[fk_alter_code]

	UNION ALL

	select
				[fk_tenant_id]
			   ,[fk_account_code]
			   ,[department_code]
			   ,[fk_function_code]
			   ,[fk_project_code]
			   ,[free_dim_1]
			   ,[free_dim_2]
			   ,[free_dim_3]
			   ,[free_dim_4]
			   ,[resource_id]
			   ,[fk_employment_id]
			   ,[description]
			   ,[budget_year]
			   ,[period]
			   ,[fk_key_id]
			   ,[fk_action_id]
			   ,[fk_adjustment_code]
			   ,[fk_alter_code]
			   ,amount_year_1_delta = 0
			   ,amount_year_1_old = SUM(amount_year_1)
	from #temp_tbu_in_dw
	GROUP BY [fk_tenant_id],[fk_account_code],[department_code],[fk_function_code],[fk_project_code],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4]
			   ,[resource_id],[fk_employment_id],[description],[budget_year],[period],[fk_key_id],[fk_action_id],[fk_adjustment_code],[fk_alter_code]
) CALC
')

--Insert to tbu_trans_detail the changes and update the full table
EXEC    sp_executesql @sql_code1;

PRINT 'Delta done calculating, starting insert into tbu_trans and the raw DB tables'  + convert(varchar(400),GETDATE())

DECLARE @sql_code2 NVARCHAR(4000) = (SELECT
'
INSERT INTO ['+@environment+'].[tbu_trans_detail]
([db_name],[fk_tenant_id],[fk_account_code],[department_code],[fk_function_code],[fk_project_code],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4],[resource_id],[fk_employment_id],[description],[budget_year],[period]
,amount_year_1,[fk_key_id],[fk_action_id],[fk_adjustment_code],[fk_alter_code])
select		[db_name] = '''+@db_name+'''
           ,[fk_tenant_id]
           ,[fk_account_code]
           ,[department_code]
           ,[fk_function_code]
           ,[fk_project_code]
           ,[free_dim_1]
           ,[free_dim_2]
           ,[free_dim_3]
           ,[free_dim_4]
           ,[resource_id]
           ,[fk_employment_id]
           ,[description]
           ,[budget_year]
           ,[period]
		   ,amount_year_1 = SUM(amount_year_1_delta-amount_year_1_old)
           ,[fk_key_id]
           ,[fk_action_id]
           ,[fk_adjustment_code]
           ,[fk_alter_code]
FROM #temp_delta_calc
GROUP BY [fk_tenant_id],[fk_account_code],[department_code],[fk_function_code],[fk_project_code],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4]
           ,[resource_id],[fk_employment_id],[description],[budget_year],[period],[fk_key_id],[fk_action_id],[fk_adjustment_code],[fk_alter_code]
HAVING SUM(amount_year_1_delta-amount_year_1_old) <> 0


UPDATE STATISTICS ['+@environment+'].[tbu_trans_detail]

delete b
from [dbo].[delta_'+@db_name+'_tbu_trans_detail] a
JOIN [dbo].[full_'+@db_name+'_tbu_trans_detail] b on a.c_pk_id = b.pk_id

INSERT [dbo].[full_'+@db_name+'_tbu_trans_detail]
select [pk_id],[bu_trans_id],[fk_tenant_id],[action_type],[line_order],[fk_account_code],[department_code],[fk_function_code],[fk_project_code],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4]
           ,[resource_id],[fk_employment_id],[description],[budget_year],[period],[budget_type],[amount_year_1],[amount_year_2],[amount_year_3],[amount_year_4],[fk_key_id],[updated],[updated_by]
           ,[allocation_pct],[total_amount],[tax_flag],[holiday_flag],[fk_pension_type],[fk_action_id],[fk_investment_id],[fk_portfolio_code],[fk_prog_code],[fk_adjustment_code],[fk_alter_code]
           ,[change_flag],[parent_bu_trans_id]
from [dbo].[delta_'+@db_name+'_tbu_trans_detail]
WHERE SYS_CHANGE_OPERATION IN (''U'',''I'')

--Update statistic on the updated table
UPDATE STATISTICS [dbo].[full_'+@db_name+'_tbu_trans_detail]
'

)

EXEC    sp_executesql @sql_code2;

PRINT 'Starting insert into period table '  + convert(varchar(400),GETDATE())

SET @sql_code2 =
'
INSERT INTO ['+@environment+'].[tbu_trans_detail_period]
           ([db_name]
           ,[fk_tenant_id]
           ,[fk_account_code]
           ,[department_code]
           ,[fk_function_code]
           ,[fk_project_code]
           ,[free_dim_1]
           ,[free_dim_2]
           ,[free_dim_3]
           ,[free_dim_4]
           ,[budget_year]
           ,[period]
           ,[fk_adjustment_code]
           ,[amount_period]
           ,[amount_ytd])
select		[db_name] = '''+@db_name+'''
           ,[fk_tenant_id]
           ,[fk_account_code]
           ,[department_code]
           ,[fk_function_code]
           ,[fk_project_code]
           ,[free_dim_1]
           ,[free_dim_2]
           ,[free_dim_3]
           ,[free_dim_4]
           ,[budget_year]
           ,[period]
		   ,[fk_adjustment_code]
           ,amount_period = SUM(amount_year_1_delta-amount_year_1_old)
		   ,amount_ytd = 0
FROM #temp_delta_calc
GROUP BY  [fk_tenant_id],[fk_account_code],[department_code],[fk_function_code],[fk_project_code],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4],[budget_year],[period],[fk_adjustment_code]
HAVING SUM(amount_year_1_delta-amount_year_1_old) <> 0
'
EXEC    sp_executesql @sql_code2;

--Generate full period set for use in YTD calculation
--First need to make sure everything is grouped nicely
select [fk_tenant_id], [fk_account_code], [department_code], [fk_function_code], [fk_project_code], [free_dim_1], [free_dim_2], [free_dim_3], [free_dim_4], [budget_year],[fk_adjustment_code], [period]
,amount_period = SUM(amount_year_1_delta-amount_year_1_old)
INTO #temp_ytd_calc
from #temp_delta_calc
GROUP BY [fk_tenant_id], [fk_account_code], [department_code], [fk_function_code], [fk_project_code], [free_dim_1], [free_dim_2], [free_dim_3], [free_dim_4], [budget_year],[fk_adjustment_code], [period]

--Then generate the full sett with all periods
select a.[fk_tenant_id], a.[fk_account_code], a.[department_code], a.[fk_function_code], a.[fk_project_code], a.[free_dim_1], a.[free_dim_2], a.[free_dim_3], a.[free_dim_4], a.[budget_year],a.[fk_adjustment_code]
,b.period
,amount_period = ISNULL(c.amount_period,0)
INTO #tbu_period_ytd_calc
from
(
select [fk_tenant_id], [fk_account_code], [department_code], [fk_function_code], [fk_project_code], [free_dim_1], [free_dim_2], [free_dim_3], [free_dim_4], [budget_year],[fk_adjustment_code]
from #temp_ytd_calc
GROUP BY [fk_tenant_id], [fk_account_code], [department_code], [fk_function_code], [fk_project_code], [free_dim_1], [free_dim_2], [free_dim_3], [free_dim_4], [budget_year],[fk_adjustment_code]
) A
JOIN [dbo].[gco_dw_period_list] b on a.budget_year = b.budget_year
LEFT JOIN #temp_ytd_calc c ON
a.[fk_tenant_id]		= c.[fk_tenant_id]		 AND	
a.[fk_account_code]		= c.[fk_account_code]	 AND	
a.[department_code]		= c.[department_code]	 AND	
a.[fk_function_code]	= c.[fk_function_code]	 AND
a.[fk_project_code]		= c.[fk_project_code]	 AND	
a.[free_dim_1]			= c.[free_dim_1]		 AND	
a.[free_dim_2]			= c.[free_dim_2]		 AND	
a.[free_dim_3]			= c.[free_dim_3]		 AND	
a.[free_dim_4]			= c.[free_dim_4]		 AND	
a.[budget_year]			= c.[budget_year]		 AND	
a.[fk_adjustment_code]	= c.[fk_adjustment_code] AND
b.[period]			    = c.period

--Then finally calculate the YTD amounts to be inserted
select
[fk_tenant_id], [fk_account_code], [department_code], [fk_function_code], [fk_project_code], [free_dim_1], [free_dim_2], [free_dim_3], [free_dim_4], [budget_year], [period]
,[fk_adjustment_code]
,amount_period = 0
,amount_ytd =  SUM(amount_period) OVER (PARTITION BY [fk_tenant_id], [fk_account_code], [department_code], [fk_function_code], [fk_project_code], [free_dim_1], [free_dim_2], [free_dim_3], [free_dim_4], [budget_year],[fk_adjustment_code] ORDER BY period)
INTO #temp_period_ytd
FROM  #tbu_period_ytd_calc


SET @sql_code2 =
'
INSERT INTO ['+@environment+'].[tbu_trans_detail_period]
           ([db_name]
           ,[fk_tenant_id]
           ,[fk_account_code]
           ,[department_code]
           ,[fk_function_code]
           ,[fk_project_code]
           ,[free_dim_1]
           ,[free_dim_2]
           ,[free_dim_3]
           ,[free_dim_4]
           ,[budget_year]
           ,[period]
           ,[fk_adjustment_code]
           ,[amount_period]
           ,[amount_ytd])
select		[db_name] = '''+@db_name+'''
           ,[fk_tenant_id]
           ,[fk_account_code]
           ,[department_code]
           ,[fk_function_code]
           ,[fk_project_code]
           ,[free_dim_1]
           ,[free_dim_2]
           ,[free_dim_3]
           ,[free_dim_4]
           ,[budget_year]
           ,[period]
           ,[fk_adjustment_code]
           ,[amount_period]
           ,[amount_ytd]
FROM  #temp_period_ytd
WHERE amount_ytd != 0

--Update statistic on the updated table
UPDATE STATISTICS ['+@environment+'].[tbu_trans_detail_period]
'

EXEC    sp_executesql @sql_code2;


PRINT 'Starting insert into year table '  + convert(varchar(400),GETDATE())

SET @sql_code2 =
'
INSERT INTO ['+@environment+'].[tbu_trans_detail_year]
           ([db_name]
           ,[fk_tenant_id]
           ,[fk_account_code]
           ,[department_code]
           ,[fk_function_code]
           ,[fk_project_code]
           ,[free_dim_1]
           ,[free_dim_2]
           ,[free_dim_3]
           ,[free_dim_4]
           ,[budget_year]
           ,[fk_adjustment_code]
           ,[amount_year_1])

SELECT		[db_name] = '''+@db_name+'''
           ,[fk_tenant_id]
           ,[fk_account_code]
           ,[department_code]
           ,[fk_function_code]
           ,[fk_project_code]
           ,[free_dim_1]
           ,[free_dim_2]
           ,[free_dim_3]
           ,[free_dim_4]
           ,[budget_year]
           ,[fk_adjustment_code]
		   ,amount_year_1 = SUM(amount_year_1_delta-amount_year_1_old)
FROM #temp_delta_calc
GROUP BY    [fk_tenant_id],[fk_account_code],[department_code],[fk_function_code],[fk_project_code],[free_dim_1],[free_dim_2],[free_dim_3],[free_dim_4],[budget_year],[fk_adjustment_code]
HAVING SUM(amount_year_1_delta-amount_year_1_old) <> 0


--Update statistic on the updated table
UPDATE STATISTICS ['+@environment+'].[tbu_trans_detail_year]

'
EXEC    sp_executesql @sql_code2;
