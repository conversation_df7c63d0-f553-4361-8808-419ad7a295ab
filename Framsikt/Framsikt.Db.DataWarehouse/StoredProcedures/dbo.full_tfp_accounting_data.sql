CREATE PROC [dbo].[full_tfp_accounting_data] @environment [NVARCHAR](25),@table_catalog [NVARCHAR](50) AS

CREATE TABLE #temp_tfp_accounting_data
(
	[db_name] [nvarchar](25) NOT NULL,
	[pk_id] [int] NOT NULL,
	[fk_tenant_id] [int] NOT NULL,
	[fk_account_code] [nvarchar](25) NOT NULL,
	[department_code] [nvarchar](25) NOT NULL,
	[fk_function_code] [nvarchar](25) NOT NULL,
	[fk_project_code] [nvarchar](25) NOT NULL,
	[free_dim_1] [nvarchar](25) NOT NULL,
	[free_dim_2] [nvarchar](25) NOT NULL,
	[free_dim_3] [nvarchar](25) NOT NULL,
	[free_dim_4] [nvarchar](25) NOT NULL,
	[description] [nvarchar](255) NOT NULL,
	[gl_year] [int] NOT NULL,
	[period] [int] NOT NULL,
	[amount] [decimal](18, 2) NOT NULL

)


--First, create a temporary table containing a unique row number used to identify the individual statements:
CREATE TABLE #years
WITH
( DISTRIBUTION = ROUND_ROBIN
)
AS
SELECT  ROW_NUMBER() OVER(ORDER BY (SELECT NULL)) AS Sequence
,		[db_name]
,		sql_code =
'INSERT #temp_tfp_accounting_data
select
			[db_name] = '''+ [db_name] +'''
			,[pk_id] 
			,[fk_tenant_id] 
			,[fk_account_code] 
			,[department_code] 
			,[fk_function_code] 
			,[fk_project_code] 
			,[free_dim_1] 
			,[free_dim_2] 
			,[free_dim_3] 
			,[free_dim_4] 
			,[description] 
			,[gl_year] 
			,[period]
			,[amount]

from [dbo].[full_'+[db_name]+'_tfp_accounting_data]
'
from [dbo].[gco_dw_db_list]
WHERE environment = @environment
AND [full_active] = 1
;


--Second, initialize the variables required to perform the loop:
DECLARE @nbr_years INT = (SELECT COUNT(*) FROM #years)
,       @y INT = 1
;

--Now loop over statements executing them one at a time:
WHILE   @y <= @nbr_years
BEGIN
    DECLARE @sql_code NVARCHAR(4000) = (SELECT sql_code FROM #years WHERE Sequence = @y);
    EXEC    sp_executesql @sql_code;
    SET     @y +=1;

END

--Finally drop the temporary table created in the first step
DROP TABLE #years;

PRINT 'Group data to prepare for CTAS'  + convert(varchar(400),GETDATE())
--Group to prepare for CTAS
SELECT		 [db_name] 
			,[fk_tenant_id] 
			,[fk_account_code] 
			,[department_code] 
			,[fk_function_code] 
			,[fk_project_code] 
			,[free_dim_1] 
			,[free_dim_2] 
			,[free_dim_3] 
			,[free_dim_4] 
			,[description] 
			,[gl_year] 
			,[period]
			,[amount] = SUM(amount)
INTO #temp_tfp_acc_grouped
FROM #temp_tfp_accounting_data
GROUP BY [db_name] ,[fk_tenant_id] ,[fk_account_code] ,[department_code] ,[fk_function_code] ,[fk_project_code] ,[free_dim_1] ,[free_dim_2] ,[free_dim_3] ,[free_dim_4] ,[description] ,[gl_year] ,[period]

--CTAS new table
IF	(
		SELECT COUNT(*)
		FROM INFORMATION_SCHEMA.TABLES
		WHERE TABLE_CATALOG = @table_catalog
		AND TABLE_TYPE = 'BASE TABLE'
		AND TABLE_SCHEMA = @environment
		and table_name = 'tfp_accounting_data_full' 
	)	> 0
BEGIN
SET @sql_code = 'DROP TABLE ['+@environment+'].[tfp_accounting_data_full]'
EXEC    sp_executesql @sql_code
END

PRINT 'CTAS into new tfp_accounting_data_full'  + convert(varchar(400),GETDATE())

SET @sql_code = '
CREATE TABLE ['+@environment+'].[tfp_accounting_data_full]
WITH
(
       DISTRIBUTION = ROUND_ROBIN,
       CLUSTERED COLUMNSTORE INDEX
)
AS SELECT * FROM #temp_tfp_acc_grouped
'
EXEC    sp_executesql @sql_code

PRINT 'Applying indexes and update statistics on new tfp_accounting_data_full'  + convert(varchar(400),GETDATE())

SET @sql_code = 'CREATE NONCLUSTERED INDEX [ind_account] ON ['+@environment+'].[tfp_accounting_data_full] ([fk_tenant_id] ASC,	[gl_year] ASC,	[fk_account_code] ASC)'
EXEC    sp_executesql @sql_code
SET @sql_code = 'CREATE NONCLUSTERED INDEX [ind_department] ON ['+@environment+'].[tfp_accounting_data_full] ([fk_tenant_id] ASC,[gl_year] ASC,[department_code] ASC)'
EXEC    sp_executesql @sql_code
SET @sql_code = 'CREATE NONCLUSTERED INDEX [ind_function] ON ['+@environment+'].[tfp_accounting_data_full] ([fk_tenant_id] ASC,[gl_year] ASC,[fk_function_code] ASC)'
EXEC    sp_executesql @sql_code
SET @sql_code = 'CREATE NONCLUSTERED INDEX [ind_period] ON ['+@environment+'].[tfp_accounting_data_full] ([fk_tenant_id] ASC,[gl_year] ASC,[period] ASC)'
EXEC    sp_executesql @sql_code

SET @sql_code = 'UPDATE STATISTICS ['+@environment+'].[tfp_accounting_data_full]'
EXEC    sp_executesql @sql_code


--Rename old table if exists
IF	(
		SELECT COUNT(*)
		FROM INFORMATION_SCHEMA.TABLES
		WHERE TABLE_CATALOG = @table_catalog
		AND TABLE_TYPE = 'BASE TABLE'
		AND TABLE_SCHEMA = @environment
		and table_name = 'tfp_accounting_data' 
	)	> 0
BEGIN
SET @sql_code = 'RENAME OBJECT ['+@environment+'].[tfp_accounting_data] TO [tfp_accounting_data_Old]'
EXEC    sp_executesql @sql_code
END

--Rename new table to actual
SET @sql_code = 'RENAME OBJECT ['+@environment+'].[tfp_accounting_data_full] TO [tfp_accounting_data]'
EXEC    sp_executesql @sql_code

--Drop old table
IF	(
		SELECT COUNT(*)
		FROM INFORMATION_SCHEMA.TABLES
		WHERE TABLE_CATALOG = @table_catalog
		AND TABLE_TYPE = 'BASE TABLE'
		AND TABLE_SCHEMA = @environment
		and table_name = 'tfp_accounting_data_Old' 
	)	> 0
BEGIN
SET @sql_code = 'DROP TABLE ['+@environment+'].tfp_accounting_data_Old'
EXEC    sp_executesql @sql_code
END

PRINT 'New tbu_trans_table done and swapped'  + convert(varchar(400),GETDATE())

/*************** CREATE PERIOD TABLE ***************/

CREATE TABLE #tfp_acc_period
(
       [db_name] [nvarchar](25) NOT NULL,
       [fk_tenant_id] [int] NOT NULL,
       [fk_account_code] [nvarchar](25) NOT NULL,
       [department_code] [nvarchar](25) NOT NULL,
       [fk_function_code] [nvarchar](25) NOT NULL,
       [fk_project_code] [nvarchar](25) NOT NULL,
       [free_dim_1] [nvarchar](25) NOT NULL,
       [free_dim_2] [nvarchar](25) NOT NULL,
       [free_dim_3] [nvarchar](25) NOT NULL,
       [free_dim_4] [nvarchar](25) NOT NULL,
       [gl_year] [int] NOT NULL,
       [period] [int] NOT NULL,
       [amount_period] [decimal](38, 2) NULL,
       [amount_ytd] [decimal](38, 2) NULL
)

PRINT 'Calculate period data into temp period table'  + convert(varchar(400),GETDATE())

--Fetch period data

INSERT #tfp_acc_period
SELECT [db_name], [fk_tenant_id], [fk_account_code], [department_code], [fk_function_code], [fk_project_code], [free_dim_1], [free_dim_2], [free_dim_3], [free_dim_4], [gl_year], [period]
, amount_period = SUM([amount])
,amount_ytd = 0
FROM #temp_tfp_accounting_data
GROUP BY [db_name], [fk_tenant_id], [fk_account_code], [department_code], [fk_function_code], [fk_project_code], [free_dim_1], [free_dim_2], [free_dim_3], [free_dim_4], [gl_year], [period]


PRINT 'Period done, start calculate YTD data into temp period table'  + convert(varchar(400),GETDATE())

--Generate full period set for use in YTD calculation
select a.[db_name],a.[fk_tenant_id], a.[fk_account_code], a.[department_code], a.[fk_function_code], a.[fk_project_code], a.[free_dim_1], a.[free_dim_2], a.[free_dim_3], a.[free_dim_4], a.[gl_year]
,b.period
,amount_period = ISNULL(c.amount_period,0)
INTO #tfp_period_ytd_calc
from
(
select [db_name],[fk_tenant_id], [fk_account_code], [department_code], [fk_function_code], [fk_project_code], [free_dim_1], [free_dim_2], [free_dim_3], [free_dim_4], [gl_year]
from #tfp_acc_period
GROUP BY [db_name],[fk_tenant_id], [fk_account_code], [department_code], [fk_function_code], [fk_project_code], [free_dim_1], [free_dim_2], [free_dim_3], [free_dim_4], [gl_year]
) A
JOIN [dbo].[gco_dw_period_list] b on a.[gl_year] = b.[budget_year]
LEFT JOIN #tfp_acc_period c ON
a.[db_name]				= c.[db_name]			 AND
a.[fk_tenant_id]		= c.[fk_tenant_id]		 AND	
a.[fk_account_code]		= c.[fk_account_code]	 AND	
a.[department_code]		= c.[department_code]	 AND	
a.[fk_function_code]	= c.[fk_function_code]	 AND
a.[fk_project_code]		= c.[fk_project_code]	 AND	
a.[free_dim_1]			= c.[free_dim_1]		 AND	
a.[free_dim_2]			= c.[free_dim_2]		 AND	
a.[free_dim_3]			= c.[free_dim_3]		 AND	
a.[free_dim_4]			= c.[free_dim_4]		 AND	
a.[gl_year]				= c.[gl_year]		 AND	
b.[period]			    = c.period


--Fetch YTD Data
INSERT #tfp_acc_period
select
[db_name], [fk_tenant_id], [fk_account_code], [department_code], [fk_function_code], [fk_project_code], [free_dim_1], [free_dim_2], [free_dim_3], [free_dim_4], [gl_year], [period]
,amount_period = 0
,amount_ytd =  SUM(amount_period) OVER (PARTITION BY [db_name], [fk_tenant_id], [fk_account_code], [department_code], [fk_function_code], [fk_project_code], [free_dim_1], [free_dim_2], [free_dim_3], [free_dim_4], [gl_year] ORDER BY period)
FROM  #tfp_period_ytd_calc


--Create the new table and apply indexes
PRINT 'Period and YTD done, CTAS period table starts'  + convert(varchar(400),GETDATE())

IF	(
		SELECT COUNT(*)
		FROM INFORMATION_SCHEMA.TABLES
		WHERE TABLE_CATALOG = @table_catalog
		AND TABLE_TYPE = 'BASE TABLE'
		AND TABLE_SCHEMA = @environment
		and table_name = 'tfp_accounting_data_period_rebuild' 
	)	> 0
BEGIN
SET @sql_code = 'DROP TABLE ['+@environment+'].[tfp_accounting_data_period_rebuild]'
EXEC    sp_executesql @sql_code
END


SET @sql_code = '
CREATE TABLE ['+@environment+'].[tfp_accounting_data_period_rebuild]
WITH
(
       DISTRIBUTION = ROUND_ROBIN,
       CLUSTERED COLUMNSTORE INDEX
)
AS
select [db_name], [fk_tenant_id], [fk_account_code], [department_code], [fk_function_code], [fk_project_code], [free_dim_1], [free_dim_2], [free_dim_3], [free_dim_4], [gl_year], [period]
,amount_period = SUM(amount_period)
,amount_ytd = SUM(amount_ytd)
from #tfp_acc_period
GROUP BY [db_name], [fk_tenant_id], [fk_account_code], [department_code], [fk_function_code], [fk_project_code], [free_dim_1], [free_dim_2], [free_dim_3], [free_dim_4], [gl_year], [period]
'
EXEC    sp_executesql @sql_code

PRINT 'Starting index and update statistics on new period table'  + convert(varchar(400),GETDATE())

SET @sql_code = 'CREATE NONCLUSTERED INDEX [ind_account] ON ['+@environment+'].[tfp_accounting_data_period_rebuild] ([fk_tenant_id] ASC,[gl_year] ASC,	[fk_account_code] ASC)'
EXEC    sp_executesql @sql_code
SET @sql_code = 'CREATE NONCLUSTERED INDEX [ind_department] ON ['+@environment+'].[tfp_accounting_data_period_rebuild] ([fk_tenant_id] ASC,[gl_year] ASC,[department_code] ASC)'
EXEC    sp_executesql @sql_code
SET @sql_code = 'CREATE NONCLUSTERED INDEX [ind_function] ON ['+@environment+'].[tfp_accounting_data_period_rebuild] ([fk_tenant_id] ASC,[gl_year] ASC,[fk_function_code] ASC)'
EXEC    sp_executesql @sql_code
SET @sql_code = 'CREATE NONCLUSTERED INDEX [ind_period] ON ['+@environment+'].[tfp_accounting_data_period_rebuild] ([fk_tenant_id] ASC,[gl_year] ASC,[period] ASC)'
EXEC    sp_executesql @sql_code

SET @sql_code = 'UPDATE STATISTICS ['+@environment+'].[tfp_accounting_data_period_rebuild]'
EXEC    sp_executesql @sql_code

--Swap in the new period table
IF	(
		SELECT COUNT(*)
		FROM INFORMATION_SCHEMA.TABLES
		WHERE TABLE_CATALOG = @table_catalog
		AND TABLE_TYPE = 'BASE TABLE'
		AND TABLE_SCHEMA = @environment
		and table_name = 'tfp_accounting_data_period' 
	)	> 0
BEGIN
SET @sql_code = 'RENAME OBJECT ['+@environment+'].[tfp_accounting_data_period] TO [tfp_accounting_data_period_old]'
EXEC    sp_executesql @sql_code
END;

SET @sql_code = 'RENAME OBJECT ['+@environment+'].[tfp_accounting_data_period_rebuild] TO [tfp_accounting_data_period];'
EXEC    sp_executesql @sql_code

IF	(
		SELECT COUNT(*)
		FROM INFORMATION_SCHEMA.TABLES
		WHERE TABLE_CATALOG = @table_catalog
		AND TABLE_TYPE = 'BASE TABLE'
		AND TABLE_SCHEMA = @environment
		and table_name = 'tfp_accounting_data_period_old' 
	)	> 0
BEGIN
SET @sql_code = 'DROP TABLE ['+@environment+'].[tfp_accounting_data_period_old]'
EXEC    sp_executesql @sql_code
END

PRINT 'New period table swapped and done'  + convert(varchar(400),GETDATE())

/*************** CREATE YEAR TABLE ***************/

PRINT 'Starting calculate and insert to rebuild year table'  + convert(varchar(400),GETDATE())

IF	(
		SELECT COUNT(*)
		FROM INFORMATION_SCHEMA.TABLES
		WHERE TABLE_CATALOG = @table_catalog
		AND TABLE_TYPE = 'BASE TABLE'
		AND TABLE_SCHEMA = @environment
		and table_name = 'tfp_accounting_data_year_rebuild' 
	)	> 0
BEGIN
SET @sql_code = 'DROP TABLE ['+@environment+'].[tfp_accounting_data_year_rebuild]'
EXEC    sp_executesql @sql_code
END


SET @sql_code = '
CREATE TABLE ['+@environment+'].[tfp_accounting_data_year_rebuild]
WITH
(
       DISTRIBUTION = ROUND_ROBIN,
       CLUSTERED COLUMNSTORE INDEX
)
AS
SELECT [db_name], [fk_tenant_id], [fk_account_code], [department_code], [fk_function_code], [fk_project_code], [free_dim_1], [free_dim_2], [free_dim_3], [free_dim_4], [gl_year]
, [amount] = SUM([amount])
FROM #temp_tfp_accounting_data 
GROUP BY [db_name], [fk_tenant_id], [fk_account_code], [department_code], [fk_function_code], [fk_project_code], [free_dim_1], [free_dim_2], [free_dim_3], [free_dim_4], [gl_year]
'
EXEC    sp_executesql @sql_code

PRINT 'Starting to apply index and statistics on rebuild year table'  + convert(varchar(400),GETDATE())

SET @sql_code = 'CREATE NONCLUSTERED INDEX [ind_account] ON ['+@environment+'].[tfp_accounting_data_year_rebuild] ([fk_tenant_id] ASC,[gl_year] ASC,	[fk_account_code] ASC)'
EXEC    sp_executesql @sql_code
SET @sql_code = 'CREATE NONCLUSTERED INDEX [ind_department] ON ['+@environment+'].[tfp_accounting_data_year_rebuild] ([fk_tenant_id] ASC,[gl_year] ASC,[department_code] ASC)'
EXEC    sp_executesql @sql_code
SET @sql_code = 'CREATE NONCLUSTERED INDEX [ind_function] ON ['+@environment+'].[tfp_accounting_data_year_rebuild] ([fk_tenant_id] ASC,[gl_year] ASC,[fk_function_code] ASC)'
EXEC    sp_executesql @sql_code
SET @sql_code = 'CREATE NONCLUSTERED INDEX [ind_year] ON ['+@environment+'].[tfp_accounting_data_year_rebuild] ([fk_tenant_id] ASC,[gl_year] ASC)'
EXEC    sp_executesql @sql_code

SET @sql_code = 'UPDATE STATISTICS ['+@environment+'].[tfp_accounting_data_year_rebuild]'
EXEC    sp_executesql @sql_code


IF	(
		SELECT COUNT(*)
		FROM INFORMATION_SCHEMA.TABLES
		WHERE TABLE_CATALOG = @table_catalog
		AND TABLE_TYPE = 'BASE TABLE'
		AND TABLE_SCHEMA = @environment
		and table_name = 'tfp_accounting_data_year' 
	)	> 0
BEGIN
SET @sql_code = 'RENAME OBJECT ['+@environment+'].[tfp_accounting_data_year] TO [tfp_accounting_data_year_old]'
EXEC    sp_executesql @sql_code
END;

SET @sql_code = 'RENAME OBJECT ['+@environment+'].[tfp_accounting_data_year_rebuild] TO [tfp_accounting_data_year]'
EXEC    sp_executesql @sql_code


IF	(
		SELECT COUNT(*)
		FROM INFORMATION_SCHEMA.TABLES
		WHERE TABLE_CATALOG = @table_catalog
		AND TABLE_TYPE = 'BASE TABLE'
		AND TABLE_SCHEMA = @environment
		and table_name = 'tfp_accounting_data_year_old' 
	)	> 0
BEGIN
SET @sql_code = 'DROP TABLE ['+@environment+'].[tfp_accounting_data_year_old]'
EXEC    sp_executesql @sql_code
END

PRINT 'Year table swapped and done'  + convert(varchar(400),GETDATE())