using EntityFrameworkExtras.EFCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Framsikt.Entities.Core
{
    [StoredProcedure("prcSyncProjWarehouse")]
    public class prcSyncProjWarehouse
    {
        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "tenant_id")]
        public int TenantId { get; set; }

        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "budget_year")]
        public int BudgetYear { get; set; }

        [StoredProcedureParameter(SqlDbType.BigInt, ParameterName = "user_id")]
        public long UserId { get; set; }
    }
}
