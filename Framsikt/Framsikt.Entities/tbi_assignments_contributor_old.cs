
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    

namespace Framsikt.Entities
{
    [Table("tbi_assignments_contributor")]
    public partial class tbi_assignments_contributor_old
    {
        [Key]
        [Column("pk_id")]
        public long pk_id { get; set; }

        [Column("fk_assignment_id")]
        public Guid fk_assignment_id { get; set; }


        [Column("fk_tenant_id")]
        public int fk_tenant_id { get; set; }



        public int contributor_user_id { get; set; }

        public DateTime? updated { get; set; }

        public int? updated_by { get; set; }
    }

}