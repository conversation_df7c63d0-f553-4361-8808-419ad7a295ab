namespace Framsikt.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;

    public partial class tmr_report_status
    {
        [Key]
        public int pk_id { get; set; }

        public int fk_tenant_id { get; set; }

        public int forecast_period { get; set; }

        [Required]
        [StringLength(50)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string process_type { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [Required]
        public int tab_type { get; set; }

        [Required(AllowEmptyStrings = true)]
        [StringLength(50)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string tab_name { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [Required]
        [StringLength(25)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string org_id { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        public int org_level { get; set; }

        [Required]
        [StringLength(25)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string service_id { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        public int service_level { get; set; }

        public int report_status { get; set; }

        public int wf_status { get; set; }

        [Required]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string tag { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        public DateTime updated { get; set; }

        public int updated_by { get; set; }

        public DateTime? deadline_date { get; set; }

        [StringLength(50)]
        public string fk_attribute_id { get; set; } = string.Empty;
    }
}
