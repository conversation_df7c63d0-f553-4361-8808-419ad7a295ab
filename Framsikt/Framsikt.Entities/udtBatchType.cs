using EntityFrameworkExtras.EFCore;

namespace Framsikt.Entities
{
    [UserDefinedTableType("udtBatchType")]
    public class UdtBatchType
    {
        [UserDefinedTableTypeColumn(1)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string BatchType { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
    }
}
