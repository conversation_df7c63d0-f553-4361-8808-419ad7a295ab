namespace Framsikt.Entities
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public partial class gco_tenants
    {
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public gco_tenants()
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        {
            vwUserDetails = new HashSet<vwUserDetail>();
            tko_ind_explanation = new HashSet<tko_ind_explanation>();
            tko_indicator_target = new HashSet<tko_indicator_target>();
            tco_discussions = new HashSet<tco_discussions>();
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int pk_id { get; set; }

        [Required]
        [StringLength(100)]
        public string tenant_name { get; set; }

        public int client_id { get; set; }

        [Required]
        [StringLength(25)]
        public string municipality_id { get; set; }

        [Required]
        [StringLength(50)]
        public string compare_1 { get; set; }

        [Required]
        [StringLength(50)]
        public string compare_2 { get; set; }

        [Required]
        [StringLength(50)]
        public string compare_3 { get; set; }

        [Required]
        [StringLength(50)]
        public string compare_4 { get; set; }

        [Required]
        [StringLength(50)]
        public string forecast_type { get; set; }

        [Required]
        [StringLength(50)]
        public string user_name { get; set; }

        [Required]
        [StringLength(1000)]
        public string connection_string { get; set; }

        public Guid? sp_column_selector { get; set; }

        public Guid? mr_fs_column_selector { get; set; }

        public Guid? mf_ov_column_selector { get; set; }

        public Guid? pm_investment_col_selector { get; set; }

        public Guid? bm_goals_target_col_selector { get; set; }

        [Required]
        public int tenant_type_id { get; set; }

        [StringLength(50)]
        public string? publish_id { get; set; }

        [Required]
        [StringLength(50)]
        public string erp_type { get; set; }

        [StringLength(5000)]
        public string analytics_tracking_code { get; set; }
        [Required]
        [StringLength(10)]
        public string language_preference { get; set; }

        public int master_tenant_id { get; set; }

        public virtual gco_municipalities gco_municipalities { get; set; }

        [NotMapped]
        public virtual ICollection<vwUserDetail> vwUserDetails { get; set; }

        [NotMapped]
        public virtual ICollection<tko_ind_explanation> tko_ind_explanation { get; set; }

        [NotMapped]
        public virtual ICollection<tko_indicator_target> tko_indicator_target { get; set; }

        [NotMapped]
        public virtual ICollection<tco_discussions> tco_discussions { get; set; }

        [Required]
        [StringLength(50)]
        public string fk_region_code_county { get; set; }
    }
}
