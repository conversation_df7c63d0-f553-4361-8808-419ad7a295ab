using EntityFrameworkExtras.EFCore;
using System.Collections.Generic;
using System.Data;

namespace Framsikt.Entities
{
    [StoredProcedure("prcInsertIntoTcoInvestmentDetails")]
    public class prcInsertIntoTcoInvestmentDetails
    {
        [StoredProcedureParameter(SqlDbType.Udt, ParameterName = "udtTcoInvestmentDetail")]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public List<udtTcoInvestmentDetail> udtTcoInvestmentDetail { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
    }
}
