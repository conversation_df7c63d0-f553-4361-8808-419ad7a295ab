namespace Framsikt.Entities
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    

    public partial class tbu_stage_org_structure_import
    {
        [Key]
        public long pk_id { get; set; }

        public int fk_tenant_id { get; set; }

        public int user_id { get; set; }

        [Required]
        [StringLength(100)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string fk_org_version { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [StringLength(100)]
        public string? org_id_1 { get; set; }

        [StringLength(500)]
        public string? org_name_1 { get; set; }

        [StringLength(100)]
        public string? org_id_2 { get; set; }

        [StringLength(500)]
        public string? org_name_2 { get; set; }

        [StringLength(100)]
        public string? org_id_3 { get; set; }

        [StringLength(500)]
        public string? org_name_3 { get; set; }

        [StringLength(100)]
        public string? org_id_4 { get; set; }

        [StringLength(500)]
        public string? org_name_4 { get; set; }

        [StringLength(100)]
        public string? org_id_5 { get; set; }

        [StringLength(500)]
        public string? org_name_5 { get; set; }

        [StringLength(100)]
        public string? org_id_6 { get; set; }

        [StringLength(200)]
        public string? org_name_6 { get; set; }

        [StringLength(100)]
        public string? org_id_7 { get; set; }

        [StringLength(200)]
        public string? org_name_7 { get; set; }

        [StringLength(100)]
        public string? org_id_8 { get; set; }

        [StringLength(500)]
        public string? org_name_8 { get; set; }

        [StringLength(200)]
        public string? fk_department_code { get; set; }

        public bool fk_org_version_error { get; set; }

        public bool fk_department_code_error { get; set; }

        public int error_count { get; set; }

        public DateTime updated { get; set; }

        public int updated_by { get; set; }

        public long? job_Id { get; set; }

        public bool org_id_1_error { get; set; }

        public bool org_name_1_error { get; set; }

        public bool org_id_2_error { get; set; }

        public bool org_name_2_error { get; set; }

        public bool org_id_3_error { get; set; }

        public bool org_name_3_error { get; set; }

        public bool org_id_4_error { get; set; }

        public bool org_name_4_error { get; set; }

        public bool org_id_5_error { get; set; }

        public bool org_name_5_error { get; set; }

        public bool org_id_6_error { get; set; }

        public bool org_name_6_error { get; set; }

        public bool org_id_7_error { get; set; }

        public bool org_name_7_error { get; set; }

        public bool org_id_8_error { get; set; }

        public bool org_name_8_error { get; set; }
    }
}
