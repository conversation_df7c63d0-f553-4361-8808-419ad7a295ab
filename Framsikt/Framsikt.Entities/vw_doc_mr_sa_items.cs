namespace Framsikt.Entities
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public partial class vw_doc_mr_sa_items
    {
        
        [Column(Order = 0)]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int fk_tenant_id { get; set; }

        
        [Column(Order = 1)]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int forecast_period { get; set; }

        [StringLength(25)]
        public string? aggregate_id { get; set; }

        [StringLength(100)]
        public string? aggregate_name { get; set; }

        [StringLength(25)]
        public string? sub_level_id { get; set; }

        [StringLength(100)]
        public string? sub_level_name { get; set; }

        public decimal? forecast_amount { get; set; }

        public decimal? original_budget { get; set; }

        public decimal? revised_budget { get; set; }

        public decimal? bud_amt_ytd { get; set; }

        public decimal? bud_amt_period { get; set; }

        public decimal? actual_amt_year { get; set; }

        public decimal? actual_amt_ytd { get; set; }

        public decimal? actual_amt_period { get; set; }

        public decimal? org_bud_amt_last_year { get; set; }

        public decimal? actual_amt_last_ytd { get; set; }

        public decimal? budget_changes { get; set; }

        public decimal? revised_budget_income { get; set; }

        public decimal? revised_budget_expence { get; set; }

        public decimal? actual_amt_ytd_income { get; set; }

        public decimal? actual_amt_ytd_expence { get; set; }

        public decimal? forecast_expence { get; set; }

        
        [Column(Order = 2)]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int show_flag { get; set; }

        public decimal? deviation_amount { get; set; }

        public decimal? forecast_incl_deviation { get; set; }
    }
}
