using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Framsikt.Entities
{
    [Table("tpd_plandiscs_embeddedviews")]
    public partial class TpdPlandiscEmbeddedViews
    {
        [Key]
        [Column("pk_id")]
        public int Id { get; set; }

        [Column("fk_plandisc_id")]
        public Guid PlandiscId { get; set; }

        [Required(AllowEmptyStrings = true)]
        [StringLength(100)]
        [Column("pk_embeddedview_id")]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string EmbeddedViewId { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [Required(AllowEmptyStrings = true)]
        [StringLength(200)]
        [Column("title")]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string Title { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [Required(AllowEmptyStrings = true)]
        [StringLength(500)]
        [Column("url")]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string Url { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [Column("sort_order")]
        public int SortOrder { get; set; }

        [Column("fk_tenant_id")]
        public int TenantId { get; set; }

        [Column("updated_by")]
        public int UpdatedBy { get; set; }

        [Column("updated")]
        public DateTime UpdatedAt { get; set; }
    }
}