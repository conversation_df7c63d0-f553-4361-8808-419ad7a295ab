using EntityFrameworkExtras.EFCore;
using System.Collections.Generic;
using System.Data;

namespace Framsikt.Entities
{
    [StoredProcedure("prcInsertIntoTbuTransDetail")]
    public class prcInsertIntoTbuTransDetail
    {
        [StoredProcedureParameter(SqlDbType.Udt, ParameterName = "udtTransDetail")]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public List<udtTbuTransDetailType> udtTransDetail { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
    }
}
