namespace Framsikt.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;

    public partial class tps_operations_propopsal
    {
        [Key]
        public int pk_id { get; set; }

        public int fk_tenant_id { get; set; }

        public Guid fk_proposal_id { get; set; }

        public int budget_year { get; set; }

        public int line_group_id { get; set; }

        [Required]
        [StringLength(200)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string line_group { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        public int line_item_id { get; set; }

        [Required]
        [StringLength(200)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string line_item { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        public int fk_action_id { get; set; }


        [StringLength(255)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string action_name { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [Required]
        [StringLength(25)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string org_id { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        
        [StringLength(255)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string org_name { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        public decimal gl_amount { get; set; }

        public decimal org_budget_amount { get; set; }

        public decimal revised_budget_amount { get; set; }

        public decimal year_1_amount { get; set; }

        public decimal year_2_amount { get; set; }

        public decimal year_3_amount { get; set; }

        public decimal year_4_amount { get; set; }

        public decimal year_5_amount { get; set; }

        public decimal year_6_amount { get; set; }

        public decimal year_7_amount { get; set; }

        public decimal year_8_amount { get; set; }

        public decimal year_9_amount { get; set; }

        public decimal year_10_amount { get; set; }

        public decimal change_1_amount { get; set; }

        public decimal change_2_amount { get; set; }

        public decimal change_3_amount { get; set; }

        public decimal change_4_amount { get; set; }

        public decimal change_5_amount { get; set; }

        public decimal change_6_amount { get; set; }

        public decimal change_7_amount { get; set; }

        public decimal change_8_amount { get; set; }

        public decimal change_9_amount { get; set; }

        public decimal change_10_amount { get; set; }

        public string? comment { get; set; }

        public string? description { get; set; }

        public bool? status { get; set; }

        public DateTime updated { get; set; }

        public int updated_by { get; set; }

        [StringLength(20)]
        public string? altercode { get; set; }

        public bool? show_flag { get; set; }
        public bool isblistaction { get; set; }

        public string? account_code { get; set; }
        public string? department_code { get; set; }
        public string? function_code { get; set; }
        public int? income_flag { get; set; }

        [StringLength(25)]
        public string? fk_main_project_code { get; set; }

        public int? action_type { get; set; }
    }
}
