using EntityFrameworkExtras.EFCore;
using System;
using System.Collections.Generic;
using System.Data;

namespace Framsikt.Entities
{
    [StoredProcedure("procYBInvestmentLockOrginalBudget")]
    public class procYBInvestmentLockOrginalBudget
    {
        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "tenant_id")]
        public int tenant_id { get; set; }

        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "budget_year")]
        public int budget_year { get; set; }

        [StoredProcedureParameter(SqlDbType.VarChar, ParameterName = "adjusmntCode")]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string adjusmntCode { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [StoredProcedureParameter(SqlDbType.DateTime, ParameterName = "updated")]
        public DateTime updated { get; set; }

        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "updatedBy")]
        public int updatedBy { get; set; }

        [StoredProcedureParameter(SqlDbType.Udt, ParameterName = "udtDepartmentCode")]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public List<udtDepartmentCodes> departmentCodes { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
    }
}
