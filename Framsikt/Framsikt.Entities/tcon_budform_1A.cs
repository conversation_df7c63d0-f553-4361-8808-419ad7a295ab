namespace Framsikt.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public partial class tcon_budform_1A
    {
        [Key]
        public int pk_id { get; set; }

        public int fk_tenant_id { get; set; }

        public int budget_year { get; set; }

        public int consol_level { get; set; }

        [Column("1A_line")]
        public int C1A_line { get; set; }

        public int line_group_id { get; set; }

        [Required]
        [StringLength(200)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string line_group { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        public int line_item_id { get; set; }

        [Required]
        [StringLength(150)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string line_item { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [Required]
        [StringLength(25)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string fk_kostra_account_code { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [Required]
        [StringLength(25)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string fk_kostra_function_code { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        public decimal actual_amt_last_year { get; set; }

        public decimal actual_amt_year { get; set; }

        public decimal org_bud_amt_year { get; set; }

        public decimal revised_bud_amt_year { get; set; }

        public DateTime updated { get; set; }

        public int updated_by { get; set; }
    }
}
