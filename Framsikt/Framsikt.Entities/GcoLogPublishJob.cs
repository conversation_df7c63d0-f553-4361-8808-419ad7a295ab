using System.ComponentModel.DataAnnotations.Schema;

namespace Framsikt.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;

    [Table("gco_log_publish_job")]
    public class GcoLogPublishJob
    {
        [Required]
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("Id")]
        public long Id { get; set; }

        [Required]
        [Column("job_id")]
        public Guid JobId { get; set; }

        [Required]
        [Column("time_stamp")]
        public DateTime TimeStamp { get; set; }

        [Required]
        [StringLength(100)]
        [Column("user_name")]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string UserName { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [Required]
        [Column("tenant_id")]
        public int TenantId { get; set; }

        [Required]
        [StringLength(2000)]
        [Column("job_json")]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string JobJson { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [StringLength(3000)]
        [Column("message")]
        public string? Message { get; set; }

        [StringLength(50)]
        [Column("job_type")]
        public string? JobType { get; set; }

        [Required]
        [StringLength(100)]
        [Column("log_source_name")]
        public string? LogSourceName { get; set; }
    }
}

