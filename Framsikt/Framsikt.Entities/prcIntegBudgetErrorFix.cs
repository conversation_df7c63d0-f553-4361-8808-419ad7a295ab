using EntityFrameworkExtras.EFCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Framsikt.Entities
{
    [StoredProcedure("proc_bud_original_control_updates")]
    public class prcIntegBudgetErrorFix
    {
        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "tenant_id")]
        public int tenant_id { get; set; }

        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "budget_year")]
        public int budget_year { get; set; }

        [StoredProcedureParameter(SqlDbType.NVarChar, ParameterName = "batch_id")]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string batch_id { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
    }
}
