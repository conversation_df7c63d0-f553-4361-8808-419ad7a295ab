namespace Framsikt.Entities
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public partial class tpl_investments
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public tpl_investments()
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        {
            tpl_plan_investments = new HashSet<tpl_plan_investments>();
            tpl_investments_detail = new HashSet<tpl_investments_detail>();
            tpl_investments_goal = new HashSet<tpl_investments_goal>();
            tpl_investments_target = new HashSet<tpl_investments_target>();
            tpl_investments_strategy = new HashSet<tpl_investments_strategy>();
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Guid pk_plan_investment_id { get; set; }

        public Guid fk_masterplan_id { get; set; }

        [Required]
        [StringLength(1000)]
        public string name { get; set; }

        public int fk_prog_code { get; set; }

        [StringLength(300)]
        public string tags { get; set; }

        public int start_year { get; set; }

        public int end_year { get; set; }

        public string description { get; set; }

        public Guid description_historyId { get; set; }

        public int fk_tenant_id { get; set; }

        public int updated_by { get; set; }

        public DateTime updated { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<tpl_plan_investments> tpl_plan_investments { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<tpl_investments_detail> tpl_investments_detail { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<tpl_investments_goal> tpl_investments_goal { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<tpl_investments_target> tpl_investments_target { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<tpl_investments_strategy> tpl_investments_strategy { get; set; }

    }
}