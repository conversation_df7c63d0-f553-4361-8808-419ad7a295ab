using EntityFrameworkExtras.EFCore;
using System.Data;

namespace Framsikt.Entities
{
    [StoredProcedure("prcUpdateActionTypeForActionImport")]
    public class PrcUpdateActionTypeForActionImport
    {
        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "tenant_id")]
        public int TenantId { get; set; }

        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "user_id")]
        public int UserId { get; set; }

        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "budget_year")]
        public int BudgetYear { get; set; }

        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "change_id")]
        public int ChangeId { get; set; }

        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "action_type")]
        public int ActionType { get; set; }
    }
}
