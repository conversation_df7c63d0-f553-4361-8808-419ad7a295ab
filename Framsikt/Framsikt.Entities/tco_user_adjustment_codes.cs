namespace Framsikt.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;
    using System.Text.Json.Serialization;
   

    public partial class tco_user_adjustment_codes
    {
        [Key]
        [JsonIgnore]
        public int Id { get; set; }

        public int fk_tenant_id { get; set; }

#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string pk_adj_code { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [Required(AllowEmptyStrings = true)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string description { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        public Guid description_id { get; set; }

        public bool status { get; set; }
        public bool is_hidden { get; set; }
        public bool is_periodization { get; set; }

        public int fk_user_id { get; set; }

        public DateTime updated { get; set; }

#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string org_level_value { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        public int budget_year { get; set; }

        public int org_level { get; set; }

        public bool is_original_flag { get; set; }

        public string? prefix_adjCode { get; set; }

        public bool inv_adj_code { get; set; }

        public string? fk_main_project_code { get; set; }

        public int? fk_change_id { get; set; }

        public bool include_in_calculation { get; set; }

        public int forecast_period { get; set; }

        public bool salary_budget_flag { get; set; }

        [Required]
        [StringLength(100)]
        public string case_ref { get; set; } = string.Empty;

        public DateTime decision_date { get; set; } = new DateTime(1990, 01, 01);

        [Required]
        [StringLength(500)]
        public string long_description { get; set; } = string.Empty;

        [Required]
        [StringLength(8)]
        public string fk_bud_auth_code { get; set; } = string.Empty;

        [Required]
        [StringLength(25)]
        public string fk_attribute_id { get; set; } = string.Empty;
    }
}