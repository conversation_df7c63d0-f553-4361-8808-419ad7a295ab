using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Framsikt.Entities
{
    public partial class tco_dashboards_distribution
    {
        [Key]
        [Column(Order = 0)]
        public int ID { get; set; }

        public int fk_dashboard_id { get; set; }

        [StringLength(20)]
        public string? org_id_created { get; set; }

        public int? org_level_created { get; set; }

        [StringLength(20)]
        public string? org_id { get; set; }

        public int? org_level { get; set; }

        [StringLength(20)]
        public string? service_id { get; set; }

        public int fk_tenant_id { get; set; }
    }
}
