namespace Framsikt.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;

    public partial class tpl_investments_goal
    {
        [Key]
        public int pk_id { get; set; }

        public int fk_tenant_id { get; set; }

        public Guid fk_plan_investment_id { get; set; }

        public Guid fk_plan_goal_id { get; set; }

        public DateTime updated { get; set; }

        public int updated_by { get; set; }

#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public virtual tpl_investments tpl_investments { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
    }
}