namespace Framsikt.Entities
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    

    public partial class vw_mr_absence_employees_details_data_warehouse
    {
        
        [Column(Order = 0)]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int fk_tenant_id { get; set; }

        
        [Column(Order = 1)]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int forecast_period { get; set; }

        
        [Column(Order = 2)]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int budget_year { get; set; }

        
        [Column(Order = 3)]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int employee_id { get; set; }

        [StringLength(50)]
        public string? ida { get; set; }

        [StringLength(50)]
        public string? absence_code { get; set; }

        [StringLength(50)]
        public string? absence_code_name { get; set; }

        [Column(Order = 4)]
        public DateTime date_from { get; set; }

        
        [Column(Order = 5)]
        public DateTime date_to { get; set; }

        public int? days_count { get; set; }

        public decimal? absence_percentage { get; set; }

        public bool? work_related { get; set; }

        public bool? pregnancy_related { get; set; }
    }
}
