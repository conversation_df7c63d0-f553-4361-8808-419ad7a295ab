namespace Framsikt.Entities
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    

    public partial class tco_fee_mapping
    {
        [Key]
        public int pk_id { get; set; }

        public int fee_id_from { get; set; }

        public int fee_id_to { get; set; }

        public int budget_year { get; set; }

        public int fk_tenant_id { get; set; }

        public int updated_by { get; set; }

        public DateTime updated { get; set; }
    }
}
