using EntityFrameworkExtras.EFCore;
using System.Data;

namespace Framsikt.Entities
{
    [StoredProcedure("prcValidateImportedObjectValues")]
    public class prcValidateImportedObjectValues
    {
        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "tenant_id")]
        public int TenantId { get; set; }

        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "user_id")]
        public int UserId { get; set; }

        [StoredProcedureParameter(SqlDbType.BigInt, ParameterName = "jobId")]
        public long JobId { get; set; }
    }
}
