using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Framsikt.Entities
{
    [Table("tbi_checklist_type")]
    public partial class TbiChecklistType
    {
        [Key]
        [Column("pk_checklist_type_id")]
        public Guid ChecklistTypeId { get; set; }

        [Required]
        [StringLength(5000)]
        [Column("checklisttype_name")]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string ChecklistTypeName { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [Column("fk_tenant_id")]
        public int TenantId { get; set; }

        [Column("created_by")]
        public int CreatedBy { get; set; }

        [Column("created_date")]
        public DateTime CreatedDate { get; set; }
    }
}
