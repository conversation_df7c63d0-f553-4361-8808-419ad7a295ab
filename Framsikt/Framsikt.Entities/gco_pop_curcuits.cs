namespace Framsikt.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;

    public partial class gco_pop_curcuits
    {
        [Key]
        [StringLength(25)]
        public string pk_curcuit_id { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        public string curcuit_name { get; set; } = string.Empty;

        public DateTime updated { get; set; }

        public int updated_by { get; set; }
    }
}