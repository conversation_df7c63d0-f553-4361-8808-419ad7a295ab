namespace Framsikt.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;

    public partial class tco_salary_acc_category
    {
        [Key]
        public int pk_id { get; set; }

        public int fk_tenant_id { get; set; }

        [Required]
        [StringLength(50)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string pk_salary_acc_category { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [Required]
        [StringLength(50)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string category_name { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [Required]
        [StringLength(50)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string fk_account_code { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        public int handling_id { get; set; }

        public bool aga_flag { get; set; }

        public bool vacation_flag { get; set; }

        public bool pension_flag { get; set; }

        public DateTime updated { get; set; }

        public int updatedby { get; set; }

        public int year_from { get; set; }
        public int year_to { get; set; }
    }
}
