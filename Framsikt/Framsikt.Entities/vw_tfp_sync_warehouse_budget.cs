namespace Framsikt.Entities
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public partial class vw_tfp_sync_warehouse_budget
    {
        
        [Column(Order = 0)]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int fk_tenant_id { get; set; }

        
        [Column(Order = 1)]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int budget_year { get; set; }

        
        [Column(Order = 2)]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int sub_tenant_id { get; set; }

        [DefaultValue(0.0)]
        public decimal? org_main { get; set; }

        [DefaultValue(0.0)]
        public decimal? revised_main { get; set; }

        [DefaultValue(0.0)]
        public decimal? revised_sub { get; set; }

        [DefaultValue(0.0)]
        public decimal? deviation { get; set; }

        [DefaultValue(0.0)]
        public decimal? budget_main { get; set; }

        [DefaultValue(0.0)]
        public decimal? budget_sub { get; set; }

        [DefaultValue(0.0)]
        public decimal? deviation_budget { get; set; }
    }
}
