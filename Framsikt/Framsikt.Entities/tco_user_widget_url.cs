

namespace Framsikt.Entities
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;
    using System.ComponentModel.DataAnnotations;
    public partial class tco_user_widget_url
    {
        [Key]
        public int id { get; set; }

        public int fk_tenant_id { get; set; }

        public Guid instance_id { get; set; }

        [Required]
        public string link_configs { get; set; } = string.Empty;

        public DateTime updated { get; set; }

        public int updated_by { get; set; }
        public string title { get; set; } = string.Empty ;
    }
}
