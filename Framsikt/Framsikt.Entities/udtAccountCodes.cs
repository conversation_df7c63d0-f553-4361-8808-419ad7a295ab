using EntityFrameworkExtras.EFCore;

namespace Framsikt.Entities
{
    [UserDefinedTableType("udtAccountCodes")]
    public class udtAccountCodes
    {
        [UserDefinedTableTypeColumn(1)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string accountCode { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
    }
}
