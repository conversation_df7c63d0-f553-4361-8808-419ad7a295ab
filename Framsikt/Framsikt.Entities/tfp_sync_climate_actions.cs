using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Framsikt.Entities.Core
{
    public class tfp_sync_climate_actions
    {
        [Key]
        public int pk_id { get; set; }

        public int main_fk_tenant_id { get; set; }

        public int main_fk_climate_id { get; set; }

        public Guid main_fk_climate_dist_id { get; set; }

        public int sub_fk_tenant_id { get; set; }

        public int sub_fk_climate_id { get; set; }

        [Required]
        [StringLength(25)]
        public string process_stage { get; set; } = string.Empty;

        public int updated_by { get; set; }

        [Column(TypeName = "date")]
        public DateTime updated { get; set; }
    }
}
