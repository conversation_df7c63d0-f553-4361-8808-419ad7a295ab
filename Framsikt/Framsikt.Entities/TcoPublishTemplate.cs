using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Framsikt.Entities
{
    [Table("tco_publish_template")]
    public partial class TcoPublishTemplate
    {
        [Key]
        [Column("pk_id")]
        public int PkId { get; set; }

        [Column("fk_tenant_id")]
        public int FkTenantId { get; set; }

        [Required]
        [StringLength(500)]
        [Column("name")]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string Name { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [Required]
        [StringLength(20)]
        [Column("tree_type")]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string TreeType { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [Required]
        [StringLength(500)]
        [Column("template_url")]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string TemplateUrl { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [Column("is_global")]
        public bool IsGlobal { get; set; }

        [Column("is_default")]
        public bool IsDefault { get; set; }

        [Column("param1")]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string Param1 { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [Column("updated")]
        public DateTime Updated { get; set; }

        [Column("updated_by")]
        public int UpdateBy { get; set; }


        [Column("budget_year")]
        public int BudgetYear { get; set; }

        [Column("forecast_period")]
        public int ForecastPeriod { get; set; }

        [StringLength(15)]
        [Column("org_Id")]
        public string? OrgId { get; set; }

        [StringLength(300)]
        [Column("org_name")]
        public string? OrgName { get; set; }

        [Column("org_level")]
        public int OrgLevel { get; set; }

        [Column("is_editable")]
        public bool IsEditable { get; set; }
        [Column("fk_budget_phase_id")]
        public Guid budgetPhaseId { get; set; }

        [Column("is_data_migrated")]
        public bool isDataMigrated { get; set; }

        [Column("display_only_tables")] 
        public bool display_only_tables { get; set; }


        [Column("include_internal_desc")] 
        public bool include_internal_desc { get; set; }


        [Column("show_only_modified")] 
        public bool show_only_modified { get; set; }


        [Column("include_blist_internal_desc")] 
        public bool include_blist_internal_desc { get; set; }
    }


}
