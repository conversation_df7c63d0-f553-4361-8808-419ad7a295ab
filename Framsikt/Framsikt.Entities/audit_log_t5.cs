namespace Framsikt.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public partial class audit_log_t5
    {
        [Key]
        public long pk_id { get; set; }

        [Column(TypeName = "datetime2")]
        public DateTime event_time { get; set; }

        [Required]
        [StringLength(8)]
        public string action_id { get; set; } = string.Empty;

        [Required]
        [StringLength(64)]
        public string session_server_principal_name { get; set; } = string.Empty;

        [Required]
        [StringLength(32)]
        public string database_name { get; set; } = string.Empty;

        [Required]
        [StringLength(4000)]
        public string statement { get; set; } = string.Empty;

        [Required]
        [StringLength(15)]
        public string client_ip { get; set; } = string.Empty;

        [StringLength(255)]
        public string? audit_log_info { get; set; }

        [StringLength(32)]
        public string? host_name { get; set; }

        public int? tenant_id { get; set; }
    }
}