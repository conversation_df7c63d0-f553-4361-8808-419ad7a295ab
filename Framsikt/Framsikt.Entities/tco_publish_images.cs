namespace Framsikt.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public partial class tco_publish_images
    {
        [Key]
        [Column(Order = 1)]
        public Guid fk_banner_image_id { get; set; }

        [Key]
        [Column(Order = 2)]
        public Guid fk_tile_image_id { get; set; }

        [Key]
        [Column(Order = 3)]
        public Guid fk_original_image_id { get; set; }

        [Required]
        public int fk_tenant_id { get; set; }

        [Required(AllowEmptyStrings = true)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string banner_bounds { get; set; }

#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [Required(AllowEmptyStrings = true)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string tile_bounds { get; set; }

#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [Required]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string image_type { get; set; }

#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [Required]
        public DateTime updated { get; set; }

        [Required]
        public int updated_by { get; set; }

        public bool is_original_coordinates { get; set; }
    }
}