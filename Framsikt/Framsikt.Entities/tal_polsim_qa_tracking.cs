namespace Framsikt.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;

    public partial class tal_polsim_qa_tracking
    {
        [Key]
        public int pk_id { get; set; }

        public Guid? fk_question_id { get; set; }

        public DateTime? date { get; set; }

        public int fk_tenant_id { get; set; }

        public int budget_year { get; set; }

        [Required]
        [StringLength(50)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string alert_type { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        public int pk_user_id { get; set; }

        public bool is_mail_alert { get; set; }

        public bool is_sms_alert { get; set; }

        public int updated_by { get; set; }

        public DateTime updated { get; set; }

        public string? rejection_comments { get; set; }
    }
}