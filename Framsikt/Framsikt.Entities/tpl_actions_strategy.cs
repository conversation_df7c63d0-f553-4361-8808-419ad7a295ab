using System;
using System.ComponentModel.DataAnnotations;

namespace Framsikt.Entities
{
    public partial class tpl_actions_strategy
    {
        [Key]
        public int pk_id { get; set; }

        public int fk_plan_action_id { get; set; }

        public int fk_plan_strategy_id { get; set; }

        public int fk_tenant_id { get; set; }

        public DateTime updated { get; set; }

        public int updated_by { get; set; }

#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public virtual tpl_actions tpl_actions { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
    }
}