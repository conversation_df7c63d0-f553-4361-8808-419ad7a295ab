using EntityFrameworkExtras.EFCore;
using System.Data;

namespace Framsikt.Entities
{
    [StoredProcedure("procMoveBudgetChanges")]
    public class ProcMoveBudgetChanges
    {
        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "tenant_id")]
        public int TenantId { get; set; }

        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "budget_year")]
        public int BudgetYear { get; set; }

        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "jodId")]
        public int JodId { get; set; }
        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "userId")]
        public int UserId { get; set; }
        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "changeId")]
        public int ChangeId { get; set; }
        [StoredProcedureParameter(SqlDbType.VarChar, ParameterName = "batchType")]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string BatchType { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [StoredProcedureParameter(SqlDbType.VarChar, ParameterName = "alterCode")]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string AlterCode { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
    }
}
