namespace Framsikt.Entities
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    

    public partial class integ_agrpostback_batches
    {
        [Key]
        public int pk_id { get; set; }

        [Required]
        [StringLength(14)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string Batchid { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        public int fk_tenant_id { get; set; }

        public bool IsOrig { get; set; }

        public bool transferred { get; set; }

        public DateTime updated { get; set; }

        public int budget_year { get; set; }

        public bool error_fixed { get; set; }

        public int? export_type { get; set; }

        public string? response_message { get; set; }

        public bool control_batch { get; set; }
    }
}
