using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Framsikt.Entities.Core
{
    public partial class tco_api_auth_activities
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int pk_id { get; set; }

        [Required]
        //[StringLength(100)]
        public string method_name { get; set; } = string.Empty;

        //[StringLength(100)]
        public string controller_name { get; set; } = string.Empty ;
    }
}
