namespace Framsikt.Entities
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public partial class gco_ducky_details
    {
        [Key]
        public int pk_id { get; set; }

        [Required]
        [StringLength(25)]
        public string fk_municipality_id { get; set; } = string.Empty;

        [Required]
        [StringLength(25)]
        public string fk_curcuit_id { get; set; } = string.Empty;

        public int budget_year { get; set; }

        [Required]
        [StringLength(25)]
        public string batch { get; set; } = string.Empty;

        [Required]
        [StringLength(25)]
        public string fk_ducky_id { get; set; } = string.Empty;

        public decimal value { get; set; }

        public DateTime updated { get; set; }

        public int updated_by { get; set; }

        [Required]
        [StringLength(25)]
        public string county_id { get; set; } = string.Empty;
    }
}
