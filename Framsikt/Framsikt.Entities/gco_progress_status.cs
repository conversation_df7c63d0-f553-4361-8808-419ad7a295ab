namespace Framsikt.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;

    public partial class gco_progress_status
    {
        [Key]
        public int pk_id { get; set; }

        public int status_id { get; set; }

        [Required]
        [StringLength(100)]
        public string status_description { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string lang_ID { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string type { get; set; } = string.Empty;

        public int active { get; set; }

        public DateTime updated { get; set; }

        public int updated_by { get; set; }
    }
}