namespace Framsikt.Entities
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public partial class vw_doc_function_report
    {
        
        [Column(Order = 0)]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int fk_tenant_id { get; set; }

        
        [Column(Order = 1)]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int budget_year { get; set; }

        
        [Column(Order = 2)]
        [StringLength(25)]
        public string? fp_level_1_value { get; set; }

        
        [Column(Order = 3)]
        [StringLength(25)]
        public string? fp_level_2_value { get; set; }

        
        [Column(Order = 4)]
        [StringLength(25)]
        public string? fk_function_code { get; set; }

        
        [Column(Order = 5)]
        [StringLength(100)]
        public string? display_name { get; set; }

        
        [Column(Order = 6)]
        [StringLength(25)]
        public string? org_id { get; set; }

        
        [Column(Order = 7)]
        [StringLength(100)]
        public string? org_name { get; set; }

        
        [Column(Order = 8)]
        public int fk_change_id { get; set; }
        public decimal? gl_amount { get; set; }

        public decimal? budget_amount { get; set; }

        public decimal? revised_budget_amount { get; set; }

        public decimal? year_1_amount { get; set; }

        public decimal? year_2_amount { get; set; }

        public decimal? year_3_amount { get; set; }

        public decimal? year_4_amount { get; set; }
        public string attribute_id { get; set; } = string.Empty;
        public string attribute_name { get; set; } = string.Empty;

    }
}
