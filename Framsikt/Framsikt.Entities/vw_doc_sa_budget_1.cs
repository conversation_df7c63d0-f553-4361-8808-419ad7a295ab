namespace Framsikt.Entities
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public partial class vw_doc_sa_budget_1
    {
        
        [Column(Order = 0)]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int fk_tenant_id { get; set; }

        
        [Column(Order = 1)]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int budget_year { get; set; }

        [StringLength(25)]
        public string? service_id { get; set; }

        [StringLength(100)]
        public string? service_name { get; set; }

        
        [Column(Order = 2)]
        [StringLength(25)]
        public string? level_2_id { get; set; }

        
        [Column(Order = 3)]
        [StringLength(125)]
        public string? level_2_name { get; set; }

        public decimal? org_budget { get; set; }

        public decimal? price_adj { get; set; }

        public decimal? new_adj { get; set; }

        public decimal? cost_red { get; set; }

        public decimal? new_budget { get; set; }

        public decimal? change_pct { get; set; }
        public int fk_change_id { get; set; }
    }
}
