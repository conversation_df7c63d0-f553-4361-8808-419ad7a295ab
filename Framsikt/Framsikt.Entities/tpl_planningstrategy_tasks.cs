    namespace Framsikt.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;

    public partial class tpl_planningstrategy_tasks
    {
        [Key]
        public Guid pk_strategy_task_id { get; set; }

        public Guid fk_planning_strategy_id { get; set; }

        [Required]
        [StringLength(200)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string strategy_task_name { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        public Guid fk_plan_type_id { get; set; }

        public Guid version_type { get; set; }

        public int start_year { get; set; }

        public int end_year { get; set; }

        public int fk_tenant_id { get; set; }

        public int updated_by { get; set; }

        public DateTime updated { get; set; }

        public Guid? fk_plan_category_id { get; set; }

        [StringLength(250)]
        public string? short_description { get; set; }

#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string long_description { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public bool is_included_in_fp_doc { get; set; } = true;
    }
}