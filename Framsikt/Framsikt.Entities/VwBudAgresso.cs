namespace Framsikt.Entities
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("vw_bud_agresso")]
    public partial class VwBudAgresso
    {
       
        [Column("fk_tenant_id", Order = 0)]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int FkTenantId { get; set; }

       
        [Column("budget_year", Order = 1)]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int BudgetYear { get; set; }

       
        [Column("fk_account_code", Order = 2)]
        [StringLength(25)]
        public string? FkAccountCode { get; set; }

       
        [Column("department_code", Order = 3)]
        [StringLength(25)]
        public string? DepartmentCode { get; set; }

       
        [Column("fk_function_code", Order = 4)]
        [StringLength(25)]
        public string? FkFunctionCode { get; set; }

       
        [Column("fk_project_code", Order = 5)]
        [StringLength(25)]
        public string? FkProjectCode { get; set; }

       
        [Column("free_dim_1", Order = 6)]
        [StringLength(25)]
        public string? FreeDim1 { get; set; }

       
        [Column("free_dim_2", Order = 7)]
        [StringLength(25)]
        public string? FreeDim2 { get; set; }

       
        [Column("free_dim_3", Order = 8)]
        [StringLength(25)]
        public string? FreeDim3 { get; set; }

       
        [Column("free_dim_4", Order = 9)]
        [StringLength(25)]
        public string? FreeDim4 { get; set; }

        [Column("jan")]
        public decimal? Jan { get; set; }

        [Column("feb")]
        public decimal? Feb { get; set; }

        [Column("mar")]
        public decimal? Mar { get; set; }

        [Column("apr")]
        public decimal? Apr { get; set; }

        [Column("may")]
        public decimal? May { get; set; }

        [Column("jun")]
        public decimal? Jun { get; set; }

        [Column("jul")]
        public decimal? Jul { get; set; }

        [Column("aug")]
        public decimal? Aug { get; set; }

        [Column("sep")]
        public decimal? Sep { get; set; }

        [Column("oct")]
        public decimal? Oct { get; set; }

        [Column("nov")]
        public decimal? Nov { get; set; }

        [Column("dec")]
        public decimal? Dec { get; set; }
    }
}
