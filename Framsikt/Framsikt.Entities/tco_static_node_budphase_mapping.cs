namespace Framsikt.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;

    public partial class tco_static_node_budphase_mapping
    {
        [Key]
        public Guid pk_mapping_id { get; set; }

        public Guid fk_budget_phase_id { get; set; }

        [Required]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string fk_node_id { get; set; }
        public string node_type { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        public int level { get; set; }

        public bool is_connected { get; set; }

        public int fk_tenant_id { get; set; }

        public int budget_year { get; set; }

        public DateTime updated { get; set; }

        public int updated_by { get; set; }
    }
}