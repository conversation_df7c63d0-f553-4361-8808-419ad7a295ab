namespace Framsikt.Entities
{
    using System.ComponentModel.DataAnnotations;

    public partial class tco_color_palettes
    {
        [Key]
        public int pk_palette_id { get; set; }

        public int fk_tenant_id { get; set; }

        [Required]
        [StringLength(100)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string palette_name { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [Required]
        [StringLength(100)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string publish_type { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        public int budget_year_valid_from { get; set; }

        public int budget_year_valid_upto { get; set; }

        public int period_valid_from { get; set; }

        public int period_valid_upto { get; set; }
    }
}