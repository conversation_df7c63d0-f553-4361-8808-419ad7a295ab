using EntityFrameworkExtras.EFCore;
using System;
using System.Data;

namespace Framsikt.Entities
{
    [StoredProcedure("prcStaffplanningInsertImportIdAndUpdateColumns")]
    public class prcStaffplanningInsertImportIdAndUpdateColumns
    {
        [StoredProcedureParameter(SqlDbType.UniqueIdentifier, ParameterName = "importId")]
        public Guid importId { get; set; }

        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "tenantId")]
        public int tenantId { get; set; }

        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "budgetYear")]
        public int budgetYear { get; set; }

        [StoredProcedureParameter(SqlDbType.VarChar, ParameterName = "importType")]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string importType { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [StoredProcedureParameter(SqlDbType.Date, ParameterName = "importedDate")]
        public DateTime importedDate { get; set; }

        [StoredProcedureParameter(SqlDbType.VarChar, ParameterName = "status")]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string status { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [StoredProcedureParameter(SqlDbType.Date, ParameterName = "publishDate")]
        public DateTime? publishDate { get; set; }

        [StoredProcedureParameter(SqlDbType.DateTime, ParameterName = "updated")]
        public DateTime updated { get; set; }

        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "updatedBy")]
        public int updatedBy { get; set; }

        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "useSalaryTable")]
        public int useSalaryTable { get; set; }

        [StoredProcedureParameter(SqlDbType.NVarChar, ParameterName = "salaryTableName")]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string salaryTableName { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
    }
}
