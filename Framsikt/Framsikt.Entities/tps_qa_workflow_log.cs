namespace Framsikt.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;

    public partial class tps_qa_workflow_log
    {
        [Key]
        public int pk_id { get; set; }


        public Guid fk_question_id { get; set; }


        public int fk_tenant_id { get; set; }


        public int budget_year { get; set; }

        public int? status { get; set; }

        [Required(AllowEmptyStrings = true)]
        [StringLength(35)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string work_flow_action { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [Required(AllowEmptyStrings = true)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string comments { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.


        public int responsible { get; set; }


        public DateTime updated { get; set; }

        public int updated_by { get; set; }
    }
}
