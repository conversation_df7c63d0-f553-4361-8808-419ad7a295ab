using EntityFrameworkExtras.EFCore;
using System.Collections.Generic;
using System.Data;

namespace Framsikt.Entities
{
    [StoredProcedure("prcInsertIntoTbuForecastTransactions")]
    public class prcInsertIntoTbuForecastTransactions
    {
        [StoredProcedureParameter(SqlDbType.Udt, ParameterName = "udtForecastTran")]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public List<udtTbuForecastTransactionsType> udtForecastTran { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
    }
}
