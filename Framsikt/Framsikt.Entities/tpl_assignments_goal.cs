using System;
using System.ComponentModel.DataAnnotations;

namespace Framsikt.Entities
{
    public partial class tpl_assignments_goal
    {
        [Key]
        public int pk_id { get; set; }

        public int fk_tenant_id { get; set; }

        public Guid fk_plan_assignment_id { get; set; }

        public Guid fk_plan_goal_id { get; set; }

        public DateTime updated { get; set; }

        public int updated_by { get; set; }

#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public virtual tpl_assignments tpl_assignments { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
    }
}
