namespace Framsikt.Entities
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    

    public partial class tco_newyear_log
    {
        [Key]
        
        public Guid pk_id { get; set; }

        [Required]
        [StringLength(25)]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string operation_name { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        public int fk_tenant_id { get; set; }

        public int budget_year { get; set; }

        public int updated_by { get; set; }

        public DateTime updated { get; set; }
    }
}
