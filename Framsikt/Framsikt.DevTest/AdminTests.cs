using Microsoft.VisualStudio.TestTools.UnitTesting;
using Newtonsoft.Json;
using NSass;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;

namespace Framsikt.DevTest
{

    [TestClass]
    public class AdminTests
    {

        //[TestMethod]
        //public async Task TestGetLangStrings()
        //{
        //    //string userName = "<EMAIL>";
        //    //IAppDataCache dc = new AppDataCache(false, "",null);
        //    //IUtility utilBL = new Utility(dc, new TestDbContextManager(dc, _tokenManager, userName), _tokenManager);

        //    //var sut = new AdminLangStrings(utilBL);
        //    //List<LangStringOverride> ls = await sut.GetLangStringsWithOverrides(userName);
        //    //string strLs = JsonConvert.SerializeObject(ls);
        //}
        [TestMethod]
        public void TestRGB()
        {
            Color test = Color.FromArgb(28864);
        }
        [TestMethod]
        public void TestDate()
        {
            DateTime dt = DateTime.Now;
            string strdt = dt.ToShortDateString();
        }

        [TestMethod]
        public void TestTryParse()
        {
            string val = string.Empty;
            decimal parsedVal;
            bool success = decimal.TryParse("0,23", out parsedVal);
        }

        [TestMethod]
        public void TestUrl()
        {
            Uri url = new Uri("https://www.framsikt.net/Content/CKGetImage?fileName=35c17b60-37f8-4587-8b3f-7192fa6bb9a6.JPG&amp;t=18");
            string fileName = HttpUtility.ParseQueryString(url.Query).Get("fileName");
            //bool proper = Uri.IsWellFormedUriString("http://www.google.co.in", UriKind.Absolute);
            //bool improper = Uri.IsWellFormedUriString("arbeidskapital", UriKind.Absolute);
        }
        [TestMethod]
        public void TestToString()
        {
            List<string> testList = new List<string>()
            {
                "one",
                "two",
                "three"
            };

            IEnumerable<string> testEnum = testList;

            string testStr = JsonConvert.SerializeObject(testEnum);
        }

        [TestMethod]
        public void TestTrial()
        {
            //Load all lines from file

            string textFromFile = System.IO.File.ReadAllText(@"D:\temp\color_definitions.scss");

            Dictionary<string, string> overRides = new Dictionary<string, string>
            {
                { "light-blue", "#fff" },
                { "test-color", "#2342342" }
            };


            //Clean up
            //Remove block comments
            textFromFile = Regex.Replace(textFromFile, "\\/\\*(.*?)\\*\\/", string.Empty);
            //Remove new lines
            //textFromFile = textFromFile.Replace("\n", string.Empty).Replace("\r", string.Empty);

            List<string> splitLines = textFromFile.Split(';').ToList();

            List<string> outputLines = new List<string>();

            foreach (var line in splitLines)
            {
                string cleanLine = line.Replace("\n", string.Empty).Replace("\r", string.Empty);
                if (string.IsNullOrEmpty(cleanLine) || string.IsNullOrWhiteSpace(cleanLine)) continue;
                var overRide = overRides.FirstOrDefault(x => cleanLine.Contains(x.Key));
                if (!string.IsNullOrEmpty(overRide.Key))
                {
                    outputLines.Add($"${overRide.Key}: {overRide.Value};");
                }
                else
                {
                    outputLines.Add($"{cleanLine};");
                }
            }

            string colorPaletteScss = System.IO.File.ReadAllText(@"D:\temp\color_palette_style.scss");
            colorPaletteScss = colorPaletteScss.Replace("@import \"color_definitions\";", string.Empty);

            StringBuilder colorPaletteMerged = new StringBuilder();
            foreach (var line in outputLines)
            {
                colorPaletteMerged.AppendLine(line);
            }

            colorPaletteMerged.Append(colorPaletteScss);

            var compiler = new SassCompiler();
            string scss = colorPaletteMerged.ToString();
            string compiledCss = compiler.Compile(scss);
            System.IO.File.WriteAllText(@"D:\temp\test.scss", compiledCss);
        }

        [TestMethod]
        public void TestVariableExtractor()
        {
            //Load all lines from file

            string textFromFile = System.IO.File.ReadAllText(@"D:\temp\color_definitions.scss");

            MatchCollection extract = Regex.Matches(textFromFile, "\\$(.*?)\\:");

        }

        [TestMethod]
        public void TestUrl2()
        {
            Uri abs = new Uri("https://dev.framsikt.net/ManageUser/ManageUserOverview");
            Uri rel = new Uri("../ManageUser/ManageUserOverview", UriKind.Relative);
            bool ret = Uri.IsWellFormedUriString(abs.ToString(), UriKind.Absolute);
            ret = Uri.IsWellFormedUriString(abs.ToString(), UriKind.Relative);
        }
    }
}
