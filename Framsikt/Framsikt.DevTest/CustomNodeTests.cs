using Framsikt.BL;
using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Framsikt.DocExportWorker;
using Microsoft.Practices.Unity;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Collections.Generic;

namespace Framsikt.DevTest
{
    [TestClass]
    public class CustomNodeTests
    {
        private readonly IDbTokenManager _tokenManager;
        public CustomNodeTests()
        {
            _tokenManager = new DbTokenManager();
        }

        [TestMethod]
        public void TestGetCustomNodes()
        {
            string userName = "<EMAIL>";
            //string tenantId = "1";
            int budgetYear = 2017;


            UnityContainer container = new UnityContainer();
            IAppDataCache dc = new AppDataCache(true, "", new CacheClientFactory());
            IUtility utilBL = new Utility(dc, new DbContextManagerTests(dc, _tokenManager, userName), _tokenManager, null);
            UserData ud = utilBL.GetUserDetails(userName);
            ContainerBootstraper.RegisterTypes(container, ud, GetBudgetYearsForExport(budgetYear), string.Empty);
            var sut = container.Resolve<ICustomNodeManager>();
            var ret = sut.GetCustomNodes(userName, budgetYear, PublishTreeType.MonthlyReport, string.Empty);
        }

        [TestMethod]
        public void TestCreateCustomNode()
        {
            //string userName = "<EMAIL>";
            ////string tenantId = "1";
            //int budgetYear = 2017;


            //UnityContainer container = new UnityContainer();
            //IAppDataCache dc = new AppDataCache(true, "");
            //IUtility utilBL = new Utility(dc, new TestDbContextManager(dc, _tokenManager, userName), _tokenManager);
            //UserData ud = utilBL.GetUserDetails(userName);
            //ContainerBootstraper.RegisterTypes(container, ud, GetBudgetYearsForExport(budgetYear));
            //var sut = container.Resolve<ICustomNode>();
            //string html = "<p>This is a test node</p>";
            //sut.CreateCustomNode(userName, PublishTreeType.MonthlyReport, budgetYear, "Test Node", html,false);
        }

        [TestMethod]
        public void TestUpdateCustomNode()
        {
            //string userName = "<EMAIL>";
            ////string tenantId = "1";
            //int budgetYear = 2017;


            //UnityContainer container = new UnityContainer();
            //IAppDataCache dc = new AppDataCache(true, "");
            //IUtility utilBL = new Utility(dc, new TestDbContextManager(dc, _tokenManager, userName), _tokenManager);
            //UserData ud = utilBL.GetUserDetails(userName);
            //ContainerBootstraper.RegisterTypes(container, ud, GetBudgetYearsForExport(budgetYear));
            //var sut = container.Resolve<ICustomNode>();
            //string html = "<p>This is a test node</p>";
            //PublishTreeNode newNode = sut.CreateCustomNode(userName, PublishTreeType.MonthlyReport, budgetYear, "Test Node", html,false);
            //html = "<p>This is the updated text</p>";
            //bool ret = sut.UpdateCustomNodeContent(userName, Guid.Parse(newNode.id), "Updated Title", html);
        }

        [TestMethod]
        public void TestDeleteCustomNode()
        {
            //string userName = "<EMAIL>";
            ////string tenantId = "1";
            //int budgetYear = 2017;


            //UnityContainer container = new UnityContainer();
            //IAppDataCache dc = new AppDataCache(true, "");
            //IUtility utilBL = new Utility(dc, new TestDbContextManager(dc, _tokenManager, userName), _tokenManager);
            //UserData ud = utilBL.GetUserDetails(userName);
            //ContainerBootstraper.RegisterTypes(container, ud, GetBudgetYearsForExport(budgetYear));
            //var sut = container.Resolve<ICustomNode>();
            //string html = "<p>This is a test node</p>";
            //PublishTreeNode newNode = sut.CreateCustomNode(userName, PublishTreeType.MonthlyReport, budgetYear, "Test Node", html,false);
            //bool ret = sut.DeleteCustomNode(userName, Guid.Parse(newNode.id));
        }
        private Dictionary<string, int> GetBudgetYearsForExport(int budgetYear)
        {
            Dictionary<string, int> modBudgetYears = new Dictionary<string, int>();
            modBudgetYears.Add("KOSTRA_BUDGET_YEAR", budgetYear - 1);
            modBudgetYears.Add("POPSTAT_BUDGET_YEAR", budgetYear - 1);
            modBudgetYears.Add("BUDGETTASK_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("UTILITY_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("BUDGETASSUMPTION_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("BUDMAN_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("BUDPROP_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("CAB_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("INVESTMENTS_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("FINANCING_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("KPIDATA_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("OPPASSMNT_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("STAFFPLAN_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("YEARLYBUDGET_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("BUDGETREGULATION_BUDGET_YEAR", budgetYear);

            return modBudgetYears;
        }
    }
}
