using System;
using Framsikt.BL;
using Framsikt.BL.Helpers;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace Framsikt.DevTest
{
    [TestClass]
    public class TestKpi
    {
        [TestMethod]
        public void TestMethod1()
        {
            IAppDataCache pAppDataCache = new AppDataCache(false);
            IUtility pUtility = new Utility(pAppDataCache);
            IBackendJob pBackendJob = new BackendJob();
            IKostraData pKostraData = new KostraData(pUtility, pAppDataCache, pBackendJob);
            var sut = new KPIData(pUtility, pKostraData);

            var ret = sut.GetKPIData("<EMAIL>", clsConstants.BM_KPI_ScreenIDs.BM.ToString());
        }
    }
}
