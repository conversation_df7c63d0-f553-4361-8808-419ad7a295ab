using Framsikt.BL.Helpers;
using Framsikt.BL.Plan.Helpers;
using Framsikt.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Framsikt.BL.Plan.Repository
{
    public interface IPublishRepository
    {
        Task<TcoPublishTemplate> GetPublishTemplatebyID(int tenantId, int id);

        tco_publish_config? GetPublishConfigByIdForPlanPreview(int configId);

        Task<tco_publish_config> GetPublishConfigById(int configId);

        Task<tco_publish_config> GetPublishConfig(int tenantId, int templateId);

        Task<PlanPublishDetailHelper> GetConfigDetails(int tenantId, Guid planId);

        Task<string> GetUserData(int tenantId, int userId);

        Task<PlanChapterIntroText> GetPlanMainChapters(int tenantId, Guid planId);

        Task<tpl_frontpagegoals_image?> GetFrontPageGoalsImageData(int tenantId, Guid planId, PlanOptionsType options, string uniqueId, int publishTemplateId);

        Task<IEnumerable<PlanFrontPageGoalGridHelper>> GetGoalConfigData(int tenantId, Guid planId, int publishTemplateId, KeyValueStringData langStrings);

        Task<IEnumerable<keyvaluewithGuid>> GetUnSustainabilityGoalIds(int tenantId, Guid planId, int publishTemplateId);

        Task<IEnumerable<PlanFrontPageGoalGridHelper>> GetUnSustainabilityGoalData(int tenantId, List<string> sustainibilitygoals, KeyValueStringData langStrings, int publishTemplateId);

        Task<IEnumerable<PlanFrontPageGoalGridHelper>> GetFocusAreaConfigData(int tenantId, Guid planId, int publishTemplateId, KeyValueStringData langStrings);

        Task<vw_tco_parameters> GetParamDefinitions(int tenantId, string paramName);

        Task RemoveDataFromFrontPageGoalsImageTable(int tenantId, PlanOptionsType optionType, Guid planId);

        Task<PlanOverviewPublishConfigData?> GetPlanOverviewConfigData(int tenantId);

        Task<IEnumerable<PlanConfigData>> GetPlansConfigDataperTenant(int tenantId);

        Task<gco_doc_table_defs?> GetGlobalTableDefinition(string tableId);

        Task<IEnumerable<gco_column_defs>> GetGlobalColumnDefinition(string tableId);

        Task<PlanTableDefOverrides?> GetTableOverrideDefinition(Guid planId, string tableId, int tenantId);

        Task<IEnumerable<PlanColumnDefOverrides>> GetColumnOverrideDefinition(Guid planId, string tableId, int tenantId);

        Task<PlanObjectsConfig?> GetPlanObjectConfigInfo(Guid planId, int tenantId);

        Task<tpl_planningstrategy_category_config?> GetStrategyCategotyConfig(Guid planId, Guid nodeId, int tenantId);

        Task<tpl_plan> GetPlanData(Guid planId, int tenantId);

        Task<IEnumerable<tpl_frontpagegoals_image>> GetGoalImagesforFrontPagebyPublishId(int tenantId, Guid planId, int publishId);

        Task<tpl_plan_previewtemplate> GetPreviewTemplatePerUser(int tenantId, Guid planId, int userId);

        Task<PlanPublishDetailHelper> GetPreviewConfigDetails(int tenantId, Guid planId, int userId);
        Task<TplPlanNodeConfig?> GetPlanNodeConfig(int tenantId, Guid planId, string nodeId);
        Task<IEnumerable<tco_doc_widget>> WidgetConfiguration(Guid widgetId, int tenantId, int budgetYear, bool isPlan);
    }
}