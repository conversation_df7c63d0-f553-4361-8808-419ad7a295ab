#pragma warning disable CS8629

#pragma warning disable CS8601
#pragma warning disable CS8602
#pragma warning disable CS8603

using Framsikt.BL.Helpers;
using Framsikt.BL.Plan.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;

namespace Framsikt.BL.Plan.Repository
{
    public class PublishRepository : IPublishRepository
    {
        private readonly TenantDBContext _tenantDBContext;

        public PublishRepository(TenantDBContext dbContext)
        {
            _tenantDBContext = dbContext;
        }

        public async Task<TcoPublishTemplate> GetPublishTemplatebyID(int tenantId, int id)
        {
            return await _tenantDBContext.TcoPublishTemplate.FirstOrDefaultAsync(x => x.FkTenantId == tenantId && x.PkId == id);
        }

        public tco_publish_config? GetPublishConfigByIdForPlanPreview(int configId)
        {
            return _tenantDBContext.tco_publish_config.FirstOrDefault(x => x.pk_id == configId);
        }

        public async Task<tco_publish_config> GetPublishConfigById(int configId)
        {
            return await _tenantDBContext.tco_publish_config.FirstOrDefaultAsync(x => x.pk_id == configId);
        }

        public async Task<tco_publish_config> GetPublishConfig(int tenantId, int templateId)
        {
            return await _tenantDBContext.tco_publish_config.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.fk_template_id == templateId);
        }

        public async Task<PlanPublishDetailHelper> GetConfigDetails(int tenantId, Guid planId)
        {
            var planData = await _tenantDBContext.tpl_plan.Where(x => x.fk_tenant_id == tenantId && x.pk_plan_id == planId)
                                                    .Include(x => x.tpl_plan_template_details).FirstOrDefaultAsync();
            if (planData.publish_template_id != 0)
            {
                var configData = await (from a in _tenantDBContext.TcoPublishTemplate
                                        join b in _tenantDBContext.tco_publish_config on new { tID = a.FkTenantId, templateId = a.PkId }
                                                                                  equals new { tID = b.fk_tenant_id, templateId = b.fk_template_id }
                                        where a.TreeType == PublishTreeType.PlanModule.ToString() && a.PkId == planData.publish_template_id && a.FkTenantId == tenantId
                                        select new PlanPublishDetailHelper
                                        {
                                            ConfigId = b.pk_id,
                                            TemplateId = a.PkId,
                                            TemplateUrl = a.TemplateUrl,
                                        }).FirstOrDefaultAsync();
                configData.PlanData = planData;
                return configData;
            }
            return new PlanPublishDetailHelper();
        }

        public async Task<string> GetUserData(int tenantId, int userId)
        {
            var userData = await _tenantDBContext.vwUserDetails.FirstOrDefaultAsync(y => y.pk_id == userId);
            if (userData != null)
            {
                return userData.first_name + " " + userData.last_name;
            }
            return string.Empty;
        }

        public async Task<PlanChapterIntroText> GetPlanMainChapters(int tenantId, Guid planId)
        {
            var planData = await _tenantDBContext.tpl_plan.Where(y => y.pk_plan_id == planId && y.fk_tenant_id == tenantId)
                                                     .Include(x => x.tpl_plan_template_details).FirstOrDefaultAsync();

            var planAbstractTextDataAsync = await _tenantDBContext.tpl_plan_texts.Where(y => y.fk_plan_id == planId && y.fk_tenant_id == tenantId).ToListAsync();
            var planAbstractTextData = (from a in planAbstractTextDataAsync
                                        group a by new { a.fk_plan_id, a.node_id } into x
                                        select new
                                        {
                                            x.Key.fk_plan_id,
                                            x.Key.node_id,
                                            Id = x.Min(z => z.pk_id),
                                        }).ToList();

            var mainChapters = planData.tpl_plan_template_details.Where(x => x.status.Value && x.parent_node_id == Guid.Empty).OrderBy(z => z.node_order).ToList();

            if (mainChapters.Any())
            {
                var planChapterTexts = (from a in mainChapters
                                        join b in planAbstractTextData.ToList() on a.node_id equals b.node_id into x1
                                        from textData in x1.DefaultIfEmpty()
                                        select new PlanFrontPageChapterHelper
                                        {
                                            Url = $"/generic/summary/{a.node_id}",
                                            Title = a.node_name,
                                            AbstractText = (textData != null && a.node_type == ContentNodeType.Text.ToString()) ? planAbstractTextDataAsync.FirstOrDefault(x => x.pk_id == textData.Id).abstract_text : string.Empty,
                                            VideoUrl = string.Empty,
                                            ImageUrl = $"content/images/summary/{a.node_id}/thumbnail.jpg",
                                            AltText = string.Empty,
                                            NodeId = a.node_id
                                        }).ToList();

                return new PlanChapterIntroText { PlanChapterTexts = planChapterTexts };
            }

            return new PlanChapterIntroText { PlanChapterTexts = new List<PlanFrontPageChapterHelper>() };
        }

        public async Task<tpl_frontpagegoals_image?> GetFrontPageGoalsImageData(int tenantId, Guid planId, PlanOptionsType options, string uniqueId, int publishTemplateId)
        {
            return await _tenantDBContext.tpl_frontpagegoals_image.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId &&
                                                                                            x.fk_plan_id == planId &&
                                                                                            x.unique_id_type == options.ToString() &&
                                                                                            x.unique_id == uniqueId &&
                                                                                            x.publish_template_id == publishTemplateId);
        }

        public async Task RemoveDataFromFrontPageGoalsImageTable(int tenantId, PlanOptionsType optionType, Guid planId)
        {
            var planData = await _tenantDBContext.tpl_plan.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.pk_plan_id == planId);
            List<tpl_frontpagegoals_image> dataToRemove = await _tenantDBContext.tpl_frontpagegoals_image.Where(x => x.fk_tenant_id == tenantId && x.unique_id_type == optionType.ToString()
                                                           && x.publish_template_id == planData.publish_template_id).ToListAsync();
            if (dataToRemove.Any())
            {
                _tenantDBContext.tpl_frontpagegoals_image.RemoveRange(dataToRemove);
            }
        }

        public async Task<IEnumerable<PlanFrontPageGoalGridHelper>> GetGoalConfigData(int tenantId, Guid planId, int publishTemplateId, KeyValueStringData langStrings)
        {
            var result = await (from p in _tenantDBContext.tpl_plan
                                join pg in _tenantDBContext.tpl_plan_goal on new { a = p.fk_tenant_id, b = p.pk_plan_id, c = p.publish_template_id } equals new { a = pg.fk_tenant_id, b = pg.fk_plan_id, c = publishTemplateId }
                                join g in _tenantDBContext.tpl_goal on new { a = pg.fk_plan_goal_id, b = pg.fk_tenant_id } equals new { a = g.pk_plan_goal_id, b = g.fk_tenant_id }
                                join img in _tenantDBContext.tpl_frontpagegoals_image.Where(z => z.fk_tenant_id == tenantId && z.unique_id_type == PlanOptionsType.Goals.ToString() && z.publish_template_id == publishTemplateId) on new { a = g.fk_tenant_id, b = g.pk_plan_goal_id.ToString() }
                                          equals new { a = img.fk_tenant_id, b = img.unique_id } into imgRes
                                from res in imgRes.DefaultIfEmpty()
                                where g.fk_tenant_id == tenantId && p.pk_plan_id == planId
                                group new { g, res } by new { g.pk_plan_goal_id, g.name, res.image_name, res.image_path } into grp
                                select new PlanFrontPageGoalGridHelper()
                                {
                                    UniqueId = grp.Key.pk_plan_goal_id.ToString(),
                                    Name = grp.Key.name,
                                    KeyFigureImage = new GlobalKeyFiguresImageHelper { Key = grp.Key.image_name == null ? string.Empty : grp.Key.image_name, Value = grp.Key.image_path == null ? string.Empty : grp.Key.image_path },
                                    SortOrder = 0,
                                    IsIncludedInPublish = false,
                                    IconText = (grp.Key.image_path != null && !string.IsNullOrEmpty(grp.Key.image_path)) ? langStrings.Value : langStrings.Key
                                }).ToListAsync();
            return result;
        }

        public async Task<IEnumerable<keyvaluewithGuid>> GetUnSustainabilityGoalIds(int tenantId, Guid planId, int publishTemplateId)
        {
            var result = await (from p in _tenantDBContext.tpl_plan
                                join pg in _tenantDBContext.tpl_plan_goal on new { a = p.fk_tenant_id, b = p.pk_plan_id, c = p.publish_template_id } equals new { a = pg.fk_tenant_id, b = pg.fk_plan_id, c = publishTemplateId }
                                join g in _tenantDBContext.tpl_goal on new { a = pg.fk_plan_goal_id, b = pg.fk_tenant_id } equals new { a = g.pk_plan_goal_id, b = g.fk_tenant_id }
                                where g.fk_tenant_id == tenantId && p.pk_plan_id == planId && !string.IsNullOrEmpty(g.fk_sustainable_goal_id)
                                select new keyvaluewithGuid()
                                {
                                    key = g.pk_plan_goal_id,
                                    value = g.fk_sustainable_goal_id,
                                }).ToListAsync();
            return result;
        }

        public async Task<IEnumerable<PlanFrontPageGoalGridHelper>> GetUnSustainabilityGoalData(int tenantId, List<string> sustainibilitygoals, KeyValueStringData langStrings, int publishTemplateId)
        {
            var result = await (from g in _tenantDBContext.gco_un_susdev_goals
                                join img in _tenantDBContext.tpl_frontpagegoals_image.Where(z => z.fk_tenant_id == tenantId && z.unique_id_type == PlanOptionsType.UnSustainability.ToString() && z.publish_template_id == publishTemplateId)
                                on new { a = tenantId, b = g.pk_goal_id }
                                equals new { a = img.fk_tenant_id, b = img.unique_id } into imgRes
                                from res in imgRes.DefaultIfEmpty()
                                where sustainibilitygoals.Contains(g.pk_goal_id)
                                group new { g, res } by new { g.pk_goal_id, g.goal_short_name, res.image_name, res.image_path } into grp
                                select new PlanFrontPageGoalGridHelper()
                                {
                                    UniqueId = grp.Key.pk_goal_id,
                                    Name = grp.Key.goal_short_name,
                                    KeyFigureImage = new GlobalKeyFiguresImageHelper { Key = grp.Key.image_name == null ? string.Empty : grp.Key.image_name, Value = grp.Key.image_path == null ? string.Empty : grp.Key.image_path },
                                    SortOrder = 0,
                                    IsIncludedInPublish = false,
                                    IconText = (grp.Key.image_path != null && !string.IsNullOrEmpty(grp.Key.image_path)) ? langStrings.Value : langStrings.Key
                                }).ToListAsync();
            return result;
        }

        public async Task<IEnumerable<PlanFrontPageGoalGridHelper>> GetFocusAreaConfigData(int tenantId, Guid planId, int publishTemplateId, KeyValueStringData langStrings)
        {
            var result = await (from p in _tenantDBContext.tpl_plan
                                join pg in _tenantDBContext.tpl_plan_goal on new { a = p.fk_tenant_id, b = p.pk_plan_id, c = p.publish_template_id } equals new { a = pg.fk_tenant_id, b = pg.fk_plan_id, c = publishTemplateId }
                                join g in _tenantDBContext.tpl_goal on new { a = pg.fk_plan_goal_id, b = pg.fk_tenant_id } equals new { a = g.pk_plan_goal_id, b = g.fk_tenant_id }
                                join f in _tenantDBContext.tpl_focusarea on new { a = g.fk_tenant_id, b = (int?)g.fk_focusarea_id ?? 0 } equals new { a = f.fk_tenant_id, b = f.pk_focus_area_id }
                                join img in _tenantDBContext.tpl_frontpagegoals_image.Where(z => z.fk_tenant_id == tenantId && z.unique_id_type == PlanOptionsType.FocusArea.ToString() && z.publish_template_id == publishTemplateId) on new { a = g.fk_tenant_id, b = f.pk_focus_area_id.ToString() }
                                          equals new { a = img.fk_tenant_id, b = img.unique_id } into imgRes
                                from res in imgRes.DefaultIfEmpty()
                                where g.fk_tenant_id == tenantId && p.pk_plan_id == planId
                                group new { f, res } by new { f.pk_focus_area_id, f.name, res.image_name, res.image_path } into grp
                                select new PlanFrontPageGoalGridHelper()
                                {
                                    UniqueId = grp.Key.pk_focus_area_id.ToString(),
                                    Name = grp.Key.name,
                                    KeyFigureImage = new GlobalKeyFiguresImageHelper { Key = grp.Key.image_name == null ? string.Empty : grp.Key.image_name, Value = grp.Key.image_path == null ? string.Empty : grp.Key.image_path },
                                    SortOrder = 0,
                                    IsIncludedInPublish = false,
                                    IconText = (grp.Key.image_path != null && !string.IsNullOrEmpty(grp.Key.image_path)) ? langStrings.Value : langStrings.Key
                                }).ToListAsync();
            return result;
        }

        public async Task<vw_tco_parameters> GetParamDefinitions(int tenantId, string paramName)
        {
            return await _tenantDBContext.vw_tco_parameters.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.param_name.ToLower().Trim() == paramName.ToLower().Trim());
        }

        public async Task<PlanOverviewPublishConfigData?> GetPlanOverviewConfigData(int tenantId)
        {
            var configData = await (from a in _tenantDBContext.TcoPublishTemplate
                                    join b in _tenantDBContext.tco_publish_config on new { tID = a.FkTenantId, templateId = a.PkId }
                                                                              equals new { tID = b.fk_tenant_id, templateId = b.fk_template_id }
                                    where a.TreeType == PublishTreeType.PlanOverview.ToString() && a.FkTenantId == tenantId
                                    select new PlanOverviewPublishConfigData
                                    {
                                        ConfigId = b.pk_id,
                                        TemplateId = a.PkId,
                                    }).FirstOrDefaultAsync();
            return configData;
        }

        public async Task<IEnumerable<PlanConfigData>> GetPlansConfigDataperTenant(int tenantId)
        {
            var configData = await (from plan in _tenantDBContext.tpl_plan
                                    join c in _tenantDBContext.tco_plan_type on new { tID = plan.fk_tenant_id, planTypeiD = plan.fk_plan_type_id }
                                                                              equals new { tID = c.TenantId, planTypeiD = c.Id } into pt1
                                    from pt in pt1.DefaultIfEmpty()

                                    join b in _tenantDBContext.tco_publish_config on new { tID = plan.fk_tenant_id, templateId = plan.publish_template_id }
                                                                              equals new { tID = b.fk_tenant_id, templateId = b.fk_template_id } into config
                                    from config1 in config.DefaultIfEmpty()
                                    where plan.fk_tenant_id == tenantId
                                    select new PlanConfigData
                                    {
                                        PlanId = plan.pk_plan_id,
                                        PlanTypeId = pt != null ? pt.Id : Guid.Empty,
                                        ConfigId = config1 == null ? 0 : config1.pk_id,
                                        TemplateId = config1 == null ? 0 : config1.fk_template_id,
                                        ProdStatus = config1 == null ? 0 : config1.prod_status,
                                        IsOldPlan = plan.is_old_plan,
                                        ApprovedDate = plan.date_processed,
                                        ShortName = config1 != null ? config1.short_name : string.Empty,
                                        Tree_type = config1 != null ? config1.tree_type : string.Empty
                                    }).ToListAsync();
            return configData;
        }

        public async Task<gco_doc_table_defs?> GetGlobalTableDefinition(string tableId)
        {
            return await _tenantDBContext.gco_doc_table_defs.FirstOrDefaultAsync(x => x.table_id == tableId);
        }

        public async Task<IEnumerable<gco_column_defs>> GetGlobalColumnDefinition(string tableId)
        {
            return await _tenantDBContext.gco_column_defs.Where(x => x.table_id == tableId).ToListAsync();
        }

        public async Task<PlanTableDefOverrides?> GetTableOverrideDefinition(Guid planId, string tableId, int tenantId)
        {
            return await _tenantDBContext.PlanTableDefOverrides.FirstOrDefaultAsync(x => x.TableId == tableId && x.TenantId == tenantId && x.PlanId == planId);
        }

        public async Task<IEnumerable<PlanColumnDefOverrides>> GetColumnOverrideDefinition(Guid planId, string tableId, int tenantId)
        {
            return await _tenantDBContext.PlanColumnDefOverrides.Where(x => x.TableId == tableId && x.TenantId == tenantId && x.PlanId == planId).ToListAsync();
        }

        public async Task<PlanObjectsConfig?> GetPlanObjectConfigInfo(Guid planId, int tenantId)
        {
            return await _tenantDBContext.PlanObjectsConfig.FirstOrDefaultAsync(x => x.PlanId == planId && x.TenantId == tenantId);
        }

        public async Task<tpl_planningstrategy_category_config?> GetStrategyCategotyConfig(Guid planId, Guid nodeId, int tenantId)
        {
            return await _tenantDBContext.tpl_planningstrategy_category_config.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.fk_plan_id == planId && x.fk_node_id == nodeId);
        }

        public async Task<tpl_plan> GetPlanData(Guid planId, int tenantId)
        {
            return await _tenantDBContext.tpl_plan.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.pk_plan_id == planId);
        }

        public async Task<IEnumerable<tpl_frontpagegoals_image>> GetGoalImagesforFrontPagebyPublishId(int tenantId, Guid planId, int publishId)
        {
            return await _tenantDBContext.tpl_frontpagegoals_image.Where(x => x.fk_tenant_id == tenantId && x.fk_plan_id == planId && x.publish_template_id == publishId).ToListAsync();
        }

        public async Task<tpl_plan_previewtemplate> GetPreviewTemplatePerUser(int tenantId, Guid planId, int userId)
        {
            return await _tenantDBContext.tpl_plan_previewtemplate.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.fk_plan_id == planId && x.fk_user_id == userId);
        }

        public async Task<PlanPublishDetailHelper> GetPreviewConfigDetails(int tenantId, Guid planId, int userId)
        {
            var previewData = await GetPreviewTemplatePerUser(tenantId, planId, userId);
            var planData = await _tenantDBContext.tpl_plan.Where(x => x.fk_tenant_id == tenantId && x.pk_plan_id == planId)
                                                    .Include(x => x.tpl_plan_template_details).FirstOrDefaultAsync();
            if (previewData.preview_template_id != 0)
            {
                var configData = await (from a in _tenantDBContext.TcoPublishTemplate
                                        join b in _tenantDBContext.tco_publish_config on new { tID = a.FkTenantId, templateId = a.PkId }
                                                                                  equals new { tID = b.fk_tenant_id, templateId = b.fk_template_id }
                                        where a.TreeType == PublishTreeType.PlanPreview.ToString() && a.PkId == previewData.preview_template_id && a.FkTenantId == tenantId
                                        select new PlanPublishDetailHelper
                                        {
                                            ConfigId = b.pk_id,
                                            TemplateId = a.PkId,
                                            TemplateUrl = a.TemplateUrl,
                                        }).FirstOrDefaultAsync();
                configData.PlanData = planData;
                return configData;
            }
            return new PlanPublishDetailHelper();
        }

        public async Task<TplPlanNodeConfig?> GetPlanNodeConfig(int tenantId, Guid planId, string nodeId)
        {
            return await _tenantDBContext.TplPlanNodeConfig.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.PlanId == planId && x.NodeId == nodeId);
        }

        public async Task<IEnumerable<tco_doc_widget>> WidgetConfiguration(Guid widgetId, int tenantId, int budgetYear, bool isPlan)
        {
            if (!isPlan)
            {
                return await _tenantDBContext.tco_doc_widget.Where(x => x.pk_id == widgetId && x.fk_tenant_id == tenantId && x.budget_year == budgetYear).ToListAsync();
            }
            else
            {
                return await _tenantDBContext.tco_doc_widget.Where(x => x.pk_id == widgetId && x.fk_tenant_id == tenantId).ToListAsync();
            }
        }
    }
}