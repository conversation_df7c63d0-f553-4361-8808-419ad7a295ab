using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Framsikt.Entities.Core;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using static Framsikt.BL.DocWidget;

namespace Framsikt.BL.Repository
{
    public interface IPlanDiscAdminRepo
    {
        Task<tpd_admin_plandiscs_setup?> GetPlanDiscInfo(int pkId , int tenantId);
        Task<List<PlanDiscAdminGridHelper>> GetAllPlanDisc(int tenantId);
        Task<tpd_admin_plandiscs_setup_log?> GetPlanDiscLogInfo(int planDiscId, opertationtype opertation);
        Task<tpd_admin_plandiscs_setup?> GetActivePlanDiscDetail(int planDiscId, int tenantId);
        Task<List<tpd_plandisc_admin_setup_delegation>> GetPlanDiscDelegations(int planDiscId, int tenantId);
        Task DeletePlanDiscDelegations(int planDiscId, int tenantId);
        Task<List<PDKeyValuePair>> GetAllAssignmentCategories(int tenantId);
        Task<List<PDKeyValuePairWithOrder>> GetAllAssignmentStatus(int tenantId);
        Task<List<tpd_admin_setup_assignment_dates>> GetAssignmentDates(int planDiscId, int tenantId);
        Task<List<tpd_admin_setup_assignment_tags>> GetAssignmentTags(int planDiscId, int tenantId);
        Task DeleteAssignmentTags(int planDiscId, int tenantId);
        Task<List<tbiassignments>> GetTbiAssignmentsAsync(int tenantId, int budgetYear, List<string> categories, List<string> tags);
        Task<List<tco_service_values>> GetTcoServiceValuesAsync(int tenantId);
        Task<List<PlanDiscAdminLabelHelper>> GetTcoProgressStatusAsync(int tenantId, string planDiscId);
        Task<List<PlanDiscAdminCategoryHelper>> GetTcoCategoryAsync(int tenantId, string planDiscId);        
        Task<List<TpdPlanDiscAssignmentMeetings>> GetPlanDiscMeetingsAsync(int tenantId, int budgetYear, string planDiscId, int planDiscSetupId);
        Task<(List<Guid> CategoryIds, List<Guid> LabelIds)> GetExistingCategoryAndLabelIdsAsync(int tenantId);
        Task UpdateAssignmentCategoryAsync(ActivityColorGroup group, Guid plandiscId, int tenantId, int updatedBy);
        Task CreateAssignmentCategoryAsync(ActivityColorGroup group, Guid plandiscId, int tenantId, int updatedBy);
        Task UpdateAssignmentLabelAsync(LabelDefinition label, Guid plandiscId, int tenantId, int updatedBy);
        Task CreateAssignmentLabelAsync(LabelDefinition label, Guid plandiscId, int tenantId, int updatedBy);
        Task<List<TpdPlanDiscTrackAssignments>> GetPDTrackAssignmentsAsync(int tenantId, int budgetYear);
    }
}
