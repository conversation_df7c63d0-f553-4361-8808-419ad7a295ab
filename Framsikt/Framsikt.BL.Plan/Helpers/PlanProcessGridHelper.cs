#pragma warning disable <PERSON><PERSON>18


using Newtonsoft.Json;

namespace Framsikt.BL.Plan.Helpers
{
    public class PlanProcessGridHelper
    {
        [JsonProperty("pkId")] public string PlanId { get; set; }
        [JsonProperty("plan")] public string Plan { get; set; }
        [JsonProperty("typeId")] public string PlanTypeId { get; set; }
        [JsonProperty("type")] public string PlanTypeName { get; set; }
        [JsonProperty("status")] public string PlanStatus { get; set; }
        [JsonProperty("target")] public bool GoalFlag { get; set; }
        [JsonProperty("measures")] public bool ActionFlag { get; set; }
        [JsonProperty("strategy")] public bool StrategyFlag { get; set; }
        [JsonProperty("assignment")] public bool AssignmentFlag { get; set; }
    }
}
