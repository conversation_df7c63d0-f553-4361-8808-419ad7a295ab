#pragma warning disable CS8625
#pragma warning disable CS8629

#pragma warning disable CS8600
#pragma warning disable CS8601
#pragma warning disable CS8602
#pragma warning disable CS8603
#pragma warning disable CS8604
#pragma warning disable <PERSON>8619

using Framsikt.BL.Constants;
using Framsikt.BL.Helpers;
using Framsikt.BL.Plan.Helpers;
using Framsikt.BL.Plan.Repository;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using StackExchange.Redis;
using System.Text;
using System.Text.RegularExpressions;
using KeyValuePair = Framsikt.BL.Helpers.KeyValuePair;

namespace Framsikt.BL.Plan
{
    public class Planning : IPlanning
    {
        private readonly IPlanUoW _unitOfWork;
        private readonly IUtility _utility;
        private readonly IAzureBlobHelper _blobHelper;
        private readonly IBackendRequest _backendJob;
        private readonly IPlanningStrategy _planningStrategy;
        private readonly IAppDataCache _cache;
        private readonly TimeSpan cacheTimeOut = new TimeSpan(1, 0, 0);
        private const string levelClasshasChildren = "tvicons-level{0}-items";
        private const string levelClasshasNoChildren = "tv-level{0}-items";

        #region public methods

        public Planning(IPlanUoW uow,
                        IUtility util,
                        IAzureBlobHelper blobHelper,
                        IBackendRequest backendJob,
                        IPlanningStrategy planningStrategy,
                        IAppDataCache cache)
        {
            _unitOfWork = uow;
            _utility = util;
            _blobHelper = blobHelper;
            _backendJob = backendJob;
            _planningStrategy = planningStrategy;
            _cache = cache;
        }

        public async Task<IEnumerable<tco_plan_type>> GetPlanTypes(string userId)
        {
            //Update Plan Templates Color ID
            await UpdatePlanTemplatesColorID(userId);

            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            IEnumerable<tco_plan_type> planTypes = (await _unitOfWork.PlanningRepository.GetPlanTemplates(userId, userDetails.tenant_id)).Where(x => x.IsActive).OrderBy(x => x.SortOrder);
            return planTypes;
        }

        public async Task<IEnumerable<tpl_plan_strategy_categories>> GetPlanCategory(string userId)
        {
            UserData userDetail = await _utility.GetUserDetailsAsync(userId);

            return await _unitOfWork.PlanningRepository.GetPlanStrategyCategory(userDetail.tenant_id);
        }

        public async Task<IEnumerable<KeyValuePair<string, string>>> GetOptionalContentTypesAsync(string userId, bool isPlanningStrategy)
        {
            List<KeyValuePair<string, string>> optionalContentTypes = new List<KeyValuePair<string, string>>();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            var langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Planning");
            optionalContentTypes.Add(new KeyValuePair<string, string>(ContentNodeType.Target.ToString(),
                langStrings.FirstOrDefault(x => x.Key.Equals($"PM_Content_Type_{ContentNodeType.Target}")).Value.LangText));

            if (isPlanningStrategy)
            {
                optionalContentTypes.Add(new KeyValuePair<string, string>(ContentNodeType.Strategy.ToString(),
                    langStrings.FirstOrDefault(x => x.Key.Equals($"PM_Content_Type_{ContentNodeType.Strategy}")).Value.LangText));

                optionalContentTypes.Add(new KeyValuePair<string, string>(ContentNodeType.PlanStrategyTask.ToString(),
                    langStrings.FirstOrDefault(x => x.Key.Equals($"PM_Content_Type_{ContentNodeType.PlanStrategyTask}")).Value.LangText));
            }
            else
            {
                optionalContentTypes.Add(new KeyValuePair<string, string>(ContentNodeType.Strategy.ToString(),
                    langStrings.FirstOrDefault(x => x.Key.Equals($"PM_Content_Type_{ContentNodeType.Strategy}")).Value.LangText));

                optionalContentTypes.Add(new KeyValuePair<string, string>(ContentNodeType.Assignment.ToString(),
                    langStrings.FirstOrDefault(x => x.Key.Equals($"PM_Content_Type_{ContentNodeType.Assignment}")).Value.LangText));

                optionalContentTypes.Add(new KeyValuePair<string, string>(ContentNodeType.Action.ToString(),
                    langStrings.FirstOrDefault(x => x.Key.Equals($"PM_Content_Type_{ContentNodeType.Action}")).Value.LangText));

                optionalContentTypes.Add(new KeyValuePair<string, string>(ContentNodeType.Investment.ToString(),
                    langStrings.FirstOrDefault(x => x.Key.Equals($"PM_Content_Type_{ContentNodeType.Investment}")).Value.LangText));

                optionalContentTypes.Add(new KeyValuePair<string, string>(ContentNodeType.ClimateAction.ToString(),
                    langStrings.FirstOrDefault(x => x.Key.Equals($"PM_Content_Type_{ContentNodeType.ClimateAction}")).Value.LangText));
            }
            return optionalContentTypes;
        }

        public async Task<Guid> CreatePlanType(string userId, PlanType template)
        {
            Guid planId = Guid.NewGuid();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            template.Id = planId;
            string blobPath = await ValidateAndUploadBlob(template, userDetails, true);
            var planSortData = await _unitOfWork.PlanningRepository.GetPlanTemplates(userId, userDetails.tenant_id);
            int sortOrder = planSortData.Any() ? (planSortData.OrderByDescending(x => x.SortOrder).FirstOrDefault().SortOrder + 1) : 0;
            //Create a row in the table
            tco_plan_type newPlan = new tco_plan_type
            {
                Id = planId,
                Name = template.Name,
                TemplatePath = blobPath,
                TenantId = userDetails.tenant_id,
                ContentTypesUsed = JsonConvert.SerializeObject(template.AllowedContentNodeTypes),
                ColorId = 0,
                IsActive = true,
                SortOrder = sortOrder,
                limit_goal_access = template.LimitGoalAccess
            };
            _unitOfWork.GenericRepo.Add(newPlan);
            await _unitOfWork.CompleteAsync();
            return planId;
        }

        public async Task UpdatePlanType(string userId, PlanType template)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            await ValidateAndUploadBlob(template, userDetails, true);
            tco_plan_type planRow = await _unitOfWork.PlanningRepository.GetPlanTemplateData(userDetails.tenant_id, template.Id);
            planRow.Name = template.Name;
            planRow.ContentTypesUsed = JsonConvert.SerializeObject(template.AllowedContentNodeTypes);
            planRow.limit_goal_access = template.LimitGoalAccess;

            //Modify Existing plan
            var plansBasedOntemplate = await _unitOfWork.PlanningRepository.GetPlansBasedonTemplateId(userDetails.tenant_id, template.Id);
            if (plansBasedOntemplate.Any())
            {
                plansBasedOntemplate.ToList().ForEach(x => x.contenttypesused = JsonConvert.SerializeObject(template.AllowedContentNodeTypes));
                plansBasedOntemplate.ToList().ForEach(x => x.usage = template.Usage.ToString());

                foreach (var item in plansBasedOntemplate)
                {
                    var planTemplate = (await _unitOfWork.PlanningRepository.GetPlanTree(userDetails.tenant_id, item.pk_plan_id)).ToList();
                    if (planTemplate.Any())
                    {
                        if (template.Usage.ToString() == PlanningNodeType.Guideline.ToString())
                        {
                            planTemplate.ForEach(x => x.usage_type = (int)PlanningNodeType.Guideline);
                        }
                        if (template.Usage.ToString() == PlanningNodeType.Mandatory.ToString())
                        {
                            planTemplate.ForEach(x => x.usage_type = (int)PlanningNodeType.Mandatory);
                        }
                    }
                }
            }
            await _unitOfWork.CompleteAsync();
        }

        public async Task DeletePlanType(string userId, Guid planTypeId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            //Soft Delete Plan Type
            tco_plan_type entity = await _unitOfWork.PlanningRepository.GetPlanTemplateData(userDetails.tenant_id, planTypeId);
            if (entity != null)
            {
                entity.SortOrder = 0;
                entity.IsActive = false;
            }
            await _unitOfWork.CompleteAsync();
        }

        public async Task<PlanType> GetPlanType(string userId, Guid planTypeId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            string blobPath = $"{userDetails.tenant_id}/plantypes/{planTypeId}.json";

            bool isBlobExists = await _blobHelper.BlobExistsAsync(StorageAccount.AppStorage, BlobContainers.Planning, blobPath);
            if (isBlobExists)
            {
                string planTypeStr = await _blobHelper.GetTextBlobAsync(StorageAccount.AppStorage, BlobContainers.Planning, blobPath);
                PlanType planTemplate = JsonConvert.DeserializeObject<PlanType>(planTypeStr);
                return planTemplate;
            }
            return null;
        }

        public async Task<PlanType> GetPlanStrategyType(string userId, Guid planTypeId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            string blobPath = $"{userDetails.tenant_id}/planstrategytype/{planTypeId}.json";
            bool isBlobExists = await _blobHelper.BlobExistsAsync(StorageAccount.AppStorage, BlobContainers.Planning, blobPath);
            if (isBlobExists)
            {
                string planTypeStr = await _blobHelper.GetTextBlobAsync(StorageAccount.AppStorage, BlobContainers.Planning, blobPath);
                PlanType planTemplate = JsonConvert.DeserializeObject<PlanType>(planTypeStr);
                return planTemplate;
            }
            return null;
        }

        public async Task<PlanHelper> GetPlanTypeStructure(string userId, Guid planTypeId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            string blobPath = $"{userDetails.tenant_id}/plantypes/{planTypeId}.json";

            string planTypeStr = await _blobHelper.GetTextBlobAsync(StorageAccount.AppStorage, BlobContainers.Planning, blobPath);
            PlanHelper planTemplate = JsonConvert.DeserializeObject<PlanHelper>(planTypeStr);
            UpdatePlanningTreeLevelNumber(planTemplate.TemplateTree, 0, planTemplate.Usage.ToString(), true);
            return planTemplate;
        }

        public async Task<PlanHelper> GetPlanStrategyTypeStructure(string userId, Guid typeId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            string blobPath = $"{userDetails.tenant_id}/planstrategytype/{typeId}.json";
            string planTypeStr = await _blobHelper.GetTextBlobAsync(StorageAccount.AppStorage, BlobContainers.Planning, blobPath);
            PlanHelper planTemplate = JsonConvert.DeserializeObject<PlanHelper>(planTypeStr);
            UpdatePlanningTreeLevelNumber(planTemplate.TemplateTree, 0, PlanTypeUsage.Guideline.ToString(), true);
            return planTemplate;
        }

        public async Task<PlannningBaseDataHelper> GetMyPlans(string userId, PlansListInputHelper inputHelper)
        {
            //Update Plan Templates Color ID
            if (!inputHelper.IsPlanningStrategy)
                await UpdatePlanTemplatesColorID(userId);

            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            IEnumerable<tpl_plan> plans = !inputHelper.IsPlanningStrategy ? await _unitOfWork.PlanningRepository.GetPlans(userDetails.tenant_id, true)
                                                                         : await _unitOfWork.PlanningRepository.GetStrategyPlans(userDetails.tenant_id, inputHelper.StartYear);

            IEnumerable<tpl_plan_participants> planParticipants = await _unitOfWork.PlanningRepository.GetPlanParticipants(userDetails.tenant_id);
            IEnumerable<tco_plan_type> planTypes = await _unitOfWork.PlanningRepository.GetPlanTemplates(userId, userDetails.tenant_id);
            planTypes.Where(x => x.SortOrder == 0 && !x.IsActive).ToList().ForEach(x => x.SortOrder = 99999);
            planTypes = planTypes.OrderBy(x => x.SortOrder).ToList();
            IEnumerable<tco_progress_status> planStatuses = await _unitOfWork.PlanningRepository.GetStatuses(userDetails.tenant_id, "PLAN");
            IEnumerable<gco_plantype_colors> planTypeColors = await _unitOfWork.PlanningRepository.GetPlanTypeColors();
            var activeUsersData = await _unitOfWork.PlanningRepository.GetActiveUsers(userDetails.tenant_id);
            List<KeyValuePair> planUsersData = GetFormattedUsersData(activeUsersData);

            var gridData = new List<PlanGridHelper>();
            var planDatafromDB = (from a in plans
                                  join b in planParticipants on new { tenantID = a.fk_tenant_id, planID = a.pk_plan_id } equals new { tenantID = b.fk_tenant_id, planID = b.fk_plan_id }
                                  select new PlanGridDataHelper
                                  {
                                      pk_plan_id = a.pk_plan_id,
                                      name = a.name,
                                      fk_status_id = a.fk_status_id.Value,
                                      fk_plan_type_id = a.fk_plan_type_id,
                                      created_by = a.created_by,
                                      is_main_owner = b.is_main_owner.Value,
                                      write_access = b.write_access.Value,
                                      read_access = b.read_access.Value,
                                      fk_user_id = b.fk_user_id,
                                      is_old_plan = a.is_old_plan,
                                      startYear = a.start_year
                                  }).ToList();
            if (await IsPlannedAdminUserAsync(userId))
            {
                gridData = (await GetPlanDashboardAdminRolesAsync(userDetails, plans, planStatuses, planUsersData)).ToList();
            }
            else
            {
                gridData = GetPlanDashboard(userDetails, planStatuses, planUsersData, planDatafromDB, planParticipants).ToList();
            }
            var planTypeswithColorList = (from a in planTypes
                                          join b in planTypeColors on a.ColorId equals b.pk_color_id
                                          select new { PlanTemplateID = a.Id, PlanTemplateName = a.Name, ClassName = b.color_name }).ToList();
            var planTypeList = (from a in gridData
                                join b in planTypeswithColorList on new { pTypeID = a.PlanTemplateId.ToString() } equals new { pTypeID = b.PlanTemplateID.ToString() }
                                select new
                                {
                                    pTypeID = b.PlanTemplateID,
                                    TemplateName = b.PlanTemplateName,
                                    className = b.ClassName,
                                    planID = a.PlanId
                                }).ToList();

            if (!string.IsNullOrEmpty(inputHelper.Filter))
            {
                gridData = gridData.Where(x => x.PlanName.ToLower().Contains(inputHelper.Filter.ToLower())).ToList();
            }
            planTypeswithColorList = planTypeswithColorList.Where(x => gridData.Select(y => y.PlanTemplateId).Contains(x.PlanTemplateID)).ToList();
            var distinctTemplates = planTypeswithColorList.Select(x => new { x.ClassName, x.PlanTemplateName }).Distinct().ToList();
            if (gridData.Any())
            {
                gridData.ForEach(x => { x.IconColor = planTypeList.FirstOrDefault(z => z.planID == x.PlanId) != null ? planTypeList.FirstOrDefault(z => z.planID == x.PlanId).className : string.Empty; });
                gridData = UpdatePlanTypeAndSortOrder(planTypes, gridData);
                if (!inputHelper.IsPlanningStrategy)
                    gridData = ApplyPlanGridFilter(inputHelper, gridData);
            }
            return new PlannningBaseDataHelper
            {
                PlanGridHelper = gridData.OrderBy(z => z.SortOrder).ToList(),
                PlanTemplateHelperColor = distinctTemplates.Select(x => new PlanTemplateColorHelper { ClassName = x.ClassName, PlanTemplateName = x.PlanTemplateName }).ToList(),
                disableCopyPlan = !await IsRoleForCopyPlanAsync(userId),
                disableDeletePlan = !await IsRole1Or15UserAsync(userId),
                PlanStatusList = planStatuses.Select(x => new { Id = x.status_id, Description = x.status_description }).Distinct().Select(x => new PlanStatusHelper { Id = x.Id, Status = x.Description }).ToList()
            };
        }

        private List<PlanGridHelper> ApplyPlanGridFilter(PlansListInputHelper inputHelper, List<PlanGridHelper> gridData)
        {
            if (!string.IsNullOrEmpty(inputHelper.GridFilter.PlanName))
                gridData = gridData.Where(x => x.PlanName.ToLower().Contains(inputHelper.GridFilter.PlanName.ToLower())).ToList();
            if (!string.IsNullOrEmpty(inputHelper.GridFilter.PlanType))
                gridData = gridData.Where(x => x.PlanType.ToLower().Contains(inputHelper.GridFilter.PlanType.ToLower())).ToList();
            if (!string.IsNullOrEmpty(inputHelper.GridFilter.CreatedBy))
                gridData = gridData.Where(x => x.Responsible.ToLower().Contains(inputHelper.GridFilter.CreatedBy.ToLower())).ToList();
            if (!string.IsNullOrEmpty(inputHelper.GridFilter.Status))
                gridData = gridData.Where(x => x.Status.ToLower().Contains(inputHelper.GridFilter.Status.ToLower())).ToList();
            return gridData;
        }

        private static List<PlanGridHelper> UpdatePlanTypeAndSortOrder(IEnumerable<tco_plan_type> planTypes, List<PlanGridHelper> gridData)
        {
            var data = (from a in gridData
                        join b in planTypes on a.PlanTemplateId equals b.Id into x1
                        from b1 in x1.DefaultIfEmpty()
                        select new PlanGridHelper
                        {
                            IconColor = a.IconColor,
                            PlanId = a.PlanId,
                            PlanName = a.PlanName,
                            Structure = a.Structure,
                            Settings = a.Settings,
                            Status = a.Status,
                            Responsible = a.Responsible,
                            PlanTemplateId = a.PlanTemplateId,
                            Role = a.Role,
                            IsOldPlan = a.IsOldPlan,
                            StartYear = a.StartYear,
                            MetaData = a.MetaData,
                            IsSoftDelete = a.IsSoftDelete,
                            SortOrder = b1 != null ? b1.SortOrder : 0,
                            PlanType = b1 != null ? b1.Name : string.Empty,
                            IsActive = b1 != null ? b1.IsActive : true,
                        }).ToList();

            data.Where(x => x.SortOrder == 0).ToList().ForEach(x => x.SortOrder = 9999);
            data.Where(x => x.SortOrder == 0 && !x.IsActive).ToList().ForEach(x => x.SortOrder = 99999);
            return data;
        }

        public async Task<List<PlanGridHelper>> GetCopyPlansDropDown(string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            //to get active plans for role 1,15
            IEnumerable<tpl_plan> plans = await _unitOfWork.PlanningRepository.GetPlans(userDetails.tenant_id, true);
            plans = plans.Where(x => !x.is_old_plan);
            //to check for role access(owner,read or write) for role 24
            IEnumerable<tpl_plan_participants> planParticipants = await _unitOfWork.PlanningRepository.GetPlanParticipants(userDetails.tenant_id);
            List<PlanGridHelper> finalData = new List<PlanGridHelper>();
            if (await IsRole1Or15UserAsync(userId))
            {
                finalData = plans.Select(x => new PlanGridHelper() { PlanId = x.pk_plan_id, PlanName = x.name }).ToList();
            }
            else if (await IsRole24UserAsync(userId))
            {
                finalData = (from a in plans
                             join b in planParticipants on new { tenantID = a.fk_tenant_id, planID = a.pk_plan_id } equals new { tenantID = b.fk_tenant_id, planID = b.fk_plan_id }
                             where (b.is_main_owner.Value || b.write_access.Value || b.read_access.Value) && b.fk_user_id == userDetails.pk_id
                             select new PlanGridHelper
                             {
                                 PlanId = a.pk_plan_id,
                                 PlanName = a.name,
                             }).ToList();
            }
            return finalData;
        }

        public async Task<PlanBaseDataHelper> GetPlansBaseDataonCreate(string userId, bool isEditable = false)
        {
            var planningCommonData = new PlanBaseDataHelper();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Planning");

            var planTypeData = (await _unitOfWork.PlanningRepository.GetPlanTemplates(userId, userDetails.tenant_id)).Where(x => x.IsActive).Select(x => new KeyValuePair { key = x.Id.ToString(), value = x.Name }).ToList();
            var planCategoryData = (await _unitOfWork.PlanningRepository.GetPlanCategories()).Select(x => new KeyValuePair { key = x.pk_plan_category_id.ToString(), value = x.plan_category_name }).ToList();
            var planStatusData = (await _unitOfWork.PlanningRepository.GetStatuses(userDetails.tenant_id, "PLAN")).Select(x => new KeyValuePair { key = x.status_id.ToString(), value = x.status_description }).ToList();
            var activeUsersData = await _unitOfWork.PlanningRepository.GetActiveUsers(userDetails.tenant_id);
            var allPlanStrategyTask = (await _unitOfWork.PlanningRepository.GetAllStrategyTaskforPlan(langStrings, userDetails.tenant_id)).ToList();
            var paramValue = await _utility.GetParameterValueAsync(userId, "PLAN_SHOW_CATEGORY_TYPE_OPTION");
            if (string.IsNullOrEmpty(paramValue))
            {
                paramValue = "false";
            }
            var planCategoryType = paramValue.ToLower() == "true" ? (await _unitOfWork.PlanningRepository.GetplanCategoryType(userDetails.tenant_id)).Select(x => new KeyValuePair { key = x.pk_cat_id.ToString(), value = x.description }).ToList() : new List<KeyValuePair>();

            //orgVersion Data Source
            var orgVersionData = (await _unitOfWork.PlanningRepository.GetOrgVersionDataSource(userDetails.tenant_id)).ToList();
            ClsOrgVersionSpecificContent orgVersionContent = new ClsOrgVersionSpecificContent();
            List<KeyValuePair> orgVersionDS = new List<KeyValuePair>();
            KeyValuePair defaultOrgVersion = new KeyValuePair();
            if (orgVersionData != null)
            {
                defaultOrgVersion = GetDefaultOrgVersion(orgVersionData);
                if (defaultOrgVersion != null)
                {
                    orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, defaultOrgVersion.key);
                    orgVersionDS = orgVersionData.Select(x => new KeyValuePair { key = x.pk_org_version, value = x.version_name }).ToList();
                }
            }
            var planUsersData = (from a in activeUsersData
                                 select new KeyValuePair
                                 {
                                     key = a.Id.ToString(),
                                     value = string.Format("{0} {1}", a.Firstname, a.Lastname)
                                 }).OrderBy(z => z.value).ToList();

            planningCommonData.Plantype = planTypeData;
            planningCommonData.Plancategory = planCategoryData;
            planningCommonData.Planstatus = planStatusData;
            planningCommonData.Planownerusers = planUsersData;
            planningCommonData.Planwriteaccessusers = planUsersData;
            planningCommonData.Planreadaccessusers = planUsersData;
            planningCommonData.PlanStrategyTaskData = allPlanStrategyTask;
            planningCommonData.LoggeduserId = !isEditable ? userDetails.pk_id.ToString() : string.Empty;
            planningCommonData.OrgVersionData = new PlanOrgVersionDataHelper { OrgVersionDataSource = orgVersionDS, DefaultOrgVersion = defaultOrgVersion };
            planningCommonData.OrgId = orgVersionContent.lstOrgDataLevel1.Any() ? orgVersionContent.lstOrgDataLevel1.ElementAt(0).org_id_1 : "-1";
            planningCommonData.OrgLevel = orgVersionContent != null ? 1 : -1;// As of now keep org level as 1
            planningCommonData.PlancategoryType = planCategoryType;
            planningCommonData.ShowCategoryDropdown = Boolean.Parse(paramValue);
            return planningCommonData;
        }

        public async Task<string> SaveUpdatePlan(string userId, PlanDataHelper planDataHelper)
        {
            var userDetails = await _utility.GetUserDetailsAsync(userId);

            //Create Plan
            if (string.IsNullOrEmpty(planDataHelper.planID))
            {
                string planId = await CreatePlan(userDetails, planDataHelper);
                await savePlanConnectedToOrgData(userDetails, planId, planDataHelper.OrgId, planDataHelper.OrgLevel, planDataHelper.orgVersion, planDataHelper.OrgList);
                return planId;
            }
            else
            {
                await savePlanConnectedToOrgData(userDetails, planDataHelper.planID, planDataHelper.OrgId, planDataHelper.OrgLevel, planDataHelper.orgVersion, planDataHelper.OrgList);
                return await ModifyPlan(userDetails, planDataHelper);
            }
        }

        public async Task<PlanDataHelper> GetMyplan(string userId, Guid planID, bool displayNumber, bool isPlanPreview)
        {
            var userDetails = await _utility.GetUserDetailsAsync(userId);
            var plan = await _unitOfWork.PlanningRepository.GetPlanbyID(userDetails.tenant_id, planID);
            var planTypeData = plan.start_year == 0 ? await GetPlanType(userId, plan.fk_plan_type_id) : await GetPlanStrategyType(userId, plan.fk_plan_type_id);
            var isDesignedperLaw = (planTypeData != null && plan.start_year == 0) ? planTypeData.IsDesignedPerLaw : false;
            var planDocs = (await _unitOfWork.PlanningRepository.GetPlanDocuments(planID, userDetails.tenant_id)).ToList();
            var isTemplateAvailable = plan.tpl_plan_template_details.Any(x => x.status.Value);
            await SetPlanPreview(userId, planID, isPlanPreview, plan);
            if (isTemplateAvailable)
            {
                return await GetPlanDataWithTemplate(userId, plan, isDesignedperLaw, planDocs, displayNumber);
            }
            else
            {
                return await GetPlanDataWithoutTemplate(userId, plan, isDesignedperLaw, planDocs);
            }
        }

        private async Task savePlanConnectedToOrgData(UserData userDetails, string planId, string orgId, int orgLevel, string orgVersion, List<OrgDataHelper> orgList)
        {
            Guid planIdOrgConnected = Guid.Parse(planId);
            await _unitOfWork.PlanningRepository.RemovePlanConnectedOrgData(planIdOrgConnected, orgId, orgLevel, userDetails.tenant_id, orgVersion);// remove the data first, then add into the database
            if (orgList.Any())
            {
                orgList = GetSelectedOrgData(orgList);
                foreach (var item in orgList)
                {
                    tpl_plan_distribution tplPlanDistData = new tpl_plan_distribution
                    {
                        fk_plan_id = planIdOrgConnected,
                        fk_tenant_id = userDetails.tenant_id,
                        org_id_created = orgId,
                        org_level_created = orgLevel,
                        org_id = item.orgId,
                        org_level = item.orgLevel,
                        fk_org_version = orgVersion,
                        service_id = string.Empty,
                        updated = DateTime.Now,
                        updated_by = userDetails.pk_id
                    };
                    _unitOfWork.GenericRepo.Add(tplPlanDistData);
                }
                await _unitOfWork.CompleteAsync();
            }
        }

        private List<OrgDataHelper> GetSelectedOrgData(List<OrgDataHelper> orgData)
        {
            List<OrgDataHelper> orgSelectedData = new List<OrgDataHelper>();
            foreach (var item in orgData)
            {
                if (item.isChecked)
                    orgSelectedData.Add(item);
            }
            orgSelectedData = orgSelectedData.GroupBy(x => x.orgId).Select(x => x.FirstOrDefault()).Distinct().ToList();
            return orgSelectedData;
        }

        private async Task SetPlanPreview(string userId, Guid planID, bool isPlanPreview, tpl_plan plan)
        {
            if (isPlanPreview && plan.start_year == 0)
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                PublishTreeType treeType = PublishTreeType.PlanPreview;
                var previewTemplateInfo = await _unitOfWork.PublishRepository.GetPreviewTemplatePerUser(userDetails.tenant_id, planID, userDetails.pk_id);
                int templateId = previewTemplateInfo == null ? 0 : previewTemplateInfo.preview_template_id;
                if (templateId == 0)
                {
                    var previewConfigHelper = new PlanPublishConfigHelper
                    {
                        Name = plan.name,
                        ShortName = plan.short_name,
                        PlanId = planID,
                    };
                    templateId = await CreatePublishConfig(userId, previewConfigHelper, treeType);
                    previewTemplateInfo = new tpl_plan_previewtemplate
                    {
                        fk_plan_id = planID,
                        fk_user_id = userDetails.pk_id,
                        preview_template_id = templateId,
                        fk_tenant_id = userDetails.tenant_id,
                        updated = DateTime.UtcNow,
                        updated_by = userDetails.pk_id,
                    };
                    _unitOfWork.GenericRepo.Add(previewTemplateInfo);
                    await _unitOfWork.CompleteAsync();
                }
                await ValidateBuildVersion(userId, planID, userDetails, templateId);
                if (!previewTemplateInfo.is_publish_file_generated)
                {
                    await RequestCopyPublishFilesAsync(templateId, userId, planID);
                }
            }
        }

        private async Task ValidateBuildVersion(string userId, Guid planID, UserData userDetails, int templateId)
        {
            string buildFileName = "build.json";
            TenantData tenantData = await _utility.GetTenantDataAsync(userId);
            var pubConfig = await _unitOfWork.PublishRepository.GetPublishConfig(userDetails.tenant_id, templateId);
            string baseStoragePath = $"{PlanPublishConstants.planPreviewText}/{tenantData.publish_Id.ToLower()}/{pubConfig.short_name.ToLower()}/{buildFileName}";
            bool isPublishBuildAvailable = await _blobHelper.BlobExistsAsync(StorageAccount.AppStorage, BlobContainers.BuildBm, buildFileName);
            bool isPreviewBuildAvailable = await _blobHelper.BlobExistsAsync(StorageAccount.PublishStage, BlobContainers.PlanPreviewContent, baseStoragePath);
            var previewTemplateInfo = await _unitOfWork.PublishRepository.GetPreviewTemplatePerUser(userDetails.tenant_id, planID, userDetails.pk_id);
            if (isPublishBuildAvailable && isPreviewBuildAvailable)
            {
                var buildBlobData = JsonConvert.DeserializeObject<JObject>(await _blobHelper.GetTextBlobAsync(StorageAccount.AppStorage, BlobContainers.BuildBm, buildFileName));
                var previewBlobData = JsonConvert.DeserializeObject<JObject>(await _blobHelper.GetTextBlobAsync(StorageAccount.PublishStage, BlobContainers.PlanPreviewContent, baseStoragePath));
                if ((string)buildBlobData["version"] != (string)previewBlobData["version"])
                    await ChangeStateForRepublishingFiles(planID, userDetails);
            }
            if (!isPreviewBuildAvailable && previewTemplateInfo.is_publish_file_generated)
                await ChangeStateForRepublishingFiles(planID, userDetails);
        }

        private async Task ChangeStateForRepublishingFiles(Guid planID, UserData userDetails)
        {
            var previewTemplateInfo = await _unitOfWork.PublishRepository.GetPreviewTemplatePerUser(userDetails.tenant_id, planID, userDetails.pk_id);
            previewTemplateInfo.is_publish_file_generated = false;
            previewTemplateInfo.updated = DateTime.UtcNow;
            previewTemplateInfo.updated_by = userDetails.pk_id;
            await _unitOfWork.CompleteAsync();
        }

        public async Task RequestCopyPublishFilesAsync(int templateId, string userId, Guid planId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var queueMessage = new PlanPreviewMessageHelper();
            queueMessage.TemplateId = templateId;
            queueMessage.PlanId = planId.ToString();
            queueMessage.UserID = userId;
            string strQueueMessage = JsonConvert.SerializeObject(queueMessage);
            _backendJob.QueueMessage(userDetails, QueueName.planpreviewrequestqueue, strQueueMessage, 0);
        }

        public async Task<string> GetEditorContent(string userId, Guid planId, Guid nodeId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var plan = await _unitOfWork.PlanningRepository.GetPlanbyID(userDetails.tenant_id, planId);
            tpl_plan_template_details nodeData = plan.tpl_plan_template_details.FirstOrDefault(x => x.fk_plan_id == planId && x.node_id == nodeId);
            nodeData.node_description = await _utility.GetValidDescription(userId, nodeData.node_id, nodeData.node_description, 0, nameof(GetEditorContent), PublishTreeType.PlanModule);
            return nodeData != null ? nodeData.node_description ?? string.Empty : string.Empty;
        }

        public async Task SaveEditorContent(string userId, Guid planId, Guid nodeId, string description)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var plan = await _unitOfWork.PlanningRepository.GetPlanbyID(userDetails.tenant_id, planId);
            tpl_plan_template_details nodeData = plan.tpl_plan_template_details.FirstOrDefault(x => x.fk_plan_id == planId && x.node_id == nodeId);

            if (nodeData != null)
            {
                nodeData.node_description = description;
                nodeData.updated = DateTime.UtcNow;
                nodeData.updated_by = userDetails.pk_id;
                await _unitOfWork.CompleteAsync();
            }
        }

        public async Task<string> GetTextEditorStatus(string userId, Guid planId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var plan = await _unitOfWork.PlanningRepository.GetPlanbyID(userDetails.tenant_id, planId);
            var planParticipants = plan.tpl_plan_participants.Where(x => x.fk_user_id == userDetails.pk_id);
            if (planParticipants.Any(x => x.is_main_owner.Value) || planParticipants.Any(x => x.write_access.Value))
            {
                return PlanRole.WRITE.ToString();
            }
            else if (await IsPlannedAdminUserAsync(userId))
            {
                return PlanRole.ADMIN.ToString();
            }
            else
            {
                var langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Planning");
                return langStrings.FirstOrDefault(x => x.Key == "Planning_Invalid_operation").Value.LangText;
            }
        }

        public async Task SaveNodeDescription(string userId, Guid planId, Guid nodeId, string description, string nodeName, string typeDisplayText)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var plan = await _unitOfWork.PlanningRepository.GetPlanbyID(userDetails.tenant_id, planId);
            tpl_plan_template_details nodeData = plan.tpl_plan_template_details.FirstOrDefault(x => x.fk_plan_id == planId && x.node_id == nodeId);
            if (nodeData != null)
            {
                nodeData.node_planned_description = description;
                nodeData.node_name = nodeName;
                nodeData.node_text = typeDisplayText;
                nodeData.updated = DateTime.UtcNow;
                nodeData.updated_by = userDetails.pk_id;
                await _unitOfWork.CompleteAsync();
            }
        }

        public async Task DisableNode(string userId, Guid planId, Guid nodeId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var plan = await _unitOfWork.PlanningRepository.GetPlanbyID(userDetails.tenant_id, planId);
            tpl_plan_template_details nodeData = plan.tpl_plan_template_details.FirstOrDefault(x => x.fk_plan_id == planId && x.node_id == nodeId);
            if (nodeData != null)
            {
                nodeData.status = false;
                nodeData.node_order = 0;
                nodeData.updated = DateTime.UtcNow;

                List<Guid> childNodesToDisable = new List<Guid>();
                var planTreeList = plan.tpl_plan_template_details;
                GetNodestoDisable(planTreeList.ToList(), nodeId, childNodesToDisable);

                foreach (var item in childNodesToDisable)
                {
                    tpl_plan_template_details childNode = plan.tpl_plan_template_details.FirstOrDefault(x => x.fk_plan_id == planId && x.node_id == item);
                    if (childNode != null)
                    {
                        childNode.status = false;
                        childNode.node_order = 0;
                        childNode.updated = DateTime.UtcNow;
                    }
                }
                await _unitOfWork.CompleteAsync();
            }
        }

        //Below Method updates the level number for Plan Type Tree
        public void UpdateLevelNumber(IEnumerable<PlanTemplateNode> currentLevel, int currentLevelNumber, bool isBlobUpload, string userId)
        {
            int nextLevelNum = currentLevelNumber + 1;
            foreach (var node in currentLevel)
            {
                if (isBlobUpload)
                    node.Description = _utility.DecodeHtmlString(node.Description, userId).GetAwaiter().GetResult();

                node.LevelNumber = currentLevelNumber;
                node.LevelClassName = node.Items != null && node.Items.Any() ? string.Format(levelClasshasChildren, node.LevelNumber + 1) : string.Format(levelClasshasNoChildren, node.LevelNumber + 1);
                if (node.Items != null && node.Items.Any())
                {
                    UpdateLevelNumber(node.Items, nextLevelNum, isBlobUpload, userId);
                }
            }
        }

        public async Task UpdateLevelNumberAsync(IEnumerable<PlanTemplateNode> currentLevel, int currentLevelNumber, bool isBlobUpload, string userId)
        {
            int nextLevelNum = currentLevelNumber + 1;
            foreach (var node in currentLevel)
            {
                if (isBlobUpload)
                {
                    node.Description = await _utility.DecodeHtmlString(node.Description, userId);
                }

                node.LevelNumber = currentLevelNumber;
                node.LevelClassName = node.Items != null && node.Items.Any()
                    ? string.Format(levelClasshasChildren, node.LevelNumber + 1)
                    : string.Format(levelClasshasNoChildren, node.LevelNumber + 1);
                if (node.Items != null && node.Items.Any())
                {
                    await UpdateLevelNumberAsync(node.Items, nextLevelNum, isBlobUpload, userId);
                }
            }
        }

        public async Task<string> SaveUpdatePlanGoalsbyNode(string userId, PlanGoalHelper planGoalData)
        {
            if (planGoalData.MasterGoalId == Guid.Empty)
            {
                return await CreateGoal(userId, planGoalData);
            }
            else
            {
                return await ModifyGoal(userId, planGoalData);
            }
        }

        public async Task<PlanGoalBaseDataHelper> RetrievePlanGoalsBaseData(string userId)
        {
            var data = new PlanGoalBaseDataHelper();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            //Get Master Data Based on Tenant

            var tagsData = await _unitOfWork.PlanningRepository.GetActionTagsData(userDetails.tenant_id);
            var unSustainabilityData = await _unitOfWork.PlanningRepository.GetUNSustainbilityData();

            data.Focusarea = await _unitOfWork.PlanningRepository.GetFocusAreaDataForTenantAndActivePlans(userDetails.tenant_id);
            data.MasterGoals = await _unitOfWork.PlanningRepository.GetMasterGoalsBasedonTenantAndActivePlans(userDetails.tenant_id);
            data.Strategy = await _unitOfWork.PlanningRepository.GetStrategyDataforTenantAndActivePlans(userDetails.tenant_id);

            //96671
            var multiSelectData = _utility.GetParameterValueAndActiveStatus(userId, "IS_MULTI_SELECT_GOALS", out int active);
            if (!(!string.IsNullOrEmpty(multiSelectData) && active == 1 && multiSelectData.ToLower() == "true"))
            {
                data.Strategy = await _unitOfWork.PlanningRepository.GetFilteredStrategyData(userDetails.tenant_id, data.Strategy, string.Empty);
            }

            data.Tags = tagsData.Select(x => new KeyValuePair { key = x.PkId.ToString(), value = x.TagDescription }).ToList();
            data.Unsustainability = unSustainabilityData.Select(x => new KeyValuePair { key = x.pk_goal_id.ToString(), value = x.goal_name }).ToList();
            return data;
        }

        public async Task<PlanMasterGoalDataHelper> GetMasterGoalData(string userId, string masterGoalId, string planId, string nodeId)
        {
            var data = new PlanMasterGoalDataHelper();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            //Get Master Data Based on Tenant
            var tagsData = await _unitOfWork.PlanningRepository.GetActionTagsData(userDetails.tenant_id);
            var unSustainabilityData = await _unitOfWork.PlanningRepository.GetUNSustainbilityData();
            var focusAreaData = await _unitOfWork.PlanningRepository.GetFocusAreaDataForTenantAndActivePlans(userDetails.tenant_id);
            var masterGoalsData = await _unitOfWork.PlanningRepository.GetMasterGoalsBasedonTenantAndActivePlans(userDetails.tenant_id);
            var masterStrategyData = await _unitOfWork.PlanningRepository.GetStrategyDataforTenantAndActivePlans(userDetails.tenant_id);
            var masterPlanName = await _unitOfWork.PlanningRepository.GetMasterPlanforGoal(userDetails.tenant_id, Guid.Parse(masterGoalId));

            //Get Data Specific to goal id , plan id and nodeid
            var goalData = await _unitOfWork.PlanningRepository.GetMasterGoalSingleData(userDetails.tenant_id, Guid.Parse(masterGoalId));
            var goalDataforPlanandNode = await _unitOfWork.PlanningRepository.GetPlanGoalSingleData(userDetails.tenant_id, Guid.Parse(masterGoalId), Guid.Parse(planId), Guid.Parse(nodeId));
            var goalStrategyData = await _unitOfWork.PlanningRepository.GetStrategyDataforGoal(userDetails.tenant_id, Guid.Parse(masterGoalId));

            if (goalData != null)
            {
                data.Focusarea = goalData.fk_focusarea_id.HasValue ? focusAreaData.Where(x => x.key == goalData.fk_focusarea_id.Value.ToString()).Select(x => x.key).ToList() :
                                                                      new List<string>();
                data.MasterGoals = masterGoalsData.Where(x => x.key.ToLower() == goalData.pk_plan_goal_id.ToString()).Select(x => new KeyValuePair { key = x.key.ToString(), value = x.value, isActive = x.isActive }).ToList();
                data.Tags = !string.IsNullOrEmpty(goalData.tags) ? tagsData.Where(x => goalData.tags.Contains(x.PkId.ToString())).Select(x => x.PkId.ToString()).ToList() : new List<string>();

                List<string> sustainibilitygoals = new List<string>();
                if (!string.IsNullOrEmpty(goalData.fk_sustainable_goal_id))
                {
                    foreach (var item in goalData.fk_sustainable_goal_id.Split(','))
                    {
                        if (unSustainabilityData.FirstOrDefault(x => x.pk_goal_id == item) != null)
                        {
                            sustainibilitygoals.Add(item);
                        }
                    }
                }
                var susUnGoals = sustainibilitygoals.Select(s => Convert.ToInt32(s)).OrderBy(x => x).ToList();
                data.Unsustainability = !string.IsNullOrEmpty(goalData.fk_sustainable_goal_id) ? susUnGoals.Select(x => x.ToString()).ToList() : new List<string>();
                data.Description = goalData != null ? goalData.description : string.Empty;
                data.PlanReferenceText = GetPlanReferenceText(goalDataforPlanandNode, masterPlanName);
                if (goalStrategyData.Any() && masterStrategyData.Any(x => goalStrategyData.Select(z => z.fk_plan_strategy_id.ToString()).Contains(x.key)))
                {
                    data.Strategy = masterStrategyData.Where(x => goalStrategyData.Select(z => z.fk_plan_strategy_id.ToString()).Contains(x.key))
                                                              .Select(x => x.key.ToString()).ToList();
                    data.StrategyKeyValue = await _unitOfWork.PlanningRepository.GetFilteredStrategyData(userDetails.tenant_id, masterStrategyData, masterGoalId);
                }
                else
                {
                    data.Strategy = new List<string>();
                    data.StrategyKeyValue = new List<KeyValuePair>();
                }
                if (goalData.fk_masterplan_id != null)
                {
                    data.IsEditable = goalData.fk_masterplan_id.Value == Guid.Empty ? true : goalData.fk_masterplan_id.Value == Guid.Parse(planId);
                }
                return data;
            }
            return data;
        }

        public async Task<IEnumerable<PlanGoalGridHelper>> GetGoalGridDatabyPlan(string userId, Guid planId, Guid nodeId, bool isGridView,
                                                                                List<PlanGridsSearchFilter> searchFilters, PlanSortingDetails sortDetails,
                                                                                bool isWebPublish = false)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            string paramValue = await _utility.GetParameterValueAsync(userId, "TARGET_USE_UNGOALS");

            bool paramVal = paramValue.ToLower() == "true";
            var masterGoalDataAsync = _unitOfWork.PlanningRepository.GetMasterGoalsBasedonTenant(userDetails.tenant_id);
            var masterTargetDataAsync = _unitOfWork.PlanningRepository.GetMasterTargetsBasedonTenant(userDetails.tenant_id);
            var goalsbyPlanNodeIdAsync = _unitOfWork.PlanningRepository.GetGoalsBasedonPlanIdandNodeId(userDetails.tenant_id, planId, nodeId);
            var targetbyPlanNodeIdAsync = _unitOfWork.PlanningRepository.GetTargetsBasedonPlanIdandNodeId(userDetails.tenant_id, planId, nodeId);
            var tagsDataAsync = _unitOfWork.PlanningRepository.GetActionTagsData(userDetails.tenant_id);
            var focusAreaDataAsync = _unitOfWork.PlanningRepository.GetFocusAreaDataForTenant(userDetails.tenant_id);
            var sustainabilityDataAsync = _unitOfWork.PlanningRepository.GetUNSustainbilityData();
            var unSustainabilityDataTargetAsync = _unitOfWork.PlanningRepository.GetUNSustainbilityTargetData();
            var plansbyTenantAsync = _unitOfWork.PlanningRepository.GetPlansTenantSpecific(userDetails.tenant_id);
            var processedGoalsAsync = _unitOfWork.PlanningRepository.GetProcessedGoals(userDetails.tenant_id);
            var processedTargetsAsync = _unitOfWork.PlanningRepository.GetProcessedTargets(userDetails.tenant_id);
            var goalSortInfoAsync = _unitOfWork.PlanningRepository.FetchGoalSortOrder(planId, nodeId, userDetails.tenant_id);
            var targetSortInfoAsync = _unitOfWork.PlanningRepository.FetchTargetSortOrder(planId, nodeId, userDetails.tenant_id);
            var focusAreaSortInfoAsync = _unitOfWork.PlanningRepository.RetrieveFocusAreaSortDetails(planId, userDetails.tenant_id);
            var isPlanApprovedAsync = _unitOfWork.PlanningRepository.IsPlanApproved(userDetails.tenant_id, planId);

            await Task.WhenAll(masterGoalDataAsync, masterTargetDataAsync, goalsbyPlanNodeIdAsync, targetbyPlanNodeIdAsync, tagsDataAsync, focusAreaDataAsync,
                sustainabilityDataAsync, unSustainabilityDataTargetAsync, plansbyTenantAsync, processedGoalsAsync, processedTargetsAsync, goalSortInfoAsync,
                targetSortInfoAsync, focusAreaSortInfoAsync, isPlanApprovedAsync);

            var masterGoalData = masterGoalDataAsync.Result;
            var masterTargetData = masterTargetDataAsync.Result;
            var goalsbyPlanNodeId = goalsbyPlanNodeIdAsync.Result;
            var targetbyPlanNodeId = targetbyPlanNodeIdAsync.Result;
            var tagsData = tagsDataAsync.Result;
            var focusAreaData = focusAreaDataAsync.Result;
            var sustainabilityData = sustainabilityDataAsync.Result;
            var unSustainabilityDataTarget = unSustainabilityDataTargetAsync.Result;
            var plansbyTenant = plansbyTenantAsync.Result;
            var processedGoals = processedGoalsAsync.Result;
            var processedTargets = processedTargetsAsync.Result;
            var goalSortInfo = goalSortInfoAsync.Result;
            var targetSortInfo = targetSortInfoAsync.Result;
            var focusAreaSortInfo = focusAreaSortInfoAsync.Result;
            var isPlanApproved = isPlanApprovedAsync.Result;

            var processedGoalsMaxYear = GetProcessedGoals(processedGoals);
            var processedTargetsMaxYear = GetProcessedTargets(processedTargets);

            var activePlans = plansbyTenant.Where(x => !x.is_soft_delete);
            bool isgoalSortAvailable = (goalSortInfo.ToList().Count == goalSortInfo.Count(x => x.SortOrder != 0));
            bool istargetSortAvailable = (targetSortInfo.ToList().Count == targetSortInfo.Count(x => x.SortOrder != 0));

            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Planning");
            var finstatusText = langStrings.FirstOrDefault(x => x.Key == "PGT_FinStatus_Text").Value.LangText;
            var busplanText = langStrings.FirstOrDefault(x => x.Key == "PGT_Busplan_Text").Value.LangText;
            var notIncludedText = langStrings.FirstOrDefault(x => x.Key == "PGT_FinStatus_NotIncluded").Value.LangText;
            var includedText = langStrings.FirstOrDefault(x => x.Key == "PGT_FinStatus_Included").Value.LangText;
            Dictionary<string, string> ungoal = new Dictionary<string, string>();
            Dictionary<string, string> unTarget = new Dictionary<string, string>();

            var goalsSet = (from a in masterGoalData
                            join b in goalsbyPlanNodeId on a.pk_plan_goal_id equals b.fk_plan_goal_id
                            join e in goalSortInfo on a.pk_plan_goal_id.ToString() equals e.Id.ToLower()
                            join c in plansbyTenant on b.fk_plan_id equals c.pk_plan_id
                            join d in processedGoalsMaxYear on b.fk_plan_goal_id equals d.Id into goalsData
                            from x in goalsData.DefaultIfEmpty()
                            join f in focusAreaSortInfo on a.fk_focusarea_id equals f.fk_focus_area_id into focusAreaSortData
                            from x1 in focusAreaSortData.DefaultIfEmpty()
                            select new PlanGoalDataHelper
                            {
                                NodeId = nodeId,
                                PlanID = planId,
                                GoalId = a.pk_plan_goal_id,
                                GoalName = a.name,
                                GoalDescription = a.description,
                                GoalStatus = GetGoalTargetFinstatusText(x, null, finstatusText, notIncludedText, includedText, true, busplanText, userDetails.tenant_id),
                                GoalStatusDescription = string.Empty,
                                GoalActionTags = GetTags(tagsData, a.tags, GetFormattedMasterPlanID(a.fk_masterplan_id), b.plan_reference_text, plansbyTenant.ToList()),
                                FocusAreaId = !a.fk_focusarea_id.HasValue ? 0 : a.fk_focusarea_id.Value,
                                FocusAreaSortOrder = x1 != null ? x1.sort_order : 0,
                                GoalFocusAreaTag = GetFocusArea(focusAreaData, !a.fk_focusarea_id.HasValue ? 0 : a.fk_focusarea_id.Value, isWebPublish),
                                GoalSustainbilityTag = GetSustainibilityTags(sustainabilityData, a.fk_sustainable_goal_id, ref ungoal, isWebPublish),
                                GoalMasterPlanId = GetFormattedMasterPlanID(a.fk_masterplan_id),
                                GoalPlanStatus = c.fk_status_id == null ? 0 : c.fk_status_id.Value,
                                MasterPlan = GetMasterPlanName(activePlans.ToList(), GetFormattedMasterPlanID(a.fk_masterplan_id)),
                                DisplayGoalStatusImage = x != null,
                                SortOrder = e.SortOrder
                            }).ToList();

            var targetSet = (from a in masterTargetData
                             join b in targetbyPlanNodeId on a.pk_plan_target_id equals b.fk_plan_target_id
                             join e in targetSortInfo on a.pk_plan_target_id.ToString() equals e.Id.ToLower()
                             join c in plansbyTenant on b.fk_plan_id equals c.pk_plan_id
                             join d in processedTargetsMaxYear on b.fk_plan_target_id equals d.Id into targetsData
                             from x in targetsData.DefaultIfEmpty()
                             select new PlanTargetDataHelper
                             {
                                 NodeId = nodeId,
                                 PlanID = planId,
                                 ConnectedGoalId = a.fk_plan_goal_id,
                                 TargetName = a.name,
                                 TargetDescription = a.target_description,
                                 TargetId = a.pk_plan_target_id,
                                 TargetStatus = GetGoalTargetFinstatusText(null, x, finstatusText, notIncludedText, includedText, false, busplanText, userDetails.tenant_id),
                                 TargetStatusDescription = string.Empty,
                                 SortOrder = e.SortOrder,
                                 TargetActionTags = GetTags(tagsData, a.tags, Guid.Empty),
                                 TargetSustainbilityTag = paramVal ? GetSustainibilityTags(sustainabilityData, a.fk_param_sustainable_goal_id, ref unTarget, isWebPublish) : GetTargetSustainibilityTags(unSustainabilityDataTarget, a.fk_sustainable_goal_id, ref unTarget, isWebPublish),
                                 TargetPlanStatus = c.fk_status_id == null ? 0 : c.fk_status_id.Value,
                                 TargetMasterPlanId = GetFormattedMasterPlanID(a.fk_masterplan_id),
                                 DisplayTargetStatusImage = x != null
                             }).ToList();

            var resultSet = GetDataBasedonDataSource(goalsSet, targetSet, searchFilters, sortDetails,
                                            new PlanGoalTargetDataFormatHelper { PlanId = planId, UserId = userId, IsGoalSortingAvailable = isgoalSortAvailable, IsTargetSortingAvailable = istargetSortAvailable },
                                            ungoal, unTarget, isWebPublish);

            if (!searchFilters.Any() && string.IsNullOrEmpty(sortDetails.SortKey) && string.IsNullOrEmpty(sortDetails.SortOrder) && isGridView)
            {
                int clientId = userDetails.client_id;
                string cacheKey = $"{PlanPublishConstants.planPreviewGoalTargetCache}-{planId}-{nodeId}-{userDetails.tenant_id}";
                string cacheData = await _cache.GetStringForUserAsync(clientId, userDetails.tenant_id, userId, cacheKey);
                if (!string.IsNullOrEmpty(cacheData)) await _cache.DeleteStringForUserAsync(clientId, userDetails.tenant_id, userId, cacheKey);

                var newgoalsSet = (from a in masterGoalData
                                   join b in goalsbyPlanNodeId on a.pk_plan_goal_id equals b.fk_plan_goal_id
                                   join e in goalSortInfo on a.pk_plan_goal_id.ToString() equals e.Id.ToLower()
                                   join c in plansbyTenant on b.fk_plan_id equals c.pk_plan_id
                                   join d in processedGoalsMaxYear on b.fk_plan_goal_id equals d.Id into goalsData
                                   from x in goalsData.DefaultIfEmpty()
                                   join f in focusAreaSortInfo on a.fk_focusarea_id equals f.fk_focus_area_id into focusAreaSortData
                                   from x1 in focusAreaSortData.DefaultIfEmpty()
                                   select new PlanGoalDataHelper
                                   {
                                       NodeId = nodeId,
                                       PlanID = planId,
                                       GoalId = a.pk_plan_goal_id,
                                       GoalName = a.name,
                                       GoalDescription = a.description,
                                       GoalStatus = GetGoalTargetFinstatusText(x, null, finstatusText, notIncludedText, includedText, true, busplanText, userDetails.tenant_id),
                                       GoalStatusDescription = string.Empty,
                                       GoalActionTags = GetTags(tagsData, a.tags, GetFormattedMasterPlanID(a.fk_masterplan_id), b.plan_reference_text, plansbyTenant.ToList()),
                                       FocusAreaId = !a.fk_focusarea_id.HasValue ? 0 : a.fk_focusarea_id.Value,
                                       FocusAreaSortOrder = x1 != null ? x1.sort_order : 0,
                                       GoalFocusAreaTag = GetFocusArea(focusAreaData, !a.fk_focusarea_id.HasValue ? 0 : a.fk_focusarea_id.Value, true),
                                       GoalSustainbilityTag = GetSustainibilityTags(sustainabilityData, a.fk_sustainable_goal_id, ref ungoal, true),
                                       GoalMasterPlanId = GetFormattedMasterPlanID(a.fk_masterplan_id),
                                       GoalPlanStatus = c.fk_status_id == null ? 0 : c.fk_status_id.Value,
                                       MasterPlan = GetMasterPlanName(activePlans.ToList(), GetFormattedMasterPlanID(a.fk_masterplan_id)),
                                       DisplayGoalStatusImage = x != null,
                                       SortOrder = e.SortOrder
                                   }).ToList();

                var newresultSet = GetDataBasedonDataSource(newgoalsSet, targetSet, searchFilters, sortDetails,
                                           new PlanGoalTargetDataFormatHelper { PlanId = planId, UserId = userId, IsGoalSortingAvailable = isgoalSortAvailable, IsTargetSortingAvailable = istargetSortAvailable },
                                           ungoal, unTarget, true);

                await _cache.SetStringForUserAsync(clientId, userDetails.tenant_id, userId, cacheKey, JsonConvert.SerializeObject(newresultSet), cacheTimeOut);
            }

            if (isPlanApproved)
            {
                resultSet.ForEach(x =>
                {
                    x.DisplayGoalDeleteIcon = false;
                    x.DisplayTargetDeleteIcon = false;
                });
            }
            return resultSet;
        }

        private List<BudgetProposalPlanMappingHelper> GetProcessedTargets(IEnumerable<tpl_tfp_target_mapping> processedTargets)
        {
            var processedUniqueTargets = (from a in processedTargets
                                          group a by new
                                          {
                                              a.fk_plan_target_id,
                                              a.fk_tenant_id,
                                          } into grp
                                          select new
                                          {
                                              Id = grp.Key.fk_plan_target_id,
                                              TenantId = grp.Key.fk_tenant_id,
                                              BudgetYear = grp.Max(x => x.budget_year),
                                          }).ToList();

            var processedTargetsMaxYear = (from a in processedUniqueTargets
                                           select new BudgetProposalPlanMappingHelper
                                           {
                                               Id = a.Id,
                                               TenantId = a.TenantId,
                                               BudgetYear = a.BudgetYear,
                                               TargetId = processedTargets.First(x => x.fk_plan_target_id == a.Id && x.fk_tenant_id == a.TenantId && x.budget_year == a.BudgetYear).fk_target_id
                                           }).ToList();

            processedTargetsMaxYear = (from a in processedTargetsMaxYear
                                       join b in processedTargets
                                       on new { a = a.Id, b = a.BudgetYear, c = a.TenantId } equals new { a = b.fk_plan_target_id, b = b.budget_year, c = b.fk_tenant_id }
                                       select new
                                       {
                                           Id = a.Id,
                                           TenantId = a.TenantId,
                                           BudgetYear = a.BudgetYear,
                                           TargetId = a.TargetId
                                       }).Distinct().Select(a => new BudgetProposalPlanMappingHelper
                                       {
                                           Id = a.Id,
                                           TenantId = a.TenantId,
                                           BudgetYear = a.BudgetYear,
                                           TargetId = a.TargetId
                                       }).ToList();
            processedTargetsMaxYear.ForEach(x => x.IsTransferredtoFinplan = processedTargets.Any(z => z.fk_plan_target_id == x.Id && z.is_transferred_tofinplan));
            return processedTargetsMaxYear;
        }

        private List<BudgetProposalPlanMappingHelper> GetProcessedGoals(IEnumerable<tpl_tfp_goal_mapping> processedGoals)
        {
            var processedUniqueGoals = (from a in processedGoals
                                        group a by new
                                        {
                                            a.fk_plan_goal_id,
                                            a.fk_tenant_id,
                                        } into grp
                                        select new
                                        {
                                            Id = grp.Key.fk_plan_goal_id,
                                            TenantId = grp.Key.fk_tenant_id,
                                            BudgetYear = grp.Max(x => x.budget_year),
                                        }).ToList();

            var processedGoalsMaxYear = (from a in processedUniqueGoals
                                         select new BudgetProposalPlanMappingHelper
                                         {
                                             Id = a.Id,
                                             TenantId = a.TenantId,
                                             BudgetYear = a.BudgetYear,
                                             GoalId = processedGoals.First(x => x.fk_plan_goal_id == a.Id && x.fk_tenant_id == a.TenantId && x.budget_year == a.BudgetYear).fk_goal_id
                                         }).ToList();

            processedGoalsMaxYear = (from a in processedGoalsMaxYear
                                     join b in processedGoals
                                     on new { a = a.Id, b = a.BudgetYear, c = a.TenantId } equals new { a = b.fk_plan_goal_id, b = b.budget_year, c = b.fk_tenant_id }
                                     select new
                                     {
                                         Id = a.Id,
                                         TenantId = a.TenantId,
                                         BudgetYear = a.BudgetYear,
                                         GoalId = a.GoalId
                                     }).Distinct().Select(a => new BudgetProposalPlanMappingHelper
                                     {
                                         Id = a.Id,
                                         TenantId = a.TenantId,
                                         BudgetYear = a.BudgetYear,
                                         GoalId = a.GoalId
                                     }).ToList();

            processedGoalsMaxYear.ForEach(x => x.IsTransferredtoFinplan = processedGoals.Any(z => z.fk_plan_goal_id == x.Id && z.is_transferred_tofinplan));
            return processedGoalsMaxYear;
        }

        public async Task<IEnumerable<PlanModuleColumnSelector>> GetGridColumnSelector(string userId, Guid planId, Guid nodeId, PlanningGridColumnSelector type)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Planning");
            string paramValue = await _utility.GetParameterValueAsync(userId, "TARGET_USE_UNGOALS");
            bool paramVal = paramValue.ToLower() == "true";
            var columnSelectorList = new List<PlanModuleColumnSelector>();
            var availableOptions = new List<string> { "Plan_GoalGrid_FocusArea",
                                                      "Plan_GoalGrid_Goal",
                                                      "Plan_GoalGrid_GoalStatus",
                                                      "Plan_GoalGrid_unGoal",
                                                      "Plan_GoalGrid_GoalTag",
                                                      "Plan_GoalGrid_Target",
                                                      "Plan_GoalGrid_TargetStatus",
                                                      "Plan_GoalGrid_unTarget",
                                                      "Plan_GoalGrid_TargetTag"
                                                    };
            var old_unTarget_columnName = "Plan_GoalGrid_unTarget";
            var new_unTarget_columnName = "Plan_Target_unGoal";
            if (paramVal)
            {
                var indexOld = availableOptions.IndexOf(old_unTarget_columnName);
                if (indexOld > 0)
                {
                    availableOptions[indexOld] = new_unTarget_columnName;
                }
            }
            tpl_column_selector columnSelector = await _unitOfWork.PlanningRepository.GetBlobPathBasedonPlanIdandNodeIdAndType(userDetails.tenant_id, planId, nodeId, type, 0);
            if (columnSelector == null)
            {
                int index = 1;
                foreach (var item in availableOptions)
                {
                    columnSelectorList.Add(new PlanModuleColumnSelector
                    {
                        ColumnId = item,
                        ColumnName = langStrings.FirstOrDefault(x => x.Key.Equals(item)).Value.LangText,
                        IsChecked = true,
                        Order = index
                    });
                    index++;
                }
            }
            else
            {
                string goalGridCS = await _blobHelper.GetTextBlobAsync(StorageAccount.AppStorage, BlobContainers.Planning, columnSelector.path);
                var datailsAvailabeinBlob = JsonConvert.DeserializeObject<List<PlanModuleColumnSelector>>(goalGridCS);
                foreach (var item in datailsAvailabeinBlob)
                {
                    columnSelectorList.Add(new PlanModuleColumnSelector
                    {
                        ColumnId = item.ColumnId,
                        ColumnName = item.ColumnName,
                        IsChecked = item.IsChecked,
                        Order = item.Order
                    });
                }

                var nonAvailableList = availableOptions.Select(x => x).Except(columnSelectorList.Select(x => x.ColumnId));
                if (nonAvailableList.Any())
                {
                    int index = columnSelectorList.LastOrDefault().Order + 1;
                    foreach (var item in nonAvailableList)
                    {
                        columnSelectorList.Add(new PlanModuleColumnSelector
                        {
                            ColumnId = item,
                            ColumnName = langStrings.FirstOrDefault(x => x.Key.Equals(item)).Value.LangText,
                            IsChecked = true,
                            Order = index
                        });
                        index++;
                    }
                }
            }
            return columnSelectorList;
        }

        public async Task SavePlanningColumnSelector(string userId, Guid planId, Guid nodeId, List<PlanModuleColumnSelector> planningModuleColumns, PlanningGridColumnSelector type)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            //foreach (var item in planningModuleColumns)
            //{
            //    if(item.ColumnId == "PM_PlanAction_UnTargetsColumn") { item.ColumnName = langStrings.FirstOrDefault(x => x.Key.Equals("PM_PlanAction_UnTargetsColumn")).Value.LangText; }
            //}
            tpl_column_selector columnSelector = await _unitOfWork.PlanningRepository.GetBlobPathBasedonPlanIdandNodeIdAndType(userDetails.tenant_id, planId, nodeId, type, 0);
            if (columnSelector == null)
            {
                //upload blob
                string blobPath = $"{userDetails.tenant_id}/{type.ToString()}/{planId}/{nodeId}";
                await _blobHelper.UploadTextBlobAsync(StorageAccount.AppStorage, BlobContainers.Planning, blobPath, JsonConvert.SerializeObject(planningModuleColumns));

                //add column selector details
                _unitOfWork.GenericRepo.Add(new tpl_column_selector
                {
                    fk_tenant_id = userDetails.tenant_id,
                    fk_plan_id = planId,
                    fk_node_Id = nodeId,
                    type = type.ToString(),
                    updated = DateTime.UtcNow,
                    updated_by = userDetails.pk_id,
                    path = blobPath
                });
                await _unitOfWork.CompleteAsync();
            }
            else
            {
                await _blobHelper.UploadTextBlobAsync(StorageAccount.AppStorage, BlobContainers.Planning, columnSelector.path, JsonConvert.SerializeObject(planningModuleColumns));
            }
        }

        public async Task<PlanBaseTargetDataHelper> GetBaseTargetData(string userId, string masterGoalId, string planId, string nodeId)
        {
            var baseObject = new PlanBaseTargetDataHelper();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var tagsData = await _unitOfWork.PlanningRepository.GetActionTagsData(userDetails.tenant_id);
            var unSustainabilityData = await _unitOfWork.PlanningRepository.GetUNSustainbilityData();
            var unSustainabilityDataTarget = await _unitOfWork.PlanningRepository.GetUNSustainbilityTargetData();
            var focusAreaData = await _unitOfWork.PlanningRepository.GetFocusAreaDataForTenantAndActivePlans(userDetails.tenant_id);
            var masterGoalsData = await _unitOfWork.PlanningRepository.GetMasterGoalsBasedonTenantAndActivePlans(userDetails.tenant_id);
            var goalData = await _unitOfWork.PlanningRepository.GetMasterGoalSingleData(userDetails.tenant_id, Guid.Parse(masterGoalId));
            var mastertargetData = (await _unitOfWork.PlanningRepository.GetMasterTargetsBasedonTenantAndActivePlan(userDetails.tenant_id)).Where(x => x.goalId.ToString() == masterGoalId).ToList();
            var goalDataforPlanandNode = await _unitOfWork.PlanningRepository.GetPlanGoalSingleData(userDetails.tenant_id, Guid.Parse(masterGoalId), Guid.Parse(planId), Guid.Parse(nodeId));

            //strategy Data
            var masterStrategyData = await _unitOfWork.PlanningRepository.GetStrategyDataforTenantAndActivePlans(userDetails.tenant_id);
            var goalStrategyData = await _unitOfWork.PlanningRepository.GetStrategyDataforGoal(userDetails.tenant_id, Guid.Parse(masterGoalId));
            List<KeyValuePair> sustainibilitygoals = new List<KeyValuePair>();
            if (goalData != null)
            {
                var goaldataObject = new PlanGoalBaseDataHelperExtended();
                goaldataObject.MasterGoals = masterGoalsData.Where(x => x.key.ToLower() == goalData.pk_plan_goal_id.ToString()).Select(x => new KeyValuePair { key = x.key.ToString(), value = x.value, isActive = x.isActive }).ToList();

                goaldataObject.Tags = !string.IsNullOrEmpty(goalData.tags) ? tagsData.Where(x => goalData.tags.Contains(x.PkId.ToString())).Select(x => new KeyValuePair { key = x.PkId.ToString(), value = x.TagDescription }).ToList()
                                                                            : new List<KeyValuePair>();

                goaldataObject.Focusarea = goalData.fk_focusarea_id.HasValue ? focusAreaData.Where(x => x.key == goalData.fk_focusarea_id.Value.ToString()).Select(x => new KeyValuePair { key = x.key.ToString(), value = x.value, isActive = x.isActive }).ToList() :
                                                                              new List<KeyValuePair>();

                if (!string.IsNullOrEmpty(goalData.fk_sustainable_goal_id))
                {
                    foreach (var item in goalData.fk_sustainable_goal_id.Split(','))
                    {
                        if (unSustainabilityData.FirstOrDefault(x => x.pk_goal_id == item) != null)
                        {
                            sustainibilitygoals.Add(new KeyValuePair { key = item, value = unSustainabilityData.FirstOrDefault(x => x.pk_goal_id == item).goal_name });
                        }
                    }
                }

                goaldataObject.Unsustainability = !string.IsNullOrEmpty(goalData.fk_sustainable_goal_id) ? sustainibilitygoals : new List<KeyValuePair>();
                goaldataObject.Description = goalDataforPlanandNode != null ? goalDataforPlanandNode.plan_reference_text : string.Empty;
                if (goalStrategyData.Any() && masterStrategyData.Any(x => goalStrategyData.Select(z => z.fk_plan_strategy_id.ToString()).Contains(x.key)))
                {
                    goaldataObject.Strategy = masterStrategyData.Where(x => goalStrategyData.Select(z => z.fk_plan_strategy_id.ToString()).Contains(x.key))
                                                              .Select(x => new KeyValuePair { key = x.key.ToString(), value = x.value, isActive = x.isActive }).ToList();
                }
                else
                {
                    goaldataObject.Strategy = new List<KeyValuePair>();
                }
                baseObject.GoalData = goaldataObject;
            }

            var targetdataObject = new PlanTargetBaseDataHelper();
            foreach (var item in mastertargetData)
            {
                item.value = item.value + " - " + item.planName;
            }
            targetdataObject.MasterTargets = mastertargetData;
            targetdataObject.Tags = tagsData.Select(x => new KeyValuePair { key = x.PkId.ToString(), value = x.TagDescription }).ToList();
            string paramValue = await _utility.GetParameterValueAsync(userId, "TARGET_USE_UNGOALS");
            if (paramValue.ToLower() == "true")
            {
                targetdataObject.Unsustainability = !string.IsNullOrEmpty(goalData.fk_sustainable_goal_id) ? sustainibilitygoals : unSustainabilityData.Select(x => new KeyValuePair { key = x.pk_goal_id.ToString(), value = x.goal_name }).ToList(); ;
            }
            else
            {
                targetdataObject.Unsustainability = goalData == null ? unSustainabilityDataTarget.Select(x => new KeyValuePair { key = x.pk_target_id.ToString(), value = x.target_name }).ToList()
                                                                 : unSustainabilityDataTarget.Where(z => goalData.fk_sustainable_goal_id.Split(',').ToList().Contains(z.fk_goal_id)).
                                                                            Select(x => new KeyValuePair { key = x.pk_target_id.ToString(), value = x.target_name }).ToList();
            }
            targetdataObject.SortOrder = 0;
            targetdataObject.Description = string.Empty;
            baseObject.TargetData = targetdataObject;

            return baseObject;
        }

        public async Task<PlanMasterTargetDataObject> GetTargetDataonEdit(string userId, string masterGoalId, string targetId, string planId, string nodeId)
        {
            var baseObject = new PlanMasterTargetDataObject();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            string paramValue = await _utility.GetParameterValueAsync(userId, "TARGET_USE_UNGOALS");
            bool paramVal = paramValue.ToLower() == "true";
            var tagsData = await _unitOfWork.PlanningRepository.GetActionTagsData(userDetails.tenant_id);
            var unSustainabilityData = await _unitOfWork.PlanningRepository.GetUNSustainbilityData();
            var focusAreaData = await _unitOfWork.PlanningRepository.GetFocusAreaDataForTenant(userDetails.tenant_id);
            var masterGoalsData = await _unitOfWork.PlanningRepository.GetMasterGoalsBasedonTenant(userDetails.tenant_id);
            var goalData = await _unitOfWork.PlanningRepository.GetMasterGoalSingleData(userDetails.tenant_id, Guid.Parse(masterGoalId));
            var mastertargetData = await _unitOfWork.PlanningRepository.GetMasterTargetsBasedonMasterGoalID(userDetails.tenant_id, Guid.Parse(masterGoalId));
            var goalDataforPlanandNode = await _unitOfWork.PlanningRepository.GetPlanGoalSingleData(userDetails.tenant_id, Guid.Parse(masterGoalId), Guid.Parse(planId), Guid.Parse(nodeId));
            var unSustainabilityDataTarget = await _unitOfWork.PlanningRepository.GetUNSustainbilityTargetData();

            //strategy Data
            var masterStrategyData = await _unitOfWork.PlanningRepository.GetStrategyDataforTenant(userDetails.tenant_id);
            var goalStrategyData = await _unitOfWork.PlanningRepository.GetStrategyDataforGoal(userDetails.tenant_id, Guid.Parse(masterGoalId));
            if (goalData != null)
            {
                var goaldataObject = new PlanMasterGoalDataHelper();
                goaldataObject.MasterGoals = masterGoalsData.Where(x => x.pk_plan_goal_id == goalData.pk_plan_goal_id).Select(x => new KeyValuePair { key = x.pk_plan_goal_id.ToString(), value = x.name }).ToList();

                goaldataObject.Tags = !string.IsNullOrEmpty(goalData.tags) ? tagsData.Where(x => goalData.tags.Contains(x.PkId.ToString())).Select(x => x.PkId.ToString()).ToList()
                                                                            : new List<string>();
                goaldataObject.Focusarea = goalData.fk_focusarea_id.HasValue ? focusAreaData.Where(x => x.pk_focus_area_id == goalData.fk_focusarea_id.Value).Select(x => x.pk_focus_area_id.ToString()).ToList() :
                                                                                new List<string>();

                List<string> sustainibilitygoals = new List<string>();
                if (!string.IsNullOrEmpty(goalData.fk_sustainable_goal_id))
                {
                    foreach (var item in goalData.fk_sustainable_goal_id.Split(','))
                    {
                        if (unSustainabilityData.FirstOrDefault(x => x.pk_goal_id == item) != null)
                        {
                            sustainibilitygoals.Add(item);
                        }
                    }
                }

                goaldataObject.Unsustainability = !string.IsNullOrEmpty(goalData.fk_sustainable_goal_id) ? sustainibilitygoals : new List<string>();

                goaldataObject.Description = goalDataforPlanandNode != null ? goalDataforPlanandNode.plan_reference_text : string.Empty;
                if (goalStrategyData.Any() && masterStrategyData.Any(x => goalStrategyData.Select(z => z.fk_plan_strategy_id).Contains(x.pk_plan_stategy_id)))
                {
                    goaldataObject.Strategy = masterStrategyData.Where(x => goalStrategyData.Select(z => z.fk_plan_strategy_id).Contains(x.pk_plan_stategy_id))
                                                              .Select(x => x.pk_plan_stategy_id.ToString()).ToList();
                }
                else
                {
                    goaldataObject.Strategy = new List<string>();
                }
                baseObject.GoalData = goaldataObject;
            }

            var targetData = goalData != null ? await _unitOfWork.PlanningRepository.GetMasterTargetSingleData(userDetails.tenant_id, goalData.pk_plan_goal_id, Guid.Parse(targetId)) : null;
            if (targetData != null)
            {
                var targetdataObject = new PlanMasterTargetDataHelper();
                targetdataObject.MasterTargets = mastertargetData.Where(x => x.pk_plan_target_id == targetData.pk_plan_target_id).Select(x => new KeyValuePair { key = x.pk_plan_target_id.ToString(), value = x.name }).ToList();
                targetdataObject.Tags = !string.IsNullOrEmpty(targetData.tags) ? tagsData.Where(x => targetData.tags.Contains(x.PkId.ToString())).Select(x => x.PkId.ToString()).ToList() : new List<string>();
                if (paramVal)
                {
                    targetdataObject.Unsustainability = !string.IsNullOrEmpty(targetData.fk_param_sustainable_goal_id) ?
                                                    unSustainabilityData.Where(z => targetData.fk_param_sustainable_goal_id.Split(',').ToList().Contains(z.pk_goal_id)).Select(x => x.pk_goal_id.ToString()).ToList()
                                                    : new List<string>();
                }
                else
                {
                    targetdataObject.Unsustainability = !string.IsNullOrEmpty(targetData.fk_sustainable_goal_id) ?
                                                    unSustainabilityDataTarget.Where(z => targetData.fk_sustainable_goal_id.Split(',').ToList().Contains(z.pk_target_id)).Select(x => x.pk_target_id.ToString()).ToList()
                                                    : new List<string>();
                }
                targetdataObject.Description = targetData.target_description;
                if (targetData.fk_masterplan_id != null)
                {
                    targetdataObject.IsEditable = targetData.fk_masterplan_id.Value == Guid.Empty ? true : targetData.fk_masterplan_id.Value == Guid.Parse(planId);
                }
                baseObject.TargetData = targetdataObject;
            }
            return baseObject;
        }

        public async Task<string> SaveUpdatePlanTargetsbyNode(string userId, PlanTargetHelper planTargetData)
        {
            if (planTargetData.MasterTargetId == Guid.Empty)
            {
                return await CreateTarget(userId, planTargetData);
            }
            else
            {
                return await ModifyTarget(userId, planTargetData);
            }
        }

        public async Task DeletePlanGoalsTarget(string userId, PlanGoalGridDeleteHelper gridDeleteHelper)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            if (gridDeleteHelper.GoalId != Guid.Empty && gridDeleteHelper.TargetId != Guid.Empty)
            {
                var targetData = await _unitOfWork.PlanningRepository.GetSingleTargetForPlanIdNodeIdTargetId(userDetails.tenant_id, gridDeleteHelper.PlanId, gridDeleteHelper.NodeId, gridDeleteHelper.TargetId);
                if (targetData != null)
                {
                    _unitOfWork.GenericRepo.Delete(targetData);
                    await _unitOfWork.CompleteAsync();
                }

                // Update fk_masterplan_id to Empty Guid for Target ID
                var masterTargetData = await _unitOfWork.PlanningRepository.GetTargetsbasedonTargetId(userDetails.tenant_id, gridDeleteHelper.TargetId);
                if (masterTargetData != null && masterTargetData.fk_masterplan_id != null && masterTargetData.fk_masterplan_id.Value == gridDeleteHelper.PlanId)
                {
                    masterTargetData.fk_masterplan_id = Guid.Empty;
                    masterTargetData.updated = DateTime.UtcNow;
                    masterTargetData.updated_by = userDetails.pk_id;
                    await _unitOfWork.CompleteAsync();
                }
            }
            else
            {
                var goalData = await _unitOfWork.PlanningRepository.GetPlanGoalSingleData(userDetails.tenant_id, gridDeleteHelper.GoalId, gridDeleteHelper.PlanId, gridDeleteHelper.NodeId);
                if (goalData != null)
                {
                    _unitOfWork.GenericRepo.Delete(goalData);
                    await _unitOfWork.CompleteAsync();
                }

                // Update fk_masterplan_id to Empty Guid for Goal ID
                var masterGoalData = await _unitOfWork.PlanningRepository.GetMasterGoalSingleData(userDetails.tenant_id, gridDeleteHelper.GoalId);
                if (masterGoalData != null && masterGoalData.fk_masterplan_id != null && masterGoalData.fk_masterplan_id.Value == gridDeleteHelper.PlanId)
                {
                    masterGoalData.fk_masterplan_id = Guid.Empty;
                    masterGoalData.updated = DateTime.UtcNow;
                    masterGoalData.updated_by = userDetails.pk_id;
                    await _unitOfWork.CompleteAsync();
                }

                // Update fk_masterplan_id to Empty Guid for Targets linked to GoalID
                var mastertargetData = (await _unitOfWork.PlanningRepository.GetMasterTargetsBasedonMasterGoalID(userDetails.tenant_id, gridDeleteHelper.GoalId)).ToList();
                foreach (var item in mastertargetData)
                {
                    if (item.fk_masterplan_id != null && item.fk_masterplan_id.Value == gridDeleteHelper.PlanId)
                    {
                        item.fk_masterplan_id = Guid.Empty;
                        item.updated = DateTime.UtcNow;
                        item.updated_by = userDetails.pk_id;
                        await _unitOfWork.CompleteAsync();
                    }
                }
            }
        }

        public async Task<int> AddPlanFocusArea(string userId, string name, string description, int focusAreaId, Guid planId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            //Update
            if (focusAreaId != 0)
            {
                tpl_focusarea focusAreaData = await _unitOfWork.PlanningRepository.GetFocusAreaById(userDetails.tenant_id, focusAreaId);
                focusAreaData.name = name;
                focusAreaData.description = description;
                focusAreaData.updated_by = userDetails.pk_id;
                focusAreaData.updated = DateTime.UtcNow;
                if (focusAreaData.fk_masterplan_id == Guid.Empty)
                {
                    focusAreaData.fk_masterplan_id = planId;
                }
                await _unitOfWork.CompleteAsync();
                return focusAreaId;
            }
            else //New FocusArea
            {
                var focusArea = new tpl_focusarea
                {
                    name = name,
                    description = description,
                    fk_tenant_id = userDetails.tenant_id,
                    created_by = userDetails.pk_id,
                    created_date = DateTime.UtcNow,
                    updated_by = userDetails.pk_id,
                    updated = DateTime.UtcNow,
                    fk_masterplan_id = planId
                };
                _unitOfWork.GenericRepo.Add(focusArea);
                await _unitOfWork.CompleteAsync();
                return focusArea.pk_focus_area_id;
            }
        }

        public async Task<IEnumerable<PlanFocusAreaHelper>> GetPlanFocusAreaData(string userId, Guid planId, Dictionary<string, clsLanguageString> langStrings)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var planFocusArea = (await _unitOfWork.PlanningRepository.GetPlanFocusArea(userDetails.tenant_id, planId)).ToList();
            var distinctFocusArea = planFocusArea.Select(x => new { x.FocusAreaId, x.FocusAreaName, x.FocusAreaDesc, x.IsApproved, x.IsMasterPlan }).DistinctBy(x => x.FocusAreaId);
            List<PlanFocusAreaHelper> data = new List<PlanFocusAreaHelper>();
            foreach (var item in distinctFocusArea)
            {
                var focusAreaData = new PlanFocusAreaHelper
                {
                    UniqueId = Guid.NewGuid().ToString(),
                    FocusAreaId = item.FocusAreaId,
                    FocusAreaName = item.FocusAreaName,
                    FocusAreaDescription = item.FocusAreaDesc,
                    IsEditable = GetEditabilityStatus(item.IsApproved, item.IsMasterPlan),
                    Description = GetFocusAreaDescription(item.IsApproved, item.IsMasterPlan, langStrings),
                    Title = GetFocusAreaTitle(item.IsApproved, item.IsMasterPlan, langStrings)
                };
                focusAreaData.GridData = planFocusArea.Where(x => x.FocusAreaId == item.FocusAreaId).Select(z => new PlanStrategyPopUpHelper
                {
                    ID = z.PlanId.ToString(),
                    PlanName = z.PlanName,
                    PlanTypeName = z.PlanTemplate,
                    PlanStatusId = z.PlanStatusId,
                    PlanStatus = z.PlanStatus,
                    NodeName = z.NodeName
                }).ToList();
                data.Add(focusAreaData);
            }
            return data;
        }

        public async Task<IEnumerable<PlanModuleColumnSelector>> GetAssignmentColumnSelector(string userId, Guid planId, Guid nodeId, PlanningGridColumnSelector type)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Planning");
            var columnSelectorList = new List<PlanModuleColumnSelector>();
            var availableOptions = new List<string> { "Plan_Assignment_process_nodeName",
                                                      "Plan_Assignment_process_goalName",
                                                      "Plan_Assignment_process_targetName" ,
                                                      "Plan_Assignment_process_motherAssignmentName"
                                                    };
            tpl_column_selector columnSelector = await _unitOfWork.PlanningRepository.GetBlobPathBasedonPlanIdandNodeIdAndType(userDetails.tenant_id, planId, nodeId, type, 0);
            if (columnSelector == null)
            {
                int index = 1;
                foreach (var item in availableOptions)
                {
                    columnSelectorList.Add(new PlanModuleColumnSelector
                    {
                        ColumnId = item,
                        ColumnName = langStrings.FirstOrDefault(x => x.Key.Equals(item)).Value.LangText,
                        IsChecked = (item == "Plan_Assignment_process_nodeName" || item == "Plan_Assignment_process_goalName"),
                        Order = index
                    });
                    index++;
                }
            }
            else
            {
                string goalGridCS = await _blobHelper.GetTextBlobAsync(StorageAccount.AppStorage, BlobContainers.Planning, columnSelector.path);
                var datailsAvailabeinBlob = JsonConvert.DeserializeObject<List<PlanModuleColumnSelector>>(goalGridCS);
                foreach (var item in datailsAvailabeinBlob)
                {
                    columnSelectorList.Add(new PlanModuleColumnSelector
                    {
                        ColumnId = item.ColumnId,
                        ColumnName = item.ColumnName,
                        IsChecked = item.IsChecked,
                        Order = item.Order
                    });
                }

                var nonAvailableList = availableOptions.Select(x => x).Except(columnSelectorList.Select(x => x.ColumnId));
                if (nonAvailableList.Any())
                {
                    int index = columnSelectorList.LastOrDefault().Order + 1;
                    foreach (var item in nonAvailableList)
                    {
                        columnSelectorList.Add(new PlanModuleColumnSelector
                        {
                            ColumnId = item,
                            ColumnName = langStrings.FirstOrDefault(x => x.Key.Equals(item)).Value.LangText,
                            IsChecked = true,
                            Order = index
                        });
                        index++;
                    }
                }
            }
            return columnSelectorList;
        }

        private bool GetEditabilityStatus(bool isApproved, bool isMasterPlan)
        {
            if (isApproved || !isMasterPlan) return false;
            return true;
        }

        private string GetFocusAreaDescription(bool isApproved, bool isMasterPlan, Dictionary<string, clsLanguageString> langStrings)
        {
            if (isApproved)
            {
                return langStrings.FirstOrDefault(x => x.Key.Equals("PFA_description_FocusArea_Approved")).Value.LangText;
            }
            else if (!isMasterPlan)
            {
                return langStrings.FirstOrDefault(x => x.Key.Equals("PFA_description_FocusArea_MasterPlan")).Value.LangText;
            }
            else
            {
                return langStrings.FirstOrDefault(x => x.Key.Equals("PFA_description_FocusAreaEdit")).Value.LangText;
            }
        }

        private string GetFocusAreaTitle(bool isApproved, bool isMasterPlan, Dictionary<string, clsLanguageString> langStrings)
        {
            if (isApproved || !isMasterPlan)
            {
                return langStrings.FirstOrDefault(x => x.Key.Equals("PFA_NoChangeTitle_FocusArea")).Value.LangText;
            }
            else
            {
                return langStrings.FirstOrDefault(x => x.Key.Equals("PFA_ChangeTitle_FocusArea")).Value.LangText;
            }
        }

        public async Task<IEnumerable<PlanGoalTargetPopUpHelper>> GetReadOnlyPopUpData(string userId, string type, string id)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            if (type.ToLower() == "goal".ToLower())
            {
                var goalID = Guid.Parse(id);
                return (await _unitOfWork.PlanningRepository.GetGoalLinkedtoPlanData(userDetails.tenant_id, goalID)).ToList();
            }
            else
            {
                var targetID = Guid.Parse(id);
                return (await _unitOfWork.PlanningRepository.GetTargetLinkedtoPlanData(userDetails.tenant_id, targetID)).ToList();
            }
        }

        public async Task<PlanGoalTargetUsagePopUp> GetFinplanUsageData(string userId, string type, string id)
        {
            var finplanData = new PlanGoalTargetUsagePopUp();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Planning");
            var finstatusText = langStrings.FirstOrDefault(x => x.Key == "PGT_FinStatus_Text").Value.LangText;
            var notIncludedText = langStrings.FirstOrDefault(x => x.Key == "PGT_FinStatus_NotIncluded").Value.LangText;
            var includedText = langStrings.FirstOrDefault(x => x.Key == "PGT_FinStatus_Included").Value.LangText;
            if (type.ToLower() == "goal".ToLower())
            {
                var goalID = Guid.Parse(id);

                finplanData.PlanGoalTargetPopUpHelper = (await _unitOfWork.PlanningRepository.GetGoalLinkedtoPlanData(userDetails.tenant_id, goalID)).ToList();
                var allGoalData = await _unitOfWork.PlanningRepository.GetProcessedGoalsbyPlanId(userDetails.tenant_id, goalID);
                List<PlanGoalTargetFinplanUsageHelper> finPlanUsage = new List<PlanGoalTargetFinplanUsageHelper>();
                foreach (var item in allGoalData)
                {
                    StringBuilder sb = new StringBuilder();
                    var goalDistributionData = await _unitOfWork.PlanningRepository.GetGoalDistributionData(userDetails.tenant_id, item.fk_goal_id);

                    if (goalDistributionData != null)
                    {
                        var targetText = goalDistributionData.target_blob_id != Guid.Empty ? (await _utility.GetDataFromAzureAsync(userId, goalDistributionData.target_blob_id.ToString(), "WADTenantData")).ToString() : string.Empty;
                        var focustext = goalDistributionData.focus_blob_id != Guid.Empty ? (await _utility.GetDataFromAzureAsync(userId, goalDistributionData.focus_blob_id.ToString(), "WADTenantData")).ToString() : string.Empty;
                        if (!string.IsNullOrEmpty(targetText.Trim()))
                        {
                            sb.Append($"<b>{langStrings.FirstOrDefault(x => x.Key.Equals("Plan_UsagePop_TargetText")).Value.LangText}</b><br>");
                            sb.Append($"{targetText}<br>");
                        }
                        if (!string.IsNullOrEmpty(focustext.Trim()))
                        {
                            sb.Append($"<b>{langStrings.FirstOrDefault(x => x.Key.Equals("Plan_UsagePop_FocusText")).Value.LangText}</b><br>");
                            sb.Append($"{focustext}<br>");
                        }
                        var finplanUsageHelper = new PlanGoalTargetFinplanUsageHelper
                        {
                            ID = Guid.NewGuid().ToString(),
                            //FinplanName = goalMappingData == null ? string.Empty : $"{finstatusText} {goalMappingData.budget_year} - {goalMappingData.budget_year + 3}",
                            FinplanName = $"{finstatusText} {item.budget_year} - {item.budget_year + 3}",
                            Comments = sb.ToString(),
                            Status = item.is_transferred_tofinplan ? includedText : notIncludedText
                        };
                        finPlanUsage.Add(finplanUsageHelper);
                    }
                }
                finplanData.FinplanUsageHelper = finPlanUsage;
                return finplanData;
            }
            else
            {
                var targetID = Guid.Parse(id);
                finplanData.PlanGoalTargetPopUpHelper = (await _unitOfWork.PlanningRepository.GetTargetLinkedtoPlanData(userDetails.tenant_id, targetID)).ToList();
                var targetMappingData = await _unitOfWork.PlanningRepository.GetProcessedTargetsbyPlanId(userDetails.tenant_id, targetID);
                List<PlanGoalTargetFinplanUsageHelper> finPlanUsage = new List<PlanGoalTargetFinplanUsageHelper>();

                foreach (var item in targetMappingData)
                {
                    var targetDistributionData = await _unitOfWork.PlanningRepository.GetTargetDistributionData(userDetails.tenant_id, item.fk_target_id);
                    if (targetDistributionData != null)
                    {
                        var finplanUsageHelper = new PlanGoalTargetFinplanUsageHelper
                        {
                            ID = Guid.NewGuid().ToString(),
                            FinplanName = $"{finstatusText} {item.budget_year} - {item.budget_year + 3}",
                            Comments = targetDistributionData.plan_target_text,
                            Status = item.is_transferred_tofinplan ? includedText : notIncludedText
                        };
                        finPlanUsage.Add(finplanUsageHelper);
                    }
                }
                finplanData.FinplanUsageHelper = finPlanUsage;
                return finplanData;
            }
        }

        public async Task<int> GetApprovedStatusId(int tenantId)
        {
            IEnumerable<tco_progress_status> planStatuses = await _unitOfWork.PlanningRepository.GetStatuses(tenantId, "PLAN");
            if (planStatuses.Any())
            {
                var approvedStatus = planStatuses.FirstOrDefault(x => x.finished_flag);
                return approvedStatus != null ? approvedStatus.status_id : 0;
            }
            return 0;
        }

        public async Task<string> GetAbstractText(string userId, Guid planId, Guid nodeId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            tpl_plan_texts nodeData = await _unitOfWork.PlanningRepository.GetAbstractTextAsync(userDetails.tenant_id, planId, nodeId);
            return nodeData != null ? nodeData.abstract_text : string.Empty;
        }

        public async Task<Guid> SaveAbstractText(string userId, Guid planId, Guid nodeId, string abstractText)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            tpl_plan planData = await _unitOfWork.PlanningRepository.GetPlanbyID(userDetails.tenant_id, planId);
            tpl_plan_texts nodeData = await _unitOfWork.PlanningRepository.GetAbstractTextAsync(userDetails.tenant_id, planId, nodeId);
            var isTextNode = (planData.tpl_plan_template_details.FirstOrDefault(x => x.node_id == nodeId && x.node_type == ContentNodeType.Text.ToString())) != null;
            if (isTextNode)
            {
                //update
                if (nodeData != null)
                {
                    nodeData.abstract_text = abstractText;
                    nodeData.updated = DateTime.UtcNow;
                    nodeData.updated_by = userDetails.pk_id;
                }
                else // new record
                {
                    tpl_plan_texts newPlanText = new tpl_plan_texts
                    {
                        fk_tenant_id = userDetails.tenant_id,
                        fk_plan_id = planId,
                        node_id = nodeId,
                        abstract_text = abstractText,
                        updated = DateTime.UtcNow,
                        updated_by = userDetails.pk_id
                    };
                    _unitOfWork.GenericRepo.Add(newPlanText);
                }
                await _unitOfWork.CompleteAsync();
            }
            return nodeId;
        }

        public async Task<string> GetMainDocumentURL(string userId, Guid planID)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var planDoc_name = (await _unitOfWork.PlanningRepository.GetPlanDocuments(planID, userDetails.tenant_id)).FirstOrDefault(x => x.is_main_doc).doc_name;
            string blobPath = $"{userDetails.tenant_id}/{PlanPublishConstants.planText}/{planID}/{planDoc_name}";
            return blobPath;
        }

        public async Task<string> GetPlanDocumentName(string userId, Guid planID)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var planDocInfo = (await _unitOfWork.PlanningRepository.GetPlanDocuments(planID, userDetails.tenant_id)).FirstOrDefault(x => x.is_main_doc);
            return planDocInfo != null ? planDocInfo.doc_name : string.Empty;
        }

        public async Task DeletePlanMainDocument(string userId, Guid planID)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var planDoc_name = (await _unitOfWork.PlanningRepository.GetPlanDocuments(planID, userDetails.tenant_id)).FirstOrDefault(x => x.is_main_doc).doc_name;
            string blobPath = $"{userDetails.tenant_id}/{PlanPublishConstants.planText}/{planID}/{planDoc_name}";
            await _blobHelper.DeleteBlobAsync(StorageAccount.AppStorage, BlobContainers.Planning, blobPath);
            await _unitOfWork.PlanningRepository.DeleteMainDocument(planID, userDetails.tenant_id);
            await _unitOfWork.CompleteAsync();
        }

        public async Task<string> AddorGetTags(string userid, List<KeyValuePair> tags)
        {
            List<string> tagsIDs = new List<string>();
            foreach (var item in tags)
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userid);
                var tagData = await _unitOfWork.PlanningRepository.GetActionTagSingleData(userDetails.tenant_id, int.Parse(item.key), item.value);
                if (tagData != null)
                {
                    tagsIDs.Add(tagData.PkId.ToString());
                }
                else
                {
                    TcoActionTags objTags = new TcoActionTags()
                    {
                        FkTenantId = userDetails.tenant_id,
                        TagDescription = item.value,
                        UpdatedBy = userDetails.pk_id,
                        Updated = DateTime.UtcNow
                    };
                    _unitOfWork.GenericRepo.Add(objTags);
                    await _unitOfWork.CompleteAsync();
                    tagsIDs.Add(objTags.PkId.ToString());
                }
            }
            return string.Join(",", tagsIDs);
        }

        public async Task<int> CreatePublishConfig(string userId, PlanPublishConfigHelper publishConfigData, PublishTreeType treeType)
        {
            // CREATE TEMPLATE IN TCO_PUBLISH_TEMPLATE
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TcoPublishTemplate newTemplate = new TcoPublishTemplate
            {
                FkTenantId = userDetails.tenant_id,
                BudgetYear = 0,
                Name = publishConfigData.Name,
                TreeType = treeType.ToString(),
                TemplateUrl = string.Empty,
                IsDefault = false,
                Updated = DateTime.UtcNow,
                UpdateBy = userDetails.pk_id,
                Param1 = string.Empty,
                IsGlobal = false,
                ForecastPeriod = 0
            };
            _unitOfWork.GenericRepo.Add(newTemplate);
            await _unitOfWork.CompleteAsync();

            // UPDATE TEMPLATE URL IN TCO_PUBLISH_TEMPLATE
            int templateId = newTemplate.PkId;
            string blobName = $"{userDetails.tenant_id.ToString().Trim()}/{templateId}.txt";
            newTemplate.TemplateUrl = blobName;

            // CREATE CONFIG IN TCO_PUBLISH_CONFIG
            Guid urlSuffix = Guid.NewGuid();
            string newShortName = $"{publishConfigData.PlanId}-{templateId}";
            tco_publish_config publishConfig = new tco_publish_config()
            {
                fk_template_id = templateId,
                stg_publish_url = await _utility.GeneratepublishURLAsync(userId, newShortName, 0, treeType.ToString(), 0, Guid.Empty),
                full_doc_tittle_1 = string.IsNullOrEmpty(publishConfigData.Title1) ? string.Empty : publishConfigData.Title1,
                full_doc_tittle_2 = string.IsNullOrEmpty(publishConfigData.Title2) ? string.Empty : publishConfigData.Title2,
                full_doc_tittle_3 = string.IsNullOrEmpty(publishConfigData.Title3) ? string.Empty : publishConfigData.Title3,
                short_name = newShortName.Replace(" ", "_").Replace(".", "").Replace("/", "_"),
                tree_type = treeType.ToString(),
                budget_year = 0,
                updated = DateTime.UtcNow,
                updated_by = userDetails.pk_id,
                fk_tenant_id = userDetails.tenant_id,
                prod_publish_url = string.Empty,
                url_suffix = urlSuffix,
                org_Id = string.Empty,
                org_name = string.Empty,
                org_level = 0,
                analytics_tracking_code = string.Empty,
                hotjar_tracking_code = string.Empty,
                url_access_controldetails_json = string.Empty,
                web_accessibility_url = string.Empty
            };
            _unitOfWork.GenericRepo.Add(publishConfig);
            await _unitOfWork.CompleteAsync();

            //Update TemplateID back in Plan
            tpl_plan planData = await _unitOfWork.PlanningRepository.GetPlanbyID(userDetails.tenant_id, publishConfigData.PlanId);
            if (planData != null && treeType == PublishTreeType.PlanModule)
            {
                planData.publish_template_id = templateId;
                await _unitOfWork.CompleteAsync();
            }
            return templateId;
        }

        public async Task<JObject> GetHierarchyForPlanDistribution(string userid, string orgId, int orgIdlevelNo, string planId, string orgVersion)
        {
            int orgLevel1 = 1;
            dynamic data = new JObject();

            UserData userDetails = await _utility.GetUserDetailsAsync(userid);

            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userid, orgVersion);

            List<tco_org_level> lstOrgLevels = orgVersionContent.lstOrgLevel.Where(x => x.org_level > 1).OrderBy(y => y.org_level).ToList();

            dynamic lowerOrgLevelInfo = new JArray();

            foreach (var l in lstOrgLevels)
            {
                dynamic levelInfo = new JObject();
                levelInfo.key = l.org_level;
                levelInfo.value = l.level_name;
                lowerOrgLevelInfo.Add(levelInfo);
            }

            data.Add("lowerOrgLevelInfo", lowerOrgLevelInfo);

            if (!string.IsNullOrEmpty(planId))
            {
                Guid id = Guid.Parse(planId);
                tpl_plan_distribution tplPlanData = await _unitOfWork.PlanningRepository.GetHierarchyForPlanDistribution(id, userDetails.tenant_id, orgVersionContent.lstOrgDataLevel1.ElementAt(0).org_id_1, orgLevel1, orgVersion);
                if (tplPlanData != null)
                {
                    var orgList = await _unitOfWork.PlanningRepository.GetHierarchyForPlanDistributuionData(id, userDetails.tenant_id, orgId, orgIdlevelNo, orgVersion);
                    JArray orgData = await _utility.GetOrgStructureforBusinessPlanAsync(orgVersionContent, userid, orgVersionContent.lstOrgDataLevel1.ElementAt(0).org_id_1, orgLevel1, orgList, false, true);
                    bool isCitySelected = orgList.Any(x => x.OrgId == orgId);
                    if (orgData.Any())
                    {
                        orgData[0]["checked"] = isCitySelected;
                        orgData[0]["items"][0]["checked"] = isCitySelected;
                    }
                    data.Add("orgStructurePlan", orgData);
                    return data;
                }
                else
                {
                    JArray orgData = await _utility.GetOrgStructureforBusinessPlanAsync(orgVersionContent, userid, orgVersionContent.lstOrgDataLevel1.ElementAt(0).org_id_1, orgLevel1, null, false, true);
                    if (orgData.Any())
                    {
                        orgData[0]["checked"] = false;
                        orgData[0]["items"][0]["checked"] = false;
                    }
                    data.Add("orgStructurePlan", orgData);
                    return data;
                }
            }
            else
            {
                JArray orgData = await _utility.GetOrgStructureforBusinessPlanAsync(orgVersionContent, userid, orgVersionContent.lstOrgDataLevel1.ElementAt(0).org_id_1, orgLevel1, null, false, true);
                if (orgData.Any())
                {
                    orgData[0]["checked"] = false;
                    orgData[0]["items"][0]["checked"] = false;
                }
                data.Add("orgStructurePlan", orgData);
                return data;
            }
        }

        public async Task<PlanGoalTargetSortData> GetGoalTargetSortOrder(string userId, string planId, string nodeId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var sortInfo = new PlanGoalTargetSortData();
            sortInfo.GoalsSortData = (await _unitOfWork.PlanningRepository.FetchGoalSortOrder(Guid.Parse(planId), Guid.Parse(nodeId), userDetails.tenant_id)).ToList();
            sortInfo.TargetsSortData = (await _unitOfWork.PlanningRepository.FetchTargetSortOrder(Guid.Parse(planId), Guid.Parse(nodeId), userDetails.tenant_id)).ToList();
            return sortInfo;
        }

        public async Task<List<PlanObjectSortHelper>> GetFocusAreaSortOrder(string userId, string planId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var sortInfo = (await _unitOfWork.PlanningRepository.FetchFocusAreaSortOrder(Guid.Parse(planId), userDetails.tenant_id)).ToList();
            return sortInfo;
        }

        public async Task<List<PlanObjectSortHelper>> GetStrategySortOrder(string userId, string planId, string nodeId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var sortInfo = (await _unitOfWork.PlanningRepository.FetchStrategySortOrder(Guid.Parse(planId), Guid.Parse(nodeId), userDetails.tenant_id)).ToList();
            return sortInfo;
        }

        public async Task<PlanGoalTargetSortData> GetGoalTargetStrategySortOrder(string userId, string planId, string nodeId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var sortInfo = new PlanGoalTargetSortData();
            sortInfo.GoalsSortData = (await _unitOfWork.PlanningRepository.FetchGoalStrategySortOrder(Guid.Parse(planId), Guid.Parse(nodeId), userDetails.tenant_id)).ToList();
            sortInfo.TargetsSortData = (await _unitOfWork.PlanningRepository.FetchTargetStrategySortOrder(Guid.Parse(planId), Guid.Parse(nodeId), userDetails.tenant_id)).ToList();
            return sortInfo;
        }

        public async Task SavePlanMetaData(PlanMetaDataHelper planMetaData, string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            await savePlanMetaDataContactInformation(planMetaData, userDetails);
            await savePlanMetaDataRelevantLinks(planMetaData, userDetails);
        }

        public async Task<PlanMetaDataHelper> GetPlanMetaData(Guid planId, string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            PlanMetaDataHelper planMetaData = new PlanMetaDataHelper();
            tpl_metadata_setup metadataSetupInfo = await _unitOfWork.PlanningRepository.GetPlanMetadataSetupInfo(userDetails.tenant_id);
            if (metadataSetupInfo != null)
            {
                IEnumerable<tpl_metadata_setup_texts> planMetadataSetupText = await _unitOfWork.PlanningRepository.GetPlanMetadataSetupTexts(userDetails.tenant_id, metadataSetupInfo.pk_metadata_id);
                planMetaData = GetPlanMetadataAccessInfo(metadataSetupInfo, planMetaData);
                planMetaData = await GetPlanMetaDataContactInfo(planMetaData, planId, userDetails.tenant_id);
                planMetaData = await GetPlanMetaDataDescriptionTexts(planMetaData, planMetadataSetupText, planId, userDetails);
                planMetaData = await GetPlanMetaDataRelevantLinks(planMetaData, planId, userDetails.tenant_id);
            }
            return planMetaData;
        }

        public async Task<PlanMetaDataHelper> GetPlanMetaDataContactInfo(PlanMetaDataHelper planMetaData, Guid planId, int tenantId)
        {
            if (planMetaData.IsContactInformationAccessible)
            {
                tpl_metadata_contact_information metadataContactInfo = await _unitOfWork.PlanningRepository.GetPlanMetadataContactInformation(planId, tenantId);
                if (metadataContactInfo != null)
                {
                    planMetaData.UnitResponsible = metadataContactInfo.person_responsible;
                    planMetaData.Email = metadataContactInfo.epost;
                    planMetaData.Phoneno = metadataContactInfo.telephone;
                }
            }
            return planMetaData;
        }

        public async Task<PlanMetaDataHelper> GetPlanMetaDataRelevantLinks(PlanMetaDataHelper planMetaData, Guid planId, int tenantId)
        {
            if (planMetaData.isLinksAccessible)
            {
                IEnumerable<tpl_metadata_relevant_links> planMetadataRelevantLinks = await _unitOfWork.PlanningRepository.GetPlanMetadataRelevantLinks(planId, tenantId);
                if (planMetadataRelevantLinks.Any())
                {
                    foreach (var item in planMetadataRelevantLinks.OrderBy(x => x.sort_order))
                    {
                        planMetaData.ExternalLinks.Add(new PlanMetaDataExternalLinks
                        {
                            UniqueId = item.sort_order,
                            Protocol = item.protocol,
                            Url = item.url,
                            Title = item.title,
                            sortOrder = item.sort_order
                        });
                    }
                }
            }
            return planMetaData;
        }

        public async Task<PlanMetaDataHelper> GetPlanMetaDataDescriptionTexts(PlanMetaDataHelper planMetaData, IEnumerable<tpl_metadata_setup_texts> planMetadataSetupText, Guid planId, UserData userDetails)
        {
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Planning");
            foreach (var item in planMetadataSetupText)
            {
                if (item.show_flag)
                {
                    tpl_metadata_texts descriptionTextEditor = await _unitOfWork.PlanningRepository.GetPlanMetadataDescription(planId, userDetails.tenant_id, item.pk_plan_metadata_text_id);
                    if (descriptionTextEditor != null)
                    {
                        planMetaData.Description.Add(new PlanMetadataText
                        {
                            PlanId = planId,
                            Title = item.title,
                            InfoText = item.guidance_text,
                            DescriptionData = descriptionTextEditor.description,
                            DescriptionId = descriptionTextEditor.history_id,
                            FkPlanMetaDataTextId = item.pk_plan_metadata_text_id,
                            InfoTextHeader = langStrings.FirstOrDefault(x => x.Key.Equals("Plan_Metadata_Desc_Header")).Value.LangText
                        });
                    }
                    else
                    {
                        planMetaData.Description.Add(new PlanMetadataText
                        {
                            PlanId = planId,
                            Title = item.title,
                            InfoText = item.guidance_text,
                            DescriptionData = string.Empty,
                            DescriptionId = Guid.NewGuid(),
                            FkPlanMetaDataTextId = item.pk_plan_metadata_text_id,
                            InfoTextHeader = langStrings.FirstOrDefault(x => x.Key.Equals("Plan_Metadata_Desc_Header")).Value.LangText
                        });
                    }
                }
            }
            return planMetaData;
        }

        public async Task savePlanMetadataDescriptionTexts(PlanMetadataText descTextInfo, string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            tpl_metadata_texts metadataInfo = await _unitOfWork.PlanningRepository.GetPlanMetadataDescription(descTextInfo.PlanId, userDetails.tenant_id, descTextInfo.FkPlanMetaDataTextId);
            await CreateAndUpdateMetadataDescriptionTextAsync(metadataInfo, descTextInfo, descTextInfo.PlanId, userId);
        }

        public async Task<bool> SaveUpdateGoalTargetSortOrder(string userId, Guid planId, Guid nodeId, bool isGoal, IEnumerable<PlanObjectSortHelper> sortOrderData)
        {
            try
            {
                int count = 1;
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                sortOrderData.ToList().ForEach(x => x.SortOrder = count++);
                if (isGoal)
                {
                    var goalSortData = await _unitOfWork.PlanningRepository.RetrieveGoalSortDetails(planId, nodeId, userDetails.tenant_id);

                    // Remove Existing Data
                    foreach (var item in goalSortData)
                        _unitOfWork.GenericRepo.Delete(item);

                    //Add Goal Sorting Data
                    foreach (var item in sortOrderData)
                    {
                        _unitOfWork.GenericRepo.Add(new tpl_goal_sort
                        {
                            fk_plan_id = planId,
                            fk_node_id = nodeId,
                            fk_plan_goal_id = Guid.Parse(item.Id),
                            sort_order = item.SortOrder,
                            fk_tenant_id = userDetails.tenant_id,
                            updated = DateTime.UtcNow,
                            updated_by = userDetails.pk_id
                        });
                    }
                    await _unitOfWork.CompleteAsync();
                    return true;
                }
                else
                {
                    var targetSortData = await _unitOfWork.PlanningRepository.RetrieveTargetSortDetails(planId, nodeId, userDetails.tenant_id);

                    // Remove Existing Data
                    foreach (var item in targetSortData)
                        _unitOfWork.GenericRepo.Delete(item);

                    //Add Target Sorting Data
                    foreach (var item in sortOrderData)
                    {
                        _unitOfWork.GenericRepo.Add(new tpl_target_sort
                        {
                            fk_plan_id = planId,
                            fk_node_id = nodeId,
                            fk_plan_target_id = Guid.Parse(item.Id),
                            sort_order = item.SortOrder,
                            fk_tenant_id = userDetails.tenant_id,
                            updated = DateTime.UtcNow,
                            updated_by = userDetails.pk_id
                        });
                    }
                    await _unitOfWork.CompleteAsync();
                    return true;
                }
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<bool> SaveUpdateFocusAreaSortOrder(string userId, Guid planId, IEnumerable<PlanObjectSortHelper> sortOrderData)
        {
            int count = 1;
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            sortOrderData.ToList().ForEach(x => x.SortOrder = count++);
            var goalSortData = await _unitOfWork.PlanningRepository.RetrieveFocusAreaSortDetails(planId, userDetails.tenant_id);

            // Remove Existing Data
            foreach (var item in goalSortData)
                _unitOfWork.GenericRepo.Delete(item);

            //Add Goal Sorting Data
            foreach (var item in sortOrderData)
            {
                _unitOfWork.GenericRepo.Add(new tpl_focusarea_sort
                {
                    fk_plan_id = planId,
                    fk_focus_area_id = int.Parse(item.Id),
                    sort_order = item.SortOrder,
                    fk_tenant_id = userDetails.tenant_id,
                    updated = DateTime.UtcNow,
                    updated_by = userDetails.pk_id
                });
            }
            await _unitOfWork.CompleteAsync();
            return true;
        }

        public async Task<bool> SaveUpdateStrategySortOrder(string userId, Guid planId, Guid nodeId, IEnumerable<PlanObjectSortHelper> sortOrderData)
        {
            int count = 1;
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            sortOrderData.ToList().ForEach(x => x.SortOrder = count++);
            var strategySortData = await _unitOfWork.PlanningRepository.RetrieveStrategySortDetails(planId, nodeId, userDetails.tenant_id);

            // Remove Existing Data
            foreach (var item in strategySortData)
                _unitOfWork.GenericRepo.Delete(item);

            //Add Goal Sorting Data
            foreach (var item in sortOrderData)
            {
                _unitOfWork.GenericRepo.Add(new tpl_strategy_sort
                {
                    fk_plan_id = planId,
                    fk_node_id = nodeId,
                    fk_plan_strategy_id = int.Parse(item.Id),
                    sort_order = item.SortOrder,
                    fk_tenant_id = userDetails.tenant_id,
                    updated = DateTime.UtcNow,
                    updated_by = userDetails.pk_id
                });
            }
            await _unitOfWork.CompleteAsync();
            return true;
        }

        public async Task<PlanMetaDataHelper> GetPlanMetadataSetupConfig(string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            PlanMetaDataHelper data = new PlanMetaDataHelper();
            tpl_metadata_setup metadataSetupInf = await _unitOfWork.PlanningRepository.GetPlanMetadataSetupInfo(userDetails.tenant_id);
            if (metadataSetupInf != null)
            {
                data.IsContactInformationAccessible = metadataSetupInf.contact_flag;
                data.IsAttachmentsAccessible = metadataSetupInf.attachment_flag;
                data.isLinksAccessible = metadataSetupInf.links_flag;
                IEnumerable<tpl_metadata_setup_texts> textData = await _unitOfWork.PlanningRepository.GetPlanMetadataSetupTexts(userDetails.tenant_id, metadataSetupInf.pk_metadata_id);
                if (textData.Any())
                {
                    foreach (var txtd in textData)
                    {
                        PlanMetadataSetupTextHelper textSetupData = new PlanMetadataSetupTextHelper
                        {
                            PkMetadataIdText = txtd.pk_plan_metadata_text_id,
                            FkMetadataIdText = txtd.fk_metadata_id,
                            Title = txtd.title,
                            GuidanceText = txtd.guidance_text,
                            ShowFlag = txtd.show_flag
                        };
                        data.MetadataSetupTexts.Add(textSetupData);
                    }
                }
            }
            else
            {
                Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Planning");
                data.IsContactInformationAccessible = false;
                data.IsAttachmentsAccessible = false;
                data.isLinksAccessible = false;
                PlanMetadataSetupTextHelper metaDataTextSetup = new PlanMetadataSetupTextHelper
                {
                    PkMetadataIdText = Guid.NewGuid(),
                    Title = langStrings.FirstOrDefault(x => x.Key.Equals("plan_metadata_setup_plan_information")).Value.LangText,
                    ShowFlag = false
                };
                data.MetadataSetupTexts.Add(metaDataTextSetup);
            }
            return data;
        }

        public async Task<string> SavePlanMetaDataSetupConfigs(string userId, PlanMetaDataHelper metaDataInput)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            await SavePlanningMetaDataConfig(userDetails.pk_id, userDetails.tenant_id, metaDataInput);
            return clsConstants.OperationStatus.Success.ToString();
        }

        public async Task<bool> SoftDeleteSelectedPlan(string userId, Guid planId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            tpl_plan planDetails = await _unitOfWork.PlanningRepository.GetPlanbyID(userDetails.tenant_id, planId);
            if (planDetails != null)
            {
                planDetails.is_soft_delete = true;
                await _unitOfWork.CompleteAsync();
            }
            return true;
        }

        public async Task<bool> RevokeSelectedPlans(string userId, List<Guid> planIds)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var planDetails = await _unitOfWork.PlanningRepository.GetAllPlans(userDetails.tenant_id, planIds);
            if (planDetails.ToList().Count > 0)
            {
                planDetails.ToList().ForEach(x =>
                {
                    x.is_soft_delete = false;
                });
                await _unitOfWork.CompleteAsync();
            }
            return true;
        }

        public async Task<List<PlanSoftDeletedPopupHelper>> GetSoftDeletedPlans(string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var activeUsersData = await _unitOfWork.PlanningRepository.GetActiveUsers(userDetails.tenant_id);
            List<KeyValuePair> planUsersData = GetFormattedUsersData(activeUsersData);
            return await _unitOfWork.PlanningRepository.GetSoftDeleteSelectedPlan(userDetails.tenant_id, planUsersData);
        }

        public async Task<List<PlanTemplateSoftDeletePopupHelper>> GetSoftDeletedPlanChapters(string userId, Guid planId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var activeUsersData = await _unitOfWork.PlanningRepository.GetActiveUsers(userDetails.tenant_id);
            List<KeyValuePair> planUsersData = GetFormattedUsersData(activeUsersData);
            return await _unitOfWork.PlanningRepository.GetSoftDeletedPlanChapter(userDetails.tenant_id, planUsersData, planId);
        }

        public async Task<bool> RevokeSelectedPlanChapters(string userId, List<Guid> nodeIds)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Guid planId = nodeIds[nodeIds.Count - 1];
            nodeIds.Remove(planId);
            var planChapters = await _unitOfWork.PlanningRepository.GetAllPlanChapters(userDetails.tenant_id, planId);
            var planDetails = planChapters.Where(x => nodeIds.Contains(x.node_id)).ToList();
            if (planDetails.Count > 0)
            {
                foreach (var item in planDetails)
                {
                    List<Guid> parentNodeIds = new List<Guid>();
                    List<Guid> NodeIdList = await GetPlanParentNodes(item.parent_node_id, parentNodeIds, planChapters.ToList());
                    var parentNodes = planChapters.Where(x => NodeIdList.Contains(x.node_id)).ToList();
                    if (parentNodes.Count > 0)
                    {
                        parentNodes.ForEach(x =>
                        {
                            x.status = true;
                        });
                    }
                    item.status = true;
                }
                await _unitOfWork.CompleteAsync();
            }
            return true;
        }

        public async Task<List<Guid>> GetPlanParentNodes(Guid parentNodeId, List<Guid> parentNodeIds, List<tpl_plan_template_details> planChapters)
        {
            if (parentNodeId != Guid.Parse("00000000-0000-0000-0000-000000000000"))
            {
                parentNodeIds.Add(parentNodeId);
                var parentNode = planChapters.FirstOrDefault(x => x.node_id == parentNodeId && x.status.HasValue && !x.status.Value);
                if (parentNode != null)
                    await GetPlanParentNodes(parentNode.parent_node_id, parentNodeIds, planChapters);
            }
            return parentNodeIds;
        }

        public async Task CopyExistingPlanQueueAsync(string userId, PlanCopyHelper resqObj)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            dynamic jsonGuid = new JObject();
            jsonGuid.Add("userId", userId);
            jsonGuid.Add("tenantId", userDetails.tenant_id);
            jsonGuid.Add("planToCopyFrom", resqObj.PlanToCopyFrom);
            jsonGuid.Add("newPlanName", resqObj.NewPlanName);
            jsonGuid.Add("newPlanShortName", resqObj.NewPlanShortName);

            string strJsonGuid = JsonConvert.SerializeObject(jsonGuid);
            Dictionary<string, int> modBudgetYears = new Dictionary<string, int>() { };
            _backendJob.QueueMessage(userDetails, modBudgetYears, QueueName.copyexistingplanqueue, strJsonGuid);
        }

        public async Task<List<PlanGoalTargetImportGridHelper>> GetPlanGoalTargetImportCount(string userId, PlanGoalTargetImportGridInput input)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var dataSet = await _unitOfWork.PlanningRepository.GetPlanGoalTargetCountForImport(userDetails.tenant_id, Guid.Parse(input.CurrentPlanId), Guid.Parse(input.CurrentNodeId));
            if (dataSet.Any() && input.FilterObj != null)
            {
                if (!string.IsNullOrEmpty(input.FilterObj.PlanName))
                    dataSet = dataSet.Where(x => x.PlanName.ToLower().Contains(input.FilterObj.PlanName.ToLower())).ToList();
                if (!string.IsNullOrEmpty(input.FilterObj.PlanStatus))
                    dataSet = dataSet.Where(x => x.PlanStatus.ToLower().Contains(input.FilterObj.PlanStatus.ToLower())).ToList();
                if (!string.IsNullOrEmpty(input.FilterObj.PlanType))
                    dataSet = dataSet.Where(x => x.PlanType.ToLower().Contains(input.FilterObj.PlanType.ToLower())).ToList();
            }
            return dataSet;
        }

        public async Task<List<PlanGoalTargetImportGridHelper>> GetPlanSpecificGoalsImportData(string userId, PlanGoalTargetImportGridInput input)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var dataSet = await _unitOfWork.PlanningRepository.GetPlanGoalImportData(userDetails.tenant_id, Guid.Parse(input.CurrentPlanId), Guid.Parse(input.ProcessPlanId), Guid.Parse(input.CurrentNodeId));
            switch (input.FilterDataValue)
            {
                case PlanGoalTargetImportFilter.Imported:
                    dataSet = dataSet.Where(x => x.IsGoalProcessed).ToList();
                    break;

                case PlanGoalTargetImportFilter.Notimported:
                    dataSet = dataSet.Where(x => !x.IsGoalProcessed).ToList();
                    break;
            }

            if (dataSet.Any() && input.FilterObj != null)
            {
                if (!string.IsNullOrEmpty(input.FilterObj.NodeName))
                    dataSet = dataSet.Where(x => x.NodeName.ToLower().Contains(input.FilterObj.NodeName.ToLower())).ToList();
                if (!string.IsNullOrEmpty(input.FilterObj.GoalName))
                    dataSet = dataSet.Where(x => x.GoalName.ToLower().Contains(input.FilterObj.GoalName.ToLower())).ToList();
                if (!string.IsNullOrEmpty(input.FilterObj.MotherGoalName))
                    dataSet = dataSet.Where(x => x.MotherGoalName.ToLower().Contains(input.FilterObj.MotherGoalName.ToLower())).ToList();
            }
            return dataSet;
        }

        public async Task<List<PlanGoalTargetImportGridHelper>> GetPlanSpecificGoalsTargetImportData(string userId, PlanGoalTargetImportGridInput inputData)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var dataSet = await _unitOfWork.PlanningRepository.GetPlanTargetImportData(userDetails.tenant_id, Guid.Parse(inputData.CurrentPlanId), Guid.Parse(inputData.ProcessPlanId), Guid.Parse(inputData.CurrentNodeId));
            switch (inputData.FilterDataValue)
            {
                case PlanGoalTargetImportFilter.Imported:
                    dataSet = dataSet.Where(x => x.Isimported).ToList();
                    break;

                case PlanGoalTargetImportFilter.Notimported:
                    dataSet = dataSet.Where(x => !x.Isimported).ToList();
                    break;
            }
            if (dataSet.Any() && inputData.FilterObj != null)
            {
                if (!string.IsNullOrEmpty(inputData.FilterObj.GoalName))
                    dataSet = dataSet.Where(x => x.GoalName.ToLower().Contains(inputData.FilterObj.GoalName.ToLower())).ToList();
                if (!string.IsNullOrEmpty(inputData.FilterObj.NodeName))
                    dataSet = dataSet.Where(x => x.NodeName.ToLower().Contains(inputData.FilterObj.NodeName.ToLower())).ToList();
                if (!string.IsNullOrEmpty(inputData.FilterObj.TargetName))
                    dataSet = dataSet.Where(x => x.TargetName.ToLower().Contains(inputData.FilterObj.TargetName.ToLower())).ToList();
                if (!string.IsNullOrEmpty(inputData.FilterObj.MotherGoalName))
                    dataSet = dataSet.Where(x => x.MotherGoalName.ToLower().Contains(inputData.FilterObj.MotherGoalName.ToLower())).ToList();
                if (!string.IsNullOrEmpty(inputData.FilterObj.MotherTargetName))
                    dataSet = dataSet.Where(x => x.MotherTargetName.ToLower().Contains(inputData.FilterObj.MotherTargetName.ToLower())).ToList();
            }
            return dataSet;
        }

        public async Task<string> ImportGoalandTarget(string userId, PlanGoalTargetImportRequest input)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            bool isDbUpdated = false;
            List<tpl_plan_goal> goalMappingSet = new List<tpl_plan_goal>();
            List<tpl_plan_target> targetMappingSet = new List<tpl_plan_target>();
            if (input.IsGoal)
            {
                var linkedGoalData = await _unitOfWork.PlanningRepository.GetGoalsBasedonPlanIdandNodeId(userDetails.tenant_id, Guid.Parse(input.PlanId), Guid.Parse(input.NodeId));
                foreach (var item in from item in input.Data.Where(x => x.IsGoalProcessed)
                                     where !linkedGoalData.Any(x => x.fk_node_id == Guid.Parse(input.NodeId) && x.fk_plan_goal_id == item.GoalId)
                                     select item)
                {
                    RetrieveGoalMappingList(input, userDetails, item, goalMappingSet);
                }

                foreach (var mapping in goalMappingSet)
                {
                    _unitOfWork.GenericRepo.Add(mapping);
                    isDbUpdated = true;
                }
            }
            else
            {
                var linkedGoalData = await _unitOfWork.PlanningRepository.GetGoalsBasedonPlanIdandNodeId(userDetails.tenant_id, Guid.Parse(input.PlanId), Guid.Parse(input.NodeId));
                var linkedTargetData = await _unitOfWork.PlanningRepository.GetTargetsBasedonPlanIdandNodeId(userDetails.tenant_id, Guid.Parse(input.PlanId), Guid.Parse(input.NodeId));

                // Goal Mapping
                foreach (var item in from item in input.Data.Where(x => x.IsTargetProcessed)
                                     where !linkedGoalData.Any(x => x.fk_node_id == Guid.Parse(input.NodeId) && x.fk_plan_goal_id == item.GoalId)
                                     select item)
                {
                    RetrieveGoalMappingList(input, userDetails, item, goalMappingSet);
                }

                foreach (var mapping in goalMappingSet)
                {
                    _unitOfWork.GenericRepo.Add(mapping);
                    isDbUpdated = true;
                }

                // Target Mapping
                foreach (var item in from item in input.Data.Where(x => x.IsTargetProcessed)
                                     where !linkedTargetData.Any(x => x.fk_node_id == Guid.Parse(input.NodeId) && x.fk_plan_target_id == item.TargetId)
                                     select item)
                {
                    RetrieveTargetMappingList(input, userDetails, item, targetMappingSet);
                }

                foreach (var mapping in targetMappingSet)
                {
                    _unitOfWork.GenericRepo.Add(mapping);
                    isDbUpdated = true;
                }
            }

            if (isDbUpdated)
                await _unitOfWork.CompleteAsync();

            return PlanPublishConstants.ImportSuccessful;
        }

        public async Task<List<PlanObjectSortHelper>> GetAssignmentSortOrder(string userId, string planId, string nodeId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            return await FetchAssignmentSortOrderAsync(Guid.Parse(planId), Guid.Parse(nodeId), userDetails.tenant_id);
        }

        public async Task<bool> SaveUpdateAssignmentSortOrder(string userId, Guid planId, Guid nodeId, IEnumerable<PlanObjectSortHelper> sortOrderData)
        {
            try
            {
                int count = 1;
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                sortOrderData.ToList().ForEach(x => x.SortOrder = count++);
                
                List<tpl_assignment_sort> assignmentSortData = await _unitOfWork.PlanningRepository.RetrieveAssignmentSortDetails(planId, nodeId, userDetails.tenant_id);

                // Remove Existing Data
                if (assignmentSortData.Any())
                {
                    _unitOfWork.GenericRepo.BulkDelete(assignmentSortData);
                }

                //Add Goal Sorting Data
                List<tpl_assignment_sort> newData = new();
                foreach (var item in sortOrderData)
                {
                    newData.Add(new tpl_assignment_sort
                    {
                        fk_plan_id = planId,
                        fk_node_id = nodeId,
                        fk_plan_assignment_id = Guid.Parse(item.Id),
                        sort_order = item.SortOrder,
                        fk_tenant_id = userDetails.tenant_id,
                        updated = DateTime.UtcNow,
                        updated_by = userDetails.pk_id
                    });
                }
                _unitOfWork.GenericRepo.BulkInsert(newData);
                await _unitOfWork.CompleteAsync();
                return true;
                
            }
            catch (Exception)
            {
                throw;
            }
        }

        private void RetrieveGoalMappingList(PlanGoalTargetImportRequest input, UserData userDetails, PlanGoalTargetImportGridHelper? item, List<tpl_plan_goal> goalMappingSet)
        {
            if (goalMappingSet.FirstOrDefault(x => x.fk_plan_id == Guid.Parse(input.PlanId) &&
                                         x.fk_node_id == Guid.Parse(input.NodeId) &&
                                         x.fk_plan_goal_id == item.GoalId) == null)
            {
                goalMappingSet.Add(new tpl_plan_goal
                {
                    fk_plan_id = Guid.Parse(input.PlanId),
                    fk_node_id = Guid.Parse(input.NodeId),
                    fk_plan_goal_id = item.GoalId,
                    plan_reference = Guid.Empty,
                    plan_reference_text = String.Empty,
                    fk_tenant_id = userDetails.tenant_id,
                    created_by = userDetails.pk_id,
                    created_date = DateTime.UtcNow,
                    updated_by = userDetails.pk_id,
                    updated = DateTime.UtcNow
                });
            }
        }

        private void RetrieveTargetMappingList(PlanGoalTargetImportRequest input, UserData userDetails, PlanGoalTargetImportGridHelper? item, List<tpl_plan_target> targetMappingSet)
        {
            if (targetMappingSet.FirstOrDefault(x => x.fk_plan_id == Guid.Parse(input.PlanId) &&
                                         x.fk_node_id == Guid.Parse(input.NodeId) &&
                                         x.fk_plan_target_id == item.TargetId) == null)
            {
                targetMappingSet.Add(new tpl_plan_target
                {
                    fk_plan_id = Guid.Parse(input.PlanId),
                    fk_node_id = Guid.Parse(input.NodeId),
                    fk_plan_target_id = item.TargetId,
                    fk_tenant_id = userDetails.tenant_id,
                    created_by = userDetails.pk_id,
                    created_date = DateTime.UtcNow,
                    updated_by = userDetails.pk_id,
                    updated = DateTime.UtcNow
                });
            }
        }

        public async Task<List<PlanStrategyImportGridHelper>> GetPlanStrategyImportCount(string userId, PlanStrategyImportGridInput input)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            #region Fetch Count Data

            var dataSet = await _unitOfWork.PlanningRepository.GetPlanStrategyCountForImport(userDetails.tenant_id, Guid.Parse(input.CurrentPlanId), Guid.Parse(input.CurrentNodeId));

            #endregion Fetch Count Data

            #region Filtering Data based on Input

            if (dataSet.Any() && input.FilterObj != null)
            {
                if (!string.IsNullOrEmpty(input.FilterObj.PlanName))
                {
                    dataSet = dataSet.Where(x => x.PlanName.ToLower().Contains(input.FilterObj.PlanName.ToLower())).ToList();
                }
                if (!string.IsNullOrEmpty(input.FilterObj.PlanStatus))
                {
                    dataSet = dataSet.Where(x => x.PlanStatus.ToLower().Contains(input.FilterObj.PlanStatus.ToLower())).ToList();
                }
                if (!string.IsNullOrEmpty(input.FilterObj.PlanType))
                {
                    dataSet = dataSet.Where(x => x.PlanType.ToLower().Contains(input.FilterObj.PlanType.ToLower())).ToList();
                }
            }

            #endregion Filtering Data based on Input

            return dataSet;
        }

        public async Task<List<PlanStrategyImportGridHelper>> GetPlanStrategyImportDetails(string userId, PlanStrategyImportGridInput input)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            var dataSet = await _unitOfWork.PlanningRepository.GetPlanStrategyImportData(userDetails.tenant_id, Guid.Parse(input.CurrentPlanId), Guid.Parse(input.ProcessPlanId), Guid.Parse(input.CurrentNodeId));
            switch (input.FilterDataValue)
            {
                case PlanStrategyImportFilter.Imported:
                    dataSet = dataSet.Where(x => x.IsStrategyProcessed).ToList();
                    break;

                case PlanStrategyImportFilter.Notimported:
                    dataSet = dataSet.Where(x => !x.IsStrategyProcessed).ToList();
                    break;
            }

            switch (input.GoalTargetFilter)
            {
                case PlanStrategyGoalTargetFilter.ConnectedToGoalTarget:
                    dataSet = dataSet.Where(x => x.IsStrategyConnectedToGoalTarget).ToList();
                    break;

                case PlanStrategyGoalTargetFilter.NotConnectedToGoalTarget:
                    dataSet = dataSet.Where(x => !x.IsStrategyConnectedToGoalTarget).ToList();
                    break;
            }

            if (dataSet.Any())
            {
                if (!string.IsNullOrEmpty(input.FilterObj.StrategyName))
                {
                    dataSet = dataSet.Where(x => x.StrategyName.ToLower().Contains(input.FilterObj.StrategyName.ToLower())).ToList();
                }
                if (!string.IsNullOrEmpty(input.FilterObj.NodeName))
                {
                    dataSet = dataSet.Where(x => x.NodeName.ToLower().Contains(input.FilterObj.NodeName.ToLower())).ToList();
                }
                if (!string.IsNullOrEmpty(input.FilterObj.Goal))
                {
                    dataSet = dataSet.Where(x => x.GoalName.ToLower().Contains(input.FilterObj.Goal.ToLower())).ToList();
                }
                if (!string.IsNullOrEmpty(input.FilterObj.Target))
                {
                    dataSet = dataSet.Where(x => x.TargetName.ToLower().Contains(input.FilterObj.Target.ToLower())).ToList();
                }
            }
            return dataSet;
        }

        public async Task<IEnumerable<PlanModuleColumnSelector>> GetStrategyImportProcessGridColumnSelector(string userId, Guid planId, Guid nodeId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> planLangStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Planning");
            List<PlanModuleColumnSelector> columnSelectorList = new List<PlanModuleColumnSelector>();
            List<string> availableColumns = new List<string> {"Plan_Strategy_Import_process_planType",
                                                              "Plan_Strategy_Import_process_planProgress"
                                                             };
            tpl_column_selector columnSelector = await _unitOfWork.PlanningRepository.GetBlobPathBasedonPlanIdandNodeIdAndType(userDetails.tenant_id, planId, nodeId, PlanningGridColumnSelector.StrategyImportProcessGrid, 0);
            if (columnSelector == null)
            {
                int index = 1;
                foreach (var item in availableColumns)
                {
                    columnSelectorList.Add(new PlanModuleColumnSelector
                    {
                        ColumnId = item,
                        ColumnName = planLangStrings.FirstOrDefault(x => x.Key.Equals(item)).Value.LangText,
                        IsChecked = true,
                        Order = index
                    });
                    index++;
                }
            }
            else
            {
                string strategyImportProcessColSelector = await _blobHelper.GetTextBlobAsync(StorageAccount.AppStorage, BlobContainers.Planning, columnSelector.path);
                var detailsAvailableinBlob = JsonConvert.DeserializeObject<List<PlanModuleColumnSelector>>(strategyImportProcessColSelector);
                foreach (var item in detailsAvailableinBlob)
                {
                    if (availableColumns.Any(x => x.ToLower() == item.ColumnId.ToLower()))
                    {
                        columnSelectorList.Add(new PlanModuleColumnSelector
                        {
                            ColumnId = item.ColumnId,
                            ColumnName = item.ColumnName,
                            IsChecked = item.IsChecked,
                            Order = item.Order
                        });
                    }
                }

                var nonAvailableList = availableColumns.Select(x => x).Except(columnSelectorList.Select(x => x.ColumnId));
                if (nonAvailableList.Any())
                {
                    int index = columnSelectorList.LastOrDefault().Order + 1;
                    foreach (var item in nonAvailableList)
                    {
                        columnSelectorList.Add(new PlanModuleColumnSelector
                        {
                            ColumnId = item,
                            ColumnName = planLangStrings.FirstOrDefault(x => x.Key.Equals(item)).Value.LangText,
                            IsChecked = true,
                            Order = index
                        });
                        index++;
                    }
                }
            }
            return columnSelectorList;
        }

        public async Task<string> ImportStrategyData(string userId, PlanStrategyImportRequest input)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            bool isDbUpdated = false;
            var linkedStrategyData = await _unitOfWork.PlanningRepository.GetStrategiesBasedonPlanIdandNodeId(userDetails.tenant_id, Guid.Parse(input.PlanId), Guid.Parse(input.NodeId));
            foreach (var item in from item in input.Data.Where(x => x.IsStrategyProcessed)
                                 where !linkedStrategyData.Any(x => x.fk_node_Id == Guid.Parse(input.NodeId) && x.fk_plan_stategy_id == item.StrategyId)
                                 select item)
            {
                _unitOfWork.GenericRepo.Add(new tpl_plan_strategy
                {
                    fk_plan_ID = Guid.Parse(input.PlanId),
                    fk_node_Id = Guid.Parse(input.NodeId),
                    fk_plan_stategy_id = item.StrategyId,
                    fk_tenant_id = userDetails.tenant_id,
                    created_by = userDetails.pk_id,
                    created_date = DateTime.UtcNow,
                    updated_by = userDetails.pk_id,
                    updated = DateTime.UtcNow
                });
                isDbUpdated = true;
            }

            if (isDbUpdated)
                await _unitOfWork.CompleteAsync();

            return PlanPublishConstants.ImportSuccessful;
        }

        public async Task<List<PlanAssignmentImportGridHelper>> GetPlanAssignmentImportCount(string userId, PlanAssignmentImportGridInput input)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            #region Fetch Count Data

            var dataSet = await _unitOfWork.PlanningRepository.GetPlanAssignmentCountForImport(userDetails.tenant_id, Guid.Parse(input.CurrentPlanId), Guid.Parse(input.CurrentNodeId));

            #endregion Fetch Count Data

            #region Filtering Data based on Input

            if (dataSet.Any() && input.FilterObj != null)
            {
                if (!string.IsNullOrEmpty(input.FilterObj.PlanName))
                {
                    dataSet = dataSet.Where(x => x.PlanName.ToLower().Contains(input.FilterObj.PlanName.ToLower())).ToList();
                }
                if (!string.IsNullOrEmpty(input.FilterObj.PlanStatus))
                {
                    dataSet = dataSet.Where(x => x.PlanStatus.ToLower().Contains(input.FilterObj.PlanStatus.ToLower())).ToList();
                }
                if (!string.IsNullOrEmpty(input.FilterObj.PlanType))
                {
                    dataSet = dataSet.Where(x => x.PlanType.ToLower().Contains(input.FilterObj.PlanType.ToLower())).ToList();
                }
            }

            #endregion Filtering Data based on Input

            return dataSet;
        }

        public async Task<List<PlanAssignmentImportGridHelper>> GetPlanAssignmentImportDetails(string userId, PlanAssignmentImportGridInput input)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            var dataSet = await _unitOfWork.PlanningRepository.GetPlanAssignmentImportData(userDetails.tenant_id, Guid.Parse(input.CurrentPlanId), Guid.Parse(input.ProcessPlanId), Guid.Parse(input.CurrentNodeId));
            switch (input.FilterDataValue)
            {
                case PlanAssignmentImportFilter.Imported:
                    dataSet = dataSet.Where(x => x.IsAssignmentProcessed).ToList();
                    break;

                case PlanAssignmentImportFilter.NotImported:
                    dataSet = dataSet.Where(x => !x.IsAssignmentProcessed).ToList();
                    break;
            }

            switch (input.GoalTargetFilter)
            {
                case PlanAssignmentGoalTargetFilter.ConnectedToGoal:
                    dataSet = dataSet.Where(x => x.IsAssignmentConnectedToGoalTarget).ToList();
                    break;

                case PlanAssignmentGoalTargetFilter.NotConnectedToGoal:
                    dataSet = dataSet.Where(x => !x.IsAssignmentConnectedToGoalTarget).ToList();
                    break;
            }

            if (dataSet.Any())
            {
                if (!string.IsNullOrEmpty(input.FilterObj.AssignmentName))
                {
                    dataSet = dataSet.Where(x => x.AssignmentName.ToLower().Contains(input.FilterObj.AssignmentName.ToLower())).ToList();
                }
                if (!string.IsNullOrEmpty(input.FilterObj.NodeName))
                {
                    dataSet = dataSet.Where(x => x.NodeName.ToLower().Contains(input.FilterObj.NodeName.ToLower())).ToList();
                }
                if (!string.IsNullOrEmpty(input.FilterObj.GoalName))
                {
                    dataSet = dataSet.Where(x => x.GoalName.ToLower().Contains(input.FilterObj.GoalName.ToLower())).ToList();
                }
                if (!string.IsNullOrEmpty(input.FilterObj.TargetName))
                {
                    dataSet = dataSet.Where(x => x.TargetName.ToLower().Contains(input.FilterObj.TargetName.ToLower())).ToList();
                }
                if (!string.IsNullOrEmpty(input.FilterObj.MotherAssignmentName))
                {
                    dataSet = dataSet.Where(x => x.MotherAssignmentName.ToLower().Contains(input.FilterObj.MotherAssignmentName.ToLower())).ToList();
                }
            }
            return dataSet;
        }

        public async Task<string> ImportAssignmentData(string userId, PlanAssignmentImportRequest input)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            bool isDbUpdated = false;
            var linkedAssignmentData = await _unitOfWork.PlanningRepository.GetAssignmentsBasedonPlanIdandNodeId(userDetails.tenant_id, Guid.Parse(input.PlanId), Guid.Parse(input.NodeId));
            foreach (var item in from item in input.Data.Where(x => x.IsAssignmentProcessed)
                                 where !linkedAssignmentData.Any(x => x.fk_node_Id == Guid.Parse(input.NodeId) && x.fk_plan_assignment_id == item.AssignemntId)
                                 select item)
            {
                _unitOfWork.GenericRepo.Add(new tpl_plan_assignments
                {
                    fk_plan_ID = Guid.Parse(input.PlanId),
                    fk_node_Id = Guid.Parse(input.NodeId),
                    fk_plan_assignment_id = item.AssignemntId,
                    fk_tenant_id = userDetails.tenant_id,
                    created_by = userDetails.pk_id,
                    created_date = DateTime.UtcNow,
                    updated_by = userDetails.pk_id,
                    updated = DateTime.UtcNow
                });
                isDbUpdated = true;
            }

            if (isDbUpdated)
                await _unitOfWork.CompleteAsync();

            return PlanPublishConstants.ImportSuccessful;
        }

        #endregion public methods

        #region private methods

        private async Task SavePlanningMetaDataConfig(int pk_id, int tenantId, PlanMetaDataHelper input)
        {
            tpl_metadata_setup metadataCheck = await _unitOfWork.PlanningRepository.GetPlanMetadataSetupInfo(tenantId);
            if (metadataCheck != null)
            {
                metadataCheck.contact_flag = input.IsContactInformationAccessible;
                metadataCheck.attachment_flag = input.IsAttachmentsAccessible;
                metadataCheck.links_flag = input.isLinksAccessible;
                IEnumerable<tpl_metadata_setup_texts> txtData = await _unitOfWork.PlanningRepository.GetPlanMetadataSetupTexts(tenantId, metadataCheck.pk_metadata_id);
                int count = txtData.Select(x => x.sort_order).LastOrDefault();
                if (input.MetadataSetupTexts.Any())
                {
                    foreach (var txtdt in input.MetadataSetupTexts)
                    {
                        tpl_metadata_setup_texts setuptxtData = txtData.FirstOrDefault(x => x.fk_metadata_id == metadataCheck.pk_metadata_id && x.pk_plan_metadata_text_id == txtdt.PkMetadataIdText);
                        if (setuptxtData != null)
                        {
                            setuptxtData.title = txtdt.Title;
                            setuptxtData.show_flag = txtdt.ShowFlag;
                            setuptxtData.guidance_text = txtdt.GuidanceText;
                        }
                        else
                        {
                            tpl_metadata_setup_texts newMetadataText = new tpl_metadata_setup_texts
                            {
                                pk_plan_metadata_text_id = Guid.NewGuid(),
                                fk_metadata_id = metadataCheck.pk_metadata_id,
                                fk_tenant_id = tenantId,
                                title = txtdt.Title,
                                guidance_text = txtdt.GuidanceText,
                                show_flag = true,
                                sort_order = count + 1,
                                is_static = false,
                                updated = DateTime.UtcNow,
                                updated_by = pk_id
                            };
                            _unitOfWork.GenericRepo.Add(newMetadataText);
                        }
                    }
                }
            }
            else
            {
                Guid newGuid = Guid.NewGuid();
                tpl_metadata_setup newMetaData = new tpl_metadata_setup
                {
                    pk_metadata_id = newGuid,
                    fk_tenant_id = tenantId,
                    contact_flag = input.IsContactInformationAccessible,
                    attachment_flag = input.IsAttachmentsAccessible,
                    links_flag = input.isLinksAccessible,
                    updated = DateTime.UtcNow,
                    updated_by = pk_id
                };
                _unitOfWork.GenericRepo.Add(newMetaData);
                if (input.MetadataSetupTexts.Any())
                {
                    int sortOrder = 1;
                    foreach (var mtdtxt in input.MetadataSetupTexts)
                    {
                        tpl_metadata_setup_texts newMetadataText = new tpl_metadata_setup_texts
                        {
                            pk_plan_metadata_text_id = Guid.NewGuid(),
                            fk_metadata_id = newGuid,
                            fk_tenant_id = tenantId,
                            title = mtdtxt.Title,
                            guidance_text = mtdtxt.GuidanceText,
                            show_flag = true,
                            sort_order = sortOrder,
                            is_static = sortOrder == 1,
                            updated = DateTime.UtcNow,
                            updated_by = pk_id
                        };
                        _unitOfWork.GenericRepo.Add(newMetadataText);
                        sortOrder++;
                    }
                }
            }
            await _unitOfWork.CompleteAsync();
        }

        private async Task savePlanMetaDataContactInformation(PlanMetaDataHelper planMetaData, UserData userDetails)
        {
            tpl_metadata_contact_information metadataContactInfo = await _unitOfWork.PlanningRepository.GetPlanMetadataContactInformation(planMetaData.PlanId, userDetails.tenant_id);
            if (metadataContactInfo == null)
            {
                metadataContactInfo = new tpl_metadata_contact_information
                {
                    pk_id = Guid.NewGuid(),
                    fk_plan_id = planMetaData.PlanId,
                    fk_tenant_id = userDetails.tenant_id,
                    person_responsible = planMetaData.UnitResponsible,
                    epost = planMetaData.Email,
                    telephone = planMetaData.Phoneno,
                    updated = DateTime.UtcNow,
                    updateby = userDetails.pk_id
                };
                _unitOfWork.GenericRepo.Add(metadataContactInfo);
            }
            else
            {
                metadataContactInfo.person_responsible = planMetaData.UnitResponsible;
                metadataContactInfo.epost = planMetaData.Email;
                metadataContactInfo.telephone = planMetaData.Phoneno;
                metadataContactInfo.updated = DateTime.UtcNow;
                metadataContactInfo.updateby = userDetails.pk_id;
            }
            await _unitOfWork.CompleteAsync();
        }

        public async Task CreateAndUpdateMetadataDescriptionTextAsync(tpl_metadata_texts planMetaDataText, PlanMetadataText metadataDescriptionInfo, Guid planId, string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            if (planMetaDataText == null)
            {
                planMetaDataText = new tpl_metadata_texts
                {
                    pk_plan_metadata_text_id = Guid.NewGuid(),
                    fk_plan_id = planId,
                    fk_plan_metatdata_text_id = metadataDescriptionInfo.FkPlanMetaDataTextId,
                    fk_tenant_id = userDetails.tenant_id,
                    description = metadataDescriptionInfo.DescriptionData,
                    history_id = metadataDescriptionInfo.DescriptionId,
                    updated = DateTime.UtcNow,
                    updateby = userDetails.pk_id,
                };
                _unitOfWork.GenericRepo.Add(planMetaDataText);
                await _unitOfWork.CompleteAsync();
                if (!string.IsNullOrEmpty(metadataDescriptionInfo.DescriptionData))
                {
                    await _utility.SaveTextLogAsync(userId, metadataDescriptionInfo.DescriptionId, metadataDescriptionInfo.DescriptionData);
                }
            }
            else
            {
                planMetaDataText.description = metadataDescriptionInfo.DescriptionData;
                planMetaDataText.history_id = metadataDescriptionInfo.DescriptionId;
                planMetaDataText.updated = DateTime.UtcNow;
                planMetaDataText.updateby = userDetails.pk_id;
                _unitOfWork.Complete();
                if (!string.IsNullOrEmpty(metadataDescriptionInfo.DescriptionData) && metadataDescriptionInfo.LogHistory)
                {
                    await _utility.SaveTextLogAsync(userId, metadataDescriptionInfo.DescriptionId, metadataDescriptionInfo.DescriptionData);
                }
            }

        }

        private async Task savePlanMetaDataRelevantLinks(PlanMetaDataHelper planMetaData, UserData userDetails)
        {
            await _unitOfWork.PlanningRepository.RemoveAllLinksByPlanId(planMetaData.PlanId, userDetails.tenant_id);
            foreach (var item in planMetaData.ExternalLinks)
            {
                tpl_metadata_relevant_links relevantLink = new tpl_metadata_relevant_links
                {
                    pk_id = Guid.NewGuid(),
                    fk_plan_id = planMetaData.PlanId,
                    fk_tenant_id = userDetails.tenant_id,
                    protocol = item.Protocol,
                    url = item.Url,
                    title = item.Title,
                    sort_order = item.sortOrder,
                    updated = DateTime.UtcNow,
                    updated_by = userDetails.pk_id
                };
                _unitOfWork.GenericRepo.Add(relevantLink);
            }
            await _unitOfWork.CompleteAsync();
        }

        private PlanMetaDataHelper GetPlanMetadataAccessInfo(tpl_metadata_setup metadataSetupInfo, PlanMetaDataHelper planMetaData)
        {
            planMetaData.IsContactInformationAccessible = metadataSetupInfo.contact_flag;
            planMetaData.IsAttachmentsAccessible = metadataSetupInfo.attachment_flag;
            planMetaData.isLinksAccessible = metadataSetupInfo.links_flag;
            return planMetaData;
        }

        private string GetPlanRole(List<tpl_plan_participants> planParticipants, int userId)
        {
            var participantData = planParticipants.FirstOrDefault(x => x.fk_user_id == userId);
            if (participantData != null)
            {
                if (participantData.is_main_owner.HasValue && participantData.is_main_owner.Value)
                {
                    return PlanRole.ADMIN.ToString();
                }
                else if (participantData.write_access.HasValue && participantData.write_access.Value)
                {
                    return PlanRole.WRITE.ToString();
                }
                else
                {
                    return PlanRole.READ.ToString();
                }
            }
            return string.Empty;
        }

        private string GetPlanRoleTyeForNonAdminRole(List<tpl_plan_participants> planParticipants, int userId)
        {
            if (planParticipants.Any())
            {
                if (planParticipants.FirstOrDefault().write_access.HasValue && planParticipants.FirstOrDefault().write_access.Value)
                {
                    return PlanRole.WRITE.ToString();
                }
                else
                {
                    return PlanRole.READ.ToString();
                }
            }
            return string.Empty;
        }

        private bool DisplayPublishLink(List<tpl_plan_participants> planParticipants, int userId, bool isAdmin)
        {
            if (isAdmin) return true;
            var participantData = planParticipants.FirstOrDefault(x => x.fk_user_id == userId);
            if (participantData != null && participantData.is_main_owner.HasValue && participantData.is_main_owner.Value)
            {
                return true;
            }
            return false;
        }

        private void GetNodestoDisable(List<tpl_plan_template_details> treeDetails, Guid nodeId, List<Guid> childNodesToDisable)
        {
            var nodeTreeList = treeDetails.Where(x => x.parent_node_id == nodeId).ToList();
            foreach (var item in nodeTreeList)
            {
                childNodesToDisable.Add(item.node_id);
                GetNodestoDisable(treeDetails, item.node_id, childNodesToDisable);
            }
        }

        private async Task<string> ValidateAndUploadBlob(PlanType template, UserData userDetails, bool isPlanTemplate)
        {
            //Run validations
            List<ContentNodeType> validTypes = template.AllowedContentNodeTypes.ToList();

            //Dedupe - Remove duplicates of allowed content types if any by reassigning a clone of the distinct values
            template.AllowedContentNodeTypes = validTypes.Distinct().ToList();

            //Add default allowed types
            validTypes.Add(ContentNodeType.Folder);
            validTypes.Add(ContentNodeType.Text);

            IEnumerable<ContentNodeType> distinctValidTypes = validTypes.Distinct();

            IEnumerable<PlanTemplateNode> templateTree = template.TemplateTree;

            var distinctContentTypes = GetDistinctContentTypes(templateTree);

            IEnumerable<ContentNodeType> notAllowed = distinctContentTypes.Except(distinctValidTypes);
            if (notAllowed.Any())
            {
                string errMessage = "Plan template contains the following types that are not allowed - ";
                foreach (var problemNode in notAllowed)
                {
                    errMessage = $"{errMessage} {problemNode.ToString()}";
                }
                throw new InvalidDataException(errMessage);
            }

            //Update the node level
            await UpdateLevelNumberAsync(template.TemplateTree, 0, true, userDetails.user_name);

            //Upload template to blob storage
            string blobPath = isPlanTemplate ? $"{userDetails.tenant_id}/plantypes/{template.Id}.json" : $"{userDetails.tenant_id}/planstrategytype/{template.Id}.json";
            await _blobHelper.UploadTextBlobAsync(StorageAccount.AppStorage, BlobContainers.Planning, blobPath, JsonConvert.SerializeObject(template));
            return blobPath;
        }

        private IEnumerable<ContentNodeType> GetDistinctContentTypes(IEnumerable<PlanTemplateNode> templateTree)
        {
            var flatTree = FlattenTree(templateTree);

            var distinctTypes = flatTree.Select(x => x.ContentNodeType).Distinct().ToList();
            return distinctTypes;
        }

        private IEnumerable<PlanTemplateNode> FlattenTree(IEnumerable<PlanTemplateNode> currentLevel)
        {
            List<PlanTemplateNode> flatTree = new List<PlanTemplateNode>();
            foreach (var node in currentLevel)
            {
                flatTree.Add(node);
                if (node.Items != null && node.Items.Any())
                {
                    var children = FlattenTree(node.Items);
                    flatTree.AddRange(children);
                }
            }

            return flatTree;
        }

        // Below Method updates the level number for Plan Specific Tree
        // isInitialPlanStructure => While creating a new plan we should control the behaviour of Deletion based on plan Usage, so included the check
        private void UpdatePlanningTreeLevelNumber(IEnumerable<PlanningTree> currentLevel, int currentLevelNumber, string planUsage, bool isInitialPlanStructure = false)
        {
            int nextLevelNum = currentLevelNumber + 1;
            foreach (var node in currentLevel)
            {
                node.LevelNumber = currentLevelNumber;
                node.LevelClassName = node.Items != null && node.Items.Any() ? string.Format(levelClasshasChildren, node.LevelNumber + 1) : string.Format(levelClasshasNoChildren, node.LevelNumber + 1);
                if (currentLevelNumber == 0 && node.ContentNodeType == ContentNodeType.Text)
                {
                    node.ShowAbstractText = true;
                }
                if (isInitialPlanStructure)
                {
                    node.CanDeleted = planUsage.ToString().ToLower().Trim() != PlanningNodeType.Mandatory.ToString().ToLower().Trim();
                }
                if (node.Items != null && node.Items.Any())
                {
                    UpdatePlanningTreeLevelNumber(node.Items, nextLevelNum, planUsage, isInitialPlanStructure);
                }
            }
        }

        private async Task<string> CreatePlan(UserData userDetails, PlanDataHelper plan)
        {
            var planID = Guid.NewGuid();
            var planUsage = plan.planHelper.Usage.ToString();
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Planning");
            string userdefined = langStrings.FirstOrDefault(x => x.Key.Equals("PM_NodeAddedFromPlanning_Text")).Value.LangText;
            //tpl_plan
            tpl_plan planData = CreatePlanObject(userDetails, plan, planID, planUsage);

            if (plan.isOldPlan)
            {
                await UploadPlanDocuments(plan, userDetails, planID);
            }
            else
            {
                //participants details
                var participants = new List<tpl_plan_participants>();
                GetParticipantsData(userDetails, plan, planID, participants);

                if (participants.Any())
                {
                    participants.ForEach(x =>
                    {
                        x.created_date = DateTime.UtcNow;
                        x.updated = DateTime.UtcNow;
                    });
                    planData.tpl_plan_participants = participants;
                }

                //plan template details
                var templateTree = plan.planHelper.TemplateTree != null ? plan.planHelper.TemplateTree.ToList() : new List<PlanningTree>();
                var parentNodeDetails = templateTree.Select(x => new PlanningTree
                {
                    ContentNodeType = x.ContentNodeType,
                    TypeDisplayText = x.TypeDisplayText,
                    Name = x.Name,
                    ActualNodeId = x.ChildNodeID
                }).ToList();

                int nodeOrder = 0;

                // We Need to create parent child relation ship before saving Tree into database
                IEnumerable<PlanningTree> treeWithRelationship = CreateParentChildRelation(templateTree, parentNodeDetails, nodeOrder);

                // We have to flatten the tree structure and get the plan template details for saving it too in the database
                List<tpl_plan_template_details> templateDetails = new List<tpl_plan_template_details>();
                GetNodeDetailsfromTree(userdefined, userDetails, treeWithRelationship.ToList(), templateDetails, planID, planUsage);
                if (templateDetails.Any())
                {
                    templateDetails.ForEach(x =>
                    {
                        x.created_date = DateTime.UtcNow;
                        x.updated = DateTime.UtcNow;
                    });
                    planData.tpl_plan_template_details = templateDetails;
                }
            }
            _unitOfWork.GenericRepo.Add(planData);
            await _unitOfWork.CompleteAsync();
            return planID.ToString();
        }

        private void GetParticipantsData(UserData userDetails, PlanDataHelper plan, Guid planID, List<tpl_plan_participants> participants)
        {
            if (plan.mainOwnerList != null && plan.mainOwnerList.Any())
            {
                participants.AddRange(plan.mainOwnerList.Select(x => new tpl_plan_participants
                {
                    fk_plan_id = planID,
                    fk_user_id = int.Parse(x),
                    is_main_owner = true,
                    write_access = false,
                    read_access = false,
                    created_by = userDetails.pk_id,
                    updated_by = userDetails.pk_id,
                    fk_tenant_id = userDetails.tenant_id
                }));
            }

            if (plan.readList != null && plan.readList.Any())
            {
                participants.AddRange(plan.readList.Select(x => new tpl_plan_participants
                {
                    fk_plan_id = planID,
                    fk_user_id = int.Parse(x),
                    is_main_owner = false,
                    write_access = false,
                    read_access = true,
                    created_by = userDetails.pk_id,
                    updated_by = userDetails.pk_id,
                    fk_tenant_id = userDetails.tenant_id
                }));
            }
            if (plan.writeList != null && plan.writeList.Any())
            {
                participants.AddRange(plan.writeList.Select(x => new tpl_plan_participants
                {
                    fk_plan_id = planID,
                    fk_user_id = int.Parse(x),
                    is_main_owner = false,
                    write_access = true,
                    read_access = false,
                    created_by = userDetails.pk_id,
                    updated_by = userDetails.pk_id,
                    fk_tenant_id = userDetails.tenant_id
                }));
            }
        }

        private tpl_plan CreatePlanObject(UserData userDetails, PlanDataHelper plan, Guid planID, string planUsage)
        {
            return new tpl_plan()
            {
                pk_plan_id = planID,
                name = plan.planname,
                short_name = string.IsNullOrEmpty(plan.shortname) ? string.Empty : plan.shortname,
                fk_plan_type_id = Guid.Parse(plan.planTypeID),
                fk_plan_stategy_id = Guid.Empty,
                fk_plan_category_id = string.IsNullOrEmpty(plan.planCategoryID) ? 0 : int.Parse(plan.planCategoryID),
                state_funds = plan.stateFunds,
                parent_plan_id = Guid.Empty,
                fk_status_id = string.IsNullOrEmpty(plan.Status) ? 0 : int.Parse(plan.Status),
                created_by = userDetails.pk_id,
                updated_by = userDetails.pk_id,
                fk_tenant_id = userDetails.tenant_id,
                contenttypesused = JsonConvert.SerializeObject(plan.planHelper.AllowedContentNodeTypes),
                usage = planUsage,
                is_old_plan = plan.isOldPlan,
                created_date = DateTime.UtcNow,
                updated = DateTime.UtcNow,
                start_year = plan.StartYear,
                treated_case = string.IsNullOrEmpty(plan.TreatedCase) ? string.Empty : plan.TreatedCase,
                date_processed = string.IsNullOrEmpty(plan.DateProcessed) ? (DateTime?)null : DateTime.Parse(plan.DateProcessed),
                link_to_casefile = string.IsNullOrEmpty(plan.LinkToCaseFile) ? string.Empty : plan.LinkToCaseFile,
                fk_strategy_task_id = string.IsNullOrEmpty(plan.PlanStrategyTaskId) ? Guid.Empty : Guid.Parse(plan.PlanStrategyTaskId),
                valid_from = string.IsNullOrEmpty(plan.ValidFrom) ? 0 : int.Parse(plan.ValidFrom),
                valid_to = string.IsNullOrEmpty(plan.ValidTo) ? 0 : int.Parse(plan.ValidTo),
                planned_revision = string.IsNullOrEmpty(plan.PlannedRevision) ? 0 : int.Parse(plan.PlannedRevision),
                plan_category_type = string.IsNullOrEmpty(plan.PlanCategoryType) ? "" : plan.PlanCategoryType
            };
        }

        private IEnumerable<PlanningTree> CreateParentChildRelation(List<PlanningTree> tree, List<PlanningTree> parentNodeData, int nodeOrder = 0, string parentNodeID = null)
        {
            foreach (var node in tree)
            {
                var isMainNode = parentNodeData.FirstOrDefault(x => x.ContentNodeType == node.ContentNodeType && x.TypeDisplayText == node.TypeDisplayText && x.Name == node.Name && x.ActualNodeId == node.ChildNodeID);
                node.NodeOrder = nodeOrder + 1;
                if (isMainNode != null)
                {
                    node.ParentNodeID = Guid.Empty.ToString();
                    node.ChildNodeID = Guid.NewGuid().ToString();
                }
                else
                {
                    node.ParentNodeID = parentNodeID;
                    node.ChildNodeID = Guid.NewGuid().ToString();
                }
                var oldTree = node.Items.ToList();
                if (oldTree != null && oldTree.Any())
                {
                    CreateParentChildRelation(oldTree, parentNodeData, node.NodeOrder, node.ChildNodeID);
                }
                nodeOrder++;
            }
            return tree;
        }

        private void GetNodeDetailsfromTree(string userdefinedText, UserData userData, IEnumerable<PlanningTree> currentLevel, List<tpl_plan_template_details> templateDetails, Guid planID, string planUsage)
        {
            foreach (var item in currentLevel)
            {
                int usageType = planUsage.Trim().ToLower() == PlanningNodeType.Guideline.ToString().Trim().ToLower() ? (int)PlanningNodeType.Guideline : (int)PlanningNodeType.Mandatory;
                templateDetails.Add(new tpl_plan_template_details
                {
                    node_id = Guid.Parse(item.ChildNodeID),
                    node_text = string.IsNullOrEmpty(item.TypeDisplayText) ? string.Empty : item.TypeDisplayText,
                    node_type = item.ContentNodeType.ToString(),
                    node_order = item.NodeOrder,
                    parent_node_id = string.IsNullOrEmpty(item.ParentNodeID) ? Guid.Empty : Guid.Parse(item.ParentNodeID),
                    created_by = userData.pk_id,
                    updated_by = userData.pk_id,
                    fk_tenant_id = userData.tenant_id,
                    fk_plan_id = planID,
                    status = true,
                    node_planned_description = item.Description,
                    node_description = string.Empty,
                    node_name = item.Name,
                    usage_type = item.MandateText.ToLower().Trim() == userdefinedText.ToLower().Trim() ? (int)PlanningNodeType.UserDefined : usageType
                });

                if (item.Items.Any())
                {
                    GetNodeDetailsfromTree(userdefinedText, userData, item.Items.ToList(), templateDetails, planID, planUsage);
                }
            }
        }

        private IList<PlanningTree> BuildTree(IEnumerable<PlanningTree> source)
        {
            var groups = source.GroupBy(i => i.ParentNodeID);
            var roots = groups.FirstOrDefault(g => g.Key == Guid.Empty.ToString()).ToList();
            if (roots.Count > 0)
            {
                var dict = groups.Where(g => g.Key != Guid.Empty.ToString()).ToDictionary(g => g.Key, g => g.ToList());
                for (int i = 0; i < roots.Count; i++)
                    AddChildren(roots[i], dict);
            }
            return roots;
        }

        private static void AddChildren(PlanningTree node, IDictionary<string, List<PlanningTree>> source)
        {
            if (source.ContainsKey(node.ChildNodeID))
            {
                node.Items = source[node.ChildNodeID];
                for (int i = 0; i < node.Items.ToList().Count; i++)
                    AddChildren(node.Items.ToList()[i], source);
            }
            else
            {
                node.Items = new List<PlanningTree>();
            }
        }

        private string GetNodeTypeImage(ContentNodeType contentNodeType)
        {
            string nodeImage = string.Empty;
            switch (contentNodeType)
            {
                case ContentNodeType.Assignment:
                case ContentNodeType.Strategy:
                case ContentNodeType.Target:
                case ContentNodeType.Action:
                case ContentNodeType.Investment:
                case ContentNodeType.ClimateAction:
                case ContentNodeType.PlanStrategyTask:
                    nodeImage = @"../images/plan_icons_table.svg";
                    break;

                case ContentNodeType.Folder:
                    nodeImage = @"../images/plan_icons_parent_node.svg";
                    break;

                default:
                    nodeImage = @"../images/plan_icons_text.svg";
                    break;
            }
            return nodeImage;
        }

        private async Task<string> ModifyPlan(UserData userDetails, PlanDataHelper plandataHelper)
        {
            var planID = Guid.Parse(plandataHelper.planID);
            var modifiedTree = plandataHelper.planHelper.TemplateTree.ToList();
            var parentNodeDetails = modifiedTree.Select(x => new PlanningTree
            {
                ContentNodeType = x.ContentNodeType,
                TypeDisplayText = x.TypeDisplayText,
                Name = x.Name,
                ActualNodeId = x.ChildNodeID
            }).ToList();

            int nodeOrder = 0;
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Planning");
            // Get Newly Added Nodes from Modified Tree straightaway coming from UI
            var templateNodeHelper = new PlanTemplateNodeHelper
            {
                langStrings = langStrings,
                planUsage = plandataHelper.planHelper.Usage.ToString(),
                userDetails = userDetails,
                planID = planID,
                currentLevel = modifiedTree,
                parentNodeData = parentNodeDetails,
                newlyAddedNodes = new List<tpl_plan_template_details>(),
                totalNode = new List<NodeOrderHelper>(),
                nodeOrder = nodeOrder,
                parentNodeID = null
            };
            GetNewlyAddedNodes(templateNodeHelper);
            var newlyAddedNodes = templateNodeHelper.newlyAddedNodes;
            var totalNodes = templateNodeHelper.totalNode;

            if (newlyAddedNodes.Any())
            {
                newlyAddedNodes.ForEach(x => { x.created_date = DateTime.UtcNow; x.updated = DateTime.UtcNow; });
                foreach (var newitem in newlyAddedNodes)
                {
                    _unitOfWork.GenericRepo.Add(newitem);
                }
            }
            var plan = await _unitOfWork.PlanningRepository.GetPlanbyID(userDetails.tenant_id, planID);

            //Remove and Add Participants if any
            var userSettoRemove = plan.tpl_plan_participants.ToList();
            if (userSettoRemove.Any())
            {
                foreach (var record in userSettoRemove)
                {
                    _unitOfWork.GenericRepo.Delete(record);
                }
            }

            foreach (var item in plandataHelper.mainOwnerList)
            {
                _unitOfWork.GenericRepo.Add(new tpl_plan_participants
                {
                    fk_plan_id = planID,
                    fk_user_id = int.Parse(item),
                    is_main_owner = true,
                    write_access = false,
                    read_access = false,
                    created_by = userDetails.pk_id,
                    updated_by = userDetails.pk_id,
                    fk_tenant_id = userDetails.tenant_id,
                    created_date = DateTime.UtcNow,
                    updated = DateTime.UtcNow
                });
            }

            foreach (var item in plandataHelper.writeList)
            {
                _unitOfWork.GenericRepo.Add(new tpl_plan_participants
                {
                    fk_plan_id = planID,
                    fk_user_id = int.Parse(item),
                    is_main_owner = false,
                    write_access = true,
                    read_access = false,
                    created_by = userDetails.pk_id,
                    updated_by = userDetails.pk_id,
                    fk_tenant_id = userDetails.tenant_id,
                    created_date = DateTime.UtcNow,
                    updated = DateTime.UtcNow
                });
            }
            foreach (var item in plandataHelper.readList)
            {
                _unitOfWork.GenericRepo.Add(new tpl_plan_participants
                {
                    fk_plan_id = planID,
                    fk_user_id = int.Parse(item),
                    is_main_owner = false,
                    write_access = false,
                    read_access = true,
                    created_by = userDetails.pk_id,
                    updated_by = userDetails.pk_id,
                    fk_tenant_id = userDetails.tenant_id,
                    created_date = DateTime.UtcNow,
                    updated = DateTime.UtcNow
                });
            }

            // Disable Nodes which are deleted from front end
            var nodesList = plan.tpl_plan_template_details.Select(x => x.node_id.ToString());
            var disableNodesSet = nodesList.Except(totalNodes.Select(x => x.NodeID));
            plan.tpl_plan_template_details.Where(z => disableNodesSet.Contains(z.node_id.ToString())).ToList().ForEach(x => { x.status = false; x.updated = DateTime.UtcNow; });
            plan.tpl_plan_template_details.Where(z => disableNodesSet.Contains(z.parent_node_id.ToString())).ToList().ForEach(x => { x.status = false; x.updated = DateTime.UtcNow; });

            // Finally Updating the Node Order and Node Description and set ParentNode id which was entered during Node Modification
            plan.tpl_plan_template_details.ToList().ForEach(
                x =>
                {
                    x.node_order = totalNodes.FirstOrDefault(z => z.NodeID == x.node_id.ToString()) != null ? totalNodes.FirstOrDefault(z => z.NodeID == x.node_id.ToString()).NodeOrder : 0;
                });

            foreach (var item in plan.tpl_plan_template_details)
            {
                var individualNode = totalNodes.FirstOrDefault(z => z.NodeID == item.node_id.ToString());
                if (individualNode != null)
                {
                    item.node_planned_description = individualNode.NodePlannedDesc;
                    item.parent_node_id = Guid.Parse(individualNode.ParentNodeID);
                }
            }

            foreach (var item in plan.tpl_plan_template_details)
            {
                item.updated = DateTime.UtcNow;
                item.updated_by = userDetails.pk_id;
            }

            // Update main plan details if any
            plan.name = plandataHelper.planname;
            plan.short_name = string.IsNullOrEmpty(plandataHelper.shortname) ? string.Empty : plandataHelper.shortname;
            plan.fk_plan_category_id = string.IsNullOrEmpty(plandataHelper.planCategoryID) ? 0 : int.Parse(plandataHelper.planCategoryID);
            plan.fk_strategy_task_id = string.IsNullOrEmpty(plandataHelper.PlanStrategyTaskId) ? Guid.Empty : Guid.Parse(plandataHelper.PlanStrategyTaskId);
            plan.fk_status_id = string.IsNullOrEmpty(plandataHelper.Status) ? 0 : int.Parse(plandataHelper.Status);
            plan.state_funds = plandataHelper.stateFunds;
            plan.updated = DateTime.UtcNow;
            plan.treated_case = string.IsNullOrEmpty(plandataHelper.TreatedCase) ? string.Empty : plandataHelper.TreatedCase;
            plan.date_processed = string.IsNullOrEmpty(plandataHelper.DateProcessed) ? (DateTime?)null : DateTime.Parse(plandataHelper.DateProcessed);
            plan.link_to_casefile = string.IsNullOrEmpty(plandataHelper.LinkToCaseFile) ? string.Empty : plandataHelper.LinkToCaseFile;
            plan.valid_from = string.IsNullOrEmpty(plandataHelper.ValidFrom) ? 0 : int.Parse(plandataHelper.ValidFrom);
            plan.valid_to = string.IsNullOrEmpty(plandataHelper.ValidTo) ? 0 : int.Parse(plandataHelper.ValidTo);
            plan.planned_revision = string.IsNullOrEmpty(plandataHelper.PlannedRevision) ? 0 : int.Parse(plandataHelper.PlannedRevision);
            plan.plan_category_type = string.IsNullOrEmpty(plandataHelper.PlanCategoryType) ? "" : plandataHelper.PlanCategoryType;

            //Update document if new one uploaded
            if (plan.is_old_plan)
            {
                await UploadPlanDocuments(plandataHelper, userDetails, planID);
            }

            _unitOfWork.GenericRepo.Update(plan);
            await _unitOfWork.CompleteAsync();

            //If planning strategy approved then save the node content in blob
            if (plan.start_year != 0 && !string.IsNullOrEmpty(plandataHelper.Status) && await IsPlanApproved(userDetails.user_name, int.Parse(plandataHelper.Status)))
            {
                await _planningStrategy.SavePlanningStrategyContentInBlob(userDetails.user_name, plan.pk_plan_id, plan.start_year);
            }
            return planID.ToString();
        }

        private void GetNewlyAddedNodes(PlanTemplateNodeHelper nodeHelper)
        {
            string userdefined = nodeHelper.langStrings.FirstOrDefault(x => x.Key.Equals("PM_NodeAddedFromPlanning_Text")).Value.LangText;
            int usageType = nodeHelper.planUsage.Trim().ToLower() == PlanningNodeType.Guideline.ToString().Trim().ToLower() ? (int)PlanningNodeType.Guideline : (int)PlanningNodeType.Mandatory;
            foreach (var node in nodeHelper.currentLevel)
            {
                var isMainNode = nodeHelper.parentNodeData.FirstOrDefault(x => x.ContentNodeType == node.ContentNodeType && x.TypeDisplayText == node.TypeDisplayText && x.Name == node.Name
                                                                                && x.ActualNodeId == node.ChildNodeID);
                node.NodeOrder = nodeHelper.nodeOrder + 1;
                if (isMainNode != null)
                {
                    bool isNewNode = (string.IsNullOrEmpty(node.ParentNodeID) && string.IsNullOrEmpty(node.ChildNodeID));
                    node.ParentNodeID = Guid.Empty.ToString(); // For Main Chapter, the parent ID will be always Empty Guid
                    node.ChildNodeID = isNewNode ? Guid.NewGuid().ToString() : node.ChildNodeID;
                    if (isNewNode)
                    {
                        nodeHelper.newlyAddedNodes.Add(new tpl_plan_template_details()
                        {
                            node_id = Guid.Parse(node.ChildNodeID),
                            node_text = node.TypeDisplayText,
                            node_type = node.ContentNodeType.ToString(),
                            node_order = node.NodeOrder,
                            parent_node_id = Guid.Parse(node.ParentNodeID),
                            created_by = nodeHelper.userDetails.pk_id,
                            updated_by = nodeHelper.userDetails.pk_id,
                            fk_tenant_id = nodeHelper.userDetails.tenant_id,
                            fk_plan_id = nodeHelper.planID,
                            status = true,
                            node_planned_description = node.Description,
                            node_description = string.Empty,
                            node_name = node.Name,
                            usage_type = node.MandateText != null ? node.MandateText.ToLower().Trim() == userdefined.ToLower().Trim() ? (int)PlanningNodeType.UserDefined : usageType : 0
                        });
                    }
                    nodeHelper.totalNode.Add(new NodeOrderHelper { NodeID = node.ChildNodeID, NodeOrder = node.NodeOrder, NodePlannedDesc = node.Description, ParentNodeID = node.ParentNodeID });
                }
                else
                {
                    node.ParentNodeID = nodeHelper.parentNodeID;
                    bool isNewNode = string.IsNullOrEmpty(node.ChildNodeID);
                    node.ChildNodeID = isNewNode ? Guid.NewGuid().ToString() : node.ChildNodeID;
                    if (isNewNode)
                    {
                        nodeHelper.newlyAddedNodes.Add(new tpl_plan_template_details()
                        {
                            node_id = Guid.Parse(node.ChildNodeID),
                            node_text = node.TypeDisplayText,
                            node_type = node.ContentNodeType.ToString(),
                            node_order = node.NodeOrder,
                            parent_node_id = Guid.Parse(node.ParentNodeID),
                            created_by = nodeHelper.userDetails.pk_id,
                            updated_by = nodeHelper.userDetails.pk_id,
                            fk_tenant_id = nodeHelper.userDetails.tenant_id,
                            fk_plan_id = nodeHelper.planID,
                            status = true,
                            node_planned_description = node.Description,
                            node_description = string.Empty,
                            node_name = node.Name,
                            usage_type = node.MandateText.ToLower().Trim() == userdefined.ToLower().Trim() ? (int)PlanningNodeType.UserDefined : usageType
                        });
                    }
                    nodeHelper.totalNode.Add(new NodeOrderHelper { NodeID = node.ChildNodeID, NodeOrder = node.NodeOrder, NodePlannedDesc = node.Description, ParentNodeID = node.ParentNodeID });
                }
                var oldTree = node.Items.ToList();
                if (oldTree != null && oldTree.Any())
                {
                    GetNewlyAddedNodes(new PlanTemplateNodeHelper
                    {
                        langStrings = nodeHelper.langStrings,
                        planUsage = nodeHelper.planUsage,
                        userDetails = nodeHelper.userDetails,
                        planID = nodeHelper.planID,
                        currentLevel = oldTree,
                        parentNodeData = nodeHelper.parentNodeData,
                        newlyAddedNodes = nodeHelper.newlyAddedNodes,
                        totalNode = nodeHelper.totalNode,
                        nodeOrder = node.NodeOrder,
                        parentNodeID = node.ChildNodeID
                    });
                }
                nodeHelper.nodeOrder++;
            }
        }

        private async Task<string> CreateGoal(string userId, PlanGoalHelper planGoalData)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            // Master Goal i.e., available across all plans
            var goalData = new tpl_goal
            {
                pk_plan_goal_id = Guid.NewGuid(),
                fk_goal_id = Guid.Empty,
                name = planGoalData.GoalName,
                tags = !planGoalData.Tags.Any() ? string.Empty : await AddorGetTags(userId, planGoalData.Tags),
                fk_sustainable_goal_id = !planGoalData.SustainableGoalId.Any() ? string.Empty : string.Join(",", planGoalData.SustainableGoalId),
                description = planGoalData.Description,
                fk_focusarea_id = planGoalData.FocusAreaId.Any() ? int.Parse(planGoalData.FocusAreaId.FirstOrDefault()) : 0,
                fk_tenant_id = userDetails.tenant_id,
                created_by = userDetails.pk_id,
                created_date = DateTime.UtcNow,
                updated_by = userDetails.pk_id,
                updated = DateTime.UtcNow,
                fk_masterplan_id = planGoalData.PlanId
            };
            _unitOfWork.GenericRepo.Add(goalData);

            // Goal Specific to Plan and Node
            var planGoal = new tpl_plan_goal
            {
                fk_plan_id = planGoalData.PlanId,
                fk_node_id = planGoalData.NodeId,
                fk_plan_goal_id = goalData.pk_plan_goal_id,
                plan_reference = planGoalData.PlanReference,
                plan_reference_text = planGoalData.PlanReferenceText,
                fk_tenant_id = userDetails.tenant_id,
                created_by = userDetails.pk_id,
                created_date = DateTime.UtcNow,
                updated_by = userDetails.pk_id,
                updated = DateTime.UtcNow,
            };

            _unitOfWork.GenericRepo.Add(planGoal);
            await _unitOfWork.CompleteAsync();
            await AddUpdateGoalStrategyData(userId, planGoalData, goalData.pk_plan_goal_id);

            return goalData.pk_plan_goal_id.ToString();
        }

        private async Task<string> ModifyGoal(string userId, PlanGoalHelper planGoalData)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var masterGoalData = await _unitOfWork.PlanningRepository.GetMasterGoalSingleData(userDetails.tenant_id, planGoalData.MasterGoalId);

            if (masterGoalData != null)
            {
                masterGoalData.fk_goal_id = Guid.Empty;
                masterGoalData.description = planGoalData.Description;
                masterGoalData.name = planGoalData.GoalName;
                if (GetMasterPlanIDforGoal(masterGoalData) == Guid.Empty)
                {
                    masterGoalData.fk_masterplan_id = planGoalData.PlanId;
                    await UpdateLinkedTargetMasterPlanID(userId, planGoalData.MasterGoalId, planGoalData.PlanId);
                }

                masterGoalData.tags = !planGoalData.Tags.Any() ? string.Empty : await AddorGetTags(userId, planGoalData.Tags);
                masterGoalData.fk_sustainable_goal_id = !planGoalData.SustainableGoalId.Any() ? string.Empty : string.Join(",", planGoalData.SustainableGoalId);
                masterGoalData.fk_focusarea_id = planGoalData.FocusAreaId.Any() ? int.Parse(planGoalData.FocusAreaId.FirstOrDefault()) : 0;
                masterGoalData.fk_tenant_id = userDetails.tenant_id;
                masterGoalData.created_by = userDetails.pk_id;
                masterGoalData.created_date = DateTime.UtcNow;
                masterGoalData.updated_by = userDetails.pk_id;
                masterGoalData.updated = DateTime.UtcNow;

                var planGoalsLinkedData = await _unitOfWork.PlanningRepository.GetPlanGoalSingleData(userDetails.tenant_id, planGoalData.MasterGoalId, planGoalData.PlanId, planGoalData.NodeId);
                if (planGoalsLinkedData != null)
                {
                    planGoalsLinkedData.plan_reference_text = planGoalData.PlanReferenceText;
                    planGoalsLinkedData.plan_reference = planGoalData.PlanReference;
                    planGoalsLinkedData.created_by = userDetails.pk_id;
                    planGoalsLinkedData.created_date = DateTime.UtcNow;
                    planGoalsLinkedData.updated_by = userDetails.pk_id;
                    planGoalsLinkedData.updated = DateTime.UtcNow;
                }
                else
                {
                    tpl_plan_goal newgoalData = new tpl_plan_goal
                    {
                        fk_plan_id = planGoalData.PlanId,
                        fk_node_id = planGoalData.NodeId,
                        fk_plan_goal_id = planGoalData.MasterGoalId,
                        plan_reference = Guid.Empty,
                        plan_reference_text = planGoalData.PlanReferenceText,
                        fk_tenant_id = userDetails.tenant_id,
                        created_by = userDetails.pk_id,
                        created_date = DateTime.UtcNow,
                        updated = DateTime.UtcNow,
                        updated_by = userDetails.pk_id
                    };
                    _unitOfWork.GenericRepo.Add(newgoalData);
                }
                await _unitOfWork.CompleteAsync();
                await AddUpdateGoalStrategyData(userId, planGoalData, planGoalData.MasterGoalId);
            }
            return planGoalData.MasterGoalId.ToString();
        }

        private string GetTags(IEnumerable<TcoActionTags> tagsList, string tags, Guid mPlanId, string referenceText = null, List<tpl_plan> list = null)
        {
            StringBuilder sb = new StringBuilder();
            string planName = GetMasterPlanName(list, mPlanId, true);
            if (!string.IsNullOrEmpty(referenceText) && planName != referenceText)
            {
                sb.Append("<span class='theme-plan-tags' title='" + referenceText + "'><img src='../images/plan_tag-01.svg' class='tags-img' alt='Plan tag'/>" + referenceText + "</span>");
            }

            if (!string.IsNullOrEmpty(tags) && tags.Split(',').Any())
            {
                List<string> tagsDesc = new List<string>();
                foreach (var tag in tags.Split(','))
                {
                    if (tagsList.FirstOrDefault(x => x.PkId == int.Parse(tag)) != null)
                    {
                        tagsDesc.Add(tagsList.FirstOrDefault(x => x.PkId == int.Parse(tag)).TagDescription);
                    }
                }

                if (tagsDesc.Any())
                {
                    foreach (var tag in tagsDesc)
                    {
                        sb.Append("<span class='planworker-tags' title='" + tag + "'><img src='../images/free_tag-01.svg' class='tags-img'/>" + tag + "</span>");
                    }
                }
            }
            return sb.ToString();
        }

        private string GetSustainibilityTags(IEnumerable<gco_un_susdev_goals> sustainibiltyList, string sustainabilitytags, ref Dictionary<string, string> ungoalTarget, bool isWebPublish = false)
        {
            StringBuilder sb = new StringBuilder();
            if (!string.IsNullOrEmpty(sustainabilitytags) && sustainabilitytags.Split(',').Any())
            {
                List<string> tagsDesc = new List<string>();
                var tags = sustainabilitytags.Split(',').OrderBy(x => x).ToList();
                foreach (var tag in tags)
                {
                    if (sustainibiltyList.FirstOrDefault(x => x.pk_goal_id == tag) != null)
                    {
                        string tagDescription = sustainibiltyList.FirstOrDefault(x => x.pk_goal_id == tag).goal_name;
                        tagsDesc.Add(tagDescription);
                        if (!ungoalTarget.ContainsKey(tag))
                        {
                            ungoalTarget.Add(tag, tagDescription);
                        }
                    }
                }

                if (tagsDesc.Any())
                {
                    tagsDesc = tagsDesc.OrderBy(x => x).ToList();
                    if (isWebPublish) return string.Join("<br>", tagsDesc);
                    foreach (var tag in tagsDesc)
                    {
                        sb.Append("<span class='planworker-tags' title='" + tag + "'>" + tag + "</span>");
                    }
                }
            }
            return sb.ToString();
        }

        private string GetTargetSustainibilityTags(IEnumerable<gco_un_susdev_targets> sustainibiltyList, string sustainabilitytags, ref Dictionary<string, string> unTarget, bool isWebPublish)
        {
            StringBuilder sb = new StringBuilder();
            if (!string.IsNullOrEmpty(sustainabilitytags) && sustainabilitytags.Split(',').Any())
            {
                List<string> tagsDesc = new List<string>();
                var tags = sustainabilitytags.Split(',').OrderBy(x => x).ToList();
                foreach (var tag in tags)
                {
                    if (sustainibiltyList.FirstOrDefault(x => x.pk_target_id == tag) != null)
                    {
                        string tagDescription = sustainibiltyList.FirstOrDefault(x => x.pk_target_id == tag).target_name;
                        tagsDesc.Add(tagDescription);
                        if (!unTarget.ContainsKey(tag))
                        {
                            unTarget.Add(tag, tagDescription);
                        }
                    }
                }

                if (tagsDesc.Any())
                {
                    if (isWebPublish) return string.Join("<br>", tagsDesc);
                    foreach (var tag in tagsDesc)
                    {
                        sb.Append("<span class='planworker-tags' title='" + tag + "'>" + tag + "</span><br>");
                    }
                }
            }
            return sb.ToString();
        }

        private string GetFocusArea(IEnumerable<tpl_focusarea> focusAreaList, int focusArea, bool iswebPublish = false)
        {
            if (focusArea != 0)
            {
                var text = focusAreaList.FirstOrDefault(x => x.pk_focus_area_id == focusArea);
                if (text == null) return string.Empty;
                return !iswebPublish ? "<span class='focusarea-tags' title='" + text.name + "'>" + text.name + "</span>&nbsp;" : text.name;
            }
            else
            {
                return string.Empty;
            }
        }

        private async Task<string> CreateTarget(string userId, PlanTargetHelper planTargetData)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            string paramValue = await _utility.GetParameterValueAsync(userId, "TARGET_USE_UNGOALS");
            bool paramVal = paramValue.ToLower() == "true";
            var unsustainableSet = !planTargetData.SustainableGoalId.Any() ? string.Empty : string.Join(",", planTargetData.SustainableGoalId);
            // Master Target i.e., available across all plans
            var targetData = new tpl_target
            {
                pk_plan_target_id = Guid.NewGuid(),
                fk_plan_goal_id = planTargetData.FkMasterGoalId,
                name = planTargetData.TargetName,
                tags = !planTargetData.Tags.Any() ? string.Empty : await AddorGetTags(userId, planTargetData.Tags),
                target_description = planTargetData.Description,
                fk_tenant_id = userDetails.tenant_id,
                created_by = userDetails.pk_id,
                created_date = DateTime.UtcNow,
                updated_by = userDetails.pk_id,
                updated = DateTime.UtcNow,
                fk_masterplan_id = planTargetData.PlanId
            };
            if (paramVal)
            {
                targetData.fk_param_sustainable_goal_id = unsustainableSet;
            }
            else
            {
                targetData.fk_sustainable_goal_id = unsustainableSet;
            }
            _unitOfWork.GenericRepo.Add(targetData);

            // Target Specific to Plan and Node
            var planTarget = new tpl_plan_target
            {
                fk_plan_id = planTargetData.PlanId,
                fk_node_id = planTargetData.NodeId,
                fk_plan_target_id = targetData.pk_plan_target_id,
                fk_tenant_id = userDetails.tenant_id,
                created_by = userDetails.pk_id,
                created_date = DateTime.UtcNow,
                updated_by = userDetails.pk_id,
                updated = DateTime.UtcNow,
            };
            _unitOfWork.GenericRepo.Add(planTarget);
            await _unitOfWork.CompleteAsync();

            return targetData.pk_plan_target_id.ToString();
        }

        private async Task<string> ModifyTarget(string userId, PlanTargetHelper planTargetData)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var masterTargetData = await _unitOfWork.PlanningRepository.GetMasterTargetSingleData(userDetails.tenant_id, planTargetData.FkMasterGoalId, planTargetData.MasterTargetId);
            string paramValue = await _utility.GetParameterValueAsync(userId, "TARGET_USE_UNGOALS");
            if (masterTargetData != null)
            {
                masterTargetData.name = planTargetData.TargetName;
                masterTargetData.tags = !planTargetData.Tags.Any() ? string.Empty : await AddorGetTags(userId, planTargetData.Tags);
                if (paramValue.ToLower() == "true")
                {
                    masterTargetData.fk_param_sustainable_goal_id = !planTargetData.SustainableGoalId.Any() ? string.Empty : string.Join(",", planTargetData.SustainableGoalId);
                }
                else
                {
                    masterTargetData.fk_sustainable_goal_id = !planTargetData.SustainableGoalId.Any() ? string.Empty : string.Join(",", planTargetData.SustainableGoalId);
                }
                masterTargetData.target_description = planTargetData.Description;
                masterTargetData.fk_tenant_id = userDetails.tenant_id;
                masterTargetData.created_by = userDetails.pk_id;
                masterTargetData.created_date = DateTime.UtcNow;
                masterTargetData.updated_by = userDetails.pk_id;
                masterTargetData.updated = DateTime.UtcNow;
                var masterPlanID = GetMasterPlanIDforTarget(masterTargetData);
                if (masterPlanID == Guid.Empty)
                {
                    masterTargetData.fk_masterplan_id = planTargetData.PlanId;
                }
                await _unitOfWork.CompleteAsync();
            }

            //Below Code is used if there is existing target detached for a given plan , goal and node and then again attached to the same combination,
            // in such case we have to make an entry in tpl_plan_target table
            var planSpecificTargetData = await _unitOfWork.PlanningRepository.GetSingleTargetForPlanIdNodeIdTargetId(userDetails.tenant_id,
                                                                                                                planTargetData.PlanId,
                                                                                                                planTargetData.NodeId,
                                                                                                                planTargetData.MasterTargetId);
            if (planSpecificTargetData == null)
            {
                var planTarget = new tpl_plan_target
                {
                    fk_plan_id = planTargetData.PlanId,
                    fk_node_id = planTargetData.NodeId,
                    fk_plan_target_id = planTargetData.MasterTargetId,
                    fk_tenant_id = userDetails.tenant_id,
                    created_by = userDetails.pk_id,
                    created_date = DateTime.UtcNow,
                    updated_by = userDetails.pk_id,
                    updated = DateTime.UtcNow,
                };
                _unitOfWork.GenericRepo.Add(planTarget);
                await _unitOfWork.CompleteAsync();
            }
            return planTargetData.MasterTargetId.ToString();
        }

        private List<PlanGoalGridHelper> GetDataBasedonDataSource(List<PlanGoalDataHelper> goalsSet,
                                                                  List<PlanTargetDataHelper> targetSet,
                                                                  List<PlanGridsSearchFilter> searchFilters,
                                                                  PlanSortingDetails sortDetails,
                                                                  PlanGoalTargetDataFormatHelper formatHelper,
                                                                  Dictionary<string, string> ungoal,
                                                                  Dictionary<string, string> unTarget,
                                                                  bool isWebPublish = false)
        {
            var planGoalGridHelper = new List<PlanGoalGridHelper>();
            var distinctGoals = new List<Guid>();

            if (sortDetails == null)
            {
                distinctGoals = !formatHelper.IsGoalSortingAvailable ? goalsSet.Select(x => x.GoalId).Distinct().ToList()
                                                                     : goalsSet.OrderBy(z => z.SortOrder).Select(x => x.GoalId).Distinct().ToList();
            }
            else
            {
                if ((string.IsNullOrEmpty(sortDetails.SortKey) || string.IsNullOrEmpty(sortDetails.SortOrder))
                    || (sortDetails != null && sortDetails.SortKey.ToLower() == "target".ToLower())
                    || (sortDetails != null && sortDetails.SortKey.ToLower() == "targetstatus".ToLower())
                    || (sortDetails != null && sortDetails.SortKey.ToLower() == "unTarget".ToLower())
                    || (sortDetails != null && sortDetails.SortKey.ToLower() == "targetTag".ToLower()))
                {
                    distinctGoals = !formatHelper.IsGoalSortingAvailable ? goalsSet.Select(x => x.GoalId).Distinct().ToList()
                                                                         : goalsSet.OrderBy(z => z.SortOrder).Select(x => x.GoalId).Distinct().ToList();
                }
                if (sortDetails.SortKey.ToLower() == "goals".ToLower() && sortDetails.SortOrder.ToLower() == "asc".ToLower())
                {
                    distinctGoals = !formatHelper.IsGoalSortingAvailable ? goalsSet.Select(x => x.GoalId).Distinct().ToList()
                                                                         : goalsSet.OrderBy(z => z.SortOrder).Select(x => x.GoalId).Distinct().ToList();
                }
                if (sortDetails.SortKey.ToLower() == "goals".ToLower() && sortDetails.SortOrder.ToLower() == "desc".ToLower())
                {
                    distinctGoals = !formatHelper.IsGoalSortingAvailable ? goalsSet.Select(x => x.GoalId).Distinct().ToList()
                                                                         : goalsSet.OrderByDescending(z => z.SortOrder).Select(x => x.GoalId).Distinct().ToList();
                }
                if (sortDetails.SortKey.ToLower() == "goalstatus".ToLower() && sortDetails.SortOrder.ToLower() == "asc".ToLower())
                {
                    distinctGoals = goalsSet.OrderBy(z => z.GoalStatus).Select(x => x.GoalId).Distinct().ToList();
                }
                if (sortDetails.SortKey.ToLower() == "goalstatus".ToLower() && sortDetails.SortOrder.ToLower() == "desc".ToLower())
                {
                    distinctGoals = goalsSet.OrderByDescending(z => z.GoalStatus).Select(x => x.GoalId).Distinct().ToList();
                }
                if (sortDetails.SortKey.ToLower() == "focusArea".ToLower() && sortDetails.SortOrder.ToLower() == "asc".ToLower())
                {
                    distinctGoals = goalsSet.OrderBy(z => z.GoalFocusAreaTag).Select(x => x.GoalId).Distinct().ToList();
                }
                if (sortDetails.SortKey.ToLower() == "focusArea".ToLower() && sortDetails.SortOrder.ToLower() == "desc".ToLower())
                {
                    distinctGoals = goalsSet.OrderByDescending(z => z.GoalFocusAreaTag).Select(x => x.GoalId).Distinct().ToList();
                }
                if (sortDetails.SortKey.ToLower() == "unGoal".ToLower() && sortDetails.SortOrder.ToLower() == "asc".ToLower())
                {
                    distinctGoals = goalsSet.OrderBy(z => z.GoalSustainbilityTag).Select(x => x.GoalId).Distinct().ToList();
                }
                if (sortDetails.SortKey.ToLower() == "unGoal".ToLower() && sortDetails.SortOrder.ToLower() == "desc".ToLower())
                {
                    distinctGoals = goalsSet.OrderByDescending(z => z.GoalSustainbilityTag).Select(x => x.GoalId).Distinct().ToList();
                }
                if (sortDetails.SortKey.ToLower() == "goalTag".ToLower() && sortDetails.SortOrder.ToLower() == "asc".ToLower())
                {
                    distinctGoals = goalsSet.OrderBy(z => z.GoalActionTags).Select(x => x.GoalId).Distinct().ToList();
                }
                if (sortDetails.SortKey.ToLower() == "goalTag".ToLower() && sortDetails.SortOrder.ToLower() == "desc".ToLower())
                {
                    distinctGoals = goalsSet.OrderByDescending(z => z.GoalActionTags).Select(x => x.GoalId).Distinct().ToList();
                }
            }

            Guid prevGoalID = Guid.Empty;
            foreach (var item in distinctGoals)
            {
                var targetsLinkedtoGoal = new List<PlanTargetDataHelper>();
                if (sortDetails == null)
                {
                    targetsLinkedtoGoal = targetSet.Where(x => x.ConnectedGoalId == item).OrderBy(z => z.SortOrder).ToList();
                }
                else
                {
                    if ((string.IsNullOrEmpty(sortDetails.SortKey) || string.IsNullOrEmpty(sortDetails.SortOrder))
                    || (sortDetails.SortKey.ToLower() == "goals".ToLower()) || (sortDetails.SortKey.ToLower() == "goalstatus".ToLower())
                    || (sortDetails.SortKey.ToLower() == "focusArea".ToLower()) || (sortDetails.SortKey.ToLower() == "unGoal".ToLower())
                    || (sortDetails.SortKey.ToLower() == "goalTag".ToLower()))
                    {
                        targetsLinkedtoGoal = targetSet.Where(x => x.ConnectedGoalId == item).OrderBy(z => z.SortOrder).ToList();
                    }
                    if (sortDetails.SortKey.ToLower() == "target".ToLower() && sortDetails.SortOrder.ToLower() == "asc".ToLower())
                    {
                        targetsLinkedtoGoal = targetSet.Where(x => x.ConnectedGoalId == item).OrderBy(z => z.TargetName).ToList();
                    }
                    if (sortDetails.SortKey.ToLower() == "target".ToLower() && sortDetails.SortOrder.ToLower() == "desc".ToLower())
                    {
                        targetsLinkedtoGoal = targetSet.Where(x => x.ConnectedGoalId == item).OrderByDescending(z => z.TargetName).ToList();
                    }
                    if (sortDetails.SortKey.ToLower() == "targetstatus".ToLower() && sortDetails.SortOrder.ToLower() == "asc".ToLower())
                    {
                        targetsLinkedtoGoal = targetSet.Where(x => x.ConnectedGoalId == item).OrderBy(z => z.TargetStatus).ToList();
                    }
                    if (sortDetails.SortKey.ToLower() == "targetstatus".ToLower() && sortDetails.SortOrder.ToLower() == "desc".ToLower())
                    {
                        targetsLinkedtoGoal = targetSet.Where(x => x.ConnectedGoalId == item).OrderByDescending(z => z.TargetStatus).ToList();
                    }
                    if (sortDetails.SortKey.ToLower() == "unTarget".ToLower() && sortDetails.SortOrder.ToLower() == "asc".ToLower())
                    {
                        targetsLinkedtoGoal = targetSet.Where(x => x.ConnectedGoalId == item).OrderBy(z => z.TargetSustainbilityTag).ToList();
                    }
                    if (sortDetails.SortKey.ToLower() == "unTarget".ToLower() && sortDetails.SortOrder.ToLower() == "desc".ToLower())
                    {
                        targetsLinkedtoGoal = targetSet.Where(x => x.ConnectedGoalId == item).OrderByDescending(z => z.TargetSustainbilityTag).ToList();
                    }
                    if (sortDetails.SortKey.ToLower() == "targetTag".ToLower() && sortDetails.SortOrder.ToLower() == "asc".ToLower())
                    {
                        targetsLinkedtoGoal = targetSet.Where(x => x.ConnectedGoalId == item).OrderBy(z => z.TargetActionTags).ToList();
                    }
                    if (sortDetails.SortKey.ToLower() == "targetTag".ToLower() && sortDetails.SortOrder.ToLower() == "desc".ToLower())
                    {
                        targetsLinkedtoGoal = targetSet.Where(x => x.ConnectedGoalId == item).OrderByDescending(z => z.TargetActionTags).ToList();
                    }
                }

                if (targetsLinkedtoGoal.Any())
                {
                    int index = 0;
                    foreach (var target in targetsLinkedtoGoal)
                    {
                        planGoalGridHelper.Add(new PlanGoalGridHelper
                        {
                            UniqueId = Guid.NewGuid().ToString(),
                            GoalId = goalsSet.FirstOrDefault(x => x.GoalId == item).GoalId.ToString(),
                            Goal = goalsSet.FirstOrDefault(x => x.GoalId == item).GoalName,
                            GoalDescription = goalsSet.FirstOrDefault(x => x.GoalId == item).GoalDescription,
                            GoalStatusDescription = goalsSet.FirstOrDefault(x => x.GoalId == item).GoalStatusDescription,
                            GoalStatus = goalsSet.FirstOrDefault(x => x.GoalId == item).GoalStatus,
                            GoalsActionTag = goalsSet.FirstOrDefault(x => x.GoalId == item).GoalActionTags,
                            FocusAreaTag = goalsSet.FirstOrDefault(x => x.GoalId == item).GoalFocusAreaTag,
                            FocusAreaId = goalsSet.FirstOrDefault(x => x.GoalId == item).FocusAreaId,
                            FocusAreaSortOrder = goalsSet.FirstOrDefault(x => x.GoalId == item).FocusAreaSortOrder,
                            GoalSustainbilityTag = goalsSet.FirstOrDefault(x => x.GoalId == item).GoalSustainbilityTag,
                            Target = target.TargetName,
                            TargetId = target.TargetId.ToString(),
                            TargetDescription = target.TargetDescription,
                            TargetStatus = target.TargetStatus,
                            TargetStatusDescription = target.TargetStatusDescription,
                            TargetsActionTag = target.TargetActionTags,
                            TargetSortOrder = target.SortOrder,
                            IsLastTarget = index == targetsLinkedtoGoal.Count - 1,
                            DisplayTargetDeleteIcon = true,
                            DisplayGoalDeleteIcon = false,
                            TargetSustainbilityTag = target.TargetSustainbilityTag,
                            IsMainOwnerGoal = isMainOwner(goalsSet.FirstOrDefault(x => x.GoalId == item).GoalMasterPlanId, formatHelper.PlanId),
                            MasterPlan = goalsSet.FirstOrDefault(x => x.GoalId == item).MasterPlan,
                            IsMainOwnerTarget = isMainOwner(target.TargetMasterPlanId, formatHelper.PlanId),
                            DisplayGoalStatusImage = prevGoalID == item ? false : true,
                            DisplayTargetStatusImage = true
                        });
                        index++;
                    }
                }
                else
                {
                    planGoalGridHelper.Add(new PlanGoalGridHelper
                    {
                        UniqueId = Guid.NewGuid().ToString(),
                        GoalId = goalsSet.FirstOrDefault(x => x.GoalId == item).GoalId.ToString(),
                        Goal = goalsSet.FirstOrDefault(x => x.GoalId == item).GoalName,
                        GoalDescription = goalsSet.FirstOrDefault(x => x.GoalId == item).GoalDescription,
                        GoalStatusDescription = goalsSet.FirstOrDefault(x => x.GoalId == item).GoalStatusDescription,
                        GoalStatus = goalsSet.FirstOrDefault(x => x.GoalId == item).GoalStatus,
                        GoalsActionTag = goalsSet.FirstOrDefault(x => x.GoalId == item).GoalActionTags,
                        FocusAreaTag = goalsSet.FirstOrDefault(x => x.GoalId == item).GoalFocusAreaTag,
                        FocusAreaId = goalsSet.FirstOrDefault(x => x.GoalId == item).FocusAreaId,
                        FocusAreaSortOrder = goalsSet.FirstOrDefault(x => x.GoalId == item).FocusAreaSortOrder,
                        GoalSustainbilityTag = goalsSet.FirstOrDefault(x => x.GoalId == item).GoalSustainbilityTag,
                        Target = string.Empty,
                        TargetId = Guid.Empty.ToString(),
                        TargetDescription = string.Empty,
                        TargetStatus = string.Empty,
                        TargetStatusDescription = string.Empty,
                        TargetsActionTag = string.Empty,
                        TargetSortOrder = 0,
                        IsLastTarget = true,
                        DisplayTargetDeleteIcon = false,
                        DisplayGoalDeleteIcon = true,
                        TargetSustainbilityTag = string.Empty,
                        IsMainOwnerGoal = isMainOwner(goalsSet.FirstOrDefault(x => x.GoalId == item).GoalMasterPlanId, formatHelper.PlanId),
                        MasterPlan = goalsSet.FirstOrDefault(x => x.GoalId == item).MasterPlan,
                        IsMainOwnerTarget = false,
                        DisplayGoalStatusImage = prevGoalID == item ? false : true,
                        DisplayTargetStatusImage = false
                    });
                }
                prevGoalID = item;
            }

            if (searchFilters != null && searchFilters.Any())
            {
                foreach (var item in searchFilters)
                {
                    if (item.SearchKey == (int)PlanningGridSearchType.focusArea)
                    {
                        planGoalGridHelper = planGoalGridHelper.Where(x => Regex.Replace(x.FocusAreaTag.ToLower(), "<.*?>", String.Empty).Contains(item.SearchValue.ToLower())).ToList();
                    }
                    if (item.SearchKey == (int)PlanningGridSearchType.Goal)
                    {
                        planGoalGridHelper = planGoalGridHelper.Where(x => x.Goal.ToLower().Contains(item.SearchValue.ToLower())).ToList();
                    }
                    if (item.SearchKey == (int)PlanningGridSearchType.GoalStatus)
                    {
                        planGoalGridHelper = planGoalGridHelper.Where(x => x.GoalStatus.ToLower().Contains(item.SearchValue.ToLower())).ToList();
                    }
                    if (item.SearchKey == (int)PlanningGridSearchType.unGoal)
                    {
                        planGoalGridHelper = planGoalGridHelper.Where(x => Regex.Replace(x.GoalSustainbilityTag.ToLower(), "<.*?>", String.Empty).Contains(item.SearchValue.ToLower())).ToList();
                    }
                    if (item.SearchKey == (int)PlanningGridSearchType.goalTag)
                    {
                        planGoalGridHelper = planGoalGridHelper.Where(x => Regex.Replace(x.GoalsActionTag.ToLower(), "<.*?>", String.Empty).Contains(item.SearchValue.ToLower())).ToList();
                    }
                    if (item.SearchKey == (int)PlanningGridSearchType.Target)
                    {
                        planGoalGridHelper = planGoalGridHelper.Where(x => x.Target.ToLower().Contains(item.SearchValue.ToLower())).ToList();
                    }
                    if (item.SearchKey == (int)PlanningGridSearchType.TargetStatus)
                    {
                        planGoalGridHelper = planGoalGridHelper.Where(x => x.TargetStatus.ToLower().Contains(item.SearchValue.ToLower())).ToList();
                    }
                    if (item.SearchKey == (int)PlanningGridSearchType.unTarget)
                    {
                        planGoalGridHelper = planGoalGridHelper.Where(x => Regex.Replace(x.TargetSustainbilityTag.ToLower(), "<.*?>", String.Empty).Contains(item.SearchValue.ToLower())).ToList();
                    }
                    if (item.SearchKey == (int)PlanningGridSearchType.targetTag)
                    {
                        planGoalGridHelper = planGoalGridHelper.Where(x => Regex.Replace(x.TargetsActionTag.ToLower(), "<.*?>", String.Empty).Contains(item.SearchValue.ToLower())).ToList();
                    }
                }
            }

            var distinctGoalsPostSearch = planGoalGridHelper.Select(x => x.GoalId).Distinct().ToList();
            var finalSet = GetFinalSet(planGoalGridHelper, distinctGoalsPostSearch, isWebPublish);
            if (finalSet.Any())
            {
                finalSet.ForEach(x =>
                {
                    x.UngoalTargetList = ungoal;
                    x.UnTargetList = unTarget;
                });// used in publish to filter on UN goal target
            }
            return finalSet;
        }

        private List<PlanGoalGridHelper> GetFinalSet(List<PlanGoalGridHelper> planGoalGridHelper, List<string> distinctGoalsPostSearch, bool isWebpublish = false)
        {
            var finalSet = new List<PlanGoalGridHelper>();
            foreach (var item in distinctGoalsPostSearch)
            {
                var targetsLinkedtoGoal = planGoalGridHelper.Where(x => x.GoalId == item);
                if (targetsLinkedtoGoal.Any())
                {
                    int index = 0;
                    foreach (var target in targetsLinkedtoGoal)
                    {
                        var goalTargetData = planGoalGridHelper.FirstOrDefault(x => x.GoalId == item && x.TargetId == target.TargetId.ToString());
                        finalSet.Add(new PlanGoalGridHelper
                        {
                            UniqueId = Guid.NewGuid().ToString(),
                            GoalId = goalTargetData.GoalId.ToString(),
                            Goal = index > 0 ? string.Empty : goalTargetData.Goal,
                            GoalDescription = index > 0 ? string.Empty : goalTargetData.GoalDescription,
                            GoalStatusDescription = index > 0 ? string.Empty : goalTargetData.GoalStatusDescription,
                            GoalStatus = index > 0 ? string.Empty : goalTargetData.GoalStatus,
                            GoalsActionTag = index > 0 ? string.Empty : goalTargetData.GoalsActionTag,
                            FocusAreaTag = index > 0 ? string.Empty : goalTargetData.FocusAreaTag,
                            FocusAreaId = goalTargetData.FocusAreaId,
                            FocusAreaSortOrder = goalTargetData.FocusAreaSortOrder,
                            GoalSustainbilityTag = GetSustainbilityTag(index, goalTargetData, isWebpublish),
                            Target = target.Target,
                            TargetId = target.TargetId.ToString(),
                            TargetDescription = target.TargetDescription,
                            TargetStatus = target.TargetStatus,
                            TargetStatusDescription = target.TargetStatusDescription,
                            TargetsActionTag = target.TargetsActionTag,
                            TargetSortOrder = target.TargetSortOrder,
                            IsLastTarget = goalTargetData.IsLastTarget,
                            DisplayTargetDeleteIcon = goalTargetData.DisplayTargetDeleteIcon,
                            DisplayGoalDeleteIcon = goalTargetData.DisplayGoalDeleteIcon,
                            TargetSustainbilityTag = target.TargetSustainbilityTag,
                            IsMainOwnerGoal = goalTargetData.IsMainOwnerGoal,
                            IsMainOwnerTarget = goalTargetData.IsMainOwnerTarget,
                            MasterPlan = index > 0 ? string.Empty : goalTargetData.MasterPlan,
                            DisplayGoalStatusImage = index > 0 ? false : goalTargetData.DisplayGoalStatusImage,
                            DisplayTargetStatusImage = target.DisplayTargetStatusImage
                        });
                        index++;
                    }
                }
            }
            return finalSet;
        }

        private string GetSustainbilityTag(int index, PlanGoalGridHelper goalTargetData, bool isWebpublish = false)
        {
            if (isWebpublish) return goalTargetData.GoalSustainbilityTag;
            return index > 0 ? string.Empty : goalTargetData.GoalSustainbilityTag;
        }

        private bool isMainOwner(Guid goalMasterPlanId, Guid planId)
        {
            if (goalMasterPlanId == Guid.Empty)
            {
                return true;
            }
            else
            {
                return goalMasterPlanId == planId;
            }
        }

        private async Task AddUpdateGoalStrategyData(string userId, PlanGoalHelper planGoalData, Guid goalId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var goalStrategyData = await _unitOfWork.PlanningRepository.GetStrategyDataforGoal(userDetails.tenant_id, goalId);
            foreach (var strategy in goalStrategyData)
                _unitOfWork.GenericRepo.Delete(strategy);

            if (planGoalData.StrategyId.Any())
            {
                foreach (var item in planGoalData.StrategyId)
                {
                    _unitOfWork.GenericRepo.Add(new tpl_strategy_goal
                    {
                        fk_tenant_id = userDetails.tenant_id,
                        fk_plan_strategy_id = int.Parse(item),
                        fk_plan_goal_id = goalId,
                        updated_by = userDetails.pk_id,
                        updated = DateTime.UtcNow
                    });
                }
            }
            await _unitOfWork.CompleteAsync();
        }

        private Guid GetMasterPlanIDforGoal(tpl_goal masterGoalData)
        {
            return masterGoalData.fk_masterplan_id == null ? Guid.Empty : masterGoalData.fk_masterplan_id.Value;
        }

        private Guid GetMasterPlanIDforTarget(tpl_target targetData)
        {
            return targetData.fk_masterplan_id == null ? Guid.Empty : targetData.fk_masterplan_id.Value;
        }

        private Guid GetFormattedMasterPlanID(Guid? masterPlanId)
        {
            return masterPlanId == null ? Guid.Empty : masterPlanId.Value;
        }

        private async Task UpdateLinkedTargetMasterPlanID(string userid, Guid masterGoalId, Guid masterPlanId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userid);
            var mastertargetData = (await _unitOfWork.PlanningRepository.GetMasterTargetsBasedonMasterGoalID(userDetails.tenant_id, masterGoalId)).ToList();
            foreach (var item in mastertargetData)
            {
                item.fk_masterplan_id = masterPlanId;
                item.updated = DateTime.UtcNow;
                item.updated_by = userDetails.pk_id;
            }
            await _unitOfWork.CompleteAsync();
        }

        // We have added optional parameter returnPlainText which compares the plan reference text with plan name (!!!! full name not short name)
        // If it is same, then we can show only plan short name as tag or else both as tag (short name and plan reference text)

        private string GetMasterPlanName(List<tpl_plan> list, Guid mPlanId, bool returnPlainText = false)
        {
            if (mPlanId != Guid.Empty)
            {
                string planName = string.Empty;
                if (list != null)
                {
                    var plan = list.FirstOrDefault(x => x.pk_plan_id == mPlanId);
                    if (plan != null && !string.IsNullOrEmpty(plan.short_name))
                    {
                        return returnPlainText ? plan.name :
                        $"<span class='theme-plan-tags' title='{plan.short_name}'><img src='../images/plan_tag-01.svg' class='tags-img' alt='Plan tag'/>{plan.short_name}</span>";
                    }
                    return planName;
                }
                return string.Empty;
            }
            return string.Empty;
        }

        private string GetPlanReferenceText(tpl_plan_goal goalDataforPlanandNode, string masterPlanName)
        {
            if (!string.IsNullOrEmpty(masterPlanName))
            {
                return masterPlanName;
            }
            else
            {
                return goalDataforPlanandNode != null ? goalDataforPlanandNode.plan_reference_text : string.Empty;
            }
        }

        private string GetGoalTargetFinstatusText(BudgetProposalPlanMappingHelper goalmappingObject, BudgetProposalPlanMappingHelper targetmappingObject,
                                            string finstatusText, string notIncludedText, string includedText, bool isGoalGrid, string busplanText, int tenantId)
        {
            if (isGoalGrid)
            {
                if (goalmappingObject != null && goalmappingObject.IsTransferredtoFinplan)
                {
                    var goalData = _unitOfWork.PlanningRepository
                        .GetPlanProcessedGoals(tenantId, goalmappingObject.GoalId).GetAwaiter().GetResult();
                    if (goalData != null && goalData.is_busplan_goal)
                    {
                        return $"{busplanText} {includedText}";
                    }
                    return $"{finstatusText} {includedText}";
                }
                else
                    return $"{finstatusText} {notIncludedText}";
            }
            else
            {
                if (targetmappingObject != null && targetmappingObject.IsTransferredtoFinplan)
                {
                    var targetData = _unitOfWork.PlanningRepository
                        .GetPlanProcessedTarget(tenantId, targetmappingObject.TargetId).GetAwaiter().GetResult();
                    if (targetData != null && targetData.is_busplan_target)
                    {
                        return $"{busplanText} {includedText}";
                    }
                    return $"{finstatusText} {includedText}";
                }
                else
                    return $"{finstatusText} {notIncludedText}";
            }
        }

        private async Task UpdatePlanTemplatesColorID(string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var templates = await _unitOfWork.PlanningRepository.GetPlanTemplates(userId, userDetails.tenant_id);
            var planTypeColor = await _unitOfWork.PlanningRepository.GetPlanTypeColors();
            int planTemplateCssColors = planTypeColor.Where(x => x.is_active_type.HasValue && x.is_active_type.Value).Count();
            int index = 1;
            foreach (var item in templates.Where(x => x.IsActive))
            {
                if (index > planTemplateCssColors)
                {
                    index = 1;
                }
                item.ColorId = item.IsActive ? index++ : planTypeColor.FirstOrDefault(x => !x.is_active_type.Value).pk_color_id;
                _unitOfWork.GenericRepo.Update(item);
            }

            await _unitOfWork.CompleteAsync();
        }

        private async Task UploadPlanDocuments(PlanDataHelper inputData, UserData userDetails, Guid planId)
        {
            String FileExt = Path.GetExtension(inputData.mainDocName).ToUpper();
            var planDocs = (await _unitOfWork.PlanningRepository.GetPlanDocuments(planId, userDetails.tenant_id)).FirstOrDefault(x => x.is_main_doc);

            if (FileExt == ".PDF" && planDocs == null)
            {
                Guid pkId = Guid.NewGuid();
                tpl_plan_documents planDocument = new tpl_plan_documents
                {
                    pk_id = pkId,
                    fk_plan_id = planId,
                    doc_name = inputData.mainDocName,
                    is_main_doc = true,
                    updated = DateTime.UtcNow,
                    updated_by = userDetails.pk_id,
                    fk_tenant_id = userDetails.tenant_id
                };
                _unitOfWork.GenericRepo.Add(planDocument);
                string blobPath = $"{userDetails.tenant_id}/{PlanPublishConstants.planText}/{planId}/{inputData.mainDocName}";
                await _blobHelper.UploadFromStreamAsync(StorageAccount.AppStorage, BlobContainers.Planning, blobPath, inputData.mainDocument);
            }
        }

        private void UpdateNodeNumber(IEnumerable<PlanningTree> currentLevel, List<int> levelInfo, string prefix = null)
        {
            int index = 0;
            foreach (var node in currentLevel)
            {
                prefix = GetPrefixValue(prefix, node, levelInfo, index);
                node.NodeNumber = prefix;
                if (node.Items != null && node.Items.Any())
                {
                    var newLevelData = GetItems(node.Items.Count());
                    UpdateNodeNumber(node.Items, newLevelData, prefix);
                }
                index++;
            }
        }

        private string GetPrefixValue(string prefix, PlanningTree node, List<int> levelInfo, int index)
        {
            // Set Prefix to null when node level number is 0 (i.e., Main Chapter)
            if (node.LevelNumber == 0) prefix = null;

            // If Prefix Length Exceeds node level number then we have to remove last item to make sure that the node gets the correct number
            if (!string.IsNullOrEmpty(prefix) && prefix.Split('.').Count() > node.LevelNumber)
            {
                var newList = prefix.Split('.').ToList();
                int length = newList.Count;
                newList.RemoveAt(length - 1);
                prefix = string.Join(".", newList);
            }
            return !string.IsNullOrEmpty(prefix) ? $"{prefix}.{levelInfo[index]}" : $"{levelInfo[index]}";
        }

        private List<int> GetItems(int count)
        {
            List<int> x = new List<int>();
            int i = 1;
            do
            {
                x.Add(i);
                i++;
            } while (i <= count);
            return x;
        }

        private IEnumerable<PlanGridHelper> GetPlanDashboardAdminRoles(UserData userDetails, IEnumerable<tpl_plan> plans, IEnumerable<tco_progress_status> planStatuses, List<KeyValuePair> planUsersData)
        {
            List<PlanGridHelper> gridData = new List<PlanGridHelper>();
            Dictionary<string, clsLanguageString> langStrings = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "Planning");
            string settingsText = langStrings.FirstOrDefault(x => x.Key.Equals("PM_Settings_Text")).Value.LangText;
            string structureText = langStrings.FirstOrDefault(x => x.Key.Equals("PM_Structure_Text")).Value.LangText;
            string metaDataText = langStrings.FirstOrDefault(x => x.Key.Equals("Planning_Tab_MetaData")).Value.LangText;
            string deletePlanText = langStrings.FirstOrDefault(x => x.Key.Equals("Planning_Tab_Delete_Plan")).Value.LangText;

            gridData.AddRange(plans.Select(x => new PlanGridHelper
            {
                PlanId = x.pk_plan_id,
                PlanName = x.name,
                Structure = structureText,
                Settings = settingsText,
                Status = planStatuses.FirstOrDefault(z => z.status_id == x.fk_status_id) != null ? planStatuses.FirstOrDefault(z => z.status_id == x.fk_status_id).status_description : String.Empty,
                Responsible = planUsersData.FirstOrDefault(z => z.key == x.created_by.ToString()) != null ?
                                                planUsersData.FirstOrDefault(z => z.key == x.created_by.ToString()).value : string.Empty,
                PlanTemplateId = x.fk_plan_type_id,
                Role = PlanRole.WRITE.ToString(),
                IsOldPlan = x.is_old_plan,
                StartYear = x.start_year,
                MetaData = metaDataText,
                IsSoftDelete = deletePlanText
            }));

            return gridData;
        }

        public async Task<IEnumerable<PlanGridHelper>> GetPlanDashboardAdminRolesAsync(UserData userDetails, IEnumerable<tpl_plan> plans, IEnumerable<tco_progress_status> planStatuses, List<KeyValuePair> planUsersData)
        {
            List<PlanGridHelper> gridData = new List<PlanGridHelper>();
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Planning");

            string settingsText = langStrings.FirstOrDefault(x => x.Key.Equals("PM_Settings_Text")).Value.LangText;
            string structureText = langStrings.FirstOrDefault(x => x.Key.Equals("PM_Structure_Text")).Value.LangText;
            string metaDataText = langStrings.FirstOrDefault(x => x.Key.Equals("Planning_Tab_MetaData")).Value.LangText;
            string deletePlanText = langStrings.FirstOrDefault(x => x.Key.Equals("Planning_Tab_Delete_Plan")).Value.LangText;

            gridData.AddRange(plans.Select(x => new PlanGridHelper
            {
                PlanId = x.pk_plan_id,
                PlanName = x.name,
                Structure = structureText,
                Settings = settingsText,
                Status = planStatuses.FirstOrDefault(z => z.status_id == x.fk_status_id) != null ? planStatuses.FirstOrDefault(z => z.status_id == x.fk_status_id).status_description : String.Empty,
                Responsible = planUsersData.FirstOrDefault(z => z.key == x.created_by.ToString()) != null ?
                    planUsersData.FirstOrDefault(z => z.key == x.created_by.ToString()).value : string.Empty,
                PlanTemplateId = x.fk_plan_type_id,
                Role = PlanRole.WRITE.ToString(),
                IsOldPlan = x.is_old_plan,
                StartYear = x.start_year,
                MetaData = metaDataText,
                IsSoftDelete = deletePlanText
            }));

            return gridData;
        }

        private IEnumerable<PlanGridHelper> GetPlanDashboard(UserData userDetails, IEnumerable<tco_progress_status> planStatuses, List<KeyValuePair> planUsersData, List<PlanGridDataHelper> planDatafromDB, IEnumerable<tpl_plan_participants> planParticipants)
        {
            List<PlanGridHelper> gridData = new List<PlanGridHelper>();

            // plans with Admin Permission
            gridData.AddRange(GetGridBasedonParticipantType(userDetails, planStatuses, planUsersData, planDatafromDB, PlanRole.ADMIN, planParticipants).ToList());

            // plans with Write Permission
            gridData.AddRange(GetGridBasedonParticipantType(userDetails, planStatuses, planUsersData, planDatafromDB, PlanRole.WRITE, planParticipants).ToList());

            // plans with Read Permission
            gridData.AddRange(GetGridBasedonParticipantType(userDetails, planStatuses, planUsersData, planDatafromDB, PlanRole.READ, planParticipants).ToList());

            return gridData.Select(x => new
            {
                x.PlanId,
                x.PlanName,
                x.Structure,
                x.Settings,
                x.Status,
                x.Responsible,
                x.PlanTemplateId,
                x.Role,
                x.IsOldPlan,
                x.StartYear,
                x.MetaData,
                x.IsSoftDelete
            }).Distinct().Select(x => new PlanGridHelper
            {
                PlanId = x.PlanId,
                PlanName = x.PlanName,
                Structure = x.Structure,
                Settings = x.Settings,
                Status = x.Status,
                Responsible = x.Responsible,
                PlanTemplateId = x.PlanTemplateId,
                Role = x.Role,
                IsOldPlan = x.IsOldPlan,
                StartYear = x.StartYear,
                MetaData = x.MetaData,
                IsSoftDelete = x.IsSoftDelete
            });
        }

        private IEnumerable<PlanGridHelper> GetGridBasedonParticipantType(UserData userDetails,
                                                                         IEnumerable<tco_progress_status> planStatuses,
                                                                         List<KeyValuePair> planUsersData,
                                                                         List<PlanGridDataHelper> planDatafromDB,
                                                                         PlanRole participantType, IEnumerable<tpl_plan_participants> planParticipants)
        {

            return GetGridBasedonParticipantTypeAsync(userDetails, planStatuses, planUsersData, planDatafromDB,
                participantType, planParticipants).GetAwaiter().GetResult();
        }

        public async Task<IEnumerable<PlanGridHelper>> GetGridBasedonParticipantTypeAsync(UserData userDetails,
            IEnumerable<tco_progress_status> planStatuses,
            List<KeyValuePair> planUsersData,
            List<PlanGridDataHelper> planDatafromDB,
            PlanRole participantType, IEnumerable<tpl_plan_participants> planParticipants)
        {
            var isAdmin = await IsPlannedAdminUserAsync(userDetails.user_name);
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Planning");

            string settingsText = langStrings.FirstOrDefault(x => x.Key.Equals("PM_Settings_Text")).Value.LangText;
            string structureText = langStrings.FirstOrDefault(x => x.Key.Equals("PM_Structure_Text")).Value.LangText;
            string metadataText = langStrings.FirstOrDefault(x => x.Key.Equals("Planning_Tab_MetaData")).Value.LangText;
            string deletePlanText = langStrings.FirstOrDefault(x => x.Key.Equals("Planning_Tab_Delete_Plan")).Value.LangText;
            if (participantType == PlanRole.ADMIN)
            {
                planDatafromDB = planDatafromDB.Where(x => x.fk_user_id == userDetails.pk_id && x.is_main_owner).ToList();
            }
            else if (participantType == PlanRole.WRITE)
            {
                planDatafromDB = planDatafromDB.Where(x => x.fk_user_id == userDetails.pk_id && x.write_access).ToList();
            }
            else
            {
                planDatafromDB = planDatafromDB.Where(x => x.fk_user_id == userDetails.pk_id && x.read_access).ToList();
            }
            return planDatafromDB.Select(x => new PlanGridHelper
            {
                PlanId = x.pk_plan_id,
                PlanName = x.name,
                Structure = (isAdmin || x.is_main_owner) ? structureText : string.Empty,
                Settings = (isAdmin || x.is_main_owner) ?  settingsText : string.Empty,
                Status = planStatuses.FirstOrDefault(z => z.status_id == x.fk_status_id) != null ? planStatuses.FirstOrDefault(z => z.status_id == x.fk_status_id).status_description : string.Empty,
                Responsible = planUsersData.FirstOrDefault(z => z.key == x.created_by.ToString()) != null ? planUsersData.FirstOrDefault(z => z.key == x.created_by.ToString()).value : string.Empty,
                PlanTemplateId = x.fk_plan_type_id,
                Role = (isAdmin || x.is_main_owner) ? PlanRole.ADMIN.ToString() : GetPlanRoleTyeForNonAdminRole(planParticipants.Where(z => z.fk_plan_id == x.pk_plan_id).ToList(), userDetails.pk_id),
                IsOldPlan = x.is_old_plan,
                StartYear = x.startYear,
                MetaData = (isAdmin || x.is_main_owner) ? metadataText : string.Empty,
                IsSoftDelete = (isAdmin || x.is_main_owner) ? deletePlanText : null,
            });
        }

        private string GetRoleBasedOnPartcipantType(PlanRole participantType)
        {
            if (participantType == PlanRole.ADMIN)
            {
                return PlanRole.ADMIN.ToString();
            }
            else if (participantType == PlanRole.WRITE)
            {
                return PlanRole.WRITE.ToString();
            }
            return PlanRole.READ.ToString();
        }

        private static List<KeyValuePair> GetFormattedUsersData(IEnumerable<ActiveUsersHelper> activeUsersData)
        {
            return (from a in activeUsersData
                    select new KeyValuePair
                    {
                        key = a.Id.ToString(),
                        value = string.Format("{0} {1}", a.Firstname, a.Lastname)
                    }).ToList();
        }

        private async Task<bool> IsPlannedAdminUserAsync(string userId)
        {
            List<int> plannedAdminRoles = new List<int> { 1, 2, 15 };
            var roleIDs = await _utility.GetUserRoleIdsAsync(userId);
            return roleIDs.Any(x => plannedAdminRoles.Contains(x));
        }

        private bool IsRoleForCopyPlan(string userId)
        {
            List<int> plannedAdminRoles = new List<int> { 1, 15, 24 };
            var roleIDs = _utility.GetUserRoleIds(userId);
            return roleIDs.Any(x => plannedAdminRoles.Contains(x));
        }

        private async Task<bool> IsRoleForCopyPlanAsync(string userId)
        {
            List<int> plannedAdminRoles = new List<int> { 1, 15, 24 };
            var roleIDs = await _utility.GetUserRoleIdsAsync(userId);
            return roleIDs.Any(x => plannedAdminRoles.Contains(x));
        }

        private async Task<bool> IsRole1Or15UserAsync(string userId)
        {
            List<int> plannedAdminRoles = new List<int> { 1, 15 };
            var roleIDs = await _utility.GetUserRoleIdsAsync(userId);
            return roleIDs.Any(x => plannedAdminRoles.Contains(x));
        }

        private async Task<bool> IsRole24UserAsync(string userId)
        {
            var roleIDs = await _utility.GetUserRoleIdsAsync(userId);
            return roleIDs.Any(x => x == 24);
        }

        private async Task<PlanDataHelper> GetPlanDataWithTemplate(string userId, tpl_plan plan, bool isDesignedperLaw, List<tpl_plan_documents> planDocs, bool displayNumber)
        {
            var userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Planning");

            string docUploadedUser = await GetUploadedUser(plan, planDocs, userDetails);
            string moveText = langStrings.FirstOrDefault(x => x.Key.Equals("PM_Move_Node_Text")).Value.LangText;
            string predefined = langStrings.FirstOrDefault(x => x.Key.Equals("PM_MandateText")).Value.LangText;
            string userdefined = langStrings.FirstOrDefault(x => x.Key.Equals("PM_NodeAddedFromPlanning_Text")).Value.LangText;
            var isAdmin = await IsPlannedAdminUserAsync(userId);
            bool showPublishLink = DisplayPublishLink(plan.tpl_plan_participants.ToList(), userDetails.pk_id, isAdmin);
            var templateDetails = plan.tpl_plan_template_details.Where(x => x.status.Value).ToList();
            var source = GetSource(moveText, predefined, userdefined, templateDetails);
            var resultSet = new PlanDataHelper
            {
                planID = plan.pk_plan_id.ToString(),
                planTypeID = plan.fk_plan_type_id.ToString(),
                stateFunds = plan.state_funds.Value,
                planCategoryID = plan.fk_plan_category_id.ToString(),
                PlanStrategyTaskId = plan.fk_strategy_task_id.ToString(),
                planname = plan.name,
                shortname = plan.short_name,
                Status = plan.fk_status_id.Value.ToString(),
                mainOwnerList = plan.tpl_plan_participants.Any() ? plan.tpl_plan_participants.Where(x => x.is_main_owner.HasValue && x.is_main_owner.Value).Select(x => x.fk_user_id.ToString()).ToList() : new List<string>(),
                readList = plan.tpl_plan_participants.Any() ? plan.tpl_plan_participants.Where(x => x.read_access.HasValue && x.read_access.Value).Select(x => x.fk_user_id.ToString()).ToList() : new List<string>(),
                writeList = plan.tpl_plan_participants.Any() ? plan.tpl_plan_participants.Where(x => x.write_access.HasValue && x.write_access.Value).Select(x => x.fk_user_id.ToString()).ToList() : new List<string>(),
                planHelper = new PlanHelper
                {
                    Id = plan.pk_plan_id,
                    Name = plan.name,
                    AllowedContentNodeTypes = JsonConvert.DeserializeObject<List<string>>(plan.contenttypesused)
                                                                        .Select(x => (ContentNodeType)Enum.Parse(typeof(ContentNodeType), x))
                                                                        .ToList(),
                    Usage = (PlanTypeUsage)Enum.Parse(typeof(PlanTypeUsage), plan.usage),
                    TemplateTree = BuildTree(source)
                },
                role = isAdmin ? PlanRole.ADMIN.ToString() : GetPlanRole(plan.tpl_plan_participants.ToList(), userDetails.pk_id),
                DisplayPublishLink = showPublishLink,
                WebPublishText = showPublishLink ? langStrings.FirstOrDefault(x => x.Key.Equals("PM_WebPublish_Text")).Value.LangText : string.Empty,
                PublishTemplateId = plan.publish_template_id,
                IsDesignedPerLaw = isDesignedperLaw,
                isOldPlan = plan.is_old_plan,
                mainDocName = plan.is_old_plan && planDocs.FirstOrDefault(x => x.is_main_doc) != null ? planDocs.FirstOrDefault(x => x.is_main_doc).doc_name : string.Empty,
                mainDocUploadBy = docUploadedUser,
                mainDocUploadDate = plan.is_old_plan && planDocs.FirstOrDefault(x => x.is_main_doc) != null ? $"{planDocs.FirstOrDefault(x => x.is_main_doc).updated.ToLocalTime()}" : string.Empty,
                IsPlanApproved = await IsPlanApproved(userId, plan.fk_status_id.Value),
                orgVersion = await GetSelectedOrgVersionForPlan(plan.pk_plan_id, userDetails.tenant_id),
                TreatedCase = plan.treated_case,
                DateProcessed = (plan.date_processed != null) ? plan.date_processed.ToString() : string.Empty,
                LinkToCaseFile = plan.link_to_casefile,
                ValidFrom = plan.valid_from != 0 ? plan.valid_from.ToString() : string.Empty,
                ValidTo = plan.valid_to != 0 ? plan.valid_to.ToString() : string.Empty,
                PlannedRevision = plan.planned_revision != 0 ? plan.planned_revision.ToString() : string.Empty,
                PlanCategoryType = plan.plan_category_type != null ? plan.plan_category_type : string.Empty
            };
            UpdatePlanningTreeLevelNumber(resultSet.planHelper.TemplateTree, 0, plan.usage.ToString().ToLower());
            resultSet.planHelper.Depth = MaxDepthInListTree(resultSet.planHelper.TemplateTree);
            resultSet.planHelper.SelectedDepth = plan.max_tree_depth;
            if (displayNumber)
            {
                var itemCount = GetItems(resultSet.planHelper.TemplateTree.Count());
                UpdateNodeNumber(resultSet.planHelper.TemplateTree, itemCount);
            }
            return resultSet;
        }

        private int MaxDepthInListTree(IEnumerable<PlanningTree> currentTree)
        {
            if (currentTree == null)
                return 0;

            int maxDepth = 1;
            foreach (var node in currentTree)
            {
                maxDepth = Math.Max(maxDepth, MaxDepth(node));
            }
            return maxDepth;
        }

        private int MaxDepth(PlanningTree currentTree)
        {
            if (currentTree == null)
                return 0;

            int maxDepth = 0;

            foreach (var item in currentTree.Items)
            {
                maxDepth = Math.Max(maxDepth, MaxDepth(item));
            }

            return maxDepth + 1;
        }

        private List<PlanningTree> GetSource(string moveText, string predefined, string userdefined, List<tpl_plan_template_details> templateDetails)
        {
            return templateDetails.Where(x => x.status.Value).OrderBy(z => z.node_order).Select(x => new PlanningTree
            {
                ContentNodeType = (ContentNodeType)Enum.Parse(typeof(ContentNodeType), x.node_type),
                TypeDisplayText = x.node_text,
                MandateText = (x.usage_type == (int)PlanningNodeType.Guideline || x.usage_type == (int)PlanningNodeType.Mandatory) ? predefined : userdefined,
                Name = x.node_name,
                LevelNumber = 0,
                MoveRows = moveText,
                Description = x.node_planned_description ?? string.Empty,
                NodeOrder = x.node_order,
                ParentNodeID = x.parent_node_id.ToString(),
                ChildNodeID = x.node_id.ToString(),
                ContentNodeTypeImage = GetNodeTypeImage((ContentNodeType)Enum.Parse(typeof(ContentNodeType), x.node_type)),
                CanDeleted = x.usage_type != (int)PlanningNodeType.Mandatory,
                IsExistingNode = true
            }).ToList();
        }

        private async Task<string> GetUploadedUser(tpl_plan plan, List<tpl_plan_documents> planDocs, UserData userDetails)
        {
            if (plan.is_old_plan)
            {
                var activeUsersData = await _unitOfWork.PlanningRepository.GetActiveUsers(userDetails.tenant_id);
                if (planDocs.FirstOrDefault(y => y.is_main_doc) != null)
                {
                    int updatedUser = planDocs.FirstOrDefault(y => y.is_main_doc).updated_by;
                    var userData = activeUsersData.FirstOrDefault(x => x.Id == updatedUser);
                    if (userData != null)
                    {
                        return $"{userData.Firstname} {userData.Lastname}";
                    }
                }
            }
            return string.Empty;
        }

        private async Task<PlanDataHelper> GetPlanDataWithoutTemplate(string userId, tpl_plan plan, bool isDesignedperLaw, List<tpl_plan_documents> planDocs)
        {
            var userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Planning");
            var isAdmin = await IsPlannedAdminUserAsync(userId);
            bool showPublishLink = DisplayPublishLink(plan.tpl_plan_participants.ToList(), userDetails.pk_id, isAdmin);
            string docUploadedUser = await GetUploadedUser(plan, planDocs, userDetails);
            return new PlanDataHelper
            {
                planID = plan.pk_plan_id.ToString(),
                planTypeID = plan.fk_plan_type_id.ToString(),
                stateFunds = plan.state_funds.Value,
                planCategoryID = plan.fk_plan_category_id.ToString(),
                PlanStrategyTaskId = plan.fk_strategy_task_id.ToString(),
                planname = plan.name,
                shortname = plan.short_name,
                Status = plan.fk_status_id.Value.ToString(),
                mainOwnerList = plan.tpl_plan_participants.Any() ? plan.tpl_plan_participants.Where(x => x.is_main_owner.HasValue && x.is_main_owner.Value).Select(x => x.fk_user_id.ToString()).ToList() : new List<string>(),
                readList = plan.tpl_plan_participants.Any() ? plan.tpl_plan_participants.Where(x => x.read_access.HasValue && x.read_access.Value).Select(x => x.fk_user_id.ToString()).ToList() : new List<string>(),
                writeList = plan.tpl_plan_participants.Any() ? plan.tpl_plan_participants.Where(x => x.write_access.HasValue && x.write_access.Value).Select(x => x.fk_user_id.ToString()).ToList() : new List<string>(),
                planHelper = new PlanHelper
                {
                    Id = plan.pk_plan_id,
                    Name = plan.name,
                    AllowedContentNodeTypes = JsonConvert.DeserializeObject<List<string>>(plan.contenttypesused)
                                                                                        .Select(x => (ContentNodeType)Enum.Parse(typeof(ContentNodeType), x))
                                                                                        .ToList(),
                    Usage = (PlanTypeUsage)Enum.Parse(typeof(PlanTypeUsage), plan.usage),
                    TemplateTree = new List<PlanningTree>()
                },
                role = isAdmin ? PlanRole.ADMIN.ToString() : GetPlanRole(plan.tpl_plan_participants.ToList(), userDetails.pk_id),
                DisplayPublishLink = showPublishLink,
                WebPublishText = showPublishLink ? langStrings.FirstOrDefault(x => x.Key.Equals("PM_WebPublish_Text")).Value.LangText : string.Empty,
                PublishTemplateId = plan.publish_template_id,
                IsDesignedPerLaw = isDesignedperLaw,
                isOldPlan = plan.is_old_plan,
                mainDocName = plan.is_old_plan && planDocs.FirstOrDefault(x => x.is_main_doc) != null ? planDocs.FirstOrDefault(x => x.is_main_doc).doc_name : string.Empty,
                mainDocUploadBy = docUploadedUser,
                mainDocUploadDate = plan.is_old_plan && planDocs.FirstOrDefault(x => x.is_main_doc) != null ? $"{planDocs.FirstOrDefault(x => x.is_main_doc).updated.ToLocalTime()}" : string.Empty,
                IsPlanApproved = await IsPlanApproved(userId, plan.fk_status_id.Value),
                orgVersion = await GetSelectedOrgVersionForPlan(plan.pk_plan_id, userDetails.tenant_id),
                TreatedCase = plan.treated_case,
                DateProcessed = (plan.date_processed != null) ? plan.date_processed.ToString() : string.Empty,
                LinkToCaseFile = plan.link_to_casefile,
                ValidFrom = plan.valid_from != 0 ? plan.valid_from.ToString() : string.Empty,
                ValidTo = plan.valid_to != 0 ? plan.valid_to.ToString() : string.Empty,
                PlannedRevision = plan.planned_revision != 0 ? plan.planned_revision.ToString() : string.Empty,
                PlanCategoryType = plan.plan_category_type != null ? plan.plan_category_type : string.Empty
            };
        }

        public async Task<bool> IsPlanApproved(string userId, int statusId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            IEnumerable<tco_progress_status> planStatuses = await _unitOfWork.PlanningRepository.GetStatuses(userDetails.tenant_id, "PLAN");
            return planStatuses.Any(x => x.finished_flag && x.status_id == statusId);
        }

        public async Task<Guid> CreateUpdatePlanStrategy(string userId, PlanType template)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            tpl_planstrategy_type strategyTemplate = await _unitOfWork.PlanningRepository.GetPlanStrategyTemplate(userDetails.tenant_id);
            if (strategyTemplate == null)
                return await CreatePlanStrategyType(userId, template);
            else
                return await UpdatePlanStrategyType(userId, template);
        }

        private async Task<Guid> CreatePlanStrategyType(string userId, PlanType template)
        {
            Guid templateId = Guid.NewGuid();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            template.Id = templateId;
            string blobPath = await ValidateAndUploadBlob(template, userDetails, false);
            if (template.AllowedContentNodeTypes == null)
            {
                template.AllowedContentNodeTypes = new List<ContentNodeType>();
            }

            tpl_planstrategy_type planStrageyType = new tpl_planstrategy_type
            {
                pk_id = templateId,
                template_path = blobPath,
                fk_tenant_id = userDetails.tenant_id,
                content_type_used = JsonConvert.SerializeObject(template.AllowedContentNodeTypes),
                updated_by = userDetails.pk_id,
                updated = DateTime.UtcNow
            };
            _unitOfWork.GenericRepo.Add(planStrageyType);
            await _unitOfWork.CompleteAsync();
            return templateId;
        }

        private async Task<Guid> UpdatePlanStrategyType(string userId, PlanType template)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            tpl_planstrategy_type planRow = await _unitOfWork.PlanningRepository.GetPlanStrategyTemplate(userDetails.tenant_id);
            if (template.Id != Guid.Empty)
            {
                template.Id = planRow.pk_id;
                await ValidateAndUploadBlob(template, userDetails, false);
                //Modify Existing plan
                var plansBasedOntemplate = await _unitOfWork.PlanningRepository.GetPlansBasedonTemplateId(userDetails.tenant_id, planRow.pk_id);
                if (plansBasedOntemplate.Any())
                {
                    plansBasedOntemplate.ToList().ForEach(x => x.contenttypesused = JsonConvert.SerializeObject(template.AllowedContentNodeTypes));
                }
                planRow.content_type_used = JsonConvert.SerializeObject(template.AllowedContentNodeTypes);
                planRow.updated_by = userDetails.pk_id;
                planRow.updated = DateTime.UtcNow;
                await _unitOfWork.CompleteAsync();
            }
            return planRow.pk_id;
        }

        public async Task<Guid> GetPlanStrategyTemplate(string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            tpl_planstrategy_type strategyTemplate = await _unitOfWork.PlanningRepository.GetPlanStrategyTemplate(userDetails.tenant_id);
            if (strategyTemplate == null)
                return await CreatePlanStrategyType(userId, new PlanType { AllowedContentNodeTypes = new List<ContentNodeType>() { ContentNodeType.PlanStrategyTask }, TemplateTree = new List<PlanTemplateNode>() });
            return strategyTemplate.pk_id;
        }

        public async Task<string> GetSelectedOrgVersionForPlan(Guid planId, int tenantId)
        {
            List<string> orgVersionList = _unitOfWork.PlanningRepository.GetOrgVersionConnectedWithPlanId(planId, tenantId);
            List<tco_org_version> orgVersionData = (await _unitOfWork.PlanningRepository.GetOrgVersionDataSource(tenantId)).ToList();
            KeyValuePair defaultOrgVersion = GetDefaultOrgVersion(orgVersionData);
            if (orgVersionData != null)
            {
                if (orgVersionList.Any())
                {
                    if (orgVersionList.Any(x => x.Equals(defaultOrgVersion.key)))
                    {
                        return defaultOrgVersion.key;
                    }
                    else
                    {
                        return orgVersionList.FirstOrDefault(x => x != defaultOrgVersion.key);
                    }
                }
                else
                {
                    if (defaultOrgVersion != null)
                    {
                        return defaultOrgVersion.key;
                    }
                    else
                    {
                        return string.Empty;
                    }
                }
            }
            else
            {
                return string.Empty;
            }
        }

        public async Task<string> DeleteCustomMetadataConfig(string userId, Guid fkMetadataId, Guid pkMetadataTextId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            tpl_metadata_setup_texts data = await _unitOfWork.PlanningRepository.GetPlanMetadataSetupTextsFromPkId(userDetails.tenant_id, pkMetadataTextId);
            _unitOfWork.GenericRepo.Delete(data);
            await _unitOfWork.CompleteAsync();
            IEnumerable<tpl_metadata_setup_texts> metaDataTextData = (await _unitOfWork.PlanningRepository.GetPlanMetadataSetupTexts(userDetails.tenant_id, fkMetadataId)).Where(x => !x.is_static);
            int count = 2;
            metaDataTextData.ToList().ForEach(x => x.sort_order = count++);
            await _unitOfWork.CompleteAsync();
            return clsConstants.OperationStatus.Success.ToString();
        }

        public async Task SavePlanTypesSortOrder(string userId, List<string> planTypeIds)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var planTemplates = await _unitOfWork.PlanningRepository.GetPlanTemplates(userId, userDetails.tenant_id);
            foreach (var plan in planTypeIds)
            {
                var planDetails = planTemplates.FirstOrDefault(x => x.Id.ToString() == plan);
                planDetails.SortOrder = planTypeIds.IndexOf(plan) + 1;
                _unitOfWork.GenericRepo.Update(planDetails);
            }
            planTemplates.Where(x => !x.IsActive).ToList().ForEach(z => z.SortOrder = 0);
            await _unitOfWork.CompleteAsync();
        }

        public async Task<List<AdminMyPlanDataHelper>> GetAdminMyPlanData(string userId, AdminPlansGridFilterHelper serachFilter)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            List<AdminMyPlanDataHelper> data = await _unitOfWork.PlanningRepository.GetMyPlanningDataRepo(userDetails.tenant_id);
            data = ApplyAdminPlanGridFilter(serachFilter, data);
            return data;
        }

        public async Task<List<AdminPlanningTaskDataHelper>> GetAdminPlanningTaskData(string userId, int periodFrom, AdminPlansAndTaskGridFilterHelper serachFilter)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            List<AdminPlanningTaskDataHelper> plannigTaskDataList = new List<AdminPlanningTaskDataHelper>();
            var versionsAsync = _unitOfWork.PlanningRepository.GetStrategyTaskVersion(userDetails.tenant_id, "PLANING_STRATEGY_VERSION");
            var statusesAsync = _unitOfWork.PlanningRepository.GetStatuses(userDetails.tenant_id, "PLAN");
            var planStratTaskDataAsync = _unitOfWork.PlanningRepository.GetPlanningTaskDataRepo(userDetails.tenant_id);
            var myPlanListAsync = _unitOfWork.PlanningRepository.GetPublicMyPlans(userDetails.tenant_id);
            var planTypeListAsync = _unitOfWork.PlanningRepository.GetPlanTemplates(userId, userDetails.tenant_id);

            await Task.WhenAll(versionsAsync, statusesAsync, planStratTaskDataAsync, myPlanListAsync, planTypeListAsync);

            var versions = versionsAsync.Result;
            var statuses = statusesAsync.Result;
            var planStratTaskDataList = planStratTaskDataAsync.Result;
            var myPlanList = myPlanListAsync.Result;
            var planTypeList = planTypeListAsync.Result;

            // planning strategy connected with plan
            var stratTaskConnectedPlan = (from a in planStratTaskDataList
                                          join b in myPlanList on a.PlanningTaskId equals b.fk_strategy_task_id
                                          select new AdminPlanningTaskDataHelper
                                          {
                                              PlanningTaskId = a.PlanningTaskId,
                                              PlanId = b.pk_plan_id,
                                              PlanTypeId = a.PlanTypeId,
                                              PlanningTaskName = a.PlanningTaskName,
                                              PlanName = b.name,
                                              PlanTypeName = planTypeList.FirstOrDefault(x => x.Id == a.PlanTypeId) != null ? planTypeList.FirstOrDefault(x => x.Id == a.PlanTypeId).Name : "-",
                                              PlanTypeOrder = planTypeList.FirstOrDefault(x => x.Id == a.PlanTypeId) != null ? planTypeList.FirstOrDefault(x => x.Id == a.PlanTypeId).SortOrder : 99999,
                                              ApprovedDate = b.date_processed == null ? "-" : b.date_processed.Value.ToString("dd.MM.yyyy"),
                                              ApprovedRef = !string.IsNullOrEmpty(b.treated_case) ? b.treated_case : "-",
                                              ValidFrom = b.valid_from != 0 ? b.valid_from.ToString() : "-",
                                              ValidTo = b.valid_to != 0 ? b.valid_to.ToString() : "-",
                                              Progress_statusId = b.fk_status_id,
                                              Progress_Status = b.fk_status_id.HasValue && statuses.FirstOrDefault(z => z.status_id == b.fk_status_id.Value) != null ?
                                                                  statuses.FirstOrDefault(z => z.status_id == b.fk_status_id.Value).status_description : "-",
                                              VersionId = a.VersionId,
                                              VersionName = a.VersionId != Guid.Empty && versions.FirstOrDefault(x => x.pk_cat_id == a.VersionId) != null ?
                                                                  versions.FirstOrDefault(x => x.pk_cat_id == a.VersionId).description : "-",
                                              IsDocIncluded = a.IsDocIncluded,
                                              Progress_public_statusId = b.fk_status_id.HasValue ? statuses.FirstOrDefault(z => z.status_id == b.fk_status_id.Value).fk_public_status_id : null,
                                              StartYear = a.StartYear == 0 ? "-" : a.StartYear.ToString(),
                                              EndYear = a.EndYear == 0 ? "-" : a.EndYear.ToString()
                                          }).ToList();

            // planning strategy not connnected with plan
            var stratTaskWithoutPlan = (from a in planStratTaskDataList
                                        join b in myPlanList on a.PlanningTaskId equals b.fk_strategy_task_id into grp
                                        from grp1 in grp.DefaultIfEmpty()
                                        where a.PeriodFrom == periodFrom && grp1 == null
                                        select new AdminPlanningTaskDataHelper
                                        {
                                            PlanningTaskId = a.PlanningTaskId,
                                            PlanId = Guid.Empty,
                                            PlanTypeId = a.PlanTypeId,
                                            PlanningTaskName = a.PlanningTaskName,
                                            PlanName = "-",
                                            PlanTypeName = planTypeList.FirstOrDefault(x => x.Id == a.PlanTypeId) != null ? planTypeList.FirstOrDefault(x => x.Id == a.PlanTypeId).Name : "-",
                                            PlanTypeOrder = planTypeList.FirstOrDefault(x => x.Id == a.PlanTypeId) != null ? planTypeList.FirstOrDefault(x => x.Id == a.PlanTypeId).SortOrder : 99999,
                                            ApprovedDate = "-",
                                            ApprovedRef = "-",
                                            ValidFrom = "-",
                                            ValidTo = "-",
                                            Progress_statusId = 0,
                                            Progress_Status = "-",
                                            VersionId = a.VersionId,
                                            VersionName = a.VersionId != Guid.Empty && versions.FirstOrDefault(x => x.pk_cat_id == a.VersionId) != null ?
                                                                    versions.FirstOrDefault(x => x.pk_cat_id == a.VersionId).description : "-",
                                            IsDocIncluded = a.IsDocIncluded,
                                            EndYear = a.EndYear == 0 ? "-" : a.EndYear.ToString(),
                                            StartYear = a.StartYear == 0 ? "-" : a.StartYear.ToString()
                                        }).ToList();

            // my plan data not connnected with planning strategy task
            var myPlanWithoutStratTask = (from a in myPlanList
                                          where a.fk_strategy_task_id == Guid.Empty
                                          select new AdminPlanningTaskDataHelper
                                          {
                                              PlanningTaskId = Guid.Empty,
                                              PlanId = a.pk_plan_id,
                                              PlanTypeId = a.fk_plan_type_id,
                                              PlanningTaskName = "-",
                                              PlanName = a.name,
                                              PlanTypeName = planTypeList.FirstOrDefault(x => x.Id == a.fk_plan_type_id) != null ? planTypeList.FirstOrDefault(x => x.Id == a.fk_plan_type_id).Name : "-",
                                              PlanTypeOrder = planTypeList.FirstOrDefault(x => x.Id == a.fk_plan_type_id) != null ? planTypeList.FirstOrDefault(x => x.Id == a.fk_plan_type_id).SortOrder : 99999,
                                              ApprovedDate = a.date_processed != null ? a.date_processed.Value.ToString("dd.MM.yyyy") : "-",
                                              ApprovedRef = string.IsNullOrEmpty(a.treated_case) ? "-" : a.treated_case,
                                              ValidFrom = a.valid_from != 0 ? a.valid_from.ToString() : "-",
                                              ValidTo = a.valid_to != 0 ? a.valid_to.ToString() : "-",
                                              Progress_statusId = a.fk_status_id,
                                              Progress_Status = a.fk_status_id.HasValue && statuses.FirstOrDefault(z => z.status_id == a.fk_status_id.Value) != null ?
                                                                  statuses.FirstOrDefault(z => z.status_id == a.fk_status_id.Value).status_description : "-",
                                              VersionId = Guid.Empty,
                                              VersionName = "-",
                                              IsDocIncluded = a.is_included_in_fp_doc,
                                              Progress_public_statusId = a.fk_status_id.HasValue ? statuses.FirstOrDefault(z => z.status_id == a.fk_status_id.Value).fk_public_status_id : null,
                                              StartYear = "-",
                                              EndYear = "-",
                                          }).ToList();

            plannigTaskDataList.AddRange(stratTaskConnectedPlan);
            plannigTaskDataList.AddRange(stratTaskWithoutPlan);
            plannigTaskDataList.AddRange(myPlanWithoutStratTask);

            plannigTaskDataList = ApplyAdminPlaAndTasknGridFilter(serachFilter, plannigTaskDataList);
            plannigTaskDataList = plannigTaskDataList.OrderBy(x => x.PlanTypeOrder).ThenBy(x => x.PlanName).ToList();

            return plannigTaskDataList;
        }

        public async Task SaveIncludePlanTaskData(string userId, List<AdminPlanningTaskDataHelper> inputObj)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var updatePlanTaskData = inputObj.Where(x => x.PlanningTaskId != Guid.Empty).ToList();
            var updatedPlabData = inputObj.Where(x => x.PlanId != Guid.Empty).ToList();

            if (updatePlanTaskData.Any())
            {
                var stratTaskIds = updatePlanTaskData.Select(z => z.PlanningTaskId).ToList();
                List<tpl_planningstrategy_tasks> planTaskRecords = await _unitOfWork.PlanningRepository.GetPlanningTaskRecords(userDetails.tenant_id, stratTaskIds);
                foreach (var item in planTaskRecords)
                {
                    item.is_included_in_fp_doc = updatePlanTaskData.FirstOrDefault(x => x.PlanningTaskId == item.pk_strategy_task_id).IsDocIncluded;
                    item.updated = DateTime.UtcNow;
                    item.updated_by = userDetails.pk_id;
                    _unitOfWork.GenericRepo.Update(item);
                }
            }
            if (updatedPlabData.Any())
            {
                List<tpl_plan> myPlanList = await _unitOfWork.PlanningRepository.GetPublicMyPlans(userDetails.tenant_id);
                foreach (var item in updatedPlabData)
                {
                    var updateRec = myPlanList.FirstOrDefault(x => x.pk_plan_id == item.PlanId);
                    if (updateRec != null)
                    {
                        updateRec.is_included_in_fp_doc = item.IsDocIncluded;
                        updateRec.updated = DateTime.UtcNow;
                        updateRec.updated_by = userDetails.pk_id;
                        _unitOfWork.GenericRepo.Update(updateRec);
                    }
                }
            }
            await _unitOfWork.CompleteAsync();
        }
        public async Task SaveIncludeApprovedPlans(string userId, List<AdminPlanningTaskDataHelper> inputObj)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var updatedPlanInfo = inputObj.Where(x => x.PlanId != Guid.Empty).ToList();
            if (updatedPlanInfo.Any())
            {
                List<Guid> planIds = updatedPlanInfo.Select(x => x.PlanId).ToList();
                IEnumerable<tpl_plan> planData = await _unitOfWork.PlanningRepository.GetAllPlans(userDetails.tenant_id, planIds);
                foreach (var item in updatedPlanInfo)
                {
                    var updateRec = planData.FirstOrDefault(x => x.pk_plan_id == item.PlanId);
                    if (updateRec != null)
                    {
                        updateRec.is_included_in_fp_doc = item.IsDocIncluded;
                        updateRec.updated = DateTime.UtcNow;
                        updateRec.updated_by = userDetails.pk_id;
                        _unitOfWork.GenericRepo.Update(updateRec);
                    }
                }
                await _unitOfWork.CompleteAsync();
            }
        }

        public async Task SavePlanAdminSelectedPeriod(string userId, int selectedPeriod)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var record = await _unitOfWork.PlanningRepository.GetPlanAdminSelectedPeriodRepo(userDetails.tenant_id, "PLAN_ADMIN_YEAR");
            if (record != null)
            {
                record.updated = DateTime.UtcNow;
                record.updated_by = userDetails.pk_id;
                record.budget_year = selectedPeriod;
                _unitOfWork.GenericRepo.Update(record);
            }
            else
            {
                tco_application_flag newRec = new tco_application_flag()
                {
                    flag_name = "PLAN_ADMIN_YEAR",
                    flag_key_id = userDetails.tenant_id.ToString(),
                    flag_status = 1,
                    fk_tenant_id = userDetails.tenant_id,
                    updated = DateTime.UtcNow,
                    updated_by = userDetails.pk_id,
                    budget_year = selectedPeriod,
                };
                _unitOfWork.GenericRepo.Add(newRec);
            }
            await _unitOfWork.CompleteAsync();
        }

        public async Task<int> GetPlanningAdminSelectedPeriod(int tenantId, string flagName)
        {
            var record = await _unitOfWork.PlanningRepository.GetPlanAdminSelectedPeriodRepo(tenantId, flagName);
            return record != null ? record.budget_year : 0;
        }

        public async Task<IEnumerable<tco_public_status>> GetPublicStatusesAsync(int tenantId, string type)
        {
            return await _unitOfWork.PlanningRepository.GetPublicStatusesRepoAsync(tenantId, type);
        }
        public async Task<int> GetGoalAccessLimitInfo(int tenantId, string planId)
        {
            var planInfo = await _unitOfWork.PlanningRepository.GetPlanInfo(tenantId, planId);
            var planTypes = await _unitOfWork.PlanningRepository.GetPlanTemplateData(tenantId, planInfo.fk_plan_type_id);
            return planTypes == null ? 0 : planTypes.limit_goal_access;
        }

        private KeyValuePair GetDefaultOrgVersion(List<tco_org_version> orgVersionData)
        {
            int month = DateTime.Now.Month;
            int year = DateTime.Now.Year;
            int forecastPeriod = year * 100 + month;
            return orgVersionData.Where(x => x.period_from <= forecastPeriod && x.period_to >= forecastPeriod).Select(x => new KeyValuePair { key = x.pk_org_version, value = x.version_name }).FirstOrDefault();
        }

        private List<AdminMyPlanDataHelper> ApplyAdminPlanGridFilter(AdminPlansGridFilterHelper gridFilterHelper, List<AdminMyPlanDataHelper> gridData)
        {
            if (!string.IsNullOrEmpty(gridFilterHelper.PlanName))
                gridData = gridData.Where(x => x.PlanName.ToLower().Contains(gridFilterHelper.PlanName.ToLower())).ToList();
            if (!string.IsNullOrEmpty(gridFilterHelper.PlanTypeName))
                gridData = gridData.Where(x => x.PlanTypeName.ToLower().Contains(gridFilterHelper.PlanTypeName.ToLower())).ToList();

            return gridData;
        }

        private List<AdminPlanningTaskDataHelper> ApplyAdminPlaAndTasknGridFilter(AdminPlansAndTaskGridFilterHelper gridFilterHelper, List<AdminPlanningTaskDataHelper> gridData)
        {
            if (!string.IsNullOrEmpty(gridFilterHelper.PlanName))
                gridData = gridData.Where(x => x.PlanName.ToLower().Contains(gridFilterHelper.PlanName.ToLower())).ToList();
            if (!string.IsNullOrEmpty(gridFilterHelper.PlanTypeName))
                gridData = gridData.Where(x => x.PlanTypeName.ToLower().Contains(gridFilterHelper.PlanTypeName.ToLower())).ToList();
            if (!string.IsNullOrEmpty(gridFilterHelper.PlanningTaskName))
                gridData = gridData.Where(x => x.PlanningTaskName.ToLower().Contains(gridFilterHelper.PlanningTaskName.ToLower())).ToList();
            if (!string.IsNullOrEmpty(gridFilterHelper.StartYear))
                gridData = gridData.Where(x => x.StartYear.ToLower().Contains(gridFilterHelper.StartYear.ToLower())).ToList();
            if (!string.IsNullOrEmpty(gridFilterHelper.EndYear))
                gridData = gridData.Where(x => x.EndYear.ToLower().Contains(gridFilterHelper.EndYear.ToLower())).ToList();

            return gridData;
        }

        private async Task<List<PlanObjectSortHelper>> FetchAssignmentSortOrderAsync(Guid planId, Guid nodeId, int tenantId)
        {
            var resultSet = await _unitOfWork.PlanningRepository.FetchAssignmentSortOrderRepoAsync(planId, nodeId, tenantId);
            bool isAssignSortAvailable = (resultSet.ToList().Count == resultSet.Count(x => x.SortOrder != 0));
            var resultSetFinal = isAssignSortAvailable ? resultSet.Select(x => new PlanObjectSortHelper
            {
                Id = x.Id,
                Name = x.Name,
                SortOrder = x.SortOrder,
            }).OrderBy(x => x.SortOrder) :
            resultSet.OrderBy(x => x.LastUpdated).Select(x => new PlanObjectSortHelper
            {
                Id = x.Id,
                Name = x.Name,
                SortOrder = x.SortOrder,
            });

            return resultSetFinal.Select(x => new
            {
                Id = x.Id,
                Name = x.Name,
                SortOrder = x.SortOrder,
            }).Distinct().Select(x => new PlanObjectSortHelper
            {
                Id = x.Id,
                Name = x.Name,
                SortOrder = x.SortOrder,
            }).ToList();
        }
        #endregion private methods
    }
}