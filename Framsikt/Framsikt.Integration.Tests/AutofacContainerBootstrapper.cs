#pragma warning disable CS8618
#pragma warning disable CS8625
#pragma warning disable CS8602
#pragma warning disable CS8600
#pragma warning disable CS8629
#pragma warning disable CS8604

using Autofac;
using Autofac.Extensions.DependencyInjection;
using Framsikt.BL.Helpers;
using Framsikt.DocExportWorker;
using Microsoft.Extensions.DependencyInjection;

namespace Framsikt.Integration.Tests
{
    public class AutofacContainerBootstrapper
    {
        public static AutofacServiceProvider ConfigureAutofacContainerForQueues(IServiceCollection servicesCollection, UserData ud, int budgetYear, string queueName = "")
        {
            var builder = new ContainerBuilder();
            builder.Populate(servicesCollection);
            if (!string.IsNullOrEmpty(queueName))
                ContainerBootstraper.RegisterTypes(builder, ud, GetBudgetYearsForExport(budgetYear), string.Empty, queueName);
            else
                ContainerBootstraper.RegisterTypes(builder, ud, GetBudgetYearsForExport(budgetYear), string.Empty);

            return new AutofacServiceProvider(builder.Build());
        }

        private static Dictionary<string, int> GetBudgetYearsForExport(int budgetYear)
        {
            Dictionary<string, int> modBudgetYears = new Dictionary<string, int>();
            modBudgetYears.Add("KOSTRA_BUDGET_YEAR", budgetYear - 1);
            modBudgetYears.Add("POPSTAT_BUDGET_YEAR", budgetYear - 1);
            modBudgetYears.Add("BUDGETTASK_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("UTILITY_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("BUDGETASSUMPTION_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("BUDMAN_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("BUDPROP_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("CAB_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("INVESTMENTS_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("FINANCING_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("KPIDATA_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("OPPASSMNT_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("STAFFPLAN_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("YEARLYBUDGET_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("BUDGETREGULATION_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("POPFCAST_POPEXP_GROWTH_STARTYR", budgetYear);
            return modBudgetYears;
        }
    }
}