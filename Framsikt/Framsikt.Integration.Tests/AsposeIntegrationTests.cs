#pragma warning disable CS8618
#pragma warning disable CS8625
#pragma warning disable CS8602
#pragma warning disable CS8600
#pragma warning disable CS8629
#pragma warning disable CS8604
#pragma warning disable CS8620
#pragma warning disable CS8714

using Aspose.Cells;
using Autofac;
using Autofac.Extensions.DependencyInjection;
using Azure.Data.Tables;
using Azure.Identity;
using Azure.Storage.Blobs;
using Framsikt.BL;
using Framsikt.BL.Core.Helpers;
using Framsikt.BL.Helpers;
using Framsikt.BL.Helpers.CkEditorHelpers;
using Framsikt.BL.Repository;
using Framsikt.DocExportWorker;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.FeatureManagement;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Data;

namespace Framsikt.Integration.Tests
{
    public class AsposeIntegrationTests
    {
        private static IServiceCollection servicesCollection;
        private static IFeatureManager _featureManager;
        private static IConfiguration _config;
        private static IAzureStorageAccountClient _azureStorageAccountClient;
        private static IVaultHelper _vaultHelper;

        public static void InitializeServices(IServiceCollection builderservicesCollection, IFeatureManager featureManager, IConfiguration config, IAzureStorageAccountClient azureStorageAccountClient, IVaultHelper vaultHelper)
        {
            servicesCollection = builderservicesCollection;
            _featureManager = featureManager;
            _config = config;
            _azureStorageAccountClient = azureStorageAccountClient;
            _vaultHelper = vaultHelper;
        }

        public static void TestExcelGrouping()
        {
            try
            {
                Aspose.Words.License asposeWordLicense = new Aspose.Words.License();
                asposeWordLicense.SetLicense("Aspose.Words.lic");

                Aspose.Cells.License asposeCellsLicense = new Aspose.Cells.License();
                asposeCellsLicense.SetLicense("Aspose.Cells.lic");

                string dataDir = @"C:\AccountStatement\";
                string id = "5a8f1f51-4fec-4827-914c-3e665e6e79c5";

                using (FileStream fstream = new FileStream(dataDir + $"{id}.xlsx", FileMode.Open))
                {
                    // Opening the Excel file through the file stream
                    Workbook workbook = new Workbook(fstream);

                    // Accessing the first worksheet in the Excel file
                    Worksheet worksheet = workbook.Worksheets[0];

                    //// Grouping first six rows (from 1 to 5) and making them hidden by passing true
                    //worksheet.Cells.GroupRows(10, 13, false);
                    //worksheet.Cells.GroupRows(4, 8, false);

                    //// Grouping first six rows (from 1 to 5) and making them hidden by passing true
                    //worksheet.Cells.GroupRows(3, 9, false);

                    worksheet.Cells.GroupRows(2, 2, true);
                    worksheet.Cells.GroupRows(3, 13, false);
                    worksheet.Cells.GroupRows(10, 13, false);
                    worksheet.Cells.GroupRows(4, 8, false);

                    // Saving the modified Excel file
                    workbook.Save(dataDir + $"TEST OUTPUT - {Guid.NewGuid()}.xlsx");
                }
            }
            catch (Exception e)
            {
                Console.Write(e.ToString());
            }
        }

        public static void TestGetOrgVersion()
        {
            try
            {
                string userName = "<EMAIL>";
                int clientId = 7684;
                int tenantid = 7684;

                IAppDataCache dc = new AppDataCache(true, "", new CacheClientFactory());
                var blobServiceClient = new BlobServiceClient(new Uri(_config.GetValue<string>("azureMainAppStorageAccountUri").Replace("{Service}", "blob")), new DefaultAzureCredential());
                IUtility utilBL = new Utility(dc, new DbContextManagerTests(dc, userName, tenantid, clientId), null, new DocExportSessionManager(clientId, userName), new CKEditorExtensions(_vaultHelper), _featureManager, _azureStorageAccountClient);
                var orgVersion = utilBL.GetOrgVersionSpecificContent(userName, 202309);
            }
            catch (Exception e)
            {
                Console.Write(e.ToString());
            }
        }

        public static void TestGenerateExcelandGrouping()
        {
            try
            {
                //////// Horten
                //string userName = "<EMAIL>";
                //int tenantid = 2097;

                //////// Oslo
                string userName = "<EMAIL>";
                int clientId = 7684;
                int tenantid = 7684;
                int budgetYear = 2024;
                IAppDataCache dc = new AppDataCache(true, "", new CacheClientFactory());
                var blobServiceClient = new BlobServiceClient(new Uri(_config.GetValue<string>("azureMainAppStorageAccountUri").Replace("{Service}", "blob")), new DefaultAzureCredential());
                var tableServiceClient = new TableServiceClient(new Uri(_config.GetValue<string>("azureMainAppStorageAccountUri").Replace("{Service}", "table")), new DefaultAzureCredential());
                IUtility utilBL = new Utility(dc, new DbContextManagerTests(dc, userName, tenantid, clientId), null, new DocExportSessionManager(clientId, userName), new CKEditorExtensions(_vaultHelper), _featureManager, _azureStorageAccountClient);
                UserData ud = utilBL.GetUserDetails(userName);

                string str = @"{""userName"":""<EMAIL>"",""tenantId"":7684,""budgetYear"":2024,""forecastPeriod"":202401,""isPerPeriodGrid"":false,""viewType"":""0"",""orgId"":""OK"",""orgLevel"":1,""jobId"":0,""columnsList"":[{""key"":""Name"",""value"":""Art"",""columnField"":""acL""},{""key"":""trLi"",""value"":""Status"",""columnField"":""trLi""},{""key"":""BudPeriod"",""value"":""Rev. bud. denne periode"",""columnField"":""bPd""},{""key"":""AccPeriod"",""value"":""Regnskap denne periode"",""columnField"":""acP""},{""key"":""DevPeriod"",""value"":""Avvik i kr"",""columnField"":""dv""},{""key"":""DevPeriodPct"",""value"":""Avvik i %"",""columnField"":""dPt""},{""key"":""BudYtd"",""value"":""Rev. bud. hiå."",""columnField"":""bYTD""},{""key"":""AccYtd"",""value"":""Regnskap hiå."",""columnField"":""acYTD""},{""key"":""DevBudgetYtd"",""value"":""Avvik hiå. i kr"",""columnField"":""dBYTD""},{""key"":""DevBudgetYtdPct"",""value"":""Avvik hiå. i %"",""columnField"":""dBPtYTD""},{""key"":""originalBudgetPeriod"",""value"":""Opprinnelig budsjett"",""columnField"":""oB""},{""key"":""budgetChange"",""value"":""Budsjett- endringer"",""columnField"":""bChg""},{""key"":""AnnualBudget"",""value"":""Just. bud."",""columnField"":""aB""},{""key"":""UsagePct"",""value"":""Forbruks %"",""columnField"":""uP""},{""key"":""AccLastYr"",""value"":""Regnskap i fjor"",""columnField"":""acLYr""},{""key"":""AccLastYrYtd"",""value"":""Regnsk. i fjor hiå."",""columnField"":""acLYrYTD""}]}";

                var json = JObject.Parse(str);

                AccountStmtExcelExportInput input = JsonConvert.DeserializeObject<AccountStmtExcelExportInput>(JsonConvert.SerializeObject(json));

                using (var container = AutofacContainerBootstrapper.ConfigureAutofacContainerForQueues(servicesCollection, ud, budgetYear))
                {
                    Aspose.Words.License asposeWordLicense = new Aspose.Words.License();
                    asposeWordLicense.SetLicense("Aspose.Words.lic");

                    Aspose.Cells.License asposeCellsLicense = new Aspose.Cells.License();
                    asposeCellsLicense.SetLicense("Aspose.Cells.lic");

                    var _utility = container.GetRequiredService<IUtility>();

                    var tree = GetFlattenedData(container, tenantid);
                    int index = 1;
                    tree.ForEach(x => x.RowNumber = index++);
                    DataTable data = _utility.ConvertToDataTable(tree);

                    var level3StartInfo = tree.Where(x => x.Level == 3).Select(x => new RowPositionInfo { Id = x.Id, RowNumber = x.RowNumber }).ToList();
                    var level3EndInfo = tree.Where(x => x.Level == 4).Select(x => new RowPositionInfo { Id = x.ParentId, RowNumber = x.RowNumber }).ToList();

                    var level2StartInfo = tree.Where(x => x.Level == 2).Select(x => new RowPositionInfo { Id = x.Id, RowNumber = x.RowNumber }).ToList();
                    var level2EndInfo = tree.Where(x => x.Level == 3).Select(x => new RowPositionInfo { Id = x.ParentId, RowNumber = x.RowNumber }).ToList();

                    var level1Info = tree.Where(x => x.Level == 1).Select(x => new RowPositionInfo { Id = x.Id, RowNumber = x.RowNumber }).ToList();
                    var lastRowlevel1Info = tree.FirstOrDefault(x => x.Level == 9999);

                    List<ReportColumnHelper> columns = new List<ReportColumnHelper>();
                    ColumnInfo columnInfo = new ColumnInfo();
                    columnInfo.Fields.Add("Id");
                    columnInfo.Titles.Add("Id");
                    columnInfo.DataTypes.Add("string");

                    columnInfo.Fields.Add("ParentId");
                    columnInfo.Titles.Add("ParentId");
                    columnInfo.DataTypes.Add("string");

                    columnInfo.Fields.Add("Level");
                    columnInfo.Titles.Add("Level");
                    columnInfo.DataTypes.Add("numeric");

                    columnInfo.Fields.Add("RowNumber");
                    columnInfo.Titles.Add("RowNumber");
                    columnInfo.DataTypes.Add("numeric");

                    //columnInfo.Fields.Add("Name");
                    //columnInfo.Titles.Add("Name");
                    //columnInfo.DataTypes.Add("string");

                    //columnInfo.Fields.Add("BudPeriod");
                    //columnInfo.Titles.Add("BudPeriod");
                    //columnInfo.DataTypes.Add("numeric");

                    //columnInfo.Fields.Add("BudPeriodYtd");
                    //columnInfo.Titles.Add("BudPeriodYtd");
                    //columnInfo.DataTypes.Add("numeric");

                    foreach (var column in input.columnsList)
                    {
                        if (column.Key != "trLi")
                        {
                            columnInfo.Fields.Add(column.Key);
                            columnInfo.Titles.Add(column.Value);
                            if (column.Key == "Name")
                            {
                                columnInfo.DataTypes.Add("string");
                            }
                            else
                            {
                                columnInfo.DataTypes.Add("numeric");
                            }
                        }
                    }

                    for (int i = 0; i < columnInfo.Fields.Count; i++)
                    {
                        ReportColumnHelper c = new ReportColumnHelper
                        {
                            ColName = columnInfo.Fields[i],
                            DisplayName = columnInfo.Titles[i],
                            DataType = columnInfo.DataTypes[i]
                        };
                        columns.Add(c);
                    }

                    Guid fileName = Guid.NewGuid();

                    using (MemoryStream ms = _utility.ExportGridToExcel2(ud.language_preference, data, columns, userName))
                    {
                        ms.Position = 0;
                        using (FileStream file = new FileStream($"D:\\AccountStatement\\{fileName}.xlsx", FileMode.OpenOrCreate, FileAccess.ReadWrite))
                        {
                            ms.WriteTo(file);
                        }
                    }

                    using (FileStream fstream = new FileStream($"D:\\AccountStatement\\{fileName}.xlsx", FileMode.Open))
                    {
                        // Opening the Excel file through the file stream
                        Workbook workbook = new Workbook(fstream);

                        // Accessing the first worksheet in the Excel file
                        Worksheet worksheet = workbook.Worksheets[0];

                        //Collapse Level 3
                        CollapseRows(level3StartInfo, level3EndInfo, worksheet);

                        //Collapse Level 2
                        CollapseRows(level2StartInfo, level2EndInfo, worksheet);

                        //Collapse Parent Row or Level 1
                        CollapseParentRow(level1Info, lastRowlevel1Info, worksheet);

                        worksheet.Cells.DeleteColumn(columnInfo.Fields.Count - 1); // Deleting RowNumber Column
                        worksheet.Cells.DeleteColumn(columnInfo.Fields.Count - 2); // Deleting Level Column
                        worksheet.Cells.DeleteColumn(2); // Deleting ParentId Column
                        worksheet.Cells.DeleteColumn(0); // Deleting ID Column

                        // Saving the modified Excel file
                        workbook.Save($"D:\\AccountStatement\\{tenantid}-{fileName}.xlsx");
                    }
                }
            }
            catch (Exception e)
            {
                Console.Write(e.ToString());
            }
        }

        private static void CollapseParentRow(List<RowPositionInfo> level1Info, AccountStatementFlattenedData? lastRowlevel1Info, Worksheet worksheet)
        {
            for (int i = 0; i < level1Info.Count(); i++)
            {
                var currentItem = level1Info[i];
                RowPositionInfo nextItem = null;
                try
                {
                    nextItem = level1Info[i + 1];
                }
                catch (Exception)
                {
                    if (lastRowlevel1Info != null)
                    {
                        nextItem = new RowPositionInfo { Id = lastRowlevel1Info.Id, RowNumber = lastRowlevel1Info.RowNumber };
                    }
                }
                worksheet.Cells.GroupRows(currentItem.RowNumber + 1, nextItem.RowNumber - 1, true);
            }
        }

        private static void CollapseRows(List<RowPositionInfo> levelStartInfo, List<RowPositionInfo> levelEndInfo, Worksheet worksheet)
        {
            if (levelStartInfo.Count() == levelEndInfo.Count())
            {
                for (int i = 0; i < levelStartInfo.Count(); i++)
                {
                    var currentItem = levelStartInfo[i];
                    RowPositionInfo endInfo = levelEndInfo[i];
                    worksheet.Cells.GroupRows(currentItem.RowNumber + 1, endInfo.RowNumber, false);
                }
            }
            else
            {
                for (int i = 0; i < levelStartInfo.Count(); i++)
                {
                    var currentItem = levelStartInfo[i];
                    RowPositionInfo endInfo = levelEndInfo.LastOrDefault(x => x.Id == currentItem.Id);
                    if (endInfo != null)
                    {
                        worksheet.Cells.GroupRows(currentItem.RowNumber + 1, endInfo.RowNumber, false);
                    }
                }
            }
        }

        private static List<AccountStatementFlattenedData> GetFlattenedData(AutofacServiceProvider container, int tenantId)
        {
            int accountLevel = 4;

            string str = @"{""userName"":""<EMAIL>"",""tenantId"":7684,""budgetYear"":2024,""forecastPeriod"":202401,""isPerPeriodGrid"":false,""viewType"":""0"",""orgId"":""OK"",""orgLevel"":1,""jobId"":0,""columnsList"":[{""key"":""Name"",""value"":""Art"",""columnField"":""acL""},{""key"":""trLi"",""value"":""Status"",""columnField"":""trLi""},{""key"":""BudPeriod"",""value"":""Rev. bud. denne periode"",""columnField"":""bPd""},{""key"":""AccPeriod"",""value"":""Regnskap denne periode"",""columnField"":""acP""},{""key"":""DevPeriod"",""value"":""Avvik i kr"",""columnField"":""dv""},{""key"":""DevPeriodPct"",""value"":""Avvik i %"",""columnField"":""dPt""},{""key"":""BudYtd"",""value"":""Rev. bud. hiå."",""columnField"":""bYTD""},{""key"":""AccYtd"",""value"":""Regnskap hiå."",""columnField"":""acYTD""},{""key"":""DevBudgetYtd"",""value"":""Avvik hiå. i kr"",""columnField"":""dBYTD""},{""key"":""DevBudgetYtdPct"",""value"":""Avvik hiå. i %"",""columnField"":""dBPtYTD""},{""key"":""originalBudgetPeriod"",""value"":""Opprinnelig budsjett"",""columnField"":""oB""},{""key"":""budgetChange"",""value"":""Budsjett- endringer"",""columnField"":""bChg""},{""key"":""AnnualBudget"",""value"":""Just. bud."",""columnField"":""aB""},{""key"":""UsagePct"",""value"":""Forbruks %"",""columnField"":""uP""},{""key"":""AccLastYr"",""value"":""Regnskap i fjor"",""columnField"":""acLYr""},{""key"":""AccLastYrYtd"",""value"":""Regnsk. i fjor hiå."",""columnField"":""acLYrYTD""}]}";

            var json = JObject.Parse(str);

            AccountStmtExcelExportInput input = JsonConvert.DeserializeObject<AccountStmtExcelExportInput>(JsonConvert.SerializeObject(json));
            var _dashBoardWidgets = container.GetRequiredService<IDashBoardWidgets>();
            //AccountStmtExcelExportInput input = new AccountStmtExcelExportInput()
            //{
            //    orgId = "10",
            //    orgLevel = 2,
            //    forecastPeriod = 202312,
            //    budgetYear = 2023,
            //    isPerPeriodGrid = false,
            //    tenantId = 7684,
            //    viewType = "0",
            //    userName = "<EMAIL>",

            //};

            var data2 = _dashBoardWidgets.GetDataForExcelExport(input).GetAwaiter().GetResult();
            var orgDataNew = _dashBoardWidgets.CreateParentChildDataList(data2, input, 4);
            var treeNew = BuildTree(orgDataNew.ToList());
            var flatTreeNew = FlattenTree(treeNew.ToList());

            var totalRowDataNew = new AccountStatementFlattenedData
            {
                Id = "Total",
                ParentId = string.Empty,
                Level = 9999,
                Name = "Total",
                BudPeriod = flatTreeNew.Where(x => x.ParentId == string.Empty).Sum(a => a.BudPeriod),
                BudPeriodYtd = flatTreeNew.Where(x => x.ParentId == string.Empty).Sum(a => a.BudPeriodYtd),
                AccPeriod = flatTreeNew.Where(x => x.ParentId == string.Empty).Sum(a => a.AccPeriod),
                DevPeriod = flatTreeNew.Where(x => x.ParentId == string.Empty).Sum(a => a.BudPeriod) - flatTreeNew.Where(x => x.ParentId == string.Empty).Sum(a => a.AccPeriod),
                DevPeriodPct = flatTreeNew.Where(x => x.ParentId == string.Empty).Sum(a => a.BudPeriod) != 0 ? ((flatTreeNew.Where(x => x.ParentId == string.Empty).Sum(a => a.BudPeriod) - (flatTreeNew.Where(x => x.ParentId == string.Empty).Sum(a => a.AccPeriod))) / (Math.Abs(flatTreeNew.Where(x => x.ParentId == string.Empty).Sum(a => a.BudPeriod)))) : 0,
                AccYtd = flatTreeNew.Where(x => x.ParentId == string.Empty).Sum(a => a.AccYtd),
                DevBudgetYtd = flatTreeNew.Where(x => x.ParentId == string.Empty).Sum(a => a.BudPeriodYtd) - flatTreeNew.Where(x => x.ParentId == string.Empty).Sum(a => a.AccYtd),
                DevBudgetYtdPct = flatTreeNew.Where(x => x.ParentId == string.Empty).Sum(a => a.BudPeriodYtd) != 0 ? ((flatTreeNew.Where(x => x.ParentId == string.Empty).Sum(a => a.BudPeriodYtd)) - (flatTreeNew.Where(x => x.ParentId == string.Empty).Sum(a => a.AccYtd)) / (Math.Abs(flatTreeNew.Where(x => x.ParentId == string.Empty).Sum(a => a.BudPeriodYtd)))) : 0,
                AnnualBudget = flatTreeNew.Where(x => x.ParentId == string.Empty).Sum(a => a.AnnualBudget),
                UsagePct = flatTreeNew.Where(x => x.ParentId == string.Empty).Sum(a => a.AnnualBudget) != 0 ? (flatTreeNew.Where(x => x.ParentId == string.Empty).Sum(a => a.AccYtd) / (Math.Abs(flatTreeNew.Where(x => x.ParentId == string.Empty).Sum(a => a.AnnualBudget)))) : 0,
                budgetChange = flatTreeNew.Where(x => x.ParentId == string.Empty).Sum(a => a.budgetChange),
                AccLastYr = flatTreeNew.Where(x => x.ParentId == string.Empty).Sum(a => a.AccLastYr),
                AccLastYrYtd = flatTreeNew.Where(x => x.ParentId == string.Empty).Sum(a => a.AccLastYrYtd),
                originalBudgetPeriod = flatTreeNew.Where(x => x.ParentId == string.Empty).Sum(a => a.originalBudgetPeriod),
            };

            flatTreeNew.Add(totalRowDataNew);
            //return flatTreeNew;//flatTree;

            var budgetperPeriodData = _dashBoardWidgets.TestBudgetbyPeriodDataCityLevel(tenantId).GetAwaiter().GetResult();
            var serialzedData = JsonConvert.DeserializeObject<List<RawData>>(budgetperPeriodData);

            string emptyString = " ";
            serialzedData = serialzedData.Where(x => x.Period == 202309 && x.Org_id_2 == "10").ToList();
            var orgData = (from a in serialzedData
                           group a by new { a.Org_id_2, a.Org_name_2 } into g1
                           select new AccountStatementTreeData
                           {
                               Id = $"{g1.Key.Org_id_2}-1", // Level 1 (OrgId)
                               ParentId = string.Empty,     // Parent ID Empty
                               Level = 1,
                               Name = $"{g1.Key.Org_id_2} {g1.Key.Org_name_2}",
                               BudPeriod = g1.Sum(x => x.bPd),
                               BudPeriodYtd = g1.Sum(x => x.bYTD)
                           }).ToList();

            var level1Data = (from a in serialzedData
                              group a by new { a.Org_id_2, a.Level_1_id, a.Level_1_description } into g1
                              select new AccountStatementTreeData
                              {
                                  Id = $"{g1.Key.Org_id_2}-{g1.Key.Level_1_id}-2",   // Level 2
                                  ParentId = $"{g1.Key.Org_id_2}-1", // Parent ID Level 1
                                  Level = 2,
                                  Name = $"- {emptyString}{g1.Key.Level_1_id} {g1.Key.Level_1_description}",
                                  BudPeriod = g1.Sum(x => x.bPd),
                                  BudPeriodYtd = g1.Sum(x => x.bYTD),
                              }).ToList();

            var level2Data = (from a in serialzedData
                              group a by new { a.Org_id_2, a.Level_1_id, a.Level_2_id, a.Level_2_description } into g1
                              select new AccountStatementTreeData
                              {
                                  Id = $"{g1.Key.Org_id_2}-{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-3",   // Level 3
                                  ParentId = $"{g1.Key.Org_id_2}-{g1.Key.Level_1_id}-2",     // Parent ID Level 2
                                  Level = 3,
                                  Name = $"- {emptyString}{emptyString}{g1.Key.Level_2_id} {g1.Key.Level_2_description}",
                                  BudPeriod = g1.Sum(x => x.bPd),
                                  BudPeriodYtd = g1.Sum(x => x.bYTD),
                              }).ToList();

            var level3Data = (from a in serialzedData
                              group a by new { a.Org_id_2, a.Level_1_id, a.Level_2_id, a.Level_3_id, a.Level_3_description } into g1
                              select new AccountStatementTreeData
                              {
                                  Id = $"{g1.Key.Org_id_2}-{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-{g1.Key.Level_3_id}-4",   // Level 4
                                  ParentId = $"{g1.Key.Org_id_2}-{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-3",     // Parent ID Level 3
                                  Level = 4,
                                  Name = $"- {emptyString}{emptyString}{emptyString}{g1.Key.Level_3_id} {g1.Key.Level_3_description}",
                                  BudPeriod = g1.Sum(x => x.bPd),
                                  BudPeriodYtd = g1.Sum(x => x.bYTD),
                              }).ToList();

            var level4Data = (from a in serialzedData
                              group a by new { a.Org_id_2, a.Level_1_id, a.Level_2_id, a.Level_3_id, a.Level_4_id, a.Level_4_description } into g1
                              select new AccountStatementTreeData
                              {
                                  Id = $"{g1.Key.Org_id_2}-{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-{g1.Key.Level_3_id}-{g1.Key.Level_4_id}-5",   // Level 5
                                  ParentId = $"{g1.Key.Org_id_2}-{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-{g1.Key.Level_3_id}--4",     // Parent ID Level 4
                                  Level = 5,
                                  Name = $"- {emptyString}{emptyString}{emptyString}{emptyString}{g1.Key.Level_4_id} {g1.Key.Level_4_description}",
                                  BudPeriod = g1.Sum(x => x.bPd),
                                  BudPeriodYtd = g1.Sum(x => x.bYTD),
                              }).ToList();

            if (accountLevel == 3)
            {
                orgData.AddRange(level1Data);
                orgData.AddRange(level2Data);
                orgData.AddRange(level3Data);
            }

            var tree = BuildTree(orgData);
            var flatTree = FlattenTree(tree);

            var totalRowData = new AccountStatementFlattenedData
            {
                Id = "Total",
                ParentId = string.Empty,
                Level = 9999,
                Name = "Total",
                BudPeriod = flatTree.Where(x => x.ParentId == string.Empty).Sum(a => a.BudPeriod),
                BudPeriodYtd = flatTree.Where(x => x.ParentId == string.Empty).Sum(a => a.BudPeriodYtd),
            };

            flatTree.Add(totalRowData);

            return flatTreeNew;//flatTree;
        }

        #region Build and Flatten Tree Services

        private static List<AccountStatementTreeData_excel> BuildTree(List<AccountStatementTreeData_excel> source)
        {
            var groups = source.GroupBy(i => i.Parent);
            var roots = groups.FirstOrDefault(g => g.Key == string.Empty).ToList();
            if (roots.Count > 0)
            {
                var dict = groups.Where(g => g.Key != string.Empty).ToDictionary(g => g.Key, g => g.ToList());
                for (int i = 0; i < roots.Count; i++)
                    AddChildren(roots[i], dict);
            }
            return roots;
        }

        private static void AddChildren(AccountStatementTreeData_excel node, IDictionary<string, List<AccountStatementTreeData_excel>> source)
        {
            if (source.ContainsKey(node.Id))
            {
                node.Items = source[node.Id];
                for (int i = 0; i < node.Items.ToList().Count; i++)
                    AddChildren(node.Items.ToList()[i], source);
            }
            else
            {
                node.Items = new List<AccountStatementTreeData_excel>();
            }
        }

        private static void AddChildren(AccountStatementTreeData node, IDictionary<string, List<AccountStatementTreeData>> source)
        {
            if (source.ContainsKey(node.Id))
            {
                node.Items = source[node.Id];
                for (int i = 0; i < node.Items.ToList().Count; i++)
                    AddChildren(node.Items.ToList()[i], source);
            }
            else
            {
                node.Items = new List<AccountStatementTreeData>();
            }
        }

        private static List<AccountStatementFlattenedData> FlattenTree(List<AccountStatementTreeData_excel> currentLevel)
        {
            List<AccountStatementFlattenedData> flatTree = new List<AccountStatementFlattenedData>();
            foreach (var node in currentLevel)
            {
                flatTree.Add(new AccountStatementFlattenedData
                {
                    Id = node.Id,
                    ParentId = node.Parent,
                    Level = node.Level,
                    RowNumber = 0,
                    Name = node.Name,
                    BudPeriod = node.BudPeriod,
                    BudPeriodYtd = node.BudYtd,
                    AccPeriod = node.AccPeriod,
                    DevPeriod = node.BudPeriod - node.AccPeriod,
                    DevPeriodPct = node.BudPeriod != 0 ? ((node.BudPeriod) - (node.AccPeriod)) / (Math.Abs(node.BudPeriod)) : 0,
                    AccYtd = node.AccYtd,
                    DevBudgetYtd = node.BudYtd - node.AccYtd,
                    DevBudgetYtdPct = node.BudYtd != 0 ? ((node.BudYtd) - (node.AccYtd)) / (Math.Abs(node.BudYtd)) : 0,
                    AnnualBudget = node.AnnualBudget,
                    UsagePct = node.AnnualBudget != 0 ? ((node.AccYtd) / (Math.Abs(node.AnnualBudget))) : 0,
                    budgetChange = node.AnnualBudget - node.originalBudgetPeriod,
                    AccLastYr = node.AccLastYr,
                    AccLastYrYtd = node.AccLastYrYtd,
                    originalBudgetPeriod = node.originalBudgetPeriod,
                });
                if (node.Items != null && node.Items.Any())
                {
                    var children = FlattenTree(node.Items);
                    flatTree.AddRange(children);
                }
            }

            return flatTree;
        }

        private static List<AccountStatementTreeData> BuildTree(List<AccountStatementTreeData> source)
        {
            var groups = source.GroupBy(i => i.ParentId);
            var roots = groups.FirstOrDefault(g => g.Key == string.Empty).ToList();
            if (roots.Count > 0)
            {
                var dict = groups.Where(g => g.Key != string.Empty).ToDictionary(g => g.Key, g => g.ToList());
                for (int i = 0; i < roots.Count; i++)
                    AddChildren(roots[i], dict);
            }
            return roots;
        }

        private static List<AccountStatementFlattenedData> FlattenTree(List<AccountStatementTreeData> currentLevel)
        {
            List<AccountStatementFlattenedData> flatTree = new List<AccountStatementFlattenedData>();
            foreach (var node in currentLevel)
            {
                flatTree.Add(new AccountStatementFlattenedData
                {
                    Id = node.Id,
                    ParentId = node.ParentId,
                    Level = node.Level,
                    RowNumber = 0,
                    Name = node.Name,
                    BudPeriod = node.BudPeriod,
                    BudPeriodYtd = node.BudPeriodYtd,
                });
                if (node.Items != null && node.Items.Any())
                {
                    var children = FlattenTree(node.Items);
                    flatTree.AddRange(children);
                }
            }

            return flatTree;
        }

        #endregion Build and Flatten Tree Services
    }

    public class RowPositionInfo
    {
        public string? Id { get; set; }
        public int RowNumber { get; set; }
    }

    public class AccountStatementTreeData
    {
        public AccountStatementTreeData()
        {
            Items = new List<AccountStatementTreeData>();
        }

        public string? Id { get; set; }
        public string? Name { get; set; }
        public string? ParentId { get; set; }
        public decimal BudPeriod { get; set; }
        public decimal BudPeriodYtd { get; set; }
        public int Level { get; set; }

        public List<AccountStatementTreeData> Items { get; set; }
    }

    public class AccountStatementFlattenedData
    {
        public string? Id { get; set; }
        public string? Name { get; set; }
        public string? ParentId { get; set; }
        public decimal BudPeriod { get; set; }
        public decimal BudPeriodYtd { get; set; }
        public decimal AccPeriod { get; set; }
        public decimal DevPeriod { get; set; }
        public decimal DevPeriodPct { get; set; }
        public decimal AccYtd { get; set; }
        public decimal DevBudgetYtd { get; set; }
        public decimal DevBudgetYtdPct { get; set; }
        public decimal AnnualBudget { get; set; }
        public decimal UsagePct { get; set; }
        public decimal budgetChange { get; set; }

        public decimal AccLastYr { get; set; }
        public decimal AccLastYrYtd { get; set; }
        public decimal originalBudgetPeriod { get; set; }
        public int Level { get; set; }

        public int RowNumber { get; set; }
    }

    public class RawData
    {
        public string Id { get; set; }

        public string ParentID { get; set; }

        public string Name { get; set; }

        public int Period { get; set; }

        public decimal bPd { get; set; }

        public decimal bYTD { get; set; }

        public string Org_id_2 { get; set; } = string.Empty;
        public string Org_name_2 { get; set; } = string.Empty;

        public string Level_1_id { get; set; } = string.Empty;
        public string Level_1_description { get; set; } = string.Empty;

        public string Level_2_id { get; set; } = string.Empty;
        public string Level_2_description { get; set; } = string.Empty;

        public string Level_3_id { get; set; } = string.Empty;
        public string Level_3_description { get; set; } = string.Empty;

        public string Level_4_id { get; set; } = string.Empty;
        public string Level_4_description { get; set; } = string.Empty;
    }
}