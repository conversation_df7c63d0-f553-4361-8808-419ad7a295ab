using Azure.Identity;
using Framsikt.BL.Core.Helpers;
using Framsikt.BL.Helpers;
using Microsoft.Extensions.Azure;
using Microsoft.Extensions.DependencyInjection;

namespace Framsikt.Integration.Tests
{
    public static class AzureClientConfigurationExtension
    {
        public static IServiceCollection AddAzureClientConfiguration(this IServiceCollection services)
        {
            services.AddAzureClients(builder =>
            {
                string azurePublishProdUri = AppConfiguration.GetConfigurationSetting("azurePublishProdUri");
                string azurePublishStageUri = AppConfiguration.GetConfigurationSetting("azurePublishStagUri");
                string azureMainAppStorageAccountUri = AppConfiguration.GetConfigurationSetting("azureMainAppStorageAccountUri");
                string azureMainAppTableStorageAccountUri = AppConfiguration.GetConfigurationSetting("azureMainAppTableStorageAccountUri");
                string azurePublishProdBackupUri = AppConfiguration.GetConfigurationSetting("azurePublishProdBackupUri");
                string azureIntegrationStorageAccountUri = AppConfiguration.GetConfigurationSetting("azureIntegrationStorageAccountUri");
                string azureIntegrationStatusStorageAccountUri = AppConfiguration.GetConfigurationSetting("azureIntegrationStatusStorageAccountUri");

                var blobUri = new Uri(azureMainAppStorageAccountUri.Replace("{Service}", "blob"));

                builder.AddBlobServiceClient(blobUri);
                builder.AddBlobServiceClient(
                    new Uri(azurePublishProdUri.Replace("{Service}", "blob")))
                    .WithName("PublishProd");
                builder.AddBlobServiceClient(
                    new Uri(azurePublishStageUri.Replace("{Service}", "blob")))
                    .WithName("PublishStage");
                builder.AddBlobServiceClient(
                    new Uri(azurePublishProdBackupUri.Replace("{Service}", "blob")))
                    .WithName("PublishProdBackup");

                
                builder.AddTableServiceClient(
                    new Uri(azurePublishProdUri.Replace("{Service}", "table")))
                    .WithName("PublishProd");
                builder.AddTableServiceClient(
                    new Uri(azurePublishStageUri.Replace("{Service}", "table"))
                    ).WithName("PublishStage");

                var isLocal = Environment.GetEnvironmentVariable("IsLocal") ?? "false";

                if (!isLocal.Equals("true", StringComparison.OrdinalIgnoreCase))
                {
                    builder.AddTableServiceClient(
                        new Uri(azureMainAppTableStorageAccountUri.Replace("{Service}", "table")));

                    builder.AddQueueServiceClient(
                        new Uri(azureMainAppStorageAccountUri.Replace("{Service}", "queue")));
                    builder.AddQueueServiceClient(
                        new Uri(azureIntegrationStorageAccountUri.Replace("{Service}", "queue"))
                    ).WithName("Integration");
                    builder.AddQueueServiceClient(
                        new Uri(azureIntegrationStatusStorageAccountUri))
                        .WithName("IntegrationStatus");
                }
                else
                {
                    builder.AddTableServiceClient("UseDevelopmentStorage=true");
                    builder.AddQueueServiceClient("UseDevelopmentStorage=true");
                    builder.AddQueueServiceClient("UseDevelopmentStorage=true")
                        .WithName("Integration");
                    builder.AddQueueServiceClient("UseDevelopmentStorage=true")
                        .WithName("IntegrationStatus");
                }

                DefaultAzureCredential credential = new();
                builder.UseCredential(credential);
            });

            services.AddScoped<IAzureStorageAccountClient, AzureStorageAccountClient>();

            return services;
        }
    }
}
