using Framsikt.BL.Repository;

#pragma warning disable CS8618
#pragma warning disable CS8625
#pragma warning disable CS8602
#pragma warning disable CS8600
#pragma warning disable CS8629
#pragma warning disable CS8604

namespace Framsikt.Integration.Tests
{
    using Framsikt.BL.Core.Helpers;
    using Framsikt.BL.Core.Repository.Contracts;
    using Framsikt.BL.Core.Repository.Services;
    using Framsikt.BL.Helpers;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.Hosting;
    using Microsoft.FeatureManagement;
    using Serilog;
    using System;

    internal class Program
    {
        private static void Main(string[] args)
        {
            var builder = new ConfigurationBuilder();
            BuildConfig(builder);

            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(builder.Build())
                .Enrich.FromLogContext()
                .WriteTo.Console()
                .CreateLogger();

            Log.Logger.Information("Application Starting");

            var host = Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) =>
                {
                    IConfiguration config = context.Configuration;
                    AppConfiguration.Initialize(config);
                    services.AddTransient<IServiceBusProvider, ServiceBusProvider>();
                    services.AddTransient<IVaultHelper, AzureKeyVaultHelper>();
                    services.AddFeatureManagement();
                    services.AddAzureClientConfiguration();
                    using var provider = services.BuildServiceProvider();
                    var featureManager = provider.GetRequiredService<IFeatureManager>();
                    var azureStorageAccountClient = provider.GetRequiredService<IAzureStorageAccountClient>();
                    var vaultHelper = provider.GetRequiredService<IVaultHelper>();
                    BMExportPublishIntegrationTest.InitializeServices(services, featureManager, azureStorageAccountClient, vaultHelper);
                  
                })
                .UseSerilog()
                .Build();

            BMExportPublishIntegrationTest.TestBMDocPublish();
        }

        private static void BuildConfig(IConfigurationBuilder builder)
        {
            builder.SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddEnvironmentVariables();
        }
    }
}