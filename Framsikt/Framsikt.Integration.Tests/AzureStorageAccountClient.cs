using Azure.Data.Tables;
using Azure.Storage.Blobs;
using Azure.Storage.Queues;
using Framsikt.BL.Core.Helpers;
using Microsoft.Extensions.Azure;

namespace Framsikt.Integration.Tests
{
    public class AzureStorageAccountClient : IAzureStorageAccountClient
    {
        private readonly BlobServiceClient _blobServiceAppClient;
        private readonly TableServiceClient _tableServiceAppClient;
        private readonly QueueServiceClient _queueServiceAppClient;
        private readonly IAzureClientFactory<BlobServiceClient> _azureBlobServiceClientFactory;
        private readonly IAzureClientFactory<TableServiceClient> _azureTableServiceClientFactory;
        private readonly IAzureClientFactory<QueueServiceClient> _azureQueueServiceClientFactory;

        public AzureStorageAccountClient(
            BlobServiceClient blobServiceAppClient,
            TableServiceClient tableServiceAppClient,
            QueueServiceClient queueServiceAppClient,
            IAzureClientFactory<BlobServiceClient> azureBlobServiceClientFactory,
            IAzureClientFactory<TableServiceClient> azureTableServiceClientFactory,
            IAzureClientFactory<QueueServiceClient> azureQueueServiceClientFactory)
        {
            _blobServiceAppClient = blobServiceAppClient ?? throw new ArgumentNullException(nameof(blobServiceAppClient));
            _tableServiceAppClient = tableServiceAppClient ?? throw new ArgumentNullException(nameof(tableServiceAppClient));
            _queueServiceAppClient = queueServiceAppClient ?? throw new ArgumentNullException(nameof(queueServiceAppClient));
            _azureBlobServiceClientFactory = azureBlobServiceClientFactory ?? throw new ArgumentNullException(nameof(azureBlobServiceClientFactory));
            _azureTableServiceClientFactory = azureTableServiceClientFactory ?? throw new ArgumentNullException(nameof(azureTableServiceClientFactory));
            _azureQueueServiceClientFactory = azureQueueServiceClientFactory ?? throw new ArgumentNullException(nameof(azureQueueServiceClientFactory));
        }

        public BlobServiceClient CreateBlobServiceAppClient()
        {
            return _blobServiceAppClient;
        }

        public BlobServiceClient CreateBlobServicePublishProdClient()
        {
            return _azureBlobServiceClientFactory.CreateClient("PublishProd");
        }

        public BlobServiceClient CreateBlobServicePublishStageClient()
        {
            return _azureBlobServiceClientFactory.CreateClient("PublishStage");
        }

        public BlobServiceClient CreateBlobServicePublishProdBackupClient()
        {
            return _azureBlobServiceClientFactory.CreateClient("PublishProdBackup");
        }

        public TableServiceClient CreateTableServiceClient()
        {
            return _tableServiceAppClient;
        }

        public TableServiceClient CreateTableServicePublishProdClient()
        {
            return _azureTableServiceClientFactory.CreateClient("PublishProd");
        }

        public TableServiceClient CreateTableServicePublishStageClient()
        {
            return _azureTableServiceClientFactory.CreateClient("PublishStage");
        }

        public QueueServiceClient CreateQueueServiceClient()
        {
            return _queueServiceAppClient;
        }

        public QueueServiceClient CreateQueueServiceIntegrationClient()
        {
            return _azureQueueServiceClientFactory.CreateClient("Integration");
        }

        public QueueServiceClient CreateQueueServiceIntegrationStatusClient()
        {
            return _azureQueueServiceClientFactory.CreateClient("IntegrationStatus");
        }
    }
}
