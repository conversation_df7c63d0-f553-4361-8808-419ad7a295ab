using Framsikt.BL;
using Framsikt.BL.Core.Helpers;
using Framsikt.BL.Helpers;
using Framsikt.BL.Helpers.CkEditorHelpers;
using Framsikt.BL.Plan.Core;
using Framsikt.BL.Repository;
using Framsikt.DocExportWorker;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.FeatureManagement;

namespace Framsikt.Integration.Tests
{
    internal class ViewsIntegrationTests
    {
        private static IServiceCollection servicesCollection;
        private static IFeatureManager _featureManager;
        private static IAzureStorageAccountClient _azureStorageAccountClient;
        private static IVaultHelper _vaultHelper;

        public static void InitializeServices(IServiceCollection builderservicesCollection, IFeatureManager featureManager, IAzureStorageAccountClient azureStorageAccountClient, IVaultHelper vaultHelper)
        {
            servicesCollection = builderservicesCollection;
            _featureManager = featureManager;
            _azureStorageAccountClient = azureStorageAccountClient;
            _vaultHelper = vaultHelper;
        }

        public static async Task TestViews()
        {
            string userId = "<EMAIL>";
            int clientId = 1019;
            int tenantId = 1019;
            int budgetYear = 2025;

            IAppDataCache dc = new AppDataCache(true, "", new CacheClientFactory());
            IUtility utilBL = new Utility(dc, new DbContextManagerTests(dc, userId, tenantId, clientId), null, new DocExportSessionManager(clientId, userId), new CKEditorExtensions(_vaultHelper), _featureManager, _azureStorageAccountClient);
            UserData ud = utilBL.GetUserDetails(userId);

            using (var container = AutofacContainerBootstrapper.ConfigureAutofacContainerForQueues(servicesCollection, ud, budgetYear, ""))
            {
                Aspose.Words.License asposeWordLicense = new Aspose.Words.License();
                asposeWordLicense.SetLicense("Aspose.Words.lic");

                Aspose.Cells.License asposeCellsLicense = new Aspose.Cells.License();
                asposeCellsLicense.SetLicense("Aspose.Cells.lic");

                ITestView _testView = container.GetRequiredService<ITestView>();
                await _testView.TestViews(userId, budgetYear);
            }
        }
    }
}