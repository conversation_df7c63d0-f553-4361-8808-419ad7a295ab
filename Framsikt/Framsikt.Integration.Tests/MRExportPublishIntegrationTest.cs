#pragma warning disable CS8618
#pragma warning disable CS8625
#pragma warning disable CS8602
#pragma warning disable CS8600
#pragma warning disable CS8629
#pragma warning disable CS8604

using Framsikt.BL;
using Framsikt.BL.Core.Helpers;
using Framsikt.BL.Helpers;
using Framsikt.BL.Helpers.CkEditorHelpers;
using Framsikt.BL.Repository;
using Framsikt.DocExportWorker;
using Framsikt.DocExportWorker.Core;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.FeatureManagement;
using Newtonsoft.Json;

namespace Framsikt.Integration.Tests
{
    public class MRExportPublishIntegrationTest
    {
        private static IServiceCollection servicesCollection;
        private static IFeatureManager _featureManager;
        private static IAzureStorageAccountClient _azureStorageAccountClient;
        private static IVaultHelper _vaultHelper;

        public static void InitializeServices(IServiceCollection builderservicesCollection, IFeatureManager featureManager, IAzureStorageAccountClient azureStorageAccountClient, IVaultHelper vaultHelper)
        {
            ArgumentNullException.ThrowIfNull(vaultHelper);
            servicesCollection = builderservicesCollection;
            _featureManager = featureManager;
            _vaultHelper = vaultHelper;
        }

        public static void TestMonthlyReportPublishWebsite()
        {
            try
            {
                string userId = "<EMAIL>";
                //int budgetYear = 2025;
                //int forecastPeriod = 02;
                //int configId = 18299;
                //int clientId = 3082;
                //int tenantid = 3082;

                int budgetYear = 2025;
                int forecastPeriod = 04;
                int configId = 992;
                int clientId = 31;
                int tenantid = 31;

                //int budgetYear = 2025;
                //int forecastPeriod = 03;
                //int configId = 971;
                //int clientId = 31;
                //int tenantid = 31;

                //int budgetYear = 2025;
                //int forecastPeriod = 04;
                //int configId = 12277;
                //int clientId = 2;
                //int tenantid = 7688;

                //int budgetYear = 2025;
                //int forecastPeriod = 06;
                //int configId = 3881;
                //int clientId = 7684;
                //int tenantid = 8350;

                string Period = forecastPeriod.ToString().Length == 2 ? string.Concat(budgetYear, forecastPeriod) : budgetYear + "0" + forecastPeriod;


                IAppDataCache dc = new AppDataCache(true, "", new CacheClientFactory());
                IUtility utilBL = new Utility(dc, new DbContextManagerTests(dc, userId, tenantid, clientId), null, new DocExportSessionManager(clientId, userId), new CKEditorExtensions(_vaultHelper), _featureManager, _azureStorageAccountClient);
                UserData ud = utilBL.GetUserDetails(userId);

                using (var container = AutofacContainerBootstrapper.ConfigureAutofacContainerForQueues(servicesCollection, ud, DateTime.UtcNow.Year, "mrpublishrequestqueue"))
                {
                    Aspose.Words.License asposeWordLicense = new Aspose.Words.License();
                    asposeWordLicense.SetLicense("Aspose.Words.lic");

                    Aspose.Cells.License asposeCellsLicense = new Aspose.Cells.License();
                    asposeCellsLicense.SetLicense("Aspose.Cells.lic");

                    MonthlyReportingExportHelper exp = new MonthlyReportingExportHelper(container);

                    IUtility util = container.GetRequiredService<IUtility>();
                    IAzureBlobHelper azureBlobHelper = container.GetRequiredService<IAzureBlobHelper>();
                    var templateId = util.GetTenantDBContext().tco_publish_config.Find(configId).fk_template_id;
                    string blobPath = util.GetTenantDBContext().TcoPublishTemplate.Find(templateId).TemplateUrl;
                    string templateFromBlob = azureBlobHelper.GetTextBlobAsync(StorageAccount.AppStorage, BlobContainers.PublishTemplate, blobPath).GetAwaiter().GetResult();
                    PublishTemplateHelper template = JsonConvert.DeserializeObject<PublishTemplateHelper>(templateFromBlob) ?? new PublishTemplateHelper();

                    exp.PublishWebsiteToStaging(userId, false, budgetYear, Convert.ToInt32(Period), configId, false, string.Empty, template, true);
                }
            }
            catch (Exception e)
            {
                Console.Write(e.ToString());
            }
        }

        public static void TestMonthlyReportPublishWebsiteProd()
        {
            try
            {
                String userId = "<EMAIL>";
                int budgetYear = 2024;
                int forecastPeriod = 12;
                int configId = 8311;
                string Period = forecastPeriod.ToString().Length == 2 ? string.Concat(budgetYear, forecastPeriod) : budgetYear + "0" + forecastPeriod;
                int clientId = 6630;
                int tenantid = 6630;

                TimeSpan interval = TimeSpan.FromSeconds(2000);
                string timeInterval = interval.ToString();

                IAppDataCache dc = new AppDataCache(true, "", new CacheClientFactory());
                IUtility utilBL = new Utility(dc, new DbContextManagerTests(dc, userId, tenantid, clientId), null, new DocExportSessionManager(clientId, userId), new CKEditorExtensions(_vaultHelper), _featureManager, _azureStorageAccountClient);
                UserData ud = utilBL.GetUserDetails(userId);

                using (var container = AutofacContainerBootstrapper.ConfigureAutofacContainerForQueues(servicesCollection, ud, DateTime.UtcNow.Year, "mrpublishrequestqueue"))
                {
                    Aspose.Words.License asposeWordLicense = new Aspose.Words.License();
                    asposeWordLicense.SetLicense("Aspose.Words.lic");

                    Aspose.Cells.License asposeCellsLicense = new Aspose.Cells.License();
                    asposeCellsLicense.SetLicense("Aspose.Cells.lic");

                    MonthlyReportingExportHelper exp = new MonthlyReportingExportHelper(container);
                    PublishTemplateHelper template = new PublishTemplateHelper();
                    exp.PublishWebsiteToProd(userId, configId);
                }
            }
            catch (Exception e)
            {
                Console.Write(e.ToString());
            }
        }

        public static void TestMontlyReportCreateDocument()
        {
            try
            {
                string userId = "<EMAIL>";
                int budgetYear = 2025;
                int forecastPeriod = 04;
                int clientId = 7859;
                int tenantid = 7859;
                int templateId = 39322;
                string Period = forecastPeriod.ToString().Length == 2 ? string.Concat(budgetYear, forecastPeriod) : budgetYear + "0" + forecastPeriod;

                IAppDataCache dc = new AppDataCache(true, "", new CacheClientFactory());
                IUtility utilBL = new Utility(dc, new DbContextManagerTests(dc, userId, tenantid, clientId), null, new DocExportSessionManager(clientId, userId), new CKEditorExtensions(_vaultHelper), _featureManager, _azureStorageAccountClient);
                UserData ud = utilBL.GetUserDetails(userId);

                using (var container = AutofacContainerBootstrapper.ConfigureAutofacContainerForQueues(servicesCollection, ud, DateTime.UtcNow.Year))
                {
                    Aspose.Words.License asposeWordLicense = new Aspose.Words.License();
                    asposeWordLicense.SetLicense("Aspose.Words.lic");

                    Aspose.Cells.License asposeCellsLicense = new Aspose.Cells.License();
                    asposeCellsLicense.SetLicense("Aspose.Cells.lic");

                    MonthlyReportingExportHelper exp = new MonthlyReportingExportHelper(container);
                    
                    IUtility util = container.GetRequiredService<IUtility>();
                    IAzureBlobHelper azureBlobHelper = container.GetRequiredService<IAzureBlobHelper>();
                    string blobPath = util.GetTenantDBContext().TcoPublishTemplate.Find(templateId).TemplateUrl;
                    string templateFromBlob = azureBlobHelper.GetTextBlobAsync(StorageAccount.AppStorage, BlobContainers.PublishTemplate, blobPath).GetAwaiter().GetResult();
                    PublishTemplateHelper template = JsonConvert.DeserializeObject<PublishTemplateHelper>(templateFromBlob) ?? new PublishTemplateHelper();
                    IEnumerable<PublishTreeNode> exParams = template.Tree;

                    using (MemoryStream ms = exp.CreateDocument(exParams, false, userId, budgetYear, Convert.ToInt32(Period), DocFormat.Word))
                    {
                        ms.Position = 0;
                        using (FileStream file = new FileStream(@"C:\test\export1.docx", FileMode.OpenOrCreate, FileAccess.ReadWrite))
                        {
                            ms.WriteTo(file);
                        }
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }
        }

        public static void TestMRSyncExport()
        {
            string userName = "<EMAIL>";
            int clientId = 7684;
            int tenantid = 8350;
            int budgetYear = 2025;
            IAppDataCache dc = new AppDataCache(true, "", new CacheClientFactory());
            IUtility utilBL = new Utility(dc, new DbContextManagerTests(dc, userName, tenantid, clientId), null, new DocExportSessionManager(clientId, userName), new CKEditorExtensions(_vaultHelper), _featureManager, _azureStorageAccountClient);
            UserData ud = utilBL.GetUserDetails(userName);
            var publishConfigId = 3861;

            using var container = AutofacContainerBootstrapper.ConfigureAutofacContainerForQueues(servicesCollection, ud, budgetYear);

            IUtility util = container.GetRequiredService<IUtility>();
            IAzureBlobHelper azureBlobHelper = container.GetRequiredService<IAzureBlobHelper>();
            var templateId = util.GetTenantDBContext().tco_publish_config.Find(publishConfigId).fk_template_id;
            string blobPath = util.GetTenantDBContext().TcoPublishTemplate.Find(templateId).TemplateUrl;
            string templateFromBlob = azureBlobHelper.GetTextBlobAsync(StorageAccount.AppStorage, BlobContainers.PublishTemplate, blobPath).GetAwaiter().GetResult();
            var documentTree = JsonConvert.DeserializeObject<List<PublishTreeNode>>(templateFromBlob) ?? new List<PublishTreeNode>();

            Aspose.Words.License asposeWordLicense = new Aspose.Words.License();
            asposeWordLicense.SetLicense("Aspose.Words.lic");

            Aspose.Cells.License asposeCellsLicense = new Aspose.Cells.License();
            asposeCellsLicense.SetLicense("Aspose.Cells.lic");
            PublishProcessStageInputHelper publishStageInputHelper = new PublishProcessStageInputHelper()
            {
                orgID = "UKE",
                orgLevel = 1,
                includeInternalDesc = false,
                budgetYear = 2025,
                forecastPeriod = 202506,
                documentTree = documentTree,
                treeType = PublishTreeType.MRSyncTree.ToString(),
                fontID = -1,
                configID = 3861,
                templateID = 15979,
            };
            MRSyncDocExport expHelper = new MRSyncDocExport(container);
            using MemoryStream ms = expHelper.CreateDocument(userName, publishStageInputHelper).GetAwaiter().GetResult();
            ms.Position = 0;
            using FileStream file = new FileStream(@"C:\\test\\mrsyncexport.docx", FileMode.OpenOrCreate, FileAccess.ReadWrite);
            ms.WriteTo(file);
        }
    }
}