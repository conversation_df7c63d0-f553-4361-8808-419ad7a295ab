#pragma warning disable CS8618
#pragma warning disable CS8625
#pragma warning disable CS8602
#pragma warning disable CS8600
#pragma warning disable CS8629
#pragma warning disable CS8604

using Azure.Data.Tables;
using Azure.Identity;
using Azure.Storage.Blobs;
using Framsikt.BL;
using Framsikt.BL.Core.Helpers;
using Framsikt.BL.Helpers;
using Framsikt.BL.Helpers.CkEditorHelpers;
using Framsikt.BL.Plan.Helpers;
using Framsikt.BL.Plan.PublishHelpers;
using Framsikt.BL.Repository;
using Framsikt.DocExportWorker;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.FeatureManagement;

namespace Framsikt.Integration.Tests
{
    public class PlanExportPublishIntegrationTest
    {
        private static IServiceCollection servicesCollection;
        private static IFeatureManager _featureManager;
        private static IAzureStorageAccountClient _azureStorageAccountClient;
        private static IVaultHelper _vaultHelper;

        public static void InitializeServices(IServiceCollection builderservicesCollection, IFeatureManager featureManager, IAzureStorageAccountClient azureStorageAccountClient, IVaultHelper vaultHelper)
        {
            servicesCollection = builderservicesCollection;
            _featureManager = featureManager;
            _azureStorageAccountClient = azureStorageAccountClient;
            _vaultHelper = vaultHelper;
        }

        public static void TestPlanExport()
        {
            try
            {
                int configId = 7268;
                int templateId = 36906;
                int clientId = 1017;
                int tenantId = 1017;
                string userName = "<EMAIL>";
                string planId = "5AF3888F-E37F-4720-AC9C-38866F448984";

                IAppDataCache dc = new AppDataCache(true, "", new CacheClientFactory());
                IUtility utilBL = new Utility(dc, new DbContextManagerTests(dc, userName, tenantId, clientId), null, new DocExportSessionManager(clientId, userName), new CKEditorExtensions(_vaultHelper), _featureManager, _azureStorageAccountClient);
                UserData ud = utilBL.GetUserDetails(userName);

                using (var container = AutofacContainerBootstrapper.ConfigureAutofacContainerForQueues(servicesCollection, ud, DateTime.UtcNow.Year))
                {
                    Aspose.Words.License asposeWordLicense = new Aspose.Words.License();
                    asposeWordLicense.SetLicense("Aspose.Words.lic");

                    Aspose.Cells.License asposeCellsLicense = new Aspose.Cells.License();
                    asposeCellsLicense.SetLicense("Aspose.Cells.lic");

                    PlanDocumentHelper exp = new PlanDocumentHelper(container);
                    PlanPublishQueueMessageHelper queuehelper = new PlanPublishQueueMessageHelper
                    {
                        TenantId = tenantId,
                        ConfigId = configId,
                        TemplateId = templateId,
                        UserId = userName,
                        PlanId = planId
                    };

                    var data = exp.CreateDocument(queuehelper).GetAwaiter().GetResult();
                    using (MemoryStream ms = data)
                    {
                        ms.Position = 0;
                        using (FileStream file = new FileStream(@"C:\Plan_" + planId + ".docx", FileMode.OpenOrCreate, FileAccess.ReadWrite))
                        {
                            ms.WriteTo(file);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Console.Write(e.ToString());
            }
        }

        public static void TestPlanPublish()
        {
            try
            {
                int configId = 6093;
                int templateId = 18017;
                int tenantId = 7686;
                int clientId = 7686;
                string userName = "<EMAIL>";
                string planId = "202A8EF0-F3AE-4616-86F4-2BC4C149AAE1";

                IAppDataCache dc = new AppDataCache(true, "", new CacheClientFactory());
                IUtility utilBL = new Utility(dc, new DbContextManagerTests(dc, userName, tenantId, clientId), null, new DocExportSessionManager(clientId, userName), new CKEditorExtensions(_vaultHelper), _featureManager, _azureStorageAccountClient);
                UserData ud = utilBL.GetUserDetails(userName);

                using (var container = AutofacContainerBootstrapper.ConfigureAutofacContainerForQueues(servicesCollection, ud, DateTime.UtcNow.Year, "planpublishrequestqueue"))
                {
                    Aspose.Words.License asposeWordLicense = new Aspose.Words.License();
                    asposeWordLicense.SetLicense("Aspose.Words.lic");

                    Aspose.Cells.License asposeCellsLicense = new Aspose.Cells.License();
                    asposeCellsLicense.SetLicense("Aspose.Cells.lic");

                    PlanPublishQueueMessageHelper queuehelper = new PlanPublishQueueMessageHelper
                    {
                        TenantId = tenantId,
                        ConfigId = configId,
                        TemplateId = templateId,
                        UserId = userName,
                        PlanId = planId
                    };

                    PlanPublishHelper exp = new PlanPublishHelper(container);
                    var status = exp.PublishWebsiteToStaging(queuehelper, true).GetAwaiter().GetResult();
                }
            }
            catch (Exception e)
            {
                Console.Write(e.ToString());
            }
        }
    }
}