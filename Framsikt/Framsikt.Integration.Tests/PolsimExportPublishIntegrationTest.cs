#pragma warning disable CS8618
#pragma warning disable CS8625
#pragma warning disable CS8602
#pragma warning disable CS8600
#pragma warning disable CS8629
#pragma warning disable CS8604

using Framsikt.BL;
using Framsikt.BL.Core.Helpers;
using Framsikt.BL.Helpers;
using Framsikt.BL.Helpers.CkEditorHelpers;
using Framsikt.BL.Repository;
using Framsikt.DocExportWorker;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.FeatureManagement;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Framsikt.Integration.Tests
{
    public class PolsimExportPublishIntegrationTest
    {
        private static IServiceCollection servicesCollection;
        private static IFeatureManager _featureManager;
        private static IAzureStorageAccountClient _azureStorageAccountClient;
        private static IVaultHelper _vaultHelper;

        public static void InitializeServices(IServiceCollection builderservicesCollection, IFeatureManager featureManager, IAzureStorageAccountClient azureStorageAccountClient, IVaultHelper vaultHelper)
        {
            servicesCollection = builderservicesCollection;
            _featureManager = featureManager;
            _azureStorageAccountClient = azureStorageAccountClient;
            _vaultHelper = vaultHelper;
        }

        public static void TestPublish()
        {
            try
            {
                int configId = 4274;
                string proposalId = "1267E774-9DB2-4772-A279-84955AD10AEB";
                string userName = "<EMAIL>";
                int budgetYear = 2023;
                int clientId = 2097;
                int tenantId = 2097;

                IAppDataCache dc = new AppDataCache(true, "", new CacheClientFactory());
                IUtility utilBL = new Utility(dc, new DbContextManagerTests(dc, userName, tenantId, clientId), null, new DocExportSessionManager(clientId, userName), new CKEditorExtensions(_vaultHelper), _featureManager, _azureStorageAccountClient);
                UserData ud = utilBL.GetUserDetails(userName);

                using (var container = AutofacContainerBootstrapper.ConfigureAutofacContainerForQueues(servicesCollection, ud, DateTime.UtcNow.Year, "pspublishrequestqueue"))
                {
                    Aspose.Words.License asposeWordLicense = new Aspose.Words.License();
                    asposeWordLicense.SetLicense("Aspose.Words.lic");

                    Aspose.Cells.License asposeCellsLicense = new Aspose.Cells.License();
                    asposeCellsLicense.SetLicense("Aspose.Cells.lic");

                    IBudgetManagement _budgetManagement = container.GetRequiredService<IBudgetManagement>();
                    PublishTemplateHelper templateFromService = _budgetManagement.GetPoliticalSimulationTemplate(userName);
                    templateFromService.BudgetYear = budgetYear;

                    PSExportHelper sut = new PSExportHelper(container);
                    var publishStatus = sut.PublishWebsiteToStaging(templateFromService, userName, System.Guid.Parse(proposalId), configId, true);
                }
            }
            catch (Exception e)
            {
                Console.Write(e.ToString());
            }
        }

        public static void TestExport()
        {
            try
            {
                string userName = "<EMAIL>";
                string budproposalId = "********-6BB5-4CC7-A4AF-966A6512FE5E";
                int clientId = 4097;
                int tenantId = 4097;

                IAppDataCache dc = new AppDataCache(true, "", new CacheClientFactory());
                IUtility utilBL = new Utility(dc, new DbContextManagerTests(dc, userName, tenantId, clientId), null, new DocExportSessionManager(clientId, userName), new CKEditorExtensions(_vaultHelper), _featureManager, _azureStorageAccountClient);
                UserData ud = utilBL.GetUserDetails(userName);

                using (var container = AutofacContainerBootstrapper.ConfigureAutofacContainerForQueues(servicesCollection, ud, DateTime.UtcNow.Year, string.Empty))
                {
                    Aspose.Words.License asposeWordLicense = new Aspose.Words.License();
                    asposeWordLicense.SetLicense("Aspose.Words.lic");

                    Aspose.Cells.License asposeCellsLicense = new Aspose.Cells.License();
                    asposeCellsLicense.SetLicense("Aspose.Cells.lic");

                    var sut = new PSExportHelper(container);
                    using (MemoryStream ms = sut.CreateDocument(userName, Guid.Parse(budproposalId)))
                    {
                        ms.Position = 0;
                        using (FileStream file = new FileStream(@"C:\docexports\Holmestrand.docx", FileMode.OpenOrCreate, FileAccess.ReadWrite))
                        {
                            ms.WriteTo(file);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Console.Write(e.ToString());
            }
        }

        public static void TestFlushDocument()
        {
            try
            {
                string userName = "<EMAIL>";
                int clientId = 2097;
                int tenantId = 2097;

                IAppDataCache dc = new AppDataCache(true, "", new CacheClientFactory());
                IUtility utilBL = new Utility(dc, new DbContextManagerTests(dc, userName, tenantId, clientId), null, new DocExportSessionManager(clientId, userName), new CKEditorExtensions(_vaultHelper), _featureManager, _azureStorageAccountClient);
                UserData ud = utilBL.GetUserDetails(userName);

                using (var container = AutofacContainerBootstrapper.ConfigureAutofacContainerForQueues(servicesCollection, ud, DateTime.UtcNow.Year, string.Empty))
                {
                    Aspose.Words.License asposeWordLicense = new Aspose.Words.License();
                    asposeWordLicense.SetLicense("Aspose.Words.lic");

                    Aspose.Cells.License asposeCellsLicense = new Aspose.Cells.License();
                    asposeCellsLicense.SetLicense("Aspose.Cells.lic");

                    var collaboration = container.GetRequiredService<ICollaboration>();
                    string value = File.ReadAllText(@"C:\Not C Drive\Blob Errors\response.txt");
                    var eventInfo = JsonConvert.DeserializeObject<EventInfo>(value);
                    var stateInfo = FlushDocument(eventInfo, collaboration).GetAwaiter().GetResult();
                }
            }
            catch (Exception e)
            {
                Console.Write(e.ToString());
            }
        }

        private static async Task<Dictionary<Guid, string>> FlushDocument(EventInfo? eventInfo, ICollaboration _collaboration)
        {
            Guid documentId = Guid.Empty;
            string message = string.Empty;
            try
            {
                if (eventInfo != null && eventInfo.Event.Contains("collaboration.user.disconnected"))
                {
                    JObject? payload = eventInfo?.Payload;
                    if (payload != null && payload.ContainsKey("document") && payload.ContainsKey("connected_users"))
                    {
                        JToken? documentKey = payload["document"];
                        JToken? connectedUsers = payload["connected_users"];
                        if (documentKey != null && documentKey.HasValues)
                        {
                            var docId = documentKey["id"];
                            if (docId != null)
                            {
                                if (Guid.TryParse(docId?.Value<string>(), out documentId) && connectedUsers is JArray && connectedUsers.Count() == 0)
                                {
                                    await _collaboration.FlushDocumentAsync(documentId.ToString(), false);
                                    return new Dictionary<Guid, string> {
                                    { documentId, $"Document with id {documentId} flushed successfully" }
                                    };
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                message = ex.Message;
            }
            return new Dictionary<Guid, string> {
                                    { documentId, message }
                                    };
        }
    }

    public class EventInfo
    {
        [JsonProperty("environment_id")]
        public string EnvironmentId { get; set; }

        [JsonProperty("event")]
        public string Event { get; set; }

        [JsonProperty("payload")]
        public JObject Payload { get; set; }

        [JsonProperty("sent_at")]
        public string SentAt { get; set; }
    }
}