using Framsikt.BL.Repository;

namespace Framsikt.Integration.Tests
{
    internal class InfraIntegrationTests
    {
        private static IVaultHelper _vaultHelper;

        public static void InitializeServices(IVaultHelper vaultHelper)
        {
            _vaultHelper = vaultHelper;
        }

        public static void GetSecretInfo()
        {
            Console.WriteLine(GetSecret("AzureClientSecret"));
            Console.WriteLine(GetSecret("azureHelpCenterSearchApiKey"));
            Console.WriteLine(GetSecret("colEditSecretKey"));
            Console.WriteLine(GetSecret("idpSecret"));
            Console.WriteLine(GetSecret("iFramelyApiKey"));
            Console.WriteLine(GetSecret("colApiSecret"));
            Console.WriteLine(GetSecret("azurePublishStagSearchApiKey"));
            Console.WriteLine(GetSecret("azurePublishProdSearchApiKey"));
            Console.WriteLine(GetSecret("azureSearchApiKey"));
        }

        private static string GetSecret(string secretName)
        {
            return $"{secretName} : {_vaultHelper.GetSecret(secretName)}";
        }
    }
}