{
  //////////////////////////////////////////////////    DEV SETTINGS START //////////////////////////////////////////////////

  "ConnectionStrings": {
    "AzureWebJobsDashboard": "DefaultEndpointsProtocol=https;AccountName=stodevframsikt;AccountKey=****************************************************************************************",
    "AzureWebJobsStorage": "DefaultEndpointsProtocol=https;AccountName=stodevframsikt;AccountKey=****************************************************************************************",
    "ServiceBusConnection": "sblocalframsikt.servicebus.windows.net"
  },
  "FramsiktDBConnectionString": "Server=tcp:ig873o3x7z.database.windows.net;MultipleActiveResultSets=True;Database=SQL-DEV-APP;Encrypt=true;Authentication=Active Directory Default;",
  "azureMainAppStorageAccountUri": "https://stodevframsikt.{Service}.core.windows.net/",
  "azurePublishProdUri": "https://stodevpubprodframsikt.{Service}.core.windows.net/",
  "azurePublishStagUri": "https://stodevpubstageframsikt.{Service}.core.windows.net/",
  "azurePublishProdBackupUri": "https://stodevpubprodbakframsikt.{Service}.core.windows.net/",
  "azureIntegrationStorageAccountUri": "UseDevelopmentStorage=true",
  "publishBmStagWebSiteUrl": "http://appdevframsikt.azurewebsites.net",
  "publishBmProdWebSiteUrl": "http://appdevframsikt-prod.azurewebsites.net",
  "publishMrStagWebSiteUrl": "http://appdevframsikt.azurewebsites.net",
  "publishMrProdWebSiteUrl": "http://appdevframsikt-prod.azurewebsites.net",
  "publishPsStagWebSiteUrl": "https://appstgframsikt-version4-stage.azurewebsites.net",
  "publishBusplanActivityStagWebSiteUrl": "http://appdevframsikt.azurewebsites.net",
  "publishBusplanActivityProdWebSiteUrl": "http://appdevframsikt-prod.azurewebsites.net",
  "publishPsProdWebSiteUrl": "https://appstgframsikt-version4.azurewebsites.net",
  "publishplanStagWebSiteUrl": "http://appdevframsikt.azurewebsites.net",
  "azurePublishProdSearchServiceName": "framsiktdev",
  "azurePublishStagSearchServiceName": "framsiktdev",
  "AzureClientId": "91a87910-a612-419f-aa4a-7184e23d12f4",
  "AzureTenantId": "8ee197b7-85ae-4348-bf55-35fa41f254ad",
  "keyVaultUrl": "https://framsiktdevvault.vault.azure.net/",
  "azurePublishProdIndexName": "prod3",
  "azurePublishProdIndexName_NO": "prod3",
  "azurePlanOverviewPublishProdIndexName": "prodplanoverview",
  "azurePlanOverviewPublishProdIndexName2": "prodplanoverview2",
  "Microsoft.WindowsAzure.Plugins.Diagnostics.ConnectionString": "UseDevelopmentStorage=true",
  "azureRedisHostName": "localhost",
  "azureBmPublishContainerName": "content-v4",
  "azureMrPublishContainerName": "content-v4",
  "azurePsPublishContainerName": "content-v4",
  "azurePlanPublishContainerName": "content-v4",
  "azureBusPlanActPublishContainerName": "content-v4",
  "azureBusPlanAppointmentPublishContainerName": "content-v4",
  "azureDescriptionsContainerName": "descriptionscontainer",
  "azureReportTemplateContainerName": "reporttemplate",
  "azureBmDocNodeDescContainerName": "bmdocnodedesc",
  "azureFilterJsonDataContainerName": "filterjsondata",
  "exportqueuevisibilitytimeout": "{&quot;hours&quot;:0,&quot;minutes&quot;:0,&quot;seconds&quot;:10}",
  "visibilitytimeoutStaffPlanningExport": "{&quot;hours&quot;:1,&quot;minutes&quot;:0,&quot;seconds&quot;:0}",
  "AppInSightInstrumentationKey": "",
  "CacheEnabled": "True",
  "cacheKeyPrefix": "dlocal",
  "frontendCacheKeyPrefix": "local",
  "LogDbConnectionString": "server=tcp:ig873o3x7z.database.windows.net;MultipleActiveResultSets=True;database=SQL-DEV-T2;Encrypt=true;Authentication=Active Directory Default;",
  "EnablePublishJobLogging": "true",
  "LogSourceName": "Local",
  "datawarehouseConnectionString": "Server=tcp:framsiktpilotstage.database.windows.net,1433;database=dwstgsmpframsikt;Persist Security Info=False;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;",
  "iFramelyApiKey": "4109bb0e5b676ba456fe88",
  "FramsiktRenderApi": "https://appdevframsiktrender.azurewebsites.net/",
  "MainAppKeyVaultUrl": "https://framsiktdevmainapp.vault.azure.net/",
  "ClamavServer": "localhost",
  "HangfireDBConnectionString": "Server=(localDb)\\localInstance;Initial Catalog=HANGFIRE_DEV_DOTNET6;Integrated Security=true",
  "backgroundJobConfig": "{\"defaultQueueMessageCount\":3,\"criticalQueueMessageCount\":5,\"docExportMessageCount\":1,\"jobCronSchedule\":\"*/2 * * * *\"}",
  "AppLogBatchPostingLimit": "100",
  "AuditLogInfoDbConnectionString": "server=tcp:framsiktpilotstage.database.windows.net;MultipleActiveResultSets=True;database=SQL-STG-AUDIT;Encrypt=true;Authentication=Active Directory Default;",
  "logQueryInformation": true,
  "Z.EntityFramework.Extensions": {
    "LicenseName": "1792;100-Framsikt",
    "LicenseKey": "62efaf1a-f340-1265-419d-13ffbf3809bf"
  },
  "colEditorApi": "https://appdevckframsikt.azurewebsites.net/api/v5",
  "colApiSecret": "a9453a00781c32407674de1cbc55ce567fdde5e998754ca6e4c85ccd55d020dce3d11dd91c82f1e6969d602263375ed6ef27034a76c5a85abaf3cd95",
  "colEditEnvironmentId": "6c302b48aaee1bb70f2b",
  "azurePublishStageIndexName": "staging3",
  "azurePlanOverviewPublishStgIndexName": "stagingplanoverview",
  "azurePlanOverviewPublishStgIndexName2": "stagingplanoverview2",
  "azureRedisConnectionString": "localhost,abortConnect=false,allowAdmin=true"

  //////////////////////////////////////////////////    DEV SETTINGS END //////////////////////////////////////////////////

  //////////////////////////////////////////////////    STAGING SETTINGS START //////////////////////////////////////////////////

  //"ConnectionStrings": {
  //  "AzureWebJobsDashboard": "DefaultEndpointsProtocol=https;AccountName=stostagframsikt;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
  //  "AzureWebJobsStorage": "DefaultEndpointsProtocol=https;AccountName=stostagframsikt;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
  //  "ServiceBusConnection": "sblocalframsikt.servicebus.windows.net"
  //},
  //"FramsiktDBConnectionString": "server=tcp:framsiktpilotstage.database.windows.net;MultipleActiveResultSets=True;database=SQL-STG-APP;Encrypt=true;Authentication=Active Directory Default;",
  //"azureMainAppStorageAccountUri": "https://stostagframsikt.{Service}.core.windows.net/",
  //"azurePublishProdUri": "https://stostagpubframsikt.{Service}.core.windows.net/",
  //"azurePublishStagUri": "https://stostagpubframsiktstage.{Service}.core.windows.net/",
  //"azurePublishProdBackupUri": "https://stostgpubprodbakframsikt.{Service}.core.windows.net/",
  //"azureIntegrationStorageAccountUri": "UseDevelopmentStorage=true",
  //"publishBmStagWebSiteUrl": "https://appstgframsikt-version4-stage.azurewebsites.net",
  //"publishBmProdWebSiteUrl": "https://appstgframsikt-version4.azurewebsites.net",
  //"publishMrStagWebSiteUrl": "https://appstgframsikt-version4-stage.azurewebsites.net",
  //"publishMrProdWebSiteUrl": "https://appstgframsikt-version4.azurewebsites.net",
  //"publishPsStagWebSiteUrl": "https://appstgframsikt-version4-stage.azurewebsites.net",
  //"publishBusplanActivityStagWebSiteUrl": "https://appstgframsikt-version4-stage.azurewebsites.net",
  //"publishBusplanActivityProdWebSiteUrl": "https://appstgframsikt-version4.azurewebsites.net",
  //"publishPsProdWebSiteUrl": "https://appstgframsikt-version4.azurewebsites.net",
  //"publishplanStagWebSiteUrl": "https://appstgframsikt-version4-stage.azurewebsites.net",
  //"publishBudPropProcessStageWebSiteUrl": "https://appstgframsikt-version4-stage.azurewebsites.net",
  //"azurePublishProdSearchServiceName": "framsiktstg",
  //"azurePublishProdSearchApiKey": "900B92C141D0B00F12F570AF55873BA4",
  //"azurePublishStagSearchServiceName": "framsiktstg",
  //"azurePublishStagSearchApiKey": "900B92C141D0B00F12F570AF55873BA4",
  //"AzureClientId": "91a87910-a612-419f-aa4a-7184e23d12f4",
  //"AzureTenantId": "8ee197b7-85ae-4348-bf55-35fa41f254ad",
  //"AzureClientSecret": "****************************************",
  //"keyVaultUrl": "https://framsiktstgvault.vault.azure.net/",
  //"authUrlFint": "https://idp.felleskomponent.no/nidp/oauth/nam/token",
  //"azurePublishProdIndexName": "prod3",
  //"azurePublishProdIndexName_NO": "prod3",
  //"azurePlanOverviewPublishProdIndexName": "prodplanoverview",
  //"azurePlanOverviewPublishProdIndexName2": "prodplanoverview2",
  //"Microsoft.WindowsAzure.Plugins.Diagnostics.ConnectionString": "UseDevelopmentStorage=true",
  //"azureRedisConnectionString": "localhost,abortConnect=false,allowAdmin=true",
  //"azureBmPublishContainerName": "content-v4",
  //"azureMrPublishContainerName": "content-v4",
  //"azurePsPublishContainerName": "content-v4",
  //"azurePlanPublishContainerName": "content-v4",
  //"azureBusPlanActPublishContainerName": "content-v4",
  //"azureBusPlanAppointmentPublishContainerName": "content-v4",
  //"azureDescriptionsContainerName": "descriptionscontainer",
  //"azureReportTemplateContainerName": "reporttemplate",
  //"azureBmDocNodeDescContainerName": "bmdocnodedesc",
  //"azureFilterJsonDataContainerName": "filterjsondata",
  //"exportqueuevisibilitytimeout": "{&quot;hours&quot;:0,&quot;minutes&quot;:0,&quot;seconds&quot;:10}",
  //"visibilitytimeoutStaffPlanningExport": "{&quot;hours&quot;:1,&quot;minutes&quot;:0,&quot;seconds&quot;:0}",
  //"AppInSightInstrumentationKey": "",
  //"CacheEnabled": "True",
  //"cacheKeyPrefix": "dlocal",
  //"frontendCacheKeyPrefix": "local",
  //"azureRedisHostName": "localhost",
  //"LogDbConnectionString": "server=tcp:framsiktpilotstage.database.windows.net;MultipleActiveResultSets=True;database=SQL-STG-T2;Encrypt=true;Authentication=Active Directory Default;",
  //"EnablePublishJobLogging": "true",
  //"LogSourceName": "Local",
  //"datawarehouseConnectionString": "Server=tcp:framsiktpilotstage.database.windows.net,1433;database=dwstgsmpframsikt;Persist Security Info=False;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Authentication=Active Directory Default;",
  //"iFramelyApiKey": "4109bb0e5b676ba456fe88",
  //"FramsiktRenderApi": "https://appdevframsiktrender.azurewebsites.net/",
  //"MainAppKeyVaultUrl": "https://framsiktstgmainapp.vault.azure.net/",
  //"ClamavServer": "localhost",
  //"HangfireDBConnectionString": "Server=(localdb)\\MSSQLLocalDB;Initial Catalog=HANGFIRE_DEV_DOTNET6;Integrated Security=true",
  //"backgroundJobConfig": "{\"defaultQueueMessageCount\":3,\"criticalQueueMessageCount\":5,\"docExportMessageCount\":1,\"jobCronSchedule\":\"*/2 * * * *\"}",
  //"AppLogBatchPostingLimit": "100",
  //"AuditLogInfoDbConnectionString": "server=tcp:framsiktpilotstage.database.windows.net;MultipleActiveResultSets=True;database=SQL-STG-AUDIT;Encrypt=true;Authentication=Active Directory Default;",
  //"logQueryInformation": false,
  //"Z.EntityFramework.Extensions": {
  //  "LicenseName": "1792;100-Framsikt",
  //  "LicenseKey": "62229326-55d0-1f14-b9bf-54735c718f71"
  //},
  //"colEditorApi": "https://appstgckframsikt.azurewebsites.net/api/v5",
  //"colApiSecret": "96cea83cce4a6300f27c03b8c81138076c2550fe3f8d336871d66eb143b00831c2d22b00df11bc57d1d6025fde3e559b01605595d97b40f251fc51e1",
  //"colEditEnvironmentId": "bc21b43988a38fb4439c",
  //"azurePublishStageIndexName": "staging3",
  //"azurePlanOverviewPublishStgIndexName": "stagingplanoverview",
  //"azurePlanOverviewPublishStgIndexName2": "stagingplanoverview2",

  //////////////////////////////////////////////////    STAGING SETTINGS END //////////////////////////////////////////////////

  ////////////////////////////////////////////////    HOTFIX SETTINGS START //////////////////////////////////////////////////
//  "ConnectionStrings": {
//    "AzureWebJobsDashboard": "DefaultEndpointsProtocol=https;AccountName=stostagframsikt;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
//    "AzureWebJobsStorage": "DefaultEndpointsProtocol=https;AccountName=stostagframsikt;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
//    "ServiceBusConnection": "sbhotfixframsikt.servicebus.windows.net"
//  },
//  "FramsiktDBConnectionString": "server=tcp:framsiktpilotstage.database.windows.net;MultipleActiveResultSets=True;database=SQL-STG-APP;Encrypt=true;Authentication=Active Directory Default;",
//  "azureMainAppStorageAccountUri": "https://stohotfixframsikt.{Service}.core.windows.net/",
//  "azurePublishProdUri": "https://stohotfixpubframsikt.{Service}.core.windows.net/",
//  "azurePublishStagUri": "https://stohotfixpubframsiktstg.{Service}.core.windows.net/",
//  "azurePublishProdBackupUri": "https://stostgpubprodbakframsikt.{Service}.core.windows.net/",
//  "azureIntegrationStorageAccountUri": "https://stointegnewhotfix.{Service}.core.windows.net/",
//  "publishBmStagWebSiteUrl": "https://framsikthotfixpublish-test.azurewebsites.net/",
//  "publishBmProdWebSiteUrl": "https://framsikthotfixpublish.azurewebsites.net/",
//  "publishMrStagWebSiteUrl": "https://framsikthotfixpublish-test.azurewebsites.net/",
//  "publishMrProdWebSiteUrl": "https://framsikthotfixpublish.azurewebsites.net/",
//  "publishPsStagWebSiteUrl": "https://framsikthotfixpublish-test.azurewebsites.net/",
//  "publishBusplanActivityStagWebSiteUrl": "https://framsikthotfixpublish-test.azurewebsites.net/",
//  "publishBusplanActivityProdWebSiteUrl": "https://framsikthotfixpublish.azurewebsites.net/",
//  "publishPsProdWebSiteUrl": "https://framsikthotfixpublish.azurewebsites.net/",
//  "publishplanStagWebSiteUrl": "https://framsikthotfixpublish-test.azurewebsites.net/",
//  "publishBudPropProcessStageWebSiteUrl": "https://framsikthotfixpublish-test.azurewebsites.net/",
//  "azurePublishProdSearchServiceName": "srchframsikthotfix",
//  "azurePublishProdSearchApiKey": "****************************************************",
//  "azurePublishStagSearchServiceName": "srchframsikthotfix",
//  "azurePublishStagSearchApiKey": "****************************************************",
//  "AzureClientId": "91a87910-a612-419f-aa4a-7184e23d12f4",
//  "AzureTenantId": "8ee197b7-85ae-4348-bf55-35fa41f254ad",
//  "AzureClientSecret": "****************************************",
//  "keyVaultUrl": "https://framsikthotfixmainapp.vault.azure.net/",
//  "azurePublishProdIndexName": "hotfix",
//  "azurePublishProdIndexName_NO": "hotfix",
//  "azurePlanOverviewPublishProdIndexName": "hotfixplanoverview",
//  "azurePlanOverviewPublishProdIndexName2": "hotfixplanoverview2",
//  "Microsoft.WindowsAzure.Plugins.Diagnostics.ConnectionString": "",
//  "azureRedisConnectionString": "localhost,abortConnect=false,allowAdmin=true",
//  "azureBmPublishContainerName": "content-v4",
//  "azureMrPublishContainerName": "content-v4",
//  "azurePsPublishContainerName": "content-v4",
//  "azurePlanPublishContainerName": "content-v4",
//  "azureBusPlanActPublishContainerName": "content-v4",
//  "azureBusPlanAppointmentPublishContainerName": "content-v4",
//  "azureDescriptionsContainerName": "descriptionscontainer",
//  "azureReportTemplateContainerName": "reporttemplate",
//  "azureBmDocNodeDescContainerName": "bmdocnodedesc",
//  "azureFilterJsonDataContainerName": "filterjsondata",
//  "exportqueuevisibilitytimeout": "{&quot;hours&quot;:0,&quot;minutes&quot;:0,&quot;seconds&quot;:10}",
//  "visibilitytimeoutStaffPlanningExport": "{&quot;hours&quot;:1,&quot;minutes&quot;:0,&quot;seconds&quot;:0}",
//  "AppInSightInstrumentationKey": "",
//  "CacheEnabled": "True",
//  "cacheKeyPrefix": "dlocal",
//  "frontendCacheKeyPrefix": "local",
//  "LogDbConnectionString": "server=tcp:ig873o3x7z.database.windows.net;MultipleActiveResultSets=True;database=SQL-DEV-T2;Encrypt=true;Authentication=Active Directory Default;",
//  "EnablePublishJobLogging": "true",
//  "LogSourceName": "Local",
//  "datawarehouseConnectionString": "Server=tcp:framsiktpilotstage.database.windows.net,1433;database=dwstgsmpframsikt;Persist Security Info=False;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;",
//  "iFramelyApiKey": "4109bb0e5b676ba456fe88",
//  "FramsiktRenderApi": "https://appdevframsiktrender.azurewebsites.net/",
//  "MainAppKeyVaultUrl": "https://framsikthotfixmainapp.vault.azure.net/",
//  "ClamavServer": "localhost",
//  "HangfireDBConnectionString": "server=tcp:framsiktpilotstage.database.windows.net;MultipleActiveResultSets=True;database=HANGFIRE_HOTFIX;Encrypt=true;Authentication=Active Directory Default;",
//  "backgroundJobConfig": "{\"defaultQueueMessageCount\":32,\"criticalQueueMessageCount\":32,\"docExportMessageCount\":32,\"jobCronSchedule\":\"*/10 * * * * *\" ,\"workerCount\":4}",
//  "AppLogBatchPostingLimit": "100",
//  "AuditLogInfoDbConnectionString": "server=tcp:framsiktpilotstage.database.windows.net;MultipleActiveResultSets=True;database=SQL-STG-AUDIT;Encrypt=true;Authentication=Active Directory Default;",
//  "logQueryInformation": true,
//  "Z.EntityFramework.Extensions": {
//    "LicenseName": "1792;100-Framsikt",
//    "LicenseKey": "62efaf1a-f340-1265-419d-13ffbf3809bf"
//  },
//  "colEditorApi": "https://apphotfixckframsikt.azurewebsites.net/api/v5",
//  "colApiSecret": "52662c1e8239d75c890627d22bcb81ebbbdbc5f980a603c5c7f2c4014cba6c8f376feab36a53e14ae776e7b58a0103d4fbd80f7f8d7260b125e3411b",
//  "colEditEnvironmentId": "7eda159eb9678a2bcc47",
//  "azurePublishStageIndexName": "hotfixstg",
//  "azurePlanOverviewPublishStgIndexName": "hotfixplanoverviewstg",
//  "azurePlanOverviewPublishStgIndexName2": "hotfixplanoverviewstg2",
//  "azureRedisHostName": "localhost",
//  "authUrlFint": "https://idp.felleskomponent.no/nidp/oauth/nam/token",
//  "azureIntegrationStatusStorageAccountUri": "https://stointegnewhotfix.queue.core.windows.net/"

  //////////////////////////////////////////////////    HOTFIX SETTINGS END //////////////////////////////////////////////////
}