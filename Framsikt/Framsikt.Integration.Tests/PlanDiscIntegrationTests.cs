#pragma warning disable CS8618
#pragma warning disable CS8625

using Framsikt.BL;
using Framsikt.BL.Core.Helpers;
using Framsikt.BL.Helpers;
using Framsikt.BL.Helpers.CkEditorHelpers;
using Framsikt.BL.Plan.Core;
using Framsikt.BL.Repository;
using Framsikt.DocExportWorker;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.FeatureManagement;

namespace Framsikt.Integration.Tests
{
    public class PlanDiscIntegrationTests
    {
        private static IServiceCollection servicesCollection;
        private static IFeatureManager _featureManager;
        private static IAzureStorageAccountClient _azureStorageAccountClient;
        private static IVaultHelper _vaultHelper;

        public static void InitializeServices(IServiceCollection builderservicesCollection, IFeatureManager featureManager, IAzureStorageAccountClient azureStorageAccountClient, IVaultHelper vaultHelper)
        {
            servicesCollection = builderservicesCollection;
            _featureManager = featureManager;
            _azureStorageAccountClient = azureStorageAccountClient;
            _vaultHelper = vaultHelper;
        }


        public static async Task TestPlanDiscRingCreation()
        {
            string userId = "<EMAIL>";
            int clientId = 2157;
            int tenantId = 2157;
            int budgetYear = 2025;
            List<PlanDiscRingDTO> createRingsListDTO = new()
            {
                new PlanDiscRingDTO{name= "420 Kommunalteknisk", orderIndex= 219},
                new PlanDiscRingDTO{name= "62 DRIFT OG VEDLIKEHOLD", orderIndex= 220},
                new PlanDiscRingDTO{name= "6200 Kommunalteknisk Enhet", orderIndex= 222},
                new PlanDiscRingDTO{name= "6201 Komtek prosjekt", orderIndex= 223},
                new PlanDiscRingDTO{name= "6213 Komtek - maskinpark", orderIndex= 224},
                new PlanDiscRingDTO{name= "6216 Serviceavdelingen", orderIndex= 225},
                new PlanDiscRingDTO{name= "6240 Park/Idrett", orderIndex= 226},
                new PlanDiscRingDTO{name= "63 VAR", orderIndex= 226},
                new PlanDiscRingDTO{name= "6320 Komtek - Vannforsyning", orderIndex= 228},
                new PlanDiscRingDTO{name= "6321 Komtek råvannsforsyning", orderIndex= 229},
                new PlanDiscRingDTO{name= "6330 Komtek - Avløp", orderIndex= 230},
                new PlanDiscRingDTO{name= "6340 Komtek - SHMIL", orderIndex= 231},
                new PlanDiscRingDTO{name= "6350 Komtek - Kommunal renovasjon", orderIndex= 232},
                new PlanDiscRingDTO{name= "6360 Komtek - Slam", orderIndex= 233},
                new PlanDiscRingDTO{name= "6599 Fordeling selvkost kommunalteknisk", orderIndex= 234},
                new PlanDiscRingDTO{name= "64 VEIER OG GATELYS", orderIndex= 234},
                new PlanDiscRingDTO{name= "6400 Komtek - Veier", orderIndex= 236},
                new PlanDiscRingDTO{name= "6455 Flytebrygger og kaier", orderIndex= 237},
                new PlanDiscRingDTO{name= "6468 Komtek - veilys", orderIndex= 238},
                new PlanDiscRingDTO{name= "65 PARKERINGSPLASSER OG ANLEGG", orderIndex= 238},
                new PlanDiscRingDTO{name= "6500 Komtek - Parkeringsplasser", orderIndex= 240}
            };
            List<string> deleteRingsList = new();
            //List<PlanDiscRingDTO> createRingsListDTO = JsonConvert.DeserializeObject<List<PlanDiscRingDTO>>(Convert.ToString(createRingsListInput)) ?? new List<PlanDiscRingDTO>();
            List<PlanDiscRing> createRingsList = (from a in createRingsListDTO
                                                  select new PlanDiscRing
                                                  {
                                                      calendarRingDetails = new CalendarRingHelper { name = a.name },
                                                      orderIndex = a.orderIndex
                                                  }).ToList();
            var updatePDRings = new UpdatePDRingsInput
            {
                planDiscId = "db625f9f-86ed-49da-a5d4-7b2ef97f1de7",
                planDiscSetupId = 8,
                createRingsList = createRingsList,
                deleteRingsList = deleteRingsList
            };
            IAppDataCache dc = new AppDataCache(true, "", new CacheClientFactory());
            IUtility utilBL = new Utility(dc, new DbContextManagerTests(dc, userId, tenantId, clientId), null, new DocExportSessionManager(clientId, userId), new CKEditorExtensions(_vaultHelper), _featureManager, _azureStorageAccountClient);
            UserData ud = utilBL.GetUserDetails(userId);

            using (var container = AutofacContainerBootstrapper.ConfigureAutofacContainerForQueues(servicesCollection, ud, budgetYear, ""))
            {
                Aspose.Words.License asposeWordLicense = new Aspose.Words.License();
                asposeWordLicense.SetLicense("Aspose.Words.lic");

                Aspose.Cells.License asposeCellsLicense = new Aspose.Cells.License();
                asposeCellsLicense.SetLicense("Aspose.Cells.lic");

                IPlanDiscRingsImport _planDiscRingsImport = container.GetRequiredService<IPlanDiscRingsImport>();
                await _planDiscRingsImport.ImportPlanDiscRingsInfo(userId, updatePDRings);
            }
        }
    }
}
