#pragma warning disable CS8618
#pragma warning disable CS8625
#pragma warning disable CS8602
#pragma warning disable CS8600
#pragma warning disable CS8629
#pragma warning disable CS8604
#pragma warning disable CS8603

using Newtonsoft.Json;
using System.Text;

namespace Framsikt.Integration.Tests
{
    public class UserAPITests
    {
        private static readonly string baseurl = "https://localhost:44340/";
        private static readonly string subscriptionKey = "84985e8a4417411e8f8d3d82d940a915";
        private static readonly bool isLocalAPI = true;

        public async Task CreateUser()
        {
            try
            {
                int startId = 1000;
                int endId = 1000;

                List<string> usersList = new List<string>();
                for (int i = startId; i <= endId; i++)
                    usersList.Add($"venkat+innlandet{i}@framsikt.no");

                List<NewUser> users = new List<NewUser>();
                foreach (var item in usersList)
                    users.Add(new NewUser { Email = item, FirstName = "venkat", LastName = "raman", IsActive = true, Domain = "externalAuth", UserName = item });

                var taskList = new List<Task>();
                foreach (var item in users)
                    taskList.Add(CreateUserAsync(item));

                await Task.WhenAll(taskList);
            }
            catch (Exception)
            {
                throw;
            }
        }

        private async Task<string> CreateUserAsync(NewUser user)
        {
            var token = await GetAccessToken();
            using (var client = new HttpClient())
            {
                client.BaseAddress = new Uri($"{baseurl.TrimEnd('/')}/v2/Users");
                client.DefaultRequestHeaders.Add("Authorization", token);
                if (!isLocalAPI)
                    client.DefaultRequestHeaders.Add("Ocp-Apim-Subscription-Key", subscriptionKey);

                var bodyContent = JsonConvert.SerializeObject(user);
                var stringContent = new StringContent(bodyContent, UnicodeEncoding.UTF8, "application/json");
                var result = await client.PostAsync("", stringContent);
                string resultContent = await result.Content.ReadAsStringAsync();
                return resultContent;
            }
        }

        private async Task<string> GetAccessToken()
        {
            ClientInfo clientInfo = new ClientInfo();
            using (var client = new HttpClient())
            {
                client.BaseAddress = new Uri($"{baseurl.TrimEnd('/')}/v2/Token/IssueBearerToken");
                if (!isLocalAPI)
                    client.DefaultRequestHeaders.Add("Ocp-Apim-Subscription-Key", subscriptionKey);

                var bodyContent = JsonConvert.SerializeObject(clientInfo);
                var stringContent = new StringContent(bodyContent, UnicodeEncoding.UTF8, "application/json");
                var result = await client.PostAsync("", stringContent);
                string resultContent = (string)await result.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<string>(resultContent);
            }
        }
    }

    public class NewUser
    {
        /// <summary>
        /// Not in use. Kept for backward compatibility.
        /// </summary>
        [JsonProperty(PropertyName = "userName")]
        public string UserName { get; set; }

        /// <summary>
        /// User's first name
        /// </summary>
        [JsonProperty(PropertyName = "firstName")]
        public string FirstName { get; set; }

        /// <summary>
        /// User's last name
        /// </summary>
        [JsonProperty(PropertyName = "lastName")]
        public string LastName { get; set; }

        /// <summary>
        /// User's account status. True if the User's account is active.
        /// </summary>
        [JsonProperty(PropertyName = "isActive")]
        public bool IsActive { get; set; }

        /// <summary>
        /// Not in use. Kept for backward compatibility.
        /// </summary>
        [JsonProperty(PropertyName = "domain")]
        public string Domain { get; set; }

        /// <summary>
        /// Email Id of the user
        /// </summary>
        [JsonProperty(PropertyName = "email")]
        public string Email { get; set; }
    }

    public class ClientInfo
    {
        [JsonProperty(PropertyName = "clientId")]
        public string ClientId { get; set; } = "26edae2b-698a-4917-9e7e-d4c143293273".ToLower();

        [JsonProperty(PropertyName = "secret")]
        public string Secret { get; set; } = "H2R5rdQZjRqdykSH6WUyEkAJa95QHTe8AGYKR2BGDt4UTgrNdoyYt6OWetKhiss1";

        [JsonProperty(PropertyName = "scope")]
        public string Scope { get; set; } = "framsikt-stg-api:all";
    }
}