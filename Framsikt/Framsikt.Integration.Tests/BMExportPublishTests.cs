#pragma warning disable CS8618
#pragma warning disable CS8625
#pragma warning disable CS8602
#pragma warning disable CS8600
#pragma warning disable CS8629
#pragma warning disable CS8604

using Framsikt.BL;
using Framsikt.BL.Core.Helpers;
using Framsikt.BL.Helpers;
using Framsikt.BL.Helpers.CkEditorHelpers;
using Framsikt.BL.PublishHelpers;
using Framsikt.BL.Repository;
using Framsikt.DocExportWorker;
using Framsikt.DocExportWorker.Core;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.FeatureManagement;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Framsikt.Integration.Tests
{
    public class BMExportPublishIntegrationTest
    {
        private static IServiceCollection servicesCollection;
        private static IFeatureManager _featureManager;
        private static IAzureStorageAccountClient _azureStorageAccountClient;
        private static IVaultHelper _vaultHelper;

        public static void InitializeServices(IServiceCollection builderservicesCollection, IFeatureManager featureManager, IAzureStorageAccountClient azureStorageAccountClient, IVaultHelper vaultHelper)
        {
            servicesCollection = builderservicesCollection;
            _featureManager = featureManager;
            _azureStorageAccountClient = azureStorageAccountClient;
            _vaultHelper = vaultHelper;
        }

        public static void TestBMDocExport()
        {
            try
            {
                string userName = "<EMAIL>";

                // int clientId = 31;
                // int tenantid = 31;
                // int publishConfigId = 5065;
                // int budgetYear = 2025;
                int clientId = 7684;
                int tenantid = 7684;
                int publishConfigId = 3925;
                int budgetYear = 2025;

                string userId = userName;
                IAppDataCache dc = new AppDataCache(true, "", new CacheClientFactory());

                IUtility utilBL = new Utility(dc, new DbContextManagerTests(dc, userName, tenantid, clientId), null, new DocExportSessionManager(clientId, userName), new CKEditorExtensions(_vaultHelper), _featureManager, _azureStorageAccountClient);
                UserData ud = utilBL.GetUserDetails(userName);

                using (var container = AutofacContainerBootstrapper.ConfigureAutofacContainerForQueues(servicesCollection, ud, budgetYear))
                {
                    Aspose.Words.License asposeWordLicense = new Aspose.Words.License();
                    asposeWordLicense.SetLicense("Aspose.Words.lic");

                    Aspose.Cells.License asposeCellsLicense = new Aspose.Cells.License();
                    asposeCellsLicense.SetLicense("Aspose.Cells.lic");

                    var sut = new BmExportHelper(container);
                    IUtility util = container.GetRequiredService<IUtility>();
                    IAzureBlobHelper azureBlobHelper = container.GetRequiredService<IAzureBlobHelper>();

                    var templateId = util.GetTenantDBContext().tco_publish_config.Find(publishConfigId).fk_template_id;
                    string blobPath = util.GetTenantDBContext().TcoPublishTemplate.Find(templateId).TemplateUrl;

                    string templateFromBlob = azureBlobHelper.GetTextBlobAsync(StorageAccount.AppStorage, BlobContainers.PublishTemplate, blobPath).GetAwaiter().GetResult();
                    PublishTemplateHelper template = JsonConvert.DeserializeObject<PublishTemplateHelper>(templateFromBlob) ?? new PublishTemplateHelper();
                    IEnumerable<PublishTreeNode> exParams = template.Tree;
                    string expParams = JsonConvert.SerializeObject(template.Tree);
                    List<PublishTreeNode> exp = JsonConvert.DeserializeObject<List<PublishTreeNode>>(expParams);

                    bool includeInternalDesc = template.IncludeInternalDesc;
                    string bphaseID = template.BudgetPhaseId;
                    bool showOnlyModified = template.ShowOnlyModified;
                    bool incBlistIntDesc = template.IncludeBlistInternalDesc;


                    string kostraTemplateId = util.GetKostraTemplateId(userId, "OKPLAN").ToString();
                    using (MemoryStream ms = sut.CreateDocument(exp.AsReadOnly(), userId, kostraTemplateId.ToString(), includeInternalDesc, "", bphaseID, showOnlyModified, false, -1, incBlistIntDesc, templateId, string.Empty))
                    {
                        ms.Position = 0;
                        using (FileStream file = new FileStream($@"C:\\test\\bm_doc_{publishConfigId}.docx", FileMode.OpenOrCreate, FileAccess.ReadWrite))
                        {
                            ms.WriteTo(file);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Console.Write(e.ToString());
            }
        }

        public static void TestBMSyncExport() {
            string userName = "<EMAIL>";

            int clientId = 7888;
            int tenantid = 7888;
            int publishConfigId = 12365;
            int budgetYear = 2026;


            IAppDataCache dc = new AppDataCache(true, "", new CacheClientFactory());
            IUtility utilBL = new Utility(dc, new DbContextManagerTests(dc, userName, tenantid, clientId), null, new DocExportSessionManager(clientId, userName), new CKEditorExtensions(_vaultHelper), _featureManager, _azureStorageAccountClient);
            UserData ud = utilBL.GetUserDetails(userName);

            using var container = AutofacContainerBootstrapper.ConfigureAutofacContainerForQueues(servicesCollection, ud, budgetYear);
            
            IUtility util = container.GetRequiredService<IUtility>();
            IAzureBlobHelper azureBlobHelper = container.GetRequiredService<IAzureBlobHelper>();
            var templateId = util.GetTenantDBContext().tco_publish_config.Find(publishConfigId).fk_template_id;
            string blobPath = util.GetTenantDBContext().TcoPublishTemplate.Find(templateId).TemplateUrl;
            string templateFromBlob = azureBlobHelper.GetTextBlobAsync(StorageAccount.AppStorage, BlobContainers.PublishTemplate, blobPath).GetAwaiter().GetResult();
            var documentTree = JsonConvert.DeserializeObject<List<PublishTreeNode>>(templateFromBlob) ?? new List<PublishTreeNode>();

            Aspose.Words.License asposeWordLicense = new Aspose.Words.License();
            asposeWordLicense.SetLicense("Aspose.Words.lic");

            Aspose.Cells.License asposeCellsLicense = new Aspose.Cells.License();
            asposeCellsLicense.SetLicense("Aspose.Cells.lic");
            PublishProcessStageInputHelper publishStageInputHelper = new PublishProcessStageInputHelper()
            {
                orgID = "OBF",
                orgLevel = 1,
                includeInternalDesc = false,
                budgetYear = 2026,
                forecastPeriod = 0,
                documentTree = documentTree,
                treeType = PublishTreeType.BPSyncTree.ToString(),
                fontID = -1,
                configID = -1,
                templateID = 15979,
            };
            SyncDocExport expHelper = new SyncDocExport(container);
            using MemoryStream ms =  expHelper.CreateDocument(documentTree,userName, 15979, 2026,"OBF",1, "b1cdcf72-49d6-4a4d-93ce-f067ed1aa65e",-1, PublishTreeType.BPSyncTree.ToString());
            ms.Position = 0;
            using FileStream file = new FileStream(@"C:\\test\\bmsyncexport.docx", FileMode.OpenOrCreate, FileAccess.ReadWrite);
            ms.WriteTo(file);
        }

        public static void TestBMDocPublish()
        {
            try
            {
                string userName = "<EMAIL>";
                //int clientId = 5006;
                //int tenantid = 5006;
                //int publishConfigId = 18825;
                //int budgetYear = 2025;

                // int clientId = 7684;
                // int tenantid = 7684;
                // int publishConfigId = 3885;
                // int budgetYear = 2026;
                //
                // int clientId = 31;
                // int tenantid = 31;
                // int publishConfigId = 990;
                // int budgetYear = 2025;
                
                int clientId = 7684;
                int tenantid = 7684;
                int publishConfigId = 3925;
                int budgetYear = 2025;

                IAppDataCache dc = new AppDataCache(true, "", new CacheClientFactory());
                IUtility utilBL = new Utility(dc, new DbContextManagerTests(dc, userName, tenantid, clientId), null, new DocExportSessionManager(clientId, userName), new CKEditorExtensions(_vaultHelper), _featureManager, _azureStorageAccountClient);
                UserData ud = utilBL.GetUserDetails(userName);

                using (var container = AutofacContainerBootstrapper.ConfigureAutofacContainerForQueues(servicesCollection, ud, budgetYear, "bmpublishrequestqueue"))
                {
                    Aspose.Words.License asposeWordLicense = new Aspose.Words.License();
                    asposeWordLicense.SetLicense("Aspose.Words.lic");

                    Aspose.Cells.License asposeCellsLicense = new Aspose.Cells.License();
                    asposeCellsLicense.SetLicense("Aspose.Cells.lic");

                    var sut = new BmExportHelper(container);

                    IUtility util = container.GetRequiredService<IUtility>();
                    IAzureBlobHelper azureBlobHelper = container.GetRequiredService<IAzureBlobHelper>();
                    var templateId = util.GetTenantDBContext().tco_publish_config.Find(publishConfigId).fk_template_id;
                    string blobPath = util.GetTenantDBContext().TcoPublishTemplate.Find(templateId).TemplateUrl;
                    string templateFromBlob = azureBlobHelper.GetTextBlobAsync(StorageAccount.AppStorage, BlobContainers.PublishTemplate, blobPath).GetAwaiter().GetResult();
                    PublishTemplateHelper template = JsonConvert.DeserializeObject<PublishTemplateHelper>(templateFromBlob) ?? new PublishTemplateHelper();
                    string kostraTemplateId = Guid.NewGuid().ToString();
                    PublishStatus status = sut.PublishWebsiteToStaging(template, userName, kostraTemplateId, publishConfigId, false, string.Empty, true);
                }
            }
            catch (Exception e)
            {
                Console.Write(e.ToString());
            }
        }
    }
}