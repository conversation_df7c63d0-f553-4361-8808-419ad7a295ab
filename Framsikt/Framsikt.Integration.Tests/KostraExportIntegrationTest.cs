#pragma warning disable CS8618
#pragma warning disable CS8625
#pragma warning disable CS8602
#pragma warning disable CS8600
#pragma warning disable CS8629
#pragma warning disable CS8604

using Framsikt.BL;
using Framsikt.BL.Core.Helpers;
using Framsikt.BL.Helpers;
using Framsikt.BL.Helpers.CkEditorHelpers;
using Framsikt.BL.Repository;
using Framsikt.DocExportWorker;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.FeatureManagement;
using Newtonsoft.Json;

namespace Framsikt.Integration.Tests
{
    public class KostraExportIntegrationTest
    {
        private static IServiceCollection servicesCollection;
        private static IFeatureManager _featureManager;
        private static IAzureStorageAccountClient _azureStorageAccountClient;
        private static IVaultHelper _vaultHelper;

        public static void InitializeServices(IServiceCollection builderservicesCollection, IFeatureManager featureManager, IAzureStorageAccountClient azureStorageAccountClient, IVaultHelper vaultHelper)
        {
            servicesCollection = builderservicesCollection;
            _featureManager = featureManager;
            _azureStorageAccountClient = azureStorageAccountClient;
            _vaultHelper = vaultHelper;
        }

        public static void TestExport()
        {
            try
            {
                string userName = "<EMAIL>";
                int clientId = 7684;
                int tenantId = 7892;
                int budgetYear = 2024;
                var input = File.ReadAllText(@"C:\tree\kostratree.txt");

                IAppDataCache dc = new AppDataCache(true, "", new CacheClientFactory());
                IUtility utilBL = new Utility(dc, new DbContextManagerTests(dc, userName, tenantId, clientId), null, new DocExportSessionManager(clientId, userName), new CKEditorExtensions(_vaultHelper), _featureManager, _azureStorageAccountClient);
                UserData ud = utilBL.GetUserDetails(userName);

                using (var container = AutofacContainerBootstrapper.ConfigureAutofacContainerForQueues(servicesCollection, ud, budgetYear, string.Empty))
                {
                    Aspose.Words.License asposeWordLicense = new Aspose.Words.License();
                    asposeWordLicense.SetLicense("Aspose.Words.lic");

                    Aspose.Cells.License asposeCellsLicense = new Aspose.Cells.License();
                    asposeCellsLicense.SetLicense("Aspose.Cells.lic");

                    ExportHelper exp = new ExportHelper(container);
                    ExportParameters exParams = JsonConvert.DeserializeObject<ExportParameters>(input);
                    using (MemoryStream ms = exp.CreateDocument(exParams, userName))
                    {
                        ms.Position = 0;

                        using (FileStream file = new FileStream($"C:\\docexports\\kostra-{Guid.NewGuid()}.docx", FileMode.OpenOrCreate, FileAccess.ReadWrite))
                        {
                            ms.WriteTo(file);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Console.Write(e.ToString());
            }
        }

        public static void TestEnvironment()
        {
            try
            {
                string userName = "<EMAIL>";
                int clientId = 7684;
                int tenantId = 7892;
                int budgetYear = 2024;

                IAppDataCache dc = new AppDataCache(true, "", new CacheClientFactory());
                IUtility utilBL = new Utility(dc, new DbContextManagerTests(dc, userName, tenantId, clientId), null, new DocExportSessionManager(clientId, userName), new CKEditorExtensions(_vaultHelper), _featureManager, _azureStorageAccountClient);
                UserData ud = utilBL.GetUserDetails(userName);

                using (var container = AutofacContainerBootstrapper.ConfigureAutofacContainerForQueues(servicesCollection, ud, budgetYear, string.Empty))
                {
                    var vaultHelper = container.GetRequiredService<IVaultHelper>();
                    var value = vaultHelper.GetSecret("containerEnvironment");
                }
            }
            catch (Exception e)
            {
                Console.Write(e.ToString());
            }
        }
    }
}