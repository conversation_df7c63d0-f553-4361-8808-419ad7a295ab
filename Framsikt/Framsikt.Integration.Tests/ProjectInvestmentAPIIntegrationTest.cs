#pragma warning disable CS8618
#pragma warning disable CS8625
#pragma warning disable CS8602
#pragma warning disable CS8600
#pragma warning disable CS8629
#pragma warning disable CS8604

using Framsikt.BL;
using Framsikt.BL.Core.Helpers;
using Framsikt.BL.Helpers;
using Framsikt.BL.Helpers.CkEditorHelpers;
using Framsikt.BL.Repository;
using Framsikt.DocExportWorker;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.FeatureManagement;
using Newtonsoft.Json;

namespace Framsikt.Integration.Tests
{
    public class ProjectInvestmentAPIIntegrationTest
    {
        private static IServiceCollection servicesCollection;
        private static IFeatureManager _featureManager;
        private static IAzureStorageAccountClient _azureStorageAccountClient;
        private static IVaultHelper _vaultHelper;

        public static void InitializeServices(IServiceCollection builderservicesCollection, IFeatureManager featureManager, IAzureStorageAccountClient azureStorageAccountClient, IVaultHelper vaultHelper)
        {
            servicesCollection = builderservicesCollection;
            _featureManager = featureManager;
            _azureStorageAccountClient = azureStorageAccountClient;
            _vaultHelper = vaultHelper;
        }

        public static void TestGetInvestmentOverviewGridData()
        {
            try
            {
                int clientId = 31;
                int tenantId = 31;
                string userName = "<EMAIL>";

                IAppDataCache dc = new AppDataCache(true, "", new CacheClientFactory());
                IUtility utilBL = new Utility(dc, new DbContextManagerTests(dc, userName, tenantId, clientId), null, new DocExportSessionManager(clientId, userName), new CKEditorExtensions(_vaultHelper), _featureManager, _azureStorageAccountClient);
                UserData ud = utilBL.GetUserDetails(userName);

                using (var container = AutofacContainerBootstrapper.ConfigureAutofacContainerForQueues(servicesCollection, ud, DateTime.UtcNow.Year))
                {
                    Aspose.Words.License asposeWordLicense = new Aspose.Words.License();
                    asposeWordLicense.SetLicense("Aspose.Words.lic");

                    Aspose.Cells.License asposeCellsLicense = new Aspose.Cells.License();
                    asposeCellsLicense.SetLicense("Aspose.Cells.lic");

                    string payload = File.ReadAllText(@"C:\Not C Drive\Blob Errors\investmentpayload.txt");
                    var inputObject = JsonConvert.DeserializeObject<InvProjectFilters>(payload);

                    var _invProj = container.GetRequiredService<IInvestmentProject>();
                    var dataSet = _invProj.GetInvestmentOveriewDataAsync(userName, inputObject).GetAwaiter().GetResult();
                }
            }
            catch (Exception e)
            {
                Console.Write(e.ToString());
            }
        }
    }
}