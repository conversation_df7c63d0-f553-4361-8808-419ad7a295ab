using Framsikt.BL.Helpers;
using Framsikt.BL.Politician.Helpers;
using KeyValuePair = Framsikt.BL.Helpers.KeyValuePair;

namespace Framsikt.BL.Politician
{
    public interface IPSQuestionAndAnswer
    {
        Task<dynamic> GetQNAOpenDateAsync(int budgetYear, string userId, int questionRoundId);

        Task<List<statusValues>> GetQuestionStatusAsync(string userId, userType userLoginType);

        Task<List<KeyValuePair<int, string>>> GetQuestionAskedByAsync(string userId, int budgetYear);

        Task<List<AdminQuestionRoundGrid>> GetActiveQuestionRoundsAsync(string userId, int budgetYear);

        Task<List<QuestionGrid>> QuestionAskingGridDataAsync(string userId, int budgetYear, GridSearchInput searchInput, int questionRoundId);



        Task<List<AnsweringGrid>> QuestionAnsweringGridDataAsync(string userId, int budgetYear, GridSearchInput searchInput, int questionRoundId);

        List<AnsweringGrid> DelegationGridData(string userId, int budgetYear, GridSearchInput searchInput, int questionRoundId);

        Task<List<AnsweringGrid>> DelegationGridDataAsync(string userId, int budgetYear, GridSearchInput searchInput, int questionRoundId);



        Task<List<AnsweringGrid>> OwnerGridDataAsync(string userId, int budgetYear, GridSearchInput searchInput, int questionRoundId);

        Task<List<AnsweringGrid>> ApproverGridDataAsync(string userId, int budgetYear, GridSearchInput searchInput, int questionRoundId);


        Task<List<KeyValuePair<Guid, string>>> GetPartyListAsync(string userId);



        Task<List<KeyValuePair<Guid, string>>> GetThemeListAsync(string userId, int budgetYear);



        Task<List<KeyValuePair<int, string>>> GetUserListAsync(string userId, bool isOwner, bool isApprover, bool isAnswerer);
        Task<string> AddNewPsQuestionAsync(string userId, int budYear, QuestionGrid questionData);


        Task<QuestionGrid> GetQuestionAndAnswerDataAsync(string userId, Guid questionId);



        Task DeleteQuestionAsync(Guid questionId, string userId, int budgetYear);


        Task<AnsweringDetailPage> GetDetailPageQuestionDetailsAsync(string userId, Guid questionId, int budgetYear, userType loginType);



        Task<string> SaveDetailDataAsync(DetailDataSaveInput detailSaveInput, string userId);



        Task<string> UpdateAnswerAsync(Guid questionId, string userId, int budgetYear, string answer, userType user_type);


        Task<string> GetAnswerAsync(Guid questionId, string userId, int budgetYear);

        Task ApproveAndPublishAsync(List<KeyValuePair<Guid, bool>> publishInput, string userId, int budgetYear);

        Task<List<KeyValuePair>> GetColumnSelectorForQaGridAsync(string userId, int budgetYear, List<GridColumnHelper> colConfig);

        Task SaveColumnSelectorForQaGrid(string userId, List<KeyValuePair> invCols, int budgetYear);

        Task<List<KeyValuePair>> GetColumnSelectorForApproverGridAsync(string userId, int budgetYear, List<GridColumnHelper> colConfig);

        Task SaveColumnSelectorForApproverGrid(string userId, List<KeyValuePair> invCols, int budgetYear);

        Task<string> UpdateQuestionStatusAsync(DetailDataSaveInput detailSaveInput, string userId);


        Task<string> RejectAnswerAsync(RejectionInput input, string userId);


        Task<List<KeyValuePair<int, string>>> GetUserListBasedOnRoleAsync(string userId, int roleId);


        Task<MemoryStream> ExportQuestionAskingGridAsync(string userId, QuestionAskingExcelHelper exportData);

        Task WriteToExpReqQueueForQA(string userId, int budgetYear);

        PublishTemplateHelper GetPoliticalQATemplate(string userId);
        Task<PublishTemplateHelper> GetPoliticalQATemplateAsync(string userId);
        void WithdrawAnswer(DetailDataSaveInput detailSaveInput, string userId);
        Task WithdrawAnswerAsync(DetailDataSaveInput detailSaveInput, string userId);
         List<AnsweringGrid> GetDataForDocExport(string userId, int budgetYear);
        Task<List<AnsweringGrid>> GetDataForDocExportAsync(string userId, int budgetYear);


    }
}