#pragma warning disable CS8629

#pragma warning disable CS8601
#pragma warning disable CS8602
#pragma warning disable CS8603
#pragma warning disable CS8604
#pragma warning disable CS8619

using Framsikt.BL.ApprovalProcess.Helpers;
using Framsikt.BL.ApprovalProcess.Repository;
using Framsikt.BL.Core.BudgetProposalPartial;
using Framsikt.BL.Helpers;
using Framsikt.BL.Reporting;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Framsikt.Entities.Core;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Text;

#pragma warning disable CS8600

namespace Framsikt.BL.ApprovalProcess
{
    public class AdminFinplanAprovalProcess : IAdminFinplanAprovalProcess
    {
        private readonly IAdminFinplanAprovalUoW _unitOfWork;
        private readonly IUtility _utility;
        private readonly IAppDataCache _cache;
        private readonly IBmPublishTree _publishTree;
        private readonly IAzureBlobHelper _azureBlobHelper;
        private readonly IBackendRequest _backendJob;
        private readonly IPublishTemplateManager _publishTemplateManager;
        private readonly IBudgetManagement _budman;
        private readonly IConsequenceAdjustedBudget _conseq;
        private readonly IMonthlyReportingDoc _monthlyReportTree;
        private readonly INotification _notification;
        private readonly IAdminAlert _adminAlert;
        private readonly IConsequenceAdjustedBudget _cab;
        private readonly IInvestmentProject _invProj;
        private const string bm = "bm";
        private const string mr = "mr";
        private readonly IBudgetProposal budProp;
        public const int level1Node = 2;
        public const int level2Node = 3;
        private const string externalText = "external";
        private const string internalText = "internal";
        private const string budgetphaseText = "budgetphase";
        private const string consequenceText = "consequence";

        public AdminFinplanAprovalProcess(IAdminFinplanAprovalUoW uow, IUtility util, IAppDataCache cache, IBmPublishTree publishTree, IAzureBlobHelper azureBlobHelper, IBackendRequest backendJob,
            IPublishTemplateManager publishTemplateManager, IBudgetManagement budman, IConsequenceAdjustedBudget conseq, 
            IMonthlyReportingDoc monthlyReportTree, INotification notification, IAdminAlert adminAlert, IBudgetProposal budgetProposal, IConsequenceAdjustedBudget cab, IInvestmentProject investmentProject)
        {
            _unitOfWork = uow;
            _utility = util;
            _cache = cache;
            _publishTree = publishTree;
            _azureBlobHelper = azureBlobHelper;
            _backendJob = backendJob;
            _publishTemplateManager = publishTemplateManager;
            _budman = budman;
            _conseq = conseq;
            _monthlyReportTree = monthlyReportTree;
            _notification = notification;
            _adminAlert = adminAlert;
            budProp = budgetProposal;
            _cab = cab;
            _invProj = investmentProject;
        }

        public async Task<List<AdminFinPlanAprProcessHelper>> GetApprovalProcessStep1Data(string userId, int budgetYear, int forecastPeriod, PublishTreeType pTreeType)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "AdminFinPlanAproval");
            IEnumerable<int> userRoleById = await _utility.GetUserRoleIdsAsync(userId);
            List<KeyValueWithLevelId> keyValueData = new List<KeyValueWithLevelId>();
            keyValueData.Add(new KeyValueWithLevelId() { Key = DocChanpterId.TotalNodePart.ToString(), level1Id = DocChanpterId.TotalNodePart.ToString(), Value = langString["ADM_FinPlan_TotalPart"].LangText });// total node
            keyValueData.Add(new KeyValueWithLevelId() { Key = DocChanpterId.CommonNode.ToString(), level1Id = DocChanpterId.CommonNode.ToString(), Value = langString["ADM_FinPlan_CommonNode"].LangText });// common node
            if (pTreeType == PublishTreeType.MonthlyReport)
            {
                keyValueData.AddRange(await GetMonthlyReportTreeData(forecastPeriod, userId));
            }
            else
            {
                keyValueData.AddRange(await GetServiceAeaFromDocTree(budgetYear, userId, userRoleById));// service area/serviceIdNode
            }
            var respobUserData = await _unitOfWork.AdminFinplanAprovalRepository.GetAllDocWfResponsiblesData(userDetails.tenant_id, pTreeType.ToString(), budgetYear, forecastPeriod);// get all data from db
            return FormatTableData(keyValueData, respobUserData.ToList());// create table data
        }

        public async Task<List<AdminFinPlanAprProcessTemplateHelper>> GetTemplateList(string userId, int budgetYear, int forecastPeriod, PublishTreeType treeType)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var templateData = await _unitOfWork.AdminFinplanAprovalRepository.GetAllDocPublishTemplate(userDetails.tenant_id, treeType, budgetYear, forecastPeriod);// get all data from db
            tco_docwf_template? docWfTemplate = await _unitOfWork.AdminFinplanAprovalRepository.GetDocWfTemplateData(userDetails.tenant_id, treeType.ToString(), budgetYear, forecastPeriod);
            List<AdminFinPlanAprProcessTemplateHelper> finalData = new List<AdminFinPlanAprProcessTemplateHelper>();
            foreach (var item in templateData.ToList())
            {
                finalData.Add(new AdminFinPlanAprProcessTemplateHelper()
                {
                    KeyId = item.PkId,
                    ValueString = item.Name,
                    IsSelected = docWfTemplate != null && item.PkId == docWfTemplate.fk_template_id,
                    templateUrl = item.TemplateUrl,
                    tenantId = userDetails.tenant_id
                });
            }

            return finalData;
        }

        public async Task<string> GetSelectedTemplateDocVersion(int tenant_id, string templateUrl)
        {
            string templateFromBlob = await _azureBlobHelper.GetTextBlobAsync(StorageAccount.AppStorage, BlobContainers.PublishTemplate, templateUrl);
            var templateData = JsonConvert.DeserializeObject<PublishTemplateHelper>(templateFromBlob);

            return templateData != null && !string.IsNullOrEmpty(templateData.BudgetPhaseId) ? _unitOfWork.AdminFinplanAprovalRepository.GetBudgetPhaseData(tenant_id, Guid.Parse(templateData.BudgetPhaseId)).description : string.Empty;
        }

        public async Task SaveApprovalProcessStep1Data(string userId, int budgetYear, int forecastPeriod, PublishTreeType pTreeType, List<AdminFinPlanAprProcessHelper> inputData, int templateId)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                var respobUserData = await _unitOfWork.AdminFinplanAprovalRepository.GetAllDocWfResponsiblesData(userDetails.tenant_id, pTreeType.ToString(), budgetYear, forecastPeriod);// get all data from db
                var docPartLog = await _unitOfWork.AdminFinplanAprovalRepository.GetAllDocWfdocPartLogData(userDetails.tenant_id, pTreeType.ToString(), budgetYear, templateId, forecastPeriod);// get all data from db

                foreach (var item in inputData)
                {
                    await AddDatatoDB(userDetails, budgetYear, forecastPeriod, pTreeType, respobUserData.ToList(), item);
                    await AddDatatoDocPartLog(userDetails, budgetYear, forecastPeriod, pTreeType, docPartLog.ToList(), item, templateId);
                }
                await _unitOfWork.CompleteAsync();
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        public async Task SaveApprovalProcessTemplateData(string userId, int budgetYear, int forecastPeriod, PublishTreeType pTreeType, int templateId)
        {
            try
            {
                if (templateId != 0)
                {
                    UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                    tco_docwf_template? savedTemplate = await _unitOfWork.AdminFinplanAprovalRepository.GetDocWfTemplateData(userDetails.tenant_id, pTreeType.ToString(), budgetYear, forecastPeriod);// get all data from db

                    if (savedTemplate == null)// save template is empty
                    {
                        tco_docwf_template newData = new tco_docwf_template()
                        {
                            pk_id = Guid.NewGuid(),
                            forecast_period = forecastPeriod,
                            budget_year = budgetYear,
                            fk_template_id = templateId,
                            doc_type = pTreeType.ToString(),
                            updated = DateTime.UtcNow,
                            updated_by = userDetails.pk_id,
                            fk_tenant_id = userDetails.tenant_id
                        };
                        await _unitOfWork.GenericRepo.AddAsync(newData);
                    }
                    else
                    {
                        //update template id
                        savedTemplate.fk_template_id = templateId;
                        savedTemplate.updated = DateTime.UtcNow;
                        savedTemplate.updated_by = userDetails.pk_id;
                    }
                    await _unitOfWork.CompleteAsync();
                }
                else
                {
                    throw new InvalidOperationException();
                }
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        public async Task<List<KeyValueInt>?> GetUserListToShare(string userId, UserListType userType, bool includeInactive)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                var usersList = await GetTcoUserData(userId, includeInactive);
                TimeSpan cacheTimeOut = new TimeSpan(24, 0, 0);
                int clientId = userDetails.client_id;

                string CacheKeyName = GetCacheName(userType);
                List<int> roleIds = GetRoleIds(userType);

                List<KeyValueInt> userDataList = new List<KeyValueInt>();
                string result = await _cache.GetStringForUserAsync(clientId, userDetails.tenant_id, userId, CacheKeyName + "_" + includeInactive);
                if (result == null)
                {
                    AuthenticationDBContext userAdminDBContext = _utility.GetAuthenticationContext();
                    if (roleIds.Any())// return users only if roles are set, else return empty list
                    {
                        List<int> userIdList = await userAdminDBContext.tco_auth_user_role_mapping.Where(x => x.tenant_id == userDetails.tenant_id && roleIds.Contains(x.fk_role_id)).Select(z => z.fk_user_id).Distinct().ToListAsync();

                        userDataList.AddRange(usersList.Where(x => userIdList.Contains(x.pk_id) && x.IsActive).Select(z => new KeyValueInt() { Key = z.pk_id, Value = z.first_name + " " + z.last_name }).ToList());
                        result = JsonConvert.SerializeObject(userDataList.OrderBy(x => x.Value));
                        await _cache.SetStringForUserAsync(clientId, userDetails.tenant_id, userId, CacheKeyName, result, cacheTimeOut);
                    }
                }
                else
                {
                    userDataList = JsonConvert.DeserializeObject<List<KeyValueInt>>(result);
                }

                return userDataList;
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        public async Task<GetSelectedTemplateData> GetSelectedTemplateForYear(ApprovalProcessInputHelper inputObj, string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            GetSelectedTemplateData finalData = new GetSelectedTemplateData();
            tco_docwf_template savedTemplate = await _unitOfWork.AdminFinplanAprovalRepository.GetDocWfTemplateData(userDetails.tenant_id, inputObj.pubTreeType.ToString(), inputObj.budgetYear, inputObj.forecastPeriod);// get all data from db
            TcoPublishTemplate? SelectedtemplateData = savedTemplate != null ? _unitOfWork.AdminFinplanAprovalRepository.GetSelectedDocPublishTemplate(userDetails.tenant_id, savedTemplate.fk_template_id, inputObj.pubTreeType, inputObj.budgetYear, inputObj.forecastPeriod) : null;// get all data from db
            if (savedTemplate != null && SelectedtemplateData != null)
            {
                string templateFromBlob = await _azureBlobHelper.GetTextBlobAsync(StorageAccount.AppStorage, BlobContainers.PublishTemplate, SelectedtemplateData.TemplateUrl);
                var templateData = JsonConvert.DeserializeObject<PublishTemplateHelper>(templateFromBlob);

                finalData.DocVerionId = templateData != null && !string.IsNullOrEmpty(templateData.BudgetPhaseId) ? templateData.BudgetPhaseId : string.Empty;
                finalData.DocVersionName = templateData != null && !string.IsNullOrEmpty(templateData.BudgetPhaseId) ? _unitOfWork.AdminFinplanAprovalRepository.GetBudgetPhaseData(userDetails.tenant_id, Guid.Parse(templateData.BudgetPhaseId)).description : string.Empty;
                finalData.SelectedTemplateId = savedTemplate.fk_template_id;
                finalData.TemplateName = SelectedtemplateData.Name;

                return finalData;
            }
            else
            {
                finalData.DocVerionId = string.Empty;
                finalData.DocVersionName = string.Empty;
                finalData.SelectedTemplateId = 0;
                finalData.TemplateName = string.Empty;

                return finalData;
            }
        }

        public async Task<List<GetApprovalNodeAccessList>> GetUserApprovalNodeList(ApprovalProcessInputHelper inputObj, string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            List<GetApprovalNodeAccessList> finalData = new List<GetApprovalNodeAccessList>();
            var currenUserDataNodeList = await _unitOfWork.AdminFinplanAprovalRepository.GetCurrentUserNodeAccessList(userDetails.tenant_id, inputObj.pubTreeType.ToString(), inputObj.budgetYear, userDetails.pk_id, inputObj.forecastPeriod);// get all data from db

            var processDataList = await _unitOfWork.AdminFinplanAprovalRepository.GetDocWfProcessData(userDetails.tenant_id, inputObj.pubTreeType.ToString(), inputObj.templateId, inputObj.budgetYear, inputObj.forecastPeriod);// get all data from db
            Dictionary<string, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "AdminFinPlanAproval");
            List<KeyValueWithLevelId> keyValueData = inputObj.pubTreeType.ToString() == PublishTreeType.BudgetManagement.ToString() ? await GetTreeData(inputObj.budgetYear, userId) : await GetMonthlyReportTreeData(inputObj.forecastPeriod, userId);// get document tree data

            foreach (var doc_chapter_id in currenUserDataNodeList.Select(Z => Z.doc_chapter_id).Distinct().ToList())// distinct
            {
                finalData.Add(new GetApprovalNodeAccessList()
                {
                    isDisabled = await SetDisableStatus(inputObj.budgetYear, processDataList, doc_chapter_id, keyValueData, userDetails, inputObj.forecastPeriod, inputObj.pubTreeType, inputObj.templateId),
                    NodeId = doc_chapter_id,
                    NodeIdName = SetNodeName(keyValueData, doc_chapter_id, langString),
                    VerionTitle = string.Empty,
                    descGuid = Guid.NewGuid()
                });
            }
            return finalData;
        }

        private async Task<List<KeyValueWithLevelId>> GetMonthlyReportTreeData(int forecastPeriod, string userId)
        {
            List<KeyValueWithLevelId> keyValueData;
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TimeSpan cacheTimeOut = new TimeSpan(24, 0, 0);
            int clientId = userDetails.client_id;
            string result = await _cache.GetStringForTenantAsync(clientId, userDetails.tenant_id, "ADM_BasePublishMRTreeData_" + forecastPeriod);
            int publishTemplateId = -1;
            int budgetYear = forecastPeriod / 100;
            int periodShort = forecastPeriod % 100;
            if (result == null)
            {
                PublishTemplateHelper treeData = await _monthlyReportTree.FinPlanExportTreeAsync(userId, budgetYear, publishTemplateId, periodShort);

                var serviceAreaNodeData = treeData.Tree.Where(x => x.Uid.Contains("ServiceAreaRootCtrl-ServiceAreaRootCtrl")).Select(z => z.items).FirstOrDefault();// get node from tree

                keyValueData = serviceAreaNodeData.Select(x => new KeyValueWithLevelId() { Key = x.Uid, Value = x.text, level1Id = x.id }).ToList();// create key value list
                result = JsonConvert.SerializeObject(keyValueData);
                await _cache.SetStringForTenantAsync(clientId, userDetails.tenant_id, "ADM_BasePublishMRTreeData_" + forecastPeriod, result, cacheTimeOut);
            }
            else
            {
                keyValueData = JsonConvert.DeserializeObject<List<KeyValueWithLevelId>>(result);
            }

            return keyValueData;
        }

        public async Task SendDocForReview(string userId, SendDocForReviewHelper inputObj)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                Guid process_id = Guid.NewGuid();
                tco_docwf_process newData = new tco_docwf_process()
                {
                    owner_user_id = userDetails.pk_id,
                    budget_year = inputObj.budgetYear,
                    doc_chapter_id = inputObj.nodeId,
                    doc_type = inputObj.treeType.ToString(),
                    fk_tenant_id = userDetails.tenant_id,
                    forecast_period = inputObj.forecastPeriod,
                    pk_process_id = process_id,
                    deadline_date = DateTime.Parse(inputObj.deadlineDate),
                    Desc = inputObj.description,
                    Desc_history = inputObj.descGuid,
                    created_date = DateTime.UtcNow,
                    process_status = ProccessStatus.NotStarted.ToString(),
                    updated = DateTime.UtcNow,
                    updated_by = userDetails.pk_id,
                    title = inputObj.verionTitle,
                    template_id = inputObj.templateId,
                };
                await _unitOfWork.GenericRepo.AddAsync(newData);
                // LogProcess(userDetails, process_id, ProccessStatus.Started.ToString());// log
                await AddApproverViewerUsersToDB(process_id, userDetails, inputObj.viewerUserList, inputObj.approverUserList);// add user to db

                await _unitOfWork.CompleteAsync();

                var configId = _budman.SavePublishConfigApprovalProcess(userId, new PublishConfigHelper()
                {
                    budgetYear = inputObj.budgetYear,
                    full_doc_tittle_1 = inputObj.verionTitle,
                    shortName = inputObj.verionTitle,
                    tree_type = inputObj.treeType.ToString(),
                    templateId = inputObj.templateId,
                    forecastPeriod = inputObj.forecastPeriod
                });

                int shortPeriod = inputObj.treeType == PublishTreeType.MonthlyReport ? Convert.ToInt32(inputObj.forecastPeriod.ToString().Substring(4)) : 0;
                PublishTemplateHelper savedTemplate = await _publishTemplateManager.GetPublishTemplateAsync(userId, inputObj.templateId, inputObj.budgetYear, shortPeriod);
                await PublishSelectedProcess(savedTemplate, userId, configId, inputObj.budgetYear, process_id, inputObj.templateId, inputObj.forecastPeriod, inputObj.nodeId, inputObj.treeType);
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        private async Task PublishSelectedProcess(PublishTemplateHelper template, string userId, int configId, int BudgetYear, Guid process_id, int templateId,
                                            int forecastPeriod, string processType, PublishTreeType treeType)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            string strTemplate = JsonConvert.SerializeObject(template);
            string exportTreePath = await InsertJsonRequest(strTemplate, userId);

            PublishType publishType = treeType == PublishTreeType.BudgetManagement ? PublishType.PublishProcessBM : PublishType.PublishProcessMR;

            dynamic queueMessage = new JObject();
            queueMessage.Add("RequestType", publishType.ToString());
            queueMessage.Add("RequestTreePath", exportTreePath);
            queueMessage.Add("UserId", userId);
            queueMessage.Add("TenantId", userDetails.tenant_id);
            queueMessage.Add("ConfigId", configId);
            queueMessage.Add("TemplateId", templateId);
            queueMessage.Add("BudgetYear", BudgetYear);
            queueMessage.Add("ProcessId", process_id);
            queueMessage.Add("ProcessType", processType);
            queueMessage.Add("ForecastPeriod", forecastPeriod);

            string strQueueMessage = JsonConvert.SerializeObject(queueMessage);
            Dictionary<string, int> modBudgetYears = GetBudgetYearsForExport(BudgetYear);

            switch (treeType)
            {
                case PublishTreeType.BudgetManagement:
                    await _backendJob.QueueMessageAsync(userDetails, modBudgetYears, QueueName.bmpublishapprovalprocess, strQueueMessage);
                    break;

                case PublishTreeType.MonthlyReport:
                    await _backendJob.QueueMessageAsync(userDetails, modBudgetYears, QueueName.mrpublishapprovalprocess, strQueueMessage);
                    break;

                default: break;
            }

            var processIdData = await _unitOfWork.AdminFinplanAprovalRepository.GetDocWfProcessDataForSelectedProcessId(userDetails.tenant_id, process_id);
            processIdData.process_status = ProccessStatus.PublishStarted.ToString();
            processIdData.updated = DateTime.UtcNow;
            processIdData.updated_by = userDetails.pk_id;
            _unitOfWork.GenericRepo.Update(processIdData);
            await _unitOfWork.CompleteAsync();
        }

        private async Task<string> InsertJsonRequest(string jsonData, string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            string path = $"{userDetails.tenant_id}-{userDetails.user_name}/{userDetails.tenant_id}-{userDetails.user_name}-{DateTime.UtcNow.ToString("yyyyMMddHHmmss")}.txt";
            await _azureBlobHelper.UploadTextBlobAsync(StorageAccount.AppStorage, BlobContainers.ExportTreeData, path, jsonData);
            return path;
        }

        private static Dictionary<string, int> GetBudgetYearsForExport(int budgetYear)
        {
            Dictionary<string, int> modBudgetYears = new Dictionary<string, int>();
            modBudgetYears.Add("KOSTRA_BUDGET_YEAR", budgetYear - 1);
            modBudgetYears.Add("POPSTAT_BUDGET_YEAR", budgetYear - 1);
            modBudgetYears.Add("BUDGETTASK_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("UTILITY_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("BUDGETASSUMPTION_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("BUDMAN_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("BUDPROP_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("CAB_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("INVESTMENTS_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("FINANCING_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("KPIDATA_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("OPPASSMNT_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("STAFFPLAN_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("YEARLYBUDGET_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("BUDGETREGULATION_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("POPFCAST_POPEXP_GROWTH_STARTYR", budgetYear - 1);
            return modBudgetYears;
        }

        public async Task<ApproverViewListHelper> GetApproverViewerUserForSelectNode(string userId, string processId)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                var UserDataNodeList = await _unitOfWork.AdminFinplanAprovalRepository.GetAproverViewerUserDataForProcess(userDetails.tenant_id, Guid.Parse(processId));// get all data from db

                ApproverViewListHelper finalDta = new ApproverViewListHelper();
                finalDta.approverUserList = UserDataNodeList.Where(x => x.User_type == (int)UserListType.ApproverUserList).Select(x => x.fk_user_id).ToList();
                finalDta.prevApproverUserList = UserDataNodeList.Where(x => x.User_type == (int)UserListType.ApproverUserList).Select(x => x.fk_user_id).ToList();
                finalDta.viewerUserList = UserDataNodeList.Where(x => x.User_type == (int)UserListType.ViewerUserList).Select(x => x.fk_user_id).ToList();
                finalDta.prevViewerUserList = UserDataNodeList.Where(x => x.User_type == (int)UserListType.ViewerUserList).Select(x => x.fk_user_id).ToList();
                finalDta.processId = Guid.Parse(processId);
                return finalDta;
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        public async Task UpdateApproverViewerUserForProcess(string userId, ApproverViewListHelper saveDAta)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                var UserDataNodeList = await _unitOfWork.AdminFinplanAprovalRepository.GetAproverViewerUserDataForProcess(userDetails.tenant_id, Guid.Parse(saveDAta.processId.ToString()));// get all data from db
                Dictionary<string, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "AdminFinPlanAproval");

                Uri bmApprovalUrl;
                string strUrl = (saveDAta.treeType == PublishTreeType.BudgetManagement) ? ("/FinPlanAdminApproval/FinPlanAdminApprovalFlow?FromNotification=true&processId=" + saveDAta.processId + "&template=" + saveDAta.templateId + "&budgetYear=" + saveDAta.budgetYear).ToLower() : ("/MRAdminApproval/MRAdminApprovalFlow?FromNotification=true&processId=" + saveDAta.processId + "&template=" + saveDAta.templateId + "&forcastPeriod=" + saveDAta.forecastPeriod).ToLower();
                Uri.TryCreate(strUrl, UriKind.RelativeOrAbsolute, out bmApprovalUrl);
                var allUserList = await _unitOfWork.AdminFinplanAprovalRepository.GetAllUsers(true);
                Dictionary<string, clsLanguageString> notificationStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference,
                    userId, "Notifications");

                // insert viewer
                foreach (var vUser in saveDAta.viewerUserList)
                {
                    if (UserDataNodeList == null || UserDataNodeList.FirstOrDefault(z => z.fk_user_id == vUser && z.User_type == (int)UserListType.ViewerUserList) == null)
                    {
                        tco_docwf_process_approver_viewer newData = new tco_docwf_process_approver_viewer()
                        {
                            fk_process_id = saveDAta.processId,
                            fk_tenant_id = userDetails.tenant_id,
                            User_type = (int)UserListType.ViewerUserList,
                            fk_user_id = vUser,
                            updated = DateTime.UtcNow,
                            updated_by = userDetails.pk_id,
                            is_mail_alert = false
                        };
                        await _unitOfWork.GenericRepo.AddAsync(newData);

                        Guid notId = Guid.NewGuid();
                        string notificationMsg = notificationStrings["ntf_approval_pub_done_viewer"].LangText;
                        CreatApprovalNotification(_notification, userDetails.tenant_id, notificationMsg, bmApprovalUrl, notId, allUserList.FirstOrDefault(x => x.pk_id == vUser).user_name);
                    }
                }
                // RemoveUserData(saveDAta, UserListType.ViewerUserList, userDetails.tenant_id);// remove removed user from db with user_type as viewer

                // insert approver
                foreach (var vUser in saveDAta.approverUserList)
                {
                    if (UserDataNodeList == null || UserDataNodeList.FirstOrDefault(z => z.fk_user_id == vUser && z.User_type == (int)UserListType.ApproverUserList) == null)
                    {
                        tco_docwf_process_approver_viewer newData = new tco_docwf_process_approver_viewer()
                        {
                            fk_process_id = saveDAta.processId,
                            fk_tenant_id = userDetails.tenant_id,
                            User_type = (int)UserListType.ApproverUserList,
                            fk_user_id = vUser,
                            updated = DateTime.UtcNow,
                            updated_by = userDetails.pk_id,
                            is_mail_alert = false
                        };
                        await _unitOfWork.GenericRepo.AddAsync(newData);

                        Guid notId = Guid.NewGuid();
                        string notificationMsg = notificationStrings["ntf_approval_pub_done_approver"].LangText;
                        CreatApprovalNotification(_notification, userDetails.tenant_id, notificationMsg, bmApprovalUrl, notId, allUserList.FirstOrDefault(x => x.pk_id == vUser).user_name);
                    }
                }
                // RemoveUserData(saveDAta, UserListType.ApproverUserList, userDetails.tenant_id);// remove removed user from db with user_type as viewer

                await _unitOfWork.CompleteAsync();

                var processDetail = await _unitOfWork.AdminFinplanAprovalRepository.GetDocWfProcessDataForSelectedProcessId(userDetails.tenant_id, saveDAta.processId);

                string documentName = string.Empty;

                if (processDetail.doc_chapter_id == DocChanpterId.CommonNode.ToString() || processDetail.doc_chapter_id == DocChanpterId.TotalNodePart.ToString())
                {
                    switch (processDetail.doc_chapter_id)
                    {
                        case "CommonNode":
                            documentName = langString["ADM_FinPlan_CommonNode"].LangText;
                            break;

                        case "TotalNodePart":
                            documentName = langString["ADM_FinPlan_TotalPart"].LangText;
                            break;
                    }
                }
                else
                {
                    documentName = await _unitOfWork.AdminFinplanAprovalRepository.GetDocChapterText(userDetails.tenant_id, saveDAta.treeType.ToString(), saveDAta.templateId, saveDAta.processId, processDetail.doc_chapter_id);
                }

                await _adminAlert.SendMessageToApprovalDocumentReady(userId, saveDAta.processId.ToString(), saveDAta.treeType, string.Empty, saveDAta.budgetYear, saveDAta.forecastPeriod, documentName, saveDAta.treeType == PublishTreeType.BudgetManagement ? MasterAlerts.BMApprovalDocumentSendToNewUsers : MasterAlerts.MRApprovalDocumentSendToNewUsers);
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        public async Task<List<ProcessAppovalViewerGridHelper>> GetApproverViewerGrid(string userId, ApprovalProcessInputHelper inputObj, int templateId)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);

                var responsibleNodeAccess = await _unitOfWork.AdminFinplanAprovalRepository.GetAllDocWfResponsiblesDataForCurrentUser(userDetails.tenant_id, inputObj.pubTreeType.ToString(), userDetails.pk_id, inputObj.budgetYear, inputObj.forecastPeriod);// get all chapter access to current user from tco_docwf_responsibles
                List<string> chapterNodeList = responsibleNodeAccess.Select(z => z.doc_chapter_id).Distinct().ToList();
                var processDataList = await _unitOfWork.AdminFinplanAprovalRepository.GetAllDocWfProcessDataByNodeList(userDetails.tenant_id, inputObj.pubTreeType.ToString(), chapterNodeList, inputObj.budgetYear, inputObj.forecastPeriod);// get all data from db for accessible tco_docwf_process
                processDataList = processDataList.Where(x => x.template_id == templateId).ToList();
                List<Guid> processPkId = processDataList.Select(x => x.pk_process_id).ToList();
                var UserDataNodeList = await _unitOfWork.AdminFinplanAprovalRepository.GetAllAproverViewerUserDataForProcess(userDetails.tenant_id, processPkId);// get all data from tco_docwf_process_approver_viewer

                var usersList = await GetTcoUserData(userId, true);
                Dictionary<string, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "AdminFinPlanAproval");
                List<KeyValueWithLevelId> keyValueData = new List<KeyValueWithLevelId>();
                keyValueData.Add(new KeyValueWithLevelId() { Key = DocChanpterId.TotalNodePart.ToString(), level1Id = DocChanpterId.TotalNodePart.ToString(), Value = langString["ADM_FinPlan_TotalPart"].LangText });// total node
                keyValueData.Add(new KeyValueWithLevelId() { Key = DocChanpterId.CommonNode.ToString(), level1Id = DocChanpterId.TotalNodePart.ToString(), Value = langString["ADM_FinPlan_CommonNode"].LangText });// common node
                keyValueData.AddRange(inputObj.pubTreeType.ToString() == PublishTreeType.BudgetManagement.ToString() ? await GetTreeData(inputObj.budgetYear, userId) : await GetMonthlyReportTreeData(inputObj.forecastPeriod, userId));// service area/serviceIdNode
                List<ProcessAppovalViewerGridHelper> finalDta2 = new List<ProcessAppovalViewerGridHelper>();

                foreach (var item in processDataList.OrderByDescending(x => x.updated).ToList())
                {
                    ProcessAppovalViewerGridHelper temp = new ProcessAppovalViewerGridHelper();
                    temp.nodeId = item.doc_chapter_id;
                    temp.nodeIdName = keyValueData.FirstOrDefault(x => x.Key.ToLower() == item.doc_chapter_id.ToLower()).Value;
                    temp.proecessSatusId = item.process_status;
                    temp.proecessStatusName = SetProcessStatusText(item.process_status, langString);
                    temp.VerionTitle = item.title;
                    temp.budgetYear = item.budget_year;
                    temp.createdDate = item.created_date;
                    temp.deadlineDate = item.deadline_date;
                    temp.desc = item.Desc;
                    temp.processId = item.pk_process_id;
                    temp.processDetails = CreateProcessStringHTML(UserDataNodeList, item.pk_process_id, item.Desc, usersList, langString, keyValueData.FirstOrDefault(x => x.Key.ToLower() == item.doc_chapter_id.ToLower()).Value);
                    temp.processDetailsStr = langString["ADM_Flow_Grid_processDetailsStr"].LangText;
                    temp.logUrl = langString["ADM_Flow_log_url_text"].LangText;
                    temp.editUrl = SetEditUptedDisplayText(item.process_status, langString);
                    temp.recall = item.process_status.ToLower() == "started" ? langString["ADM_Flow_recall"].LangText : string.Empty;

                    finalDta2.Add(temp);
                }
                return finalDta2;
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        public async Task UpdateProcessStatus(string userId, ProcessRecallHelper saveData)
        {
            try
            {
                if (saveData.processId != Guid.Empty)
                {
                    UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                    var processData = await _unitOfWork.AdminFinplanAprovalRepository.GetDocWfProcessDataForSelectedProcessId(userDetails.tenant_id, saveData.processId);
                    UserListType userType = UserListType.BudgetCoordinatorUserList;
                    // add record in tco_docwf_proccess_log
                    await InsertIntoDocProcessLogforRecall(userDetails, saveData, userType);
                    // status update to tco_docwf_process
                    UpdateRecalledStatustoProcess(userDetails, processData);

                    await _unitOfWork.CompleteAsync();
                }
                else
                {
                    throw new InvalidOperationException("incorrect Process Id");
                }
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        private async Task InsertIntoDocProcessLogforRecall(UserData userDetails, ProcessRecallHelper saveData, UserListType userType)
        {
            try
            {
                tco_docwf_proccess_log newData = new tco_docwf_proccess_log()
                {
                    fk_process_id = saveData.processId,
                    fk_tenant_id = userDetails.tenant_id,
                    comments = saveData.comment,
                    log_action = ProccessLogAction.Recalled.ToString(),
                    updated = DateTime.UtcNow,
                    updated_by = userDetails.pk_id,
                    user_type = userType.ToString()
                };
                await _unitOfWork.GenericRepo.AddAsync(newData);
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        private static void UpdateRecalledStatustoProcess(UserData userDetails, tco_docwf_process processData)
        {
            processData.process_status = ProccessStatus.Recalled.ToString();
            processData.updated = DateTime.UtcNow;
            processData.updated_by = userDetails.pk_id;
        }

        public async Task<List<KeyValueNewDataLogData>> GetProcessLogDropDown(string userId, ApprovalProcessInputHelper inputData)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                Dictionary<string, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "AdminFinPlanAproval");
                var responsibleNodeAccess = await _unitOfWork.AdminFinplanAprovalRepository.GetAllDocWfResponsiblesDataForCurrentUser(userDetails.tenant_id, inputData.pubTreeType.ToString(), userDetails.pk_id, inputData.budgetYear, inputData.forecastPeriod);// get all chapter access to current user from tco_docwf_responsibles
                List<string> chapterNodeList = responsibleNodeAccess.Select(z => z.doc_chapter_id).Distinct().ToList();

                var processDataList = await _unitOfWork.AdminFinplanAprovalRepository.GetAllDocWfProcessData(userDetails.tenant_id, inputData.pubTreeType.ToString(), inputData.budgetYear, inputData.forecastPeriod);// get all data from db for accessible tco_docwf_process
                List<Guid> allProcessPkId = processDataList.Select(z => z.pk_process_id).ToList();

                var ViwerApproverAccessProcessList = await _unitOfWork.AdminFinplanAprovalRepository.GetAproverViewerDataForCurrentUser(userDetails.tenant_id, userDetails.pk_id, allProcessPkId);// get viwer approver data for current user from tco_docwf_process_approver_viewer

                List<Guid> viewerApproverProcessIdList = ViwerApproverAccessProcessList.Select(z => z.fk_process_id).Distinct().ToList();

                processDataList = chapterNodeList.Any() ? processDataList.Where(x => (x.owner_user_id == userDetails.pk_id || viewerApproverProcessIdList.Contains(x.pk_process_id) || chapterNodeList.Contains(x.doc_chapter_id))).ToList() : processDataList.Where(x => (x.owner_user_id == userDetails.pk_id || viewerApproverProcessIdList.Contains(x.pk_process_id))).ToList();// select only those data where current user is the creator or has access to as Approver or viewer

                List<KeyValueGuidData> nodeIdProcessIdList = processDataList.Select(z => new KeyValueGuidData() { Key = z.pk_process_id, Value = z.doc_chapter_id }).ToList();

                List<string> nodeIdList = processDataList.Select(x => x.doc_chapter_id).Distinct().ToList();

                List<KeyValueNewDataLogData> docNodeList = new List<KeyValueNewDataLogData>();
                //tree data
                List<KeyValueWithLevelId> Treedata = new List<KeyValueWithLevelId>();
                Treedata.Add(new KeyValueWithLevelId() { Key = DocChanpterId.TotalNodePart.ToString(), level1Id = DocChanpterId.TotalNodePart.ToString(), Value = langString["ADM_FinPlan_TotalPart"].LangText });// total node
                Treedata.Add(new KeyValueWithLevelId() { Key = DocChanpterId.CommonNode.ToString(), level1Id = DocChanpterId.CommonNode.ToString(), Value = langString["ADM_FinPlan_CommonNode"].LangText });// common node
                if (inputData.pubTreeType.ToString() == PublishTreeType.BudgetManagement.ToString())
                {
                    Treedata.AddRange(await GetTreeData(inputData.budgetYear, userId));// BM service area/serviceIdNode
                }
                else
                {
                    Treedata.AddRange(await GetMonthlyReportTreeData(inputData.forecastPeriod, userId));// MR service area/serviceIdNode
                }

                docNodeList.Add(new KeyValueNewDataLogData() { Key = "0", Value = langString["ADM_Flow_Grid_SelectOption"].LangText, ProcessIdList = nodeIdProcessIdList.Select(x => x.Key).Distinct().ToList() });// select opton
                foreach (var item in nodeIdList)
                {
                    KeyValueNewDataLogData temp = new KeyValueNewDataLogData() { Key = item, Value = Treedata.FirstOrDefault(x => x.Key == item).Value, ProcessIdList = nodeIdProcessIdList.Where(s => s.Value == item).Select(y => y.Key).ToList() };// actual data
                    docNodeList.Add(temp);
                }

                return docNodeList;
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        public async Task<ProcessLogClsHelper> GetProcessLogGrid(string userId, ApproverLogGridInputHelper inputData)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                var usersList = await GetTcoUserData(userId, true);
                var responsibleNodeAccess = await _unitOfWork.AdminFinplanAprovalRepository.GetAllDocWfResponsiblesDataForCurrentUser(userDetails.tenant_id, inputData.pubTreeType.ToString(), userDetails.pk_id, inputData.budgetYear, inputData.forecastPeriod);// get all chapter access to current user from tco_docwf_responsibles
                List<string> chapterNodeList = responsibleNodeAccess.Select(z => z.doc_chapter_id).Distinct().ToList();

                var processDataList = await _unitOfWork.AdminFinplanAprovalRepository.GetAllDocWfProcessData(userDetails.tenant_id, inputData.pubTreeType.ToString(), inputData.budgetYear, inputData.forecastPeriod);// get all data from db for accessible tco_docwf_process
                processDataList = FilterProcessData(processDataList, inputData.nodeId);
                List<Guid> processPkId = processDataList.Select(x => x.pk_process_id).ToList();

                var ViwerApproverAccessProcessList = await _unitOfWork.AdminFinplanAprovalRepository.GetAproverViewerDataForCurrentUser(userDetails.tenant_id, userDetails.pk_id, processPkId);// get viwer approver data for current user from tco_docwf_process_approver_viewer

                List<Guid> viewerApproverProcessIdList = ViwerApproverAccessProcessList.Select(z => z.fk_process_id).Distinct().ToList();
                processDataList = chapterNodeList.Any() ? processDataList.Where(x => (x.owner_user_id == userDetails.pk_id || viewerApproverProcessIdList.Contains(x.pk_process_id) || chapterNodeList.Contains(x.doc_chapter_id))).ToList() : processDataList.Where(x => (x.owner_user_id == userDetails.pk_id || viewerApproverProcessIdList.Contains(x.pk_process_id))).ToList();
                processPkId = processDataList.Select(x => x.pk_process_id).ToList();

                var processLogDataList = await _unitOfWork.AdminFinplanAprovalRepository.GetAllDocWfProcessLogData(userDetails.tenant_id, processPkId);// get all data from tco_docwf_proccess_log
                var UserDataNodeList = await _unitOfWork.AdminFinplanAprovalRepository.GetAllAproverViewerUserDataForProcess(userDetails.tenant_id, processPkId);// get all data from from tco_docwf_process_approver_viewer
                Dictionary<string, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "AdminFinPlanAproval");

                //tree data
                List<KeyValueWithLevelId> Treedata = new List<KeyValueWithLevelId>();
                Treedata.Add(new KeyValueWithLevelId() { Key = nameof(DocChanpterId.TotalNodePart), level1Id = nameof(DocChanpterId.TotalNodePart), Value = langString["ADM_FinPlan_TotalPart"].LangText });// total node
                Treedata.Add(new KeyValueWithLevelId() { Key = nameof(DocChanpterId.CommonNode), level1Id = nameof(DocChanpterId.CommonNode), Value = langString["ADM_FinPlan_CommonNode"].LangText });// common node
                if (inputData.pubTreeType.ToString() == nameof(PublishTreeType.BudgetManagement))
                {
                    Treedata.AddRange(await GetTreeData(inputData.budgetYear, userId));// BM service area/serviceIdNode
                }
                else
                {
                    Treedata.AddRange(await GetMonthlyReportTreeData(inputData.forecastPeriod, userId));// MR service area/serviceIdNode
                }

                ProcessLogClsHelper finalObject = new ProcessLogClsHelper();
                List<KeyValueGuidLogData> VersionTitleList = new List<KeyValueGuidLogData>();
                VersionTitleList.Add(new KeyValueGuidLogData() { Key = Guid.Empty, Value = langString.FirstOrDefault(x => x.Key.Equals("ADM_Flow_Grid_SelectOption", StringComparison.InvariantCultureIgnoreCase)).Value.LangText });

                List<KeyValueNewData> StatusList = new List<KeyValueNewData>();
                StatusList.Add(new KeyValueNewData() { Key = "-1", Value = langString.FirstOrDefault(x => x.Key.Equals("ADM_Flow_Grid_SelectOption", StringComparison.InvariantCultureIgnoreCase)).Value.LangText });

                List<ProcessLogGridHelper> finalData = new List<ProcessLogGridHelper>();
                List<keyvaluewithGuid> nodeIdProcessIdList = processDataList.Select(z => new keyvaluewithGuid() { key = z.pk_process_id, value = z.doc_chapter_id }).ToList();
                int id = 1;
                foreach (var item in processLogDataList.OrderByDescending(z => z.updated))
                {
                    string currentNodeId = (nodeIdProcessIdList.FirstOrDefault(y => y.key == item.fk_process_id).value);
                    var itemUser = usersList.FirstOrDefault(x => x.pk_id == item.updated_by);
                    ProcessLogGridHelper temp = new ProcessLogGridHelper();
                    temp.id = id;
                    temp.processId = item.fk_process_id;
                    temp.lastUpdateBy = itemUser.first_name + " " + itemUser.last_name;
                    temp.lastUpdated = item.updated;
                    temp.lastUpdatedStr = item.updated.ToShortDateString();
                    temp.processDetails = InsertProcessLogDetailHtml(UserDataNodeList, item, processDataList, usersList, langString, Treedata, currentNodeId);
                    temp.processDetailsStr = langString["ADM_Flow_Grid_processDetailsStr"].LangText;
                    temp.proecessLogSatusId = item.log_action;
                    temp.proecessLogStatusName = SetProcessStatusText(item.log_action, langString);
                    temp.processTitle = processDataList.FirstOrDefault(Z => Z.pk_process_id == item.fk_process_id).title;
                    temp.userType = langString["ADM_Flow_Grid_UserType_" + item.user_type].LangText;
                    finalData.Add(temp);
                    id++;

                    if (!(VersionTitleList.Any(x => x.Key == item.fk_process_id)))
                    {
                        VersionTitleList.Add(new KeyValueGuidLogData() { Key = item.fk_process_id, Value = processDataList.FirstOrDefault(Z => Z.pk_process_id == item.fk_process_id).title });
                    }
                    if (!(StatusList.Any(x => x.Key == item.log_action)))
                    {
                        StatusList.Add(new KeyValueNewData() { Key = item.log_action, Value = SetProcessStatusText(item.log_action, langString) });
                    }
                }

                finalData = SearchLogGrid(finalData, inputData.searchInput);
                finalObject.LogData = finalData;
                finalObject.VersionTitleList = VersionTitleList;
                finalObject.StatusList = StatusList;
                return finalObject;
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        public async Task SaveAprovalStatusProcessLog(string userId, AprovalStatusProcessLogSaveHelper saveObj)
        {
            try
            {
                if (saveObj.processId != Guid.Empty)
                {
                    UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                    List<Guid> proccessLogIdList = new List<Guid>();
                    proccessLogIdList.Add(saveObj.processId);

                    var UserDataNodeList = await _unitOfWork.AdminFinplanAprovalRepository.GetAllAproverViewerUserDataForProcess(userDetails.tenant_id, proccessLogIdList);// get all data from db
                    var processData = await _unitOfWork.AdminFinplanAprovalRepository.GetDocWfProcessDataForSelectedProcessId(userDetails.tenant_id, saveObj.processId);// get all data from db
                    tco_docwf_proccess_log newData = new tco_docwf_proccess_log()
                    {
                        fk_process_id = saveObj.processId,
                        fk_tenant_id = userDetails.tenant_id,
                        comments = saveObj.comment,
                        log_action = saveObj.logAction.ToString(),
                        updated = DateTime.UtcNow,
                        updated_by = userDetails.pk_id,
                        user_type = saveObj.userType.ToString(),
                        fk_progress_status_id = saveObj.statusId,
                    };
                    await _unitOfWork.GenericRepo.AddAsync(newData);
                    await _unitOfWork.CompleteAsync();
                    var processLogDataList = await _unitOfWork.AdminFinplanAprovalRepository.GetAllDocWfProcessLogData(userDetails.tenant_id, proccessLogIdList);// get all data from db
                    await CheckAndSetReviewDoneStatusForProcess(processLogDataList, UserDataNodeList, processData);
                    if (processData.process_status == ProccessStatus.ReviewProcessDone.ToString())// trigger notification to BudgetCordinator if process status is Review done
                    {
                        await TriggerNotificationToUser(userDetails, ProccessNotificationType.ReviewProcessDone, processData, processData.doc_type);
                    }
                }
                else
                {
                    throw new InvalidOperationException("inccorrect Process Id");
                }
            }
            catch (Exception e)
            {
                throw new InvalidOperationException(e.Message.ToString());
            }
        }

        public async Task<AprovalStatusProcessLogGetHelper> GetApprovalStatusDropDown(string userId, Guid processPkId, progressStatus_type statusType)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                AprovalStatusProcessLogGetHelper finalObject = new AprovalStatusProcessLogGetHelper();
                var processLogDataList = await _unitOfWork.AdminFinplanAprovalRepository.GetAllProgress_status(userDetails.tenant_id, statusType.ToString());// get all data from db
                List<Guid> proccessLogIdList = new List<Guid>();
                proccessLogIdList.Add(processPkId);
                var UserDataList = await _unitOfWork.AdminFinplanAprovalRepository.GetAllAproverViewerUserDataForProcess(userDetails.tenant_id, proccessLogIdList);// get all data from db
                var currentUserType = UserDataList.FirstOrDefault(x => x.User_type == (int)UserListType.ApproverUserList && x.fk_user_id == userDetails.pk_id);

                finalObject.StatusDropDownList = processLogDataList.ToList();
                finalObject.showStatusDropDown = (currentUserType != null);

                return finalObject;
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        public async Task<List<ProcessNodeDropDownHelper>> GetProcessNodeDropDown(string userId, int templateId, ApprovalProcessInputHelper inputData)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                Dictionary<string, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "AdminFinPlanAproval");
                List<ProcessNodeDropDownHelper> finalObject = new List<ProcessNodeDropDownHelper>();

                var docWfProcessList = await _unitOfWork.AdminFinplanAprovalRepository.GetDocWfProcessDataForTemplate(userDetails.tenant_id, inputData.pubTreeType.ToString(), templateId);// get all data from db
                List<KeyValueWithLevelId> keyValueData = new List<KeyValueWithLevelId>
                {
                    new() { Key = nameof(DocChanpterId.TotalNodePart), level1Id = nameof(DocChanpterId.TotalNodePart), Value = langString["ADM_FinPlan_TotalPart"].LangText }, // total node
                    new() { Key = nameof(DocChanpterId.CommonNode), level1Id = nameof(DocChanpterId.CommonNode), Value = langString["ADM_FinPlan_CommonNode"].LangText } // common node
                };
                
                keyValueData.AddRange(inputData.pubTreeType.ToString() == nameof(PublishTreeType.BudgetManagement) ? await GetTreeData(inputData.budgetYear, userId) : await GetMonthlyReportTreeData(inputData.forecastPeriod, userId));// get document tree data
                List<Guid> proccessLogIdList = docWfProcessList.Select(x => x.pk_process_id).ToList();
                var UserDataList = await _unitOfWork.AdminFinplanAprovalRepository.GetCurrentUserAproverViewerUserDataForProcess(userDetails.tenant_id, proccessLogIdList, userDetails.pk_id);// get all data from db
                var responsibleNodeAccess = await _unitOfWork.AdminFinplanAprovalRepository.GetAllDocWfResponsiblesDataForCurrentUser(userDetails.tenant_id, inputData.pubTreeType.ToString(), userDetails.pk_id, inputData.budgetYear, inputData.forecastPeriod);// get all chapter access to current user from tco_docwf_responsibles
                var userProcessLogs =
                    (await _unitOfWork.AdminFinplanAprovalRepository.GetAllDocWfProcessLogData(userDetails.tenant_id, proccessLogIdList))
                    .Where(x => x.updated_by == userDetails.pk_id).OrderByDescending(x => x.updated).ToList();
                
                ProcessNodeDropDownHelper temp;
                foreach (var process in docWfProcessList)
                {
                    var isCordinator = responsibleNodeAccess.Where(x => x.doc_chapter_id == process.doc_chapter_id)
                        .Select(z => z.fk_user_id).Contains(userDetails.pk_id);

                    if (isCordinator || UserDataList.FirstOrDefault(x =>
                            x.fk_user_id == userDetails.pk_id && x.fk_process_id == process.pk_process_id) !=
                        null) // check if current user is there in approver viewer
                    {
                        temp = new ProcessNodeDropDownHelper
                        {
                            userType = SetUserTypeForProcessNodeDropDown(process, UserDataList, userDetails.pk_id,
                                userProcessLogs, isCordinator),
                            key = process.pk_process_id,
                            value = "[" + process.title + "]" + " " +
                                    SetNodeName(keyValueData, process.doc_chapter_id, langString),
                            processStatus = process.process_status
                        };
                        finalObject.Add(temp);
                    }
                }

                return finalObject;
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        public async Task<string> GetSelectedProcessDetails(string userId, Guid proccessId, ApprovalProcessInputHelper approvalProcessInput)
        {
            try
            {
                if (proccessId != Guid.Empty)
                {
                    UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                    List<KeyValueWithLevelId> keyValueData = new List<KeyValueWithLevelId>();

                    List<Guid> processPkId = new List<Guid>();
                    processPkId.Add(proccessId);
                    var processData = await _unitOfWork.AdminFinplanAprovalRepository.GetDocWfProcessDataForSelectedProcessId(userDetails.tenant_id, proccessId);// get all data from db
                    var usersList = await GetTcoUserData(userId, true);
                    var UserDataNodeList = await _unitOfWork.AdminFinplanAprovalRepository.GetAllAproverViewerUserDataForProcess(userDetails.tenant_id, processPkId);// get all data from db
                    Dictionary<string, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "AdminFinPlanAproval");
                    keyValueData.Add(new KeyValueWithLevelId() { Key = DocChanpterId.TotalNodePart.ToString(), level1Id = DocChanpterId.TotalNodePart.ToString(), Value = langString["ADM_FinPlan_TotalPart"].LangText });// total node
                    keyValueData.Add(new KeyValueWithLevelId() { Key = DocChanpterId.CommonNode.ToString(), level1Id = DocChanpterId.CommonNode.ToString(), Value = langString["ADM_FinPlan_CommonNode"].LangText });// common node
                    keyValueData.AddRange(approvalProcessInput.pubTreeType.ToString() == PublishTreeType.BudgetManagement.ToString() ? await GetTreeData(approvalProcessInput.budgetYear, userId) : await GetMonthlyReportTreeData(approvalProcessInput.forecastPeriod, userId));// service area/serviceIdNode
                    return processData != null ? CreateProcessStringHTML(UserDataNodeList, processData.pk_process_id, processData.Desc, usersList, langString, keyValueData.FirstOrDefault(x => x.Key == processData.doc_chapter_id).Value) : string.Empty;
                }
                else
                {
                    throw new InvalidOperationException("invalid process id");
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException(ex.Message);
            }
        }

        public async Task<PublishTemplateHelper> GetProcessParentTree(string userId, int templateId, PublishTreeType treeType, string parentId, Guid processId, int chapterLevel)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantData tenantData = await _utility.GetTenantDataAsync(userId);

            PublishTemplateHelper parentTree = new PublishTemplateHelper();
            var fpLevel2Value = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_2");
            bool isServiceId = false;
            if (!string.IsNullOrEmpty(fpLevel2Value) && fpLevel2Value.Contains("service"))
            {
                isServiceId = true;
            }
            IEnumerable<tco_publish_review_child> treeItems;

            var processData = await _unitOfWork.AdminFinplanAprovalRepository.GetDocWfProcessDataForSelectedProcessId(userDetails.tenant_id, processId);

            if (string.IsNullOrEmpty(parentId) &&
                processData.doc_chapter_id != DocChanpterId.CommonNode.ToString() && processData.doc_chapter_id != DocChanpterId.TotalNodePart.ToString())
            {
                treeItems = await _unitOfWork.AdminFinplanAprovalRepository.GetProcessTreeDataForSAProcess(userDetails.tenant_id, templateId, treeType, processId);
                var rootNode = treeItems.FirstOrDefault(y => y.parent == "");
                var serviceAreaRoot = treeItems.FirstOrDefault(x => x.parent == rootNode.uid);
                treeItems = treeItems.Where(x => x.parent == serviceAreaRoot.uid);
            }
            else
            {
                treeItems = await _unitOfWork.AdminFinplanAprovalRepository.GetProcessTreeData(userDetails.tenant_id, templateId, treeType, parentId, processId);
            }

            string parentNodeId = await _unitOfWork.AdminFinplanAprovalRepository.GetParentNodeId(userDetails.tenant_id, templateId, treeType, parentId, processId);

            var parent = await _unitOfWork.AdminFinplanAprovalRepository.GetParentDetail(userDetails.tenant_id, processId);
            var configDetails = await _unitOfWork.AdminFinplanAprovalRepository.GetPublishConfig(userDetails.tenant_id, parent.config_id);

            var treeData = (from td in treeItems
                            select new PublishTreeNode
                            {
                                Uid = td.uid,
                                HasChildren = td.hasChildren.Value,
                                NodeId = td.nodeId,
                                text = td.text,
                                type = td.type,
                                isEditableNode = td.isEditableNode.Value,
                                parameters = AddPreviewUrl(treeType, td.url, td.bookmark_id, configDetails, tenantData, td.editable_node_id, td.nodeId),
                                chapterLevel = chapterLevel,
                                ShowEditWarningIcon = td.Show_Edit_Warning_Icon.Value,
                                isOperationBudgetServiceArea = td.type == "OperationBudgetServiceArea" || td.type == "OperationBudgetServiceAreaLvl1",
                                parentId = parentNodeId,
                                isServiceId = isServiceId,
                                orgLevel = chapterLevel,
                            }).ToList();

            parentTree.Tree = treeData;
            return parentTree;
        }

        public async Task<gmd_publish_tree_node_definitions> GetNodeDefinition(string nodeId, string treeType)
        {
            return await _unitOfWork.AdminFinplanAprovalRepository.GetNodeDefinition(nodeId, treeType);
        }

        public async Task<tco_docwf_process> GetDocWfProcessDataForSelectedProcessId(string userId, Guid processId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            return await _unitOfWork.AdminFinplanAprovalRepository.GetDocWfProcessDataForSelectedProcessId(userDetails.tenant_id, processId);
        }

        public async Task<tco_docwf_proccess_log> GetDocWfProcessLogDataByUserId(string userId, Guid processId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            return await _unitOfWork.AdminFinplanAprovalRepository.GetDocWfProcessLogDataByUserId(userDetails.tenant_id, processId, userDetails.pk_id);
        }

        public void LogProcess(UserData userDetails, Guid process_id, string status)
        {
            try
            {
                tco_docwf_proccess_log newData = new tco_docwf_proccess_log()
                {
                    fk_process_id = process_id,
                    fk_tenant_id = userDetails.tenant_id,
                    comments = string.Empty,
                    log_action = status,
                    updated = DateTime.UtcNow,
                    updated_by = userDetails.pk_id,
                    user_type = UserListType.BudgetCoordinatorUserList.ToString(),
                    fk_progress_status_id = null
                };
                _unitOfWork.GenericRepo.Add(newData);
                _unitOfWork.Complete();
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        public async Task<string> GetStatusSelectedProccess(string userId, Guid process_id)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                var processData = await _unitOfWork.AdminFinplanAprovalRepository.GetDocWfProcessDataForSelectedProcessId(userDetails.tenant_id, process_id);// get all data from db
                if (processData.process_status == ProccessStatus.PublishStarted.ToString())
                {
                    Dictionary<string, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "AdminFinPlanAproval");
                    return langString["ADM_Publish_InProgress"].LangText;
                }
                else
                {
                    return string.Empty;
                }
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        public async Task<bool> ShowApprovalProcedureTab(string userId, ApprovalProcessInputHelper inputObj)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                var respobUserData = await _unitOfWork.AdminFinplanAprovalRepository.GetAllDocWfResponsiblesData(userDetails.tenant_id, inputObj.pubTreeType.ToString(), inputObj.budgetYear, inputObj.forecastPeriod);// get all data from db

                return respobUserData.Any(z => z.fk_user_id == userDetails.pk_id);
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        public async Task<string> LockScreenBasedOnApprovalProcess(string userId, int budgetYear, string orgId, int? orgLevel, bool chkOnlyServiceArea, BL.Helpers.PageType pageId, PublishTreeType publishTreeType, int forecastPeriod)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            string statusMessage = string.Empty;
            var langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "AdminFinPlanAproval");

            List<string> nodeList = new List<string> { DocChanpterId.TotalNodePart.ToString(), DocChanpterId.CommonNode.ToString() };

            var processStatus = await _unitOfWork.AdminFinplanAprovalRepository.GetProcessStatus(userDetails.tenant_id, budgetYear, publishTreeType.ToString(), forecastPeriod);

            var nonServiceAreaPage = new List<string> { PageType.MYTEXT.ToString() };

            if (!chkOnlyServiceArea && processStatus.Any(x => nodeList.Contains(x.doc_chapter_id)))
            {
                switch (pageId)
                {
                    case PageType.BUDGETPROPOSAL:
                        statusMessage = langString["ADM_FinPlan_Common_running_error_budprop"].LangText;
                        break;

                    case PageType.BUDMANDOC:
                        statusMessage = langString["ADM_FinPlan_Common_running_error_budmandoc"].LangText;
                        break;

                    case PageType.BUDMANDOCADMIN:
                        //statusMessage = langString["ADM_FinPlan_Common_running_error_bmdocadmin"].LangText; #120405
                        break;

                    case PageType.MYTEXT:
                        statusMessage = langString["ADM_FinPlan_Common_running_error_mytext"].LangText;
                        break;

                    case PageType.MONTHLYREPORT:
                        statusMessage = langString["ADM_MR_Common_running_error_monthlyreport"].LangText;
                        break;

                    case PageType.MONTHLYREPORTOVERVIEW:
                        statusMessage = langString["ADM_MR_Common_running_error_monthlyreportoverview"].LangText;
                        break;

                    case PageType.MRDOC:
                        statusMessage = langString["ADM_MR_Common_running_error_mrdoc"].LangText;
                        break;

                    case PageType.MRDOCADMIN:
                        //statusMessage = langString["ADM_MR_Common_running_error_mrdocadmin"].LangText; #120405
                        break;

                    case PageType.PROJREPORTINVESTMENT:
                        statusMessage = langString["ADM_MR_Common_running_error_inv"].LangText;
                        break;
                }
            }
            else if (!nonServiceAreaPage.Contains(pageId.ToString()) && (string.IsNullOrEmpty(orgId) && processStatus.Any(x => !nodeList.Contains(x.doc_chapter_id)) ||
                        await ServiceAreaProcessOpen(userId, budgetYear, orgId, orgLevel, processStatus.Where(x => !nodeList.Contains(x.doc_chapter_id)).ToList(), publishTreeType, forecastPeriod)))
            {
                switch (pageId)
                {
                    case PageType.BUDGETPROPOSAL:
                        statusMessage = langString["ADM_FinPlan_SA_running_error_budprop"].LangText;
                        break;

                    case PageType.BUDMANDOC:
                        statusMessage = langString["ADM_FinPlan_SA_running_error_budmandoc"].LangText;
                        break;

                    case PageType.BUDMANDOCADMIN:
                        // statusMessage = langString["ADM_FinPlan_SA_running_error_bmdocadmin"].LangText; #120405
                        break;

                    case PageType.MONTHLYREPORT:
                        statusMessage = langString["ADM_MR_SA_running_error_monthlyreport"].LangText;
                        break;

                    case PageType.MONTHLYREPORTOVERVIEW:
                        statusMessage = langString["ADM_MR_SA_running_error_mroverview"].LangText;
                        break;

                    case PageType.MRDOC:
                        statusMessage = langString["ADM_MR_SA_running_error_mrdoc"].LangText;
                        break;

                    case PageType.MRDOCADMIN:
                        //statusMessage = langString["ADM_MR_SA_running_error_mrdocadmin"].LangText; #120405
                        break;

                    case PageType.PROJREPORTINVESTMENT:
                        statusMessage = langString["ADM_MR_SA_running_error_inv"].LangText;
                        break;
                }
            }
            return statusMessage;
        }

        public async Task RemoveEditWarningIconForCurrentNode(string userId, RemoveEditIconHelper inputObj)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                var warningNodeDataList = await _unitOfWork.AdminFinplanAprovalRepository.GetProcessTreeCurrentNodeDataAllProcess(userDetails.tenant_id, inputObj.templateId, inputObj.treeType.ToString(), inputObj.uId);// get all data from db

                if (warningNodeDataList.Any())
                {
                    warningNodeDataList.ForEach(z =>
                    {
                        z.Show_Edit_Warning_Icon = false;
                        z.updated_by = userDetails.pk_id;
                        z.updated = DateTime.UtcNow;
                    });
                }
                await _unitOfWork.CompleteAsync();
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        public async Task<bool> SetEditWarningIconForCurrentNode(string userId, RemoveEditIconHelper inputObj)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                var respobUserData = await _unitOfWork.AdminFinplanAprovalRepository.GetProcessTreeCurrentNodeData(userDetails.tenant_id, inputObj.templateId, inputObj.treeType.ToString(), inputObj.uId, inputObj.nodeId, inputObj.processId);// get all data from db

                if (respobUserData != null)
                {
                    respobUserData.Show_Edit_Warning_Icon = true;
                    respobUserData.updated_by = userDetails.pk_id;
                    respobUserData.updated = DateTime.UtcNow;
                }
                await _unitOfWork.CompleteAsync();
                return true;
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        public async Task FinishApprovalStatusProcessLog(string userId, AprovalStatusProcessLogSaveHelper saveObj)
        {
            try
            {
                if (saveObj.processId != Guid.Empty)
                {
                    UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                    List<Guid> proccessLogIdList = new List<Guid>();
                    proccessLogIdList.Add(saveObj.processId);
                    //to get doc_chapter_id of processId
                    var processData = await _unitOfWork.AdminFinplanAprovalRepository.GetDocWfProcessDataForSelectedProcessId(userDetails.tenant_id, saveObj.processId);

                    AdminFinPlanAprProcessHelper item = new AdminFinPlanAprProcessHelper();
                    item.nodeNameId = processData.doc_chapter_id;
                    // status update to tco_docwf_doc_part_log
                    await AddFinishStatusToDocPart(saveObj.budgetYear, userDetails, item, saveObj.templateId, saveObj.docPartStatus, saveObj);
                    // add record in tco_docwf_proccess_log
                    await InsertIntoDocProcessLog(userDetails, ProccessLogAction.BCDone, saveObj);
                    // status update to tco_docwf_process
                    UpdateFinishStatustoDocProcess(userDetails, processData, ProccessStatus.Finished);
                    await _unitOfWork.CompleteAsync();
                    if (saveObj.treeType == PublishTreeType.BudgetManagement)
                        await ProcessApprovalDescriptions(userId, saveObj.budgetYear, saveObj.processId, saveObj.templateId);
                }
                else
                {
                    throw new InvalidOperationException("incorrect Process Id");
                }
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        public async Task UpdateDocPartStatus(string userId, AprovalStatusProcessLogSaveHelper saveObj)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                AdminFinPlanAprProcessHelper item = new AdminFinPlanAprProcessHelper();
                item.nodeNameId = saveObj.docChapterId;
                // status update to tco_docwf_process
                //await Task.Run(() => AddFinishStatusToDocProcess(saveObj.budgetYear, userDetails, item, saveObj.templateId, ProccessStatus.Finished));
                // status update to tco_docwf_doc_part_log
                await AddFinishStatusToDocPart(saveObj.budgetYear, userDetails, item, saveObj.templateId, saveObj.docPartStatus, saveObj);
                await _unitOfWork.CompleteAsync();
            }
            catch (Exception e)
            {
                throw new InvalidOperationException(e.Message);
            }
        }

        public async Task<List<ApprovalStatusGridHelper>> GetDocApprovalStatusGrid(string userId, int budgetYear, int templateId, int forecastPeriod, PublishTreeType treeType)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "AdminFinPlanAproval");
            List<ApprovalStatusGridHelper> finalData = new List<ApprovalStatusGridHelper>();
            List<string> processStatusDisabled = new List<string>() { ProccessStatus.Started.ToString(), ProccessStatus.PublishStarted.ToString(), ProccessStatus.ReviewProcessDone.ToString(), string.Empty };
            List<KeyValueWithLevelId> keyValueData = new List<KeyValueWithLevelId>();
            //get chapter name langstrings
            keyValueData.Add(new KeyValueWithLevelId() { Key = DocChanpterId.TotalNodePart.ToString(), level1Id = DocChanpterId.TotalNodePart.ToString(), Value = langString["ADM_FinPlan_TotalPart"].LangText });// total node
            keyValueData.Add(new KeyValueWithLevelId() { Key = DocChanpterId.CommonNode.ToString(), level1Id = DocChanpterId.TotalNodePart.ToString(), Value = langString["ADM_FinPlan_CommonNode"].LangText });// common node
            keyValueData.AddRange(treeType.ToString() == PublishTreeType.BudgetManagement.ToString() ? await GetTreeData(budgetYear, userId) : await GetMonthlyReportTreeData(forecastPeriod, userId));// service area/serviceIdNode

            //get chapters specific to current user
            var responsibleNodeAccess = await _unitOfWork.AdminFinplanAprovalRepository.GetAllDocWfResponsiblesDataForCurrentUser(userDetails.tenant_id, treeType.ToString(), userDetails.pk_id, budgetYear, forecastPeriod);// get all chapter access to current user from tco_docwf_responsibles
            List<string> chapterNodeList = responsibleNodeAccess.Select(z => z.doc_chapter_id).Distinct().ToList();
            //get doc part status
            var nodesWithDocPartStatus = await _unitOfWork.AdminFinplanAprovalRepository.GetAllDocWfdocPartLogDataOfChapters(userDetails.tenant_id, treeType.ToString(), templateId, budgetYear, chapterNodeList, forecastPeriod);
            //get recently updated process status specific to chapters
            var processDataList = await _unitOfWork.AdminFinplanAprovalRepository.GetAllDocWfProcessDataByNodeList(userDetails.tenant_id, treeType.ToString(), chapterNodeList, budgetYear, forecastPeriod);// get all data from db for accessible tco_docwf_process
            processDataList = processDataList.Where(x => x.template_id == templateId).OrderByDescending(x => x.updated).ToList();

            foreach (var item in chapterNodeList)
            {
                ApprovalStatusGridHelper temp = new ApprovalStatusGridHelper();
                var processStatus = processDataList.FirstOrDefault(x => x.doc_chapter_id == item);
                var docPartStatus = nodesWithDocPartStatus.FirstOrDefault(x => x.doc_chapter_id == item);
                temp.docChapterId = item;
                temp.documentSection = keyValueData.FirstOrDefault(x => x.Key.ToLower() == item.ToLower()).Value;
                temp.processStatusId = GetProcessStatusBasedOnDocPart(processStatus, docPartStatus);
                temp.processStatus = SetProcessStatusText(temp.processStatusId, langString);
                temp.docPartStatus = docPartStatus == null ? docPart_status.process_open.ToString() : docPartStatus.log_action;
                temp.docPartStatusId = docPartStatus == null ? 0 : docPartStatus.pk_id;
                temp.disabled = processStatusDisabled.Contains(temp.processStatusId);
                finalData.Add(temp);
            }

            return finalData;
        }

        public async Task<CommentSection> GetCommentSection(string userId, Guid processId, string uniqueId, int budgetYear, int forecastPeriod, string treeType)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var commentsDetails = await _unitOfWork.AdminFinplanAprovalRepository.GetNodeComments(userDetails.tenant_id, processId, uniqueId, budgetYear, forecastPeriod, treeType);
            var commenters = await _unitOfWork.AdminFinplanAprovalRepository.GetAllCommenters(userDetails.tenant_id, commentsDetails.Select(z => z.updated_by).Distinct().ToList());
            return new CommentSection() { commenters = commenters, commentsDetails = commentsDetails };
        }

        public async Task SaveOrUpdateComments(string userId, SaveComment inputObj)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            if (inputObj.commentId != 0)
            {
                var commentsDetails = await _unitOfWork.AdminFinplanAprovalRepository.GetNodeCommentById(userDetails.tenant_id, inputObj.commentId);
                commentsDetails.description = inputObj.description;
                commentsDetails.updated = DateTime.UtcNow;
            }
            else
            {
                var process = await _unitOfWork.AdminFinplanAprovalRepository.GetDocWfProcessDataForSelectedProcessId(userDetails.tenant_id, inputObj.processId);
                tco_docwf_table_comments newComment = new tco_docwf_table_comments()
                {
                    fk_tenant_id = userDetails.tenant_id,
                    tree_type = inputObj.treeType.ToString(),
                    unique_id = inputObj.uniqueId,
                    doc_chapter_id = process.doc_chapter_id,
                    template_id = process.template_id,
                    budget_year = inputObj.budgetYear,
                    forcast_period = inputObj.forecastPeriod,
                    description = inputObj.description,
                    updated = DateTime.UtcNow,
                    updated_by = userDetails.pk_id
                };
                await _unitOfWork.GenericRepo.AddAsync(newComment);
            }
            await _unitOfWork.CompleteAsync();
            // set editwarning

            await SetEditWarningIconForCurrentNode(userId, new RemoveEditIconHelper
            {
                uId = inputObj.uniqueId,
                nodeId = inputObj.NodeId,
                processId = (inputObj.processId),
                treeType = inputObj.treeType,
                budgetYear = inputObj.budgetYear,
                forecastPeriod = inputObj.forecastPeriod,
                templateId = inputObj.TemplateId
            });
        }

        public async Task DeleteComments(string userId, int pkId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            await _unitOfWork.AdminFinplanAprovalRepository.DeleteNodeComments(userDetails.tenant_id, pkId);
        }

        public async Task<bool> GetEditWarningIconForCurrentNode(string userId, RemoveEditIconHelper inputObj)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                var respobUserData = await _unitOfWork.AdminFinplanAprovalRepository.GetProcessTreeCurrentNodeData(userDetails.tenant_id, inputObj.templateId, inputObj.treeType.ToString(), inputObj.uId, inputObj.nodeId, inputObj.processId);// get all data from db
                return respobUserData != null && respobUserData.Show_Edit_Warning_Icon.HasValue && respobUserData.Show_Edit_Warning_Icon.Value;
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        public async Task<List<AdminFinPlanAprProcessTemplateHelper>> GetAllTemplateUsedInProcess(string userId, ApprovalProcessInputHelper inputObj)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                var ListTemplateIds = await _unitOfWork.AdminFinplanAprovalRepository.GetAllTemplateIdsUsedInProcess(userDetails.tenant_id, inputObj.pubTreeType.ToString(), inputObj.budgetYear, inputObj.forecastPeriod);// get all data from db
                var templateData = await _unitOfWork.AdminFinplanAprovalRepository.GetAllDocPublishTemplate(userDetails.tenant_id, inputObj.pubTreeType, inputObj.budgetYear, inputObj.forecastPeriod);// get all data from db
                List<AdminFinPlanAprProcessTemplateHelper> finalData = new List<AdminFinPlanAprProcessTemplateHelper>();
                var filteredTemplateData = templateData.Where(x => ListTemplateIds.Contains(x.PkId)).ToList();
                foreach (var item in filteredTemplateData.ToList())
                {
                    finalData.Add(new AdminFinPlanAprProcessTemplateHelper()
                    {
                        KeyId = item.PkId,
                        ValueString = item.Name,
                        templateUrl = item.TemplateUrl,
                        tenantId = userDetails.tenant_id
                    });
                }

                return finalData;
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        public async Task<DocLogGridHelper> GetAdminDocLogGridData(string userId, DocLogGridInputHelper inputObj, int templateId)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                Dictionary<string, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "AdminFinPlanAproval");

                var processDataList = await _unitOfWork.AdminFinplanAprovalRepository.GetAllProcessDataForTemplate(userDetails.tenant_id, inputObj.pubTreeType.ToString(), inputObj.budgetYear, inputObj.forecastPeriod, templateId);// get all process data from db for selected template

                var budgetCordinatorList = await _unitOfWork.AdminFinplanAprovalRepository.GetAllDocWfResponsiblesData(userDetails.tenant_id, inputObj.pubTreeType.ToString(), inputObj.budgetYear, inputObj.forecastPeriod);// get all chapter access to current user from tco_docwf_responsibles

                // get doc part status
                var nodesDocPartStatus = await _unitOfWork.AdminFinplanAprovalRepository.GetAllDocWfdocPartLogData(userDetails.tenant_id, inputObj.pubTreeType.ToString(), inputObj.budgetYear, templateId, inputObj.forecastPeriod);

                DocLogGridHelper finalData = new DocLogGridHelper();

                List<DocLogGridDataHelper> Data = new List<DocLogGridDataHelper>();
                //tree data
                List<KeyValueWithLevelId> Treedata = await GetDocumentTree(userId, inputObj.budgetYear, inputObj.forecastPeriod, inputObj.pubTreeType.ToString());// get tree data
                DocLogGridDataHelper tempData;
                var usersList = await GetTcoUserData(userId, true);

                foreach (var item in Treedata)
                {
                    List<int> bcPkIdList = budgetCordinatorList.Where(x => x.doc_chapter_id == item.Key).Select(z => z.fk_user_id).ToList();
                    List<string> bcNameList = GetNameFromUserPkIdList(bcPkIdList, usersList);
                    var lastUpdatedProcessData = processDataList.FirstOrDefault() != null ? GetLastUpdatedProcessData(item, processDataList) : null;
                    var docPartStatus = nodesDocPartStatus.FirstOrDefault(z => z.doc_chapter_id == item.Key);
                    tempData = new DocLogGridDataHelper();
                    tempData.lastUpdatedProcessId = lastUpdatedProcessData != null ? lastUpdatedProcessData.pk_process_id : Guid.Empty;
                    tempData.docNodeId = item.Key;
                    tempData.docNodeIdName = item.Value;
                    tempData.finishedDate = lastUpdatedProcessData != null ? lastUpdatedProcessData.deadline_date : DateTime.MinValue;
                    tempData.finishedDateStr = lastUpdatedProcessData != null ? lastUpdatedProcessData.deadline_date.ToShortDateString() : "-";
                    tempData.lastUpdatedProcessStatus = lastUpdatedProcessData != null ? SetProcessStatusText(lastUpdatedProcessData.process_status, langString) : "-";
                    tempData.lastUpdatedProcessStatusKey = lastUpdatedProcessData != null ? lastUpdatedProcessData.process_status : "-";
                    tempData.doc_status = docPartStatus != null ? SetProcessStatusText(docPartStatus.log_action, langString) : "-";
                    tempData.doc_statusKey = docPartStatus != null ? docPartStatus.log_action : "-";
                    tempData.budgetCordinatorNameList = bcNameList.Any() ? String.Join(", ", bcNameList) : string.Empty;
                    tempData.procSeeDetailsStr = langString["ADM_Flow_Grid_processDetailsStr"].LangText;
                    Data.Add(tempData);
                }

                finalData.data = FilterAdminDocLogData(Data, inputObj.searchInput);
                finalData.processStatusList = GetProcessStatusList(Data, langString);
                finalData.docStatusList = GetDocStatusList(nodesDocPartStatus, langString);
                return finalData;
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        public async Task TransferApprovalNodeDescriptions(string userId, int templateId, int budgetYear, Guid processId, string treeType)
        {
            //process
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var processTreeData = await _unitOfWork.BudgetProposalRepository.GetAllApprovalLogDescription(userDetails.tenant_id, budgetYear, processId);
            if (processTreeData.Any(x => x.is_edited))
            {
                processTreeData = processTreeData.Where(x => x.is_edited).ToList();
                ProcessActionDescription(userId, processTreeData.Where(x => x.node_type == BPNodeType.Action.ToString() && x.tree_type == treeType));
                ProcessInvestmentDescription(userId, budgetYear, processTreeData.Where(x => x.node_type == BPNodeType.Investment.ToString() && x.tree_type == treeType));
                await TransferCustomNodesLogtoMainTable(userId, processId, userDetails.tenant_id, budgetYear);
                await TransferStandardNodesLogToMainTable(userId, processId, userDetails.tenant_id, budgetYear);
                await TransferGuidanceTextLogToMainTable(userId, userDetails.tenant_id, budgetYear);
            }
            //clear approval log
            await _unitOfWork.BudgetProposalRepository.ClearApprovalNodeDescriptionLogs(userDetails.tenant_id, budgetYear, processId);
        }

        private void ProcessActionDescription(string userId, IEnumerable<tco_approval_node_description_log> nodesDescription)
        {
            #region Process Finplan action

            var finplanAction = nodesDescription.Where(x => (x.dimension_type == NodeDimentionType.FinplanAction.ToString())|| x.dimension_type == NodeDimentionType.CABGrid.ToString()).ToList();
            if (finplanAction.Any())
            {
                finplanAction.ForEach (x =>
                {
                    ActionDescriptionsForBudgetChanges input = new()
                    {
                        budgetphaseId = x.budget_phase_id.ToString(),
                        actionId = int.Parse(x.dimension_id),
                        description = x.description,
                        logHistory = true,
                        editdesctype = GetActionDescriptionType(x.description_type)
                    };
                    _cab.AddActionDetailsDescriptionsWithBudgetChangeAsync(input, userId).GetAwaiter().GetResult();
                });
            }

            #endregion Process Finplan action

            #region Blist & Parked Action

            var blistParkedAction = nodesDescription.Where(x => x.dimension_type == NodeDimentionType.BlistAction.ToString() || x.dimension_type == NodeDimentionType.ParkedAction.ToString()).ToList();
            if (blistParkedAction.Any())
            {
                blistParkedAction.ForEach(x =>
                {
                    ActionDescriptionsForBudgetChanges input = new()
                    {
                        budgetphaseId = x.budget_phase_id.ToString(),
                        actionId = int.Parse(x.dimension_id),
                        description = x.description,
                        logHistory = true,
                        editdesctype = GetActionDescriptionType(x.description_type)
                    };
                    _cab.AddBlistActionDetailsDescriptionsWithBudgetChange(input, userId);
                });
            }

            #endregion Blist & Parked Action
        }

        private void ProcessInvestmentDescription(string userId, int budgetYear, IEnumerable<tco_approval_node_description_log> nodesDescription)
        {
            nodesDescription.ToList().ForEach( x =>
            {
                SaveDescription input = new()
                {
                    budgetYear = budgetYear,
                    description = x.description,
                    descriptionType = x.description_type,
                    mainProjCode = x.dimension_id,
                    logHistory = true,
                    phaseId = x.budget_phase_id
                };
                _invProj.SaveInvestmentDescriptionAsync(userId, input).GetAwaiter().GetResult();
            });
        }

        private static string GetActionDescriptionType(string descriptionType)
        {
            string editType = string.Empty;
            switch (descriptionType)
            {
                case externalText:
                    editType = 1.ToString();
                    break;

                case internalText:
                    editType = 2.ToString();
                    break;

                case budgetphaseText:
                    editType = 3.ToString();
                    break;

                case consequenceText:
                    editType = 4.ToString();
                    break;
            }
            return editType;
        }

        private static List<DocLogGridDataHelper> FilterAdminDocLogData(IEnumerable<DocLogGridDataHelper> result, DocLogGridSearchHelper inputObject)
        {
            if (inputObject != null)
            {
                if (!string.IsNullOrEmpty(inputObject.finishedDateStr))
                {
                    result = result.Where(x => x.finishedDateStr.Contains(inputObject.finishedDateStr));
                }

                if (!string.IsNullOrEmpty(inputObject.docNodeIdName))
                {
                    result = result.Where(x => x.docNodeIdName.ToLower().Contains(inputObject.docNodeIdName.ToLower()));
                }
                if (!string.IsNullOrEmpty(inputObject.budgetCordinatorName))
                {
                    result = result.Where(x => x.budgetCordinatorNameList.ToLower().Contains(inputObject.budgetCordinatorName.ToLower()));
                }

                if (!string.IsNullOrEmpty(inputObject.lastUpdatedProcessStatusKey) && inputObject.lastUpdatedProcessStatusKey != "-1")
                {
                    result = result.Where(x => x.lastUpdatedProcessStatusKey == inputObject.lastUpdatedProcessStatusKey);
                }
                if (!string.IsNullOrEmpty(inputObject.doc_statusKey) && inputObject.doc_statusKey != "-1")
                {
                    result = result.Where(x => x.doc_statusKey == inputObject.doc_statusKey);
                }
            }

            return result.ToList();
        }

        public async Task<ProcessLogClsHelper> GetAdminDocLogGridDetailData(string userId, ApproverLogGridInputHelper inputObj, int templateId)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                Dictionary<string, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "AdminFinPlanAproval");

                var processDataList = await _unitOfWork.AdminFinplanAprovalRepository.GetAllProcessDataForTemplate(userDetails.tenant_id, inputObj.pubTreeType.ToString(), inputObj.budgetYear, inputObj.forecastPeriod, templateId);// get all process data from db for selected template
                processDataList = FilterProcessData(processDataList, inputObj.nodeId);

                List<Guid> processGuidList = processDataList.Select(z => z.pk_process_id).Distinct().ToList();
                var processLogDataList = await _unitOfWork.AdminFinplanAprovalRepository.GetAllDocWfProcessLogData(userDetails.tenant_id, processGuidList);// get all data from tco_docwf_proccess_log

                var approverViwerUserDataNodeList = await _unitOfWork.AdminFinplanAprovalRepository.GetAllAproverViewerUserDataForProcess(userDetails.tenant_id, processGuidList);// get all data from from tco_docwf_process_approver_viewer

                //tree data
                List<KeyValueWithLevelId> Treedata = await GetDocumentTree(userId, inputObj.budgetYear, inputObj.forecastPeriod, inputObj.pubTreeType.ToString());// get tree data
                                                                                                                                                            // try to fetch from publish preview child table

                var usersList = await GetTcoUserData(userId, true);

                ProcessLogClsHelper finalObject = new ProcessLogClsHelper();
                List<KeyValueGuidLogData> VersionTitleList = new List<KeyValueGuidLogData>();
                VersionTitleList.Add(new KeyValueGuidLogData() { Key = Guid.Empty, Value = langString.FirstOrDefault(x => x.Key.Equals("ADM_Flow_Grid_SelectOption", StringComparison.InvariantCultureIgnoreCase)).Value.LangText });

                List<KeyValueNewData> StatusList = new List<KeyValueNewData>();
                StatusList.Add(new KeyValueNewData() { Key = "-1", Value = langString.FirstOrDefault(x => x.Key.Equals("ADM_Flow_Grid_SelectOption", StringComparison.InvariantCultureIgnoreCase)).Value.LangText });

                List<KeyValueNewData> userRoleList = new List<KeyValueNewData>();
                userRoleList.Add(new KeyValueNewData() { Key = "-1", Value = langString.FirstOrDefault(x => x.Key.Equals("ADM_Flow_Grid_SelectOption", StringComparison.InvariantCultureIgnoreCase)).Value.LangText });

                List<ProcessLogGridHelper> finalData = new List<ProcessLogGridHelper>();
                List<keyvaluewithGuid> nodeIdProcessIdList = processDataList.Select(z => new keyvaluewithGuid() { key = z.pk_process_id, value = z.doc_chapter_id }).ToList();
                int id = 1;
                foreach (var item in processLogDataList.OrderByDescending(z => z.updated))
                {
                    string currentNodeId = (nodeIdProcessIdList.FirstOrDefault(y => y.key == item.fk_process_id).value);
                    var itemUser = usersList.FirstOrDefault(x => x.pk_id == item.updated_by);
                    ProcessLogGridHelper temp = new ProcessLogGridHelper();
                    temp.id = id;
                    temp.processId = item.fk_process_id;
                    temp.lastUpdateBy = itemUser.first_name + " " + itemUser.last_name;
                    temp.lastUpdated = item.updated;
                    temp.lastUpdatedStr = item.updated.ToShortDateString();
                    temp.processDetails = InsertProcessLogDetailHtml(approverViwerUserDataNodeList, item, processDataList, usersList, langString, Treedata, currentNodeId);
                    temp.processDetailsStr = langString["ADM_Flow_Grid_processDetailsStr"].LangText;
                    temp.proecessLogSatusId = item.log_action;
                    temp.proecessLogStatusName = SetProcessStatusText(item.log_action, langString);
                    temp.processTitle = processDataList.FirstOrDefault(Z => Z.pk_process_id == item.fk_process_id).title;
                    temp.userType = langString["ADM_Flow_Grid_UserType_" + item.user_type].LangText;
                    temp.userTypeId = item.user_type;
                    finalData.Add(temp);
                    id++;

                    if (!(VersionTitleList.Any(x => x.Key == item.fk_process_id)))
                    {
                        VersionTitleList.Add(new KeyValueGuidLogData() { Key = item.fk_process_id, Value = processDataList.FirstOrDefault(Z => Z.pk_process_id == item.fk_process_id).title });
                    }
                    if (!(StatusList.Any(x => x.Key == item.log_action)))
                    {
                        StatusList.Add(new KeyValueNewData() { Key = item.log_action, Value = SetProcessStatusText(item.log_action, langString) });
                    }
                    if (!(userRoleList.Any(x => x.Key == item.user_type)))
                    {
                        userRoleList.Add(new KeyValueNewData() { Key = item.user_type, Value = langString["ADM_Flow_Grid_UserType_" + item.user_type].LangText });
                    }
                }

                finalData = SearchLogGrid(finalData, inputObj.searchInput);
                finalObject.LogData = finalData;
                finalObject.VersionTitleList = VersionTitleList;
                finalObject.StatusList = StatusList;
                finalObject.UserRoleList = userRoleList;
                return finalObject;
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        public async Task<List<KeyValueNewDataLogData>> GetAdminDocLogDropDown(string userId, ApprovalProcessInputHelper inputData, int templateId)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                Dictionary<string, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "AdminFinPlanAproval");
                var processDataList = await _unitOfWork.AdminFinplanAprovalRepository.GetAllProcessDataForTemplate(userDetails.tenant_id, inputData.pubTreeType.ToString(), inputData.budgetYear, inputData.forecastPeriod, templateId);// get all process data from db for selected template

                List<KeyValueGuidData> nodeIdProcessIdList = processDataList.Select(z => new KeyValueGuidData() { Key = z.pk_process_id, Value = z.doc_chapter_id }).ToList();

                List<string> nodeIdList = processDataList.Select(x => x.doc_chapter_id).Distinct().ToList();

                List<KeyValueNewDataLogData> docNodeList = new List<KeyValueNewDataLogData>();
                //tree data
                List<KeyValueWithLevelId> Treedata = new List<KeyValueWithLevelId>();
                Treedata.Add(new KeyValueWithLevelId() { Key = DocChanpterId.TotalNodePart.ToString(), level1Id = DocChanpterId.TotalNodePart.ToString(), Value = langString["ADM_FinPlan_TotalPart"].LangText });// total node
                Treedata.Add(new KeyValueWithLevelId() { Key = DocChanpterId.CommonNode.ToString(), level1Id = DocChanpterId.CommonNode.ToString(), Value = langString["ADM_FinPlan_CommonNode"].LangText });// common node
                if (inputData.pubTreeType.ToString() == PublishTreeType.BudgetManagement.ToString())
                {
                    Treedata.AddRange(await GetTreeData(inputData.budgetYear, userId));// BM service area/serviceIdNode
                }
                else
                {
                    Treedata.AddRange(await GetMonthlyReportTreeData(inputData.forecastPeriod, userId));// MR service area/serviceIdNode
                }

                docNodeList.Add(new KeyValueNewDataLogData() { Key = "0", Value = langString["ADM_Flow_Grid_SelectOption"].LangText, ProcessIdList = nodeIdProcessIdList.Select(x => x.Key).Distinct().ToList() });// select opton
                foreach (var item in nodeIdList)
                {
                    KeyValueNewDataLogData temp = new KeyValueNewDataLogData() { Key = item, Value = Treedata.FirstOrDefault(x => x.Key == item).Value, ProcessIdList = nodeIdProcessIdList.Where(s => s.Value == item).Select(y => y.Key).ToList() };// actual data
                    docNodeList.Add(temp);
                }

                return docNodeList;
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        public async Task<IEnumerable<vw_tco_parameters>> GetParameterValues(string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            IEnumerable<vw_tco_parameters> monthRepParams = await _unitOfWork.AdminFinplanAprovalRepository.GetParameterValues(userDetails.tenant_id);
            return monthRepParams;
        }

        public async Task<List<KeyValueInt>> GetUserListToShareForProcessEdit(string userId, UserListType userType, bool includeInactive, ApprovalProcessInputHelper input)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                var usersList = await GetTcoUserData(userId, includeInactive);
                TimeSpan cacheTimeOut = new TimeSpan(24, 0, 0);
                int clientId = userDetails.client_id;

                string CacheKeyName = GetCacheName(userType);
                List<int> roleIds = GetRoleIds(userType);
                var UserDataNodeList = await _unitOfWork.AdminFinplanAprovalRepository.GetAproverViewerUserDataForProcess(userDetails.tenant_id, input.processId);// get all data from db
                List<KeyValueInt> userDataList = new List<KeyValueInt>();
                string result = await _cache.GetStringForUserAsync(clientId, userDetails.tenant_id, userId, CacheKeyName + "_" + includeInactive);
                if (result == null)
                {
                    AuthenticationDBContext userAdminDBContext = _utility.GetAuthenticationContext();
                    if (roleIds.Any())// return users only if roles are set, else return empty list
                    {
                        List<int> userIdList = await userAdminDBContext.tco_auth_user_role_mapping.Where(x => x.tenant_id == userDetails.tenant_id && roleIds.Contains(x.fk_role_id)).Select(z => z.fk_user_id).Distinct().ToListAsync();

                        userDataList.AddRange(usersList.Where(x => userIdList.Contains(x.pk_id) && x.IsActive).Select(z => new KeyValueInt() { Key = z.pk_id, Value = z.first_name + " " + z.last_name }).ToList());
                        result = JsonConvert.SerializeObject(userDataList.OrderBy(x => x.Value));
                        await _cache.SetStringForUserAsync(clientId, userDetails.tenant_id, userId, CacheKeyName, result, cacheTimeOut);
                    }
                }
                else
                {
                    userDataList = JsonConvert.DeserializeObject<List<KeyValueInt>>(result);
                }

                List<int> useridtoRemove = UserDataNodeList.Where(x => x.User_type == (int)userType).Select(z => z.fk_user_id).ToList();
                userDataList.RemoveAll(z => useridtoRemove.Contains(z.Key));
                return userDataList;
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        public async Task<string> TestMethod(string userId, int loopCount)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();

            StringBuilder sb = new StringBuilder();

            for (int i = 0; i <= loopCount; i++)
            {
                DateTime startTime = DateTime.Now;
                var datasetIqueryable = (from a in dbContext.tbu_trans_detail
                                         where a.fk_tenant_id == 31
                                         && a.budget_year == 2025
                                         //&& deptSetHashed.Contains(a.department_code)
                                         group a by new
                                         {
                                             a.fk_account_code,
                                             a.department_code,
                                             a.fk_function_code,
                                             a.fk_project_code,
                                             a.free_dim_1,
                                             a.free_dim_2,
                                             a.free_dim_3,
                                             a.free_dim_4
                                         } into g
                                         select new
                                         {
                                             fk_account_code = g.Key.fk_account_code,
                                             department_code = g.Key.department_code,
                                             fk_function_code = g.Key.fk_function_code,
                                             fk_project_code = g.Key.fk_project_code,
                                             free_dim_1 = g.Key.free_dim_1,
                                             free_dim_2 = g.Key.free_dim_2,
                                             free_dim_3 = g.Key.free_dim_3,
                                             free_dim_4 = g.Key.free_dim_4,
                                             amount = g.Sum(x => x.amount_year_1)
                                         });

                var str = datasetIqueryable.ToQueryString();

                if (i == 1)
                {
                    sb.AppendLine(str);
                }

                sb.AppendLine();

                var data = await datasetIqueryable.ToListAsync();
                DateTime endTime = DateTime.Now;
                TimeSpan difference = endTime - startTime;
                sb.AppendLine($"Minutes: {difference.Minutes}, Seconds: {difference.Seconds}, Row Count: {data.Count()}");
            }

            return sb.ToString();
        }


        /// <summary>
        /// Gets the Current User List Type by process ID.
        /// If user is reviewer, approver and also coordinator,
        /// then based on the current approval stage, it will return the current needed user type to proceed
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="process"></param>
        /// <returns></returns>
        public async Task<UserListType> GetCurrentUserListType(string userId, tco_docwf_process process)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            var userProcessLogs =
                (await _unitOfWork.AdminFinplanAprovalRepository.GetAllDocWfProcessLogData(userDetails.tenant_id,
                    new List<Guid> { process.pk_process_id }))
                .Where(x => x.updated_by == userDetails.pk_id)
                .OrderByDescending(x => x.updated).ToList();

            // Check if user is a coordinator for this process
            // Get the responsible node access for the current user and process
            var isCordinator =
                await _unitOfWork.AdminFinplanAprovalRepository.IsCoordinatorForProcess(
                    userDetails.tenant_id,
                    userDetails.pk_id, process.pk_process_id);

            var userDataList =
                (await _unitOfWork.AdminFinplanAprovalRepository.GetCurrentUserAproverViewerUserDataForProcess(
                    userDetails.tenant_id, new List<Guid> { process.pk_process_id }, userDetails.pk_id))
                .Select(x => (UserListType)x.User_type).ToHashSet();

            return GetCurrentUserListType(userProcessLogs, isCordinator, userDataList, process);
        }

        private static UserListType GetCurrentUserListType(List<tco_docwf_proccess_log> userProcessLogs,
            bool isCoordinator, HashSet<UserListType> userDataList, tco_docwf_process process)
        {
            if (isCoordinator && (process.process_status is nameof(ProccessStatus.ReviewProcessDone) or nameof(ProccessStatus.Recalled)))
            {
                return UserListType.BudgetCoordinatorUserList;
            }

            // Get Current Status
            foreach (var log in userProcessLogs)
            {
                switch (log.log_action)
                {
                    // If Approved, then current user act as coordniator if coordinator
                    case nameof(ProccessLogAction.ApproverDone) when isCoordinator:
                        return UserListType.BudgetCoordinatorUserList;
                    // If Reviewer has completed, then return approver if exists
                    case nameof(ProccessLogAction.ViewerDone) when
                        userDataList.Contains(UserListType.ApproverUserList):
                        return UserListType.ApproverUserList;
                }
            }

            if (userDataList.Contains(UserListType.ViewerUserList))
            {
                return UserListType.ViewerUserList;
            }

            if (userDataList.Contains(UserListType.ApproverUserList))
            {
                return UserListType.ApproverUserList;
            }

            return isCoordinator
                ? UserListType.BudgetCoordinatorUserList
                : throw new InvalidOperationException(
                    $"User does not have access to process. User must be assigned as viewer, approver, or coordinator.");
        }

        public async Task TransferCustomNodesLogtoMainTable(string userId, Guid processId, int tenantId, int budgetYear) {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            //fetch all proceesid related nodes
            IEnumerable<tco_publish_review_child> publishChildList = await _unitOfWork.AdminFinplanAprovalRepository.GetPublishNodesByProcessId(tenantId, processId, PublishTreeType.BudgetManagement.ToString());

            List<tco_publish_review_child> customNodeList = publishChildList.Where(x => x.type == clsConstants.BM_Tree_Types.customNode.ToString()).ToList();
            List<Guid> customNodeIdLst = customNodeList.Select(x => Guid.Parse(x.nodeId)).ToList();
            //fetch all approval process log related nodes
            IEnumerable<tco_approval_node_description_log> approvalCustomNodeLst = await _unitOfWork.AdminFinplanAprovalRepository
                                                                                           .GetApprovalNodeLogData(tenantId, budgetYear, clsConstants.BM_Tree_Types.customNode.ToString());
            approvalCustomNodeLst = approvalCustomNodeLst.Where(x => customNodeIdLst.Contains(x.node_id)).ToList();
            customNodeIdLst = approvalCustomNodeLst.Select(x => x.node_id).ToList();
            List<TcoCustomNode> allCustomNodes = dbContext.TcoCustomNode.Where(x => x.FkTenantId == userDetails.tenant_id &&
                                                                                    x.BudgetYear == budgetYear &&
                                                                                    customNodeIdLst.Contains(x.PkNodeId)).ToList();
            foreach (var item in allCustomNodes) {
                tco_approval_node_description_log nodeLog = approvalCustomNodeLst.FirstOrDefault(x => x.node_id == item.PkNodeId);
                item.Description = nodeLog.description;
                item.AbstractText = nodeLog.abstract_text;
                item.Updated = DateTime.UtcNow;
                item.UpdatedBy = userDetails.pk_id;
                await _utility.SaveTextLogAsync(userId, item.PkNodeId, nodeLog.description);
            }
            await dbContext.SaveChangesAsync();
        }

        public async Task TransferStandardNodesLogToMainTable(string userId, Guid processId, int tenantId, int budgetYear) {

            IEnumerable<tco_publish_review_child> publishChildList = await _unitOfWork.AdminFinplanAprovalRepository.GetPublishNodesByProcessId(tenantId, processId, PublishTreeType.BudgetManagement.ToString());

            IEnumerable<tco_approval_node_description_log> approvalStandardNodeLst = await _unitOfWork.AdminFinplanAprovalRepository
                                                                                           .GetApprovalNodeLogData(tenantId, budgetYear, clsConstants.BM_Tree_Types.Standard.ToString());
            //fetch only processid related standard node list
            approvalStandardNodeLst = approvalStandardNodeLst.Where(x => publishChildList.Select(y => y.uid).Contains(x.dimension_id)).ToList();

            foreach (var item in approvalStandardNodeLst) {
                DescriptionInput orgInfo = GetOrgInformation(item.dimension_type, item.dimension_id);
                string? parentNodeId = publishChildList.FirstOrDefault(x => x.uid == item.dimension_id).parent;
                await budProp.SaveStrategyObjectivesAsync(orgInfo.orgId, orgInfo.serviceId, item.description, 
                                                          orgInfo.fieldType, userId, budgetYear, string.Empty, 
                                                          item.abstract_text,
                                                          string.IsNullOrEmpty(parentNodeId) ? level1Node : level2Node, 
                                                          new List<string>(), true, true);
            }

        }

        public DescriptionInput GetOrgInformation(string nodeType, string nodeUniqueId)
        {
            DescriptionInput orgInput = new DescriptionInput();
            int level = 1;
            switch (nodeType)
            {
                case "ServiceIdDescription":
                    level = 2;
                    orgInput.fieldType = "BP-CA";
                    break;

                case "ServiceAreaDescription":
                    orgInput.fieldType = "BP-CA";
                    break;

                case "ServiceIDStrategyAndChallenge":
                    level = 2;
                    orgInput.fieldType = "BP-SC";
                    break;

                case "StrategyAndChallenge":
                    orgInput.fieldType = "BP-SC";
                    break;

                case "BudgetServiceArea":
                    orgInput.fieldType = "BP-BD";
                    break;

                case "ChapterIntro":
                    orgInput.fieldType = "BP-SU-SA";
                    break;

                default:
                    return orgInput;
            }
            //move this inside switch when new nodes are added
            var uid = nodeUniqueId.Split('-');
            orgInput.orgId = uid[2];
            if (level == 2)
            {
                orgInput.serviceId = uid[4];
            }
            return orgInput;
        }

        #region Private


        private async Task TransferGuidanceTextLogToMainTable(string userId, int tenantId, int budgetYear)
        {

            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            //fetch all approval process log related nodes
            IEnumerable<tco_approval_node_description_log> approvalNodeLst = await _unitOfWork.AdminFinplanAprovalRepository
                                                                                           .GetApprovalNodeLogData(tenantId, budgetYear, string.Empty);

            var guidanceTextList = approvalNodeLst.Where(x => x.dimension_type == NodeDimentionType.GuidanceText.ToString() && x.node_id == x.description_id).ToList();
            var nodeList = guidanceTextList.Select(x => x.node_id).ToList();
            var originalData = await dbContext.tco_widget_node_delegation.Where(x => x.fk_tenant_id == userDetails.tenant_id &&
                                                                                    x.budget_year == budgetYear &&
                                                                                    nodeList.Contains(x.pk_node_id)).ToListAsync();
            foreach (var item in originalData)
            {
                tco_approval_node_description_log nodeLog = guidanceTextList.FirstOrDefault(x => x.node_id == item.pk_node_id);
                item.description = nodeLog.description;
                item.updated = DateTime.UtcNow;
                item.updated_by = userDetails.pk_id;
                await _utility.SaveTextLogAsync(userId, item.pk_node_id, nodeLog.description);
            }
            await dbContext.SaveChangesAsync();

        }


        private static Dictionary<string, string> AddPreviewUrl(PublishTreeType treeType, string url, string bookmark_id,
            tco_publish_config_approval_process configDetails, TenantData tenantData, string editableNodeId, string nodeId)
        {
            Dictionary<string, string> previewUrl = new Dictionary<string, string>();
            string stgUrl = string.Empty;
            string publishType;
            if (!string.IsNullOrEmpty(url))
            {
                #region Domain

                if (treeType.ToString().Equals(PublishTreeType.MonthlyReport.ToString(), StringComparison.InvariantCultureIgnoreCase))
                {
                    stgUrl = AppConfiguration.GetConfigurationSetting("publishMrStagWebSiteUrl");
                    publishType = mr;
                }
                else
                {
                    stgUrl = AppConfiguration.GetConfigurationSetting("publishBmStagWebSiteUrl");
                    publishType = bm;
                }

                #endregion Domain

                if (treeType.ToString().Equals(PublishTreeType.MonthlyReport.ToString(), StringComparison.InvariantCultureIgnoreCase))
                {
                    stgUrl += $"/{configDetails.budget_year}/{tenantData.publish_Id.ToLower()}/{configDetails.url_suffix}/{publishType}-{configDetails.forecast_period}-{configDetails.short_name}/#{url}";
                }
                else
                {
                    stgUrl += $"/{configDetails.budget_year}/{tenantData.publish_Id.ToLower()}/{configDetails.url_suffix}/{publishType}-{configDetails.budget_year}-{configDetails.short_name}/#{url}";
                }
                if (!string.IsNullOrEmpty(bookmark_id))
                {
                    stgUrl += $"?scrollTo={bookmark_id}&preview=true";
                }
                else
                {
                    stgUrl += $"?preview=true";
                }
                if (!stgUrl.Contains("https"))
                {
                    stgUrl = stgUrl.Replace("http", "https");
                }
            }
            previewUrl.Add("previewUrl", stgUrl);
            previewUrl.Add("id", nodeId);
            previewUrl.Add("editableNodeId", !string.IsNullOrEmpty(editableNodeId) ? editableNodeId : string.Empty);
            return previewUrl;
        }

        private static List<ProcessLogGridHelper> SearchLogGrid(IEnumerable<ProcessLogGridHelper> result, ProcessLogSearchHelper inputObject)
        {
            if (inputObject != null)
            {
                if (!string.IsNullOrEmpty(inputObject.lastUpdatedDate))
                {
                    result = result.Where(x => x.lastUpdatedStr.Contains(inputObject.lastUpdatedDate));
                }

                if (!string.IsNullOrEmpty(inputObject.userName))
                {
                    result = result.Where(x => x.lastUpdateBy.ToLower().Contains(inputObject.userName.ToLower()));
                }

                if (!string.IsNullOrEmpty(inputObject.proecessLogSatusId) && inputObject.proecessLogSatusId != "-1")
                {
                    result = result.Where(x => x.proecessLogSatusId == inputObject.proecessLogSatusId);
                }
                if (!string.IsNullOrEmpty(inputObject.userRole) && inputObject.userRole != "-1")
                {
                    result = result.Where(x => x.userTypeId == inputObject.userRole);
                }

                if (inputObject.processId != Guid.Empty)
                {
                    result = result.Where(x => x.processId == inputObject.processId);
                }
            }

            return result.ToList();
        }

        private static IEnumerable<tco_docwf_process> FilterProcessData(IEnumerable<tco_docwf_process> processDataList, string docNodeId)
        {
            if (!string.IsNullOrEmpty(docNodeId) && docNodeId != "0")
            {
                processDataList = processDataList.Where(x => x.doc_chapter_id == docNodeId);
            }

            return processDataList;
        }

        private static string SetEditUptedDisplayText(string process_status, Dictionary<string, clsLanguageString> langString)
        {
            List<string> statusToConsider = new List<string>() { ProccessStatus.Started.ToString(), ProccessStatus.Inprogress.ToString(), ProccessStatus.Rejected.ToString(), ProccessStatus.Returned.ToString() };
            if (statusToConsider.Contains(process_status))
            {
                return langString["ADM_Flow_Grid_editUrl"].LangText;
            }
            else
            {
                return string.Empty;
            }
        }

        private static string CreateProcessStringHTML(IEnumerable<tco_docwf_process_approver_viewer> userDataNodeList, Guid pk_process_id, string descs, IEnumerable<UserData> usersList, Dictionary<string, clsLanguageString> langString, string NodeName)
        {
            StringBuilder sb = new StringBuilder();

            List<int> approverPkid = userDataNodeList.Where(x => x.fk_process_id == pk_process_id && x.User_type == (int)UserListType.ApproverUserList).Select(z => z.fk_user_id).ToList();
            List<int> viewerPkId = userDataNodeList.Where(x => x.fk_process_id == pk_process_id && x.User_type == (int)UserListType.ViewerUserList).Select(z => z.fk_user_id).ToList();
            List<String> AprpverNameList = GetNameFromUserPkIdList(approverPkid, usersList);
            List<String> ViewerNameList = GetNameFromUserPkIdList(viewerPkId, usersList);
            sb.AppendLine("<div class='padding10'>");
            sb.AppendLine("<div class='border-bottom'>");
            sb.AppendLine("<div class='semi bottom10 font15'>" + NodeName + "</div></div>");

            sb.AppendLine("<div class='semi top10'>" + langString["ADM_Flow_viewers"].LangText + "</div>");
            sb.AppendLine("<div class='bottom10 top5'>");
            sb.AppendLine(ViewerNameList.Any() ? String.Join("<spa> , </span>", ViewerNameList) : string.Empty);
            sb.AppendLine("</div>");

            sb.AppendLine("<div class='semi '>" + langString["ADM_Flow_approvers"].LangText + "</div>");
            sb.AppendLine("<div class='bottom10 top5'>");
            sb.AppendLine(AprpverNameList.Any() ? String.Join("<spa> , </span> ", AprpverNameList) : string.Empty);
            sb.AppendLine("</div>");

            sb.AppendLine("<div class='semi'>" + langString["ADM_Flow_msg_readers"].LangText + "</div>");
            sb.AppendLine("<div class='bottom10 top5'>");
            sb.AppendLine("<p>" + descs + "</p>");
            sb.AppendLine("</div>");
            sb.AppendLine("</div>");
            sb.AppendLine("</div>");
            return sb.ToString();
        }

        private static List<string> GetNameFromUserPkIdList(List<int> userPkIdList, IEnumerable<UserData> usersDetailsList)
        {
            List<string> NamesList = new List<string>();
            string Name = string.Empty;
            foreach (var item in userPkIdList)
            {
                var currentUser = usersDetailsList.FirstOrDefault(z => z.pk_id == item);
                if (currentUser != null)
                {
                    Name = currentUser.first_name + " " + currentUser.last_name;
                    NamesList.Add(Name);
                }
            }
            return NamesList;
        }

        private static string SetNameFromUserPkId(int userPkId, IEnumerable<UserData> usersDetailsList)
        {
            string Name = string.Empty;
            var currentUser = usersDetailsList.FirstOrDefault(z => z.pk_id == userPkId);
            if (currentUser != null)
            {
                Name = currentUser.first_name + " " + currentUser.last_name;
            }

            return Name;
        }

        private static string SetProcessStatusText(string process_status, Dictionary<string, clsLanguageString> langString)
        {
            switch (process_status)
            {
                case "NotStarted": return langString["ADM_FinPlan_Process_NotStarted"].LangText;//NotStarted
                case "Started": return langString["ADM_FinPlan_Process_Started"].LangText;//Started,
                case "Inprogress": return langString["ADM_FinPlan_Process_Inprogress"].LangText; //Inprogress,
                case "Rejected": return langString["ADM_FinPlan_Process_Rejected"].LangText; // Rejected,
                case "Returned": return langString["ADM_FinPlan_Process_Returned"].LangText; //Returned,
                case "Approved": return langString["ADM_FinPlan_Process_Approved"].LangText; //Approved,
                case "Finished": return langString["ADM_FinPlan_Process_Finished"].LangText; //Finished
                case "ApproverDone": return langString["ADM_FinPlan_Process_ApproverDone"].LangText; //ApproverDone
                case "ViewerDone": return langString["ADM_FinPlan_Process_ViewerDone"].LangText; //ViewerDone
                case "BCDone": return langString["ADM_FinPlan_Process_BCDone"].LangText; // BCDone
                case "ReviewProcessDone": return langString["ADM_FinPlan_Process_ReviewProcessDone"].LangText; //ReviewProcessDone
                case "PublishStarted": return langString["ADM_FinPlan_Process_PublishStarted"].LangText;   //PublishStarted
                case "Recalled": return langString["ADM_FinPlan_Process_Recalled"].LangText;   //Recalled
                case "process_closed": return langString["ADM_FinPlan_Process_Closed"].LangText;   //doc part open
                case "process_open": return langString["ADM_FinPlan_Process_Open"].LangText;   //doc part closed
                default: return string.Empty;
            }
        }

        private async Task<bool> SetDisableStatus(int budgetYear, IEnumerable<tco_docwf_process> processDataList, string doc_chapter_id, List<KeyValueWithLevelId> keyValueData, UserData userDetails, int forecastPeriod, PublishTreeType treeType, int templateId)
        {
            List<string> chapterId = processDataList.Select(x => x.doc_chapter_id).ToList();
            List<string> treeNodeId = keyValueData.Select(x => x.Key).ToList();

            if (chapterId.Contains(DocChanpterId.TotalNodePart.ToString()))
            {
                return true;
            }
            else if (doc_chapter_id == DocChanpterId.CommonNode.ToString())
            {
                return ((chapterId.Contains(DocChanpterId.CommonNode.ToString())) || (chapterId.Contains(DocChanpterId.TotalNodePart.ToString())) || (await CheckIfProcessClosed(budgetYear, userDetails, doc_chapter_id, forecastPeriod, treeType, templateId)));
            }
            else if (doc_chapter_id == DocChanpterId.TotalNodePart.ToString())
            {
                var serviceNode = chapterId.Intersect(treeNodeId);
                return (serviceNode.Any() || await CheckIfProcessClosed(budgetYear, userDetails, doc_chapter_id, forecastPeriod, treeType, templateId));
            }
            else if (await CheckIfProcessClosed(budgetYear, userDetails, doc_chapter_id, forecastPeriod, treeType, templateId))
            {
                return true;
            }
            else if (chapterId.Contains(doc_chapter_id))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        private async Task<bool> CheckIfProcessClosed(int budgetYear, UserData userDetails, string chapterId, int forecastPeriod, PublishTreeType treeType, int templateId)
        {
            bool isProcessClosed = false;
            var docPartLog = await _unitOfWork.AdminFinplanAprovalRepository.GetSpecificDocWfdocPartLogData(userDetails.tenant_id, treeType.ToString(), templateId, budgetYear, chapterId, forecastPeriod);
            if (docPartLog.Any())
            {
                isProcessClosed = (docPartLog.First().log_action == docPart_status.process_closed.ToString());
            }
            return isProcessClosed;
        }

        private async Task AddApproverViewerUsersToDB(Guid process_id, UserData userDetails, List<int> viewerUserList, List<int> approverUserList)
        {
            try
            {
                foreach (var vwUser in viewerUserList)
                {
                    tco_docwf_process_approver_viewer newData = new tco_docwf_process_approver_viewer()
                    {
                        fk_process_id = process_id,
                        fk_user_id = vwUser,
                        fk_tenant_id = userDetails.tenant_id,
                        User_type = (int)UserListType.ViewerUserList,
                        updated = DateTime.UtcNow,
                        updated_by = userDetails.pk_id,
                        is_mail_alert = false
                    };
                    await _unitOfWork.GenericRepo.AddAsync(newData);
                }
                foreach (var user in approverUserList)
                {
                    tco_docwf_process_approver_viewer newData = new tco_docwf_process_approver_viewer()
                    {
                        fk_process_id = process_id,
                        fk_user_id = user,
                        fk_tenant_id = userDetails.tenant_id,
                        User_type = (int)UserListType.ApproverUserList,
                        updated = DateTime.UtcNow,
                        updated_by = userDetails.pk_id,
                        is_mail_alert = false
                    };
                    await _unitOfWork.GenericRepo.AddAsync(newData);
                }
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        private async Task AddDatatoDB(UserData userDetails, int budgetYear, int forecastPeriod, PublishTreeType pTreeType, List<tco_docwf_responsibles> respobUserData, AdminFinPlanAprProcessHelper item)
        {
            tco_docwf_responsibles currentData = null;
            foreach (var user in item.budgetCoordinator)
            {
                currentData = respobUserData.FirstOrDefault(x => x.doc_chapter_id == item.nodeNameId && x.fk_user_id == user);
                if (currentData == null)
                {
                    tco_docwf_responsibles newData = new tco_docwf_responsibles()
                    {
                        fk_user_id = user,
                        budget_year = budgetYear,
                        doc_chapter_id = item.nodeNameId,
                        doc_type = pTreeType.ToString(),
                        fk_tenant_id = userDetails.tenant_id,
                        forecast_period = forecastPeriod,
                        updated = DateTime.UtcNow,
                        updated_by = userDetails.pk_id,
                        level1Id = item.level1Id
                    };
                    await _unitOfWork.GenericRepo.AddAsync(newData);
                }
            }
            if (item.prevbudgetCoordinator.Any())
            {
                await RemoveDataForPrev(userDetails.tenant_id, item, budgetYear, pTreeType, forecastPeriod);// previous data now removed from selection, remove it from db
            }
        }

        private static void UpdateFinishStatustoDocProcess(UserData userDetails, tco_docwf_process processData, ProccessStatus processStatus)
        {
            processData.process_status = processStatus.ToString();
            processData.updated = DateTime.UtcNow;
            processData.updated_by = userDetails.pk_id;
        }

        private async Task InsertIntoDocProcessLog(UserData userDetails, ProccessLogAction status, AprovalStatusProcessLogSaveHelper saveObj)
        {
            try
            {
                tco_docwf_proccess_log newData = new tco_docwf_proccess_log()
                {
                    fk_process_id = saveObj.processId,
                    fk_tenant_id = userDetails.tenant_id,
                    comments = saveObj.comment,
                    log_action = status.ToString(),
                    updated = DateTime.UtcNow,
                    updated_by = userDetails.pk_id,
                    user_type = saveObj.userType.ToString(),
                    fk_progress_status_id = saveObj.statusId,
                };
                await _unitOfWork.GenericRepo.AddAsync(newData);
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        private async Task AddFinishStatusToDocPart(int budgetYear, UserData userDetails, AdminFinPlanAprProcessHelper item, int templateId, docPart_status docPartStatus, AprovalStatusProcessLogSaveHelper saveObj)
        {
            var docPartLog = await _unitOfWork.AdminFinplanAprovalRepository.GetSpecificDocWfdocPartLogData(userDetails.tenant_id, saveObj.treeType.ToString(), templateId, budgetYear, item.nodeNameId, saveObj.forecastPeriod);
            List<tco_docwf_doc_part_log> docPartList = docPartLog.ToList();
            tco_docwf_doc_part_log currentData = null;
            currentData = docPartList.Count > 0 ? docPartList.FirstOrDefault(x => x.doc_chapter_id == item.nodeNameId) : null;
            if (currentData == null)
            {
                await InsertIntoDocPart(userDetails, budgetYear, saveObj.forecastPeriod, saveObj.treeType, docPartStatus, item, templateId);
            }
            else
            {
                UpdateStatusToDocPartLog(userDetails, currentData, docPartStatus);
            }
        }

        private static void UpdateStatusToDocPartLog(UserData userDetails, tco_docwf_doc_part_log data, docPart_status docPartStatus)
        {
            data.log_action = docPartStatus.ToString();
            data.updated = DateTime.UtcNow;
            data.updated_by = userDetails.pk_id;
        }

        private async Task AddDatatoDocPartLog(UserData userDetails, int budgetYear, int forecastPeriod, PublishTreeType pTreeType, List<tco_docwf_doc_part_log> docPartData, AdminFinPlanAprProcessHelper item, int templateId)
        {
            tco_docwf_doc_part_log currentData = null;
            foreach (var user in item.budgetCoordinator)
            {
                currentData = docPartData.FirstOrDefault(x => x.doc_chapter_id == item.nodeNameId);
                if (currentData == null)
                {
                    await InsertIntoDocPart(userDetails, budgetYear, forecastPeriod, pTreeType, docPart_status.process_open, item, templateId);
                }
            }
        }

        private async Task InsertIntoDocProcess(int budgetYear, UserData userDetails, ProccessStatus status, AdminFinPlanAprProcessHelper item, int templateId)
        {
            Guid process_id = Guid.NewGuid();
            tco_docwf_process newData = new tco_docwf_process()
            {
                owner_user_id = userDetails.pk_id,
                budget_year = budgetYear,
                doc_chapter_id = item.nodeNameId,
                doc_type = PublishTreeType.BudgetManagement.ToString(),
                fk_tenant_id = userDetails.tenant_id,
                forecast_period = 0,
                pk_process_id = process_id,
                created_date = DateTime.UtcNow,
                deadline_date = DateTime.UtcNow,
                process_status = status.ToString(),
                updated = DateTime.UtcNow,
                updated_by = userDetails.pk_id,
                template_id = templateId,
                title = string.Empty
            };
            await _unitOfWork.GenericRepo.AddAsync(newData);
        }

        private async Task InsertIntoDocPart(UserData userDetails, int budgetYear, int forecastPeriod, PublishTreeType treeType, docPart_status status, AdminFinPlanAprProcessHelper item, int templateId)
        {
            tco_docwf_doc_part_log newData = new tco_docwf_doc_part_log()
            {
                budget_year = budgetYear,
                doc_chapter_id = item.nodeNameId,
                doc_type = treeType.ToString(),
                fk_tenant_id = userDetails.tenant_id,
                forecast_period = forecastPeriod,
                fk_template_id = templateId,
                log_action = status.ToString(),
                updated = DateTime.UtcNow,
                updated_by = userDetails.pk_id,
            };
            await _unitOfWork.GenericRepo.AddAsync(newData);
        }

        private async Task RemoveDataForPrev(int tenant_id, AdminFinPlanAprProcessHelper item, int budgetYear, PublishTreeType treeType, int forecastPeriod)
        {
            List<KeyValueIntDataPair> dataTobeRemoved = new List<KeyValueIntDataPair>();
            foreach (var prevUser in item.prevbudgetCoordinator)
            {
                if (!item.budgetCoordinator.Contains(prevUser))
                {
                    dataTobeRemoved.Add(new KeyValueIntDataPair()
                    {
                        Key = item.nodeNameId,
                        Value = prevUser
                    });
                }
            }

            await _unitOfWork.AdminFinplanAprovalRepository.RemoveDataFromDocWfTable(tenant_id, budgetYear, treeType.ToString(), dataTobeRemoved, forecastPeriod);
        }

        private List<AdminFinPlanAprProcessHelper> FormatTableData(List<KeyValueWithLevelId> keyValueData, List<tco_docwf_responsibles> respobUserData)
        {
            List<AdminFinPlanAprProcessHelper> data = new List<AdminFinPlanAprProcessHelper>();
            int count = 1;
            foreach (var item in keyValueData)
            {
                data.Add(new AdminFinPlanAprProcessHelper()
                {
                    id = count,
                    budgetCoordinator = SetUserList(respobUserData, item),// set the user list
                    prevbudgetCoordinator = SetUserList(respobUserData, item),// set the user list
                    nodeName = item.Value,
                    nodeNameId = item.Key,
                    level1Id = item.level1Id
                });
                count++;
            }
            return data;
        }

        private static List<int> SetUserList(List<tco_docwf_responsibles> respobUserData, KeyValueWithLevelId item)
        {
            return respobUserData.Any(x => x.doc_chapter_id == item.Key) ? respobUserData.Where(x => x.doc_chapter_id == item.Key).Select(z => z.fk_user_id).ToList() : new List<int>();
        }

        private async Task<List<KeyValueWithLevelId>> GetServiceAeaFromDocTree(int budgetYear, string userId, IEnumerable<int> userRoleById)
        {
            List<KeyValueWithLevelId> keyValueData = new List<KeyValueWithLevelId>();
            if (userRoleById.Contains(2) || userRoleById.Contains(1))
            {
                keyValueData = await GetTreeData(budgetYear, userId);
            }

            return keyValueData;
        }

        private async Task<List<KeyValueWithLevelId>> GetTreeData(int budgetYear, string userId)
        {
            List<KeyValueWithLevelId> keyValueData;
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TimeSpan cacheTimeOut = new TimeSpan(24, 0, 0);
            int clientId = userDetails.client_id;
            string result = await _cache.GetStringForTenantAsync(clientId, userDetails.tenant_id, "ADM_BasePublishTreeData_" + budgetYear);
            int publishTemplateId = -1;

            if (result == null)
            {
                var treeData = await _publishTree.GetBaseTemplateAsync(userId, publishTemplateId, budgetYear);

                var serviceAreaNodeData = treeData.Tree.Where(x => x.Uid.Contains("BudSa-BudgetByServiceArea")).Select(z => z.items).FirstOrDefault();// get node from tree

                keyValueData = serviceAreaNodeData.Select(x => new KeyValueWithLevelId() { Key = x.Uid, Value = x.text, level1Id = x.id }).ToList();// create key value list
                result = JsonConvert.SerializeObject(keyValueData);
                await _cache.SetStringForTenantAsync(clientId, userDetails.tenant_id, "ADM_BasePublishTreeData_" + budgetYear, result, cacheTimeOut);
            }
            else
            {
                keyValueData = JsonConvert.DeserializeObject<List<KeyValueWithLevelId>>(result);
            }

            return keyValueData;
        }

        private static string SetNodeName(List<KeyValueWithLevelId> keyValueData, string doc_chapter_id, Dictionary<string, clsLanguageString> langString)
        {
            if (doc_chapter_id.ToLower() == DocChanpterId.CommonNode.ToString().ToLower())
            {
                return langString["ADM_FinPlan_CommonNode"].LangText;
            }
            else if (doc_chapter_id.ToLower() == DocChanpterId.TotalNodePart.ToString().ToLower())
            {
                return langString["ADM_FinPlan_TotalPart"].LangText;
            }
            else
            {
                return keyValueData.Any(x => x.Key.ToLower() == doc_chapter_id.ToLower()) ? keyValueData.FirstOrDefault(x => x.Key.ToLower() == doc_chapter_id.ToLower()).Value : string.Empty;
            }
        }

        private static List<int> GetRoleIds(UserListType userType)
        {
            List<int> roleIds;
            if (userType == UserListType.ApproverUserList)
            {
                roleIds = new List<int>() { 2, 34 };// #99749
            }
            else if (userType == UserListType.ViewerUserList)
            {
                roleIds = new List<int>() { 2, 34 };// #99749
            }
            else
            {
                roleIds = new List<int>() { 2, 34 };// #99749
            }
            return roleIds;
        }

        private static string GetCacheName(UserListType userType)
        {
            if (userType == UserListType.ApproverUserList)
            {
                return "ADM_BudgetCoordinatorUserList";
            }
            else if (userType == UserListType.ViewerUserList)
            {
                return "ADM_ViewerUserList";
            }
            else
            {
                return "ADM_BudgetCoordinatorUserList";
            }
        }

        private async Task CheckAndSetReviewDoneStatusForProcess(IEnumerable<tco_docwf_proccess_log> processLogDataList, IEnumerable<tco_docwf_process_approver_viewer> userDataNodeList, tco_docwf_process processData)
        {
            int processApproverCount = userDataNodeList.Count(x => x.User_type == (int)UserListType.ApproverUserList);
            int processViewerCount = userDataNodeList.Count(x => x.User_type == (int)UserListType.ViewerUserList);
            int logProcessApproverCount = processLogDataList.Count(x => x.user_type == UserListType.ApproverUserList.ToString() && x.log_action == ProccessLogAction.ApproverDone.ToString());
            int logProcessViewerCount = processLogDataList.Count(x => x.user_type == UserListType.ViewerUserList.ToString() && x.log_action == ProccessLogAction.ViewerDone.ToString());
            if (processApproverCount > 0 && logProcessApproverCount == processApproverCount)
            {
                processData.process_status = ProccessStatus.ReviewProcessDone.ToString();
            }
            else if ((processViewerCount > 0 && processApproverCount == 0) && logProcessViewerCount == processViewerCount)
            {
                processData.process_status = ProccessStatus.ReviewProcessDone.ToString();
            }
            await _unitOfWork.CompleteAsync();
        }

        private static UserListType SetUserTypeForProcessNodeDropDown(tco_docwf_process process,
            IEnumerable<tco_docwf_process_approver_viewer> userDataList, int loggedinUserPkId,
            List<tco_docwf_proccess_log> processLogDataList, bool isCoordinator = false)
        {
            var userRole = userDataList.Where(x =>
                    x.fk_user_id == loggedinUserPkId && x.fk_process_id == process.pk_process_id)
                .Select(x => (UserListType)x.User_type).ToHashSet();
            
            var logs = processLogDataList
                .Where(x => x.updated_by == loggedinUserPkId &&
                            x.fk_process_id == process.pk_process_id).ToList();
            
            return GetCurrentUserListType(logs, isCoordinator, userRole, process);
        }

        private async Task<bool> ServiceAreaProcessOpen(string userId, int budgetYear, string orgId, int? orgLevel, List<tco_docwf_process> processStatus, PublishTreeType publishTreeType, int forecastPeriod)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            string docLevel1Id = string.Empty;
            string finPlanLevel1 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_1");
            var level1Value_split = finPlanLevel1.Split('_').ToList();
            if (level1Value_split[0].ToUpper() == "ORG")
            {
                if (Convert.ToInt32(level1Value_split[2]) == orgLevel || Convert.ToInt32(level1Value_split[2]) > orgLevel)
                {
                    docLevel1Id = orgId;
                }
                else if (Convert.ToInt32(level1Value_split[2]) < orgLevel)
                {
                    var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));
                    List<tco_org_hierarchy> lstOrgValues = orgVersionContent.lstOrgHierarchy;

                    tco_org_hierarchy orgHierarchy = new tco_org_hierarchy();
                    switch (orgLevel)
                    {
                        case 2:
                            orgHierarchy = lstOrgValues.FirstOrDefault(x => x.org_id_2 == orgId);
                            break;

                        case 3:
                            orgHierarchy = lstOrgValues.FirstOrDefault(x => x.org_id_3 == orgId);
                            break;

                        case 4:
                            orgHierarchy = lstOrgValues.FirstOrDefault(x => x.org_id_4 == orgId);
                            break;

                        case 5:
                            orgHierarchy = lstOrgValues.FirstOrDefault(x => x.org_id_5 == orgId);
                            break;

                        case 6:
                            orgHierarchy = lstOrgValues.FirstOrDefault(x => x.org_id_6 == orgId);
                            break;

                        case 7:
                            orgHierarchy = lstOrgValues.FirstOrDefault(x => x.org_id_7 == orgId);
                            break;

                        case 8:
                            orgHierarchy = lstOrgValues.FirstOrDefault(x => x.org_id_8 == orgId);
                            break;
                    }

                    switch (Convert.ToInt32(level1Value_split[2]))
                    {
                        case 1:
                            docLevel1Id = orgHierarchy.org_id_1;
                            break;

                        case 2:
                            docLevel1Id = orgHierarchy.org_id_2;
                            break;

                        case 3:
                            docLevel1Id = orgHierarchy.org_id_3;
                            break;

                        case 4:
                            docLevel1Id = orgHierarchy.org_id_4;
                            break;

                        case 5:
                            docLevel1Id = orgHierarchy.org_id_5;
                            break;
                    }
                }

                var respobUserData = await _unitOfWork.AdminFinplanAprovalRepository.GetAllDocWfResponsiblesData(userDetails.tenant_id, publishTreeType.ToString(), budgetYear, forecastPeriod);// get all data from db

                List<string> chapterId = respobUserData.Where(x => x.level1Id == docLevel1Id).Select(x => x.doc_chapter_id).ToList();
                return (chapterId.Any() && processStatus.Any(x => chapterId.Contains(x.doc_chapter_id)));
            }
            return false;
        }

        private string InsertProcessLogDetailHtml(IEnumerable<tco_docwf_process_approver_viewer> userDataNodeList, tco_docwf_proccess_log item, IEnumerable<tco_docwf_process> processDataList, IEnumerable<UserData> usersList, Dictionary<string, clsLanguageString> langString, List<KeyValueWithLevelId> Treedata, string currentNodeId)
        {
            if (item.log_action == ProccessLogAction.Started.ToString())
            {
                return CreateProcessStringHTML(userDataNodeList, item.fk_process_id, processDataList.FirstOrDefault(z => z.pk_process_id == item.fk_process_id).Desc, usersList, langString, Treedata.FirstOrDefault(x => x.Key == currentNodeId).Value);
            }
            else
            {
                return CreateProcessLogComentStringHTML(item, usersList, langString, Treedata.FirstOrDefault(x => x.Key == currentNodeId).Value);
            }
        }

        private static string CreateProcessLogComentStringHTML(tco_docwf_proccess_log item, IEnumerable<UserData> usersList, Dictionary<string, clsLanguageString> langString, string NodeName)
        {
            StringBuilder sb = new StringBuilder();

            string userName = SetNameFromUserPkId(item.updated_by, usersList);

            sb.AppendLine("<div class='padding10'>");
            sb.AppendLine("<div class='border-bottom'>");
            sb.AppendLine("<div class='semi bottom10 font15'>" + NodeName + "</div></div>");

            if (item.user_type == UserListType.ApproverUserList.ToString())
            {
                sb.AppendLine("<div class='semi top10'>" + langString["ADM_Flow_approvers_log"].LangText + "</div>");
                sb.AppendLine("<div class='bottom10 top5'>");
                sb.AppendLine(userName);
                sb.AppendLine("</div>");
            }
            else if (item.user_type == UserListType.ViewerUserList.ToString())
            {
                sb.AppendLine("<div class='semi'>" + langString["ADM_Flow_viewers_log"].LangText + "</div>");
                sb.AppendLine("<div class='bottom10 top5'>");
                sb.AppendLine(userName);
                sb.AppendLine("</div>");
            }
            else
            {
                sb.AppendLine("<div class='semi'>" + langString["ADM_Flow_BudGetCordinator_log"].LangText + "</div>");
                sb.AppendLine("<div class='bottom10 top5'>");
                sb.AppendLine(userName);
                sb.AppendLine("</div>");
            }

            if (item.comments != string.Empty)
            {
                sb.AppendLine("<div class='semi'>" + langString["ADM_Flow_detail_log_comment"].LangText + "</div>");
                sb.AppendLine("<div class='bottom10 top5'>");
                sb.AppendLine("<p>" + item.comments + "</p>");
                sb.AppendLine("</div>");
            }
            sb.AppendLine("</div>");
            sb.AppendLine("</div>");
            return sb.ToString();
        }

        private static string GetProcessStatusBasedOnDocPart(tco_docwf_process processData, tco_docwf_doc_part_log docPartData)
        {
            string? status = string.Empty;
            if (docPartData != null)
            {
                if (docPartData.log_action == docPart_status.process_closed.ToString())
                {
                    status = ProccessStatus.Finished.ToString();
                }
                else
                {
                    status = processData != null ? processData.process_status : ProccessStatus.NotStarted.ToString();
                }
            }
            else
            {
                status = processData != null ? processData.process_status : ProccessStatus.NotStarted.ToString();
            }
            return status == null ? string.Empty : status;
        }

        private async Task TriggerNotificationToUser(UserData userDetails, ProccessNotificationType notificationFor, tco_docwf_process processData, string treeType)
        {
            Dictionary<string, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "AdminFinPlanAproval");
            Uri bmApprovalUrl;

            string strUrl = (treeType == PublishTreeType.BudgetManagement.ToString()) ? ("/FinPlanAdminApproval/FinPlanAdminApprovalFlow?FromNotification=true&processId=" + processData.pk_process_id + "&template=" + processData.template_id + "&budgetYear=" + processData.budget_year).ToLower() : ("/MRAdminApproval/MRAdminApprovalFlow?FromNotification=true&processId=" + processData.pk_process_id + "&template=" + processData.template_id + "&forcastPeriod=" + processData.forecast_period).ToLower();

            Uri.TryCreate(strUrl, UriKind.RelativeOrAbsolute, out bmApprovalUrl);

            switch (notificationFor.ToString())
            {
                case "ReviewProcessDone":
                    var budcorUser = await _unitOfWork.AdminFinplanAprovalRepository.GetAllResponsiblesDataForCurrentDocNode(userDetails.tenant_id, processData.doc_type, processData.doc_chapter_id, processData.budget_year, processData.forecast_period);// get all chapter access to current user from tco_docwf_responsibles
                    List<int> budPKIdList = budcorUser.Select(x => x.fk_user_id).ToList();
                    var tcoUserList = await _unitOfWork.AdminFinplanAprovalRepository.GetTcoUsersForMatchingPkIds(budPKIdList);

                    /*Trigger email alert to budgetcoordinators*/
                    await _adminAlert.SendMessageToBudgetCoordinators(userDetails.user_name, processData, treeType == PublishTreeType.BudgetManagement.ToString() ? PublishTreeType.BudgetManagement : PublishTreeType.MonthlyReport, (await GetDocumentTree(userDetails.user_name, processData.budget_year, processData.forecast_period, treeType)).FirstOrDefault(z => z.Key == processData.doc_chapter_id).Value);

                    foreach (var user in tcoUserList)
                    {
                        Guid notId = Guid.NewGuid();
                        CreatApprovalNotification(_notification, userDetails.tenant_id, langString["ntf_approval_pub_website_ReviewDone"].LangText, bmApprovalUrl, notId, user.user_name);
                    }
                    break;

                case "Finished": break;
                case "Recalled": break;
            }

            await _unitOfWork.CompleteAsync();
        }

        private async Task<List<KeyValueWithLevelId>> GetDocumentTree(string userId, int budgetYear, int forecastPeriod, string treeType)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "AdminFinPlanAproval");
            List<KeyValueWithLevelId> Treedata = new List<KeyValueWithLevelId>();
            Treedata.Add(new KeyValueWithLevelId() { Key = DocChanpterId.TotalNodePart.ToString(), level1Id = DocChanpterId.TotalNodePart.ToString(), Value = langString["ADM_FinPlan_TotalPart"].LangText });// total node
            Treedata.Add(new KeyValueWithLevelId() { Key = DocChanpterId.CommonNode.ToString(), level1Id = DocChanpterId.CommonNode.ToString(), Value = langString["ADM_FinPlan_CommonNode"].LangText });// common node
            if (treeType == PublishTreeType.BudgetManagement.ToString())
            {
                Treedata.AddRange(await GetTreeData(budgetYear, userId));// BM service area/serviceIdNode
            }
            else
            {
                Treedata.AddRange(await GetMonthlyReportTreeData(forecastPeriod, userId));// MR service area/serviceIdNode
            }
            return Treedata;
        }

        private static void CreatApprovalNotification(INotification notification, int tenantId, string notificationStrings, Uri bmApprovalUrl, Guid notId, string user)
        {
            notification.CreateNotification(notId, user, tenantId, notificationStrings, bmApprovalUrl, 24);
        }

        private List<KeyValueNewData> GetProcessStatusList(List<DocLogGridDataHelper> data, Dictionary<string, clsLanguageString> langString)
        {
            List<KeyValueNewData> processList = new List<KeyValueNewData>();

            List<string> statusKeyList = data.Select(z => z.lastUpdatedProcessStatusKey).Distinct().ToList();
            foreach (var item in statusKeyList)
            {
                processList.Add(new KeyValueNewData() { Key = item, Value = (item == "-" ? "-" : SetProcessStatusText(item, langString)) });
            }
            return processList;
        }

        private static tco_docwf_process? GetLastUpdatedProcessData(KeyValueWithLevelId item, IEnumerable<tco_docwf_process> processDataList)
        {
            return processDataList.Where(x => x.doc_chapter_id == item.Key).OrderByDescending(x => x.updated).FirstOrDefault();
        }

        private List<KeyValueNewData> GetDocStatusList(IEnumerable<tco_docwf_doc_part_log> data, Dictionary<string, clsLanguageString> langString)
        {
            List<KeyValueNewData> processList = new List<KeyValueNewData>();

            List<string> statusKeyList = data.Select(z => z.log_action).Distinct().ToList();
            foreach (var item in statusKeyList)
            {
                processList.Add(new KeyValueNewData() { Key = item, Value = (item == "-" ? "-" : SetProcessStatusText(item, langString)) });
            }
            return processList;
        }

        private async Task<IEnumerable<UserData>> GetTcoUserData(string userId, bool includeInactive)
        {
            TimeSpan cacheTimeOut = new TimeSpan(24, 0, 0);

            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            int clientId = userDetails.client_id;
            string keyName = (includeInactive) ? "cacheAllUserDataForApproval_includeInactive" : "cacheAllUserDataForApproval_includeActive";
            string strUserData = await _cache.GetStringForTenantAsync(clientId, userDetails.tenant_id, keyName);
            if (string.IsNullOrEmpty(strUserData))
            {
                var userData = await _unitOfWork.AdminFinplanAprovalRepository.GetAllUsers(includeInactive);
                if (userData != null)
                {
                    string szUserData = JsonConvert.SerializeObject(userData);
                    await _cache.SetStringForTenantAsync(clientId, userDetails.tenant_id, keyName, szUserData, cacheTimeOut);
                }
                return userData;
            }
            else
            {
                var UserDataFromCacheDetails = JsonConvert.DeserializeObject<List<UserData>>(strUserData);
                return UserDataFromCacheDetails;
            }
        }

        private async Task ProcessApprovalDescriptions(string userId, int BudgetYear, Guid processId, int templateId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            dynamic queueMessage = new JObject();
            queueMessage.Add("RequestType", PublishTreeType.BudgetManagement.ToString());
            queueMessage.Add("UserId", userId);
            queueMessage.Add("TenantId", userDetails.tenant_id);
            queueMessage.Add("TemplateId", templateId);
            queueMessage.Add("BudgetYear", BudgetYear);
            queueMessage.Add("ProcessId", processId);

            string strQueueMessage = JsonConvert.SerializeObject(queueMessage);
            Dictionary<string, int> modBudgetYears = GetBudgetYearsForExport(BudgetYear);
            await _backendJob.QueueMessageAsync(userDetails, modBudgetYears, QueueName.bmprocessapprovaldescription, strQueueMessage);
        }

        #endregion Private
    }

    public enum DocChanpterId
    {
        CommonNode = 1,
        TotalNodePart
    }

    public enum ProccessLogAction
    {
        NotStarted = 0,
        Started,
        Inprogress,
        ApproverDone,
        ViewerDone,
        BCDone,
        Recalled
    }

    public enum NodeContenantType
    {
        table,
        graph,
        tableWithText,
        graphWithText,
        text
    }

    public enum progressStatus_type
    {
        FP_APPROVAL_REVIEW,
        FP_APPROVAL_BC_REVIEW
    }

    public enum docPart_status
    {
        process_open,
        process_closed
    }
}