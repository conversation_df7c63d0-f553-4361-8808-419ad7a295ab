#pragma warning disable CS8629

#pragma warning disable CS8601
#pragma warning disable CS8603
#pragma warning disable CS8604

using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Framsikt.Entities.Core;
using Microsoft.EntityFrameworkCore;
using System.Diagnostics;

namespace Framsikt.BL.ApprovalProcess.Repository
{
    public class AdminFinplanAprovalRepository : IAdminFinplanAprovalRepository
    {
        private readonly TenantDBContext _tenantDBContext;

        public AdminFinplanAprovalRepository(TenantDBContext dbContext)
        {
            _tenantDBContext = dbContext;
        }

        public async Task<IEnumerable<tco_docwf_responsibles>> GetAllDocWfResponsiblesData(int tenantId, string doc_type, int budgetYear, int forecastePeriod)
        {
            if (doc_type == PublishTreeType.MonthlyReport.ToString())
            {
                return await _tenantDBContext.tco_docwf_responsibles.Where(x => x.fk_tenant_id == tenantId && x.doc_type == doc_type && x.forecast_period == forecastePeriod).ToListAsync();
            }
            else
            {
                return await _tenantDBContext.tco_docwf_responsibles.Where(x => x.fk_tenant_id == tenantId && x.doc_type == doc_type && x.budget_year == budgetYear).ToListAsync();
            }
        }

        public async Task<IEnumerable<tco_docwf_doc_part_log>> GetSpecificDocWfdocPartLogData(int tenantId, string doc_type, int templateId, int budgetYear, string chapterId, int forecastePeriod = 0)
        {
            return forecastePeriod == 0 ? await _tenantDBContext.tco_docwf_doc_part_log.Where(x => x.fk_tenant_id == tenantId && x.fk_template_id == templateId && x.doc_type == doc_type && x.budget_year == budgetYear && x.doc_chapter_id == chapterId).ToListAsync() : await _tenantDBContext.tco_docwf_doc_part_log.Where(x => x.fk_tenant_id == tenantId && x.fk_template_id == templateId && x.doc_type == doc_type && x.forecast_period == forecastePeriod && x.doc_chapter_id == chapterId).ToListAsync();
        }

        public async Task<tco_docwf_template?> GetDocWfTemplateData(int tenantId, string doc_type, int budgetYear, int forecastePeriod)
        {
            if (doc_type == PublishTreeType.MonthlyReport.ToString())
            {
                return await _tenantDBContext.tco_docwf_template.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.doc_type == doc_type && x.forecast_period == forecastePeriod);
            }
            else
            {
                return await _tenantDBContext.tco_docwf_template.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.doc_type == doc_type && x.budget_year == budgetYear);
            }
        }

        public async Task RemoveDataFromDocWfTable(int tenantId, int budgetYear, string doc_type, List<KeyValueIntDataPair> dataTobeRemoved, int forecastePeriod = 0)
        {
            var chaperterId = dataTobeRemoved.Select(x => x.Key).Distinct().ToList();
            var chaperterIdUsers = dataTobeRemoved.Select(x => x.Value).Distinct().ToList();
            var dataToBeDeleted = forecastePeriod == 0 ? await _tenantDBContext.tco_docwf_responsibles.
                                                         Where(x => x.fk_tenant_id == tenantId && x.doc_type == doc_type && x.budget_year == budgetYear &&
                                                               chaperterId.Contains(x.doc_chapter_id) && chaperterIdUsers.Contains(x.fk_user_id)).ToListAsync()
                                                       : await _tenantDBContext.tco_docwf_responsibles.
                                                         Where(x => x.fk_tenant_id == tenantId && x.doc_type == doc_type && x.forecast_period == forecastePeriod &&
                                                               chaperterId.Contains(x.doc_chapter_id) && chaperterIdUsers.Contains(x.fk_user_id)).ToListAsync();

            _tenantDBContext.tco_docwf_responsibles.RemoveRange(dataToBeDeleted);
        }

        public async Task<IEnumerable<TcoPublishTemplate>> GetAllDocPublishTemplate(int tenantId, PublishTreeType treeType, int budgetYear, int forecastePeriod)
        {
            if (treeType == PublishTreeType.MonthlyReport)
            {
                var templateList = await _tenantDBContext.TcoPublishTemplate.Where(x => x.FkTenantId == tenantId && x.IsGlobal && x.BudgetYear == budgetYear && (!x.IsDefault) && x.ForecastPeriod == forecastePeriod).ToListAsync();
                return templateList.Where(x => x.TreeType.Equals(treeType.ToString(), StringComparison.InvariantCultureIgnoreCase)).ToList();
            }
            else
            {
                var templateList = await _tenantDBContext.TcoPublishTemplate.Where(x => x.FkTenantId == tenantId && x.IsGlobal && x.BudgetYear == budgetYear && (!x.IsDefault)).ToListAsync();
                return templateList.Where(x => x.TreeType.Equals(treeType.ToString(), StringComparison.InvariantCultureIgnoreCase)).ToList();
            }
        }

        public TcoPublishTemplate GetSelectedDocPublishTemplate(int tenantId, int templateId, PublishTreeType treeType, int budgetYear, int forecastePeriod = 0)
        {
            if (treeType == PublishTreeType.MonthlyReport)
            {
                var templateList = _tenantDBContext.TcoPublishTemplate.Where(x => x.FkTenantId == tenantId && x.IsGlobal && x.BudgetYear == budgetYear && x.ForecastPeriod == forecastePeriod && x.PkId == templateId).ToList();
                return templateList.FirstOrDefault(x => x.TreeType.Equals(treeType.ToString(), StringComparison.InvariantCultureIgnoreCase));
            }
            else
            {
                var templateList = _tenantDBContext.TcoPublishTemplate.Where(x => x.FkTenantId == tenantId && x.IsGlobal && x.BudgetYear == budgetYear && x.PkId == templateId).ToList();
                return templateList.FirstOrDefault(x => x.TreeType.Equals(treeType.ToString(), StringComparison.InvariantCultureIgnoreCase));
            }
        }

        public tco_budget_phase GetBudgetPhaseData(int tenantId, Guid budgetPhase)
        {
            return _tenantDBContext.tco_budget_phase.FirstOrDefault(x => x.fk_tenant_id == tenantId && x.org_budget_flag == 1 && x.status == 1 && x.pk_budget_phase_id == budgetPhase);
        }

        public async Task<IEnumerable<UserData>> GetAllUsers(bool includeInactive)
        {
            return includeInactive ? await (from ud in _tenantDBContext.tco_users
                                            select new UserData
                                            {
                                                pk_id = ud.pk_id,
                                                first_name = ud.first_name,
                                                last_name = ud.last_name,
                                                IsActive = ud.IsActive.Value,
                                                user_name = ud.user_name,
                                            }).OrderBy(z => z.first_name).ThenBy(x => x.last_name).ToListAsync() : await (from ud in _tenantDBContext.tco_users
                                                                                                                          where ud.IsActive.HasValue && ud.IsActive.Value
                                                                                                                          select new UserData
                                                                                                                          {
                                                                                                                              pk_id = ud.pk_id,
                                                                                                                              first_name = ud.first_name,
                                                                                                                              last_name = ud.last_name,
                                                                                                                              IsActive = ud.IsActive.Value,
                                                                                                                              user_name = ud.user_name,
                                                                                                                          }).OrderBy(z => z.first_name).ThenBy(x => x.last_name).ToListAsync();
        }

        public async Task<IEnumerable<tco_docwf_responsibles>> GetCurrentUserNodeAccessList(int tenantId, string doc_type, int budgetYear, int userPkId, int forecastePeriod = 0)
        {
            if (doc_type == PublishTreeType.MonthlyReport.ToString())
            {
                return await _tenantDBContext.tco_docwf_responsibles.Where(x => x.fk_tenant_id == tenantId && x.doc_type == doc_type && x.forecast_period == forecastePeriod && x.fk_user_id == userPkId).Distinct().ToListAsync();
            }
            else
            {
                return await _tenantDBContext.tco_docwf_responsibles.Where(x => x.fk_tenant_id == tenantId && x.doc_type == doc_type && x.budget_year == budgetYear && x.fk_user_id == userPkId).Distinct().ToListAsync();
            }
        }

        public async Task<IEnumerable<tco_docwf_process_approver_viewer>> GetAproverViewerUserDataForProcess(int tenantId, Guid processId)
        {
            try
            {
                await Task.Delay(1000);
                return await _tenantDBContext.tco_docwf_process_approver_viewer.Where(x => x.fk_tenant_id == tenantId && x.fk_process_id == processId).ToListAsync();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException(ex.Message);
            }
        }

        public void RemoveDataFromDocWfApproverViewer(int tenantId, List<int> dataTobeRemoved, Guid processId, UserListType userType)
        {
            var dataToBeDeleted = _tenantDBContext.tco_docwf_process_approver_viewer.Where(x => x.fk_tenant_id == tenantId && x.fk_process_id == processId && x.User_type == (int)userType && dataTobeRemoved.Contains(x.fk_user_id)).ToList();

            _tenantDBContext.tco_docwf_process_approver_viewer.RemoveRange(dataToBeDeleted);
        }

        public async Task<IEnumerable<tco_docwf_process>> GetDocWfProcessData(int tenantId, string doc_type, int templateId, int budgetYear, int forecastePeriod = 0)
        {
            if (doc_type == PublishTreeType.MonthlyReport.ToString())
            {
                return await _tenantDBContext.tco_docwf_process.Where(x => x.fk_tenant_id == tenantId && x.doc_type == doc_type && x.template_id == templateId && x.forecast_period == forecastePeriod && x.process_status != ProccessStatus.Finished.ToString()).ToListAsync();
            }
            else
            {
                return await _tenantDBContext.tco_docwf_process.Where(x => x.fk_tenant_id == tenantId && x.doc_type == doc_type && x.template_id == templateId && x.budget_year == budgetYear && x.process_status != ProccessStatus.Finished.ToString()).ToListAsync();
            }
        }

        public async Task<IEnumerable<tco_docwf_process>> GetAllDocWfProcessData(int tenantId, string doc_type, int budgetYear, int forecastPeriod = 0)
        {
            if (doc_type == PublishTreeType.MonthlyReport.ToString())
            {
                return await _tenantDBContext.tco_docwf_process.Where(x => x.fk_tenant_id == tenantId && x.doc_type == doc_type && x.forecast_period == forecastPeriod).ToListAsync();
            }
            else
            {
                return await _tenantDBContext.tco_docwf_process.Where(x => x.fk_tenant_id == tenantId && x.doc_type == doc_type && x.budget_year == budgetYear).ToListAsync();
            }
        }

        public async Task<IEnumerable<tco_docwf_process_approver_viewer>> GetAllAproverViewerUserDataForProcess(int tenantId, List<Guid> processPkId)
        {
            return await _tenantDBContext.tco_docwf_process_approver_viewer.Where(x => x.fk_tenant_id == tenantId && processPkId.Contains(x.fk_process_id)).ToListAsync();
        }

        public async Task<List<tco_docwf_proccess_log>> GetAllDocWfProcessLogData(int tenantId, List<Guid> processPkId)
        {
            return await _tenantDBContext.tco_docwf_proccess_log.Where(x => x.fk_tenant_id == tenantId && processPkId.Contains(x.fk_process_id)).ToListAsync();
        }

        public async Task<IEnumerable<KeyValueInt>> GetAllProgress_status(int tenantId, string progress_status_type)
        {
            return await _tenantDBContext.tco_progress_status.Where(x => x.fk_tenant_id == tenantId && x.type == progress_status_type).Select(z => new KeyValueInt() { Key = z.status_id, Value = z.status_description }).ToListAsync();
        }

        public async Task<IEnumerable<tco_docwf_process>> GetDocWfProcessDataForTemplate(int tenantId, string doc_type, int templateId)
        {
            return await _tenantDBContext.tco_docwf_process.Where(x => x.fk_tenant_id == tenantId && x.doc_type == doc_type && x.template_id == templateId && x.process_status != ProccessStatus.Finished.ToString()).ToListAsync();
        }

        public async Task<tco_docwf_process?> GetDocWfProcessDataForSelectedProcessId(int tenantId, Guid processId)
        {
            return await _tenantDBContext.tco_docwf_process.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.pk_process_id == processId);
        }

        public tco_docwf_process GetDocWfProcessDataForSelectedProcessIdNonAsync(int tenantId, Guid processId)
        {
            return _tenantDBContext.tco_docwf_process.FirstOrDefault(x => x.fk_tenant_id == tenantId && x.pk_process_id == processId);
        }

        public async Task<IEnumerable<tco_publish_review_child>> GetProcessTreeData(int tenantId, int templateId, PublishTreeType treeType, string parent, Guid processId)
        {
            return await (from td in _tenantDBContext.tco_publish_review_child
                          where td.fk_tenant_id == tenantId && td.template_id == templateId && td.tree_type == treeType.ToString() && td.parent == parent && td.processId == processId
                          select td).ToListAsync();
        }

        public async Task<string> GetParentNodeId(int tenantId, int templateId, PublishTreeType treeType, string parent, Guid processId)
        {
            var data = await _tenantDBContext.tco_publish_review_child.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.template_id == templateId && x.tree_type == treeType.ToString() && x.uid == parent && x.processId == processId);
            if (data == null)
                return string.Empty;
            return data.nodeId;
        }

        public async Task<IEnumerable<tco_publish_review_child>> GetProcessTreeDataForSAProcess(int tenantId, int templateId, PublishTreeType treeType, Guid processId)
        {
            return await (from td in _tenantDBContext.tco_publish_review_child
                          where td.fk_tenant_id == tenantId && td.template_id == templateId && td.tree_type == treeType.ToString() && td.processId == processId
                          select td).ToListAsync();
        }

        public async Task<tco_publish_review_parent> GetParentDetail(int tenantId, Guid processId)
        {
            return await _tenantDBContext.tco_publish_review_parent.FirstOrDefaultAsync(z => z.fk_tenant_id == tenantId && z.processId == processId);
        }

        public async Task<tco_publish_config_approval_process> GetPublishConfig(int tenantId, int configId)
        {
            return await _tenantDBContext.tco_publish_config_approval_process.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.pk_id == configId);
        }

        public async Task<gmd_publish_tree_node_definitions> GetNodeDefinition(string nodeId, string treeType)
        {
            return await _tenantDBContext.gmd_publish_tree_node_definitions.FirstOrDefaultAsync(x => x.type == nodeId && x.tree_type == treeType);
        }

        public async Task<tco_docwf_proccess_log> GetDocWfProcessLogDataByUserId(int tenantId, Guid processPkId,
            int fkUserId)
        {
            return await _tenantDBContext.tco_docwf_proccess_log.Where(x =>
                    x.fk_tenant_id == tenantId && x.fk_process_id == processPkId && x.updated_by == fkUserId)
                .OrderByDescending(x => x.updated)
                .FirstOrDefaultAsync();
        }

        public async Task<IEnumerable<tco_docwf_process_approver_viewer>> GetCurrentUserAproverViewerUserDataForProcess(
            int tenantId, List<Guid> processPkId, int userPkId)
        {
            return await _tenantDBContext.tco_docwf_process_approver_viewer.Where(x =>
                    x.fk_tenant_id == tenantId && processPkId.Contains(x.fk_process_id) && x.fk_user_id == userPkId)
                .ToListAsync();
        }

        public async Task<IEnumerable<tco_docwf_process_approver_viewer>> GetAproverViewerDataForCurrentUser(int tenantId, int userPkId, List<Guid> processPkId)
        {
            return await _tenantDBContext.tco_docwf_process_approver_viewer.Where(x => x.fk_tenant_id == tenantId && x.fk_user_id == userPkId && processPkId.Contains(x.fk_process_id)).ToListAsync();
        }

        public async Task<bool> IsCoordinatorForProcess(int tenantId, int userPkId, Guid processId)
        {
            var processData = await GetDocWfProcessDataForSelectedProcessId(tenantId, processId);

            if (processData == null)
                return false;
            
            return await GetDocCoordinatorsQuery(tenantId, processData.doc_type, userPkId, processData.budget_year,
                    processData.forecast_period, processData.doc_chapter_id).AnyAsync();
        }

        public async Task<IEnumerable<tco_docwf_responsibles>> GetAllDocWfResponsiblesDataForCurrentUser(int tenantId,
            string docType, int userPkId, int budgetYear, int forecastePeriod = 0, string? docChapterId = null)
        {
            return await GetDocCoordinatorsQuery(tenantId, docType, userPkId, budgetYear, forecastePeriod, docChapterId)
                .ToListAsync();
        }

        private IQueryable<tco_docwf_responsibles> GetDocCoordinatorsQuery(int tenantId, string docType,
            int userPkId, int budgetYear, int forecastePeriod,
            string? docChapterId)
        {
            var query = _tenantDBContext.tco_docwf_responsibles.Where(x =>
                x.fk_tenant_id == tenantId && x.doc_type == docType && x.fk_user_id == userPkId);

            if (!string.IsNullOrEmpty(docChapterId))
            {
                query = query.Where(x => x.doc_chapter_id == docChapterId);
            }

            return docType == nameof(PublishTreeType.MonthlyReport)
                ? query.Where(x => x.forecast_period == forecastePeriod)
                : query.Where(x => x.budget_year == budgetYear);
        }

        public async Task<IEnumerable<tco_docwf_process>> GetAllDocWfProcessDataByNodeList(int tenantId, string doc_type, List<string> nodeList, int budgetYear, int forecastPeriod = 0)
        {
            if (doc_type == PublishTreeType.MonthlyReport.ToString())
            {
                return await _tenantDBContext.tco_docwf_process.Where(x => x.fk_tenant_id == tenantId && x.doc_type == doc_type && x.forecast_period == forecastPeriod && nodeList.Contains(x.doc_chapter_id)).ToListAsync();
            }
            else
            {
                return await _tenantDBContext.tco_docwf_process.Where(x => x.fk_tenant_id == tenantId && x.doc_type == doc_type && x.budget_year == budgetYear && nodeList.Contains(x.doc_chapter_id)).ToListAsync();
            }
        }

        public async Task<IEnumerable<tco_docwf_process>> GetProcessStatus(int tenantId, int budgetYear, string publishTreeType, int forecastPeriod)
        {
            if (publishTreeType == PublishTreeType.MonthlyReport.ToString())
            {
                return await _tenantDBContext.tco_docwf_process.Where(x => x.fk_tenant_id == tenantId && x.doc_type == publishTreeType && x.forecast_period == forecastPeriod && (x.process_status != ProccessStatus.Finished.ToString() && x.process_status != ProccessStatus.Recalled.ToString())).ToListAsync();
            }
            else
            {
                return await _tenantDBContext.tco_docwf_process.Where(x => x.fk_tenant_id == tenantId && x.doc_type == publishTreeType && x.budget_year == budgetYear && (x.process_status != ProccessStatus.Finished.ToString() && x.process_status != ProccessStatus.Recalled.ToString())).ToListAsync();
            }
        }

        public async Task<tco_publish_review_child> GetProcessTreeCurrentNodeData(int tenantId, int templateId, string treeType, string nodeUid, string nodeId, Guid processId)
        {
            return await _tenantDBContext.tco_publish_review_child.FirstOrDefaultAsync(z => z.fk_tenant_id == tenantId && z.template_id == templateId && z.tree_type == treeType && z.nodeId == nodeId && z.processId == processId && z.uid == nodeUid);
        }

        public async Task<List<tco_publish_review_child>> GetProcessTreeCurrentNodeDataAllProcess(int tenantId, int templateId, string treeType, string nodeId)
        {
            return await _tenantDBContext.tco_publish_review_child.Where(z => z.fk_tenant_id == tenantId && z.template_id == templateId && z.tree_type == treeType && z.uid == nodeId && z.Show_Edit_Warning_Icon.HasValue && z.Show_Edit_Warning_Icon.Value).ToListAsync();
        }

        public async Task<IEnumerable<tco_docwf_table_comments>> GetNodeComments(int tenantId, Guid processId, string uniqueId, int budgetYear, int forecastPeriod, string treeType)
        {
            var process = await GetDocWfProcessDataForSelectedProcessId(tenantId, processId);
            if (treeType == PublishTreeType.MonthlyReport.ToString())
            {
                return await _tenantDBContext.tco_docwf_table_comments.Where(z => z.fk_tenant_id == tenantId && z.tree_type == treeType.ToString()
                          && z.unique_id == uniqueId && z.forcast_period == forecastPeriod && z.template_id == process.template_id && z.doc_chapter_id == process.doc_chapter_id).ToListAsync();
            }
            else
            {
                return await _tenantDBContext.tco_docwf_table_comments.Where(z => z.fk_tenant_id == tenantId && z.tree_type == treeType.ToString()
                         && z.unique_id == uniqueId && z.budget_year == budgetYear && z.template_id == process.template_id && z.doc_chapter_id == process.doc_chapter_id).ToListAsync();
            }
        }

        public async Task<IEnumerable<ActiveUsersHelper>> GetAllCommenters(int tenantId, List<int> pkUserIds)
        {
            var activeUsersData = await (from a in _tenantDBContext.tco_users
                                         join b in _tenantDBContext.tco_users_settings on a.pk_id equals b.fk_user_id
                                         where b.tenant_id == tenantId && pkUserIds.Contains(a.pk_id)// remove join
                                         select new { a.pk_id, a.first_name, a.last_name, a.user_name }).Distinct().ToListAsync();

            return activeUsersData.Select(x => new ActiveUsersHelper
            {
                Id = x.pk_id,
                Firstname = x.first_name,
                Lastname = x.last_name,
                Username = x.user_name
            });
        }

        public async Task<tco_docwf_table_comments> GetNodeCommentById(int tenantId, int pkId)
        {
            return await _tenantDBContext.tco_docwf_table_comments.FirstOrDefaultAsync(z => z.fk_tenant_id == tenantId && z.pk_id == pkId);
        }

        public async Task DeleteNodeComments(int tenantId, int pkId)
        {
            var nodeComment = await _tenantDBContext.tco_docwf_table_comments.FirstOrDefaultAsync(z => z.fk_tenant_id == tenantId && z.pk_id == pkId);
            if (nodeComment != null)
            {
                _tenantDBContext.tco_docwf_table_comments.Remove(nodeComment);
                await _tenantDBContext.SaveChangesAsync();
            }
        }

        public async Task<IEnumerable<tco_docwf_doc_part_log>> GetAllDocWfdocPartLogDataOfChapters(int tenantId, string doc_type, int templateId, int budgetYear, List<string> chaptersList, int forecastePeriod = 0)
        {
            return forecastePeriod == 0 ? await _tenantDBContext.tco_docwf_doc_part_log.Where(x => x.fk_tenant_id == tenantId && x.doc_type == doc_type && x.fk_template_id == templateId && x.budget_year == budgetYear && chaptersList.Contains(x.doc_chapter_id)).ToListAsync() : await _tenantDBContext.tco_docwf_doc_part_log.Where(x => x.fk_tenant_id == tenantId && x.doc_type == doc_type && x.fk_template_id == templateId && x.forecast_period == forecastePeriod && chaptersList.Contains(x.doc_chapter_id)).ToListAsync();
        }

        public async Task<tco_docwf_process> GetDocWfProcessForChapterId(int tenantId, string chapterId)
        {
            return await _tenantDBContext.tco_docwf_process.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.doc_chapter_id == chapterId);
        }

        public async Task<IEnumerable<tco_docwf_responsibles>> GetAllResponsiblesDataForCurrentDocNode(int tenantId, string doc_type, string docNode, int budgetYear, int forecastePeriod = 0)
        {
            if (doc_type == PublishTreeType.MonthlyReport.ToString())
            {
                return await _tenantDBContext.tco_docwf_responsibles.Where(x => x.fk_tenant_id == tenantId && x.doc_type == doc_type && x.forecast_period == forecastePeriod && x.doc_chapter_id == docNode).ToListAsync();
            }
            else
            {
                return await _tenantDBContext.tco_docwf_responsibles.Where(x => x.fk_tenant_id == tenantId && x.doc_type == doc_type && x.budget_year == budgetYear && x.doc_chapter_id == docNode).ToListAsync();
            }
        }

        public async Task<IEnumerable<UserData>> GetTcoUsersForMatchingPkIds(List<int> userPkiList)
        {
            return await
                (from ud in _tenantDBContext.tco_users
                 where userPkiList.Contains(ud.pk_id)
                 select new UserData
                 {
                     pk_id = ud.pk_id,
                     first_name = ud.first_name,
                     last_name = ud.last_name,
                     IsActive = ud.IsActive.Value,
                     user_name = ud.user_name
                 }).ToListAsync();

        }

        public async Task<IEnumerable<int>> GetAllTemplateIdsUsedInProcess(int tenantId, string doc_type, int budgetYear, int forecastPeriod = 0)
        {
            if (doc_type == PublishTreeType.MonthlyReport.ToString())
            {
                return await _tenantDBContext.tco_docwf_process.Where(x => x.fk_tenant_id == tenantId && x.doc_type == doc_type && x.forecast_period == forecastPeriod).Select(z => z.template_id).Distinct().ToListAsync();
            }
            else
            {
                return await _tenantDBContext.tco_docwf_process.Where(x => x.fk_tenant_id == tenantId && x.doc_type == doc_type && x.budget_year == budgetYear).Select(z => z.template_id).Distinct().ToListAsync();
            }
        }

        public async Task<IEnumerable<tco_docwf_process>> GetAllProcessDataForTemplate(int tenantId, string doc_type, int budgetYear, int forecastPeriod, int templateId)
        {
            if (doc_type == PublishTreeType.MonthlyReport.ToString())
            {
                return await _tenantDBContext.tco_docwf_process.Where(x => x.fk_tenant_id == tenantId && x.doc_type == doc_type && x.forecast_period == forecastPeriod && x.template_id == templateId).ToListAsync();
            }
            else
            {
                return await _tenantDBContext.tco_docwf_process.Where(x => x.fk_tenant_id == tenantId && x.doc_type == doc_type && x.budget_year == budgetYear && x.template_id == templateId).ToListAsync();
            }
        }

        public async Task<IEnumerable<tco_docwf_doc_part_log>> GetAllDocWfdocPartLogData(int tenantId, string doc_type, int budgetYear, int templateId, int forecastePeriod = 0)
        {
            if (doc_type == PublishTreeType.MonthlyReport.ToString())
            {
                return await _tenantDBContext.tco_docwf_doc_part_log.Where(x => x.fk_tenant_id == tenantId && x.doc_type == doc_type && x.forecast_period == forecastePeriod && x.fk_template_id == templateId).ToListAsync();
            }
            else
            {
                return await _tenantDBContext.tco_docwf_doc_part_log.Where(x => x.fk_tenant_id == tenantId && x.doc_type == doc_type && x.budget_year == budgetYear && x.fk_template_id == templateId).ToListAsync();
            }
        }

        public async Task<IEnumerable<vw_tco_parameters>> GetParameterValues(int tenantId)
        {
            return await (from monthlyReportParams in _tenantDBContext.vw_tco_parameters
                          where monthlyReportParams.fk_tenant_id == tenantId &&
                          (monthlyReportParams.param_name == "MONTHREP_LEVEL_1" || monthlyReportParams.param_name == "MONTHREP_LEVEL_2")
                          && monthlyReportParams.active == 1
                          select monthlyReportParams).OrderBy(x => x.param_value).ToListAsync();
        }

        public async Task<string> GetDocChapterText(int tenantId, string treeType, int templateId, Guid processId, string docChapterId)
        {
            var doc = await _tenantDBContext.tco_publish_review_child.FirstOrDefaultAsync(z => z.fk_tenant_id == tenantId && z.processId == processId && z.template_id == templateId && z.tree_type == treeType && z.uid == docChapterId);
            return doc != null ? doc.text : string.Empty;
        }

        public async Task<IEnumerable<tco_publish_review_child>> GetPublishNodesByProcessId(int tenantId, Guid processId, string treeType) {
            return await _tenantDBContext.tco_publish_review_child.Where(x => x.fk_tenant_id == tenantId && x.processId == processId && x.tree_type == treeType).ToListAsync();
        }

        public async Task<IEnumerable<tco_approval_node_description_log>> GetApprovalNodeLogData(int tenantId, int budgetYear, string nodeType)
        {
            return await _tenantDBContext.tco_approval_node_description_log.Where(x =>
                x.fk_tenant_id == tenantId &&
                x.node_type == nodeType &&
                x.budget_year == budgetYear &&
                x.is_edited).ToListAsync();
        }

        public async Task<IEnumerable<tco_publish_review_child>> GetAllProcessTreeData(int tenantId, int templateId, string treeType, Guid processId)
        {
            return await (from td in _tenantDBContext.tco_publish_review_child
                          where td.fk_tenant_id == tenantId && td.template_id == templateId && td.tree_type == treeType.ToString() && td.processId == processId
                          select td).ToListAsync();
        }
    }
}