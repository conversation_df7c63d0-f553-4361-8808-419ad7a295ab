using Framsikt.BL.Repository;
using Framsikt.Entities;

namespace Framsikt.BL.ApprovalProcess.Repository
{
    public class AdminFinplanAprovalUoW : UnitOfWorkBase, IAdminFinplanAprovalUoW
    {
        private readonly TenantDBContext _tenantDbContext;

        public AdminFinplanAprovalUoW(IDbContextManager dbContextManager) : base(dbContextManager)
        {
            //To Do: Remove creation of objects using new
            _tenantDbContext = dbContextManager.GetTenantDbContext();
            GenericRepo = new GenericRepository(GetTenantDbContext());
            AdminFinplanAprovalRepository = new AdminFinplanAprovalRepository(_tenantDbContext);
            BudgetProposalRepository = new BudgetProposalRepository(_tenantDbContext, dbContextManager);
        }

        public IAdminFinplanAprovalRepository AdminFinplanAprovalRepository { get; private set; }

        public IBudgetProposalRepository BudgetProposalRepository { get; private set; }
    }
}