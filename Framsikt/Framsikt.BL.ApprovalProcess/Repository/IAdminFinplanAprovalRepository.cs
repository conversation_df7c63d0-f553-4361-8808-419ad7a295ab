using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Framsikt.Entities.Core;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Framsikt.BL.ApprovalProcess.Repository
{
    public interface IAdminFinplanAprovalRepository
    {
        Task<IEnumerable<tco_docwf_responsibles>> GetAllDocWfResponsiblesData(int tenantId, string doc_type, int budgetYear, int forecastePeriod);

        Task<IEnumerable<tco_docwf_doc_part_log>> GetAllDocWfdocPartLogData(int tenantId, string doc_type, int budgetYear, int templateId, int forecastePeriod = 0);

        Task<IEnumerable<tco_docwf_doc_part_log>> GetSpecificDocWfdocPartLogData(int tenantId, string doc_type, int templateId, int budgetYear, string chapterId, int forecastePeriod = 0);

        Task<tco_docwf_template?> GetDocWfTemplateData(int tenantId, string doc_type, int budgetYear, int forecastePeriod);

        Task RemoveDataFromDocWfTable(int tenantId, int budgetYear, string doc_type, List<KeyValueIntDataPair> dataTobeRemoved, int forecastePeriod = 0);

        Task<IEnumerable<UserData>> GetAllUsers(bool includeInactive);

        Task<IEnumerable<TcoPublishTemplate>> GetAllDocPublishTemplate(int tenantId, PublishTreeType treeType, int budgetYear, int forecastePeriod);

        tco_budget_phase GetBudgetPhaseData(int tenantId, Guid budgetPhase);

        TcoPublishTemplate GetSelectedDocPublishTemplate(int tenantId, int templateId, PublishTreeType treeType, int budgetYear, int forecastePeriod = 0);

        Task<IEnumerable<tco_docwf_responsibles>> GetCurrentUserNodeAccessList(int tenantId, string doc_type, int budgetYear, int userPkId, int forecastePeriod = 0);

        Task<IEnumerable<tco_docwf_process_approver_viewer>> GetAproverViewerUserDataForProcess(int tenantId, Guid processId);

        void RemoveDataFromDocWfApproverViewer(int tenantId, List<int> dataTobeRemoved, Guid processId, UserListType userType);

        Task<IEnumerable<tco_docwf_process>> GetDocWfProcessData(int tenantId, string doc_type, int templateId, int budgetYear, int forecastePeriod = 0);

        Task<IEnumerable<tco_docwf_process>> GetAllDocWfProcessData(int tenantId, string doc_type, int budgetYear, int forecastPeriod = 0);

        Task<IEnumerable<tco_docwf_process_approver_viewer>> GetAllAproverViewerUserDataForProcess(int tenantId, List<Guid> processPkId);

        Task<List<tco_docwf_proccess_log>> GetAllDocWfProcessLogData(int tenantId, List<Guid> processPkId);

        Task<IEnumerable<KeyValueInt>> GetAllProgress_status(int tenantId, string progress_status_type);

        Task<IEnumerable<tco_docwf_process>> GetDocWfProcessDataForTemplate(int tenantId, string doc_type, int templateId);

        Task<tco_docwf_process?> GetDocWfProcessDataForSelectedProcessId(int tenantId, Guid processId);

        tco_docwf_process GetDocWfProcessDataForSelectedProcessIdNonAsync(int tenantId, Guid processId);

        Task<tco_publish_review_parent> GetParentDetail(int tenantId, Guid processId);

        Task<tco_publish_config_approval_process> GetPublishConfig(int tenantId, int configId);

        Task<IEnumerable<tco_publish_review_child>> GetProcessTreeData(int tenantId, int templateId, PublishTreeType treeType, string parent, Guid processId);

        Task<string> GetParentNodeId(int tenantId, int templateId, PublishTreeType treeType, string parent, Guid processId);

        Task<IEnumerable<tco_publish_review_child>> GetProcessTreeDataForSAProcess(int tenantId, int templateId, PublishTreeType treeType, Guid processId);

        Task<gmd_publish_tree_node_definitions> GetNodeDefinition(string nodeId, string treeType);

        Task<tco_docwf_proccess_log> GetDocWfProcessLogDataByUserId(int tenantId, Guid processPkId, int fkUserId);

        Task<IEnumerable<tco_docwf_process_approver_viewer>> GetCurrentUserAproverViewerUserDataForProcess(int tenantId, List<Guid> processPkId, int userPkId);

        Task<IEnumerable<tco_docwf_process_approver_viewer>> GetAproverViewerDataForCurrentUser(int tenantId, int userPkId, List<Guid> processPkId);

        Task<IEnumerable<tco_docwf_responsibles>> GetAllDocWfResponsiblesDataForCurrentUser(int tenantId,
            string doc_type, int userPkId, int budgetYear, int forecastePeriod = 0, string docChapterId = null);

        Task<IEnumerable<tco_docwf_process>> GetAllDocWfProcessDataByNodeList(int tenantId, string doc_type, List<string> nodeList, int budgetYear, int forecastPeriod = 0);

        Task<IEnumerable<tco_docwf_process>> GetProcessStatus(int tenantId, int budgetYear, string publishTreeType, int forecastPeriod);

        Task<tco_publish_review_child> GetProcessTreeCurrentNodeData(int tenantId, int templateId, string treeType, string nodeUid, string nodeId, Guid processId);

        Task<IEnumerable<tco_docwf_table_comments>> GetNodeComments(int tenantId, Guid processId, string uniqueId, int budgetYear, int forecastPeriod, string treeType);

        Task<IEnumerable<ActiveUsersHelper>> GetAllCommenters(int tenantId, List<int> pkUserIds);

        Task<tco_docwf_table_comments> GetNodeCommentById(int tenantId, int pkId);

        Task DeleteNodeComments(int tenantId, int pkId);

        Task<IEnumerable<tco_docwf_doc_part_log>> GetAllDocWfdocPartLogDataOfChapters(int tenantId, string doc_type, int templateId, int budgetYear, List<string> chaptersList, int forecastePeriod = 0);

        Task<tco_docwf_process> GetDocWfProcessForChapterId(int tenantId, string chapterId);

        Task<List<tco_publish_review_child>> GetProcessTreeCurrentNodeDataAllProcess(int tenantId, int templateId, string treeType, string nodeId);

        Task<IEnumerable<tco_docwf_responsibles>> GetAllResponsiblesDataForCurrentDocNode(int tenantId, string doc_type, string docNode, int budgetYear, int forecastePeriod = 0);

        Task<IEnumerable<UserData>> GetTcoUsersForMatchingPkIds(List<int> userPkiList);

        Task<IEnumerable<int>> GetAllTemplateIdsUsedInProcess(int tenantId, string doc_type, int budgetYear, int forecastPeriod = 0);

        Task<IEnumerable<tco_docwf_process>> GetAllProcessDataForTemplate(int tenantId, string doc_type, int budgetYear, int forecastPeriod, int templateId);

        Task<IEnumerable<vw_tco_parameters>> GetParameterValues(int tenantId);

        Task<string> GetDocChapterText(int tenantId, string treeType, int templateId, Guid processId, string docChapterId);
        Task<IEnumerable<tco_publish_review_child>> GetPublishNodesByProcessId(int tenantId, Guid processId, string treeType);
        Task<IEnumerable<tco_approval_node_description_log>> GetApprovalNodeLogData(int tenantId, int budgetYear, string nodeType);

        Task<IEnumerable<tco_publish_review_child>> GetAllProcessTreeData(int tenantId, int templateId, string treeType, Guid processId);
        Task<bool> IsCoordinatorForProcess(int tenantId, int userPkId, Guid processId);
    }
}