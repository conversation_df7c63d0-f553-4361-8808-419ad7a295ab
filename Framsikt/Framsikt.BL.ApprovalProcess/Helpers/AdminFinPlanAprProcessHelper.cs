using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
#pragma warning disable CS8618

namespace Framsikt.BL.ApprovalProcess.Helpers
{
    public class AdminFinPlanAprProcessHelper
    {
        public int id { get; set; }
        public string nodeNameId { get; set; }
        public string nodeName { get; set; }
        public List<int> budgetCoordinator { get; set; }
        public List<int> prevbudgetCoordinator { get; set; }
        public string level1Id { get; set; }
    }

    public class SaveTemplateHelper
    {
        public int budgetYear { get; set; }
        public int forecastPeriod { get; set; }
        public PublishTreeType pubTreeType { get; set; }
        public int SelectedTemplateId { get; set; }
    }

    public class AdminFinPlanAprProcessTemplateHelper
    {
        public int KeyId { get; set; }
        public string ValueString { get; set; }
        public string DocVerionString { get; set; }
        public string DocVerionId { get; set; }
        public int tenantId { get; set; }
        public bool IsSelected { get; set; }

        public string templateUrl { get; set; }
    }

    public class GetSelectedTemplateData
    {
        public string TemplateName { get; set; }
        public int SelectedTemplateId { get; set; }
        public string DocVerionId { get; set; }
        public string DocVersionName { get; set; }
    }

    public class GetApprovalNodeAccessList
    {
        public string NodeId { get; set; }
        public string NodeIdName { get; set; }
        public bool isDisabled { get; set; }
        public string VerionTitle { get; set; }
        public Guid descGuid { get; set; }
    }

    public class SendDocForReviewHelper
    {
        public int templateId { get; set; }
        public string nodeId { get; set; }
        public string verionTitle { get; set; }
        public List<int> approverUserList { get; set; }
        public List<int> viewerUserList { get; set; }

        public string description { get; set; }

        public string deadlineDate { get; set; }

        public int budgetYear { get; set; }

        public Guid descGuid { get; set; }
        public int configId { get; set; }
        public int forecastPeriod { get; set; }
        public PublishTreeType treeType { get; set; }
    }

    public class ApproverViewListHelper
    {
        public List<int> approverUserList { get; set; }
        public List<int> viewerUserList { get; set; }

        public List<int> prevApproverUserList { get; set; }
        public List<int> prevViewerUserList { get; set; }

        public Guid processId { get; set; }
        public int templateId { get; set; }
        public int budgetYear { get; set; }
        public int forecastPeriod { get; set; }
        public PublishTreeType treeType { get; set; }

    }

    public class ProcessRecallHelper
    {
        public string comment { get; set; }
        public Guid processId { get; set; }
        public UserListType userType { get; set; }
    }

    public class ProcessAppovalViewerGridHelper
    {
        public Guid processId { get; set; }
        public string nodeId { get; set; }
        public string nodeIdName { get; set; }
        public string VerionTitle { get; set; }
        public Guid descGuid { get; set; }
        public string desc { get; set; }
        public DateTime deadlineDate { get; set; }
        public DateTime createdDate { get; set; }
        public int budgetYear { get; set; }

        public string proecessSatusId { get; set; }
        public string proecessStatusName { get; set; }

        public string processDetailsStr { get; set; }
        public string processDetails { get; set; }

        public string logUrl { get; set; }
        public string editUrl { get; set; }
        public string recall { get; set; }
    }

    public class ProcessLogGridHelper
    {
        public int id { get; set; }
        public Guid processId { get; set; }

        public string lastUpdateBy { get; set; }
        public DateTime lastUpdated { get; set; }
        public string lastUpdatedStr { get; set; }

        public string processDetails { get; set; }
        public string processDetailsStr { get; set; }

        public string proecessLogSatusId { get; set; }
        public string proecessLogStatusName { get; set; }

        public string processTitle { get; set; }

        public string userType { get; set; }
        public string userTypeId { get; set; }
    }

    public class ProcessLogClsHelper
    {
        public List<ProcessLogGridHelper> LogData { get; set; }
        public List<KeyValueGuidLogData> VersionTitleList { get; set; }
        public List<KeyValueNewData> StatusList { get; set; }
        public List<KeyValueNewData> UserRoleList { get; set; }
    }

    public class ProcessLogSearchHelper
    {
        public string lastUpdatedDate { get; set; }
        public string proecessLogSatusId { get; set; }
        public string userName { get; set; }
        public Guid processId { get; set; }
        public string userRole { get; set; }
    }

    public class KeyValueGuidLogData
    {
        [JsonProperty("Key")]
        public Guid Key { get; set; }

        [JsonProperty("Value")]
        public string Value { get; set; }

        public List<string> NodeIdList { get; set; }
    }

    public class KeyValueNewDataLogData
    {
        [JsonProperty("Key")]
        public string Key { get; set; }

        [JsonProperty("Value")]
        public string Value { get; set; }

        public List<Guid> ProcessIdList { get; set; }
    }

    public class ProcessLogDropDownHelepr
    {
        public List<KeyValueNewDataLogData> docNodeList { get; set; }
        public List<KeyValueGuidLogData> versionTitleList { get; set; }
    }

    public class AprovalStatusProcessLogSaveHelper
    {
        public Guid processId { get; set; }
        public UserListType userType { get; set; }
        public int statusId { get; set; }
        public int templateId { get; set; }
        public int budgetYear { get; set; }
        public ProccessLogAction logAction { get; set; }
        public string comment { get; set; }
        public docPart_status docPartStatus { get; set; }
        public int docPartStatusId { get; set; }
        public string docChapterId { get; set; }
        public ProccessStatus processStatus { get; set; }
        public PublishTreeType treeType { get; set; }
        public int forecastPeriod { get; set; }
    }

    public class AprovalStatusProcessLogGetHelper
    {
        public bool showStatusDropDown { get; set; }
        public List<KeyValueInt> StatusDropDownList { get; set; }
    }

    public class ApprovalStatusGridHelper
    {
        public string docChapterId { get; set; }
        public string documentSection { get; set; }
        public string processStatus { get; set; }
        public string processStatusId { get; set; }
        public int docPartStatusId { get; set; }
        public string docPartStatus { get; set; }
        public bool disabled { get; set; }
    }

    public class ProcessNodeDropDownHelper
    {
        public UserListType userType { get; set; }
        public Guid key { get; set; }
        public string value { get; set; }
        public string processStatus { get; set; }
    }

    public class NodeDescriptionInput
    {
        [JsonProperty("nodeType")] public string NodeType { get; set; }
        [JsonProperty("editableNodeId")] public string EditableNodeId { get; set; }
        [JsonProperty("actualNodeType")] public string ActualNodeType { get; set; }
        [JsonProperty("budgetYear")] public int BudgetYear { get; set; }
        [JsonProperty("forecastPeriod")] public int ForecastPeriod { get; set; }
        [JsonProperty("nodeId")] public string NodeId { get; set; }
        [JsonProperty("parentNodeId")] public string ParentNodeId { get; set; }
        [JsonProperty("uniqueId")] public string UniqueId { get; set; }
        [JsonProperty("treeType")] public PublishTreeType TreeType { get; set; }
        [JsonProperty("nodeContent")] public NodeDescription NodeContent { get; set; }
        [JsonProperty("processId")] public string ProcessId { get; set; }
        [JsonProperty("templateId")] public int TemplateId { get; set; }
        [JsonProperty("cloudDocumentVersion")] public long CloudDocumentVersion { get; set; }
        [JsonProperty("logHistory")] public bool LogHistory { get; set; }
    }

    public class NodeDescription
    {
        public Guid descriptionHistoryId { get; set; }
        public Guid uniqueId { get; set; }
        public string abstractText { get; set; }
        public string description { get; set; }
        public bool showWarningText { get; set; }
        public bool showEditor { get; set; }
        public string nodeContentTypeDesc { get; set; }
    }

    public class DescriptionInput
    {
        public string fieldType { get; set; }
        public string orgId { get; set; }
        public string serviceId { get; set; }
        public int orgLevel { get; set; }
        public string previousOrgId { get; set; }
        public string currentOrgId { get; set; }
        public DescriptionInput()
        {
            serviceId = "ALL";
        }
    }

    public class AccessObject
    {
        public bool showTreeSection { get; set; }
        public string warningText { get; set; }

        public AccessObject()
        {
            warningText = string.Empty;
            showTreeSection = true;
        }
    }

    public class CommentSection
    {
        public IEnumerable<ActiveUsersHelper> commenters { get; set; }
        public IEnumerable<tco_docwf_table_comments> commentsDetails { get; set; }
    }

    public class CommentDetails
    {
        [JsonProperty("pkId")]
        public int commentId { get; set; }

        public string shortName { get; set; }
        public string userName { get; set; }
        public string updated { get; set; }
        public bool isEditable { get; set; }
        public string description { get; set; }
    }

    public class SaveComment
    {
        [JsonProperty("pkId")]
        public int commentId { get; set; }

        public Guid processId { get; set; }
        public PublishTreeType treeType { get; set; }
        public string uniqueId { get; set; }
        public int budgetYear { get; set; }
        public int forecastPeriod { get; set; }
        public string description { get; set; }

        public string NodeId { get; set; }     
       
       
        public int TemplateId { get; set; }
    }

    public class RemoveEditIconHelper
    {
        public string uId { get; set; }

        public string nodeId { get; set; }
        public Guid processId { get; set; }
        public PublishTreeType treeType { get; set; }
        public int budgetYear { get; set; }
        public int forecastPeriod { get; set; }
        public int templateId { get; set; }
    }

    public class ApprovalProcessInputHelper
    {
        public int budgetYear { get; set; }
        public int forecastPeriod { get; set; }
        public PublishTreeType pubTreeType { get; set; }
        public int templateId { get; set; }
        public Guid processId { get; set; }
    }

    public class ApproverLogGridInputHelper
    {
        public int budgetYear { get; set; }
        public int forecastPeriod { get; set; }
        public PublishTreeType pubTreeType { get; set; }
        public string nodeId { get; set; }
        public ProcessLogSearchHelper searchInput { get; set; }
    }

    public class DocLogGridDataHelper
    {
        public Guid lastUpdatedProcessId { get; set; }
        public string docNodeId { get; set; }
        public string docNodeIdName { get; set; }
        public string budgetCordinatorNameList { get; set; }
        public string lastUpdatedProcessStatus { get; set; }
        public string lastUpdatedProcessStatusKey { get; set; }
        public string doc_status { get; set; }
        public string doc_statusKey { get; set; }
        public DateTime? finishedDate { get; set; }
        public string finishedDateStr { get; set; }
        public string procSeeDetails { get; set; }
        public string procSeeDetailsStr { get; set; }
    }

    public class DocLogGridHelper
    {
        public List<DocLogGridDataHelper> data { get; set; }
        public List<KeyValueNewData> processStatusList { get; set; }
        public List<KeyValueNewData> docStatusList { get; set; }
    }

    public class DocLogGridInputHelper
    {
        public int budgetYear { get; set; }
        public int forecastPeriod { get; set; }
        public PublishTreeType pubTreeType { get; set; }
        public DocLogGridSearchHelper searchInput { get; set; }
    }

    public class DocLogGridSearchHelper
    {
        public string docNodeIdName { get; set; }
        public string budgetCordinatorName { get; set; }
        public string lastUpdatedProcessStatusKey { get; set; }
        public string doc_statusKey { get; set; }
        public string finishedDateStr { get; set; }
    }

}