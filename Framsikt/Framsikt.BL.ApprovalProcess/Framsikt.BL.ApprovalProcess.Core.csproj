<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Configurations>Debug;Release;SAST</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Properties\**" />
    <EmbeddedResource Remove="Properties\**" />
    <None Remove="Properties\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="packages.config" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Framsikt.BL.Reporting\Framsikt.BL.Reporting.Core.csproj" />
    <ProjectReference Include="..\Framsikt.BL\Framsikt.BL.Core.csproj" />
  </ItemGroup>

</Project>
