#pragma warning disable CS8600
#pragma warning disable CS8601
#pragma warning disable CS8602
#pragma warning disable CS8604


using Framsikt.BL.Core;
using Framsikt.BL.Helpers;
using Framsikt.BL.Reporting.Repository;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Globalization;

using KeyValuePair = Framsikt.BL.Helpers.KeyValuePair;

namespace Framsikt.BL.Reporting
{
    public class StatusEmpManYears : IStatusEmpManYears
    {
        private readonly IUtility _utility;
        private readonly IReportingUoW _unitOfWork;
        public const string women = "kvinne";
        public const string men = "mann";

        public StatusEmpManYears(IServiceProvider container)
        {
            _utility = container.GetRequiredService<IUtility>();
            _unitOfWork = container.GetRequiredService<IReportingUoW>();
        }

        public async Task<StatusEmpManYearsHelper> GetStatusEmpManYearsGridData(string userId, StatusEmpDataInput input)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, input.ForecastPeriod);
            StatusEmpManYearsHelper result = new StatusEmpManYearsHelper();
            List<StatusEmpManYearsPrimaryData> data = new List<StatusEmpManYearsPrimaryData>();

            switch (input.GroupingType) {
                case ((int)StatusEmpGroupingOption.PerUnit):
                    data = input.IsYtdGrid ? await _unitOfWork.StatusEmpManYearsRepo.GetStatusEmpDataPerOrgUnitYtd(userDetails.tenant_id, orgVersionContent.orgVersion, input)
                        :await _unitOfWork.StatusEmpManYearsRepo.GetStatusEmpDataPerOrgUnit(userDetails.tenant_id, orgVersionContent.orgVersion, input);
                    break;

                case ((int)StatusEmpGroupingOption.PositionCategory):
                    data = input.IsYtdGrid ? await _unitOfWork.StatusEmpManYearsRepo.GetStatusEmpDataPerPosCategoryYtd(userDetails.tenant_id, orgVersionContent.orgVersion, input)
                        :await _unitOfWork.StatusEmpManYearsRepo.GetStatusEmpDataPerPosCategory(userDetails.tenant_id, orgVersionContent.orgVersion, input);
                    break;

                case ((int)StatusEmpGroupingOption.KSPositionGroup):
                    data = input.IsYtdGrid ? await _unitOfWork.StatusEmpManYearsRepo.GetStatusEmpDataPerKSPositionYtd(userDetails.tenant_id, orgVersionContent.orgVersion, input)
                        :await _unitOfWork.StatusEmpManYearsRepo.GetStatusEmpDataPerKSPosition(userDetails.tenant_id, orgVersionContent.orgVersion, input);
                    break;

                case ((int)StatusEmpGroupingOption.AgeGroups):
                    data = await GetStatusEmpManYearsDataPerAge(userDetails, orgVersionContent.orgVersion, input);
                    break;
            }

            result.Data = data;
            result.Date = data.Select(x => x.Date).FirstOrDefault();

            return result;
        }

        public async Task<List<string>> GetStatusEmpPositionTypes(string userId, int budgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            return await _unitOfWork.StatusEmpManYearsRepo.GetPositionTypes(userDetails.tenant_id, budgetYear);
        }

        public dynamic FormatData(List<StatusEmpManYearsPrimaryData> data, List<StatusEmpManYearsPrimaryData> prevYearData , int groupingType, List<string> posTypeNames)
        {
            List<string> groupingValues = new List<string>();
            List<string> onlyPrevGroupingValues = new List<string>();

            switch (groupingType)
            {
                case ((int)StatusEmpGroupingOption.PerUnit):
                    groupingValues = data.Select(x => x.OrgName).Distinct().ToList();
                    onlyPrevGroupingValues = prevYearData.Where(x => !groupingValues.Contains(x.OrgName) && !string.IsNullOrEmpty(x.OrgName)).Select(x => x.OrgName).Distinct().ToList();
                    break;

                case ((int)StatusEmpGroupingOption.PositionCategory):
                    groupingValues = data.Select(x => x.PublicPosCodeName).Distinct().ToList();
                    onlyPrevGroupingValues = prevYearData.Where(x => !groupingValues.Contains(x.OrgName) && !string.IsNullOrEmpty(x.OrgName)).Select(x => x.OrgName).Distinct().ToList();
                    break;

                case ((int)StatusEmpGroupingOption.KSPositionGroup):
                    groupingValues = data.Select(x => x.KsPositionGroup).Distinct().ToList();
                    onlyPrevGroupingValues = prevYearData.Where(x => !groupingValues.Contains(x.OrgName) && !string.IsNullOrEmpty(x.OrgName)).Select(x => x.OrgName).Distinct().ToList();
                    break;

                case ((int)StatusEmpGroupingOption.AgeGroups):
                    groupingValues = data.Select(x => x.AgeGroup).Distinct().OrderBy(x => x).ToList();
                    onlyPrevGroupingValues = prevYearData.Where(x => !groupingValues.Contains(x.OrgName) && !string.IsNullOrEmpty(x.OrgName)).Select(x => x.OrgName).Distinct().ToList();
                    break;
            }

            if (onlyPrevGroupingValues.Any())
            {
                groupingValues.AddRange(onlyPrevGroupingValues);
            }

            //for sum row
            decimal[] prevBudTotalRowValues = new decimal[posTypeNames.Count + 1];
            decimal[] prevActTotalRowValues = new decimal[posTypeNames.Count + 1];
            decimal[] prevBudActDevTotalRowValues = new decimal[posTypeNames.Count + 1];
            decimal[] prevRevActDevTotalRowValues = new decimal[posTypeNames.Count + 1];
            decimal[] prevRevTotalRowValues = new decimal[posTypeNames.Count + 1];
            decimal[] budTotalRowValues = new decimal[posTypeNames.Count+1];
            decimal[] actTotalRowValues = new decimal[posTypeNames.Count+1];
            decimal[] revTotalRowValues = new decimal[posTypeNames.Count+1];
            decimal[] budActDevTotalRowValues = new decimal[posTypeNames.Count+1];
            decimal[] revActDevTotalRowValues = new decimal[posTypeNames.Count+1];

            decimal[] prevBudEmployeeCountValues = new decimal[posTypeNames.Count + 1];
            decimal[] prevActEmployeeCountValues = new decimal[posTypeNames.Count + 1];
            decimal[] prevBudActEmployeeCountDevValues = new decimal[posTypeNames.Count + 1];
            decimal[] prevRevActEmployeeCountDevValues = new decimal[posTypeNames.Count + 1];
            decimal[] prevRevEmployeeCountValues = new decimal[posTypeNames.Count + 1];
            decimal[] budEmployeeCountValues = new decimal[posTypeNames.Count + 1];
            decimal[] actEmployeeCountValues = new decimal[posTypeNames.Count + 1];
            decimal[] revEmployeeCountValues = new decimal[posTypeNames.Count + 1];
            decimal[] employeeCountBudActDevValues = new decimal[posTypeNames.Count + 1];
            decimal[] employeeCountRevActDevValues = new decimal[posTypeNames.Count + 1];

            JArray result = new JArray();

            dynamic totalRow = new JObject();
            totalRow.groupingColTemplate = "Total";
            int i = 0;

            List<ManYearsTotalHelper> prevBudEmployeeCountTotalList = new();
            List<ManYearsTotalHelper> prevActEmployeeCountTotalList = new();
            List<ManYearsTotalHelper> prevBudActEmployeeCountDevTotalList = new();
            List<ManYearsTotalHelper> prevRevActEmployeeCountDevTotalList = new();
            List<ManYearsTotalHelper> prevRevEmployeeCountTotalList = new();
            List<ManYearsTotalHelper> budEmployeeCountTotalList = new();
            List<ManYearsTotalHelper> actEmployeeCountTotalList = new();
            List<ManYearsTotalHelper> revEmployeeCountTotalList = new();
            List<ManYearsTotalHelper> employeeCountBudActDevTotalList = new();
            List<ManYearsTotalHelper> employeeCountRevActDevTotalList = new();


            foreach (var value in groupingValues)
            {
                dynamic res = new JObject();
                res.groupingColTemplate = value;

                List<StatusEmpManYearsPrimaryData> rowDataList = new List<StatusEmpManYearsPrimaryData>();
                List<StatusEmpManYearsPrimaryData> prevYearRowDataList = new List<StatusEmpManYearsPrimaryData>();

                switch (groupingType)
                {
                    case ((int)StatusEmpGroupingOption.PerUnit):
                        rowDataList = data.Where(x => x.OrgName == value).ToList();
                        prevYearRowDataList = prevYearData.Where(x => x.OrgName == value).ToList();
                        break;

                    case ((int)StatusEmpGroupingOption.PositionCategory):
                        rowDataList = data.Where(x => x.PublicPosCodeName == value).ToList();
                        prevYearRowDataList = prevYearData.Where(x => x.PublicPosCodeName == value).ToList();
                        break;

                    case ((int)StatusEmpGroupingOption.KSPositionGroup):
                        rowDataList = data.Where(x => x.KsPositionGroup == value).ToList();
                        prevYearRowDataList = prevYearData.Where(x => x.KsPositionGroup == value).ToList();
                        break;

                    case ((int)StatusEmpGroupingOption.AgeGroups):
                        rowDataList = data.Where(x => x.AgeGroup == value).ToList();
                        prevYearRowDataList = prevYearData.Where(x => x.AgeGroup == value).ToList();
                        break;
                }

                i = 0;

                //for total column for all postion types
                decimal prevBudTotal = 0;
                decimal prevActTotal = 0;
                decimal prevBudActDevTotal = 0;
                decimal prevRevActDevTotal = 0;
                decimal prevRevTotal = 0;
                decimal budTotal = 0;
                decimal actTotal = 0;
                decimal revTotal = 0;
                decimal budActDevTotal = 0;
                decimal revActDevTotal = 0;

                decimal prevBudEmployeeCountTotal = 0;
                decimal prevActEmployeeCountTotal = 0;
                decimal prevBudActEmployeeCountDevTotal = 0;
                decimal prevRevActEmployeeCountDevTotal = 0;
                decimal prevRevEmployeeCountTotal = 0;
                decimal budEmployeeCountTotal = 0;
                decimal actEmployeeCountTotal = 0;
                decimal revEmployeeCountTotal = 0;
                decimal employeeCountBudActDevTotal = 0;
                decimal employeeCountRevActDevTotal = 0;
                //for total column where emp count is considered
                var totalPrevBudDistinctEmployeeList = new List<string>();
                var totalPrevRevDistinctEmployeeList = new List<string>();
                var totalPrevActDistinctEmployeeList = new List<string>();
                var totalBudDistinctEmployeeList = new List<string>();
                var totalRevDistinctEmployeeList = new List<string>();
                var totalActDistinctEmployeeList = new List<string>();


                foreach (var posType in posTypeNames)
                {
                    var totalDistinctEmployees = new List<string>();
                    //previous year row data
                    {
                        var prevYearRowData = prevYearRowDataList.Where(x => x.PosTypeName == posType).ToList();

                        //Budgeted data previous year
                        var prevBudManData = prevYearRowData.FirstOrDefault(x => x.IsOriginalBudget && x.BudgetType == 1);

                        int prevBudEmpCount = prevBudManData != null ? prevBudManData.EmployeeCount : 0;//Emp count budgeted
                        res.Add($"prevBudEmployeeCount_{i}", prevBudEmpCount == 0 ? "-" : String.Format("{0:n2}", prevBudEmpCount));
                        //for total column and sum row
                        prevBudEmployeeCountValues[i] += prevBudEmpCount;
                        if (prevBudManData != null && prevBudManData.EmployeeList.Any())
                        {
                            totalPrevBudDistinctEmployeeList.AddRange(prevBudManData.EmployeeList.Distinct());
                            prevBudEmployeeCountTotalList.Add(new ManYearsTotalHelper { EmployeeList = prevBudManData.EmployeeList, posType = posType });
                        }
                        decimal prevBudManYears = prevBudManData != null ? prevBudManData.ManYears : 0;//Man years budgeted
                        res.Add($"prevBudPositionType_{i}", prevBudManYears == 0 ? "-" : String.Format("{0:n2}", prevBudManYears));
                        //for total column and sum row
                        prevBudTotal += prevBudManYears;
                        prevBudTotalRowValues[i] += prevBudManYears;




                        //Revised budget data previous year
                        var prevRevManData = prevYearRowData.FirstOrDefault(x => !x.IsOriginalBudget && x.BudgetType == 2);

                        int prevRevEmpCount = prevRevManData != null ? prevRevManData.EmployeeCount : 0;//Emp count revised
                        res.Add($"prevRevEmployeeCount_{i}", prevRevEmpCount == 0 ? "-" : string.Format("{0:n2}", prevRevEmpCount));
                        //for total column and sum row
                        prevRevEmployeeCountValues[i] += prevRevEmpCount;
                        if (prevRevManData != null && prevRevManData.EmployeeList.Any())
                        {
                            totalPrevRevDistinctEmployeeList.AddRange(prevRevManData.EmployeeList.Distinct());
                            prevRevEmployeeCountTotalList.Add(new ManYearsTotalHelper { EmployeeList = prevRevManData.EmployeeList, posType = posType });
                        }
                        decimal prevRevManYears = prevRevManData != null ? prevRevManData.ManYears : 0;//Man years revised
                        res.Add($"prevRevPositionType_{i}", prevRevManYears == 0 ? "-" : String.Format("{0:n2}", prevRevManYears));
                        //for total column and sum row
                        prevRevTotal += prevRevManYears;
                        prevRevTotalRowValues[i] += prevRevManYears;




                        //Actual data previous year
                        var prevActManData = prevYearRowData.FirstOrDefault(x => !x.IsOriginalBudget && x.BudgetType == 0);

                        int prevActEmpCount = prevActManData != null ? prevActManData.EmployeeCount : 0;//Emp count actual
                        res.Add($"prevActEmployeeCount_{i}", prevActEmpCount == 0 ? "-" : String.Format("{0:n2}", prevActEmpCount));
                        //for total column and sum row
                        prevActEmployeeCountValues[i] += prevActEmpCount;
                        if (prevActManData != null && prevActManData.EmployeeList.Any())
                        {
                            totalPrevActDistinctEmployeeList.AddRange(prevActManData.EmployeeList.Distinct());
                            prevActEmployeeCountTotalList.Add(new ManYearsTotalHelper { EmployeeList = prevActManData.EmployeeList, posType = posType });
                        }
                        decimal prevActManYears = prevActManData != null ? prevActManData.ManYears : 0;//Man Years actual
                        res.Add($"prevActPositionType_{i}", prevActManYears == 0 ? "-" : String.Format("{0:n2}", prevActManYears));
                        //for total column and sum row
                        prevActTotal += prevActManYears;
                        prevActTotalRowValues[i] += prevActManYears;




                        //Deviation data previous year actual and budgeted
                        var prevBudActDevEmpCount = prevBudEmpCount - prevActEmpCount;
                        res.Add($"prevBudActEmployeeCountDev_{i}", prevBudActDevEmpCount == 0 ? "-" : String.Format("{0:n2}", prevBudActDevEmpCount));
                        prevBudActEmployeeCountDevValues[i] += prevBudActDevEmpCount;

                        var prevBudActDevManYears = prevBudManYears - prevActManYears;
                        res.Add($"prevBudActDevPositionType_{i}", prevBudActDevManYears == 0 ? "-" : String.Format("{0:n2}", prevBudActDevManYears));
                        prevBudActDevTotal += prevBudActDevManYears;
                        prevBudActDevTotalRowValues[i] += prevBudActDevManYears;


                        //Deviation data previous year revised and actual
                        var prevRevActDevEmpCount = prevRevEmpCount - prevActEmpCount;
                        res.Add($"prevRevActEmployeeCountDev_{i}", prevRevActDevEmpCount == 0 ? "-" : String.Format("{0:n2}", prevRevActDevEmpCount));
                        prevRevActEmployeeCountDevValues[i] += prevRevActDevEmpCount;

                        var prevRevActDevManYears = prevRevManYears - prevActManYears;
                        res.Add($"prevRevActDevPositionType_{i}", prevRevActDevManYears == 0 ? "-" : String.Format("{0:n2}", prevRevActDevManYears));
                        prevRevActDevTotal += prevRevActDevManYears;
                        prevRevActDevTotalRowValues[i] += prevRevActDevManYears;
                    }




                    //current year row data
                    {
                        var rowData = rowDataList.Where(x => x.PosTypeName == posType).ToList();

                        //Budget data current year
                        var budManData = rowData.FirstOrDefault(x => x.IsOriginalBudget && x.BudgetType == 1);

                        int budEmpCount = budManData != null ? budManData.EmployeeCount : 0;//Emp count budgeted
                        res.Add($"budEmployeeCount_{i}", budEmpCount == 0 ? "-" : String.Format("{0:n2}", budEmpCount));
                        budEmployeeCountValues[i] += budEmpCount;
                        if (budManData != null && budManData.EmployeeList.Any())
                        {
                            totalBudDistinctEmployeeList.AddRange(budManData.EmployeeList.Distinct());
                            budEmployeeCountTotalList.Add(new ManYearsTotalHelper { EmployeeList = budManData.EmployeeList, posType = posType });
                        }
                        decimal budManYears = budManData != null ? budManData.ManYears : 0;//Man years budgeted
                        res.Add($"budPositionType_{i}", budManYears == 0 ? "-" : String.Format("{0:n2}", budManYears));
                        budTotal += budManYears;
                        budTotalRowValues[i] += budManYears;


                        //Revised budget data current year
                        var revManData = rowData.FirstOrDefault(x => !x.IsOriginalBudget && x.BudgetType == 2);

                        int revEmpCount = revManData != null ? revManData.EmployeeCount : 0;//Emp count revised
                        res.Add($"revEmployeeCount_{i}", revEmpCount == 0 ? "-" : String.Format("{0:n2}", revEmpCount));
                        revEmployeeCountValues[i] += revEmpCount;
                        if (revManData != null && revManData.EmployeeList.Any())
                        {
                            totalRevDistinctEmployeeList.AddRange(revManData.EmployeeList.Distinct());
                            revEmployeeCountTotalList.Add(new ManYearsTotalHelper { EmployeeList = revManData.EmployeeList, posType = posType });
                        }
                        decimal revManYears = revManData != null ? revManData.ManYears : 0; //Man Years Revised
                        res.Add($"revPositionType_{i}", revManYears == 0 ? "-" : String.Format("{0:n2}", revManYears));
                        revTotal += revManYears;
                        revTotalRowValues[i] += revManYears;



                        //Actual data current year
                        var actManData = rowData.FirstOrDefault(x => !x.IsOriginalBudget && x.BudgetType == 0);
                        if(actManData != null && actManData.EmployeeList.Any())
                        {
                            totalDistinctEmployees.AddRange(actManData.EmployeeList.Distinct());
                            totalActDistinctEmployeeList.AddRange(actManData.EmployeeList.Distinct());
                            actEmployeeCountTotalList.Add(new ManYearsTotalHelper { EmployeeList = actManData.EmployeeList, posType = posType });
                        }
                        var distinctEmployees = totalDistinctEmployees.Distinct().Count();
                        int actEmpCount = actManData != null ? distinctEmployees : 0;//Emp count actual
                        res.Add($"actEmployeeCount_{i}", actEmpCount == 0 ? "-" : String.Format("{0:n2}", actManData != null ? actManData.EmployeeCount : 0));
                        actEmployeeCountValues[i] += actManData != null ? actManData.EmployeeCount : 0;
                        decimal actManYears = actManData != null ? actManData.ManYears : 0;//Man years actual
                        res.Add($"actPositionType_{i}", actManYears == 0 ? "-" : String.Format("{0:n2}", actManYears));
                        actTotal += actManYears;
                        actTotalRowValues[i] += actManYears;



                        //Deviation data current year actual  and budgeted
                        var budActDevEmpCount = budEmpCount - actEmpCount;//Emp count deviation
                        res.Add($"budActEmployeeCountDev_{i}", budActDevEmpCount == 0 ? "-" : String.Format("{0:n2}", budActDevEmpCount));
                        employeeCountBudActDevValues[i] += budActDevEmpCount;

                        var budActDevManYears = budManYears - actManYears;//Man years deviation
                        res.Add($"budActDevPositionType_{i}", budActDevManYears == 0 ? "-" : String.Format("{0:n2}", budActDevManYears));
                        budActDevTotal += budActDevManYears;
                        budActDevTotalRowValues[i] += budActDevManYears;


                        //Deviation data current year revised  and budgeted
                        var devRevActEmpCount = revEmpCount - actEmpCount;//Emp count deviation
                        res.Add($"revActEmployeeCountDev_{i}", devRevActEmpCount == 0 ? "-" : String.Format("{0:n2}", devRevActEmpCount));
                        employeeCountRevActDevValues[i] += devRevActEmpCount;

                        var devRevActManYears = revManYears - actManYears;//Man years deviation
                        res.Add($"revActDevPositionType_{i}", devRevActManYears == 0 ? "-" : String.Format("{0:n2}", devRevActManYears));
                        revActDevTotal += devRevActManYears;
                        revActDevTotalRowValues[i] += devRevActManYears;
                    }

                    i++;
                }
                //total column where employee count is considered.
                prevBudEmployeeCountTotal = totalPrevBudDistinctEmployeeList.Distinct().Count();
                prevRevEmployeeCountTotal = totalPrevRevDistinctEmployeeList.Distinct().Count();
                prevActEmployeeCountTotal = totalPrevActDistinctEmployeeList.Distinct().Count();
                budEmployeeCountTotal = totalBudDistinctEmployeeList.Distinct().Count();
                revEmployeeCountTotal = totalRevDistinctEmployeeList.Distinct().Count();
                actEmployeeCountTotal = totalActDistinctEmployeeList.Distinct().Count();
                prevBudActEmployeeCountDevTotal = prevBudEmployeeCountTotal - prevActEmployeeCountTotal;
                prevRevActEmployeeCountDevTotal = prevRevEmployeeCountTotal - prevActEmployeeCountTotal;
                employeeCountBudActDevTotal = budEmployeeCountTotal - actEmployeeCountTotal;
                employeeCountRevActDevTotal = revEmployeeCountTotal - actEmployeeCountTotal;

                //total columns
                res.prevBudEmployeeCount_Total = prevBudEmployeeCountTotal == 0 ? "-" : String.Format("{0:n2}", prevBudEmployeeCountTotal);
                res.prevActEmployeeCount_Total = prevActEmployeeCountTotal == 0 ? "-" : String.Format("{0:n2}", prevActEmployeeCountTotal);
                res.prevRevEmployeeCount_Total = prevRevEmployeeCountTotal == 0 ? "-" : String.Format("{0:n2}", prevRevEmployeeCountTotal);
                res.prevBudActEmployeeCountDev_Total = prevBudActEmployeeCountDevTotal == 0 ? "-" : String.Format("{0:n2}", prevBudActEmployeeCountDevTotal);
                res.prevRevActEmployeeCountDev_Total = prevRevActEmployeeCountDevTotal == 0 ? "-" : String.Format("{0:n2}", prevRevActEmployeeCountDevTotal);
                res.budEmployeeCount_Total = budEmployeeCountTotal == 0 ? "-" : String.Format("{0:n2}", budEmployeeCountTotal);
                res.actEmployeeCount_Total = actEmployeeCountTotal == 0 ? "-" : String.Format("{0:n2}", actEmployeeCountTotal);
                res.revEmployeeCount_Total = revEmployeeCountTotal == 0 ? "-" : String.Format("{0:n2}", revEmployeeCountTotal);
                res.budActEmployeeCountDev_Total = employeeCountBudActDevTotal == 0 ? "-" : String.Format("{0:n2}", employeeCountBudActDevTotal);
                res.revActEmployeeCountDev_Total = employeeCountRevActDevTotal == 0 ? "-" : String.Format("{0:n2}", employeeCountRevActDevTotal);
                res.prevBudPositionType_Total = prevBudTotal == 0 ? "-" : String.Format("{0:n2}", prevBudTotal);
                res.prevActPositionType_Total = prevActTotal == 0 ? "-" : String.Format("{0:n2}", prevActTotal);
                res.prevRevPositionType_Total = prevRevTotal == 0 ? "-" : String.Format("{0:n2}", prevRevTotal);
                res.prevBudActDevPositionType_Total = prevBudActDevTotal == 0 ? "-" : String.Format("{0:n2}", prevBudActDevTotal);
                res.prevRevActDevPositionType_Total = prevRevActDevTotal == 0 ? "-" : String.Format("{0:n2}", prevRevActDevTotal);
                res.budPositionType_Total = budTotal == 0 ? "-" : String.Format("{0:n2}", budTotal);
                res.actPositionType_Total = actTotal == 0 ? "-" : String.Format("{0:n2}", actTotal);
                res.revPositionType_Total = revTotal == 0 ? "-" : String.Format("{0:n2}", revTotal);
                res.budActDevPositionType_Total = budActDevTotal == 0 ? "-" : String.Format("{0:n2}", budActDevTotal);
                res.revActDevPositionType_Total = revActDevTotal == 0 ? "-" : String.Format("{0:n2}", revActDevTotal);

                

                result.Add(res);
            }

            i = 0;

            //sum row
            while (i < posTypeNames.Count)
            {
                totalRow.Add($"prevBudEmployeeCount_{i}", !prevBudEmployeeCountTotalList.Any(x => x.posType == posTypeNames[i]) ? "-" : String.Format("{0:n2}", prevBudEmployeeCountTotalList.Where(x => x.posType == posTypeNames[i]).SelectMany(x => x.EmployeeList).Distinct().Count()));
                totalRow.Add($"prevActEmployeeCount_{i}", !prevActEmployeeCountTotalList.Any(x => x.posType == posTypeNames[i]) ? "-" : String.Format("{0:n2}", prevActEmployeeCountTotalList.Where(x => x.posType == posTypeNames[i]).SelectMany(x => x.EmployeeList).Distinct().Count()));
                totalRow.Add($"prevRevEmployeeCount_{i}", !prevRevEmployeeCountTotalList.Any(x => x.posType == posTypeNames[i]) ? "-" : String.Format("{0:n2}", prevRevEmployeeCountTotalList.Where(x => x.posType == posTypeNames[i]).SelectMany(x => x.EmployeeList).Distinct().Count()));
                var prevBudActEmployeeDeviation = prevBudEmployeeCountTotalList.Where(x => x.posType == posTypeNames[i]).SelectMany(x => x.EmployeeList).Distinct().Count() - prevActEmployeeCountTotalList.Where(x => x.posType == posTypeNames[i]).SelectMany(x => x.EmployeeList).Distinct().Count();
                totalRow.Add($"prevBudActEmployeeCountDev_{i}", prevBudActEmployeeDeviation == 0 ? "-" : String.Format("{0:n2}", prevBudActEmployeeDeviation));
                var prevRevActEmployeeDeviation = prevRevEmployeeCountTotalList.Where(x => x.posType == posTypeNames[i]).SelectMany(x => x.EmployeeList).Distinct().Count() - prevActEmployeeCountTotalList.Where(x => x.posType == posTypeNames[i]).SelectMany(x => x.EmployeeList).Distinct().Count();
                totalRow.Add($"prevRevActEmployeeCountDev_{i}", prevRevActEmployeeDeviation == 0 ? "-" : String.Format("{0:n2}", prevRevActEmployeeDeviation));
                totalRow.Add($"budEmployeeCount_{i}", !budEmployeeCountTotalList.Any(x => x.posType == posTypeNames[i]) ? "-" : String.Format("{0:n2}", budEmployeeCountTotalList.Where(x => x.posType == posTypeNames[i]).SelectMany(x => x.EmployeeList).Distinct().Count()));
                totalRow.Add($"actEmployeeCount_{i}", !actEmployeeCountTotalList.Any(x => x.posType == posTypeNames[i]) ? "-" : String.Format("{0:n2}", actEmployeeCountTotalList.Where(x => x.posType == posTypeNames[i]).SelectMany(x => x.EmployeeList).Distinct().Count()));
                totalRow.Add($"revEmployeeCount_{i}", !revEmployeeCountTotalList.Any(x => x.posType == posTypeNames[i]) ? "-" : String.Format("{0:n2}", revEmployeeCountTotalList.Where(x => x.posType == posTypeNames[i]).SelectMany(x => x.EmployeeList).Distinct().Count()));
                var budActEmployeeDeviation = budEmployeeCountTotalList.Where(x => x.posType == posTypeNames[i]).SelectMany(x => x.EmployeeList).Distinct().Count() - actEmployeeCountTotalList.Where(x => x.posType == posTypeNames[i]).SelectMany(x => x.EmployeeList).Distinct().Count();
                totalRow.Add($"budActEmployeeCountDev_{i}", budActEmployeeDeviation == 0 ? "-" : String.Format("{0:n2}", budActEmployeeDeviation));
                var revActEmployeeDeviation = revEmployeeCountTotalList.Where(x => x.posType == posTypeNames[i]).SelectMany(x => x.EmployeeList).Distinct().Count() - actEmployeeCountTotalList.Where(x => x.posType == posTypeNames[i]).SelectMany(x => x.EmployeeList).Distinct().Count();
                totalRow.Add($"revActEmployeeCountDev_{i}", revActEmployeeDeviation == 0 ? "-" : String.Format("{0:n2}", revActEmployeeDeviation));
                totalRow.Add($"prevBudPositionType_{i}", prevBudTotalRowValues[i] == 0 ? "-" : String.Format("{0:n2}", prevBudTotalRowValues[i]));
                totalRow.Add($"prevActPositionType_{i}", prevActTotalRowValues[i] == 0 ? "-" : String.Format("{0:n2}", prevActTotalRowValues[i]));
                totalRow.Add($"prevRevPositionType_{i}", prevRevTotalRowValues[i] == 0 ? "-" : String.Format("{0:n2}", prevRevTotalRowValues[i]));
                totalRow.Add($"prevBudActDevPositionType_{i}", prevBudActDevTotalRowValues[i] == 0 ? "-" : String.Format("{0:n2}", prevBudActDevTotalRowValues[i]));
                totalRow.Add($"prevRevActDevPositionType_{i}", prevRevActDevTotalRowValues[i] == 0 ? "-" : String.Format("{0:n2}", prevRevActDevTotalRowValues[i]));
                totalRow.Add($"budPositionType_{i}", budTotalRowValues[i] == 0 ? "-" : String.Format("{0:n2}", budTotalRowValues[i]));
                totalRow.Add($"actPositionType_{i}", actTotalRowValues[i] == 0 ? "-" : String.Format("{0:n2}", actTotalRowValues[i]));
                totalRow.Add($"revPositionType_{i}", revTotalRowValues[i] == 0 ? "-" : String.Format("{0:n2}", revTotalRowValues[i]));
                totalRow.Add($"budActDevPositionType_{i}", budActDevTotalRowValues[i] == 0 ? "-" : String.Format("{0:n2}", budActDevTotalRowValues[i]));
                totalRow.Add($"revActDevPositionType_{i}", revActDevTotalRowValues[i] == 0 ? "-" : String.Format("{0:n2}", revActDevTotalRowValues[i]));
                i++;
            }

            totalRow.prevBudEmployeeCount_Total = !prevBudEmployeeCountTotalList.Any() ? "-" : String.Format("{0:n2}", prevBudEmployeeCountTotalList.SelectMany(x => x.EmployeeList).Distinct().Count());
            totalRow.prevActEmployeeCount_Total = !prevActEmployeeCountTotalList.Any() ? "-" : String.Format("{0:n2}", prevActEmployeeCountTotalList.SelectMany(x => x.EmployeeList).Distinct().Count());
            totalRow.prevRevEmployeeCount_Total = !prevRevEmployeeCountTotalList.Any() ? "-" : String.Format("{0:n2}", prevRevEmployeeCountTotalList.SelectMany(x => x.EmployeeList).Distinct().Count());
            var prevBudActEmployeeDeviationTotal = prevBudEmployeeCountTotalList.SelectMany(x => x.EmployeeList).Distinct().Count() - prevActEmployeeCountTotalList.SelectMany(x => x.EmployeeList).Distinct().Count();
            totalRow.prevBudActEmployeeCountDev_Total = prevBudActEmployeeDeviationTotal == 0 ? "-" : String.Format("{0:n2}", prevBudActEmployeeDeviationTotal);
            var prevRevActEmployeeDeviationTotal = prevRevEmployeeCountTotalList.SelectMany(x => x.EmployeeList).Distinct().Count() - prevActEmployeeCountTotalList.SelectMany(x => x.EmployeeList).Distinct().Count();
            totalRow.prevRevActEmployeeCountDev_Total = prevRevActEmployeeCountDevValues[i] == 0 ? "-" : String.Format("{0:n2}", prevRevActEmployeeCountDevValues[i]);
            totalRow.budEmployeeCount_Total = !budEmployeeCountTotalList.Any() ? "-" : String.Format("{0:n2}", budEmployeeCountTotalList.SelectMany(x => x.EmployeeList).Distinct().Count());
            totalRow.actEmployeeCount_Total = !actEmployeeCountTotalList.Any() ? "-" : String.Format("{0:n2}", actEmployeeCountTotalList.SelectMany(x => x.EmployeeList).Distinct().Count());
            totalRow.revEmployeeCount_Total = !revEmployeeCountTotalList.Any() ? "-" : String.Format("{0:n2}", revEmployeeCountTotalList.SelectMany(x => x.EmployeeList).Distinct().Count());
            var budActEmployeeDeviationTotal = budEmployeeCountTotalList.SelectMany(x => x.EmployeeList).Distinct().Count() - actEmployeeCountTotalList.SelectMany(x => x.EmployeeList).Distinct().Count();
            totalRow.budActEmployeeCountDev_Total = budActEmployeeDeviationTotal == 0 ? "-" : String.Format("{0:n2}", budActEmployeeDeviationTotal);
            var revActEmployeeDeviationTotal = revEmployeeCountTotalList.SelectMany(x => x.EmployeeList).Distinct().Count() - actEmployeeCountTotalList.SelectMany(x => x.EmployeeList).Distinct().Count();
            totalRow.revActEmployeeCountDev_Total = revActEmployeeDeviationTotal == 0 ? "-" : String.Format("{0:n2}", revActEmployeeDeviationTotal);
            totalRow.prevBudPositionType_Total = prevBudTotalRowValues[i] == 0 ? "-" : String.Format("{0:n2}", prevBudTotalRowValues[i]);
            totalRow.prevActPositionType_Total = prevActTotalRowValues[i] == 0 ? "-" : String.Format("{0:n2}", prevActTotalRowValues[i]);
            totalRow.prevRevPositionType_Total = prevRevTotalRowValues[i] == 0 ? "-" : String.Format("{0:n2}", prevRevTotalRowValues[i]);
            totalRow.prevBudActDevPositionType_Total = prevBudActDevTotalRowValues[i] == 0 ? "-" : String.Format("{0:n2}", prevBudActDevTotalRowValues[i]);
            totalRow.prevRevActDevPositionType_Total = prevRevActDevTotalRowValues[i] == 0 ? "-" : String.Format("{0:n2}", prevRevActDevTotalRowValues[i]);
            totalRow.budPositionType_Total = budTotalRowValues[i] == 0 ? "-" : String.Format("{0:n2}", budTotalRowValues[i]);
            totalRow.actPositionType_Total = actTotalRowValues[i] == 0 ? "-" : String.Format("{0:n2}", actTotalRowValues[i]);
            totalRow.revPositionType_Total = revTotalRowValues[i] == 0 ? "-" : String.Format("{0:n2}", revTotalRowValues[i]);
            totalRow.budActDevPositionType_Total = budActDevTotalRowValues[i] == 0 ? "-" : String.Format("{0:n2}", budActDevTotalRowValues[i]);
            totalRow.revActDevPositionType_Total = revActDevTotalRowValues[i] == 0 ? "-" : String.Format("{0:n2}", revActDevTotalRowValues[i]);

            result.Add(totalRow);

            return result;
        }

        public async Task<JObject> GetStatusEmpWidgetColSelector(int forecastPeriod, bool isYtdGridColSel, string userId)
        {
            int budgetYear = forecastPeriod / 100;
            UserData user = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(user.language_preference, user.user_name, "StatusEmpManYears");
            List<KeyValuePair> mainColumnsToDisplayList = new List<KeyValuePair>();
            List<KeyValuePair> subColumnsToDisplayList = new List<KeyValuePair>();
            List<KeyValuePair> mainColumnsConfig = new List<KeyValuePair>();
            List<KeyValuePair> subColumnsConfig = new List<KeyValuePair>();
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            string mainColsFlagName = isYtdGridColSel ?  "STATUS_EMP_YTD_MAIN_COLS" : "STATUS_EMP_MAIN_COLS";
            string subColsFlagName = isYtdGridColSel ? "STATUS_EMP_YTD_SUB_COLS" : "STATUS_EMP_SUB_COLS";
            string title = string.Empty;
            bool isChecked = true;

            //check data for main columns
            var statusEmpMainColsConfig = await dbContext.tco_application_flag.Where(x => x.fk_tenant_id == user.tenant_id && x.flag_name == mainColsFlagName && x.budget_year == budgetYear && (x.flag_key_id == "-1" || x.flag_key_id == user.pk_id.ToString())).ToListAsync();

            var tenantMainColumnConfig = statusEmpMainColsConfig.Any() ? statusEmpMainColsConfig.FirstOrDefault(x => x.flag_key_id == "-1") : null;

            if (tenantMainColumnConfig != null)
            {
                Guid? flagGuid = tenantMainColumnConfig.flag_guid;

                if (flagGuid != null)
                {
                    clsTenantCloudTableEntity updateEntity = await _utility.GetCloudTableContentAsync("WADTenantData", user.tenant_id.ToString(), flagGuid.ToString());
                    if (updateEntity != null)
                    {
                        JArray columnsArray = new JArray();
                        columnsArray = JArray.FromObject(JsonConvert.DeserializeObject(updateEntity.data.ToString()));

                        mainColumnsConfig = (from s in columnsArray
                                             select new KeyValuePair
                                             {
                                                 key = (string)s["key"],
                                                 value = (string)s["value"],
                                                 isChecked = (bool)s["isChecked"],
                                             }).ToList();
                    }
                }
            }

            List<string> allMainColumns = new List<string>() { "prevBudEmployeeCount", "prevRevEmployeeCount", "prevActEmployeeCount", "prevBudActEmployeeCountDev","prevRevActEmployeeCountDev",
                                                                "prevBudManYears", "prevRevManYears", "prevActualManYears", "prevBudActDeviationManYears", "prevRevActDeviationManYears",
                                                                "budEmployeeCount", "revEmployeeCount", "actEmployeeCount", "budActEmployeeCountDev", "revActEmployeeCountDev",
                                                                "budManYears", "revManYears", "actualManYears", "budActDeviationManYears", "revActDeviationManYears" };

            List<string> defaultCheckedMainColumns = new List<string>() { "actEmployeeCount", "budManYears", "actualManYears",  "budActDeviationManYears" };

            //loop main columns
            foreach (var item in allMainColumns)
            {
                switch (item.ToString())
                {
                    case "prevBudEmployeeCount":
                        title = isYtdGridColSel ? langStringValues["StatusEmp_PrevBud_EmpCount_Ytd"].LangText : langStringValues["StatusEmp_PrevBud_EmpCount"].LangText;
                        break;
                    case "prevActEmployeeCount":
                        title = isYtdGridColSel ? langStringValues["StatusEmp_PrevAct_EmpCount_Ytd"].LangText : langStringValues["StatusEmp_PrevAct_EmpCount"].LangText;
                        break;
                    case "prevRevEmployeeCount":
                        title = isYtdGridColSel ? langStringValues["StatusEmp_PrevRev_EmpCount_Ytd"].LangText : langStringValues["StatusEmp_PrevRev_EmpCount"].LangText;
                        break;
                    case "prevBudActEmployeeCountDev":
                        title = isYtdGridColSel ? langStringValues["StatusEmp_PrevDev_EmpCount_Ytd"].LangText : langStringValues["StatusEmp_PrevDev_EmpCount"].LangText;
                        break;
                    case "prevRevActEmployeeCountDev":
                        title = isYtdGridColSel ? langStringValues["StatusEmp_PrevRevActDev_EmpCount_Ytd"].LangText : langStringValues["StatusEmp_PrevRevActDev_EmpCount"].LangText;
                        break;
                    case "budEmployeeCount":
                        title = isYtdGridColSel ? langStringValues["StatusEmp_Bud_EmpCount_Ytd"].LangText : langStringValues["StatusEmp_Bud_EmpCount"].LangText;
                        break;
                    case "actEmployeeCount":
                        title = isYtdGridColSel ? langStringValues["StatusEmp_Act_EmpCount_Ytd"].LangText : langStringValues["StatusEmp_Act_EmpCount"].LangText;
                        break;
                    case "revEmployeeCount":
                        title = isYtdGridColSel ? langStringValues["StatusEmp_Rev_EmpCount_Ytd"].LangText : langStringValues["StatusEmp_Rev_EmpCount"].LangText;
                        break;
                    case "budActEmployeeCountDev":
                        title = isYtdGridColSel ? langStringValues["StatusEmp_Dev_EmpCount_Ytd"].LangText : langStringValues["StatusEmp_Dev_EmpCount"].LangText;
                        break;
                    case "revActEmployeeCountDev":
                        title = isYtdGridColSel ? langStringValues["StatusEmp_RevActDev_EmpCount_Ytd"].LangText : langStringValues["StatusEmp_RevActDev_EmpCount"].LangText;
                        break;
                    case "prevBudManYears":
                        title = isYtdGridColSel ? langStringValues["StatusEmp_PrevBud_ManYears_Ytd"].LangText : langStringValues["StatusEmp_PrevBud_ManYears"].LangText;
                        break;
                    case "prevActualManYears":
                        title = isYtdGridColSel ? langStringValues["StatusEmp_PrevAct_ManYears_Ytd"].LangText : langStringValues["StatusEmp_PrevAct_ManYears"].LangText;
                        break;
                    case "prevRevManYears":
                        title = isYtdGridColSel ? langStringValues["StatusEmp_PrevRev_ManYears_Ytd"].LangText : langStringValues["StatusEmp_PrevRev_ManYears"].LangText;
                        break;
                    case "prevBudActDeviationManYears":
                        title = isYtdGridColSel ? langStringValues["StatusEmp_PrevDev_ManYears_Ytd"].LangText : langStringValues["StatusEmp_PrevDev_ManYears"].LangText;
                        break;
                    case "prevRevActDeviationManYears":
                        title = isYtdGridColSel ? langStringValues["StatusEmp_PrevRevActDev_ManYears_Ytd"].LangText : langStringValues["StatusEmp_PrevRevActDev_ManYears"].LangText;
                        break;
                    case "budManYears":
                        title = isYtdGridColSel ? langStringValues["StatusEmp_Bud_ManYears_Ytd"].LangText : langStringValues["StatusEmp_Bud_ManYears"].LangText;
                        break;
                    case "actualManYears":
                        title = isYtdGridColSel ? langStringValues["StatusEmp_Act_ManYears_Ytd"].LangText : langStringValues["StatusEmp_Act_ManYears"].LangText;
                        break;
                    case "revManYears":
                        title = isYtdGridColSel ? langStringValues["StatusEmp_Rev_ManYears_Ytd"].LangText : langStringValues["StatusEmp_Rev_ManYears"].LangText;
                        break;
                    case "budActDeviationManYears":
                        title = isYtdGridColSel ? langStringValues["StatusEmp_Dev_ManYears_Ytd"].LangText : langStringValues["StatusEmp_Dev_ManYears"].LangText;
                        break;
                    case "revActDeviationManYears":
                        title = isYtdGridColSel ? langStringValues["StatusEmp_RevActDev_ManYears_Ytd"].LangText : langStringValues["StatusEmp_RevActDev_ManYears"].LangText;
                        break;
                }

                if (tenantMainColumnConfig != null)
                {
                    if (mainColumnsConfig.Count > 0 && mainColumnsConfig.Select(x => x.key).Contains(item.ToString()))
                    {
                        isChecked = mainColumnsConfig.FirstOrDefault(x => x.key == item.ToString()).isChecked;
                    }
                }
                else
                {
                    isChecked = defaultCheckedMainColumns.Contains(item);
                }

                KeyValuePair columnInfo = new KeyValuePair()
                {
                    key = item.ToString(),
                    value = title,
                    isChecked = isChecked
                };
                mainColumnsToDisplayList.Add(columnInfo);
            }


            //check data for sub columns
            List<string> defaultCheckedSubColumns = new List<string>() { "Total" };

            var statusEmpSubColsConfig = await dbContext.tco_application_flag.Where(x => x.fk_tenant_id == user.tenant_id && x.flag_name == subColsFlagName && x.budget_year == budgetYear && (x.flag_key_id == "-1" || x.flag_key_id == user.pk_id.ToString())).ToListAsync();

            var tenantSubColumnConfig = statusEmpSubColsConfig.Any() ? statusEmpSubColsConfig.FirstOrDefault(x => x.flag_key_id == "-1") : null;

            if (tenantSubColumnConfig != null)
            {
                Guid? flagGuid = tenantSubColumnConfig.flag_guid;

                if (flagGuid != null)
                {
                    clsTenantCloudTableEntity updateEntity = await _utility.GetCloudTableContentAsync("WADTenantData", user.tenant_id.ToString(), flagGuid.ToString());
                    if (updateEntity != null)
                    {
                        JArray columnsArray = new JArray();
                        columnsArray = JArray.FromObject(JsonConvert.DeserializeObject(updateEntity.data.ToString()));

                        subColumnsConfig = (from s in columnsArray
                                            select new KeyValuePair
                                            {
                                                key = (string)s["key"],
                                                value = (string)s["value"],
                                                isChecked = (bool)s["isChecked"],
                                            }).ToList();
                    }
                }
            }

            List<string> allSubColumns = await _unitOfWork.StatusEmpManYearsRepo.GetPositionTypes(user.tenant_id, budgetYear);
            allSubColumns.Add("Total");

            //loop sub columns
            foreach (var item in allSubColumns)
            {

                if (tenantSubColumnConfig != null)
                {
                    if (subColumnsConfig.Count > 0 && subColumnsConfig.Select(x => x.key).Contains(item.ToString()))
                    {
                        isChecked = subColumnsConfig.FirstOrDefault(x => x.key == item.ToString()).isChecked;
                    }
                }
                else
                {

                    isChecked = defaultCheckedSubColumns.Contains(item);

                }

                KeyValuePair columnInfo = new KeyValuePair()
                {
                    key = item.ToString(),
                    value = item.ToString(),
                    isChecked = isChecked
                };
                subColumnsToDisplayList.Add(columnInfo);
            }

            JObject retVal = new JObject();
            retVal.Add("mainColumns", JArray.FromObject(mainColumnsToDisplayList));
            retVal.Add("defaultMainColumns", JArray.FromObject(allMainColumns));
            retVal.Add("subColumns", JArray.FromObject(subColumnsToDisplayList));
            retVal.Add("defaultSubColumns", JArray.FromObject(allSubColumns));

            return retVal;
        }

        public async Task<string> SaveStatusEmpColSelector(string userId, StatusEmpColHelper inputData)
        {
            try
            {
                UserData user = await _utility.GetUserDetailsAsync(userId);
                int year = inputData.ForecastPeriod / 100;

                string mainColsFlagName = inputData.IsYtdGridColSel ? "STATUS_EMP_YTD_MAIN_COLS" : "STATUS_EMP_MAIN_COLS";
                string subColsFlagName = inputData.IsYtdGridColSel ? "STATUS_EMP_YTD_SUB_COLS" : "STATUS_EMP_SUB_COLS";

                string selectedMainColumns = JsonConvert.SerializeObject(inputData.SelectedMainColumns);
                await _utility.SaveColumnsConfigAsync(userId, mainColsFlagName, selectedMainColumns,  -1 , year);

                string selectedSubColumns = JsonConvert.SerializeObject(inputData.SelectedSubColumns);
                await _utility.SaveColumnsConfigAsync(userId, subColsFlagName, selectedSubColumns, -1, year);

                return "true";
            }
            catch
            {
                return "false";
            }
        }

        public async Task<JObject> GetStatusEmpEqualityColSelector(int forecastPeriod, string userId)
        {
            int budgetYear = forecastPeriod / 100;
            UserData user = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(user.language_preference, user.user_name, "MREquality");
            List<KeyValuePair> mainColumnsToDisplayList = new List<KeyValuePair>();
            List<KeyValuePair> mainColumnsConfig = new List<KeyValuePair>();
            string mainColsFlagName = "STATUS_EMP_EQUALITY_MAIN_COLS";
            string title = string.Empty;
            bool isChecked = true;

            //check data for main columns
            var statusEmpEqualityColsConfig = await _utility.GetApplicationFlag(userId, mainColsFlagName,"-1", budgetYear);

            if (statusEmpEqualityColsConfig != null)
            {
                Guid? flagGuid = statusEmpEqualityColsConfig.flag_guid;

                if (flagGuid != null)
                {
                    clsTenantCloudTableEntity updateEntity = await _utility.GetCloudTableContentAsync("WADTenantData", user.tenant_id.ToString(), flagGuid.ToString());
                    if (updateEntity != null)
                    {
                        JArray columnsArray = new JArray();
                        columnsArray = JArray.FromObject(JsonConvert.DeserializeObject(updateEntity.data.ToString()));

                        mainColumnsConfig = (from s in columnsArray
                                             select new KeyValuePair
                                             {
                                                 key = (string)s["key"],
                                                 value = (string)s["value"],
                                                 isChecked = (bool)s["isChecked"],
                                             }).ToList();
                    }
                }
            }

            List<string> allMainColumns = new List<string>() { "numberOfEmp", "manYears", "totalPercentage", "averageSalary", "avgSalaryPercentage_WomenVSMen", "averagePositionPercentage", "averageSeniority" };

            List<string> defaultCheckedMainColumns = new List<string>() { "numberOfEmp", "manYears", "totalPercentage", "averageSalary", "avgSalaryPercentage_WomenVSMen", "averagePositionPercentage", "averageSeniority" };

            //loop main columns
            foreach (var item in allMainColumns)
            {
                switch (item.ToString())
                {
                    case "numberOfEmp":
                        title = langStringValues["MR_Equality_numberOfEmp"].LangText;
                        break;
                    case "manYears":
                        title = langStringValues["MR_Equality_manYears"].LangText;
                        break;
                    case "totalPercentage":
                        title = langStringValues["MR_Equality_totalPercentage"].LangText;
                        break;
                    case "averageSalary":
                        title = langStringValues["MR_Equality_averageSalary"].LangText;
                        break;
                    case "avgSalaryPercentage_WomenVSMen":
                        title = langStringValues["MR_Equality_avgSalaryPercentage_WomenVSMen"].LangText;
                        break;
                    case "averagePositionPercentage":
                        title = langStringValues["MR_Equality_averagePositionPercentage"].LangText;
                        break;
                    case "averageSeniority":
                        title = langStringValues["MR_Equality_averageSeniority"].LangText;
                        break;
                }

                if (statusEmpEqualityColsConfig != null)
                {
                    if (mainColumnsConfig.Count > 0 && mainColumnsConfig.Select(x => x.key).Contains(item.ToString()))
                    {
                        isChecked = mainColumnsConfig.FirstOrDefault(x => x.key == item.ToString()).isChecked;
                    }
                }
                else
                {
                    isChecked = defaultCheckedMainColumns.Contains(item);
                }

                KeyValuePair columnInfo = new KeyValuePair()
                {
                    key = item.ToString(),
                    value = title,
                    isChecked = isChecked
                };
                mainColumnsToDisplayList.Add(columnInfo);
            }


            JObject retVal = new JObject();
            retVal.Add("mainColumns", JArray.FromObject(mainColumnsToDisplayList));
            retVal.Add("defaultMainColumns", JArray.FromObject(allMainColumns));

            return retVal;
        }


        public async Task<string> SaveStatusEmpEqualityColSelector(string userId, StatusEmpColHelper inputData)
        {
            try
            {
                UserData user = await _utility.GetUserDetailsAsync(userId);
                int year = inputData.ForecastPeriod / 100;

                string selectedMainColumns = JsonConvert.SerializeObject(inputData.SelectedMainColumns);
                await _utility.SaveColumnsConfigAsync(userId, "STATUS_EMP_EQUALITY_MAIN_COLS", selectedMainColumns, -1, year);

                return "true";
            }
            catch
            {
                return "false";
            }
        }

        public async Task<List<StatusEmpManYearsPrimaryData>> GetStatusEmpManYearsDataPerAge(UserData userDetails, string orgVersion, StatusEmpDataInput input)
        {
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "StatusEmpManYears");

            string unefinedAgesTitle = langStringValues["StatusEmp_Unefined_Ages_Title"].LangText;

            var manYearsData = input.IsYtdGrid ? await _unitOfWork.StatusEmpManYearsRepo.GetStatusEmpDataYtd(userDetails.tenant_id, orgVersion, input):await _unitOfWork.StatusEmpManYearsRepo.GetStatusEmpData(userDetails.tenant_id, orgVersion, input);

            var ageGroupData = await _unitOfWork.StatusEmpManYearsRepo.GetAbsenceAgeGroups(userDetails.tenant_id);

            var data = (from a in manYearsData
                        select new StatusEmpManYearsPrimaryData()
                        {
                            AgeGroup = (a.Age == -1)? unefinedAgesTitle : GetAgeGroupByAge(a.Age, ageGroupData),
                            Age = a.Age,
                            ManYears = a.ManYears,
                            PosTypeName = a.PosTypeName,
                            IsOriginalBudget = a.IsOriginalBudget,
                            Date = a.Date,
                            EmployeeCount = a.EmployeeCount,
                            ResName = a.ResName,
                            BudgetType = a.BudgetType
                        }).ToList();

            data = (from a in data
                    group new { a } by new { a.AgeGroup, a.PosTypeName, a.IsOriginalBudget, a.BudgetType } into g
                    select new StatusEmpManYearsPrimaryData()
                    {
                        AgeGroup = g.Key.AgeGroup,
                        PosTypeName = g.Key.PosTypeName,
                        IsOriginalBudget = g.Key.IsOriginalBudget,
                        ManYears = g.Sum(x => x.a.ManYears),
                        Date = g.Select(x => x.a.Date).FirstOrDefault(),
                        EmployeeCount = g.Select(x => x.a.EmployeeCount != 0 ? x.a.EmployeeCount.ToString() : x.a.ResName).Distinct().Count(),
                        EmployeeList = g.Select(x => x.a.EmployeeCount != 0 ? x.a.EmployeeCount.ToString() : x.a.ResName).ToList(),
                        BudgetType = g.Key.BudgetType
                    }).ToList();

            return data;
        }

        public async Task<List<ReportEqualityExpandDataStr>> GetEqualityExpandData(string userId, StatusEmpDataInput input)
        {
            UserData userDetail = await _utility.GetUserDetailsAsync(userId);
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, input.ForecastPeriod);

            var equalityInfo = await _unitOfWork.StatusEmpManYearsRepo.GetEqualityExpandData(userDetail.tenant_id, orgVersionContent.orgVersion, input);
            var tmpData = await _unitOfWork.MonthlyReportEqualityRepo.getEmpData(userDetail.tenant_id);
            var dataResult = getDataBasedonEmpId(userDetail.tenant_id, equalityInfo, tmpData);

            List<ReportEqualityExpandDataStr> result = new List<ReportEqualityExpandDataStr>();


            var distinctDataresult = equalityInfo.GroupBy(x => x.EmployeeId).Select(x => x.First()).ToList();
            var percentageDatResult = equalityInfo.GroupBy(d => d.EmployeeId).Select(g => new { employeeId = g.First().EmployeeId, positionPercentage = Math.Round(g.Sum(s => s.AvgPosPercentage)), gender = g.First().Gender });

            var genderBalanceWomen = distinctDataresult.Count(x => x.Gender.ToLower() == women);
            var genderBalanceMen = distinctDataresult.Count(x => x.Gender.ToLower() == men);
            var temporaryEmployeesWomen = dataResult.Where(x => x.Gender.ToLower() == women).Select(x => x.EmployeeId).Distinct().Count();
            var temporaryEmployeesMen = dataResult.Where(x => x.Gender.ToLower() == men).Select(x => x.EmployeeId).Distinct().Count();
            var partTimeWomen = percentageDatResult.Where(x => x.gender.ToLower() == women && x.positionPercentage < 100).Count();
            var partTimeMen = percentageDatResult.Where(x => x.gender.ToLower() == men && x.positionPercentage < 100).Count();

            ReportEqualityExpandDataStr row = new ReportEqualityExpandDataStr()
            {
                GenderBalanceWomen = genderBalanceWomen == 0 ? "-" : genderBalanceWomen.ToString(),
                GenderBalanceMen = genderBalanceMen == 0 ? "-" : genderBalanceMen.ToString(),
                TemporaryEmployeesWomen = temporaryEmployeesWomen == 0 ? "-" : temporaryEmployeesWomen.ToString(),
                TemporaryEmployeesMen = temporaryEmployeesMen == 0 ? "-" : temporaryEmployeesMen.ToString(),
                PartTimeWomen = partTimeWomen == 0 ? "-" : partTimeWomen.ToString(),
                PartTimeMen = partTimeMen == 0 ? "-" : partTimeMen.ToString(),
                Date = equalityInfo.Any() ? equalityInfo.FirstOrDefault().Date : string.Empty,
            };
            result.Add(row);
            return result;
        }

        private List<ReportExpandPrimaryData> getDataBasedonEmpId(int tenantId, List<ReportExpandPrimaryData> equalityInfo, List<tmd_emp_type> empData)
        {
            var data = (from a in equalityInfo
                        join b in empData on new { c = a.PosTypeValue.Trim() } equals new { c = b.pk_emp_type_id.Trim() }
                        where b.fk_tenant_id == tenantId
                        select new ReportExpandPrimaryData
                        {
                            Gender = a.Gender,
                            AvgPosPercentage = a.AvgPosPercentage,
                            PosTypeValue = a.PosTypeValue,
                            EmployeeId = a.EmployeeId,
                            Date = a.Date
                        }).ToList();
            return data;
        }

        public async Task<List<StatusEmpEqualityData>> GetReportEqualityData(string userId, StatusEmpDataInput input)
        {
            UserData userDetail = await _utility.GetUserDetailsAsync(userId);
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, input.ForecastPeriod);
            Dictionary<string, clsLanguageString> manYearsLangStrings = await _utility.GetLanguageStringsAsync(userDetail.language_preference, userDetail.user_name, "MREquality");
            List<StatusEmpEqualityPrimaryDataUpdated> data = new List<StatusEmpEqualityPrimaryDataUpdated>();
            List<StatusEmpEqualityPrimaryDataUpdated> dataResult = new List<StatusEmpEqualityPrimaryDataUpdated>();
            List<string> groupingData = new List<string>();

            switch (input.GroupingType)
            {
                case ((int)StatusEmpGroupingOption.PerUnit):
                    data = await _unitOfWork.StatusEmpManYearsRepo.GetReportEqualityDataPerOrgUnitData(userDetail.tenant_id, orgVersionContent.orgVersion, input);
                    dataResult = await _unitOfWork.StatusEmpManYearsRepo.GetReportEqualityDataPerOrgUnit(userDetail.tenant_id, orgVersionContent.orgVersion, input);
                    groupingData = dataResult.Select(x => x.OrgName).Distinct().OrderBy(x => x).ToList();
                    break;

                case ((int)StatusEmpGroupingOption.PositionCategory):
                    data = await _unitOfWork.StatusEmpManYearsRepo.FetchResultForPerPosCategoryDataAsync(userDetail.tenant_id, orgVersionContent.orgVersion, input);
                    dataResult = await _unitOfWork.StatusEmpManYearsRepo.GetReportEqualityDataPerPosCategory(userDetail.tenant_id, orgVersionContent.orgVersion, input);
                    groupingData = dataResult.Select(x => x.PublicPosCodeName).Distinct().OrderBy(x => x).ToList();
                    break;

                case ((int)StatusEmpGroupingOption.KSPositionGroup):
                    data = await _unitOfWork.StatusEmpManYearsRepo.FetchResultForPerKsPositionDataAsync(userDetail.tenant_id, orgVersionContent.orgVersion, input);
                    dataResult = await _unitOfWork.StatusEmpManYearsRepo.GetReportEqualityDataPerKsPosition(userDetail.tenant_id, orgVersionContent.orgVersion, input);
                    groupingData = dataResult.Select(x => x.KsPositionGroup).Distinct().OrderBy(x => x).ToList();
                    break;

                case ((int)StatusEmpGroupingOption.AgeGroups):
                    data = await _unitOfWork.StatusEmpManYearsRepo.GetReportEqualityData(userDetail.tenant_id, orgVersionContent.orgVersion, input);
                    dataResult = await GetStatusEmpEqualityDataPerAge(userDetail.tenant_id, orgVersionContent.orgVersion, input);
                    groupingData = dataResult.Select(x => x.AgeGroup).Distinct().OrderBy(x => x).ToList();
                    break;
            }

            StatusEmpEqualityData totalRow = new StatusEmpEqualityData();
            totalRow.PublicPosCodeName = manYearsLangStrings["MR_Equality_Total_Row"].LangText;
            totalRow.OrgName = manYearsLangStrings["MR_Equality_Total_Row"].LangText;
            totalRow.AgeGroup = manYearsLangStrings["MR_Equality_Total_Row"].LangText;
            totalRow.KsPositionGroup = manYearsLangStrings["MR_Equality_Total_Row"].LangText;

            List<StatusEmpEqualityData> result = new List<StatusEmpEqualityData>();
            foreach (var item in groupingData)
            {
                List<StatusEmpEqualityPrimaryDataUpdated> dataSetWomen = new List<StatusEmpEqualityPrimaryDataUpdated>();
                List<StatusEmpEqualityPrimaryDataUpdated> dataSetMen = new List<StatusEmpEqualityPrimaryDataUpdated>();

                switch (input.GroupingType)
                {
                    case ((int)StatusEmpGroupingOption.PerUnit):
                        dataSetWomen = dataResult.Where(x => x.OrgName == item && x.Gender.ToLower() == women).ToList();
                        dataSetMen = dataResult.Where(x => x.OrgName == item && x.Gender.ToLower() == men).ToList();
                        break;

                    case ((int)StatusEmpGroupingOption.PositionCategory):
                        dataSetWomen = dataResult.Where(x => x.PublicPosCodeName == item && x.Gender.ToLower() == women).ToList();
                        dataSetMen = dataResult.Where(x => x.PublicPosCodeName == item && x.Gender.ToLower() == men).ToList();
                        break;

                    case ((int)StatusEmpGroupingOption.KSPositionGroup):
                        dataSetWomen = dataResult.Where(x => x.KsPositionGroup == item && x.Gender.ToLower() == women).ToList();
                        dataSetMen = dataResult.Where(x => x.KsPositionGroup == item && x.Gender.ToLower() == men).ToList();
                        break;

                    case ((int)StatusEmpGroupingOption.AgeGroups):
                        dataSetWomen = dataResult.Where(x => x.AgeGroup == item && x.Gender.ToLower() == women).ToList();
                        dataSetMen = dataResult.Where(x => x.AgeGroup == item && x.Gender.ToLower() == men).ToList();
                        break;
                }

                var distinctEmpIdsCount_Women = (dataSetWomen ?? new List<StatusEmpEqualityPrimaryDataUpdated>()).Where(x => x.EmployeeIdLst != null).SelectMany(x => x.EmployeeIdLst).Distinct().Count();
                var distinctEmpIdsCount_Men = (dataSetMen ?? new List<StatusEmpEqualityPrimaryDataUpdated>()).Where(x => x.EmployeeIdLst != null).SelectMany(x => x.EmployeeIdLst).Distinct().Count();
                var distinctSeniorityCount_Women = (dataSetWomen ?? new List<StatusEmpEqualityPrimaryDataUpdated>()).Where(x => x.EmployeeIdLstForSeniority != null).SelectMany(x => x.EmployeeIdLstForSeniority).Distinct().Count();
                var distinctSeniorityCount_Men = (dataSetMen ?? new List<StatusEmpEqualityPrimaryDataUpdated>()).Where(x => x.EmployeeIdLstForSeniority != null).SelectMany(x => x.EmployeeIdLstForSeniority).Distinct().Count();
                StatusEmpEqualityData row = new StatusEmpEqualityData()
                {
                    OrgName = item,
                    PublicPosCodeName = item,
                    KsPositionGroup = item,
                    AgeGroup = item,
                    ManYears_Women = dataSetWomen.Sum(x => x.ManYears),
                    AvgSalary_Women = distinctEmpIdsCount_Women > 0 ? (dataSetWomen?.Sum(x => x.AvgSalary) / distinctEmpIdsCount_Women ?? 0) : 0,
                    AvgPosPercentage_Women = distinctEmpIdsCount_Women > 0 ?(dataSetWomen?.Sum(x => x.AvgPosPercentage) / distinctEmpIdsCount_Women ?? 0) :0 ,
                    AvgSeniority_Women = distinctSeniorityCount_Women > 0 ? (dataSetWomen?.Sum(x => x.AvgSeniority) / distinctSeniorityCount_Women ?? 0) : 0,
                    NumberOfEmp_Women = distinctEmpIdsCount_Women ,
                    ManYears_Men = dataSetMen?.Sum(x => x.ManYears) ?? 0,
                    AvgSalary_Men = distinctEmpIdsCount_Men > 0 ? (dataSetMen?.Sum(x => x.AvgSalary) / distinctEmpIdsCount_Men ?? 0) : 0,
                    AvgPosPercentage_Men = distinctEmpIdsCount_Men > 0 ? (dataSetMen?.Sum(x => x.AvgPosPercentage) / distinctEmpIdsCount_Men ?? 0) : 0,
                    AvgSeniority_Men = distinctSeniorityCount_Men > 0 ? (dataSetMen?.Sum(x => x.AvgSeniority) / distinctSeniorityCount_Men ?? 0) : 0,
                    NumberOfEmp_Men = distinctEmpIdsCount_Men,
                };

                totalRow.ManYears_Women += row.ManYears_Women;
                totalRow.ManYears_Men += row.ManYears_Men;
                totalRow.NumberOfEmp_Men += row.NumberOfEmp_Men;
                totalRow.NumberOfEmp_Women += row.NumberOfEmp_Women;

                if (row.AvgSalary_Women != 0 && row.AvgSalary_Men != 0)
                    row.AvgSalaryPercentage_WomenVSMen = (row.AvgSalary_Women * 100) / row.AvgSalary_Men;
                else
                    row.AvgSalaryPercentage_WomenVSMen = -1;

                var totalManYears = row.NumberOfEmp_Men + row.NumberOfEmp_Women;

                row.TotalPercentage_Women = totalManYears == 0 ? 0 : Convert.ToDecimal(row.NumberOfEmp_Women * 100) / totalManYears;
                row.TotalPercentage_Men = totalManYears == 0 ? 0 : Convert.ToDecimal(row.NumberOfEmp_Men * 100) / totalManYears;

                result.Add(row);
            }
            List<int> employeeListMen = new List<int>();
            List<int> employeeListWomen = new List<int>();
            foreach(var lst in dataResult.Where(x => x.Gender.ToLower() == men))
            {
                employeeListMen.AddRange(lst.EmployeeIdLst);
            }
            foreach (var lst in dataResult.Where(x => x.Gender.ToLower() == women))
            {
                employeeListWomen.AddRange(lst.EmployeeIdLst);
            }
            totalRow.NumberOfEmp_Women = employeeListWomen.Distinct().Count();
            totalRow.NumberOfEmp_Men = employeeListMen.Distinct().Count();

            var totalResManYears = totalRow.NumberOfEmp_Women + totalRow.NumberOfEmp_Men;
            totalRow.TotalPercentage_Women = totalResManYears == 0 ? 0 : Convert.ToDecimal(totalRow.NumberOfEmp_Women * 100) / totalResManYears;
            totalRow.TotalPercentage_Men = totalResManYears == 0 ? 0 : Convert.ToDecimal(totalRow.NumberOfEmp_Men * 100) / totalResManYears;

            if (dataResult.Count != 0)
            {
                //var avgPosPercentageByEmpId = (from a in dataResult
                //                               group a by new { a.EmployeeId } into g
                //                               select new
                //                               {
                //                                   avgPosPercentage = g.Sum(x => x.AvgPosPercentage),
                //                                   gender = g.FirstOrDefault().Gender
                //                               }).ToList();

                var avgPosPercentange = (from a in dataResult
                                         group a by new { a.Gender } into g
                                         select new
                                         {
                                             avgPosPercentange = g.Sum(x => x.AvgPosPercentage) / g.SelectMany(x => x.EmployeeIdLst).Distinct().Count(),
                                             gender = g.Key.Gender
                                         }).ToList();

                var avgSalary = (from a in data
                                 group a by new { a.Gender } into g
                                 select new
                                 {
                                     avgSalary = g.Sum(x => x.AvgSalary)/ g.Select(x => x.EmployeeId).Distinct().Count(),
                                     gender = g.Key.Gender
                                 }).ToList();

                var avgSeniority = (from a in dataResult
                                    group a by new { a.Gender } into g
                                    select new
                                    {
                                        avgSeniority = g.SelectMany(x => x.EmployeeIdLstForSeniority).Count() > 0 ? (g.Sum(x => x.AvgSeniority) / g.SelectMany(x => x.EmployeeIdLstForSeniority).Count()) : 0,
                                        gender = g.Key.Gender,
                                    }).ToList();

                var numOfEmp = (from a in dataResult
                                group a by new { a.Gender } into g
                                select new
                                {
                                    numOfEmp = g.SelectMany(x => x.EmployeeIdLst).Distinct().Count(),
                                    gender = g.Key.Gender,
                                }).ToList();

                var empCountMen = numOfEmp.FirstOrDefault(x => x.gender.ToString().ToLower() == men);
                if (empCountMen != null) totalRow.NumberOfEmp_Men = empCountMen.numOfEmp;

                var empCountWomen = numOfEmp.FirstOrDefault(x => x.gender.ToString().ToLower() == women);
                if (empCountWomen != null) totalRow.NumberOfEmp_Women = empCountWomen.numOfEmp;

                var avgPosPercentageMen = avgPosPercentange.FirstOrDefault(x => x.gender.ToString().ToLower() == men);
                if (avgPosPercentageMen != null) totalRow.AvgPosPercentage_Men = avgPosPercentageMen.avgPosPercentange;
                var avgPosPercentageWomen = avgPosPercentange.FirstOrDefault(x => x.gender.ToString().ToLower() == women);
                if (avgPosPercentageWomen != null) totalRow.AvgPosPercentage_Women = avgPosPercentageWomen.avgPosPercentange;

                var avgSalaryMen = avgSalary.FirstOrDefault(x => x.gender.ToString().ToLower() == men);
                if (avgSalaryMen != null)
                {
                    totalRow.AvgSalary_Men = avgSalaryMen.avgSalary;
                }
                var avgSalaryWomen = avgSalary.FirstOrDefault(x => x.gender.ToString().ToLower() == women);
                if (avgSalaryWomen != null)
                {
                    totalRow.AvgSalary_Women = avgSalaryWomen.avgSalary;
                }

                var avgSeniorityMen = avgSeniority.FirstOrDefault(x => x.gender.ToString().ToLower() == men);
                if (avgSeniorityMen != null) totalRow.AvgSeniority_Men = avgSeniorityMen.avgSeniority;
                var avgSeniorityWomen = avgSeniority.FirstOrDefault(x => x.gender.ToString().ToLower() == women);
                if (avgSeniorityWomen != null) totalRow.AvgSeniority_Women = avgSeniorityWomen.avgSeniority;
                if (avgSalaryMen != null && avgSalaryWomen != null) totalRow.AvgSalaryPercentage_WomenVSMen = Math.Round(avgSalaryMen.avgSalary, 0) != 0 ? (avgSalaryWomen.avgSalary * 100) / avgSalaryMen.avgSalary : 0;
            }
            else
            {
                totalRow.AvgPosPercentage_Men = 0;
                totalRow.AvgPosPercentage_Women = 0;
                totalRow.AvgSalary_Men = 0;
                totalRow.AvgSalary_Women = 0;
                totalRow.AvgSeniority_Men = 0;
                totalRow.AvgSeniority_Women = 0;
                totalRow.AvgSalaryPercentage_WomenVSMen = 0;
            }
            totalRow.SemiFlag = true;
            result.Add(totalRow);
            return result;
        }

        public async Task<List<StatusEmpEqualityPrimaryDataUpdated>> GetStatusEmpEqualityDataPerAge(int tenantId, string orgVersion, StatusEmpDataInput input)
        {
            var equalityData = await _unitOfWork.StatusEmpManYearsRepo.GetReportEqualityData(tenantId, orgVersion, input);

            var ageGroupData = await _unitOfWork.StatusEmpManYearsRepo.GetAbsenceAgeGroups(tenantId);

            var data = (from a in equalityData
                        select new StatusEmpEqualityPrimaryDataUpdated()
                        {
                            AgeGroup = GetAgeGroupByAge(a.Age, ageGroupData),
                            Age = a.Age,
                            Gender = a.Gender,
                            ManYears = a.ManYears,
                            AvgSalary = a.AvgSalary,
                            AvgPosPercentage = a.AvgPosPercentage,
                            AvgSeniority = a.AvgSeniority,
                            EmployeeId = a.EmployeeId
                        }).ToList();

            data = (from a in data
                    group new { a } by new { a.AgeGroup, a.Gender } into g
                    select new StatusEmpEqualityPrimaryDataUpdated()
                    {
                        AgeGroup = g.Key.AgeGroup,
                        Gender = g.Key.Gender,
                        ManYears = g.Sum(x => x.a.ManYears),
                        AvgSalary = g.Sum(x => x.a.AvgSalary),
                        AvgPosPercentage = g.Sum(x => x.a.AvgPosPercentage),
                        AvgSeniority = g.Any(x => x.a.AvgSeniority != Convert.ToDecimal(-1.00)) ? g.Where(x => x.a.AvgSeniority != Convert.ToDecimal(-1.00)).Sum(x => x.a.AvgSeniority) : 0,
                        EmployeeId = g.Select(x => x.a.EmployeeId).Distinct().Count(),
                        EmployeeIdLst = g.Select(x => x.a.EmployeeId),
                        EmployeeIdLstForSeniority= g.Where(x => x.a.AvgSeniority != Convert.ToDecimal(-1.00)).Select(x => x.a.EmployeeId).ToList()

                    }).ToList();

            return data;
        }


        public async Task<int> GetOrgLvlCountPerTenant(string userId, int forecastPeriod)
        {
            UserData userDetail = await _utility.GetUserDetailsAsync(userId);
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId,userDetail.tenant_id, forecastPeriod);
            return orgVersionContent.lstOrgLevel.Count;
        }

        public async Task<List<StatusEmpPopupData>> GetStatusEmpManYearsPopupData(string userId, StatusEmpPopupHelper input)
        {
            UserData userDetail = await _utility.GetUserDetailsAsync(userId);
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, userDetail.tenant_id, input.forecastPeriod);
            int lastOrgLevel = orgVersionContent.lstOrgLevel.Max(x => x.org_level) ;
            bool isLastOrgLevel = lastOrgLevel == input.orgLevel;
            List<StatusEmpPopupData> result = new List<StatusEmpPopupData>();
            if (input.groupingOption == (int)StatusEmpGroupingOption.KSPositionGroup)
                result = await _unitOfWork.StatusEmpManYearsRepo.GetEmpStatusKSPopupData(userDetail.tenant_id, orgVersionContent.orgVersion, input);
            else
                result = await _unitOfWork.StatusEmpManYearsRepo.GetEmpStatusPopupData(userDetail.tenant_id, orgVersionContent.orgVersion, input, isLastOrgLevel);
            return result;
        }

        private string GetAgeGroupByAge(decimal age, List<tmr_absence_age_groups> ageGroupData)
        {
            foreach (var ageGroup in ageGroupData)
            {
                if (ageGroup.description.Contains("+"))
                {
                    var descAge = int.Parse(ageGroup.description.Substring(0, 2));
                    if (age >= descAge)
                    {
                        return ageGroup.description;
                    }
                }
                else
                {
                    var range = ageGroup.description.Split('-');
                    if (int.Parse(range[0]) <= age && int.Parse(range[1]) >= age)
                    {
                        return ageGroup.description;
                    }
                }
            }

            return string.Empty;
        }

        public List<StatusEmpPopupData> FilterDataBySearchFilter(StatusEmpPopupHelper input, List<StatusEmpPopupData> gridData)
        {
            if (input.searchFilter != null)
            {
                StatusEmpPopupData searchFilterInput = input.searchFilter;
                if (!searchFilterInput.empId.IsNullOrEmpty())
                    gridData = gridData.Where(x =>  x.empId.Contains(searchFilterInput.empId)).ToList();
                if (!searchFilterInput.empName.IsNullOrEmpty())
                    gridData = gridData.Where(x =>  (x.empName.ToUpper().Contains(searchFilterInput.empName.ToUpper()))).ToList();
                if (!searchFilterInput.positionCode.IsNullOrEmpty())
                    gridData = gridData.Where(x => (x.positionCode.Contains(searchFilterInput.positionCode))).ToList();
                if (!searchFilterInput.positionName.IsNullOrEmpty())
                    gridData = gridData.Where(x => x.positionName.ToUpper().Contains(searchFilterInput.positionName.ToUpper())).ToList();
                if (!searchFilterInput.employmentType.IsNullOrEmpty())
                    gridData = gridData.Where(x => x.employmentType.ToUpper().Contains(searchFilterInput.employmentType.ToUpper())).ToList();
                if (!searchFilterInput.positionType.IsNullOrEmpty())
                    gridData = gridData.Where(x => x.positionType.ToUpper().Contains(searchFilterInput.positionType.ToUpper())).ToList();
                if (!searchFilterInput.positionTypeName.IsNullOrEmpty())
                    gridData = gridData.Where(x => x.positionTypeName.ToUpper().Contains(searchFilterInput.positionTypeName.ToUpper())).ToList();
                if (!searchFilterInput.deptCode.IsNullOrEmpty())
                    gridData = gridData.Where(x => x.deptCode.Contains(searchFilterInput.deptCode)).ToList();
                if (!searchFilterInput.positionPercent.IsNullOrEmpty())
                    gridData = gridData.Where(x => x.positionPercent.Contains(searchFilterInput.positionPercent)).ToList();
                if (!searchFilterInput.presencePercent.IsNullOrEmpty())
                    gridData = gridData.Where(x => x.presencePercent.Contains(searchFilterInput.presencePercent)).ToList();
                if (!searchFilterInput.monthStart.IsNullOrEmpty())
                    gridData = gridData.Where(x => x.monthStart.Contains(searchFilterInput.monthStart)).ToList();
                if (!searchFilterInput.monthEnd.IsNullOrEmpty())
                    gridData = gridData.Where(x => x.monthEnd.Contains(searchFilterInput.monthEnd)).ToList();
                if (!searchFilterInput.total.IsNullOrEmpty())
                    gridData = gridData.Where(x => x.total.Contains(searchFilterInput.total)).ToList();
            }
            return gridData;

        }
        public async Task<StatusEmpPopupTreeData> ConstructTotalRowForGrid(List<StatusEmpPopupData> data , string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringValues =await  _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "StatusEmpManYears");
            decimal manyearTotal = 0;
            if (data.Any())
            {
                List<string> totalsStr = data.Select(x => x.total).ToList();
                List<decimal> totals = new List<decimal>();
                foreach (StatusEmpPopupData s in data)
                {
                    var x = Convert.ToDecimal(s.total, CultureInfoFactory.CreateCulture(userDetails.language_preference));
                    s.total = x.ToString();
                    totals.Add(x);
                }
                manyearTotal = totals.Sum();
            }
            StatusEmpPopupTreeData totalRow = new StatusEmpPopupTreeData
            {
                empId = langStringValues["Status_Man_Details_Popup"].LangText,
                empName = "-",
                positionCode = "-",
                positionName = "-",
                employmentType = "-",
                positionType = "-",
                positionTypeName = "-",
                deptCode = "-",
                positionPercent = "-",
                presencePercent = "-",
                monthStart = "-",
                monthEnd = "-",
                total = manyearTotal.ToString(),
                pk_id = Guid.NewGuid().ToString(),
                parentId = null

            };
            return totalRow;

        }

        public async Task<List<StatusEmpPopupTreeData>> GetStatusEmpPopupTreeData(List<StatusEmpPopupData> empData, string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            if (!empData.Any())
            {
                return new List<StatusEmpPopupTreeData>();
            }
            List<StatusEmpPopupTreeData> treeData = empData.Select(data => new StatusEmpPopupTreeData
            {
                empId = data.empId,
                empName = data.empName,
                positionCode = data.positionCode,
                positionName = data.positionName,
                employmentType = data.employmentType,
                positionType = data.positionType,
                positionTypeName = data.positionTypeName,
                publicPositionCodeName = data.publicPositionCodeName,
                deptCode = data.deptCode,
                positionPercent = data.positionPercent,
                presencePercent = data.presencePercent,
                monthStart = data.monthStart,
                monthEnd = data.monthEnd,
                total = data.total,
                orgName1 = data.orgName1,
                orgName2 = data.orgName2,
                orgName3 = data.orgName3,
                orgName4 = data.orgName4,
                orgName5 = data.orgName5,
                orgName6 = data.orgName6,
                orgName7 = data.orgName7,
                orgName8 = data.orgName8,
                orgId1 = data.orgId1,
                orgId2 = data.orgId2,
                orgId3 = data.orgId3,
                orgId4 = data.orgId4,
                orgId5 = data.orgId5,
                orgId6 = data.orgId6,
                orgId7 = data.orgId7,
                orgId8 = data.orgId8,
                age = data.age,
                ksPosition = data.ksPosition,
                pk_id = Guid.NewGuid().ToString(),
                parentId = null,
                hasChildren = false,
                isExpanded = false,
            }).ToList();

            var groupedData = treeData.GroupBy(x => x.empId)
                          .Where(g => g.Count() > 1)
                          .Select(g => new
                          {
                              empId = g.Key,
                              empName = g.First().empName ?? null,
                              //positionPercent = Math.Round(g.Sum(x => decimal.Parse(x.positionPercent, CultureInfoFactory.CreateCulture(userDetails.language_preference))), 2).ToString("0.00"),
                              //presencePercent = Math.Round(g.Sum(x => decimal.Parse(x.presencePercent, CultureInfoFactory.CreateCulture(userDetails.language_preference))), 2).ToString("0.00"), commenting for now to fix it as part of later story
                              positionPercent = "-",
                              presencePercent = "-",
                              total = Math.Round(g.Sum(x => decimal.Parse(x.total, CultureInfoFactory.CreateCulture(userDetails.language_preference))), 2).ToString("0.00"),
                          })
                          .ToList();


            List<StatusEmpPopupTreeData> aggregateRows  = new List<StatusEmpPopupTreeData>();
            foreach(var group in groupedData)
            {
                string aggPkId  = Guid.NewGuid().ToString();
                string employeeId = group.empId.ToString();
                var aggregatedRow = new StatusEmpPopupTreeData
                {
                    empId = group.empId,
                    empName = group.empName,
                    positionCode = "-",
                    positionName = "-",
                    employmentType = "-",
                    positionType = "-",
                    positionTypeName = "-",
                    publicPositionCodeName = "-",
                    deptCode = "-",
                    positionPercent = group.positionPercent,
                    presencePercent = group.presencePercent,
                    monthStart = "-",
                    monthEnd = "-",
                    total = group.total,
                    orgName1 = "-",
                    orgName2 = "-",
                    orgName3 = "-",
                    orgName4 = "-",
                    orgName5 = "-",
                    orgName6 = "-",
                    orgName7 = "-",
                    orgName8 = "-",
                    orgId1 = "-",
                    orgId2 = "-",
                    orgId3 = "-",
                    orgId4 = "-",
                    orgId5 = "-",
                    orgId6 = "-",
                    orgId7 = "-",
                    orgId8 = "-",
                    age = "-",
                    ksPosition = "-",
                    pk_id = aggPkId,
                    parentId = null,
                    hasChildren = true,
                    isExpanded = false
                };

                treeData = (from t in treeData
                            select new StatusEmpPopupTreeData
                            {
                                empId = t.empId,
                                empName = t.empName,
                                positionCode = t.positionCode,
                                positionName = t.positionName,
                                employmentType = t.employmentType,
                                positionType = t.positionType,
                                positionTypeName = t.positionTypeName,
                                publicPositionCodeName = t.publicPositionCodeName,
                                deptCode = t.deptCode,
                                positionPercent = t.positionPercent,
                                presencePercent = t.presencePercent,
                                monthStart = t.monthStart,
                                monthEnd = t.monthEnd,
                                total = t.total,
                                orgName1 = t.orgName1,
                                orgName2 = t.orgName2,
                                orgName3 = t.orgName3,
                                orgName4 = t.orgName4,
                                orgName5 = t.orgName5,
                                orgName6 = t.orgName6,
                                orgName7 = t.orgName7,
                                orgName8 = t.orgName8,
                                orgId1 = t.orgId1,
                                orgId2 = t.orgId2,
                                orgId3 = t.orgId3,
                                orgId4 = t.orgId4,
                                orgId5 = t.orgId5,
                                orgId6 = t.orgId6,
                                orgId7 = t.orgId7,
                                orgId8 = t.orgId8,
                                age = "-",
                                ksPosition = "-",
                                pk_id = t.pk_id,
                                parentId = t.empId == employeeId ? aggPkId : t.parentId,
                                hasChildren = t.hasChildren,
                                isExpanded = t.isExpanded
                            }).ToList();

                aggregateRows.Add(aggregatedRow);
            }

            treeData.InsertRange(0,aggregateRows);
            treeData = treeData.OrderBy(x => x.empName).ThenBy(x=> x.monthStart == "-" ? int.MaxValue: int.Parse(x.monthStart)).ToList();

            return treeData;
        }

       
    }
}