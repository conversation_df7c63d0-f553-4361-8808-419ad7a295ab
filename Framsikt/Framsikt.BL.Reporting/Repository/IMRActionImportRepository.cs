using Framsikt.BL.Helpers;
using Framsikt.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
namespace Framsikt.BL.Reporting.Repository
{
    public interface IMRActionImportRepository
    {
        Task<List<tmr_stage_action_import>> GetActionStagedData(int tenantId, int userId, int forecastPeriod);
        Task DeleteStagedMRActionData(int tenant_id, int pk_id, int forecastPeriod);
        Task<List<tmr_stage_action_import>> GetMrActionStagedData(int tenantId, int budgetYear, int forecastPeriod, int userId, List<int> pkIds);
        Task<ImportInfo> GetMrActionImportInfo(int tenantId, int forecastPeriod, int userId);
        Task ValidateActionImportData(int tenantId, int userId, int budgetYear, int forecastPeriod);
        Task<long> UpdateMrActionImportJobStatus(int tenantId, string jobType, int userId);
        Task<int> GenerateActionId();
    }
}