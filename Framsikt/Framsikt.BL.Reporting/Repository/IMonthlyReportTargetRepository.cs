using Framsikt.BL.Helpers;
using Framsikt.BL.Reporting.Helpers;
using Framsikt.Entities;

namespace Framsikt.BL.Reporting.Repository
{
    public interface IMonthlyReportTargetRepository
    {
        Task<List<GoalnDistrFth>> FetchGoalDstDataRepo(int tenantId, int budgetYear);
        Task<List<TmrCityGoalsStatus>> FetchMRGoalStatusRepo(int tenantId, int budgetYear);
        Task<List<ProgressStatus>> FetchMRProgressStatusRepo(int tenantId);
        Task<List<TargetnDistrFth>> FetchTargetDstDataRepo(int tenantId, int budgetYear);
        Task<List<TmrEffectTargetStatus>> FetchMRTargetStatusRepo(int tenantId, int budgetYear);
        Task<List<MRIndicatorDataHelper>> FetchIndicatorDataRepo(int tenantId, List<int> allowdedFrequency);
        Task<List<tmd_indicator_results>> FetchIndicatorResultsRepo(int tenantId);
        Task<List<tco_goals>> GetGoalDataRepo(int tenantId, int budgetYear);
        Task<List<TmrEffectTargetStatus>> UpdateMRTargetDescDataRepo(int tenantId, int forecastPeriod);
        Task<List<MRPlanGoalHelper>> GetMRPlanGoalMappingDataRepo(int tenantId, int budgetYear);
        Task<List<MRPlanTargetHelper>> GetMRPlanTargetMappingDataRepo(int tenantId, int budgetYear);
        Task<List<GoalsTargetSubscribedtoPlanHelper>> GetMRPlanGoalSubscribDataRepo(int tenantId, int budgetYear);
        Task<List<GoalsTargetSubscribedtoPlanHelper>> GetMRPlanTgtSubscribDataRepo(int tenantId, int budgetYear);
        Task<List<tco_focusarea>> GetFocusArea(int tenantId, int budgetYear);
        Task<List<gco_un_susdev_goals>> GetUnGoals();
        Task<List<gco_un_susdev_targets>> GetUnTargets();
        Task<List<KeyValueData>> GetActionsTags(int tenantId);
    }
}
