using Framsikt.BL.Helpers;
using Framsikt.BL.Reporting.Helpers;
using Framsikt.Entities;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

#pragma warning disable CS8618
namespace Framsikt.BL.Reporting.Core.Helpers
{
    public class MRAssignmentHelper
    {

    }
    public class ColumnSelectorHelper
    {
        public ColumnSelectorHelper()
        {
            AssignmentColList = new List<KeyValuePair>();
            GoalColList = new List<KeyValuePair>();
        }
        [JsonProperty("assignmentColList")] public List<KeyValuePair> AssignmentColList { get; set; }
        [JsonProperty("goalColList")] public List<KeyValuePair> GoalColList { get; set; }
    }

    public class KeyValuePair
    {
        public string key { get; set; }
        public string value { get; set; }
        public bool isChecked { get; set; }
        public bool isActive { get; set; }
        public int setIndex { get; set; }
        public int orgLevel { get; set; }
        public string orgId { get; set; }
        public decimal co2Factor { get; set; }
        public Guid goalId { get; set; }
        public Guid targetId { get; set; }
        public string forecastPeriod { get; set; }
    }
    public class MonthlyReportBusPlanColSelHelper
    {
        public List<KeyValuePair> selectedColumns { get; set; }
        public int budgetYear { get; set; }
        public bool isTenantSpecificColConfigSave { get; set; }
    }
    public class MRAssignmentGridHelper
    {
        public string monthYear { get; set; }
        public int orgLevel { get; set; }
        public string orgId { get; set; }
        public string orgName { get; set; }
        public string serviceId { get; set; }
        public string serviceName { get; set; }
        public string level1OrgId { get; set; }
        public string level2OrgId { get; set; }
        public string level3OrgId { get; set; }
        public string level4OrgId { get; set; }
        public string level5OrgId { get; set; }
        public string level6OrgId { get; set; }
        public string level7OrgId { get; set; }
        public string level8OrgId { get; set; }
        public OrgInpLevels orgInput { get; set; }
        public string viewType { get; set; }
        public bool isNotEditable { get; set; }
        public string deptCode { get; set; }
        public clsMonRepFinStatusColSelectWrapper finStatusColumnConfig { get; set; }
        public List<KeyValuePair> columnsToDispay { get; set; }
        public bool hideFinished { get; set; }
        public List<BL.Helpers.KeyValuePair> displayColumns { get; set; }
        public string orgVersion { get; set; }
        public int period { get; set; }
        public int budgetYear { get; set; }
        public string attributeId { get; set; }
        public string attributeName { get; set; }
        public bool isServiceSetup { get; set; }
        public bool isChapterSetup { get; set; }
    }
    public class MRAssignmentLinkedObjectsHelper
    {
        public Guid AssignmentId { get; set; }
        public string Goal { get; set; }
        public string UnGoal { get; set; }
        public string Target { get; set; }
        public string UnTarget { get; set; }
        public string UnGoalsConnectedToTarget { get; set; }
        public string FocusArea { get; set; }
        public string Strategy { get; set; }
        public string Categorydescription { get; set; }
        public bool isAssignmentReported { get; set; }
        public string AssignmentTags { get; set; }
        public string OtherInfo { get; set; }
    }
    public class MRDataFetchHelper
    {
        public MRDataFetchHelper()
        {
            liveStatusObject = new MRLiveStatusHelper();
            liveStatusDesc = string.Empty;
            liveStatusdescGuid = Guid.Empty;
        }

        public Guid id { get; set; }
        public Guid uniqueid { get; set; }
        public string goalTag { get; set; }
        public string creatorOrgId { get; set; }
        public string creatorOrgName { get; set; }
        public int creatorOrgLevel { get; set; }
        public string activityName { get; set; }
        public string activityDesc { get; set; }
        public DateTime deadlineDate { get; set; }
        public string responsible { get; set; }
        public int reportedPeriod { get; set; }
        public int status { get; set; }
        public riskStatusKeyValuePair statusObj { get; set; }
        public riskStatusKeyValuePair statusFinObj { get; set; }
        public int risk { get; set; }
        public riskStatusKeyValuePair riskObj { get; set; }
        public string statusdesc { get; set; }
        public string statusDescGUID { get; set; }
        public string category { get; set; }
        public string categoryDescription { get; set; }
        public string externalReference { get; set; }
        public bool isIncludedInMRDoc { get; set; }
        public int owner { get; set; }
        public string ownerName { get; set; }
        public int busplanStatus { get; set; }
        public Guid climateCategoryId { get; set; }
        public string focusArea { get; set; }
        public string goal { get; set; }
        public string unGoal { get; set; }
        public string target { get; set; }
        public string unTarget { get; set; }
        public string unGoalsConnectedToTarget { get; set; }
        public string strategy { get; set; }
        public string serviceName { get; set; }
        public string servicId { get; set; }
        public string attributeId { get; set; }
        public string attributeName { get; set; }
        public int livestatus { get; set; }
        public MRLiveStatusHelper liveStatusObject { get; set; }
        public string liveStatusDesc { get; set; }
        public Guid liveStatusdescGuid { get; set; }
        public string contributors { get; set; }
        public DateTime updated { get; set; }
        public string updated_by { get; set; }
        public string motherPlan { get; set; }
        public List<string> allPlanName { get; set; }
        public int startYear { get; set; }
        public int endYear { get; set; }
        public int startYearBP { get; set; }
        public int statusFinplan { get; set; }
        public int syncStatus { get; set; }
        public string assignmentId { get; set; }
    }
    public class AssignmentFilterHelper
    {
        public List<string> categoryTypeList { get; set; }
        public List<string> categoryList { get; set; }
        public List<string> serviceAreaList { get; set; }
        public List<string> assignmentList { get; set; }
        public List<riskStatusKeyValuePair> statusList { get; set; }
        public List<riskStatusKeyValuePair> riskList { get; set; }
        public List<MRLiveStatusHelper> lievStatusList { get; set; }
        public List<string> deadlineList { get; set; }
        public List<string> responsibleList { get; set; }
        public List<string> contributorsList { get; set; }
        public List<string> motherPlanList { get; set; }
        public List<string> allPlansList { get; set; }
        public List<string> madeFromList { get; set; }
        public List<string> tagsList { get; set; }
        public List<string> strategyList { get; set; }
        public List<string> targetList { get; set; }
        public List<string> unTargetList { get; set; }
        public List<string> unGoalTargetList { get; set; }
        public List<string> goalList { get; set; }
        public List<string> unGoalList { get; set; }
        public List<string> focusAreaList { get; set; }

        public List<string> categoryDescription { get; set; }
        public List<string> startYearList { get; set; }
        public List<string> endYearList { get; set; }
        public List<string> startYearBPList { get; set; }
        public List<riskStatusKeyValuePair> statusFinplanList { get; set; }
        public List<string> assignmentRefIdList { get; set; }
        public List<string> referenceList { get; set; }
        public List<string> ownerList { get; set; }

    }
    public class riskStatusKeyValuePair
    {
        public int key { get; set; }
        public string value { get; set; }
    }
    public class AssignmentListHelper
    {
        public string assignmentId { get; set; }
        public int budgetYear { get; set; }
        public int forecastPeriod { get; set; }
        public string orgId { get; set; }
        public string orgLevel { get; set; }
        public string serviceId { get; set; }
        public bool isChapterSetup { get; set; }
    }
    public class AssignmentDetailInfoHelper
    {
        public string assignmentName { get; set; }
        public Guid uniqueAssignmentId { get; set; }
        public JArray category { get; set; }
        public string categorySet { get; set; }
        public JArray tags { get; set; }
        public string tagsSet { get; set; }
        public JArray goals { get; set; }
        public string goalsSet { get; set; }
        public JArray targets { get; set; }
        public string targetSet { get; set; }
        public JArray strategy { get; set; }
        public string strategySet { get; set; }
        public JArray focusArea { get; set; }
        public string focusAreaSet { get; set; }
        public dynamic mouseHoverDesc { get; set; }
        public string externalReference { get; set; }
        public string parentassignmentendDate { get; set; }
        public int user { get; set; }
        public int assignmentOwner { get; set; }
        public string assignmentOwnerName { get; set; }
        public string responsibleuser { get; set; }
        public string assignmentrefid { get; set; }
        public string OrgCreatedAt { get; set; }
        public int status { get; set; }
        public int risk { get; set; }
        public string textdescription { get; set; }
        public string textdescriptionid { get; set; }
        public int liveStatus { get; set; }
        public string liveStatusDesc { get; set; }
        public Guid liveStatusdescGuid { get; set; }
        public List<FinplanBusplanDescHelper> busplanDesc { get; set; }
        public List<FinplanBusplanDescHelper> finplanDesc { get; set; }
        public FinplanBusplanDescHelper busplanDescOrgLevel { get; set; }
        public FinplanBusplanDescHelper finplanDescOrgLevel { get; set; }
        public JArray statusDropdown { get; set; }
        public JArray riskDropdown { get; set;}
        public string deadline { get; set; }
        public string contributor { get; set; }
        public int startYear { get; set; }
        public int endYear { get; set; }
        public int startYearBP { get; set; }
        public int statusFinplan { get; set; }

    }
    public class unGoalTargetHelper
    {
        public string formattedName { get; set; }
        public string shortName { get; set; }
    }
    public class FinplanBusplanDescHelper
    {
        public string descriptionId { get; set; } = "";
        public string description { get; set; } = "";
        public string descriptionName { get; set; } = "";
        public Guid? fkAssignmentId { get; set; }
    }

    public class FetchDescHelper
    {
        public int forecastPeriod { get; set; }
        public string orgId { get; set; }
        public OrgInpLevels orgInput { get; set; }
        public int orgLevel { get; set; }
        public string serviceId { get; set; }
        public int budgetYear { get; set; }
        public string nodeId { get; set; }
        public string mappingId { get; set; }

    }

    public class belowLevelDescHelper
    {
        public string id { get; set; }
        public string parentId { get; set; }
        public string orgName { get; set; }
        public string assignmentDesc { get; set; }
        public string taskDesc { get; set; }
        public string task { get; set; }
        public string taskStatus { get; set; }
        public string assignmentStatus { get; set; }
        public string orgId { get; set; }
        public int orgLevel { get; set; }
        public bool isAssignDescChecked { get; set; }
        public bool isTaskDescChecked { get; set; }
        public string serviceId { get; set; }
        public string assignmentId { get; set; }
        public string taskId { get; set; }
        public bool isDisableAssignCheckbox { get; set; }
        public bool isCurrentLevel { get; set; }
        public bool isExpanded { get; set; }
        public string sortOrder { get; set; }

    }
    public class LoadCopiedDescHelper
    {
        public string orgId { get; set; }
        public int forecastPeriod { get; set; }
        public int budgetYear { get; set; }
        public IEnumerable<SelectedRowsHelper> selectMatrix { get; set; }
    }
    public class SelectedRowsHelper
    {
        public int orgLevel { get; set; }
        public string orgId { get; set; }
        public string orgName { get; set; }
        public string serviceId { get; set; }
        public string assignmentId { get; set; }
        public string taskId { get; set; }
        public string taskName { get; set; }
        public bool isAssignDescChecked { get; set; }
        public bool isTaskDescChecked { get; set; }
        public string sortOrder { get; set; }
    }

    public class SaveDescHelper
    {
        public string orgId { get; set; }
        public int orgLevel { get; set; }
        public int forecastPeriod { get; set; }
        public string assignmentId { get; set; }
        public string assignmentDesc { get; set; }
        public bool logHistory { get; set; }

    }
    public class taskInfo
    {
        public string taskId { get; set; }
        public string taskName { get; set; }
        public string taskDesc { get; set; }
        public int status { get; set; }
    }

    public class MRTasksGetInputHelper
    {
        public int budgetYear { get; set; }
        public int forecastPeriod { get; set; }
        public int orgLevel { get; set; }
        public string orgId { get; set; }
        public string serviceId { get; set; }
        public bool isServiceSetup { get; set; }
        public List<KeyValuePair> displayColumns { get; set; }
        public bool isAngular { get; set; }
    }

    public class MRTasksGridDataHelper
    {
        public Guid id { get; set; }
        public string category { get; set; }
        public string assignment { get; set; }
        public string taskName { get; set; }
        public string createdAt { get; set; }
        public string description { get; set; }
        public string deadline { get; set; }
        public string responsible { get; set; }
        public string status { get; set; }
        public object statusObj { get; set; } 
        public string statusdesc { get; set; }
        public Guid statusDescId { get; set; } 
        public string statusDescPreview { get; set; }
        public string servicename { get; set; }

    }

    public class DelegatedAssignInputHelper
    {
        public int forecastPeriod { get; set; }
        public string orgId { get; set; }
        public int orgLevel { get; set; }
        public string serviceId { get; set; }
        public int budgetYear { get; set; }
        public string uniqueAssignmentId { get; set; }
        public bool isCurrentLevelSection { get; set; }

    }
    public class DelegatedAssignHelper
    {
        public string orgName { get; set; }
        public string assignmentDesc { get; set; }
        public string assignmentStatus { get; set; }
        public string assignmentComments { get; set; }
        public string task { get; set; }
        public string taskDesc { get; set; }
        public string taskStatus { get; set; }
        public string taskComments { get; set; }
        public string id { get; set; }
        public string parentId { get; set; }
        public int assignStatusKey { get; set; }
        public int taskStatusKey { get; set; }
        public bool isTaskAdded { get; set; }
        public bool isExpanded { get; set;}

    }

}
