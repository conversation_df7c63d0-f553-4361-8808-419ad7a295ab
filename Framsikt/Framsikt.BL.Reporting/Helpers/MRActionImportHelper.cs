#pragma warning disable <PERSON><PERSON>18

using Framsikt.BL.Helpers;
using Framsikt.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Framsikt.BL.Reporting.Helpers
{
    public class MRActionImportHelper
    {
        public List<tmr_stage_action_import> Data { get; set; }
        public int TotalCount { get; set; }
    }

    public class MrActionImportData
    {
        public long pk_id { get; set; }

        public int fk_tenant_id { get; set; }

        public int user_id { get; set; }

        public int forecast_period { get; set; }

        public int budget_year { get; set; }

        public int? action_id { get; set; }

        public string action_title { get; set; }
        public string account_code { get; set; }
        public string department_code { get; set; }
        public string function_code { get; set; }
        public string project_code { get; set; }
        public string free_dim_1 { get; set; }
        public string free_dim_2 { get; set; }
        public string free_dim_3 { get; set; }
        public string free_dim_4 { get; set; }

        public decimal? year_1_amount { get; set; }

        public decimal? year_2_amount { get; set; }

        public decimal? year_3_amount { get; set; }

        public decimal? year_4_amount { get; set; }

        public decimal? year_5_amount { get; set; }
        public string alter_code { get; set; }
        public string description { get; set; }
        public string periodicKey { get; set; }
        public string adjustment_code { get; set; }

        public bool? action_id_error { get; set; }

        public bool? action_title_error { get; set; }

        public bool? account_code_error { get; set; }

        public bool? department_code_error { get; set; }

        public bool? function_code_error { get; set; }

        public bool? project_code_error { get; set; }

        public bool? free_dim_1_error { get; set; }

        public bool? free_dim_2_error { get; set; }

        public bool? free_dim_3_error { get; set; }

        public bool? free_dim_4_error { get; set; }

        public bool? adjustment_code_error { get; set; }

        public bool? alter_code_error { get; set; }

        public bool? year_1_amount_error { get; set; }

        public bool? year_2_amount_error { get; set; }

        public bool? year_3_amount_error { get; set; }

        public bool? year_4_amount_error { get; set; }

        public bool? year_5_amount_error { get; set; }

        public bool? periodicKey_error { get; set; }

        public int? error_count { get; set; }

        public int action_type { get; set; }

        public int? line_order { get; set; }

        public int change_id { get; set; }

        public int isManuallyAdded { get; set; }
    }

    public class MRImportInput
    {
       public DateTime monthYear { get; set; }
        public decimal adjustment { get; set; }
        public int valueFactor { get; set; }
        public List<KeyValueData> tagsData { get; set; }
    }
}
