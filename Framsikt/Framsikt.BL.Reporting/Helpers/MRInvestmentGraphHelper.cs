#pragma warning disable CS8618

namespace Framsikt.BL.Reporting.Helpers
{
    public class MRInvestmentGraphHelper
    {
        public string OrgId { get; set; }
        public string OrgName { get; set; }
        public string ServiceId { get; set; }
        public string ServiceName { get; set; }
        public string Type { get; set; }
        public string PkId { get; set; }
        public string Investment { get; set; }
        public string ApprovalReference { get; set; }
        public string ApprovalReferenceUrl { get; set; }
        public int FinishedYear { get; set; }
        public int EstimatedQuarter { get; set; }
        public int Status { get; set; }
        public int Risk { get; set; }
        public string StatusReview { get; set; }
        public bool DisplayNextLevel { get; set; }
        public string InvestmentStatus { get; set; }

        // Amount Related Properties
        public decimal Accounting { get; set; }
        public decimal AccountingTotal { get; set; }
        public decimal ActualAmtLastYear { get; set; }
        public decimal AdjustedBudget { get; set; }
        public decimal BudgetAcctDeviation { get; set; }
        public decimal Changetotalamount { get; set; }
        public decimal ConsumePercentage { get; set; }
        public decimal CostApproval { get; set; }
        public decimal Deviation { get; set; }
        public decimal ForecastAmount { get; set; }
        public decimal ForecastDeviation { get; set; }
        public decimal PlannedAmount { get; set; }
        public decimal Regulation { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal TotalYbUbcBudget { get; set; }
        public decimal UnApprvBudChange { get; set; }
        public decimal ProposedAdjustment { get; set; }
        public decimal BudChangeYear1 { get; set; }
        public decimal BudChangeYear2 { get; set; }
        public decimal BudChangeYear3 { get; set; }
        public decimal BudChangeYear4 { get; set; }
        public decimal BudChangeYearTotal { get; set; }

        public int BudgetYear { get; set; }
        public int TenantId { get; set; }
        public string InvestmentPhase { get; set; }
        public string Oe_flag { get; set; }
        public string QualityStatusDesc { get; set; }
        public string FinStatusDesc { get; set; }
    }
}
