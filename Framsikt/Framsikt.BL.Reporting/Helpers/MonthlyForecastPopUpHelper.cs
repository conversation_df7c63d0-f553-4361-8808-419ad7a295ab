#pragma warning disable CS8618

using System;
using Framsikt.BL.Helpers;
using System.Collections.Generic;

namespace Framsikt.BL.Reporting.Helpers
{
    public class MonthlyForecastPopUpHelper
    {
        public int TenantId { get; set; }
        public int ForecastPeriod { get; set; }
        public int BudgetYear { get; set; }
        public string ProcessType { get; set; }
        public int OrgLevel { get; set; }
        public string OrgId { get; set; }
        public int ServiceLevel { get; set; }
        public string ServiceId { get; set; }
        public int WfStatus { get; set; }
        public List<KeyValueData> Tags { get; set; }
        public DateTime? deadlineDate { get; set; }
        public bool returnSubUnits { get; set; }
        public OrgInpLevels orgInput { get; set; }
    }
}
