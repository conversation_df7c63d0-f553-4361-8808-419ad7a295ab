#pragma warning disable CS8618

namespace Framsikt.BL.Reporting.Helpers
{
    public class AssignmentbyCategoryHelper
    {
        public string UniqueId { get; set; }
        public string AssignmentName { get; set; }
        public string AssignmentDescription { get; set; }
        public string AssignmentOwner { get; set; }
        public string AssignmentResponsible { get; set; }
        public int StatusId { get; set; }
        public string StatusName { get; set; }
        public int RiskId { get; set; }
        public string RiskName { get; set; }
        public string StatusDescription { get; set; }
        public string OrgId { get; set; }
        public int? OrgLevel { get; set; }
        public string OrgName { get; set; }
        public string ExternalReference { get; set; }
        public string GoalId { get; set; }
        public string GoalName { get; set; }
        public string TargetId { get; set; }
        public string TargetName { get; set; }
        public string UNSGoalId { get; set; }
        public string UNSGoalName { get; set; }
        public string UNSTargetId { get; set; }
        public string UNSTargetName { get; set; }
        public string UNSGoalTargetId { get; set; }
        public string UNSGoalTargetName { get; set; }
        public int StartYear { get; set; }
        public int EndYear { get; set; }
        public int StartYearBP { get; set; }
        public int StatusFinplan { get; set; }
        public string StatusFinplanName { get; set; }
    }
}