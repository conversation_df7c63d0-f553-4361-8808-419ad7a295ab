using Framsikt.BL.Helpers;
using Framsikt.BL.Reporting.Repository;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Microsoft.Extensions.DependencyInjection;

namespace Framsikt.BL.Reporting
{
    public class SyncMonthlyReportAssignment : ISyncMonthlyReportAssignment
    {
        private readonly IDataSyncUtility _dataSyncUtility;
        private readonly IReportingUoW _rUnitOfWork;
        private readonly IUtility _utility;
        private readonly IUnitOfWork _unitOfWork;

        public SyncMonthlyReportAssignment(IServiceProvider container)
        {
            _dataSyncUtility = container.GetRequiredService<IDataSyncUtility>();
            _rUnitOfWork = container.GetRequiredService<IReportingUoW>();
            _utility = container.GetRequiredService<IUtility>();
            _unitOfWork = container.GetRequiredService<IUnitOfWork>();
        }

        public async Task<bool> FetchMRAssignments(MRSyncFetchtHelper fetchObj)
        {
            try
            {
                // Get Data to transfer
                var mainTenantAssignAsync = _rUnitOfWork.MonthlyReportSyncRepository.GetSyncReportedAssignment(fetchObj.targetTenantId, fetchObj.forecastPeriod);
                var subTenantAssignAsync = _rUnitOfWork.MonthlyReportSyncRepository.GetSyncReportedAssignment(fetchObj.sourceTenantId, fetchObj.forecastPeriod);
                var mainTenantAssignIdAsync = _rUnitOfWork.MonthlyReportSyncRepository.GetSyncMappedAssignments(fetchObj.budgetYear, fetchObj.targetTenantId);

                await Task.WhenAll(mainTenantAssignAsync, subTenantAssignAsync, mainTenantAssignIdAsync);

                var mainTenantAssign = mainTenantAssignAsync.Result;
                var subTenantAssign = subTenantAssignAsync.Result;
                var mainTenantAssignId = mainTenantAssignIdAsync.Result;

                foreach (var item in subTenantAssign)
                {
                    var mappedId = mainTenantAssignId.Any(x => x.Value == item.assignmentId) ? mainTenantAssignId.FirstOrDefault(x => x.Value == item.assignmentId).Key : Guid.Empty;
                    if (mappedId != Guid.Empty)
                    {
                        var assignData = mainTenantAssign.FirstOrDefault(x => x.assignmentId == mappedId);
                        if (assignData != null)
                        {
                            assignData.risk = item.risk;
                            assignData.description = item.description;
                            assignData.status = item.status;
                            assignData.updated = DateTime.UtcNow;
                            assignData.updatedby = fetchObj.userId;
                            _rUnitOfWork.GenericRepo.Update(assignData);
                        }
                        else
                        {
                            tbiassignmentmonthlystatus newRecord = new tbiassignmentmonthlystatus
                            {
                                tenantId = fetchObj.targetTenantId,
                                forecastPeriod = fetchObj.forecastPeriod,
                                assignmentId = mappedId,
                                updated = DateTime.UtcNow,
                                updatedby = fetchObj.userId,
                                isIncludedInMRDoc = false,
                                status = item.status,
                                risk = item.risk,
                                description = item.description,
                            };
                            _rUnitOfWork.GenericRepo.Add(newRecord);
                            await _rUnitOfWork.CompleteAsync();

                        }
                        string historyStatusText = item.description ?? string.Empty;
                        await _utility.SaveTextLogAsync(fetchObj.userIdName, mappedId, historyStatusText);
                    }
                }
                return true;
            }
            catch (Exception)
            {
                return false;
            }

        }

        public async Task<bool> FetchAssignmentDescription(MRSyncFetchtHelper fetchObj, bool chapterSetup)
        {
            try
            {
                var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(fetchObj.userIdName, _utility.GetForecastPeriod(fetchObj.budgetYear, 1));
                string mainTenantOrgVersion = orgVersionContent.orgVersion;
                bool isFeatureFlagEnabled = await _utility.IsFeatureEnabled(FeatureFlags.monthRep_Text);
                string subTenantOrgVersion = _utility.GetOrgVersionBasedOnTenant(fetchObj.sourceTenantId, _utility.GetForecastPeriod(fetchObj.forecastPeriod / 100, 1)).orgVersion;
                string subTenantOrgId = await _unitOfWork.BudgetTransferRepository.GetOrgIdForSubTenantAsync(fetchObj.sourceTenantId, subTenantOrgVersion);
                List<string> listSubOrgId = new List<string> { subTenantOrgId };

                var orgData = await _unitOfWork.BudgetProposalSyncDocRepository.GetOrgIdsForTransfer(fetchObj.targetTenantId, fetchObj.orgId, mainTenantOrgVersion, fetchObj.orgLevel);
                List<string> lstOrgId3 = orgData.Where(x => x.subTenantId == fetchObj.sourceTenantId).Select(x => x.orgId3).ToList();


                if (isFeatureFlagEnabled)
                {
                    var subTenantDescList = await _rUnitOfWork.MonthlyReportSyncRepository.GetSyncMRUpdatedDescriptions(fetchObj.sourceTenantId, fetchObj.forecastPeriod, 1, listSubOrgId);
                    var mainTenantDescList = await _rUnitOfWork.MonthlyReportSyncRepository.GetSyncMRUpdatedDescriptions(fetchObj.targetTenantId, fetchObj.forecastPeriod, 3, lstOrgId3);

                    subTenantDescList = subTenantDescList.Where(x => x.description_type == nameof(clsConstants.MonthlyReportTextBoxType.BusPlanStatusDesc)).ToList();
                    mainTenantDescList = mainTenantDescList.Where(x => x.description_type == nameof(clsConstants.MonthlyReportTextBoxType.BusPlanStatusDesc)).ToList();

                    foreach(var item in subTenantDescList)
                    {
                        tco_monthrep_descriptions dataToUpdate = null;
                        if (item?.description_history != null && item.description_text != null)
                        {
                            dataToUpdate = chapterSetup ? mainTenantDescList.FirstOrDefault(x => x.org_id == item.fk_attribute_id) : mainTenantDescList.FirstOrDefault();

                            if (dataToUpdate is null && !string.IsNullOrEmpty(item.description_text))
                            {
                                var newDescData =  _dataSyncUtility.CreateMRDescriptionObject(fetchObj, item, chapterSetup, lstOrgId3);
                                await _unitOfWork.GenericRepo.AddAsync(newDescData);
                                await _unitOfWork.CompleteAsync();
                                await _utility.SaveTextLogAsync(fetchObj.userIdName, item.description_history, item.description_text);

                            }
                            else if (dataToUpdate != null && !string.IsNullOrEmpty(item.description_text))
                            {
                                dataToUpdate.description_text = item.description_text;
                                if(dataToUpdate?.description_history != null )
                                    dataToUpdate.description_history = item.description_history;

                                dataToUpdate.updated = DateTime.UtcNow;
                                dataToUpdate.updated_by = fetchObj.userId;

                                _rUnitOfWork.GenericRepo.Update(dataToUpdate);
                                await _rUnitOfWork.CompleteAsync();

                                string assignText = item.description_text ?? string.Empty;
                                await _utility.SaveTextLogAsync(fetchObj.userIdName, dataToUpdate.description_history, assignText);
                            }
                        }
                    }

                }
                else
                {
                    var subTenantDescList = await _rUnitOfWork.MonthlyReportSyncRepository.GetSyncMRDescriptions(fetchObj.sourceTenantId, fetchObj.forecastPeriod, 1, listSubOrgId);
                    var mainTenantDescList = await _rUnitOfWork.MonthlyReportSyncRepository.GetSyncMRDescriptions(fetchObj.targetTenantId, fetchObj.forecastPeriod, 3, lstOrgId3);

                    if (chapterSetup)
                    {
                        foreach (var item in subTenantDescList)
                        {
                            if (item.busplan_status_desc != null && item.busplan_status_history != null)
                            {
                                var dataToUpdate = mainTenantDescList.FirstOrDefault(x => x.org_id == item.fk_attribute_id);
                                if (dataToUpdate is not null)
                                {
                                    dataToUpdate.busplan_status_desc = item.busplan_status_desc;
                                    if (dataToUpdate.busplan_status_history == null)
                                    {
                                        dataToUpdate.busplan_status_history = Guid.NewGuid();
                                    }
                                    dataToUpdate.updated = DateTime.UtcNow;
                                    dataToUpdate.updated_by = fetchObj.userId;

                                    _rUnitOfWork.GenericRepo.Update(dataToUpdate);
                                    await _rUnitOfWork.CompleteAsync();

                                    string assignText = item.busplan_status_desc ?? string.Empty;
                                    await _utility.SaveTextLogAsync(fetchObj.userIdName, dataToUpdate.busplan_status_history.Value, assignText);

                                }
                            }
                        }
                    }
                    else
                    {
                        var dataToUpdate = mainTenantDescList.FirstOrDefault();
                        var subTenantDescData = subTenantDescList.FirstOrDefault();

                        if (dataToUpdate is not null && subTenantDescData is not null && subTenantDescData.busplan_status_desc is not null)
                        {
                            dataToUpdate.busplan_status_desc = subTenantDescData?.busplan_status_desc ?? string.Empty;
                            if (dataToUpdate.busplan_status_history == null)
                            {
                                dataToUpdate.busplan_status_history = Guid.NewGuid();
                            }
                            dataToUpdate.updated = DateTime.UtcNow;
                            dataToUpdate.updated_by = fetchObj.userId;

                            _rUnitOfWork.GenericRepo.Update(dataToUpdate);
                            await _rUnitOfWork.CompleteAsync();

                            string assignText = subTenantDescData?.busplan_status_desc ?? string.Empty;
                            await _utility.SaveTextLogAsync(fetchObj.userIdName, dataToUpdate.busplan_status_history.Value, assignText);

                        }
                    }
                }
                return true;
            }
            catch(Exception)
            {
                return false;
            }
        }
    }
}
