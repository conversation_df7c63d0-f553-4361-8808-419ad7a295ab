using System.Collections.Generic;

namespace Framsikt.BL.Reporting
{
    public interface IAccountActivity
    {
        Dictionary<string, string> GetPageTitles(string userId);

        Task<Dictionary<string, string>> GetPageTitlesAsync(string userId);

        Task<dynamic> GetAcActvFiltersData(string userName,
                                            int accountPeriod,
                                            int accountYear,
                                            int orgLevel,
                                            string orgId,
                                            string level1OrgId,
                                            string level2OrgId,
                                            string level3OrgId,
                                            string level4OrgId,
                                            string level5OrgId);

        Task<dynamic> GetAccActiveUserGrData(string userName,
                                       int accountYear,
                                       int accountPeriod,
                                       int orgLevel,
                                       string orgId,
                                       string orgName,
                                       string serviceId,
                                       string level1OrgId,
                                       string level2OrgId,
                                       string level3OrgId,
                                       string level4OrgId,
                                       string level5OrgId,
                                       string filterOrg,
                                       List<string> filterServ);

        Task<dynamic> GetAccActiveHorsGrData(string userName,
                                       int accountYear,
                                       int accountPeriod,
                                       int orgLevel,
                                       string orgId,
                                       string orgName,
                                       string serviceId,
                                       string level1OrgId,
                                       string level2OrgId,
                                       string level3OrgId,
                                       string level4OrgId,
                                       string level5OrgId,
                                       string filterOrg,
                                       List<string> filterServ);
    }
}
