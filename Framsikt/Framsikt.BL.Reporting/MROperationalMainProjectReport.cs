#pragma warning disable CS8600
#pragma warning disable CS8602
#pragma warning disable CS8604
#pragma warning disable CS8629


using Framsikt.BL.Helpers;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using static Framsikt.BL.Helpers.clsConstants;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

using Framsikt.BL.Reporting.Repository;
using Framsikt.BL.Reporting.Helpers;
using Microsoft.Azure.Amqp.Framing;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using KeyValuePair = Framsikt.BL.Helpers.KeyValuePair;

namespace Framsikt.BL.Reporting.Core
{
    public class MROperationalMainProjectReport : IMROperationalMainProjectReport
    {
        private readonly IUtility _utility;
        private readonly IReportingUoW _rUow;
        private readonly IAccountingInfoAdmin _adminAccInfo;
        private readonly IMonthlyReportForecastUtility _mrfUtility;
        public MROperationalMainProjectReport(IServiceProvider container)
        {
            _utility = container.GetRequiredService<IUtility>();
            _adminAccInfo = container.GetRequiredService<IAccountingInfoAdmin>();
            _rUow = container.GetRequiredService<IReportingUoW>();
            _mrfUtility = container.GetRequiredService<IMonthlyReportForecastUtility>();
        }
            // this function returns and obejct with two list one with the project level eg Main project, Project etc and the other operational project type
        public async Task<projectLevelAndType> GetProjectLevelAndTypeList(string userName)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userName);
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "MonthlyReport");
       
          
            projectLevelAndType data = new projectLevelAndType();
           
            KeyValuePairString temp;
            List<string> OPProjectLevel = Enum.GetNames(typeof(OperationalProjectLevel)).ToList();
            foreach (var item  in OPProjectLevel)
            {
                 temp = new KeyValuePairString() { 
                  Key=  SetOPProjectLevelKey(item),
                  Value= langStrings["MR_Operational_Proj_Level_"+ item].LangText
                 };

                data.projectLevelList.Add(temp);
            }
            var projectTypesList = await _adminAccInfo.GetOperationalProjectType(userDetails.tenant_id);
            projectTypesList.Insert(0, new KeyValueInt { Key = -1, Value = langStrings["MR_ServIdSelectAllText"].LangText });
            data.projectTypeList = projectTypesList;
           
            return data;
        }

        
        //this function returns list of data(e.g. OperationalMainproject,....) based on the selecton of dropdowns projectlevel and projectType
        public async Task<IEnumerable<KeyValueDataPair>> GetOperationalDropdownData(string userName, OperationalGridInputHelper input)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userName);        

             var   OrgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userName, input.forecastePeriod);
            var negateDeviation = await _utility.CheckMonthlyReportDeviationNegateAsync(userDetails.user_name, "TRUE");
            var data = await GetAndFormatOperationalGridData(userDetails, input, negateDeviation); //await _rUow.MROperationalMainProjectReportRepository.GetOperationalMPRepoData(userDetails.tenant_id, input, OrgVersionContent.orgVersion);

            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "MonthlyReport");

            List<string>DistinctMpCodes= data.Where(z=>string.IsNullOrEmpty(z.parentId)).Select(z=>z.mpCode).Distinct().ToList();

            List<KeyValueDataPair> finalData = data.Where(z => string.IsNullOrEmpty(z.parentId)).Select(z=> new KeyValueDataPair() { Key=z.mpCode,Value=(z.mpName) }).DistinctBy(x=>x.Key).ToList();

            finalData = finalData.OrderBy(z => z.Key).ToList();
            if (finalData.Any())
            {
                finalData.Insert(0,new KeyValueDataPair() { Key = "-1", Value = langStrings["MR_ServIdSelectAllText"].LangText });
            }

            return finalData;
        }


        //this function returns list of data in tree tree structure Operational main project ->project->account
        public async Task<IEnumerable<OperationalMPGridData>> GetOperationalGridData(string userName, OperationalGridInputHelper input)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userName);
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "MonthlyReport");
        
            var negateDeviation = await _utility.CheckMonthlyReportDeviationNegateAsync(userDetails.user_name, "TRUE");

           var Data = await GetAndFormatOperationalGridData(userDetails,  input, negateDeviation);

            List<OperationalMPGridData> finalData = Data.ToList();

            OperationalMPGridData totalRow = new OperationalMPGridData()
            {
                id = "-1",
                mpName = langStrings["MR_total_text"].LangText,                
                mpCode = "-1",
                //incomeFlag = c.Key.income_flag,
                acountingYtd = finalData.Where(z=> string.IsNullOrEmpty(z.parentId)).Sum(x => x.acountingYtd),
                deviationYtdKr = finalData.Where(z => string.IsNullOrEmpty(z.parentId)).Sum(x => x.deviationYtdKr),
                devationYtdPerc = finalData.Where(z => string.IsNullOrEmpty(z.parentId)).Sum(x => x.revisedBudgetYtd) != 0 ? Math.Round((finalData.Where(z => string.IsNullOrEmpty(z.parentId)).Sum(x => x.deviationYtdKr) / finalData.Where(z => string.IsNullOrEmpty(z.parentId)).Sum(x => x.revisedBudgetYtd)) , 3) : 0,
                originalBudget = finalData.Where(z => string.IsNullOrEmpty(z.parentId)).Sum(x => x.originalBudget),
                revisedBudgetYtd = finalData.Where(z => string.IsNullOrEmpty(z.parentId)).Sum(x => x.revisedBudgetYtd),
                sumBudgetChanges = finalData.Where(z => string.IsNullOrEmpty(z.parentId)).Sum(x => x.sumBudgetChanges),
                revisedBudget = finalData.Where(z => string.IsNullOrEmpty(z.parentId)).Sum(x => x.revisedBudget),
                notApprBudChng = finalData.Where(z => string.IsNullOrEmpty(z.parentId)).Sum(x => x.notApprBudChng),
                revBudgetInclNotApprBudChng = finalData.Where(z => string.IsNullOrEmpty(z.parentId)).Sum(x => x.revBudgetInclNotApprBudChng),
                propForecast = finalData.Where(z => string.IsNullOrEmpty(z.parentId)).Sum(z => z.propForecast),
                forecastChange = finalData.Where(z => string.IsNullOrEmpty(z.parentId)).Sum(z => z.forecastChange),
                defaultForecastChange = finalData.Where(z => string.IsNullOrEmpty(z.parentId)).Sum(z => z.forecastChange),
                newForecast = finalData.Where(z => string.IsNullOrEmpty(z.parentId)).Sum(z => z.newForecast),
                defaultNewForecast = finalData.Where(z => string.IsNullOrEmpty(z.parentId)).Sum(z => z.newForecast),
                deviationKr = (finalData.Where(z => string.IsNullOrEmpty(z.parentId)).Sum(z => z.revBudgetInclNotApprBudChng) - finalData.Where(z => string.IsNullOrEmpty(z.parentId)).Sum(x => x.newForecast)),
                deviationPerc = Math.Abs(finalData.Where(z => string.IsNullOrEmpty(z.parentId)).Sum(x => x.revBudgetInclNotApprBudChng)) < mrBudgetBlankLimitAmount ? 0 : (Math.Round((finalData.Where(z => string.IsNullOrEmpty(z.parentId)).Sum(z => z.revBudgetInclNotApprBudChng) - finalData.Where(z => string.IsNullOrEmpty(z.parentId)).Sum(x => x.newForecast)) / (finalData.Where(z => string.IsNullOrEmpty(z.parentId)).Sum(x => x.revBudgetInclNotApprBudChng)), 3)),
                changePreviousForecast= finalData.Where(z => string.IsNullOrEmpty(z.parentId)).Sum(z => z.changePreviousForecast),
                defaultChangePreviousForecast = finalData.Where(z => string.IsNullOrEmpty(z.parentId)).Sum(z => z.changePreviousForecast),
                hasChildren = false
            };
            if (finalData.Any())
            {
                finalData.Add(totalRow);
            }
            return finalData;
        }

        // this methode formats the data in tree structure, the fuction checks if allzero and exclude it from the list
        protected List<OperationalMPGridData> FormatGridData(string userName,IEnumerable<OperationalMPRepoData> data,bool negateDeviation,IEnumerable<tmr_proj_operations_status> statusDesc,Dictionary<string, string> deviationsKey, int forecastePeriod)
        {
            FsTrafLiType trafLiType = _mrfUtility.GetFsTrafficLightType(userName, forecastePeriod);
            List<string> distinctMpCodes = data!=null && data.Any() ? data.Select(z => z.mpCode).Distinct().OrderBy(z => z).ToList() : new List<string>();
            List<OperationalMPGridData> finalData = new List<OperationalMPGridData>();

            foreach (var mp in distinctMpCodes)
            {
                var mpData = data.Where(z => z.mpCode == mp);
                var distinctProjCodes = mpData.Select(z => z.projectCode).Distinct().OrderBy(z => z).ToList();
                int id = 1;
                foreach (var proj in distinctProjCodes)
                {
                    var projData = mpData.Where(z => z.projectCode == proj);

                    //last level item row
                    var accountRows = projData.GroupBy(z => new { z.projectCode, z.accountCode, z.accountName })
                        .Select(c => new OperationalMPGridData
                        {
                            id = "LastChild_" + proj + "_" + c.Key.accountCode,
                            mpName = $"{c.Key.accountCode} {c.Key.accountName}",
                            mpCode = c.Key.accountCode,
                            projectCode = c.Key.projectCode,
                            parentId = "SecondChild_" + proj,
                            acountingYtd = c.Sum(x => x.acountingYtd),
                            deviationYtdKr = c.Sum(x => x.deviationYtdKr),
                            devationYtdPerc = c.Sum(x => x.revisedBudgetYtd) != 0 ? Math.Round((c.Sum(x => x.deviationYtdKr) / c.Sum(x => x.revisedBudgetYtd)), 3) : 0,
                            originalBudget = c.Sum(x => x.originalBudget),
                            revisedBudgetYtd = c.Sum(x => x.revisedBudgetYtd),
                            sumBudgetChanges = c.Sum(x => x.sumBudgetChanges),
                            revisedBudget = c.Sum(x => x.revisedBudget),
                            notApprBudChng = c.Sum(x => x.notApprBudChng),
                            revBudgetInclNotApprBudChng = c.Sum(x => x.revBudgetInclNotApprBudChng),
                            propForecast = c.Sum(x => x.sumG),
                            defaultForecastChange = c.Sum(x => x.sumFr),
                            forecastChange = c.Sum(x => x.sumFr),
                            newForecast = c.Sum(x => x.sumM),
                            defaultNewForecast = c.Sum(x => x.sumM),
                            deviationKr =  (c.Sum(x => x.revBudgetInclNotApprBudChng) - c.Sum(x => x.sumM)),
                            deviationPerc = Math.Abs(c.Sum(x => x.revBudgetInclNotApprBudChng)) < mrBudgetBlankLimitAmount ? 0 : (Math.Round((c.Sum(x => x.revBudgetInclNotApprBudChng) - c.Sum(x => x.sumM)) / (c.Sum(x => x.revBudgetInclNotApprBudChng)), 3)),
                            hasChildren = false,
                            statusDesc = "-",
                            isAccountRow = true,
                            isChangeForecastEdit = false,
                            statusDeviation = GetDeviationData(c, deviationsKey, negateDeviation, trafLiType),
                            changePreviousForecast = c.Sum(x => x.acmChgFc),
                            defaultChangePreviousForecast = c.Sum(x => x.acmChgFc)
                        }).ToList();

                    if (accountRows.Any(row => !IsZeroRow(row)))// check if any of the lowest level item is non zero
                    {
                      
                        //second level item row
                        var projectRow = projData.GroupBy(z => new { z.projectCode,z.projectName }).Select(c=> new OperationalMPGridData
                        {
                            id = "SecondChild_" + proj,
                            mpName = $"{c.First().projectName}",
                            mpCode = proj,
                            parentId = mp.ToString(),
                            acountingYtd = c.Sum(x => x.acountingYtd),
                            deviationYtdKr = c.Sum(x => x.deviationYtdKr),
                            devationYtdPerc = c.Sum(x => x.revisedBudgetYtd) != 0 ? Math.Round((c.Sum(x => x.deviationYtdKr) / c.Sum(x => x.revisedBudgetYtd)), 3) : 0,
                            originalBudget = c.Sum(x => x.originalBudget),
                            revisedBudgetYtd = c.Sum(x => x.revisedBudgetYtd),
                            sumBudgetChanges = c.Sum(x => x.sumBudgetChanges),
                            revisedBudget = c.Sum(x => x.revisedBudget),
                            notApprBudChng = c.Sum(x => x.notApprBudChng),
                            revBudgetInclNotApprBudChng = c.Sum(x => x.revBudgetInclNotApprBudChng),
                            propForecast = c.Sum(x => x.sumG),
                            forecastChange = c.Sum(x => x.sumFr),
                            defaultForecastChange = c.Sum(x => x.sumFr),
                            newForecast = c.Sum(x => x.sumM),
                            defaultNewForecast = c.Sum(x => x.sumM),
                            deviationKr = (c.Sum(x => x.revBudgetInclNotApprBudChng) - c.Sum(x => x.sumM)),
                            deviationPerc = Math.Abs(c.Sum(x => x.revBudgetInclNotApprBudChng)) < mrBudgetBlankLimitAmount ? 0 : (Math.Round((c.Sum(x => x.revBudgetInclNotApprBudChng) - c.Sum(x => x.sumM)) / (c.Sum(x => x.revBudgetInclNotApprBudChng)), 3)),
                            hasChildren = true,
                            isProjectRow = true,
                            statusDesc = statusDesc?.FirstOrDefault(z => z.Level.ToLower() == "p" && z.LevelId == proj)?.StatusDesc ?? "-",
                            statusDeviation = GetDeviationData(c, deviationsKey, negateDeviation, trafLiType),
                            changePreviousForecast = c.Sum(x => x.acmChgFc),
                            defaultChangePreviousForecast = c.Sum(x => x.acmChgFc),
                        }).FirstOrDefault();

                        finalData.Add(projectRow);
                        finalData.AddRange(accountRows.Where(row => !IsZeroRow(row)));// remove the row with all zero data
                    }
                }
                
                //parent item row
                var parentRow = mpData.GroupBy(z=> new { z.mpCode }).Select(c=> new OperationalMPGridData
                {
                    id = mp.ToString(),
                    mpName = $"{mp} - {c.First().mpName}",
                    mpCode = mp,
                    acountingYtd = c.Sum(x => x.acountingYtd),
                    deviationYtdKr = c.Sum(x => x.deviationYtdKr),
                    devationYtdPerc = c.Sum(x => x.revisedBudgetYtd) != 0 ? Math.Round((c.Sum(x => x.deviationYtdKr) / c.Sum(x => x.revisedBudgetYtd)), 3) : 0,
                    originalBudget = c.Sum(x => x.originalBudget),
                    revisedBudgetYtd = c.Sum(x => x.revisedBudgetYtd),
                    sumBudgetChanges = c.Sum(x => x.sumBudgetChanges),
                    revisedBudget = c.Sum(x => x.revisedBudget),
                    notApprBudChng = c.Sum(x => x.notApprBudChng),
                    revBudgetInclNotApprBudChng = c.Sum(x => x.revBudgetInclNotApprBudChng),
                    propForecast = c.Sum(x => x.sumG),
                    forecastChange = c.Sum(x => x.sumFr),
                    defaultForecastChange = c.Sum(x => x.sumFr),
                    newForecast = c.Sum(x => x.sumM),
                    defaultNewForecast = c.Sum(x => x.sumM),
                    deviationKr = (c.Sum(x => x.revBudgetInclNotApprBudChng) - c.Sum(x => x.sumM)),
                    deviationPerc = Math.Abs(c.Sum(x => x.revBudgetInclNotApprBudChng)) < mrBudgetBlankLimitAmount ? 0 : (Math.Round((c.Sum(x => x.revBudgetInclNotApprBudChng) - c.Sum(x => x.sumM)) / (c.Sum(x => x.revBudgetInclNotApprBudChng)), 3)),
                    hasChildren = finalData.Count(accountRows => accountRows.parentId == mp.ToString()) > 0,
                    statusDeviation = GetDeviationData(c, deviationsKey, negateDeviation, trafLiType),
                    changePreviousForecast = mpData.Sum(x => x.acmChgFc),
                    defaultChangePreviousForecast = mpData.Sum(x => x.acmChgFc),
                }).FirstOrDefault();

                if (finalData.Any(row => row.parentId == parentRow.id))// check if any lower level data is avail then add parent row
                {
                    finalData.Add(parentRow);
                }

                id++;
            }

            return finalData;
        }



        private IEnumerable<OperationalMPRepoData> GetSumFRData( int SelectedOrgLevel,  IEnumerable<OperationalMPForecastData> forecastData,int nextOrgLevelJumpLevels, int serviceUnitOrgLevel, bool isLastOrgLevelSunit)
        {
            var orgLevel = SelectedOrgLevel;

            List<int> budgetTypesAtOrgLevel = new List<int>();
            switch (orgLevel + nextOrgLevelJumpLevels)
            {
                case 2:
                    budgetTypesAtOrgLevel = (orgLevel + nextOrgLevelJumpLevels > serviceUnitOrgLevel && !isLastOrgLevelSunit) ?
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level2),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level3),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level4),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level5),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level6),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level7),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level8),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level9)} :
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level2) };
                    break;

                case 3:
                    budgetTypesAtOrgLevel = (orgLevel + nextOrgLevelJumpLevels > serviceUnitOrgLevel && !isLastOrgLevelSunit) ?
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level3),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level4),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level5),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level6),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level7),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level8),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level9)} :
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level3) };
                    break;

                case 4:
                    budgetTypesAtOrgLevel = (orgLevel + nextOrgLevelJumpLevels > serviceUnitOrgLevel && !isLastOrgLevelSunit) ?
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level4),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level5),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level6),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level7),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level8),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level9)} :
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level4) };
                    break;

                case 5:
                    budgetTypesAtOrgLevel = (orgLevel + nextOrgLevelJumpLevels > serviceUnitOrgLevel && !isLastOrgLevelSunit) ?
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level5),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level6),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level7),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level8),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level9)} :
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level5) };
                    break;

                case 6:
                    budgetTypesAtOrgLevel = (orgLevel + nextOrgLevelJumpLevels > serviceUnitOrgLevel && !isLastOrgLevelSunit) ?
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level6),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level7),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level8),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level9)} :
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level6) };
                    break;

                case 7:
                    budgetTypesAtOrgLevel = (orgLevel + nextOrgLevelJumpLevels > serviceUnitOrgLevel && !isLastOrgLevelSunit) ?
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level7),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level8),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level9)} :
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level7) };
                    break;

                case 8:
                    budgetTypesAtOrgLevel = (orgLevel + nextOrgLevelJumpLevels > serviceUnitOrgLevel && !isLastOrgLevelSunit) ?
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level8),
                                                            (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level9)} :
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level8) };
                    break;

                case 9:
                    budgetTypesAtOrgLevel = new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level9) };
                    break;

                default:
                    break;
            }

            return (from a in forecastData
                    where budgetTypesAtOrgLevel.Contains(a.budgetType)
                    group a by new { a.projectCode, a.projectName, a.mpName, a.mpCode, a.accountName, a.accountCode, a.incomeFlag } into c
                    select new OperationalMPRepoData
                    {
                        projectCode = c.Key.projectCode,
                        projectName = c.Key.projectName,
                        mpCode = c.Key.mpCode,
                        mpName = c.Key.mpName,
                        accountCode = c.Key.accountCode,
                        accountName = c.Key.accountName,
                        incomeFlag = c.Key.incomeFlag,
                        sumFr = c.Sum(x => x.sumAmtYear1)
                    }).OrderBy(x => x.mpCode).ToList();
        }

        private IEnumerable<OperationalMPRepoData> GetSumMData(int orgLevel, IEnumerable<OperationalMPForecastData> forecastData, int nextOrgLevelJumpLevels,  tmr_period_setup result_flag)
        {
            List<int> excludeBudgetTypes = new List<int>();
            const int acCorrectionBudgetType = 20;

          
            bool isForTypeBudget = result_flag != null && (string.Equals(result_flag.initialize_type, "Yearlybudget") || string.Equals(result_flag.initialize_type, "AccSal"));

            switch (orgLevel + nextOrgLevelJumpLevels)
            {
                case 2:
                    excludeBudgetTypes = new List<int> { };
                    break;

                case 3:
                    excludeBudgetTypes = new List<int> { 12 };
                    break;

                case 4:
                    excludeBudgetTypes = new List<int> { 12, 13 };
                    break;

                case 5:
                    excludeBudgetTypes = new List<int> { 12, 13, 14 };
                    break;

                case 6:
                    excludeBudgetTypes = new List<int> { 12, 13, 14, 15 };
                    break;

                case 7:
                    excludeBudgetTypes = new List<int> { 12, 13, 14, 15, 10 };
                    break;

                case 8:
                    excludeBudgetTypes = new List<int> { 12, 13, 14, 15, 10, 16 };
                    break;

                case 9:
                    excludeBudgetTypes = new List<int> { 12, 13, 14, 15, 10, 16, 17 };
                    break;

                default:
                    break;
            }

            if (isForTypeBudget)
                excludeBudgetTypes.Add(acCorrectionBudgetType);

            return (from a in forecastData
                    where !excludeBudgetTypes.Contains(a.budgetType)
                    group a by new { a.projectCode, a.projectName, a.mpName, a.mpCode, a.accountName, a.accountCode, a.incomeFlag } into c
                    select new OperationalMPRepoData
                    {
                        projectCode = c.Key.projectCode,
                        projectName = c.Key.projectName,
                        mpCode = c.Key.mpCode,
                        mpName = c.Key.mpName,
                        accountCode = c.Key.accountCode,
                        accountName = c.Key.accountName,
                        incomeFlag = c.Key.incomeFlag,
                        sumM = c.Sum(x => x.sumAmtYear1)
                    }).OrderBy(x => x.mpCode).ToList();
        }

        private IEnumerable<OperationalMPRepoData>GetSumGData(ClsOrgVersionSpecificContent orgVersionContent, int orgLevel, IEnumerable<OperationalMPForecastData> forecastData, int nextOrgLevelJumpLevels, bool isLastOrgLevelSunit, tmr_period_setup resultFlag)
        {
            var serviceUnitOrgLevel = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.forecast_flag == 1)!.org_level;

            List<int> excludeBudgetTypes = new List<int>();

            const int acCorrectionBudgetType = 20;

           

            var isForTypeBudget = resultFlag != null && (string.Equals(resultFlag.initialize_type, "Yearlybudget") || string.Equals(resultFlag.initialize_type, "AccSal") ? true : false);

            switch (orgLevel + nextOrgLevelJumpLevels)
            {
                case 2:
                    excludeBudgetTypes = (orgLevel >= serviceUnitOrgLevel && !isLastOrgLevelSunit) ?
                                         new List<int> { 12, 13, 14, 15, 10, 16, 17, 18 } :
                                         new List<int> { 12 };
                    break;

                case 3:
                    excludeBudgetTypes = (orgLevel >= serviceUnitOrgLevel && !isLastOrgLevelSunit) ?
                                         new List<int> { 12, 13, 14, 15, 10, 16, 17, 18 } :
                                         new List<int> { 12, 13 };
                    break;

                case 4:
                    excludeBudgetTypes = (orgLevel >= serviceUnitOrgLevel && !isLastOrgLevelSunit) ?
                                         new List<int> { 12, 13, 14, 15, 10, 16, 17, 18 } :
                                         new List<int> { 12, 13, 14 };
                    break;

                case 5:
                    excludeBudgetTypes = (orgLevel >= serviceUnitOrgLevel && !isLastOrgLevelSunit) ?
                                         new List<int> { 12, 13, 14, 15, 10, 16, 17, 18 } :
                                         new List<int> { 12, 13, 14, 15 };
                    break;

                case 6:
                    excludeBudgetTypes = (orgLevel >= serviceUnitOrgLevel && !isLastOrgLevelSunit) ?
                                         new List<int> { 12, 13, 14, 15, 10, 16, 17, 18 } :
                                         new List<int> { 12, 13, 14, 15, 10 };
                    break;

                case 7:
                    excludeBudgetTypes = (orgLevel >= serviceUnitOrgLevel && !isLastOrgLevelSunit) ?
                                         new List<int> { 12, 13, 14, 15, 10, 16, 17, 18 } :
                                         new List<int> { 12, 13, 14, 15, 10, 16 };
                    break;

                case 8:
                    excludeBudgetTypes = (orgLevel >= serviceUnitOrgLevel && !isLastOrgLevelSunit) ?
                                         new List<int> { 12, 13, 14, 15, 10, 16, 17, 18 } :
                                         new List<int> { 12, 13, 14, 15, 10, 16, 17 };
                    break;

                case 9:
                    excludeBudgetTypes = new List<int> { 12, 13, 14, 15, 10, 16, 17, 18 };
                    break;

                default:
                    break;
            }

            if (isForTypeBudget)
                excludeBudgetTypes.Add(acCorrectionBudgetType);

            return (from a in forecastData
                    where !excludeBudgetTypes.Contains(a.budgetType)
                    group a by new { a.projectCode, a.projectName, a.mpName, a.mpCode, a.accountName, a.accountCode, a.incomeFlag } into c
                    select new OperationalMPRepoData
                    {
                        projectCode = c.Key.projectCode,
                        projectName = c.Key.projectName,
                        mpCode = c.Key.mpCode,
                        mpName = c.Key.mpName,
                        accountCode = c.Key.accountCode,
                        accountName = c.Key.accountName,
                        incomeFlag = c.Key.incomeFlag,
                        sumG = c.Sum(x => x.sumAmtYear1)
                    }).OrderBy(x => x.mpCode).ToList();

            
        }

        public async Task<ColumnSelector> GetOpMainProjColumns(string userId, int budgetYear, List<ColumnSelectorColumn> allColumns, int orgLevel, int forecastPeriod)
        {
            ColumnSelector finalJson = new ();
            var userDetails = await _utility.GetUserDetailsAsync(userId);

            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "MonthlyReport");

            //checking for the Monthly report Yearly setup
            bool isYearlySetup = await IsYearlySetupForMonthlyReport(userId, forecastPeriod);
            string flagName = isYearlySetup ? MROperationalProjectConstantKeys.MROperationalMPYearlyFlagName : MROperationalProjectConstantKeys.MROperationalMPFlagName;
            MROperMainProjColumnConfigHelper helper = new()
            {
                UserId = userId,
                BudgetYear = budgetYear,
                AllColumns = allColumns,
                OrgLevel = orgLevel,
                ForecastPeriod = "01/01/" + budgetYear.ToString() + " 00:00:00",
                FlagName = flagName,
                OrgVersion = string.Empty,
            };
            List<ColumnSelectorColumn> columnsConfig = await GetColumnConfig(helper); //userId, budgetYear, allColumns, orgLevel, forecastPeriod, flagName, string.Empty);

            List<string> section = columnsConfig.Select(z => z.section).Distinct().ToList();

            List<ColumnSelectorSection> colList = new ();
            foreach (var item in section)
            {
                ColumnSelectorSection temp = new();
                temp.columns = columnsConfig.Where(z => z.section == item).ToList();
                temp.name = langStrings["MR_OP_col_sel_grp_" + item].LangText;

                colList.Add(temp);
            }

            finalJson.ColumnSelectorSection = colList;
            finalJson.columnSelectorTitle = langStrings["MR_OP_col_sel_header_title"].LangText;
            finalJson.columnSelectorDescription = langStrings["MR_OP_col_sel_header_description"].LangText;
            return finalJson;
        }

        public async Task<List<ColumnSelectorColumn>> GetOpMainProjReportSetupColumns(MROperMainProjColumnConfigHelper helper)
        {
            string flagName = helper.IsYearlySetup ? MROperationalProjectConstantKeys.MROperationalMPYearlyFlagName : MROperationalProjectConstantKeys.MROperationalMPFlagName;
            helper.FlagName = flagName;
            List<ColumnSelectorColumn> columnsconfig = await GetColumnConfig(helper);
            return columnsconfig;
        }

        public async Task<string> SaveOpMainProjColumns(MROperMainProjColumnConfigHelper helper)
        {
            String strJsonColumnSet = JsonConvert.SerializeObject(helper.AllColumns);
            string flagName = helper.IsYearlySetup ? MROperationalProjectConstantKeys.MROperationalMPYearlyFlagName : MROperationalProjectConstantKeys.MROperationalMPFlagName;
            return await _rUow.MROperationalMainProjectReportRepository.SaveOperationalProjColumnsConfigAsync(helper.UserId, flagName, strJsonColumnSet, -1, helper.BudgetYear, helper.OrgLevel, helper.OrgVersion);
        }

        public int GetForecastPeriodFromDate(string monthYear)
        {
            int budgetYear = 0;
            int ForecastPeriod = 0;

            if (!string.IsNullOrEmpty(monthYear))
            {
                string inputDateTime = monthYear;
                budgetYear = DateTime.ParseExact(inputDateTime, "MM/dd/yyyy hh:mm:ss", CultureInfo.InvariantCulture).Year;
                ForecastPeriod = budgetYear * 100 + DateTime.ParseExact(inputDateTime, "MM/dd/yyyy hh:mm:ss", CultureInfo.InvariantCulture).Month;
            }
            return ForecastPeriod;
        }

        public async Task<List<ColumnSelectorColumn>> GetOperationMPRepGridColumns(string userID, Stream configStream, int orgLevel, int forecastPeriod)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            var orgVersionContentAsync = _utility.GetOrgVersionSpecificContentAsync(userID, forecastPeriod);
            var langStrAsync = _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "MonthlyReport");
            await Task.WhenAll(orgVersionContentAsync, langStrAsync);
            var orgVersionContent = orgVersionContentAsync.Result;
            var langStr = langStrAsync.Result;
            int serviceUnitOrgLevel = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.forecast_flag == 1).org_level;
            StreamReader reader = new StreamReader(configStream);
            string config = await reader.ReadToEndAsync();

            JArray colConfig = JArray.Parse(config);
            List<ColumnSelectorColumn> columnList = (colConfig.ToObject<List<ColumnSelectorColumn>>());

            foreach (ColumnSelectorColumn column in columnList)
            {
                column.value = column.key switch
                {
                    "statusDeviation" => langStr["MR_status_deviation"].LangText,
                    "revisedBudgetYtd" => langStr["MR_revised_budget_ytd"].LangText,
                    "acountingYtd" => langStr["MR_accounting_ThisYearSum"].LangText,
                    "deviationYtdKr" => langStr["MR_deviation_AmtYtd"].LangText,
                    "devationYtdPerc" => langStr["MR_deviation_PerYtd"].LangText,
                    "originalBudget" => langStr["MR_originalBudget"].LangText,
                    "sumBudgetChanges" => langStr["MR_totalBudgetAdjustment"].LangText,
                    "revisedBudget" => langStr["MR_Info_incorporated"].LangText,
                    "notApprBudChng" => langStr["MR_revisedBudgetNonAdopt"].LangText,
                    "revBudgetInclNotApprBudChng" => langStr["MR_revisedBudgetThisYear"].LangText,
                    "propForecast" => langStr["MR_latestForecastSalary"].LangText,
                    "forecastChange" => (orgLevel >= serviceUnitOrgLevel) ? langStr["MR_changeForecast"].LangText : langStr["MR_Risk_Assessment"].LangText,
                    "newForecast" => langStr["MR_newForecast"].LangText,
                    "deviationKr" => langStr["MR_deviationForecastThisYear"].LangText,
                    "deviationPerc" => langStr["MR_deviationForecastPer"].LangText,
                    "status" => langStr["MR_ActivityStatus_Editor_Title"].LangText,
                    "risk" => langStr["MR_risk"].LangText,
                    "quality" => langStr["MR_SearchTable_quality"].LangText,
                    "finStatus" => langStr["MRYearly_SearchTable_financialstatus"].LangText,
                    "statusDesc" => langStr["MR_Abs_Description"].LangText,
                    "changePreviousForecast" => langStr["MR_changePreviousForecast"].LangText,
                    _ => ""
                };
            }
            return columnList;
        }

        #region Private Methods

        private async Task<List<ColumnSelectorColumn>> GetColumnConfig(MROperMainProjColumnConfigHelper helper)
        {
            var userDetails = await _utility.GetUserDetailsAsync(helper.UserId);
            List<ColumnSelectorColumn> columnsconfig = new();
            if (string.IsNullOrEmpty(helper.OrgVersion))
            {
                int ForecastPeriod = GetForecastPeriodFromDate(helper.ForecastPeriod);
                var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(helper.UserId, ForecastPeriod);
                helper.OrgVersion = orgVersionContent.orgVersion;
            }
            var tenantColumnConfig = await _rUow.MROperationalMainProjectReportRepository.GetOperationalProjApplicationFlag(helper.UserId, helper.FlagName, "-1", helper.BudgetYear, helper.OrgLevel, helper.OrgVersion);
            if (tenantColumnConfig != null)
            {
                Guid? flagGuid = tenantColumnConfig.flag_guid;

                if (flagGuid != null)
                {
                    clsTenantCloudTableEntity updateEntity = await _utility.GetCloudTableContentAsync("WADTenantData", userDetails.tenant_id.ToString(), flagGuid.ToString());
                    if (updateEntity != null)
                    {
                        JArray columnsArray = new();
                        columnsArray = JArray.FromObject(JsonConvert.DeserializeObject(updateEntity.data.ToString()));
                        List<ColumnSelectorColumn> columnList = columnsArray.ToObject<List<ColumnSelectorColumn>>();

                        columnsconfig = (from s in helper.AllColumns
                                         join b in columnList on s.key equals b.key into grp
                                         from grp1 in grp.DefaultIfEmpty()
                                         select new ColumnSelectorColumn()
                                         {
                                             key = grp1 != null ? grp1.key : s.key,
                                             value = grp1 != null ? grp1.value : s.value,
                                             isChecked = grp1 != null ? grp1.isChecked : s.isChecked,
                                             section = grp1 != null ? grp1.section : s.section,
                                         }).ToList();
                    }
                }
            }
            else
            {
                columnsconfig = (from s in helper.AllColumns
                                 select new ColumnSelectorColumn()
                                 {
                                     key = s.key,
                                     value = s.value,
                                     isChecked = s.isDefault,
                                     section = s.section,
                                 }).ToList();
            }
            return columnsconfig;
        }

        private async Task<bool> IsYearlySetupForMonthlyReport(string userId, int forecastPeriod)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            tmr_period_setup result_flag = await _rUow.MonthlyReportCommonRepo.GetMRPeriodSetup(userDetails.tenant_id, forecastPeriod);
            bool result = result_flag != null && (result_flag.isYearlySetupForMonthlyReport.HasValue ? result_flag.isYearlySetupForMonthlyReport.Value : false);
            return result;
        }
        public async Task<StatusDropdownList> GetStatusDropdownList(string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "MonthlyReport");
            StatusDropdownList result = new();

            result.Status = await GetOperationalStatusList(userDetails.tenant_id,langStrings);
            result.Finstatus = await GetOperationalFinstatusList(userDetails.tenant_id, langStrings);
            result.Risk = await GetOperationalRiskList(userDetails.tenant_id, langStrings);
            result.Quality = await GetOperationalQualityList(userDetails.tenant_id, langStrings);
            return result;
        }
        public async Task<OperationalStatusHelper> GetOperationalStatusDropdownData(string userId,int forecastPeriod, string level, string levelId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var statusData = await _rUow.MROperationalMainProjectReportRepository.GetOperationalStatusDropdownData(userDetails.tenant_id, forecastPeriod,levelId, level) ?? new();
            return statusData;
        }
        public async Task<bool> SaveOperationalForecastGridData(string userName, int budgetYear, int budPeriod,
             List<saveForecastHelper> forecastValuePair, string orgId, int orgLevel, string serviceId, OrgInpLevels orgInput,int period, string attributeId = "")
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userName);
                TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
                bool isChapterSetup = await _mrfUtility.isChapterSetupTenant(userName);
                string viewType = ((int)clsConstants.MR_EcoStatusGrid_ViewType.accsetup).ToString();
                IQueryable<string> departmentsInServiceArea = null;
                int servUnitRowIndex = 0, budgetType = 0;
                List<int> budgetTypesAtOrgLevel = new List<int>();
                string servFunctionCode = "";
                string projectCode = string.Empty;
                string freedimCode = string.Empty;
                dbContext.Database.SetCommandTimeout(300);
                string origServiceId = serviceId;
                // string[] projfrdmFunDelims = new string[] { "x", "z" };
                
                var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userName, budPeriod);
                int serviceUnitOrgLevel = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.forecast_flag == 1).org_level;

                int nextOrgLevelRptSetup = await _mrfUtility.GetNextOrgLevelFinStatusReportingAsync(userName, orgLevel, budPeriod);
                int nextOrgLevelJumpLevels = nextOrgLevelRptSetup - orgLevel;

                int lastOrgLevelRptSetup = await _mrfUtility.GetLastOrgLevelFinStatusReportingAsync(userName, budPeriod);
                bool isLastOrgLevelSunit = (lastOrgLevelRptSetup == serviceUnitOrgLevel);

                int dummyRowIdx = 0;
                string chapterId = string.Empty;
                List<tco_org_level> lstOrgLevels = orgVersionContent.lstOrgLevel.OrderBy(y => y.org_level).ToList();
                int dropdownServiceLevel = (lstOrgLevels.FirstOrDefault(x => x.org_level == orgLevel) == null || serviceId == "-1") ? -1 : lstOrgLevels.FirstOrDefault(x => x.org_level == orgLevel).fk_service_level_id.Value;

                if (forecastValuePair != null && forecastValuePair.Count > 0 && (!(forecastValuePair.Where(x => x.OrgId == "controller").Any() || forecastValuePair.Where(x => x.OrgId == "action").Any())
                    && !(forecastValuePair.Where(x => x.OrgId == "0").Any() && (forecastValuePair.Where(x => x.Value == 0).Any()))))
                {
                    List<IQueryable<string>> functionsInServiceUnitArr = new List<IQueryable<string>>();
                    List<IQueryable<string>> departmentsInServiceUnitArr = new List<IQueryable<string>>();

                    (budgetType, budgetTypesAtOrgLevel) = GetBudgetTypes(orgLevel, nextOrgLevelJumpLevels, serviceUnitOrgLevel, isLastOrgLevelSunit);
                    List<string> edAccountCodeKeyList = new List<string>();
                    List<int> edAccountCodesLevels = new List<int>();

                    await _mrfUtility.SetStatusToInprogressAsync(userName, orgId, serviceId, budPeriod.ToString(), orgLevel, 1);
                    if (!isChapterSetup || string.IsNullOrEmpty(attributeId)) departmentsInServiceArea = _rUow.MRFinStatusRepository.GetDepartmentsForOrgIdHierLvlsQuery(orgVersionContent, userDetails.tenant_id, budgetYear, orgInput, dbContext, false);

                    foreach (var fcKeyVal in forecastValuePair)
                    {

                        functionsInServiceUnitArr.Add(string.IsNullOrEmpty(serviceId) ? null
                                                                                : _rUow.MRFinStatusRepository.GetFunctionsForOrgIdHierLvlsQuery(orgVersionContent, userDetails.tenant_id, orgInput, dbContext, serviceId == "-1" ? null : serviceId, dropdownServiceLevel).Select(x => x.FunctionCode).AsQueryable());
                        departmentsInServiceUnitArr.Add(string.IsNullOrEmpty(attributeId) ? null
                                                                                : _rUow.MRFinStatusRepository.GetAttributeConnectedDepartmentsForSelectedOrgQuery(userDetails.tenant_id, budgetYear, orgId, orgLevel, orgVersionContent, dbContext, attributeId));
                        if (isChapterSetup && !string.IsNullOrEmpty(attributeId))
                        {
                            if (departmentsInServiceArea is not null)
                                departmentsInServiceArea = departmentsInServiceArea.Concat(departmentsInServiceUnitArr.Last());
                            else
                                departmentsInServiceArea = departmentsInServiceUnitArr[0];
                        }

                        string edAcCodeKey = fcKeyVal.AccountCode;
                        edAccountCodeKeyList.Add(edAcCodeKey);
                    }

                    List<AccountDataHelper> accCodeDeptDict = await _rUow.MRFinStatusRepository.FetchAccountData(userDetails, departmentsInServiceArea, budPeriod, budgetYear);


                    List<tbu_forecast_transactions> forecastTrasactionDataToAdd = new List<tbu_forecast_transactions>();
                    servUnitRowIndex = 0;
                    foreach (var forecastData in forecastValuePair)
                    {
                        decimal lowerLevelCumulative = 0;
                        bool lowerLevelCumuDone = false;
                        //Empty key value pair as defaulted by MVC if UI sends wrong key-value pair (only key, no value etc).
                        if (forecastData.OrgId == dummyRowIdx.ToString() && forecastData.Value == 0)
                        {
                            dummyRowIdx++;
                            continue;
                        }

                        servFunctionCode = string.Empty;
                        projectCode = string.Empty;
                        freedimCode = string.Empty;
                        string linkValueOrg = orgInput.level2OrgId;
                        if (!string.IsNullOrEmpty(serviceId))
                        {
                            linkValueOrg = serviceId;
                        }
                        var defFunction = await _rUow.MRFinStatusRepository.GetDefaultAccount(userDetails.tenant_id, orgVersionContent, linkValueOrg, "SERVICEAREA", "FUNCTION");

                        List<string> accountCodeList = await FilterAccountCodeList(departmentsInServiceArea, accCodeDeptDict);

                        if (!string.IsNullOrEmpty(forecastData.ProjectCode))
                        {
                            projectCode = forecastData.ProjectCode;
                        }
                        servFunctionCode = defFunction.acc_value;

                        tmd_acc_defaults defAccount = await _rUow.MRFinStatusRepository.GetDefaultAccount(userDetails.tenant_id, orgVersionContent, linkValueOrg, "SERVICEAREA", "Account");
                        tmd_acc_defaults defDepartment = await _rUow.MRFinStatusRepository.GetDefaultAccount(userDetails.tenant_id, orgVersionContent, linkValueOrg, "SERVICEAREA", "DEPARTMENT");

                        List<tbu_forecast_transactions> forecastTransListBudTypes = await _rUow.MRFinStatusRepository.GetForecastTransactionData(userDetails, serviceId, servUnitRowIndex, servFunctionCode,
                                                                                                                    viewType, functionsInServiceUnitArr, dbContext, orgLevel, orgVersionContent, budPeriod,
                                                                                                                    departmentsInServiceArea, budgetYear, budgetTypesAtOrgLevel, accountCodeList, period);

                        List<tbu_forecast_transactions> forcastTransactionDataList = forecastTransListBudTypes.Where(x => x.budget_type == budgetType).ToList();

                        tbu_forecast_transactions? forecastTransactionData = GetDefaultForecastTransactionData(forcastTransactionDataList, ref forecastTransListBudTypes, projectCode, forecastData.AccountCode);

                        //173343 commenting this code for quick fix later we will do account logic implentation in story 
                       // (lowerLevelCumulative, lowerLevelCumuDone) = CalculateCummilativeData(forecastTransListBudTypes, lowerLevelCumulative, lowerLevelCumuDone);

                        if (serviceId != "-1" && !string.IsNullOrEmpty(serviceId) && string.IsNullOrEmpty(servFunctionCode))
                        {
                            servFunctionCode = functionsInServiceUnitArr[servUnitRowIndex] != null ? await functionsInServiceUnitArr[servUnitRowIndex].FirstOrDefaultAsync()
                                : await _rUow.MRFinStatusRepository.FetchOneFunctionCodeForServiceIdAsync(userDetails, dbContext, serviceId, orgLevel, orgVersionContent, budPeriod);
                        }

                        if (forecastTransactionData is null)
                        {
                            forecastTrasactionDataToAdd.Add(CreateForecastDataToAdd(userDetails, orgLevel, accountCodeList, budgetYear, period, budPeriod,
                                forecastData, defFunction, departmentsInServiceArea.ToList(), projectCode, budgetType, defDepartment, lowerLevelCumulative));
                        }
                        else
                        {
                            forecastTransactionData.amount_year_1 = forecastData.Value - lowerLevelCumulative;
                            forecastTransactionData.total_amount = forecastData.Value - lowerLevelCumulative;
                            forecastTransactionData.period = period != 0 ? period : budgetYear * 100 + 12;
                            forecastTransactionData.budget_year = budgetYear;
                            forecastTransactionData.forecast_period = budPeriod;
                            forecastTransactionData.updated = DateTime.UtcNow;
                            forecastTransactionData.updated_by = userDetails.pk_id;
                            forecastTransactionData.fk_function_code = defFunction.acc_value;
                            

                             //setting applicable budget_type according to org_level involved.
                                forecastTransactionData.budget_type = budgetType;

                            await _rUow.MRFinStatusRepository.SaveChangesAsync();
                        }
                        servUnitRowIndex++;
                    }

                    _rUow.MRFinStatusRepository.AddTbuForecastTransactionData(forecastTrasactionDataToAdd);
                    await _rUow.MRFinStatusRepository.SaveChangesAsync();
                }
                await _rUow.CompleteAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
        private (decimal, bool) CalculateCummilativeData(List<tbu_forecast_transactions> forecastTransListBudTypes, decimal lowerLevelCumulative, bool lowerLevelCumuDone)
        {
            //Do not delete the lower level forecast, but calculate the total of lower level forecasts in order to apply the difference when add/update record at current level budget type.
            if (forecastTransListBudTypes.Count > 0 && !lowerLevelCumuDone)
            {
                foreach (var item in forecastTransListBudTypes)
                {
                    lowerLevelCumulative += item.amount_year_1;
                }

                lowerLevelCumuDone = true;
            }
            return (lowerLevelCumulative, lowerLevelCumuDone);
        }

        public async Task SaveOperationalStatusData(string userId, int forecastPeriod, OperationalStatusHelper input)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext tenantdbContext = await _utility.GetTenantDBContextAsync();
            var record = await _rUow.MROperationalMainProjectReportRepository.GetOperationalStatusData(userDetails.tenant_id, forecastPeriod, input.LevelId, input.Level);
            if (record == null)
            {
                tmr_proj_operations_status newRecord = new();
                newRecord.FkTenantId = userDetails.tenant_id;
                newRecord.ForecastPeriod = forecastPeriod;
                newRecord.Level = input.Level;
                newRecord.LevelId = input.LevelId;
                newRecord.Status = input.Status;
                newRecord.FinStatus = input.Finstatus;
                newRecord.Quality = input.Quality;
                newRecord.Risk = input.Risk;
                newRecord.StatusDesc = string.Empty;
                newRecord.StatusDescIdHistory = Guid.NewGuid();
                newRecord.Updated = DateTime.UtcNow;
                newRecord.UpdatedBy = userDetails.pk_id;
                await _rUow.GenericRepo.AddAsync(newRecord);
            }
            else
            {
                record.Status = input.Status;
                record.FinStatus = input.Finstatus;
                record.Quality = input.Quality;
                record.Risk = input.Risk;
                record.Updated = DateTime.UtcNow;
                record.UpdatedBy = userDetails.pk_id;
                _rUow.GenericRepo.Update(record);
            }
            await _rUow.CompleteAsync();
        }
        public async Task<List<KeyValueInt>> GetOperationalStatusList(int tenantId, Dictionary<string, clsLanguageString> langStrings)
        {
            TenantDBContext tenantdbContext = await _utility.GetTenantDBContextAsync();
            var statusList = await (from a in tenantdbContext.tco_progress_status
                                    where a.fk_tenant_id == tenantId && a.type == "MONTHREP_OP"
                                    select new KeyValueInt
                                    {
                                        Key = a.status_id,
                                        Value = a.status_description
                                    }).ToListAsync();
            statusList.Insert(0,new KeyValueInt { Key = 0, Value = langStrings["MR_OP_status_default"].LangText });
            return statusList;
        }
        public async Task<List<KeyValueInt>> GetOperationalRiskList(int tenantId, Dictionary<string, clsLanguageString> langStrings)
        {
            TenantDBContext tenantdbContext = await _utility.GetTenantDBContextAsync();
            var statusList = await (from a in tenantdbContext.tco_progress_status
                                    where a.fk_tenant_id == tenantId && a.type == "MONTHREP_OP_RISK"
                                    select new KeyValueInt
                                    {
                                        Key = a.status_id,
                                        Value = a.status_description
                                    }).ToListAsync();
            statusList.Insert(0, new KeyValueInt { Key = 0, Value = langStrings["MR_OP_status_default"].LangText });
            return statusList;
        }
        public async Task<List<KeyValueInt>> GetOperationalQualityList(int tenantId, Dictionary<string, clsLanguageString> langStrings)
        {
            TenantDBContext tenantdbContext = await _utility.GetTenantDBContextAsync();
            var statusList = await (from a in tenantdbContext.tco_progress_status
                                    where a.fk_tenant_id == tenantId && a.type == "MONTHREP_OP_QUALITY"
                                    select new KeyValueInt
                                    {
                                        Key = a.status_id,
                                        Value = a.status_description
                                    }).ToListAsync();
            statusList.Insert(0, new KeyValueInt { Key = 0, Value = langStrings["MR_OP_status_default"].LangText });
            return statusList;
        }
        public async Task<List<KeyValueInt>> GetOperationalFinstatusList(int tenantId, Dictionary<string, clsLanguageString> langStrings)
        {
            TenantDBContext tenantdbContext = await _utility.GetTenantDBContextAsync();
            var statusList = await (from a in tenantdbContext.tco_progress_status
                                    where a.fk_tenant_id == tenantId && a.type == "MONTHREP_OP_FIN_STATUS"
                                    select new KeyValueInt
                                    {
                                        Key = a.status_id,
                                        Value = a.status_description
                                    }).ToListAsync();
            statusList.Insert(0, new KeyValueInt { Key = 0, Value = langStrings["MR_OP_status_default"].LangText });
            return statusList;
        }

        public async Task<OperationalDescriptionsHelper> GetOPMainProjectTextDescription(string userId, int forecastPeriod, string levelId, string level)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var finalData = new OperationalDescriptionsHelper();
            tmr_proj_operations_status statusData = await _rUow.MROperationalMainProjectReportRepository.GetOperationalStatusData(userDetails.tenant_id, forecastPeriod, levelId, level);
            finalData.description = statusData?.StatusDesc ?? string.Empty;
            finalData.descriptionGUID = statusData?.StatusDescIdHistory ?? Guid.Empty;
            return finalData;
        }

        public async Task<JObject> GetOPMainTextEditorHistory(string userId, int forecastPeriod, string level, string levelId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            JObject finalData = new JObject();
            JArray pageHeader = new JArray();
            dynamic headerTitle = new JObject();
            JArray data = new JArray();
            var guid = new Guid();
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "common");

            headerTitle.title = langStrings.FirstOrDefault(v => v.Key == "cmn_text_historyTitle").Value.LangText;
            headerTitle.descriptiontip = langStrings.FirstOrDefault(v => v.Key == "cmn_text_historyDesc").Value.LangText;
            headerTitle.textDiffTitle = langStrings.FirstOrDefault(v => v.Key == "cmn_text_diffHistoryTitle").Value.LangText;
            headerTitle.textDiffDescriptiontip = langStrings.FirstOrDefault(v => v.Key == "cmn_text_diffHistoryDesc").Value.LangText;
            pageHeader.Add(headerTitle);
            finalData.Add("header", pageHeader);
            tmr_proj_operations_status statusData = await _rUow.MROperationalMainProjectReportRepository.GetOperationalStatusData(userDetails.tenant_id, forecastPeriod, levelId, level);

            if (statusData != null) {
                guid = statusData.StatusDescIdHistory == Guid.Empty ? Guid.NewGuid() : statusData.StatusDescIdHistory;
                List<TextEditorHelper> textEditorLog = await GetTxtHistoryUserTsAsync(userId, guid);
                foreach (var item in textEditorLog)
                {
                    dynamic groupedData = new JObject();
                    groupedData.period = item.savedDate;
                    JArray description = new JArray();
                    dynamic indDescData = new JObject();
                    indDescData.userName = item.SavedUser;
                    indDescData.updatedTime = (item.SavedDateTime.Year + "-" + item.SavedDateTime.ToString("MM") + "-" + item.SavedDateTime.ToString("dd") + "T" + item.SavedDateTime.ToString("HH") + ":"
                                    + item.SavedDateTime.ToString("mm") + ":" + item.SavedDateTime.ToString("ss") + "Z");
                    indDescData.text = await _utility.SanitizeHtml(userId, item.Description);
                    indDescData.hasMultipleUsers = false;
                    var userNamesList = new List<string>();
                    description.Add(indDescData);
                    groupedData.Add("description", description);
                    data.Add(groupedData);
                }
            }
            finalData.Add("data", data);
            return finalData;
        }

        public async Task<List<TextEditorHelper>> GetTxtHistoryUserTsAsync(string userId, Guid historyGuid)
        {
            List<TextEditorHelper> histUserTs = new List<TextEditorHelper>();
            string fpHistory = await _utility.GetHistoryDataAsync(userId, historyGuid);
            if (!string.IsNullOrEmpty(fpHistory))
            {
                histUserTs = JsonConvert.DeserializeObject<List<TextEditorHelper>>(fpHistory);
            }
            return histUserTs!;
        }

        public async Task<string> SaveOPMainProjectTextdescription(string userId, OperationalDescriptionSaveHelper inputStatusValues)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            tmr_proj_operations_status projectStatus = await _rUow.MROperationalMainProjectReportRepository.GetOperationalStatusData(userDetails.tenant_id, inputStatusValues.forecastPeriod, inputStatusValues.levelId, inputStatusValues.level);
            Guid descriptionId = Guid.Empty;
            string description = String.Empty;
            DateTime updated = DateTime.UtcNow;
            if (projectStatus != null)
            {
                projectStatus.StatusDesc = description = inputStatusValues.description;
                projectStatus.Updated = updated =  DateTime.UtcNow;
                projectStatus.UpdatedBy = userDetails.pk_id;
                _rUow.GenericRepo.Update(projectStatus);
                descriptionId = projectStatus.StatusDescIdHistory;
            }
            else
            {
                tmr_proj_operations_status newRecord = new tmr_proj_operations_status()
                {
                    FkTenantId = userDetails.tenant_id,
                    ForecastPeriod = inputStatusValues.forecastPeriod,
                    Level = inputStatusValues.level,
                    LevelId = inputStatusValues.levelId,
                    StatusDesc = description = inputStatusValues.description,
                    StatusDescIdHistory = descriptionId = Guid.NewGuid(),
                    Status = 0,
                    Risk = 0,
                    FinStatus = 0,
                    Quality = 0,
                    Updated = updated = DateTime.UtcNow,
                    UpdatedBy = userDetails.pk_id
                };
                _rUow.GenericRepo.Add(newRecord);
            }
            if (inputStatusValues.logHistory)
                await _utility.SaveTextLogAsync(userId, descriptionId, description, updated, "");
            await _rUow.CompleteAsync();
            return "success";
        }

        public async Task<List<OperationalFetchDescriptionHelper>> FetchOPMainProjectTextPopup(string userId, OperationalDescriptionsInputHelper input)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            List<OperationalFetchDescriptionHelper> opMainProjectList = await _rUow.MROperationalMainProjectReportRepository.GetOPMainProjectDescTextFetchTextPopup(userDetails.tenant_id, input.forecastPeriod, input.levelId, input.level, input.projectsIds);
            return opMainProjectList;
        }

        public string LoadBelowLevelOPMainProjectDescription(List<OperationalFetchDescriptionHelper> selectedIds)
        {
            string concatenatedDescription = "";
            foreach (var item in selectedIds)
            {
                var titleText = "<b>" + item.objectName + "</b><br><br>";
                var descriptionText = item.description;
                concatenatedDescription = concatenatedDescription + titleText + descriptionText + "<br><br>";
            }
            return concatenatedDescription;
        }
        public async Task<bool> CopyPeriodDescription(string userId,CopyPeriodDescriptionHelper input)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var periodData = await _rUow.MROperationalMainProjectReportRepository.GetOperationalStatusData(userDetails.tenant_id, input.SelectedPeriod, input.LevelId, input.Level);
            var forecastPeriodData = await _rUow.MROperationalMainProjectReportRepository.GetOperationalStatusData(userDetails.tenant_id, input.ForecastPeriod, input.LevelId, input.Level);
            try
            {
                if (periodData != null)
                {
                    if (forecastPeriodData != null)
                    {
                        forecastPeriodData.StatusDesc = periodData.StatusDesc;
                        forecastPeriodData.Updated = DateTime.UtcNow;
                        forecastPeriodData.UpdatedBy = userDetails.pk_id;
                        _rUow.GenericRepo.Update(forecastPeriodData);
                        await _utility.SaveTextLogAsync(userId, forecastPeriodData.StatusDescIdHistory, forecastPeriodData.StatusDesc, forecastPeriodData.Updated, "");
                    }
                    else
                    {
                        tmr_proj_operations_status newRecord = new();
                        newRecord.FkTenantId = userDetails.tenant_id;
                        newRecord.ForecastPeriod = input.ForecastPeriod;
                        newRecord.Level = input.Level;
                        newRecord.LevelId = input.LevelId;
                        newRecord.StatusDesc = periodData.StatusDesc;
                        newRecord.StatusDescIdHistory = Guid.NewGuid();
                        newRecord.Status = 0;
                        newRecord.Risk = 0;
                        newRecord.Quality = 0;
                        newRecord.FinStatus = 0;
                        newRecord.Updated = DateTime.UtcNow;
                        newRecord.UpdatedBy = userDetails.pk_id;
                        await _rUow.GenericRepo.AddAsync(newRecord);
                        await _utility.SaveTextLogAsync(userId, newRecord.StatusDescIdHistory, newRecord.StatusDesc, newRecord.Updated, "");
                    }
                    await _rUow.CompleteAsync();
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception)
            {
                return false;
            }
        }

        private string SetOPProjectLevelKey(string item)
        {
            switch (item)
            {

            case  "OPMainProject": return "MP";
                default: return "MP";
            }
    }
        private (int, List<int>) GetBudgetTypes(int orgLevel, int nextOrgLevelJumpLevels, int serviceUnitOrgLevel, bool isLastOrgLevelSunit)
        {
            int budgetType = 0;
            List<int> budgetTypesAtOrgLevel = new List<int>();
            switch (orgLevel + nextOrgLevelJumpLevels)
            {
                case 2:
                    budgetType = (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level2);
                    budgetTypesAtOrgLevel = (orgLevel + nextOrgLevelJumpLevels > serviceUnitOrgLevel && !isLastOrgLevelSunit) ?
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level2),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level3),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level4),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level5),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level6),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level7),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level8),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level9)} :
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level2) };
                    break;

                case 3:
                    budgetType = (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level3);
                    budgetTypesAtOrgLevel = (orgLevel + nextOrgLevelJumpLevels > serviceUnitOrgLevel && !isLastOrgLevelSunit) ?
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level3),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level4),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level5),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level6),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level7),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level8),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level9)} :
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level3) };
                    break;

                case 4:
                    budgetType = (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level4);
                    budgetTypesAtOrgLevel = (orgLevel + nextOrgLevelJumpLevels > serviceUnitOrgLevel && !isLastOrgLevelSunit) ?
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level4),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level5),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level6),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level7),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level8),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level9)} :
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level4) };
                    break;

                case 5:
                    budgetType = (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level5);
                    budgetTypesAtOrgLevel = (orgLevel + nextOrgLevelJumpLevels > serviceUnitOrgLevel && !isLastOrgLevelSunit) ?
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level5),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level6),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level7),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level8),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level9)} :
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level5) };
                    break;

                case 6:
                    budgetType = (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level6);
                    budgetTypesAtOrgLevel = (orgLevel + nextOrgLevelJumpLevels > serviceUnitOrgLevel && !isLastOrgLevelSunit) ?
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level6),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level7),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level8),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level9)} :
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level6) };
                    break;

                case 7:
                    budgetType = (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level7);
                    budgetTypesAtOrgLevel = (orgLevel + nextOrgLevelJumpLevels > serviceUnitOrgLevel && !isLastOrgLevelSunit) ?
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level7),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level8),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level9)} :
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level7) };
                    break;

                case 8:
                    budgetType = (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level8);
                    budgetTypesAtOrgLevel = (orgLevel + nextOrgLevelJumpLevels > serviceUnitOrgLevel && !isLastOrgLevelSunit) ?
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level8),
                                                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level9)} :
                                            new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level8) };
                    break;

                case 9:
                    budgetType = (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level9);
                    budgetTypesAtOrgLevel = new List<int> { (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level9) };
                    break;

                default:
                    break;

            }
            return (budgetType, budgetTypesAtOrgLevel);
        }
        private async Task<List<string>> FilterAccountCodeList(IQueryable<string> departmentsInServiceArea,List<AccountDataHelper> accCodeDeptDict)
        {
            List<string> departmentsInServiceAreaList = await departmentsInServiceArea.ToListAsync();
            List<string> accountCodeList = new List<string>();

            accountCodeList = (from a in accCodeDeptDict
                                   join b in departmentsInServiceAreaList
                                   on a.departmentCode equals b
                                   select a.pkAccountCode).Distinct().ToList();
            return accountCodeList;
        }
        private tbu_forecast_transactions? GetDefaultForecastTransactionData(List<tbu_forecast_transactions> forcastTransactionDataList, ref List<tbu_forecast_transactions> forecastTransListBudTypes,string projectCode,string accountCode)
        {
            tbu_forecast_transactions foreacastTransactionData = null;
            if (forcastTransactionDataList.Count > 0)
            {
                forecastTransListBudTypes = forecastTransListBudTypes.Where(x => x.fk_project_code == projectCode).ToList();
                forcastTransactionDataList = forecastTransListBudTypes.Where(x => x.fk_project_code == projectCode).ToList();
                foreacastTransactionData = forcastTransactionDataList.FirstOrDefault(x => x.fk_account_code == accountCode && x.fk_project_code == projectCode);
                forecastTransListBudTypes.Remove(foreacastTransactionData);
            }
            return foreacastTransactionData;
        }
        private tbu_forecast_transactions CreateForecastDataToAdd(UserData userDetails,int orgLevel, List<string> accountCodeList, int budgetYear, int period, int budPeriod,
            saveForecastHelper forecastData,tmd_acc_defaults defFunction,List<string> departmentsInServiceArea, string projectCode,int budgetType, tmd_acc_defaults defDepartment, decimal lowerLevelCumulative)
        {
            string? accountCode = accountCodeList.Count > 0 ? accountCodeList[0] : null;

            //Dummy record for no accountCode match
            tbu_forecast_transactions newForecastData = new tbu_forecast_transactions();
            newForecastData.pk_id = Guid.NewGuid();
            newForecastData.bu_trans_id = Guid.NewGuid();
            newForecastData.amount_year_1 = forecastData.Value - lowerLevelCumulative;
            newForecastData.total_amount = forecastData.Value - lowerLevelCumulative;
            newForecastData.budget_year = budgetYear;
            newForecastData.period = period != 0 ? period : budgetYear * 100 + 12;
            newForecastData.forecast_period = budPeriod;
            newForecastData.fk_tenant_id = userDetails.tenant_id;
            newForecastData.updated = DateTime.UtcNow;
            newForecastData.updated_by = userDetails.pk_id;
            newForecastData.holiday_flag = 0;
            newForecastData.line_order = 0;
            newForecastData.action_type = 5;
            newForecastData.fk_project_code = projectCode;
            newForecastData.free_dim_1 = "";
            newForecastData.free_dim_2 = "";
            newForecastData.free_dim_3 = "";
            newForecastData.free_dim_4 = "";
            newForecastData.resource_id = "";
            newForecastData.description = "";
            newForecastData.fk_employment_id = 0;
            newForecastData.amount_year_2 = 0;
            newForecastData.amount_year_3 = 0;
            newForecastData.amount_year_4 = 0;
            newForecastData.tax_flag = 0;
            newForecastData.holiday_flag = 0;
            newForecastData.fk_pension_type = "";
            newForecastData.fk_prog_code = "";
            newForecastData.fk_alter_code = "";
            newForecastData.description = string.Empty;
            newForecastData.budget_type = budgetType;
            newForecastData.fk_account_code = forecastData.AccountCode;
            //Min of the department code in departmentsInServiceArea which is a sorted list of departments within servicearea/serviceunit
            newForecastData.department_code = departmentsInServiceArea[0];
            newForecastData.fk_function_code = defFunction.acc_value;
            return newForecastData;
        }

        #endregion

        public async Task<bool> CheckFinStatusIsSatrted(string userId, int forecastPeriod)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var statusData = await _rUow.MROperationalMainProjectReportRepository.CheckTmrReportSetupStatus(userDetails.tenant_id, forecastPeriod, 1);
            return statusData;
        }

        public async Task<List<KeyValuePair>> GetStartedPeriodDropdownData(UserData userDetails, int forecastPeriod, int orgLevel)
        {
            try
            {
                int forecastPeriodStart = 0;
                int budgetYear = forecastPeriod / 100;
                forecastPeriodStart = _utility.GetForecastPeriod(budgetYear, 1);
                List<KeyValuePair> periodData = new List<KeyValuePair>();
                Dictionary<string, clsLanguageString> langStringValuesCommon = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Common");
                string[] monthFields = new string[] { ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_january_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_february_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_march_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_april_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_may_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_june_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_july_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_august_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_september_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_october_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_november_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_december_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_rebudget_13th")).Value).LangText};
                var data = await _rUow.MROperationalMainProjectReportRepository.GetStartedPeriodDropdownData(userDetails.tenant_id, forecastPeriodStart, forecastPeriod, orgLevel);
                foreach (int item in data)
                {
                    int year = item / 100;
                    int month = item - (year * 100);
                    KeyValuePair period = new KeyValuePair();
                    period.key = new DateTime(year, month, 1).ToString();
                    period.value = monthFields[month - 1] + " " + year;
                    period.forecastPeriod = item.ToString();
                    periodData.Add(period);
                }
                return periodData;
            }
            catch (Exception)
            {
                throw;
            }
        }

        private int AddDeviation(decimal devAcPy, decimal sumH, decimal dePy, decimal sumC, Dictionary<string, string> deviationsKey, int negmultipication, FsTrafLiType trafLiType)
        {
            int deviationStatus = 0;

            decimal trafLiTypeDeviation = trafLiType switch
            {
                FsTrafLiType.DeviationAction => devAcPy,
                FsTrafLiType.Deviation => sumH,
                _ => dePy * -1
            };

            if (deviationsKey?.Any() == true)
            {
                if (Math.Abs(sumC) < mrBudgetBlankLimitAmount)
                {
                    deviationStatus = trafLiTypeDeviation * negmultipication < 0 ? 0 : 2;
                }
                else
                {
                    //Set deviation flags
                    if (trafLiTypeDeviation * negmultipication >= decimal.Parse(deviationsKey["MONHTREP_DEVIATION_GREEN"], CultureInfo.InvariantCulture))
                        deviationStatus = 2;
                    else if (trafLiTypeDeviation * negmultipication >= decimal.Parse(deviationsKey["MONHTREP_DEVIATION_YELLOW"], CultureInfo.InvariantCulture)
                             && trafLiTypeDeviation * negmultipication < decimal.Parse(deviationsKey["MONHTREP_DEVIATION_GREEN"], CultureInfo.InvariantCulture))
                        deviationStatus = 1;
                    else if (trafLiTypeDeviation * negmultipication < decimal.Parse(deviationsKey["MONHTREP_DEVIATION_YELLOW"], CultureInfo.InvariantCulture))
                        deviationStatus = 0;
                }
            }

            return deviationStatus;
        }

        private int GetDeviationData(IGrouping<object, OperationalMPRepoData> c, Dictionary<string, string> deviationsKey, bool negateDeviation, FsTrafLiType trafLiType)
        {
            decimal sumC = c.Sum(z=>z.revisedBudget);
            decimal devAcPy= Math.Abs(sumC) < mrBudgetBlankLimitAmount ? 0 : Math.Round((c.Sum(z=>z.sumDevForAmt) / Math.Abs(sumC)) * 100, 1);
            decimal sumH = Math.Abs(sumC) < mrBudgetBlankLimitAmount ? 0 : Math.Round(((c.Sum(z => z.sumM) - sumC) / Math.Abs(sumC) * 100), 1);
            decimal deYd = c.Sum(z=>z.revisedBudgetYtd) - c.Sum(z=>z.acountingYtd);
            decimal dePy = Math.Abs(c.Sum(z => z.revisedBudgetYtd)) < mrBudgetBlankLimitAmount ? 0 : Math.Round(deYd / Math.Abs(c.Sum(z => z.revisedBudgetYtd)) * 100, 1);
            
            var negmultipication = negateDeviation ? -1 : 1;
            var data = AddDeviation(devAcPy, sumH, dePy, sumC, deviationsKey, negmultipication, trafLiType);

            return data;
        }

        private IEnumerable<OperationalMPRepoData> GetsumDevForAmt(IEnumerable<OperationalMPDeviastionReportData> deviationReportDat, IEnumerable<OperationalMPRepoData> sumMData, IEnumerable<OperationalMPRepoData> data)
        {
            return (from a in deviationReportDat
                    join b in sumMData on new {a=a.mpCode, b=a.projectCode,c=a.accountCode} equals  new { a = b.mpCode, b = b.projectCode, c = b.accountCode }
                    join c in data on new { a = a.mpCode, b = a.projectCode, c = a.accountCode } equals new { a = c.mpCode, b = c.projectCode, c = c.accountCode }
                    group new { a, b, c } by new { a.projectCode, a.projectName, a.mpName, a.mpCode, a.accountName, a.accountCode, a.incomeFlag } into c
                    select new OperationalMPRepoData
                    {
                        projectCode = c.Key.projectCode,
                        projectName = c.Key.projectName,
                        mpCode = c.Key.mpCode,
                        mpName = c.Key.mpName,
                        accountCode = c.Key.accountCode,
                        accountName = c.Key.accountName,
                        incomeFlag = c.Key.incomeFlag,
                        sumDevForAmt = (c.Sum(z => z.a.sumDevAc) + c.Sum(z => z.b.sumM)) - c.Sum(z=>z.c.revisedBudget),
                    }).OrderBy(x => x.mpCode).ToList();
        }

        private IEnumerable<OperationalMPRepoData> GetSumAcmChgFcData(int SelectedOrgLevel, IEnumerable<OperationalMPForecastData> forecastData)
        {
            List<int> changeForecastBudTypes = new List<int> {(int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level2),
                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level3),
                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level4),
                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level5),
                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level6),
                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level7),
                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level8),
                (int)(clsConstants.BudgetTypesForOrgLevels.Bud_Level9)};

            return (from a in forecastData
                    where changeForecastBudTypes.Contains(a.budgetType)
                    group a by new { a.projectCode, a.projectName, a.mpName, a.mpCode, a.accountName, a.accountCode, a.incomeFlag } into c
                    select new OperationalMPRepoData
                    {
                        projectCode = c.Key.projectCode,
                        projectName = c.Key.projectName,
                        mpCode = c.Key.mpCode,
                        mpName = c.Key.mpName,
                        accountCode = c.Key.accountCode,
                        accountName = c.Key.accountName,
                        incomeFlag = c.Key.incomeFlag,
                        acmChgFc = c.Sum(x => x.sumAmtYear1)
                    }).OrderBy(x => x.mpCode).ToList();
        }

        // Helper to determine if a row consists entirely of zero values
        bool IsZeroRow(OperationalMPGridData row)
        {
            return row.acountingYtd == 0 &&
                   row.deviationYtdKr == 0 &&
                   row.originalBudget == 0 &&
                   row.revisedBudgetYtd == 0 &&
                   row.sumBudgetChanges == 0 &&
                   row.revisedBudget == 0 &&
                   row.notApprBudChng == 0 &&
                   row.revBudgetInclNotApprBudChng == 0 &&
                   row.propForecast == 0 &&
                   row.forecastChange == 0 &&
                   row.newForecast == 0 &&
                   row.deviationKr == 0 &&
                   row.deviationPerc == 0 &&
                   row.changePreviousForecast == 0;
        }

        protected virtual async Task<IEnumerable<OperationalMPGridData>> GetAndFormatOperationalGridData(UserData userDetails, OperationalGridInputHelper input,bool negateDeviation)
        {


            ClsOrgVersionSpecificContent OrgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userDetails.user_name, input.forecastePeriod);
            var dataAsync = _rUow.MROperationalMainProjectReportRepository.GetOperationalMPRepoData(userDetails.tenant_id, input, OrgVersionContent.orgVersion);// warehouse data
            var forecastDataAsync = _rUow.MROperationalMainProjectReportRepository.GetOperationalMPForecastData(userDetails.tenant_id, input, OrgVersionContent.orgVersion);//tbu forecast data
            var getAllstatusDataAsync = _rUow.MROperationalMainProjectReportRepository.GetAllOperationalStatusData(userDetails.tenant_id, input.forecastePeriod);
            var deviationReportDataAsync = _rUow.MROperationalMainProjectReportRepository.GetOperationalMPDeviationReporttData(userDetails.tenant_id, input, OrgVersionContent.orgVersion);//tbu forecast data
            await Task.WhenAll(dataAsync, forecastDataAsync, getAllstatusDataAsync, deviationReportDataAsync);

            var resultFlag = await _rUow.MRFinStatusRepository.GetPeriodSetup(userDetails.tenant_id, input.forecastePeriod);// period setup data
          
            //fetch orgLevel info
            int nextOrgLevelJumpLevels = 0;

            int nextOrgLevelRptSetup = await _mrfUtility.GetNextOrgLevelFinStatusReportingAsync(userDetails.user_name, input.orgLevel, input.forecastePeriod);
            nextOrgLevelJumpLevels = nextOrgLevelRptSetup - input.orgLevel;

            int serviceUnitOrgLevel = OrgVersionContent.lstOrgLevel.FirstOrDefault(x => x.forecast_flag == 1)!.org_level;
            int lastOrgLevelRptSetup = await _mrfUtility.GetLastOrgLevelFinStatusReportingAsync(userDetails.user_name, input.forecastePeriod);
            bool isLastOrgLevelSunit = (lastOrgLevelRptSetup == serviceUnitOrgLevel);

            var data = dataAsync.Result;
            var forecastData = forecastDataAsync.Result;
            var getAllstatusData = getAllstatusDataAsync.Result;
            var deviationReportDat = deviationReportDataAsync.Result;

            data = !string.IsNullOrEmpty(input.selectedMPCode) && input.selectedMPCode != "-1" ? data.Where(z => z.mpCode == input.selectedMPCode).ToList() : data;

            List<string> disctincMpCode = data.Select(z => z.mpCode).Distinct().ToList();
            forecastData = forecastData.Where(z => disctincMpCode.Contains(z.mpCode)).ToList();
            deviationReportDat = deviationReportDat.Where(z => disctincMpCode.Contains(z.mpCode)).ToList();
            forecastData = !string.IsNullOrEmpty(input.selectedMPCode) && input.selectedMPCode != "-1" ? forecastData.Where(z => z.mpCode == input.selectedMPCode).ToList() : forecastData;

            //SumG
            var SumGData = GetSumGData(OrgVersionContent, input.orgLevel, forecastData, nextOrgLevelJumpLevels, isLastOrgLevelSunit, resultFlag);

            //SumM
            var SumMData = GetSumMData(input.orgLevel, forecastData, nextOrgLevelJumpLevels, resultFlag);

            //SumFR
            var SumFRData = GetSumFRData(input.orgLevel, forecastData, nextOrgLevelJumpLevels, serviceUnitOrgLevel, isLastOrgLevelSunit);


            //acmChgFc
            var acmChgFc = GetSumAcmChgFcData(input.orgLevel, forecastData);

            //sumDevForAmt
            var SumDevForAmt = GetsumDevForAmt(deviationReportDat, SumMData, data);


            data = data.Union(SumGData).Union(SumMData).Union(SumFRData).Union(acmChgFc).Union(SumDevForAmt);

            var deviationsKey = await _rUow.MRFinStatusRepository.GetDeviationsKey(userDetails);
           return FormatGridData(userDetails.user_name, data, negateDeviation, getAllstatusData, deviationsKey, input.forecastePeriod);


        }


    }
}
