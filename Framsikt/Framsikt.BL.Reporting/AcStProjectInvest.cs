#pragma warning disable CS8625

#pragma warning disable CS8600
#pragma warning disable CS8601
#pragma warning disable CS8602
#pragma warning disable CS8603
#pragma warning disable CS8604

using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Globalization;
namespace Framsikt.BL.Reporting
{
    public class AcStProjectInvest : IAcStProjectInvest
    {
        private readonly IUtility _utility;
        private readonly IAppDataCache _cache;
        private readonly IBusinessPlanGoalsTarget _bpTarget;

        public AcStProjectInvest(IServiceProvider container)
        {
            _utility = container.GetRequiredService<IUtility>();
            _cache = container.GetRequiredService<IAppDataCache>();
            _bpTarget = container.GetRequiredService<IBusinessPlanGoalsTarget>();
        }

        public Dictionary<string, string> GetPageTitles(string userId)
        {
            Dictionary<string, string> pageTitles = new Dictionary<string, string>();
            UserData userDetails = _utility.GetUserDetails(userId);
            Dictionary<string, clsLanguageString> langStringValues = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "AccountStatementReport");

            pageTitles.Add((langStringValues.FirstOrDefault(v => v.Key == "AcStProjectInvestHeading")).Key,
                           (langStringValues.FirstOrDefault(v => v.Key == "AcStProjectInvestHeading").Value).LangText);

            return pageTitles;
        }

        public async Task<dynamic> GetAccProjectInvestments(string userId,
                                                            int accountPeriod,
                                                            int viewType,
                                                            Protfolio portCodes,
                                                            List<string> sOrgAreas,
                                                            List<string> sServAreas,
                                                            List<string> projCodes,
                                                            List<string> deptCodes,
                                                            List<string> funcCodes,
                                                            List<string> projMgrs,
                                                            List<string> projOwrs,
                                                            List<string> accCodes)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            string divideByWhat = await _utility.CheckOnDivideByWhatAsync(userId, "ACCPROJ_INV_DIVIDEBY");

            int divideByNumber = 1;
            if (divideByWhat.ToLower() == "million".ToLower())
                divideByNumber = 1000000;
            else if (divideByWhat.ToLower() == "thousand".ToLower())
                divideByNumber = 1000;

            int numProjGrpLevels = 0;
            if (viewType == 1)
            {
                var tcoProjLvl = dbContext.tco_proj_level.Where(x => x.fk_tenant_id == userDetails.tenant_id).OrderByDescending(x => x.projLevel).FirstOrDefault();
                if (tcoProjLvl != null)
                    numProjGrpLevels = tcoProjLvl.projLevel;
            }

            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "AccountData");
            //dynamic accProjInvGrid = new JObject();

            //accProjInvGrid.Title = ((langStrings.FirstOrDefault(v => v.Key == "AccProjInvGridTitle")).Value).LangText;
            //accProjInvGrid.Description = ((langStrings.FirstOrDefault(v => v.Key == "AccProjInvGridDesc")).Value).LangText;

            //dynamic columnsArray = GetGridColumns(langStrings);
            //accProjInvGrid.Add("columns", columnsArray);

            var gridData = new JArray();

            //accProjInvGrid.Add("dataSource", gridData);
            List<AccInvestHelper> accProjInvData = await FetchAccProjectInvData(userId, accountPeriod, viewType, numProjGrpLevels, portCodes, sOrgAreas, sServAreas, projCodes, deptCodes, funcCodes, projMgrs, projOwrs, accCodes);
            foreach (var item in accProjInvData)
            {
                dynamic row = new JObject();
                row.id = item.level2Id;
                row.parentId = item.level1Id;
                row.projectAcInv = item.displayId == "0" ? "-0000" + " " + ((langStrings.FirstOrDefault(v => v.Key == "AccProjInvGrid_GrpMainPrjInvalCode")).Value).LangText : item.displayId + " " + item.level2Description;
                row.totCostBudget = item.totalCostBudget / divideByNumber;
                row.actualAccPrevYr = item.actualAccountPrevYr / divideByNumber;
                row.origBudgetYr = item.originBudgetYr / divideByNumber;
                row.adjustCal = item.adjustmentCal / divideByNumber;
                row.reviBudgetYr = item.revisedBudgetYr / divideByNumber;
                row.actualAccYr = item.actualAccountYr / divideByNumber;
                row.restBudgetYrCal = item.restBudgetYrCal / divideByNumber;
                row.usageYrAcPcCal = item.usageYrAccountPcCal;
                row.totAcc = item.totalAccount / divideByNumber;
                row.restBudTotCal = item.restBudgetTotalCal / divideByNumber;
                row.usageTotAcPc = item.usageTotalAccountPc;
                row.unApprovedChanges = item.unApprovedChanges / divideByNumber;
                row.approvedCost = item.approvedCost / divideByNumber;
                row.costEstimate = item.costEstimate / divideByNumber;
                row.unApprovedChanges = item.unApprovedChanges / divideByNumber;
                row.unAprTotalYearlyBud = item.unAprTotalYearlyBud / divideByNumber;
                gridData.Add(row);
            }

            //return accProjInvGrid;
            return gridData;
        }

        private async Task<List<AccInvestHelper>> FetchAccProjectInvData(string userId,
                                                                         int accountPeriod,
                                                                         int viewType,
                                                                         int numPrjGrpLvls,
                                                                         Protfolio portCodes,
                                                                         List<string> sOrgAreas,
                                                                         List<string> sServAreas,
                                                                         List<string> projCodes,
                                                                         List<string> deptCodes,
                                                                         List<string> funcCodes,
                                                                         List<string> projMgrs,
                                                                         List<string> projOwrs,
                                                                         List<string> accCodes)
        {
            List<AccInvestHelper> accProjInvData = await FetchAccProjectInvBaseData(userId, accountPeriod, true);
            accProjInvData = ApplyFilter(userId, accProjInvData, portCodes, sOrgAreas, sServAreas, projCodes, deptCodes, funcCodes, projMgrs, projOwrs, accCodes);
            accProjInvData = InsertHigherLevelAggRecs(userId, viewType, numPrjGrpLvls, accProjInvData);
            return accProjInvData;
        }

        private async Task<List<AccInvestHelper>> FetchAccProjectInvBaseData(string userId,
                                                                             int accountPeriod,
                                                                             bool includePrevYr)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            int clientId = userDetails.client_id;
            List<AccInvestHelper> accProjInvData = new List<AccInvestHelper>();
            List<AccInvestHelper> accProjInvRetn = new List<AccInvestHelper>();
            TimeSpan cacheAccDataTimeOut = new TimeSpan(0, 1, 0);
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "AccountData");

            string strBaseData = includePrevYr ? await _cache.GetStringForUserAsync(clientId, userDetails.tenant_id, userId, "cacheAccProjInvBaseData" + "_" + accountPeriod)
                                               : await _cache.GetStringForUserAsync(clientId, userDetails.tenant_id, userId, "cacheAccProjInvCuYrData" + "_" + accountPeriod);
            if (!string.IsNullOrEmpty(strBaseData))
            {
                accProjInvData = JsonConvert.DeserializeObject<List<AccInvestHelper>>(strBaseData);
                return accProjInvData;
            }
            else
            {
                var accInvCurYrAsync = QrFetchAccInvCurYear(userId, accountPeriod);
                var accInvCurPeriodAsync = QrFetchAccInvCurPeriod(userId, accountPeriod);

                await Task.WhenAll(accInvCurYrAsync, accInvCurPeriodAsync);

                List<AccInvestHelper> accInvCurYr = accInvCurYrAsync.Result;
                List<AccInvestHelper> accInvCurPeriod = accInvCurPeriodAsync.Result;
                accProjInvData.AddRange(accInvCurYr);

                accProjInvData = accProjInvData.Union(accInvCurPeriod).ToList();

                #region Fetch based on user role

                IEnumerable<int> userRoleById = await _utility.GetUserRoleIdsAsync(userId);
                if ((userRoleById.Contains(5) || userRoleById.Contains(22)) && !userRoleById.Any(x => x < 5))
                {
                    List<InvKeyValuePair> projMainproj = GetMainProjectOrProjList(userId, new List<string>() { userDetails.pk_id.ToString() });
                    List<string> projList = projMainproj.Any(x => x.value == "P") ? projMainproj.Where(y => y.value == "P").Select(z => z.key).ToList() : new List<string>();
                    List<string> MainjList = projMainproj.Any(x => x.value == "MP") ? projMainproj.Where(y => y.value == "MP").Select(z => z.key).ToList() : new List<string>();
                    accProjInvData = accProjInvData.Where(x => projList.Contains(x.projectCode) || MainjList.Contains(x.mainProjectCode)).ToList();
                    accInvCurYr = accInvCurYr.Where(x => projList.Contains(x.projectCode) || MainjList.Contains(x.mainProjectCode)).ToList();
                }

                #endregion Fetch based on user role

                foreach (var item in accProjInvData)
                {
                    item.totalAccount = item.actualAccountCurYr + item.actualAccountPrevYr;
                    item.restBudgetTotalCal = item.totalCostBudget - item.totalAccount;
                    item.usageTotalAccountPc = item.totalCostBudget != 0 ? item.totalAccount / item.totalCostBudget * 100 : 0;

                    item.usageYrAccountPcCal = item.revisedBudgetYr != 0 ? item.actualAccountYr / item.revisedBudgetYr : 0;

                    if (!((item.mainProjectCode == "0" || item.projectCode == "0") && item.revisedBudgetYr == 0 && item.actualAccountYr == 0))
                        accProjInvRetn.Add(item);
                }

                foreach (var pItem in accProjInvRetn.Where(x => (x.mainProjectCode == "0" || x.mainProjectCode == "") && x.projectCode != "" && x.projectCode != "0" && x.projectName.ToLower().Contains("manglende")))
                {
                    pItem.mainProjectCode = "-0000";
                    pItem.mainProjectName = ((langStrings.FirstOrDefault(v => v.Key == "AccProjInvGrid_GrpProjectWiFkCodes")).Value).LangText;
                }

                strBaseData = JsonConvert.SerializeObject(accProjInvRetn);
                string strCurYrData = JsonConvert.SerializeObject(accInvCurYr);

                await _cache.SetStringForUserAsync(clientId, userDetails.tenant_id, userId, "cacheAccProjInvBaseData" + "_" + accountPeriod, strBaseData, cacheAccDataTimeOut);
                await _cache.SetStringForUserAsync(clientId, userDetails.tenant_id, userId, "cacheAccProjInvCuYrData" + "_" + accountPeriod, strCurYrData, cacheAccDataTimeOut);
                return includePrevYr ? accProjInvRetn : accInvCurYr;
            }
        }

        private async Task<List<AccInvestHelper>> QrFetchAccInvCurYear(string userId, int accountPeriod)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDbContextForParallelReadAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            int budgetYear = accountPeriod / 100;
            tenantDbContext.Database.SetCommandTimeout(300);

            List<AccInvestHelper> accProjInvData = await (from vai in tenantDbContext.vw_acc_investments
                                                          where vai.fk_tenant_id == userDetails.tenant_id
                                                             && vai.budget_year == budgetYear
                                                          group vai by new
                                                          {
                                                              portfolioCode = vai.fk_portfolio_code,
                                                              portfolioName = vai.portfolio_name,
                                                              projGrpId1 = vai.proj_gr_1,
                                                              projGrpName1 = vai.proj_gr_name_1,
                                                              projGrpId2 = vai.proj_gr_2,
                                                              projGrpName2 = vai.proj_gr_name_2,
                                                              projGrpId3 = vai.proj_gr_3,
                                                              projGrpName3 = vai.proj_gr_name_3,
                                                              projGrpId4 = vai.proj_gr_4,
                                                              projGrpName4 = vai.proj_gr_name_4,
                                                              projGrpId5 = vai.proj_gr_5,
                                                              projGrpName5 = vai.proj_gr_name_5,
                                                              mainProjectCode = vai.pk_main_project_code,
                                                              mainProjectName = vai.main_project_name,
                                                              projectCode = vai.fk_project_code,
                                                              projectName = vai.project,
                                                              accountGroupId = vai.level_1_id,
                                                              accountGroupDesc = vai.level_1_description,
                                                              orgId = vai.fk_org_id,
                                                              orgName = vai.org_name,
                                                              serviceId = vai.service_id,
                                                              accountCode = vai.fk_account_code,
                                                              accountName = vai.account,
                                                              deptCode = vai.fk_department_code,
                                                              funcCode = vai.fk_function_code,
                                                              projectMgr = vai.project_manager,
                                                              investmentId = vai.pk_investment_id,
                                                              investmentName = vai.investment,
                                                              accountTypeId = vai.line_group_id,
                                                              accountTypeName = vai.line_group
                                                          } into grp
                                                          select new AccInvestHelper
                                                          {
                                                              portfolioCode = grp.Key.portfolioCode ?? string.Empty,
                                                              portfolioName = grp.Key.portfolioName ?? string.Empty,
                                                              projGrpId1 = grp.Key.projGrpId1 ?? string.Empty,
                                                              projGrpName1 = grp.Key.projGrpName1 ?? string.Empty,
                                                              projGrpId2 = grp.Key.projGrpId2 ?? string.Empty,
                                                              projGrpName2 = grp.Key.projGrpName2 ?? string.Empty,
                                                              projGrpId3 = grp.Key.projGrpId3 ?? string.Empty,
                                                              projGrpName3 = grp.Key.projGrpName3 ?? string.Empty,
                                                              projGrpId4 = grp.Key.projGrpId4 ?? string.Empty,
                                                              projGrpName4 = grp.Key.projGrpName4 ?? string.Empty,
                                                              projGrpId5 = grp.Key.projGrpId5 ?? string.Empty,
                                                              projGrpName5 = grp.Key.projGrpName5 ?? string.Empty,
                                                              mainProjectCode = grp.Key.mainProjectCode ?? string.Empty,
                                                              mainProjectName = grp.Key.mainProjectName ?? string.Empty,
                                                              projectCode = grp.Key.projectCode ?? string.Empty,
                                                              projectName = grp.Key.projectName ?? string.Empty,
                                                              accountGroupId = grp.Key.accountGroupId ?? string.Empty,
                                                              accountGroupDesc = grp.Key.accountGroupDesc ?? string.Empty,
                                                              orgId = grp.Key.orgId ?? string.Empty,
                                                              orgName = grp.Key.orgName ?? string.Empty,
                                                              serviceId = grp.Key.serviceId ?? string.Empty,
                                                              accountCode = grp.Key.accountCode ?? string.Empty,
                                                              accountName = grp.Key.accountName ?? string.Empty,
                                                              deptCode = grp.Key.deptCode ?? string.Empty,
                                                              funcCode = grp.Key.funcCode ?? string.Empty,
                                                              projectMgr = grp.Key.projectMgr ?? string.Empty,
                                                              investmentId = grp.Key.investmentId ?? string.Empty,
                                                              investmentName = grp.Key.investmentName ?? string.Empty,
                                                              totalCostBudget = grp.Sum(x => x.net_cost),
                                                              originBudgetYr = grp.Sum(x => x.org_budget_year_1),
                                                              revisedBudgetYr = grp.Sum(x => x.fp_year_1_amount),
                                                              adjustmentCal = grp.Sum(x => x.fp_year_1_amount) - grp.Sum(x => x.org_budget_year_1),
                                                              actualAccountCurYr = grp.Sum(x => x.accounting_year_1),
                                                              restBudgetYrCal = grp.Sum(x => x.fp_year_1_amount) - grp.Sum(x => x.accounting_year_1),
                                                              actualAccountPrevYr = Math.Round(grp.Sum(x => x.accounting_prev_year), 2),
                                                              unApprovedChanges = grp.Sum(x => x.unaprv_bud_change),
                                                              accountTypeID = grp.Key.accountTypeId,
                                                              accountTypeName = grp.Key.accountTypeName ?? string.Empty,
                                                              approvedCost = grp.Sum(x => x.approval_cost),
                                                              costEstimate = grp.Sum(x => x.cost_estimate_p50),
                                                              unAprTotalYearlyBud = grp.Sum(x => x.fp_year_1_amount) + grp.Sum(x => x.unaprv_bud_change),
                                                          }).TagWith("Use hint: recompile").ToListAsync();

            return accProjInvData;
        }

        private async Task<List<AccInvestHelper>> QrFetchAccInvCurPeriod(string userId, int accountPeriod)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDbContextForParallelReadAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            int budgetYear = accountPeriod / 100;
            tenantDbContext.Database.SetCommandTimeout(300);

            List<AccInvestHelper> accProjInvData = await (from vai in tenantDbContext.vw_acc_investments
                                                          where vai.fk_tenant_id == userDetails.tenant_id
                                                             && vai.budget_year == budgetYear
                                                             && vai.period <= accountPeriod
                                                          //&& vai.report == "INVREPORT"
                                                          group vai by new
                                                          {
                                                              portfolioCode = vai.fk_portfolio_code,
                                                              portfolioName = vai.portfolio_name,
                                                              projGrpId1 = vai.proj_gr_1,
                                                              projGrpName1 = vai.proj_gr_name_1,
                                                              projGrpId2 = vai.proj_gr_2,
                                                              projGrpName2 = vai.proj_gr_name_2,
                                                              projGrpId3 = vai.proj_gr_3,
                                                              projGrpName3 = vai.proj_gr_name_3,
                                                              projGrpId4 = vai.proj_gr_4,
                                                              projGrpName4 = vai.proj_gr_name_4,
                                                              projGrpId5 = vai.proj_gr_5,
                                                              projGrpName5 = vai.proj_gr_name_5,
                                                              mainProjectCode = vai.pk_main_project_code,
                                                              mainProjectName = vai.main_project_name,
                                                              projectCode = vai.fk_project_code,
                                                              projectName = vai.project,
                                                              accountGroupId = vai.level_1_id,
                                                              accountGroupDesc = vai.level_1_description,
                                                              orgId = vai.fk_org_id,
                                                              orgName = vai.org_name,
                                                              serviceId = vai.service_id,
                                                              accountCode = vai.fk_account_code,
                                                              accountName = vai.account,
                                                              deptCode = vai.fk_department_code,
                                                              funcCode = vai.fk_function_code,
                                                              projectMgr = vai.project_manager,
                                                              investmentId = vai.pk_investment_id,
                                                              investmentName = vai.investment,
                                                              accountTypeId = vai.line_group_id,
                                                              accountTypeName = vai.line_group
                                                          } into grp
                                                          select new AccInvestHelper
                                                          {
                                                              portfolioCode = grp.Key.portfolioCode ?? string.Empty,
                                                              portfolioName = grp.Key.portfolioName ?? string.Empty,
                                                              projGrpId1 = grp.Key.projGrpId1 ?? string.Empty,
                                                              projGrpName1 = grp.Key.projGrpName1 ?? string.Empty,
                                                              projGrpId2 = grp.Key.projGrpId2 ?? string.Empty,
                                                              projGrpName2 = grp.Key.projGrpName2 ?? string.Empty,
                                                              projGrpId3 = grp.Key.projGrpId3 ?? string.Empty,
                                                              projGrpName3 = grp.Key.projGrpName3 ?? string.Empty,
                                                              projGrpId4 = grp.Key.projGrpId4 ?? string.Empty,
                                                              projGrpName4 = grp.Key.projGrpName4 ?? string.Empty,
                                                              projGrpId5 = grp.Key.projGrpId5 ?? string.Empty,
                                                              projGrpName5 = grp.Key.projGrpName5 ?? string.Empty,
                                                              mainProjectCode = grp.Key.mainProjectCode ?? string.Empty,
                                                              mainProjectName = grp.Key.mainProjectName ?? string.Empty,
                                                              projectCode = grp.Key.projectCode ?? string.Empty,
                                                              projectName = grp.Key.projectName ?? string.Empty,
                                                              orgId = grp.Key.orgId ?? string.Empty,
                                                              orgName = grp.Key.orgName ?? string.Empty,
                                                              serviceId = grp.Key.serviceId ?? string.Empty,
                                                              accountGroupId = grp.Key.accountGroupId ?? string.Empty,
                                                              accountGroupDesc = grp.Key.accountGroupDesc ?? string.Empty,
                                                              accountCode = grp.Key.accountCode ?? string.Empty,
                                                              accountName = grp.Key.accountName ?? string.Empty,
                                                              deptCode = grp.Key.deptCode ?? string.Empty,
                                                              funcCode = grp.Key.funcCode ?? string.Empty,
                                                              projectMgr = grp.Key.projectMgr ?? string.Empty,
                                                              investmentId = grp.Key.investmentId ?? string.Empty,
                                                              investmentName = grp.Key.investmentName ?? string.Empty,
                                                              actualAccountYr = grp.Sum(x => x.accounting_year_1),
                                                              //unApprovedChanges = grp.Sum(x => x.unaprv_bud_change),          to remove duplicate data
                                                              accountTypeID = grp.Key.accountTypeId,
                                                              accountTypeName = grp.Key.accountTypeName ?? string.Empty,
                                                          }).TagWith("Use hint: recompile").ToListAsync();

            return accProjInvData;
        }

        private List<AccInvestHelper> InsertHigherLevelAggRecs(string userId, int viewType, int numPrjGrpLvls, List<AccInvestHelper> rowData)
        {
            List<AccInvestHelper> hLevelAggrRecs = new List<AccInvestHelper>();
            UserData userDetails = _utility.GetUserDetails(userId);
            Dictionary<string, clsLanguageString> langStrings = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "AccountData");
            string totalRowDesc = ((langStrings.FirstOrDefault(v => v.Key == "AccProjInvGridSumDesc")).Value).LangText;
            string undefPrjGrpLvl = ((langStrings.FirstOrDefault(v => v.Key == "AccProjInvGridUndefProjLvl")).Value).LangText;
            if (viewType == 0)
            {
                bool usePortfolio = _utility.GetActiveParameterValue(userId, "INV_USE_PORTFOLIO").ToLower() == "true";

                List<AccInvestHelper> hPortRecs = AggregateLevel1PortOrOrg(usePortfolio, rowData);
                List<AccInvestHelper> hMainProRecs = AggregateLevel2MainPro(usePortfolio, rowData);
                List<AccInvestHelper> hProjRecs = AggregateLevel3Project(usePortfolio, rowData);
                List<AccInvestHelper> hAccGrpRecs = AggregateLevel4AccGrp(usePortfolio, rowData);
                List<AccInvestHelper> hAccCnRecs = AggregateAccountCode(usePortfolio, rowData);

                hLevelAggrRecs.AddRange(hPortRecs);
                hLevelAggrRecs.AddRange(hMainProRecs);
                hLevelAggrRecs.AddRange(hProjRecs);
                hLevelAggrRecs.AddRange(hAccGrpRecs);
                hLevelAggrRecs.AddRange(hAccCnRecs);
            }
            else
            {
                string[] projGrpDelims = new string[] { "", "", "", "", "" };
                switch (numPrjGrpLvls)
                {
                    case 5:
                        projGrpDelims = new string[] { "_z5", "_z4", "_z3", "_z2", "_z1" };
                        hLevelAggrRecs.AddRange(AggregateProjGrp5(rowData, undefPrjGrpLvl, projGrpDelims));
                        hLevelAggrRecs.AddRange(AggregateProjGrp4(false, rowData, undefPrjGrpLvl, projGrpDelims));
                        hLevelAggrRecs.AddRange(AggregateProjGrp3(false, rowData, undefPrjGrpLvl, projGrpDelims));
                        hLevelAggrRecs.AddRange(AggregateProjGrp2(false, rowData, undefPrjGrpLvl, projGrpDelims));
                        hLevelAggrRecs.AddRange(AggregateProjGrp1(false, rowData, undefPrjGrpLvl, projGrpDelims));
                        break;

                    case 4:
                        projGrpDelims = new string[] { "", "_z4", "_z3", "_z2", "_z1" };
                        hLevelAggrRecs.AddRange(AggregateProjGrp4(true, rowData, undefPrjGrpLvl, projGrpDelims));
                        hLevelAggrRecs.AddRange(AggregateProjGrp3(false, rowData, undefPrjGrpLvl, projGrpDelims));
                        hLevelAggrRecs.AddRange(AggregateProjGrp2(false, rowData, undefPrjGrpLvl, projGrpDelims));
                        hLevelAggrRecs.AddRange(AggregateProjGrp1(false, rowData, undefPrjGrpLvl, projGrpDelims));
                        break;

                    case 3:
                        projGrpDelims = new string[] { "", "", "_z3", "_z2", "_z1" };
                        hLevelAggrRecs.AddRange(AggregateProjGrp3(true, rowData, undefPrjGrpLvl, projGrpDelims));
                        hLevelAggrRecs.AddRange(AggregateProjGrp2(false, rowData, undefPrjGrpLvl, projGrpDelims));
                        hLevelAggrRecs.AddRange(AggregateProjGrp1(false, rowData, undefPrjGrpLvl, projGrpDelims));
                        break;

                    case 2:
                        projGrpDelims = new string[] { "", "", "", "_z2", "_z1" };
                        hLevelAggrRecs.AddRange(AggregateProjGrp2(true, rowData, undefPrjGrpLvl, projGrpDelims));
                        hLevelAggrRecs.AddRange(AggregateProjGrp1(false, rowData, undefPrjGrpLvl, projGrpDelims));
                        break;

                    case 1:
                        projGrpDelims = new string[] { "", "", "", "", "_z1" };
                        hLevelAggrRecs.AddRange(AggregateProjGrp1(true, rowData, undefPrjGrpLvl, projGrpDelims));
                        break;
                }
                bool IsNoProjGrp = numPrjGrpLvls < 1;
                hLevelAggrRecs.AddRange(AggregateProjGrpMainPro(IsNoProjGrp, rowData, projGrpDelims));
                hLevelAggrRecs.AddRange(AggregateProjGrpProject(IsNoProjGrp, rowData, projGrpDelims));
                hLevelAggrRecs.AddRange(AggregateProjGrpAccGrp(IsNoProjGrp, rowData, projGrpDelims));
                hLevelAggrRecs.AddRange(AggregateProjGrpAccCdNm(IsNoProjGrp, rowData, projGrpDelims));
            }

            hLevelAggrRecs.AddRange(AggregateOverallTotal(rowData, totalRowDesc));
            return hLevelAggrRecs;
        }

        private List<AccInvestHelper> AggregateOverallTotal(List<AccInvestHelper> rowData, string totalRowDesc)
        {
            List<AccInvestHelper> retRecs = (from rd in rowData
                                             group rd by new { rd.totalRowId } into rdGrp
                                             select new AccInvestHelper
                                             {
                                                 level1Id = null,
                                                 level1Description = string.Empty,
                                                 level2Id = "-1",
                                                 level2Description = totalRowDesc,
                                                 displayId = "",
                                                 totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                 actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                 originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                 revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                 adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                 actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                 restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                 totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                 restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                 unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                 approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                 costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                 unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                             }).ToList();
            foreach (var item in retRecs)
            {
                item.usageYrAccountPcCal = item.revisedBudgetYr != 0 ? item.actualAccountYr / item.revisedBudgetYr * 100 : 0;
                item.usageTotalAccountPc = item.totalCostBudget != 0 ? item.totalAccount / item.totalCostBudget * 100 : 0;
            }
            return retRecs.OrderBy(x => x.displayId).ToList();
        }

        private List<AccInvestHelper> AggregateProjGrp5(List<AccInvestHelper> rowData, string undefStr, string[] projGrpDelims)
        {
            List<AccInvestHelper> retRecs = (from rd in rowData
                                             group rd by new { rd.projGrpId5, rd.projGrpName5 } into rdGrp
                                             select new AccInvestHelper
                                             {
                                                 level1Id = null,
                                                 level1Description = string.Empty,
                                                 level2Id = rdGrp.Key.projGrpId5 + projGrpDelims[0],
                                                 level2Description = rdGrp.Key.projGrpName5,
                                                 displayId = rdGrp.Key.projGrpId5,
                                                 totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                 actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                 originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                 revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                 adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                 actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                 restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                 totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                 restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                 unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                 approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                 costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                 unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                             }).ToList();
            foreach (var item in retRecs)
            {
                item.usageYrAccountPcCal = item.revisedBudgetYr != 0 ? item.actualAccountYr / item.revisedBudgetYr * 100 : 0;
                item.usageTotalAccountPc = item.totalCostBudget != 0 ? item.totalAccount / item.totalCostBudget * 100 : 0;

                if (string.IsNullOrEmpty(item.level2Description))
                    item.level2Description = undefStr + " 5";
            }
            return retRecs.OrderBy(x => x.displayId).ToList();
        }

        private List<AccInvestHelper> AggregateProjGrp4(bool isParentNull, List<AccInvestHelper> rowData, string undefStr, string[] projGrpDelims)
        {
            List<AccInvestHelper> retRecs = isParentNull ? (from rd in rowData
                                                            group rd by new { rd.projGrpId4, rd.projGrpName4 } into rdGrp
                                                            select new AccInvestHelper
                                                            {
                                                                level1Id = null,
                                                                level1Description = string.Empty,
                                                                level2Id = rdGrp.Key.projGrpId4 + projGrpDelims[1],
                                                                level2Description = rdGrp.Key.projGrpName4,
                                                                displayId = rdGrp.Key.projGrpId4,
                                                                totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                                actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                                originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                                revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                                adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                                actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                                restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                                totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                                restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                                unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                                approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                                costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                                unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                            }).ToList() :
                                                            (from rd in rowData
                                                             group rd by new { rd.projGrpId5, rd.projGrpName5, rd.projGrpId4, rd.projGrpName4 } into rdGrp
                                                             select new AccInvestHelper
                                                             {
                                                                 level1Id = rdGrp.Key.projGrpId5 + projGrpDelims[0],
                                                                 level1Description = rdGrp.Key.projGrpName5,
                                                                 level2Id = rdGrp.Key.projGrpId5 + projGrpDelims[0] + rdGrp.Key.projGrpId4 + projGrpDelims[1],
                                                                 level2Description = rdGrp.Key.projGrpName4,
                                                                 displayId = rdGrp.Key.projGrpId4,
                                                                 totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                                 actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                                 originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                                 revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                                 adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                                 actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                                 restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                                 totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                                 restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                                 unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                                 approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                                 costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                                 unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                             }).ToList();
            foreach (var item in retRecs)
            {
                item.usageYrAccountPcCal = item.revisedBudgetYr != 0 ? item.actualAccountYr / item.revisedBudgetYr * 100 : 0;
                item.usageTotalAccountPc = item.totalCostBudget != 0 ? item.totalAccount / item.totalCostBudget * 100 : 0;

                if (string.IsNullOrEmpty(item.level2Description))
                    item.level2Description = undefStr + " 4";
            }
            return retRecs.OrderBy(x => x.displayId).ToList();
        }

        private List<AccInvestHelper> AggregateProjGrp3(bool isParentNull, List<AccInvestHelper> rowData, string undefStr, string[] projGrpDelims)
        {
            List<AccInvestHelper> retRecs = isParentNull ? (from rd in rowData
                                                            group rd by new { rd.projGrpId3, rd.projGrpName3 } into rdGrp
                                                            select new AccInvestHelper
                                                            {
                                                                level1Id = null,
                                                                level1Description = string.Empty,
                                                                level2Id = rdGrp.Key.projGrpId3 + projGrpDelims[2],
                                                                level2Description = rdGrp.Key.projGrpName3,
                                                                displayId = rdGrp.Key.projGrpId3,
                                                                totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                                actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                                originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                                revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                                adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                                actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                                restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                                totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                                restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                                unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                                approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                                costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                                unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                            }).ToList() :
                                                            (from rd in rowData
                                                             group rd by new { rd.projGrpId5, rd.projGrpName5, rd.projGrpId4, rd.projGrpName4, rd.projGrpId3, rd.projGrpName3 } into rdGrp
                                                             select new AccInvestHelper
                                                             {
                                                                 level1Id = rdGrp.Key.projGrpId5 + projGrpDelims[0] + rdGrp.Key.projGrpId4 + projGrpDelims[1],
                                                                 level1Description = rdGrp.Key.projGrpName4,
                                                                 level2Id = rdGrp.Key.projGrpId5 + projGrpDelims[0] + rdGrp.Key.projGrpId4 + projGrpDelims[1] + rdGrp.Key.projGrpId3 + projGrpDelims[2],
                                                                 level2Description = rdGrp.Key.projGrpName3,
                                                                 displayId = rdGrp.Key.projGrpId3,
                                                                 totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                                 actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                                 originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                                 revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                                 adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                                 actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                                 restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                                 totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                                 restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                                 unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                                 approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                                 costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                                 unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                             }).ToList();
            foreach (var item in retRecs)
            {
                item.usageYrAccountPcCal = item.revisedBudgetYr != 0 ? item.actualAccountYr / item.revisedBudgetYr * 100 : 0;
                item.usageTotalAccountPc = item.totalCostBudget != 0 ? item.totalAccount / item.totalCostBudget * 100 : 0;

                if (string.IsNullOrEmpty(item.level2Description))
                    item.level2Description = undefStr + " 3";
            }
            return retRecs.OrderBy(x => x.displayId).ToList();
        }

        private List<AccInvestHelper> AggregateProjGrp2(bool isParentNull, List<AccInvestHelper> rowData, string undefStr, string[] projGrpDelims)
        {
            List<AccInvestHelper> retRecs = isParentNull ? (from rd in rowData
                                                            group rd by new { rd.projGrpId2, rd.projGrpName2 } into rdGrp
                                                            select new AccInvestHelper
                                                            {
                                                                level1Id = null,
                                                                level1Description = string.Empty,
                                                                level2Id = rdGrp.Key.projGrpId2 + projGrpDelims[3],
                                                                level2Description = rdGrp.Key.projGrpName2,
                                                                displayId = rdGrp.Key.projGrpId2,
                                                                totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                                actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                                originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                                revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                                adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                                actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                                restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                                totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                                restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                                unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                                approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                                costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                                unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                            }).ToList() :
                                                            (from rd in rowData
                                                             group rd by new { rd.projGrpId5, rd.projGrpName5, rd.projGrpId4, rd.projGrpName4, rd.projGrpId3, rd.projGrpName3, rd.projGrpId2, rd.projGrpName2 } into rdGrp
                                                             select new AccInvestHelper
                                                             {
                                                                 level1Id = rdGrp.Key.projGrpId5 + projGrpDelims[0] + rdGrp.Key.projGrpId4 + projGrpDelims[1] + rdGrp.Key.projGrpId3 + projGrpDelims[2],
                                                                 level1Description = rdGrp.Key.projGrpName3,
                                                                 level2Id = rdGrp.Key.projGrpId5 + projGrpDelims[0] + rdGrp.Key.projGrpId4 + projGrpDelims[1] + rdGrp.Key.projGrpId3 + projGrpDelims[2] + rdGrp.Key.projGrpId2 + projGrpDelims[3],
                                                                 level2Description = rdGrp.Key.projGrpName2,
                                                                 displayId = rdGrp.Key.projGrpId2,
                                                                 totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                                 actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                                 originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                                 revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                                 adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                                 actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                                 restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                                 totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                                 restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                                 unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                                 approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                                 costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                                 unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                             }).ToList();
            foreach (var item in retRecs)
            {
                item.usageYrAccountPcCal = item.revisedBudgetYr != 0 ? item.actualAccountYr / item.revisedBudgetYr * 100 : 0;
                item.usageTotalAccountPc = item.totalCostBudget != 0 ? item.totalAccount / item.totalCostBudget * 100 : 0;

                if (string.IsNullOrEmpty(item.level2Description))
                    item.level2Description = undefStr + " 2";
            }
            return retRecs.OrderBy(x => x.displayId).ToList();
        }

        private List<AccInvestHelper> AggregateProjGrp1(bool isParentNull, List<AccInvestHelper> rowData, string undefStr, string[] projGrpDelims)
        {
            List<AccInvestHelper> retRecs = isParentNull ? (from rd in rowData
                                                            group rd by new { rd.projGrpId1, rd.projGrpName1 } into rdGrp
                                                            select new AccInvestHelper
                                                            {
                                                                level1Id = null,
                                                                level1Description = string.Empty,
                                                                level2Id = rdGrp.Key.projGrpId1 + projGrpDelims[4],
                                                                level2Description = rdGrp.Key.projGrpName1,
                                                                displayId = rdGrp.Key.projGrpId1,
                                                                totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                                actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                                originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                                revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                                adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                                actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                                restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                                totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                                restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                                unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                                approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                                costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                                unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                            }).ToList() :
                                                            (from rd in rowData
                                                             group rd by new { rd.projGrpId5, rd.projGrpName5, rd.projGrpId4, rd.projGrpName4, rd.projGrpId3, rd.projGrpName3, rd.projGrpId2, rd.projGrpName2, rd.projGrpId1, rd.projGrpName1 } into rdGrp
                                                             select new AccInvestHelper
                                                             {
                                                                 level1Id = rdGrp.Key.projGrpId5 + projGrpDelims[0] + rdGrp.Key.projGrpId4 + projGrpDelims[1] + rdGrp.Key.projGrpId3 + projGrpDelims[2] + rdGrp.Key.projGrpId2 + projGrpDelims[3],
                                                                 level1Description = rdGrp.Key.projGrpName2,
                                                                 level2Id = rdGrp.Key.projGrpId5 + projGrpDelims[0] + rdGrp.Key.projGrpId4 + projGrpDelims[1] + rdGrp.Key.projGrpId3 + projGrpDelims[2] + rdGrp.Key.projGrpId2 + projGrpDelims[3] + rdGrp.Key.projGrpId1 + projGrpDelims[4],
                                                                 level2Description = rdGrp.Key.projGrpName1,
                                                                 displayId = rdGrp.Key.projGrpId1,
                                                                 totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                                 actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                                 originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                                 revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                                 adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                                 actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                                 restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                                 totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                                 restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                                 unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                                 approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                                 costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                                 unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                             }).ToList();
            foreach (var item in retRecs)
            {
                item.usageYrAccountPcCal = item.revisedBudgetYr != 0 ? item.actualAccountYr / item.revisedBudgetYr * 100 : 0;
                item.usageTotalAccountPc = item.totalCostBudget != 0 ? item.totalAccount / item.totalCostBudget * 100 : 0;

                if (string.IsNullOrEmpty(item.level2Description))
                    item.level2Description = undefStr + " 1";
            }
            return retRecs.OrderBy(x => x.displayId).ToList();
        }

        private List<AccInvestHelper> AggregateProjGrpMainPro(bool IsNoProjGrp, List<AccInvestHelper> rowData, string[] projGrpDelims)
        {
            List<AccInvestHelper> retRecs = IsNoProjGrp ? (from rd in rowData
                                                           group rd by new { rd.mainProjectCode, rd.mainProjectName } into rdGrp
                                                           select new AccInvestHelper
                                                           {
                                                               level1Id = null,
                                                               level1Description = string.Empty,
                                                               level2Id = rdGrp.Key.mainProjectCode + "x",
                                                               level2Description = rdGrp.Key.mainProjectName,
                                                               displayId = rdGrp.Key.mainProjectCode,
                                                               totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                               actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                               originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                               revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                               adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                               actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                               restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                               totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                               restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                               unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                               approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                               costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                               unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                           }).ToList() :
                                                            (from rd in rowData
                                                             group rd by new { rd.projGrpId5, rd.projGrpName5, rd.projGrpId4, rd.projGrpName4, rd.projGrpId3, rd.projGrpName3, rd.projGrpId2, rd.projGrpName2, rd.projGrpId1, rd.projGrpName1, rd.mainProjectCode, rd.mainProjectName } into rdGrp
                                                             select new AccInvestHelper
                                                             {
                                                                 level1Id = rdGrp.Key.projGrpId5 + projGrpDelims[0] + rdGrp.Key.projGrpId4 + projGrpDelims[1] + rdGrp.Key.projGrpId3 + projGrpDelims[2] + rdGrp.Key.projGrpId2 + projGrpDelims[3] + rdGrp.Key.projGrpId1 + projGrpDelims[4],
                                                                 level1Description = rdGrp.Key.projGrpName1,
                                                                 level2Id = rdGrp.Key.projGrpId5 + projGrpDelims[0] + rdGrp.Key.projGrpId4 + projGrpDelims[1] + rdGrp.Key.projGrpId3 + projGrpDelims[2] + rdGrp.Key.projGrpId2 + projGrpDelims[3] + rdGrp.Key.projGrpId1 + projGrpDelims[4] + rdGrp.Key.mainProjectCode + "x",
                                                                 level2Description = rdGrp.Key.mainProjectName,
                                                                 displayId = rdGrp.Key.mainProjectCode,
                                                                 totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                                 actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                                 originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                                 revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                                 adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                                 actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                                 restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                                 totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                                 restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                                 unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                                 approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                                 costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                                 unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                             }).ToList();
            foreach (var item in retRecs)
            {
                item.usageYrAccountPcCal = item.revisedBudgetYr != 0 ? item.actualAccountYr / item.revisedBudgetYr * 100 : 0;
                item.usageTotalAccountPc = item.totalCostBudget != 0 ? item.totalAccount / item.totalCostBudget * 100 : 0;
            }
            return retRecs.OrderBy(x => x.displayId).ToList();
        }

        private List<AccInvestHelper> AggregateProjGrpProject(bool IsNoProjGrp, List<AccInvestHelper> rowData, string[] projGrpDelims)
        {
            List<AccInvestHelper> retRecs = IsNoProjGrp ? (from rd in rowData
                                                           group rd by new { rd.mainProjectCode, rd.mainProjectName, rd.projectCode, rd.projectName } into rdGrp
                                                           select new AccInvestHelper
                                                           {
                                                               level1Id = rdGrp.Key.mainProjectCode + "x",
                                                               level1Description = rdGrp.Key.mainProjectName,
                                                               level2Id = rdGrp.Key.mainProjectCode + "x" + rdGrp.Key.projectCode + "_",
                                                               level2Description = rdGrp.Key.projectName,
                                                               displayId = rdGrp.Key.projectCode,
                                                               totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                               actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                               originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                               revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                               adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                               actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                               restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                               totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                               restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                               unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                               approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                               costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                               unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                           }).ToList() :
                                                            (from rd in rowData
                                                             group rd by new { rd.projGrpId5, rd.projGrpName5, rd.projGrpId4, rd.projGrpName4, rd.projGrpId3, rd.projGrpName3, rd.projGrpId2, rd.projGrpName2, rd.projGrpId1, rd.projGrpName1, rd.mainProjectCode, rd.mainProjectName, rd.projectCode, rd.projectName } into rdGrp
                                                             select new AccInvestHelper
                                                             {
                                                                 level1Id = rdGrp.Key.projGrpId5 + projGrpDelims[0] + rdGrp.Key.projGrpId4 + projGrpDelims[1] + rdGrp.Key.projGrpId3 + projGrpDelims[2] + rdGrp.Key.projGrpId2 + projGrpDelims[3] + rdGrp.Key.projGrpId1 + projGrpDelims[4] + rdGrp.Key.mainProjectCode + "x",
                                                                 level1Description = rdGrp.Key.mainProjectName,
                                                                 level2Id = rdGrp.Key.projGrpId5 + projGrpDelims[0] + rdGrp.Key.projGrpId4 + projGrpDelims[1] + rdGrp.Key.projGrpId3 + projGrpDelims[2] + rdGrp.Key.projGrpId2 + projGrpDelims[3] + rdGrp.Key.projGrpId1 + projGrpDelims[4] + rdGrp.Key.mainProjectCode + "x" + rdGrp.Key.projectCode + "_",
                                                                 level2Description = rdGrp.Key.projectName,
                                                                 displayId = rdGrp.Key.projectCode,
                                                                 totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                                 actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                                 originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                                 revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                                 adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                                 actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                                 restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                                 totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                                 restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                                 unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                                 approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                                 costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                                 unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                             }).ToList();
            foreach (var item in retRecs)
            {
                item.usageYrAccountPcCal = item.revisedBudgetYr != 0 ? item.actualAccountYr / item.revisedBudgetYr * 100 : 0;
                item.usageTotalAccountPc = item.totalCostBudget != 0 ? item.totalAccount / item.totalCostBudget * 100 : 0;
            }
            return retRecs.OrderBy(x => x.displayId).ToList();
        }

        private List<AccInvestHelper> AggregateProjGrpAccGrp(bool IsNoProjGrp, List<AccInvestHelper> rowData, string[] projGrpDelims)
        {
            List<AccInvestHelper> retRecs = IsNoProjGrp ? (from rd in rowData
                                                           group rd by new { rd.mainProjectCode, rd.mainProjectName, rd.projectCode, rd.projectName, rd.accountGroupId, rd.accountGroupDesc } into rdGrp
                                                           select new AccInvestHelper
                                                           {
                                                               level1Id = rdGrp.Key.mainProjectCode + "x" + rdGrp.Key.projectCode + "_",
                                                               level1Description = rdGrp.Key.mainProjectName,
                                                               level2Id = rdGrp.Key.mainProjectCode + "x" + rdGrp.Key.projectCode + "_" + rdGrp.Key.accountGroupId + "z",
                                                               level2Description = rdGrp.Key.accountGroupDesc,
                                                               displayId = rdGrp.Key.accountGroupId,
                                                               totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                               actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                               originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                               revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                               adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                               actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                               restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                               totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                               restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                               unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                               approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                               costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                               unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                           }).ToList() :
                                                            (from rd in rowData
                                                             group rd by new { rd.projGrpId5, rd.projGrpName5, rd.projGrpId4, rd.projGrpName4, rd.projGrpId3, rd.projGrpName3, rd.projGrpId2, rd.projGrpName2, rd.projGrpId1, rd.projGrpName1, rd.mainProjectCode, rd.mainProjectName, rd.projectCode, rd.projectName, rd.accountGroupId, rd.accountGroupDesc } into rdGrp
                                                             select new AccInvestHelper
                                                             {
                                                                 level1Id = rdGrp.Key.projGrpId5 + projGrpDelims[0] + rdGrp.Key.projGrpId4 + projGrpDelims[1] + rdGrp.Key.projGrpId3 + projGrpDelims[2] + rdGrp.Key.projGrpId2 + projGrpDelims[3] + rdGrp.Key.projGrpId1 + projGrpDelims[4] + rdGrp.Key.mainProjectCode + "x" + rdGrp.Key.projectCode + "_",
                                                                 level1Description = rdGrp.Key.mainProjectName,
                                                                 level2Id = rdGrp.Key.projGrpId5 + projGrpDelims[0] + rdGrp.Key.projGrpId4 + projGrpDelims[1] + rdGrp.Key.projGrpId3 + projGrpDelims[2] + rdGrp.Key.projGrpId2 + projGrpDelims[3] + rdGrp.Key.projGrpId1 + projGrpDelims[4] + rdGrp.Key.mainProjectCode + "x" + rdGrp.Key.projectCode + "_" + rdGrp.Key.accountGroupId + "z",
                                                                 level2Description = rdGrp.Key.accountGroupDesc,
                                                                 displayId = rdGrp.Key.accountGroupId,
                                                                 totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                                 actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                                 originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                                 revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                                 adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                                 actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                                 restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                                 totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                                 restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                                 unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                                 approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                                 costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                                 unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                             }).ToList();
            foreach (var item in retRecs)
            {
                item.usageYrAccountPcCal = item.revisedBudgetYr != 0 ? item.actualAccountYr / item.revisedBudgetYr * 100 : 0;
                item.usageTotalAccountPc = item.totalCostBudget != 0 ? item.totalAccount / item.totalCostBudget * 100 : 0;
            }
            return retRecs.OrderBy(x => x.displayId).ToList();
        }

        private List<AccInvestHelper> AggregateProjGrpAccCdNm(bool IsNoProjGrp, List<AccInvestHelper> rowData, string[] projGrpDelims)
        {
            List<AccInvestHelper> retRecs = IsNoProjGrp ? (from rd in rowData
                                                           group rd by new { rd.mainProjectCode, rd.mainProjectName, rd.projectCode, rd.projectName, rd.accountGroupId, rd.accountGroupDesc, rd.accountCode, rd.accountName } into rdGrp
                                                           select new AccInvestHelper
                                                           {
                                                               level1Id = rdGrp.Key.mainProjectCode + "x" + rdGrp.Key.projectCode + "_" + rdGrp.Key.accountGroupId + "z",
                                                               level1Description = rdGrp.Key.mainProjectName,
                                                               level2Id = rdGrp.Key.mainProjectCode + "x" + rdGrp.Key.projectCode + "_" + rdGrp.Key.accountGroupId + "z" + rdGrp.Key.accountCode,
                                                               level2Description = rdGrp.Key.accountName,
                                                               displayId = rdGrp.Key.accountCode,
                                                               totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                               actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                               originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                               revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                               adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                               actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                               restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                               totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                               restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                               unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                               approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                               costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                               unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                           }).ToList() :
                                                            (from rd in rowData
                                                             group rd by new { rd.projGrpId5, rd.projGrpName5, rd.projGrpId4, rd.projGrpName4, rd.projGrpId3, rd.projGrpName3, rd.projGrpId2, rd.projGrpName2, rd.projGrpId1, rd.projGrpName1, rd.mainProjectCode, rd.mainProjectName, rd.projectCode, rd.projectName, rd.accountGroupId, rd.accountGroupDesc, rd.accountCode, rd.accountName } into rdGrp
                                                             select new AccInvestHelper
                                                             {
                                                                 level1Id = rdGrp.Key.projGrpId5 + projGrpDelims[0] + rdGrp.Key.projGrpId4 + projGrpDelims[1] + rdGrp.Key.projGrpId3 + projGrpDelims[2] + rdGrp.Key.projGrpId2 + projGrpDelims[3] + rdGrp.Key.projGrpId1 + projGrpDelims[4] + rdGrp.Key.mainProjectCode + "x" + rdGrp.Key.projectCode + "_" + rdGrp.Key.accountGroupId + "z",
                                                                 level1Description = rdGrp.Key.mainProjectName,
                                                                 level2Id = rdGrp.Key.projGrpId5 + projGrpDelims[0] + rdGrp.Key.projGrpId4 + projGrpDelims[1] + rdGrp.Key.projGrpId3 + projGrpDelims[2] + rdGrp.Key.projGrpId2 + projGrpDelims[3] + rdGrp.Key.projGrpId1 + projGrpDelims[4] + rdGrp.Key.mainProjectCode + "x" + rdGrp.Key.projectCode + "_" + rdGrp.Key.accountGroupId + "z" + rdGrp.Key.accountCode,
                                                                 level2Description = rdGrp.Key.accountName,
                                                                 displayId = rdGrp.Key.accountCode,
                                                                 totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                                 actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                                 originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                                 revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                                 adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                                 actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                                 restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                                 totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                                 restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                                 unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                                 approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                                 costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                                 unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                             }).ToList();
            foreach (var item in retRecs)
            {
                item.usageYrAccountPcCal = item.revisedBudgetYr != 0 ? item.actualAccountYr / item.revisedBudgetYr * 100 : 0;
                item.usageTotalAccountPc = item.totalCostBudget != 0 ? item.totalAccount / item.totalCostBudget * 100 : 0;
            }
            return retRecs.OrderBy(x => x.displayId).ToList();
        }

        private List<AccInvestHelper> AggregateLevel1PortOrOrg(bool usePortfolio, List<AccInvestHelper> rowData)
        {
            List<AccInvestHelper> retRecs = usePortfolio ? (from rd in rowData
                                                            group rd by new { rd.portfolioCode, rd.portfolioName } into rdGrp
                                                            select new AccInvestHelper
                                                            {
                                                                level1Id = null,
                                                                level1Description = string.Empty,
                                                                level2Id = rdGrp.Key.portfolioCode + "_o",
                                                                level2Description = rdGrp.Key.portfolioName,
                                                                displayId = rdGrp.Key.portfolioCode,
                                                                totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                                actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                                originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                                revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                                adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                                actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                                restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                                totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                                restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                                unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                                approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                                costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                                unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                            }).ToList() :
                                                           (from rd in rowData
                                                            group rd by new { rd.orgId, rd.orgName } into rdGrp
                                                            select new AccInvestHelper
                                                            {
                                                                level1Id = null,
                                                                level1Description = string.Empty,
                                                                level2Id = rdGrp.Key.orgId + "_o",
                                                                level2Description = rdGrp.Key.orgName,
                                                                displayId = rdGrp.Key.orgId,
                                                                totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                                actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                                originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                                revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                                adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                                actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                                restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                                totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                                restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                                unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                                approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                                costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                                unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                            }).ToList();
            foreach (var item in retRecs)
            {
                item.usageYrAccountPcCal = item.revisedBudgetYr != 0 ? item.actualAccountYr / item.revisedBudgetYr * 100 : 0;
                item.usageTotalAccountPc = item.totalCostBudget != 0 ? item.totalAccount / item.totalCostBudget * 100 : 0;
            }
            return retRecs.OrderBy(x => x.displayId).ToList();
        }

        private List<AccInvestHelper> AggregateLevel2MainPro(bool usePortfolio, List<AccInvestHelper> rowData)
        {
            List<AccInvestHelper> hMainProRecs = usePortfolio ? (from rd in rowData
                                                                 group rd by new { rd.portfolioCode, rd.portfolioName, rd.mainProjectCode, rd.mainProjectName } into rdGrp
                                                                 select new AccInvestHelper
                                                                 {
                                                                     level1Id = rdGrp.Key.portfolioCode + "_o",
                                                                     level1Description = rdGrp.Key.portfolioName,
                                                                     level2Id = rdGrp.Key.portfolioCode + "_o" + rdGrp.Key.mainProjectCode + "x",
                                                                     level2Description = rdGrp.Key.mainProjectName,
                                                                     displayId = rdGrp.Key.mainProjectCode,
                                                                     totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                                     actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                                     originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                                     revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                                     adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                                     actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                                     restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                                     totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                                     restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                                     unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                                     approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                                     costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                                     unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                                 }).ToList() :
                                                                (from rd in rowData
                                                                 group rd by new { rd.orgId, rd.orgName, rd.mainProjectCode, rd.mainProjectName } into rdGrp
                                                                 select new AccInvestHelper
                                                                 {
                                                                     level1Id = rdGrp.Key.orgId + "_o",
                                                                     level1Description = rdGrp.Key.orgName,
                                                                     level2Id = rdGrp.Key.orgId + "_o" + rdGrp.Key.mainProjectCode + "x",
                                                                     level2Description = rdGrp.Key.mainProjectName,
                                                                     displayId = rdGrp.Key.mainProjectCode,
                                                                     totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                                     actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                                     originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                                     revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                                     adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                                     actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                                     restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                                     totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                                     restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                                     unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                                     approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                                     costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                                     unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                                 }).ToList();
            foreach (var item in hMainProRecs)
            {
                item.usageYrAccountPcCal = item.revisedBudgetYr != 0 ? item.actualAccountYr / item.revisedBudgetYr * 100 : 0;
                item.usageTotalAccountPc = item.totalCostBudget != 0 ? item.totalAccount / item.totalCostBudget * 100 : 0;
            }
            return hMainProRecs.OrderBy(x => x.displayId).ToList();
        }

        private List<AccInvestHelper> AggregateLevel3Project(bool usePortfolio, List<AccInvestHelper> rowData)
        {
            List<AccInvestHelper> returnRecods = usePortfolio ? (from rd in rowData
                                                                 group rd by new { rd.portfolioCode, rd.portfolioName, rd.mainProjectCode, rd.mainProjectName, rd.projectCode, rd.projectName } into rdGrp
                                                                 select new AccInvestHelper
                                                                 {
                                                                     level1Id = rdGrp.Key.portfolioCode + "_o" + rdGrp.Key.mainProjectCode + "x",
                                                                     level1Description = rdGrp.Key.mainProjectName,
                                                                     level2Id = rdGrp.Key.portfolioCode + "_o" + rdGrp.Key.mainProjectCode + "x" + rdGrp.Key.projectCode + "_",
                                                                     level2Description = rdGrp.Key.projectName,
                                                                     displayId = rdGrp.Key.projectCode,
                                                                     totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                                     actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                                     originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                                     revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                                     adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                                     actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                                     restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                                     totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                                     restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                                     unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                                     approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                                     costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                                     unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                                 }).ToList() :
                                                                (from rd in rowData
                                                                 group rd by new { rd.orgId, rd.orgName, rd.mainProjectCode, rd.mainProjectName, rd.projectCode, rd.projectName } into rdGrp
                                                                 select new AccInvestHelper
                                                                 {
                                                                     level1Id = rdGrp.Key.orgId + "_o" + rdGrp.Key.mainProjectCode + "x",
                                                                     level1Description = rdGrp.Key.mainProjectName,
                                                                     level2Id = rdGrp.Key.orgId + "_o" + rdGrp.Key.mainProjectCode + "x" + rdGrp.Key.projectCode + "_",
                                                                     displayId = rdGrp.Key.projectCode,
                                                                     level2Description = rdGrp.Key.projectName,
                                                                     totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                                     actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                                     originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                                     revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                                     adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                                     actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                                     restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                                     totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                                     restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                                     unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                                     approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                                     costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                                     unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                                 }).ToList();
            foreach (var item in returnRecods)
            {
                item.usageYrAccountPcCal = item.revisedBudgetYr != 0 ? item.actualAccountYr / item.revisedBudgetYr * 100 : 0;
                item.usageTotalAccountPc = item.totalCostBudget != 0 ? item.totalAccount / item.totalCostBudget * 100 : 0;
            }
            return returnRecods.OrderBy(x => x.displayId).ToList();
        }

        private List<AccInvestHelper> AggregateLevel4AccGrp(bool usePortfolio, List<AccInvestHelper> rowData)
        {
            List<AccInvestHelper> returnRecods = usePortfolio ? (from rd in rowData
                                                                 group rd by new { rd.portfolioCode, rd.portfolioName, rd.mainProjectCode, rd.mainProjectName, rd.projectCode, rd.projectName, rd.accountGroupId, rd.accountGroupDesc } into rdGrp
                                                                 select new AccInvestHelper
                                                                 {
                                                                     level1Id = rdGrp.Key.portfolioCode + "_o" + rdGrp.Key.mainProjectCode + "x" + rdGrp.Key.projectCode + "_",
                                                                     level1Description = rdGrp.Key.projectName,
                                                                     level2Id = rdGrp.Key.portfolioCode + "_o" + rdGrp.Key.mainProjectCode + "x" + rdGrp.Key.projectCode + "_" + rdGrp.Key.accountGroupId + "z",
                                                                     level2Description = rdGrp.Key.accountGroupDesc,
                                                                     displayId = rdGrp.Key.accountGroupId,
                                                                     totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                                     actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                                     originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                                     revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                                     adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                                     actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                                     restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                                     totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                                     restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                                     unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                                     approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                                     costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                                     unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                                 }).ToList() :
                                                                (from rd in rowData
                                                                 group rd by new { rd.orgId, rd.orgName, rd.mainProjectCode, rd.mainProjectName, rd.projectCode, rd.projectName, rd.accountGroupId, rd.accountGroupDesc } into rdGrp
                                                                 select new AccInvestHelper
                                                                 {
                                                                     level1Id = rdGrp.Key.orgId + "_o" + rdGrp.Key.mainProjectCode + "x" + rdGrp.Key.projectCode + "_",
                                                                     level1Description = rdGrp.Key.projectName,
                                                                     level2Id = rdGrp.Key.orgId + "_o" + rdGrp.Key.mainProjectCode + "x" + rdGrp.Key.projectCode + "_" + rdGrp.Key.accountGroupId + "z",
                                                                     level2Description = rdGrp.Key.accountGroupDesc,
                                                                     displayId = rdGrp.Key.accountGroupId,
                                                                     totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                                     actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                                     originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                                     revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                                     adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                                     actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                                     restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                                     totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                                     restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                                     unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                                     approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                                     costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                                     unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                                 }).ToList();
            foreach (var item in returnRecods)
            {
                item.usageYrAccountPcCal = item.revisedBudgetYr != 0 ? item.actualAccountYr / item.revisedBudgetYr * 100 : 0;
                item.usageTotalAccountPc = item.totalCostBudget != 0 ? item.totalAccount / item.totalCostBudget * 100 : 0;
            }
            return returnRecods.OrderBy(x => x.displayId).ToList();
        }

        private List<AccInvestHelper> AggregateAccountCode(bool usePortfolio, List<AccInvestHelper> rowData)
        {
            List<AccInvestHelper> returnRecods = usePortfolio ? (from rd in rowData
                                                                 group rd by new { rd.portfolioCode, rd.portfolioName, rd.mainProjectCode, rd.mainProjectName, rd.projectCode, rd.projectName, rd.accountGroupId, rd.accountGroupDesc, rd.accountCode, rd.accountName } into rdGrp
                                                                 select new AccInvestHelper
                                                                 {
                                                                     level1Id = rdGrp.Key.portfolioCode + "_o" + rdGrp.Key.mainProjectCode + "x" + rdGrp.Key.projectCode + "_" + rdGrp.Key.accountGroupId + "z",
                                                                     level1Description = rdGrp.Key.projectName,
                                                                     level2Id = rdGrp.Key.portfolioCode + "_o" + rdGrp.Key.mainProjectCode + "x" + rdGrp.Key.projectCode + "_" + rdGrp.Key.accountGroupId + "z" + rdGrp.Key.accountCode,
                                                                     level2Description = rdGrp.Key.accountName,
                                                                     displayId = rdGrp.Key.accountCode,
                                                                     totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                                     actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                                     originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                                     revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                                     adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                                     actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                                     restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                                     totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                                     restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                                     unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                                     approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                                     costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                                     unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                                 }).ToList() :
                                                                (from rd in rowData
                                                                 group rd by new { rd.orgId, rd.orgName, rd.mainProjectCode, rd.mainProjectName, rd.projectCode, rd.projectName, rd.accountGroupId, rd.accountGroupDesc, rd.accountCode, rd.accountName } into rdGrp
                                                                 select new AccInvestHelper
                                                                 {
                                                                     level1Id = rdGrp.Key.orgId + "_o" + rdGrp.Key.mainProjectCode + "x" + rdGrp.Key.projectCode + "_" + rdGrp.Key.accountGroupId + "z",
                                                                     level1Description = rdGrp.Key.projectName,
                                                                     level2Id = rdGrp.Key.orgId + "_o" + rdGrp.Key.mainProjectCode + "x" + rdGrp.Key.projectCode + "_" + rdGrp.Key.accountGroupId + "z" + rdGrp.Key.accountCode,
                                                                     level2Description = rdGrp.Key.accountName,
                                                                     displayId = rdGrp.Key.accountCode,
                                                                     totalCostBudget = rdGrp.Sum(x => x.totalCostBudget),
                                                                     actualAccountPrevYr = rdGrp.Sum(x => x.actualAccountPrevYr),
                                                                     originBudgetYr = rdGrp.Sum(x => x.originBudgetYr),
                                                                     revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                                     adjustmentCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.originBudgetYr),
                                                                     actualAccountYr = rdGrp.Sum(x => x.actualAccountYr),
                                                                     restBudgetYrCal = rdGrp.Sum(x => x.revisedBudgetYr) - rdGrp.Sum(x => x.actualAccountYr),
                                                                     totalAccount = rdGrp.Sum(x => x.totalAccount),
                                                                     restBudgetTotalCal = rdGrp.Sum(x => x.restBudgetTotalCal),
                                                                     unApprovedChanges = rdGrp.Sum(x => x.unApprovedChanges),
                                                                     approvedCost = rdGrp.Sum(x => x.approvedCost),
                                                                     costEstimate = rdGrp.Sum(x => x.costEstimate),
                                                                     unAprTotalYearlyBud = rdGrp.Sum(x => x.revisedBudgetYr) + rdGrp.Sum(x => x.unApprovedChanges),
                                                                 }).ToList();
            foreach (var item in returnRecods)
            {
                item.usageYrAccountPcCal = item.revisedBudgetYr != 0 ? item.actualAccountYr / item.revisedBudgetYr * 100 : 0;
                item.usageTotalAccountPc = item.totalCostBudget != 0 ? item.totalAccount / item.totalCostBudget * 100 : 0;
            }
            return returnRecods.OrderBy(x => x.displayId).ToList();
        }

        private List<AccInvestHelper> ApplyFilter(string userId, List<AccInvestHelper> fData,
                                                  Protfolio portCodes,
                                                  List<string> sOrgAreas,
                                                  List<string> sServAreas,
                                                  List<string> projCodes,
                                                  List<string> deptCodes,
                                                  List<string> funcCodes,
                                                  List<string> projMgrs,
                                                  List<string> projOwrs,
                                                  List<string> accCodes)
        {
            if (portCodes != null)
            {
                if (portCodes.ProjectLevel1Data != null && portCodes.ProjectLevel1Data.Count > 0)
                {
                    fData = fData.Where(x => portCodes.ProjectLevel1Data.Contains(x.projGrpId1)).ToList();
                }
                if (portCodes.ProjectLevel2Data != null && portCodes.ProjectLevel2Data.Count > 0)
                {
                    fData = fData.Where(x => portCodes.ProjectLevel2Data.Contains(x.projGrpId2)).ToList();
                }
                if (portCodes.ProjectLevel3Data != null && portCodes.ProjectLevel3Data.Count > 0)
                {
                    fData = fData.Where(x => portCodes.ProjectLevel3Data.Contains(x.projGrpId3)).ToList();
                }
                if (portCodes.ProjectLevel4Data != null && portCodes.ProjectLevel4Data.Count > 0)
                {
                    fData = fData.Where(x => portCodes.ProjectLevel4Data.Contains(x.projGrpId4)).ToList();
                }
                if (portCodes.ProjectLevel5Data != null && portCodes.ProjectLevel5Data.Count > 0)
                {
                    fData = fData.Where(x => portCodes.ProjectLevel5Data.Contains(x.projGrpId5)).ToList();
                }
            }

            if (sOrgAreas.Count > 0)
            {
                fData = fData.Where(x => sOrgAreas.Contains(x.orgId)).ToList();
            }

            if (sServAreas.Count > 0)
            {
                fData = fData.Where(x => sServAreas.Contains(x.serviceId)).ToList();
            }

            if (projCodes.Count > 0)
            {
                fData = fData.Where(x => projCodes.Contains(x.mainProjectCode)).ToList();
            }

            if (deptCodes.Count > 0)
            {
                fData = fData.Where(x => deptCodes.Contains(x.deptCode)).ToList();
            }

            if (funcCodes.Count > 0)
            {
                fData = fData.Where(x => funcCodes.Contains(x.funcCode)).ToList();
            }

            if (projMgrs.Count > 0)
            {
                List<InvKeyValuePair> mainProjectList = GetMainProjectOrProjList(userId, projMgrs, "MP");
                fData = fData.Where(x => mainProjectList.Select(z => z.key).Contains(x.mainProjectCode)).ToList();
            }

            if (projOwrs.Count > 0)
            {
                List<InvKeyValuePair> projectList = GetMainProjectOrProjList(userId, projOwrs, "P");
                fData = fData.Where(x => projectList.Select(z => z.key).Contains(x.projectCode.ToString())).ToList();
            }

            if (accCodes.Count > 0)
            {
                var accountList = accCodes.Select(int.Parse).ToList();
                if (!accountList.Contains(40))
                {
                    fData = fData.Where(x => accountList.Contains(x.accountTypeID)).ToList();
                }
            }

            List<AccInvestHelper> allZeroData = fData.Where(x => x.totalCostBudget == 0
                                                              && x.actualAccountCurYr == 0
                                                              && x.actualAccountYr == 0
                                                              && x.actualAccountPrevYr == 0     //actual Acc PrevYr
                                                              && x.originBudgetYr == 0
                                                              && x.adjustmentCal == 0
                                                              && x.revisedBudgetYr == 0
                                                              && x.restBudgetYrCal == 0
                                                              && x.usageYrAccountPcCal == 0
                                                              && x.totalAccount == 0            //tot Acc
                                                              && x.restBudgetTotalCal == 0      //rest Bud Tot Cal
                                                              && x.usageTotalAccountPc == 0
                                                              && x.approvedCost == 0
                                                              && x.costEstimate == 0
                                                              && x.unApprovedChanges == 0
                                                              && x.unAprTotalYearlyBud == 0).ToList();

            List<AccInvestHelper> inActiveStatusData = fData.Where(x => x.mainProjectCode == "0" && x.actualAccountYr == 0).ToList();

            foreach (var azd in allZeroData)
                fData.Remove(azd);

            foreach (var ina in inActiveStatusData)
                fData.Remove(ina);

            return fData;
        }

        public async Task<dynamic> GetUsageAccYrGraph(string userId,
                                                      int accountPeriod,
                                                      Protfolio portCodes,
                                                      List<string> sOrgAreas,
                                                      List<string> sServAreas,
                                                      List<string> projCodes,
                                                      List<string> deptCodes,
                                                      List<string> funcCodes,
                                                      List<string> projMgrs,
                                                      List<string> projOwrs,
                                                      List<string> accCodes)
        {
            decimal totRevisedBudgetYr = 0;
            decimal totActualAccountYr = 0;
            decimal totUsageYrAccPcCal = 0;
            List<AccInvestHelper> accProjInvData = await FetchAccProjectInvBaseData(userId, accountPeriod, false);
            accProjInvData = ApplyFilter(userId, accProjInvData, portCodes, sOrgAreas, sServAreas, projCodes, deptCodes, funcCodes, projMgrs, projOwrs,accCodes);
            List<AccInvestHelper> mainProUsage = AggregateMainProUsageAcPc(accProjInvData);

            foreach (var item in mainProUsage)
            {
                totRevisedBudgetYr += item.revisedBudgetYr;
                totActualAccountYr += item.actualAccountCurYr;
                item.usageYrAccountPcCal = item.revisedBudgetYr != 0 ? item.actualAccountCurYr / item.revisedBudgetYr : 0;
            }
            totUsageYrAccPcCal = totRevisedBudgetYr != 0 ? totActualAccountYr / totRevisedBudgetYr : 0;

            return FormatUsageAccYrGraph(userId, mainProUsage, totUsageYrAccPcCal);
        }

        private dynamic FormatUsageAccYrGraph(string user, List<AccInvestHelper> accInvData, decimal totalUsageYrAcPc)
        {
            UserData userDetails = _utility.GetUserDetails(user);
            Dictionary<string, clsLanguageString> langStrings = _utility.GetLanguageStrings(userDetails.language_preference, user, "AccountData");
            string precentSpecifier = "P";
            CultureInfo culture = CultureInfo.CreateSpecificCulture(userDetails.language_preference);

            dynamic budPerOrg = new JObject();
            const int maxBarsInView = 6;
            //The array of colors for label, valueAxis, valueAxis-labels, series-positive, series-negative bars
            List<string> colorExpArray = new List<string> { "#030303", "#c3c3c3", "#606060", "#367b9a", "#b56666" };

            dynamic accBudOrgGraph = new JObject();
            accBudOrgGraph.renderAs = "canvas";
            accBudOrgGraph.legend = new JObject() {
                new JProperty("visible", false)
            };

            accBudOrgGraph.seriesDefaults = new JObject() {
                new JProperty("type", "bar"),
                new JProperty("overlay", new JObject() { new JProperty("gradient", "none")}),
                new JProperty("labels", new JObject() { new JProperty("font", "13px regularFont, sans-serif"),
                                                        new JProperty("position", "insideBase"),
                                                        new JProperty("visible", true),
                                                        new JProperty("format", "{0}"),
                                                        new JProperty("color", colorExpArray.ElementAt(0)),
                                                        new JProperty("background", "transparent"),
                                                        new JProperty("margin", new JObject() { new JProperty("left", 5), new JProperty("right", 15)}),
                                                        new JProperty("template", "#= dataItem.name #: #=(kendo.toString(value,'p2'))#")
                                                       })
            };

            dynamic seriesArr = new JArray();
            JObject seriesArrItem = new JObject() { new JProperty("type", "bar"),
                                                    new JProperty("field", "data")};
            seriesArr.Add(seriesArrItem);

            accBudOrgGraph.series = seriesArr;

            accBudOrgGraph.valueAxis = new JObject() {
                new JProperty("color", colorExpArray.ElementAt(1)),
                new JProperty("line", new JObject() { new JProperty("visible", true)}),
                new JProperty("labels", new JObject() { new JProperty("padding", new JObject(){ new JProperty("top", 5)}),
                                                        new JProperty("rotation", -45),
                                                        new JProperty("color", colorExpArray.ElementAt(2)),
                                                        new JProperty("template", "#= (kendo.toString(value,'p2')) #")})
            };

            accBudOrgGraph.categoryAxis = new JObject() {
                new JProperty("majorGridLines", new JObject(){ new JProperty("visible", false)}),
                new JProperty("labels", new JObject() { new JProperty("rotation", -90)}),
                new JProperty("min", 0),
                new JProperty("max", maxBarsInView),
            };

            accBudOrgGraph.tooltip = new JObject() {
                new JProperty("visible", true),
                new JProperty("format", "{0}%"),
                new JProperty("template", "#= dataItem.name #: #= (kendo.toString(value,'p2')) #")
            };

            accBudOrgGraph.pannable = new JObject() { new JProperty("lock", "x") };
            accBudOrgGraph.zoomable = new JObject() { new JProperty("mousewheel", new JObject() { new JProperty("lock", "x") }),
                                                      new JProperty("selection", new JObject() { new JProperty("lock", "x") })};
            budPerOrg.chart = accBudOrgGraph;

            dynamic accBudPerOrgArr = new JArray();

            foreach (AccInvestHelper item in accInvData)
            {
                JObject itemObj = new JObject() {
                                                  new JProperty("name", item.level1Description),
                                                  new JProperty("id", item.level1Id),
                                                  new JProperty("data", item.usageYrAccountPcCal),
                                                  new JProperty("color", item.usageYrAccountPcCal < 0 ? colorExpArray.ElementAt(4) : colorExpArray.ElementAt(3)),
                                                  new JProperty("opacity", 0.2)};
                accBudPerOrgArr.Add(itemObj);
            }

            budPerOrg.seriesdata = accBudPerOrgArr;

            string zoomInstruction = accInvData.Count > maxBarsInView ? ((langStrings.FirstOrDefault(v => v.Key == "AccInvUsgGraph_ZoomInstruction")).Value).LangText : string.Empty;
            budPerOrg.header = new JObject() { new JProperty("title", ((langStrings.FirstOrDefault(v => v.Key == "AccInvUsgGraph_title")).Value).LangText),
                                               new JProperty("tooltipdesc", ((langStrings.FirstOrDefault(v => v.Key == "AccInvUsgGraph_tooltipdesc")).Value).LangText),
                                               new JProperty("totalUsagePcYr", ((langStrings.FirstOrDefault(v => v.Key == "AccInvUsgGraph_total")).Value).LangText + " : " + totalUsageYrAcPc.ToString(precentSpecifier, culture)),
                                               new JProperty("zoomtip", zoomInstruction)};
            return budPerOrg;
        }

        private List<AccInvestHelper> AggregateMainProUsageAcPc(List<AccInvestHelper> rowData)
        {
            List<AccInvestHelper> mProUsagRecs = (from rd in rowData
                                                  group rd by new { rd.mainProjectCode, rd.mainProjectName } into rdGrp
                                                  select new AccInvestHelper
                                                  {
                                                      level1Id = rdGrp.Key.mainProjectCode ?? string.Empty,
                                                      level1Description = rdGrp.Key.mainProjectName ?? string.Empty,
                                                      revisedBudgetYr = rdGrp.Sum(x => x.revisedBudgetYr),
                                                      actualAccountCurYr = rdGrp.Sum(x => x.actualAccountCurYr)
                                                  }).ToList();
            return mProUsagRecs;
        }

        private dynamic GetGridColumns(Dictionary<string, clsLanguageString> langStrings)
        {
            List<string> columnFields = new List<string> { "projectAcInv", "costEstimate", "approvedCost", "totCostBudget", "actualAccPrevYr", "origBudgetYr", "adjustCal", "reviBudgetYr", "actualAccYr", "restBudgetYrCal", "usageYrAcPcCal", "totAcc", "restBudTotCal", "usageTotAcPc" };
            List<string> columnTitles = new List<string>();
            foreach (var cf in columnFields)
            {
                columnTitles.Add(((langStrings.FirstOrDefault(v => v.Key == "AccProjInvGrid_" + cf)).Value).LangText);
            }

            List<int> colCounts = Enumerable.Repeat<int>(0, columnFields.Count).ToList();
            List<bool> encodes = Enumerable.Repeat<bool>(false, columnFields.Count).ToList();
            List<bool> hiddens = Enumerable.Repeat<bool>(false, columnFields.Count).ToList();
            List<bool> expands = Enumerable.Repeat<bool>(false, columnFields.Count).ToList();
            List<int> widths = new List<int> { 250, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100 };
            List<string> attrStyles = new List<string> { "text-align:left;border-left:none;", "text-align:right;border-left:none;", "text-align:right; border-left:none;", "text-align:right;border-left:none;", "text-align:right; border-left:none;", "text-align:right;border-left-color: #000;", "text-align:right;border-left:none;", "text-align:right;border-left:none;", "text-align:right;border-left:none;", "text-align:right;border-left:none;", "text-align:right;border-left:none;", "text-align:right; border-left-color: #000;", "text-align:right;border-left:none;", "text-align:right;border-left:none;", "text-align:right;border-left:none;" };
            List<string> hdrAttrStyles = new List<string> { "text-align:left;", "text-align:right;", "text-align:right;", "text-align:right;", "text-align:right;", "text-align:right;border-left-style: solid; border-left-color: #000;", "text-align:right;", "text-align:right;", "text-align:right;", "text-align:right;", "text-align:right;", "text-align:right;border-left-style: solid; border-left-color: #000;", "text-align:right;", "text-align:right;", "text-align:right;" };
            List<string> ftrAddrStyles = Enumerable.Repeat<string>(null, columnFields.Count).ToList();
            List<string> templates = new List<string> { "", "#: kendo.format('{0:n0}', costEstimate)  #",
                                                        "#: kendo.format('{0:n0}', approvedCost)  #",
                                                        "#: kendo.format('{0:n0}', totCostBudget)  #",
                                                        "#: kendo.format('{0:n0}', actualAccPrevYr)   #",
                                                        "#: kendo.format('{0:n0}', origBudgetYr)   #",
                                                        "#: kendo.format('{0:n0}', adjustCal)   #",
                                                        "#: kendo.format('{0:n0}', reviBudgetYr)  #",
                                                        "#: kendo.format('{0:n0}', actualAccYr)  #",
                                                        "#: kendo.format('{0:n0}', restBudgetYrCal)  #",
                                                        "#: kendo.format('{0:p2}', usageYrAcPcCal/100)  #",
                                                        "#: kendo.format('{0:n0}', totAcc)  #",
                                                        "#: kendo.format('{0:n0}', restBudTotCal)  #",
                                                        "#: kendo.format('{0:p2}', usageTotAcPc/100)  #"
            };
            List<string> hdrTemplates = Enumerable.Repeat<string>(null, columnFields.Count).ToList();
            List<string> ftrTemplates = Enumerable.Repeat<string>(null, columnFields.Count).ToList();
            List<bool> lockeds = Enumerable.Repeat<bool>(false, columnFields.Count).ToList();

            dynamic columnsArray = new JArray();

            for (int i = 0; i < columnFields.Count; i++)
            {
                columnsArray.Add(_utility.GenerateColumnObject(columnTitles.ElementAt(i),
                                                               columnFields.ElementAt(i),
                                                               colCounts.ElementAt(i),
                                                               encodes.ElementAt(i),
                                                               hiddens.ElementAt(i),
                                                               expands.ElementAt(i),
                                                               widths.ElementAt(i),
                                                               attrStyles.ElementAt(i),
                                                               hdrAttrStyles.ElementAt(i),
                                                               ftrAddrStyles.ElementAt(i),
                                                               templates.ElementAt(i),
                                                               hdrTemplates.ElementAt(i),
                                                               ftrTemplates.ElementAt(i),
                                                               lockeds.ElementAt(i)));
            }
            return columnsArray;
        }

        public List<InvKeyValuePair> GetMainProjectOrProjList(string userId, List<string> userIds, string type = null)
        {
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            UserData userDetails = _utility.GetUserDetails(userId);
            if (string.IsNullOrEmpty(type))
            {
                return tenantDbContext.tco_proj_responsible.Where(x => x.fk_tenant_id == userDetails.tenant_id
                      && userIds.Contains(x.fk_user_id.ToString()) && (x.level == "P" || x.level == "MP")).Select(y => new InvKeyValuePair() { key = y.level_id, value = y.level }).Distinct().ToList();
            }
            else
            {
                return tenantDbContext.tco_proj_responsible.Where(x => x.fk_tenant_id == userDetails.tenant_id
                      && userIds.Contains(x.fk_user_id.ToString()) && x.level == type).Select(y => new InvKeyValuePair() { key = y.level_id, value = y.level }).Distinct().ToList();
            }
        }

        public JArray GetPrInViewTypes(string userId, int forecastPeriod)
        {
            return GetPrInViewTypesAsync(userId, forecastPeriod).GetAwaiter().GetResult();
        }

        public async Task<JArray> GetPrInViewTypesAsync(string userId, int forecastPeriod)
        {
            JArray returnJArr = new JArray();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "AccountData");

            string orgSetup = ((langStrings.FirstOrDefault(v => v.Key == "AD_ProjInvGrid_Opt_OrgSetup")).Value).LangText;
            string prjSetup = ((langStrings.FirstOrDefault(v => v.Key == "AD_ProjInvGrid_Opt_ProjSetup")).Value).LangText;

            dynamic option;

            option = new JObject();
            option.Key = "0";
            option.Value = orgSetup;
            option.IsSelected = false;
            returnJArr.Add(option);

            option = new JObject();
            option.Key = "1";
            option.Value = prjSetup;
            option.IsSelected = true;
            returnJArr.Add(option);

            return returnJArr;
        }

        public async Task<List<AccInvProjColumn>> GetAccountInvestmentColumnSelectorAsync(string userId, int period, List<GridColumnHelper>? allColumnsDetails)
        {
            UserData user = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(user.language_preference, user.user_name, "AccountData");

            dynamic columsToDisplayList = new JObject();
            int budgetYear = (period);
            List<AccInvProjColumn> columnsToDisplayList = new List<AccInvProjColumn>();
            List<AccInvProjColumn> columnsconfig = new List<AccInvProjColumn>();
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            string flagName = "ACCOUNT_INVESTMENT_PROJECT_COLS_VIEW";

            var targetStatusColumnConfig = await dbContext.tco_application_flag.Where(x =>
                x.fk_tenant_id == user.tenant_id && x.flag_name == flagName && x.budget_year == budgetYear &&
                (x.flag_key_id == "-1" || x.flag_key_id == user.pk_id.ToString())).ToListAsync();

            var tenantColumnConfig = targetStatusColumnConfig.Any() ? targetStatusColumnConfig.FirstOrDefault(x => x.flag_key_id == "-1") : null;
            var userColumnConfig = targetStatusColumnConfig.Any() ? targetStatusColumnConfig.FirstOrDefault(x => x.flag_key_id == user.pk_id.ToString()) : null;

            List<string> defaultSelectedCol = new List<string> { "totCostBudget", "actualAccPrevYr", "origBudgetYr", "adjustCal", "reviBudgetYr", "actualAccYr", "restBudgetYrCal", "usageYrAcPcCal", "totAcc", "restBudTotCal", "usageTotAcPc" };
            List<string> defualtColumns = new List<string> { "projectAcInv" };

            if (tenantColumnConfig != null || userColumnConfig != null)
            {
                Guid? flagGuid = userColumnConfig != null ? userColumnConfig.flag_guid : tenantColumnConfig.flag_guid;

                if (flagGuid != null)
                {
                    clsTenantCloudTableEntity updateEntity = await _utility.GetCloudTableContentAsync("WADTenantData", user.tenant_id.ToString(), flagGuid.ToString());
                    if (updateEntity != null)
                    {
                        JArray columnsArray = new JArray();
                        columnsArray = JArray.FromObject(JsonConvert.DeserializeObject(updateEntity.data.ToString()));

                        columnsconfig = (from s in columnsArray
                                         select new AccInvProjColumn
                                         {
                                             key = (string)s["key"],
                                             value = (string)s["value"],
                                             isChecked = (bool)s["isChecked"],
                                         }).ToList();
                    }
                }
            }

            string title = string.Empty;
            bool isChecked = true;
            List<string?> allColumns = allColumnsDetails.Select(x => x.field).ToList();
            allColumns = allColumns.Except(defualtColumns).ToList(); // to remove the default columns
            foreach (var item in allColumns)
            {
                isChecked = false;
                title = langStrings.FirstOrDefault(v => v.Key == "AccProjInvGrid_" + item).Value.LangText;
                if (tenantColumnConfig != null || userColumnConfig != null)
                {
                    if (columnsconfig.Count > 0 && columnsconfig.Select(x => x.key).Contains(item.ToString()))
                    {
                        isChecked = columnsconfig.FirstOrDefault(x => x.key == item.ToString()).isChecked;
                    }
                }
                else
                {
                    if (defaultSelectedCol.Contains(item.ToString()))

                    {
                        isChecked = true;
                    }
                }
                AccInvProjColumn columnInfo = new ()
                {
                    key = item.ToString(),
                    value = title,
                    isChecked = isChecked
                };
                columnsToDisplayList.Add(columnInfo);
            }
            return columnsToDisplayList;
        }

        public async Task SaveAccInvProjGridColumnsAsync(string userId, AccInvProjColSelHelper columnSelectorInput)
        {
            UserData user = await _utility.GetUserDetailsAsync(userId);
            string selectedColumns = JsonConvert.SerializeObject(columnSelectorInput.selectedColumns);
            if (columnSelectorInput.isTenantSpecificColConfigSave)
            {
                await _bpTarget.DeleteUserSpecificColumnConfigAsync(userId, "ACCOUNT_INVESTMENT_PROJECT_COLS_VIEW", columnSelectorInput.budgetYear);
            }
            await _utility.SaveColumnsConfigAsync(userId, "ACCOUNT_INVESTMENT_PROJECT_COLS_VIEW", selectedColumns, columnSelectorInput.isTenantSpecificColConfigSave ? -1 : user.pk_id, columnSelectorInput.budgetYear);
        }
    }
}