#pragma warning disable CS8625
#pragma warning disable CS8629

#pragma warning disable CS8073
#pragma warning disable CS8600
#pragma warning disable CS8601
#pragma warning disable CS8602
#pragma warning disable CS8603
#pragma warning disable CS8604

using Framsikt.BL.Core.Helpers;
using Framsikt.BL.Helpers;
using Framsikt.BL.Reporting.Core.Helpers;
using Framsikt.BL.Reporting.Helpers;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Linq;
using System.Reflection;
using System.Text;
using KeyValuePair = Framsikt.BL.Helpers.KeyValuePair;

namespace Framsikt.BL.Reporting
{
    public class MonthlyReportProposedBudgetAdjustments : IMonthlyReportProposedBudgetAdjustments
    {
        private readonly IUtility _utility;
        private readonly IOrgUtility _orgUtility;
        private readonly IMonthlyReportForecastUtility _mrfUtility;
        private readonly IConsequenceAdjustedBudget _consequenceAdjustBudget;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IActionUploadFileUtility _actionUploadFileUtility;
        private readonly IFinUtility _finUtility;

        public MonthlyReportProposedBudgetAdjustments(IUtility util, IOrgUtility orgUtil, IConsequenceAdjustedBudget consequenceAdjustBudget, IMonthlyReportForecastUtility mrfUtil, IUnitOfWork unitOfWork, IActionUploadFileUtility actionUploadFileUtility, IFinUtility finUtility)
        {
            _utility = util;
            _orgUtility = orgUtil;
            _mrfUtility = mrfUtil;
            _consequenceAdjustBudget = consequenceAdjustBudget;
            _unitOfWork = unitOfWork;
            _actionUploadFileUtility = actionUploadFileUtility;
            _finUtility = finUtility;
        }

        public JArray GetDepartments(string userName, int budgetYear, int forecastPeriod, OrgInpLevels orgInput,
            bool isMROverViewReq = false)
        {
            var result = GetDepartmentsAsync(userName, budgetYear, forecastPeriod, orgInput, isMROverViewReq)
                .GetAwaiter().GetResult();
            return result;
        }

        public async Task<JArray> GetDepartmentsAsync(string userName, int budgetYear, int forecastPeriod, OrgInpLevels orgInput, bool isMROverViewReq = false, bool isChatperSetupMR = false, string attributeId = null)
        {
            dynamic departments = new JArray();
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userName, forecastPeriod);
            if (isMROverViewReq)
            {
                orgInput.level1OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault().org_id_1;
            }

            List<string> lstDepartments = await _orgUtility.GetDepartmentsForOrgIdHierLvlsAsync(orgVersionContent, userName, budgetYear, orgInput);

            List<string> relevantDepartmentsList = (from nloid in lstDepartments
                                                    select nloid).Distinct().ToList();

            List<Department> lstTenantDepartments = (await _utility.GetTenantDepartmentsAsync(userName)).ToList();

            var departmentsWithText = (from d1 in relevantDepartmentsList
                                       join d2 in lstTenantDepartments on d1 equals d2.departmentValue
                                       select new
                                       {
                                           deptValue = d1,
                                           deptText = d2.departmentText
                                       }).ToList().OrderBy(x => x.deptValue).ToList();
            if(isChatperSetupMR && !(attributeId == "-1" || string.IsNullOrEmpty(attributeId)))
            {
                List<string> deptCodesConnectedToAttribute = await _finUtility.GetAttributeConnectedDepartmentsForSelectedOrgAsync(userName, budgetYear, orgInput.level1OrgId, 1, attributeId);
                departmentsWithText = (from d1 in deptCodesConnectedToAttribute
                                       join d2 in departmentsWithText on d1 equals d2.deptValue
                                       select new
                                       {
                                           deptValue = d1,
                                           deptText = d2.deptText
                                       }).ToList().OrderBy(x => x.deptValue).ToList();
            }

            foreach (var v in departmentsWithText)
            {
                dynamic dept = new JObject();
                dept.departmentValue = v.deptValue;
                dept.departmentText = v.deptValue + "-" + v.deptText;
                departments.Add(dept);
            }

            return departments;
        }

        public JArray GetFunctions(string userName, string selectedServiceId, int forecastPeriod, OrgInpLevels orgInput,
            bool isMROverViewReq = false)
        {
            var result = GetFunctionsAsync(userName, selectedServiceId, forecastPeriod, orgInput, isMROverViewReq)
                .GetAwaiter().GetResult();
            return result;
        }

        public async Task<JArray> GetFunctionsAsync(string userName, string selectedServiceId, int forecastPeriod, OrgInpLevels orgInput, bool isMROverViewReq = false)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userName);
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userName, forecastPeriod);
            int budgetYear = forecastPeriod / 100;

            dynamic functions = new JArray();

            List<tco_functions> lstFunctions = await tenantDbContext.tco_functions
                                                                    .Where(x => x.pk_tenant_id == userDetails.tenant_id
                                                                                && budgetYear >= x.dateFrom.Year 
                                                                                && budgetYear <= x.dateTo.Year
                                                                                && x.isActive).ToListAsync();

            if (isMROverViewReq)
            {
                orgInput.level1OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault().org_id_1;
            }

            List<string> relevantFunctionsList = await _orgUtility.GetFunctionsForOrgIdHierLvlsAsync(orgVersionContent, userName, orgInput, selectedServiceId == "-1" ? null : selectedServiceId);

            var functionsWithText = (from t in lstFunctions
                                     where t.pk_id == -1
                                     select new
                                     {
                                         functionValue = string.Empty,
                                         functionText = string.Empty
                                     }).ToList();

            if (relevantFunctionsList.Count() == 0 || (relevantFunctionsList.Count() == 1 && relevantFunctionsList.Contains("")))
            {
                functionsWithText = (from f1 in lstFunctions
                                     select new
                                     {
                                         functionValue = f1.pk_Function_code,
                                         functionText = f1.display_name
                                     }).ToList().OrderBy(x => x.functionValue).ToList();
            }
            else
            {
                functionsWithText = (from f1 in relevantFunctionsList
                                     join f2 in lstFunctions on f1 equals f2.pk_Function_code
                                     select new
                                     {
                                         functionValue = f1,
                                         functionText = f2.display_name
                                     }).ToList().OrderBy(x => x.functionValue).ToList();
            }

            foreach (var v in functionsWithText)
            {
                dynamic func = new JObject();
                func.functionValue = v.functionValue;
                func.functionText = v.functionValue + "-" + v.functionText;
                functions.Add(func);
            }

            return functions;
        }

        public ActionLogGridAllDataHelper GetProposedBudgetAdjustmentsLog(string userId, int actionId, bool isAngular)
        {
            var result = GetProposedBudgetAdjustmentsLogAsync(userId, actionId, isAngular).GetAwaiter().GetResult();
            return result;
        }

        public async Task<ActionLogGridAllDataHelper> GetProposedBudgetAdjustmentsLogAsync(string userId, int actionId, bool isAngular)
        {
            ActionLogGridAllDataHelper result = new ActionLogGridAllDataHelper();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringValuesBudgetProposal =
               await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetProposal");
            TenantDBContext tendbContext = await _utility.GetTenantDBContextAsync();
            List<ActionLogGridDataHelper> data = await (from a in tendbContext.tfp_temp_header
                                                        join b in tendbContext.tfp_action_change_log on new { b = a.log_id, c = a.fk_tenant_id } equals new { b = b.fk_log_id, c = b.fk_tenant_id } into g1
                                                        from b in g1.DefaultIfEmpty()
                                                        join d in tendbContext.tco_users on new { a = b.updated_by } equals new { a = d.pk_id } into g3
                                                        from d in g3.DefaultIfEmpty()
                                                        where a.fk_tenant_id == userDetails.tenant_id && a.pk_temp_id == actionId
                                                        orderby b.updated, b.action_status
                                                        select new ActionLogGridDataHelper
                                                        {
                                                            date = (b.updated != null) ? b.updated : (DateTime?)null,
                                                            actionStatus = (b.type != null) ? b.type : string.Empty,
                                                            statusComment = (b.comment != null) ? b.comment : string.Empty,
                                                            updatedBy = (d.first_name != null && d.last_name != null) ? (d.first_name + " " + d.last_name) : string.Empty,
                                                            popupTitle = a.description
                                                        }).ToListAsync();
            foreach (var row in data.Where(x => !string.IsNullOrEmpty(x.actionStatus)))
            {
                if (row.actionStatus == ((char)(clsConstants.LogActionType.Blist)).ToString())
                {
                    row.actionStatus = langStringValuesBudgetProposal.FirstOrDefault(v => v.Key == "BP_GridColumn_type2").Value.LangText;
                }
                else
                {
                    row.actionStatus = langStringValuesBudgetProposal.FirstOrDefault(v => v.Key == "BP_actionlog_parked").Value.LangText;
                }
            }
            result.data = data;
            if (!isAngular)
            {
                List<GridColumnHelper> column = GetProposedBudgetAdjustmentsActionLogGridColumns(langStringValuesBudgetProposal);
                result.columns = column;
            }
            return result;
        }

        public string SavePropBudSamAction(string userId, List<MrBudAdjSam> pbaList)
        {
            var result = SavePropBudSamActionAsync(userId, pbaList).GetAwaiter().GetResult();
            return result;
        }

        public async Task<string> SavePropBudSamActionAsync(string userId, List<MrBudAdjSam> pbaList)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
                List<int> pbActionIds = pbaList.Select(x => x.id).ToList();

                List<tfp_temp_header> actionList = await tenantDbContext.tfp_temp_header.Where(x => x.fk_tenant_id == userDetails.tenant_id && pbActionIds.Contains(x.pk_temp_id)).ToListAsync();
                foreach (var tth in actionList)
                    tth.is_sam_action = pbaList.FirstOrDefault(x => x.id == tth.pk_temp_id).isSamAction;

                await tenantDbContext.SaveChangesAsync();

                return "success";
            }
            catch
            {
                return "fail";
            }
        }

        public JObject GetProposedBudgetAdjustments(string userName,
            int budgetYear,
            string budMonthLong,
            int budPeriod,
            string selectedOrgName,
            int selectedOrgLevel,
            string selectedOrgId,
            string selectedServiceId,
            OrgInpLevels orgInput,
            int forecastPeriod,
            bool isBudgetChangesRequest,
            bool isTypeInsert,
            bool isDoc,
            bool isMROverViewReq,
            bool isMonthlyReport,
            budgetAdjustmentFilterInput filterInput,
            string sortInput,
            string sortOrder = null,
            string gridSort = null,
            string userAdjustmentCode = null,
            string nodeType=null)
        {
            var result = GetProposedBudgetAdjustmentsAsync(userName, budgetYear, budMonthLong, budPeriod,
                selectedOrgName, selectedOrgLevel, selectedOrgId, selectedServiceId, orgInput, forecastPeriod,
                isBudgetChangesRequest, isTypeInsert, isDoc, isMROverViewReq, isMonthlyReport, filterInput, sortInput,
                sortOrder, gridSort, userAdjustmentCode, nodeType:nodeType).GetAwaiter().GetResult();
            return result;
        }

        public async Task<JObject> GetProposedBudgetAdjustmentsAsync(string userName,
                                                    int budgetYear,
                                                    string budMonthLong,
                                                    int budPeriod,
                                                    string selectedOrgName,
                                                    int selectedOrgLevel,
                                                    string selectedOrgId,
                                                    string selectedServiceId,
                                                    OrgInpLevels orgInput,
                                                    int forecastPeriod,
                                                    bool isBudgetChangesRequest,
                                                    bool isTypeInsert,
                                                    bool isDoc,
                                                    bool isMROverViewReq,
                                                    bool isMonthlyReport,
                                                    budgetAdjustmentFilterInput filterInput,
                                                    string sortInput,
                                                    string sortOrder = null,
                                                    string gridSort = null,
                                                    string userAdjustmentCode = null,
                                                    bool isChapterSetupMR = false,
                                                    string attributeId = null,
                                                    string nodeType=null)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userName);
                TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
                var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userName, budPeriod);
                bool isEditable = true;
                //string tempNaParam = string.Empty;
                var (reportSubmitStatus, tempNaParam) = await _mrfUtility.GetMonthlyReportStatusAsync(userName, selectedOrgId, selectedServiceId, budPeriod.ToString(), selectedOrgLevel, string.Empty, true);
                bool bAjTabSubmitStatus = false;

                // if report is set then only role_id 2 and 12 can edit.
                if (reportSubmitStatus || bAjTabSubmitStatus)
                {
                    isEditable = await _mrfUtility.GetMonthlyReportUserRoleEditAccessAsync(userDetails.pk_id, userDetails.tenant_id);
                }
                else
                {
                    List<tco_user_orgrole> userAccessibleOrgHeirList = await _utility.GetUserAccessibleOrgHierarchyListAsync(orgVersionContent, userName);

                    List<string> uaCurrentOrgLevelIdList = (from uaohl in userAccessibleOrgHeirList
                                                            where uaohl.hierarchy_level == selectedOrgLevel
                                                            select uaohl.fk_org_id).Distinct().ToList();

                    isEditable = !reportSubmitStatus && uaCurrentOrgLevelIdList.Contains(selectedOrgId) && !bAjTabSubmitStatus;
                }

                var datasetBeforeFilter = await (from a in tenantDbContext.tfp_temp_header
                                                 join b in tenantDbContext.tfp_temp_detail on new { a = a.fk_tenant_id, b = a.pk_temp_id } equals new { a = b.fk_tenant_id, b = b.fk_temp_id }
                                                 join c in tenantDbContext.tco_fp_alter_codes on new { a = a.fk_tenant_id, b = b.fk_alter_code } equals new { a = c.fk_tenant_id, b = c.pk_alter_code } into c1
                                                 from c2 in c1.DefaultIfEmpty()
                                                 where a.fk_tenant_id == userDetails.tenant_id
                                                    && b.budget_year == budgetYear
                                                    && a.action_type == 60
                                                 select new clsProposedBudgetAdjustmentsHelper()
                                                 {
                                                     actionId = a.pk_temp_id,
                                                     actionDesc = a.description,
                                                     deptCode = b.department_code,
                                                     funcCode = b.function_code,
                                                     accCode = b.fk_account_code,
                                                     tags = a.tags,
                                                     alterCode = (string.IsNullOrEmpty(c2.limit_description) ? string.Empty : c2.limit_description),
                                                     priority = a.priority,
                                                     year1Amount = b.year_1_amount,
                                                     year2Amount = b.year_2_amount,
                                                     year3Amount = b.year_3_amount,
                                                     year4Amount = b.year_4_amount,
                                                     year5Amount = b.year_5_amount == null ? 0 : b.year_5_amount.Value,
                                                     description = a.long_description,
                                                     internldesc = a.financial_plan_description,
                                                     forecastPeriod = b.forecast_period,
                                                     isImported = a.is_imported,
                                                     is_MROverview_flag = a.is_MROverview_flag,// added for story 40448
                                                     lineGrp = "",
                                                     orgId = a.org_id,
                                                     orgLevel = a.org_level ?? 0,
                                                     isParkedAction = a.is_parked_action,
                                                     isSamAction = a.is_sam_action,
                                                     projectCode = b.project_code,
                                                     freeDim1 = b.free_dim_1,
                                                     freeDim2 = b.free_dim_2,
                                                     freeDim3 = b.free_dim_3,
                                                     freeDim4 = b.free_dim_4,
                                                     tempAdjFinplan = false,
                                                     tempAdjSam = false
                                                 }).ToListAsync();
                if (isMROverViewReq)
                {
                    datasetBeforeFilter = datasetBeforeFilter.Where(x => x.isParkedAction == false).ToList();
                }

                if (isBudgetChangesRequest && forecastPeriod != -1)
                {
                    int forecastPeriodLong = _utility.GetForecastPeriod(budgetYear, forecastPeriod);
                    datasetBeforeFilter = datasetBeforeFilter.Where(x => x.forecastPeriod == forecastPeriodLong).ToList();
                }

                if (isDoc && isMROverViewReq)// 34797 get the line group for mroverviewdata
                {
                    datasetBeforeFilter = await GetLineGrpForDataAsync(datasetBeforeFilter, userDetails, budPeriod,nodeType);
                }
                if (isBudgetChangesRequest)
                {
                    datasetBeforeFilter = datasetBeforeFilter.Where(x => x.isImported != true).ToList();
                }
                else if (isMROverViewReq) // added for story 40448
                {
                    datasetBeforeFilter = datasetBeforeFilter.Where(x => x.forecastPeriod == budPeriod).ToList();
                }
                else
                {
                    datasetBeforeFilter = await GetActionDataAsync(datasetBeforeFilter, budPeriod, budgetYear, userName);
                    // datasetBeforeFilter = datasetBeforeFilter.Where(x => x.forecastPeriod == budPeriod && x.is_MROverview_flag == false).ToList();
                }

                if (!isBudgetChangesRequest || isMROverViewReq)
                {
                    List<string> lstDepartments = new List<string>();
                    if (isChapterSetupMR && !(attributeId == "-1" || string.IsNullOrEmpty(attributeId)))
                    {
                        lstDepartments = await _finUtility.GetAttributeConnectedDepartmentsForSelectedOrgAsync(userName, budgetYear, selectedOrgId, selectedOrgLevel, attributeId);
                    }
                    else
                    {
                        lstDepartments = await _orgUtility.GetDepartmentsForOrgIdHierLvlsAsync(orgVersionContent, userName, budgetYear, orgInput);
                    }

                    List<string> lstFunctions = await _orgUtility.GetFunctionsForOrgIdHierLvlsAsync(orgVersionContent, userName, orgInput, selectedServiceId == "-1" ? null : selectedServiceId);
                    datasetBeforeFilter = datasetBeforeFilter.Where(x => x.forecastPeriod == budPeriod).ToList();

                    if (lstDepartments != null && lstDepartments.Count() > 0)
                    {
                        datasetBeforeFilter = datasetBeforeFilter.Where(x => lstDepartments.Contains(x.deptCode)).ToList();
                    }
                    if (lstFunctions != null && lstFunctions.Count() > 0)
                    {
                        datasetBeforeFilter = datasetBeforeFilter.Where(x => lstFunctions.Contains(x.funcCode)).ToList();
                    }
                }

                var groupedDatasetAfterFilter = (from d in datasetBeforeFilter
                                                 group d by new { d.actionId, d.actionDesc, d.tags, d.alterCode, d.priority, d.description, d.internldesc, d.is_MROverview_flag, d.lineGrp, d.orgId, d.orgLevel, d.isParkedAction, d.isSamAction } into g
                                                 select new clsProposedBudgetAdjustmentsHelper
                                                 {
                                                     actionId = g.Key.actionId,
                                                     actionDesc = g.Key.actionDesc,
                                                     tags = g.Key.tags,
                                                     alterCode = g.Key.alterCode,
                                                     priority = g.Key.priority,
                                                     year1Amount = g.Sum(x => x.year1Amount),
                                                     year2Amount = g.Sum(x => x.year2Amount),
                                                     year3Amount = g.Sum(x => x.year3Amount),
                                                     year4Amount = g.Sum(x => x.year4Amount),
                                                     year5Amount = g.Sum(x => x.year5Amount),
                                                     description = g.Key.description,
                                                     internldesc = g.Key.internldesc,
                                                     isMROverViewReq = g.Key.is_MROverview_flag,
                                                     lineGrp = g.Key.lineGrp,
                                                     orgId = g.Key.orgId,
                                                     orgLevel = g.Key.orgLevel,
                                                     isParkedAction = g.Key.isParkedAction,
                                                     isSamAction = g.Key.isSamAction
                                                 }).OrderBy(x => x.alterCode).ThenBy(y => y.actionDesc).ToList();

                if (!isMonthlyReport && !isMROverViewReq)
                {
                    groupedDatasetAfterFilter = groupedDatasetAfterFilter.Where(x => !x.isParkedAction).ToList();
                }

                var tempIdsForSelectedAdjCode = await tenantDbContext.tco_finplan_adj_codes_actions_temp.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                                           && x.budget_year == budgetYear).ToListAsync();

                if (!string.IsNullOrEmpty(userAdjustmentCode))
                {
                    /*find all the temp actions which are not part of selected user adj code and remove them from the datasetBeforeFilter*/
                    var ids = tempIdsForSelectedAdjCode.Where(x => x.finplan_user_adj_code != userAdjustmentCode).Select(y => y.fk_temp_id).Distinct().ToList();
                    groupedDatasetAfterFilter = groupedDatasetAfterFilter.Where(x => !ids.Contains(x.actionId)).ToList();

                    /*find all the temp actions which are part of selected user adj code and set finplan and sam in the datasetBeforeFilter*/
                    ids = tempIdsForSelectedAdjCode.Where(x => x.finplan_user_adj_code == userAdjustmentCode).Select(y => y.fk_temp_id).Distinct().ToList();

                    groupedDatasetAfterFilter.Where(x => ids.Contains(x.actionId)).ToList().ForEach(y =>
                    {
                        y.tempAdjFinplan = tempIdsForSelectedAdjCode.FirstOrDefault(a => a.fk_temp_id == y.actionId) == null ? false : tempIdsForSelectedAdjCode.FirstOrDefault(a => a.fk_temp_id == y.actionId).is_finplan;
                        y.tempAdjSam = tempIdsForSelectedAdjCode.FirstOrDefault(a => a.fk_temp_id == y.actionId) == null ? false : tempIdsForSelectedAdjCode.FirstOrDefault(a => a.fk_temp_id == y.actionId).is_sam;
                    });
                }

                List<KeyValueData> lstActionTags = await (from atg in tenantDbContext.tcoActionTags
                                                          where atg.FkTenantId == userDetails.tenant_id
                                                          select new KeyValueData
                                                          {
                                                              KeyId = atg.PkId,
                                                              ValueString = atg.TagDescription
                                                          }).ToListAsync();

                string isSamEnabled = await _utility.GetParameterValueAsync(userName, "MR_SAM_ACCESS");
                dynamic result = new JObject();

                dynamic header = new JArray();
                dynamic header1 = new JObject();

                Dictionary<string, clsLanguageString> langStrs = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "MonthlyReport");
                Dictionary<string, clsLanguageString> langStringValuesLog = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetProposal");

                header1.title = ((langStrs.FirstOrDefault(v => v.Key == "proposed_bud_adj_title")).Value).LangText;
                header1.titleDescription = ((langStrs.FirstOrDefault(v => v.Key == "proposed_bud_adj_desc")).Value).LangText;
                header.Add(header1);

                result.header = header;
                result.isEditable = isEditable;
                result.saveEnable = isSamEnabled.ToLower() == "true";

                dynamic columnsArray = new JArray();

                // add check box column when the request is from budget changes
                if (isBudgetChangesRequest)
                {
                    columnsArray.Add(GenerateColumnObject(" ", "IsChecked", 1, false, false, null, " ", " "));
                }

                columnsArray.Add(GenerateColumnObject(((langStrs.FirstOrDefault(v => v.Key == "proposed_bud_adj_description")).Value).LangText, "description", 1, false, false, null, "text-align:left;width:25%", "text-align:left;width:25%"));
                if (isMonthlyReport)
                {
                    columnsArray.Add(GenerateColumnObject(((langStringValuesLog.FirstOrDefault(v => v.Key == "BP_Action_Log_icon")).Value).LangText, "description", 1, false, false, null, "text-align:left;width:5%", "text-align:left;width:5%"));
                }
                if (isTypeInsert || isDoc)
                {
                    columnsArray.Add(GenerateColumnObject(((langStrs.FirstOrDefault(v => v.Key == "proposed_bud_adj_type")).Value).LangText, "type", 3, false, false, null, "text-align:left;width:25%", "text-align:left;width:25%"));
                }

                if (isSamEnabled.ToLower() == "true" && isBudgetChangesRequest)
                {
                    columnsArray.Add(GenerateColumnObject(((langStrs.FirstOrDefault(v => v.Key == "proposed_bud_adj_sam_col")).Value).LangText,
                     "isSamAction", false, false, "text-align:center;width:10%", "text-align:center;width:10%", null, "# if(id != ''){# <input type='checkbox' #=isSamAction? checked='checked' : ''  # class='chkbx'> # } #", null, false));
                }

                columnsArray.Add(GenerateColumnObject((budgetYear).ToString(), "year1", 4, false, false, "{0:n0}", "text-align:right", "text-align:right"));
                columnsArray.Add(GenerateColumnObject((budgetYear + 1).ToString(), "year2", 5, false, false, "{0:n0}", "text-align:right", "text-align:right"));
                columnsArray.Add(GenerateColumnObject((budgetYear + 2).ToString(), "year3", 6, false, false, "{0:n0}", "text-align:right", "text-align:right"));
                columnsArray.Add(GenerateColumnObject((budgetYear + 3).ToString(), "year4", 7, false, false, "{0:n0}", "text-align:right", "text-align:right"));
                columnsArray.Add(GenerateColumnObject((budgetYear + 4).ToString(), "year5", 8, false, false, "{0:n0}", "text-align:right", "text-align:right"));

                if (isSamEnabled.ToLower() == "true" && !isBudgetChangesRequest)
                {
                    columnsArray.Add(GenerateColumnObject(((langStrs.FirstOrDefault(v => v.Key == "proposed_bud_adj_sam_col")).Value).LangText,
                     "isSamAction", false, false, "text-align:center;width:10%", "text-align:center;width:10%", null, "# if(id != ''){# <input type='checkbox' #=isSamAction? checked='checked' : ''  # class='chkbx'> # } #", null, false));
                }
                result.Add("columns", columnsArray);

                List<tco_org_hierarchy> lstOrgHierarchy = orgVersionContent.lstOrgHierarchy;
                var completehierarchyList = (from p in lstOrgHierarchy
                                             select new
                                             {
                                                 tenantID = p.fk_tenant_id,
                                                 orgID_1 = p.org_id_1,
                                                 orgName_1 = p.org_name_1,
                                                 orgLevel_1 = 1,
                                                 orgID_2 = p.org_id_2,
                                                 orgName_2 = p.org_name_2,
                                                 orgLevel_2 = 2,
                                                 orgID_3 = p.org_id_3,
                                                 orgName_3 = p.org_name_3,
                                                 orgLevel_3 = 3,
                                                 orgID_4 = p.org_id_4,
                                                 orgName_4 = p.org_name_4,
                                                 orgLevel_4 = 4,
                                                 orgID_5 = p.org_id_5,
                                                 orgName_5 = p.org_name_5,
                                                 orgLevel_5 = 5,
                                                 orgID_6 = p.org_id_6,
                                                 orgName_6 = p.org_name_6,
                                                 orgLevel_6 = 6,
                                                 orgID_7 = p.org_id_7,
                                                 orgName_7 = p.org_name_7,
                                                 orgLevel_7 = 7,
                                                 orgID_8 = p.org_id_8,
                                                 orgName_8 = p.org_name_8,
                                                 orgLevel_8 = 8
                                             }).Distinct().ToList();

                var data = new JArray();
                List<decimal> lstTotals = new List<decimal>() { 0, 0, 0, 0, 0 };
                var AttachmentData = (await _unitOfWork.InvestmentProjectRepository.GetAllAttachmentDataAsync(userDetails.tenant_id, budgetYear, Modules.MONTHLYREPORT.ToString(), isMROverViewReq ? PageId.MonthlyReportOperations.ToString() : PageId.MonthlyReportOverview.ToString())).ToList();
                foreach (var d in groupedDatasetAfterFilter)
                {
                    string orgName = string.Empty;

                    if (d.orgLevel == 1)
                    {
                        d.orgName = completehierarchyList.FirstOrDefault(x => x.orgID_1 == d.orgId).orgName_1;
                    }
                    else if (d.orgLevel == 2)
                    {
                        d.orgName = completehierarchyList.FirstOrDefault(x => x.orgID_2 == d.orgId).orgName_2;
                    }
                    else if (d.orgLevel == 3)
                    {
                        d.orgName = completehierarchyList.FirstOrDefault(x => x.orgID_3 == d.orgId).orgName_3;
                    }
                    else if (d.orgLevel == 4)
                    {
                        d.orgName = completehierarchyList.FirstOrDefault(x => x.orgID_4 == d.orgId).orgName_4;
                    }
                    else if (d.orgLevel == 5)
                    {
                        d.orgName = completehierarchyList.FirstOrDefault(x => x.orgID_5 == d.orgId).orgName_5;
                    }
                    else if (d.orgLevel == 6)
                    {
                        d.orgName = completehierarchyList.FirstOrDefault(x => x.orgID_6 == d.orgId).orgName_6;
                    }
                    else if (d.orgLevel == 7)
                    {
                        d.orgName = completehierarchyList.FirstOrDefault(x => x.orgID_7 == d.orgId).orgName_7;
                    }
                    else if (d.orgLevel == 8)
                    {
                        d.orgName = completehierarchyList.FirstOrDefault(x => x.orgID_8 == d.orgId).orgName_8;
                    }
                    else
                    {
                        d.orgName = orgName;
                    }
                }
                if (!string.IsNullOrEmpty(gridSort))
                {
                    switch (gridSort)
                    {
                        case "actionName":
                            groupedDatasetAfterFilter = groupedDatasetAfterFilter.OrderBy(x => x.actionDesc).ToList();
                            break;

                        case "createdAt":
                            groupedDatasetAfterFilter = groupedDatasetAfterFilter.OrderBy(x => x.orgName).ToList();
                            break;

                        case "actionStatus":
                            var bList = groupedDatasetAfterFilter.Where(x => !x.isParkedAction).ToList();
                            groupedDatasetAfterFilter = groupedDatasetAfterFilter.Where(x => x.isParkedAction).ToList();
                            groupedDatasetAfterFilter.AddRange(bList);
                            break;
                    }
                }
                if (!string.IsNullOrEmpty(sortInput))
                {
                    switch (sortInput)
                    {
                        case "actionName":
                            groupedDatasetAfterFilter = groupedDatasetAfterFilter.OrderBy(x => x.actionDesc).ToList();
                            break;

                        case "createdAt":
                            groupedDatasetAfterFilter = groupedDatasetAfterFilter.OrderBy(x => x.orgName).ToList();
                            break;

                        case "actionStatus":
                            var bList = groupedDatasetAfterFilter.Where(x => !x.isParkedAction).ToList();
                            groupedDatasetAfterFilter = groupedDatasetAfterFilter.Where(x => !x.isParkedAction).ToList();
                            groupedDatasetAfterFilter.AddRange(bList);
                            break;

                        case "type":
                            if (sortOrder == "asc")
                            {
                                groupedDatasetAfterFilter = groupedDatasetAfterFilter.OrderBy(x => x.alterCode).ToList();
                            }
                            else
                            {
                                groupedDatasetAfterFilter = groupedDatasetAfterFilter.OrderByDescending(x => x.alterCode).ToList();
                            }
                            break;

                        case "year1":
                            if (sortOrder == "asc")
                            {
                                groupedDatasetAfterFilter = groupedDatasetAfterFilter.OrderBy(x => x.year1Amount).ToList();
                            }
                            else
                            {
                                groupedDatasetAfterFilter = groupedDatasetAfterFilter.OrderByDescending(x => x.year1Amount).ToList();
                            }
                            break;

                        case "year2":
                            if (sortOrder == "asc")
                            {
                                groupedDatasetAfterFilter = groupedDatasetAfterFilter.OrderBy(x => x.year2Amount).ToList();
                            }
                            else
                            {
                                groupedDatasetAfterFilter = groupedDatasetAfterFilter.OrderByDescending(x => x.year2Amount).ToList();
                            }
                            break;

                        case "year3":
                            if (sortOrder == "asc")
                            {
                                groupedDatasetAfterFilter = groupedDatasetAfterFilter.OrderBy(x => x.year3Amount).ToList();
                            }
                            else
                            {
                                groupedDatasetAfterFilter = groupedDatasetAfterFilter.OrderByDescending(x => x.year3Amount).ToList();
                            }
                            break;

                        case "year4":
                            if (sortOrder == "asc")
                            {
                                groupedDatasetAfterFilter = groupedDatasetAfterFilter.OrderBy(x => x.year4Amount).ToList();
                            }
                            else
                            {
                                groupedDatasetAfterFilter = groupedDatasetAfterFilter.OrderByDescending(x => x.year4Amount).ToList();
                            }
                            break;

                        case "year5":
                            if (sortOrder == "asc")
                            {
                                groupedDatasetAfterFilter = groupedDatasetAfterFilter.OrderBy(x => x.year5).ToList();
                            }
                            else
                            {
                                groupedDatasetAfterFilter = groupedDatasetAfterFilter.OrderByDescending(x => x.year5).ToList();
                            }
                            break;
                    }
                }
                foreach (var d in groupedDatasetAfterFilter)
                {
                    dynamic obj = new JObject();
                    obj.id = d.actionId;
                    obj.description = d.actionDesc;
                    string orgName = string.Empty;
                    if (isMonthlyReport || isMROverViewReq)
                    {
                        obj.description = AttachmentData.Any(y => y.parent_id == d.actionId.ToString()) ? $"{d.actionDesc} <img src='../images/attachment.svg'/>" : d.actionDesc;
                    }

                    StringBuilder sb = new StringBuilder();
                    if (!d.isMROverViewReq)
                    {
                        if (!string.IsNullOrEmpty(d.orgName))
                            sb.Append("<span class='su-bp-tag su-bp-tag-medium' style='width:auto;'>" + d.orgName + "</span>&nbsp;");
                    }
                    if (isMonthlyReport)
                    {
                        if (!d.isParkedAction)
                        {
                            sb.Append("<span class='bp-tag-fin'>Ønskede tiltak</span>&nbsp;");
                        }
                        else
                            sb.Append("<span class='inv-running'>Parkerte tiltak</span>&nbsp;");
                    }
                    if (!string.IsNullOrEmpty(d.tags))
                    {
                        List<int> mappedTagIds = d.tags.Split(',').Select(int.Parse).ToList();
                        sb.Append("<br>");
                        foreach (var tags in mappedTagIds)
                        {
                            var firstOrDefault = lstActionTags.FirstOrDefault(x => x.KeyId == tags);
                            if (firstOrDefault != null)
                            {
                                sb.Append("<span class='bp-tag-selected'>" + firstOrDefault.ValueString + "</span>&nbsp;");
                            }
                        }
                    }

                    obj.tags = sb.ToString();
                    obj.type = d.alterCode;
                    obj.year1 = d.year1Amount / 1000;
                    lstTotals[0] = lstTotals[0] + (d.year1Amount / 1000);
                    obj.year2 = d.year2Amount / 1000;
                    lstTotals[1] = lstTotals[1] + (d.year2Amount / 1000);
                    obj.year3 = d.year3Amount / 1000;
                    lstTotals[2] = lstTotals[2] + (d.year3Amount / 1000);
                    obj.year4 = d.year4Amount / 1000;
                    lstTotals[3] = lstTotals[3] + (d.year4Amount / 1000);
                    obj.year5 = d.year5Amount / 1000;
                    lstTotals[4] = lstTotals[4] + (d.year5Amount / 1000);
                    obj.tooltipdesc = d.description;
                    obj.internldesc = d.internldesc;
                    obj.isMROverViewReq = d.isMROverViewReq;
                    obj.lineGrp = d.lineGrp;
                    obj.isSamAction = d.isSamAction;
                    obj.tempAdjSam = d.tempAdjSam;
                    obj.tempAdjFinplan = d.tempAdjFinplan;
                    data.Add(obj);
                }

                var action = data.ToObject<List<clsProposedBudgetAdjustmentsHelper>>();
                if (filterInput != null && !string.IsNullOrEmpty(filterInput.description))
                {
                    //var tagId = lstActionTags.Where(x => x.ValueString.ToLower().Contains(filterInput.description.ToLower())).Select(x => x.KeyId).ToList();

                    action = action.Where(x => x.description.ToLower().Contains(filterInput.description.ToLower()) || x.tags.ToLower().Contains(filterInput.description.ToLower())).ToList();
                }
                if (filterInput != null && filterInput.sam != "0")
                {
                    if (filterInput.sam == "1")
                    {
                        action = action.Where(x => x.isSamAction == true).ToList();
                    }
                    else if (filterInput.sam == "2")
                    {
                        action = action.Where(x => x.isSamAction == false).ToList();
                    }
                }
                if (filterInput != null && !string.IsNullOrEmpty(filterInput.type))
                {
                    action = action.Where(x => x.type.Contains(filterInput.type)).ToList();
                }
                if (!isBudgetChangesRequest)
                {
                    clsProposedBudgetAdjustmentsHelper objTotals = new clsProposedBudgetAdjustmentsHelper();
                    objTotals.id = 0;
                    objTotals.description = ((langStrs.FirstOrDefault(v => v.Key == "proposed_bud_adj_total")).Value).LangText;
                    objTotals.tags = string.Empty;
                    objTotals.type = string.Empty;
                    objTotals.year1 = lstTotals[0];
                    objTotals.year2 = lstTotals[1];
                    objTotals.year3 = lstTotals[2];
                    objTotals.year4 = lstTotals[3];
                    objTotals.year5 = lstTotals[4];
                    objTotals.tooltipdesc = string.Empty;
                    objTotals.internldesc = string.Empty;
                    objTotals.isSamAction = false;

                    action.Add(objTotals);
                }

                result.Add("data", JToken.FromObject(action));

                var paramValue = await _utility.GetParameterValueAsync(userName, "YB_UPDATE_MANDATORY");
                bool activateAndDisableBudgetCheckbox = false;
                if (!string.IsNullOrEmpty(paramValue) && paramValue.ToLower() == "true".ToLower())
                    activateAndDisableBudgetCheckbox = true;

                result.Add("activateAndDisableBudgetCheckbox", activateAndDisableBudgetCheckbox);
                return result;
            }
            catch (Exception e)
            {
                throw new Exception(e.Message);
            }
        }

        private List<clsProposedBudgetAdjustmentsHelper> GetActionData(
            List<clsProposedBudgetAdjustmentsHelper> datasetBeforeFilter, int budPeriod, int budgetYear,
            string userName)
        {
            var result = GetActionDataAsync(datasetBeforeFilter, budPeriod, budgetYear, userName).GetAwaiter()
                .GetResult();
            return result;
        }

        private async Task<List<clsProposedBudgetAdjustmentsHelper>> GetActionDataAsync(List<clsProposedBudgetAdjustmentsHelper> datasetBeforeFilter, int budPeriod, int budgetYear, string userName)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userName);
            var mrOverviewAction = datasetBeforeFilter.Where(x => x.is_MROverview_flag).ToList();
            var mrAction = datasetBeforeFilter.Where(x => x.is_MROverview_flag == false).ToList();
            List<clsProposedBudgetAdjustmentsHelper> dataSet = new List<clsProposedBudgetAdjustmentsHelper>();
            string isParameterEnabled = await _utility.GetParameterValueAsync(userName, "MR_SCREEN_USE_1A");
            if (string.IsNullOrEmpty(isParameterEnabled) || isParameterEnabled.ToLower() != "true")
            { 
                var tmdFinplanlineSetup = await (from s in tenantDbContext.tmd_finplan_line_setup
                                                 where s.fk_tenant_id == userDetails.tenant_id && s.budget_year == budgetYear
                                                 orderby s.priority ascending
                                                 select new clsLine_OrderValidation
                                                 {
                                                     action_type = s.action_type,
                                                     line_order = s.line_order,
                                                     fk_account_code = s.fk_account_code,
                                                     fk_department_code_from = string.IsNullOrEmpty(s.fk_department_code_from) ? "" : s.fk_department_code_from,
                                                     fk_department_code_to = string.IsNullOrEmpty(s.fk_department_code_to) ? "" : s.fk_department_code_to,
                                                     fk_function_code_from = string.IsNullOrEmpty(s.fk_function_code_from) ? "" : s.fk_function_code_from,
                                                     fk_function_code_to = string.IsNullOrEmpty(s.fk_function_code_to) ? "" : s.fk_function_code_to,
                                                     fk_project_code_from = string.IsNullOrEmpty(s.fk_project_code_from) ? "" : s.fk_project_code_from,
                                                     fk_project_code_to = string.IsNullOrEmpty(s.fk_project_code_to) ? "" : s.fk_project_code_to,
                                                     free_dim_1_from = string.IsNullOrEmpty(s.free_dim_1_from) ? "" : s.free_dim_1_from,
                                                     free_dim_1_to = string.IsNullOrEmpty(s.free_dim_1_to) ? "" : s.free_dim_1_to,
                                                     free_dim_2_from = string.IsNullOrEmpty(s.free_dim_2_from) ? "" : s.free_dim_2_from,
                                                     free_dim_2_to = string.IsNullOrEmpty(s.free_dim_2_to) ? "" : s.free_dim_2_to,
                                                     free_dim_3_from = string.IsNullOrEmpty(s.free_dim_3_from) ? "" : s.free_dim_3_from,
                                                     free_dim_3_to = string.IsNullOrEmpty(s.free_dim_3_to) ? "" : s.free_dim_3_to,
                                                     free_dim_4_from = string.IsNullOrEmpty(s.free_dim_4_from) ? "" : s.free_dim_4_from,
                                                     free_dim_4_to = string.IsNullOrEmpty(s.free_dim_4_to) ? "" : s.free_dim_4_to,
                                                     priority = s.priority
                                                 }).ToListAsync();

                foreach (var actionData in mrOverviewAction)
                {
                    var account = actionData.accCode;
                    var department = actionData.deptCode;
                    var function = actionData.funcCode;
                    var project = actionData.projectCode;
                    var freedim1 = actionData.freeDim1;
                    var freedim2 = actionData.freeDim2;
                    var freedim3 = actionData.freeDim3;
                    var freedim4 = actionData.freeDim4;
                    bool isCentral = false;

                    var finPlanLineSetup = tmdFinplanlineSetup.Where(x => x.fk_account_code == account).ToList();
                    foreach (var strB in finPlanLineSetup)
                    {
                        int chkDeptFrom = string.Compare(department, strB.fk_department_code_from);
                        int chkDeptTo = string.Compare(department, strB.fk_department_code_to);

                        int chkFuncFrom = string.Compare(function, strB.fk_function_code_from);
                        int chkFuncTo = string.Compare(function, strB.fk_function_code_to);

                        int chkProjFrom = string.Compare(project, strB.fk_project_code_from);
                        int chkProjTo = string.Compare(project, strB.fk_project_code_to);

                        int chkFD1From = string.Compare(freedim1, strB.free_dim_1_from);
                        int chkFD1To = string.Compare(freedim1, strB.free_dim_1_to);

                        int chkFD2From = string.Compare(freedim2, strB.free_dim_2_from);
                        int chkFD2To = string.Compare(freedim2, strB.free_dim_2_to);

                        int chkFD3From = string.Compare(freedim3, strB.free_dim_3_from);
                        int chkFD3To = string.Compare(freedim3, strB.free_dim_3_to);

                        int chkFD4From = string.Compare(freedim4, strB.free_dim_4_from);
                        int chkFD4To = string.Compare(freedim4, strB.free_dim_4_to);

                        if (account == strB.fk_account_code
                         && (chkDeptFrom >= 0 && chkDeptTo <= 0)
                         && (chkFuncFrom >= 0 && chkFuncTo <= 0)
                         && (chkProjFrom >= 0 && chkProjTo <= 0)
                         && (chkFD1From >= 0 && chkFD1To <= 0)
                         && (chkFD2From >= 0 && chkFD2To <= 0)
                         && (chkFD3From >= 0 && chkFD3To <= 0)
                         && (chkFD4From >= 0 && chkFD4To <= 0))
                        {
                            isCentral = true;
                            break;
                        }
                    }
                    if (!isCentral)
                    {
                        mrAction.Add(actionData);
                    }
                }
                return mrAction;
            }
            else
            {
                var reportingLine = await tenantDbContext.gmd_reporting_line.Where(x => x.report == "54_DRIFTA").ToListAsync();
                var tcoAccount = await tenantDbContext.tco_accounts.Where(x => x.pk_tenant_id == userDetails.tenant_id).ToListAsync();
                var accountCode = (from r in reportingLine
                                   join t in tcoAccount on r.fk_kostra_account_code equals t.fk_kostra_account_code
                                   select t).ToList();
                var pkAccountCode = accountCode.Select(x => x.pk_account_code).ToList();
                mrOverviewAction = mrOverviewAction.Where(x => !pkAccountCode.Contains(x.accCode)).ToList();
                mrAction.AddRange(mrOverviewAction);
                return mrAction;
            }
        }

        public List<KeyValuePair> filterValues(string userId)
        {
            UserData userdata = _utility.GetUserDetails(userId);
            Dictionary<string, clsLanguageString> langStr = _utility.GetLanguageStrings(userdata.language_preference, userdata.user_name, "MonthlyReport");
            List<KeyValuePair> isSamAction = new List<KeyValuePair>();
            isSamAction.Add(new KeyValuePair
            {
                key = "1",
                value = ((langStr.FirstOrDefault(v => v.Key == "MR_Target_Reported_Yes")).Value).LangText
            });
            isSamAction.Add(new KeyValuePair
            {
                key = "2",
                value = ((langStr.FirstOrDefault(v => v.Key == "MR_Target_Reported_No")).Value).LangText
            });
            return isSamAction;
        }

        private List<clsProposedBudgetAdjustmentsHelper> GetLineGrpForData(
            List<clsProposedBudgetAdjustmentsHelper> datasetBeforeFilter, UserData userDetails, int forecastPeriod, string nodeType)
        {
            var result = GetLineGrpForDataAsync(datasetBeforeFilter, userDetails, forecastPeriod, nodeType).GetAwaiter()
                .GetResult();
            return result;
        }

        private async Task<List<clsProposedBudgetAdjustmentsHelper>> GetLineGrpForDataAsync(List<clsProposedBudgetAdjustmentsHelper> datasetBeforeFilter, UserData userDetails, int forecastPeriod,string nodeType)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            // fetching data from datawarehouse
            string isParameterEnabled = await _utility.GetParameterValueAsync(userDetails.user_name, "MR_SCREEN_USE_1A");
            if (string.IsNullOrEmpty(isParameterEnabled) || isParameterEnabled.ToLower() != "true")
            {
                List<clsProposedBudgetAdjustmentsHelper> mrAction = new();
                int budgetYear = forecastPeriod / 100;
                var tmdFinplanlineSetup = await (from s in tenantDbContext.tmd_finplan_line_setup
                                                 where s.fk_tenant_id == userDetails.tenant_id && s.budget_year == budgetYear
                                                 orderby s.priority ascending
                                                 select new clsLine_OrderValidation
                                                 {
                                                     action_type = s.action_type,
                                                     line_order = s.line_order,
                                                     fk_account_code = s.fk_account_code,
                                                     fk_department_code_from = string.IsNullOrEmpty(s.fk_department_code_from) ? "" : s.fk_department_code_from,
                                                     fk_department_code_to = string.IsNullOrEmpty(s.fk_department_code_to) ? "" : s.fk_department_code_to,
                                                     fk_function_code_from = string.IsNullOrEmpty(s.fk_function_code_from) ? "" : s.fk_function_code_from,
                                                     fk_function_code_to = string.IsNullOrEmpty(s.fk_function_code_to) ? "" : s.fk_function_code_to,
                                                     fk_project_code_from = string.IsNullOrEmpty(s.fk_project_code_from) ? "" : s.fk_project_code_from,
                                                     fk_project_code_to = string.IsNullOrEmpty(s.fk_project_code_to) ? "" : s.fk_project_code_to,
                                                     free_dim_1_from = string.IsNullOrEmpty(s.free_dim_1_from) ? "" : s.free_dim_1_from,
                                                     free_dim_1_to = string.IsNullOrEmpty(s.free_dim_1_to) ? "" : s.free_dim_1_to,
                                                     free_dim_2_from = string.IsNullOrEmpty(s.free_dim_2_from) ? "" : s.free_dim_2_from,
                                                     free_dim_2_to = string.IsNullOrEmpty(s.free_dim_2_to) ? "" : s.free_dim_2_to,
                                                     free_dim_3_from = string.IsNullOrEmpty(s.free_dim_3_from) ? "" : s.free_dim_3_from,
                                                     free_dim_3_to = string.IsNullOrEmpty(s.free_dim_3_to) ? "" : s.free_dim_3_to,
                                                     free_dim_4_from = string.IsNullOrEmpty(s.free_dim_4_from) ? "" : s.free_dim_4_from,
                                                     free_dim_4_to = string.IsNullOrEmpty(s.free_dim_4_to) ? "" : s.free_dim_4_to,
                                                     priority = s.priority
                                                 }).ToListAsync();
                var connectedActionType = GetConnectedActionType(nodeType);
                if (connectedActionType != 0) tmdFinplanlineSetup=tmdFinplanlineSetup.Where(x => x.action_type == connectedActionType).ToList();
                foreach (var actionData in datasetBeforeFilter)
                {
                    var account = actionData.accCode;
                    var department = actionData.deptCode;
                    var function = actionData.funcCode;
                    var project = actionData.projectCode;
                    var freedim1 = actionData.freeDim1;
                    var freedim2 = actionData.freeDim2;
                    var freedim3 = actionData.freeDim3;
                    var freedim4 = actionData.freeDim4;
                    bool isCentral = false;

                    var finPlanLineSetup = tmdFinplanlineSetup.Where(x => x.fk_account_code == account).ToList();
                    foreach (var strB in finPlanLineSetup)
                    {
                        int chkDeptFrom = string.Compare(department, strB.fk_department_code_from);
                        int chkDeptTo = string.Compare(department, strB.fk_department_code_to);

                        int chkFuncFrom = string.Compare(function, strB.fk_function_code_from);
                        int chkFuncTo = string.Compare(function, strB.fk_function_code_to);

                        int chkProjFrom = string.Compare(project, strB.fk_project_code_from);
                        int chkProjTo = string.Compare(project, strB.fk_project_code_to);

                        int chkFD1From = string.Compare(freedim1, strB.free_dim_1_from);
                        int chkFD1To = string.Compare(freedim1, strB.free_dim_1_to);

                        int chkFD2From = string.Compare(freedim2, strB.free_dim_2_from);
                        int chkFD2To = string.Compare(freedim2, strB.free_dim_2_to);

                        int chkFD3From = string.Compare(freedim3, strB.free_dim_3_from);
                        int chkFD3To = string.Compare(freedim3, strB.free_dim_3_to);

                        int chkFD4From = string.Compare(freedim4, strB.free_dim_4_from);
                        int chkFD4To = string.Compare(freedim4, strB.free_dim_4_to);

                        if (account == strB.fk_account_code
                         && (chkDeptFrom >= 0 && chkDeptTo <= 0)
                         && (chkFuncFrom >= 0 && chkFuncTo <= 0)
                         && (chkProjFrom >= 0 && chkProjTo <= 0)
                         && (chkFD1From >= 0 && chkFD1To <= 0)
                         && (chkFD2From >= 0 && chkFD2To <= 0)
                         && (chkFD3From >= 0 && chkFD3To <= 0)
                         && (chkFD4From >= 0 && chkFD4To <= 0))
                        {
                            isCentral = true;
                            break;
                        }
                    }
                    if (isCentral)
                    {
                        mrAction.Add(actionData);
                    }
                }
                return mrAction;
            }
            else
            {
                var datafromgmdLine = await (from p in tenantDbContext.gmd_reporting_line
                                             where p.report == "54_DRIFTA"
                                             select new MROverviewHelper
                                             {
                                                 LineGroupId = p.line_group_id,
                                                 LineGroupName = p.line_group,
                                                 LineItemId = p.line_item_id,
                                                 LineItemName = p.line_item,
                                                 KostraAccount = p.fk_kostra_account_code,
                                             }).ToListAsync();
                var connectedLinegroup = GetConnectedLineGroup(nodeType);
                if (connectedLinegroup.Any())datafromgmdLine = datafromgmdLine.Where(x => connectedLinegroup.Contains(x.LineGroupId)).ToList();
                var tcoAccount = await tenantDbContext.tco_accounts.Where(x => x.pk_tenant_id == userDetails.tenant_id).ToListAsync();

                datasetBeforeFilter = (from ta in tcoAccount
                                       join ttd in datasetBeforeFilter on ta.pk_account_code equals ttd.accCode
                                       join gka in datafromgmdLine on ta.fk_kostra_account_code equals gka.KostraAccount into g
                                       from g2 in g.DefaultIfEmpty()
                                       where ttd.forecastPeriod == forecastPeriod
                                       select new clsProposedBudgetAdjustmentsHelper
                                       {
                                           actionId = ttd.actionId,
                                           actionDesc = ttd.actionDesc,
                                           deptCode = ttd.deptCode,
                                           funcCode = ttd.funcCode,
                                           accCode = ttd.accCode,
                                           tags = ttd.tags,
                                           alterCode = ttd.alterCode,
                                           priority = ttd.priority,
                                           year1Amount = ttd.year1Amount,
                                           year2Amount = ttd.year2Amount,
                                           year3Amount = ttd.year3Amount,
                                           year4Amount = ttd.year4Amount,
                                           year5Amount = ttd.year5Amount,
                                           description = ttd.description,
                                           internldesc = ttd.internldesc,
                                           forecastPeriod = ttd.forecastPeriod,
                                           isImported = ttd.isImported,
                                           is_MROverview_flag = ttd.is_MROverview_flag,// added for story 40448
                                           lineGrp = g2 != null ? g2.LineGroupId.ToString() : "100", //b2 != null ? b2.LineGroupId.ToString() : string.Empty
                                           isSamAction = ttd.isSamAction
                                       }).ToList();

                return datasetBeforeFilter;
            }
        }
        private int GetConnectedActionType(string nodeType)
        {
            int actionType = 0;
            switch(nodeType)
            {
                case "BudgetChangesCentralRevenue":
                    actionType = 1;
                    break;
                case "BudgetChangesCentralExpenses":
                    actionType = 100;
                    break;
                case "BudgetChangesFinIncomeExpense":
                    actionType = 2;
                    break;
                case "BudgetChangesProvisionAnnual":
                    actionType = 3;
                    break;
            }
            return actionType;
        }
        private List<int> GetConnectedLineGroup(string nodeType)
        {
            List<int> lineGroup = new();
            switch (nodeType)
            {
                case "BudgetChangesCentralRevenue":
                    lineGroup.Add(10);
                    break;
                case "BudgetChangesCentralExpenses":
                    break;
                case "BudgetChangesFinIncomeExpense":
                    lineGroup.AddRange(new List<int> {11,12,13 });
                    break;
                case "BudgetChangesProvisionAnnual":
                    lineGroup.Add(30);
                    break;
            }
            return lineGroup;
        }
        public JObject GetProposedBudgetAdjustmentsforDocExportService(string userName,
                                                                       int budgetYear,
                                                                       int budPeriod,
                                                                       string orgID,
                                                                       string serviceID,
                                                                       int orgLevel,
                                                                       int serviceLevel,
                                                                       bool isTypeInsert, List<string> selectedCol)
        {
            UserData userDetails = _utility.GetUserDetails(userName);
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            var orgVersionContent = _utility.GetOrgVersionSpecificContent(userName, budPeriod);

            List<string> lstDepartments = _utility.GetDepartmentsFromTcoOrgHierarchyTable(orgVersionContent, userName, orgLevel == 1 ? orgID : string.Empty,
                                                                                                    orgLevel == 2 ? orgID : string.Empty,
                                                                                                    orgLevel == 3 ? orgID : string.Empty,
                                                                                                    orgLevel == 4 ? orgID : string.Empty,
                                                                                                    orgLevel == 5 ? orgID : string.Empty);

            List<string> lstFunctions = _utility.GetFunctionsFromTcoServiceValues(orgVersionContent, userName, orgLevel == 1 ? orgID : string.Empty,
                                                                                                    orgLevel == 2 ? orgID : string.Empty,
                                                                                                    orgLevel == 3 ? orgID : string.Empty,
                                                                                                    orgLevel == 4 ? orgID : string.Empty,
                                                                                                    orgLevel == 5 ? orgID : string.Empty, serviceID == "-1" ? null : serviceID, serviceLevel);

            var datasetBeforeFilter = (from a in tenantDbContext.tfp_temp_header
                                       join b in tenantDbContext.tfp_temp_detail on new { a = a.fk_tenant_id, b = a.pk_temp_id } equals new { a = b.fk_tenant_id, b = b.fk_temp_id }
                                       join c in tenantDbContext.tco_fp_alter_codes on new { a = a.fk_tenant_id, b = b.fk_alter_code } equals new { a = c.fk_tenant_id, b = c.pk_alter_code } into c1
                                       from c2 in c1.DefaultIfEmpty()
                                       where a.fk_tenant_id == userDetails.tenant_id
                                          && b.budget_year == budgetYear
                                          && a.action_type == 60
                                          && b.forecast_period == budPeriod
                                          && a.is_MROverview_flag == false
                                          && a.is_parked_action == false
                                       select new clsProposedBudgetAdjustmentsHelper
                                       {
                                           actionId = a.pk_temp_id,
                                           actionDesc = a.description,
                                           deptCode = b.department_code,
                                           funcCode = b.function_code,
                                           tags = a.tags,
                                           alterCode = (string.IsNullOrEmpty(c2.limit_description) ? string.Empty : c2.limit_description),
                                           priority = a.priority,
                                           year1Amount = b.year_1_amount,
                                           year2Amount = b.year_2_amount,
                                           year3Amount = b.year_3_amount,
                                           year4Amount = b.year_4_amount,
                                           year5Amount = b.year_5_amount == null ? 0 : b.year_5_amount.Value,
                                           description = a.long_description,
                                           internldesc = a.financial_plan_description
                                       }).ToList();

            if (lstDepartments != null && lstDepartments.Count() > 0)
            {
                datasetBeforeFilter = datasetBeforeFilter.Where(x => lstDepartments.Contains(x.deptCode)).ToList();
            }
            if (lstFunctions != null && lstFunctions.Count() > 0)
            {
                datasetBeforeFilter = datasetBeforeFilter.Where(x => lstFunctions.Contains(x.funcCode)).ToList();
            }
            List<tco_org_hierarchy> lstOrgHierarchy = orgVersionContent.lstOrgHierarchy;
            List<clsProposedBudgetAdjustmentsHelper> baseDataWithMonrepcolum = new List<clsProposedBudgetAdjustmentsHelper>();
            if (selectedCol.Contains("monthRep_level_2_code") || selectedCol.Contains("monthRep_level_2_name"))
            {
                var monthlyReportParameters = _utility.GetParameterValue(userName, "MONTHREP_LEVEL_2");

                if (monthlyReportParameters.Contains("org_id"))
                {
                    switch (monthlyReportParameters)
                    {
                        case "org_id_1":
                            baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                       join oh in lstOrgHierarchy on a.deptCode equals oh.fk_department_code
                                                       select new clsProposedBudgetAdjustmentsHelper()
                                                       {
                                                           actionId = a.actionId,
                                                           actionDesc = a.actionDesc,
                                                           deptCode = a.deptCode,
                                                           funcCode = a.funcCode,
                                                           alterCode = a.alterCode,
                                                           tags = a.tags,
                                                           priority = a.priority,
                                                           year1Amount = a.year1Amount,
                                                           year2Amount = a.year2Amount,
                                                           year3Amount = a.year3Amount,
                                                           year4Amount = a.year4Amount,
                                                           year5Amount = a.year5Amount,
                                                           internldesc = a.internldesc,
                                                           monthRep_level2_code = oh.org_id_1,
                                                           monthRep_level2_name = oh.org_name_1,
                                                       }).ToList(); break;
                        case "org_id_2":
                            baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                       join oh in lstOrgHierarchy on a.deptCode equals oh.fk_department_code
                                                       select new clsProposedBudgetAdjustmentsHelper()
                                                       {
                                                           actionId = a.actionId,
                                                           actionDesc = a.actionDesc,
                                                           deptCode = a.deptCode,
                                                           funcCode = a.funcCode,
                                                           alterCode = a.alterCode,
                                                           tags = a.tags,
                                                           priority = a.priority,
                                                           year1Amount = a.year1Amount,
                                                           year2Amount = a.year2Amount,
                                                           year3Amount = a.year3Amount,
                                                           year4Amount = a.year4Amount,
                                                           year5Amount = a.year5Amount,
                                                           internldesc = a.internldesc,
                                                           monthRep_level2_code = oh.org_id_2,
                                                           monthRep_level2_name = oh.org_name_2,
                                                       }).ToList(); break;
                        case "org_id_3":
                            baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                       join oh in lstOrgHierarchy on a.deptCode equals oh.fk_department_code
                                                       select new clsProposedBudgetAdjustmentsHelper()
                                                       {
                                                           actionId = a.actionId,
                                                           actionDesc = a.actionDesc,
                                                           deptCode = a.deptCode,
                                                           funcCode = a.funcCode,
                                                           alterCode = a.alterCode,
                                                           tags = a.tags,
                                                           priority = a.priority,
                                                           year1Amount = a.year1Amount,
                                                           year2Amount = a.year2Amount,
                                                           year3Amount = a.year3Amount,
                                                           year4Amount = a.year4Amount,
                                                           year5Amount = a.year5Amount,
                                                           internldesc = a.internldesc,
                                                           monthRep_level2_code = oh.org_id_3,
                                                           monthRep_level2_name = oh.org_name_3,
                                                       }).ToList(); break;
                        case "org_id_4":
                            baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                       join oh in lstOrgHierarchy on a.deptCode equals oh.fk_department_code
                                                       select new clsProposedBudgetAdjustmentsHelper()
                                                       {
                                                           actionId = a.actionId,
                                                           actionDesc = a.actionDesc,
                                                           deptCode = a.deptCode,
                                                           funcCode = a.funcCode,
                                                           alterCode = a.alterCode,
                                                           tags = a.tags,
                                                           priority = a.priority,
                                                           year1Amount = a.year1Amount,
                                                           year2Amount = a.year2Amount,
                                                           year3Amount = a.year3Amount,
                                                           year4Amount = a.year4Amount,
                                                           year5Amount = a.year5Amount,
                                                           internldesc = a.internldesc,
                                                           monthRep_level2_code = oh.org_id_4,
                                                           monthRep_level2_name = oh.org_name_4,
                                                       }).ToList(); break;
                        case "org_id_5":
                            baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                       join oh in lstOrgHierarchy on a.deptCode equals oh.fk_department_code
                                                       select new clsProposedBudgetAdjustmentsHelper()
                                                       {
                                                           actionId = a.actionId,
                                                           actionDesc = a.actionDesc,
                                                           deptCode = a.deptCode,
                                                           funcCode = a.funcCode,
                                                           alterCode = a.alterCode,
                                                           tags = a.tags,
                                                           priority = a.priority,
                                                           year1Amount = a.year1Amount,
                                                           year2Amount = a.year2Amount,
                                                           year3Amount = a.year3Amount,
                                                           year4Amount = a.year4Amount,
                                                           year5Amount = a.year5Amount,
                                                           internldesc = a.internldesc,
                                                           monthRep_level2_code = oh.org_id_5,
                                                           monthRep_level2_name = oh.org_name_5,
                                                       }).ToList(); break;
                        case "org_id_6":
                            baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                       join oh in lstOrgHierarchy on a.deptCode equals oh.fk_department_code
                                                       select new clsProposedBudgetAdjustmentsHelper()
                                                       {
                                                           actionId = a.actionId,
                                                           actionDesc = a.actionDesc,
                                                           deptCode = a.deptCode,
                                                           funcCode = a.funcCode,
                                                           alterCode = a.alterCode,
                                                           tags = a.tags,
                                                           priority = a.priority,
                                                           year1Amount = a.year1Amount,
                                                           year2Amount = a.year2Amount,
                                                           year3Amount = a.year3Amount,
                                                           year4Amount = a.year4Amount,
                                                           year5Amount = a.year5Amount,
                                                           internldesc = a.internldesc,
                                                           monthRep_level2_code = oh.org_id_6,
                                                           monthRep_level2_name = oh.org_name_6,
                                                       }).ToList(); break;
                        case "org_id_7":
                            baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                       join oh in lstOrgHierarchy on a.deptCode equals oh.fk_department_code
                                                       select new clsProposedBudgetAdjustmentsHelper()
                                                       {
                                                           actionId = a.actionId,
                                                           actionDesc = a.actionDesc,
                                                           deptCode = a.deptCode,
                                                           funcCode = a.funcCode,
                                                           alterCode = a.alterCode,
                                                           tags = a.tags,
                                                           priority = a.priority,
                                                           year1Amount = a.year1Amount,
                                                           year2Amount = a.year2Amount,
                                                           year3Amount = a.year3Amount,
                                                           year4Amount = a.year4Amount,
                                                           year5Amount = a.year5Amount,
                                                           internldesc = a.internldesc,
                                                           monthRep_level2_code = oh.org_id_7,
                                                           monthRep_level2_name = oh.org_name_7,
                                                       }).ToList(); break;
                        case "org_id_8":
                            baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                       join oh in lstOrgHierarchy on a.deptCode equals oh.fk_department_code
                                                       select new clsProposedBudgetAdjustmentsHelper()
                                                       {
                                                           actionId = a.actionId,
                                                           actionDesc = a.actionDesc,
                                                           deptCode = a.deptCode,
                                                           funcCode = a.funcCode,
                                                           alterCode = a.alterCode,
                                                           tags = a.tags,
                                                           priority = a.priority,
                                                           year1Amount = a.year1Amount,
                                                           year2Amount = a.year2Amount,
                                                           year3Amount = a.year3Amount,
                                                           year4Amount = a.year4Amount,
                                                           year5Amount = a.year5Amount,
                                                           internldesc = a.internldesc,
                                                           monthRep_level2_code = oh.org_id_8,
                                                           monthRep_level2_name = oh.org_name_8,
                                                       }).ToList(); break;
                    }
                }
                else
                {
                    List<tco_service_values> lstServiceValues = tenantDbContext.tco_service_values.Where(x => x.fk_tenant_id == userDetails.tenant_id).ToList();
                    switch (monthlyReportParameters)
                    {
                        case "service_id_1":
                            baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                       join oh in lstServiceValues on a.funcCode equals oh.fk_function_code
                                                       select new clsProposedBudgetAdjustmentsHelper()
                                                       {
                                                           actionId = a.actionId,
                                                           actionDesc = a.actionDesc,
                                                           deptCode = a.deptCode,
                                                           funcCode = a.funcCode,
                                                           alterCode = a.alterCode,
                                                           tags = a.tags,
                                                           priority = a.priority,
                                                           year1Amount = a.year1Amount,
                                                           year2Amount = a.year2Amount,
                                                           year3Amount = a.year3Amount,
                                                           year4Amount = a.year4Amount,
                                                           year5Amount = a.year5Amount,
                                                           internldesc = a.internldesc,
                                                           monthRep_level2_code = oh.service_id_1,
                                                           monthRep_level2_name = oh.service_name_1,
                                                       }).ToList(); break;
                        case "service_id_2":
                            baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                       join oh in lstServiceValues on a.funcCode equals oh.fk_function_code
                                                       select new clsProposedBudgetAdjustmentsHelper()
                                                       {
                                                           actionId = a.actionId,
                                                           actionDesc = a.actionDesc,
                                                           deptCode = a.deptCode,
                                                           funcCode = a.funcCode,
                                                           alterCode = a.alterCode,
                                                           tags = a.tags,
                                                           priority = a.priority,
                                                           year1Amount = a.year1Amount,
                                                           year2Amount = a.year2Amount,
                                                           year3Amount = a.year3Amount,
                                                           year4Amount = a.year4Amount,
                                                           year5Amount = a.year5Amount,
                                                           internldesc = a.internldesc,
                                                           monthRep_level2_code = oh.service_id_2,
                                                           monthRep_level2_name = oh.service_name_2,
                                                       }).ToList(); break;
                        case "service_id_3":
                            baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                       join oh in lstServiceValues on a.funcCode equals oh.fk_function_code
                                                       select new clsProposedBudgetAdjustmentsHelper()
                                                       {
                                                           actionId = a.actionId,
                                                           actionDesc = a.actionDesc,
                                                           deptCode = a.deptCode,
                                                           funcCode = a.funcCode,
                                                           alterCode = a.alterCode,
                                                           tags = a.tags,
                                                           priority = a.priority,
                                                           year1Amount = a.year1Amount,
                                                           year2Amount = a.year2Amount,
                                                           year3Amount = a.year3Amount,
                                                           year4Amount = a.year4Amount,
                                                           year5Amount = a.year5Amount,
                                                           internldesc = a.internldesc,
                                                           monthRep_level2_code = oh.service_id_3,
                                                           monthRep_level2_name = oh.service_name_3,
                                                       }).ToList(); break;
                        case "service_id_4":
                            baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                       join oh in lstServiceValues on a.funcCode equals oh.fk_function_code
                                                       select new clsProposedBudgetAdjustmentsHelper()
                                                       {
                                                           actionId = a.actionId,
                                                           actionDesc = a.actionDesc,
                                                           deptCode = a.deptCode,
                                                           funcCode = a.funcCode,
                                                           alterCode = a.alterCode,
                                                           tags = a.tags,
                                                           priority = a.priority,
                                                           year1Amount = a.year1Amount,
                                                           year2Amount = a.year2Amount,
                                                           year3Amount = a.year3Amount,
                                                           year4Amount = a.year4Amount,
                                                           year5Amount = a.year5Amount,
                                                           internldesc = a.internldesc,
                                                           monthRep_level2_code = oh.service_id_4,
                                                           monthRep_level2_name = oh.service_name_4,
                                                       }).ToList(); break;
                        case "service_id_5":
                            baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                       join oh in lstServiceValues on a.funcCode equals oh.fk_function_code
                                                       select new clsProposedBudgetAdjustmentsHelper()
                                                       {
                                                           actionId = a.actionId,
                                                           actionDesc = a.actionDesc,
                                                           deptCode = a.deptCode,
                                                           funcCode = a.funcCode,
                                                           alterCode = a.alterCode,
                                                           tags = a.tags,
                                                           priority = a.priority,
                                                           year1Amount = a.year1Amount,
                                                           year2Amount = a.year2Amount,
                                                           year3Amount = a.year3Amount,
                                                           year4Amount = a.year4Amount,
                                                           year5Amount = a.year5Amount,
                                                           internldesc = a.internldesc,
                                                           monthRep_level2_code = oh.service_id_5,
                                                           monthRep_level2_name = oh.service_name_5,
                                                       }).ToList(); break;

                        default:
                            baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                       join oh in lstServiceValues on a.funcCode equals oh.fk_function_code
                                                       select new clsProposedBudgetAdjustmentsHelper()
                                                       {
                                                           actionId = a.actionId,
                                                           actionDesc = a.actionDesc,
                                                           deptCode = a.deptCode,
                                                           funcCode = a.funcCode,
                                                           alterCode = a.alterCode,
                                                           tags = a.tags,
                                                           priority = a.priority,
                                                           year1Amount = a.year1Amount,
                                                           year2Amount = a.year2Amount,
                                                           year3Amount = a.year3Amount,
                                                           year4Amount = a.year4Amount,
                                                           year5Amount = a.year5Amount,
                                                           internldesc = a.internldesc,
                                                           monthRep_level2_code = oh.service_id_2,
                                                           monthRep_level2_name = oh.service_name_2,
                                                       }).ToList(); break;
                    }
                }
            }
            else
            {
                baseDataWithMonrepcolum = datasetBeforeFilter;
            }

            var groupedDatasetAfterFilter = (from d in baseDataWithMonrepcolum
                                             group d by new
                                             {
                                                 monthRep_level2_code = selectedCol.Contains("monthRep_level_2_code") ? d.monthRep_level2_code : null,
                                                 monthRep_level2_name = selectedCol.Contains("monthRep_level_2_name") ? d.monthRep_level2_name : null,
                                                 d.actionId,
                                                 d.actionDesc,
                                                 d.alterCode,
                                                 d.tags,
                                                 d.priority,
                                                 d.description,
                                                 d.internldesc
                                             } into g
                                             select new
                                             {
                                                 actionId = g.Key.actionId,
                                                 actionDesc = g.Key.actionDesc,
                                                 tags = g.Key.tags,
                                                 alterCode = g.Key.alterCode,
                                                 priority = g.Key.priority,
                                                 year1Amount = g.Sum(x => x.year1Amount),
                                                 year2Amount = g.Sum(x => x.year2Amount),
                                                 year3Amount = g.Sum(x => x.year3Amount),
                                                 year4Amount = g.Sum(x => x.year4Amount),
                                                 year5Amount = g.Sum(x => x.year5Amount),
                                                 description = g.Key.description,
                                                 internldesc = g.Key.internldesc,
                                                 monthRep_level2_code = g.Key.monthRep_level2_code,
                                                 monthRep_level2_name = g.Key.monthRep_level2_name,
                                             }).OrderBy(x => x.alterCode).ThenBy(y => y.actionDesc).ToList();

            List<KeyValueData> lstActionTags = (from atg in tenantDbContext.tcoActionTags
                                                where atg.FkTenantId == userDetails.tenant_id
                                                select new KeyValueData
                                                {
                                                    KeyId = atg.PkId,
                                                    ValueString = atg.TagDescription
                                                }).ToList();

            dynamic result = new JObject();

            dynamic header = new JArray();
            dynamic header1 = new JObject();

            Dictionary<string, clsLanguageString> langStringValuesMonthlyReport = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "MonthlyReport");

            header1.title = ((langStringValuesMonthlyReport.FirstOrDefault(v => v.Key == "proposed_bud_adj_title")).Value).LangText;
            header1.titleDescription = ((langStringValuesMonthlyReport.FirstOrDefault(v => v.Key == "proposed_bud_adj_desc")).Value).LangText;
            header.Add(header1);

            result.header = header;

            dynamic columnsArray = new JArray();
            columnsArray.Add(GenerateColumnObject(((langStringValuesMonthlyReport.FirstOrDefault(v => v.Key == "proposed_bud_adj_monthreplevel2_code")).Value).LangText, "monthRep_level2_code", 1, false, false, null, "text-align:left;width:25%", "text-align:left;width:25%"));
            columnsArray.Add(GenerateColumnObject(((langStringValuesMonthlyReport.FirstOrDefault(v => v.Key == "proposed_bud_adj_monthreplevel2_name")).Value).LangText, "monthRep_level2_name", 1, false, false, null, "text-align:left;width:25%", "text-align:left;width:25%"));
            columnsArray.Add(GenerateColumnObject(((langStringValuesMonthlyReport.FirstOrDefault(v => v.Key == "proposed_bud_adj_description")).Value).LangText, "description", 1, false, false, null, "text-align:left;width:25%", "text-align:left;width:25%"));
            columnsArray.Add(GenerateColumnObject(((langStringValuesMonthlyReport.FirstOrDefault(v => v.Key == "proposed_bud_adj_type")).Value).LangText, "type", 3, false, false, null, "text-align:right", "text-align:right"));
            columnsArray.Add(GenerateColumnObject(((langStringValuesMonthlyReport.FirstOrDefault(v => v.Key == "proposed_bud_adj_tags")).Value).LangText, "tags", 3, false, false, null, "text-align:left;width:25%", "text-align:left;width:25%"));
            columnsArray.Add(GenerateColumnObject((budgetYear).ToString(), "year1", 4, false, false, "{0:n0}", "text-align:right", "text-align:right"));
            columnsArray.Add(GenerateColumnObject((budgetYear + 1).ToString(), "year2", 5, false, false, "{0:n0}", "text-align:right", "text-align:right"));
            columnsArray.Add(GenerateColumnObject((budgetYear + 2).ToString(), "year3", 6, false, false, "{0:n0}", "text-align:right", "text-align:right"));
            columnsArray.Add(GenerateColumnObject((budgetYear + 3).ToString(), "year4", 7, false, false, "{0:n0}", "text-align:right", "text-align:right"));
            columnsArray.Add(GenerateColumnObject((budgetYear + 4).ToString(), "year5", 8, false, false, "{0:n0}", "text-align:right", "text-align:right"));

            result.Add("columns", columnsArray);
            List<decimal> lstTotals = new List<decimal>() { 0, 0, 0, 0, 0 };
            var data = new JArray();
            foreach (var d in groupedDatasetAfterFilter)
            {
                dynamic obj = new JObject();
                obj.id = d.actionId;
                obj.monthRep_level2_code = d.monthRep_level2_code;
                obj.monthRep_level2_name = d.monthRep_level2_name;
                obj.description = d.actionDesc;
                StringBuilder sb = new StringBuilder();
                if (!string.IsNullOrEmpty(d.tags))
                {
                    List<int> mappedTagIds = d.tags.Split(',').Select(int.Parse).ToList();
                    //sb.Append("<br>");
                    foreach (var tags in mappedTagIds)
                    {
                        var firstOrDefault = lstActionTags.FirstOrDefault(x => x.KeyId == tags);
                        if (firstOrDefault != null)
                        {
                            //sb.Append("<span class='bp-tag-selected'>" + firstOrDefault.ValueString + "</span>&nbsp;");
                            sb.Append(firstOrDefault.ValueString + " ");
                        }
                    }
                }

                obj.type = d.alterCode;
                obj.tags = sb.ToString();
                obj.year1 = d.year1Amount / 1000;
                lstTotals[0] = lstTotals[0] + (d.year1Amount / 1000);
                obj.year2 = d.year2Amount / 1000;
                lstTotals[1] = lstTotals[1] + (d.year2Amount / 1000);
                obj.year3 = d.year3Amount / 1000;
                lstTotals[2] = lstTotals[2] + (d.year3Amount / 1000);
                obj.year4 = d.year4Amount / 1000;
                lstTotals[3] = lstTotals[3] + (d.year4Amount / 1000);
                obj.year5 = d.year5Amount / 1000;
                lstTotals[4] = lstTotals[4] + (d.year5Amount / 1000);
                obj.tooltipdesc = d.description;
                obj.internldesc = d.internldesc;

                data.Add(obj);
            }

            dynamic objTotals = new JObject();
            objTotals.id = string.Empty;
            objTotals.monthRep_level2_code = selectedCol.Contains("monthRep_level_2_code") ? ((langStringValuesMonthlyReport.FirstOrDefault(v => v.Key == "proposed_bud_adj_total")).Value).LangText : string.Empty;

            objTotals.monthRep_level2_name = (!selectedCol.Contains("monthRep_level_2_code") && selectedCol.Contains("monthRep_level_2_name")) ? ((langStringValuesMonthlyReport.FirstOrDefault(v => v.Key == "proposed_bud_adj_total")).Value).LangText : string.Empty;

            objTotals.description = (!selectedCol.Contains("monthRep_level_2_code") && !selectedCol.Contains("monthRep_level_2_name")) ? ((langStringValuesMonthlyReport.FirstOrDefault(v => v.Key == "proposed_bud_adj_total")).Value).LangText : string.Empty;

            objTotals.type = string.Empty;
            objTotals.tags = string.Empty;
            objTotals.year1 = lstTotals[0];
            objTotals.year2 = lstTotals[1];
            objTotals.year3 = lstTotals[2];
            objTotals.year4 = lstTotals[3];
            objTotals.year5 = lstTotals[4];
            objTotals.tooltipdesc = string.Empty;
            objTotals.internldesc = string.Empty;

            data.Add(objTotals);

            result.Add("data", data);

            return result;
        }

        public string DeleteBListAction(string userName, int actionId)
        {
            var result = DeleteBListActionAsync(userName, actionId).GetAwaiter().GetResult();
            return result;
        }

        public async Task<string> DeleteBListActionAsync(string userName, int actionId)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userName);
                TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();

                var blistHeaderData = await tenantDbContext.tfp_temp_header.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_temp_id == actionId).ToListAsync();
                if (blistHeaderData.Any())
                {
                    tenantDbContext.tfp_temp_detail.RemoveRange(await tenantDbContext.tfp_temp_detail.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.fk_temp_id == actionId).ToListAsync());
                    tenantDbContext.tfp_temp_header.RemoveRange(blistHeaderData);
                }

                await tenantDbContext.SaveChangesAsync();

                return "success";
            }
            catch
            {
                return "Failed";
            }
        }

        public async Task UploadAttachment(AttachmentInput input)
        {
            await _actionUploadFileUtility.UploadAttachmentAsync(input);
        }

        public Dictionary<string, string> GetAttachmentURL(string userId, Guid attachmentId)
        {
            var result = GetAttachmentURLAsync(userId, attachmentId).GetAwaiter().GetResult();
            return result;
        }

        public async Task<Dictionary<string, string>> GetAttachmentURLAsync(string userId, Guid attachmentId)
        {
            return await _actionUploadFileUtility.GetAttachmentURLAsync(userId, attachmentId);
        }

        public async Task DeleteAttachment(string userId, Guid attachmentId)
        {
            await _actionUploadFileUtility.DeleteAttachment(userId, attachmentId);
        }

        public List<Attachments> GetAttachments(AttachmentInput input, string userId)
        {
            var result = GetAttachmentsAsync(input, userId).GetAwaiter().GetResult();
            return result;
        }

        public async Task<List<Attachments>> GetAttachmentsAsync(AttachmentInput input, string userId)
        {
            return await _actionUploadFileUtility.GetAttachmentsAsync(input, userId);
        }

        public dynamic GetActionDetailsBList(string userID, int actionId, string orgId, string serviceId,
            int budgetYear, int orgLevel)
        {
            var result = GetActionDetailsBListAsync(userID, actionId, orgId, serviceId, budgetYear, orgLevel)
                .GetAwaiter().GetResult();
            return result;
        }

        public async Task<dynamic> GetActionDetailsBListAsync(string userID, int actionId, string orgId, string serviceId, int budgetYear, int orgLevel, bool isChapterSetupMR = false, string attributeId = null)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            Dictionary<string, clsLanguageString> langStringValues =
                await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name,
                    "ConsequenceAdjustedBudget");
            Dictionary<string, clsLanguageString> langStringValuesYBtype = 
                await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name,
                    "YearlyBudget");
            Dictionary<string, clsLanguageString> langStringValuesMonRep =
                await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name,
                    "MonthlyReport");
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            List<tco_free_dim_values> lstFreeDimValues = (await _utility.GetFreeDimValuesAsync(userID)).ToList();

            dynamic lstAjstmt = JArray.FromObject(await _consequenceAdjustBudget.GetAdjustmentCodesAsync(userID));
            dynamic lstAlter = JArray.FromObject(await _consequenceAdjustBudget.GetAlterCodesAsync(userID, 60));

            var lst = await (from th in tenantDbContext.tfp_temp_header
                             join td in tenantDbContext.tfp_temp_detail on new { a = th.fk_tenant_id, b = th.pk_temp_id } equals new { a = td.fk_tenant_id, b = td.fk_temp_id }
                             join ta in tenantDbContext.tco_accounts on
                                 new { b = td.fk_account_code, c = th.fk_tenant_id } equals
                                 new { b = ta.pk_account_code, c = ta.pk_tenant_id }
                             join f1 in tenantDbContext.tco_functions on
                                 new { a = td.function_code, b = th.fk_tenant_id } equals
                                 new { a = f1.pk_Function_code, b = f1.pk_tenant_id }
                             join p1 in tenantDbContext.tco_projects.Where(x => x.date_from.Year <= budgetYear && x.date_to.Year >= budgetYear) on
                                 new { a = td.project_code, b = th.fk_tenant_id } equals
                                 new { a = p1.pk_project_code, b = p1.fk_tenant_id } into lojproj
                             from pj in lojproj.DefaultIfEmpty()
                             where (th.fk_tenant_id == userDetails.tenant_id && td.budget_year == budgetYear)
                             && budgetYear >= ta.dateFrom.Year && budgetYear <= ta.dateTo.Year
                             && budgetYear >= f1.dateFrom.Year && budgetYear <= f1.dateTo.Year
                             select new clsActions
                             {
                                 actionType = th.action_type,
                                 actionID = th.pk_temp_id,
                                 actionDescription = th.description,
                                 consequence = th.consequence,
                                 pk_id = td.pk_id,
                                 accountCode = ta.pk_account_code,
                                 accountName = ta.display_name,
                                 deptCode = td.department_code,
                                 functionCode = td.function_code,
                                 functionName = f1.display_name,
                                 projectCode = td.project_code == null ? String.Empty : td.project_code,
                                 projectName = (pj == null ? String.Empty : pj.project_name),
                                 freedimCode1 = td.free_dim_1 == null ? String.Empty : td.free_dim_1,
                                 freedimName1 = string.Empty,
                                 freedimCode2 = td.free_dim_2 == null ? String.Empty : td.free_dim_2,
                                 freedimName2 = string.Empty,
                                 freedimCode3 = td.free_dim_3 == null ? String.Empty : td.free_dim_3,
                                 freedimName3 = string.Empty,
                                 freedimCode4 = td.free_dim_4 == null ? String.Empty : td.free_dim_4,
                                 freedimName4 = string.Empty,
                                 year1Ammount = td.year_1_amount,
                                 year2Ammount = td.year_2_amount,
                                 year3Ammount = td.year_3_amount,
                                 year4Ammount = td.year_4_amount,
                                 year5Ammount = td.year_5_amount == null ? 0 : td.year_5_amount,
                                 fk_area_id = th.fk_area_id == null ? 0 : th.fk_area_id.Value,
                                 tag = th.tag == null ? "" : th.tag,
                                 priority = th.priority == null ? 0 : th.priority.Value,
                                 long_description = th.long_description == null ? "" : th.long_description,
                                 fk_alter_code = td.fk_alter_code,
                                 fk_adjustment_code = td.fk_adjustment_code,
                                 tags = th.tags,
                                 description = td.description,
                                 fk_key_id = td.fk_key_id == null ? 0 : td.fk_key_id.Value,
                                 isParkedAction = th.is_parked_action
                             }).ToListAsync();

            lst = lst.Where(x => x.actionID == actionId).ToList();

            var adjustmentCodesSet = await tenantDbContext.tco_adjustment_codes
                .Where(y => y.fk_tenant_id == userDetails.tenant_id).ToListAsync();

            var alterCodesSet = await tenantDbContext.tco_fp_alter_codes
                .Where(y => y.fk_tenant_id == userDetails.tenant_id).ToListAsync();

            lst.ForEach(x =>
            {
                x.freedimName1 = lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode1) == null
                    ? string.Empty
                    : lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode1).description;
                x.freedimName2 = lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode2) == null
                    ? string.Empty
                    : lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode2).description;
                x.freedimName3 = lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode3) == null
                    ? string.Empty
                    : lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode3).description;
                x.freedimName4 = lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode4) == null
                    ? string.Empty
                    : lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode4).description;
                x.adjustmentCodeName =
                    adjustmentCodesSet.Where(
                        y => y.fk_tenant_id == userDetails.tenant_id && y.pk_adjustment_code == x.fk_adjustment_code)
                        .ToList()
                        .Count == 0
                        ? string.Empty
                        : adjustmentCodesSet.Where(
                            y => y.fk_tenant_id == userDetails.tenant_id && y.pk_adjustment_code == x.fk_adjustment_code)
                            .Select(y => y.description)
                            .First()
                            .ToString();
                x.alterCodeName =
                    alterCodesSet.Where(
                        y => y.fk_tenant_id == userDetails.tenant_id && y.pk_alter_code == x.fk_alter_code)
                        .ToList()
                        .Count == 0
                        ? string.Empty
                        : alterCodesSet.Where(
                            y => y.fk_tenant_id == userDetails.tenant_id && y.pk_alter_code == x.fk_alter_code)
                            .Select(y => y.alter_description)
                            .First()
                            .ToString();
            });

            Dictionary<string, string> departments = new Dictionary<string, string>();
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userID, _utility.GetForecastPeriod(budgetYear, 1));
            List<Department> lstDepartments = (await _utility.GetTenantDepartmentsAsync(userID, budgetYear)).ToList();
            List<string> departmentsOnOrg = new List<string>();
            switch (orgLevel)
            {
                case 1:
                    departmentsOnOrg = orgVersionContent.lstOrgHierarchy.Where(x => x.org_id_1 == orgId).Select(x => x.fk_department_code).ToList();
                    break;

                case 2:
                    departmentsOnOrg = orgVersionContent.lstOrgHierarchy.Where(x => x.org_id_2 == orgId).Select(x => x.fk_department_code).ToList();
                    break;

                case 3:
                    departmentsOnOrg = orgVersionContent.lstOrgHierarchy.Where(x => x.org_id_3 == orgId).Select(x => x.fk_department_code).ToList();
                    break;

                case 4:
                    departmentsOnOrg = orgVersionContent.lstOrgHierarchy.Where(x => x.org_id_4 == orgId).Select(x => x.fk_department_code).ToList();
                    break;

                case 5:
                    departmentsOnOrg = orgVersionContent.lstOrgHierarchy.Where(x => x.org_id_5 == orgId).Select(x => x.fk_department_code).ToList();
                    break;

                case 6:
                    departmentsOnOrg = orgVersionContent.lstOrgHierarchy.Where(x => x.org_id_6 == orgId).Select(x => x.fk_department_code).ToList();
                    break;

                case 7:
                    departmentsOnOrg = orgVersionContent.lstOrgHierarchy.Where(x => x.org_id_7 == orgId).Select(x => x.fk_department_code).ToList();
                    break;

                case 8:
                    departmentsOnOrg = orgVersionContent.lstOrgHierarchy.Where(x => x.org_id_8 == orgId).Select(x => x.fk_department_code).ToList();
                    break;
            }

            if (departmentsOnOrg != null && departmentsOnOrg.Count > 0)
            {
                lst = lst.Where(x => departmentsOnOrg.Contains(x.deptCode)).ToList();
            }
            // Get Departments if chapterSetup is true
            List<string> deptFromChapter = new List<string>();
            if (isChapterSetupMR)
            {
                if(attributeId != "-1" && !string.IsNullOrEmpty(attributeId))
                {
                    deptFromChapter = await _finUtility.GetAttributeConnectedDepartmentsForSelectedOrgAsync(userID, budgetYear, orgId, orgLevel, attributeId);
                    lstDepartments = (from d1 in deptFromChapter
                                      join d2 in lstDepartments
                                      on d1 equals d2.departmentValue
                                      select new Department
                                      {
                                          departmentValue = d1,
                                          departmentText = d2.departmentText
                                      }).ToList();
                }
            }

            

            foreach (var data in lstDepartments)
            {
                string deptID = data.departmentValue;
                string deptName = data.departmentText;
                if (!departments.ContainsKey(deptID))
                {
                    departments.Add(deptID, deptName);
                }
            }

            var result = (from a in lst
                          join b in departments on a.deptCode equals b.Key
                          select new clsActions
                          {
                              actionType = a.actionType,
                              actionID = a.actionID,
                              actionDescription = a.actionDescription,
                              consequence = a.consequence,
                              pk_id = a.pk_id,
                              accountCode = a.accountCode,
                              accountName = a.accountName,
                              deptCode = a.deptCode,
                              deptName = b.Value,
                              functionCode = a.functionCode,
                              functionName = a.functionName,
                              projectCode = a.projectCode,
                              projectName = a.projectName,
                              freedimCode1 = a.freedimCode1,
                              freedimName1 = a.freedimName1,
                              freedimCode2 = a.freedimCode2,
                              freedimName2 = a.freedimName2,
                              freedimCode3 = a.freedimCode3,
                              freedimName3 = a.freedimName3,
                              freedimCode4 = a.freedimCode4,
                              freedimName4 = a.freedimName4,
                              year1Ammount = a.year1Ammount,
                              year2Ammount = a.year2Ammount,
                              year3Ammount = a.year3Ammount,
                              year4Ammount = a.year4Ammount,
                              year5Ammount = a.year5Ammount,
                              fk_adjustment_code = a.fk_adjustment_code,
                              adjustmentCodeName = a.adjustmentCodeName,
                              fk_alter_code = a.fk_alter_code,
                              alterCodeName = a.alterCodeName,
                              fk_area_id = a.fk_area_id,
                              tag = a.tag,
                              priority = a.priority,
                              long_description = a.long_description,
                              tags = a.tags,
                              description = a.description,
                              fk_key_id = a.fk_key_id,
                              isParkedAction = a.isParkedAction
                          }).ToList();

            List<freedimDefinition> freeDims = await _utility.GetFreeDimDefinitionsBasedOnPageIdAsync(userID, string.Empty);
            List<freedimDefinition> freeDimColumns = freeDims.ToList();

            Dictionary<string, clsLanguageString> langStringValuesFP =
                await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "FinancialPlan");
            gmd_action_types actionTypeDesc =
                await tenantDbContext.gmd_action_types.FirstOrDefaultAsync(
                    x => x.pk_language == userDetails.language_preference && x.pk_action_type == 60);

            dynamic actions = new JObject();

            actions.actionId = actionId;
            actions.actionDescription = lst.Count == 0 ? string.Empty : (lst.FirstOrDefault()).actionDescription;
            actions.fk_area_id = lst.Count == 0 ? 0 : (lst.FirstOrDefault()).fk_area_id;
            actions.priority = lst.Count == 0 ? 0 : (lst.FirstOrDefault()).priority;
            actions.long_description = lst.Count == 0 ? string.Empty : (lst.FirstOrDefault()).long_description;
            actions.groupId = 60.ToString();
            actions.groupName = actionTypeDesc == null ? "" : actionTypeDesc.action_type_descr;
            actions.display_description_apendix_flag = false;
            actions.isParkedAction = lst.Any() ? lst.FirstOrDefault().isParkedAction : lst.Any();

            dynamic nonActiveChangeColumns = new JArray();
            dynamic activeChangeColumns = new JArray();

            activeChangeColumns.Add(GenerateColumnObject(" ", "id", false, true, "display:none", "display:none", null, null, null, true));
            activeChangeColumns.Add(GenerateColumnObject(" ", "guid", false, true, "display:none", "display:none", null, null, null, true));

            activeChangeColumns.Add(GenerateColumnObject(((langStringValues.FirstOrDefault(v => v.Key == "account_text")).Value).LangText, "account", false, false, "border-left: none; text-align:left;width:137px", "text-align:left;border-left: none;",
                null,
                "<span title = '#=account.accountText#' >#=account.accountValue#</span>",
                "<span id='financingAccount'>" + ((langStringValues.FirstOrDefault(v => v.Key == "account_text")).Value).LangText + "</span><span class='red'>*</span>", true));

            activeChangeColumns.Add(GenerateColumnObject(((langStringValues.FirstOrDefault(v => v.Key == "department_text")).Value).LangText, "department", false, false, "border-left: none; text-align:left;width:137px", "text-align:left;border-left: none;",
                null,
                "<span title = '#=department.departmentText#' >#=department.departmentValue#</span>",
                "<span id='financingDepartment'>" + ((langStringValues.FirstOrDefault(v => v.Key == "department_text")).Value).LangText + "</span><span class='red'>*</span>", true));

            activeChangeColumns.Add(GenerateColumnObject(((langStringValues.FirstOrDefault(v => v.Key == "function_text")).Value).LangText, "functionn", false, false, "border-left: none; text-align:left;width:137px", "text-align:left;border-left: none;",
                null,
                "<span title = '#=functionn.functionText#' >#=functionn.functionValue#</span>",
                "<span id='financingFunction'>" + ((langStringValues.FirstOrDefault(v => v.Key == "function_text")).Value).LangText + "</span><span class='red'>*</span>", true));

            activeChangeColumns.Add(GenerateColumnObject(((langStringValues.FirstOrDefault(v => v.Key == "fp_project_text")).Value).LangText, "project", false, false, "border-left: none; text-align:left;width:137px", "text-align:left;border-left: none;",
                null,
                "<span title = '#=project.project_name#' >#=project.fk_project_code#</span>",
                "<span>" + ((langStringValues.FirstOrDefault(v => v.Key == "fp_project_text")).Value).LangText + "</span>", true));

            foreach (var freedim in freeDimColumns)
            {
                if (freedim.freeDimColumn == "free_dim_1")
                {
                    activeChangeColumns.Add(GenerateColumnObject(freedim.freeDimHeader, "freeDim" + 1.ToString()
                        , false, false, "border-left: none; text-align:left;width:137px", "text-align:left;border-left: none;", null,
                        "<span title = '#=freeDim" + 1.ToString() + ".freedim_name#' >#=freeDim" + 1.ToString() + ".fk_freedim_code#</span>",
                        "<span id='financingFreeDim" + 1.ToString() + "'>" + freedim.freeDimHeader.ToString() + "</span>",
                      true));
                }

                if (freedim.freeDimColumn == "free_dim_2")
                {
                    activeChangeColumns.Add(GenerateColumnObject(freedim.freeDimHeader, "freeDim" + 2.ToString()
                        , false, false, "border-left: none; text-align:left;width:137px", "text-align:left;border-left: none;", null,
                        "<span title = '#=freeDim" + 2.ToString() + ".freedim_name#' >#=freeDim" + 2.ToString() + ".fk_freedim_code#</span>",
                        "<span id='financingFreeDim" + 2.ToString() + "'>" + freedim.freeDimHeader.ToString() + "</span>",
                      true));
                }

                if (freedim.freeDimColumn == "free_dim_3")
                {
                    activeChangeColumns.Add(GenerateColumnObject(freedim.freeDimHeader, "freeDim" + 3.ToString()
                        , false, false, "border-left: none; text-align:left;width:137px", "text-align:left;border-left: none;", null,
                        "<span title = '#=freeDim" + 3.ToString() + ".freedim_name#' >#=freeDim" + 3.ToString() + ".fk_freedim_code#</span>",
                        "<span id='financingFreeDim" + 3.ToString() + "'>" + freedim.freeDimHeader.ToString() + "</span>",
                      true));
                }

                if (freedim.freeDimColumn == "free_dim_4")
                {
                    activeChangeColumns.Add(GenerateColumnObject(freedim.freeDimHeader, "freeDim" + 4.ToString()
                        , false, false, "border-left: none; text-align:left;width:137px", "text-align:left;border-left: none;", null,
                        "<span title = '#=freeDim" + 4.ToString() + ".freedim_name#' >#=freeDim" + 4.ToString() + ".fk_freedim_code#</span>",
                        "<span id='financingFreeDim" + 4.ToString() + "'>" + freedim.freeDimHeader.ToString() + "</span>",
                      true));
                }
            }
            if (lstAjstmt.Count > 1)
            {
                activeChangeColumns.Add(GenerateColumnObject(((langStringValuesFP.FirstOrDefault(v => v.Key == "fp_adjustment_code")).Value).LangText, "adjustmentCode", false, false, "border-left: none; text-align:left;width:137px", "text-align:left;border-left: none;",
                null,
                "<span title = '#=adjustmentCode.value#' >#=adjustmentCode.key#</span>",
                "<span id='financingAdjCode'>" + ((langStringValuesFP.FirstOrDefault(v => v.Key == "fp_adjustment_code")).Value).LangText,
                true));
            }
            if (lstAlter.Count > 1)
            {
                activeChangeColumns.Add(GenerateColumnObject(((langStringValuesFP.FirstOrDefault(v => v.Key == "fp_alter_code_text")).Value).LangText, "alterCode", false, false, "border-left: none; text-align:left;width:137px", "text-align:left;border-left: none;", null,
                "<span title='#=alterCode.value#'>#=alterCode.key#</span>",
                "<span id='financingAlterCode'>" + ((langStringValuesFP.FirstOrDefault(v => v.Key == "fp_alter_code_text")).Value).LangText + "</span><span class='red'>*</span>", true));
            }
            activeChangeColumns.Add(GenerateColumnObject(budgetYear.ToString(), "year1", false, false, "border-left: none; text-align:right;width:90px", "text-align:right;border-left: none;", null,
                "#= (year1 == null) ? '0' :  kendo.toString(year1, 'n0')  #",
                "<span id='MODULESYear1'>" + budgetYear.ToString() + "</span>", true));
            activeChangeColumns.Add(GenerateColumnObject((budgetYear + 1).ToString(), "year2", false, false, "border-left: none; text-align:right;width:90px", "text-align:right;border-left: none;", null,
                "#= (year2 == null) ? '0' :  kendo.toString(year2, 'n0')  #",
                "<span id='MODULESYear2'>" + (budgetYear + 1).ToString() + "</span>", true));
            activeChangeColumns.Add(GenerateColumnObject((budgetYear + 2).ToString(), "year3", false, false, "border-left: none; text-align:right;width:90px", "text-align:right;border-left: none;", null,
                "#= (year3 == null) ? '0' :  kendo.toString(year3, 'n0')  #",
                "<span id='MODULESYear3'>" + (budgetYear + 2).ToString() + "</span>", true));
            activeChangeColumns.Add(GenerateColumnObject((budgetYear + 3).ToString(), "year4", false, false, "border-left: none; text-align:right;width:90px", "text-align:right;border-left: none;", null,
                "#= (year4 == null) ? '0' :  kendo.toString(year4, 'n0')  #",
                "<span id='MODULESYear4'>" + (budgetYear + 3).ToString() + "</span>", true));
            activeChangeColumns.Add(GenerateColumnObject((budgetYear + 4).ToString(), "year5", false, false, "border-left: none; text-align:right;width:90px", "text-align:right;border-left: none;", null,
                "#= (year5 == null) ? '0' :  kendo.toString(year5, 'n0')  #",
                "<span id='MODULESYear5'>" + (budgetYear + 4).ToString() + "</span>", true));

            activeChangeColumns.Add(GenerateColumnObject(langStringValues.FirstOrDefault(v => v.Key == "CAB_description").Value.LangText, "description", false, false, "border-left: none;white-space:normal;text-align:left;width:90px", "text-align:left;border-left: none;",
                null,
                "#= (description == '') ? '" + langStringValuesMonRep.FirstOrDefault(v => v.Key == "PLHDR_Enter_Description").Value.LangText + "':description#",
                "<span>" + langStringValues.FirstOrDefault(v => v.Key == "CAB_description").Value.LangText + "</span>", true));

            activeChangeColumns.Add(GenerateColumnObject(((langStringValuesMonRep.FirstOrDefault(v => v.Key == "PBA_periodic_key_text")).Value).LangText, "periodicKey", false, false, "border-left: none; text-align:left;width:90px", "text-align:left;border-left: none;",
            null,
            "<span title = '#=periodicKey.value#' >#=periodicKey.value#</span>",
            "<span id='periodicKeyCode'>" + ((langStringValuesMonRep.FirstOrDefault(v => v.Key == "PBA_periodic_key_text")).Value).LangText + "</span><span class='red' id='commonPeriodicKeyMandatory'></span>",
            true));

            actions.Add("nonActiveChangeColumns", nonActiveChangeColumns);
            actions.Add("activeChangeColumns", activeChangeColumns);

            dynamic nonActiveChangeData = new JArray();
            actions.Add("nonActiveChangeData", nonActiveChangeData);

            List<int> TagIds = new List<int>();
            if (lst.Count() > 0 && !string.IsNullOrEmpty((lst.FirstOrDefault()).tags))
            {
                TagIds = (lst.FirstOrDefault()).tags.Split(',').Select(int.Parse).ToList();
            }
            actions.Add("tags", JToken.FromObject(TagIds));

            dynamic defaultAccountingInfo = await _consequenceAdjustBudget.GetDefaultsForBudgetChangeAsync(60, userID,
                orgId, serviceId, "MontlyReport", budgetYear);

            actions.Add("defaultModel", (JArray)defaultAccountingInfo["gridDatabdtchange"]);

            dynamic activeChangeData = new JArray();
            decimal year1total = 0;
            decimal year2total = 0;
            decimal year3total = 0;
            decimal year4total = 0;
            decimal year5total = 0;

            dynamic ActionDetailsTotalbdtchange = new JObject();
            foreach (var a in result)
            {
                dynamic ActionDetails = new JObject();
                ActionDetails.id = a.pk_id;

                dynamic accountDetails = new JObject();
                accountDetails.accountText = a.accountCode + "-" + a.accountName;
                accountDetails.accountValue = a.accountCode;
                ActionDetails.account = accountDetails;

                dynamic deptDetails = new JObject();
                deptDetails.departmentText = a.deptCode + "-" + a.deptName;
                deptDetails.departmentValue = a.deptCode;
                ActionDetails.department = deptDetails;

                dynamic funcDetails = new JObject();
                funcDetails.functionText = a.functionCode + "-" + a.functionName;
                funcDetails.functionValue = a.functionCode;
                ActionDetails.functionn = funcDetails;

                dynamic projDetails = new JObject();
                projDetails.fk_project_code = a.projectCode;
                projDetails.project_name = a.projectCode + "-" + a.projectName;
                ActionDetails.project = projDetails;

                if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_1") != null)
                {
                    dynamic freeDim1Details = new JObject();
                    freeDim1Details.fk_freedim_code = a.freedimCode1;
                    freeDim1Details.freedim_name = a.freedimCode1 + "-" + a.freedimName1;
                    ActionDetails.freeDim1 = freeDim1Details;
                }
                if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_2") != null)
                {
                    dynamic freeDim2Details = new JObject();
                    freeDim2Details.fk_freedim_code = a.freedimCode2;
                    freeDim2Details.freedim_name = a.freedimCode2 + "-" + a.freedimName2;
                    ActionDetails.freeDim2 = freeDim2Details;
                }
                if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_3") != null)
                {
                    dynamic freeDim3Details = new JObject();
                    freeDim3Details.fk_freedim_code = a.freedimCode3;
                    freeDim3Details.freedim_name = a.freedimCode3 + "-" + a.freedimName3;
                    ActionDetails.freeDim3 = freeDim3Details;
                }
                if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_4") != null)
                {
                    dynamic freeDim4Details = new JObject();
                    freeDim4Details.fk_freedim_code = a.freedimCode4;
                    freeDim4Details.freedim_name = a.freedimCode4 + "-" + a.freedimName4;
                    ActionDetails.freeDim4 = freeDim4Details;
                }

                if (lstAjstmt.Count > 1)
                {
                    ActionDetails.adjustmentCode = GenerateDynamicAdjustmentCodeObject(a.fk_adjustment_code,
                        a.adjustmentCodeName);
                }
                if (lstAlter.Count > 1)
                {
                    ActionDetails.alterCode = GenerateDynamicAlterCodeObject(a.fk_alter_code, a.alterCodeName);
                }

                ActionDetails.year1 = a.year1Ammount.Value / 1000;
                ActionDetails.year2 = a.year2Ammount.Value / 1000;
                ActionDetails.year3 = a.year3Ammount.Value / 1000;
                ActionDetails.year4 = a.year4Ammount.Value / 1000;
                ActionDetails.year5 = a.year5Ammount.Value / 1000;

                year1total = year1total + (a.year1Ammount.Value / 1000);
                year2total = year2total + (a.year2Ammount.Value / 1000);
                year3total = year3total + (a.year3Ammount.Value / 1000);
                year4total = year4total + (a.year4Ammount.Value / 1000);
                year5total = year5total + (a.year5Ammount.Value / 1000);

                ActionDetails.description = a.description == null ? string.Empty : a.description;

                dynamic pdKeyDetails = new JObject();
                pdKeyDetails.key = a.fk_key_id.ToString();
                var pdKeyVal = (await _utility.GetTenantDBContextAsync()).tco_periodic_key.FirstOrDefault(x => x.key_id.ToString() == a.fk_key_id.ToString());
                pdKeyDetails.value = pdKeyVal != null ? pdKeyVal.key_id + "-" + pdKeyVal.key_description : 
                    a.fk_key_id == 0 ? a.fk_key_id.ToString() + "-" + (langStringValuesYBtype["YB_budget_entries_zero_periodic_key_text"]).LangText : "";
                ActionDetails.periodicKey = pdKeyDetails;

                activeChangeData.Add(ActionDetails);
            }

            if ((actionId == 0))
            {
                dynamic defaultObj = (JObject)(defaultAccountingInfo["gridDatabdtchange"])[0];
                dynamic ActionDetailsWithBudgetChange = new JObject();
                ActionDetailsWithBudgetChange.id = 0;
                ActionDetailsWithBudgetChange.guid = Guid.NewGuid();
                ActionDetailsWithBudgetChange.account =
                    GenerateDynamicAccountObject((string)defaultObj["account"]["accountValue"],
                        (string)defaultObj["account"]["accountValue"]);

                ActionDetailsWithBudgetChange.department =
                    GenerateDynamicDepartmentObject((string)defaultObj["department"]["departmentValue"],
                        (string)defaultObj["department"]["departmentValue"]);

                ActionDetailsWithBudgetChange.functionn =
                    GenerateDynamicFunctionObject((string)defaultObj["functionn"]["functionValue"],
                        (string)defaultObj["functionn"]["functionValue"]);

                ActionDetailsWithBudgetChange.project =
                    GenerateDynamicProjectDimObject((string)defaultObj["project"]["fk_project_code"],
                        (string)defaultObj["project"]["fk_project_code"]);

                if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_1") != null)
                {
                    ActionDetailsWithBudgetChange.freeDim1 =
                        GenerateDynamicFreeDimObject((string)defaultObj["freeDim1"]["fk_freedim_code"],
                            (string)defaultObj["freeDim1"]["fk_freedim_code"]);
                }
                if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_2") != null)
                {
                    ActionDetailsWithBudgetChange.freeDim2 =
                        GenerateDynamicFreeDimObject((string)defaultObj["freeDim2"]["fk_freedim_code"],
                            (string)defaultObj["freeDim2"]["fk_freedim_code"]);
                }
                if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_3") != null)
                {
                    ActionDetailsWithBudgetChange.freeDim3 =
                        GenerateDynamicFreeDimObject((string)defaultObj["freeDim3"]["fk_freedim_code"],
                            (string)defaultObj["freeDim3"]["fk_freedim_code"]);
                }
                if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_4") != null)
                {
                    ActionDetailsWithBudgetChange.freeDim4 =
                        GenerateDynamicFreeDimObject((string)defaultObj["freeDim4"]["fk_freedim_code"],
                            (string)defaultObj["freeDim4"]["fk_freedim_code"]);
                }

                if (lstAjstmt.Count > 1)
                {
                    ActionDetailsWithBudgetChange.adjustmentCode =
                        GenerateDynamicAdjustmentCodeObject((string)defaultObj["adjustmentCode"]["key"],
                            (string)defaultObj["adjustmentCode"]["key"]);
                }
                if (lstAlter.Count > 1)
                {
                    dynamic alterDetails = new JObject();
                    alterDetails.key = (string)defaultObj["alterCode"]["key"];
                    alterDetails.value = (string)defaultObj["alterCode"]["value"];
                    ActionDetailsWithBudgetChange.alterCode = alterDetails;
                }

                ActionDetailsWithBudgetChange.year1 = 0;
                ActionDetailsWithBudgetChange.year2 = 0;
                ActionDetailsWithBudgetChange.year3 = 0;
                ActionDetailsWithBudgetChange.year4 = 0;

                ActionDetailsWithBudgetChange.description = string.Empty;

                string defaultKeyValue = await _utility.GetParameterValueAsync(userID, "DEFAULT_BUDGET_PER_KEY");

                dynamic defaultPeriodicKey = new JObject();
                defaultPeriodicKey.key = defaultKeyValue;
                var tcoPeriodicKey = (await _utility.GetTenantDBContextAsync()).tco_periodic_key.FirstOrDefault(x => x.key_id.ToString() == defaultKeyValue);

                if (tcoPeriodicKey != null)
                {
                    defaultPeriodicKey.value = defaultKeyValue + "-" + tcoPeriodicKey.key_description;
                }

                ActionDetailsWithBudgetChange.periodicKey = defaultPeriodicKey;

                activeChangeData.Add(ActionDetailsWithBudgetChange);
            }

            ActionDetailsTotalbdtchange.id = "total";

            dynamic accDetails = new JObject();
            accDetails.accountText = langStringValues.FirstOrDefault(v => v.Key == "cmn_title_total").Value.LangText;
            accDetails.accountValue = "";

            dynamic accDetailsfinaltotal = new JObject();
            accDetailsfinaltotal.accountText =
                langStringValues.FirstOrDefault(v => v.Key == "total_incl_curr_bud_chg").Value.LangText;
            accDetailsfinaltotal.accountValue = "";

            ActionDetailsTotalbdtchange.account = accDetails;
            ActionDetailsTotalbdtchange.department = GenerateDynamicDepartmentObject("", "");
            ActionDetailsTotalbdtchange.functionn = GenerateDynamicFunctionObject("", "");
            ActionDetailsTotalbdtchange.project = GenerateDynamicProjectDimObject("", "");

            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_1") != null)
            {
                ActionDetailsTotalbdtchange.freeDim1 = GenerateDynamicFreeDimObject("", "");
            }
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_2") != null)
            {
                ActionDetailsTotalbdtchange.freeDim2 = GenerateDynamicFreeDimObject("", "");
            }
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_3") != null)
            {
                ActionDetailsTotalbdtchange.freeDim3 = GenerateDynamicFreeDimObject("", "");
            }
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_4") != null)
            {
                ActionDetailsTotalbdtchange.freeDim4 = GenerateDynamicFreeDimObject("", "");
            }

            if (lstAjstmt.Count > 1)
            {
                ActionDetailsTotalbdtchange.adjustmentCode = GenerateDynamicAdjustmentCodeObject("", "");
            }

            if (lstAlter.Count > 1)
            {
                ActionDetailsTotalbdtchange.alterCode = GenerateDynamicAlterCodeObject("", "");
            }

            actions.Add("activeChangeData", activeChangeData);

            ActionDetailsTotalbdtchange.year1 = year1total;
            ActionDetailsTotalbdtchange.year2 = year2total;
            ActionDetailsTotalbdtchange.year3 = year3total;
            ActionDetailsTotalbdtchange.year4 = year4total;
            ActionDetailsTotalbdtchange.year5 = year5total;

            ActionDetailsTotalbdtchange.Add("description", "");

            dynamic totpdKeyDetails = new JObject();
            totpdKeyDetails.key = "";
            totpdKeyDetails.value = "";
            ActionDetailsTotalbdtchange.periodicKey = totpdKeyDetails;

            dynamic totalsData = new JArray();
            totalsData.Add(ActionDetailsTotalbdtchange);

            actions.Add("totalsData", totalsData);

            return actions;
        }

        public List<KeyValueNewData> GetColumnsBudAdjustmentsGrid(string userId, int budgetYear, int orgLevel, string budgetPeriod, string orgVersion)
        {
            var result = GetColumnsBudAdjustmentsGridAsync(userId, budgetYear, orgLevel, budgetPeriod, orgVersion).GetAwaiter().GetResult();
            return result;
        }

        public async Task<List<KeyValueNewData>> GetColumnsBudAdjustmentsGridAsync(string userId, int budgetYear, int orgLevel, string budgetPeriod, string orgVersion)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStr = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "MonthlyReport");
            List<KeyValueNewData> budAdjColumnConfig = new List<KeyValueNewData>();

            List<string> budAdjColumnKeys = new List<string> { "adjCode", "adjDescription", "accountCode", "deptCode", "functionCode", "projectCode", "month1", "month2", "month3", "month4", "month5", "month6", "month7", "month8", "month9", "month10", "month11", "month12", "yearAmountTotal", "transDescription" };
            List<string> budAdjColumnFields = InitBudAdjustmentColumnTitles(langStr).ToObject<List<string>>();

            for (int i = 0; i < budAdjColumnFields.Count; i++)
            {
                KeyValueNewData column = new KeyValueNewData();
                column.Key = budAdjColumnKeys[i];
                column.Value = budAdjColumnFields[i];
                if (i == 0)
                {
                    column.isActive = true;
                }
                else
                {
                    column.isActive = false;
                }
                budAdjColumnConfig.Add(column);
            }
            return budAdjColumnConfig;
        }

        public void SaveBudAdjustmentsDashboardWidgetColumns(string userId, DashboardFinstatusColumnHelper input)
        {
            SaveBudAdjustmentsDashboardWidgetColumnsAsync(userId, input).GetAwaiter().GetResult();
        }

        public async Task SaveBudAdjustmentsDashboardWidgetColumnsAsync(string userId, DashboardFinstatusColumnHelper input)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();

            tco_dashboards dashboard = await dbContext.tco_dashboards.FirstOrDefaultAsync(x => x.Id == input.DashboardId);

            if (dashboard != null)
            {
                List<WidgetConfig> widgetConfigs = JsonConvert.DeserializeObject<List<WidgetConfig>>(dashboard.WidgetConfig);
                if (widgetConfigs == null)
                {
                    //no widget configs exist
                    widgetConfigs = new List<WidgetConfig>();
                }
                WidgetConfig widgetConfig = widgetConfigs.FirstOrDefault(x => x.InstanceId.Equals(input.WidgetInstanceId));

                JObject config = new JObject
                {
                    { "columns", JArray.FromObject(input.ColumnsConfig.Where(x => x.isActive).ToList()) }
                };

                if (widgetConfig != null)
                {
                    //Exists - update config
                    widgetConfig.Config = JsonConvert.SerializeObject(config);
                }
                else
                {
                    //Create new if does not exist
                    widgetConfigs.Add(new WidgetConfig()
                    {
                        InstanceId = input.WidgetInstanceId,
                        Config = JsonConvert.SerializeObject(config)
                    });
                }
                dashboard.WidgetConfig = JsonConvert.SerializeObject(widgetConfigs);
                await dbContext.SaveChangesAsync();
            }
        }

        private Dictionary<string, bool> GetGroupingBasedOnSelectedColumns(List<string> selectedColumnKeys)
        {
            Dictionary<string, bool> result = new Dictionary<string, bool>();
            //result.Add("adjCode", true);
            if (selectedColumnKeys.Any())
            {
                foreach (var column in selectedColumnKeys)
                {
                    switch (column)
                    {
                        case "accountCode":
                        case "deptCode":
                        case "functionCode":
                        case "projectCode":
                        case "adjDescription":
                        case "transDescription":
                            result.Add(column, true);
                            break;

                        default:
                            result.Add(column, false);
                            break;
                    }
                }
            }
            return result;
        }

        public async Task<List<BudAdjustmentDashboardColumnHelper>> GetBudgetAdjustmentDashboardData(string userId, int budgetYear, int orgLevel, string orgId, string orgVersion, List<string> selectedColumnKeys, FilterBudAdjDashboardHelper filterInput)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            List<BudAdjustmentDashboardColumnHelper> finalData = new List<BudAdjustmentDashboardColumnHelper>();
            Dictionary<string, bool> groupingText = GetGroupingBasedOnSelectedColumns(selectedColumnKeys);
            List<string> adjustmentCodes = new List<string>();
            var transDetailAsync = GetTransDetailsAsync(userId, budgetYear, orgLevel, orgId, orgVersion, selectedColumnKeys);
            var tcoAccountsAsync = TcoAcntsWithKostraAcntsAsync(userId, budgetYear);
            var tcofunctionsAsync = TcoFunctionsAsync(userId, budgetYear);
            var tcoDeptsAsync = TcoDepartmentsAsync(userId, budgetYear);
            var tcoProjectsAsync = TcoProjectsAsync(userId, budgetYear);
            var tcoUserAdjCodesAsync = TcoUserAdjCodesAsync(userId, budgetYear);

            await Task.WhenAll(transDetailAsync, tcoAccountsAsync, tcofunctionsAsync, tcoDeptsAsync, tcoProjectsAsync, tcoUserAdjCodesAsync);

            var transDetails = transDetailAsync.Result;
            var tcoAccounts = tcoAccountsAsync.Result;
            var tcoFunctions = tcofunctionsAsync.Result;
            var tcoDepts = tcoDeptsAsync.Result;
            var tcoProjects = tcoProjectsAsync.Result;
            var tcoUserAdjCodes = tcoUserAdjCodesAsync.Result;

            var transData = (from t in transDetails
                             join adj in tcoUserAdjCodes on t.adjCode equals adj.adjCode
                             select new BudAdjustmentDashboardColumnHelper
                             {
                                 adjCode = t.adjCode,
                                 adjDescription = adj.adjDescription,
                                 accountCode = t.accountCode,
                                 deptCode = t.deptCode,
                                 functionCode = t.functionCode,
                                 projectCode = t.projectCode,
                                 periodSum = t.periodSum,
                                 yearAmountTotal = t.yearAmountTotal,
                                 transDescription = t.transDescription,
                                 month1 = t.month1,
                                 month2 = t.month2,
                                 month3 = t.month3,
                                 month4 = t.month4,
                                 month5 = t.month5,
                                 month6 = t.month6,
                                 month7 = t.month7,
                                 month8 = t.month8,
                                 month9 = t.month9,
                                 month10 = t.month10,
                                 month11 = t.month11,
                                 month12 = t.month12,
                                 isSumRow = t.isSumRow
                             }).ToList();

            var data = GetColumnsSpecificData(groupingText, transData, tcoAccounts, tcoFunctions, tcoDepts, tcoProjects, tcoUserAdjCodes, selectedColumnKeys);

            var filteredData = GetFilterBudAdjData(data, filterInput);
            var adjCodes = filteredData.Select(x => x.adjCode).Distinct().ToList();
            finalData.AddRange(filteredData);
            foreach (var adjCode in adjCodes)
            {
                var isRowExist = filteredData.Where(y => y.adjCode == adjCode);
                var adjList = isRowExist.ToList();
                bool isMoreAdjRow = adjList.Count > 1 ? true : false;
                if (isMoreAdjRow || groupingText.ContainsKey("accountCode") || groupingText.ContainsKey("deptCode") || groupingText.ContainsKey("functionCode") || groupingText.ContainsKey("projectCode") || groupingText.ContainsKey("adjDescription") || groupingText.ContainsKey("transDescription"))
                {
                    BudAdjustmentDashboardColumnHelper sumRow = new BudAdjustmentDashboardColumnHelper();
                    sumRow = GetSumRowForAdjustment(adjCode, isRowExist);
                    finalData.Add(sumRow);
                }
            }
            finalData = finalData.OrderBy(x => x.adjCode).Distinct().ToList();
            return finalData;
        }

        private List<BudAdjustmentDashboardColumnHelper> GetColumnsSpecificData(Dictionary<string, bool> groupingText, List<BudAdjustmentDashboardColumnHelper> transData, List<BudAdjustmentDashboardColumnHelper> tcoAccounts, List<BudAdjustmentDashboardColumnHelper> tcoFunctions, List<BudAdjustmentDashboardColumnHelper> tcoDepts, List<BudAdjustmentDashboardColumnHelper> tcoProjects, List<BudAdjustmentDashboardColumnHelper> tcoUserAdjCodes, List<string> selectedColumnKeys)
        {
            if (groupingText.ContainsKey("accountCode"))
            {
                transData = (from t in transData
                             join ac in tcoAccounts on t.accountCode equals ac.accountCode
                             select new BudAdjustmentDashboardColumnHelper
                             {
                                 adjCode = t.adjCode,
                                 adjDescription = t.adjDescription,
                                 accountCode = t.accountCode,
                                 accountName = ac.accountName,
                                 deptCode = t.deptCode,
                                 functionCode = t.functionCode,
                                 projectCode = t.projectCode,
                                 periodSum = t.periodSum,
                                 yearAmountTotal = t.yearAmountTotal,
                                 transDescription = t.transDescription,
                                 month1 = t.month1,
                                 month2 = t.month2,
                                 month3 = t.month3,
                                 month4 = t.month4,
                                 month5 = t.month5,
                                 month6 = t.month6,
                                 month7 = t.month7,
                                 month8 = t.month8,
                                 month9 = t.month9,
                                 month10 = t.month10,
                                 month11 = t.month11,
                                 month12 = t.month12,
                                 isSumRow = t.isSumRow
                             }).OrderBy(x => x.adjCode).Distinct().ToList();
            }
            if (groupingText.ContainsKey("deptCode"))
            {
                transData = (from t in transData
                             join td in tcoDepts on t.deptCode equals td.deptCode
                             select new BudAdjustmentDashboardColumnHelper
                             {
                                 adjCode = t.adjCode,
                                 adjDescription = t.adjDescription,
                                 accountCode = t.accountCode,
                                 accountName = t.accountName,
                                 deptCode = t.deptCode,
                                 deptName = td.deptName,
                                 functionCode = t.functionCode,
                                 projectCode = t.projectCode,
                                 periodSum = t.periodSum,
                                 yearAmountTotal = t.yearAmountTotal,
                                 transDescription = t.transDescription,
                                 month1 = t.month1,
                                 month2 = t.month2,
                                 month3 = t.month3,
                                 month4 = t.month4,
                                 month5 = t.month5,
                                 month6 = t.month6,
                                 month7 = t.month7,
                                 month8 = t.month8,
                                 month9 = t.month9,
                                 month10 = t.month10,
                                 month11 = t.month11,
                                 month12 = t.month12,
                                 isSumRow = t.isSumRow
                             }).OrderBy(x => x.adjCode).Distinct().ToList();
            }
            if (groupingText.ContainsKey("functionCode"))
            {
                transData = (from t in transData
                             join tf in tcoFunctions on t.functionCode equals tf.functionCode
                             select new BudAdjustmentDashboardColumnHelper
                             {
                                 adjCode = t.adjCode,
                                 adjDescription = t.adjDescription,
                                 accountCode = t.accountCode,
                                 accountName = t.accountName,
                                 deptCode = t.deptCode,
                                 deptName = t.deptName,
                                 functionCode = t.functionCode,
                                 functionName = tf.functionName,
                                 projectCode = t.projectCode,
                                 periodSum = t.periodSum,
                                 yearAmountTotal = t.yearAmountTotal,
                                 transDescription = t.transDescription,
                                 month1 = t.month1,
                                 month2 = t.month2,
                                 month3 = t.month3,
                                 month4 = t.month4,
                                 month5 = t.month5,
                                 month6 = t.month6,
                                 month7 = t.month7,
                                 month8 = t.month8,
                                 month9 = t.month9,
                                 month10 = t.month10,
                                 month11 = t.month11,
                                 month12 = t.month12,
                                 isSumRow = t.isSumRow
                             }).OrderBy(x => x.adjCode).Distinct().ToList();
            }
            if (groupingText.ContainsKey("projectCode"))
            {
                transData = (from t in transData
                             join tp in tcoProjects on t.projectCode equals tp.projectCode
                             select new BudAdjustmentDashboardColumnHelper
                             {
                                 adjCode = t.adjCode,
                                 adjDescription = t.adjDescription,
                                 accountCode = t.accountCode,
                                 accountName = t.accountName,
                                 deptCode = t.deptCode,
                                 deptName = t.deptName,
                                 functionCode = t.functionCode,
                                 functionName = t.functionName,
                                 projectCode = t.projectCode,
                                 projectName = tp.projectName,
                                 periodSum = t.periodSum,
                                 yearAmountTotal = t.yearAmountTotal,
                                 transDescription = t.transDescription,
                                 month1 = t.month1,
                                 month2 = t.month2,
                                 month3 = t.month3,
                                 month4 = t.month4,
                                 month5 = t.month5,
                                 month6 = t.month6,
                                 month7 = t.month7,
                                 month8 = t.month8,
                                 month9 = t.month9,
                                 month10 = t.month10,
                                 month11 = t.month11,
                                 month12 = t.month12,
                                 isSumRow = t.isSumRow
                             }).OrderBy(x => x.adjCode).Distinct().ToList();
            }
            return transData;
        }

        private BudAdjustmentDashboardColumnHelper GetSumRowForAdjustment(string adjCode, IEnumerable<BudAdjustmentDashboardColumnHelper> isRowExist)
        {
            BudAdjustmentDashboardColumnHelper sumRow = new BudAdjustmentDashboardColumnHelper();
            List<decimal> monthData = new List<decimal>();
            sumRow.adjCode = adjCode;
            sumRow.accountCode = string.Empty;
            sumRow.deptCode = string.Empty;
            sumRow.functionCode = string.Empty;
            sumRow.projectCode = string.Empty;
            sumRow.adjDescription = string.Empty;
            sumRow.transDescription = string.Empty;
            sumRow.accountName = string.Empty;
            sumRow.deptName = string.Empty;
            sumRow.functionName = string.Empty;
            sumRow.projectName = string.Empty;
            monthData = isRowExist.Select(z => z.month1).ToList();
            sumRow.month1 = monthData.Sum();
            monthData = isRowExist.Select(z => z.month2).ToList();
            sumRow.month2 = monthData.Sum();
            monthData = isRowExist.Select(z => z.month3).ToList();
            sumRow.month3 = monthData.Sum();
            monthData = isRowExist.Select(z => z.month4).ToList();
            sumRow.month4 = monthData.Sum();
            monthData = isRowExist.Select(z => z.month5).ToList();
            sumRow.month5 = monthData.Sum();
            monthData = isRowExist.Select(z => z.month6).ToList();
            sumRow.month6 = monthData.Sum();
            monthData = isRowExist.Select(z => z.month7).ToList();
            sumRow.month7 = monthData.Sum();
            monthData = isRowExist.Select(z => z.month8).ToList();
            sumRow.month8 = monthData.Sum();
            monthData = isRowExist.Select(z => z.month9).ToList();
            sumRow.month9 = monthData.Sum();
            monthData = isRowExist.Select(z => z.month10).ToList();
            sumRow.month10 = monthData.Sum();
            monthData = isRowExist.Select(z => z.month11).ToList();
            sumRow.month11 = monthData.Sum();
            monthData = isRowExist.Select(z => z.month12).ToList();
            sumRow.month12 = monthData.Sum();
            List<decimal> amounts = isRowExist.Select(z => z.yearAmountTotal).ToList();
            sumRow.yearAmountTotal = amounts.Sum();
            sumRow.isSumRow = true;
            return sumRow;
        }

        private List<BudAdjustmentDashboardColumnHelper> GetFilterBudAdjData(List<BudAdjustmentDashboardColumnHelper> data, FilterBudAdjDashboardHelper filterInput)
        {
            List<BudAdjustmentDashboardColumnHelper> result = new List<BudAdjustmentDashboardColumnHelper>();
            if (!string.IsNullOrEmpty(filterInput.adjCode))
            {
                data = data.Where(x => x.adjCode.ToLower().Contains(filterInput.adjCode.Trim().ToLower())).ToList();
            }
            if (!string.IsNullOrEmpty(filterInput.adjDescription))
            {
                data = data.Where(x => x.adjDescription.ToLower().Contains(filterInput.adjDescription.Trim().ToLower())).ToList();
            }
            if (!string.IsNullOrEmpty(filterInput.accountCode))
            {
                data = data.Where(x => x.accountCode.ToLower().Contains(filterInput.accountCode.Trim().ToLower())).ToList();
            }
            if (!string.IsNullOrEmpty(filterInput.deptCode))
            {
                data = data.Where(x => x.deptCode.ToLower().Contains(filterInput.deptCode.Trim().ToLower())).ToList();
            }
            if (!string.IsNullOrEmpty(filterInput.functionCode))
            {
                data = data.Where(x => x.functionCode.ToLower().Contains(filterInput.functionCode.Trim().ToLower())).ToList();
            }
            if (!string.IsNullOrEmpty(filterInput.projectCode))
            {
                data = data.Where(x => x.projectCode.ToLower().Contains(filterInput.projectCode.Trim().ToLower())).ToList();
            }
            if (!string.IsNullOrEmpty(filterInput.transDescription))
            {
                data = data.Where(x => x.transDescription.ToLower().Contains(filterInput.transDescription.Trim().ToLower())).ToList();
            }
            return data;
        }

        private async Task<List<BudAdjustmentDashboardColumnHelper>> GetTransDetailsAsync(string userId, int budgetYear, int orgLevel, string orgId, string orgVersion, List<string> selectedColumnKeys)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            List<BudAdjustmentDashboardColumnHelper> data = new List<BudAdjustmentDashboardColumnHelper>();
            List<BudAdjustmentDashboardColumnHelper> finalData = new List<BudAdjustmentDashboardColumnHelper>();
            Dictionary<string, bool> groupingText = GetGroupingBasedOnSelectedColumns(selectedColumnKeys);
            bool isAcntCode = groupingText.ContainsKey("accountCode") ? groupingText["accountCode"] : false;
            bool isDeptCode = groupingText.ContainsKey("deptCode") ? groupingText["deptCode"] : false;
            bool isFunCode = groupingText.ContainsKey("functionCode") ? groupingText["functionCode"] : false;
            bool isProjCode = groupingText.ContainsKey("projectCode") ? groupingText["projectCode"] : false;
            bool isTransDesc = groupingText.ContainsKey("transDescription") ? groupingText["transDescription"] : false;
            bool isYearAmtTotal = groupingText.ContainsKey("yearAmountTotal") ? groupingText["yearAmountTotal"] : false;
            int temp = budgetYear * 100;
            List<int> months = new List<int> { temp + 01, temp + 02, temp + 03, temp + 04, temp + 05, temp + 06, temp + 07, temp + 08, temp + 09, temp + 10, temp + 11, temp + 12 };

            switch (orgLevel)
            {
                case 1:
                    data = await (from tbu in tenantDbContext.tbu_trans_detail
                                  join org in tenantDbContext.tco_org_hierarchy on new { a = tbu.fk_tenant_id, b = tbu.department_code } equals new { a = org.fk_tenant_id, b = org.fk_department_code }
                                  where tbu.fk_tenant_id == userDetails.tenant_id && tbu.budget_year == budgetYear && org.fk_org_version == orgVersion
                                  group tbu by new
                                  {
                                      adjCode = tbu.fk_adjustment_code,
                                      accountCode = isAcntCode ? tbu.fk_account_code : null,
                                      deptCode = isDeptCode ? tbu.department_code : null,
                                      functionCode = isFunCode ? tbu.fk_function_code : null,
                                      projectCode = isProjCode ? tbu.fk_project_code : null,
                                      transDescription = isTransDesc ? tbu.description : null,
                                      tbu.period
                                  } into res
                                  select new BudAdjustmentDashboardColumnHelper
                                  {
                                      adjCode = res.Key.adjCode,
                                      accountCode = res.Key.accountCode,
                                      deptCode = res.Key.deptCode,
                                      functionCode = res.Key.functionCode,
                                      projectCode = res.Key.projectCode,
                                      transDescription = res.Key.transDescription,
                                      yearAmountTotal = res.Sum(x => x.total_amount),
                                      periodSum = res.Sum(x => x.amount_year_1),
                                      period = res.Key.period,
                                  }).ToListAsync();
                    break;

                case 2:
                    data = await (from tbu in tenantDbContext.tbu_trans_detail
                                  join org in tenantDbContext.tco_org_hierarchy on new { a = tbu.fk_tenant_id, b = tbu.department_code } equals new { a = org.fk_tenant_id, b = org.fk_department_code }
                                  where tbu.fk_tenant_id == userDetails.tenant_id && tbu.budget_year == budgetYear && org.fk_org_version == orgVersion && org.org_id_2 == orgId
                                  group tbu by new
                                  {
                                      adjCode = tbu.fk_adjustment_code,
                                      accountCode = isAcntCode ? tbu.fk_account_code : null,
                                      deptCode = isDeptCode ? tbu.department_code : null,
                                      functionCode = isFunCode ? tbu.fk_function_code : null,
                                      projectCode = isProjCode ? tbu.fk_project_code : null,
                                      transDescription = isTransDesc ? tbu.description : null,
                                      tbu.period
                                  } into res
                                  select new BudAdjustmentDashboardColumnHelper
                                  {
                                      adjCode = res.Key.adjCode,
                                      accountCode = res.Key.accountCode,
                                      deptCode = res.Key.deptCode,
                                      functionCode = res.Key.functionCode,
                                      projectCode = res.Key.projectCode,
                                      transDescription = res.Key.transDescription,
                                      yearAmountTotal = res.Sum(x => x.total_amount),
                                      periodSum = res.Sum(x => x.amount_year_1),
                                      period = res.Key.period,
                                  }).ToListAsync();
                    break;

                case 3:
                    data = await (from tbu in tenantDbContext.tbu_trans_detail
                                  join org in tenantDbContext.tco_org_hierarchy on new { a = tbu.fk_tenant_id, b = tbu.department_code } equals new { a = org.fk_tenant_id, b = org.fk_department_code }
                                  where tbu.fk_tenant_id == userDetails.tenant_id && tbu.budget_year == budgetYear && org.fk_org_version == orgVersion && org.org_id_3 == orgId
                                  group tbu by new
                                  {
                                      adjCode = tbu.fk_adjustment_code,
                                      accountCode = isAcntCode ? tbu.fk_account_code : null,
                                      deptCode = isDeptCode ? tbu.department_code : null,
                                      functionCode = isFunCode ? tbu.fk_function_code : null,
                                      projectCode = isProjCode ? tbu.fk_project_code : null,
                                      transDescription = isTransDesc ? tbu.description : null,
                                      tbu.period
                                  } into res
                                  select new BudAdjustmentDashboardColumnHelper
                                  {
                                      adjCode = res.Key.adjCode,
                                      accountCode = res.Key.accountCode,
                                      deptCode = res.Key.deptCode,
                                      functionCode = res.Key.functionCode,
                                      projectCode = res.Key.projectCode,
                                      transDescription = res.Key.transDescription,
                                      yearAmountTotal = res.Sum(x => x.total_amount),
                                      periodSum = res.Sum(x => x.amount_year_1),
                                      period = res.Key.period,
                                  }).ToListAsync();
                    break;

                case 4:
                    data = await (from tbu in tenantDbContext.tbu_trans_detail
                                  join org in tenantDbContext.tco_org_hierarchy on new { a = tbu.fk_tenant_id, b = tbu.department_code } equals new { a = org.fk_tenant_id, b = org.fk_department_code }
                                  where tbu.fk_tenant_id == userDetails.tenant_id && tbu.budget_year == budgetYear && org.fk_org_version == orgVersion && org.org_id_4 == orgId
                                  group tbu by new
                                  {
                                      adjCode = tbu.fk_adjustment_code,
                                      accountCode = isAcntCode ? tbu.fk_account_code : null,
                                      deptCode = isDeptCode ? tbu.department_code : null,
                                      functionCode = isFunCode ? tbu.fk_function_code : null,
                                      projectCode = isProjCode ? tbu.fk_project_code : null,
                                      transDescription = isTransDesc ? tbu.description : null,
                                      tbu.period
                                  } into res
                                  select new BudAdjustmentDashboardColumnHelper
                                  {
                                      adjCode = res.Key.adjCode,
                                      accountCode = res.Key.accountCode,
                                      deptCode = res.Key.deptCode,
                                      functionCode = res.Key.functionCode,
                                      projectCode = res.Key.projectCode,
                                      transDescription = res.Key.transDescription,
                                      yearAmountTotal = res.Sum(x => x.total_amount),
                                      periodSum = res.Sum(x => x.amount_year_1),
                                      period = res.Key.period,
                                  }).ToListAsync();
                    break;

                case 5:
                    data = await (from tbu in tenantDbContext.tbu_trans_detail
                                  join org in tenantDbContext.tco_org_hierarchy on new { a = tbu.fk_tenant_id, b = tbu.department_code } equals new { a = org.fk_tenant_id, b = org.fk_department_code }
                                  where tbu.fk_tenant_id == userDetails.tenant_id && tbu.budget_year == budgetYear && org.fk_org_version == orgVersion && org.org_id_5 == orgId
                                  group tbu by new
                                  {
                                      adjCode = tbu.fk_adjustment_code,
                                      accountCode = isAcntCode ? tbu.fk_account_code : null,
                                      deptCode = isDeptCode ? tbu.department_code : null,
                                      functionCode = isFunCode ? tbu.fk_function_code : null,
                                      projectCode = isProjCode ? tbu.fk_project_code : null,
                                      transDescription = isTransDesc ? tbu.description : null,
                                      tbu.period
                                  } into res
                                  select new BudAdjustmentDashboardColumnHelper
                                  {
                                      adjCode = res.Key.adjCode,
                                      accountCode = res.Key.accountCode,
                                      deptCode = res.Key.deptCode,
                                      functionCode = res.Key.functionCode,
                                      projectCode = res.Key.projectCode,
                                      transDescription = res.Key.transDescription,
                                      yearAmountTotal = res.Sum(x => x.total_amount),
                                      periodSum = res.Sum(x => x.amount_year_1),
                                      period = res.Key.period,
                                  }).ToListAsync();
                    break;

                case 6:
                    data = await (from tbu in tenantDbContext.tbu_trans_detail
                                  join org in tenantDbContext.tco_org_hierarchy on new { a = tbu.fk_tenant_id, b = tbu.department_code } equals new { a = org.fk_tenant_id, b = org.fk_department_code }
                                  where tbu.fk_tenant_id == userDetails.tenant_id && tbu.budget_year == budgetYear && org.fk_org_version == orgVersion && org.org_id_6 == orgId
                                  group tbu by new
                                  {
                                      adjCode = tbu.fk_adjustment_code,
                                      accountCode = isAcntCode ? tbu.fk_account_code : null,
                                      deptCode = isDeptCode ? tbu.department_code : null,
                                      functionCode = isFunCode ? tbu.fk_function_code : null,
                                      projectCode = isProjCode ? tbu.fk_project_code : null,
                                      transDescription = isTransDesc ? tbu.description : null,
                                      tbu.period
                                  } into res
                                  select new BudAdjustmentDashboardColumnHelper
                                  {
                                      adjCode = res.Key.adjCode,
                                      accountCode = res.Key.accountCode,
                                      deptCode = res.Key.deptCode,
                                      functionCode = res.Key.functionCode,
                                      projectCode = res.Key.projectCode,
                                      transDescription = res.Key.transDescription,
                                      yearAmountTotal = res.Sum(x => x.total_amount),
                                      periodSum = res.Sum(x => x.amount_year_1),
                                      period = res.Key.period,
                                  }).ToListAsync();
                    break;

                case 7:
                    data = await (from tbu in tenantDbContext.tbu_trans_detail
                                  join org in tenantDbContext.tco_org_hierarchy on new { a = tbu.fk_tenant_id, b = tbu.department_code } equals new { a = org.fk_tenant_id, b = org.fk_department_code }
                                  where tbu.fk_tenant_id == userDetails.tenant_id && tbu.budget_year == budgetYear && org.fk_org_version == orgVersion && org.org_id_7 == orgId
                                  group tbu by new
                                  {
                                      adjCode = tbu.fk_adjustment_code,
                                      accountCode = isAcntCode ? tbu.fk_account_code : null,
                                      deptCode = isDeptCode ? tbu.department_code : null,
                                      functionCode = isFunCode ? tbu.fk_function_code : null,
                                      projectCode = isProjCode ? tbu.fk_project_code : null,
                                      transDescription = isTransDesc ? tbu.description : null,
                                      tbu.period
                                  } into res
                                  select new BudAdjustmentDashboardColumnHelper
                                  {
                                      adjCode = res.Key.adjCode,
                                      accountCode = res.Key.accountCode,
                                      deptCode = res.Key.deptCode,
                                      functionCode = res.Key.functionCode,
                                      projectCode = res.Key.projectCode,
                                      transDescription = res.Key.transDescription,
                                      yearAmountTotal = res.Sum(x => x.total_amount),
                                      periodSum = res.Sum(x => x.amount_year_1),
                                      period = res.Key.period,
                                  }).ToListAsync();
                    break;

                case 8:
                    data = await (from tbu in tenantDbContext.tbu_trans_detail
                                  join org in tenantDbContext.tco_org_hierarchy on new { a = tbu.fk_tenant_id, b = tbu.department_code } equals new { a = org.fk_tenant_id, b = org.fk_department_code }
                                  where tbu.fk_tenant_id == userDetails.tenant_id && tbu.budget_year == budgetYear && org.fk_org_version == orgVersion && org.org_id_8 == orgId
                                  group tbu by new
                                  {
                                      adjCode = tbu.fk_adjustment_code,
                                      accountCode = isAcntCode ? tbu.fk_account_code : null,
                                      deptCode = isDeptCode ? tbu.department_code : null,
                                      functionCode = isFunCode ? tbu.fk_function_code : null,
                                      projectCode = isProjCode ? tbu.fk_project_code : null,
                                      transDescription = isTransDesc ? tbu.description : null,
                                      tbu.period
                                  } into res
                                  select new BudAdjustmentDashboardColumnHelper
                                  {
                                      adjCode = res.Key.adjCode,
                                      accountCode = res.Key.accountCode,
                                      deptCode = res.Key.deptCode,
                                      functionCode = res.Key.functionCode,
                                      projectCode = res.Key.projectCode,
                                      transDescription = res.Key.transDescription,
                                      yearAmountTotal = res.Sum(x => x.total_amount),
                                      periodSum = res.Sum(x => x.amount_year_1),
                                      period = res.Key.period
                                  }).ToListAsync();
                    break;
            }
            var noPeriodData = (from a in data
                                group a by new { a.adjCode, a.accountCode, a.deptCode, a.functionCode, a.projectCode, a.transDescription } into p
                                select new BudAdjustmentDashboardColumnHelper
                                {
                                    adjCode = p.Key.adjCode,
                                    accountCode = p.Key.accountCode,
                                    deptCode = p.Key.deptCode,
                                    functionCode = p.Key.functionCode,
                                    projectCode = p.Key.projectCode,
                                    transDescription = p.Key.transDescription,
                                    yearAmountTotal = p.Sum(x => x.yearAmountTotal)
                                }).ToList();

            var month1Data = (from a in data
                              join b in months on a.period equals b
                              where b == months[0]
                              group a by new { a.adjCode, a.accountCode, a.deptCode, a.functionCode, a.projectCode, a.transDescription } into p
                              select new BudAdjustmentDashboardColumnHelper
                              {
                                  adjCode = p.Key.adjCode,
                                  accountCode = p.Key.accountCode,
                                  deptCode = p.Key.deptCode,
                                  functionCode = p.Key.functionCode,
                                  projectCode = p.Key.projectCode,
                                  transDescription = p.Key.transDescription,
                                  month1 = p.Sum(x => x.periodSum),
                              }).ToList();
            var month2Data = (from a in data
                              join b in months on a.period equals b
                              where b == months[1]
                              group a by new { a.adjCode, a.accountCode, a.deptCode, a.functionCode, a.projectCode, a.transDescription } into p
                              select new BudAdjustmentDashboardColumnHelper
                              {
                                  adjCode = p.Key.adjCode,
                                  accountCode = p.Key.accountCode,
                                  deptCode = p.Key.deptCode,
                                  functionCode = p.Key.functionCode,
                                  projectCode = p.Key.projectCode,
                                  transDescription = p.Key.transDescription,
                                  month2 = p.Sum(x => x.periodSum),
                              }).ToList();
            var month3Data = (from a in data
                              join b in months on a.period equals b
                              where b == months[2]
                              group a by new { a.adjCode, a.accountCode, a.deptCode, a.functionCode, a.projectCode, a.transDescription } into p
                              select new BudAdjustmentDashboardColumnHelper
                              {
                                  adjCode = p.Key.adjCode,
                                  accountCode = p.Key.accountCode,
                                  deptCode = p.Key.deptCode,
                                  functionCode = p.Key.functionCode,
                                  projectCode = p.Key.projectCode,
                                  transDescription = p.Key.transDescription,
                                  month3 = p.Sum(x => x.periodSum),
                              }).ToList();
            var month4Data = (from a in data
                              join b in months on a.period equals b
                              where b == months[3]
                              group a by new { a.adjCode, a.accountCode, a.deptCode, a.functionCode, a.projectCode, a.transDescription } into p
                              select new BudAdjustmentDashboardColumnHelper
                              {
                                  adjCode = p.Key.adjCode,
                                  accountCode = p.Key.accountCode,
                                  deptCode = p.Key.deptCode,
                                  functionCode = p.Key.functionCode,
                                  projectCode = p.Key.projectCode,
                                  transDescription = p.Key.transDescription,
                                  month4 = p.Sum(x => x.periodSum),
                              }).ToList();
            var month5Data = (from a in data
                              join b in months on a.period equals b
                              where b == months[4]
                              group a by new { a.adjCode, a.accountCode, a.deptCode, a.functionCode, a.projectCode, a.transDescription } into p
                              select new BudAdjustmentDashboardColumnHelper
                              {
                                  adjCode = p.Key.adjCode,
                                  accountCode = p.Key.accountCode,
                                  deptCode = p.Key.deptCode,
                                  functionCode = p.Key.functionCode,
                                  projectCode = p.Key.projectCode,
                                  transDescription = p.Key.transDescription,
                                  month5 = p.Sum(x => x.periodSum),
                              }).ToList();
            var month6Data = (from a in data
                              join b in months on a.period equals b
                              where b == months[5]
                              group a by new { a.adjCode, a.accountCode, a.deptCode, a.functionCode, a.projectCode, a.transDescription } into p
                              select new BudAdjustmentDashboardColumnHelper
                              {
                                  adjCode = p.Key.adjCode,
                                  accountCode = p.Key.accountCode,
                                  deptCode = p.Key.deptCode,
                                  functionCode = p.Key.functionCode,
                                  projectCode = p.Key.projectCode,
                                  transDescription = p.Key.transDescription,
                                  month6 = p.Sum(x => x.periodSum),
                              }).ToList();
            var month7Data = (from a in data
                              join b in months on a.period equals b
                              where b == months[6]
                              group a by new { a.adjCode, a.accountCode, a.deptCode, a.functionCode, a.projectCode, a.transDescription } into p
                              select new BudAdjustmentDashboardColumnHelper
                              {
                                  adjCode = p.Key.adjCode,
                                  accountCode = p.Key.accountCode,
                                  deptCode = p.Key.deptCode,
                                  functionCode = p.Key.functionCode,
                                  projectCode = p.Key.projectCode,
                                  transDescription = p.Key.transDescription,
                                  month7 = p.Sum(x => x.periodSum),
                              }).ToList();
            var month8Data = (from a in data
                              join b in months on a.period equals b
                              where b == months[7]
                              group a by new { a.adjCode, a.accountCode, a.deptCode, a.functionCode, a.projectCode, a.transDescription } into p
                              select new BudAdjustmentDashboardColumnHelper
                              {
                                  adjCode = p.Key.adjCode,
                                  accountCode = p.Key.accountCode,
                                  deptCode = p.Key.deptCode,
                                  functionCode = p.Key.functionCode,
                                  projectCode = p.Key.projectCode,
                                  transDescription = p.Key.transDescription,
                                  month8 = p.Sum(x => x.periodSum),
                              }).ToList();
            var month9Data = (from a in data
                              join b in months on a.period equals b
                              where b == months[8]
                              group a by new { a.adjCode, a.accountCode, a.deptCode, a.functionCode, a.projectCode, a.transDescription } into p
                              select new BudAdjustmentDashboardColumnHelper
                              {
                                  adjCode = p.Key.adjCode,
                                  accountCode = p.Key.accountCode,
                                  deptCode = p.Key.deptCode,
                                  functionCode = p.Key.functionCode,
                                  projectCode = p.Key.projectCode,
                                  transDescription = p.Key.transDescription,
                                  month9 = p.Sum(x => x.periodSum),
                              }).ToList();
            var month10Data = (from a in data
                               join b in months on a.period equals b
                               where b == months[9]
                               group a by new { a.adjCode, a.accountCode, a.deptCode, a.functionCode, a.projectCode, a.transDescription } into p
                               select new BudAdjustmentDashboardColumnHelper
                               {
                                   adjCode = p.Key.adjCode,
                                   accountCode = p.Key.accountCode,
                                   deptCode = p.Key.deptCode,
                                   functionCode = p.Key.functionCode,
                                   projectCode = p.Key.projectCode,
                                   transDescription = p.Key.transDescription,
                                   month10 = p.Sum(x => x.periodSum),
                               }).ToList();
            var month11Data = (from a in data
                               join b in months on a.period equals b
                               where b == months[10]
                               group a by new { a.adjCode, a.accountCode, a.deptCode, a.functionCode, a.projectCode, a.transDescription } into p
                               select new BudAdjustmentDashboardColumnHelper
                               {
                                   adjCode = p.Key.adjCode,
                                   accountCode = p.Key.accountCode,
                                   deptCode = p.Key.deptCode,
                                   functionCode = p.Key.functionCode,
                                   projectCode = p.Key.projectCode,
                                   transDescription = p.Key.transDescription,
                                   month11 = p.Sum(x => x.periodSum),
                               }).ToList();
            var month12Data = (from a in data
                               join b in months on a.period equals b
                               where b == months[11]
                               group a by new { a.adjCode, a.accountCode, a.deptCode, a.functionCode, a.projectCode, a.transDescription } into p
                               select new BudAdjustmentDashboardColumnHelper
                               {
                                   adjCode = p.Key.adjCode,
                                   accountCode = p.Key.accountCode,
                                   deptCode = p.Key.deptCode,
                                   functionCode = p.Key.functionCode,
                                   projectCode = p.Key.projectCode,
                                   transDescription = p.Key.transDescription,
                                   month12 = p.Sum(x => x.periodSum),
                               }).ToList();

            var periodData = (from a in noPeriodData
                              join m in month1Data on new { a = a.adjCode, b = a.accountCode, c = a.deptCode, d = a.functionCode, e = a.projectCode, f = a.transDescription } equals new { a = m.adjCode, b = m.accountCode, c = m.deptCode, d = m.functionCode, e = m.projectCode, f = m.transDescription }
                              join b in month2Data on new { a = a.adjCode, b = a.accountCode, c = a.deptCode, d = a.functionCode, e = a.projectCode, f = a.transDescription } equals new { a = b.adjCode, b = b.accountCode, c = b.deptCode, d = b.functionCode, e = b.projectCode, f = b.transDescription }
                              join c in month3Data on new { a = a.adjCode, b = a.accountCode, c = a.deptCode, d = a.functionCode, e = a.projectCode, f = a.transDescription } equals new { a = c.adjCode, b = c.accountCode, c = c.deptCode, d = c.functionCode, e = c.projectCode, f = c.transDescription }
                              join d in month4Data on new { a = a.adjCode, b = a.accountCode, c = a.deptCode, d = a.functionCode, e = a.projectCode, f = a.transDescription } equals new { a = d.adjCode, b = d.accountCode, c = d.deptCode, d = d.functionCode, e = d.projectCode, f = d.transDescription }
                              join e in month5Data on new { a = a.adjCode, b = a.accountCode, c = a.deptCode, d = a.functionCode, e = a.projectCode, f = a.transDescription } equals new { a = e.adjCode, b = e.accountCode, c = e.deptCode, d = e.functionCode, e = e.projectCode, f = e.transDescription }
                              join f in month6Data on new { a = a.adjCode, b = a.accountCode, c = a.deptCode, d = a.functionCode, e = a.projectCode, f = a.transDescription } equals new { a = f.adjCode, b = f.accountCode, c = f.deptCode, d = f.functionCode, e = f.projectCode, f = f.transDescription }
                              join g in month7Data on new { a = a.adjCode, b = a.accountCode, c = a.deptCode, d = a.functionCode, e = a.projectCode, f = a.transDescription } equals new { a = g.adjCode, b = g.accountCode, c = g.deptCode, d = g.functionCode, e = g.projectCode, f = g.transDescription }
                              join h in month8Data on new { a = a.adjCode, b = a.accountCode, c = a.deptCode, d = a.functionCode, e = a.projectCode, f = a.transDescription } equals new { a = h.adjCode, b = h.accountCode, c = h.deptCode, d = h.functionCode, e = h.projectCode, f = h.transDescription }
                              join i in month9Data on new { a = a.adjCode, b = a.accountCode, c = a.deptCode, d = a.functionCode, e = a.projectCode, f = a.transDescription } equals new { a = i.adjCode, b = i.accountCode, c = i.deptCode, d = i.functionCode, e = i.projectCode, f = i.transDescription }
                              join j in month10Data on new { a = a.adjCode, b = a.accountCode, c = a.deptCode, d = a.functionCode, e = a.projectCode, f = a.transDescription } equals new { a = j.adjCode, b = j.accountCode, c = j.deptCode, d = j.functionCode, e = j.projectCode, f = j.transDescription }
                              join k in month11Data on new { a = a.adjCode, b = a.accountCode, c = a.deptCode, d = a.functionCode, e = a.projectCode, f = a.transDescription } equals new { a = k.adjCode, b = k.accountCode, c = k.deptCode, d = k.functionCode, e = k.projectCode, f = k.transDescription }
                              join l in month12Data on new { a = a.adjCode, b = a.accountCode, c = a.deptCode, d = a.functionCode, e = a.projectCode, f = a.transDescription } equals new { a = l.adjCode, b = l.accountCode, c = l.deptCode, d = l.functionCode, e = l.projectCode, f = l.transDescription }
                              select new BudAdjustmentDashboardColumnHelper
                              {
                                  adjCode = a.adjCode,
                                  accountCode = a.accountCode != null ? a.accountCode : string.Empty,
                                  deptCode = a.deptCode != null ? a.deptCode : string.Empty,
                                  functionCode = a.functionCode != null ? a.functionCode : string.Empty,
                                  projectCode = a.projectCode != null ? a.projectCode : string.Empty,
                                  transDescription = a.transDescription != null ? a.transDescription : string.Empty,
                                  month1 = m.month1,
                                  month2 = b.month2,
                                  month3 = c.month3,
                                  month4 = d.month4,
                                  month5 = e.month5,
                                  month6 = f.month6,
                                  month7 = g.month7,
                                  month8 = h.month8,
                                  month9 = i.month9,
                                  month10 = j.month10,
                                  month11 = k.month11,
                                  month12 = l.month12,
                                  yearAmountTotal = m.month1 + b.month2 + c.month3 + d.month4 + e.month5 + f.month6 + g.month7 + h.month8 + i.month9 + j.month10 + k.month11 + l.month12,
                                  isSumRow = false
                              }).ToList();
            finalData = periodData.OrderBy(x => x.adjCode).Distinct().ToList();
            return finalData;
        }

        private async Task<List<BudAdjustmentDashboardColumnHelper>> TcoAcntsWithKostraAcntsAsync(string userId, int budgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext tenantDBContext = await _utility.GetTenantDbContextForParallelReadAsync();
            tenantDBContext.Database.SetCommandTimeout(1000);

            var data = await (from ta in tenantDBContext.tco_accounts
                              join gkc in tenantDBContext.gco_kostra_accounts on ta.fk_kostra_account_code equals gkc.pk_kostra_account_code
                              where ta.pk_tenant_id == userDetails.tenant_id && gkc.type.ToLower() == "operations"
                              && ta.dateFrom.Year <= budgetYear && ta.dateTo.Year >= budgetYear
                              select new BudAdjustmentDashboardColumnHelper
                              {
                                  accountCode = ta.pk_account_code,
                                  accountName = ta.display_name
                              }).ToListAsync();

            return data;
        }

        private async Task<List<BudAdjustmentDashboardColumnHelper>> TcoFunctionsAsync(string userId, int budgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext tenantDBContext = await _utility.GetTenantDbContextForParallelReadAsync();
            tenantDBContext.Database.SetCommandTimeout(1000);

            var data = await (from tf in tenantDBContext.tco_functions
                              where tf.pk_tenant_id == userDetails.tenant_id
                              && tf.dateFrom.Year <= budgetYear && tf.dateTo.Year >= budgetYear
                              select new BudAdjustmentDashboardColumnHelper
                              {
                                  functionCode = tf.pk_Function_code,
                                  functionName = tf.display_name,
                              }).ToListAsync();

            return data;
        }

        private async Task<List<BudAdjustmentDashboardColumnHelper>> TcoDepartmentsAsync(string userId, int budgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext tenantDBContext = await _utility.GetTenantDbContextForParallelReadAsync();
            tenantDBContext.Database.SetCommandTimeout(1000);

            var data = await (from td in tenantDBContext.tco_departments
                              where td.fk_tenant_id == userDetails.tenant_id
                              && td.year_from <= budgetYear && td.year_to >= budgetYear
                              select new BudAdjustmentDashboardColumnHelper
                              {
                                  deptCode = td.pk_department_code,
                                  deptName = td.department_name,
                              }).ToListAsync();

            return data;
        }

        private async Task<List<BudAdjustmentDashboardColumnHelper>> TcoProjectsAsync(string userId, int budgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext tenantDBContext = await _utility.GetTenantDbContextForParallelReadAsync();
            tenantDBContext.Database.SetCommandTimeout(1000);

            var data = await (from tp in tenantDBContext.tco_projects
                              where tp.fk_tenant_id == userDetails.tenant_id
                              && tp.date_from.Year <= budgetYear
                              && tp.date_to.Year >= budgetYear
                              select new BudAdjustmentDashboardColumnHelper
                              {
                                  projectCode = tp.pk_project_code,
                                  projectName = tp.project_name
                              }).ToListAsync();
            BudAdjustmentDashboardColumnHelper emptyProject = new BudAdjustmentDashboardColumnHelper() { projectCode = string.Empty, projectName = string.Empty };
            data.Add(emptyProject);
            return data;
        }

        private async Task<List<BudAdjustmentDashboardColumnHelper>> TcoUserAdjCodesAsync(string userId, int budgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext tenantDBContext = await _utility.GetTenantDbContextForParallelReadAsync();
            tenantDBContext.Database.SetCommandTimeout(1000);

            var data = await (from adj in tenantDBContext.tco_user_adjustment_codes
                              where adj.fk_tenant_id == userDetails.tenant_id
                              && adj.budget_year == budgetYear
                              && adj.status && !adj.is_original_flag
                              select new BudAdjustmentDashboardColumnHelper
                              {
                                  adjCode = adj.pk_adj_code,
                                  adjDescription = adj.description
                              }).ToListAsync();

            return data;
        }

        public List<KeyValueNewData> GetBudAdjWidgetColumnConfig(string userId, int dashboardId, Guid widgetInstanceId)
        {
            var result = GetBudAdjWidgetColumnConfigAsync(userId, dashboardId, widgetInstanceId).GetAwaiter()
                .GetResult();
            return result;
        }

        public async Task<List<KeyValueNewData>> GetBudAdjWidgetColumnConfigAsync(string userId, int dashboardId, Guid widgetInstanceId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            List<KeyValueNewData> columns = new List<KeyValueNewData>();
            TargeTableWidgetConfigHelper data = new TargeTableWidgetConfigHelper();

            tco_dashboards dashboard = await dbContext.tco_dashboards.FirstOrDefaultAsync(x => x.Id == dashboardId);
            if (dashboard != null)
            {
                IEnumerable<WidgetConfig> widgetConfigs = JsonConvert.DeserializeObject<IEnumerable<WidgetConfig>>(dashboard.WidgetConfig);
                if (widgetConfigs != null)
                {
                    WidgetConfig widgetConfig = widgetConfigs.FirstOrDefault(x => x.InstanceId.Equals(widgetInstanceId));

                    if (widgetConfig != null)
                    {
                        JObject configJson = JObject.Parse(widgetConfig.Config);

                        columns = configJson.Value<JArray>("columns").ToObject<List<KeyValueNewData>>();
                    }
                }
            }
            return columns;
        }

        private JArray InitBudAdjustmentColumnTitles(Dictionary<string, clsLanguageString> langStr)
        {
            return new JArray() {
                    ((langStr.FirstOrDefault(v => v.Key == "BA_Grid_adjCode")).Value).LangText,                               //*1
                    ((langStr.FirstOrDefault(v => v.Key == "BA_Grid_adjDescription")).Value).LangText,                        //*2
                    ((langStr.FirstOrDefault(v => v.Key == "BA_Grid_accountCode")).Value).LangText,                           //*3
                    ((langStr.FirstOrDefault(v => v.Key == "BA_Grid_deptCode")).Value).LangText,                              //*4
                    ((langStr.FirstOrDefault(v => v.Key == "BA_Grid_functionCode")).Value).LangText,                          //*5
                    ((langStr.FirstOrDefault(v => v.Key == "BA_Grid_projectCode")).Value).LangText,                           //*6
                    ((langStr.FirstOrDefault(v => v.Key == "BA_Grid_month1")).Value).LangText,                                //*8
                    ((langStr.FirstOrDefault(v => v.Key == "BA_Grid_month2")).Value).LangText,                                //*9
                    ((langStr.FirstOrDefault(v => v.Key == "BA_Grid_month3")).Value).LangText,                                //*10
                    ((langStr.FirstOrDefault(v => v.Key == "BA_Grid_month4")).Value).LangText,                                //*11
                    ((langStr.FirstOrDefault(v => v.Key == "BA_Grid_month5")).Value).LangText,                                //*12
                    ((langStr.FirstOrDefault(v => v.Key == "BA_Grid_month6")).Value).LangText,                                //*13
                    ((langStr.FirstOrDefault(v => v.Key == "BA_Grid_month7")).Value).LangText,                                //*14
                    ((langStr.FirstOrDefault(v => v.Key == "BA_Grid_month8")).Value).LangText,                                //*15
                    ((langStr.FirstOrDefault(v => v.Key == "BA_Grid_month9")).Value).LangText,                                //*16
                    ((langStr.FirstOrDefault(v => v.Key == "BA_Grid_month10")).Value).LangText,                               //*17
                    ((langStr.FirstOrDefault(v => v.Key == "BA_Grid_month11")).Value).LangText,                               //*18
                    ((langStr.FirstOrDefault(v => v.Key == "BA_Grid_month12")).Value).LangText,                               //*19
                    ((langStr.FirstOrDefault(v => v.Key == "BA_Grid_yearAmountTotal")).Value).LangText,                       //*20
                    ((langStr.FirstOrDefault(v => v.Key == "BA_Grid_transDescription")).Value).LangText,                      //*21
            };
        }

        private JObject GenerateColumnObject(string title, string field, int colCount, bool encoded, bool hidden, string format, string style, string headerAttributesStyle)
        {
            dynamic obj = new JObject();

            obj.title = title;
            obj.field = field;
            obj.colCount = colCount;
            obj.encoded = encoded;
            obj.hidden = hidden;

            dynamic attributes = new JObject();
            attributes.style = style;
            obj.attributes = attributes;

            dynamic headerAttributes = new JObject();
            headerAttributes.style = headerAttributesStyle;
            obj.headerAttributes = headerAttributes;

            obj.format = format;

            return obj;
        }

        private JObject GenerateActionDetailColumnObject(string title, string field, bool encoded, bool hidden, JObject style, JObject headerAttributesStyle, string format, string template, string headerTemplate, bool isActiveChangeColumn = false)
        {
            dynamic obj = new JObject();

            obj.title = title;
            obj.field = field;
            obj.encoded = encoded;
            obj.hidden = hidden;


            obj.attributes = style;


            obj.headerAttributes = headerAttributesStyle;

            if (isActiveChangeColumn)
            {
                obj.headerTemplate = headerTemplate;
            }

            obj.format = format;
            obj.template = template;

            return obj;
        }


        private JObject GenerateColumnObject(string title, string field, bool encoded, bool hidden, string style, string headerAttributesStyle, string format, string template, string headerTemplate, bool isActiveChangeColumn = false)
        {
            dynamic obj = new JObject();

            obj.title = title;
            obj.field = field;
            obj.encoded = encoded;
            obj.hidden = hidden;

            dynamic attributes = new JObject();
            attributes.style = style;
            obj.attributes = attributes;

            dynamic headerAttributes = new JObject();
            headerAttributes.style = headerAttributesStyle;
            obj.headerAttributes = headerAttributes;

            if (isActiveChangeColumn)
            {
                obj.headerTemplate = headerTemplate;
            }

            obj.format = format;
            obj.template = template;

            return obj;
        }

        private dynamic GenerateDynamicAdjustmentCodeObject(string adjustmentCode, string adjustmentValue)
        {
            dynamic adjustmentDetails = new JObject();
            if (adjustmentCode == "" && adjustmentValue == "")
            {
                adjustmentDetails.value = "";
            }
            else
            {
                adjustmentDetails.value = adjustmentCode + "-" + adjustmentValue;
            }

            if (adjustmentCode == "0")
            {
                adjustmentDetails.key = "";
            }
            else
            {
                adjustmentDetails.key = adjustmentCode;
            }
            return adjustmentDetails;
        }

        private dynamic GenerateDynamicAlterCodeObject(string alterCode, string alterValue)
        {
            dynamic alterDetails = new JObject();
            if (alterCode == "" && alterValue == "")
            {
                alterDetails.value = "";
            }
            else
            {
                alterDetails.value = alterCode + "-" + alterValue;
            }

            if (alterCode == "0")
            {
                alterDetails.key = "";
            }
            else
            {
                alterDetails.key = alterCode;
            }
            return alterDetails;
        }

        private dynamic GenerateDynamicDepartmentObject(string deptCode, string deptValue)
        {
            dynamic deptDetails = new JObject();
            if (deptCode == "" && deptValue == "")
            {
                deptDetails.departmentText = "";
            }
            else
            {
                deptDetails.departmentText = deptCode + "-" + deptValue;
            }

            if (deptCode == "0")
            {
                deptDetails.departmentValue = "";
            }
            else
            {
                deptDetails.departmentValue = deptCode;
            }
            return deptDetails;
        }

        private dynamic GenerateDynamicFunctionObject(string funcCode, string funcValue)
        {
            dynamic funcDetails = new JObject();
            if (funcCode == "" && funcValue == "")
            {
                funcDetails.functionText = "";
            }
            else
            {
                funcDetails.functionText = funcCode + "-" + funcValue;
            }

            if (funcCode == "0")
            {
                funcDetails.functionValue = "";
            }
            else
            {
                funcDetails.functionValue = funcCode;
            }
            return funcDetails;
        }

        private dynamic GenerateDynamicProjectDimObject(string projCode, string projValue)
        {
            dynamic projDetails = new JObject();
            if (projCode == "" && projValue == "")
            {
                projDetails.project_name = "";
            }
            else
            {
                projDetails.project_name = projCode + "-" + projValue;
            }
            projDetails.fk_project_code = projCode;
            return projDetails;
        }

        private dynamic GenerateDynamicFreeDimObject(string freeDimCode, string freeDimValue)
        {
            dynamic freeDimDetails = new JObject();
            if (freeDimCode == "" && freeDimValue == "")
            {
                freeDimDetails.freedim_name = "";
            }
            else
            {
                freeDimDetails.freedim_name = freeDimCode + "-" + freeDimValue;
            }
            freeDimDetails.fk_freedim_code = freeDimCode;
            return freeDimDetails;
        }

        private dynamic GenerateDynamicAccountObject(string accCode, string accValue)
        {
            dynamic accDetails = new JObject();
            if (accCode == "" && accValue == "")
            {
                accDetails.accountText = "";
            }
            else
            {
                accDetails.accountText = accCode + "-" + accValue;
            }

            if (accCode == "0")
            {
                accDetails.accountValue = "";
            }
            else
            {
                accDetails.accountValue = accCode;
            }
            return accDetails;
        }

        private List<GridColumnHelper> GetProposedBudgetAdjustmentsActionLogGridColumns(Dictionary<string, clsLanguageString> langStringValuesBudgetProposal)
        {
            List<GridColumnHelper> logColumns = new List<GridColumnHelper>();
            ColumnStyleHelper csh;
            ColumnStyleHelper csas;
            cell cell = new cell();
            filterable filterable = new filterable();
            cell.enabled = false;
            filterable.cell = cell;
            csh = new ColumnStyleHelper();
            csh.style = "width:160px;text-align: left;border-left: 0px none;border-right: 0px none;";
            csas = new ColumnStyleHelper();
            csas.style = "text-align: left;border-left: 0px none;border-right: 0px none;";
            GridColumnHelper columnInfo = new GridColumnHelper
            {
                field = "date",
                title =
                    langStringValuesBudgetProposal.FirstOrDefault(v => v.Key == "BP_ActionLog_GridColumn_Date").Value.LangText,
                colCount = 0,
                encoded = false,
                attributes = csas,
                headerAttributes = csh,
                filterable = filterable,
                format = "{0: dd.MM.yyyy HH:mm:ss}",
                width = 160
            };
            logColumns.Add(columnInfo);
            cell = new cell();
            filterable = new filterable();
            cell.enabled = false;
            filterable.cell = cell;
            csh = new ColumnStyleHelper();
            csh.style = "width:160px;text-align: left;border-left: 0px none;border-right: 0px none;";
            csas = new ColumnStyleHelper();
            csas.style = "text-align: left;border-left: 0px none;border-right: 0px none;";
            columnInfo = new GridColumnHelper
            {
                field = "actionStatus",
                title =
                    langStringValuesBudgetProposal.FirstOrDefault(v => v.Key == "BP_ActionLog_GridColumn_actionStatus").Value.LangText,
                colCount = 0,
                encoded = false,
                attributes = csas,
                headerAttributes = csh,
                filterable = filterable,
                width = 160
            };
            logColumns.Add(columnInfo);
            cell = new cell();
            filterable = new filterable();
            cell.enabled = false;
            filterable.cell = cell;
            csh = new ColumnStyleHelper();
            csh.style = "width:160px;text-align: left;border-left: 0px none;border-right: 0px none;";
            csas = new ColumnStyleHelper();
            csas.style = "text-align: left;border-left: 0px none;border-right: 0px none;";
            columnInfo = new GridColumnHelper
            {
                field = "statusComment",
                title =
                    langStringValuesBudgetProposal.FirstOrDefault(v => v.Key == "BP_ActionLog_GridColumn_statusComment").Value.LangText,
                colCount = 0,
                encoded = false,
                attributes = csas,
                headerAttributes = csh,
                filterable = filterable,
                width = 160
            };
            logColumns.Add(columnInfo);
            cell = new cell();
            filterable = new filterable();
            cell.enabled = false;
            filterable.cell = cell;
            csh = new ColumnStyleHelper();
            csh.style = "width:160px;text-align: left;border-left: 0px none;border-right: 0px none;";
            csas = new ColumnStyleHelper();
            csas.style = "text-align: left;border-left: 0px none;border-right: 0px none;";
            columnInfo = new GridColumnHelper
            {
                field = "updatedBy",
                title =
                    langStringValuesBudgetProposal.FirstOrDefault(v => v.Key == "BP_ActionLog_GridColumn_updatedBy").Value.LangText,
                colCount = 0,
                encoded = false,
                attributes = csas,
                headerAttributes = csh,
                filterable = filterable,
                width = 160
            };
            logColumns.Add(columnInfo);

            return logColumns;
        }

        public JObject GetProposedBudgetAdjustmentsDoc(string userName,
                                                int budgetYear,
                                                string budMonthLong,
                                                int budPeriod,
                                                string selectedOrgName,
                                                int selectedOrgLevel,
                                                string selectedOrgId,
                                                string selectedServiceId,
                                                OrgInpLevels orgInput,
                                                int forecastPeriod,
                                                bool isBudgetChangesRequest,
                                                bool isTypeInsert,
                                                bool isDoc,
                                                bool isMROverViewReq,
                                                bool isMonthlyReport,
                                                budgetAdjustmentFilterInput filterInput,
                                                List<string> selectedCol,string nodeType)
        {
            try
            {
                UserData userDetails = _utility.GetUserDetails(userName);
                TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
                var orgVersionContent = _utility.GetOrgVersionSpecificContent(userName, budPeriod);
                bool isEditable = true;
                string tempNaParam = string.Empty;
                bool reportSubmitStatus = _mrfUtility.GetMonthlyReportStatus(userName, selectedOrgId, selectedServiceId, budPeriod.ToString(), selectedOrgLevel, out tempNaParam, true);
                bool bAjTabSubmitStatus = false;

                // if report is set then only role_id 2 and 12 can edit.
                if (reportSubmitStatus || bAjTabSubmitStatus)
                {
                    isEditable = _mrfUtility.GetMonthlyReportUserRoleEditAccess(userDetails.pk_id, userDetails.tenant_id);
                }
                else
                {
                    List<tco_user_orgrole> userAccessibleOrgHeirList = _utility.GetUserAccessibleOrgHierarchyList(orgVersionContent, userName);

                    List<string> uaCurrentOrgLevelIdList = (from uaohl in userAccessibleOrgHeirList
                                                            where uaohl.hierarchy_level == selectedOrgLevel
                                                            select uaohl.fk_org_id).Distinct().ToList();

                    isEditable = !reportSubmitStatus && uaCurrentOrgLevelIdList.Contains(selectedOrgId) && !bAjTabSubmitStatus;
                }

                var datasetBeforeFilter = (from a in tenantDbContext.tfp_temp_header
                                           join b in tenantDbContext.tfp_temp_detail on new { a = a.fk_tenant_id, b = a.pk_temp_id } equals new { a = b.fk_tenant_id, b = b.fk_temp_id }
                                           join c in tenantDbContext.tco_fp_alter_codes on new { a = a.fk_tenant_id, b = b.fk_alter_code } equals new { a = c.fk_tenant_id, b = c.pk_alter_code } into c1
                                           from c2 in c1.DefaultIfEmpty()
                                           where a.fk_tenant_id == userDetails.tenant_id
                                              && b.budget_year == budgetYear
                                              && a.action_type == 60
                                           select new clsProposedBudgetAdjustmentsHelper()
                                           {
                                               actionId = a.pk_temp_id,
                                               actionDesc = a.description,
                                               deptCode = b.department_code,
                                               funcCode = b.function_code,
                                               accCode = b.fk_account_code,
                                               tags = a.tags,
                                               alterCode = (string.IsNullOrEmpty(c2.limit_description) ? string.Empty : c2.limit_description),
                                               priority = a.priority,
                                               year1Amount = b.year_1_amount,
                                               year2Amount = b.year_2_amount,
                                               year3Amount = b.year_3_amount,
                                               year4Amount = b.year_4_amount,
                                               year5Amount = b.year_5_amount == null ? 0 : b.year_5_amount.Value,
                                               description = a.long_description,
                                               internldesc = a.financial_plan_description,
                                               forecastPeriod = b.forecast_period,
                                               isImported = a.is_imported,
                                               is_MROverview_flag = a.is_MROverview_flag,// added for story 40448
                                               lineGrp = "",
                                               orgId = a.org_id,
                                               orgLevel = a.org_level ?? 0,
                                               isParkedAction = a.is_parked_action,
                                               isSamAction = a.is_sam_action,
                                               projectCode = b.project_code,
                                               freeDim1 = b.free_dim_1,
                                               freeDim2 = b.free_dim_2,
                                               freeDim3 = b.free_dim_3,
                                               freeDim4 = b.free_dim_4
                                           }).ToList();

                if (isBudgetChangesRequest && forecastPeriod != -1)
                {
                    int forecastPeriodLong = _utility.GetForecastPeriod(budgetYear, forecastPeriod);
                    datasetBeforeFilter = datasetBeforeFilter.Where(x => x.forecastPeriod == forecastPeriodLong).ToList();
                }

                if (isDoc && isMROverViewReq)// 34797 get the line group for mroverviewdata
                {
                    datasetBeforeFilter = GetLineGrpForData(datasetBeforeFilter, userDetails, budPeriod,nodeType);
                }
                if (isBudgetChangesRequest)
                {
                    datasetBeforeFilter = datasetBeforeFilter.Where(x => x.isImported != true).ToList();
                }
                else if (isMROverViewReq) // added for story 40448
                {
                    datasetBeforeFilter = datasetBeforeFilter.Where(x => x.forecastPeriod == budPeriod).ToList();
                }
                else
                {
                    datasetBeforeFilter = GetActionData(datasetBeforeFilter, budPeriod, budgetYear, userName);
                    // datasetBeforeFilter = datasetBeforeFilter.Where(x => x.forecastPeriod == budPeriod && x.is_MROverview_flag == false).ToList();
                }

                if (!isBudgetChangesRequest || isMROverViewReq)
                {
                    List<string> lstDepartments = _orgUtility.GetDepartmentsForOrgIdHierLvls(orgVersionContent, userName, budgetYear, orgInput);

                    List<string> lstFunctions = _orgUtility.GetFunctionsForOrgIdHierLvls(orgVersionContent, userName, orgInput, selectedServiceId == "-1" ? null : selectedServiceId);
                    datasetBeforeFilter = datasetBeforeFilter.Where(x => x.forecastPeriod == budPeriod).ToList();

                    if (lstDepartments != null && lstDepartments.Count() > 0)
                    {
                        datasetBeforeFilter = datasetBeforeFilter.Where(x => lstDepartments.Contains(x.deptCode)).ToList();
                    }
                    if (lstFunctions != null && lstFunctions.Count() > 0)
                    {
                        datasetBeforeFilter = datasetBeforeFilter.Where(x => lstFunctions.Contains(x.funcCode)).ToList();
                    }
                }
                List<tco_org_hierarchy> lstOrgHierarchy = orgVersionContent.lstOrgHierarchy;
                List<clsProposedBudgetAdjustmentsHelper> baseDataWithMonrepcolum = new List<clsProposedBudgetAdjustmentsHelper>();
                if (selectedCol.Contains("monthRep_level_2_code") || selectedCol.Contains("monthRep_level_2_name"))
                {
                    var monthlyReportParameters = _utility.GetParameterValue(userName, "MONTHREP_LEVEL_2");

                    if (monthlyReportParameters.Contains("org_id"))
                    {
                        switch (monthlyReportParameters)
                        {
                            case "org_id_1":
                                baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                           join oh in lstOrgHierarchy on a.deptCode equals oh.fk_department_code
                                                           select new clsProposedBudgetAdjustmentsHelper()
                                                           {
                                                               actionId = a.actionId,
                                                               actionDesc = a.actionDesc,
                                                               deptCode = a.deptCode,
                                                               funcCode = a.funcCode,
                                                               accCode = a.accCode,
                                                               tags = a.tags,
                                                               alterCode = a.alterCode,
                                                               priority = a.priority,
                                                               year1Amount = a.year1Amount,
                                                               year2Amount = a.year2Amount,
                                                               year3Amount = a.year3Amount,
                                                               year4Amount = a.year4Amount,
                                                               year5Amount = a.year5Amount,
                                                               description = a.description,
                                                               internldesc = a.internldesc,
                                                               forecastPeriod = a.forecastPeriod,
                                                               isImported = a.isImported,
                                                               is_MROverview_flag = a.is_MROverview_flag,// added for story 40448
                                                               lineGrp = a.lineGrp,
                                                               orgId = a.orgId,
                                                               orgLevel = a.orgLevel,
                                                               isParkedAction = a.isParkedAction,
                                                               isSamAction = a.isSamAction,
                                                               projectCode = a.projectCode,
                                                               freeDim1 = a.freeDim1,
                                                               freeDim2 = a.freeDim2,
                                                               freeDim3 = a.freeDim3,
                                                               freeDim4 = a.freeDim4,
                                                               monthRep_level2_code = oh.org_id_1,
                                                               monthRep_level2_name = oh.org_name_1,
                                                           }).ToList(); break;
                            case "org_id_2":
                                baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                           join oh in lstOrgHierarchy on a.deptCode equals oh.fk_department_code
                                                           select new clsProposedBudgetAdjustmentsHelper()
                                                           {
                                                               actionId = a.actionId,
                                                               actionDesc = a.actionDesc,
                                                               deptCode = a.deptCode,
                                                               funcCode = a.funcCode,
                                                               accCode = a.accCode,
                                                               tags = a.tags,
                                                               alterCode = a.alterCode,
                                                               priority = a.priority,
                                                               year1Amount = a.year1Amount,
                                                               year2Amount = a.year2Amount,
                                                               year3Amount = a.year3Amount,
                                                               year4Amount = a.year4Amount,
                                                               year5Amount = a.year5Amount,
                                                               description = a.description,
                                                               internldesc = a.internldesc,
                                                               forecastPeriod = a.forecastPeriod,
                                                               isImported = a.isImported,
                                                               is_MROverview_flag = a.is_MROverview_flag,// added for story 40448
                                                               lineGrp = a.lineGrp,
                                                               orgId = a.orgId,
                                                               orgLevel = a.orgLevel,
                                                               isParkedAction = a.isParkedAction,
                                                               isSamAction = a.isSamAction,
                                                               projectCode = a.projectCode,
                                                               freeDim1 = a.freeDim1,
                                                               freeDim2 = a.freeDim2,
                                                               freeDim3 = a.freeDim3,
                                                               freeDim4 = a.freeDim4,
                                                               monthRep_level2_code = oh.org_id_2,
                                                               monthRep_level2_name = oh.org_name_2,
                                                           }).ToList(); break;
                            case "org_id_3":
                                baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                           join oh in lstOrgHierarchy on a.deptCode equals oh.fk_department_code
                                                           select new clsProposedBudgetAdjustmentsHelper()
                                                           {
                                                               actionId = a.actionId,
                                                               actionDesc = a.actionDesc,
                                                               deptCode = a.deptCode,
                                                               funcCode = a.funcCode,
                                                               accCode = a.accCode,
                                                               tags = a.tags,
                                                               alterCode = a.alterCode,
                                                               priority = a.priority,
                                                               year1Amount = a.year1Amount,
                                                               year2Amount = a.year2Amount,
                                                               year3Amount = a.year3Amount,
                                                               year4Amount = a.year4Amount,
                                                               year5Amount = a.year5Amount,
                                                               description = a.description,
                                                               internldesc = a.internldesc,
                                                               forecastPeriod = a.forecastPeriod,
                                                               isImported = a.isImported,
                                                               is_MROverview_flag = a.is_MROverview_flag,// added for story 40448
                                                               lineGrp = a.lineGrp,
                                                               orgId = a.orgId,
                                                               orgLevel = a.orgLevel,
                                                               isParkedAction = a.isParkedAction,
                                                               isSamAction = a.isSamAction,
                                                               projectCode = a.projectCode,
                                                               freeDim1 = a.freeDim1,
                                                               freeDim2 = a.freeDim2,
                                                               freeDim3 = a.freeDim3,
                                                               freeDim4 = a.freeDim4,
                                                               monthRep_level2_code = oh.org_id_3,
                                                               monthRep_level2_name = oh.org_name_3,
                                                           }).ToList(); break;
                            case "org_id_4":
                                baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                           join oh in lstOrgHierarchy on a.deptCode equals oh.fk_department_code
                                                           select new clsProposedBudgetAdjustmentsHelper()
                                                           {
                                                               actionId = a.actionId,
                                                               actionDesc = a.actionDesc,
                                                               deptCode = a.deptCode,
                                                               funcCode = a.funcCode,
                                                               accCode = a.accCode,
                                                               tags = a.tags,
                                                               alterCode = a.alterCode,
                                                               priority = a.priority,
                                                               year1Amount = a.year1Amount,
                                                               year2Amount = a.year2Amount,
                                                               year3Amount = a.year3Amount,
                                                               year4Amount = a.year4Amount,
                                                               year5Amount = a.year5Amount,
                                                               description = a.description,
                                                               internldesc = a.internldesc,
                                                               forecastPeriod = a.forecastPeriod,
                                                               isImported = a.isImported,
                                                               is_MROverview_flag = a.is_MROverview_flag,// added for story 40448
                                                               lineGrp = a.lineGrp,
                                                               orgId = a.orgId,
                                                               orgLevel = a.orgLevel,
                                                               isParkedAction = a.isParkedAction,
                                                               isSamAction = a.isSamAction,
                                                               projectCode = a.projectCode,
                                                               freeDim1 = a.freeDim1,
                                                               freeDim2 = a.freeDim2,
                                                               freeDim3 = a.freeDim3,
                                                               freeDim4 = a.freeDim4,
                                                               monthRep_level2_code = oh.org_id_4,
                                                               monthRep_level2_name = oh.org_name_4,
                                                           }).ToList(); break;
                            case "org_id_5":
                                baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                           join oh in lstOrgHierarchy on a.deptCode equals oh.fk_department_code
                                                           select new clsProposedBudgetAdjustmentsHelper()
                                                           {
                                                               actionId = a.actionId,
                                                               actionDesc = a.actionDesc,
                                                               deptCode = a.deptCode,
                                                               funcCode = a.funcCode,
                                                               accCode = a.accCode,
                                                               tags = a.tags,
                                                               alterCode = a.alterCode,
                                                               priority = a.priority,
                                                               year1Amount = a.year1Amount,
                                                               year2Amount = a.year2Amount,
                                                               year3Amount = a.year3Amount,
                                                               year4Amount = a.year4Amount,
                                                               year5Amount = a.year5Amount,
                                                               description = a.description,
                                                               internldesc = a.internldesc,
                                                               forecastPeriod = a.forecastPeriod,
                                                               isImported = a.isImported,
                                                               is_MROverview_flag = a.is_MROverview_flag,// added for story 40448
                                                               lineGrp = a.lineGrp,
                                                               orgId = a.orgId,
                                                               orgLevel = a.orgLevel,
                                                               isParkedAction = a.isParkedAction,
                                                               isSamAction = a.isSamAction,
                                                               projectCode = a.projectCode,
                                                               freeDim1 = a.freeDim1,
                                                               freeDim2 = a.freeDim2,
                                                               freeDim3 = a.freeDim3,
                                                               freeDim4 = a.freeDim4,
                                                               monthRep_level2_code = oh.org_id_5,
                                                               monthRep_level2_name = oh.org_name_5,
                                                           }).ToList(); break;
                            case "org_id_6":
                                baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                           join oh in lstOrgHierarchy on a.deptCode equals oh.fk_department_code
                                                           select new clsProposedBudgetAdjustmentsHelper()
                                                           {
                                                               actionId = a.actionId,
                                                               actionDesc = a.actionDesc,
                                                               deptCode = a.deptCode,
                                                               funcCode = a.funcCode,
                                                               accCode = a.accCode,
                                                               tags = a.tags,
                                                               alterCode = a.alterCode,
                                                               priority = a.priority,
                                                               year1Amount = a.year1Amount,
                                                               year2Amount = a.year2Amount,
                                                               year3Amount = a.year3Amount,
                                                               year4Amount = a.year4Amount,
                                                               year5Amount = a.year5Amount,
                                                               description = a.description,
                                                               internldesc = a.internldesc,
                                                               forecastPeriod = a.forecastPeriod,
                                                               isImported = a.isImported,
                                                               is_MROverview_flag = a.is_MROverview_flag,// added for story 40448
                                                               lineGrp = a.lineGrp,
                                                               orgId = a.orgId,
                                                               orgLevel = a.orgLevel,
                                                               isParkedAction = a.isParkedAction,
                                                               isSamAction = a.isSamAction,
                                                               projectCode = a.projectCode,
                                                               freeDim1 = a.freeDim1,
                                                               freeDim2 = a.freeDim2,
                                                               freeDim3 = a.freeDim3,
                                                               freeDim4 = a.freeDim4,
                                                               monthRep_level2_code = oh.org_id_6,
                                                               monthRep_level2_name = oh.org_name_6,
                                                           }).ToList(); break;
                            case "org_id_7":
                                baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                           join oh in lstOrgHierarchy on a.deptCode equals oh.fk_department_code
                                                           select new clsProposedBudgetAdjustmentsHelper()
                                                           {
                                                               actionId = a.actionId,
                                                               actionDesc = a.actionDesc,
                                                               deptCode = a.deptCode,
                                                               funcCode = a.funcCode,
                                                               accCode = a.accCode,
                                                               tags = a.tags,
                                                               alterCode = a.alterCode,
                                                               priority = a.priority,
                                                               year1Amount = a.year1Amount,
                                                               year2Amount = a.year2Amount,
                                                               year3Amount = a.year3Amount,
                                                               year4Amount = a.year4Amount,
                                                               year5Amount = a.year5Amount,
                                                               description = a.description,
                                                               internldesc = a.internldesc,
                                                               forecastPeriod = a.forecastPeriod,
                                                               isImported = a.isImported,
                                                               is_MROverview_flag = a.is_MROverview_flag,// added for story 40448
                                                               lineGrp = a.lineGrp,
                                                               orgId = a.orgId,
                                                               orgLevel = a.orgLevel,
                                                               isParkedAction = a.isParkedAction,
                                                               isSamAction = a.isSamAction,
                                                               projectCode = a.projectCode,
                                                               freeDim1 = a.freeDim1,
                                                               freeDim2 = a.freeDim2,
                                                               freeDim3 = a.freeDim3,
                                                               freeDim4 = a.freeDim4,
                                                               monthRep_level2_code = oh.org_id_7,
                                                               monthRep_level2_name = oh.org_name_7,
                                                           }).ToList(); break;
                            case "org_id_8":
                                baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                           join oh in lstOrgHierarchy on a.deptCode equals oh.fk_department_code
                                                           select new clsProposedBudgetAdjustmentsHelper()
                                                           {
                                                               actionId = a.actionId,
                                                               actionDesc = a.actionDesc,
                                                               deptCode = a.deptCode,
                                                               funcCode = a.funcCode,
                                                               accCode = a.accCode,
                                                               tags = a.tags,
                                                               alterCode = a.alterCode,
                                                               priority = a.priority,
                                                               year1Amount = a.year1Amount,
                                                               year2Amount = a.year2Amount,
                                                               year3Amount = a.year3Amount,
                                                               year4Amount = a.year4Amount,
                                                               year5Amount = a.year5Amount,
                                                               description = a.description,
                                                               internldesc = a.internldesc,
                                                               forecastPeriod = a.forecastPeriod,
                                                               isImported = a.isImported,
                                                               is_MROverview_flag = a.is_MROverview_flag,// added for story 40448
                                                               lineGrp = a.lineGrp,
                                                               orgId = a.orgId,
                                                               orgLevel = a.orgLevel,
                                                               isParkedAction = a.isParkedAction,
                                                               isSamAction = a.isSamAction,
                                                               projectCode = a.projectCode,
                                                               freeDim1 = a.freeDim1,
                                                               freeDim2 = a.freeDim2,
                                                               freeDim3 = a.freeDim3,
                                                               freeDim4 = a.freeDim4,
                                                               monthRep_level2_code = oh.org_id_8,
                                                               monthRep_level2_name = oh.org_name_8,
                                                           }).ToList(); break;
                        }
                    }
                    else
                    {
                        List<tco_service_values> lstServiceValues = tenantDbContext.tco_service_values.Where(x => x.fk_tenant_id == userDetails.tenant_id).ToList();
                        switch (monthlyReportParameters)
                        {
                            case "service_id_1":
                                baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                           join oh in lstServiceValues on a.funcCode equals oh.fk_function_code
                                                           select new clsProposedBudgetAdjustmentsHelper()
                                                           {
                                                               actionId = a.actionId,
                                                               actionDesc = a.actionDesc,
                                                               deptCode = a.deptCode,
                                                               funcCode = a.funcCode,
                                                               accCode = a.accCode,
                                                               tags = a.tags,
                                                               alterCode = a.alterCode,
                                                               priority = a.priority,
                                                               year1Amount = a.year1Amount,
                                                               year2Amount = a.year2Amount,
                                                               year3Amount = a.year3Amount,
                                                               year4Amount = a.year4Amount,
                                                               year5Amount = a.year5Amount,
                                                               description = a.description,
                                                               internldesc = a.internldesc,
                                                               forecastPeriod = a.forecastPeriod,
                                                               isImported = a.isImported,
                                                               is_MROverview_flag = a.is_MROverview_flag,// added for story 40448
                                                               lineGrp = a.lineGrp,
                                                               orgId = a.orgId,
                                                               orgLevel = a.orgLevel,
                                                               isParkedAction = a.isParkedAction,
                                                               isSamAction = a.isSamAction,
                                                               projectCode = a.projectCode,
                                                               freeDim1 = a.freeDim1,
                                                               freeDim2 = a.freeDim2,
                                                               freeDim3 = a.freeDim3,
                                                               freeDim4 = a.freeDim4,
                                                               monthRep_level2_code = oh.service_id_1,
                                                               monthRep_level2_name = oh.service_name_1,
                                                           }).ToList(); break;
                            case "service_id_2":
                                baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                           join oh in lstServiceValues on a.funcCode equals oh.fk_function_code
                                                           select new clsProposedBudgetAdjustmentsHelper()
                                                           {
                                                               actionId = a.actionId,
                                                               actionDesc = a.actionDesc,
                                                               deptCode = a.deptCode,
                                                               funcCode = a.funcCode,
                                                               accCode = a.accCode,
                                                               tags = a.tags,
                                                               alterCode = a.alterCode,
                                                               priority = a.priority,
                                                               year1Amount = a.year1Amount,
                                                               year2Amount = a.year2Amount,
                                                               year3Amount = a.year3Amount,
                                                               year4Amount = a.year4Amount,
                                                               year5Amount = a.year5Amount,
                                                               description = a.description,
                                                               internldesc = a.internldesc,
                                                               forecastPeriod = a.forecastPeriod,
                                                               isImported = a.isImported,
                                                               is_MROverview_flag = a.is_MROverview_flag,// added for story 40448
                                                               lineGrp = a.lineGrp,
                                                               orgId = a.orgId,
                                                               orgLevel = a.orgLevel,
                                                               isParkedAction = a.isParkedAction,
                                                               isSamAction = a.isSamAction,
                                                               projectCode = a.projectCode,
                                                               freeDim1 = a.freeDim1,
                                                               freeDim2 = a.freeDim2,
                                                               freeDim3 = a.freeDim3,
                                                               freeDim4 = a.freeDim4,
                                                               monthRep_level2_code = oh.service_id_2,
                                                               monthRep_level2_name = oh.service_name_2,
                                                           }).ToList(); break;
                            case "service_id_3":
                                baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                           join oh in lstServiceValues on a.funcCode equals oh.fk_function_code
                                                           select new clsProposedBudgetAdjustmentsHelper()
                                                           {
                                                               actionId = a.actionId,
                                                               actionDesc = a.actionDesc,
                                                               deptCode = a.deptCode,
                                                               funcCode = a.funcCode,
                                                               accCode = a.accCode,
                                                               tags = a.tags,
                                                               alterCode = a.alterCode,
                                                               priority = a.priority,
                                                               year1Amount = a.year1Amount,
                                                               year2Amount = a.year2Amount,
                                                               year3Amount = a.year3Amount,
                                                               year4Amount = a.year4Amount,
                                                               year5Amount = a.year5Amount,
                                                               description = a.description,
                                                               internldesc = a.internldesc,
                                                               forecastPeriod = a.forecastPeriod,
                                                               isImported = a.isImported,
                                                               is_MROverview_flag = a.is_MROverview_flag,// added for story 40448
                                                               lineGrp = a.lineGrp,
                                                               orgId = a.orgId,
                                                               orgLevel = a.orgLevel,
                                                               isParkedAction = a.isParkedAction,
                                                               isSamAction = a.isSamAction,
                                                               projectCode = a.projectCode,
                                                               freeDim1 = a.freeDim1,
                                                               freeDim2 = a.freeDim2,
                                                               freeDim3 = a.freeDim3,
                                                               freeDim4 = a.freeDim4,
                                                               monthRep_level2_code = oh.service_id_3,
                                                               monthRep_level2_name = oh.service_name_3,
                                                           }).ToList(); break;
                            case "service_id_4":
                                baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                           join oh in lstServiceValues on a.funcCode equals oh.fk_function_code
                                                           select new clsProposedBudgetAdjustmentsHelper()
                                                           {
                                                               actionId = a.actionId,
                                                               actionDesc = a.actionDesc,
                                                               deptCode = a.deptCode,
                                                               funcCode = a.funcCode,
                                                               accCode = a.accCode,
                                                               tags = a.tags,
                                                               alterCode = a.alterCode,
                                                               priority = a.priority,
                                                               year1Amount = a.year1Amount,
                                                               year2Amount = a.year2Amount,
                                                               year3Amount = a.year3Amount,
                                                               year4Amount = a.year4Amount,
                                                               year5Amount = a.year5Amount,
                                                               description = a.description,
                                                               internldesc = a.internldesc,
                                                               forecastPeriod = a.forecastPeriod,
                                                               isImported = a.isImported,
                                                               is_MROverview_flag = a.is_MROverview_flag,// added for story 40448
                                                               lineGrp = a.lineGrp,
                                                               orgId = a.orgId,
                                                               orgLevel = a.orgLevel,
                                                               isParkedAction = a.isParkedAction,
                                                               isSamAction = a.isSamAction,
                                                               projectCode = a.projectCode,
                                                               freeDim1 = a.freeDim1,
                                                               freeDim2 = a.freeDim2,
                                                               freeDim3 = a.freeDim3,
                                                               freeDim4 = a.freeDim4,
                                                               monthRep_level2_code = oh.service_id_4,
                                                               monthRep_level2_name = oh.service_name_4,
                                                           }).ToList(); break;
                            case "service_id_5":
                                baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                           join oh in lstServiceValues on a.funcCode equals oh.fk_function_code
                                                           select new clsProposedBudgetAdjustmentsHelper()
                                                           {
                                                               actionId = a.actionId,
                                                               actionDesc = a.actionDesc,
                                                               deptCode = a.deptCode,
                                                               funcCode = a.funcCode,
                                                               accCode = a.accCode,
                                                               tags = a.tags,
                                                               alterCode = a.alterCode,
                                                               priority = a.priority,
                                                               year1Amount = a.year1Amount,
                                                               year2Amount = a.year2Amount,
                                                               year3Amount = a.year3Amount,
                                                               year4Amount = a.year4Amount,
                                                               year5Amount = a.year5Amount,
                                                               description = a.description,
                                                               internldesc = a.internldesc,
                                                               forecastPeriod = a.forecastPeriod,
                                                               isImported = a.isImported,
                                                               is_MROverview_flag = a.is_MROverview_flag,// added for story 40448
                                                               lineGrp = a.lineGrp,
                                                               orgId = a.orgId,
                                                               orgLevel = a.orgLevel,
                                                               isParkedAction = a.isParkedAction,
                                                               isSamAction = a.isSamAction,
                                                               projectCode = a.projectCode,
                                                               freeDim1 = a.freeDim1,
                                                               freeDim2 = a.freeDim2,
                                                               freeDim3 = a.freeDim3,
                                                               freeDim4 = a.freeDim4,
                                                               monthRep_level2_code = oh.service_id_5,
                                                               monthRep_level2_name = oh.service_name_5,
                                                           }).ToList(); break;
                            default:
                                baseDataWithMonrepcolum = (from a in datasetBeforeFilter
                                                           join oh in lstServiceValues on a.funcCode equals oh.fk_function_code
                                                           select new clsProposedBudgetAdjustmentsHelper()
                                                           {
                                                               actionId = a.actionId,
                                                               actionDesc = a.actionDesc,
                                                               deptCode = a.deptCode,
                                                               funcCode = a.funcCode,
                                                               accCode = a.accCode,
                                                               tags = a.tags,
                                                               alterCode = a.alterCode,
                                                               priority = a.priority,
                                                               year1Amount = a.year1Amount,
                                                               year2Amount = a.year2Amount,
                                                               year3Amount = a.year3Amount,
                                                               year4Amount = a.year4Amount,
                                                               year5Amount = a.year5Amount,
                                                               description = a.description,
                                                               internldesc = a.internldesc,
                                                               forecastPeriod = a.forecastPeriod,
                                                               isImported = a.isImported,
                                                               is_MROverview_flag = a.is_MROverview_flag,// added for story 40448
                                                               lineGrp = a.lineGrp,
                                                               orgId = a.orgId,
                                                               orgLevel = a.orgLevel,
                                                               isParkedAction = a.isParkedAction,
                                                               isSamAction = a.isSamAction,
                                                               projectCode = a.projectCode,
                                                               freeDim1 = a.freeDim1,
                                                               freeDim2 = a.freeDim2,
                                                               freeDim3 = a.freeDim3,
                                                               freeDim4 = a.freeDim4,
                                                               monthRep_level2_code = oh.service_id_2,
                                                               monthRep_level2_name = oh.service_name_2,
                                                           }).ToList(); break;
                        }
                    }
                }
                else
                {
                    baseDataWithMonrepcolum = datasetBeforeFilter;
                }

                List<clsProposedBudgetAdjustmentsHelper> groupedDatasetAfterFilter = (from d in baseDataWithMonrepcolum
                                                                                      group d by new { d.actionId, d.actionDesc, d.tags, d.alterCode, d.priority, d.description, d.internldesc, d.is_MROverview_flag, d.lineGrp, d.orgId, d.orgLevel, d.isParkedAction, d.isSamAction, d.monthRep_level2_code, d.monthRep_level2_name } into g
                                                                                      select new clsProposedBudgetAdjustmentsHelper
                                                                                      {
                                                                                          actionId = g.Key.actionId,
                                                                                          actionDesc = g.Key.actionDesc,
                                                                                          tags = g.Key.tags,
                                                                                          alterCode = g.Key.alterCode,
                                                                                          priority = g.Key.priority,
                                                                                          year1Amount = g.Sum(x => x.year1Amount),
                                                                                          year2Amount = g.Sum(x => x.year2Amount),
                                                                                          year3Amount = g.Sum(x => x.year3Amount),
                                                                                          year4Amount = g.Sum(x => x.year4Amount),
                                                                                          year5Amount = g.Sum(x => x.year5Amount),
                                                                                          description = g.Key.description,
                                                                                          internldesc = g.Key.internldesc,
                                                                                          isMROverViewReq = g.Key.is_MROverview_flag,
                                                                                          lineGrp = g.Key.lineGrp,
                                                                                          orgId = g.Key.orgId,
                                                                                          orgLevel = g.Key.orgLevel,
                                                                                          isParkedAction = g.Key.isParkedAction,
                                                                                          isSamAction = g.Key.isSamAction,
                                                                                          monthRep_level2_name = g.Key.monthRep_level2_name,
                                                                                          monthRep_level2_code = g.Key.monthRep_level2_code
                                                                                      }).OrderBy(x => x.alterCode).ThenBy(y => y.actionDesc).ToList();

                if (!isMonthlyReport && !isMROverViewReq)
                {
                    groupedDatasetAfterFilter = groupedDatasetAfterFilter.Where(x => !x.isParkedAction).ToList();
                }

                List<KeyValueData> lstActionTags = (from atg in tenantDbContext.tcoActionTags
                                                    where atg.FkTenantId == userDetails.tenant_id
                                                    select new KeyValueData
                                                    {
                                                        KeyId = atg.PkId,
                                                        ValueString = atg.TagDescription
                                                    }).ToList();

                string isSamEnabled = _utility.GetParameterValue(userName, "MR_SAM_ACCESS");
                dynamic result = new JObject();

                dynamic header = new JArray();
                dynamic header1 = new JObject();

                Dictionary<string, clsLanguageString> langStrs = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "MonthlyReport");
                Dictionary<string, clsLanguageString> langStringValuesLog = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "BudgetProposal");

                header1.title = ((langStrs.FirstOrDefault(v => v.Key == "proposed_bud_adj_title")).Value).LangText;
                header1.titleDescription = ((langStrs.FirstOrDefault(v => v.Key == "proposed_bud_adj_desc")).Value).LangText;
                header.Add(header1);

                result.header = header;
                result.isEditable = isEditable;
                result.saveEnable = isSamEnabled.ToLower() == "true";

                dynamic columnsArray = new JArray();

                // add check box column when the request is from budget changes
                if (isBudgetChangesRequest)
                {
                    columnsArray.Add(GenerateColumnObject(" ", "IsChecked", 1, false, false, null, " ", " "));
                }
                columnsArray.Add(GenerateColumnObject(((langStrs.FirstOrDefault(v => v.Key == "proposed_bud_adj_monthreplevel2_code")).Value).LangText, "monthRep_level2_code", 1, false, false, null, "text-align:left;width:25%", "text-align:left;width:25%"));
                columnsArray.Add(GenerateColumnObject(((langStrs.FirstOrDefault(v => v.Key == "proposed_bud_adj_monthreplevel2_name")).Value).LangText, "monthRep_level2_name", 1, false, false, null, "text-align:left;width:25%", "text-align:left;width:25%"));

                columnsArray.Add(GenerateColumnObject(((langStrs.FirstOrDefault(v => v.Key == "proposed_bud_adj_description")).Value).LangText, "description", 1, false, false, null, "text-align:left;width:25%", "text-align:left;width:25%"));
                if (isMonthlyReport)
                {
                    columnsArray.Add(GenerateColumnObject(((langStringValuesLog.FirstOrDefault(v => v.Key == "BP_Action_Log_icon")).Value).LangText, "description", 1, false, false, null, "text-align:left;width:5%", "text-align:left;width:5%"));
                }
                if (isTypeInsert || isDoc)
                {
                    columnsArray.Add(GenerateColumnObject(((langStrs.FirstOrDefault(v => v.Key == "proposed_bud_adj_type")).Value).LangText, "type", 3, false, false, null, "text-align:left;width:25%", "text-align:left;width:25%"));
                }

                columnsArray.Add(GenerateColumnObject(((langStrs.FirstOrDefault(v => v.Key == "proposed_bud_adj_tags")).Value).LangText, "tags", 3, false, false, null, "text-align:left;width:25%", "text-align:left;width:25%"));

                if (isSamEnabled.ToLower() == "true" && isBudgetChangesRequest)
                {
                    columnsArray.Add(GenerateColumnObject(((langStrs.FirstOrDefault(v => v.Key == "proposed_bud_adj_sam_col")).Value).LangText,
                     "isSamAction", false, false, "text-align:center;width:10%", "text-align:center;width:10%", null, "# if(id != ''){# <input type='checkbox' #=isSamAction? checked='checked' : ''  # class='chkbx'> # } #", null, false));
                }

                columnsArray.Add(GenerateColumnObject((budgetYear).ToString(), "year1", 4, false, false, "{0:n0}", "text-align:right", "text-align:right"));
                columnsArray.Add(GenerateColumnObject((budgetYear + 1).ToString(), "year2", 5, false, false, "{0:n0}", "text-align:right", "text-align:right"));
                columnsArray.Add(GenerateColumnObject((budgetYear + 2).ToString(), "year3", 6, false, false, "{0:n0}", "text-align:right", "text-align:right"));
                columnsArray.Add(GenerateColumnObject((budgetYear + 3).ToString(), "year4", 7, false, false, "{0:n0}", "text-align:right", "text-align:right"));
                columnsArray.Add(GenerateColumnObject((budgetYear + 4).ToString(), "year5", 8, false, false, "{0:n0}", "text-align:right", "text-align:right"));

                if (isSamEnabled.ToLower() == "true" && !isBudgetChangesRequest)
                {
                    columnsArray.Add(GenerateColumnObject(((langStrs.FirstOrDefault(v => v.Key == "proposed_bud_adj_sam_col")).Value).LangText,
                     "isSamAction", false, false, "text-align:center;width:10%", "text-align:center;width:10%", null, "# if(id != ''){# <input type='checkbox' #=isSamAction? checked='checked' : ''  # class='chkbx'> # } #", null, false));
                }
                result.Add("columns", columnsArray);

                var completehierarchyList = (from p in lstOrgHierarchy
                                             select new
                                             {
                                                 tenantID = p.fk_tenant_id,
                                                 orgID_1 = p.org_id_1,
                                                 orgName_1 = p.org_name_1,
                                                 orgLevel_1 = 1,
                                                 orgID_2 = p.org_id_2,
                                                 orgName_2 = p.org_name_2,
                                                 orgLevel_2 = 2,
                                                 orgID_3 = p.org_id_3,
                                                 orgName_3 = p.org_name_3,
                                                 orgLevel_3 = 3,
                                                 orgID_4 = p.org_id_4,
                                                 orgName_4 = p.org_name_4,
                                                 orgLevel_4 = 4,
                                                 orgID_5 = p.org_id_5,
                                                 orgName_5 = p.org_name_5,
                                                 orgLevel_5 = 5,
                                                 orgID_6 = p.org_id_6,
                                                 orgName_6 = p.org_name_6,
                                                 orgLevel_6 = 6,
                                                 orgID_7 = p.org_id_7,
                                                 orgName_7 = p.org_name_7,
                                                 orgLevel_7 = 7,
                                                 orgID_8 = p.org_id_8,
                                                 orgName_8 = p.org_name_8,
                                                 orgLevel_8 = 8
                                             }).Distinct().ToList();

                var data = new JArray();
                List<decimal> lstTotals = new List<decimal>() { 0, 0, 0, 0, 0 };
                var AttachmentData = _unitOfWork.InvestmentProjectRepository.GetAllAttachmentData(userDetails.tenant_id, budgetYear, Modules.MONTHLYREPORT.ToString(), isMROverViewReq ? PageId.MonthlyReportOperations.ToString() : PageId.MonthlyReportOverview.ToString()).ToList();
                foreach (var d in groupedDatasetAfterFilter)
                {
                    string orgName = string.Empty;

                    if (d.orgLevel == 1)
                    {
                        d.orgName = completehierarchyList.FirstOrDefault(x => x.orgID_1 == d.orgId).orgName_1;
                    }
                    else if (d.orgLevel == 2)
                    {
                        d.orgName = completehierarchyList.FirstOrDefault(x => x.orgID_2 == d.orgId).orgName_2;
                    }
                    else if (d.orgLevel == 3)
                    {
                        d.orgName = completehierarchyList.FirstOrDefault(x => x.orgID_3 == d.orgId).orgName_3;
                    }
                    else if (d.orgLevel == 4)
                    {
                        d.orgName = completehierarchyList.FirstOrDefault(x => x.orgID_4 == d.orgId).orgName_4;
                    }
                    else if (d.orgLevel == 5)
                    {
                        d.orgName = completehierarchyList.FirstOrDefault(x => x.orgID_5 == d.orgId).orgName_5;
                    }
                    else if (d.orgLevel == 6)
                    {
                        d.orgName = completehierarchyList.FirstOrDefault(x => x.orgID_6 == d.orgId).orgName_6;
                    }
                    else if (d.orgLevel == 7)
                    {
                        d.orgName = completehierarchyList.FirstOrDefault(x => x.orgID_7 == d.orgId).orgName_7;
                    }
                    else if (d.orgLevel == 8)
                    {
                        d.orgName = completehierarchyList.FirstOrDefault(x => x.orgID_8 == d.orgId).orgName_8;
                    }
                    else
                    {
                        d.orgName = orgName;
                    }
                }

                List<clsProposedBudgetAdjustmentsHelper> finalGroupedDataset = (from d in groupedDatasetAfterFilter
                                                                                group d by new
                                                                                {
                                                                                    monthRep_level2_code = selectedCol.Contains("monthRep_level_2_code") ? d.monthRep_level2_code : null,
                                                                                    monthRep_level2_name = selectedCol.Contains("monthRep_level_2_name") ? d.monthRep_level2_name : null,
                                                                                    d.actionId,
                                                                                    d.actionDesc,
                                                                                    d.alterCode,
                                                                                    tags = selectedCol.Contains("tags") ? d.tags : null,
                                                                                    d.priority,
                                                                                    d.description,
                                                                                    d.internldesc,
                                                                                    d.is_MROverview_flag,
                                                                                    d.lineGrp,
                                                                                    d.orgId,
                                                                                    d.orgLevel,
                                                                                    d.isParkedAction,
                                                                                    d.isSamAction,
                                                                                } into g
                                                                                select new clsProposedBudgetAdjustmentsHelper
                                                                                {
                                                                                    actionId = g.Key.actionId,
                                                                                    actionDesc = g.Key.actionDesc,
                                                                                    tags = g.Key.tags,
                                                                                    alterCode = g.Key.alterCode,
                                                                                    priority = g.Key.priority,
                                                                                    year1Amount = g.Sum(x => x.year1Amount),
                                                                                    year2Amount = g.Sum(x => x.year2Amount),
                                                                                    year3Amount = g.Sum(x => x.year3Amount),
                                                                                    year4Amount = g.Sum(x => x.year4Amount),
                                                                                    year5Amount = g.Sum(x => x.year5Amount),
                                                                                    description = g.Key.description,
                                                                                    internldesc = g.Key.internldesc,
                                                                                    isMROverViewReq = g.Key.is_MROverview_flag,
                                                                                    lineGrp = g.Key.lineGrp,
                                                                                    orgId = g.Key.orgId,
                                                                                    orgLevel = g.Key.orgLevel,
                                                                                    isParkedAction = g.Key.isParkedAction,
                                                                                    isSamAction = g.Key.isSamAction,
                                                                                    monthRep_level2_name = g.Key.monthRep_level2_name,
                                                                                    monthRep_level2_code = g.Key.monthRep_level2_code
                                                                                }).OrderBy(x => x.alterCode).ThenBy(y => y.actionDesc).ToList();

                if (selectedCol.Contains("monthRep_level_2_code") || selectedCol.Contains("monthRep_level_2_name"))
                {
                    finalGroupedDataset = finalGroupedDataset.OrderBy(z => z.monthRep_level2_code).ToList();
                }

                foreach (var d in finalGroupedDataset)
                {
                    dynamic obj = new JObject();
                    obj.id = d.actionId;
                    obj.monthRep_level2_code = d.monthRep_level2_code;
                    obj.monthRep_level2_name = d.monthRep_level2_name;
                    obj.description = d.actionDesc;
                    string orgName = string.Empty;
                    if (isMonthlyReport || isMROverViewReq)
                    {
                        obj.description = AttachmentData.Any(y => y.parent_id == d.actionId.ToString()) ? $"{d.actionDesc} <img src='../images/attachment.svg'/>" : d.actionDesc;
                    }

                    StringBuilder sb = new StringBuilder();
                    if (!d.isMROverViewReq)
                    {
                        if (!string.IsNullOrEmpty(d.orgName))
                            sb.Append("<span class='su-bp-tag su-bp-tag-medium' style='width:auto;'>" + d.orgName + "</span>&nbsp;");
                    }
                    if (isMonthlyReport)
                    {
                        if (!d.isParkedAction)
                        {
                            sb.Append("<span class='bp-tag-fin'>Ønskede tiltak</span>&nbsp;");
                        }
                        else
                            sb.Append("<span class='inv-running'>Parkerte tiltak</span>&nbsp;");
                    }
                    if (!string.IsNullOrEmpty(d.tags))
                    {
                        List<int> mappedTagIds = d.tags.Split(',').Select(int.Parse).ToList();

                        foreach (var tags in mappedTagIds)
                        {
                            var firstOrDefault = lstActionTags.FirstOrDefault(x => x.KeyId == tags);
                            if (firstOrDefault != null)
                            {
                                //sb.Append("<span class='bp-tag-selected'>" + firstOrDefault.ValueString + "</span>&nbsp;");
                                sb.Append(firstOrDefault.ValueString + " ");
                            }
                        }
                    }

                    obj.type = d.alterCode;
                    obj.tags = sb.ToString();
                    obj.year1 = d.year1Amount / 1000;
                    lstTotals[0] = lstTotals[0] + (d.year1Amount / 1000);
                    obj.year2 = d.year2Amount / 1000;
                    lstTotals[1] = lstTotals[1] + (d.year2Amount / 1000);
                    obj.year3 = d.year3Amount / 1000;
                    lstTotals[2] = lstTotals[2] + (d.year3Amount / 1000);
                    obj.year4 = d.year4Amount / 1000;
                    lstTotals[3] = lstTotals[3] + (d.year4Amount / 1000);
                    obj.year5 = d.year5Amount / 1000;
                    lstTotals[4] = lstTotals[4] + (d.year5Amount / 1000);
                    obj.tooltipdesc = d.description;
                    obj.internldesc = d.internldesc;
                    obj.isMROverViewReq = d.isMROverViewReq;
                    obj.lineGrp = d.lineGrp;
                    obj.isSamAction = d.isSamAction;
                    data.Add(obj);
                }

                var action = data.ToObject<List<clsProposedBudgetAdjustmentsHelper>>();
                if (filterInput != null && !string.IsNullOrEmpty(filterInput.description))
                {
                    //var tagId = lstActionTags.Where(x => x.ValueString.ToLower().Contains(filterInput.description.ToLower())).Select(x => x.KeyId).ToList();

                    action = action.Where(x => x.description.ToLower().Contains(filterInput.description.ToLower()) || x.tags.ToLower().Contains(filterInput.description.ToLower())).ToList();
                }
                if (filterInput != null && filterInput.sam != "0")
                {
                    if (filterInput.sam == "1")
                    {
                        action = action.Where(x => x.isSamAction == true).ToList();
                    }
                    else if (filterInput.sam == "2")
                    {
                        action = action.Where(x => x.isSamAction == false).ToList();
                    }
                }
                if (filterInput != null && !string.IsNullOrEmpty(filterInput.type))
                {
                    action = action.Where(x => x.type.Contains(filterInput.type)).ToList();
                }
                if (!isBudgetChangesRequest)
                {
                    clsProposedBudgetAdjustmentsHelper objTotals = new clsProposedBudgetAdjustmentsHelper();
                    objTotals.id = 0;
                    objTotals.monthRep_level2_code = selectedCol.Contains("monthRep_level_2_code") ? ((langStrs.FirstOrDefault(v => v.Key == "proposed_bud_adj_total")).Value).LangText : string.Empty;
                    objTotals.monthRep_level2_name = (!selectedCol.Contains("monthRep_level_2_code") && selectedCol.Contains("monthRep_level_2_name")) ? ((langStrs.FirstOrDefault(v => v.Key == "proposed_bud_adj_total")).Value).LangText : string.Empty;
                    objTotals.description = (!selectedCol.Contains("monthRep_level_2_code") && !selectedCol.Contains("monthRep_level_2_name")) ? ((langStrs.FirstOrDefault(v => v.Key == "proposed_bud_adj_total")).Value).LangText : string.Empty;
                    //objTotals.monthRep_level2_code = string.Empty;
                    //objTotals.monthRep_level2_name = string.Empty;
                    //objTotals.description = ((langStrs.FirstOrDefault(v => v.Key == "proposed_bud_adj_total")).Value).LangText;
                    objTotals.type = string.Empty;
                    objTotals.tags = string.Empty;
                    objTotals.year1 = lstTotals[0];
                    objTotals.year2 = lstTotals[1];
                    objTotals.year3 = lstTotals[2];
                    objTotals.year4 = lstTotals[3];
                    objTotals.year5 = lstTotals[4];
                    objTotals.tooltipdesc = string.Empty;
                    objTotals.internldesc = string.Empty;
                    objTotals.isSamAction = false;

                    action.Add(objTotals);
                }

                result.Add("data", JToken.FromObject(action));

                var paramValue = _utility.GetParameterValue(userName, "YB_UPDATE_MANDATORY");
                bool activateAndDisableBudgetCheckbox = false;
                if (!string.IsNullOrEmpty(paramValue) && paramValue.ToLower() == "true".ToLower())
                    activateAndDisableBudgetCheckbox = true;

                result.Add("activateAndDisableBudgetCheckbox", activateAndDisableBudgetCheckbox);
                return result;
            }
            catch (Exception e)
            {
                throw new Exception(e.Message);
            }
        }

        public async Task<JObject> GetMRBudgetAdjustmentGrid(string userName, int budgetYear, int budPeriod, int selectedOrgLevel, string selectedOrgId, string selectedServiceId, OrgInpLevels orgInput, bool isChapterSetupMR, bool isServiceSetupMR, string attributeId, Stream? configStream)
        {
            
            UserData userDetails = await _utility.GetUserDetailsAsync(userName);
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            string isSamEnabled = await _utility.GetParameterValueAsync(userName, "MR_SAM_ACCESS");
            Dictionary<string, clsLanguageString> langStrs = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "MonthlyReport");
            Dictionary<string, clsLanguageString> BPlangStr = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetProposal");
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userName, budPeriod);
            bool isEditable = true;
            dynamic result = new JObject();
            List<KeyValueNewData> actionTypeList = new List<KeyValueNewData>();
            List<KeyValueNewData> actionStatusList = new List<KeyValueNewData>();
            List<KeyValueNewData> tagList = new List<KeyValueNewData>();
            List<KeyValueInt> actionList = new List<KeyValueInt>();
            List<KeyValueNewData> orgCreatedAtList = new List<KeyValueNewData>();
            List<string> serviceNameList = new List<string>();
            List<string> chapterList = new List<string>();
            List<AttributeDepartmentMap> chapter_values = new List<AttributeDepartmentMap>();


            var (reportSubmitStatus, tempNaParam) = await _mrfUtility.GetMonthlyReportStatusAsync(userName, selectedOrgId, selectedServiceId, budPeriod.ToString(), selectedOrgLevel, string.Empty, true);
            bool bAjTabSubmitStatus = false;

            // if report is set then only role_id 2 and 12 can edit.
            if (reportSubmitStatus || bAjTabSubmitStatus)
            {
                isEditable = await _mrfUtility.GetMonthlyReportUserRoleEditAccessAsync(userDetails.pk_id, userDetails.tenant_id);
            }
            else
            {
                List<tco_user_orgrole> userAccessibleOrgHeirList = await _utility.GetUserAccessibleOrgHierarchyListAsync(orgVersionContent, userName);

                List<string> uaCurrentOrgLevelIdList = (from uaohl in userAccessibleOrgHeirList
                                                        where uaohl.hierarchy_level == selectedOrgLevel
                                                        select uaohl.fk_org_id).Distinct().ToList();

                isEditable = !reportSubmitStatus && uaCurrentOrgLevelIdList.Contains(selectedOrgId) && !bAjTabSubmitStatus;
            }

            var datasetBeforeFilter = await (from a in tenantDbContext.tfp_temp_header
                                                join b in tenantDbContext.tfp_temp_detail on new { a = a.fk_tenant_id, b = a.pk_temp_id } equals new { a = b.fk_tenant_id, b = b.fk_temp_id }
                                                join c in tenantDbContext.tco_fp_alter_codes on new { a = a.fk_tenant_id, b = b.fk_alter_code } equals new { a = c.fk_tenant_id, b = c.pk_alter_code } into c1
                                                from c2 in c1.DefaultIfEmpty()
                                                where a.fk_tenant_id == userDetails.tenant_id
                                                && b.budget_year == budgetYear
                                                && a.action_type == 60
                                                select new clsProposedBudgetAdjustmentsHelper()
                                                {
                                                    actionId = a.pk_temp_id,
                                                    actionDesc = a.description,
                                                    deptCode = b.department_code,
                                                    funcCode = b.function_code,
                                                    accCode = b.fk_account_code,
                                                    tags = a.tags,
                                                    alterCode = (string.IsNullOrEmpty(c2.limit_description) ? string.Empty : c2.limit_description),
                                                    priority = a.priority,
                                                    year1Amount = b.year_1_amount,
                                                    year2Amount = b.year_2_amount,
                                                    year3Amount = b.year_3_amount,
                                                    year4Amount = b.year_4_amount,
                                                    year5Amount = b.year_5_amount == null ? 0 : b.year_5_amount.Value,
                                                    description = a.long_description,
                                                    internldesc = a.financial_plan_description,
                                                    forecastPeriod = b.forecast_period,
                                                    isImported = a.is_imported,
                                                    is_MROverview_flag = a.is_MROverview_flag,// added for story 40448
                                                    lineGrp = "",
                                                    orgId = a.org_id,
                                                    orgLevel = a.org_level ?? 0,
                                                    isParkedAction = a.is_parked_action,
                                                    isSamAction = a.is_sam_action,
                                                    projectCode = b.project_code,
                                                    freeDim1 = b.free_dim_1,
                                                    freeDim2 = b.free_dim_2,
                                                    freeDim3 = b.free_dim_3,
                                                    freeDim4 = b.free_dim_4,
                                                    tempAdjFinplan = false,
                                                    tempAdjSam = false,
                                                    pkAlterCode = c2.pk_alter_code
                                                }).ToListAsync();
            datasetBeforeFilter = await GetActionDataAsync(datasetBeforeFilter, budPeriod, budgetYear, userName);
                
            List<string> lstDepartments = new List<string>();
            if (isChapterSetupMR && !(attributeId == "-1" || string.IsNullOrEmpty(attributeId)))
            {
                lstDepartments = await _finUtility.GetAttributeConnectedDepartmentsForSelectedOrgAsync(userName, budgetYear, selectedOrgId, selectedOrgLevel, attributeId);
            }
            else
            {
                lstDepartments = await _orgUtility.GetDepartmentsForOrgIdHierLvlsAsync(orgVersionContent, userName, budgetYear, orgInput);
            }

            List<string> lstFunctions = await _orgUtility.GetFunctionsForOrgIdHierLvlsAsync(orgVersionContent, userName, orgInput, selectedServiceId == "-1" ? null : selectedServiceId);
            datasetBeforeFilter = datasetBeforeFilter.Where(x => x.forecastPeriod == budPeriod).ToList();

            if (lstDepartments != null && lstDepartments.Count() > 0)
            {
                datasetBeforeFilter = datasetBeforeFilter.Where(x => lstDepartments.Contains(x.deptCode)).ToList();
            }
            if (lstFunctions != null && lstFunctions.Count() > 0)
            {
                datasetBeforeFilter = datasetBeforeFilter.Where(x => lstFunctions.Contains(x.funcCode)).ToList();
            }
                

            var groupedDatasetAfterFilter = (from d in datasetBeforeFilter
                                            group d by new { d.actionId, d.actionDesc, d.tags, d.alterCode, d.priority, d.description, d.internldesc, d.is_MROverview_flag, d.lineGrp, d.orgId, d.orgLevel, d.isParkedAction, d.isSamAction, d.pkAlterCode} into g
                                            select new clsProposedBudgetAdjustmentsHelper
                                            {
                                                actionId = g.Key.actionId,
                                                actionDesc = g.Key.actionDesc,
                                                tags = g.Key.tags,
                                                alterCode = g.Key.alterCode,
                                                priority = g.Key.priority,
                                                year1Amount = g.Sum(x => x.year1Amount),
                                                year2Amount = g.Sum(x => x.year2Amount),
                                                year3Amount = g.Sum(x => x.year3Amount),
                                                year4Amount = g.Sum(x => x.year4Amount),
                                                year5Amount = g.Sum(x => x.year5Amount),
                                                description = g.Key.description,
                                                internldesc = g.Key.internldesc,
                                                isMROverViewReq = g.Key.is_MROverview_flag,
                                                lineGrp = g.Key.lineGrp,
                                                orgId = g.Key.orgId,
                                                orgLevel = g.Key.orgLevel,
                                                isParkedAction = g.Key.isParkedAction,
                                                isSamAction = g.Key.isSamAction,
                                                pkAlterCode = g.Key.pkAlterCode,
                                            }).OrderBy(x => x.alterCode).ThenBy(y => y.actionDesc).ToList();

                
            List<KeyValueData> lstActionTags = await (from atg in tenantDbContext.tcoActionTags
                                                        where atg.FkTenantId == userDetails.tenant_id
                                                        select new KeyValueData
                                                        {
                                                            KeyId = atg.PkId,
                                                            ValueString = atg.TagDescription
                                                        }).ToListAsync();

            List<tco_org_hierarchy> lstOrgHierarchy = orgVersionContent.lstOrgHierarchy;
            var completehierarchyList = (from p in lstOrgHierarchy
                                            select new
                                            {
                                                tenantID = p.fk_tenant_id,
                                                orgID_1 = p.org_id_1,
                                                orgName_1 = p.org_name_1,
                                                orgLevel_1 = 1,
                                                orgID_2 = p.org_id_2,
                                                orgName_2 = p.org_name_2,
                                                orgLevel_2 = 2,
                                                orgID_3 = p.org_id_3,
                                                orgName_3 = p.org_name_3,
                                                orgLevel_3 = 3,
                                                orgID_4 = p.org_id_4,
                                                orgName_4 = p.org_name_4,
                                                orgLevel_4 = 4,
                                                orgID_5 = p.org_id_5,
                                                orgName_5 = p.org_name_5,
                                                orgLevel_5 = 5,
                                                orgID_6 = p.org_id_6,
                                                orgName_6 = p.org_name_6,
                                                orgLevel_6 = 6,
                                                orgID_7 = p.org_id_7,
                                                orgName_7 = p.org_name_7,
                                                orgLevel_7 = 7,
                                                orgID_8 = p.org_id_8,
                                                orgName_8 = p.org_name_8,
                                                orgLevel_8 = 8
                                            }).Distinct().ToList();

            var data = new List<BudgetAdjustmentGridDataHelper>();
            List<decimal> lstTotals = new List<decimal>() { 0, 0, 0, 0, 0 };
            var AttachmentData = (await _unitOfWork.InvestmentProjectRepository.GetAllAttachmentDataAsync(userDetails.tenant_id, budgetYear, Modules.MONTHLYREPORT.ToString(), PageId.MonthlyReportOverview.ToString())).ToList();
            foreach (var d in groupedDatasetAfterFilter)
            {
                string orgName = string.Empty;

                if (d.orgLevel == 1)
                {
                    d.orgName = completehierarchyList.FirstOrDefault(x => x.orgID_1 == d.orgId).orgName_1;
                }
                else if (d.orgLevel == 2)
                {
                    d.orgName = completehierarchyList.FirstOrDefault(x => x.orgID_2 == d.orgId).orgName_2;
                }
                else if (d.orgLevel == 3)
                {
                    d.orgName = completehierarchyList.FirstOrDefault(x => x.orgID_3 == d.orgId).orgName_3;
                }
                else if (d.orgLevel == 4)
                {
                    d.orgName = completehierarchyList.FirstOrDefault(x => x.orgID_4 == d.orgId).orgName_4;
                }
                else if (d.orgLevel == 5)
                {
                    d.orgName = completehierarchyList.FirstOrDefault(x => x.orgID_5 == d.orgId).orgName_5;
                }
                else if (d.orgLevel == 6)
                {
                    d.orgName = completehierarchyList.FirstOrDefault(x => x.orgID_6 == d.orgId).orgName_6;
                }
                else if (d.orgLevel == 7)
                {
                    d.orgName = completehierarchyList.FirstOrDefault(x => x.orgID_7 == d.orgId).orgName_7;
                }
                else if (d.orgLevel == 8)
                {
                    d.orgName = completehierarchyList.FirstOrDefault(x => x.orgID_8 == d.orgId).orgName_8;
                }
                else
                {
                    d.orgName = orgName;
                }
            }

            if (isChapterSetupMR)
            {
                chapter_values = _finUtility.GetObjectAttributeConnectedDepartments(userName, budgetYear, "CHAPTER");
            }
                
            foreach (var d in groupedDatasetAfterFilter)
            {
                BudgetAdjustmentGridDataHelper obj = new BudgetAdjustmentGridDataHelper();
                obj.id = d.actionId;
                string orgName = string.Empty;
                StringBuilder sbTags = new StringBuilder();
                    if (isServiceSetupMR)
                    {
                        obj.chapter = "";
                        d.funcCode = datasetBeforeFilter.FirstOrDefault(x => x.actionId == d.actionId).funcCode;
                        tco_service_values tServs = tenantDbContext.tco_service_values.FirstOrDefault(t => t.fk_function_code == d.funcCode);
                        if (tServs != null)
                        {
                            obj.serviceName = tServs.service_id_2 + " " + tServs.service_name_2;
                            serviceNameList.Add(obj.serviceName);
                        }
                    }
                    if (isChapterSetupMR)
                    {
                        obj.serviceName = "";
                        d.deptCode = datasetBeforeFilter.FirstOrDefault(x => x.actionId == d.actionId).deptCode;
                        AttributeDepartmentMap attdeptMap = chapter_values.FirstOrDefault(c => c.departmentCode == d.deptCode);
                        if (attdeptMap != null)
                        {
                            obj.chapter = attdeptMap.attributeName;
                            chapterList.Add(obj.chapter);
                        }
                    }

                obj.actionName = d.actionDesc;
                obj.hasAttachment = AttachmentData.Any(y => y.parent_id == d.actionId.ToString());
                if (!string.IsNullOrEmpty(d.orgName))
                        obj.orgName = ("<span class='org-created-tag'>" + d.orgName + "</span>");
                if (orgCreatedAtList.FirstOrDefault(z => z.Key == d.orgId) == null)
                {
                    orgCreatedAtList.Add(new KeyValueNewData() { Key = d.orgId, Value = d.orgName });
                }
                if (d.isParkedAction)
                {
                    string parkedAction = BPlangStr.FirstOrDefault(v => v.Key == "BudProp_tableType_parkedBlist").Value.LangText;
                    obj.actionStatus = ("<span class='status-tags'><img src = './assets/images/parked_action.svg' alt='parked action' /></span> <span>" + parkedAction + "</span>");
                    if (actionStatusList.FirstOrDefault(z => z.Key == parkedAction) == null)
                    {
                        actionStatusList.Add(new KeyValueNewData() { Key = parkedAction, Value = parkedAction });
                    }
                }
                else
                {
                    string bList = BPlangStr.FirstOrDefault(v => v.Key == "BP_GridColumn_type2").Value.LangText;
                    obj.actionStatus = ("<span class='status-tags'><img src = './assets/images/blist_action.svg' alt='blist action' /></span> <span>" + bList +"</span>");
                    if (actionStatusList.FirstOrDefault(z => z.Key == bList) == null)
                    {
                        actionStatusList.Add(new KeyValueNewData() { Key = bList, Value = bList });
                    }
                }
                if (actionList.FirstOrDefault(z => z.Key == d.actionId) == null)
                {
                    actionList.Add(new KeyValueInt() { Key = d.actionId, Value = d.actionDesc });
                }
                if (actionTypeList.FirstOrDefault(z => z.Key == d.pkAlterCode) == null)
                {
                    actionTypeList.Add(new KeyValueNewData() { Key = d.pkAlterCode, Value = d.alterCode });
                }
                if (!string.IsNullOrEmpty(d.tags))
                {
                    List<int> mappedTagIds = d.tags.Split(',').Select(int.Parse).ToList();
                    
                    foreach (var tags in mappedTagIds)
                    {
                        var firstOrDefault = lstActionTags.FirstOrDefault(x => x.KeyId == tags);
                        if (firstOrDefault != null)
                        {
                            tagList.Add(new KeyValueNewData() { Key = firstOrDefault.ValueString, Value = firstOrDefault.ValueString });
                            sbTags.Append("<span class='f-13 br-5 px-2 action-tags mt-2'>" + firstOrDefault.ValueString + "</span>");
                        }
                    }
                }

                obj.tags = sbTags.ToString();
                obj.type = d.alterCode;
                obj.year1 = d.year1Amount / 1000;
                lstTotals[0] = lstTotals[0] + (d.year1Amount / 1000);
                obj.year2 = d.year2Amount / 1000;
                lstTotals[1] = lstTotals[1] + (d.year2Amount / 1000);
                obj.year3 = d.year3Amount / 1000;
                lstTotals[2] = lstTotals[2] + (d.year3Amount / 1000);
                obj.year4 = d.year4Amount / 1000;
                lstTotals[3] = lstTotals[3] + (d.year4Amount / 1000);
                obj.year5 = d.year5Amount / 1000;
                lstTotals[4] = lstTotals[4] + (d.year5Amount / 1000);
                obj.description = string.IsNullOrEmpty(d.description) ? "" : d.description;
                obj.lineGroup = d.lineGrp;
                obj.isSamAction = d.isSamAction;
                obj.tempAdjSam = d.tempAdjSam;
                obj.tempAdjFinplan = d.tempAdjFinplan;
                data.Add(obj);
            }


            BudgetAdjustmentGridDataHelper objTotals = new BudgetAdjustmentGridDataHelper();
                objTotals.id = 0;
                objTotals.actionName = ((langStrs.FirstOrDefault(v => v.Key == "proposed_bud_adj_total")).Value).LangText;
                objTotals.orgName = string.Empty;
                objTotals.type = string.Empty;
                objTotals.actionStatus = string.Empty;
                objTotals.year1 = lstTotals[0];
                objTotals.year2 = lstTotals[1];
                objTotals.year3 = lstTotals[2];
                objTotals.year4 = lstTotals[3];
                objTotals.year5 = lstTotals[4];
                objTotals.description = string.Empty;
                objTotals.isSamAction = false;
                objTotals.lineGroup = string.Empty;
                objTotals.tags = string.Empty;
                objTotals.hasAttachment = false;
                data.Add(objTotals);
                

            result.Add("data", JToken.FromObject(data));
            result.isEditable = isEditable;
            result.saveEnable = isSamEnabled.ToLower() == "true";

            result.Add("columns", await GetBudgetAdjustmentColumns(userName, configStream, budgetYear, isSamEnabled, isChapterSetupMR, isServiceSetupMR));
            var paramValue = await _utility.GetParameterValueAsync(userName, "YB_UPDATE_MANDATORY");
            bool activateAndDisableBudgetCheckbox = false;
            if (!string.IsNullOrEmpty(paramValue) && paramValue.ToLower() == "true".ToLower())
                activateAndDisableBudgetCheckbox = true;

            result.Add("activateAndDisableBudgetCheckbox", activateAndDisableBudgetCheckbox);
            result.actionList = JToken.FromObject(actionList);
            result.actionStatusList = JToken.FromObject(actionStatusList);
            result.actionTypeList = JToken.FromObject(actionTypeList);
            result.orgCreatedAtList = JToken.FromObject(orgCreatedAtList);
            result.tagList = JToken.FromObject(tagList.DistinctBy(x => x.Key));
            result.serviceNameList = (serviceNameList.Count > 0) ? JToken.FromObject(serviceNameList.Distinct()) : JToken.FromObject(serviceNameList) ;
            result.chapterList = (chapterList.Count > 0) ? JToken.FromObject(chapterList.Distinct()) : JToken.FromObject(chapterList);

            return result;
            
        }

        public async Task<JObject> GetActionDetailsBListV2(string userID, MRActionDetailBlistInput inputObj)
        {
            int budgetYear = inputObj.budgetYear;
            int actionId = inputObj.actionId;
            int orgLevel = inputObj.orgLevel;
            string orgId = inputObj.orgId;
            string serviceId = inputObj.serviceId;
            string attributeId = inputObj.attributeId;
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name,"ConsequenceAdjustedBudget");
            Dictionary<string, clsLanguageString> langStringValuesYBtype = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "YearlyBudget");
            Dictionary<string, clsLanguageString> langStringValuesMonRep = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "MonthlyReport");
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            List<tco_free_dim_values> lstFreeDimValues = (await _utility.GetFreeDimValuesAsync(userID)).ToList();

            JArray lstAjstmt = JArray.FromObject(await _consequenceAdjustBudget.GetAdjustmentCodesAsync(userID));
            JArray lstAlter = JArray.FromObject(await _consequenceAdjustBudget.GetAlterCodesAsync(userID, 60));

            var lst = await (from th in tenantDbContext.tfp_temp_header
                             join td in tenantDbContext.tfp_temp_detail on new { a = th.fk_tenant_id, b = th.pk_temp_id } equals new { a = td.fk_tenant_id, b = td.fk_temp_id }
                             join ta in tenantDbContext.tco_accounts on
                                 new { b = td.fk_account_code, c = th.fk_tenant_id } equals
                                 new { b = ta.pk_account_code, c = ta.pk_tenant_id }
                             join f1 in tenantDbContext.tco_functions on
                                 new { a = td.function_code, b = th.fk_tenant_id } equals
                                 new { a = f1.pk_Function_code, b = f1.pk_tenant_id }
                             join p1 in tenantDbContext.tco_projects.Where(x => x.date_from.Year <= budgetYear && x.date_to.Year >= budgetYear) on
                                 new { a = td.project_code, b = th.fk_tenant_id } equals
                                 new { a = p1.pk_project_code, b = p1.fk_tenant_id } into lojproj
                             from pj in lojproj.DefaultIfEmpty()
                             where (th.fk_tenant_id == userDetails.tenant_id && td.budget_year == budgetYear)
                             && budgetYear >= ta.dateFrom.Year && budgetYear <= ta.dateTo.Year
                             && budgetYear >= f1.dateFrom.Year && budgetYear <= f1.dateTo.Year
                             select new clsActions
                             {
                                 actionType = th.action_type,
                                 actionID = th.pk_temp_id,
                                 actionDescription = th.description,
                                 consequence = th.consequence,
                                 pk_id = td.pk_id,
                                 accountCode = ta.pk_account_code,
                                 accountName = ta.display_name,
                                 deptCode = td.department_code,
                                 functionCode = td.function_code,
                                 functionName = f1.display_name,
                                 projectCode = td.project_code == null ? String.Empty : td.project_code,
                                 projectName = (pj == null ? String.Empty : pj.project_name),
                                 freedimCode1 = td.free_dim_1 == null ? String.Empty : td.free_dim_1,
                                 freedimName1 = string.Empty,
                                 freedimCode2 = td.free_dim_2 == null ? String.Empty : td.free_dim_2,
                                 freedimName2 = string.Empty,
                                 freedimCode3 = td.free_dim_3 == null ? String.Empty : td.free_dim_3,
                                 freedimName3 = string.Empty,
                                 freedimCode4 = td.free_dim_4 == null ? String.Empty : td.free_dim_4,
                                 freedimName4 = string.Empty,
                                 year1Ammount = td.year_1_amount,
                                 year2Ammount = td.year_2_amount,
                                 year3Ammount = td.year_3_amount,
                                 year4Ammount = td.year_4_amount,
                                 year5Ammount = td.year_5_amount == null ? 0 : td.year_5_amount,
                                 fk_area_id = th.fk_area_id == null ? 0 : th.fk_area_id.Value,
                                 tag = th.tag == null ? "" : th.tag,
                                 priority = th.priority == null ? 0 : th.priority.Value,
                                 long_description = th.long_description == null ? "" : th.long_description,
                                 fk_alter_code = td.fk_alter_code,
                                 fk_adjustment_code = td.fk_adjustment_code,
                                 tags = th.tags,
                                 description = td.description,
                                 fk_key_id = td.fk_key_id == null ? 0 : td.fk_key_id.Value,
                                 isParkedAction = th.is_parked_action
                             }).ToListAsync();

            var lst2 = await (from th in tenantDbContext.tfp_temp_header
                             where (th.fk_tenant_id == userDetails.tenant_id)
                             select new clsActions
                             {
                                 actionType = th.action_type,
                                 actionID = th.pk_temp_id,
                                 actionDescription = th.description,
                                 consequence = th.consequence,
                                 fk_area_id = th.fk_area_id == null ? 0 : th.fk_area_id.Value,
                                 tag = th.tag == null ? "" : th.tag,
                                 priority = th.priority == null ? 0 : th.priority.Value,
                                 long_description = th.long_description == null ? "" : th.long_description,
                                 tags = th.tags,
                                 isParkedAction = th.is_parked_action
                             }).ToListAsync();

            lst = lst.Where(x => x.actionID == actionId).ToList();

            var adjustmentCodesSet = await tenantDbContext.tco_adjustment_codes
                .Where(y => y.fk_tenant_id == userDetails.tenant_id).ToListAsync();

            var alterCodesSet = await tenantDbContext.tco_fp_alter_codes
                .Where(y => y.fk_tenant_id == userDetails.tenant_id).ToListAsync();

            lst.ForEach(x =>
            {
                x.freedimName1 = lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode1) == null
                    ? string.Empty
                    : lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode1).description;
                x.freedimName2 = lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode2) == null
                    ? string.Empty
                    : lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode2).description;
                x.freedimName3 = lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode3) == null
                    ? string.Empty
                    : lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode3).description;
                x.freedimName4 = lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode4) == null
                    ? string.Empty
                    : lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode4).description;
                x.adjustmentCodeName =
                    adjustmentCodesSet.Where(
                        y => y.fk_tenant_id == userDetails.tenant_id && y.pk_adjustment_code == x.fk_adjustment_code)
                        .ToList()
                        .Count == 0
                        ? string.Empty
                        : adjustmentCodesSet.Where(
                            y => y.fk_tenant_id == userDetails.tenant_id && y.pk_adjustment_code == x.fk_adjustment_code)
                            .Select(y => y.description)
                            .First()
                            .ToString();
                x.alterCodeName =
                    alterCodesSet.Where(
                        y => y.fk_tenant_id == userDetails.tenant_id && y.pk_alter_code == x.fk_alter_code)
                        .ToList()
                        .Count == 0
                        ? string.Empty
                        : alterCodesSet.Where(
                            y => y.fk_tenant_id == userDetails.tenant_id && y.pk_alter_code == x.fk_alter_code)
                            .Select(y => y.alter_description)
                            .First()
                            .ToString();
            });

            Dictionary<string, string> departments = new Dictionary<string, string>();
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userID, _utility.GetForecastPeriod(budgetYear, 1));
            List<Department> lstDepartments = (await _utility.GetTenantDepartmentsAsync(userID, budgetYear)).ToList();
            List<string> departmentsOnOrg = new List<string>();
            switch (orgLevel)
            {
                case 1:
                    departmentsOnOrg = orgVersionContent.lstOrgHierarchy.Where(x => x.org_id_1 == orgId).Select(x => x.fk_department_code).ToList();
                    break;

                case 2:
                    departmentsOnOrg = orgVersionContent.lstOrgHierarchy.Where(x => x.org_id_2 == orgId).Select(x => x.fk_department_code).ToList();
                    break;

                case 3:
                    departmentsOnOrg = orgVersionContent.lstOrgHierarchy.Where(x => x.org_id_3 == orgId).Select(x => x.fk_department_code).ToList();
                    break;

                case 4:
                    departmentsOnOrg = orgVersionContent.lstOrgHierarchy.Where(x => x.org_id_4 == orgId).Select(x => x.fk_department_code).ToList();
                    break;

                case 5:
                    departmentsOnOrg = orgVersionContent.lstOrgHierarchy.Where(x => x.org_id_5 == orgId).Select(x => x.fk_department_code).ToList();
                    break;

                case 6:
                    departmentsOnOrg = orgVersionContent.lstOrgHierarchy.Where(x => x.org_id_6 == orgId).Select(x => x.fk_department_code).ToList();
                    break;

                case 7:
                    departmentsOnOrg = orgVersionContent.lstOrgHierarchy.Where(x => x.org_id_7 == orgId).Select(x => x.fk_department_code).ToList();
                    break;

                case 8:
                    departmentsOnOrg = orgVersionContent.lstOrgHierarchy.Where(x => x.org_id_8 == orgId).Select(x => x.fk_department_code).ToList();
                    break;
            }

            if (departmentsOnOrg != null && departmentsOnOrg.Count > 0)
            {
                lst = lst.Where(x => departmentsOnOrg.Contains(x.deptCode)).ToList();
            }
            if (!lst.Any())
            {
                lst = lst2.Where(x => x.actionID == actionId).ToList();
            }
            // Get Departments if chapterSetup is true
            List<string> deptFromChapter = new List<string>();
            if (inputObj.isChapterSetupMR)
            {
                if (attributeId != "-1" && !string.IsNullOrEmpty(attributeId))
                {
                    deptFromChapter = await _finUtility.GetAttributeConnectedDepartmentsForSelectedOrgAsync(userID, budgetYear, orgId, orgLevel, attributeId);
                    lstDepartments = (from d1 in deptFromChapter
                                      join d2 in lstDepartments
                                      on d1 equals d2.departmentValue
                                      select new Department
                                      {
                                          departmentValue = d1,
                                          departmentText = d2.departmentText
                                      }).ToList();
                }
            }



            foreach (var data in lstDepartments)
            {
                string deptID = data.departmentValue;
                string deptName = data.departmentText;
                if (!departments.ContainsKey(deptID))
                {
                    departments.Add(deptID, deptName);
                }
            }

            var result = (from a in lst
                          join b in departments on a.deptCode equals b.Key
                          select new clsActions
                          {
                              actionType = a.actionType,
                              actionID = a.actionID,
                              actionDescription = a.actionDescription,
                              consequence = a.consequence,
                              pk_id = a.pk_id,
                              accountCode = a.accountCode,
                              accountName = a.accountName,
                              deptCode = a.deptCode,
                              deptName = b.Value,
                              functionCode = a.functionCode,
                              functionName = a.functionName,
                              projectCode = a.projectCode,
                              projectName = a.projectName,
                              freedimCode1 = a.freedimCode1,
                              freedimName1 = a.freedimName1,
                              freedimCode2 = a.freedimCode2,
                              freedimName2 = a.freedimName2,
                              freedimCode3 = a.freedimCode3,
                              freedimName3 = a.freedimName3,
                              freedimCode4 = a.freedimCode4,
                              freedimName4 = a.freedimName4,
                              year1Ammount = a.year1Ammount,
                              year2Ammount = a.year2Ammount,
                              year3Ammount = a.year3Ammount,
                              year4Ammount = a.year4Ammount,
                              year5Ammount = a.year5Ammount,
                              fk_adjustment_code = a.fk_adjustment_code,
                              adjustmentCodeName = a.adjustmentCodeName,
                              fk_alter_code = a.fk_alter_code,
                              alterCodeName = a.alterCodeName,
                              fk_area_id = a.fk_area_id,
                              tag = a.tag,
                              priority = a.priority,
                              long_description = a.long_description,
                              tags = a.tags,
                              description = a.description,
                              fk_key_id = a.fk_key_id,
                              isParkedAction = a.isParkedAction
                          }).ToList();

            List<freedimDefinition> freeDims = await _utility.GetFreeDimDefinitionsBasedOnPageIdAsync(userID, string.Empty);
            List<freedimDefinition> freeDimColumns = freeDims.ToList();

            Dictionary<string, clsLanguageString> langStringValuesFP =
                await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "FinancialPlan");
            gmd_action_types actionTypeDesc =
                await tenantDbContext.gmd_action_types.FirstOrDefaultAsync(
                    x => x.pk_language == userDetails.language_preference && x.pk_action_type == 60);

            JObject actions = new JObject();
            actions.Add("actionId", actionId);
            actions.Add("actionDescription", lst.Count == 0 ? string.Empty : (lst.FirstOrDefault()).actionDescription);
            actions.Add("isParkedAction", lst.Any() ? lst.FirstOrDefault().isParkedAction : lst.Any());
            actions.Add("display_description_apendix_flag", false);
            actions.Add("groupName", actionTypeDesc == null ? "" : actionTypeDesc.action_type_descr);
            actions.Add("fk_area_id", lst.Count == 0 ? 0 : (lst.FirstOrDefault()).fk_area_id);
            actions.Add("priority", lst.Count == 0 ? 0 : (lst.FirstOrDefault()).priority);
            actions.Add("long_description", lst.Count == 0 ? 0 : (lst.FirstOrDefault()).long_description);
            actions.Add("groupId", "60");

            JArray nonActiveChangeColumns = new JArray();
            JArray activeChangeColumns = new JArray();

            activeChangeColumns.Add(GenerateActionDetailColumnObject(" ", "id", false, true, new JObject(){ { "display", "none" } }, new JObject() { { "display", "none" } }, null, null, null, true));
            activeChangeColumns.Add(GenerateActionDetailColumnObject(" ", "guid", false, true, new JObject() { { "display", "none" } }, new JObject() { { "display", "none" } }, null, null, null, true));

            activeChangeColumns.Add(GenerateActionDetailColumnObject(((langStringValues.FirstOrDefault(v => v.Key == "account_text")).Value).LangText, "account", false, false, new JObject() { { "border-left", "none" }, { "text-align", "left" }, { "width", "137px" } }, new JObject() { { "border-left", "none" }, { "text-align", "left" }, { "width", "137px" } },
                null,
                "<span title = '#=account.accountText#' >#=account.accountValue#</span>",
                "<span id='financingAccount'>" + ((langStringValues.FirstOrDefault(v => v.Key == "account_text")).Value).LangText + "</span><span class='red'>*</span>", true));

            activeChangeColumns.Add(GenerateActionDetailColumnObject(((langStringValues.FirstOrDefault(v => v.Key == "department_text")).Value).LangText, "department", false, false, new JObject() { { "border-left", "none" }, { "text-align", "left" }, { "width", "137px" } }, new JObject() { { "border-left", "none" }, { "text-align", "left" }, { "width", "137px" } },
                null,
                "<span title = '#=department.departmentText#' >#=department.departmentValue#</span>",
                "<span id='financingDepartment'>" + ((langStringValues.FirstOrDefault(v => v.Key == "department_text")).Value).LangText + "</span><span class='red'>*</span>", true));

            activeChangeColumns.Add(GenerateActionDetailColumnObject(((langStringValues.FirstOrDefault(v => v.Key == "function_text")).Value).LangText, "functionn", false, false, new JObject() { { "border-left", "none" }, { "text-align", "left" }, { "width", "137px" } }, new JObject() { { "border-left", "none" }, { "text-align", "left" }, { "width", "137px" } },
                null,
                "<span title = '#=functionn.functionText#' >#=functionn.functionValue#</span>",
                "<span id='financingFunction'>" + ((langStringValues.FirstOrDefault(v => v.Key == "function_text")).Value).LangText + "</span><span class='red'>*</span>", true));

            activeChangeColumns.Add(GenerateActionDetailColumnObject(((langStringValues.FirstOrDefault(v => v.Key == "fp_project_text")).Value).LangText, "project", false, false, new JObject() { { "border-left", "none" }, { "text-align", "left" }, { "width", "137px" } }, new JObject() { { "border-left", "none" }, { "text-align", "left" }, { "width", "137px" } },
                null,
                "<span title = '#=project.project_name#' >#=project.fk_project_code#</span>",
                "<span>" + ((langStringValues.FirstOrDefault(v => v.Key == "fp_project_text")).Value).LangText + "</span>", true));

            foreach (var freedim in freeDimColumns)
            {
                if (freedim.freeDimColumn == "free_dim_1")
                {
                    activeChangeColumns.Add(GenerateActionDetailColumnObject(freedim.freeDimHeader, "freeDim" + 1.ToString()
                        , false, false, new JObject() { { "border-left", "none" }, { "text-align", "left" }, { "width", "137px" } }, new JObject() { { "border-left", "none" }, { "text-align", "left" }, { "width", "137px" } }, null,
                        "<span title = '#=freeDim" + 1.ToString() + ".freedim_name#' >#=freeDim" + 1.ToString() + ".fk_freedim_code#</span>",
                        "<span id='financingFreeDim" + 1.ToString() + "'>" + freedim.freeDimHeader.ToString() + "</span>",
                      true));
                }

                if (freedim.freeDimColumn == "free_dim_2")
                {
                    activeChangeColumns.Add(GenerateActionDetailColumnObject(freedim.freeDimHeader, "freeDim" + 2.ToString()
                        , false, false, new JObject() { { "border-left", "none" }, { "text-align", "left" }, { "width", "137px" } }, new JObject() { { "border-left", "none" }, { "text-align","left" }, { "width", "137px" } }, null,
                        "<span title = '#=freeDim" + 2.ToString() + ".freedim_name#' >#=freeDim" + 2.ToString() + ".fk_freedim_code#</span>",
                        "<span id='financingFreeDim" + 2.ToString() + "'>" + freedim.freeDimHeader.ToString() + "</span>",
                      true));
                }

                if (freedim.freeDimColumn == "free_dim_3")
                {
                    activeChangeColumns.Add(GenerateActionDetailColumnObject(freedim.freeDimHeader, "freeDim" + 3.ToString()
                        , false, false, new JObject() { { "border-left", "none" }, { "text-align", "left" }, { "width", "137px" } }, new JObject() { { "border-left", "none" }, { "text-align", "left" }, { "width", "137px" } }, null,
                        "<span title = '#=freeDim" + 3.ToString() + ".freedim_name#' >#=freeDim" + 3.ToString() + ".fk_freedim_code#</span>",
                        "<span id='financingFreeDim" + 3.ToString() + "'>" + freedim.freeDimHeader.ToString() + "</span>",
                      true));
                }

                if (freedim.freeDimColumn == "free_dim_4")
                {
                    activeChangeColumns.Add(GenerateActionDetailColumnObject(freedim.freeDimHeader, "freeDim" + 4.ToString()
                        , false, false, new JObject() { { "border-left", "none" }, { "text-align", "left" }, { "width", "137px" } }, new JObject() { { "border-left", "none" }, { "text-align", "left" }, { "width", "137px" } }, null,
                        "<span title = '#=freeDim" + 4.ToString() + ".freedim_name#' >#=freeDim" + 4.ToString() + ".fk_freedim_code#</span>",
                        "<span id='financingFreeDim" + 4.ToString() + "'>" + freedim.freeDimHeader.ToString() + "</span>",
                      true));
                }
            }
            if (lstAjstmt.Count > 1)
            {
                activeChangeColumns.Add(GenerateActionDetailColumnObject(((langStringValuesFP.FirstOrDefault(v => v.Key == "fp_adjustment_code")).Value).LangText, "adjustmentCode", false, false, new JObject() { { "border-left", "none" }, { "text-align", "left" }, { "width", "137px" } }, new JObject() { { "border-left", "none" }, { "text-align", "left" }, { "width", "137px" } },
                null,
                "<span title = '#=adjustmentCode.value#' >#=adjustmentCode.key#</span>",
                "<span id='financingAdjCode'>" + ((langStringValuesFP.FirstOrDefault(v => v.Key == "fp_adjustment_code")).Value).LangText,
                true));
            }
            if (lstAlter.Count > 1)
            {
                activeChangeColumns.Add(GenerateActionDetailColumnObject(((langStringValuesFP.FirstOrDefault(v => v.Key == "fp_alter_code_text")).Value).LangText, "alterCode", false, false, new JObject() { { "border-left", "none" }, { "text-align", "left" }, { "width", "137px" } }, new JObject() { { "border-left", "none" }, { "text-align", "left" }, { "width", "137px" } }, null,
                "<span title='#=alterCode.value#'>#=alterCode.key#</span>",
                "<span id='financingAlterCode'>" + ((langStringValuesFP.FirstOrDefault(v => v.Key == "fp_alter_code_text")).Value).LangText + "</span><span class='red'>*</span>", true));
            }
            activeChangeColumns.Add(GenerateActionDetailColumnObject(budgetYear.ToString(), "year1", false, false, new JObject() { { "border-left", "none" }, { "text-align", "right" }, { "width", "90px" } }, new JObject() { { "border-left", "none" }, { "text-align", "right" }, { "width", "90px" } }, null,
                "#= (year1 == null) ? '0' :  kendo.toString(year1, 'n0')  #",
                "<span id='MODULESYear1'>" + budgetYear.ToString() + "</span>", true));
            activeChangeColumns.Add(GenerateActionDetailColumnObject((budgetYear + 1).ToString(), "year2", false, false, new JObject() { { "border-left", "none" }, { "text-align", "right" }, { "width", "90px" } }, new JObject() { { "border-left", "none" }, { "text-align", "right" }, { "width", "90px" } }, null,
                "#= (year2 == null) ? '0' :  kendo.toString(year2, 'n0')  #",
                "<span id='MODULESYear2'>" + (budgetYear + 1).ToString() + "</span>", true));
            activeChangeColumns.Add(GenerateActionDetailColumnObject((budgetYear + 2).ToString(), "year3", false, false, new JObject() { { "border-left", "none" }, { "text-align", "right" }, { "width", "90px" } }, new JObject() { { "border-left", "none" }, { "text-align", "right" }, { "width", "90px" } }, null,
                "#= (year3 == null) ? '0' :  kendo.toString(year3, 'n0')  #",
                "<span id='MODULESYear3'>" + (budgetYear + 2).ToString() + "</span>", true));
            activeChangeColumns.Add(GenerateActionDetailColumnObject((budgetYear + 3).ToString(), "year4", false, false, new JObject() { { "border-left", "none" }, { "text-align", "right" }, { "width", "90px" } }, new JObject() { { "border-left", "none" }, { "text-align", "right" }, { "width", "90px" } }, null,
                "#= (year4 == null) ? '0' :  kendo.toString(year4, 'n0')  #",
                "<span id='MODULESYear4'>" + (budgetYear + 3).ToString() + "</span>", true));
            activeChangeColumns.Add(GenerateActionDetailColumnObject((budgetYear + 4).ToString(), "year5", false, false, new JObject() { { "border-left", "none" }, { "text-align", "right" }, { "width", "90px" } }, new JObject() { { "border-left", "none" }, { "text-align", "right" }, { "width", "90px" } }, null,
                "#= (year5 == null) ? '0' :  kendo.toString(year5, 'n0')  #",
                "<span id='MODULESYear5'>" + (budgetYear + 4).ToString() + "</span>", true));

            activeChangeColumns.Add(GenerateActionDetailColumnObject(langStringValues.FirstOrDefault(v => v.Key == "CAB_description").Value.LangText, "description", false, false, new JObject() { { "border-left", "none" }, { "text-align", "left" }, { "width", "90px" } }, new JObject() { { "border-left", "none" }, { "text-align", "left" }, { "width", "90px" } },
                null,
                "#= (description == '') ? '" + langStringValuesMonRep.FirstOrDefault(v => v.Key == "PLHDR_Enter_Description").Value.LangText + "':description#",
                "<span>" + langStringValues.FirstOrDefault(v => v.Key == "CAB_description").Value.LangText + "</span>", true));

            activeChangeColumns.Add(GenerateActionDetailColumnObject(((langStringValuesMonRep.FirstOrDefault(v => v.Key == "PBA_periodic_key_text")).Value).LangText, "periodicKey", false, false, new JObject() { { "border-left", "none" }, { "text-align", "left" }, { "width", "90px" } }, new JObject() { { "border-left", "none" }, { "text-align", "left" }, { "width", "90px" } },
            null,
            "<span title = '#=periodicKey.value#' >#=periodicKey.value#</span>",
            "<span id='periodicKeyCode'>" + ((langStringValuesMonRep.FirstOrDefault(v => v.Key == "PBA_periodic_key_text")).Value).LangText + "</span><span class='red' id='commonPeriodicKeyMandatory'></span>",
            true));

            actions.Add("nonActiveChangeColumns", nonActiveChangeColumns);
            actions.Add("activeChangeColumns", activeChangeColumns);

            JArray nonActiveChangeData = new JArray();
            actions.Add("nonActiveChangeData", nonActiveChangeData);

            List<int> TagIds = new List<int>();
            if (lst.Count() > 0 && !string.IsNullOrEmpty((lst.FirstOrDefault()).tags))
            {
                TagIds = (lst.FirstOrDefault()).tags.Split(',').Select(int.Parse).ToList();
            }
            actions.Add("tags", JToken.FromObject(TagIds));

            JObject defaultAccountingInfo = await _consequenceAdjustBudget.GetDefaultsForBudgetChangeAsync(60, userID,
                orgId, serviceId, "MontlyReport", budgetYear);

            actions.Add("defaultModel", (JArray)defaultAccountingInfo["gridDatabdtchange"]);

            JArray activeChangeData = new JArray();
            decimal year1total = 0;
            decimal year2total = 0;
            decimal year3total = 0;
            decimal year4total = 0;
            decimal year5total = 0;

            JObject ActionDetailsTotalbdtchange = new JObject();
            foreach (var a in result)
            {
                JObject ActionDetails = new JObject();
                ActionDetails.Add("id", a.pk_id);

                JObject accountDetails = new JObject();
                accountDetails.Add("accountText", a.accountCode + "-" + a.accountName);
                accountDetails.Add("accountValue", a.accountCode);
                ActionDetails.Add("account", accountDetails);

                JObject deptDetails = new JObject();
                deptDetails.Add("departmentText", a.deptCode + "-" + a.deptName);
                deptDetails.Add("departmentValue", a.deptCode);
                ActionDetails.Add("department", deptDetails);

                JObject funcDetails = new JObject();
                funcDetails.Add("functionText", a.functionCode + "-" + a.functionName);
                funcDetails.Add("functionValue", a.functionCode);
                ActionDetails.Add("functionn", funcDetails);

                JObject projDetails = new JObject();
                projDetails.Add("fk_project_code", a.projectCode);
                projDetails.Add("project_name", a.projectCode + "-" + a.projectName);
                ActionDetails.Add("project", projDetails);

                if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_1") != null)
                {
                    JObject freeDim1Details = new JObject();
                    freeDim1Details.Add("fk_freedim_code", a.freedimCode1);
                    freeDim1Details.Add("freedim_name", a.freedimCode1 + "-" + a.freedimName1);
                    ActionDetails.Add("freeDim1", freeDim1Details);
                }
                if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_2") != null)
                {
                    JObject freeDim2Details = new JObject();
                    freeDim2Details.Add("fk_freedim_code", a.freedimCode2);
                    freeDim2Details.Add("freedim_name", a.freedimCode2 + "-" + a.freedimName2);
                    ActionDetails.Add("freeDim2", freeDim2Details);

                }
                if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_3") != null)
                {
                    JObject freeDim3Details = new JObject();
                    freeDim3Details.Add("fk_freedim_code", a.freedimCode3);
                    freeDim3Details.Add("freedim_name", a.freedimCode3 + "-" + a.freedimName3);
                    ActionDetails.Add("freeDim3", freeDim3Details);
                }
                if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_4") != null)
                {
                    JObject freeDim4Details = new JObject();
                    freeDim4Details.Add("fk_freedim_code", a.freedimCode4);
                    freeDim4Details.Add("freedim_name", a.freedimCode4 + "-" + a.freedimName4);
                    ActionDetails.Add("freeDim4", freeDim4Details);
                }

                if (lstAjstmt.Count > 1)
                {
                    ActionDetails.Add("adjustmentCode", GenerateDynamicAdjustmentCodeObject(a.fk_adjustment_code, a.adjustmentCodeName));
                }
                if (lstAlter.Count > 1)
                {
                    ActionDetails.Add("alterCode", GenerateDynamicAdjustmentCodeObject(a.fk_alter_code, a.alterCodeName));
                }

                ActionDetails.Add("year1", a.year1Ammount.Value / 1000);
                ActionDetails.Add("year2", a.year2Ammount.Value / 1000);
                ActionDetails.Add("year3", a.year3Ammount.Value / 1000);
                ActionDetails.Add("year4", a.year4Ammount.Value / 1000);
                ActionDetails.Add("year5", a.year5Ammount.Value / 1000);

                year1total = year1total + (a.year1Ammount.Value / 1000);
                year2total = year2total + (a.year2Ammount.Value / 1000);
                year3total = year3total + (a.year3Ammount.Value / 1000);
                year4total = year4total + (a.year4Ammount.Value / 1000);
                year5total = year5total + (a.year5Ammount.Value / 1000);

                ActionDetails.Add("description", a.description == null ? string.Empty : a.description);

                JObject pdKeyDetails = new JObject();
                pdKeyDetails.Add("key", a.fk_key_id.ToString());
                var pdKeyVal = (await _utility.GetTenantDBContextAsync()).tco_periodic_key.FirstOrDefault(x => x.key_id.ToString() == a.fk_key_id.ToString());
                pdKeyDetails.Add("value", pdKeyVal != null ? pdKeyVal.key_id + "-" + pdKeyVal.key_description :
                    a.fk_key_id == 0 ? a.fk_key_id.ToString() + "-" + (langStringValuesYBtype["YB_budget_entries_zero_periodic_key_text"]).LangText : "");
                ActionDetails.Add("periodicKey",pdKeyDetails);

                activeChangeData.Add(ActionDetails);
            }

            if ((actionId == 0))
            {
                JObject defaultObj = (JObject)(defaultAccountingInfo["gridDatabdtchange"])[0];
                JObject ActionDetailsWithBudgetChange = new JObject();
                ActionDetailsWithBudgetChange.Add("id", 0);
                ActionDetailsWithBudgetChange.Add("guid", Guid.NewGuid());
                ActionDetailsWithBudgetChange.Add("account", GenerateDynamicAccountObject((string)defaultObj["account"]["accountValue"],
                        (string)defaultObj["account"]["accountValue"]));
                ActionDetailsWithBudgetChange.Add("department", GenerateDynamicAccountObject((string)defaultObj["department"]["departmentValue"],
                        (string)defaultObj["department"]["departmentValue"]));
                ActionDetailsWithBudgetChange.Add("functionn", GenerateDynamicAccountObject((string)defaultObj["functionn"]["functionValue"],
                        (string)defaultObj["functionn"]["functionValue"]));
                ActionDetailsWithBudgetChange.Add("project", GenerateDynamicAccountObject((string)defaultObj["project"]["fk_project_code"],
                        (string)defaultObj["project"]["fk_project_code"]));


                if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_1") != null)
                {
                    ActionDetailsWithBudgetChange.Add("freeDim1", GenerateDynamicAccountObject((string)defaultObj["freeDim1"]["fk_freedim_code"],
                        (string)defaultObj["freeDim1"]["fk_freedim_code"]));
                }
                if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_2") != null)
                {
                    ActionDetailsWithBudgetChange.Add("freeDim2", GenerateDynamicAccountObject((string)defaultObj["freeDim2"]["fk_freedim_code"],
                         (string)defaultObj["freeDim2"]["fk_freedim_code"]));
                }
                if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_3") != null)
                {
                    ActionDetailsWithBudgetChange.Add("freeDim3", GenerateDynamicAccountObject((string)defaultObj["freeDim3"]["fk_freedim_code"],
                        (string)defaultObj["freeDim3"]["fk_freedim_code"]));
                }
                if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_4") != null)
                {
                    ActionDetailsWithBudgetChange.Add("freeDim4", GenerateDynamicAccountObject((string)defaultObj["freeDim4"]["fk_freedim_code"],
                        (string)defaultObj["freeDim4"]["fk_freedim_code"]));
                }

                if (lstAjstmt.Count > 1)
                {
                    ActionDetailsWithBudgetChange.Add("adjustmentCode",GenerateDynamicAdjustmentCodeObject((string)defaultObj["adjustmentCode"]["key"],
                            (string)defaultObj["adjustmentCode"]["key"]));
                }
                if (lstAlter.Count > 1)
                {
                    JObject alterDetails = new JObject();
                    alterDetails.Add("key", (string)defaultObj["alterCode"]["key"]);
                    alterDetails.Add("value", (string)defaultObj["alterCode"]["value"]);
                    ActionDetailsWithBudgetChange.Add("alterCode", alterDetails);
                }

                ActionDetailsWithBudgetChange.Add("year1", 0);
                ActionDetailsWithBudgetChange.Add("year2", 0);
                ActionDetailsWithBudgetChange.Add("year3", 0);
                ActionDetailsWithBudgetChange.Add("year4", 0);
                ActionDetailsWithBudgetChange.Add("description", string.Empty);

                string defaultKeyValue = await _utility.GetParameterValueAsync(userID, "DEFAULT_BUDGET_PER_KEY");

                JObject defaultPeriodicKey = new JObject();
                defaultPeriodicKey.Add("key", defaultKeyValue);
                var tcoPeriodicKey = (await _utility.GetTenantDBContextAsync()).tco_periodic_key.FirstOrDefault(x => x.key_id.ToString() == defaultKeyValue);

                if (tcoPeriodicKey != null)
                {
                    defaultPeriodicKey.Add("value", defaultKeyValue + "-" + tcoPeriodicKey.key_description);
                }

                ActionDetailsWithBudgetChange.Add("periodicKey", defaultPeriodicKey);

                activeChangeData.Add(ActionDetailsWithBudgetChange);
            }

            ActionDetailsTotalbdtchange.Add("id", "total");

            JObject accDetails = new JObject();
            accDetails.Add("accountText", langStringValues.FirstOrDefault(v => v.Key == "cmn_title_total").Value.LangText);
            accDetails.Add("accountValue", "");

            JObject accDetailsfinaltotal = new JObject();
            accDetailsfinaltotal.Add("accountText", langStringValues.FirstOrDefault(v => v.Key == "total_incl_curr_bud_chg").Value.LangText);
            accDetailsfinaltotal.Add("accountValue", "");
            ActionDetailsTotalbdtchange.Add("account", accDetails);
            ActionDetailsTotalbdtchange.Add("department", GenerateDynamicDepartmentObject("", ""));
            ActionDetailsTotalbdtchange.Add("functionn", GenerateDynamicFunctionObject("", ""));
            ActionDetailsTotalbdtchange.Add("project", GenerateDynamicProjectDimObject("", ""));

            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_1") != null)
            {
                ActionDetailsTotalbdtchange.Add("freeDim1", GenerateDynamicFreeDimObject("", ""));
            }
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_2") != null)
            {
                ActionDetailsTotalbdtchange.Add("freeDim2", GenerateDynamicFreeDimObject("", ""));
            }
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_3") != null)
            {
                ActionDetailsTotalbdtchange.Add("freeDim3", GenerateDynamicFreeDimObject("", ""));
            }
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_4") != null)
            {
                ActionDetailsTotalbdtchange.Add("freeDim4", GenerateDynamicFreeDimObject("", ""));
            }

            if (lstAjstmt.Count > 1)
            {
                ActionDetailsTotalbdtchange.Add("adjustmentCode", GenerateDynamicAdjustmentCodeObject("", ""));
            }

            if (lstAlter.Count > 1)
            {
                ActionDetailsTotalbdtchange.Add("alterCode", GenerateDynamicAlterCodeObject("", ""));
            }

            actions.Add("activeChangeData", activeChangeData);

            ActionDetailsTotalbdtchange.Add("year1", year1total);
            ActionDetailsTotalbdtchange.Add("year2", year2total);
            ActionDetailsTotalbdtchange.Add("year3", year3total);
            ActionDetailsTotalbdtchange.Add("year4", year4total);
            ActionDetailsTotalbdtchange.Add("year5", year5total);

            ActionDetailsTotalbdtchange.Add("description", "");

            JObject totpdKeyDetails = new JObject();
            totpdKeyDetails.Add("key", "");
            totpdKeyDetails.Add("value", "");
            ActionDetailsTotalbdtchange.Add("periodicKey", totpdKeyDetails);

            JArray totalsData = new JArray();
            totalsData.Add(ActionDetailsTotalbdtchange);

            actions.Add("totalsData", totalsData);

            return actions;
        }

        private async Task<JArray> GetBudgetAdjustmentColumns(string userName,Stream? configStream, int budgetYear, string isSamEnabled, bool isChapterSetup, bool isServiceSetup)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userName);
            Assembly assembly = Assembly.GetExecutingAssembly();
            Dictionary<string, clsLanguageString> langStr = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "MonthlyReport");
            JArray colConfig = new JArray();
            if (configStream != null)
            {
                StreamReader reader = new StreamReader(configStream);
                string config = await reader.ReadToEndAsync();
                colConfig = JArray.Parse(config);
            }
            JArray updatedColConfig = new JArray();
            foreach (var col in colConfig)
            {
                switch ((string)col["field"])
                {
                    case "select":
                        col["title"] = langStr["MR_select_action"].LangText;
                        updatedColConfig.Add(col);
                        break;
                    case "actionName":
                        col["title"] = langStr["MR_action_name"].LangText;
                        updatedColConfig.Add(col);
                        break;
                    case "serviceName":
                        if (isServiceSetup) 
                        { 
                           col["title"] = langStr["BusPlan_Col_servicename"].LangText;
                           updatedColConfig.Add(col); 
                        }
                        break;
                    case "chapter":
                        if (isChapterSetup)
                        {
                            col["title"] = langStr["MR_AllChapterTitle"].LangText;
                            updatedColConfig.Add(col);
                        }
                        break;
                    case "orgName":
                        col["title"] = langStr["MR_org_createdAt"].LangText;
                        updatedColConfig.Add(col);
                        break;
                    case "actionStatus":
                        col["title"] = langStr["MR_action_status"].LangText;
                        updatedColConfig.Add(col);
                        break;
                    case "tags":
                        col["title"] = langStr["BusPlan_Col_tag"].LangText;
                        updatedColConfig.Add(col);
                        break;
                    case "type":
                        col["title"] = langStr["MR_Action_Type"].LangText;
                        updatedColConfig.Add(col);
                        break;
                    case "description":
                        col["title"] = langStr["MR_action_desc"].LangText;
                        updatedColConfig.Add(col);
                        break;
                    case "log":
                        col["title"] = langStr["MR_action_log"].LangText;
                        updatedColConfig.Add(col);
                        break;
                    case "year1":
                        col["title"] = budgetYear.ToString();
                        updatedColConfig.Add(col);
                        break;
                    case "year2":
                        col["title"] = (budgetYear + 1).ToString();
                        updatedColConfig.Add(col);
                        break;
                    case "year3":
                        col["title"] = (budgetYear + 2).ToString();
                        updatedColConfig.Add(col);
                        break;
                    case "year4":
                        col["title"] = (budgetYear + 3).ToString();
                        updatedColConfig.Add(col);
                        break;
                    case "year5":
                        col["title"] = (budgetYear + 4).ToString(); 
                        updatedColConfig.Add(col);
                        break;
                    case "isSamAction":
                        if (isSamEnabled.ToLower() == "true")
                        {
                            col["title"] = langStr["MR_sam_action"].LangText;
                            updatedColConfig.Add(col);
                        }
                        break;
                    case "delete":
                        col["title"] = langStr["MR_action_delete"].LangText;
                        updatedColConfig.Add(col);
                        break;
                }

            }
            return updatedColConfig;
        }
    }
}