/* externally added styles */
/*@import url('style-responsive.css');*/

@font-face {
    font-family: semiFont;
    src: url('../open_fonts/OpenSans-SemiBold.woff') format("woff");
}

@font-face {
    font-family: openBoldFont;
    src: url('../open_fonts/OpenSans-Bold.woff') format("woff");
}

@font-face {
    font-family: regularFont;
    src: url('../open_fonts/OpenSans-Regular.woff') format("woff");
}

html {
    width: 1px;
    min-width: 100%;
    font-size: 100% !important;
}

body {
    -webkit-font-smoothing: subpixel-antialiased;
    zoom: 1;
}

.wrapper-container {
    position: relative;
    z-index: 0;
}

.padding1em {
    padding: 0.1em;
}

/*COMMON FONT STYLING STARTS*/

.font40 {
    font-size: 40px;
}

.font30 {
    font-size: 30px;
    font-family: openBoldFont, sans-serif;
}

.font26 {
    font-size: 26px;
}

.font24 {
    font-size: 24px;
}

.font22 {
    font-size: 22px;
}

.font20 {
    font-size: 20px;
}

.font17 {
    font-size: 17px;
}

.font16 {
    font-size: 16px !important;
}

.font15 {
    font-size: 15px;
}

.font14 {
    font-size: 14px !important;
}

.font13 {
    font-size: 13px !important;
}

.font12 {
    font-size: 12px !important;
}

.font11 {
    font-size: 11px !important;
}

.font10 {
    font-size: 10px !important;
}

.font18 {
    font-size: 18px !important;
}

.font-italic {
    font-style: italic;
}

.bold {
    font-weight: bold;
}

.grey-color {
    color: #767676;
}

.normal-font {
    font-weight: normal;
}

/*COMMON FONT STYLING END*/


/*COMMON BORDER STYLING STARTS*/

.border-none,
.border0,
.no-border {
    border: 0 none !important;
    box-shadow: none;
}

.border-top-white {
    border-top: 1px solid white;
}

.border1 {
    border: 1px solid #c3c3c3 !important;
}

.border2 {
    border: 2px solid #606060;
}

.border3 {
    border: 1px solid #D28E06 !important;
}

.border-bottom-none {
    border-bottom: none !important;
}

.border-radius0 {
    border-radius: 0px !important;
}

.border-top0 {
    border-top: 0 none !important;
}

.border-dashed {
    border: 1px dashed #e5e5e5;
}

/*COMMON BORDER STYLING END*/

.bottom-minus5 {
    bottom: -5px;
}

.bottom-minus10 {
    bottom: -10px;
}

.bottom0 {
    bottom: 0px !important;
}

.bottom2 {
    margin-bottom: 2px;
}

.bottom4 {
    margin-bottom: 4px;
}

.bottom5 {
    margin-bottom: 5px;
}

.bottom7 {
    margin-bottom: 7px;
}

.bottom10 {
    margin-bottom: 10px;
}

.bottom12 {
    margin-bottom: 12px;
}

.bottom15 {
    margin-bottom: 15px;
}

.bottom20 {
    margin-bottom: 20px;
}

.bottom30 {
    margin-bottom: 30px;
}

.bottom35 {
    margin-bottom: 35px;
}

.bottom40 {
    margin-bottom: 40px;
}

.bottom50 {
    margin-bottom: 50px;
}

.padding0 {
    padding: 0px !important;
}

.padding1 {
    padding: 1px;
}

.padding2 {
    padding: 2px;
}

.padding3 {
    padding: 3px;
}

.padding4 {
    padding: 4px;
}

.padding5 {
    padding: 5px;
}

.padding7 {
    padding: 7px;
}

.padding10 {
    padding: 10px;
}

.padding15 {
    padding: 15px;
}

.padding20 {
    padding: 20px !important;
}

.padding30 {
    padding: 30px !important;
}

.padding50 {
    padding: 50px;
}

.padding-top0 {
    padding-top: 0px !important;
}

.padding-top2 {
    padding-top: 2px !important;
}

.padding-top3 {
    padding-top: 3px !important;
}

.padding-top4 {
    padding-top: 4px !important;
}

.padding-top5 {
    padding-top: 5px !important;
}

.padding-top7 {
    padding-top: 7px !important;
}

.padding-top8 {
    padding-top: 8px !important;
}

.padding-top10 {
    padding-top: 10px !important;
}

.padding-top16 {
    padding-top: 16px !important;
}

.padding-top12 {
    padding-top: 12px;
}

.padding-top15 {
    padding-top: 15px !important;
}

.padding-left4 {
    padding-left: 4px !important;
}

.padding-left13 {
    padding-left: 13px;
}

.padding-left18 {
    padding-left: 18px !important;
}

.padding-left25 {
    padding-left: 25px !important;
}

.padding-left26 {
    padding-left: 26px !important;
}

.padding-left30 {
    padding-left: 30px !important;
}

.padding-left35 {
    padding-left: 35px !important;
}

.padding-left57 {
    padding-left: 57px !important;
}

.padding-top20 {
    padding-top: 20px !important;
}

.padding-top30 {
    padding-top: 30px !important;
}

.padding-top35 {
    padding-top: 35px !important;
}

.padding-bottom5 {
    padding-bottom: 5px !important;
}

.padding-bottom0 {
    padding-bottom: 0px !important;
}

.padding-bottom2 {
    padding-bottom: 2px !important;
}

.padding-bottom3 {
    padding-bottom: 4px !important;
}

.padding-bottom7 {
    padding-bottom: 7px !important;
}

.padding-bottom8 {
    padding-bottom: 8px !important;
}

.padding-bottom10 {
    padding-bottom: 10px !important;
}

.padding-bottom12 {
    padding-bottom: 12px !important;
}

.padding-bottom15 {
    padding-bottom: 15px !important;
}

.padding-bottom20 {
    padding-bottom: 20px !important;
}

.padding-bottom30 {
    padding-bottom: 30px !important;
}

.padding-bottom50 {
    padding-bottom: 50px !important;
}

.padding-top40 {
    padding-top: 40px !important;
}

.padding-top60 {
    padding-top: 60px !important;
}

.padding-left0 {
    padding-left: 0px !important;
}

.padding-left5 {
    padding-left: 5px !important;
}

.padding-left6 {
    padding-left: 6px !important;
}

.padding-left7 {
    padding-left: 7px !important;
}

.padding-left10 {
    padding-left: 10px !important;
}

.padding-left12 {
    padding-left: 12px !important;
}

.padding-right0 {
    padding-right: 0px !important;
}

.padding-right5 {
    padding-right: 5px !important;
}

.padding-right15 {
    padding-right: 15px !important;
}

.padding-left2 {
    padding-left: 2px !important;
}

.padding-left1 {
    padding-left: 1px !important;
}

.padding-left15 {
    padding-left: 15px !important;
}

.padding-left16 {
    padding-left: 16px !important;
}

.padding-left20 {
    padding-left: 20px !important;
}

.padding-left45 {
    padding-left: 45px !important;
}

.padding-left50 {
    padding-left: 50px !important;
}

.padding-left62 {
    padding-left: 62px !important;
}

.padding-left66 {
    padding-left: 66px !important;
}

.padding-left70 {
    padding-left: 70px !important;
}

.padding-left74 {
    padding-left: 74px !important;
}

.padding-left140 {
    padding-left: 140px !important;
}

.padding-left98 {
    padding-left: 98px !important;
}

.padding-left-10pr {
    padding-left: 10% !important;
}

.padding-left8 {
    padding-left: 8px !important;
}

.padding-right3 {
    padding-right: 3px !important;
}

.padding-right10 {
    padding-right: 10px !important;
}

.padding-right40 {
    padding-right: 40px !important;
}

.padding-right20 {
    padding-right: 20px !important;
}

.padding-right24 {
    padding-right: 24px !important;
}

.padding-right25 {
    padding-right: 25px !important;
}

.padding-right30 {
    padding-right: 30px !important;
}

.padding-right32 {
    padding-right: 32px !important;
}

.padding-right50 {
    padding-right: 50px !important;
}

.padding-leftright5 {
    padding-left: 5px !important;
    padding-right: 5px !important;
}

.padding-leftright10 {
    padding-left: 10px !important;
    padding-right: 10px !important;
}

.padding-leftright15 {
    padding-left: 15px !important;
    padding-right: 15px !important;
}

.display-inline-block {
    display: inline-block !important;
}

.bp-last-crumb-item {
    border-bottom: 2px solid #4A8EB9 !important;
    border-top: 5px;
}

.margin0 {
    margin: 0px !important;
}

.margin1 {
    margin: 1px !important;
}

.margin2 {
    margin: 2px;
}

.margin3 {
    margin: 3px;
}

.margin4 {
    margin: 4px;
}

.margin5 {
    margin: 5px;
}

.margin10 {
    margin: 10px;
}

.margin50 {
    margin: 50px;
}

.marginleft0 {
    margin-left: 0px !important;
}

.marginleft1 {
    margin-left: 1px !important;
}

.marginleft2 {
    margin-left: 2px !important;
}

.marginleft3 {
    margin-left: 3px !important;
}

.marginleft5 {
    margin-left: 5px !important;
}

.marginleft6 {
    margin-left: 6px !important;
}

.margin-left8 {
    margin-left: 8px !important;
}

.marginleft10 {
    margin-left: 10px !important;
}

.marginleft12 {
    margin-left: 12px !important;
}

.marginleft15, .margin-left15 {
    margin-left: 15px !important;
}

.marginleft18 {
    margin-left: 18px !important;
}

.marginleft20 {
    margin-left: 20px !important;
}

.marginleft40 {
    margin-left: 40px !important;
}

.marginleft30 {
    margin-left: 30px !important;
}

.margin-left-5 {
    position: relative;
    margin-left: -5px;
}

.marginleft-10 {
    margin-left: -10px !important;
}

.marginleft-35 {
    margin-left: -35px !important;
}

.marginleft75 {
    margin-left: 75px;
}

.marginleft60 {
    margin-left: 60px;
}

.marginleftperc28 {
    margin-left: 28% !important;
}

.marginlefttperc-6 {
    margin-left: -6% !important;
}

.marginrightperc15 {
    margin-right: 15% !important;
}

.marginright5 {
    margin-right: 5px !important;
}

.marginright3 {
    margin-right: 3px !important;
}

.marginright10 {
    margin-right: 10px;
}

.margin-right15 {
    margin-right: 15px !important;
}

.margin-right18 {
    margin-right: 18px !important;
}

.marginright25 {
    margin-right: 25px !important;
}

.marginright70 {
    margin-right: 70px;
}

.margin-left10 {
    margin-left: 10px;
}

.margin-left-6 {
    margin-left: -6px;
}

.margin-left-10 {
    margin-left: -10px;
}

.margin-left-3 {
    margin-left: -3px;
}

.margin-left-8 {
    margin-left: -8px !important;
}

.margin-left20 {
    margin-left: 20px;
}

.marginleft25 {
    margin-left: 25px !important;
}

.margin-top14 {
    margin-top: 14px !important;
}

.margin-top12 {
    margin-top: 12px !important;
}

.margin-top1 {
    margin-top: 1px !important;
}

.margin-top5 {
    margin-top: 5px !important;
}

.margin-top10 {
    margin-top: 10px !important;
}

.margin-top7 {
    margin-top: 7px !important;
}

.margin-top-7 {
    margin-top: -7px;
}

.margin-top-2 {
    margin-top: -2px;
}

.margin-top-3 {
    margin-top: -3px;
}

.margin-top-1 {
    margin-top: -1px !important;
}

.margin-top-4 {
    margin-top: -4px;
}

.marginleft-5 {
    margin-left: -5px;
}

.margin-top-10 {
    margin-top: -10px;
}

.margin-top-20 {
    margin-top: -20px;
}

.margin-top20 {
    margin-top: 20px;
}

.margin-bottom0 {
    margin-bottom: 0px !important;
}

.top2 {
    margin-top: 2px;
}

.top3 {
    margin-top: 3px;
}

.top5 {
    margin-top: 5px;
}

.top6 {
    margin-top: 6px;
}

.top7 {
    margin-top: 7px;
}

.top8,
.position-top8 {
    margin-top: 8px;
}

.top10 {
    margin-top: 10px;
}

.top12 {
    margin-top: 12px;
}

.top15 {
    margin-top: 15px;
}

.top20 {
    margin-top: 20px;
}

.top24 {
    margin-top: 24px;
}

.top25 {
    margin-top: 25px;
}

.top30 {
    margin-top: 30px;
}

.top35 {
    margin-top: 35px;
}

.top40 {
    margin-top: 40px !important;
}

.top48 {
    margin-top: 48px !important;
}

.top50 {
    margin-top: 50px !important;
}

.top55 {
    margin-top: 55px !important;
}

.top60 {
    margin-top: 60px !important;
}

.top137 {
    margin-top: 137px !important;
}

.top70 {
    margin-top: 70px !important;
}

.top28 {
    margin-top: 28px;
}

.top100 {
    margin-top: 100px;
}

.top200 {
    margin-top: 200px;
}

.top400 {
    margin-top: 400px;
}

.top-minus-five {
    top: -5px;
}

.top-minus-20 {
    top: -20px;
}

.bottom75 {
    margin-bottom: 75px;
}

.bottom45 {
    margin-bottom: 45px;
}

.bottom100 {
    margin-bottom: 100px !important;
}

.bg-row-highlight {
    background: rgba(118, 171, 217, 0.43) !important;
}

.left12 {
    margin-left: 12px;
}

.red {
    color: red !important;
}

.green {
    color: green !important;
}

.white {
    color: #fff !important;
}

.width9 {
    width: 9% !important;
}

.width10 {
    width: 10% !important;
}

.width20 {
    width: 20%;
}

.width25 {
    width: 25%;
}

.width30 {
    width: 30%;
}

.width40 {
    width: 40%;
}

.width50 {
    width: 50%;
}

.width95-percent {
    width: 95%;
}

.width100 {
    width: 100% !important;
}

.width120 {
    width: 120% !important;
}

.width95 {
    width: 95px;
}

.width135 {
    width: 135px;
}

.width147 {
    width: 147px;
}

.width1000px {
    width: 1000px;
}

.widthP120 {
    width: 120px;
}

.widthP150 {
    width: 150px;
}

.widthP250 {
    width: 250px;
}

.width-99 {
    width: 99% !important;
}

.white-space-normal {
    white-space: normal !important;
}

.height-auto {
    height: auto !important;
}

.height5 {
    height: 5px !important;
}

.height10 {
    height: 10px !important;
}

.height15 {
    height: 15px !important;
}

.height17 {
    height: 17px !important;
}

.height20 {
    height: 20px !important;
}

.height22 {
    height: 22px !important;
}

.height27 {
    height: 27px !important;
}

.height29 {
    height: 29px !important;
}

.height30 {
    height: 30px !important;
}

.height31 {
    height: 31px !important;
}

.height32 {
    height: 32px !important;
}

.height70 {
    height: 70px !important;
}

.height35 {
    height: 35px !important;
}

.height90 {
    height: 90px !important;
}

.height95 {
    height: 95px !important;
}

.height28 {
    height: 28px !important;
}

.height63 {
    height: 63px !important;
}

.height-inh {
    height: inherit;
}

.width-inh {
    width: inherit;
}


.min-width-85 {
    min-width: 85px !important;
}

.min-width-50 {
    min-width: 50px !important;
}

.min-width-100 {
    min-width: 100px !important;
}

.min-width-150 {
    min-width: 150px !important;
}

.width250 {
    width: 250px !important;
}

.width300 {
    width: 300px !important;
}

.width-100 {
    width: 100px !important;
}

.border-left0 {
    border-left: 0 none !important;
}

.underline {
    text-decoration: underline !important;
}

.underline-color {
    text-decoration-color: #367b9a !important;
}

.text-color1, .plantype-items a {
    color: #266EAD !important;
}

.text-color2 {
    color: #606060 !important;
}

.text-color3 {
    color: #377B9A !important;
}

.action-text-color1 {
    color: #347b9a !important;
}

.cmn-background-blue {
    background: #347b9a !important;
}

.cmn-save-close-btn {
    background: #fff;
    border: 1px solid #266EAD;
    color: #266EAD;
    border-radius: 4px;
}

.cmn-cancel-btn {
    background: transparent;
    color: #347B9A;
    border: none;
    margin-right: 5px;
}

.cmn-save-btn-border {
    border: 2px solid #347B9A;
    font-size: 16px !important;
}

.cmn-display-none,
.d-none{
    display: none !important;
}

.cmn-relative-position {
    position: relative !important;
}

.absolute {
    position: absolute;
}

.blue-box-shadow {
    border-bottom: 1px solid rgba(74, 142, 185, 0.3);
    box-shadow: 0.5px 0px 5px 0px rgba(74,142,185,0.3);
}

.cmn-display-block {
    display: block !important;
}

.cmn-tool-bar {
    background: transparent !important;
    border-width: 1.2px;
    font-weight: bold;
}

.cmn-chkbox-style {
    height: 16px;
    width: 16px;
    vertical-align: middle;
    margin-top: 0 !important;
}

.display-inline-flex {
    display: inline-flex;
}

.disable-anchor {
    pointer-events: none;
    cursor: default;
}

    .disable-anchor a {
        color: #777 !important;
    }

.cmn-box-shadow {
    -webkit-box-shadow: 0px 6px 31px 5px rgba(195,195,195,1);
    -moz-box-shadow: 0px 6px 31px 5px rgba(195,195,195,1);
    box-shadow: 0px 6px 31px 5px rgba(195,195,195,1);
}

.cmn-align-middle {
    vertical-align: middle !important;
}

.color-white-smoke {
    background-color: RGB(244,244,244);
}

.color-white {
    color: #fff;
}

.new-color-blue {
    color: #1B2B6A;
}


.cmn-clear-both {
    clear: both;
}

.width85 {
    width: 85px;
}

.width156 {
    width: 156px;
}

.cmn-width-4p {
    width: 4%;
}

.cmn-width-10p {
    width: 10%;
}

.cmn-width-15p {
    width: 15%;
}

.cmn-width-20p {
    width: 20%;
}

.cmn-width-22p {
    width: 21.5%;
}

.cmn-width-23p {
    width: 23%;
}

.cmn-width-25p {
    width: 25%;
}

.cmn-width-26p {
    width: 26%;
}

.cmn-width-28p {
    width: 28%;
}

.cmn-width-30p {
    width: 30% !important;
}

.cmn-width-40p {
    width: 40% !important;
}

.cmn-width-45p {
    width: 45% !important;
}

.cmn-width-46p {
    width: 46% !important;
}

.cmn-width-50p {
    width: 50%;
}

.cmn-width-55p {
    width: 55%;
}

.cmn-width-auto {
    width: auto !important;
}

.cmn-width-60p {
    width: 60% !important;
}

.cmn-width-63p {
    width: 63% !important;
}

.cmn-width-64p {
    width: 64% !important;
}

.cmn-width-65p {
    width: 65% !important;
}

.cmn-width-68p {
    width: 68% !important;
}

.cmn-width-70p {
    width: 70%;
}

.cmn-width-75p {
    width: 75%;
}

.cmn-width-80p {
    width: 80% !important;
}

.cmn-width-85p {
    width: 85%;
}

.cmn-width-86p {
    width: 86%;
}

.cmn-width-90p {
    width: 90% !important;
}

.cmn-width-91p {
    width: 91% !important;
}

.cmn-width-92p {
    width: 92% !important;
}

.cmn-width-93p {
    width: 93% !important;
}

.cmn-width-94p {
    width: 94%;
}

.cmn-width-95p {
    width: 95%;
}

.cmn-width-96p {
    width: 96% !important;
}

.cmn-width-97p {
    width: 97%;
}

.cmn-width-98p {
    width: 98% !important;
}

.cmn-width-99p {
    width: 98.5% !important;
}

.cmn-width-100 {
    width: 100% !important;
}

.cmn-width-82 {
    width: 82% !important;
}

.cmn-width-12 {
    width: 12% !important;
}

.display-inline{
    display: inline-block;
}

.cmn-width-height {
    height: 19px;
    width: 19px;
    vertical-align: text-bottom;
    padding-left: 7px;
}

.cmn-year-selector-wrapper {
    border-top: 1px solid #6fa1b2;
    border-bottom: 1px solid #6fa1b2;
}

.year-selector {
    background: #fff !important;
    height: 27px !important;
    width: 100%;
    border: 0 none;
}

.year-selector-wrapper {
    border-top: 1px solid #6fa1b2;
    border-bottom: 1px solid #6fa1b2;
    width: 90px;
}

.visibility-hidden,
.hide {
    visibility: hidden !important;
}

.cmn-height {
    height: 100%;
}

.year-selector-width {
    width: 90px;
}

.min-height0 {
    min-height: 0px !important;
}

.min-height10 {
    min-height: 10px;
}

.min-height20 {
    min-height: 20px;
}

.min-height25 {
    min-height: 25px;
}

.min-height30 {
    min-height: 30px;
}

.min-height35 {
    min-height: 35px;
}

.min-height40 {
    min-height: 40px;
}

.min-height42 {
    min-height: 42px;
}

.min-height50 {
    min-height: 50px;
}

.min-height60 {
    min-height: 60px;
}

.min-height75 {
    min-height: 75px;
}

.min-height80 {
    min-height: 80px !important;
}

.min-height85 {
    min-height: 85px;
}

.min-height100 {
    min-height: 100px;
}

.min-height120 {
    min-height: 120px !important;
}
.min-height140 {
    min-height: 140px !important;
}

.min-height150 {
    min-height: 150px !important;
}

.min-height185 {
    min-height: 185px !important;
}

.min-height160 {
    min-height: 160px;
}

.min-height200 {
    min-height: 200px;
}

.min-height210 {
    min-height: 210px;
}

.min-height240 {
    min-height: 240px;
}

.min-height300 {
    min-height: 300px;
}

.min-height320 {
    min-height: 320px;
}

.min-height400 {
    min-height: 400px;
}

.min-height420 {
    min-height: 420px;
}

.min-height500 {
    min-height: 500px;
}

.max-height308 {
    max-height: 308px;
}

.max-height500 {
    max-height: 500px;
}

.max-height600 {
    max-height: 600px;
}

.height100 {
    height: 100px;
}

.height120 {
    height: 120px;
}

.height150 {
    height: 150px;
}

.height200 {
    height: 200px;
}

.height300 {
    height: 300px;
}

.opacity-75 {
    opacity: 0.75 !important;
}

.opacity-1-75 {
    opacity: 1.75 !important;
}

.pleft10 {
    left: 10px;
}

.error-msg {
    color: red;
}

.float-none {
    float: none;
}

.float-right {
    float: right;
}

.float-left {
    float: left !important;
}

.align-left {
    text-align: left !important;
}

.align-right {
    text-align: right !important;
}

.align-center, .monthly-year-center {
    text-align: center !important;
}

.monthly-year-center {
    width: auto;
}

.vertical-align-sub {
    vertical-align: sub;
}

.vertical-align-super {
    vertical-align: super;
}

.vertical-align-initial {
    vertical-align: initial;
}

.act {
    color: #606060 !important;
}

.semi {
    font-family: semiFont, 'Open Sans Semibold', sans-serif !important;
}

.openBold {
    font-family: openBoldFont, 'Open Sans Bold', sans-serif !important;
}

.lighter {
    font-weight: 100;
}

.background-white {
    background-color: #fff !important;
}

.background-blue {
    background-color: #EBF0F7 !important;
}

.background-white-withheight {
    background-color: #fff !important;
    min-height: 100px;
}

.border-white1 {
    border: 1px solid #fff !important;
}

.white-color {
    color: #fff !important;
}

.grid-cell-color, #polsimQADelegateGrid a.k-link, #polsimQAGrid a.k-link, #polsimQAOwnerGrid a.k-link {
    color: #4a8eb9;
}

.border-left1 {
    border-left: 1px solid #c3c3c3 !important;
}

.border-left1-black {
    border-left: 1px solid #505050 !important;
}

.border-white-left1 {
    border-left: 1px solid #c3c3c3 !important;
}

.border-white-right1 {
    border-right: 1px solid #c3c3c3 !important;
}

.border-right1 {
    border-right: 1px solid #c3c3c3 !important;
}

.border-right0 {
    border-right: 0 none !important;
}

.border-highlight {
    border: 1px solid #009fe3;
}


.border-top,
.border-top1 {
    border-top: 1px solid #c3c3c3 !important;
}

.border-bottom {
    border-bottom: 1px solid #c3c3c3 !important;
}

.border-bottom-thick {
    border-bottom: 2px solid #cbc9c9 !important;
}

.border-bottom0 {
    border-bottom: 0 none !important;
}

.border-right {
    border-right: 1px solid #c3c3c3 !important;
}

.border-bottom-c1 {
    border-bottom: 1px solid #367b9a !important;
}

.min-width-1 {
    min-width: 1px !important;
}

.tab-section-minwidth85 {
    min-width: 85px !important;
}

.no-border-top {
    border-top: none !important;
}

.border-right-dotted {
    border-right: 1px dotted #B1B1B1;
}

.border-bottom-dotted {
    border-bottom: 1px dotted #B1B1B1;
}

.border-top-dotted {
    border-top: 1px dotted #B1B1B1 !important;
}

.border-bottom-solid {
    border-bottom: 1px solid #B1B1B1;
}

.border-bottom-grey {
    border-bottom: 1.5px solid #CBC9C9;
}

.border-bottom-solid-light {
    border-bottom: 1px solid #c3c3c3;
}

.border-radius2 {
    border-radius: 2px;
}

.border-radius4 {
    border-radius: 4px;
}

.border-bottom-radius4 {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
}

.border-radius-right4 {
    border-bottom-right-radius: 4px !important;
    border-top-right-radius: 4px !important;
}

.border-radius-left4 {
    border-bottom-left-radius: 4px !important;
    border-top-left-radius: 4px !important;
}

.font-family-regular {
    font-family: regularFont, 'Open Sans Regular', sans-serif !important;
}

.default-border-color {
    border-color: rgb(111, 161, 178) !important;
}

.border {
    border: 1px solid #c3c3c3;
    border-radius: 4px;
}

.cmn-input-border,
#tableCaption:hover {
    border: 1px solid #6FA1B2 !important;
}

.over-auto {
    overflow: auto;
}

.over-hidden {
    overflow: hidden;
}

.opacity1 {
    opacity: 1;
}

.opacity0 {
    opacity: 0.5;
}

.plan-personal-info-warning {
    background-color: antiquewhite;
    margin-right: 10px;
    padding: 8px;
    font-size: 13px;
}

.personal-info-warning {
    background-color: antiquewhite;
    margin-right: 10px;
    padding: 5px;
    font-size: 13px;
}

.hand-pointer {
    cursor: pointer;
}

.cursor-wait {
    cursor: wait !important;
    pointer-events: none;
}

.vertical-align-top {
    vertical-align: top !important;
}

.vertical-align-middle {
    vertical-align: middle !important;
}

.vertical-align-bottom {
    vertical-align: bottom !important;
}

.drag-template-style {
    width: auto;
    background: #fff;
    border-left: 3px solid #367b9a;
    padding: 10px;
}

.vertical-align-text-bottom,
#targetIndicatorDashBoard svg {
    vertical-align: text-bottom !important;
}

.block {
    display: block;
}

.display-flex {
    display: flex !important;
}

.border-radius5 {
    border-radius: 5px;
}

.border-radius30 {
    border-radius: 30px;
}

.border-black-1 {
    border: 1px solid #606060;
}

.no-wrap {
    white-space: nowrap !important;
}

.border-input {
    border: 1px solid #6FA1B2;
    border-radius: 4px;
}

.header-row {
    border-bottom: 1px solid #c3c3c3;
    margin-bottom: 20px;
}

.header-column1 {
    margin-top: 5px;
}

.header-column2 {
    margin: 5px 5px 5px 0px;
    height: 90px;
    padding: 2px 0px 4px 13px;
}

.top-header-chart {
    width: 100%;
    height: 55px;
    padding-right: 8px;
}

.header-bottom-border {
    border-bottom: 1px solid #c3c3c3 !important;
}

.btn-custom {
    width: 20%;
    font-size: 14px;
    padding: 2px;
}

.delete-confirm-yes {
    width: 50px;
    margin-right: 10px;
}

.cancel-btn-role {
    background-color: grey;
}

.delete-confirm-cancel {
    width: 50px;
}

.date-picker-width {
    width: 100% !important;
}

.cursor {
    cursor: pointer;
}

.input-checkBox-up {
    vertical-align: middle;
    margin-top: -2px !important;
    position: relative !important;
}

.hide-class {
    display: none;
}

.show-class {
    display: block;
}

.dotted-line-style {
    border-bottom: 1px dashed #c3c3c3;
    padding-bottom: 8px;
}

.dotted-line-style-black {
    border-bottom: 1px dashed #000000;
    padding-bottom: 8px;
}

.solid-line-style {
    border-bottom: 1px solid #c3c3c3;
    padding-bottom: 8px;
}

.border-left-radius-4 {
    border-top-left-radius: 4px !important;
    border-bottom-left-radius: 4px !important;
}

.border-right-radius-4 {
    border-top-right-radius: 4px !important;
    border-bottom-right-radius: 4px !important;
}

.border-left-radius-0 {
    border-top-left-radius: 0px !important;
    border-bottom-left-radius: 0px !important;
}

.border-right-radius-0 {
    border-top-right-radius: 0px !important;
    border-bottom-right-radius: 0px !important;
}

.default-color {
    background: #fff !important;
    color: #606060 !important;
}

.default-hover-color,
.non-editable-col-bg,
.grey-header-color {
    background: #ebebeb !important;
}

.base-tooltip-popup {
    background-color: #fff !important;
    border: 1px solid #c3c3c3 !important;
    min-height: 50px !important;
}

.banner-background-style {
    background-repeat: no-repeat;
    background-size: 100%;
}

.whitspace-nowrap {
    white-space: nowrap;
}

.input-checkbox-style {
    width: 17px;
    height: 15px;
}

.width137 {
    width: 137% !important;
}

.edit-link {
    position: absolute;
    right: 28px;
    height: 5px;
    background: transparent url('../images/edit-small.png') no-repeat 50% 50%;
    overflow: hidden;
    display: inline-block;
    font-size: 0;
    line-height: 0;
    vertical-align: top;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
}

.delete-link {
    position: absolute;
    right: 7px;
    height: 6px;
    background: transparent url('../images/close_small.png') no-repeat 50% 50%;
    overflow: hidden;
    display: inline-block;
    font-size: 0;
    line-height: 0;
    vertical-align: top;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
}

.edit-icon-link {
    position: absolute;
    right: 28px;
    overflow: hidden;
    display: inline-block;
    font-size: 11px;
    line-height: 1;
    vertical-align: top;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
}

.switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

    .switch input {
        display: none;
    }

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
}

    .slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        right: 3px;
        bottom: 3px;
        background-color: white;
        -webkit-transition: .4s;
        transition: .4s;
    }

input:checked + .slider {
    background-color: #2196F3;
}

input:focus + .slider {
    box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
    -webkit-transform: translateX(22px);
    -ms-transform: translateX(22px);
    transform: translateX(22px);
}

input:checked + .busplan-slider {
    background-color: #228B22;
}

.column-count2 {
    -webkit-column-count: 2;
    -moz-column-count: 2;
    column-count: 2;
    width: 67%;
    column-fill: auto;
}

.column-count3 {
    -webkit-column-count: 3;
    -moz-column-count: 3;
    column-count: 3;
    width: 100%;
    column-fill: auto;
}

.column-count4 {
    -webkit-column-count: 4;
    -moz-column-count: 4;
    column-count: 4;
    width: 100%;
    column-fill: auto;
}

.account-popup-footer {
    position: absolute;
    bottom: 0;
    right: 0;
    border-top: 1px solid #c3c3c3;
    width: 100%;
    background: #f4f4f4 !important;
    height: 54px;
}
/* Rounded sliders */
.slider.round {
    border-radius: 34px;
}

    .slider.round:before {
        border-radius: 50%;
    }

/*Welcome page css*/

@media (min-width: 1360px) {
    .welcome-container {
        width: 1170px;
    }

    .welcome-page {
        margin-top: 50px;
        padding: 15px !important;
        background-color: #347B9A !important;
    }

    .welcome-text-content {
        text-align: justify;
        margin-top: 10px;
        font-size: 14px;
        color: #fff;
        line-height: 150%;
    }

    .welcome-heading {
        font-size: 40px;
        color: #fff;
    }

    .margin-top-welcome-txt {
        margin-top: 15px;
    }

    .spacing-welcome-txt {
        padding-left: 32px;
        padding-right: 18px;
    }

    .welcome-logo-spacing {
        padding-left: 5px;
    }

    .welcome-nav-spacing {
        margin-left: -95px;
    }
}

@media (min-width: 1600px) {
    #collapseKostraStatusReport {
        padding-top: 35px;
    }

    .welcome-container {
        width: 1260px;
    }

    .welcome-page {
        margin-top: 51px;
        padding: 15px !important;
        background-color: #347B9A !important;
        height: 550px;
    }

    .welcome-text-content {
        text-align: justify;
        margin-top: 10px;
        font-size: 14px;
        color: #fff;
        line-height: 170%;
    }

    .welcome-heading {
        font-size: 40px;
        color: #fff;
    }

    .margin-top-welcome-txt {
        margin-top: 15px;
    }

    .spacing-welcome-txt {
        padding-left: 32px;
        padding-right: 18px;
    }

    .welcome-logo-spacing {
        padding-left: 5px;
    }

    .welcome-nav-spacing {
        margin-left: 0px;
    }

    .ps-dynawidth-grid {
        width: 90.5% !important;
    }
}

.margin-left0 {
    margin-left: 0 !important;
}

.margin-right0 {
    margin-right: 0 !important;
}

.margin-right10 {
    margin-right: 10px !important;
}

.margin-right20 {
    margin-right: 20px !important;
}

.margin-right25 {
    margin-right: 25px !important;
}

.welcome-top {
    margin-top: 100px;
}

.welcome-page {
    margin-top: 50px;
    padding: 15px !important;
    background-color: #347B9A !important;
}

.welcome-heading {
    font-size: 40px;
    color: #fff;
}


.welcome-text-content {
    text-align: justify;
    margin-top: 10px;
    font-size: 14px;
    color: #fff;
    line-height: 150%;
}

.welcome-link-style {
    color: #fff;
    font-size: 19px;
    text-decoration: underline;
}
/*Welcome page css end*/



/*Error Logout CSS*/

#copyrightText, #logoutCopyrightText {
    font-size: 10px;
}

.framsikt-tower-img {
    height: 12px;
    width: 8px;
    margin-bottom: 3px;
}

.copyright-text-align {
    text-align: justify;
    word-break: break-all;
}

.bg-login-white {
    background-color: #fff;
}

.login-logo-wrap {
    margin: -43px 0 0 -41px;
}

.login-logo {
    width: 232px;
    height: 100%;
}

.error-common-text {
    font-size: 15px;
}

.btn-sign-in {
    display: inline-block;
    padding: 3px 20px;
    font-size: 14px;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    -webkit-user-select: none;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 3px;
}

/*Error Logout CSS End*/


/*mega menu*/

.acc-grid-background-news {
    max-height: 418px;
    background: #fff;
    padding-bottom: 12px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    overflow: hidden;
}

.news-footer-sec {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    height: 25px;
}

.filter-doc-dropdown {
    width: 97% !important;
}


.image-top-buttons button {
    padding: 2px 6px;
    border-radius: 2px;
}

.imgupld-desc-section .desc-save-btn {
    position: absolute;
    bottom: 3px;
    right: -35px;
    padding: 2px 6px;
    border-radius: 2px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

#filterWraffer button:hover,
#filterWraffer button:focus {
    background: #367b9a !important;
}

.megaMenuServiceArea {
    cursor: pointer;
    color: #4A8EB9;
}

.popupClose {
    margin-top: -18px !important;
    margin-right: -18px !important;
}
/* new header */
.logo-img {
    height: 30px;
}

.help-center {
    float: left;
    /*margin-left: -12px;*/
    margin-top: 12px;
}

.header-menu-height {
    height: 42px;
}

.header-top-position {
    position: fixed;
}

.header-dropdown-list {
    list-style-type: none;
    line-height: 20px;
    padding: 0;
    font-size: 12px;
}

.logo-txt {
    margin-top: -6px;
    position: absolute;
    font-size: 32px;
    padding: 11px;
}

.carousel-position {
    margin-left: 70px;
    text-align: center;
    font-size: 12px;
}

.carousel-image {
    width: 200px;
    border: 2px solid rgb(196, 188, 188);
    border-radius: 4px;
}

.carousel-image-title {
    font-family: semiFont, 'Open Sans Semibold', sans-serif;
    padding-top: 6px;
}
/* custom panel styles */
.push-pane-top {
    margin: 5px 0px 5px 0px;
    padding: 2px 5px 0px 0px;
}

.pane-head {
    padding: 4px 0px 4px 6px;
    background-color: #fff !important;
}

.pane-title {
    font-family: regularFont, 'Open Sans Regular', sans-serif;
    font-size: 14px;
    color: #347B9A;
}

.panel-background {
    background-color: #FAFBFB !important;
}

.title-no-collapse {
    text-decoration: none;
    font-size: 14px;
    margin-left: 5px;
    font-family: semiFont, 'Open Sans Semibold', sans-serif;
}

#headerSearchRightBar .title-no-collapse {
    color: #fff !important;
    padding: 5px 8px !important;
    height: 40px;
}

.net-total {
    /*background-color:#E8E8E8 !important;*/
    font-family: semiFont, 'Open Sans Semibold', sans-serif;
}

/* Logout Pop up css*/
#logoutSection {
    padding: 15px;
    background-color: #fff;
    margin: 10px !important;
}

.logout-message {
    margin-bottom: 75px;
}

.logout-button-section {
    margin-bottom: 0px;
}

#logoutConfirmationYes {
    width: 50px;
    margin-right: 10px;
}

#logoutConfirmationNo {
    width: 50px;
}

#logoutPopupLogo {
    margin-right: 55px;
}

#kommune-icon {
    cursor: default;
    position: relative;
    top: -14px;
    padding-left: 0px;
    margin-left: -12px;
}

.person-profile {
    background: transparent;
    padding-bottom: 5px;
    border: 0 none !important;
    position: relative;
}

.top-user-menu {
    margin-left: 15px;
}

.top-user-menu-new {
    cursor: pointer;
}

    .top-user-menu-new:hover {
        text-decoration: underline;
    }

.top-user-menu-header {
    margin-left: 22px;
}

.top-user-menu-tenant {
    margin-left: 14px;
    position: relative !important;
    top: -29px;
    left: -5px;
    cursor: default;
}

.top-user-menu-expand {
    float: right;
    padding-top: 16px;
}

.selected_tenant {
    position: relative;
    top: 16px;
    white-space: normal;
    display: inline-block;
    padding-left: 0.5rem;
}

.profile-subtenant {
    position: relative;
    width: 340px;
    padding: 8px 9px;
    border: 1px #B7B8BD solid;
    border-radius: 9px;
    background: #EBF0F6;
    text-align: left;
    cursor: pointer;
    overflow: hidden;
    white-space: normal;
    vertical-align: middle;
    color: black;
    z-index: 1001;
}

.user-menu-profile {
    display: inline-block;
    width: 430px;
    height: 88px;
    padding: 23px 7px;
    clear: both;
    font-weight: normal;
    line-height: 1.428571429;
    color: #333333;
    white-space: nowrap;
    cursor: pointer;
}

.user-menu-profile-kommune {
    display: inline-block;
    width: 430px;
    height: 114.6px;
    padding-top: 18px;
    padding-bottom: 30px;
    padding-left: 11px;
    padding-right: 20px;
    clear: both;
    font-weight: normal;
    line-height: 1.428571429;
    color: #333333;
    white-space: nowrap;
}

#subTenantDropDown {
    position: relative;
    left: -5px;
    top: -6px;
    width: 340px;
    cursor: pointer;
    border-top-right-radius: 0px;
    border-top-left-radius: 0px;
    border-top: none;
}

    #subTenantDropDown > li > a {
        white-space: normal;
        padding-left: 8px;
    }

.selectpicker option {
    width: 80% !important;
    padding: 5px !important;
    border: 1px #B7B8BD solid !important;
    border-radius: 9px !important;
    background: white !important;
    text-align: left !important;
    padding-top: 8px !important;
    padding-bottom: 8px !important;
    cursor: pointer !important
}

.contactus-popup {
    background-color: #fff !important;
    border-color: #fff !important;
    margin-left: 6px;
}

.support-heading {
    left: -60px;
    bottom: -10px;
    position: relative;
    margin-bottom: 20px;
}

.support-header-shape {
    border-radius: 5px 5px 1px 1px;
    width: 382px;
}

.support-txt-background {
    background: #F4F4F4;
    width: 383px;
    padding-top: 8px;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    padding-bottom: 24px;
    word-break: normal;
    text-align: left;
}

.support-border1 {
    border: 1px solid #C3C3C3;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    background-color: #fff;
    margin-top: 10px -3px -5px;
    width: 354px;
}

/*adding the arrow color for popup*/
.arrow-color {
    border-color: #6FA1B2 !important;
}
/* Logout Pop up css end*/

/*Privacy Pop up Start*/

.privacy-popup-title {
    margin-left: 10px;
}

#privacySection {
    padding-top: 12px;
    background-color: #fff;
    margin: 10px !important;
}

@media (min-width: 1024px) {
    .privacy-popup-width {
        width: 700px;
    }

    .privacy-text-section {
        height: 400px;
        overflow-y: scroll;
        padding-right: 25px;
        margin-top: -12px;
        width: 75%;
    }

    .privacy-logo-top {
        margin-top: 115px;
    }

    .privacy-text {
        text-align: justify;
        line-height: 200%;
        margin-top: 10px;
        /*font-family: regularFont;*/
        font-size: 12px;
    }
}

@media (min-width: 1280px) {
    #staffPositionSettings {
        width: 25%;
    }

    .privacy-popup-width {
        width: 700px;
    }

    .privacy-text-section {
        height: 400px;
        overflow-y: scroll;
        padding-right: 25px;
        margin-top: -12px;
        width: 75%;
    }

    .privacy-logo-top {
        margin-top: 115px;
    }

    .privacy-text {
        text-align: justify;
        line-height: 200%;
        margin-top: 10px;
        /*font-family: regularFont;*/
        font-size: 12px;
    }

    #populateStatSectionsTabWrap {
        max-height: 220px !important;
    }

    #finPlanImportSection {
        margin-left: 45px !important;
    }

    #accountsNotIncludedGrid {
        top: -16px;
    }

    #monthlyReportTabSection a {
        width: 16%;
        padding-top: 11px;
    }
}

/* Media query for FP popup start*/
@media (max-width: 1152px) {

    .login-info-content-wrap {
        top: 0 !important;
        left: 0 !important;
        position: relative !important;
    }

    .content-box-bg {
        width: 100% !important;
    }

    .login-footer {
        margin-top: 30px;
    }

    .filter-doc-dropdown {
        width: 128px;
    }

    #collapseActionInformation .styleWrapper, #collapseActionDesc .styleWrapper {
        padding-right: 22px;
        width: 97.1% !important;
    }

    #BPGoalsGridExport a {
        padding: 5px 2px !important;
    }

    #allWage {
        margin-left: 230px;
    }

    #allPrice, #allDeflator {
        margin-left: 62px;
    }

    #allNoAdj {
        margin-left: 68px;
    }
}

@media (max-width: 1366px) {

    #leftLoginContentSection .desc-text,
    #leftLoginContentSection .middle-sec {
        padding: 0 5px;
    }

    .filter-doc-dropdown {
        width: 150px;
    }

    #populateStatSectionsTabWrap {
        max-height: 190px !important;
    }

    #budgetPropContentWrapper, #FPBCContentWrapper {
        padding-left: 7px !important;
    }

    #budProDescriptionWrapper, #FPActionInfoWrapper, #budgPropActionInfoWrapper {
        width: 97.5% !important;
    }

    #FPActionInfoWrapper, #budgPropActionInfoWrapper {
        padding-right: 19px;
    }

    .box .k-chart {
        height: 270px !important;
    }
}

@media (max-width: 1380px) {
    .filter-doc-dropdown {
        width: 155px;
    }

    #uploadedImage {
        overflow: auto !important;
    }

    .upld-right-bottom-sec {
        bottom: 50px !important;
    }
}

@media (max-width: 1200px) {

    .upld-right-bottom-sec {
        bottom: 120px !important;
    }

    #imageUploadWindow,
    #uploadImagePopupWindow {
        zoom: 0.9;
    }
}

@media (max-width: 1360px) {
    .box .k-chart {
        height: 263px !important;
    }
}

@media (max-width: 1440px) {
    .filter-doc-dropdown {
        width: 165px;
    }
}

@media (max-width: 1280px) {
    #monthlyReportTabSection a {
        width: 17% !important;
    }

    .filter-doc-dropdown {
        width: 145px;
    }

    .box .k-chart {
        height: 245px !important;
    }
}

@media (min-width: 1280px) {
    #monthlyReportTabSection a {
        width: 17% !important;
    }

    .filter-doc-dropdown {
        width: 145px;
    }
}

@media (max-width: 1600px) {

    .filter-doc-dropdown {
        width: 185px;
    }

    #budgetPropContentWrapper, #FPBCContentWrapper {
        padding-left: 10px;
    }

    .styleWrapper {
        width: 97.8% !important;
    }

    #monthlyReportTabSection a {
        padding-top: 15px !important;
    }

    #leftLoginContentSection .desc-text,
    #leftLoginContentSection .middle-sec {
        padding: 0 5px;
    }

    #leftLoginContentSection .bottom-btn-sec {
        bottom: 40px;
    }
}
/* Media query for FP popup end*/

@media (min-width: 1360px) {
    .privacy-popup-width {
        width: 700px;
    }

    .privacy-text-section {
        height: 400px;
        overflow-y: scroll;
        padding-right: 25px;
        margin-top: -12px;
        width: 75%;
    }

    .privacy-logo-top {
        margin-top: 115px;
    }

    .privacy-text {
        text-align: justify;
        line-height: 200%;
        margin-top: 10px;
        font-size: 12px;
    }
}


@media (min-width: 1366px) {

    .privacy-popup-width {
        width: 700px;
    }

    .privacy-text-section {
        height: 400px;
        overflow-y: scroll;
        padding-right: 25px;
        margin-top: -12px;
        width: 75%;
    }

    .privacy-logo-top {
        margin-top: 115px;
    }

    .privacy-text {
        text-align: justify;
        line-height: 200%;
        margin-top: 10px;
        /*font-family: regularFont;*/
        font-size: 12px;
    }
}

@media (min-width: 1600px) {

    .privacy-popup-width {
        width: 1000px;
    }

    .privacy-text-section {
        height: 490px;
        overflow-y: auto;
        margin-top: -12px;
        padding-left: -17px;
        margin-left: -45px;
        width: 82%;
        padding-right: 27px;
    }


    .privacy-logo-top {
        margin-top: 150px;
    }

    .privacy-text {
        text-align: justify;
        line-height: 200%;
        margin-top: 10px;
        /*font-family: regularFont;*/
        font-size: 14px;
    }
}

/*CK editor*/
.cke_editable {
    font-family: editorFont, sans-serif;
    min-height: 200px !important;
    padding: 15px !important;
    font-size: 13px;
}

/*Privacy Pop Up End*/

/*ServiceArea Start CSS*/
.coverage-label {
    top: -15px;
}

.margincost-label {
    top: -35px;
}

.font-breadcrum {
    font-size: 12px;
}

.btnbudgetfinancialplanning {
    background-color: #347B9A;
    color: #F8F8F8;
    margin-top: 5px;
}

.actionbuttonsave {
    background-color: #347B9A;
    color: #F8F8F8;
}

.actionbuttoncancel {
    background-color: #347B9A;
    color: #F8F8F8;
}


.btnapply-budgetadjustment {
    background-color: #347B9A;
    color: #F8F8F8;
    padding: 5px;
    padding-left: 35px;
    padding-right: 35px;
    font-size: 10pt;
    margin-top: 30px;
}

.panel-border {
    border-color: #347B9A;
    margin-bottom: 0px;
}

.budgetadj-lblmarginalcost {
    margin-top: 32px;
}

.service-collapse {
    margin-top: 30px;
}

.budgetAdj-val1 {
    padding: 1px;
    padding-left: 25px;
    padding-right: -108px;
    padding-right: 14px;
    width: 80px;
    color: #347B9A;
}

.budgetAdj-val2 {
    padding: 1px;
    padding-left: 25px;
    padding-right: -108px;
    padding-right: 14px;
    width: 80px;
    color: #347B9A;
}

.budgetAdj-val3 {
    padding: 1px;
    padding-left: 25px;
    padding-right: -108px;
    padding-right: 14px;
    width: 80px;
    color: #347B9A;
}

.budgetAdj-val4 {
    padding: 1px;
    padding-left: 25px;
    padding-right: -108px;
    padding-right: 14px;
    width: 80px;
    color: #347B9A;
}

.budgetAdj-input-section1 {
    text-align: right;
}

.budgetAdj-input-section2 {
    text-align: right;
}

.budgetadj-marginalcost {
    padding: 1px;
    padding-left: 25px;
    padding-right: -108px;
    padding-right: 14px;
    width: 114px;
    margin-top: 30px;
    color: #347B9A;
}

.kr {
    font-size: 16px;
    font-family: regularFont, 'Open Sans Regular', sans-serif;
    font-weight: 500;
    line-height: 1.1;
    color: #333333;
}

.margincost-labeltext {
    top: -19px;
}

.panelbackground-white {
    background-color: #fff;
}

.budgetAdj-val1, .budgetAdj-val2, .budgetAdj-val3, .budgetAdj-val4, .budgetadj-marginalcost {
    border-color: #6FA1B2;
    border-width: thin;
    border-style: double;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    text-align: right;
}

textarea[name="actionDescription"] {
    width: 300%;
    height: 105px;
    border-style: ridge;
    border-color: #6FA1B2;
    border-width: thin;
}

textarea[name="actionConsequences"] {
    width: 300%;
    height: 105px;
    border-style: ridge;
    border-color: #6FA1B2;
    border-width: thin;
}

.textarea-content-left-right {
    direction: ltr;
}

.k-datepicker {
    width: 200px;
    border-style: ridge;
}

.btn-actions {
    /*margin-top: 24px;*/
    margin-right: -42px;
}

#consequenceDetailsEditGrid, #consequencePopupGrid, #BPPopupGrid {
    border: none;
}

/*Style for pop up end*/

/*ServiceArea End CSS*/

/*Climate data analysis background*/
.climate-data-analysis-bg {
    background-image: url('../images/climate-data-analysis.jpg');
}

/*Project Manager Investment background*/
.project-manager-investment-bg {
    background-image: url('../images/aasandfjellet_1.jpg');
}

/*Project Manager  New Investment background*/
.project-manager-newinvestment-bg {
    background-image: url('../images/mr_investment_project.jpg');
}

/*Financing Forecast Overview background*/
.financing-forecast-overview-bg {
    background-image: url('../images/finance_banner.jpg');
}

/*MR Accounting statement Overview background*/
.accounting-overview-bg {
    background-image: url('../images/accounting_overviewbanner_image.jpg');
}

/*opportunity assessment*/
.opportunity-analysis-dynamic-heading {
    margin-left: -26px;
    margin-top: 10px;
}

.opportunity-dynamic-heading {
    margin-top: 38px;
    color: #fff;
    font-size: 38px;
    font-family: impact, sans-serif;
}

.opportunity-assessment-bg {
    background-image: url('../images/opportunity-banner.jpg');
    background-repeat: no-repeat;
    background-size: 100%;
    /*background-position:center;*/
}

#opportunityDropContainer .k-dropdown {
    width: 40%;
}

#oppsAssessmentDropdown .k-dropdown {
    width: 25%;
}

#budgetManagementDocumentDropdwn .k-dropdown {
    width: 100%;
}

#popAnalysisTemplateContainer .k-dropdown {
    width: 100% !important;
}

.table-wrapper {
    display: table;
    width: 100%;
    table-layout: fixed;
}

.table-row {
    display: table-row;
    text-align: center;
}

.table-column1 {
    display: table-cell;
    text-align: left;
    width: 60%;
    text-overflow: ellipsis;
}

.table-column2 {
    display: table-cell;
    width: 20%;
    overflow: hidden;
    text-align: right;
    text-overflow: ellipsis;
}

.table-column3 {
    display: table-cell;
    width: 20%;
    text-align: right;
    text-overflow: ellipsis;
}

.chk-opp {
    margin-top: 28px;
}

.textarea-opp {
    padding-left: 16px;
}

.top-img {
    height: 330px;
    width: 410px;
}

.user-single {
    border-radius: 30px;
    padding: 0;
    background: #78A7BB;
}

.user-single-desc {
    padding: 4px 0px 0px 4px;
    color: #fff;
    text-shadow: 1px 1px 2px #000000;
}

.user-single-desc-participant {
    padding: 5px 0px 0px 0px;
}

.user-single-participant {
    border-radius: 30px;
    padding: 0;
    background: #E5E5E5;
}

.discussion-text {
    /*width:100%;*/
    border: 10px solid #E5E5E5;
    border-radius: 0px;
}

.user-comment {
    color: #b1b1b1 !important;
    font-size: 12px;
}

.comment-display {
    margin-left: 65px;
}
/*Opportunity assessment POPUP*/
.popup-editor {
    border: 1px solid #6FA1B2;
    cursor: pointer;
    background-color: #fff;
    border-radius: 4px;
}




/*kostra commom css start*/
.acc-header-shape {
    border-radius: 5px 5px 1px 1px;
    cursor: pointer;
}

.acc-header-left {
    left: -5px;
    bottom: -5px;
    position: relative;
    margin-bottom: 2px;
}

.acc-header-right {
    position: relative;
    bottom: -15px;
    right: -14px;
}

.acc-grid-background {
    background: #F4F4F4;
    padding-bottom: 15px;
}

.acc-grid {
    margin-top: 10px;
    border-color: #c3c3c3;
    overflow: auto;
}

.goto-map {
    padding-left: 12px;
    padding-top: 1px;
}

.merge-left div #chart1, div #chart2, div #chart3, div #chart4 {
    width: 99%;
    height: 100%;
    margin-top: 4px;
}

/*kostra commom css end*/

/*Error notification start*/

.error {
    opacity: 0.6;
    background: #D41113;
    color: #ffffff;
    width: 440px;
    min-height: 135px;
    /*padding-top: 40px;*/
    line-height: 100px;
}

    .error h3 {
        font-size: 1.5em;
        font-weight: normal;
        display: inline-block;
        vertical-align: middle;
        color: #fff;
    }

    .error h6 {
        color: #fff;
    }

    .error img {
        display: inline-block;
        vertical-align: middle;
        margin-right: 10px;
    }

/*Error notification end*/

/*Kostra main css start*/

.kpi-title {
    height: 36px;
}

.top-drop .k-multiselect-wrap {
    overflow: auto;
    max-height: 55px;
}


.kostra-main-bg {
    background-image: url('../images/kostra_analysis_pano.jpg');
}

.work-statistics-bg {
    background-image: url('../images/work_statistics_pano.jpg');
}

.kostra-docexportoverview-bg {
    background-image: url('../images/KOSTRA_docprod_banner.jpg');
}

.report-calendar-bg {
    background-image: url('../images/report_calendar_banner.jpg');
}

.accountinginfo-admin-bg {
    background-image: url('../images/report_calendar_banner.jpg');
}

.climate-action-bg {
    background-image: url('../images/climate-action-bg.jpg');
}


.kostra-left-bg {
    /*background-image:url(../images/kostra-analysis.png);*/
    background-repeat: no-repeat;
}

.kostra-right-bg {
    background-image: url(../images/kostra-analysis-right.png);
    margin-top: 12px;
    margin-left: -8px;
    background-repeat: no-repeat;
    background-size: 100%;
    margin-bottom: 15px;
}

.population-right-bg {
    background-image: url(../images/population-right.png);
    margin-top: 12px;
    background-size: 100%;
    margin-bottom: 10px;
}

.cons-right-bg {
    background-image: url(../images/kostra-analysis-right.png);
    margin-top: 12px;
    margin-left: -8px;
    background-repeat: no-repeat;
    background-size: 100%;
    margin-bottom: 15px;
}

.learn-label {
    padding-left: 30px;
    float: right;
    width: 20%;
    color: #fff;
    left: 25px;
}

.kostra-learn-label {
    padding-left: 60px;
    float: left;
    color: #fff;
    left: 60px;
}

.indicatorChart {
    height: 115px !important;
}

.kostra-indicator-box {
    margin-bottom: 0px;
    background-color: #f6f6f6;
    border: 1px solid #c6c6c6;
    border-radius: 4px;
    cursor: pointer;
    padding-top: 2px;
}

.kostra-indicator-left {
    top: 0px;
    color: #000;
}

.kostra-indicator-right {
    top: 3px;
    position: relative;
    float: right;
    left: -13px;
    color: black;
}


.kpi-indicator-box {
    margin-bottom: 0px;
    background-color: #fff;
    border-radius: 4px;
    cursor: pointer;
}

.kpi-indicator-left {
    top: 0px;
}

.kpi-indicator-right {
    top: 3px;
    position: relative;
    float: right;
    left: -13px;
    color: #fff;
}

.kostramain-citizen-border1 {
    border: 1px solid #C3C3C3;
    border-radius: 4px;
    background-color: #fff;
    margin-top: 10px;
}

.kostramain-border1 {
    height: 250px;
    border: 1px solid #C3C3C3;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    background-color: #fff;
    overflow-y: scroll;
}

.kostra-analysis-img-export {
    position: relative;
    bottom: -6px;
    right: -21px;
}

.kostra-analysis-heading-left {
    left: 0px;
    bottom: -10px;
    position: relative;
    margin-bottom: 20px;
}

.kostra-analysis-heading-right {
    left: -23px;
    bottom: -10px;
    position: relative;
    margin-bottom: 20px;
}

.kostraborderheading {
    font-family: semiFont, 'Open Sans Semibold', sans-serif;
    margin-top: 12px;
}

.comment-text {
    border: 10px solid #E5E5E5;
    border-radius: 0px;
    font-size: inherit;
}

.kostra-analysis-comment {
    background-color: #E5E5E5;
    margin-left: 16px;
}

.kostra-first-screen-img {
    padding-top: 6px;
    padding-bottom: 6px;
}

.kostra-analysis-headerarea {
    background-color: #E5E5E5;
    margin-left: 16px;
}

.kostra-analysis-comment-text {
    padding-right: 0px;
    padding-left: 0px;
}

.kostra-analysis-user {
    padding-right: 0px;
    padding-top: 6px;
}

#kostraMainGrid1,
#kostraMainGrid2,
#kostraMainGrid3,
#kostraMainGrid4,
#kostraMainGrid5,
#kostraMainGrid6,
#kostraMainGridInEval,
#BPBudgetKeyFAGrid {
    border: none;
    overflow: auto;
}

    #BPBudgetKeyFAGrid.k-grid tr td {
        border-left: none;
    }

    #kostraMainGrid1.k-grid tr td,
    #kostraMainGrid2.k-grid tr td,
    #kostraMainGrid3.k-grid tr td,
    #kostraMainGrid4.k-grid tr td,
    #kostraMainGrid5.k-grid tr td,
    #kostraMainGrid6.k-grid tr td {
        border-left: none;
        text-align: right;
        white-space: nowrap;
    }

.treeview-export-img {
    margin-bottom: 10px;
    position: relative;
    margin-right: 10px;
    float: right;
}

.kostra-dynamic-heading {
    margin-top: 38px;
    color: #fff;
    font-size: 250%;
    font-family: impact, sans-serif;
    white-space: nowrap;
    text-shadow: 1px 1px 2px #000000;
}

.kostra-subheading {
    color: #fff;
    font-size: 12px;
    text-shadow: 1px 1px 2px #000000;
}

.kostra-subheading-top {
    margin-top: -4px;
}

.kostra-analysis-dynamic-heading {
    margin-left: -26px;
    margin-top: 10px;
}

#kostraMainCostCitizenGrid1,
#kostraMainCostCitizenGrid2,
#costReductionPotentialGrid,
#kostraCRIndicatorGrid,
#kostraMainExpenseGrid {
    border: none;
    overflow: auto;
}

    #kostraMainCostCitizenGrid1.k-grid tr td,
    #kostraMainCostCitizenGrid2.k-grid tr td,
    #costReductionPotentialGrid.k-grid tr td,
    #costReductionPotentialGrid1.k-grid tr td,
    #kostraMainExpenseGrid.k-grid tr td {
        border-left: none;
        text-align: right;
        white-space: nowrap;
        padding-top: 0px;
        padding-bottom: 0px;
    }

    #kostraCRIndicatorGrid.k-grid tr td {
        border-left: none;
        text-align: right;
        white-space: normal;
    }

.adjust-expense {
    position: relative;
    top: -42px;
    left: 124px;
}

.adjust-expenditure {
    position: relative;
    top: -42px;
    left: 200px;
}

.costreduction-adjust-expense {
    position: relative;
    top: -32px;
    right: 200px;
}

.costreduction-adjust-chk {
    margin-left: 10px;
    top: 3px;
    position: relative;
}

.adjust-chk {
    margin-left: 5px;
    top: 3px;
    position: relative;
}

.doc-selectall-chk {
    margin-left: 4px;
    bottom: 3px;
    position: relative;
}

.acc-kostra-eval-background {
    background: #F4F4F4;
    padding-bottom: 15px;
    margin-top: -10px;
}

.kostradetail-ssb-border1 {
    height: 250px;
    border: 1px solid #C3C3C3;
    border-radius: 4px;
    background-color: #fff;
    margin-top: 10px;
    overflow-y: scroll;
    padding-top: 15px;
}

.acc-padding-main {
    min-height: 49px;
}

.kostra-detail-heading-left {
    left: 0px;
    bottom: -10px;
    position: relative;
    margin-bottom: 20px;
}

.production-spacing {
    border-top: none;
    margin-top: 12px;
    margin-bottom: 12px;
}

.document-prod-padding {
    padding-top: 15px;
    padding-bottom: 10px;
}

#PublishedDocGrid {
    border: none;
    overflow: auto;
}

    #PublishedDocGrid.k-grid tr td {
        border-left: none;
        text-align: right;
        white-space: nowrap;
    }

.kostra-detail-adj-chk-style {
    margin-left: 26px;
    margin-top: -6px;
}

.kostra-adj-display-none {
    display: none !important;
}

.kostra-save-reset-style {
    margin-top: -7px;
    margin-bottom: 4px;
}


.kostra-doc-radio {
    vertical-align: middle;
    margin-top: -1px !important;
}

.kostra-popup-reset-save-style {
    margin-top: -10px;
    margin-bottom: 3px;
    padding-right: 5px;
}

.kostra-indicator-tooltip-style {
    border-radius: 4px;
}

.kostra-indicator-popup {
    background-color: #fff !important;
    border: 1px solid rgb(111, 161, 178) !important;
    min-height: 50px !important;
}

.monthly-roportdoc-popup {
    background-color: #e3e3e3 !important;
    border: 1px solid #505050 !important;
    border-radius: 0px !important;
    padding-left: 0;
    display: block !important;
}

.staff-admin-import-tooltip-popup {
    background-color: #f4f4f4 !important;
    border: 1px solid #505050 !important;
    border-radius: 0px !important;
    padding-left: 5px;
}

.kostra-indicator-status-style {
    color: #606060;
    background-size: auto;
    min-height: 45px;
    background-position-x: 55%;
    background-position-y: 0%;
    background-position: 55% 0%;
    background-repeat: no-repeat;
}

.kostra-indicator-header-style {
    color: #606060;
    padding-top: 20px;
    padding-left: 5px;
}

#kostraQualityScoreGrid,
#kostraQualityScoreGrid tr th,
#kostraQualityScoreGrid .k-grid-header, 
#kostraQualityScoreGrid .k-grid-header-wrap,
#kostraQualityScoreGrid tr td {
    background: #f4f4f4 !important;
}

/*Kostra main css end*/

/*Kostra overview CSS*/
#kostraOverviewGrid {
    min-height: 50px;
}

    #sustainableGoalsAnalysisTreeGrid .k-grid-content tr td, 
    #sustainableGoalsAnalysisGrid.k-grid tr td, 
    #kostraOverviewGrid.k-grid tr td {
        border-left: none;
        text-align: right;
    }

.kosta-analysis-midsize-circle {
    width: 100px;
    height: 100px;
    border-radius: 250px;
    font-size: 20px;
    color: #FFFFFF;
    line-height: 96px;
    text-align: center;
    background: #78A7BB;
    border-style: solid;
    border-width: 3px;
    border-color: #367B9A;
    cursor: pointer;
}

.kosta-analysis-smallsize-circle {
    width: 40px;
    height: 40px;
    border-radius: 100px;
    font-size: 12px;
    color: #FFFFFF;
    line-height: 35px;
    text-align: center;
    background: #BBD3DE;
    border-style: solid;
    border-width: 3px;
    border-color: #78A7BB;
    cursor: pointer;
}

#kostraIndicatorYear2 {
    margin-top: 0px;
    margin-left: -2px;
}

#kostraIndicatorYear3 {
    margin-left: 3px;
    margin-top: 0px;
}

#kostraIndicatorYear4 {
    margin-left: -19px;
    margin-top: -4px;
}

.arrow-down {
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid #4A8EB9;
    margin-top: 52px;
    position: absolute;
    margin-left: 0px;
}

.arrow-up {
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid #4A8EB9;
    margin-top: 52px;
    position: absolute;
    margin-left: 0px;
}

.arrow-top {
    margin-top: 44px;
}

.heading-key-indicator {
    margin-left: 62px;
}

.arrowdown-loss {
    margin-top: 44px;
}

#kostraOverviewFunction,
#kostraOverviewDataSource,
#kostraOverviewAdjSetup {
    padding: 5px !important;
    margin-left: 23px;
    margin-top: 5%;
    border-bottom: none;
    border-radius: 4px;
    box-shadow: 1px 2px 2px 1px rgba(0,0,0,0.1) !important;
    width: 20% !important;
}

.common-adjustment-li-style {
    display: block;
    padding: 3px;
    font-size: 12px;
}

.common-adjustment-li-content {
    margin-left: 3px;
    top: 3px;
    position: relative;
    margin-right: 5px;
}

.maximize-wrapper {
    z-index: 9999 !important;
    width: 100% !important;
    height: 100%;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    bottom: 0 !important;
    background: #fff !important;
    overflow: hidden !important;
}

    .maximize-wrapper .indicator-buttons-section {
        background: #f4f4f4;
        position: fixed;
        z-index: 9999;
        width: 100%;
        bottom: 0px;
        left: 0px;
        height: 55px;
        border-top: 1px solid #b3b3b3;
        padding-left: 14px !important;
    }

    .maximize-wrapper .max-section-wrappers {
        padding-left: 0px !important;
        padding-right: 0px !important;
    }
/*Kostra overview CSS End*/

/*Kostra detail CSS Start */
#kostraDetailIndicatorGrid {
    overflow: hidden;
    border-right: 20px solid rgb(196,188,188);
}

    #kostraDetailIndicatorGrid.k-grid tr td {
        border-left: none;
        text-align: right;
    }

.archive-Doc-Dropdown {
    width: 100%;
}

.btn-adjustment-update-style {
    margin-top: 10px;
    margin-left: 7px;
}

.btn-kostra-detail-back {
    background-color: #347B9A;
    color: #F8F8F8;
    margin-top: 10px;
    margin-left: 15px;
    padding-left: 14px;
    padding-right: 14px;
    width: 40%;
}

.btn-kostra-detail-chk {
    padding-top: 4px;
    padding-bottom: 4px;
    padding-left: 15px;
    padding-right: 15px;
}

.kostra-detail-include-pallete {
    width: 100%;
    list-style: none;
}

    .kostra-detail-include-pallete li {
        float: left;
        text-align: center;
    }

.kostra-detail-include-link {
    min-width: 63px;
    margin-left: 5px;
}

.kostra-detail-include-link-temp {
    min-width: 59px;
    margin-left: 5px;
}

.nav {
    padding-left: 25px;
}

.textarea-kostra-explanation, .textarea-kostra-evaluation {
    width: 406px;
    margin-left: 4px;
    text-align: left;
}

#chkKostraDetailLockContent, #chkKostraDetailIncluded {
    border-color: #6FA1B2;
}


.kostra-section-detail-tablink {
    min-width: 63px;
    margin-left: 4px;
}

.kostra-section > .kostra-section-detail-tablink .active > a.selected {
    background-color: #377B9A;
    color: #ffffff;
    border-color: #30697D;
}


.kostra-section-tablink {
    min-width: 75px;
    margin-left: 4px;
}

.kostra-section > .kostra-section-tablink .active > a.selected {
    background-color: #377B9A;
    color: #ffffff;
    border-color: #30697D;
}

.imgupld-button-section {
    width: 15%;
}

.imgupld-image-section {
    width: 40%;
    position: relative;
}

    .imgupld-image-section img {
        width: 100%;
        height: 115px;
        position: relative;
    }

    .imgupld-image-section .image-top-buttons {
        position: absolute;
        bottom: 10px;
        right: 10px;
    }

.imgupld-desc-section {
    width: 40%;
    position: relative;
}

    .imgupld-desc-section .imgupld-desc {
        width: 95%;
        position: relative;
        height: 110px;
        border-color: #6FA1B2 !important;
    }

.selected-name-bg {
    background: #f4f4f4;
    padding-top: 5px;
    padding-bottom: 9px;
    border-radius: 4px;
}

    .selected-name-bg a {
        color: #000;
        font-family: semiFont, 'Open Sans Semibold', sans-serif;
    }

.imgupld-image-contents {
    width: 23%;
    margin-bottom: 10px;
    margin-right: 10px;
    border: 1px solid #30697D;
    height: 100px;
    padding: 0;
    position: relative;
}

    .imgupld-image-contents img {
        width: 100%;
        height: 100%;
        position: relative;
    }

.image-wrapper-container img {
    height: 100%;
    width: 100%;
}

.imgupld-image-contents .image-top-buttons {
    position: absolute;
    bottom: 0;
    right: 0;
}

.use-image-wrapper {
    height: 50px;
    width: 230px;
}

    .use-image-wrapper img {
        height: 100%;
        width: 100%;
    }

.has-image-star {
    width: 10px;
}

.nav-tabs > li > a {
    color: #377B9A;
    border-radius: 5px;
    background-color: transparent;
    text-align: center;
}

.nav-tabs > li.active > a:hover {
    background-color: #377B9A;
    color: #fff;
    border-radius: 5px;
    text-align: center;
    height: 35px;
    padding: 0px;
}

.menuactive {
    border: 1px solid #fff !important;
    background-color: #367b9a !important;
    outline: 2px solid #367b9a !important;
}

    .menuactive a, .menuactive:hover {
        color: #fff !important;
    }

#budgetChangesTab li a,
#bpNewStrategyTabSections.nav-tabs > li > a,
#investmentServiceTabSections.nav-tabs > li > a,
#budgetProposalTabSections.nav-tabs > li > a,
#bpResponsibleTabSections.nav-tabs > li > a,
#municipalTabSections.nav-tabs > li > a,
#dispForecastTabSections.nav-tabs > li > a,
#visionTabSections.nav-tabs > li > a,
#internalActionTabSections.nav-tabs > li > a,
#indicatorAnalysisTabSections.nav-tabs > li > a,
#planTabSections.nav-tabs > li > a,
#focusAreaTabSections.nav-tabs > li > a,
#bpFeeTabSections.nav-tabs > li > a {
    height: 36px;
    color: #377B9A;
    border-radius: 5px;
    background-color: #fff;
    text-align: center;
    padding: 0px;
    border-color: #377B9A;
}

#kostraSections.nav-tabs > li > a,
#populateStatSectionsTab li a {
    height: 24px;
    color: #fff;
    border-radius: 4px;
    border-color: #909090;
    background-color: #B2B2B2;
    padding: 2px;
    text-align: center;
}

#populateStatYearsTab button,
#populationStatChartForeCostTabs li a,
#populationStatChartTabs li a,
#populateStatSections li a {
    border-color: #377B9A;
    color: #377B9A;
    background-color: #fff;
    height: 30px;
    padding: 4px;
}

    #kostraSections.nav-tabs > li.active > a,
    #populateStatYearsTab button.active,
    #populationStatChartForeCostTabs.nav-tabs > li.active > a,
    #populationStatChartTabs.nav-tabs > li.active > a,
    #populateStatSections.nav-tabs li.active a,
    #populateStatSectionsTab.nav-tabs li.active a,
    #cityranking.nav-tabs > li.active > a,
    #budgetChangesTab.nav-tabs li.active a {
        background-color: #377B9A;
        color: #fff;
        border-radius: 4px;
        border-color: #30697D;
    }

#budgetChangesTab,
#populateStatSectionsTab,
#populateStatSections {
    margin: 0;
    padding-left: 0;
}

    #populateStatSectionsTab li, #populateStatSections li {
        width: 50%;
        margin-right: 0;
    }

    #populateStatSectionsTab li {
        margin-bottom: 10px;
    }

        #populateStatSectionsTab li a {
            border-color: #377B9A;
            color: #377B9A;
            background-color: #fff;
            height: 34px !important;
            overflow: hidden;
        }

.population-nav-tab li a:hover {
    border-radius: 4px !important;
}

#populationstatgraph svg g[clip-path] path {
    fill-opacity: 1;
}

    #populationstatgraph svg g[clip-path] path[stroke*="#fff"] {
        fill-opacity: 0.4 !important;
    }

.kostra-detail-border {
    border-radius: 5px 5px 0px 0px;
    cursor: pointer;
    min-height: 45px;
}

.kostra-detail-headertxt {
    padding-bottom: 26px;
    top: 13px;
}

.kostra-detail-imgexport-header {
    top: 7px;
}

.kostra-detail-export-image {
    margin-right: -15px;
}

.kostra-detail-tabsection {
    background: #f4f4f4;
    padding-bottom: 15px;
}

.kostra-detail-tabsection-border {
    margin-top: 15px;
    padding-top: 15px;
    background: #ffffff;
}

.kostra-section {
    border: none;
    padding: 0;
    margin-left: 16px;
    padding-bottom: 13px;
}

.kostra-detail-explanation-border {
    border-radius: 5px 5px 0px 0px;
    cursor: pointer;
}

.kostra-detail-explanation {
    padding-bottom: 26px;
    top: 13px;
    /*color:#347B9A;*/
}

.kostra-details-items,
.population-statistics-details-items {
    cursor: pointer;
}

.population-statistics-details-hide {
    display: none;
}

.kostra-detail-bckground {
    background: #f4f4f4;
    padding-bottom: 15px;
    padding-top: 15px;
}

.kostra-detail-explanation-txtarea {
    background: #fff;
}

.btnPostbckground {
    background: #E5E5E5;
    height: 35px;
}

#kostraSectionGrid1.k-grid tr td,
#kostraSectionGrid2.k-grid tr td,
#kostraSectionGrid3.k-grid tr td,
#kostraSectionGrid4.k-grid tr td,
#kostraSectionGrid5.k-grid tr td,
#kostraSectionGrid6.k-grid tr td,
#kostraSectionGrid7.k-grid tr td,
#kostraSectionGrid8.k-grid tr td,
#kostraSectionGrid9.k-grid tr td {
    border-left: none;
    text-align: right;
}

#kostraSectionGrid1,
#kostraSectionGrid2,
#kostraSectionGrid3,
#kostraSectionGrid4,
#kostraSectionGrid5,
#kostraSectionGrid6,
#kostraSectionGrid7,
#kostraSectionGrid8,
#kostraSectionGrid9 {
    margin: 4px;
    margin-top: 30px;
    border: none;
}

.kostra-detail-popup-grid.k-grid tr td, .kostra-detail-popup-grid.k-grid tr th {
    border-left: none;
    text-align: right;
}

    .kostra-detail-popup-grid.k-grid tr th:first-child {
        text-align: left;
    }

.kostra-detail-popup-grid {
    margin: 4px;
    border: none;
    font-size: 14px;
}

#kostraSectionChart2, #kostraSectionChart3, #kostraSectionChart4, #kostraSectionChart5, #kostraSectionChart6, #kostraSectionChart7 {
    position: static;
}

.kostra-productivity-availablerevenue-title {
    left: 10px;
    bottom: -10px;
    top: 25px;
    position: relative;
    margin-bottom: 20px;
}

.kostra-revenue-slider {
    width: 460px;
    margin-left: 8px;
}

.tab-radio-text {
    position: relative;
    top: -3px;
}
/*Kostra detail CSS End */


/*Kostra Analysis First Screen added new*/


.k-window-title .ProfitVsRevenueWindow_wnd_title,
.k-window-title.budgetManagementDebtPerCitizenWindow_wnd_title,
.k-window-title.ConsequenceProfitVsRevenueWindow_wnd_title,
.k-window-title .consequenceReseveFundWindow_wnd_title,
.k-window-title .consequenceDebtPerCitizenWindow_wnd_title {
    margin-left: 10px;
    margin-top: -2px;
}

.profitVsrevenue-bckground,
.reservefund-bckground,
.debtvsrevenue-bckground,
.debtpercitizen-bckground,
.ConsequenceprofitVsrevenue-bckground,
.ConsequenceReseveFundWindow-bckground,
.Consequencedebtvsrevenue-bckground,
.consequencedebtpercitizen-bckground,
.budgetManagementReseveFundWindow-bckground,
.budgetManagementdebtvsrevenue-bckground,
.budgetManagementdebtpercitizen-bckground {
    background: #fff;
    width: 555px;
    /*height: 501px;*/
    margin-top: -3px;
}

.profitVsrevenue-border,
.reservefund-border,
.debtvsrevenue-border,
.debtpercitizen-border {
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #ccc;
    padding-top: 4px;
    margin-top: -13px;
}

.bm-window-border-style {
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #ccc;
    padding-top: 4px;
    margin-top: -13px;
    width: 94%;
    background-color: #FFF;
}

.upld-right-bottom-sec {
    position: absolute;
    left: 20px;
    bottom: 0;
}

.profit-vsrevenue-custom-window.k-window-action,
.reservefund-customWindow.k-window-action,
.consequence-reseve-fund-custom-window.k-window-action,
.consequenceReseveFundWindows.k-window-action,
.consqdebt-vsrevenue-custom-window.k-window-action,
.financeDebtvsRevenueWindowcustomWindow.k-window-action,
.kostracostredcustomWindow.k-window-action {
    padding: 0px;
}

.profit-vsrevenue-custom-window .k-window-titlebar.k-header,
.reservefund-customWindow .k-window-titlebar.k-header,
.budgetManagementReseveFundcustomWindow .k-window-titlebar.k-header,
.budgetManagementdebtvsrevenueWindowcustomWindow .k-window-titlebar.k-header,
.ConsequenceProfitVsRevenueWindowcustomWindow .k-window-titlebar.k-header,
.consequence-reseve-fund-custom-window .k-window-titlebar.k-header,
.consqdebt-vsrevenue-custom-window .k-window-titlebar.k-header,
.ConsequenceDebtPerCitizenWindowcustomWindow .k-window-titlebar.k-header,
.financeProfitVsRevenueWindowcustomWindow .k-window-titlebar.k-header,
.financeReseveFundWindowcustomWindow .k-window-titlebar.k-header,
.financeDebtvsRevenueWindowcustomWindow .k-window-titlebar.k-header,
.financeDebtPerCitizenWindowcustomWindow .k-window-titlebar.k-header,
.kostracostredcustomWindow .k-window-titlebar.k-header {
    margin-top: -18px !important;
    max-height: 100px;
    position: relative;
    height: inherit;
    border-bottom: 0px;
    float: left;
}

.profit-vsrevenue-custom-window .k-window-title,
.reservefund-customWindow .k-window-title,
.budgetManagementReseveFundcustomWindow.k-window-title,
.budgetManagementdebtvsrevenueWindowcustomWindow .k-window-title,
.ConsequenceProfitVsRevenueWindowcustomWindow .k-window-title,
.kostracostredcustomWindow .k-window-title {
    margin-top: -6px;
    white-space: normal;
    position: relative;
}

.profit-vsrevenue-custom-window .k-state-hover,
.reservefund-customWindow .k-state-hover,
.budgetManagementResevecustomWindow .k-state-hover,
.ConsequenceProfitVsRevenueWindowcustomWindow.k-state-hover,
.consqdebt-vsrevenue-custom-window.k-state-hover,
.ConsequenceDebtPerCitizenWindowcustomWindow.k-state-hover,
.budgetManagementReseveFundcustomWindow.k-state-hover,
.budgetManagementdebtvsrevenueWindowcustomWindow .k-state-hover,
.budgetManagementDebtPerCitizenWindow .k-state-hover,
.kostracostredcustomWindow .k-state-hover {
    border-color: transparent;
    background-color: transparent;
}

#ProfitVsRevenueWindow,
#reserveFundWindow,
#DebtvsRevenueWindow,
#DebtPerCitizen,
#budgetManagementReseveFundWindow,
#budgetManagementdebtvsrevenueWindow,
#budgetManagementDebtPerCitizenWindow,
#ConsequenceProfitVsRevenueWindow,
#consequenceReseveFundWindow,
#ConsequenceDebtvsRevenueWindow,
#consequenceDebtPerCitizenWindow,
#financeReseveFundWindow,
#financeDebtvsRevenueWindow,
#financeDebtPerCitizenWindow,
#kostraCostReductionWindow {
    margin-top: 10px;
}

#ProfitVsRevenueGrid.k-grid tr td,
#reserveFundGrid.k-grid tr td,
#DebtvsRevenueGrid.k-grid tr td,
#DebtPerCitizenGrid.k-grid tr td {
    border-left: none;
    text-align: right;
}

#ProfitVsRevenueGrid,
#reserveFundGrid,
#DebtvsRevenueGrid,
#DebtPerCitizenGrid,
#kostraCostReductionPopGrid {
    border: none;
}
/*city overview*/

#cityRankingGrid.k-grid tr td {
    border-left: none;
    text-align: right;
}


/*City ranking CSS start*/

.kostra-dynamic-heading-city {
    margin-top: 38px;
    color: #387b9a;
    font-size: 38px;
    font-family: impact, sans-serif;
}

.kostra-subheading-city {
    color: #387b9a;
    font-size: 12px;
}

.cityranking-tabsection-border {
    margin-top: 15px;
    padding-top: 15px;
    background: #ffffff;
}

.cityranking-tabsection {
    padding-bottom: 14px;
    background: #f4f4f4;
}

.wordcloud {
    height: 380px;
    width: 500px;
    margin: 0.3in auto;
    margin-top: -25px;
    padding: 0px;
}

.cityranking-section > .cityranking-section-tablink .active > a.selected {
    background-color: #377B9A;
    color: #fff;
    border-color: #30697D;
    border-radius: 4px;
}

#cityranking.nav-tabs > li > a {
    height: 24px;
    color: #ffffff;
    border-radius: 4px;
    border-color: #909090;
    background-color: #B2B2B2;
    padding: 2px;
    text-align: center;
    margin-left: 5px;
}

.cityranking-section {
    margin-top: -2px;
    margin-bottom: 8px;
    border-bottom: 0px;
    padding-left: 0px;
}

.kostra-city-details-items {
    cursor: pointer;
}

.city-detail-Slider {
    width: 560px;
    margin-left: 8px;
}

#kostraCityRankingSectionGrid1.k-grid tr td,
#kostraCityRankingSectionGrid2.k-grid tr td,
#kostraCityRankingSectionGrid3.k-grid tr td,
#kostraCityRankingSectionGrid4.k-grid tr td,
#kostraCityRankingSectionGrid5.k-grid tr td {
    border-left: none;
    text-align: right;
}

#kostraCityRankingSectionGrid1,
#kostraCityRankingSectionGrid2,
#kostraCityRankingSectionGrid3,
#kostraCityRankingSectionGrid4,
#kostraCityRankingSectionGrid5 {
    border: none;
    margin: 8px;
    border-color: #c3c3c3;
}

.cityrankingbottom {
    margin-bottom: -40px;
}

#awesomeCloudcityRankingWordCloud {
    z-index: 10 !important;
}
/*City ranking CSS end*/

/*population statistics CSS start*/
.population-dropdownrow {
    margin-top: -20px;
}

.learn-more-section {
    margin-top: 0px;
    padding-top: 10px;
}

.population-section-tab {
    margin-top: 5px;
    padding-top: 10px;
    background: #ffffff;
}

.population-nav-tab {
    border: none;
    padding: 0;
    margin-left: 126px;
    margin-bottom: 2px;
}

.population-section-tablink {
    margin-right: 7px;
    color: #000;
    width: 100px;
    padding: 0px;
    padding-left: 4px;
    margin-left: 4px;
}

.population-section > .population-section-tablink > a {
    background-color: #B2B2B2;
    padding: 2px;
    text-align: center;
    border-radius: 4px;
    height: 24px;
    color: #fff;
}

.population-section > .population-section-tablink .active > a.selected {
    background-color: #377B9A;
    color: #ffffff;
    border-color: #30697D;
}

#population.nav-tabs > li.active > a {
    background-color: #377B9A;
    color: #ffffff;
    border-radius: 4px;
    border-color: #30697D;
}

.population-tabcontent {
    margin-top: 0px;
    padding-top: 0px;
}

#populationStatDetailChart1 circle {
    display: none !important;
}

#forcastchart circle {
    display: none !important;
}

#populationstatgraph2 circle {
    display: none !important;
}


.populationstatistics-explanation-border {
    border-radius: 5px 5px 0px 0px;
}

.populationstatistics-explanation, .populationstatistics-evaluation {
    padding-bottom: 26px;
    top: 13px;
    color: #000;
}

.populationstatistics-bckground {
    background: #f4f4f4;
    padding-bottom: 15px;
    padding-top: 15px;
}

.populationstatistics-evaluation-border {
    border-radius: 5px 5px 0px 0px;
}

.populationstatistics-formarea {
    background: #f4f4f4;
    padding-bottom: 15px;
    padding-top: 15px;
}

#chkPopulationstatisticsIncluded, #chkPopulationstatisticsLockContent {
    border-color: #6FA1B2;
}

.btn-custom-populationstatistics {
    width: 95%;
    font-size: 11px;
    padding: 2px;
    height: 34px;
    margin-bottom: 10px;
    white-space: normal;
}

.pop-stat-colour-formatting {
    color: #ffffff;
    border-radius: 4px;
    border-color: #909090;
    background-color: #B2B2B2;
}

.pop-stat-resume-colour-formatting {
    color: #ffffff;
    border-radius: 4px;
    border-color: #285e8e;
    background-color: #347B9A;
}

.population-statistics-main-bg {
    background-image: url(../images/population_statistics_pano.jpg);
    background-repeat: no-repeat;
    background-size: 100%;
}

.popgrid-display {
    display: none;
}

/*Population statistics CSS end*/


/*pupulation stat detail scree*/
.populationStatDetail-section {
    border: none;
    padding: 0;
    margin-left: 60px;
    padding-bottom: 13px;
}

.populationStatDetail-section-tablink {
    margin-right: 7px;
    color: #000;
    width: 105px;
    padding: 0px;
    padding-left: 4px;
    margin-left: 4px;
}

#populationStatDetailGrid1 .k-grid-header {
    padding: 0 !important;
}

#populationStatDetailGrid1 .k-grid-content {
    overflow-y: visible;
}

.populationstatistics-evaluation-txtarea,
.populationstatistics-explanation-txtarea {
    background: #fff;
}
/*population stat detail end*/


/*New Kostra Overview Begin*/

#kostraServiceOverviewGridEvaluation.k-grid tr td,
#kostraServiceOverviewGridProductivity.k-grid tr td,
#kostraServiceOverviewGridCoverage.k-grid tr td,
#kostraServiceOverviewGridQuality.k-grid tr td,
#kostraServiceOverviewGridPriority.k-grid tr td,
#kostraServiceOverviewGridAdministration.k-grid tr td,
#kostraServiceOverviewGridFinancials.k-grid tr td,
#kostraOverviewGridWithFilter.k-grid tr td,
#kostraServiceOverviewGridEvaluation1.k-grid tr td,
#kostraOverviewGridWithFilter1.k-grid tr td {
    border-left: none;
    text-align: right;
    white-space: nowrap;
}

.border-kostra-explanation, .border-kostra-evaluation {
    border: 1px solid #c3c3c3;
    cursor: pointer;
    background-color: #fff;
    padding-top: 15px;
    margin-top: 7px;
}

.kostraoverview-explanation-border {
    background: #fff;
}

.txt-kostraoverview-explanation,
.txt-kostraoverview-evaluation {
    padding-bottom: 10px;
    top: 0px;
    color: #000;
}

.kostraoverview-explanation-bckground {
    background: #f4f4f4;
    padding-bottom: 15px;
}

.kostraoverview-explanation-txtarea {
    margin-top: -3px;
    margin-left: -30px;
    width: 643px;
    border: none;
}

.kostraoverview-evaluation-txtarea {
    margin-top: -3px;
    margin-left: -15px;
    width: 595px;
    border: none;
}

.kostra-overview-explanation,
.kostra-overview-evaluation {
    margin-top: 12px;
}

.kostraserviceoverview-grid-background {
    background: #F4F4F4;
    padding-bottom: 15px;
}

.kostraserviceoverview-grid {
    margin-top: 10px;
    border-color: #c3c3c3;
}

.acc-kostraoverview-header-shape {
    border-radius: 5px 5px 1px 1px;
}

.acc-kostraoverview-title {
    min-height: 60px;
}

.acc-kostraoverview-header-left {
    left: -5px;
    bottom: -3px;
    position: relative;
    margin-bottom: 2px;
}

.acc-kostraoverview-header-right {
    position: relative;
    bottom: -15px;
    right: -14px;
}


.bckgrnd-kostraoverview-explanation-txtarea,
.bckgrnd-kostraoverview-evaluation-txtarea {
    background: #fff;
}

.k-window-title .ProfitVsRevenueWindow_wnd_title,
.k-window-title .budgetManagementReseveFundWindow_wnd_title {
    margin-left: 10px;
    margin-top: -2px;
}

.kostraoverview-profitVsrevenue-window,
.kostraoverview-reservefund-window,
.kostraoverview-debtvsrevenue-window,
.kostraoverview-debtpercitizen-window {
    width: 580px;
    height: 511px;
    margin-left: 10px;
    margin-top: 12px;
}


.kostraoverview-profitVsrevenue-bckground,
.kostraoverview-reservefund-bckground,
.kostraoverview-debtvsrevenue-bckground,
.kostraoverview-debtpercitizen-bckground {
    background: #fff;
    width: 555px;
    margin-top: -3px;
}

.kostraoverview-profitVsrevenue-border,
.kostraoverview-reservefund-border,
.kostraoverview-debtvsrevenue-border,
.kostraoverview-debtpercitizen-border {
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #ccc;
    padding-top: 4px;
    margin-top: -13px;
}

#KostraOverviewProfitVsRevenueWindow,
#KostraOverviewreserveFundWindow,
#KostraOverviewDebtvsRevenueWindow,
#KostraOverviewDebtPerCitizen {
    margin-top: 10px;
}

#KostraOverviewProfitVsRevenueGrid.k-grid tr td,
#KostraOverviewreserveFundGrid.k-grid tr td,
#KostraOverviewDebtvsRevenueGrid.k-grid tr td,
#KostraOverviewDebtPerCitizenGrid.k-grid tr td {
    border-left: none;
    text-align: right;
}

#KostraOverviewProfitVsRevenueGrid,
#KostraOverviewreserveFundGrid,
#KostraOverviewDebtvsRevenueGrid,
#KostraOverviewDebtPerCitizenGrid {
    border: none;
}

.btn-custom-overview {
    width: 15%;
    font-size: 14px;
    padding: 2px;
}

.btnCancel {
    color: #347B9A;
    background-color: #fff;
    border-color: #fff;
}

.kostra-overview-evaluation {
    padding-right: 2px;
}

.kostraoverview-lockcontent {
    margin-left: -6px;
}

.kostraoverview-explanation-division {
    border: dotted 1px #c3c3c3;
    margin-top: 1px;
    margin-left: -15px;
    width: 610px;
    margin-bottom: 2px;
}

.kostraoverview-evaluation-division {
    border: dotted 1px #c3c3c3;
    margin-top: 1px;
    margin-left: -15px;
    width: 592px;
    margin-bottom: 2px;
}

/* New Kostra Overview End*/


/*Map page CSS Start*/
.kostramap-selectcomparision-border {
    padding-bottom: 26px;
    top: 13px;
    color: #347B9A;
}

.kostramap-selectcomparision-border, .kostra-map-select-kostra-border {
    border-radius: 5px 5px 0px 0px;
}

.kostra-productivity-explanation {
    padding-bottom: 26px;
    top: 13px;
    color: #347B9A;
}

.kostra-map-selectcomparision, .kostra-map-selectkostragp, .kostra-map-selectkostraregion, .kostra-map-norwaymap-header, .kostra-map-select10-header {
    padding-bottom: 26px !important;
    top: 13px;
    color: #6E6E6E;
}

.kostra-map-comparisionsection, .kostra-map-select10 {
    background: #f4f4f4;
    padding-bottom: 15px;
}

.kostra-map-comparisionsection-border, .kostra-map-select10-border, .kostra-map-group-border, .kostra-map-region-border {
    margin-top: 15px;
    padding-top: 15px;
    background: #ffffff;
}

.kostra-map-slider-section {
    left: 4px;
    bottom: -10px;
    top: 5px;
    position: relative;
    margin-bottom: 10px;
}

#rangeMapSlider1,
#rangeMapSlider2,
#rangeMapSlider3,
#rangeMapSlider4,
#rangeMapSlider5,
#rangeMapSlider6,
#rangeMapSlider7,
#rangeMapSlider8,
#rangeMapSlider9,
#rangeMapSlider10,
#rangeMapSlider11,
#rangeMapSlider12 {
    width: 357px;
    margin-left: 14px;
    margin-top: 2px;
    margin-bottom: 30px;
}


.kostra-map-selectkostragp-section, .kostra-map-selectkostraregion-section {
    background: #f4f4f4;
    padding-bottom: 15px;
}

.kostra-map-selectkostragp-bckground, .kostra-map-selectkostraregion-bckground {
    background-color: #fff;
    margin-top: 12px;
}

.kostra-map {
    background-color: #fff;
}

.kostra-map-section {
    padding: 15px;
    margin-top: 45px;
    background-color: #f4f4f4;
}

.btn-kostramap-saveasdefault {
    background-color: #347B9A;
    color: #F8F8F8;
    margin-top: 10px;
    padding-left: 14px;
    padding-right: 14px;
    margin-left: 90px;
}

.btn-kostramap-save {
    background-color: #347B9A;
    color: #F8F8F8;
    margin-top: 10px;
    padding-left: 46px;
    padding-right: 49px;
    margin-left: 50px;
}

.btn-kostramap-reset {
    background-color: #347B9A;
    color: #F8F8F8;
    margin-top: 10px;
    padding-left: 48px;
    padding-right: 48px;
}

.k-notification-upload-success {
    opacity: 0.6;
    border: none !important;
}

.upload-success {
    background: rgba(0, 153, 101, 0.73);
    color: #fff;
    width: 362px;
    min-height: 108px;
    line-height: 100px;
}

.new-notification {
    background: #000;
    width: 362px;
    height: 124px;
    padding-top: 15px;
    line-height: 100px;
}

.upload-success h3 {
    font-size: 1.4em;
    font-weight: normal;
    display: inline-block;
    vertical-align: middle;
    color: #fff;
}

.upload-success img {
    display: inline-block;
    vertical-align: middle;
    margin-right: 10px;
}

#KostraMapListView {
    max-height: 500px;
    overflow: auto;
    overflow-x: hidden;
}

#KostraMapListView, #KostraGroupListView, #KostraRegionListView, #KostraMapListViewFirst {
    border: none;
}

.kostra-default-cities {
    float: left;
    position: relative;
    width: 170px;
    height: 25px;
    padding: 0;
    margin: 5px 0 5px 20px;
}

.kostra-cities, .kostra-cities-first {
    float: left;
    position: relative;
    width: 240px;
    height: 25px;
    padding: 0;
    margin: 5px 0 5px 20px;
}

.kostra-groups {
    float: left;
    position: relative;
    width: 176px;
    height: 28px;
    margin: 5px 0 5px 20px;
    padding: 0px;
    bottom: 5px;
}

.kostra-regions {
    float: left;
    position: relative;
    width: 176px;
    height: 28px;
    margin: 5px 0 5px 20px;
    padding: 0px;
    bottom: 5px;
}


.kostra-cities p {
    visibility: hidden;
}

.k-listview:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}

#KostaMapPagerForCities {
    margin-top: 15px;
    border-color: transparent;
    background: #f4f4f4;
    margin-bottom: 10px;
}


    #KostaMapPagerForCities .k-state-selected {
        background-color: #67AFE9 !important;
        margin-bottom: 2px !important;
        border-color: #67AFE9 !important;
    }

.kostramap-section2 {
    padding-right: 0px;
}


.chklbl-nationalavg {
    margin-left: 23px;
}

.chklbl-regionalavg {
    margin-left: 10px;
}

#inlineCityNationAverage,
#inlineRegionalAverage,
#inlineKostraGpAverage,
#kostraMapkostraGroupCheck,
#kostraMapRegionCheck {
    margin-left: -16px;
}


/* Financial Overview Start */

.not-active {
    pointer-events: none;
    cursor: default;
    opacity: 0.6;
}

/* Financial Overview End */


/*Investment Overview Start*/

#invOverviewConsolidatedGrid {
    overflow-x: hidden !important;
}

.maximize-wrapper #investmentGridWrapper .k-grid-toolbar {
    position: fixed;
    z-index: 99999;
    bottom: 7px;
}

#investmentTabSec li.active {
    position: relative;
    z-index: 5;
    border-radius: 0 !important;
    border-bottom: 0 !important;
}

.investment-overview-bg {
    background-image: url('../images/investment_banner.jpg');
    background-repeat: no-repeat;
    background-size: 100%;
}

.investment-project-bg {
    background-image: url('../images/investment.jpg');
    background-repeat: no-repeat;
    background-size: 100%;
}

.investment-project-bg-height {
    min-height: 250px !important;
}

.investment-overview-command-style {
    background-color: #fff;
    border: 1px solid #c3c3c3;
    border-top: none;
    padding-bottom: 25px !important;
}

.investment-nav-tab {
    border: none;
    padding: 0px;
    margin-left: 0px;
    margin-bottom: 2px;
}

.investment-section-tab {
    padding-top: 10px;
    margin-top: 10px;
    background: transparent;
}

#investmentSections.nav-tabs > li.active > a {
    background-color: #377B9A;
    color: #ffffff;
    border-radius: 4px;
    border-color: #30697D;
}

#investmentSections.nav-tabs > li > a {
    padding: 2px;
    text-align: center;
    min-height: 40px;
    width: 106px;
}


.investment-section-tablink {
    margin-right: 7px;
    color: #000;
    width: 105px;
    padding: 0px;
    padding-left: 4px;
    margin-left: 4px;
}

.investment-section > .investment-section-tablink > a {
    background-color: #B2B2B2;
    padding: 2px;
    text-align: center;
    border-radius: 4px;
    height: 24px;
    color: #fff;
}

.investment-section > .investment-section-tablink .active > a.selected {
    background-color: #377B9A;
    color: #ffffff;
    border-color: #30697D;
}

.investment-tabcontent {
    margin-top: 0px;
    padding-top: 0px;
}

.investment-overview-grid-bottom {
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
}

#investmentOverviewServiceAreaContainer .k-dropdown {
    width: 25%;
}

#actionImportBudgetChangesContainer .k-dropdown {
    width: 80% !important;
}

#investmentOverviewFilterContainer .k-dropdown {
    width: 50%;
}

#budgetEntryDrpDwnContainer .k-combobox {
    width: 50%;
}

.invest-export-top {
    margin-top: -8px;
}


.inv-overview-checkbox-style {
    width: 20px;
    height: 13px;
    padding: 0;
    margin: 0;
    vertical-align: bottom;
    position: relative;
    top: -2px;
}

.inv-overview-checkbox-lbl-style {
    display: block;
    padding-left: 15px;
    text-indent: -15px;
}

.investment-overview-overall-section {
    background-color: #fff;
    border: 1px solid #c3c3c3;
}

.overallgrid-border-style {
    border-left: none;
    border-right: none;
    border-bottom: none;
}
/*Investment Overview End*/


/*Investment Detail CSS*/



.inv-numeric-expand-padding {
    padding-right: 4px;
}

.validation-hide {
    display: none !important;
}

.investment-detail-command-style {
    background-color: #F4F4F4 !important;
    border: 1px solid #c3c3c3;
    border-top: 1px solid #c3c3c3;
    padding-bottom: 5px !important;
}

#investmentDetailGrid1 .k-grid-header {
    padding: 0 !important;
}

.inv-detail-hide {
    display: none !important;
}

#investmentDetailGrid1 .k-grid-content {
    overflow-y: visible;
}

.investment-projectinfo-border {
    border-radius: 5px 5px 0px 0px;
}

.investment-detail-header {
    cursor: pointer;
    min-height: 45px;
}

.investment-projectinfo {
    padding-bottom: 26px;
    top: 13px;
    color: #000;
}

.investment-projectinfo-bckarea {
    margin-top: 15px;
    background: #fff;
}

.projectinfo-description {
    margin-top: 10px;
    margin-left: 15px;
    margin-bottom: 0px;
}


.project-info-section-bckground {
    border: 1px solid #C3C3C3;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    background-color: #fff;
    margin-top: 15px;
    margin-bottom: 15px;
    padding: 10px;
    padding-top: 20px;
    padding-bottom: 20px;
}

.inv-detail-editor-style {
    background-color: #fff;
    width: 602px;
}

.investment-import-style {
    margin-top: 4px;
    padding: 2px 2px;
    display: inline-block;
    padding-top: 0px;
    margin-bottom: 0;
    font-size: 12px;
    font-weight: normal;
    line-height: 1.42857143;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-image: none;
    border-radius: 4px;
}

.investment-export-style {
    display: inline-block !important;
    padding: 4px 5px !important;
    margin-bottom: 0 !important;
    font-size: 12px !important;
    font-weight: normal !important;
    line-height: 1.42857143 !important;
    text-align: center !important;
    white-space: nowrap !important;
    vertical-align: middle !important;
    cursor: pointer !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    background-image: none !important;
    border: 1px solid #c3c3c3 !important;
    border-radius: 4px !important;
}

@media (min-width: 1366px) {
    .upload_button_txt {
        padding-right: 62px;
        background-image: url(../images/upload_investment.png);
        background-repeat: no-repeat;
        background-position: 0px;
        background-position-x: 94px;
    }

    #populateStatSectionsTabWrap {
        max-height: 240px !important;
        overflow-y: auto;
    }
}

@media (min-width: 1600px) {
    .upload_button_txt {
        padding-right: 45px;
        background-image: url(../images/upload_investment.png);
        background-repeat: no-repeat;
        background-position: 0px;
        background-position-x: 105px;
    }

    #finPlanImportSection {
        margin-left: 110px !important;
    }
}

#investmentDetailServiceAreaContainer .k-dropdown {
    min-width: 90% !important;
    width: 90%;
}

#investmentDetailInvPhaseContainer .k-dropdown {
    min-width: 90% !important;
    width: 90%;
}

#investmentDetailInvStatusContainer .k-dropdown {
    min-width: 90% !important;
    width: 90%;
}

#investmentDetailPriorityContainer .k-dropdown {
    min-width: 90% !important;
    width: 90%;
}

#investmentDetailInvProjCodeContainer .k-combobox {
    min-width: 90% !important;
    width: 90%;
}

#investmentDetailPortFolioContainer .k-dropdown {
    min-width: 90% !important;
    width: 90%;
}

#investmentDetailInvTypeContainer .k-dropdown {
    min-width: 90% !important;
    width: 90%;
}

#investmentDetailEstYearContainer .k-dropdown {
    min-width: 90% !important;
    width: 90%;
}

#investmentDetailStartYearContainer .k-datepicker .k-header {
    min-width: 90% !important;
    width: 90% !important;
}

.investment-projectinfo-bckground {
    background: #f4f4f4 !important;
}

.investment-detail-height {
    margin-top: 20px;
}

.inv-val-name-style {
    margin-left: -15px;
    margin-top: 3px;
}

.inv-val-service-area {
    margin-left: 0px;
    margin-top: 3px;
}

.inv-val-start-year {
    margin-left: 0px;
    margin-top: -3px;
}

.inv-val-est-completion {
    margin-left: 0px;
    margin-top: -3px;
}

#investmentDetailInputName {
    width: 96%;
    border-color: #6FA1B2 !important;
    border-width: thin;
    border-style: solid;
    padding: 6px;
}

.investment-detail-compeletion-width {
    padding: 5px;
    border-color: #6FA1B2 !important;
    border-width: thin;
    border-style: solid;
    border-radius: 4px;
}

#invDetailCompletionContainer .k-numerictextbox {
    width: 94%;
}

#invDetailFinAmountContainer .k-numerictextbox,
.inv-detail-org-finish-width,
#invDetailPrevBudgetedContainer .k-numerictextbox,
#invDetailAppCostContainer .k-numerictextbox, 
#invDetailPrevActualContainer .k-numerictextbox,
#invDetailUnitContainer .k-numerictextbox {
    width: 90%;
}

.investment-detail-input-width {
    width: 100%;
    padding: 5px;
    border-color: #6FA1B2 !important;
    border-width: thin;
    border-style: solid;
    border-radius: 4px;
}

.climate-detail-input-width {
    padding: 5px;
    border-color: #6FA1B2 !important;
    border-width: thin;
    border-style: solid;
    border-radius: 4px;
    width: 100%;
}

.investment-detail-app-cost-width {
    width: 90%;
    padding: 5px;
    border-color: #6FA1B2 !important;
    border-width: thin;
    border-style: solid;
    border-radius: 4px;
}

.investment-detail-app-ref-width,
.investment-detail-app-ref-url-width {
    width: 100%;
    padding: 5px;
    border-color: #6FA1B2 !important;
    border-width: thin;
    border-style: solid;
    border-radius: 4px;
    height: 31px;
}

.investment-detail-app-cost .k-input,
.inv-detail-prev-budgeted .k-input,
.inv-detail-unit-sec .k-input,
.inv-detail-completion-sec .k-input,
.inv-detail-prev-fin-amt .k-input {
    text-align: right;
}

#invDetailApprovedContainer .k-datepicker,
#investmentDetailApprovedDatePicker_dateview.k-calendar,
#invDetailProjectPhaseContainer .k-dropdown {
    width: 94%;
}

#investmentDetailFieldsSection {
    margin-left: -2%;
}

.investment-detail-dropdwn-border {
    border-color: #6FA1B2 !important;
    border-width: thin;
    border-style: hidden;
    width: 94% !important;
}

.investment-detail-lbl-reports-approved-left {
    margin-right: 0%;
}

.investment-detail-multi-width {
    width: 116% !important;
}

.investment-detail-save {
    width: 70px;
    height: 30px;
}

.command-header-changes {
    background-color: #fff;
    border: 1px solid #c3c3c3;
}

.investment-detail-grid-bottom {
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
    border-bottom: none;
}

.cmn-background1, .investment-detail-custom-colour {
    background-color: #377B9A;
}

.cmn-background2 {
    background: #f4f4f4 !important;
}

.cmn-background3 {
    background: #377B9A !important;
}

.budget-changes-bg {
    background-image: url('../images/display_budget_changes.jpeg');
}

.addnew-record-btn {
    background: transparent !important;
    padding-top: 3px !important;
    padding-bottom: 3px !important;
    margin: 0 !important;
}

.investment-detail-custom-border {
    border-top: 1px solid #c3c3c3;
}

.investment-detail-btn-add {
    background-image: url(../images/add.png);
    background-position-x: -4px;
    background-position-y: -3px;
    border-color: transparent;
    padding: 8px;
    margin-left: 27px;
    background-repeat: no-repeat;
    border: transparent;
    color: #fff;
    height: 14px;
    margin-right: 4px;
    vertical-align: middle;
}

.margin-spacing {
    margin-top: 5px;
    margin-bottom: 5px;
}

.btn-investment {
    display: inline-block;
    padding: 5px 12px;
    font-size: 12px;
    font-weight: normal;
    line-height: 1.42857143;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 4px;
    margin-top: 4px;
    margin-bottom: 4px;
}

.edit-padding {
    padding-left: 22px;
    padding-right: 22px;
}

.investmentdetail-border-style {
    border-color: #6FA1B2 !important;
    border-width: thin;
    border-style: solid;
}

.txtarea-border {
    border-color: #6FA1B2 !important;
    border-width: thin;
    border-style: solid;
}

.investmentdetail-button-disabled {
    color: #ffffff;
    border-radius: 4px;
    border-color: #909090;
    background-color: #B2B2B2;
}

.btn-primary[disabled],
.btn-action-primary[disabled],
.btn-primary:hover[disabled],
.btn-action-primary:hover[disabled] {
    border-color: #909090;
    background-color: #B2B2B2;
}

.btn-primary.disabled,
.btn-primary.disabled:hover,
.btn-primary.not-active,
.btn-primary.not-active:hover {
    border-color: #909090;
    background-color: #B2B2B2;
}

.cmn-save-close-btn.not-active {
    border-color: #909090;
    color: #B2B2B2;
}

.investment-detail-autocomplete-style {
    display: inline-block;
    background: none;
    box-shadow: none;
    padding: 0px;
}

.alert-img {
    background-image: url(../images/alert.png);
    background-size: cover;
    background-repeat: no-repeat;
    margin-top: 3px;
    padding: 9px;
}

.insert-validation-lbl {
    content: "*";
    color: red;
}

.inv-sec-header-padding {
    padding-top: 20px !important;
    padding-bottom: 20px !important;
    padding-left: 10px !important;
    padding-right: 10px !important;
}

.investment-validation-icon {
    background-image: url(../images/validation_small.png);
    background-repeat: no-repeat;
    height: 20px;
    width: 20px;
}

.investment-validation-align {
    margin-left: 4px;
    margin-top: 0px;
    padding: 2px;
}

.inv-validation-top {
    margin-left: 0px;
    margin-top: -3px;
}

.login-screen-bg {
    background: rgba(27, 43, 106, 0.4) 0% 0% no-repeat padding-box;
    background-image: url('../vc_branding/images/login_screen_bg_v1.jpg');
    min-height: 100vh;
    min-width: 100vw;
    background-size: cover;
}

.login-info-content-wrap {
    position: absolute;
    top: 18vh;
    left: 15vw;
}

.content-box-bg {
    background: #1B2B6A 0% 0% no-repeat padding-box;
    box-shadow: 0px 2px 3px #0000001C;
    border-radius: 10px;
    width: 630px;
    padding: 1.3rem 2rem;
    color: #fff;
}

.login-top-content {
    min-height: 336px;
}

.login-bottom-content {
    min-height: 142px;
}

.login-btn {
    background: #fff 0% 0% no-repeat padding-box;
    box-shadow: none;
    border-radius: 10px;
    width: 119px;
    height: 39px;
    color: #000;
    padding-left: 0;
    border: 1px solid #fff;
}

    .login-btn:hover {
        background: #ebefff 0% 0% no-repeat padding-box;
    }

.login-footer {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 45px;
    padding-right: 8px;
}

/*Investment CSS end*/
@media (min-width: 1000px) {
    .brand {
        width: 227px;
    }

    .nav {
        padding-left: 0px;
    }

    .notify-row {
        margin-left: 15px;
    }

    ul.top-menu {
        margin-right: 4px;
    }

        ul.top-menu > li > a {
            margin-right: 4px;
        }

    .merge-left div.kpi-indicator-box {
        height: 123px;
    }

    .merge-left div.kostra-indicator-box {
        height: 123px;
    }

    .merge-left div #chart1, div #chart2, div #chart3, div #chart4 {
        height: 83px !important;
    }

    #chart1, #chart2, #chart3, #chart4 {
        height: 75px !important;
    }

    #politicalSimulationKPIchart1, #politicalSimulationKPIchart2, #politicalSimulationKPIchart3, #politicalSimulationKPIchart4 {
        height: 75px !important;
    }

    .kostra-indicator-box {
        height: 123px;
    }

    .kpi-indicator-box {
        height: 103px;
    }

    #financechart1, #financechart2, #financechart3, #financechart4 {
        height: 55px !important;
    }

    .learn-label {
        padding-top: 280px;
    }

    .merge-left div.learn-label {
        padding-top: 300px;
    }

    .nav-tabs > li {
        margin-bottom: 4px;
    }

    .kostra-detail-border {
        min-height: 55px;
    }

    .inc-font {
        font-size: 11px;
    }

    .kpi-title {
        height: 32px;
    }

    .wordcloud {
        height: 265px;
        width: 450px;
    }

    .merge-left div.wordcloud {
        height: 280px;
        width: 450px;
    }

    .population-nav-tab {
        margin-left: 60px;
    }

    .popstatchart {
        height: 250px;
    }

    .merge-left div.popstatchart {
        height: 230px;
    }

    .population-statistics-main-bg {
        min-height: 342px;
    }

    .bm-overview-cmn-chart {
        height: 350px;
    }

    .merge-left div.bm-overview-cmn-chart {
        height: 370px;
    }

    .kostra-revenue-slider {
        width: 395px;
        margin-left: 8px;
    }

    .map-slider {
        width: 263px;
        height: 0px;
    }

    .kostra-groups {
        width: 110px;
    }

    .kostra-regions {
        width: 115px;
    }

    .kostra-default-cities, .kostra-cities, .kostra-cities-first {
        width: 165px;
    }

    /*opportunity ass*/
    .opportunity-assessment-bg {
        height: 342px;
    }

    .investment-overview-bg {
        height: 342px;
    }

    .investment-project-bg {
        height: 342px;
    }

    /* financing overview start */

    .financing-overview-bg {
        height: 342px;
    }

    /* financing overview end */

    .financing-project-bg {
        height: 342px;
    }

    .cons-right-bg {
        min-height: 352px;
    }
    /*Budget assumption*/
    .budget-assumption-bg {
        min-height: 342px;
    }

    .bud-mang-right-bg {
        min-height: 320px !important;
    }
    /*budget management workflow section*/
    .bm-workflow-parent-style {
        height: 40px;
        border-radius: 18px;
        padding: 0px;
    }

    .bm-workflow-number-style {
        padding-top: 10px !important;
        border-radius: 50% !important;
        height: 38px !important;
        width: 38px !important;
        padding-left: 14px !important;
    }

    .bm-workflow-txt-A {
        font-size: 9px;
        margin-top: 7px;
    }

    .bm-workflow-txt-B {
        font-size: 9px;
        margin-top: 10px;
    }

    .bm-workflow-txt-C {
        font-size: 9px;
        margin-top: 10px;
    }

    .bm-workflow-txt-D {
        font-size: 9px;
        margin-top: 10px;
    }

    .bmWorkFlowDescTxtStyle {
        margin-left: -1px;
        text-align: left;
        font-size: 9px;
    }
    /*budget management workflow section*/

    .service-area-nav-tab {
        margin-left: 12px;
    }

    .bud-proposal-right-bg {
        min-height: 320px !important;
    }

    .bud-task-input {
        width: 178px;
        padding: 5px;
        border-color: #6FA1B2 !important;
        border-width: thin;
        border-style: solid;
        border-radius: 4px;
    }

    .bud-task-small-input {
        padding: 5px;
        width: 50px;
        border-color: #6FA1B2 !important;
        border-width: thin;
        border-style: solid;
        border-radius: 4px;
    }

    .bud-goal-text-input {
        border: 1px solid #377b9a;
        border-radius: 5px;
        height: 26px;
    }

    /*staff planning*/
    .banner-background-style {
        min-height: 510px;
    }

    .MR-banner-grid-chart {
        height: 265px;
    }

    .yb-banner-left-grid {
        height: 183px !important;
    }

    .YB-banner-grid-chart {
        height: 274px;
    }

    .action-import-bg {
        min-height: 342px;
    }

    /*Document Export reporting*/
    .docexport-report-bg {
        min-height: 342px;
    }

    .month-year-left, .month-year-right {
        top: 0px;
        width: 20px;
        padding: 0px;
        height: 29px;
        background: #fff;
        border: 1px solid #6fa1b2;
    }

    /*Log on page css*/
    .log-on-copyright {
        position: fixed;
    }

    .log-on-error-bg {
        background-image: url('../images/log_on_banner.jpg');
        background-repeat: no-repeat;
        min-height: 707px !important;
        background-size: 1024px 707px;
    }

    .error-right-bg {
        height: 707px !important;
    }

    .error-logo-top {
        margin-top: 40px;
    }

    .error-top-line-one {
        margin-top: 40px;
    }

    .error-top-line-two {
        margin-top: 20px;
    }

    .error-top-line-three {
        margin-top: 40px;
    }

    .error-top-line-four {
        margin-top: 40px;
    }

    .error-bottom-copyright {
        margin-top: 275px;
    }

    .logout-bottom-copyright {
        margin-top: 345px;
    }
    /*Log on page css*/

    /*Investment overview css start*/
    .inv-overview-header-spacing {
        padding-left: 10px !important;
    }

    .inv-ovr-chk-style-1 {
        width: 10% !important;
        margin-top: 20px;
        margin-left: 17px;
    }

    .inv-ovr-chk-style-2 {
        width: 17% !important;
        margin-top: 20px;
        margin-left: 0px;
    }

    .inv-ovr-chk-style-3 {
        width: 17% !important;
        margin-top: 20px;
        margin-left: 0px;
    }

    .inv-export-left {
        margin-left: -25px;
    }
    /*Investment overview css end*/


    /*Kostra Detail CSS*/
    .kostra-section-detail-tablink {
        min-width: 43px;
        margin-left: 6px;
    }

    .kostra-detail-include-link {
        min-width: 45px;
        margin-left: 4px;
    }

    .kostra-detail-include-link-temp {
        min-width: 42px;
        margin-left: 4px;
    }
    /*Kostra Detail CSS*/
    /*Investment import export button section*/
    .inv-header-btn-padding {
        padding-left: 3px;
    }
    /*Investment import export button section*/

    /*Budget production*/
    .doc-tree-check-left {
        left: -22%;
    }

    .org-str-warning-container-width {
        width: 106.4%;
    }
    /*Budget production*/

    /*pop analysis css start*/
    .pop-analysis-year-tab-left {
        margin-left: 0% !important;
    }

    .pop-analysis-country-tab-left {
        margin-left: 1% !important;
    }

    .pop-analysis-region-tab-left {
        margin-left: 2% !important;
    }

    .pop-analysis-kostra-tab-left {
        margin-left: 3% !important;
    }

    .pop-analysis-sort-tab-left {
        margin-left: 4% !important;
    }
    /*pop analysis css end*/

    .ws-industry-tab-left {
        margin-left: 1% !important;
    }

    .ws-total-tab-left {
        margin-left: 2% !important;
    }

    .ws-country-tab-left {
        margin-left: 2% !important;
    }

    .ws-kostra-tab-left {
        margin-left: 2% !important;
    }
}

@media (min-width: 1200px) {
    .brand {
        width: 240px;
    }

    .nav {
        padding-left: 25px;
    }

    .notify-row {
        margin-left: 25px;
    }

    ul.top-menu {
        margin-right: 15px;
    }

        ul.top-menu > li > a {
            margin-right: 15px;
        }

    .kpi-title {
        height: 36px;
    }

    .merge-left div.kostra-indicator-box {
        height: 160px;
    }

    .kostra-indicator-box {
        height: 164px;
    }

    .merge-left div #chart1, div #chart2, div #chart3, div #chart4 {
        height: 114px !important;
    }

    #chart1, #chart2, #chart3, #chart4 {
        height: 105px !important;
    }

    #politicalSimulationKPIchart1, #politicalSimulationKPIchart2, #politicalSimulationKPIchart3, #politicalSimulationKPIchart4 {
        height: 105px !important;
    }

    .map-slider {
        width: 350px;
        height: 0px;
    }

    .nav-tabs {
        padding-left: 0px;
    }

    .kostra-default-cities, .kostra-cities, .kostra-cities-first {
        width: 160px;
    }

    .map-buttons {
        padding-left: 100px;
    }

    .inc-font {
        font-size: 12px;
    }

    .kostra-revenue-slider {
        width: 515px;
        margin-left: 8px;
    }

    .learn-label {
        padding-top: 387px;
    }

    .merge-left div.learn-label {
        padding-top: 409px;
    }

    .merge-left div.kpi-indicator-box {
        height: 160px;
    }

    .kpi-indicator-box {
        height: 144px;
    }

    #financechart1, #financechart2, #financechart3, #financechart4 {
        height: 95px !important;
    }

    .merge-left div.city-main-bg {
        min-height: 435px;
    }

    .city-main-bg, .investment-overview-bg, .financing-overview-bg {
        min-height: 408px;
    }

    .population-statistics-main-bg {
        min-height: 429px;
    }

    .popstatchart {
        height: 338px;
    }

    .merge-left div.popstatchart {
        height: 310px;
    }

    .bm-overview-cmn-chart {
        height: 382px;
    }

    .merge-left div.bm-overview-cmn-chart {
        height: 402px;
    }

    .popDetailChart {
        width: 600px !important;
    }

    .kostra-groups {
        width: 160px;
    }

    .kostra-regions {
        width: 160px;
    }

    .wordcloud {
        height: 353px;
    }

    .merge-left div.wordcloud {
        height: 375px;
    }

    #KostraOverviewWebViewChart {
        height: 550px !important;
    }

    #indicatorSearch {
        padding: 6px 22%;
        border-radius: 0px;
    }

    /* opportunity assessment*/
    .merge-left div.opportunity-assessment-bg {
        min-height: 435px;
    }

    .merge-left div.investment-overview-bg {
        min-height: 435px;
    }

    /* financing overview start */

    .merge-left div.financing-overview-bg {
        min-height: 435px;
    }

    .cons-right-bg {
        min-height: 440px;
    }
    /*Budget assumption*/
    .merge-left div.budget-assumption-bg {
        min-height: 435px;
    }

    .budget-assumption-bg {
        min-height: 408px;
    }

    .bud-ass-col3 {
        left: 4px;
        position: relative;
    }

    .bud-ass-col4 {
        left: 7px;
        position: relative;
    }

    .bud-ass-hdcol4 {
        position: relative;
        left: 6px;
    }

    .bud-ass-hdcol3 {
        position: relative;
        left: 33px !important;
    }

    .assump-radio-selectall {
        margin-left: 31px !important;
        position: relative;
        top: -16px;
    }

    .assump-radio-wage {
        margin-left: 260px !important;
        position: relative;
        top: -23px;
    }

    .assump-radio-price {
        margin-left: 73px !important;
        position: relative;
        top: -23px;
    }

    .assump-radio-deflator {
        margin-left: 75px !important;
        position: relative;
        top: -23px;
    }

    .assump-radio-noadj {
        margin-left: 75px !important;
        position: relative;
        top: -23px;
    }

    .bud-mang-right-bg {
        min-height: 409px !important;
    }
    /*budget management workflow section*/
    .bm-workflow-parent-style {
        height: 44px;
        border-radius: 21px;
        padding: 0px;
        width: 18%;
    }

    .bm-workflow-number-style {
        padding-top: 10px !important;
        border-radius: 50% !important;
        height: 42px !important;
        width: 42px !important;
        padding-left: 16px !important;
    }

    .bm-workflow-txt-A {
        font-size: 10px;
        margin-top: 3px;
    }

    .bm-workflow-txt-B {
        font-size: 10px;
        margin-top: 10px;
    }

    .bm-workflow-txt-C {
        font-size: 10px;
        margin-top: 10px;
    }

    .bm-workflow-txt-D {
        font-size: 10px;
        margin-top: 10px;
    }

    .bmWorkFlowDescTxtStyle {
        margin-left: -1px;
        text-align: left;
        font-size: 10px;
    }

    .work-flow-desc {
        width: 80%;
    }
    /*budget management workflow section*/
    /*financial plan tab start*/

    .budget-meeting-section {
        margin-left: 265px;
    }

    .bud-not-container {
        width: 49% !important;
    }

    .bud-act-col2 {
        text-align: left !important;
        width: 12% !important;
        position: relative;
        left: 40px;
    }

    .bud-act-col3 {
        position: relative;
        left: 56px;
        text-align: left !important;
    }

    .bud-act-col4 {
        position: relative;
        left: 46px;
    }

    .bud-act-col5 {
        position: relative;
        left: 52px;
        white-space: nowrap;
    }

    .bud-act-col6 {
        position: relative;
        left: 62px;
        white-space: nowrap;
    }

    .bud-act-col7 {
        position: relative;
        left: 72px;
        white-space: nowrap;
    }

    .bud-act-col8 {
        padding-right: 0px;
        white-space: nowrap;
        position: relative;
        left: 82px;
    }

    .bud-act-col9 {
        position: relative;
        left: 62px;
    }

    .bud-act-col10 {
        position: relative;
        left: 43px;
    }

    .bud-act-col11 {
        position: relative;
        left: 22px;
    }

    .bud-act-col12 {
        position: relative;
        left: 6px;
    }
    /*financial plan tab end*/
    /*budget process tab start*/
    .budget-process-drill-deadline {
        margin-left: -130px;
        width: 23%;
    }

    .budpro-task-addnew {
        width: 31%;
        margin-left: 11px;
    }

    .budget-process-drill-taskname {
        width: 26%;
        margin-left: 0px;
    }
    /*budget process tab end*/

    /*Budget management end*/

    .bud-proposal-right-bg {
        min-height: 409px !important;
    }
    /*Log on page css */
    .log-on-copyright {
        position: fixed;
    }

    .log-on-error-bg {
        background-image: url('../images/log_on_banner.jpg');
        background-repeat: no-repeat;
        min-height: 707px !important;
        background-size: 1280px 707px;
    }

    .error-right-bg {
        height: 707px !important;
    }

    .error-logo-top {
        margin-top: 40px;
    }

    .error-top-line-one {
        margin-top: 40px;
    }

    .error-top-line-two {
        margin-top: 20px;
    }

    .error-top-line-three {
        margin-top: 40px;
    }

    .error-top-line-four {
        margin-top: 40px;
    }

    .error-bottom-copyright {
        margin-top: 292px;
    }

    .logout-bottom-copyright {
        margin-top: 350px;
    }

    /*Log on page css end*/
    /*financial detail oppurtunity pop up start*/
    .fin-pop-col1 {
        left: 2px;
        position: relative;
    }

    .fin-pop-col2 {
        left: -3px;
        position: relative;
    }

    .fin-pop-col3 {
        left: -3px;
        position: relative;
    }

    .fin-pop-col4 {
        left: 3px;
        position: relative;
    }
    /*financial detail oppurtunity pop up end*/

    /*Investment overview css start*/
    .inv-overview-header-spacing {
        padding-left: 10px !important;
    }

    .inv-ovr-chk-style-1 {
        width: 12% !important;
        margin-top: 20px;
        margin-left: 40px;
    }

    .inv-ovr-chk-style-2 {
        width: 17% !important;
        margin-top: 20px;
        margin-left: 0px;
    }

    .inv-ovr-chk-style-3 {
        width: 17% !important;
        margin-top: 20px;
        margin-left: 0px;
    }

    .inv-export-left {
        margin-left: -25px;
    }
    /*Investment overview css end*/

    /*staff planning*/
    .staff-plan-right-bg {
        min-height: 409px !important;
    }

    #staffGrid {
        height: 515px;
    }

    #staffPositionSettings {
        width: 22%;
    }
    /*yearly budget*/
    .yearly-budget-right-bg {
        min-height: 409px !important;
    }

    .banner-background-style {
        min-height: 520px;
    }

    .MR-banner-grid-chart {
        height: 220px;
    }

    .yb-banner-left-grid {
        height: 230px !important;
    }

    .YB-banner-grid-chart {
        height: 262px;
    }

    .action-import-bg {
        min-height: 408px;
    }

    /*Document Export reporting*/
    .docexport-report-bg {
        min-height: 408px;
    }
    /*Kostra Reporting area tabs*/

    /*Kostra Detail CSS*/
    .kostra-section-detail-tablink {
        min-width: 57px;
        margin-left: 4px;
    }

    .kostra-detail-include-link {
        min-width: 53px;
        margin-left: 8px;
    }

    .kostra-detail-include-link-temp {
        min-width: 55px;
        margin-left: 4px;
    }
    /*Kostra Detail CSS*/

    /*Investment import export button section*/
    .inv-header-btn-padding {
        padding-left: 67px;
    }
    /*Investment import export button section*/
    .doc-tree-check-left {
        left: -8%;
    }

    .org-str-warning-container-width {
        width: 105%;
    }

    /*pop analysis css start*/
    .pop-analysis-year-tab-left {
        margin-left: 0% !important;
    }

    .pop-analysis-country-tab-left {
        margin-left: 1% !important;
    }

    .pop-analysis-region-tab-left {
        margin-left: 1% !important;
    }

    .pop-analysis-kostra-tab-left {
        margin-left: 3% !important;
    }

    .pop-analysis-sort-tab-left {
        margin-left: 3% !important;
    }
    /*pop analysis css end*/

    .ws-analysis-align {
        padding-right: 0px !important;
    }

    .ws-industry-tab-left {
        margin-left: 1% !important;
    }

    .ws-total-tab-left {
        margin-left: 2% !important;
    }

    .ws-country-tab-left {
        margin-left: 1% !important;
    }

    .ws-kostra-tab-left {
        margin-left: 2% !important;
    }
}

@media (min-width: 1900px) {
    .filter-doc-dropdown {
        width: 220px;
    }

    #populateStatSectionsTabWrap {
        max-height: 320px !important;
    }

    #finPlanImportSection {
        margin-left: 180px !important;
    }

    .ws-analysis-align {
        padding-right: 3px !important;
    }
}

@media (min-width: 1600px) {
    /*#populationStatChartForeCostTabs, #populationStatChartTabs {
	margin-left: 68px !important;
}*/
    #populateStatSectionsTabWrap {
        max-height: 270px !important;
    }

    .ws-analysis-align {
        padding-right: 3px !important;
    }
}

@media (max-width: 1024px) {
    #populateStatSectionsTabWrap {
        max-height: 120px !important;
    }

    .population-section-tab {
        padding-top: 0px !important;
    }

    #populateStatSections {
        padding-bottom: 0 !important;
    }

    #populateStatYearsTab button {
        height: 34px !important;
    }

    #staffPositionSettings {
        width: 27%;
    }

    .ws-analysis-align {
        padding-right: 0px !important;
    }
}

@media (min-width: 1300px) {
    .map-slider {
        width: 375px;
        /*margin-left:5px;*/
    }

    .inc-font {
        font-size: 12px;
    }

    .map-buttons {
        padding-left: 150px;
    }

    .kostra-default-cities, .kostra-cities, .kostra-cities-first {
        width: 165px;
    }

    .kostra-revenue-slider {
        width: 555px;
        margin-left: 8px;
    }

    .merge-left div.city-main-bg {
        min-height: 436px;
    }

    .city-main-bg {
        min-height: 436px;
    }

    .population-statistics-main-bg {
        min-height: 436px;
    }

    .banner-background-style {
        min-height: 490px;
    }

    .learn-label {
        padding-top: 417px;
    }

    .merge-left div.learn-label {
        padding-top: 437px;
    }

    .merge-left div.popstatchart {
        height: 342px;
    }

    .popstatchart {
        height: 367px;
    }

    .bm-overview-cmn-chart {
        height: 380px;
    }

    .merge-left div.bm-overview-cmn-chart {
        height: 400px;
    }


    #financechart1, #financechart2, #financechart3, #financechart4 {
        height: 150px !important;
    }

    .merge-left div #chart1, div #chart2, div #chart3, div #chart4 {
        height: 126px !important;
    }

    #chart1, #chart2, #chart3, #chart4 {
        height: 115px !important;
    }

    #politicalSimulationKPIchart1, #politicalSimulationKPIchart2, #politicalSimulationKPIchart3, #politicalSimulationKPIchart4 {
        height: 115px !important;
    }

    .kostra-indicator-box {
        height: 169px;
    }

    .merge-left div.kostra-indicator-box {
        height: 172px;
    }

    .kpi-indicator-box {
        height: 159px;
    }

    .merge-left div.kpi-indicator-box {
        height: 172px;
    }


    .merge-left div.budget-indicator-box {
        height: 169px;
    }

    .kostra-groups {
        width: 165px;
    }

    .kostra-regions {
        width: 165px;
    }

    .wordcloud {
        height: 384px;
    }

    .merge-left div.wordcloud {
        height: 404px;
    }

    #KostraOverviewWebViewChart {
        height: 550px !important;
        /*width: 860px !important;*/
    }

    #indicatorSearch {
        padding: 6px 24%;
        border-radius: 0px;
    }
    /* opportunity assessment*/
    .merge-left div.opportunity-assessment-bg {
        min-height: 460px;
    }

    .opportunity-assessment-bg {
        min-height: 460px;
    }

    .merge-left div.investment-overview-bg {
        min-height: 460px;
    }

    .investment-overview-bg {
        min-height: 460px;
    }

    /* financing overview start */

    .merge-left div.financing-overview-bg {
        min-height: 460px;
    }

    .financing-overview-bg {
        min-height: 460px;
    }

    /* financing overview end */

    .cons-right-bg {
        min-height: 468px;
    }
    /*Budget assumption*/
    .merge-left div.budget-assumption-bg {
        min-height: 460px;
    }

    .budget-assumption-bg {
        min-height: 460px;
    }

    .bud-ass-col3 {
        left: 9px;
        position: relative;
    }

    .bud-ass-col4 {
        left: 14px;
        position: relative;
    }

    .bud-ass-col5 {
        left: 3px;
        position: relative;
    }

    .bud-ass-hdcol4 {
        position: relative;
        left: 12px;
    }

    .bud-ass-hdcol3 {
        position: relative;
        left: 40px !important;
    }

    .assump-radio-selectall {
        margin-left: 51px !important;
        position: relative;
        top: -16px;
    }

    .assump-radio-wage {
        margin-left: 280px !important;
        position: relative;
        top: -23px;
    }

    .assump-radio-price {
        margin-left: 78px !important;
        position: relative;
        top: -23px;
    }

    .assump-radio-deflator {
        margin-left: 80px !important;
        position: relative;
        top: -23px;
    }

    .assump-radio-noadj {
        margin-left: 82px !important;
        position: relative;
        top: -23px;
    }
    /*Budget management start*/

    .bud-mang-right-bg {
        min-height: 438px !important;
    }
    /*budget management workflow section*/
    .bm-workflow-parent-style {
        height: 43px;
        border-radius: 20px;
        padding: 0px;
        width: 16.6%;
    }

    .bm-workflow-number-style {
        padding-top: 10px !important;
        border-radius: 53% !important;
        height: 41px !important;
        width: 41px !important;
        padding-left: 15px !important;
    }

    .bm-workflow-txt-A {
        font-size: 10px;
        margin-top: 3px;
    }

    .bm-workflow-txt-B {
        font-size: 10px;
        margin-top: 8px;
    }

    .bm-workflow-txt-C {
        font-size: 10px;
        margin-top: 8px;
    }

    .bm-workflow-txt-D {
        font-size: 10px;
        margin-top: 8px;
    }

    .bmWorkFlowDescTxtStyle {
        margin-left: -1px;
        text-align: left;
        font-size: 10px;
    }

    .work-flow-desc {
        width: 76%;
    }
    /*budget management workflow section*/
    /*financial plan tab start*/

    .budget-meeting-section {
        margin-left: 273px;
    }

    .bud-not-container {
        width: 45% !important;
    }

    .bud-act-col1 {
        width: 23% !important;
    }

    .bud-act-col2 {
        text-align: left !important;
        width: 14% !important;
        padding-left: 48px;
    }

    .bud-act-col3 {
        position: relative;
        left: 62px;
        text-align: left !important;
    }

    .bud-act-col4 {
        position: relative;
        left: 45px;
    }

    .bud-act-col5 {
        position: relative;
        left: 38px;
        white-space: nowrap;
    }

    .bud-act-col6 {
        position: relative;
        left: 49px;
        white-space: nowrap;
    }

    .bud-act-col7 {
        position: relative;
        left: 55px;
        white-space: nowrap;
    }

    .bud-act-col8 {
        position: relative;
        left: 60px;
        white-space: nowrap;
    }

    .bud-act-col9 {
        position: relative;
        left: 48px;
    }

    .bud-act-col10 {
        position: relative;
        left: 34px;
    }

    .bud-act-col11 {
        position: relative;
        left: 20px;
    }

    .bud-act-col12 {
        position: relative;
        left: 9px;
    }
    /*financial plan tab end*/
    /*budget process tab start*/
    .budget-process-drill-deadline {
        margin-left: -133px;
        width: 23%;
    }

    .budpro-task-addnew {
        width: 0%;
        margin-left: 1px;
    }

    .budget-process-drill-taskname {
        width: 28%;
        margin-left: -16px;
    }

    /*budget process tab end*/
    /*Budget management end*/


    .bud-proposal-right-bg {
        min-height: 438px !important;
    }

    /*Log on css start*/
    .log-on-copyright {
        position: fixed;
    }

    .log-on-error-bg {
        background-image: url('../images/log_on_banner.jpg');
        background-repeat: no-repeat;
        min-height: 707px !important;
        background-size: 1300px 707px;
    }

    .error-right-bg {
        height: 707px !important;
    }

    .error-logo-top {
        margin-top: 40px;
    }

    .error-top-line-one {
        margin-top: 40px;
    }

    .error-top-line-two {
        margin-top: 20px;
    }

    .error-top-line-three {
        margin-top: 40px;
    }

    .error-top-line-four {
        margin-top: 40px;
    }

    .error-bottom-copyright {
        margin-top: 296px;
    }

    .logout-bottom-copyright {
        margin-top: 353px;
    }
    /*Log on css end*/

    /*financial detail oppurtunity pop up*/


    /*financial detail oppurtunity pop up start*/
    .fin-pop-col1 {
        left: 1px;
        position: relative;
    }

    .fin-pop-col2 {
        left: -3px;
        position: relative;
    }

    .fin-pop-col3 {
        left: -3px;
        position: relative;
    }

    .fin-pop-col4 {
        left: 3px;
        position: relative;
    }
    /*financial detail oppurtunity pop up end*/
    /*Investment overview css start*/
    .inv-overview-header-spacing {
        padding-left: 10px !important;
    }

    .inv-ovr-chk-style-1 {
        width: 12% !important;
        margin-top: 20px;
        margin-left: 60px;
    }

    .inv-ovr-chk-style-2 {
        width: 17% !important;
        margin-top: 20px;
        margin-left: 0px;
    }

    .inv-ovr-chk-style-3 {
        width: 17% !important;
        margin-top: 20px;
        margin-left: 0px;
    }

    .inv-export-left {
        margin-left: -25px;
    }
    /*Investment overview css end*/
    /*staff plan start*/

    .staff-plan-right-bg {
        min-height: 438px !important;
    }

    #staffGrid {
        height: 515px;
    }
    /*yearly budget*/

    .yearly-budget-right-bg {
        min-height: 438px !important;
    }

    .MR-banner-grid-chart {
        height: 240px;
    }

    .yb-banner-left-grid {
        height: 235px !important;
    }

    .YB-banner-grid-chart {
        height: 265px;
    }

    .action-import-bg {
        min-height: 460px;
    }

    /*Document Export reporting*/
    .docexport-report-bg {
        min-height: 460px;
    }

    /*Kostra Detail CSS*/
    .kostra-section-detail-tablink {
        min-width: 63px;
        margin-left: 4px;
    }

    .kostra-detail-include-link {
        min-width: 61px;
        margin-left: 7px;
    }

    .kostra-detail-include-link-temp {
        min-width: 61px;
        margin-left: 4px;
    }
    /*Kostra Detail CSS*/

    /*Investment import export button section*/
    .inv-header-btn-padding {
        padding-left: 85px;
    }
    /*Investment import export button section*/

    .doc-tree-check-left {
        left: -4%;
    }

    .org-str-warning-container-width {
        width: 105%;
    }

    /*pop analysis css start*/
    .pop-analysis-year-tab-left {
        margin-left: 0% !important;
    }

    .pop-analysis-country-tab-left {
        margin-left: 1% !important;
    }

    .pop-analysis-region-tab-left {
        margin-left: 1% !important;
    }

    .pop-analysis-kostra-tab-left {
        margin-left: 3% !important;
    }

    .pop-analysis-sort-tab-left {
        margin-left: 3% !important;
    }
    /*pop analysis css end*/

    .ws-analysis-align {
        padding-right: 3px !important;
    }

    .ws-industry-tab-left {
        margin-left: 1% !important;
    }

    .ws-total-tab-left {
        margin-left: 1.5% !important;
    }

    .ws-country-tab-left {
        margin-left: 1.4% !important;
    }

    .ws-kostra-tab-left {
        margin-left: 1.2% !important;
    }
}


@media (min-width: 1400px) {
    .inc-font {
        font-size: 14px;
    }

    .map-slider {
        width: 401px;
    }

    .kostra-default-cities, .kostra-cities, .kostra-cities-first {
        width: 175px;
    }

    .map-buttons {
        padding-left: 200px;
    }

    .kostra-revenue-slider {
        width: 600px;
        margin-left: 8px;
    }

    .city-main-bg, .merge-left div.city-main-bg {
        min-height: 463px;
    }

    .banner-background-style {
        min-height: 500px;
    }

    .kpi-indicator-box {
        height: 169px;
    }

    .kostra-indicator-box {
        height: 200px;
    }

    .merge-left div.kostra-indicator-box {
        height: 200px;
    }

    .merge-left div #chart1, div #chart2, div #chart3, div #chart4 {
        height: 150px !important;
    }

    #chart1, #chart2, #chart3, #chart4 {
        height: 150px !important;
    }

    #politicalSimulationKPIchart1, #politicalSimulationKPIchart2, #politicalSimulationKPIchart3, #politicalSimulationKPIchart4 {
        height: 150px !important;
        width: 340px !important;
    }

    .merge-left div.kpi-indicator-box {
        height: 200px;
    }

    .population-statistics-main-bg {
        min-height: 484px;
    }

    .wordcloud {
        height: 401px;
    }

    .merge-left div.wordcloud {
        height: 423px;
    }

    .learn-label {
        padding-top: 439px;
    }

    .merge-left div.learn-label {
        padding-top: 461px;
    }

    .merge-left div.popstatchart {
        height: 362px;
    }

    .popstatchart {
        height: 392px;
    }

    .bm-overview-cmn-chart {
        height: 380px;
    }

    .merge-left div.bm-overview-cmn-chart {
        height: 400px;
    }

    #financechart1, #financechart2, #financechart3, #cfinancechart4 {
        height: 150px !important;
    }

    .top-drop .k-multiselect-wrap {
        overflow: auto;
        max-height: 60px;
    }

    .kostra-groups {
        width: 178px;
    }

    .kostra-regions {
        width: 178px;
    }

    #KostraOverviewWebViewChart {
        height: 600px !important;
        /*width: 900px !important;*/
    }

    #indicatorSearch {
        padding: 6px 25px;
        border-radius: 0px;
    }
    /* opportunity assessment*/
    .merge-left div.opportunity-assessment-bg {
        min-height: 485px;
    }

    .opportunity-assessment-bg {
        min-height: 485px;
    }

    .merge-left div.investment-overview-bg {
        min-height: 485px;
    }

    .investment-overview-bg {
        min-height: 485px;
    }

    /* financing overview start */

    .merge-left div.financing-overview-bg {
        min-height: 485px;
    }

    .financing-overview-bg {
        min-height: 485px;
    }

    .cons-right-bg {
        min-height: 494px;
    }
    /*Budget assumption*/
    .merge-left div.budget-assumption-bg {
        min-height: 485px;
    }

    .budget-assumption-bg {
        min-height: 485px;
    }

    .bud-ass-hdcol2 {
        position: relative !important;
        left: 28px !important;
    }

    .bud-ass-col5 {
        position: relative !important;
        left: 3px !important;
    }

    .bud-ass-hdcol4 {
        position: relative;
        left: 14px;
    }

    .bud-ass-col3 {
        position: relative;
        left: 10px;
    }

    .assump-radio-selectall {
        margin-left: 69px !important;
        position: relative;
        top: -16px;
    }

    .assump-radio-wage {
        margin-left: 300px !important;
        position: relative;
        top: -23px;
    }

    .assump-radio-price {
        margin-left: 84px !important;
        position: relative;
        top: -23px;
    }

    .assump-radio-deflator {
        margin-left: 87px !important;
        position: relative;
        top: -23px;
    }

    .assump-radio-noadj {
        margin-left: 86px !important;
        position: relative;
        top: -23px;
    }

    .bud-mang-right-bg {
        min-height: 463px !important;
    }
    /*budget management workflow section*/
    .bm-workflow-parent-style {
        height: 46px;
        border-radius: 23px;
        padding: 0px;
        width: 16%;
    }

    .bm-workflow-number-style {
        padding-top: 10px !important;
        border-radius: 50% !important;
        height: 44px !important;
        width: 44px !important;
        padding-left: 16px !important;
    }

    .bm-workflow-txt-A {
        font-size: 11px;
        margin-top: 1px;
    }

    .bm-workflow-txt-B {
        font-size: 11px;
        margin-top: 8px;
    }

    .bm-workflow-txt-C {
        font-size: 11px;
        margin-top: 8px;
    }

    .bm-workflow-txt-D {
        font-size: 11px;
        margin-top: 8px;
    }

    .bmWorkFlowDescTxtStyle {
        margin-left: -1px;
        text-align: left;
        font-size: 11px;
    }

    .work-flow-desc {
        width: 74%;
    }
    /*budget management workflow section*/
    /*financial plan tab start*/

    .budget-meeting-section {
        margin-left: 285px;
    }

    .bud-not-container {
        width: 45% !important;
    }

    .bud-act-col1 {
        width: 22% !important;
    }

    .bud-act-col2 {
        text-align: left !important;
        width: 14% !important;
        padding-left: 30px;
    }

    .bud-act-col3 {
        position: relative;
        left: 62px;
        text-align: left !important;
    }

    .bud-act-col4 {
        position: relative;
        left: 28px;
    }

    .bud-act-col5 {
        padding-right: 35px;
    }

    .bud-act-col6 {
        padding-right: 29px;
    }

    .bud-act-col7 {
        padding-right: 19px;
    }

    .bud-act-col8 {
        position: relative;
        left: 46px;
    }

    .bud-act-col9 {
        position: relative;
        left: 42px;
    }

    .bud-act-col10 {
        position: relative;
        left: 31px;
    }

    /*financial plan tab end*/
    /*budget process tab start*/
    .budget-process-drill-deadline {
        margin-left: -145px;
        width: 25%;
    }

    .budpro-task-addnew {
        width: 34%;
        margin-left: -9px;
    }

    .budget-process-drill-taskname {
        width: 27%;
        margin-left: -17px;
    }
    /*budget process tab end*/
    /*Budget management end*/

    .bud-proposal-right-bg {
        min-height: 463px !important;
    }
    /*Log on css*/
    .log-on-copyright {
        position: fixed;
    }

    .log-on-error-bg {
        background-image: url('../images/log_on_banner.jpg');
        background-repeat: no-repeat;
        min-height: 839px !important;
        background-size: 1400px 839px;
    }

    .error-right-bg {
        height: 839px !important;
    }

    .error-logo-top {
        margin-top: 50px;
    }

    .error-top-line-one {
        margin-top: 50px;
    }

    .error-top-line-two {
        margin-top: 30px;
    }

    .error-top-line-three {
        margin-top: 50px;
    }

    .error-top-line-four {
        margin-top: 60px;
    }

    .error-bottom-copyright {
        margin-top: 356px;
    }

    .error-common-text {
        font-size: 18px !important;
    }

    .logout-bottom-copyright {
        margin-top: 434px;
    }

    /*Log on css end*/

    /*financial detail oppurtunity pop up*/


    /*financial detail oppurtunity pop up start*/
    .fin-pop-col1 {
        left: 0px;
        position: relative;
    }

    .fin-pop-col2 {
        left: -2px;
        position: relative;
    }

    .fin-pop-col3 {
        left: -2px;
        position: relative;
    }

    .fin-pop-col4 {
        left: 5px;
        position: relative;
    }
    /*financial detail oppurtunity pop up end*/
    /*Investment overview css start*/
    .inv-overview-header-spacing {
        padding-left: 10px !important;
    }

    .inv-ovr-chk-style-1 {
        width: 12% !important;
        margin-top: 20px;
        margin-left: 70px;
    }

    .inv-ovr-chk-style-2 {
        width: 17% !important;
        margin-top: 20px;
        margin-left: 0px;
    }

    .inv-ovr-chk-style-3 {
        width: 17% !important;
        margin-top: 20px;
        margin-left: 0px;
    }

    .inv-export-left {
        margin-left: -25px;
    }
    /*Investment overview css end*/


    /*staff planning*/

    .staff-plan-right-bg {
        min-height: 463px !important;
    }

    #staffGrid {
        height: 538px;
    }
    /*yearly budget*/


    .yearly-budget-right-bg {
        min-height: 463px !important;
    }

    .MR-banner-grid-chart {
        height: 270px;
    }

    .yb-banner-left-grid {
        height: 248px !important;
    }

    .YB-banner-grid-chart {
        height: 270px;
    }

    /*action import*/
    .action-import-bg {
        min-height: 485px;
    }

    /*Document Export reporting*/
    .docexport-report-bg {
        min-height: 485px;
    }

    /*Kostra Detail CSS*/
    .kostra-section-detail-tablink {
        min-width: 66px;
        margin-left: 4px;
    }

    .kostra-detail-include-link {
        min-width: 65px;
        margin-left: 4px;
    }

    .kostra-detail-include-link-temp {
        min-width: 64px;
        margin-left: 4px;
    }
    /*Kostra Detail CSS*/
    /*Investment import export button section*/
    .inv-header-btn-padding {
        padding-left: 91px;
    }
    /*Investment import export button section*/

    .doc-tree-check-left {
        left: -1%;
    }

    .org-str-warning-container-width {
        width: 104.4%;
    }

    /*pop analysis css start*/
    .pop-analysis-year-tab-left {
        margin-left: 0% !important;
    }

    .pop-analysis-country-tab-left {
        margin-left: 1% !important;
    }

    .pop-analysis-region-tab-left {
        margin-left: 1% !important;
    }

    .pop-analysis-kostra-tab-left {
        margin-left: 2% !important;
    }

    .pop-analysis-sort-tab-left {
        margin-left: 3% !important;
    }
    /*pop analysis css end*/

    .ws-analysis-align {
        padding-right: 3px !important;
    }

    .ws-industry-tab-left {
        margin-left: 1% !important;
    }

    .ws-total-tab-left {
        margin-left: 1.5% !important;
    }

    .ws-country-tab-left {
        margin-left: 1.2% !important;
    }

    .ws-kostra-tab-left {
        margin-left: 1% !important;
    }
}

@media (min-width: 1500px) {
    .banner-background-style {
        min-height: 560px;
    }

    .kostra-indicator-box {
        height: 214px !important;
    }

    .ws-analysis-align {
        padding-right: 3px !important;
    }
}

@media (min-width: 1600px) {
    .map-slider {
        width: 455px;
    }

    .inc-font {
        font-size: 14px;
    }

    .map-buttons {
        padding-left: 250px;
    }

    .kostra-default-cities, .kostra-cities, .kostra-cities-first {
        width: 200px;
    }

    .kostra-revenue-slider {
        width: 680px;
        margin-left: 8px;
    }

    .banner-background-style, .city-main-bg {
        min-height: 550px;
    }

    .kpi-indicator-box {
        height: 195px;
    }

    .kostra-indicator-box {
        height: 195px;
    }

    .merge-left div.kostra-indicator-box {
        height: 225px !important;
    }

    .merge-left div.kpi-indicator-box {
        height: 215px;
    }

    .merge-left div #chart1, div #chart2, div #chart3, div #chart4 {
        height: 154px !important;
    }

    #chart1, #chart2, #chart3, #chart4 {
        height: 140px !important;
    }

    #politicalSimulationKPIchart1, #politicalSimulationKPIchart2, #politicalSimulationKPIchart3, #politicalSimulationKPIchart4 {
        height: 140px !important;
    }

    .population-statistics-main-bg {
        min-height: 538px;
    }

    .wordcloud {
        height: 455px;
    }

    .merge-left div.wordcloud {
        height: 478px;
    }

    .learn-label {
        padding-top: 492px;
    }

    .merge-left div.learn-label {
        padding-top: 515px;
    }

    .merge-left div.popstatchart {
        height: 420px;
    }

    .popstatchart {
        height: 445px;
    }

    .bm-overview-cmn-chart {
        height: 385px;
    }

    .merge-left div.bm-overview-cmn-chart {
        height: 405px;
    }

    #financechart1, #financechart2, #financechart3, #financechart4 {
        height: 150px !important;
    }

    .kostra-groups {
        width: 205px;
    }

    .kostra-regions {
        width: 205px;
    }

    #KostraOverviewWebViewChart {
        height: 600px !important;
        /*width: 1134px !important;*/
    }

    #indicatorSearch {
        padding: 6px 31px;
        border-radius: 0px;
    }
    /* opportunity assessment*/
    .merge-left div.opportunity-assessment-bg {
        min-height: 540px;
    }

    .opportunity-assessment-bg {
        min-height: 540px;
    }

    .merge-left div.investment-overview-bg {
        min-height: 540px;
    }

    .investment-overview-bg {
        min-height: 540px;
    }

    /* financing overview start */

    .merge-left div.financing-overview-bg {
        min-height: 540px;
    }

    .financing-overview-bg {
        min-height: 540px;
    }

    .cons-right-bg {
        min-height: 554px;
    }
    /*Budget assumption*/
    .merge-left div.budget-assumption-bg {
        min-height: 540px;
    }

    .budget-assumption-bg {
        min-height: 540px;
    }

    .bud-ass-col3 {
        left: 21px;
        position: relative;
    }

    .bud-ass-col4 {
        left: 26px;
        position: relative;
    }

    .bud-ass-col5 {
        left: 12px !important;
        position: relative;
    }

    .bud-ass-hdcol4 {
        position: relative;
        left: 22px;
    }

    .bud-ass-hdcol3 {
        position: relative;
        left: 57px !important;
    }

    .bud-ass-hdcol2 {
        position: relative !important;
        left: 42px !important;
    }

    .assump-radio-selectall {
        margin-left: 103px !important;
        position: relative;
        top: -16px;
    }

    .assump-radio-wage {
        margin-left: 334px !important;
        position: relative;
        top: -23px;
    }

    .assump-radio-price {
        margin-left: 100px !important;
        position: relative;
        top: -23px;
    }

    .assump-radio-deflator {
        margin-left: 98px !important;
        position: relative;
        top: -23px;
    }

    .assump-radio-noadj {
        margin-left: 97px !important;
        position: relative;
        top: -23px;
    }

    .bud-mang-right-bg {
        min-height: 515px !important;
    }

    /*budget management workflow section*/
    .bm-workflow-parent-style {
        height: 42px;
        border-radius: 23px;
        padding: 0px;
    }

    .bm-workflow-number-style {
        padding-top: 10px !important;
        border-radius: 50% !important;
        height: 40px !important;
        width: 40px !important;
        padding-left: 15px !important;
    }

    .bm-workflow-txt-A {
        font-size: 12px;
        margin-top: 0px;
    }

    .bm-workflow-txt-B {
        font-size: 12px;
        margin-top: 8px;
    }

    .bm-workflow-txt-C {
        font-size: 12px;
        margin-top: 8px;
    }

    .bm-workflow-txt-D {
        font-size: 12px;
        margin-top: 8px;
    }

    .bmWorkFlowDescTxtStyle {
        margin-left: -1px;
        text-align: left;
        font-size: 12px;
    }
    /*budget management workflow section*/
    /*financial plan tab start*/

    .budget-meeting-section {
        margin-left: 281px;
    }

    .bud-not-container {
        width: 40% !important;
    }

    .bud-act-col1 {
        width: 19% !important;
    }

    .bud-act-col2 {
        text-align: left !important;
        width: 16% !important;
        left: 15px;
        position: relative;
    }

    .bud-act-col3 {
        left: 59px;
        text-align: left !important;
    }

    .bud-act-col4 {
        position: relative;
        left: 44px;
    }

    .bud-act-col5 {
        padding-right: 32px;
        white-space: nowrap;
    }

    .bud-act-col6 {
        position: relative;
        left: 48px;
    }

    .bud-act-col7 {
        padding-right: 15px;
    }

    .bud-act-col8 {
        position: relative;
        left: 68px;
    }

    .bud-act-col9 {
        position: relative;
        left: 55px;
    }

    .bud-act-col10 {
        position: relative;
        left: 40px;
    }

    .bud-act-col11 {
        position: relative;
        left: 24px;
    }

    .bud-act-col12 {
        position: relative;
        left: 11px;
    }

    /*financial plan tab end*/
    /*budget process tab start*/
    .budget-process-drill-deadline {
        margin-left: -148px;
        width: 23%;
    }

    .budpro-task-addnew {
        width: 35%;
        margin-left: -30px;
    }

    .budget-process-drill-taskname {
        width: 28%;
        margin-left: -30px;
    }
    /*budget process tab end*/
    /*Budget management end*/

    .bud-proposal-right-bg {
        min-height: 515px !important;
    }

    /*Log on css start*/
    .log-on-copyright {
        position: fixed;
    }

    .log-on-error-bg {
        background-image: url('../images/log_on_banner.jpg');
        background-repeat: no-repeat;
        min-height: 839px !important;
    }

    .error-right-bg {
        height: 839px !important;
    }

    .error-logo-top {
        margin-top: 50px;
    }

    .error-top-line-one {
        margin-top: 50px;
    }

    .error-top-line-two {
        margin-top: 30px;
    }

    .error-top-line-three {
        margin-top: 50px;
    }

    .error-top-line-four {
        margin-top: 60px;
    }

    .error-bottom-copyright {
        margin-top: 355px;
    }

    .error-common-text {
        font-size: 18px !important;
    }

    .logout-bottom-copyright {
        margin-top: 433px;
    }

    /*Log on css end*/

    /*financial detail oppurtunity pop up*/


    /*financial detail oppurtunity pop up start*/
    .fin-pop-col1 {
        left: 0px;
        position: relative;
    }

    .fin-pop-col2 {
        left: -2px;
        position: relative;
    }

    .fin-pop-col3 {
        left: -2px;
        position: relative;
    }

    .fin-pop-col4 {
        left: 7px;
        position: relative;
    }
    /*financial detail oppurtunity pop up end*/
    /*Investment overview css start*/
    .inv-overview-header-spacing {
        padding-left: 10px !important;
    }

    .inv-ovr-chk-style-1 {
        width: 12% !important;
        margin-top: 20px;
        margin-left: 80px;
    }

    .inv-ovr-chk-style-2 {
        width: 17% !important;
        margin-top: 20px;
        margin-left: 0px;
    }

    .inv-ovr-chk-style-3 {
        width: 17% !important;
        margin-top: 20px;
        margin-left: 0px;
    }

    .inv-export-left {
        margin-left: -25px;
    }
    /*Investment overview css end*/

    /*staff planning*/

    .staff-plan-right-bg {
        min-height: 515px !important;
    }

    #staffGrid {
        height: 538px;
    }
    /*yearly budget*/
    .yb-banner-left-grid {
        height: 258px !important;
    }

    .yearly-budget-right-bg {
        min-height: 515px !important;
    }

    .MR-banner-grid-chart {
        height: 325px;
    }

    .YB-banner-grid-chart {
        height: 325px !important;
    }

    /*action export*/
    .action-import-bg {
        min-height: 540px;
    }

    /*Document Export reporting*/
    .docexport-report-bg {
        min-height: 540px;
    }

    .month-year-left, .monthly-year-center, .month-year-right {
        right: 40px;
    }

    /*Kostra Detail CSS*/
    .kostra-section-detail-tablink {
        min-width: 77px;
        margin-left: 4px;
    }

    .kostra-detail-include-link {
        min-width: 67px;
        margin-left: 14px;
    }

    .kostra-detail-include-link-temp {
        min-width: 73px;
        margin-left: 6px;
    }
    /*Kostra Detail CSS*/
    /*Kostra Detail CSS*/
    /*Investment import export button section*/
    .inv-header-btn-padding {
        padding-left: 136px;
    }
    /*Investment import export button section*/

    .doc-tree-check-left {
        left: 3%;
    }

    .org-str-warning-container-width {
        width: 104%;
    }

    /*pop analysis css start*/
    .pop-analysis-year-tab-left {
        margin-left: 0% !important;
    }

    .pop-analysis-country-tab-left {
        margin-left: 1% !important;
    }

    .pop-analysis-region-tab-left {
        margin-left: 1% !important;
    }

    .pop-analysis-kostra-tab-left {
        margin-left: 2% !important;
    }

    .pop-analysis-sort-tab-left {
        margin-left: 3% !important;
    }
    /*pop analysis css end*/

    .ws-analysis-align {
        padding-right: 3px !important;
    }

    .ws-industry-tab-left {
        margin-left: 0.8% !important;
    }

    .ws-total-tab-left {
        margin-left: 1.4% !important;
    }

    .ws-country-tab-left {
        margin-left: 1.1% !important;
    }

    .ws-kostra-tab-left {
        margin-left: 1% !important;
    }
}

@media (min-width: 1650px) {
    .banner-background-style {
        min-height: 580px;
    }

    .org-str-warning-container-width {
        width: 103.8%;
    }
}

@media (min-width: 1700px) {
    .banner-background-style {
        min-height: 640px;
    }

    .kostra-indicator-box {
        height: 245px;
    }

    .YB-left-banner-grid-chart {
        height: 261px;
    }

    .ws-analysis-align {
        padding-right: 3px !important;
    }
    /*monthly report css*/
    .monthly-report-top-tabs {
        width: 94% !important;
    }
}

@media (min-width: 1900px) {

    /*log on css*/
    .log-on-copyright {
        position: fixed;
        top: 825px !important;
    }

    .banner-background-style {
        min-height: 680px;
    }

    #staffPlanningWrapperContainer .banner-background-style {
        min-height: 647px;
    }

    .map-slider {
        width: 555px;
    }

    .merge-left div #chart1, div #chart2, div #chart3, div #chart4 {
        height: 160px !important;
    }

    #chart1, #chart2, #chart3, #chart4 {
        height: 140px !important;
    }

    #politicalSimulationKPIchart1, #politicalSimulationKPIchart2, #politicalSimulationKPIchart3, #politicalSimulationKPIchart4 {
        height: 140px !important;
        width: 420px !important;
    }

    #financechart1, #financechart2, #financechart3, #financechart4 {
        height: 130px !important;
        width: 400px !important;
    }

    #KostraOverviewWebViewChart {
        height: 650px !important;
        /*width: 1300px !important;*/
    }

    #indicatorSearch {
        padding: 6px 45px;
        border-radius: 0px;
    }
    /*pop stat*/
    .merge-left div.popstatchart {
        min-height: 530px;
    }

    .population-statistics-main-bg {
        min-height: 646px;
    }

    /*Consequence*/
    .cons-right-bg {
        min-height: 660px;
    }
    /*Budget Assumption*/
    .merge-left div.budget-assumption-bg {
        min-height: 648px;
    }

    .bud-ass-col3 {
        left: 37px;
        position: relative;
    }

    .bud-ass-col4 {
        left: 44px;
        position: relative;
    }

    .bud-ass-col5 {
        left: 23px !important;
        position: relative;
    }

    .bud-ass-hdcol4 {
        position: relative;
        left: 41px;
    }

    .bud-ass-hdcol3 {
        position: relative;
        left: 91px !important;
    }

    .bud-ass-hdcol5 {
        position: relative;
        left: 12px;
    }

    .bud-ass-hdcol2 {
        position: relative !important;
        left: 61px !important;
    }

    .assump-radio-selectall {
        margin-left: 98px !important;
        position: relative;
        top: 0px;
    }

    .assump-radio-wage {
        margin-left: 410px !important;
        position: relative;
        top: -23px;
    }

    .assump-radio-price {
        margin-left: 123px !important;
        position: relative;
        top: -23px;
    }

    .assump-radio-deflator {
        margin-left: 121px !important;
        position: relative;
        top: -23px;
    }

    .assump-radio-noadj {
        margin-left: 123px !important;
        position: relative;
        top: -23px;
    }
    /*Budget management start*/

    /*budget management workflow section*/
    .bm-workflow-parent-style {
        height: 42px;
        border-radius: 23px;
        padding: 0px;
    }

    .bm-workflow-number-style {
        padding-top: 10px !important;
        border-radius: 50% !important;
        height: 40px !important;
        width: 40px !important;
        padding-left: 15px !important;
    }

    .bm-workflow-txt-A {
        font-size: 12px;
        margin-top: 7px;
    }

    .bm-workflow-txt-B {
        font-size: 12px;
        margin-top: 7px;
    }

    .bm-workflow-txt-C {
        font-size: 12px;
        margin-top: 7px;
    }

    .bm-workflow-txt-D {
        font-size: 12px;
        margin-top: 7px;
    }

    .bmWorkFlowDescTxtStyle {
        margin-left: -1px;
        text-align: left;
        font-size: 12px;
    }
    /*budget management workflow section*/
    /*financial plan tab start*/

    .budget-meeting-section {
        margin-left: 287px;
    }

    .bud-not-container {
        width: 33% !important;
    }

    .bud-act-col1 {
        width: 13% !important;
    }

    .bud-act-col2 {
        text-align: left !important;
        width: 24% !important;
        padding-left: 143px;
    }

    .bud-act-col3 {
        position: relative;
        left: 28px;
    }

    .bud-act-col4 {
        position: relative;
        left: -40px;
    }

    .bud-act-col5 {
        padding-right: 0px;
        text-align: right !important;
        position: relative;
        left: -48px;
    }

    .bud-act-col6 {
        padding-left: 0px;
        text-align: right !important;
        position: relative;
        left: 44px;
    }

    .bud-act-col7 {
        padding-right: 0px;
        position: relative;
        left: 75px;
    }

    .bud-act-col8 {
        position: relative;
        left: 138px;
    }

    .bud-act-col9 {
        position: relative;
        left: 110px;
    }

    .bud-act-col10 {
        position: relative;
        left: 80px;
    }

    .bud-act-col11 {
        position: relative;
        left: 50px;
    }

    .bud-act-col12 {
        position: relative;
        left: 20px;
    }



    /*financial plan tab end*/
    /*budget process tab start*/
    .budget-process-drill-deadline {
        margin-left: -160px;
        width: 21%;
    }

    .budget-process-drill-taskname {
        width: 33%;
        margin-left: -50px;
    }
    /*budget process tab end*/
    /*Budget management end*/


    /*financial detail oppurtunity pop up start*/
    .fin-pop-col1 {
        left: 3px;
        position: relative;
    }

    .fin-pop-col2 {
        left: 0px;
        position: relative;
    }

    .fin-pop-col3 {
        left: -2px;
        position: relative;
    }

    .fin-pop-col4 {
        left: 8px;
        position: relative;
    }
    /*financial detail oppurtunity pop up end*/
    /*Investment overview css start*/
    .inv-overview-header-spacing {
        padding-left: 10px !important;
    }

    .inv-ovr-chk-style-1 {
        width: 12% !important;
        margin-top: 20px;
        margin-left: 100px;
    }

    .inv-ovr-chk-style-2 {
        width: 17% !important;
        margin-top: 20px;
        margin-left: 0px;
    }

    .inv-ovr-chk-style-3 {
        width: 17% !important;
        margin-top: 20px;
        margin-left: 0px;
    }

    .inv-export-left {
        margin-left: -25px;
    }
    /*Investment overview css end*/

    /*staff planning*/
    #staffGrid {
        height: 538px;
    }

    .MR-banner-grid-chart {
        height: 430px;
    }

    .YB-banner-grid-chart {
        height: 430px;
    }

    /*action export*/
    .action-import-bg {
        min-height: 645px;
    }

    /*Document Export reporting*/
    .docexport-report-bg {
        min-height: 645px;
    }

    .month-year-left, .monthly-year-center, .month-year-right {
        right: 70px;
    }

    /*Kostra Detail CSS*/
    .kostra-section-detail-tablink {
        min-width: 95px;
        margin-left: 4px;
    }

    .kostra-detail-include-link {
        min-width: 73px;
        margin-left: 25px;
    }

    .kostra-detail-include-link-temp {
        min-width: 91px;
        margin-left: 6px;
    }
    /*Kostra Detail CSS*/

    /*Investment import export button section*/
    .inv-header-btn-padding {
        padding-left: 224px;
    }
    /*Investment import export button section*/

    .doc-tree-check-left {
        left: 9%;
    }

    .org-str-warning-container-width {
        width: 103.3%;
    }

    /*pop analysis css start*/
    .pop-analysis-year-tab-left {
        margin-left: 0% !important;
    }

    .pop-analysis-country-tab-left {
        margin-left: 1% !important;
    }

    .pop-analysis-region-tab-left {
        margin-left: 1% !important;
    }

    .pop-analysis-kostra-tab-left {
        margin-left: 1% !important;
    }

    .pop-analysis-sort-tab-left {
        margin-left: 2% !important;
    }
    /*pop analysis css end*/

    .ws-analysis-align {
        padding-right: 3px !important;
    }

    .ws-industry-tab-left {
        margin-left: 0.6% !important;
    }

    .ws-total-tab-left {
        margin-left: 1.2% !important;
    }

    .ws-country-tab-left {
        margin-left: 1% !important;
    }

    .ws-kostra-tab-left {
        margin-left: 0.8% !important;
    }
    /*monthly report css*/
    .monthly-report-top-tabs {
        width: 94% !important;
    }
}



@media (min-width: 1024px) {
    .kostraoverview-explanation-txtarea {
        width: 472px;
    }

    .kostraoverview-explanation-division {
        width: 439px;
    }

    .kostraoverview-evaluation-txtarea {
        width: 424px;
    }

    .kostraoverview-evaluation-division {
        width: 422px;
    }

    #dashBoardWrapper .box-header h3 {
        font-size: 12px !important;
    }
}

@media (min-width: 1200px) {
    .bckgrnd-kostraoverview-explanation-txtarea {
        width: 600px !important;
    }

    .bckgrnd-kostraoverview-evaluation-txtarea {
        width: 582px !important;
    }

    .kostraoverview-explanation-txtarea {
        width: 627px;
    }

    .kostraoverview-explanation-division {
        width: 600px;
    }

    .kostraoverview-evaluation-txtarea {
        width: 582px;
    }

    .kostraoverview-evaluation-division {
        width: 582px;
    }
}

@media (min-width: 1300px) {
    .bckgrnd-kostraoverview-explanation-txtarea {
        width: 613px !important;
    }

    .bckgrnd-kostraoverview-evaluation-txtarea {
        width: 595px !important;
    }

    .kostraoverview-explanation-txtarea {
        width: 643px;
    }

    .kostraoverview-explanation-division {
        width: 610px;
    }

    .kostraoverview-evaluation-txtarea {
        width: 595px;
    }

    .kostraoverview-evaluation-division {
        width: 592px;
    }
}

@media (min-width: 1400px) {
    .kostraoverview-explanation-txtarea {
        width: 650px;
    }

    .kostraoverview-explanation-division {
        width: 650px;
    }

    .kostraoverview-evaluation-txtarea {
        width: 632px;
    }

    .kostraoverview-evaluation-division {
        width: 632px;
    }

    .bckgrnd-kostraoverview-explanation-txtarea {
        width: 650px !important;
    }

    .bckgrnd-kostraoverview-evaluation-txtarea {
        width: 632px !important;
    }
}


@media (min-width: 1600px) {

    .kostraoverview-explanation-txtarea {
        width: 735px;
    }

    .kostraoverview-explanation-division {
        width: 735px;
    }

    .kostraoverview-evaluation-txtarea {
        width: 715px;
    }

    .kostraoverview-evaluation-division {
        width: 715px;
    }

    .bckgrnd-kostraoverview-explanation-txtarea {
        width: 735px !important;
    }

    .bckgrnd-kostraoverview-evaluation-txtarea {
        width: 715px !important;
    }
}

@media (min-width: 1600px) {
    #btnPopStatTenYearHistorical, #btnPopStatFourYear, #btnPopStatTenYear, #btnPopStatTwentyFive {
        width: 100px;
    }
}

/* Learn More Section Start*/
.learn-more-text {
    text-align: justify;
    line-height: 140%;
    font-size: 14px;
}

#flowChartKostra {
    height: 500px;
}

#imgPopulationStatisticsLearnMore {
    height: 500px;
}

#imgCityRankingLearnMore {
    height: 500px;
}

.flow-image-align {
    text-align: center;
}

.learn-more-hide {
    display: none !important;
}

.learn-more-slideup-image {
    width: 80px;
    height: 20px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: 50% 50%;
    cursor: pointer;
}


.learn-more-font-size {
    font-size: 30px;
}

.learn-more-txt-align {
    text-align: center;
}
/*Learn More Section End*/


/*Export As-Start*/
.btn-kostra-detail-Export {
    background-color: #347B9A;
    color: #F8F8F8;
    margin-top: 10px;
    margin-left: 320px;
    padding-left: 14px;
    padding-right: 14px;
    width: 17%;
    margin-bottom: 7px;
}

.btn-kostra-cr-export {
    background-color: #347B9A;
    color: #F8F8F8;
    margin-top: 10px;
    margin-left: 320px;
    padding-left: 14px;
    padding-right: 14px;
    width: 17%;
    margin-bottom: 7px;
}

.kostra-detail-export-border {
    margin-top: 1px;
    padding-top: 15px;
    background: #ffffff;
    margin-left: 6px;
    margin-right: 6px;
    margin-bottom: 6px;
    border: 1px solid #c3c3c3;
}

.kostra-detail-export {
    background: #f4f4f4 !important;
    padding-bottom: 15px;
}

.kostra-hide-cost-potential {
    display: none !important;
}

.export-type {
    float: left;
    position: relative;
    width: 325px;
    height: 28px;
    margin: 5px 0 5px 20px;
    padding: 0px;
    bottom: 5px;
}

.main-export-img {
    margin-bottom: 10px;
    position: relative;
    margin-right: -13px;
    float: right;
    height: 28px;
}

.costreduction-export-img {
    position: relative;
    float: right;
    margin-top: 9px;
    height: 28px;
}

.main-img-export {
    position: relative;
    bottom: 0px;
    right: -5px;
}

.overview-export-img {
    margin-bottom: 10px;
    position: relative;
    margin-right: -13px;
    float: right;
    margin-top: 15px;
    height: 28px;
}

.btn-export {
    border-color: #c3c3c3;
    background-color: #f3f4f4;
    color: #606060;
}

    .btn-export:hover {
        border-color: #c3c3c3;
        background-color: #f3f4f4;
        color: #606060;
    }

.expot-img {
    margin-top: -9px;
}

.export-span {
    position: relative;
    top: -4px;
}

.btn-export, .btn-export:hover {
    border-color: #c3c3c3;
    background-color: #f3f4f4;
    color: #606060;
}

.detailpage-export-img {
    margin-bottom: 10px;
    position: relative;
    margin-right: -13px;
    float: right;
    margin-top: 0px;
}
/*Export As -End*/

/*consquence adjusted budget - start*/
.consequence-analysis-dynamic-heading {
    margin-left: -26px;
    margin-top: 10px;
}

.consequence-dynamic-heading {
    margin-top: 38px;
    color: #fff;
    font-size: 38px;
    font-family: impact, sans-serif;
}

.finplan-bg {
    background-image: url('../images/consequence-banner.jpg');
}

#budgetChangesDetailTreeGrid,
#revenueGrid,
#centralExpensesGrid,
#operationalGrid,
#operationalGrid1,
#operationalGridNew,
#operationalGridNew1,
#financialGrid,
#financialGrid1,
#provisionsGrid,
/*#operationalOrgStructureGrid,*/
#consequenceOverviewTotalGrid {
    border: 1px solid #c3c3c3;
    overflow: auto;
    border-radius: 0px;
}

#FPInActionsGrid {
    border: 1px solid #c3c3c3;
    border-radius: 0px;
}

    #revenueGrid.k-grid tr td,
    #centralExpensesGrid.k-grid tr td,
    #operationalGrid.k-grid tr td,
    #operationalGrid1.k-grid tr td,
    #operationalGridNew.k-grid tr td,
    #operationalGridNew1.k-grid tr td,
    #financialGrid.k-grid tr td,
    #financialGrid1.k-grid tr td,
    #provisionsGrid.k-grid tr td,
    #consequenceOverviewTotalGrid.k-grid tr td,
    #FPInActionsGrid.k-grid tr td {
        border-left: none;
        text-align: right;
        white-space: nowrap;
    }

.cons- .k-window-titlebar.k-header {
    overflow: auto;
    max-height: 100px;
    position: relative;
    height: inherit;
    overflow-y: hidden;
}

#consequencePopupGrid {
    border: none;
    overflow: auto;
}

    #consequencePopupGrid.k-grid td {
        border-left: none;
        text-align: right;
        white-space: nowrap;
        line-height: 23px;
    }

/*consquence adjusted detail - start*/

#SFORevenuesGridTotal,
#SFORevenuesGrid,
#SFORevenuesGrid1,
#consequencesOriginalBudgetDetailGrid,
#consequencesBudgetAdjustmentGrid,
#consequencesTotalsDetailGrid,
#consequencesChangesFinancialPlanDetailGrid,
#consequencesChangesDemographyDetailGridOne,
#financialDetailCostReductionGrid,
#financialDetailNewPrioritiesGrid,
#financialDetailPoliticalProcessGrid,
#BPBudgetCostReducitonGrid,
#BPBudgetNewPrioritiesGrid,
#financialDetailOperationalEffect,
#financingOverviewNeed,
#financingNewInvesmentsGrid,
#publishedHistoryDocGrid,
#financingOverviewExternal,
#financingOverviewInternal,
#financingOverviewTotal,
#financingOverviewSummery,
#financialDetailCR_NP_BA_Grid {
    overflow: auto;
    border: 1px solid rgb(195, 195, 195);
}

    #SFORevenuesGridTotal.k-grid tr td,
    #SFORevenuesGrid1.k-grid tr td,
    #SFORevenuesGrid.k-grid tr td,
    #consequencesOriginalBudgetDetailGrid.k-grid tr td,
    #consequencesBudgetAdjustmentGrid.k-grid tr td,
    #consequencesChangesFinancialPlanDetailGrid.k-grid tr td,
    #consequenceDetailsEditGrid.k-grid tr td,
    #consequencesChangesDemographyDetailGridOne.k-grid tr td,
    #financialDetailCostReductionGrid.k-grid tr td,
    #financialDetailNewPrioritiesGrid.k-grid tr td,
    #financialDetailPoliticalProcessGrid.k-grid tr td,
    #budPropChangesFinancialPlanDetailGrid.k-grid tr td,
    #BPBudgetCostReducitonGrid.k-grid tr td,
    #BPBudgetNewPrioritiesGrid.k-grid tr td,
    #BMServiceAreasStatusGrid.k-grid tr td,
    #financialDetailOperationalEffect.k-grid tr td,
    #financingOverviewNeed.k-grid tr td,
    #financingOverviewSummery.k-grid tr td,
    #financingNewInvesmentsGrid.k-grid tr td,
    #serviceUnitDetailInfoGrid.k-grid tr td,
    #publishedHistoryDocGrid.k-grid tr td,
    #financingOverviewExternal.k-grid tr td,
    #financingOverviewInternal.k-grid tr td,
    #financingOverviewTotal.k-grid tr td,
    #financialDetailCR_NP_BA_Grid.k-grid tr td {
        border-left: none;
        text-align: right;
        white-space: nowrap;
    }

    #consequencesTotalsDetailGrid.k-grid tr td {
        border-left: none;
        white-space: normal !important;
    }

#consequenceDetailsEditGrid {
    border: none;
    overflow: auto;
}

.cons-adj-details-tb-column1 {
    display: table-cell;
    text-align: left;
    width: 42%;
    text-overflow: ellipsis;
}

.cons-adj-details-tb-column2 {
    display: table-cell;
    width: 17%;
    text-overflow: ellipsis;
    text-align: right;
}

.cons-adj-details-tb-column3 {
    display: table-cell;
    width: 17%;
    text-overflow: ellipsis;
    text-align: right;
}

.cons-adj-details-tb-column4 {
    display: table-cell;
    width: 17%;
    text-overflow: ellipsis;
    text-align: right;
}

.cons-adj-details-tb-column5 {
    display: table-cell;
    width: 17%;
    text-overflow: ellipsis;
    text-align: right;
}

.inv-detail-demography-tb-column1 {
    display: table-cell;
    width: 42%;
    text-overflow: ellipsis;
    padding-left: 20px;
}

.inv-detail-demography-tb-column2 {
    display: table-cell;
    width: 17%;
    text-overflow: ellipsis;
    text-align: right;
}

.inv-detail-demography-tb-column3 {
    display: table-cell;
    width: 17%;
    text-overflow: ellipsis;
    text-align: right;
}

.inv-detail-demography-tb-column4 {
    display: table-cell;
    width: 17%;
    text-overflow: ellipsis;
    text-align: right;
}

.inv-detail-demography-tb-column5 {
    display: table-cell;
    width: 17%;
    text-overflow: ellipsis;
    text-align: right;
}


.cons-adj-detail-panel {
    border-bottom: none !important;
    background-color: #fff !important;
    padding-top: 5px;
    padding-bottom: 0px;
    padding-right: 65px;
}

.cons-adj-panel-header {
    border: 1px solid rgb(195, 195, 195);
    background: #fff;
    padding-top: 12px;
    padding-bottom: 12px;
    /*margin-bottom: 12px;*/
    padding-right: 65px;
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.daam-col {
    padding-right: 8px;
}

.inv-detail-demography-total-row {
    padding-right: 65px;
}

#consDemoTotal, #actioContainer {
    display: table !important;
}

.cons-adj-panel-radius {
    border-top-left-radius: 0px !important;
    border-top-right-radius: 0px !important;
    border-bottom-left-radius: 0px !important;
    border-bottom-right-radius: 0px !important;
    border: 1px solid rgb(195, 195, 195);
}

.inv-detail-demography-addnew {
    margin-bottom: 15px;
    text-align: left;
    background: #FFF;
    height: 27px;
    margin-top: -20px;
    border-top-left-radius: 0px !important;
    border-top-right-radius: 0px !important;
    border-bottom-left-radius: 0px !important;
    border-bottom-right-radius: 0px !important;
    border: 1px solid rgb(195, 195, 195);
    border-top: none;
}

.inv-detail-demography-total-row {
    border: 1px solid rgb(195, 195, 195);
    background: #fff;
    padding-top: 10px;
    padding-bottom: 5px;
    border-top: none;
    top: -15px;
}

.inv-demography-grid-border-top {
    overflow: hidden;
    border-top: 1px solid rgb(195, 195, 195);
    padding-top: 10px;
}

.demography-add-activity {
    background-image: url(../images/add_small.png);
    background-repeat: no-repeat;
    padding-left: 20px;
    margin-left: 5px;
}

.demography-add-postion {
    position: absolute;
    margin-top: 4px;
}

.fin-tb-column1 {
    display: table-cell;
    text-align: left;
    width: 28%;
    text-overflow: ellipsis;
    padding-left: 5px;
}

.fin-tb-column2 {
    display: table-cell;
    text-align: right;
    width: 18%;
    text-overflow: ellipsis;
}

.fin-tb-column3 {
    display: table-cell;
    width: 22%;
    text-overflow: ellipsis;
    text-align: right;
}

.fin-tb-column4 {
    display: table-cell;
    width: 14%;
    text-overflow: ellipsis;
    text-align: right;
}

.fin-tb-column5 {
    display: table-cell;
    text-overflow: ellipsis;
    text-align: right;
    width: 14%;
}

.fin-tb-column6 {
    display: table-cell;
    text-overflow: ellipsis;
    text-align: right;
    width: 13%;
}

.fin-tb-column7 {
    display: table-cell;
    text-overflow: ellipsis;
    text-align: right;
    width: 6%;
}

.custom-priority-numeric {
    width: 24%;
}
/*consquence adjusted detail - end*/

/* Political Simulation style start*/
.custom-polsim-ass-btn {
    height: 26px !important;
    font-size: 12px !important;
    margin-top: 10px !important;
    padding-top: 3px !important;
}
/* Political Simulation style end*/

/*Budget Assumption*/

.budget-assumption-bg {
    background-image: url('../images/budget-assumption-banner.jpg');
    background-repeat: no-repeat;
    background-size: 100%;
}

.budget-assumption-dynamic-heading {
    margin-left: -26px;
    margin-top: 10px;
}

.budgetassumption-dynamic-heading {
    margin-top: 38px;
    color: #fff;
    font-size: 38px;
    font-family: impact, sans-serif;
}

#BudgetAdjustmentGrid {
    border: 1px solid #c3c3c3;
    overflow: auto;
    border-radius: 0px;
}

    #BudgetAdjustmentGrid.k-grid tr td {
        border-left: none;
        text-align: right;
        white-space: nowrap;
    }

#BudgetDetailsGrid {
    border: 1px solid #c3c3c3;
    overflow: auto;
    border-radius: 0px;
}

    #BudgetDetailsGrid.k-grid tr td {
        border-left: none;
        text-align: right;
        white-space: nowrap;
    }

.bud-ass-panel-radius {
    border-top-left-radius: 0px !important;
    border-top-right-radius: 0px !important;
    border-bottom-left-radius: 0px !important;
    border-bottom-right-radius: 0px !important;
    border: 1px solid rgb(195, 195, 195);
    border-left: 0;
    border-right: 0;
}

#ABAGridHeader {
    display: inline-flex;
}

    #ABAGridHeader div {
        display: inline-block;
        padding: .5em .6em .4em .6em;
    }

.bud-ass-panel {
    border-bottom: none !important;
    background-color: #fff !important;
    padding-top: 5px;
    padding-bottom: 0px;
}

.bud-ass-tb-column1 {
    display: table-cell;
    width: 28%;
    text-overflow: ellipsis;
    padding-left: 5px;
}

.bud-ass-tb-column2 {
    display: table-cell;
    width: 15%;
    text-overflow: ellipsis;
    text-align: right;
}

.bud-ass-tb-column3 {
    display: table-cell;
    width: 22%;
    text-overflow: ellipsis;
    text-align: right;
}

.bud-ass-tb-column4 {
    display: table-cell;
    width: 14%;
    text-overflow: ellipsis;
    text-align: right;
}

.bud-ass-tb-column5 {
    display: table-cell;
    text-overflow: ellipsis;
    text-align: right;
}

.bud-ass-tb-column6 {
    display: table-cell;
    width: 7%;
    text-overflow: ellipsis;
    text-align: right;
}

.bud-ass-grid-border-top {
    overflow: hidden;
    border-top: 1px solid rgb(195, 195, 195);
    padding-top: 10px;
}

.bud-panel-margin {
    margin: 0px 1px 0px 0px;
}



/*Budget Management Overview Start*/

.budget-year-left, .budget-year-right {
    width: 9%;
    padding: 5px 0px 0px 4px;
    height: 36px;
    background: #fff;
    border: 1px solid #6fa1b2;
}

.budget-year-center {
    height: 36px;
}

.budget-year-center {
    border-top: 1px solid #6fa1b2;
    border-bottom: 1px solid #6fa1b2;
}

    .budget-year-center .k-numeric-wrap {
        border-radius: 0px;
    }

.service-area-nav-tab {
    border: none;
    padding: 0;
    margin-left: 12px;
    margin-bottom: 2px;
}

.budget-management-dynamic-heading {
    margin-left: -26px;
    margin-top: 10px;
}

.budget-dynamic-heading {
    margin-top: 38px;
    color: #fff;
    font-size: 38px;
    font-family: impact, sans-serif;
    white-space: nowrap;
    text-shadow: 1px 1px 2px #000000;
}

.budget-section {
    border: none;
    padding: 0;
    padding-left: 11px;
    padding-bottom: 13px;
}

.budget-section-tablink {
    min-width: 160px;
    max-width: 160px;
    padding: 4px;
}

.budget-section > .budget-section-tablink .active > a.selected {
    background-color: #377B9A;
    color: #ffffff;
    border-color: #30697D;
}

#budgetSections.nav-tabs > li.active > a {
    background-color: #377B9A;
    color: #ffffff;
    border-radius: 5px;
    text-align: center;
    height: 45px;
    padding: 0px;
}

#budgetSections.nav-tabs > li > a {
    height: 45px;
    color: #377B9A;
    border-radius: 5px;
    background-color: #fff;
    text-align: center;
    border-color: #377B9A;
    padding: 0px;
}

.bm-tab-text {
    display: table-cell !important;
    width: 166px;
    vertical-align: middle;
}

.budget-management-bg {
    background-image: url('../images/budget-management_banner.jpg');
}


.bm-document-production-spacing {
    margin-top: 8px;
    margin-bottom: 8px;
}

.budget-production-spacing {
    border-top: none;
    margin-top: 0px;
    margin-bottom: 12px;
}

.bud-mang-right-bg {
    background-image: url(../images/kostra-analysis-right.png);
    margin-top: 12px;
    margin-left: -8px;
    background-repeat: no-repeat;
    background-size: 100%;
    /*min-height: 438px  !important;*/
    margin-bottom: 15px;
}

.bm-simulation-type {
    float: left;
    position: relative;
    height: 28px;
    margin: 5px 0 5px 20px;
    padding: 0px;
    bottom: 5px;
}

.bm-simulation-bck-style {
    background-color: #fff !important;
    height: 283px;
    overflow-y: auto;
}
/*graph css content banner*/
.bud-mang-indicator-box {
    margin-bottom: 0px;
    background-color: #fff;
    border: 1px solid #6FA1B2;
    border-radius: 4px;
    cursor: pointer;
}
/*budget management popup*/
#HistoricalGrid.k-grid tr td,
#budgetManagementReseveFundGrid.k-grid tr td,
#budgetManagementDebtvsRevenueGrid.k-grid tr td,
#budgetManagementDebtPerCitizenGrid.k-grid tr td,
#ConsequenceHistoricalGrid.k-grid tr td,
#ConsequenceReseveFundGrid.k-grid tr td,
#ConsequenceDebtvsRevenueGrid.k-grid tr td,
#ConsequenceDebtPerCitizenGrid.k-grid tr td,
#financeHistoricalGrid.k-grid tr td,
#financeReseveFundGrid.k-grid tr td,
#financeDebtvsRevenueGrid.k-grid tr td,
#politicalSimulationKPIPopupGrid.k-grid tr td,
#financeDebtPerCitizenGrid.k-grid tr td {
    border-left: none;
    text-align: right;
    white-space: nowrap;
}

#HistoricalGrid,
#budgetManagementReseveFundGrid,
#budgetManagementDebtvsRevenueGrid,
#budgetManagementDebtPerCitizenGrid,
#ConsequenceHistoricalGrid,
#ConsequenceReseveFundGrid,
#ConsequenceDebtvsRevenueGrid,
#ConsequenceDebtPerCitizenGrid,
#financeHistoricalGrid,
#financeReseveFundGrid,
#financeDebtvsRevenueGrid,
#financeDebtPerCitizenGrid,
#politicalSimulationKPIPopupGrid {
    overflow: auto;
    border: none;
}


.historical-chart {
    height: 75%;
}

.bud-mang-chart1 {
    margin-right: 15px;
    background-color: #fff;
    margin-top: 67px;
}

.objective {
    float: right;
    margin-right: 19px;
}

.bud-mang-grid-align {
    border: none;
    border-right: 1px solid #c3c3c3;
}


/*Budget Management - meeting tab Start*/


.bm-delete-pop-btn-style {
    margin-right: -6%;
}

.chk-bm-include-all {
    display: block;
    padding-left: 15px;
    text-indent: -19px;
}

.budget-meeting-service-tabsection {
    background: #f4f4f4;
    padding-bottom: 15px;
}

.budget-meeting-service-tabsection-border {
    background: #ffffff;
}

.budget-meeting-section {
    border: none;
    padding: 0;
    padding-bottom: 5px;
    margin-top: 10px;
}

.budget-meeting-section-tablink {
    min-width: 75px;
    margin-left: 4px;
}

.budget-meeting-section > .budget-meeting-section-tablink .active > a.selected {
    background-color: #377B9A;
    color: #ffffff;
    border-color: #30697D;
}

#budgetMeetingSections.nav-tabs > li.active > a {
    background-color: #377B9A;
    color: #ffffff;
    border-radius: 4px;
    border-color: #30697D;
}

#budgetMeetingSections.nav-tabs > li > a {
    height: 24px;
    color: #377B9A;
    border-radius: 4px;
    background-color: #fff;
    padding: 2px;
    text-align: center;
    border-color: #377B9A;
}

#budgetServiceAreaGrid1, #budgetServiceAreaGrid2, #budgetServiceAreaGrid3, #budgetServiceAreaGrid4, #BMFinancialOverviewGrid, #BPBudgetPrioritisedActionsGrid, #rejectedActionsGrid, #BMFinancialNotificationGrid {
    overflow: auto;
    border: none;
    background: #FFF;
}

    #BMFinancialNotificationGrid.k-grid tr td, #budgetServiceAreaGrid1.k-grid tr td, #budgetServiceAreaGrid2.k-grid tr td, #budgetServiceAreaGrid3.k-grid tr td, #budgetServiceAreaGrid4.k-grid tr td, #BMFinancialOverviewGrid.k-grid tr td, #BPBudgetRequiredActionGrid.k-grid tr td, #BPBudgetPrioritisedActionsGrid.k-grid tr td, #rejectedActionsGrid.k-grid tr td {
        border-left: none;
        text-align: right;
        white-space: nowrap;
    }

#serviceBudgetAdjustmentGrid {
    overflow: auto;
    border: none;
    box-shadow: none;
    background: transparent;
}


#desiredActionsGrid .k-grid-content, #simulationStatusActionsGrid .k-grid-content {
    overflow-y: auto !important;
    max-height: 600px;
    border-top: none !important;
}

#serviceBudgetAdjustmentGrid.k-grid tr td {
    text-align: right;
    white-space: nowrap;
    background: rgb(244,244,244);
    border: none;
}

.bud-action-proposal-tb-column1 {
    display: table-cell;
    text-align: left;
    width: 24%;
    text-overflow: ellipsis;
}

.bud-action-proposal-tb-column2 {
    display: table-cell;
    text-overflow: ellipsis;
    text-align: right;
    width: 10%;
}

.bud-action-proposal-tb-column3 {
    display: table-cell;
    text-overflow: ellipsis;
    text-align: right;
}

.bud-tooltip-style {
    color: #606060;
    padding: 2px;
    text-align: left;
    min-height: 300px !important;
}

.mr-tooltip-style {
    color: #606060;
    padding: 2px;
    text-align: left;
    min-height: 70px !important;
    word-break: break-word;
}

.action-prop-tooltip {
    background: url(../images/grid_tooltip_12.png);
    display: block;
    width: 15px;
    height: 15px;
    background-repeat: no-repeat;
}
/* Budget Management Workflow section start */

.bm-workflow-connector-style {
    margin-top: 20px;
    height: 1px;
    background-color: #c3c3c3;
    color: #c3c3c3;
    border: none;
}

.bm-active-workflow {
    border-color: #377B9A;
    border: 1px solid #377B9A;
    color: #fff;
    background: #377B9A;
}

.bm-active-number-workflow {
    background: #377B9A;
    color: #fff;
    border: 1px solid #fff;
    margin-left: 1px;
}

.bm-disabled-workflow {
    border-color: #377B9A;
    border: 1px solid #377B9A;
    cursor: pointer;
}

.bm-disabled-number-workflow {
    border: 1px solid #377B9A;
}

/* Budget Management Workflow section end */



/*Budget Management Overview Graph sections start*/
.bm-overview-graphs-section-style {
    background: #F4F4F4;
    min-height: 510px;
}

.bm-overview-graphs-section1 {
    background: #fff;
    min-height: 473px;
    width: 33%;
}

.bm-overview-graphs-section2 {
    background: #fff;
    min-height: 473px;
    width: 33%;
}

.bm-overview-graphs-section3 {
    background: #fff;
    min-height: 473px;
    width: 32%;
}

.bm-overview-filler-1 {
    background: #F4F4F4;
    min-height: 473px;
    width: 1%;
}

.bm-overview-filler-2 {
    background: #F4F4F4;
    min-height: 473px;
    width: 1%;
}

.bm-overview-filler-3 {
    background: #F4F4F4;
    min-height: 473px;
    width: 0%;
}

/*Temp section css*/

.temp-bm-overview-graphs-section1 {
    background: #fff;
    min-height: 473px;
    width: 49%;
}

.temp-bm-overview-filler-1 {
    background: #F4F4F4;
    min-height: 473px;
    width: 1%;
}

.temp-bm-overview-graphs-section2 {
    background: #fff;
    min-height: 473px;
    width: 50%;
}

.temp-bm-overview-filler-2 {
    background: #F4F4F4;
    min-height: 473px;
    width: 0%;
}

/*Temp section css*/

/*Budget Management Overview Graph sections end*/

/*Budget Management - meeting tab End*/

/*Budget Management Overview End*/

/*Budget Management Detail CSS Start*/

.bm-acc-header-left {
    left: -5px;
    bottom: -3px;
    position: relative;
}

#bmDetailBudgetAdjServiceAreaTreeGrid {
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
    border-bottom: none;
    overflow: auto;
    margin-top: 0px;
    border-top: none;
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
}

#BMDetailEditorSection {
    border: 1px solid #c3c3c3;
    background-color: #fff;
    border-bottom: none;
}


#bmDetailBudgetAdjServiceAreaGrid {
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    border-top: none;
    overflow: auto;
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
}

#bmDetailBudgetAdjTotalsTreeGrid {
    border-top: none;
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
}

#bmDetailBudgetAdjServiceAreaGrid .k-grid-header {
    height: 0;
    border-bottom-width: 0;
    display: none;
    overflow: hidden;
}
/*end*/

/*Budget Management Detail CSS End*/


/*Budget Proposal Css start*/

.budget-proposal-bg {
    background-image: url('../images/budget-proposal_banner.jpg');
}

.budget-proposal-dynamic-heading {
    margin-left: -26px;
    margin-top: 10px;
}

.budget-pro-dynamic-heading {
    margin-top: 38px;
    color: #fff;
    font-size: 38px;
    font-family: impact, sans-serif;
}

.budget-proposal-section {
    border: none;
    padding: 0;
    padding-bottom: 13px;
}

.budget-proposal-section-tablink {
    min-width: 160px;
    max-width: 160px;
    padding-right: 4px;
}

.bp-hente-padding {
    padding-bottom: 8px;
    padding-top: 8px;
    border-radius: 0px;
    padding-left: 20px;
    padding-right: 20px;
}

    .bp-hente-padding:hover {
        border-radius: 0px !important;
    }

.budget-section > .budget-proposal-section-tablink .active > a.selected {
    background-color: #377B9A;
    color: #ffffff;
    border-color: #30697D;
}

#politicsTabSection li a, #politicsTabSection li.active a {
    height: 40px !important;
}

#budgetProposalSections.nav-tabs > li > a {
    height: 45px;
    color: #377B9A;
    border-radius: 5px;
    background-color: #fff;
    text-align: center;
    border-color: #377B9A;
    padding: 0px;
}

#budgetProposalSections.nav-tabs > li.active > a {
    background-color: #377B9A;
    color: #fff;
    border-radius: 5px;
    height: 45px;
    padding: 0px;
}

#freeDimGrantGridParent .k-grid-header th {
    color: #428bca !important;
}

#addNewDashBoard.active, #editSettingsDashBoard.active {
    background-color: #377B9A;
    color: #fff !important;
}

#dashBoardTabSections li > a, #dashBoardTabSections li.active > a {
    height: 35px !important;
}

.budget-proposal-tab-text {
    display: table-cell !important;
    width: 165px;
    vertical-align: middle;
}

.budget-proposal-common-save {
    margin-right: 25px !important;
    margin-top: 5px !important;
    padding-left: 25px !important;
    padding-right: 25px !important;
}
/*Budget Proposal overview tab(first) css start*/

.proposal-overview-service-tabsection {
    background: #f4f4f4;
    padding-bottom: 15px;
}

.proposal-overview-service-tabsection-border {
    background: #ffffff;
}

.proposal-overview-section {
    border: none;
    padding: 0;
    margin-left: 16px;
    padding-bottom: 5px;
    margin-top: 10px;
}

.proposal-overview-section-tablink {
    min-width: 75px;
    margin-left: 4px;
}

.proposal-overview-section > .proposal-overview-section-tablink .active > a.selected {
    background-color: #377B9A;
    color: #ffffff;
    border-color: #30697D;
}

#proposalOverviewSections.nav-tabs > li.active > a {
    background-color: #377B9A;
    color: #ffffff;
    border-radius: 4px;
    border-color: #30697D;
}

#proposalOverviewSections.nav-tabs > li > a {
    height: 24px;
    color: #377B9A;
    border-radius: 4px;
    background-color: #fff;
    padding: 2px;
    text-align: center;
    border-color: #377B9A;
}

#budgetProposalOverChildSections.nav-tabs > li.active > a {
    background-color: #377B9A;
    color: #ffffff;
    border-radius: 4px;
    border-color: #30697D;
}

#budgetProposalOverChildSections.nav-tabs > li > a {
    height: 24px;
    color: #377B9A;
    border-radius: 4px;
    background-color: #fff;
    padding: 2px;
    text-align: center;
    border-color: #377B9A;
}

#proposalServiceDirectorGrid1, #proposalServiceDirectorGrid2, #proposalServiceDirectorGrid3, #proposalServiceDirectorGrid4 {
    overflow: auto;
    border: none;
    background: #FFF;
}

    #proposalServiceDirectorGrid1.k-grid tr td,
    #proposalServiceDirectorGrid2.k-grid tr td,
    #proposalServiceDirectorGrid3.k-grid tr td,
    #proposalServiceDirectorGrid4.k-grid tr td {
        border-left: none;
        white-space: nowrap;
    }

#BPBudgetCostReducitonGrid, #BPBudgetNewPrioritiesGrid, #BPBudgetPrioritisedActionsGrid, #BPBudgetRequiredActionGrid {
    border-bottom: 1px solid #c3c3c3 !important;
}


/*Budget Proposal - Proposal Tab CSS Start */
.budget-proposals-section {
    border: none;
    padding: 0;
    padding-bottom: 10px;
}

.budget-proposals-section-tablink {
    min-width: 160px;
    max-width: 160px;
    padding: 4px;
}

.budget-proposals-section > .budget-proposals-section-tablink .active > a.selected {
    background-color: #377B9A;
    color: #ffffff;
    border-color: #30697D;
}

.budget-proposals-lock-section-tablink {
    min-width: 180px;
    max-width: 180px;
    padding: 4px;
}

.budget-serviceunit-section > .budget-proposals-lock-section-tablink .active > a.selected {
    background-color: #377B9A;
    color: #ffffff;
    border-color: #30697D;
}


#budgetProposalTabSections.nav-tabs > li.active > a,
#bpResponsibleTabSections.nav-tabs > li.active > a,
#municipalTabSections.nav-tabs > li.active > a,
#visionTabSections.nav-tabs > li.active > a,
#bpNewStrategyTabSections .nav-tabs > li.active > a,
#investmentServiceTabSections.nav-tabs > li.active > a,
#internalActionTabSections.nav-tabs > li.active > a,
#indicatorAnalysisTabSections.nav-tabs > li.active > a,
#serviceUnitTabLockSections.nav-tabs > li.active > a,
#serviceUnitTabCostLockSections.nav-tabs > li.active > a,
#dispForecastTabSections.nav-tabs > li.active > a,
#planTabSections.nav-tabs > li.active > a,
#focusAreaTabSections.nav-tabs > li.active > a,
#bpFeeTabSections.nav-tabs > li.active > a {
    background-color: #377B9A;
    color: #fff;
    border-radius: 5px;
    text-align: center;
    height: 36px;
    padding: 0px;
}

.budget-proposals-tab-text {
    display: table-cell !important;
    width: 150px;
    vertical-align: middle;
}

.budget-proposals-lock-tab-text {
    display: inline-block !important;
    width: 175px;
    vertical-align: middle;
    padding: 4px !important;
}

.lock-button-style {
    display: inline-block;
    border: 1px solid #367b9a;
    padding: 4px;
    width: 175px;
    border-radius: 4px;
    position: relative;
    height: 30px;
    text-align: center;
}

.lockButton-input-style {
    position: absolute;
    left: 20px;
    top: 4px;
}

#adjustmentCode {
    color: #367b9a;
    width: 100%;
    height: 30px;
}

.adjCodeDescription-textbox-style {
    width: 60%;
    min-height: 33px;
}

.active .lock-button-style:hover {
    color: #fff;
}

.active .lock-button-style {
    background-color: #367b9a;
    color: #fff;
}

.lock-button-text-style {
    position: absolute;
    left: 50px;
    top: 6px;
}

.budget-proposals-inner-section {
    border: none;
    padding: 0;
    padding-left: 18px;
    padding-bottom: 13px;
}

.budget-proposals-inner-section-tablink {
    min-width: 150px;
    max-width: 150px;
    padding: 4px;
}

.budget-proposals-inner-section > .budget-proposals-inner-section-tablink .active > a.selected {
    background-color: #377B9A;
    color: #fff;
    border-color: #30697D;
}

#budgetProposalTabInnerSections.nav-tabs > li.active > a {
    background-color: #377B9A;
    color: #fff;
    border-radius: 5px;
    height: 40px;
    padding-top: 2px;
    padding-bottom: 0px;
}

#budgetProposalTabInnerSections.nav-tabs > li > a {
    height: 40px;
    color: #ffffff;
    border-radius: 5px;
    background-color: #B2B2B2;
    text-align: center;
    padding-top: 2px;
    padding-bottom: 0px;
}

.budget-proposals-inner-tab-text {
    display: table-cell !important;
    width: 150px;
    vertical-align: middle;
}


.bm-proposals-budget-task-left {
    margin-left: -10px;
}

.bm-proposals-deadline-left {
    margin-left: -15px;
}

.bm-proposal-current-budget-border {
    border: 1px solid #C3C3C3;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    background-color: #fff;
    margin-top: 10px;
}

#deletedActionsBySAGrid.k-grid tr td, #deletedActionsByCAGrid.k-grid tr td {
    border-left: none;
    text-align: right;
    white-space: nowrap;
}

#startegyTabSections.nav-tabs > li.active > a {
    background-color: #377B9A;
    color: #fff;
    border-radius: 5px;
    height: 35px;
    text-align: center;
    padding: 0px;
}

#startegyTabSections.nav-tabs > li > a {
    height: 35px;
    color: #377B9A;
    border-radius: 5px;
    background-color: #fff;
    text-align: center;
    padding: 0px;
    border-color: #377B9A;
}

#keyActivityTabSections.nav-tabs > li > a,
#bpNewStrategyTabSections.nav-tabs > li > a {
    height: 35px;
    color: #377B9A;
    border-radius: 5px;
    background-color: #fff;
    text-align: center;
    padding: 0px;
    border-color: #377B9A;
}


#keyActivityTabSections.nav-tabs > li.active > a,
#bpNewStrategyTabSections.nav-tabs > li.active > a {
    background-color: #377B9A;
    color: #fff;
    border-radius: 5px;
    height: 35px;
    text-align: center;
    padding: 0px;
}

.bp-txt-align {
    text-align: justify;
}

.bp-delete-textarea-style {
    width: 100%;
    background-color: #fff;
    background-image: none;
    border: 1px solid #6fa1b2 !important;
    border-radius: 4px;
    padding: 5px;
}

.bp-show-tasks {
    position: relative;
    top: -32px;
    right: 8px;
}

.bp-show-task-chk {
    margin-left: 10px;
    top: 3px;
    position: relative;
}

.bp-delete-tooltip {
    background: url(../images/bp_delete_tooltip.png);
    display: inline-block;
    width: 20px;
    height: 17px;
    background-repeat: no-repeat;
    vertical-align: middle;
}
/* budget proposal - serviceunit budget css start */
#serviceUnitTabSections.nav-tabs > li.active > a {
    background-color: #377B9A;
    color: #ffffff;
    border-radius: 5px;
    height: 35px;
    text-align: center;
    padding: 0px;
}

#serviceUnitTabSections.nav-tabs > li > a {
    height: 35px;
    color: #377B9A;
    border-radius: 5px;
    background-color: #fff;
    text-align: center;
    padding: 0px;
    border-color: #377B9A;
}

#serviceUnitTabLockSections.nav-tabs > li.active > a {
    background-color: #377B9A;
    color: #ffffff;
    border-radius: 5px;
    height: 30px;
    text-align: center;
    padding: 0px;
}

#serviceUnitTabLockSections.nav-tabs > li > p {
    height: 30px;
    color: #377B9A;
    border-radius: 5px;
    background-color: #fff;
    text-align: center;
    padding: 0px;
    border-color: #377B9A;
    border: 1px solid #377B9A;
}

#serviceUnitTabLockSectionsSP.nav-tabs > li > p {
    height: 30px;
    color: #377B9A;
    border-radius: 5px;
    background-color: #fff;
    text-align: center;
    padding: 0px;
    border-color: #377B9A;
    border: 1px solid #377B9A;
}

#serviceUnitTabCostLockSections.nav-tabs > li > a {
    height: 30px;
    color: #377B9A;
    border-radius: 5px;
    background-color: #fff;
    text-align: center;
    padding: 0px;
    border-color: #377B9A;
}

#serviceUnitTabCostLockSections.nav-tabs > li.active > a {
    background-color: #377B9A;
    color: #ffffff;
    border-radius: 5px;
    height: 30px;
    text-align: center;
    padding: 0px;
}

#serviceUnitTabLockSections.nav-tabs > li > p.active,
#serviceUnitGridSections.nav-tabs > li.active > p,
#serviceUnitTabLockSectionsSP.nav-tabs > li > p.active,
#serviceUnitGridSectionsSP.nav-tabs > li.active > p,
.nav-tabs > li.active > p:hover,
.nav-tabs > li p.active:hover,
.nav-tabs > li a.active:hover,
#serviceUnitTabCostLockSections.nav-tabs > li > a.active,
#serviceUnitGridSections.nav-tabs > li.active > a {
    background-color: #377B9A !important;
    color: #fff !important;
    border-radius: 4px;
    border-color: #30697D;
}

#yearbuttonsTabSection li:last-child a {
    border-bottom-right-radius: 4px;
    border-top-right-radius: 4px;
}


#serviceUnitGridSections.nav-tabs > li > a {
    height: 24px;
    color: #377B9A;
    border-radius: 4px;
    background-color: #fff;
    padding: 2px;
    text-align: center;
    border-color: #377B9A;
}

.budget-proposal-seviceunit-tablink {
    min-width: 75px;
    top: 2px;
    padding: 4px;
}

.budget-proposals-serviceunit-tab-text {
    display: table-cell !important;
    width: 75px;
    vertical-align: middle;
}

#proposalServiceUnitGrid1 {
    border: 1px solid #c3c3c3;
    overflow: auto;
    border-radius: 0px;
}

    #proposalServiceUnitGrid1.k-grid tr td {
        border-left: none;
        text-align: right;
        white-space: nowrap;
    }

.reset-allocation-style {
    width: 100px;
    white-space: normal;
    font-size: 12px;
    padding-top: 3px;
    padding-bottom: 4px;
}
/* budget proposal - serviceunit budget css end */

/* budget proposal plan tab start*/
.btn-bp-plan-get-style {
    border-radius: 0;
    padding-left: 30px;
    padding-right: 30px;
}
/* budget proposal plan tab end*/
/* budget proposal - focus area css start */
#budgetProposalFocusAreaContainer .k-dropdown {
    width: 25%;
}
/* budget proposal - focus area  css start */
/* End */
.selected {
    width: 108% !important;
}

.status {
    text-align: left;
}

/*financial Plan*/
.budgetfinancialeditor {
    background-color: #fff;
    margin-top: 41px;
    margin-bottom: 89px;
}

.financal-date-meeting {
    margin-top: -74px;
    margin-bottom: 38px;
}

.financial-meetingName {
    margin-right: 4px;
    margin-left: -1px;
}
/*financial ends*/


#budgetTaskGrid.k-grid tr td {
    border-left: none;
    white-space: nowrap;
}
/* Financing Overview CSS Start */

.financing-overview-bg {
    background-image: url('../images/finance_banner.jpg');
    background-repeat: no-repeat;
    background-size: 100%;
}


/* Financing Overview CSS End*/

.financing-project-bg {
    background-image: url('../images/admin_banner.jpg');
}

.admin-invforecast-bg {
    background-image: url('../images/financing_project.jpg');
}



/* Staff Planning css start*/

.staff-plan-bg {
    background-image: url('../images/staff-planning-banner.jpg');
}

.business-plan-bg {
    background-image: url('../images/business_plan_banner.jpg');
    background-size: cover;
}

.staff-planning-dynamic-heading {
    margin-left: -26px;
    margin-top: 10px;
}

.staff-dynamic-heading {
    margin-top: 38px;
    color: #fff;
    font-size: 38px;
    font-family: impact, sans-serif;
    text-shadow: 1px 1px 2px #000000;
}

.staff-section {
    border: none;
    padding: 0;
    padding-left: 11px;
    padding-bottom: 13px;
}

.staff-section-tablink {
    min-width: 160px;
    max-width: 160px;
    padding: 4px;
}

.staff-section > .staff-section-tablink .active > a.selected {
    background-color: #377B9A;
    color: #ffffff;
    border-color: #30697D;
}

#staffSections.nav-tabs > li.active > a {
    background-color: #377B9A;
    color: #ffffff;
    border-radius: 5px;
    text-align: center;
    height: 45px;
    padding: 0px;
}

#staffSections.nav-tabs > li > a {
    height: 45px;
    color: #377B9A;
    border-radius: 5px;
    background-color: #fff;
    text-align: center;
    border-color: #377B9A;
    padding: 0px;
}

.staff-plan-section {
    border: none;
    padding: 0;
    padding-left: 11px;
    padding-bottom: 13px;
}

.staff-plan-section-tablink {
    min-width: 100px;
    max-width: 100px;
    padding: 4px;
}

.staff-plan-section > .staff-plan-section-tablink .active > a.selected {
    background-color: #377B9A;
    color: #ffffff;
    border-color: #30697D;
}

#userDroplistIcon li, #helpcenterDropdownContainer li {
    list-style: none;
}

#staffPlanSections.nav-tabs > li.active > a {
    background-color: #377B9A;
    color: #ffffff;
    border-radius: 5px;
    text-align: center;
    height: 30px;
    padding: 0px;
}

#staffPlanSections.nav-tabs > li > a {
    height: 30px;
    color: #377B9A;
    border-radius: 5px;
    background-color: #fff;
    text-align: center;
    border-color: #377B9A;
    padding: 0px;
}

.staff-plan-tab-text {
    display: table-cell !important;
    width: 90px;
    vertical-align: middle;
}

.position-radio-text {
    position: relative;
    top: 3px;
}


/*Yealy budget css start*/
.yb-checkbox-lbl-style {
    display: block;
    text-align: left;
}

.yearly-budget-bg {
    background-image: url('../images/yearly_budget_banner-2.jpg');
}

.political-sim-bg {
    background-image: url('../images/political_sim_banner.jpg');
}

.yearly-dynamic-heading {
    margin-top: 38px;
    color: #fff;
    font-size: 38px;
    font-family: impact, sans-serif;
}

.yearly-budget-dynamic-heading {
    margin-left: -26px;
    margin-top: 10px;
}


.hide-yearly-budget-section {
    display: none !important;
}
/*Yealy budget css end*/

/*financial plan pop up import button /kendo upload css*/
.fin-upload-style, .fin-upload-style:hover {
    background: #f3f4f4 !important;
    border: none;
}

.fm-upload-height-auto {
    height: auto !important;
}

.import-btn-style {
    margin-top: 4px;
    padding: 2px 7px;
    display: inline-block;
    padding-top: 0px;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.42857143;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 4px;
}
/*financial plan pop up import button /kendo upload  css*/

.bm-doc-window-style {
    padding-left: 9%;
    padding-top: 5px;
}

/*Ck Editor - Budget Management Editor Changes Start*/
.bp-financial-save-style {
    width: 680px !important;
}

.bm-meeting-editor-width {
    width: 601px !important;
}

/*Ck Editor - Budget Management Editor Changes End*/


/*CK Editor Changes for budget management detail Start*/
.bm-detail-fixed-width {
    width: 674px !important;
}

/*CK Editor Changes for budget management detail End*/

/*CK Editor Changes for budget proposal Start*/

.bp_ck_editor_body_witdh {
    width: 632px !important;
}

/*CK Editor Changes for budget proposal end*/

/*CK Editor Changes for consequences/ financial overview start*/

.ck-consequences-editor-body-width {
    width: 632px !important;
}
/*CK Editor Changes for consequences/ financial overview end*/

/*CK Editor Changes for opp pop up start*/
.opp-assessment-popup-width {
    width: 600px !important;
}

.opp-assessment-area-title-width {
    width: 575px;
}
/*CK Editor Changes for opp pop up end*/

/*Common CK Editor Fixed Width Section Start*/

.cke_width {
    max-width: 600px !important;
}

.ck-editor-body-width {
    width: 662px !important;
}

/*Common CK Editor Fixed Width Section End*/

/*CK Editor Changes for Investments Start*/

#investmentStrategySection {
    width: 602px;
}
/*CK Editor Changes for Investments End*/

.fin-tb-column1, .fin-tb-column2, .fin-tb-column3, .fin-tb-column4, .fin-tb-column5, .fin-tb-column6, .fin-tb-column7 {
    padding: 0.4em 0.6em;
    left: 0 !important;
}

#blistFinTotalYear1, #blistFinTotalYear2, #blistFinTotalYear3 {
    padding-right: 0px;
}


#financialGrid1,
#operationalGrid1,
#operationalGridNew1 {
    margin-top: 5px;
}

    #financialGrid1 tbody tr td,
    #operationalGrid1 tbody tr td,
    #operationalGridNew1 tbody tr td {
        text-align: right;
        border-top: 0;
    }

        #financialGrid1 tbody tr td:first-child,
        #operationalGrid1 tbody tr td:first-child,
        #operationalGridNew1 tbody tr td:first-child {
            text-align: left !important;
        }


/*Yearly Budget*/
.budget-entry-section-background {
    background: #fff;
    border: 1px solid #c3c3c3;
}

.budget-entry-section-style {
    border-top: 1px solid #c3c3c3;
}

#informationField {
    background-color: #f3f1f2;
}

.budget-expcollapse-editor {
    width: 85%;
}
/*Yearly Budget*/

/*staff planning*/
#staffGrid {
    border: 1px solid #c3c3c3;
    border-radius: 0px;
    border-bottom: none;
}

#staffGrid1 {
    border-top: none;
}

    #staffGrid.k-grid tr td, #staffGrid1.k-grid tr td {
        border-left: none;
        text-align: right;
    }

#addOnsPopupGrid, #addOnsPopupGrid1 {
    border: 1px solid #c3c3c3;
    border-radius: 0px;
}

#addOnsPopupGrid1 {
    border-top: none;
}

    #addOnsPopupGrid.k-grid tr td, #addOnsPopupGrid1.k-grid tr td {
        border-left: none;
        text-align: right;
    }

/*fin plan setup start*/

#finPlanSetupGrid .position-rule.k-input {
    border: 1px solid #377b9a !important;
}

#finPlanSetupGrid .handling-rule.k-input {
    border: 1px solid #377b9a !important;
}

/*fin plan setup end*/

/*fin plan reporting start*/
#finPlanReportingGrid {
    overflow: auto;
}

    #finPlanReportingGrid.k-grid tr td {
        border-left: none;
        white-space: nowrap;
    }

.hide-selected > li.k-state-selected {
    display: none;
}
/*fin plan reporting end*/

/* investment reporting */
#investmentReportingGrid {
    overflow: auto;
}
/* investment reporting end*/
#monthlyReportServiceAreaContainer .k-dropdown {
    width: 75%;
}
/* monthly report css start*/

#MRInvestmentInputName {
    width: 87%;
    border-radius: 4px;
    border-color: #6FA1B2 !important;
    border-width: thin;
    border-style: solid;
    padding: 6px;
}

#monthlyReportingSaveBtn, #monthlyReportingSaveBtn1 {
    background: #367b9a;
    color: #fff;
}

.mnrpt-button-style {
    height: 32px !important;
    width: 100% !important;
    background: #fff;
}

.monthly-report-bg {
    background-image: url('../images/monthly-report-banner.jpg');
    background-size: cover;
}

.monthly-forecast-bg {
    background-image: url('../images/monthlyforecast_banner.jpg');
}

.monthly-topdrop-style {
    background-color: #fff !important;
    border-radius: 0px !important;
    height: 25px;
}

.month-year-left-img, .month-year-right-img {
    padding: 7px 5px !important;
}

.monthly-report-top-tabs {
    width: 98%;
}

.monthly-top-drop-label {
    color: #fff;
    text-shadow: 1px 1px 2px #000000;
}

.dynamic-widget-dashboard-bg {
    background-image: url('../images/kostra_doc_export_menu.jpg');
}

.arrowtab-navbar-content {
    background-color: #fff;
    float: left;
    color: #000000;
    height: 48px;
    position: relative;
    min-width: 5%;
}

    .arrowtab-navbar-content:hover {
        color: #4A8EB9;
        text-decoration: underline;
    }

.anchor-style,
.anchor-style:hover,
.arrowtab-navbar-current:hover {
    color: #fff;
    text-decoration: none;
}

.arrowtab-navbar-arrow {
    border-color: transparent transparent transparent #fff;
    border-style: solid;
    border-width: 1.7em 0.6em 1.7em 1.7em;
    float: left;
    height: 0;
    width: 0;
    margin-bottom: 3px;
}

.arrowtab-navbar-arrow-hook {
    border-color: #fff #fff #fff transparent;
    border-style: solid;
    border-width: 24px;
    float: left;
    height: 0;
    width: 0;
    margin-left: -2em;
}

.arrowtab-navbar-arrow-hook-current {
    border-color: #377B9A #377B9A #377B9A transparent;
}

.arrowtab-navbar-arrow-current {
    border-color: transparent transparent transparent #377B9A;
}

.arrowtab-navbar-current {
    background-color: #377B9A;
    color: #FFFFFF;
}

#MRTargetGrid .acheived-result .k-numeric-wrap, #MRTargetGrid .forecast-value .k-numeric-wrap {
    border-width: 0;
}

    #MRTargetGrid .acheived-result .k-numeric-wrap:before, #MRTargetGrid .forecast-value .k-numeric-wrap:before, .changeAmount-items .k-numeric-wrap:before, #plan-sorting-items .k-numeric-wrap:before, #proposalServiceUnitTreelist td .k-i-none:first-child, #operationalGrid .k-i-none:first-child, #operationalGridNew .k-i-none:first-child, #operationalGrid .k-i-none:nth-child(2), #operationalGridNew .k-i-none:nth-child(2), #operationalOrgStructureGrid .k-i-none:first-child, #operationalOrgStructureGrid .k-i-none:nth-child(2) {
        display: none !important;
    }

#MRTargetGrid .acheived-result.k-input, #MRTargetGrid .forecast-value.k-input {
    border: 1px solid #ccc;
}

#proposalServiceUnitTreelist .k-icon {
    float: left;
    top: 2px;
}

@media (min-width: 992px) {
    .monthly-report-head-selector {
        width: 21% !important;
    }
}

@media (max-width: 1152px) {
    .month-year-left-img, .month-year-right-img {
        padding: 7px 2px !important;
    }

    .monthly-report-bg {
        background-image: url(../images/monthly-report-banner-1040.jpg);
    }

    #staffPositionSettings {
        width: 26%;
    }

    .box .k-chart {
        height: 200px !important;
    }
}


@media (max-width: 1024px) {
    .header-top-position {
        position: absolute;
    }

    #FPBCContentWrapper, #budgetPropContentWrapper, #financialPlanBCWindow, #BPWindow {
        padding-left: 7px !important;
    }

    #collapseActionInformation .styleWrapper, #collapseActionDesc .styleWrapper {
        width: 96.5% !important;
        padding-right: 27px !important;
    }

    #budgetChangesTab li {
        width: 27% !important;
    }

    .filter-doc-dropdown {
        width: 120px;
    }

    .box .k-chart {
        height: 320px !important;
    }

    .add-dashboard-img {
        background: #fff;
        padding-top: 8px;
        padding-bottom: 3px;
        display: block;
        height: 33px;
        margin-right: 10px;
        color: #000 !important;
        position: relative;
        bottom: 5px;
    }
}

#monthlyReportTabSection {
    width: 94%;
    position: absolute;
    top: 23%;
}

    #monthlyReportTabSection a {
        height: 80px;
        display: inline-block;
        width: 15%;
        color: #fff;
        text-align: center;
        padding-top: 20px;
        margin-left: -38px;
        font-size: 16px;
        font-weight: 600;
    }

#MNVisningDropDown .k-widget.k-dropdown, #MRPopVisningDropDown .k-widget.k-dropdown {
    margin-top: 5px !important;
}

#MNVisningDropDown .k-dropdown-wrap, #MRPopVisningDropDown .k-dropdown-wrap, #freeDimGrantViewDropDown .k-dropdown-wrap {
    height: 25px !important;
    font-size: 13px;
}

#MNVisningDropDown .k-input, #MRPopVisningDropDown .k-input, #freeDimGrantViewDropDown .k-input {
    padding-top: 0px !important
}

.MRServiceid-chkbox {
    position: absolute;
    left: 5px;
    top: 2px;
    width: 11px;
    height: 11px;
    border-radius: 10px;
}

.MRServiceid-chkbox-selected {
    position: absolute;
    left: 3px;
    top: 2px;
    width: 11px;
    height: 11px;
    border-radius: 10px;
    visibility: hidden;
}

label.servIdCheckbox {
    position: absolute;
    left: 3px;
    top: 6px;
    width: 11px;
    height: 11px;
    border-radius: 10px;
    cursor: pointer;
}

#serviceIdSection input[type=checkbox]:checked + label.servIdCheckbox:after {
    opacity: 1;
}

#CommonDescPopupServiceSelector input[type=checkbox]:checked + label.servIdCheckbox:after {
    opacity: 1;
}

.serviceIdSection label:hover::after {
    opacity: 0;
}

label.servIdCheckbox:after {
    content: '';
    width: 8px;
    height: 5px;
    position: absolute;
    top: 1px;
    left: 4px;
    border: 2px solid #fff;
    border-top: none;
    border-right: none;
    background: transparent;
    opacity: 0;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
}

#CommonDescPopupServiceSelector label {
    background: #fff !important;
}

    #CommonDescPopupServiceSelector label.servIdCheckbox:after {
        border-color: #367b9a !important;
        left: 2px !important;
        top: 0px !important;
    }

#CommonDescPopupServiceSelector img {
    height: 20px;
    width: 20px;
}

#MRFinplanMaxWindow .display-original-plan, #MRInvestmentsMaxWindow .display-original-plan, #MRFinplanActionMaxWindow .display-original-plan, #MRGoalsMaxWindow .cmn-btns-wrapper, #MRTargetMaxWindow .cmn-btns-wrapper {
    position: fixed;
    bottom: 10px;
    z-index: 9999;
}

#MRFinplanMaxWindow #monthlyReportSavetooltipPopup, #MRInvestmentsMaxWindow #MRInvestmentSave, #MRFinplanActionMaxWindow #monthlyFinancialSaveButtonPopup, #MRGoalsMaxWindow #monthlyGoalsMaxSaveButton, #MRTargetMaxWindow #monthlyTargetMaxSaveButton {
    margin-right: 15px;
}
/* monthly report css end*/
/*Document Export reporting starts*/
.docexport-report-bg {
    background-image: url('../images/banner_image.jpg');
    background-repeat: no-repeat;
    background-size: 100%;
}
/*Document Export reporting endf*/

/*AccountStatementView css start*/

#absenceBarGraph1 circle {
    display: none !important;
}

/*AccountStatementView css end*/

/* Common filter styles for StaffPlanning/SalaryForecast/YearlyBueget/CentralBudget start*/
.list-view-scroll {
    overflow-x: hidden;
    overflow-y: scroll;
    padding: 0;
    height: 215px;
}

    .list-view-scroll li a {
        color: #377B9A;
        width: 86%;
        margin: 5px;
        height: 18px;
        padding: 5px 0px 5px 10px;
        font-size: 13px;
        overflow: hidden;
        display: inline-block;
        white-space: nowrap;
    }

        .list-view-scroll li a.form-control:focus {
            box-shadow: none !important;
            color: #555;
        }

.commonHeader {
    border-bottom: 1px dashed #c3c3c3;
    padding: 5px;
}

    .commonHeader span {
        margin: 10px;
    }

    .commonHeader input {
        float: right;
        margin-right: 0px !important;
        color: #377B9A;
    }

.filteractive {
    border: 2px solid #377B9A !important;
    background-color: #377B9A !important;
    color: #fff !important;
}

a.filteractive:hover {
    color: #fff !important;
}

#summarySalaryGrid tr th:nth-child(2n+3), #summarySalaryGrid tr td:nth-child(2n+3) {
    background-color: #F8F8F8;
}

.filterkeys {
    display: none;
}

#listDepartments li a.notActive {
    pointer-events: none;
    cursor: default;
}

#applyFilter {
    padding: 5px 25px;
}

#menuToggle {
    cursor: pointer;
    display: inline-block;
    position: absolute;
    top: 55%;
    left: 0%;
    width: 200px;
    transform: translate(0, -50%) rotate(-90deg);
    -webkit-transform: translate(0, -50%) rotate(-90deg);
    -webkit-transform-origin: left top 0;
}

    #menuToggle #expandImg {
        transform: rotate(180deg);
        -webkit-transform: rotate(180deg);
    }

    #menuToggle #collapseImg {
        transform: rotate(90deg);
        -webkit-transform: rotate(90deg);
    }

#toggleMenuWrap {
    position: absolute;
    right: 0;
}

    #toggleMenuWrap.menu-toggle-parent {
        left: 0;
    }

#leftSideBarContent ul li {
    list-style: none;
    padding: 5px 0px;
}

#leftSideBarContent ul {
    margin: 0;
    padding: 3px 10px !important;
}

    #leftSideBarContent ul.panel-collapse, #filterAll {
        border-bottom: 1px dotted #606060;
        margin-bottom: 5px;
    }

#listFreeDiamention, #listDepartmentAll, #listFunctionAll, #listProjectAll, #listFreeDiamentionAll {
    margin: 0;
    border-bottom: 0 none !important;
}

#collapseDepartmentFilter {
    margin: 0;
    padding-right: 0;
}

.width-selection {
    width: 22px;
}

.width-selections {
    width: 98%;
}

/* Common filter styles for StaffPlanning/SalaryForecast/YearlyBueget/CentralBudget end*/

/* Common Expense accounting Popup start*/

#yearlyBudgetAccDetailWindow {
    margin-top: 0;
    padding-top: 0;
}

#yearlyBudgetExpenseAccountingWindow {
    padding: 10px !important;
}

.titleWindoeDescription {
    font-family: regularFont, 'Open Sans Regular', sans-serif;
    font-size: 13px;
    padding-left: 14px;
    font-weight: normal;
    border-bottom: 1px solid #c3c3c3;
    padding-bottom: 5px;
}

#yearlyBudgetAccDetailWindow_wnd_title,
#yearlyBudgetExpenseAccountingWindow_wnd_title,
#FPBudgetChangesDetailWindow_wnd_title {
    font-family: semiFont, 'Open Sans Semibold', sans-serif;
    font-size: 15px;
}

#financialPlanBCPopupGrid,
#financialPlanBCPopupGrid1,
#financialPlanPopupTotalGrid,
#yearlyBudgetPopupGrid tr td, #yearlyBudgetPopupGrid1 tr td,
#YBExpenseAccountingGrid tr td,
#YBExpenseAccountingTotalGrid tr td {
    border: 0 none;
}

#yearlyBudgetAccDetailWindow .k-header.k-grid-toolbar {
    border-left: 0 none !important;
    border-right: 0 none !important;
    border-top: 1px solid #c3c3c3 !important;
}

#yearlyBudgetExpenseAccountingWindow .k-header.k-grid-toolbar {
    border: 0 none !important;
    border-top: 1px solid #c3c3c3 !important;
}

#YBBudgetEntryImportSection {
    width: auto;
    display: inline-block;
    text-align: right;
    padding-top: 0;
    padding-bottom: 0;
}

.description-items input, .change-items input {
    width: 100% !important;
}

#YBExpenseAccountingGrid tr td {
    white-space: nowrap !important;
}

#YBBudgetEntryImportSection .k-upload .k-upload-button {
    margin-right: 0px !important;
}

#YBudgetEntryExport {
    width: auto;
    display: inline-block;
}


#yearlyBudgetPopupGrid1 tr td {
    border-bottom: 1px solid #c3c3c3 !important;
    padding: .4em .6em;
    vertical-align: middle;
    text-overflow: ellipsis;
}

#YBExpenseAccountingTotalGrid tr td {
    border-bottom: 1px solid #c3c3c3 !important;
    padding: .4em .6em;
    vertical-align: middle;
    text-overflow: ellipsis;
}


#financialPlanBCPopupGrid tbody tr:last-child td {
    border-bottom: 0 none;
}

.titleWindoeDescription span {
    display: block;
    width: 46%;
    white-space: normal;
}

/* Common Expense accounting Popup end*/

/* YearlyBudget starts*/

#yearlyBudgetAllocationGrid tr:first-child td input {
    width: 100%;
    border-radius: 2px;
    border: 1px solid #ccc;
}

#yearlyBudgetPopupGrid_active_cell .k-state-focused {
    width: 80px;
}

#yearlyBudgetButtonsec ul, #yearlyBudgetOriginalButtonsec ul {
    padding-left: 5px;
    margin-bottom: 3px;
}

.yearly-budget-toolbar {
    background: rgb(255, 255, 255);
    height: 50px;
    padding-top: 7px;
    padding-bottom: 10px;
    padding-left: 15px;
    position: relative;
}

.addRecordPopUp {
    height: 30px;
    position: absolute;
    left: 10px;
    top: 8px;
}

.abs-positioning {
    position: absolute;
    bottom: 0;
    right: 0;
}

.overALLButtonSec.nav-tabs > li > a,
.overALLButtonSec.nav-tabs > li > a:hover {
    height: 24px;
    color: #606060;
    border-radius: 0;
    border-color: #367b9a;
    background-color: #fff;
    padding: 2px;
    text-align: center;
}

.overALLButtonSec.nav-tabs > li.active > a,
.overALLButtonSec.nav-tabs > li.active > a:hover {
    background-color: #377b9a;
    color: #fff;
    border-radius: 0;
    border-color: #30697d;
}


#yearbuttonsTabSection li:first-child a {
    border-bottom-left-radius: 4px;
    border-top-left-radius: 4px;
}

/* YearlyBudget end*/

/* Periodic allocation start*/
#automaticAllocation {
    background: #fff;
}

#autoAllocation {
    padding: 0;
    border: 0 none;
}

.periodic-string-background {
    background: #F4F4F4 !important;
}

/* Periodic allocation end*/

.graphs {
    border: 1px solid #ccc;
    height: 286px;
    margin-left: 20px;
    width: 46%;
    background: #fff;
}

/*Financial plan start*/

.autoList {
    list-style: none;
}

    .autoList .valueString {
        margin-left: 10px;
        font-size: 13px;
    }

.header-content {
    border-top: 1px dotted #909090;
    padding-left: 0;
}
/*.wrappers {
    margin-top: 15px;
}*/
.common-list-ctrl {
    width: 45%;
    height: 18px;
}

.common-list-ctrlapt {
    width: 52%;
    margin-left: 12px;
}

.FPBCPopupGridLeft {
    padding-right: 0;
}

    .FPBCPopupGridLeft .k-datepicker {
        width: 52% !important;
        margin-left: 7px;
    }

.FPBCPopupGridright {
    padding-left: 0;
    width: 52%;
}

.FPBCPopupGridMiddle {
    margin-left: 30px;
}

.overALLButtonSec {
    padding: 5px 0px 0px;
}

    .overALLButtonSec li:first-child {
        padding-left: 0;
    }

    .overALLButtonSec li a {
        height: 32px;
        padding: 6px 0px;
        text-align: center;
        font-size: 12px;
        color: #377B9A;
    }

.costRedEmptyPopGrid {
    text-align: center;
    padding: 50px;
}

.styleWrapper {
    width: 98.2%;
}

#financialPlanBCWindow, #BPWindow {
    padding: 5px 15px !important;
}

#planClimateDescription {
    overflow-y: auto !important;
}

#financialPlanBCWindow {
    overflow-y: scroll !important;
}

#FPBCContentWrapper, #budgetPropContentWrapper {
    width: 98.4%;
}

.budget-change-toolbar {
    text-align: left;
    background: #fff;
    height: 30px;
    padding-top: 6px;
}

/*Financial plan end*/

#budgetMemoSec {
    border-top: 1px dotted #ccc;
    overflow: hidden;
}

    #budgetMemoSec #budgetMemoDescription {
        margin-left: 10px; /*width: 51%;*/
        margin-top: 15px;
    }

.budget-lock-text-style {
    top: 2px;
    position: relative;
    left: -10px;
}

.budget-lock-chk-style {
    position: relative;
    float: left;
    margin-left: 6px;
}

.budgetMemoSec label {
    margin-left: 10px;
}

.budgetMemoSec {
    margin-bottom: 5px;
    overflow: hidden;
}

.BMwrapper {
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
    border-top: 0;
}

/*#documentTemplates .archive-Doc-Dropdown{ width: 419px !important;}*/
#documentTemplates {
    padding: 0 !important;
    margin: 0 !important;
}

#docContentWrap {
    padding: 0 !important;
}

/*Kosta main/overview/populationstat/cityranking start*/

#kMainDocumentTemplates #docVersionDropdownnew {
    height: 30px;
    padding: 5px;
}

#searchIndicatorInput {
    height: 39px;
    width: 100%;
    box-shadow: 5px 0px 10px 2px #c3c3c3;
    padding-left: 10px;
}

.indicateLabel {
    margin-right: 25px;
}


#kostraSearchIndicators {
    border-top: none;
    border-radius: 0px 0px 2px 2px;
}

.searchWrapper {
    padding-bottom: 10px;
    padding-left: 10px;
}

#searchIndicator {
    max-height: 288px;
    overflow-y: auto;
}

#docContentWrap .archive-Doc-Dropdown {
    width: 100%;
}

#analysisDocumentSetupWrap .k-datepicker {
    width: 95% !important;
}

.k-datepicker {
    white-space: normal;
}

#kostraMainCostCitizenGrid2 .k-icon.k-i-expand, #kostraMainCostCitizenGrid1 .k-icon.k-i-expand, #kostraMainExpenseGrid .k-icon.k-i-expand, #kostraMainCostCitizenGrid2 .k-icon.k-i-collapse, #kostraMainCostCitizenGrid1 .k-icon.k-i-collapse, #kostraMainExpenseGrid .k-icon.k-i-collapse {
    position: relative;
    top: 0;
    left: 0;
}

.historyContents p:last-child {
    max-height: 240px;
    overflow-y: auto;
}

#analysisCityLevelHistory {
    padding: 10px !important;
}

#populationStatDetailGrid2, #populationStatDetailAreaLineGrid, #populationStatDetailAreaChart, #budgetAssumExpensesGrid {
    min-height: 50px;
}

#populationStatDetailGrid2, #populationStatDetailAreaChart {
    min-height: 50px;
}

#kostraCostReductionPopGrid {
    min-height: 50px;
}

/*Kosta main/overview/populationstat/cityranking end*/

#activeDocProcess {
    margin-top: -1px;
    height: 20px;
    width: 15px;
    vertical-align: top;
}

.k-combobox.k-header {
    background: #fff;
}
/*.k-input{padding:0!important}*/


/*Budget Proposal start*/

#budgPropTopTextFieldSection .k-dropdown-wrap.k-state-default {
    height: 34px !important;
}

/*PowerBI Screen customizations start*/
#serviceunitCtrl .k-dropdown-wrap.k-state-default {
    height: 31px !important;
}
/*PowerBI Screen customizations end*/

#amountFormatDropdown {
    border: 1px solid #6FA1B2;
    border-radius: 4px;
    width: 130px;
    height: 28px;
}

/*Budget Proposal end*/

/*StaffPlanning and Salaryforecast start*/

.column-selector-style {
    text-decoration: underline;
}

.break-width {
    display: inline-block;
    width: 75px;
    margin-top: 0px;
    margin-left: 5px;
    font-weight: bold;
    font-family: semiFont, 'Open Sans Semibold', sans-serif;
}

.filterfont, .filterfont :hover {
    color: #B2B2B2;
    font-weight: normal;
}

#filterRadioWrapper .position-radio-text {
    height: 16px;
    width: 20px;
}

#staffPlanningMonthSel .k-state-default {
    height: 30px !important;
}

#staffGrid a.k-link {
    padding: 0 !important;
}

/*Action Import Css Start*/
.action-import-bg {
    background-image: url('../images/banner_action_import.jpg');
    background-repeat: no-repeat;
    background-size: 100%;
}
/*Action IMport Css End*/

/*BudgetProposal new UI*/
#leftContentWrapper {
    width: 13.5% !important;
    float: left;
    overflow-y: auto;
    overflow-x: hidden;
}

#rightContentWrapper {
    width: 85.7% !important;
    float: left;
    height: 750px;
    overflow-y: auto;
}

#rightContentPopupWrapper {
    float: left;
    width: 87%;
}

#leftContentPopupWrapper {
    float: left;
    width: 13%;
    overflow-x: hidden;
    overflow-y: auto !important;
}

#BPfilterAll {
    padding: 10px !important;
    height: 30px;
    background: #e5ebeb !important;
}

#BPmaxPopUpFooter {
    position: fixed;
    z-index: 1000;
    height: 35px;
    width: 100%;
}

#YBmaxPopUpFooter {
    position: absolute;
    z-index: 1000;
    height: 50px;
    width: 99%;
    padding: 0;
    background-image: none;
    background-position: 50% 50%;
}

.max-popup-footer {
    position: fixed;
    z-index: 1000;
    height: 55px;
    width: 100%;
    background-image: none;
    background-position: 50% 50%;
    left: -1px;
    bottom: 0;
}

.filter-collapse-icon {
    background-image: url('../images/show_filter.svg') !important;
}

.filter-expand-icon {
    background-image: url('../images/hide_filter.svg') !important;
}

.filter-collapse-icon, .filter-expand-icon {
    display: inline-block;
    width: 100%;
    height: 25px;
    background-repeat: no-repeat;
    background-position: 0 !important;
    font-size: 0;
    margin-top: -16px;
}

.button-width {
    width: 75% !important;
    background: #F4F4F4;
    color: #367b9a;
    display: inline-block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-height: 32px;
}

#rightContentWrapper table tr {
    border: 1px solid #606060;
    border-left: 0 none;
    border-right: 0 none;
}

    #rightContentWrapper table tr:first-child {
        border-top: 0 none;
    }

    #rightContentWrapper table tr:last-child {
        border-bottom: 0 none;
    }

#commonFilterWrapperidden .k-widget.k-multiselect, #commonFilterWrapperHidden .k-widget.k-multiselect, #budgetPeriodCommonFilterWrapperHidden .k-widget.k-multiselect {
    border: 0 none;
}

#commonFilterWrapperidden .k-multiselect-wrap.k-floatwrap {
    padding: 0 !important;
    background: #F4F4F4 !important;
    box-shadow: none !important;
    min-height: 0px !important;
}

#commonFilterWrapperHidden .k-multiselect-wrap.k-floatwrap, #budgetPeriodCommonFilterWrapperHidden .k-multiselect-wrap.k-floatwrap {
    padding: 0 !important;
    background: #fff !important;
    box-shadow: none !important;
    min-height: 0px !important;
    border-style: none;
}

#commonFilterWrapperidden .k-multiselect-wrap li, #commonFilterWrapperHidden .k-multiselect-wrap li, #budgetPeriodCommonFilterWrapperHidden .k-multiselect-wrap li {
    padding: 0;
    color: #fff !important;
    background: #516165 !important;
    border-radius: 4px !important;
    border: 0 none !important;
    padding-right: 1.7em !important;
    padding-left: 3px;
}

#budPropGridsMaxWindow {
    padding-top: 0px !important;
    margin-top: -1px;
}

#commonFilterWrapperidden .k-select, #commonFilterWrapperHidden .k-select, #budgetPeriodCommonFilterWrapperHidden .k-select {
    top: 2px !important;
    padding: 0px !important;
}

#commonFilterWrapperidden .k-multiselect-wrap li:first-child, #commonFilterWrapperHidden .k-multiselect-wrap li:first-child, #budgetPeriodCommonFilterWrapperHidden .k-multiselect-wrap li:first-child {
    font-size: 12px !important;
}

#commonFilterWrapperidden .k-list-scroller, #commonFilterWrapperHidden .k-list-scroller, #budgetPeriodCommonFilterWrapperHidden .k-list-scroller {
    height: 0px !important;
    display: none !important
}

#BPapplyFilter {
    padding: 2px 10px;
    background: #367b9a;
    width: 60%;
}

#commonFilterWrapperidden .k-multiselect-wrap input, #commonFilterWrapperHidden .k-multiselect-wrap input, #budgetPeriodCommonFilterWrapperHidden .k-multiselect-wrap input {
    display: none !important;
}

.type-style {
    background: #cbcb8b;
    color: #fff;
    margin-top: 4px;
    display: inline-block;
    width: 100%;
    text-align: center;
    border-radius: 4px;
}

.bp-tag-fin {
    background: #cbcb8b !important;
    color: #fff !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
}

.bp-tag-selected {
    background: #808080 !important;
    color: #fff !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
    margin-right: 5px;
}

.bp-tag-selected-New {
    background: #fff !important;
    color: #000 !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
    pointer-events: none;
    margin-right: 5px;
    border: 2px solid #808080 !important;
}

.bp-focus-selected {
    background: #404040 !important;
    color: #fff !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
    pointer-events: none;
    margin-right: 5px;
}

.bp-tag-delete {
    background: #a83636 !important;
    color: #fff !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
    margin-right: 5px;
}

.bp-green-tag {
    background: green !important;
    color: #fff !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
    margin-right: 5px;
}

.bp-grey-tag {
    background: #808080 !important;
    color: #fff !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
    margin-right: 5px;
}

.bp-white-tag {
    background: #fff !important;
    color: #000000 !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding: 5px;
    text-align: center !important;
    border-radius: 10px !important;
    font-size: 12px !important;
    white-space: nowrap !important;
    border: 1px solid red;
    margin-right: 5px;
}

.bp-red-tag {
    background: #fff !important;
    color: #000000 !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    border: 1px solid saddlebrown;
    margin-right: 5px;
}

.bp-blue-tag {
    background: #fff !important;
    color: #000000 !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
    border: 1px solid #035d2c;
    margin-right: 5px;
}

.bp-blue-AssignTag {
    background: #fff !important;
    color: #000000 !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    border: 1px solid #035d2c;
    margin-right: 5px;
}

.bp-blue-StgAct {
    background: #fff !important;
    color: #000000 !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
    border: 1px solid #516064;
    margin-right: 5px;
}

.su-bp-tag-saddlebrown {
    background: #fff !important;
    color: #000000 !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
    border: 1px solid saddlebrown;
    margin-right: 5px;
}

.bp-plan-tag {
    background: #EEEED2 !important;
    color: #000000 !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 10px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
    margin-right: 5px;
    border: 1px solid;
}

.plan-assignment-tag {
    background: #fff !important;
    color: #000 !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
    pointer-events: none;
    margin-right: 5px;
    border: 2px solid #e4bc60 !important;
}
/* investment tag classes */
.inv-newinvestment {
    background: #6fa1b2 !important;
    color: #fff !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
}

.bp-grey-tag.white-space-normal {
    white-space: normal !important;
}

.bp-red-tag.white-space-normal {
    white-space: normal !important;
}

.bp-blue-tag.white-space-normal {
    white-space: normal !important;
}

.su-bp-tag-saddlebrown.white-space-normal {
    white-space: normal !important;
}

.inv-preapproved {
    background: #f2ac66 !important;
    color: #fff !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
}

.inv-running {
    background: #e4bc60 !important;
    color: #fff !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
}

.inv-evaluation {
    background: #cbcb8b !important;
    color: #fff !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
}

.inv-parked {
    background: #bbcfcf !important;
    color: #fff !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
}

.inv-deleted {
    background: #a83636 !important;
    color: #fff !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
}

.inv-finished {
    background: #bbcfcf !important;
    color: #fff !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
}

.inv-required {
    background: #c57f56 !important;
    color: #fff !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
}

.inv-consequence {
    background: #285e8e !important;
    color: #fff !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
}


/* investment tag classes */


.bp-tag-StgAct {
    background: #516064 !important;
    color: #fff !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
}

.bp-tag-new {
    background: #6fa1b2 !important;
    color: #fff !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
}

.bp-tag-parked {
    background: #b78e3c !important;
    color: #fff !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
}


.inv-active {
    background: #cbcb8b !important;
    color: #fff !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
}

.inv-old {
    background: #a83636 !important;
    color: #fff !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
}

.inv-non-active {
    background: #6fa1b2 !important;
    color: #fff !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
}

.inv-ongoing {
    background: #b27e67 !important;
    color: #fff !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
}

.target-section-divider {
    display: block;
    border: 0;
    border-top: 2px dotted #ccc;
    margin: 0;
    padding: 0;
}

.approver-section-divider {
    display: block;
    border: 0;
    border-top: 2px solid #ccc;
    margin: 0;
    padding: 0;
}

#commonFilterWrapperidden .k-button .k-i-close,
#commonFilterWrapperHidden .k-button .k-i-close,
#budgetPeriodCommonFilterWrapperHidden .k-button .k-i-close {
    vertical-align: top !important;
    margin-top: 0px !important;
    color: #fff !important;
}

#accountFilterMultiDropdown-list,
#serviceUnitFilterMultiDropdown-list,
#deptFilterMultiDropdown-list,
#functionFilterMultiDropdown-list,
#projFilterMultiDropdown-list,
#freeDimFilterMultiDropdown1-list,
#freeDimFilterMultiDropdown2-list,
#freeDimFilterMultiDropdown3-list,
#freeDimFilterMultiDropdown4-list,
#filterTypeDropdownMulti-list,
#filterAlterCodeMultiDropdown-list,
#filterAdjCodeMultiDropdown-list,
#filterBudgetChangeMultiDropdown-list,
#filterTagsMultiDropdown-list {
    display: none !important;
}

#accountFilterMultiDropdown,
#serviceUnitFilterMultiDropdown,
#deptFilterMultiDropdown,
#functionFilterMultiDropdown,
#projFilterMultiDropdown,
#freeDimFilterMultiDropdown1,
#freeDimFilterMultiDropdown2,
#freeDimFilterMultiDropdown3,
#freeDimFilterMultiDropdown4,
#filterTypeDropdownMulti,
#filterAlterCodeMultiDropdown,
#filterAdjCodeMultiDropdown,
#filterBudgetChangeMultiDropdown,
#filterTagsMultiDropdown {
    pointer-events: none !important;
}

#budgetPropMaxButton, #budgetPropBaseMaxButton {
    background: #f4f4f4;
    outline: 0 none;
}

.bp-act-checkbox-lbl-style {
    text-indent: -15px;
    margin-left: 10px;
    display: inline-block;
    padding: 5px;
    padding-left: 15px;
    border-radius: 4px;
}

.black-color {
    color: #000 !important
}

.blue-color {
    color: #377B9A
}

.dark-blue-color {
    color: #1B2B6A;
}

.light-blue-color {
    color: #4A8EB9;
}



/*Service unit start*/
#serviceUnitDetailInfoGrid {
    overflow: auto !important;
}

    #serviceUnitDetailInfoGrid .k-grid-content tr {
        height: 42px !important;
    }

/*service unit end*/

/*  business plan checklist styles start */
.business-plan-category-tooltip {
    background-color: white !important;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: 500px;
    margin: -4px;
    border: 1px solid #6FA1B2;
    border-radius: 4px;
    text-align: justify;
    padding-right: 18px !important;
    padding-left: 18px !important;
}

/*  business plan checklist styles end */


#businessPlanCheckListOpinionGrid .k-grid-content tr {
    height: 42px;
}

.business-type-tag {
    background: #f6921e !important;
    color: #fff !important;
    display: inline-block !important;
    padding-left: 10px !important;
    padding-right: 10px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
    width: 100px;
}

#checkListDetailsGrid .business-type-tag {
    background: #f6921e !important;
    color: #fff !important;
    display: inline-block !important;
    padding-left: 10px !important;
    padding-right: 10px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
    width: auto;
}

#checkListGrid .k-grid-header {
    min-height: 30px !important;
}

#checkListDetailsGrid .k-grid-header {
    min-height: 30px !important;
}

.su-bp-tag {
    color: #fff !important;
    margin-top: 4px !important;
    display: inline-block !important;
    padding-left: 7px !important;
    padding-right: 7px !important;
    text-align: center !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    white-space: normal !important;
}

.su-bp-tag-light {
    background: #f2ac66 !important;
}

.su-bp-tag-medium {
    background: #f6921e !important;
}

.su-bp-tag-dark {
    background: #c57f56 !important;
}

/*Common new action pop up CSS start */

.budchange-toolbar-add, #userDroplistIcon li a:hover, #helpcenterDropdownContainer li a:hover {
    background: none !important;
}

#descTabSection.nav-tabs > li.active > a {
    background-color: #377B9A;
    color: #ffffff;
    border-radius: 5px;
    height: 30px;
    text-align: center;
    padding: 0px;
}

#descTabSection.nav-tabs > li > a {
    height: 30px;
    color: #377B9A;
    border-radius: 5px;
    background-color: #fff;
    text-align: center;
    padding: 0px;
    border-color: #6FA1B2 !important;
}

.desc-detail-section > .desc-section-tablink .active > a.selected {
    background-color: #377B9A;
    color: #ffffff;
    border-color: #30697D;
}

.desc-section-tablink {
    min-width: 180px;
    max-width: 180px;
    padding: 4px;
}

#btnDescSection {
    height: 15px !important;
    padding-top: 11px !important;
}

.desc-section-tab-text {
    display: table-cell !important;
    width: 175px;
    vertical-align: middle;
}

.desc-section-chk-style {
    position: relative;
    float: left;
    margin-left: 0px;
    margin-top: 7px;
}

#actionPopupPriorityContainer .k-dropdown .k-dropdown-wrap, #budgetProposalExpenseContainer .k-dropdown .k-dropdown-wrap {
    height: 35px !important;
}

#bpKeyFiguresIndicatorContainer .k-dropdown .k-dropdown-wrap, #budgetProposalExpenseContainer .k-dropdown .k-dropdown-wrap, #BPKeyFrequencyContainer .k-dropdown-wrap {
    height: 35px !important;
}

#bpKeyFiguresIndicatorDrpdwn-list, #bpActivityIndicatorDrpdwn-list {
    width: 75% !important;
}

#bpKeyFiguresValueTypeContainer .k-dropdown .k-dropdown-wrap {
    height: 35px !important;
}

#bpKeyFiguresAccessibilityContainer .k-dropdown .k-dropdown-wrap {
    height: 35px !important;
}
/* css for action pop up placeholder start */
.input-placeholder {
    position: relative;
}

    .input-placeholder input:focus + .placeholder {
        display: none;
    }

    .input-placeholder input:valid + .placeholder {
        display: none;
    }

.placeholder {
    position: absolute;
    pointer-events: none;
    top: 0;
    bottom: 0;
    height: 16px;
    left: 10px;
    margin: auto;
    color: #000000;
}

    .placeholder span {
        color: red;
    }


.placeholder-wrap {
    display: inline-block;
    position: relative;
    background: #FFF;
}

    .placeholder-wrap .placeholder {
        position: absolute;
        pointer-events: none;
        top: 0;
        bottom: 0;
        height: 16px;
        font-size: 15px;
        left: 10px;
        margin: auto;
        color: #000000;
    }

    .placeholder-wrap input {
        position: relative;
        z-index: 10;
    }

        .placeholder-wrap input:focus + .placeholder {
            display: none;
            color: transparent;
        }


.btn-action, .btn-action:hover, .btn-action:focus {
    background-color: #fff !important;
    border-color: #6FA1B2 !important;
    color: #377B9A !important;
    font-size: 12px;
}

.btn-action-primary, .btn-action-primary:hover, .btn-action-primary:focus {
    background-color: #fff !important;
    border-color: #6FA1B2 !important;
    color: #377B9A !important;
    font-size: 12px;
}

.action-editor-img {
    margin-bottom: 6px;
    position: relative;
    margin-right: 10px;
    float: right;
}

.action-section-divider {
    display: block;
    border: 0;
    border-top: 1px solid #ccc;
    margin: 0;
    padding: 0;
}

.btn-separator:after {
    content: ' ';
    display: block;
    background: #ADADAD;
    margin: 10px 11px;
    height: 34px;
    width: 1px;
    float: right;
}

#actionBCPopupGrid, #actionBCPopupGrid1, #actionPopupTotalGrid {
    border: 0 none;
    color: #000000 !important;
    box-shadow: none !important;
    background-color: #fff !important;
}

    #actionBCPopupGrid th, #actionBCPopupGrid1 th {
        color: #000000 !important;
    }

#actionBCPopupGrid1Total {
    color: #000000 !important;
    box-shadow: none !important;
    background-color: #fff !important;
}

#actionBCPopupGrid tbody tr:last-child td {
    border-bottom: 0 none;
}

#actionPopupPriorityContainer .k-dropdown, #budgetProposalExpenseContainer .k-dropdown {
    width: 98%;
}

.bp-priority-mandatory-style {
    float: left;
    margin-right: 4px;
    margin-left: -10px;
    font-size: 14px;
    color: red;
}

.btn-action-active-style {
    background-color: #377B9A !important;
    color: #ffffff !important;
    font-size: 12px !important;
}

#alterAdjContainerSection .k-combobox {
    width: 92% !important;
    border-bottom: 1px solid #6FA1B2 !important;
    min-width: 92% !important;
}

#actionTagSection .k-widget.k-multiselect.k-header {
    border-color: #c6c6c6 !important;
}

#actionTagSection .k-multiselect-wrap li {
    padding: 0;
    color: #fff !important;
    background: #808080 !important;
    border-radius: 10px !important;
    border: 0 none !important;
    padding-right: 1.7em !important;
    padding-left: 5px;
    font-size: 12px;
    margin-top: 3px
}

#actionTagSection .k-icon.k-i-close {
    color: #ffffff !important;
}

#actionTagInvSection .k-widget.k-multiselect.k-header {
    border-color: #c6c6c6 !important;
}

#actionTagInvSection .k-multiselect-wrap li {
    padding: 0;
    color: #fff !important;
    background: #808080 !important;
    border-radius: 10px !important;
    border: 0 none !important;
    padding-right: 1.7em !important;
    padding-left: 5px;
    font-size: 12px;
    margin-top: 3px
}

#actionTagInvSection .k-icon.k-i-close {
    color: #ffffff !important;
}

/* css for action pop up placeholder start */

/*Common new action pop up CSS end */

/*css for new ckeditor start*/

.item-active-grey {
    background: #f4f4f4 !important;
}

/*css for new ckeditor end*/
.zIndexEnforce {
    z-index: 0 !important;
}

/* New menu styles start*/

#container {
    position: relative;
}

#menuHeaderWrapper {
    height: 55px;
}

#topMenuItems {
    padding: 0px;
    margin: 0px;
}

.tabs {
    position: relative;
    overflow: hidden;
    margin: 0 auto;
    font-weight: 200;
    font-size: 15px;
    margin-left: 0px;
}

    .tabs .main-menu-tab-text {
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 2.5;
        padding: 5px 11px;
        width: 100%;
    }

    .tabs nav .main-menu-tab-text span {
        vertical-align: middle;
    }


.tab-current {
    z-index: 100;
    background-color: #367B9A !important;
    color: #fff !important;
}

.mainmenu-list-items {
    position: relative;
    z-index: 1;
    display: inline-block;
    margin: 0;
    text-align: center;
}

    .mainmenu-list-items .main-menu-tab-text {
        height: 55px;
    }

    .mainmenu-list-items .tab-current::after {
        position: absolute;
        top: 92%;
        left: 50%;
        margin-left: -10px;
        width: 0;
        height: 0;
        border: solid transparent;
        border-width: 10px;
        border-top-color: #367B9A;
        content: '';
        pointer-events: none;
        z-index: 9999;
    }

.menuContentWrap {
    position: fixed;
    top: 55px;
    z-index: 1;
    left: 0;
    right: 0;
    padding-right: 0px !important;
    margin-right: 0px !important;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    border-bottom: 1px solid #c3c3c3;
    border-top: 1px solid #c3c3c3;
    border-radius: 4px;
    -webkit-box-shadow: 0 6px 12px rgba(0,0,0,.175);
    box-shadow: 0 6px 12px rgba(0,0,0,.175);
    padding-bottom: 15px;
}

.header-img-text-styling {
    color: #367B9A;
    text-align: left;
    text-transform: uppercase;
    padding-bottom: 10px !important;
    padding-left: 10px !important;
    font-size: 14px;
    white-space: nowrap;
}

.header-img-desc-styling {
    height: 75px;
    padding-left: 10px !important;
    padding-top: 10px !important;
    text-align: left;
    font-size: 13px;
    width: 206px;
    white-space: normal !important;
}

.tenent-name-bg-style {
    border: 1px solid #367B9A;
    background: #367B9A;
    color: #fff;
    border-radius: 4px;
}

.tenent-name-bg-style-new {
    border: 1px solid #1B2B6A;
    background: #1B2B6A;
    color: #fff;
    border-radius: 20px 20px 0px 0px;
    padding: 12px 20px;
    height: 57px;
}

#headerMenuDropDown:before {
    bottom: 100%;
    left: 90%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-color: rgba(238,238,238,0);
    border-width: 10px;
    margin-left: -8px;
    border-bottom-color: #367B9A !important;
    z-index: 99;
}

#headerProfileDropDown:before {
    bottom: 100%;
    left: 80%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-color: rgba(238,238,238,0);
    border-width: 10px;
    margin-left: -8px;
    border-bottom-color: #1B2B6A !important;
    z-index: 99;
}

#welcomeText {
    display: none;
}

#headerMenuDropDown {
    width: 230px !important;
    left: -185px !important;
    top: 45px !important;
}

#headerProfileDropDown {
    padding: 0px !important;
    width: 430px !important;
    left: -372px !important;
}

#headerMenuOrgDropDown:before {
    bottom: 100%;
    left: 80%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-width: 10px;
    border-bottom-color: #1B2B6A;
    z-index: 99;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

#header_notification_bar .dropdown-menu.extended {
    left: -256px !important;
}

#notificationCount {
    padding: 0px 5px;
    line-height: normal;
    position: absolute;
    top: -10px;
    left: 25px;
}

ul.dropdown-menu.extended.noti-arrow.notification.old-notification::before {
    bottom: 100%;
    left: 92%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-color: rgba(238,238,238,0);
    border-width: 10px;
    margin-left: -10px;
    border-bottom-color: #32323A !important;
    z-index: 99;
}

.org-menu-align {
    width: 100% !important;
    margin-left: 5px !important;
    padding-top: 5px;
    padding-bottom: 5px;
}

.org-submenu-style {
    border-left: none;
    border-right: none;
    color: #000;
}

.border-bottom-dark {
    border-bottom: 1px #b3b3b3 solid !important;
}

.border-bottom-black {
    border-bottom: 1px #000000 solid !important;
}

.dash-board-list-style {
    height: auto;
    display: inline-block;
    border-bottom: 1px solid #b3b3b3;
    padding-left: 10px;
    cursor: pointer;
    clear: both;
    width: 100%;
    overflow: hidden;
}

.active-list {
    background: #c3c3c3;
    opacity: 0.8;
}

#menuDropUserMenu {
    margin-right: 0px !important;
    padding-left: 0px !important;
}

#headerSearchRightBar.open-right-bar {
    right: 6.3% !important;
    height: auto !important;
    top: 70px !important;
    display: block;
    z-index: 1004;
    width: 40%;
}

.recently-updated-news {
    border: 1px solid lightgreen;
    height: 15px;
    display: inline-block;
    width: 15px;
    background: lightgreen;
    border-radius: 20px;
}

#newHeaderMessageBar .dropdown-menu.extended li p {
    padding: 0px;
}

.not-recently-updated-news {
    border: 1px solid white;
    height: 15px;
    display: inline-block;
    width: 15px;
    background: white;
    border-radius: 20px;
}

#menuNewsCount {
    padding: 0px 5px !important;
    line-height: normal !important;
    position: absolute !important;
    top: 7px !important;
    left: 30px !important;
}

#monthlyReportSpecificMesg #menuNewsCount {
    left: 37px !important;
}

#headerSearchRightBar .search-row {
    padding: 0px !important;
    margin: 0px !important;
}

.monthly-report-only#headerSearchRightBar.open-right-bar {
    right: 12.7% !important;
}

#newNotificationCount {
    padding: 2px 3px 0px 3px;
    position: absolute;
    height: 14px;
    min-width: 14px;
    /* left: 25px; */
    align-content: center;
    top: 13px;
    font-size: 80%;
}

#newMessageCount {
    padding: 2px 3px 0px 3px;
    position: absolute;
    height: 14px;
    min-width: 14px;
    right: 6px;
    align-content: center;
    top: 12px;
    font-size: 80%;
}


#newDropdownHeader {
    background-color: #1B2B6A;
    height: 57px;
    border-top-left-radius: inherit;
    border-top-right-radius: inherit;
    width: 450px !important;
}

#messageDropdownHeader {
    background-color: #1B2B6A;
    height: 57px;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    width: 450px !important;
}

#newDropdownHeader .noti-header {
    align-self: center;
}

#newDropdownHeader span {
    font-size: 16px;
    color: white;
}

#messageDropdownHeader span {
    font-size: 16px;
    color: white;
}

.user-image-style {
    height: 24px;
    width: 24px;
}

.img-bg-round-border {
    border-radius: 50%;
    border: 1px solid #f4f4f4;
}

#headerSearchRightBar {
    background: none !important;
}

.selected-news-section {
    border: 1px solid #377B9A !important;
}

#headerSearchRightBar:before {
    bottom: 100%;
    left: 93%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-color: rgba(238,238,238,0);
    border-width: 15px;
    margin-left: -12px;
    border-bottom-color: #377B9A !important;
    z-index: 9999;
}

.selected-news-section:before {
    bottom: 100%;
    left: 100%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-color: rgba(238,238,238,0);
    border-width: 15px;
    border-left-color: #377B9A !important;
    z-index: 9999;
    top: 40%;
}

#mainHeader ul.dropdown-menu.extended.noti-arrow.notification::before {
    bottom: 100%;
    right: 50.5%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-color: rgba(238,238,238,0);
    border-width: 10px;
    margin-left: -260px;
    border-bottom-color: #1B2B6A !important;
    z-index: 99;
}

#mainHeader ul.dropdown-menu.extended.msg-arrow.notification::before {
    right: 36%;
}

#leftHeaderMenuSec .dropdown-menu.extended {
    max-width: 320px !important;
    min-width: 160px !important;
    width: 300px !important;
    padding: 0 10px;
    box-shadow: 0 0px 5px rgba(0,0,0,0.1) !important;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    background: #fff !important;
    border: none;
    top: 42px !important;
}


#newHeaderNotificationBar .dropdown-menu.extended {
    left: unset;
    max-height: 615px;
    width: 450px !important;
    right: -213px;
    min-height: 200px;
}

#userDroplistIcon .dropdown-menu.extended,
#helpcenterDropdownContainer .dropdown-menu.extended {
    right: -19px !important;
    left: unset !important;
    top: 58px;
    position: absolute;
}

#userDroplistIcon, #helpcenterDropdownContainer {
    margin: 0px !important;
    padding: 0px !important;
}

input.k-checkbox {
    -webkit-appearance: checkbox;
    -moz-appearance: checkbox;
}


.glyphicon.glyphicon-plus {
    font-family: regularFont, 'Open Sans Regular', sans-serif !important;
    font-size: 22px !important;
    font-weight: lighter;
    -webkit-font-smoothing: antialiased !important;
}

.glyphicon-remove:before {
    content: "\2b";
}

.glyphicon.glyphicon-remove {
    font-family: regularFont, 'Open Sans Regular', sans-serif !important;
    font-size: 22px !important;
    font-weight: lighter;
    -webkit-font-smoothing: antialiased !important;
    width: 13px;
    top: 3px !important;
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    padding-right: 15px !important;
}

#mainMenuOrgLevelSection .caret {
    border-top: 6px solid !important;
    border-right: 5px solid !important;
    border-left: 6px solid !important;
    border-top-color: #404040 !important;
    border-bottom-color: #404040 !important;
}
/* New menu styles end*/

#kostraReportingSections .kostra-reporting-tablink {
    min-width: 0px;
    margin: 0;
    width: 20%;
    border: 1px solid #367b9a;
    background-color: #fff;
    padding: 0px 5px;
    margin-top: 2px;
    border-left: 0;
    text-align: center;
}

#kostraReportingSections {
    width: 100%;
    float: left;
    min-height: 140px;
}

    #kostraReportingSections li a {
        height: 23px;
        color: #000;
        border-radius: 0;
        border: 0 none;
        padding: 0px;
        background: none;
        white-space: nowrap;
        padding-top: 3px;
        font-size: 11px;
    }

.staff-tab-text {
    display: inline-block;
    width: 100%;
    margin: 0;
    overflow: hidden;
    vertical-align: middle;
    color: #000;
}

.muli-select-filter {
    right: 14%;
    top: 5px;
}

.filter-border-line {
    position: absolute;
    border-right: 1px solid #fff;
    height: 30px;
    right: 21%;
    top: 6px;
}

.border-white-top1 {
    border-top: 1px solid #fff;
}

.border-white-bottom1 {
    border-bottom: 1px solid #fff;
}

.exp-collapse {
    margin-right: 115px;
    cursor: pointer;
}

.dark-blue-bgcolor {
    background: #367b9a;
}

.cmn-blue-color {
    color: #367b9a !important;
}

.project-superUser-bgcolor {
    background: white;
    margin-top: 15px;
    height: 33px;
    border-radius: 5px !important;
}

#kostraReportingSections li:hover {
    background: #367b9a;
    border: 1px solid #fff !important;
    color: #fff !important;
    border-radius: 5px !important;
}

    #kostraReportingSections li:hover a {
        color: #fff !important;
    }

.checkbox-disp-neg-wrap {
    left: 9%;
}

.checkbox-costred-wrap {
    left: 5%;
}

.kostra-tabs-seprate {
    width: 18%;
}

.checkbox-costred-wrap span, .checkbox-disp-neg-wrap span {
    white-space: nowrap;
}

.reporting-area-wrapper {
    min-height: 175px;
}

.ws-reporting-area-wrapper {
    min-height: 120px;
}

.collapsed-sec {
    margin-left: 20px;
    border: 1px solid #347B9A !important;
    margin-top: -1px !important;
}

.banner-right-buttons {
    width: 38%;
}

.kostra-reporting-tabs-style {
    border-bottom: 0 none;
}


#maxToMinClose,
#indicatorMaxButton,
#ybMaxMinText {
    display: inline-block !important;
    background: transparent !important
}

    #maxToMinClose:hover,
    #maxToMinActClose:hover,
    #ybMaxMinText:hover {
        border: 0 none !important;
        background: transparent !important;
    }

.btn-seprator {
    border-left: 1px solid #428bca;
    height: 30px;
    display: inline-block;
    position: relative;
    top: 12px;
    width: 0;
}

    .btn-seprator:hover {
        border-left: 1px solid #428bca !important;
    }

#dashBoardWrapper .box-header-btns {
    top: 8px;
    right: 10px;
}

#dashBoardWrapper .box-header h3 {
    font-size: 15px;
    font-weight: bold;
    color: #000;
}

#dashBoardWrapper .box {
    border-radius: 4px;
}

#dashBoardWrapper .box-header {
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;
}

#yearlyBudgetAllocationGrid .k-pager-wrap.k-grid-pager {
    display: none;
}

#ordersGrid tr td, #freeDimGrantGridParent tr td {
    vertical-align: middle !important;
}

#freeDimGrantGridParent tr td {
    vertical-align: top !important;
}

.common-tabs-style li, #BPDocExportSectionList li,
#MRDocumentSectionList li {
    margin: 0;
}

#politicsTabs1 li a, #politicsTabs2 li a {
    border-top: 1px solid #ddd !important;
    border-left: 1px solid #ddd !important;
    margin-left: -3px;
    background: #ebebeb;
    color: #000;
    font-weight: bold;
}

#politicsTabs1 li:first-child a, #politicsTabs2 li:first-child a {
    border-left: 1px solid #ddd !important;
    margin-left: 15px;
    border-radius: 4px !important;
}

#politicsTabs1 li:last-child a, #politicsTabs2 li:last-child a {
    border-right: 1px solid #ddd !important;
}

#investmentsGrid2A .k-grid-header-locked, #investmentsGrid2B .k-grid-header-locked, #operationalBudgetGrid1A .k-grid-header-locked, #operationalBudgetGrid1ATotal .k-grid-header-locked, #operationalBudgetGrid1B .k-grid-header-locked {
    margin-left: -1px;
}

.common-tabs-style li.active,
#BPDocExportSectionList li.active,
#MRDocumentSectionList li.active {
    margin-bottom: -1px;
}

    .common-tabs-style li.active a,
    #BPDocExportSectionList li.active a,
    #MRDocumentSectionList li.active a {
        background: #fff;
    }

.exp-collapse a {
    padding: 4px 10px !important;
}

#BPDocExportSection .nav-tabs > li.active > a,
#BPDocExportSection .nav-tabs > li.active > a:focus,
#BPDocExportSection .nav-tabs > li.active > a:hover {
    color: rgb(74, 142, 185) !important;
    cursor: default !important;
    background-color: #fff !important;
    border: 1px solid #ddd !important;
    border-bottom-color: #fff !important;
    padding: 11px 15px !important;
    height: 100% !important;
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;
}

.common-tabs-style.nav-tabs > li.active > a,
.common-tabs-style.nav-tabs > li.active > a:focus,
.common-tabs-style.nav-tabs > li.active > a:hover,
#MRDocumentSectionList li.active > a,
#MRDocumentSectionList li.active > a:focus,
#MRDocumentSectionList li.active > a:hover {
    color: #000 !important;
    cursor: default !important;
    background-color: #fff !important;
    border: 1px solid #ddd !important;
    border-bottom-color: #fff !important;
    padding: 11px 15px !important;
    height: 100% !important;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}

#businessPlanControllerId .bp-green-tag {
    margin-right: 6px;
    padding: 4px 6px;
}

#collapseMatrixSelector button {
    margin-right: 10px !important;
}

.dropdown-texts {
    position: relative;
    top: 9px;
    display: inline-block;
    float: left;
    width: 25%;
}

.matrix-popup-header {
    margin-top: 0px !important;
    padding-top: 0px !important;
    padding-left: 0px !important;
    padding-right: 0px !important;
    width: 99.9% !important;
    border: 0 !important;
}

#matrixTextEditorWindow {
    width: 100%;
    padding: 0 !important;
}

.carousel-inner {
    margin-left: -3px;
}

#MREditorCarousel,
#MREditor1Carousel, #MNTargetTextEditorScope, .MR-dynamic-carousal {
    clear: both;
    position: relative;
    width: 54%;
    left: 3%;
}

#MNTargetTextEditorScope, .MR-dynamic-carousal {
    left: 2%;
}

#MRMatrixSectionList #departmentDropDown {
    width: 78%;
    display: inline-block;
    float: left;
}

.editor-comment-right-section {
    /*padding-right: 40px !important;
    float: right;*/
    width: 50%;
    padding-top: 20px !important;
    overflow-y: auto;
}

.mr-editor-comment-right-section {
    /*margin-right: 40px !important;
    float: right;*/
    width: 48.5%;
    padding-top: 8px !important;
    overflow-y: auto;
}

#matixSelectorFilter {
    /* width: 4%;
    position: absolute;
    right: 20px;
    top: 23px;*/
}

#CKEditorWebTableWindow .k-grid-footer {
    border: 0 none !important;
}

#DBMytasksList li {
    background: #e3e3e3 !important;
    border-bottom: 1px solid #c3c3c3 !important;
    margin-bottom: -2px;
    border: 1px solid #b3b3b3;
    margin-right: 2px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

    #DBMytasksList li a {
        color: #000 !important;
    }

        #DBMytasksList li a:hover,
        #DBMytasksList li a:focus {
            border: 0 !important;
            border-bottom: 0 !important;
            border-top-right-radius: 4px;
            border-top-left-radius: 4px;
            background: #d3d3d3 !important;
        }

    #DBMytasksList li.active {
        background: #f4f4f4 !important;
        border-bottom: 1px solid #f4f4f4 !important;
    }

        #DBMytasksList li.active a {
            background: #f4f4f4 !important;
            border: 0 !important;
            border-bottom: 1px solid #f4f4f4 !important;
            border-top-right-radius: 4px;
            border-top-left-radius: 4px;
        }

.tasks-heading {
    color: #FC8D75;
}

#myOnGoingTasks, #myDueTasks {
    width: 94.5%;
}

/*.status-dropdown-list:before {
	content: "";
}*/
.status-dropdown-list .k-select:before {
    content: "\e114";
    font-size: 12px !important;
    color: #347B9A !important;
    position: relative;
    top: 1px;
    display: inline-block;
    font-family: 'Glyphicons Halflings', sans-serif;
    font-style: normal;
    font-weight: normal;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.status-dropdown-list .k-select {
    background: #fff !important;
}

    .status-dropdown-list .k-select .k-icon {
        display: none;
    }

#DBTasksSection .task-text-appender p {
    display: inline;
}

#orgVersionManageContainer .k-dropdown.k-header {
    width: 40% !important;
}

#orgVersionUserContainer .k-dropdown {
    width: 40% !important;
}

.status-dropdown-list .k-dropdown-wrap.k-state-default {
    border: 0 !important;
    width: 100%;
    margin-left: -2px;
}

    .status-dropdown-list .k-dropdown-wrap.k-state-default:hover {
        background: none !important;
    }

.action-popup-bp-dyna-height {
    overflow-x: hidden;
    overflow-y: auto;
}


.expcollapse-header i.fa:before {
    content: "\f107";
}

.expcollapse-header.collapsed i.fa:before {
    content: "\f106";
}

#BPGoalsGrid .year, .resultyear, .resultprvyr, .achievedresults {
    width: 50%;
}

.monthly-rpt-items {
    display: inline-block;
    width: auto;
}

.remove-border-bottom {
    border-bottom: 0 !important;
}

.db-target-indicator {
    display: inline-block;
    font-size: 1.2em;
    text-overflow: ellipsis;
    width: 100%;
    height: 25px;
    white-space: nowrap;
    overflow: hidden;
}

.target-db-value-wrap {
    border-bottom: 1.5px solid #909090;
    display: inline-block;
    height: 50px;
}

.target-db-value {
    font-size: 3.5em;
    vertical-align: bottom;
    line-height: 1;
    display: flex;
    width: auto;
    float: left;
}

.target-db-bottom-txt {
    display: inline-block;
    font-size: 1.5em;
    width: 100%;
    line-height: 1;
}

.gridster-item.maximize-wrapper .db-target-indicator {
    margin-top: 50px;
    font-size: 2em;
    height: 100%;
    text-overflow: unset;
}

.gridster-item.maximize-wrapper .target-db-value-wrap {
    height: 100%;
    line-height: 3;
}

.gridster-item.maximize-wrapper .target-db-value {
    font-size: 4.5em;
}

.gridster-item.maximize-wrapper .target-db-bottom-txt {
    font-size: 2em;
    line-height: 2;
}

#yearlyBudgetPopupGrid .k-grid-content,
#YBExpenseAccountingGrid .k-grid-content {
    max-height: 500px;
}

.no-grid-data-style {
    border: 1px solid #c3c3c3;
    width: auto;
    border-left: 0;
    border-right: 0;
    text-align: center;
    font-size: 16px;
}

.acc-grid-header-white {
    padding: 5px 0px 5px 10px;
    box-shadow: 1px 2px 10px 0px rgba(111, 161, 178, 0.69);
}

.small-tabs-a {
    padding: 5px !important;
    height: 30px !important;
}

#sustainableGoalsAnalysisTreeGrid .k-icon.k-i-none {
    display: none;
}

.sustain-image-wrap {
    width: 22px;
    display: inline-block;
}

#finPlanSetupGrid .k-numeric-wrap.k-state-default {
    border: none !important;
}

.selected-image-box {
    border: 1.5px solid #b3b3b3;
    border-radius: 6px;
    padding: 4% 13%;
    height: 100%;
    width: auto;
    display: inline-block;
}

.highlighted-list {
    background: #c3c3c3 !important;
}

.left-contents:hover {
    background: #f4f4f4;
}

.highlighted-list a {
    color: #fff;
}

.highlighted-list:hover {
    background: #377B9A;
}

.image-over-text {
    display: inline-block;
    position: absolute;
    top: 0;
    left: 0;
    border: 1px solid #fff;
    background: #2fac66;
    border-radius: 10px;
    width: 85px;
    color: #fff;
    text-align: center;
    font-size: 17px;
    padding: 1px 10px;
}

.display-icons img {
    width: 25px;
    height: 25px;
}

.upld-right-bottom-sec #tabSectionSmall li a {
    height: 20px !important;
    padding: 5px 9px !important;
}

.fixed-header {
    top: 0;
    position: fixed;
    width: auto;
    z-index: 1;
}

#assignmentTasksGrid .k-grid-header th a {
    position: relative;
}

#assignmentTasksGrid .k-grid-header th .k-icon.k-i-sort-asc-sm,
#assignmentTasksGrid .k-grid-header th .k-icon.k-i-sort-desc-sm {
    position: absolute;
    right: 40px;
    top: 20px;
}

.bp_task-starts {
    border-top: none;
    border-bottom: none;
    background-color: #EAF8FB;
}
/*Media quries*/
@media only screen and (max-width: 1920px) {
    #collapseKostraStatusReport {
        padding-top: 23px !important;
    }
}

@media only screen and (max-width: 1680px) {
    #indicatorSearch {
        padding: 6px 27%;
        border-radius: 0px;
    }

    #monthlyReportSpecificMesg #menuNewsCount {
        left: 42px !important;
    }
}

@media only screen and (max-width: 1500px) {
    .kostra-tabs-seprate {
        width: 21%;
    }

    .chapter-name {
        width: 200px !important;
    }

    .checkbox-disp-neg-wrap {
        left: 6%;
    }

    .checkbox-costred-wrap {
        left: 3%;
    }

    #bannerContentWrapper {
        position: relative;
    }

    #monthlyReportingSaveBtn,
    #monthlyReportingInprogressBtn {
        position: absolute;
        right: 10px;
        width: 245px !important;
    }

    #monthlyReportingInprogressBtn {
        top: 50px !important;
    }

    #monthlyReportSpecificMesg #menuNewsCount {
        left: 39px !important;
    }
}

@media only screen and (min-width: 1000px) and (max-width: 1040px) {
    .logo {
        zoom: 94%;
    }

    .right-mult-wraper {
        width: 90.5% !important;
    }

    .reporting-area-wrapper .user-single-desc, #bannerContentWrapper .banner-wrapper-left {
        zoom: 78% !important;
    }

    .kostra-tabs-seprate {
        width: 24% !important;
    }

    .checkbox-disp-neg-wrap {
        left: -6% !important;
    }

    .checkbox-costred-wrap {
        left: -4% !important;
    }

    #menuHeaderWrapper {
        zoom: 88% !important;
    }

    .muli-select-filter, .banner-right-buttons {
        zoom: 73% !important;
    }

    #kostraCostReductionMovementList {
        margin-left: -25px;
    }

    .menuContentWrap {
        zoom: 94%;
    }

    #kostraOverviewFunctionWrapper {
        margin-left: -30px !important;
    }

    #docExportWrapper {
        margin-left: 10px;
        width: 35%;
    }

    #financialStatusAccountDetailWindow {
        zoom: .94;
    }
}

@media only screen and (max-width: 1200px) {
    #financialStatusAccountDetailWindow, #kostraDetaiIncDocument {
        zoom: 78%;
    }

    #tabSectionSmall {
        zoom: 80%;
    }

    .kostra-tabs-seprate {
        width: 23%;
    }

    .checkbox-disp-neg-wrap {
        left: -1%;
    }

    .checkbox-costred-wrap {
        left: -1%;
    }

    #kostraCostReductionMovementList {
        margin-left: -25px;
    }

    .right-mult-wraper {
        width: 94.5%;
        zoom: 90%;
    }

    .banner-wrapper-left .kostra-analysis-dynamic-heading, .user-single-desc, .banner-wrapper-left {
        zoom: 88%;
    }

    #menuHeaderWrapper {
        zoom: 90%;
    }

    .banner-right-buttons {
        width: 45%;
        zoom: 88%;
    }

    .muli-select-filter {
        right: 9%;
        zoom: 88%;
    }

    #indicatorSearch {
        padding: 6px 17%;
        border-radius: 0px;
    }

    #topDocumentDropdownnew {
        margin-left: 15px;
    }
}


@media only screen and (min-width: 1201px) and (max-width: 1299px) {
    #kostraDetaiIncDocument {
        zoom: 93%;
    }

    .right-mult-wraper {
        width: 94.5%;
    }

    #bannerContentWrapper .banner-wrapper-left, #tabSectionSmall {
        zoom: 95%;
    }

    .muli-select-filter {
        right: 8%;
    }

    .banner-right-buttons {
        width: 46%;
    }

    .kostra-tabs-seprate {
        width: 23%;
    }

    .checkbox-disp-neg-wrap {
        left: 3%;
    }

    .checkbox-costred-wrap {
        left: 1%;
    }

    #topDocumentDropdownnew {
        margin-left: 15px;
    }
}

@media only screen and (min-width: 1300px) and (max-width: 1399px) {

    .include-save-wrapper {
        float: right !important;
        margin-right: 8px;
        margin-top: -5px;
    }

    .muli-select-filter {
        right: 12%;
    }

    .banner-right-buttons {
        width: 41%;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1450px) {
    .include-save-wrapper {
        float: right !important;
        margin-right: 8px;
        margin-top: -23px;
    }
}

@media only screen and (min-width: 1400px) {
    .banner-right-buttons {
        width: 40%;
    }

    .muli-select-filter {
        right: 12%;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1410px) {
    .arrowtab-navbar-content {
        min-width: 9%;
    }
}
/*Kostra doc production*/
#tabSections li a, #alertTabSections li a {
    color: #000;
    border: 0;
    background-color: #fff;
}

#tabSections li.active a, #alertTabSections li.active a {
    background-color: #367b9a;
    color: #fff;
}

#tabSections li:last-child a, #alertTabSections li:last-child a {
    border-radius: 4px;
    border-bottom-left-radius: 0px;
    border-top-left-radius: 0px;
}

#tabSections li:first-child a, #alertTabSections li:first-child a {
    border-radius: 4px;
    border-bottom-right-radius: 0px;
    border-top-right-radius: 0px;
}

.multi-select-modify-wrapper .k-dropdown-wrap.k-state-default {
    height: 24px !important;
    width: 120px;
}

.multi-select-modify-wrapper .k-dropdown-wrap .k-input {
    padding-top: 0 !important;
}

/*#forgotPassordModalWindow {
    overflow: hidden !important;
}*/
/* Kostra new design end*/

/* Monthly Report - Forecast Overview Comment */
#MRForecastOverviewTags .bp-tag-selected {
    padding: 5px 7px !important;
}

#MRForecastOverviewCommentUpdate {
    position: absolute;
    bottom: 10px;
    right: 5px;
}

.position-top-style {
    top: 2080px !important;
}

.calendar-control .k-datepicker {
    width: 90% !important;
}

.report-cal-width1 {
    width: 43.5%;
    height: 42px;
}

.report-cal-width2 {
    width: 24.5%;
}

.report-cal-width3 {
    width: 32%;
}

#reportCalendarGridSave {
    padding: 6px 40px !important;
}

#reportCalendarReporting,
#investmentReporting {
    padding: 6px 15px !important;
}

.yearly-budget-entry-periodisering {
    background: url('../images/periodising.png') !important;
    background-repeat: no-repeat !important;
    min-width: 24px !important;
    height: 24px !important;
    display: inline-block !important;
    cursor: pointer !important;
}

#collapseContentDetails .k-numeric-wrap,
#nestedMain .k-numeric-wrap {
    border: 1px solid #6fa1b2 !important
}

#collapseContentDetails .k-multiselect-wrap input,
#nestedMain .k-multiselect-wrap input {
    width: auto !important;
    opacity: 1;
}

#collapseContentDetails .k-multiselect-wrap.k-floatwrap {
    border-color: #6FA1B2 !important;
}

#kostraDetailTwinSelectSection .k-multiselect-wrap input {
    width: 50% !important;
}

#sustainGoalsWrap li:first-child a {
    border-top-left-radius: 4px !important;
    border-bottom-left-radius: 4px !important;
}

#sustainGoalsWrap li:last-child a {
    border-top-right-radius: 4px !important;
    border-bottom-right-radius: 4px !important;
}
/*media query based css added for org menu start */
@media (max-width: 1000px) {

    .ps-dynawidth-grid {
        width: 78% !important;
    }

    /*Action pop css start*/
    .action-popup-dyna-height {
        height: 297px !important;
    }

    .action-popup-bp-dyna-height {
        height: 290px !important;
    }
    /*Action pop css end*/

}

@media (min-width: 1000px) {
    .action-popup-bp-dyna-height {
        height: 142px !important;
    }
}

@media (min-width: 1200px) {
    /*Action pop css start*/
    .action-popup-dyna-height {
        height: 308px !important;
    }

    .action-popup-bp-dyna-height {
        height: 159px !important;
    }

    .ps-dynawidth-grid {
        width: 84% !important;
    }
    /*Action pop css end*/
}

@media (min-width: 1300px) {

    .ps-dynawidth-grid {
        width: 85% !important;
    }
    /*Action pop css start*/
    .action-popup-dyna-height {
        height: 308px !important;
    }

    .action-popup-bp-dyna-height {
        height: 163px !important;
    }
    /*Action pop css end*/

}

@media (min-width: 1400px) {

    .ps-dynawidth-grid {
        width: 85.5% !important;
    }
    /*Action pop css start*/
    .action-popup-dyna-height {
        height: 380px !important;
    }

    .action-popup-bp-dyna-height {
        height: 217px !important;
    }
    /*Action pop css end*/

}

@media (min-width: 1600px) {

    .ps-dynawidth-grid {
        width: 87% !important;
    }
    /*Action pop css start*/
    .action-popup-dyna-height {
        height: 437px !important;
    }

    .action-popup-bp-dyna-height {
        height: 265px !important;
    }
    /*Action pop css end*/
}

@media (min-width: 1920px) {
    #monthlyReportSpecificMesg #menuNewsCount {
        left: 50px !important;
    }

    .ps-dynawidth-grid {
        width: 88.5% !important;
    }
    /*Action pop css start*/
    .action-popup-dyna-height {
        height: 403px !important;
    }

    .action-popup-bp-dyna-height {
        height: 392px !important;
    }
    /*Action pop css end*/

}

/*media query based css added for org menu end */

#deviationActionPopupGrid .start-date-items .k-datepicker {
    width: 110px !important
}

.dropdown-backdrop {
    display: none !important;
}

.last-messurment-TL, .result-TL, .achived-result-TL {
    padding-right: 3px;
    width: 10%;
}

    .last-messurment-TL img, .result-TL img {
        padding-left: 10px !important;
    }

#MRTargetGrid .achived-result-TL {
    padding-right: 3px;
    width: 20%;
}
/* Bug :60407
.resultprvyr, .resultyear, #BPGoalsGrid .year {
    display: inline-flex;
}*/
/*CK Editor table formate*/
.cke_editable table {
    border-collapse: collapse !important;
    border: none !important;
    text-align: right !important;
}

.cke_editable td, .cke_editable th {
    border-bottom: dashed black 1.0pt !important;
    border-left: none !important;
    border-right: none !important;
    border-top: none !important;
}

    .cke_editable td:first-child, .cke_editable th:first-child {
        text-align: left !important;
    }

.cke_toolgroup a.cke_button:last-child:after,
.cke_toolgroup a.cke_button.cke_button_disabled:hover:last-child:after,
.cke_combo:after {
    height: 0px !important;
}

.cke_editable tr:first-child {
    font-weight: bold !important;
    border-bottom: solid black 1.0pt !important;
}

.cmn-ckeditor-htmlcontent p {
    font-size: 13px !important;
}
/*** User management styles**/
.input-box-style {
    width: 91% !important;
    border-color: #6FA1B2 !important;
    height: 100% !important;
}

.drop-down-box-style {
    width: 100% !important;
    border: 0 none !important;
    height: 18px !important;
}

#addUsersGrid,
#addUsersGrid table th,
#addUsersGrid table td {
    border-left: 0 none !important;
    border-right: 0 none !important;
}

    #addUsersGrid table th,
    #addUsersGrid table td {
        padding-left: 17px;
    }

#addUsersGrid {
    max-height: 260px;
    overflow-y: auto;
}

#yearlyBudgetDetailBudgetMaxWindow .filteractive {
    border: 1px solid #367b9a !important;
}

#yearlyBudgetDetailedMaxTreeGrid .k-grid-header th.k-header {
    vertical-align: top;
}

#yearlyBudgetButtonMaxSec li a,
#yearlyBudgetButtonMaxSec li a:hover {
    height: 20px !important;
}

.ps-toolbar-add {
    border: 1px solid #4A8EB9;
    border-radius: 4px;
    padding: 2px 8px;
}

.tab-section-minwidth160 {
    min-width: 160px !important;
}

#tabSection.tab-section.nav-tabs > li.active > a,
#tabSection.tab-section.nav-tabs > li.active > a:hover {
    background-color: #377B9A !important;
    width: 100%;
    color: #fff !important;
}

#tabSection.tab-section.nav-tabs > li > a {
    color: #606060 !important;
    width: 100%;
}

#budgetSuggestionGrid tr td {
    line-height: 8px;
}

#measuresGrid div.k-grid-header,
#investmentActionGrid div.k-grid-header,
#investmentsGrid2A div.k-grid-header {
    margin-top: 25px !important;
    margin-top: 70px !important;
}

    #measuresGrid div.k-grid-header .k-grid-header-locked,
    #investmentActionGrid div.k-grid-header .k-grid-header-locked,
    #investmentsGrid2A div.k-grid-header .k-grid-header-locked {
        border-color: #fff !important;
    }

#investmentActionGrid .k-numeric-wrap.k-state-default,
#investmentsGrid2A .k-numeric-wrap.k-state-default,
#measuresGrid .k-numeric-wrap.k-state-default,
#operationalBudgetGrid1A .k-numeric-wrap.k-state-default,
#businessPlanGetGoalGrid .k-numeric-wrap.k-state-default,
#monthlyActivityGrid .k-numeric-wrap.k-state-default,
#proposalServiceUnitTreelist .k-numeric-wrap.k-state-default {
    border-color: #377B9A;
    height: 23px;
}

    #investmentActionGrid .k-numeric-wrap.k-state-default input,
    #investmentsGrid2A .k-numeric-wrap.k-state-default input,
    #measuresGrid .k-numeric-wrap.k-state-default input,
    #operationalBudgetGrid1A .k-numeric-wrap.k-state-default input,
    #businessPlanGetGoalGrid .k-numeric-wrap.k-state-default input,
    #monthlyActivityGrid .k-numeric-wrap.k-state-default input,
    #proposalServiceUnitTreelist .k-numeric-wrap.k-state-default input {
        padding: 0;
        height: 20px;
    }

#yearlyBudgetPopupGrid .k-state-focused,
#YBExpenseAccountingGrid .k-state-focused,
#yearlyBudgetAllocationGrid .k-state-focused {
    box-shadow: inset 10px 10px 5px #f4f4f4 !important;
    background-color: #f4f4f4 !important;
}

.yb-banner-chart-bg {
    margin-bottom: 0px !important;
    background-color: #f6f6f6 !important;
    border: 1px solid #c6c6c6 !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    padding-top: 2px !important;
}

.history-tooltip-popup {
    background-color: #f4f4f4 !important;
    border: 1px solid black !important;
    min-height: 22px !important;
}


.span-border {
    border: 1px solid #ccc;
    padding: 5px 5px 0px 5px;
    background-color: #6099b1;
}

.div-border {
    border-bottom: 1px solid #ccc;
    font-family: semiFont, 'Open Sans Semibold', sans-serif;
}

.user-font {
    font-style: italic;
    color: #e94e1b;
}

.editable-link img {
    width: 16px;
    height: 16px;
}

.update-link img, .dynamic-content-link img {
    width: 16px;
    height: 16px;
}

.deletable-link img {
    width: 11px;
    height: 11px;
}

.deletable-link-node img {
    width: 11px;
    height: 11px;
}

#budgetSaveWrapper {
    top: 11px;
}

#serviceUnitBudgetSave {
    width: 90px;
}

#budgetAllocationLockSection {
    position: relative;
    z-index: 1;
}

#btnProposalServiceUnitLockSP span::selection,
#btnProposalServiceUnitLockSP input::selection,
#btnProposalServiceUnitLock span::selection,
#btnProposalServiceUnitLock input::selection,
#btnProposalServiceUnitCostLock span::selection,
#btnProposalServiceUnitCostLock input::selection {
    background: none !important;
    color: #377b9a !important;
    outline: 0 none !important;
}

/*Staff Planning Admin*/
#salaryAccountsGrid .k-grid-header {
    border-bottom: none !important;
}

#salaryAccountsGrid {
    border: none !important;
}

.custom-node-color {
    color: #009fe3;
}

#monthlyReportTabSection1 a:first-child .arrowtab-navbar-content {
    padding-left: 10px;
}

#monthlyReportTabSection1 a:last-child .arrowtab-navbar-content {
    padding-right: 10px;
}

/*News section command help center style*/
.help-search-results-content {
    font-size: 16px;
    line-height: 30px;
}

.help-search-results-detail-content img {
    /*width:auto!important;*/
    max-width: 100%;
    height: 100%;
}

.common-overlay {
    position: fixed;
    background: #000;
    height: 100%;
    width: 100%;
    z-index: 1;
    opacity: 0.7;
    top: 55px;
}

.news-sec-border {
    border: 1px solid #f4f4f4;
    border-radius: 2px;
}

.news-section-style {
    border: 1px solid #f4f4f4;
    border-radius: 2px;
    margin-left: 20px;
    width: 95%;
}

#helpCenter_tt_active {
    right: 15px;
    top: 15px;
    background: #fff;
    padding: 0;
    border: 0 none !important;
}

    #helpCenter_tt_active .k-tooltip-content {
        padding-right: 0;
    }

#wrapper {
    overflow: hidden;
}

#helpCenterContent a:hover {
    text-decoration: underline;
}

.search-results em, .highlighted {
    background-color: #ffed00;
    font-style: normal;
    font-weight: bold;
}

.videodetector {
    height: 461px;
    width: 854px;
}

.remove-videodetector {
    display: none;
}

    .remove-videodetector:hover {
        background-color: #c0392b;
    }

.videodetector:hover .remove-videodetector {
    display: none;
}

#headerNotifications {
    width: 10%;
}

#monthlyReportSpecificMesg {
    width: 16%;
}

#headerUserInfo {
    width: 9%;
}

#headerLogoSeprator {
    width: 2%;
}

#headerHelpIcon {
    width: 16%;
}

#helpCenterParentContainer {
    top: 40px !important;
}

.helpcenter-tooltip-toparrow-height {
    top: -11px !important;
}

#actionPopupTopSecWrapper {
    overflow-x: hidden;
    overflow-y: auto;
    width: 96.5%;
}

#actionPopupBottomSection {
    width: 96.5%;
}

#operationalTabSec li, #budgetChangesTabSec li {
    margin-bottom: -1px !important;
}

    #operationalTabSec li.active,
    #budgetChangesTabSec li.active {
        height: 29px;
    }

    #budgetChangesTabSec li a,
    #operationalTabSec li a {
        color: #000;
        background-color: #B1B1B1;
        border: 1px solid #c3c3c3;
        border-radius: 0px;
        border-top-right-radius: 4px;
        margin: 0;
        padding: 6px 15px;
    }

    #operationalTabSec li.active,
    #operationalTabSec li.active a,
    #operationalTabSec li.active a:hover,
    #budgetChangesTabSec li.active,
    #budgetChangesTabSec li.active a,
    #budgetChangesTabSec li.active a:hover {
        color: #000 !important;
        background: #fff !important;
        border-radius: 0px !important;
        border-top-left-radius: 4px !important;
        border-top-right-radius: 4px !important;
    }

        #operationalTabSec li.active,
        #operationalTabSec li.active a:hover,
        #budgetChangesTabSec li.active,
        #budgetChangesTabSec li.active a:hover {
            border-bottom: 0 none !important;
            text-align: left !important;
        }

    #operationalTabSec li:last-child,
    #budgetChangesTabSec li:last-child {
        border-bottom: 1px solid #c3c3c3;
        width: 22%;
        height: 30px;
    }

#budgetChangesGrid, #operationalGrid {
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    padding-top: 5px !important;
}

.column10-items input, .k-window-titlebar .k-window-action {
    width: 100%;
}

#importExportTitleBar {
    position: absolute;
    right: 90px;
    top: 6px;
}

#importFinExportTitleBar {
    position: absolute;
    right: 90px;
    top: 14px;
}

.import-wrapper {
    display: inline-block;
}

#kostraQualityScoreGrid .k-grid-content-locked {
    height: 100% !important;
}

.month-year-left, .monthly-year-center, .month-year-right {
    right: 0;
}

#staffGrid .k-grid-header, #staffGrid .k-pager-wrap.k-grid-pager,
#staffGridFooter {
    clear: both;
}

#staffGrid .k-pager-wrap.k-grid-pager {
    float: left;
}

.bm-editor-yes:hover, .bm-editor-no:hover {
    color: #fff;
}

#budgetChangesHistoryTitleBar {
    position: absolute;
    right: 50px;
    top: 12px;
}

#financialStatusBudgetSearchGrid .k-grid-header,
#accountReportingSearchGrid .k-grid-header {
    padding-right: 5px !important;
}

    #budgetChangesHistorySearchGrid .k-grid-header .k-auto-scrollable,
    #financialStatusBudgetSearchGrid .k-grid-header .k-auto-scrollable,
    #accountReportingSearchGrid .k-grid-header .k-auto-scrollable {
        overflow-x: auto !important;
    }

#budgetChangesHistorySearchGrid .k-operator-hidden,
#budgetChangesHistorySearchGrid .k-operator-hidden .k-input.k-textbox {
    width: 100% !important;
}

    #budgetChangesHistorySearchGrid .k-operator-hidden .k-input.k-textbox,
    #financialStatusBudgetSearchGrid .k-operator-hidden .k-input.k-textbox,
    #accountReportingSearchGrid .k-operator-hidden .k-input.k-textbox {
        border: 1px solid #377b9a;
    }

#BPBudgetOverviewGrid {
    max-height: 600px;
    overflow-y: auto;
}

#financialStatusBudgetSearchGrid .k-operator-hidden,
#financialStatusBudgetSearchGrid .k-operator-hidden .k-input.k-textbox,
#accountReportingSearchGrid .k-operator-hidden,
#accountReportingSearchGrid .k-operator-hidden .k-input.k-textbox {
    width: 100% !important;
}

#investmentDetailOrgFinishYearPicker,
#investmentDetailStartYearPicker {
    height: 31px;
}

#proposalServiceUnitTreelist table {
    min-width: 100%;
}

#reportingFinStatusAccountDetailGrid .k-operator-hidden,
#reportingFinStatusAccountDetailGrid .k-operator-hidden input,
#reportingFinStatusAccountDetailGrid .k-widget.k-datepicker {
    width: 100% !important;
}

#populationDetailsGrid .k-grid-content {
    overflow-y: hidden;
}

#populationDetailsGrid .k-grid-header {
    padding-right: 0px !important;
}

.status-line-style {
    display: inline-block;
    height: 45px;
    width: auto;
}

.border-color1 {
    border-left: 6px solid #32CD32;
}

.border-color2 {
    border-left: 6px solid #FF5733;
}

.border-color3 {
    border-left: 6px solid #FF0000;
}

.number-color {
    color: #4a8eb9;
    padding-left: 5px;
}

.filter-check-wrap {
    display: inline-block;
}

.filter-rows-chk {
    margin-top: 0px;
    vertical-align: top;
    margin-right: 5px !important;
}

#FPDelActionTotalRow .fin-tb-column-hidden {
    padding: 0 !important;
    width: 0 !important;
}

#ybBudgetEntryGridMaximizeScreen {
    outline: none;
}

#adminAdjustmentCodeGridMaximizeScreen {
    outline: none;
    float: right;
}

#yearlyBudgetAllocationTotal table {
    padding-right: 17px;
}

#yearlyBudgetPopupGrid2 {
    border-right: none;
}

#yearlyBudgetAllocationTotal {
    border-top: 1px solid #ccc !important;
    border-right: none;
}

    #salaryAccountGrid tr td,
    #yearlyBudgetPopupGrid2 tr td,
    #yearlyBudgetAllocationTotal tr td {
        border-right: none;
        border-left: none;
    }

#orientationWindow {
    padding: 20px;
}

#YBBudgetLineWrapper {
    position: absolute;
    right: 20px;
    top: 20px;
}

/*Combined indicators CSS start*/
#combinedIndicatorsSelectIndicatorsContainer .k-dropdown.k-header {
    width: 100% !important;
}

#combinedIndicatorsIndicatorGrid .k-grid-content,
#combExpGrid1 .k-grid-content,
#combExpGrid2 .k-grid-content {
    overflow-x: auto;
    overflow-y: hidden;
}

#combinedIndicatorOrgTreeView_tv_active {
    border-top: none !important;
}

#selectedIndicatorsGrid {
    min-height: 200px;
    max-height: 500px;
}

    #selectedIndicatorsGrid .k-grid-content {
        overflow-x: hidden;
        overflow-y: auto;
    }
/*Combined indicators CSS end*/
td.edit-new-change-items {
    border: 1px solid #367b9a !important;
    border-radius: 5px !important;
    background: #fff !important;
    display: inline-block;
    height: 100% !important;
    width: 90% !important;
}

    td.edit-new-change-items span {
        border: none !important;
        border-radius: 0 !important;
        box-shadow: none !important
    }

#investmentPopupTitleBar {
    position: absolute;
    right: 35px;
    top: 8px;
}

#createOrderWrapper .k-picker-wrap.k-state-default {
    width: 100% !important;
}

.on-off-text {
    position: absolute;
    top: 5px;
}

.k-state-hover .k-clear-value.k-i-close {
    display: none;
}

.add-dashboard-img {
    background: #fff;
    padding-top: 7px;
    padding-bottom: 3px;
    display: block;
    height: 33px;
    margin-right: 10px;
    color: #000 !important;
}

.dashboard-top-section-li {
    border-right: 1px solid #c3c3c3;
    width: 15%;
}

.header-img-text-styling a {
    color: #347B9A;
}

.dash-board-close {
    color: #808080 !important
}

.budget-suggestion {
    display: inline-block;
    line-height: 1.2;
    width: 100%;
}

#filterWraffer .k-icon {
    margin-top: 4px !important;
}

#monthlyProjectReportGrid .k-filtercell .k-input,
#monthlyProjectReportGrid .filter-input-search,
#investmentStatusGrid .k-filtercell,
#investmentStatusGrid .filter-input-search,
#investmentStatusMaxPopupGrid .k-filtercell,
#investmentStatusMaxPopupGrid .k-filtercell .k-input,
#ordersGrid .k-filtercell, #freeDimGrantGridParent .k-filtercell,
#ordersGrid .k-filtercell .k-input {
    width: 95%;
}

#investmentStatusGrid .k-filtercell,
#investmentStatusMaxPopupGrid .k-filtercell,
#monthlyProjectReportGrid .k-filtercell {
    width: 110%;
}

    #investmentStatusGrid .k-filtercell .k-input.k-textbox,
    #investmentStatusMaxPopupGrid .k-filtercell .k-input.k-textbox,
    #ordersGrid .k-filtercell .k-input.k-textbox,
    #feeGrid .k-filtercell .k-input.k-textbox,
    #monthlyProjectReportGrid .k-filtercell .k-input.k-textbox {
        border: 1px solid #6FA1B2 !important;
    }

#investmentStatusMaxPopupGrid .k-filter-row th,
#investmentStatusGrid .k-filter-row th,
#monthlyProjectReportGrid .k-filter-row th {
    border: 0 none !important;
}

#ordersGrid .k-filtercell,
#ordersGrid .k-filtercell .k-widget,
#ordersGrid .k-filtercell > span,
#freeDimGrantGridParent .k-filtercell > span {
    width: auto !important;
}

#kostraServiceOverviewGridEvaluation .kostra-eval-items img {
    vertical-align: top !important;
}

.popup-window-styles {
    width: 100%;
}

#hiddenBordersListWrapper {
    height: 30px;
    width: 78.5%;
    position: absolute;
    z-index: 999;
    left: 19.5%;
    top: 10px;
}

.hidden-borders-list {
    border: 1px solid #377B9A;
    color: #30697D;
    width: 25px;
    float: left;
    margin-right: 5px;
}

.less-than-symbol {
    position: absolute;
    right: -20px;
    top: 2px;
    font-size: 10px;
    font-weight: normal;
    color: #367b9a;
}

.hidden-borders-list.collapse {
    display: none;
}

.hidden-borders-list.in {
    display: inline-block;
}

#ExportPeriodTreelistToExcel, #ExportAccountTreelistToExcel {
    padding: 2px 8px;
}

.expand-collapse-filter-sec {
    background: #367A9B;
    outline: 2px solid #367A9B;
    position: absolute;
    top: 1px;
    cursor: pointer;
    min-width: 24px;
    right: 0;
}

.expand-collapse-filters {
    position: absolute;
    right: -26px;
    top: 45%;
    width: 80px;
    height: auto;
    transform: rotate(-90deg);
    color: #fff
}

.exp-collapse-top {
    position: absolute;
    top: 6px;
    width: 24px;
}

.exp-collapse-bottom {
    position: absolute;
    bottom: 6px;
    width: 24px;
}

.expand-collapse-filter-sec .glyphicon {
    font-size: 10px;
    color: #fff;
    float: left;
}

.expand-collapse-filter .glyphicon-chevron-right:before {
    content: "\e079";
}

.expand-collapse-filter .glyphicon-chevron-left:before {
    content: "\e080";
}

.collapsedFilter-wrapper {
    background: #367A9B;
    padding-bottom: 5px !important;
    outline: 1px solid #367A9B;
}

.task-alert-sec {
    width: 5%;
}

.task-contents {
    width: 50%;
}

.task-img-content-sec {
    width: 17%;
}

.tasks-desc-sec {
    width: 28%;
}

.assignmet-popup-leftsection {
    margin-left: 16px;
    margin-right: 11px;
    width: 18%;
}

.assign-exp-collaspe {
    height: 10px;
    width: 20px;
    display: inline-block;
    background-repeat: no-repeat;
    vertical-align: text-bottom;
}

#assignmentLeftGrid {
    height: 550px;
    overflow-y: auto;
}

#assignDelegationGrid th {
    vertical-align: top;
}

#delegateExpCollapse.collapsed .assign-exp-collaspe, #expCollapseMissing.collapsed .assign-exp-collaspe {
    background-image: url('../images/ic_expand_less_24px.svg');
}

#delegateExpCollapse .assign-exp-collaspe, #expCollapseMissing .assign-exp-collaspe {
    background-image: url('../images/ic_expand_more_24px.svg');
}

#assignmentLeftGrid li:last-child {
    border: none !important
}

#investmentStatusGrid td:not(:first-child).k-command-cell {
    border: none !important;
    border-top: 1px solid #c3c3c3 !important;
}

#assignmentLeftGrid li a.active {
    font-weight: bold;
}

#articleEditorSaveWrap {
    position: absolute;
    bottom: 15px;
    right: 50px;
}

.k-autocomplete.k-state-hover > .k-i-close {
    display: inline-block !important;
}

.sustain-img-section {
    display: inline-block;
    width: 13%;
    margin-right: 30px;
}

.active-image img {
    border: 1px solid #30697D;
    border-radius: 4px;
}

.change-items .k-numerictextbox,
.newAmount-items .k-numerictextbox,
.changetotalbudget-items .k-numerictextbox,
.newTotalBudget-items .k-numerictextbox {
    width: 100%;
}

#salaryAccountGrid .k-numerictextbox input,
#salaryAccountGrid .k-dropdown-wrap.k-state-default {
    height: 26px !important;
}

#salaryAccountAddContainer, #salaryAccountModeToggle {
    border: 1px solid #367b9a;
    padding: 4px 11px;
    border-radius: 4px;
}

.dropdown-cover-style .k-dropdown-wrap.k-state-default {
    height: 27px !important;
}

#salaryAccountGrid .k-grid-header th.k-header.account-poup-header {
    text-align: left;
    border: none;
    vertical-align: top;
}

.salary-account-edit-color {
    background: rgba(74, 142, 185, 0.11);
}

.override-overlay {
    z-index: 10004 !important;
    opacity: 0.3 !important;
}

#salaryAccountGrid .k-filter-row input.k-input {
    height: 28px;
}


#salaryAccountGrid .change-items,
#salaryAccountGrid .changetotalbudget-items,
#salaryAccountGrid .newTotalBudget-items,
#salaryAccountGrid .newAmount-items,
#salaryAccountGrid .description-items,
#salaryAccountGrid .dropDown-pensiontype,
#salaryAccountGrid .dropDown-periodicKey {
    border: 1px solid rgb(54, 123, 154);
    border-radius: 4px;
    height: 24px;
    padding: 2px 5px;
    background: #fff;
    display: inline-block;
    width: 95%;
}

#salaryAccountGrid .dropDown-periodicKey, #salaryAccountGrid .dropDown-pensiontype {
    width: 89% !important;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

#salaryAccountGrid .description-items {
    min-height: 24px;
    height: auto;
}

#salaryAccountGrid .k-filtercell, 
#salaryAccountGrid .k-filtercell .k-widget,
#salaryAccountGrid .k-filtercell > span {
    display: block;
    width: 100%;
}

#salaryAccountGrid_active_cell .k-filtercell,
#salaryAccountGrid_active_cell .k-filtercell .k-widget,
#salaryAccountGrid_active_cell .k-filtercell > span {
    margin-left: 2px !important;
}

#salaryAccountGrid td .k-widget.k-numerictextbox {
    width: 95%;
}

#salaryAccountGrid td .k-numeric-wrap.k-state-default.k-expand-padding {
    display: inline-block;
    height: 28px;
    width: 100%;
}

#salaryAccountGrid td span.k-numeric-wrap input {
    display: inline-block;
    padding-right: 5px;
}

.budgetHistory-items img {
    height: 18px;
    width: 18px;
    cursor: pointer;
}

#operationalBudgetGrid1ATotal .k-grid-content-locked {
    height: 100% !important;
}

#assignmentTasksGrid .cmn-display-none, 
#assignmentTasksUnitGrid .cmn-display-none {
    display: none !important;
}

#baseImage_logo {
    height: 100% !important;
}

.approvedAdjustmentCode {
    vertical-align: sub;
}

.check-box-wrapper {
    position: absolute;
    right: 115px;
    top: 5px;
}

@media (min-width: 1000px) {

    #monthlyReportSpecificMesg #menuNewsCount {
        left: 34px !important;
    }

    #newNotificationCount {
        right: 10px;
    }
}


@media (min-width: 1680px) {
    #monthlyReportSpecificMesg #menuNewsCount {
        left: 45px !important;
    }
}

@media (min-width: 1920px) {
    #monthlyReportSpecificMesg #menuNewsCount {
        left: 49px !important;
    }
}
/* Media queries for lower resolution screens and zoom in/zoom out START*/
@-ms-viewport {
    width: device-width;
}

@media only screen and (min-width: 2200px) {
    #okonomiStatusGridParentMaxPopup,
    #okonomiStatusGridMaxPopup,
    #okonomiStatusTreelistPopup,
    #okonomiPlanGridPopup,
    #financialStatusAccountDetailGrid,
    #financialStatusBudgetDetailGrid,
    #investmentStatusMaxPopupGrid,
    #proposedBudgetMaxGrid,
    #MRGoalsMaxGrid,
    #MRTargetMaxGrid,
    #absenceStatusMaxGrid {
        zoom: 1.5;
        overflow-x: hidden !important;
    }

    .max-popup-footer,
    #MRFinplanMaxWindow .display-original-plan,
    #MRInvestmentsMaxWindow .display-original-plan,
    #MRFinplanActionMaxWindow .display-original-plan,
    #MRGoalsMaxWindow .cmn-btns-wrapper,
    #MRTargetMaxWindow .cmn-btns-wrapper {
        zoom: 1.5;
    }

    .k-window-titlebar.background-white {
        zoom: 1.5;
    }
}

@media screen and (min-width: 1930px) {

    .arrowtab-navbar-content {
        width: auto;
    }

    #monthlyReportTabSection1 .arrowtab-navbar-content div {
        white-space: nowrap;
    }
}

@media only screen and (min-width: 1500px) {
    #filterColumnFilters .col-md-3 input.k-textbox {
        width: 90% !important;
    }
}

@media screen and (min-width: 1500px) {
    #searchBPMaxCommonOverviewGrid {
        padding: 3px 8px !important;
    }
}

@media only screen and (max-width: 1550px) {
    #financialStatusAccountDetailWindow {
        zoom: .9;
        overflow: auto;
        max-height: 800px
    }

    .chapter-name {
        width: 200px !important;
    }
}

@media only screen and (max-width: 1400px) {
    #financialStatusAccountDetailWindow {
        zoom: .9;
        overflow: auto;
        max-height: 670px
    }

    .btn {
        zoom: 0.89;
    }

    .k-window-title {
        zoom: 0.8;
    }
}

@media only screen and (max-width: 1300px) {
    #financialStatusAccountDetailWindow,
    #uploadImagePopupWindow {
        zoom: .88;
        overflow: auto;
        max-height: 680px;
    }
}

@media only screen and (max-width: 1200px) {
    #financialStatusAccountDetailWindow {
        max-height: 600px
    }
}

@media only screen and (max-width: 1100px) {
    #okonomiStatusGridParentMaxPopup,
    #okonomiStatusGridMaxPopup,
    #okonomiStatusTreelistPopup,
    #okonomiPlanGridPopup,
    #financialStatusAccountDetailGrid,
    #financialStatusBudgetDetailGrid,
    #investmentStatusMaxPopupGrid,
    #proposedBudgetMaxGrid,
    #MRGoalsMaxGrid,
    #MRTargetMaxGrid,
    #absenceStatusMaxGrid {
        width: 100%;
        height: 100%;
    }

    .max-popup-footer,
    #MRFinplanMaxWindow .display-original-plan,
    #MRInvestmentsMaxWindow .display-original-plan,
    #MRFinplanActionMaxWindow .display-original-plan,
    #MRGoalsMaxWindow .cmn-btns-wrapper,
    #MRTargetMaxWindow .cmn-btns-wrapper {
        zoom: 0.7;
        width: 100%;
        bottom: 1px;
    }

    #MRFinplanMaxWindow,
    #monthlyReportSaveDataParentPopup,
    #MRInvestmentsMaxWindow,
    #MRInvestmentSave,
    #MRFinplanActionMaxWindow,
    #monthlyFinancialSaveButtonPopup,
    #MRGoalsMaxWindow #monthlyGoalsMaxSaveButton,
    #MRTargetMaxWindow #monthlyTargetMaxSaveButton {
        margin-right: 25px;
    }

    .monthly-report-maxwindow {
        padding: 0;
        padding-left: 5px;
    }

    .max-popup-footer {
        height: 38px;
    }

    #financialStatusAccountDetailWindow {
        max-height: 500px
    }

    .k-window-title {
        zoom: 0.6;
    }
}

@media only screen and (max-width: 800px) {
    #okonomiStatusGridParentMaxPopup,
    #okonomiStatusGridMaxPopup,
    #okonomiStatusTreelistPopup,
    #okonomiPlanGridPopup,
    #financialStatusAccountDetailGrid,
    #financialStatusBudgetDetailGrid,
    #investmentStatusMaxPopupGrid,
    #proposedBudgetMaxGrid
    #MRGoalsMaxGrid,
    #MRTargetMaxGrid,
    #absenceStatusMaxGrid {
        width: 100%;
        zoom: 0.5;
        height: auto;
    }

    .k-window-titlebar.background-white {
        zoom: 0.5;
        margin-top: -75px !important;
    }

    .max-popup-footer, #MRFinplanMaxWindow .display-original-plan,
    #MRInvestmentsMaxWindow .display-original-plan,
    #MRFinplanActionMaxWindow .display-original-plan,
    #MRGoalsMaxWindow .cmn-btns-wrapper,
    #MRTargetMaxWindow .cmn-btns-wrapper {
        zoom: 0.5;
        width: 100%;
        bottom: 1px;
    }

    .max-popup-footer {
        height: 35px;
    }

    #MRFinplanMaxWindow #monthlyReportSaveDataParentPopup,
    #MRInvestmentsMaxWindow #MRInvestmentSave,
    #MRFinplanActionMaxWindow #monthlyFinancialSaveButtonPopup,
    #MRGoalsMaxWindow #monthlyGoalsMaxSaveButton,
    #MRTargetMaxWindow #monthlyTargetMaxSaveButton {
        margin-right: 25px;
    }

    .monthly-report-maxwindow {
        padding: 0 !important;
        width: 99.8% !important;
    }

    #financialStatusAccountDetailWindow {
        max-height: 430px
    }

    .btn {
        zoom: 0.6;
    }

    .k-window-title {
        zoom: 0.6;
    }
}

@media only screen and (max-width: 600px) {
    #okonomiStatusGridParentMaxPopup,
    #okonomiStatusGridMaxPopup,
    #okonomiStatusTreelistPopup,
    #okonomiPlanGridPopup,
    #financialStatusAccountDetailGrid,
    #financialStatusBudgetDetailGrid,
    #investmentStatusMaxPopupGrid,
    #proposedBudgetMaxGrid,
    #MRGoalsMaxGrid,
    #MRTargetMaxGrid,
    #absenceStatusMaxGrid {
        width: 100%;
        zoom: 0.4;
        height: 398px;
    }

    .k-window-titlebar.background-white {
        zoom: 0.4;
        margin-top: -75px !important;
    }

    .max-popup-footer,
    #MRFinplanMaxWindow .display-original-plan,
    #MRInvestmentsMaxWindow .display-original-plan,
    #MRFinplanActionMaxWindow .display-original-plan,
    #MRGoalsMaxWindow .cmn-btns-wrapper,
    #MRTargetMaxWindow .cmn-btns-wrapper {
        zoom: 0.4;
        width: 100%;
        bottom: 1px;
    }

    .max-popup-footer {
        height: 35px;
    }

    #MRFinplanMaxWindow, #monthlyReportSaveDataParentPopup,
    #MRInvestmentsMaxWindow,
    #MRInvestmentSave,
    #MRFinplanActionMaxWindow,
    #monthlyFinancialSaveButtonPopup,
    #MRGoalsMaxWindow #monthlyGoalsMaxSaveButton,
    #MRTargetMaxWindow #monthlyTargetMaxSaveButton {
        margin-right: 25px;
    }

    .monthly-report-maxwindow {
        padding: 0 !important;
        width: 99.8% !important;
    }

    #financialStatusAccountDetailWindow {
        max-height: 280px;
        zoom: .75;
    }

    .btn {
        zoom: 0.6;
    }

    .k-window-title {
        zoom: 0.6;
    }

    .login-info-content-wrap {
        top: 0 !important;
        left: 0 !important;
        position: relative !important;
    }

    .content-box-bg {
        width: 100% !important;
    }

    .login-footer {
        position: relative !important;
        margin-top: 30px;
        padding-right: 0 !important;
        height: 30px !important;
    }
}

@media only screen and (max-width: 500px) {
    #okonomiStatusGridParentMaxPopup,
    #okonomiStatusGridMaxPopup,
    #okonomiStatusTreelistPopup,
    #okonomiPlanGridPopup,
    #financialStatusAccountDetailGrid,
    #financialStatusBudgetDetailGrid,
    #investmentStatusMaxPopupGrid,
    #proposedBudgetMaxGrid,
    #MRGoalsMaxGrid,
    #MRTargetMaxGrid,
    #absenceStatusMaxGrid {
        height: 395px;
    }
}

.ck-img-alt-required label:after {
    content: '*';
    color: red;
}

#samSubTabSection .subTab-list-items {
    position: relative;
    z-index: 1;
    display: inline-block;
    margin: 0;
    text-align: center;
}

#samSubTabSection .subTab-menu-tab-text {
    display: inline-block;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 7px 15px;
    width: 100%;
}

#samSubTabSection .subTab-list-items.active {
    border: 1px solid #000;
}

#helpCenterItemsList li, #helpCenterStaticItemsList li {
    width: 22%;
    margin: 10px;
    box-shadow: 0px 5px 12px 5px rgba(195,195,195,1);
    font-size: 18px;
    font-family: semiFont, sans-serif;
    color: #377B9A;
}

    #helpCenterItemsList li.active, #helpCenterStaticItemsList li.active {
        background: #377B9A !important;
        color: #fff;
    }

#helpCenterItemsList img {
    width: 150px;
    height: 150px;
}

#helpCenterStaticItemsList img {
    width: 80px;
    height: 80px;
}

#helpCenterItemsList span, #helpCenterStaticItemsList span {
    display: inline-block;
    padding-bottom: 20px;
    border-bottom: 3px solid #367b9a;
}

.helpcenter-banner-image {
    background-image: url('../images/hjelpesenter_banner_main.svg');
}

.help-search-input {
    width: 85%;
    float: left;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.help-search-button {
    float: left;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    height: 35px;
    width: 100px;
}

/*Strategy opportunity assessment*/
#assessmentServiceIdContainer .k-dropdown {
    width: 98% !important;
}

#opportunityAssessmentGrid .k-grid-content {
    overflow-y: auto !important;
    max-height: 600px;
    border-top: none !important;
}

/* END */
.text-shadow-none {
    text-shadow: none !important;
}

.hc-module-border {
    border: 1px solid #367b9a;
}

.sam-update-btn {
    position: absolute;
    right: 0;
    bottom: 20px;
}

#goalsTargetGrid.k-grid tr td.remove-border-bottom,
#strategyGrid.k-grid tr td.remove-border-bottom, #assignmentGrid.k-grid tr td.remove-border-bottom {
    border-bottom: 0 !important;
    border-top: 0 !important;
}
/* Media queries for lower resolution screens and zoom in/zoom out END*/

.custom-node-content {
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 86%;
    position: relative;
    top: 3px;
}

#assessmentSummeryTab a {
    border-radius: 4px !important;
}

#assessmentDetailTab a {
    border-radius: 4px !important;
}

#assessmentsSummeryTab, #assessmentsDetailTab, #assessmentDetailTab {
    max-width: 100% !important;
}

#uploadContentSec {
    min-height: 310px;
}

#uploadedImageContent {
    max-width: 74%;
    width: auto;
}

#alterTextSection {
    height: 100%;
    display: block;
    position: relative;
    width: 25%;
    min-height: 243px;
}

.warning-bg-color {
    background: #fccb8d;
}

#climateActionDetailGrid .k-grid-footer {
    border: none !important;
}

#climateActionDetailGrid .k-grid-header {
    padding-right: 0px !important;
}

.projectWarning {
    max-height: 200px;
    overflow-y: auto;
}

.update-button {
    position: absolute;
    bottom: 10px;
    right: 0;
}

#tagsAndCheckListTabs {
    border-bottom: 0;
}

    #tagsAndCheckListTabs li a {
        background: #dbd9d9;
    }

    #tagsAndCheckListTabs li.active a {
        border: 0 !important;
    }


#planActionAddEditPopupWindow tr th.header-stylings {
    white-space: nowrap !important;
    width: 110px !important;
}

.dropDown-departmentCode .k-widget.k-dropdown {
    width: 100%;
    white-space: normal;
    overflow: hidden;
    text-overflow: ellipsis;
}

figure.image.image_resized img {
    width: 100%;
}

.psqa-hr-line {
    border-top: 1px solid #C8DB6A !important;
}

.sustain-img-section img {
    width: 120px;
    height: 120px;
}

.collapse-drop-icon {
    padding-right: 5px;
}

.list-style-none {
    list-style-type: none;
}

.collapsed .fa-chevron-down:before {
    content: "\f054";
}

#snapseFilterWrap ul li a {
    color: #000;
}

/*Consolidation css start*/

#consolidationAdminGrid .k-grid-content {
    overflow-y: auto;
    text-overflow: ellipsis;
    min-height: 100px;
    max-height: 500px;
}

    #consolidationAdminGrid .k-grid-content tbody tr:first-child td {
        border-left: 0 !important;
    }


#includeInConsolidationContainer .k-dropdown {
    width: 109% !important;
}

#includeInConsolidationContainer .k-dropdown-wrap {
    height: 36px !important;
}

#includeInCompanyTypeContainer .k-dropdown {
    width: 90%;
}

#includeInCompanyTypeContainer .k-dropdown-wrap {
    height: 36px !important;
}

#consolidationLevelContainer .k-dropdown {
    width: 106%;
}

#consolidationLevelContainer .k-dropdown-wrap {
    height: 36px !important;
}

#functionInConsolidationContainer .k-dropdown {
    width: 91%;
}

#functionInConsolidationContainer .k-dropdown-wrap {
    height: 36px !important;
}

#companyIdNumeric .k-numerictextbox {
    width: 80% !important;
}

#companyShareNumericId .k-numerictextbox {
    width: 80% !important;
}
/*Consolidation css end*/

#planWorkerControllerId a:hover,
#planPublishControllerId a:hover,
#planPublishControllerId a:active,
#planWorkerControllerId a:active {
    text-decoration: underline;
}

.hidden-tooltip-info {
    position: absolute;
    visibility: hidden;
}

.collaborative-text-editor {
    min-height: 16cm;
}

.warning-note-style {
    background: #FCEBD6;
    display: inline-block;
    padding: 4px 12px;
    margin-right: 20px;
    line-height: 2;
    vertical-align: top;
}

.month-year-drop-down-assignment-task-tab {
    width: 15%;
    min-height: 33px;
    margin-right: 1%;
    text-align: left;
}

.month-year-drop-down-assignment-goal-tab {
    width: 20%;
    min-height: 33px;
    margin-right: 1%;
    text-align: left;
}

#filterColumnFilters .col-md-3 input[type="checkbox"] {
    margin-right: 5px;
}

html.noscroll, body.noscroll {
    overflow: hidden;
}

ins {
    text-decoration: none;
    background-color: #e6ffe6;
}

del {
    text-decoration: line-through;
    background-color: #ffe6e6;
}

.filter-search-btn-ybr {
    padding: 1px 5px;
    vertical-align: baseline;
    margin-left: 4px;
}

.filter-img-ybr {
    height: 19px;
    width: 18px;
    vertical-align: text-top;
}

.status-text-overview {
    font-family: latoItalicFont, sans-serif;
    font-size: 11px !important;
}

.mr-reporting-items {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 56%;
}

.goals-delete-msg {
    white-space: initial;
    line-height: 35px;
    top: 17px;
}

.readable-editor-content {
    max-height: 100px;
    overflow: hidden;
}

#budgetChange1APopUpGrid #adjustmentCode {
    color: black !important;
}

.plan-dyn-padding-left5 {
    padding-left: 5% !important;
}

.overfolw-y-auto,
.menu-list-container {
    overflow-y: auto !important;
}

#salaryAccountsGrid .k-grid-content,
#2BSubFinGrid1 .k-grid-content,
#2BSubFinGrid2 .k-grid-content,
#2BSubFinGrid3 .k-grid-content,
#2BSubFinGrid4 .k-grid-content,
#Framsikt1AGrid .k-grid-content,
#Framsikt1BGrid .k-grid-content,
#1AGrid .k-grid-content,
#1BGrid .k-grid-content,
#2AGrid .k-grid-content,
#2BGrid .k-grid-content,
#B3Grid .k-grid-content,
#B4Grid .k-grid-content,
#checkListGrid .k-grid-content,
#checkListDetailsGrid .k-grid-content,
#actionSummeryGrid .k-grid-content,
#assessmentActionsTotalGrid .k-grid-content,
#negativeActionsGrid .k-grid-content,
#positiveActionsGrid .k-grid-content,
#populateStatSectionsTabWrap,
#planClimateDescription {
    overflow-y: auto !important;
}

.k-tooltip, .k-tooltip.k-popup, .k-tooltip.k-widget {
    color: #000;
    background-color: #fff;
    border: 1px solid rgb(111, 161, 178);
    min-height: 50px;
}

.ck-content a span {
    color: #266ead !important;
}

.dash-widget-thumbnail {
    width: 47px;
    height: 44px;
}

.non-editable-item {
    display: inline-block;
    width: 85%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.linkNodeTooltip img {
    cursor: default !important;
}

#collapseOperational .k-widget .k-icon.k-i-collapse,
#collapseOperational .k-widget .k-icon.k-i-expand {
    float: left !important;
}

.bc-month-selector .k-dropdown-wrap.k-state-default {
    height: 29px !important;
}

/* mradmin approval flow custom css start */

.fin-plan-collabortive-history {
    display: inline-block;
    background: #fff;
    padding: 5px 10px;
    margin-right: 5px;
    vertical-align: middle;
    border-radius: 4px;
    text-decoration: none;
}

.mr-approval-tab-background {
    background: #5b3365 !important;
}

    .mr-approval-tab-background li a {
        height: 100% !important;
        padding: 8px 10px;
        display: inline-block;
    }

    .mr-approval-tab-background .glyphicon-play {
        color: #F58D66;
    }


.mr-approval-tab-highlighter {
    background: #31003f !important;
}

.mr-approval-banner-background {
    background: #44264C !important;
}

.widget-error-first-numeric, .widget-error-no-column {
    background-color: #fef6ed;
    padding: 8px;
    padding-top: 6px;
    padding-bottom: 10px;
    border-radius: 4px;
    margin-right: 7px;
}

    .widget-error-first-numeric img, .widget-error-no-column img {
        vertical-align: text-bottom;
        margin-right: 3px;
    }

#customPluginPopupWindow1 #customPlaceholderFirstNumericCol,
#customPluginPopupWindow1 #errorMessageSelectMin1Col {
    position: relative;
    top: 4px;
}
/* mradmin approval flow custom css end */

#link-icon img {
    height: 25px !important;
    width: 25px !important;
}

#notificationContents {
    position: absolute;
}

#messageContents {
    position: absolute;
}

#newNotificationMenu {
    border-bottom-left-radius: inherit;
    border-bottom-right-radius: inherit;
    position: absolute;
    box-shadow: 0 0 5px rgba(0, 0, 0,0.1 );
    /* color: #1B2B6A; */
}

#messageDropdown {
    border-bottom-left-radius: inherit;
    border-bottom-right-radius: inherit;
    box-shadow: 0 0 5px rgba(0, 0, 0,0.1 );
}

.msgs-footer a {
    padding: 10px 25px 13px 0px !important;
}

#newNotificationMenu a:hover {
    text-decoration: underline;
}

#newNotificationMenu li {
    min-height: 65px;
}

#messageDropdown .msgs-footer a:hover {
    text-decoration: underline;
}

#messageDropdown > div {
    min-height: 65px;
    padding: 10px 10px 5px 10px;
}

.new-cross-icon,
.new-cross-icon:hover {
    margin: 10px 0px 0px 0px !important;
    padding: 0px 2px 0px 0px !important;
    background: transparent;
}

.new-bell-icon {
    width: 31px;
    height: 31px;
}

.new-msg-icon {
    width: 22px;
    height: 22.13px;
}

.new-link-icon {
    height: 25px;
    width: 25px;
}

#newDeleteAllButton {
    color: #1B2B6A !important;
    background-color: white;
    font-size: 13px !important;
    border-radius: 16px;
    height: 25px;
    border: none;
}

#newHeaderMessageBar .dropdown-menu.extended {
    max-height: 543px;
    width: 450px !important;
    right: -145px;
    left: unset;
}

#messageContents a:hover {
    text-decoration: underline;
}

#mainHeader .badge.bg-warning.yellow-bubble {
    background: #E6A54B;
    color: black;
}

#mainHeader .badge.bg-warning.blue-bubble {
    background: #1B2B6A;
    color: white;
}

#logoHederWrapper {
    width: 19%;
}

#middleHeaderMenuSec {
    width: 66%;
    white-space: nowrap;
}

#leftHeaderMenuSec {
    width: 15%;
}

.helpcentre-menu-profile {
    display: inline-block;
    width: 100%;
    height: 60px;
    padding: 0px 14px;
    clear: both;
    font-weight: bold;
    line-height: 1.428571429;
    color: #000000;
    white-space: nowrap;
    cursor: pointer;
}

.helpcentre-menu-profile-kommune {
    width: 100%;
    padding: 18px 10px 30px 3px;
    clear: both;
    font-weight: normal;
    line-height: 1.428571429;
    color: #000000;
    white-space: nowrap;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
}

.collapsible-dropdown-helpcentre {
    padding: 5px 20px 1px 28px;
    margin-top: -18px;
    margin-bottom: 30px;
}

#mainHeader #helpCentreDropDown {
    margin-right: 7px !important;
    max-width: 589px !important;
    right: -15px !important;
}

#helpCentreDropDown:before {
    bottom: 100%;
    left: 94%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-color: rgba(238,238,238,0);
    border-width: 10px;
    margin-left: -10px;
    border-bottom-color: #1B2B6A !important;
    z-index: 99;
}

#collapsibleDropdownHelpcentreCollapse0 p {
    padding-left: 29px;
    font-size: 14px;
    color: #000000;
    letter-spacing: 0px;
    line-height: 19px;
}

.helpcentre-menu-profile-kommune a {
    letter-spacing: 0px;
    line-height: 19px;
    font-family: semiFont, 'Open Sans Semibold', sans-serif !important;
    text-decoration: underline;
}

    .helpcentre-menu-profile-kommune a:hover {
        color: #000 !important;
    }

.opensans-normal {
    font-size: 14px;
    color: #1B2B6A;
    font-family: semiFont, 'Open Sans', sans-serif !important;
    letter-spacing: 0px;
    line-height: 19px;
}

#helpcentre-dropdown-contact div {
    margin-bottom: 3px;
}

.help-centre-img {
    padding: 7px !important;
}

.help-centre-content {
    padding: 15px 0 0 0 !important;
}

#collapsibleDropdownHelpcentreParent .card-header ::after {
    flex-shrink: 0;
    width: 1.25rem;
    height: 1.25rem;
    margin-left: auto;
    content: "";
    background-image: url('../images/expand.svg');
    background-size: 1.25rem;
    transition: transform .2s ease-in-out;
}

#collapsibleDropdownHelpcentreParent .card-header:not(.collapsed)::after {
    background-image: url('../images/expand.svg');
    transform: rotate(90deg);
}


#menuDropDashBoard {
    cursor: default;
    padding: 12px 16px;
}
/*Old menu styles starts*/
#userDropdownlistIcon li {
    list-style: none;
}


    .budchange-toolbar-add, #userDropdownlistIcon li a:hover {
        background: none !important;
    }

#userDropdownlistIcon .dropdown-menu.extended {
    left: -258px !important;
    position: absolute;
    border-radius: 20px !important;
}

#userLoginDetail #userDropdownlistIcon .dropdown-menu.extended {
    left: -255px !important;
}

#userDropdownlistIcon {
    margin: 0px !important;
    padding: 0px !important;
}

.dropdown-menu.extended.logoutSec li a {
    padding: 10px;
}

.top-user-menu-padding {
    margin-left: 15px;
}

#headerOrgSelectorSection {
    width: 46%;
}

@media (min-width: 1000px) {
    #headerOrgSelectorSection ul {
        margin-right: 15px;
    }

    #monthlyReportSpecificMesg #menuNewsCount {
        left: 34px !important;
    }

    #notificationCount {
        left: 18px;
    }
}

@media (min-width: 1300px) {
    #headerOrgSelectorSection ul {
        margin-right: 10px;
    }
}

.user-img-style {
    height: 33px;
    width: 33px;
}

.dropdown-menu.extended li a {
    font-size: 12px;
    list-style: none;
}

@media (max-width: 1000px) {
    #headerMenuOrgDropDownOld {
        width: 230px !important;
        left: -185px !important;
        top: 45px !important;
    }
}

@media (min-width: 1200px) {
    #headerMenuOrgDropDownOld {
        width: 230px !important;
        left: -184px !important;
        top: 45px !important;
    }
}

@media (min-width: 1300px) {
    #headerMenuOrgDropDownOld {
        width: 230px !important;
        left: -175px !important;
        top: 45px !important;
    }
}

@media (min-width: 1400px) {
    #headerMenuOrgDropDownOld {
        width: 230px !important;
        left: -144px !important;
        top: 45px !important;
    }
}

.top-user-menu-old {
    margin-left: 15px;
}

.org-item-active {
    font-family: semiFont, 'Open Sans Semibold', sans-serif !important;
    color: #367B9A !important;
}

.org-structure-tree .org-item-active {
    color: #fff !important;
}

#mainHeaderOldVersion .dropdown-menu.extended li a {
    font-size: 12px;
    list-style: none;
}


.org-submenu-style-old {
    border-left: none;
    border-right: none;
    color: #000;
    border-bottom: none !important;
}

#headerMenuOrgDropDownOld:before {
    bottom: 100%;
    left: 90%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-color: rgba(238,238,238,0);
    border-width: 10px;
    margin-left: -8px;
    border-bottom-color: #FFFFFF !important;
    z-index: 99;
}

#mainMenuOrgLevelNameOld {
    white-space: nowrap;
    overflow: hidden;
}

.logout-button {
    background-color: #347B9A;
    color: #fff;
    border: none;
    border-radius: 7px;
    width: 25%;
    height: 18px;
    margin-right: 10px;
}

    .logout-button:hover, .logout-button:focus {
        color: #fff !important;
        outline: none !important;
        border: none !important;
    }

.cancel-logout {
    border-radius: 7px;
    width: 24%;
    height: 16px;
    background-color: #fff;
    border: 1px solid;
}

    .cancel-logout:hover, .cancel-logout:focus {
        color: black !important;
        outline: none !important;
    }

.logout-content-background {
    background-color: white !important;
}


.hover-leftMenu:hover {
    background-color: #EFF5FA;
    font-weight: 600;
}

.main-content {
    position: relative;
    top: 55px;
}

.wcag {
    height: 640px;
}

.active-leftMenu {
    background-color: #EFF5FA;
    font-weight: 600;
}

.bg-banner {
    background-color: #EFF5FA;
}

.pos {
    position: fixed;
    z-index: 1;
}

.publishErrorWarning-bg {
    background-color: #FCF7F7;
    border-radius: 4px;
}

.textTitle-wcag {
    color: #000000;
}

.budget-integration-error-warning {
    position: relative;
    max-height: 134px;
    overflow-y: auto;
}

.margin-left-10-rem {
    margin-left: -9.9rem;
}

.justification-border-box {
    box-sizing: border-box !important;
}

.editable-info-text {
    background-color: #ECF7FF;
    margin-right: 10px;
    padding: 8px;
    font-size: 13px;
}

#relationValuesGrid .k-grid-header th {
    border: none;
}

#relationValuesGrid .k-filter-row th {
    border-bottom: none;
    background: #ebebeb;
    width: 100% !important
}

#relationValuesGrid .k-master-row {
    height: 50px;
}

#relationValuesGrid .k-operator-hidden {
    width: 100% !important
}

.attribute-type {
    width: 89% !important;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    border: 1px solid #6FA1B2;
    padding: 5px 10px 5px 15px;
    border-radius: 4px;
    display: inline-block;
    height: 20px;
    cursor: pointer;
}

#relationValuesGrid div.k-grid-header {
    padding-right: 0px !important;
}

.dep-search {
    padding: 4px 25px;
    min-width: 8px;
    border-radius: 4px;
}

#relationValuesGrid tr td .k-widget {
    width: 99% !important;
}

#changeDimensionGrid .k-grid-content, #newDimensionGrid .k-grid-content {
    overflow-y: auto !important;
    overflow-x: scroll;
}

#changeDimensionGrid .k-grid-header tr th, #newDimensionGrid .k-grid-header tr th {
    border: none !important;
}

.justification-text {
    background-color: #FFED9F;
    margin-right: 10px;
    padding: 5px;
    font-size: 13px;
}

.justification-textarea {
    width: 100%;
    background-color: #fff;
    background-image: none;
    border: 1px solid #6fa1b2 !important;
    border-radius: 4px;
    padding: 5px;
}

#adminAccountImportWrapper .drag-to-left {
    margin-left: -40px;
}

#adminAccountImportWrapper .change-report-type-text {
    font-size: 18px;
    padding-right: 10px;
    font-weight: 600;
}

.font-bold-important {
    font-weight: bold !important;
}

.tab-section-budget-integration {
    border: none;
    padding: 10px;
    padding-left: 13px !important;
    padding-bottom: 0px;
}

.width-24 {
    width: 24%;
}

#messageContents .k-loading-color {
    border-radius: 20px;
}

#kostraGroupTags {
    display: none !important;
}

#resultDivBudgetIntegration {
    background: #F1F2F7;
}

#tenantCombobox-list {
    padding: 0px;
    border: 1px solid #c3c3c3;
    z-index: 10;
    background-color: white;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

#tenantCombobox_listbox > .k-item {
    font-size: 14px;
    cursor: pointer;
    padding-left: 16px;
    padding-top: 10px;
}

#tenantCombobox_listbox > .k-state-selected {
    background-color: #006acb !important;
    margin-bottom: 0px !important;
    border-color: #006acb !important;
}

.width28{
    width: 28% !important;
}

/*Multi client login screen css*/

.mc-login-wrapper .login-box-content {
    min-width: 400px;
    width: auto;
}

.mc-dropdown-section label,
.mc-dropdown-section .mc-login-dropdown {
    width: 250px;
    text-align: left;
}

.mc-dropdown-section .mc-login-dropdown,
.mc-dropdown-section .k-combobox .k-dropdown-wrap,
.mc-dropdown-section .k-combobox .k-select {
    border-radius: 20px !important;
}

    .mc-dropdown-section .k-combobox .k-select:hover,
    .mc-dropdown-section .k-combobox:hover,
    .mc-dropdown-section .k-combobox .k-dropdown-wrap:hover,
    .mc-dropdown-section .k-combobox .k-input.mc-login-dropdown {
        background: #fff;
    }

.mc-login-wrapper {
    position: absolute;
    transform: translate(24vw, 28vh);
}

.mc-dropdown-section .mc-login-dropdown.k-state-border-down,
.mc-dropdown-section .mc-login-dropdown .k-dropdown-wrap.k-state-active.k-state-border-down {
    background: #fff;
    border-radius: 20px 20px 0 0 !important;
}

.mc-login-dropdown .k-icon.k-clear-value.k-i-close,
.mc-login-dropdown .k-icon.k-clear-value.k-i-close:hover {
    display: none;
    content: "";
}
.help-text-box {
    border: 1px solid #cacaca !important;
    max-height: 200px;
    width: 96%;
    box-shadow: 0px 3px 6px rgba(0, 0, 0, .16);
    background-color: white;
    border-radius: 6px !important;
    font-size: small;
    padding-top: 5px;
    margin: 5px 0px 5px 14px;
}
.alert-image {
    margin: 1px;
    padding-right: 7px;
    float: left;
    height: 42px;
    width: 30px;
}

.display-flex-center {
    display: flex;
    justify-content: center;
}
.k-list-container .k-list-scroller, .k-list-container .k-list {
    overflow-y: auto !important;
    touch-action: auto !important;
}

.display-flex-end {
    display: flex;
    justify-content: flex-end;
}

.background-transparent {
    background-color: transparent;
}