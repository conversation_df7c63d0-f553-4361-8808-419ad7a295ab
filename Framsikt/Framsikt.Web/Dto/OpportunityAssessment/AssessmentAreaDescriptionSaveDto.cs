#pragma warning disable CS8618

using Framsikt.Web.Core.Dto.Common;

namespace Framsikt.Web.Core.Dto.OpportunityAssessment
{
    public class AssessmentAreaDescriptionSaveDto
    {
        public Guid assessmentId { get; set; }
        public Guid assessmentAreaId { get; set; }
        public string assessmentAreaName { get; set; }
        public int budgetYear { get; set; }
        public string orgId { get; set; }
        public int orgLevel { get; set; }
        public string serviceId { get; set; }
        public int serviceLevel { get; set; }
        public string assessmentAreaDescription { get; set; }
        public Guid areaDescId { get; set; }
        public List<KeyValuePairDto> columsToDisplay { get; set; }
        public bool logHistory { get; set; }
        public AssessmentAreaDescriptionSaveDto()
        {
            columsToDisplay = new List<KeyValuePairDto>();
        }
    }
}