using Newtonsoft.Json.Linq;

namespace Framsikt.Web.Core.Dto.MonthlyReportBusinessPlan
{
    public class MonthlyRptBusPlanHelperDto
    {
        public Guid id { get; set; }
        public Guid uniqueid { get; set; }
        public string goalTag { get; set; } = string.Empty;
        public string creatorOrgId { get; set; } = string.Empty;
        public string creatorOrgName { get; set; } = string.Empty;
        public int creatorOrgLevel { get; set; }
        public string activityName { get; set; } = string.Empty;
        public string activityDesc { get; set; } = string.Empty;
        public DateTime deadlineDate { get; set; }
        public string responsible { get; set; } = string.Empty;
        public int reportedPeriod { get; set; }
        public int status { get; set; }
        public JObject statusObj { get; set; }
        public int risk { get; set; }
        public JObject riskObj { get; set; }
        public string statusdesc { get; set; } = string.Empty;
        public string statusDescGUID { get; set; } = string.Empty;
        public string category { get; set; } = string.Empty;
        public string categoryDescription { get; set; } = string.Empty;
        public string externalReference { get; set; } = string.Empty;
        public bool isIncludedInMRDoc { get; set; }
        public int owner { get; set; }
        public string ownerName { get; set; } = string.Empty;
        public int busplanStatus { get; set; }
        public Guid climateCategoryId { get; set; }
        public string focusArea { get; set; } = string.Empty;
        public string goal { get; set; } = string.Empty;
        public string unGoal { get; set; } = string.Empty;
        public string target { get; set; } = string.Empty;
        public string unTarget { get; set; } = string.Empty;
        public string unGoalsConnectedToTarget { get; set; } = string.Empty;
        public string strategy { get; set; } = string.Empty;
        public string serviceName { get; set; } = string.Empty;
        public string servicId { get; set; } = string.Empty;
        public string attributeId { get; set; } = string.Empty;
        public string attributeName { get; set; } = string.Empty;
        public MonthlyRptLiveStatusDto liveStatusObject { get; set; } = new MonthlyRptLiveStatusDto();
        public string liveStatusDesc { get; set; } = string.Empty;
        public Guid liveStatusdescGuid { get; set; }
        public string contributors { get; set; } = string.Empty;
        public DateTime updated { get; set; }
        public string updated_by { get; set; } = string.Empty;
        public string motherPlan { get; set; } = string.Empty;
        public List<string> allPlanName { get; set; } = new List<string>();
        public int startYear { get; set; }
        public int endYear { get; set; }
        public int startYearBP { get; set; }
        public int statusFinplan { get; set; }
        public JObject statusFinObj { get; set; } = new JObject();
        public int syncStatus { get; set; }
        public string assignmentId { get; set; } = string.Empty;

        public MonthlyRptBusPlanHelperDto()
        {
            liveStatusObject = new MonthlyRptLiveStatusDto();
            statusObj = new JObject();
            riskObj = new JObject();
            statusFinObj = new JObject();
        }
    }
}