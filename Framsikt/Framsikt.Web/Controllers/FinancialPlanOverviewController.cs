using Framsikt.BL;
using Framsikt.Web.Filters;
using Framsikt.Web.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Framsikt.Web.Controllers
{
    [CustomHandleError(Order = 4)]
    [Logging(Order = 3)]
    
    [Localized(Order = 1)]
    public class FinancialPlanOverviewController : Controller
    {
        // GET: FinancialPlanOverview
        private readonly IApplication _application;

        private readonly IUtility _pUtility;

        public FinancialPlanOverviewController(IApplication app, IUtility util)
        {
            _application = app;
            _pUtility = util;
        }

        public ActionResult FinancialPlanOverview()
        {
            Dictionary<string, string> userInfo = UserHelper.GetUserInfo(_pUtility, UserIdentity.GetName(User));

            Dictionary<string, string> appInfo = _application.GetAppInfo(UserIdentity.GetName(User));
            ViewBag.userDetails = userInfo;
            ViewBag.appDetails = appInfo;
            return View();
        }
    }
}