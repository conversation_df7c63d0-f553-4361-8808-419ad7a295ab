#pragma warning disable CS8600
#pragma warning disable CS8602
#pragma warning disable CS8604

using Aspose.Cells;
using AutoMapper;
using Framsikt.BL;
using Framsikt.BL.Core;
using Framsikt.BL.Core.Helpers;
using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Framsikt.Entities.Core;
using Framsikt.Web.Core.Dto.Investments;
using Framsikt.Web.Core.Dto.CommonImport.AssignmentImport;
using Framsikt.Web.Core.Dto.CommonImport.AdminAlert;
using Framsikt.Web.Core.Dto.CommonImport.ObjectValuesImport;
using Framsikt.Web.Core.Dto.CommonImport.YBBudgetImport;
using Framsikt.Web.Filters;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.IdentityModel.Tokens;
using Microsoft.Net.Http.Headers;
using Newtonsoft.Json.Linq;
using System.Data;
using System.Net;
using System.Reflection;
using static Framsikt.BL.Core.Helpers.ProjectStructureImportHelper;
using Framsikt.Web.Core.Dto.CommonImport.Activity;
using Microsoft.Azure.Amqp.Framing;
using static Microsoft.ApplicationInsights.MetricDimensionNames.TelemetryContext;
using System.Globalization;
using Framsikt.Web.Helpers;

namespace Framsikt.Web.Controllers.api
{
    
    [Logging]
    [CustomWapiError]
    [ApiController]
    public class CommonImportApiController : ControllerBase
    {
        private readonly IUtility _utility;
        private readonly IObjectValuesImport _objectValuesImport;
        private readonly IImportUtility _importUtility;
        private readonly IAssignmentImport _assignmentImport;
        private readonly IInvestmentImportISY _investmentImportISY;
        private readonly IActivityIndicatorImport _activityIndicatorImport;
        private readonly IYBImportBudget _ybImportBudget;
        private readonly IProjectStructureImport _projectStructureImport;
        private readonly IStaffPlanPositionsImport _staffPlanPositionsImport;
        private readonly IBudgetImport _budgetImport;
        private readonly IAdminAlertImport _adminAlertImport;
        private readonly ISalaryForecastImport _salaryForecastImport;
        private readonly IAdminAbsenceAccessImport _adminAbsenceAccessImport;
        private readonly IMapper mapper;

        public CommonImportApiController(IUtility utility, IObjectValuesImport objectValuesImport, IImportUtility importUtility, IAssignmentImport assignmentImport,
            IInvestmentImportISY investmentImportISY, IActivityIndicatorImport activityIndicatorImport, IProjectStructureImport projectStructureImport, 
            IYBImportBudget yBImportBudget, IStaffPlanPositionsImport staffPlanPositionsImport, IBudgetImport budgetImport, IAdminAlertImport adminAlertImport, 
            ISalaryForecastImport salaryForecastImport, IAdminAbsenceAccessImport adminAbsenceAccessImport, IMapper mapper)
        {
            this._utility = utility;
            this._objectValuesImport = objectValuesImport;
            this._importUtility = importUtility;
            _assignmentImport = assignmentImport;
            _investmentImportISY = investmentImportISY;
            _activityIndicatorImport = activityIndicatorImport;
            _projectStructureImport = projectStructureImport;
            _ybImportBudget = yBImportBudget;
            _staffPlanPositionsImport = staffPlanPositionsImport;
            _budgetImport = budgetImport;
            _adminAlertImport = adminAlertImport;
            _salaryForecastImport = salaryForecastImport;
            _adminAbsenceAccessImport = adminAbsenceAccessImport;
            this.mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        #region Common api's

        [Route("api/CommonImportApi/GetImportDropdownData")]
        [HttpGet]
        public async Task<JArray> GetImportDropdownData()
        {
            return JArray.FromObject(await _importUtility.GetImportDropdownData(UserIdentity.GetName(User)));
        }

        [Route("api/CommonImportApi/GetImportDropdownGroupLangstrings")]
        [HttpGet]
        public async Task<List<KeyValueStringPair>> GetImportDropdownGroupLangstrings()
        {
            return await _importUtility.GetImportDropdownGroupLangstrings(UserIdentity.GetName(User));
        }

        [Route("api/CommonImportApi/GetImportProgressStatus")]
        [HttpGet]
        public async Task<TcoJobStatus> GetImportProgressStatus(ImportScreenType importScreenType, int forecastPeriod, long jobId)
        {
            var importScreenTyp = new UserTrackedJobs();
            switch (importScreenType)
            {
                case ImportScreenType.ObjectValuesImport:
                    importScreenTyp = UserTrackedJobs.ObjectValuesImport;
                    break;

                case ImportScreenType.RelationValuesImport:
                    importScreenTyp = UserTrackedJobs.RelationValuesImport;
                    break;

                case ImportScreenType.InvestmentForecastImport:
                    importScreenTyp = UserTrackedJobs.InvestmentForecastImport;
                    break;
                case ImportScreenType.InvestmentStatusImport:
                    importScreenTyp = UserTrackedJobs.InvestmentStatusImport;
                    break;
                case ImportScreenType.YBImportBudget:
                    importScreenTyp = UserTrackedJobs.BudgetImport;
                    break;
                case ImportScreenType.ActivityIndicatorImport:
                    importScreenTyp = UserTrackedJobs.ActivityIndicatorImport;
                    break;
                case ImportScreenType.ProjectStructureImport:
                    importScreenTyp = UserTrackedJobs.ProjectStructureImport;
                    break;
                case ImportScreenType.StaffPlanImport:
                    importScreenTyp = UserTrackedJobs.StaffPlanPositionImport;
                    break;
                case ImportScreenType.AdminAlertImport:
                    importScreenTyp = UserTrackedJobs.AdminAlertImport;
                    break;
                case ImportScreenType.SalaryForecastImport:
                    importScreenTyp = UserTrackedJobs.SalaryForecastImport;
                    break;
                case ImportScreenType.AdminAbsenceAccessImport:
                    importScreenTyp = UserTrackedJobs.AdminAbsenceAccessImport;
                    break;
            }
            return jobId == -1 ? await _utility.GetJobProgressAsync(UserIdentity.GetName(User), importScreenTyp)
                                : await _utility.GetJobProgressByJobIdAsync(UserIdentity.GetName(User), importScreenTyp, jobId);
        }

        [Route("api/CommonImportApi/GetImportAccessApi")]
        [HttpGet]
        public async Task<bool?> GetImportAccessApi(ImportScreenType importScreenType)
        {
            return await _importUtility.GetImportAccessData(UserIdentity.GetName(User),importScreenType);
        }
        #endregion

        [Route("api/CommonImportApi/ObjectList")]
        [HttpGet]
        public async Task<JArray> GetObjectDropdownData()
        {
            return JArray.FromObject(await _objectValuesImport.GetObjectList(UserIdentity.GetName(User)));
        }

        [Route("api/CommonImportApi/ObjectValuesTemplate")]
        [HttpGet]
        public async Task<ActionResult> DownloadObjectValuesTemplate(ImportScreenType importScreenType, bool isPeriodicKey = false,bool isLockBudget=false)
        {
            try
            {
                Thread.CurrentThread.CurrentCulture = CultureInfo.InvariantCulture;
                UserData userData = await _utility.GetUserDetailsAsync(UserIdentity.GetName(User));

                List<ReportColumnHelper> columns = new List<ReportColumnHelper>();
                ColumnInfo columnInfo = new ColumnInfo();
                switch (importScreenType)
                {
                    case ImportScreenType.ObjectValuesImport:
                    case ImportScreenType.RelationValuesImport:
                        columnInfo = importScreenType.Equals(ImportScreenType.ObjectValuesImport) ?
                                                                             await _objectValuesImport.GetColumnInfo(UserIdentity.GetName(User), false) :
                                                                             await _objectValuesImport.GetColumnInfo(UserIdentity.GetName(User), true);
                        break;

                    case ImportScreenType.assignment:
                        columnInfo = await _objectValuesImport.GetAssignmentColumnInfo(UserIdentity.GetName(User));
                        break;

                    case ImportScreenType.InvestmentForecastImport:
                        columnInfo = await _investmentImportISY.GetInvestmentForecastColumnInfo(UserIdentity.GetName(User), false);
                        break;
                    case ImportScreenType.InvestmentStatusImport:
                        columnInfo = await _investmentImportISY.GetInvestmentStatusColumnInfo(UserIdentity.GetName(User), false);
                        break;
                    case ImportScreenType.YBImportBudget:
                        var isVismaConfig = await _ybImportBudget.IsVismaConfig(userData.tenant_id);
                        if (isVismaConfig && !isPeriodicKey)
                        {
                            columnInfo = await _ybImportBudget.GetBudgetImportColumnInfo(UserIdentity.GetName(User), isPeriodicKey, true, false, isLockBudget);
                        }
                        else
                        {
                            columnInfo = await _ybImportBudget.GetBudgetImportColumnInfo(UserIdentity.GetName(User), isPeriodicKey, false, false, isLockBudget);
                        }
                        break;
                    case ImportScreenType.ActivityIndicatorImport:
                        columnInfo = await _activityIndicatorImport.GetActivityIndicatorColumnInfo(UserIdentity.GetName(User));
                        break;
                    case ImportScreenType.ProjectStructureImport:
                        columnInfo = await _projectStructureImport.GetProjectStructureColumnInfo(UserIdentity.GetName(User));

                        break;
                    case ImportScreenType.AdminAlertImport:
                        columnInfo = await _adminAlertImport.GetAdminAlertImportColumnInfo(UserIdentity.GetName(User));
                        break;
                    case ImportScreenType.AdminAbsenceAccessImport:
                        columnInfo = await _adminAbsenceAccessImport.GetAdminAbsenceAccessImportColumnInfo(UserIdentity.GetName(User));
                        break;
                        //other cases depending on the import type.
                }

                for (int i = 0; i < columnInfo.Fields.Count; i++)
                {
                    ReportColumnHelper c = new ReportColumnHelper
                    {
                        ColName = columnInfo.Fields[i],
                        DisplayName = columnInfo.Titles[i],
                        DataType = columnInfo.DataTypes[i]
                    };
                    columns.Add(c);
                }
                using (MemoryStream ms = await _objectValuesImport.GetExcelTemplateData(userData.language_preference, columns, importScreenType, UserIdentity.GetName(User), isPeriodicKey))
                {
                    string fName = await _utility.UploadToStorageAsync(ms, userData);
                    JObject ret = new JObject { { "success", true }, { "fName", fName } };
                    return StatusCode((int)HttpStatusCode.OK, ret);
                }
            }
            catch (Exception)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, "Export Failed");
            }
        }

        [Route("api/CommonImportApi/ObjectValuesGridData")]
        [HttpGet]
        public async Task<JObject> GetObjectValuesImportGridData(int skip, int take, ImportScreenType importScreenType, bool isErrorToggle, long jobId = -1)
        {
            if (importScreenType.Equals(ImportScreenType.ObjectValuesImport))
                return JObject.FromObject(await _objectValuesImport.GetObjectValuesImportStageData(UserIdentity.GetName(User), skip, take, jobId, isErrorToggle));
            else
                return JObject.FromObject(await _objectValuesImport.GetRelationValuesImportStageData(UserIdentity.GetName(User), skip, take, jobId, isErrorToggle));
        }

        [Route("api/CommonImportApi/ObjectValuesGridColumns")]
        [HttpGet]
        public async Task<JArray> GetObjectValuesImportGridColumns(ImportScreenType importScreenType)
        {
            return importScreenType.Equals(ImportScreenType.ObjectValuesImport) ? await GetObjectValuesGridConfig() : await GetRelationValueGridColumn();
        }

        [Route("api/CommonImportApi/UploadObjectValuesTmplData")]
        [HttpPost]
        [DisableFormModelBinding]
        public async Task<JObject> ImportObjectValuesFromTemplate(ImportScreenType importScreenType)
        {
            Workbook wb = null;
            var boundary = HeaderUtilities.RemoveQuotes(MediaTypeHeaderValue.Parse(Request.ContentType).Boundary).Value;
            var reader = new MultipartReader(boundary, Request.Body);
            var section = await reader.ReadNextSectionAsync();
            while (section != null)
            {
                var hasContentDispositionHeader = ContentDispositionHeaderValue.TryParse(section.ContentDisposition, out var contentDisposition);
                if (hasContentDispositionHeader)
                {
                    if (contentDisposition != null && contentDisposition.DispositionType.Equals("form-data") && !string.IsNullOrEmpty(contentDisposition.FileName.Value))
                    {
                        using (var stream = new MemoryStream())
                        {
                            // make sure that body is read from the beginning
                            section.Body.Seek(0, SeekOrigin.Begin);
                            await section.Body.CopyToAsync(stream);
                            wb = new Workbook(stream);
                        }
                    }
                }
                section = await reader.ReadNextSectionAsync();
            }

            if (wb != null)
            {
                if (importScreenType.Equals(ImportScreenType.ObjectValuesImport))
                    await _objectValuesImport.ImportExcelToStagingTable(UserIdentity.GetName(User), wb);
                else
                    await _objectValuesImport.ImportExcelToRelationStagingTable(UserIdentity.GetName(User), wb, importScreenType);
                wb.Dispose();
            }
            JObject ret = new JObject();
            return ret;
        }

        [Route("api/CommonImportApi/UpdateObjectValuesFromExcel")]
        [HttpPost]
        [DisableFormModelBinding]
        public async Task<JObject> UpdateObjectValuesFromExcel(ImportScreenType importScreenType)
        {
            Workbook wb = null;
            var boundary = HeaderUtilities.RemoveQuotes(MediaTypeHeaderValue.Parse(Request.ContentType).Boundary).Value;
            var reader = new MultipartReader(boundary, Request.Body);
            var section = await reader.ReadNextSectionAsync();
            while (section != null)
            {
                var hasContentDispositionHeader = ContentDispositionHeaderValue.TryParse(section.ContentDisposition, out var contentDisposition);
                if (hasContentDispositionHeader)
                {
                    if (contentDisposition != null && contentDisposition.DispositionType.Equals("form-data") && !string.IsNullOrEmpty(contentDisposition.FileName.Value))
                    {
                        using (var stream = new MemoryStream())
                        {
                            // make sure that body is read from the beginning
                            section.Body.Seek(0, SeekOrigin.Begin);
                            await section.Body.CopyToAsync(stream);
                            wb = new Workbook(stream);
                        }
                    }
                }
                section = await reader.ReadNextSectionAsync();
            }

            if (wb != null)
            {
                if (importScreenType.Equals(ImportScreenType.ObjectValuesImport))
                    await _objectValuesImport.UpdateObjectValuesStageFromExcel(UserIdentity.GetName(User), wb);
                else
                    await _objectValuesImport.UpdateRelationValuesStageFromExcel(UserIdentity.GetName(User), wb);
                wb.Dispose();
            }
            JObject ret = new JObject();
            return ret;
        }

        [Route("api/CommonImportApi/ImportWorkflowState")]
        [HttpGet]
        public async Task<string> GetImportWorkflowState(ImportScreenType importScreenType, int forecastPeriod, int budgetYear, string userAdjCode, long jobId, bool isBudgetLock)
        {
            ImportWorkflowState state = await _objectValuesImport.GetWorkflowState(UserIdentity.GetName(User), importScreenType, forecastPeriod, budgetYear, userAdjCode, jobId, isBudgetLock);
            return state.ToString();
        }

        [Route("api/CommonImportApi/Export")]
        [HttpGet]
        public async Task<ActionResult> ExportGridToExcel(ImportScreenType importScreenType, int skip, int take, long jobId)
        {
            try
            {
                UserData userData = await _utility.GetUserDetailsAsync(UserIdentity.GetName(User));
                DataTable data = new DataTable();

                if (importScreenType == ImportScreenType.ObjectValuesImport)
                {
                    data = await _objectValuesImport.GetObjectDataForExport(UserIdentity.GetName(User), skip, take, jobId);
                }
                else
                {
                    data = await _objectValuesImport.GetRelationDataForExport(UserIdentity.GetName(User), skip, take, jobId);
                }

                List<ReportColumnHelper> columns = new List<ReportColumnHelper>();
                ColumnInfo columnInfo = new ColumnInfo();

                columnInfo = importScreenType.Equals(ImportScreenType.ObjectValuesImport) ?
                        await _objectValuesImport.GetColumnInfoForUpdate(UserIdentity.GetName(User)) :
                        await _objectValuesImport.GetRelationColumnInfoForUpdate(UserIdentity.GetName(User));

                for (int i = 0; i < columnInfo.Fields.Count; i++)
                {
                    ReportColumnHelper c = new ReportColumnHelper
                    {
                        ColName = columnInfo.Fields[i],
                        DisplayName = columnInfo.Titles[i],
                        DataType = columnInfo.DataTypes[i]
                    };
                    columns.Add(c);
                }

                using (MemoryStream ms = await _utility.ExportGridToExcel2Async(userData.language_preference, data, columns, UserIdentity.GetName(User)))
                {
                    string fName = await _utility.UploadToStorageAsync(ms, userData);
                    JObject ret = new JObject { { "success", true }, { "fName", fName } };
                    return StatusCode((int)HttpStatusCode.OK, ret);
                }
            }
            catch (Exception)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, "Export Failed");
            }
        }

        [Route("api/CommonImportApi/DeleteAllStagedObjectValues")]
        [HttpDelete]
        public async Task DeleteAllStagedObjectValues(ImportScreenType importScreenType, long jobId)
        {
            await _objectValuesImport.DeleteAllStagedObjectValues(UserIdentity.GetName(User), importScreenType, jobId);
        }

        [Route("api/CommonImportApi/DeleteStagedObjectValuesByIds")]
        [HttpPost]
        public async Task DeleteStagedObjectValuesByIds([FromBody] DeleteAssignmentsChecked obj, ImportScreenType importScreenType)
        {
            await _objectValuesImport.DeleteStagedObjectValuesByIds(UserIdentity.GetName(User), obj.Ids, importScreenType);
        }

        [Route("api/CommonImportApi/UpdateRelationObjects")]
        [HttpPost]
        public async Task UpdateRelationObjects([FromBody] List<UpdateRelationObjectsDto> rowsToUpdate, long jobId)
        {
            var updatedRows = mapper.Map<List<RelationValuesImportGridHelper>>(rowsToUpdate);
            foreach (var item in updatedRows)
            {
                item.error_count = 0;
            }
            await _objectValuesImport.UpdateRelationValues(UserIdentity.GetName(User), updatedRows, jobId);
        }

        [Route("api/CommonImportApi/UpdateObjectValuesGridData")]
        [HttpPost]
        public async Task UpdateObjectValuesGridData(List<UpdtObjValGridDTO> data, long jobId)
        {
            var inputData=mapper.Map<List<AttributeValuesImportGridHelper>>(data);
            await _objectValuesImport.UpdateImportedObjectValuesData(UserIdentity.GetName(User), inputData, jobId);
        }

        [Route("api/CommonImportApi/StartImport")]
        [HttpGet]
        public async Task StartObjectValuesImport(string objectName, ImportScreenType importScreenType, long jobId = -1)
        {
            if (importScreenType.Equals(ImportScreenType.ObjectValuesImport))
                await _objectValuesImport.WriteToObjectValuesImportQueue(UserIdentity.GetName(User), objectName, jobId);
            else
                await _objectValuesImport.WriteToRelationValuesImportQueue(UserIdentity.GetName(User), jobId);
        }

        [Route("api/CommonImportApi/Info")]
        [HttpGet]
        public async Task<ImportInfo> GetImportInfo(ImportScreenType importScreenType, int forecastPeriod, long jobId = -1)
        {
            ImportInfo importInfo = await _objectValuesImport.GetImportInfo(UserIdentity.GetName(User), importScreenType, forecastPeriod, jobId);
            return importInfo;
        }

        [Route("api/CommonImportApi/GetTcoJobStatusTransactionsForObjects")]
        [HttpGet]
        public async Task<List<TcoJobsHelper>> GetTcoJobStatusTransactionsForObjects()
        {
            return await _objectValuesImport.GetTcoJobStatusTransactions(UserIdentity.GetName(User), UserTrackedJobs.ObjectValuesImport);
        }

        [Route("api/CommonImportApi/GetTcoJobStatusTransactionsForRelations")]
        [HttpGet]
        public async Task<List<TcoJobsHelper>> GetTcoJobStatusTransactionsForRelations()
        {
            return await _objectValuesImport.GetTcoJobStatusTransactions(UserIdentity.GetName(User), UserTrackedJobs.RelationValuesImport);
        }

        #region Assignment Import API

        [Route("api/CommonImportApi/UploadAssignmentDataExcel")]
        [HttpPost]
        [DisableFormModelBinding]
        public async Task<JObject> UploadAssignmentDataExcel(int budgetYear)
        {
            Workbook wb = null;
            var boundary = HeaderUtilities.RemoveQuotes(MediaTypeHeaderValue.Parse(Request.ContentType).Boundary).Value;
            var reader = new MultipartReader(boundary, Request.Body);
            var section = await reader.ReadNextSectionAsync();
            while (section != null)
            {
                var hasContentDispositionHeader = ContentDispositionHeaderValue.TryParse(section.ContentDisposition, out var contentDisposition);
                if (hasContentDispositionHeader)
                {
                    if (contentDisposition != null && contentDisposition.DispositionType.Equals("form-data") && !string.IsNullOrEmpty(contentDisposition.FileName.Value))
                    {
                        using (var stream = new MemoryStream())
                        {
                            // make sure that body is read from the beginning
                            section.Body.Seek(0, SeekOrigin.Begin);
                            await section.Body.CopyToAsync(stream);
                            wb = new Workbook(stream);
                        }
                    }
                }
                section = await reader.ReadNextSectionAsync();
            }

            if (wb != null)
            {
                await _assignmentImport.ImportExcelToAssignmentStagetTable(UserIdentity.GetName(User), budgetYear, wb);
                wb.Dispose();
            }
            JObject ret = new JObject();
            return ret;
        }

        [Route("api/CommonImportApi/GetAssignmentImportGridColumns")]
        [HttpGet]
        public async Task<JArray> GetAssignmentImportGridColumns()
        {
            return await GetAssignmentImportGridColumn();
        }

        [Route("api/CommonImportApi/GetAssignmentImportGridData")]
        [HttpGet]
        public async Task<AssignmentImportDataHelper> GetAssignmentImportGridData(int skip, int take, bool isErrorToggle, long jobId)
        {
            return await _assignmentImport.GetAssignmentImportGridData(UserIdentity.GetName(User), skip, take, isErrorToggle, jobId);
        }

        [Route("api/CommonImportApi/GetModuleDropdownData")]
        [HttpGet]
        public async Task<List<ModuleDropdownData>> GetModuleDropdownData()
        {
            return await _assignmentImport.GetModuleDropdownData(UserIdentity.GetName(User));
        }

        [Route("api/CommonImportApi/GetAssignmentImportInfo")]
        [HttpGet]
        public async Task<ImportInfo> GetAssignmentImportInfo(long jobId = -1)
        {
            ImportInfo importInfo = await _assignmentImport.GetAssignmentImportInfo(UserIdentity.GetName(User), jobId);
            return importInfo;
        }

        [Route("api/CommonImportApi/AssignmentImportWorkflowState")]
        [HttpGet]
        public async Task<string> AssignmentImportWorkflowState(long jobId)
        {
            ImportWorkflowState state = await _assignmentImport.GetAssignmentWorkflowState(UserIdentity.GetName(User), jobId);
            return state.ToString();
        }

        [Route("api/CommonImportApi/StartAssignmentImport")]
        [HttpGet]
        public async Task StartAssignmentImport(int budgetYear, bool isBudgetProposal, long jobId = -1)
        {
            await _assignmentImport.WriteToAssignmentImportQueue(UserIdentity.GetName(User), jobId, budgetYear, isBudgetProposal);
        }

        [Route("api/CommonImportApi/DeleteStagedAssignmentByIds")]
        [HttpPost]
        public async Task DeleteStagedAssignmentByIds([FromBody] DeleteAssignmentsChecked obj)
        {
            await _assignmentImport.DeleteStagedAssignmentByIds(UserIdentity.GetName(User), obj.Ids);
        }

        [Route("api/CommonImportApi/UpdateAssignmentStagedDataFromExcel")]
        [HttpPost]
        [DisableFormModelBinding]
        public async Task<JObject> UpdateAssignmentStagedDataFromExcel(int budgetYear)
        {
            Workbook wb = null;
            var boundary = HeaderUtilities.RemoveQuotes(MediaTypeHeaderValue.Parse(Request.ContentType).Boundary).Value;
            var reader = new MultipartReader(boundary, Request.Body);
            var section = await reader.ReadNextSectionAsync();
            while (section != null)
            {
                var hasContentDispositionHeader = ContentDispositionHeaderValue.TryParse(section.ContentDisposition, out var contentDisposition);
                if (hasContentDispositionHeader)
                {
                    if (contentDisposition != null && contentDisposition.DispositionType.Equals("form-data") && !string.IsNullOrEmpty(contentDisposition.FileName.Value))
                    {
                        using (var stream = new MemoryStream())
                        {
                            // make sure that body is read from the beginning
                            section.Body.Seek(0, SeekOrigin.Begin);
                            await section.Body.CopyToAsync(stream);
                            wb = new Workbook(stream);
                        }
                    }
                }
                section = await reader.ReadNextSectionAsync();
            }

            if (wb != null)
            {
                await _assignmentImport.UpdateAssignmentStageFromExcel(UserIdentity.GetName(User), budgetYear, wb);
                wb.Dispose();
            }
            JObject ret = new JObject();
            return ret;
        }

        [Route("api/CommonImportApi/ExportAssignmentGrid")]
        [HttpGet]
        public async Task<ActionResult> ExportAssignmentGrid(int skip, int take, long jobId)
        {
            try
            {
                UserData userData = await _utility.GetUserDetailsAsync(UserIdentity.GetName(User));
                DataTable data = await _assignmentImport.GetAssignmentDataForExport(UserIdentity.GetName(User), skip, take, jobId);

                List<ReportColumnHelper> columns = new List<ReportColumnHelper>();
                ColumnInfo columnInfo = new ColumnInfo();

                columnInfo = await _assignmentImport.GetAssignmentInfoForUpdate(UserIdentity.GetName(User));

                for (int i = 0; i < columnInfo.Fields.Count; i++)
                {
                    ReportColumnHelper c = new ReportColumnHelper
                    {
                        ColName = columnInfo.Fields[i],
                        DisplayName = columnInfo.Titles[i],
                        DataType = columnInfo.DataTypes[i]
                    };
                    columns.Add(c);
                }

                using (MemoryStream ms = await _utility.ExportGridToExcel2Async(userData.language_preference, data, columns, UserIdentity.GetName(User)))
                {
                    string fName = await _utility.UploadToStorageAsync(ms, userData);
                    JObject ret = new JObject { { "success", true }, { "fName", fName } };
                    return StatusCode((int)HttpStatusCode.OK, ret);
                }
            }
            catch (Exception)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, "Export Failed");
            }
        }

        [Route("api/CommonImportApi/DeleteAllStagedAssignmentData")]
        [HttpDelete]
        public async Task DeleteAllStagedAssignmentData(long jobId)
        {
            await _assignmentImport.DeleteAllStagedAssignmentData(UserIdentity.GetName(User), jobId);
        }

        [Route("api/CommonImportApi/UpdateAssignmentStageData")]
        [HttpPost]
        public async Task UpdateAssignmentStageData([FromBody] List<UpdateAssignmentStageDataDto> rowsToUpdate, int budgetYear, long jobId)
        {
            var rowsToUpdateMapped = mapper.Map<List<AssignmentImportGridDataHelper>>(rowsToUpdate);
            foreach (var row in rowsToUpdateMapped)
            {
                row.error_count = 0;
            }
            await _assignmentImport.UpdateImportedAssignmentStageData(UserIdentity.GetName(User), rowsToUpdateMapped, budgetYear, jobId);
        }

        [Route("api/CommonImportApi/GetAssignmentImportProgressStatus")]
        [HttpGet]
        public async Task<TcoJobStatus> GetAssignmentImportProgressStatus(long jobId)
        {
            return jobId == -1 ? await _utility.GetJobProgressAsync(UserIdentity.GetName(User), UserTrackedJobs.AssignmentImport)
                                : await _utility.GetJobProgressByJobIdAsync(UserIdentity.GetName(User), UserTrackedJobs.AssignmentImport, jobId);
        }

        [Route("api/CommonImportApi/GetJobIdsAssignmentImport")]
        [HttpGet]
        public async Task<List<TcoJobsHelper>> GetJobIdsAssignmentImport(int budgetYear)
        {
            var jobIds = await _assignmentImport.GetJobIdsAssignmentimport(UserIdentity.GetName(User), budgetYear);
            return jobIds;
        }     

        [Route("api/CommonImportApi/GetSelectedModuleForJob")]
        [HttpGet]
        public async Task<bool?> GetSelectedModuleForJob(long jobId)
        {
            var isBudgetProposal = await _assignmentImport.GetSelectedModuleForJob(UserIdentity.GetName(User), jobId);
            return isBudgetProposal;
        }
        #endregion Assignment Import API

        #region Investment Forecast Import API
        [Route("api/CommonImportApi/GetTcoJobIdsInvestmentForecast")]
        [HttpGet]
        public async Task<List<TcoJobsHelper>> GetTcoJobIdsInvestmentForecast()
        {
            return await _objectValuesImport.GetTcoJobStatusTransactions(UserIdentity.GetName(User), UserTrackedJobs.InvestmentForecastImport);
        }
        [Route("api/CommonImportApi/ExportInvestmentForecastGrid")]
        [HttpGet]
        public async Task<ActionResult> ExportInvestmentForecastGrid(int forecastPeriod, int skip, int take, long jobId)
        {
            try
            {
                UserData userData = await _utility.GetUserDetailsAsync(UserIdentity.GetName(User));
                DataTable data = await _investmentImportISY.GetInvestmentForecastForExport(UserIdentity.GetName(User), forecastPeriod, skip, take, jobId);

                List<ReportColumnHelper> columns = new List<ReportColumnHelper>();
                ColumnInfo columnInfo = new ColumnInfo();

                columnInfo = await _investmentImportISY.GetInvestmentForecastColumnInfo(UserIdentity.GetName(User), true);

                for (int i = 0; i < columnInfo.Fields.Count; i++)
                {
                    ReportColumnHelper c = new ReportColumnHelper
                    {
                        ColName = columnInfo.Fields[i],
                        DisplayName = columnInfo.Titles[i],
                        DataType = columnInfo.DataTypes[i]
                    };
                    columns.Add(c);
                }

                using (MemoryStream ms = await _utility.ExportGridToExcel2Async(userData.language_preference, data, columns, UserIdentity.GetName(User)))
                {
                    string fName = await _utility.UploadToStorageAsync(ms, userData);
                    JObject ret = new JObject { { "success", true }, { "fName", fName } };
                    return StatusCode((int)HttpStatusCode.OK, ret);
                }
            }
            catch (Exception)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, "Export Failed");
            }
        }
        [Route("api/CommonImportApi/GetInvestmentForecastImportGridColumns")]
        [HttpGet]
        public async Task<JArray> GetInvestmentForecastImportGridColumns(int budgetYear)
        {
            return await GetInvestmentForecastImportGridColumn(budgetYear);
        }

        [Route("api/CommonImportApi/UploadInvestmentForecastDataExcel")]
        [HttpPost]
        [DisableFormModelBinding]
        public async Task<JObject> UploadInvestmentForecastDataExcel([FromQuery] int forecastPeriod)
        {
            Workbook wb = null;
            var boundary = HeaderUtilities.RemoveQuotes(MediaTypeHeaderValue.Parse(Request.ContentType).Boundary).Value;
            var reader = new MultipartReader(boundary, Request.Body);
            var section = await reader.ReadNextSectionAsync();
            while (section != null)
            {
                var hasContentDispositionHeader = ContentDispositionHeaderValue.TryParse(section.ContentDisposition, out var contentDisposition);
                if (hasContentDispositionHeader)
                {
                    if (contentDisposition != null && contentDisposition.DispositionType.Equals("form-data") && !string.IsNullOrEmpty(contentDisposition.FileName.Value))
                    {
                        using (var stream = new MemoryStream())
                        {
                            // make sure that body is read from the beginning
                            section.Body.Seek(0, SeekOrigin.Begin);
                            await section.Body.CopyToAsync(stream);
                            wb = new Workbook(stream);
                        }
                    }
                }
                section = await reader.ReadNextSectionAsync();
            }

            if (wb != null)
            {
                await _investmentImportISY.ImportExcelToInvestmentForecastStagetTable(UserIdentity.GetName(User), wb, forecastPeriod);
                wb.Dispose();
            }
            JObject ret = new JObject();
            return ret;
        }

        [Route("api/CommonImportApi/GetInvestmentForecastImportGridData")]
        [HttpGet]
        public async Task<InvestmentForecastImportDataHelper> GetInvestmentForecastImportGridData(int forecastPeriod, int skip, int take, bool isErrorToggle, long jobId)
        {
            return await _investmentImportISY.GetInvestmentForecastImportGridData(UserIdentity.GetName(User), forecastPeriod, skip, take, isErrorToggle, jobId);
        }

        [Route("api/CommonImportApi/StartInvestmentForecastImport")]
        [HttpGet]
        public async Task StartInvestmentForecastImport(string objectName, int budgetYear, int forecastPeriod, long jobId = -1)
        {
            await _investmentImportISY.WriteToInvForecastImportQueue(UserIdentity.GetName(User), jobId, budgetYear, forecastPeriod);
        }

        [Route("api/CommonImportApi/UpdateInvestmentForecastStageData")]
        [HttpPost]
        public async Task UpdateInvestmentForecastStageData([FromBody] List<InvestmentForecastImportGridDataDTO> rowsToUpdate, long jobId, int forecastPeriod)
        {
            var rows = mapper.Map<List<InvestmentForecastImportGridDataHelper>>(rowsToUpdate);
            await _investmentImportISY.UpdateImportedInvestmentForecastStageData(UserIdentity.GetName(User), forecastPeriod, rows, jobId);
        }

        [Route("api/CommonImportApi/UpdateInvestmentForecastStagedDataFromExcel")]
        [HttpPost]
        [DisableFormModelBinding]
        public async Task<JObject> UpdateInvestmentForecastStagedDataFromExcel(int forecastPeriod)
        {
            Workbook wb = null;
            var boundary = HeaderUtilities.RemoveQuotes(MediaTypeHeaderValue.Parse(Request.ContentType).Boundary).Value;
            var reader = new MultipartReader(boundary, Request.Body);
            var section = await reader.ReadNextSectionAsync();
            while (section != null)
            {
                var hasContentDispositionHeader = ContentDispositionHeaderValue.TryParse(section.ContentDisposition, out var contentDisposition);
                if (hasContentDispositionHeader)
                {
                    if (contentDisposition != null && contentDisposition.DispositionType.Equals("form-data") && !string.IsNullOrEmpty(contentDisposition.FileName.Value))
                    {
                        using (var stream = new MemoryStream())
                        {
                            // make sure that body is read from the beginning
                            section.Body.Seek(0, SeekOrigin.Begin);
                            await section.Body.CopyToAsync(stream);
                            wb = new Workbook(stream);
                        }
                    }
                }
                section = await reader.ReadNextSectionAsync();
            }

            if (wb != null)
            {
                await _investmentImportISY.UpdateInvestmentForecastStageFromExcel(UserIdentity.GetName(User), forecastPeriod, wb);
                wb.Dispose();
            }
            JObject ret = new JObject();
            return ret;
        }
        [Route("api/CommonImportApi/UploadInvestmentStatusDataExcel")]
        [HttpPost]
        [DisableFormModelBinding]
        public async Task<JObject> UploadInvestmentStatusDataExcel(int forecastPeriod)
        {
            Workbook wb = null;
            var boundary = HeaderUtilities.RemoveQuotes(MediaTypeHeaderValue.Parse(Request.ContentType).Boundary).Value;
            var reader = new MultipartReader(boundary, Request.Body);
            var section = await reader.ReadNextSectionAsync();
            while (section != null)
            {
                var hasContentDispositionHeader = ContentDispositionHeaderValue.TryParse(section.ContentDisposition, out var contentDisposition);
                if (hasContentDispositionHeader)
                {
                    if (contentDisposition != null && contentDisposition.DispositionType.Equals("form-data") && !string.IsNullOrEmpty(contentDisposition.FileName.Value))
                    {
                        using (var stream = new MemoryStream())
                        {
                            // make sure that body is read from the beginning
                            section.Body.Seek(0, SeekOrigin.Begin);
                            await section.Body.CopyToAsync(stream);
                            wb = new Workbook(stream);
                        }
                    }
                }
                section = await reader.ReadNextSectionAsync();
            }

            if (wb != null)
            {
                await _investmentImportISY.ImportExcelToInvestmentStatusStageTable(UserIdentity.GetName(User), wb, forecastPeriod);
                wb.Dispose();
            }
            JObject ret = new JObject();
            return ret;
        }
        [Route("api/CommonImportApi/UpdateInvestmentStatusStagedDataFromExcel")]
        [HttpPost]
        [DisableFormModelBinding]
        public async Task<JObject> UpdateInvestmentStatusStagedDataFromExcel(int forecastPeriod)
        {
            Workbook wb = null;
            var boundary = HeaderUtilities.RemoveQuotes(MediaTypeHeaderValue.Parse(Request.ContentType).Boundary).Value;
            var reader = new MultipartReader(boundary, Request.Body);
            var section = await reader.ReadNextSectionAsync();
            while (section != null)
            {
                var hasContentDispositionHeader = ContentDispositionHeaderValue.TryParse(section.ContentDisposition, out var contentDisposition);
                if (hasContentDispositionHeader)
                {
                    if (contentDisposition != null && contentDisposition.DispositionType.Equals("form-data") && !string.IsNullOrEmpty(contentDisposition.FileName.Value))
                    {
                        using (var stream = new MemoryStream())
                        {
                            // make sure that body is read from the beginning
                            section.Body.Seek(0, SeekOrigin.Begin);
                            await section.Body.CopyToAsync(stream);
                            wb = new Workbook(stream);
                        }
                    }
                }
                section = await reader.ReadNextSectionAsync();
            }

            if (wb != null)
            {
                await _investmentImportISY.UpdateInvestmentStatusStageFromExcel(UserIdentity.GetName(User), forecastPeriod, wb);
                wb.Dispose();
            }
            JObject ret = new JObject();
            return ret;
        }

        [Route("api/CommonImportApi/ValidateInvestmentForecastData")]
        [HttpGet]
        public async Task ValidateInvestmentForecastData(int forecastPeriod, long jobId = -1)
        {
            await _investmentImportISY.ValidateImportedInvestmentForecastDataAsync(UserIdentity.GetName(User), forecastPeriod, jobId);
        }
        [Route("api/CommonImportApi/DeleteAllStagedInvestmentForecastData")]
        [HttpDelete]
        public async Task DeleteAllStagedInvestmentForecastData(int forecastPeriod, long jobId)
        {
            await _investmentImportISY.DeleteAllStagedInvestmentForecastData(UserIdentity.GetName(User), forecastPeriod, jobId);
        }

        [Route("api/CommonImportApi/GetInvestmentPeriodReport")]
        [HttpGet]
        public async Task<bool?> GetInvestmentPeriodReport(int forecastPeriod)
        {
            return await _investmentImportISY.GetInvestmentPeriodReportAccess(UserIdentity.GetName(User), forecastPeriod);
        }

        [Route("api/CommonImportApi/DeleteStagedInvestmentForecastDataByIds")]
        [HttpPost]
        public async Task DeleteStagedInvestmentForecastDataByIds([FromBody] DeleteProjStructureChecked obj)
        {
            await _investmentImportISY.DeleteStagedInvestmentForecastByIds(UserIdentity.GetName(User), obj.Ids);
        }
        #endregion Investment Forecast Import API

        #region YB Import Budget API
        [Route("api/CommonImportApi/GetYBImportBudgetInfo")]
        [HttpGet]
        public async Task<YBImportGridSumHelper> GetYBImportBudgetInfo(int budgetYear, string userAdjCode, bool isBudgetLock = false)
        {
            var importInfo = await _ybImportBudget.GetYBImportBudgetInfo(UserIdentity.GetName(User), budgetYear, userAdjCode, isBudgetLock);
            return importInfo;
        }

        [Route("api/CommonImportApi/GetYBImportBudgetGridColumns")]
        [HttpGet]
        public async Task<JArray> GetYBImportBudgetGridColumns(bool isPeriodicKey, bool isLockBudget)
        {
            return await GetYBBudgetImportGridColumns(isPeriodicKey,isLockBudget);
        }

        [Route("api/CommonImportApi/GetYBImportBudgetGridData")]
        [HttpGet]
        public async Task<YBBudgetImportDataHelper> GetYBImportBudgetGridData(int skip, int take, int budgetYear, bool isErrorToggle, bool isLockBudget, long jobId, string userAdjCode = "")
        {
            return await _ybImportBudget.GetYBImportBudgetGridData(UserIdentity.GetName(User), skip, take, budgetYear, isErrorToggle, isLockBudget, jobId, userAdjCode);
        }

        [Route("api/CommonImportApi/ExportYBImportBudgetGrid")]
        [HttpGet]
        public async Task<ActionResult> ExportYBImportBudgetGrid(int budgetYear, int skip, int take, bool isPeriodicKey, long jobId, bool isErrorToggle, bool isLockBudget, string userAdjCode = "")
        {
            try
            {
                UserData userData = await _utility.GetUserDetailsAsync(UserIdentity.GetName(User));
                DataTable data = await _ybImportBudget.GetYBImportBudgetsForExport(UserIdentity.GetName(User), budgetYear, skip, take, isPeriodicKey, jobId, isErrorToggle, isLockBudget, userAdjCode);

                List<ReportColumnHelper> columns = new List<ReportColumnHelper>();
                ColumnInfo columnInfo = new ColumnInfo();
                bool isVismaConfig = await _ybImportBudget.IsVismaConfig(userData.tenant_id);
                columnInfo = await _ybImportBudget.GetBudgetImportColumnInfo(UserIdentity.GetName(User), isPeriodicKey, isVismaConfig, true, isLockBudget);

                for (int i = 0; i < columnInfo.Fields.Count; i++)
                {
                    ReportColumnHelper c = new ReportColumnHelper
                    {
                        ColName = columnInfo.Fields[i],
                        DisplayName = columnInfo.Titles[i],
                        DataType = columnInfo.DataTypes[i]
                    };
                    columns.Add(c);
                }

                using (MemoryStream ms = await _utility.ExportGridToExcel2Async(userData.language_preference, data, columns, UserIdentity.GetName(User)))
                {
                    string fName = await _utility.UploadToStorageAsync(ms, userData);
                    JObject ret = new JObject { { "success", true }, { "fName", fName } };
                    return StatusCode((int)HttpStatusCode.OK, ret);
                }
            }
            catch (Exception)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, "Export Failed");
            }
        }

        [Route("api/CommonImportApi/UpdateYBImportBudgetStageData")]
        [HttpPost]
        public async Task UpdateYBImportBudgetStageData([FromBody] List<UpdateYBImportBudgetDTO> rowsToUpdate, [FromQuery] long jobId, [FromQuery] int budgetYear, [FromQuery] bool isPeriodicKey, [FromQuery] string orgId, [FromQuery] int orgLevel, [FromQuery] bool isLockBudget, [FromQuery] string adjustmentCode)
        {
            var data = mapper.Map<List<YBImportBudgetGridDataHelper>>(rowsToUpdate);
            await _ybImportBudget.UpdateYBImportBudgetStageData(UserIdentity.GetName(User), budgetYear, data, isPeriodicKey, orgId, orgLevel, jobId, isLockBudget, adjustmentCode);
        }

        [Route("api/CommonImportApi/StartYBBudgetImport")]
        [HttpPost]
        public async Task StartYBBudgetImport([FromBody] YBImportBudgetInputHelper input)
        {
            await _ybImportBudget.WriteToYBBudgetImportQueue(UserIdentity.GetName(User), input);
        }

        [Route("api/CommonImportApi/UploadBudgetImportDataExcel")]
        [HttpPost]
        [DisableFormModelBinding]
        public async Task<ActionResult> UploadBudgetImportDataExcel([FromQuery] int budgetYear, [FromQuery] string adjustmentCode, [FromQuery] bool isPeriodicKey, [FromQuery] string orgId, [FromQuery] int orgLevel, [FromQuery] bool isLockBudget)
        {
            Workbook wb = null;
            var boundary = HeaderUtilities.RemoveQuotes(MediaTypeHeaderValue.Parse(Request.ContentType).Boundary).Value;
            var reader = new MultipartReader(boundary, Request.Body);
            var section = await reader.ReadNextSectionAsync();
            while (section != null)
            {
                var hasContentDispositionHeader = ContentDispositionHeaderValue.TryParse(section.ContentDisposition, out var contentDisposition);
                if (hasContentDispositionHeader)
                {
                    if (contentDisposition != null && contentDisposition.DispositionType.Equals("form-data") && !string.IsNullOrEmpty(contentDisposition.FileName.Value))
                    {
                        using (var stream = new MemoryStream())
                        {
                            // make sure that body is read from the beginning
                            section.Body.Seek(0, SeekOrigin.Begin);
                            await section.Body.CopyToAsync(stream);
                            wb = new Workbook(stream);
                        }
                    }
                }
                section = await reader.ReadNextSectionAsync();
            }
            if (wb != null)
            {
                var input = new YBImportBudgetUploadHelper()
                {
                    budgetYear = budgetYear,
                    adjustmentCode = adjustmentCode,
                    isPeriodicKey = isPeriodicKey,
                    orgId = orgId,
                    orgLevel = orgLevel
                };
                JObject returnValue = await _ybImportBudget.ImportExcelToYBImportBudgetStagedTable(UserIdentity.GetName(User), wb, input, isLockBudget);
                if (returnValue["Error"]?.ToString() == "True")
                {
                    return StatusCode((int)HttpStatusCode.InternalServerError, "Invalid Template");

                }
                wb.Dispose();
            }
            return StatusCode((int)HttpStatusCode.OK, "Success");
        }

        [Route("api/CommonImportApi/UpdateYBImportStageFromExcel")]
        [HttpPost]
        [DisableFormModelBinding]
        public async Task<JObject> UpdateYBImportStageFromExcel([FromQuery] int budgetYear, [FromQuery] string adjustmentCode, [FromQuery] bool isPeriodicKey, [FromQuery] string orgId, [FromQuery] int orgLevel, [FromQuery] bool isLockBudget)
        {
            Workbook wb = null;
            var boundary = HeaderUtilities.RemoveQuotes(MediaTypeHeaderValue.Parse(Request.ContentType).Boundary).Value;
            var reader = new MultipartReader(boundary, Request.Body);
            var section = await reader.ReadNextSectionAsync();
            while (section != null)
            {
                var hasContentDispositionHeader = ContentDispositionHeaderValue.TryParse(section.ContentDisposition, out var contentDisposition);
                if (hasContentDispositionHeader)
                {
                    if (contentDisposition != null && contentDisposition.DispositionType.Equals("form-data") && !string.IsNullOrEmpty(contentDisposition.FileName.Value))
                    {
                        using (var stream = new MemoryStream())
                        {
                            // make sure that body is read from the beginning
                            section.Body.Seek(0, SeekOrigin.Begin);
                            await section.Body.CopyToAsync(stream);
                            wb = new Workbook(stream);
                        }
                    }
                }
                section = await reader.ReadNextSectionAsync();
            }

            if (wb != null)
            {
                var input = new YBImportBudgetUploadHelper()
                {
                    budgetYear = budgetYear,
                    adjustmentCode = adjustmentCode,
                    isPeriodicKey = isPeriodicKey,
                    orgId = orgId,
                    orgLevel = orgLevel
                };
                await _ybImportBudget.UpdateYBImportStageFromExcel(UserIdentity.GetName(User), input, wb, isLockBudget);
                wb.Dispose();
            }
            JObject ret = new JObject();
            return ret;
        }
        [Route("api/CommonImportApi/DeleteAllStagedImportBudgetData")]
        [HttpDelete]
        public async Task DeleteAllStagedImportBudgetData(long jobId, string userAdjCode)
        {
            await _ybImportBudget.DeleteAllStagedImportBudgetData(UserIdentity.GetName(User), jobId, userAdjCode);
        }
        [Route("api/CommonImportApi/DeleteStagedImportBudgetDataByIds")]
        [HttpPost]
        public async Task DeleteStagedImportBudgetDataByIds([FromBody] DeleteProjStructureChecked obj)
        {
            await _ybImportBudget.DeleteStagedImportBudgetByIds(UserIdentity.GetName(User), obj.Ids);
        }

        [Route("api/CommonImport/ValidateYBImportData")]
        [HttpPost]
        public async Task ValidateYBImportData([FromBody] YBImportBudgetUploadHelper input)
        {
            await _budgetImport.ValidateBudgetImportAsync(UserIdentity.GetName(User), input.budgetYear, input.isPeriodicKey, input.isLockBudget, input.adjustmentCode, input.orgId, input.orgLevel);
        }
        #endregion YB Import Budget API

        #region Investment Status Import API
        [Route("api/CommonImportApi/GetTcoJobIdsInvestmentStatus")]
        [HttpGet]
        public async Task<List<TcoJobsHelper>> GetTcoJobIdsInvestmentStatus()
        {
            return await _objectValuesImport.GetTcoJobStatusTransactions(UserIdentity.GetName(User), UserTrackedJobs.InvestmentStatusImport);
        }
        [Route("api/CommonImportApi/GetInvestmentStatusImportGridColumns")]
        [HttpGet]
        public async Task<JArray> GetInvestmentStatusImportGridColumns()
        {
            return await GetInvestmentStatusImportGridColumn();
        }
        [Route("api/CommonImportApi/GetInvestmentStatusImportGridData")]
        [HttpGet]
        public async Task<InvestmentStatusImportDataHelper> GetInvestmentStatusImportGridData(int forecastPeriod, int skip, int take, bool isErrorToggle, long jobId)
        {
            return await _investmentImportISY.GetInvestmentStatusImportGridData(UserIdentity.GetName(User), forecastPeriod, skip, take, isErrorToggle, jobId);
        }
        [Route("api/CommonImportApi/UpdateInvestmentStatusStageData")]
        [HttpPost]
        public async Task UpdateInvestmentStatusStageData([FromBody] List<InvestmentStatusImportGridDataHelper> rowsToUpdate, long jobId, int forecastPeriod)
        {
            await _investmentImportISY.UpdateImportedInvestmentStatusStageData(UserIdentity.GetName(User), forecastPeriod, rowsToUpdate, jobId);
        }
        [Route("api/CommonImportApi/ExportInvestmentStatusGrid")]
        [HttpGet]
        public async Task<ActionResult> ExportInvestmentStatusGrid(int forecastPeriod, int skip, int take, long jobId)
        {
            try
            {
                UserData userData = await _utility.GetUserDetailsAsync(UserIdentity.GetName(User));
                DataTable data = await _investmentImportISY.GetInvestmentStatusForExport(UserIdentity.GetName(User), forecastPeriod, skip, take, jobId);

                List<ReportColumnHelper> columns = new List<ReportColumnHelper>();
                ColumnInfo columnInfo = new ColumnInfo();

                columnInfo = await _investmentImportISY.GetInvestmentStatusColumnInfo(UserIdentity.GetName(User), true);

                for (int i = 0; i < columnInfo.Fields.Count; i++)
                {
                    ReportColumnHelper c = new ReportColumnHelper
                    {
                        ColName = columnInfo.Fields[i],
                        DisplayName = columnInfo.Titles[i],
                        DataType = columnInfo.DataTypes[i]
                    };
                    columns.Add(c);
                }

                using (MemoryStream ms = await _utility.ExportGridToExcel2Async(userData.language_preference, data, columns, UserIdentity.GetName(User)))
                {
                    string fName = await _utility.UploadToStorageAsync(ms, userData);
                    JObject ret = new JObject { { "success", true }, { "fName", fName } };
                    return StatusCode((int)HttpStatusCode.OK, ret);
                }
            }
            catch (Exception)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, "Export Failed");
            }
        }
        [Route("api/CommonImportApi/StartInvestmentStatusImport")]
        [HttpGet]
        public async Task StartInvestmentStatusImport(string objectName, int budgetYear, int forecastPeriod, long jobId = -1)
        {
            await _investmentImportISY.WriteToInvestmentStatusImportQueue(UserIdentity.GetName(User), jobId, budgetYear, forecastPeriod);
        }
        [Route("api/CommonImportApi/ValidateInvestmentStatusData")]
        [HttpGet]
        public async Task ValidateInvestmentStatusData(int forecastPeriod, long jobId = -1)
        {
            await _investmentImportISY.ValidateImportedInvestmentStatusDataAsync(UserIdentity.GetName(User), forecastPeriod, jobId);
        }
        [Route("api/CommonImportApi/DeleteAllStagedInvestmentStatusData")]
        [HttpDelete]
        public async Task DeleteAllStagedInvestmentStatusData(int forecastPeriod, long jobId)
        {
            await _investmentImportISY.DeleteAllStagedInvestmentStatusData(UserIdentity.GetName(User), forecastPeriod, jobId);
        }
        [Route("api/CommonImportApi/DeleteStagedInvestmentStatusDataByIds")]
        [HttpPost]
        public async Task DeleteStagedInvestmentStatusDataByIds([FromBody] DeleteProjStructureChecked obj)
        {
            await _investmentImportISY.DeleteStagedInvestmentStatusByIds(UserIdentity.GetName(User), obj.Ids);
        }
        #endregion

        #region Activity indicator import

        [Route("api/CommonImportApi/GetActivityIndicatorImportGridData")]
        [HttpGet]
        public async Task<ActivityIndicatorImportDataHelper> GetActivityIndicatorImportGridData(int budgetYear, int skip, int take, bool isErrorToggle, int jobId = -1)
        {
            return await _activityIndicatorImport.GetActivityIndicatorImportGridData(UserIdentity.GetName(User), budgetYear, skip, take, isErrorToggle, jobId);
        }

        [Route("api/CommonImportApi/GetActivityIndicatorImportGridColumns")]
        [HttpGet]
        public async Task<JArray> GetActivityIndicatorImportGridColumns()
        {
            return await GetActivityIndicatorImportGridColumn();
        }

        [Route("api/CommonImportApi/DeleteStagedActivityIndicatorsByIds")]
        [HttpPost]
        public async Task DeleteStagedActivityIndicatorsByIds([FromBody] DeleteAssignmentsChecked deleteObj, int jobId = -1)
        {
            await _activityIndicatorImport.DeleteStagedActivityIndicatorValuesByIds(UserIdentity.GetName(User), deleteObj.Ids, jobId);
        }

        [Route("api/CommonImportApi/DeleteAllStagedActivityIndicators")]
        [HttpDelete]
        public async Task DeleteAllStagedActivityIndicators(int budgetYear, int jobId = -1)
        {
            await _activityIndicatorImport.DeleteAllActivityIndicators(UserIdentity.GetName(User), budgetYear, jobId);

        }

        [Route("api/CommonImportApi/ImportActivityIndicatorFromTemplate")]
        [HttpPost]
        [DisableFormModelBinding]
        public async Task<JObject> ImportActivityIndicatorFromTemplate(int budgetYear)
        {
            Workbook wb = null;
            var boundary = HeaderUtilities.RemoveQuotes(MediaTypeHeaderValue.Parse(Request.ContentType).Boundary).Value;
            var reader = new MultipartReader(boundary, Request.Body);
            var section = await reader.ReadNextSectionAsync();
            while (section != null)
            {
                var hasContentDispositionHeader = ContentDispositionHeaderValue.TryParse(section.ContentDisposition, out var contentDisposition);
                if (hasContentDispositionHeader)
                {
                    if (contentDisposition != null && contentDisposition.DispositionType.Equals("form-data") && !string.IsNullOrEmpty(contentDisposition.FileName.Value))
                    {
                        using (var stream = new MemoryStream())
                        {
                            // make sure that body is read from the beginning
                            section.Body.Seek(0, SeekOrigin.Begin);
                            await section.Body.CopyToAsync(stream);
                            wb = new Workbook(stream);
                        }
                    }
                }
                section = await reader.ReadNextSectionAsync();
            }

            if (wb != null)
            {
                await _activityIndicatorImport.ImportExcelToActivityIndicatorStagetTable(UserIdentity.GetName(User), budgetYear, wb);
                wb.Dispose();
            }
            JObject ret = new JObject();
            return ret;
        }


        [Route("api/CommonImportApi/UpdateImportedActivityIndicatorStageData")]
        [HttpPost]
        public async Task UpdateImportedActivityIndicatorStageData([FromBody] List<UpdActIndImportGridDTO> rowsToUpdate, int budgetYear, long jobId = -1)
        {
            var data = mapper.Map<List<ActivityIndicatorImportGridDataHelper>>(rowsToUpdate);
            foreach (var item in data)
            {
                item.error_count = 0;
            }
            await _activityIndicatorImport.UpdateImportedActivityIndicatorStageData(UserIdentity.GetName(User), data, budgetYear, jobId);
        }


        [Route("api/CommonImportApi/StartActivityIndicatorImport")]
        [HttpGet]
        public async Task StartActivityIndicatorImport(int budgetYear, long jobId = -1)
        {
            await _activityIndicatorImport.WriteToActivityIndicatorImportQueue(UserIdentity.GetName(User), jobId, budgetYear);
        }

        [Route("api/CommonImportApi/ValidateActivityIndicatorData")]
        [HttpGet]
        public async Task ValidateActivityIndicatorData(int budgetYear, int jobId)
        {
            await _activityIndicatorImport.ValidateImportedActivityIndicatorData(UserIdentity.GetName(User), budgetYear, jobId);
        }

        [Route("api/CommonImportApi/GetJobIdsActivityIndicatorImport")]
        [HttpGet]
        public async Task<List<TcoJobsHelper>> GetJobIdsActivityIndicatorImport(int budgetYear)
        {
            var jobIds = await _activityIndicatorImport.GetJobIdsActivityIndicatorimport(UserIdentity.GetName(User), budgetYear);
            return jobIds;
        }

        [Route("api/CommonImportApi/ExportActivityIndicatorGrid")]
        [HttpGet]
        public async Task<ActionResult> ExportActivityIndicatorGrid(int skip, int take, long jobId, int budgetYear)
        {
            try
            {
                UserData userData = await _utility.GetUserDetailsAsync(UserIdentity.GetName(User));
                DataTable data = await _activityIndicatorImport.GetActivityIndicatorDataForExport(UserIdentity.GetName(User), skip, take, jobId, budgetYear);

                List<ReportColumnHelper> columns = new List<ReportColumnHelper>();
                ColumnInfo columnInfo = new ColumnInfo();

                columnInfo = await _activityIndicatorImport.GetActivityIndicatorColumnInfo(UserIdentity.GetName(User));

                for (int i = 0; i < columnInfo.Fields.Count; i++)
                {
                    ReportColumnHelper c = new ReportColumnHelper
                    {
                        ColName = columnInfo.Fields[i],
                        DisplayName = columnInfo.Titles[i],
                        DataType = columnInfo.DataTypes[i]
                    };
                    columns.Add(c);
                }

                using (MemoryStream ms = await _utility.ExportGridToExcel2Async(userData.language_preference, data, columns, UserIdentity.GetName(User)))
                {
                    string fName = await _utility.UploadToStorageAsync(ms, userData);
                    JObject ret = new JObject { { "success", true }, { "fName", fName } };
                    return StatusCode((int)HttpStatusCode.OK, ret);
                }
            }
            catch (Exception)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, "Export Failed");
            }
        }
        [Route("api/CommonImportApi/ActivityIndicatorInfo")]
        [HttpGet]
        public async Task<ImportInfo> ActivityIndicatorInfo(int budgetYear, long jobId = -1)
        {
            ImportInfo importInfo = await _activityIndicatorImport.GetImportInfo(UserIdentity.GetName(User), jobId, budgetYear);
            return importInfo;
        }


        #endregion Activity indicator import

        #region Project Structure Import
        [Route("api/CommonImportApi/UploadProjectStructureTemplate")]
        [HttpPost]
        [DisableFormModelBinding]
        public async Task<JObject> UploadProjectStructureTemplate()
        {
            Workbook wb = null;
            var boundary = HeaderUtilities.RemoveQuotes(MediaTypeHeaderValue.Parse(Request.ContentType).Boundary).Value;
            var reader = new MultipartReader(boundary, Request.Body);
            var section = await reader.ReadNextSectionAsync();
            while (section != null)
            {
                var hasContentDispositionHeader = ContentDispositionHeaderValue.TryParse(section.ContentDisposition, out var contentDisposition);
                if (hasContentDispositionHeader)
                {
                    if (contentDisposition != null && contentDisposition.DispositionType.Equals("form-data") && !string.IsNullOrEmpty(contentDisposition.FileName.Value))
                    {
                        using (var stream = new MemoryStream())
                        {
                            // make sure that body is read from the beginning
                            section.Body.Seek(0, SeekOrigin.Begin);
                            await section.Body.CopyToAsync(stream);
                            wb = new Workbook(stream);
                        }
                    }
                }
                section = await reader.ReadNextSectionAsync();
            }

            if (wb != null)
            {
                await _projectStructureImport.ImportExcelToProjStructureStagingTable(UserIdentity.GetName(User), wb);
                wb.Dispose();
            }
            JObject ret = new JObject();
            return ret;
        }
        [Route("api/CommonImportApi/GetTcoJobIdsProjectStructure")]
        [HttpGet]
        public async Task<List<TcoJobsHelper>> GetTcoJobIdsProjectStructure()
        {
            return await _projectStructureImport.GetJobIdsProjStructureImport(UserIdentity.GetName(User));
        }
        [Route("api/CommonImportApi/GetProjectStructureInfo")]
        [HttpGet]
        public async Task<ImportInfo> GetProjectStructureInfo(long jobId = -1)
        {
            ImportInfo importInfo = await _projectStructureImport.GetImportInfo(UserIdentity.GetName(User), jobId);
            return importInfo;
        }
        [Route("api/CommonImportApi/ProjectStructureImportWorkflowState")]
        [HttpGet]
        public async Task<string> ProjectStructureImportWorkflowState(long jobId)
        {
            ImportWorkflowState state = await _projectStructureImport.GetProjectStructureWorkflowState(UserIdentity.GetName(User), jobId);
            return state.ToString();
        }
        [Route("api/CommonImportApi/UpdateProjectStructureImport")]
        [HttpPost]
        public async Task UpdateProjectStructureImport([FromBody] List<ProjectStructureGridDTO> rowsToUpdate, long jobId = -1)
        {
            var rows = mapper.Map<List<ProjectStructureGridHelper>>(rowsToUpdate);
            await _projectStructureImport.UpdateProjectStructureData(UserIdentity.GetName(User), rows, jobId);
        }
        [Route("api/CommonImportApi/DeleteAllStagedProjStructureData")]
        [HttpDelete]
        public async Task DeleteAllStagedProjStructureData(long jobId)
        {
            await _projectStructureImport.DeleteAllStagedProjStructureData(UserIdentity.GetName(User), jobId);
        }
        [Route("api/CommonImportApi/DeleteStagedProjStructureDataByIds")]
        [HttpPost]
        public async Task DeleteStagedProjStructureDataByIds([FromBody] DeleteProjStructureChecked obj)
        {
            await _projectStructureImport.DeleteStagedProjStructureByIds(UserIdentity.GetName(User), obj.Ids);
        }
        [Route("api/CommonImportApi/GetProjStructureImportGridColumns")]
        [HttpGet]
        public async Task<JArray> GetProjStructureImportGridColumns()
        {
            return await GetProjStructureImportGridColumn();
        }

        [Route("api/CommonImportApi/GetProjStructureImportGridData")]
        [HttpGet]
        public async Task<ProjStructureImportDataHelper> GetProjStructureImportGridData(int skip, int take, bool isErrorToggle, long jobId)
        {
            return await _projectStructureImport.GetProjStructureImportGridData(UserIdentity.GetName(User), skip, take, isErrorToggle, jobId);
        }
        [Route("api/CommonImportApi/UpdateProjStructureDataFromExcel")]
        [HttpPost]
        [DisableFormModelBinding]
        public async Task<JObject> UpdateProjStructureDataFromExcel()
        {
            Workbook wb = null;
            var boundary = HeaderUtilities.RemoveQuotes(MediaTypeHeaderValue.Parse(Request.ContentType).Boundary).Value;
            var reader = new MultipartReader(boundary, Request.Body);
            var section = await reader.ReadNextSectionAsync();
            while (section != null)
            {
                var hasContentDispositionHeader = ContentDispositionHeaderValue.TryParse(section.ContentDisposition, out var contentDisposition);
                if (hasContentDispositionHeader)
                {
                    if (contentDisposition != null && contentDisposition.DispositionType.Equals("form-data") && !string.IsNullOrEmpty(contentDisposition.FileName.Value))
                    {
                        using (var stream = new MemoryStream())
                        {
                            // make sure that body is read from the beginning
                            section.Body.Seek(0, SeekOrigin.Begin);
                            await section.Body.CopyToAsync(stream);
                            wb = new Workbook(stream);
                        }
                    }
                }
                section = await reader.ReadNextSectionAsync();
            }

            if (wb != null)
            {

                await _projectStructureImport.UpdateProjStructureDataStageFromExcel(UserIdentity.GetName(User), wb);
                wb.Dispose();
            }
            JObject ret = new JObject();
            return ret;
        }
        [Route("api/CommonImportApi/ExportProjectStructureGrid")]
        [HttpGet]
        public async Task<ActionResult> ExportProjectStructureGrid(int skip, int take, long jobId)
        {
            try
            {
                UserData userData = await _utility.GetUserDetailsAsync(UserIdentity.GetName(User));
                DataTable data = await _projectStructureImport.GetProjStructureDataForExport(UserIdentity.GetName(User), skip, take, jobId);

                List<ReportColumnHelper> columns = new List<ReportColumnHelper>();
                ColumnInfo columnInfo = new ColumnInfo();

                columnInfo = await _projectStructureImport.GetProjStructureInfoForUpdate(UserIdentity.GetName(User));

                for (int i = 0; i < columnInfo.Fields.Count; i++)
                {
                    ReportColumnHelper c = new ReportColumnHelper
                    {
                        ColName = columnInfo.Fields[i],
                        DisplayName = columnInfo.Titles[i],
                        DataType = columnInfo.DataTypes[i]
                    };
                    columns.Add(c);
                }

                using (MemoryStream ms = await _utility.ExportGridToExcel2Async(userData.language_preference, data, columns, UserIdentity.GetName(User)))
                {
                    string fName = await _utility.UploadToStorageAsync(ms, userData);
                    JObject ret = new JObject { { "success", true }, { "fName", fName } };
                    return StatusCode((int)HttpStatusCode.OK, ret);
                }
            }
            catch (Exception)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, "Export Failed");
            }
        }
        [Route("api/CommonImportApi/ValidateProjectStructureData")]
        [HttpGet]
        public async Task ValidateProjectStructureData(long jobId = -1)
        {
            await _projectStructureImport.ValidateImportedProjectStructureDataAsync(UserIdentity.GetName(User),jobId);
        }
        [Route("api/CommonImportApi/StartProjectStructureImport")]
        [HttpGet]
        public async Task StartProjectStructureImport(long jobId = -1)
        {
            await _projectStructureImport.WriteToProjectStructureImportQueue(UserIdentity.GetName(User), jobId);
        }
        #endregion

        #region Staffplan import

        [Route("api/CommonImportApi/GetPositionsGridData")]
        [HttpGet]
        public async Task<StaffPlanImportDataHelper> GetPositionsGridData(int budgetYear, int skip, int take, bool isErrorToggle, string salaryTableName ,bool isSalaryTableChange)
        {
            return await _staffPlanPositionsImport.GetStaffPlanPositonsData(UserIdentity.GetName(User), budgetYear,skip,take,isErrorToggle, salaryTableName,isSalaryTableChange);
        }
        [Route("api/CommonImportApi/GetStaffplanGridColConfig")]
        [HttpGet]
        public async Task<JArray> GetStaffplanGridColConfig()
        {
            return await GetStaffPlanImportGridColConfig();
        }
        [Route("api/CommonImportApi/GetStaffplanImportInfo")]
        [HttpGet]
        public async Task<ImportInfo> GetStaffplanImportInfo(int budgetYear)
        {
            return await _staffPlanPositionsImport.GetImportInfo(UserIdentity.GetName(User) , budgetYear);
        }
        
        [Route("api/CommonImport/UploadStaffPlanData")]
        [HttpPost]
        [DisableFormModelBinding]
        public async Task<IActionResult> UploadStaffPlanData(int budgetYear, string salaryTable) {
            UploadResult result = null;
            try
            {
                Workbook wb = await GetUploadedData();
                 result = await _staffPlanPositionsImport.UploadStaffPlanPositionsDataAsync(UserIdentity.GetName(User), budgetYear, salaryTable, wb);

                wb.Dispose();

                return result.Success
                    ? Ok(new { Success = true, Message = result.Message, ImportId = result.ImportId })
                    :  StatusCode((int)HttpStatusCode.BadRequest, new
                    {
                        Success = false,
                        Message = result.Message,
                        ValidationErrors = result.ValidationErrors?
                        .SelectMany(dict => dict.Keys)
                        .Distinct()
                        .ToList()
                    });
            }
            catch (Exception ex)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, new { Success = false, Message = $"An error occurred: {ex.Message}" ,
                 ValidationErrors  = result?.ValidationErrors?
                .SelectMany(dict => dict.Keys)
                .Distinct()
                .ToList()
                });
            }
        }


        [Route("api/CommonImport/ValidateStaffPlanEmpIdProcedure")]
        [HttpGet]
        public async Task StaffPlanEmpIdGeneration(int budgetYear, Guid importId, string salaryTableName)
        {
            var useSalaryTableParamValue = await _utility.GetParameterValueAsync(UserIdentity.GetName(User), "USE_SALARY_TABLE");
            int useSalaryTable = useSalaryTableParamValue.ToLower() == "true" ? 1 : 0;
            await _staffPlanPositionsImport.ExecuteSpGenerateEmployementIdProcedureAsync(importId, UserIdentity.GetName(User), budgetYear, useSalaryTable, salaryTableName);
        }

        [Route("api/CommonImport/StaffPlanPositionsTemplate")]
        [HttpGet]
        public async Task<ActionResult> DownloadStaffPlanPositionsTemplate()
        {
            UserData userData = await _utility.GetUserDetailsAsync(UserIdentity.GetName(User));

            List<ReportColumnHelper> columns = new List<ReportColumnHelper>();
            ColumnInfo columnInfo = new ColumnInfo();
            columnInfo = await _staffPlanPositionsImport.GetStaffPlanPositionsColumnInfoAsync(UserIdentity.GetName(User));

            for (int i = 0; i < columnInfo.Fields.Count; i++)
            {
                ReportColumnHelper c = new ReportColumnHelper
                {
                    ColName = columnInfo.Fields[i],
                    DisplayName = columnInfo.Titles[i],
                    DataType = columnInfo.DataTypes[i]
                };
                columns.Add(c);
            }
            using (MemoryStream ms = await _utility.GetExcelTemplateAsync(userData.language_preference, columns, UserIdentity.GetName(User)))
            {
                string fName = await _utility.UploadToStorageAsync(ms, userData);
                JObject ret = new JObject { { "success", true }, { "fName", fName } };
                return StatusCode((int)HttpStatusCode.OK, ret);
            }
            
        }


        [Route("api/CommonImport/StaffPlanPositionsGridData")]
        [HttpGet]
        public async Task<ActionResult> DownloadStaffPlanPositionGridData(int budgetYear, Guid importId, int skip, int take)
        {
            
            UserData userData = await _utility.GetUserDetailsAsync(UserIdentity.GetName(User));
            DataTable data = await _staffPlanPositionsImport.GetStaffPlanPositionsStagedData(UserIdentity.GetName(User), budgetYear, importId, skip, take);

            List<ReportColumnHelper> columns = new List<ReportColumnHelper>();
            ColumnInfo columnInfo = await _staffPlanPositionsImport.GetStaffPlanPositionsColumnInfoAsync(UserIdentity.GetName(User));

            for (int i = 0; i < columnInfo.Fields.Count; i++)
            {
                ReportColumnHelper c = new ReportColumnHelper
                {
                    ColName = columnInfo.Fields[i],
                    DisplayName = columnInfo.Titles[i],
                    DataType = columnInfo.DataTypes[i]
                };
                columns.Add(c);
            }

            using (MemoryStream ms = await _utility.ExportGridToExcel2Async(userData.language_preference, data, columns, UserIdentity.GetName(User)))
            {
                string fName = await _utility.UploadToStorageAsync(ms, userData);
                JObject ret = new JObject { { "success", true }, { "fName", fName } };
                return StatusCode((int)HttpStatusCode.OK, ret);
            }
            
        }

        [Route("api/CommonImport/StaffPlanPositions")]
        [HttpDelete]
        public async Task DeleteStaffplanPositionStagedData(int budgetYear, Guid importId) {
            await _staffPlanPositionsImport.DeleteStaffPlanPositionsStagedData(UserIdentity.GetName(User), budgetYear, importId);
        }

        [Route("api/CommonImport/StaffPlanPositionsBatchOperation")]
        [HttpPost]
        public async Task DeleteStaffplanPositionStagedDataByRow(int budgetYear, Guid importId, [FromBody] DeleteImportChecked Ids)
        {
            await _staffPlanPositionsImport.DeleteStaffPlanPositionsStagedDataByRowId(UserIdentity.GetName(User), budgetYear, importId, Ids.Ids);
        }

        [Route("api/CommonImportApi/StaffPlanPositionsWorkflowState")]
        [HttpGet]
        public async Task<string> GetStaffPlanPositionsWorkflowState( int budgetYear)
        {
            ImportWorkflowState state = await _staffPlanPositionsImport.GetWorkflowState(UserIdentity.GetName(User), budgetYear);
            return state.ToString();
        }

        [Route("api/CommonImportApi/StaffPlanPositionsImport")]
        [HttpGet]
        public async Task StartStaffPlanPositionsImport(int budgetYear, Guid importId)
        {
            await _staffPlanPositionsImport.SendQueueRequestToImportStaffPlanPositions(UserIdentity.GetName(User), budgetYear, importId);
        }
        //insert

        [Route("api/CommonImport/ValidateStaffPlanImport")]
        [HttpGet]
        public async Task ValidateStaffPlanImport(int budgetYear, Guid importId, string salaryTableName)
        {
            await _staffPlanPositionsImport.ValidateImportedData(UserIdentity.GetName(User), budgetYear, importId,salaryTableName);
        }

        [Route("api/CommonImport/SaveStaffPlanStageData")]
        [HttpPost]
        public async Task<string> SaveStaffPlanStageData(int budgetYear, Guid importId, string salaryTableName, [FromBody]List<StaffPlanImportGridInput> data)
        {
            return await _staffPlanPositionsImport.SaveStaffPlanImportStageData(UserIdentity.GetName(User), budgetYear, importId, salaryTableName, data);
        }

        [Route("api/CommonImportApi/StaffPlanWebServiceImport")]
        [HttpGet]
        public async Task GetStaffPlanWebServiceImport(int budgetYear, string salaryTableName)
        {
            await _staffPlanPositionsImport.ImportFromPositionDetails(UserIdentity.GetName(User), budgetYear, salaryTableName);
        }

        #endregion Staffplan import

        #region alert import
        [Route("api/CommonImportApi/ImportAdminAlertDataExcel")]
        [HttpPost]
        [DisableFormModelBinding]
        public async Task ImportAdminAlertDataExcel(AdminAlertSetupTypes importType, string orgVersion, int orgLevel, string orgId, int budgetYear )
        {
            Workbook wb = await GetUploadedData();
            if (wb != null)
            {
                AdminAlertImportHelper input = new AdminAlertImportHelper()
                {
                    orgVersion = orgVersion,
                    orgId = orgId,
                    orgLevel = orgLevel,
                    alertType = importType,
                    budgetYear = budgetYear
                };
                await _adminAlertImport.ImportExcelToAdminAlertStageTable(UserIdentity.GetName(User), wb, input);
                wb.Dispose();
            }
        }

        [Route("api/CommonImportApi/ExportAdminAlertStageGrid")]
        [HttpGet]
        public async Task<ActionResult> ExportAdminAlertStageGrid(int skip, int take, long jobId , ImportScreenType importScreenType = ImportScreenType.AdminAlertImport )
        {
            try
            {
                UserData userData = await _utility.GetUserDetailsAsync(UserIdentity.GetName(User));
                DataTable data = new();
                ColumnInfo columnInfo = new ColumnInfo();
                switch (importScreenType)
                {
                    case ImportScreenType.AdminAlertImport:
                         data = await _adminAlertImport.GetAdminAlertStageDataForExport(UserIdentity.GetName(User), skip, take, jobId);
                         columnInfo = await _adminAlertImport.GetAdminAlertImportColumnInfo(UserIdentity.GetName(User), true);
                        break;
                    case ImportScreenType.AdminAbsenceAccessImport:
                         data = await _adminAbsenceAccessImport.GetAdminAbsenceAccessImportStageDataForExport(UserIdentity.GetName(User),skip, take,jobId);
                         columnInfo = await _adminAbsenceAccessImport.GetAdminAbsenceAccessImportColumnInfo(UserIdentity.GetName(User), true);
                        break;
                }
                
                List<ReportColumnHelper> columns = new List<ReportColumnHelper>();
                
                for (int i = 0; i < columnInfo.Fields.Count; i++)
                {
                    ReportColumnHelper c = new ReportColumnHelper
                    {
                        ColName = columnInfo.Fields[i],
                        DisplayName = columnInfo.Titles[i],
                        DataType = columnInfo.DataTypes[i]
                    };
                    columns.Add(c);
                }

                using (MemoryStream ms = _utility.ExportGridToExcel2(userData.language_preference, data, columns, UserIdentity.GetName(User)))
                {
                    string fName = await _utility.UploadToStorageAsync(ms, userData);
                    JObject ret = new JObject { { "success", true }, { "fName", fName } };
                    return StatusCode((int)HttpStatusCode.OK, ret);
                }
            }
            catch (Exception)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, "Export Failed");
            }
        }

        [Route("api/CommonImportApi/UploadAdminAlertData")]
        [HttpPost]
        [DisableFormModelBinding]
        public async Task UploadAdminAlertData([FromQuery] AdminAlertSetupTypes alertType, [FromQuery] string orgVersion, [FromQuery] int orgLevel, [FromQuery] string orgId, [FromQuery] int budgetYear, [FromQuery] long jobId)
        {
            Workbook wb = await GetUploadedData();
            if (wb != null)
            {
                AdminAlertImportHelper input = new AdminAlertImportHelper()
                {
                    orgVersion = orgVersion,
                    orgId = orgId,
                    orgLevel = orgLevel,
                    alertType = alertType,
                    budgetYear = budgetYear,
                    jobId = jobId
                };
                await _adminAlertImport.ReImportExcelToAdminAlertStageTable(UserIdentity.GetName(User), wb, input);
                wb.Dispose();
            }
        }

        [Route("api/CommonImportApi/GetAdminAlertImportGridColumns")]
        [HttpGet]
        public JArray GetAdminAlertImportGridColumns(ImportScreenType importScreenType = ImportScreenType.AdminAlertImport)
        {
            return GetAdminAlertImportGridColumn(importScreenType);
        }

        [Route("api/CommonImportApi/GetAdminAlertImportGridData")]
        [HttpGet]
        public async Task<AdminAlertImportDataHelper> GetAdminAlertImportGridData(int skip, int take, bool isErrorToggle, long jobId)
        {
            return await _adminAlertImport.GetAdminAlertImportGridData(UserIdentity.GetName(User), skip, take, isErrorToggle, jobId);
        }

        [Route("api/CommonImportApi/UpdateAdminAlertImport")]
        [HttpPost]
        public async Task UpdateAdminAlertImport([FromBody] List<AdmAlertUpdImportGridDTO> rowsToModify,[FromQuery] AdminAlertSetupTypes importType, [FromQuery] string orgVersion, [FromQuery] int orgLevel, [FromQuery] string orgId, [FromQuery] int budgetYear, [FromQuery] long jobId)
        {
            AdminAlertImportHelper input = new AdminAlertImportHelper()
            {
                orgVersion = orgVersion,
                orgId = orgId,
                orgLevel = orgLevel,
                alertType = importType,
                budgetYear = budgetYear
            };
            var data = mapper.Map<List<AdminAlertImportGridDataHelper>>(rowsToModify);
            foreach (var item in data)
            {
                item.error_count = 0;
            }
            List<tal_stage_admin_alert_import> dataToModify = ConvertAdminALertHelperToEntity(data, input);
             await _adminAlertImport.UpdateAdminAlertStageData(dataToModify, input ,UserIdentity.GetName(User), jobId);
        }

        [Route("api/CommonImportApi/StartAdminAlertImport")]
        [HttpGet]
        public async Task StartAdminAlertImport(long jobId, bool delete = false )
        {
            await _adminAlertImport.WriteToAdminAlertImportQueue(UserIdentity.GetName(User),jobId, delete );
        }

        [Route("api/CommonImportApi/DeleteAdminAlertStagedData")]
        [HttpPost]
        public async Task DeleteAdminAlertStagedData(long jobId, int budgetYear, bool isDeleteAll, string orgVersion, AdminAlertSetupTypes setupType, [FromBody] AdminAlertDeleteHelper input)
        {
            await _adminAlertImport.DeleteAdminAlertStagedData(UserIdentity.GetName(User), budgetYear, isDeleteAll, input.Ids, jobId, orgVersion, setupType.ToString());
        }

        [Route("api/CommonImportApi/DeleteAdminAlertStagedAllData")]
        [HttpDelete]
        public async Task DeleteAdminAlertStagedAllData(long jobId, int budgetYear, bool isDeleteAll, string orgVersion, AdminAlertSetupTypes setupType)
        {
            await _adminAlertImport.DeleteAdminAlertStagedData(UserIdentity.GetName(User), budgetYear, isDeleteAll, new List<long>() , jobId, orgVersion, setupType.ToString());;
        }

        [Route("api/CommonImportApi/GetAdminAlertInfo")]
        [HttpGet]
        public async Task<ImportInfo> GetAdminAlertInfo(long jobId)
        {
            ImportInfo importInfo = await _adminAlertImport.GetImportInfo(UserIdentity.GetName(User), jobId);
            return importInfo;
        }

        [Route("api/CommonImportApi/ValidateAdminAlertData")]
        [HttpGet]
        public async Task ValidateAdminAlertData(AdminAlertSetupTypes importType, string orgVersion, int orgLevel, string orgId, int budgetYear, long jobId)
        {
            AdminAlertImportHelper input = new AdminAlertImportHelper()
            {
                orgVersion = orgVersion,
                orgId = orgId,
                orgLevel = orgLevel,
                alertType = importType,
                budgetYear = budgetYear
            };
            await _adminAlertImport.ValidateAdminAlertData(UserIdentity.GetName(User), input, jobId);
        }

        [Route("api/CommonImportApi/GetTcoJobIdsAlertResponsibles")]
        [HttpGet]
        public async Task<List<TcoJobsHelper>> GetJobIdsForAdminAlertImport()
        {
            return await _adminAlertImport.GetJobIdsForAdminAlertImport(UserIdentity.GetName(User));
        }



        [Route("api/CommonImportApi/GetJobIdSetupSettings")]
        [HttpGet]
        public async Task<AdminAlertJobSetupHelper> GetJobIdSetupSettings(long jobId)
        {
            return await _adminAlertImport.GetJobIdSetupSettings(UserIdentity.GetName(User), jobId);
        }
        #endregion alert import

        #region SalaryForecast import

        [Route("api/CommonImport/UploadSalaryForecastData")]
        [HttpPost]
        [DisableFormModelBinding]
        public async Task<IActionResult> UploadSalaryForecastData([FromQuery] int budgetYear, [FromQuery] int forecastPeriod, [FromQuery] string salaryTable)
        {
            UploadResult result = null;
            try
            {
                Workbook wb = await GetUploadedData();


                result  = await _salaryForecastImport.UploadSalaryForecastPositionsDataAsync(UserIdentity.GetName(User), budgetYear, forecastPeriod, salaryTable, wb);
                    wb.Dispose();
               
                return result.Success
                    ? Ok(new { Success = true, Message = result.Message})
                    : StatusCode((int)HttpStatusCode.BadRequest, new
                    {
                        Success = false,
                        Message = result.Message,
                        ValidationErrors = result.ValidationErrors?
                    .SelectMany(dict => dict.Keys)
                    .Distinct()
                    .ToList()
                    });

            }
            catch (Exception ex)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, new
                {
                    Success = false,
                    Message = $"An error occurred: {ex.Message}",
                    ValidationErrors = result?.ValidationErrors?
                .SelectMany(dict => dict.Keys)
                .Distinct()
                .ToList()
                });
            }
        }

        [Route("api/CommonImport/ValidateSalaryForecastEmpIdProcedure")]
        [HttpGet]
        public async Task SalaryForecastEmpIdGeneration([FromQuery] int budgetYear, [FromQuery] int forecastPeriod, [FromQuery] string salaryTable)
        {
            
            await _salaryForecastImport.ExecuteSalaryForecastEmpIdProcedureAsync(UserIdentity.GetName(User), budgetYear, forecastPeriod, salaryTable);
        }

        [Route("api/CommonImportApi/GetSalaryForecastGridData")]
        [HttpPost]
        public async Task<StaffPlanImportDataHelper> GetSalaryForecastGridData([FromBody] SalaryForecastImportObj inputObj)
        {
            return await _salaryForecastImport.GetSalaryForecastImportDataAsync(UserIdentity.GetName(User), inputObj);
        }

        [Route("api/CommonImport/ExportSalaryForecastGridData")]
        [HttpPost]
        public async Task<ActionResult> DownloadSalaryForecastGridData([FromBody] SalaryForecastImportObj inputObj)
        {
            UserData userData = await _utility.GetUserDetailsAsync(UserIdentity.GetName(User));
            DataTable data = await _salaryForecastImport.GetSalaryForecastStagedDataAsync(UserIdentity.GetName(User), inputObj);

            List<ReportColumnHelper> columns = await GetSalaryForecastGridColumnsData(UserIdentity.GetName(User));

            return await ExportGridDataToExcel(UserIdentity.GetName(User), columns, data);

        }

        [Route("api/CommonImportApi/DeleteSalaryForecastStagedData")]
        [HttpPost]
        public async Task DeleteSalaryForecastStagedData([FromQuery] int budgetYear, [FromQuery] int forecastPeriod, [FromBody] DeleteImportChecked Ids)
        {
            await _salaryForecastImport.DeleteSalaryForecastStagedData(UserIdentity.GetName(User), budgetYear, forecastPeriod, false, Ids);
        }

        [Route("api/CommonImportApi/DeleteAllSalaryForecastStagedData")]
        [HttpDelete]
        public async Task DeleteAllSalaryForecastStagedData([FromQuery] int budgetYear, [FromQuery] int forecastPeriod)
        {
            await _salaryForecastImport.DeleteSalaryForecastStagedData(UserIdentity.GetName(User), budgetYear, forecastPeriod, true, new DeleteImportChecked());
        }

        [Route("api/CommonImport/ValidateSalaryForecastStagedData")]
        [HttpGet]
        public async Task ValidateSalaryForecastStagedData([FromQuery] int budgetYear, [FromQuery] int forecastPeriod, [FromQuery] string salaryTableName)
        {
            await _salaryForecastImport.ExecuteValidateImportedDataAsync(UserIdentity.GetName(User), budgetYear, forecastPeriod, salaryTableName);
        }


        private async Task<ActionResult> ExportGridDataToExcel(string userId, List<ReportColumnHelper> columns, DataTable data) {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            using (MemoryStream ms = await _utility.ExportGridToExcel2Async(userDetails.language_preference, data, columns, userId))
            {
                string fName = await _utility.UploadToStorageAsync(ms, userDetails);
                JObject ret = new JObject { { "success", true }, { "fName", fName } };
                return StatusCode((int)HttpStatusCode.OK, ret);
            }
        }

        [Route("api/CommonImportApi/SalaryForecastImportInfo")]
        [HttpGet]
        public async Task<ImportInfo> GetSalaryForecastImportInfo([FromQuery]int budgetYear, [FromQuery]int forecastPeriod)
        {
            return await _salaryForecastImport.GetSalaryForecastImportInfo(UserIdentity.GetName(User), budgetYear, forecastPeriod);
        }
        [Route("api/CommonImportApi/SalaryForecastImport")]
        [HttpGet]
        public async Task StartSalaryForecastImport([FromQuery]int budgetYear, [FromQuery]int forecastPeriod)
        {
            await _salaryForecastImport.SendQueueRequestToImportSalaryForecast(UserIdentity.GetName(User), budgetYear, forecastPeriod);
        }

        [Route("api/CommonImportApi/SalaryForecastWorkflowState")]
        [HttpGet]
        public async Task<string> GetSalaryForecastWorkflowState([FromQuery]int budgetYear, [FromQuery]int forecastPeriod)
        {
            ImportWorkflowState state = await _salaryForecastImport.GetSalaryForecastWorkflowState(UserIdentity.GetName(User), budgetYear, forecastPeriod);
            return state.ToString();
        }
        [Route("api/CommonImport/SalaryForecastImportStageData")]
        [HttpPost]
        public async Task<string> SaveSalaryForecastStageData([FromQuery]int budgetYear, [FromQuery]int forecastPeriod, [FromQuery]string salaryTableName, [FromBody] List<StaffPlanImportGridInput> input)
        {
            var data = mapper.Map<List<StaffPlanImportGrid>>(input);
            return await _salaryForecastImport.SaveSalaryForecastImportStageData(UserIdentity.GetName(User), budgetYear,forecastPeriod, salaryTableName, data);
        }
        [Route("api/CommonImportApi/SalaryForecastWebServiceImport")]
        [HttpGet]
        public async Task GetSalaryForecastWebServiceImport([FromQuery]int budgetYear, [FromQuery]int forecastPeriod, [FromQuery]string salaryTableName)
        {
            await _salaryForecastImport.SalaryForecastWebserviceImportAsync(UserIdentity.GetName(User), budgetYear, forecastPeriod, salaryTableName);
        }
        [Route("api/CommonImportApi/CheckingTenantAccessForWebservice")]
        [HttpGet]
        public async Task<bool> WebServiceTenantAccess()
        {
            return await _salaryForecastImport.CheckingWebServiceTenantAccessAsync(UserIdentity.GetName(User), "ImportWebService");
        }

        #endregion SalaryForecast import

        #region Absence Access Setup Import

        [Route("api/CommonImportApi/GetAdminAbsenceAccessImportGridData")]
        [HttpGet]
        public async Task<AdminAbsenceAccessImportDataHelper> GetAdminAbsenceAccessImportGridData(int skip, int take, bool isErrorToggle, long jobId)
        {
            return await _adminAbsenceAccessImport.GetAdminAbsenceAccessImportGridData(UserIdentity.GetName(User), skip, take, isErrorToggle, jobId);
        }


        [Route("api/CommonImportApi/UpdateAdminAbsenceAccessImport")]
        [HttpPost]
        public async Task UpdateAdminAbsenceAccessImport([FromBody] List<UpdateAdminAbsenceAccessImportDto> rowsToModify, [FromQuery] string orgVersion, [FromQuery] int orgLevel, [FromQuery] string orgId, [FromQuery] int budgetYear, [FromQuery] long jobId)
        {
            AbsenceAccessImportHelper input = new AbsenceAccessImportHelper()
            {
                orgVersion = orgVersion,
                orgId = orgId,
                orgLevel = orgLevel,
                budgetYear = budgetYear
            };
            List<tal_stage_absence_access_import> dataToModify = ConvertAdminAbsenceAccessHelperToEntity(rowsToModify, input);
            await _adminAbsenceAccessImport.UpdateAdminAbsenceAccessStageData(dataToModify, input, UserIdentity.GetName(User), jobId);
        }

        [Route("api/CommonImportApi/DeleteAdminAbsenceAccessStagedData")]
        [HttpPost]
        public async Task DeleteAdminAbsenceAccessStagedData(long jobId, int budgetYear, bool isDeleteAll, string orgVersion, [FromBody] AdminAbsenceAccessImportDeleteHelper input)
        {
            await _adminAbsenceAccessImport.DeleteAdminAbsenceAccessStagedData(UserIdentity.GetName(User), budgetYear, isDeleteAll, input.Ids, jobId, orgVersion);
        }

        [Route("api/CommonImportApi/DeleteAdminAbsenceAccessStagedAllData")]
        [HttpDelete]
        public async Task DeleteAdminAbsenceAccessStagedAllData(long jobId, int budgetYear, bool isDeleteAll, string orgVersion)
        {
            await _adminAbsenceAccessImport.DeleteAdminAbsenceAccessStagedData(UserIdentity.GetName(User), budgetYear, isDeleteAll, new List<long>(), jobId, orgVersion); 
        }

        [Route("api/CommonImportApi/ImportAdminAbsenceAccessDataExcel")]
        [HttpPost]
        [DisableFormModelBinding]
        public async Task ImportAdminAbsenceAccessDataExcel(string orgVersion, int orgLevel, string orgId, int budgetYear)
        {
            Workbook wb = await GetUploadedData();
            if (wb != null)
            {
                AbsenceAccessImportHelper input = new AbsenceAccessImportHelper()
                {
                    orgVersion = orgVersion,
                    orgId = orgId,
                    orgLevel = orgLevel,
                    budgetYear = budgetYear

                };
                await _adminAbsenceAccessImport.ImportExcelToAdminAbsenceAccessStageTable(UserIdentity.GetName(User), wb, input);
                wb.Dispose();
            }
        }

        [Route("api/CommonImportApi/ValidateAdminAbsenceAccessData")]
        [HttpGet]
        public async Task ValidateAdminAbsenceAccessData(string orgVersion, int orgLevel, string orgId, int budgetYear, long jobId)
        {
            AbsenceAccessImportHelper input = new AbsenceAccessImportHelper()
            {
                orgVersion = orgVersion,
                orgId = orgId,
                orgLevel = orgLevel,
                budgetYear = budgetYear
            };
            await _adminAbsenceAccessImport.ValidateAdminAbsenceAccessData(UserIdentity.GetName(User), input, jobId);
        }

        [Route("api/CommonImportApi/StartAdminAbsenceAccessImport")]
        [HttpGet]
        public async Task StartAdminAbsenceAccessImport(long jobId, bool delete = false)
        {
            await _adminAbsenceAccessImport.WriteToAdminAbsenceAccessImportQueue(UserIdentity.GetName(User), jobId, delete);
        }

        [Route("api/CommonImportApi/UploadAdminAbsenceAccessEditedData")]
        [HttpPost]
        [DisableFormModelBinding]
        public async Task UploadAdminAbsenceAccessEditedData([FromQuery] string orgVersion, [FromQuery] int orgLevel, [FromQuery] string orgId, [FromQuery] int budgetYear, [FromQuery] long jobId)
        {
            Workbook wb = await GetUploadedData();
            if (wb != null)
            {
                AbsenceAccessImportHelper input = new AbsenceAccessImportHelper()
                {
                    orgVersion = orgVersion,
                    orgId = orgId,
                    orgLevel = orgLevel,
                    budgetYear = budgetYear,
                    jobId = jobId
                };
                await _adminAbsenceAccessImport.ReImportExcelToAdminAbsenceAccessStageTable(UserIdentity.GetName(User), wb, input);
                wb.Dispose();
            }
        }

        [Route("api/CommonImportApi/GetTcoJobIdsAbsenceAccessSetup")]
        [HttpGet]
        public async Task<List<TcoJobsHelper>> GetTcoJobIdsAbsenceAccessSetup()
        {
            return await _adminAbsenceAccessImport.GetJobIdsForAbsenceAccessImportAsync(UserIdentity.GetName(User));
        }



        [Route("api/CommonImportApi/GetAbsenceAccessJobIdSetupSettings")]
        [HttpGet]
        public async Task<AbsenceAccessJobSetupHelper> GetAbsenceAccessJobIdSetupSettings(long jobId)
        {
            return await _adminAbsenceAccessImport.GetAbsenceAccessJobIdSetupSettingsAsync(UserIdentity.GetName(User), jobId);
        }


        #endregion Absence Access Setup Import

        #region Private Methods

        private async Task<JArray> GetObjectValuesGridConfig()
        {
            Assembly assembly = Assembly.GetExecutingAssembly();
            Stream configStream = null;

            configStream = assembly.GetManifestResourceStream("Framsikt.Web.Core.Controllers.config.Import.ObjectValuesImportGrid.json");

            StreamReader reader = new StreamReader(configStream);
            string config = await reader.ReadToEndAsync();
            JArray colConfig = JArray.Parse(config);

            UserData userDetails = await _utility.GetUserDetailsAsync(UserIdentity.GetName(User));
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "CommonImport");

            //Replace language string specific properties
            foreach (var col in colConfig)
            {
                switch ((string)col["field"])
                {
                    case "selectColumn":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("object_import_select_column", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "attribute_id":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("object_import_attribute_id", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "attribute_name":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("object_import_attribute_name", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "year_from":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("object_import_year_from", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "year_to":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("object_import_year_to", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "status":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("object_import_status", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                }
            }
            return colConfig;
        }

        private async Task<JArray> GetRelationValueGridColumn()
        {
            Assembly assembly = Assembly.GetExecutingAssembly();
            Stream configStream = null;

            configStream = assembly.GetManifestResourceStream("Framsikt.Web.Core.Controllers.config.Import.RelationValuesImportGrid.json");
            StreamReader reader = new StreamReader(configStream);
            string config = await reader.ReadToEndAsync();
            JArray colConfig = JArray.Parse(config);

            UserData userDetails = await _utility.GetUserDetailsAsync(UserIdentity.GetName(User));
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "CommonImport");
            //Replace language string specific properties
            foreach (var col in colConfig)
            {
                switch ((string)col["field"])
                {
                    case "selectColumn":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("object_import_select_column", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "attribute_value_id":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_relation_id", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "relation_value_from":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_relation_values_from", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "relation_value_to":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_relation_values_to", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "year_from":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("object_import_year_from", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "year_to":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("object_import_year_to", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    default:
                        break;
                }
            }

            return colConfig;
        }

        private async Task<JArray> GetAssignmentImportGridColumn()
        {
            Assembly assembly = Assembly.GetExecutingAssembly();
            Stream configStream = null;

            configStream = assembly.GetManifestResourceStream("Framsikt.Web.Core.Controllers.config.Import.AssignmentImportGrid.json");
            StreamReader reader = new StreamReader(configStream);
            string config = await reader.ReadToEndAsync();
            JArray colConfig = JArray.Parse(config);

            UserData userDetails = await _utility.GetUserDetailsAsync(UserIdentity.GetName(User));
            bool isServiceSetup = await _utility.IsServiceSetupMrLvlAsync(UserIdentity.GetName(User));
            if (!isServiceSetup)
            {
                colConfig = JArray.FromObject(colConfig.Where(z => z["field"].ToString() != "serviceId").ToList());
            }

            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "CommonImport");

            foreach (var col in colConfig)
            {
                switch ((string)col["field"])
                {
                    case "selectColumn":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("object_import_select_column", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "assignmentName":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_assignment_name", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "category":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_assignment_category", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "caseExternalReference":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_case_external_reference", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "startYearBP":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_case_start_year_bp", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "endYear":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_case_end_year", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "startDate":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_assignment_start_date", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "endDate":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_assignment_end_date", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "status":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_assignment_status", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "urlPrefix":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_assignment_url_prefix", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "url":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_assignment_url", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "urlTitle":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_assignment_url_title", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "orgLevel":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_assignment_org_level", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "orgId":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_assignment_org_id", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "serviceId":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_assignment_service_id", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "description":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_assignment_description", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "assignmentOwner":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_assignment_assignment_owner", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "responsibleUser":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_assignment_responsible_user", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    default:
                        break;
                }
            }

            return colConfig;
        }

        private async Task<JArray> GetInvestmentForecastImportGridColumn(int budgetYear)
        {
            Assembly assembly = Assembly.GetExecutingAssembly();
            Stream configStream = null;

            configStream = assembly.GetManifestResourceStream("Framsikt.Web.Core.Controllers.config.Import.InvestmentForecastImportGrid.json");
            StreamReader reader = new StreamReader(configStream);
            string config = await reader.ReadToEndAsync();
            JArray colConfig = JArray.Parse(config);

            UserData userDetails = await _utility.GetUserDetailsAsync(UserIdentity.GetName(User));
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ng_import");
            Dictionary<string, clsLanguageString> commonLangStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "CommonImport");

            foreach (var col in colConfig)
            {
                switch ((string)col["field"])
                {
                    case "project_code":
                        col["title"] = (langStringValues["project_import_column"]).LangText;
                        break;

                    case "project_name":
                        col["title"] = (langStringValues["project_name_import_column"]).LangText;
                        break;

                    case "account_code":
                        col["title"] = (langStringValues["account_column_import"]).LangText;
                        break;

                    case "department_code":
                        col["title"] = (langStringValues["department_column_import"]).LangText;
                        break;

                    case "function_code":
                        col["title"] = (langStringValues["function_column_import"]).LangText;
                        break;

                    case "free_dim_1":
                        col["title"] = (langStringValues["freedim1_column_import"]).LangText;
                        break;

                    case "free_dim_2":
                        col["title"] = (langStringValues["freedim2_column_import"]).LangText;
                        break;

                    case "free_dim_3":
                        col["title"] = (langStringValues["freedim3_column_import"]).LangText;
                        break;

                    case "free_dim_4":
                        col["title"] = (langStringValues["freedim4_column_import"]).LangText;
                        break;

                    case "forecast_year_1":
                        col["title"] = (langStringValues["year_wise_amount_import"]).LangText + " " + budgetYear;
                        break;

                    case "forecast_year_2":
                        col["title"] = (langStringValues["year_wise_amount_import"]).LangText + " " + (budgetYear + 1);
                        break;

                    case "forecast_year_3":
                        col["title"] = (langStringValues["year_wise_amount_import"]).LangText + " " + (budgetYear + 2);
                        break;

                    case "forecast_year_4":
                        col["title"] = (langStringValues["year_wise_amount_import"]).LangText + " " + (budgetYear + 3);
                        break;

                    case "forecast_year_5":
                        col["title"] = (langStringValues["year_wise_amount_import"]).LangText + " " + (budgetYear + 4);
                        break;

                    case "forecast_year_6":
                        col["title"] = (langStringValues["year_wise_amount_import"]).LangText + " " + (budgetYear + 5);
                        break;

                    case "forecast_year_7":
                        col["title"] = (langStringValues["year_wise_amount_import"]).LangText + " " + (budgetYear + 6);
                        break;

                    case "forecast_year_8":
                        col["title"] = (langStringValues["year_wise_amount_import"]).LangText + " " + (budgetYear + 7);
                        break;

                    case "forecast_year_9":
                        col["title"] = (langStringValues["year_wise_amount_import"]).LangText + " " + (budgetYear + 8);
                        break;

                    case "forecast_year_10":
                        col["title"] = (langStringValues["year_wise_amount_import"]).LangText + " " + (budgetYear + 9);
                        break;

                    case "total_forecast":
                        col["title"] = (langStringValues["total_forecast_import"]).LangText;
                        break;

                    case "approval_cost":
                        col["title"] = (langStringValues["approval_cost_import"]).LangText;
                        break;
                    case "isy_update_date":
                        col["title"] = (commonLangStringValues["import_inv_ISYupdatedate"]).LangText;
                        break;
                    default:
                        break;
                }
            }

            return colConfig;
        }

        private async Task<JArray> GetActivityIndicatorImportGridColumn()
        {
            Assembly assembly = Assembly.GetExecutingAssembly();
            Stream configStream = null;

            configStream = assembly.GetManifestResourceStream("Framsikt.Web.Core.Controllers.config.Import.ActivityIndicatorImport.json");
            StreamReader reader = new StreamReader(configStream);
            string config = await reader.ReadToEndAsync();
            JArray colConfig = JArray.Parse(config);
            var fpLevel2Value = await _utility.GetParameterValueAsync(UserIdentity.GetName(User), "FINPLAN_LEVEL_2");
            UserData userDetails = await _utility.GetUserDetailsAsync(UserIdentity.GetName(User));
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "CommonImport");

            foreach (var col in colConfig)
            {
                switch ((string)col["field"])
                {
                    case "selectColumn":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("object_import_select_column", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    case "id":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_activityIndic_id", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "Name":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_activityIndic_Name", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "orgLevel":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_activityIndic_orgLevel", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "orgId":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_activityIndic_orgId", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "serviceId":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_activityIndic_serviceId", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        if (fpLevel2Value != null && fpLevel2Value.Contains("ser"))
                        {
                            col["hidden"] = false;
                        }
                        break;

                    case "period":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_activityIndic_period", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "result":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_activityIndic_result", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    default:
                        break;

                }
            }

            return colConfig;
        }

        private async Task<JArray> GetYBBudgetImportGridColumns(bool isPeriodicKey, bool isLockBudget)
        {
            Assembly assembly = Assembly.GetExecutingAssembly();
            Stream configStream = null;

            configStream = assembly.GetManifestResourceStream("Framsikt.Web.Core.Controllers.config.Import.YBBudgetImportGrid.json");
            StreamReader reader = new StreamReader(configStream);
            string config = await reader.ReadToEndAsync();
            JArray colConfig = JArray.Parse(config);
          
            UserData userDetails = await _utility.GetUserDetailsAsync(UserIdentity.GetName(User));
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ng_import");
            Dictionary<string, clsLanguageString> langStringCommonValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "CommonImport");
            IEnumerable<freedimDefinition> freeDims = (await _utility.GetFreeDimDefinitionsBasedOnPageIdAsync(UserIdentity.GetName(User), "staff_planning")).ToList();
            bool altCodetoDisplay = false, serviceCodeToDisplay = false, isFrameCheckToRole = false;
            var altCodeParam = await _utility.GetActiveParameterValueAsync(UserIdentity.GetName(User), "YBUD_USE_ALTERCODE");
            altCodetoDisplay = altCodeParam != string.Empty && bool.Parse(altCodeParam) == true ? true : false;
            
            if (isLockBudget)
            {
                var frameCheckParam = await _utility.GetActiveParameterValueAsync(UserIdentity.GetName(User), "YB_IMPORT_FRAME_CONTROL");
                isFrameCheckToRole = await _ybImportBudget.IsFrameCheckAddedToRole(UserIdentity.GetName(User));
                serviceCodeToDisplay = frameCheckParam != null && isFrameCheckToRole ? true : false;
            }

            var result = new JArray();
            foreach (var col in colConfig)
            {
                var field = (string)col["field"];
                switch ((string)col["field"])
                {
                    case "selectColumn":
                        col["title"] = langStringCommonValues["object_import_select_column"].LangText;
                        result.Add(col);
                        break;
                    case "account_code":
                        col["title"] = langStringValues["import_yib_AccountCode"].LangText;
                        result.Add(col);
                        break;
                    case "department_code":
                        col["title"] = langStringValues["import_yib_DepartmentCode"].LangText;
                        result.Add(col);
                        break;
                    case "function_code":
                        col["title"] = langStringValues["import_yib_FunctionCode"].LangText;
                        result.Add(col);
                        break;
                    case "project_code":
                        col["title"] = langStringValues["import_yib_ProjectCode"].LangText;
                        result.Add(col);
                        break;
                    case "total_budget":
                        col["title"] = langStringValues["import_yib_TotalBudget"].LangText;
                        result.Add(col);
                        break;
                    case "comments":
                        col["title"] = langStringValues["import_yib_Comments"].LangText;
                        result.Add(col);
                        break;
                    default:
                        break;
                }

                if (field == "project_code")
                {
                    if (freeDims.Any())
                    {
                        if (freeDims.Any(x => x.freeDimColumn == "free_dim_1"))
                        {
                            var column = colConfig[1];
                            column["field"] = "free_dim_1";
                            column["title"] = freeDims.FirstOrDefault(x => x.freeDimColumn == "free_dim_1").freeDimHeader;
                            result.Add(column);
                        }
                        if (freeDims.Any(x => x.freeDimColumn == "free_dim_2"))
                        {
                            var column = colConfig[1];
                            column["field"] = "free_dim_2";
                            column["title"] = freeDims.FirstOrDefault(x => x.freeDimColumn == "free_dim_2").freeDimHeader;
                            result.Add(column);
                        }
                        if (freeDims.Any(x => x.freeDimColumn == "free_dim_3"))
                        {
                            var column = colConfig[1];
                            column["field"] = "free_dim_3";
                            column["title"] = freeDims.FirstOrDefault(x => x.freeDimColumn == "free_dim_3").freeDimHeader;
                            result.Add(column);
                        }
                        if (freeDims.Any(x => x.freeDimColumn == "free_dim_4"))
                        {
                            var column = colConfig[1];
                            column["field"] = "free_dim_4";
                            column["title"] = freeDims.FirstOrDefault(x => x.freeDimColumn == "free_dim_4").freeDimHeader;
                            result.Add(column);
                        }

                    }
                    if (altCodetoDisplay)
                    {
                        var column = colConfig[1];
                        column["field"] = "alter_code";
                        column["title"] = (langStringValues["import_yib_AlterCode"]).LangText;
                        result.Add(column);
                    }
                }
            }

            var periodColumn = colConfig[1];
            if (isPeriodicKey)
            {
                periodColumn["field"] = "periodic_key";
                periodColumn["title"] = (langStringValues["import_yib_PeriodicKey"]).LangText;
            }
            else
            {
                periodColumn["field"] = "period";
                periodColumn["title"] = (langStringValues["import_yib_Period"]).LangText;
            }
            result.Add(periodColumn);

            if (serviceCodeToDisplay)
            {
                var column = colConfig[1];
                column["field"] = "service_area_code";
                column["title"] = langStringValues["import_yib_ServiceAreaCode"].LangText;
                result.Add(column);
            }
            return result;
        }
        private async Task<JArray> GetInvestmentStatusImportGridColumn()
        {
            Assembly assembly = Assembly.GetExecutingAssembly();
            Stream configStream = null;

            configStream = assembly.GetManifestResourceStream("Framsikt.Web.Core.Controllers.config.Import.InvestmentStatusImportGrid.json");
            StreamReader reader = new StreamReader(configStream);
            string config = await reader.ReadToEndAsync();
            JArray colConfig = JArray.Parse(config);

            UserData userDetails = await _utility.GetUserDetailsAsync(UserIdentity.GetName(User));
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "CommonImport");

            foreach (var col in colConfig)
            {
                switch ((string)col["field"])
                {
                    case "selectColumn":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("object_import_select_column", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "status_level":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_inv_status_level", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "ID":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_inv_ID", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "est_finish_quarter":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_inv_est_finish_quarter", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "status":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_inv_status", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "risk":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_inv_risk", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "quality":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_inv_quality", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "fin_status":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_inv_finstatus", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "status_desc":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_inv_statusdesc", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    case "isy_update_date":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("import_inv_ISYupdatedate", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    default:
                        break;
                }
            }

            return colConfig;
        }
        private async Task<JArray> GetProjStructureImportGridColumn()
        {
            Assembly assembly = Assembly.GetExecutingAssembly();
            Stream configStream = null;

            configStream = assembly.GetManifestResourceStream("Framsikt.Web.Core.Controllers.config.Import.ProjectStructureImportGrid.json");
            StreamReader reader = new StreamReader(configStream);
            string config = await reader.ReadToEndAsync();
            JArray colConfig = JArray.Parse(config);

            UserData userDetails = await _utility.GetUserDetailsAsync(UserIdentity.GetName(User));

            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "CommonImport");

            foreach (var col in colConfig)
            {
                switch ((string)col["field"])
                {
                    case "selectColumn":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("object_import_select_column", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "fk_proj_version":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("Import_fk_proj_version", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "fk_project_code":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("Import_fk_proj_code", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "proj_grp_1":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("Import_proj_grp_1", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "proj_grp_name_1":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("Import_proj_name_1", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "proj_grp_2":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("Import_proj_grp_2", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "proj_grp_name_2":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("Import_proj_name_2", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "proj_grp_3":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("Import_proj_grp_3", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "proj_grp_name_3":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("Import_proj_name_3", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "proj_grp_4":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("Import_proj_grp_4", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "proj_grp_name_4":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("Import_proj_name_4", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "proj_grp_5":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("Import_proj_grp_5", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "proj_grp_name_5":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("Import_proj_name_5", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    default:
                        break;
                }
            }

            return colConfig;
        }

        private JArray GetAdminAlertImportGridColumn(ImportScreenType importScreenType)
        {
            Assembly assembly = Assembly.GetExecutingAssembly();
            Stream configStream = null;

            switch (importScreenType)
            {
                case ImportScreenType.AdminAlertImport:
                    configStream = assembly.GetManifestResourceStream("Framsikt.Web.Core.Controllers.config.Import.AdminAlertImportGrid.json");
                    break;

                case ImportScreenType.AdminAbsenceAccessImport:
                    configStream = assembly.GetManifestResourceStream("Framsikt.Web.Core.Controllers.config.Import.AdminAbsenceAccessImportGrid.json");
                    break;
            }
       
            StreamReader reader = new StreamReader(configStream);
            string config = reader.ReadToEnd();
            JArray colConfig = JArray.Parse(config);

            UserData userDetails = _utility.GetUserDetails(UserIdentity.GetName(User));

            Dictionary<string, clsLanguageString> langStringValues = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "CommonImport");

            foreach (var col in colConfig)
            {
                switch ((string)col["field"])
                {
                    case "selectColumn":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("object_import_select_column", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "id":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("Import_alert_id", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "org_level":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("Import_alert_org_lvl", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "org_id":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("Import_alert_org_id", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "responsible_user_id":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("Import_alert_responsible_userId", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "reporting_user_id":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("Import_alert_reporting_userId", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "user_id":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("Import_absence_access_user", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    default:
                        break;
                }
            }

            return colConfig;
        }

        private async Task<JArray> GetStaffPlanImportGridColConfig()
        {
            Assembly assembly = Assembly.GetExecutingAssembly();
            Stream configStream = null;

            configStream = assembly.GetManifestResourceStream("Framsikt.Web.Core.Controllers.config.Import.StaffPlanPositionImportGrid.json");
            StreamReader reader = new StreamReader(configStream);
            string config = await reader.ReadToEndAsync();
            JArray colConfig = JArray.Parse(config);
            var useSalaryTable = await _utility.GetParameterValueAsync(UserIdentity.GetName(User), "USE_SALARY_TABLE") == "TRUE" ? true: false;
            UserData userDetails = await _utility.GetUserDetailsAsync(UserIdentity.GetName(User));
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "StaffPlanning");

            foreach (var col in colConfig)
            {
                switch ((string)col["field"])
                {
                    case "selectColumn":
                        col["title"] = "Velg";
                        break;
                    case "employmentId":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_EmploymentId", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    case "treatment":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_treatment_code", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "deptCode":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_Department", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "functionCode":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_Function", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "accountCode":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_Account", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "salaryType":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_SalaryType", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "salaryTypeDescription":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_SalaryType_desc", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "projectCode":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_Project", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    case "freeDim1":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_Freedim1", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    case "freeDim2":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_Freedim2", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    case "freeDim3":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_Freedim3", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    case "freeDim4":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_Freedim4", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    case "resourceId":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_ResourceId", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    case "resourceName":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_ResourceName", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    case "externalReference":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_ExtRef", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    case "ExternalPosReference":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_ExtPosRef", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    case "externalEmpType":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_ExtEmpType", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    case "positionPct":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_position_pct", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    case "salaryStep":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_salaryStep", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        if (!useSalaryTable)
                            col["hidden"] = true;
                        break;
                    case "amountSalaryYear":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_amt_sal_year", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    case "pensionType":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_pension_type", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    case "taxRate":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_tax_rate", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    case "amountSalaryMonth":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_amt_sal_month", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    case "startPeriod":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_StartPeriod", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    case "endPeriod":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_EndPeriod", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    case "resourceBirthDate":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_ResourceBirthDate", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    case "positionId":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_PositionId", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    case "positionName":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_PositionName", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                    case "externalPosReference":
                        col["title"] = langStringValues.FirstOrDefault(x => x.Key.Equals("sp_pos_ExtPosRef", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    default:
                        break;

                }
            }

            return colConfig;
        }

        private async Task<Workbook> GetUploadedData() {
            Workbook wb = null;
            var boundary = HeaderUtilities.RemoveQuotes(MediaTypeHeaderValue.Parse(Request.ContentType).Boundary).Value;
            var reader = new MultipartReader(boundary, Request.Body);
            var section = await reader.ReadNextSectionAsync();
            while (section != null)
            {
                var hasContentDispositionHeader = ContentDispositionHeaderValue.TryParse(section.ContentDisposition, out var contentDisposition);
                if (hasContentDispositionHeader)
                {
                    if (contentDisposition != null && contentDisposition.DispositionType.Equals("form-data") && !string.IsNullOrEmpty(contentDisposition.FileName.Value))
                    {
                        using (var stream = new MemoryStream())
                        {
                            // make sure that body is read from the beginning
                            section.Body.Seek(0, SeekOrigin.Begin);
                            await section.Body.CopyToAsync(stream);
                            wb = new Workbook(stream);
                        }
                    }
                }
                section = await reader.ReadNextSectionAsync();
            }
            return wb ?? new Workbook();
        }

        private List<tal_stage_admin_alert_import> ConvertAdminALertHelperToEntity (List<AdminAlertImportGridDataHelper> rowsToModify, AdminAlertImportHelper input)
        {
            List<tal_stage_admin_alert_import> result = new List<tal_stage_admin_alert_import>();
            foreach( var row in rowsToModify)
            {
                tal_stage_admin_alert_import data = new tal_stage_admin_alert_import()
                {
                    id = row.id,
                    org_id = row.org_id,
                    org_level = row.org_level,
                    responsible_user_id = row.responsible_user_id,
                    reporting_user_id = row.reporting_user_id,
                    setup_type = input.alertType.ToString(),
                    org_version = input.orgVersion,
                    budget_year = input.budgetYear,
                };
                result.Add(data);
            }
            return result;
        }


        private async Task<List<ReportColumnHelper>> GetSalaryForecastGridColumnsData(string userId)
        {
            List<ReportColumnHelper> columns = new List<ReportColumnHelper>();
            ColumnInfo columnInfo = await _staffPlanPositionsImport.GetStaffPlanPositionsColumnInfoAsync(userId);

            for (int i = 0; i < columnInfo.Fields.Count; i++)
            {
                ReportColumnHelper c = new ReportColumnHelper
                {
                    ColName = columnInfo.Fields[i],
                    DisplayName = columnInfo.Titles[i],
                    DataType = columnInfo.DataTypes[i]
                };
                columns.Add(c);
            }
            return columns;
        }

        private List<tal_stage_absence_access_import> ConvertAdminAbsenceAccessHelperToEntity(List<UpdateAdminAbsenceAccessImportDto> rowsToModify, AbsenceAccessImportHelper input)
        {
            List<tal_stage_absence_access_import> result = new List<tal_stage_absence_access_import>();
            foreach (var row in rowsToModify)
            {
                tal_stage_absence_access_import data = new tal_stage_absence_access_import()
                {
                    id = row.id,
                    org_id = row.org_id,
                    org_level = row.org_level,
                    user_id = row.user_id,
                    org_version = input.orgVersion,
                    budget_year = input.budgetYear,
                };
                result.Add(data);
            }
            return result;
        }

        

        #endregion Private Methods
    }
}