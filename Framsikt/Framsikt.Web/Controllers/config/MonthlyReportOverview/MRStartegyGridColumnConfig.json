{"columns": [{"title": "Strategy Name", "field": "strategy", "colCount": 1, "encoded": false, "hidden": false, "format": null, "width": 250, "attributes": {"style": "text-align:left; border-left:none; padding-bottom:10px;vertical-align: top"}, "headerAttributes": {"style": "text-align:left; border-left:none;"}, "template": "<div class='strategy-item'></div>", "filterable": {"cell": null}}, {"title": "Status", "field": "status", "colCount": 2, "encoded": false, "hidden": false, "format": null, "width": 180, "attributes": {"style": "text-align:left; border-left:none; white-space:nowrap;padding-bottom:10px;vertical-align: top"}, "headerAttributes": {"style": "text-align:left; border-left:none; white-space:nowrap;"}, "template": "<span style='border:1px solid \\#6FA1B2;padding:5px 15px 5px 15px;border-radius:4px;display:inline-block;width:70%;height:20px;cursor:pointer;'> #=statusObj.Value# </span>", "filterable": {"cell": null}}, {"title": "Status Description", "field": "statusdesc", "colCount": 3, "encoded": false, "hidden": false, "format": null, "width": 180, "attributes": {"style": "text-align:left; border-left:none; vertical-align: top"}, "headerAttributes": {"style": "text-align:left; border-left:none; white-space:nowrap;"}, "template": "# if( statusdesc.length < 70) {#<div class='statusdesc-ckeditor' style='height:103px;border-radius:5px;'></div>#}else{#<div class='statusdesc-ckeditor' style='height:103px;border-radius:5px;'></div>#}#", "filterable": {"cell": null}}, {"title": "Target", "field": "target", "colCount": 4, "encoded": false, "hidden": false, "format": null, "width": 220, "attributes": {"style": "text-align:left; border-left:none; padding-bottom:10px;vertical-align: top"}, "headerAttributes": {"style": "text-align:left; border-left:none;"}, "template": "<div class='target'></div>", "filterable": {"cell": null}}, {"title": "UNTarget", "field": "unTarget", "colCount": 5, "encoded": false, "hidden": false, "format": null, "width": 250, "attributes": {"style": "text-align:left; border-left:none; padding-bottom:10px;vertical-align: top"}, "headerAttributes": {"style": "text-align:left; border-left:none;"}, "template": "<div class='unTarget'></div>", "filterable": {"cell": null}}, {"title": "Goal", "field": "goal", "colCount": 6, "encoded": false, "hidden": false, "format": null, "width": 220, "attributes": {"style": "text-align:left; border-left:none; padding-bottom:10px;vertical-align: top"}, "headerAttributes": {"style": "text-align:left; border-left:none;"}, "template": "<div class='goal'></div>", "filterable": {"cell": null}}, {"title": "UNGoal", "field": "unGoal", "colCount": 7, "encoded": false, "hidden": false, "format": null, "width": 250, "attributes": {"style": "text-align:left; border-left:none; padding-bottom:10px;vertical-align: top"}, "headerAttributes": {"style": "text-align:left; border-left:none;"}, "template": "<div class='unGoal'></div>", "filterable": {"cell": null}}, {"title": "FocusArea", "field": "focusArea", "colCount": 8, "encoded": false, "hidden": false, "format": null, "width": 250, "attributes": {"style": "text-align:left; border-left:none; padding-bottom:10px;vertical-align: top"}, "headerAttributes": {"style": "text-align:left; border-left:none;"}, "template": "<div class='focusArea'></div>", "filterable": {"cell": null}}, {"title": "isReported", "field": "isReported", "colCount": 9, "encoded": false, "hidden": false, "format": null, "width": 180, "attributes": {"style": "text-align:center; border-left:none; vertical-align: top; padding-top:10px"}, "headerAttributes": {"style": "text-align:left; border-left:none; white-space:nowrap;"}, "template": "<div><input class='k-checkbox' type='checkbox' data-bind ='checked:isReported' data-role = 'checkbox'></div>", "filterable": {"cell": null}}]}