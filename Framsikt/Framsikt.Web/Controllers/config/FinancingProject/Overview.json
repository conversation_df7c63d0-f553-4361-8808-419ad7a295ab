[{"template": null, "hidden": true, "format": null, "title": " ", "field": "lineItemId", "colCount": 0, "width": 0, "attributes": {"style": "text-align:left;border-left:none;white-space:normal;vertical-align:top;"}, "attributeStyle": {"text-align": "left", "border-left": "none", "white-space": "normal", "vertical-align": "top"}, "headerAttributes": {"style": "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "headerStyle": {"text-align": "left", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "#if(lineItemId !== -1 && lineItemId !== 0){#<div class='financing-service-items col-md-12 padding0'><div class='financing-indicator-name col-md-10 padding0'> <a ng-click='vm.openFinancingActionPopup(\"#:lineItemId#\",\"#:lineGroupId#\",\"#:lineItemName#\")'>#=lineItemName#</a></div> #if(lineItemId == '1010' || lineItemId == '2010'){#<div class='refresh-items col-md-1 padding0 align-center'><a ng-click='vm.getGridSummary(\"#:lineItemId#\",#:programCode#,\"#:lineItemName#\")' tabindex='0'><img src='../images/forecast_overview_clean.png' class='calculate-VAT'/> </a></div>#}if(caculateVAT || calculateLoan){#<div class='refresh-items edit-vat-loan col-md-1 padding0 align-center'>#if((caculateVAT && disableCalculateVAT) || (calculateLoan && disableCalculateLoan)){#<img src='../images/refresh_inactive.svg'   alt='refresh_inactive' class='calculate-VAT'/>#}else{# <a ng-click='vm.refreshFinancing(\"#:programCode#\",#:caculateVAT#,#:calculateLoan#)' tabindex='0'><img src='../images/refresh_small.png'   alt='refresh' class='calculate-VAT'/> </a>#}#</div>#}#</div>#}else if(lineItemId == 0){# <div class='financing-service-items col-md-12 padding0'><div class='financing-indicator-name col-md-11 padding0'><span>#=lineItemName#</span></div></div>#}else{#<span class='semi'>#=lineItemName#</span>#}#", "hidden": false, "format": null, "title": "line item name", "field": "lineItemName", "colCount": 0, "width": 300, "attributes": {"style": "text-align:left;border-left:none;white-space:normal;vertical-align:top;"}, "headerAttributes": {"style": "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"text-align": "left", "border-left": "none", "white-space": "normal", "vertical-align": "top"}, "headerStyle": {"text-align": "left", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "#if(lineItemId !== -1 && lineItemId !== 0){#<div class='financing-service-items col-md-12 padding0'><div class='financing-indicator-name col-md-10 padding0'> <a ng-click='vm.openFinancingActionPopup(\"#:lineItemId#\",\"#:lineGroupId#\",\"#:lineItemName#\")'>#=lineItemName#</a></div> #if(lineItemId == '1010' || lineItemId == '2010'){#<div class='refresh-items col-md-1 padding0 align-center'><a ng-click='vm.getGridSummary(\"#:lineItemId#\",#:programCode#,\"#:lineItemName#\")' tabindex='0'><img src='../images/forecast_overview_clean.png' class='calculate-VAT'/> </a></div>#}if(caculateVAT || calculateLoan){#<div class='refresh-items edit-vat-loan col-md-1 padding0 align-center'>#if((caculateVAT && disableCalculateVAT) || (calculateLoan && disableCalculateLoan)){#<img src='../images/refresh_inactive.svg'   alt='refresh_inactive' class='calculate-VAT'/>#}else{# <a ng-click='vm.refreshFinancing(\"#:programCode#\",#:caculateVAT#,#:calculateLoan#)' tabindex='0'><img src='../images/refresh_small.png'   alt='refresh' class='calculate-VAT'/> </a>#}#</div>#}#</div>#}else if(lineItemId == 0){# <div class='financing-service-items col-md-12 padding0'><div class='financing-indicator-name col-md-11 padding0'><span>#=lineItemName#</span></div></div>#}else{#<span class='semi'>#=lineItemName#</span>#}#", "hidden": false, "format": null, "title": "<PERSON><PERSON>", "field": "popupColumn", "colCount": 0, "width": 150, "attributes": {"style": "text-align:left;border-left:none;white-space:normal;vertical-align:top;"}, "headerAttributes": {"style": "text-align:left;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"text-align": "center", "white-space": "normal", "border-left-width": "1px", "border-right-width": "1px !important", "vertical-align": "top"}, "headerStyle": {"text-align": "center", "border-right-width": "1px !important", "border-left-width": "1px", "border-bottom-width": "2px", "border-left-color": "rgb(202, 202, 202)"}, "encoded": false}, {"template": "<span>#if(prevYear1Amount !== '' && prevYear1Amount !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(prevYear1Amount),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(prevYear1Amount),'n0')# </span># } }else if(prevYear1Amount == null){# 0 #} else{# #=prevYear1Amount# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2019", "field": "prevYear1Amount", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(year1Amount !== '' && year1Amount !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(year1Amount),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(year1Amount),'n0')# </span># } }else if(year1Amount == null){# 0 #} else{# #=year1Amount# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2020", "field": "year1Amount", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(year2Amount !== '' && year2Amount !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(year2Amount),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(year2Amount),'n0')# </span># } }else if(year2Amount == null){# 0 #} else{# #=year2Amount# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2021", "field": "year2Amount", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(year3Amount !== '' && year3Amount !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(year3Amount),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(year3Amount),'n0')# </span># } }else if(year3Amount == null){# 0 #} else{# #=year3Amount# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2022", "field": "year3Amount", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(year4Amount !== '' && year4Amount !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(year4Amount),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(year4Amount),'n0')# </span># } }else if(year4Amount == null){# 0 #} else{# #=year4Amount# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2023", "field": "year4Amount", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(year5Amount !== '' && year5Amount !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(year5Amount),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(year5Amount),'n0')# </span># } }else if(year5Amount == null){# 0 #} else{# #=year5Amount# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2024", "field": "year5Amount", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(year6Amount !== '' && year6Amount !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(year6Amount),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(year6Amount),'n0')# </span># } }else if(year6Amount == null){# 0 #} else{# #=year6Amount# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2025", "field": "year6Amount", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(year7Amount !== '' && year7Amount !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(year7Amount),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(year7Amount),'n0')# </span># } }else if(year7Amount == null){# 0 #} else{# #=year7Amount# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2026", "field": "year7Amount", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(year8Amount !== '' && year8Amount !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(year8Amount),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(year8Amount),'n0')# </span># } }else if(year8Amount == null){# 0 #} else{# #=year8Amount# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2027", "field": "year8Amount", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(year9Amount !== '' && year9Amount !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(year9Amount),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(year9Amount),'n0')# </span># } }else if(year9Amount == null){# 0 #} else{# #=year9Amount# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2028", "field": "year9Amount", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(year10Amount !== '' && year10Amount !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(year10Amount),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(year10Amount),'n0')# </span># } }else if(year10Amount == null){# 0 #} else{# #=year10Amount# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2029", "field": "year10Amount", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(year11Amount !== '' && year11Amount !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(year11Amount),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(year11Amount),'n0')# </span># } }else if(year11Amount == null){# 0 #} else{# #=year11Amount# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2030", "field": "year11Amount", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(year12Amount !== '' && year12Amount !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(year12Amount),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(year12Amount),'n0')# </span># } }else if(year12Amount == null){# 0 #} else{# #=year12Amount# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2031", "field": "year12Amount", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(year13Amount !== '' && year13Amount !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(year13Amount),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(year13Amount),'n0')# </span># } }else if(year13Amount == null){# 0 #} else{# #=year13Amount# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2032", "field": "year13Amount", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(year14Amount !== '' && year14Amount !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(year14Amount),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(year14Amount),'n0')# </span># } }else if(year14Amount == null){# 0 #} else{# #=year14Amount# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2034", "field": "year14Amount", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(year15Amount !== '' && year15Amount !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(year15Amount),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(year15Amount),'n0')# </span># } }else if(year15Amount == null){# 0 #} else{# #=year15Amount# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2035", "field": "year15Amount", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(year16Amount !== '' && year16Amount !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(year16Amount),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(year16Amount),'n0')# </span># } }else if(year16Amount == null){# 0 #} else{# #=year16Amount# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2036", "field": "year16Amount", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "encoded": false}, {"template": "<span>#if(year17Amount !== '' && year17Amount !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(year17Amount),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(year17Amount),'n0')# </span># } }else if(year17Amount == null){# 0 #} else{# #=year17Amount# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2037", "field": "year17Amount", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(year18Amount !== '' && year18Amount !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(year18Amount),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(year18Amount),'n0')# </span># } }else if(year18Amount == null){# 0 #} else{# #=year18Amount# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2038", "field": "year18Amount", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(year19Amount !== '' && year19Amount !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(year19Amount),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(year19Amount),'n0')# </span># } }else if(year19Amount == null){# 0 #} else{# #=year19Amount# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2039", "field": "year19Amount", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(year20Amount !== '' && year20Amount !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(year20Amount),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(year20Amount),'n0')# </span># } }else if(year20Amount == null){# 0 #} else{# #=year20Amount# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2034", "field": "year20Amount", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(prevchangeYear1 !== '' && prevchangeYear1 !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(prevchangeYear1),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(prevchangeYear1),'n0')# </span># } }else if(prevchangeYear1 == null){# 0 #} else{# #=prevchangeYear1# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2023", "field": "prevchangeYear1", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(changeYear1 !== '' && changeYear1 !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(changeYear1),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(changeYear1),'n0')# </span># } }else if(changeYear1 == null){# 0 #} else{# #=changeYear1# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2023", "field": "changeYear1", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(changeYear2 !== '' && changeYear2 !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(changeYear2),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(changeYear2),'n0')# </span># } }else if(changeYear2 == null){# 0 #} else{# #=changeYear2# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2023", "field": "changeYear2", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(changeYear3 !== '' && changeYear3 !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(changeYear3),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(changeYear3),'n0')# </span># } }else if(changeYear3 == null){# 0 #} else{# #=changeYear3# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2023", "field": "changeYear3", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(changeYear4 !== '' && changeYear4 !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(changeYear4),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(changeYear4),'n0')# </span># } }else if(changeYear4 == null){# 0 #} else{# #=changeYear4# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2023", "field": "changeYear4", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(changeYear5 !== '' && changeYear5 !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(changeYear5),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(changeYear5),'n0')# </span># } }else if(changeYear5 == null){# 0 #} else{# #=changeYear5# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2024", "field": "changeYear5", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(changeYear6 !== '' && changeYear6 !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(changeYear6),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(changeYear6),'n0')# </span># } }else if(changeYear6 == null){# 0 #} else{# #=changeYear6# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2025", "field": "changeYear6", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(changeYear7 !== '' && changeYear7 !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(changeYear7),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(changeYear7),'n0')# </span># } }else if(changeYear7 == null){# 0 #} else{# #=changeYear7# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2026", "field": "changeYear7", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(changeYear8 !== '' && changeYear8 !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(changeYear8),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(changeYear8),'n0')# </span># } }else if(changeYear8 == null){# 0 #} else{# #=changeYear8# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2027", "field": "changeYear8", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(changeYear9 !== '' && changeYear9 !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(changeYear9),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(changeYear9),'n0')# </span># } }else if(changeYear9 == null){# 0 #} else{# #=changeYear9# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2023", "field": "changeYear9", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(changeYear10 !== '' && changeYear10 !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(changeYear10),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(changeYear10),'n0')# </span># } }else if(changeYear10 == null){# 0 #} else{# #=changeYear10# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2023", "field": "changeYear10", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(changeYear11 !== '' && changeYear11 !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(changeYear11),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(changeYear11),'n0')# </span># } }else if(changeYear11 == null){# 0 #} else{# #=changeYear11# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2023", "field": "changeYear11", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(changeYear12 !== '' && changeYear12 !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(changeYear12),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(changeYear12),'n0')# </span># } }else if(changeYear12 == null){# 0 #} else{# #=changeYear12# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2023", "field": "changeYear12", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(changeYear13 !== '' && changeYear13 !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(changeYear13),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(changeYear13),'n0')# </span># } }else if(changeYear13 == null){# 0 #} else{# #=changeYear13# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2023", "field": "changeYear13", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(changeYear14 !== '' && changeYear14 !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(changeYear14),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(changeYear14),'n0')# </span># } }else if(changeYear14 == null){# 0 #} else{# #=changeYear14# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2023", "field": "changeYear14", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(changeYear15 !== '' && changeYear15 !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(changeYear15),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(changeYear15),'n0')# </span># } }else if(changeYear15 == null){# 0 #} else{# #=changeYear15# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2023", "field": "changeYear15", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(changeYear16 !== '' && changeYear16 !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(changeYear16),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(changeYear16),'n0')# </span># } }else if(changeYear16 == null){# 0 #} else{# #=changeYear16# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2023", "field": "changeYear16", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(changeYear17 !== '' && changeYear17 !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(changeYear17),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(changeYear17),'n0')# </span># } }else if(changeYear17 == null){# 0 #} else{# #=changeYear17# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2023", "field": "changeYear17", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(changeYear18 !== '' && changeYear18 !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(changeYear18),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(changeYear18),'n0')# </span># } }else if(changeYear18 == null){# 0 #} else{# #=changeYear18# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2023", "field": "changeYear18", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(changeYear19 !== '' && changeYear19 !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(changeYear19),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(changeYear19),'n0')# </span># } }else if(changeYear19 == null){# 0 #} else{# #=changeYear19# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2023", "field": "changeYear19", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}, {"template": "<span>#if(changeYear20 !== '' && changeYear20 !== null){if(lineItemId == -1){#<span class='semi'>#= kendo.toString(parseFloat(changeYear20),'n0') #</span># } else { #<span> #= kendo.toString(parseFloat(changeYear20),'n0')# </span># } }else if(changeYear20 == null){# 0 #} else{# #=changeYear20# #}#</span>", "hidden": false, "format": "{0:n0}", "title": "2023", "field": "changeYear20", "colCount": 0, "width": 100, "attributes": {"style": "white-space:nowrap;text-align:right;width:100px;border-left: none;vertical-align:top;"}, "headerAttributes": {"style": "text-align:right;border-right-width:0;border-left-width:0;border-bottom-width:0;"}, "attributeStyle": {"white-space": "nowrap", "text-align": "right", "width": "100px", "border-left": "none", "vertical-align": "top"}, "headerStyle": {"text-align": "right", "border-right-width": "0", "border-left-width": "0", "border-bottom-width": "2px"}, "encoded": false}]