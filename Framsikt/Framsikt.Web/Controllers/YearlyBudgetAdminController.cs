using Framsikt.BL;
using Framsikt.Web.Filters;
using Framsikt.Web.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Framsikt.Web.Controllers
{
    [CustomHandleError(Order = 3)]
    [Logging(Order = 2)]
    [Localized(Order = 1)]
    
    public class YearlyBudgetAdminController : Controller
    {
        private readonly IUtility _pUtility;
        private readonly IApplication _application;
        private readonly IYearlyBudget _yearlyBudget;

        public YearlyBudgetAdminController(IApplication app, IUtility utility, IYearlyBudget yearBudget)
        {
            _pUtility = utility;
            _application = app;
            _yearlyBudget = yearBudget;
        }

        public async Task<ActionResult> Overview()
        {
            Dictionary<string, string> userInfo = await UserHelper.GetUserInfoAsync(_pUtility, UserIdentity.GetName(User));
            Dictionary<string, string> appInfo = await _application.GetAppInfoAsync(UserIdentity.GetName(User));

            ViewBag.userDetails = userInfo;
            ViewBag.appDetails = appInfo;
            ViewBag.UserRoles = await _pUtility.GetUserRoleIdsAsync(UserIdentity.GetName(User));
            string paramValue = await _pUtility.GetActiveParameterValueAsync(UserIdentity.GetName(User), "USE_SALARY_TABLE");
            ViewBag.salaryTabAccess = paramValue.ToLower() == "true" ? true : false;
            string showImportTab = await _pUtility.GetActiveParameterValueAsync(UserIdentity.GetName(User), "SHOW_SALARY_IMPORT");
            ViewBag.showImportTab = ((List<int>)ViewBag.UserRoles).Contains(1)?true : !((List<int>)ViewBag.UserRoles).Contains(2)? false: showImportTab.ToLower() == "true" ? true : false ;
            return View();
        }

    }
}