var changedPopulationStatisticsRowID = '', 
    populationStatisticsDetailGridChangeChecked = '',
    populationStatisticsDescChanged = '',
    populationStatDetailAreaLineGrid = '',
    templateId,
    populationStatisticsIndicatorCode,
    populationStatisticsForecastType,
    serviceArea,
    kostraPopulationStatisticsIndicatorGrid,
    templateName,
    finalPopForecastDetailData,
    columnsIndicatorGrid = [],
    popforecastIndicatorGridDS = [],
    POPULATIONSTATISTICSDETAILS = {},
    POPULATIONSTATISTICSDETAILSEVENTS = {};

POPULATIONSTATISTICSDETAILS = {

    populationStatisticsIndicatorGridDS: function (serviceArea, populationStatisticsIndicatorCode) {
        $.ajax({
            contentType: 'application/json; charset=utf-8',
            url: '../PopulationStatistics/GetPopulationStatisticsDetailsIndicatorGrid?areaCode=' + serviceArea + '&templateId=' + templateId,
            type: "POST",
            dataType: 'JSON',
            success: function (response) {
                columnsIndicatorGrid = [];
                popforecastIndicatorGridDS = [];
                var popStatisticsDetailHeaderTitle = localStorage.getItem('PopDetailHeaderTitle');
                var colFields = ["PopStatisticsIndicatorID", "PopStatisticsIndicatorName", "PopStatisticsStatus"];
                var colTitles = ["id", popStatisticsDetailHeaderTitle, "status"];

                for (var j = 0; j < colFields.length; j++) {
                    columnsIndicatorGrid.push({ field: colFields[j], title: colTitles[j] });
                }

                columnsIndicatorGrid[1].attributes = { style: "text-align:left;" };
                columnsIndicatorGrid[1].template = '<div class="population-statistics-details-items"></div>';// to be done
                columnsIndicatorGrid[0].attributes = { style: "border-left: none;" };

                for (var r = 0; r < response.jsonData.length; r++) {
                    var obj = {};
                    obj[colFields[0]] = response.jsonData[r].PopStatisticsIndicatorID;
                    obj[colFields[1]] = response.jsonData[r].PopStatisticsIndicatorName;
                    obj[colFields[2]] = response.jsonData[r].PopStatisticsStatus;
                    popforecastIndicatorGridDS.push(obj);

                }

                POPULATIONSTATISTICSDETAILS.populationStatisticsIndicatorGrid(populationStatisticsIndicatorCode, popforecastIndicatorGridDS, columnsIndicatorGrid);
                EVENTS.loaderOFF("#populationStatDetailGrid2");

            },
            error: function (jqXHR, textStatus, errorThrown) {
                var methodName = 'populationStatisticsIndicatorGridDS';
                ERROR.displayException(jqXHR, textStatus, errorThrown, methodName);
                EVENTS.loaderOFF("#container");
            }
        });

    },

    populationStatisticsObjectFormation: function (populationStatisticsIndicatorCode) {
        var yearCount = localStorage.getItem('yearCount');
        var populationChk, gowthChk, foreCastPopChk, foreCastGrowthChk, foreCastExpChk;
        if (localStorage['HistoryPopulation'] == "true") {
            populationChk = true;
        }
        else {
            populationChk = false;
        }
        if (localStorage['HistoryGrowth'] == "true") {
            gowthChk = true;
        }
        else {
            gowthChk = false;
        }

        if (localStorage['ForeCostPop'] == "true") {
            foreCastPopChk = true;
        }
        else {
            foreCastPopChk = false;
        }
        if (localStorage['ForeCostGrowth'] == "true") {
            foreCastGrowthChk = true;
        }
        else {
            foreCastGrowthChk = false;
        }
        if (localStorage['ForeCostPopExp'] == "true") {
            foreCastExpChk = true;
        }
        else {
            foreCastExpChk = false;
        }
        var histroyForecostObj = {};
        if (localStorage['HistryForeCost'] == 0) {
            histroyForecostObj = {
                "templateId": templateId,
                "indicatorCode": populationStatisticsIndicatorCode,
                "history": {
                    "population": populationChk,
                    "growth": gowthChk,
                    "populationExpense": false,
                },
                "forecast": {
                    "population": false,
                    "growth": false,
                    "populationExpense": false,
                    "forecastType": '',
                    "forecastPeriod": '',
                }
            };
        }
        else {
            histroyForecostObj = {
                "templateId": templateId,
                "indicatorCode": populationStatisticsIndicatorCode,
                "history": {
                    "population": false,
                    "growth": false,
                    "populationExpense": false,
                },
                "forecast": {
                    "population": foreCastPopChk,
                    "growth": foreCastGrowthChk,
                    "populationExpense": foreCastExpChk,
                    "forecastType": populationStatisticsForecastType,
                    "forecastPeriod": parseInt(yearCount),
                }
            };
        }
       var finalJsonObj = JSON.stringify(histroyForecostObj);
       POPULATIONSTATISTICSDETAILS.populationStatisticsDetailGrid(finalJsonObj);

    },

    populationStatisticsIndicatorGrid: function (populationStatisticsIndicatorCode, popforecastIndicatorGridDS, columnsIndicatorGrid) {
        if (kostraPopulationStatisticsIndicatorGrid) {
            $('#populationStatDetailGrid2').empty();
            $("#populationStatDetailGrid2").data("kendoGrid").destroy();
        }

        kostraPopulationStatisticsIndicatorGrid = $("#populationStatDetailGrid2").kendoGrid({
            dataSource: {
                data: popforecastIndicatorGridDS
            },
            scrollable: false,
            sortable: false,
            selectable: true,
            columns: columnsIndicatorGrid,
            dataBound: function (e) {
                var grid = this;
               
                $("#populationStatDetailGrid2").data("kendoGrid").hideColumn(0);
                $("#populationStatDetailGrid2").data("kendoGrid").hideColumn(2);
                $("#populationStatDetailGrid2").find(".k-header").css("text-align", "left");
                $("#populationStatDetailGrid2").find(".k-header").css("padding", "9px");
                $("#populationStatDetailGrid2 tr").css("border-color", "#c3c3c3");
                $("#populationStatDetailGrid2 td").css("border-left", "0 none");

                $("#populationStatDetailGrid2 tr td>.population-statistics-details-items").each(function () {
                    var row = $(this).closest("tr");
                    var populationStatIndicatorModel = grid.dataItem(row);
                    var displayIndicator = '',popstatStatus='';
                    var string = populationStatIndicatorModel.PopStatisticsIndicatorName;

                    if (populationStatisticsIndicatorCode == populationStatIndicatorModel.PopStatisticsIndicatorID) {
                        grid.select(row);
                        populationStatisticsDetailGridChangeChecked = "done";

                    }
                    if (string.length > 15) {
                        displayIndicator = string.substring(0, 15) + '...';
                    }
                    else {
                        displayIndicator = string;
                    }

                    if (populationStatIndicatorModel.PopStatisticsStatus == "Complete") {
                        popstatStatus = "4_status_completed";
                    }
                    else if (populationStatIndicatorModel.PopStatisticsStatus == "NotStarted") {
                        popstatStatus = "1_status_incomplete";
                    }
                    else if (populationStatIndicatorModel.PopStatisticsStatus == "InProgress") {
                        popstatStatus = "3_status_inprogress";
                    }
                    else {
                        popstatStatus = "1_status_incomplete";
                    }
                    if (populationStatIndicatorModel.PopStatisticsIndicatorName != "total") {
                        $(this).html('<div class="col-md-12 padding0"><div class="col-md-1 padding0"><img src="../images/' + popstatStatus + '.png"/></div><div class="col-md-8" style="margin-top:3px;">' + displayIndicator + '</div></div>');
                    }
                    else {
                        $(this).html('<div class="row" style="padding-left: 25px;">' + " " + displayIndicator + '</div>');
                    }
                });
            },
            change: function (e) {
                var selectedRow = this.select();
                var modelRow = this.dataItem(selectedRow);

                changedPopulationStatisticsRowID = modelRow.PopStatisticsIndicatorID;
                populationStatisticsIndicatorCode = modelRow.PopStatisticsIndicatorID;

                if (populationStatisticsDetailGridChangeChecked == "done") {                
                    POPULATIONSTATISTICSDETAILS.publishedDocumentDropDownList(changedPopulationStatisticsRowID);                 
                    EVENTS.loaderON("#populationStatDetailAreaChart");
                    EVENTS.loaderON("#populationStatDetailAreaLineGrid");
                    EVENTS.loaderON("#documentTemplates");
                }
            }
        }).data("kendoGrid");
    },

    populationStatisticsDetailGrid: function (finalJsonObj) {
        $.ajax({
            contentType: 'application/json; charset=utf-8',
            url: '../PopulationStatistics/GetPopForecastDataByIndicatorType',
            type: "POST",
            dataType: 'JSON',
            data: finalJsonObj,
            success: function (response) {                
                POPULATIONSTATISTICSDETAILS.populationStatisticsAgeIntervalGridChartDS(response);             
                
            },
            error: function (jqXHR, textStatus, errorThrown) {
                var methodName = 'populationStatisticsIndicatorGridDS';
                ERROR.displayException(jqXHR, textStatus, errorThrown, methodName);
                EVENTS.loaderOFF("#container");
            }
        });

    },

    populationStatisticsAgeIntervalGridChartDS: function (response) {
        
                var popforecastGridDS = [];
                var popforecastGridColumns = [];
                var popForecastbackIndex = [];

            
                for (var r = 0; r < response.jsonData.length; r++) {
                    var obj = {};
                    obj[response.Fields[0]] = response.jsonData[r].indicatorCode;
                    obj[response.Fields[1]] = response.jsonData[r].Name;
                    obj[response.Fields[2]] = response.jsonData[r].status;
                    for (var s = 0; s < response.jsonData[r].gridData.length; s++) {
                      //  obj[response.Fields[s + 3]] = response.jsonData[r].gridData[s];
                        var numData = response.jsonData[r].gridData[s];
                        var numValue = kendo.toString(numData, response.numberType);
                        obj[response.Fields[s + 3]] = numValue;
                    }
                    popforecastGridDS.push(obj);
                }
            

                for (var t = 0; t < response.Titles.length; t++) {
                    if (response.Fields[t] !== "Status" && response.Fields[t] !== "popForecastindicatorCode") {
                        if (response.Fields[t] !== "Name") {
                            popforecastGridColumns.push({ field: response.Fields[t], title: response.Titles[t], attributes: { style: "text-align:right;border-left: none;" }, width: 70, type: "decimal" });
                        }
                        else {
                            popforecastGridColumns.push({ field: response.Fields[t], title: response.Titles[t], attributes: { style: "text-align:right;border-left: none;" }, width: 70 });
                        }
                        }
                }
               // popforecastGridColumns[0].width = "115px";
               // popforecastGridColumns[0].locked = true;
                popforecastGridColumns[0].attributes = { style: "text-align:left;border-left: none;" };
                popforecastGridColumns[0].template = '<div class="population-statistics-detailGrid-items"></div>';

                for (var k = 0; k < response['backGroundIndex'].length; k++) {
                    popforecastGridColumns[response['backGroundIndex'][k]].attributes = { style: "background:rgb(240,240,240);border-left: none;text-align:right;" };
                    popForecastbackIndex.push(response['backGroundIndex'][k]);
                }
                finalPopForecastDetailData = JSON.stringify({ dataSource: popforecastGridDS, columns: popforecastGridColumns });

                POPULATIONSTATISTICSDETAILS.populationStatisticsAgeIntervalGrid(popforecastGridDS, popforecastGridColumns, popForecastbackIndex);
                POPULATIONSTATISTICSDETAILS.populationStatisticsGraphCharts(response); 
                EVENTS.loaderOFF("#populationStatDetailAreaLineGrid");
                EVENTS.loaderOFF("#populationStatDetailAreaChart");
                EVENTS.loaderOFF("#container");
    },

    populationStatisticsGraphCharts: function (response) {

        var series = [];
        var populationStatisticsSpace = 0;
        for (var i = 0; i < response.jsonData.length; i++) {
            var PopulationStatisticsChartApp = response.jsonData[i];
            var popChartFormat = response.numberType;
            series.push({
                tooltipText: popChartFormat,
                data: PopulationStatisticsChartApp.chartData,
                name: PopulationStatisticsChartApp.Name,
                color: PopulationStatisticsChartApp.color,
                spacing: populationStatisticsSpace,
                type: PopulationStatisticsChartApp.type,
                stack: PopulationStatisticsChartApp.stack,
                tooltip: {
                visible: true,
                template: "#= series.name #: #= (kendo.toString(value,series.tooltipText)) #"
                 }
            });
        }
        POPULATIONSTATISTICSDETAILS.populationStatisticsOverviewChart1();
        var populationstatChart1 = $("#populationStatDetailAreaChart").data('kendoChart');
        populationstatChart1.options.categoryAxis.categories = response.Labels;
        populationstatChart1.options.series = series;
        populationstatChart1.refresh();


      

    },

    populationStatisticsAgeIntervalGrid: function (popforecastDetailGridDS, popforecastDetailGridColumns, popForecastDetailbackIndex) {
        if (populationStatDetailAreaLineGrid) {
            $('#populationStatDetailAreaLineGrid').data().kendoGrid.destroy();
            $('#populationStatDetailAreaLineGrid').empty();
        }
        populationStatDetailAreaLineGrid = $("#populationStatDetailAreaLineGrid").kendoGrid({
            dataSource: {
                data: popforecastDetailGridDS,
               // pageSize: 20

            },
            scrollable: false,
            excel: {
                filename: "Population Statistics.xlsx",
                proxyURL: "Save",
                allPages: true
            },
            columns: popforecastDetailGridColumns,
            dataBound: function (e) {
                var grid = this;
                $("#populationStatDetailAreaLineGrid").find(".k-header").css("text-align", "right");
                $("#populationStatDetailAreaLineGrid tr").css("border-color", "#c3c3c3");

                for (var l = 0; l < popForecastDetailbackIndex.length; l++) {
                    $("#populationStatDetailAreaLineGrid th").eq(popForecastDetailbackIndex[l]).css("background-color", "#f4f4f4");
                }

                var dataView = this.dataSource.view();
                for (var i = 0; i < dataView.length; i++) {
                    if (dataView[i].Name == "Total") {
                        var uid = dataView[i].uid;
                        $("#populationStatDetailAreaLineGrid tbody").find("tr[data-uid=" + uid + "]").addClass("semi");
                    }
                }

                $("#populationStatDetailAreaLineGrid tr td>.population-statistics-detailGrid-items").each(function () {
                    var row = $(this).closest("tr");
                    var popforecastModel = grid.dataItem(row);

                    if (popforecastModel.Name != "Total") {
                        $(this).html('<div class="row" style="margin-left: 5px;text-overflow: ellipsis;white-space: nowrap;">' + " " + popforecastModel.Name + '</div>');
                    }
                    else {
                        $(this).html('<div class="row" style="margin-left: 5px;">' + " " + popforecastModel.Name + '</div>');
                    }
                });

                var tabActiveIndex = localStorage['HistryForeCost'];
                var actIndex = localStorage['HistoryPopGrowth'];
                var actIndexs = localStorage['ForeCostPopGrowExp'];
                if (actIndex == 1 || actIndexs == 1) {
                    $('#populationStatDetailAreaLineGridTitle').text(localStorage.getItem("AgeIntPopGrowth"));
                    $('#populationStatDetailAreaLineGridDesc').text(localStorage.getItem("AgeIntPopDec"));
                }
                if (actIndex == 0 || actIndexs == 0) {
                    $('#populationStatDetailAreaLineGridTitle').text(localStorage.getItem("AgeIntPop"));
                    $('#populationStatDetailAreaLineGridDesc').text(localStorage.getItem("AgeIntPopDec"));
                }
                if (tabActiveIndex == 1 && actIndexs == 2) {
                    $('#populationStatDetailAreaLineGridTitle').text(localStorage.getItem("AgeIntPopGrowthExp"));
                    $('#populationStatDetailAreaLineGridDesc').text(localStorage.getItem("AgeIntPopGrowthExpDesc"));
                }

            }

        }).data("kendoGrid");

    },

    populationStatisticsOverviewChart1: function () {
        $("#populationStatDetailAreaChart").kendoChart({

            legend: {
                position: "bottom",
                offsetX: 0,
                offsetY: 0
            },
           /* seriesDefaults: {
                type: "area",
                stack: true
            },*/
            valueAxis: {
                labels: {
                    format: "{0:n0}",
                    color: "#606060",
                    visible: true
                },
                majorGridLines: {
                    visible: false,
                },
                axisCrossingValue: 0
            },
            categoryAxis: {
                color: "#606060",
                line: {
                    visible: true
                },
                majorGridLines: {
                    visible: false,
                },
                labels: {
                    padding: { top: 5 },
                    color: "#606060"
                }
            },
            chartArea: {
                background: ""
            },
           /* tooltip: {
                visible: true,
                format: "{0}%",
                template: "#= series.name #: #= value #"
            }*/
        }).data("kendoChart");
    },

    saveConfirmation: function () {
        var savePopupNotification = $("#popupNotification").kendoNotification({
            position: {
                pinned: true,
                top: 0,
                right: 0
            },
            autoHideAfter: 3000,
            stacking: "down",
            templates: [{
                type: "upload-success",
                template: $("#dataSavedTemplate").html()
            }]

        }).data("kendoNotification");

        savePopupNotification.show({
            message: "Saved"
        }, "upload-success");

    },

    lengthConfirmation: function () {
        var popupNotification = $("#popupNotification").kendoNotification({
            position: {
                pinned: true,
                top: 0,
                right: 0
            },
            autoHideAfter: 0,
            hideOnClick: true,
            stacking: "down",
            templates: [{
                type: "error",
                template: $("#lengthConfirmationTemplate").html()
            }]

        }).data("kendoNotification");

        popupNotification.show({
            message: localStorage.getItem("EditorTextLimit")
        }, "error");


    },

    publishedDocumentDropDownList: function (populationStatisticsIndicatorCode) {
        EVENTS.loaderON("#documentTemplates");
        $.ajax({
            contentType: 'application/json; charset=utf-8',
            url: "../Kostra/GetDocumentTemplates",
            type: "GET",
            dataType: 'JSON',
            success: function (result) {


                var templateDTEvalHTML = '';
                var evalActiveClass = '';
                $('.pop-det-eval').empty();
                for (var g = 0; g < result.length; g++) {

                    if (result[g].key == templateId) {
                        templateName = result[g].value;
                        evalActiveClass = "active";
                    }
                    else {
                        evalActiveClass = '';
                    }
                    if (g == 0) {
                        var marginClass = "margin-left0";
                    }
                    templateDTEvalHTML = '<li onclick=POPULATIONSTATISTICSDETAILS.getDTEvaluationData("' + result[g].key + '","' + populationStatisticsIndicatorCode + '"); class="kostra-section-tablink ' + evalActiveClass + ' ' + marginClass + '" id =dtEval' + result[g].key + '><a data-toggle="tab">' + result[g].value + '</a></li>';
                    $('.pop-det-eval').append(DOMPurify.sanitize(templateDTEvalHTML, domPurifyConfig));
                    $('.pop-det-eval').find('#dtEval'+result[g].key).attr({'onclick' : 'POPULATIONSTATISTICSDETAILS.getDTEvaluationData(\'' + result[g].key + '\',\''+ populationStatisticsIndicatorCode + '\')'});
                    marginClass = '';
                    templateDTEvalHTML = '';
                }


               /* for (var g = 0; g < result.length; g++) {

                    if (templateId == result[g].key) {
                        templateName = result[g].value;
                    }

                }*/

              /*  $("#docVersionDropdownnew").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: result,
                    value: templateId,
                    change: function (e) {
                        var index = e.sender.selectedIndex;
                        templateId = result[index].key;
                        templateName = result[index].value;
                        EVENTS.loaderON("#populationStatDetailAreaLineGrid");
                        EVENTS.loaderON("#populationStatDetailGrid2");
                        EVENTS.loaderON("#populationStatDetailAreaChart");
                        POPULATIONSTATISTICSDETAILS.populationStatisticsDetailGrid();
                        POPULATIONSTATISTICSDETAILS.populationStatisticsIndicatorGridDS(serviceArea, populationStatisticsIndicatorCode);
                        MODULES.getKostraAnalysisMainDescription("#kostraPopulationDetailEvaluationTitle", "#kostraPopulationDetailEvaluation", "#kostraPopulationDetailEvaluationEdit", templateName, "Retrieve", populationStatisticsIndicatorCode, "PO", templateId, 1, "", "#populationDetailsHistory");

                    }
                });*/
                POPULATIONSTATISTICSDETAILS.populationStatisticsObjectFormation(populationStatisticsIndicatorCode);

                MODULES.getDetailAnalysisMainDescription("#kostraPopulationDetailEvaluationTitle", "#kostraPopulationDetailEvaluation", "#kostraPopulationDetailEvaluationEdit", templateName, "Retrieve", populationStatisticsIndicatorCode, "PO", templateId, 1, "", "populationDetailsHistory", "#chkPopDetailIncluded", "#chkPopDetailLockContent");
                EVENTS.loaderOFF("#documentTemplates");
            },

            error: function (jqXHR, textStatus, errorThrown) {
                var methodName = 'publishedDocumentDropDownList';
                EVENTS.loaderOFF("#documemtTemplates");
                ERROR.displayException(jqXHR, textStatus, errorThrown, methodName);
            }
        })
    },

    getDTEvaluationData: function (key, populationStatisticsIndicatorCode) {
        templateId = key;
        var popDTTemName = $("#dtEval" + key + " a").text();
        MODULES.getDetailAnalysisMainDescription("#kostraPopulationDetailEvaluationTitle", "#kostraPopulationDetailEvaluation", "#kostraPopulationDetailEvaluationEdit", popDTTemName, "Retrieve", populationStatisticsIndicatorCode, "PO", key, 1, "", "populationDetailsHistory", "#chkPopDetailIncluded", "#chkPopDetailLockContent");
        POPULATIONSTATISTICSDETAILS.populationStatisticsObjectFormation(populationStatisticsIndicatorCode);
   },

    savePopAnalysisDescription: function () {
        EVENTS.loaderON("#kostraPopulationDetailEvaluationTitle")
        var chkIncludeAnalysisVal = '';
        var chkLockedContentVal = '';
        var indicatorType = '';

        chkIncludeAnalysisVal = $("#chkPopDetailIncluded").prop("checked");
        chkLockedContentVal = $("#chkPopDetailLockContent").prop("checked");
        //indicatorType

        $.ajax({
            contentType: 'application/json; charset=utf-8',
            url: "../kostra/SaveInlcudeInDocument?indicatorCode=" + changedPopulationStatisticsRowID + "&templateId=" + templateId + "&inlcudeInDoc=" + chkIncludeAnalysisVal + "&lockContent=" + chkLockedContentVal + "&indicatorType=PO",
            type: "POST",
            dataType: 'JSON',
            success: function (result) {
                MODULES.saveConfirmation();

                if ($("#chkPopDetailLockContent").prop("checked") == true) {
               
                    $("#kostraPopulationDetailEvaluationEdit").hide();
                }
                else {
                    $("#kostraPopulationDetailEvaluationEdit").show();
                }
                EVENTS.loaderOFF("#kostraPopulationDetailEvaluationTitle");
            },
            error: function () {
                var methodName = 'savePopAnalysisDescription';
                ERROR.displayException(jqXHR, textStatus, errorThrown, methodName);
                EVENTS.loaderOFF("#kostraPopulationDetailEvaluationTitle");
            }
        });
    }
}

POPULATIONSTATISTICSDETAILSEVENTS = {

    getParameterByName: function (name) {
        name = name.replace(/[\[]/, "\\[").replace(/[\]]/, "\\]");
        var regex = new RegExp("[\\?&]" + name + "=([^&#]*)"),
        results = regex.exec(location.search);
        return results === null ? "" : decodeURIComponent(results[1].replace(/\+/g, " "));
    },

    populationStatisticsGoBack: function () {
        var confirmString = localStorage.getItem("Confirm");
        var populationStatisticsConfirmWindow = $("<div />").kendoWindow({
            width: "600px",
            title: confirmString,
            resizable: false,
            modal: true
        });
        localStorage['HistoryPopulation'] = '';
        localStorage['HistoryGrowth'] = '';
        localStorage['ForeCostPop'] = '';
        localStorage['ForeCostGrowth'] = '';
        localStorage['ForeCostPopExp'] = 'GoBackPP';
        if (populationStatisticsDescChanged == 'yes') {
            populationStatisticsConfirmWindow.data("kendoWindow")
               .content($("#populationStatisticsGoBackConfiramation").html())
               .center().open();

            $(".popstat-confirm-yes").click(function () {
                parent.history.back();
                populationStatisticsConfirmWindow.data("kendoWindow").close();
                return false;
            });

            $(".popstat-confirm-no").click(function () {
                populationStatisticsConfirmWindow.data("kendoWindow").close();
                return false;
            });

        }
        else {
            parent.history.back();
            return false;
        }
    },

    chartResize: function () {
        $(document).on('shown.bs.tab', 'a[data-toggle="tab"]', function (e) {

            $("#populationStatDetailAreaChart").resize();

            $("#populationStatDetailLineChart").resize();

            $("#populationStatDetailForecastChart").resize();

        });

        $(window).resize(function () {
            $(".k-chart").each(function () {
                var chart = kendo.widgetInstance($(this), kendo.dataviz.ui);
                chart.options.transitions = false;
                chart.redraw();
            });
        });
    },

    eventHandling: function () {
        $('#populationStatDetailSectionA').bind('click', function (e) {
            EVENTS.loaderON("#container");
            $('#popstatDetailsAgeIntervalSection').removeClass('popgrid-display');
            setTimeout(function () {
                $("#populationStatDetailAreaChart").resize();
                $("#populationStatDetailAreaLineGrid").data("kendoGrid").refresh();
                EVENTS.loaderOFF("#container");
            }, 2000);
        });
        $("#PopStatExportDiv").hide();

        $('#exportTo').attr("disabled", "disabled");

        $('.chkexp').change(function () {
            var total = $('input[class="chkexp"]:checked').length;
            if (total == 0) {
                $('#exportTo').attr("disabled", "disabled");
            }
            else {
                $('#exportTo').removeAttr("disabled")
            }
        });


        $(document).on("click", "#PopStatDetailexport", function (e) {

            $("#PopStatExportDiv").kendoWindow({
                width: "460px",
                draggable: true,
                title: localStorage.getItem("cmnExportAs"),
                modal: true,
                visible: false,
                resizable: false,
                actions: ["Close"]
            }).data("kendoWindow").center().open();
            return false;
        });

        $("#exportTo").on("click", function () {

            EVENTS.loaderON('#PopStatExportDiv');

            if ($('#expGraph').is(":checked")) {
               
               $("#populationStatDetailAreaChart").css({ "background-color": "white" });

                    kendo.drawing.drawDOM($("#populationStatDetailAreaChart"))
                   .then(function (group) {
                       group.transform(
                         kendo.geometry.transform().scale(2, 2)
                       );

                       return kendo.drawing.exportImage(group);
                   })
                   .done(function (data) {
               
                    $("#populationStatDetailAreaChart").css({ "background-color": "" });
                    $("#PopStatExportDiv").data("kendoWindow").close();
                    EVENTS.loaderOFF('#PopStatExportDiv');

                       kendo.saveAs({
                           dataURI: data,
                           fileName: "Population Statistics Area Chart.jpg",
                           proxyURL: "Save",
                       });
                   });               
                
            }
            if ($('#expExcel').is(":checked")) {

                var actionURL = '../Content/ExportGridToExcel';
                var getURL = '../Content/DownloadExcel';

                var t = JSON.parse(finalPopForecastDetailData);
               for (var g = 0; g < t.dataSource.length; g++) {
                    delete t.dataSource[g].popForecastindicatorCode;
                    delete t.dataSource[g].Status;
                }
                var finalJsonObjToService = JSON.stringify(t);
                $.ajax({
                    contentType: 'application/json; charset=utf-8',
                    url: actionURL,
                    data: finalJsonObjToService,
                    type: "POST",
                    dataType: 'JSON',
                    success: function (response) {
                        if (response.success) {
                            window.location = DOMPurify.sanitize(getURL + "?fName=" + response.fName, domPurifyConfig);
                        }
                        EVENTS.loaderOFF('#PopStatExportDiv');
                        $("#PopStatExportDiv").data("kendoWindow").close();
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        $("#PopStatExportDiv").data("kendoWindow").close();
                        var methodName = 'KostraOverviewGridExport';
                        ERROR.displayException(jqXHR, textStatus, errorThrown, methodName);
                    }

                });


                /*
                    var grid = $("#populationStatDetailAreaLineGrid").data("kendoGrid");
                    grid.saveAsExcel();
                    */
                
            }
            $("input[class=chkexp]").each(function () {
                this.checked = false;
            });
            $("#PopStatExportDiv").data("kendoWindow").close();
        });        
    }

}

$(document).ready(function () {    
    EVENTS.loaderON("#populationStatDetailAreaLineGrid");
    EVENTS.loaderON("#populationStatDetailGrid2");
    EVENTS.loaderON("#populationStatDetailAreaChart"); 
    EVENTS.loaderON("#container");
    serviceArea = POPULATIONSTATISTICSDETAILSEVENTS.getParameterByName('ServiceAreaID');
    populationStatisticsIndicatorCode = POPULATIONSTATISTICSDETAILSEVENTS.getParameterByName('indicatorCode');
    populationStatisticsForecastType = POPULATIONSTATISTICSDETAILSEVENTS.getParameterByName('forecastType');
    templateId = localStorage.getItem("kostraTemplateID");
    POPULATIONSTATISTICSDETAILS.publishedDocumentDropDownList(populationStatisticsIndicatorCode);
    POPULATIONSTATISTICSDETAILS.populationStatisticsIndicatorGridDS(serviceArea, populationStatisticsIndicatorCode);
    POPULATIONSTATISTICSDETAILSEVENTS.chartResize();
    POPULATIONSTATISTICSDETAILSEVENTS.eventHandling();

});
