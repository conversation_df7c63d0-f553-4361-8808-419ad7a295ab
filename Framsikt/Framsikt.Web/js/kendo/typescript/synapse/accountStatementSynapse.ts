
declare var MODULES: any;
declare var EVENTS: any;
declare var ERROR: any;
declare var ORGMENU: any;
var domPurifyConfig = { SAFE_FOR_JQUERY: true };

module synapse {
    interface IAccountSynapseScope extends ng.IScope {
        vm: synapse;
        synapseBudgetYearOptions: kendo.ui.DropDownListOptions;
        accountGridOptions: kendo.ui.TreeListOptions;
        periodGridOptions: kendo.ui.TreeListOptions;
        accountReportingSearchGridSynapseOptions: kendo.ui.GridOptions;
        accountReportingGridSynapseOptions: kendo.ui.TreeListOptions;
        accountReportingTreeGridSynapseOptions: kendo.ui.TreeListOptions;
        reportingFinStatusAccountDetailGridSynapseOptions: kendo.ui.GridOptions;
    }

    type BudgetYearResponse = {
        key: number,
        value: string,
        isDefault: boolean,
        isChecked: boolean
    }
    type FilterMainResponse = {
        filterHeader: string,
        filterId: string,
        totalCount: number,
        filterDropdown: any
    }

    type StyleAttribute = {
        style?: string;
    }

    type KeyValue = {
        KeyId?: string;
        ValueString?: string;
        key?: string,
        value?: string,
        isSelected?: boolean,
        departmentText?: string,
        departmentValue?: string,
        fdvDescription?: string,
        fdvCode?: number | string
    }

    type MRFinanaceReportGrid = {
        data: gridData[],
        totalRows: number
    }



    type gridData = {
        Account: string,
        Amount: number,
        Code: string,
        Date: string,
        Freedim: string,
        Function: string,
        Id: string,
        Invoice: string,
        Journo: string,
        Period: string,
        Project: string,
        ReskAccount: string,
        Responsibility: string,
        Text: string
    }
    type getAccountReportingGrid = {

        departments: [],
        functions: [],
        projects: [],
        freeDim1: [],
        freeDim2: [],
        nextorg: [],
        servs: [],
        monthYear: string,
        level1OrgId: string,
        level2OrgId: string,
        level3OrgId: string,
        level4OrgId: string,
        level5OrgId: string,
        orgId: string,
        orgLevel: number,
        orgName: string,
        serviceId: string,
        serviceName: string,
        accountCodeId: string,
        budget_year: number,
        period: number,
        isPeriodCheck: boolean,
        sortField: string,
        sortOrder: string,
        accountCodeName: string

    }

    type jqueryStr = string | number | string[];
    let accountTotalRow: totalRowAccount[];
    let accountGridVar: any;

    type totalRowAccount = {
        Account: string,
        Responsibility: string,
        Function: string,
        Freedim: string,
        Project: string,
        Amount: string,
        Period: string,
        Date: string,
        Journo: string,
        ReskAccount: string,
        Invoice: string,
        Code: string,
        Text: string
    };

    class synapse {
        startYear: number;
        endYear: number;
        budgetYear = 2019;
        selectedMonthValue: any = '';
        orgMenuLevel: number = 0;
        orgMenuId: string = '';
        orgIdLevel1: string = '';
        orgIdLevel2: string = '';
        orgIdLevel3: string = '';
        orgIdLevel4: string = '';
        orgIdLevel5: string = '';
        orgIdLevel6: string = '';
        orgIdLevel7: string = '';
        orgIdLevel8: string = '';
        orgLastLevel: string = '';
        orgName: string = '';
        filteredInputText: Array<[]> = [];
        filteredValues: Array<[][]> = [];
        totalValues: Array<any> = [];
        expandFilterSection: boolean = true;
        showApplyFilter: boolean = false;
        clearFilterShow: Array<boolean> = [];
        showCloseImage: Array<boolean> = [];
        filterDataResponse: Array<any> = [];
        checkMore: Array<boolean> = [];
        selectedOrgHeaderText: string;
        updatedDate: string;
        accountStatementColumns: string;
        showAccountStatementColumns: boolean = false;
        accColumnSelectorToolTipOptions: kendo.ui.TooltipOptions;
        graphOrgFilter: string = '';
        graphAccFilter: string = '';
        graphServiceFilter: string = '';
        showGraphView: boolean = false;
        showAccountView: boolean = false;
        showServiceGraph: boolean = false;
        showAbsenseView:boolean = false;
        showPeriodicView = false;
        ASBarGraph2Dialogue: any;
        accBudOrgGraph: any;
        excelExportData: any;
        orgViewFilterDS = [];
        orgViewFilterKey = '';
        accountFilterLoaded = false;

        // acct stmt by period
        acctStmtPeriodData = "";
        synapsePeriodColumns = [];
        filteredPeriodInputText: Array<[]> = [];
        filteredPeriodValues: Array<[][]> = [];
        totalPeriodValues: Array<any> = [];
        expandPeriodFilterSection = true;
        showApplyPeriodFilter = false;
        clearPeriodFilterShow: Array<boolean> = [];
        showPeriodCloseImage: Array<boolean> = [];
        filterPeriodDataResponse: Array<any> = [];
        checkMorePeriod: Array<boolean> = [];
        excelExportPeriodData: {};
        periodFilterLoaded = false;
        accountReportingSearchGridSynapse: any;
        accountStmtWindow: kendo.ui.Window;
        MRReportingFinancialStatusWindow: kendo.ui.Window;
        selectedAccountDetailId = "";
        selectedAccountDetailorgViewId="";
        selectedAccountDetailName = "";
        selectedServiceId = "";
        selectedServiceName = "";
        financialStatusAccountPopupTotalRows = 0;
        budgetHistoryPopupSelectedId = "";
        budgetHistoryOrgViewPopupSelectedId = "";
        orgIdForOrgView = "";
        budgetHistoryPopupSelectedPeriodCheck: boolean = false;
        budgetHistoryPopupSelectedAccountName = "";
        absenceDataResponseGraph1: any
        absenceDataResponseGraph2: any
        absenceDataResponseGraph3: any
        absenceDataResponseGraph4: any
        FinancialStatusSelectedOrgName = "";
        budgetHistoryOrgName = "";
        loadAllFilterItems: Array<boolean> = [];
        loadAllPeriodFilterItems: Array<boolean> = [];


        constructor(protected $scope: IAccountSynapseScope, protected $http: ng.IHttpService, public $promise: ng.IPromise<any>, public $location: ng.ILocationService, public $timeout: ng.ITimeoutService, public $compile: ng.ICompileService) {
            this.getMonthSelector();
            EVENTS.loaderON(".wrapper-container");
        }
        

        getDate() {
            let today: any;
            today = new Date();

            var dd = today.getDate();
            var mm = today.getMonth() + 1; //January is 0!
            var yyyy = today.getFullYear();

            if (dd < 10) {
                dd = '0' + dd;
            }
            if (mm < 10) {
                mm = '0' + mm;
            }
            today = yyyy + '-' + mm + '-' + dd;
            return today;
        }
        //get month selector
        getMonthSelector() {

            let currentDate = this.getDate();
            this.$http.get<BudgetYearResponse[]>("../AccountStatementReport/GetMonthYearFilter?jsDateTime=" + currentDate).then(response => {
                let responseData: BudgetYearResponse[] = response.data;
                if (localStorage.getItem("ASYearSelectorVal")) {
                    this.selectedMonthValue = localStorage.getItem("ASYearSelectorVal");
                }
                else {
                    for (var g = 0; g < responseData.length; g++) {
                        if (responseData[g].isChecked == true) {
                            this.selectedMonthValue = responseData[g].key;
                            localStorage.setItem("ASYearSelectorVal", this.selectedMonthValue);
                        }
                    }
                }
                this.$scope.synapseBudgetYearOptions = {
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: responseData,
                    change: (ev: kendo.ui.DropDownListChangeEvent) => {
                        this.selectedMonthValue = $("#synapseBudgetYearIdDropdown").data("kendoDropDownList").value();
                        localStorage.setItem("ASYearSelectorVal", this.selectedMonthValue);
                        EVENTS.loaderOFF(".wrapper-container");
                        EVENTS.loaderON(".wrapper-container");
                        ORGMENU.orgStructureLoad(this.selectedMonthValue, true);
                    },
                    value: this.selectedMonthValue,
                    enable: true
                };
                ORGMENU.orgStructureLoad(this.selectedMonthValue, true);
            }, error => {
                EVENTS.loaderOFF(".wrapper-container");
                ERROR.displayException(error.data, error.status, error.data, "getMonthSelector");
            });
        }
        //Org structure implemnetation
        orgLevelSetup() {

            this.orgMenuLevel = 0;
            this.orgMenuId = '';
            // org level service values from selected service dropdown menu
            this.orgIdLevel1 = localStorage.getItem("orgValueLevel1") || null;
            this.orgIdLevel2 = localStorage.getItem("orgValueLevel2") || null;
            this.orgIdLevel3 = localStorage.getItem("orgValueLevel3") || null;
            this.orgIdLevel4 = localStorage.getItem("orgValueLevel4") || null;
            this.orgIdLevel5 = localStorage.getItem("orgValueLevel5") || null;
            this.orgIdLevel6 = localStorage.getItem("orgValueLevel6") || null;
            this.orgIdLevel7 = localStorage.getItem("orgValueLevel7") || null;
            this.orgIdLevel8 = localStorage.getItem("orgValueLevel8") || null;
            this.orgLastLevel = localStorage.getItem("orgValueLastLevel");

            if (this.orgIdLevel1 !== null) { //level 1

                this.orgMenuLevel = 1;
                this.orgMenuId = this.orgIdLevel1.toString();

                if (this.orgIdLevel2 !== null) { //level 2
                    this.orgMenuLevel = 2;
                    this.orgMenuId = this.orgIdLevel2.toString();
                    if (this.orgIdLevel3 !== null) { //level 3
                        this.orgMenuLevel = 3;
                        this.orgMenuId = this.orgIdLevel3.toString();

                        if (this.orgIdLevel4 !== null) { //level 4
                            this.orgMenuLevel = 4;
                            this.orgMenuId = this.orgIdLevel4.toString();

                            if (this.orgIdLevel5 !== null) { //level 5
                                this.orgMenuLevel = 5;
                                this.orgMenuId = this.orgIdLevel5.toString();

                                if (this.orgIdLevel6 !== null) { //level 6
                                    this.orgMenuLevel = 6;
                                    this.orgMenuId = this.orgIdLevel6.toString();

                                    if (this.orgIdLevel7 !== null) { //level 7
                                        this.orgMenuLevel = 7;
                                        this.orgMenuId = this.orgIdLevel7.toString();

                                        if (this.orgIdLevel8 !== null) { //level 8
                                            this.orgMenuLevel = 8;
                                            this.orgMenuId = this.orgIdLevel8.toString();
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            // selected org level name
            this.orgName = localStorage.getItem("orgValueLevelSelectName") || "";
            EVENTS.loaderOFF(".wrapper-container");
            this.getMROrgText();
            this.getUpdatedBudgetDate();
            this.filteredValues = [];
            this.filteredPeriodValues = [];
            this.showApplyFilter = false;
            this.showApplyPeriodFilter = false;
            //this.getFilterData();
            //this.getPeriodFilterData();
            $("#AccountingStatmentTabSec .tab-section-tablink").removeClass('active');

            this.$http.get<string>("../api/Common/GetApplicationSetting?key=DeactivateEconomyTabs").then(response => {
                if (response.data.toLowerCase() === "true") {
                    this.loadSynapseTabs('AccountStatmentSynapseTabD');
                    $("#AccountStatmentSynapseTabD").addClass('active');
                }
                else {
                    $("#AccountStatmentSynapseTabA").removeClass('cmn-display-none');
                    $("#AccountStatmentSynapseTabB").removeClass('cmn-display-none');
                    $("#AccountStatmentSynapseTabC").removeClass('cmn-display-none');
                    if (localStorage.getItem("accountTabs")) {
                        this.graphOrgFilter = '';
                        this.graphAccFilter = '';
                        this.graphServiceFilter = '';
                        this.loadSynapseTabs(localStorage.getItem("accountTabs"));
                        $("#" + localStorage.getItem("accountTabs")).addClass('active');

                    } else {
                        this.loadSynapseTabs('AccountStatmentSynapseTabA');
                        $("#AccountStatmentSynapseTabA").addClass('active');
                    }
                }
            }, error => {
                ERROR.displayException(error.data, error.status, error.data, "GetApplicationSetting");
            });        
        }
        loadSynapseTabs(tabName: string) {
            localStorage.setItem("accountTabs", tabName);
            switch (tabName) {
                case "AccountStatmentSynapseTabA":
                    this.showGraphView = false;
                    this.showAccountView = true;
                    this.showPeriodicView = false;
                    this.showAbsenseView = false;
                    this.getOrgFilterData();   
                    this.getFilterData();
                    this.$scope.accountGridOptions = {};
                    break;
                case "AccountStatmentSynapseTabB":
                    this.showGraphView = true;
                    this.showAccountView = false;
                    this.showPeriodicView = false;
                    this.showAbsenseView = false;
                    this.loadSynapseGraph();
                    break;
                case "AccountStatmentSynapseTabC":
                    this.showGraphView = false;
                    this.showAccountView = false;
                    this.showPeriodicView = true;
                    this.showAbsenseView = false;
                    this.getPeriodTreelistColumnSelector();
                    this.getPeriodFilterData();
                    this.$scope.periodGridOptions = {};
                    break;
                case "AccountStatmentSynapseTabD":
                    this.showGraphView = false;
                    this.showAccountView = false;
                    this.showPeriodicView = false;
                    this.showAbsenseView = true;
                    this.loadSynapseAbsenseGraph();
                    break;
            }
        }
        getMROrgText() {
            this.$http.get<[]>("../api/AccountStmtReportApiController/GetTenantOrgHeaderText?selectedOrgName=" + encodeURIComponent(this.orgName) + "&orgId=" + + this.orgMenuId + "&orgLevel=" + this.orgMenuLevel + "&monthYear=" + this.selectedMonthValue).then(response => {
                let responseData: any = response.data;
                this.selectedOrgHeaderText = responseData.headertext;
            }, error => {
                ERROR.displayException(error.data, error.status, error.data, "getMROrgText");
            });
        }
        getUpdatedBudgetDate() {
            var currentDate = this.selectedMonthValue;
            var splitdate = currentDate.split(" ")[0];
            const dd = splitdate.split("/")[0];
            const year = splitdate.split("/")[2];
            this.$http.get<[]>("../api/AccountStmtReportApiController/GetLastUpdatedDate?budgetYear=" + year).then(response => {
                let responseData: any = response.data;
                this.updatedDate = responseData;
            }, error => {
                ERROR.displayException(error.data, error.status, error.data, "getUpdatedBudgetDate");
            });
        }
        getFilterData() {

            EVENTS.loaderON("#accStatementFilterSection");
            var currentDate = this.selectedMonthValue;
            var splitdate = currentDate.split(" ")[0];
            const dd = splitdate.split("/")[0];
            const year = splitdate.split("/")[2];
            //let currentDate = this.getDate();
            this.$http.get<FilterMainResponse[]>("../api/AccountStatement/GetFilterData?budgetYear=" + year + "&orgLevel=" + this.orgMenuLevel + "&orgId=" + this.orgMenuId).then(response => {
                //  this.getAccountsGridData();

                let filterJson: FilterMainResponse[] = response.data;
                this.filterDataResponse = filterJson;
                let filterHTML = '', filterInnerHTML = '', countHtml = '';
                for (let g = 0; g < filterJson.length; g++) {
                    filterInnerHTML = '';
                    this.filteredValues[filterJson[g].filterId] = [];
                    this.clearFilterShow[g] = false;
                    this.showCloseImage[g] = false;
                    this.loadAllFilterItems[g] = false;
                    let dropdownValue = '';
                    for (let h = 0; h < filterJson[g].filterDropdown.length; h++) {
                        if (filterJson[g].filterDropdown[h].Value.length > 26) { dropdownValue = filterJson[g].filterDropdown[h].Value.substring(0, 15) + '..'; } else { dropdownValue = filterJson[g].filterDropdown[h].Value; }
                        filterInnerHTML = filterInnerHTML + '<li style="height:23px;" ng-click="vm.selectUnselectFilterList(\'filter' + filterJson[g].filterId + filterJson[g].filterDropdown[h].Key + '\',\'' + filterJson[g].filterDropdown[h].Key + '\',\'' + filterJson[g].filterId + '\',\'' + g + '\')" id=filter' + filterJson[g].filterId + filterJson[g].filterDropdown[h].Key + ' class="hand-pointer bottom5 padding1 filter-item"><div class="col-md-3 padding0">' + filterJson[g].filterDropdown[h].Key + '</div><div class="col-md-9 padding0" title=\'' + filterJson[g].filterDropdown[h].Value + '\'>' + dropdownValue + '</div></li>';
                    }
                    this.totalValues[g] = filterJson[g].filterDropdown.length;
                    if (filterJson[g].totalCount > this.totalValues[g]) {
                        this.checkMore[g] = true;
                    }
                    else {
                        this.checkMore[g] = false;
                    }
                    if (this.totalValues[g] >= filterJson[g].totalCount) {
                        countHtml = '<div class="col-md-12 align-center" id="listCountData' + g + '">' + filterJson[g].filterDropdown.length + ' ' + $('#cmnShownLabel').html() + '<a ng-show="vm.checkMore[' + g + ']" ng-click="vm.showMoreFilterData(\'listFilterData' + g + '\',\'' + 'listCountData' + g + '\',\'' + g + '\',\'' + filterJson[g].totalCount + '\',\'' + filterJson[g].filterId + '\',0,false)">&nbsp' + $('#cmnShowLessLabel').html() + '</a></div>';
                    } else {
                        countHtml = '<div class="col-md-12 align-center" id="listCountData' + g + '">' + filterJson[g].filterDropdown.length + ' ' + $('#cmnShownLabel').html() + '<a ng-show="vm.checkMore[' + g + ']" ng-click="vm.showMoreFilterData(\'listFilterData' + g + '\',\'' + 'listCountData' + g + '\',\'' + g + '\',\'' + filterJson[g].totalCount + '\',\'' + filterJson[g].filterId + '\',\'' + this.totalValues[g] + '\',false)">&nbsp' + localStorage.getItem('showAllText') + '</a></div>';
                    }
                    $('#commonSynapseFilterWrapper').html(' ');
                    filterHTML = filterHTML + '<div class="col-md-12 padding0 padding-bottom10">' +
                        '<a href="#" class="black-color font13 collapsed" data-toggle="collapse" data-parent="#accordion" data-target="#collapseAccountFilter' + g + '"> <i class="fa fa-chevron-down collapse-drop-icon" aria-hidden="true"> </i>' + filterJson[g].filterHeader + '</a>' +
                        '<div class="panel-collapse collapse" id="collapseAccountFilter' + g + '">' +
                        '<div class="col-md-12">' +
                        '<div class="col-md-6"></div>' +
                        '<div class="col-md-6 padding0"> <span style="position:absolute;top:-14px;font-size:12px;" > ' + localStorage.getItem('selectAll') + '&nbsp <input class="selectAllChk" type="checkbox" id="selectAllChk_' + filterJson[g].filterId + '" ng-click="vm.selectAllFilter(\'' + filterJson[g].filterId + '\',\'' + g + '\')" /> </span><div style="float:right;" ng-show="vm.clearFilterShow[' + g + ']"><div class="btn btn-primary border-radius0" style="position:absolute;top:-14px;padding:0px 2px 0px 2px;left:70%;font-size:12px;" ng-click="vm.clearPerFilter(\'' + filterJson[g].filterId + '\',\'' + g + '\')">&nbsp' + localStorage.getItem('clearText') + '</div>' +
                        '</div></div>' +
                        '</div>' +
                        '<input type="textbox" style="width:88%;margin-left:17px;" class="top10" ng-model="vm.filteredInputText[' + g + ']" ng-keyup="vm.searchFilterData(\'listFilterData' + g + '\',\'' + g + '\',$event,\'' + filterJson[g].filterId + '\',\'' + filterJson[g].totalCount + '\')">' +
                        '<ul class="col-md-12 top10 list-style-none min-height20 filter-custom-height" id="listFilterData' + g + '">' + filterInnerHTML +
                        '</ul>' + countHtml +
                        '</div>' +
                        '</div>';
                }
                let content = this.$compile(filterHTML)(this.$scope);
                $('#commonSynapseFilterWrapper').append(content);
                // to avoid loading filters again on tab click
                this.accountFilterLoaded = true;
                EVENTS.loaderOFF("#accStatementFilterSection");
            }, error => {
                EVENTS.loaderOFF("#accStatementFilterSection");
                ERROR.displayException(error.data, error.status, error.data, "getFilterData");
            });
        }
        showMoreFilterData(id: any, countId: any, val, totalCount, filterId, skip, selectAllChk) {
            var currentDate = this.selectedMonthValue;
            var splitdate = currentDate.split(" ")[0];
            const dd = splitdate.split("/")[0];
            const year = splitdate.split("/")[2];
            let isLoadAllData = true;
            if (skip == '0') {
                this.showCloseImage[val] = false;
                this.filteredInputText[val] = [];
                this.filteredValues[filterId] = [];
                this.clearFilterShow[val] = false;
                $("#" + id).html('');
                isLoadAllData = false;
                this.loadAllFilterItems[val] = false;
                $("#selectAllChk_" + filterId).prop('checked', false);
            } else {

                this.loadAllFilterItems[val] = true;
            }
            $("#" + countId).html('');
            EVENTS.loaderON("#" + id);
            //for show/hide apply filter
            this.showApplyFilter = false;
            for (let i = 0; i < this.filterDataResponse.length; i++) {
                if (this.filteredValues[this.filterDataResponse[i].filterId].length > 0) {
                    this.showApplyFilter = true;
                }
            }
            this.$http.get("../api/AccountStatement/GetFilterDataById?budgetYear=" + year + "&orgLevel=" + this.orgMenuLevel + "&orgId=" + this.orgMenuId + "&filterId=" + filterId + "&skip=" + skip + "&isLoadAll= " + isLoadAllData).then(response => {

                     let filterJson: any = response.data;
                    this.filterDataResponse[val].filterDropdown = [];
                    let filterInnerHTML = '', countHtml = '';
                    let dropdownValue = '';
                    for (let h = 0; h < filterJson.filterDropdown.length; h++) {
                        if (filterJson.filterDropdown[h].Value.length > 26) { dropdownValue = filterJson.filterDropdown[h].Value.substring(0, 15) + '..'; } else { dropdownValue = filterJson.filterDropdown[h].Value; }
                        filterInnerHTML = filterInnerHTML + '<li style="height:23px;" ng-click="vm.selectUnselectFilterList(\'filter' + filterId + '' + filterJson.filterDropdown[h].Key + '\',\'' + filterJson.filterDropdown[h].Key + '\',\'' + filterId + '\',\'' + val + '\')" id=filter' + filterId + '' + filterJson.filterDropdown[h].Key + ' class="hand-pointer bottom5 padding1 filter-item"><div class="col-md-3 padding0 align-left">' + filterJson.filterDropdown[h].Key + '</div><div class="col-md-9 padding0" title=\'' + filterJson.filterDropdown[h].Value + '\'>' + dropdownValue + '</div></li>';
                        this.filterDataResponse[val].filterDropdown.push(filterJson.filterDropdown[h]);
                    }

               /*   this.totalValues[val] = parseInt(skip) + filterJson.filterDropdown.length;
                    if (filterJson.totalCount <= 10) {
                        this.checkMore[val] = false;
                    }
                    else {
                        this.checkMore[val] = true;
                    }
                    if (this.totalValues[val] >= totalCount) {
                        countHtml = '<span>' + this.totalValues[val] + ' ' + $('#cmnShownLabel').html() + '<a ng-show="vm.checkMore[' + val + ']" ng-click="vm.showMoreFilterData(\'listFilterData' + val + '\',\'' + 'listCountData' + val + '\',\'' + val + '\',\'' + totalCount + '\',\'' + filterId + '\',0)">&nbsp' + $('#cmnShowLessLabel').html() + '</a></span>';
                    } else {
                        countHtml = '<span>' + this.totalValues[val] + ' ' + $('#cmnShownLabel').html() + '<a ng-click="vm.showMoreFilterData(\'listFilterData' + val + '\',\'' + 'listCountData' + val + '\',\'' + val + '\',\'' + totalCount + '\',\'' + filterId + '\',\'' + this.totalValues[val] + '\')">&nbsp' + localStorage.getItem('showMoreText') + '</a></span>';
                    }
                    let countContent = this.$compile(countHtml)(this.$scope);
                    $("#" + countId).html('').append(countContent);
                    let content = this.$compile(filterInnerHTML)(this.$scope);
                    if (skip == '0') {
                        $("#" + id).html('').append(content);
                    } else {
                        $("#" + id).append(content);
                    }*/

                    this.totalValues[val] = filterJson.filterDropdown.length;

                    if (filterJson.totalCount <= 10) {
                        this.checkMore[val] = false;
                    }
                    else {
                        this.checkMore[val] = true;
                    }

                    if (skip == '0') {
                        countHtml = '<span>' + this.totalValues[val] + ' ' + $('#cmnShownLabel').html() + '<a ng-click="vm.showMoreFilterData(\'listFilterData' + val + '\',\'' + 'listCountData' + val + '\',\'' + val + '\',\'' + totalCount + '\',\'' + filterId + '\',\'' + this.totalValues[val] + '\',false)">&nbsp' + localStorage.getItem('showAllText') + '</a></span>';
                    } else {
                        countHtml = '<span>' + this.totalValues[val] + ' ' + $('#cmnShownLabel').html() + '</span>';
                    }
                    let countContent = this.$compile(countHtml)(this.$scope);
                    $("#" + countId).html('').append(countContent);

                    let content = this.$compile(filterInnerHTML)(this.$scope);
                    $("#" + id).html('').append(content);

                EVENTS.loaderOFF("#" + id);

                if (selectAllChk) {
                    this.filteredValues[filterId] = [];
                    this.clearFilterShow[val] = true;
                    this.showApplyFilter = true;
                    $("#listFilterData" + val).find('li').addClass('item-selected');
                    for (let i = 0; i < filterJson.filterDropdown.length; i++) {
                        this.filteredValues[filterId].push(filterJson.filterDropdown[i].Key);
                    }            
                }

            }, error => {
                EVENTS.loaderOFF("#" + id);
                ERROR.displayException(error.data, error.status, error.data, "showMoreFilterData");
            });
        }
        clearSearchedItems(id, val) {
            this.filteredInputText[val] = [];
            this.showCloseImage[val] = false;
            this.getFilterData(); //bug 76629 
            this.showApplyFilter = false; //bug 76629 
            //   $('#' + id + ' li:not(.item-selected).searched-items').remove(); 
        }
        //showLessFilterData(id: any, countId: any, val, totalCount, filterId) {
        //    let filterInnerHTML = '', countHtml = '';
        //    let filterJson = [{ "Value": "test4", "Key": "0000" }, { "Value": "test5", "Key": "0001" }, { "Value": "test6", "Key": "0003" }];
        //    this.totalValues[val] = 3;
        //    for (let i = 0; i < filterJson.length; i++) {
        //        filterInnerHTML = filterInnerHTML + '<li ng-click="vm.selectUnselectFilterList(\'' + id + '' + filterJson[i].Key + '\',\'' + filterJson[i].Key + '\',\'' + filterId + '\',\'' + val + '\')" id=' + id + '' + filterJson[i].Key + ' class="hand-pointer bottom10 padding3 filter-item"><span class="align-left">' + filterJson[i].Value + '</span><span class="float-right">' + filterJson[i].Key + '</span></li>';
        //    }
        //    if (this.totalValues[val] >= totalCount) {
        //        countHtml = '<span>' + this.totalValues[val] + ' ' + $('#cmnShownLabel').html() + '<a ng-click="vm.showMoreFilterData(\'listFilterData' + val + '\',\'' + 'listCountData' + val + '\',\'' + val + '\',\'' + totalCount + '\',\'' + filterId + '\')">&nbsp' + $('#cmnShowLessLabel').html() + '</a></span>';
        //    } else {
        //        countHtml = '<span>' + this.totalValues[val] + ' ' + $('#cmnShownLabel').html() + '<a ng-click="vm.showMoreFilterData(\'listFilterData' + val + '\',\'' + 'listCountData' + val + '\',\'' + val + '\',\'' + totalCount + '\',\'' + filterId + '\')">&nbsp' + localStorage.getItem('showMoreText') + '</a></span>';
        //    }
        //    let countContent = this.$compile(countHtml)(this.$scope);
        //    $("#" + countId).html('').append(countContent);
        //    let content = this.$compile(filterInnerHTML)(this.$scope);
        //    $("#"+id).html('').append(content);
        //}
        searchFilterData(id: any, val, e, filterId, totalCount) {
            var key = e.which;
            if (key == 13)  // the enter key code
            {
                EVENTS.loaderON("#" + id);
                if (!this.filteredInputText[val]) {
                    this.showMoreFilterData('listFilterData' + val, 'listCountData' + val, val, totalCount, filterId, 0,false);
                    this.showCloseImage[val] = false;
                } else {
                    this.showCloseImage[val] = true;
                    $('#listCountData' + val).html(' ');
                    var currentDate = this.selectedMonthValue;
                    var splitdate = currentDate.split(" ")[0];
                    const dd = splitdate.split("/")[0];
                    const year = splitdate.split("/")[2];
                    this.$http.get("../api/AccountStatement/SearchFilterValues?budgetYear=" + year + "&orgLevel=" + this.orgMenuLevel + "&orgId=" + this.orgMenuId + "&filterId=" + filterId + "&searchString=" + this.filteredInputText[val] + "").then(response => {
                        //removed unselected values
                        $('#listFilterData' + val + ' li:not(.item-selected)').remove();
                        let filterJson: any = response.data;
                        let filterInnerHTML = '';
                        if (filterJson.filterDropdown.length == 0) {
                            filterInnerHTML = '<li class="border1" style="text-align:center;">' + $("#noResultDataLabel").html() + '</li>';
                        }
                        else {
                            for (let h = 0; h < filterJson.filterDropdown.length; h++) {
                                console.log(filterJson.filterDropdown[h].Key);
                                console.log(this.filteredValues[filterId]);
                                if (jQuery.inArray(filterJson.filterDropdown[h].Key, this.filteredValues[filterId]) == -1) {
                                    filterInnerHTML = filterInnerHTML + '<li style="height:23px;" ng-click="vm.selectUnselectFilterList(\'filter' + filterId + '' + filterJson.filterDropdown[h].Key + '\',\'' + filterJson.filterDropdown[h].Key + '\',\'' + filterId + '\',\'' + val + '\')" id=filter' + filterId + '' + filterJson.filterDropdown[h].Key + ' class="hand-pointer bottom5 padding1 filter-item searched-items"><div class="col-md-3 padding0">' + filterJson.filterDropdown[h].Key + '</div><div class="col-md-9 padding0">' + filterJson.filterDropdown[h].Value + '</div></li>';
                                }
                            }
                        }
                        let content = this.$compile(filterInnerHTML)(this.$scope);
                        $("#" + id).append(content);
                        EVENTS.loaderOFF("#" + id);
                    }, error => {
                        EVENTS.loaderOFF("#" + id);
                        ERROR.displayException(error.data, error.status, error.data, "searchFilterData");
                    });
                }
                //console.log(this.filteredInputText[val]);             
            }
            else {
                return false;
            }

        }

        selectUnselectFilterList(id: any, key: any, filterId: any, val) {

            if ($("#" + id).hasClass('item-selected')) {
                $("#" + id).removeClass('item-selected');
                for (let i = 0; i < this.filteredValues[filterId].length; i++) {
                    if (this.filteredValues[filterId][i] == key) {
                        this.filteredValues[filterId].splice(i, 1);
                        if (this.filteredValues[filterId].length > 0) { this.clearFilterShow[val] = true; } else { this.clearFilterShow[val] = false; }
                        //console.log(this.filteredValues);

                    }
                }
            } else {
                $("#" + id).addClass('item-selected');
                this.filteredValues[filterId].push(key);
                for (let i = 0; i < this.filteredValues[filterId].length; i++) {
                    if (this.filteredValues[filterId].length > 0) { this.clearFilterShow[val] = true; } else { this.clearFilterShow[val] = false; }
                }
                // console.log(this.filteredValues);

            }

            this.showApplyFilter = false;
            for (let i = 0; i < this.filterDataResponse.length; i++) {
                if (this.filteredValues[this.filterDataResponse[i].filterId].length > 0) {
                    this.showApplyFilter = true;
                }
            }
        }
        clearPerFilter(filterId, val) {
            this.filteredValues[filterId] = [];
            this.clearFilterShow[val] = false;
            this.loadAllFilterItems[val] = false;
            this.showApplyFilter = false;
            $("#selectAllChk_" + filterId).prop('checked', false);
            $("#listFilterData" + val).find('li').removeClass('item-selected');
            for (let i = 0; i < this.filterDataResponse.length; i++) {
                if (this.filteredValues[this.filterDataResponse[i].filterId].length > 0) {
                    this.showApplyFilter = true;
                }
            }
            this.getAccountsGridData();
        }
        clearAllFilter() {
            this.showApplyFilter = false;
            $(".selectAllChk").prop('checked', false);
            for (let i = 0; i < this.filterDataResponse.length; i++) {
                this.filteredValues[this.filterDataResponse[i].filterId] = [];
                this.clearFilterShow[i] = false;
                $("#listFilterData" + i).find('li').removeClass('item-selected');
            }
            this.getAccountsGridData();
        }
        expandFilter(param: boolean) {
            setTimeout(function () {
                $("#accountStatementGrid").data("kendoTreeList").resize();  
            }, 50);
            this.expandFilterSection = param;
        }

        selectAllFilter(filterId, val) {
            
            let checked = $("#selectAllChk_" + filterId).is(":checked") ? true : false;
            if (checked) {
                if (this.loadAllFilterItems[val] == false) {
                    this.showMoreFilterData('listFilterData' + val, 'listCountData' + val, val, this.filterDataResponse[val].filterDropdown.length, filterId, this.filterDataResponse[val].filterDropdown.length, true);
                } else {
                    this.filteredValues[filterId] = [];
                    this.clearFilterShow[val] = true;
                    this.showApplyFilter = true;
                    $("#listFilterData" + val).find('li').addClass('item-selected');
                    for (let i = 0; i < this.filterDataResponse[val].filterDropdown.length; i++) {
                        this.filteredValues[filterId].push(this.filterDataResponse[val].filterDropdown[i].Key);
                    }
                }
            } else {
                this.filteredValues[filterId] = [];
                this.clearFilterShow[val] = false;
                this.showApplyFilter = false;
                $("#listFilterData" + val).find('li').removeClass('item-selected');
                for (let i = 0; i < this.filterDataResponse.length; i++) {
                    if (this.filteredValues[this.filterDataResponse[i].filterId].length > 0) {
                        this.showApplyFilter = true;
                    }
                }
            }
        }

        getaccountStatementColumnSelector() {
            var currentDate = this.selectedMonthValue;
            var splitdate = currentDate.split(" ")[0];
            const dd = splitdate.split("/")[0];
            const year = splitdate.split("/")[2];
            let acViewType = this.orgViewFilterKey;

            console.log(this.orgViewFilterKey, " GetColumnConfig");
            this.$http.get<[]>("../api/AccountStatement/GetColumnConfig?budgetYear=" + year + "&viewType=" + acViewType).then(response => {
                let responseData: any = response.data;
                if (localStorage.getItem("synapseAccountStatementColumns")) {
                    this.accountStatementColumns = JSON.parse(localStorage.getItem("synapseAccountStatementColumns"));
                }
                else {
                    this.accountStatementColumns = responseData;
                }
                this.accountColSelectorClick('#accountStatementColumnSelector', '#accountStatementPopupColSelector');
                this.showColumnSelectorSave();
                this.getAccountsGridData();
            }, error => {
                ERROR.displayException(error.data, error.status, error.data, "getaccountStatementColumnSelector");
            });
        }

        accountStatementColumnSelectorOk() {
            let accountStatementColumnsStr = JSON.stringify(this.accountStatementColumns);
            localStorage.setItem("synapseAccountStatementColumns", accountStatementColumnsStr);
            this.getAccountsGridData();
        }

        accountStatementColumnSelectorSave() {
            let accountStatementColumnsStr = JSON.stringify(this.accountStatementColumns);
            localStorage.setItem("synapseAccountStatementColumns", accountStatementColumnsStr);
            EVENTS.loaderON("#accountStatementColumnSelectorContent");
            var currentDate = this.selectedMonthValue;
            var splitdate = currentDate.split(" ")[0];
            const dd = splitdate.split("/")[0];
            const year = splitdate.split("/")[2];
            var finalJosnObj = '';
            var finalObj = { selectedColumns: this.accountStatementColumns, budgetYear: year }
            finalJosnObj = angular.toJson(finalObj);
            this.$http.post("../api/AccountStatement/SaveColumnConfig", finalJosnObj).then(response => {
                MODULES.saveConfirmation();
                this.getAccountsGridData();
                EVENTS.loaderOFF("#accountStatementColumnSelectorContent");
            }, error => {
                EVENTS.loaderOFF("#accountStatementColumnSelectorContent");
                error.methodName = "Account statement reporting - accountStatementColumnSelectorSave";
                ERROR.displayException(error.data, error.status, error.data, error.methodName);
            });

        }

        accountColSelectorClick(id, contentID) {
            let indicatorToolTipID = id;
            let tooltipID = indicatorToolTipID.toString();

            $(tooltipID).kendoTooltip({
                content: function (e) {
                    $(contentID).css("display", "block");
                    var target = $(contentID); // the element for which the tooltip is shown
                    return target; // set the element text as content of the tooltip
                },
                width: 200,
                showOn: "click",
                show: function (e) {
                    this.popup.element.addClass("base-tooltip-popup");
                    $("div[role='tooltip']").find('.k-i-close').css('visibility', 'hidden');
                    $('.k-tooltip-content').css('padding-right', '0px');  // added to avoid custom padding added in custom css
                    // $(tooltipID + '_tt_active').find('.k-i-close').css('visibility', 'hidden');
                    $(tooltipID + '_tt_active').find('.k-callout-s').css('border-top-color', '#fff');
                    $(tooltipID + '_tt_active').find('.k-callout-w').css({ 'border-right-color': '#fff' });
                    $('.k-callout-n').css('border-bottom-color', '#fff');
                    $(tooltipID + '_tt_active').find('.k-callout-e').css({ 'border-left-color': '#fff' });
                },
                autoHide: false,
                position: "right"
            });
        }
        showColumnSelectorSave() {
            this.$http.get<boolean>("../api/AccountStatement/ShowColumnConfigSaveButton").then(response => {
                this.showAccountStatementColumns = response.data;
            }, error => {
                ERROR.displayException(error.data, error.status, error.data, "showColumnSelectorSave");
            });

        }

        exportAccountTreelistToExcel() {

            var gridId = "#accountStatementGrid";
            EVENTS.loaderON(gridId);
            let getURL = "../Content/DownloadExcel";

            let finalJSON = JSON.stringify(this.excelExportData);
            this.$http.post("../AccountStatementReport/ExportTreeToExcel", finalJSON).then(response => {
                let responseData: any = response.data;
                window.location.href = getURL + "?fName=" + responseData.fName;
                EVENTS.loaderOFF(gridId);
            }, error => {
                EVENTS.loaderOFF(gridId);
                ERROR.displayException(error.data, error.status, error.data, "ExportTreeToExcel");
            });
        }

        //grid1
        getAccountsGridData() {

      
            var gridId = "#accountStatementGrid";
            EVENTS.loaderON(gridId);
            let filterValuesArray = [], obj: any = {};
            for (let g = 0; g < Object.keys(this.filteredValues).length; g++) {
                if (Object.values(this.filteredValues)[g].length > 0) {
                    obj = {};
                    obj.filterId = Object.keys(this.filteredValues)[g];
                    obj.filterValues = Object.values(this.filteredValues)[g];
                    filterValuesArray.push(obj);
                }
            }
            if (localStorage.getItem("synapseAccountStatementColumns")) {
                this.accountStatementColumns = JSON.parse(localStorage.getItem("synapseAccountStatementColumns"));
            }
            let orgViewFilter = localStorage.getItem('rememberViewFilterSelection') ? localStorage.getItem('rememberViewFilterSelection') : $('#viewOrgFilter').data('kendoDropDownList').value();

            console.log(this.orgViewFilterKey, " GetAccountStatementData");
            let currentDate = this.selectedMonthValue;
            let splitdate = currentDate.split(" ")[0];
            const dd = splitdate.split("/")[0];
            const year = splitdate.split("/")[2];
            let filterObj = {
                filters: filterValuesArray,
                orgId: this.orgMenuId,
                orgLevel: this.orgMenuLevel,
                period: year + dd,
                budgetYear: year,
                selectedColumns: this.accountStatementColumns,
                viewType: orgViewFilter,
                level1OrgId: this.orgIdLevel1,
                level2OrgId: this.orgIdLevel2,
                level3OrgId: this.orgIdLevel3,
                level4OrgId: this.orgIdLevel4,
                level5OrgId: this.orgIdLevel5,
                level6OrgId: this.orgIdLevel6,
                level7OrgId: this.orgIdLevel7,
                level8OrgId: this.orgIdLevel8
            }
            let finalJSON = JSON.stringify(filterObj);
            this.$http.post("../api/AccountStatement/GetAccountStatementData", finalJSON).then(response => {

                let responseData: any = response.data;

                let gridColumns = [];
                for (let t = 0; t < responseData.columns.length; t++) {

                    let columnField = responseData.columns[t].field;
                    if (columnField !== "trLi")
                        gridColumns.push(responseData.columns[t]);
                }

                this.excelExportData = {
                    columns: gridColumns,
                    dataSource: responseData.data
                }

                let dataSource = new kendo.data.TreeListDataSource({
                    data: responseData.data,
                    schema: {
                        model: {
                            id: "id",
                            parentId: "parentId",
                            expanded: false,
                            fields: {
                                id: { type: "string" },
                                parentId: { type: "string", nullable: true }
                            }
                        }
                    }
                });

                this.$scope.accountGridOptions = {
                    dataSource: dataSource,
                    resizable: true,
                    expand: (ev) => {
                        setTimeout(function () {
                            $(gridId).find('.k-grid-content-locked').find("tr:last td").addClass('semi');//for last row always bold 
                            $(gridId).find("tr:last").addClass("semi");//for last row always bold   
                        }, 250);
                    },
                    collapse: (ev) => {
                        setTimeout(function () {
                            $(gridId).find('.k-grid-content-locked').find("tr:last td").addClass('semi');//for last row always bold 
                            $(gridId).find("tr:last").addClass("semi");//for last row always bold   
                        }, 250);
                    },
                    dataBound: (ev) => {
                        $(gridId).find('.k-grid-content-locked').find("tr:last td").addClass('semi');//for last row always bold 
                        $(gridId).find("tr:last").addClass("semi");//for last row always bold   
                        $(gridId).find('.k-grid-header-wrap').css('width', $("#accountStatementGrid .k-grid-content").width());
                        
                    },
                    columns: responseData.columns,
                    scrollable: true
                }
                              
                //setTimeout(() => {
                //    $(gridId).data("kendoTreeList").setOptions({
                //        columns: responseData.columns
                //    });
                //    $(gridId).data("kendoTreeList").setDataSource(dataSource);
                //}, 100);

                setTimeout(function () {
                    // 78471
                    $(gridId).find('.k-grid-content').css('max-height', '600px');
                    $(gridId).find("tr:last-child").css("font-weight", "bold");//for last row always bold
                    $(gridId).data("kendoTreeList").refresh(); //80432
                    $(window).resize(() => {
                        if ($('#accountStatementGrid').data("kendoTreeList")) {
                            $('#accountStatementGrid').data("kendoTreeList").resize();
                            $(gridId).find('.k-grid-header-wrap').css('width', $("#accountStatementGrid .k-grid-content").width());
                        }
                    });
                }, 1000); 
                EVENTS.loaderOFF(gridId);
            }, error => {
                error.methodName = "Account statement reporting - getAccountsGridData";
                EVENTS.loaderOFF(gridId);
                ERROR.displayException(error.data, error.status, error.data, error.methodName);
            });

            $('#accountStatementTitleDesc img').click(function (e) {
                MODULES.informationTooltipClick('#accountStatementTitleDesc', localStorage.getItem("AccountStatementTooltipDesc"));
            });
        }



        getAccStmtWindow(id, accountLevel, periodCheck,orgName) {
            let accountLevelDup = id.split("_")[0];
            let accountCodeDup = id.split("_")[1];
            let regex = /[xyz]/;
            const index_accountLevelDup = accountLevelDup.search(regex);
            if (index_accountLevelDup != -1) {
                accountLevelDup = accountLevelDup.substring(0, index_accountLevelDup);
            }
            const index_accountCodeDup = accountCodeDup.search(regex);
            if (index_accountCodeDup != -1) {
                accountCodeDup = accountCodeDup.substring(0, index_accountCodeDup);
            }
            if ($('#viewOrgFilter').data('kendoDropDownList')) {

                let viewDropDownValue = $('#viewOrgFilter').data('kendoDropDownList').value();
                if (viewDropDownValue == "0") {
                    this.budgetHistoryOrgViewPopupSelectedId = this.getAccountLevelId(id.split("_")[3]);
                }
            }

            id = accountLevelDup + "_" + accountCodeDup;
            this.budgetHistoryPopupSelectedId = id;
            this.budgetHistoryPopupSelectedPeriodCheck = periodCheck;
            this.orgIdForOrgView = accountCodeDup;
            this.budgetHistoryOrgViewPopupSelectedId = accountLevelDup + "_" + this.budgetHistoryOrgViewPopupSelectedId;
            this.budgetHistoryPopupSelectedAccountName = accountLevel;
            this.budgetHistoryOrgName = orgName;
            this.accountStmtWindow = <kendo.ui.Window>$('#accountStatementRevisedBdgtPopup').kendoWindow({
                modal: true,
                actions: ["close"],
                scrollable: true,
                resizable: false,
                draggable: true,
                width: "93%",
                height: $(window).height() - 50
            }).data("kendoWindow").center().open();
            this.accountStmtWindow.element.prev().addClass('cmn-background2');
            let finalJsonData = this.applyBudgetHistoryInputData();
            this.getAccountReportingGridData(finalJsonData, false);
            this.getAccountReportingTreeGridGridData(finalJsonData);
        }

        getAccountLevelId = function(accountLevel){

            let regex = /[xyz]/;
            const index_orgViewId = accountLevel.search(regex);
            if (index_orgViewId != -1) {
                accountLevel = accountLevel.substring(0, index_orgViewId);
            }
            return accountLevel;
        }

        closeAccountStatementSynapsePopup() {
            this.clearBudgetHistorySearchFilter();
            this.accountStmtWindow.close();
        }

        applyBudgetHistoryInputData = function (sortField="", sortOrder="") {
            let forecastPeriod = $("#synapseBudgetYearIdDropdown").data("kendoDropDownList").value();
            let parsePeriod = new Date(forecastPeriod);
            let month = parsePeriod.getMonth() + 1;
            let year = parsePeriod.getFullYear();
            this.selectedServiceId = '';
            this.selectedServiceName = '';
            let dropDownObjData = {
                departments: this.filteredValues["DepartmentFilter"],
                functions: this.filteredValues["FunctionFilter"],
                projects: this.filteredValues["ProjectFilter"],
                freeDim1: this.filteredValues["FreeDim1Filter"],
                freeDim2: this.filteredValues["FreeDim2Filter"],
                nextorg: this.filteredValues["OrgFilter"],
                servs: this.filteredValues["ServiceFilter"],
                monthYear: forecastPeriod,
                level1OrgId: this.orgIdLevel1,
                level2OrgId: this.orgIdLevel2,
                level3OrgId: this.orgIdLevel3,
                level4OrgId: this.orgIdLevel4,
                level5OrgId: this.orgIdLevel5,
                level6OrgId: this.orgIdLevel6,
                level7OrgId: this.orgIdLevel7,
                level8OrgId: this.orgIdLevel8,
                orgId: this.orgMenuId,
                orgLevel: this.orgMenuLevel,
                orgName: this.orgName,
                serviceId: this.selectedServiceId,
                serviceName: this.selectedServiceName,
                accountCodeId: this.budgetHistoryPopupSelectedId,
                budget_year: year,
                period: month,
                isPeriodCheck: this.budgetHistoryPopupSelectedPeriodCheck,
                sortField: sortField,
                sortOrder: sortOrder,
                accountCodeName: this.budgetHistoryPopupSelectedAccountName
            }
            if ($('#viewOrgFilter').data('kendoDropDownList')) {// if the dropdown value is org view

                var viewDropDownValue = $('#viewOrgFilter').data('kendoDropDownList').value();

                if (viewDropDownValue == "0") {
                    dropDownObjData["orgId"] = this.orgIdForOrgView;
                    dropDownObjData["orgLevel"] = this.orgMenuLevel + 1;
                    dropDownObjData["orgName"] = this.budgetHistoryOrgName;
                    dropDownObjData["accountCodeId"] = this.budgetHistoryOrgViewPopupSelectedId;
                    dropDownObjData["level" + (this.orgMenuLevel + 1) + "OrgId"] = this.orgIdForOrgView;
                }
            }
            return dropDownObjData;
        }

        getAccountReportingGridData = function (finalJsonData, sortFlag) {

            EVENTS.loaderON('#accountReportingGridSynapse');
            this.$http.post("../api/MonthlyReportingDocApi/GetBudgetHistoryTreeListData", angular.toJson(finalJsonData)).then(response => {
                EVENTS.loaderOFF('#accountReportingGridSynapse');
                let accountReportingGridTitle = response.data.Title;
                this.accountStmtWindow.element.prev().find(".k-window-title").html('<span>' + accountReportingGridTitle + '</span><span class="padding-left5 cursor" data-role="tooltip" id="accountReportToolTipSynapse"><img src="../images/icons_monthly_report_information.png"></span>');
                $('#accountReportToolTipSynapse img').click(function (e) {
                    MODULES.informationTooltipClick('#accountReportToolTipSynapse', response.data.Description);
                });
                
                if (sortFlag == false) {
                    this.getBudgetHistoryHeaderGrid(response.data);
                }
                this.getBudgetHistoryTreelistData(response.data);
            }, error => {
                    error.methodName = "getAccountReportingGridData";
                    EVENTS.loaderOFF("#accountReportingGridSynapse");
                    ERROR.displayException(error.data, error.status, error.data, error.methodName);
            });
        }

        getBudgetHistoryHeaderGrid = function (response:any) {
            let gridId = "#accountReportingSearchGridSynapse";
            let notSortedColumns = ["account", "department", "function", "project", "description"];
            if (response.columns.length > 0) {
                for (let i = 0; i < response.columns.length; i++) {
                    let columnField = response.columns[i].field;
                    switch (columnField) {
                        case "date":
                            response.columns[i]["filterable"] =
                            {
                                cell:
                                {
                                    operator: "contains",
                                    showOperators: false,
                                    template: (el) => {
                                        // adding a customized filter(text box)
                                        $(el.element).removeAttr('data-bind');
                                        $(el.element).addClass("k-input k-textbox form-control");

                                        $(el.element).attr('id', columnField);
                                        const placeHolderId = "#" + columnField + "_placeholder";
                                        $(el.element).attr('placeholder', $(placeHolderId).text());
                                        let keyUpColField = columnField;
                                        $("body").on("keyup", "#" + keyUpColField, (e) => {
                                            e.preventDefault();
                                            if (e.keyCode == 13) {
                                                this.historySearchGridData();
                                            }
                                        });
                                    }
                                }
                            };
                            break;
                        case "adjustmentCodeStatus":
                            response.columns[i]["filterable"] =
                            {
                                cell:
                                {
                                    operator: "contains",
                                    showOperators: false,
                                    template: (el) => {
                                        // adding a customized filter(text box)
                                        $(el.element).removeAttr('data-bind');
                                        $(el.element).addClass("k-input k-textbox form-control");

                                        $(el.element).attr('id', columnField);
                                        const placeHolderId = "#" + columnField + "_placeholder";
                                        $(el.element).attr('placeholder', $(placeHolderId).text());
                                        let keyUpColField = columnField;
                                        $("body").on("keyup", "#" + keyUpColField, (e) => {
                                            e.preventDefault();
                                            if (e.keyCode == 13) {
                                                this.historySearchGridData();
                                            }
                                        });
                                    }
                                }
                            };
                            break;
                        case "transactioncomment":
                            response.columns[i]["filterable"] =
                            {
                                cell:
                                {
                                    operator: "contains",
                                    showOperators: false,
                                    template: (el) => {
                                        // adding a customized filter(text box)
                                        $(el.element).removeAttr('data-bind');
                                        $(el.element).addClass("k-input k-textbox form-control");

                                        $(el.element).attr('id', columnField);
                                        const placeHolderId = "#" + columnField + "_placeholder";
                                        $(el.element).attr('placeholder', $(placeHolderId).text());
                                        let keyUpColField = columnField;
                                        $("body").on("keyup", "#" + keyUpColField, (e) => {
                                            e.preventDefault();
                                            if (e.keyCode == 13) {
                                                this.historySearchGridData();
                                            }
                                        });
                                    }
                                }
                            };
                            break;
                        case "updatedby":
                            response.columns[i]["filterable"] =
                            {
                                cell:
                                {
                                    operator: "contains",
                                    showOperators: false,
                                    template: (el) => {
                                        // adding a customized filter(text box)
                                        $(el.element).removeAttr('data-bind');
                                        $(el.element).addClass("k-input k-textbox form-control");

                                        $(el.element).attr('id', columnField);
                                        const placeHolderId = "#" + columnField + "_placeholder";
                                        $(el.element).attr('placeholder', $(placeHolderId).text());
                                        let keyUpColField = columnField;
                                        $("body").on("keyup", "#" + keyUpColField, (e) => {
                                            e.preventDefault();
                                            if (e.keyCode == 13) {
                                                this.historySearchGridData();
                                            }
                                        });
                                    }
                                }
                            };
                            break;
                        case "changeamount":
                            response.columns[i]["filterable"] =
                            {
                                cell:
                                {
                                    operator: "contains",
                                    showOperators: false,
                                    template: (el) => {
                                        // adding a customized filter(text box)
                                        $(el.element).removeAttr('data-bind');
                                        $(el.element).addClass("k-input k-textbox form-control");

                                        $(el.element).attr('id', columnField);
                                        const placeHolderId = "#" + columnField + "_placeholder";
                                        $(el.element).attr('placeholder', $(placeHolderId).text());
                                        let keyUpColField = columnField;
                                        $("body").on("keyup", "#" + keyUpColField, (e) => {
                                            e.preventDefault();
                                            if (e.keyCode == 13) {
                                                this.historySearchGridData();
                                            }
                                        });
                                    }
                                }
                            };
                            break;
                        case "account":
                            response.columns[i]["filterable"] =
                            {
                                cell:
                                {
                                    operator: "contains",
                                    showOperators: false,
                                    template: (el) => {
                                        // adding a customized filter(text box)
                                        $(el.element).removeAttr('data-bind');
                                        $(el.element).addClass("k-input k-textbox form-control");

                                        $(el.element).attr('id', columnField);
                                        const placeHolderId = "#" + columnField + "_placeholder";
                                        $(el.element).attr('placeholder', $(placeHolderId).text());
                                        let keyUpColField = columnField;
                                        $("body").on("keyup", "#" + keyUpColField, (e) => {
                                            e.preventDefault();
                                            if (e.keyCode == 13) {
                                                this.historySearchGridData();
                                            }
                                        });
                                    }
                                }
                            };
                            break;
                        case "department":
                            response.columns[i]["filterable"] =
                            {
                                cell:
                                {
                                    operator: "contains",
                                    showOperators: false,
                                    template: (el) => {
                                        // adding a customized filter(text box)
                                        $(el.element).removeAttr('data-bind');
                                        $(el.element).addClass("k-input k-textbox form-control");

                                        $(el.element).attr('id', columnField);
                                        const placeHolderId = "#" + columnField + "_placeholder";
                                        $(el.element).attr('placeholder', $(placeHolderId).text());
                                        let keyUpColField = columnField;
                                        $("body").on("keyup", "#" + keyUpColField, (e) => {
                                            e.preventDefault();
                                            if (e.keyCode == 13) {
                                                this.historySearchGridData();
                                            }
                                        });
                                    }
                                }
                            };
                            break;
                        case "function":
                            response.columns[i]["filterable"] =
                            {
                                cell:
                                {
                                    operator: "contains",
                                    showOperators: false,
                                    template: (el) => {
                                        // adding a customized filter(text box)
                                        $(el.element).removeAttr('data-bind');
                                        $(el.element).addClass("k-input k-textbox form-control");

                                        $(el.element).attr('id', columnField);
                                        const placeHolderId = "#" + columnField + "_placeholder";
                                        $(el.element).attr('placeholder', $(placeHolderId).text());
                                        let keyUpColField = columnField;
                                        $("body").on("keyup", "#" + keyUpColField, (e) => {
                                            e.preventDefault();
                                            if (e.keyCode == 13) {
                                                this.historySearchGridData();
                                            }
                                        });
                                    }
                                }
                            };
                            break;
                        case "project":
                            response.columns[i]["filterable"] =
                            {
                                cell:
                                {
                                    operator: "contains",
                                    showOperators: false,
                                    template: (el) => {
                                        // adding a customized filter(text box)
                                        $(el.element).removeAttr('data-bind');
                                        $(el.element).addClass("k-input k-textbox form-control");

                                        $(el.element).attr('id', columnField);
                                        const placeHolderId = "#" + columnField + "_placeholder";
                                        $(el.element).attr('placeholder', $(placeHolderId).text());
                                        let keyUpColField = columnField;
                                        $("body").on("keyup", "#" + keyUpColField, (e) => {
                                            e.preventDefault();
                                            if (e.keyCode == 13) {
                                                this.historySearchGridData();
                                            }
                                        });
                                    }
                                }
                            };
                            break;
                        case "free_dim_1":
                            response.columns[i]["filterable"] =
                            {
                                cell:
                                {
                                    operator: "contains",
                                    showOperators: false,
                                    template: (el) => {
                                        // adding a customized filter(text box)
                                        $(el.element).removeAttr('data-bind');
                                        $(el.element).addClass("k-input k-textbox form-control");

                                        $(el.element).attr('id', columnField);
                                        const placeHolderId = "#" + columnField + "_placeholder";
                                        $(el.element).attr('placeholder', $(placeHolderId).text());
                                        let keyUpColField = columnField;
                                        $("body").on("keyup", "#" + keyUpColField, (e) => {
                                            e.preventDefault();
                                            if (e.keyCode == 13) {
                                                this.historySearchGridData();
                                            }
                                        });
                                    }
                                }
                            };
                            break;
                        case "free_dim_2":
                            response.columns[i]["filterable"] =
                            {
                                cell:
                                {
                                    operator: "contains",
                                    showOperators: false,
                                    template: (el) => {
                                        // adding a customized filter(text box)
                                        $(el.element).removeAttr('data-bind');
                                        $(el.element).addClass("k-input k-textbox form-control");

                                        $(el.element).attr('id', columnField);
                                        const placeHolderId = "#" + columnField + "_placeholder";
                                        $(el.element).attr('placeholder', $(placeHolderId).text());
                                        let keyUpColField = columnField;
                                        $("body").on("keyup", "#" + keyUpColField, (e) => {
                                            e.preventDefault();
                                            if (e.keyCode == 13) {
                                                this.historySearchGridData();
                                            }
                                        });
                                    }
                                }
                            };
                            break;
                        case "free_dim_3":
                            response.columns[i]["filterable"] =
                            {
                                cell:
                                {
                                    operator: "contains",
                                    showOperators: false,
                                    template: (el) => {
                                        // adding a customized filter(text box)
                                        $(el.element).removeAttr('data-bind');
                                        $(el.element).addClass("k-input k-textbox form-control");

                                        $(el.element).attr('id', columnField);
                                        const placeHolderId = "#" + columnField + "_placeholder";
                                        $(el.element).attr('placeholder', $(placeHolderId).text());
                                        let keyUpColField = columnField;
                                        $("body").on("keyup", "#" + keyUpColField, (e) => {
                                            e.preventDefault();
                                            if (e.keyCode == 13) {
                                                this.historySearchGridData();
                                            }
                                        });
                                    }
                                }
                            };
                            break;
                        case "free_dim_4":
                            response.columns[i]["filterable"] =
                            {
                                cell:
                                {
                                    operator: "contains",
                                    showOperators: false,
                                    template: (el) => {
                                        // adding a customized filter(text box)
                                        $(el.element).removeAttr('data-bind');
                                        $(el.element).addClass("k-input k-textbox form-control");

                                        $(el.element).attr('id', columnField);
                                        const placeHolderId = "#" + columnField + "_placeholder";
                                        $(el.element).attr('placeholder', $(placeHolderId).text());
                                        let keyUpColField = columnField;
                                        $("body").on("keyup", "#" + keyUpColField, (e) => {
                                            e.preventDefault();
                                            if (e.keyCode == 13) {
                                                this.historySearchGridData();
                                            }
                                        });
                                    }
                                }
                            };
                            break;
                        case "accinfocomment":
                            response.columns[i]["filterable"] =
                            {
                                cell:
                                {
                                    operator: "contains",
                                    showOperators: false,
                                    template: (el) => {
                                        // adding a customized filter(text box)
                                        $(el.element).removeAttr('data-bind');
                                        $(el.element).addClass("k-input k-textbox form-control");

                                        $(el.element).attr('id', columnField);
                                        const placeHolderId = "#" + columnField + "_placeholder";
                                        $(el.element).attr('placeholder', $(placeHolderId).text());
                                        let keyUpColField = columnField;
                                        $("body").on("keyup", "#" + keyUpColField, (e) => {
                                            e.preventDefault();
                                            if (e.keyCode == 13) {
                                                this.historySearchGridData();
                                            }
                                        });
                                    }
                                }
                            };
                            break;
                        case "adjustmentCode":
                            response.columns[i]["filterable"] =
                            {
                                cell:
                                {
                                    operator: "contains",
                                    showOperators: false,
                                    template: (el) => {
                                        // adding a customized filter(text box)
                                        $(el.element).removeAttr('data-bind');
                                        $(el.element).addClass("k-input k-textbox form-control");

                                        $(el.element).attr('id', columnField);
                                        const placeHolderId = "#" + columnField + "_placeholder";
                                        $("#adjustmentCode").css("color","#333");
                                        $(el.element).attr('placeholder', $(placeHolderId).text());
                                        let keyUpColField = columnField;
                                        $("body").on("keyup", "#" + keyUpColField, (e) => {
                                            e.preventDefault();
                                            if (e.keyCode == 13) {
                                                this.historySearchGridData();
                                            }
                                        });
                                    }
                                }
                            };
                            break;
                        case "year_1_amount":
                            response.columns[i].filterable = false;
                            break;
                        case "description":
                            response.columns[i]["filterable"] =
                            {
                                cell:
                                {
                                    operator: "contains",
                                    showOperators: false,
                                    template: (el) => {
                                        // adding a customized filter(text box)
                                        $(el.element).removeAttr('data-bind');
                                        $(el.element).addClass("k-input k-textbox form-control");

                                        $(el.element).attr('id', columnField);
                                        const placeHolderId = "#" + columnField + "_placeholder";
                                        $(el.element).attr('placeholder', $(placeHolderId).text());
                                        let keyUpColField = columnField;
                                        $("body").on("keyup", "#" + keyUpColField, (e) => {
                                            e.preventDefault();
                                            if (e.keyCode == 13) {
                                                this.historySearchGridData();
                                            }
                                        });
                                    }
                                }
                            };
                            break;
                    }
                }
            }
            EVENTS.loaderON(gridId);
            let obj= {
                dataSource: {
                    data: [],
                    schema: {
                        model: {
                            fields: {
                            }
                        }
                    },
                },
                columns: response.columns,
                sortable: true,
                scrollable:true,
                filterable: {
                    mode: "row"
                },
                sort: (e) => {
                     //some columns are not sortable
                    let sortField = (e.sort.field !== undefined) ? e.sort.field : "";
                    let sortOrder = (e.sort.dir !== undefined) ? e.sort.dir : "";
                    sortField = (sortOrder == "") ? "" : sortField;

                    // apply sorting to the header grid
                    if (notSortedColumns.indexOf(sortField) < 0 || sortField == "") {
                        let finalJsonData = this.applyBudgetHistoryInputData(sortField, sortOrder);
                        this.getAccountReportingGridData(finalJsonData, true);
                    } else {
                        e.preventDefault();
                    }

                },
                dataBound: (e) => {
                    $(gridId).find(".k-grid-content").css('display', 'none');
                    $(gridId).css("border", "none");
                    $(gridId + " tr").css("border-color", "#c3c3c3");
                    $(gridId).find("thead th:nth-child(2)").css("padding-left", "5px");
                    $(gridId).find(".k-filter-row th:nth-child(2)").css("padding-left", "5px");
                    $("#accountReportingSearchGridSynapse .k-grid-header table tr:first th").each(function (i, e) {
                        let field = $(this).attr("data-field");
                        if (notSortedColumns.indexOf(field) != -1) {
                            $(this).find("a").css("color", "#000000");
                            $(this).addClass("disable-anchor");
                        }
                    });
                    let gridHeight = $(window).height() - 300;
                    $(gridId).find(".k-grid-header").css({'min-height': '100px', 'overflow-x': 'auto', 'overflow': 'auto' });
                    $(gridId).find("thead tr.k-filter-row").addClass("item-active-grey");
                    $(gridId).find("thead tr.k-filter-row th:last-child").append("<button id='searchAccStmtBudgetTreeGrid' class='btn btn-primary' title='Search'>" + localStorage.getItem("searchString") + "</button>");
                    $(gridId).find("thead tr.k-filter-row").addClass("item-active-grey");
                    //search click
                    $("#searchAccStmtBudgetTreeGrid").unbind('click').click(() => {
                        this.historySearchGridData();
                    });
                },
            }
            if (accountGridVar) {
                $(gridId).empty();
                $(gridId).data("kendoGrid").destroy();
            }

            accountGridVar = $(gridId).kendoGrid(obj);
            EVENTS.loaderOFF(gridId);
            this.clearBudgetHistorySearchFilter();
            
        }

        getBudgetHistoryTreelistData(response: any) {
            let gridId = "#accountReportingGridSynapse";
            EVENTS.loaderON(gridId);
            if (response.columns.length > 0) {
                for (let i = 0; i < response.columns.length; i++) {
                    switch (response.columns[i].field) {
                        case "year_1_amount":
                            response.columns[i].template = ' #=kendo.format("{0:' + response.columns[i].format + '}", year_1_amount)# ';
                            break;
                    }
                }
            }

            this.$scope.accountReportingGridSynapseOptions = {
                dataSource: response.dataSource,
                columns: response.columns,
                scrollable: true,
                filterable: true,
                dataBound: (e) => {
                    $(gridId).find(".k-grid-header").css('display', 'none');
                    $(gridId).css('color', '#000');
                    $(gridId).css('border', 'none');
                    $(gridId + " tr:not(.k-treelist-group)").css('font-style', 'italic');
                    $(gridId + " tr td").css({ 'border-left': '0 none', 'border-right': '0 none' });
                    $(gridId + " tr th").css({ 'border-left': '0 none', 'border-right': '0 none' });
                    $(gridId).find(".k-grid-content").css({ 'max-height': $(window).height() - 600, 'overflow-x': 'auto', 'overflow': 'auto' });
                    if (response.dataSource.length == 0) {
                        $('#historyPopupTabSectionSynapse').addClass('cmn-display-none');
                    }
                    else {
                        $('#historyPopupTabSectionSynapse').removeClass('cmn-display-none');
                    }
                },

            }
            this.$timeout(() => {
                let treeList = $(gridId).data("kendoTreeList");
                treeList.setDataSource(response.dataSource);
                treeList.setOptions({ columns: response.columns });
                EVENTS.loaderOFF(gridId);
            }, 200);
            EVENTS.loaderOFF(gridId);
        }


        expandedBudgetChangesHistoryTreeGridSynapse(id: number) {
            let treeList = $("#accountReportingGridSynapse").data("kendoTreeList");
            let dataItems = treeList.dataSource.data();
            $.each(dataItems, function (i, item) {
                item.expanded = id;
            });
            treeList.dataSource.data(dataItems);
        }

        getAccountReportingTreeGridGridData(finalJsonData:any) {

            let gridId = "#accountReportingTreeGridSynapse";
            EVENTS.loaderON(gridId);
            this.$http.post("../api/AccountStmtReportApiController/BudgetChangesGridData", angular.toJson(finalJsonData)).then(response => {

                let dataSource: any = response.data;
                $('#accountReportingTreeGridTitleSynapse').text(dataSource.header[0].title);
                this.$scope.accountReportingTreeGridSynapseOptions = {
                    dataSource: {
                        data:[]
                    },
                    dataBound: () => {
                        let height = $(window).height() / 2 - 120;
                        $(gridId).find(".k-grid-content").css({ 'max-height': '200px', 'overflow-y': 'auto' }); //107352
                        //$(gridId).find(".k-grid-content").css({ 'min-height': '100px', 'max-height': '150px', 'overflow-y': 'auto'});
                        $(gridId).find("tr:last").addClass("semi");
                    },
                    columns: dataSource.columns
                }
                this.$timeout(() => {
                    let treeList = $("#accountReportingTreeGridSynapse").data("kendoTreeList");
                    treeList.setDataSource(dataSource.data);
                    treeList.setOptions({ columns: dataSource.columns });
                    EVENTS.loaderOFF(gridId);
                }, 50);
            }, error => {
                    error.methodName = "getAccountReportingTreeGridGridData";
                    EVENTS.loaderOFF("#accountReportingTreeGridSynapse");
                    ERROR.displayException(error.data, error.status, error.data, error.methodName);
            });
        }

        historySearchGridData () {

            let gridId = "#accountReportingGridSynapse";
            EVENTS.loaderON(gridId);

            let forecastPeriod = $("#synapseBudgetYearIdDropdown").data("kendoDropDownList").value();
            let parsePeriod = new Date(forecastPeriod);
            let month = parsePeriod.getMonth() + 1;
            let year = parsePeriod.getFullYear();
            this.selectedServiceId = '';
            this.selectedServiceName = '';

            let filterObjData = this.getBudgetHistoryTreelistSearchFilterData();

            let dropDownObjData = {
                departments: this.filteredValues["DepartmentFilter"],
                functions: this.filteredValues["FunctionFilter"],
                projects: this.filteredValues["ProjectFilter"],
                freeDim1: this.filteredValues["FreeDim1Filter"],
                freeDim2: this.filteredValues["FreeDim2Filter"],
                nextorg: this.filteredValues["OrgFilter"],
                servs: this.filteredValues["ServiceFilter"],
                monthYear: forecastPeriod,
                level1OrgId: this.orgIdLevel1,
                level2OrgId: this.orgIdLevel2,
                level3OrgId: this.orgIdLevel3,
                level4OrgId: this.orgIdLevel4,
                level5OrgId: this.orgIdLevel5,
                level6OrgId: this.orgIdLevel6,
                level7OrgId: this.orgIdLevel7,
                level8OrgId: this.orgIdLevel8,
                orgId: this.orgMenuId,
                orgLevel: this.orgMenuLevel,
                orgName: this.orgName,
                serviceId: this.selectedServiceId,
                serviceName: this.selectedServiceName,
                accountCodeId: this.budgetHistoryPopupSelectedId,
                budget_year: year,
                period: month,
                isPeriodCheck: this.budgetHistoryPopupSelectedPeriodCheck,
                sortField: "",
                sortOrder: "",
                accountCodeName: this.budgetHistoryPopupSelectedAccountName,
                filterObj: filterObjData
            }


            this.$http.post("../api/MonthlyReportingDocApi/GetBudgetHistoryTreeListData", angular.toJson(dropDownObjData)).then(response => {
                let result:any = response.data;
                $(gridId).data("kendoTreeList").setDataSource(result.dataSource);
                EVENTS.loaderOFF(gridId);
            }, error => {
                    EVENTS.loaderOFF(gridId);
                    ERROR.displayException(error.data, error.status, error.data, "historySearchGridData");
            });
           
        }

        getBudgetHistoryTreelistSearchFilterData() {
            let filterObjData = {};
            filterObjData = {
                date : $("#date").val() || "" ,
                transactioncomment: $("#transactioncomment").val() || "" ,
                adjustmentCodeStatus : $("#adjustmentCodeStatus").val() || "" ,
                updatedby: $("#updatedby").val() || "" ,
                year_1_amount: $("#year_1_amount").val() || "" ,
                account: $("#account").val() || "" ,
                department: $("#department").val() || "" ,
                function : $("#function").val() || "" ,
                project: $("#project").val() || "" ,
                free_dim_1: $("#free_dim_1").val() || "" ,
                free_dim_2: $("#free_dim_2").val() || "" ,
                free_dim_3: $("#free_dim_3").val() || "" ,
                free_dim_4: $("#free_dim_4").val() || "" ,
                accinfocomment: $("#accinfocomment").val() || "" ,
                adjustmentCode : $("#adjustmentCode").val() || "" ,
                description: $("#description").val() || "" ,
            }
            return filterObjData;
        }

        clearBudgetHistorySearchFilter() {
                $("#date").val("") ,
                $("#transactioncomment").val("") ,
                $("#adjustmentCodeStatus").val("") ,
                $("#updatedby").val("") ,
                $("#year_1_amount").val(""),
                $("#account").val("") ,
                $("#department").val("") ,
                $("#function").val("") ,
                $("#project").val(""),
                $("#free_dim_1").val("") ,
                $("#free_dim_2").val("") ,
                $("#free_dim_3").val("") ,
                $("#free_dim_4").val("") ,
                $("#accinfocomment").val("") ,
                $("#adjustmentCode").val("") ,
                $("#description").val("") 
        }

        getFinancialStatusAccountWindow(id, name,orgName) {

            this.MRReportingFinancialStatusWindow = $('#MRReportingFinancialStatusAccountWindowSynapse').kendoWindow({
                modal: true,
                actions: ["close"],
                scrollable: true,
                resizable: false,
                draggable: false,
                width: "95%",
                height: $(window).height() - 100
            }).data("kendoWindow").open();

            let accountLevelDup = id.split("_")[0];
            let accountCodeDup = id.split("_")[1];
            let regex = /[xyz]/;
            if ($('#viewOrgFilter').data('kendoDropDownList')) {

                let viewDropDownValue = $('#viewOrgFilter').data('kendoDropDownList').value();
                if (viewDropDownValue == "0") {
                    this.selectedAccountDetailorgViewId = this.getAccountLevelId(id.split("_")[3]);
                }
            }
            const index_accountLevelDup = accountLevelDup.search(regex);
            if (index_accountLevelDup != -1) {
                accountLevelDup = accountLevelDup.substring(0, index_accountLevelDup);
            }
            const index_accountCodeDup = accountCodeDup.search(regex);
            if (index_accountCodeDup != -1) {
                accountCodeDup = accountCodeDup.substring(0, index_accountCodeDup);
            }
            id = accountLevelDup + "_" + accountCodeDup;
            this.selectedAccountDetailorgViewId = accountLevelDup + "_" + this.selectedAccountDetailorgViewId;
            this.FinancialStatusSelectedOrgName = orgName;
            this.clearFinancialStatusFilterSearch();
            this.getFinancialStatusAccountDetails(id, name);
        }


        getFinancialStatusAccountDetails(id, name) {

            this.selectedAccountDetailId = id;
            this.selectedAccountDetailName = name;

            EVENTS.loaderON('#reportingFinStatusAccountDetailGridSynapse');

            let filterObjData = {};
            filterObjData = this.getFilterObjData();
            let flag = false;
            for (let key in filterObjData) {
                if (filterObjData[key] !== "") {
                    flag = true;
                    break;
                }
            }

            let finalFilterObjData = filterObjData;
            //Bug #86426
            //if (flag === true) {
            //    finalFilterObjData = filterObjData;
            //}
            this.selectedServiceId = '';
            this.selectedServiceName = '';
            let dropDownObjData = this.getDropdownObjData(finalFilterObjData);
            $('#MRReportingFinancialStatusAccountWindowSynapse').parent().css({ 'position': 'fixed', 'top': '10px', 'left': '2.5%' });
            this.$http.get("../MonthlyReport/GetFinStatusAccountDetailGridCol?accountCodeName=" + name).then(response => {
                let responseData:any = response.data;
                let columnsLength = responseData.columns.length;
                for (let t = 0; t < columnsLength; t++) {
                    let columnType = responseData.columns[t].type;
                    let columnField = responseData.columns[t].field;
                    if (columnType == "date") {
                        responseData.columns[t]["filterable"] = {
                            cell: {
                                operator: "contains",
                                dataSource: {},
                                template: (el) => {
                                    $(el.element).kendoDatePicker({
                                        format: "dd.MM.yyyy"
                                    });
                                    $(el.element).removeAttr('data-bind');
                                    $(el.element).css("width", "100%");
                                },
                                showOperators: false
                            }
                        };

                        responseData.columns[t].sortable = {
                            compare: (a, b) => {

                                let formatDate1 = [];
                                let formatDate2 = [];
                                formatDate1 = (a.Date).toString().split(".");
                                formatDate2 = (b.Date).toString().split(".");
                                let newDate1 = new Date(formatDate1[2], formatDate1[1] - 1, formatDate1[0]);
                                let newDate2 = new Date(formatDate2[2], formatDate2[1] - 1, formatDate2[0]);
                                if (newDate1 == newDate2) {
                                    return 0;
                                } else if (newDate1 < newDate2) {
                                    return -1;
                                } else {
                                    return 1;
                                }
                            }
                        };     

                    } else {
                        responseData.columns[t]["filterable"] = {
                            cell: {
                                operator: "contains",
                                dataSource: {},
                                template: (el) => {
                                    $(el.element).removeAttr('data-bind');
                                    $(el.element).addClass("k-input k-textbox");
                                    $(el.element).css("width", "100%");
                                },
                                showOperators: false
                            }
                        };
                    }
                }
                this.MRReportingFinancialStatusWindow.element.prev().find(".k-window-title").html(responseData.header[0].title + '<span class="padding-left5 hand-pointer" data-role="tooltip" id="MRAccountDetailGridDataTooltipSynapse"><img src="../images/icons_monthly_report_information.png"></span>');
                $('#MRAccountDetailGridDataTooltipSynapse img').click(function (e) {
                    MODULES.informationTooltipClick('#MRAccountDetailGridDataTooltipSynapse', responseData.header[0].descriptiontip);
                });

                let columns: any = responseData.columns;
                let dataSourceFinStatus:any = new kendo.data.DataSource({
                    type: "json",
                    pageSize: 15,
                    transport: {
                        read: (e) => {
                            EVENTS.loaderON('#reportingFinStatusAccountDetailGridSynapse');
                            const skip = e.data.skip;
                            let take = e.data.take;
                            if (take == undefined) {
                                take = this.financialStatusAccountPopupTotalRows;
                            }
                            dropDownObjData.sortField = "";
                            dropDownObjData.sortOrder = "";
                            if (e.data.sort !== undefined && e.data.sort.length > 0) {
                                dropDownObjData.sortField = e.data.sort[0].field;
                                dropDownObjData.sortOrder = e.data.sort[0].dir;
                            }

                            this.$http.post("../api/AccountStmtReportApiController/AccDetailGridData?skip=" + skip + "&take=" + take, angular.toJson(dropDownObjData)).then(response => {
                                EVENTS.loaderOFF('#reportingFinStatusAccountDetailGridSynapse');
                                e.success(response.data);
                            }, error => {
                                EVENTS.loaderOFF('#reportingFinStatusAccountDetailGridSynapse');
                                let methodName = 'getFinancialStatusAccountDetails';
                            });
                        }
                    },
                    schema: {
                        model: {
                            fields: {

                            }
                        },
                        data: (response) => {
                            accountTotalRow = response.data.pop();
                            return response.data;
                        },
                        total: (response) => {
                            this.financialStatusAccountPopupTotalRows = response.totalRows;
                            return response.totalRows;
                        }
                    },
                    serverPaging: true,
                    serverFiltering: false,
                    serverSorting: false
                });

                this.$timeout(() => { 
                    $("#reportingFinStatusAccountDetailGridSynapse").kendoGrid({
                     
                        columns:columns,
                        filterable: {
                            mode: "row"
                        },
                        editable: false,
                        scrollable: true,
                        navigatable: true,
                        sortable:true,
                        pageable: {
                            pageSize: 15,
                            pageSizes: [15, 25, 40, "all"],
                            buttonCount: 5
                        },
                        dataBound: (e: kendo.ui.GridDataBoundEvent) => {
                            let currGridLength = $("#reportingFinStatusAccountDetailGridSynapse" + " tr th").length;
                            for (let g = 1; g <= currGridLength; g++) {
                                let widthsL1 = $("#reportingFinStatusAccountDetailGridSynapse" + " tr th:nth-last-child(" + g + ")").width();
                                $(".k-grid-footer-wrap tr td:nth-last-child(" + g + ")").width(widthsL1);
                            }
                            $("#reportingFinStatusAccountDetailGridSynapse").find(".k-grid-content").css('max-height', $(window).height() - 400);
                            $("#reportingFinStatusAccountDetailGridSynapse").find('.k-grid-header th.k-header').css({ "border-left-style": "solid" });
                            // appending id attr to filters
                            $("#reportingFinStatusAccountDetailGridSynapse").find('.k-filter-row th span.k-filtercell').each(function(i, e) {
                                let field = $(this).attr('data-field');
                                $(this).find("input").attr("id", field);
                                $("#" + field).val(finalFilterObjData[field]);
                                if (dropDownObjData['accDetailSearchFilter'] !== null && dropDownObjData['accDetailSearchFilter'][field]) {
                                    $(this).find("input").val(dropDownObjData['accDetailSearchFilter'][field]);
                                }
                            });

                            // apply color to header rows to indicate sorting enabled
                            $("#reportingFinStatusAccountDetailGridSynapse .k-grid-header table tr:first th").each(function(i, el)  {
                                $(this).find("a").css("color", "#4A8EB9");
                            });

                            if (accountTotalRow) {
                                $("#Accountcol").html(accountTotalRow["Account"]);
                                $("#Responsibilitycol").html(accountTotalRow["Responsibility"]);
                                $("#Functioncol").html(accountTotalRow["Function"]);
                                $("#Freedimcol").html(accountTotalRow["Freedim"]);
                                $("#Projectcol").html(kendo.format('{0:n0}', accountTotalRow["Project"]));
                                $("#Amountcol").html(kendo.format('{0:n0}', accountTotalRow["Amount"]));
                                $("#Periodcol").html(accountTotalRow["Period"]);
                                $("#Datecol").html(accountTotalRow["Date"]);
                                $("#Journocol").html(accountTotalRow["Journo"]);
                                $("#ReskAccountcol").html(accountTotalRow["ReskAccount"]);
                                $("#Invoicecol").html(accountTotalRow["Invoice"]);
                                $("#Codecol").html(accountTotalRow["Code"]);
                                $("#Textcol").html(accountTotalRow["Text"]);
                            } else {
                                $("#Accountcol").html("");
                                $("#Responsibilitycol").html("");
                                $("#Functioncol").html("");
                                $("#Freedimcol").html("");
                                $("#Projectcol").html("");
                                $("#Amountcol").html("");
                                $("#Periodcol").html("");
                                $("#Datecol").html("");
                                $("#Journocol").html("");
                                $("#ReskAccountcol").html("");
                                $("#Invoicecol").html("");
                                $("#Codecol").html("");
                                $("#Textcol").html("");
                            }

                            $("#Account,#Responsibility,#Freedim,#Project,#Amount,#Date,#Journo,#ReskAccount,#Invoice,#Code,#Text,#Period").keyup((e) => {
                                e.preventDefault();
                                if (e.keyCode == 13) {
                                    this.getFinancialStatusAccountDetails(id, name);
                                }
                            });
                            

                            
                        }
                    });

                    $("#reportingFinStatusAccountDetailGridSynapse").data("kendoGrid").setDataSource(dataSourceFinStatus);  
                    EVENTS.loaderOFF("#reportingFinStatusAccountDetailGridSynapse");
                    $("#finStatusAccountFilterSearchSynapse").unbind('click').click(() => {
                        this.getFinancialStatusAccountDetails(id, name);
                    });
                      
                }, 50);
                    
            }, error => {
                    EVENTS.loaderOFF("#reportingFinStatusAccountDetailGridSynapse");
                    ERROR.displayException(error.data, error.status, error.data, "getFinancialStatusAccountDetails");
            });


        }

        getDropdownObjData(finalFilterObjData) {
         
            let dropDownObjData = {
                monthYear: $("#synapseBudgetYearIdDropdown").data("kendoDropDownList").value(),
                level1OrgId: this.orgIdLevel1,
                level2OrgId: this.orgIdLevel2,
                level3OrgId: this.orgIdLevel3,
                level4OrgId: this.orgIdLevel4,
                level5OrgId: this.orgIdLevel5,
                level6OrgId: this.orgIdLevel6,
                level7OrgId: this.orgIdLevel7,
                level8OrgId: this.orgIdLevel8,
                orgId: this.orgMenuId,
                orgLevel: this.orgMenuLevel,
                orgName: this.orgName,
                serviceId: this.selectedServiceId,
                serviceName: this.selectedServiceName,
                accountCodeId: this.selectedAccountDetailId,
                accDetailSearchFilter: finalFilterObjData,
                departments: this.filteredValues["DepartmentFilter"],
                functions: this.filteredValues["FunctionFilter"],
                projects: this.filteredValues["ProjectFilter"],
                freeDim1: this.filteredValues["FreeDim1Filter"],
                freeDim2: this.filteredValues["FreeDim2Filter"],
                nextorg: this.filteredValues["OrgFilter"],
                servs: this.filteredValues["ServiceFilter"],
                account: this.filteredValues["AccountFilter"],
                freeDim3: this.filteredValues["FreeDim3Filter"],
                freeDim4: this.filteredValues["FreeDim4Filter"],
                sortField: "",
                sortOrder: ""
            }
                

            if ($('#viewOrgFilter').data('kendoDropDownList')) {// if the dropdown value is org view

                let viewDropDownValue = $('#viewOrgFilter').data('kendoDropDownList').value();

                if (viewDropDownValue == "0") {
                    let orgIdForOrgView = this.selectedAccountDetailId.split("_")[1];
                    dropDownObjData["orgId"] = orgIdForOrgView;
                    dropDownObjData["orgLevel"] = this.orgMenuLevel + 1;
                    dropDownObjData["orgName"] = this.FinancialStatusSelectedOrgName;
                    dropDownObjData["accountCodeId"] = this.selectedAccountDetailorgViewId;
                    dropDownObjData["level" + (this.orgMenuLevel + 1) + "OrgId"] = orgIdForOrgView;
                }
            }
            return dropDownObjData;

        }

        getFilterObjData() {

            let filterObjData = {
                Account: $("#Account").val() || "",
                Responsibility: $("#Responsibility").val() || "",
                Function: $("#Function").val() || "",
                Freedim: $("#Freedim").val() || "",
                Project: $("#Project").val() || "",
                Amount: $("#Amount").val() || "",
                Date: $("#Date").val() || "",
                Journo: $("#Journo").val() || "",
                ReskAccount: $("#ReskAccount").val() || "",
                Invoice: $("#Invoice").val() || "",
                Code: $("#Code").val() || "",
                Text: $("#Text").val() || "",
                Period: $("#Period").val() || "",
            }
            return filterObjData;
        }

        clearFinancialStatusFilterSearch() {
                $("#Account").val("") ,
                $("#Responsibility").val("") ,
                $("#Function").val("") ,
                $("#Freedim").val("") ,
                $("#Project").val("") ,
                $("#Amount").val("") ,
                $("#Date").val("") ,
                $("#Journo").val("") ,
                $("#ReskAccount").val("") ,
                $("#Invoice").val("") ,
                $("#Code").val("") ,
                $("#Text").val("") ,
                $("#Period").val("") 
        }

        closeMRReportFinancialPopup() {
            this.clearFinancialStatusFilterSearch();
            this.MRReportingFinancialStatusWindow.close();
        }

        loadSynapseGraph() {
            $("#accountStatementBarGraph1,#accountStatementBarGraph2,#accountStatementLineGraph1,#accountStatementLineGraph2,#accountStatementBarGraph3").html('');
            this.getAllSynapseGraphs("all");         
        }

        getAllSynapseGraphs = function (loadChart:string) {
            EVENTS.loaderON("#accountStmtBarGraph1Container");
            EVENTS.loaderON("#accountStmtBarGraph2Container");
            EVENTS.loaderON("#accountStmtBarGraph3Container");
            EVENTS.loaderON("#accountStatementLineGraph1Wrapper");
            EVENTS.loaderON("#accountStatementLineGraph2Wrapper");
            let currentDate = this.selectedMonthValue;
            let splitdate = currentDate.split(" ")[0];
            const dd = splitdate.split("/")[0];
            const year = splitdate.split("/")[2];
            var obj = {
                budgetYear: year,
                period: year+dd,
                orgLevel: this.orgMenuLevel,
                orgId: this.orgMenuId,
                orgName: this.orgName,
                level1OrgId: this.orgIdLevel1,
                level2OrgId: this.orgIdLevel2,
                level3OrgId: this.orgIdLevel3,
                level4OrgId: this.orgIdLevel4,
                level5OrgId: this.orgIdLevel5,
                level6OrgId: this.orgIdLevel6,
                level7OrgId: this.orgIdLevel7,
                level8OrgId: this.orgIdLevel8,
                inGrFltrOrg: this.graphOrgFilter,
                inGrFltrAcGrp: this.graphAccFilter,
                inGrFltrService: this.graphServiceFilter,
                isAccDetailsGraph: false
            };
            var finalJSON = JSON.stringify(obj);
           this.$http.post("../api/AccountStatement/GetAccountStatementGraph", finalJSON).then(response => {
                this.accBudOrgGraph = response.data.graphData.accBudOrgGraph;
               this.accBudPerMonthLineGraph = response.data.graphData.accBudPerMonthLineGraph;
               this.budDevLevelGrpGraph = response.data.graphData.budDevLevelGrpGraph;
                this.acuAccBudLineGraph = response.data.graphData.acuAccBudLineGraph;
               this.accBudServiceGraph = response.data.graphData.accBudServiceGraph;
               this.showServiceGraph = response.data.graphData.showServiceGraph;
                if (this.showServiceGraph == true) {
                    $("#accountStmtBarGraph3Container").show();
                }
                else {
                    $("#accountStmtBarGraph3Container").hide();
                }
                switch (loadChart) {
                    case "all":
                        this.getBudgetBarGraph1();
                        this.getBudgetBarGraph2();
                        this.getBudgetLineGraph1();
                        this.getBudgetLineGraph2();
                        this.getBudgetBarGraph3();
                        break;
                    case "first":
                        this.getBudgetBarGraph2();
                        this.getBudgetLineGraph1();
                        this.getBudgetLineGraph2();
                        this.getBudgetBarGraph3();
                        break;
                    case "second":
                        this.getBudgetBarGraph1();
                        this.getBudgetLineGraph1();
                        this.getBudgetLineGraph2();
                        this.getBudgetBarGraph3();
                        break;
                    case "third":
                        this.getBudgetBarGraph1();
                        this.getBudgetBarGraph2();
                        this.getBudgetLineGraph1();
                        this.getBudgetLineGraph2();
                        break;
               }

               EVENTS.loaderOFF("#accountStmtBarGraph1Container");
               EVENTS.loaderOFF("#accountStmtBarGraph2Container");
               EVENTS.loaderOFF("#accountStmtBarGraph3Container");
               EVENTS.loaderOFF("#accountStatementLineGraph1Wrapper");
               EVENTS.loaderOFF("#accountStatementLineGraph2Wrapper");
               
          }, error => {
               EVENTS.loaderOFF("#accountStmtBarGraph1Container");
               EVENTS.loaderOFF("#accountStmtBarGraph2Container");
               EVENTS.loaderOFF("#accountStmtBarGraph3Container");
               EVENTS.loaderOFF("#accountStatementLineGraph1Wrapper");
               EVENTS.loaderOFF("#accountStatementLineGraph2Wrapper");
               ERROR.displayException(error.data, error.status, error.data, 'getBudgetBarGraph1');
            });
        }

        getBudgetBarGraph1 = function () {
                var response = this.accBudOrgGraph;
                var cleanString = DOMPurify.sanitize(response.header.title, domPurifyConfig);
                $("#accountStatementBarGraph1Title").html(cleanString);
                $('#accountStatementBarGraph1TitleDesc img').click(function (e) {
                    MODULES.informationTooltipClick('#accountStatementBarGraph1TitleDesc', response.header.tooltipdesc);
                });
                var cleanString = DOMPurify.sanitize(response.header.amtinunits, domPurifyConfig);
                $("#amtinunits1").html(cleanString);
                var cleanString = DOMPurify.sanitize(response.header.totalbudev, domPurifyConfig);
                $("#totalbudev").html(cleanString);
                let zoomTip = '', zoomText;
                if (response.header.zoomtip != "") {
                    zoomText = true;
                    zoomTip = response.header.zoomtip;
                } else {
                    zoomText = false;
                }

              //  vm.isFilteredBarGraph1 = response.header.isfiltered;
                if (response.header.isfiltered === false) {
                    response.chart.seriesClick = this.onSeriesClickGraph1;
                } else {
                    response.chart.seriesClick = this.onSeriesClickFilteredGraph;
                }
            response.chart.seriesHover = function (e) {
                setTimeout(function () {
                    $("#accountStatementBarGraph1").css("cursor", "pointer");
                    if (e.value < 0 && !$("#accountStmtBarGraph1Container").hasClass("maximize-wrapper")) {
                        var t = $(".accBudOrgGraph-tooltip").parent().css('top');
                        $(".accBudOrgGraph-tooltip").parent().css("top", (parseInt(t) - 60));
                    }
                 },300);
                }

                $("#accountStatementBarGraph1").kendoChart(response.chart);
                $("#accountStatementBarGraph1").data("kendoChart").dataSource.data(response.seriesdata);

                $("#accountStatementBarGraph1").css("height", "500px");
              //  getBudgetBarGraph1response = true;
        }

        getBudgetBarGraph2 = function () {
            var response = this.budDevLevelGrpGraph;
            var cleanString = DOMPurify.sanitize(response.header.title, domPurifyConfig);
            $("#accountStatementBarGraph2Title").html(cleanString);
            $('#accountStatementBarGraph2TitleDesc img').click(function (e) {
                MODULES.informationTooltipClick('#accountStatementBarGraph2TitleDesc', response.header.tooltipdesc);
            });
            var cleanString = DOMPurify.sanitize(response.header.amtinunits, domPurifyConfig);
            $("#amtinunits3").html(cleanString);
            //  vm.isFilteredBarGraph2 = response.header.isfiltered;
            if (response.header.isfiltered === false) {
                response.chart.seriesClick = this.onSeriesClickGraph2;
            } else {
                response.chart.seriesClick = this.onSeriesClickFilteredGraph;
            }
            response.chart.seriesHover = function () {
                $("#accountStatementBarGraph2").css("cursor", "pointer");
            }
            response.chart.zoomable = { "mousewheel": { "lock": "x" }, "selection": { "lock": "x" } };
            response.chart.pannable = { "lock": "x" };
            response.chart.valueAxis.labels.rotation = -45;           
            $("#accountStatementBarGraph2").kendoChart(response.chart);
            $("#accountStatementBarGraph2").css("height", "500px");              
            // getBudgetBarGraph2response = true;
        }

        getBudgetLineGraph1 = function () {    
            var response = this.acuAccBudLineGraph;
            var cleanString = DOMPurify.sanitize(response.header.title, domPurifyConfig);
            $("#accountStatementLineGraph1Title").html(cleanString);
            $('#accountStatementLineGraph1TitleDesc img').click(function (e) {
                MODULES.informationTooltipClick('#accountStatementLineGraph1TitleDesc', response.header.tooltipdesc);
            });
            var cleanString = DOMPurify.sanitize(response.header.amtinunits, domPurifyConfig);
            $("#amtinunits2").html(cleanString);
            $("#accountStatementLineGraph1").kendoChart(response.chart);
            $("#accountStatementLineGraph1").css("height", "500px");
            $("#accountStatementLineGraph1").data("kendoChart").redraw();
        }

        getBudgetLineGraph2 = function () {
            var response = this.accBudPerMonthLineGraph;            
            var cleanString = DOMPurify.sanitize(response.header.title, domPurifyConfig);
            $("#accountStatementLineGraph2Title").html(cleanString);
            $('#accountStatementLineGraph2TitleDesc img').click(function (e) {
                MODULES.informationTooltipClick('#accountStatementLineGraph2TitleDesc', response.header.tooltipdesc);
            });
            var cleanString = DOMPurify.sanitize(response.header.amtinunits, domPurifyConfig);
            $("#amtinunits4").html(cleanString);
            $("#accountStatementLineGraph2").kendoChart(response.chart);
            $("#accountStatementLineGraph2").css("height", "500px");
            $("#accountStatementLineGraph2").data("kendoChart").redraw();
            //  getBudgetLineGraph2response = true;
        }

        getBudgetBarGraph3 = function () {
            var response = this.accBudServiceGraph;
            var cleanString = DOMPurify.sanitize(response.header.title, domPurifyConfig);
            $("#accountStatementBarGraph3Title").html(cleanString);
            $('#accountStatementBarGraph3TitleDesc img').click(function (e) {
                MODULES.informationTooltipClick('#accountStatementBarGraph3TitleDesc', response.header.tooltipdesc);
            });
            var cleanString = DOMPurify.sanitize(response.header.amtinunits, domPurifyConfig);
            $("#amtinunits3").html(cleanString);
            var cleanString = DOMPurify.sanitize(response.header.totalbudev, domPurifyConfig);
            $("#totalbudevBarGraph3").html(cleanString);
            let zoomTip = '', zoomText;
            if (response.header.zoomtip != "") {
                zoomText = true;
                zoomTip = response.header.zoomtip;
            } else {
                zoomText = false;
            }

            //  vm.isFilteredBarGraph1 = response.header.isfiltered;
            if (response.header.isfiltered === false) {
                response.chart.seriesClick = this.onSeriesClickGraph3;
            } else {
                response.chart.seriesClick = this.onSeriesClickFilteredGraph;
            }
            response.chart.seriesHover = function () {
                $("#accountStatementBarGraph3").css("cursor", "pointer");
            }

            $("#accountStatementBarGraph3").kendoChart(response.chart);
            $("#accountStatementBarGraph3").data("kendoChart").dataSource.data(response.seriesdata);

            $("#accountStatementBarGraph3").css("height", "500px");
            //  getBudgetBarGraph1response = true;
        }

        onSeriesClickGraph1 = (e) => {
      //      console.log(e);
        var chart = $("#accountStatementBarGraph1").data("kendoChart");

            this.graphAccFilter = "";
            this.graphServiceFilter = "";
        if (this.graphOrgFilter == "") {
            this.graphOrgFilter = e.dataItem.id;
        } else {
            if (this.graphOrgFilter == e.dataItem.id) {
                this.graphOrgFilter = "";
            } else {
                this.graphOrgFilter = e.dataItem.id;
            }
        }
        var color1 = "#b56666";
        var changeColor1 = "#dcb6b6";
        var color2 = "#367b9a";
        var changeColor2 = "#9dc9dd";
        for (var i = 0; i < chart.options.series[0].data.length; i++) {
            if (this.graphOrgFilter !== "") {
                if (chart.options.series[0].data[i].id == this.graphOrgFilter) {
                    if (e.dataItem.data > 0) {
                        chart.options.series[0].data[i].color = color2;
                    } else {
                        chart.options.series[0].data[i].color = color1;
                    }
                } else {
                    if (chart.options.series[0].data[i].data > 0) {
                        chart.options.series[0].data[i].color = changeColor2;
                    } else {
                        chart.options.series[0].data[i].color = changeColor1;
                    }
                }
            } else {
                if (chart.options.series[0].data[i].data > 0) {
                    chart.options.series[0].data[i].color = color2;
                } else {
                    chart.options.series[0].data[i].color = color1;
                }
            }
        }
        //chart.bind("seriesClick", onSeriesClickFilteredGraph);
        if ($("#accountStmtBarGraph1Container").hasClass("maximize-wrapper")) {
            this.accountStmtBarGraph1MaximizePopup();
        }
        this.getAllSynapseGraphs("first");        

      /*  this.getBudgetBarGraph2();
        this.getBudgetLineGraph1();
        this.getBudgetLineGraph2();*/

        chart.refresh();
        // console.log("org e : ", e);
    }

        onSeriesClickGraph2 = (e) =>  {
        //console.log("non filtered2 : ", vm.isFilteredBarGraph2);
        //console.log(kendo.format("Series click :: {0} ({1}): {2}", e.series.name, e.series.id, e.value));
        var chart = $("#accountStatementBarGraph2").data("kendoChart");
            console.log(e.series);
        this.graphOrgFilter = "";
        this.graphServiceFilter = "";
        if (this.graphAccFilter == "") {
            this.graphAccFilter = e.series.id;
            chart.options.series[e.series.index].opacity = 1;
        } else {
            if (this.graphAccFilter == e.series.id) {
                this.graphAccFilter = "";
            } else {
                this.graphAccFilter = e.series.id;
            }
            //chart.refresh();
        }

        // to hightlight the selected graph axis
        var opacity = (this.graphAccFilter !== "") ? 0.4 : 0.8;
            for (var i = 0; i < chart.options.series.length; i++) {
                console.log(chart.options.series[i]);
           // if (chart.options.series[i].index !== this.graphAccFilter) {
                if (i == e.series.index) {
                chart.options.series[i].opacity = opacity;
            } else {
                chart.options.series[i].opacity = 1;
            }
        }
         this.getAllSynapseGraphs("second");
        //this.getBudgetBarGraph1();
        //this.getBudgetLineGraph1();
        //this.getBudgetLineGraph2();
        chart.refresh();

        }

        onSeriesClickGraph3 = (e) => {
            //      console.log(e);
            var chart = $("#accountStatementBarGraph3").data("kendoChart");

            this.graphOrgFilter = "";
            this.graphAccFilter = "";
            if (this.graphServiceFilter == "") {
                this.graphServiceFilter = e.dataItem.id;
            } else {
                if (this.graphServiceFilter == e.dataItem.id) {
                    this.graphServiceFilter = "";
                } else {
                    this.graphServiceFilter = e.dataItem.id;
                }
            }
            var color1 = "#b56666";
            var changeColor1 = "#dcb6b6";
            var color2 = "#367b9a";
            var changeColor2 = "#9dc9dd";
            for (var i = 0; i < chart.options.series[0].data.length; i++) {
                if (this.graphServiceFilter !== "") {
                    if (chart.options.series[0].data[i].id == this.graphServiceFilter) {
                        if (e.dataItem.data > 0) {
                            chart.options.series[0].data[i].color = color2;
                        } else {
                            chart.options.series[0].data[i].color = color1;
                        }
                    } else {
                        if (chart.options.series[0].data[i].data > 0) {
                            chart.options.series[0].data[i].color = changeColor2;
                        } else {
                            chart.options.series[0].data[i].color = changeColor1;
                        }
                    }
                } else {
                    if (chart.options.series[0].data[i].data > 0) {
                        chart.options.series[0].data[i].color = color2;
                    } else {
                        chart.options.series[0].data[i].color = color1;
                    }
                }
            }
            //chart.bind("seriesClick", onSeriesClickFilteredGraph);
            if ($("#accountStmtBarGraph3Container").hasClass("maximize-wrapper")) {
                this.accountStmtBarGraph3MaximizePopup();
            }
            this.getAllSynapseGraphs("third");

            chart.refresh();
        }

        onSeriesClickFilteredGraph = (e) =>  {
        //console.log("filtered1 : ", vm.isFilteredBarGraph1);
        this.graphAccFilter = "";
        this.graphOrgFilter = "";
        this.graphServiceFilter = "";
        this.getAllSynapseGraphs("all");
        //this.getBudgetBarGraph1();
        //this.getBudgetBarGraph2();
        //this.getBudgetLineGraph1();
        //this.getBudgetLineGraph2();
    }

        accountStmtBarGraph1MaximizePopup = function () {
        $("#accountStmtBarGraph1Container").toggleClass("maximize-wrapper");
        //$scope.currentResolution = $(window).width();
        var chart = $("#accountStatementBarGraph1").data("kendoChart");
        if ($("#accountStmtBarGraph1Container").hasClass("maximize-wrapper")) {
            //$scope.currentResolution <= 1600 ? $("#yearlyBudgetAllocationGrid").css('max-height',$(window).height() / 2 + 80) : $("#yearlyBudgetAllocationGrid").css('max-height',$(window).height() / 2 + 150);
            // $("#accountStmtBarGraph1Container").removeClass("padding15");
            $("#budgetEntrySaveContainer").css("height", "50px");
            $('body').css('overflow', 'hidden');
            $('#collapseAccountStatementBarGraph1Section').height($(window).height() - 60);
            $("#accountStatementBarGraph1").css("height", ($(window).height() - 60));
            $("#accountStmtBarGraph1Container").removeClass("padding-left0");
            $("#ASBarGraph1MaximizeScreenImg").attr("src", "../images/full_screen__minimize.png");
            // chart.options.zoomable = false;
        }
        else {
            // $("#accountStmtBarGraph1Container").addClass("padding15");
            $('body').removeAttr('style');
            $("#collapseAccountStatementBarGraph1Section").css('max-height', '500px');
            $("#accountStatementBarGraph1").css("height", "500px");
            $("#accountStmtBarGraph1Container").addClass("padding-left0");
            $("#ASBarGraph1MaximizeScreenImg").attr("src", "../images/full_screen__maximize.png");
        }
        chart.refresh();
    }
        accountStmtBarGraph3MaximizePopup = function () {
            $("#accountStmtBarGraph3Container").toggleClass("maximize-wrapper");
            //$scope.currentResolution = $(window).width();
            var chart = $("#accountStatementBarGraph3").data("kendoChart");
            if ($("#accountStmtBarGraph3Container").hasClass("maximize-wrapper")) {
                //$scope.currentResolution <= 1600 ? $("#yearlyBudgetAllocationGrid").css('max-height',$(window).height() / 2 + 80) : $("#yearlyBudgetAllocationGrid").css('max-height',$(window).height() / 2 + 150);
                // $("#accountStmtBarGraph1Container").removeClass("padding15");
                $("#budgetEntrySaveContainer").css("height", "50px");
                $('body').css('overflow', 'hidden');
                $('#collapseAccountStatementBarGraph3Section').height($(window).height() - 60);
                $("#accountStatementBarGraph3").css("height", ($(window).height() - 60));
                $("#accountStmtBarGraph3Container").removeClass("padding-left0");
                $("#ASBarGraph3MaximizeScreenImg").attr("src", "../images/full_screen__minimize.png");
                // chart.options.zoomable = false;
            }
            else {
                // $("#accountStmtBarGraph1Container").addClass("padding15");
                $('body').removeAttr('style');
                $("#collapseAccountStatementBarGraph3Section").css('max-height', '500px');
                $("#accountStatementBarGraph3").css("height", "500px");
                $("#accountStmtBarGraph3Container").addClass("padding-left0");
                $("#ASBarGraph3MaximizeScreenImg").attr("src", "../images/full_screen__maximize.png");
            }
            chart.refresh();
        }
        accountStmtBarGraph2MaximizePopup = function () {
        $("#accountStmtBarGraph2Container").toggleClass("maximize-wrapper");
        var chart = $("#accountStatementBarGraph2").data("kendoChart");
        if ($("#accountStmtBarGraph2Container").hasClass("maximize-wrapper")) {
            $("#budgetEntrySaveContainer").css("height", "50px");
            $('body').css('overflow', 'hidden');
            $('#collapseAccountStatementBarGraph2Section').height($(window).height() - 60);
            $("#accountStatementBarGraph2").css("height", ($(window).height() - 60));
            $("#accountStmtBarGraph2Container").removeClass("padding-left0");
            $("#ASBarGraph2MaximizeScreenImg").attr("src", "../images/full_screen__minimize.png");
        }
        else {
            $('body').removeAttr('style');
            $("#collapseAccountStatementBarGraph2Section").css('max-height', '500px');
            $("#accountStatementBarGraph2").css("height", "500px");
            $("#accountStmtBarGraph2Container").addClass("padding-left0");
            $("#ASBarGraph2MaximizeScreenImg").attr("src", "../images/full_screen__minimize.png");
        }
        chart.refresh();
    }
        // Accounting budget graphs
        getBudgetBarGraphMaximize2 = function () {
            var graphId = "#accountStatementBarGraphMax2";
            EVENTS.loaderON(graphId);

            var obj = {
                monthYear: $("#synapseBudgetYearIdDropdown").data("kendoDropDownList").value(),
                orgLevel: this.orgMenuLevel,
                orgId: this.orgMenuId,
                orgName: this.orgName,
                level1OrgId: this.orgIdLevel1,
                level2OrgId: this.orgIdLevel2,
                level3OrgId: this.orgIdLevel3,
                level4OrgId: this.orgIdLevel4,
                level5OrgId: this.orgIdLevel5,
                level6OrgId: this.orgIdLevel6,
                level7OrgId: this.orgIdLevel7,
                level8OrgId: this.orgIdLevel8,
                serviceId: "",
                inGrFltrOrg: this.graphOrgFilter,
                inGrFltrAcGrp: this.graphAccFilter,
                isAccDetailsGraph: true
            };
            var finalJSON = JSON.stringify(obj);
       //     var responsePromise = $http.post("../AccountStatementReport/GetAccPnLGraphBudDevPeLevelGrp", finalJSON);
       //     responsePromise.success(function (response, status, headers, config) {

            this.$http.post("../AccountStatementReport/GetAccPnLGraphBudDevPeLevelGrp", finalJSON).then(response => {

                var response = response.data;

      

                // $("#accountStatementBarGraphMax2Title").html(response.header.title);
                $('#accountStatementBarGraphMax2TitleDesc img').click(function (e) {
                    MODULES.informationTooltipClick('#accountStatementBarGraphMax2TitleDesc', response.header.tooltipdesc);
                });
                this.ASBarGraph2Dialogue.element.prev().find(".k-window-title").parent().css("height", "45px");
                this.ASBarGraph2Dialogue.element.prev().find(".k-window-title").html('<span id="okonomiStatusTitleParentPopup">' + response.header.title + '</span><span class="padding-left5 padding-right10" data-role="tooltip" id="ASRGMaxToolTip"><img src="../images/icons_monthly_report_information.png"></span></span><span class="marginleft40">' + response.header.totalbudev + '</span>');

                $('#ASRGMaxToolTip img').click(function (e) {
                    MODULES.informationTooltipClick('#ASRGMaxToolTip', response.header.tooltipdesc);
                });

                $('#ASBarGraph2MaxWindow_wnd_title').next('.k-window-actions').before('<span id="amtinunitsmax2" style="position: absolute; right: 90px;top:15px;">' + response.header.amtinunits + '</span>');
              
                    let zoomTip = '', zoomText;
                if (response.header.zoomtip != "") {
                    zoomText = true;
                    zoomTip = response.header.zoomtip;
                } else {
                    zoomText = false;
                }

                EVENTS.loaderOFF(graphId);
               // vm.isFilteredBarGraph1 = response.header.isfiltered;
               
                response.chart.seriesHover = function () {
                    $("#accountStatementBarGraphMax2").css("cursor", "pointer");
                }
                $("#accountStatementBarGraphMax2").kendoChart(response.chart);
                $("#accountStatementBarGraphMax2").data("kendoChart").dataSource.data(response.seriesdata);
                $("#accountStatementBarGraphMax2").css("height", ($(window).height() - 60));
                }, error => {
                    EVENTS.loaderOFF(graphId);
                    error.methodName = "Account statement reporting - getBudgetBarGraph1";
                    ERROR.displayException(error.data, error.status, error.data, error.methodName);
                });


        }

        acctStmtBarGraph2MaximizePopup = function () {
            $('#ASBarGraph2MaxWindow').kendoWindow({
                modal: true,
                actions: ["close"],
                scrollable: true,
                resizable: false,
                draggable: false,
                width: "100%"
            }); // localStorage.getItem("MRFPTitle") +
            this.ASBarGraph2Dialogue = $('#ASBarGraph2MaxWindow').data("kendoWindow").open();
            this.ASBarGraph2Dialogue.maximize();
            this.ASMaximizePopupCommonDesign('#ASBarGraph2MaxWindow', '#ASRGMainContentWrapper', '#ASRGMaxToolTip');
            // get account detail maximize graph
            this.getBudgetBarGraphMaximize2();
        }

        // common maximize popup design
        ASMaximizePopupCommonDesign = function (popId, contentWrapper, toolTip) {
            $(popId).parent().css({ 'position': 'fixed', 'top': '0' });
            $('body').css({ 'overflow': 'hidden' }); // .addClass('background-white')
            $(popId).prev().css({ 'margin-top': '-29px', 'border-bottom': '0 none', 'background': '#f4f4f4', 'border': '1px solid #b3b3b3', 'width': '99.8%', 'padding-left': '0px', 'padding-right': '0px', 'height': '30px' });
            $(popId + '_wnd_title').next().find('a:first-child .k-i-close').addClass('pull-right').css({ 'background': 'url(../images/full_screen__minimize.png)', 'margin-top': '0px', 'margin-right': '2px', 'height': '25px', 'width': '25px', 'font': '1px/1 sans-serif', 'position': 'absolute', 'background-repeat': 'no-repeat' });
            $(popId + '_wnd_title').next().find('a:first-child').attr('id', 'maxToMinClose').css({ "opacity": "1", "width": "45px", "height": "30px" });
            $(popId + '_wnd_title').next().css({ 'width': '50px', 'margin-top': '5px' });
            $('#amtinunitsmax2').css({ 'width': 'auto', 'margin-top': '0px' });
            $(popId + '_wnd_title').next('div').find('a.k-window-action').removeAttr('href');
            $(popId + '_wnd_title').css('color', '#000');
            $(popId).parent().height($(window).height() - 25).addClass('padding0 padding-top30');
            $(contentWrapper + ' .acct-stmt-report-maxwindow').height($(window).height() - 75);
            $('.k-window-actions #maxToMinClose').unbind('click').click(function (e) {
                e.preventDefault();
                $(this).removeAttr('href');
                $('body').removeAttr('style');
                $('.k-window-actions a:first-child #maxMinText').remove();
            });
        }

        getOrgFilterData = function () {
            EVENTS.loaderON("#accountStmtBarGraph1Container");
            let currentDate = this.selectedMonthValue;
            let splitdate = currentDate.split(" ")[0];
            const month = splitdate.split("/")[0];
            const year = splitdate.split("/")[2];
            const forecastperiod = year + month;
            this.$http.get("../api/AccountStatement/GetFinAcViewTypes?orgLevel=" + this.orgMenuLevel + "&period=" + forecastperiod).then(response => {
                let responseData = response.data;
                this.orgViewFilterDS = responseData;

                if (responseData.length == 1) {

                    this.orgViewFilterKey = responseData[0].Key;
                    localStorage.setItem("rememberViewFilterSelection", this.orgViewFilterKey);
                } else {

                    if (localStorage.getItem("rememberViewFilterSelection")) {
                        this.orgViewFilterKey = localStorage.getItem("rememberViewFilterSelection");
                    } else {
                        for (let iterate = 0; iterate < responseData.length; iterate++) {
                            if (responseData[iterate].IsSelected === true) {
                                this.orgViewFilterKey = responseData[iterate].Key;
                            }
                        }
                    }

                }

                console.log(this.orgViewFilterKey," filter");
                this.getaccountStatementColumnSelector();
                EVENTS.loaderOFF("#accountStmtBarGraph1Container");
            }, error => {
                EVENTS.loaderOFF("#accountStmtBarGraph1Container");
                ERROR.displayException(error.data, error.status, error.data, 'getOrgFilterData');
            });
        }
        changeOrgViewFilterOption = function (event: any) {
            var ds = event.sender.dataSource.data();
            this.orgViewFilterKey = ds[event.sender.selectedIndex].Key;
            localStorage.setItem("rememberViewFilterSelection", this.orgViewFilterKey);
            this.getAccountsGridData();
        }

        getPeriodTreelistColumnSelector() {
            const currentDate = this.selectedMonthValue;
            const splitdate = currentDate.split(" ")[0];
            const dd = splitdate.split("/")[0];
            const year = splitdate.split("/")[2];
            this.$http.get(`../api/AccountStatement/GetColumnConfigForPerPeriodTab?budgetYear=${year}&isDynamic=false&widgetId=********-0000-0000-0000-********0000`).then(response => {
                let responseData: any = response.data;
                if (localStorage.getItem("synapseASPeriodColumns")) {
                    this.synapsePeriodColumns = JSON.parse(localStorage.getItem("synapseASPeriodColumns"));
                } else {
                    this.synapsePeriodColumns = responseData;
                }
                this.accountColSelectorClick('#synapsePeriodColumnSelector', '#synapsePeriodPopupColSelector');
                this.showColumnSelectorSave();
                this.getPeriodGridData();
            }, error => {
                    ERROR.displayException(error.data, error.status, error.data, "getPeriodTreelistColumnSelector");
            });
        }

        periodGridColumnSelectorOk() {
            let periodGridColumnsStr = JSON.stringify(this.synapsePeriodColumns);
            localStorage.setItem("synapseASPeriodColumns", periodGridColumnsStr);
            this.getPeriodGridData();
        }

        periodGridColumnSelectorSave() {
            let periodGridColumnsStr = JSON.stringify(this.synapsePeriodColumns);
            localStorage.setItem("synapseASPeriodColumns", periodGridColumnsStr);
            EVENTS.loaderON("#synapsePeriodColumnSelectorContent");
            const currentDate = this.selectedMonthValue;
            const splitdate = currentDate.split(" ")[0];
            const dd = splitdate.split("/")[0];
            const year = splitdate.split("/")[2];
            const finalObj = { selectedColumns: this.synapsePeriodColumns, budgetYear: year }
            const finalJosnObj = angular.toJson(finalObj);
            this.$http.post("../api/AccountStatement/SaveColumnConfigForPerPeriodTab", finalJosnObj).then(response => {
                MODULES.saveConfirmation();
                this.getPeriodGridData();
                EVENTS.loaderOFF("#synapsePeriodColumnSelectorContent");
            }, error => {
                    EVENTS.loaderOFF("#synapsePeriodColumnSelectorContent");
                    error.methodName = "Account statement reporting - periodGridColumnSelectorSave";
                ERROR.displayException(error.data, error.status, error.data, error.methodName);
            });

        }

        getPeriodGridData() {
            const gridId = "#periodGrid";
            EVENTS.loaderON(gridId);

            const currentDate = this.selectedMonthValue;
            const splitdate = currentDate.split(" ")[0];
            const dd = splitdate.split("/")[0];
            const year = splitdate.split("/")[2];
            
            const filterValuesArray = [];
            let obj: any = {};
            for (let g = 0; g < Object.keys(this.filteredPeriodValues).length; g++) {
                if (Object.values(this.filteredPeriodValues)[g].length > 0) {
                    obj = {};
                    obj.filterId = Object.keys(this.filteredPeriodValues)[g];
                    obj.filterValues = Object.values(this.filteredPeriodValues)[g];
                    filterValuesArray.push(obj);
                }
            }
            if (localStorage.getItem("synapseASPeriodColumns")) {
                this.synapsePeriodColumns = JSON.parse(localStorage.getItem("synapseASPeriodColumns"));
            }

            // Grid header title description
            $('#synapsePeriodTitleDesc img').click(function (e) {
                MODULES.informationTooltipClick('#synapsePeriodTitleDesc', $("#accountStatementPeriodTooltipDesc").text());
            });

            const filterObj = {
                filters: filterValuesArray,
                orgId: this.orgMenuId,
                orgLevel: this.orgMenuLevel,
                period: year + dd,
                budgetYear: year,
                viewType: "",
                selectedColumns: this.synapsePeriodColumns,
                level1OrgId: this.orgIdLevel1,
                level2OrgId: this.orgIdLevel2,
                level3OrgId: this.orgIdLevel3,
                level4OrgId: this.orgIdLevel4,
                level5OrgId: this.orgIdLevel5,
                level6OrgId: this.orgIdLevel6,
                level7OrgId: this.orgIdLevel7,
                level8OrgId: this.orgIdLevel8
            }

            const finalJSON = JSON.stringify(filterObj);

            // reload collapse/expand buttons
            $("#hiddenBordersListWrapper li").removeClass("in");

            this.$http.post<any>("../api/AccountStatement/GetAccountStatementDataPerPeriod", finalJSON).then(resp => {
            
                const response = resp.data;
                EVENTS.loaderOFF(gridId);
                
                const colsConofig = this.synapsePeriodColumns;
                if (colsConofig[0].isChecked == true) {
                    response.columns[0]["locked"] = true;
                }

                // data-title array
                const dataTitleArr = [];
                for (let c = 0; c < response.columns.length; c++) {
                    const col = response.columns[c];
                    dataTitleArr.push(col.title);
                }
                const data = response.data;
                this.acctStmtPeriodData = JSON.stringify({ dataSource: data, columns: response.columns });

                this.excelExportPeriodData = {
                    columns: response.columns,
                    dataSource: data
                }

                const dataSource = new kendo.data.TreeListDataSource({
                    data: data,
                    schema: {
                        model: {
                            id: "id",
                            expanded: false
                        }
                    }
                });
                this.$scope.periodGridOptions = {
                    dataSource: dataSource,
                    resizable: true,
                    expand: (ev) => {
                        setTimeout(function () {
                            $(gridId).find('.k-grid-content-locked').find("tr:last td").addClass('semi');//for last row always bold 
                            $(gridId).find("tr:last").addClass("semi");//for last row always bold   
                        }, 250);
                    },
                    collapse: (ev) => {
                        setTimeout(function () {
                            $(gridId).find('.k-grid-content-locked').find("tr:last td").addClass('semi');//for last row always bold 
                            $(gridId).find("tr:last").addClass("semi");//for last row always bold   
                        }, 250);
                    },
                    dataBound: (ev) => {
                        let i = 0;
                        // expand/collase of month columns
                        const selectedColumns = this.synapsePeriodColumns;
                        $("#periodGrid").find("thead tr:first-child th").each(function (e) {
                            if (i > 0) {
                                $(this).html("");
                                $(this).html("<div class='col-md-12 padding0 cmn-relative-position'><span class='col-md-11 padding0'>" + $(this).attr('data-title') + "</span><span class='col-md-1 padding0 glyphicon glyphicon-chevron-left less-than-symbol collapsed hand-pointer' aria-hidden='true' id='" + $(this).attr('data-title') + "_id' data-toggle='collapse' data-parent='#accordion' data-target='#" + i + "_id'></span>");
                                $("#" + i + "_id").attr("data-title", $(this).attr('data-title'));
                            }
                            const currId = i + "_id";
                            const monthArr = ["", "jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec", "yar"];

                            $("#" + currId).click(function (e) {
                                // expand selected column from number buttons
                                const expandColumn = $(this).attr("id").split("_")[0];
                                const collapsedDataTitle = $(this).attr("data-title");
                                if (selectedColumns.length > 0) {
                                    for (let j = 1; j <= selectedColumns.length; j++) {
                                        if (selectedColumns[j - 1].isChecked == true) {
                                            const columnField = monthArr[expandColumn] + "_" + selectedColumns[j - 1].key;
                                            $("#periodGrid .k-grid-header-wrap th[data-field='" + columnField + "']").show();
                                            $("#periodGrid tr td >." + columnField + "-items").each(function () {
                                                $(this).closest("td").show();
                                            });
                                        }
                                    }
                                }
                                $("#periodGrid .k-grid-header-wrap th[data-title='" + collapsedDataTitle + "']").show();
                                // remove all number buttons
                                $("#hiddenBordersListWrapper li#" + currId).removeClass("in");
                                $(gridId).find('.k-grid-content-locked').find("tr:last td").addClass('semi');//for last row always bold 
                                $(gridId).find("tr:last").addClass("semi");//for last row always bold                       
                            });

                            $("#expandAll").unbind('click').click(function (e) {
                                // expand all columns of treelist
                                for (let k = 1; k <= 13; k++) {
                                    if (selectedColumns.length > 0) {
                                        for (let m = 1; m <= selectedColumns.length; m++) {
                                            if (selectedColumns[m - 1].isChecked == true) {
                                                const columnField = monthArr[k] + "_" + selectedColumns[m - 1].key;
                                                $("#periodGrid .k-grid-header-wrap th[data-field='" + columnField + "']").show();
                                                $("#periodGrid tr td >." + columnField + "-items").each(function () {
                                                    $(this).closest("td").show();
                                                });
                                            }
                                        }
                                        $("#periodGrid .k-grid-header-wrap th[data-title='" + dataTitleArr[k] + "']").show();
                                        $("#hiddenBordersListWrapper li").removeClass("in");
                                    }
                                }
                                $(gridId).find('.k-grid-content-locked').find("tr:last td").addClass('semi');//for last row always bold 
                                $(gridId).find("tr:last").addClass("semi");//for last row always bold                       
                            });

                            $(this).find('.less-than-symbol').click(function (e) {
                                // collapse/hide selected column from treelist
                                const index = $(this).closest(".k-grid-header-wrap th").index() + 1;
                                const dataTitle = $(this).closest(".k-grid-header-wrap th").attr("data-title");

                                if (selectedColumns.length > 0) {
                                    // each column selector columns
                                    for (let j = 1; j <= selectedColumns.length; j++) {
                                        if (selectedColumns[j - 1].isChecked == true) {
                                            const columnField = monthArr[index] + "_" + selectedColumns[j - 1].key;
                                            $("#periodGrid .k-grid-header-wrap th[data-field='" + columnField + "']").hide();
                                            $("#periodGrid tr td >." + columnField + "-items").each(function () {
                                                $(this).closest("td").hide();
                                            });
                                        }
                                    }
                                }
                                // hide base column
                                $("#periodGrid .k-grid-header-wrap th[data-title='" + dataTitle + "']").hide();
                                $(gridId).find('.k-grid-content-locked').find("tr:last td").addClass('semi');//for last row always bold 
                                $(gridId).find("tr:last").addClass("semi");//for last row always bold                       
                            });

                            const firstSubColumn = response.columns[1].columns[0].field.split('_')[1] || '';
                            if(firstSubColumn) {
                                for (let b = 2; b <= monthArr.length; b++) {
                                    let columnField = monthArr[b] + "_" + firstSubColumn;
                                    $("#periodGrid .k-grid-header-wrap th[data-field='" + columnField + "']").css("border-left-style", "solid");
                                    $("#periodGrid .k-grid-content td[class='" + firstSubColumn + "-items-attrs']").addClass("period-grid-sub-border-style");
                                }
                            }
                            i++;
                                              
                        });

                        $(gridId).find('.k-grid-content-locked').find("tr:last td").addClass('semi');//for last row always bold 
                        $(gridId).find("tr:last").addClass("semi");//for last row always bold    

                        $(gridId).css({ 'max-height': '600', 'padding-top': '50px' });
                        $(gridId + ' .k-grid-header-wrap tr:first-child').find('th').css({ 'background': '#f4f4f4' });
                        $(gridId + ' .k-grid-header-wrap tr:first-child').find('th').not('th:first-child').css({ 'border-left': '1px solid #c3c3c3' });

                        // make some spacec between last row and horizantal scroll bar
                        $("#periodGrid .k-grid-content-locked table").append("<tr><td>&nbsp;</td></tr>");
                        $("#periodGrid .k-grid-content table").append("<tr><td colspan='12'>&nbsp;</td></tr>");

                        $(gridId + ' .k-grid-header-wrap tr:first-child th:last-child').css('border-right', '0');
                        $('.expand-collapse-filter-sec').css('height', $('.filter-contents').height());
                        $('.expand-collapse-filter-sec').removeClass('not-active');
                        $("#periodGrid > div.k-grid-header > div.k-grid-header-wrap").css("width", $("#periodGrid .k-grid-content").width());
                        
                    },
                    columns: response.columns,
                    //scrollable: true
                }
                setTimeout(function () {
                    $(window).resize(() => {
                        if ($('#periodGrid').data("kendoTreeList")) {
                            $('#periodGrid').data("kendoTreeList").resize();
                            $("#periodGrid > div.k-grid-header > div.k-grid-header-wrap").css("width", $("#periodGrid .k-grid-content").width());
                        }
                    });
                }, 1000);
                EVENTS.loaderOFF(gridId);
            }, error => {
                    EVENTS.loaderOFF(gridId);
                //const methodName = 'Account statement reporting - getperiodGridData';
                    EVENTS.loaderOFF(gridId);
                    console.log("getperiodGridData : ", error);
                //ERROR.displayException(response, status, response.MessageDetail, methodName);
            });
        }
      
        getPeriodFilterData() {
            EVENTS.loaderON("#accStatementPeriodFilterSection");
            const currentDate = this.selectedMonthValue;
            const splitdate = currentDate.split(" ")[0];
            const dd = splitdate.split("/")[0];
            const year = splitdate.split("/")[2];
            this.$http.get<FilterMainResponse[]>("../api/AccountStatement/GetFilterDataPerPeriod?budgetYear=" + year + "&orgLevel=" + this.orgMenuLevel + "&orgId=" + this.orgMenuId).then(response => {

                let filterJson: FilterMainResponse[] = response.data;
                this.filterPeriodDataResponse = filterJson;
                let filterHTML = '', filterInnerHTML = '', countHtml = '';
                for (let g = 0; g < filterJson.length; g++) {
                    filterInnerHTML = '';
                    this.filteredPeriodValues[filterJson[g].filterId] = [];
                    this.clearPeriodFilterShow[g] = false;
                    this.showPeriodCloseImage[g] = false;
                    this.loadAllPeriodFilterItems[g] = false;
                    let dropdownValue = '';
                    for (let h = 0; h < filterJson[g].filterDropdown.length; h++) {
                        if (filterJson[g].filterDropdown[h].Value.length > 26) {
                            dropdownValue = filterJson[g].filterDropdown[h].Value.substring(0, 15) + '..';
                        } else {
                            dropdownValue = filterJson[g].filterDropdown[h].Value;
                        }
                        filterInnerHTML = filterInnerHTML + '<li style="height:23px;" ng-click="vm.selectUnselectPeriodFilterList(\'periodFilter' + filterJson[g].filterId + filterJson[g].filterDropdown[h].Key + '\',\'' + filterJson[g].filterDropdown[h].Key + '\',\'' + filterJson[g].filterId + '\',\'' + g + '\')" id=periodFilter' + filterJson[g].filterId + filterJson[g].filterDropdown[h].Key + ' class="hand-pointer bottom5 padding1 filter-item"><div class="col-md-3 padding0">' + filterJson[g].filterDropdown[h].Key + '</div><div class="col-md-9 padding0" title=\'' + filterJson[g].filterDropdown[h].Value + '\'>' + dropdownValue + '</div></li>';
                    }
                    this.totalPeriodValues[g] = filterJson[g].filterDropdown.length;
                    if (filterJson[g].totalCount > this.totalPeriodValues[g]) {
                        this.checkMorePeriod[g] = true;
                    }
                    else {
                        this.checkMorePeriod[g] = false;
                    }
                    if (this.totalPeriodValues[g] >= filterJson[g].totalCount) {
                        countHtml = '<div class="col-md-12 align-center" id="periodListCountData' + g + '">' + filterJson[g].filterDropdown.length + ' ' + $('#cmnShownLabel').html() + '<a ng-show="vm.checkMorePeriod[' + g + ']" ng-click="vm.showMorePeriodFilterData(\'periodListFilterData' + g + '\',\'' + 'periodListCountData' + g + '\',\'' + g + '\',\'' + filterJson[g].totalCount + '\',\'' + filterJson[g].filterId + '\',0)">&nbsp' + $('#cmnShowLessLabel').html() + '</a></div>';
                    } else {
                        countHtml = '<div class="col-md-12 align-center" id="periodListCountData' + g + '">' + filterJson[g].filterDropdown.length + ' ' + $('#cmnShownLabel').html() + '<a ng-show="vm.checkMorePeriod[' + g + ']" ng-click="vm.showMorePeriodFilterData(\'periodListFilterData' + g + '\',\'' + 'periodListCountData' + g + '\',\'' + g + '\',\'' + filterJson[g].totalCount + '\',\'' + filterJson[g].filterId + '\',\'' + this.totalPeriodValues[g] + '\')">&nbsp' + localStorage.getItem('showAllText') + '</a></div>';
                    }
                    $('#commonSynapsePeriodFilterWrapper').html(' ');
                    filterHTML = filterHTML + '<div class="col-md-12 padding0 padding-bottom10">' +
                        '<a href="#" class="black-color font13 collapsed" data-toggle="collapse" data-parent="#accordion" data-target="#collapsePeriodFilter' + g + '"> <i class="fa fa-chevron-down collapse-drop-icon" aria-hidden="true"> </i>' + filterJson[g].filterHeader + '</a>' +
                        '<div class="panel-collapse collapse" id="collapsePeriodFilter' + g + '">' +
                        '<div class="col-md-12">' +
                        '<div class="col-md-6"></div>' +
                        '<div class="col-md-6 padding0">  <span style="position:absolute;top:-14px;font-size:12px;" > ' + localStorage.getItem('selectAll') + '&nbsp <input class="selectAllPeriodChk" type="checkbox" id="selectAllPeriodChk_' + filterJson[g].filterId + '" ng-click="vm.selectAllPeriodFilter(\'' + filterJson[g].filterId + '\',\'' + g + '\')" /> </span><div style="float:right;" ng-show="vm.clearPeriodFilterShow[' + g + ']"><div class="btn btn-primary border-radius0" style="position:absolute;top:-14px;padding:0px 2px 0px 2px;left:70%;font-size:12px;" ng-click="vm.clearPerPeriodFilter(\'' + filterJson[g].filterId + '\',\'' + g + '\')">&nbsp' + localStorage.getItem('clearText') + '</div>' +
                        '</div></div>' +
                        '</div>' +
                        '<input type="textbox" style="width:100%;" class="top10" ng-model="vm.filteredPeriodInputText[' + g + ']" ng-keyup="vm.searchPeriodFilterData(\'periodListFilterData' + g + '\',\'' + g + '\',$event,\'' + filterJson[g].filterId + '\',\'' + filterJson[g].totalCount + '\')">' +
                        '<ul class="col-md-12 padding0 top10 list-style-none min-height20" id="periodListFilterData' + g + '">' + filterInnerHTML +
                        '</ul>' + countHtml +
                        '</div>' +
                        '</div>';
                }
                const content = this.$compile(filterHTML)(this.$scope);
                $('#commonSynapsePeriodFilterWrapper').append(content);

                // to avoid loading filters again on tab click - 78471
                this.periodFilterLoaded = true;

                EVENTS.loaderOFF("#accStatementPeriodFilterSection");
            }, error => {
                EVENTS.loaderOFF("#accStatementPeriodFilterSection");
                    ERROR.displayException(error.data, error.status, error.data, "getPeriodFilterData");
            });
        }
        showMorePeriodFilterData(id: any, countId: any, val, totalCount, filterId, skip, selectAllChk) {
            const currentDate = this.selectedMonthValue;
            const splitdate = currentDate.split(" ")[0];
            const year = splitdate.split("/")[2];
            let isLoadAllData = true;
            if (skip == '0') {
                this.showPeriodCloseImage[val] = false;
                this.filteredPeriodInputText[val] = [];
                this.filteredPeriodValues[filterId] = [];
                this.clearPeriodFilterShow[val] = false;
                $("#" + id).html('');
                isLoadAllData = false;
                this.loadAllPeriodFilterItems[val] = false;
                $("#selectAllPeriodChk_" + filterId).prop('checked', false);
            } else {

                this.loadAllPeriodFilterItems[val] = true;
            }
            $("#" + countId).html('');
            EVENTS.loaderON("#" + id);
            //for show/hide apply filter
            this.showApplyPeriodFilter = false;
            for (let i = 0; i < this.filterPeriodDataResponse.length; i++) {
                if (this.filteredPeriodValues[this.filterPeriodDataResponse[i].filterId].length > 0) {
                    this.showApplyPeriodFilter = true;
                }
            }
            this.$http.get("../api/AccountStatement/GetFilterDataByIdPerPeriod?budgetYear=" + year + "&orgLevel=" + this.orgMenuLevel + "&orgId=" + this.orgMenuId + "&filterId=" + filterId + "&skip=" + skip + "&isLoadAll=" + isLoadAllData).then(response => {
                let filterJson: any = response.data;
                this.filterPeriodDataResponse[val].filterDropdown = [];
                let filterInnerHTML = '', countHtml = '';
                let dropdownValue = '';
                for (let h = 0; h < filterJson.filterDropdown.length; h++) {
                    if (filterJson.filterDropdown[h].Value.length > 26) { dropdownValue = filterJson.filterDropdown[h].Value.substring(0, 15) + '..'; } else { dropdownValue = filterJson.filterDropdown[h].Value; }
                    filterInnerHTML = filterInnerHTML + '<li style="height:23px;" ng-click="vm.selectUnselectPeriodFilterList(\'periodFilter' + filterId + '' + filterJson.filterDropdown[h].Key + '\',\'' + filterJson.filterDropdown[h].Key + '\',\'' + filterId + '\',\'' + val + '\')" id=periodFilter' + filterId + '' + filterJson.filterDropdown[h].Key + ' class="hand-pointer bottom5 padding1 filter-item"><div class="col-md-3 padding0 align-left">' + filterJson.filterDropdown[h].Key + '</div><div class="col-md-9 padding0" title=\'' + filterJson.filterDropdown[h].Value + '\'>' + dropdownValue + '</div></li>';
                    this.filterPeriodDataResponse[val].filterDropdown.push(filterJson.filterDropdown[h]);
                }

               /* this.totalPeriodValues[val] = parseInt(skip) + filterJson.filterDropdown.length;
                if (filterJson.totalCount <= 10) {
                    this.checkMorePeriod[val] = false;
                }
                else {
                    this.checkMorePeriod[val] = true;
                }
                if (this.totalPeriodValues[val] >= totalCount) {
                    countHtml = '<span>' + this.totalPeriodValues[val] + ' ' + $('#cmnShownLabel').html() + '<a ng-show="vm.checkMorePeriod[' + val + ']" ng-click="vm.showMorePeriodFilterData(\'periodListFilterData' + val + '\',\'' + 'periodListCountData' + val + '\',\'' + val + '\',\'' + totalCount + '\',\'' + filterId + '\',0)">&nbsp' + $('#cmnShowLessLabel').html() + '</a></span>';
                } else {
                    countHtml = '<span>' + this.totalPeriodValues[val] + ' ' + $('#cmnShownLabel').html() + '<a ng-click="vm.showMorePeriodFilterData(\'periodListFilterData' + val + '\',\'' + 'periodListCountData' + val + '\',\'' + val + '\',\'' + totalCount + '\',\'' + filterId + '\',\'' + this.totalPeriodValues[val] + '\')">&nbsp' + localStorage.getItem('showMoreText') + '</a></span>';
                }
                const countContent = this.$compile(countHtml)(this.$scope);
                $("#" + countId).html('').append(countContent);
                const content = this.$compile(filterInnerHTML)(this.$scope);
                if (skip == '0') {
                    $("#" + id).html('').append(content);
                } else {
                    $("#" + id).append(content);
                }*/


                this.totalPeriodValues[val] = filterJson.filterDropdown.length;

                if (filterJson.totalCount <= 10) {
                    this.checkMorePeriod[val] = false;
                }
                else {
                    this.checkMorePeriod[val] = true;
                }
                if (skip == '0') {
                    countHtml = '<span>' + this.totalPeriodValues[val] + ' ' + $('#cmnShownLabel').html() + '<a ng-click="vm.showMorePeriodFilterData(\'periodListFilterData' + val + '\',\'' + 'periodListCountData' + val + '\',\'' + val + '\',\'' + totalCount + '\',\'' + filterId + '\',\'' + this.totalPeriodValues[val] + '\',false)">&nbsp' + localStorage.getItem('showAllText') + '</a></span>';

                } else {
                    countHtml = '<span>' + this.totalPeriodValues[val] + ' ' + $('#cmnShownLabel').html() + '<a ng-show="vm.checkMorePeriod[' + val + ']" ng-click="vm.showMorePeriodFilterData(\'periodListFilterData' + val + '\',\'' + 'periodListCountData' + val + '\',\'' + val + '\',\'' + totalCount + '\',\'' + filterId + '\',0,false)">&nbsp' + $('#cmnShowLessLabel').html() + '</a></span>';
                }

                const countContent = this.$compile(countHtml)(this.$scope);
                $("#" + countId).html('').append(countContent);

                const content = this.$compile(filterInnerHTML)(this.$scope);
                $("#" + id).html('').append(content);

                EVENTS.loaderOFF("#" + id);

                if (selectAllChk) {
                    this.filteredPeriodValues[filterId] = [];
                    this.clearPeriodFilterShow[val] = true;
                    this.showApplyPeriodFilter = true;
                    $("#periodListFilterData" + val).find('li').addClass('item-selected');
                    for (let i = 0; i < filterJson.filterDropdown.length; i++) {
                        this.filteredPeriodValues[filterId].push(filterJson.filterDropdown[i].Key);
                    }
                }

            }, error => {
                EVENTS.loaderOFF("#" + id);
                    ERROR.displayException(error.data, error.status, error.data, "showMorePeriodFilterData");
            });
        }
        clearSearchedPeriodItems(id, val) {
            this.filteredPeriodInputText[val] = [];
            this.showPeriodCloseImage[val] = false;
            this.getPeriodFilterData(); //bug 76629 
            this.showApplyPeriodFilter = false; //bug 76629 
        }

        searchPeriodFilterData(id: any, val, e, filterId, totalCount) {
            const key = e.which;
            if (key == 13)  // the enter key code
            {
                EVENTS.loaderON("#" + id);
                if (!this.filteredPeriodInputText[val]) {
                    this.showMorePeriodFilterData('periodListFilterData' + val, 'periodListCountData' + val, val, totalCount, filterId, 0,false);
                    this.showPeriodCloseImage[val] = false;
                } else {
                    this.showPeriodCloseImage[val] = true;
                    $('#periodListCountData' + val).html(' ');
                    const currentDate = this.selectedMonthValue;
                    const splitdate = currentDate.split(" ")[0];
                    const year = splitdate.split("/")[2];
                    this.$http.get("../api/AccountStatement/SearchFilterValuesPerPeriod?budgetYear=" + year + "&orgLevel=" + this.orgMenuLevel + "&orgId=" + this.orgMenuId + "&filterId=" + filterId + "&searchString=" + this.filteredPeriodInputText[val] + "").then(response => {
                        //removed unselected values
                        $('#periodListFilterData' + val + ' li:not(.item-selected)').remove();
                        const filterJson: any = response.data;
                        let filterInnerHTML = '';
                        if (filterJson.filterDropdown.length == 0) {
                            filterInnerHTML = '<li class="border1" style="text-align:center;">' + $("#noResultDataLabel").html() + '</li>';
                        }
                        else {
                            for (let h = 0; h < filterJson.filterDropdown.length; h++) {
                                if (jQuery.inArray(filterJson.filterDropdown[h].Key, this.filteredPeriodValues[filterId]) == -1) {
                                    filterInnerHTML = filterInnerHTML + '<li style="height:23px;" ng-click="vm.selectUnselectPeriodFilterList(\'periodFilter' + filterId + '' + filterJson.filterDropdown[h].Key + '\',\'' + filterJson.filterDropdown[h].Key + '\',\'' + filterId + '\',\'' + val + '\')" id=periodFilter' + filterId + '' + filterJson.filterDropdown[h].Key + ' class="hand-pointer bottom5 padding1 filter-item searched-items"><div class="col-md-3 padding0">' + filterJson.filterDropdown[h].Key + '</div><div class="col-md-9 padding0">' + filterJson.filterDropdown[h].Value + '</div></li>';
                                }
                            }
                        }
                        let content = this.$compile(filterInnerHTML)(this.$scope);
                        $("#" + id).append(content);
                        EVENTS.loaderOFF("#" + id);
                    }, error => {
                        EVENTS.loaderOFF("#" + id);
                            ERROR.displayException(error.data, error.status, error.data, "searchPeriodFilterData");
                    });
                }
            }
            else {
                return false;
            }

        }

        selectUnselectPeriodFilterList(id: any, key: any, filterId: any, val) {

            if ($("#" + id).hasClass('item-selected')) {
                $("#" + id).removeClass('item-selected');
                for (let i = 0; i < this.filteredPeriodValues[filterId].length; i++) {
                    if (this.filteredPeriodValues[filterId][i] == key) {
                        this.filteredPeriodValues[filterId].splice(i, 1);
                        if (this.filteredPeriodValues[filterId].length > 0) { this.clearPeriodFilterShow[val] = true; } else { this.clearPeriodFilterShow[val] = false; }
                    }
                }
            } else {
                $("#" + id).addClass('item-selected');
                this.filteredPeriodValues[filterId].push(key);
                for (let i = 0; i < this.filteredPeriodValues[filterId].length; i++) {
                    if (this.filteredPeriodValues[filterId].length > 0) { this.clearPeriodFilterShow[val] = true; } else { this.clearPeriodFilterShow[val] = false; }
                }
            }

            this.showApplyPeriodFilter = false;
            for (let i = 0; i < this.filterPeriodDataResponse.length; i++) {
                if (this.filteredPeriodValues[this.filterPeriodDataResponse[i].filterId].length > 0) {
                    this.showApplyPeriodFilter = true;
                }
            }
        }
        clearPerPeriodFilter(filterId, val) {
            this.filteredPeriodValues[filterId] = [];
            this.clearPeriodFilterShow[val] = false;
            this.showApplyPeriodFilter = false;
            this.loadAllPeriodFilterItems[val] = false;
            $("#selectAllPeriodChk_" + filterId).prop('checked', false);
            $("#periodListFilterData" + val).find('li').removeClass('item-selected');
            for (let i = 0; i < this.filterPeriodDataResponse.length; i++) {
                if (this.filteredPeriodValues[this.filterPeriodDataResponse[i].filterId].length > 0) {
                    this.showApplyPeriodFilter = true;
                }
            }
            this.getPeriodGridData();
        }
        clearAllPeriodFilter() {
            this.showApplyPeriodFilter = false;
            $(".selectAllPeriodChk").prop('checked', false);
            for (let i = 0; i < this.filterPeriodDataResponse.length; i++) {
                this.filteredPeriodValues[this.filterPeriodDataResponse[i].filterId] = [];
                this.clearPeriodFilterShow[i] = false;
                $("#periodListFilterData" + i).find('li').removeClass('item-selected');
            }
            this.getPeriodGridData();
        }
        selectAllPeriodFilter(filterId, val) {

            let checked = $("#selectAllPeriodChk_" + filterId).is(":checked") ? true : false;
            if (checked) {
                if (this.loadAllPeriodFilterItems[val] == false) {
                    this.showMorePeriodFilterData('periodListFilterData' + val, 'periodListCountData' + val, val, this.filterPeriodDataResponse[val].filterDropdown.length, filterId, this.filterPeriodDataResponse[val].filterDropdown.length, true);
                } else {
                    this.filteredPeriodValues[filterId] = [];
                    this.clearPeriodFilterShow[val] = true;
                    this.showApplyPeriodFilter = true;
                    $("#periodListFilterData" + val).find('li').addClass('item-selected');
                    for (let i = 0; i < this.filterPeriodDataResponse[val].filterDropdown.length; i++) {
                        this.filteredValues[filterId].push(this.filterPeriodDataResponse[val].filterDropdown[i].Key);
                    }
                }
            } else {
                this.filteredPeriodValues[filterId] = [];
                this.clearPeriodFilterShow[val] = false;
                this.showApplyPeriodFilter = false;
                $("#periodListFilterData" + val).find('li').removeClass('item-selected');
                for (let i = 0; i < this.filterPeriodDataResponse.length; i++) {
                    if (this.filteredPeriodValues[this.filterPeriodDataResponse[i].filterId].length > 0) {
                        this.showApplyPeriodFilter = true;
                    }
                }
            }
        }
        expandPeriodFilter(param: boolean) {
            this.expandPeriodFilterSection = param;
            setTimeout(() => {
                        $("#periodGrid").data("kendoTreeList").resize();                     
            }, 50);
        }

        exportPeriodTreelistToExcel() {

            const gridId = "#periodGrid";
            EVENTS.loaderON(gridId);
            const getURL = "../Content/DownloadExcel";

            const finalJSON = JSON.stringify(this.excelExportPeriodData);
            this.$http.post("../AccountStatementReport/ExportTreeToExcel", finalJSON).then(response => {
                const responseData: any = response.data;
                window.location.href = getURL + "?fName=" + responseData.fName;
                EVENTS.loaderOFF(gridId);
            }, error => {
                EVENTS.loaderOFF(gridId);
                    ERROR.displayException(error.data, error.status, error.data, "exportPeriodTreelistToExcel");
            });
        }
        loadSynapseAbsenseGraph() {

            $("#absenceBarGraph1,#absenceBarGraph2,#absenceBarGraph3,#absencePieGraph1").html('');
         
            EVENTS.loaderON("#synapseAbsenceView");
            let obj = {
                monthYear: $("#synapseBudgetYearIdDropdown").data("kendoDropDownList").value(),
                orgLevel: this.orgMenuLevel,
                orgId: this.orgMenuId,
                orgName: this.orgName,
                level1OrgId: this.orgIdLevel1,
                level2OrgId: this.orgIdLevel2,
                level3OrgId: this.orgIdLevel3,
                level4OrgId: this.orgIdLevel4,
                level5OrgId: this.orgIdLevel5,
                level6OrgId: this.orgIdLevel6,
                level7OrgId: this.orgIdLevel7,
                level8OrgId: this.orgIdLevel8,
                serviceId: ""
            };
            let finalJSON = JSON.stringify(obj);
            this.$http.post<any>("../api/AccountStmtReportApiController/AbsenceDataGraph", finalJSON).then(response => {
                EVENTS.loaderOFF("#synapseAbsenceView");
                this.absenceDataResponseGraph1 = response.data.JsonDataByMonthYears;
                this.absenceDataResponseGraph2 = response.data.JsonDataByAgeGroup;
                this.absenceDataResponseGraph3 = response.data.JsonDataByOrgUnit;
                this.absenceDataResponseGraph4 = response.data.JsonDataByTotalByAge;
                this.getAbsenceBarGraph1();
                this.getAbsenceBarGraph2();
                this.getAbsenceBarGraph3();
                this.getAbsencePieGraph1();
            }, error => {
                EVENTS.loaderOFF("#synapseAbsenceView");
                error.methodName = "Account statement synapse - getAbsenceBarGraph1";
                ERROR.displayException(error.data, error.status, error.data, error.methodName);
            });


            
        }
        getAbsenceBarGraph1 = function () {

                var response = this.absenceDataResponseGraph1;             
                var cleanString = DOMPurify.sanitize(response.header.title, domPurifyConfig);
                $("#absenceBarGraph1Title").html(cleanString);
                $('#absenceBarGraph1TitleDesc img').click(function (e) {
                    MODULES.informationTooltipClick('#absenceBarGraph1TitleDesc', response.header.tooltipdesc);
                });
                var chartObj = response.chart;
                chartObj.legend.item = {};
                chartObj.legend["item"]["visual"] = function (e) {
                    let color = e.options.markers.background;
                    let labelColor = e.options.labels.color;
                    let rect = new kendo.geometry.Rect([0, 0], [120, 50]);
                    let layout = new kendo.drawing.Layout(rect, {
                        spacing: 10,
                        alignItems: "start",
                        wrap: false
                    });

                    let marker = new kendo.drawing.Path({
                        fill: {
                            color: color
                        },
                        stroke: {
                            color: color
                        },
                        cursor: "pointer"
                    }).moveTo(12, 0).lineTo(12, 12).lineTo(0, 12).lineTo(0, 0).close();

                    let geom = kendo.geometry;
                    let label = new kendo.drawing.Text(e.series.name, new geom.Point(0, 0), {
                        fill: {
                            color: labelColor
                        },
                        font: "12px sans-serif",
                        cursor: "pointer"
                    });

                    layout.append(marker, label);
                    layout.reflow();

                    return layout;
                }

                setTimeout(function () {
                    $("#absenceBarGraph1").kendoChart(chartObj);
                }, 100);           
        }

        getAbsenceBarGraph2 = function () {         

                var response = this.absenceDataResponseGraph2;               
                var cleanString = DOMPurify.sanitize(response.header.title, domPurifyConfig);
                $("#absenceBarGraph2Title").html(cleanString);
                $('#absenceBarGraph2TitleDesc img').click(function (e) {
                    MODULES.informationTooltipClick('#absenceBarGraph2TitleDesc', response.header.tooltipdesc);
                });
                setTimeout(function () {
                    $("#absenceBarGraph2").kendoChart(response.chart);
                }, 100);

        }

        getAbsenceBarGraph3 = function () {
            var response = this.absenceDataResponseGraph3;
                var cleanString = DOMPurify.sanitize(response.header.title, domPurifyConfig);
                $("#absenceBarGraph3Title").html(cleanString);
                var cleanString = DOMPurify.sanitize(response.header.overallabs, domPurifyConfig);
                $("#totalAbsByOrgUnit").html(cleanString);
                $('#absenceBarGraph3TitleDesc img').click(function (e) {
                    MODULES.informationTooltipClick('#absenceBarGraph3TitleDesc', response.header.tooltipdesc);
                });
                setTimeout(function () {
                    $("#absenceBarGraph3").kendoChart(response.chart);
                }, 100);          
        }

        getAbsencePieGraph1 = function () {
                var response = this.absenceDataResponseGraph4;
                var cleanString = DOMPurify.sanitize(response.header.title, domPurifyConfig);
                $("#absencePieGraph1Title").html(cleanString);
                $('#absencePieGraph1TitleDesc img').click(function (e) {
                    MODULES.informationTooltipClick('#absencePieGraph1TitleDesc', response.header.tooltipdesc);
                });
                setTimeout(function () {
                    $("#absencePieGraph1").kendoChart(response.chart);
                }, 100);
        }
    }
    angular.module("synapseApp", ["kendo.directives"]).config(['$httpProvider', function ($httpProvider) {
        $httpProvider.interceptors.push(() => {
            return {
                'request': function (config) {
                    return MODULES.validateHttpRequest(config);
                }
            };
        });
    }])
        .controller("synapseController", ['$scope', '$http', '$q', '$location', '$timeout', '$compile', function ($scope, $http, $q, $location, $timeout, $compile) {
            var bannerBgHeight = $('.accounting-overview-bg').height();
            MODULES.bannerHideShow(bannerBgHeight, '#collapseBannerSection', '.accounting-overview-bg', true, "RASBannerHide");       
            return new synapse($scope, $http, $q, $location, $timeout, $compile);
        }]);
}
