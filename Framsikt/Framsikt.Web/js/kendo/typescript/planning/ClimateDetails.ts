declare var EVENTS: any;
declare var MODULES: any;
declare var app: any;
declare var UTILS: any;
module ClimateDetails {
    interface IClimateDetailsScope extends ng.IScope {
        vm: ClimateDetails;
        tagsOptions: kendo.ui.MultiSelectOptions;
        climateGoalOptions: kendo.ui.DropDownListOptions;
        climateTargetOptions: kendo.ui.DropDownListOptions;
        climateStategyOptions: kendo.ui.DropDownListOptions;
        climateCategoryOptions: kendo.ui.DropDownListOptions;
        climateSecurityEstimatesOptions: kendo.ui.DropDownListOptions;
        climateFeasibilityOptions: kendo.ui.DropDownListOptions;
        startYearOptions: kendo.ui.DatePickerOptions;
        endYearOptions: kendo.ui.DatePickerOptions;
        longTermEconomicImpactOptions: kendo.ui.NumericTextBoxOptions;
        planClimateReductionTooltipOptions: kendo.ui.TooltipOptions;
        reductionGridOptions: kendo.ui.GridOptions;
        externalYearSortingOptions1: kendo.ui.NumericTextBoxOptions;
    }

    type CommonColumnSelectorObject = {
        columnid: string;
        name: string;
        checkd: boolean;
        columnorder: number;
    }
    type ClimateActionDescriptionHelper = {
        descriptionType: number;
        historyId: string;
        description: string;
    }
    // function to return url parameter values
    export const getUrlParameter = (param: string, dummyPath?: string): string => {
        let sPageURL = dummyPath || window.location.search.substring(1),
            sURLVariables = sPageURL.split(/[&||?]/),
            res;

        for (let i = 0; i < sURLVariables.length; i += 1) {
            let paramName = sURLVariables[i],
                sParameterName = (paramName || '').split('=');

            if (sParameterName[0] === param) {
                res = sParameterName[1];
            }
        }
        return res;
    };

    export interface IStorageItem {
        key: string;
        value: any;
    }

    export class StorageItem {
        key: string;
        value: any;

        constructor(data: IStorageItem) {
            this.key = data.key;
            this.value = data.value;
        }
    }

    export class LocalStorageWorker {
        localStorageSupported: boolean;

        constructor() {
            this.localStorageSupported = typeof window['localStorage'] != "undefined" && window['localStorage'] != null;
        }

        // add value to storage
        add(key: string, item: string) {
            if (this.localStorageSupported) {
                localStorage.setItem(key, item);
            }
        }

        // get all values from storage (all items)
        getAllItems(): Array<StorageItem> {
            var list = new Array<StorageItem>();

            for (var i = 0; i < localStorage.length; i++) {
                var key = localStorage.key(i);
                var value = localStorage.getItem(key);

                list.push(new StorageItem({
                    key: key,
                    value: value
                }));
            }
            return list;
        }

        // get only all values from localStorage
        getAllValues(): Array<any> {
            var list = new Array<any>();
            for (var i = 0; i < localStorage.length; i++) {
                var key = localStorage.key(i);
                var value = localStorage.getItem(key);

                list.push(value);
            }
            return list;
        }

        // get one item by key from storage
        get(key: string): string {
            if (this.localStorageSupported) {
                var item = localStorage.getItem(key);
                return item;
            } else {
                return null;
            }
        }

        // remove value from storage
        remove(key: string) {
            if (this.localStorageSupported) {
                localStorage.removeItem(key);
            }
        }

        // clear storage (remove all items from it)
        clear() {
            if (this.localStorageSupported) {
                localStorage.clear();
            }
        }
    };

    class ClimateDetails {
        vm: ClimateDetails;
        climateActionId = "0";
        planId = "-1";
        nodeId = "";
        planName = "";
        planType = "";
        climateActionName = "";
        pageChanges = false;
        dynamicYearColumnChanges = false;
        backToMainConfirmWindow: kendo.ui.Window;
        deleteInvestmentConfirmWindow: kendo.ui.Window;
        reductionGridDeleteConfirmWindow: kendo.ui.Window;
        selectDefaultDropDownKey = '--' + localStorage.getItem("selectPlaceholder") + '--';
        emptyGuid = "00000000-0000-0000-0000-000000000000";
        referenceUrl = "";
        longTermEconomicImpact = "";
        climateReductionColumns: CommonColumnSelectorObject[];
        descDataObj: ClimateActionDescriptionHelper[] = [];
        ClimateActionReductionBaseData: any;
        ClimateActionReductionBaseDatas: any;

        // Input widgets
        startYear = "";
        climateCategoryModel = "";
        climateGoalModel = "";
        climateTargetModel = "";
        climateStrategyModel = "";
        climateSecurityEstimatesModel = "";
        climateFeasibilityModel = "";
        endYear = "";
        climateTagListModel = [];
        internalDescriptionText = '';
        externalDescriptionId = '';
        financeDescriptionText = '';
        financeDescriptionId = '';
        externalDescriptionText = '';
        internalDescriptionId = '';
        editorWebSocketUrl = '';
        departmentDataSource = [];
        functionDataSource = [];
        projectDataSource = [];
        freeDim1DataSource = [];
        freeDim2DataSource = [];
        freeDim3DataSource = [];
        freeDim4DataSource = [];
        sectorDataSource = [];
        sourceDataSource = [];
        emissionTypeDataSource = [];
        climateReductionQuantity = [];
        actionTagDataSource = [];

        changeExternalYear1 = 0;
        changeExternalYear2 = 0;
        changeExternalYear3 = 0;
        changeExternalYear4 = 0;
        changeExternalYear5 = 0;
        isClimateActionPlanApproved = false;

        localStorageService = new LocalStorageWorker();
        constructor(private $scope: IClimateDetailsScope, private $http: ng.IHttpService, public $promise: ng.IPromise<any>, public $location: ng.ILocationService, public $timeout: ng.ITimeoutService, public $sce: ng.ISCEService, public $compile: ng.ICompileService) {
            this.vm = this;
        }

        defineWidgets() {
            // Define input widgets
            this.$scope.tagsOptions = {
                dataTextField: "value",
                dataValueField: "key",
                valuePrimitive: true,
                autoBind: false,
                filter: "contains",
                dataSource: [],
                value: [],
                noDataTemplate: $("#noDataClimateTags").html(),
                change: (ev: kendo.ui.MultiSelectChangeEvent) => {
                    this.pageChanges = true;
                }
            };

            this.$scope.climateGoalOptions = {
                dataTextField: "value",
                dataValueField: "key",
                valuePrimitive: true,
                autoBind: false,
                filter: "contains",
                select: function (e) {
                    if (e.dataItem.isActive == false) {
                        e.preventDefault();
                    }
                },
                template: kendo.template($("#planDropdownTemplate").html()),
                optionLabel:
                {
                    value: $('#PIDetailSelectGoal').text(),
                    key: ""
                },
                dataSource: [],
                change: (ev) => {
                    this.pageChanges = true;
                    this.getDataBasedOnGoal(true);
                }
            };

            this.$scope.climateTargetOptions = {
                dataTextField: "value",
                dataValueField: "key",
                valuePrimitive: true,
                autoBind: false,
                filter: "contains",
                select: function (e) {
                    if (e.dataItem.isActive == false) {
                        e.preventDefault();
                    }
                },
                template: kendo.template($("#planDropdownTemplate").html()),
                dataSource: [],
                optionLabel:
                {
                    value: $('#PIDetailSelectTarget').text(),
                    key: ""
                },
                change: (ev) => {
                    this.pageChanges = true;
                    if ($("#climateTargetId").data("kendoDropDownList").value() == "") {
                        $("#climateGoalId").data("kendoDropDownList").value("");
                    }
                    this.getDataBasedOnGoal(true);
                }
            };

            this.$scope.climateSecurityEstimatesOptions = {
                dataTextField: "Value",
                dataValueField: "Key",
                valuePrimitive: true,
                autoBind: false,
                filter: "contains",
                dataSource: [],
                change: (ev) => {
                    this.pageChanges = true;
                }
            };

            this.$scope.climateFeasibilityOptions = {
                dataTextField: "Value",
                dataValueField: "Key",
                valuePrimitive: true,
                autoBind: false,
                filter: "contains",
                dataSource: [],
                change: (ev) => {
                    this.pageChanges = true;
                }
            };

            this.$scope.climateStategyOptions = {
                dataTextField: "value",
                dataValueField: "key",
                valuePrimitive: true,
                autoBind: false,
                filter: "contains",
                select: function (e) {
                    if (e.dataItem.isActive == false) {
                        e.preventDefault();
                    }
                },
                template: kendo.template($("#planDropdownTemplate").html()),
                dataSource: [],
                optionLabel:
                {
                    value: $('#PIDetailSelectStrategy').text(),
                    key: ""
                },
                change: (ev) => {
                    this.pageChanges = true;
                }
            };

            this.$scope.climateCategoryOptions = {
                dataTextField: "value",
                dataValueField: "key",
                valuePrimitive: true,
                autoBind: false,
                filter: "contains",
                dataSource: [],
                change: (ev) => {
                    this.pageChanges = true;
                }
            };

            this.$scope.startYearOptions = {
                start: "decade",
                depth: "decade",
                format: "yyyy",
                change: (e: kendo.ui.DatePickerChangeEvent) => {
                    let startYear = e.sender.value().getFullYear();
                    this.pageChanges = true;
                    this.dynamicYearColumnChanges = true;
                    if (this.endYear == "") {
                        this.endYear = (startYear + 3).toString();
                    }
                    this.$timeout(() => {
                        this.validateActionYears();
                    }, 200);
                }
            }

            this.$scope.endYearOptions = {
                start: "decade",
                depth: "decade",
                format: "yyyy",
                change: (e: kendo.ui.DatePickerChangeEvent) => {
                    const endYear = e.sender.value().getFullYear();
                    this.pageChanges = true;
                    this.dynamicYearColumnChanges = true;
                    this.validateActionYears();
                }
            }

            this.$scope.longTermEconomicImpactOptions = {
                min: -9223372036854775808,
                format: "n0",
                value: 0,
                spinners: false,
                change: (ev: kendo.ui.NumericTextBoxChangeEvent) => { //auto fill functionality
                    this.longTermEconomicImpact = this.longTermEconomicImpact;
                    this.pageChanges = true;
                }
            }

            const loaderId = "#climateDetailSection";
            EVENTS.loaderOFF(loaderId);
        }

        validateActionYears(savePopupFlag?: boolean) {
            const newDate = new Date();
            const currentYear = newDate.getFullYear();
            const errorMessage: string[] = [];
            if (this.startYear !== "" && this.endYear !== "") {
                const startYear = parseInt(this.startYear);
                const endYear = parseInt(this.endYear);
                if (startYear > endYear) {
                    errorMessage.push($("#PMActionYearValidation1").text());
                    MODULES.errorConfirmation(errorMessage);
                    this.endYear = "";
                    return false;
                } else if ((endYear - startYear) > 11) {
                    errorMessage.push($("#PMActionYearValidation2").text());
                    MODULES.errorConfirmation(errorMessage);
                    return false;
                } else if (startYear > (currentYear + 9)) {
                    errorMessage.push($("#PMActionYearValidation3").text());
                    MODULES.errorConfirmation(errorMessage);
                    this.startYear = "";
                    return false;
                }
                else {
                    if (savePopupFlag == true) {
                        return true;
                    } else {
                        // reload grid with selected years
                        this.climateReductionColumnSelector(true);
                        this.planClimateActionFinanceData();
                    }
                }
            } else {
                if (this.startYear == "" || this.endYear == "") {
                    errorMessage.push($("#PMActionYearEmptyValidation").text());
                    MODULES.errorConfirmation(errorMessage);
                    return false;
                }
            }
        }

        climateReductionColumnSelector(onChangeYearValue: boolean) {
            // to keep the updated planId & nodeId
            EVENTS.loaderON("#planClimateReductionColSelector");
            this.planId = (this.planId !== '' && this.planId !== "-1") ? this.planId : this.$scope.vm.planId;
            this.nodeId = this.$scope.vm.nodeId;
            this.climateReductionColSelectorClick('#planClimateReductionColumnSelector', ' ');
            this.$timeout(() => {
                $("#planClimateReductionColumnSelector").data("kendoTooltip").refresh();
            }, 100);

            let climateReductionColumns = this.localStorageService.get("pwclimateReductionColumnSelector_" + this.climateActionId);
            $("#climateReductionColumnSelectorContent").text('');
            if (!onChangeYearValue && (climateReductionColumns != null && climateReductionColumns !== "") && this.climateActionId != '0') {
                this.climateReductionColumns = JSON.parse(climateReductionColumns);
                this.setClimateReductionColSelectorHTML(climateReductionColumns, true);
                this.getReductionGridData();
                EVENTS.loaderOFF("#planClimateReductionColSelector");
            }
            else {
                const columnSelObject = {
                    "planId": this.planId,
                    "nodeId": this.nodeId,
                    "climateActionId": this.climateActionId,
                    "startYear": this.startYear,
                    "endYear": this.endYear,
                }
                this.$http.post<CommonColumnSelectorObject[]>("../api/PlanClimateActionApi/GetCAReductionGridColumnSelector", angular.toJson(columnSelObject)).then(response => {
                    this.climateReductionColumns = response.data;
                    this.setClimateReductionColSelectorHTML(response.data, false);
                    if (this.climateActionId != "0") {
                        this.localStorageService.add("pwclimateReductionColumnSelector_" + this.climateActionId, JSON.stringify(this.climateReductionColumns)); //basically to add value
                    }
                    this.getReductionGridData();
                    EVENTS.loaderOFF("#planClimateReductionColSelector");
                }, error => {
                    EVENTS.loaderOFF("#planClimateReductionColSelector");
                    ERROR.displayException(error.data, error.status, error.data, 'GetCAReductionGridColumnSelector');
                });
            }
        }

        setClimateReductionColSelectorHTML(response, parseSts: boolean) {
            let items = [];
            let htmlColmnContent = '';
            let checkColsBoxHtml = '';
            items = parseSts == true ? JSON.parse(response) : response;
            items.forEach(function (item, index) {
                checkColsBoxHtml = '<span class="col-md-1 padding-left10 padding-bottom5"><input type="checkbox" ng-model="vm.climateReductionColumns[' + index + '][\'checkd\']" /> </span>'
                htmlColmnContent += '<span class="col-md-12 padding-top10">' +
                    checkColsBoxHtml +
                    '<span class="col-md-8 padding0 padding-bottom5 font14">' + item.name + '</span>' +
                    '<br/>' +
                    '</span>';
            });

            const finalclimateActionColHTML = '<div class="col-md-12 padding0 mr-tooltip-style padding-top10" id="climateReductionColumnSelectorDiv">' + htmlColmnContent + '</div>' +
                '<div class="col-md-12 top5">' +
                '<span class="pull-right" style = "margin-right:25px;">' +
                '</span>' +
                '</div>' +
                '<div class="col-md-12 top2 padding0">' +
                '<button class="btn btn-primary padding5 align-center width95-percent bottom5" ng-click="vm.saveclimateReductionColumnsStsColSelectorUser();">' + localStorage.getItem("PM_goals_target_save_tenant_level") + '</button>' +
                '</div>' +
                '<div class="col-md-12 btn top2 padding0 padding-bottom5">' +
                '<button class="btn padding5 align-center width95-percent bottom5" ng-click="vm.saveclimateReductionStsColSeletor();">' + localStorage.getItem("PM_goals_target_save_session_level") + '</button>' +
                '</div>';

            let columnsContent = this.$compile(finalclimateActionColHTML)(this.$scope);

            $("#climateReductionColumnSelectorContent").text("").append(columnsContent);
            this.$timeout(() => {
                $("#climateReductionColumnSelectorDiv").css("max-height", $(window).height() - 440);
                $("#climateReductionColumnSelectorDiv").css({ "overflow-y": "auto", "overflow-x": "hidden" });
            }, 100);
        }

        saveclimateReductionStsColSeletor() {
            $('.k-animation-container').hide();
            if (this.climateActionId != "0") {
                this.localStorageService.add("pwclimateReductionColumnSelector_" + this.climateActionId, JSON.stringify(this.climateReductionColumns));
            }
            this.getReductionGridData();
        }
        saveclimateReductionColumnsStsColSelectorUser() {
            $('.k-animation-container').hide();
            if (this.climateActionId != "0") {
                this.localStorageService.add("pwclimateReductionColumnSelector_" + this.climateActionId, "");
            }
            let jsonObj = {
                "climateActionColumns": this.climateReductionColumns,
                "planId": this.planId,
                "nodeId": this.nodeId,
                "id": this.climateActionId,
            }
            this.$http.post("../api/PlanClimateActionApi/SavePlanClimateReductionGridColumnSelector", angular.toJson(jsonObj)).then(response => {
                this.getReductionGridData();
            }, error => {
                error.methodName = "saveclimateReductionColumnsStsColSelectorUser";
                ERROR.displayException(error.data, error.status, error.data, error.methodName);
            });
        }

        climateReductionColSelectorClick(tooltipID: string, desc: string) {
            this.$scope.planClimateReductionTooltipOptions = {};
            this.$scope.planClimateReductionTooltipOptions = {
                content: function (e) {
                    $("#planClimateReductionColSelector").css("display", "block");
                    const target = $("#planClimateReductionColSelector"); // the element for which the tooltip is shown
                    return target; // set the element text as content of the tooltip
                },
                width: 300,
                showOn: "click",
                show: function (e) {
                    this.popup.element.addClass("base-tooltip-popup grid-box-shadow border1");
                    $("div[role='tooltip']").find('.k-i-close').css('visibility', 'hidden');
                    $('.k-tooltip-content').css('padding-right', '0px');  // added to avoid custom padding added in custom css
                    $(tooltipID + '_tt_active').find('.k-callout-s').css('border-top-color', '#fff');
                    $(tooltipID + '_tt_active').find('.k-callout-w').css({ 'border-right-color': '#fff' });
                    $('.k-callout-n').css('border-bottom-color', '#fff');
                    $(tooltipID + '_tt_active').css({ 'margin-left': '22%' });
                    $(tooltipID + '_tt_active').find('.k-callout-e').css({ 'border-left-color': '#fff' });
                },
                autoHide: false,
                position: "bottom"
            }
        }

        getDataBasedOnGoal(isGoalChange?: boolean) {
            const loaderId = "#collapseClimateHeader";
            let goalId = $("#climateGoalId").data("kendoDropDownList").value();
            if (isGoalChange == false && goalId == "")
                return;
            EVENTS.loaderON(loaderId);
            if (goalId == "")
                goalId = this.emptyGuid;
            let targetId = $("#climateTargetId").data("kendoDropDownList").value();
            if (targetId == "")
                targetId = this.emptyGuid;
            this.$http.get<any>("../api/PlanClimateActionApi/GetDetailsConnectedToGoals?goalList=" + goalId + "&targetList=" + targetId).then(response => {
                const responseData = response.data;
                $("#climateGoalId").data("kendoDropDownList").value(responseData.goalId);
                $("#climateTargetId").data("kendoDropDownList").setDataSource(new kendo.data.DataSource({
                    data: responseData.masterTargets
                }));
                $("#climateStrategyId").data("kendoDropDownList").setDataSource(new kendo.data.DataSource({
                    data: responseData.masterStrategies
                }));
                if (targetId != this.emptyGuid) {
                    $("#climateTargetId").data("kendoDropDownList").value(targetId);
                } 
                $("#climateFocusArea").html(responseData.focusArea);
                if (responseData.IsGoalTargetParam && targetId != "00000000-0000-0000-0000-000000000000") {
                    $("#climateUnGoals").html(responseData.UNGoalTargets);
                } else {
                    $("#climateUnGoals").html(responseData.UNGoals);
                }
                if (responseData.focusArea == "") {
                    $("#climateFocusAreaSection").hide();
                }
                else {
                    $("#climateFocusAreaSection").show();
                }
                if (responseData.IsGoalTargetParam) {
                    if (responseData.UNGoals == "" && responseData.UNGoalTargets == "") {
                        $("#climateUnGoalsSection").hide();
                    }
                    else {
                        $("#climateUnGoalsSection").show();
                    }
                    $("#climateUnTargetsSection").hide();
                } else {
                    if (responseData.UNGoals == "" || goalId == "00000000-0000-0000-0000-000000000000") {
                        $("#climateUnGoalsSection").hide();
                        $("#climateUnTargetsSection").hide();
                    }
                    else if (responseData.UNGoals != "" && responseData.UNTargets == "") {
                        $("#climateUnGoalsSection").show();
                        $("#climateUnTargetsSection").hide();
                    }
                    else {
                        $("#climateUnGoalsSection").show();
                        $("#climateUnTargetsSection").show();
                    }
                    $("#climateUnTargets").html(responseData.UNTargets);
                    }
                EVENTS.loaderOFF(loaderId);
            }, error => {
                error.methodName = "GetDetailsConnectedToGoals";
                ERROR.displayException(error.data, error.status, error.data, error.methodName);
                EVENTS.loaderOFF(loaderId);
            });
        }

        getDropdownServices() {
            const loaderId = "#climateDetailSection";
            EVENTS.loaderON(loaderId);
            //Category Dropdown
            this.$http.get<any>("../api/ClimateActionApi/GetCategoriesBasedOnType?type=CLIMATE_ACTION").then(response => {
                const responseData = response.data;
                $("#climateCategoriesId").data("kendoDropDownList").setDataSource(new kendo.data.DataSource({
                    data: responseData
                }));
            }, error => {
                error.methodName = "GetCategoriesBasedOnType";
                EVENTS.loaderOFF(loaderId);
                ERROR.displayException(error.data, error.status, error.data, error.methodName);
            });
            //Security Estimates Dropdown
            this.$http.get<any>("../api/ClimateActionApi/GetSecurityEstimatesListByType?type=CLIMATE_ACTION").then(response => {
                const responseData = response.data;
                $("#climateSecurityEstimatesId").data("kendoDropDownList").setDataSource(new kendo.data.DataSource({
                    data: responseData
                }));
            }, error => {
                error.methodName = "GetSecurityEstimatesListByType";
                EVENTS.loaderOFF(loaderId);
                ERROR.displayException(error.data, error.status, error.data, error.methodName);
            });
            //Feasibility Dropdown
            this.$http.get<any>("../api/ClimateActionApi/GetFeasibilityListByType?type=CLIMATE_ACTION").then(response => {
                const responseData = response.data;
                $("#climateFeasibilityId").data("kendoDropDownList").setDataSource(new kendo.data.DataSource({
                    data: responseData
                }));
            }, error => {
                error.methodName = "GetFeasibilityListByType";
                EVENTS.loaderOFF(loaderId);
                ERROR.displayException(error.data, error.status, error.data, error.methodName);
            });
            //Strategy Dropdown
            this.$http.get<any>("../api/PlanClimateActionApi/GetPlanStrategyList").then(response => {
                const responseData = response.data;
                $("#climateStrategyId").data("kendoDropDownList").setDataSource(new kendo.data.DataSource({
                    data: responseData
                }));
            }, error => {
                error.methodName = "GetPlanStrategyList";
                EVENTS.loaderOFF(loaderId);
                ERROR.displayException(error.data, error.status, error.data, error.methodName);
            });

            //Goal Target Dropdown
            this.$http.get<any>("../api/PlanClimateActionApi/GetBaseDataforClimateActionCreation").then(response => {
                const responseData = response.data;
                //Goal & target Dropdowns
                $("#climateGoalId").data("kendoDropDownList").setDataSource(new kendo.data.DataSource({
                    data: responseData.masterGoals
                }));
                $("#climateTargetId").data("kendoDropDownList").setDataSource(new kendo.data.DataSource({
                    data: responseData.masterTargets
                }));

                //Tag Dropdown
                $("#climateDetailTagList").data("kendoMultiSelect").setDataSource(new kendo.data.DataSource({
                    data: responseData.masterTags
                }));

                // handling keydown events for tag widget
                $('#climateDetailTagList').data().kendoMultiSelect.input.on('keydown', (e) => {
                    const multiSelect = $("#climateDetailTagList").data("kendoMultiSelect");
                    if (e.keyCode == 13 || e.keyCode == 9) { //on enter key press or tab key press
                        const input = multiSelect.input;
                        const inputValue = input.val() as string;
                        this.addNewClimateActionTags(multiSelect.element[0].id, inputValue);
                    }
                });
                this.getClimateActionDetailData();
            }, error => {
                EVENTS.loaderOFF(loaderId);
                error.methodName = "getDropdownValuesFromGlobalClimateTables";
                ERROR.displayException(error.data, error.status, error.data, error.methodName);
            });
            EVENTS.loaderOFF(loaderId);
        }

        getBaseGridDataSource() {
            this.$http.get<any>("../api/PlanClimateActionApi/GetClimateActionDataSource").then(response => {
                const responseData: any = response.data;
                this.departmentDataSource = responseData.departments;
                this.functionDataSource = responseData.functions;
                this.projectDataSource = responseData.projects;
                this.freeDim1DataSource = responseData.freedim1;
                this.freeDim2DataSource = responseData.freedim2;
                this.freeDim3DataSource = responseData.freedim3;
                this.freeDim4DataSource = responseData.freedim4;
                this.sectorDataSource = responseData.sector;
                this.sourceDataSource = responseData.source;
                this.emissionTypeDataSource = responseData.emissionTypes;
                this.actionTagDataSource = responseData.actionTags;
                this.climateReductionQuantity = responseData.climateReductionQuantity;
            }, error => {
                error.methodName = "Base Grid Dropdown DataSource";
                ERROR.displayException(error.data, error.status, error.data, error.methodName);
            });
        }
        getReductionGridData() {
            let loaderId = "#reductionGrid";
            EVENTS.loaderON(loaderId);
            this.$scope.reductionGridOptions = {
                dataSource: {
                    data: [],
                    schema: {
                        model: {
                            fields: {
                                departmentData: {
                                    defaultValue: {
                                        key: this.selectDefaultDropDownKey, value: ""
                                    }
                                },
                                functionData: {
                                    defaultValue: {
                                        key: this.selectDefaultDropDownKey, value: ""
                                    }
                                },
                                projectData: {
                                    defaultValue: {
                                        key: this.selectDefaultDropDownKey, value: ""
                                    }
                                },
                                sectorData: {
                                    defaultValue: {
                                        key: this.selectDefaultDropDownKey, value: ""
                                    }
                                },
                                sourceData: {
                                    defaultValue: {
                                        key: this.selectDefaultDropDownKey, value: ""
                                    }
                                },
                                emissionTypeData: {
                                    defaultValue: {
                                        key: this.selectDefaultDropDownKey, value: ""
                                    }
                                },
                                freeDim1: {
                                    defaultValue: {
                                        key: this.selectDefaultDropDownKey, value: ""
                                    }
                                },
                                freeDim2: {
                                    defaultValue: {
                                        key: this.selectDefaultDropDownKey, value: ""
                                    }
                                },
                                freeDim3: {
                                    defaultValue: {
                                        key: this.selectDefaultDropDownKey, value: ""
                                    }
                                },
                                freeDim4: {
                                    defaultValue: {
                                        key: this.selectDefaultDropDownKey, value: ""
                                    }
                                },
                                climateReductionQuantity: {
                                    defaultValue: {
                                        key: this.selectDefaultDropDownKey, value: ""
                                    }
                                },
                                delete: {
                                    editable: false
                                }
                            }
                        }
                    },
                },
                columns: [],
                editable: {
                    confirmation: false
                },
                scrollable: true,
                navigatable: true,
                dataBound: (ev) => {
                }
            }
            const climateObject = {
                "planId": this.planId,
                "nodeId": this.nodeId,
                "climateActionId": this.climateActionId,
                "startYear": this.startYear,
                "endYear": this.endYear,
            }
            const finalObj = {
                "climateActionInput": climateObject,
                "columnSelector": this.climateReductionColumns
            }
            this.$http.post<any>("../api/PlanClimateActionApi/GetClimateActionBaseData", angular.toJson(finalObj)).then(response => {
                const responseData: any = response.data;
                this.ClimateActionReductionBaseData = responseData.data;
                this.ClimateActionReductionBaseDatas = response.data;
                if (this.climateActionId != "0") {
                    this.$http.post<any>("../api/PlanClimateActionApi/GetCAReductionGridData", angular.toJson(finalObj)).then(response => {
                        const responseData: any = response.data;
                        this.setEditableClimateActionGridOptions(responseData);
                    }, error => {
                        error.methodName = "Reduction Grid DataSource";
                        ERROR.displayException(error.data, error.status, error.data, error.methodName);
                        EVENTS.loaderOFF(loaderId);
                    });
                }
                else {
                    this.setEditableClimateActionGridOptions(this.ClimateActionReductionBaseDatas);
                }
            }, error => {
                error.methodName = "Reduction Grid DataSource";
                ERROR.displayException(error.data, error.status, error.data, error.methodName);
            });
        }
        setEditableClimateActionGridOptions(responseData: any) {
            $("#reductionGrid").data("kendoGrid").setDataSource(new kendo.data.DataSource({
                data: []
            }));
            const yearRegex = /year[0-9]{4}/; // regex check for dynamic year fields
            for (var t = 0; t < responseData.columns.length; t++) {
                const field = <string>responseData.columns[t].field;
                switch (field) {
                    case "departmentData.key":
                    case "functionData.key":
                    case "projectData.key":
                    case "sectorData.key":
                    case "sourceData.key":
                    case "emissionTypeData.key":
                    case "climateReductionQuantity.key":
                        responseData.columns[t].editor = this.gridDropdownEditor;
                        break;

                    case "freeDim1.key":
                    case "freeDim2.key":
                    case "freeDim3.key":
                    case "freeDim4.key":
                        responseData.columns[t].editor = this.freedimDropdownEditor;
                        break;
                    case "longtermreduction":
                        responseData.columns[t].editor = this.yearEditor;
                        break;
                    default:
                        // if the column field is dynamic year
                        if (yearRegex.test(field)) {
                            responseData.columns[t].editor = this.yearEditor;
                        }
                        break;
                }
            }

            $("#reductionGrid").data("kendoGrid").setOptions({
                columns: responseData.columns,
                dataBound: (evt) => {
                    $("#reductionGrid tr:last").find("a").css("color", "#000000");
                    $('#reductionGrid').find(".k-grid-content.k-auto-scrollable").css({ 'max-height': '300px', 'overflow': 'auto' });
                    $("#reductionGrid").find('table').attr("tabindex", '-1');
                },
                scrollable: true,
                navigatable: true,
                resizable: true,
                edit: (ed) => {
                    if (ed.model.uniqueId == "-1") {
                        $("#reductionGrid").data("kendoGrid").closeCell();
                    }
                }
            });

            $("#reductionGrid").data("kendoGrid").setDataSource(new kendo.data.DataSource({
                data: responseData.data,
                schema: this.reductionGridSchemaFields(),
                change: (e: kendo.data.DataSourceChangeEvent) => {
                    const gridId = "#reductionGrid";
                    const dsData = $(gridId).data("kendoGrid").dataSource.data();
                    const yearRegex = /year[0-9]{4}/;
                    // apply on year change
                    if (e.action && e.action == "itemchange") {
                        // generate year list array
                        const tempYear = [];
                        const yearsList = [];
                        for (let i = parseInt(this.startYear); i <= parseInt(this.endYear); i++) {
                            tempYear.push(i);
                        }
                        tempYear.forEach(x => {
                            if (this.climateReductionColumns.filter(z => z.columnid.includes(x.toString()) && z.checkd == true).length > 0)
                            {
                                yearsList.push(x);
                            }
                        });

                        if (yearRegex.test(e.field) && yearsList.length > 0) {
                            for (let j = 0; j < yearsList.length; j++) {
                                const yearColumn = "year" + yearsList[j];
                                if (yearColumn == e.field) {
                                    for (let i = 0; i < dsData.length - 1; i++) {
                                        if (e.items[0]["uid"] == dsData[i].uid) {
                                            if (j == 0) {
                                                for (let k = yearsList[j] + 1; k <= yearsList[yearsList.length - 1]; k++) {
                                                    if (this.climateReductionColumns.filter(z => z.columnid.includes(k.toString()) && z.checkd == true).length > 0) {
                                                        if (dsData[i]["year" + k] == null || dsData[i]["year" + k] == 0) {
                                                            dsData[i]["year" + k] = e.items[0][e.field];
                                                            $(gridId).find("tbody tr").find("#year_" + k + dsData[i].uid).html(kendo.toString(parseInt(e.items[0][e.field]), "n0"));
                                                        }
                                                    }
                                                }
                                            } else if (j > 0) {
                                                let m = yearsList[j - 1];
                                                for (let k = yearsList[j] + 1; k <= yearsList[yearsList.length - 1]; k++) {
                                                    if (this.climateReductionColumns.filter(z => z.columnid.includes(k.toString()) && z.checkd == true).length > 0) {
                                                        if (dsData[i]["year" + k] == null || dsData[i]["year" + k] == 0 || (dsData[i]["year" + k] == dsData[i]["year" + m])) {
                                                            dsData[i]["year" + k] = e.items[0][e.field];
                                                            $(gridId).find("tbody tr").find("#year_" + k + dsData[i].uid).html(kendo.toString(parseInt(e.items[0][e.field]), "n0"));
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            // total sum calculation
                            this.applyTotalSumCalculation();
                        }
                        else if (e.field == "longtermreduction")
                        {
                            this.applyRedcutionTotalSumCalculation();
                        }
                    }
                }
            }));
            EVENTS.loaderOFF("#reductionGrid");
        }
        applyRedcutionTotalSumCalculation()
        {
            const gridId = "#reductionGrid";
            const dsData = $(gridId).data("kendoGrid").dataSource.data();
            let longtermreduction = 0;
            for (let i = 0; i < dsData.length; i++) {
                if (dsData[i]['uniqueId'] != "-1") {
                    let amount = this.emissionTypeDataSource.find(x => x.key == dsData[i]["emissionTypeData"].key) != null ? this.emissionTypeDataSource.find(x => x.key == dsData[i]["emissionTypeData"].key).co2Factor : 0;
                    longtermreduction = longtermreduction + (dsData[i]["longtermreduction"] * amount);
                } else {
                    dsData[i]["longtermreduction"] = parseInt(longtermreduction.toString());
                    $(gridId).find("tbody tr").find("#longtermreduction_" + dsData[i].uid).html(kendo.toString(parseInt(longtermreduction.toString()), "n0"));
                }
            }
        }
        applyTotalSumCalculation() {
            const gridId = "#reductionGrid";
            const dsData = $(gridId).data("kendoGrid").dataSource.data();
            const yearsList = [];
            for (let i = parseInt(this.startYear); i <= parseInt(this.endYear); i++) {
                yearsList.push(i);
            }

            // total year columns calculation
            for (let j = 0; j < yearsList.length; j++) {
                let yearTotal = 0, sumTotal = 0;
                const yearColumn = "year" + yearsList[j];
                for (let i = 0; i < dsData.length; i++) {
                    if (dsData[i]['uniqueId'] != "-1") {
                        let amount = this.emissionTypeDataSource.find(x => x.key == dsData[i]["emissionTypeData"].key) != null ? this.emissionTypeDataSource.find(x => x.key == dsData[i]["emissionTypeData"].key).co2Factor : 0;
                        yearTotal = yearTotal + (dsData[i][yearColumn] * amount);
                    } else {
                        dsData[i][yearColumn] = yearTotal;
                        $(gridId).find("tbody tr").find("#year_" + yearsList[j] + dsData[i].uid).html(kendo.toString(yearTotal, "n0"));
                    }
                }
            }
            
        }
        reductionGridSchemaFields() {
            const schema = {
                model: {
                    fields: {
                        departmentData: {
                            defaultValue: {
                                key: this.selectDefaultDropDownKey, value: ""
                            }
                        },
                        functionData: {
                            defaultValue: {
                                key: this.selectDefaultDropDownKey, value: ""
                            }
                        },
                        projectData: {
                            defaultValue: {
                                key: this.selectDefaultDropDownKey, value: ""
                            }
                        },
                        sectorData: {
                            defaultValue: {
                                key: this.selectDefaultDropDownKey, value: ""
                            }
                        },
                        sourceData: {
                            defaultValue: {
                                key: this.selectDefaultDropDownKey, value: ""
                            }
                        },
                        emissionTypeData: {
                            defaultValue: {
                                key: this.selectDefaultDropDownKey, value: ""
                            }
                        },
                        freeDim1: {
                            defaultValue: {
                                key: this.selectDefaultDropDownKey, value: ""
                            }
                        },
                        freeDim2: {
                            defaultValue: {
                                key: this.selectDefaultDropDownKey, value: ""
                            }
                        },
                        freeDim3: {
                            defaultValue: {
                                key: this.selectDefaultDropDownKey, value: ""
                            }
                        },
                        freeDim4: {
                            defaultValue: {
                                key: this.selectDefaultDropDownKey, value: ""
                            }
                        },
                        delete: {
                            editable: false
                        }
                    }
                }
            };
            return schema;
        }
        addNewClimateActionTags(widgetId: string, value: string) {
            const validString = /([^\s])/.test(value); //test for empty strings and whitespaces

            if (validString == true) {
                const widget = <kendo.ui.MultiSelect>$("#" + widgetId).data("kendoMultiSelect");
                const dataSource = widget.dataSource;

                // Add new datasource item
                dataSource.add({
                    key: "0",
                    value: value
                });

                const newValue = "0";
                this.$timeout(() => {
                    widget.value(widget.value().concat([newValue]));
                    dataSource.sync();
                }, 100);
            }
        }

        getClimateActionDetailData() {
            const loaderId = "#climateDetailSection";
            EVENTS.loaderON(loaderId);
            const createActionObject = {
                "planId": this.planId,
                "nodeId": this.nodeId,
                "climateActionId": parseInt(this.climateActionId),
                "startYear": Number(this.startYear),
                "endYear": Number(this.endYear),
            }
            this.$http.post<any>("../api/PlanClimateActionApi/GetPlanClimateActionDetail", angular.toJson(createActionObject)).then(response => {
                const responseData = response.data;
                this.isClimateActionPlanApproved = responseData.isClimateActionPlanApproved;
                if (Object.entries(responseData).length > 0) {
                    if (this.climateActionId != "0") {
                        $("#climateTargetId").data("kendoDropDownList").value(responseData.targetId);
                        $("#climateGoalId").data("kendoDropDownList").value(responseData.goalId);
                        $("#climateCategoriesId").data("kendoDropDownList").value(responseData.categoryId);
                        $("#climateStrategyId").data("kendoDropDownList").value(responseData.strategyId);
                        $("#climateSecurityEstimatesId").data("kendoDropDownList").value(responseData.estimateId);
                        $("#climateFeasibilityId").data("kendoDropDownList").value(responseData.feasibilityId);
                        $("#climateDetailTagList").data("kendoMultiSelect").value(responseData.tagIds);
                        this.climateActionName = responseData.climateName;
                        this.climateActionId = responseData.climateId;
                        this.referenceUrl = responseData.referenceUrl;
                        $("#climateFocusArea").html(responseData.focusArea);
                        if (responseData.IsGoalTargetParam && responseData.UNGoalTargets != "") {
                            $("#climateUnGoals").html(responseData.UNGoalTargets);
                        } else {
                            $("#climateUnGoals").html(responseData.UNGoals);
                        }
                        this.getDataBasedOnGoal(true);
                        $("#climateTargetId").data("kendoDropDownList").value(responseData.targetId);
                        if (responseData.focusArea == "") {
                            $("#climateFocusAreaSection").hide();
                        }
                        else {
                            $("#climateFocusAreaSection").show();
                        }
                        if (responseData.UNGoals == "") {
                            $("#climateUnGoalsSection").hide();
                            $("#climateUnTargetsSection").hide();
                        }
                        else {
                            $("#climateUnGoalsSection").show();
                            $("#climateUnTargetsSection").show();
                        }
                    }
                    this.planName = responseData.planName;
                    this.startYear = responseData.startYear;
                    this.endYear = responseData.endYear;
                    this.planType = responseData.planType;
                    this.planClimateActionFinanceData();
                    this.climateReductionColumnSelector(false);
                }
                EVENTS.loaderOFF(loaderId);
            }, error => {
                error.methodName = "GetPlanClimateActionDetail";
                ERROR.displayException(error.data, error.status, error.data, error.methodName);
                EVENTS.loaderOFF(loaderId);
            });
        }

        planClimateActionFinanceData() {
            const loaderId = "#collapseClimateOkonomi";
            const endYear = (this.endYear) ? parseInt(this.endYear) : this.endYear;
            EVENTS.loaderON(loaderId);
            const actionFinanceObject = {
                "planId": this.planId,
                "nodeId": this.nodeId,
                "climateActionId": this.climateActionId,
                "startYear": this.startYear,
                "endYear": endYear
            }

            this.$http.post<any>("../api/PlanClimateActionApi/GetPlanClimateActionFinanceData", angular.toJson(actionFinanceObject)).then(response => {
                this.getEditorConfigInfo();
                const responseData = response.data;
                this.longTermEconomicImpact = responseData.longTermEconomicImpact;
                const incomeAmountData = responseData.incomeAmountData;
                const outcomeAmountData = responseData.outcomeAmountData;

                let incomeData = [];
                let outcomeData = [];

                //income section
                incomeAmountData.forEach((item, index) => {
                    let idx = index + 1;
                    incomeData.push({
                        "label": item.key,
                        "name": item.key,
                        "value": item.value,
                        "id": "incomeAmountTextBox" + idx
                    })
                });

                let incomeAmountModel = {
                    "fields": incomeData
                };

                let incomeAmountViewModel = kendo.observable(incomeAmountModel);
                kendo.bind($("#incomeAmountSection"), incomeAmountViewModel);

                //outcome section
                outcomeAmountData.forEach((item, index) => {
                    let idx1 = index + 1;
                    outcomeData.push({
                        "label": item.key,
                        "name": item.key,
                        "value": item.value,
                        "id": "outcomeAmountTextBox" + idx1
                    })
                });

                let outcomeAmountModel = {
                    "fields": outcomeData
                };

                let outcomeAmountViewModel = kendo.observable(outcomeAmountModel);
                kendo.bind($("#outcomeAmountSection"), outcomeAmountViewModel);

                let startYear = Number(this.startYear);
                let endYear = Number(this.endYear);

                const incomeAmountTextBox1 = $("#incomeAmountTextBox1").data("kendoNumericTextBox");
                incomeAmountTextBox1.bind("change", function () {
                    const incomeAmountValue = this.value();

                    let incomeAutoFillAllowed = true;
                    for (let i = 1; i < (endYear - startYear) + 1; i++) {
                        let j = i + 1;
                        if ($("#incomeAmountTextBox" + j).data("kendoNumericTextBox").value() !== 0 && $("#incomeAmountTextBox" + j).data("kendoNumericTextBox").value() !== null) {
                            incomeAutoFillAllowed = false;
                        }
                    }

                    if (incomeAutoFillAllowed) {
                        for (let k = 1; k < (endYear - startYear) + 1; k++) {
                            let l = k + 1;
                            $("#incomeAmountTextBox" + l).data("kendoNumericTextBox").setOptions({ value: incomeAmountValue });
                        }
                    }
                });

                const outcomeAmountTextBox1 = $("#outcomeAmountTextBox1").data("kendoNumericTextBox");
                outcomeAmountTextBox1.bind("change", function () {
                    const outcomeAmountValue = this.value();
                    let outcomeAutoFillAllowed = true;
                    for (let i = 1; i < (endYear - startYear) + 1; i++) {
                        let j = i + 1;
                        if ($("#outcomeAmountTextBox" + j).data("kendoNumericTextBox").value() !== 0 && $("#outcomeAmountTextBox" + j).data("kendoNumericTextBox").value() !== null) {
                            outcomeAutoFillAllowed = false;
                        }
                    }

                    if (outcomeAutoFillAllowed) {
                        for (let k = 1; k < (endYear - startYear) + 1; k++) {
                            let l = k + 1;
                            $("#outcomeAmountTextBox" + l).data("kendoNumericTextBox").setOptions({ value: outcomeAmountValue });
                        }
                    }
                });

                setTimeout(function () {
                    $(".income-outcome-num input[type=text]").on("focus", function () {
                        var input = $(this);
                        clearTimeout(input.data("selectTimeId")); //stop started time out if any

                        let selectTimeId = setTimeout(function () {
                            input.select();
                            let value = $(input[0]).val();

                            if (value == 0) {
                                $(input[0]).val('');
                            }
                        });

                        input.data("selectTimeId", selectTimeId);
                    }).blur(function (e) {
                        clearTimeout($(this).data("selectTimeId")); //stop started timeout
                    });
                }, 100)

                this.climateActionDetailEditor();
                EVENTS.loaderOFF(loaderId);
            }, error => {
                error.methodName = "planClimateActionFinanceData";
                ERROR.displayException(error.data, error.status, error.data, error.methodName);
                EVENTS.loaderOFF(loaderId);
            });
        }

        init() {
            const loaderId = "#climateDetailSection";
            EVENTS.loaderON(loaderId);
            this.planId = getUrlParameter("planId");
            this.nodeId = getUrlParameter("nodeId");
            this.climateActionId = getUrlParameter("climateId");
            this.defineWidgets();
            this.getDropdownServices();
            this.getBaseGridDataSource();
            $("#climateFocusAreaSection").hide();
            $("#climateUnGoalsSection").hide();
            $("#climateUnTargetsSection").hide();
        }

        goToMain(page = 'planworker') {
            if (this.pageChanges == true) {
                this.backToMainConfirmWindow = $("#planClimateGoBackConfirmation").kendoWindow({
                    width: "600px",
                    title: localStorage.getItem("Confirm"),
                    resizable: false,
                    modal: true
                }).data("kendoWindow").center().open() as kendo.ui.Window;

                $(".planclimate-back-confirm-yes").click(() => {
                    if (page == 'myplan') {
                        window.location.href = "../Planning/PlanView";
                    } else {
                        window.location.href = "../Planning/PlanWorker?planId=" + this.planId + "&planName=" + escape(this.planName) + "&type=&nodeId=" + this.nodeId + "&t=worker";
                    }
                    this.backToMainConfirmWindow.close();
                    return false;
                });
                $(".planclimate-back-confirm-no").click(() => {
                    this.backToMainConfirmWindow.close();
                    return false;
                });
            } else {
                if (page == 'myplan') {
                    window.location.href = "../Planning/PlanView";
                } else {
                    window.location.href = "../Planning/PlanWorker?planId=" + this.planId + "&planName=" + escape(this.planName) + "&type=&nodeId=" + this.nodeId + "&t=worker";
                }
            }
        }

        getEditorConfigInfo() {
            // collaborative editor config urls
            this.$http.get("../content/getcsurls").then(response => {
                let responseData: any = response.data;
                this.editorWebSocketUrl = responseData.webSocketUrl;
            }, error => {
                ERROR.displayException(error.data, error.status, error.data, "getEditorConfigInfo");
            });
        }

        climateActionDetailEditor() {
            let loaderId = "#collapseClimateDescription";
            EVENTS.loaderON(loaderId);
            this.$http.get("../api/PlanClimateActionApi/GetPlanClimateActionDescriptionsData?climateActionId=" + this.climateActionId + "&planId=" + this.planId).then(response => {
                let responseData: any = response.data;
                this.externalDescriptionId = responseData.descriptionData[0].historyId;
                this.internalDescriptionId = responseData.descriptionData[1].historyId;
                this.financeDescriptionId = responseData.descriptionData[2].historyId;
                let externalDescriptionId = responseData.descriptionData[0].historyId;
                let internalDescriptionId = responseData.descriptionData[1].historyId;
                let financeDescriptionId = responseData.descriptionData[2].historyId;
                let externalDescriptionText = responseData.descriptionData[0].description;
                let internalDescriptionText = responseData.descriptionData[1].description;
                let financeDescriptionText = responseData.descriptionData[2].description;
                let saveInput = {
                    descriptionType: 1
                };
                //Editor1 -> climate external description
                let getHeight = MODULES.checkEditorHeight("#externalDescEditorHiddenContent", externalDescriptionText);
                $("#externalDescriptionEditorContentTitle").addClass("cmn-display-none");
                $("#externalDescEditorContentTitle").removeClass("cmn-display-none");
                TYPESCRIPTMODULES.setCollaborativeEditorHTML(externalDescriptionText, getHeight, "#externalDescEditorHtmlContent", "#externalDescShowMoreContent");
                $("#externalDescEditor,#externalDescShowMoreContent").off("click").on("click", () => {
                    saveInput.descriptionType = 1;
                    TYPESCRIPTMODULES.getCollaborativeEditor(externalDescriptionText, this.editorWebSocketUrl, this.externalDescriptionId, 771, saveInput, 'externalDescEditorHtmlContent', 'externalDescEditorHiddenContent', true);
                });
                EVENTS.loaderOFF('#externalDescWrapper');

                //Editor2 -> climate internal description
                getHeight = MODULES.checkEditorHeight("#internalDescEditorHiddenContent", internalDescriptionText);
                $("#internalDescriptionEditorrContentTitle").addClass("cmn-display-none");
                $("#internalDescContentTitle").removeClass("cmn-display-none");
                TYPESCRIPTMODULES.setCollaborativeEditorHTML(internalDescriptionText, getHeight, "#internalDescEditorHtmlContent", "#internalDescShowMoreContent");
                $("#internalDescEditor,#internalDescShowMoreContent").off("click").on("click", () => {
                    saveInput.descriptionType = 2;
                    TYPESCRIPTMODULES.getCollaborativeEditor(internalDescriptionText, this.editorWebSocketUrl, this.internalDescriptionId, 771, saveInput, 'internalDescEditorHtmlContent', 'internalDescEditorHiddenContent', true);
                });
                EVENTS.loaderOFF('#internalDescWrapper');

                //Editor3 -> climate finance description
                getHeight = MODULES.checkEditorHeight("#financeDescEditorHiddenContent", financeDescriptionText);
                $("#financeDesctiptionEditorContentTitle").addClass("cmn-display-none");
                $("#financeDescEditorContentTitle").removeClass("cmn-display-none");
                TYPESCRIPTMODULES.setCollaborativeEditorHTML(financeDescriptionText, getHeight, "#financeDescEditorHtmlContent", "#financeDescShowMoreContent");
                $("#financeDescCollabEditor,#financeDescShowMoreContent").off("click").on("click", () => {
                    saveInput.descriptionType = 3;
                    TYPESCRIPTMODULES.getCollaborativeEditor(financeDescriptionText, this.editorWebSocketUrl, this.financeDescriptionId, 771, saveInput, 'financeDescEditorHtmlContent', 'financeDescEditorHiddenContent', true);
                });
                EVENTS.loaderOFF('#financeDescriptionWrapper');

                //Editor1 -> history & tooltip
                $('#externalDescHistory span').unbind('click').click(function (e) {
                    MODULES.historyTooltipClick('externalDescHistory', '', '', externalDescriptionId);
                });
                $('#externalDescEditorHeaderDesc img').on('click keypress', function (e) {
                    MODULES.informationTooltipClick('#externalDescEditorHeaderDesc', $("#externaldescriptionTooltip").text());
                });

                //Editor2 -> history & tooltip
                $('#internalDescHistory span').unbind('click').click(function (e) {
                    MODULES.historyTooltipClick('internalDescHistory', '', '', internalDescriptionId);
                });
                $('#internalDescEditorHeaderDesc img').on('click keypress', function (e) {
                    MODULES.informationTooltipClick('#internalDescEditorHeaderDesc', $("#internaldescriptionTooltip").text());
                });

                //Editor3 -> history & tooltip
                $('#financeDescHistory span').unbind('click').click(function (e) {
                    MODULES.historyTooltipClick('financeDescHistory', '', '', financeDescriptionId);
                });
                $('#financeDescEditorHeaderDesc img').on('click keypress', function () {
                    MODULES.informationTooltipClick('#financeDescEditorHeaderDesc', $("#financingdescriptionTooltip").text());
                });
                EVENTS.loaderOFF(loaderId);
            }, error => {
                ERROR.displayException(error.data, error.status, error.data, "climateActionDetailEditor");
                EVENTS.loaderOFF(loaderId);
            });
        }

        saveClimateDescriptions(description: string, descriptionId: string, descriptionType: number, collaborativePopupCloseFlag: boolean) {
            //Save description into the database on click on editor save
            if (this.climateActionId == "0") {
                if (descriptionType === 1) {
                    $('#externalDescEditorHtmlContent,#externalDescEditorHiddenContent').html(description);
                }
                else if (descriptionType === 2) {
                    $('#internalDescEditorHtmlContent,#internalDescEditorHiddenContent').html(description);
                }
                else {
                    $('#financeDescEditorHtmlContent,#financeDescEditorHiddenContent').html(description);
                }
                if (collaborativePopupCloseFlag && $('#collaborativeEditorWindow').data('kendoWindow')) {
                    $('#collaborativeEditorWindow').data('kendoWindow').close();
                }
                EVENTS.loaderOFF('#collaborativeEditorWindow');
            }
            else {
                let finalObject = {
                    description: UTILS.encodeHtml(description),
                    descriptionType: descriptionType,
                    historyId: descriptionId,
                };

                this.$http.post<void>("../api/PlanClimateActionApi/SavePlanClimateActionDescriptionsData?climateActionId=" + this.climateActionId + "&planId=" + this.planId, angular.toJson(finalObject)).then(response => {
                    EVENTS.loaderOFF('#collaborativeEditorWindow');
                    if (collaborativePopupCloseFlag) {
                        $('#collaborativeEditorWindow').data('kendoWindow').close();
                    }
                    this.climateActionDetailEditor();
                }, error => {
                    EVENTS.loaderOFF('#collaborativeEditorWindow');
                    console.log("err : ", error);
                    ERROR.displayException(error, error.textStatus, error.data, "saveClimateDescriptions");
                });
            }
        }

        addNewRowReductionGrid(gridId: string) {
            const grid = $(gridId).data("kendoGrid");
            if (grid) {
                const datasource = grid.dataSource;
                const newRowId = datasource.data().length - 1;

                const dataSourceObject = {
                    "uniqueId": 0,
                    "departmentData": {
                        "key": this.ClimateActionReductionBaseData[0].departmentData.key,
                        "value": this.ClimateActionReductionBaseData[0].departmentData.value
                    },
                    "functionData": {
                        "key": this.ClimateActionReductionBaseData[0].functionData.key,
                        "value": this.ClimateActionReductionBaseData[0].functionData.value
                    },
                    "emissionTypeData": {
                        "key": this.ClimateActionReductionBaseData[0].emissionTypeData.key,
                        "value": this.ClimateActionReductionBaseData[0].emissionTypeData.value
                    },
                    "projectData": {
                        "key": this.ClimateActionReductionBaseData[0].projectData.key,
                        "value": this.ClimateActionReductionBaseData[0].projectData.value
                    },
                    "sectorData": {
                        "key": this.ClimateActionReductionBaseData[0].sectorData.key,
                        "value": this.ClimateActionReductionBaseData[0].sectorData.value
                    },
                    "sourceData": {
                        "key": this.ClimateActionReductionBaseData[0].sourceData.key,
                        "value": this.ClimateActionReductionBaseData[0].sourceData.value
                    },
                    "freeDim1": {
                        "key": this.ClimateActionReductionBaseData[0].isFreeDim1Used ? this.ClimateActionReductionBaseData[0].freeDim1.key : "",
                        "value": this.ClimateActionReductionBaseData[0].isFreeDim1Used ? this.ClimateActionReductionBaseData[0].freeDim1.value : "",
                    },
                    "freeDim2": {
                        "key": this.ClimateActionReductionBaseData[0].isFreeDim2Used ? this.ClimateActionReductionBaseData[0].freeDim2.key : "",
                        "value": this.ClimateActionReductionBaseData[0].isFreeDim2Used ? this.ClimateActionReductionBaseData[0].freeDim2.value : "",
                    },
                    "freeDim3": {
                        "key": this.ClimateActionReductionBaseData[0].isFreeDim3Used ? this.ClimateActionReductionBaseData[0].freeDim3.key : "",
                        "value": this.ClimateActionReductionBaseData[0].isFreeDim3Used ? this.ClimateActionReductionBaseData[0].freeDim3.value : "",
                    },
                    "freeDim4": {
                        "key": this.ClimateActionReductionBaseData[0].isFreeDim4Used ? this.ClimateActionReductionBaseData[0].freeDim4.key : "",
                        "value": this.ClimateActionReductionBaseData[0].isFreeDim4Used ? this.ClimateActionReductionBaseData[0].freeDim4.value : "",
                    },
                    "longtermreduction": 0,
                    "climateReductionQuantity": {
                        "key": this.ClimateActionReductionBaseData[0].climateReductionQuantity.key,
                        "value": this.ClimateActionReductionBaseData[0].climateReductionQuantity.value,
                    }
                }

                // Generating dynamic years columns
                const regex = /year[0-9]{4}/;
                for (let i = 0; i < grid.columns.length; i++) {
                    const field = grid.columns[i]["field"];
                    if (regex.test(field)) {
                        dataSourceObject[field] = 0;
                    }
                }

                // insert new row dataitem
                datasource.insert(newRowId, dataSourceObject);
            }

            this.$timeout(() => {
                $(gridId).find(".k-grid-content").scrollTop($(gridId).find(".k-grid-content tbody").height());
            }, 100);
        }

        deleteReductionGridData(investment: { uniqueId: string }) {
            this.reductionGridDeleteConfirmWindow = $("#reductionGridDeleteConfirmation").kendoWindow({
                width: "600px",
                title: localStorage.getItem("Confirm"),
                resizable: false,
                modal: true
            }).data("kendoWindow").center().open() as kendo.ui.Window;

            const target: any = $(event.target).closest("tr");

            $("#reductionGridDeleteConfirmationYes").unbind("click").click(() => {
                //if (uniqueId == "0") {
                // delete grid row
                $("#reductionGrid").data("kendoGrid").removeRow(target);
                this.applyTotalSumCalculation();
                this.applyRedcutionTotalSumCalculation();
                //}
                this.reductionGridDeleteConfirmWindow.close();
                return false;
            });
            $("#reductionGridDeleteConfirmationNo").unbind("click").click(() => {
                this.reductionGridDeleteConfirmWindow.close();
                return false;
            });
        }

        gridDropdownEditor = (container: JQuery, options: kendo.ui.GridColumnEditorOptions): void => {
            const fieldName = options.field.split(".")[0];
            const field = options.field;
            const inputModel = options.model.get(field);
            let select = (inputModel == "0") ? false : true;
            let dataSource = [];
            switch (field) {
                case "projectData.key":
                    dataSource = this.projectDataSource;
                    break;
                case "departmentData.key":
                    dataSource = this.departmentDataSource;
                    break;
                case "functionData.key":
                    dataSource = this.functionDataSource;
                    break;
                case "sectorData.key":
                    dataSource = this.sectorDataSource;
                    break;
                case "sourceData.key":
                    dataSource = this.sourceDataSource;
                    break;
                case "emissionTypeData.key":
                    dataSource = this.emissionTypeDataSource;
                    break;
                case "climateReductionQuantity.key":
                    dataSource = this.climateReductionQuantity;
                    break;
            }
            const bindValue = fieldName + ".value";
            $('<input data-text-field="value" id="' + fieldName + '" data-value-field="value" data-bind="value: ' + bindValue + '"/>').appendTo(container).kendoComboBox({
                filter: "contains",
                dataSource: dataSource,
                change: (e) => {
                    let selected = e.sender.value();
                    const search = dataSource.find(element => element.value === selected);
                    if (Object.entries(search).length > 0) {
                        options.model.set(field, search.key);
                        options.model.set(bindValue, search.value);
                    }
                    if (field == "emissionTypeData.key") {
                        this.applyTotalSumCalculation();
                        this.applyRedcutionTotalSumCalculation();
                    }
                },
                filtering: function (e) {
                    // to apply custom filter option by including key/value pair
                    //e.preventDefault();
                    //let filters = [];
                    //filters.push({
                    //    field: e.sender.options.dataValueField,
                    //    operator: e.filter.operator,
                    //    value: e.filter.value.toLowerCase()
                    //});
                    //filters.push({
                    //    field: e.sender.options.dataTextField,
                    //    operator: e.filter.operator,
                    //    value: e.filter.value.toLowerCase()
                    //});
                    //e.sender.dataSource.filter({
                    //    logic: "or",
                    //    filters: filters
                    //});
                },
                autoBind: false
            });
            const widget = $("#" + fieldName).data("kendoComboBox");
            MODULES.comboBoxInputSelector(widget, select);
            $('#' + fieldName + '-list.k-list-container.k-popup').css({ 'width': '270px' });
        }
        freedimDropdownEditor = (container: JQuery, options: kendo.ui.GridColumnEditorOptions): void => {
            const field = options.field.split(".")[0];
            let freedimKey = field + ".key";
            let freedimValue = field + ".value";
            const inputModel = options.model.get(freedimKey);
            let select = (inputModel == "0") ? false : true;
            let dataSource = [];
            switch (field) {
                case "freeDim1":
                    dataSource = this.freeDim1DataSource;
                    break;
                case "freeDim2":
                    dataSource = this.freeDim2DataSource;
                    break;
                case "freeDim3":
                    dataSource = this.freeDim3DataSource;
                    break;
                case "freeDim4":
                    dataSource = this.freeDim4DataSource;
                    break;
            }
            $('<input data-text-field="value" id="freeDim" data-value-field="value" data-bind="value:' + freedimValue + '"/>').appendTo(container).kendoComboBox({
                filter: "contains",
                dataSource: dataSource,
                change: function (e: kendo.ui.ComboBoxChangeEvent) {
                    let selected = this.value();
                    const search = dataSource.find(element => element.value === selected);
                    if (Object.entries(search).length > 0) {
                        options.model.set(freedimKey, search.key);
                        options.model.set(freedimValue, search.value);
                    }
                },
                autoBind: false
            });
            const widget = $("#freeDim").data("kendoComboBox");
            MODULES.comboBoxInputSelector(widget, select);
            $('#freeDim-list.k-list-container.k-popup').css({ 'width': '270px' });
        }

        yearEditor = (container: JQuery, options: kendo.ui.GridColumnEditorOptions): void => {
            const widget = $('<input id="climateActionYear" style="text-align:right" data-bind="value:' + options.field + '"/>')
                .appendTo(container)
                .kendoNumericTextBox({
                    spinners: false,
                    decimals: 3,
                    format: "n0"
                });
            this.$timeout(() => {
                $("#climateActionYear").select();
            }, 50);
        }
        saveClimateActionData(closeFlag: boolean) {
            const loaderId = "#climateDetailSection";

            // Required Validation
            let msg = [];

            if (this.climateActionName === "") {
                msg.push($("#PMActionNameValidationMsg").text());
                MODULES.errorConfirmation(msg);
                return false;
            }
            if ($("#climateCategoriesId").data("kendoDropDownList").value() == "") {
                msg.push($("#PMActionCategoryValidationMsg").text());
                MODULES.errorConfirmation(msg);
                return false;
            }
            if (!this.validateActionYears(true)) {
                return;
            };

            const gridData = JSON.parse(JSON.stringify($("#reductionGrid").data("kendoGrid").dataSource.data()));
            gridData.pop();
            if (gridData.length < 1) {
                msg.push($("#PMClimateReductionGridValidation").text());
                MODULES.errorConfirmation(msg);
                return false;
            }

            let incomeGridErr = 0;
            let gridError = [];
            for (let j = 0; j < gridData.length; j++) {
                if (gridData[j].sectorData.key == this.selectDefaultDropDownKey || gridData[j].sectorData.value == "") {
                    gridError.push($("#PWActionSectorRequired").text());
                    incomeGridErr++;
                    break;
                }
                if (gridData[j].sourceData.key == this.selectDefaultDropDownKey || gridData[j].sourceData.value == "") {
                    gridError.push($("#PWActionSourceRequired").text());
                    incomeGridErr++;
                    break;
                }
                if (gridData[j].emissionTypeData.key == this.selectDefaultDropDownKey || gridData[j].emissionTypeData.value == "") {
                    gridError.push($("#PWActionEmissionRequired").text());
                    incomeGridErr++;
                    break;
                }
                if (gridData[j].departmentData.key == this.selectDefaultDropDownKey || gridData[j].departmentData.value == "") {
                    gridError.push($("#PWActionDepartmentRequired").text());
                    incomeGridErr++;
                    break;
                }
                if (gridData[j].functionData.key == this.selectDefaultDropDownKey || gridData[j].functionData.value == "") {
                    gridError.push($("#PWActionFunctionRequired").text());
                    incomeGridErr++;
                    break;
                }
            }

            if (incomeGridErr > 0) {
                MODULES.errorConfirmation(gridError);
                return;
            }

            let goalId = $("#climateGoalId").data("kendoDropDownList").value();
            let targetId = $("#climateTargetId").data("kendoDropDownList").value();
            let strategyId = $("#climateStrategyId").data("kendoDropDownList").value();
            let feasibilityId = $("#climateFeasibilityId").data("kendoDropDownList").value();
            let estimateId = $("#climateSecurityEstimatesId").data("kendoDropDownList").value();
            let headerData = {
                "climateId": this.climateActionId,
                "climateName": this.climateActionName,
                "categoryId": $("#climateCategoriesId").data("kendoDropDownList").value(),
                "startYear": this.startYear,
                "endYear": this.endYear,
                "goalId": goalId == '' ? this.emptyGuid : goalId,
                "targetId": targetId == '' ? this.emptyGuid : targetId,
                "strategyId": strategyId == '' ? 0 : strategyId,
                "actionTags": $("#climateDetailTagList").data("kendoMultiSelect").dataItems(),
                "feasibilityId": feasibilityId == '' ? 0 : feasibilityId,
                "estimateId": estimateId == '' ? 0 : estimateId,
                "referenceUrl": this.referenceUrl
            };

            let colSelObj = {
                "climateActionColumns": this.climateReductionColumns,
                "planId": this.planId,
                "nodeId": this.nodeId,
                "id": this.climateActionId,
            }

            let descDataObjexternal: ClimateActionDescriptionHelper = {
                description: $("#externalDescEditorHiddenContent").html(),
                historyId: this.externalDescriptionId,
                descriptionType: 1
            }

            let descDataObjinternal: ClimateActionDescriptionHelper = {
                description: $("#internalDescEditorHiddenContent").html(),
                historyId: this.internalDescriptionId,
                descriptionType: 2
            }

            let descDataObjfinance: ClimateActionDescriptionHelper = {
                description: $("#financeDescEditorHiddenContent").html(),
                historyId: this.financeDescriptionId,
                descriptionType: 3
            }

            this.descDataObj.push(descDataObjexternal);
            this.descDataObj.push(descDataObjinternal);
            this.descDataObj.push(descDataObjfinance);

            let incomeAmount = [];
            let outcomeAmount = [];

            let startYear = Number(this.startYear);
            let endYear = Number(this.endYear);
            let j = startYear
            for (let i = 1; i <= (endYear - startYear) + 1; i++) {
                incomeAmount.push({
                    "key": j,
                    "value": $("#incomeAmountTextBox" + i).data("kendoNumericTextBox").value()
                })
                outcomeAmount.push({
                    "key": j,
                    "value": $("#outcomeAmountTextBox" + i).data("kendoNumericTextBox").value()
                })
                j++;
            }

            let financeData = {
                "incomeAmountData": incomeAmount,
                "outcomeAmountData": outcomeAmount,
                "longTermEconomicImpact": this.longTermEconomicImpact,
            }

            EVENTS.loaderON(loaderId);
            const createActionObject = {
                "headerData": headerData,
                "descriptionData": this.descDataObj,
                "reductionGridColumnSel": colSelObj,
                "financeData": financeData,
                "reductionGridRecordString": JSON.stringify(gridData)
            }

            this.$http.post<string>("../api/PlanClimateActionApi/SavePlanClimateActionDetail?climateActionId=" + this.climateActionId + "&planId=" + this.planId + "&nodeId=" + this.nodeId, angular.toJson(createActionObject)).then(response => {
                this.climateActionId = response.data;
                EVENTS.loaderOFF(loaderId);
                if (this.climateActionId == "-1") {
                    msg.push($("#DuplicateCombinationValidation").text());
                    MODULES.errorConfirmation(msg);
                    return false;
                }
                else {
                    if (this.dynamicYearColumnChanges == true) {
                        localStorage.setItem("pwClimateActionColumnSelector" + this.nodeId, "");
                        localStorage.setItem("pwclimateReductionColumnSelector_" + this.climateActionId, "");
                    }
                    MODULES.saveConfirmation();
                    this.pageChanges = false;

                    if (closeFlag) {
                        window.location.href = "../Planning/PlanWorker?planId=" + this.planId + "&planName=" + escape(this.planName) + "&nodeId=" + this.nodeId + "&t=worker";
                    } else {
                        window.location.href = "../Planning/ClimateDetails?planId=" + this.planId + "&planName=" + escape(this.planName) + "&nodeId=" + this.nodeId + "&climateId=" + this.climateActionId;
                    }
                }
            }, error => {
                error.methodName = "SavePlanClimateActionDetail";
                ERROR.displayException(error.data, error.status, error.data, error.methodName);
                EVENTS.loaderOFF(loaderId);
            });
        }
    }

    app = angular.module("ClimateDetails", ["kendo.directives"]).config(['$httpProvider', function ($httpProvider) {
        $httpProvider.interceptors.push(() => {
            return {
                'request': function (config) {
                    return MODULES.validateHttpRequest(config);
                }
            };
        });
    }])
        .controller("climateDetailsController", ['$scope', '$http', '$q', '$location', '$timeout', '$sce', '$compile', function ($scope, $http, $q, $location, $timeout, $sce, $compile) {
            return new ClimateDetails($scope, $http, $q, $location, $timeout, $sce, $compile);
        }]);
}