//declare var window: Window;
declare var EVENTS: any;
declare var ERROR: any;
declare var UTILS: any;
module MyPlanDetail {

    interface IPlanScope extends ng.IScope {
        vm: MyPlanDetail;
        structureTreelistOptions: kendo.ui.TreeListOptions;
        structureTreeviewOptions: kendo.ui.TreeViewOptions;
        mainOwnerDataOptions: kendo.ui.MultiSelectOptions;
        writeAccessDataOptions: kendo.ui.MultiSelectOptions;
        readAccessDataOptions: kendo.ui.MultiSelectOptions;
        planTypeOptions: kendo.ui.DropDownListOptions;
        planCategoryOptions: kendo.ui.DropDownListOptions;
        planStatusOptions: kendo.ui.DropDownListOptions;
        filteredContents: string[];
        planUploadOptions: kendo.ui.UploadOptions;
        planOrgVersionOptions: kendo.ui.DropDownListOptions;
        planOrgStructureTreeviewOptions: kendo.ui.TreeViewOptions;
        downLoadListGridOptions: kendo.ui.GridOptions;
        metaExternalLinksTreeViewOptions: kendo.ui.TreeViewOptions;
        metaDatePickerOptions: kendo.ui.DatePickerOptions;
    }

    type PlanStructure = {
        uid?: string;
        planId?: string;
        key?: string;
        overview?: string;
        description?: string;
        childNodeID?: string;
        contentNodeType: string;
        typeDisplayText: string;
    }

    type PlanStructureTreeNode = {
        uid?: string;
        name?: string;
        items?: PlanStructureTreeNode[];
        levelNumber?: number;
        moveRows?: string;
        contentNodeTypeImage?: string;
        contentNodeType?: string;
        description?: string;
        typeDisplayText?: string;
        levelClassName?: string;
    }

    type planOrgStructureArray = {
        orgStructurePlan: planOrgStructure[];
        lowerOrgLevelInfo: lowerLevelOrg[];
    }

    type planOrgStructure = {
        expanded: boolean;
        id: string;
        items: [];
        orgLevel: number;
        text: string;
    }

    type lowerLevelOrg = {
        key: number;
        value: string;
    }

    type orgStructureViewType = {
        orgId: string;
        orgLevel: number;
        dirty: boolean;
        isChecked: boolean;
    }

    interface KeyValuePair {
        key: string;
        value: string;
    }

    // function to return url parameter values
    export const getUrlParameter = (param: string, dummyPath?: string): string => {
        let sPageURL = dummyPath || window.location.search.substring(1),
            sURLVariables = sPageURL.split(/[&||?]/),
            res;

        for (let i = 0; i < sURLVariables.length; i += 1) {
            let paramName = sURLVariables[i],
                sParameterName = (paramName || '').split('=');

            if (sParameterName[0] === param) {
                res = sParameterName[1];
            }
        }
        return res;
    };

    //// function to apply tooltipSter styling
    export const tooltipSter = (className: string): void => {
        (<any>$(className)).tooltipster({
            trigger: 'hover',
            side: 'bottom',
            distance: 0
        });
        $(className)
            .focus(function () {
                (<any>$(this)).tooltipster('open');
            })
            .focusout(function () {
                (<any>$(this)).tooltipster('close');
            })
            .blur(function () {
                (<any>$(this)).tooltipster('close');
            });
    };

    class MyPlanDetail {
        vm: any;
        dropdownDataSource = [];
        contentElementsDataSource = [];
        planId: string = "";
        planTypeId: string = "-1";
        planTypeName: string = "";
        planName = "";
        planTreatedCase = "";
        planLinkCaseFile = "";
        planShortName = "";
        planTypeTreeGridData = 0;
        planState = false;
        planTypeContent = "Mandatory";
        planContentNodeTypes: string[] = [];
        planContentTypes: { "Key": string, "Value": string }[] = [];
        deleteConfirmWindow: kendo.ui.Window;
        addNewPlanStructureWindow: kendo.ui.Window;
        guidanceTextWindow: kendo.ui.Window;
        deleteContentElementWindow: kendo.ui.Window;
        descTextEditorWindow: kendo.ui.Window;
        backToMainConfirmWindow: kendo.ui.Window;
        infoPopupDetailDialog: kendo.ui.Window;
        deleteDocumentWindow: kendo.ui.Window;
        deleteAttachementWindow: kendo.ui.Window;
        showEditor = false;
        structureOverview = "";
        structureTitle = "";
        structureTitleText = "";
        structureDescription = "";
        structureUid = "";
        structureNodeId = "";
        pageChanges = false;
        editor: any;
        ckEditorContentCssTimeStamp: any;
        descriptionContentNodeType = "";
        descriptionContentNodeTypeImage = "";
        descriptionText = "";
        planEditable: boolean = true;
        activeTab = 1;
        accessRole = "READ";
        displayPlanWorkerLink = false;
        isDesignedPerLaw = false;
        showEditorReadOnlyText = true;
        readableEditorDescriptionText = "";
        exisitingPlanFileId = [];
        existingPdfFile = "";
        pdfFileContent: any;
        existinPlanModelData = false;
        isDocumentDeleted = false;
        mainDocUploadBy = "";
        mainDocUploadDate = "";
        isPlanApproved = false;
        lowerOrgLevelInfo: lowerLevelOrg[];
        orgTreeViewEditedData: orgStructureViewType[];
        orgId: string;
        orgLevel: number;
        metaDataDatePicker = '';
        releventLinksProtocolDS = [{ 'key': 0, 'value': 'http://' }, { 'key': 1, 'value': 'https://' }];
        releventLinksProtocol = 0;
        isEnableUploadFile = false;
        existingFile = "";
        exisitingFileId = [];
        uploadSaveUrl = '';
        displayAttachementItems = false;
        downloadedFilesGridLength = 0;
        budgetYear = 2020;
        mainProjectCode = "017490";
        ckEditor5WebSocketURL = "";
        uploadModuleId = "PLAN";
        uploadPageId = "PlanOverview";
        metaExternalProtocolDS = [{ 'key': 0, 'value': 'http://' }, { 'key': 1, 'value': 'https://' }];
        metaExternalProtocol = 0;
        metaExternalUrl = '';
        metaExternalTitle = '';
        uniqueGuid = "00000000-0000-0000-0000-000000000000";
        metaExternalLinksGuid = "-1";
        metaExternalLinkEdit = false;
        metaExternalLinksData = [];
        addNewMetaExternalUrl = false;
        addNewCustomIdCounter = 1;
        isContactInformationAccessible = false;
        isDescriptionAccessible = false;
        isAttachmentsAccessible = false;
        isLinksAccessible = false;
        metaYearPicker = Date;
        metaUnitResponsible = "";
        metaEmail = "";
        metaPhoneNo = "";
        static $inject = ["$http", "$scope"];
        editorKey = "";

        constructor(private $scope: IPlanScope, private $http: ng.IHttpService, public $promise: ng.IPromise<any>, public $location: ng.ILocationService, public $timeout: ng.ITimeoutService, public $compile: ng.ICompileService) {
            this.vm = this;
            this.ckEditorContentCssTimeStamp = 'contentCss1';
            this.addEditPlan(123);
            this.initializePlanDocumentUpload();
            this.getDownLoadListGridData();
            this.initializeDocumentUpload();
            this.getEditorConfigInfo();

            this.$scope.metaDatePickerOptions = {
                format: "dd.MM.yyyy",
                change: (e: kendo.ui.DatePickerChangeEvent) => {                    
                    setTimeout(() => {
                        this.pageChanges = true;
                        this.$scope.$apply();
                    }, 100);
                }
            }

            setTimeout(() => {
                tooltipSter(".existing-plan-links");
            }, 200);
        }
        getEditorConfigInfo() {
            // collaborative editor config urls
            this.$http.get("../content/getcsurls").then(response => {
                let responseData: any = response.data;
                this.editorKey = responseData.key;
            }, error => {
                error.methodName = "getEditorConfigInfo";
                ERROR.displayException(error.data, error.status, error.data, error.methodName);
            });
        }

        addNewPlanType(planId?: number, planTypeName?: string) {
            this.addEditPlan(planId, planTypeName);
        }

        goToMain(pageType: string) {
            // 66315
            let pageUrl = "../Planning/PlanView";
            if (pageType == "worker") {
                pageUrl = "../Planning/PlanWorker?planId=" + this.planId + "&planName=" + escape(this.planName) + "&type=";
            }
            if (this.pageChanges == true) {
                this.backToMainConfirmWindow = $("#planTypeDetailGoBackConfirmation").kendoWindow({
                    width: "600px",
                    title: localStorage.getItem("Confirm"),
                    resizable: false,
                    modal: true
                }).data("kendoWindow").center().open() as kendo.ui.Window;

                $(".plantypedetail-back-confirm-yes").click(() => {
                    window.location.href = pageUrl;
                    this.backToMainConfirmWindow.close();
                    return false;
                });
                $(".plantypedetail-back-confirm-no").click(() => {
                    this.backToMainConfirmWindow.close();
                    return false;
                });
            } else {
                window.location.href = pageUrl;
            }
        }

        expandAll() {
            let treeview = <kendo.ui.TreeView>$("#planStructureTreeView").data("kendoTreeView");
            treeview.expand(".k-item");
        }

        collapseAll() {
            let treeview = <kendo.ui.TreeView>$("#planStructureTreeView").data("kendoTreeView");
            treeview.collapse(".k-item");
        }

        addEditPlan(planId?: number | string, planTypeName?: string) {

            planId = getUrlParameter("planId");
            planTypeName = getUrlParameter("planTypeName");

            // for selected tab
            let t = getUrlParameter("t");
            if (t == "structure") {
                this.activeTab = 2;
            }
            else if (t == "metadata") {
                this.activeTab = 3;
            }
            else {
                this.activeTab = 1;
            }   
            const planWorker = getUrlParameter("pw");
            if (planWorker == "1") {
                this.displayPlanWorkerLink = true;
            }

            this.planId = planId;

            this.contentElementsDataSource = [];

            MODULES.getCKEditor5ConfigInfo((output) => {
                this.ckEditor5WebSocketURL = output.webSocketUrl;
            });

            // Dropdownlist configurations
            this.$scope.planTypeOptions = {
                dataTextField: "value",
                dataValueField: "key",
                dataSource: [],
                change: (ev: kendo.ui.DropDownListChangeEvent) => {
                    let selectedValue = $("#planTypeDropDown").data("kendoDropDownList").value();
                    this.getPlanStructureTreeData(selectedValue);
                    this.applyPageChanges();
                }
            };

            this.$scope.planCategoryOptions = {
                dataTextField: "value",
                dataValueField: "key",
                dataSource: [],
                change: (ev: kendo.ui.DropDownListChangeEvent) => {
                    this.applyPageChanges();
                }
            };

            this.$scope.planStatusOptions = {
                dataTextField: "value",
                dataValueField: "key",
                dataSource: [],
                change: (ev: kendo.ui.DropDownListChangeEvent) => {
                    this.applyPageChanges();
                }
            };

            // Multiselect configurations

            this.$scope.mainOwnerDataOptions = {
                dataTextField: "value",
                dataValueField: "key",
                valuePrimitive: true,
                autoBind: false,
                dataSource: [],
                change: (ev: kendo.ui.MultiSelectChangeEvent) => {
                    this.applyPageChanges();
                }
            };

            this.$scope.writeAccessDataOptions = {
                dataTextField: "value",
                dataValueField: "key",
                valuePrimitive: true,
                autoBind: false,
                dataSource: [],
                change: (ev: kendo.ui.MultiSelectChangeEvent) => {
                    this.applyPageChanges();
                }
            };

            this.$scope.readAccessDataOptions = {
                dataTextField: "value",
                dataValueField: "key",
                valuePrimitive: true,
                autoBind: false,
                dataSource: [],
                change: (ev: kendo.ui.MultiSelectChangeEvent) => {
                    this.applyPageChanges();
                }
            };

            this.$scope.planOrgVersionOptions = {
                dataTextField: "value",
                dataValueField: "key",
                valuePrimitive: true,
                dataSource: [],
                change: (ev: kendo.ui.DropDownListChangeEvent) => {
                    this.applyPageChanges();
                    this.getPlanDetailOrgTree();
                }
            }

            this.$scope.planOrgStructureTreeviewOptions = {
                dataSource: [],
                autoScroll: true,
                dragAndDrop: false,
                checkboxes: {
                    checkChildren: true,
                    template: kendo.template(
                        "<input class='k-checkbox #= item.orgLevel # checkBoxPadding' type='checkbox' #= item.checked ? 'checked' : '' #  #= item.isNodeDisabled ? 'disabled' : '' #/>"
                    )
                }
            }

            const isEditable = (this.planId) ? true : false;
            this.$http.get("../api/PlanningApi/GetPlansBaseData/" + isEditable).then(response => {

                let responseData: any = response.data;
                $("#planTypeDropDown").data("kendoDropDownList").setDataSource(responseData.planTypeData);
                $("#planCategoryDropDown").data("kendoDropDownList").setDataSource(responseData.planCategoryData);
                $("#planStatusDropDown").data("kendoDropDownList").setDataSource(responseData.planStatusData);
                $("#mainOwnerData").data("kendoMultiSelect").setDataSource(responseData.planMainOwnerUsersData);
                $("#writeAccessData").data("kendoMultiSelect").setDataSource(responseData.planWriteAccessUsersData);
                $("#readAccessData").data("kendoMultiSelect").setDataSource(responseData.planReadAccessUsersData);
                $("#planOrgVersionDropDown").data("kendoDropDownList").setDataSource(responseData.orgVersionData.orgVersionDataSource);
                if (responseData.orgVersionData.defaultOrgVersion != null) {
                    $("#planOrgVersionDropDown").data("kendoDropDownList").value(responseData.orgVersionData.defaultOrgVersion.key);
                }
                this.orgId = responseData.orgId;
                this.orgLevel = responseData.orgLevel
                if (responseData.loggedUserId != "") {
                    this.$timeout(() => {
                        $("#mainOwnerData").data("kendoMultiSelect").value(responseData.loggedUserId);
                    }, 100);
                }

                // get Plan data details
                if (this.planId) {
                    this.getPlanData(this.planId);
                }
                if (!this.planId && this.orgId != "-1" && this.orgLevel != -1) {
                    this.getPlanDetailOrgTree();
                }
            }, error => {
                console.log("err : ", error);
            });

            // get plantype allowedcontentnodetypes
            this.$http.get("../api/PlanAdminApi/PlanContentTypes/" + false).then(response => {
                let responseData: any = response.data;
                this.planContentTypes = responseData;
            }, error => {
                console.log("contentnodetypes err : ", error);
            });

            let treeviewJson = { "Treedata": [] };


            this.planTypeTreeGridData = treeviewJson.Treedata.length;
            let disableLoader = false;
            this.$scope.structureTreeviewOptions = {
                dataSource: treeviewJson.Treedata,
                dataTextField: "text",
                autoScroll: true,
                template: kendo.template($("#planStructureTreeviewTemplate").html()),
                dragAndDrop: true,
                //loadOnDemand: false,
                dragend: (e: kendo.ui.TreeViewDragendEvent) => {
                    const treeview = $("#planStructureTreeView").data("kendoTreeView");
                    const treeviewDS = treeview.dataSource.data();
                    const destDataItem: any = treeview.dataItem(e.destinationNode);
                    const parentDestDataItem: any = treeview.dataItem(treeview.parent(e.destinationNode));
                    const position = e.dropPosition;
                    // 77366
                    if (destDataItem) {
                        if (position === "over") {
                            localStorage.setItem("MyPlanDetailChildNodeID", destDataItem.childNodeID);
                        } else if ((position == "before" || position == "after") && parentDestDataItem) {
                            localStorage.setItem("MyPlanDetailChildNodeID", destDataItem.childNodeID);
                        }
                        this.savePlanData();
                    }
                },
                drag: (e) => {
                    disableLoader = false;
                    if (e.statusClass == "i-cancel" || e.statusClass == "k-i-cancel") {
                        disableLoader = true;
                    }
                    let eleTop = e.pageY;
                    let treeScrollTop = $("#planStructureTreeView").scrollTop();
                    let treeTop = $("#planStructureTreeView").offset().top;
                    $("#planStructureTreeView").animate({
                        scrollTop: (treeScrollTop + eleTop) - treeTop
                    });
                },
                drop: (e: kendo.ui.TreeViewDropEvent) => {
                    // Handling customized treeview template design according to levelNumber
                    const treeview = $("#planStructureTreeView").data("kendoTreeView");
                    const sourceDataItem: any = treeview.dataItem(e.sourceNode);
                    const destDataItem: any = treeview.dataItem(e.destinationNode);
                    const position = e.dropPosition;

                    if (typeof e.destinationNode === "undefined") {
                        e.preventDefault();
                        return false;
                    }

                    if (typeof destDataItem === "undefined") {
                        e.preventDefault();
                    }

                    const parentSourceDataItem = treeview.dataItem(treeview.parent(e.sourceNode));
                    const parentDestDataItem = treeview.dataItem(treeview.parent(e.destinationNode));

                    if (typeof parentDestDataItem == "undefined") {
                        sourceDataItem.levelNumber = 0;
                    }
                    if (typeof parentSourceDataItem !== "undefined" && typeof parentDestDataItem !== "undefined") {
                        if (parentSourceDataItem["id"] == parentDestDataItem["id"] && (position == "before" || position == "after")) {
                            sourceDataItem.levelNumber = destDataItem.levelNumber;
                        }

                        if (parentSourceDataItem["id"] == parentDestDataItem["id"] && position == "over") {
                            sourceDataItem.levelNumber = destDataItem.levelNumber + 1;
                        }

                        if (parentSourceDataItem["id"] !== parentDestDataItem["id"] && (position == "before" || position == "after")) {
                            sourceDataItem.levelNumber = destDataItem.levelNumber;
                        }

                        if (parentSourceDataItem["id"] == destDataItem["id"] && (position == "before" || position == "after")) {
                            sourceDataItem.levelNumber = destDataItem.levelNumber;
                        }
                    }

                    if (typeof parentSourceDataItem == "undefined" && typeof parentDestDataItem !== "undefined" && position == "over") {
                        sourceDataItem.levelNumber = destDataItem.levelNumber + 1;
                    }

                    if (typeof parentSourceDataItem !== "undefined" && typeof parentDestDataItem == "undefined" && position == "over") {
                        sourceDataItem.levelNumber = destDataItem.levelNumber + 1;
                    }

                    if (typeof parentDestDataItem == "undefined" && typeof parentSourceDataItem == "undefined" && position == "over") {
                        sourceDataItem.levelNumber = destDataItem.levelNumber + 1;
                    }
                    this.pageChanges = true;
                    if (!disableLoader) {
                        EVENTS.loaderON("#planTypeTreeGridList");
                    }
                },
                dataBound: (e) => {
                    $("#planStructureTreeView").removeAttr('tabindex');
                    $("#planStructureTreeView .tvicons-level1-items").closest('span.k-in').prev('.k-icon').addClass('level1-icons-style');
                    this.$timeout(() => {
                        if (!$(".delete-plan-structure").hasClass("tooltipstered")) {
                            tooltipSter(".delete-plan-structure");
                        }
                    }, 100);
                    this.$timeout(() => {
                        if (!$(".chapter-name").hasClass("tooltipstered")) {
                            tooltipSter(".chapter-name");
                        }
                    }, 400);
                },
                expand: function (e) {
                    $("#planStructureTreeView .level1-icons-style").closest('li').addClass('treeview-expandbg-style');
                },
                change: (e: kendo.ui.TreeViewEvent) => {
                    this.pageChanges = true;
                }
            }

            // kendo treeview dragclue
            kendo.ui.TreeView.fn._hintText = function (node) {
                var template = kendo.template($("#treeview-custom-template").html());
                var result = template({
                    item: this.dataItem(node)
                });
                return result;
            };
            // Applying Tooltipster to selected content elements on page load
            tooltipSter(".tooltipcls");

            // Meta setup
            this.getMetaDataDetails();

        }

        getPlanDetailOrgTree() {
            let loaderId = this.getLoaderId();
            EVENTS.loaderON(loaderId);
            var orgVersion = $("#planOrgVersionDropDown").data("kendoDropDownList").value();
            this.$http.get<planOrgStructureArray>("../api/PlanningApi/GetHierarchyForPlan?orgIdCreatedAt=" + this.orgId + "&orgIdLevelCreatedAt=" + this.orgLevel + "&planId=" + this.planId + "&fkOrgVersion=" + orgVersion).then(response => {

                this.lowerOrgLevelInfo = response.data.lowerOrgLevelInfo;
                $("#planOrgStructureTreeView").data("kendoTreeView").setDataSource(new kendo.data.HierarchicalDataSource({
                    data: response.data.orgStructurePlan
                }));
                $("#planOrgStructureTreeView").data("kendoTreeView").bind("check", this.treeviewDataOnCheck);
                EVENTS.loaderOFF(loaderId);
            }, error => {
                EVENTS.loaderOFF(loaderId);
                console.log("Tree Structure View : ", error);
            });
        }

        lowerOrgLevelInfoCheck = function (orgLevel) {
            let treeData;
            if (orgLevel.check) {
                $('#planOrgStructureTreeView').find('.' + orgLevel.key).each(function () {
                    $(this).prop("checked", true);
                });

                treeData = $("#planOrgStructureTreeView").data("kendoTreeView").dataSource.view();
                this.checkForLowerOrgLevel(treeData, orgLevel.key, true);
            }
            else {
                $('#planOrgStructureTreeView').find('.' + orgLevel.key).each(function () {
                    $(this).prop("checked", false);
                });

                treeData = $("#planOrgStructureTreeView").data("kendoTreeView").dataSource.view();
                this.checkForLowerOrgLevel(treeData, orgLevel.key, false);
            }
            treeData = $("#planOrgStructureTreeView").data("kendoTreeView").dataSource.view();
            this.updateIndeterminateNodes(treeData);
        }

        checkForLowerOrgLevel = function (treeData, orgLevel, dirty) {
            for (var i = 0; i < treeData.length; i++) {
                if (treeData[i].orgLevel == orgLevel) {
                    treeData[i].dirty = dirty;
                    treeData[i].checked = dirty;
                }
                if (treeData[i].hasChildren) {
                    this.checkForLowerOrgLevel(treeData[i].children.view(), orgLevel, dirty);
                }
            }
            this.pageChanges = true;
            var checkedNodes = [];
            var arr = this.checkForNodes(treeData, checkedNodes);
            this.treeViewCheckedData = arr;
            this.orgTreeViewEditedData = arr;
        }

        checkForNodes = function (treeData, checkedNodes, childrenDirty) {
            for (var i = 0; i < treeData.length; i++) {
                var checked = (treeData[i].checked) ? treeData[i].checked : false;
                checkedNodes.push({
                    orgId: treeData[i].id,
                    orgLevel: treeData[i].orgLevel,
                    dirty: treeData[i].dirty,
                    isChecked: checked
                });
                if (treeData[i].hasChildren) {
                    this.checkForNodes(treeData[i].children.view(), checkedNodes, treeData[i].dirty);
                }
            }
            return checkedNodes;
        }

        checkForTreeViewNodes(treeData: any, checkedNodes: any[]) {

            for (let i = 0; i < treeData.length; i++) {
                let checked = (treeData[i].checked) ? treeData[i].checked : false;
                checkedNodes.push({
                    orgId: treeData[i].id,
                    orgLevel: treeData[i].orgLevel,
                    dirty: treeData[i].dirty,
                    isChecked: checked
                });
                if (treeData[i].hasChildren) {
                    this.checkForTreeViewNodes(treeData[i].children.view(), checkedNodes);
                }
            }
            return checkedNodes;
        }

        updateIndeterminateNodes(treeData: any) {
            EVENTS.loaderON('#planOrgStructureTreeView');
            EVENTS.loaderON('#ordersOrgTopLevelList');
            var treeview = $("#planOrgStructureTreeView").data("kendoTreeView");
            for (let i = 0; i < treeData.length; i++) {
                let node = treeview.findByUid(treeData[i].uid);
                treeview.updateIndeterminate(node);
            }
            EVENTS.loaderOFF('#planOrgStructureTreeView');
            EVENTS.loaderOFF('#ordersOrgTopLevelList');
        }

        treeviewDataOnCheck = () => {
            let checkedNodes = [];
            this.applyPageChanges();
            let orgTreeViewData = $("#planOrgStructureTreeView").data("kendoTreeView").dataSource.view();
            this.orgTreeViewEditedData = this.checkForTreeViewNodes(orgTreeViewData, checkedNodes);
        }

        initializePlanDocumentUpload() {
            this.$scope.planUploadOptions = {
                name: "planUploadFile",
                files: [],
                multiple: false,
                showFileList: false,
                validation: {
                    allowedExtensions: [".pdf"]
                },
                upload: (e: kendo.ui.UploadUploadEvent) => {

                },
                select: (e: kendo.ui.UploadSelectEvent) => {
                    let notAllowed = false;
                    $.each(e.files, (index, value) => {
                        if (value.extension !== '.pdf') {
                            notAllowed = true;
                        }

                        if (value.extension == '.pdf') {
                            this.pageChanges = true;
                            let current = {
                                name: value.name,
                                extension: value.extension,
                                size: value.size
                            }
                            this.isDocumentDeleted = true; // 74676
                            this.existingPdfFile = value.name;
                            $("#planPdfFileName").text(value.name);

                            this.exisitingPlanFileId.push(current);
                            setTimeout(() => {
                                tooltipSter(".plan-pdf-delete");
                                this.convertFileToBlob(value);
                            }, 100);
                        }

                        if (notAllowed) {
                            // upload file validation
                            const msg = [$("#planNameUploadValidation").text()];
                            MODULES.errorConfirmation(msg);
                            e.preventDefault();
                        }

                        this.formatImportUpload();
                    });

                }
            }

            setTimeout(() => {
                this.formatImportUpload();
            }, 200);

        }

        formatImportUpload() {
            $("#planUploadImportSection").find('.k-upload-selected').css({ 'display': 'none' });
            $("#planUploadImportSection").find('.k-upload-files.k-reset').css({ 'display': 'none' });
            $("#planUploadImportSection").find('.k-upload-action').css({ 'display': 'none' });
            $("#planUploadImportSection").find('.k-dropzone').attr('class', 'fp-import-section');
            $("#planUploadImportSection").find('.fp-import-section').find('em').remove();
            $("#planUploadImportSection").find('.fp-import-section').find('.k-upload-button').addClass('no-border');
            $("#planUploadImportSection").find('.fp-import-section').find('.k-upload-button').addClass('padding0');
            $("#planUploadImportSection").next().text('');
            $("#planUploadImportSection").next().html('<a class="" href=""><img src="../images/icon_import.png" style="margin-top:-4px;"></a>');
            $("#planUploadImportSection").find('.k-upload-empty').addClass('fm-upload-height-auto');
            $("#planUploadImportSection").find('.k-upload').addClass('fm-upload-height-auto');
            $("#planUploadImportSection").find('.k-upload').addClass('no-border');
            $("#planUploadImportSection").find('.k-upload').addClass('min-width-1');
            $("#planUploadImportSection").find('.k-upload-empty').addClass('no-border');
            $("#planUploadImportSection").find('.k-upload').addClass('opacity-1-75');
            $("#planUploadImportSection").find('.k-upload .k-upload-button').css('min-width', '2px');
            $("#planUploadImportSection").find('.k-upload .k-upload-button').attr('title', $("#planNameUploadAttachments").text());
            $("#planUploadImportSection").find('.k-upload-status.k-upload-status-total').css({ 'display': 'none' });
            $("#planUploadPdf").next().addClass("btn btn-primary font16");
            $("#planUploadPdf").next().addClass("btn btn-primary font16").text($("#planNameUploadMainDoc").text());
        }

        convertFileToBlob(data: any) {
            this.pdfFileContent = data.rawFile;
            this.applyPageChanges();
        }

        applyPageChanges() {
            this.pageChanges = true;
            this.$scope.$apply();
        }

        getPlanData(planId: string) {
            let loaderId = this.getLoaderId();
            if (planId) {
                // get Plan details
                EVENTS.loaderON(loaderId);
                this.$http.get("../api/PlanningApi/GetMyplan/" + planId + "/" + false).then(response => {
                    this.planEditable = false;
                    let responseData: any = response.data;
                    this.planTypeId = responseData.planTypeID;
                    this.planState = responseData.stateFunds;
                    this.planName = responseData.planname;
                    this.planShortName = responseData.shortname;
                    this.accessRole = responseData.role;
                    this.isDesignedPerLaw = responseData.isDesignedPerLaw;
                    this.existinPlanModelData = responseData.isOldPlan;
                    this.existingPdfFile = responseData.mainDocName;
                    this.mainDocUploadBy = responseData.mainDocUploadBy;
                    this.mainDocUploadDate = responseData.mainDocUploadDate;
                    this.isPlanApproved = responseData.isPlanApproved;
                    this.planTreatedCase = responseData.treatedCase;
                    this.planLinkCaseFile = responseData.linkToCaseFile;
                    this.metaYearPicker = responseData.dateProcessed;

                    if (responseData.shortname)
                        document.title = responseData.shortname;

                    if (responseData.isOldPlan == false) {
                        $("#existingPlanDiv").addClass("not-active");
                    } else {
                        $("#existingPlanDiv").removeClass("not-active");
                    }

                    if (!responseData.mainDocName) {
                        $("#planPdfFileName").html("");
                        this.isDocumentDeleted = true;
                        $("#uploadFileBtn").removeClass("not-active");
                    } else {
                        $("#planPdfExistingFileName").html(responseData.mainDocName);
                        this.isDocumentDeleted = false;
                        $("#uploadFileBtn").addClass("not-active");
                    }

                    // populate dropdown & multiselect values
                    $("#planTypeDropDown").data("kendoDropDownList").value(responseData.planTypeID);
                    $("#planCategoryDropDown").data("kendoDropDownList").value(responseData.planCategoryID);
                    $("#planStatusDropDown").data("kendoDropDownList").value(responseData.Status);
                    $("#mainOwnerData").data("kendoMultiSelect").value(responseData.mainOwnerList);
                    $("#writeAccessData").data("kendoMultiSelect").value(responseData.writeList);
                    $("#readAccessData").data("kendoMultiSelect").value(responseData.readList);
                    if (responseData.orgVersion != null) {
                        $("#planOrgVersionDropDown").data("kendoDropDownList").value(responseData.orgVersion);
                    }
                    this.$timeout(() => {
                        this.planTypeContent = responseData.planHelper.usage;
                        this.planContentNodeTypes = responseData.planHelper.allowedContentNodeTypes;
                        //this.
                        $("#planStructureTreeView").data("kendoTreeView").setDataSource(new kendo.data.HierarchicalDataSource({
                            data: responseData.planHelper.templateTree
                        }));
                        this.planTypeTreeGridData = responseData.planHelper.templateTree.length;
                        let filteredContents = [];
                        if (this.planContentNodeTypes.length > 0) {
                            for (let i = 0; i < this.planContentTypes.length; i++) {
                                if (this.planContentNodeTypes.indexOf(this.planContentTypes[i]["Key"]) >= 0) {
                                    filteredContents.push({ "contentNodeType": this.planContentTypes[i].Key, "typeDisplayText": this.planContentTypes[i].Value });
                                }
                            }
                            this.$scope.filteredContents = filteredContents;
                        }

                        // expand the nodes - 77366
                        if (localStorage.getItem("MyPlanDetailChildNodeID")) {
                            const treeview = $("#planStructureTreeView").data("kendoTreeView");
                            const treeNode = this.searchTreeViewNode(treeview.dataSource.data(), localStorage.getItem("MyPlanDetailChildNodeID"));
                            // expand the treeview
                            if (treeNode && Object.keys(treeNode).length > 0) {
                                //treeview.expand(treeview.findByUid(treeNode.uid));
                                const item = treeview.findByUid(treeNode.uid);
                                if (treeNode.hasChildren === true) {
                                    treeview.expand(item);
                                }
                                let parent = treeview.parent(item);
                                while (parent && Object.keys(parent).length > 0) {
                                    treeview.expand(parent);
                                    parent = treeview.parent(parent);
                                }
                                localStorage.setItem("MyPlanDetailChildNodeID", "");
                            }
                        }

                        tooltipSter(".plan-pdf-delete");
                        if (this.orgId != "-1" && this.orgLevel != -1) {
                            this.getPlanDetailOrgTree();
                        }
                        this.$scope.$apply();
                    }, 100);


                    EVENTS.loaderOFF(loaderId);
                }, error => {
                    console.log("contentnodetypes err : ", error);
                    EVENTS.loaderOFF(loaderId);
                });
            }
        }

        checkContentElementTreeView(value: string, data: kendo.ui.TreeView) {
            const treeview = <kendo.ui.TreeView>$("#planStructureTreeView").data("kendoTreeView");
            let treeviewData = treeview.dataSource.data();

            for (let i = 0; i < treeviewData.length; i++) {
                if (treeviewData[i].id == "0" && treeviewData[i].contentNodeType == value) {
                    let item = treeview.findByUid(treeviewData[i].uid);
                    treeview.remove(item);
                }
            }
        }

        getPlanStructureTreeData(planTypeId: string) {
            if (planTypeId) {
                EVENTS.loaderON("#structureTabContent");
                this.$http.get("../api/PlanningApi/GetPlanTypeStructure/" + planTypeId).then(response => {
                    let responseData: any = response.data;
                    this.$timeout(() => {
                        this.planTypeId = responseData.id;
                        this.planTypeName = responseData.name;
                        this.planTypeContent = responseData.usage;
                        this.planContentNodeTypes = responseData.allowedContentNodeTypes;
                        this.isDesignedPerLaw = responseData.isDesignedPerLaw;
                        $("#planStructureTreeView").data("kendoTreeView").setDataSource(new kendo.data.HierarchicalDataSource({
                            data: responseData.templateTree
                        }));
                        this.planTypeTreeGridData = responseData.templateTree.length;
                        let filteredContents = [];
                        if (this.planContentNodeTypes.length > 0) {
                            for (let i = 0; i < this.planContentTypes.length; i++) {
                                if (this.planContentNodeTypes.indexOf(this.planContentTypes[i]["Key"]) >= 0) {
                                    filteredContents.push({ "contentNodeType": this.planContentTypes[i].Key, "typeDisplayText": this.planContentTypes[i].Value });
                                }
                                console.log("key : ", this.planContentTypes[i]["Key"]);
                            }
                            this.$scope.filteredContents = filteredContents;
                        }
                    }, 100);

                    EVENTS.loaderOFF("#structureTabContent");
                }, error => {
                    console.log("err : ", error);
                    EVENTS.loaderOFF("#structureTabContent");
                    let msg = [localStorage.getItem('ErrorMessage')];
                    MODULES.errorConfirmation(msg);
                });
            }
        }

        // To add class name for treeview alignment
        addClassTreeViewNodes(treeData: {}[], levelNumber: number) {
            for (let i = 0; i < treeData.length; i++) {
                let j = levelNumber;
                treeData[i]["className"] = "tv-level" + j + "-items";
                if (treeData[i]["items"].length > 0) {
                    treeData[i]["className"] = "tvicons-level" + j + "-items";
                    this.addClassTreeViewNodes(treeData[i]["items"], treeData[i]["levelNumber"] + 1);
                }
            }
        }

        confirmContentElementDelete(key: string, value: string) {
            const contentElements = <kendo.ui.MultiSelect>$("#contentElements").data("kendoMultiSelect");
            let contentElementsDataSource = contentElements.value();
            this.deleteContentElementWindow = $('#deleteContentElementWindow').kendoWindow({
                modal: true,
                actions: ["close"],
                scrollable: true,
                resizable: false,
                draggable: false,
                width: "40%"
            }).data("kendoWindow").center().open() as kendo.ui.Window;
            this.deleteContentElementWindow.element.prev().remove(); //find(".k-window-title").parent('<span></span>');

            $("#deleteContentElementOk").click(() => {
                let filterValue = contentElementsDataSource.filter((obj: string) => {
                    return obj !== key;
                });
                contentElements.value(filterValue);

                // remove structure from treeview
                const treeview = <kendo.ui.TreeView>$("#planStructureTreeView").data("kendoTreeView");
                let treeviewData = treeview.dataSource.data();
                for (let i = 0; i < treeviewData.length; i++) {
                    if (treeviewData[i].contentNodeType == key) {
                        let item = treeview.findByUid(treeviewData[i].uid);
                        treeview.remove(item);
                    }
                }
                this.deleteContentElementWindow.close();
            });

            $("#deleteContentElementCancel").click(() => {
                this.deleteContentElementWindow.close();
                contentElementsDataSource.push(key);
                $("#contentElements").data("kendoMultiSelect").value(contentElementsDataSource);

                let dataItems = $("#contentElements").data("kendoMultiSelect").dataItems();
                let filteredItems = [];
                if (dataItems.length > 0) {
                    for (let i = 0; i < dataItems.length; i++) {
                        filteredItems.push({ "contentNodeType": dataItems[i].Key, "typeDisplayText": dataItems[i].Value });
                    }
                    this.$scope.filteredContents = filteredItems;
                    this.$scope.$apply();
                }
            });
        }

        deletePlanStructure(planTypeId: string, treeviewUId: string, nodeId: string, isExistingNode: boolean) {
            this.deleteConfirmWindow = $('#deletePlanWindow').kendoWindow({
                modal: true,
                actions: ["close"],
                scrollable: true,
                resizable: false,
                draggable: false,
                width: "40%"
            }).data("kendoWindow").center().open() as kendo.ui.Window;
            this.deleteConfirmWindow.element.prev().remove(); //find(".k-window-title").parent('<span></span>');

            $("#deleteCancel").click(() => {
                this.deleteConfirmWindow.close();
            });

            $("#deleteOk").unbind("click").click(() => {
                // remove from treeview if its new node
                if (isExistingNode == false) {
                    let treeview = <kendo.ui.TreeView>$("#planStructureTreeView").data("kendoTreeView");
                    let item = treeview.findByUid(treeviewUId);
                    treeview.remove(item);
                    this.deleteConfirmWindow.close();
                } else {
                    // remove from backend for existing nodes
                    const jsonData: {} = {};
                    if (this.planId) {
                        EVENTS.loaderON("#planTypeTreeGridList");
                        this.$http.post("../api/PlanningApi/DeleteNode/" + this.planId + "/" + nodeId, angular.toJson(jsonData)).then(response => {
                            EVENTS.loaderOFF("#planTypeTreeGridList");
                            MODULES.saveConfirmation();
                            this.deleteConfirmWindow.close();
                            //this.pageChanges = true;
                            this.getPlanData(this.planId);
                        }, error => {
                            console.log("err : ", error);
                            EVENTS.loaderOFF("#planTypeTreeGridList");
                        });
                    }
                }

            });
        }

        addNewPlanStructure(planStructure: PlanStructure) {
            this.addNewPlanStructureWindow = $('#addNewPlanTypeWindow').kendoWindow({
                modal: true,
                actions: ["close"],
                scrollable: true,
                resizable: false,
                draggable: false,
                width: "70%"
            }).data("kendoWindow").center().open() as kendo.ui.Window;
            this.addNewPlanStructureWindow.element.prev().addClass('background-white padding-top15 padding-bottom10');
            this.addNewPlanStructureWindow.element.prev().find('#addNewPlanTypeWindow_wnd_title').html('<span class="padding-left20"><img src="../images/plan_icons_text.svg" class="icons-height-width"/><span class="padding-left5" id="nodeWindowHeaderTitle"></span></span>'); //find(".k-window-title").parent('<span></span>');
            this.addNewPlanStructureWindow.element.prev().find('.k-window-actions').addClass('close-icon-img padding-right10');
            this.addNewPlanStructureWindow.element.parent().removeAttr('tabindex');
            $('#textFieldHeading').focus();
            // 87667
            //this.addNewPlanStructureWindow.element.prev().find('.k-window-actions a').attr('tabindex', '0').attr('id', 'cancelAddNew'); 
            this.addNewPlanStructureWindow.element.parent().css({ 'position': 'fixed', 'top': '0' });
            $("#cancelAddNew").on('click keypress', () => {
                this.addNewPlanStructureWindow.close();
            });
            this.readableEditorDescriptionText = "";

            if (!this.editor) {
                CKEDITOR5.DocumentEditor
                    .create(document.querySelector('#planDetailEditor1'), {
                        simpleUpload: {
                            headers: TYPESCRIPTMODULES.getSimpleUplodHeaders()
                        },
                        removePlugins: ['Heading'],
                        licenseKey: this.editorKey,
                        toolbar: {
                            removeItems: ['heading']
                        }
                    })
                    .then(editor => {
                        this.editor = editor;
                        const toolbarContainer = document.querySelector('.document-editor__toolbar');
                        toolbarContainer.appendChild(editor.ui.view.toolbar.element);
                        TYPESCRIPTMODULES.replaceRemoveFormatIcon(editor);
                        $('.document-editor__toolbar button').attr('tabindex', '0'); // 74750 - Keyboard navigation

                        if (typeof planStructure.uid !== "undefined" && planStructure.uid != "") {
                            this.editor.setData(unescape(planStructure.description));
                        }
                        TYPESCRIPTMODULES.editorImageUploadStatus(editor);
                    })
                    .catch(err => {
                        console.error(err);
                    });
            }

            this.structureOverview = "";
            this.structureDescription = "";
            this.structureUid = "";

            if (typeof planStructure.uid !== "undefined" && planStructure.uid != "") {
                // edit plan structure
                this.structureOverview = unescape(planStructure.overview);
                this.structureDescription = unescape(planStructure.description);
                this.structureTitle = planStructure.typeDisplayText;
                this.structureTitleText = planStructure.contentNodeType;
                this.structureUid = planStructure.uid;
                this.structureNodeId = planStructure.childNodeID;
                this.readableEditorDescriptionText = MODULES.stripHtmlTags(unescape(planStructure.description));
                if (this.editor) {
                    this.editor.setData(unescape(planStructure.description));
                }
            } else {
                // add plan structure
                this.structureTitle = planStructure.typeDisplayText;
                this.structureTitleText = planStructure.contentNodeType;
                if (this.editor) {
                    this.editor.setData("");
                    this.readableEditorDescriptionText = "";
                }
            }
            // window header title
            $("#nodeWindowHeaderTitle").html(this.structureTitle);
        }

        insertNewNode() {
            const structureTreeView = $("#planStructureTreeView").data("kendoTreeView") as kendo.ui.TreeView;

            // treeview datasource
            let tvData = $("#planStructureTreeView").data("kendoTreeView").dataSource.data();

            let contentNodeTypeImage = "../images/plan_icons_text.svg";
            if (this.structureTitleText) {
                let structureTitle = this.structureTitleText.toLowerCase();
                if (structureTitle == "text") {
                    contentNodeTypeImage = "../images/plan_icons_text.svg";
                } else if (structureTitle == "folder") {
                    contentNodeTypeImage = "../images/plan_icons_parent_node.svg";
                } else {
                    contentNodeTypeImage = "../images/plan_icons_table.svg";
                }
            }

            // add new plan structure
            let item = {};
            let errorMessage = [];
            if (this.structureOverview.trim() === "") {
                errorMessage.push($("#nodeNameValidation").html());
            }
            if (errorMessage.length > 0) {
                MODULES.errorConfirmation(errorMessage);
                return false;
            }

            if (this.structureUid == "") {
                item = {
                    name: this.structureOverview,
                    items: [],
                    contentNodeType: this.structureTitleText,
                    typeDisplayText: this.structureTitle,
                    moveRows: "<img src='../images/plan_icons_move.svg'>Flytt",
                    contentNodeTypeImage: contentNodeTypeImage,
                    levelNumber: 0,
                    description: UTILS.encodeHtml(this.editor.getData()),
                    mandateText: $("#PM_Predefined_Text").text(),
                    canDeleted: true,
                    isExistingNode: false,
                    levelClassName: "tv-level1-items"
                };
            } else {
                const jsonData: any = {
                    "planId": this.planId,
                    "nodeId": this.structureNodeId,
                    "description": UTILS.encodeHtml(this.editor.getData()),
                    "nodeName": this.structureOverview,
                    "typeDisplayText": this.structureTitle
                };
                // edit plan - save node
                if (this.planId) {
                    EVENTS.loaderON("#addNewPlanTypeWindow");
                    this.$http.post("../api/PlanningApi/SavePlanningNodeDescription", angular.toJson(jsonData)).then(response => {
                        EVENTS.loaderOFF("#addNewPlanTypeWindow");
                        MODULES.saveConfirmation();
                        this.showEditor = false;
                        this.updateNode();
                        //this.addNewPlanStructureWindow.close();
                        //this.getPlanData(this.planId);
                    }, error => {
                        console.log("err : ", error);
                        EVENTS.loaderOFF("#addNewPlanTypeWindow");
                    });
                } else {
                    // add plan - insert node
                    this.updateNode();
                }
            }

            this.pageChanges = true;

            //$("#planStructureTreeView").data("kendoTreeView").append(newItem, $("#planStructureTreeView .k-item:first").find('ul:eq(0)'));
            //if (tvData.length == 0) {
            //    structureTreeView.append(item);
            //} else {
            if (this.structureUid == "") {
                //structureTreeView.append(item);
                $("#planStructureTreeView").data("kendoTreeView").append(item);
                this.showEditor = false;
                this.planTypeTreeGridData = $("#planStructureTreeView").data("kendoTreeView").dataSource.data().length;
                this.addNewPlanStructureWindow.close();
                $(window).scrollTop(screen.height);
            }
            //}
        }

        updateNode() {
            const structureTreeView = $("#planStructureTreeView").data("kendoTreeView") as kendo.ui.TreeView;
            const dataItem = structureTreeView.dataSource.getByUid(this.structureUid);
            dataItem["contentNodeType"] = this.structureTitleText;
            dataItem["description"] = this.editor.getData();
            dataItem["name"] = this.structureOverview;
            dataItem["typeDisplayText"] = this.structureTitle;
            this.$timeout(() => {
                const tvDatasource = $("#planStructureTreeView").data("kendoTreeView").dataSource.data();
                $("#planStructureTreeView").data("kendoTreeView").setDataSource(new kendo.data.HierarchicalDataSource({
                    data: tvDatasource
                }));
                this.planTypeTreeGridData = $("#planStructureTreeView").data("kendoTreeView").dataSource.data().length;
            }, 10);
            this.addNewPlanStructureWindow.close();
        }

        getLoaderId() {
            let loaderId = "";
            if (this.activeTab === 1) {
                loaderId = "#tabContentId";
            }
            else if (this.activeTab === 2) {
                loaderId = "#planTypeTreeGridList";
            }
            else {
                loaderId = "#planMetaData";
            }
            return loaderId;
        }

        savePlanData() {
            if (this.activeTab == 3) {
                this.saveMetaPlanData();
                return;
            } 

            let loaderId = this.getLoaderId();
            let tvDataSource = $("#planStructureTreeView").data("kendoTreeView").dataSource.data();
            let planTypeDropDown = $("#planTypeDropDown").data("kendoDropDownList").value();
            let planCategoryDropDown = $("#planCategoryDropDown").data("kendoDropDownList").value();
            let planStatusDropDown = $("#planStatusDropDown").data("kendoDropDownList").value();
            let mainOwnerData = $("#mainOwnerData").data("kendoMultiSelect").value();
            let writeAccessData = $("#writeAccessData").data("kendoMultiSelect").value();
            let readAccessData = $("#readAccessData").data("kendoMultiSelect").value();

            let errorMessage = [];
            if (this.planName == "") {
                errorMessage.push($("#planNameTitleValidation").html());
            }

            if (this.planShortName == "") {
                errorMessage.push($("#planNameShortValidation").text());
            }

            if (this.planTypeId == "-1") {
                errorMessage.push($("#planTypeValidation").text());
            }

            if (errorMessage.length > 0) {
                MODULES.errorConfirmation(errorMessage);
                return false;
            }

            let jsonData: any = {
                "planID": null,
                "planTypeID": planTypeDropDown,
                "stateFunds": this.planState,
                "planname": this.planName,
                "shortname": this.planShortName,
                "Status": planStatusDropDown,
                "planCategoryID": planCategoryDropDown,
                "mainOwnerList": mainOwnerData,
                "writeList": writeAccessData,
                "readList": readAccessData,
                "planHelper": {
                    "id": this.planTypeId,
                    "name": this.planTypeName,
                    "allowedContentNodeTypes": this.planContentNodeTypes,
                    "usage": this.planTypeContent,
                    "templateTree": tvDataSource
                },
                //"mainDocument": this.pdfFileContent,
                "mainDocName": this.existingPdfFile,
                "isOldPlan": this.existinPlanModelData,
                "orgList": this.orgTreeViewEditedData,
                "orgId": this.orgId,
                "orgLevel": this.orgLevel,
                "orgVersion": $("#planOrgVersionDropDown").data("kendoDropDownList").value(),
                "treatedCase": this.planTreatedCase,
                "dateProcessed": this.metaYearPicker,
                "linkToCaseFile": this.planLinkCaseFile
            };

            if (this.planId)
                jsonData["planID"] = this.planId;

            // 70387 - Upload pdf document updates
            const data = new FormData();
            data.append("planDataHelper", UTILS.encodeHtml(angular.toJson(jsonData)));
            if (this.existinPlanModelData)
                data.append("files", this.pdfFileContent);

            if (this.planId) {
                EVENTS.loaderON(loaderId);

                this.$http.post("../api/PlanningApi/SaveUpdateplan", data, {
                    transformRequest: angular.identity,
                    headers: { 'Content-Type': undefined }
                }).then(response => {
                    EVENTS.loaderOFF(loaderId);
                    MODULES.saveConfirmation();
                    this.pageChanges = false;
                    this.addEditPlan(this.planId);
                }, error => {
                    console.log("err : ", error);
                    EVENTS.loaderOFF(loaderId);
                });
            } else {
                EVENTS.loaderON(loaderId);
                this.$http.post("../api/PlanningApi/SaveUpdateplan", data, {
                    transformRequest: angular.identity,
                    headers: { 'Content-Type': undefined }
                }).then(response => {
                    let responseData: any = response.data;
                    EVENTS.loaderOFF(loaderId);
                    MODULES.saveConfirmation();
                    this.$timeout(() => {
                        this.redirectToIndex(responseData);
                    }, 100);
                }, error => {
                    EVENTS.loaderOFF(loaderId);
                    ERROR.displayException(error.data, error.status, error.data, "PlanDetails - SavePlanData");
                });
            }
        }

        saveMetaPlanData() {
            let loaderId = this.getLoaderId();
            let externalLinksData = [];
            let metaExternalLinksData = $("#metaExternalLinksTreeView").data("kendoTreeView").dataSource.data();
            if (metaExternalLinksData.length > 0) {
                for (let i = 0; i < metaExternalLinksData.length; i++) {
                    externalLinksData.push({
                        "uniqueId": metaExternalLinksData[i].uniqueId,
                        "title": metaExternalLinksData[i].title,
                        "url": metaExternalLinksData[i].url,
                        "protocol": metaExternalLinksData[i].protocol,
                        "isSelected": metaExternalLinksData[i].isSelected,
                        "items": [],
                        "index": metaExternalLinksData[i].index
                    })
                }
            }

            let jsonData: any = {
                "isContactInformationAccessible": false,
                "isDescriptionAccessible": false,
                "isAttachmentsAccessible": false,
                "isLinksAccessible": false,
                "planId": this.planId,
                "unitResponsible": this.metaUnitResponsible,
                "email": this.metaEmail,
                "phoneno": this.metaPhoneNo,
                "description": [],
                "externalLinks": externalLinksData
            }

            EVENTS.loaderON(loaderId);
            this.$http.post("../api/PlanningApi/SavePlanMetadataInformation", angular.toJson(jsonData)).then(response => {
                EVENTS.loaderOFF(loaderId);
                MODULES.saveConfirmation();
                this.$timeout(() => {
                    this.pageChanges = false;
                    this.getMetaDataDetails();
                }, 100);
            }, error => {
                EVENTS.loaderOFF(loaderId);
                ERROR.displayException(error.data, error.status, error.data, "PlanDetails - saveMetaPlanData");
            });
        }

        redirectToIndex(planId?: string) {
            this.$timeout(() => {
                if (typeof planId != "undefined") {
                    window.location.href = "../Planning/PlanDetails?planId=" + planId;
                } else {
                    window.location.href = "../Planning/PlanView";
                }
            }, 300);
        }

        launchGuidanceTextPopup(planStructureNode: PlanStructureTreeNode) {
            this.guidanceTextWindow = $('#guidanceTextWindow').kendoWindow({
                modal: true,
                actions: ["close"],
                scrollable: true,
                resizable: false,
                draggable: false,
                width: "60%"
            }).data("kendoWindow").center().open() as kendo.ui.Window;
            this.guidanceTextWindow.element.prev().addClass('background-white padding-top15 padding-bottom10');
            this.guidanceTextWindow.element.prev().find('#guidanceTextWindow_wnd_title').html('<img src="../images/kostra_icon_info.png" class="vertical-align-middle"/><span class="padding-left5">Veiledningstekst</span>'); //find(".k-window-title").parent('<span></span>');
            this.guidanceTextWindow.element.prev().find('.k-window-actions').addClass('close-icon-img padding-right10 hand-pointer').append('<span class="padding-left5 vertical-align-bottom close-icon-text">Lukk</span>');
            this.guidanceTextWindow.element.prev().find('.k-window-actions span.close-icon-text').nextAll('span.close-icon-text').remove();
            $("#cancelAddNew, .close-icon-img").click(() => {
                this.guidanceTextWindow.close();
            });

            this.descriptionContentNodeType = planStructureNode.typeDisplayText;
            this.descriptionText = unescape(planStructureNode.description);
            this.descriptionContentNodeTypeImage = planStructureNode.contentNodeTypeImage || "../images/plan_icons_text.svg";

            $("#descriptionText").html("");
            $("#descriptionText").html(this.descriptionText);

        }

        showHideDescEditor(flag: boolean) {
            this.showEditor = flag;
            if (!flag) {
                this.showEditorReadOnlyText = true;
                const editorData: string = this.editor.getData();
                this.readableEditorDescriptionText = MODULES.stripHtmlTags(editorData);
            } else {
                this.showEditorReadOnlyText = false;
            }
        }

        openInfoPopup() {
            this.infoPopupDetailDialog = $('#myPlanInfoDetailWindow').kendoWindow({
                modal: true,
                actions: ["close"],
                scrollable: true,
                resizable: false,
                draggable: false,
                width: "50%"
            }).data("kendoWindow").center().open() as kendo.ui.Window;
            this.infoPopupDetailDialog.element.prev().remove();
            this.infoPopupDetailDialog.element.parent().addClass('border0 plan-banner-background');
            $("#closeInfoBox").click(() => {
                this.infoPopupDetailDialog.close();
            });
        }

        tabsClick(activeTabNum: number) {
            $('#plansTabWrapper a').on('focus focusout blur', function () {
                $('#plansTabWrapper a').css('box-shadow', 'none');
            });

            this.activeTab = activeTabNum;
            let queryParm = '';
            if (activeTabNum === 1) {
                queryParm = 'settings';
            }
            else if (activeTabNum === 2) {
                queryParm = 'structure';
            }
            else {
                queryParm = 'metadata';
            }
            let newurl = window.location.protocol + "//" + window.location.host + window.location.pathname + '?planId=' + this.planId + '&t=' + queryParm;
            setTimeout(() => {
                window.history.pushState({ path: newurl }, '', newurl);
            },100)
        }

        downloadPdfFile() {
            if (this.planId)
                window.location.href = "../api/PlanningApi/MainDocument?planID=" + this.planId;
        }

        deleteDocument() {
            let isEditPlan = false;
            if (this.planId && this.isDocumentDeleted == false)
                isEditPlan = true;

            if (isEditPlan == false) {
                const uploadedFile = $("#planUploadPdf").data("kendoUpload") as kendo.ui.Upload;
                $(".k-upload-files").remove();
                $(".k-upload-status").remove();
                $(".k-upload.k-header").addClass("k-upload-empty");
                this.existingPdfFile = "";
                $("#planPdfFileName").html("");
                setTimeout(() => {
                    this.applyPageChanges();
                }, 200);
                return;
            }

            this.deleteDocumentWindow = $('#deletePlanWindow').kendoWindow({
                modal: true,
                actions: ["close"],
                scrollable: true,
                resizable: false,
                draggable: false,
                width: "40%"
            }).data("kendoWindow").center().open() as kendo.ui.Window;
            this.deleteDocumentWindow.element.prev().remove();

            $("#deleteCancel").click(() => {
                this.deleteDocumentWindow.close();
            });

            $("#deleteOk").unbind("click").click(() => {
                // remove from backend for existing nodes
                const jsonData = {
                    planID: this.planId
                };
                EVENTS.loaderON("#planLoaderId");
                this.$http.post("../api/PlanningApi/DeleteDocument/?planID=" + this.planId, angular.toJson(jsonData)).then(response => {
                    EVENTS.loaderOFF("#planLoaderId");
                    MODULES.deleteConfirmation();
                    this.pageChanges = true;
                    this.isDocumentDeleted = true;
                    this.existingPdfFile = "";
                    $("#uploadFileBtn").removeClass("not-active");
                    this.deleteDocumentWindow.close();
                    this.getPlanData(this.planId);

                }, error => {
                    console.log("err : ", error);
                    EVENTS.loaderOFF("#planLoaderId");
                });
            });
        }

        searchTreeViewNode(treeData, childNodeID) {
            let t = $("#planStructureTreeView").data("kendoTreeView");
            for (let i = 0; i < treeData.length; i++) {
                if (treeData[i].childNodeID == childNodeID) {
                    return treeData[i];
                } else if (treeData[i].items.length > 0) {
                    let result = null;
                    result = this.searchTreeViewNode(treeData[i].items, childNodeID);
                    if (result) {
                        return result;
                    }
                }
            }
            return null;
        }

        releventLinksProtocolChange(event) {
            this.metaExternalProtocol = event.sender.selectedIndex;
        }

        getMetaDataDetails() {

            this.$http.get<any>("../api/PlanningApi/GetPlanMetadataInformation?planId=" + this.planId).then(result => {
                
                //const response = { "planId": "cd4cd540-0795-4db0-8c18-f4f516b15157", "unitResponsible": "JoAudun", "email": "<EMAIL>", "telefon": "93939398", "isContactInformationAccessible": true, "isDescriptionAccessible": true, "isAttachmentsAccessible": true, "isLinksAccessible": true, "description": [{ "title": "Planinformasjon", "infoText": "Information guide", "descriptionId": "4d76412e-7e54-4809-90b6-6b34467f91ae", "descriptionData": "Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisienim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor inhendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blanditpraesent luptatum zzril delenit augue duis dolore te feugait nulla facilis", "infoTextHeader": "Veiledningstekst" }, { "title": "User defined 1", "infoText": "User1 - Information guide", "descriptionId": "51e1e68a-0795-4db0-8c18-f4f516b15157", "descriptionData": "User defined text - 1", "infoTextHeader": "Veiledningstekst" }, { "title": "User defined 2", "infoText": "User2 - Information guide", "descriptionId": "cd4cd540-f94b-4c76-b01d-1bea5c7a1146", "descriptionData": "User defined text - 2", "infoTextHeader": "Veiledningstekst" }], "externalLinks": [{ "uniqueId": "00000000-0000-0000-0000-000000000002", "title": "test fr", "url": "framsikt.no", "protocol": 0, "isSelected": false, "items": [], "index": 0 }, { "uniqueId": "00000000-0000-0000-0000-000000000003", "title": "goog", "url": "google.no", "protocol": 0, "isSelected": true, "items": [], "index": 1 }] };
                const response = result.data;
                this.isContactInformationAccessible = response.isContactInformationAccessible;
                this.isAttachmentsAccessible = response.isAttachmentsAccessible;
                this.isLinksAccessible = response.isLinksAccessible;

                this.metaUnitResponsible = response.unitResponsible;
                this.metaEmail = response.email;
                this.metaPhoneNo = response.phoneno;

                const planMetaInformationTitle = $('#planMetaInformationTitle').text();
                const commonAddEditComments = localStorage.getItem('commonAddEditComments');
                const commonHistoryText = localStorage.getItem('commonHistoryText');
                $('#metaTextEditors').html("");
                if (response.description.length > 0) {
                    angular.forEach(response.description, (value, i) => {
                        const { title, infoText, infoTextHeader, descriptionId: descId, descriptionData: descData } = value;
                        const cls = (i > 0) ? "top30" : "";
                        const editorHtml = `<div class="row ${cls}" id="metaTextEitorSec${i}">
                                                <div class="border-bottom padding-bottom10 lato-semi">
                                                    <span class="font16 padding-right5">${title}</span>
                                                    <img src="../images/plan_info_icon-01.svg" class="icon-name hand-pointer vertical-align-bottom" alt="Info Icon" width="19" ng-click="vm.getEditorInfoTooltip({'header': '${infoTextHeader}', 'infoText': '${infoText}'})"/>
                                                </div>
                                                <div id="metaTextEditorHiddenContent${i}" class="cmn-display-none">${descData}</div>
                                                <div class="col-md-12 padding5 top10 font14" style="max-height:100px;overflow:hidden;" id="metaTextEditorContent${i}"></div>
                                                <div class="col-md-12 bottom20 padding0" id="metaDataDescTextEditorWrapper${i}">
                                                    <div id="metaDataDescTextEditorContentHidden" class="cmn-display-none"></div>
                                                    <div class="col-md-12 padding0 cmn-width-94p projectWarning font14 bottom10" id="metaDataDescDescHidden"></div>
                                                    <div class="col-md-12 padding0 border-bottom padding-bottom5" id="metaDataDescTextEditorContentTitle">
                                                        <span id="metaDataDescEditor-${i}" role="link" tabindex="0" class="text-color1 hand-pointer">
                                                            <img src="../images/Edit_CK5.svg" class="padding-right3 icons-height-width" alt="New Comment" />
                                                            <span class="padding-right5 add-new-comment font14 lato-semi">${commonAddEditComments}</span>
                                                        </span>
                                                        <a data-role="tooltip" id="metaDataDescHistory-${i}" class="hand-pointer padding-left10" tabindex="0">
                                                            <img src="../images/History_CK5.svg" class="padding-right3 icons-height-width18" alt="History"/>
                                                            <span class="font14 lato-semi font14">${commonHistoryText}</span>
                                                            <img src="../images/plan_icons_expand.svg" alt="Expand all" class="padding-right3 icons-height-width">
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>`;

                        $('#metaTextEditors').append(editorHtml);

                        const elem = $("#metaTextEditors");
                        this.$compile(elem)(this.$scope);

                        const getHeight = MODULES.checkEditorHeight("#metaTextEditorHiddenContent" + i, descData);

                        TYPESCRIPTMODULES.setCollaborativeEditorHTML(descData, getHeight, "#metaTextEditorContent" + i, "#metaTextEditorContent-" + i);


                        // Editor events
                        $("#metaDataDescEditor-" + i).unbind('click').click((e) => {
                            const id = $(e.target).parent().attr("id").split("-")[1];
                            const descId = response.description[id].descriptionId;
                            const saveInputParams = {
                                "descriptionId": descId,
                                "title": response.description[id].title,
                                "infoText": response.description[id].infoText,
                                "infoTextHeader": response.description[id].infoTextHeader,
                                "fkPlanMetaDataTextId": response.description[id].fkPlanMetaDataTextId,
                                "descriptionData": response.description[id].descriptionData,
                                "planId": this.planId
                            }
                            // metadata collaborative editor
                            TYPESCRIPTMODULES.getCollaborativeEditor(response.description[id].descriptionData, this.ckEditor5WebSocketURL, descId, 321, saveInputParams, "#metaDataDescEditor" + id, "#metaTextEditorHiddenContent" + id, true);
                        });

                        // History events
                        $('#metaDataDescHistory-' + i + ' img, #metaDataDescHistory-' + i + ' span').on('click', (e) => {
                            const id = $(e.target).parent().attr("id").split("-")[1];
                            const descId = response.description[id].descriptionId;
                            MODULES.historyTooltipClick('metaDataDescHistory-' + id, '', response.description[id].title, descId);
                        });
                    });
                }

                this.metaExternalLinksData = response.externalLinks;

                this.$scope.metaExternalLinksTreeViewOptions = {
                    dataSource: this.metaExternalLinksData,
                    autoScroll: true,
                    template: kendo.template($("#metaExternalLinksTreeViewTemplate").html()),
                    dragAndDrop: true,
                    drag: (e: kendo.ui.TreeViewDragEvent) => {
                        //console.log("drag : ", e);
                        if (e.statusClass == "i-plus") {
                            e.setStatusClass("k-denied");
                        }
                    },
                    drop: (e: kendo.ui.TreeViewDropEvent) => {
                        //console.log("drop : ", e);
                        const treeview = $("#metaExternalLinksTreeView").data("kendoTreeView");
                        const sourceDataItem: any = treeview.dataItem(e.sourceNode);
                        const destDataItem: any = treeview.dataItem(e.destinationNode);
                        const position = e.dropPosition;

                        const parentSourceDataItem = treeview.dataItem(treeview.parent(e.sourceNode));
                        const parentDestDataItem = treeview.dataItem(treeview.parent(e.destinationNode));

                        //console.log("source : ", sourceDataItem, "\ndest : ", destDataItem, "\nparentsource : ", parentSourceDataItem, "\nparentdesc :", parentDestDataItem, "\nposition : ", position);
                        if ((typeof parentSourceDataItem !== "undefined" && typeof parentDestDataItem !== "undefined") || position == "over") {
                            e.preventDefault();
                        }
                        this.pageChanges = true;
                    },
                    dragstart: (e: kendo.ui.TreeViewDragstartEvent) => {

                    },
                    dragend: (e: kendo.ui.TreeViewDragendEvent) => {
                        // for treeview alignment
                        setTimeout(() => {
                            $('#metaExternalLinksTreeView .k-item').removeClass('border-top0 border-bottom');
                            $('#metaExternalLinksTreeView .k-item.k-first').addClass('border-top0');
                            $('#metaExternalLinksTreeView .k-item.k-last').addClass('border-bottom');
                        }, 100);
                    },
                    dataBound: () => {
                        //$('#metaExternalLinksTreeView').find('.k-in.k-link').remove();
                        $("#metaExternalLinksTreeView").removeAttr('tabindex');
                        $('#metaExternalLinksTreeView').find('.k-in.k-link').removeAttr("href").addClass("padding-left0");
                        $('#metaExternalLinksTreeView .k-item.k-first').addClass('border-top0');
                        $('#metaExternalLinksTreeView .k-item').addClass('padding-left0');
                        $('#metaExternalLinksTreeView .k-item.k-last').addClass('border-bottom');
                        tooltipSter(".external-links-delete");
                        tooltipSter(".external-links-move");
                    }
                };

                // kendo treeview dragclue
                kendo.ui.TreeView.fn._hintText = function (node) {
                    let template = kendo.template($("#metaexternallinks-treeview-drag-template").html());
                    let result = template({
                        item: this.dataItem(node)
                    });
                    return result;
                };

                kendo.ui.TreeView.fn.focus = function () {
                    let wrapper = this.wrapper,
                        scrollContainer = wrapper[0],
                        containers = [],
                        offsets = [],
                        documentElement = document.documentElement,
                        i;

                    do {
                        scrollContainer = scrollContainer.parentNode;

                        if (scrollContainer.scrollHeight > scrollContainer.clientHeight) {
                            containers.push(scrollContainer);
                            offsets.push(scrollContainer.scrollTop);
                        }
                    } while (scrollContainer != documentElement);

                    for (i = 0; i < containers.length; i++) {
                        containers[i].scrollTop = offsets[i];
                    }
                };
                EVENTS.loaderOFF("#planMetaData");
            }, error => {
                EVENTS.loaderOFF("#planMetaData");
                error.methodName = "Plan Meta - getMetaDataDetails";
                console.log("err : ", error);
            });
        }

        getEditorInfoTooltip(info) {
            this.guidanceTextWindow = $('#guidanceTextWindow').kendoWindow({
                modal: true,
                actions: ["close"],
                scrollable: true,
                resizable: false,
                draggable: false,
                width: "60%"
            }).data("kendoWindow").center().open() as kendo.ui.Window;
            this.guidanceTextWindow.element.prev().addClass('background-white padding-top15 padding-bottom10');
            this.guidanceTextWindow.element.prev().find('#guidanceTextWindow_wnd_title').html('<img src="../images/kostra_icon_info.png" class="vertical-align-middle"/><span class="padding-left5">' + info.header +'</span>');
            this.guidanceTextWindow.element.prev().find('.k-window-actions').addClass('close-icon-img padding-right10 hand-pointer').append('<span class="padding-left5 vertical-align-bottom close-icon-text">Lukk</span>');
            this.guidanceTextWindow.element.prev().find('.k-window-actions span.close-icon-text').nextAll('span.close-icon-text').remove();
            $("#cancelAddNew, .close-icon-img").click(() => {
                this.guidanceTextWindow.close();
            });

            this.descriptionContentNodeType = "";
            this.descriptionText = unescape(info.infoText);
            this.descriptionContentNodeTypeImage = "";

            $("#descriptionText").html("");
            $("#descriptionText").html(this.descriptionText);
        }

        planMetaSaveEditorData(saveInput, closeFlag, autoSaveFlag) {
            if (!autoSaveFlag) {
                EVENTS.loaderON("#collaborativeEditorWindow");
            }

            this.$http.post("../api/PlanningApi/SavePlanMetadataDescriptionText", angular.toJson(saveInput)).then(response => {
                
                if (!closeFlag) {
                    EVENTS.loaderOFF("#metaTextEditors");
                }
                if (!autoSaveFlag) {
                    EVENTS.loaderOFF("#collaborativeEditorWindow");
                    MODULES.saveConfirmation();
                }
                if (closeFlag && $("#collaborativeEditorWindow").data("kendoWindow")) {
                    $("#collaborativeEditorWindow").data("kendoWindow").close();
                }
            }, error => {
                ERROR.displayException(error.data, error.status, error.data, 'planMetaSaveEditorData');
                if (!autoSaveFlag) {
                    EVENTS.loaderOFF("#collaborativeEditorWindow");
                }
            });
        }

        addEditExternalLink(uniqueId: string, title: string, url: string, protocol: number, editFlag: boolean) {
            this.metaExternalProtocol = protocol;
            this.metaExternalUrl = url;
            this.metaExternalTitle = title;
            this.metaExternalLinksGuid = uniqueId.toString();
            this.metaExternalLinkEdit = editFlag;
            this.pageChanges = true;
        }

        updateExternalUrl(url: string) {
            this.metaExternalUrl = url;
        }

        updateExternalTitle(title: string) {
            this.metaExternalTitle = title;
        }

        externalProtocolChange(event) {
            this.metaExternalProtocol = event.sender.selectedIndex;
        }

        isUrlValid(userInput) {
            var res = userInput.match(/^(https?:\/\/)(www\.)?[a-zA-Z0-9-]{2,256}\.[a-z]{2,63}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)$/);
            if (res == null)
                return false;
            else
                return true;
        }

        createExternalLink() {
            let validateUrl = this.isUrlValid(this.metaExternalUrl);
            if (validateUrl == false) {
                let validationMsg = [$('#PlanPublishURLValidation').text()];
                MODULES.errorConfirmation(validationMsg);
                return false;
            }
            if (this.metaExternalUrl == "") {
                let validationMsg = [$('#PlanPublishMandatoryURL').text()];
                MODULES.errorConfirmation(validationMsg);
                return false;
            }
            if (this.metaExternalTitle == "") {
                let validationMsg = [$('#PlanPublishMandatoryTitle').text()];
                MODULES.errorConfirmation(validationMsg);
                return false;
            }

            this.addNewMetaExternalUrl = true;
            for (let i = 0; i < this.metaExternalLinksData.length; i++) {
                if (this.metaExternalLinksData[i].uniqueId == this.metaExternalLinksGuid) {
                    this.metaExternalLinksData[i] = {
                        "uniqueId": this.metaExternalLinksGuid,
                        "title": this.metaExternalTitle,
                        "url": this.metaExternalUrl,
                        "protocol": this.metaExternalProtocol,
                        "isSelected": true,
                        "items": []
                    };
                    this.addNewMetaExternalUrl = false;
                }
                else {
                    this.metaExternalLinksData[i].isSelected = false;
                }
            }

            const dsLength = ($('#metaExternalLinksTreeView').data('kendoTreeView')) ? $('#metaExternalLinksTreeView').data('kendoTreeView').dataSource.data().length : 0;
            //if (dsLength === 5 && this.addNewMetaExternalUrl === true) {
            //    let validationMsg = [$('#PlanPublishMaximumLinksLimit').text()];
            //    MODULES.errorConfirmation(validationMsg);
            //    return false;
            //} else {
                if (this.metaExternalLinksGuid === '-1') {
                    let extGuid = 0; //with one less zero from last
                    //let customGuid = extGuid.concat((this.addNewCustomIdCounter).toString());
                    let customGuid = (this.metaExternalLinksData.length > 0) ? this.metaExternalLinksData.reduce((prev, current) => (parseInt(prev.uniqueId) > parseInt(current.uniqueId)) ? prev : current) : {};
                    if (Object.keys(customGuid).length > 0) {
                        extGuid = parseInt(customGuid.uniqueId) + 1;
                    } 
                    
                    for (let i = 0; i < this.metaExternalLinksData.length; i++) {
                        if (this.metaExternalLinksData[i].url == this.metaExternalUrl) {
                            this.urlValidationCheck(this.metaExternalLinksData[i].url);
                            return false;
                        }
                    }
                    this.metaExternalLinksData[this.metaExternalLinksData.length] = {
                        "uniqueId": extGuid.toString(),
                        "title": this.metaExternalTitle,
                        "url": this.metaExternalUrl,
                        "protocol": this.metaExternalProtocol,
                        "isSelected": true,
                        "items": []
                    };
                    this.addNewCustomIdCounter++;
                }

                $("#metaExternalLinksTreeView").data("kendoTreeView").setDataSource(new kendo.data.HierarchicalDataSource({
                    data: this.metaExternalLinksData
                }));

                this.pageChanges = true;
            //}
            
            this.clearExternalLink();
        }

        urlValidationCheck(url) {
            let existingURL = this.regxFilterUrl(url);
            let currURL = this.regxFilterUrl(this.metaExternalUrl);
            let foundText = [];
            if (existingURL == currURL) {
                foundText.push(this.metaExternalUrl + " - " + $('#duplicationUrlCheck').text());
                MODULES.errorConfirmation(foundText);
                return false;
            }
        }

        regxFilterUrl(url) {
            return url.replace(/^(?:https?:\/\/)?(?:www\.)?/i, "").split('/')[0];
        }

        clearExternalLink() {
            this.metaExternalProtocol = 0;
            this.metaExternalUrl = '';
            this.metaExternalTitle = '';
            this.metaExternalLinksGuid = "-1";
            this.addNewMetaExternalUrl = true;
        }

        deleteExternalLink(id: string) {
            for (let i = 0; i < this.metaExternalLinksData.length; i++) {
                if (this.metaExternalLinksData[i].uniqueId == id) {
                    this.metaExternalLinksData.splice(i, 1);
                }
            }

            $("#metaExternalLinksTreeView").data("kendoTreeView").setDataSource(new kendo.data.HierarchicalDataSource({
                data: this.metaExternalLinksData
            }));
            this.pageChanges = true;
        }
        
        getDownLoadListGridData() {
            EVENTS.loaderON("#downLoadListGrid");
            const jsonData = {
                "moduleId": this.uploadModuleId,
                "pageId": this.uploadPageId,
                "budgetYear": 2020,
                "parentId": this.planId
            };

            this.$scope.downLoadListGridOptions = {
                dataSource: {
                    data: []
                },
                dataBound: (ev) => {
                    $("#downLoadListGrid tr:first-child td").addClass('border-top0');
                    $("#downLoadListGrid tr td").addClass('font14');
                    $("#downLoadListGrid tr th").addClass('font15');
                    $("#downLoadListGrid tr td:first-child, #downLoadListGrid tr th:first-child").addClass('padding-left0');
                },
                columns: [],
                sortable: false,
                navigatable: true,
                scrollable: false
            }
            this.$http.post<any>("../api/InvestmentProjectApi/GetAttachments", angular.toJson(jsonData)).then(response => {
                const responseData = response.data
                this.exisitingFileId = responseData.data;
                this.displayAttachementItems = (responseData.data.length > 0) ? true : false;
                this.$timeout(() => {
                    $("#downLoadListGrid").data("kendoGrid").setOptions({ columns: responseData.columns });
                    $("#downLoadListGrid").data("kendoGrid").setDataSource(new kendo.data.DataSource({
                        data: responseData.data
                    }));

                    EVENTS.loaderOFF("#downLoadListGrid");
                }, 100);

            }, error => {
                EVENTS.loaderOFF("#downLoadListGrid");
                error.methodName = "getDownLoadListGridData";
                ERROR.displayException(error.data, error.status, error.data, error.methodName);
            });
        }

        downloadFPDocumentRow(item, itemId) {
            window.open("../api/InvestmentProjectApi/DownloadAttachment?attachmentId=" + itemId, '_blank');
        }

        deleteDocumentRow(item, itemId) {
            this.deleteAttachementWindow = <kendo.ui.Window>$("#metaDeleteAttachmentConfirmation").kendoWindow({
                width: "600px",
                title: localStorage.getItem("Confirm"),
                resizable: false,
                modal: true
            }).data("kendoWindow").center().open();

            $(".meta-del-attachment-confirm-yes").unbind().click(() => {
                EVENTS.loaderON("#downLoadListGrid");
                this.deleteAttachementWindow.close();
                this.$http.get<any>("../api/InvestmentProjectApi/DeleteAttachment?attachmentId=" + itemId).then(response => {
                    MODULES.saveConfirmation();
                    this.getDownLoadListGridData();
                }, error => {
                    EVENTS.loaderOFF("#downLoadListGrid");
                    error.methodName = "deleteDocumentRow";
                    ERROR.displayException(error.data, error.status, error.data, error.methodName);
                });

            });

            $(".meta-del-attachment-confirm-no").click(() => {
                this.deleteAttachementWindow.close();
                return false;
            });
        }

        initializeDocumentUpload() {
            let fileName = '';
            let extension = '';
            const uploadURL = "../api/InvestmentProjectApi/UploadAttachment?fileName=" + fileName + "&extension=" + extension + "&moduleId=" + this.uploadModuleId + "&pageId=" + this.uploadPageId + "&budgetYear=2020&parentId=" + this.planId;
            if ($("#uploadAttachments").data("kendoUpload")) {
                $("#uploadAttachments").data("kendoUpload").options.async.saveUrl = uploadURL;
            }

            $("#uploadAttachments").kendoUpload({
                async: {
                    saveUrl: uploadURL,
                    autoUpload: false
                },
                upload: (e: any) => {
                    for (let itr = 0; itr < e.files.length; itr++) {
                        fileName = e.files[itr].name;
                        let filenameWithoutExtention = fileName.split('.').slice(0, -1).join(".");
                        extension = e.files[itr].extension;
                        e.sender.options.async.saveUrl = "../api/InvestmentProjectApi/UploadAttachment?fileName=" + filenameWithoutExtention + "&extension=" + extension + "&moduleId=" + this.uploadModuleId + "&pageId=" + this.uploadPageId + "&budgetYear=2020&parentId=" + this.planId;
                    }
                    //added to handle XSRF header information to kendo upload files
                    MODULES.setKendoUploadRequestHeaders(e);
                    EVENTS.loaderON("#uploadAttachmentWrapper");
                    this.formatUploadFileButton();
                },
                multiple: false,
                select: (e: any) => {
                    $.each(e.files, function (index, value) {
                        setTimeout(function () {
                            EVENTS.loaderON("#uploadAttachmentWrapper");
                            $("#uploadAttachmentWrapper").find(".k-upload-selected").click(); //after selection code to upload
                        }, 100);
                    });
                    this.formatUploadFileButton();
                },
                success: (e: any) => {
                    EVENTS.loaderOFF("#uploadAttachmentWrapper");
                    this.getDownLoadListGridData();
                    if (e.response) {
                        let getURL = '../Content/DownloadExcel';
                        if (e.response.fName) {
                            window.location.href = getURL + "?fName=" + e.response.fName;
                        }
                    }

                    this.formatUploadFileButton();
                    $("#uploadAttachmentWrapper").find('.k-upload').removeClass('opacity-75');
                },
                error: (e: any) => {
                    setTimeout(function () {
                        $("#uploadAttachmentWrapper").find('.k-upload').removeClass('opacity-75');
                    }, 100);
                    EVENTS.loaderOFF("#uploadAttachmentWrapper");
                }
            });
            setTimeout(() => {
                this.formatUploadFileButton();
            }, 200);

        }

        formatUploadFileButton() {
            const sectionID = "#uploadAttachmentWrapper";
            const importID = "#uploadAttachments";
            $(sectionID).find('.k-dropzone').attr('class', 'fp-import-section');
            $(sectionID).find('.fp-import-section').find('em').remove();
            $(sectionID).find('.fp-import-section').find('.k-upload-button').addClass('no-border padding0');
            $(importID).next().text('');
            $(importID).next().html('<a class="font16" tabindex="0"><span class="font18 padding-right3">+</span>' + $('#uploadAttachmentText').text() + '</a>');
            $(sectionID).find('.k-upload-empty').addClass('fm-upload-height-auto no-border');
            $(sectionID).find('.k-upload').addClass('fm-upload-height-auto');
            $(sectionID).find('.k-upload').addClass('no-border min-width-1 opacity-1-75');
            this.isEnableUploadFile = true;
            $(importID).removeClass('cmn-display-none');
            $('#uploadAttachmentText').addClass("cmn-display-none");
        }

        downloadAllAttachemnts() {
            for (let itrI = 0; itrI < this.exisitingFileId.length; itrI++) {
                setTimeout(() => {
                    this.downloadFPDocumentRow('', this.exisitingFileId[itrI].fileId);
                }, 500);
            }
        }
    }

    angular.module("PlanDetailApp", ["kendo.directives"]).config(['$httpProvider', function ($httpProvider) {
        $httpProvider.interceptors.push(() => {
            return {
                'request': function (config) {
                    return MODULES.validateHttpRequest(config);
                }
            };
        });
    }])
        .controller("planDetailController", ['$scope', '$http', '$q', '$location', '$timeout', '$compile', function ($scope, $http, $q, $location, $timeout, $compile) {
            $("body").addClass('plan-content-background');
            return new MyPlanDetail($scope, $http, $q, $location, $timeout, $compile);
        }]);

}



