declare var EVENTS: any;
declare var MODULES: any;
declare var app: any;
module PlanningStrategyAdmin {
    interface IPlanningStrategyAdminScope extends ng.IScope {
        vm: PlanningStrategyAdmin;
        structureTreeviewOptions: kendo.ui.TreeViewOptions;
    }

    type PlanType = {
        id: string;
        name: string;
        usage: string;
        allowedContentNodeTypes: string[];
        templateTree: PlanStructureTreeNode[];
        isDesignedPerLaw: boolean;
    }

    type PlanStructureTreeNode = {
        uid?: string;
        name?: string;
        items?: PlanStructureTreeNode[];
        levelNumber?: number;
        moveRows?: string;
        contentNodeTypeImage?: string;
        contentNodeType: string;
        description?: string;
        typeDisplayText: string;
        levelClassName?: string;
    }

    type PlanStructure = {
        uid?: string;
        planId?: string;
        key?: string;
        overview?: string;
        description?: string;
        childNodeID?: string;
        contentNodeType: string;
        typeDisplayText: string;
    }

    //functions for localStorage

    export interface IStorageItem {
        key: string;
        value: any;
    }

    export class StorageItem {
        key: string;
        value: any;

        constructor(data: IStorageItem) {
            this.key = data.key;
            this.value = data.value;
        }
    }

    export class LocalStorageWorker {
        localStorageSupported: boolean;

        constructor() {
            this.localStorageSupported = typeof window['localStorage'] != "undefined" && window['localStorage'] != null;
        }

        // add value to storage
        add(key: string, item: string) {
            if (this.localStorageSupported) {
                localStorage.setItem(key, item);
            }
        }

        // get all values from storage (all items)
        getAllItems(): Array<StorageItem> {
            var list = new Array<StorageItem>();

            for (var i = 0; i < localStorage.length; i++) {
                var key = localStorage.key(i);
                var value = localStorage.getItem(key);

                list.push(new StorageItem({
                    key: key,
                    value: value
                }));
            }
            return list;
        }

        // get only all values from localStorage
        getAllValues(): Array<any> {
            var list = new Array<any>();
            for (var i = 0; i < localStorage.length; i++) {
                var key = localStorage.key(i);
                var value = localStorage.getItem(key);

                list.push(value);
            }
            return list;
        }

        // get one item by key from storage
        get(key: string): string {
            if (this.localStorageSupported) {
                var item = localStorage.getItem(key);
                return item;
            } else {
                return null;
            }
        }

        // remove value from storage
        remove(key: string) {
            if (this.localStorageSupported) {
                localStorage.removeItem(key);
            }
        }

        // clear storage (remove all items from it)
        clear() {
            if (this.localStorageSupported) {
                localStorage.clear();
            }
        }
    };

    // function to apply tooltipSter styling
    export const tooltipSter = (className: string | JQuery<HTMLElement>): void => {
        (<any>$(className)).tooltipster({
            trigger: 'hover',
            side: 'bottom',
            distance: 0
        });
        $(className)
            .focus(function () {
                (<any>$(this)).tooltipster('open');
            })
            .focusout(function () {
                (<any>$(this)).tooltipster('close');
            })
            .blur(function () {
                (<any>$(this)).tooltipster('close');
            });
    };

    class PlanningStrategyAdmin {
        vm: PlanningStrategyAdmin;
        planContentTypes: { "Value": string, "Key": string }[] = [];
        planTypeTreeGridData = 0;
        filteredContents = [];
        addNewPlanStructureWindow: kendo.ui.Window;
        readableEditorDescriptionText = "";
        editor: any;
        structureOverview = "";
        structureTitle = "";
        structureTitleText = "";
        structureDescription = "";
        structureUid = "";
        structureNodeId = "";
        showEditorReadOnlyText = true;
        showEditor = false;
        planTypeId = "00000000-0000-0000-0000-000000000000";
        planContentNodeTypesDefault = [];
        guidanceTextWindow: kendo.ui.Window;
        descriptionContentNodeType = "";
        descriptionContentNodeTypeImage = "";
        descriptionText = "";
        deleteConfirmWindow: kendo.ui.Window;
        pageChanges = false;
        editorKey = "";

        constructor(private $scope: IPlanningStrategyAdminScope, private $http: ng.IHttpService, public $promise: ng.IPromise<any>, public $location: ng.ILocationService, public $timeout: ng.ITimeoutService, public $sce: ng.ISCEService, public $compile: ng.ICompileService) {
            this.vm = this;
            this.getPlanType();
            this.getEditorConfigInfo();
        }
        getPlanType() {
            this.$http.get("../api/PlanAdminApi/PlanContentTypes/true").then(response => {
                let responseData: any = response.data;
                this.planContentTypes = responseData;
                for (let i = 0; i < this.planContentTypes.length; i++) {
                    this.filteredContents.push({ "contentNodeType": this.planContentTypes[i].Key, "typeDisplayText": this.planContentTypes[i].Value });
                    this.planContentNodeTypesDefault.push(this.planContentTypes[i].Key);
                }
                this.initializeTreeView();
            }, error => {
                console.log("contentnodetypes err : ", error);
            });
        }
        getEditorConfigInfo() {
            // collaborative editor config urls
            this.$http.get("../content/getcsurls").then(response => {
                let responseData: any = response.data;
                this.editorKey = responseData.key;
            }, error => {
                error.methodName = "getEditorConfigInfo";
                ERROR.displayException(error.data, error.status, error.data, error.methodName);
            });
        }

        initializeTreeView() {
            let disableLoader = false;
            let treeviewJson = { "Treedata": [] };

            this.planTypeTreeGridData = treeviewJson.Treedata.length;
            this.$scope.structureTreeviewOptions = {
                dataSource: treeviewJson.Treedata,
                dataTextField: "text",
                autoScroll: true,
                template: kendo.template($("#planStructureTreeviewTemplate").html()),
                dragAndDrop: true,
                //loadOnDemand: false,
                dragend: (e: kendo.ui.TreeViewDragendEvent) => {
                    const treeview = $("#planStructureTreeView").data("kendoTreeView");
                    const treeviewDS = treeview.dataSource.data();
                    const destDataItem: any = treeview.dataItem(e.destinationNode);
                    const parentDestDataItem: any = treeview.dataItem(treeview.parent(e.destinationNode));
                    const position = e.dropPosition;
                    if (destDataItem) {
                        if (position === "over") {
                            localStorage.setItem("MyPlanDetailChildNodeID", destDataItem.childNodeID);
                        } else if ((position == "before" || position == "after") && parentDestDataItem) {
                            localStorage.setItem("MyPlanDetailChildNodeID", destDataItem.childNodeID);
                        }
                        this.getPlanningStrategyType(true);
                    }

                    this.$timeout(() => {
                    }, 200);
                },
                drag: (e) => {
                    disableLoader = false;
                    if (e.statusClass == "i-cancel" || e.statusClass == "k-i-cancel") {
                        disableLoader = true;
                    }
                    let eleTop = e.pageY;
                    let treeScrollTop = $("#planStructureTreeView").scrollTop();
                    let treeTop = $("#planStructureTreeView").offset().top;
                    $("#planStructureTreeView").animate({
                        scrollTop: (treeScrollTop + eleTop) - treeTop
                    });
                },
                drop: (e: kendo.ui.TreeViewDropEvent) => {
                    // Handling customized treeview template design according to levelNumber
                    const treeview = $("#planStructureTreeView").data("kendoTreeView");
                    const sourceDataItem: any = treeview.dataItem(e.sourceNode);
                    const destDataItem: any = treeview.dataItem(e.destinationNode);
                    const position = e.dropPosition;

                    if (typeof e.destinationNode === "undefined") {
                        e.preventDefault();
                        return false;
                    }

                    if (typeof destDataItem === "undefined") {
                        e.preventDefault();
                    }

                    const parentSourceDataItem = treeview.dataItem(treeview.parent(e.sourceNode));
                    const parentDestDataItem = treeview.dataItem(treeview.parent(e.destinationNode));

                    if (typeof parentDestDataItem == "undefined") {
                        sourceDataItem.levelNumber = 0;
                    }
                    if (typeof parentSourceDataItem !== "undefined" && typeof parentDestDataItem !== "undefined") {
                        if (parentSourceDataItem["id"] == parentDestDataItem["id"] && (position == "before" || position == "after")) {
                            sourceDataItem.levelNumber = destDataItem.levelNumber;
                        }

                        if (parentSourceDataItem["id"] == parentDestDataItem["id"] && position == "over") {
                            sourceDataItem.levelNumber = destDataItem.levelNumber + 1;
                        }

                        if (parentSourceDataItem["id"] !== parentDestDataItem["id"] && (position == "before" || position == "after")) {
                            sourceDataItem.levelNumber = destDataItem.levelNumber;
                        }

                        if (parentSourceDataItem["id"] == destDataItem["id"] && (position == "before" || position == "after")) {
                            sourceDataItem.levelNumber = destDataItem.levelNumber;
                        }
                    }

                    if (typeof parentSourceDataItem == "undefined" && typeof parentDestDataItem !== "undefined" && position == "over") {
                        sourceDataItem.levelNumber = destDataItem.levelNumber + 1;
                    }

                    if (typeof parentSourceDataItem !== "undefined" && typeof parentDestDataItem == "undefined" && position == "over") {
                        sourceDataItem.levelNumber = destDataItem.levelNumber + 1;
                    }

                    if (typeof parentDestDataItem == "undefined" && typeof parentSourceDataItem == "undefined" && position == "over") {
                        sourceDataItem.levelNumber = destDataItem.levelNumber + 1;
                    }
                    //this.pageChanges = true;
                    if (!disableLoader) {
                        //    EVENTS.loaderON("#planTypeTreeGridList");
                    }
                    // if(typeof parentSourceDataItem !== "undefined" && typeof parentDestDataItem !== "undefined" && position == "")
                },
                dragstart: function (e: kendo.ui.TreeViewDragstartEvent) {
                    //console.log("dragstart :", e);
                },
                dataBound: (e) => {
                    $("#planStructureTreeView").removeAttr('tabindex');
                    $("#planStructureTreeView .tvicons-level1-items").closest('span.k-in').prev('.k-icon').addClass('level1-icons-style');
                    this.$timeout(() => {
                        if (!$(".delete-plan-structure").hasClass("tooltipstered")) {
                            //    tooltipSter(".delete-plan-structure");
                        }
                    }, 100);
                    this.$timeout(() => {
                        if (!$(".chapter-name").hasClass("tooltipstered")) {
                            //    tooltipSter(".chapter-name");
                        }
                    }, 400);
                },
                expand: function (e) {
                    $("#planStructureTreeView .level1-icons-style").closest('li').addClass('treeview-expandbg-style');
                },
                change: (e: kendo.ui.TreeViewEvent) => {
                    // console.log("e : ", e);
                    //    this.pageChanges = true;
                }
            }

            // kendo treeview dragclue
            kendo.ui.TreeView.fn._hintText = function (node) {
                var template = kendo.template($("#treeview-custom-template").html());
                var result = template({
                    item: this.dataItem(node)
                });
                return result;
            };
            this.getPlanningStrategyType(false);
        }

        getPlanningStrategyType(save: boolean) {
            EVENTS.loaderON("#planTypeTreeGridList");
            const jsonData: any = {
                "id": this.planTypeId,
                "name": "",
                "allowedContentNodeTypes": this.planContentNodeTypesDefault,
                "usage": 0, // Confirm
                "templateTree": save ? $("#planStructureTreeView").data("kendoTreeView").dataSource.data() : []
            };
            this.$http.post("../api/PlanAdminApi/CreateUpdatePlanStrategyTemplate", angular.toJson(jsonData)).then(response => {
                EVENTS.loaderOFF("#planTypeTreeGridList");
                let responseData: any = response.data;
                this.planTypeId = responseData;
                this.pageChanges = false;
                if(save) MODULES.saveConfirmation();
                this.getPlanningStratgeyTreeData(this.planTypeId);
            }, error => {
                console.log("CreateUpdatePlanStrategyTemplate err : ", error);
            });
        }

        getPlanningStratgeyTreeData(planTypeId: string) {
            if (planTypeId) {
                EVENTS.loaderON("#planTypeTreeGridList");
                this.$http.get("../api/PlanningApi/GetPlanStrategyTypeStructure/" + planTypeId).then(response => {
                    let responseData: any = response.data;
                    this.$timeout(() => {
                        this.planTypeId = responseData.id;
                        this.planTypeTreeGridData = responseData.templateTree.length;
                        $("#planStructureTreeView").data("kendoTreeView").setDataSource(new kendo.data.HierarchicalDataSource({
                            data: responseData.templateTree
                        }));
                    }, 100);
                   
                    EVENTS.loaderOFF("#planTypeTreeGridList");
                }, error => {
                    console.log("err : ", error);
                    EVENTS.loaderOFF("#structureTabContent");
                    let msg = [localStorage.getItem('ErrorMessage')];
                    MODULES.errorConfirmation(msg);
                });
            }
        }

        addNewPlanStructure(planStructure: PlanStructure) {
            this.addNewPlanStructureWindow = $('#addNewPlanTypeWindow').kendoWindow({
                modal: true,
                actions: ["close"],
                scrollable: true,
                resizable: false,
                draggable: false,
                width: "70%"
            }).data("kendoWindow").center().open() as kendo.ui.Window;
            this.addNewPlanStructureWindow.element.prev().addClass('background-white padding-top15 padding-bottom10');
            this.addNewPlanStructureWindow.element.prev().find('#addNewPlanTypeWindow_wnd_title').html('<span class="padding-left20"><img src="../images/plan_icons_text.svg" class="icons-height-width"/><span class="padding-left5" id="nodeWindowHeaderTitle"></span></span>'); //find(".k-window-title").parent('<span></span>');
            this.addNewPlanStructureWindow.element.prev().find('.k-window-actions').addClass('close-icon-img padding-right10');
            this.addNewPlanStructureWindow.element.parent().css({ 'position': 'fixed', 'top': '0' });
            this.addNewPlanStructureWindow.element.parent().removeAttr('tabindex');
            $('#textFieldHeading').focus();
            $("#cancelAddNew").on('click keypress', () => {
                this.addNewPlanStructureWindow.close();
            });
            this.readableEditorDescriptionText = "";

            if (!this.editor) {
                CKEDITOR5.DocumentEditor
                    .create(document.querySelector('#planningStrategyDetailEditor'), {
                        simpleUpload: {
                            headers: TYPESCRIPTMODULES.getSimpleUplodHeaders()
                        },
                        removePlugins: ['Heading'],
                        licenseKey: this.editorKey,
                        toolbar: {
                            removeItems: ['heading']
                        }
                    })
                    .then(editor => {
                        this.editor = editor;
                        const toolbarContainer = document.querySelector('.document-editor__toolbar_structure');
                        toolbarContainer.appendChild(editor.ui.view.toolbar.element);
                        $('.document-editor__toolbar_structure button').attr('tabindex', '0'); // 74750 - Keyboard navigation
                        TYPESCRIPTMODULES.replaceRemoveFormatIcon(editor);
                        if (typeof planStructure.uid !== "undefined" && planStructure.uid != "") {
                            this.editor.setData(unescape(planStructure.description));
                        }
                        TYPESCRIPTMODULES.editorImageUploadStatus(editor);
                    })
                    .catch(err => {
                        console.error(err);
                    });
            }

            this.structureOverview = "";
            this.structureDescription = "";
            this.structureUid = "";

            if (typeof planStructure.uid !== "undefined" && planStructure.uid != "") {
                // edit plan structure

                this.structureOverview = unescape(planStructure.overview);
                this.structureDescription = unescape(planStructure.description);
                this.structureTitle = planStructure.typeDisplayText;
                this.structureTitleText = planStructure.contentNodeType;
                this.structureUid = planStructure.uid;
                this.structureNodeId = planStructure.childNodeID;
                this.readableEditorDescriptionText = MODULES.stripHtmlTags(unescape(planStructure.description));
                if (this.editor) {
                    this.editor.setData(unescape(planStructure.description));
                }
            } else {
                // add plan structure
                this.structureTitle = planStructure.typeDisplayText;
                this.structureTitleText = planStructure.contentNodeType;
                if (this.editor) {
                    this.editor.setData("");
                    this.readableEditorDescriptionText = "";
                }
            }
            // window header title
            $("#nodeWindowHeaderTitle").html(this.structureTitle);
        }

        showHideDescEditor(flag: boolean) {
            this.showEditor = flag;
            if (!flag) {
                this.showEditorReadOnlyText = true;
                const editorData: string = this.editor.getData();
                this.readableEditorDescriptionText = MODULES.stripHtmlTags(editorData);
            } else {
                this.showEditorReadOnlyText = false;
            }
        }

        insertNewNode() {
            const structureTreeView = $("#planStructureTreeView").data("kendoTreeView") as kendo.ui.TreeView;

            // treeview datasource
            let tvData = $("#planStructureTreeView").data("kendoTreeView").dataSource.data();

            let contentNodeTypeImage = "../images/plan_icons_text.svg";
            if (this.structureTitleText) {
                let structureTitle = this.structureTitleText.toLowerCase();
                if (structureTitle == "text") {
                    contentNodeTypeImage = "../images/plan_icons_text.svg";
                } else if (structureTitle == "folder") {
                    contentNodeTypeImage = "../images/plan_icons_parent_node.svg";
                } else {
                    contentNodeTypeImage = "../images/plan_icons_table.svg";
                }
            }

            // add new plan structure
            let item = {};
            let errorMessage = [];
            if (this.structureOverview.trim() === "") {
                errorMessage.push($("#nodeNameValidation").html());
            }
            if (errorMessage.length > 0) {
                MODULES.errorConfirmation(errorMessage);
                return false;
            }

            if (this.structureUid == "") {
                item = {
                    name: this.structureOverview,
                    items: [],
                    contentNodeType: this.structureTitleText,
                    typeDisplayText: this.structureTitle,
                    moveRows: "<img src='../images/plan_icons_move.svg'>Flytt",
                    contentNodeTypeImage: contentNodeTypeImage,
                    levelNumber: 0,
                    description: this.editor.getData(),
                    mandateText: $("#PM_Predefined_Text").text(),
                    canDeleted: true,
                    isExistingNode: false,
                    levelClassName: "tv-level1-items"
                };
            } else {
               this.updateNode();
            }

            //this.pageChanges = true;

            //$("#planStructureTreeView").data("kendoTreeView").append(newItem, $("#planStructureTreeView .k-item:first").find('ul:eq(0)'));
            //if (tvData.length == 0) {
            //    structureTreeView.append(item);
            //} else {
            if (this.structureUid == "") {
                //structureTreeView.append(item);
                $("#planStructureTreeView").data("kendoTreeView").append(item);
                this.showEditor = false;
                this.planTypeTreeGridData = $("#planStructureTreeView").data("kendoTreeView").dataSource.data().length;
                this.addNewPlanStructureWindow.close();
                $(window).scrollTop(screen.height);
            }
            this.pageChanges = false;
            this.getPlanningStrategyType(true);
            //}
        }

        updateNode() {
            const structureTreeView = $("#planStructureTreeView").data("kendoTreeView") as kendo.ui.TreeView;
            const dataItem = structureTreeView.dataSource.getByUid(this.structureUid);
            dataItem["contentNodeType"] = this.structureTitleText;
            dataItem["description"] = this.editor.getData();
            dataItem["name"] = this.structureOverview;
            dataItem["typeDisplayText"] = this.structureTitle;
            this.$timeout(() => {
                const tvDatasource = $("#planStructureTreeView").data("kendoTreeView").dataSource.data();
                $("#planStructureTreeView").data("kendoTreeView").setDataSource(new kendo.data.HierarchicalDataSource({
                    data: tvDatasource
                }));
                this.planTypeTreeGridData = $("#planStructureTreeView").data("kendoTreeView").dataSource.data().length;
            }, 10);
            this.addNewPlanStructureWindow.close();
        }

        expandAll() {
            let treeview = <kendo.ui.TreeView>$("#planStructureTreeView").data("kendoTreeView");
            treeview.expand(".k-item");
        }

        collapseAll() {
            let treeview = <kendo.ui.TreeView>$("#planStructureTreeView").data("kendoTreeView");
            treeview.collapse(".k-item");
        }

        launchGuidanceTextPopup(planStructureNode: PlanStructureTreeNode) {
            this.guidanceTextWindow = $('#guidanceTextWindow').kendoWindow({
                modal: true,
                actions: ["close"],
                scrollable: true,
                resizable: false,
                draggable: false,
                width: "60%"
            }).data("kendoWindow").center().open() as kendo.ui.Window;
            // this.guidanceTextWindow.element.parent().css({ 'position': 'fixed', 'top': '10px' });
            this.guidanceTextWindow.element.prev().addClass('background-white padding-top15 padding-bottom10');
            this.guidanceTextWindow.element.prev().find('#guidanceTextWindow_wnd_title').html('<img src="../images/kostra_icon_info.png" class="vertical-align-middle"/><span class="padding-left5">Veiledningstekst</span>'); //find(".k-window-title").parent('<span></span>');
            this.guidanceTextWindow.element.prev().find('.k-window-actions').addClass('close-icon-img padding-right10 hand-pointer').append('<span class="padding-left5 vertical-align-bottom close-icon-text">Lukk</span>');
            this.guidanceTextWindow.element.prev().find('.k-window-actions span.close-icon-text').nextAll('span.close-icon-text').remove();
            $("#cancelAddNew, .close-icon-img").click(() => {
                this.guidanceTextWindow.close();
            });

            this.descriptionContentNodeType = planStructureNode.typeDisplayText;
            this.descriptionText = unescape(planStructureNode.description);
            this.descriptionContentNodeTypeImage = planStructureNode.contentNodeTypeImage || "../images/plan_icons_text.svg";

            $("#descriptionText").html("");
            $("#descriptionText").html(this.descriptionText);
        }

        deletePlanStructure(planTypeId: string, treeviewUId: string, nodeId: string, isExistingNode: boolean) {
            //console.log("delete plan : ", planTypeId);
            this.deleteConfirmWindow = $('#deletePlanWindow').kendoWindow({
                modal: true,
                actions: ["close"],
                scrollable: true,
                resizable: false,
                draggable: false,
                width: "40%"
            }).data("kendoWindow").center().open() as kendo.ui.Window;
            this.deleteConfirmWindow.element.prev().remove(); //find(".k-window-title").parent('<span></span>');

            $("#deleteCancel").click(() => {
                this.deleteConfirmWindow.close();
            });

            $("#deleteOk").unbind("click").click(() => {
                // remove from treeview if its new node
                if (isExistingNode == false) {
                    let treeview = <kendo.ui.TreeView>$("#planStructureTreeView").data("kendoTreeView");
                    let item = treeview.findByUid(treeviewUId);
                    treeview.remove(item);
                    this.pageChanges = true;
                    this.deleteConfirmWindow.close();
                } else {
                    this.getPlanningStrategyType(true);
                }
            });
        }
    }

    app = angular.module("PlanStrategyApp", ["kendo.directives"]).config(['$httpProvider', function ($httpProvider) {
        $httpProvider.interceptors.push(() => {
            return {
                'request': function (config) {
                    return MODULES.validateHttpRequest(config);
                }
            };
        });
    }])
        .controller("planStrategyController", ['$scope', '$http', '$q', '$location', '$timeout', '$sce', '$compile', function ($scope, $http, $q, $location, $timeout, $sce, $compile) {
            $("body").addClass('plan-content-background');
            return new PlanningStrategyAdmin($scope, $http, $q, $location, $timeout, $sce, $compile);
        }]);
}