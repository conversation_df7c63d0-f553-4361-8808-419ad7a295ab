declare var MODULES: any;
declare var TYPESCRIPTMODULES: any;
declare var EVENTS: any;
declare var CKEDITOR5: any;

module KostraWidgetSetup {
    interface IKostraWidgetSetupScope extends ng.IScope {
        vm: KostraWidgetSetup;
        kostraWidgetRegionAreaMultiSelectOptions: kendo.ui.MultiSelectOptions;
        kostraWidgetCategoryMultiSelectOptions: kendo.ui.MultiSelectOptions;
        kostraWidgetDataSourceMultiSelectOptions: kendo.ui.MultiSelectOptions;
        kostraMunicipalitiesMultiSelectOptions: kendo.ui.MultiSelectOptions;
        kostraYearSelectOptions: kendo.ui.DropDownListOptions;
        kostraYearCountSelectOptions: kendo.ui.DropDownListOptions;
        indicatorsSelectingGridOptions: kendo.ui.GridOptions;
        indicatorsFinalGridOptions: kendo.ui.GridOptions;
        toolTipWindow: kendo.ui.Window;
        saveAsNewTableWindow: kendo.ui.Window;
        unsavedChangesWindow: kendo.ui.Window;
        kostraDeleteWindow: kendo.ui.Window;
        kostraDeleteConfirmWindow: kendo.ui.Window;
        infoToolTip: kendo.ui.Tooltip;
        widgetTypeSelectOptions: kendo.ui.DropDownListOptions;
        widgetStatusSelectOptions: kendo.ui.DropDownListOptions;
        forecastTypeSelectOptions: kendo.ui.DropDownListOptions;
        fromYearSelectOptions: kendo.ui.DropDownListOptions;
        toYearSelectOptions: kendo.ui.DropDownListOptions;
        detailedPopulationStatGridOptions: kendo.ui.GridOptions;
        popStatDeleteConfirmWindow: kendo.ui.Window;
        leftMenuTreeViewOptions: kendo.ui.TreeViewOptions;
    }
    export const tooltipSter = (className: string): void => {
        let temeColor = className == '.information-tooltip' ? 'custom-style-border' : '';
        (<any>$(className)).tooltipster({
            trigger: 'hover',
            side: 'bottom',
            distance: 0,
            theme: temeColor
        });
        $(className)
            .focus(function () {
                (<any>$(this)).tooltipster('open');
            })
            .focusout(function () {
                (<any>$(this)).tooltipster('close');
            })
            .blur(function () {
                (<any>$(this)).tooltipster('close');
            });
    };
    type city = {
        RegionCode: string,
        RegionName: string
    }
    enum WidgetTypes 
    {
        kostra,
        populationStatistics
    }
    class KostraWidgetSetup {

        vm: KostraWidgetSetup;
        ckEditor5WebSocketURL = "";
        infoPopupDialog: kendo.ui.Window;
        groupTagSelectOptions: kendo.ui.MultiSelectOptions;
        groupTagSelectOptionsPopStat: kendo.ui.MultiSelectOptions;
        leftPopStatMenuList = [];        
        leftKostraMenuList = [];        
        typesOptions = [];
        yearOptions = [];
        tables = [];
        showCollapseIcon = false;
        isExpandCollapse = false;
        municipalityCheck = false;
        historyCheck = false;
        deflatorCheck = false;
        expenseNeedCheck = false;
        editcaption = false;
        editname = false;
        yearChanged = false;
        yearCountChanged = false;
        commonTab = "commanTab1";
        activeMaintab = "mainTab1";
        reportAreas = [];
        reportColumnsValArray = [];
        reportColumnResult = [];
        filterColumnResp = [];
        filterColumnValues = [];
        groupTagSelectPopStat: any;
        categories = [];        
        dataSources = [];
        municipalities = [];
        defaultRegions = [];
        selectedRegions = [];
        selectedReportAreas = [];
        selectedIndicators = [];
        chosingIndicators = [];
        chosingSelectedData = [];
        selectedCategories = [];
        selectedDataSources = [];
        tableSelectedData = [];
        cities: [];
        chosingFinalIndicators = [];
        chosingSelectedFinalData = [];
        finalSelectedIndicators = [];
        tableFinalData = [];
        areaCode = '01';
        year = '';
        yearCount = '';
        templateId = '00000000-0000-0000-0000-000000000000';
        indicaterParameter = '';
        indicatorList = [];
        selectedIndicatorList = [];
        widgetId = '00000000-0000-0000-0000-000000000000';
        newWidgetId = '00000000-0000-0000-0000-000000000000';
        tableName = '';
        captionText = '';
        showCityGraph = false;
        showHistoryGraph = false;
        adjustForDeflator = false;
        adjustForExpenseNeed = false;
        type = '';
        edittableName: any;
        editcaptionText: any;
        municipalityCheckValue : any;
        historyCheckValue : any;
        deflatorCheckValue : any;
        expenseNeedCheckValue : any;
        edittype: any;
        editYear: any;
        isPageChange = false;
        isFirstCheck = true;
        newName: any;
        newCaptionText: any;
        newTableName: any;
        groupId: any;
        groupName: any;
        groupTagSelect: any;     
        groupsDropdownDS: any;
        finalColumns: [];
        emptyString = '';
        selectedIndicatorsFromDropdown = [];
        finalIndicatorsFromDropdown = [];
        Grid2Data = [];
        Grid2InitialData = [];
        unsavedChangesChecked = false;
        activeLefttab: any;
        tabToSwitch: any;
        detailsTableName: any;
        detailsCaptionText: any;
        detailsMunCheck: any;
        detailsHisCheck: any;
        detailsDefCheck: any;
        detailsExpCheck: any;
        secondGridData: any;
        kostraDocExpCitySelectorDropdown: any;
        tn: any;
        mc: any;
        ct: any;
        hc: any;
        dc: any;
        enc: any;
        newbtn: any;
        editCaptionName: any;
        editTableText: any;
        deleteTable: any;
        firstLoadOfTable = true;
        displayDeleteMsg: any;
        gridColumns: any;
        tableType: boolean;
        newTableWidgetId: any;
        savedIndicators: any;
        tableNameToShow: any;
        fullTableName: any;
        showIndicatorSearch = false;
        widgetTypeOptions = [];
        widgetStatusOptions = [];
        forecastTypeOptions = [];
        fromToYearOptions = [];
        widgetType = "0";
        widgetStatus = "0";
        widgetName = "";
        collapseTitle = "";
        tableDescription = "";
        showPopStatCollapseSection = false;
        graphTypes = [];
        forecastType = "MMMM";      
        defaultFromYear = "";
        defaultToYear = "";
        showPopStatSection = true;
        showOnlyTable = false;
        emptyGuid = '00000000-0000-0000-0000-000000000000';
        leftMenuClick = false;
        popStatWidget = false;
        hideKostraCollapseSection = true;
        editWidgetName = false;
        widthNeeded = "100%";
        editYearCount: any;
        selectedPrevYearsCount = "";
        widgetConfigLoaded = false;
        isFirstExpand = true;
        totalIndicatorRecords = 0;

        constructor(private $scope: IKostraWidgetSetupScope, private $http: ng.IHttpService, public $compile: ng.ICompileService) {
            this.vm = this;
            this.initializeYearDeafults();
        }

        mainTabLoad() {
            this.widgetTypeOptions = [{ Key: 0, Value: $("#addKostraWidget").text() }, { Key: 1, Value: $("#addPopStatWidget").text() }]; //need service
            this.$scope.widgetTypeSelectOptions = {
                dataTextField: "Value",
                dataValueField: "Key",
                valuePrimitive: true,
                value: this.widgetType,
                dataSource: this.widgetTypeOptions,
                change: (e: kendo.ui.DropDownListChangeEvent) => {
                    this.widgetType = e.sender.value();
                }
            }          


            this.typesOptions = [{ Key: "1", Value: $("#globalType").text() }, { Key: "0", Value: $("#personalType").text() }];
            this.getAllTables(false);
            this.fullTableName = $("#createTableHeading").text();
            this.newTableName = $("#createTableHeading").text();
            this.newTableName = this.newTableName.length > 60 ? this.newTableName.substring(0, 60) + "..." : this.newTableName;
            this.tableNameToShow = this.newTableName.length > 30 ? this.newTableName.substring(0, 30) + "..." : this.newTableName;
            $('#captionTextTooltipText img').click(function () {
                MODULES.informationTooltipClick('#captionTextTooltipText', localStorage.getItem('captionTooltip'));
            });
        }
        getleftMenuTree(responseData: any) {
            this.$scope.leftMenuTreeViewOptions = {
                dataSource: responseData,
                dataTextField: "table_name",
                autoScroll: true,
                template: kendo.template($("#leftMenuKostraTreeViewTemplate").html()),
                dragAndDrop: false,
                dataBound: (e) => {
                    $("#leftMenuTreeView").removeAttr('tabindex');
                    $("#leftMenuTreeView .tvicons-level1-items").closest('span.k-in').prev('.k-icon').addClass('level1-icons-style');
                    $("#leftMenuTreeView .tvicons-level2-items, #leftMenuTreeView .tv-level2-items, #leftMenuTreeView .tvicons-level3-items, #leftMenuTreeView .tv-level3-items,#leftMenuTreeView .tvicons-level4-items, #leftMenuTreeView .tv-level4-items,#leftMenuTreeView .tvicons-level5-items, #leftMenuTreeView .tv-level5-items,.tvicons-level6-items, #leftMenuTreeView .tv-level6-items,.tvicons-level7-items, #leftMenuTreeView .tv-level7-items, .tvicons-level8-items, #leftMenuTreeView .tv-level8-items, .tvicons-level9-items, #leftMenuTreeView .tv-level9-items, .tvicons-level10-items, #leftMenuTreeView .tv-level10-items").closest('.k-item').addClass('border-top0');
                    $("#leftMenuTreeView .tvicons-level2-items, #leftMenuTreeView .tv-level2-items").closest('ul').addClass('level2-items-wrapper');
                    $("#leftMenuTreeView .tvicons-level3-items").closest('li').addClass('padding-left6');
                    $("#leftMenuTreeView .level1-icons-style").closest('li').addClass('padding-left26');
                    $("#leftMenuTreeView .tv-level1-items").closest('li').addClass('padding-left30');
                    $('#leftMenuTreeView').css({ 'max-height': $(window).height() - 300, 'overflow-y': 'auto' });
                    $("#leftMenuTreeView .tvicons-level1-items,#leftMenuTreeView .tv-level1-items,#leftMenuTreeView .tvicons-level2-items,#leftMenuTreeView .tvicons-level3-items,#leftMenuTreeView .tvicons-level4-items,#leftMenuTreeView .tvicons-level5-items,#leftMenuTreeView .tvicons-level6-items,#leftMenuTreeView .tvicons-level7-items,#leftMenuTreeView .tvicons-level8-items,#leftMenuTreeView .tvicons-level9-items,#leftMenuTreeView .tvicons-level10-items").closest('li').attr('aria-label', 'Parent');
                    $("#leftMenuTreeView .tv-level2-items,#leftMenuTreeView .tv-level3-items,#leftMenuTreeView .tv-level4-items,#leftMenuTreeView .tv-level5-items,#leftMenuTreeView .tv-level6-items,#leftMenuTreeView .tv-level7-items,#leftMenuTreeView .tv-level8-items,#leftMenuTreeView .tv-level9-items,#leftMenuTreeView .tv-level10-items").closest('li').attr('aria-label', 'Child');
                    $('#leftMenuTreeView').removeAttr('role');
                    $('#leftMenuTreeView ul:first-child').attr('role', 'tree');
                },
                expand: (e) => {
                    $("#leftMenuTreeView .level1-icons-style").closest('li').addClass('treeview-expandbg-style');                   
                }
            }          
        }

        popStatTooltip()
        {
            MODULES.informationTooltipClick('#popStatCaptionTextTooltipText', $("#descriptionToolTip").text());
        }
        initializeYearDeafults()
        {
            let today: any;
            today = new Date();
            var yyyy = today.getFullYear();
            this.defaultFromYear = (yyyy - 4).toString();
            this.defaultToYear = (yyyy + 9).toString();
            this.setGraphWidth();

        }
        resetAddNewKostraWidget()
        {
            this.widgetName = "";
            this.widgetStatus = "0";
            this.tableDescription = "";
        }

        showInitialSection(widgetType) {
            if (widgetType == "0") {
                this.hideKostraCollapseSection = true;
            }
            else {
                this.showPopStatSection = true;
            }
        }

        showPopStatAddNew(showAddNewPopStatWidgetSection)
        {
            this.widgetConfigLoaded = false;
            this.editWidgetName = false;
            this.popStatWidget = false;
            this.showPopStatCollapseSection = false;
            this.hideKostraCollapseSection = true;
            this.resetAddNewKostraWidget();
            this.showOnlyTable = false;
            $("#showGraphAndTable").prop('checked', true);
            $("#showOnlyTable").removeAttr('checked');
            this.initializeYearDeafults();
            this.widgetId = '00000000-0000-0000-0000-000000000000';
            $("#addingWidgetTitleSection").addClass('in');
            $("#tableNewBtn").addClass("ng-hide");
            $('ul#popStatWidgetsCollapseSection').find('li.active').removeClass('active');
            if (showAddNewPopStatWidgetSection) {
                this.showPopStatSection = true;
                $("#addNewWidgetSection").removeClass('ng-hide');
                $("#addingWidgetTitleSection").addClass('ng-hide');
                this.widgetType = "1";
            }
            else
            {
                this.showPopStatSection = true;
                $("#addNewWidgetSection").removeClass('ng-hide');
                $("#addingWidgetTitleSection").addClass('ng-hide');
                this.widgetType = "0";
            }
            //this.showSelectedWidgetType();
        }

        showSelectedWidgetType()
        {
            switch (this.widgetType)
            {
                case "0":
                    this.createTable();
                    this.getGroupTagSection();
                    break;
                case "1":
                    $("#addNewWidgetSection").addClass('ng-hide');
                    $("#addingWidgetTitleSection").removeClass('ng-hide');
                    this.widgetStatusOptions = [{ Key: 0, Value: $("#widgetStatusGlobalType").text() }, { Key: 1, Value: $("#widgetStatusPersonalType").text() }]; //need service
                    this.$scope.widgetStatusSelectOptions = {
                        dataTextField: "Value",
                        dataValueField: "Key",
                        valuePrimitive: true,
                        value: this.widgetStatus,
                        dataSource: this.widgetStatusOptions,
                        change: (e: kendo.ui.DropDownListChangeEvent) => {
                            this.widgetStatus = e.sender.value();
                        }
                    }
                    this.getGroupTagSection();
                    break;
            }
        }

        changeWidgetName(event)
        {
            this.editWidgetName = event;
        }

        createPopulationWidget(updateOnlyTitle) {
            if (this.widgetName == "" || this.tableDescription == "") {
                const detailMsg = [];
                detailMsg.push($("#titleValidation").text());
                MODULES.errorConfirmation(detailMsg);
                return;
            }
            this.collapseTitle = this.widgetName;
            this.popStatWidget = true;
            this.savePopulationWidgetData(false, updateOnlyTitle, false);
            this.getWidetSettingsData(false);
        }

        getWidetSettingsData(fromSave)
        {
            this.showPopStatCollapseSection = true;
            if (!fromSave)
            {
                this.graphListItems();
            }
            $("#addingWidgetTitleSection").removeClass('ng-hide');
            $("#addingWidgetTitleSection").addClass('collapse');
            if (this.forecastTypeOptions.length == 0)
            {
                this.getForecastTypeDropdown();
            }
            if (this.fromToYearOptions.length == 0) {
                this.getYearDropdown();
            }
        }

        graphListItems() {
            this.graphTypes = [];
            this.graphTypes.push({ "key": 0, "value": $("#graphType1").text() });
            this.graphTypes.push({ "key": 1, "value": $("#graphType2").text() });
            this.graphTypes.push({ "key": 2, "value": $("#graphType3").text() });
            this.graphTypes.push({ "key": 3, "value": $("#graphType4").text() });
            this.graphTypes.push({ "key": 4, "value": $("#graphType5").text() });    
            if ($("input[name=graphType]:checked").val() == "false") {
                $("#graph0").prop("checked", true);
            }
        }
        
        validateYear()
        {
            if ((this.defaultFromYear >= this.defaultToYear))
            {
                const detailMsg = [];
                detailMsg.push("Invalid year selection");
                MODULES.errorConfirmation(detailMsg);
                return;
            }
            else
            {
                this.setGraphWidth();
                this.getPopulationStatisticsWidget();
            }
        }

        setGraphWidth()
        {
            if (parseInt(this.defaultToYear) - parseInt(this.defaultFromYear) > 15) {
                this.widthNeeded = "1200px";
            }
            else {
                this.widthNeeded = "100%";
            }
        }

        validateGraphAndTableView(showOnlyTable)
        {
            this.showOnlyTable = showOnlyTable;
        }

        getYearDropdown() {
            EVENTS.loaderON("#graphTableSettingsSection");
            this.$http.get<any>("../api/KostraWidgetSetupAPIController/GetKostraWidgetFromAndToYear").then(responseData => {
                this.fromToYearOptions = responseData.data.yearSelector;
                if (!this.widgetConfigLoaded)
                {
                    this.defaultFromYear = responseData.data.fromYearDefault;
                    this.defaultToYear = responseData.data.toYearDefault;
                }
                this.$scope.fromYearSelectOptions = {
                    dataTextField: "value",
                    dataValueField: "key",
                    valuePrimitive: true,
                    value: this.defaultFromYear,
                    dataSource: this.fromToYearOptions,
                    change: (e: kendo.ui.DropDownListChangeEvent) => {
                        this.defaultFromYear = e.sender.value();
                        this.validateYear();
                    }
                }
                this.$scope.toYearSelectOptions = {
                    dataTextField: "value",
                    dataValueField: "key",
                    valuePrimitive: true,
                    value: this.defaultToYear,
                    dataSource: this.fromToYearOptions,
                    change: (e: kendo.ui.DropDownListChangeEvent) => {
                        this.defaultToYear = e.sender.value();
                        this.validateYear();
                    }
                }
                if (!this.leftMenuClick) {
                    $("#graph0").prop("checked", true);
                }
                this.setActivePopStatWidget();
                EVENTS.loaderOFF("#graphTableSettingsSection");
            }, error => {
                const err = { status: error.status };
                ERROR.displayException(err, error.statusText, error.data, "GetForecastType");
            });
        }

        getForecastTypeDropdown()
        {
            EVENTS.loaderON("#graphTableSettingsSection");
            this.$http.get<any>("../PopulationStatistics/GetForecastType").then(responseData => {
                this.forecastTypeOptions = responseData.data.forecastTypes;
                this.forecastType = responseData.data.forecastTypes[0].ForecastTypeCode;
                this.$scope.forecastTypeSelectOptions = {
                    dataTextField: "ForecastName",
                    dataValueField: "ForecastTypeCode",
                    valuePrimitive: true,
                    value: this.forecastType,
                    dataSource: this.forecastTypeOptions,
                    change: (e: kendo.ui.DropDownListChangeEvent) => {
                        this.forecastType = e.sender.value();
                        this.getPopulationStatisticsWidget();
                    }
                }
                if (!this.leftMenuClick)
                {
                    this.getPopulationStatisticsWidget();
                }
                this.setActivePopStatWidget();
            }, error => {
                const err = { status: error.status };
                ERROR.displayException(err, error.statusText, error.data, "GetForecastType");
            });
        }

        savePopulationWidgetData(saveAsCopy, updateOnlyTitle, updateGraphTableConfig) {
            let sectionId = "#addingWidgetTitleSection";
            if (updateGraphTableConfig) {
                sectionId = "#graphTableSettingsSection";
            }
            EVENTS.loaderON(sectionId);
            let newWidget = false;
            if (this.editWidgetName && (this.widgetId == "" || this.widgetId == this.emptyGuid)) {
                newWidget = true;
            }
            var gridObject = {
                "widgetSetupId": this.widgetId,
                "widgetId": this.widgetId,
                "isGlobal": this.widgetStatus == "0" ? true : false,
                "tableName": saveAsCopy ? this.newName : this.widgetName,
                "captionText": saveAsCopy ? this.newCaptionText : this.tableDescription,
                "tableType": this.widgetType == '1' ? true : false,
                "checkTotalPopulation": newWidget ? false : $("#graph0").prop("checked"),
                "checkCitizensChange": newWidget ? false : $("#graph1").prop("checked"),
                "checkYouthChange": newWidget ? false : $("#graph2").prop("checked"),
                "checkElderChange": newWidget ? false : $("#graph3").prop("checked"),
                "checkWorkForceChange": newWidget ? false : $("#graph4").prop("checked"),
                "fromYear": parseInt(this.defaultFromYear),
                "toYear": parseInt(this.defaultToYear),
                "forecastType": this.forecastType,
                "widgetType": 1,
                "updateOnlyTitle": updateOnlyTitle,
                "showOnlyTable": $("input[name=graphOrTable]:checked").val() == "1",
                "groupId": parseInt($("#popStatGroupTags").val() + ""),
                "groupName": $("#popStatGroupTags").data("kendoMultiSelect").dataItems().length != 0 ? $("#popStatGroupTags").data("kendoMultiSelect").dataItems()[0].groupName:""
            };
            let jsonData = angular.toJson(gridObject);
            this.$http.post<any>("../api/KostraWidgetSetup/SavePopulationStatisticsWidget?isCopy=" + saveAsCopy, jsonData).then(responseData => {
                this.widgetId = responseData.data;
                if (saveAsCopy) {
                    MODULES.saveConfirmation($("#copiedSuccessfully").text());
                    this.getAllTables(true);
                }
                else
                {
                    MODULES.saveConfirmation();
                    this.getAllTables(false);
                }
                this.getWidetSettingsData(true);
                if (newWidget)
                {
                    this.widthNeeded = "100%";
                    $("#graph0").prop("checked", true);
                    this.initializeYearDeafults();
                    this.getWidgetDetails(this.widgetId, false);
                }
                EVENTS.loaderOFF(sectionId);
            }, error => {
                EVENTS.loaderOFF(sectionId);
                const err = { status: error.status };
                ERROR.displayException(err, error.statusText, error.data, "getKostraFinalGridData");
            });
        }

        getPopulationStatisticsWidget()
        {
            EVENTS.loaderON("#populationStatChartSection");
            EVENTS.loaderON("#detailedPopulationStatGrid");
            let graphType = $("input[name=graphType]:checked").val() == undefined ? 0 : $("input[name=graphType]:checked").val();
            this.$http.get<any>("../api/KostraWidgetSetupAPIController/GetPopulationStatisticsWidget?graphTypeToshow=" + graphType + "&fromYear=" + this.defaultFromYear + "&toYear=" + this.defaultToYear + "&forecastType=" + this.forecastType + "&widgetId=" + this.widgetId + "&widgetTitle=" + this.collapseTitle ).then(responseData => {
                let response = responseData.data;
                $("#populationStatChart").kendoChart(response.graphDataSource);
                this.$scope.detailedPopulationStatGridOptions = {
                    dataSource: { data: response.gridDataSource },
                    dataBound: (ev) => {
                        let gridId = "#detailedPopulationStatGrid";
                        $(gridId).find('table').removeAttr('tabindex');
                        $(gridId + " tr td").css({ "border-left": "0 none", "border-right": "0 none" });
                        $(gridId + " tr th").css({ "border-left": "0 none", "border-right": "0 none" });
                        $(gridId).find('.k-grid-header th.k-header').css("padding-right", "0px");
                        $(gridId).find(".k-grid-header").css('padding-right', '8px');
                        $(gridId + " tr:last").addClass("semi");
                        $(gridId).find('.k-grid-header th.k-header').css("background-color", "#FFFCF5");
                        $(gridId + " tr:last").css("background-color", "#fff2db");
                        $(gridId).addClass('border-none border1');
                    },
                    columns: response.gridColumns
                };
                this.setActivePopStatWidget();
                EVENTS.loaderOFF("#populationStatChartSection");
                EVENTS.loaderOFF("#detailedPopulationStatGrid");
            }, error => {
                const err = { status: error.status };
                ERROR.displayException(err, error.statusText, error.data, "GetPopulationStatisticsWidget");
            });
        }

        popStatleftMenuChange(param)
        {
            this.editWidgetName = false;
            this.widgetConfigLoaded = false;
            $('ul#kostraWidgetsCollapseSection').find('li.active').removeClass('active');
            if ($("#addingWidgetTitleSection").hasClass("in"))
            {
                $("#addingWidgetTitleSection").removeClass("in");
            }
            if (!$("#popCollapsingElement").hasClass("collapsed"))
            {
                $("#popCollapsingElement").addClass("collapsed");
            }
            this.hideKostraCollapseSection = true;
            this.popStatWidget = true;
            $("#tableNewBtn").addClass("ng-hide");
            this.leftMenuClick = true;
            this.getWidetSettingsData(false);
            this.widgetName = param.table_name;
            this.collapseTitle = param.table_name;
            this.widgetId = param.pk_id;
            this.tableDescription = param.caption_text;
            this.showPopStatSection = true;
            this.widgetType = "1";
            $("#addNewWidgetSection").addClass('ng-hide');
            $("#addingWidgetTitleSection").removeClass('ng-hide');
            this.showPopStatCollapseSection = true;
            this.getWidgetDetails(param.pk_id, false);            
        }

        createTable() {
            this.hideKostraCollapseSection = false;
            this.fullTableName = $("#createTableHeading").text();
            this.newTableName = $("#createTableHeading").text();
            this.newTableName = this.newTableName.length > 60 ? this.newTableName.substring(0, 60) + "..." : this.newTableName;
            this.tableNameToShow = this.newTableName.length > 30 ? this.newTableName.substring(0, 30) + "..." : this.newTableName;
            $("#tableBtn").addClass("ng-hide");
            $("#addNewWidgetSection").addClass("ng-hide");
            //$("#detailsTableBtn").removeClass("ng-hide");
            $("#expandCollapseContentSec").removeClass("align-center");
            $("#createTablePopup").removeClass("ng-hide");
            $("#collapsingElement").removeClass('collapsed');
            $("#createTablePopupDetails").addClass('in');
            $("#tabExplanationText").addClass('ng-hide');
            $("#polSimTopTabButtons").addClass('ng-hide');
            //$("#saveTablePopup").removeClass("ng-hide");
             this.editTableText = '';
            if ($("#kostraTableTypeDropdown").data("kendoDropDownList")) {
                $("#kostraTableTypeDropdown").data("kendoDropDownList").value('');
            }
            this.editCaptionName = '';
            this.municipalityCheckValue = false;
            this.historyCheckValue = false;
            this.deflatorCheckValue = false;
            this.expenseNeedCheckValue = false;
            this.getWidgetDetails(this.widgetId, true);
            this.getMunicipalitiesDropdown(true);
            this.getTableTypeDropDown(true);             
        }        
        editTable() {
            this.widgetId = this.templateId;
            this.fullTableName = $("#createTableHeading").text();
            this.newTableName = $("#createTableHeading").text();
            this.newTableName = this.newTableName.length > 60 ? this.newTableName.substring(0, 60) + "..." : this.newTableName;
            this.tableNameToShow = this.newTableName.length > 30 ? this.newTableName.substring(0, 30) + "..." : this.newTableName;
            $("#tableBtn").addClass("ng-hide");
            $("#saveTablePopup").addClass("ng-hide");
            $("#createTablePopup").removeClass("ng-hide");
            $("#expandCollapseContentSec").removeClass("align-center");
            $("#collapsingElement").removeClass('collapsed');
            $("#createTablePopupDetails").addClass('in');
            $("#tabExplanationText").addClass('ng-hide');
            $("#polSimTopTabButtons").addClass('ng-hide');
            $("#tableNewBtn").addClass("ng-hide");

            this.editTableText = '';
            $("#kostraTableTypeDropdown").data("kendoDropDownList").value('1');
            this.editCaptionName = '';
            this.municipalityCheckValue = false;
            this.historyCheckValue = false;
            this.deflatorCheckValue = false;
            this.expenseNeedCheckValue = false;
            //$("#detailsTableBtn").addClass("ng-hide");
            $("#PSLeftMenuSection ul li").removeClass('active');
            $("#uprightBtn").addClass("ng-hide");
            $("#saveBtn").removeClass("ng-hide");
            this.getYearsCountDropdown('4');
        }
        editTableSettings() {
            //$("#saveTablePopup").addClass("ng-hide");
            //$("#createTablePopup").removeClass("ng-hide");
            //$("#detailsTableBtn").addClass("ng-hide");
        }
        saveTable(isExisting: boolean) {
            EVENTS.loaderON("#saveTablePopupDetails");
            this.edittableName = this.editTableText;
            this.edittype = $("#kostraTableTypeDropdown").data("kendoDropDownList").value();
            this.editYear = $("#kostraYearsDropdown").data("kendoDropDownList").value();
            this.editYearCount = $("#kostraYearsCountDropdown").data("kendoDropDownList").value();
            this.editcaptionText = this.editCaptionName;
            if (this.edittableName == "" || this.editcaptionText == "" || this.edittype == "" || this.editYear == "") {
                const detailMsg = [];
                detailMsg.push($("#validateMsg").text());
                MODULES.errorConfirmation(detailMsg);
            }
            else {
                if (this.editTableText != '') {
                    this.fullTableName = this.editTableText;
                    this.newTableName = this.editTableText;
                    this.newTableName = this.newTableName.length > 60 ? this.newTableName.substring(0, 60) + "..." : this.newTableName;
                    this.tableNameToShow = this.newTableName.length > 30 ? this.newTableName.substring(0, 30) + "..." : this.newTableName;
                }
                //$("#newTableName").text(this.newTableName);
                $("#tableBtn").addClass("ng-hide");
                $("#expandCollapseContentSec").removeClass("align-center");
                $("#tableNewBtn").removeClass("ng-hide");
                $("#collapsingElement").addClass('collapsed');
                $("#createTablePopupDetails").removeClass('in');
                $("#tabExplanationText").removeClass('ng-hide');
                $("#polSimTopTabButtons").removeClass('ng-hide');
                $("#saveTablePopup").removeClass("ng-hide");
                $("#detailsTableBtn").removeClass("ng-hide");
                if (!isExisting) {
                    this.saveKostraWidgetGrid(false, false);
                }
                this.indicatorSearch();
                this.getReportAreaDropdown();
                this.getCategoryAndDataSourcesDropdown();
            }
            EVENTS.loaderOFF("#saveTablePopupDetails");
        }
        refreshDropdowns() {
            $("#kostraTableTypeDropdown").data("kendoDropDownList").enable(true);
            $("#kostraYearsDropdown").data("kendoDropDownList").enable(true);
            if (this.isFirstExpand) {
                $("#kostraYearsCountDropdown").data("kendoDropDownList").value(this.selectedPrevYearsCount);
                this.isFirstExpand = false;
            }
        }
        updateCommonTab(tab: string) {

            this.commonTab = tab;

            $('.commanTab').removeClass('active');
            $('#PSCommanTab li, #PSCommanTab li a').removeClass('active');
            $("#" + tab).addClass('active');
            $("#" + tab).parent('li').addClass('active');
            $("#" + tab + "Content").addClass("in active");

            if (this.activeMaintab == "mainTab1") {

                switch (tab) {
                    case "commanTab1":
                        //this.getKostraGrid1Data();
                        break;
                    case "commanTab2":
                        if (this.tableSelectedData.length == 0) {
                            this.getKostraFinalGridData();
                        }
                        else {
                            this.Grid2Data = [];
                            for (var x in this.tableSelectedData) {
                                this.Grid2Data.push(this.tableSelectedData[x].value);
                            }
                            this.secondGridData = Array.from(new Set(this.Grid2Data));
                            let Data1 = this.formatIndicatorValues(this.secondGridData);
                            if (!$("#indicatorsFinalGrid").data("kendoGrid")) {
                                this.$scope.indicatorsFinalGridOptions = {
                                    dataSource: { data: Data1 },
                                    dataBound: (ev) => {
                                        $('#indicatorsFinalGrid').find('table').removeAttr('tabindex');
                                    },
                                    columns: this.finalColumns,
                                    sortable: true,
                                    navigatable: true,
                                    scrollable: true,
                                    height: 432
                                };
                            }
                            else {
                                $("#indicatorsFinalGrid").data("kendoGrid").setDataSource(new kendo.data.DataSource({
                                    data: this.Grid2Data
                                }));
                            }
                        }
                        break;
                }

            }
        }

        getAllTables(newTable) {
            this.$http.get<any>("../api/KostraWidgetSetupAPIController/GetKostraWidgetTables").then(responseData => {
                let response = responseData.data;                
                this.getleftMenuTree(response);
                if (newTable) {
                    this.getWidgetDetails(this.widgetId, false);
                }
                else {
                    setTimeout(() => {
                        this.setActivePopStatWidget();
                    }, 200);
                }
                
                //this.leftMenuList = response.Data;
            }, error => {
                const err = { status: error.status };
                ERROR.displayException(err, error.statusText, error.data, "getAllTables");
            });
        }
        addNewGroup(inputValue:string, gridId:string) {
            var validString = /([^\s])/.test(inputValue);
            if (validString == true) {
                inputValue = inputValue.trim();               
                var widget = <kendo.ui.MultiSelect>$("#" + gridId).data("kendoMultiSelect");
                var select = widget.dataSource;
                
                select.add({
                    groupId: 0,
                    groupName: inputValue
                });                
                const newValue = "0";
                setTimeout(() => {
                    widget.value(widget.value().concat([newValue]));
                    select.sync();
                }, 100);                                
                               
            }
        }
        getGroupTagSection() {
            let gridId = '';            
            if (parseInt(this.widgetType) == WidgetTypes.populationStatistics) {
                gridId = "#popStatGroupTags";
                this.groupTagSelectOptionsPopStat = {
                    dataTextField: "groupName",
                    maxSelectedItems: 1,
                    dataValueField: "groupId",
                    valuePrimitive: true,
                    autoBind: false,
                    filter: "contains",
                    dataSource: [],
                    value: this.groupId,
                    select: function (e) {
                    },
                    itemTemplate: kendo.template($("#popStatWidgetDropdownTemplate").html()),
                    noDataTemplate: $("#noDataPopStatGroupTags").html(),

                };
            }
            else {
                gridId = "#kostraGroupTags";
                this.groupTagSelectOptions = {
                    dataTextField: "groupName",
                    maxSelectedItems: 1,
                    dataValueField: "groupId",
                    valuePrimitive: true,
                    autoBind: false,
                    filter: "contains",
                    dataSource: [],
                    value: this.groupId,
                    select: function (e) {
                    },
                    itemTemplate: kendo.template($("#kostraWidgetDropdownTemplate").html()),
                    noDataTemplate: $("#noDataKostraGroupTags").html(),

                }; 
            }
            
            
            this.$http.get<any>("../api/KostraWidgetSetupAPIController/GetWidgetGroups?widgetType=" + this.widgetType).then(responseData => {
                var data = responseData.data.groupData;
                //var funcThis = this;
                if (gridId == "#popStatGroupTags") {
                    $("#popStatGroupTags").data("kendoMultiSelect").setDataSource(new kendo.data.DataSource({
                        data: responseData.data.groupData
                    }));
                    $("#popStatGroupTags").data("kendoMultiSelect").value(this.groupId);
                }
                else {
                    $("#kostraGroupTags").data("kendoMultiSelect").setDataSource(new kendo.data.DataSource({
                        data: responseData.data.groupData
                    }));
                    $("#kostraGroupTags").data("kendoMultiSelect").value(this.groupId);
                }
                                       
                
                
                setTimeout(() => {
                        $(gridId).data().kendoMultiSelect.input.on('keydown', (e) => {

                        const multiSelect = $(gridId).data("kendoMultiSelect");
                        if (e.keyCode == 13 || e.keyCode == 9) { //on enter key press or tab key press
                            const input = multiSelect.input;
                            const inputValue = input.val() as string;
                            this.addNewGroup(inputValue,multiSelect.element[0].id);
                        }
                    });
                }, 100);
                
                
                
            });

        }
       
        

        getTableTypeDropDown(value: any) {
            this.tableType = value;
            $("#kostraTableTypeDropdown").kendoDropDownList({
                dataTextField: "Value",
                dataValueField: "Key",
                dataSource: this.typesOptions,
                change: (e: kendo.ui.DropDownListChangeEvent) => {
                    this.tableType = $("#kostraTableTypeDropdown").data("kendoDropDownList").value() == '1' ? true : false;
                    this.isPageChange = true;
                },
                value: value
            });
            if ($("#kostraTableTypeDropdown").data("kendoDropDownList")) {
                $("#kostraTableTypeDropdown").data("kendoDropDownList").value(value == true ? '1' : '0');
            }
        }

        getWidgetDetails(widgetSetupId: string, firstLoad: boolean) {
            EVENTS.loaderON("#createTablePopupDetailsContent");
            this.$http.get<any>("../api/KostraWidgetSetupAPIController/GetKostraWidgetSetupGridConfig?widgetSetupId=" + widgetSetupId + "&widgetType="+ this.widgetType).then(responseData => {
                let response = responseData.data;
                if (parseInt(this.widgetType) == WidgetTypes.kostra) {
                    this.kostraTableFormating(response, firstLoad);
                }
                else
                {
                    this.popSatWidgetSetUp(response);
                }
            }, error => {
                EVENTS.loaderOFF("#createTablePopupDetailsContent");
                const err = { status: error.status };
                ERROR.displayException(err, error.statusText, error.data, "getWidgetDetails");
            });
        }

        
        popSatWidgetSetUp(response)
        {
            this.widgetName = response.data.tableName;
            this.collapseTitle = response.data.tableName;
            this.widgetStatus = response.data.isGlobal ? "0" : "1";
            this.tableDescription = response.data.captionText;
            this.defaultFromYear = response.data.fromYear;
            this.defaultToYear = response.data.toYear;
            this.groupId = response.data.groupId;
            this.setGraphWidth();
            this.showOnlyTable = response.data.showOnlyTable;
            if (this.showOnlyTable) {
                $("#showOnlyTable").prop('checked', true);
            }
            else {
                $("#showGraphAndTable").prop('checked', true);
            }
            this.setActivePopStatWidget();
            this.setGraphTableType(response);
            this.forecastType = response.data.forecastType == "" && this.forecastTypeOptions.length > 0 ? this.forecastTypeOptions[0].ForecastTypeCode : response.data.forecastType;
            this.showPopStatCollapseSection = true;
            this.showSelectedWidgetType();            
            this.widgetConfigLoaded = true;
        }

        setActivePopStatWidget() {
            let id = this.widgetType == "1" ? "#popStatWidgetsCollapseSection" : "#kostraWidgetsCollapseSection";
            if (this.widgetId != "" && this.widgetId != this.emptyGuid) {
                $('ul' + id).find('li.active').removeClass('active');
                $("#" + this.widgetId).addClass('active');
            }
        }

        setGraphTableType(response) {
            let type = "0";
            if (response.data.checkCitizensChange) {
                type = "1";
            }
            else if (response.data.checkYouthChange) {
                type = "2";
            }
            else if (response.data.checkElderChange) {
                type = "3";
            }
            else if (response.data.checkWorkForceChange) {
                type = "4";
            }
          
            $("input[name=graphType][value=" + type + "]").attr('checked', 'checked');
            this.getPopulationStatisticsWidget();
        }

        kostraTableFormating(response, firstLoad)
        {
            let tableDetails = response.data;
            this.year = tableDetails.selectedYear;
            if (this.isFirstCheck) {
                var index = this.leftKostraMenuList.findIndex(x => x.widgetId === tableDetails.widgetSetupId);
                if (index === -1) {
                    this.newWidgetId = tableDetails.widgetSetupId;
                    this.isFirstCheck = false;
                }
            }
            this.widgetId = tableDetails.widgetSetupId;
            this.setActivePopStatWidget();
            this.finalSelectedIndicators = tableDetails.selectedIndicators;
            this.selectedRegions = tableDetails.regions;
            this.groupId = tableDetails.groupId;
            if (tableDetails.tableName != '') {
                this.fullTableName = tableDetails.tableName;
                this.newTableName = tableDetails.tableName;
                this.newTableName = this.newTableName.length > 60 ? this.newTableName.substring(0, 60) + "..." : this.newTableName;
                this.tableNameToShow = this.newTableName.length > 30 ? this.newTableName.substring(0, 30) + "..." : this.newTableName;
                //$("#PSLeftMenuSection ul li").removeClass('active');
                var index = this.leftKostraMenuList.findIndex(x => x.widgetId === this.widgetId);
                //$("#PSLeftMenuSection ul  #" + index).addClass('active');
            }
            setTimeout(() => {
                if (!firstLoad) {
                    this.getGroupTagSection();
                    this.getMunicipalitiesDropdown(false);
                    this.getTableTypeDropDown(tableDetails.type);
                    this.getKostraFinalGridData();
                    
                }
                this.getYearsDropdown(tableDetails.selectedYear);
                this.getYearsCountDropdown(tableDetails.selectedPrevYearCount);
            }, 200);
            this.detailsTableName = tableDetails.tableName;
            this.detailsMunCheck = tableDetails.showCityGraph;
            this.detailsHisCheck = tableDetails.showHistoryGraph;
            this.detailsDefCheck = tableDetails.adjustForDeflator;
            this.detailsExpCheck = tableDetails.adjustForExpenseNeed;
            this.detailsCaptionText = tableDetails.captionText;
            this.editTableText = tableDetails.tableName;
            this.editCaptionName = tableDetails.captionText;
            tableDetails.showCityGraph ? this.municipalityCheckValue = true : this.municipalityCheckValue = false;
            tableDetails.showHistoryGraph ? this.historyCheckValue = true : this.historyCheckValue = false;
            tableDetails.adjustForDeflator ? this.deflatorCheckValue = true : this.deflatorCheckValue = false;
            tableDetails.adjustForExpenseNeed ? this.expenseNeedCheckValue = true : this.expenseNeedCheckValue = false;
            EVENTS.loaderOFF("#createTablePopupDetailsContent");
        }

        getYearsCountDropdown(value: any) {
            let yearsCountArray = [];
            for (let i = 1; i <= 10; i++) {
                let tempObject = {
                    "id": i,
                }
                yearsCountArray.push(tempObject);
            }
            this.$scope.kostraYearCountSelectOptions = {
                dataTextField: "id",
                dataValueField: "id",
                valuePrimitive: true,
                dataSource: yearsCountArray,
                index: 3,
                //optionLabel: "-- Fyll inn --",
                change: (e: kendo.ui.DropDownListChangeEvent) => {
                    this.yearCount = $("#kostraYearsCountDropdown").data("kendoDropDownList").value();
                    this.isPageChange = true;
                }
            }
            if ($("#kostraYearsCountDropdown").data("kendoDropDownList")) {
                $("#kostraYearsCountDropdown").data("kendoDropDownList").value(value);
            }
        }

        getYearsDropdown(value: any) {
            this.$http.get<any>("../api/KostraWidgetSetupAPIController/GetKostraWidgetBudgetYears").then(responseData => {
                let response = responseData.data;
                this.yearOptions = response.Data;
                this.year = value;
                this.$scope.kostraYearSelectOptions = {
                    dataTextField: "value",
                    dataValueField: "key",
                    valuePrimitive: true,
                    dataSource: this.yearOptions,
                    //optionLabel: "-- Fyll inn --",
                    change: (e: kendo.ui.DropDownListChangeEvent) => {
                        this.year = $("#kostraYearsDropdown").data("kendoDropDownList").value();
                        this.yearChanged = true;
                        this.isPageChange = true;
                    },
                    value: value
                }
                if ($("#kostraYearsDropdown").data("kendoDropDownList")) {
                    $("#kostraYearsDropdown").data("kendoDropDownList").value(value);
                }
            }, error => {
                const err = { status: error.status };
                ERROR.displayException(err, error.statusText, error.data, "getYearsDropdown");
            });
        }
        getKostraFinalGridData() {
            EVENTS.loaderON("#commanTab2Content");
            var gridObject = {
                "adjustforExp": this.expenseNeedCheckValue,
                "adjustforDef": this.deflatorCheckValue,
                "widgetId": this.widgetId,
                "chosenYear": this.year,
                "selectedPrevYearCount": this.selectedPrevYearsCount,
                "indicators": this.finalSelectedIndicators,
                "dataSources": this.selectedDataSources,
                "categories": this.selectedCategories,
                "reportAreas": this.selectedReportAreas,
                "regionAreas": this.selectedRegions
            };
            this.$http.post<any>("../api/KostraWidgetSetupAPIController/GetKostraWidgetGridSelectedIndicatorsData", angular.toJson(gridObject)).then(responseData => {
                let response = responseData.data;
                this.Grid2Data = response.data;
                this.savedIndicators = this.Grid2Data.length;
                this.finalColumns = response.columns;
                this.Grid2InitialData = [];
                this.tableSelectedData = [];
                for (var x in this.Grid2Data) {
                    this.Grid2Data[x].active = true;
                    this.Grid2InitialData.push({
                        key: "#active_" + this.Grid2Data[x].rowId,
                        value: this.Grid2Data[x]
                    });
                    this.tableSelectedData.push({
                        key: "#active_" + this.Grid2Data[x].rowId,
                        value: this.Grid2Data[x],
                        code: this.Grid2Data[x].indicatorCode
                    });
                }
                let Data = this.formatIndicatorValues(this.Grid2Data);
                setTimeout(() => {

                    if (!$("#indicatorsFinalGrid").data("kendoGrid")) {
                        this.$scope.indicatorsFinalGridOptions = {
                            dataSource: { data: Data },
                            dataBound: (ev) => {
                                $('#indicatorsFinalGrid').find('table').removeAttr('tabindex');
                            },
                            columns: response.columns,
                            sortable: true,
                            navigatable: true,
                            scrollable: true,
                            height: 432
                        };
                    }
                    else {
                        $("#indicatorsFinalGrid").data("kendoGrid").setOptions({ columns: response.columns });
                        $("#indicatorsFinalGrid").data("kendoGrid").setDataSource(new kendo.data.DataSource({
                            data: this.Grid2Data
                        }));
                    }

                }, 150);

                EVENTS.loaderOFF("#commanTab2Content");
            }, error => {
                EVENTS.loaderOFF("#commanTab2Content");
                const err = { status: error.status };
                ERROR.displayException(err, error.statusText, error.data, "getKostraFinalGridData");
            });
        }
        getkotraGrid1Columns() {
            var gridObject = {
                "widgetId": this.widgetId,
                "chosenYear": parseInt(this.year == "" || this.year == undefined ? "0" : this.year),
                //"selectedPrevYearCount": this.tabToSwitch.selectedPrevYearCount,
                "indicators": [],
                "dataSources": [],
                "categories": [],
                "reportAreas": [],
                "regionAreas": [],
                "take": 0,
                "skip": 0,
                "selectedPrevYearCount": parseInt(this.selectedPrevYearsCount)
            };
            this.$http.post<any>("../api/KostraWidgetSetupAPIController/GetKostraWidgetGridDataColumns", angular.toJson(gridObject)).then(responseData => {
                let response = responseData.data;
                //if ($("#indicatorsSelectingGrid").data('kendoGrid')) {
                //    $("#indicatorsSelectingGrid").data("kendoGrid").setOptions({ columns: response.columns });
                //    $("#indicatorsSelectingGrid").data("kendoGrid").setDataSource(new kendo.data.DataSource({ data: [] }));
                //    $('#indicatorsSelectingGrid .k-grid-content').height(0);
                //}
                $("#indicatorsSelectingGrid").kendoGrid({
                        dataSource: { data : [] },
                        columns: response.columns,
                        height: 0,
                    sortable: true
                }).data("kendoGrid");

            }, error => {
                EVENTS.loaderOFF("#commanTab1Content");
                const err = { status: error.status };
                ERROR.displayException(err, error.statusText, error.data, "getkotraGrid1Columns");
            });
        }

        getKostraGrid1Data() {
            EVENTS.loaderON("#commanTab1Content"); 
            $("#noDataMsg").addClass('ng-hide');
            $("#tab1Data").removeClass('ng-hide');
            if (this.selectedReportAreas.length > 0 || this.selectedCategories.length > 0 || this.selectedDataSources.length > 0) {
                let filteredReportAreas = this.selectedReportAreas, filteredDataSources = this.selectedDataSources, filteredCategories = this.selectedCategories;
                //if no filter is selected, send all valid values of reporting areas, categories, dataSources
                if (filteredReportAreas.length === 0) {
                    filteredReportAreas = this.reportAreas.length > 0 ? this.reportAreas.map(x => x.id) : [];
                }
                if (filteredDataSources.length === 0) {
                    filteredDataSources = this.dataSources.length > 0 ? this.dataSources.map(x => x.DataSourceId) : [];
                }
                if (filteredCategories.length === 0) {
                    filteredCategories = this.categories.length > 0 ? this.categories.map(x => x.CategoryId) : [];
                }
                var gridObject = {
                    "adjustforExp": this.expenseNeedCheckValue,
                    "adjustforDef": this.deflatorCheckValue,
                    "widgetId": this.widgetId,
                    "chosenYear": this.year,
                    "indicators": this.finalSelectedIndicators,
                    "dataSources": filteredDataSources,
                    "categories": filteredCategories,
                    "reportAreas": filteredReportAreas,
                    "regionAreas": this.selectedRegions,
                    "take": 0,
                    "skip": 0,
                    "selectedPrevYearCount": $("#kostraYearsCountDropdown").data("kendoDropDownList") ? $("#kostraYearsCountDropdown").data("kendoDropDownList").value() : '4',
                    "filteredIndicator": ''
                };
                //var finalGridJson = JSON.stringify(gridObject);
                this.$http.post<any>("../api/KostraWidgetSetupAPIController/GetKostraWidgetGridDataColumns", angular.toJson(gridObject)).then(responseData => {
                    let response = responseData.data;
                    this.gridColumns = response.columns;

                    for (var i = 0; i < this.gridColumns.length; i++) {
                        let columnField = this.gridColumns[i].field;
                        switch (columnField) {
                            case "indicatorDescription":
                                this.gridColumns[i]["filterable"] = {
                                    cell: {
                                        operator: "contains",
                                        enabled: true,
                                        showOperators: false,
                                        minLength: 3,
                                        delay: 1000
                                    }
                                };
                                break;
                        }
                    }

                    if ($("#indicatorsSelectingGrid").data("kendoGrid")) {
                        $("#indicatorsSelectingGrid").data("kendoGrid").destroy();
                        $("#indicatorsSelectingGrid").empty();
                    }
                    $("#indicatorsSelectingGrid").kendoGrid({
                        dataSource: {
                            type: "json",
                            serverPaging: true,
                            serverFiltering : true,
                            pageSize: 15,
                            transport: {
                                read: (e) => {
                                    EVENTS.loaderOFF("#commanTab1Content");
                                    EVENTS.loaderON("#commanTab1Content");
                                    gridObject.skip = e.data.skip;
                                    gridObject.take = e.data.take;

                                    if (e.data.skip === undefined) {
                                        gridObject.skip = 0;
                                    }
                                    if (e.data.take === undefined) {
                                        gridObject.take = this.totalIndicatorRecords;
                                    }

                                    if (e.data.filter !== undefined) {
                                        if (e.data.filter.filters.length === 0) {
                                            gridObject.filteredIndicator = '';
                                        } else {
                                            let filterValue = e.data.filter.filters[0];
                                            if (filterValue.field === "indicatorDescription") {
                                                gridObject.filteredIndicator = filterValue.value;
                                            }
                                        }
                                    }

                                    this.$http.post<any>("../api/KostraWidgetSetupAPIController/GetKostraWidgetGridData", gridObject)
                                        .then(response => {
                                            EVENTS.loaderOFF("#commanTab1Content");
                                            let responseData: any = response.data;
                                            e.success(responseData);
                                            var html = $('#indicatorsSelectingGrid');
                                            this.$compile(html)(this.$scope);
                                            this.$scope.$apply();

                                        }, error => {
                                            var methodName = 'getKostraGrid1Data';
                                            ERROR.displayException(error, error.textStatus, error.textStatus, methodName);
                                            EVENTS.loaderOFF("#commanTab1Content");
                                        });
                                }
                            },
                            schema: {
                                model: {
                                    fields: {
                                    }
                                },
                                data: (response) => {
                                    return this.formatIndicatorValues(response.data);
                                },
                                total: (response) => {
                                    this.totalIndicatorRecords = response.totalRecords;
                                    return response.totalRecords;
                                }
                            }
                        },
                        dataBound: (ev) => {
                            $('#indicatorsSelectingGrid').find('table').removeAttr('tabindex');

                        },
                        columns: this.gridColumns,
                        sortable: true,
                        filterable: {
                            mode: "row"
                        },
                        navigatable: true,
                        height: 432,
                        scrollable: true,
                        pageable: {
                            pageSizes: [15, 25, 40, "all"],
                            pageSize: 15,
                            buttonCount: 5
                        }
                    }).data("kendoGrid");

                
                  



                }, error => {
                    EVENTS.loaderOFF("#commanTab1Content");
                    const err = { status: error.status };
                    ERROR.displayException(err, error.statusText, error.data, "getYearsDropdown");
                });

            }
            else {
                EVENTS.loaderOFF("#commanTab1Content");
                const filterMsg = [];
                filterMsg.push($("#filterMsg").text());
                MODULES.errorConfirmation(filterMsg);
                //return false;
            }
        }

        formatIndicatorValues(data) {
            for (var x in data) {
                var format = data[x].numberType;
                var values = data[x].IndicatorValues;
                for (var y in values) {
                    data[x].IndicatorValues[y] = kendo.toString(data[x].IndicatorValues[y], format);
                }
            }
            return data;
        }

        mappingIndicator(e, indicatorCode: string, indicatorId: string) {


            var id = "#active_" + indicatorId;
            if ($("#indicatorTab1").hasClass('active')) {

                let getDataSource = $('#indicatorsSelectingGrid').data('kendoGrid').dataSource.data().toJSON();
                var getDataItem = getDataSource.filter(y => y.indicatorCode === indicatorCode)[0];

                if ($(id).is(':checked')) {
                    this.chosingIndicators.push({
                        key: id,
                        value: indicatorCode
                    });
                    this.chosingSelectedData.push({
                        key: id,
                        value: getDataItem,
                        code: indicatorCode
                    });
                }
                else {
                    var ind_index = this.chosingIndicators.findIndex(x => x.key === id);
                    this.chosingIndicators.splice(ind_index, 1);//remove element from array
                    var data_index = this.chosingSelectedData.findIndex(x => x.key === id);
                    this.chosingSelectedData.splice(data_index, 1);
                }
                //this.selectedIndicators = [];
                //this.tableSelectedData = [];
                for (var x in Object.entries(this.chosingIndicators)) {
                    var ind_value = this.chosingIndicators[x].value;
                    this.selectedIndicators.push(ind_value);
                }
                for (var x in Object.entries(this.chosingSelectedData)) {
                    var data_value = this.chosingSelectedData[x].value;
                    data_value.active = true;
                    data_value.isChecked = true;
                    var isExist = this.tableSelectedData.findIndex(y => y.key === this.chosingSelectedData[x].key && y.code === this.chosingSelectedData[x].code);
                    if (isExist === -1) {
                        this.tableSelectedData.push(this.chosingSelectedData[x]);
                    }
                }
                //if (this.Grid2InitialData.length > 0) {
                //    for (var x in this.Grid2InitialData) {
                //        var doesExist = this.tableSelectedData.findIndex(y => y.key === this.Grid2InitialData[x].key);
                //        if (doesExist === -1) {
                //            this.tableSelectedData.push(this.Grid2InitialData[x]);
                //        }
                //    }
                //}

                //console.log(this.selectedIndicators);
            }
            else {
                this.finalSelectedIndicators = [];
                this.tableFinalData = [];
                this.chosingSelectedFinalData = [];
                if (this.tableSelectedData.length == 0) {
                    this.chosingSelectedFinalData = this.Grid2InitialData;
                }
                else {
                    this.chosingSelectedFinalData = this.tableSelectedData;
                }
                var item_index = this.chosingSelectedFinalData.findIndex(x => x.key === id);
                var index_data = this.chosingSelectedFinalData[item_index].value;
                if (index_data.active) {
                    index_data.active = false;
                }
                else {
                    index_data.active = true;
                }
                //this.isFirstCheck = this.isFirstCheck ? false : true;
                for (var x in Object.entries(this.chosingSelectedFinalData)) {
                    var data_value = this.chosingSelectedFinalData[x].value;
                    if (data_value.active) {
                        this.finalSelectedIndicators.push(data_value.indicatorCode);
                    }
                    this.tableFinalData.push(data_value);
                }
            }
        }
        removeDuplicates(data) {
            data.filter((v, i) => data.indexOf(v) === i);
        }
        getMunicipalitiesDropdown(isFirstLoad: boolean) {
            EVENTS.loaderON("#createTablePopupDetailsContent");
            this.$http.get<any>("../kostra/GetdefaultregionData?templateId=" + this.templateId + "&areaCode=" + "").then(responseData => {
                let response = responseData.data;
                this.reportColumnResult = response;
                this.municipalities = response.AllRegions;
                this.defaultRegions = response.DefaultRegions;
                if (isFirstLoad) {
                    this.selectedRegions = this.defaultRegions;
                }
                if (!this.kostraDocExpCitySelectorDropdown) {
                    this.kostraDocExpCitySelectorDropdown = $("#kostraMunicipalitiesDropdown").kendoMultiSelect({
                        dataTextField: "RegionName",
                        dataValueField: "RegionCode",
                        dataSource: this.municipalities,
                        filter: 'contains',
                        maxSelectedItems: 10,
                        valuePrimitive: true,
                        change: (e) => {
                            this.selectedRegions = $("#kostraMunicipalitiesDropdown").data("kendoMultiSelect").value();
                            this.isPageChange = true;
                            this.getOrderedCitiesDropdown();
                        }
                    }).data("kendoMultiSelect");
                }

                this.kostraDocExpCitySelectorDropdown.ul.addClass('hide-selected');

                this.kostraDocExpCitySelectorDropdown.tagList.kendoSortable({
                    hint: function (element) {
                        return element.clone().addClass("hint");
                    },
                    placeholder: function (element) {
                        return element.clone().addClass("placeholder").text("drop here").css("height", "30px");
                    },
                    change: (e) => {
                        this.getOrderedCitiesDropdown();
                    }
                });
                setTimeout(() => {
                    $("#kostraMunicipalitiesDropdown").data("kendoMultiSelect").value(this.selectedRegions);
                }, 50);
                EVENTS.loaderOFF("#createTablePopupDetailsContent");
            }, error => {
                EVENTS.loaderOFF("#createTablePopupDetailsContent");
                const err = { status: error.status };
                ERROR.displayException(err, error.statusText, error.data, "getMunicipalitiesDropdown");
            });
        }

        getOrderedCitiesDropdown() {
            var dropdownTextArray = [];
            let regionsPair = [], mappedRegions = [], mappedRegionCodes = [];
            $("#kostraMunicipalitiesDropdown_taglist li span:not(.k-select,.k-icon)").each(function (e) {
                dropdownTextArray.push($(this).html());
            });
            regionsPair = $("#kostraMunicipalitiesDropdown").data("kendoMultiSelect").dataItems();
            //this.selectedRegions = dropdownTextArray;
            for (var c in regionsPair) {
                mappedRegions.push({
                    key: regionsPair[c].RegionCode,
                    value: regionsPair[c].RegionName
                });
            }
            for (var x in dropdownTextArray) {
                let d = mappedRegions.findIndex(y => y.value === dropdownTextArray[x]);
                mappedRegionCodes.push(mappedRegions[d].key);
            }
            this.selectedRegions = mappedRegionCodes;
        }

        getReportAreaDropdown() {
            EVENTS.loaderON("#tabKostraWidgetContent1Wrapper1");
            this.$http.get<any>("../Kostra/GetReportingAreas").then(responseData => {
                let response = responseData.data;
                this.reportColumnResult = response;
                this.reportAreas = response.ReportingAreas;

                if (!$("#kostraWidgetRegionAreaDropdown").data("kendoDropDownTree")) {

                    $("#kostraWidgetRegionAreaDropdown").kendoDropDownTree({
                        checkAll: true,
                        checkAllTemplate: localStorage.getItem('SelectAll'),
                        checkboxes: true,
                        autoClose: false,
                        tagMode: "single",
                        dataBound: (ev) => {
                        },
                        placeholder: localStorage.getItem("selectPlaceholder"),
                        open: (ev) => {
                            $('.k-list-container.k-popup-dropdowntree').css({ 'width': '250px' });
                            $('.k-list-container.k-popup-dropdowntree').find('.k-treeview').css("overflow-y", "auto");
                        },
                        change: (e) => {
                            this.selectedReportAreas = $("#kostraWidgetRegionAreaDropdown").data("kendoDropDownTree").value();
                            //this.isPageChange = true;
                        },
                        dataSource: this.reportAreas,
                        dataTextField: "name",
                        dataValueField: "id",
                    });

                }

                setTimeout(() => {
                    let reportDropdown = $("#kostraWidgetRegionAreaDropdown").data("kendoMultiSelect");
                }, 100);
                EVENTS.loaderOFF("#tabKostraWidgetContent1Wrapper1");
            }, error => {
                EVENTS.loaderOFF("#tabKostraWidgetContent1Wrapper1");
                const err = { status: error.status };
                ERROR.displayException(err, error.statusText, error.data, "getReportAreaDropdown");
            });
        }

        getCategoryAndDataSourcesDropdown() {
            EVENTS.loaderON("#tabKostraWidgetContent1Wrapper1");
            this.$http.get<any>("../api/KostraWidgetSetupAPIController/GetCategoriesAndDataSources").then(responseData => {
                let response = responseData.data;
                this.categories = response.categories;
                this.dataSources = response.dataSources;

                if (!$("#kostraWidgetCategoryDropdown").data("kendoDropDownTree")) {

                    $("#kostraWidgetCategoryDropdown").kendoDropDownTree({
                        checkAll: true,
                        checkAllTemplate: localStorage.getItem('SelectAll'),
                        checkboxes: true,
                        autoClose: false,
                        tagMode: "single",
                        dataBound: (ev) => {
                        },
                        placeholder: localStorage.getItem("selectPlaceholder"),
                        open: (ev) => {
                            $('.k-list-container.k-popup-dropdowntree').css({ 'width': '200px' });
                            $('.k-list-container.k-popup-dropdowntree').find('.k-treeview').css("overflow-y", "auto");
                        },
                        change: (e) => {
                            this.selectedCategories = $("#kostraWidgetCategoryDropdown").data("kendoDropDownTree").value();
                            //this.isPageChange = true;
                        },
                        dataSource: this.categories,
                        dataTextField: "CategoryName",
                        dataValueField: "CategoryId"
                    });

                }


                if (!$("#kostraWidgetDataSourceDropdown").data("kendoDropDownTree")) {

                    $("#kostraWidgetDataSourceDropdown").kendoDropDownTree({
                        checkAll: true,
                        checkAllTemplate: localStorage.getItem('SelectAll'),
                        checkboxes: true,
                        autoClose: false,
                        tagMode: "single",
                        dataBound: (ev) => {
                        },
                        placeholder: localStorage.getItem("selectPlaceholder"),
                        open: (ev) => {
                            $('.k-list-container.k-popup-dropdowntree').css({ 'width': '200px' });
                            $('.k-list-container.k-popup-dropdowntree').find('.k-treeview').css("overflow-y", "auto");
                        },
                        dataTextField: "DataSourceName",
                        dataValueField: "DataSourceId",
                        dataSource: this.dataSources,
                        change: (ev) => {
                            this.selectedDataSources = $("#kostraWidgetDataSourceDropdown").data("kendoDropDownTree").value();
                            //this.isPageChange = true;
                        }

                    });

                }

                EVENTS.loaderOFF("#tabKostraWidgetContent1Wrapper1");
            }, error => {
                EVENTS.loaderOFF("#tabKostraWidgetContent1Wrapper1");
                const err = { status: error.status };
                ERROR.displayException(err, error.statusText, error.data, "getCategoryAndDataSourcesDropdown");
            });

        }
        getApprovalFlowGridTooltip(e, toolTip: string,rowId:number) {
            var toolTipId = "#toolTip_" + rowId;
            MODULES.informationTooltipClick(toolTipId, toolTip, 400, "bottom");
            //$("#infoPopup").removeClass("ng-hide");
            //$("#toolTipInfo").val(toolTip);
        }

        getTabExplanationTooltip(toolTipId: string) {
            var toolTip = $("#tabTooltip").text();
            MODULES.informationTooltipClick(toolTipId, toolTip, 500);
        }


        validateSaveAsNewPopupDetails() {
            this.newName = $("#newName").val();
            this.newCaptionText = $("#newCaptionText").val();
            if (this.newName == '' || this.newCaptionText == "") {
                const newTableMsg = [];
                newTableMsg.push($("#validateMsg").text());
                MODULES.errorConfirmation(newTableMsg);
            }
            else {
                if (this.widgetType == "0") {
                    this.saveKostraWidgetGrid(true, false);
                }
                else
                {
                    this.savePopulationWidgetData(true, false, true);
                }
                this.$scope.saveAsNewTableWindow.close();
                $("#newName").val('');
                $("#newCaptionText").val('');
            }
        }

        isMunCheck() {
            this.municipalityCheck = true;
        }
        isHisCheck() {
            this.historyCheck = true;
        }
        isDefCheck() {
            this.deflatorCheck = true;
        }
        isExpCheck() {
            this.expenseNeedCheck = true;
        }

        saveKostraWidgetGrid(copy: boolean, isFromUnsave: boolean) {

            if (!copy && !isFromUnsave) {
                $("#collapsingElement").addClass('collapsed');
                $("#createTablePopupDetails").removeClass('in');
            }
            let id: boolean = false;
            if (this.tableSelectedData.length > 0) {
                for (var x in this.tableSelectedData) {
                    var data_val = this.tableSelectedData[x].value;
                    if (data_val.active) {
                        if (this.finalSelectedIndicators.findIndex(y => y === data_val.indicatorCode) == -1) {
                            this.finalSelectedIndicators.push(data_val.indicatorCode);
                        }
                    }
                }
            }

            let saveAsNewMsg = $("#saveAsNewMsg").text();
            if (this.widgetId == '00000000-0000-0000-0000-000000000000') {
                id = true;
            }
            this.selectedPrevYearsCount = $("#kostraYearsCountDropdown").data("kendoDropDownList").value();
            const jsonData = {
                widgetId: this.widgetId,
                type: this.tableType,
                selectedYear: this.year,
                //selectedPrevYearCount: this.yearCount,
                selectedPrevYearCount: this.selectedPrevYearsCount,
                tableName: copy ? this.newName : this.editTableText,
                captionText: copy ? this.newCaptionText : this.editCaptionName,
                regions: this.selectedRegions,
                showCityGraph: this.municipalityCheckValue,
                showHistoryGraph: this.historyCheckValue,
                adjustForDeflator: this.deflatorCheckValue,
                adjustForExpenseNeed: this.expenseNeedCheckValue,
                selectedIndicators: id ? [] : this.finalSelectedIndicators,
                isCopy: copy,
                widgetType: 0,
                groupId: parseInt($("#kostraGroupTags").val() + ""),
                groupName: $("#kostraGroupTags").data("kendoMultiSelect").dataItems().length != 0 ? $("#kostraGroupTags").data("kendoMultiSelect").dataItems()[0].groupName:""
            };
            this.$http.post<any>("../api/KostraWidgetSetupAPIController/SaveKostraWidgetSetupGrid", angular.toJson(jsonData)).then(responseData => {
                if (copy) {
                    MODULES.saveConfirmation($("#copiedSuccessfully").text());
                }
                else {
                    MODULES.saveConfirmation();
                }
                let response = responseData.data;
                this.widgetId = response;
                this.reportColumnResult = response;
                this.detailsTableName = this.editTableText;
                this.municipalityCheck = false;
                this.historyCheck = false;
                this.deflatorCheck = false;
                this.expenseNeedCheck = false;
                this.detailsCaptionText = this.editCaptionName;
                this.isPageChange = false;
                this.newbtn = false;
                setTimeout(() => {
                    //this.getWidgetDetails(this.widgetId, true);
                    this.getAllTables(true);
                    if (id || this.newWidgetId == this.widgetId) {
                        this.leftMenuChange({ pk_id: this.widgetId, table_name: this.editTableText}, true);
                    }
                    if (this.selectedReportAreas.length > 0 || this.selectedCategories.length > 0 || this.selectedDataSources.length > 0 && !id) {
                        this.getKostraGrid1Data();
                    }
                    if (id) {
                        this.finalSelectedIndicators = [];

                        this.chosingIndicators = [];
                        this.chosingSelectedData = [];
                        this.selectedIndicators = [];
                        this.tableSelectedData = [];
                    }
                    this.getKostraFinalGridData();
                }, 250);
              
            }, error => {
                const err = { status: error.status };
                ERROR.displayException(err, error.statusText, error.data, "saveKostraWidgetGrid");
            });

        }

        deleteKostraTable() {
            EVENTS.loaderON("#kostraDeleteConfirmTableWindow");
            this.$http.post<any>("../api/KostraWidgetSetupAPIController/DeleteKostraWidgetTable?widgetSetupId=" + this.widgetId + "&widgetType=" + this.widgetType, null).then(responseData => {
                let response = responseData.data;
                EVENTS.loaderOFF("#kostraDeleteConfirmTableWindow");
                if (this.widgetType == "1")
                {
                    this.$scope.kostraDeleteConfirmWindow.close();
                    if (response.isDeleted)
                    {
                        MODULES.saveConfirmation("deleted successfully -NO");
                        this.showPopStatAddNew(false);
                        this.getAllTables(false);
                    }
                    else
                    {
                        this.displayDeleteMsg = response.returnMsg;
                        $("#displayDeleteMsg").text(response.returnMsg);
                        this.kostraDeleteReferenceTablePopup();
                    }
                }
                else
                {
                    var deleteMsg = $("#deleteTableMsg").text();
                    var noTableExistMsg = $("#NoTableExistMsg").text();
                    var referenceMsg = $("#tableReferenceMsg").text();
                    this.$scope.kostraDeleteConfirmWindow.close();
                    if (response.isDeleted) {
                        MODULES.saveConfirmation(deleteMsg);
                        $("#tableNewBtn").addClass("ng-hide");
                        $("#expandCollapseContentSec").addClass("align-center");
                        $("#tabExplanationText").addClass('ng-hide');
                        $("#polSimTopTabButtons").addClass('ng-hide');
                        $("#tableBtn").removeClass("ng-hide");
                        this.deleteTable = true;
                        this.editTable();
                        this.getAllTables(false);
                        this.hideKostraCollapseSection = true;
                        this.showPopStatSection = true;
                        $("#addNewWidgetSection").removeClass("ng-hide");
                        $("#addingWidgetTitleSection").addClass("ng-hide");
                        this.resetAddNewKostraWidget();
                    }
                    else {
                        this.displayDeleteMsg = response.returnMsg;
                        $("#displayDeleteMsg").text(this.displayDeleteMsg);
                        this.kostraDeleteReferenceTablePopup();
                    }
                }
                EVENTS.loaderOFF("#kostraDeleteConfirmTableWindow");
            }, error => {
                EVENTS.loaderOFF("#kostraDeleteConfirmTableWindow");
                const err = { status: error.status };
                ERROR.displayException(err, error.statusText, error.data, "deleteKostraTable");
            });
        }

        saveAsNewTablePopup(isFromUnsaved: boolean) {
            if (isFromUnsaved) {
                this.$scope.unsavedChangesWindow.close();
            }
            this.$scope.saveAsNewTableWindow = <kendo.ui.Window>$('#kostraTooltipWindow').kendoWindow({
                modal: true,
                actions: ["close"],
                scrollable: true,
                resizable: false,
                draggable: false,
                width: "50%"
            }).data("kendoWindow").center().open();
            this.$scope.saveAsNewTableWindow.element.prev().remove();
            this.$scope.saveAsNewTableWindow.element.parent().addClass('border0');
            $("#closeKostraTablesTooltipBox").click(() => {
                this.$scope.saveAsNewTableWindow.close();
            });
        }      

        kostraDeleteReferenceTablePopup() {
            this.$scope.kostraDeleteWindow = <kendo.ui.Window>$('#kostraDeleteTableWindow').kendoWindow({
                modal: true,
                actions: ["close"],
                scrollable: true,
                resizable: false,
                draggable: false,
                width: "50%"
            }).data("kendoWindow").center().open();
            this.$scope.kostraDeleteWindow.element.prev().remove();
            this.$scope.kostraDeleteWindow.element.parent().addClass('border0');
            $("#closeDeleteTableWindow").click(() => {
                this.$scope.kostraDeleteWindow.close();
            });
        }

        kostraDeleteConfirmTablePopup() {
            this.$scope.kostraDeleteConfirmWindow = <kendo.ui.Window>$('#kostraDeleteConfirmTableWindow').kendoWindow({
                modal: true,
                actions: ["close"],
                scrollable: true,
                resizable: false,
                draggable: false,
                width: "50%"
            }).data("kendoWindow").center().open();
            this.$scope.kostraDeleteConfirmWindow.element.prev().remove();
            this.$scope.kostraDeleteConfirmWindow.element.parent().addClass('border0');
            $("#deleteConfirmTableWindow").click(() => {
                EVENTS.loaderON("#kostraDeleteConfirmTableWindow");
                this.deleteKostraTable();
            });

            $("#closeDeleteConfirmTableWindow").click(() => {
                this.$scope.kostraDeleteConfirmWindow.close();
            });
        }

        openInfoPopup() {
            this.infoPopupDialog = <kendo.ui.Window>$('#kostraTablesInfoWindow').kendoWindow({
                modal: true,
                actions: ["close"],
                scrollable: true,
                resizable: false,
                draggable: false,
                width: "50%"
            }).data("kendoWindow").center().open();
            this.infoPopupDialog.element.prev().remove();
            this.infoPopupDialog.element.parent().addClass('border0 plan-banner-background');
            $("#closeKostraTablesInfoBox").click(() => {
                this.infoPopupDialog.close();
            });
        }

        leftMenuChange = function (param, isNewTable: boolean) {
            $('ul#popStatWidgetsCollapseSection').find('li.active').removeClass('active');
            this.showPopStatSection = false;
            this.popStatWidget = false;
            this.showPopStatCollapseSection = false;
            this.hideKostraCollapseSection = false;
            this.widgetType = "0";
            this.tabToSwitch = param;
            this.selectedPrevYearsCount = isNewTable ? this.selectedPrevYearsCount : param.selectedPrevYearCount;
            if (!isNewTable) {
                $("#tabExplanationText").removeClass('ng-hide');
                $("#polSimTopTabButtons").removeClass('ng-hide');
                $("#collapsingElement").addClass('collapsed');
                $("#createTablePopupDetails").removeClass('in');
                $("#tableNewBtn").removeClass("ng-hide");
            }
            this.tn = this.editTableText != "" ? (this.editTableText != this.detailsTableName) : false;
            this.mc = this.municipalityCheck;
            this.hc = this.historyCheck;
            this.dc = this.deflatorCheck;
            this.enc = this.expenseNeedCheck;
            this.ct = this.editCaptionName != "" ? (this.editCaptionName != this.detailsCaptionText) : false;
            this.newbtn = $("#tableBtn").hasClass('ng-hide');
            //$("#captionText").hasClass('ng-touched');
            this.getkotraGrid1Columns();           
            if ((this.tn || this.mc || this.hc || this.dc || this.enc || this.ct || this.isPageChange) && this.newbtn) {
                this.isPageChange = false;
                if (!isNewTable && !this.deleteTable) {
                    this.deleteTable = false;
                    let firstSave = true;
                    this.$scope.unsavedChangesWindow = <kendo.ui.Window>$('#kostraUnsavedChangesWindow').kendoWindow({
                        modal: true,
                        actions: ["close"],
                        scrollable: true,
                        resizable: false,
                        draggable: false,
                        width: "50%"
                    }).data("kendoWindow").center().open();
                    this.$scope.unsavedChangesWindow.element.prev().remove();
                    this.$scope.unsavedChangesWindow.element.parent().addClass('border0');
                    $("#unsavedSaveKostraTableAsNew").click(() => {
                        if (firstSave) {
                            firstSave = false;
                            this.saveKostraWidgetGrid(false, true);
                            //$("#PSLeftMenuSection ul li").removeClass('active');
                            this.$scope.unsavedChangesWindow.close();
                            this.switchToAnotherTable(param);
                        //    var index = this.leftMenuList.findIndex(x => x.widgetId === param.pk_id);
                        //    $("#PSLeftMenuSection ul  #" + index).addClass('active');
                        }
                    });
                    $("#closeUnsavedChangesWindow").click(() => {
                        this.$scope.unsavedChangesWindow.close();
                        //$("#PSLeftMenuSection ul li").removeClass('active');
                        var ele = { pk_id: this.widgetId, table_name: this.detailsTableName, caption_text: this.detailsCaptionText, selected_year: this.year };
                        //var index = this.leftMenuList.findIndex(x => x.widgetId === this.widgetId);
                        //$("#PSLeftMenuSection ul  #" + index).addClass('active');
                        this.switchToAnotherTable(param);
                    });
                }
            }
            else {
                this.switchToAnotherTable(param);
            }
        }

        switchToAnotherTable(param) {
            this.activeLefttab = param;
            this.getWidgetDetails(param.pk_id, false);
            this.chosingIndicators = [];
            this.chosingSelectedData = [];
            this.selectedIndicators = [];
            this.tableSelectedData = [];
            $("#tableBtn").addClass("ng-hide");
            $("#expandCollapseContentSec").removeClass("align-center");
            $("#createTablePopup").removeClass("ng-hide");
            $("#saveBtn").addClass("ng-hide");
            $("#uprightBtn").removeClass("ng-hide");
            $("#saveTablePopup").removeClass('ng-hide');
            $("#detailsTableBtn").removeClass('ng-hide');
            if (this.firstLoadOfTable) {
                this.firstLoadOfTable = false;
                this.indicatorSearch();
                this.getReportAreaDropdown();
                this.getCategoryAndDataSourcesDropdown();
            }
            if ($("#kostraWidgetRegionAreaDropdown").data("kendoDropDownTree")) {
                $("#kostraWidgetRegionAreaDropdown").data("kendoDropDownTree").value('');
            }
            if ($("#kostraWidgetCategoryDropdown").data("kendoDropDownTree")) {
                $("#kostraWidgetCategoryDropdown").data("kendoDropDownTree").value('');
            }
            if ($("#kostraWidgetDataSourceDropdown").data("kendoDropDownTree")) {
                $("#kostraWidgetDataSourceDropdown").data("kendoDropDownTree").value('');
            }
            this.selectedReportAreas = [];
            this.selectedCategories = [];
            this.selectedDataSources = [];
            $('#indicatorsSelectingGrid').empty();
            $("#noDataMsg").removeClass('ng-hide');
           // $("#tab1Data").addClass('ng-hide');
            //$('#indicatorsFinalGrid').empty();
            this.showIndicatorSearch = false;
            if ($("#indicatorSearchName").data("kendoDropDownTree")) {
                $("#indicatorSearchName").data("kendoDropDownTree").value('');
            }
            if ($("#indicatorSearchName").data("kendoDropDownTree")) {
                $("#indicatorSearchName").data("kendoDropDownTree").dataSource.data('');
            }
            //this.getKostraFinalGridData();
        }

        savingIndicatorsFromDropdown() {
            EVENTS.loaderON("#indicatorSearchDropdown");
            this.finalIndicatorsFromDropdown = $('#indicatorSearchName').data("kendoDropDownTree").value();
            let indicatorsSelected = [];
            for (var x in this.finalIndicatorsFromDropdown) {
                indicatorsSelected.push(this.finalIndicatorsFromDropdown[x]);
            }
            var selectIndInput = {
                "indicators": indicatorsSelected,
                "widgetId": this.widgetId
            }
            this.$http.post<any>("../api/KostraWidgetSetupAPIController/SaveIndicatorsFromDropdown", angular.toJson(selectIndInput)).then(responseData => {
                let response = responseData.data;
                MODULES.saveConfirmation();
                this.isPageChange = false;
                setTimeout(() => {
                    this.getWidgetDetails(this.widgetId, false);
                    this.getKostraFinalGridData();
                }, 200);
                EVENTS.loaderOFF("#indicatorSearchDropdown");

            }, error => {
                EVENTS.loaderOFF("#indicatorSearchDropdown");
                const err = { status: error.status };
                ERROR.displayException(err, error.statusText, error.data, "savingIndicatorsFromDropdown");
            });
        }

        displayIndicatorSearch() {
            $("#indicatorSearchBtn").addClass('ng-hide');
            $("#indicatorSearchDropdown").removeClass('ng-hide');
            setTimeout(() => {
                $('#indicatorSearchName').data('kendoDropDownTree').open();
            }, 50);
        }

        indicatorSearch() {
            let saveMsg = $("#saveAsNewMsg").text();
            let searchPlaceholder = $("#searchPlaceholder").text();
            let noDataFound = $("#noDataFound").text();

            if (!$("#indicatorSearchName").data("kendoDropDownTree")) {

                $("#indicatorSearchName").kendoDropDownTree({
                    checkboxes: true,
                    footerTemplate: '<div style="text-align:center;margin-right: 30px;" class="pull-right bottom10 top10"><button class="btn btn-primary searchFilter" ng-click="vm.savingIndicatorsFromDropdown();"   id="saveIndicatorButton">' + localStorage.getItem('saveBtnPlaceHolder') + '</button></div>',
                    noDataTemplate: noDataFound,
                    //placeholder: searchPlaceholder,
                    autoClose: false,
                    tagMode: "single",
                    dataBound: (ev) => {
                    },
                    open: (ev) => {
                        $('.k-list-container.k-popup-dropdowntree').css({ 'width': '250px' });
                        $('.k-list-container.k-popup-dropdowntree').find('.k-treeview').css("overflow-y", "auto");
                        $('.k-list-container.k-popup-dropdowntree').find('.k-textbox').attr("placeholder", searchPlaceholder);
                    },
                    change: (e) => {
                        const se = e.sender.value();
                        for (var x in this.indicatorList) {
                            if (this.indicatorList[x].indicatorcode == se) {
                                this.indicatorList[x].active = this.indicatorList[x].active ? false : true;
                                if (this.indicatorList[x].active) {
                                    this.selectedIndicatorsFromDropdown.push({ key: se, value: se });
                                }
                                else {
                                    var ind_index = this.selectedIndicatorsFromDropdown.findIndex(x => x.key === se);
                                    this.selectedIndicatorsFromDropdown.splice(ind_index, 1);//remove element from array
                                }
                            }
                        }
                        $('#indicatorSearchName').data("kendoDropDownTree").value();
                        this.isPageChange = true;
                    },
                    //  loadOnDemand: true,
                    dataSource: [],
                    dataTextField: "description",
                    dataValueField: "indicatorcode",
                    filter: "contains",
                    minLength: 3,
                    close: (ev) => {
                        if ($('#indicatorSearchName').data("kendoDropDownTree").value() == '' || $('#indicatorSearchName').data("kendoDropDownTree").value().length == 0)
                        {
                            $("#indicatorSearchBtn").removeClass('ng-hide');
                            $("#indicatorSearchDropdown").addClass('ng-hide');
                        }
                    },
                    filtering: (ev) => {

                        setTimeout(() => {
                            $('.k-list-container.k-popup-dropdowntree').find('.k-treeview-lines').find('.k-checkbox').attr('name', 'psChildNodes');
                        }, 20);
                        if (ev.filter.value.length >= 3){
                            this.loadindicator(ev.filter.value);
                         }
                    }
                });

            }

            //}, error => {
            //        const err = { status: error.status };
            //    ERROR.displayException(err, error.statusText, error.data, "getCategoryDropdown");
            //});
            // $("#indicators").removeClass("ng-hide");
        }
        loadindicator(searchval) {
            EVENTS.loaderON("#indicatorSearchDropdown");
            var object = {
                "searchText": searchval,
                "areaCode": '',
                "templateId": ''
            };
            //var finalJson = JSON.stringify(object);
            this.$http.post<any>("../Kostra/IndicatorSearch", angular.toJson(object)).then(responseData => {
                let response = responseData.data;
                this.indicatorList = response.Data;
                setTimeout(() => {
                    var dataSource = new kendo.data.HierarchicalDataSource({
                        data: this.indicatorList
                    });
                    var multiselect = $("#indicatorSearchName").data("kendoDropDownTree");
                    multiselect.setDataSource(dataSource);
                    $("#saveIndicatorButton").click(() => {
                        this.savingIndicatorsFromDropdown();
                    });

                }, 500);

                EVENTS.loaderOFF("#indicatorSearchDropdown");
            }, error => {
                EVENTS.loaderOFF("#indicatorSearchDropdown");
                const err = { status: error.status };
                ERROR.displayException(err, error.statusText, error.data, "getIndicatorsDropdown");
            });
        }
        expandCollapseEditor(flag: boolean) {

            if (flag === true) {
                this.showCollapseIcon = flag;
                $('#expandCollapseSec').addClass('ng-hide');
                $('#expandCollapseContentSec').removeClass('col-md-10').addClass('col-md-12 cmn-width-96p');
            }
            else {
                this.showCollapseIcon = flag;
                $('#expandCollapseSec').removeClass('ng-hide');
                $('#expandCollapseContentSec').removeClass('col-md-12 cmn-width-96p').addClass('col-md-10');
            }
            this.isExpandCollapse = true;

            // adjust grid widths from selected common tab
            const tab = this.commonTab;
            if (tab != "") {
                if (this.activeMaintab == "mainTab1") {
                    switch (tab) {
                        case "commanTab1":
                            break;
                        case "commanTab2":
                            break;
                    }
                }
            }
        }
    }

    angular.module("KostraTablesDetailApp", ["kendo.directives"]).config(['$httpProvider', function ($httpProvider) {
        $httpProvider.interceptors.push(() => {
            return {
                'request': function (config) {
                    return MODULES.validateHttpRequest(config);
                }
            };
        });
    }])
        .controller("KostraTablesDetailController", ['$scope', '$http', '$compile', function ($scope, $http, $compile) {
            tooltipSter(".plan-search-input");
            tooltipSter(".information-tooltip");
            tooltipSter(".create-myplan-tooltip");
            return new KostraWidgetSetup($scope, $http, $compile);
        }]);
}
