var app = angular.module('accountStatementApp', ["kendo.directives"]);
var orgIdLevel1,
    orgIdLevel2,
    orgIdLevel3,
    orgIdLevel4,
    orgIdLevel5,
    orgMenuLevel,
    orgMenuId,
    orgName,
    orgLastLevel,
    budgetChangesHistoryJsonObj,
    accStatementTreeGrid = '',
    budgetPeriodTreelist = '',
    getAccountYtdPopupDlg = '',
    accountReportingSearchGrid,
    deptCodeArr = [],
    funcCodeArr = [],
    projectCodeArr = [],
    freeDim1Arr = [],
    freeDim2Arr = [],
    nextOrgArr = [],
    serviceAreaIdArr = [],
    accountReportingGrid = '',
    accountReportingTreeGrid = '',
    accountReportingDeviationGrid = '',
    columnSelectorToolTip, ASBarGraph2Dialogue,
    timerForAccStateRepYearClicks,
    getBudgetBarGraph1response = false,
    getBudgetBarGraph2response = false,
    getBudgetLineGraph1response = false,
    getBudgetLineGraph2response = false,
  //  getAbsenceBarGraph1response = false,
  //  getAbsenceBarGraph2response = false,
 //   getAbsenceBarGraph3response = false,
 //   getAbsencePieGraph1response = false,
    budgetPeriodDeptData = [],
    budgetPeriodFuncData = [],
    budgetPeriodProjData = [],
    budgetPeriodFreeDim1Data = [],
    budgetPeriodFreeDim2Data = [],
    budgetPeriodNextOrgData = [],
    budgetPeriodServiceIdData = [],
    multiBudDropDeptArray = [],
    multiBudDropFuncArray = [],
    multiBudDropProjChangeArray = [],
    multiBudDropfreeDim1Array = [],
    multiBudDropfreeDim2Array = [],
    multiBudDropNextOrgArray = [],
    multiBudDropServiceIdArray = [],
    deptCodeBudMultiSelect = '',
    funcCodeBudMultiSelect = '',
    projectCodeBudMultiSelect = '',
    freeDim1BudMultiSelect = '',
    freeDim2BudMultiSelect = '',
    nextOrgMultiSelect = '',
    nextOrgCodeBud = '',
    serviceIdMultiSelect = '',
    serviceIdBud = [],
    deptCodeBud = [],
    funcCodeBud = [],
    projectCodeBud = [],
    freeDim1Bud = [],
    freeDim2Bud = [],
    timerForAccountingStatmentTabCLoad = '',timerForAccountingStatmentTabBLoad='',
    tooltipID = '', indicatorToolTipID = '',
    searchBtnTxt = localStorage.getItem("searchString");
app.config(['$httpProvider', function ($httpProvider) {
    //initialize get if not there
    if (!$httpProvider.defaults.headers.get) {
        $httpProvider.defaults.headers.get = {};
    }
    //disable IE ajax request caching
    $httpProvider.defaults.headers.get['If-Modified-Since'] = 'Mon, 26 Jul 1997 05:00:00 GMT';
    $httpProvider.defaults.headers.get['Cache-Control'] = 'no-cache';
    $httpProvider.defaults.headers.get['Pragma'] = 'no-cache';

    $httpProvider.interceptors.push(() => {
        return {
            'request': function (config) {
                return MODULES.validateHttpRequest(config);
            }
        };
    });
}]);

app.config(['$controllerProvider', function ($controllerProvider) {
    app.controllerProvider = $controllerProvider;
}]);

// factory mehtod holds the input objects
app.factory('getInputObj', function () {
    return {
        monthYear: '',
        orgIdLevel1: '',
        orgIdLevel2: '',
        orgIdLevel3: '',
        orgIdLevel4: '',
        orgIdLevel5: '',
        orgMenuId: '',
        orgMenuLevel: '',
        orgName: ''
    };
}
);

app.controller('accountStatementController', ['$http', '$scope', '$compile', '$timeout', 'getInputObj', function ($http, $scope, $compile, $timeout, getInputObj) {
    var multiAccDropDeptArray = [],
        multiAccDropFuncArray = [],
        multiAccDropProjChangeArray = [],
        multiAccDropfreeDim1Array = [],
        multiAccDropfreeDim2Array = [],
        multiAccDropNextOrgArray = [],
        multiAccDropServiceIdArray = [],
        accStatementDeptData = [],
        accStatementFuncData = [],
        accStatementProjData = [],
        accStatementFreeDim1Data = [],
        accStatementFreeDim2Data = [],
        accStatementNextOrgData = [],
        accStatementServiceIdData = [],
        deptCodeMultiSelect = '',
        funcCodeMultiSelect = '',
        projectCodeMultiSelect = '',
        freeDim1MultiSelect = '',
        freeDim2MultiSelect = '',
        budPeriodNextOrgMultiSelect = '',
        budPeriodServiceIdMultiSelect = '';
    var vm = this;
    vm.selectedAccountDetailId = '';
    vm.selectedAccountDetailName = '';
    vm.graphOrgFilter = '';
    vm.graphAccFilter = '';
    vm.forceRefreshFlag = false;
    vm.isFilteredBarGraph1 = false;
    vm.isFilteredBarGraph2 = false;
    vm.zoomText = false; // chart zoom text false by default
    vm.showAccStatementSAS = true;
    vm.showFreedim2 = false;
    vm.showNextOrg = false;
    vm.showNextOrg1 = false;
    vm.showServiceId = false;
    vm.showBPServiceId = false;
    $scope.jsonInputObject = getInputObj;
    vm.accPeriodData = '';
    vm.accountPeriodData = '';
    vm.budgetHistoryPopupSelectedId = "";
    vm.budgetHistoryPopupSelectedPeriodCheck = "";
    vm.budgetHistoryPopupSelectedAccountName = "";
    vm.monthYearSelector = function () {
        var currentDate = vm.getDate();
        var responsePromise = $http.get("../AccountStatementReport/GetMonthYearFilter?jsDateTime=" + currentDate);

        responsePromise.success(function (response, status, headers, config) {
            if (localStorage.getItem("ASYearSelectorVal")) {
                var selectedVal = localStorage.getItem("ASYearSelectorVal");
            }
            else {
                for (var g = 0; g < response.length; g++) {
                    if (response[g].isChecked == true) {
                        var selectedVal = response[g].key;
                        localStorage.setItem("ASYearSelectorVal", selectedVal);
                    }
                }
            }
            if (response.length != 0) {
                var monthYearDropList = $("#accountStatementYearSelector")
                    .kendoDropDownList({
                        dataTextField: "value",
                        dataValueField: "key",
                        dataSource: response,
                        change: function (e) {
                            clearInterval(timerForAccStateRepYearClicks);
                            getBudgetBarGraph1response = false;
                            getBudgetBarGraph2response = false;
                            getBudgetLineGraph1response = false;
                            getBudgetLineGraph2response = false;

                          /*  getAbsenceBarGraph1response = false;
                            getAbsenceBarGraph2response = false;
                            getAbsenceBarGraph3response = false;
                            getAbsencePieGraph1response = false; */

                            vm.selectedMonthValue = $("#accountStatementYearSelector").data("kendoDropDownList").value();
                            localStorage.setItem("ASYearSelectorVal", vm.selectedMonthValue);
                            getInputObj.monthYear = vm.selectedMonthValue;
                            
                            vm.forceRefreshFlag = true;
                            ORGMENU.orgStructureLoad(vm.selectedMonthValue, false);
                        },
                        value: selectedVal,
                        enable: true
                    }).data("kendoDropDownList");
             /*   $("#accountStatementYearSelectorWrapper").find(".k-select").css("display", "none");
                $("#accountStatementYearSelectorWrapper").find(".k-input").css("margin-top", "-2px");
                $("#accountStatementYearSelectorWrapper").find(".k-dropdown-wrap").addClass("monthly-topdrop-style");

                var totalLength = $("#accountStatementYearSelector").data("kendoDropDownList").dataSource.data().length;

                if ($("#accountStatementYearSelector").data("kendoDropDownList").select() == 0) {
                    $("#monthlyYearLeftIcon").addClass("not-active");
                    $("#monthlyYearRightIcon").removeClass("not-active");
                }*/

              /*  $("#monthlyYearRightIcon").click(function () {
                    clearInterval(timerForAccStateRepYearClicks);
                    getBudgetBarGraph1response = false;
                    getBudgetBarGraph2response = false;
                    getBudgetLineGraph1response = false;
                    getBudgetLineGraph2response = false;

                    getAbsenceBarGraph1response = false;
                    getAbsenceBarGraph2response = false;
                    getAbsenceBarGraph3response = false;
                    getAbsencePieGraph1response = false;
                    var selectRightIcon = $("#accountStatementYearSelector").data("kendoDropDownList").select();
                    var select = selectRightIcon + 1;
                    $("#accountStatementYearSelector").data("kendoDropDownList").select(select);

                    vm.selectedMonthValue = $("#accountStatementYearSelector").data("kendoDropDownList").value();
                    localStorage.setItem("ASYearSelectorVal", vm.selectedMonthValue);
                    getInputObj.monthYear = vm.selectedMonthValue;
                    if (selectRightIcon == totalLength - 2) {
                        $("#monthlyYearRightIcon").addClass("not-active");
                        $("#monthlyYearLeftIcon").removeClass("not-active");
                    }
                    else {
                        $("#monthlyYearRightIcon").removeClass("not-active");
                        $("#monthlyYearLeftIcon").removeClass("not-active");
                    }
                    vm.forceRefreshFlag = true;
                    ORGMENU.orgStructureLoad(vm.selectedMonthValue, false);
                });*/

              /*  $("#monthlyYearLeftIcon").click(function () {
                 /*   clearInterval(timerForAccStateRepYearClicks);
                    getBudgetBarGraph1response = false;
                    getBudgetBarGraph2response = false;
                    getBudgetLineGraph1response = false;
                    getBudgetLineGraph2response = false;

                    getAbsenceBarGraph1response = false;
                    getAbsenceBarGraph2response = false;
                    getAbsenceBarGraph3response = false;
                    getAbsencePieGraph1response = false;
                    var selectLeftIcon = $("#accountStatementYearSelector").data("kendoDropDownList").select();
                    var select = selectLeftIcon - 1;
                    $("#accountStatementYearSelector").data("kendoDropDownList").select(select);

                    vm.selectedMonthValue = $("#accountStatementYearSelector").data("kendoDropDownList").value();
                    localStorage.setItem("ASYearSelectorVal", vm.selectedMonthValue);
                    getInputObj.monthYear = vm.selectedMonthValue;
                    if (selectLeftIcon == 1) {
                        $("#monthlyYearLeftIcon").addClass("not-active");
                        $("#monthlyYearRightIcon").removeClass("not-active");
                    }
                    else {
                        $("#monthlyYearRightIcon").removeClass("not-active");
                        $("#monthlyYearLeftIcon").removeClass("not-active");
                    }
                    vm.forceRefreshFlag = true;
                    ORGMENU.orgStructureLoad(vm.selectedMonthValue, false);
                });*/

                ORGMENU.orgStructureLoad(selectedVal, false);
                getInputObj.monthYear = $("#accountStatementYearSelector").data("kendoDropDownList").value();
            }
        });
        responsePromise.error(function (response, status, headers, config) {
            var methodName = 'account statement - monthYearSelector';
            ERROR.displayException(response, status, response.MessageDetail, methodName);
        });
    }

    vm.tabLoadFunctionCalls = function () {
        clearInterval(timerForAccStateRepYearClicks);
        vm.getUpdatedBudgetDate();
        EVENTS.loaderON("#bannerContentWrapper");
        var activeTab = "";

        if (localStorage.getItem("ASYearActiveTab")) {
            activeTab = $("#tabSections li.active").attr('id');
            var href = $("#tabSections li.active a").attr('href');
            $("#" + activeTab).removeAttr('class', "active");
            $(href).removeAttr('class', "active");
            $(href).attr('class', "tab-pane");

            activeTab = localStorage.getItem("ASYearActiveTab");
            $("#" + activeTab).attr('class', "active");
            var hrefId = $("#tabSections li.active a").attr('href');
            $(hrefId).attr('class', "tab-pane active");
        } else {
            $("#AccountingStatmentTabA").attr('class', "active");
            $("#budgetDetails").attr('class', "tab-pane active");
        }
        activeTab = $("#tabSections li.active").attr('id');

        switch (activeTab) {
            case "AccountingStatmentTabA":
                vm.multiSelectFilterClearingData();
                vm.getAccountingFilterData();
                break;
            case "AccountingStatmentTabB":
                vm.getChartsCachedFlag();
                //Commented out caching of detail data upon page load - Bug #35492
                //vm.getBarChart2CachedFlag(); // added to call cache flag to bar chart 2 maximize
                break;
            case "AccountingStatmentTabC":
                vm.getAllAbsenseGraph();          
            /*    timerForAccountingStatmentTabCLoad = setInterval(function () {
                    if (getAbsenceBarGraph1response == true && getAbsenceBarGraph2response == true && getAbsenceBarGraph3response == true && getAbsencePieGraph1response == true) {
                        EVENTS.loaderOFF("#bannerContentWrapper");
                        clearInterval(timerForAccountingStatmentTabCLoad);
                    }
                }, 2500); */
                break;
            case "AccountingStatmentTabD":
                vm.multiSelectFilterClearingDataBudgetPeriod();
                //vm.getBudgetPeriodTreelistData();
                vm.getBudgetPeriodFilterData();
                break;
        }
    }

    $('#AccountingStatmentTabA').click(function (e) {
        EVENTS.loaderON("#bannerContentWrapper");
        vm.multiSelectFilterClearingData();
        vm.getAccountingFilterData();
        setTimeout(function () {
            var activeTab = $("#tabSections li.active").attr('id');
            localStorage.setItem("ASYearActiveTab", activeTab);
        }, 100);
    });

    $('#AccountingStatmentTabB').click(function (e) {
        getBudgetBarGraph1response = false;
        getBudgetBarGraph2response = false;
        getBudgetLineGraph1response = false;
        getBudgetLineGraph2response = false;
        EVENTS.loaderON("#bannerContentWrapper");
        vm.forceRefreshFlag = false;
        //vm.getBudgetBarGraph1();
        //vm.getBudgetBarGraph2();
        //vm.getBudgetLineGraph1();
        //vm.getBudgetLineGraph2();
        vm.getChartsCachedFlag();
        setTimeout(function () {
            var activeTab = $("#tabSections li.active").attr('id');
            localStorage.setItem("ASYearActiveTab", activeTab);
        }, 100);
    });

    $('#AccountingStatmentTabC').click(function (e) {
      //  getAbsenceBarGraph1response = false;
      //  getAbsenceBarGraph2response = false;
     //   getAbsenceBarGraph3response = false;
     //   getAbsencePieGraph1response = false;
     //   EVENTS.loaderON("#bannerContentWrapper");
          vm.getAllAbsenseGraph();
        setTimeout(function () {
            var activeTab = $("#tabSections li.active").attr('id');
            localStorage.setItem("ASYearActiveTab", activeTab);
        }, 100);
        //loader off after all charts loaded
      /*  timerForAccountingStatmentTabCLoad = setInterval(function () {
            if (getAbsenceBarGraph1response == true && getAbsenceBarGraph2response == true && getAbsenceBarGraph3response == true && getAbsencePieGraph1response == true) {
                EVENTS.loaderOFF("#bannerContentWrapper");
                clearInterval(timerForAccountingStatmentTabCLoad);
            }
        }, 2500);*/
    });

    $('#AccountingStatmentTabD').click(function (e) {
        EVENTS.loaderON("#bannerContentWrapper");
        vm.multiSelectFilterClearingDataBudgetPeriod();
        //  vm.getBudgetPeriodTreelistData();
        vm.getBudgetPeriodFilterData();
        setTimeout(function () {
            var activeTab = $("#tabSections li.active").attr('id');
            localStorage.setItem("ASYearActiveTab", activeTab);
        }, 100);
    });

    vm.getChartsCachedFlag = function () {
        // var loaderId = "#accounting_tab";
        //EVENTS.loaderON(loaderId);

        //var obj = {
        //    monthYear: $("#accountStatementYearSelector").val(),
        //    orgLevel: parseInt(localStorage.getItem("accStatementOrgMenuLevel")),
        //    orgId: orgMenuId,
        //    orgName: orgName,
        //    level1OrgId: orgIdLevel1,
        //    level2OrgId: orgIdLevel2,
        //    level3OrgId: orgIdLevel3,
        //    level4OrgId: orgIdLevel4,
        //    level5OrgId: orgIdLevel5,
        //    serviceId: "",
        //    forceRefresh: vm.forceRefreshFlag
        //};
        //var finalJSON = JSON.stringify(obj);
        //var responsePromise = $http.post("../AccountStatementReport/CacheGraphBaseData", finalJSON);
        //responsePromise.success(function (response, status, headers, config) {

        // EVENTS.loaderOFF(loaderId);
        // calling graph api calls after the response from cachedflag call
        vm.getBudgetBarGraph1();
        vm.getBudgetBarGraph2();
        vm.getBudgetLineGraph1();
        vm.getBudgetLineGraph2();

        //loader off after all charts loaded
        timerForAccountingStatmentTabBLoad = setInterval(function () {
            if (getBudgetBarGraph1response == true && getBudgetBarGraph2response == true && getBudgetLineGraph1response == true && getBudgetLineGraph2response == true) {
                EVENTS.loaderOFF("#bannerContentWrapper");
                clearInterval(timerForAccountingStatmentTabBLoad);
            }
        }, 2500);



        //});
        //responsePromise.error(function (response, status, headers, config) {
        //    var methodName = 'Account statement reporting - getChartCachedFlag';
        //    EVENTS.loaderOFF(loaderId);
        //    ERROR.displayException(response, status, response.MessageDetail, methodName);
        //});
    }

    vm.getBarChart2CachedFlag = function () {
        var loaderId = "#accounting_tab";
        EVENTS.loaderON(loaderId);

        var obj = {
            monthYear: $("#accountStatementYearSelector").val(),
            orgLevel: parseInt(localStorage.getItem("accStatementOrgMenuLevel")),
            orgId: orgMenuId,
            orgName: orgName,
            level1OrgId: orgIdLevel1,
            level2OrgId: orgIdLevel2,
            level3OrgId: orgIdLevel3,
            level4OrgId: orgIdLevel4,
            level5OrgId: orgIdLevel5,
            serviceId: "",
            forceRefresh: vm.forceRefreshFlag
        };
        var finalJSON = JSON.stringify(obj);
        var responsePromise = $http.post("../AccountStatementReport/CacheGraphBaseAccDetailsData", finalJSON);
        responsePromise.success(function (response, status, headers, config) {
            EVENTS.loaderOFF(loaderId);
        });
        responsePromise.error(function (response, status, headers, config) {
            var methodName = 'Account statement reporting - getBarChart2CachedFlag';
            EVENTS.loaderOFF(loaderId);
            ERROR.displayException(response, status, response.MessageDetail, methodName);
        });
    }

    // Accounting budget graphs
    vm.getBudgetBarGraph1 = function () {
        var graphId = "#accountStatementBarGraph1";
        EVENTS.loaderON(graphId);

        var obj = {
            monthYear: $("#accountStatementYearSelector").val(),
            orgLevel: parseInt(localStorage.getItem("accStatementOrgMenuLevel")),
            orgId: orgMenuId,
            orgName: orgName,
            level1OrgId: orgIdLevel1,
            level2OrgId: orgIdLevel2,
            level3OrgId: orgIdLevel3,
            level4OrgId: orgIdLevel4,
            level5OrgId: orgIdLevel5,
            serviceId: "",
            inGrFltrOrg: vm.graphOrgFilter,
            inGrFltrAcGrp: vm.graphAccFilter
        };
        var finalJSON = JSON.stringify(obj);
        var responsePromise = $http.post("../AccountStatementReport/GetAccPnLGraphBudPeOrg", finalJSON);
        responsePromise.success(function (response, status, headers, config) {
            var cleanString = DOMPurify.sanitize(response.header.title,domPurifyConfig);                  
            $("#accountStatementBarGraph1Title").html(cleanString);
            $('#accountStatementBarGraph1TitleDesc img').click(function (e) {
                MODULES.informationTooltipClick('#accountStatementBarGraph1TitleDesc', response.header.tooltipdesc);
            });
            var cleanString = DOMPurify.sanitize(response.header.amtinunits,domPurifyConfig);                  
            $("#amtinunits1").html(cleanString);
            var cleanString = DOMPurify.sanitize(response.header.totalbudev,domPurifyConfig);                  
            $("#totalbudev").html(cleanString);
            vm.zoomTip = '';
            if (response.header.zoomtip != "") {
                vm.zoomText = true;
                //$("#chartZoomText").html(response.header.zoomtip);
                vm.zoomTip = response.header.zoomtip;
            } else {
                vm.zoomText = false;
            }

            EVENTS.loaderOFF(graphId);
            vm.isFilteredBarGraph1 = response.header.isfiltered;
            if (response.header.isfiltered === false) {
                response.chart.seriesClick = onSeriesClickGraph1;
            } else {
                response.chart.seriesClick = onSeriesClickFilteredGraph;
            }
            response.chart.seriesHover = function () {
                $("#accountStatementBarGraph1").css("cursor", "pointer");
            }
            //response.chart.dataBound = onBarGraph1DataBound();
            //response.chart.valueAxis.labels.rotation = -45;


            //response.chart.tooltip.background = "#e8e8e8";
            //response.chart.tooltip.border = {
            //    width: 1,
            //    color: "#e8e8e8"
            //}
            //response.chart.tooltip.padding = {
            //    right: -10
            //}

            //response.chart.tooltip.template = "<div style='width:400px;height:auto;white-space:normal;max-height:150px;overflow-Y:auto;'><div style='text-align:left;'><div>#= dataItem.name # : #= dataItem.deviationheading # : #= (kendo.toString(dataItem.deviation,'n0')) #</div><p><p><u> #if(dataItem.actionPlanDeviation){ # #= dataItem.actionplandeviationheading # : #}#</u><p><div>#= dataItem.actionPlanDeviation #</div><p><u>#if(dataItem.finplanDeviation){ # #= dataItem.finplandeviationheading # : #}#</u><p><div>#= dataItem.finplanDeviation #</div><u>#if(dataItem.forecastComment){ # #= dataItem.forecastcommentheading # : # } #</u><p><div>#= dataItem.forecastComment #</div><u>#if(dataItem.forecastReportComment){ # #= dataItem.forecastreportcommentheading # : # } #</u><p><div>#= dataItem.forecastReportComment #</div></div></div>"
            //for (var b = 0; b < response.seriesdata.length; b++) {
            //    response.seriesdata[b].desc = b+"test data test data test data test data test data test data test data test data test data test data test data test data test data test data test data<p>test data test data test data test data test data test data test data test data test data test data test data test data test data test data test data<p>test data test data test data test data test data test data test data test data test data test data test data test data test data test data test data<p>test data test data test data test data test data test data test data test data test data test data test data test data test data test data test data<p>";
            //}
            //console.log(response.seriesdata);
            //console.log(response.chart);


            $("#accountStatementBarGraph1").kendoChart(response.chart);
            $("#accountStatementBarGraph1").data("kendoChart").dataSource.data(response.seriesdata);

            $("#accountStatementBarGraph1").css("height", "500px");

            // if(response.chart.series.length > 0) {
            //     var chart = $("#accountStatementBarGraph1").data("kendoChart");
            //     chart.bind("dataBound", onBarGraph1DataBound);
            //     chart.dataSource.fetch();
            // }
            getBudgetBarGraph1response = true;

        });
        responsePromise.error(function (response, status, headers, config) {
            EVENTS.loaderOFF("#bannerContentWrapper");
            var methodName = 'Account statement reporting - getBudgetBarGraph1';
            EVENTS.loaderOFF(graphId);
            ERROR.displayException(response, status, response.MessageDetail, methodName);
        });
    }

    vm.getBudgetBarGraph2 = function () {
        var graphId = "#accountStatementBarGraph2";
        EVENTS.loaderON(graphId);

        var obj = {
            monthYear: $("#accountStatementYearSelector").val(),
            orgLevel: parseInt(localStorage.getItem("accStatementOrgMenuLevel")),
            orgId: orgMenuId,
            orgName: orgName,
            level1OrgId: orgIdLevel1,
            level2OrgId: orgIdLevel2,
            level3OrgId: orgIdLevel3,
            level4OrgId: orgIdLevel4,
            level5OrgId: orgIdLevel5,
            serviceId: "",
            inGrFltrOrg: vm.graphOrgFilter,
            inGrFltrAcGrp: vm.graphAccFilter,
            isAccDetailsGraph: false
        };
        var finalJSON = JSON.stringify(obj);
        var responsePromise = $http.post("../AccountStatementReport/GetAccPnLGraphBudDevPeLevelGrp", finalJSON);
        responsePromise.success(function (response, status, headers, config) {
            var cleanString = DOMPurify.sanitize(response.header.title,domPurifyConfig);                  
            $("#accountStatementBarGraph2Title").html(cleanString);
            $('#accountStatementBarGraph2TitleDesc img').click(function (e) {
                MODULES.informationTooltipClick('#accountStatementBarGraph2TitleDesc', response.header.tooltipdesc);
            });
            var cleanString = DOMPurify.sanitize(response.header.amtinunits,domPurifyConfig);                  
            $("#amtinunits3").html(cleanString);

            EVENTS.loaderOFF(graphId);
            vm.isFilteredBarGraph2 = response.header.isfiltered;
            if (response.header.isfiltered === false) {
                response.chart.seriesClick = onSeriesClickGraph2;
            } else {
                response.chart.seriesClick = onSeriesClickFilteredGraph;
            }
            response.chart.seriesHover = function () {
                $("#accountStatementBarGraph2").css("cursor", "pointer");
            }
            response.chart.zoomable = { "mousewheel": { "lock": "x" }, "selection": { "lock": "x" } };
            response.chart.pannable = { "lock": "x" };
            response.chart.valueAxis.labels.rotation = -45;
            // response.chart.zoomable = function {
            //     mousewheel: true
            // response.chart.dataBound = onBarGraph1DataBound();
            $("#accountStatementBarGraph2").kendoChart(response.chart);
            $("#accountStatementBarGraph2").css("height", "500px");
            // if(response.chart.series.length > 0) {
            //     var chart = $("#accountStatementBarGraph2").data("kendoChart");
            //     chart.bind("dataBound", onBarGraph2DataBound);
            //     chart.dataSource.fetch();
            // }
            getBudgetBarGraph2response = true;
        });
        responsePromise.error(function (response, status, headers, config) {
            EVENTS.loaderOFF("#bannerContentWrapper");
            var methodName = 'Account statement reporting - getBudgetBarGraph2';
            EVENTS.loaderOFF(graphId);
            ERROR.displayException(response, status, response.MessageDetail, methodName);
        });
    }

    vm.getBudgetLineGraph1 = function () {
        var graphId = "#accountStatementLineGraph1";
        EVENTS.loaderON(graphId);

        var obj = {
            monthYear: $("#accountStatementYearSelector").val(),
            orgLevel: parseInt(localStorage.getItem("accStatementOrgMenuLevel")),
            orgId: orgMenuId,
            orgName: orgName,
            level1OrgId: orgIdLevel1,
            level2OrgId: orgIdLevel2,
            level3OrgId: orgIdLevel3,
            level4OrgId: orgIdLevel4,
            level5OrgId: orgIdLevel5,
            serviceId: "",
            inGrFltrOrg: vm.graphOrgFilter,
            inGrFltrAcGrp: vm.graphAccFilter
        };
        var finalJSON = JSON.stringify(obj);
        var responsePromise = $http.post("../AccountStatementReport/GetAccPnLGraphAcuAccBud", finalJSON);
        responsePromise.success(function (response, status, headers, config) {
            var cleanString = DOMPurify.sanitize(response.header.title,domPurifyConfig);                 
            $("#accountStatementLineGraph1Title").html(cleanString);
            $('#accountStatementLineGraph1TitleDesc img').click(function (e) {
                MODULES.informationTooltipClick('#accountStatementLineGraph1TitleDesc', response.header.tooltipdesc);
            });
            var cleanString = DOMPurify.sanitize(response.header.amtinunits,domPurifyConfig);                  
            $("#amtinunits2").html(cleanString);
            EVENTS.loaderOFF(graphId);
            $("#accountStatementLineGraph1").kendoChart(response.chart);
            $("#accountStatementLineGraph1").css("height", "500px");
            $("#accountStatementLineGraph1").data("kendoChart").redraw();
            getBudgetLineGraph1response = true;
        });
        responsePromise.error(function (response, status, headers, config) {
            EVENTS.loaderOFF("#bannerContentWrapper");
            var methodName = 'Account statement reporting - getBudgetLineGraph1';
            EVENTS.loaderOFF(graphId);
            ERROR.displayException(response, status, response.MessageDetail, methodName);
        });
    }

    vm.getBudgetLineGraph2 = function () {
        var graphId = "#accountStatementLineGraph2";
        EVENTS.loaderON(graphId);

        var obj = {
            monthYear: $("#accountStatementYearSelector").val(),
            orgLevel: parseInt(localStorage.getItem("accStatementOrgMenuLevel")),
            orgId: orgMenuId,
            orgName: orgName,
            level1OrgId: orgIdLevel1,
            level2OrgId: orgIdLevel2,
            level3OrgId: orgIdLevel3,
            level4OrgId: orgIdLevel4,
            level5OrgId: orgIdLevel5,
            serviceId: "",
            inGrFltrOrg: vm.graphOrgFilter,
            inGrFltrAcGrp: vm.graphAccFilter
        };
        var finalJSON = JSON.stringify(obj);
        var responsePromise = $http.post("../AccountStatementReport/GetAccPnLGraphAccBudPerMonth", finalJSON);
        responsePromise.success(function (response, status, headers, config) {
            var cleanString = DOMPurify.sanitize(response.header.title,domPurifyConfig);                  
            $("#accountStatementLineGraph2Title").html(cleanString);
            $('#accountStatementLineGraph2TitleDesc img').click(function (e) {
                MODULES.informationTooltipClick('#accountStatementLineGraph2TitleDesc', response.header.tooltipdesc);
            });
            var cleanString = DOMPurify.sanitize(response.header.amtinunits,domPurifyConfig);                  
            $("#amtinunits4").html(cleanString);
            EVENTS.loaderOFF(graphId);
            $("#accountStatementLineGraph2").kendoChart(response.chart);
            $("#accountStatementLineGraph2").css("height", "500px");
            $("#accountStatementLineGraph2").data("kendoChart").redraw();
            getBudgetLineGraph2response = true;
        });
        responsePromise.error(function (response, status, headers, config) {
            EVENTS.loaderOFF("#bannerContentWrapper");
            var methodName = 'Account statement reporting - getBudgetLineGraph2';
            EVENTS.loaderOFF(graphId);
            ERROR.displayException(response, status, response.MessageDetail, methodName);
        });
    }

    function onSeriesClickGraph1(e) {
        //console.log("non filtered1 : ", vm.isFilteredBarGraph1);
        //console.log(kendo.format("Series click :: {0} ({1}): {2}", e.series.name, e.series.id, e.value));
        var chart = $("#accountStatementBarGraph1").data("kendoChart");

        vm.graphAccFilter = "";
        // if(vm.graphOrgFilter == "") {
        //     vm.graphOrgFilter = e.series.id;
        //     chart.options.series[e.series.index].opacity = 1;
        // } else {
        //     if(vm.graphOrgFilter == e.series.id) {
        //         vm.graphOrgFilter = "";
        //     } else {
        //         vm.graphOrgFilter = e.series.id;
        //     }
        // }
        // // to hightlight the selected graph axis
        // var opacity = (vm.graphOrgFilter !== "") ? 0.4 : 0.8;
        // for(var i=0; i<chart.options.series.length; i++) {
        //     if(chart.options.series[i].id !== vm.graphOrgFilter) {
        //         chart.options.series[i].opacity = opacity;
        //     } else {
        //         chart.options.series[i].opacity = 1;
        //     }
        // }
        if (vm.graphOrgFilter == "") {
            vm.graphOrgFilter = e.dataItem.id;
        } else {
            if (vm.graphOrgFilter == e.dataItem.id) {
                vm.graphOrgFilter = "";
            } else {
                vm.graphOrgFilter = e.dataItem.id;
            }
        }
        var color1 = "#b56666";
        var changeColor1 = "#dcb6b6";
        var color2 = "#367b9a";
        var changeColor2 = "#9dc9dd";
        for (var i = 0; i < chart.options.series[0].data.length; i++) {
            if (vm.graphOrgFilter !== "") {
                if (chart.options.series[0].data[i].id == vm.graphOrgFilter) {
                    if (e.dataItem.data > 0) {
                        chart.options.series[0].data[i].color = color2;
                    } else {
                        chart.options.series[0].data[i].color = color1;
                    }
                } else {
                    if (chart.options.series[0].data[i].data > 0) {
                        chart.options.series[0].data[i].color = changeColor2;
                    } else {
                        chart.options.series[0].data[i].color = changeColor1;
                    }
                }
            } else {
                if (chart.options.series[0].data[i].data > 0) {
                    chart.options.series[0].data[i].color = color2;
                } else {
                    chart.options.series[0].data[i].color = color1;
                }
            }
        }
        //chart.bind("seriesClick", onSeriesClickFilteredGraph);
        if ($("#accountStmtBarGraph1Container").hasClass("maximize-wrapper")) {
            vm.accountStmtBarGraph1MaximizePopup();
        }

        vm.getBudgetBarGraph2();
        vm.getBudgetLineGraph1();
        vm.getBudgetLineGraph2();

        chart.refresh();
        // console.log("org e : ", e);
    }

    function onSeriesClickGraph2(e) {
        //console.log("non filtered2 : ", vm.isFilteredBarGraph2);
        //console.log(kendo.format("Series click :: {0} ({1}): {2}", e.series.name, e.series.id, e.value));
        var chart = $("#accountStatementBarGraph2").data("kendoChart");

        vm.graphOrgFilter = "";
        if (vm.graphAccFilter == "") {
            vm.graphAccFilter = e.series.id;
            chart.options.series[e.series.index].opacity = 1;
        } else {
            if (vm.graphAccFilter == e.series.id) {
                vm.graphAccFilter = "";
            } else {
                vm.graphAccFilter = e.series.id;
            }
            //chart.refresh();
        }

        // to hightlight the selected graph axis
        var opacity = (vm.graphAccFilter !== "") ? 0.4 : 0.8;
        for (var i = 0; i < chart.options.series.length; i++) {
            if (chart.options.series[i].id !== vm.graphAccFilter) {
                chart.options.series[i].opacity = opacity;
            } else {
                chart.options.series[i].opacity = 1;
            }
        }
        //chart.bind("seriesClick", onSeriesClickFilteredGraph);

        // if($("#accountStmtBarGraph2Container").hasClass("maximize-wrapper")) {
        //     vm.accountStmtBarGraph2MaximizePopup();
        // }

        vm.getBudgetBarGraph1();
        vm.getBudgetLineGraph1();
        vm.getBudgetLineGraph2();
        //setTimeout(function() {
        chart.refresh();
        //}, 100);

        // console.log("acc e : ", e);
    }

    function onSeriesClickFilteredGraph(e) {
        //console.log("filtered1 : ", vm.isFilteredBarGraph1);
        vm.graphAccFilter = "";
        vm.graphOrgFilter = "";

        vm.getBudgetBarGraph1();
        vm.getBudgetBarGraph2();
        vm.getBudgetLineGraph1();
        vm.getBudgetLineGraph2();
    }

    // function onBarGraph1DataBound(e) {
    //     var chartID = "#accountStatementBarGraph1";
    //     //vm.barChartValueAxisChange(e, chartID);
    // }

    // function onBarGraph2DataBound(e) {
    //     var chartID = "#accountStatementBarGraph2";
    //     //vm.barChartValueAxisChange(e, chartID);
    // }

    vm.accountStmtBarGraph1MaximizePopup = function () {
        $("#accountStmtBarGraph1Container").toggleClass("maximize-wrapper");
        //$scope.currentResolution = $(window).width();
        var chart = $("#accountStatementBarGraph1").data("kendoChart");
        if ($("#accountStmtBarGraph1Container").hasClass("maximize-wrapper")) {
            //$scope.currentResolution <= 1600 ? $("#yearlyBudgetAllocationGrid").css('max-height',$(window).height() / 2 + 80) : $("#yearlyBudgetAllocationGrid").css('max-height',$(window).height() / 2 + 150);
            // $("#accountStmtBarGraph1Container").removeClass("padding15");
            $("#budgetEntrySaveContainer").css("height", "50px");
            $('body').css('overflow', 'hidden');
            $('#collapseAccountStatementBarGraph1Section').height($(window).height() - 60);
            $("#accountStatementBarGraph1").css("height", ($(window).height() - 60));
            $("#accountStmtBarGraph1Container").removeClass("padding-left0");
            $("#ASBarGraph1MaximizeScreenImg").attr("src", "../images/full_screen__minimize.png");
            // chart.options.zoomable = false;
        }
        else {
            // $("#accountStmtBarGraph1Container").addClass("padding15");
            $('body').removeAttr('style');
            $("#collapseAccountStatementBarGraph1Section").css('max-height', '500px');
            $("#accountStatementBarGraph1").css("height", "500px");
            $("#accountStmtBarGraph1Container").addClass("padding-left0");
            $("#ASBarGraph1MaximizeScreenImg").attr("src", "../images/full_screen__maximize.png");
        }
        chart.refresh();
    }

    vm.accountStmtBarGraph2MaximizePopup = function () {
        $("#accountStmtBarGraph2Container").toggleClass("maximize-wrapper");
        var chart = $("#accountStatementBarGraph2").data("kendoChart");
        if ($("#accountStmtBarGraph2Container").hasClass("maximize-wrapper")) {
            $('body').css('overflow', 'hidden');
            $('#collapseAccountStatementBarGraph2Section').height($(window).height() - 60);
            $("#accountStatementBarGraph2").css("height", ($(window).height() - 60));
            $("#accountStmtBarGraph2Container").removeClass("padding-left0");
        }
        else {
            $('body').removeAttr('style');
            $("#collapseAccountStatementBarGraph2Section").css('max-height', '500px');
            $("#accountStatementBarGraph2").css("height", "500px");
            $("#accountStmtBarGraph2Container").addClass("padding-left0");
        }
        chart.refresh();
    }

    // Accounting budget graphs
    vm.getBudgetBarGraphMaximize2 = function () {
        var graphId = "#accountStatementBarGraphMax2";
        EVENTS.loaderON(graphId);

        var obj = {
            monthYear: $("#accountStatementYearSelector").val(),
            orgLevel: parseInt(localStorage.getItem("accStatementOrgMenuLevel")),
            orgId: orgMenuId,
            orgName: orgName,
            level1OrgId: orgIdLevel1,
            level2OrgId: orgIdLevel2,
            level3OrgId: orgIdLevel3,
            level4OrgId: orgIdLevel4,
            level5OrgId: orgIdLevel5,
            serviceId: "",
            inGrFltrOrg: vm.graphOrgFilter,
            inGrFltrAcGrp: vm.graphAccFilter,
            isAccDetailsGraph: true
        };
        var finalJSON = JSON.stringify(obj);
        var responsePromise = $http.post("../AccountStatementReport/GetAccPnLGraphBudDevPeLevelGrp", finalJSON);
        responsePromise.success(function (response, status, headers, config) {
            // var response = {"chart":{"renderAs":"canvas","legend":{"visible":false},"seriesDefaults":{"type":"bar","overlay":{"gradient":"none"},"labels":{"font":"13px regularFont","position":"insideBase","visible":true,"format":"{0}","color":"#030303","background":"transparent","margin":{"left":5,"right":15},"template":"#= dataItem.name #: #=(kendo.toString(value,'n0'))#"}},"series":[{"field":"data","type":"bar"}],"valueAxis":{"color":"#c3c3c3","line":{"visible":true},"labels":{"padding":{"top":5},"rotation":-45,"color":"#606060","template":"#= (kendo.toString(value,'n0')) #"}},"categoryAxis":{"majorGridLines":{"visible":false},"labels":{"rotation":-90},"min":0,"max":6},"tooltip":{"visible":true,"format":"{0}%","template":"#= dataItem.name #: #= (kendo.toString(value,'n0')) #"},"pannable":{"lock":"x"},"zoomable":{"mousewheel":{"lock":"x"},"selection":{"lock":"x"}}},"seriesdata":[{"name":"test graph data 1","id":"3390","opacity":0.2,"data":-4663204,"color":"#b56666"},{"name":"test graph data 2","id":"3305","opacity":0.2,"data":-3862475,"color":"#b56666"},{"name":"test graph data 3","id":"3360","opacity":0.2,"data":-2294459,"color":"#b56666"},{"name":"test graph data 4","id":"3310","opacity":0.2,"data":-852321,"color":"#b56666"},{"name":"test graph data 5","id":"3320","opacity":0.2,"data":589815,"color":"#367b9a"},{"name":"test graph data 6","id":"3340","opacity":0.2,"data":886894,"color":"#367b9a"},{"name":"test graph data 7","id":"3350","opacity":0.2,"data":1339407,"color":"#367b9a"},{"name":"test graph data 8","id":"3375","opacity":0.2,"data":1788200,"color":"#367b9a"},{"name":"test graph data 9","id":"3380","opacity":0.2,"data":1806878,"color":"#367b9a"},{"name":"test graph data 10","id":"3370","opacity":0.2,"data":2476785,"color":"#367b9a"},{"name":"test graph data 11","id":"3300","opacity":0.2,"data":3250232,"color":"#367b9a"}],"header":{"title":"Budsjettavvik per hovedpost","tooltipdesc":"Budget Deviation by Account Tool tip Description - NO","amtinunits" : "Beløp i hele mill kroner", "isfiltered":false}};

            // $("#accountStatementBarGraphMax2Title").html(response.header.title);
            $('#accountStatementBarGraphMax2TitleDesc img').click(function (e) {
                MODULES.informationTooltipClick('#accountStatementBarGraphMax2TitleDesc', response.header.tooltipdesc);
            });

            ASBarGraph2Dialogue.element.prev().find(".k-window-title").html('<span id="okonomiStatusTitleParentPopup">' + response.header.title + '</span><span class="padding-left5 padding-right10" data-role="tooltip" id="ASRGMaxToolTip"><img src="../images/icons_monthly_report_information.png"></span></span><span class="marginleft40">' + response.header.totalbudev + '</span>');

            $('#ASRGMaxToolTip img').click(function (e) {
                MODULES.informationTooltipClick('#ASRGMaxToolTip', response.header.tooltipdesc);
            });

            $('#ASBarGraph2MaxWindow_wnd_title').next('.k-window-actions').before('<span id="amtinunitsmax2" style="position: absolute; right: 90px;">' + response.header.amtinunits + '</span>');
            // $('#ASBarGraph2MaxWindow_wnd_title').next('.k-window-actions').find("span #amtinunitsmax2").html(response.header.amtinunits);
            // $("#amtinunitsmax2").html(response.header.amtinunits);
            // $("#totalbudevmax").html(response.header.totalbudev);
            vm.zoomTip = '';
            if (response.header.zoomtip != "") {
                vm.zoomText = true;
                vm.zoomTip = response.header.zoomtip;
            } else {
                vm.zoomText = false;
            }

            EVENTS.loaderOFF(graphId);
            vm.isFilteredBarGraph1 = response.header.isfiltered;
            // if(response.header.isfiltered === false) {
            //     response.chart.seriesClick = onSeriesClickGraph1;
            // } else {
            //     response.chart.seriesClick = onSeriesClickFilteredGraph;
            // }
            response.chart.seriesHover = function () {
                $("#accountStatementBarGraphMax2").css("cursor", "pointer");
            }
            $("#accountStatementBarGraphMax2").kendoChart(response.chart);
            $("#accountStatementBarGraphMax2").data("kendoChart").dataSource.data(response.seriesdata);

            $("#accountStatementBarGraphMax2").css("height", ($(window).height() - 60));

        });
        responsePromise.error(function (response, status, headers, config) {
            var methodName = 'Account statement reporting - getBudgetBarGraph1';
            EVENTS.loaderOFF(graphId);
            ERROR.displayException(response, status, response.MessageDetail, methodName);
        });
    }

    vm.acctStmtBarGraph2MaximizePopup = function () {
        $('#ASBarGraph2MaxWindow').kendoWindow({
            modal: true,
            actions: ["close"],
            scrollable: true,
            resizable: false,
            draggable: false,
            width: "100%"
        }); // localStorage.getItem("MRFPTitle") +
        ASBarGraph2Dialogue = $('#ASBarGraph2MaxWindow').data("kendoWindow").open();
        ASBarGraph2Dialogue.maximize();
        vm.ASMaximizePopupCommonDesign('#ASBarGraph2MaxWindow', '#ASRGMainContentWrapper', '#ASRGMaxToolTip');
        // get account detail maximize graph
        vm.getBudgetBarGraphMaximize2();
    }

    // common maximize popup design
    vm.ASMaximizePopupCommonDesign = function (popId, contentWrapper, toolTip) {
        $(popId).parent().css({ 'position': 'fixed', 'top': '0' });
        $('body').css({ 'overflow': 'hidden' }); // .addClass('background-white')
        $(popId).prev().css({ 'margin-top': '-29px', 'border-bottom': '0 none', 'background': '#f4f4f4', 'border': '1px solid #b3b3b3', 'width': '99.8%', 'padding-left': '0px', 'padding-right': '0px', 'height': '30px' });
        $(popId + '_wnd_title').next().find('a:first-child .k-i-close').addClass('pull-right').css({ 'background': 'url(../images/full_screen__minimize.png)', 'margin-top': '0px', 'margin-right': '2px', 'height': '25px', 'width': '25px', 'font': '1px/1 sans-serif', 'position': 'absolute', 'background-repeat': 'no-repeat' });
        $(popId + '_wnd_title').next().find('a:first-child').attr('id', 'maxToMinClose').css({ "opacity": "1", "width": "45px", "height": "30px" });
        $(popId + '_wnd_title').next().css({ 'width': '50px', 'margin-top': '5px' });
        $('#amtinunitsmax2').css({ 'width': 'auto', 'margin-top': '0px' });
        $(popId + '_wnd_title').next('div').find('a.k-window-action').removeAttr('href');
        $(popId + '_wnd_title').css('color', '#000');
        $(popId).parent().height($(window).height() - 25).addClass('padding0 padding-top30');
        $(contentWrapper + ' .acct-stmt-report-maxwindow').height($(window).height() - 75);
        $('.k-window-actions #maxToMinClose').unbind('click').click(function (e) {
            e.preventDefault();
            $(this).removeAttr('href');
            $('body').removeAttr('style');
            $('.k-window-actions a:first-child #maxMinText').remove();
        });
    }

    // to apply chart valueaxis majorunit dynamically
    // vm.barChartValueAxisChange = function (ev, chartID) {
    //     var axisDivision = 1, maxAxis = 0, maxAxisValue = 0, numberOfDivition = 4;
    //     // The setTimeout is required as we're missing a "pre-render" event
    //     // that triggers when the series are populated and ready to render.
    //     setTimeout(function() {
    //       var chart = $(chartID).data("kendoChart");
    //       var plotArea = chart._plotArea;
    //       //console.log("chartID : ", chartID, ",plotArea : ", plotArea.charts.length, ", query : ", plotArea.valueAxisRangeTracker.query());
    //       //If there's at least one visible element in the graph
    //       if (plotArea.charts.length !== 0) {
    //         maxAxisValue = plotArea.valueAxisRangeTracker.query().max;
    //         maxAxis = maxAxisValue + (maxAxisValue / 100 * 10);
    //         axisDivision = parseInt(maxAxis / numberOfDivition);
    //         var axisLength = axisDivision.toString().length;
    //         var powerDigits = 10 ** (axisLength - 1);
    //         axisDivision = Math.round(axisDivision / powerDigits) * powerDigits;
    //       }
    //       //console.log("maxAxis : ", maxAxis, ", maxAxisValue : ", maxAxisValue, ", axisDivision : ", axisDivision, ", axisLength : ", axisLength, ", powerDigits : ", powerDigits);
    //       // extend chart options by applying calculated valueaxis
    //       $.extend(true, chart.options, {
    //         valueAxis: {
    //           max: maxAxis,
    //           majorUnit: axisDivision
    //         }
    //       });
    //       chart.redraw();
    //     });
    // }

    // Absence graphs
    vm.getAllAbsenseGraph = function(){
         EVENTS.loaderON("#bannerContentWrapper");
         var graphId1 = "#absenceBarGraph1";
         EVENTS.loaderON(graphId1);
         var graphId2 = "#absenceBarGraph2";
         EVENTS.loaderON(graphId2);
         var graphId3 = "#absenceBarGraph3";
         EVENTS.loaderON(graphId3);
         var graphId4= "#absencePieGraph1";
         EVENTS.loaderON(graphId4);
        var obj = {
            monthYear: $("#accountStatementYearSelector").val(),
            orgLevel: parseInt(localStorage.getItem("accStatementOrgMenuLevel")),
            orgId: orgMenuId,
            orgName: orgName,
            level1OrgId: orgIdLevel1,
            level2OrgId: orgIdLevel2,
            level3OrgId: orgIdLevel3,
            level4OrgId: orgIdLevel4,
            level5OrgId: orgIdLevel5,
            serviceId: ""
        };
        var finalJSON = JSON.stringify(obj);
        var responsePromise = $http.post("../api/AccountStmtReportApiController/AbsenceDataGraph", finalJSON);
        responsePromise.success(function (response, status, headers, config) {
             vm.getAbsenceBarGraph1(response.JsonDataByMonthYears);
             vm.getAbsenceBarGraph2(response.JsonDataByAgeGroup);
             vm.getAbsenceBarGraph3(response.JsonDataByOrgUnit);
             vm.getAbsencePieGraph1(response.JsonDataByTotalByAge);
            EVENTS.loaderOFF("#bannerContentWrapper");
             EVENTS.loaderOFF(graphId1);
             EVENTS.loaderOFF(graphId2);
             EVENTS.loaderOFF(graphId3);
             EVENTS.loaderOFF(graphId4);
        });
        responsePromise.error(function (response, status, headers, config) {
            var methodName = 'Account statement reporting - getAllAbsenseGraph';
             EVENTS.loaderOFF(graphId1);
             EVENTS.loaderOFF(graphId2);
             EVENTS.loaderOFF(graphId3);
             EVENTS.loaderOFF(graphId4); 
            EVENTS.loaderOFF("#bannerContentWrapper");           
            ERROR.displayException(response, status, response.MessageDetail, methodName);
        });

    }
    vm.getAbsenceBarGraph1 = function (response) {      
       
          //  getAbsenceBarGraph1response = true;          
            var cleanString = DOMPurify.sanitize(response.header.title,domPurifyConfig);                  
            $("#absenceBarGraph1Title").html(cleanString);
            $('#absenceBarGraph1TitleDesc img').click(function (e) {
                MODULES.informationTooltipClick('#absenceBarGraph1TitleDesc', response.header.tooltipdesc);
            });
            var chartObj = response.chart;
            chartObj.legend.item = {};
            chartObj.legend["item"]["visual"] = function (e) {
                var color = e.options.markers.background;
                var labelColor = e.options.labels.color;
                var rect = new kendo.geometry.Rect([0, 0], [120, 50]);
                var layout = new kendo.drawing.Layout(rect, {
                    spacing: 10,
                    alignItems: "start",
                    //alignContent: "start",
                    //lineSpacing: 0,
                    wrap: false
                });

                var marker = new kendo.drawing.Path({
                    fill: {
                        color: color
                    },
                    stroke: {
                        color: color
                    },
                    cursor: "pointer"
                }).moveTo(12, 0).lineTo(12, 12).lineTo(0, 12).lineTo(0, 0).close();

                var label = new kendo.drawing.Text(e.series.name, [0, 0], {
                    fill: {
                        color: labelColor
                    },
                    font: "12px sans-serif",
                    cursor: "pointer"
                });

                layout.append(marker, label);
                layout.reflow();

                return layout;
            }

            setTimeout(function () {
                $("#absenceBarGraph1").kendoChart(chartObj);
            }, 100);      
    }

    vm.getAbsenceBarGraph2 = function (response) {      
       
           // getAbsenceBarGraph2response = true;          
            var cleanString = DOMPurify.sanitize(response.header.title,domPurifyConfig);                  
            $("#absenceBarGraph2Title").html(cleanString);
            $('#absenceBarGraph2TitleDesc img').click(function (e) {
                MODULES.informationTooltipClick('#absenceBarGraph2TitleDesc', response.header.tooltipdesc);
            });
            setTimeout(function () {
                $("#absenceBarGraph2").kendoChart(response.chart);
            }, 100);

    }

    vm.getAbsenceBarGraph3 = function (response) {
         //   getAbsenceBarGraph3response = true;            
            var cleanString = DOMPurify.sanitize(response.header.title,domPurifyConfig);                  
            $("#absenceBarGraph3Title").html(cleanString);
            var cleanString = DOMPurify.sanitize(response.header.overallabs,domPurifyConfig);                  
            $("#totalAbsByOrgUnit").html(cleanString);
            $('#absenceBarGraph3TitleDesc img').click(function (e) {
                MODULES.informationTooltipClick('#absenceBarGraph3TitleDesc', response.header.tooltipdesc);
            });
            setTimeout(function () {
                $("#absenceBarGraph3").kendoChart(response.chart);
            }, 100);       
    }

    vm.getAbsencePieGraph1 = function (response) {   
        //    getAbsencePieGraph1response = true;          
            var cleanString = DOMPurify.sanitize(response.header.title,domPurifyConfig);                  
            $("#absencePieGraph1Title").html(cleanString);
            $('#absencePieGraph1TitleDesc img').click(function (e) {
                MODULES.informationTooltipClick('#absencePieGraph1TitleDesc', response.header.tooltipdesc);
            });
            setTimeout(function () {
                $("#absencePieGraph1").kendoChart(response.chart);
            }, 100);       
    }

    vm.getAccountingFilterData = function () {
        // populate the filters
        var filterId = "#accStatementFilterSection";
        EVENTS.loaderON(filterId);

        var obj = {
            monthYear: $("#accountStatementYearSelector").val(),
            orgLevel: parseInt(localStorage.getItem("accStatementOrgMenuLevel")),
            orgId: orgMenuId,
            level1OrgId: orgIdLevel1,
            level2OrgId: orgIdLevel2,
            level3OrgId: orgIdLevel3,
            level4OrgId: orgIdLevel4,
            level5OrgId: orgIdLevel5
        };
        var finalJSON = JSON.stringify(obj);
        var responsePromise = $http.post("../AccountStatementReport/GetFilterFields", finalJSON);
        responsePromise.success(function (response, status, headers, config) {
            var obj = {}
            multiAccDropDeptArray = [], multiAccDropFuncArray = [], multiAccDropProjChangeArray = [], multiAccDropfreeDim1Array = [], multiAccDropfreeDim2Array = [], multiAccDropNextOrgArray = [], multiAccDropServiceIdArray = [];
            accStatementDeptData = [], accStatementFuncData = [], accStatementProjData = [], accStatementFreeDim1Data = [], accStatementFreeDim2Data = [], accStatementNextOrgData = [], accStatementServiceIdData = [];
            var deptDefaultOption = response.optionalLabels[2].value;
            var funDefaultOption = response.optionalLabels[3].value;// == "" ? "funksjon" : response.optionalLabels[2].value;
            var projDefaultOption = response.optionalLabels[4].value;
            var freeDim1DefaultOption = response.optionalLabels[5].value;
            var freeDim2DefaultOption = response.optionalLabels[6].value;
            var nextOrgDefaultOption = response.optionalLabels[0].value;
            var serviceIdDefaultOption = response.optionalLabels[1].value;
            accStatementDeptData = response.department;
            accStatementFuncData = response.function;
            accStatementProjData = response.project;
            accStatementFreeDim1Data = response.freeDim1;
            accStatementFreeDim2Data = response.freeDim2;
            accStatementNextOrgData = response.nextorg;
            accStatementServiceIdData = response.servs;
            if (response.freeDim2.length !== 0) {
                vm.showFreedim2 = true;
            }
            if (response.nextorg.length !== 0) {
                vm.showNextOrg = true;
            }
            if (response.servs.length !== 0) {
                vm.showServiceId = true;
            }
            // Department code
            if (accStatementDeptData.length != 0) {
                var deptCodeTemp;
                deptCodeArr = [];
                for (var i = 0; i < accStatementDeptData.length; i++) {
                    if (accStatementDeptData[i].value.length < 19) {
                        deptCodeTemp = accStatementDeptData[i].value;
                    }
                    else {
                        deptCodeTemp = accStatementDeptData[i].value.substring(0, 18) + "...";
                    }
                    obj = { key: accStatementDeptData[i].key, value: deptCodeTemp };
                    multiAccDropDeptArray.push(obj);                 
                }

                $("#deptFilterMultiDropdown").kendoMultiSelect({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: multiAccDropDeptArray,
                    change: function (e) {
                        deptCodeArr = [];
                        deptCodeArr = $("#deptFilterMultiDropdown").data("kendoMultiSelect").value();
                        $("#deptFilterMultiDropdown-list").parent('.k-animation-container').css('display', 'none');
                        var currArrLength = $("#deptFilterMultiDropdown").data("kendoMultiSelect").value().length;
                        currArrLength == 0 ? $('#filterDeptMultiWrapper').removeClass('cmn-display-block').addClass('cmn-display-none') : $('#filterDeptMultiWrapper').removeClass('cmn-display-none').addClass('cmn-display-block');
                    },
                    dataBound: function () {
                        $("#deptFilterMultiDropdown").parent().find('.k-icon.k-clear-value.k-i-close').remove();
                        $("#deptFilterMultiDropdown").data("kendoMultiSelect").value('');
                    }
                });

                $("#deptFilterDropdown").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: accStatementDeptData,
                    optionLabel: {
                        value: '' + $.parseHTML(deptDefaultOption)[0].data + '',
                        key: "Empty"
                    },
                    change: function (e) {
                        var index = e.sender.selectedIndex - 1;
                        if (index != -1) {
                            deptCodeArr.push(accStatementDeptData[index].key);
                        }
                        if (deptCodeArr.length != 0) {
                            $('#filterDeptMultiWrapper').removeClass('cmn-display-none').addClass('cmn-display-block');
                            $("#deptFilterMultiDropdown").data("kendoMultiSelect").value(deptCodeArr);
                            $("#deptFilterDropdown").data("kendoDropDownList").select(0);
                        }
                        else {
                            $('#filterDeptMultiWrapper').removeClass('cmn-display-block').addClass('cmn-display-none');
                        }
                    }
                });
                var dropdownlistDept = $("#deptFilterDropdown").data("kendoDropDownList");
                dropdownlistDept.select(0);
                $('#deptFilterDropdown-list.k-list-container.k-popup').css({ 'width': '320px' });
                deptCodeMultiSelect = $('#deptFilterMultiDropdown').data("kendoMultiSelect");
            }
            else {
                $("#deptFilterDropdown").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: accStatementDeptData,
                    optionLabel: {
                        value: '' + $.parseHTML(deptDefaultOption)[0].data + '',
                        key: "Empty"
                    }
                });
            }

            // Function code
            if (accStatementFuncData.length != 0) {
                var funcCodeTemp;
                funcCodeArr = [];
                for (i = 0; i < accStatementFuncData.length; i++) {
                    if (accStatementFuncData[i].value.length < 19) {
                        funcCodeTemp = accStatementFuncData[i].value;
                    }
                    else {
                        funcCodeTemp = accStatementFuncData[i].value.substring(0, 18) + "...";
                    }
                    obj = { key: accStatementFuncData[i].key, value: funcCodeTemp };
                    multiAccDropFuncArray.push(obj);                   
                }

                $("#functionFilterMultiDropdown").kendoMultiSelect({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: multiAccDropFuncArray,
                    change: function (e) {
                        funcCodeArr = [];
                        funcCodeArr = $("#functionFilterMultiDropdown").data("kendoMultiSelect").value();
                        $("#functionFilterMultiDropdown-list").parent('.k-animation-container').css('display', 'none');
                        var currArrLength = $("#functionFilterMultiDropdown").data("kendoMultiSelect").value().length;
                        currArrLength == 0 ? $('#filterFunMultiWrapper').removeClass('cmn-display-block').addClass('cmn-display-none') : $('#filterFunMultiWrapper').removeClass('cmn-display-none').addClass('cmn-display-block');
                    },
                    dataBound: function () {
                        $("#functionFilterMultiDropdown").parent().find('.k-icon.k-clear-value.k-i-close').remove();
                        $("#functionFilterMultiDropdown").data("kendoMultiSelect").value('');
                    }
                });

                $("#functionFilterDropdown").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: accStatementFuncData,
                    optionLabel: {
                        value: '' + $.parseHTML(funDefaultOption)[0].data + '',
                        key: "Empty"
                    },
                    change: function (e) {
                        var index = e.sender.selectedIndex - 1;
                        if (index != -1) {
                            funcCodeArr.push(accStatementFuncData[index].key);
                        }
                        if (funcCodeArr.length != 0) {
                            $('#filterFunMultiWrapper').removeClass('cmn-display-none').addClass('cmn-display-block');
                            $("#functionFilterMultiDropdown").data("kendoMultiSelect").value(funcCodeArr);
                            $("#functionFilterDropdown").data("kendoDropDownList").select(0);
                        }
                        else {
                            $('#filterFunMultiWrapper').removeClass('cmn-display-block').addClass('cmn-display-none');
                        }
                    }
                });
                var dropdownlistFunc = $("#functionFilterDropdown").data("kendoDropDownList");
                dropdownlistFunc.select(0);
                $('#functionFilterDropdown-list.k-list-container.k-popup').css({ 'width': '320px' });
                funcCodeMultiSelect = $('#functionFilterMultiDropdown').data("kendoMultiSelect");
            }
            else {
                $("#functionFilterDropdown").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: accStatementFuncData,
                    optionLabel: {
                        value: '' + $.parseHTML(funDefaultOption)[0].data + '',
                        key: "Empty"
                    }
                });
            }

            // Project code
            if (accStatementProjData.length != 0) {
                var projectCodeTemp;
                projectCodeArr = [];
                for (i = 0; i < accStatementProjData.length; i++) {
                    if (accStatementProjData[i].value.length < 19) {
                        projectCodeTemp = accStatementProjData[i].value;
                    }
                    else {
                        projectCodeTemp = accStatementProjData[i].value.substring(0, 18) + "...";
                    }
                    obj = { key: accStatementProjData[i].key, value: projectCodeTemp };
                    multiAccDropProjChangeArray.push(obj);                    
                }

                $("#projFilterMultiDropdown").kendoMultiSelect({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: multiAccDropProjChangeArray,
                    change: function (e) {
                        projectCodeArr = [];
                        projectCodeArr = $("#projFilterMultiDropdown").data("kendoMultiSelect").value();
                        $("#projFilterMultiDropdown-list").parent('.k-animation-container').css('display', 'none');
                        var currArrLength = $("#projFilterMultiDropdown").data("kendoMultiSelect").value().length;
                        currArrLength == 0 ? $('#filterProjMultiWrapper').removeClass('cmn-display-block').addClass('cmn-display-none') : $('#filterProjMultiWrapper').removeClass('cmn-display-none').addClass('cmn-display-block');
                    },
                    dataBound: function () {
                        $("#projFilterMultiDropdown").parent().find('.k-icon.k-clear-value.k-i-close').remove();
                        $("#projFilterMultiDropdown").data("kendoMultiSelect").value('');
                    }
                });

                $("#projFilterDropdown").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: accStatementProjData,
                    optionLabel: {
                        value: '' + $.parseHTML(projDefaultOption)[0].data + '',
                        key: "Empty"
                    },
                    change: function (e) {
                        var index = e.sender.selectedIndex - 1;
                        if (index != -1) {
                            projectCodeArr.push(accStatementProjData[index].key);
                        }
                        if (projectCodeArr.length != 0) {
                            $('#filterProjMultiWrapper').removeClass('cmn-display-none').addClass('cmn-display-block');
                            $("#projFilterMultiDropdown").data("kendoMultiSelect").value(projectCodeArr);
                            $("#projFilterDropdown").data("kendoDropDownList").select(0);
                        }
                        else {
                            $('#filterProjMultiWrapper').removeClass('cmn-display-block').addClass('cmn-display-none');
                        }
                    }
                });
                var dropdownlistProject = $("#projFilterDropdown").data("kendoDropDownList");
                dropdownlistProject.select(0);
                $('#projFilterDropdown-list.k-list-container.k-popup').css({ 'width': '320px' });
                projectCodeMultiSelect = $('#projFilterMultiDropdown').data("kendoMultiSelect");
            }
            else {
                $("#projFilterDropdown").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: accStatementProjData,
                    optionLabel: {
                        value: '' + $.parseHTML(projDefaultOption)[0].data + '',
                        key: "Empty"
                    }
                });
            }

            // Freedim1 code

            if (accStatementFreeDim1Data.length != 0) {
                var freeDim1CodeTemp;
                freeDim1Arr = [];
                for (i = 0; i < accStatementFreeDim1Data.length; i++) {
                    if (accStatementFreeDim1Data[i].value.length < 19) {
                        freeDim1CodeTemp = accStatementFreeDim1Data[i].value;
                    }
                    else {
                        freeDim1CodeTemp = accStatementFreeDim1Data[i].value.substring(0, 18) + "...";
                    }
                    obj = { key: accStatementFreeDim1Data[i].key, value: freeDim1CodeTemp };
                    multiAccDropfreeDim1Array.push(obj);                  
                }

                $("#freeDimFilterMultiDropdown1").kendoMultiSelect({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: multiAccDropfreeDim1Array,
                    change: function (e) {
                        freeDim1Arr = [];
                        freeDim1Arr = $("#freeDimFilterMultiDropdown1").data("kendoMultiSelect").value();
                        $("#freeDimFilterMultiDropdown1-list").parent('.k-animation-container').css('display', 'none');
                        var currArrLength = $("#freeDimFilterMultiDropdown1").data("kendoMultiSelect").value().length;
                        currArrLength == 0 ? $('#filterFreedimMultiWrapper1').removeClass('cmn-display-block').addClass('cmn-display-none') : $('#filterFreedimMultiWrapper1').removeClass('cmn-display-none').addClass('cmn-display-block');
                    },
                    dataBound: function () {
                        $("#freeDimFilterMultiDropdown1").parent().find('.k-icon.k-clear-value.k-i-close').remove();
                        $("#freeDimFilterMultiDropdown1").data("kendoMultiSelect").value('');
                    }
                });

                $("#freeDimFilterDropdown1").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: accStatementFreeDim1Data,
                    optionLabel: {
                        value: '' + $.parseHTML(freeDim1DefaultOption)[0].data + '',
                        key: "Empty"
                    },
                    change: function (e) {
                        var index = e.sender.selectedIndex - 1;
                        if (index != -1) {
                            freeDim1Arr.push(accStatementFreeDim1Data[index].key);
                        }
                        if (freeDim1Arr.length != 0) {
                            $('#filterFreedimMultiWrapper1').removeClass('cmn-display-none').addClass('cmn-display-block');
                            $("#freeDimFilterMultiDropdown1").data("kendoMultiSelect").value(freeDim1Arr);
                            $("#freeDimFilterDropdown1").data("kendoDropDownList").select(0);
                        }
                        else {
                            $('#filterFreedimMultiWrapper1').removeClass('cmn-display-block').addClass('cmn-display-none');
                        }
                    }
                });
                var dropdownlistFreeDim1 = $("#freeDimFilterDropdown1").data("kendoDropDownList");
                dropdownlistFreeDim1.select(0);
                $('#freeDimFilterDropdown1-list.k-list-container.k-popup').css({ 'width': '320px' });
                freeDim1MultiSelect = $('#freeDimFilterMultiDropdown1').data("kendoMultiSelect");
            }
            else {
                $("#freeDimFilterDropdown1").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: accStatementFreeDim1Data,
                    optionLabel: {
                        value: '' + $.parseHTML(freeDim1DefaultOption)[0].data + '',
                        key: "Empty"
                    }
                });
            }

            // Freedim2 code

            if (accStatementFreeDim2Data.length != 0) {
                var freeDim2CodeTemp;
                freeDim2Arr = [];
                for (i = 0; i < accStatementFreeDim2Data.length; i++) {
                    if (accStatementFreeDim2Data[i].value.length < 19) {
                        freeDim2CodeTemp = accStatementFreeDim2Data[i].value;
                    }
                    else {
                        freeDim2CodeTemp = accStatementFreeDim2Data[i].value.substring(0, 18) + "...";
                    }
                    obj = { key: accStatementFreeDim2Data[i].key, value: freeDim2CodeTemp };
                    multiAccDropfreeDim2Array.push(obj);                  
                }

                $("#freeDimFilterMultiDropdown2").kendoMultiSelect({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: multiAccDropfreeDim2Array,
                    change: function (e) {
                        freeDim2Arr = [];
                        freeDim2Arr = $("#freeDimFilterMultiDropdown2").data("kendoMultiSelect").value();
                        $("#freeDimFilterMultiDropdown2-list").parent('.k-animation-container').css('display', 'none');
                        var currArrLength = $("#freeDimFilterMultiDropdown2").data("kendoMultiSelect").value().length;
                        currArrLength == 0 ? $('#filterFreedimMultiWrapper2').removeClass('cmn-display-block').addClass('cmn-display-none') : $('#filterFreedimMultiWrapper2').removeClass('cmn-display-none').addClass('cmn-display-block');
                    },
                    dataBound: function () {
                        $("#freeDimFilterMultiDropdown2").parent().find('.k-icon.k-clear-value.k-i-close').remove();
                        $("#freeDimFilterMultiDropdown2").data("kendoMultiSelect").value('');
                    }
                });

                $("#freeDimFilterDropdown2").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: accStatementFreeDim2Data,
                    optionLabel: {
                        value: '' + $.parseHTML(freeDim2DefaultOption)[0].data + '',
                        key: "Empty"
                    },
                    change: function (e) {
                        var index = e.sender.selectedIndex - 1;
                        if (index != -1) {
                            freeDim2Arr.push(accStatementFreeDim2Data[index].key);
                        }
                        if (freeDim2Arr.length != 0) {
                            $('#filterFreedimMultiWrapper2').removeClass('cmn-display-none').addClass('cmn-display-block');
                            $("#freeDimFilterMultiDropdown2").data("kendoMultiSelect").value(freeDim2Arr);
                            $("#freeDimFilterDropdown2").data("kendoDropDownList").select(0);
                        }
                        else {
                            $('#filterFreedimMultiWrapper2').removeClass('cmn-display-block').addClass('cmn-display-none');
                        }
                    }
                });
                var dropdownlistFreeDim2 = $("#freeDimFilterDropdown2").data("kendoDropDownList");
                dropdownlistFreeDim2.select(0);
                $('#freeDimFilterDropdown2-list.k-list-container.k-popup').css({ 'width': '320px' });
                freeDim2MultiSelect = $('#freeDimFilterMultiDropdown2').data("kendoMultiSelect");
            }
            else {
                $("#freeDimFilterDropdown2").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: accStatementFreeDim2Data,
                    optionLabel: {
                        value: '' + $.parseHTML(freeDim2DefaultOption)[0].data + '',
                        key: "Empty"
                    }
                });
            }

            // Next Org code
            if (accStatementNextOrgData.length != 0) {
                var nextOrgTemp;
                nextOrgArr = [];
                for (i = 0; i < accStatementNextOrgData.length; i++) {
                    if (accStatementNextOrgData[i].value.length < 19) {
                        nextOrgTemp = accStatementNextOrgData[i].value;
                    }
                    else {
                        nextOrgTemp = accStatementNextOrgData[i].value.substring(0, 18) + "...";
                    }
                    obj = { key: accStatementNextOrgData[i].key, value: nextOrgTemp };
                    multiAccDropNextOrgArray.push(obj);
                    obj = {};
                }

                $("#nextOrgFilterMultiDropdown").kendoMultiSelect({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: multiAccDropNextOrgArray,
                    change: function (e) {
                        nextOrgArr = [];
                        nextOrgArr = $("#nextOrgFilterMultiDropdown").data("kendoMultiSelect").value();
                        $("#nextOrgFilterMultiDropdown-list").parent('.k-animation-container').css('display', 'none');
                        var currArrLength = $("#nextOrgFilterMultiDropdown").data("kendoMultiSelect").value().length;
                        currArrLength == 0 ? $('#filterNextOrgMultWrapper').removeClass('cmn-display-block').addClass('cmn-display-none') : $('#filterNextOrgMultWrapper').removeClass('cmn-display-none').addClass('cmn-display-block');
                    },
                    dataBound: function () {
                        $("#nextOrgFilterMultiDropdown").parent().find('.k-icon.k-clear-value.k-i-close').remove();
                        $("#nextOrgFilterMultiDropdown").data("kendoMultiSelect").value('');
                    }
                });

                $("#nextOrgFilterDropdown").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: accStatementNextOrgData,
                    optionLabel: {
                        value: '' + $.parseHTML(nextOrgDefaultOption)[0].data + '',
                        key: "Empty"
                    },
                    change: function (e) {
                        var index = e.sender.selectedIndex - 1;
                        if (index != -1) {
                            nextOrgArr.push(accStatementNextOrgData[index].key);
                        }
                        if (nextOrgArr.length != 0) {
                            $('#filterNextOrgMultWrapper').removeClass('cmn-display-none').addClass('cmn-display-block');
                            $("#nextOrgFilterMultiDropdown").data("kendoMultiSelect").value(nextOrgArr);
                            $("#nextOrgFilterDropdown").data("kendoDropDownList").select(0);
                        }
                        else {
                            $('#filterNextOrgMultWrapper').removeClass('cmn-display-block').addClass('cmn-display-none');
                        }
                    }
                });
                var dropdownlistnextOrg = $("#nextOrgFilterDropdown").data("kendoDropDownList");
                dropdownlistnextOrg.select(0);
                $('#nextOrgFilterDropdown-list.k-list-container.k-popup').css({ 'width': '320px' });
                nextOrgMultiSelect = $('#nextOrgFilterMultiDropdown').data("kendoMultiSelect");
            }
            else {
                $("#nextOrgFilterDropdown").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: accStatementNextOrgData,
                    optionLabel: {
                        value: '' + $.parseHTML(nextOrgDefaultOption)[0].data + '',
                        key: "Empty"
                    }
                });
            }
            // ServiceId
            if (accStatementServiceIdData.length != 0) {
                var serviceIdTemp;
                serviceAreaIdArr = [];
                for (i = 0; i < accStatementServiceIdData.length; i++) {
                    if (accStatementServiceIdData[i].value.length < 19) {
                        serviceIdTemp = accStatementServiceIdData[i].value;
                    }
                    else {
                        serviceIdTemp = accStatementServiceIdData[i].value.substring(0, 18) + "...";
                    }
                    obj = { key: accStatementServiceIdData[i].key, value: serviceIdTemp };
                    multiAccDropServiceIdArray.push(obj);
                    obj = {};
                }

                $("#serviceIdFilterMultiDropdown").kendoMultiSelect({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: multiAccDropServiceIdArray,
                    change: function (e) {
                        serviceAreaIdArr = [];
                        serviceAreaIdArr = $("#serviceIdFilterMultiDropdown").data("kendoMultiSelect").value();
                        $("#serviceIdFilterMultiDropdown-list").parent('.k-animation-container').css('display', 'none');
                        var currArrLength = $("#serviceIdFilterMultiDropdown").data("kendoMultiSelect").value().length;
                        currArrLength == 0 ? $('#filterServiceIdMultWrapper').removeClass('cmn-display-block').addClass('cmn-display-none') : $('#filterServiceIdMultWrapper').removeClass('cmn-display-none').addClass('cmn-display-block');
                    },
                    dataBound: function () {
                        $("#serviceIdFilterMultiDropdown").parent().find('.k-icon.k-clear-value.k-i-close').remove();
                        $("#serviceIdFilterMultiDropdown").data("kendoMultiSelect").value('');
                    }
                });

                $("#serviceIdFilterDropdown").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: accStatementServiceIdData,
                    optionLabel: {
                        value: '' + $.parseHTML(serviceIdDefaultOption)[0].data + '',
                        key: "Empty"
                    },
                    change: function (e) {
                        var index = e.sender.selectedIndex - 1;
                        if (index != -1) {
                            serviceAreaIdArr.push(accStatementServiceIdData[index].key);
                        }
                        if (serviceAreaIdArr.length != 0) {
                            $('#filterServiceIdMultWrapper').removeClass('cmn-display-none').addClass('cmn-display-block');
                            $("#serviceIdFilterMultiDropdown").data("kendoMultiSelect").value(serviceAreaIdArr);
                            $("#serviceIdFilterDropdown").data("kendoDropDownList").select(0);
                        }
                        else {
                            $('#filterServiceIdMultWrapper').removeClass('cmn-display-block').addClass('cmn-display-none');
                        }
                    }
                });
                var dropdownlistServiceId = $("#serviceIdFilterDropdown").data("kendoDropDownList");
                dropdownlistServiceId.select(0);
                $('#serviceIdFilterDropdown-list.k-list-container.k-popup').css({ 'width': '320px' });
                serviceIdMultiSelect = $('#serviceIdFilterMultiDropdown').data("kendoMultiSelect");
            }
            else {
                $("#serviceIdFilterDropdown").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: accStatementServiceIdData,
                    optionLabel: {
                        value: '' + $.parseHTML(serviceIdDefaultOption)[0].data + '',
                        key: "Empty"
                    }
                });
            }
            vm.getAccountsGridData();
            EVENTS.loaderOFF(filterId);
        });
        responsePromise.error(function (response, status, headers, config) {
            EVENTS.loaderOFF("#bannerContentWrapper");
            var methodName = 'Account statement reporting - getAccountingFilterData';
            EVENTS.loaderOFF(filterId);
            ERROR.displayException(response, status, response.MessageDetail, methodName);
        });
    }

    vm.getAccountsGridData = function () {
        var gridId = "#accountStatementGrid";
        EVENTS.loaderON(gridId);
        var projCodesData = JSON.parse(JSON.stringify(projectCodeArr));

        for (var i = 0; i < projCodesData.length; i++) {
            if (projCodesData[i] == -1) projCodesData[i] = "";
        }
        var freeDim1Data = JSON.parse(JSON.stringify(freeDim1Arr));
        for (var i = 0; i < freeDim1Data.length; i++) {
            if (freeDim1Data[i] == -1) freeDim1Data[i] = "";
        }
        var freeDim2Data = JSON.parse(JSON.stringify(freeDim2Arr));
        for (var i = 0; i < freeDim2Data.length; i++) {
            if (freeDim2Data[i] == -1) freeDim2Data[i] = "";
        }
        vm.accountStatementColumns.forEach(function(v){ delete v.$$hashKey }); // removed $$hashkey property from obj for bug 76617
        var obj = {
            departments: deptCodeArr,
            functions: funcCodeArr,
            projects: projCodesData,
            freeDim1: freeDim1Data,
            freeDim2: freeDim2Data,
            nextorg: nextOrgArr,
            servs: serviceAreaIdArr,
            monthYear: $("#accountStatementYearSelector").val(),
            orgLevel: parseInt(localStorage.getItem("accStatementOrgMenuLevel")),
            orgId: orgMenuId,
            orgName: orgName,
            serviceId: "",
            serviceName: "",
            level1OrgId: orgIdLevel1,
            level2OrgId: orgIdLevel2,
            level3OrgId: orgIdLevel3,
            level4OrgId: orgIdLevel4,
            level5OrgId: orgIdLevel5,
            colsConfig: vm.accountStatementColumns,
        };
        var finalJSON = JSON.stringify(obj);

        var responsePromise = $http.post("../AccountStatementReport/GetAccPnLReportGrid", finalJSON);
        responsePromise.success(function (response, status, headers, config) {
            EVENTS.loaderOFF("#bannerContentWrapper");
            if (accStatementTreeGrid) {
                $(gridId).data("kendoTreeList").destroy();
                $(gridId).empty();
            }
            var data = response.dataSource;
            vm.accountPeriodData = JSON.stringify({ dataSource: data, columns: response.columns });
            accStatementTreeGrid = $(gridId).kendoTreeList({
                dataSource: {
                    data: response.dataSource,
                    schema: {
                        model: {
                            fields: {
                                id: { type: "string" }
                            }
                        }
                    }
                },

                dataBound: function () {
                    var t = this;
                    $(gridId + " tr td >.account-ytd-items").each(function () {
                        var row = $(this).closest("tr");
                        var model = t.dataItem(row);
                        var accountLevel = model.accountLevel;
                        var id = model.id;
                        var isItalic = model.isItalic;
                        var periodCheck = true;
                        var accountType = model.accountType;
                        if (isItalic == true) {
                            $(this).html('<a>' + kendo.toString(parseInt(model.accountYtd), "n0") + '</a>')
                            $(this).unbind('click').click(function () {
                                vm.getFinancialStatusAccountDetails(id, accountLevel);
                            });
                        }
                        else {
                            $(this).html('<span>' + kendo.toString(parseInt(model.accountYtd), "n0") + '</span>')
                        }
                    });

                    $(gridId + " tr td >.revised-budget-ytd-items").each(function () {
                        var row = $(this).closest("tr");
                        var model = t.dataItem(row);
                        var accountLevel = model.accountLevel;
                        var id = model.id;
                        var isItalic = model.isItalic;
                        var periodCheck = true;
                        var accountType = model.accountType;
                        if (isItalic == true) {
                            $(this).html('<a>' + kendo.toString(parseInt(model.revisedBudgetYtd), "n0") + '</a>')
                            $(this).unbind('click').click(function () {
                                vm.getRevisedBudgetYtdPopup(id, accountLevel, periodCheck);
                            });
                        }
                        else {
                            $(this).html('<span>' + kendo.toString(parseInt(model.revisedBudgetYtd), "n0") + '</span>')
                        }
                    });

                    $(gridId + " tr td >.annual-budget-items").each(function () {
                        var row = $(this).closest("tr");
                        var model = t.dataItem(row);
                        var accountLevel = model.accountLevel;
                        var id = model.id;
                        var isItalic = model.isItalic;
                        var periodCheck = true;
                        if (isItalic == true) {
                            $(this).html('<a>' + kendo.toString(parseInt(model.annualBudget), "n0") + '</a>')
                            $(this).unbind('click').click(function () {
                                vm.getRevisedBudgetYtdPopup(id, accountLevel, periodCheck);
                            });
                        }
                        else {
                            $(this).html('<span>' + kendo.toString(parseInt(model.annualBudget), "n0") + '</span>');
                        }
                    });

                    $(gridId).css('max-height', '600');
                    $('.expand-collapse-filter-sec').css('height', $('.filter-contents').height());
                    $('.expand-collapse-filter-sec').removeClass('not-active');
                },
                columns: response.columns
            });
            EVENTS.loaderOFF(gridId);
        });
        responsePromise.error(function (response, status, headers, config) {
            EVENTS.loaderOFF("#bannerContentWrapper");
            var methodName = 'Account statement reporting - getAccountsGridData';
            EVENTS.loaderOFF(gridId);
            ERROR.displayException(response, status, response.MessageDetail, methodName);
        });
    }

    vm.ExportAccountTreelistToExcel = function () {
        var getURL = "../Content/DownloadExcel";
        var promiseExport = $http.post("../AccountStatementReport/ExportTreeToExcel", vm.accountPeriodData);
        promiseExport.success(function (response, status, headers, config) {
            window.location = getURL + "?fName=" + response.fName;
        });
        promiseExport.error(function (response, status, headers, config) {
            ERROR.displayException(response, status, response.MessageDetail);
        });
    }

    // Tab AccountingStatmentTabD Starts here
    vm.getBudgetPeriodTreelistData = function () {
        var gridId = "#budgetPeriodTreelist";
        EVENTS.loaderON(gridId);
        var projCodesBudData = JSON.parse(JSON.stringify(projectCodeBud));

        for (var i = 0; i < projCodesBudData.length; i++) {
            if (projCodesBudData[i] == -1) projCodesBudData[i] = "";
        }
        var obj = {
            departments: deptCodeBud,
            functions: funcCodeBud,
            projects: projCodesBudData,
            freeDim1: freeDim1Bud,
            freeDim2: freeDim2Bud,
            nextorg: nextOrgCodeBud,
            servs: serviceIdBud,
            monthYear: $("#accountStatementYearSelector").val(),
            orgLevel: parseInt(localStorage.getItem("accStatementOrgMenuLevel")),
            orgId: orgMenuId,
            orgName: orgName,
            serviceId: "",
            serviceName: "",
            level1OrgId: orgIdLevel1,
            level2OrgId: orgIdLevel2,
            level3OrgId: orgIdLevel3,
            level4OrgId: orgIdLevel4,
            level5OrgId: orgIdLevel5,
            colsConfig: vm.budgetPeriodSelColumns,
        };
        var finalJSON = JSON.stringify(obj);

        // reload collapse/expand buttons
        $("#hiddenBordersListWrapper li").removeClass("in");

        var responsePromise = $http.post("../api/AccountStmtReportApiController/GetAccPeriodGrid", finalJSON);
        responsePromise.success(function (response, status, headers, config) {

            EVENTS.loaderOFF("#bannerContentWrapper");
            if (budgetPeriodTreelist) {
                $(gridId).data("kendoTreeList").destroy();
                $(gridId).empty();
            }

            var colsConofig = vm.budgetPeriodSelColumns;
            if (colsConofig[0].isChecked == true) {
                response.columns[0]["locked"] = true;
            }

            // data-title array
            var dataTitleArr = [];
            for (let c = 0; c < response.columns.length; c++) {
                let col = response.columns[c];
                dataTitleArr.push(col.title);
            }
            var data = response.data;
            vm.accPeriodData = JSON.stringify({ dataSource: data, columns: response.columns });
            budgetPeriodTreelist = $(gridId).kendoTreeList({
                dataSource: {
                    data: data,
                    schema: {
                        model: {
                            fields: {
                                id: { type: "string" }
                            }
                        }
                    }
                },
                dataBound: function () {
                    var i = 0;
                    // expand/collase of month columns
                    var selectedColumns = vm.budgetPeriodSelColumns;
                    $("#budgetPeriodTreelist").find("thead tr:first-child th").each(function (e) {
                        if (i > 0) {
                            $(this).html("");
                            $(this).html("<div class='col-md-12 padding0 cmn-relative-position'><span class='col-md-11 padding0'>" + $(this).attr('data-title') + "</span><span class='col-md-1 padding0 glyphicon glyphicon-chevron-left less-than-symbol collapsed hand-pointer' aria-hidden='true' id='" + $(this).attr('data-title') + "_id' data-toggle='collapse' data-parent='#accordion' data-target='#" + i + "_id'></span>");
                            //dataTitleArr.push($(this).attr('data-title'));
                            $("#" + i + "_id").attr("data-title", $(this).attr('data-title'));
                        }
                        var currId = i + "_id";
                        var monthArr = ["", "jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec", "yar"];

                        $("#" + currId).click(function (e) {
                            // expand selected column from number buttons
                            var expandColumn = $(this).attr("id").split("_")[0];
                            var collapsedDataTitle = $(this).attr("data-title");
                            if (selectedColumns.length > 0) {
                                for (let j = 1; j <= 7; j++) {
                                    if (selectedColumns[j-1].isChecked == true) {
                                        let columnField = monthArr[expandColumn] + "_" + selectedColumns[j-1].key;
                                        $("#budgetPeriodTreelist .k-grid-header-wrap th[data-field='" + columnField + "']").show();
                                        $("#budgetPeriodTreelist tr td >." + columnField + "-items").each(function () {
                                            $(this).closest("td").show();
                                        });
                                    }
                                }
                            }
                            //$("#budgetPeriodTreelist").data("kendoTreeList").showColumn(parseInt(expandColumn));
                            $("#budgetPeriodTreelist .k-grid-header-wrap th[data-title='" + collapsedDataTitle + "']").show();
                            // remove all number buttons
                            $("#hiddenBordersListWrapper li#" + currId).removeClass("in");
                        });

                        $("#expandAll").unbind('click').click(function (e) {
                            // expand all columns of treelist
                            for (let k = 1; k <= 13; k++) {
                                if (selectedColumns.length > 0) {
                                    for (let m = 1; m <= 7; m++) {
                                        if (selectedColumns[m-1].isChecked == true) {
                                            let columnField = monthArr[k] + "_" + selectedColumns[m-1].key;
                                            $("#budgetPeriodTreelist .k-grid-header-wrap th[data-field='" + columnField + "']").show();
                                            $("#budgetPeriodTreelist tr td >." + columnField + "-items").each(function () {
                                                $(this).closest("td").show();
                                            });
                                        }
                                    }
                                    //$("#budgetPeriodTreelist").data("kendoTreeList").showColumn(k);
                                    $("#budgetPeriodTreelist .k-grid-header-wrap th[data-title='" + dataTitleArr[k] + "']").show();
                                    $("#hiddenBordersListWrapper li").removeClass("in");
                                }
                            }
                        });

                        $(this).find('.less-than-symbol').click(function (e) {
                            // collapse/hide selected column from treelist
                            // var baseColumnName = currId.toString().split("_")[0];
                            var index = $(this).closest(".k-grid-header-wrap th").index() + 1;
                            let dataTitle = $(this).closest(".k-grid-header-wrap th").attr("data-title");

                            if (selectedColumns.length > 0) {
                                // each column selector columns
                                for (let j = 1; j <= 7; j++) {
                                    if (selectedColumns[j-1].isChecked == true) {
                                        let columnField = monthArr[index] + "_" + selectedColumns[j-1].key;
                                        $("#budgetPeriodTreelist .k-grid-header-wrap th[data-field='" + columnField + "']").hide();
                                        $("#budgetPeriodTreelist tr td >." + columnField + "-items").each(function () {
                                            $(this).closest("td").hide();
                                        });
                                    }
                                }
                            }
                            // hide base column
                            $("#budgetPeriodTreelist .k-grid-header-wrap th[data-title='" + dataTitle + "']").hide();
                            //$("#budgetPeriodTreelist").data("kendoTreeList").hideColumn(index);
                            // $("#budgetPeriodTreelist").data("kendoTreeList").refresh();
                        });

                        for (let b = 2; b <= monthArr.length; b++) {
                            $("#budgetPeriodTreelist .k-grid-header-wrap th[data-field='" + monthArr[b] + "_budget']").css("border-left-style", "solid");
                        }
                        i++;
                    });

                    $(gridId).css({ 'max-height': '600', 'padding-top': '50px' });
                    $(gridId + ' .k-grid-header-wrap tr:first-child th').css({ 'border-right': '1px solid #c3c3c3', 'background': '#f4f4f4' });//.addClass('cmn-position-relative').append('<span class="less-than-symbol"><</span>');
                    //$(gridId + ' thead tr:last-child th:nth-last-child(3n+1)').css('border-right', '1px solid #c3c3c3');
                    //$(gridId + ' thead tr:first-child th:first-child').css({'background': 'none' });

                    // make some spacec between last row and horizantal scroll bar
                    $("#budgetPeriodTreelist .k-grid-content-locked table").append("<tr><td>&nbsp;</td></tr>");
                    $("#budgetPeriodTreelist .k-grid-content table").append("<tr><td colspan='12'>&nbsp;</td></tr>");

                    //$(gridId + ' thead k-grid-header-locked th').css({'background': 'none', 'border-right': 'none' });
                    $(gridId + ' .k-grid-header-wrap tr:first-child th:last-child').css('border-right', '0');
                    $('.expand-collapse-filter-sec').css('height', $('.filter-contents').height());
                    $('.expand-collapse-filter-sec').removeClass('not-active');
                },
                columns: response.columns
            });

            EVENTS.loaderOFF(gridId);
        });
        responsePromise.error(function (response, status, headers, config) {
            EVENTS.loaderOFF("#bannerContentWrapper");
            var methodName = 'Account statement reporting - getBudgetPeriodTreelistData';
            EVENTS.loaderOFF(gridId);
            ERROR.displayException(response, status, response.MessageDetail, methodName);
        });
    }

    vm.multiSelectFilterClearingData = function () {
        multiAccDropDeptArray = [], multiAccDropFuncArray = [], multiAccDropProjChangeArray = [], multiAccDropfreeDim1Array = [], multiAccDropfreeDim2Array = [], multiAccDropNextOrgArray = [], multiAccDropServiceIdArray = [];
        accStatementDeptData = [], accStatementFuncData = [], accStatementProjData = [], accStatementFreeDim1Data = [], accStatementFreeDim2Data = [], accStatementNextOrgData = [], accStatementServiceIdData = [];
        deptCodeArr = [], funcCodeArr = [], projectCodeArr = [], freeDim1Arr = [], freeDim2Arr = [], nextOrgArr = [], serviceAreaIdArr = [];

        if (deptCodeMultiSelect != "") { $("#deptFilterMultiDropdown").data("kendoMultiSelect").value(''); }
        if (funcCodeMultiSelect != "") { $("#functionFilterMultiDropdown").data("kendoMultiSelect").value(''); }
        if (projectCodeMultiSelect != "") { $("#projFilterMultiDropdown").data("kendoMultiSelect").value(''); }
        if (freeDim1MultiSelect != "") { $("#freeDimFilterMultiDropdown1").data("kendoMultiSelect").value(''); }
        if (freeDim2MultiSelect != "") { $("#freeDimFilterMultiDropdown2").data("kendoMultiSelect").value(''); }
        if (nextOrgMultiSelect != "") { $("#nextOrgFilterMultiDropdown").data("kendoMultiSelect").value(''); }
        if (serviceIdMultiSelect != "") { $("#serviceIdFilterMultiDropdown").data("kendoMultiSelect").value(''); }
        if ($('.common-multidrop-wrapper').hasClass('cmn-display-block')) {
            $('.common-multidrop-wrapper').removeClass('cmn-display-block').addClass('cmn-display-none');
        }
    }

    vm.getMROrgText = function () {

        var monthYear = $("#accountStatementYearSelector").data("kendoDropDownList").value();
        var responsePromise = $http.get("../api/AccountStmtReportApiController/GetTenantOrgHeaderText?selectedOrgName=" + encodeURIComponent(orgName) + "&orgId=" + orgMenuId + "&orgLevel=" + orgMenuLevel + "&monthYear=" + monthYear);
        responsePromise.success(function (response, status, headers, config) {
            vm.selectedOrgHeaderText = response.headertext;
        });
        responsePromise.error(function (response, status, headers, config) {
            var methodName = 'account statementReporting - getMROrgText';
            ERROR.displayException(response, status, response.MessageDetail, methodName);
        });
    }

    vm.getDate = function () {
        var today = new Date();
        var dd = today.getDate();
        var mm = today.getMonth() + 1; //January is 0!
        var yyyy = today.getFullYear();

        if (dd < 10) {
            dd = '0' + dd;
        }
        if (mm < 10) {
            mm = '0' + mm;
        }
        today = yyyy + '-' + mm + '-' + dd;
        return today;
    }

    vm.getRevisedBudgetYtdPopup = function (id, accountLevel, periodCheck) {
        vm.budgetHistoryPopupSelectedId = id;
        vm.budgetHistoryPopupSelectedPeriodCheck = periodCheck;
        vm.budgetHistoryPopupSelectedAccountName = accountLevel;
        getAccountYtdPopupDlg = $('#MRReporingPopup1Window').kendoWindow({
            modal: true,
            close: function (e) {

            },
            scrollable: false,
            resizable: false,
            draggable: true,
            height: $(window).height() - 100,
            width: "93%",
            actions: ["Close"]
        }).data("kendoWindow").center().open();
        getAccountYtdPopupDlg.element.prev().addClass('cmn-background2');

        var finalJsonData = vm.applyBudgetHistoryInputData();

        vm.getAccountReportingGridData(finalJsonData, false);
        vm.getAccountReportingTreeGridGridData(finalJsonData);
    }

    vm.applyBudgetHistoryInputData = function (sortField = "", sortOrder = "") {
        var forecastPeriod = $("#accountStatementYearSelector").data("kendoDropDownList").value();
        var parsePeriod = new Date(forecastPeriod);
        var month = parsePeriod.getMonth() + 1;
        var year = parsePeriod.getFullYear();
        vm.selectedServiceId = '';
        vm.selectedServiceName = '';

        var dropDownObjData = {
            departments: deptCodeArr,
            functions: funcCodeArr,
            projects: projectCodeArr,
            freeDim1: freeDim1Arr,
            freeDim2: freeDim2Arr,
            nextorg: nextOrgArr,
            servs: serviceAreaIdArr,
            monthYear: forecastPeriod,
            level1OrgId: orgIdLevel1,
            level2OrgId: orgIdLevel2,
            level3OrgId: orgIdLevel3,
            level4OrgId: orgIdLevel4,
            level5OrgId: orgIdLevel5,
            orgId: orgMenuId,
            orgLevel: orgMenuLevel,
            orgName: orgName,
            serviceId: vm.selectedServiceId,
            serviceName: vm.selectedServiceName,
            accountCodeId: vm.budgetHistoryPopupSelectedId,
            budget_year: year,
            period: month,
            isPeriodCheck: vm.budgetHistoryPopupSelectedPeriodCheck,
            sortField: sortField,
            sortOrder: sortOrder,
            accountCodeName: vm.budgetHistoryPopupSelectedAccountName
        }

        var finalJsonData = JSON.stringify(dropDownObjData);
        return finalJsonData;
    }

    vm.closePopup = function () {
        getAccountYtdPopupDlg.close();
    }

    vm.getAccountReportingGridData = function (finalJsonData, sortFlag) {
        EVENTS.loaderON('#accountReportingGrid');
        var responsePromise = $http.post("../api/MonthlyReportingDocApi/GetBudgetHistoryTreeListData", finalJsonData);
        responsePromise.success(function (response, status, headers, config) {
            EVENTS.loaderOFF('#accountReportingGrid');
            vm.accountReportingGridTitle = response.Title;
            getAccountYtdPopupDlg.element.prev().find(".k-window-title").html('<span>' + vm.accountReportingGridTitle + '</span><span class="padding-left5 cursor" data-role="tooltip" id="accountReportToolTip"><img src="../images/icons_monthly_report_information.png"></span>');
            $('#accountReportToolTip img').click(function (e) {
                MODULES.informationTooltipClick('#accountReportToolTip', response.Description);
            });

            // loading header grid for filters
            if (sortFlag == false) {
                vm.getBudgetHistoryHeaderGrid(response);
            }

            // Financial Status Budget Treelist data
            vm.getBudgetHistoryTreelistData(response);
        });

        responsePromise.error(function (response, status, headers, config) {
            EVENTS.loaderOFF(gridId);
            var methodName = 'Account Reporting - getAccountReportingGridData';
            ERROR.displayException(response, status, response.MessageDetail, methodName);
        });
    }

    vm.getBudgetHistoryHeaderGrid = function (response) {

        var gridId = "#accountReportingSearchGrid";
        var notSortedColumns = ["account", "department", "function", "project", "description"];
        if (response.columns.length > 0) {
            for (var i = 0; i < response.columns.length; i++) {
                switch (response.columns[i].field) {
                    case "date":
                        response.columns[i].filterable.cell.template = function (el) {
                            $scope.filterTemplate(el);
                        };
                        break;
                    case "adjustmentCodeStatus":
                        response.columns[i].filterable.cell.template = function (el) {
                            $scope.filterTemplate(el);
                        };
                        break;
                    case "transactioncomment":
                        response.columns[i].filterable.cell.template = function (el) {
                            $scope.filterTemplate(el);
                        };
                        break;
                    case "updatedby":
                        response.columns[i].filterable.cell.template = function (el) {
                            $scope.filterTemplate(el);
                        };
                        break;
                    case "changeamount":
                        response.columns[i].filterable.cell.template = function (el) {
                            $scope.filterTemplate(el);
                        };
                        break;
                    case "account":
                        response.columns[i].filterable.cell.template = function (el) {
                            $scope.filterTemplate(el);
                        };
                        break;
                    case "department":
                        response.columns[i].filterable.cell.template = function (el) {
                            $scope.filterTemplate(el);
                        };
                        break;
                    case "function":
                        response.columns[i].filterable.cell.template = function (el) {
                            $scope.filterTemplate(el);
                        };
                        break;
                    case "project":
                        response.columns[i].filterable.cell.template = function (el) {
                            $scope.filterTemplate(el);
                        };
                        break;
                    case "free_dim_1":
                        response.columns[i].filterable.cell.template = function (el) {
                            $scope.filterTemplate(el);
                        };
                        break;
                    case "free_dim_2":
                        response.columns[i].filterable.cell.template = function (el) {
                            $scope.filterTemplate(el);
                        };
                        break;
                    case "free_dim_3":
                        response.columns[i].filterable.cell.template = function (el) {
                            $scope.filterTemplate(el);
                        };
                        break;
                    case "free_dim_4":
                        response.columns[i].filterable.cell.template = function (el) {
                            $scope.filterTemplate(el);
                        };
                        break;
                    case "accinfocomment":
                        response.columns[i].filterable.cell.template = function (el) {
                            $scope.filterTemplate(el);
                        };
                        break;
                    case "adjustmentCode":
                        response.columns[i].filterable.cell.template = function (el) {
                            $scope.filterTemplate(el);
                        };
                        break;
                    case "year_1_amount":
                        response.columns[i].filterable = false;
                        break;
                    case "description":
                        response.columns[i].filterable.cell.template = function (el) {
                            $scope.filterTemplate(el);
                        };
                        break;
                }
            }
        }
        var obj = {
            dataSource: {
                data: "",
                schema: {
                    model: {
                        fields: {
                            adjustmentCode: { type: "string" },
                            adjustmentCodeStatus: { type: "string" },
                            date: { type: "string" },
                            transactioncomment: { type: "string" },
                            updatedby: { type: "string" },
                            year_1_amount: { type: "number" },
                            account: { type: "string" },
                            department: { type: "string" },
                            function: { type: "string" },
                            project: { type: "string" },
                            description: { type: "string" }
                        }
                    }
                },
                change: function (e) {
                }
            },
            columns: response.columns,
            sortable: true,
            filterable: {
                mode: "row"
            },
            sort: function (e) {
                // some columns are not sortable
                let sortField = (e.sort.field !== undefined) ? e.sort.field : "";
                let sortOrder = (e.sort.dir !== undefined) ? e.sort.dir : "";
                sortField = (sortOrder == "") ? "" : sortField;

                // apply sorting to the header grid
                if (notSortedColumns.indexOf(sortField) < 0 || sortField == "") {
                    var finalJsonData = vm.applyBudgetHistoryInputData(sortField, sortOrder);
                    vm.getAccountReportingGridData(finalJsonData, true);
                } else {
                    e.preventDefault();
                }

            },
            dataBound: function (e) {
                $(gridId).find(".k-grid-content").css('display', 'none');
                $(gridId).css("border", "none");
                $(gridId + " tr").css("border-color", "#c3c3c3");
                $(gridId + " tr td").css({ "border-left": "0 none", "border-right": "0 none" });
                $(gridId + " tr th").css({ "border-left": "0 none", "border-right": "0 none" });
                $(gridId).find("thead th:nth-child(2)").css("padding-left", "5px");
                $(gridId).find(".k-filter-row th:nth-child(2)").css("padding-left", "5px");

                $("#accountReportingSearchGrid .k-grid-header table tr:first th").each(function (i, e) {
                    let field = $(this).attr("data-field");
                    if (notSortedColumns.indexOf(field) == -1) {
                        $(this).find("a").css("color", "#4A8EB9");
                    }
                });
            },
        }

        if (accountReportingSearchGrid) {
            $(gridId).empty();
            $(gridId).data("kendoGrid").destroy();
        }

        accountReportingSearchGrid = $(gridId).kendoGrid(obj);

        $(gridId).find("thead tr.k-filter-row th:last-child").append("<button id='searchAccStmtBudgetTreeGrid' class='btn financialStatusBudgetTreeGridSearch' title='Search'>" + searchBtnTxt + "</button>");
        $(gridId).find('.financialStatusBudgetTreeGridSearch').addClass('btn-primary');
        $(gridId).find(".financialStatusBudgetTreeGridSearch").css({ "padding": "4px 15px", "min-width": "8px", "border-radius": "4px" });
        $(gridId).find(".k-filter-row").addClass("item-active-grey");

        // search click
        $(document).on("click", "#searchAccStmtBudgetTreeGrid", function (e) {
            $scope.searchFinancialStatusBudgetHistoryData();
        });
    }

    vm.getBudgetHistoryTreelistData = function (response) {
        var gridId = "#accountReportingGrid";

        if (response.columns.length > 0) {
            for (var i = 0; i < response.columns.length; i++) {
                switch (response.columns[i].field) {
                    case "year_1_amount":
                        response.columns[i].template = ' #=kendo.format("{0:' + response.columns[i].format + '}", year_1_amount)# ';
                        break;
                }
            }
        }

        if (accountReportingGrid) {
            $(gridId).data("kendoTreeList").destroy();
            $(gridId).empty();
        }
        accountReportingGrid = $(gridId).kendoTreeList({
            dataSource: {
                data: response.dataSource
            },
            columns: response.columns,
            dataBound: function (e) {
                $(gridId).find(".k-grid-header").css('display', 'none');
                $(gridId).css("color", "#000");
                $(gridId).css("border", "none");
                $(gridId + " tr:not(.k-treelist-group)").css("font-style", "italic");
                $(gridId + " tr td").css({ "border-left": "0 none", "border-right": "0 none" });
                $(gridId + " tr th").css({ "border-left": "0 none", "border-right": "0 none" });
                var gridHeight = $(window).height() - 300;
                $(gridId).find(".k-grid-content").css({ 'min-height': '100px', 'max-height': gridHeight, 'overflow-x': 'auto', 'overflow': 'auto' });
                if (response.dataSource.length == 0) {
                    $('#historyPopupTabSection').addClass('cmn-display-none');
                }
                else {
                    $('#historyPopupTabSection').removeClass('cmn-display-none');
                }
            },
        });
        EVENTS.loaderOFF(gridId);
    }

    $scope.expandedBudgetChangesHistoryTreeGrid = function (id) {
        var treeList = $("#accountReportingGrid").data("kendoTreeList");
        var dataItems = treeList.dataSource.data();
        $.each(dataItems, function (i, item) {
            item.expanded = id;
        });
        treeList.dataSource.data(dataItems);
    }
    $scope.filterTemplate = function (el) {
        $(el.element).removeAttr('data-bind');
        $(el.element).addClass("k-input k-textbox");
        $(el.element).attr('id', el.dataSource.options.data[0]);
        $("#" + el.dataSource.options.data[0]).on('keyup', function (e) {
            e.preventDefault();
            if (e.keyCode == 13) {
                $scope.searchFinancialStatusBudgetHistoryData();
            }
        });
    }
    $scope.searchFinancialStatusBudgetHistoryData = function () {

        var gridId = "#financialStatusBudgetDetailGrid";
        EVENTS.loaderON(gridId);

        var forecastPeriod = $("#monthlyReportYearSelector").data("kendoDropDownList").value();
        var parsePeriod = new Date(forecastPeriod);
        var month = parsePeriod.getMonth() + 1;
        var year = parsePeriod.getFullYear();

        var filterObjData = $scope.getBudgetHistoryTreelistSearchFilterData();

        var dropDownObjData = {
            monthYear: $("#monthlyReportYearSelector").data("kendoDropDownList").value(),
            level1OrgId: $scope.orgIdLevel1,
            level2OrgId: $scope.orgIdLevel2,
            level3OrgId: $scope.orgIdLevel3,
            level4OrgId: $scope.orgIdLevel4,
            level5OrgId: $scope.orgIdLevel5,
            orgId: $scope.orgMenuId,
            orgLevel: $scope.orgMenuLevel,
            orgName: $scope.orgName,
            serviceId: $scope.selectedServiceId,
            serviceName: $scope.selectedServiceName,
            accountCodeId: $scope.selectedBudgetDetailId,
            budget_year: year,
            filterObj: filterObjData
        }

        var finalJsonData = JSON.stringify(dropDownObjData);
        var responsePromise = $http.post('../api/MonthlyReportingDocApi/GetBudgetHistoryTreeListData', finalJsonData);
        responsePromise.success(function (response, status, headers, config) {

            $(gridId).data("kendoTreeList").setDataSource(response.dataSource);
            budgetChangesHistoryJsonObj = JSON.stringify({ dataSource: response.dataSource, columns: response.columns });
            EVENTS.loaderOFF(gridId);
        });
        responsePromise.error(function (xhrStatus, status, statusText, config) {
            EVENTS.loaderOFF(gridId);
            var methodName = 'searchFinancialStatusBudgetHistoryData';
            ERROR.displayException(xhrStatus, status, statusText, methodName);
        });
    }

    $scope.getBudgetHistoryTreelistSearchFilterData = function () {
        var filterObjData = {};
        filterObjData.date = $("#dateFilter").val() || "";
        filterObjData.transactioncomment = $("#transactioncommentFilter").val() || "";
        filterObjData.adjustmentCodeStatus = $("#adjustmentCodeStatusfilter").val() || "";
        filterObjData.updatedby = $("#updatedbyfilter").val() || "";
        filterObjData.year_1_amount = $("#year_1_amountfilter").val() || "";
        filterObjData.account = $("#accountfilter").val() || "";
        filterObjData.department = $("#departmentfilter").val() || "";
        filterObjData.function = $("#functionfilter").val() || "";
        filterObjData.project = $("#projectfilter").val() || "";
        filterObjData.free_dim_1 = $("#free_dim_1filter").val() || "";
        filterObjData.free_dim_2 = $("#free_dim_2filter").val() || "";
        filterObjData.free_dim_3 = $("#free_dim_3filter").val() || "";
        filterObjData.free_dim_4 = $("#free_dim_4filter").val() || "";
        filterObjData.accinfocomment = $("#accinfocommentfilter").val() || "";
        filterObjData.adjustmentCode = $("#adjustmentCodefilter").val() || "";
        filterObjData.description = $("#descriptionfilter").val() || "";
        return filterObjData;
    }
    vm.getAccountReportingTreeGridGridData = function (finalJsonData) {
        var gridId = "#accountReportingTreeGrid";
        EVENTS.loaderON(gridId);
        var responsePromise = $http.post("../api/AccountStmtReportApiController/BudgetChangesGridData", finalJsonData);
        responsePromise.success(function (response, status, headers, config) {
            $('#accountReportingTreeGridTitle').text(response.header[0].title);
            if (accountReportingTreeGrid) {
                $(gridId).data("kendoTreeList").destroy();
                $(gridId).empty();
            }

            accountReportingTreeGrid = $(gridId).kendoTreeList({
                dataSource: {
                    data: response.data
                },
                dataBound: function () {
                    var height = $(window).height() / 2 - 120;
                    $(gridId + " .k-grid-content.k-auto-scrollable").css('max-height', height);
                    $(gridId).find("tr:last").addClass("semi");
                },
                columns: response.columns
            });
            EVENTS.loaderOFF(gridId);
        });
        responsePromise.error(function (response, status, headers, config) {
            var methodName = 'Account statement reporting - getAccountsGridData';
            EVENTS.loaderOFF(gridId);
            ERROR.displayException(response, status, response.MessageDetail, methodName);
        });
    }

    vm.getFinancialStatusAccountDetails = function (id, name) {

        vm.selectedAccountDetailId = id;
        vm.selectedAccountDetailName = name;

        EVENTS.loaderON('#reportingFinStatusAccountDetailGrid');

        var filterObjData = {};
        filterObjData.Account = $("#Account").val() || "";
        filterObjData.Responsibility = $("#Responsibility").val() || "";
        filterObjData.Function = $("#Function").val() || "";
        filterObjData.Freedim = $("#Freedim").val() || "";
        filterObjData.Project = $("#Project").val() || "";
        filterObjData.Amount = $("#Amount").val() || "";
        filterObjData.Date = $("#Date").val() || "";
        filterObjData.Journo = $("#Journo").val() || "";
        filterObjData.ReskAccount = $("#ReskAccount").val() || "";
        filterObjData.Invoice = $("#Invoice").val() || "";
        filterObjData.Code = $("#Code").val() || "";
        filterObjData.Text = $("#Text").val() || "";
        filterObjData.Period = $("#Period").val() || "";

        var flag = false;
        for (let key in filterObjData) {
            if (filterObjData[key] !== "") {
                flag = true;
                break;
            }
        }

        var finalFilterObjData = null;
        if (flag === true) {
            finalFilterObjData = filterObjData;
        }
        vm.selectedServiceId = '';
        vm.selectedServiceName = '';
        var dropDownObjData = {
            monthYear: $("#accountStatementYearSelector").data("kendoDropDownList").value(),
            level1OrgId: orgIdLevel1,
            level2OrgId: orgIdLevel2,
            level3OrgId: orgIdLevel3,
            level4OrgId: orgIdLevel4,
            level5OrgId: orgIdLevel5,
            orgId: orgMenuId,
            orgLevel: orgMenuLevel,
            orgName: orgName,
            serviceId: vm.selectedServiceId,
            serviceName: vm.selectedServiceName,
            accountCodeId: vm.selectedAccountDetailId,
            accDetailSearchFilter: finalFilterObjData,
            departments: deptCodeArr,
            functions: funcCodeArr,
            projects: projectCodeArr,
            freeDim1: freeDim1Arr,
            freeDim2: freeDim2Arr,
            nextorg: nextOrgArr,
            servs: serviceAreaIdArr
        }

        getAccountYtdPopupDlg = $('#MRReporingFinancialStatusAccountWindow').kendoWindow({
            modal: true,
            actions: ["close"],
            scrollable: true,
            resizable: false,
            draggable: false,
            width: "95%",
            height: $(window).height() - 150
        }).data("kendoWindow").open();

        $('#MRReporingFinancialStatusAccountWindow').parent().css({ 'position': 'fixed', 'top': '10px', 'left': '2.5%' });
        var responsePromise = $http.post("../MonthlyReport/GetFinStatusAccountDetailGridCol?accountCodeName=" + name);
        responsePromise.success(function (response, status, headers, config) {

            // Adding manual filter row
            var columnsLength = response.columns.length;
            for (var t = 0; t < columnsLength; t++) {
                var columnType = response.columns[t].type;

                if (columnType === "date") {
                    response.columns[t]["filterable"] = {
                        cell: {
                            operator: "contains",
                            dataSource: {},
                            template: function (el) {
                                $(el.element).kendoDatePicker({
                                    format: "dd.MM.yyyy"
                                });
                                $(el.element).removeAttr('data-bind');
                            },
                            showOperators: false
                        }
                    };
                } else {
                    response.columns[t]["filterable"] = {
                        cell: {
                            operator: "contains",
                            dataSource: {},
                            template: function (el) {
                                $(el.element).removeAttr('data-bind');
                                $(el.element).addClass("k-input k-textbox");
                            },
                            showOperators: false
                        }
                    };
                }
            }

            EVENTS.loaderOFF('#reportingFinStatusAccountDetailGrid');

            getAccountYtdPopupDlg.element.prev().find(".k-window-title").html(response.header[0].title + '<span class="padding-left5 hand-pointer" data-role="tooltip" id="accountDetailGridDataTooltip"><img src="../images/icons_monthly_report_information.png"></span>');

            $('#accountDetailGridDataTooltip img').click(function (e) {
                MODULES.informationTooltipClick('#accountDetailGridDataTooltip', response.header[0].descriptiontip);
            });

            if ($('#reportingFinStatusAccountDetailGrid').data("kendoGrid")) {
                $('#reportingFinStatusAccountDetailGrid').data("kendoGrid").destroy();
                $('#reportingFinStatusAccountDetailGrid').empty();
            }

            var columns = [];
            columns = response.columns;

            var g = 1;
            $("#reportingFinStatusAccountDetailGrid").kendoGrid({
                dataSource: {
                    type: "json",
                    pageSize: 15,
                    //data: response.data,
                    transport: {
                        read: function (e) {
                            EVENTS.loaderON('#reportingFinStatusAccountDetailGrid');
                            var skip = e.data.skip;
                            var take = e.data.take;
                            if (take == undefined) {
                                take = vm.financialStatusAccountPopupTotalRows;
                            }
                            dropDownObjData.sortField = "";
                            dropDownObjData.sortOrder = "";
                            if (e.data.sort !== undefined && e.data.sort.length > 0) {
                                dropDownObjData.sortField = e.data.sort[0].field;
                                dropDownObjData.sortOrder = e.data.sort[0].dir;
                            }

                            var finalJsonData = JSON.stringify(dropDownObjData);

                            $http.post("../api/AccountStmtReportApiController/AccDetailGridData?skip=" + skip + "&take=" + take, finalJsonData)
                                .success(function (data) {
                                    EVENTS.loaderOFF('#reportingFinStatusAccountDetailGrid');
                                    e.success(data);
                                })
                                .error(function (data) {
                                    EVENTS.loaderOFF('#reportingFinStatusAccountDetailGrid');
                                    var methodName = 'getFinancialStatusAccountDetails';
                                    ERROR.displayException(response, response.textStatus, response.textStatus, methodName);
                                });
                        }
                    },
                    schema: {
                        model: {
                            fields: {
                                Account: { type: "number" },
                                Responsibility: { type: "number" },
                                Function: { type: "number" },
                                Freedim: { type: "string" },
                                Project: { type: "string" },
                                Amount: { type: "number" },
                                Journo: { type: "number" },
                                ReskAccount: { type: "string" },
                                Invoice: { type: "string" },
                                Text: { type: "string" }
                            }
                        },
                        data: function (response) {
                            vm.totalRowAccount = response.data.pop();
                            return response.data;
                        },
                        total: function (response) {
                            vm.financialStatusAccountPopupTotalRows = response.totalRows;
                            return response.totalRows;
                        }
                    },
                    change: function (e) {
                    },
                    serverPaging: true,
                    serverFiltering: false,
                    serverSorting: true
                },
                editable: false,
                scrollable: true,
                navigatable: true,
                sortable: true,
                filterable: {
                    mode: "row"
                },
                pageable: {
                    pageSize: 15,
                    pageSizes: [15, 25, 40, "all"],
                    buttonCount: 5
                },
                columns: columns,
                change: function (e) {
                    
                },
                dataBound: function () {
                    var t = this;
                    vm.gridWidthDivider("#reportingFinStatusAccountDetailGrid");
                    $("#reportingFinStatusAccountDetailGrid").find(".k-grid-content").css('max-height', $(window).height() - 400);
                    $("#reportingFinStatusAccountDetailGrid").find('.k-grid-header th.k-header').css({ "border-left-style": "solid" });
                    // appending id attr to filters
                    $("#reportingFinStatusAccountDetailGrid").find('.k-filter-row th span.k-filtercell').each(function (i, e) {
                        var field = $(this).attr('data-field');
                        $(this).find("input").attr("id", field);
                        if (dropDownObjData['accDetailSearchFilter'] !== null && dropDownObjData['accDetailSearchFilter'][field]) {
                            $(this).find("input").val(dropDownObjData['accDetailSearchFilter'][field]);
                        }
                    });

                    this.tbody.find('tr').each(function () {
                        var tr = $(this);
                        kendo.bind(tr, t.dataItem(tr));
                    });
                    if (vm.totalRowAccount) {
                        $("#Accountcol").html(vm.totalRowAccount.Account);
                        $("#Responsibilitycol").html(vm.totalRowAccount.Responsibility);
                        $("#Functioncol").html(vm.totalRowAccount.Function);
                        $("#Freedimcol").html(vm.totalRowAccount.Freedim);
                        $("#Projectcol").html(kendo.format('{0:n0}', vm.totalRowAccount.Project));
                        $("#Amountcol").html(kendo.format('{0:n0}', vm.totalRowAccount.Amount));
                        $("#Periodcol").html(vm.totalRowAccount.Period);
                        $("#Datecol").html(vm.totalRowAccount.Date);
                        $("#Journocol").html(vm.totalRowAccount.Journo);
                        $("#ReskAccountcol").html(vm.totalRowAccount.ReskAccount);
                        $("#Invoicecol").html(vm.totalRowAccount.Invoice);
                        $("#Codecol").html(vm.totalRowAccount.Code);
                        $("#Textcol").html(vm.totalRowAccount.Text);
                    } else {
                        $("#Accountcol").html("");
                        $("#Responsibilitycol").html("");
                        $("#Functioncol").html("");
                        $("#Freedimcol").html("");
                        $("#Projectcol").html("");
                        $("#Amountcol").html("");
                        $("#Periodcol").html("");
                        $("#Datecol").html("");
                        $("#Journocol").html("");
                        $("#ReskAccountcol").html("");
                        $("#Invoicecol").html("");
                        $("#Codecol").html("");
                        $("#Textcol").html("");
                    }

                    // apply color to header rows to indicate sorting enabled
                    $("#reportingFinStatusAccountDetailGrid .k-grid-header table tr:first th").each(function (i, e) {
                        $(this).find("a").css("color", "#4A8EB9");
                    });
                }
            });

            //$("#reportingFinStatusAccountDetailGrid").data("kendoGrid").refresh();

            $("#financialStausAccountDetailClose").click(function () {
                $("#financialStausAccountDetailClose").closest(".k-window-content").data("kendoWindow").close();
            });
        });

        responsePromise.error(function (response, status, headers, config) {
            EVENTS.loaderOFF('#reportingFinStatusAccountDetailGrid');
            var methodName = 'account statementReporting- getFinancialStatusAccountDetails';
            ERROR.displayException(response, status, response.MessageDetail, methodName);
        });

    }
    vm.gridWidthDivider = function (gridId) {

        var currGridLength = $(gridId + " tr th").length;
        var gridWidth = $(gridId).width();
        for (var g = 1; g <= currGridLength; g++) {
            var widthsL1 = $(gridId + " tr th:nth-last-child(" + g + ")").width();
            $(".k-grid-footer-wrap tr td:nth-last-child(" + g + ")").width(widthsL1);
        }

    },

        vm.getUpdatedBudgetDate = function () {
            var forecastPeriod = $("#accountStatementYearSelector").data("kendoDropDownList").value();
            var parsePeriod = new Date(forecastPeriod);
            var year = parsePeriod.getFullYear();
            var responsePromise = $http.get("../api/AccountStmtReportApiController/GetLastUpdatedDate?budgetYear=" + year);
            responsePromise.success(function (response, status, headers, config) {
                vm.updatedDate = response;
            });
            responsePromise.error(function (response, status, headers, config) {
                var methodName = 'getUpdatedBudgetDate';
                ERROR.displayException(response, status, response.MessageDetail, methodName);
            });
        }

    vm.getaccountStatementColumnSelector = function () {
        var forecastPeriod = $("#accountStatementYearSelector").data("kendoDropDownList").value();
        var parsePeriod = new Date(forecastPeriod);
        var year = parsePeriod.getFullYear();
        var responsePromise = $http.get("../api/AccountStmtReportApiController/GetColumnsConfig?budgetYear=" + year);
        responsePromise.success(function (response, status, headers, config) {
            if (localStorage.getItem("accountStatementColumns")) {
                vm.accountStatementColumns = JSON.parse(localStorage.getItem("accountStatementColumns"));
            }
            else {
                vm.accountStatementColumns = response;
            }
            vm.columnSelectorTooltipClick('#accountStatementColumnSelector', '#accountStatementPopupColSelector');
        });
        responsePromise.error(function (response, status, headers, config) {
            var methodName = 'account statementReporting - getaccountStatementColumnSelector';
            ERROR.displayException(response, status, response.MessageDetail, methodName);
        });
        vm.checkAccountStatementSaveAsStandard();
    }

    vm.checkAccountStatementSaveAsStandard = function () {
        var responsePromise = $http.get("../api/AccountStmtReportApiController/ShowColumnConfigSaveButton");
        responsePromise.success(function (response, status, headers, config) {
            vm.showAccStatementSAS = response;
        });
        responsePromise.error(function (response, status, headers, config) {
            var methodName = 'account statementReporting - checkAccountStatementSaveAsStandard';
            ERROR.displayException(response, status, response.MessageDetail, methodName);
        });
    }

    vm.accountStatementColumnSelectorOk = function () {
        var accountStatementColumnsStr = JSON.stringify(vm.accountStatementColumns);
        localStorage.setItem("accountStatementColumns", accountStatementColumnsStr);
        vm.getAccountsGridData();
    }

    vm.accountStatementColumnSelectorSave = function () {

        var forecastPeriod = $("#accountStatementYearSelector").data("kendoDropDownList").value();
        var parsePeriod = new Date(forecastPeriod);
        var year = parsePeriod.getFullYear();
        var finalJosnObj = '';
        var finalObj = { selectedColumns: vm.accountStatementColumns, orgLevel: orgMenuLevel, budgetYear: year }

        finalJosnObj = angular.toJson(finalObj);
        var responsePromise = $http.post("../api/AccountStmtReportApiController/SaveColumnsConfig", finalJosnObj);
        responsePromise.success(function (response, status, headers, config) {
            MODULES.saveConfirmation();
            vm.getAccountsGridData();
        });
        responsePromise.error(function (response, status, headers, config) {
            var methodName = 'account statementReporting - accountStatementColumnSelectorSave';
            ERROR.displayException(response, status, response.MessageDetail, methodName);
        });

    }

    vm.columnSelectorTooltipClick = function (id, contentID) {

        indicatorToolTipID = id;
        tooltipID = indicatorToolTipID.toString();

        columnSelectorToolTip = '';
        if (columnSelectorToolTip) {
            columnSelectorToolTip.data("kendoTooltip").destroy();
        }

        columnSelectorToolTip = $(tooltipID).kendoTooltip({
            content: function (e) {
                $(contentID).css("display", "block");
                var target = $(contentID); // the element for which the tooltip is shown
                return target; // set the element text as content of the tooltip
            },
            width: 200,
            showOn: "click",
            show: vm.indicatorContentShow,
            autoHide: false,
            position: "right"
        });
    }

    vm.indicatorContentShow = function (e) {

        this.popup.element.addClass("base-tooltip-popup");
        $("div[role='tooltip']").find('.k-i-close').css('visibility', 'hidden');
        $('.k-tooltip-content').css('padding-right', '0px');  // added to avoid custom padding added in custom css
        // $(tooltipID + '_tt_active').find('.k-i-close').css('visibility', 'hidden');
        $(tooltipID + '_tt_active').find('.k-callout-s').css('border-top-color', '#fff');
        $(tooltipID + '_tt_active').find('.k-callout-w').css({ 'border-right-color': '#fff' });
        $('.k-callout-n').css('border-bottom-color', '#fff');
        $(tooltipID + '_tt_active').find('.k-callout-e').css({ 'border-left-color': '#fff' });
    }

    vm.getBudgetPeriodFilterData = function () {
        // populate the filters
        var filterId = "#budgetPeriodFilterSection";
        EVENTS.loaderON(filterId);

        var obj = {
            monthYear: $("#accountStatementYearSelector").val(),
            orgLevel: parseInt(localStorage.getItem("accStatementOrgMenuLevel")),
            orgId: orgMenuId,
            level1OrgId: orgIdLevel1,
            level2OrgId: orgIdLevel2,
            level3OrgId: orgIdLevel3,
            level4OrgId: orgIdLevel4,
            level5OrgId: orgIdLevel5
        };
        var finalJSON = JSON.stringify(obj);
        var responsePromise = $http.post("../AccountStatementReport/GetFilterFields", finalJSON);
        responsePromise.success(function (response, status, headers, config) {
            var obj = {}

            multiBudDropDeptArray = [], multiBudDropFuncArray = [], multiBudDropProjChangeArray = [], multiBudDropfreeDim1Array = [], multiBudDropfreeDim2Array = [], multiBudDropNextOrgArray = [], multiBudDropServiceIdArray = [];
            budgetPeriodDeptData = [], budgetPeriodFuncData = [], budgetPeriodProjData = [], budgetPeriodFreeDim1Data = [], budgetPeriodFreeDim2Data = [], budgetPeriodNextOrgData = [], budgetPeriodServiceIdData = [];
            var deptDefaultOption = response.optionalLabels[2].value;
            var funDefaultOption = response.optionalLabels[3].value;
            var projDefaultOption = response.optionalLabels[4].value;
            var freeDim1DefaultOption = response.optionalLabels[5].value;
            var freeDim2DefaultOption = response.optionalLabels[6].value;
            var nextOrgDefaultOption = response.optionalLabels[0].value;
            var serviceIdDefaultOption = response.optionalLabels[1].value;

            budgetPeriodDeptData = response.department;
            budgetPeriodFuncData = response.function;
            budgetPeriodProjData = response.project;
            budgetPeriodFreeDim1Data = response.freeDim1;
            budgetPeriodFreeDim2Data = response.freeDim2;
            budgetPeriodNextOrgData = response.nextorg;
            budgetPeriodServiceIdData = response.servs;

            if (response.freeDim2.length !== 0) {
                vm.showFreedim2 = true;
            }

            if (response.nextorg.length !== 0) {
                vm.showNextOrg1 = true;
            }

            if (response.servs.length !== 0) {
                vm.showBPServiceId = true;
            }
            // Department code
            if (budgetPeriodDeptData.length != 0) {
                var deptCodeTemp;
                deptCodeBud = [];
                for (let i = 0; i < budgetPeriodDeptData.length; i++) {
                    if (budgetPeriodDeptData[i].value.length < 19) {
                        deptCodeTemp = budgetPeriodDeptData[i].value;
                    }
                    else {
                        deptCodeTemp = budgetPeriodDeptData[i].value.substring(0, 18) + "...";
                    }
                    obj = { key: budgetPeriodDeptData[i].key, value: deptCodeTemp };
                    multiBudDropDeptArray.push(obj);
                    obj = {};
                }

                $("#budgetPeriodDeptFilterMultiDropdown").kendoMultiSelect({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: multiBudDropDeptArray,
                    change: function (e) {
                        deptCodeBud = [];
                        deptCodeBud = $("#budgetPeriodDeptFilterMultiDropdown").data("kendoMultiSelect").value();
                        $("#budgetPeriodDeptFilterMultiDropdown-list").parent('.k-animation-container').css('display', 'none');
                        var currArrLength = $("#budgetPeriodDeptFilterMultiDropdown").data("kendoMultiSelect").value().length;
                        currArrLength == 0 ? $('#budgetPeriodFilterDeptMultiWrapper').removeClass('cmn-display-block').addClass('cmn-display-none') : $('#budgetPeriodFilterDeptMultiWrapper').removeClass('cmn-display-none').addClass('cmn-display-block');
                    },
                    dataBound: function () {
                        $("#budgetPeriodDeptFilterMultiDropdown").parent().find('.k-icon.k-clear-value.k-i-close').remove();
                        $("#budgetPeriodDeptFilterMultiDropdown").data("kendoMultiSelect").value('');
                    }
                });

                $("#budgetPeriodDeptFilterDropdown").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: budgetPeriodDeptData,
                    optionLabel: {
                        value: '' + $.parseHTML(deptDefaultOption)[0].data + '',
                        key: "Empty"
                    },
                    change: function (e) {
                        var index = e.sender.selectedIndex - 1;
                        if (index != -1) {
                            deptCodeBud.push(budgetPeriodDeptData[index].key)
                        }
                        if (deptCodeBud.length != 0) {
                            $('#budgetPeriodFilterDeptMultiWrapper').removeClass('cmn-display-none').addClass('cmn-display-block');
                            $("#budgetPeriodDeptFilterMultiDropdown").data("kendoMultiSelect").value(deptCodeBud);
                            $("#budgetPeriodDeptFilterDropdown").data("kendoDropDownList").select(0);
                        }
                        else {
                            $('#budgetPeriodFilterDeptMultiWrapper').removeClass('cmn-display-block').addClass('cmn-display-none');
                        }
                    }
                });
                var dropdownlistDept = $("#budgetPeriodDeptFilterDropdown").data("kendoDropDownList");
                dropdownlistDept.select(0);
                $('#deptFilterDropdown-list.k-list-container.k-popup').css({ 'width': '320px' });
                deptCodeBudMultiSelect = $('#budgetPeriodDeptFilterMultiDropdown').data("kendoMultiSelect");
            }
            else {
                $("#budgetPeriodDeptFilterDropdown").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: budgetPeriodDeptData,
                    optionLabel: {
                        value: '' + $.parseHTML(deptDefaultOption)[0].data + '',
                        key: "Empty"
                    }
                });
            }

            // Function code
            if (budgetPeriodFuncData.length != 0) {
                var funcCodeTemp;
                funcCodeBud = [];
                for (let i = 0; i < budgetPeriodFuncData.length; i++) {
                    if (budgetPeriodFuncData[i].value.length < 19) {
                        funcCodeTemp = budgetPeriodFuncData[i].value;
                    }
                    else {
                        funcCodeTemp = budgetPeriodFuncData[i].value.substring(0, 18) + "...";
                    }
                    obj = { key: budgetPeriodFuncData[i].key, value: funcCodeTemp };
                    multiBudDropFuncArray.push(obj);
                    obj = {};
                }

                $("#budgetPeriodFunctionFilterMultiDropdown").kendoMultiSelect({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: multiBudDropFuncArray,
                    change: function (e) {
                        funcCodeBud = [];
                        funcCodeBud = $("#budgetPeriodFunctionFilterMultiDropdown").data("kendoMultiSelect").value();
                        $("#budgetPeriodFunctionFilterMultiDropdown-list").parent('.k-animation-container').css('display', 'none');
                        var currArrLength = $("#budgetPeriodFunctionFilterMultiDropdown").data("kendoMultiSelect").value().length;
                        currArrLength == 0 ? $('#filterFunMultiWrapper').removeClass('cmn-display-block').addClass('cmn-display-none') : $('#filterFunMultiWrapper').removeClass('cmn-display-none').addClass('cmn-display-block');
                    },
                    dataBound: function () {
                        $("#budgetPeriodFunctionFilterMultiDropdown").parent().find('.k-icon.k-clear-value.k-i-close').remove();
                        $("#budgetPeriodFunctionFilterMultiDropdown").data("kendoMultiSelect").value('');
                    }
                });

                $("#budgetPeriodFunctionFilterDropdown").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: budgetPeriodFuncData,
                    optionLabel: {
                        value: '' + $.parseHTML(funDefaultOption)[0].data + '',
                        key: "Empty"
                    },
                    change: function (e) {
                        var index = e.sender.selectedIndex - 1;
                        if (index != -1) {
                            funcCodeBud.push(budgetPeriodFuncData[index].key)
                        }
                        if (funcCodeBud.length != 0) {
                            $('#budgetPeriodFilterFunMultiWrapper').removeClass('cmn-display-none').addClass('cmn-display-block');
                            $("#budgetPeriodFunctionFilterMultiDropdown").data("kendoMultiSelect").value(funcCodeBud);
                            $("#budgetPeriodFunctionFilterDropdown").data("kendoDropDownList").select(0);
                        }
                        else {
                            $('#budgetPeriodFilterFunMultiWrapper').removeClass('cmn-display-block').addClass('cmn-display-none');
                        }
                    }
                });
                var dropdownlistFunc = $("#budgetPeriodFunctionFilterDropdown").data("kendoDropDownList");
                dropdownlistFunc.select(0);
                $('#budgetPeriodFunctionFilterDropdown-list.k-list-container.k-popup').css({ 'width': '320px' });
                funcCodeBudMultiSelect = $('#budgetPeriodFunctionFilterMultiDropdown').data("kendoMultiSelect");
            }
            else {
                $("#budgetPeriodFunctionFilterDropdown").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: budgetPeriodFuncData,
                    optionLabel: {
                        value: '' + $.parseHTML(funDefaultOption)[0].data + '',
                        key: "Empty"
                    }
                });
            }

            // Project code
            if (budgetPeriodProjData.length != 0) {
                var projectCodeTemp;
                projectCodeBud = [];
                for (let i = 0; i < budgetPeriodProjData.length; i++) {
                    if (budgetPeriodProjData[i].value.length < 19) {
                        projectCodeTemp = budgetPeriodProjData[i].value;
                    }
                    else {
                        projectCodeTemp = budgetPeriodProjData[i].value.substring(0, 18) + "...";
                    }
                    obj = { key: budgetPeriodProjData[i].key, value: projectCodeTemp };
                    multiBudDropProjChangeArray.push(obj);
                    obj = {};
                }

                $("#budgetPeriodProjFilterMultiDropdown").kendoMultiSelect({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: multiBudDropProjChangeArray,
                    change: function (e) {
                        projectCodeBud = [];
                        projectCodeBud = $("#budgetPeriodProjFilterMultiDropdown").data("kendoMultiSelect").value();
                        $("#budgetPeriodProjFilterMultiDropdown-list").parent('.k-animation-container').css('display', 'none');
                        var currArrLength = $("#budgetPeriodProjFilterMultiDropdown").data("kendoMultiSelect").value().length;
                        currArrLength == 0 ? $('#filterProjMultiWrapper').removeClass('cmn-display-block').addClass('cmn-display-none') : $('#filterProjMultiWrapper').removeClass('cmn-display-none').addClass('cmn-display-block');
                    },
                    dataBound: function () {
                        $("#budgetPeriodProjFilterMultiDropdown").parent().find('.k-icon.k-clear-value.k-i-close').remove();
                        $("#budgetPeriodProjFilterMultiDropdown").data("kendoMultiSelect").value('');
                    }
                });
                $("#budgetPeriodProjFilterDropdown").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: budgetPeriodProjData,
                    optionLabel: {
                        value: '' + $.parseHTML(projDefaultOption)[0].data + '',
                        key: "Empty"
                    },
                    change: function (e) {
                        var index = e.sender.selectedIndex - 1;
                        if (index != -1) {
                            projectCodeBud.push(budgetPeriodProjData[index].key)
                        }
                        if (projectCodeBud.length != 0) {
                            $('#budgetPeriodFilterProjMultiWrapper').removeClass('cmn-display-none').addClass('cmn-display-block');
                            $("#budgetPeriodProjFilterMultiDropdown").data("kendoMultiSelect").value(projectCodeBud);
                            $("#budgetPeriodProjFilterDropdown").data("kendoDropDownList").select(0);
                        }
                        else {
                            $('#budgetPeriodFilterProjMultiWrapper').removeClass('cmn-display-block').addClass('cmn-display-none');
                        }
                    }
                });
                var dropdownlistProject = $("#budgetPeriodProjFilterDropdown").data("kendoDropDownList");
                dropdownlistProject.select(0);
                $('#budgetPeriodProjFilterDropdown-list.k-list-container.k-popup').css({ 'width': '320px' });
                projectCodeBudMultiSelect = $('#budgetPeriodProjFilterMultiDropdown').data("kendoMultiSelect");
            }
            else {
                $("#budgetPeriodProjFilterDropdown").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: budgetPeriodProjData,
                    optionLabel: {
                        value: '' + $.parseHTML(projDefaultOption)[0].data + '',
                        key: "Empty"
                    }
                });
            }

            // Freedim1 code

            if (budgetPeriodFreeDim1Data.length != 0) {
                var freeDim1CodeTemp;
                freeDim1Bud = [];
                for (let i = 0; i < budgetPeriodFreeDim1Data.length; i++) {
                    if (budgetPeriodFreeDim1Data[i].value.length < 19) {
                        freeDim1CodeTemp = budgetPeriodFreeDim1Data[i].value;
                    }
                    else {
                        freeDim1CodeTemp = budgetPeriodFreeDim1Data[i].value.substring(0, 18) + "...";
                    }
                    obj = { key: budgetPeriodFreeDim1Data[i].key, value: freeDim1CodeTemp };
                    multiBudDropfreeDim1Array.push(obj);
                    obj = {};
                }

                $("#budgetPeriodFreeDimFilterMultiDropdown1").kendoMultiSelect({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: multiBudDropfreeDim1Array,
                    change: function (e) {
                        freeDim1Bud = [];
                        freeDim1Bud = $("#budgetPeriodFreeDimFilterMultiDropdown1").data("kendoMultiSelect").value();
                        $("#budgetPeriodFreeDimFilterMultiDropdown1-list").parent('.k-animation-container').css('display', 'none');
                        var currArrLength = $("#budgetPeriodFreeDimFilterMultiDropdown1").data("kendoMultiSelect").value().length;
                        currArrLength == 0 ? $('#filterFreedimMultiWrapper1').removeClass('cmn-display-block').addClass('cmn-display-none') : $('#filterFreedimMultiWrapper1').removeClass('cmn-display-none').addClass('cmn-display-block');
                    },
                    dataBound: function () {
                        $("#budgetPeriodFreeDimFilterMultiDropdown1").parent().find('.k-icon.k-clear-value.k-i-close').remove();
                        $("#budgetPeriodFreeDimFilterMultiDropdown1").data("kendoMultiSelect").value('');
                    }
                });

                $("#budgetPeriodFreeDimFilterDropdown1").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: budgetPeriodFreeDim1Data,
                    optionLabel: {
                        value: '' + $.parseHTML(freeDim1DefaultOption)[0].data + '',
                        key: "Empty"
                    },
                    change: function (e) {
                        var index = e.sender.selectedIndex - 1;
                        if (index != -1) {
                            freeDim1Bud.push(budgetPeriodFreeDim1Data[index].key)
                        }
                        if (freeDim1Bud.length != 0) {
                            $('#budgetPeriodFilterFreedimMultiWrapper1').removeClass('cmn-display-none').addClass('cmn-display-block');
                            $("#budgetPeriodFreeDimFilterMultiDropdown1").data("kendoMultiSelect").value(freeDim1Bud);
                            $("#budgetPeriodFreeDimFilterDropdown1").data("kendoDropDownList").select(0);
                        }
                        else {
                            $('#budgetPeriodFilterFreedimMultiWrapper1').removeClass('cmn-display-block').addClass('cmn-display-none');
                        }
                    }
                });
                var dropdownlistFreeDim1 = $("#budgetPeriodFreeDimFilterDropdown1").data("kendoDropDownList");
                dropdownlistFreeDim1.select(0);
                $('#budgetPeriodFreeDimFilterDropdown1-list.k-list-container.k-popup').css({ 'width': '320px' });
                freeDim1BudMultiSelect = $('#budgetPeriodFreeDimFilterMultiDropdown1').data("kendoMultiSelect");
            }
            else {
                $("#budgetPeriodFreeDimFilterDropdown1").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: budgetPeriodFreeDim1Data,
                    optionLabel: {
                        value: '' + $.parseHTML(freeDim1DefaultOption)[0].data + '',
                        key: "Empty"
                    }
                });
            }

            // Freedim2 code

            if (budgetPeriodFreeDim2Data.length != 0) {
                var freeDim2CodeTemp;
                freeDim2Bud = [];
                for (let i = 0; i < budgetPeriodFreeDim2Data.length; i++) {
                    if (budgetPeriodFreeDim2Data[i].value.length < 19) {
                        freeDim2CodeTemp = budgetPeriodFreeDim2Data[i].value;
                    }
                    else {
                        freeDim2CodeTemp = budgetPeriodFreeDim2Data[i].value.substring(0, 18) + "...";
                    }
                    obj = { key: budgetPeriodFreeDim2Data[i].key, value: freeDim2CodeTemp };
                    multiBudDropfreeDim2Array.push(obj);
                    obj = {};
                }

                $("#budgetPeriodFreeDimFilterMultiDropdown2").kendoMultiSelect({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: multiBudDropfreeDim2Array,
                    change: function (e) {
                        freeDim2Bud = [];
                        freeDim2Bud = $("#budgetPeriodFreeDimFilterMultiDropdown2").data("kendoMultiSelect").value();
                        $("#budgetPeriodFreeDimFilterMultiDropdown2-list").parent('.k-animation-container').css('display', 'none');
                        var currArrLength = $("#budgetPeriodFreeDimFilterMultiDropdown2").data("kendoMultiSelect").value().length;
                        currArrLength == 0 ? $('#filterFreedimMultiWrapper2').removeClass('cmn-display-block').addClass('cmn-display-none') : $('#filterFreedimMultiWrapper2').removeClass('cmn-display-none').addClass('cmn-display-block');
                    },
                    dataBound: function () {
                        $("#budgetPeriodFreeDimFilterMultiDropdown2").parent().find('.k-icon.k-clear-value.k-i-close').remove();
                        $("#budgetPeriodFreeDimFilterMultiDropdown2").data("kendoMultiSelect").value('');
                    }
                });

                $("#budgetPeriodFreeDimFilterDropdown2").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: budgetPeriodFreeDim2Data,
                    optionLabel: {
                        value: '' + $.parseHTML(freeDim2DefaultOption)[0].data + '',
                        key: "Empty"
                    },
                    change: function (e) {
                        var index = e.sender.selectedIndex - 1;
                        if (index != -1) {
                            freeDim2Bud.push(budgetPeriodFreeDim2Data[index].key)
                        }
                        if (freeDim2Bud.length != 0) {
                            $('#budgetPeriodFilterFreedimMultiWrapper2').removeClass('cmn-display-none').addClass('cmn-display-block');
                            $("#budgetPeriodFreeDimFilterMultiDropdown2").data("kendoMultiSelect").value(freeDim2Bud);
                            $("#budgetPeriodFreeDimFilterDropdown2").data("kendoDropDownList").select(0);
                        }
                        else {
                            $('#budgetPeriodFilterFreedimMultiWrapper2').removeClass('cmn-display-block').addClass('cmn-display-none');
                        }
                    }
                });
                var dropdownlistFreeDim2 = $("#budgetPeriodFreeDimFilterDropdown2").data("kendoDropDownList");
                dropdownlistFreeDim2.select(0);
                $('#budgetPeriodFreeDimFilterDropdown2-list.k-list-container.k-popup').css({ 'width': '320px' });
                freeDim2BudMultiSelect = $('#budgetPeriodFreeDimFilterMultiDropdown2').data("kendoMultiSelect");
            }
            else {
                $("#budgetPeriodFreeDimFilterDropdown2").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: budgetPeriodFreeDim2Data,
                    optionLabel: {
                        value: '' + $.parseHTML(freeDim2DefaultOption)[0].data + '',
                        key: "Empty"
                    }
                });
            }

            // Next org code
            if (budgetPeriodNextOrgData.length != 0) {
                var nextOrgCodeTemp;
                nextOrgCodeBud = [];
                for (let i = 0; i < budgetPeriodNextOrgData.length; i++) {
                    if (budgetPeriodNextOrgData[i].value.length < 19) {
                        nextOrgCodeTemp = budgetPeriodNextOrgData[i].value;
                    }
                    else {
                        nextOrgCodeTemp = budgetPeriodNextOrgData[i].value.substring(0, 18) + "...";
                    }
                    obj = { key: budgetPeriodNextOrgData[i].key, value: nextOrgCodeTemp };
                    multiBudDropNextOrgArray.push(obj);
                    obj = {};
                }

                $("#budgetPeriodNextOrgFilterMultiDropdown").kendoMultiSelect({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: multiBudDropNextOrgArray,
                    change: function (e) {
                        nextOrgCodeBud = [];
                        nextOrgCodeBud = $("#budgetPeriodNextOrgFilterMultiDropdown").data("kendoMultiSelect").value();
                        $("#budgetPeriodNextOrgFilterMultiDropdown-list").parent('.k-animation-container').css('display', 'none');
                        var currArrLength = $("#budgetPeriodNextOrgFilterMultiDropdown").data("kendoMultiSelect").value().length;
                        currArrLength == 0 ? $('#budgetPeriodFilterNextOrgMultiWrapper').addClass('cmn-display-none') : $('#budgetPeriodFilterNextOrgMultiWrapper').removeClass('cmn-display-none');
                    },
                    dataBound: function () {
                        $("#budgetPeriodNextOrgFilterMultiDropdown").parent().find('.k-icon.k-clear-value.k-i-close').remove();
                        $("#budgetPeriodNextOrgFilterMultiDropdown").data("kendoMultiSelect").value('');
                    }
                });

                $("#budgetPeriodNextOrgFilterDropdown").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: budgetPeriodNextOrgData,
                    optionLabel: {
                        value: '' + $.parseHTML(nextOrgDefaultOption)[0].data + '',
                        key: "Empty"
                    },
                    change: function (e) {
                        var index = e.sender.selectedIndex - 1;
                        if (index != -1) {
                            nextOrgCodeBud.push(budgetPeriodNextOrgData[index].key)
                        }
                        if (nextOrgCodeBud.length != 0) {
                            $('#budgetPeriodFilterNextOrgMultiWrapper').removeClass('cmn-display-none');
                            $("#budgetPeriodNextOrgFilterMultiDropdown").data("kendoMultiSelect").value(nextOrgCodeBud);
                            $("#budgetPeriodNextOrgFilterDropdown").data("kendoDropDownList").select(0);
                        }
                        else {
                            $('#budgetPeriodFilterNextOrgMultiWrapper').addClass('cmn-display-none');
                        }
                    }
                });
                var dropdownlistNextOrg = $("#budgetPeriodNextOrgFilterDropdown").data("kendoDropDownList");
                dropdownlistNextOrg.select(0);
                $('#budgetPeriodNextOrgFilterDropdown-list.k-list-container.k-popup').css({ 'width': '320px' });
                budPeriodNextOrgMultiSelect = $('#budgetPeriodNextOrgFilterMultiDropdown').data("kendoMultiSelect");
            }
            else {
                $("#budgetPeriodNextOrgFilterDropdown").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: budgetPeriodNextOrgData,
                    optionLabel: {
                        value: '' + $.parseHTML(nextOrgDefaultOption)[0].data + '',
                        key: "Empty"
                    }
                });
            }
            // ServiceId
            if (budgetPeriodServiceIdData.length != 0) {
                var serviceIdTemp;
                serviceIdBud = [];
                for (let i = 0; i < budgetPeriodServiceIdData.length; i++) {
                    if (budgetPeriodServiceIdData[i].value.length < 19) {
                        serviceIdTemp = budgetPeriodServiceIdData[i].value;
                    }
                    else {
                        serviceIdTemp = budgetPeriodServiceIdData[i].value.substring(0, 18) + "...";
                    }
                    obj = { key: budgetPeriodServiceIdData[i].key, value: serviceIdTemp };
                    multiBudDropServiceIdArray.push(obj);
                    obj = {};
                }

                $("#budgetPeriodServiceIdFilterMultiDropdown").kendoMultiSelect({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: multiBudDropServiceIdArray,
                    change: function (e) {
                        serviceIdBud = [];
                        serviceIdBud = $("#budgetPeriodServiceIdFilterMultiDropdown").data("kendoMultiSelect").value();
                        $("#budgetPeriodServiceIdFilterMultiDropdown-list").parent('.k-animation-container').css('display', 'none');
                        var currArrLength = $("#budgetPeriodServiceIdFilterMultiDropdown").data("kendoMultiSelect").value().length;
                        currArrLength == 0 ? $('#budgetPeriodFilterServiceIdMultiWrapper').addClass('cmn-display-none') : $('#budgetPeriodFilterServiceIdMultiWrapper').removeClass('cmn-display-none');
                    },
                    dataBound: function () {
                        $("#budgetPeriodServiceIdFilterMultiDropdown").parent().find('.k-icon.k-clear-value.k-i-close').remove();
                        $("#budgetPeriodServiceIdFilterMultiDropdown").data("kendoMultiSelect").value('');
                    }
                });

                $("#budgetPeriodServiceIdFilterDropdown").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: budgetPeriodServiceIdData,
                    optionLabel: {
                        value: '' + $.parseHTML(serviceIdDefaultOption)[0].data + '',
                        key: "Empty"
                    },
                    change: function (e) {
                        var index = e.sender.selectedIndex - 1;
                        if (index != -1) {
                            serviceIdBud.push(budgetPeriodServiceIdData[index].key);
                        }
                        if (serviceIdBud.length != 0) {
                            $('#budgetPeriodFilterServiceIdMultiWrapper').removeClass('cmn-display-none');
                            $("#budgetPeriodServiceIdFilterMultiDropdown").data("kendoMultiSelect").value(serviceIdBud);
                            $("#budgetPeriodServiceIdFilterDropdown").data("kendoDropDownList").select(0);
                        }
                        else {
                            $('#budgetPeriodFilterServiceIdMultiWrapper').addClass('cmn-display-none');
                        }
                    }
                });
                var dropdownlistServiceId = $("#budgetPeriodServiceIdFilterDropdown").data("kendoDropDownList");
                dropdownlistServiceId.select(0);
                $('#budgetPeriodServiceIdFilterDropdown-list.k-list-container.k-popup').css({ 'width': '320px' });
                budPeriodServiceIdMultiSelect = $('#budgetPeriodServiceIdFilterMultiDropdown').data("kendoMultiSelect");
            }
            else {
                $("#budgetPeriodServiceIdFilterDropdown").kendoDropDownList({
                    dataTextField: "value",
                    dataValueField: "key",
                    dataSource: budgetPeriodServiceIdData,
                    optionLabel: {
                        value: '' + $.parseHTML(serviceIdDefaultOption)[0].data + '',
                        key: "Empty"
                    }
                });
            }
            vm.getBudgetPeriodTreelistData();
            EVENTS.loaderOFF(filterId);
        });
        responsePromise.error(function (response, status, headers, config) {
            EVENTS.loaderOFF("#budgetPeriodFilterSection");
            var methodName = 'Account statement reporting - getAccountingFilterData';
            EVENTS.loaderOFF(filterId);
            ERROR.displayException(response, status, response.MessageDetail, methodName);
        });
    }

    vm.multiSelectFilterClearingDataBudgetPeriod = function () {
        multiBudDropDeptArray = [], multiBudDropFuncArray = [], multiBudDropProjChangeArray = [], multiBudDropfreeDim1Array = [], multiBudDropfreeDim2Array = [], multiBudDropNextOrgArray = [], multiBudDropServiceIdArray = [];
        budgetPeriodDeptData = [], budgetPeriodFuncData = [], budgetPeriodProjData = [], budgetPeriodFreeDim1Data = [], budgetPeriodFreeDim2Data = [], budgetPeriodNextOrgData = [], budgetPeriodServiceIdData = [];
        deptCodeBud = [], funcCodeBud = [], projectCodeBud = [], freeDim1Bud = [], freeDim2Bud = [], nextOrgCodeBud = [], serviceIdBud = [];

        if (deptCodeBudMultiSelect != "") { $("#budgetPeriodDeptFilterMultiDropdown").data("kendoMultiSelect").value(''); }
        if (funcCodeBudMultiSelect != "") { $("#budgetPeriodFunctionFilterMultiDropdown").data("kendoMultiSelect").value(''); }
        if (projectCodeBudMultiSelect != "") { $("#budgetPeriodProjFilterMultiDropdown").data("kendoMultiSelect").value(''); }
        if (freeDim1BudMultiSelect != "") { $("#budgetPeriodFreeDimFilterMultiDropdown1").data("kendoMultiSelect").value(''); }
        if (freeDim2BudMultiSelect != "") { $("#budgetPeriodFreeDimFilterMultiDropdown2").data("kendoMultiSelect").value(''); }
        if (budPeriodNextOrgMultiSelect != "") { $("#budgetPeriodNextOrgFilterMultiDropdown").data("kendoMultiSelect").value(''); }
        if (budPeriodServiceIdMultiSelect != "") { $("#budgetPeriodServiceIdFilterMultiDropdown").data("kendoMultiSelect").value(''); }

        if ($('.common-multidrop-wrapper').hasClass('cmn-display-block')) {
            $('.common-multidrop-wrapper').removeClass('cmn-display-block').addClass('cmn-display-none');
        }
    }


    vm.orgLevelSetup = function () {

        orgMenuLevel = '';
        orgMenuId = '';

        // org level service values from selected service dropdown menu
        orgIdLevel1 = localStorage.getItem("orgValueLevel1") || null;
        orgIdLevel2 = localStorage.getItem("orgValueLevel2") || null;
        orgIdLevel3 = localStorage.getItem("orgValueLevel3") || null;
        orgIdLevel4 = localStorage.getItem("orgValueLevel4") || null;
        orgIdLevel5 = localStorage.getItem("orgValueLevel5") || null;
        orgLastLevel = localStorage.getItem("orgValueLastLevel");

        if (orgIdLevel1 !== null) { //level 1

            orgMenuLevel = 1;
            orgMenuId = orgIdLevel1.toString();

            if (orgIdLevel2 !== null) { //level 2
                orgMenuLevel = 2;
                orgMenuId = orgIdLevel2.toString();

                if (orgIdLevel3 !== null) { //level 3
                    orgMenuLevel = 3;
                    orgMenuId = orgIdLevel3.toString();

                    if (orgIdLevel4 !== null) { //level 4
                        orgMenuLevel = 4;
                        orgMenuId = orgIdLevel4.toString();

                        if (orgIdLevel5 !== null) { //level 5
                            orgMenuLevel = 5;
                            orgMenuId = orgIdLevel5.toString();
                        }
                    }
                }
            }
        }
        // selected org level name
        orgName = localStorage.getItem("orgValueLevelSelectName") || "";
        localStorage.setItem("accStatementOrgMenuLevel", orgMenuLevel);
        localStorage.setItem("accStatementOrgId", orgMenuId);

        // updating factory variables
        getInputObj.orgIdLevel1 = orgIdLevel1;
        getInputObj.orgIdLevel2 = orgIdLevel2;
        getInputObj.orgIdLevel3 = orgIdLevel3;
        getInputObj.orgIdLevel4 = orgIdLevel4;
        getInputObj.orgIdLevel5 = orgIdLevel5;

        getInputObj.orgMenuId = orgMenuId;
        getInputObj.orgMenuLevel = orgMenuLevel;

        vm.getMROrgText();
        vm.getaccountStatementColumnSelector();
        vm.getbudgetPeriodColumnSelector();
        vm.tabLoadFunctionCalls();

    }

    angular.element(document).ready(function () {
        // interval to check for org level menu setup
        vm.monthYearSelector();


        var bannerBgHeight = $('.accounting-overview-bg').height();
        MODULES.bannerHideShow(bannerBgHeight, '#collapseBannerSection', '.accounting-overview-bg', true, "RASBannerHide");

        $('#accountStatementTitleDesc img').click(function (e) {
            MODULES.informationTooltipClick('#accountStatementTitleDesc', localStorage.getItem("AccountStatementTooltipDesc"));
        });

        var budgetyear = $("#accountStatementYearSelector").val();
        $("#finStatusAccountFilterSearch").click(function () {
            vm.getFinancialStatusAccountDetails(vm.selectedAccountDetailId, vm.selectedAccountDetailName);
        });

        $('#budgetPeriodFilters').click(function (e) {
            $(this).toggleClass('expand-collapse-filter');
            if ($(this).hasClass('expand-collapse-filter')) {
                $(this).prev('.filter-contents').hide();
                $('#budgetPeriodTreelistSection').css({ 'width': '98%' });
                $('#budgetPeriodFilterSection').css({ 'width': '2%' });
                $('#budgetPeriodFilters .expand-collapse-filters').html(localStorage.getItem('OpenFilter'));
            }
            else {
                $(this).prev('.filter-contents').show();
                $('#budgetPeriodTreelistSection').removeAttr('style');
                $('#budgetPeriodFilterSection').removeAttr('style');
                $('#budgetPeriodFilters .expand-collapse-filters').html(localStorage.getItem('HideFilter'));
            }
            $(this).addClass('not-active');
            vm.getBudgetPeriodTreelistData();
        });
        $('#accountStatementFilter').click(function (e) {
            $(this).toggleClass('expand-collapse-filter');
            if ($(this).hasClass('expand-collapse-filter')) {
                $(this).prev('.filter-contents').hide();
                $('#accountFilterContentSec').css({ 'width': '98%' });
                $('#accStatementFilterSection').css({ 'width': '2%' });
                $('#accountStatementFilter .expand-collapse-filters').html(localStorage.getItem('OpenFilter'));
            }
            else {
                $(this).prev('.filter-contents').show();
                $('#accountFilterContentSec').removeAttr('style');
                $('#accStatementFilterSection').removeAttr('style');
                $('#accountStatementFilter .expand-collapse-filters').html(localStorage.getItem('HideFilter'));

            }
            //$(this).addClass('not-active');
            //vm.getAccountsGridData();
        });
    });

    // get tooltip columns
    vm.getbudgetPeriodColumnSelector = function () {
        var forecastPeriod = $("#accountStatementYearSelector").data("kendoDropDownList").value();
        var parsePeriod = new Date(forecastPeriod);
        var year = parsePeriod.getFullYear();
        var responsePromise = $http.get("../api/AccountStmtReportApiController/GetAcPeriodGridColCfg?budgetYear=" + year);
        responsePromise.success(function (response, status, headers, config) {
            if (localStorage.getItem("budgetPeriodSelColumns")) {
                vm.budgetPeriodSelColumns = JSON.parse(localStorage.getItem("budgetPeriodSelColumns"));
            }
            else {
                vm.budgetPeriodSelColumns = response;
            }
            vm.columnSelectorTooltipClick('#budgetPeriodTreelistColumnSelector', '#budgetPeriodPopupColSelector');
        });
        responsePromise.error(function (response, status, headers, config) {
            var methodName = 'account statementReporting - getbudgetPeriodColumnSelector';
            ERROR.displayException(response, status, response.MessageDetail, methodName);
        });
        vm.checkBudgetPeriodSaveAsStandard();
    }

    vm.checkBudgetPeriodSaveAsStandard = function () {
        var responsePromise = $http.get("../api/AccountStmtReportApiController/ShowColumnConfigSaveButton");
        responsePromise.success(function (response, status, headers, config) {
            vm.showAccStatementSAS = response;
        });
        responsePromise.error(function (response, status, headers, config) {
            var methodName = 'account statementReporting - checkBudgetPeriodSaveAsStandard';
            ERROR.displayException(response, status, response.MessageDetail, methodName);
        });
    }
    // load selected columns on grid
    vm.budgetPeriodColumnSelectorOk = function () {
        var budgetPeriodColumnsStr = JSON.stringify(vm.budgetPeriodSelColumns);
        localStorage.setItem("budgetPeriodSelColumns", budgetPeriodColumnsStr);
        vm.getBudgetPeriodTreelistData();
    }

    // save
    vm.budgetPeriodColumnSelectorSave = function () {

        var forecastPeriod = $("#accountStatementYearSelector").data("kendoDropDownList").value();
        var parsePeriod = new Date(forecastPeriod);
        var year = parsePeriod.getFullYear();
        var finalJosnObj = '';
        var finalObj = { selectedColumns: vm.budgetPeriodSelColumns, orgLevel: orgMenuLevel, budgetYear: year }

        finalJosnObj = angular.toJson(finalObj);
        var responsePromise = $http.post("../api/AccountStmtReportApiController/SaveAcPeriodGridColCfg", finalJosnObj);
        responsePromise.success(function (response, status, headers, config) {
            MODULES.saveConfirmation();
            vm.getBudgetPeriodTreelistData();
        });
        responsePromise.error(function (response, status, headers, config) {
            var methodName = 'account statementReporting - budgetPeriodColumnSelectorSave';
            ERROR.displayException(response, status, response.MessageDetail, methodName);
        });

    }
    vm.ExportPeriodTreelistToExcel = function () {
        var getURL = "../Content/DownloadExcel";
        var promiseExport = $http.post("../AccountStatementReport/ExportTreeToExcel", vm.accPeriodData);
        promiseExport.success(function (response, status, headers, config) {
            window.location = getURL + "?fName=" + response.fName;
        });
        promiseExport.error(function (response, status, headers, config) {
            ERROR.displayException(response, status, response.MessageDetail);
        });
    }
}]);
function getFinStsAccDetails(id, accountLevel) {
    var scope = angular.element($("#accountStatementControllerId")).scope();
    scope.$apply(function () {
        scope.accountStatement.getFinancialStatusAccountDetails(id, accountLevel);

    });
}
//app.controller('DashboardCtrl', ['$scope', '$timeout',
//    function($scope, $timeout) {
//        $scope.gridsterOptions = {
//            margins: [20, 20],
//            columns: 4,
//            draggable: {
//                handle: 'h3'
//            }
//        };

//        $scope.dashboards = {
//            '1': {
//                id: '1',
//                name: 'Home',
//                widgets: [{
//                    col: 0,
//                    row: 0,
//                    sizeY: 1,
//                    sizeX: 1,
//                    name: "Widget 1",
//                    partial: "/AccountStatementReport/GetAcctStmtBarGraph1Partial"
//                }, {
//                    col: 1,
//                    row: 0,
//                    sizeY: 1,
//                    sizeX: 1,
//                    name: "Widget 2",
//                    partial: "/AccountStatementReport/GetAcctStmtLineGraph1Partial"
//                }, {
//                    col: 2,
//                    row: 0,
//                    sizeY: 1,
//                    sizeX: 1,
//                    name: "Widget 3",
//                    partial: "graph3"
//                }, {
//                    col: 3,
//                    row: 0,
//                    sizeY: 1,
//                    sizeX: 1,
//                    name: "Widget 4",
//                    partial: "graph4"
//                }]
//            },
//            '2': {
//                id: '2',
//                name: 'Other',
//                widgets: [{
//                    col: 1,
//                    row: 1,
//                    sizeY: 1,
//                    sizeX: 2,
//                    name: "Other Widget 1"
//                }, {
//                    col: 1,
//                    row: 3,
//                    sizeY: 1,
//                    sizeX: 1,
//                    name: "Other Widget 2"
//                }]
//            }
//        };

//        $scope.clear = function() {
//            $scope.dashboard.widgets = [];
//        };

//        $scope.addWidget = function() {
//            $scope.dashboard.widgets.push({
//                name: "New Widget",
//                sizeX: 1,
//                sizeY: 1
//            });
//        };

//        $scope.$watch('selectedDashboardId', function(newVal, oldVal) {
//            if (newVal !== oldVal) {
//                $scope.dashboard = $scope.dashboards[newVal];
//            } else {
//                $scope.dashboard = $scope.dashboards[1];
//            }

//            console.log("dash : ", $scope.dashboard,"\ndashboards : ", $scope.dashboards);
//        });

//        // init dashboard
//        $scope.selectedDashboardId = '1';

//    }
//]);

//app.directive("acctStmtPartial", function ($http, $compile) {
//    return {
//        restrict: 'A',
//        // scope : {
//        //     url: '=',
//        // },
//        link: function ($scope, element, attrs) {
//            $http.get(attrs.url) // immediately call to retrieve partial
//              .success(function (data) {
//                    // console.log('data : ', data);
//                    element.html(data);  // replace insides of this element with response
//                    $compile(element.contents())($scope);
//              });
//        }
//    };
//});

//app.controller('CustomWidgetCtrl', ['$scope', '$modal', function($scope, $modal) {

//        $scope.remove = function(widget) {
//            $scope.dashboard.widgets.splice($scope.dashboard.widgets.indexOf(widget), 1);
//        };

//        $scope.openSettings = function(widget) {
//            // $modal.open({
//            //     scope: $scope,
//            //     templateUrl: 'demo/dashboard/widget_settings.html',
//            //     controller: 'WidgetSettingsCtrl',
//            //     resolve: {
//            //         widget: function() {
//            //             return widget;
//            //         }
//            //     }
//            // });
//        };





//    }
//]);

//app.controller('WidgetSettingsCtrl', ['$scope', '$timeout', '$rootScope', '$modalInstance', 'widget',
//    function($scope, $timeout, $rootScope, $modalInstance, widget) {
//        $scope.widget = widget;

//        $scope.form = {
//            name: widget.name,
//            sizeX: widget.sizeX,
//            sizeY: widget.sizeY,
//            col: widget.col,
//            row: widget.row
//        };

//        $scope.sizeOptions = [{
//            id: '1',
//            name: '1'
//        }, {
//            id: '2',
//            name: '2'
//        }, {
//            id: '3',
//            name: '3'
//        }, {
//            id: '4',
//            name: '4'
//        }];

//        $scope.dismiss = function() {
//            $modalInstance.dismiss();
//        };

//        $scope.remove = function() {
//            $scope.dashboard.widgets.splice($scope.dashboard.widgets.indexOf(widget), 1);
//            $modalInstance.close();
//        };

//        $scope.submit = function() {
//            angular.extend(widget, $scope.form);

//            $modalInstance.close(widget);
//        };

//    }
//]);

//// helper code
//app.filter('object2Array', function() {
//    return function(input) {
//        var out = [];
//        for (i in input) {
//            out.push(input[i]);
//        }
//        return out;
//    }
//});