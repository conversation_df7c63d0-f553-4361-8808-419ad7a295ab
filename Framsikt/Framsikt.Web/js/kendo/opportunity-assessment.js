var editor;
var assID;
var areaID;
var gridID;
var dsDropdownParticipants;
var getParticipants = [];
var opportunityParticipantDropdown; 
var currentBudgetYear = '';
var ASSESSMENT = {};
var serviceArray = [];
var delegateArray = [];
var assessmentArray = [];
var h = [];
var assessmentAreaPOPDesc = '';
var assessmentAreaPOPCons = '';
var OPPORTUNITYASSESSMENTEVENTS = {};
var OPPORTUNITYAREADATASOURCE = {};
var OPPORTUNITYAREAGRID = {};
var asId = '';
var arId = '';
var grId = '';
var acId = '';

ASSESSMENT = {
    
    getOPAssessmentBudgetYear: function () {

        var loaderID = ".opportunity-assessment-bg";
        $.ajax({
            contentType: 'application/json; charset=utf-8',
            url: "../user/GetAllBudgetYears?paramName=OPPASSMNT_BUDGET_YEAR",
            type: "POST",
           // async: false,
            dataType: 'JSON',
            success: function (result) {
                if (localStorage.getItem("appAssesmentBudgetYearVal")) {
                    currentBudgetYear = localStorage.getItem("appAssesmentBudgetYearVal");
                }
                else {
                    currentBudgetYear = result.defaultYear;
                    localStorage.setItem("appAssesmentBudgetYearVal", currentBudgetYear);
                }
                var t;
                $("#oppAssessmentYearSelector").kendoNumericTextBox({
                    format: "#",
                    decimals: 0,
                    min: result.minYear,
                    max: result.maxYear,
                    value: currentBudgetYear,
                    change: function (e) {
                        var value = this.value();
                        if (t !== value && value !== null) {
                            localStorage.setItem("appAssesmentBudgetYearVal", value);
                            currentBudgetYear = value;
                            var assessmentType = OPPORTUNITYASSESSMENTEVENTS.getParameterByName('Type');
                            var assessmentServiceAreaId = localStorage.getItem("ServiceAreaId");
                            ASSESSMENT.assessmentDropdown(assessmentType, assessmentServiceAreaId);
                        }
                  
                    },
                    spin: function (e) {
                        var value = this.value();
                        if (value !== null) {
                            localStorage.setItem("appAssesmentBudgetYearVal", value);  
                            currentBudgetYear = value;
                            var assessmentType = OPPORTUNITYASSESSMENTEVENTS.getParameterByName('Type');
                            var assessmentServiceAreaId = localStorage.getItem("ServiceAreaId");
                            ASSESSMENT.assessmentDropdown(assessmentType, assessmentServiceAreaId);
                            t = value;
                        }
                    }
                });
                var assessmentType = OPPORTUNITYASSESSMENTEVENTS.getParameterByName('Type');
                var assessmentServiceAreaId = localStorage.getItem("ServiceAreaId");
                ASSESSMENT.assessmentDropdown(assessmentType, assessmentServiceAreaId);
            },
            error: function (jqXHR, textStatus, errorThrown) {
                EVENTS.loaderOFF(loaderID);         
                var methodName = 'getOPAssessmentBudgetYear';
                ERROR.displayException(jqXHR, textStatus, errorThrown, methodName);
            }
        });
    },
    getDelegates: function (assID) {
        $.ajax({
            contentType: 'application/json; charset=utf-8',
            url: "../OpportunityAssessments/GetDelegatesByAssessmentID?assessmentID=" + assID,
            type: "POST",
            dataType: 'JSON',
            success: function (result) {
                for (var t = 0; t < result.length; t++) {
                    var delegatesHTML = '<div class="col-md-12 bottom20 user-single-participant">' +
                               '<div class="col-md-2 padding0">' +
                                   '<a href="#"><img class="logo border-radius30" src="../images/profile-pic_grey.png"></a>' +
                               '</div>' +
                               '<div class="col-md-10 user-single-desc-participant font14">' +
                                   '<div class="col-md-12 top10">' + result[t].name + '</div>' +
                                   '<div class="col-md-12"> </div>' +
                               '</div>' +
                           '</div>';
                    $("#delegates").append(DOMPurify.sanitize(delegatesHTML, domPurifyConfig));
                    delegatesHTML = '';
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                var methodName = 'getDelegates';
                ERROR.displayException(jqXHR, textStatus, errorThrown, methodName);
            }

        });

    },
    getParticipants: function (assID) {
        EVENTS.loaderON("#participants");
        $.ajax({
            contentType: 'application/json; charset=utf-8',
            url: "../OpportunityAssessments/GetParticipantsByAssessmentID?assessmentID=" + assID,
            type: "POST",
            dataType: 'JSON',
            success: function (result) {
                for (var t = 0; t < result.length; t++) {
                    var participantsHTML = '<div class="col-md-12 bottom20 user-single-participant">' +
                               '<div class="col-md-2 padding0">' +
                                   '<a href="#"><img class="logo border-radius30" src="../images/profile-pic_grey.png"></a>' +
                               '</div>' +
                               '<div class="col-md-10 user-single-desc-participant font14">' +
                                   '<div class="col-md-12 top10">' + result[t].name + '</div>' +
                                   '<div class="col-md-12"> </div>' +
                               '</div>' +
                           '</div>';
                    $("#participants").append(DOMPurify.sanitize(participantsHTML, domPurifyConfig));
                    participantsHTML = '';
                }
                EVENTS.loaderOFF("#participants");
            },
            error: function (jqXHR, textStatus, errorThrown) {
                var methodName = 'getParticipants';
                ERROR.displayException(jqXHR, textStatus, errorThrown, methodName);
                EVENTS.loaderOFF("#participants");
            }

        });

    },
    getParticipantDropdown: function (assID) {
        EVENTS.loaderON("#opportunityParticipantsDropdown");
        $.ajax({
            contentType: 'application/json; charset=utf-8',
            url: "../OpportunityAssessments/GetEligibleParticipants?assessmentID=" + assID,
            type: "POST",
            dataType: 'JSON',
            success: function (result) {
                if (opportunityParticipantDropdown) {
                    $(".k-multiselect").remove();
                    $("#opportunityParticipantsDropdonWrapper").append(DOMPurify.sanitize('<select id="opportunityParticipantsDropdown" multiple="multiple" data-placeholder="Select"></select>', domPurifyConfig));
                }
                dsDropdownParticipants = new kendo.data.DataSource({
                    data: result
                });
                opportunityParticipantDropdown = $("#opportunityParticipantsDropdown").kendoMultiSelect({
                    dataTextField: "name",
                    dataValueField: "id",
                    dataSource: dsDropdownParticipants,
                    maxSelectedItems: 10,
                    change: function (e) {
                        getParticipants = [];
                        getParticipants = this.value();
                    }
                    /* value: selectedCitiesFromLocalDropDown*/
                }).data("kendoMultiSelect");
                EVENTS.loaderOFF("#opportunityParticipantsDropdown");

            },
            error: function (jqXHR, textStatus, errorThrown) {
                var methodName = 'getParticipantDropdown';
                ERROR.displayException(jqXHR, textStatus, errorThrown, methodName);
                EVENTS.loaderOFF("#opportunityParticipantsDropdown");

            }

        });
    },
    submitParticipantDropdown: function () {
        EVENTS.loaderON("#participants");
        var assmentValue = $("#assessmentDropdown").data("kendoDropDownList").value();

        var participantData = { partipants: getParticipants, assessmentID: assmentValue }
        var finalParticipantData = JSON.stringify(participantData);

        $.ajax({
            contentType: 'application/json; charset=utf-8',
            url: "../OpportunityAssessments/InsertUpdateParticipants",
            type: "POST",
            dataType: 'JSON',
            data: finalParticipantData,
            success: function (result) {
                ASSESSMENT.getParticipants(assmentValue);
            },
            error: function (jqXHR, textStatus, errorThrown) {
                var methodName = 'submitParticipantDropdown';
                ERROR.displayException(jqXHR, textStatus, errorThrown, methodName);
                EVENTS.loaderOFF("#participants");
            }

        });
    },
    createNew: function (task) {
        $('#assessmentName').val('');
        $('#backgroundObjective').val('');
        $('#assessmentDatepicker').val('');
        $('.costReduction').prop('checked', false);
        $("input:radio[name=oppAssOptions]").prop("checked", false);
        $('#serviceAreas').data("kendoMultiSelect").value([]);
        $('#delegates').data("kendoMultiSelect").value([]);
        $('#assessmentAreas').data("kendoMultiSelect").value([]);
        if (task == "edit") {
            var title = "Edit assessment";
        }
        else {
            var title = "Add new assessment";
        }
        $('#newAssessmentWindow').css("visibility", "visible");//adding to hide popup
        var assessmentDlg = $('#newAssessmentWindow');
        if (!assessmentDlg.data("kendoWindow")) {
            assessmentDlg.kendoWindow({
                modal: true,
                width: '55%',
                visible: false,
                close: function (e) {
                }
            }).data("kendoWindow").center().open();
        }
        else {
            assessmentDlg.data("kendoWindow").center().open();
        }
        assessmentDlg.data("kendoWindow").title(title);
        title = '';
        if (task == "edit") {
            var assmentValue = $("#assessmentInput").data("kendoDropDownList").value();
            ASSESSMENT.assessmentEdit(assmentValue, task);
        }
    },
    assessmentEdit: function (val, task) {

        if (task == "edit") {
            assID = val;
            $('#assessmentName').val('');
            $('#backgroundObjective').val('');
            $('#assessmentDatepicker').val('');
            $('.costReduction').prop('checked', false);
            $("input:radio[name=oppAssOptions]").prop("checked", false);
            $('#serviceAreas').data("kendoMultiSelect").value([]);
            $('#delegates').data("kendoMultiSelect").value([]);
            $('#assessmentAreas').data("kendoMultiSelect").value([]);
        }
        var finalAssessmentObj = {
            task: "edit",
            assessmentID: val,
            assessmentName: " ",
            isCostReductionAnalysis: " ",
            backgroundObjective: " ",
            deadline: " ",
            status: " ",
            serviceAreas: [],
            delegates: [],
            assessmentAreas: []
        }
        var finalAssessmentJSON = JSON.stringify(finalAssessmentObj);
        $.ajax({
            contentType: 'application/json; charset=utf-8',
            url: "../json/jsonCreateUpdateAssessments",
            data: finalAssessmentJSON, // the "data" field contains paging, sorting, filtering and grouping data
            type: "POST",
            dataType: 'JSON',
            success: function (result) {
                if (task == "edit") {
                    var serviceData = result.serviceAreas;
                    var serviceArr = [];
                    var serviceObject = {};
                    serviceArray = [];
                    for (var prop in serviceData) {
                        serviceArr.push(serviceData[prop].ID);
                        serviceObject = { "ID": serviceData[prop].ID }
                        serviceArray.push(serviceObject);
                    }
                    $("#serviceAreas").data('kendoMultiSelect').value(serviceArr);
                    var delegateData = result.delegates;
                    var delegateArr = [];
                    var delegateObject = {};
                    delegateArray = [];
                    for (var prop in delegateData) {
                        delegateArr.push(delegateData[prop].ID);
                        delegateObject = { "ID": delegateData[prop].ID }
                        delegateArray.push(delegateObject);
                    }
                    $("#deegates").data('kendoMultiSelect').value(delegateArr);

                    var areaData = result.assessmentAreas;
                    var areaArr = [];
                    var assessmentObject = {};
                    assessmentArray = [];
                    for (var prop in areaData) {
                        areaArr.push(areaData[prop].ID);
                        assessmentObject = { "ID": areaData[prop].ID }
                        assessmentArray.push(assessmentObject);
                    }
                    $("#assessmentAreas").data('kendoMultiSelect').value(areaArr);

                    $('#assessmentName').val(result.assessmentName);
                    $('#backgroundObjective').val(result.backgroundObjective);
                    $('#assessmentDatepicker').val(result.deadline);

                    if (result.isCostReductionAnalysis == true) {
                        if (!$('.costReduction').prop('checked')) {
                            $('.costReduction').prop('checked', true);
                        }
                    }

                    if (result.status == "active") {
                        $("#oppAssRadio1").prop("checked", true);
                    }
                    else if (result.status == "completed") {
                        $("#oppAssRadio2").prop("checked", true);
                    }
                    else if (result.status == "inactive") {
                        $("#oppAssRadio3").prop("checked", true);
                    }
                }
                else {

                    $("#oppAssBackObjective").html(DOMPurify.sanitize(result.backgroundObjective, domPurifyConfig));
                    $("#oppAssDeadlineDate").html(DOMPurify.sanitize(result.deadline, domPurifyConfig));
                    $("#oppAssStatus").html(DOMPurify.sanitize(result.status, domPurifyConfig));

                    var delegateData = result.delegates;
                    var h = 0;
                    var delegateUserHTML = '';
                    for (var prop in delegateData) {

                        if (h % 2 != 0) {
                            var addClass = "col-md-offset-1";
                        }
                        else {
                            var addClass = " ";
                        }
                        delegateUserHTML = delegateUserHTML + '<div class="col-md-5 bottom20 user-single ' + addClass + ' ">' +
                                                  '<div class="col-md-2 padding0">' +
                                                    '<a href="#"><img class="logo border-radius30" src="../images/profile-pic.png"></a>' +
                                                  '</div>' +
                                                  '<div class="col-md-10 user-single-desc">' +
                                                      '<div class="col-md-12">' + delegateData[prop].name + '</div>' +
                                                      '<div class="col-md-12">Financial Planner</div>' +
                                                  '</div>' +
                                              '</div>'
                        h++;
                    }
                    $("#delegateUsers").html(DOMPurify.sanitize(delegateUserHTML, domPurifyConfig));



                }
            },
            error: function (result) {
            }

        });
    },
    assessmentSave: function () {       
        
        var taskName = assID ? "update": "save";

        if ($('input:radio[name=oppAssOptions]:checked').val() == "option1")
            var option = "active";
        else if ($('input:radio[name=oppAssOptions]:checked').val() == "option2")
            var option = "completed";
        else if ($('input:radio[name=oppAssOptions]:checked').val() == "option3")
            var option = "inactive";
        var finalAssessmentObj = {
            task: taskName,
            assessmentID: assID,
            assessmentName: $('#assessmentName').val(),
            isCostReductionAnalysis: $('.costReduction').is(":checked"),
            backgroundObjective: $('#backgroundObjective').val(),
            deadline: $('#assessmentDatepicker').val(),
            status: option,
            serviceAreas: serviceArray,
            delegates: delegateArray,
            assessmentAreas: assessmentArray
        }
        var finalAssessmentJSON = JSON.stringify(finalAssessmentObj);
        $.ajax({
            contentType: 'application/json; charset=utf-8',
            url: "../json/jsonCreateUpdateAssessments",
            data: finalAssessmentJSON, // the "data" field contains paging, sorting, filtering and grouping data
            type: "POST",
            dataType: 'JSON',
            success: function (result) {
                assID = '';
                ASSESSMENT.assessmentCancel();
                $('#assessmentName').val('');
                $('#backgroundObjective').val('');
                $('#assessmentDatepicker').val('');
                $('.costReduction').prop('checked', false);
                $("input:radio[name=oppAssOptions]").prop("checked", false);
                $('#serviceAreas').data("kendoMultiSelect").value([]);
                $('#delegates').data("kendoMultiSelect").value([]);
                $('#assessmentAreas').data("kendoMultiSelect").value([]);
            },
            error: function (result) {
            }

        });
    },
    assessmentCancel: function () {
        $("#newAssessmentWindow").data("kendoWindow").close()
    },
    onServiceSelect: function () {
        var selectedService = this.value();
        var serviceObject = {};
        serviceArray = [];
        for (var u = 0; u < selectedService.length; u++) {
            serviceObject = { "ID": selectedService[u] }
            serviceArray.push(serviceObject);
        }
    },
    onDelegateSelect: function () {
        var selectedDelegate = this.value();
        var delegateObject = {};
        delegateArray = [];
        for (var u = 0; u < selectedDelegate.length; u++) {
            delegateObject = { "ID": selectedDelegate[u] }
            delegateArray.push(delegateObject);
        }
    },
    onAssessmentSelect: function () {
        var selectedAssessment = this.value();
        var assessmentObject = {};
        assessmentArray = [];
        for (var u = 0; u < selectedAssessment.length; u++) {
            assessmentObject = { "ID": selectedAssessment[u] }
            assessmentArray.push(assessmentObject);
        }
    },
    multiSelect: function (services, delegates, assessmentAreas) {


         $("#serviceAreas").kendoMultiSelect({
            placeholder: "Select servicearea...",
            dataTextField: "name",
            dataValueField: "ID",
            change: ASSESSMENT.onServiceSelect,
            dataSource: {
                data: services
            }
        }).data("kendoMultiSelect");
         $("#deegates").kendoMultiSelect({
            placeholder: "Select delegates...",
            dataTextField: "name",
            dataValueField: "ID",
            filter: "contains",
            change: ASSESSMENT.onDelegateSelect,
            dataSource: {
                data: delegates
            }
        }).data("kendoMultiSelect");
        $("#assessmentAreas").kendoMultiSelect({
            placeholder: "Select assessmentareas...",
            dataTextField: "name",
            dataValueField: "ID",
            filter: "contains",
            change: ASSESSMENT.onAssessmentSelect,
            dataSource: {
                data: assessmentAreas
            }
        }).data("kendoMultiSelect");


    },
    addPeopleMultiselect: function (services) {
        $("#oppAssAddPeople").kendoMultiSelect({
            placeholder: "Add People...",
            dataTextField: "name",
            dataValueField: "ID",
            // change: ASSESSMENT.onServiceSelect,
            dataSource: {
                data: services
            }
        }).data("kendoMultiSelect");
    },   
    assessmentDropdownChange: function () {
        var assmentValue = $("#assessmentDropdown").data("kendoDropDownList").value();
        ASSESSMENT.assessmentAreasDisplay(assmentValue);
        ASSESSMENT.getDelegates(assmentValue);
        ASSESSMENT.getParticipants(assmentValue);
        ASSESSMENT.getParticipantDropdown(assmentValue);
    },
    assessmentDropdown: function (type, serviceAreaId) {
        if (!serviceAreaId) {
            serviceAreaId = "All";
        }
        var assessmentDatasource = new kendo.data.DataSource({
            transport: {
                read: {
                    url: "../OpportunityAssessments/GetOpportunityAssessments?serviceArea=" + serviceAreaId + "&Type=" + type + '&BudgetYear=' + currentBudgetYear,
                    dataType: "json",
                }
            }
        });
        $("#assessmentDropdown").kendoDropDownList({
            dataTextField: "name",
            dataValueField: "id",
            dataSource: assessmentDatasource,
            change: ASSESSMENT.assessmentDropdownChange,
            dataBound: function () {
                this.select(0);
                var assmentValue = this.value();
                if (assmentValue) {
                    ASSESSMENT.assessmentAreasDisplay(assmentValue);
                    ASSESSMENT.getDelegates(assmentValue);
                    ASSESSMENT.getParticipants(assmentValue);
                    ASSESSMENT.getParticipantDropdown(assmentValue);
                }
            }
        });
    },
    assessmentEditor: function (len) {
        CKEDITOR.disableAutoInline = true;
        h = [];
       
        for (var f = 1; f <= len; f++) {
            h[f] = CKEDITOR.inline(document.getElementById('editor' + f + ''));
            h[f].on('focus', function (e) {
                $('#b' + this.name + '').show();
                
            });
            h[f].on('blur', function (e) {
                $('#b' + this.name + '').hide();
            });
            $('#beditor' + f + '').hide();
           
        }
    },
    
    getEditorData: function (i, assessID, areID) {
        EVENTS.loaderON("#actionEditor" + i);
        var data = h[i].getData();
        var finalAssessmentAreaDescObj = {
            assessmentID: assessID,
            areaID: areID,
            areaDesc: data
        }
        var finalAssessmentAreaDescJSON = JSON.stringify(finalAssessmentAreaDescObj);
        $.ajax({
            contentType: 'application/json; charset=utf-8',
            url: "../OpportunityAssessments/UpdateAssessmentAreaDescription",
            data: finalAssessmentAreaDescJSON, // the "data" field contains paging, sorting, filtering and grouping data
            type: "POST",
            dataType: 'JSON',
            success: function (result) {
                EVENTS.loaderOFF("#actionEditor" + i);
                MODULES.saveConfirmation();
            },
            error: function (jqXHR, textStatus, errorThrown) {
                var methodName = 'getEditorData';
                ERROR.displayException(jqXHR, textStatus, errorThrown, methodName);
                EVENTS.loaderOFF("#actionEditor" + i);
            }
        });
    },
   
 
    assessmentAreasDisplay: function (val) {
        EVENTS.loaderON("#opportunityAssessmentArea");
        $.ajax({
            contentType: 'application/json; charset=utf-8',
            url: "../OpportunityAssessments/GetAssessmentAreasByAssessmentID?assessmentID=" + val + '&BudgetYear=' + currentBudgetYear,
            type: "POST",
            dataType: 'JSON',
            success: function (result) {
                $("#oppAssessmentBackObjective").html(' ');
                $("#assessmentTotalValue").html(' ');
                $("#oppAssDeadlineDate").html(' ');
                var oldDeadDateFormat = new Date(result.assessmentData[0].deadline);
                var newDeadDate = kendo.toString(oldDeadDateFormat, "d");
                $("#oppAssDeadlineDate").html(DOMPurify.sanitize(" "+newDeadDate, domPurifyConfig));
                $("#oppAssessmentBackObjective").html(DOMPurify.sanitize(result.assessmentData[0].assessmentDesc, domPurifyConfig));
                var numTotal = parseFloat(result.totalData[0].total);
                var total = kendo.toString(numTotal, result.amountFormat);
                $("#assessmentTotalValue").html(DOMPurify.sanitize(total, domPurifyConfig));


                var assessmentAreaContainerHTML = '';
                var collapseClass = '';
                var colexp = '';
                $(".assessmentAreaContainer").html(' ');
                for (name in CKEDITOR.instances) {
                    CKEDITOR.instances[name].destroy()
                }
                ASSESSMENT.editor();
               
                for (var g = 0; g < result.areaData.length; g++) {
                    if (g == 0) {
                        collapseClass = "in";
                        colexp = " ";
                    }
                    else {
                        collapseClass = " ";
                        colexp = "collapsed";
                    }
                    //panel panel-default
                    var numTotalData = parseFloat(result.areaData[g].totalAmount);
                    var totalAmount = kendo.toString(numTotalData, result.amountFormat);
                    var numTotalActionsData = parseFloat(result.areaData[g].noOfActions);
                    var noOfActions = kendo.toString(numTotalActionsData, result.amountFormat);
                    var assessmentAreaContainerHTML = '<div class="no-border">' +
                        '<div class="panel-heading padding5">' +
                            '<div class="table-wrapper">' +
                                '<div class="table-row">' +
                                    '<div class="table-column1">' +
                                        '<a class="OPaccordion-toggle ' + colexp + '" data-toggle="collapse" href="#OAcollapse' + (g + 1) + '">' + result.areaData[g].name +

                                        '</a>' +
                                    '</div>' +
                                    '<div class="table-column2">' + totalAmount + '</div>' +
                                    '<div class="table-column3">' + noOfActions + '</div>' +
                                '</div>' +
                                    '</div>' +
                       '<div class="row" id="actionEditorSection' + (g + 1) + '" style="width:600px;">' +
                            '<div class="row" id="actionEditor' + (g + 1) + '">' +
                                '<textarea  id="editor' + (g + 1) + '">' +
                                    result.areaData[g].description +
                                '</textarea>' +
                            '</div>' +
                            '<div class="row">' +
                               '<button class="btn btn-primary pull-right top5" id="beditor' + (g + 1) + '"  onClick="ASSESSMENT.getEditorData(' + (g + 1) + ',' + result.assessmentData[0].assessmentId + ',' + result.areaData[g].id + ');">Save</button>' +
                            '</div>' +
                        '</div>'+
                    '</div>' +
                    '<div id="OAcollapse' + (g + 1) + '" class="panel-collapse collapse ' + collapseClass + ' bottom20">' +
                            '<div class="row bottom5">' +
                                '<div class="col-md-12"><div id="assessmentAreaGrid' + (g + 1) + '"></div></div>' +
                            '</div>' +
                    '</div>' +
               '</div>';
                    $(".assessmentAreaContainer").append(DOMPurify.sanitize(assessmentAreaContainerHTML, domPurifyConfig));
                    OPPORTUNITYAREADATASOURCE.assessmentAreaDS("assessmentAreaGrid" + (g + 1), result.assessmentData[0].assessmentId, result.areaData[g].id);
                    assessmentAreaContainerHTML = '';
                }

                ASSESSMENT.assessmentEditor(result.areaData.length);
                $('body').trigger("click");
                EVENTS.loaderOFF("#opportunityAssessmentArea");
            },
            error: function (jqXHR, textStatus, errorThrown) {
                EVENTS.loaderOFF("#opportunityAssessmentArea");
                var methodName = 'assessmentAreasDisplay';
                ERROR.displayException(jqXHR, textStatus, errorThrown, methodName);
            }
        });
    },
    saveConfirmation: function () {
        var popupNotification = $("#popupNotification").kendoNotification({
            position: {
                pinned: true,
                top: 0,
                right: 0
            },
            //show: onShow,
            autoHideAfter: 3000,
            stacking: "down",
            templates: [{
                type: "upload-success",
                template: $("#dataSavedTemplate").html()
            }]

        }).data("kendoNotification");

        popupNotification.show({
            message: "Saved"
        }, "upload-success");

    },
    lengthConfirmation: function () {
        var popupLengthNotification = $("#popupNotification").kendoNotification({
            position: {
                pinned: true,
                top: 0,
                right: 0
            },
            //show: onShow,
            autoHideAfter: 3000,
            //hideOnClick: true,
            stacking: "down",
            templates: [{
                type: "error",
                template: $("#lengthConfirmationTemplate").html()
            }]

        }).data("kendoNotification");

        popupLengthNotification.show({
            message: localStorage.getItem("EditorTextLimit")
        }, "error");
    },
    editor: function () {
        assessmentAreaPOPDesc = CKEDITOR.inline(document.getElementById('editorAssAreaPOP1'));
        assessmentAreaPOPCons = CKEDITOR.inline(document.getElementById('editorAssAreaPOP2'));

        assessmentAreaPOPDesc.on('contentDom', function () {
            assessmentAreaPOPDesc.document.on('keyup', function (event) {
                var expData = assessmentAreaPOPDesc.getData();
                if (expData.length > '4000') {
                    ASSESSMENT.lengthConfirmation();
                }
            });
        });

        assessmentAreaPOPCons.on('contentDom', function () {
            assessmentAreaPOPCons.document.on('keyup', function (event) {
                var expData = assessmentAreaPOPCons.getData();
                if (expData.length > '4000') {
                    ASSESSMENT.lengthConfirmation();
                }
            });
        });

        var editorsID = [];
        editorsID = ['assessmentAreaTitle', 'popupDsc', 'popupCons'];

        for (var i = 0; i < editorsID.length; i++) {
            var elementID = '';
            elementID = editorsID[i];
            ASSESSMENT.formatEditor(elementID);
        }

    },
    formatEditor: function (elementID) {
        var actual_window_size = $(window).width() + 15;
        if (actual_window_size > 1000) {
            $('.cke_textarea_inline').addClass('cke_width');
            if (elementID == 'assessmentAreaTitle') {
                $('#' + elementID).addClass('opp-assessment-area-title-width');
            }
            else{
                $('#' + elementID).addClass('opp-assessment-popup-width');
            }
        }
        else {
            $('.cke_textarea_inline').removeClass('cke_width');
            if (elementID == 'assessmentAreaTitle') {
                $('#' + elementID).removeClass('opp-assessment-area-title-width');
            }
            else {
                $('#' + elementID).removeClass('opp-assessment-popup-width');
            }
        }
    }
}
OPPORTUNITYASSESSMENTEVENTS = {
    getParameterByName: function (name) {
        name = name.replace(/[\[]/, "\\[").replace(/[\]]/, "\\]");
        var regex = new RegExp("[\\?&]" + name + "=([^&#]*)"),
        results = regex.exec(location.search);
        return results === null ? "" : decodeURIComponent(results[1].replace(/\+/g, " "));
    }
}
OPPORTUNITYAREADATASOURCE = {
    assessmentAreaDS: function (gridID, assID, areaID) {
        var assessmentGridURL = ''; var ds = '';
        $("#" + gridID).empty();
        $("#" + gridID).next(".add-tool-bar").remove();
        assessmentGridURL = '../OpportunityAssessments/GetAssessmentAreaActions?assessmentID=' + assID + '&areaID=' + areaID + '&BudgetYear=' + currentBudgetYear;
        EVENTS.loaderON("#" + gridID);
        $.ajax({
            contentType: 'application/json; charset=utf-8',
            url: assessmentGridURL,
            type: "POST",
            dataType: 'JSON',
            success: function (response) {

                var gridColFields = [];  
                var gridRows = [];
               for (var r = 0; r < response.actionData.length; r++) {
                    var obj = {};
                    obj[response.columnsFields[0]] = response.actionData[r].actionId;
                    obj[response.columnsFields[1]] = response.actionData[r].ActionName;

                    var oldDateFormat = new Date(response.actionData[r].earliestStartDate);
                    var newDate = kendo.toString(oldDateFormat, "d");
                    obj[response.columnsFields[2]] = newDate;

                    var numActionData = parseFloat(response.actionData[r].amount);
                    var numActionValue = kendo.toString(numActionData, response.amountFormat);


                    obj[response.columnsFields[3]] = numActionValue;
                    gridRows.push(obj);
                }
                for (var t = 0; t < response.columnsFields.length; t++) {
                    if (response.columnsFields[t] !== "ActionId") {
                        gridColFields.push({ field: response.columnsFields[t], title: response.columnsTitles[t] });
                    }
                }
                gridColFields.push({
                    command: [{
                        name: "destroy",
                        text: " ",
                        className: "opportunity-assessment-delete"
                    }],
                    title: "&nbsp;",
                    width: "100px"
                });
                gridColFields[0].attributes = { style: "text-align:left;white-space:normal;" };
                gridColFields[0].template = '<span class="assessmentAreaactions"></span>';

                OPPORTUNITYAREAGRID.assessmentAreaGrid(gridColFields, gridRows, gridID, assID, areaID);
            },
            error: function (jqXHR, textStatus, errorThrown) {
                var methodName = 'assessmentAreaDS';
                ERROR.displayException(jqXHR, textStatus, errorThrown, methodName);
                EVENTS.loaderOFF("#" + gridID);
            }

        });

    }
}



OPPORTUNITYAREAGRID = {

    assessmentAreaGrid: function (columns, ds, gridID, assID, areaID) {
        var deleteText = localStorage.getItem("Delete");
        var deleteConfirmWindow = $("<div />").kendoWindow({
            width: "450px",
            title: deleteText,
            resizable: false,
            modal: true
        });
        $("#" + gridID).kendoGrid({
            dataSource: ds,
            dataBound: function (e) {
                var grid = this;
                $("#" + gridID).find(".k-grid-toolbar").addClass("add-tool-bar margin10").insertAfter($("#" + gridID));

                $("#" + gridID).find(".k-header").css({ "text-align": "right", "border": "none" });
                $("#" + gridID).find("thead").find("th:nth-child(1)").css("text-align", "left");
                $("#" + gridID).find(".k-grid-content").css("overflow", "hidden");
                $("#" + gridID).find(".k-grid-header").css({ "border": "none","padding":"0" });
                $("#" + gridID + ".k-grid tr td").css({ "border-left": "none" });

                $("#" + gridID).css({ "border-left": "none", "border-right": "none", "border-top": "none", "text-align": "right", "border-radius": "0px" });

                $("#" + gridID).find("tr td>.assessmentAreaactions").each(function () {
                    var row = $(this).closest("tr");
                    var model = grid.dataItem(row);
                    $(this).html(DOMPurify.sanitize('<a href="javascript:void(0)"><span>' + model.ActionName + '</span></a>', domPurifyConfig));
                    $(this).find('a').attr('onclick','OPPORTUNITYAREAGRID.addAssessmentAreaAction(' + assID + ',' + areaID + ',' + gridID + ',' + model.ActionId + ')');
                });

                $("#" + gridID).find(".opportunity-assessment-delete").css({ "background": "url('../images/close_small.png')", "background-repeat": "no-repeat" });
                $("#" + gridID).find(".k-icon").removeClass('k-icon k-delete');
                $("#" + gridID).find("tbody .k-button").css({ "min-width": "18px", "height": "18px", "padding": "11px 0px 5px 1px" });
                $("#" + gridID).find(".k-button").removeAttr("href");



                $("tr .opportunity-assessment-delete", "#" + gridID).click(function (e) {
                   // e.preventDefault();
                    var item = $("#" + gridID).data("kendoGrid").dataItem($(this).closest("tr"));
                    deleteConfirmWindow.data("kendoWindow")
                     .content($("#deleteConfirmation").html())
                     .center().open();


                    $(".delete-confirm-yes").click(function () {
                        EVENTS.loaderON("#" + gridID);
                        OPPORTUNITYAREAGRID.assessmentDelete(assID, areaID, item.ActionId, gridID);
                        deleteConfirmWindow.data("kendoWindow").close();
                       // $("#" + gridID).data("kendoGrid").removeRow($(this).closest("tr"));
                    });

                    $(".delete-confirm-cancel").click(function () {
                        deleteConfirmWindow.data("kendoWindow").close();
                        return false;
                    });
                 });
             },
            toolbar: [
                {
                    template: '#= OPPORTUNITYAREAGRID.assessmentAreaGridToolbar(' + assID + ',' + areaID + ',"' + gridID + '")#'
                }
            ],
            columns: columns
        }).data("kendoGrid");
        EVENTS.loaderOFF("#" + gridID);

    },
    assessmentAreaGridToolbar: function (assID, areaID, gridID) {
        var addText = localStorage.getItem("AddNewLine");
        return '<a onclick="OPPORTUNITYAREAGRID.addAssessmentAreaAction(' + assID + ',' + areaID + ',' + gridID + ')" style="background-image: url(../images/add_small.png); background-repeat: no-repeat;padding-left:22px;">' + addText + '</a>';
    },
    assessmentDelete: function (asId, arId, acId,grId) {

        var finalAssessmentAreaObj = {
            task: "delete",
            assessmentID: asId,
            areaID: arId,
            actionID: acId,
            actionName: " ",
            actionDescription: " ",
            actionConsequence: " ",
            earliestDate: " ",
            expense_amount_year_1: " ",
            expense_amount_year_2: " ",
            expense_amount_year_3: " ",
            expense_amount_year_4: " ",
            income_amount_year_1: " ",
            income_amount_year_2: " ",
            income_amount_year_3: " ",
            income_amount_year_4: " "
        }
        var finalAssessmentAreaJSON = JSON.stringify(finalAssessmentAreaObj);
        $.ajax({
            contentType: 'application/json; charset=utf-8',
            url: "../OpportunityAssessments/CreateUpdateDeleteAssessmentAreaAction?BudgetYear=" + currentBudgetYear,
            data: finalAssessmentAreaJSON, // the "data" field contains paging, sorting, filtering and grouping data
            type: "POST",
            dataType: 'JSON',
            success: function (result) {
                ASSESSMENT.assessmentAreasDisplay(asId);
            },
            error: function (jqXHR, textStatus, errorThrown) {
                EVENTS.loaderOFF("#" + grId);
                var methodName = 'assessmentDelete';
                ERROR.displayException(jqXHR, textStatus, errorThrown, methodName);
            }

        });

    },
    addAssessmentAreaAction: function (assID, areaID, gridID, actId) {

        $('#assessmentAreaTitle').val('');
        assessmentAreaPOPDesc.setData(' ');
        assessmentAreaPOPCons.setData(' ');
        $('#assessmentAreaDatepicker').val(' ');
        $('#assessmentAreaExpenseYear1').val(' ');
        $('#assessmentAreaExpenseYear2').val(' ');
        $('#assessmentAreaExpenseYear3').val(' ');
        $('#assessmentAreaExpenseYear4').val(' ');
        $('#assessmentAreaRevenueYear1').val(' ');
        $('#assessmentAreaRevenueYear2').val(' ');
        $('#assessmentAreaRevenueYear3').val(' ');
        $('#assessmentAreaRevenueYear4').val(' ');

        asId = assID;
        arId = areaID;
        grId = gridID.id;
        acId = actId;

        $('#addnewAssessmentAreaWindow').css("visibility", "visible");//adding to hide popup
        var assessmentAreaDlg = $('#addnewAssessmentAreaWindow');
        if (!assessmentAreaDlg.data("kendoWindow")) {
            assessmentAreaDlg.kendoWindow({
                width: "670px",
                draggable: true,
                modal: true,
                visible: false,
                resizable: true,
                close: function (e) {
                }
            }).data("kendoWindow").center().open();
        }
        else {
            assessmentAreaDlg.data("kendoWindow").center().open();
        }

        $("#assessmentAreaDatepicker").bind("click", function () {
            $(this).data("kendoDatePicker").open();
        });

        $("#cke_editorAssAreaPOP1").css("z-index", "10005");
        $("#cke_editorAssAreaPOP2").css("z-index", "10005");
        //$("#assessmentAreaRevenueYear1,#assessmentAreaRevenueYear2,#assessmentAreaRevenueYear3,#assessmentAreaRevenueYear4,#assessmentAreaExpenseYear1,#assessmentAreaExpenseYear2,#assessmentAreaExpenseYear3,#assessmentAreaExpenseYear4").kendoNumericTextBox({
        //    culture: kendo.culture().name,
        //    format: "n0"
        //});
        //if (!$("#assessmentAreaRevenueYear1").data("kendoNumericTextBox")) {
        //    $("#assessmentAreaRevenueYear1").kendoNumericTextBox({ format: 'n0', spinners: false, value: "" });
        //}
        //else {
        //    $("#assessmentAreaRevenueYear1").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: "" });
        //}
        //$("#assessmentAreaRevenueYear2").kendoNumericTextBox({ format: 'n0', spinners: false, value: "" });
        //$("#assessmentAreaRevenueYear3").kendoNumericTextBox({ format: 'n0', spinners: false, value: "" });
        //$("#assessmentAreaRevenueYear4").kendoNumericTextBox({ format: 'n0', spinners: false, value: "" });
        //$("#assessmentAreaExpenseYear1").kendoNumericTextBox({ format: 'n0', spinners: false, value: "" });
        //$("#assessmentAreaExpenseYear2").kendoNumericTextBox({ format: 'n0', spinners: false, value: "" });
        //$("#assessmentAreaExpenseYear3").kendoNumericTextBox({ format: 'n0', spinners: false, value: "" });
        //$("#assessmentAreaExpenseYear4").kendoNumericTextBox({ format: 'n0', spinners: false, value: "" });


        //$("#assessmentAreaRevenueYear1").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: '' });
        //$("#assessmentAreaRevenueYear2").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: '' });
        //$("#assessmentAreaRevenueYear3").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: '' });
        //$("#assessmentAreaRevenueYear4").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: '' });
        //$("#assessmentAreaExpenseYear1").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: '' });
        //$("#assessmentAreaExpenseYear2").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: '' });
        //$("#assessmentAreaExpenseYear3").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: '' });
        //$("#assessmentAreaExpenseYear4").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: '' });


        $(".opp-hide-select").css("width", "97");        
        $(".opp-hide-select").find('.k-formatted-value').css("width", "95"); 
        $(" .opp-hide-select .k-numerictextbox").find('.k-numeric-wrap').css("padding-right", "2px");   
        $('.opp-hide-select').find('.k-select').hide();
     
        $("#popupDsc").find(".cke_editable").attr('style', 'min-height: 100px !important');
        $("#popupCons").find(".cke_editable").attr('style', 'min-height: 100px !important');
        var d = new Date();
        var n = d.getFullYear();
        $("#assessmentAreaDatepicker").kendoDatePicker({
            culture: kendo.culture().name,
            value: new Date(n + 1, 00, 01)
        });
        
        if (acId) {
            OPPORTUNITYAREAGRID.assessmentAreaGridPopupEdit();
            var editText = localStorage.getItem("Edit");
            assessmentAreaDlg.data("kendoWindow").title(editText);
        }
        else {
            
            var addNewText = localStorage.getItem("AddNew");
            assessmentAreaDlg.data("kendoWindow").title(addNewText);

            if (!$("#assessmentAreaRevenueYear1").data("kendoNumericTextBox")) {
                $("#assessmentAreaRevenueYear1").kendoNumericTextBox({ format: 'n0', spinners: false, value: "" });
            }
            else {
                $("#assessmentAreaRevenueYear1").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: "" });
            }
            if (!$("#assessmentAreaRevenueYear2").data("kendoNumericTextBox")) {
                $("#assessmentAreaRevenueYear2").kendoNumericTextBox({ format: 'n0', spinners: false, value: "" });
            }
            else {
                $("#assessmentAreaRevenueYear2").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: "" });
            }
            if (!$("#assessmentAreaRevenueYear3").data("kendoNumericTextBox")) {
                $("#assessmentAreaRevenueYear3").kendoNumericTextBox({ format: 'n0', spinners: false, value: "" });
            }
            else {
                $("#assessmentAreaRevenueYear3").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: "" });
            }
            if (!$("#assessmentAreaRevenueYear4").data("kendoNumericTextBox")) {
                $("#assessmentAreaRevenueYear4").kendoNumericTextBox({ format: 'n0', spinners: false, value: "" });
            }
            else {
                $("#assessmentAreaRevenueYear4").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: "" });
            }
            if (!$("#assessmentAreaExpenseYear1").data("kendoNumericTextBox")) {
                $("#assessmentAreaExpenseYear1").kendoNumericTextBox({ format: 'n0', spinners: false, value: "" });
            }
            else {
                $("#assessmentAreaExpenseYear1").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: "" });
            }
            if (!$("#assessmentAreaExpenseYear2").data("kendoNumericTextBox")) {
                $("#assessmentAreaExpenseYear2").kendoNumericTextBox({ format: 'n0', spinners: false, value: "" });
            }
            else {
                $("#assessmentAreaExpenseYear2").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: "" });
            }
            if (!$("#assessmentAreaExpenseYear3").data("kendoNumericTextBox")) {
                $("#assessmentAreaExpenseYear3").kendoNumericTextBox({ format: 'n0', spinners: false, value: "" });
            }
            else {
                $("#assessmentAreaExpenseYear3").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: "" });
            }
            if (!$("#assessmentAreaExpenseYear4").data("kendoNumericTextBox")) {
                $("#assessmentAreaExpenseYear4").kendoNumericTextBox({ format: 'n0', spinners: false, value: "" });
            }
            else {
                $("#assessmentAreaExpenseYear4").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: "" });
            }

        }
       
    },
    assessmentAreaGridPopupEdit: function () {
        EVENTS.loaderON("#addnewAssessmentAreaWindow");
        $('#assessmentAreaTitle').val('');
        assessmentAreaPOPDesc.setData('');
        assessmentAreaPOPCons.setData('');
        $('#assessmentAreaDatepicker').val('');
        $('#assessmentAreaExpenseYear1').val('');
        $('#assessmentAreaExpenseYear2').val('');
        $('#assessmentAreaExpenseYear3').val('');
        $('#assessmentAreaExpenseYear4').val('');
        $('#assessmentAreaRevenueYear1').val('');
        $('#assessmentAreaRevenueYear2').val('');
        $('#assessmentAreaRevenueYear3').val('');
        $('#assessmentAreaRevenueYear4').val('');
        var finalAssessmentAreaObj = {
            task: "retrieve",
            assessmentID: asId,
            areaID: arId,
            actionID: acId,
            actionName: '',
            actionDescription: '',
            actionConsequence: '',
            earliestDate: '',
            expense_amount_year_1: '',
            expense_amount_year_2: '',
            expense_amount_year_3: '',
            expense_amount_year_4: '',
            income_amount_year_1: '',
            income_amount_year_2: '',
            income_amount_year_3: '',
            income_amount_year_4: ''
        }
        var finalAssessmentAreaJSON = JSON.stringify(finalAssessmentAreaObj);
        $.ajax({
            contentType: 'application/json; charset=utf-8',
            url: "../OpportunityAssessments/RetrieveAssessmentAreaAction?BudgetYear=" + currentBudgetYear,
            data: finalAssessmentAreaJSON, // the "data" field contains paging, sorting, filtering and grouping data
            type: "POST",
            dataType: 'JSON',
            success: function (result) {
                var newearliestDate = kendo.toString(result.earliestDate, "d");
                $('#assessmentAreaTitle').val(result.actionName);
                assessmentAreaPOPDesc.setData(result.actionDescription);
                assessmentAreaPOPCons.setData(result.actionConsequence);
                //$('#assessmentAreaExpenseYear1').val(result.expense_amount_year_1);
                //$('#assessmentAreaExpenseYear2').val(result.expense_amount_year_2);
                //$('#assessmentAreaExpenseYear3').val(result.expense_amount_year_3);
                //$('#assessmentAreaExpenseYear4').val(result.expense_amount_year_4);
                //$('#assessmentAreaRevenueYear1').val(result.income_amount_year_1);
                //$('#assessmentAreaRevenueYear2').val(result.income_amount_year_2);
                //$('#assessmentAreaRevenueYear3').val(result.income_amount_year_3);
                //$('#assessmentAreaRevenueYear4').val(result.income_amount_year_4);


                //$("#assessmentAreaRevenueYear1,#assessmentAreaRevenueYear2,#assessmentAreaRevenueYear3,#assessmentAreaRevenueYear4,#assessmentAreaExpenseYear1,#assessmentAreaExpenseYear2,#assessmentAreaExpenseYear3,#assessmentAreaExpenseYear4").kendoNumericTextBox({
                //    culture: kendo.culture().name,
                //    format: "n0"
                //});


                //$("#assessmentAreaRevenueYear1").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: result.income_amount_year_1 });
                //$("#assessmentAreaRevenueYear2").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: result.income_amount_year_2 });
                //$("#assessmentAreaRevenueYear3").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: result.income_amount_year_3 });
                //$("#assessmentAreaRevenueYear4").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: result.income_amount_year_4 });
                //$("#assessmentAreaExpenseYear1").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: result.expense_amount_year_1 });
                //$("#assessmentAreaExpenseYear2").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: result.expense_amount_year_2 });
                //$("#assessmentAreaExpenseYear3").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: result.expense_amount_year_3 });
                //$("#assessmentAreaExpenseYear4").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: result.expense_amount_year_4 });

                if (!$("#assessmentAreaRevenueYear1").data("kendoNumericTextBox")) {
                    $("#assessmentAreaRevenueYear1").kendoNumericTextBox({ format: 'n0', spinners: false, value: result.income_amount_year_1 });
                }
                else {
                    $("#assessmentAreaRevenueYear1").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: result.income_amount_year_1 });
                }
                if (!$("#assessmentAreaRevenueYear2").data("kendoNumericTextBox")) {
                    $("#assessmentAreaRevenueYear2").kendoNumericTextBox({ format: 'n0', spinners: false, value: result.income_amount_year_2 });
                }
                else {
                    $("#assessmentAreaRevenueYear2").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: result.income_amount_year_2 });
                }
                if (!$("#assessmentAreaRevenueYear3").data("kendoNumericTextBox")) {
                    $("#assessmentAreaRevenueYear3").kendoNumericTextBox({ format: 'n0', spinners: false, value: result.income_amount_year_3 });
                }
                else {
                    $("#assessmentAreaRevenueYear3").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: result.income_amount_year_3 });
                }
                if (!$("#assessmentAreaRevenueYear4").data("kendoNumericTextBox")) {
                    $("#assessmentAreaRevenueYear4").kendoNumericTextBox({ format: 'n0', spinners: false, value: result.income_amount_year_4 });
                }
                else {
                    $("#assessmentAreaRevenueYear4").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: result.income_amount_year_4 });
                }
                if (!$("#assessmentAreaExpenseYear1").data("kendoNumericTextBox")) {
                    $("#assessmentAreaExpenseYear1").kendoNumericTextBox({ format: 'n0', spinners: false, value: result.expense_amount_year_1 });
                }
                else {
                    $("#assessmentAreaExpenseYear1").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: result.expense_amount_year_1 });
                }
                if (!$("#assessmentAreaExpenseYear2").data("kendoNumericTextBox")) {
                    $("#assessmentAreaExpenseYear2").kendoNumericTextBox({ format: 'n0', spinners: false, value: result.expense_amount_year_2 });
                }
                else {
                    $("#assessmentAreaExpenseYear2").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: result.expense_amount_year_2 });
                }
                if (!$("#assessmentAreaExpenseYear3").data("kendoNumericTextBox")) {
                    $("#assessmentAreaExpenseYear3").kendoNumericTextBox({ format: 'n0', spinners: false, value: result.expense_amount_year_3 });
                }
                else {
                    $("#assessmentAreaExpenseYear3").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: result.expense_amount_year_3 });
                }
                if (!$("#assessmentAreaExpenseYear4").data("kendoNumericTextBox")) {
                    $("#assessmentAreaExpenseYear4").kendoNumericTextBox({ format: 'n0', spinners: false, value: result.expense_amount_year_4 });
                }
                else {
                    $("#assessmentAreaExpenseYear4").data("kendoNumericTextBox").setOptions({ format: "n0", spinners: false, value: result.expense_amount_year_4 });
                }


                $(".opp-hide-select").css("width", "97");
               $(".opp-hide-select").find('.k-formatted-value').css("width", "95");                
               $(" .opp-hide-select .k-numerictextbox").find('.k-numeric-wrap').css("padding-right", "2px");  
                $('.opp-hide-select').find('.k-select').hide();
                var d = new Date();
                var n = d.getFullYear();
                $("#assessmentAreaDatepicker").kendoDatePicker({
                    culture: kendo.culture().name,
                    value: newearliestDate
                });

                EVENTS.loaderOFF("#addnewAssessmentAreaWindow");
             
            },
            error: function (jqXHR, textStatus, errorThrown) {
                EVENTS.loaderOFF("#addnewAssessmentAreaWindow");
                var methodName = 'assessmentAreaGridPopupEdit';
                ERROR.displayException(jqXHR, textStatus, errorThrown, methodName);
            }

        });
    },
    assessmentAreaGridPopupSave: function () {
        if (!$('#assessmentAreaTitle').val()) {
            $("#assessmentError").html(DOMPurify.sanitize('<div class="col-md-1 padding0" style="background-image: url(../images/validation_small.png);background-repeat: no-repeat;height: 20px;width:20px;"></div><div class="col-md-6 padding0" style="background-color: #F4F4F4;"><div class="col-md-12" style=" margin-left: 4px;margin-top: 0px;padding: 2px;">Please add the title</div></div>', domPurifyConfig)).fadeIn('slow').delay(5000).hide(5);
        }
        else {
            EVENTS.loaderON("#addnewAssessmentAreaWindow");
            var task = acId ? "update" : "save";
            var desc = assessmentAreaPOPDesc.getData();
            var cons = assessmentAreaPOPCons.getData();

            if ($('#assessmentAreaDatepicker').val() == " ")
            {
                var earlistDate = null;
            }
            else {
                var earlistDate = $('#assessmentAreaDatepicker').val();
            }
            var finalAssessmentAreaObj = {
                task: task,
                assessmentID: asId,
                areaID: arId,
                actionID: acId,
                actionName: $('#assessmentAreaTitle').val(),
                actionDescription: desc,
                actionConsequence: cons,
                earliestDate: earlistDate,
                expense_amount_year_1: $('#assessmentAreaExpenseYear1').val(),
                expense_amount_year_2: $('#assessmentAreaExpenseYear2').val(),
                expense_amount_year_3: $('#assessmentAreaExpenseYear3').val(),
                expense_amount_year_4: $('#assessmentAreaExpenseYear4').val(),
                income_amount_year_1: $('#assessmentAreaRevenueYear1').val(),
                income_amount_year_2: $('#assessmentAreaRevenueYear2').val(),
                income_amount_year_3: $('#assessmentAreaRevenueYear3').val(),
                income_amount_year_4: $('#assessmentAreaRevenueYear4').val()
            }
            var finalAssessmentAreaJSON = JSON.stringify(finalAssessmentAreaObj);
            $.ajax({
                contentType: 'application/json; charset=utf-8', 
                url: "../OpportunityAssessments/CreateUpdateDeleteAssessmentAreaAction?BudgetYear=" + currentBudgetYear,
                data: finalAssessmentAreaJSON, // the "data" field contains paging, sorting, filtering and grouping data
                type: "POST",
                dataType: 'JSON',
                success: function (result) {
                    EVENTS.loaderON("#" + grId);
                    EVENTS.loaderOFF("#addnewAssessmentAreaWindow");
                   ASSESSMENT.assessmentAreasDisplay(asId);
                   // OPPORTUNITYAREADATASOURCE.assessmentAreaDS(grId, asId, arId);
                    asId = ''; arId = ''; acId = '';
                    OPPORTUNITYAREAGRID.assessmentAreaGridPopupCancel();
                    $('#assessmentAreaTitle').val('');
                    assessmentAreaPOPDesc.setData('');
                    assessmentAreaPOPCons.setData('');
                    $('#assessmentAreaDatepicker').val(' ');
                    $('#assessmentAreaExpenseYear1').val(' ');
                    $('#assessmentAreaExpenseYear2').val(' ');
                    $('#assessmentAreaExpenseYear3').val(' ');
                    $('#assessmentAreaExpenseYear4').val(' ');
                    $('#assessmentAreaRevenueYear1').val(' ');
                    $('#assessmentAreaRevenueYear2').val(' ');
                    $('#assessmentAreaRevenueYear3').val(' ');
                    $('#assessmentAreaRevenueYear4').val(' ');
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    EVENTS.loaderOFF("#addnewAssessmentAreaWindow");
                    var methodName = 'assessmentAreaGridPopupSave';
                    ERROR.displayException(jqXHR, textStatus, errorThrown, methodName);
                }

            });

        }
    },
    assessmentAreaGridPopupCancel: function () {
        asId = ''; arId = ''; acId = '';
        var assessmentAreaDlg = $("#addnewAssessmentAreaWindow").data("kendoWindow");
        assessmentAreaDlg.close();
    }
}
$(document).ready(function () {
    $(window).resize(function () {
        var editorsID = [];
        editorsID = ['assessmentAreaTitle', 'popupDsc', 'popupCons'];
        for (var i = 0; i < editorsID.length; i++) {
            var elementID = '';
            elementID = editorsID[i];
            ASSESSMENT.formatEditor(elementID);
        }
    });

    ASSESSMENT.getOPAssessmentBudgetYear();   
});