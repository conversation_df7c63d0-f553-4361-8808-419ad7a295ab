
@{
    ViewBag.Title = "ImportAccountingData";
    Layout = "~/Views/Shared/_FramsiktLayout.cshtml";
}

<section id="main-content" class="merge-left" data-ng-app="accountingImportApp" data-ng-controller="accountingImportController as vm" data-ng-init="vm.init()">
    <div class="wrapper">
        <div class="container-fluid">
            <div class="row bottom5 action-import-bg">
                @*<div class="col-md-1"></div>*@
                <div class="col-md-11 kostra-left-bg">
                    <div class="col-md-12 staff-planning-dynamic-heading">
                        <div class="col-md-12 padding0">
                            <div class="col-md-6">
                                <div class="col-md-7 padding0">
                                    <span class="staff-dynamic-heading page-title">
                                        @Resources.GetResource("AccountingImport_Header")
                                    </span>
                                </div>
                                <div class="col-md-5 padding0 top15">
                                    <div class="col-md-2 month-year-left padding0" id="importActionsLeftIcon">
                                        <img src="~/images/monthly_report_left.png" class="hand-pointer month-year-left-img" ng-click="vm.leftButtonClick()" style="padding-left:3px!important" />
                                    </div>
                                    <div class="col-md-6 padding0 monthly-year-center" id="importActionsYearSelectorWrapper" style="border-top:1px solid #6fa1b2;border-bottom:1px solid #6fa1b2;">
                                        <input id="importActionsYearSelector" ng-model="vm.budgetYear" style="width:100%; background:white; height:27px; text-align: center" readonly />
                                    </div>
                                    <div class="col-md-2 month-year-right padding0" id="importActionsRightIcon">
                                        <img src="~/images/monthly_report_right.png" class="hand-pointer month-year-right-img" ng-click="vm.rightButtonClick()" style="padding-left:4px!important" />
                                    </div>
                                </div>
                                <div class="col-md-12 padding0" style="clear:both">
                                    <span class="kostra-subheading semi">
                                        @(((Dictionary<string, string>)ViewBag.userDetails)["tenantName"])
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-7 padding0"> </div>

                        </div>

                    </div>
                </div>

                <div class="col-md-12 align-center padding0 top200" ng-hide="vm.hideErrorNotification">
                    <div class="min-height25 align-center border-white-bottom1" id="errorNotification" style="background: rgb(252, 203, 141); visibility: visible;">
                        <img src="/images/deviation_icon.png" alt="Deviation"><span style="top:3px;position:relative;" id="errorNotificationText">@Resources.GetResource("AccountingImport_ErrorNotification")</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row top20">
            <div class="col-md-12 bottom10">
                <div class="col-md-3 padding0" ng-hide="vm.hideDropDown">
                        <span class="semi">@Resources.GetResource("AccountingImport_TransferId")</span>
                        <div id="transferId" class="margin-left10" style="width:65%;">
                        </div>
                </div>
                <div class="col-md-3" style="padding-left:0.3%;">
                    <span class="font14 semi">@Resources.GetResource("AccountingImport_DeleteExistingAccoutingData")</span>
                    <span style="position:relative;top:3px;"><input type="checkbox" ng-model="vm.deleteExistingAccountingData" ng-change="vm.updateBudgetChangeOptions(vm.deleteExistingAccountingData)" ng-disabled="vm.disableCheckBox"/></span>
                </div>
                <div class="col-md-3" style="padding-left:0.3%;">
                    <span class="font14 semi">@Resources.GetResource("AccountingImport_ImportintoAccoutingData")</span>
                    <span style="position:relative;top:3px;"><input type="checkbox" ng-model="vm.importintoAccoutingData" ng-change="vm.updateAccountingDataFlag(vm.importintoAccoutingData)" ng-disabled="vm.disableCheckBox"/></span>
                </div>
                <div class="col-md-3" style="padding-left:0.3%;">
                    <span class="font14 semi">@Resources.GetResource("AccountingImport_ImportintoAccoutingDataDetail")</span>
                    <span style="position:relative;top:3px;"><input type="checkbox" ng-model="vm.importintoAccoutingDataDetail" ng-change="vm.updateAccountingDetailDataFlag(vm.importintoAccoutingDataDetail)" ng-disabled="vm.disableCheckBox"/></span>
                </div>
            </div>
            @*<div class="col-md-12">
                <div class="col-md-3 padding0 padding-left5" id="actionImportDropWrapper">
                    <select id="budgetChangeDropDown" kendo-drop-down-list
                            k-option-label="'@Resources.GetResource("ai_select_budget_change")'"
                            k-data-text-field="'BudgetChange'"
                            k-data-value-field="'ChangeId'"
                            k-options="vm.budgetChangeOptions"
                            ng-model="vm.changeId"
                            class="archive-Doc-Dropdown font13"></select>
                </div>
            </div>*@
        </div>
        <div class="row top20">
            <div class="col-md-12">
                <div class="col-md-12 border1 acc-header-shape collapsed" data-toggle="collapse" data-parent="#accordion"
                     data-target="#collapseImportAccountingStatus"
                     style="cursor:pointer;background:#ebebeb;padding: 5px 0 5px 10px;">
                    <div class="col-md-10 bottom5 padding0 acc-title">
                        <div class="title-no-collapse acc-header-left" id="importAccountingStatusHeader">
                            <span id="importAccountingStatusTitle">@Resources.GetResource("AccountingImport_Header")</span>
                            <span data-role="tooltip" id="descData0"><img src="../../images/icons_monthly_report_information.png"></span>
                        </div>
                    </div>
                    <div class="col-md-2">@*<span class="pull-right padding-top5"><img src="../../images/full_screen__maximize.png"></span>*@</div>

                </div>
            </div>
        </div>
        <div class="col-md-12  panel-collapse bottom10 collapse in" id="collapseImportAccountingStatus">
            <div class="col-md-12 acc-grid-background padding-right10 padding-left10  border1 border-top0">
                <div class="row background-white">
                    <div class="col-md-12 padding0 background-white">
                        <div class="col-md-12 padding0 min-height150 bottom10 border-bottom-solid">
                            <div class="top20 bottom10 padding0 padding-left5 col-md-4 title-no-collapse min-height150 border-right1">
                                <div class="col-md-12 padding-bottom10">
                                    <button class="pull-left btn btn-export main-export-img padding-left5"
                                            ng-click="vm.getExcelTemplate()">
                                        <span class="marginleft5 export-span">@Resources.GetResource("ai_download_template")</span>
                                    </button>
                                </div>
                                <div class="col-md-12 padding-bottom10">
                                    <button class="pull-left btn btn-export main-export-img padding-left5"
                                            ng-disabled="!(vm.workflowState=='Initial' || vm.workflowState=='Finished')" 
                                            ngf-select="upload($file)">
                                        <img src="../../images/import_blue.png" class="expot-img">
                                        <span class="marginleft5 export-span">@Resources.GetResource("ai_import_excel")</span>
                                    </button>
                                </div>
                                <div class="col-md-12 padding-bottom10">
                                    <button class="pull-left btn btn-export main-export-img padding-left5"
                                            ng-click="vm.exportToExcel()" ng-disabled="!(vm.workflowState=='Staged')">
                                        <img src="../../images/export_blue.png" class="expot-img">
                                        <span class="marginleft5 export-span">@Resources.GetResource("ai_export_excel")</span>
                                    </button>
                                </div>

                            </div>
                            <div class="top20 padding-left25 bottom10 padding0 col-md-3 min-height150 border-right1">
                                <div class="col-md-5 padding0 padding-top20 red">@Resources.GetResource("ai_fail_count"):</div>
                                <div class="col-md-7 padding0 padding-top20 red">{{vm.failedCount | number}}</div>
                                <div class="col-md-5 padding0 padding-top20 semi">@Resources.GetResource("ai_action_count"):</div>
                                <div class="col-md-7 padding0 padding-top20 semi">{{vm.actionCount | number}}</div>
                                <div class="col-md-5 padding0 padding-top10 semi">@Resources.GetResource("ai_row_count"):</div>
                                <div class="col-md-7 padding0 padding-top10 semi">{{vm.totalRows | number}}</div>
                                <div class="col-md-5 padding0 padding-top10 semi">@Resources.GetResource("ai_total_amount"):</div>
                                <div class="col-md-7 padding0 padding-top10 semi">{{vm.sum | number:1}}</div>
                            </div>
                            <div class="top20 bottom10 padding0 col-md-4 min-height150">
                                <div class="col-md-6 padding0 padding-left10">
                                    <!--<div class="col-md-6 padding0 padding-bottom10">@Resources.GetResource("ai_adjustment"):</div>
                                    <div class="col-md-6 padding0 padding-bottom10">
                                        <input id="aiAdjustment" kendo-numeric-text-box k-min="0"
                                               k-max="5" k-step="0.01" k-ng-model="vm.adjustment"
                                               k-options="vm.adjustmentOptions" style="width: 100%;" />
                                    </div>-->
                                    @*<div class="col-md-6 padding0 padding-bottom10">@Resources.GetResource("ai_value_factor"):</div>
                                    <div class="col-md-6 padding0 padding-bottom10">
                                        <select id="aiValueFactorDropdown"
                                                kendo-drop-down-list
                                                k-data-text-field="'value'"
                                                k-data-value-field="'key'"
                                                k-options="vm.valueFactorOptions"
                                                ng-model="vm.valueFactor"
                                                class="archive-Doc-Dropdown font13"></select>
                                    </div>*@
                                    <div class="col-md-12 padding0 top5">
                                        <button id="importData" class="btn pull-right btn-primary"
                                                ng-disabled="(!((vm.workflowState=='Staged' && vm.isClean && vm.failedCount == 0) || vm.workflowState=='Failed')) || vm.disableSaveButton"
                                                ng-click="vm.importFromStaging()">
                                            @Resources.GetResource("ai_import")
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-6 padding0">
                                    <div class="row">
                                        <div class="col-md-1"></div>
                                        <div class="col-md-8 padding-bottom10">
                                            <button id="saveChanges" class="btn btn-block btn-primary" ng-disabled="!(vm.workflowState=='Staged' && !vm.isClean)"
                                                    ng-click="vm.saveChanges()">
                                                @Resources.GetResource("ai_save_changes")
                                            </button>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-1"></div>
                                        <div class="col-md-8 padding-bottom10">
                                            <button id="cancelChanges" class="btn btn-block btn-primary" ng-disabled="!(vm.workflowState=='Staged' && !vm.isClean)"
                                                    ng-click="vm.cancelChanges()">
                                                @Resources.GetResource("ai_cancel_changes")
                                            </button>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-1"></div>
                                        <div class="col-md-8 padding-bottom10">
                                            <button id="deleteAll" class="btn btn-block btn-primary" ng-disabled="!(vm.workflowState=='Staged' || vm.workflowState=='Failed') || vm.disbleDeleteBtn"
                                                    ng-click="vm.deleteAll()">
                                                @Resources.GetResource("ai_delete")
                                            </button>
                                        </div>
                                    </div>
                                    <div class="row" ng-show="vm.workflowState=='Importing'">
                                        <div class="col-md-1"></div>
                                        <div class="col-md-8 padding-bottom10">
                                            <div kendo-progress-bar="progressBar" id="importProgressBar"
                                                 k-min="0" k-max="100" ng-model="vm.importProgress"
                                                 style="width: 100%;">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>

                    </div>
                </div>
                <div class="row background-white">
                    <div class="col-md-12-padding0">
                        <kendo-grid id="accountingImportDetailGrid"
                                    k-options="vm.accountingImportGridOptions"
                                    k-ng-delay="vm.accountingImportGridOptions"
                                    k-rebind ="vm.accountingImportGridOptions">
                        </kendo-grid>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <span class="cmn-display-none" id="selectHolderText">@Resources.GetResource("cmn_placeholder_select")</span>
</section>
<script id="dataErrorTemplate" type="text/x-kendo-template">
    <div class="error" id="staffErrorNotification">
        <div class="row">
            <div class="col-md-2"><img src="../images/error_cross.png"></div>
            <div class="col-md-8">
                <h3 style="margin-left: 8px;">@Resources.GetResource("cmn_error_title")</h3>
            </div>
        </div>
    </div>
</script>
<script id="aiImportSuccessTemplate" type="text/x-kendo-template">
    <div class="upload-success">
        <div class="row">
            <div class="col-md-2"><img src="../images/notification_tick.png"></div>
            <div class="col-md-8">
                <h3 style="margin-left: 8px;">#= message #</h3>
            </div>
        </div>
    </div>
</script>
<script type="text/javascript">
    localStorage.setItem("ad_import_success_msg", "@Resources.GetResource("AccountingImport_success")");
    localStorage.setItem("ad_import_tooltip_msg", "@Resources.GetResource("AccountingImport_Tooltip")");
    localStorage.setItem("selectPlaceholder", "@Resources.GetResource("cmn_placeholder_select")");
</script>
@*@Scripts.Render("~/bundles/AccountingImportIndex")*@
<script src="/bundles/AccountingImportIndex.js"></script>
@if(Resources.GetLanguagePreference() == "nb-NO")
{
    <script src="~/Scripts/lang/angular-locale_nb-no.js"></script>
}
