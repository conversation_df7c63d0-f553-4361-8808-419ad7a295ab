@{
    Layout = "~/Views/Shared/_FramsiktLayout.cshtml";
}

<section id="main-content" class="merge-left">
    <div class="wrapper">
        <div class="container-fluid">
            <div class="row bottom5 finplan-bg banner-background-style">
                <div class="col-md-6">
                    <div class=" col-md-12 consequence-analysis-dynamic-heading">
                        <div class="col-md-12">
                            <span class="consequence-dynamic-heading">
                                @Resources.GetResource("consequence_adjusted_budget_title_text")
                            </span>
                        </div>
                        <div class="col-md-12">
                            <span class="kostra-subheading semi">@(((Dictionary<string, string>)ViewBag.userDetails)["tenantName"])</span>
                        </div>
                        <div class="col-md-12 top10">
                            <div class="col-md-2 padding0 top3"><span class="white font14 semi"> @Resources.GetResource("budget_year_txt") </span></div>
                            <div class="col-md-2 padding0"><input id="consequenceOVYearSelector" style="width:100%;" /></div>
                        </div>
                        @* <div class="col-md-12">
                        @if (((Dictionary<string, string>)ViewBag.userDetails)["kostraDataType"] == "city")
                        {
                            <span class="kostra-subheading semi">@(((Dictionary<string, string>)ViewBag.userDetails)["tenantName"])-@(((Dictionary<string, string>)ViewBag.appDetails)["city_figures"])</span>
                        }
                        else
                        {
                            <span class="kostra-subheading semi">@(((Dictionary<string, string>)ViewBag.userDetails)["tenantName"])-@(((Dictionary<string, string>)ViewBag.appDetails)["consolidated_number"])</span>
                        }
                    </div>
                    <div class="col-md-12  kostra-subheading-top">
                        <span class="kostra-subheading semi">@(((Dictionary<string, string>)ViewBag.appDetails)["temporary_data"])-@(((Dictionary<string, string>)ViewBag.appDetails)["kostra_last_updated_title"])</span>
                    </div>*@
                    </div>
                    @*<div class="col-md-1 semi learn-label">
                    <a style="color: white;" id="learnMoreKostraMain">@Resources.GetResource("cmn_learn_more")</a>
                </div>*@
                </div>
                <div class="col-md-6 padding0 kostra-right-bg" id="kpiLoader">
                    <!-- //cons-right-bg -->
                    <div class="row bottom10">
                            @*@Resources.GetResource("kmp_description")*@
                    </div>


                    <div class="row bottom10">
                        <div class="col-md-12 padding0">
                            <div class="row bottom10">
                                <div class="col-md-6">
                                    <div class="kpi-indicator-box" id="kostraMainIndicator1">
                                        <div class="row">
                                            <div class="col-md-9 padding-right0 kpi-title">
                                                <span class="kpi-indicator-left" id="keyIndicatorTitle1"></span>
                                                <input type="hidden" id="keyIndicator1">
                                                <input type="hidden" id="keyIndicatorItem1">
                                                <input type="hidden" id="keyIndicatorService1">
                                            </div>
                                            <div class="col-md-3  padding0">
                                                <span class="kpi-indicator-right" id="keyIndicatorCurrentYearValue1"></span>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div id="chart1" style="margin-left: 8px;width:90%;"></div>
                                        </div>
                                    </div>

                                </div>
                                <!-- <div class="col-md-2"></div> -->
                                <div class="col-md-6">
                                    <div class="kpi-indicator-box" id="kostraMainIndicator2">
                                        <div class="row">
                                            <div class="col-md-9 padding-right0 kpi-title">
                                                <span class="kpi-indicator-left" id="keyIndicatorTitle2"></span>
                                                <input type="hidden" id="keyIndicator2">
                                                <input type="hidden" id="keyIndicatorItem2">
                                                <input type="hidden" id="keyIndicatorService2">
                                            </div>
                                            <div class="col-md-3 padding0">
                                                <span class="kpi-indicator-right" id="keyIndicatorCurrentYearValue2"></span>

                                            </div>
                                        </div>
                                        <div class="padding5">
                                            <div id="chart2" style="margin-left: 8px;width:90%;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="kpi-indicator-box" id="kostraMainIndicator3">
                                        <div class="row">
                                            <div class="col-md-9 padding-right0 kpi-title">
                                                <span class="kpi-indicator-left" id="keyIndicatorTitle3"></span>
                                                <input type="hidden" id="keyIndicator3">
                                                <input type="hidden" id="keyIndicatorItem3">
                                                <input type="hidden" id="keyIndicatorService3">
                                            </div>
                                            <div class="col-md-3 padding0">
                                                <span class="kpi-indicator-right" id="keyIndicatorCurrentYearValue3"></span>
                                            </div>
                                        </div>
                                        <div class="padding5">
                                            <div id="chart3" style="margin-left: 8px;width:90%;"></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- <div class="col-md-2"></div>-->
                                <div class="col-md-6">
                                    <div class="kpi-indicator-box" id="kostraMainIndicator4">
                                        <div class="row">
                                            <div class="col-md-9 padding-right0 kpi-title">
                                                <span class="kpi-indicator-left" id="keyIndicatorTitle4"></span>
                                                <input type="hidden" id="keyIndicator4">
                                                <input type="hidden" id="keyIndicatorItem4">
                                                <input type="hidden" id="keyIndicatorService4">
                                            </div>
                                            <div class="col-md-3 padding0">
                                                <span class="kpi-indicator-right" id="keyIndicatorCurrentYearValue4"></span>
                                            </div>
                                        </div>
                                        <div class="padding5">
                                            <div id="chart4" style="margin-left: 8px;width:90%;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
                @*<div class="col-md-6 padding0 cons-right-bg" id="kpiLoader">
                    <div class="row bottom10">

                    </div>
                </div>*@

            @await Html.PartialAsync("../Shared/_PartialRevenues")
            @await Html.PartialAsync("../Shared/_PartialCentralExpences")
            @await Html.PartialAsync("../Shared/_PartialOperationalExpenses")
            @await Html.PartialAsync("../Shared/_PartialFinanceIncomeExpenses")
            @await Html.PartialAsync("../Shared/_PartialProvisions")
            @await Html.PartialAsync("../Shared/_PartialTotals")

        </div>
    </div>
</section>
<div id="ConsequenceProfitVsRevenueWindow">
    <section id="content-ConsequenceProfitVsRevenueWindow">
        <div class="k-edit-form-container ConsequenceprofitVsrevenue-window" style="width:740px;height:600px;margin-left:10px;margin-top: 12px;">
            <div class="col-md-12 ConsequenceprofitVsrevenue-bckground bm-window-border-style" id="">
                <div id="ConsequenceProfitVsRevenueChart"></div>
                <div>
                    <div id="ConsequenceHistoricalGrid"></div>
                </div>
            </div>
        </div>
    </section>
        </div>


<div id="consequenceReseveFundWindow">
    <section id="content-ConsequenceReseveFundWindow">
        <div class="k-edit-form-container ConsequenceReseveFundWindow-window" style="width:740px;height:600px;margin-left:10px;margin-top: 12px;">
            <div class="col-md-12 ConsequenceReseveFundWindow-bckground bm-window-border-style" id="">
                <div id="ConsequenceReseveFundChart"></div>
                <div>
                    <div id="ConsequenceReseveFundGrid"></div>
                </div>
            </div>
        </div>
    </section>
</div>
<div id="ConsequenceDebtvsRevenueWindow">
    <section id="content-ConsequenceDebtvsRevenueWindow">
        <div class="k-edit-form-container ConsequencedebtvsrevenueWindow-window" style="width:740px;height:600px;margin-left:10px;margin-top: 12px;">
            <div class="col-md-12 Consequencedebtvsrevenue-bckground bm-window-border-style" id="">
                <div id="ConsequenceDebtvsRevenueChart"></div>
                <div>
                    <div id="ConsequenceDebtvsRevenueGrid"></div>
                </div>
            </div>
        </div>
    </section>
</div>
<div id="consequenceDebtPerCitizenWindow">
    <section id="content-ConsequenceDebtPerCitizenWindow">
        <div class="k-edit-form-container consequencedebtpercitizenWindow-window" style="width:740px;height:600px;margin-left:10px;margin-top: 12px;">
            <div class="col-md-12 consequencedebtpercitizen-bckground bm-window-border-style">
                <div id="ConsequenceDebtPerCitizenChart"></div>
                <div>
                    <div id="ConsequenceDebtPerCitizenGrid"></div>
                </div>
            </div>
        </div>
    </section>
</div>
@*<div id="testProfitVsRevenueWindow">
    <section id="content-testProfitVsRevenueWindow">
        <div class="k-edit-form-container testprofitVsrevenue-window" style="width:580px;height:511px;margin-left:10px;margin-top: 12px;">
            <div class="col-md-12 testprofitVsrevenue-bckground bm-window-border-style" id="ProfitVsRevenueGridandChart">
                <div id="testProfitVsRevenueChart"></div>
                <div>
                    <div id="testHistoricalGrid"></div>
                </div>
            </div>
        </div>
    </section>
</div>
<div id="budgetManagementReseveFundWindow">
    <section id="content-testbudgetManagementReseveFundWindow">
        <div class="k-edit-form-container ReseveFundWindow-window" style="width:580px;height:511px;margin-left:10px;margin-top: 12px;">
            <div class="col-md-12 testbudgetManagementReseveFundWindow-bckground bm-window-border-style" id="">
                <div id="testbudgetManagementReseveFundChart"></div>
                <div>
                    <div id="testbudgetManagementReseveFundGrid"></div>
                </div>
            </div>
        </div>
    </section>
</div>
<div id="testbudgetManagementDebtvsRevenueWindow">
    <section id="content-testbudgetManagementDebtvsRevenueWindow">
        <div class="k-edit-form-container testbudgetManagementdebtvsrevenueWindow-window" style="width:580px;height:511px;margin-left:10px;margin-top: 12px;">
            <div class="col-md-12 testbudgetManagementdebtvsrevenue-bckground bm-window-border-style" id="">
                <div id="testbudgetManagementDebtvsRevenueChart"></div>
                <div>
                    <div id="testbudgetManagementDebtvsRevenueGrid"></div>
                </div>
            </div>
        </div>
    </section>
</div>
<div id="testbudgetManagementDebtPerCitizenWindow">
    <section id="content-testbudgetManagementDebtPerCitizenWindow">
        <div class="k-edit-form-container testbudgetManagementdebtpercitizenWindow-window" style="width:580px;height:511px;margin-left:10px;margin-top: 12px;">
            <div class="col-md-12 testbudgetManagementdebtpercitizen-bckground bm-window-border-style">
                <div id="testbudgetManagementDebtPerCitizenChart"></div>
                <div>
                    <div id="testbudgetManagementDebtPerCitizenGrid"></div>
                </div>
            </div>
        </div>
    </section>
</div>*@
<script id="dataErrorTemplate" type="text/x-kendo-template">
    <div class="error" id="financialOverviewNotification">
        <div class="row">
            <div class="col-md-2"><img src="../images/error_cross.png"></div>
            <div class="col-md-8">
                <h3 style="margin-left: 8px;">@Resources.GetResource("SP_no_change_made_to_grid")</h3>
            </div>
        </div>
    </div>
</script>

<script id="dataErrorTemplate1" type="text/x-kendo-template">
    <div class="error" id="financialOverviewNotification">
        <div class="row">
            <div class="col-md-2"><img src="../images/error_cross.png"></div>
            <div class="col-md-8">
                <h3 style="margin-left: 8px;">@Resources.GetResource("kdp_no_grid_data")</h3>
            </div>
        </div>
    </div>
</script>

@await Html.PartialAsync("../Shared/_PartialPopup")





@*@Scripts.Render("~/bundles/caboverview")*@
<script src="/bundles/caboverview.js"></script>







