using Framsikt.BL.Helpers;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Framsikt.BL;

public partial class KostraData 
{
    /*------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
     * Description: Write the export request to the queue
     * -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
     */

    public void WriteToExpReqQueue(string data, string userId)
    {
        WriteToExpReqQueueAsync(data, userId).GetAwaiter().GetResult();
    }

    public async Task WriteToExpReqQueueAsync(string data, string userId)
    {
        UserData ud = await _utility.GetUserDetailsAsync(userId);
        string exportTreePath = await InsertJsonRequestAsync(data, userId);
        dynamic jsonGuid = new JObject();
        jsonGuid.Add("ExportTreePath", exportTreePath);
        jsonGuid.Add("UserId", userId);
        jsonGuid.Add("TenantId", ud.tenant_id);
        string strJsonGuid = JsonConvert.SerializeObject(jsonGuid);
        Dictionary<string, int> modBudgetYears = await GetBudgetYearsForExportAsync(userId);

        _backendJob.QueueMessage(ud, modBudgetYears, QueueName.exportrequestqueue, strJsonGuid);
    }

    private Dictionary<string, int> GetBudgetYearsForExport(string userId)
    {
        var result = GetBudgetYearsForExportAsync(userId).GetAwaiter().GetResult();
        return result;
    }

    private async Task<Dictionary<string, int>> GetBudgetYearsForExportAsync(string userId)
    {
        Dictionary<string, int> modBudgetYears = new Dictionary<string, int>();
        int kostraBudYear = await GetBudgetYearAsync(userId);
        int popStatBudYear = await GetBudgetYearForModuleAsync(userId, clsConstants.BudgetYear.POPSTAT_BUDGET_YEAR.ToString());

        modBudgetYears.Add("KOSTRA_BUDGET_YEAR", kostraBudYear);
        modBudgetYears.Add("POPSTAT_BUDGET_YEAR", popStatBudYear);
        modBudgetYears.Add("POPFCAST_POPEXP_GROWTH_STARTYR", popStatBudYear + 1);

        //These budget years are irrelevant as they are not used in the document
        modBudgetYears.Add("BUDGETTASK_BUDGET_YEAR", kostraBudYear);
        modBudgetYears.Add("UTILITY_BUDGET_YEAR", kostraBudYear);
        modBudgetYears.Add("BUDGETASSUMPTION_BUDGET_YEAR", kostraBudYear);
        modBudgetYears.Add("BUDMAN_BUDGET_YEAR", kostraBudYear);
        modBudgetYears.Add("BUDPROP_BUDGET_YEAR", kostraBudYear);
        modBudgetYears.Add("CAB_BUDGET_YEAR", kostraBudYear);
        modBudgetYears.Add("INVESTMENTS_BUDGET_YEAR", kostraBudYear);
        modBudgetYears.Add("FINANCING_BUDGET_YEAR", kostraBudYear);
        modBudgetYears.Add("KPIDATA_BUDGET_YEAR", kostraBudYear);
        modBudgetYears.Add("OPPASSMNT_BUDGET_YEAR", kostraBudYear);
        modBudgetYears.Add("STAFFPLAN_BUDGET_YEAR", kostraBudYear);
        modBudgetYears.Add("YEARLYBUDGET_BUDGET_YEAR", kostraBudYear);
        modBudgetYears.Add("BUDGETREGULATION_BUDGET_YEAR", kostraBudYear);

        return modBudgetYears;
    }

    /*------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
     * Description: Write the document delete request to the queue
     * -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
     */

    public async Task WriteToDocDelReqQueueAsync(string docUrl, string userId, QueueName queueName = QueueName.deletedocrequestqueue)
    {
        UserData ud = await _utility.GetUserDetailsAsync(userId);
        await _backendJob.QueueMessageAsync(ud, queueName, docUrl, 5);
    }



    /*------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
     * Description: GetExportDoc - Gets the export document object from the database
     * -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
     */

    public tco_doc_export GetExportDoc(string userId, int tenantId, Guid docId)
    {
        var result = GetExportDocAsync(userId, tenantId, docId).GetAwaiter().GetResult();
        return result;
    }



    public async Task<tco_doc_export> GetExportDocAsync(string userId, int tenantId, Guid docId)
    {
        TenantDBContext kostraDbContext = await _utility.GetTenantDBContextAsync();
        UserData user = await _utility.GetUserDetailsAsync(userId);

        tco_doc_export expDoc = await (from d in kostraDbContext.tco_doc_export
            where (d.DocumentId == docId && d.tenant_id == tenantId && d.user_id == user.pk_id)
            select d).FirstOrDefaultAsync();
        return expDoc;
    }



    public string DeleteDocExportRow(string userId, Guid docId)
    {
        var result = DeleteDocExportRowAsync(userId, docId).GetAwaiter().GetResult();
        return result;
    }



    public async Task<string> DeleteDocExportRowAsync(string userId, Guid docId)
    {
        TenantDBContext uDbContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);

        tco_doc_export doc = await uDbContext.tco_doc_export.SingleOrDefaultAsync(x => x.DocumentId == docId);
        if (doc != null)
        {
            string docUrl = doc.url;

            int ret = 0;
            if (doc.user_id == userDetails.pk_id)
            {
                //notification belongs to the user
                uDbContext.tco_doc_export.Remove(doc);
                ret = await uDbContext.SaveChangesAsync();
            }
            if (ret > 0)
            {
                return docUrl;
            }
            else
            {
                return "";
            }
        }
        else
        {
            return "";
        }
    }



    public void DeleteExplHistr(string userId, int versionId)
    {
        DeleteExplHistrAsync(userId, versionId).GetAwaiter().GetResult();
    }



    public async Task DeleteExplHistrAsync(string userId, int versionId)
    {
        TenantDBContext uDbContext = await _utility.GetTenantDBContextAsync();
        uDbContext.tco_expl_histr.RemoveRange((await uDbContext.tco_expl_histr.Where(x => x.Fk_version_id == versionId).ToListAsync()));
        await uDbContext.SaveChangesAsync();
    }



    public void ArchiveDocument(string userId, ExportParameters exportParameter)
    {
        ArchiveDocumentAsync(userId, exportParameter).GetAwaiter().GetResult();
    }



    public async Task ArchiveDocumentAsync(string userId, ExportParameters exportParameter)
    {
        dynamic jsonReq = new JObject();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> languageStringsBannerCommon = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "bannerText");

        string strExpParams = JsonConvert.SerializeObject(exportParameter);
        dynamic jsonExpParams = JValue.Parse(strExpParams);

        dynamic jsonReqArch = new JObject();
        jsonReqArch.Add("ExportParameters", jsonExpParams);
        string strReqArch = JsonConvert.SerializeObject(jsonReqArch);

        string exportTreePath = await InsertJsonRequestAsync(strReqArch, userId);

        jsonReq.Add("UserId", userId);
        jsonReq.Add("TenantId", userDetails.tenant_id);
        jsonReq.Add("docTitle", languageStringsBannerCommon["kostra_statistics_title"].LangText);
        jsonReq.Add("ExportTreePath", exportTreePath);
        string strReq = JsonConvert.SerializeObject(jsonReq);
        Dictionary<string, int> modBudgetYears = await GetBudgetYearsForExportAsync(userId);

        _backendJob.QueueMessage(userDetails, modBudgetYears, QueueName.exportrequestqueue, strReq);
    }



    public int InsertInDocVersion(string userId, string docType, string docDescription, string docTitle,
        string strExpParams)
    {
        return InsertInDocVersionAsync(userId, docType, docDescription, docTitle, strExpParams).GetAwaiter()
            .GetResult();
    }



    public async Task<int> InsertInDocVersionAsync(string userId, string docType, string docDescription, string docTitle, string strExpParams)
    {
        TenantDBContext kostraDbContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        tco_doc_versions docVersions = new tco_doc_versions();
        docVersions.Fk_tenant_id = userDetails.tenant_id;
        docVersions.Type = docType;
        docVersions.Description = docDescription;
        docVersions.Document_title = docTitle;
        docVersions.Year = DateTime.UtcNow.Year;
        docVersions.Document_url = "";
        docVersions.Selected_nodes = strExpParams;
        docVersions.updated = DateTime.UtcNow;
        docVersions.updated_by = userDetails.pk_id;
        kostraDbContext.tco_doc_versions.Add(docVersions);
        await kostraDbContext.SaveChangesAsync();
        int versionNumber = docVersions.Pk_version_id;
        return versionNumber;
    }



    public void InsertInExplHstr(int versionNumber, string userId, ExportParameters exportParameter)
    {
        InsertInExplHstrAsync(versionNumber, userId, exportParameter).GetAwaiter().GetResult();
    }



    public async Task InsertInExplHstrAsync(int versionNumber, string userId, ExportParameters exportParameter)
    {
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        TenantDBContext kostraDbContext = await _utility.GetTenantDBContextAsync();
        int budgetYear = await GetBudgetYearAsync(userId);
        string reportingAreaId = "";
        List<string> indicatorList = new List<string>();
        foreach (var item in exportParameter.ExportNodes)
        {
            if (item.type == "All")
            {
                indicatorList.Add("All");
                indicatorList.AddRange(from indicatorItem in item.items where indicatorItem.id == "CRPGrid" select "CostRed");
                indicatorList.Add("CPC_PRIORITY");
                indicatorList.Add("exp_need_cg");
                indicatorList.Add("cpc_exp_diff");
                indicatorList.Add("resource_usage_cr");
                indicatorList.Add("KM_CITY_PRODUCTION");
            }
            else if (item.type == "PopulationDevelopment")
            {
                indicatorList.Add("pftotal");
                indicatorList.AddRange(item.items.Select(indicatorItem => indicatorItem.id));
            }
            else if (item.type == "cityranking")
            {
                indicatorList.AddRange(item.items.Select(indicatorItem => indicatorItem.id));
            }
            else
            {
                if (item.type == "ReportingArea")
                {
                    if (reportingAreaId != item.id)
                    {
                        indicatorList.Add(item.id);
                    }
                    reportingAreaId = item.id;
                    indicatorList.Add("twin_graph_ra_1_" + reportingAreaId);
                    indicatorList.Add("twin_graph_ra_2_" + reportingAreaId);
                    indicatorList.Add("kpaCityGraph_" + reportingAreaId);
                    indicatorList.Add("kpaYearGraph_" + reportingAreaId);
                }
                indicatorList.AddRange(item.items.Select(indicatorItem => indicatorItem.id));
            }
        }
        List<clsIndicatorEvaluation> getEvaluations = await (from ev in kostraDbContext.tko_ind_explanation
            where ev.year == budgetYear && indicatorList.Contains(ev.fk_indicator_code) && ev.fk_tenant_id == userDetails.tenant_id && ev.fk_template_id.ToString().ToLower() == exportParameter.templateId.ToLower()
            select new clsIndicatorEvaluation
            {
                indicatorCode = ev.fk_indicator_code,
                evaluation = ev.description,
                documentFlag = ev.document_flag,
                lockedFlag = ev.locked_flag,
                indicatorType = ev.indicator_type,
                templateId = ev.fk_template_id.ToString()
            }).ToListAsync();

        foreach (var item in getEvaluations)
        {
            tco_expl_histr expHstr = new tco_expl_histr
            {
                Fk_version_id = versionNumber,
                fk_tenant_id = userDetails.tenant_id,
                fk_indicator_code = item.indicatorCode,
                description = item.evaluation,
                document_flag = item.documentFlag,
                locked_flag = item.lockedFlag,
                indicator_type = item.indicatorType,
                year = budgetYear,
                fk_template_id = new Guid(item.templateId),
                updated = DateTime.UtcNow,
                updated_by = userDetails.pk_id
            };
            expHstr.blob_url_history = string.Empty;
            kostraDbContext.tco_expl_histr.Add(expHstr);
        }
        await kostraDbContext.SaveChangesAsync();
    }



    public void UpdateDocPublishRow(string userId, string url, int versionId, Guid notificationId)
    {
        UpdateDocPublishRowAsync(userId, url, versionId, notificationId).GetAwaiter().GetResult();
    }



    public async Task UpdateDocPublishRowAsync(string userId, string url, int versionId, Guid notificationId)
    {
        TenantDBContext kDbContext = await _utility.GetTenantDBContextAsync();
        tco_doc_versions docExpVer = await kDbContext.tco_doc_versions.FirstOrDefaultAsync(x => x.Pk_version_id == versionId);
        docExpVer.Document_url = url;
        docExpVer.fk_Notification_id = notificationId;
        await kDbContext.SaveChangesAsync();
    }



    public string GetAllPublishedDocuments(string docType, Dictionary<string, clsLanguageString> langStringValues,
        string userId)
    {
        return GetAllPublishedDocumentsAsync(docType, langStringValues, userId).GetAwaiter().GetResult();
    }



    public async Task<string> GetAllPublishedDocumentsAsync(string docType, Dictionary<string, clsLanguageString> langStringValues, string userId)
    {
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        dynamic allPublishedDocument = new JObject();
        string loggedInUser = userDetails.first_name + " " + userDetails.last_name;
        //allPublishedDocument.Add("columns", columnConfig);

        //Add Fields
        dynamic fieldsArray = new JArray();
        fieldsArray.Add("versionNumber");
        fieldsArray.Add("docId");
        fieldsArray.Add("logedInUser");
        fieldsArray.Add("document");
        fieldsArray.Add("user");
        fieldsArray.Add("updatedDate");
        fieldsArray.Add("delete");
        allPublishedDocument.Add("Fields", fieldsArray);

        ////Add titles
        dynamic titlesArray = new JArray() { " ", " ", " ", (langStringValues["kmp_ArchiveDoc"]).LangText, (langStringValues["kmp_ArchiveDoc_user"]).LangText, (langStringValues["kmp_ArchiveDoc_date"]).LangText };
        allPublishedDocument.Add("Titles", titlesArray);
        ////Add Data

        dynamic finalData = new JArray();
        JArray alldocuments = await GetAllDocumentsAsync(docType, userId);
        foreach (dynamic data in alldocuments)
        {
            dynamic jsonIndividualDocData = new JArray();
            jsonIndividualDocData.Add(data.Pk_version_id);
            jsonIndividualDocData.Add(data.DocumentId);
            jsonIndividualDocData.Add(loggedInUser);
            jsonIndividualDocData.Add(data.Description);
            jsonIndividualDocData.Add(data.UserName);
            jsonIndividualDocData.Add(data.updatedDate);
            finalData.Add(jsonIndividualDocData);
        }
        allPublishedDocument.Add("jsonData", finalData);
        //Serialize and return
        string serializedObj = JsonConvert.SerializeObject(allPublishedDocument);
        return serializedObj;
    }



    private JArray GetAllDocuments(string doctype, string userId)
    {
        return GetAllDocumentsAsync(doctype, userId).GetAwaiter().GetResult();
    }



    public async Task<JArray> GetAllDocumentsAsync(string doctype, string userId)
    {
        TenantDBContext kDbContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        var allPublishedDoc = (await (from dv in kDbContext.tco_doc_versions
            join de in kDbContext.tco_doc_export on new { tid = dv.Fk_tenant_id, docurl = dv.Document_url } equals new { tid = de.tenant_id, docurl = de.url }
            join tu in kDbContext.vwUserDetails on new { tid = dv.Fk_tenant_id, updatedBy = dv.updated_by } equals new { tid = tu.tenant_id, updatedBy = tu.pk_id }
            where dv.Fk_tenant_id == userDetails.tenant_id && dv.Type == doctype
            //orderby dv.updated descending
            select new
            {
                dv.Pk_version_id,
                de.DocumentId,
                dv.Description,
                UserName = tu.first_name + " " + tu.last_name,
                updatedDate = dv.updated,
                updated = dv.updated
                //updatedDate = SqlFunctions.DatePart("dd", dv.updated) + "." + SqlFunctions.DatePart("mm", dv.updated) + "." + SqlFunctions.DatePart("yyyy", dv.updated)
            }).ToListAsync()).OrderByDescending(x => x.updated).ToList();

        JArray dataArray = JArray.FromObject((from dv in allPublishedDoc
            select new
            {
                Pk_version_id = dv.Pk_version_id,
                DocumentId = dv.DocumentId,
                Description = dv.Description,
                UserName = dv.UserName,
                updatedDate = dv.updated.ToString("dd") + "." + dv.updated.ToString("MM") + "." + dv.updated.ToString("yyyy"),
                updated = dv.updated
            }));
        return dataArray;
    }



    public IEnumerable<Guid> DeleteDocVersionRow(string userId, int versionId)
    {
        return DeleteDocVersionRowAsync(userId, versionId).GetAwaiter().GetResult();
    }



    public async Task<IEnumerable<Guid>> DeleteDocVersionRowAsync(string userId, int versionId)
    {
        TenantDBContext uDbContext = await _utility.GetTenantDBContextAsync();

        tco_doc_versions doc = await uDbContext.tco_doc_versions.SingleOrDefaultAsync(x => x.Pk_version_id == versionId);
        tco_doc_export docexp = await uDbContext.tco_doc_export.SingleOrDefaultAsync(x => x.url == doc.Document_url);

        if (docexp != null)
        {
            if (doc != null)
            {
                List<Guid> guids = new List<Guid> { docexp.DocumentId, doc.fk_Notification_id };

                uDbContext.tco_doc_versions.Remove(doc);
                await uDbContext.SaveChangesAsync();

                return guids;
            }
        }

        return new List<Guid>();
    }



    public PublishedDocument GetPublishedDoc(string userId, int tenantId, Guid docId)
    {
        return GetPublishedDocAsync(userId, tenantId, docId).GetAwaiter().GetResult();
    }



    public async Task<PublishedDocument> GetPublishedDocAsync(string userId, int tenantId, Guid docId)
    {
        TenantDBContext kostraDbContext = await _utility.GetTenantDBContextAsync();
        PublishedDocument expDoc = await (from de in kostraDbContext.tco_doc_export
            join dv in kostraDbContext.tco_doc_versions on de.url equals dv.Document_url
            where de.DocumentId == docId && de.tenant_id == tenantId
            select new PublishedDocument()
            {
                VersionId = dv.Pk_version_id,
                DocumenttUrl = dv.Document_url,
                DocumentType = dv.Type
            }).FirstOrDefaultAsync();
        return expDoc;
    }



    public async Task<PublishedDocument> GetPublishedDocByVersionIdAsync(string userId, int tenantId, int VersionId)
    {
        TenantDBContext kostraDbContext = await _utility.GetTenantDBContextAsync();
        PublishedDocument expDoc = await (from dv in kostraDbContext.tco_doc_versions
            where dv.Pk_version_id == VersionId && dv.Fk_tenant_id == tenantId
            select new PublishedDocument()
            {
                VersionId = dv.Pk_version_id,
                DocumenttUrl = dv.Document_url,
                DocumentType = dv.Type
            }).FirstOrDefaultAsync();
        return expDoc;
    }



    public string GetDocVersionDetails(string userId, string indicatorCode, string templateId)
    {
        return GetDocVersionDetailsAsync(userId, indicatorCode, templateId).GetAwaiter().GetResult();
    }



    public async Task<string> GetDocVersionDetailsAsync(string userId, string indicatorCode, string templateId)
    {
        TenantDBContext kDbContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        var getDocVersions = (await (from dv in kDbContext.tco_doc_versions
            join eh in kDbContext.tco_expl_histr on dv.Pk_version_id equals eh.Fk_version_id
            where dv.Fk_tenant_id == userDetails.tenant_id && eh.fk_indicator_code == indicatorCode && eh.fk_template_id.ToString().ToLower() == templateId.ToLower()
            //orderby dv.updated descending
            select new
            {
                dv.Pk_version_id,
                //descriptionWithDate = dv.Description + "  (" + SqlFunctions.DatePart("dd", dv.updated) + "." + SqlFunctions.DatePart("mm", dv.updated) + "." + SqlFunctions.DatePart("yyyy", dv.updated) + ")",
                descriptionWithDate = dv.Description + "  (" + dv.updated.ToString("dd") + "." + dv.updated.ToString("MM") + "." + dv.updated.ToString("yyyy") + ")",
                dv.updated
            }).ToListAsync()).OrderByDescending(x => x.updated);
        JArray dataArray = JArray.FromObject(getDocVersions);
        string serializedObj = JsonConvert.SerializeObject(dataArray);
        return serializedObj;
    }



    public string GetExpNEvalHstry(string userId, int versionId, string indicatorCode)
    {
        return GetExpNEvalHstryAsync(userId, versionId, indicatorCode).GetAwaiter().GetResult();
    }



    public async Task<string> GetExpNEvalHstryAsync(string userId, int versionId, string indicatorCode)
    {
        TenantDBContext kDbContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        var getExpEvalHstr = await (from eh in kDbContext.tco_expl_histr
            where eh.fk_tenant_id == userDetails.tenant_id && eh.Fk_version_id == versionId && eh.fk_indicator_code == indicatorCode
            select new
            {
                evaluation = eh.description
            }).ToListAsync();

        JArray dataArray = JArray.FromObject(getExpEvalHstr);
        string serializedObj = JsonConvert.SerializeObject(dataArray);
        return serializedObj;
    }

}