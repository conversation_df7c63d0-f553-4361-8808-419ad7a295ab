using Framsikt.BL.Exceptions;
using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using static Framsikt.BL.Helpers.clsConstants;

namespace Framsikt.BL;

public partial class KostraData 
{
    public dynamic GetTreeData(string userId, string kostraTemplateId, int publishTemplateId)
    {
        return GetTreeDataAsync(userId, kostraTemplateId, publishTemplateId).GetAwaiter().GetResult();
    }

    public async Task<dynamic> GetTreeDataAsync(string userId, string kostraTemplateId, int publishTemplateId)
    {
        TenantDBContext kostraDbContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        List<PublishTreeNode> exportTree = new List<PublishTreeNode>();
        PublishTreeNode popStatChild;
        Dictionary<string, clsLanguageString> lsKostra = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "KostraExportTree");
        _dataUserRolesAndActivities = (await _utility.GetUserRolesAndActivitiesAsync(userId, userDetails.tenant_id)).ToList();

        int budgetYear = await GetBudgetYearAsync(userId);
        Dictionary<int, string> sortOrder = KostraDataSortOrder(string.Empty);
        Dictionary<int, string> sortOrderArea21 = KostraDataSortOrder("21");

        //Adding the folder for holding unused nodes
        PublishTreeNode unusedNodeFolder = await AddStaticTreeNodeAsync(userId, CommonTreeConstants.UnusedNodesFolder.ToString(),
            CommonTreeConstants.UnusedNodesFolder.ToString(),
            new Dictionary<string, string>());
        unusedNodeFolder.isDisabled = true;
        exportTree.Add(unusedNodeFolder);

        List<ClsKostraReportingAreaRegionCode> reportingAreaChartData = await CheckReportingAreaGridAndChartHasDataAsync(userId);

        var evalForCommunity = new PublishTreeNode
        {
            id = "0",
            text =
                ((await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Export"))
                    .FirstOrDefault(x => x.Key == "Export_tree_root_value")
                    .Value).LangText,
            type = "All",
            @checked = false,
            expanded = false,
            isEditableNode = false
        };

        var evaluation = new PublishTreeNode
        {
            id = "CityEvaluation",
            text =
                ((await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Export"))
                    .FirstOrDefault(x => x.Key == "Export_tree_CityEval")
                    .Value).LangText,
            type = "All",
            @checked = false,
            expanded = false,
            isEditableNode = false
        };
        evalForCommunity.items.Add(evaluation);

        evaluation = new PublishTreeNode();
        evaluation.id = "NetCPC";
        evaluation.text = ((await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Export")).FirstOrDefault(x => x.Key == "Export_tree_netCPC").Value).LangText;
        evaluation.type = "All";
        evaluation.@checked = false;
        evaluation.expanded = false;
        evaluation.isEditableNode = false;
        evalForCommunity.items.Add(evaluation);

        var chartType = await kostraDbContext.gko_main_indicators.FirstOrDefaultAsync(x => x.type == 5);
        if (chartType != null)
        {
            evaluation = new PublishTreeNode();
            evaluation.id = "EXP_NEED";
            evaluation.text =
                ((await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Export"))
                    .FirstOrDefault(x => x.Key == "Export_tree_expenditureNeed")
                    .Value).LangText;
            evaluation.type = "EXPNEED";
            evaluation.@checked = false;
            evaluation.expanded = false;
            evaluation.isEditableNode = false;
            evalForCommunity.items.Add(evaluation);
        }

        chartType = await kostraDbContext.gko_main_indicators.FirstOrDefaultAsync(x => x.type == 6);
        if (chartType != null)
        {
            evaluation = new PublishTreeNode();
            evaluation.id = "RECE_USE";
            evaluation.text =
                ((await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Export"))
                    .FirstOrDefault(x => x.Key == "Export_tree_ResourceUse")
                    .Value).LangText;
            evaluation.type = "RECEUSE";
            evaluation.@checked = false;
            evaluation.expanded = false;
            evaluation.isEditableNode = false;
            evalForCommunity.items.Add(evaluation);
        }

        chartType = await kostraDbContext.gko_main_indicators.FirstOrDefaultAsync(x => x.type == 11);
        if (chartType != null)
        {
            evaluation = new PublishTreeNode();
            evaluation.id = "QLTY_RANK";
            evaluation.text = ((await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Export"))
                .FirstOrDefault(x => x.Key == "Export_tree_QualityRanking")
                .Value).LangText;
            evaluation.type = "QLTYRANK";
            evaluation.@checked = false;
            evaluation.expanded = false;
            evaluation.isEditableNode = false;
            evalForCommunity.items.Add(evaluation);
        }

        evaluation = new PublishTreeNode();
        evaluation.id = "COMP_MUNCIP";
        evaluation.text = ((await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Export")).FirstOrDefault(x => x.Key == "Export_tree_CompMuncipality").Value).LangText;
        evaluation.type = "COMPMUNCIP";
        evaluation.@checked = false;
        evaluation.expanded = false;
        evaluation.isEditableNode = false;
        evalForCommunity.items.Add(evaluation);

        evaluation = new PublishTreeNode();
        evaluation.id = "CRPGrid";
        evaluation.text = ((await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Export")).FirstOrDefault(x => x.Key == "Export_tree_CRP_Grid").Value).LangText;
        evaluation.type = "All";
        evaluation.@checked = false;
        evaluation.expanded = false;
        evaluation.isEditableNode = false;
        evalForCommunity.items.Add(evaluation);

        exportTree.Add(evalForCommunity);

        /*2. Population Development*/
        List<string> sectionConfig = await GetTabsIncludedInDocAsync(kostraTemplateId, userId, "po");
        clsIndicatorEvaluation eval = await GetIndicatorEvaluationAsync(userId, budgetYear, "pftotal", kostraTemplateId);
        IEnumerable<PopForecastIndicator> popData = await GetPopForcastIndicatorsAsync(userId, kostraTemplateId);
        var popStat = new PublishTreeNode();
        if (sectionConfig.Any() || eval != null || popData.Any())
        {
            popStat.id = "PD01";
            popStat.text = ((lsKostra.FirstOrDefault(v => v.Key == "population_development")).Value).LangText;
            popStat.type = "PopulationDevelopment";
            popStat.expanded = false;
            popStat.@checked = false;
            popStat.isEditableNode = false;

            foreach (PopForecastIndicator p in popData)
            {
                popStatChild = new PublishTreeNode();
                popStatChild.id = p.PopForecastIndicatorID;
                popStatChild.text = p.PopForecastIndicatorName;
                popStatChild.type = "PopulationDevelopment";
                popStatChild.expanded = false;
                popStatChild.@checked = false;
                popStatChild.isEditableNode = false;
                popStat.items.Add(popStatChild);
            }
            exportTree.Add(popStat);
        }
        /*Pop Stat End*/
        var reportingAreas = await (from r in kostraDbContext.gmd_reporting_areas
            where r.active == 1
            //orderby r.sorting_order
            select new
            {
                r.pk_report_area_code,
                r.report_area_name,
                r.sorting_order
            }).OrderBy(z => z.sorting_order).ToDictionaryAsync(x => x.pk_report_area_code, x => x.report_area_name);

        var EvaluationAndIndicatorsList = await (from t in kostraDbContext.tko_ind_explanation
            join l in kostraDbContext.gmd_kostra_lables on t.fk_indicator_code equals l.pk_indicator_code
            join kf in kostraDbContext.gmd_kostra_function on l.fk_kostra_function_code equals kf.pk_kostra_function_code

            where l.active && t.fk_tenant_id == userDetails.tenant_id && t.document_flag && t.year == budgetYear && l.type.ToUpper() != "CR"
                  && reportingAreas.Keys.Contains(kf.fk_report_area_code) && t.fk_template_id.ToString().ToLower() == kostraTemplateId.ToLower()
            select new
            {
                areaCode = kf.fk_report_area_code,
                indicatorCode = t.fk_indicator_code,
                indicatorDesc = l.indicator_description,
                l.type
            }).ToListAsync();
        List<string> detailTabs = clsConstants.GetDetailTabs.Values.ToList();

        var incDocSecIndicatorsList = await (from sc in kostraDbContext.tco_section_config
            join l in kostraDbContext.gmd_kostra_lables on sc.fk_indicator_code equals l.pk_indicator_code
            join kf in kostraDbContext.gmd_kostra_function on l.fk_kostra_function_code equals kf.pk_kostra_function_code
            where l.active && sc.fk_tenant_id == userDetails.tenant_id && sc.include_in_document == true && reportingAreas.Keys.Contains(kf.fk_report_area_code) && sc.fk_template_id.ToString().ToLower() == kostraTemplateId.ToLower()
                  && detailTabs.Contains(sc.section_id)
            select new
            {
                areaCode = kf.fk_report_area_code,
                indicatorCode = sc.fk_indicator_code,
                section_id = sc.section_id
            }).Distinct().ToListAsync();

        foreach (var ra in reportingAreas)
        {
            var raEvaluationAndIndicatorsList = EvaluationAndIndicatorsList.Where(x => x.areaCode == ra.Key).ToList();
            var raEvaluationAndIndicators = (from rei in raEvaluationAndIndicatorsList
                join so in sortOrder on rei.type.ToLower() equals so.Value.ToLower()
                select new
                {
                    rei.areaCode,
                    rei.indicatorCode,
                    rei.indicatorDesc,
                    rei.type,
                    sortOrder = so.Key
                }).ToList();
            if (ra.Key == "21")
            {
                raEvaluationAndIndicators = (from rei in raEvaluationAndIndicatorsList
                    join so in sortOrderArea21 on rei.type.ToLower() equals so.Value.ToLower()
                    select new
                    {
                        rei.areaCode,
                        rei.indicatorCode,
                        rei.indicatorDesc,
                        rei.type,
                        sortOrder = so.Key
                    }).ToList();
            }

            var withLang = (from indVal in raEvaluationAndIndicators
                join lang in kostraDbContext.gco_language_strings on indVal.type.ToLower() equals lang.Description.ToLower()
                where lang.context == "KostraExportTree"
                select new
                {
                    indVal.areaCode,
                    indVal.indicatorCode,
                    indVal.indicatorDesc,
                    langCode = lang.ID,
                    indVal.sortOrder
                }).ToList();

            var lstRaEvaluationAndIndicators = (from wl in withLang
                join ls in kostraDbContext.gco_language_strings on wl.langCode equals ls.ID
                where ls.Language == userDetails.language_preference
                select new
                {
                    wl.areaCode,
                    wl.indicatorCode,
                    indicatorDesc = ls.Description + "-" + wl.indicatorDesc,
                    wl.sortOrder,
                    ls.Language
                }).ToList();
            lstRaEvaluationAndIndicators = lstRaEvaluationAndIndicators.Where(x => x.Language == userDetails.language_preference).Distinct().ToList();
            var evalDataUrl = await (from t in kostraDbContext.tko_ind_explanation
                where t.fk_indicator_code == ra.Key && t.year == budgetYear && t.fk_tenant_id == userDetails.tenant_id && t.document_flag && t.indicator_type == "RA" && t.fk_template_id.ToString().ToLower() == kostraTemplateId.ToLower()
                select new
                {
                    t.description
                }).FirstOrDefaultAsync();

            string evalData = evalDataUrl != null ? evalDataUrl.description : string.Empty;

            List<PublishTreeNode> lstIndicators = (from i in lstRaEvaluationAndIndicators
                where i.indicatorCode != ra.Key
                orderby i.sortOrder, i.indicatorDesc
                select new PublishTreeNode
                {
                    id = i.indicatorCode,
                    text = i.indicatorDesc,
                    type = "Indicator",
                    expanded = false,
                    @checked = false,
                    isEditableNode = false
                }).ToList();
            foreach (var selIndicators in lstIndicators)
            {
                List<PublishTreeNode> graphName = await GetGraphWithStatusAsync(incDocSecIndicatorsList.Where(x => x.areaCode == ra.Key && x.indicatorCode == selIndicators.id).Select(x => x.section_id).ToList(), userId);
                selIndicators.items.AddRange(graphName);
            }
            if (lstIndicators.Any())
            {
                var reportingArea = new PublishTreeNode();
                reportingArea.id = ra.Key;
                reportingArea.text = ra.Value;
                reportingArea.type = "ReportingArea";
                reportingArea.@checked = false;
                reportingArea.expanded = false;
                reportingArea.isEditableNode = false;

                if (!string.IsNullOrEmpty(evalData))
                {
                    string evaluationText = ((await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Export")).FirstOrDefault(x => x.Key == "Export_tree_evaluation_for").Value).LangText;
                    evaluation = new PublishTreeNode();
                    evaluation.id = ra.Key;
                    evaluation.text = evaluationText + " " + ra.Value;
                    evaluation.type = "ReportingAreaEval";
                    evaluation.@checked = false;
                    evaluation.expanded = false;
                    evaluation.isEditableNode = false;
                    reportingArea.items.Add(evaluation);
                }

                //Kostra overview evaluation Grid
                PublishTreeNode overviewTable = new PublishTreeNode();
                overviewTable.id = ra.Key + "OT";
                overviewTable.text = ((await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Export")).FirstOrDefault(x => x.Key == "Export_tree_kostra_eval_table").Value).LangText;
                overviewTable.type = "OviewViewTable";
                overviewTable.@checked = false;
                overviewTable.expanded = false;
                overviewTable.isEditableNode = false;
                reportingArea.items.Add(overviewTable);

                clsConfig configGraph1 = await GetSectionConfigForKostraOverviewTwinChartAsync(userId, new Guid(kostraTemplateId), "twin_graph1_per_reporting_area-" + ra.Key.ToString());
                if (configGraph1.indicatorCode != null)
                {
                    PublishTreeNode twinGraph1 = new PublishTreeNode();
                    twinGraph1.id = ra.Key;
                    twinGraph1.text = ((await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Export")).FirstOrDefault(x => x.Key == "Export_tree_overview_twint1").Value).LangText;
                    twinGraph1.type = "twinGraph1";
                    twinGraph1.@checked = false;
                    twinGraph1.expanded = false;
                    twinGraph1.isEditableNode = false;
                    reportingArea.items.Add(twinGraph1);
                }

                clsConfig configGraph2 = await GetSectionConfigForKostraOverviewTwinChartAsync(userId, new Guid(kostraTemplateId), "twin_graph2_per_reporting_area-" + ra.Key.ToString());
                if (configGraph2.indicatorCode != null)
                {
                    PublishTreeNode twinGraph2 = new PublishTreeNode();
                    twinGraph2.id = ra.Key;
                    twinGraph2.text = ((await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Export")).FirstOrDefault(x => x.Key == "Export_tree_overview_twint2").Value).LangText;
                    twinGraph2.type = "twinGraph2";
                    twinGraph2.@checked = false;
                    twinGraph2.expanded = false;
                    twinGraph2.isEditableNode = false;
                    reportingArea.items.Add(twinGraph2);
                }

                if (reportingAreaChartData.Where(x => x.reportAreaCode == ra.Key).Count() > 0)
                {
                    PublishTreeNode reportingAreaGraph = new PublishTreeNode();
                    reportingAreaGraph.id = ra.Key;
                    reportingAreaGraph.text = ((await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Export")).FirstOrDefault(x => x.Key == "Reporting_Area_graph_title").Value).LangText;
                    reportingAreaGraph.type = "reportingAreaGraph";
                    reportingAreaGraph.@checked = false;
                    reportingAreaGraph.expanded = false;
                    reportingAreaGraph.isEditableNode = false;
                    reportingArea.items.Add(reportingAreaGraph);
                }

                reportingArea.items.AddRange(lstIndicators);
                exportTree.Add(reportingArea);
            }
        }

        PublishTemplateHelper templateToApply = null;
        List<PublishTreeNode> updatedTree = null;
        dynamic treeWithIndOpt = new JObject();

        IEnumerable<PublishTreeNode> customNodes = await _customNodeManager.GetCustomNodesAsync(userId, budgetYear,
            PublishTreeType.Kostra, kostraTemplateId);
        if (publishTemplateId == -1)
        {
            //Load default template
            templateToApply = await _publishTemplateManager.GetDefaultPublishTemplateAsync(userId, PublishTreeType.Kostra, budgetYear,
                kostraTemplateId);
            if (templateToApply == null)
            {
                //If default template does not exist, create it from current tree

                PublishTemplateHelper templateData = new PublishTemplateHelper
                {
                    Name = "default",
                    ShortName = "default",
                    BudgetYear = budgetYear,
                    IsGlobal = true,
                    IsDefault = true,
                    Tree = exportTree,
                    KostraTemplateId = Guid.Parse(kostraTemplateId)
                };
                await _publishTemplateManager.CreatePublishTemplateAsync(userId, templateData, PublishTreeType.Kostra, kostraTemplateId.Trim());
                templateToApply = await _publishTemplateManager.GetDefaultPublishTemplateAsync(userId, PublishTreeType.Kostra, budgetYear,
                    kostraTemplateId);
            }
            updatedTree = _publishTemplateManager.ApplyTemplate(exportTree, templateToApply.Tree, customNodes, null, true);
        }
        else
        {
            //Load the template
            templateToApply = await _publishTemplateManager.GetPublishTemplateAsync(userId, publishTemplateId, budgetYear);
            if (templateToApply == null)
            {
                throw new InvalidDataException("Template not found");
            }
            updatedTree = _publishTemplateManager.ApplyTemplate(exportTree, templateToApply.Tree, customNodes, null, false);
        }
        treeWithIndOpt.DisplayIndicatorOption = string.IsNullOrEmpty(templateToApply.DisplayIndicatorOption) ? "3" : templateToApply.DisplayIndicatorOption;
        treeWithIndOpt.Tree = JToken.FromObject(updatedTree);
        return treeWithIndOpt;
    }

    private PublishTreeNode AddStaticTreeNode(string userId, string id, string type,
        Dictionary<string, string> parameters,
        bool serviceUnitOrItsChild = false, int orgLevel = -1, int serviceLevel = -1, bool isChapter = false,
        bool isServiceId = false, int chapterLvl = 1)
    {
        return AddStaticTreeNodeAsync(userId, id, type,
            parameters,
            serviceUnitOrItsChild, orgLevel, serviceLevel, isChapter,
            isServiceId, chapterLvl).GetAwaiter().GetResult();
    }

    private async Task<PublishTreeNode> AddStaticTreeNodeAsync(string userId, string id, string type,
        Dictionary<string, string> parameters,
        bool serviceUnitOrItsChild = false, int orgLevel = -1, int serviceLevel = -1, bool isChapter = false,
        bool isServiceId = false, int chapterLvl = 1)
    {
        UserData userdetails = await _utility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> lsKostra = await _utility.GetLanguageStringsAsync(userdetails.language_preference, userdetails.user_name, "KostraExporTree");
        IEnumerable<gmd_publish_tree_node_definitions> treeNodeDefs = await _utility.GetPulishNodeDefinitionsAsync(userId, PublishTreeType.Kostra);

        gmd_publish_tree_node_definitions current = treeNodeDefs.FirstOrDefault(x => x.type == type);
        if (current == null)
        {
            throw new DataNotFoundException($"Specified type - {type} was not present in metadata");
        }

        PublishTreeNode treeNode = new PublishTreeNode
        {
            id = id,
            text = lsKostra.FirstOrDefault(v => v.Key == current.title_key).Value.LangText,
            type = type,
            expanded = current.expanded,
            @checked = current.is_checked,
            isEditableNode = current.is_editable,
            parameters = parameters,
            serviceUnitOrItsChild = serviceUnitOrItsChild,
            orgLevel = orgLevel,
            serviceLevel = serviceLevel,
            isChapter = isChapter,
            isServiceId = isServiceId,
            chapterLevel = chapterLvl
        };
        if (!_dataUserRolesAndActivities.Exists(e => e.Equals("kostra/updatecustomnode")))
        {
            treeNode.isAdmin = false;
        }
        return treeNode;
    }

    public IEnumerable<PublishTreeNode> GetserviceAreaTreedata(string userId, string language, string areaCode,
        string templateId)
    {
        return GetserviceAreaTreedataAsync(userId, language, areaCode, templateId).GetAwaiter().GetResult();
    }



    public async Task<IEnumerable<PublishTreeNode>> GetserviceAreaTreedataAsync(string userId, string language, string areaCode, string templateId)
    {
        TenantDBContext kostraDbContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        List<PublishTreeNode> exportTree = new List<PublishTreeNode>();
        PublishTreeNode reportingArea;
        PublishTreeNode evaluation;
        int budgetYear = await GetBudgetYearAsync(userId);
        Dictionary<int, string> sortOrder = KostraDataSortOrder(areaCode);
        List<ClsKostraReportingAreaRegionCode> reportingAreaChartData = await CheckReportingAreaGridAndChartHasDataAsync(userId);
        var reportingAreas = await (from r in kostraDbContext.gmd_reporting_areas
            where r.active == 1 && r.pk_report_area_code == areaCode
            //orderby r.sorting_order
            select new
            {
                r.pk_report_area_code,
                r.report_area_name,
                r.sorting_order
            }).OrderBy(z => z.sorting_order).ToDictionaryAsync(x => x.pk_report_area_code, x => x.report_area_name);
        if (reportingAreas.Count > 0)
        {
            reportingArea = new PublishTreeNode();
            reportingArea.id = reportingAreas.ElementAt(0).Key;
            reportingArea.text = reportingAreas.ElementAt(0).Value;
            reportingArea.type = "ReportingArea";
            reportingArea.@checked = false;
            reportingArea.expanded = true;
            reportingArea.isEditableNode = false;

            var raEvaluationAndIndicatorsList = await (from t in kostraDbContext.tko_ind_explanation
                join l in kostraDbContext.gmd_kostra_lables on t.fk_indicator_code equals l.pk_indicator_code
                join kf in kostraDbContext.gmd_kostra_function on l.fk_kostra_function_code equals kf.pk_kostra_function_code

                where l.active && t.fk_tenant_id == userDetails.tenant_id && t.document_flag && t.year == budgetYear && l.type.ToUpper() != "CR"
                      && kf.fk_report_area_code == reportingArea.id && t.fk_template_id.ToString().ToLower() == templateId.ToLower()
                select new
                {
                    areaCode = kf.fk_report_area_code,
                    indicatorCode = t.fk_indicator_code,
                    indicatorDesc = l.indicator_description,
                    type = l.type
                }).ToListAsync();
            List<string> detailTabs = clsConstants.GetDetailTabs.Values.ToList();
            var incDocSecIndicatorsList = await (from sc in kostraDbContext.tco_section_config
                join l in kostraDbContext.gmd_kostra_lables on sc.fk_indicator_code equals l.pk_indicator_code
                join kf in kostraDbContext.gmd_kostra_function on l.fk_kostra_function_code equals kf.pk_kostra_function_code
                where l.active && sc.fk_tenant_id == userDetails.tenant_id && sc.include_in_document == true && reportingAreas.Keys.Contains(kf.fk_report_area_code) && sc.fk_template_id.ToString().ToLower() == templateId.ToLower()
                      && detailTabs.Contains(sc.section_id)
                select new
                {
                    areaCode = kf.fk_report_area_code,
                    indicatorCode = sc.fk_indicator_code,
                    section_id = sc.section_id
                }).Distinct().ToListAsync();

            var raEvaluationAndIndicators = (from rei in raEvaluationAndIndicatorsList
                join so in sortOrder on rei.type.ToLower() equals so.Value.ToLower()
                select new
                {
                    rei.areaCode,
                    rei.indicatorCode,
                    rei.indicatorDesc,
                    rei.type,
                    sortOrder = so.Key
                }).ToList();

            var withLang = (from indVal in raEvaluationAndIndicators
                join lang in kostraDbContext.gco_language_strings on indVal.type.ToLower() equals lang.Description.ToLower()
                where lang.context == "KostraExportTree"
                select new
                {
                    indVal.areaCode,
                    indVal.indicatorCode,
                    indVal.indicatorDesc,
                    langCode = lang.ID,
                    indVal.sortOrder
                }).ToList();

            var lstRaEvaluationAndIndicators = (from wl in withLang
                join ls in kostraDbContext.gco_language_strings on wl.langCode equals ls.ID
                where ls.Language == userDetails.language_preference
                select new
                {
                    wl.areaCode,
                    wl.indicatorCode,
                    indicatorDesc = ls.Description + "-" + wl.indicatorDesc,
                    wl.sortOrder,
                    ls.Language
                }).ToList();

            lstRaEvaluationAndIndicators = lstRaEvaluationAndIndicators.Where(x => x.Language == userDetails.language_preference).Distinct().ToList();

            var evalDataUrl = await (from t in kostraDbContext.tko_ind_explanation
                where t.fk_indicator_code == reportingArea.id && t.year == budgetYear && t.fk_tenant_id == userDetails.tenant_id && t.document_flag && t.indicator_type == "RA" && t.fk_template_id.ToString().ToLower() == templateId.ToLower()
                select new
                {
                    t.description
                }).FirstOrDefaultAsync();
            string evalData = evalDataUrl != null ? evalDataUrl.description : string.Empty;
            int index = 0;

            if (!string.IsNullOrEmpty(evalData))
            {
                string EvaluationText = ((await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Export")).FirstOrDefault(x => x.Key == "Export_tree_evaluation_for").Value).LangText;
                evaluation = new PublishTreeNode();
                evaluation.id = reportingArea.id;
                evaluation.text = EvaluationText + " " + reportingArea.text;
                evaluation.type = "ReportingAreaEval";
                evaluation.@checked = false;
                evaluation.expanded = true;
                evaluation.isEditableNode = false;
                reportingArea.items.Add(evaluation);
                index = 1;
            }

            clsConfig configGraph2 = await GetSectionConfigForKostraOverviewTwinChartAsync(userId, new Guid(templateId), "twin_graph2_per_reporting_area-" + reportingArea.id.ToString());
            if (configGraph2.indicatorCode != null)
            {
                PublishTreeNode twinGraph2 = new PublishTreeNode();
                twinGraph2.id = reportingArea.id;
                twinGraph2.text = ((await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Export")).FirstOrDefault(x => x.Key == "Export_tree_overview_twint2").Value).LangText;
                twinGraph2.type = "twinGraph2";
                twinGraph2.@checked = false;
                twinGraph2.expanded = true;
                twinGraph2.isEditableNode = false;

                reportingArea.items.Insert(index, twinGraph2);
            }

            clsConfig configGraph1 = await GetSectionConfigForKostraOverviewTwinChartAsync(userId, new Guid(templateId), "twin_graph1_per_reporting_area-" + reportingArea.id.ToString());
            if (configGraph1.indicatorCode != null)
            {
                PublishTreeNode twinGraph1 = new PublishTreeNode();
                twinGraph1.id = reportingArea.id;
                twinGraph1.text = ((await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Export")).FirstOrDefault(x => x.Key == "Export_tree_overview_twint1").Value).LangText;
                twinGraph1.type = "twinGraph1";
                twinGraph1.@checked = false;
                twinGraph1.expanded = true;
                reportingArea.items.Insert(index, twinGraph1);
            }
            if (reportingAreaChartData.Where(x => x.reportAreaCode == reportingArea.id).Count() > 0)
            {
                PublishTreeNode reportingAreaGraph = new PublishTreeNode();
                reportingAreaGraph.id = reportingArea.id;
                reportingAreaGraph.text = ((await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Export")).FirstOrDefault(x => x.Key == "Reporting_Area_graph_title").Value).LangText;
                reportingAreaGraph.type = "reportingAreaGraph";
                reportingAreaGraph.@checked = false;
                reportingAreaGraph.expanded = false;
                reportingAreaGraph.isEditableNode = false;
                reportingArea.items.Add(reportingAreaGraph);
            }

            PublishTreeNode overviewTable = new PublishTreeNode();
            overviewTable.id = reportingArea.id + "OT";
            overviewTable.text = ((await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Export")).FirstOrDefault(x => x.Key == "Export_tree_kostra_eval_table").Value).LangText;
            overviewTable.type = "OviewViewTable";
            overviewTable.@checked = false;
            overviewTable.expanded = true;
            overviewTable.isEditableNode = false;
            reportingArea.items.Insert(index, overviewTable);

            List<PublishTreeNode> lstIndicators = (from i in lstRaEvaluationAndIndicators
                where i.indicatorCode != reportingArea.id
                orderby i.sortOrder, i.indicatorDesc
                select new PublishTreeNode
                {
                    id = i.indicatorCode,
                    text = i.indicatorDesc,
                    type = "Indicator",
                    expanded = false,
                    @checked = false
                }).ToList();
            foreach (var selIndicators in lstIndicators)
            {
                List<PublishTreeNode> graphName = await GetGraphWithStatusAsync(incDocSecIndicatorsList.Where(x => x.areaCode == reportingArea.id && x.indicatorCode == selIndicators.id).Select(x => x.section_id).ToList(), userId);
                selIndicators.items.AddRange(graphName);
            }

            reportingArea.items.AddRange(lstIndicators);

            exportTree.Add(reportingArea);
        }
        return exportTree;
    }

}