using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Globalization;

namespace Framsikt.BL;

public partial class KostraData 
{
    public int GetBudgetYear(string userId)
    {
        return _utility.GetActiveBudgetYear(userId, clsConstants.BudgetYear.KOSTRA_BUDGET_YEAR.ToString());
    }

    public async Task<int> GetBudgetYearAsync(string userId)
    {
        return await _utility.GetActiveBudgetYearAsync(userId, clsConstants.BudgetYear.KOSTRA_BUDGET_YEAR.ToString());
    }

    private int GetBudgetYearForModule(string userId, string modName)
    {
        var result = GetBudgetYearForModuleAsync(userId, modName).GetAwaiter().GetResult();
        return result;
    }



    private async Task<int> GetBudgetYearForModuleAsync(string userId, string modName)
    {
        return await _utility.GetActiveBudgetYearAsync(userId, modName);
    }



    public string GetCostPerCitizen(string user, string type, bool adjustForExpenseNeed, CompFilter compFilter,
        string templateId)
    {
        var result = GetCostPerCitizenAsync(user, type, adjustForExpenseNeed, compFilter, templateId).GetAwaiter()
            .GetResult();
        return result;
    }



    public async Task<string> GetCostPerCitizenAsync(string user, string type, bool adjustForExpenseNeed, CompFilter compFilter, string templateId)
    {
        Dictionary<string, string> lstComparingCites;
        dynamic indicatorHistory;

        lstComparingCites = GetComparableCities(compFilter.Data, user);
        if (type.ToLower() == "amount")
        {
            indicatorHistory = await GetCostPerCitizenFormattedAsync(user, "amount", lstComparingCites, adjustForExpenseNeed, compFilter.Data.Any() ? compFilter.Data : null);
        }
        else
        {
            indicatorHistory = await GetCostPerCitizenFormattedAsync(user, "percentage", lstComparingCites, adjustForExpenseNeed, compFilter.Data.Any() ? compFilter.Data : null);
        }

        string serializedObj = JsonConvert.SerializeObject(indicatorHistory);

        return serializedObj;
    }



    public dynamic GetCostPerCitizenFormatted(string userId, string type, Dictionary<string, string> cities,
        bool adjustForExpenseNeed, List<clsRegion> FilterData)
    {
        var result = GetCostPerCitizenFormattedAsync(userId, type, cities, adjustForExpenseNeed, FilterData)
            .GetAwaiter().GetResult();
        return result;
    }



    public async Task<dynamic> GetCostPerCitizenFormattedAsync(string userId, string type, Dictionary<string, string> cities, bool adjustForExpenseNeed, List<clsRegion> FilterData)
    {
        IEnumerable<dynamic> lstData = null;

        if (type.ToLower() == "amount")
        {
            lstData = await GetCostPerCitizenForAmountAsync(userId, cities, adjustForExpenseNeed);
        }
        else
        {
            lstData = await GetCostPerCitizenForPercentageAsync(userId, cities);
        }
        return await FormatCostPerCitizenAsync(userId, lstData, type, adjustForExpenseNeed, FilterData);
    }



    public async Task<dynamic> FormatCostPerCitizenAsync(string userId, IEnumerable<dynamic> lstData, string type, bool adjustForExpenseNeed, List<clsRegion> FilterData)
    {
        string numberType;
        if (type == "amount")
        {
            numberType = "amount";
        }
        else
        {
            numberType = "percentage";
        }

        AuthenticationDBContext authenticationDbContext = _utility.GetAuthenticationContext();
        TenantDBContext kostraDbContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        Dictionary<string, string> colorsForGraph = await _utility.GetColorsAsync(userId, ColorsFor.Graph);

        dynamic costPerCitizen = new JObject();
        dynamic lst = new JArray();
        Dictionary<string, string> dictLables = new Dictionary<string, string>();
        List<dynamic> lstDataAfterSort = new List<dynamic>();
        if (FilterData != null)
        {
            var lstDataWithOrder = (from c in lstData
                join fd in FilterData on c.fk_region_code equals fd.RegionCode
                    into grp
                from fr in grp.DefaultIfEmpty()
                select new
                {
                    fk_region_code = c.fk_region_code,
                    fk_report_area_code = c.fk_report_area_code,
                    report_area_name = c.report_area_name,
                    fk_indicator_code = c.fk_indicator_code,
                    indicator_value = c.indicator_value,
                    expence_adjusted_value = c.expence_adjusted_value,
                    order = fr != null ? fr.sortOrder : 0
                }).ToList();

            foreach (var v in lstDataWithOrder.OrderBy(x => x.order).ToList())
            {
                dynamic val = new DynamicDictionary();
                val.fk_region_code = v.fk_region_code;
                val.fk_report_area_code = v.fk_report_area_code;
                val.report_area_name = v.report_area_name;
                val.fk_indicator_code = v.fk_indicator_code;
                val.indicator_value = v.indicator_value;
                val.expence_adjusted_value = v.expence_adjusted_value;
                lstDataAfterSort.Add(val);
            }
        }
        else
        {
            var lstDataWithOrder = (from c in lstData
                select new
                {
                    fk_region_code = c.fk_region_code,
                    fk_report_area_code = c.fk_report_area_code,
                    report_area_name = c.report_area_name,
                    fk_indicator_code = c.fk_indicator_code,
                    indicator_value = c.indicator_value,
                    expence_adjusted_value = c.expence_adjusted_value,
                    order = 0
                }).ToList();

            foreach (var v in lstDataWithOrder.OrderBy(x => x.order).ToList())
            {
                dynamic val = new DynamicDictionary();
                val.fk_region_code = v.fk_region_code;
                val.fk_report_area_code = v.fk_report_area_code;
                val.report_area_name = v.report_area_name;
                val.fk_indicator_code = v.fk_indicator_code;
                val.indicator_value = v.indicator_value;
                val.expence_adjusted_value = v.expence_adjusted_value;
                lstDataAfterSort.Add(val);
            }
        }

        dynamic citiesDetails = null;
        if (FilterData != null)
        {
            var result = (from c in lstDataAfterSort
                join fd in FilterData on c.fk_region_code equals fd.RegionCode
                    into grp
                from fr in grp.DefaultIfEmpty()
                select new
                {
                    c.fk_region_code,
                    order = fr != null ? fr.sortOrder : 0
                }).Distinct().ToList();

            citiesDetails = (from c in result
                orderby c.order ascending
                select new
                {
                    c.fk_region_code
                }).ToList();
        }
        else
        {
            citiesDetails = (from c in lstDataAfterSort
                orderby c.fk_region_code
                select new
                {
                    c.fk_region_code
                }).Distinct().ToList();
        }

        List<int> averagesIndex = new List<int>();
        foreach (var c in citiesDetails)
        {
            string regionCode = c.fk_region_code;
            dictLables.Add(regionCode, (await kostraDbContext.gco_region_codes.FirstOrDefaultAsync(x => x.pk_region_code == regionCode)).region_name);
            if (regionCode.StartsWith("E"))
            {
                averagesIndex.Add(((dictLables.Keys.ToList()).IndexOf(c.fk_region_code)) + 1);
            }
        }

        var reportingAreas = (from r in lstDataAfterSort
            select new
            {
                r.fk_report_area_code,
                r.report_area_name
            }).Distinct().ToDictionary(x => x.fk_report_area_code, x => x.report_area_name);
        var topReportingAreas = (reportingAreas != null && reportingAreas.Count() >= 10) ? (from r in reportingAreas select r).Take(9) : reportingAreas;
        var otherReportingAreas = (reportingAreas != null && reportingAreas.Count() >= 10) ? (from r in reportingAreas select r).Skip(9) : null;
        int count = 0;
        Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "NumberFormats");
        Dictionary<string, clsLanguageString> totalText = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "totalText");
        string format = ((langStringValues.FirstOrDefault(v => v.Key == numberType)).Value).LangText;
        foreach (var r in topReportingAreas)
        {
            dynamic region = new JObject();
            region.Name = r.Value;
            region.number_type = format;
            List<decimal> dataValues = new List<decimal>();
            foreach (var d in lstDataAfterSort)
            {
                foreach (var c in citiesDetails)
                {
                    if (d.fk_region_code == c.fk_region_code)
                    {
                        if (d.fk_report_area_code == r.Key)
                        {
                            if (type.ToLower() == "percentage" || adjustForExpenseNeed == false)
                            {
                                dataValues.Add(Convert.ToDecimal(d.indicator_value));
                            }
                            else
                            {
                                dataValues.Add(Convert.ToDecimal(d.expence_adjusted_value));
                            }
                        }
                    }
                }
            }
            dynamic dataArray = new JArray();
            foreach (var val in dataValues)
            {
                dataArray.Add(Convert.ToDecimal(val));
            }
            region.Add("gridData", dataArray);
            region.Add("chartData", dataArray);
            region.color = colorsForGraph[count.ToString()];
            region.Add("id", count);
            region.Add("parentId", null);
            count++;
            lst.Add(region);
        }
        if (otherReportingAreas != null && otherReportingAreas.Any())
        {
            decimal total = 0.0M;
            dynamic regionOthers = new JObject();
            regionOthers.Name = ((totalText.FirstOrDefault(v => v.Key == "cost_per_citizen_totals")).Value).LangText;
            regionOthers.number_type = format;
            dynamic dataArrayOthers = new JArray();
            foreach (var c in citiesDetails)
            {
                string regionCode = c.fk_region_code;
                foreach (var r in otherReportingAreas)
                {
                    string reportAreaCode = r.Key;
                    bool hasValue;
                    hasValue = lstDataAfterSort.Any(x => x.fk_region_code == regionCode && x.fk_report_area_code == reportAreaCode);
                    if (hasValue)
                    {
                        if (type.ToLower() == "percentage" || adjustForExpenseNeed == false)
                        {
                            var val = (lstDataAfterSort.FirstOrDefault(x => x.fk_region_code == regionCode && x.fk_report_area_code == reportAreaCode)).indicator_value;
                            total = total + Convert.ToDecimal(val);
                        }
                        else
                        {
                            var val = (lstDataAfterSort.FirstOrDefault(x => x.fk_region_code == regionCode && x.fk_report_area_code == reportAreaCode)).expence_adjusted_value;
                            total = total + Convert.ToDecimal(val);
                        }
                    }
                }
                dataArrayOthers.Add(total);
                total = 0.0M;
            }
            regionOthers.Add("gridData", dataArrayOthers);
            regionOthers.Add("chartData", dataArrayOthers);
            regionOthers.color = colorsForGraph[count.ToString()];
            regionOthers.Add("id", 1001);
            regionOthers.Add("parentId", null);
            lst.Add(regionOthers);
        }
        count++;
        decimal totalRow = 0.0M;
        dynamic totals = new JObject();
        totals.Name = ((totalText.FirstOrDefault(v => v.Key == "cmn_title_total")).Value).LangText;
        totals.number_type = format;
        dynamic dataArrayTotals = new JArray();
        foreach (var c in citiesDetails)
        {
            string regionCode = c.fk_region_code;
            foreach (var r in reportingAreas)
            {
                string reportAreaCode = r.Key;
                bool hasValue;
                hasValue = lstDataAfterSort.Any(x => x.fk_region_code == regionCode && x.fk_report_area_code == reportAreaCode);
                if (hasValue)
                {
                    if (type.ToLower() == "percentage" || adjustForExpenseNeed == false)
                    {
                        var val = (lstDataAfterSort.FirstOrDefault(x => x.fk_region_code == regionCode && x.fk_report_area_code == reportAreaCode)).indicator_value;
                        totalRow = totalRow + Convert.ToDecimal(val);
                    }
                    else
                    {
                        var val = (lstDataAfterSort.FirstOrDefault(x => x.fk_region_code == regionCode && x.fk_report_area_code == reportAreaCode)).expence_adjusted_value;
                        totalRow = totalRow + Convert.ToDecimal(val);
                    }
                }
            }
            dataArrayTotals.Add(totalRow);
            totalRow = 0.0M;
        }
        totals.Add("gridData", dataArrayTotals);
        totals.color = null;
        totals.Add("id", 1000);
        totals.Add("parentId", null);
        lst.Add(totals);

        costPerCitizen.Add("jsonData", lst);
        dynamic labelArray = new JArray() { dictLables.Values };
        costPerCitizen.Add("Labels", labelArray);

        dynamic titlesArray = new JArray() { " ", " ", " ", dictLables.Values };
        costPerCitizen.Add("Titles", titlesArray);

        dynamic fieldsArray = new JArray();
        fieldsArray.Add("id");
        fieldsArray.Add("parentId");
        fieldsArray.Add("name");
        int fieldCount = 1;
        foreach (string s in dictLables.Keys)
        {
            fieldsArray.Add("t" + fieldCount.ToString());
            fieldCount++;
        }

        costPerCitizen.Add("Fields", fieldsArray);

        dynamic backGroundIndex = new JArray() { averagesIndex };
        costPerCitizen.Add("backGroundIndex", backGroundIndex);

        costPerCitizen.costType = type;
        costPerCitizen.chartConfig = await GetCPCChartConfigDataAsync();
        costPerCitizen.gridConfig = JObject.Parse(authenticationDbContext.gco_application_settings.FirstOrDefault(x => x.Key == "GetCostPerCitizen_grid_config").Value);

        ////////////////////////////////////////////////////////////
        dynamic lstOther = new JArray();
        int inneritemcount = 1111;
        if (otherReportingAreas != null && otherReportingAreas.Any())
        {
            foreach (var r in otherReportingAreas)
            {
                dynamic region = new JObject();
                region.Name = r.Value;
                region.number_type = format;
                List<decimal> dataValues = new List<decimal>();
                foreach (var d in lstDataAfterSort)
                {
                    foreach (var c in citiesDetails)
                    {
                        if (d.fk_region_code == c.fk_region_code)
                        {
                            if (d.fk_report_area_code == r.Key)
                            {
                                if (type.ToLower() == "percentage" || adjustForExpenseNeed == false)
                                {
                                    dataValues.Add(Convert.ToDecimal(d.indicator_value));
                                }
                                else
                                {
                                    dataValues.Add(Convert.ToDecimal(d.expence_adjusted_value));
                                }
                            }
                        }
                    }
                }
                dynamic dataArray = new JArray();
                foreach (var val in dataValues)
                {
                    dataArray.Add(Convert.ToDecimal(val));
                }
                region.Add("gridData", dataArray);
                lstOther.Add(region);

                inneritemcount++;
                region.Add("id", inneritemcount);
                region.Add("parentId", 1001);
                lst.Add(region);
            }
        }
        costPerCitizen.Add("OtherData", lstOther);

        if (format == "n0")
        {
            costPerCitizen.yAxisTemplate = "#= (kendo.toString(value,'n0')) #";
        }
        else
        {
            costPerCitizen.yAxisTemplate = "#= (kendo.toString(value/100,'p1')) #";
        }

        return costPerCitizen;
    }



    public dynamic GetCostReductionPotentialData(string userId, CompFilter compFilter, clsConfig config)
    {
        var result = GetCostReductionPotentialDataAsync(userId, compFilter, config).GetAwaiter().GetResult();
        return result;
    }



    public async Task<dynamic> GetCostReductionPotentialDataAsync(string userId, CompFilter compFilter, clsConfig config)
    {
        TenantDBContext kostraDbContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        string kostraDataType = (await GetUserPreferencesAsync(userId))["kostra_data_preference"];
        List<dynamic> lstData = new List<dynamic>();
        short budgetYear = Convert.ToInt16((await GetBudgetYearAsync(userId)));
        gco_tenants tenantDetails = await kostraDbContext.gco_tenants.FirstOrDefaultAsync(x => x.pk_id == userDetails.tenant_id);

        Dictionary<string, string> lstComparingCites = GetComparableCities(compFilter.Data, userId);
        lstComparingCites = lstComparingCites.Where(x => !x.Key.StartsWith("E")).ToDictionary(x => x.Key, x => x.Value);

        List<clsKostraCrpData> reportingAreasAndIndicators = new List<clsKostraCrpData>();
        var comparableCitiesData = (from k in lstData
            select new
            {
                fk_region_code = string.Empty,
                fk_indicator_code = string.Empty,
                indicator_value = 0.0M,
                expence_adjusted_value = 0.0M,
                year = budgetYear
            }).ToList();
        List<string> lstIndicators = (from v in reportingAreasAndIndicators
            select v.fk_indicator_code).Distinct().ToList();

        if (kostraDataType.ToLower() == "city")
        {
            reportingAreasAndIndicators = (await (from cr in kostraDbContext.gko_cost_reduction_data
                    //join t in kostraDbContext.gco_tenants on cr.fk_municipality_id equals t.municipality_id
                    join r in kostraDbContext.gmd_reporting_areas on cr.fk_report_area_code equals r
                        .pk_report_area_code
                    join i in kostraDbContext.gmd_kostra_lables on cr.fk_indicator_code equals i.pk_indicator_code
                    join m in kostraDbContext.gco_municipalities on new { a = cr.fk_municipality_id }
                        equals new { a = m.pk_municipality_id }
                    join k in kostraDbContext.gko_kostra_data on new
                            { a = m.pk_municipality_id, b = cr.fk_indicator_code }
                        equals new { a = k.fk_region_code, b = k.fk_indicator_code }
                    join kf in kostraDbContext.gmd_kostra_function on cr.fk_kostra_function_code equals kf
                        .pk_kostra_function_code into kf2
                    from kf3 in kf2.DefaultIfEmpty()
                    where (cr.fk_municipality_id == tenantDetails.municipality_id && k.year == (budgetYear - 1)
                        && cr.type == kostraDataType.ToLower())
                    select new clsKostraCrpData
                    {
                        id = null,
                        parentId = null,
                        fk_report_area_code = cr.fk_report_area_code,
                        fk_kostra_function_code = cr.fk_kostra_function_code,
                        kostra_function_name = kf3.kostra_function_name,
                        report_area_name = r.report_area_name,
                        fk_indicator_code = cr.fk_indicator_code,
                        indicator_description = i.indicator_description,
                        age_group = cr.age_group,
                        bestin_kostra_group = cr.bestin_kostra_group,
                        bestin_kostra_value = cr.bestin_kostra_value,
                        bestin_region_group = cr.bestin_region_group,
                        bestin_region_value = cr.bestin_region_value,
                        bestin_country_group = cr.bestin_country_group,
                        bestin_country_value = cr.bestin_country_value,
                        bestin_asss_group = cr.bestin_asss_group,
                        bestin_asss_value = cr.bestin_asss_value,
                        exp_bestin_kostra_group = cr.exp_bestin_kostra_group,
                        exp_bestin_kostra_value = cr.exp_bestin_kostra_value,
                        exp_average_kostra_value = cr.average_kostra_value,
                        exp_bestin_region_group = cr.exp_bestin_region_group,
                        exp_bestin_region_value = cr.exp_bestin_region_value,
                        exp_average_region_value = cr.average_region_value,
                        exp_bestin_country_group = cr.exp_bestin_country_group,
                        exp_bestin_country_value = cr.exp_bestin_country_value,
                        exp_average_country_value = cr.average_country_value,
                        exp_bestin_asss_group = cr.exp_bestin_asss_group,
                        exp_bestin_asss_value = cr.exp_bestin_asss_value,
                        exp_average_asss_value = cr.average_asss_value,
                        average_kostra_value = cr.average_kostra_value,
                        average_region_value = cr.average_region_value,
                        average_country_value = cr.average_country_value,
                        average_asss_value = cr.average_asss_value,
                        inhabitants = cr.inhabitants,
                        indicator_value = k.indicator_value,
                        municipality_name = m.municipality_name,
                        expence_adjusted_value = (k.indicator_value + k.exp_adj_value),
                        regionCode = k.fk_region_code,
                        average_Mun_EAK_adj = cr.average_Mun_EAK_Adj
                    }).ToListAsync()).OrderBy(x => x.fk_report_area_code).ThenBy(x => x.fk_kostra_function_code)
                .ToList();
            lstIndicators = (from v in reportingAreasAndIndicators
                select v.fk_indicator_code).Distinct().ToList();
            comparableCitiesData = await (from b in kostraDbContext.gko_kostra_data
                where (b.year == (budgetYear - 1) && lstComparingCites.Keys.Contains(b.fk_region_code) && lstIndicators.Contains(b.fk_indicator_code))
                select new
                {
                    b.fk_region_code,
                    b.fk_indicator_code,
                    b.indicator_value,
                    expence_adjusted_value = (b.indicator_value + b.exp_adj_value),
                    b.year
                }).ToListAsync();
        }
        else
        {
            reportingAreasAndIndicators = (await (from cr in kostraDbContext.gko_cost_reduction_data
                    //join t in kostraDbContext.gco_tenants on cr.fk_municipality_id equals t.municipality_id
                    join r in kostraDbContext.gmd_reporting_areas on cr.fk_report_area_code equals r
                        .pk_report_area_code
                    join i in kostraDbContext.gmd_kostra_lables on cr.fk_indicator_code equals i.pk_indicator_code
                    join m in kostraDbContext.gco_municipalities on new { a = cr.fk_municipality_id }
                        equals new { a = m.pk_municipality_id }
                    join k in kostraDbContext.gko_kostra_data_corp on new
                            { a = m.pk_municipality_id, b = cr.fk_indicator_code }
                        equals new { a = k.fk_region_code, b = k.fk_indicator_code }
                    join kf in kostraDbContext.gmd_kostra_function on cr.fk_kostra_function_code equals kf
                        .pk_kostra_function_code into kf2
                    from kf3 in kf2.DefaultIfEmpty()
                    where (cr.fk_municipality_id == tenantDetails.municipality_id && k.year == (budgetYear - 1)
                        && cr.type == kostraDataType.ToLower())
                    select new clsKostraCrpData
                    {
                        id = null,
                        parentId = null,
                        fk_report_area_code = cr.fk_report_area_code,
                        fk_kostra_function_code = cr.fk_kostra_function_code,
                        kostra_function_name = kf3.kostra_function_name,
                        report_area_name = r.report_area_name,
                        fk_indicator_code = cr.fk_indicator_code,
                        indicator_description = i.indicator_description,
                        age_group = cr.age_group,
                        bestin_kostra_group = cr.bestin_kostra_group,
                        bestin_kostra_value = cr.bestin_kostra_value,
                        bestin_region_group = cr.bestin_region_group,
                        bestin_region_value = cr.bestin_region_value,
                        bestin_country_group = cr.bestin_country_group,
                        bestin_country_value = cr.bestin_country_value,
                        bestin_asss_group = cr.bestin_asss_group,
                        bestin_asss_value = cr.bestin_asss_value,
                        exp_bestin_kostra_group = cr.exp_bestin_kostra_group,
                        exp_bestin_kostra_value = cr.exp_bestin_kostra_value,
                        exp_average_kostra_value = cr.exp_average_kostra_value,
                        exp_bestin_region_group = cr.exp_bestin_region_group,
                        exp_bestin_region_value = cr.exp_bestin_region_value,
                        exp_average_region_value = cr.exp_average_region_value,
                        exp_bestin_country_group = cr.exp_bestin_country_group,
                        exp_bestin_country_value = cr.exp_bestin_country_value,
                        exp_average_country_value = cr.exp_average_country_value,
                        exp_bestin_asss_group = cr.exp_bestin_asss_group,
                        exp_bestin_asss_value = cr.exp_bestin_asss_value,
                        exp_average_asss_value = cr.exp_average_asss_value,
                        average_kostra_value = cr.average_kostra_value,
                        average_region_value = cr.average_region_value,
                        average_country_value = cr.average_country_value,
                        average_asss_value = cr.average_asss_value,
                        inhabitants = cr.inhabitants,
                        indicator_value = k.indicator_value,
                        municipality_name = m.municipality_name,
                        expence_adjusted_value = (k.indicator_value + k.exp_adj_value),
                        regionCode = k.fk_region_code,
                        average_Mun_EAK_adj = cr.average_Mun_EAK_Adj
                    }).ToListAsync()).OrderBy(x => x.fk_report_area_code).ThenBy(x => x.fk_kostra_function_code)
                .ToList();
            lstIndicators = (from v in reportingAreasAndIndicators
                select v.fk_indicator_code).Distinct().ToList();
            comparableCitiesData = await (from b in kostraDbContext.gko_kostra_data_corp
                where (b.year == (budgetYear - 1) && lstComparingCites.Keys.Contains(b.fk_region_code) && lstIndicators.Contains(b.fk_indicator_code))
                select new
                {
                    b.fk_region_code,
                    b.fk_indicator_code,
                    b.indicator_value,
                    expence_adjusted_value = (b.indicator_value + b.exp_adj_value),
                    b.year
                }).ToListAsync();
        }

        int id = 1;
        int childId = 101;

        foreach (clsKostraCrpData d in reportingAreasAndIndicators)
        {
            if (string.IsNullOrEmpty(d.fk_kostra_function_code))
            {
                d.parentId = null;
                d.id = id;
                id++;
            }
            else
            {
                d.parentId = reportingAreasAndIndicators.FirstOrDefault(x => x.fk_report_area_code == d.fk_report_area_code && (x.fk_kostra_function_code == string.Empty || x.fk_kostra_function_code == "")).id.Value;
                d.id = childId;
                childId++;
            }
        }

        Dictionary<string, clsLanguageString> gridHeaders = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "KostraMain");
        string reportingArea = ((gridHeaders.FirstOrDefault(v => v.Key == "kmp_cost_reduction_reporting_area_title")).Value).LangText;
        string ageGroup = ((gridHeaders.FirstOrDefault(v => v.Key == "kmp_cost_reduction_age_group_title")).Value).LangText;
        string indicatorDescription = ((gridHeaders.FirstOrDefault(v => v.Key == "kmp_cost_reduction_Indikator_title")).Value).LangText;

        string kostraGroup = ((gridHeaders.FirstOrDefault(v => v.Key == "kmp_cost_reduction_kostra_group_title")).Value).LangText;
        string kostraValue = ((gridHeaders.FirstOrDefault(v => v.Key == "kmp_cost_reduction_kostra_value_title")).Value).LangText;

        string regionGroup = ((gridHeaders.FirstOrDefault(v => v.Key == "kmp_cost_reduction_region_group_title")).Value).LangText;
        string regionValue = ((gridHeaders.FirstOrDefault(v => v.Key == "kmp_cost_reduction_region_value_title")).Value).LangText;

        string countryGroup = ((gridHeaders.FirstOrDefault(v => v.Key == "kmp_cost_reduction_country_group_title")).Value).LangText;
        string countryValue = ((gridHeaders.FirstOrDefault(v => v.Key == "kmp_cost_reduction_country_value_title")).Value).LangText;

        string asssGroup = ((gridHeaders.FirstOrDefault(v => v.Key == "kmp_cost_reduction_asss_group_title")).Value).LangText;
        string asssValue = ((gridHeaders.FirstOrDefault(v => v.Key == "kmp_cost_reduction_asss_value_title")).Value).LangText;

        string avgCities = ((gridHeaders.FirstOrDefault(v => v.Key == "kmp_cost_reduction_avg_cities_title")).Value).LangText;
        string avgGroups = ((gridHeaders.FirstOrDefault(v => v.Key == "kmp_cost_reduction_avg_groups_title")).Value).LangText;
        string avgMunicipalities = ((gridHeaders.FirstOrDefault(v => v.Key == "kmp_cost_reduction_avg_Municipalities_title")).Value).LangText;

        string inhabitants = ((gridHeaders.FirstOrDefault(v => v.Key == "kmp_cost_reduction_inhabitants_title")).Value).LangText;
        string navigationKr = ((gridHeaders.FirstOrDefault(v => v.Key == "kmp_cost_reduction_available_navigation_kr_title")).Value).LangText;
        string navigationPercentage = ((gridHeaders.FirstOrDefault(v => v.Key == "kmp_cost_reduction_available_navigation_%_title")).Value).LangText;

        dynamic lst = new JObject();
        dynamic jsonData = new JArray();
        dynamic lowestValueIndices = new JArray();

        bool displayAverageOfCities = false;
        decimal averageOfCities = 0;
        bool displayAverageOfGroups = false;
        decimal averageOfGroups = 0;
        bool displayAverageMunicipalityEAKAdj = false;
        decimal AverageMunicipalityEAKAdj = 0;

        string numberType;
        numberType = "amount";
        string oneDecimal = "dec1";
        Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "NumberFormats");
        string numberFormat = ((langStringValues.FirstOrDefault(v => v.Key == numberType)).Value).LangText;
        string oneDecimalFormat = ((langStringValues.FirstOrDefault(v => v.Key == oneDecimal)).Value).LangText;
        decimal min_prod_index = Convert.ToDecimal(await _utility.GetApplicationSettingAsync("min_prod_index"), CultureInfo.InvariantCulture);
        decimal max_prod_index = Convert.ToDecimal(await _utility.GetApplicationSettingAsync("max_prod_index"), CultureInfo.InvariantCulture);
        List<gko_prod_indicators> prodIndicators = await GetProdIndicators(userId);

        List<ClsKostraCrpParentRowDetails> lstParentRowMinimumValAndIndexDetails = new List<ClsKostraCrpParentRowDetails>();
        foreach (var v in reportingAreasAndIndicators)
        {
            dynamic row = new JObject();
            row.report_area_name = v.parentId == null ? v.report_area_name : (v.fk_kostra_function_code + " " + v.kostra_function_name);
            row.age_group = v.age_group;
            row.indicator_code = v.fk_indicator_code;
            row.indicator_description = v.indicator_description;
            row.format = numberFormat;
            row.oneDecimalFormat = oneDecimalFormat;
            row.id = v.id;
            row.parentId = v.parentId;
            row.report_area_code = v.fk_report_area_code;
            decimal compValue = 0;
            dynamic gridData = new JArray();
            dynamic regionCode = new JArray();
            dynamic indicatorStatus = new JArray();

            var currentMunicipalityWithExpenseNeed = 0.0M;
            var currentMunicipalityWithoutExpenseNeed = 0.0M;

            currentMunicipalityWithExpenseNeed = v.expence_adjusted_value;

            currentMunicipalityWithoutExpenseNeed = v.indicator_value;

            regionCode.Add(v.regionCode);
            if (prodIndicators.FirstOrDefault(x => x.fk_region_code == v.regionCode && x.fk_report_area_code == v.fk_report_area_code) != null)
            {
                compValue = prodIndicators.FirstOrDefault(x => x.fk_region_code == v.regionCode && x.fk_report_area_code == v.fk_report_area_code).total_index_value;
                indicatorStatus.Add(GetIndicatorStatus(min_prod_index, max_prod_index, compValue));
            }
            else
            {
                indicatorStatus.Add("");
            }
            if (config.sectionConfig.adjustmentSetup.adjustmentForExpenseNeed && config.sectionConfig.calculateCostRedPotential.municipalityAdjustedValue == false)
            {
                gridData.Add(Math.Round(v.expence_adjusted_value, 0));
            }
            else
            {
                gridData.Add(Math.Round(v.indicator_value, 0));
            }
            Dictionary<int, decimal> lowestValueWithIndex = new Dictionary<int, decimal>();

            int indexCount = 5;
            decimal citiesTotalAmount = 0;
            foreach (string s in lstComparingCites.Keys.ToList())
            {
                decimal value = 0.0M;
                if (config.sectionConfig.adjustmentSetup.adjustmentForExpenseNeed)
                {
                    if (comparableCitiesData.FirstOrDefault(x => x.fk_indicator_code == v.fk_indicator_code && x.year == (budgetYear - 1) && x.fk_region_code == s) != null)
                    {
                        value = Math.Round((comparableCitiesData.FirstOrDefault(x => x.fk_indicator_code == v.fk_indicator_code && x.year == (budgetYear - 1) && x.fk_region_code == s)).expence_adjusted_value, 0);
                        citiesTotalAmount = citiesTotalAmount + value;
                    }
                }
                else
                {
                    if ((comparableCitiesData.FirstOrDefault(x => x.fk_indicator_code == v.fk_indicator_code && x.year == (budgetYear - 1) && x.fk_region_code == s)) != null)
                    {
                        value = Math.Round((comparableCitiesData.FirstOrDefault(x => x.fk_indicator_code == v.fk_indicator_code && x.year == (budgetYear - 1) && x.fk_region_code == s)).indicator_value, 0);
                        citiesTotalAmount = citiesTotalAmount + value;
                    }
                }
                gridData.Add(value);
                regionCode.Add(s);
                if (prodIndicators.FirstOrDefault(x => x.fk_region_code == s && x.fk_report_area_code == v.fk_report_area_code) != null)
                {
                    compValue = prodIndicators.FirstOrDefault(x => x.fk_region_code == s && x.fk_report_area_code == v.fk_report_area_code).total_index_value;
                    indicatorStatus.Add(GetIndicatorStatus(min_prod_index, max_prod_index, compValue));
                }
                else
                {
                    indicatorStatus.Add("");
                }

                lowestValueWithIndex.Add(indexCount, value);
                indexCount++;
            }
            indexCount = 5;
            decimal column10Data = 0.0M;
            decimal column11Data = 0.0M;

            if (config.sectionConfig.calculateCostRedPotential.lowestInCities)
            {
                if (lowestValueWithIndex.Where(z => z.Value > 0.0M).ToList().Any())
                {
                    List<KeyValuePair<int, decimal>> data = lowestValueWithIndex.Where(z => z.Value > 0.0M).OrderBy(x => x.Value).ToList();

                    decimal dataForCalculation = 0.0M;

                    if (row.parentId == null)
                    {
                        lstParentRowMinimumValAndIndexDetails.Add(new ClsKostraCrpParentRowDetails
                        {
                            id = row.id,
                            parentId = row.ParentId,
                            val = (data.First()).Value,
                            index = (data.First()).Key
                        });
                        lowestValueIndices.Add((data.First()).Key);
                        dataForCalculation = data.First().Value;
                    }
                    else
                    {
                        int currentRowParentId = Convert.ToInt32(row.parentId);
                        lowestValueIndices.Add(lstParentRowMinimumValAndIndexDetails.FirstOrDefault(x => x.id == currentRowParentId) == null ? -1 :
                            lstParentRowMinimumValAndIndexDetails.FirstOrDefault(x => x.id == currentRowParentId).index);
                        dataForCalculation = lstParentRowMinimumValAndIndexDetails.FirstOrDefault(x => x.id == currentRowParentId) == null ? 0 :
                            lowestValueWithIndex[lstParentRowMinimumValAndIndexDetails.FirstOrDefault(x => x.id == currentRowParentId).index];
                    }

                    if (config.sectionConfig.adjustmentSetup.adjustmentForExpenseNeed)
                    {
                        column10Data = lstComparingCites.Count() == 0 ? 0 : (Math.Round((v.expence_adjusted_value), 0) - dataForCalculation) * v.inhabitants;
                        column10Data = lstComparingCites.Count() == 0 ? 0 : currentMunicipalityWithExpenseNeed == 0 ? column10Data : column10Data * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed;
                        if (v.expence_adjusted_value > 0)
                        {
                            column11Data = lstComparingCites.Count() == 0 ? 0 : currentMunicipalityWithExpenseNeed == 0 ? 0 : (((Math.Round((v.expence_adjusted_value), 1) - dataForCalculation) / (v.expence_adjusted_value)) * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed) * 100;
                        }
                    }
                    else
                    {
                        column10Data = lstComparingCites.Count() == 0 ? 0 : (Math.Round((v.indicator_value), 0) - dataForCalculation) * v.inhabitants;
                        if (v.indicator_value > 0)
                        {
                            column11Data = lstComparingCites.Count() == 0 ? 0 : ((Math.Round((v.indicator_value), 1) - dataForCalculation) / (v.indicator_value)) * 100;
                        }
                    }
                }
                else
                {
                    lowestValueIndices.Add(-1);
                    if (config.sectionConfig.adjustmentSetup.adjustmentForExpenseNeed)
                    {
                        column10Data = lstComparingCites.Count() == 0 ? 0 : (Math.Round((v.expence_adjusted_value), 0) - 0) * v.inhabitants;
                        column10Data = lstComparingCites.Count() == 0 ? 0 : currentMunicipalityWithExpenseNeed == 0 ? column10Data : column10Data * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed;
                        if (v.expence_adjusted_value > 0)
                        {
                            column11Data = lstComparingCites.Count() == 0 ? 0 : currentMunicipalityWithExpenseNeed == 0 ? 0 : (((Math.Round((v.expence_adjusted_value), 1) - 0) / (v.expence_adjusted_value)) * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed) * 100;
                        }
                    }
                    else
                    {
                        column10Data = lstComparingCites.Count() == 0 ? 0 : (Math.Round((v.indicator_value), 0) - 0) * v.inhabitants;
                        if (v.indicator_value > 0)
                        {
                            column11Data = lstComparingCites.Count() == 0 ? 0 : ((Math.Round((v.indicator_value), 1) - 0) / (v.indicator_value)) * 100;
                        }
                    }
                }
            }
            else if (config.sectionConfig.calculateCostRedPotential.averageOfCities)
            {
                if (config.sectionConfig.adjustmentSetup.adjustmentForExpenseNeed)
                {
                    if (lstComparingCites.Count > 0)
                    {
                        column10Data = (Math.Round((v.expence_adjusted_value), 0) - Math.Round((citiesTotalAmount / lstComparingCites.Count), 2)) * v.inhabitants;
                        column10Data = currentMunicipalityWithExpenseNeed == 0 ? column10Data : column10Data * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed;
                    }

                    if (v.expence_adjusted_value > 0)
                    {
                        if (lstComparingCites.Count > 0)
                        {
                            column11Data = currentMunicipalityWithExpenseNeed == 0 ? 0 : (((Math.Round((v.expence_adjusted_value), 1) - (citiesTotalAmount / lstComparingCites.Count)) / (v.expence_adjusted_value)) * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed) * 100;
                        }
                    }
                    averageOfCities = lstComparingCites.Count > 0 ? Math.Round((citiesTotalAmount / lstComparingCites.Count), 1) : 0;
                }
                else
                {
                    if (lstComparingCites.Count > 0)
                    {
                        column10Data = (Math.Round((v.indicator_value), 0) - Math.Round((citiesTotalAmount / lstComparingCites.Count), 2)) * v.inhabitants;
                    }
                    if (v.indicator_value > 0)
                    {
                        if (lstComparingCites.Count > 0)
                        {
                            column11Data = ((Math.Round((v.indicator_value), 1) - (citiesTotalAmount / lstComparingCites.Count)) / (v.indicator_value)) * 100;
                        }
                    }
                    averageOfCities = lstComparingCites.Count > 0 ? Math.Round((citiesTotalAmount / lstComparingCites.Count), 1) : 0;
                }
            }
            else if (config.sectionConfig.calculateCostRedPotential.averageOfGroups)
            {
                if (config.sectionConfig.adjustmentSetup.adjustmentForExpenseNeed)
                {
                    if (config.sectionConfig.costReductionpottentialGroups.bestInKostra)
                    {
                        column10Data = (Math.Round((v.expence_adjusted_value), 0) - Math.Round((v.exp_average_kostra_value), 0)) * v.inhabitants;
                        column10Data = currentMunicipalityWithExpenseNeed == 0 ? column10Data : column10Data * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed;
                        if (v.expence_adjusted_value > 0)
                        {
                            column11Data = (((Math.Round((v.expence_adjusted_value), 1) - Math.Round((v.exp_average_kostra_value), 1)) / (v.expence_adjusted_value)) * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed) * 100;
                        }
                        averageOfGroups = Math.Round((v.exp_average_kostra_value), 0);
                    }
                    else if (config.sectionConfig.costReductionpottentialGroups.bestInRegion)
                    {
                        column10Data = (Math.Round((v.expence_adjusted_value), 0) - Math.Round((v.exp_average_region_value), 0)) * v.inhabitants;
                        column10Data = currentMunicipalityWithExpenseNeed == 0 ? column10Data : column10Data * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed;
                        if (v.expence_adjusted_value > 0)
                        {
                            column11Data = (((Math.Round((v.expence_adjusted_value), 1) - Math.Round((v.exp_average_region_value), 1)) / (v.expence_adjusted_value)) * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed) * 100;
                        }
                        averageOfGroups = Math.Round((v.exp_average_region_value), 0);
                    }
                    else if (config.sectionConfig.costReductionpottentialGroups.bestInCountry)
                    {
                        column10Data = (Math.Round((v.expence_adjusted_value), 0) - Math.Round((v.exp_average_country_value), 0)) * v.inhabitants;
                        column10Data = currentMunicipalityWithExpenseNeed == 0 ? column10Data : column10Data * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed;
                        if (v.expence_adjusted_value > 0)
                        {
                            column11Data = currentMunicipalityWithExpenseNeed == 0 ? 0 : (((Math.Round((v.expence_adjusted_value), 1) - Math.Round((v.exp_average_country_value), 1)) / (v.expence_adjusted_value)) * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed) * 100;
                        }
                        averageOfGroups = Math.Round((v.exp_average_country_value), 0);
                    }
                    else if (config.sectionConfig.costReductionpottentialGroups.bestInAsss)
                    {
                        column10Data = (Math.Round((v.expence_adjusted_value), 0) - Math.Round((v.exp_average_asss_value), 0)) * v.inhabitants;
                        column10Data = currentMunicipalityWithExpenseNeed == 0 ? column10Data : column10Data * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed;
                        if (v.expence_adjusted_value > 0)
                        {
                            column11Data = currentMunicipalityWithExpenseNeed == 0 ? 0 : (((Math.Round((v.expence_adjusted_value), 1) - Math.Round((v.exp_average_asss_value), 1)) / (v.expence_adjusted_value)) * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed) * 100;
                        }
                        averageOfGroups = Math.Round((v.exp_average_asss_value), 0);
                    }
                    else
                    {
                        column10Data = (Math.Round((v.expence_adjusted_value), 0) - Math.Round((v.exp_average_kostra_value), 0)) * v.inhabitants;
                        column10Data = currentMunicipalityWithExpenseNeed == 0 ? column10Data : column10Data * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed;
                        if (v.expence_adjusted_value > 0)
                        {
                            column11Data = currentMunicipalityWithExpenseNeed == 0 ? 0 : (((Math.Round((v.expence_adjusted_value), 1) - Math.Round((v.exp_average_kostra_value), 1)) / (v.expence_adjusted_value)) * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed) * 100;
                        }
                        averageOfGroups = Math.Round((v.exp_average_kostra_value), 0);
                    }
                }
                else
                {
                    if (config.sectionConfig.costReductionpottentialGroups.bestInKostra)
                    {
                        column10Data = (Math.Round((v.indicator_value), 0) - Math.Round((v.average_kostra_value), 0)) * v.inhabitants;
                        if (v.indicator_value > 0)
                        {
                            column11Data = ((Math.Round((v.indicator_value), 1) - Math.Round((v.average_kostra_value), 1)) / (v.indicator_value)) * 100;
                        }
                        averageOfGroups = Math.Round((v.average_kostra_value), 0);
                    }
                    else if (config.sectionConfig.costReductionpottentialGroups.bestInRegion)
                    {
                        column10Data = (Math.Round((v.indicator_value), 0) - Math.Round((v.average_region_value), 0)) * v.inhabitants;
                        if (v.indicator_value > 0)
                        {
                            column11Data = ((Math.Round((v.indicator_value), 1) - Math.Round((v.average_region_value), 1)) / (v.indicator_value)) * 100;
                        }
                        averageOfGroups = Math.Round((v.average_region_value), 0);
                    }
                    else if (config.sectionConfig.costReductionpottentialGroups.bestInCountry)
                    {
                        column10Data = (Math.Round((v.indicator_value), 0) - Math.Round((v.average_country_value), 0)) * v.inhabitants;
                        if (v.indicator_value > 0)
                        {
                            column11Data = ((Math.Round((v.indicator_value), 1) - Math.Round((v.average_country_value), 1)) / (v.indicator_value)) * 100;
                        }
                        averageOfGroups = Math.Round((v.average_country_value), 0);
                    }
                    else if (config.sectionConfig.costReductionpottentialGroups.bestInAsss)
                    {
                        column10Data = (Math.Round((v.indicator_value), 0) - Math.Round((v.average_asss_value), 0)) * v.inhabitants;
                        if (v.indicator_value > 0)
                        {
                            column11Data = ((Math.Round((v.indicator_value), 1) - Math.Round((v.average_asss_value), 1)) / (v.indicator_value)) * 100;
                        }
                        averageOfGroups = Math.Round((v.average_asss_value), 0);
                    }
                    else
                    {
                        column10Data = (Math.Round((v.indicator_value), 0) - Math.Round((v.average_kostra_value), 0)) * v.inhabitants;
                        if (v.indicator_value > 0)
                        {
                            column11Data = ((Math.Round((v.indicator_value), 1) - Math.Round((v.average_kostra_value), 1)) / (v.indicator_value)) * 100;
                        }
                        averageOfGroups = Math.Round((v.average_kostra_value), 0);
                    }
                }
            }
            else if (config.sectionConfig.calculateCostRedPotential.lowestInGroups)
            {
                if (config.sectionConfig.adjustmentSetup.adjustmentForExpenseNeed)
                {
                    if (config.sectionConfig.costReductionpottentialGroups.bestInKostra)
                    {
                        column10Data = (Math.Round((v.expence_adjusted_value), 0) - Math.Round((v.exp_bestin_kostra_value), 0)) * v.inhabitants;
                        column10Data = currentMunicipalityWithExpenseNeed == 0 ? column10Data : column10Data * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed;
                        if (v.expence_adjusted_value > 0)
                        {
                            column11Data = currentMunicipalityWithExpenseNeed == 0 ? 0 : (((Math.Round((v.expence_adjusted_value), 1) - Math.Round((v.exp_bestin_kostra_value), 1)) / (v.expence_adjusted_value)) * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed) * 100;
                        }
                    }
                    else if (config.sectionConfig.costReductionpottentialGroups.bestInRegion)
                    {
                        column10Data = (Math.Round((v.expence_adjusted_value), 0) - Math.Round((v.exp_bestin_region_value), 0)) * v.inhabitants;
                        column10Data = currentMunicipalityWithExpenseNeed == 0 ? column10Data : column10Data * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed;
                        if (v.expence_adjusted_value > 0)
                        {
                            column11Data = currentMunicipalityWithExpenseNeed == 0 ? 0 : (((Math.Round((v.expence_adjusted_value), 1) - Math.Round((v.exp_bestin_region_value), 1)) / (v.expence_adjusted_value)) * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed) * 100;
                        }
                    }
                    else if (config.sectionConfig.costReductionpottentialGroups.bestInCountry)
                    {
                        column10Data = (Math.Round((v.expence_adjusted_value), 0) - Math.Round((v.exp_bestin_country_value), 0)) * v.inhabitants;
                        column10Data = currentMunicipalityWithExpenseNeed == 0 ? column10Data : column10Data * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed;
                        if (v.expence_adjusted_value > 0)
                        {
                            column11Data = currentMunicipalityWithExpenseNeed == 0 ? 0 : (((Math.Round((v.expence_adjusted_value), 1) - Math.Round((v.exp_bestin_country_value), 1)) / (v.expence_adjusted_value)) * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed) * 100;
                        }
                    }
                    else if (config.sectionConfig.costReductionpottentialGroups.bestInAsss)
                    {
                        column10Data = (Math.Round((v.expence_adjusted_value), 0) - Math.Round((v.exp_bestin_asss_value), 0)) * v.inhabitants;
                        column10Data = currentMunicipalityWithExpenseNeed == 0 ? column10Data : column10Data * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed;
                        if (v.expence_adjusted_value > 0)
                        {
                            column11Data = currentMunicipalityWithExpenseNeed == 0 ? 0 : (((Math.Round((v.expence_adjusted_value), 1) - Math.Round((v.exp_bestin_asss_value), 1)) / (v.expence_adjusted_value)) * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed) * 100;
                        }
                    }
                    else
                    {
                        column10Data = (Math.Round((v.expence_adjusted_value), 0) - Math.Round((v.exp_bestin_kostra_value), 0)) * v.inhabitants;
                        column10Data = currentMunicipalityWithExpenseNeed == 0 ? column10Data : column10Data * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed;
                        if (v.expence_adjusted_value > 0)
                        {
                            column11Data = currentMunicipalityWithExpenseNeed == 0 ? 0 : (((Math.Round((v.expence_adjusted_value), 1) - Math.Round((v.exp_bestin_kostra_value), 1)) / (v.expence_adjusted_value)) * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed) * 100;
                        }
                    }
                }
                else
                {
                    if (config.sectionConfig.costReductionpottentialGroups.bestInKostra)
                    {
                        column10Data = (Math.Round((v.indicator_value), 0) - Math.Round((v.bestin_kostra_value), 0)) * v.inhabitants;
                        if (v.indicator_value > 0)
                        {
                            column11Data = ((Math.Round((v.indicator_value), 1) - Math.Round((v.bestin_kostra_value), 1)) / (v.indicator_value)) * 100;
                        }
                    }
                    else if (config.sectionConfig.costReductionpottentialGroups.bestInRegion)
                    {
                        column10Data = (Math.Round((v.indicator_value), 0) - Math.Round((v.bestin_region_value), 0)) * v.inhabitants;
                        if (v.indicator_value > 0)
                        {
                            column11Data = ((Math.Round((v.indicator_value), 1) - Math.Round((v.bestin_region_value), 1)) / (v.indicator_value)) * 100;
                        }
                    }
                    else if (config.sectionConfig.costReductionpottentialGroups.bestInCountry)
                    {
                        column10Data = (Math.Round((v.indicator_value), 0) - Math.Round((v.bestin_country_value), 0)) * v.inhabitants;
                        if (v.indicator_value > 0)
                        {
                            column11Data = ((Math.Round((v.indicator_value), 1) - Math.Round((v.bestin_country_value), 1)) / (v.indicator_value)) * 100;
                        }
                    }
                    else if (config.sectionConfig.costReductionpottentialGroups.bestInAsss)
                    {
                        column10Data = (Math.Round((v.indicator_value), 0) - Math.Round((v.bestin_asss_value), 0)) * v.inhabitants;
                        if (v.indicator_value > 0)
                        {
                            column11Data = ((Math.Round((v.indicator_value), 1) - Math.Round((v.bestin_asss_value), 1)) / (v.indicator_value)) * 100;
                        }
                    }
                    else
                    {
                        column10Data = (Math.Round((v.indicator_value), 0) - Math.Round((v.bestin_kostra_value), 0)) * v.inhabitants;
                        if (v.indicator_value > 0)
                        {
                            column11Data = ((Math.Round((v.indicator_value), 1) - Math.Round((v.bestin_kostra_value), 1)) / (v.indicator_value)) * 100;
                        }
                    }
                }
            }
            else if (config.sectionConfig.calculateCostRedPotential.municipalityAdjustedValue)
            {
                column10Data = (Math.Round((v.indicator_value), 0) - Math.Round((v.average_Mun_EAK_adj), 0)) * v.inhabitants;
                if (v.indicator_value > 0)
                {
                    column11Data = ((Math.Round((v.indicator_value), 1) - Math.Round((v.average_Mun_EAK_adj), 1)) / (v.indicator_value)) * 100;
                }
                AverageMunicipalityEAKAdj = Math.Round((v.average_Mun_EAK_adj), 0);
            }
            else
            {
                if (lstComparingCites.Any())
                {
                    if (lowestValueWithIndex.Where(z => z.Value > 0.0M).ToList().Any())
                    {
                        List<KeyValuePair<int, decimal>> data = lowestValueWithIndex.Where(z => z.Value > 0.0M).OrderBy(x => x.Value).ToList();

                        decimal dataForCalculation = 0.0M;

                        if (row.parentId == null)
                        {
                            lstParentRowMinimumValAndIndexDetails.Add(new ClsKostraCrpParentRowDetails
                            {
                                id = row.id,
                                parentId = row.ParentId,
                                val = (data.First()).Value,
                                index = (data.First()).Key
                            });
                            lowestValueIndices.Add((data.First()).Key);
                            dataForCalculation = data.First().Value;
                        }
                        else
                        {
                            int currentRowParentId = Convert.ToInt32(row.parentId);
                            lowestValueIndices.Add(lstParentRowMinimumValAndIndexDetails.FirstOrDefault(x => x.id == currentRowParentId) == null ? -1 :
                                lstParentRowMinimumValAndIndexDetails.FirstOrDefault(x => x.id == currentRowParentId).index);
                            dataForCalculation = lstParentRowMinimumValAndIndexDetails.FirstOrDefault(x => x.id == currentRowParentId) == null ? 0 :
                                lowestValueWithIndex[lstParentRowMinimumValAndIndexDetails.FirstOrDefault(x => x.id == currentRowParentId).index];
                        }

                        if (config.sectionConfig.adjustmentSetup.adjustmentForExpenseNeed)
                        {
                            column10Data = (Math.Round((v.expence_adjusted_value), 0) - dataForCalculation) * v.inhabitants;
                            column10Data = currentMunicipalityWithExpenseNeed == 0 ? column10Data : column10Data * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed;
                            if (v.expence_adjusted_value > 0)
                            {
                                column11Data = currentMunicipalityWithExpenseNeed == 0 ? 0 : (((Math.Round((v.expence_adjusted_value), 1) - dataForCalculation) / (v.expence_adjusted_value)) * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed) * 100;
                            }
                        }
                        else
                        {
                            column10Data = (Math.Round((v.indicator_value), 0) - dataForCalculation) * v.inhabitants;
                            if (v.indicator_value > 0)
                            {
                                column11Data = ((Math.Round((v.indicator_value), 1) - dataForCalculation) / (v.indicator_value)) * 100;
                            }
                        }
                    }
                    else
                    {
                        lowestValueIndices.Add(-1);
                        if (config.sectionConfig.adjustmentSetup.adjustmentForExpenseNeed)
                        {
                            column10Data = (Math.Round((v.expence_adjusted_value), 0) - 0) * v.inhabitants;
                            column10Data = currentMunicipalityWithExpenseNeed == 0 ? column10Data : column10Data * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed;
                            if (v.expence_adjusted_value > 0)
                            {
                                column11Data = currentMunicipalityWithExpenseNeed == 0 ? 0 : (((Math.Round((v.expence_adjusted_value), 1) - 0) / (v.expence_adjusted_value)) * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed) * 100;
                            }
                        }
                        else
                        {
                            column10Data = (Math.Round((v.indicator_value), 0) - 0) * v.inhabitants;
                            if (v.indicator_value > 0)
                            {
                                column11Data = ((Math.Round((v.indicator_value), 1) - 0) / (v.indicator_value)) * 100;
                            }
                        }
                    }
                }
                else
                {
                    if (config.sectionConfig.adjustmentSetup.adjustmentForExpenseNeed)
                    {
                        column10Data = (Math.Round((v.expence_adjusted_value), 0) - v.exp_bestin_kostra_value) * v.inhabitants;
                        column10Data = currentMunicipalityWithExpenseNeed == 0 ? column10Data : column10Data * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed;
                        if (v.expence_adjusted_value > 0)
                        {
                            column11Data = currentMunicipalityWithExpenseNeed == 0 ? 0 : (((Math.Round((v.expence_adjusted_value), 1) - v.exp_bestin_kostra_value) / (v.expence_adjusted_value)) * currentMunicipalityWithoutExpenseNeed / currentMunicipalityWithExpenseNeed) * 100;
                        }
                    }
                    else
                    {
                        column10Data = (Math.Round((v.indicator_value), 0) - v.bestin_kostra_value) * v.inhabitants;
                        if (v.indicator_value > 0)
                        {
                            column11Data = ((Math.Round((v.indicator_value), 1) - v.bestin_kostra_value) / (v.indicator_value)) * 100;
                        }
                    }
                }
            }

            if (config.sectionConfig.adjustmentSetup.adjustmentForExpenseNeed)
            {
                if (config.sectionConfig.costReductionpottentialGroups.bestInKostra)
                {
                    gridData.Add(v.exp_bestin_kostra_group);
                    gridData.Add(Math.Round((v.exp_bestin_kostra_value), 0));
                    indicatorStatus.Add("");
                    indicatorStatus.Add("");
                    regionCode.Add("");
                    regionCode.Add("");
                }
                else if (config.sectionConfig.costReductionpottentialGroups.bestInRegion)
                {
                    gridData.Add(v.exp_bestin_region_group);
                    gridData.Add(Math.Round((v.exp_bestin_region_value), 0));
                    indicatorStatus.Add("");
                    indicatorStatus.Add("");
                    regionCode.Add("");
                    regionCode.Add("");
                }
                else if (config.sectionConfig.costReductionpottentialGroups.bestInCountry)
                {
                    gridData.Add(v.exp_bestin_country_group);
                    gridData.Add(Math.Round((v.exp_bestin_country_value), 0));
                    indicatorStatus.Add("");
                    indicatorStatus.Add("");
                    regionCode.Add("");
                    regionCode.Add("");
                }
                else if (config.sectionConfig.costReductionpottentialGroups.bestInAsss)
                {
                    gridData.Add(v.exp_bestin_asss_group);
                    gridData.Add(Math.Round((v.exp_bestin_asss_value), 0));
                    indicatorStatus.Add("");
                    indicatorStatus.Add("");
                    regionCode.Add("");
                    regionCode.Add("");
                }
                else
                {
                    gridData.Add(v.exp_bestin_kostra_group);
                    gridData.Add(Math.Round((v.exp_bestin_kostra_value), 0));
                    indicatorStatus.Add("");
                    indicatorStatus.Add("");
                    regionCode.Add("");
                    regionCode.Add("");
                }
            }
            else
            {
                if (config.sectionConfig.costReductionpottentialGroups.bestInKostra)
                {
                    gridData.Add(v.bestin_kostra_group);
                    gridData.Add(Math.Round((v.bestin_kostra_value), 0));
                    indicatorStatus.Add("");
                    indicatorStatus.Add("");
                    regionCode.Add("");
                    regionCode.Add("");
                }
                else if (config.sectionConfig.costReductionpottentialGroups.bestInRegion)
                {
                    gridData.Add(v.bestin_region_group);
                    gridData.Add(Math.Round((v.bestin_region_value), 0));
                    indicatorStatus.Add("");
                    indicatorStatus.Add("");
                    regionCode.Add("");
                    regionCode.Add("");
                }
                else if (config.sectionConfig.costReductionpottentialGroups.bestInCountry)
                {
                    gridData.Add(v.bestin_country_group);
                    gridData.Add(Math.Round((v.bestin_country_value), 0));
                    indicatorStatus.Add("");
                    indicatorStatus.Add("");
                    regionCode.Add("");
                    regionCode.Add("");
                }
                else if (config.sectionConfig.costReductionpottentialGroups.bestInAsss)
                {
                    gridData.Add(v.bestin_asss_group);
                    gridData.Add(Math.Round((v.bestin_asss_value), 0));
                    indicatorStatus.Add("");
                    indicatorStatus.Add("");
                    regionCode.Add("");
                    regionCode.Add("");
                }
                else
                {
                    gridData.Add(v.bestin_kostra_group);
                    gridData.Add(Math.Round((v.bestin_kostra_value), 0));
                    indicatorStatus.Add("");
                    indicatorStatus.Add("");
                    regionCode.Add("");
                    regionCode.Add("");
                }
            }

            /* Column if the Averages are selected*/

            if (config.sectionConfig.calculateCostRedPotential.averageOfCities)
            {
                gridData.Add(averageOfCities);
                indicatorStatus.Add("");
                regionCode.Add("");
                displayAverageOfCities = true;
            }
            else if (config.sectionConfig.calculateCostRedPotential.averageOfGroups)
            {
                gridData.Add(averageOfGroups);
                indicatorStatus.Add("");
                regionCode.Add("");
                displayAverageOfGroups = true;
            }
            else if (config.sectionConfig.calculateCostRedPotential.municipalityAdjustedValue)
            {
                gridData.Add(AverageMunicipalityEAKAdj);
                indicatorStatus.Add("");
                regionCode.Add("");
                displayAverageMunicipalityEAKAdj = true;
            }

            gridData.Add((v.inhabitants));
            indicatorStatus.Add("");
            regionCode.Add("");

            if (config.sectionConfig.crpDisplayNegative)
            {
                gridData.Add(Math.Round((column10Data / 1000000), 1));
                gridData.Add(Math.Round(column11Data, 1));
                indicatorStatus.Add("");
                indicatorStatus.Add("");
                regionCode.Add("");
                regionCode.Add("");
            }
            else
            {
                if (column10Data < 0)
                {
                    gridData.Add(0);
                    indicatorStatus.Add("");
                    regionCode.Add("");
                }
                else
                {
                    gridData.Add(Math.Round((column10Data / 1000000), 1));
                    indicatorStatus.Add("");
                    regionCode.Add("");
                }

                if (column11Data < 0)
                {
                    gridData.Add(0);
                    indicatorStatus.Add("");
                    regionCode.Add("");
                }
                else
                {
                    gridData.Add(Math.Round(column11Data, 1));
                    indicatorStatus.Add("");
                    regionCode.Add("");
                }
            }

            row.Add("gridData", gridData);
            row.Add("indicatorStatus", indicatorStatus);
            row.Add("RegionCode", regionCode);
            jsonData.Add(row);
        }
        lst.Add("jsonData", jsonData);
        lst.Add("lowestValueIndices", lowestValueIndices);

        dynamic titles = new JArray();
        titles.Add(" ");
        titles.Add(" ");
        titles.Add(reportingArea);
        titles.Add(ageGroup);
        titles.Add(" ");
        titles.Add(" ");
        titles.Add(indicatorDescription);
        titles.Add((await kostraDbContext.gco_region_codes.FirstOrDefaultAsync(x => x.pk_region_code == tenantDetails.municipality_id)).region_name);
        titles.Add(lstComparingCites.Values.ToList());
        if (config.sectionConfig.costReductionpottentialGroups.bestInKostra)
        {
            titles.Add(kostraGroup);
            titles.Add(kostraValue);
        }
        else if (config.sectionConfig.costReductionpottentialGroups.bestInRegion)
        {
            titles.Add(regionGroup);
            titles.Add(regionValue);
        }
        else if (config.sectionConfig.costReductionpottentialGroups.bestInCountry)
        {
            titles.Add(countryGroup);
            titles.Add(countryValue);
        }
        else if (config.sectionConfig.costReductionpottentialGroups.bestInAsss)
        {
            titles.Add(asssGroup);
            titles.Add(asssValue);
        }
        else
        {
            titles.Add(kostraGroup);
            titles.Add(kostraValue);
        }

        if (displayAverageOfCities)
        {
            titles.Add(avgCities);
        }
        else if (displayAverageOfGroups)
        {
            titles.Add(avgGroups);
        }
        else if (displayAverageMunicipalityEAKAdj)
        {
            titles.Add(avgMunicipalities);
        }

        titles.Add(inhabitants);
        titles.Add(navigationKr);
        titles.Add(navigationPercentage);

        lst.Add("Titles", titles);

        dynamic fields = new JArray();
        fields.Add("id");
        fields.Add("parentId");
        fields.Add("ReportingArea");
        fields.Add("AgeGroup");
        fields.Add("IndicatorCode");
        fields.Add("report_area_code");
        fields.Add("IndicatorDescritpion");
        fields.Add("t1");
        int count = 2;
        if (displayAverageOfCities || displayAverageOfGroups || displayAverageMunicipalityEAKAdj)
        {
            for (int i = 0; i < (lstComparingCites.Keys.Count() + 6); i++)
            {
                fields.Add("t" + count.ToString());
                count++;
            }
        }
        else
        {
            for (int i = 0; i < (lstComparingCites.Keys.Count() + 5); i++)
            {
                fields.Add("t" + count.ToString());
                count++;
            }
        }
        lst.Add("Fields", fields);
        if (displayAverageOfCities || displayAverageOfGroups || displayAverageMunicipalityEAKAdj)
        {
            dynamic backGroundIndex = new JArray() { 4, (4 + (lstComparingCites.Count()) + 5) };
            lst.Add("backGroundIndex", backGroundIndex);
        }
        else
        {
            dynamic backGroundIndex = new JArray() { 4, (4 + (lstComparingCites.Count()) + 4) };
            lst.Add("backGroundIndex", backGroundIndex);
        }

        lst.gridConfig = JObject.Parse(await _utility.GetApplicationSettingAsync("GetCostReductionPotentialData_grid_config"));

        return lst;
    }



    private static string GetIndicatorStatus(decimal minProdIndex, decimal maxProdIndex, decimal compValue)
    {
        string ret;
        if (compValue < minProdIndex)
        {
            ret = "isLow";
        }
        else if (compValue > maxProdIndex)
        {
            ret = "isGood";
        }
        else
        {
            ret = "isModerate";
        }
        return ret;
    }



    public List<clsIndicators> GetCostReductionPotentialDataIndicators(string userId)
    {
        return GetCostReductionPotentialDataIndicatorsAsync(userId).GetAwaiter().GetResult();
    }



    public async Task<List<clsIndicators>> GetCostReductionPotentialDataIndicatorsAsync(string userId)
    {
        TenantDBContext kostraDbContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        string kostraDataType = (await GetUserPreferencesAsync(userId))["kostra_data_preference"];
        List<dynamic> lstData = new List<dynamic>();
        short budgetYear = Convert.ToInt16(await GetBudgetYearAsync(userId));
        gco_tenants tenantDetails = await kostraDbContext.gco_tenants.FirstOrDefaultAsync(x => x.pk_id == userDetails.tenant_id);

        var reportingAreasAndIndicators = (from k in lstData
            select new
            {
                fk_indicator_code = string.Empty,
                indicator_description = string.Empty,
                fk_report_area_code = string.Empty,
                fk_kostra_function_code = string.Empty
            }).ToList();

        var comparableCitiesData = (from k in lstData
            select new
            {
                fk_region_code = string.Empty,
                fk_report_area_code = string.Empty,
                report_area_name = string.Empty,
                fk_indicator_code = string.Empty,
                indicator_value = 0.0M,
                expence_adjusted_value = 0.0M,
                sorting_graphs = 0,
                year = budgetYear
            }).ToList();
        List<string> lstIndicators = (from v in reportingAreasAndIndicators
            select v.fk_indicator_code).Distinct().ToList();

        if (kostraDataType.ToLower() == "city")
        {
            reportingAreasAndIndicators = (await (from cr in kostraDbContext.gko_cost_reduction_data
                    join t in kostraDbContext.gco_tenants on cr.fk_municipality_id equals t.municipality_id
                    join r in kostraDbContext.gmd_reporting_areas on cr.fk_report_area_code equals r
                        .pk_report_area_code
                    join i in kostraDbContext.gmd_kostra_lables on cr.fk_indicator_code equals i.pk_indicator_code
                    join m in kostraDbContext.gco_municipalities on t.municipality_id equals m.pk_municipality_id
                    where (t.pk_id == tenantDetails.pk_id
                           && t.municipality_id == m.pk_municipality_id && cr.type == kostraDataType.ToLower())
                    select new
                    {
                        cr.fk_indicator_code,
                        i.indicator_description,
                        cr.fk_report_area_code,
                        cr.fk_kostra_function_code
                    }).ToListAsync()).OrderBy(x => x.fk_report_area_code).ThenBy(x => x.fk_kostra_function_code)
                .ToList();
        }
        else
        {
            reportingAreasAndIndicators = (await (from cr in kostraDbContext.gko_cost_reduction_data
                    join t in kostraDbContext.gco_tenants on cr.fk_municipality_id equals t.municipality_id
                    join r in kostraDbContext.gmd_reporting_areas on cr.fk_report_area_code equals r
                        .pk_report_area_code
                    join i in kostraDbContext.gmd_kostra_lables on cr.fk_indicator_code equals i.pk_indicator_code
                    join m in kostraDbContext.gco_municipalities on t.municipality_id equals m.pk_municipality_id
                    where (t.pk_id == tenantDetails.pk_id
                           && t.municipality_id == m.pk_municipality_id && cr.type == kostraDataType.ToLower())
                    select new
                    {
                        cr.fk_indicator_code,
                        i.indicator_description,
                        cr.fk_report_area_code,
                        cr.fk_kostra_function_code
                    }).ToListAsync()).OrderBy(x => x.fk_report_area_code).ThenBy(x => x.fk_kostra_function_code)
                .ToList();
        }

        List<clsIndicators> lstIndicatorsData = new List<clsIndicators>();
        foreach (var v in reportingAreasAndIndicators)
        {
            clsIndicators i = new clsIndicators();
            i.indicatorCode = v.fk_indicator_code;
            i.indicatorDescription = v.indicator_description;
            lstIndicatorsData.Add(i);
        }

        return lstIndicatorsData;
    }



    private async Task<IEnumerable<dynamic>> GetCostPerCitizenForAmountAsync(string userId, Dictionary<string, string> cities, bool adjustForExpenseNeed)
    {
        TenantDBContext kostraDbContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        string kostraDataType = (await GetUserPreferencesAsync(userId))["kostra_data_preference"];
        List<dynamic> lstData = new List<dynamic>();
        var budgetYear = Convert.ToInt16((await GetBudgetYearAsync(userId)));
        gco_tenants tenantDetails = await kostraDbContext.gco_tenants.FirstOrDefaultAsync(x => x.pk_id == userDetails.tenant_id);

        if (kostraDataType.ToLower() == "city")
        {
            var currentMunicipality = (await (from a in kostraDbContext.gko_main_indicators
                join c in kostraDbContext.gmd_reporting_areas on a.fk_report_area_code equals c.pk_report_area_code
                join b in kostraDbContext.gko_kostra_data on a.fk_indicator_code equals b.fk_indicator_code
                //join d in kostraDbContext.gmd_expence_need on new { x = c.pk_report_area_code, y = b.fk_region_code, z = b.year } equals new { x = d.fk_report_area_code, y = d.fk_municipality_id, z = d.year }
                where (a.type == 3 && a.active == 1 && b.year == (budgetYear - 1) && b.fk_region_code == tenantDetails.municipality_id
                    )
                select new
                {
                    b.fk_region_code,
                    a.fk_report_area_code,
                    c.report_area_name,
                    b.fk_indicator_code,
                    b.indicator_value,
                    //expence_adjusted_value = (b.indicator_value / d.expence_need),
                    expence_adjusted_value = adjustForExpenseNeed ? b.indicator_value + b.exp_adj_value : b.indicator_value,
                    c.sorting_graphs,
                }).ToListAsync()).OrderBy(x => x.fk_region_code).ThenBy(x => x.sorting_graphs).Distinct().ToList();
            if (cities.Any())
            {
                var data = (await (from a in kostraDbContext.gko_main_indicators
                    join c in kostraDbContext.gmd_reporting_areas on a.fk_report_area_code equals c.pk_report_area_code
                    join b in kostraDbContext.gko_kostra_data on a.fk_indicator_code equals b.fk_indicator_code
                    //join d in kostraDbContext.gmd_expence_need on new { x = c.pk_report_area_code, y = b.fk_region_code, z = b.year } equals new { x = d.fk_report_area_code, y = d.fk_municipality_id, z = d.year }
                    where (a.type == 3 && a.active == 1 && b.year == (budgetYear - 1) && cities.Keys.Contains(b.fk_region_code)
                        )
                    select new
                    {
                        b.fk_region_code,
                        a.fk_report_area_code,
                        c.report_area_name,
                        b.fk_indicator_code,
                        b.indicator_value,
                        //expence_adjusted_value = (b.indicator_value / d.expence_need),
                        expence_adjusted_value = adjustForExpenseNeed ? b.indicator_value + b.exp_adj_value : b.indicator_value,
                        c.sorting_graphs
                    }).ToListAsync()).OrderBy(x => x.fk_region_code).ThenBy(x => x.sorting_graphs).Distinct().ToList();

                currentMunicipality.AddRange(data);
            }

            if (currentMunicipality != null && currentMunicipality.Any())
            {
                foreach (var v in currentMunicipality)
                {
                    dynamic val = new DynamicDictionary();
                    val.fk_region_code = v.fk_region_code;
                    val.fk_report_area_code = v.fk_report_area_code;
                    val.report_area_name = v.report_area_name;
                    val.fk_indicator_code = v.fk_indicator_code;
                    val.indicator_value = v.indicator_value;
                    val.expence_adjusted_value = v.expence_adjusted_value;
                    lstData.Add(val);
                }
            }

            return lstData;
        }
        else
        {
            var currentMunicipality = (await (from a in kostraDbContext.gko_main_indicators
                join c in kostraDbContext.gmd_reporting_areas on a.fk_report_area_code equals c.pk_report_area_code
                join b in kostraDbContext.gko_kostra_data_corp on a.fk_indicator_code equals b.fk_indicator_code
                //join d in kostraDbContext.gmd_expence_need on new { x = c.pk_report_area_code, y = b.fk_region_code, z = b.year } equals new { x = d.fk_report_area_code, y = d.fk_municipality_id, z = d.year }
                where (a.type == 3 && a.active == 1 && b.year == (budgetYear - 1) && b.fk_region_code == tenantDetails.municipality_id
                    )
                select new
                {
                    b.fk_region_code,
                    a.fk_report_area_code,
                    c.report_area_name,
                    b.fk_indicator_code,
                    b.indicator_value,
                    //expence_adjusted_value = (b.indicator_value / d.expence_need),
                    expence_adjusted_value = adjustForExpenseNeed ? b.indicator_value + b.exp_adj_value : b.indicator_value,
                    c.sorting_graphs
                }).ToListAsync()).OrderBy(x => x.fk_region_code).ThenBy(x => x.sorting_graphs).Distinct().ToList();
            if (cities.Any())
            {
                var data = (await (from a in kostraDbContext.gko_main_indicators
                    join c in kostraDbContext.gmd_reporting_areas on a.fk_report_area_code equals c.pk_report_area_code
                    join b in kostraDbContext.gko_kostra_data_corp on a.fk_indicator_code equals b.fk_indicator_code
                    //join d in kostraDbContext.gmd_expence_need on new { x = c.pk_report_area_code, y = b.fk_region_code, z = b.year } equals new { x = d.fk_report_area_code, y = d.fk_municipality_id, z = d.year }
                    where (a.type == 3 && a.active == 1 && b.year == (budgetYear - 1) && cities.Keys.Contains(b.fk_region_code)
                        )
                    select new
                    {
                        b.fk_region_code,
                        a.fk_report_area_code,
                        c.report_area_name,
                        b.fk_indicator_code,
                        b.indicator_value,
                        //expence_adjusted_value = (b.indicator_value / d.expence_need),
                        expence_adjusted_value = adjustForExpenseNeed ? b.indicator_value + b.exp_adj_value : b.indicator_value,
                        c.sorting_graphs
                    }).ToListAsync()).OrderBy(x => x.fk_region_code).ThenBy(x => x.sorting_graphs).Distinct().ToList();

                currentMunicipality.AddRange(data);
            }
            if (currentMunicipality != null && currentMunicipality.Any())
            {
                foreach (var v in currentMunicipality)
                {
                    dynamic val = new DynamicDictionary();
                    val.fk_region_code = v.fk_region_code;
                    val.fk_report_area_code = v.fk_report_area_code;
                    val.report_area_name = v.report_area_name;
                    val.fk_indicator_code = v.fk_indicator_code;
                    val.indicator_value = v.indicator_value;
                    val.expence_adjusted_value = v.expence_adjusted_value;
                    lstData.Add(val);
                }
            }

            return lstData;
        }
    }



    private async Task<IEnumerable<dynamic>> GetCostPerCitizenForPercentageAsync(string userId, Dictionary<string, string> cities)
    {
        TenantDBContext kostraDbContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        string kostraDataType = (await GetUserPreferencesAsync(userId))["kostra_data_preference"];
        List<dynamic> lstData = new List<dynamic>();
        short budgetYear = Convert.ToInt16((await GetBudgetYearAsync(userId)));
        gco_tenants tenantDetails = await kostraDbContext.gco_tenants.FirstOrDefaultAsync(x => x.pk_id == userDetails.tenant_id);

        if (kostraDataType.ToLower() == "city")
        {
            var currentMunicipality = (await (from a in kostraDbContext.gko_main_indicators
                join c in kostraDbContext.gmd_reporting_areas on a.fk_report_area_code equals c.pk_report_area_code
                join b in kostraDbContext.gko_kostra_data on a.fk_indicator_code equals b.fk_indicator_code
                where (a.type == 2 && a.active == 1 && b.year == (budgetYear - 1) &&
                       b.fk_region_code == tenantDetails.municipality_id
                    )
                select new
                {
                    b.fk_region_code,
                    a.fk_report_area_code,
                    c.report_area_name,
                    b.fk_indicator_code,
                    b.indicator_value,
                    expence_adjusted_value = 0.0M,
                    c.sorting_graphs
                }).ToListAsync()).OrderBy(x => x.fk_region_code).ThenBy(x => x.sorting_graphs).ToList();
            if (cities.Any())
            {
                var data = (await (from a in kostraDbContext.gko_main_indicators
                    join c in kostraDbContext.gmd_reporting_areas on a.fk_report_area_code equals c
                        .pk_report_area_code
                    join b in kostraDbContext.gko_kostra_data on a.fk_indicator_code equals b.fk_indicator_code
                    where (a.type == 2 && a.active == 1 && b.year == (budgetYear - 1) &&
                           cities.Keys.Contains(b.fk_region_code)
                        )
                    select new
                    {
                        b.fk_region_code,
                        a.fk_report_area_code,
                        c.report_area_name,
                        b.fk_indicator_code,
                        b.indicator_value,
                        expence_adjusted_value = 0.0M,
                        c.sorting_graphs
                    }).ToListAsync()).OrderBy(x => x.fk_region_code).ThenBy(x => x.sorting_graphs).ToList();

                currentMunicipality.AddRange(data);
            }

            if (currentMunicipality != null && currentMunicipality.Any())
            {
                foreach (var v in currentMunicipality)
                {
                    dynamic val = new DynamicDictionary();
                    val.fk_region_code = v.fk_region_code;
                    val.fk_report_area_code = v.fk_report_area_code;
                    val.report_area_name = v.report_area_name;
                    val.fk_indicator_code = v.fk_indicator_code;
                    val.indicator_value = v.indicator_value;
                    val.expence_adjusted_value = 0;
                    lstData.Add(val);
                }
            }
            return lstData;
        }
        else
        {
            var currentMunicipality = (await (from a in kostraDbContext.gko_main_indicators
                join c in kostraDbContext.gmd_reporting_areas on a.fk_report_area_code equals c.pk_report_area_code
                join b in kostraDbContext.gko_kostra_data_corp on a.fk_indicator_code equals b.fk_indicator_code
                where (a.type == 2 && a.active == 1 && b.year == (budgetYear - 1) && b.fk_region_code == tenantDetails.municipality_id
                    )
                select new
                {
                    b.fk_region_code,
                    a.fk_report_area_code,
                    c.report_area_name,
                    b.fk_indicator_code,
                    b.indicator_value,
                    expence_adjusted_value = 0.0M,
                    c.sorting_graphs
                }).ToListAsync()).OrderBy(x => x.fk_region_code).ThenBy(x => x.sorting_graphs).ToList();
            if (cities.Any())
            {
                var data = (await (from a in kostraDbContext.gko_main_indicators
                    join c in kostraDbContext.gmd_reporting_areas on a.fk_report_area_code equals c
                        .pk_report_area_code
                    join b in kostraDbContext.gko_kostra_data_corp on a.fk_indicator_code equals b.fk_indicator_code
                    where (a.type == 2 && a.active == 1 && b.year == (budgetYear - 1) &&
                           cities.Keys.Contains(b.fk_region_code)
                        )
                    select new
                    {
                        b.fk_region_code,
                        a.fk_report_area_code,
                        c.report_area_name,
                        b.fk_indicator_code,
                        b.indicator_value,
                        expence_adjusted_value = 0.0M,
                        c.sorting_graphs
                    }).ToListAsync()).OrderBy(x => x.fk_region_code).ThenBy(x => x.sorting_graphs).ToList();

                currentMunicipality.AddRange(data);
            }

            if (currentMunicipality != null && currentMunicipality.Any())
            {
                foreach (var v in currentMunicipality)
                {
                    dynamic val = new DynamicDictionary();
                    val.fk_region_code = v.fk_region_code;
                    val.fk_report_area_code = v.fk_report_area_code;
                    val.report_area_name = v.report_area_name;
                    val.fk_indicator_code = v.fk_indicator_code;
                    val.indicator_value = v.indicator_value;
                    val.expence_adjusted_value = 0;
                    lstData.Add(val);
                }
            }
            return lstData;
        }
    }

}