#pragma warning disable CS8601
#pragma warning disable CS8602
#pragma warning disable CS8604
#pragma warning disable CS8625
#pragma warning disable CS8600

using EntityFrameworkExtras.EFCore;
using Framsikt.BL.Helpers;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;
using System.Text;
using Framsikt.BL.Core.Helpers;

namespace Framsikt.BL
{
    public class AdminOrgStructure : IAdminOrgStructure
    {
        private readonly IUtility _utility;
        private readonly IStaffPlanning _staffPlanning;
        private readonly IAppDataCache _cache;
        private readonly IBackendRequest _backendJob;
        private readonly IFinUtility _finUtility;
        private readonly int projectErrorCode = 10; // As per DB, PK_ID 10 is the project error code, please dont forget to update if any changes made :)

        public AdminOrgStructure(IUtility util, IStaffPlanning staffPlanning, IAppDataCache cache, IBackendRequest backendJob, IFinUtility finUtility)
        {
            _utility = util;
            _staffPlanning = staffPlanning;
            _cache = cache;
            _backendJob = backendJob;
            _finUtility = finUtility;
        }

        public async Task<JObject> GetDepartments(string userId, int skip, int take, AdminOrgStructureDepartment searchParams)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            var userDetails = await _utility.GetUserDetailsAsync(userId);

            int departmentsCount = 0;
            var dbDepartments = await dbContext.tco_departments.Where(x => x.fk_tenant_id == userDetails.tenant_id).OrderBy(y => y.pk_department_code).ToListAsync();

            if (searchParams != null && !string.IsNullOrEmpty(searchParams.deptCode))
            {
                dbDepartments = dbDepartments.Where(x => x.pk_department_code.ToLower().Contains(searchParams.deptCode.ToLower())).ToList();
            }

            if (searchParams != null && !string.IsNullOrEmpty(searchParams.deptName))
            {
                dbDepartments = dbDepartments.Where(x => x.department_name.ToLower().Contains(searchParams.deptName.ToLower())).ToList();
            }

            if (searchParams != null && searchParams.yearFrom != 0)
            {
                dbDepartments = dbDepartments.Where(x => x.year_from == searchParams.yearFrom).ToList();
            }

            if (searchParams != null && searchParams.yearTo != 0)
            {
                dbDepartments = dbDepartments.Where(x => x.year_to == searchParams.yearTo).ToList();
            }

            if (searchParams != null && searchParams.active != -1)
            {
                dbDepartments = dbDepartments.Where(x => x.status == searchParams.active).ToList();
            }

            departmentsCount = dbDepartments.Any() ? dbDepartments.Count : 0;
            dbDepartments = dbDepartments.Skip(skip).Take(take).ToList();

            dynamic departments = new JArray();

            foreach (var dept in dbDepartments)
            {
                dynamic deptObj = new JObject();
                deptObj.pkId = dept.pk_id;
                deptObj.deptCode = dept.pk_department_code;
                deptObj.deptName = dept.department_name;
                deptObj.yearFrom = dept.year_from;
                deptObj.yearTo = dept.year_to;
                deptObj.active = dept.status;
                departments.Add(deptObj);
            }

            dynamic result = new JObject();
            result.Add("data", departments);
            result.totalRows = departmentsCount;

            return result;
        }

        public async Task<JObject> GetDepartmentColumns(string userId)
        {
            var userdetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringValue = await _utility.GetLanguageStringsAsync(userdetails.language_preference, userdetails.user_name, "StaffPlanning");
            dynamic columnObj = new JObject();

            dynamic headerArray = new JArray();
            dynamic headerObj = new JObject();
            headerObj.title = ((langStringValue.FirstOrDefault(v => v.Key == "org_structure_dept_title")).Value).LangText;
            headerObj.descriptiontip = ((langStringValue.FirstOrDefault(v => v.Key == "org_structure_dept_tooltip_desc")).Value).LangText;
            headerArray.Add(headerObj);

            dynamic columns = new JArray();
            columns.Add(FormatColumn(" ", "pkId", 0, false, true, null, false, 30, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));
            columns.Add(FormatColumn(((langStringValue.FirstOrDefault(v => v.Key == "org_structure_dept_code")).Value).LangText, "deptCode", 0, false, false, null, false, 100, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));
            columns.Add(FormatColumn(((langStringValue.FirstOrDefault(v => v.Key == "org_structure_dept_name")).Value).LangText, "deptName", 0, false, false, null, false, 300, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));
            columns.Add(FormatColumn(((langStringValue.FirstOrDefault(v => v.Key == "org_structure_year_from")).Value).LangText, "yearFrom", 0, false, false, null, false, 100, "text-align:right;white-space:normal;border-left:none;", "text-align:right;border-left:none;",
                                     null, null, null, null, null, false, null));
            columns.Add(FormatColumn(((langStringValue.FirstOrDefault(v => v.Key == "org_structure_year_to")).Value).LangText, "yearTo", 0, false, false, null, false, 100, "text-align:right;white-space:normal;border-left:none;", "text-align:right;border-left:none;",
                                     null, null, null, null, null, false, null));
            columns.Add(FormatColumn(((langStringValue.FirstOrDefault(v => v.Key == "org_structure_active")).Value).LangText, "active", 0, false, false, null, false, 30, "text-align:center;white-space:normal;", "text-align:center;",
                                     null, "<div class='chkbox-status'><input type =\"checkbox\" name=\"statusChecked\" id=\"statusChecked_#:deptCode#\" class=\"chkbx\" #= active ? checked='checked' : '' # /></div>",
                                     null, null, null, false, null));
            columns.Add(FormatColumn(" ", "search", 0, false, false, null, false, 30, "text-align:right;white-space:normal;", "text-align:right;",
                                     null, null, null, null, null, false, null));

            columnObj.Add("header", headerArray);
            columnObj.Add("columns", columns);

            return columnObj;
        }

        public static JObject FormatColumn(string title, string field, int colCount, bool encoded, bool hidden, string format, bool expandable, int width,
                                    string attributes, string headerAttributes, string footerAttributes, string template, string footerTemplate,
                                    string filterable, string headerTemplate, bool locked, string command)
        {
            dynamic column = new JObject();
            column.title = title;
            column.field = field;
            column.colCount = colCount;
            column.encoded = encoded;
            column.hidden = hidden;
            column.format = format;
            column.expandable = expandable;
            column.locked = locked;
            column.width = width;

            dynamic attribute = new JObject();
            attribute.style = attributes;
            column.attributes = attribute;

            dynamic headerAttribute = new JObject();
            headerAttribute.style = headerAttributes;
            column.headerAttributes = headerAttribute;

            dynamic footerAttribute = new JObject();
            footerAttribute.style = footerAttributes;
            column.footerAttributes = footerAttribute;

            column.template = template;
            column.footerTemplate = footerTemplate;

            dynamic filterableObj = new JObject();
            dynamic cell = new JObject();
            cell.enabled = true;
            cell.showOperators = false;
            cell.dataSource = new JArray();
            cell.@operator = "contains";
            filterableObj.cell = cell;
            column.filterable = filterableObj;

            column.headerTemplate = headerTemplate;
            column.locked = locked;
            column.command = command;

            return column;
        }

        public async Task<JObject> AddAditDepartments(string userId, List<AdminOrgStructureDepartment> uiData)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            dbContext.Database.SetCommandTimeout(500);
            var userDetails = await _utility.GetUserDetailsAsync(userId);
            int clientId = userDetails.client_id;
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "StaffPlanning");
            dynamic result = new JObject();

            try
            {
                foreach (var data in uiData)
                {
                    if (data.type == "add")
                    {
                        var newDepartmentDuplicateCheck = await dbContext.tco_departments.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_department_code == data.deptCode);

                        if (newDepartmentDuplicateCheck != null && ((data.yearFrom >= newDepartmentDuplicateCheck.year_from && data.yearFrom <= newDepartmentDuplicateCheck.year_to) ||
                                                                    (data.yearTo >= newDepartmentDuplicateCheck.year_from && data.yearTo <= newDepartmentDuplicateCheck.year_to)))
                        {
                            // department already exist
                            result.messageType = "duplicate";
                            result.message = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_dupliate_dept")).Value).LangText + " - " + data.deptCode;
                            return result;
                        }
                        else
                        {
                            tco_departments newDept = new tco_departments()
                            {
                                pk_department_code = data.deptCode.Trim(),
                                fk_tenant_id = userDetails.tenant_id,
                                department_name = data.deptName.Trim(),
                                status = data.active,
                                year_from = data.yearFrom,
                                year_to = data.yearTo,
                                updated = DateTime.UtcNow,
                                updated_by = userDetails.pk_id
                            };

                            await dbContext.tco_departments.AddAsync(newDept);
                            await dbContext.SaveChangesAsync();
                        }
                    }
                    else
                    {
                        var existingDept = await dbContext.tco_departments.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_department_code == data.deptCode).ToListAsync();
                        foreach (var e in existingDept.Where(x => x.pk_id != data.pkId).ToList())
                        {
                            if (((data.yearFrom >= e.year_from && data.yearFrom <= e.year_to) ||
                                                                    (data.yearTo >= e.year_from && data.yearTo <= e.year_to)))
                            {
                                // department already exist
                                result.messageType = "duplicate";
                                result.message = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_dupliate_dept")).Value).LangText + " - " + data.deptCode;
                                return result;
                            }
                        }

                        var deptToUpdate = existingDept.FirstOrDefault(x => x.pk_id == data.pkId);

                        if (deptToUpdate != null)
                        {
                            deptToUpdate.department_name = data.deptName.Trim();
                            deptToUpdate.year_from = data.yearFrom;
                            deptToUpdate.year_to = data.yearTo;
                            deptToUpdate.status = data.active;
                            deptToUpdate.updated = DateTime.UtcNow;
                            deptToUpdate.updated_by = userDetails.pk_id;

                            await dbContext.SaveChangesAsync();
                        }
                    }
                }

                result.messageType = "success";
                result.message = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_add_edit_success")).Value).LangText;

                if (uiData.Any())
                {
                   await StartJobToValidateAdminOrgStructure(userId, uiData.First().orgVersion);
                    _cache.ClearClientCache(clientId);

                    var adminaccountsvalidationsRequest = new AccountValidationsHelper()
                    {
                        UserId = userId,
                        TenantId = userDetails.tenant_id,
                        ValidationType = "AdminOrgStructure",
                        OrgVersion = uiData.First().orgVersion
                    };

                    string stradminaccountsvalidationsRequestRequest = JsonConvert.SerializeObject(adminaccountsvalidationsRequest);
                    _backendJob.QueueMessage(userDetails, null, QueueName.adminaccountsvalidationsqueue, stradminaccountsvalidationsRequestRequest);
                }

                return result;
            }
            catch (Exception)
            {
                result.messageType = "error";
                result.message = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_add_edit_error")).Value).LangText;
                return result;
            }
        }

        public async Task<JObject> GetOrgDataLevelColumns(string userId, string orgVersion)
        {
            var userdetails = await _utility.GetUserDetailsAsync(userId);

            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userdetails.language_preference, userdetails.user_name, "StaffPlanning");

            dynamic columnObj = new JObject();

            dynamic columns = new JArray();
            columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code")).Value).LangText, "orgCode", 0, false, false, null, false, 100, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));
            columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_short_name")).Value).LangText, "orgShortName", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));
            columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_long_name")).Value).LangText, "orgLongName", 0, false, false, null, false, 300, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));
            columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_active")).Value).LangText, "active", 0, false, false, null, false, 30, "text-align:center;white-space:normal;", "text-align:center;",
                                     null, "<div class='chkbox-status'><input type =\"checkbox\" name=\"statusChecked\" id=\"statusChecked_#:orgCode#\" class=\"chkbx\" #= active ? checked='checked' : '' # /></div>",
                                     null, null, null, false, null));
            columns.Add(FormatColumn(" ", "search", 0, false, false, null, false, 30, "text-align:left;white-space:normal;", "text-align:left;border-left:none;",
                                     null, "#if(id == ''){#<span> </span>#}else{#<span class ='padding-left5'><a><img src='../images/user_access_key.svg' onclick='readMoreUserDetails(\"#:orgCode#\", \"#:orgLongName#\", \"#:orgLevel#\")'/></a></span>#}#", null, null, null, false, null));

            columnObj.Add("columns", columns);

            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);

            var orgLevelDetails = orgVersionContent.lstOrgLevel.OrderByDescending(y => y.org_level).FirstOrDefault();

            dynamic headerArray = new JArray();

            if (orgLevelDetails.org_level == 1)
            {
                dynamic headerObj = new JObject();
                headerObj.level = 1;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 1).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl1_desc")).Value).LangText;
                headerArray.Add(headerObj);
            }
            else if (orgLevelDetails.org_level == 2)
            {
                dynamic headerObj = new JObject();
                headerObj.level = 1;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 1).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl1_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 2;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 2).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl2_desc")).Value).LangText;
                headerArray.Add(headerObj);
            }
            else if (orgLevelDetails.org_level == 3)
            {
                dynamic headerObj = new JObject();
                headerObj.level = 1;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 1).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl1_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 2;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 2).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl2_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 3;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 3).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl3_desc")).Value).LangText;
                headerArray.Add(headerObj);
            }
            else if (orgLevelDetails.org_level == 4)
            {
                dynamic headerObj = new JObject();
                headerObj.level = 1;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 1).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl1_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 2;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 2).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl2_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 3;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 3).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl3_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 4;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 4).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl4_desc")).Value).LangText;
                headerArray.Add(headerObj);
            }
            else if (orgLevelDetails.org_level == 5)
            {
                dynamic headerObj = new JObject();
                headerObj.level = 1;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 1).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl1_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 2;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 2).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl2_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 3;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 3).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl3_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 4;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 4).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl4_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 5;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 5).level_name;
                headerObj.descriptiontip = langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl5_desc").Value.LangText;
                headerArray.Add(headerObj);
            }
            else if (orgLevelDetails.org_level == 6)
            {
                dynamic headerObj = new JObject();
                headerObj.level = 1;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 1).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl1_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 2;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 2).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl2_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 3;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 3).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl3_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 4;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 4).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl4_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 5;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 5).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl5_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 6;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 6).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl6_desc")).Value).LangText;
                headerArray.Add(headerObj);
            }
            else if (orgLevelDetails.org_level == 7)
            {
                dynamic headerObj = new JObject();
                headerObj.level = 1;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 1).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl1_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 2;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 2).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl2_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 3;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 3).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl3_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 4;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 4).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl4_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 5;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 5).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl5_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 6;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 6).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl6_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 7;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 7).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl7_desc")).Value).LangText;
                headerArray.Add(headerObj);
            }
            else if (orgLevelDetails.org_level == 8)
            {
                dynamic headerObj = new JObject();
                headerObj.level = 1;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 1).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl1_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 2;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 2).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl2_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 3;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 3).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl3_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 4;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 4).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl4_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 5;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 5).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl5_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 6;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 6).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl6_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 7;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 7).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl7_desc")).Value).LangText;
                headerArray.Add(headerObj);

                headerObj = new JObject();
                headerObj.level = 8;
                headerObj.title = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.org_level == 8).level_name;
                headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_lvl8_desc")).Value).LangText;
                headerArray.Add(headerObj);
            }

            columnObj.Add("header", headerArray);

            return columnObj;
        }

        public async Task<JObject> GetOrgDataLevelInfo(string userId, int orgDataLevel, int skip, int take, AdminOrgStructureOrgLevelData searchParams, string orgVersion)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            var userdetails = await _utility.GetUserDetailsAsync(userId);
            var orgVersionContent =await  _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);

            var orgDataLevelInfo = await (from o in dbContext.vwUserDetails
                                    where o.pk_id == -1
                                    select new
                                    {
                                        orgCode = "",
                                        orgShortName = "",
                                        orgLongName = "",
                                        active = 0,
                                        orgLevel = 0
                                    }).ToListAsync();

            if (orgDataLevel == 1)
            {
                orgDataLevelInfo =  (from o in orgVersionContent.lstOrgDataLevel1
                                    orderby o.org_id_1
                                    select new
                                    {
                                        orgCode = o.org_id_1,
                                        orgShortName = o.org_short_name_1,
                                        orgLongName = o.org_name_1,
                                        active = o.status,
                                        orgLevel = orgDataLevel
                                    }).ToList();
            }
            else if (orgDataLevel == 2)
            {
                orgDataLevelInfo = (from o in orgVersionContent.lstOrgDataLevel2
                                    where o.fk_tenant_id == userdetails.tenant_id && o.fk_org_version == orgVersionContent.orgVersion
                                    orderby o.org_id_2
                                    select new
                                    {
                                        orgCode = o.org_id_2,
                                        orgShortName = o.org_short_name_2,
                                        orgLongName = o.org_name_2,
                                        active = o.status,
                                        orgLevel = orgDataLevel
                                    }).ToList();
            }
            else if (orgDataLevel == 3)
            {
                orgDataLevelInfo = (from o in orgVersionContent.lstOrgDataLevel3
                                    orderby o.org_id_3
                                    select new
                                    {
                                        orgCode = o.org_id_3,
                                        orgShortName = o.org_short_name_3,
                                        orgLongName = o.org_name_3,
                                        active = o.status,
                                        orgLevel = orgDataLevel
                                    }).ToList();
            }
            else if (orgDataLevel == 4)
            {
                orgDataLevelInfo = (from o in orgVersionContent.lstOrgDataLevel4
                                    orderby o.org_id_4
                                    select new
                                    {
                                        orgCode = o.org_id_4,
                                        orgShortName = o.org_short_name_4,
                                        orgLongName = o.org_name_4,
                                        active = o.status,
                                        orgLevel = orgDataLevel
                                    }).ToList();
            }
            else if (orgDataLevel == 5)
            {
                orgDataLevelInfo = (from o in orgVersionContent.lstOrgDataLevel5
                                    orderby o.org_id_5
                                    select new
                                    {
                                        orgCode = o.org_id_5,
                                        orgShortName = o.org_short_name_5,
                                        orgLongName = o.org_name_5,
                                        active = o.status,
                                        orgLevel = orgDataLevel
                                    }).ToList();
            }
            else if (orgDataLevel == 6)
            {
                orgDataLevelInfo = (from o in orgVersionContent.lstOrgDataLevel6
                                    orderby o.org_id_6
                                    select new
                                    {
                                        orgCode = o.org_id_6,
                                        orgShortName = o.org_short_name_6,
                                        orgLongName = o.org_name_6,
                                        active = o.status,
                                        orgLevel = orgDataLevel
                                    }).ToList();
            }
            else if (orgDataLevel == 7)
            {
                orgDataLevelInfo = (from o in orgVersionContent.lstOrgDataLevel7
                                    orderby o.org_id_7
                                    select new
                                    {
                                        orgCode = o.org_id_7,
                                        orgShortName = o.org_short_name_7,
                                        orgLongName = o.org_name_7,
                                        active = o.status,
                                        orgLevel = orgDataLevel
                                    }).ToList();
            }
            else if (orgDataLevel == 8)
            {
                orgDataLevelInfo = (from o in orgVersionContent.lstOrgDataLevel8
                                    orderby o.org_id_8
                                    select new
                                    {
                                        orgCode = o.org_id_8,
                                        orgShortName = o.org_short_name_8,
                                        orgLongName = o.org_name_8,
                                        active = o.status,
                                        orgLevel = orgDataLevel
                                    }).ToList();
            }

            int orgDataLevelInfoCount = 0;

            if (searchParams != null && !string.IsNullOrEmpty(searchParams.orgCode))
            {
                orgDataLevelInfo = orgDataLevelInfo.Where(x => x.orgCode.ToLower().Contains(searchParams.orgCode.ToLower())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.orgShortName))
            {
                orgDataLevelInfo = orgDataLevelInfo.Where(x => x.orgShortName.ToLower().Contains(searchParams.orgShortName.ToLower())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.orgLongName))
            {
                orgDataLevelInfo = orgDataLevelInfo.Where(x => x.orgLongName.ToLower().Contains(searchParams.orgLongName.ToLower())).ToList();
            }
            if (searchParams != null && searchParams.active != -1)
            {
                orgDataLevelInfo = orgDataLevelInfo.Where(x => x.active == searchParams.active).ToList();
            }

            orgDataLevelInfoCount = orgDataLevelInfo.Any() ? orgDataLevelInfo.Count : 0;
            orgDataLevelInfo = orgDataLevelInfo.Skip(skip).Take(take).ToList();

            dynamic orgDataLevelInfoArray = new JArray();

            foreach (var o in orgDataLevelInfo)
            {
                dynamic orgDataLevelObj = new JObject();
                orgDataLevelObj.orgCode = o.orgCode;
                orgDataLevelObj.orgShortName = o.orgShortName;
                orgDataLevelObj.orgLongName = o.orgLongName;
                orgDataLevelObj.active = o.active;
                orgDataLevelObj.orgLevel = o.orgLevel;
                orgDataLevelInfoArray.Add(orgDataLevelObj);
            }

            dynamic result = new JObject();
            result.Add("data", orgDataLevelInfoArray);
            result.totalRows = orgDataLevelInfoCount;

            return result;
        }

        public async Task<JObject> AddAditOrgLevelData(string userId, int orgDataLevel, List<AdminOrgStructureOrgLevelData> uiData, string orgVersion)
        {
            if (orgDataLevel == 1)
            {
                return  await AddAditOrgLevel1Data(userId, uiData, orgVersion);
            }
            else if (orgDataLevel == 2)
            {
                return await AddAditOrgLevel2Data(userId, uiData, orgVersion);
            }
            else if (orgDataLevel == 3)
            {
                return await AddAditOrgLevel3Data(userId, uiData, orgVersion);
            }
            else if (orgDataLevel == 4)
            {
                return await AddAditOrgLevel4Data(userId, uiData, orgVersion);
            }
            else if (orgDataLevel == 5)
            {
                return await AddAditOrgLevel5Data(userId, uiData, orgVersion);
            }
            else if (orgDataLevel == 6)
            {
                return await AddAditOrgLevel6Data(userId, uiData, orgVersion);
            }
            else if (orgDataLevel == 7)
            {
                return await AddAditOrgLevel7Data(userId, uiData, orgVersion);
            }
            else
            {
                return await AddAditOrgLevel8Data(userId, uiData, orgVersion);
            }
        }

        private async Task<JObject> AddAditOrgLevel1Data(string userId, List<AdminOrgStructureOrgLevelData> uiData, string orgVersion)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            var userDetails = await _utility.GetUserDetailsAsync(userId);
            int clientId = userDetails.client_id;
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "StaffPlanning");
            dynamic result = new JObject();

            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);

            List<AdminOrgStructureOrgLevelData> existingData = new List<AdminOrgStructureOrgLevelData>();

            try
            {
                foreach (var data in uiData)
                {
                    if (data.type == "add")
                    {
                        var newDepartmentDuplicateCheck = orgVersionContent.lstOrgDataLevel1.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id && x.org_id_1 == data.orgCode);
                        if (newDepartmentDuplicateCheck != null)
                        {
                            // department already exist
                            result.messageType = "duplicate";
                            result.message = "org code " + data.orgCode + " already exists";
                            return result;
                        }
                        else
                        {
                            tco_org_data_level_1 newOrgData = new tco_org_data_level_1()
                            {
                                org_id_1 = data.orgCode,
                                fk_tenant_id = userDetails.tenant_id,
                                fk_org_version = orgVersionContent.orgVersion,
                                org_name_1 = data.orgLongName,
                                org_short_name_1 = data.orgShortName,
                                status = data.active,
                                updated = DateTime.UtcNow,
                                updated_by = userDetails.pk_id
                            };

                           await dbContext.tco_org_data_level_1.AddAsync(newOrgData);
                            await dbContext.SaveChangesAsync();
                        }
                    }
                    else
                    {
                        var existingOrgData = orgVersionContent.lstOrgDataLevel1.FirstOrDefault(x => x.org_id_1 == data.orgCode);
                        if (existingOrgData != null)
                        {
                            existingData.Add(data);
                            existingOrgData.org_short_name_1 = data.orgShortName;
                            existingOrgData.org_name_1 = data.orgLongName;
                            existingOrgData.status = data.active;
                            existingOrgData.updated = DateTime.UtcNow;
                            existingOrgData.updated_by = userDetails.pk_id;

                           await dbContext.SaveChangesAsync();
                        }
                    }
                    _cache.ClearClientCache(clientId);
                    orgVersionContent =  await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);
                }
             await   UpdateOrgHierarchy(userId, existingData, 1, orgVersion);

                await StartJobToValidateAdminOrgStructure(userId, orgVersion);

                result.messageType = "success";
                result.message = result.message = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_add_edit_success")).Value).LangText;
                return result;
            }
            catch (Exception)
            {
                result.messageType = "error";
                result.message = result.message = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_add_edit_error")).Value).LangText;
                return result;
            }
        }

        private async Task<JObject> AddAditOrgLevel2Data(string userId, List<AdminOrgStructureOrgLevelData> uiData, string orgVersion)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            var userDetails = await _utility.GetUserDetailsAsync(userId);
            int clientId = userDetails.client_id;
            dynamic result = new JObject();
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "StaffPlanning");

            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);

            List<AdminOrgStructureOrgLevelData> existingData = new List<AdminOrgStructureOrgLevelData>();

            try
            {
                foreach (var data in uiData)
                {
                    if (data.type == "add")
                    {
                        var newDepartmentDuplicateCheck = orgVersionContent.lstOrgDataLevel2.FirstOrDefault(x => x.org_id_2 == data.orgCode);
                        if (newDepartmentDuplicateCheck != null)
                        {
                            // department already exist
                            result.messageType = "duplicate";
                            result.message = "org code " + data.orgCode + " already exists";
                            return result;
                        }
                        else
                        {
                            tco_org_data_level_2 newOrgData = new tco_org_data_level_2()
                            {
                                org_id_2 = data.orgCode,
                                fk_tenant_id = userDetails.tenant_id,
                                fk_org_version = orgVersionContent.orgVersion,
                                org_name_2 = data.orgLongName,
                                org_short_name_2 = data.orgShortName,
                                status = data.active,
                                updated = DateTime.UtcNow,
                                updated_by = userDetails.pk_id
                            };

                            await dbContext.tco_org_data_level_2.AddAsync(newOrgData);
                            await dbContext.SaveChangesAsync();
                        }
                    }
                    else
                    {
                        var existingOrgData = orgVersionContent.lstOrgDataLevel2.FirstOrDefault(x => x.org_id_2 == data.orgCode);
                        if (existingOrgData != null)
                        {
                            existingData.Add(data);
                            existingOrgData.org_short_name_2 = data.orgShortName;
                            existingOrgData.org_name_2 = data.orgLongName;
                            existingOrgData.status = data.active;
                            existingOrgData.updated = DateTime.UtcNow;
                            existingOrgData.updated_by = userDetails.pk_id;

                         await dbContext.SaveChangesAsync();
                        }
                    }
                    _cache.ClearClientCache(clientId);
                    orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);
                }
               await UpdateOrgHierarchy(userId, existingData, 2, orgVersion);

                await StartJobToValidateAdminOrgStructure(userId, orgVersion);

                result.messageType = "success";
                result.message = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_add_edit_success")).Value).LangText;
                return result;
            }
            catch (Exception)
            {
                result.messageType = "error";
                result.message = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_add_edit_error")).Value).LangText;
                return result;
            }
        }

        private async Task<JObject> AddAditOrgLevel3Data(string userId, List<AdminOrgStructureOrgLevelData> uiData, string orgVersion)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            var userDetails = await _utility.GetUserDetailsAsync(userId);
            int clientId = userDetails.client_id;
            dynamic result = new JObject();
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "StaffPlanning");

            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);

            List<AdminOrgStructureOrgLevelData> existingData = new List<AdminOrgStructureOrgLevelData>();

            try
            {
                foreach (var data in uiData)
                {
                    if (data.type == "add")
                    {
                        var newDepartmentDuplicateCheck = orgVersionContent.lstOrgDataLevel3.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id && x.org_id_3 == data.orgCode);
                        if (newDepartmentDuplicateCheck != null)
                        {
                            // department already exist
                            result.messageType = "duplicate";
                            result.message = "org code " + data.orgCode + " already exists";
                            return result;
                        }
                        else
                        {
                            tco_org_data_level_3 newOrgData = new tco_org_data_level_3()
                            {
                                org_id_3 = data.orgCode,
                                fk_tenant_id = userDetails.tenant_id,
                                fk_org_version = orgVersionContent.orgVersion,
                                org_name_3 = data.orgLongName,
                                org_short_name_3 = data.orgShortName,
                                status = data.active,
                                updated = DateTime.UtcNow,
                                updated_by = userDetails.pk_id
                            };

                           await  dbContext.tco_org_data_level_3.AddAsync(newOrgData);
                           await  dbContext.SaveChangesAsync();
                        }
                    }
                    else
                    {
                        var existingOrgData = orgVersionContent.lstOrgDataLevel3.FirstOrDefault(x => x.org_id_3 == data.orgCode);
                        if (existingOrgData != null)
                        {
                            existingData.Add(data);
                            existingOrgData.org_short_name_3 = data.orgShortName;
                            existingOrgData.org_name_3 = data.orgLongName;
                            existingOrgData.status = data.active;
                            existingOrgData.updated = DateTime.UtcNow;
                            existingOrgData.updated_by = userDetails.pk_id;

                            await dbContext.SaveChangesAsync();
                        }
                    }
                    _cache.ClearClientCache(clientId);
                    orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);
                }
                await UpdateOrgHierarchy(userId, existingData, 3, orgVersion);

               await StartJobToValidateAdminOrgStructure(userId, orgVersion);

                result.messageType = "success";
                result.message = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_add_edit_success")).Value).LangText;
                return result;
            }
            catch (Exception)
            {
                result.messageType = "error";
                result.message = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_add_edit_error")).Value).LangText;
                return result;
            }
        }

        private async Task<JObject> AddAditOrgLevel4Data(string userId, List<AdminOrgStructureOrgLevelData> uiData, string orgVersion)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            var userDetails = await _utility.GetUserDetailsAsync(userId);
            int clientId = userDetails.client_id;
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "StaffPlanning");
            dynamic result = new JObject();

            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);

            List<AdminOrgStructureOrgLevelData> existingData = new List<AdminOrgStructureOrgLevelData>();

            try
            {
                foreach (var data in uiData)
                {
                    if (data.type == "add")
                    {
                        var newDepartmentDuplicateCheck = orgVersionContent.lstOrgDataLevel4.FirstOrDefault(x => x.org_id_4 == data.orgCode);
                        if (newDepartmentDuplicateCheck != null)
                        {
                            // department already exist
                            result.messageType = "duplicate";
                            result.message = "org code " + data.orgCode + " already exists";
                            return result;
                        }
                        else
                        {
                            tco_org_data_level_4 newOrgData = new tco_org_data_level_4()
                            {
                                org_id_4 = data.orgCode,
                                fk_tenant_id = userDetails.tenant_id,
                                fk_org_version = orgVersionContent.orgVersion,
                                org_name_4 = data.orgLongName,
                                org_short_name_4 = data.orgShortName,
                                status = data.active,
                                updated = DateTime.UtcNow,
                                updated_by = userDetails.pk_id
                            };

                            await dbContext.tco_org_data_level_4.AddAsync(newOrgData);
                           await  dbContext.SaveChangesAsync();
                        }
                    }
                    else
                    {
                        var existingOrgData = orgVersionContent.lstOrgDataLevel4.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id && x.org_id_4 == data.orgCode);
                        if (existingOrgData != null)
                        {
                            existingData.Add(data);
                            existingOrgData.org_short_name_4 = data.orgShortName;
                            existingOrgData.org_name_4 = data.orgLongName;
                            existingOrgData.status = data.active;
                            existingOrgData.updated = DateTime.UtcNow;
                            existingOrgData.updated_by = userDetails.pk_id;

                            await dbContext.SaveChangesAsync();
                        }
                    }
                    _cache.ClearClientCache(clientId);
                    orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);
                }
               await UpdateOrgHierarchy(userId, existingData, 4, orgVersion);

               await  StartJobToValidateAdminOrgStructure(userId, orgVersion);

                result.messageType = "success";
                result.message = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_add_edit_success")).Value).LangText;
                return result;
            }
            catch (Exception)
            {
                result.messageType = "error";
                result.message = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_add_edit_error")).Value).LangText;
                return result;
            }
        }

        private async Task<JObject> AddAditOrgLevel5Data(string userId, List<AdminOrgStructureOrgLevelData> uiData, string orgVersion)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            var userDetails = await _utility.GetUserDetailsAsync(userId);
            int clientId = userDetails.client_id;
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "StaffPlanning");
            dynamic result = new JObject();

            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);

            List<AdminOrgStructureOrgLevelData> existingData = new List<AdminOrgStructureOrgLevelData>();

            try
            {
                foreach (var data in uiData)
                {
                    if (data.type == "add")
                    {
                        var newDepartmentDuplicateCheck = orgVersionContent.lstOrgDataLevel5.FirstOrDefault(x => x.org_id_5 == data.orgCode);
                        if (newDepartmentDuplicateCheck != null)
                        {
                            // department already exist
                            result.messageType = "duplicate";
                            result.message = "org code " + data.orgCode + " already exists";
                            return result;
                        }
                        else
                        {
                            tco_org_data_level_5 newOrgData = new tco_org_data_level_5()
                            {
                                org_id_5 = data.orgCode,
                                fk_tenant_id = userDetails.tenant_id,
                                fk_org_version = orgVersionContent.orgVersion,
                                org_name_5 = data.orgLongName,
                                org_short_name_5 = data.orgShortName,
                                status = data.active,
                                updated = DateTime.UtcNow,
                                updated_by = userDetails.pk_id
                            };

                          await   dbContext.tco_org_data_level_5.AddAsync(newOrgData);
                            await dbContext.SaveChangesAsync();
                        }
                    }
                    else
                    {
                        var existingOrgData = orgVersionContent.lstOrgDataLevel5.FirstOrDefault(x => x.org_id_5 == data.orgCode);
                        if (existingOrgData != null)
                        {
                            existingData.Add(data);
                            existingOrgData.org_short_name_5 = data.orgShortName;
                            existingOrgData.org_name_5 = data.orgLongName;
                            existingOrgData.status = data.active;
                            existingOrgData.updated = DateTime.UtcNow;
                            existingOrgData.updated_by = userDetails.pk_id;

                            await dbContext.SaveChangesAsync();
                        }
                    }
                    _cache.ClearClientCache(clientId);
                    orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);
                }

                await UpdateOrgHierarchy(userId, existingData, 5, orgVersion);

              await  StartJobToValidateAdminOrgStructure(userId, orgVersion);

                _cache.ClearClientCache(clientId);
                result.messageType = "success";
                result.message = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_add_edit_success")).Value).LangText;
                return result;
            }
            catch (Exception)
            {
                result.messageType = "error";
                result.message = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_add_edit_error")).Value).LangText;
                return result;
            }
        }
        private async Task<JObject> AddAditOrgLevel6Data(string userId, List<AdminOrgStructureOrgLevelData> uiData, string orgVersion)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            var userDetails = await _utility.GetUserDetailsAsync(userId);
            int clientId = userDetails.client_id;
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "StaffPlanning");
            dynamic result = new JObject();

            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);

            List<AdminOrgStructureOrgLevelData> existingData = new List<AdminOrgStructureOrgLevelData>();

            try
            {
                foreach (var data in uiData)
                {
                    if (data.type == "add")
                    {
                        var newDepartmentDuplicateCheck = orgVersionContent.lstOrgDataLevel6.FirstOrDefault(x => x.org_id_6 == data.orgCode);
                        if (newDepartmentDuplicateCheck != null)
                        {
                            // department already exist
                            result.messageType = "duplicate";
                            result.message = "org code " + data.orgCode + " already exists";
                            return result;
                        }
                        else
                        {
                            tco_org_data_level_6 newOrgData = new tco_org_data_level_6()
                            {
                                org_id_6 = data.orgCode,
                                fk_tenant_id = userDetails.tenant_id,
                                fk_org_version = orgVersionContent.orgVersion,
                                org_name_6 = data.orgLongName,
                                org_short_name_6 = data.orgShortName,
                                status = data.active,
                                updated = DateTime.UtcNow,
                                updated_by = userDetails.pk_id
                            };

                           await  dbContext.tco_org_data_level_6.AddAsync(newOrgData);
                            await dbContext.SaveChangesAsync();
                        }
                    }
                    else
                    {
                        var existingOrgData = orgVersionContent.lstOrgDataLevel6.FirstOrDefault(x => x.org_id_6 == data.orgCode);
                        if (existingOrgData != null)
                        {
                            existingData.Add(data);
                            existingOrgData.org_short_name_6 = data.orgShortName;
                            existingOrgData.org_name_6 = data.orgLongName;
                            existingOrgData.status = data.active;
                            existingOrgData.updated = DateTime.UtcNow;
                            existingOrgData.updated_by = userDetails.pk_id;

                           await  dbContext.SaveChangesAsync();
                        }
                    }
                    _cache.ClearClientCache(clientId);
                    orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);
                }

                await UpdateOrgHierarchy(userId, existingData, 6, orgVersion);

              await  StartJobToValidateAdminOrgStructure(userId, orgVersion);

                _cache.ClearClientCache(clientId);
                result.messageType = "success";
                result.message = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_add_edit_success")).Value).LangText;
                return result;
            }
            catch (Exception)
            {
                result.messageType = "error";
                result.message = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_add_edit_error")).Value).LangText;
                return result;
            }
        }
        private async Task<JObject> AddAditOrgLevel7Data(string userId, List<AdminOrgStructureOrgLevelData> uiData, string orgVersion)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            var userDetails = await _utility.GetUserDetailsAsync(userId);
            int clientId = userDetails.client_id;
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "StaffPlanning");
            dynamic result = new JObject();

            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);

            List<AdminOrgStructureOrgLevelData> existingData = new List<AdminOrgStructureOrgLevelData>();

            try
            {
                foreach (var data in uiData)
                {
                    if (data.type == "add")
                    {
                        var newDepartmentDuplicateCheck = orgVersionContent.lstOrgDataLevel7.FirstOrDefault(x => x.org_id_7 == data.orgCode);
                        if (newDepartmentDuplicateCheck != null)
                        {
                            // department already exist
                            result.messageType = "duplicate";
                            result.message = "org code " + data.orgCode + " already exists";
                            return result;
                        }
                        else
                        {
                            tco_org_data_level_7 newOrgData = new tco_org_data_level_7()
                            {
                                org_id_7 = data.orgCode,
                                fk_tenant_id = userDetails.tenant_id,
                                fk_org_version = orgVersionContent.orgVersion,
                                org_name_7 = data.orgLongName,
                                org_short_name_7 = data.orgShortName,
                                status = data.active,
                                updated = DateTime.UtcNow,
                                updated_by = userDetails.pk_id
                            };

                          await  dbContext.tco_org_data_level_7.AddAsync(newOrgData);
                           await dbContext.SaveChangesAsync();
                        }
                    }
                    else
                    {
                        var existingOrgData = orgVersionContent.lstOrgDataLevel7.FirstOrDefault(x => x.org_id_7 == data.orgCode);
                        if (existingOrgData != null)
                        {
                            existingData.Add(data);
                            existingOrgData.org_short_name_7 = data.orgShortName;
                            existingOrgData.org_name_7 = data.orgLongName;
                            existingOrgData.status = data.active;
                            existingOrgData.updated = DateTime.UtcNow;
                            existingOrgData.updated_by = userDetails.pk_id;

                           await  dbContext.SaveChangesAsync();
                        }
                    }
                    _cache.ClearClientCache(clientId);
                    orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);
                }

               await UpdateOrgHierarchy(userId, existingData, 7, orgVersion);

             await   StartJobToValidateAdminOrgStructure(userId, orgVersion);

                _cache.ClearClientCache(clientId);
                result.messageType = "success";
                result.message = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_add_edit_success")).Value).LangText;
                return result;
            }
            catch (Exception)
            {
                result.messageType = "error";
                result.message = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_add_edit_error")).Value).LangText;
                return result;
            }
        }
        private async Task<JObject> AddAditOrgLevel8Data(string userId, List<AdminOrgStructureOrgLevelData> uiData, string orgVersion)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            var userDetails = await _utility.GetUserDetailsAsync(userId);
            int clientId = userDetails.client_id;
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "StaffPlanning");
            dynamic result = new JObject();

            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);

            List<AdminOrgStructureOrgLevelData> existingData = new List<AdminOrgStructureOrgLevelData>();

            try
            {
                foreach (var data in uiData)
                {
                    if (data.type == "add")
                    {
                        var newDepartmentDuplicateCheck = orgVersionContent.lstOrgDataLevel8.FirstOrDefault(x => x.org_id_8 == data.orgCode);
                        if (newDepartmentDuplicateCheck != null)
                        {
                            // department already exist
                            result.messageType = "duplicate";
                            result.message = "org code " + data.orgCode + " already exists";
                            return result;
                        }
                        else
                        {
                            tco_org_data_level_8 newOrgData = new tco_org_data_level_8()
                            {
                                org_id_8 = data.orgCode,
                                fk_tenant_id = userDetails.tenant_id,
                                fk_org_version = orgVersionContent.orgVersion,
                                org_name_8 = data.orgLongName,
                                org_short_name_8 = data.orgShortName,
                                status = data.active,
                                updated = DateTime.UtcNow,
                                updated_by = userDetails.pk_id
                            };

                            await dbContext.tco_org_data_level_8.AddAsync(newOrgData);
                            await dbContext.SaveChangesAsync();
                        }
                    }
                    else
                    {
                        var existingOrgData = orgVersionContent.lstOrgDataLevel8.FirstOrDefault(x => x.org_id_8 == data.orgCode);
                        if (existingOrgData != null)
                        {
                            existingData.Add(data);
                            existingOrgData.org_short_name_8 = data.orgShortName;
                            existingOrgData.org_name_8 = data.orgLongName;
                            existingOrgData.status = data.active;
                            existingOrgData.updated = DateTime.UtcNow;
                            existingOrgData.updated_by = userDetails.pk_id;

                            await dbContext.SaveChangesAsync();
                        }
                    }
                    _cache.ClearClientCache(clientId);
                    orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);
                }

                await UpdateOrgHierarchy(userId, existingData, 8, orgVersion);

               await StartJobToValidateAdminOrgStructure(userId, orgVersion);

                _cache.ClearClientCache(clientId);
                result.messageType = "success";
                result.message = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_add_edit_success")).Value).LangText;
                return result;
            }
            catch (Exception)
            {
                result.messageType = "error";
                result.message = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_add_edit_error")).Value).LangText;
                return result;
            }
        }

        private async Task UpdateOrgHierarchy(string userId, List<AdminOrgStructureOrgLevelData> uiDataset, int orgLevel, string orgVersion)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            dbContext.Database.SetCommandTimeout(500);

            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);

            var orgHierarchy = orgVersionContent.lstOrgHierarchy;

            if (orgLevel == 1)
            {
                foreach (var uiData in uiDataset)
                {
                    var dataToUpdate = orgHierarchy.Where(x => x.org_id_1 == uiData.orgCode).ToList();
                    dataToUpdate.ForEach(x =>
                    {
                        x.org_name_1 = uiData.orgLongName;
                        x.org_shortname_1 = uiData.orgShortName;
                    });

                   await dbContext.BulkUpdateAsync(dataToUpdate);
                }
            }

            if (orgLevel == 2)
            {
                foreach (var uiData in uiDataset)
                {
                    var dataToUpdate = orgHierarchy.Where(x => x.org_id_2 == uiData.orgCode).ToList();
                    dataToUpdate.ForEach(x =>
                    {
                        x.org_name_2 = uiData.orgLongName;
                        x.org_shortname_2 = uiData.orgShortName;
                    });

                   await  dbContext.BulkUpdateAsync(dataToUpdate);
                }
            }

            if (orgLevel == 3)
            {
                foreach (var uiData in uiDataset)
                {
                    var dataToUpdate = orgHierarchy.Where(x => x.org_id_3 == uiData.orgCode).ToList();
                    dataToUpdate.ForEach(x =>
                    {
                        x.org_name_3 = uiData.orgLongName;
                        x.org_shortname_3 = uiData.orgShortName;
                    });

                    await dbContext.BulkUpdateAsync(dataToUpdate);
                }
            }

            if (orgLevel == 4)
            {
                foreach (var uiData in uiDataset)
                {
                    var dataToUpdate = orgHierarchy.Where(x => x.org_id_4 == uiData.orgCode).ToList();
                    dataToUpdate.ForEach(x =>
                    {
                        x.org_name_4 = uiData.orgLongName;
                        x.org_shortname_4 = uiData.orgShortName;
                    });

                    await dbContext.BulkUpdateAsync(dataToUpdate);
                }
            }

            if (orgLevel == 5)
            {
                foreach (var uiData in uiDataset)
                {
                    var dataToUpdate = orgHierarchy.Where(x => x.org_id_5 == uiData.orgCode).ToList();
                    dataToUpdate.ForEach(x =>
                    {
                        x.org_name_5 = uiData.orgLongName;
                        x.org_shortname_5 = uiData.orgShortName;
                    });

                    await dbContext.BulkUpdateAsync(dataToUpdate);
                }
            }

            if (orgLevel == 6)
            {
                foreach (var uiData in uiDataset)
                {
                    var dataToUpdate = orgHierarchy.Where(x => x.org_id_6 == uiData.orgCode).ToList();
                    dataToUpdate.ForEach(x =>
                    {
                        x.org_name_6 = uiData.orgLongName;
                        x.org_shortname_6 = uiData.orgShortName;
                    });

                   await dbContext.BulkUpdateAsync(dataToUpdate);
                }
            }

            if (orgLevel == 7)
            {
                foreach (var uiData in uiDataset)
                {
                    var dataToUpdate = orgHierarchy.Where(x => x.org_id_7 == uiData.orgCode).ToList();
                    dataToUpdate.ForEach(x =>
                    {
                        x.org_name_7 = uiData.orgLongName;
                        x.org_shortname_7 = uiData.orgShortName;
                    });

                   await  dbContext.BulkUpdateAsync(dataToUpdate);
                }
            }

            if (orgLevel == 8)
            {
                foreach (var uiData in uiDataset)
                {
                    var dataToUpdate = orgHierarchy.Where(x => x.org_id_8 == uiData.orgCode).ToList();
                    dataToUpdate.ForEach(x =>
                    {
                        x.org_name_8 = uiData.orgLongName;
                        x.org_shortname_8 = uiData.orgShortName;
                    });

                    await dbContext.BulkUpdateAsync(dataToUpdate);
                }
            }

           await StartJobToValidateAdminOrgStructure(userId, orgVersion);
        }

        public async Task<JObject> GetDropdownValuesForOrgHierarchy(string userId, string orgVersion)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            var userdetails = await _utility.GetUserDetailsAsync(userId);

            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);

            var orgLevelDetails = orgVersionContent.lstOrgLevel.OrderByDescending(y => y.org_level).FirstOrDefault();
            var orgHierarchyDepartments = orgVersionContent.lstOrgHierarchy.Select(y => y.fk_department_code).ToList();

            var orgDataLevel1 = orgVersionContent.lstOrgDataLevel1.OrderBy(x=>x.org_id_1).ToList();
            var orgDataLevel2 = orgVersionContent.lstOrgDataLevel2.OrderBy(x => x.org_id_2).ToList();
            var orgDataLevel3 = orgVersionContent.lstOrgDataLevel3.OrderBy(x => x.org_id_3).ToList();
            var orgDataLevel4 = orgVersionContent.lstOrgDataLevel4.OrderBy(x => x.org_id_4).ToList();
            var orgDataLevel5 = orgVersionContent.lstOrgDataLevel5.OrderBy(x => x.org_id_5).ToList();
            var orgDataLevel6 = orgVersionContent.lstOrgDataLevel6.OrderBy(x => x.org_id_6).ToList();
            var orgDataLevel7 = orgVersionContent.lstOrgDataLevel7.OrderBy(x => x.org_id_7).ToList();
            var orgDataLevel8 = orgVersionContent.lstOrgDataLevel8.OrderBy(x => x.org_id_8).ToList();

            var tcoDepartments = await dbContext.tco_departments.Where(x => x.fk_tenant_id == userdetails.tenant_id
                                                                && x.status == 1).ToListAsync();

            var departments = tcoDepartments.Where(x => !orgHierarchyDepartments.Contains(x.pk_department_code)).ToList();

            dynamic result = new JObject();
            dynamic orgDataArray = new JArray();

            foreach (var orgData in orgDataLevel1)
            {
                dynamic obj = new JObject();
                obj.key = orgData.org_id_1;
                obj.value = orgData.org_id_1 + "-" + orgData.org_name_1;
                orgDataArray.Add(obj);
            }

            result.Add("orgData1Array", orgDataArray);

            if (orgLevelDetails.org_level == 2)
            {
                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel2)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_2;
                    obj.value = orgData.org_id_2 + "-" + orgData.org_name_2;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData2Array", orgDataArray);
            }
            else if (orgLevelDetails.org_level == 3)
            {
                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel2)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_2;
                    obj.value = orgData.org_id_2 + "-" + orgData.org_name_2;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData2Array", orgDataArray);

                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel3)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_3;
                    obj.value = orgData.org_id_3 + "-" + orgData.org_name_3;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData3Array", orgDataArray);
            }
            else if (orgLevelDetails.org_level == 4)
            {
                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel2)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_2;
                    obj.value = orgData.org_id_2 + "-" + orgData.org_name_2;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData2Array", orgDataArray);

                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel3)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_3;
                    obj.value = orgData.org_id_3 + "-" + orgData.org_name_3;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData3Array", orgDataArray);

                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel4)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_4;
                    obj.value = orgData.org_id_4 + "-" + orgData.org_name_4;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData4Array", orgDataArray);
            }
            else if (orgLevelDetails.org_level == 5)
            {
                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel2)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_2;
                    obj.value = orgData.org_id_2 + "-" + orgData.org_name_2;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData2Array", orgDataArray);

                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel3)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_3;
                    obj.value = orgData.org_id_3 + "-" + orgData.org_name_3;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData3Array", orgDataArray);

                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel4)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_4;
                    obj.value = orgData.org_id_4 + "-" + orgData.org_name_4;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData4Array", orgDataArray);

                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel5)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_5;
                    obj.value = orgData.org_id_5 + "-" + orgData.org_name_5;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData5Array", orgDataArray);
            }
            else if (orgLevelDetails.org_level == 6)
            {
                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel2)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_2;
                    obj.value = orgData.org_id_2 + "-" + orgData.org_name_2;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData2Array", orgDataArray);

                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel3)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_3;
                    obj.value = orgData.org_id_3 + "-" + orgData.org_name_3;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData3Array", orgDataArray);

                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel4)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_4;
                    obj.value = orgData.org_id_4 + "-" + orgData.org_name_4;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData4Array", orgDataArray);

                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel5)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_5;
                    obj.value = orgData.org_id_5 + "-" + orgData.org_name_5;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData5Array", orgDataArray);

                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel6)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_6;
                    obj.value = orgData.org_id_6 + "-" + orgData.org_name_6;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData6Array", orgDataArray);
            }
            else if (orgLevelDetails.org_level == 7)
            {
                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel2)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_2;
                    obj.value = orgData.org_id_2 + "-" + orgData.org_name_2;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData2Array", orgDataArray);

                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel3)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_3;
                    obj.value = orgData.org_id_3 + "-" + orgData.org_name_3;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData3Array", orgDataArray);

                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel4)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_4;
                    obj.value = orgData.org_id_4 + "-" + orgData.org_name_4;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData4Array", orgDataArray);

                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel5)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_5;
                    obj.value = orgData.org_id_5 + "-" + orgData.org_name_5;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData5Array", orgDataArray);

                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel6)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_6;
                    obj.value = orgData.org_id_6 + "-" + orgData.org_name_6;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData6Array", orgDataArray);

                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel7)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_7;
                    obj.value = orgData.org_id_7 + "-" + orgData.org_name_7;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData7Array", orgDataArray);
            }
            else if (orgLevelDetails.org_level == 8)
            {
                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel2)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_2;
                    obj.value = orgData.org_id_2 + "-" + orgData.org_name_2;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData2Array", orgDataArray);

                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel3)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_3;
                    obj.value = orgData.org_id_3 + "-" + orgData.org_name_3;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData3Array", orgDataArray);

                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel4)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_4;
                    obj.value = orgData.org_id_4 + "-" + orgData.org_name_4;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData4Array", orgDataArray);

                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel5)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_5;
                    obj.value = orgData.org_id_5 + "-" + orgData.org_name_5;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData5Array", orgDataArray);

                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel6)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_6;
                    obj.value = orgData.org_id_6 + "-" + orgData.org_name_6;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData6Array", orgDataArray);

                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel7)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_7;
                    obj.value = orgData.org_id_7 + "-" + orgData.org_name_7;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData7Array", orgDataArray);

                orgDataArray = new JArray();

                foreach (var orgData in orgDataLevel8)
                {
                    dynamic obj = new JObject();
                    obj.key = orgData.org_id_8;
                    obj.value = orgData.org_id_8 + "-" + orgData.org_name_8;
                    orgDataArray.Add(obj);
                }

                result.Add("orgData8Array", orgDataArray);
            }

            orgDataArray = new JArray();
            foreach (var orgData in departments)
            {
                dynamic obj = new JObject();
                obj.key = orgData.pk_department_code;
                obj.value = orgData.pk_department_code + "-" + orgData.department_name;
                orgDataArray.Add(obj);
            }

            result.Add("departmentsArray", orgDataArray);
            return result;
        }

        public async Task<JObject> GetOrgHierarchyColumns(string userId, string orgVersion)
        {
            var userdetails = await _utility.GetUserDetailsAsync(userId);

            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userdetails.language_preference, userdetails.user_name, "StaffPlanning");

            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);

            var orgLevelDetails = orgVersionContent.lstOrgLevel.OrderByDescending(y => y.org_level).FirstOrDefault();

            dynamic columnObj = new JObject();

            dynamic columns = new JArray();
            dynamic mandatoryFields = new JArray();

            columns.Add(FormatColumn("", "pkId", 0, false, true, null, false, 100, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

            columns.Add(FormatColumn("Org Id 1", "orgId1", 0, false, true, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId1.key#", null, null, null, false, null));

            columns.Add(FormatColumn("Org Name 1", "orgName1", 0, false, true, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

            if (orgLevelDetails.org_level == 2)
            {
                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_2")).Value).LangText, "orgId2", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId2.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_2")).Value).LangText, "orgName2", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));
                mandatoryFields.Add("orgId2");
            }
            else if (orgLevelDetails.org_level == 3)
            {
                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_2")).Value).LangText, "orgId2", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId2.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_2")).Value).LangText, "orgName2", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_3")).Value).LangText, "orgId3", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId3.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_3")).Value).LangText, "orgName3", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                mandatoryFields.Add("orgId2");
                mandatoryFields.Add("orgId3");
            }
            else if (orgLevelDetails.org_level == 4)
            {
                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_2")).Value).LangText, "orgId2", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId2.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_2")).Value).LangText, "orgName2", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_3")).Value).LangText, "orgId3", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId3.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_3")).Value).LangText, "orgName3", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_4")).Value).LangText, "orgId4", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId4.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_4")).Value).LangText, "orgName4", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                mandatoryFields.Add("orgId2");
                mandatoryFields.Add("orgId3");
                mandatoryFields.Add("orgId4");
            }
            else if (orgLevelDetails.org_level == 5)
            {
                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_2")).Value).LangText, "orgId2", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId2.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_2")).Value).LangText, "orgName2", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_3")).Value).LangText, "orgId3", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId3.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_3")).Value).LangText, "orgName3", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_4")).Value).LangText, "orgId4", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId4.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_4")).Value).LangText, "orgName4", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_5")).Value).LangText, "orgId5", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId5.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_5")).Value).LangText, "orgName5", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                mandatoryFields.Add("orgId2");
                mandatoryFields.Add("orgId3");
                mandatoryFields.Add("orgId4");
                mandatoryFields.Add("orgId5");
            }
            else if (orgLevelDetails.org_level == 6)
            {
                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_2")).Value).LangText, "orgId2", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId2.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_2")).Value).LangText, "orgName2", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_3")).Value).LangText, "orgId3", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId3.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_3")).Value).LangText, "orgName3", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_4")).Value).LangText, "orgId4", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId4.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_4")).Value).LangText, "orgName4", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_5")).Value).LangText, "orgId5", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId5.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_5")).Value).LangText, "orgName5", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_6")).Value).LangText, "orgId6", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId6.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_6")).Value).LangText, "orgName6", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                mandatoryFields.Add("orgId2");
                mandatoryFields.Add("orgId3");
                mandatoryFields.Add("orgId4");
                mandatoryFields.Add("orgId5");
                mandatoryFields.Add("orgId6");
            }
            else if (orgLevelDetails.org_level == 7)
            {
                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_2")).Value).LangText, "orgId2", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId2.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_2")).Value).LangText, "orgName2", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_3")).Value).LangText, "orgId3", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId3.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_3")).Value).LangText, "orgName3", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_4")).Value).LangText, "orgId4", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId4.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_4")).Value).LangText, "orgName4", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_5")).Value).LangText, "orgId5", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId5.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_5")).Value).LangText, "orgName5", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_6")).Value).LangText, "orgId6", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId6.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_6")).Value).LangText, "orgName6", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_7")).Value).LangText, "orgId7", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId7.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_7")).Value).LangText, "orgName7", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                mandatoryFields.Add("orgId2");
                mandatoryFields.Add("orgId3");
                mandatoryFields.Add("orgId4");
                mandatoryFields.Add("orgId5");
                mandatoryFields.Add("orgId6");
                mandatoryFields.Add("orgId7");
            }
            else if (orgLevelDetails.org_level == 8)
            {
                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_2")).Value).LangText, "orgId2", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId2.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_2")).Value).LangText, "orgName2", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_3")).Value).LangText, "orgId3", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId3.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_3")).Value).LangText, "orgName3", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_4")).Value).LangText, "orgId4", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId4.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_4")).Value).LangText, "orgName4", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_5")).Value).LangText, "orgId5", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId5.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_5")).Value).LangText, "orgName5", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_6")).Value).LangText, "orgId6", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId6.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_6")).Value).LangText, "orgName6", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_7")).Value).LangText, "orgId7", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId7.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_7")).Value).LangText, "orgName7", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_id_8")).Value).LangText, "orgId8", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=orgId8.key#", null, null, null, false, null));

                columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_name_8")).Value).LangText, "orgName8", 0, false, false, null, false, 150, "text-align:left;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, null, null, null, null, false, null));

                mandatoryFields.Add("orgId2");
                mandatoryFields.Add("orgId3");
                mandatoryFields.Add("orgId4");
                mandatoryFields.Add("orgId5");
                mandatoryFields.Add("orgId6");
                mandatoryFields.Add("orgId7");
                mandatoryFields.Add("orgId8");
            }

            columns.Add(FormatColumn(((langStringValues.FirstOrDefault(v => v.Key == "org_structure_dept_name")).Value).LangText, "department", 0, false, false, null, false, 50, "text-align:right;white-space:normal;border-left:none;", "text-align:left;border-left:none;",
                                     null, "#=department.key#", null, null, null, false, null));

            columns.Add(FormatColumn(" ", "search", 0, false, false, null, false, 80, "border-left:none;text-align:right;white-space:normal;", "text-align:right;border-left:none;",
                                     null, null, null, null, null, false, null));

            columns.Add(FormatColumn(" ", "delete", 0, false, false, null, false, 30, "text-align:right;white-space:normal;border-left:none;", "text-align:center;border-left:none;",
                                     null, null, null, null, null, false, null));

            columnObj.Add("columns", columns);

            dynamic headerArray = new JArray();
            dynamic headerObj = new JObject();
            headerObj.title = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_hierarchy_title")).Value).LangText;
            headerObj.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_hierarchy_desc")).Value).LangText;
            headerArray.Add(headerObj);
            columnObj.Add("header", headerArray);
            columnObj.Add("mandatoryColumns", mandatoryFields);

            return columnObj;
        }

        public async Task<JObject> GetOrgHierarchyData(string userId, int skip, int take, AdminOrgHierarchy searchParams, string orgVersion)
        {
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);

            var orgLevelDetails = orgVersionContent.lstOrgLevel.OrderByDescending(y => y.org_level).FirstOrDefault();

            int orgHierarchyDetailsCount = 0;
            var orgHierarchyDetails = orgVersionContent.lstOrgHierarchy
                                                        .OrderBy(y => y.org_id_1)
                                                        .ThenBy(z => z.org_id_2)
                                                        .ThenBy(a => a.org_id_3)
                                                        .ThenBy(b => b.org_id_4)
                                                        .ThenBy(y => y.org_id_5)
                                                        .ThenBy(q => q.org_id_6)
                                                        .ThenBy(v => v.org_id_7)
                                                        .ThenBy(w => w.org_id_8).ToList();

            if (searchParams != null && !string.IsNullOrEmpty(searchParams.orgId2))
            {
                orgHierarchyDetails = orgHierarchyDetails.Where(x => x.org_id_2.Contains(searchParams.orgId2.Trim())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.orgId3))
            {
                orgHierarchyDetails = orgHierarchyDetails.Where(x => x.org_id_3.Contains(searchParams.orgId3.Trim())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.orgId4))
            {
                orgHierarchyDetails = orgHierarchyDetails.Where(x => x.org_id_4.Contains(searchParams.orgId4.Trim())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.orgId5))
            {
                orgHierarchyDetails = orgHierarchyDetails.Where(x => x.org_id_5.Contains(searchParams.orgId5.Trim())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.orgId6))
            {
                orgHierarchyDetails = orgHierarchyDetails.Where(x => x.org_id_6.Contains(searchParams.orgId6.Trim())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.orgId7))
            {
                orgHierarchyDetails = orgHierarchyDetails.Where(x => x.org_id_7.Contains(searchParams.orgId7.Trim())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.orgId8))
            {
                orgHierarchyDetails = orgHierarchyDetails.Where(x => x.org_id_8.Contains(searchParams.orgId8.Trim())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.orgName2))
            {
                orgHierarchyDetails = orgHierarchyDetails.Where(x => x.org_name_2.ToLower().Contains(searchParams.orgName2.ToLower().Trim())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.orgName3))
            {
                orgHierarchyDetails = orgHierarchyDetails.Where(x => x.org_name_3.ToLower().Contains(searchParams.orgName3.ToLower().Trim())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.orgName4))
            {
                orgHierarchyDetails = orgHierarchyDetails.Where(x => x.org_name_4.ToLower().Contains(searchParams.orgName4.ToLower().Trim())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.orgName5))
            {
                orgHierarchyDetails = orgHierarchyDetails.Where(x => x.org_name_5.ToLower().Contains(searchParams.orgName5.ToLower().Trim())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.orgName6))
            {
                orgHierarchyDetails = orgHierarchyDetails.Where(x => x.org_name_6.ToLower().Contains(searchParams.orgName6.ToLower().Trim())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.orgName7))
            {
                orgHierarchyDetails = orgHierarchyDetails.Where(x => x.org_name_7.ToLower().Contains(searchParams.orgName7.ToLower().Trim())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.orgName8))
            {
                orgHierarchyDetails = orgHierarchyDetails.Where(x => x.org_name_8.ToLower().Contains(searchParams.orgName8.ToLower().Trim())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.department))
            {
                orgHierarchyDetails = orgHierarchyDetails.Where(x => x.fk_department_code.Contains(searchParams.department.Trim())).ToList();
            }

            orgHierarchyDetailsCount = orgHierarchyDetails.Any() ? orgHierarchyDetails.Count : 0;
            orgHierarchyDetails = orgHierarchyDetails.Skip(skip).Take(take).ToList();

            dynamic result = new JObject();
            dynamic data = new JArray();
            foreach (var h in orgHierarchyDetails)
            {
                dynamic rowObj = new JObject();
                rowObj.pkId = h.pk_id;

                dynamic obj = new JObject();
                obj.key = h.org_id_1;
                obj.value = h.org_id_1 + "-" + h.org_name_1;

                rowObj.orgId1 = obj;
                rowObj.orgName1 = h.org_name_1;

                if (orgLevelDetails.org_level == 2)
                {
                    obj = new JObject();
                    obj.key = h.org_id_2;
                    obj.value = h.org_id_2 + "-" + h.org_name_2;

                    rowObj.orgId2 = obj;
                    rowObj.orgName2 = h.org_name_2;

                    obj = new JObject();
                    obj.key = "";
                    obj.value = "" + "-" + "";
                    rowObj.orgId3 = obj;
                    rowObj.orgName3 = "";

                    rowObj.orgId4 = obj;
                    rowObj.orgName4 = "";

                    rowObj.orgId5 = obj;
                    rowObj.orgName5 = "";
                }
                else if (orgLevelDetails.org_level == 3)
                {
                    obj = new JObject();
                    obj.key = h.org_id_2;
                    obj.value = h.org_id_2 + "-" + h.org_name_2;

                    rowObj.orgId2 = obj;
                    rowObj.orgName2 = h.org_name_2;

                    obj = new JObject();
                    obj.key = h.org_id_3;
                    obj.value = h.org_id_3 + "-" + h.org_name_3;

                    rowObj.orgId3 = obj;
                    rowObj.orgName3 = h.org_name_3;

                    obj = new JObject();
                    obj.key = "";
                    obj.value = "" + "-" + "";
                    rowObj.orgId4 = obj;
                    rowObj.orgName4 = "";

                    rowObj.orgId5 = obj;
                    rowObj.orgName5 = "";
                }
                else if (orgLevelDetails.org_level == 4)
                {
                    obj = new JObject();
                    obj.key = h.org_id_2;
                    obj.value = h.org_id_2 + "-" + h.org_name_2;

                    rowObj.orgId2 = obj;
                    rowObj.orgName2 = h.org_name_2;

                    obj = new JObject();
                    obj.key = h.org_id_3;
                    obj.value = h.org_id_3 + "-" + h.org_name_3;

                    rowObj.orgId3 = obj;
                    rowObj.orgName3 = h.org_name_3;

                    obj = new JObject();
                    obj.key = h.org_id_4;
                    obj.value = h.org_id_4 + "-" + h.org_name_4;

                    rowObj.orgId4 = obj;
                    rowObj.orgName4 = h.org_name_4;

                    obj = new JObject();
                    obj.key = "";
                    obj.value = "" + "-" + "";

                    rowObj.orgId5 = obj;
                    rowObj.orgName5 = "";
                }
                else if (orgLevelDetails.org_level == 5)
                {
                    obj = new JObject();
                    obj.key = h.org_id_2;
                    obj.value = h.org_id_2 + "-" + h.org_name_2;

                    rowObj.orgId2 = obj;
                    rowObj.orgName2 = h.org_name_2;

                    obj = new JObject();
                    obj.key = h.org_id_3;
                    obj.value = h.org_id_3 + "-" + h.org_name_3;

                    rowObj.orgId3 = obj;
                    rowObj.orgName3 = h.org_name_3;

                    obj = new JObject();
                    obj.key = h.org_id_4;
                    obj.value = h.org_id_4 + "-" + h.org_name_4;

                    rowObj.orgId4 = obj;
                    rowObj.orgName4 = h.org_name_4;

                    obj = new JObject();
                    obj.key = h.org_id_5;
                    obj.value = h.org_id_5 + "-" + h.org_name_5;

                    rowObj.orgId5 = obj;
                    rowObj.orgName5 = h.org_name_5;
                }
                else if (orgLevelDetails.org_level == 6)
                {
                    obj = new JObject();
                    obj.key = h.org_id_2;
                    obj.value = h.org_id_2 + "-" + h.org_name_2;

                    rowObj.orgId2 = obj;
                    rowObj.orgName2 = h.org_name_2;

                    obj = new JObject();
                    obj.key = h.org_id_3;
                    obj.value = h.org_id_3 + "-" + h.org_name_3;

                    rowObj.orgId3 = obj;
                    rowObj.orgName3 = h.org_name_3;

                    obj = new JObject();
                    obj.key = h.org_id_4;
                    obj.value = h.org_id_4 + "-" + h.org_name_4;

                    rowObj.orgId4 = obj;
                    rowObj.orgName4 = h.org_name_4;

                    obj = new JObject();
                    obj.key = h.org_id_5;
                    obj.value = h.org_id_5 + "-" + h.org_name_5;

                    rowObj.orgId5 = obj;
                    rowObj.orgName5 = h.org_name_5;

                    obj = new JObject();
                    obj.key = h.org_id_6;
                    obj.value = h.org_id_6 + "-" + h.org_name_6;

                    rowObj.orgId6 = obj;
                    rowObj.orgName6 = h.org_name_6;
                }
                else if (orgLevelDetails.org_level == 7)
                {
                    obj = new JObject();
                    obj.key = h.org_id_2;
                    obj.value = h.org_id_2 + "-" + h.org_name_2;

                    rowObj.orgId2 = obj;
                    rowObj.orgName2 = h.org_name_2;

                    obj = new JObject();
                    obj.key = h.org_id_3;
                    obj.value = h.org_id_3 + "-" + h.org_name_3;

                    rowObj.orgId3 = obj;
                    rowObj.orgName3 = h.org_name_3;

                    obj = new JObject();
                    obj.key = h.org_id_4;
                    obj.value = h.org_id_4 + "-" + h.org_name_4;

                    rowObj.orgId4 = obj;
                    rowObj.orgName4 = h.org_name_4;

                    obj = new JObject();
                    obj.key = h.org_id_5;
                    obj.value = h.org_id_5 + "-" + h.org_name_5;

                    rowObj.orgId5 = obj;
                    rowObj.orgName5 = h.org_name_5;

                    obj = new JObject();
                    obj.key = h.org_id_6;
                    obj.value = h.org_id_6 + "-" + h.org_name_6;

                    rowObj.orgId6 = obj;
                    rowObj.orgName6 = h.org_name_6;

                    obj = new JObject();
                    obj.key = h.org_id_7;
                    obj.value = h.org_id_7 + "-" + h.org_name_7;

                    rowObj.orgId7 = obj;
                    rowObj.orgName7 = h.org_name_7;
                }
                else if (orgLevelDetails.org_level == 8)
                {
                    obj = new JObject();
                    obj.key = h.org_id_2;
                    obj.value = h.org_id_2 + "-" + h.org_name_2;

                    rowObj.orgId2 = obj;
                    rowObj.orgName2 = h.org_name_2;

                    obj = new JObject();
                    obj.key = h.org_id_3;
                    obj.value = h.org_id_3 + "-" + h.org_name_3;

                    rowObj.orgId3 = obj;
                    rowObj.orgName3 = h.org_name_3;

                    obj = new JObject();
                    obj.key = h.org_id_4;
                    obj.value = h.org_id_4 + "-" + h.org_name_4;

                    rowObj.orgId4 = obj;
                    rowObj.orgName4 = h.org_name_4;

                    obj = new JObject();
                    obj.key = h.org_id_5;
                    obj.value = h.org_id_5 + "-" + h.org_name_5;

                    rowObj.orgId5 = obj;
                    rowObj.orgName5 = h.org_name_5;

                    obj = new JObject();
                    obj.key = h.org_id_6;
                    obj.value = h.org_id_6 + "-" + h.org_name_6;

                    rowObj.orgId6 = obj;
                    rowObj.orgName6 = h.org_name_6;

                    obj = new JObject();
                    obj.key = h.org_id_7;
                    obj.value = h.org_id_7 + "-" + h.org_name_7;

                    rowObj.orgId7 = obj;
                    rowObj.orgName7 = h.org_name_7;

                    obj = new JObject();
                    obj.key = h.org_id_8;
                    obj.value = h.org_id_8 + "-" + h.org_name_8;

                    rowObj.orgId8 = obj;
                    rowObj.orgName8 = h.org_name_8;
                }

                obj = new JObject();
                obj.key = h.fk_department_code;
                obj.value = h.fk_department_code + "-" + h.fk_department_code;

                rowObj.department = obj;

                data.Add(rowObj);
            }

            result.Add("data", data);
            result.totalRows = orgHierarchyDetailsCount;
            return result;
        }

        public async Task<JObject> AddAditOrgHierarchyDataAsync(string userId, List<AdminOrgHierarchy> uiData, bool assignDeptToUsers, string orgVersion)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            dbContext.Database.SetCommandTimeout(300);
            var userDetails = await _utility.GetUserDetailsAsync(userId);
            int clientId = userDetails.client_id;
            dynamic result = new JObject();
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "StaffPlanning");
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);
            var deptLevel = orgVersionContent.lstOrgLevel.Max(y => y.org_level);

            var orgDataLevel1 = orgVersionContent.lstOrgDataLevel1;
            var orgDataLevel2 = orgVersionContent.lstOrgDataLevel2;
            var orgDataLevel3 = orgVersionContent.lstOrgDataLevel3;
            var orgDataLevel4 = orgVersionContent.lstOrgDataLevel4;
            var orgDataLevel5 = orgVersionContent.lstOrgDataLevel5;
            var orgDataLevel6 = orgVersionContent.lstOrgDataLevel6;
            var orgDataLevel7 = orgVersionContent.lstOrgDataLevel7;
            var orgDataLevel8 = orgVersionContent.lstOrgDataLevel8;

            orgDataLevel1.ForEach(x => {
                x.org_short_name_1 = string.IsNullOrEmpty(x.org_short_name_1) ? string.Empty : x.org_short_name_1;
            });

            orgDataLevel2.ForEach(x => {
                x.org_short_name_2 = string.IsNullOrEmpty(x.org_short_name_2) ? string.Empty : x.org_short_name_2;
            });

            orgDataLevel3.ForEach(x => {
                x.org_short_name_3 = string.IsNullOrEmpty(x.org_short_name_3) ? string.Empty : x.org_short_name_3;
            });

            orgDataLevel4.ForEach(x => {
                x.org_short_name_4 = string.IsNullOrEmpty(x.org_short_name_4) ? string.Empty : x.org_short_name_4;
            });

            orgDataLevel5.ForEach(x => {
                x.org_short_name_5 = string.IsNullOrEmpty(x.org_short_name_5) ? string.Empty : x.org_short_name_5;
            });

            orgDataLevel6.ForEach(x => {
                x.org_short_name_6 = string.IsNullOrEmpty(x.org_short_name_6) ? string.Empty : x.org_short_name_6;
            });

            orgDataLevel7.ForEach(x => {
                x.org_short_name_7 = string.IsNullOrEmpty(x.org_short_name_7) ? string.Empty : x.org_short_name_7;
            });

            orgDataLevel8.ForEach(x => {
                x.org_short_name_8 = string.IsNullOrEmpty(x.org_short_name_8) ? string.Empty : x.org_short_name_8;
            });
            foreach (var data in uiData)
            {
                if (data.type == "add")
                {
                    var orgLevel1 = orgVersionContent.lstOrgDataLevel1.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id && x.fk_org_version == orgVersionContent.orgVersion);

                    tco_org_hierarchy newrow = new tco_org_hierarchy()
                    {
                        fk_tenant_id = userDetails.tenant_id,
                        fk_org_version = orgVersionContent.orgVersion,
                        org_id_1 = orgLevel1 != null ? orgLevel1.org_id_1 : "",
                        org_id_2 = data.orgId2.Trim(),
                        org_id_3 = data.orgId3.Trim(),
                        org_id_4 = data.orgId4.Trim(),
                        org_id_5 = data.orgId5.Trim(),
                        org_id_6 = data.orgId6.Trim(),
                        org_id_7 = data.orgId7.Trim(),
                        org_id_8 = data.orgId8.Trim(),
                        org_name_1 = orgLevel1 != null ? orgLevel1.org_name_1 : "",
                        org_name_2 = data.orgName2.Trim(),
                        org_name_3 = data.orgName3.Trim(),
                        org_name_4 = data.orgName4.Trim(),
                        org_name_5 = data.orgName5.Trim(),
                        org_name_6 = data.orgName6.Trim(),
                        org_name_7 = data.orgName7.Trim(),
                        org_name_8 = data.orgName8.Trim(),
                        fk_department_code = data.department,
                        org_shortname_1 = (orgDataLevel1.Any() && orgDataLevel1.FirstOrDefault(x => x.org_id_1 == data.orgId1) != null && !string.IsNullOrEmpty(orgDataLevel1.FirstOrDefault(x => x.org_id_1 == data.orgId1).org_short_name_1)) ? orgDataLevel1.FirstOrDefault(x => x.org_id_1 == data.orgId1).org_short_name_1.Trim() : string.Empty,
                        org_shortname_2 = (orgDataLevel2.Any() && orgDataLevel2.FirstOrDefault(x => x.org_id_2 == data.orgId2) != null && !string.IsNullOrEmpty(orgDataLevel2.FirstOrDefault(x => x.org_id_2 == data.orgId2).org_short_name_2)) ? orgDataLevel2.FirstOrDefault(x => x.org_id_2 == data.orgId2).org_short_name_2.Trim() : string.Empty,
                        org_shortname_3 = (orgDataLevel3.Any() && orgDataLevel3.FirstOrDefault(x => x.org_id_3 == data.orgId3) != null && !string.IsNullOrEmpty(orgDataLevel3.FirstOrDefault(x => x.org_id_3 == data.orgId3).org_short_name_3)) ? orgDataLevel3.FirstOrDefault(x => x.org_id_3 == data.orgId3).org_short_name_3.Trim() : string.Empty,
                        org_shortname_4 = (orgDataLevel4.Any() && orgDataLevel4.FirstOrDefault(x => x.org_id_4 == data.orgId4) != null && !string.IsNullOrEmpty(orgDataLevel4.FirstOrDefault(x => x.org_id_4 == data.orgId4).org_short_name_4)) ? orgDataLevel4.FirstOrDefault(x => x.org_id_4 == data.orgId4).org_short_name_4.Trim() : string.Empty,
                        org_shortname_5 = (orgDataLevel5.Any() && orgDataLevel5.FirstOrDefault(x => x.org_id_5 == data.orgId5) != null && !string.IsNullOrEmpty(orgDataLevel5.FirstOrDefault(x => x.org_id_5 == data.orgId5).org_short_name_5)) ? orgDataLevel5.FirstOrDefault(x => x.org_id_5 == data.orgId5).org_short_name_5.Trim() : string.Empty,
                        org_shortname_6 = (orgDataLevel6.Any() && orgDataLevel6.FirstOrDefault(x => x.org_id_6 == data.orgId6) != null && !string.IsNullOrEmpty(orgDataLevel6.FirstOrDefault(x => x.org_id_6 == data.orgId6).org_short_name_6)) ? orgDataLevel6.FirstOrDefault(x => x.org_id_6 == data.orgId6).org_short_name_6.Trim() : string.Empty,
                        org_shortname_7 = (orgDataLevel7.Any() && orgDataLevel7.FirstOrDefault(x => x.org_id_7 == data.orgId7) != null && !string.IsNullOrEmpty(orgDataLevel7.FirstOrDefault(x => x.org_id_7 == data.orgId7).org_short_name_7)) ? orgDataLevel7.FirstOrDefault(x => x.org_id_7 == data.orgId7).org_short_name_7.Trim() : string.Empty,
                        org_shortname_8 = (orgDataLevel8.Any() && orgDataLevel8.FirstOrDefault(x => x.org_id_8 == data.orgId8) != null && !string.IsNullOrEmpty(orgDataLevel8.FirstOrDefault(x => x.org_id_8 == data.orgId8).org_short_name_8)) ? orgDataLevel8.FirstOrDefault(x => x.org_id_8 == data.orgId8).org_short_name_8.Trim() : string.Empty,
                        updated = DateTime.UtcNow,
                        updated_by = userDetails.pk_id
                    };
                   await dbContext.tco_org_hierarchy.AddAsync(newrow);
                    await dbContext.SaveChangesAsync();
                    if (assignDeptToUsers)
                    {
                        switch (deptLevel)
                        {
                            case 8:
                                await AssignDepartmentsToUsersAsync(userId, orgVersionContent.orgVersion, 7, data.orgId7, data.orgId8, 8, orgVersionContent);
                                break;

                            case 7:
                                await AssignDepartmentsToUsersAsync(userId, orgVersionContent.orgVersion, 6, data.orgId6, data.orgId7, 7, orgVersionContent);
                                break;

                            case 6:
                                await AssignDepartmentsToUsersAsync(userId, orgVersionContent.orgVersion, 5, data.orgId5, data.orgId6, 6, orgVersionContent);
                                break;

                            case 5:
                                await AssignDepartmentsToUsersAsync(userId, orgVersionContent.orgVersion, 4, data.orgId4, data.orgId5, 5, orgVersionContent);
                                break;

                            case 4:
                                await AssignDepartmentsToUsersAsync(userId, orgVersionContent.orgVersion, 3, data.orgId3, data.orgId4, 4, orgVersionContent);
                                break;

                            case 3:
                                await AssignDepartmentsToUsersAsync(userId, orgVersionContent.orgVersion, 2, data.orgId2, data.orgId3, 3, orgVersionContent);
                                break;

                            case 2:
                                await AssignDepartmentsToUsersAsync(userId, orgVersionContent.orgVersion, 1, data.orgId1, data.orgId2, 2, orgVersionContent);
                                break;

                            default:
                                break;
                        }
                    }
                }
                else
                {
                    var esistingRow = orgVersionContent.lstOrgHierarchy.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                                   && x.fk_org_version == orgVersionContent.orgVersion
                                                                                                   && x.pk_id == data.pkId);
                    if (esistingRow != null)
                    {
                        esistingRow.org_id_1 = data.orgId1.Trim();
                        esistingRow.org_id_2 = data.orgId2.Trim();
                        esistingRow.org_id_3 = data.orgId3.Trim();
                        esistingRow.org_id_4 = data.orgId4.Trim();
                        esistingRow.org_id_5 = data.orgId5.Trim();
                        esistingRow.org_id_6 = data.orgId6.Trim();
                        esistingRow.org_id_7 = data.orgId7.Trim();
                        esistingRow.org_id_8 = data.orgId8.Trim();
                        esistingRow.org_name_1 = data.orgName1.Trim();
                        esistingRow.org_name_2 = data.orgName2.Trim();
                        esistingRow.org_name_3 = data.orgName3.Trim();
                        esistingRow.org_name_4 = data.orgName4.Trim();
                        esistingRow.org_name_5 = data.orgName5.Trim();
                        esistingRow.org_name_6 = data.orgName6.Trim();
                        esistingRow.org_name_7 = data.orgName7.Trim();
                        esistingRow.org_name_8 = data.orgName8.Trim();
                        esistingRow.fk_department_code = data.department;

                        if (orgDataLevel1.FirstOrDefault(x => x.org_id_1 == data.orgId1) == null ||
                            orgDataLevel1.FirstOrDefault(x => x.org_id_1 == data.orgId1).org_short_name_1 == null)
                        {
                            esistingRow.org_shortname_1 = string.Empty;
                        }
                        else
                        {
                            esistingRow.org_shortname_1 = orgDataLevel1.FirstOrDefault(x => x.org_id_1 == data.orgId1).org_short_name_1.Trim();
                        }

                        if (orgDataLevel2.FirstOrDefault(x => x.org_id_2 == data.orgId2) == null ||
                            orgDataLevel2.FirstOrDefault(x => x.org_id_2 == data.orgId2).org_short_name_2 == null)
                        {
                            esistingRow.org_shortname_2 = string.Empty;
                        }
                        else
                        {
                            esistingRow.org_shortname_2 = orgDataLevel2.FirstOrDefault(x => x.org_id_2 == data.orgId2).org_short_name_2.Trim();
                        }

                        if (orgDataLevel3.FirstOrDefault(x => x.org_id_3 == data.orgId3) == null ||
                            orgDataLevel3.FirstOrDefault(x => x.org_id_3 == data.orgId3).org_short_name_3 == null)
                        {
                            esistingRow.org_shortname_3 = string.Empty;
                        }
                        else
                        {
                            esistingRow.org_shortname_3 = orgDataLevel3.FirstOrDefault(x => x.org_id_3 == data.orgId3).org_short_name_3.Trim();
                        }

                        if (orgDataLevel4.FirstOrDefault(x => x.org_id_4 == data.orgId4) == null ||
                            orgDataLevel4.FirstOrDefault(x => x.org_id_4 == data.orgId4).org_short_name_4 == null)
                        {
                            esistingRow.org_shortname_4 = string.Empty;
                        }
                        else
                        {
                            esistingRow.org_shortname_4 = orgDataLevel4.FirstOrDefault(x => x.org_id_4 == data.orgId4).org_short_name_4.Trim();
                        }

                        if (orgDataLevel5.FirstOrDefault(x => x.org_id_5 == data.orgId5) == null ||
                            orgDataLevel5.FirstOrDefault(x => x.org_id_5 == data.orgId5).org_short_name_5 == null)
                        {
                            esistingRow.org_shortname_5 = string.Empty;
                        }
                        else
                        {
                            esistingRow.org_shortname_5 = orgDataLevel5.FirstOrDefault(x => x.org_id_5 == data.orgId5).org_short_name_5.Trim();
                        }

                        if (orgDataLevel6.FirstOrDefault(x => x.org_id_6 == data.orgId6) == null || 
                            orgDataLevel6.FirstOrDefault(x => x.org_id_6 == data.orgId6).org_short_name_6 == null)
                        {
                            esistingRow.org_shortname_6 = string.Empty;
                        }
                        else
                        {
                            esistingRow.org_shortname_6 = orgDataLevel6.FirstOrDefault(x => x.org_id_6 == data.orgId6).org_short_name_6.Trim();
                        }

                        if (orgDataLevel7.FirstOrDefault(x => x.org_id_7 == data.orgId7) == null ||
                            orgDataLevel7.FirstOrDefault(x => x.org_id_7 == data.orgId7).org_short_name_7 == null)
                        {
                            esistingRow.org_shortname_7 = string.Empty;
                        }
                        else
                        {
                            esistingRow.org_shortname_7 = orgDataLevel7.FirstOrDefault(x => x.org_id_7 == data.orgId7).org_short_name_7.Trim();
                        }

                        if (orgDataLevel8.FirstOrDefault(x => x.org_id_8 == data.orgId8) == null ||
                            orgDataLevel8.FirstOrDefault(x => x.org_id_8 == data.orgId8).org_short_name_8 == null)
                        {
                            esistingRow.org_shortname_8 = string.Empty;
                        }
                        else
                        {
                            esistingRow.org_shortname_8 = orgDataLevel8.FirstOrDefault(x => x.org_id_8 == data.orgId8).org_short_name_8.Trim();
                        }

                        esistingRow.updated = DateTime.UtcNow;
                        esistingRow.updated_by = userDetails.pk_id;

                        await dbContext.SaveChangesAsync();
                    }
                }
                _cache.ClearClientCache(clientId);
                orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);
            }

            await StartJobToValidateAdminOrgStructure(userId, orgVersion);

            result.messageType = "success";
            result.message = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_add_edit_success")).Value).LangText;
            _staffPlanning.CreateSpFilterJob(userId, DateTime.Now.Year, 0, null, false);

            return result;
        }

        public async Task AssignDepartmentsToUsersAsync(string userId, string orgVersions, int immediateLevelAboveDept, string immediateLevelValueAboveDept, string department, int departmentLevel, 
                                                   ClsOrgVersionSpecificContent orgVersionSpecificContent)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            dbContext.Database.SetCommandTimeout(500);
            var userDetails = await _utility.GetUserDetailsAsync(userId);
            int clientId = userDetails.client_id;
            orgVersionSpecificContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersions);

            var usersAboveDeptLevel = await dbContext.tco_user_orgrole.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                 && x.fk_org_version == orgVersions
                                                                 && x.hierarchy_level == immediateLevelAboveDept
                                                                 && x.fk_org_id == immediateLevelValueAboveDept).Select(y => y.fk_user_id).ToListAsync();

            var usersAtDeptLevel = await dbContext.tco_user_orgrole.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                 && x.fk_org_version == orgVersions
                                                                 && x.hierarchy_level == departmentLevel
                                                                 && x.fk_org_id == department).Select(y => y.fk_user_id).ToListAsync();

            usersAboveDeptLevel = usersAboveDeptLevel.OrderBy(x => x).Distinct().ToList();

            var usersToInsert = usersAboveDeptLevel.Where(x => !usersAtDeptLevel.Contains(x)).ToList();

            var lstToAdd = new List<tco_user_orgrole>();

            foreach (var u in usersToInsert)
            {
                var userOrgRole = new tco_user_orgrole()
                {
                    fk_tenant_id = userDetails.tenant_id,
                    fk_org_id = department,
                    fk_org_version = orgVersions,
                    hierarchy_level = departmentLevel,
                    fk_user_id = u,
                    isManager = 0,
                    isBuddelegate = 1,
                    isBelonging = 0,
                    updated = DateTime.UtcNow,
                    updated_by = userDetails.pk_id
                };

                lstToAdd.Add(userOrgRole);                
            }

            await dbContext.BulkInsertAsync(lstToAdd);
            await dbContext.SaveChangesAsync();
            await _cache.ClearClientCacheAsync(clientId);
        }

            public async Task AssignDepartmentsToUsersThroughImportOrgHierarchyAsync(UserOrgRoleInfoHelper userOrgInfo)
        {
                var dbContext = await _utility.GetTenantDBContextAsync();
                dbContext.Database.SetCommandTimeout(600);
                var userDetails = await _utility.GetUserDetailsAsync(userOrgInfo.UserId);
                int clientId = userDetails.client_id;

                var usersAboveDeptLevelAsync = await dbContext.tco_user_orgrole.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                     && x.fk_org_version == userOrgInfo.OrgVersion
                                                                     && x.hierarchy_level == userOrgInfo.ImmediateLevelAboveDept
                                                                     ).ToListAsync();

                var usersAboveDeptLevel = usersAboveDeptLevelAsync.Where(x => userOrgInfo.ImmediateLevelValueAboveDept.Contains(x.fk_org_id)).Select(y => y.fk_user_id).ToList();

                var usersAtDeptLevelAsync = await dbContext.tco_user_orgrole.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                     && x.fk_org_version == userOrgInfo.OrgVersion
                                                                     && x.hierarchy_level == userOrgInfo.DepartmentLevel).ToListAsync();

                var orgIds = userOrgInfo.Department.Select(x => x.fk_org_id).ToList();
                orgIds = orgIds.Where(x => x != string.Empty).ToList();

                var usersAtCurLevel= usersAtDeptLevelAsync.Where(x => orgIds.Contains(x.fk_org_id)).Select(y => y.fk_user_id).ToList();
                var usersAtDeptLevel = usersAtDeptLevelAsync.Where(x=>!orgIds.Contains(x.fk_org_id)).Select(y => y.fk_user_id).ToList();
            var intersectionList = usersAtCurLevel.Intersect(usersAtDeptLevel).ToList();
            List<int> usersToInsert;
            if (intersectionList.Any())
            {
                usersAtDeptLevel.Clear();
                usersAtDeptLevel.AddRange(intersectionList);
                usersToInsert = usersAtDeptLevel;
            }
            else
            {
                usersAboveDeptLevel = usersAboveDeptLevel.OrderBy(x => x).Distinct().ToList();
                usersAtDeptLevel= usersAtDeptLevelAsync.Where(x => orgIds.Contains(x.fk_org_id)).Select(y => y.fk_user_id).ToList();
                usersToInsert = usersAboveDeptLevel.Where(x => !usersAtDeptLevel.Contains(x)).Distinct().ToList();
            }

            var existingUsers = await dbContext.tco_user_orgrole.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                    && x.fk_org_version == userOrgInfo.OrgVersion
                                                                    && usersToInsert.Contains(x.fk_user_id)
                                                                    && orgIds.Contains(x.fk_org_id)
                                                                    ).Distinct().ToListAsync();

            var lstToAdd = new List<tco_user_orgrole>();

            try
            {
                foreach (var u in usersToInsert)
                {
                    foreach (var dept in userOrgInfo.Department)
                    {
                        var parentOrgId = userOrgInfo.ParentLevelData.Any(x => x.CurrentOrgId == dept.fk_org_id) ? userOrgInfo.ParentLevelData.First(x => x.CurrentOrgId == dept.fk_org_id).ParentOrgId : string.Empty;
                        bool isUserAccessToAboveLevel = !string.IsNullOrEmpty(parentOrgId) && usersAboveDeptLevelAsync.Any(x => x.fk_org_id == parentOrgId && x.fk_user_id == u);
                        if (isUserAccessToAboveLevel)
                        {
                            if (!existingUsers.Any(x=>x.fk_user_id==u && x.fk_org_id == dept.fk_org_id))
                            {
                                var userOrgRole = new tco_user_orgrole()
                                {
                                    fk_tenant_id = userDetails.tenant_id,
                                    fk_org_id = dept.fk_org_id,
                                    fk_org_version = dept.fk_org_version,
                                    hierarchy_level = userOrgInfo.DepartmentLevel,
                                    fk_user_id = u,
                                    isManager = 0,
                                    isBuddelegate = 1,
                                    isBelonging = 0,
                                    updated = DateTime.UtcNow,
                                    updated_by = userDetails.pk_id
                                };

                                lstToAdd.Add(userOrgRole);
                            }
                        }
                    }
                    await dbContext.BulkInsertAsync(lstToAdd);
                    await dbContext.SaveChangesAsync();
                    lstToAdd.RemoveRange(0, lstToAdd.Count);
                }
            }
            catch (Exception ex)
            {

                throw ex;
            }

            await _cache.ClearClientCacheAsync(clientId);
        }

        public async Task<AdminOrgMessage> DeleteOrgHierarchyData(string userId, int pkId, string orgVersion)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            var userDetails = await _utility.GetUserDetailsAsync(userId);
            int clientId = userDetails.client_id;

            Dictionary<string, clsLanguageString> langStringValue = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "AdmOrgStructure");
            ClsOrgVersionSpecificContent orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);

            tco_org_hierarchy dataToDelete = orgVersionContent.lstOrgHierarchy.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                                    && x.fk_org_version == orgVersionContent.orgVersion
                                                                                                    && x.pk_id == pkId);

            if (dataToDelete != null)
            {
               dbContext.tco_org_hierarchy.Remove(dataToDelete);
               await dbContext.SaveChangesAsync();

                _cache.ClearClientCache(clientId);

                await StartJobToValidateAdminOrgStructure(userId, orgVersion);

                return new AdminOrgMessage() { messageType = "success", message = langStringValue["adm_orgStructure_successMsg"].LangText };
            }
            else
            {
                return new AdminOrgMessage() { messageType = "error", message = langStringValue["adm_orgStructure_errorMsg"].LangText };
            }
        }

        public async Task<JObject> ShowValidationErrors(string userId)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            var userdetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringValue = await  _utility.GetLanguageStringsAsync(userdetails.language_preference, userdetails.user_name, "AdmOrgStructure");
            List<int> validationIds = new List<int>();
            for (int i = 1001; i <= 2000; i++)
            {
                validationIds.Add(i);
            }

            var errors = await dbContext.tco_org_validations.Where(x => x.fk_tenant_id == userdetails.tenant_id && !validationIds.Contains(x.validation_id) && x.validation_id != projectErrorCode).ToListAsync();
            var errorDescriptions = await dbContext.gco_org_validations.ToListAsync();

            dynamic result = new JObject();
            List<string> errorList = new List<string>();

            foreach (var error in errors)
            {
                var description = ((langStringValue.FirstOrDefault(v => v.Key == (errorDescriptions.FirstOrDefault(x => x.validation_id == error.validation_id).lang_string_id))).Value).LangText;
                string modifiedDescription = description.Replace("{$errror_code}", (string.IsNullOrEmpty(error.error_value) || error.error_value.Trim().Length == 0) ?
                                            "''" + ((langStringValue.FirstOrDefault(v => v.Key == "adm_org_empty_error_code")).Value).LangText + "''" :
                                            error.error_value);

                errorList.Add(modifiedDescription);
            }

            bool isChapterSetup = await _finUtility.isChapterSetup(userId);
            var tenantData = await _finUtility.GetSyncSubTenantData(userdetails.tenant_id);
            if(!tenantData.Any() && isChapterSetup)
            {
                string langstring = langStringValue.FirstOrDefault(v => v.Key == "adm_depart_not_connected").Value.LangText;
                string deptLangstring = langStringValue.FirstOrDefault(v => v.Key == "adm_fp_department").Value.LangText;
                var departNotConnectedChapter = await GetDepartNotConnectedAtrribute(userId, langstring, deptLangstring);
                errorList.AddRange(departNotConnectedChapter);
            }

            result.Add("errors", JToken.FromObject(errorList));

            return result;
        }

        public async Task PerformValidations(string userId, string orgVersion)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            dbContext.Database.SetCommandTimeout(500);
            var userdetails = await _utility.GetUserDetailsAsync(userId);

            prcValidationOfOrgData spValidate = new prcValidationOfOrgData();
            spValidate.tenant_id = userdetails.tenant_id;
            spValidate.OrgVersion = orgVersion;
            await dbContext.Database.ExecuteStoredProcedureAsync(spValidate);
        }

        public async Task PerformValidations2(string userId, string orgVersion)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            dbContext.Database.SetCommandTimeout(500);
            var userdetails = await _utility.GetUserDetailsAsync(userId);

            var lowestOrgLevel = (await dbContext.tco_org_level.Where(x => x.fk_tenant_id == userdetails.tenant_id && x.fk_org_version == orgVersion).OrderByDescending(y => y.org_level).FirstOrDefaultAsync()).org_level;

            AuthenticationDBContext authenticationDBContext = _utility.GetAuthenticationContext();
            /*fp central departments*/
            var fpCentralDepartments = await (from v in dbContext.vw_tco_parameters
                                        where v.fk_tenant_id == userdetails.tenant_id
                                        && v.param_name.ToLower() == "FP_CENTRAL_DEPARTMENTS".ToLower()
                                        select v.param_value).Distinct().ToListAsync();

            /*Retrieve users with role 3 and 4*/
            var usersWithRoles3And4 = await (from a in authenticationDBContext.tco_auth_user_role_mapping
                                        where a.tenant_id == userdetails.tenant_id
                                        && (a.fk_role_id == 3 || a.fk_role_id == 4)
                                        select a.fk_user_id).Distinct().ToListAsync();

            if (lowestOrgLevel == 5)
            {
                /*step 1. Retrieve common data*/
                var commonData = await (from a in dbContext.tco_org_hierarchy
                                    join b in dbContext.tco_user_orgrole on new { a = a.fk_tenant_id, b = a.fk_org_version, c = a.org_id_5 } 
                                                                     equals new { a = b.fk_tenant_id, b = b.fk_org_version, c = b.fk_org_id }
                                    where a.fk_tenant_id == userdetails.tenant_id
                                    && b.fk_org_version == orgVersion
                                    && b.hierarchy_level == 5
                                    select new
                                    {
                                        a.org_id_5,
                                        a.org_name_5,
                                        b.fk_user_id,
                                        a.fk_department_code
                                    }).Distinct().ToListAsync();

                /*departments in common data should not be present in central departments*/
                if (fpCentralDepartments.Any())
                {
                    commonData = commonData.Where(x => !fpCentralDepartments.Contains(x.fk_department_code)).ToList();
                }

                var commonDataOrgIds = commonData.Select(x => x.org_id_5).Distinct().ToList();

                var orgIdsWhichAreAssignedToUsersWithRole3And4 = (from a in usersWithRoles3And4
                                                                    join b in commonData on a equals b.fk_user_id
                                                                    orderby b.org_id_5
                                                                    select b.org_id_5).Distinct().ToList();

                /*out of the common data check if any of these org ids are not assigned to users with role 3 and 4*/
                var orgIdsWhichAreNotAssignedToUsersWithRole3And4 = commonData.Where(x => !orgIdsWhichAreAssignedToUsersWithRole3And4.Contains(x.org_id_5)).Select(y => y.org_id_5).Distinct().ToList();

                /*step 2. Check for data not in common data, these must have been assigned to the users with roles 3 and 4*/
                var orgIdsWhichAreNotPresentInCommonData = await (from a in dbContext.tco_org_hierarchy
                                                            where a.fk_tenant_id == userdetails.tenant_id
                                                            && a.fk_org_version == orgVersion
                                                            && !commonDataOrgIds.Contains(a.org_id_5)
                                                            select new
                                                            {
                                                                a.org_id_5,
                                                                a.fk_department_code
                                                            }).Distinct().ToListAsync();

                if (fpCentralDepartments.Any())
                {
                    orgIdsWhichAreNotPresentInCommonData = orgIdsWhichAreNotPresentInCommonData.Where(x => !fpCentralDepartments.Contains(x.fk_department_code)).ToList();
                }

                var distinctOrgIdsToInsert = orgIdsWhichAreNotAssignedToUsersWithRole3And4.Concat(orgIdsWhichAreNotPresentInCommonData.Select(x => x.org_id_5).ToList())
                                            .Distinct().ToList();

                foreach (var orgid in distinctOrgIdsToInsert)
                {
                    tco_org_validations ov = new tco_org_validations()
                    {
                        fk_tenant_id = userdetails.tenant_id,
                        validation_id = 34,
                        error_value = orgid
                    };

                  await   dbContext.tco_org_validations.AddAsync(ov);
                  await   dbContext.SaveChangesAsync();
                }
            }
            else if (lowestOrgLevel == 4)
            {
                /*step 1. Retrieve common data*/
                var commonData = await(from a in dbContext.tco_org_hierarchy
                                    join b in dbContext.tco_user_orgrole on new { a = a.fk_tenant_id, b = a.fk_org_version, c = a.org_id_4 } 
                                                                     equals new { a = b.fk_tenant_id, b = b.fk_org_version, c = b.fk_org_id }
                                    where a.fk_tenant_id == userdetails.tenant_id
                                    && b.fk_org_version == orgVersion
                                    && b.hierarchy_level == 4
                                    select new
                                    {
                                        a.org_id_4,
                                        a.org_name_4,
                                        b.fk_user_id,
                                        a.fk_department_code
                                    }).Distinct().ToListAsync();

                /*departments in common data should not be present in central departments*/
                if (fpCentralDepartments.Any())
                {
                    commonData = commonData.Where(x => !fpCentralDepartments.Contains(x.fk_department_code)).ToList();
                }

                var commonDataOrgIds = commonData.Select(x => x.org_id_4).Distinct().ToList();

                var orgIdsWhichAreAssignedToUsersWithRole3And4 = (from a in usersWithRoles3And4
                                                                    join b in commonData on a equals b.fk_user_id
                                                                    orderby b.org_id_4
                                                                    select b.org_id_4).Distinct().ToList();

                /*out of the common data check if any of these org ids are not assigned to users with role 3 and 4*/
                var orgIdsWhichAreNotAssignedToUsersWithRole3And4 = commonData.Where(x => !orgIdsWhichAreAssignedToUsersWithRole3And4.Contains(x.org_id_4)).Select(y => y.org_id_4).Distinct().ToList();

                /*step 2. Check for data not in common data, these must have been assigned to the users with roles 3 and 4*/
                var orgIdsWhichAreNotPresentInCommonData = await (from a in dbContext.tco_org_hierarchy
                                                            where a.fk_tenant_id == userdetails.tenant_id
                                                            && a.fk_org_version == orgVersion
                                                            && !commonDataOrgIds.Contains(a.org_id_4)
                                                            select new
                                                            {
                                                                a.org_id_4,
                                                                a.fk_department_code
                                                            }).Distinct().ToListAsync();

                if (fpCentralDepartments.Any())
                {
                    orgIdsWhichAreNotPresentInCommonData = orgIdsWhichAreNotPresentInCommonData.Where(x => !fpCentralDepartments.Contains(x.fk_department_code)).ToList();
                }

                var distinctOrgIdsToInsert = orgIdsWhichAreNotAssignedToUsersWithRole3And4.Concat(orgIdsWhichAreNotPresentInCommonData.Select(x => x.org_id_4).ToList()).Distinct().ToList();

                foreach (var orgid in distinctOrgIdsToInsert)
                {
                    tco_org_validations ov = new tco_org_validations()
                    {
                        fk_tenant_id = userdetails.tenant_id,
                        validation_id = 34,
                        error_value = orgid
                    };

                   await dbContext.tco_org_validations.AddAsync(ov);
                   await  dbContext.SaveChangesAsync();
                }
            }
            else if (lowestOrgLevel == 3)
            {
                /*step 1. Retrieve common data*/
                var commonData = await (from a in dbContext.tco_org_hierarchy
                                    join b in dbContext.tco_user_orgrole on new { a = a.fk_tenant_id, b = a.fk_org_version, c = a.org_id_3 } 
                                                                     equals new { a = b.fk_tenant_id, b = b.fk_org_version, c = b.fk_org_id }
                                    where a.fk_tenant_id == userdetails.tenant_id
                                    && b.fk_org_version == orgVersion
                                    && b.hierarchy_level == 3
                                    select new
                                    {
                                        a.org_id_3,
                                        a.org_name_3,
                                        b.fk_user_id,
                                        a.fk_department_code
                                    }).Distinct().ToListAsync();

                /*departments in common data should not be present in central departments*/
                if (fpCentralDepartments.Any())
                {
                    commonData = commonData.Where(x => !fpCentralDepartments.Contains(x.fk_department_code)).ToList();
                }

                var commonDataOrgIds = commonData.Select(x => x.org_id_3).Distinct().ToList();

                var orgIdsWhichAreAssignedToUsersWithRole3And4 = (from a in usersWithRoles3And4
                                                                    join b in commonData on a equals b.fk_user_id
                                                                    orderby b.org_id_3
                                                                    select b.org_id_3).Distinct().ToList();

                /*out of the common data check if any of these org ids are not assigned to users with role 3 and 4*/
                var orgIdsWhichAreNotAssignedToUsersWithRole3And4 = commonData.Where(x => !orgIdsWhichAreAssignedToUsersWithRole3And4.Contains(x.org_id_3)).Select(y => y.org_id_3).Distinct().ToList();

                /*step 2. Check for data not in common data, these must have been assigned to the users with roles 3 and 4*/
                var orgIdsWhichAreNotPresentInCommonData = await(from a in dbContext.tco_org_hierarchy
                                                            where a.fk_tenant_id == userdetails.tenant_id
                                                            && a.fk_org_version == orgVersion
                                                            && !commonDataOrgIds.Contains(a.org_id_3)
                                                            select new
                                                            {
                                                                a.org_id_3,
                                                                a.fk_department_code
                                                            }).Distinct().ToListAsync();

                if (fpCentralDepartments.Any())
                {
                    orgIdsWhichAreNotPresentInCommonData = orgIdsWhichAreNotPresentInCommonData.Where(x => !fpCentralDepartments.Contains(x.fk_department_code)).ToList();
                }

                var distinctOrgIdsToInsert = orgIdsWhichAreNotAssignedToUsersWithRole3And4.Concat(orgIdsWhichAreNotPresentInCommonData.Select(x => x.org_id_3).ToList())
                                            .Distinct().ToList();

                foreach (var orgid in distinctOrgIdsToInsert)
                {
                    tco_org_validations ov = new tco_org_validations()
                    {
                        fk_tenant_id = userdetails.tenant_id,
                        validation_id = 34,
                        error_value = orgid
                    };

                   await dbContext.tco_org_validations.AddAsync(ov);
                   await  dbContext.SaveChangesAsync();
                }
            }
            if (lowestOrgLevel == 2)
            {
                /*step 1. Retrieve common data*/
                var commonData = await (from a in dbContext.tco_org_hierarchy
                                    join b in dbContext.tco_user_orgrole on new { a = a.fk_tenant_id, b = a.fk_org_version, c = a.org_id_2 }
                                                                     equals new { a = b.fk_tenant_id, b = b.fk_org_version, c = b.fk_org_id }
                                    where a.fk_tenant_id == userdetails.tenant_id
                                    && b.fk_org_version == orgVersion
                                    && b.hierarchy_level == 2
                                    select new
                                    {
                                        a.org_id_2,
                                        a.org_name_2,
                                        b.fk_user_id,
                                        a.fk_department_code
                                    }).Distinct().ToListAsync();

                /*departments in common data should not be present in central departments*/
                if (fpCentralDepartments.Any())
                {
                    commonData = commonData.Where(x => !fpCentralDepartments.Contains(x.fk_department_code)).ToList();
                }

                var commonDataOrgIds = commonData.Select(x => x.org_id_2).Distinct().ToList();

                var orgIdsWhichAreAssignedToUsersWithRole3And4 = (from a in usersWithRoles3And4
                                                                    join b in commonData on a equals b.fk_user_id
                                                                    orderby b.org_id_2
                                                                    select b.org_id_2).Distinct().ToList();

                /*out of the common data check if any of these org ids are not assigned to users with role 3 and 4*/
                var orgIdsWhichAreNotAssignedToUsersWithRole3And4 = commonData.Where(x => !orgIdsWhichAreAssignedToUsersWithRole3And4.Contains(x.org_id_2)).Select(y => y.org_id_2).Distinct().ToList();

                /*step 2. Check for data not in common data, these must have been assigned to the users with roles 3 and 4*/
                var orgIdsWhichAreNotPresentInCommonData = await(from a in dbContext.tco_org_hierarchy
                                                            where a.fk_tenant_id == userdetails.tenant_id
                                                            && a.fk_org_version == orgVersion
                                                            && !commonDataOrgIds.Contains(a.org_id_2)
                                                            select new
                                                            {
                                                                a.org_id_2,
                                                                a.fk_department_code
                                                            }).Distinct().ToListAsync();

                if (fpCentralDepartments.Any())
                {
                    orgIdsWhichAreNotPresentInCommonData = orgIdsWhichAreNotPresentInCommonData.Where(x => !fpCentralDepartments.Contains(x.fk_department_code)).ToList();
                }

                var distinctOrgIdsToInsert = orgIdsWhichAreNotAssignedToUsersWithRole3And4.Concat(orgIdsWhichAreNotPresentInCommonData.Select(x => x.org_id_2).ToList())
                                            .Distinct().ToList();

                foreach (var orgid in distinctOrgIdsToInsert)
                {
                    tco_org_validations ov = new tco_org_validations()
                    {
                        fk_tenant_id = userdetails.tenant_id,
                        validation_id = 34,
                        error_value = orgid
                    };

                    await dbContext.tco_org_validations.AddAsync(ov);
                    await dbContext.SaveChangesAsync();
                }
            }
        }

        public async Task<JObject> GetFinplanDropdownData(string userId, string orgVersion)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            var userdetails = await _utility.GetUserDetailsAsync(userId);

            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userdetails.language_preference, userdetails.user_name, "AdmOrgStructure");

            var fpLevel1Value = await (from v in dbContext.vw_tco_parameters where v.fk_tenant_id == userdetails.tenant_id && v.active == 1 && v.param_name == "FINPLAN_LEVEL_1" select v).FirstOrDefaultAsync();
            var fpLevel2Value = await (from v in dbContext.vw_tco_parameters where v.fk_tenant_id == userdetails.tenant_id && v.active == 1 && v.param_name == "FINPLAN_LEVEL_2" select v).FirstOrDefaultAsync();
            bool isFPLevel2Configured = fpLevel2Value != null;

            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);
            var orgHierarchyDepartments = orgVersionContent.lstOrgHierarchy.Select(y => y.fk_department_code).ToList();

            var orgData = orgVersionContent.lstOrgHierarchy;

            var departments = await  dbContext.tco_departments.Where(x => x.fk_tenant_id == userdetails.tenant_id
                                                                && x.status == 1
                                                                && orgHierarchyDepartments.Contains(x.pk_department_code)).ToListAsync();

            var accounts = await dbContext.tco_accounts.Where(x => x.pk_tenant_id == userdetails.tenant_id
                                                                && x.isActive).OrderBy(z => z.pk_account_code).ToListAsync();

            var functions = await dbContext.tco_functions.Where(x => x.pk_tenant_id == userdetails.tenant_id
                                                               && x.isActive).OrderBy(z => z.pk_Function_code).ToListAsync();

            var servicesList = await dbContext.tco_service_values.Where(x => x.fk_tenant_id == userdetails.tenant_id).ToListAsync();

            dynamic result = new JObject();
            dynamic orgDataArray = new JArray();

            var fpDataLevel1 = fpLevel1Value.param_value.ToLower() == "org_id_1".ToLower() ? orgData.Select(x => new { orgID = x.org_id_1, orgName = x.org_name_1 }).OrderBy(x => x.orgID).Distinct().ToList() :
                               fpLevel1Value.param_value.ToLower() == "org_id_2".ToLower() ? orgData.Select(x => new { orgID = x.org_id_2, orgName = x.org_name_2 }).OrderBy(x => x.orgID).Distinct().ToList() :
                               fpLevel1Value.param_value.ToLower() == "org_id_3".ToLower() ? orgData.Select(x => new { orgID = x.org_id_3, orgName = x.org_name_3 }).OrderBy(x => x.orgID).Distinct().ToList() :
                               fpLevel1Value.param_value.ToLower() == "org_id_4".ToLower() ? orgData.Select(x => new { orgID = x.org_id_4, orgName = x.org_name_4 }).OrderBy(x => x.orgID).Distinct().ToList() :
                               fpLevel1Value.param_value.ToLower() == "org_id_5".ToLower() ? orgData.Select(x => new { orgID = x.org_id_5, orgName = x.org_name_5 }).OrderBy(x => x.orgID).Distinct().ToList() :
                               fpLevel1Value.param_value.ToLower() == "service_id_1".ToLower() ? servicesList.Select(x => new { orgID = x.service_id_1, orgName = x.service_name_1 }).OrderBy(x => x.orgID).Distinct().ToList() :
                               fpLevel1Value.param_value.ToLower() == "service_id_2".ToLower() ? servicesList.Select(x => new { orgID = x.service_id_2, orgName = x.service_name_2 }).OrderBy(x => x.orgID).Distinct().ToList() :
                               fpLevel1Value.param_value.ToLower() == "service_id_3".ToLower() ? servicesList.Select(x => new { orgID = x.service_id_3, orgName = x.service_name_3 }).OrderBy(x => x.orgID).Distinct().ToList() :
                               fpLevel1Value.param_value.ToLower() == "service_id_4".ToLower() ? servicesList.Select(x => new { orgID = x.service_id_4, orgName = x.service_name_4 }).OrderBy(x => x.orgID).Distinct().ToList() :
                               servicesList.Select(x => new { orgID = x.service_id_5, orgName = x.service_name_5 }).OrderBy(x => x.orgID).Distinct().ToList();

            foreach (var data in fpDataLevel1)
            {
                dynamic obj = new JObject();
                obj.key = data.orgID;
                obj.value = data.orgID + "-" + data.orgName;
                orgDataArray.Add(obj);
            }
            result.Add("fpDataLevel1Array", orgDataArray);

            if (isFPLevel2Configured)
            {
                var fpDataLevel2 = fpLevel2Value.param_value.ToLower() == "org_id_1".ToLower() ? orgData.Select(x => new { orgID = x.org_id_1, orgName = x.org_name_1 }).OrderBy(x => x.orgID).Distinct().ToList() :
                                   fpLevel2Value.param_value.ToLower() == "org_id_2".ToLower() ? orgData.Select(x => new { orgID = x.org_id_2, orgName = x.org_name_2 }).OrderBy(x => x.orgID).Distinct().ToList() :
                                   fpLevel2Value.param_value.ToLower() == "org_id_3".ToLower() ? orgData.Select(x => new { orgID = x.org_id_3, orgName = x.org_name_3 }).OrderBy(x => x.orgID).Distinct().ToList() :
                                   fpLevel2Value.param_value.ToLower() == "org_id_4".ToLower() ? orgData.Select(x => new { orgID = x.org_id_4, orgName = x.org_name_4 }).OrderBy(x => x.orgID).Distinct().ToList() :
                                   fpLevel2Value.param_value.ToLower() == "org_id_5".ToLower() ? orgData.Select(x => new { orgID = x.org_id_5, orgName = x.org_name_5 }).OrderBy(x => x.orgID).Distinct().ToList() :
                                   fpLevel2Value.param_value.ToLower() == "service_id_1".ToLower() ? servicesList.Select(x => new { orgID = x.service_id_1, orgName = x.service_name_1 }).OrderBy(x => x.orgID).Distinct().ToList() :
                                   fpLevel2Value.param_value.ToLower() == "service_id_2".ToLower() ? servicesList.Select(x => new { orgID = x.service_id_2, orgName = x.service_name_2 }).OrderBy(x => x.orgID).Distinct().ToList() :
                                   fpLevel2Value.param_value.ToLower() == "service_id_3".ToLower() ? servicesList.Select(x => new { orgID = x.service_id_3, orgName = x.service_name_3 }).OrderBy(x => x.orgID).Distinct().ToList() :
                                   fpLevel2Value.param_value.ToLower() == "service_id_4".ToLower() ? servicesList.Select(x => new { orgID = x.service_id_4, orgName = x.service_name_4 }).OrderBy(x => x.orgID).Distinct().ToList() :
                                   servicesList.Select(x => new { orgID = x.service_id_5, orgName = x.service_name_5 }).OrderBy(x => x.orgID).Distinct().ToList();
                orgDataArray = new JArray();

                dynamic obj = new JObject();
                obj.key = "";
                obj.value = ((langStringValues.FirstOrDefault(v => v.Key == "adm_org_empty_value")).Value).LangText;
                orgDataArray.Add(obj);

                foreach (var data in fpDataLevel2)
                {
                    obj = new JObject();
                    obj.key = data.orgID;
                    obj.value = data.orgID + "-" + data.orgName;
                    orgDataArray.Add(obj);
                }
                result.Add("fpDataLevel2Array", orgDataArray);
            }
            else
            {
                orgDataArray = new JArray();

                dynamic obj = new JObject();
                obj.key = "";
                obj.value = ((langStringValues.FirstOrDefault(v => v.Key == "adm_org_empty_value")).Value).LangText;
                orgDataArray.Add(obj);

                result.Add("fpDataLevel2Array", orgDataArray);
            }

            orgDataArray = new JArray();
            foreach (var data in accounts)
            {
                dynamic obj = new JObject();
                obj.key = data.pk_account_code;
                obj.value = data.pk_account_code + "-" + data.display_name;
                orgDataArray.Add(obj);
            }
            result.Add("accountsArray", orgDataArray);

            orgDataArray = new JArray();
            foreach (var data in departments)
            {
                dynamic obj = new JObject();
                obj.key = data.pk_department_code;
                obj.value = data.pk_department_code + "-" + data.department_name;
                orgDataArray.Add(obj);
            }
            result.Add("departmentsArray", orgDataArray);

            orgDataArray = new JArray();
            foreach (var data in functions)
            {
                dynamic obj = new JObject();
                obj.key = data.pk_Function_code;
                obj.value = data.pk_Function_code + "-" + data.display_name;
                orgDataArray.Add(obj);
            }
            result.Add("functionsArray", orgDataArray);
            return result;
        }

        public async Task<dynamic> GetAdminFinplanColumns(string userId)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            var userdetails = await _utility.GetUserDetailsAsync(userId);
            var fpLevel2Value = await (from v in dbContext.vw_tco_parameters where v.fk_tenant_id == userdetails.tenant_id && v.active == 1 && v.param_name == "FINPLAN_LEVEL_2" select v).FirstOrDefaultAsync();
            bool isFPLevel2Configured = fpLevel2Value != null;

            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userdetails.language_preference, userdetails.user_name, "AdmOrgStructure");
            dynamic fpLevelData = new JObject();

            JArray gridHeader = new JArray();
            dynamic headerTitle = new JObject();

            headerTitle.title = ((langStringValues.FirstOrDefault(v => v.Key == "adm_fp_header")).Value).LangText;
            headerTitle.descriptiontip = ((langStringValues.FirstOrDefault(v => v.Key == "adm_fp_description_tip")).Value).LangText;
            gridHeader.Add(headerTitle);
            fpLevelData.Add("header", gridHeader);

            dynamic fpColumns = GetAdminFinplanColumns(langStringValues, isFPLevel2Configured);
            fpLevelData.Add("columns", fpColumns);
            return fpLevelData;
        }

        public async Task<dynamic> GetAdminFinplanLevelData(string userId, int skip, int take, AdminFinPlanSearch searchParams, string orgVersion)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            var userdetails = await _utility.GetUserDetailsAsync(userId);
            var fpLevel1Value = await (from v in dbContext.vw_tco_parameters where v.fk_tenant_id == userdetails.tenant_id && v.active == 1 && v.param_name == "FINPLAN_LEVEL_1" select v).FirstOrDefaultAsync();
            var fpLevel2Value = await (from v in dbContext.vw_tco_parameters where v.fk_tenant_id == userdetails.tenant_id && v.active == 1 && v.param_name == "FINPLAN_LEVEL_2" select v).FirstOrDefaultAsync();
            bool isFPLevel2Configured = fpLevel2Value != null;

            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userdetails.language_preference, userdetails.user_name, "AdmOrgStructure");
            dynamic fpLevelData = new JObject();

            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, orgVersion);
            var orgData = orgVersionContent.lstOrgHierarchy;
            var departments = await dbContext.tco_departments.Where(x => x.fk_tenant_id == userdetails.tenant_id
                                                                && x.status == 1).ToListAsync();

            var accounts = await dbContext.tco_accounts.Where(x => x.pk_tenant_id == userdetails.tenant_id
                                                                && x.isActive).OrderBy(z => z.pk_account_code).ToListAsync();

            var functions = await  dbContext.tco_functions.Where(x => x.pk_tenant_id == userdetails.tenant_id
                                                               && x.isActive).OrderBy(z => z.pk_Function_code).ToListAsync();

            var servicesList = await dbContext.tco_service_values.Where(x => x.fk_tenant_id == userdetails.tenant_id).ToListAsync();

            var fpDataLevel1 = fpLevel1Value.param_value.ToLower() == "org_id_1".ToLower() ? orgData.Select(x => new { orgID = x.org_id_1, orgName = x.org_name_1 }).OrderBy(x => x.orgID).Distinct().ToList() :
                              fpLevel1Value.param_value.ToLower() == "org_id_2".ToLower() ? orgData.Select(x => new { orgID = x.org_id_2, orgName = x.org_name_2 }).OrderBy(x => x.orgID).Distinct().ToList() :
                              fpLevel1Value.param_value.ToLower() == "org_id_3".ToLower() ? orgData.Select(x => new { orgID = x.org_id_3, orgName = x.org_name_3 }).OrderBy(x => x.orgID).Distinct().ToList() :
                              fpLevel1Value.param_value.ToLower() == "org_id_4".ToLower() ? orgData.Select(x => new { orgID = x.org_id_4, orgName = x.org_name_4 }).OrderBy(x => x.orgID).Distinct().ToList() :
                              fpLevel1Value.param_value.ToLower() == "org_id_5".ToLower() ? orgData.Select(x => new { orgID = x.org_id_5, orgName = x.org_name_5 }).OrderBy(x => x.orgID).Distinct().ToList() :
                              fpLevel1Value.param_value.ToLower() == "service_id_1".ToLower() ? servicesList.Select(x => new { orgID = x.service_id_1, orgName = x.service_name_1 }).OrderBy(x => x.orgID).Distinct().ToList() :
                              fpLevel1Value.param_value.ToLower() == "service_id_2".ToLower() ? servicesList.Select(x => new { orgID = x.service_id_2, orgName = x.service_name_2 }).OrderBy(x => x.orgID).Distinct().ToList() :
                              fpLevel1Value.param_value.ToLower() == "service_id_3".ToLower() ? servicesList.Select(x => new { orgID = x.service_id_3, orgName = x.service_name_3 }).OrderBy(x => x.orgID).Distinct().ToList() :
                              fpLevel1Value.param_value.ToLower() == "service_id_4".ToLower() ? servicesList.Select(x => new { orgID = x.service_id_4, orgName = x.service_name_4 }).OrderBy(x => x.orgID).Distinct().ToList() :
                              servicesList.Select(x => new { orgID = x.service_id_5, orgName = x.service_name_5 }).OrderBy(x => x.orgID).Distinct().ToList();

            var fpDataLevel2 = isFPLevel2Configured ? (fpLevel2Value.param_value.ToLower() == "org_id_1".ToLower() ? orgData.Select(x => new { orgID = x.org_id_1, orgName = x.org_name_1 }).OrderBy(x => x.orgID).Distinct().ToList() :
                                  fpLevel2Value.param_value.ToLower() == "org_id_2".ToLower() ? orgData.Select(x => new { orgID = x.org_id_2, orgName = x.org_name_2 }).OrderBy(x => x.orgID).Distinct().ToList() :
                                  fpLevel2Value.param_value.ToLower() == "org_id_3".ToLower() ? orgData.Select(x => new { orgID = x.org_id_3, orgName = x.org_name_3 }).OrderBy(x => x.orgID).Distinct().ToList() :
                                  fpLevel2Value.param_value.ToLower() == "org_id_4".ToLower() ? orgData.Select(x => new { orgID = x.org_id_4, orgName = x.org_name_4 }).OrderBy(x => x.orgID).Distinct().ToList() :
                                  fpLevel2Value.param_value.ToLower() == "org_id_5".ToLower() ? orgData.Select(x => new { orgID = x.org_id_5, orgName = x.org_name_5 }).OrderBy(x => x.orgID).Distinct().ToList() :
                                  fpLevel2Value.param_value.ToLower() == "service_id_1".ToLower() ? servicesList.Select(x => new { orgID = x.service_id_1, orgName = x.service_name_1 }).OrderBy(x => x.orgID).Distinct().ToList() :
                                  fpLevel2Value.param_value.ToLower() == "service_id_2".ToLower() ? servicesList.Select(x => new { orgID = x.service_id_2, orgName = x.service_name_2 }).OrderBy(x => x.orgID).Distinct().ToList() :
                                  fpLevel2Value.param_value.ToLower() == "service_id_3".ToLower() ? servicesList.Select(x => new { orgID = x.service_id_3, orgName = x.service_name_3 }).OrderBy(x => x.orgID).Distinct().ToList() :
                                  fpLevel2Value.param_value.ToLower() == "service_id_4".ToLower() ? servicesList.Select(x => new { orgID = x.service_id_4, orgName = x.service_name_4 }).OrderBy(x => x.orgID).Distinct().ToList() :
                                  servicesList.Select(x => new { orgID = x.service_id_5, orgName = x.service_name_5 }).OrderBy(x => x.orgID).Distinct().ToList()) : null;

            var data = await (from v in dbContext.tmd_fp_level_defaults where v.fk_tenant_id == userdetails.tenant_id && v.fk_org_version == orgVersion select v).ToListAsync();

            if (searchParams != null && !string.IsNullOrEmpty(searchParams.fpLevel1Value))
            {
                data = data.Where(x => x.fp_level_1_value.Contains(searchParams.fpLevel1Value.Trim())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.fpLevel2Value))
            {
                data = data.Where(x => x.fp_level_2_value.Contains(searchParams.fpLevel2Value.Trim())).ToList();
            }
            if (searchParams != null && string.IsNullOrEmpty(searchParams.fpLevel2Value))
            {
                data = data.Where(x => x.fp_level_2_value.Contains(searchParams.fpLevel2Value.Trim())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.accountCode))
            {
                data = data.Where(x => x.fk_account_code.Contains(searchParams.accountCode.Trim())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.departmentCode))
            {
                data = data.Where(x => x.fk_department_code.Contains(searchParams.departmentCode.Trim())).ToList();
            }
            if (searchParams != null && !string.IsNullOrEmpty(searchParams.functionCode))
            {
                data = data.Where(x => x.fk_function_code.Contains(searchParams.functionCode.Trim())).ToList();
            }
            var dataCount = data.Any() ? data.Count : 0;
            data = data.Skip(skip).Take(take).ToList();
            List<AdminFinPlanLevelsHelper> objList = new List<AdminFinPlanLevelsHelper>();
            foreach (var item in data)
            {
                var objAdminFP = new AdminFinPlanLevelsHelper();
                objAdminFP.Id = item.pk_id;

                dynamic objFpLevel1Value = new JObject();
                if (fpDataLevel1.FirstOrDefault(x => x.orgID == item.fp_level_1_value) != null)
                {
                    objFpLevel1Value.key = item.fp_level_1_value;
                    objFpLevel1Value.value = item.fp_level_1_value + "-" + fpDataLevel1.FirstOrDefault(x => x.orgID == item.fp_level_1_value).orgName;
                }

                objAdminFP.FpLevel1Value = objFpLevel1Value;

                if (isFPLevel2Configured)
                {
                    dynamic objFpLevel2Value = new JObject();
                    if (fpDataLevel2.FirstOrDefault(x => x.orgID == item.fp_level_2_value) != null)
                    {
                        objFpLevel2Value.key = item.fp_level_2_value;
                        objFpLevel2Value.value = item.fp_level_2_value + "-" + fpDataLevel2.FirstOrDefault(x => x.orgID == item.fp_level_2_value).orgName;
                    }
                    else
                    {
                        objFpLevel2Value.key = string.Empty;
                        objFpLevel2Value.value = ((langStringValues.FirstOrDefault(v => v.Key == "adm_org_empty_value")).Value).LangText;
                    }
                    objAdminFP.FpLevel2Value = objFpLevel2Value;
                }

                dynamic objAccountCode = new JObject();
                if (accounts.FirstOrDefault(x => x.pk_account_code == item.fk_account_code) != null)
                {
                    objAccountCode.key = item.fk_account_code;
                    objAccountCode.value = item.fk_account_code + "-" + accounts.FirstOrDefault(x => x.pk_account_code == item.fk_account_code).display_name;
                }
                objAdminFP.AccountCode = objAccountCode;

                dynamic objDeptCode = new JObject();
                if (departments.FirstOrDefault(x => x.pk_department_code == item.fk_department_code) != null)
                {
                    objDeptCode.key = item.fk_department_code;
                    objDeptCode.value = item.fk_department_code + "-" + departments.FirstOrDefault(x => x.pk_department_code == item.fk_department_code).department_name;
                }
                objAdminFP.DepartmentCode = objDeptCode;

                dynamic objFuncCode = new JObject();
                if (functions.FirstOrDefault(x => x.pk_Function_code == item.fk_function_code) != null)
                {
                    objFuncCode.key = item.fk_function_code;
                    objFuncCode.value = item.fk_function_code + "-" + functions.FirstOrDefault(x => x.pk_Function_code == item.fk_function_code).display_name;
                }
                objAdminFP.FunctionCode = objFuncCode;

                objList.Add(objAdminFP);
            }

            fpLevelData.Add("data", (JArray)JToken.FromObject(objList));
            fpLevelData.Add("totalRows", dataCount);
            return fpLevelData;
        }

        private static dynamic GetAdminFinplanColumns(Dictionary<string, clsLanguageString> langStringValues, bool isFPLevel2Configured)
        {
            dynamic col1 = new JObject() {
                        new JProperty("title", " "),
                        new JProperty("field", "Id"),
                        new JProperty("colCount", 0),
                        new JProperty("encoded", false),
                        new JProperty("hidden", true),
                        new JProperty("format", null),
                        new JProperty("expandable", false),
                        new JProperty("locked", false),
                        new JProperty("width", 100),
                        new JProperty("attributes",new JObject { new JProperty("style", "text-align:right;white-space:normal;border-left:none;") }),
                        new JProperty("headerAttributes", new JObject { new JProperty("style", "text-align:left;border-left:none;") }),
                        new JProperty("footerAttributes", new JObject { new JProperty("style", null) }),
                        new JProperty("template", null),
                        new JProperty("footerTemplate", null),
                        new JProperty("filterable",
                                    new JObject ( new JProperty("cell",
                                                    new JObject {
                                                                    new JProperty("enabled", true),
                                                                    new JProperty("showOperators", false),
                                                                    new JProperty("dataSource", new JArray()),
                                                                    new JProperty("operator",  "contains")
                                                                })
                                                )
                                    ),
                        new JProperty("headerTemplate", null),
                        new JProperty("command", null)
            };

            dynamic col2 = new JObject() {
                        new JProperty("title", ((langStringValues.FirstOrDefault(v => v.Key == "adm_fp_level1")).Value).LangText),
                        new JProperty("field", "fpLevel1Value"),
                        new JProperty("colCount", 0),
                        new JProperty("encoded", false),
                        new JProperty("hidden", false),
                        new JProperty("format", null),
                        new JProperty("expandable", false),
                        new JProperty("locked", false),
                        new JProperty("width", 50),
                        new JProperty("attributes",new JObject { new JProperty("style", "text-align:right;white-space:normal;border-left:none;") }),
                        new JProperty("headerAttributes", new JObject { new JProperty("style", "text-align:left;border-left:none;") }),
                        new JProperty("footerAttributes", new JObject { new JProperty("style", null) }),
                        new JProperty("template", "<span id='fpOrgLevel1Value#=Id#'>#=fpLevel1Value.key#</span>"),
                        new JProperty("footerTemplate", null),
                        new JProperty("filterable",
                                    new JObject ( new JProperty("cell",
                                                    new JObject {
                                                                    new JProperty("enabled", true),
                                                                    new JProperty("showOperators", false),
                                                                    new JProperty("dataSource", new JArray()),
                                                                    new JProperty("operator",  "contains")
                                                                })
                                                )
                                    ),
                        new JProperty("headerTemplate", null),
                        new JProperty("command", null)
            };

            dynamic col3 = new JObject() {
                        new JProperty("title", ((langStringValues.FirstOrDefault(v => v.Key == "adm_fp_level2")).Value).LangText),
                        new JProperty("field", "fpLevel2Value"),
                        new JProperty("colCount", 0),
                        new JProperty("encoded", false),
                        new JProperty("hidden", false),
                        new JProperty("format", null),
                        new JProperty("expandable", false),
                        new JProperty("locked", false),
                        new JProperty("width", 50),
                        new JProperty("attributes",new JObject { new JProperty("style", "text-align:right;white-space:normal;border-left:none;") }),
                        new JProperty("headerAttributes", new JObject { new JProperty("style", "text-align:left;border-left:none;") }),
                        new JProperty("footerAttributes", new JObject { new JProperty("style", null) }),
                        new JProperty("template", "<span id='fpOrgLevel2Value#=Id#'>#=fpLevel2Value.key#</span>"),
                        new JProperty("footerTemplate", null),
                        new JProperty("filterable",
                                    new JObject ( new JProperty("cell",
                                                    new JObject {
                                                                    new JProperty("enabled", true),
                                                                    new JProperty("showOperators", false),
                                                                    new JProperty("dataSource", new JArray()),
                                                                    new JProperty("operator",  "contains")
                                                                })
                                                )
                                    ),
                        new JProperty("headerTemplate", null),
                        new JProperty("command", null)
            };

            dynamic col4 = new JObject() {
                        new JProperty("title", ((langStringValues.FirstOrDefault(v => v.Key == "adm_fp_account")).Value).LangText),
                        new JProperty("field", "accountCode"),
                        new JProperty("colCount", 0),
                        new JProperty("encoded", false),
                        new JProperty("hidden", false),
                        new JProperty("format", null),
                        new JProperty("expandable", false),
                        new JProperty("locked", false),
                        new JProperty("width", 50),
                        new JProperty("attributes",new JObject { new JProperty("style", "text-align:right;white-space:normal;border-left:none;") }),
                        new JProperty("headerAttributes", new JObject { new JProperty("style", "text-align:left;border-left:none;") }),
                        new JProperty("footerAttributes", new JObject { new JProperty("style", null) }),
                        new JProperty("template", "<span id='fpOrgLevel3Value#=Id#'>#=accountCode.key#</span>"),
                        new JProperty("footerTemplate", null),
                        new JProperty("filterable",
                                    new JObject ( new JProperty("cell",
                                                    new JObject {
                                                                    new JProperty("enabled", true),
                                                                    new JProperty("showOperators", false),
                                                                    new JProperty("dataSource", new JArray()),
                                                                    new JProperty("operator",  "contains")
                                                                })
                                                )
                                    ),
                        new JProperty("headerTemplate", null),
                        new JProperty("command", null)
            };

            dynamic col5 = new JObject() {
                        new JProperty("title",  ((langStringValues.FirstOrDefault(v => v.Key == "adm_fp_department")).Value).LangText),
                        new JProperty("field", "departmentCode"),
                        new JProperty("colCount", 0),
                        new JProperty("encoded", false),
                        new JProperty("hidden", false),
                        new JProperty("format", null),
                        new JProperty("expandable", false),
                        new JProperty("locked", false),
                        new JProperty("width", 50),
                        new JProperty("attributes",new JObject { new JProperty("style", "text-align:right;white-space:normal;border-left:none;") }),
                        new JProperty("headerAttributes", new JObject { new JProperty("style", "text-align:left;border-left:none;") }),
                        new JProperty("footerAttributes", new JObject { new JProperty("style", null) }),
                        new JProperty("template", "<span id='fpOrgLevel4Value#=Id#'>#=departmentCode.key#</span>"),
                        new JProperty("footerTemplate", null),
                        new JProperty("filterable",
                                    new JObject ( new JProperty("cell",
                                                    new JObject {
                                                                    new JProperty("enabled", true),
                                                                    new JProperty("showOperators", false),
                                                                    new JProperty("dataSource", new JArray()),
                                                                    new JProperty("operator",  "contains")
                                                                })
                                                )
                                    ),
                        new JProperty("headerTemplate", null),
                        new JProperty("command", null)
            };

            dynamic col6 = new JObject() {
                        new JProperty("title", ((langStringValues.FirstOrDefault(v => v.Key == "adm_fp_function")).Value).LangText),
                        new JProperty("field", "functionCode"),
                        new JProperty("colCount", 0),
                        new JProperty("encoded", false),
                        new JProperty("hidden", false),
                        new JProperty("format", null),
                        new JProperty("expandable", false),
                        new JProperty("locked", false),
                        new JProperty("width", 50),
                        new JProperty("attributes",new JObject { new JProperty("style", "text-align:right;white-space:normal;border-left:none;") }),
                        new JProperty("headerAttributes", new JObject { new JProperty("style", "text-align:left;border-left:none;") }),
                        new JProperty("footerAttributes", new JObject { new JProperty("style", null) }),
                        new JProperty("template", "<span id='fpOrgLevel5Value#=Id#'>#=functionCode.key#</span>"),
                        new JProperty("footerTemplate", null),
                        new JProperty("filterable",
                                    new JObject ( new JProperty("cell",
                                                    new JObject {
                                                                    new JProperty("enabled", true),
                                                                    new JProperty("showOperators", false),
                                                                    new JProperty("dataSource", new JArray()),
                                                                    new JProperty("operator",  "contains")
                                                                })
                                                )
                                    ),
                        new JProperty("headerTemplate", null),
                        new JProperty("command", null)
            };

            dynamic col7 = new JObject() {
                        new JProperty("title", " "),
                        new JProperty("field", "search"),
                        new JProperty("colCount", 0),
                        new JProperty("encoded", false),
                        new JProperty("hidden", false),
                        new JProperty("format", null),
                        new JProperty("expandable", false),
                        new JProperty("locked", false),
                        new JProperty("width", 50),
                        new JProperty("attributes",new JObject { new JProperty("style", "border-left:none;text-align:right;white-space:normal;") }),
                        new JProperty("headerAttributes", new JObject { new JProperty("style", "text-align:right;border-left:none;") }),
                        new JProperty("footerAttributes", new JObject { new JProperty("style", null) }),
                        new JProperty("template", null),
                        new JProperty("footerTemplate", null),
                        new JProperty("filterable",
                                    new JObject ( new JProperty("cell",
                                                    new JObject {
                                                                    new JProperty("enabled", true),
                                                                    new JProperty("showOperators", false),
                                                                    new JProperty("dataSource", new JArray()),
                                                                    new JProperty("operator",  "contains")
                                                                })
                                                )
                                    ),
                        new JProperty("headerTemplate", null),
                        new JProperty("command", null)
            };

            dynamic col8 = new JObject() {
                        new JProperty("title", " "),
                        new JProperty("field", "delete"),
                        new JProperty("colCount", 0),
                        new JProperty("encoded", false),
                        new JProperty("hidden", false),
                        new JProperty("format", null),
                        new JProperty("expandable", false),
                        new JProperty("locked", false),
                        new JProperty("width", 30),
                        new JProperty("attributes",new JObject { new JProperty("style", "text-align:right;white-space:normal;") }),
                        new JProperty("headerAttributes", new JObject { new JProperty("style", "text-align:center;border-left:none;") }),
                        new JProperty("footerAttributes", new JObject { new JProperty("style", null) }),
                        new JProperty("template", null),
                        new JProperty("footerTemplate", null),
                        new JProperty("filterable",
                                    new JObject ( new JProperty("cell",
                                                    new JObject {
                                                                    new JProperty("enabled", true),
                                                                    new JProperty("showOperators", false),
                                                                    new JProperty("dataSource", new JArray()),
                                                                    new JProperty("operator",  "contains")
                                                                })
                                                )
                                    ),
                        new JProperty("headerTemplate", null),
                        new JProperty("command", null)
            };

            dynamic objColumns = isFPLevel2Configured ? new JArray() { col1, col2, col3, col4, col5, col6, col7, col8 } :
                                                         new JArray() { col1, col2, col4, col5, col6, col7, col8 };
            return objColumns;
        }

        public async Task<JObject> SaveAdminFinplanData(string userId, List<AdminFinPlanSearch> adminFinPlanSearches, string orgVersion)
        {
            try
            {
                dynamic result = new JObject();
                var dbContext = await _utility.GetTenantDBContextAsync();
                var userdetails = await _utility.GetUserDetailsAsync(userId);
                var listSet = new List<tmd_fp_level_defaults>();
                Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userdetails.language_preference, userdetails.user_name, "StaffPlanning");
                foreach (var item in adminFinPlanSearches)
                {
                    if (item.Id != 0)
                    {
                        var data = await dbContext.tmd_fp_level_defaults.FirstOrDefaultAsync(x => x.pk_id == item.Id);
                        if (data != null)
                        {
                            data.fp_level_1_value = item.fpLevel1Value;
                            data.fp_level_2_value = item.fpLevel2Value;
                            data.fk_account_code = item.accountCode;
                            data.fk_department_code = item.departmentCode;
                            data.fk_function_code = item.functionCode;
                        }
                      await  dbContext.SaveChangesAsync();
                    }
                    else
                    {
                        var data = await (from v in dbContext.tmd_fp_level_defaults
                                    where v.fk_tenant_id == userdetails.tenant_id && v.fk_org_version == orgVersion && v.fp_level_1_value == item.fpLevel1Value
                                    && v.fp_level_2_value == item.fpLevel2Value && v.fk_account_code == item.accountCode && v.fk_department_code == item.departmentCode
                                    && v.fk_function_code == item.functionCode
                                    select v).FirstOrDefaultAsync();
                        if (data == null)
                        {
                            var newData = new tmd_fp_level_defaults();
                            newData.fk_tenant_id = userdetails.tenant_id;
                            newData.fp_level_1_value = item.fpLevel1Value;
                            newData.fp_level_2_value = item.fpLevel2Value;
                            newData.fk_account_code = item.accountCode;
                            newData.fk_department_code = item.departmentCode;
                            newData.fk_function_code = item.functionCode;
                            newData.fk_project_code = string.Empty;
                            newData.free_dim_1 = string.Empty;
                            newData.free_dim_2 = string.Empty;
                            newData.free_dim_3 = string.Empty;
                            newData.free_dim_4 = string.Empty;
                            newData.module = "FINPLAN".ToUpper();
                            newData.status = 1;
                            newData.updated = DateTime.UtcNow;
                            newData.updated_by = userdetails.pk_id;
                            newData.fk_org_version = orgVersion;
                            listSet.Add(newData);
                        }
                    }
                }

                if (listSet.Any())
                {
                    await dbContext.tmd_fp_level_defaults.AddRangeAsync(listSet);
                    await dbContext.SaveChangesAsync();
                }
                if (adminFinPlanSearches.Any())
                {
                  await  StartJobToValidateAdminOrgStructure(userId, adminFinPlanSearches.First().orgVersion);
                }
                result.messageType = "success";
                result.message = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_add_edit_success")).Value).LangText;
                return result;
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<JObject> DeleteAdminFinplanData(string userId, int Id, string orgVersion)
        {
            try
            {
                var dbContext = await _utility.GetTenantDBContextAsync();
                var userdetails = await _utility.GetUserDetailsAsync(userId);
                Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userdetails.language_preference, userdetails.user_name, "StaffPlanning");
                var data = await (from v in dbContext.tmd_fp_level_defaults
                            where v.fk_tenant_id == userdetails.tenant_id && v.fk_org_version == orgVersion && v.pk_id == Id && v.module == "FINPLAN".ToUpper()
                            select v).FirstOrDefaultAsync();
                if (data == null)
                {
                    throw new InvalidOperationException("Record does not exist.");
                }

                dynamic result = new JObject();
                dbContext.tmd_fp_level_defaults.Remove(data);
               await  dbContext.SaveChangesAsync();

               await StartJobToValidateAdminOrgStructure(userId, orgVersion);

                result.messageType = "success";
                result.message = ((langStringValues.FirstOrDefault(v => v.Key == "org_structure_org_code_delete_success")).Value).LangText;
                return result;
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<JObject> OrgDataUserInfo(string userId, int orgLevel, string orgId, string orgVersion)
        {
            dynamic result = new JObject();
            List<GridColumnHelper> formattedColumns = new List<GridColumnHelper>();
            var userdetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userdetails.language_preference, userdetails.user_name, "AdmOrgStructure");

            GridColumnHelper columnInfo = new GridColumnHelper
            {
                field = "username",
                title = langStringValues.FirstOrDefault(x => x.Key.ToLower() == "adm_orgCode_userName".ToLower()).Value.LangText,
                colCount = 0,
                encoded = false,
                hidden = false,
                width = 100,
                format = null,
                expandable = false,
                attributes = new ColumnStyleHelper() { style = "text-align:left; white-space: normal;vertical-align:top;" },
                headerAttributes = new ColumnStyleHelper() { style = "text-align:left;" },
                template = null,
                filterable = null,
                sortable = true,
                headerTemplate = null,
            };
            formattedColumns.Add(columnInfo);
            columnInfo = new GridColumnHelper
            {
                field = "mailId",
                title = langStringValues.FirstOrDefault(x => x.Key.ToLower() == "adm_orgCode_mailId".ToLower()).Value.LangText,
                colCount = 0,
                encoded = false,
                hidden = false,
                width = 100,
                format = null,
                expandable = false,
                attributes = new ColumnStyleHelper() { style = "text-align:left; white-space: normal;vertical-align:top;vertical-align:top;" },
                headerAttributes = new ColumnStyleHelper() { style = "text-align:left;" },
                template = null,
                filterable = null,
                sortable = true,
                headerTemplate = null,
            };
            formattedColumns.Add(columnInfo);
            result.Add("columns", JArray.FromObject(formattedColumns));
            result.Add("userList", JArray.FromObject(await GetUserDataByOrgId(userId, orgId, orgLevel, orgVersion)));
            return result;
        }

        public async Task<List<userList>> GetUserDataByOrgId(string userId, string orgId, int orgLevel, string orgVersion)
        {
            var userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAdminOrgStructureAsync(userId, userDetails.tenant_id, orgVersion);
            var lstUserOrgRole = await dbContext.tco_user_orgrole.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.fk_org_version == orgVersionContent.orgVersion && x.fk_org_id == orgId).ToListAsync();
            IEnumerable<int> userRole = await _utility.GetUserRoleIdsAsync(userId);
            List<tco_org_hierarchy> lstOrgHierarchy = FilterBasedOnOrgInfo(orgId, orgLevel, orgVersionContent);
            var hierarchyList = (from p in lstOrgHierarchy
                                 select new HierarchyList
                                 {
                                     orgID_1 = p.org_id_1,
                                     orgLevel_1 = 1,
                                     orgID_2 = p.org_id_2,
                                     orgLevel_2 = 2,
                                     orgID_3 = p.org_id_3,
                                     orgLevel_3 = 3,
                                     orgID_4 = p.org_id_4,
                                     orgLevel_4 = 4,
                                     orgID_5 = p.org_id_5,
                                     orgLevel_5 = 5,
                                     orgID_6 = p.org_id_6,
                                     orgLevel_6 = 6,
                                     orgID_7 = p.org_id_7,
                                     orgLevel_7 = 7,
                                     orgID_8 = p.org_id_8,
                                     orgLevel_8 = 8
                                 }).Distinct().ToList();
            List<int> userIds = new List<int>();
            GetFilteredUserIds(orgLevel, ref userIds, lstUserOrgRole, hierarchyList);
            List<int> usersIdLists = await dbContext.tco_users_settings
                .Where(x => x.tenant_id == userDetails.tenant_id)
                .Select(x => x.fk_user_id).Distinct().ToListAsync();

            List<userList> userData = GetTcoUserData(userIds, usersIdLists, userDetails.client_id);

            //Get user by role (1, 2)
            //61459
            if (userRole.Any(x => x == 1))
            {
                return userData;
            }
            else
            {
                return await FilterBasedOnUserRole(userData, userDetails.tenant_id);
            }
        }

        public async Task StartJobToValidateAdminOrgStructure(string userId, string orgVersion)
        {
            var userdetails = await _utility.GetUserDetailsAsync(userId);

            dynamic adminaccountsvalidationsRequest = new JObject();
            adminaccountsvalidationsRequest.Add("UserId", userId);
            adminaccountsvalidationsRequest.Add("TenantId", userdetails.tenant_id);
            adminaccountsvalidationsRequest.Add("ValidationType", "AdminOrgStructure");
            adminaccountsvalidationsRequest.Add("OrgVersion", orgVersion);

            string stradminaccountsvalidationsRequestRequest = JsonConvert.SerializeObject(adminaccountsvalidationsRequest);
            _backendJob.QueueMessage(userdetails, null, QueueName.adminaccountsvalidationsqueue, stradminaccountsvalidationsRequestRequest);
        }

        private static void GetFilteredUserIds(int orgLevel, ref List<int> userIds, List<tco_user_orgrole> lstUserOrgRole, List<HierarchyList> hierarchyList)
        {
            switch (orgLevel)
            {
                case 1:
                    userIds.AddRange(lstUserOrgRole.Where(x => hierarchyList.Select(z => z.orgID_1).Contains(x.fk_org_id) && x.hierarchy_level == 1).Select(x => x.fk_user_id).ToList());
                    break;

                case 2:
                    userIds.AddRange(lstUserOrgRole.Where(x => hierarchyList.Select(z => z.orgID_2).Contains(x.fk_org_id) && x.hierarchy_level == 2).Select(x => x.fk_user_id).ToList());
                    break;

                case 3:
                    userIds.AddRange(lstUserOrgRole.Where(x => hierarchyList.Select(z => z.orgID_3).Contains(x.fk_org_id) && x.hierarchy_level == 3).Select(x => x.fk_user_id).ToList());
                    break;

                case 4:
                    userIds.AddRange(lstUserOrgRole.Where(x => hierarchyList.Select(z => z.orgID_4).Contains(x.fk_org_id) && x.hierarchy_level == 4).Select(x => x.fk_user_id).ToList());
                    break;

                case 5:
                    userIds.AddRange(lstUserOrgRole.Where(x => hierarchyList.Select(z => z.orgID_5).Contains(x.fk_org_id) && x.hierarchy_level == 5).Select(x => x.fk_user_id).ToList());
                    break;

                case 6:
                    userIds.AddRange(lstUserOrgRole.Where(x => hierarchyList.Select(z => z.orgID_6).Contains(x.fk_org_id) && x.hierarchy_level == 6).Select(x => x.fk_user_id).ToList());
                    break;

                case 7:
                    userIds.AddRange(lstUserOrgRole.Where(x => hierarchyList.Select(z => z.orgID_7).Contains(x.fk_org_id) && x.hierarchy_level == 7).Select(x => x.fk_user_id).ToList());
                    break;

                case 8:
                    userIds.AddRange(lstUserOrgRole.Where(x => hierarchyList.Select(z => z.orgID_8).Contains(x.fk_org_id) && x.hierarchy_level == 8).Select(x => x.fk_user_id).ToList());
                    break;
            }
        }

        private List<userList> GetTcoUserData(List<int> userIds, List<int> usersIdLists, int clientId)
        {
            AuthenticationDBContext authenticationDBContext = _utility.GetAuthenticationContext();
            return (from a in authenticationDBContext.tco_users
                    join b in authenticationDBContext.user_client_mapping on a.user_name equals b.user_name
                where b.client_id == clientId && (a.IsActive.HasValue && a.IsActive.Value) && usersIdLists.Contains(a.pk_id) && userIds.Contains(a.pk_id) 
                select new userList
                {
                    pk_id = a.pk_id,
                    username = a.first_name + " " + a.last_name,
                    mailId = a.user_name
                }).Distinct().OrderBy(z => z.username).AsNoTracking().ToList();
        }

        private static List<tco_org_hierarchy> FilterBasedOnOrgInfo(string orgId, int orgLevel, ClsOrgVersionSpecificContent orgVersionContent)
        {
            List<tco_org_hierarchy> lstOrgHierarchy = new List<tco_org_hierarchy>();
            switch (orgLevel)
            {
                case 1:
                    lstOrgHierarchy = orgVersionContent.lstOrgHierarchy.Where(x => x.org_id_1 == orgId).ToList();
                    break;

                case 2:
                    lstOrgHierarchy = orgVersionContent.lstOrgHierarchy.Where(x => x.org_id_2 == orgId).ToList();
                    break;

                case 3:
                    lstOrgHierarchy = orgVersionContent.lstOrgHierarchy.Where(x => x.org_id_3 == orgId).ToList();
                    break;

                case 4:
                    lstOrgHierarchy = orgVersionContent.lstOrgHierarchy.Where(x => x.org_id_4 == orgId).ToList();
                    break;

                case 5:
                    lstOrgHierarchy = orgVersionContent.lstOrgHierarchy.Where(x => x.org_id_5 == orgId).ToList();
                    break;

                case 6:
                    lstOrgHierarchy = orgVersionContent.lstOrgHierarchy.Where(x => x.org_id_6 == orgId).ToList();
                    break;

                case 7:
                    lstOrgHierarchy = orgVersionContent.lstOrgHierarchy.Where(x => x.org_id_7 == orgId).ToList();
                    break;

                case 8:
                    lstOrgHierarchy = orgVersionContent.lstOrgHierarchy.Where(x => x.org_id_8 == orgId).ToList();
                    break;
                default:
                    break;
            }

            return lstOrgHierarchy;
        }

        private async Task<List<userList>> FilterBasedOnUserRole(List<userList> userData, int tenantId)
        {
            int CurrentRole = 2;
            List<userList> finalUserList = new List<userList>();
            List<int> userPkIds = userData.Any() ? userData.Select(x => x.pk_id).ToList() : new();
            var userRoleData = await GetUserRoleMapping(tenantId, userPkIds);
            foreach (var user in userData)
            {
                List<int> userRoleByIds = userRoleData.Any(x => x.fk_user_id == user.pk_id) ? userRoleData.Where(x => x.fk_user_id == user.pk_id).Select(x => x.fk_role_id).ToList() : new();
                if(userRoleByIds.Any(x => x >= CurrentRole))
                {
                    finalUserList.Add(user);
                }
            }
            return finalUserList;
        }

        private async Task<List<string>>GetDepartNotConnectedAtrribute(string userId, string langString, string deptLangstring)
        {
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            var userdetails = await _utility.GetUserDetailsAsync(userId);

            var departConnectedAttribute = await (from rv in dbContext.tco_relation_values
                               join av in dbContext.tco_attribute_values on new { a = rv.fk_tenant_id, b = rv.attribute_value, c = rv.attribute_type }
                                                                     equals new { a = av.fk_tenant_id, b = av.pk_attribute_id, c = av.attribute_type }
                               join dep in dbContext.tco_departments on new { a = rv.fk_tenant_id }
                                                                 equals new { a = dep.fk_tenant_id }
                               where rv.relation_value_from.CompareTo(dep.pk_department_code) <= 0 && rv.relation_value_to.CompareTo(dep.pk_department_code) >= 0
                                      && rv.fk_tenant_id == userdetails.tenant_id && rv.attribute_type == "CHAPTER" && rv.relation_type == "DEPARTMENTS"
                               select dep).ToListAsync();

            List<Department> departsList = (await _utility.GetTenantDepartmentsAsync(userId)).ToList();

            var departsNotconnected = (from a in departsList
                                       join b in departConnectedAttribute on new { a = a.departmentValue }
                                                                          equals new { a = b.pk_department_code } into grp1
                                       from grp2 in grp1.DefaultIfEmpty()
                                       where grp2 == null
                                       select new KeyValueIntKeyDataPair
                                       {
                                           Value = deptLangstring + " " + a.departmentValue + " " + langString,
                                       }).ToList();

            return departsNotconnected.Select(x => x.Value).ToList();
        }
    
        private async Task<List<tco_auth_user_role_mapping>> GetUserRoleMapping(int tenantId, List<int> pkIds)
        {
            AuthenticationDBContext authDBContext = _utility.GetAuthenticationContext();
            return await (from a in authDBContext.tco_auth_user_role_mapping
                          where a.tenant_id == tenantId && pkIds.Contains(a.fk_user_id)
                          select a).ToListAsync();
        }

    }
}