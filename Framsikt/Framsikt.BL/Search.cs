#pragma warning disable CS8625
#pragma warning disable CS8600
#pragma warning disable CS8602

using Azure;
using Azure.Search.Documents;
using Azure.Search.Documents.Indexes;
using Azure.Search.Documents.Indexes.Models;
using Azure.Search.Documents.Models;
using Azure.Storage.Blobs;
using Framsikt.BL.Constants;
using Framsikt.BL.Core.Helpers;
using Framsikt.BL.Helpers;
using Framsikt.BL.Repository;
using HtmlAgilityPack;
using Microsoft.Extensions.Azure;
using Newtonsoft.Json;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;

namespace Framsikt.BL
{
    public class Search : ISearch
    {
        private readonly IUtility _utility;
        private readonly IAzureBlobHelper _blobHelper;
        private readonly IVaultHelper _vaultHelper;
        private readonly BlobServiceClient _blobServicePublishProdClient;
        private readonly BlobServiceClient _blobServicePublishStageClient;

        public Search(IUtility utility, IAzureBlobHelper blobHelper, IAzureStorageAccountClient azureStorageAccountClient, IVaultHelper vaultHelper)
        {
            _utility = utility;
            _blobHelper = blobHelper;
            _vaultHelper = vaultHelper ?? throw new ArgumentNullException(nameof(vaultHelper));
            _blobServicePublishProdClient = azureStorageAccountClient.CreateBlobServicePublishProdClient();
            _blobServicePublishStageClient = azureStorageAccountClient.CreateBlobServicePublishStageClient();
        }

        private readonly Dictionary<string, string> _targetToServiceName = new Dictionary<string, string>()
        {
            { "Staging",  "azurePublishStagSearchServiceName" },
            { "Production", "azurePublishProdSearchServiceName" }
        };

        private readonly Dictionary<string, string> _targetToSearchKeyName = new Dictionary<string, string>()
        {
            { "Staging",  "azurePublishStagSearchApiKey" },
            { "Production", "azurePublishProdSearchApiKey" }
        };

        private readonly Dictionary<string, string> _targetToPublishUrl = new Dictionary<string, string>()
        {
            { "Staging",  "publishMrStagWebSiteUrl" },
            { "Production", "publishMrProdWebSiteUrl" }
        };

        private PublishTreeHelper _treeJson = new PublishTreeHelper();
        private string _homePageUrl = string.Empty;

        public string PopulateIndexForPublishSite(string indexName, string tenant, string version,
            PublishTreeType publishType, Target target, string folderPath, PublishTreeHelper treeJson = null, string pubUrl = null)
        {
            _treeJson = treeJson;
            //Remove everything except alphabets, numbers and -
            indexName = Regex.Replace(indexName, "[^A-Za-z0-9-]", "");
            //Remove continuous -and replace with single -
            indexName = Regex.Replace(indexName, "-+", "-");
            indexName = indexName.ToLower();
            //Not using the Azure blob helper here as we will be doing bulk operations
            
            string targetSearchServiceParamName = _targetToServiceName.FirstOrDefault(x => x.Key.Equals(target.ToString())).Value;
            string targetSearchKeyParamName = _targetToSearchKeyName.FirstOrDefault(x => x.Key.Equals(target.ToString())).Value;
            string targetPublishUrl = _targetToPublishUrl.FirstOrDefault(x => x.Key.Equals(target.ToString())).Value;

            string searchServiceName = AppConfiguration.GetConfigurationSetting(targetSearchServiceParamName);
            string apiKey = _vaultHelper.GetSecret(targetSearchKeyParamName);

            // Create a SearchIndexClient to send create/delete index commands
            Uri serviceEndpoint = new Uri($"https://{searchServiceName}.search.windows.net/");
            AzureKeyCredential credential = new AzureKeyCredential(apiKey);
            SearchIndexClient searchIndexClient = new SearchIndexClient(serviceEndpoint, credential);

            // Create a SearchClient to load and query documents
            SearchClient searchclient = new SearchClient(serviceEndpoint, indexName, credential);
            return PopulateIndex(indexName, tenant, version, publishType, target, folderPath, pubUrl, targetPublishUrl, searchIndexClient, searchclient);
        }

        private string PopulateIndex(string indexName,
                                    string tenant,
                                    string version,
                                    PublishTreeType publishType,
                                    Target target,
                                    string folderPath,
                                    string pubUrl,
                                    string targetPublishUrl,
                                    SearchIndexClient searchIndexClient,
                                    SearchClient searchClient)
        {
            var blobServiceClient = target == Target.Production ? _blobServicePublishProdClient : _blobServicePublishStageClient;
            
            BlobContainerClient blobContainer;

            string containerName = (publishType) switch
            {
                PublishTreeType.BudgetManagement or PublishTreeType.BudgetProposal =>
                    AppConfiguration.GetConfigurationSetting("azureBmPublishContainerName").ToLower(),

                PublishTreeType.PlanModule =>
                    AppConfiguration.GetConfigurationSetting("azurePlanPublishContainerName").ToLower(),

                PublishTreeType.BPSyncTree =>
                    AppConfiguration.GetConfigurationSetting("azureBudPropProcessContainerName").ToLower(),

                PublishTreeType.MRSyncTree =>
                    AppConfiguration.GetConfigurationSetting("azureMRSyncContainerName").ToLower(),

                PublishTreeType.BusPlanAppointment =>
                    AppConfiguration.GetConfigurationSetting("azureBusPlanAppointmentPublishContainerName").ToLower(),

                PublishTreeType.BusActivityPlan =>
                    AppConfiguration.GetConfigurationSetting("azureBusPlanActPublishContainerName").ToLower(),

                _ => AppConfiguration.GetConfigurationSetting("azureMrPublishContainerName").ToLower()
            };


            blobContainer = blobServiceClient.GetBlobContainerClient(containerName);

            string publishUrl = publishType == PublishTreeType.PlanOverview ? AppConfiguration.GetConfigurationSetting("publishMrProdWebSiteUrl") : AppConfiguration.GetConfigurationSetting(targetPublishUrl);
            _homePageUrl = publishUrl + pubUrl;

            //Clear the index of any documents from the same publish
            if (IsIndexExists(searchIndexClient, indexName))
            {
                DeleteFromIndex_Norwegian(indexName, version, tenant, publishType, searchIndexClient, searchClient);
            }
            //Get list of blobs
            //Add to index in batches of 1000 documents
            //var blobs = directory.ListBlobs(true);
            var blobs = blobContainer.GetBlobsByHierarchy(prefix: folderPath.ToLower());
            int i = 0;
            List<TextSearchDescription> docsForSearch = new List<TextSearchDescription>();
            List<TextNorwegianDescription> docsForNorwegianSearch = new List<TextNorwegianDescription>();

            Dictionary<string, string> blobMap = new Dictionary<string, string>();

            foreach (var blob in blobs)
            {
                string blobUri = $"{blobContainer.Uri}/{blob.Blob.Name}";
                if (blobUri.ToLower().Contains("blobmap")) continue;
                if (publishType == PublishTreeType.PlanModule && blobUri.Contains(PlanPublishConstants.documentNodeId)) continue;

                BlobClient textBlob = blobContainer.GetBlobClient(blob.Blob.Name);

                string text = string.Empty;
                using (MemoryStream mStream = new MemoryStream())
                {
                    textBlob.DownloadTo(mStream);

                    text = Encoding.UTF8.GetString(mStream.ToArray());
                }
                PublishText pubText = null;
                try
                {
                    pubText = JsonConvert.DeserializeObject<PublishText>(text);
                }
                catch
                {
                    //If deserialize fails, there is something wrong with the file
                    continue;
                }

                //If the text is of type Description, it should have an abstract or description
                if (pubText.type == PublishText.TextType.Description && string.IsNullOrEmpty(pubText.description)
                    && string.IsNullOrEmpty(pubText.abstractText))
                {
                    continue;
                }
                string contentText;
                if (pubText.type == PublishText.TextType.Title && string.IsNullOrEmpty(pubText.description))
                {
                    contentText = pubText.title.Trim();
                }
                else
                {
                    contentText = pubText.description;
                }
                string titleText = pubText.title.Trim();
                string tableContent = string.Empty;
                switch (publishType)
                {
                   case PublishTreeType.PlanModule:
                   case PublishTreeType.BudgetManagement:
                   case PublishTreeType.MonthlyReport:
                   case PublishTreeType.PoliticalSimulation:
                   case PublishTreeType.BPSyncTree:
                   case PublishTreeType.MRSyncTree:
                   case PublishTreeType.BusActivityPlan:
                   case PublishTreeType.BusPlanAppointment:
                   case PublishTreeType.BudgetProposal:
                        tableContent = ExtractTableFromHtml(pubText.description);
                        break;
                    default:
                        break;
                }
                
                IEnumerable<string> cellValues = _utility.ExtractCellValues(pubText.description);
                i++;
                
                string itemId = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{blobContainer.Name}/{blob.Blob.Name}/{i}")); // (in .Net6)

                if (pubText.type == PublishText.TextType.Title || (pubText.type != PublishText.TextType.Title && !string.IsNullOrEmpty(contentText)))
                {
                    DocsForNorwiegianSearch(tenant, version, publishType, docsForNorwegianSearch, pubText, contentText, titleText, itemId, tableContent);
                }

                foreach (var cellValue in cellValues)
                {
                    decimal outVal;
                    bool isNumeric = decimal.TryParse(cellValue, out outVal);
                    if (!isNumeric)
                    {
                        i++;
                        itemId = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{blobContainer.Name}/{blob.Blob.Name}/{i}"));// (in .Net6)
                        string cleanedValue = cellValue.Replace("\t", string.Empty).Replace("\n", string.Empty);

                        //We don't want to index numbers, hence only adding strings
                        if (!string.IsNullOrEmpty(cleanedValue.Trim()))
                        {
                            DocsForNorwiegianSearch(tenant, version, publishType, docsForNorwegianSearch, pubText, cleanedValue, titleText, itemId, tableContent);
                        }
                    }
                }
                blobMap.Add(itemId, $"{blobContainer.Name}/{blob.Blob.Name}");

                //Update the index in batchs of 999, max allowed is 1000
                //The table processing could have added elements and made the total exceed 1000

                if (docsForNorwegianSearch.Count > 999)
                {
                    var batchElements = docsForNorwegianSearch.Take(999);
                    AppendToIndex_Norweigian(indexName, batchElements, searchIndexClient, searchClient);

                    var remaining = docsForNorwegianSearch.Except(batchElements);

                    //Clear out the existing items and add back the remaining items to be processed
                    docsForNorwegianSearch.Clear();
                    docsForNorwegianSearch.AddRange(remaining);
                }
            }
            //The last batch has not yet been added to the index
            if (docsForNorwegianSearch.Any())
            {
                AppendToIndex_Norweigian(indexName, docsForNorwegianSearch, searchIndexClient, searchClient);
                docsForNorwegianSearch.Clear();
            }

            if (target == Target.Staging && (publishType == PublishTreeType.BudgetManagement
                                          || publishType == PublishTreeType.MonthlyReport
                                          || publishType == PublishTreeType.PlanModule
                                          || publishType == PublishTreeType.PoliticalSimulation
                                          || publishType == PublishTreeType.PlanOverview
                                          || publishType == PublishTreeType.BPSyncTree
                                          || publishType == PublishTreeType.MRSyncTree
                                          || publishType == PublishTreeType.BusActivityPlan
                                          || publishType == PublishTreeType.BusPlanAppointment
                                          || publishType == PublishTreeType.BudgetProposal))
            {
                string strBlobMap = JsonConvert.SerializeObject(blobMap);
                if (publishType == PublishTreeType.BudgetManagement || 
                    publishType == PublishTreeType.BudgetProposal)
                {
                    _blobHelper.UploadTextBlob(StorageAccount.PublishStage, BlobContainers.BmContent,
                        $"{folderPath}/blobmap.json", strBlobMap);
                }
                else if (publishType == PublishTreeType.PlanModule)
                {
                    _blobHelper.UploadTextBlob(StorageAccount.PublishStage, BlobContainers.PlanContent,
                        $"{folderPath}/blobmap.json", strBlobMap);
                }
                else if (publishType == PublishTreeType.PlanOverview)
                {
                    _blobHelper.UploadTextBlob(StorageAccount.PublishStage, BlobContainers.PlanOverviewContent,
                        $"{folderPath}/blobmap.json", strBlobMap);
                }
                else if (publishType == PublishTreeType.PoliticalSimulation)
                {
                    _blobHelper.UploadTextBlob(StorageAccount.PublishStage, BlobContainers.PsContent,
                        $"{folderPath}/blobmap.json", strBlobMap);
                }
                else if (publishType == PublishTreeType.BPSyncTree)
                {
                    _blobHelper.UploadTextBlob(StorageAccount.PublishStage, BlobContainers.BudPropProcessContent,
                        $"{folderPath}/blobmap.json", strBlobMap);
                }
                else if (publishType == PublishTreeType.MRSyncTree)
                {
                    _blobHelper.UploadTextBlob(StorageAccount.PublishStage, BlobContainers.MRSyncPublishContent,
                        $"{folderPath}/blobmap.json", strBlobMap);
                }
                else if (publishType == PublishTreeType.BusActivityPlan)
                {
                    _blobHelper.UploadTextBlob(StorageAccount.PublishStage, BlobContainers.BusPlanActivityContent,
                        $"{folderPath}/blobmap.json", strBlobMap);
                }
                else if (publishType == PublishTreeType.BusPlanAppointment)
                {
                    _blobHelper.UploadTextBlob(StorageAccount.PublishStage, BlobContainers.BusPlanAppointmentContent,
                        $"{folderPath}/blobmap.json", strBlobMap);
                }
                else
                {
                    _blobHelper.UploadTextBlob(StorageAccount.PublishStage, BlobContainers.MrContent,
                        $"{folderPath}/blobmap.json", strBlobMap);
                }
            }
            return indexName;
        }

        private void DocsForNorwiegianSearch(string tenant, string version, PublishTreeType publishType, List<TextNorwegianDescription> docsForSearch,
                PublishText pubText, string contentText, string titleText, string itemId, string tableContent)
        {
            pubText.pageUrl = pubText.pageUrl.Replace("info", "generic");
            if (publishType == PublishTreeType.PoliticalSimulation)
            {
                pubText.pageUrl = pubText.pageUrl.Replace("generic", "polsim");
            }
            else if (publishType == PublishTreeType.BPSyncTree || publishType == PublishTreeType.MRSyncTree) {
                pubText.pageUrl = pubText.pageUrl.Replace("generic", "budsa");
            }
            else if (publishType == PublishTreeType.BusActivityPlan || publishType == PublishTreeType.BusPlanAppointment)
            {
                var url = pubText.pageUrl.Contains("orgstructuremain");
                if (url)
                {
                    pubText.pageUrl = pubText.pageUrl.Replace("generic", "busplan");
                }
            }
            contentText = ExtractStringFromHtml(contentText);
            docsForSearch.Add(new TextNorwegianDescription()
            {
                id = itemId,
                tenant = tenant.ToLower(),
                publishtype = publishType.ToString(),
                version = version.ToLower(),
                abstractText = ExtractStringFromHtml(pubText.abstractText),
                content = contentText,
                contentnorwegian = contentText,
                title = pubText.type == PublishText.TextType.Title ? contentText : pubText.heading,
                type = pubText.type.ToString(),
                pagepath = pubText.type == PublishText.TextType.Title ? contentText : pubText.heading,
                pageurl = _homePageUrl + "#" + pubText.pageUrl.ToLower(),
                contenttable = tableContent,
                heading = publishType == PublishTreeType.PoliticalSimulation ? pubText.heading : GetHeadingText(pubText.pageUrl.ToLower().Split('?'), publishType),
                homePageUrl = _homePageUrl
            });
        }

        private string GetHeadingText(string[] parentChapterpath, PublishTreeType publishType)
        {
            string chapterTitle = string.Empty;
            if (parentChapterpath.Any() && _treeJson != null)
            {
                string chapterpath = parentChapterpath[0].Remove(parentChapterpath[0].LastIndexOf('/'));
                var chapter = publishType == PublishTreeType.BusActivityPlan || publishType == PublishTreeType.BusPlanAppointment ? _treeJson.menus.Where(z => z.type == "allpages").Select(z => z.data.Where(x => x.pageUrl.ToLower().Contains(chapterpath))).FirstOrDefault(y => y.Any()) : _treeJson.menus.Select(z => z.data.Where(x => x.pageUrl.ToLower().Contains(chapterpath))).FirstOrDefault(y => y.Any());
                if (chapter != null)
                {
                    chapterTitle = chapter.First().name;
                }
                else
                {
                    //recursvie method to get chapter title
                }
            }
            return chapterTitle;
        }

        private string ExtractStringFromHtml(string htmlString)
        {
            //List<string> unwantedChars = new List<string>() { ".", ",", "(", ")" };
            if (htmlString != null && htmlString.Length > 0)
            {
                //Remove any tables completely
                string decodedHtml = WebUtility.HtmlDecode(htmlString);
                var htmlDoc = new HtmlDocument();
                htmlDoc.LoadHtml(decodedHtml);

                //remove any tables in the html
                var tables = htmlDoc.DocumentNode.SelectNodes("//table");
                if (tables != null)
                {
                    foreach (var table in tables)
                    {
                        table.Remove();
                    }
                }
                string retVal = htmlDoc.DocumentNode.InnerText;
                
                //Replace multiple white space characters with single space
                retVal = Regex.Replace(retVal, @"\s+", " ", RegexOptions.Multiline);
                //Trim leading/ trailing spaces
                retVal = retVal.Trim();
                return retVal;
            }
            return string.Empty;
        }

        private string ExtractTableFromHtml(string htmlString)
        {
            List<string> unwantedChars = new List<string>() { ".", ",", "(", ")" };
            if (htmlString != null && htmlString.Length > 0)
            {
                //Remove any tables completely
                string decodedHtml = WebUtility.HtmlDecode(htmlString);
                var htmlDoc = new HtmlDocument();
                htmlDoc.LoadHtml(decodedHtml);

                //remove any tables in the html
                var tables = htmlDoc.DocumentNode.SelectNodes("//table");
                if (tables != null)
                {
                    StringBuilder sb = new StringBuilder();
                    foreach (var table in tables)
                    {
                        sb.Append(table.InnerText);
                    }
                    string retVal = sb.ToString();

                    //Remove unwanted characters
                    foreach (var chr in unwantedChars)
                    {
                        retVal = retVal.Replace(chr, " ");
                    }
                    //Replace multiple white space characters with single space
                    retVal = Regex.Replace(retVal, @"\s+", " ", RegexOptions.Multiline);
                    retVal = retVal.Trim();
                    return retVal;
                }
            }
            return string.Empty;
        }

        private void AppendToIndex_Norweigian(string indexName, IEnumerable<TextNorwegianDescription> descriptions, SearchIndexClient searchIndexClient, SearchClient searchClient)
        {
            indexName = indexName.ToLower();
            bool indexExists = IsIndexExists(searchIndexClient, indexName);
            if (!indexExists)
            {
                FieldBuilder fieldBuilder = new FieldBuilder();
                var searchFields = fieldBuilder.Build(typeof(TextNorwegianDescription));
                var definition = new SearchIndex(indexName, searchFields);
                searchIndexClient.CreateOrUpdateIndex(definition);
            }
            var batch = IndexDocumentsBatch.Upload(descriptions);
            Exception error = null;
            //Try thrice, if it still fails throw an exception
            for (int i = 0; i < 3; i++)
            {
                try
                {
                    error = null;
                    IndexDocumentsOptions options = new IndexDocumentsOptions { ThrowOnAnyError = true };
                    searchClient.IndexDocuments(batch, options);
                    //On success break out of the loop
                    break;
                }
                catch (Exception e2)
                {
                    if (e2.GetType() == typeof(RequestFailedException))
                    {
                        error = e2;
                        //Wait for some time before retrying
                        Thread.Sleep(2000);
                        continue;
                    }
                    throw;
                }
            }
            //If error object is not null, adding to index failed
        }

        public void DeleteFromIndex(string indexName, Target target, string version, string tenant, PublishTreeType publishType)
        {
            indexName = indexName.ToLower();
            string targetSearchServiceParamName = _targetToServiceName.FirstOrDefault(x => x.Key.Equals(target.ToString())).Value;
            string targetSearchKeyParamName = _targetToSearchKeyName.FirstOrDefault(x => x.Key.Equals(target.ToString())).Value;

            string searchServiceName = AppConfiguration.GetConfigurationSetting(targetSearchServiceParamName);
            string apiKey = _vaultHelper.GetSecret(targetSearchKeyParamName);

            // Create a SearchIndexClient to send create/delete index commands
            Uri serviceEndpoint = new Uri($"https://{searchServiceName}.search.windows.net/");
            AzureKeyCredential credential = new AzureKeyCredential(apiKey);
            SearchIndexClient searchIndexClient = new SearchIndexClient(serviceEndpoint, credential);

            // Create a SearchClient to load and query documents
            SearchClient searchclient = new SearchClient(serviceEndpoint, indexName, credential);
            DeleteFromIndex_Norwegian(indexName, version, tenant, publishType, searchIndexClient, searchclient);
        }

        private void DeleteFromIndex_Norwegian(string indexName, string version, string tenant,
            PublishTreeType publishType, SearchIndexClient searchIndexClient, SearchClient searchClient)
        {
            bool indexExists = IsIndexExists(searchIndexClient, indexName);
            if (!indexExists)
            {
                throw new InvalidOperationException($"Index {indexName} does not exist");
            }
            SearchOptions sp = new SearchOptions()
            {
                SearchMode = SearchMode.All,
                Filter = $"tenant eq '{tenant}' and version eq '{version}' and publishtype eq '{publishType.ToString()}'",
                Size = 1000
            };
            SearchResults<TextNorwegianDescription> searchRes = searchClient.Search<TextNorwegianDescription>("*", sp);
            while (searchRes.GetResults().Any())
            {
                List<TextNorwegianDescription> toBeDeleted = searchRes.GetResults().Select(x => new TextNorwegianDescription()
                {
                    id = x.Document.id
                }).ToList();
                var batch = IndexDocumentsBatch.Delete(toBeDeleted);
                //Try thrice, if it still fails throw an exception
                for (int i = 0; i < 3; i++)
                {
                    try
                    {
                        IndexDocumentsOptions options = new IndexDocumentsOptions { ThrowOnAnyError = true };
                        searchClient.IndexDocuments(batch, options);
                        //On success break out of the loop
                        break;
                    }
                    catch (Exception ex)
                    {
                        if (ex.GetType() == typeof(RequestFailedException))
                        {
                            //Wait for some time before retrying
                            Thread.Sleep(2000);
                            continue;
                        }
                        throw;
                    }
                }
                searchRes = searchClient.Search<TextNorwegianDescription>("*", sp);
            }
        }

        private bool IsIndexExists(SearchIndexClient searchIndexClient, string indexName)
        {
            var indexInfo = searchIndexClient.GetIndexes();
            return indexInfo.Any(x => x.Name == indexName);
        }
    }
}