#pragma warning disable CS8629
#pragma warning disable CS8073
#pragma warning disable CS8600
#pragma warning disable CS8601
#pragma warning disable CS8602
#pragma warning disable CS8604
#pragma warning disable CS8625

using Framsikt.BL.Helpers;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text;

namespace Framsikt.BL
{
    public class BudPropUtility : IBudPropUtility
    {
        private readonly IUtility _utility;
        private readonly IConsequenceAdjustedBudget _consAdjBudget;
        private readonly ICollaboration _collaboration;
        private readonly IFinUtility _finUtility;
        private readonly IDataSyncUtility _dataSyncUtility;
        private const SyncObjectStatusType syncStatus = SyncObjectStatusType.SYNC_OBJECT_STATUS;
        private readonly IAzureBlobHelper _azureBlobHelper;
        private readonly IUnitOfWork _unitOfWork;
        public BudPropUtility(IServiceProvider container, IAzureBlobHelper azureBlobHelper, IUnitOfWork unitOfWork)
        {
            _utility = container.GetRequiredService<IUtility>();
            _consAdjBudget = container.GetRequiredService<IConsequenceAdjustedBudget>();
            _collaboration = container.GetRequiredService<ICollaboration>();
            _finUtility = container.GetRequiredService<IFinUtility>();
            _dataSyncUtility = container.GetRequiredService<IDataSyncUtility>();
            _azureBlobHelper = azureBlobHelper;
            _unitOfWork = unitOfWork;
        }

        public void DistributeStrategyOrgList(string userId, int budgetYear, int masterStrategyId, List<OrgTreeItemGoalTgtNew> treeOrgItems, BudPropStraTxtSave data, bool isAngular = false, int type = -1)
        {
            DistributeStrategyOrgListAsync(userId, budgetYear, masterStrategyId, treeOrgItems, data, isAngular, type).GetAwaiter().GetResult();
        }

        public async Task DistributeStrategyOrgListAsync(string userId, int budgetYear, int masterStrategyId, List<OrgTreeItemGoalTgtNew> treeOrgItems, BudPropStraTxtSave data, bool isAngular = false, int type = -1 )
        {
            TenantDBContext DBContext = await _utility.GetTenantDBContextAsync();
            UserData userdata = await _utility.GetUserDetailsAsync(userId);
            List<string> processedOrgs = new List<string>();

            string paramValueFp1 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_1");
            string paramValueFp2 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_2");
            int fpLvl1OrgLevel = -1, fpLvl2OrgLevel = -1, fpLvl1ServiceLevel = -1, fpLvl2ServiceLevel = -1;
            if (!string.IsNullOrEmpty(paramValueFp1))
            {
                if (paramValueFp1.Split('_')[0].ToUpper() == "ORG")
                {
                    fpLvl1OrgLevel = int.Parse(paramValueFp1.Split('_')[2]);
                }
                else if (paramValueFp1.Split('_')[0].ToUpper() == "SERVICE")
                {
                    fpLvl1ServiceLevel = int.Parse(paramValueFp1.Split('_')[2]);
                }
            }
            if (!string.IsNullOrEmpty(paramValueFp2))
            {
                if (paramValueFp2.Split('_')[0].ToUpper() == "ORG")
                {
                    fpLvl2OrgLevel = int.Parse(paramValueFp2.Split('_')[2]);
                }
                else if (paramValueFp2.Split('_')[0].ToUpper() == "SERVICE")
                {
                    fpLvl2ServiceLevel = int.Parse(paramValueFp2.Split('_')[2]);
                }
            }

            string fpLevelTwoAllOrEmpty = await DBContext.vw_tco_parameters.Where(x => x.fk_tenant_id == userdata.tenant_id
                                                                             && x.param_name == "FINPLAN_LEVEL_2").FirstOrDefaultAsync() != null && fpLvl2ServiceLevel != -1 ? "ALL" : string.Empty;

            string prevProIdCo = string.Empty;
            string proIdCo = string.Empty;
            treeOrgItems = treeOrgItems.Where(x => x.isUseInDelegSave == true).ToList();
            Guid strategyHeaderId = await InsertIntoStrategyHeaderAsync(userdata.tenant_id, userdata.pk_id);
            foreach (var checkedItem in treeOrgItems.Where(x => (x.fpLevel == 1 || x.fpLevel == 2)).ToList())
            {
                proIdCo = checkedItem.id;
                if (!string.IsNullOrEmpty(checkedItem.parentId))
                {
                    proIdCo = proIdCo + "_" + checkedItem.parentId;
                }

                if (!processedOrgs.Contains(proIdCo) || proIdCo == prevProIdCo)
                {
                    List<tfp_strategy_text> strategyTextsData = checkedItem.fpLevel == 1 ? (fpLvl1OrgLevel != -1 ? await DBContext.tfp_strategy_text.Where(x => x.fk_tenant_id == userdata.tenant_id
                                                                                                                             && x.org_id == checkedItem.id
                                                                                                                             && !x.is_busplan
                                                                                                                             && x.org_level == fpLvl1OrgLevel
                                                                                                                             && (x.service_id.Trim() == fpLevelTwoAllOrEmpty || (string.IsNullOrEmpty(x.service_id.Trim()) && fpLevelTwoAllOrEmpty == "ALL") || (x.service_id.ToUpper() == "ALL" && string.IsNullOrEmpty(fpLevelTwoAllOrEmpty)))
                                                                                                                             && x.service_level == fpLvl1ServiceLevel).ToListAsync()
                                                                                          : await DBContext.tfp_strategy_text.Where(x => x.fk_tenant_id == userdata.tenant_id
                                                                                                                             && x.service_id == checkedItem.id
                                                                                                                             && !x.is_busplan
                                                                                                                             && x.service_level == fpLvl1ServiceLevel
                                                                                                                             && x.org_id == string.Empty
                                                                                                                             && x.org_level == 0).ToListAsync())
                                                                  : (fpLvl2OrgLevel != -1 ? await DBContext.tfp_strategy_text.Where(x => x.fk_tenant_id == userdata.tenant_id
                                                                                                                             && x.org_id == checkedItem.id
                                                                                                                             && x.org_level == fpLvl2OrgLevel
                                                                                                                             && !x.is_busplan
                                                                                                                             && (x.service_id.Trim() == fpLevelTwoAllOrEmpty || (string.IsNullOrEmpty(x.service_id.Trim()) && fpLevelTwoAllOrEmpty == "ALL") || (x.service_id.ToUpper() == "ALL" && string.IsNullOrEmpty(fpLevelTwoAllOrEmpty)))
                                                                                                                             && x.service_level == fpLvl2ServiceLevel).ToListAsync()
                                                                                          : await DBContext.tfp_strategy_text.Where(x => x.fk_tenant_id == userdata.tenant_id
                                                                                                                             && x.service_id == checkedItem.id
                                                                                                                             && !x.is_busplan
                                                                                                                             && x.service_level == fpLvl2ServiceLevel
                                                                                                                             && x.org_id == checkedItem.parentId
                                                                                                                             && x.org_level == fpLvl1OrgLevel).ToListAsync());
                    var masterStrategyInfo = strategyTextsData.FirstOrDefault(x => x.pk_strategy_id == masterStrategyId && x.master_strategy_id == null);
                    tfp_strategy_text tst = strategyTextsData.FirstOrDefault(x => x.master_strategy_id == masterStrategyId || x.pk_strategy_id == masterStrategyId);

                    if (checkedItem.@checked && masterStrategyInfo == null)
                    {
                        if (tst == null) //Add
                        {
                            tst = new tfp_strategy_text();
                            tst.master_strategy_id = masterStrategyId;
                            tst.fk_tenant_id = userdata.tenant_id;
                            tst.budget_year = budgetYear;
                            tst.strategy_name = data.strategy;
                            tst.strategy_desc = data.strategyDesc;
                            tst.is_busplan = false;
                            tst.fk_attribute_id = string.Empty;

                            if (checkedItem.fpLevel == 1)
                            {
                                if (fpLvl1OrgLevel != -1)
                                {
                                    tst.org_id = checkedItem.id;
                                    tst.org_level = fpLvl1OrgLevel;
                                    tst.service_id = fpLevelTwoAllOrEmpty;
                                    tst.service_level = fpLvl1ServiceLevel;
                                }
                                else
                                {
                                    tst.org_id = string.Empty;
                                    tst.org_level = 0;
                                    tst.service_id = checkedItem.id;
                                    tst.service_level = fpLvl1ServiceLevel;
                                }
                            }
                            else
                            {
                                if (fpLvl2OrgLevel != -1)
                                {
                                    tst.org_id = checkedItem.id;
                                    tst.org_level = fpLvl2OrgLevel;
                                    tst.service_id = fpLevelTwoAllOrEmpty;
                                    tst.service_level = fpLvl2ServiceLevel;
                                }
                                else
                                {
                                    tst.org_id = checkedItem.parentId;
                                    tst.org_level = fpLvl1OrgLevel;
                                    tst.service_id = checkedItem.id;
                                    tst.service_level = fpLvl2ServiceLevel;
                                }
                            }
                            tst.updated = DateTime.UtcNow;
                            tst.updated_by = userdata.pk_id;
                            tst.sync_status = data.syncStatus.key;
                            //insert new tags and get the list to tag ids
                            List<string> lstTags = null;
                            if (data.tagsData != null && data.tagsData.Any())
                            {
                                lstTags = await _consAdjBudget.InsertActionTagsAsync(userId, data.tagsData);
                            }
                            tst.tags = lstTags == null ? string.Empty : String.Join(",", lstTags);

                            Guid stratHistoryId = Guid.NewGuid();
                            tst.strategy_desc_history = stratHistoryId;
                            tst.fk_strategy_header_id = strategyHeaderId;
                            DBContext.tfp_strategy_text.Add(tst);

                            clsTenantCloudTableEntity _clsTenantCloudTableEntity =
                                new clsTenantCloudTableEntity(userdata.tenant_id.ToString(), stratHistoryId.ToString());
                            _clsTenantCloudTableEntity.data = data.strategyDesc;
                            _clsTenantCloudTableEntity.Timestamp = DateTime.UtcNow;
                            await _utility.InsertCloudTableContentAsync(userdata.pk_id, "WADTenantData", userdata.tenant_id.ToString(), stratHistoryId.ToString(), _clsTenantCloudTableEntity);

                            await DBContext.SaveChangesAsync();

                            if (!string.IsNullOrEmpty(data.strategyDesc))
                            {
                                await _utility.SaveTextLogAsync(userId, stratHistoryId, data.strategyDesc, null);
                            }
                        }
                        else
                        {
                            tst.budget_year = budgetYear;
                            tst.fk_tenant_id = userdata.tenant_id;
                            tst.strategy_name = data.strategy;
                            tst.strategy_desc = data.strategyDesc;
                            tst.updated_by = userdata.pk_id;
                            tst.updated = DateTime.UtcNow;
                            tst.is_busplan = false;
                            tst.fk_attribute_id = string.Empty;
                            tst.sync_status = data.syncStatus.key;
                            //insert new tags and get the list to tag ids
                            List<string> lstTags = null;
                            if (data.tagsData != null && data.tagsData.Any())
                            {
                                lstTags = await _consAdjBudget.InsertActionTagsAsync(userId, data.tagsData);
                            }
                            tst.tags = lstTags == null ? string.Empty : String.Join(",", lstTags);

                            clsTenantCloudTableEntity updateEntity = await _utility.GetCloudTableContentAsync("WADTenantData", userdata.tenant_id.ToString(), tst.strategy_desc_history.ToString());
                            //Get Previously saved text
                            string historyData = await _utility.GetHistoryDataAsync(userId, tst.strategy_desc_history.Value);
                            List<TextEditorHelper> textEditorLog = JsonConvert.DeserializeObject<List<TextEditorHelper>>(historyData);

                            if (updateEntity != null)
                            {
                                if (textEditorLog == null)
                                {
                                    var oldUserInfo = await _utility.GetUserDetailsByIdAsync(userId, tst.updated_by);
                                    if (!string.IsNullOrEmpty(updateEntity.data))
                                    {
                                        await _utility.SaveTextLogAsync(oldUserInfo.user_name, tst.strategy_desc_history.Value, updateEntity.data, tst.updated);
                                    }
                                }
                                updateEntity.data = data.strategyDesc;
                                await _utility.UpdateCloudTableContentAsync(userdata.pk_id, "WADTenantData", userdata.tenant_id.ToString(), tst.strategy_desc_history.ToString(), data.strategyDesc);
                            }
                            else
                            {
                                clsTenantCloudTableEntity _clsTenantCloudTableEntity =
                                    new clsTenantCloudTableEntity(userdata.tenant_id.ToString(), tst.strategy_desc_history.Value.ToString());
                                _clsTenantCloudTableEntity.data = data.strategyDesc;
                                _clsTenantCloudTableEntity.Timestamp = DateTime.UtcNow;
                                await _utility.InsertCloudTableContentAsync(userdata.pk_id, "WADTenantData", userdata.tenant_id.ToString(), tst.strategy_desc_history.Value.ToString(), _clsTenantCloudTableEntity);
                            }

                            if (textEditorLog != null || !string.IsNullOrEmpty(data.strategyDesc))
                            {
                                await _utility.SaveTextLogAsync(userId, tst.strategy_desc_history.Value, data.strategyDesc, null);
                            }
                            await DBContext.SaveChangesAsync();
                        }
                        //Establish strategy-goal links
                        if (data.goalTags != null)
                        {
                            await InsertLinkStrategyGoal(userId, tst.pk_strategy_id, data.goalTags);
                            await InsertLinkStrategyTarget(userId, tst.pk_strategy_id, data.targetTags);
                        }
                        if (data.assignmentsLinked != null && !isAngular)
                        {
                            await UpdateBudPlanStrategyAssignmentDataLinkedForDelegation(userdata.tenant_id, userdata.pk_id, tst.pk_strategy_id, data.assignmentsLinked, tst.org_id, tst.org_level ?? 0, tst.service_id, tst.service_level ?? 0, budgetYear);
                        }
                        processedOrgs.Add(proIdCo);
                    }

                    if (tst != null && !checkedItem.@checked && masterStrategyInfo == null)
                    {
                        List<tfp_strategy_goal> tsgDels = await DBContext.tfp_strategy_goal.Where(x => x.fk_strategy_id == tst.pk_strategy_id && x.fk_tenant_id == userdata.tenant_id).ToListAsync();
                        DBContext.tfp_strategy_goal.RemoveRange(tsgDels);
                        List<tfp_strategy_target> tstDels = await DBContext.tfp_strategy_target.Where(x => x.fk_strategy_id == tst.pk_strategy_id && x.fk_tenant_id == userdata.tenant_id).ToListAsync();
                        DBContext.tfp_strategy_target.RemoveRange(tstDels);
                        if (!isAngular)
                        {
                            DBContext.TbiAssignmentStrategy.RemoveRange(await DBContext.TbiAssignmentStrategy.Where(x => x.fk_tenant_id == userdata.tenant_id && x.fk_strategy_id == tst.pk_strategy_id).ToListAsync());
                        }
                        DBContext.tfp_strategy_text.Remove(tst);
                        processedOrgs.Add(proIdCo);
                        await DBContext.SaveChangesAsync();
                    }
                }
                prevProIdCo = proIdCo;
            }
        }

        private async Task UpdateBudPlanStrategyAssignmentDataLinkedForDelegation(int tenantId, int userId, int strategyId, List<string> assignmentIds, string orgId, int orgLevel, string serviceId, int servicelevel, int budgetYear)
        {
            TenantDBContext _tenantDBContext = await _utility.GetTenantDBContextAsync();

            List<TbiAssignmentStrategy> stgDt = await _tenantDBContext.TbiAssignmentStrategy.Where(x => x.fk_tenant_id == tenantId && x.fk_strategy_id == strategyId && x.budget_year == budgetYear).ToListAsync();
            _tenantDBContext.TbiAssignmentStrategy.RemoveRange(stgDt);
            foreach (var assignment in assignmentIds)
            {
                var assignmentDetails = await _tenantDBContext.tbiassignments.FirstOrDefaultAsync(x => x.assignmentId.ToString() == assignment && x.tenantId == tenantId);
                var delegatedAssignmentDetails = (string.IsNullOrEmpty(serviceId) || serviceId == "-1" || serviceId == "ALL") ? await _tenantDBContext.tbiassignments.FirstOrDefaultAsync(x => x.uniqueassignmentId == assignmentDetails.uniqueassignmentId && x.tenantId == tenantId && x.orgId == orgId && x.orgLevel == orgLevel && x.budgetYear == budgetYear)
                    : await _tenantDBContext.tbiassignments.FirstOrDefaultAsync(x => x.uniqueassignmentId == assignmentDetails.uniqueassignmentId && x.tenantId == tenantId && x.orgId == orgId && x.orgLevel == orgLevel && x.serviceId == serviceId && x.serviceLevel == servicelevel && x.budgetYear == budgetYear);
                if (delegatedAssignmentDetails != null)
                {
                    TbiAssignmentStrategy data = new TbiAssignmentStrategy()
                    {
                        fk_assignment_id = delegatedAssignmentDetails.assignmentId,
                        fk_strategy_id = strategyId,
                        fk_tenant_id = tenantId,
                        budget_year = budgetYear,
                        updated_by = userId,
                        updated = DateTime.UtcNow
                    };
                    _tenantDBContext.TbiAssignmentStrategy.Add(data);
                }
            }
            await _tenantDBContext.SaveChangesAsync();
        }

        private async Task InsertLinkStrategyTarget(string userId, int strategyId, List<Guid> targetsLinked)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            List<tfp_strategy_target> stgDt = await dbContext.tfp_strategy_target.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.fk_strategy_id == strategyId).ToListAsync();
            dbContext.tfp_strategy_target.RemoveRange(stgDt);
            foreach (Guid tLink in targetsLinked)
            {
                tfp_strategy_target data = new tfp_strategy_target()
                {
                    fk_target_id = tLink,
                    fk_strategy_id = strategyId,
                    fk_tenant_id = userDetails.tenant_id,
                    updated_by = userDetails.pk_id,
                    updated = DateTime.UtcNow
                };
                dbContext.tfp_strategy_target.Add(data);
            }
            await dbContext.SaveChangesAsync();
        }

        private async Task InsertLinkStrategyGoal(string userId, int strategyId, List<KeyValue> goalsLinked)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            List<tfp_strategy_goal> stgDt = await dbContext.tfp_strategy_goal.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.fk_strategy_id == strategyId).ToListAsync();
            dbContext.tfp_strategy_goal.RemoveRange(stgDt);
            foreach (KeyValue gLink in goalsLinked)
            {
                tfp_strategy_goal data = new tfp_strategy_goal()
                {
                    fk_goal_id = Guid.Parse(gLink.Key),
                    fk_strategy_id = strategyId,
                    fk_tenant_id = userDetails.tenant_id,
                    updated_by = userDetails.pk_id,
                    updated = DateTime.UtcNow
                };
                dbContext.tfp_strategy_goal.Add(data);
            }
            await dbContext.SaveChangesAsync();
        }

        public List<string> InsertFocusArea(string userId, List<KeyValueData> focusData, int budgetYear)
        {
            return InsertFocusAreaAsync(userId, focusData, budgetYear).GetAwaiter().GetResult();
        }

        public async Task<List<string>> InsertFocusAreaAsync(string userId, List<KeyValueData> focusData, int budgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            List<string> lstFocusArea = focusData.Where(x => x.KeyId >= 100).Select(x => x.KeyId.ToString()).ToList();
            foreach (var tagItem in focusData.Where(x => x.KeyId < 100).ToList())
            {
                if (await tenantDbContext.tco_focusarea.FirstOrDefaultAsync(x => x.focusarea_description.Trim() == tagItem.ValueString.Trim() && x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear) == null)
                {
                    tco_focusarea tfa = new tco_focusarea();
                    tfa.fk_tenant_id = userDetails.tenant_id;
                    tfa.focusarea_description = tagItem.ValueString.Trim();
                    tfa.updated = DateTime.UtcNow;
                    tfa.updated_by = userDetails.pk_id;
                    tfa.focusarea_longdescription = string.Empty;
                    tfa.focusarea_monthly_report_description = string.Empty;
                    tfa.focusarea_monthly_report_description2 = string.Empty;
                    tfa.budget_year = budgetYear;
                    await tenantDbContext.tco_focusarea.AddAsync(tfa);
                    await tenantDbContext.SaveChangesAsync();
                    lstFocusArea.Add(tfa.pk_id.ToString());
                }
            }
            return lstFocusArea;
        }

        public void AddGoalDistriTcox(string userId, int budgetYear, Guid goalId, bool isBusPlanGoal, CityGoalsData gData, bool isDelegatedGoal,
                                     string orgId, int orgLevel, string serviceId, int serviceLevel, bool procesedTags, List<string> connectedUserNames = null, bool LogHistory = true)
        {
            AddGoalDistriTcoxAsync(userId, budgetYear, goalId, isBusPlanGoal, gData, isDelegatedGoal,
                                   orgId, orgLevel, serviceId, serviceLevel, procesedTags, connectedUserNames, LogHistory).GetAwaiter().GetResult();
        }

        public async Task AddGoalDistriTcoxAsync(string userId, int budgetYear, Guid goalId, bool isBusPlanGoal, CityGoalsData gData, bool isDelegatedGoal, string orgId, int orgLevel, string serviceId, int serviceLevel, bool procesedTags, List<string> connectedUserNames = null, bool LogHistory = true)
        {           
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var goalDistDataAsync = _unitOfWork.BudgetProposalRepository.GetGoalDistrDataByGoalId(userDetails.tenant_id, goalId);
            var tagsDataAsync = _unitOfWork.BudgetProposalRepository.GetActionTagsData(userDetails.tenant_id);

            await Task.WhenAll(goalDistDataAsync, tagsDataAsync);
            List<tco_goals_distribution> tcgdList = goalDistDataAsync.Result;
            List<TcoActionTags> tagsData = tagsDataAsync.Result;

            List<string> serviceIdWithAll = new List<string> { "-1", "ALL", "all" };
            tco_goals_distribution tcgd;
            tcgdList = string.IsNullOrEmpty(serviceId) ? tcgdList.Where(x => x.org_id == orgId
                                                                                   && x.org_level == orgLevel).ToList() :
                                                                            tcgdList.Where(x => x.org_id == orgId
                                                                                   && x.org_level == orgLevel
                                                                                   && x.service_id == serviceId).ToList();

            if (tcgdList.Count > 1)
            {
                if (string.IsNullOrEmpty(serviceId))
                    tcgd = tcgdList.FirstOrDefault(x => serviceIdWithAll.Contains(x.service_id));
                else
                    tcgd = tcgdList.FirstOrDefault();
            }
            else
            {
                tcgd = tcgdList.FirstOrDefault();
            }

            if (tcgd != null)
            {
                tcgd.updated = DateTime.UtcNow;
                tcgd.updated_by = userDetails.pk_id;

                //insert new tags and get the list to tag ids
                List<string> lstTags = null;
                if (gData.tagsData != null && gData.tagsData.Any())
                {
                    lstTags = await InsertActionTagsForDelegationAsync(userDetails.pk_id, userDetails.tenant_id, gData.tagsData, tagsData);
                }
                tcgd.tags = lstTags == null ? string.Empty : String.Join(",", lstTags);

                if (procesedTags)
                {
                    tcgd.tags = gData.Tags;
                }

                if (tcgd.focus_blob_id == null || tcgd.focus_blob_id == Guid.Empty)
                    tcgd.focus_blob_id = Guid.NewGuid();
                if (tcgd.target_blob_id == null || tcgd.target_blob_id == Guid.Empty)
                    tcgd.target_blob_id = Guid.NewGuid();
                await _utility.SaveAzureTableDescription(userId, tcgd.target_blob_id.ToString(), gData.targettext, 0);
                await _utility.SaveAzureTableDescription(userId, tcgd.focus_blob_id.ToString(), gData.focustext, 0);
                if (LogHistory)
                {
                    await _utility.SaveTextLogAsync(userId, tcgd.target_blob_id, gData.targettext, null, "", connectedUserNames);
                    await _utility.SaveTextLogAsync(userId, tcgd.focus_blob_id, gData.focustext, null, "", connectedUserNames);
                }

                if (isDelegatedGoal)
                {
                    Guid goalBusplanDescId = tcgd.delgo_blob_id != null && tcgd.delgo_blob_id != Guid.Empty ? tcgd.delgo_blob_id ?? Guid.NewGuid() : Guid.NewGuid();
                    tcgd.delgo_blob_id = goalBusplanDescId;
                    await _utility.SaveAzureTableDescription(userId, tcgd.delgo_blob_id.ToString(), gData.delgotext, 0);
                    if (LogHistory)
                        await _utility.SaveTextLogAsync(userId, goalBusplanDescId, gData.delgotext, null, "", connectedUserNames);
                }
                _unitOfWork.GenericRepo.Update(tcgd);
                await _unitOfWork.CompleteAsync();
            }
            else
            {
                tco_goals_distribution newTcgd = new tco_goals_distribution();
                if (!isDelegatedGoal)
                    newTcgd.pk_goal_distribution_id = goalId;
                else
                    newTcgd.pk_goal_distribution_id = Guid.NewGuid();
                newTcgd.fk_tenant_id = userDetails.tenant_id;
                newTcgd.fk_goal_id = goalId;
                newTcgd.org_id = orgId;
                newTcgd.org_level = orgLevel;
                newTcgd.service_id = serviceId;
                newTcgd.service_level = -1;
                newTcgd.is_busplan_delgoa = isBusPlanGoal;
                newTcgd.fk_attribute_id = string.Empty;
                newTcgd.updated = DateTime.UtcNow;
                newTcgd.updated_by = userDetails.pk_id;

                //insert new tags and get the list to tag ids
                List<string> lstTags = null;
                if (gData.tagsData != null && gData.tagsData.Any())
                {
                    lstTags = await InsertActionTagsForDelegationAsync(userDetails.pk_id, userDetails.tenant_id, gData.tagsData, tagsData);
                }
                newTcgd.tags = lstTags == null ? string.Empty : String.Join(",", lstTags);

                if (procesedTags)
                {
                    newTcgd.tags = gData.Tags;
                }

                Guid targetguId = Guid.NewGuid();
                Guid focusguId = Guid.NewGuid();
                Guid delgoguId = Guid.NewGuid();
                newTcgd.target_blob_id = targetguId;
                newTcgd.focus_blob_id = focusguId;
                newTcgd.delgo_blob_id = delgoguId;
                await _unitOfWork.GenericRepo.AddAsync(newTcgd);
                await _unitOfWork.CompleteAsync();

                await _utility.SaveAzureTableDescription(userId, targetguId.ToString(), gData.targettext, 0);
                await _utility.SaveAzureTableDescription(userId, focusguId.ToString(), gData.focustext, 0);
                await _utility.SaveTextLogAsync(userId, targetguId, gData.targettext, null, "", connectedUserNames);
                await _utility.SaveTextLogAsync(userId, focusguId, gData.focustext, null, "", connectedUserNames);            
               
            }
        }

        public void DistributeGoalOrgListTcox(string userId, int budgetYear, Guid gGoalId, bool isBusPlanGoal, List<OrgTreeItemGoalTgt> treeOrgItems, CityGoalsData data)
        {
            DistributeGoalOrgListTcoxAsync(userId, budgetYear, gGoalId, isBusPlanGoal, treeOrgItems, data).GetAwaiter().GetResult();
        }

        public async Task DistributeGoalOrgListTcoxAsync(string userId, int budgetYear, Guid gGoalId, bool isBusPlanGoal, List<OrgTreeItemGoalTgt> treeOrgItems, CityGoalsData data)
        {
            UserData userdata = await _utility.GetUserDetailsAsync(userId);
            List<tco_goals_distribution> bulkInsertGoalData = new List<tco_goals_distribution>();
            List<tco_goals_distribution> bulkUpdateGoalData = new List<tco_goals_distribution>();
            List<tco_goals_distribution> BulkDeleteGoalData = new List<tco_goals_distribution>();

            var goalDistDataAsync = _unitOfWork.BudgetProposalRepository.GetGoalDistrDataByGoalId(userdata.tenant_id, gGoalId);
            var tagsDataAsync = _unitOfWork.BudgetProposalRepository.GetActionTagsData(userdata.tenant_id);
            var strategyDataAsync = _unitOfWork.BudgetProposalRepository.GetStrategyDetailData(userdata.tenant_id, budgetYear);
            var strategyGoalDataAsync = _unitOfWork.BudgetProposalRepository.GetStrategyGoalDetailData(userdata.tenant_id, gGoalId);
            var targetGoalDataAsync = _unitOfWork.BudgetProposalRepository.GetTargetsConnectedToGoal(userdata.tenant_id, budgetYear, gGoalId);

            await Task.WhenAll(goalDistDataAsync, tagsDataAsync, strategyDataAsync, strategyGoalDataAsync, targetGoalDataAsync);
            List<tco_goals_distribution> goalDistData = goalDistDataAsync.Result;
            List<TcoActionTags> tagsData = tagsDataAsync.Result;
            List<tfp_strategy_text> strategyData = strategyDataAsync.Result;
            List<tfp_strategy_goal> strategyGoalData = strategyGoalDataAsync.Result;
            List<tco_targets> targetGoalData = targetGoalDataAsync.Result;

            List<Guid> targetConnectedToGoalIds = targetGoalData.Select(x => x.pk_target_id).ToList();
            var targetDistrGoalData = await _unitOfWork.BudgetProposalRepository.GetTargetDistributionDetailData(userdata.tenant_id, targetConnectedToGoalIds);

            List<Guid> allTargetDetailDistrIds = targetDistrGoalData.Select(x => x.pk_target_distribution_id).ToList();
            var allTargetDetailDistrData = await _unitOfWork.BudgetProposalRepository.GetAllTargetDistrDetailData(userdata.tenant_id, allTargetDetailDistrIds);

            List<string> descIdsToDelete = new List<string>();
            List<string> goalDescIdsToInsert = new List<string>();
            List<string> goalFinplanDescIdsToInsert = new List<string>();
            List<string> processedOrgs = new List<string>();
            bool isMasterGoal = false;
            bool isFinplanSetup2 = false;
            string paramValueFp1 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_1");
            string paramValueFp2 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_2");
            int fpLvl1OrgLevel = -1, fpLvl2OrgLevel = -1, fpLvl1ServiceLevel = -1, fpLvl2ServiceLevel = -1;
            if (!string.IsNullOrEmpty(paramValueFp1))
            {
                if (paramValueFp1.Split('_')[0].ToUpper() == "ORG")
                {
                    fpLvl1OrgLevel = int.Parse(paramValueFp1.Split('_')[2]);
                }
                else if (paramValueFp1.Split('_')[0].ToUpper() == "SERVICE")
                {
                    fpLvl1ServiceLevel = int.Parse(paramValueFp1.Split('_')[2]);
                }
            }
            if (!string.IsNullOrEmpty(paramValueFp2))
            {
                if (paramValueFp2.Split('_')[0].ToUpper() == "ORG")
                {
                    fpLvl2OrgLevel = int.Parse(paramValueFp2.Split('_')[2]);
                }
                else if (paramValueFp2.Split('_')[0].ToUpper() == "SERVICE")
                {
                    fpLvl2ServiceLevel = int.Parse(paramValueFp2.Split('_')[2]);
                }
            }
            if (data.orgLevel == 1 && fpLvl1OrgLevel > 2)
            {
                isFinplanSetup2 = true;
            }
            string fpLevelTwoAllOrEmpty = paramValueFp2 != string.Empty && fpLvl2ServiceLevel != -1 ? "ALL" : string.Empty;
            string nextProIdCo = string.Empty;
            string prevProIdCo = string.Empty;
            string proIdCo = string.Empty;
            List<OrgTreeItemGoalTgt> fpTreeorgItems = new List<OrgTreeItemGoalTgt>();
            if (!isFinplanSetup2)
            {
                fpTreeorgItems = treeOrgItems.Where(x => (x.fpLevel == 1 || x.fpLevel == 2)).ToList();
            }
            else
            {
                fpTreeorgItems = treeOrgItems.Where(x => (x.fpLevel != 0)).ToList();
            }

            int toiNextCounter = 0;
            foreach (var checkedItem in fpTreeorgItems)
            {
                nextProIdCo = string.Empty;
                proIdCo = checkedItem.id;

                if (!string.IsNullOrEmpty(checkedItem.parentId))
                {
                    proIdCo = proIdCo + "_" + checkedItem.parentId;
                }

                toiNextCounter++;

                if (toiNextCounter < fpTreeorgItems.Count)
                {
                    nextProIdCo = !string.IsNullOrEmpty(fpTreeorgItems.ElementAt(toiNextCounter).parentId) ? fpTreeorgItems.ElementAt(toiNextCounter).id + "_" + fpTreeorgItems.ElementAt(toiNextCounter).parentId : fpTreeorgItems.ElementAt(toiNextCounter).id;
                }

                if (!processedOrgs.Contains(proIdCo) || proIdCo == prevProIdCo)
                {
                    tco_goals_distribution tcgd = new tco_goals_distribution();
                    if (isFinplanSetup2)
                    {
                        switch (checkedItem.fpLevel)
                        {
                            case 1:
                                tcgd = goalDistData.FirstOrDefault(x => x.org_id == checkedItem.id
                                && x.org_level == 2
                                );
                                break;

                            case 2:
                                if (fpLvl1OrgLevel != -1)
                                {
                                    tcgd = goalDistData.FirstOrDefault(x => x.org_id == checkedItem.id
                                  && x.org_level == fpLvl1OrgLevel
                                  && x.service_id == fpLevelTwoAllOrEmpty);
                                }
                                else
                                {
                                    tcgd = goalDistData.FirstOrDefault(x => x.service_id == checkedItem.id
                                    && x.org_id == string.Empty
                                    && x.org_level == 0);
                                }
                                break;

                            case 3:
                                if (fpLvl2OrgLevel != -1)
                                {
                                    tcgd = goalDistData.FirstOrDefault(x => x.org_id == checkedItem.id
                                    && x.org_level == fpLvl2OrgLevel
                                    && x.service_id == fpLevelTwoAllOrEmpty);
                                }
                                else
                                {
                                    tcgd = goalDistData.FirstOrDefault(x => x.service_id == checkedItem.id
                                    && x.org_id == checkedItem.parentId
                                    && x.org_level == fpLvl1OrgLevel);
                                }
                                break;
                        }
                    }
                    else
                    {
                        tcgd = checkedItem.fpLevel == 1 ? (fpLvl1OrgLevel != -1 ? goalDistData.FirstOrDefault(x => x.org_id == checkedItem.id
                                                                                                                            && x.org_level == fpLvl1OrgLevel
                                                                                                                            && x.service_id == fpLevelTwoAllOrEmpty)
                                                                                         : goalDistData.FirstOrDefault(x =>  x.service_id == checkedItem.id
                                                                                                                            && x.org_id == string.Empty
                                                                                                                            && x.org_level == 0))
                                                                           : (fpLvl2OrgLevel != -1 ? goalDistData.FirstOrDefault( x => x.org_id == checkedItem.id
                                                                                                                            && x.org_level == fpLvl2OrgLevel
                                                                                                                            && x.service_id == fpLevelTwoAllOrEmpty)
                                                                                         : goalDistData.FirstOrDefault(x => x.service_id == checkedItem.id
                                                                                                                            && x.org_id == checkedItem.parentId
                                                                                                                            && x.org_level == fpLvl1OrgLevel));
                    }

                    if (checkedItem.@checked == true)
                    {
                        if (tcgd == null) //Add
                        {
                            tcgd = new tco_goals_distribution();
                            tcgd.pk_goal_distribution_id = isMasterGoal == true ? gGoalId : Guid.NewGuid();
                            tcgd.fk_goal_id = gGoalId;
                            tcgd.fk_tenant_id = userdata.tenant_id;
                            tcgd.fk_attribute_id = string.Empty;
                            isMasterGoal = false;
                            if (isFinplanSetup2)
                            {
                                if (checkedItem.fpLevel == 1)
                                {
                                    tcgd.org_id = checkedItem.id;
                                    tcgd.org_level = 2; //level
                                    tcgd.service_id = string.Empty;
                                    tcgd.service_level = -1;
                                }
                                else if (checkedItem.fpLevel == 2)
                                {
                                    if (fpLvl1OrgLevel != -1)
                                    {
                                        tcgd.org_id = checkedItem.id;
                                        tcgd.org_level = fpLvl1OrgLevel; //level
                                        tcgd.service_id = fpLevelTwoAllOrEmpty;
                                        tcgd.service_level = fpLvl1ServiceLevel;
                                    }
                                    else
                                    {
                                        tcgd.org_id = string.Empty;
                                        tcgd.org_level = 0;
                                        tcgd.service_id = checkedItem.id;
                                        tcgd.service_level = fpLvl1ServiceLevel;
                                    }
                                }
                                else
                                {
                                    if (fpLvl2OrgLevel != -1)
                                    {
                                        tcgd.org_id = checkedItem.id;
                                        tcgd.org_level = fpLvl2OrgLevel;
                                        tcgd.service_id = fpLevelTwoAllOrEmpty;
                                        tcgd.service_level = fpLvl2ServiceLevel;
                                    }
                                    else
                                    {
                                        tcgd.org_id = checkedItem.parentId;
                                        tcgd.org_level = fpLvl1OrgLevel;
                                        tcgd.service_id = checkedItem.id;
                                        tcgd.service_level = fpLvl2ServiceLevel;
                                    }
                                }
                            }
                            else
                            {
                                if (checkedItem.fpLevel == 1)
                                {
                                    if (fpLvl1OrgLevel != -1)
                                    {
                                        tcgd.org_id = checkedItem.id;
                                        tcgd.org_level = fpLvl1OrgLevel;
                                        tcgd.service_id = fpLevelTwoAllOrEmpty;
                                        tcgd.service_level = fpLvl1ServiceLevel;
                                    }
                                    else
                                    {
                                        tcgd.org_id = string.Empty;
                                        tcgd.org_level = 0;
                                        tcgd.service_id = checkedItem.id;
                                        tcgd.service_level = fpLvl1ServiceLevel;
                                    }
                                }
                                else
                                {
                                    if (fpLvl2OrgLevel != -1)
                                    {
                                        tcgd.org_id = checkedItem.id;
                                        tcgd.org_level = fpLvl2OrgLevel;
                                        tcgd.service_id = fpLevelTwoAllOrEmpty;
                                        tcgd.service_level = fpLvl2ServiceLevel;
                                    }
                                    else
                                    {
                                        tcgd.org_id = checkedItem.parentId;
                                        tcgd.org_level = fpLvl1OrgLevel;
                                        tcgd.service_id = checkedItem.id;
                                        tcgd.service_level = fpLvl2ServiceLevel;
                                    }
                                }
                            }
                            tcgd.is_busplan_delgoa = isBusPlanGoal;
                            tcgd.updated = DateTime.UtcNow;
                            tcgd.updated_by = userdata.pk_id;
                            //insert new tags and get the list to tag ids
                            List<string> lstTags = null;
                            if (data.tagsData != null && data.tagsData.Any())
                            {
                                lstTags = await InsertActionTagsForDelegationAsync(userdata.pk_id, userdata.tenant_id, data.tagsData, tagsData);
                            }
                            tcgd.tags = lstTags == null ? string.Empty : String.Join(",", lstTags);

                            Guid targetguId = Guid.NewGuid();
                            Guid focusguId = Guid.NewGuid();
                            tcgd.target_blob_id = targetguId;
                            tcgd.focus_blob_id = focusguId;

                            goalFinplanDescIdsToInsert.Add(tcgd.focus_blob_id.ToString());
                            goalDescIdsToInsert.Add(tcgd.target_blob_id.ToString());
                           
                            bulkInsertGoalData.Add(tcgd);

                        }
                        else
                        {
                            tcgd.fk_tenant_id = userdata.tenant_id;
                            tcgd.is_busplan_delgoa = isBusPlanGoal;
                            tcgd.fk_attribute_id = string.Empty;
                            tcgd.updated_by = userdata.pk_id;
                            tcgd.updated = DateTime.UtcNow;

                            goalFinplanDescIdsToInsert.Add(tcgd.focus_blob_id.ToString());
                            goalDescIdsToInsert.Add(tcgd.target_blob_id.ToString());

                            //insert new tags and get the list to tag ids
                            List<string> lstTags = null;
                            if (data.tagsData != null && data.tagsData.Any())
                            {
                                lstTags = await InsertActionTagsForDelegationAsync(userdata.pk_id, userdata.tenant_id, data.tagsData, tagsData);
                            }
                            tcgd.tags = lstTags == null ? string.Empty : String.Join(",", lstTags);
                            bulkUpdateGoalData.Add(tcgd);                            
                        }
                        processedOrgs.Add(proIdCo);
                        await UpdateGoalStrategyLinkDelegation(userdata.pk_id, userdata.tenant_id, gGoalId, data.goalStrategies.Select(x => x.KeyId).ToList(), tcgd.org_id, (tcgd.org_level ?? 0), tcgd.service_id, (tcgd.service_level ?? 0), strategyData, strategyGoalData);
                    }

                    if (tcgd != null && checkedItem.@checked == false && nextProIdCo != proIdCo)
                    {
                        descIdsToDelete.Add(tcgd.focus_blob_id.ToString());
                        descIdsToDelete.Add(tcgd.target_blob_id.ToString());

                        await DeleteDelegatedGoalsAsync(userId, userdata.tenant_id, "goal", goalDistData, targetGoalData, targetDistrGoalData, allTargetDetailDistrData, budgetYear, tcgd.org_id, tcgd.org_level ?? 0, Guid.Empty, fpTreeorgItems);

                        isMasterGoal = tcgd.fk_goal_id == tcgd.pk_goal_distribution_id ? true : false;
                        await DeleteConnectedTarget(tcgd.org_id, tcgd.org_level, tcgd.service_id, targetGoalData, targetDistrGoalData, allTargetDetailDistrData);
                        BulkDeleteGoalData.Add(tcgd);
                        processedOrgs.Add(proIdCo);

                    }
                }
                prevProIdCo = proIdCo;
            }              
            if (bulkInsertGoalData.Any())
            {
                var distinctGoalData = bulkInsertGoalData
                .GroupBy(g => new { g.org_id, g.org_level, g.service_id })
                .Select(g => g.First())
                .ToList();
                if(distinctGoalData.Any()) 
                   _unitOfWork.GenericRepo.BulkInsert(distinctGoalData);

            }
            
            if (bulkUpdateGoalData.Any())
                _unitOfWork.GenericRepo.BulkUpdate(bulkUpdateGoalData);
            if (BulkDeleteGoalData.Any())
                _unitOfWork.GenericRepo.BulkDelete(BulkDeleteGoalData);
            await _unitOfWork.CompleteAsync();

            if (goalDescIdsToInsert.Any())
                await _utility.AddOrUpdateAzureTableDescriptionsAsync(userId, data.targettext,goalDescIdsToInsert,0, true);
            if (goalFinplanDescIdsToInsert.Any())
                await _utility.AddOrUpdateAzureTableDescriptionsAsync(userId, data.focustext, goalFinplanDescIdsToInsert,0, true);
            if (descIdsToDelete.Any())
                await _utility.DeleteAzuretableDecription(userdata.tenant_id, descIdsToDelete);
            
        }
        public async Task<List<string>> InsertActionTagsForDelegationAsync(int pkId, int tenantId, List<KeyValueData> tagsData, List<TcoActionTags> tagDetailData)
        {
            List<string> lstTags = tagsData.Where(x => x.KeyId >= 100).Select(x => x.KeyId.ToString()).ToList();

            foreach (var tagItem in tagsData.Where(x => x.KeyId < 100).ToList())
            {
                TcoActionTags tagData = tagDetailData.FirstOrDefault(x => x.TagDescription.Trim() == tagItem.ValueString.Trim());
                if (tagData == null)
                {
                    TcoActionTags tat = new TcoActionTags();
                    tat.FkTenantId = tenantId;
                    tat.TagDescription = tagItem.ValueString.Trim();
                    tat.Updated = DateTime.UtcNow;
                    tat.UpdatedBy = pkId;
                    await _unitOfWork.GenericRepo.AddAsync(tat);
                    await _unitOfWork.CompleteAsync();
                    lstTags.Add(tat.PkId.ToString());
                }
            }
            return lstTags;
        }
        public async Task GoalDelegationSaveForChapterTenantsAsync(string userId, int budgetYear, Guid goalId, bool isBusPlanGoal, List<OrgTreeItemGoalTgt> treeOrgItems, CityGoalsData data)
        {            
            UserData userdata = await _utility.GetUserDetailsAsync(userId);

            List<tco_goals_distribution> bulkInsertGoalData = new List<tco_goals_distribution>();
            List<tco_goals_distribution> bulkUpdateGoalData = new List<tco_goals_distribution>();
            List<tco_goals_distribution> bulkDeleteGoalData = new List<tco_goals_distribution>();
          
            List<tco_goals_distribution> goalDistributionData = await _unitOfWork.BudgetProposalRepository.GetGoalDistrDataByGoalId(userdata.tenant_id, goalId);
                        
            List<string> descIdsToDelete = new List<string>();
            List<string> goalDescIdsToInsert = new List<string>();
            List<string> goalFinplanDescIdsToInsert = new List<string>();
            foreach (var item in treeOrgItems)
            {
                var goalDetail = item.fpLevel == 0 ? goalDistributionData.FirstOrDefault(x => x.org_id == item.id && x.fk_attribute_id == string.Empty) :
                                                    goalDistributionData.FirstOrDefault(x => x.org_id == item.parentId && x.fk_attribute_id == item.id);

                if (goalDetail == null && item.@checked == true) // Add
                {
                    goalDetail = new tco_goals_distribution();
                    goalDetail.pk_goal_distribution_id = item.fpLevel == 0 ? goalId : Guid.NewGuid();
                    goalDetail.fk_goal_id = goalId;
                    goalDetail.fk_tenant_id = userdata.tenant_id;
                    goalDetail.org_id = item.fpLevel == 0 ? item.id : item.parentId;
                    goalDetail.org_level = 1;
                    goalDetail.service_id = string.Empty;
                    goalDetail.service_level = -1;
                    goalDetail.fk_attribute_id = item.fpLevel == 0 ? string.Empty : item.id;
                    goalDetail.target_blob_id = Guid.NewGuid();
                    goalDetail.focus_blob_id = Guid.NewGuid();
                    goalDetail.delgo_blob_id = null;
                    goalDetail.is_busplan_delgoa = isBusPlanGoal;
                    goalDetail.updated = DateTime.UtcNow;
                    goalDetail.updated_by = userdata.pk_id;
                    //insert new tags and get the list to tag ids
                    List<string> lstTags = null;
                    if (data.tagsData != null && data.tagsData.Any())
                    {
                        lstTags = await _consAdjBudget.InsertActionTagsAsync(userId, data.tagsData);
                    }
                    goalDetail.tags = lstTags == null ? string.Empty : String.Join(",", lstTags);                    

                    goalFinplanDescIdsToInsert.Add(goalDetail.focus_blob_id.ToString());
                    goalDescIdsToInsert.Add(goalDetail.target_blob_id.ToString());



                  
                    bulkInsertGoalData.Add(goalDetail);
                }
                else if (goalDetail != null && item.@checked == true)
                {
                    goalDetail.fk_tenant_id = userdata.tenant_id;
                    goalDetail.is_busplan_delgoa = isBusPlanGoal;
                    goalDetail.updated_by = userdata.pk_id;
                    goalDetail.updated = DateTime.UtcNow;

                    goalFinplanDescIdsToInsert.Add(goalDetail.focus_blob_id.ToString());
                    goalDescIdsToInsert.Add(goalDetail.target_blob_id.ToString());

                    //insert new tags and get the list to tag ids
                    List<string> lstTags = null;
                    if (data.tagsData != null && data.tagsData.Any())
                    {
                        lstTags = await _consAdjBudget.InsertActionTagsAsync(userId, data.tagsData);
                    }
                    goalDetail.tags = lstTags == null ? string.Empty : String.Join(",", lstTags);
                    bulkUpdateGoalData.Add(goalDetail);
                }
                else if (goalDetail != null && item.@checked == false)
                {
                    descIdsToDelete.Add(goalDetail.focus_blob_id.ToString());
                    descIdsToDelete.Add(goalDetail.target_blob_id.ToString());
                    bulkDeleteGoalData.Add(goalDetail);
                }
            }            
           
            if (bulkInsertGoalData.Any())
            {
                var distinctGoalData = bulkInsertGoalData
                .GroupBy(g => new { g.org_id, g.org_level, g.service_id })
                .Select(g => g.First())
                .ToList();
                if (distinctGoalData.Any())
                    _unitOfWork.GenericRepo.BulkInsert(distinctGoalData);

            }
            if (bulkUpdateGoalData.Any())
                _unitOfWork.GenericRepo.BulkUpdate(bulkUpdateGoalData);
            if (bulkDeleteGoalData.Any())
                _unitOfWork.GenericRepo.BulkDelete(bulkDeleteGoalData);
            await _unitOfWork.CompleteAsync();

            if(descIdsToDelete.Any())
                await _utility.DeleteAzuretableDecription(userdata.tenant_id, descIdsToDelete);
            if (goalDescIdsToInsert.Any())
                await _utility.AddOrUpdateAzureTableDescriptionsAsync(userId, data.targettext, goalDescIdsToInsert, 0, true);
            if (goalFinplanDescIdsToInsert.Any())
                await _utility.AddOrUpdateAzureTableDescriptionsAsync(userId, data.focustext, goalFinplanDescIdsToInsert, 0, true);
        }


        private async Task DeleteConnectedTarget(string orgId, int? orgLevel, string serviceId, List<tco_targets> targetGoalData, List<tco_targets_distribution> targetDistrGoalData, List<tfp_effect_target_detail> allTargetDetailDistrData)
        {
            TenantDBContext DBContext = await _utility.GetTenantDBContextAsync();
            List<tco_targets> bulkDeleteTarget = new List<tco_targets>();
            List<tco_targets_distribution> bulkDeleteTargetDist = new List<tco_targets_distribution>();
            foreach (var target in targetGoalData)
            {
                var delegatedTarget = !string.IsNullOrEmpty(serviceId?.Trim()) ? targetDistrGoalData.FirstOrDefault(x => x.fk_target_id == target.pk_target_id && x.org_id == orgId && x.org_level == orgLevel && x.service_id == serviceId) :
                    targetDistrGoalData.FirstOrDefault(x => x.fk_target_id == target.pk_target_id && x.org_id == orgId && x.org_level == orgLevel);

                if (delegatedTarget != null)
                {
                    List<tfp_effect_target_detail> connIndic = allTargetDetailDistrData.Where(x => x.fk_target_distribution_id == delegatedTarget.pk_target_distribution_id).ToList();

                    if (connIndic.Any())
                    {
                        allTargetDetailDistrData.RemoveAll(x => connIndic.Contains(x));
                        _unitOfWork.GenericRepo.BulkDelete(connIndic);
                    }
                        

                    if (target != null && target.pk_target_id == delegatedTarget.pk_target_distribution_id)
                        bulkDeleteTarget.Add(target);

                    targetDistrGoalData.Remove(delegatedTarget);
                    bulkDeleteTargetDist.Add(delegatedTarget);
                }

            }
            if (bulkDeleteTarget.Any())
                _unitOfWork.GenericRepo.BulkDelete(bulkDeleteTarget);
            if (bulkDeleteTargetDist.Any())
                _unitOfWork.GenericRepo.BulkDelete(bulkDeleteTargetDist);
            await DBContext.SaveChangesAsync();
        }


        private async Task UpdateGoalStrategyLinkDelegation(int pkId, int tenantId, Guid goalId, List<int> strategyIds, string orgId, int orgLevel, string serviceId, int serviceLevel, List<tfp_strategy_text> strategyDetailData, List<tfp_strategy_goal> strategyGoalDetailData)
        {
            List<tfp_strategy_goal> bulkInsertStrategyConnectionData = new List<tfp_strategy_goal>();
            foreach (var item in strategyIds)
            {
                var strategyDatabyId = strategyDetailData.FirstOrDefault(x => x.pk_strategy_id == item);
                var masterStrategyId = strategyDatabyId.master_strategy_id ?? strategyDatabyId.pk_strategy_id;
                var strategyData = strategyDetailData.FirstOrDefault(x => x.pk_strategy_id == masterStrategyId || x.master_strategy_id == masterStrategyId
                && x.org_id == orgId && x.org_level == orgLevel && x.service_id == serviceId && x.service_level == serviceLevel);

                if (strategyData != null)
                {
                    var strategyGoalData = strategyGoalDetailData.FirstOrDefault(x => x.fk_strategy_id == strategyData.pk_strategy_id);
                    if (strategyGoalData == null)
                    {
                        tfp_strategy_goal newData = new tfp_strategy_goal
                        {
                            fk_goal_id = goalId,
                            fk_strategy_id = strategyData.pk_strategy_id,
                            fk_tenant_id = tenantId,
                            updated = DateTime.UtcNow,
                            updated_by = pkId
                        };
                        bulkInsertStrategyConnectionData.Add(newData);
                    }
                }
            }
            if (bulkInsertStrategyConnectionData.Any())
            {
                _unitOfWork.GenericRepo.BulkInsert(bulkInsertStrategyConnectionData);
            }
            await _unitOfWork.CompleteAsync();
        }

        public async Task DeleteBusPlanGtiLowerHierAsync(IndicatorTargetDelegationHelper indiTargetHelper)
        {
            if (indiTargetHelper != null)
            {
                List<BusPlanOrgIdLevel> hierData = (await _utility.GetOrgStructureforBusinessPlanAsync(indiTargetHelper.OrgVersionContent, indiTargetHelper.UserId, indiTargetHelper.OrgId, indiTargetHelper.OrgLevel)).ToObject<List<BusPlanOrgIdLevel>>();
                if (hierData.Any())
                {
                    foreach (BusPlanOrgIdLevel bpol in hierData[0].items)
                        await DeleteBusPlanGtiTreeItem(indiTargetHelper.TenantId, indiTargetHelper.GtiType, bpol, indiTargetHelper.IndicatorCode, indiTargetHelper.BudgetYear, null, null, null, indiTargetHelper.TargetDistData, indiTargetHelper.AllTargetDetailDistrData);
                }
                await _unitOfWork.CompleteAsync();
            }                
        }

        public async Task DeleteDelegatedGoalsAsync(string userId, int tenantId, string gtiType, List<tco_goals_distribution> goalDistData, List<tco_targets> targetGoalData, List<tco_targets_distribution> targetDistrGoalData, List<tfp_effect_target_detail> allTargetDetailDistrData, int budgetYear, string orgId, int orgLevel, Guid indicatorCode, List<OrgTreeItemGoalTgt> fpTreeorgItems)
        {
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));
            List<BusPlanOrgIdLevel> hierData = (await _utility.GetOrgStructureforBusinessPlanAsync(orgVersionContent, userId, orgId, orgLevel)).ToObject<List<BusPlanOrgIdLevel>>();
            if (hierData.Count > 0)
            {
                foreach (BusPlanOrgIdLevel bpol in hierData[0].items)
                    await DeleteBusPlanGtiTreeItem(tenantId, gtiType, bpol, indicatorCode, budgetYear, fpTreeorgItems, goalDistData, targetGoalData, targetDistrGoalData, allTargetDetailDistrData);
            }
            await _unitOfWork.CompleteAsync();
        }
        private async Task DeleteBusPlanGtiTreeItem(int tenantId, string gtiType, BusPlanOrgIdLevel item, Guid indicatorCode, int budgetYear, List<OrgTreeItemGoalTgt> fpTreeorgItems = null, List<tco_goals_distribution> goalDistData = null, List<tco_targets> targetGoalData = null, List<tco_targets_distribution> targetDistrGoalData = null, List<tfp_effect_target_detail> allTargetDetailDistrData = null)
        {
            if (gtiType == "goal")
            {
                tco_goals_distribution tcgd = goalDistData.FirstOrDefault(x => x.org_id == item.id && x.org_level == item.orgLevel);
                if (tcgd != null)
                {
                    var isChecked = fpTreeorgItems.FirstOrDefault(x => x.id == tcgd.org_id) is null ? true : fpTreeorgItems.FirstOrDefault(x => x.id == tcgd.org_id).@checked;
                    if (!isChecked)
                    {

                        List<string> descIdsToDelete = new List<string>();
                        descIdsToDelete.Add(tcgd.focus_blob_id.ToString());
                        descIdsToDelete.Add(tcgd.target_blob_id.ToString());
                        if (descIdsToDelete.Any())
                            await _utility.DeleteAzuretableDecription(tenantId, descIdsToDelete);

                        await DeleteConnectedTarget(tcgd.org_id, tcgd.org_level, tcgd.service_id, targetGoalData, targetDistrGoalData, allTargetDetailDistrData);
                        _unitOfWork.GenericRepo.Delete(tcgd);
                        await _unitOfWork.CompleteAsync();
                    }
                }
            }
            else if (gtiType == "target")
            {
                tco_targets_distribution tcgd = targetDistrGoalData.FirstOrDefault(x => x.org_id == item.id && x.org_level == item.orgLevel);
                if (tcgd != null)
                    _unitOfWork.GenericRepo.Delete(tcgd);
                await _unitOfWork.CompleteAsync();
            }
            else if (gtiType == "indicator")
            {
                tco_targets_distribution targetOrgData = targetDistrGoalData.FirstOrDefault(x => x.org_id == item.id && x.org_level == item.orgLevel);
                if (targetOrgData != null)
                {
                    List<tfp_effect_target_detail> tetdList = allTargetDetailDistrData.Where(x => x.fk_target_distribution_id == targetOrgData.pk_target_distribution_id && x.fk_indicator_code == indicatorCode).ToList();
                    _unitOfWork.GenericRepo.BulkDelete(tetdList);
                    await _unitOfWork.CompleteAsync();
                }
            }
            
            foreach (BusPlanOrgIdLevel bpol in item.items)
                await DeleteBusPlanGtiTreeItem(tenantId, gtiType, bpol, indicatorCode, budgetYear, fpTreeorgItems, goalDistData, targetGoalData, targetDistrGoalData, allTargetDetailDistrData);
        }

        public Guid AddTargetDistriTcox(string userId, int budgetYear, Guid targetId, bool isRootTarget, TargetData data, bool isBusPlanTgt, string orgId, int orgLevel, string serviceId, int serviceLevel, bool procesedTags)
        {
            return AddTargetDistriTcoxAsync(userId, budgetYear, targetId, isRootTarget, data, isBusPlanTgt, orgId, orgLevel, serviceId, serviceLevel, procesedTags, "").GetAwaiter().GetResult();
        }

        public async Task<Guid> AddTargetDistriTcoxAsync(string userId, int budgetYear, Guid targetId, bool isRootTarget, TargetData data, bool isBusPlanTgt, string orgId, int orgLevel, string serviceId, int serviceLevel, bool procesedTags, string attributeId)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            bool isChapterSetup = await _utility.IsTenantChapterSetup(userDetails.tenant_id);
            Guid tgtDistrId = Guid.Empty;
            List<string> serviceIdAll = new List<string>() { "ALL", "-1" };
            tco_targets_distribution ttd;
            if (isChapterSetup)
            {
                ttd = await dbContext.tco_targets_distribution.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id
                                                                     && x.fk_target_id == targetId
                                                                     && x.org_id == orgId
                                                                     && x.org_level == orgLevel
                                                                     && x.fk_attribute_id == attributeId);
            }
            else
            {
                if (serviceIdAll.Contains(serviceId.ToUpper()))
                {
                    ttd = await dbContext.tco_targets_distribution.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id
                                                                     && x.fk_target_id == targetId
                                                                     && x.org_id == orgId
                                                                     && x.org_level == orgLevel
                                                                     && serviceIdAll.Contains(x.service_id.ToUpper()));
                }
                else
                {
                    ttd = string.IsNullOrEmpty(serviceId) ? await dbContext.tco_targets_distribution.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                                       && x.fk_target_id == targetId
                                                                                                       && x.org_id == orgId
                                                                                                       && x.org_level == orgLevel) :
                                                                                                await dbContext.tco_targets_distribution.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                                       && x.fk_target_id == targetId
                                                                                                       && x.org_id == orgId
                                                                                                       && x.org_level == orgLevel
                                                                                                       && x.service_id == serviceId);
                }
            }

            if (ttd != null)
            {
                tgtDistrId = ttd.pk_target_distribution_id;

                //insert new tags and get the list to tag ids
                List<string> lstTags = null;
                if (data.tagsData != null && data.tagsData.Any())
                {
                    lstTags = await _consAdjBudget.InsertActionTagsAsync(userId, data.tagsData);
                }
                ttd.tags = lstTags == null ? string.Empty : String.Join(",", lstTags);
                ttd.plan_target_text = string.IsNullOrEmpty(data.plantargettext) ? string.Empty : data.plantargettext;
                if (data.logHistory && !string.IsNullOrEmpty(data.plantargettext))
                {
                    await _utility.SaveTextLogAsync(userId, ttd.pk_target_distribution_id, data.plantargettext, null, "");
                }
                ttd.updated = DateTime.UtcNow;
                ttd.updated_by = userDetails.pk_id;
            }
            else
            {
                tco_targets_distribution newTtd = new tco_targets_distribution();
                if (isRootTarget)
                    newTtd.pk_target_distribution_id = targetId;
                else
                    newTtd.pk_target_distribution_id = Guid.NewGuid();
                newTtd.fk_tenant_id = userDetails.tenant_id;
                newTtd.fk_target_id = targetId;
                newTtd.org_id = orgId;
                newTtd.org_level = orgLevel;
                newTtd.service_id = serviceId;
                newTtd.service_level = serviceLevel;
                newTtd.is_busplan_deltar = isBusPlanTgt;
                newTtd.fk_attribute_id = attributeId;
                //insert new tags and get the list to tag ids
                List<string> lstTags = null;
                if (data.tagsData != null && data.tagsData.Any())
                {
                    lstTags = await _consAdjBudget.InsertActionTagsAsync(userId, data.tagsData);
                }
                newTtd.tags = lstTags == null ? string.Empty : String.Join(",", lstTags);
                newTtd.plan_target_text = string.IsNullOrEmpty(data.plantargettext) ? string.Empty : data.plantargettext;
                if (data.logHistory && !string.IsNullOrEmpty(data.plantargettext))
                {
                    await _utility.SaveTextLogAsync(userId, newTtd.pk_target_distribution_id, data.plantargettext, null, "");
                }
                newTtd.updated = DateTime.UtcNow;
                newTtd.updated_by = userDetails.pk_id;
                dbContext.tco_targets_distribution.Add(newTtd);

                tgtDistrId = newTtd.pk_target_distribution_id;
            }
            await dbContext.SaveChangesAsync();
            return tgtDistrId;
        }

        public List<GoalnDistrFth> FetchGoalDistr(string userId, int budgetYear, string serviceId, List<string> chapterIds = null, bool isMRSyncExport = false, bool isBmSyncExport = false)
        {
            return FetchGoalDistrAsync(userId, budgetYear, serviceId, chapterIds, isMRSyncExport, isBmSyncExport).GetAwaiter().GetResult();
        }

        public async Task<List<GoalnDistrFth>> FetchGoalDistrAsync(string userId, int budgetYear, string serviceId, List<string> chapterIds = null, bool isMRSyncExport = false, bool isBmSyncExport = false)
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendLine($"busplan get targetgoal @ {DateTime.UtcNow}");
            TenantDBContext dbContext = await _utility.GetTenantDbContextForParallelReadAsync();
            TenantDBContext dbContext1 = await _utility.GetTenantDbContextForParallelReadAsync();
            TenantDBContext dbContext2 = await _utility.GetTenantDbContextForParallelReadAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            int budgetYearStart = (budgetYear * 100) + 1;
            int budgetYearEnd = (budgetYear * 100) + 12;
            bool isSyncTenant = await _dataSyncUtility.IsTenantSyncSetup(userDetails.tenant_id, userDetails.client_id);
            List<GoalnDistrFth> goalDistrList;        
            sb.AppendLine($"2 ->" + DateTime.UtcNow);

            var goalDistrListAsync = (from gd in dbContext.tco_goals_distribution
                                      join tg in dbContext.tco_goals on new { tnt = gd.fk_tenant_id, gl = gd.fk_goal_id }
                                                                 equals new { tnt = tg.fk_tenant_id, gl = tg.pk_goal_id }
                                      where tg.fk_tenant_id == userDetails.tenant_id
                                         && tg.budget_year == budgetYear
                                      select new GoalnDistrFth
                                      {
                                          goalDistrId = gd.pk_goal_distribution_id,
                                          goalId = tg.pk_goal_id,
                                          goal_name = tg.goal_name,
                                          org_created = tg.org_created,
                                          org_level_created = tg.org_level_created ?? 0,
                                          focus_area = tg.focus_area,
                                          unsd_goals = tg.unsd_goals,
                                          is_busplan_goal = tg.is_busplan_goal,
                                          is_busplan_delgoa = gd.is_busplan_delgoa,
                                          org_id = gd.org_id,
                                          org_level = gd.org_level ?? 0,
                                          service_id = gd.service_id.Trim().ToUpper() == "ALL" || gd.service_id.Trim() == "" ? "-1" : gd.service_id.Trim(),
                                          service_level = gd.service_level ?? 0,
                                          target_blob_id = gd.target_blob_id,
                                          focus_blob_id = gd.focus_blob_id,
                                          delgo_blob_id = gd.delgo_blob_id ?? Guid.Empty,
                                          tags = gd.tags,
                                          tenantId = tg.fk_tenant_id,
                                          attribute_id = gd.fk_attribute_id,
                                          syncStatus = tg.sync_status
                                      }).ToListAsync();

            var tmrCityGoalsDataTask = dbContext1.tmr_city_goals_status.Where(x => x.FkTenantId == userDetails.tenant_id && x.ForecastPeriod >= budgetYearStart && x.ForecastPeriod <= budgetYearEnd).ToListAsync();
            var statusDataTask = dbContext2.tco_progress_status.Where(x => x.fk_tenant_id == userDetails.tenant_id).ToListAsync();

            await Task.WhenAll(tmrCityGoalsDataTask, statusDataTask, goalDistrListAsync);

            List<TmrCityGoalsStatus> tmrCityGoalsData = tmrCityGoalsDataTask.Result;
            List<tco_progress_status> statusData = statusDataTask.Result;
            goalDistrList = goalDistrListAsync.Result;
            sb.AppendLine($"3 ->" + DateTime.UtcNow);
            var mrStatusData = statusData.Where(x => x.type == "MONTHREP_GOAL" && x.active == 1).ToList();
            var syncStatusValue = await _dataSyncUtility.GetSyncStatusValues(SyncObjectStatusType.SYNC_OBJECT_STATUS, 1, userDetails.tenant_id, false);

            if (chapterIds != null && chapterIds.Any())
            {
                goalDistrList = goalDistrList.Where(x => chapterIds.Contains(x.attribute_id)).ToList();
            }

            if (isMRSyncExport && isSyncTenant && statusData.FirstOrDefault(x => x.type == syncStatus.ToString()) != null)
            {
                goalDistrList = goalDistrList.Where(x => syncStatusValue.Contains(x.syncStatus)).ToList();
            }

            if (isBmSyncExport && isSyncTenant && statusData.FirstOrDefault(x => x.type == syncStatus.ToString()) != null)
            {
                goalDistrList = goalDistrList.Where(x => x.syncStatus == (int)SyncObejctStatus.New || x.syncStatus == (int)SyncObejctStatus.Edited || x.syncStatus == (int)SyncObejctStatus.Default).ToList();
            }
            sb.AppendLine($"4 ->" + DateTime.UtcNow);            
            await InsertPerformanceLog(userId, sb.ToString(), "bp goalfetch");
            return goalDistrList;
        }

        public async Task InsertPerformanceLog(string userId, string content, string logType)
        {
            UserData UserDetails = await _utility.GetUserDetailsAsync(userId);
            string path = $"tenant{UserDetails.tenant_id.ToString()}/{logType}/performancelog.json";
            using (MemoryStream mStream = new MemoryStream(Encoding.UTF8.GetBytes(content)))
            {
                _azureBlobHelper.UploadFromStream(StorageAccount.AppStorage, BlobContainers.BusPlanPerformanceLogs, path, mStream);
            }
        }

        public TmrCityGoalsStatus GetGoalStatusData(List<TmrCityGoalsStatus> tmrCityGoalsData, Guid goalId, Guid goalDistrId)
        {
            tmrCityGoalsData = tmrCityGoalsData.Any() ? tmrCityGoalsData.Where(x => x.FkGoalId == goalId && x.fk_goal_distribution_id == goalDistrId).ToList() : new List<TmrCityGoalsStatus>();
            if (!tmrCityGoalsData.Any())
            {
                return new TmrCityGoalsStatus();
                        
            }
            var maxForecast = tmrCityGoalsData.Max(y => y.ForecastPeriod);
            return tmrCityGoalsData.FirstOrDefault(x => x.ForecastPeriod == maxForecast) ?? new TmrCityGoalsStatus();
        }

        public TmrEffectTargetStatus GetTargetStatusData(List<TmrEffectTargetStatus> tmrTargetData, Guid targetId, Guid targetDistrId)
        {
            tmrTargetData = tmrTargetData.Where(x => x.FkTargetId == targetId && x.fk_target_distribution_id == targetDistrId).ToList();
            var maxForecast = tmrTargetData.Any() ? tmrTargetData.Max(y => y.ForecastPeriod) : 0;
            var result = tmrTargetData.Any() ? tmrTargetData.FirstOrDefault(x => x.ForecastPeriod == maxForecast) ?? new TmrEffectTargetStatus() : new TmrEffectTargetStatus();
            return result;
        }

        public async Task<List<GoalnDistrFth>> FetchGoalDistrDetails(string userId, int budgetYear, string serviceId)
        {
            TenantDBContext dbContext = await _utility.GetTenantDbContextForParallelReadAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            List<GoalnDistrFth> goalDistrList;

            if (string.IsNullOrEmpty(serviceId))
            {
                goalDistrList = await (from gd in dbContext.tco_goals_distribution
                                       join tg in dbContext.tco_goals on new { tnt = gd.fk_tenant_id, gl = gd.fk_goal_id }
                                                                  equals new { tnt = tg.fk_tenant_id, gl = tg.pk_goal_id }
                                       where tg.budget_year == budgetYear
                                          && tg.fk_tenant_id == userDetails.tenant_id
                                       select new GoalnDistrFth
                                       {
                                           goalDistrId = gd.pk_goal_distribution_id,
                                           goalId = tg.pk_goal_id,
                                           goal_name = tg.goal_name,
                                           org_created = tg.org_created,
                                           org_level_created = tg.org_level_created ?? 0,
                                           focus_area = tg.focus_area,
                                           unsd_goals = tg.unsd_goals,
                                           is_busplan_goal = tg.is_busplan_goal,
                                           is_busplan_delgoa = gd.is_busplan_delgoa,
                                           org_id = gd.org_id,
                                           org_level = gd.org_level ?? 0,
                                           service_id = gd.service_id.Trim().ToUpper() == "ALL" || gd.service_id.Trim() == "" ? "-1" : gd.service_id.Trim(),
                                           service_level = gd.service_level ?? 0,
                                           target_blob_id = gd.target_blob_id,
                                           focus_blob_id = gd.focus_blob_id,
                                           delgo_blob_id = gd.delgo_blob_id ?? Guid.Empty,
                                           tags = gd.tags,
                                           tenantId = tg.fk_tenant_id
                                       }).ToListAsync();
            }
            else
            {
                goalDistrList = await (from gd in dbContext.tco_goals_distribution
                                       join tg in dbContext.tco_goals on new { tnt = gd.fk_tenant_id, gl = gd.fk_goal_id }
                                                                  equals new { tnt = tg.fk_tenant_id, gl = tg.pk_goal_id }
                                       where tg.budget_year == budgetYear
                                          && tg.fk_tenant_id == userDetails.tenant_id
                                          && gd.service_id == serviceId
                                       select new GoalnDistrFth
                                       {
                                           goalDistrId = gd.pk_goal_distribution_id,
                                           goalId = tg.pk_goal_id,
                                           goal_name = tg.goal_name,
                                           org_created = tg.org_created,
                                           org_level_created = tg.org_level_created ?? 0,
                                           focus_area = tg.focus_area,
                                           unsd_goals = tg.unsd_goals,
                                           is_busplan_goal = tg.is_busplan_goal,
                                           is_busplan_delgoa = gd.is_busplan_delgoa,
                                           org_id = gd.org_id,
                                           org_level = gd.org_level ?? 0,
                                           service_id = gd.service_id.Trim().ToUpper() == "ALL" || gd.service_id.Trim() == "" ? "-1" : gd.service_id.Trim(),
                                           service_level = gd.service_level ?? 0,
                                           target_blob_id = gd.target_blob_id,
                                           focus_blob_id = gd.focus_blob_id,
                                           delgo_blob_id = gd.delgo_blob_id ?? Guid.Empty,
                                           tags = gd.tags,
                                           tenantId = tg.fk_tenant_id
                                       }).ToListAsync();
            }

            return goalDistrList;
        }

        public async Task<List<TargetnDistrFth>> FetchTargetDistrDetails(string userId, int budgetYear, string serviceId)
        {
            TenantDBContext dbContext = await _utility.GetTenantDbContextForParallelReadAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            List<TargetnDistrFth> targetDistrList;

            if (string.IsNullOrEmpty(serviceId))
            {
                targetDistrList = await (from td in dbContext.tco_targets_distribution
                                         join tt in dbContext.tco_targets on new { tnt = td.fk_tenant_id, tgt = td.fk_target_id }
                                                                      equals new { tnt = tt.fk_tenant_id, tgt = tt.pk_target_id }
                                         where tt.budget_year == budgetYear
                                            && tt.fk_tenant_id == userDetails.tenant_id
                                         select new TargetnDistrFth
                                         {
                                             targetDistrId = td.pk_target_distribution_id,
                                             targetId = tt.pk_target_id,
                                             goalId = tt.fk_goal_id,
                                             target_name = tt.target_name,
                                             unsd_target = tt.unsd_target,
                                             unsd_goal = tt.unsd_goal,
                                             org_created = tt.org_created,
                                             org_level_created = tt.org_level_created ?? 0,
                                             is_busplan_target = tt.is_busplan_target,
                                             org_id = td.org_id,
                                             org_level = td.org_level ?? 0,
                                             service_id = td.service_id.Trim().ToUpper() == "ALL" || td.service_id.Trim() == "" ? "-1" : td.service_id.Trim(),
                                             service_level = td.service_level ?? 0,
                                             tags = td.tags,
                                             plan_target_text = td.plan_target_text,
                                             sort_order = tt.sort_order,
                                             unsdgoal_target = tt.unsd_goal
                                         }).ToListAsync();
            }
            else
            {
                targetDistrList = await (from td in dbContext.tco_targets_distribution
                                         join tt in dbContext.tco_targets on new { tnt = td.fk_tenant_id, tgt = td.fk_target_id }
                                                                      equals new { tnt = tt.fk_tenant_id, tgt = tt.pk_target_id }
                                         where tt.budget_year == budgetYear
                                            && tt.fk_tenant_id == userDetails.tenant_id
                                            && td.service_id == serviceId
                                         select new TargetnDistrFth
                                         {
                                             targetDistrId = td.pk_target_distribution_id,
                                             targetId = tt.pk_target_id,
                                             goalId = tt.fk_goal_id,
                                             target_name = tt.target_name,
                                             unsd_target = tt.unsd_target,
                                             unsd_goal = tt.unsd_goal,
                                             org_created = tt.org_created,
                                             org_level_created = tt.org_level_created ?? 0,
                                             is_busplan_target = tt.is_busplan_target,
                                             org_id = td.org_id,
                                             org_level = td.org_level ?? 0,
                                             service_id = td.service_id.Trim().ToUpper() == "ALL" || td.service_id.Trim() == "" ? "-1" : td.service_id.Trim(),
                                             service_level = td.service_level ?? 0,
                                             tags = td.tags,
                                             plan_target_text = td.plan_target_text,
                                             sort_order = tt.sort_order,
                                             unsdgoal_target = tt.unsd_goal
                                         }).ToListAsync();
            }

            return targetDistrList;
        }

        public List<TargetnDistrFth> FetchTargetDistr(string userId, int budgetYear, string serviceId, List<string> chapterIds = null, bool isMRSyncExport = false, bool isBmSyncExport = false)
        {
            return FetchTargetDistrAsync(userId, budgetYear, serviceId, chapterIds, isMRSyncExport, isBmSyncExport).GetAwaiter().GetResult();
        }

        public async Task<List<TargetnDistrFth>> FetchTargetDistrAsync(string userId, int budgetYear, string serviceId, List<string> chapterIds = null, bool isMRSyncExport = false, bool isBmSyncExport = false)
        {
            TenantDBContext dbContext1 = await _utility.GetTenantDbContextForParallelReadAsync();
            TenantDBContext dbContext2 = await _utility.GetTenantDbContextForParallelReadAsync();
            TenantDBContext dbContext = await _utility.GetTenantDbContextForParallelReadAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            int budgetYearStart = (budgetYear * 100) + 1;
            int budgetYearEnd = (budgetYear * 100) + 12;
            bool isSyncTenant = await _dataSyncUtility.IsTenantSyncSetup(userDetails.tenant_id, userDetails.client_id);
            List<TargetnDistrFth> targetDistrList;

            var targetDistrListAsync = (from td in dbContext.tco_targets_distribution
                                        join tt in dbContext.tco_targets on new { tnt = td.fk_tenant_id, tgt = td.fk_target_id }
                                                                     equals new { tnt = tt.fk_tenant_id, tgt = tt.pk_target_id }
                                        where tt.fk_tenant_id == userDetails.tenant_id
                                           && tt.budget_year == budgetYear
                                        select new TargetnDistrFth
                                        {
                                            targetDistrId = td.pk_target_distribution_id,
                                            targetId = tt.pk_target_id,
                                            goalId = tt.fk_goal_id,
                                            target_name = tt.target_name,
                                            unsd_target = tt.unsd_target,
                                            unsd_goal = tt.unsd_goal,
                                            org_created = tt.org_created,
                                            org_level_created = tt.org_level_created ?? 0,
                                            is_busplan_target = tt.is_busplan_target,
                                            org_id = td.org_id,
                                            org_level = td.org_level ?? 0,
                                            service_id = td.service_id.Trim().ToUpper() == "ALL" || td.service_id.Trim() == "" ? "-1" : td.service_id.Trim(),
                                            service_level = td.service_level ?? 0,
                                            tags = td.tags,
                                            plan_target_text = td.plan_target_text,
                                            sort_order = tt.sort_order,
                                            unsdgoal_target = tt.unsd_goal,
                                            attribute_id = td.fk_attribute_id,
                                            syncStatus = tt.sync_status
                                        }).ToListAsync();

            var tmrTargetStatusDataAsync = dbContext1.tmr_effect_target_status.Where(x => x.FkTenantId == userDetails.tenant_id && x.ForecastPeriod >= budgetYearStart && x.ForecastPeriod <= budgetYearEnd).ToListAsync();
            var statusDataAsync = dbContext2.tco_progress_status.Where(x => x.fk_tenant_id == userDetails.tenant_id).ToListAsync();

            await Task.WhenAll(tmrTargetStatusDataAsync, statusDataAsync, targetDistrListAsync);
            List<TmrEffectTargetStatus> tmrTargetStatusData = tmrTargetStatusDataAsync.Result;
            List<tco_progress_status> statusData = statusDataAsync.Result;
            targetDistrList = targetDistrListAsync.Result;

            var mrStatusData = statusData.Where(x => x.type == "MONTHREP_GOAL" && x.active == 1).ToList();
            var syncStatusValue = await _dataSyncUtility.GetSyncStatusValues(SyncObjectStatusType.SYNC_OBJECT_STATUS, 1, userDetails.tenant_id, false);

            if (chapterIds != null && chapterIds.Any())
            {
                targetDistrList = targetDistrList.Where(x => chapterIds.Contains(x.attribute_id)).ToList();
            }
            if (isMRSyncExport && isSyncTenant && statusData.FirstOrDefault(x => x.type == syncStatus.ToString()) != null)
            {
                targetDistrList = targetDistrList.Where(x => syncStatusValue.Contains(x.syncStatus)).ToList();
            }

            if (isBmSyncExport && isSyncTenant && statusData.FirstOrDefault(x => x.type == syncStatus.ToString()) != null)
            {
                targetDistrList = targetDistrList.Where(x => x.syncStatus == (int)SyncObejctStatus.New || x.syncStatus == (int)SyncObejctStatus.Edited || x.syncStatus == (int)SyncObejctStatus.Default).ToList();
            }

            foreach (var target in targetDistrList)
            {
                var targetData = GetTargetStatusData(tmrTargetStatusData, target.targetId, target.targetDistrId);
                target.statusTargetId = targetData.Status != null ? targetData.Status.Value : 0;
                target.statusTarget = targetData.Status != null && targetData.Status != 0 ? mrStatusData.FirstOrDefault(x => x.status_id == targetData.Status.Value).status_description : "";
                target.statusTargetDesc = targetData.target_status_desc ?? "";
            }

            return targetDistrList;
        }

        public List<KeyValueHelper> FetchStrategyDistr(string userName, string orgId, string serviceId, int budgetYear)
        {
            return FetchStrategyDistrAsync(userName, orgId, serviceId, budgetYear).GetAwaiter().GetResult();
        }
        public async Task<List<KeyValueHelper>> FetchStrategyDistrAsync(string userName, string orgId, string serviceId, int budgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userName);
            TenantDBContext dBContext = await _utility.GetTenantDBContextAsync();

            List<KeyValueHelper> goalStrategyData = string.IsNullOrEmpty(serviceId) ?
                                                    await (from tst in dBContext.tfp_strategy_text
                                                           where tst.fk_tenant_id == userDetails.tenant_id
                                                               && tst.budget_year == budgetYear
                                                               && tst.org_id == orgId
                                                           select new KeyValueHelper
                                                           {
                                                               KeyId = tst.pk_strategy_id,
                                                               KeyString = tst.strategy_name,
                                                               ValueString = tst.strategy_desc
                                                           }).ToListAsync() :
                                                    await (from tst in dBContext.tfp_strategy_text
                                                           where tst.fk_tenant_id == userDetails.tenant_id
                                                               && tst.budget_year == budgetYear
                                                               && tst.org_id == orgId
                                                               && tst.service_id == serviceId
                                                           select new KeyValueHelper
                                                           {
                                                               KeyId = tst.pk_strategy_id,
                                                               KeyString = tst.strategy_name,
                                                               ValueString = tst.strategy_desc
                                                           }).ToListAsync();

            return goalStrategyData;
        }

        public List<TargetnDistrFth> FetchTargetDistrByTgtId(string userId, int budgetYear, Guid targetId)
        {
            return FetchTargetDistrByTgtIdAsync(userId, budgetYear, targetId).GetAwaiter().GetResult();
        }

        public async Task<List<TargetnDistrFth>> FetchTargetDistrByTgtIdAsync(string userId, int budgetYear, Guid targetId)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            List<TargetnDistrFth> targetDistrList = await (from td in dbContext.tco_targets_distribution
                                                           join tt in dbContext.tco_targets on new { tnt = td.fk_tenant_id, tgt = td.fk_target_id }
                                                                                        equals new { tnt = tt.fk_tenant_id, tgt = tt.pk_target_id }
                                                           where tt.budget_year == budgetYear
                                                              && tt.fk_tenant_id == userDetails.tenant_id
                                                              && tt.pk_target_id == targetId
                                                           select new TargetnDistrFth
                                                           {
                                                               targetDistrId = td.pk_target_distribution_id,
                                                               targetId = tt.pk_target_id,
                                                               goalId = tt.fk_goal_id,
                                                               target_name = tt.target_name,
                                                               unsd_target = tt.unsd_target,
                                                               org_created = tt.org_created,
                                                               is_busplan_target = tt.is_busplan_target,
                                                               is_busplan_deltar = td.is_busplan_deltar,
                                                               org_id = td.org_id,
                                                               org_level = td.org_level ?? 0,
                                                               service_id = td.service_id,
                                                               service_level = td.service_level ?? 0,
                                                               tags = td.tags,
                                                               plan_target_text = td.plan_target_text
                                                           }).ToListAsync();
            return targetDistrList;
        }

        public List<GoalnDistrFth> ApplyOrgServFltrGoDistr(string userId, List<GoalnDistrFth> glData, string orgId, int orgLevel, string serviceId, int budgetYear)
        {
            var result = ApplyOrgServFltrGoDistrAsync(userId, glData, orgId, orgLevel, serviceId, budgetYear)
                .GetAwaiter().GetResult();
            return result;
        }

        public async Task<List<GoalnDistrFth>> ApplyOrgServFltrGoDistrAsync(string userId, List<GoalnDistrFth> glData, string orgId,
            int orgLevel, string serviceId, int budgetYear)

        {
            List<string> serviceidwithall = new List<string>();
            var cls = await GetOrgIdOrgLevelServiceIdServiceLevelAsync(userId, orgId, orgLevel, serviceId, budgetYear);

            if ((cls.finplanLevel1Type == "orgId" && cls.finplanLevel2Type == "orgId") ||
                (cls.finplanLevel1Type == "orgId" && cls.finplanLevel2Type == "") ||
                (cls.finplanLevel1Type == "orgId" && cls.finplanLevel2Type == "serviceId" && string.IsNullOrEmpty(cls.serviceId)))
            {
                if (cls.finplanLevel2Type == "serviceId" && serviceId != "-2")
                {
                    serviceidwithall.Add("ALL");
                    serviceidwithall.Add("-1");
                    serviceidwithall.Add(string.Empty);

                    glData = glData.Where(x => x.org_id == cls.orgId && x.org_level == cls.orgLevel && serviceidwithall.Contains(x.service_id)).ToList();
                }
                else
                {
                    glData = glData.Where(x => x.org_id == cls.orgId && x.org_level == cls.orgLevel).ToList();
                }
            }
            else if (cls.finplanLevel1Type == "orgId" && cls.finplanLevel2Type == "serviceId" && !string.IsNullOrEmpty(cls.serviceId))
            {
                if (serviceId.ToUpper() != "ALL" && serviceId != "-1" && serviceId != "-2")
                {
                    if (((!string.IsNullOrEmpty(serviceId)) && (!string.IsNullOrWhiteSpace(serviceId))) && ((!string.IsNullOrEmpty(orgId)) && (!string.IsNullOrWhiteSpace(orgId))))
                    {
                        glData = glData.Where(x => x.org_id == cls.orgId && x.org_level == cls.orgLevel && x.service_id == cls.serviceId).ToList();
                    }
                    else if (((!string.IsNullOrEmpty(serviceId)) && (!string.IsNullOrWhiteSpace(serviceId))) && ((string.IsNullOrEmpty(orgId)) && (string.IsNullOrWhiteSpace(orgId))))
                    {
                        if (orgLevel == 0)
                        {
                            string orgLvlFromSetup = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_1");
                            int dOrgLevel = int.Parse(orgLvlFromSetup.Split('_')[2]);
                            glData = glData.Where(x => x.service_id == cls.serviceId && x.org_level == dOrgLevel).ToList();
                        }
                    }
                    else
                    {
                        glData = glData.Where(x => x.org_id == cls.orgId && x.org_level == cls.orgLevel).ToList();
                    }
                }
                else
                {
                    if (serviceId != "-2")
                    {
                        serviceidwithall.Add("ALL");
                        serviceidwithall.Add("-1");
                        serviceidwithall.Add(string.Empty);

                        glData = glData.Where(x => x.org_id == cls.orgId && x.org_level == cls.orgLevel && serviceidwithall.Contains(x.service_id)).ToList();
                    }
                    else
                        glData = glData.Where(x => x.org_id == cls.orgId && x.org_level == cls.orgLevel).ToList();
                }
            }
            return glData;
        }

        public OrgIdOrgLevelServiceIdServiceLevel GetOrgIdOrgLevelServiceIdServiceLevel(string userId, string orgId, int? orgLevel, string serviceId, int budgetYear)
        {
            return GetOrgIdOrgLevelServiceIdServiceLevelAsync(userId, orgId, orgLevel, serviceId, budgetYear).GetAwaiter().GetResult();
        }

        public async Task<OrgIdOrgLevelServiceIdServiceLevel> GetOrgIdOrgLevelServiceIdServiceLevelAsync(string userId, string orgId, int? orgLevel, string serviceId, int budgetYear)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            UserData user = await _utility.GetUserDetailsAsync(userId);
            serviceId = string.IsNullOrEmpty(serviceId) ? string.Empty : serviceId.Trim();

            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));

            OrgIdOrgLevelServiceIdServiceLevel cls = new OrgIdOrgLevelServiceIdServiceLevel()
            {
                orgId = orgId,
                orgLevel = orgLevel == null ? 1 : orgLevel,
                serviceId = (orgLevel == null || orgLevel == 1) ? "" : (string.IsNullOrEmpty(serviceId) || (!string.IsNullOrEmpty(serviceId) && serviceId.ToUpper() == "ALL")) ? "" : serviceId.Trim(),
                serviceLevel = -1,
                finplanLevel1Type = "",
                finplanLevel2Type = ""
            };

            if (orgLevel == null || orgLevel == 1 || orgLevel == 0)
            {
                string paramValueFP1 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_1");
                string paramValueFP2 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_2");
                if (!string.IsNullOrEmpty(paramValueFP1) && !string.IsNullOrEmpty(paramValueFP2) && paramValueFP1.Contains("org_id") && paramValueFP2.Contains("org_id"))
                {
                    cls.finplanLevel1Type = "orgId";
                    cls.finplanLevel2Type = "orgId";
                    // Handle the Doc Export issue #49105
                    if (orgLevel == 0 && !string.IsNullOrEmpty(serviceId))
                    {
                        //the level2 orgId gets passed through serviceId. So override it here.
                        if (serviceId.ToUpper() != "ALL")
                        {
                            orgId = serviceId;
                        }
                    }
                }
                else if (!string.IsNullOrEmpty(paramValueFP1) && !string.IsNullOrEmpty(paramValueFP2) && paramValueFP1.Contains("org_id") && paramValueFP2.Contains("service_id"))
                {
                    cls.finplanLevel1Type = "orgId";
                    cls.finplanLevel2Type = "serviceId";
                }
                else if (!string.IsNullOrEmpty(paramValueFP1) && !string.IsNullOrEmpty(paramValueFP2) && paramValueFP1.Contains("service_id") && paramValueFP2.Contains("org_id"))
                {
                    cls.finplanLevel1Type = "serviceId";
                    cls.finplanLevel2Type = "orgId";
                }
                else if (!string.IsNullOrEmpty(paramValueFP1) && paramValueFP1.Contains("org_id") && string.IsNullOrEmpty(paramValueFP2))
                {
                    cls.finplanLevel1Type = "orgId";
                    cls.finplanLevel2Type = "";
                }
                if (orgLevel != 0)
                {
                    cls.orgId = orgVersionContent.lstOrgHierarchy.Any() ? orgVersionContent.lstOrgHierarchy.FirstOrDefault().org_id_1 : string.Empty;
                    cls.orgLevel = 1;

                    int fpLvl1OrgLevel = -1, fpLvl2OrgLevel = -1, fpLvl1ServiceLevel = -1, fpLvl2ServiceLevel = -1;
                    if (!string.IsNullOrEmpty(paramValueFP1))
                    {
                        if (paramValueFP1.Split('_')[0].ToUpper() == "ORG")
                        {
                            fpLvl1OrgLevel = int.Parse(paramValueFP1.Split('_')[2]);
                        }
                        else if (paramValueFP1.Split('_')[0].ToUpper() == "SERVICE")
                        {
                            if (serviceId.ToUpper() != "ALL")
                                fpLvl1ServiceLevel = int.Parse(paramValueFP1.Split('_')[2]);

                            cls.serviceLevel = fpLvl1ServiceLevel;
                            cls.serviceId = serviceId;
                        }
                    }
                    if (!string.IsNullOrEmpty(paramValueFP2))
                    {
                        if (paramValueFP2.Split('_')[0].ToUpper() == "ORG")
                        {
                            fpLvl2OrgLevel = int.Parse(paramValueFP2.Split('_')[2]);
                        }
                        else if (paramValueFP2.Split('_')[0].ToUpper() == "SERVICE")
                        {
                            if (serviceId.ToUpper() != "ALL")
                                fpLvl2ServiceLevel = int.Parse(paramValueFP2.Split('_')[2]);

                            cls.serviceLevel = fpLvl2ServiceLevel;
                            cls.serviceId = serviceId;
                        }
                    }
                }
                else
                {
                    if (orgId == null || string.IsNullOrEmpty(orgId.Trim()))
                    {
                        cls.orgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault().org_id_1;
                        cls.orgLevel = 1;
                    }
                    else
                    {
                        cls.orgId = orgId;

                        int fpLvl1OrgLevel = -1, fpLvl2OrgLevel = -1, fpLvl1ServiceLevel = -1, fpLvl2ServiceLevel = -1;
                        if (!string.IsNullOrEmpty(paramValueFP1))
                        {
                            if (paramValueFP1.Split('_')[0].ToUpper() == "ORG")
                            {
                                fpLvl1OrgLevel = int.Parse(paramValueFP1.Split('_')[2]);
                            }
                            else if (paramValueFP1.Split('_')[0].ToUpper() == "SERVICE")
                            {
                                if (serviceId.ToUpper() != "ALL")
                                    fpLvl1ServiceLevel = int.Parse(paramValueFP1.Split('_')[2]);
                            }
                        }
                        if (!string.IsNullOrEmpty(paramValueFP2))
                        {
                            if (paramValueFP2.Split('_')[0].ToUpper() == "ORG")
                            {
                                fpLvl2OrgLevel = int.Parse(paramValueFP2.Split('_')[2]);
                            }
                            else if (paramValueFP2.Split('_')[0].ToUpper() == "SERVICE")
                            {
                                if (serviceId.ToUpper() != "ALL")
                                    fpLvl2ServiceLevel = int.Parse(paramValueFP2.Split('_')[2]);
                            }
                        }

                        if (fpLvl1OrgLevel == 2 && orgVersionContent.lstOrgHierarchy.FirstOrDefault(x => x.org_id_2 == orgId) != null)
                        {
                            cls.orgLevel = fpLvl1OrgLevel;
                        }
                        else if (fpLvl1OrgLevel == 3 && orgVersionContent.lstOrgHierarchy.FirstOrDefault(x => x.org_id_3 == orgId) != null)
                        {
                            cls.orgLevel = fpLvl1OrgLevel;
                        }
                        else if (fpLvl2OrgLevel == 2 && orgVersionContent.lstOrgHierarchy.FirstOrDefault(x => x.org_id_2 == orgId) != null)
                        {
                            cls.orgLevel = fpLvl2OrgLevel;
                        }
                        else if (fpLvl2OrgLevel == 3 && orgVersionContent.lstOrgHierarchy.FirstOrDefault(x => x.org_id_3 == orgId) != null)
                        {
                            cls.orgLevel = fpLvl2OrgLevel;
                        }
                    }
                }
            }
            else
            {
                var displayControl = await DisplayFpLevel2Items(userId, orgLevel.Value);
                if (Convert.ToString(displayControl["fpLevel1Type"]) == "orgId" && Convert.ToString(displayControl["fpLevel2Type"]) == "orgId")
                {
                    cls.finplanLevel1Type = "orgId";
                    cls.finplanLevel2Type = "orgId";
                    if (Convert.ToInt32(displayControl["fpLevel1Value"]) == orgLevel.Value)
                    {
                        if (!string.IsNullOrEmpty(serviceId) && serviceId.ToUpper() != "ALL")
                        {
                            cls.orgId = serviceId.Trim();
                            cls.orgLevel = Convert.ToInt32(displayControl["fpLevel2Value"]);
                            cls.serviceId = string.Empty;
                            cls.serviceLevel = -1;
                        }
                        else
                        {
                            cls.serviceId = string.Empty;
                            cls.serviceLevel = -1;
                        }
                    }
                    else if (Convert.ToInt32(displayControl["fpLevel2Value"]) == orgLevel.Value)
                    {
                        cls.serviceId = string.Empty;
                        cls.serviceLevel = -1;
                    }
                }
                else if (Convert.ToString(displayControl["fpLevel1Type"]) == "orgId" && Convert.ToString(displayControl["fpLevel2Type"]) == "serviceId")
                {
                    cls.finplanLevel1Type = "orgId";
                    cls.finplanLevel2Type = "serviceId";
                    if (!string.IsNullOrEmpty(serviceId) && serviceId.ToUpper() != "ALL")
                    {
                        cls.serviceId = serviceId.Trim();
                        cls.serviceLevel = Convert.ToInt32(displayControl["fpLevel2Value"]);
                    }
                    else
                    {
                        cls.serviceId = string.Empty;
                        cls.serviceLevel = Convert.ToInt32(displayControl["fpLevel2Value"]);
                    }
                }
                else if (Convert.ToString(displayControl["fpLevel1Type"]) == "orgId" && Convert.ToString(displayControl["fpLevel2Type"]) == "")
                {
                    cls.finplanLevel1Type = "orgId";
                    cls.finplanLevel2Type = "";
                    cls.serviceId = string.Empty;
                    cls.serviceLevel = -1;
                }
            }

            return cls;
        }

        private async Task<JObject> DisplayFpLevel2Items(string userId, int orgLevel)
        {
            dynamic obj = new JObject();
            obj.displayFinplanLevel2Items = false;
            obj.displayAllBudgetProposalTabs = false;
            obj.displayDocumentGenerationTab = false;
            obj.displayGoalsActivityTab = false;
            obj.displayplanDataProcessingTab = false;
            obj.displayMoveToButtonsInActionPopUp = false;
            obj.fpLevel1Type = "";
            obj.fpLevel1Value = -1;

            obj.fpLevel2Type = "";
            obj.fpLevel2Value = -1;
            IEnumerable<int> userRole = await _utility.GetUserRoleIdsAsync(userId);
            obj.displayGoalStrategyVisionOnly = userRole.Contains(14);
            string paramValueFP1 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_1");
            string paramValueFP2 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_2");

            if (!string.IsNullOrEmpty(paramValueFP1) && !string.IsNullOrEmpty(paramValueFP2) && paramValueFP1.Contains("org_id") && paramValueFP2.Contains("org_id"))
            {
                if ((paramValueFP1.ToLower() == "org_id_1" && orgLevel == 1) ||
                    (paramValueFP1.ToLower() == "org_id_2" && orgLevel == 2) ||
                    (paramValueFP1.ToLower() == "org_id_3" && orgLevel == 3) ||
                    (paramValueFP1.ToLower() == "org_id_4" && orgLevel == 4) ||
                    (paramValueFP1.ToLower() == "org_id_5" && orgLevel == 5))
                {
                    obj.displayFinplanLevel2Items = true;
                }

                if ((paramValueFP1.ToLower() == "org_id_1" && orgLevel == 1) ||
                    (paramValueFP1.ToLower() == "org_id_2" && orgLevel == 2) ||
                    (paramValueFP1.ToLower() == "org_id_3" && orgLevel == 3) ||
                    (paramValueFP1.ToLower() == "org_id_4" && orgLevel == 4) ||
                    (paramValueFP1.ToLower() == "org_id_5" && orgLevel == 5) ||
                    (paramValueFP2.ToLower() == "org_id_1" && orgLevel == 1) ||
                    (paramValueFP2.ToLower() == "org_id_2" && orgLevel == 2) ||
                    (paramValueFP2.ToLower() == "org_id_3" && orgLevel == 3) ||
                    (paramValueFP2.ToLower() == "org_id_4" && orgLevel == 4) ||
                    (paramValueFP2.ToLower() == "org_id_5" && orgLevel == 5))
                {
                    obj.displayAllBudgetProposalTabs = true;
                }

                if ((paramValueFP1.ToLower() == "org_id_1" && orgLevel == 1) ||
                    (paramValueFP1.ToLower() == "org_id_2" && orgLevel == 2) ||
                    (paramValueFP1.ToLower() == "org_id_3" && orgLevel == 3) ||
                    (paramValueFP1.ToLower() == "org_id_4" && orgLevel == 4) ||
                    (paramValueFP1.ToLower() == "org_id_5" && orgLevel == 5))
                {
                    obj.displayDocumentGenerationTab = true;
                }

                obj.fpLevel1Type = "orgId";
                obj.fpLevel2Type = "orgId";

                obj.fpLevel1Value = (paramValueFP1.ToLower() == "org_id_1") ? 1 :
                                    (paramValueFP1.ToLower() == "org_id_2") ? 2 :
                                    (paramValueFP1.ToLower() == "org_id_3") ? 3 :
                                    (paramValueFP1.ToLower() == "org_id_4") ? 4 :
                                    (paramValueFP1.ToLower() == "org_id_5") ? 5 : -1;

                obj.fpLevel2Value = (paramValueFP2.ToLower() == "org_id_1") ? 1 :
                                    (paramValueFP2.ToLower() == "org_id_2") ? 2 :
                                    (paramValueFP2.ToLower() == "org_id_3") ? 3 :
                                    (paramValueFP2.ToLower() == "org_id_4") ? 4 :
                                    (paramValueFP2.ToLower() == "org_id_5") ? 5 : -1;

                if (orgLevel == Convert.ToInt32(obj.fpLevel1Value) || orgLevel == Convert.ToInt32(obj.fpLevel2Value) || orgLevel == 1)
                {
                    obj.displayGoalsActivityTab = true;
                    obj.displayplanDataProcessingTab = true;
                }

                if (orgLevel == Convert.ToInt32(obj.fpLevel1Value) || orgLevel == Convert.ToInt32(obj.fpLevel2Value))
                {
                    obj.displayMoveToButtonsInActionPopUp = true;
                }
            }
            else if (!string.IsNullOrEmpty(paramValueFP1) && !string.IsNullOrEmpty(paramValueFP2) && paramValueFP1.Contains("org_id") && paramValueFP2.Contains("service_id"))
            {
                obj.displayFinplanLevel2Items = true;

                if ((paramValueFP1.ToLower() == "org_id_1" && orgLevel == 1) ||
                    (paramValueFP1.ToLower() == "org_id_2" && orgLevel == 2) ||
                    (paramValueFP1.ToLower() == "org_id_3" && orgLevel == 3) ||
                    (paramValueFP1.ToLower() == "org_id_4" && orgLevel == 4) ||
                    (paramValueFP1.ToLower() == "org_id_5" && orgLevel == 5))
                {
                    obj.displayAllBudgetProposalTabs = true;
                    obj.displayDocumentGenerationTab = true;
                }

                obj.fpLevel1Type = "orgId";
                obj.fpLevel2Type = "serviceId";

                obj.fpLevel1Value = (paramValueFP1.ToLower() == "org_id_1") ? 1 :
                                    (paramValueFP1.ToLower() == "org_id_2") ? 2 :
                                    (paramValueFP1.ToLower() == "org_id_3") ? 3 :
                                    (paramValueFP1.ToLower() == "org_id_4") ? 4 :
                                    (paramValueFP1.ToLower() == "org_id_5") ? 5 : -1;

                obj.fpLevel2Value = (paramValueFP2.ToLower() == "service_id_1") ? 1 :
                                    (paramValueFP2.ToLower() == "service_id_2") ? 2 :
                                    (paramValueFP2.ToLower() == "service_id_3") ? 3 :
                                    (paramValueFP2.ToLower() == "service_id_4") ? 4 :
                                    (paramValueFP2.ToLower() == "service_id_5") ? 5 : -1;

                if (orgLevel == Convert.ToInt32(obj.fpLevel1Value) || orgLevel == 1)
                {
                    obj.displayGoalsActivityTab = true;
                    obj.displayplanDataProcessingTab = true;
                }

                if (orgLevel == Convert.ToInt32(obj.fpLevel1Value))
                {
                    obj.displayMoveToButtonsInActionPopUp = true;
                }
            }
            else if (!string.IsNullOrEmpty(paramValueFP1) && !string.IsNullOrEmpty(paramValueFP2) && paramValueFP1.Contains("service_id") && paramValueFP2.Contains("org_id"))
            {
                obj.displayFinplanLevel2Items = true;

                if ((paramValueFP2.ToLower() == "org_id_1" && orgLevel == 1) ||
                    (paramValueFP2.ToLower() == "org_id_2" && orgLevel == 2) ||
                    (paramValueFP2.ToLower() == "org_id_3" && orgLevel == 3) ||
                    (paramValueFP2.ToLower() == "org_id_4" && orgLevel == 4) ||
                    (paramValueFP2.ToLower() == "org_id_5" && orgLevel == 5))
                {
                    obj.displayAllBudgetProposalTabs = true;
                    obj.displayDocumentGenerationTab = true;
                }

                obj.fpLevel1Type = "serviceId";
                obj.fpLevel2Type = "orgId";

                obj.fpLevel1Value = (paramValueFP2.ToLower() == "service_id_1") ? 1 :
                                    (paramValueFP2.ToLower() == "service_id_2") ? 2 :
                                    (paramValueFP2.ToLower() == "service_id_3") ? 3 :
                                    (paramValueFP2.ToLower() == "service_id_4") ? 4 :
                                    (paramValueFP2.ToLower() == "service_id_5") ? 5 : -1;

                obj.fpLevel2Value = (paramValueFP1.ToLower() == "org_id_1") ? 1 :
                                    (paramValueFP1.ToLower() == "org_id_2") ? 2 :
                                    (paramValueFP1.ToLower() == "org_id_3") ? 3 :
                                    (paramValueFP1.ToLower() == "org_id_4") ? 4 :
                                    (paramValueFP1.ToLower() == "org_id_5") ? 5 : -1;

                if (orgLevel == Convert.ToInt32(obj.fpLevel2Value) || orgLevel == 1)
                {
                    obj.displayGoalsActivityTab = true;
                    obj.displayplanDataProcessingTab = true;
                }

                if (orgLevel == Convert.ToInt32(obj.fpLevel2Value))
                {
                    obj.displayMoveToButtonsInActionPopUp = true;
                }
            }
            else if (!string.IsNullOrEmpty(paramValueFP1) && paramValueFP1.Contains("org_id") && string.IsNullOrEmpty(paramValueFP2))
            {
                if ((paramValueFP1.ToLower() == "org_id_1" && orgLevel == 1) ||
                    (paramValueFP1.ToLower() == "org_id_2" && orgLevel == 2) ||
                    (paramValueFP1.ToLower() == "org_id_3" && orgLevel == 3) ||
                    (paramValueFP1.ToLower() == "org_id_4" && orgLevel == 4) ||
                    (paramValueFP1.ToLower() == "org_id_5" && orgLevel == 5))
                {
                    obj.displayAllBudgetProposalTabs = true;
                    obj.displayDocumentGenerationTab = true;
                }

                obj.fpLevel1Type = "orgId";
                obj.fpLevel2Type = "";

                obj.fpLevel1Value = (paramValueFP1.ToLower() == "org_id_1") ? 1 :
                                    (paramValueFP1.ToLower() == "org_id_2") ? 2 :
                                    (paramValueFP1.ToLower() == "org_id_3") ? 3 :
                                    (paramValueFP1.ToLower() == "org_id_4") ? 4 :
                                    (paramValueFP1.ToLower() == "org_id_5") ? 5 : -1;

                if (orgLevel == Convert.ToInt32(obj.fpLevel1Value) || orgLevel == 1)
                {
                    obj.displayGoalsActivityTab = true;
                    obj.displayplanDataProcessingTab = true;
                }

                if (orgLevel == Convert.ToInt32(obj.fpLevel1Value))
                {
                    obj.displayMoveToButtonsInActionPopUp = true;
                }
            }
            return obj;
        }

        public async Task<dynamic> GetDelegationHierarchyAsync(int budgetYear, string userId, string type, int orgLevel, string orgId, string attributeId, string id = null, Guid? goalId = null, Guid? indiCode = null, Guid? tgtDistrId = null)
        {
            TenantDBContext tenantDBContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            
            bool isChapter = false;
            if (isChapter) //not using old chapter logic as of now.
            {
                if (type == "goals")
                {
                    List<BPDelegationTreeHelper> goalData = new List<BPDelegationTreeHelper>();
                    goalData = await (from p in tenantDBContext.tco_goals
                                      join q in tenantDBContext.tco_goals_distribution on p.pk_goal_id equals q.fk_goal_id
                                      where p.fk_tenant_id == userDetails.tenant_id && p.pk_goal_id == Guid.Parse(id) && !p.is_busplan_goal && !q.is_busplan_delgoa
                                      select new BPDelegationTreeHelper { orgId = q.org_id, attributeId = q.fk_attribute_id }).Distinct().ToListAsync();
                    var tree = _finUtility.GetChapterSetupDelegationTree(userId, budgetYear, orgId, goalData, orgLevel, attributeId).GetAwaiter().GetResult();
                    return tree;
                }
                else if (type == "target")
                {
                    var gId = await tenantDBContext.tco_targets.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear && x.pk_target_id == Guid.Parse(id));
                    var goalData = await (from p in tenantDBContext.tco_goals
                                          join q in tenantDBContext.tco_goals_distribution on p.pk_goal_id equals q.fk_goal_id
                                          where p.fk_tenant_id == userDetails.tenant_id && p.pk_goal_id == gId.fk_goal_id && !p.is_busplan_goal && !q.is_busplan_delgoa
                                          select new BPDelegationTreeHelper { orgId = q.org_id, attributeId = q.fk_attribute_id }).Distinct().ToListAsync();
                    var targetData = await (from p in tenantDBContext.tco_targets_distribution
                                            where p.fk_tenant_id == userDetails.tenant_id && p.fk_target_id == Guid.Parse(id) && p.is_busplan_deltar != true
                                            select new BPDelegationTreeHelper { orgId = p.org_id, attributeId = p.fk_attribute_id }).Distinct().ToListAsync();

                    return await GetChapterSetupDelegationTreeTarget(userId, budgetYear, orgId, targetData, orgLevel, attributeId, goalData);
                }
                else
                {
                    var targetData = await (from p in tenantDBContext.tco_targets_distribution
                                            where p.fk_tenant_id == userDetails.tenant_id && p.fk_target_id == Guid.Parse(id) && p.is_busplan_deltar != true
                                            select new BPDelegationTreeHelper { orgId = p.org_id, attributeId = p.fk_attribute_id }).Distinct().ToListAsync();
                    var data = await (from p in tenantDBContext.tco_targets_distribution
                                      join d in tenantDBContext.tfp_effect_target_detail on new { t = p.fk_target_id, d = p.pk_target_distribution_id }
                                                                                  equals new { t = d.fk_target_id, d = d.fk_target_distribution_id }
                                      where p.fk_tenant_id == userDetails.tenant_id && p.fk_target_id == Guid.Parse(id) && d.fk_indicator_code == indiCode && p.is_busplan_deltar != true && d.is_bplan_indicator != true
                                      select new BPDelegationTreeHelper { orgId = p.org_id, attributeId = p.fk_attribute_id }).Distinct().ToListAsync();
                    return await GetChapterSetupDelegationTreeTarget(userId, budgetYear, orgId, data, orgLevel, attributeId, targetData);
                }
            }
            else
            {
                dynamic hierarchyData = new JArray();
                var fpLevel1 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_1");
                var fpLevel2 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_2");
                bool isServiceSetup = (!string.IsNullOrEmpty(fpLevel2) && (fpLevel2.StartsWith("ser")) || (!string.IsNullOrEmpty(fpLevel1) && (fpLevel1.StartsWith("ser"))));
                bool isLevel2Service = false;
                int fLv2OrgLvl = 0;
                var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));

                if (!string.IsNullOrEmpty(fpLevel2))
                {
                    isLevel2Service = fpLevel2.Substring(0, 3) == "ser";
                    fLv2OrgLvl = int.Parse(fpLevel2.Substring(fpLevel2.Length - 1, 1));
                }

                int fpLvl1OrgLevel = -1, fpLvl2OrgLevel = -1, fpLvl1ServiceLevel = -1, fpLvl2ServiceLevel = -1;
                if (!string.IsNullOrEmpty(fpLevel1))
                {
                    if (fpLevel1.Split('_')[0].ToUpper() == "ORG")
                    {
                        fpLvl1OrgLevel = int.Parse(fpLevel1.Split('_')[2]);
                    }
                    else if (fpLevel1.Split('_')[0].ToUpper() == "SERVICE")
                    {
                        fpLvl1ServiceLevel = int.Parse(fpLevel1.Split('_')[2]);
                    }
                }
                if (!string.IsNullOrEmpty(fpLevel2))
                {
                    if (fpLevel2.Split('_')[0].ToUpper() == "ORG")
                    {
                        fpLvl2OrgLevel = int.Parse(fpLevel2.Split('_')[2]);
                    }
                    else if (fpLevel2.Split('_')[0].ToUpper() == "SERVICE")
                    {
                        fpLvl2ServiceLevel = int.Parse(fpLevel2.Split('_')[2]);
                    }
                }

                int maxFpOrgLevel = fpLvl2OrgLevel > fpLvl1OrgLevel ? fpLvl2OrgLevel : fpLvl1OrgLevel;
                int maxFpSrvLevel = fpLvl2ServiceLevel > fpLvl1ServiceLevel ? fpLvl2ServiceLevel : fpLvl1ServiceLevel;
                bool isFinSetup2 = orgLevel > 1 ? false : (fpLvl1OrgLevel == 3 && fpLvl2OrgLevel == 4) ? true : false;
                if ((!string.IsNullOrEmpty(fpLevel1) && fpLevel1.Substring(0, 3) != "ser") && (string.IsNullOrEmpty(fpLevel2) || (!string.IsNullOrEmpty(fpLevel2))))
                {
                    List<clsOrgIdsAndServiceIds> lstResult = await _consAdjBudget.GetOrgIdsAndServiceIdsAsync(userId, false, budgetYear, true, null, isFinSetup2);
                    var orgIds = lstResult.Where(x => x.parentId == null).Select(y => y.orgId).ToList();
                    int level2 = (!string.IsNullOrEmpty(fpLevel2)) ? int.Parse(fpLevel2.Substring(fpLevel2.Length - 1)) : 0;
                    int level1 = int.Parse(fpLevel1.Substring(fpLevel1.Length - 1));
                    int maxFpLevel = level2 > level1 ? level2 : level1;
                    if (level2 != 0 && orgLevel >= level2 && !string.IsNullOrEmpty(fpLevel2) && fpLevel2.Substring(0, 3) != "ser")
                        return hierarchyData;
                    else if (orgLevel == 1)
                    {
                        List<OrgServiceDataHelper> listOfOrgIdsTargetsConnectedGoal = new();
                        List<OrgServiceDataHelper> listOfOrgIdsIndicatorsConnectedTarget = new();
                        
                        if (type == "goals")
                        {
                            listOfOrgIdsTargetsConnectedGoal = await GetAllTargetsOrgIdsConnectedToGoal(goalId.Value, userDetails.tenant_id, budgetYear, maxFpOrgLevel, maxFpSrvLevel, isLevel2Service);
                        }
                        else if(type == "target")
                        {
                            listOfOrgIdsIndicatorsConnectedTarget = await GetAllIndicatorOrgIdsConnectedToTarget(Guid.Parse(id), userDetails.tenant_id, budgetYear, maxFpOrgLevel, maxFpSrvLevel, isLevel2Service);
                        }
                        var cityLevel = orgVersionContent.lstOrgHierarchy.FirstOrDefault();

                        var itemlvl0 = new JObject();
                        itemlvl0.Add("id", cityLevel.org_id_1);
                        itemlvl0.Add("text", string.Join("-", cityLevel.org_id_1, cityLevel.org_name_1));
                        itemlvl0.Add("expanded", false);
                        itemlvl0.Add("checked", true);
                        itemlvl0.Add("fpLevel", 0);
                        itemlvl0.Add("parentId", null);
                        itemlvl0.Add("uniqId", cityLevel.org_id_1);
                        itemlvl0.Add("disabled", false);
                        itemlvl0.Add("unchecked", orgIds.Any());
                        itemlvl0.Add("isConnectedElement", type == "goals" ? listOfOrgIdsTargetsConnectedGoal.Any() : type == "target" ? listOfOrgIdsIndicatorsConnectedTarget.Any() : false);
                        if (string.IsNullOrEmpty(id))
                        {
                            if (orgIds.Count > 0)
                            {
                                dynamic tenantLevelData = new JArray();
                                var itemlvl0_1 = new JObject();
                                itemlvl0_1.Add("id", cityLevel.org_id_1);
                                itemlvl0_1.Add("text", string.Join("-", cityLevel.org_id_1, cityLevel.org_name_1));
                                itemlvl0_1.Add("expanded", true);
                                itemlvl0_1.Add("checked", cityLevel.org_id_1 == orgId);
                                itemlvl0_1.Add("fpLevel", 0);
                                itemlvl0_1.Add("parentId", null);
                                itemlvl0_1.Add("uniqId", cityLevel.org_id_1 + "_" + cityLevel.org_id_1);
                                itemlvl0_1.Add("disabled", cityLevel.org_id_1 == orgId);
                                itemlvl0_1.Add("isConnectedElement", type == "goals" ? listOfOrgIdsTargetsConnectedGoal.Any(x => x.orgId == cityLevel.org_id_1) : type == "target" ? listOfOrgIdsIndicatorsConnectedTarget.Any(x => x.orgId == cityLevel.org_id_1) : false);

                                tenantLevelData.Add(itemlvl0_1);

                                List<string> goalMappedOrgIds = new List<string>();
                                List<string> filteredList = new List<string>();
                                List<string> targetMappedOrgIds = new List<string>();
                                if (type == "target")
                                {
                                    goalMappedOrgIds = GetOrgIdsConnectedToMasterGoal(goalId.Value, userId, budgetYear, maxFpOrgLevel, maxFpSrvLevel, isLevel2Service);
                                    filteredList = lstResult.Where(x => x.parentId == null).Select(y => y.orgId).Where(x => goalMappedOrgIds.Contains(x)).Distinct().ToList();

                                    if (isFinSetup2)
                                    {
                                        List<string> mappedOrgIds = lstResult.Where(x => goalMappedOrgIds.Contains(x.orgId) && x.parentId != null).Select(y => y.orgId).Distinct().ToList();
                                        filteredList = orgVersionContent.lstOrgHierarchy.Where(x => mappedOrgIds.Contains(x.org_id_3) || mappedOrgIds.Contains(x.org_id_4)).Select(y => y.org_id_2).Distinct().ToList();
                                    }
                                }
                                else if (type == "indicator")
                                {
                                    var tgtId = Guid.Parse(id);
                                    var master_target = (from p in tenantDBContext.tco_targets
                                                         where p.fk_tenant_id == userDetails.tenant_id && p.pk_target_id == tgtId
                                                         select p).FirstOrDefault();
                                    Guid tGoalId = master_target.fk_goal_id;

                                    goalMappedOrgIds = GetOrgIdsConnectedToMasterGoal(tGoalId, userId, budgetYear, maxFpOrgLevel, maxFpSrvLevel, isLevel2Service);
                                    targetMappedOrgIds = GetOrgIdsConnectedToMasterTarget(tgtId, userId, budgetYear, maxFpOrgLevel, maxFpSrvLevel, isLevel2Service);
                                    if (isFinSetup2)
                                    {
                                        List<string> mappedOrgIds = lstResult.Where(x => targetMappedOrgIds.Contains(x.orgId) && goalMappedOrgIds.Contains(x.orgId) && x.parentId != null).Select(y => y.orgId).Distinct().ToList();
                                        filteredList = orgVersionContent.lstOrgHierarchy.Where(x => mappedOrgIds.Contains(x.org_id_3) || mappedOrgIds.Contains(x.org_id_4)).Select(y => y.org_id_2).Distinct().ToList();
                                    }
                                    else
                                    {
                                        filteredList = lstResult.Where(x => x.parentId == null).Select(y => y.orgId).Where(x => targetMappedOrgIds.Contains(x) && goalMappedOrgIds.Contains(x)).Distinct().ToList();
                                    }
                                }
                                else
                                {
                                    filteredList = lstResult.Where(x => x.parentId == null).Select(y => y.orgId).Distinct().ToList();
                                }
                                foreach (var fp1item in filteredList)
                                {
                                    var level1Detail = lstResult.FirstOrDefault(x => x.parentId == null && x.orgId == fp1item);
                                    var itemlvl1 = new JObject();
                                    itemlvl1.Add("id", level1Detail.orgId);
                                    itemlvl1.Add("text", string.Join("_", level1Detail.orgId, level1Detail.orgName));
                                    itemlvl1.Add("expanded", false);
                                    itemlvl1.Add("checked", false);
                                    itemlvl1.Add("fpLevel", 1);
                                    itemlvl1.Add("unchecked", true);
                                    itemlvl1.Add("disabled", level1Detail.orgId == orgId);
                                    itemlvl1.Add("parentId", cityLevel.org_id_1);
                                    itemlvl1.Add("uniqId", cityLevel.org_id_1 + "_" + level1Detail.orgId);

                                    var secondlevelItems = lstResult.Where(x => x.parentId == fp1item && !string.IsNullOrEmpty(x.orgId)).OrderBy(y => y.orgId).ToList();

                                    var connectedElementOrgIds = new List<string>();
                                    if (isServiceSetup)
                                    {
                                        connectedElementOrgIds = type == "goals" ? listOfOrgIdsTargetsConnectedGoal.Where(x => x.Id == fp1item).Select(x => x.orgId).ToList() : type == "target" ?
                                                                                listOfOrgIdsIndicatorsConnectedTarget.Where(x => x.Id == fp1item).Select(x => x.orgId).ToList() : new List<string>();
                                    }
                                    else
                                    {
                                        connectedElementOrgIds = type == "goals" ? listOfOrgIdsTargetsConnectedGoal.Select(x => x.orgId).ToList() : type == "target" ?
                                                                                listOfOrgIdsIndicatorsConnectedTarget.Select(x => x.orgId).ToList() : new List<string>();
                                    }
                                    

                                    itemlvl1.Add("isConnectedElement",  secondlevelItems.Any(x => connectedElementOrgIds.Contains(x.orgId)));
                                    if (secondlevelItems.Count > 0)
                                    {
                                        dynamic secondLevelData = new JArray();

                                        var itemlvl2 = new JObject();
                                        if ((string.IsNullOrEmpty(fpLevel2)) || (!string.IsNullOrEmpty(fpLevel2) && fpLevel2.Substring(0, 3) != "ser"))
                                        {
                                            itemlvl2.Add("id", level1Detail.orgId);
                                            itemlvl2.Add("text", string.Join("-", level1Detail.orgId, level1Detail.orgName));
                                            itemlvl2.Add("expanded", false);
                                            itemlvl2.Add("checked", false);
                                            itemlvl2.Add("fpLevel", 1);
                                            itemlvl2.Add("unchecked", secondlevelItems.Any());// this to avoid deleting of deligated target for two level tree
                                            itemlvl2.Add("disabled", level1Detail.orgId == orgId);
                                            itemlvl2.Add("parentId", cityLevel.org_id_1);
                                            itemlvl2.Add("uniqId", cityLevel.org_id_1 + "_1_" + level1Detail.orgId);
                                            itemlvl2.Add("items", new JArray());
                                            itemlvl2.Add("isConnectedElement", connectedElementOrgIds.Any(x => x == level1Detail.orgId));

                                            secondLevelData.Add(itemlvl2);
                                        }

                                        foreach (var fp2items in secondlevelItems)
                                        {
                                            if ((type == "target" && goalMappedOrgIds.Contains(fp2items.orgId)) ||
                                                (type == "indicator" && targetMappedOrgIds.Contains(fp2items.orgId) && goalMappedOrgIds.Contains(fp2items.orgId)) || (type != "target" && type != "indicator"))
                                            {
                                                itemlvl2 = new JObject();
                                                itemlvl2.Add("id", fp2items.orgId);
                                                itemlvl2.Add("text", string.Join("-", fp2items.orgId, fp2items.orgName));
                                                itemlvl2.Add("expanded", false);
                                                itemlvl2.Add("checked", false);
                                                itemlvl2.Add("fpLevel", 2);
                                                itemlvl2.Add("parentId", fp1item);
                                                itemlvl2.Add("uniqId", fp1item + "_" + fp2items.orgId);
                                                itemlvl2.Add("unchecked", false);
                                                itemlvl2.Add("disabled", fp2items.orgId == orgId);
                                                itemlvl2.Add("isConnectedElement", connectedElementOrgIds.Any(x => x == fp2items.orgId));

                                                if (isFinSetup2)
                                                {
                                                    var thirdlevelItems = lstResult.Where(x => x.parentId == fp2items.orgId && !string.IsNullOrEmpty(x.orgId)).OrderBy(y => y.orgId).ToList();
                                                    if (thirdlevelItems.Count > 0)
                                                    {
                                                        dynamic thirdLevelData = new JArray();
                                                        var itemlvl3 = new JObject();
                                                        itemlvl3.Add("id", fp2items.orgId);
                                                        itemlvl3.Add("text", string.Join("-", fp2items.orgId, fp2items.orgName));
                                                        itemlvl3.Add("expanded", false);
                                                        itemlvl3.Add("checked", false);
                                                        itemlvl3.Add("fpLevel", 2);
                                                        itemlvl3.Add("parentId", level1Detail.orgId);
                                                        itemlvl3.Add("unchecked", false);
                                                        itemlvl3.Add("disabled", fp2items.orgId == orgId);
                                                        itemlvl3.Add("uniqId", level1Detail.orgId + "_2_" + fp2items.orgId);
                                                        itemlvl3.Add("items", new JArray());

                                                        var connectedElementOrgIdsLvl1 = new List<string>();
                                                        if (isServiceSetup)
                                                        {
                                                            connectedElementOrgIdsLvl1 = type == "goals" ? listOfOrgIdsTargetsConnectedGoal.Where(x => x.Id == fp2items.orgId).Select(x => x.orgId).ToList() : type == "target" ?
                                                                                listOfOrgIdsIndicatorsConnectedTarget.Where(x => x.Id == fp2items.orgId).Select(x => x.orgId).ToList() : new List<string>();
                                                        }
                                                        else
                                                        {
                                                            connectedElementOrgIdsLvl1 = type == "goals" ? listOfOrgIdsTargetsConnectedGoal.Select(x => x.orgId).ToList() : type == "target" ?
                                                                                listOfOrgIdsIndicatorsConnectedTarget.Select(x => x.orgId).ToList() : new List<string>();
                                                        }

                                                        itemlvl3.Add("isConnectedElement", thirdlevelItems.Any(x => connectedElementOrgIdsLvl1.Contains(x.orgId)));
                                                        thirdLevelData.Add(itemlvl3);

                                                        foreach (var fp3items in thirdlevelItems)
                                                        {
                                                            if ((type == "target" && goalMappedOrgIds.Contains(fp3items.orgId)) ||
                                                            (type == "indicator" && targetMappedOrgIds.Contains(fp3items.orgId) && goalMappedOrgIds.Contains(fp3items.orgId)) || (type != "target" && type != "indicator"))
                                                            {
                                                                itemlvl3 = new JObject();
                                                                itemlvl3.Add("id", fp3items.orgId);
                                                                itemlvl3.Add("text", string.Join("-", fp3items.orgId, fp3items.orgName));
                                                                itemlvl3.Add("expanded", false);
                                                                itemlvl3.Add("checked", false);
                                                                itemlvl3.Add("fpLevel", 3);
                                                                itemlvl3.Add("parentId", fp2items.orgId);
                                                                itemlvl3.Add("unchecked", false);
                                                                itemlvl3.Add("disabled", fp3items.orgId == orgId);
                                                                itemlvl3.Add("uniqId", fp2items.orgId + "_" + fp3items.orgId);
                                                                itemlvl3.Add("items", new JArray());
                                                                itemlvl3.Add("isConnectedElement", connectedElementOrgIdsLvl1.Any(x => x == fp3items.orgId));
                                                                thirdLevelData.Add(itemlvl3);
                                                            }
                                                        }
                                                        itemlvl2.Add("items", thirdLevelData);
                                                    }
                                                }
                                                else
                                                {
                                                    itemlvl2.Add("items", new JArray());
                                                }
                                                secondLevelData.Add(itemlvl2);
                                            }
                                        }
                                        itemlvl1.Add("items", secondLevelData);
                                    }
                                    else
                                    {
                                        itemlvl1.Add("items", new JArray());
                                    }
                                    if (fpLevel1 == "org_id_1")
                                    {
                                        hierarchyData.Add(itemlvl1);
                                    }
                                    else
                                    {
                                        tenantLevelData.Add(itemlvl1);
                                    }
                                }
                                itemlvl0.Add("items", tenantLevelData);
                            }
                            if (fpLevel1 != "org_id_1")
                            {
                                hierarchyData.Add(itemlvl0);
                            }
                        }
                        else
                        {
                            if (type == "target")
                            {
                                var tgtId = Guid.Parse(id);
                                var master_target_id = (from p in tenantDBContext.tco_targets
                                                        where p.fk_tenant_id == userDetails.tenant_id && p.pk_target_id == tgtId
                                                        select p.pk_target_id).FirstOrDefault();
                                if (master_target_id == null)
                                {
                                    master_target_id = tgtId;
                                }

                                if (master_target_id != null)
                                {
                                    var targetDetails = (from p in tenantDBContext.tco_targets_distribution
                                                         where p.fk_tenant_id == userDetails.tenant_id && p.fk_target_id == master_target_id && p.is_busplan_deltar != true
                                                         select new { p.org_id, p.org_level, p.service_id }).Distinct().ToList();
                                    List<string> filteredList = new List<string>();
                                    List<string> goalMappedOrgIds = new List<string>();
                                    goalMappedOrgIds = GetOrgIdsConnectedToMasterGoal(goalId.Value, userId, budgetYear, maxFpOrgLevel, maxFpSrvLevel, isLevel2Service);
                                    if (type == "target" && !isFinSetup2)
                                    {
                                        filteredList = lstResult.Where(x => x.parentId == null).Select(y => y.orgId).Where(x => goalMappedOrgIds.Contains(x)).Distinct().ToList();
                                    }
                                    else if (type == "target" && isFinSetup2)
                                    {
                                        List<string> mappedOrgIds = lstResult.Where(x => goalMappedOrgIds.Contains(x.orgId) && x.parentId != null).Select(y => y.orgId).Distinct().ToList();
                                        filteredList = orgVersionContent.lstOrgHierarchy.Where(x => mappedOrgIds.Contains(x.org_id_3) || mappedOrgIds.Contains(x.org_id_4)).Select(y => y.org_id_2).Distinct().ToList();
                                    }
                                    if (orgIds.Count > 0)
                                    {
                                        dynamic tenantLevelData = new JArray();
                                        var itemlvl0_1 = new JObject();
                                        itemlvl0_1.Add("id", cityLevel.org_id_1);
                                        itemlvl0_1.Add("text", string.Join("-", cityLevel.org_id_1, cityLevel.org_name_1));
                                        itemlvl0_1.Add("expanded", true);
                                        itemlvl0_1.Add("checked", cityLevel.org_id_1 == orgId);
                                        itemlvl0_1.Add("fpLevel", 0);
                                        itemlvl0_1.Add("parentId", null);
                                        itemlvl0_1.Add("uniqId", cityLevel.org_id_1 + "_" + cityLevel.org_id_1);
                                        itemlvl0_1.Add("disabled", cityLevel.org_id_1 == orgId);
                                        itemlvl0_1.Add("isConnectedElement", listOfOrgIdsIndicatorsConnectedTarget.Any(x => x.orgId == cityLevel.org_id_1));

                                        tenantLevelData.Add(itemlvl0_1);
                                        foreach (var fp1item in filteredList)
                                        {
                                            var secondlevelItems = lstResult.Where(x => x.parentId == fp1item && !string.IsNullOrEmpty(x.orgId) && goalMappedOrgIds.Contains(x.orgId)).OrderBy(y => y.orgId).DistinctBy(x => x.orgId).ToList();

                                            var level1Detail = lstResult.FirstOrDefault(x => x.parentId == null && x.orgId == fp1item);
                                            var itemlvl1 = new JObject();
                                            itemlvl1.Add("id", level1Detail.orgId);
                                            itemlvl1.Add("text", string.Join("-", level1Detail.orgId, level1Detail.orgName));
                                            itemlvl1.Add("expanded", false);
                                            itemlvl1.Add("checked", targetDetails.FirstOrDefault(x => x.org_id == fp1item && (string.IsNullOrEmpty(x.service_id) || x.service_id.ToLower() == "all".ToLower())) == null ? false : true);
                                            itemlvl1.Add("fpLevel", 1);
                                            itemlvl1.Add("unchecked", secondlevelItems.Any());// this to avoid deleting of deligated target for two level tree
                                            itemlvl1.Add("disabled", level1Detail.orgId == orgId);
                                            itemlvl1.Add("parentId", cityLevel.org_id_1);
                                            itemlvl1.Add("uniqId", cityLevel.org_id_1 + "_" + level1Detail.orgId);

                                            var connectedElementOrgIds = isServiceSetup ? listOfOrgIdsIndicatorsConnectedTarget.Where(x => x.Id == fp1item).Select(x => x.orgId).ToList() : listOfOrgIdsIndicatorsConnectedTarget.Select(x => x.orgId).ToList();

                                            itemlvl1.Add("isConnectedElement",secondlevelItems.Any(x => connectedElementOrgIds.Contains(x.orgId)));

                                            if (secondlevelItems.Count > 0)
                                            {
                                                dynamic secondLevelData = new JArray();

                                                var itemlvl2 = new JObject();
                                                itemlvl2.Add("id", level1Detail.orgId);
                                                itemlvl2.Add("text", string.Join("-", level1Detail.orgId, level1Detail.orgName));
                                                itemlvl2.Add("expanded", false);
                                                itemlvl2.Add("checked", fpLevel1 == "org_id_1" ? true : targetDetails.FirstOrDefault(x => x.org_id == fp1item && (string.IsNullOrEmpty(x.service_id) || x.service_id.ToLower() == "all".ToLower())) == null ? false : true);
                                                itemlvl2.Add("fpLevel", 1);
                                                itemlvl2.Add("unchecked", false);
                                                itemlvl2.Add("disabled", level1Detail.orgId == orgId);
                                                itemlvl2.Add("parentId", cityLevel.org_id_1);
                                                itemlvl2.Add("uniqId", cityLevel.org_id_1 + "_1_" + level1Detail.orgId);
                                                itemlvl2.Add("items", new JArray());
                                                itemlvl2.Add("isConnectedElement", connectedElementOrgIds.Any(x => x == level1Detail.orgId));
                                                secondLevelData.Add(itemlvl2);

                                                foreach (var fp2items in secondlevelItems)
                                                {
                                                    if ((type == "target" && goalMappedOrgIds.Contains(fp2items.orgId)) || type != "target" || isFinSetup2)
                                                    {
                                                        itemlvl2 = new JObject();
                                                        itemlvl2.Add("id", fp2items.orgId);
                                                        itemlvl2.Add("text", string.Join("-", fp2items.orgId, fp2items.orgName));
                                                        itemlvl2.Add("expanded", false);
                                                        if (isFinSetup2)
                                                        {
                                                            itemlvl2.Add("checked", targetDetails.FirstOrDefault(x => x.org_id == fp2items.orgId && (string.IsNullOrEmpty(x.service_id) || x.service_id.ToLower() == "all".ToLower())) == null ? false : true);
                                                        }
                                                        else
                                                        {
                                                            if ((!string.IsNullOrEmpty(fpLevel2) && fpLevel2.Substring(0, 3) == "org"))
                                                            {
                                                                itemlvl2.Add("checked", targetDetails.FirstOrDefault(x => x.org_id == fp2items.orgId && x.org_level == fLv2OrgLvl) == null ? false : true);
                                                            }
                                                            else
                                                            {
                                                                itemlvl2.Add("checked", targetDetails.FirstOrDefault(x => x.org_id == fp1item && x.service_id == fp2items.orgId) == null ? false : true);
                                                            }
                                                        }
                                                        itemlvl2.Add("fpLevel", 2);
                                                        itemlvl2.Add("unchecked", false);
                                                        itemlvl2.Add("disabled", fp2items.orgId == orgId);
                                                        itemlvl2.Add("parentId", fp1item);
                                                        itemlvl2.Add("uniqId", fp1item + "_" + fp2items.orgId);
                                                        itemlvl2.Add("isConnectedElement", connectedElementOrgIds.Any(x => x == fp2items.orgId));

                                                        if (isFinSetup2)
                                                        {
                                                            var thirdlevelItems = lstResult.Where(x => x.parentId == fp2items.orgId && !string.IsNullOrEmpty(x.orgId)).OrderBy(y => y.orgId).ToList();
                                                            if (thirdlevelItems.Count > 0)
                                                            {
                                                                dynamic thirdLevelData = new JArray();
                                                                var itemlvl3 = new JObject();
                                                                itemlvl3.Add("id", fp2items.orgId);
                                                                itemlvl3.Add("text", string.Join("-", fp2items.orgId, fp2items.orgName));
                                                                itemlvl3.Add("expanded", false);
                                                                itemlvl3.Add("checked", targetDetails.FirstOrDefault(x => x.org_id == fp2items.orgId && (string.IsNullOrEmpty(x.service_id) || x.service_id.ToLower() == "all".ToLower())) == null ? false : true);
                                                                itemlvl3.Add("fpLevel", 2);
                                                                itemlvl3.Add("parentId", level1Detail.orgId);
                                                                itemlvl3.Add("uniqId", level1Detail.orgId + "_" + fp2items.orgId);
                                                                itemlvl3.Add("unchecked", false);
                                                                itemlvl3.Add("disabled", fp2items.orgId == orgId);
                                                                itemlvl3.Add("items", new JArray());

                                                                var connectedElementOrgIdsLvl1 = isServiceSetup ? listOfOrgIdsIndicatorsConnectedTarget.Where(x => x.Id == fp2items.orgId).Select(x => x.orgId).ToList() : listOfOrgIdsIndicatorsConnectedTarget.Select(x => x.orgId).ToList();
                                                                itemlvl3.Add("isConnectedElement", thirdlevelItems.Any(x => connectedElementOrgIdsLvl1.Contains(x.orgId)));

                                                                thirdLevelData.Add(itemlvl3);

                                                                foreach (var fp3items in thirdlevelItems)
                                                                {
                                                                    if ((type == "target" && goalMappedOrgIds.Contains(fp3items.orgId)) || type != "target" || isFinSetup2)
                                                                    {
                                                                        itemlvl3 = new JObject();
                                                                        itemlvl3.Add("id", fp3items.orgId);
                                                                        itemlvl3.Add("text", string.Join("-", fp3items.orgId, fp3items.orgName));
                                                                        itemlvl3.Add("expanded", false);
                                                                        if ((!string.IsNullOrEmpty(fpLevel2) && fpLevel2.Substring(0, 3) == "org"))
                                                                        {
                                                                            itemlvl3.Add("checked", targetDetails.FirstOrDefault(x => x.org_id == fp3items.orgId && x.org_level == fLv2OrgLvl) == null ? false : true);
                                                                        }
                                                                        else
                                                                        {
                                                                            itemlvl3.Add("checked", targetDetails.FirstOrDefault(x => x.org_id == fp2items.orgId && x.service_id == fp2items.orgId) == null ? false : true);
                                                                        }
                                                                        itemlvl3.Add("fpLevel", 3);
                                                                        itemlvl3.Add("parentId", fp2items.orgId);
                                                                        itemlvl3.Add("uniqId", fp2items.orgId + "_" + fp3items.orgId);
                                                                        itemlvl3.Add("unchecked", false);
                                                                        itemlvl3.Add("disabled", fp3items.orgId == orgId);
                                                                        itemlvl3.Add("items", new JArray());
                                                                        itemlvl3.Add("isConnectedElement", connectedElementOrgIdsLvl1.Any(x => x == fp3items.orgId));
                                                                        thirdLevelData.Add(itemlvl3);
                                                                    }
                                                                }
                                                                itemlvl2.Add("items", thirdLevelData);
                                                            }
                                                        }
                                                        else
                                                        {
                                                            itemlvl2.Add("items", new JArray());
                                                        }
                                                        secondLevelData.Add(itemlvl2);
                                                    }
                                                }
                                                itemlvl1.Add("items", secondLevelData);
                                            }
                                            else
                                            {
                                                itemlvl1.Add("items", new JArray());
                                            }
                                            if (fpLevel1 == "org_id_1")
                                            {
                                                hierarchyData.Add(itemlvl1);
                                            }
                                            else
                                            {
                                                tenantLevelData.Add(itemlvl1);
                                            }
                                        }
                                        itemlvl0.Add("items", tenantLevelData);
                                    }
                                    if (fpLevel1 != "org_id_1")
                                    {
                                        hierarchyData.Add(itemlvl0);
                                    }
                                }
                            }
                            else if (type == "goals")
                            {
                                var master_goal_id = Guid.Parse(id);

                                var goalDetails = (from p in tenantDBContext.tco_goals
                                                   join q in tenantDBContext.tco_goals_distribution on p.pk_goal_id equals q.fk_goal_id
                                                   where p.fk_tenant_id == userDetails.tenant_id && p.pk_goal_id == master_goal_id && !p.is_busplan_goal && !q.is_busplan_delgoa
                                                   select new { q.org_id, q.org_level, q.service_id }).Distinct().ToList();
                                if (orgIds.Count > 0)
                                {
                                    dynamic tenantLevelData = new JArray();
                                    var itemlvl0_1 = new JObject();
                                    itemlvl0_1.Add("id", cityLevel.org_id_1);
                                    itemlvl0_1.Add("text", string.Join("-", cityLevel.org_id_1, cityLevel.org_name_1));
                                    itemlvl0_1.Add("expanded", true);
                                    itemlvl0_1.Add("checked", cityLevel.org_id_1 == orgId);
                                    itemlvl0_1.Add("fpLevel", 0);
                                    itemlvl0_1.Add("parentId", null);
                                    itemlvl0_1.Add("uniqId", cityLevel.org_id_1 + "_" + cityLevel.org_id_1);
                                    itemlvl0_1.Add("disabled", cityLevel.org_id_1 == orgId);
                                    itemlvl0_1.Add("isConnectedElement", listOfOrgIdsTargetsConnectedGoal.Any(x => x.orgId == cityLevel.org_id_1));
                                    tenantLevelData.Add(itemlvl0_1);
                                    foreach (var fp1item in lstResult.Where(x => x.parentId == null).Select(y => y.orgId).Distinct().ToList())
                                    {
                                        var secondlevelItems = lstResult.Where(x => x.parentId == fp1item && !string.IsNullOrEmpty(x.orgId)).OrderBy(y => y.orgId).DistinctBy(x => x.orgId).ToList();

                                        var level1Detail = lstResult.FirstOrDefault(x => x.parentId == null && x.orgId == fp1item);
                                        var itemlvl1 = new JObject();
                                        itemlvl1.Add("id", level1Detail.orgId);
                                        itemlvl1.Add("text", string.Join("-", level1Detail.orgId, level1Detail.orgName));
                                        itemlvl1.Add("expanded", false);
                                        itemlvl1.Add("checked", !(master_goal_id == null || goalDetails.FirstOrDefault(x => x.org_id == fp1item && (string.IsNullOrEmpty(x.service_id) || x.service_id == "ALL")) == null));
                                        itemlvl1.Add("disabled", level1Detail.orgId == orgId);
                                        itemlvl1.Add("fpLevel", 1);
                                        itemlvl1.Add("unchecked", secondlevelItems.Any());// this to avoid deleting of deligated target for two level tree
                                        itemlvl1.Add("parentId", cityLevel.org_id_1);
                                        itemlvl1.Add("uniqId", cityLevel.org_id_1 + "_" + level1Detail.orgId);

                                        var connectedElementOrgIds = isServiceSetup ? listOfOrgIdsTargetsConnectedGoal.Where(x => x.Id == fp1item).Select(x => x.orgId).ToList() : listOfOrgIdsTargetsConnectedGoal.Select(x => x.orgId).ToList();

                                        itemlvl1.Add("isConnectedElement", secondlevelItems.Any(x => connectedElementOrgIds.Contains(x.orgId)));

                                        if (secondlevelItems.Count > 0)
                                        {
                                            dynamic secondLevelData = new JArray();
                                            bool l2check = false;
                                            if (isLevel2Service)
                                            {
                                                l2check = fpLevel1 == "org_id_1" || goalDetails.FirstOrDefault(x =>
                                                        x.org_id == fp1item &&
                                                        (string.IsNullOrEmpty(x.service_id) || x.service_id == "ALL")) != null;
                                            }
                                            else
                                            {                                             
                                                l2check = master_goal_id != null && goalDetails.FirstOrDefault(x =>
                                                        x.org_id == fp1item &&
                                                        (string.IsNullOrEmpty(x.service_id) || x.service_id != "ALL")) != null;
                                            }

                                            var itemlvl2 = new JObject();
                                            itemlvl2.Add("id", level1Detail.orgId);
                                            itemlvl2.Add("text", string.Join("-", level1Detail.orgId, level1Detail.orgName));
                                            itemlvl2.Add("expanded", false);
                                            itemlvl2.Add("checked", l2check);
                                            itemlvl2.Add("fpLevel", 1);
                                            itemlvl2.Add("parentId", cityLevel.org_id_1);
                                            itemlvl2.Add("uniqId", cityLevel.org_id_1 + "_1_" + level1Detail.orgId);
                                            itemlvl2.Add("unchecked", false);
                                            itemlvl2.Add("disabled", level1Detail.orgId == orgId);
                                            itemlvl2.Add("items", new JArray());
                                            itemlvl2.Add("isConnectedElement", connectedElementOrgIds.Any(x => x == level1Detail.orgId));
                                            secondLevelData.Add(itemlvl2);

                                            foreach (var fp2items in secondlevelItems)
                                            {
                                                itemlvl2 = new JObject();
                                                itemlvl2.Add("id", fp2items.orgId);
                                                itemlvl2.Add("text", string.Join("-", fp2items.orgId, fp2items.orgName));
                                                itemlvl2.Add("expanded", false);
                                                if (isFinSetup2)
                                                {
                                                    itemlvl2.Add("checked", !(master_goal_id == null || goalDetails.FirstOrDefault(x => x.org_id == fp2items.orgId && (string.IsNullOrEmpty(x.service_id) || x.service_id == "ALL")) == null));
                                                }
                                                else
                                                {
                                                    if ((!string.IsNullOrEmpty(fpLevel2) && fpLevel2.Substring(0, 3) == "org"))
                                                    {
                                                        itemlvl2.Add("checked", !(master_goal_id == null || goalDetails.FirstOrDefault(x => x.org_id == fp2items.orgId && x.org_level == fLv2OrgLvl && (string.IsNullOrEmpty(x.service_id) || x.service_id == "ALL")) == null));
                                                    }
                                                    else
                                                    {
                                                        itemlvl2.Add("checked", !(master_goal_id == null || goalDetails.FirstOrDefault(x => x.org_id == fp1item && x.service_id == fp2items.orgId) == null));
                                                    }
                                                }
                                                itemlvl2.Add("fpLevel", 2);
                                                itemlvl2.Add("parentId", fp1item);
                                                itemlvl2.Add("uniqId", fp1item + "_" + fp2items.orgId);
                                                itemlvl2.Add("unchecked", false);
                                                itemlvl2.Add("disabled", fp2items.orgId == orgId);
                                                itemlvl2.Add("isConnectedElement", connectedElementOrgIds.Any(x => x == fp2items.orgId));

                                                if (isFinSetup2)
                                                {
                                                    var thirdlevelItems = lstResult.Where(x => x.parentId == fp2items.orgId && !string.IsNullOrEmpty(x.orgId)).OrderBy(y => y.orgId).ToList();
                                                    if (thirdlevelItems.Count > 0)
                                                    {
                                                        dynamic thirdLevelData = new JArray();
                                                        var itemlvl3 = new JObject();
                                                        itemlvl3.Add("id", fp2items.orgId);
                                                        itemlvl3.Add("text", string.Join("-", fp2items.orgId, fp2items.orgName));
                                                        itemlvl3.Add("expanded", false);
                                                        itemlvl3.Add("checked", !(master_goal_id == null || goalDetails.FirstOrDefault(x => x.org_id == fp2items.orgId && (string.IsNullOrEmpty(x.service_id) || x.service_id == "ALL")) == null));
                                                        itemlvl3.Add("fpLevel", 2);
                                                        itemlvl3.Add("parentId", level1Detail.orgId);
                                                        itemlvl3.Add("uniqId", level1Detail.orgId + "_" + fp2items.orgId);
                                                        itemlvl3.Add("unchecked", false);
                                                        itemlvl3.Add("disabled", fp2items.orgId == orgId);
                                                        itemlvl3.Add("items", new JArray());

                                                        var connectedElementOrgIdsLvl1 = isServiceSetup ? listOfOrgIdsTargetsConnectedGoal.Where(x => x.Id == fp2items.orgId).Select(x => x.orgId).ToList() : listOfOrgIdsTargetsConnectedGoal.Select(x => x.orgId).ToList();
                                                        itemlvl3.Add("isConnectedElement", thirdlevelItems.Any(x => connectedElementOrgIdsLvl1.Contains(x.orgId)));

                                                        thirdLevelData.Add(itemlvl3);

                                                        foreach (var fp3items in thirdlevelItems)
                                                        {
                                                            itemlvl3 = new JObject();
                                                            itemlvl3.Add("id", fp3items.orgId);
                                                            itemlvl3.Add("text", string.Join("-", fp3items.orgId, fp3items.orgName));
                                                            itemlvl3.Add("expanded", false);
                                                            if ((!string.IsNullOrEmpty(fpLevel2) && fpLevel2.Substring(0, 3) == "org"))
                                                            {
                                                                itemlvl3.Add("checked", !(master_goal_id == null || goalDetails.FirstOrDefault(x => x.org_id == fp3items.orgId && x.org_level == fLv2OrgLvl && (string.IsNullOrEmpty(x.service_id) || x.service_id == "ALL")) == null));
                                                            }
                                                            else
                                                            {
                                                                itemlvl3.Add("checked", !(master_goal_id == null || goalDetails.FirstOrDefault(x => x.org_id == fp2items.orgId && x.service_id == fp3items.orgId) == null));
                                                            }
                                                            itemlvl3.Add("fpLevel", 3);
                                                            itemlvl3.Add("parentId", fp2items.orgId);
                                                            itemlvl3.Add("uniqId", fp2items.orgId + "_" + fp3items.orgId);
                                                            itemlvl3.Add("unchecked", false);
                                                            itemlvl3.Add("disabled", fp3items.orgId == orgId);
                                                            itemlvl3.Add("items", new JArray());
                                                            itemlvl3.Add("isConnectedElement", type == "goals" ? connectedElementOrgIdsLvl1.Any(x => x == fp3items.orgId) : false);
                                                            thirdLevelData.Add(itemlvl3);
                                                        }
                                                        itemlvl2.Add("items", thirdLevelData);
                                                    }
                                                }
                                                else
                                                {
                                                    itemlvl2.Add("items", new JArray());
                                                }
                                                secondLevelData.Add(itemlvl2);
                                            }
                                            itemlvl1.Add("items", secondLevelData);
                                        }
                                        else
                                        {
                                            itemlvl1.Add("items", new JArray());
                                        }
                                        if (fpLevel1 == "org_id_1")
                                        {
                                            hierarchyData.Add(itemlvl1);
                                        }
                                        else
                                        {
                                            tenantLevelData.Add(itemlvl1);
                                        }
                                    }
                                    itemlvl0.Add("items", tenantLevelData);
                                }
                                if (fpLevel1 != "org_id_1")
                                {
                                    hierarchyData.Add(itemlvl0);
                                }
                            }
                            if (type == "indicator")
                            {
                                var tgtId = Guid.Parse(id);
                                var master_target = (from p in tenantDBContext.tco_targets
                                                     where p.fk_tenant_id == userDetails.tenant_id && p.pk_target_id == tgtId
                                                     select p).FirstOrDefault();
                                Guid master_target_id = master_target.pk_target_id;
                                Guid tGoalId = master_target.fk_goal_id;

                                if (master_target_id == null)
                                {
                                    master_target_id = tgtId;
                                }

                                if (master_target_id != null)
                                {
                                    var targetDetails = (from p in tenantDBContext.tco_targets_distribution
                                                         join d in tenantDBContext.tfp_effect_target_detail on new { a = p.fk_target_id, b = p.pk_target_distribution_id }
                                                                                                        equals new { a = d.fk_target_id, b = d.fk_target_distribution_id }
                                                         where p.fk_tenant_id == userDetails.tenant_id && p.fk_target_id == master_target_id && d.fk_indicator_code == indiCode && p.is_busplan_deltar != true && d.is_bplan_indicator != true
                                                         select new { p.fk_target_id, p.org_id, p.org_level, p.service_id }).Distinct().ToList();

                                    List<string> filteredList = new List<string>();
                                    List<string> goalMappedOrgIds = GetOrgIdsConnectedToMasterGoal(tGoalId, userId, budgetYear, maxFpOrgLevel, maxFpSrvLevel, isLevel2Service);
                                    List<string> targetMappedOrgIds = GetOrgIdsConnectedToMasterTarget(tgtId, userId, budgetYear, maxFpOrgLevel, maxFpSrvLevel, isLevel2Service);
                                    if (isFinSetup2)
                                    {
                                        List<string> mappedOrgIds = lstResult.Where(x => targetMappedOrgIds.Contains(x.orgId) && goalMappedOrgIds.Contains(x.orgId) && x.parentId != null).Select(y => y.orgId).Distinct().ToList();
                                        filteredList = orgVersionContent.lstOrgHierarchy.Where(x => mappedOrgIds.Contains(x.org_id_3) || mappedOrgIds.Contains(x.org_id_4)).Select(y => y.org_id_2).Distinct().ToList();
                                    }
                                    else
                                    {
                                        filteredList = lstResult.Where(x => x.parentId == null).Select(y => y.orgId).Where(x => targetMappedOrgIds.Contains(x) && goalMappedOrgIds.Contains(x)).Distinct().ToList();
                                    }

                                    if (orgIds.Count > 0)
                                    {
                                        dynamic tenantLevelData = new JArray();
                                        var itemlvl0_1 = new JObject();
                                        itemlvl0_1.Add("id", cityLevel.org_id_1);
                                        itemlvl0_1.Add("text", string.Join("-", cityLevel.org_id_1, cityLevel.org_name_1));
                                        itemlvl0_1.Add("expanded", true);
                                        itemlvl0_1.Add("checked", cityLevel.org_id_1 == orgId);
                                        itemlvl0_1.Add("fpLevel", 0);
                                        itemlvl0_1.Add("parentId", null);
                                        itemlvl0_1.Add("uniqId", cityLevel.org_id_1 + "_" + cityLevel.org_id_1);
                                        itemlvl0_1.Add("disabled", cityLevel.org_id_1 == orgId);
                                        itemlvl0_1.Add("unchecked", false);
                                        itemlvl0_1.Add("isConnectedElement", false);
                                        tenantLevelData.Add(itemlvl0_1);
                                        foreach (var fp1item in filteredList)
                                        {
                                            var secondlevelItems = lstResult.Where(x => x.parentId == fp1item && !string.IsNullOrEmpty(x.orgId) && targetMappedOrgIds.Contains(x.orgId) && goalMappedOrgIds.Contains(x.orgId)).OrderBy(y => y.orgId).ToList();

                                            var level1Detail = lstResult.FirstOrDefault(x => x.parentId == null && x.orgId == fp1item);
                                            var itemlvl1 = new JObject();
                                            itemlvl1.Add("id", level1Detail.orgId);
                                            itemlvl1.Add("text", string.Join("-", level1Detail.orgId, level1Detail.orgName));
                                            itemlvl1.Add("expanded", false);
                                            itemlvl1.Add("checked", targetDetails.FirstOrDefault(x => x.org_id == fp1item && (string.IsNullOrEmpty(x.service_id) || x.service_id.ToLower() == "all".ToLower())) == null ? false : true);
                                            itemlvl1.Add("fpLevel", 1);
                                            itemlvl1.Add("unchecked", secondlevelItems.Any());// this to avoid deleting of deligated target for two level tree
                                            itemlvl1.Add("disabled", level1Detail.orgId == orgId);
                                            itemlvl1.Add("parentId", cityLevel.org_id_1);
                                            itemlvl1.Add("uniqId", cityLevel.org_id_1 + "_" + level1Detail.orgId);
                                            itemlvl1.Add("isConnectedElement", false);

                                            if (secondlevelItems.Count > 0)
                                            {
                                                dynamic secondLevelData = new JArray();

                                                var itemlvl2 = new JObject();
                                                itemlvl2.Add("id", level1Detail.orgId);
                                                itemlvl2.Add("text", string.Join("-", level1Detail.orgId, level1Detail.orgName));
                                                itemlvl2.Add("expanded", false);
                                                itemlvl2.Add("checked", fpLevel1 == "org_id_1" ? true : targetDetails.FirstOrDefault(x => x.org_id == fp1item && (string.IsNullOrEmpty(x.service_id) || x.service_id.ToLower() == "all".ToLower())) == null ? false : true);
                                                itemlvl2.Add("fpLevel", 1);
                                                itemlvl2.Add("parentId", cityLevel.org_id_1);
                                                itemlvl2.Add("uniqId", cityLevel.org_id_1 + "_1_" + level1Detail.orgId);
                                                itemlvl2.Add("unchecked", false);
                                                itemlvl2.Add("disabled", level1Detail.orgId == orgId);
                                                itemlvl2.Add("items", new JArray());
                                                itemlvl2.Add("isConnectedElement", false);
                                                secondLevelData.Add(itemlvl2);

                                                foreach (var fp2items in secondlevelItems)
                                                {
                                                    if ((targetMappedOrgIds.Contains(fp2items.orgId) && goalMappedOrgIds.Contains(fp2items.orgId)) || isFinSetup2)
                                                    {
                                                        itemlvl2 = new JObject();
                                                        itemlvl2.Add("id", fp2items.orgId);
                                                        itemlvl2.Add("text", string.Join("-", fp2items.orgId, fp2items.orgName));
                                                        itemlvl2.Add("expanded", false);
                                                        if (isFinSetup2)
                                                        {
                                                            itemlvl2.Add("checked", targetDetails.FirstOrDefault(x => x.org_id == fp2items.orgId && (string.IsNullOrEmpty(x.service_id) || x.service_id.ToLower() == "all".ToLower())) == null ? false : true);
                                                        }
                                                        else
                                                        {
                                                            if ((!string.IsNullOrEmpty(fpLevel2) && fpLevel2.Substring(0, 3) == "org"))
                                                            {
                                                                itemlvl2.Add("checked", targetDetails.FirstOrDefault(x => x.org_id == fp2items.orgId && x.org_level == fLv2OrgLvl) == null ? false : true);
                                                            }
                                                            else
                                                            {
                                                                itemlvl2.Add("checked", targetDetails.FirstOrDefault(x => x.org_id == fp1item && x.service_id == fp2items.orgId) == null ? false : true);
                                                            }
                                                        }
                                                        itemlvl2.Add("fpLevel", 2);
                                                        itemlvl2.Add("unchecked", false);
                                                        itemlvl2.Add("disabled", fp2items.orgId == orgId);
                                                        itemlvl2.Add("parentId", fp1item);
                                                        itemlvl2.Add("uniqId", fp1item + "_" + fp2items.orgId);
                                                        itemlvl2.Add("isConnectedElement", false);
                                                        if (isFinSetup2)
                                                        {
                                                            var thirdlevelItems = lstResult.Where(x => x.parentId == fp2items.orgId && !string.IsNullOrEmpty(x.orgId)).OrderBy(y => y.orgId).ToList(); ;
                                                            if (thirdlevelItems.Count > 0)
                                                            {
                                                                dynamic thirdLevelData = new JArray();
                                                                var itemlvl3 = new JObject();
                                                                itemlvl3.Add("id", fp2items.orgId);
                                                                itemlvl3.Add("text", string.Join("-", fp2items.orgId, fp2items.orgName));
                                                                itemlvl3.Add("expanded", false);
                                                                itemlvl3.Add("checked", targetDetails.FirstOrDefault(x => x.org_id == fp2items.orgId && (string.IsNullOrEmpty(x.service_id) || x.service_id.ToLower() == "all".ToLower())) == null ? false : true);
                                                                itemlvl3.Add("fpLevel", 2);
                                                                itemlvl3.Add("parentId", level1Detail.orgId);
                                                                itemlvl3.Add("uniqId", level1Detail.orgId + "_" + fp2items.orgId);
                                                                itemlvl3.Add("unchecked", false);
                                                                itemlvl3.Add("disabled", fp2items.orgId == orgId);
                                                                itemlvl3.Add("items", new JArray());
                                                                itemlvl3.Add("isConnectedElement", false);
                                                                thirdLevelData.Add(itemlvl3);

                                                                foreach (var fp3items in thirdlevelItems)
                                                                {
                                                                    if ((targetMappedOrgIds.Contains(fp3items.orgId) && goalMappedOrgIds.Contains(fp3items.orgId)) || isFinSetup2)
                                                                    {
                                                                        itemlvl3 = new JObject();
                                                                        itemlvl3.Add("id", fp3items.orgId);
                                                                        itemlvl3.Add("text", string.Join("-", fp3items.orgId, fp3items.orgName));
                                                                        itemlvl3.Add("expanded", false);
                                                                        if ((!string.IsNullOrEmpty(fpLevel2) && fpLevel2.Substring(0, 3) == "org"))
                                                                        {
                                                                            itemlvl3.Add("checked", targetDetails.FirstOrDefault(x => x.org_id == fp2items.orgId && x.org_level == 3) == null ? false : true);
                                                                        }
                                                                        else
                                                                        {
                                                                            itemlvl3.Add("checked", targetDetails.FirstOrDefault(x => x.org_id == fp1item && x.service_id == fp2items.orgId) == null ? false : true);
                                                                        }
                                                                        itemlvl3.Add("fpLevel", 3);
                                                                        itemlvl3.Add("parentId", fp2items.orgId);
                                                                        itemlvl3.Add("uniqId", fp2items.orgId + "_" + fp3items.orgId);
                                                                        itemlvl3.Add("unchecked", false);
                                                                        itemlvl3.Add("disabled", fp3items.orgId == orgId);
                                                                        itemlvl3.Add("items", new JArray());
                                                                        itemlvl3.Add("isConnectedElement", false);
                                                                        thirdLevelData.Add(itemlvl3);
                                                                    }
                                                                }
                                                                itemlvl2.Add("items", thirdLevelData);
                                                            }
                                                        }
                                                        else
                                                        {
                                                            itemlvl2.Add("items", new JArray());
                                                        }
                                                        secondLevelData.Add(itemlvl2);
                                                    }
                                                }
                                                JArray data = JArray.FromObject(secondLevelData);
                                                var uncheckedNodes = data.FirstOrDefault(x => (bool)x["checked"] == false)?.ToList();
                                                itemlvl1["checked"] = uncheckedNodes != null && uncheckedNodes.Any() ? false : true;
                                                itemlvl1.Add("items", secondLevelData);
                                            }
                                            else
                                            {
                                                itemlvl1.Add("items", new JArray());
                                            }
                                            if (fpLevel1 == "org_id_1")
                                            {
                                                hierarchyData.Add(itemlvl1);
                                            }
                                            else
                                            {
                                                tenantLevelData.Add(itemlvl1);
                                            }
                                        }
                                        itemlvl0.Add("items", tenantLevelData);
                                    }
                                    if (fpLevel1 != "org_id_1")
                                    {
                                        hierarchyData.Add(itemlvl0);
                                    }
                                }
                            }
                        }
                    }
                    else if (orgLevel == level1)
                    {
                        List<OrgServiceDataHelper> listOfOrgIdsTargetsConnectedGoal = new();
                        List<OrgServiceDataHelper> listOfOrgIdsIndicatorsConnectedTarget = new();

                        if (type == "goals")
                        {
                            listOfOrgIdsTargetsConnectedGoal = await GetAllTargetsOrgIdsConnectedToGoal(goalId.Value, userDetails.tenant_id, budgetYear, maxFpOrgLevel, maxFpSrvLevel, isLevel2Service);
                        }
                        else if (type == "target")
                        {
                            listOfOrgIdsIndicatorsConnectedTarget = await GetAllIndicatorOrgIdsConnectedToTarget(Guid.Parse(id), userDetails.tenant_id, budgetYear, maxFpOrgLevel, maxFpSrvLevel, isLevel2Service);
                        }
                        if (string.IsNullOrEmpty(id))
                        {
                            var level1Detail = lstResult.FirstOrDefault(x => x.parentId == null && x.orgId == orgId);
                            var itemlvl0 = new JObject();
                            itemlvl0.Add("id", level1Detail.orgId);
                            itemlvl0.Add("text", string.Join("-", level1Detail.orgId, level1Detail.orgName));
                            itemlvl0.Add("expanded", false);
                            itemlvl0.Add("checked", false);
                            itemlvl0.Add("fpLevel", 1);
                            itemlvl0.Add("unchecked", true);
                            itemlvl0.Add("disabled", level1Detail.orgId == orgId);
                            itemlvl0.Add("parentId", 0);
                            itemlvl0.Add("uniqId", "0_" + level1Detail.orgId);

                            var connectedElementOrgIds = new List<string>();
                            if (isServiceSetup)
                            {
                                connectedElementOrgIds = type == "goals" ? listOfOrgIdsTargetsConnectedGoal.Where(x => x.Id == level1Detail.orgId).Select(x => x.orgId).ToList() : type == "target" ?
                                                                                listOfOrgIdsIndicatorsConnectedTarget.Where(x => x.Id == level1Detail.orgId).Select(x => x.orgId).ToList() : new List<string>();
                            }
                            else
                            {
                                connectedElementOrgIds = type == "goals" ? listOfOrgIdsTargetsConnectedGoal.Select(x => x.orgId).ToList() : type == "target" ?
                                                                                listOfOrgIdsIndicatorsConnectedTarget.Select(x => x.orgId).ToList() : new List<string>();
                            }

                            itemlvl0.Add("isConnectedElement", connectedElementOrgIds.Any());

                            if (orgIds.Count > 0)
                            {
                                dynamic tenantLevelData = new JArray();
                                List<string> goalMappedOrgIds = new List<string>();
                                List<string> targetMappedOrgIds = new List<string>();
                                if (type == "target")
                                {
                                    goalMappedOrgIds = GetOrgIdsConnectedToMasterGoal(goalId.Value, userId, budgetYear, maxFpOrgLevel, maxFpSrvLevel, isLevel2Service);
                                }
                                else if (type == "indicator")
                                {
                                    var tgtId = Guid.Parse(id);
                                    var master_target = (from p in tenantDBContext.tco_targets
                                                         where p.fk_tenant_id == userDetails.tenant_id && p.pk_target_id == tgtId
                                                         select p).FirstOrDefault();
                                    Guid tGoalId = master_target.fk_goal_id;
                                    goalMappedOrgIds = GetOrgIdsConnectedToMasterGoal(tGoalId, userId, budgetYear, maxFpOrgLevel, maxFpSrvLevel, isLevel2Service);
                                    targetMappedOrgIds = GetOrgIdsConnectedToMasterTarget(tgtId, userId, budgetYear, maxFpOrgLevel, maxFpSrvLevel, isLevel2Service);
                                }
                                var secondlevelItems = lstResult.Where(x => x.parentId == orgId && !string.IsNullOrEmpty(x.orgId)).OrderBy(y => y.orgId).ToList();
                                if (secondlevelItems.Count > 0)
                                {
                                    dynamic secondLevelData = new JArray();

                                    var itemlvl1 = new JObject();
                                    if ((string.IsNullOrEmpty(fpLevel2)) || (!string.IsNullOrEmpty(fpLevel2) && fpLevel2.Substring(0, 3) != "ser"))
                                    {
                                        itemlvl1.Add("id", level1Detail.orgId);
                                        itemlvl1.Add("text", string.Join("-", level1Detail.orgId, level1Detail.orgName));
                                        itemlvl1.Add("expanded", false);
                                        itemlvl1.Add("checked", false);
                                        itemlvl1.Add("fpLevel", 1);
                                        itemlvl1.Add("unchecked", secondlevelItems.Any());// this to avoid deleting of deligated target for two level tree
                                        itemlvl1.Add("disabled", level1Detail.orgId == orgId);
                                        itemlvl1.Add("parentId", orgId);
                                        itemlvl1.Add("uniqId", orgId + "_" + level1Detail.orgId);
                                        itemlvl1.Add("items", new JArray());
                                        itemlvl1.Add("isConnectedElement", connectedElementOrgIds.Any(x => x == level1Detail.orgId));
                                        secondLevelData.Add(itemlvl1);
                                    }

                                    foreach (var fp2items in secondlevelItems)
                                    {
                                        if ((type == "target" && goalMappedOrgIds.Contains(fp2items.orgId)) ||
                                            (type == "indicator" && targetMappedOrgIds.Contains(fp2items.orgId) && goalMappedOrgIds.Contains(fp2items.orgId)) || (type != "target" && type != "indicator"))
                                        {
                                            itemlvl1 = new JObject();
                                            itemlvl1.Add("id", fp2items.orgId);
                                            itemlvl1.Add("text", string.Join("-", fp2items.orgId, fp2items.orgName));
                                            itemlvl1.Add("expanded", false);
                                            itemlvl1.Add("checked", false);
                                            itemlvl1.Add("fpLevel", 2);
                                            itemlvl1.Add("parentId", orgId);
                                            itemlvl1.Add("uniqId", orgId + "_" + fp2items.orgId);
                                            itemlvl1.Add("unchecked", false);
                                            itemlvl1.Add("disabled", fp2items.orgId == orgId);
                                            itemlvl1.Add("items", new JArray());
                                            itemlvl1.Add("isConnectedElement", connectedElementOrgIds.Any(x => x == fp2items.orgId));
                                            secondLevelData.Add(itemlvl1);
                                        }
                                    }
                                    itemlvl0.Add("items", secondLevelData);
                                }
                                else
                                {
                                    itemlvl0.Add("items", new JArray());
                                }
                                if (fpLevel1 == "org_id_1")
                                {
                                    hierarchyData.Add(itemlvl0);
                                }
                                else
                                {
                                    tenantLevelData.Add(itemlvl0);
                                }
                            }
                            if (fpLevel1 != "org_id_1")
                            {
                                hierarchyData.Add(itemlvl0);
                            }
                        }
                        else
                        {
                            if (type == "target")
                            {
                                var tgtId = Guid.Parse(id);
                                var master_target_id = (from p in tenantDBContext.tco_targets
                                                        where p.fk_tenant_id == userDetails.tenant_id && p.pk_target_id == tgtId
                                                        select p.pk_target_id).FirstOrDefault();
                                if (master_target_id == null)
                                {
                                    master_target_id = tgtId;
                                }
                                if (master_target_id != null)
                                {
                                    var targetDetails = (from p in tenantDBContext.tco_targets_distribution
                                                         where p.fk_tenant_id == userDetails.tenant_id && p.fk_target_id == master_target_id && p.is_busplan_deltar != true
                                                         select new { p.org_id, p.org_level, p.service_id }).Distinct().ToList();
                                    List<string> filteredList = new List<string>();
                                    List<string> goalMappedOrgIds = new List<string>();
                                    if (type == "target")
                                    {
                                        goalMappedOrgIds = GetOrgIdsConnectedToMasterGoal(goalId.Value, userId, budgetYear, maxFpOrgLevel, maxFpSrvLevel, isLevel2Service);
                                    }

                                    var level1Detail = lstResult.FirstOrDefault(x => x.parentId == null && x.orgId == orgId);
                                    var secondlevelItems = lstResult.Where(x => x.parentId == orgId && !string.IsNullOrEmpty(x.orgId)).OrderBy(y => y.orgId).DistinctBy(x => x.orgId).ToList();
                                    var itemlvl0 = new JObject();
                                    itemlvl0.Add("id", level1Detail.orgId);
                                    itemlvl0.Add("text", string.Join("-", level1Detail.orgId, level1Detail.orgName));
                                    itemlvl0.Add("expanded", false);
                                    itemlvl0.Add("checked", true);
                                    itemlvl0.Add("fpLevel", 1);
                                    itemlvl0.Add("unchecked", secondlevelItems.Any());// this to avoid deleting of deligated target for two level tree
                                    itemlvl0.Add("disabled", false);
                                    itemlvl0.Add("parentId", string.Empty);
                                    itemlvl0.Add("uniqId", "1_" + level1Detail.orgId);

                                    var connectedElementOrgIds = isServiceSetup ? listOfOrgIdsIndicatorsConnectedTarget.Where(x => x.Id == level1Detail.orgId).Select(x => x.orgId).ToList() : listOfOrgIdsIndicatorsConnectedTarget.Select(x => x.orgId).ToList();

                                    itemlvl0.Add("isConnectedElement", type == "target" ? connectedElementOrgIds.Any() : false);

                                    if (orgIds.Count > 0)
                                    {
                                        dynamic tenantLevelData = new JArray();
                                        if (secondlevelItems.Count > 0)
                                        {
                                            dynamic secondLevelData = new JArray();

                                            var itemlvl1 = new JObject();
                                            itemlvl1.Add("id", level1Detail.orgId);
                                            itemlvl1.Add("text", string.Join("-", level1Detail.orgId, level1Detail.orgName));
                                            itemlvl1.Add("expanded", false);
                                            itemlvl1.Add("checked", true);
                                            itemlvl1.Add("fpLevel", 1);
                                            itemlvl1.Add("parentId", string.Empty);
                                            itemlvl1.Add("uniqId", "2_" + level1Detail.orgId);
                                            itemlvl1.Add("unchecked", true);
                                            itemlvl1.Add("disabled", level1Detail.orgId == orgId);
                                            itemlvl1.Add("items", new JArray());
                                            itemlvl1.Add("isConnectedElement", type == "target" ? connectedElementOrgIds.Any(x => x == level1Detail.orgId) : false);
                                            secondLevelData.Add(itemlvl1);

                                            foreach (var fp2items in secondlevelItems)
                                            {
                                                if ((type == "target" && goalMappedOrgIds.Contains(fp2items.orgId)) || type != "target")
                                                {
                                                    itemlvl1 = new JObject();
                                                    itemlvl1.Add("id", fp2items.orgId);
                                                    itemlvl1.Add("text", string.Join("-", fp2items.orgId, fp2items.orgName));
                                                    itemlvl1.Add("expanded", false);
                                                    if ((!string.IsNullOrEmpty(fpLevel2) && fpLevel2.Substring(0, 3) == "org"))
                                                    {
                                                        itemlvl1.Add("checked", !(targetDetails.FirstOrDefault(x => x.org_id == fp2items.orgId && x.org_level == fLv2OrgLvl) == null));
                                                    }
                                                    else
                                                    {
                                                        itemlvl1.Add("checked", !(targetDetails.FirstOrDefault(x => x.org_id == orgId && x.service_id == fp2items.orgId) == null));
                                                    }
                                                    itemlvl1.Add("fpLevel", 2);
                                                    itemlvl1.Add("unchecked", false);
                                                    itemlvl1.Add("disabled", false);
                                                    itemlvl1.Add("parentId", orgId);
                                                    itemlvl1.Add("uniqId", orgId + "_" + fp2items.orgId);
                                                    itemlvl1.Add("items", new JArray());
                                                    itemlvl1.Add("isConnectedElement", type == "target" ? connectedElementOrgIds.Any(x => x == fp2items.orgId) : false);
                                                    secondLevelData.Add(itemlvl1);
                                                }
                                            }

                                            itemlvl0.Add("items", secondLevelData);
                                        }
                                        else
                                        {
                                            itemlvl0["checked"] = targetDetails.FirstOrDefault(x => x.org_id == orgId) != null;
                                            itemlvl0.Add("items", new JArray());
                                        }
                                        if (fpLevel1 == "org_id_1")
                                        {
                                            hierarchyData.Add(itemlvl0);
                                        }
                                        else
                                        {
                                            tenantLevelData.Add(itemlvl0);
                                        }
                                    }
                                    if (fpLevel1 != "org_id_1")
                                    {
                                        hierarchyData.Add(itemlvl0);
                                    }
                                }
                            }
                            else if (type == "goals")
                            {
                                var master_goal_id = Guid.Parse(id);

                                var goalDetails = (from p in tenantDBContext.tco_goals
                                                   join q in tenantDBContext.tco_goals_distribution on p.pk_goal_id equals q.fk_goal_id
                                                   where p.fk_tenant_id == userDetails.tenant_id && p.pk_goal_id == master_goal_id && !p.is_busplan_goal && !q.is_busplan_delgoa
                                                   select new { q.org_id, q.org_level, q.service_id }).Distinct().ToList();

                                var level1Detail = lstResult.FirstOrDefault(x => x.parentId == null && x.orgId == orgId);
                                var secondlevelItems = lstResult.Where(x => x.parentId == orgId && !string.IsNullOrEmpty(x.orgId)).OrderBy(y => y.orgId).DistinctBy(x => x.orgId).ToList();
                                var itemlvl0 = new JObject();
                                itemlvl0.Add("id", level1Detail.orgId);
                                itemlvl0.Add("text", string.Join("-", level1Detail.orgId, level1Detail.orgName));
                                itemlvl0.Add("expanded", false);
                                itemlvl0.Add("checked", true);
                                itemlvl0.Add("disabled", false);
                                itemlvl0.Add("fpLevel", 1);
                                itemlvl0.Add("unchecked", secondlevelItems.Any());// this to avoid deleting of deligated target for two level tree
                                itemlvl0.Add("parentId", string.Empty);
                                itemlvl0.Add("uniqId", "1_" + level1Detail.orgId);

                                var connectedElementOrgIds = isServiceSetup ? listOfOrgIdsTargetsConnectedGoal.Where(x => x.Id == level1Detail.orgId).Select(x => x.orgId).ToList() : listOfOrgIdsTargetsConnectedGoal.Select(x => x.orgId).ToList();

                                itemlvl0.Add("isConnectedElement", type == "goals" ? connectedElementOrgIds.Any() : false);

                                if (orgIds.Count > 0)
                                {
                                    dynamic tenantLevelData = new JArray();
                                    if (secondlevelItems.Count > 0)
                                    {
                                        dynamic secondLevelData = new JArray();

                                        var itemlvl1 = new JObject();
                                        itemlvl1.Add("id", level1Detail.orgId);
                                        itemlvl1.Add("text", string.Join("-", level1Detail.orgId, level1Detail.orgName));
                                        itemlvl1.Add("expanded", false);
                                        itemlvl1.Add("checked", true);
                                        itemlvl1.Add("disabled", level1Detail.orgId == orgId);
                                        itemlvl1.Add("fpLevel", 1);
                                        itemlvl1.Add("parentId", string.Empty);
                                        itemlvl1.Add("uniqId", "2_" + level1Detail.orgId);
                                        itemlvl1.Add("unchecked", false);
                                        itemlvl1.Add("items", new JArray());
                                        itemlvl1.Add("isConnectedElement", type == "goals" ? connectedElementOrgIds.Any(x => x == level1Detail.orgId) : false);
                                        secondLevelData.Add(itemlvl1);

                                        foreach (var fp2items in secondlevelItems)
                                        {
                                            itemlvl1 = new JObject();
                                            itemlvl1.Add("id", fp2items.orgId);
                                            itemlvl1.Add("text", string.Join("-", fp2items.orgId, fp2items.orgName));
                                            itemlvl1.Add("expanded", false);
                                            if ((!string.IsNullOrEmpty(fpLevel2) && fpLevel2.Substring(0, 3) == "org"))
                                            {
                                                itemlvl1.Add("checked", !(master_goal_id == null || goalDetails.FirstOrDefault(x => x.org_id == fp2items.orgId && x.org_level == fLv2OrgLvl && (string.IsNullOrEmpty(x.service_id) || x.service_id == "ALL")) == null));
                                            }
                                            else
                                            {
                                                itemlvl1.Add("checked", !(master_goal_id == null || goalDetails.FirstOrDefault(x => x.org_id == orgId && x.service_id == fp2items.orgId) == null));
                                            }
                                            itemlvl1.Add("fpLevel", 2);
                                            itemlvl1.Add("parentId", orgId);
                                            itemlvl1.Add("uniqId", orgId + "_" + fp2items.orgId);
                                            itemlvl1.Add("unchecked", false);
                                            itemlvl1.Add("disabled", false);
                                            itemlvl1.Add("items", new JArray());
                                            itemlvl1.Add("isConnectedElement", type == "goals" ? connectedElementOrgIds.Any(x => x == fp2items.orgId) : false);
                                            secondLevelData.Add(itemlvl1);
                                        }
                                        itemlvl0.Add("items", secondLevelData);
                                    }
                                    else
                                    {
                                        itemlvl0["checked"] = goalDetails.FirstOrDefault(x => x.org_id == orgId) != null;   // Added this line as part of #83947
                                        itemlvl0.Add("items", new JArray());
                                    }
                                    if (fpLevel1 == "org_id_1")
                                    {
                                        hierarchyData.Add(itemlvl0);
                                    }
                                    else
                                    {
                                        tenantLevelData.Add(itemlvl0);
                                    }
                                }
                                if (fpLevel1 != "org_id_1")
                                {
                                    hierarchyData.Add(itemlvl0);
                                }
                            }
                            if (type == "indicator")
                            {
                                var tgtId = Guid.Parse(id);
                                var master_target = (from p in tenantDBContext.tco_targets
                                                     where p.fk_tenant_id == userDetails.tenant_id && p.pk_target_id == tgtId
                                                     select p).FirstOrDefault();
                                Guid master_target_id = master_target.pk_target_id;
                                Guid tGoalId = master_target.fk_goal_id;
                                if (master_target_id == null)
                                {
                                    master_target_id = tgtId;
                                }

                                if (master_target_id != null)
                                {
                                    var targetDetails = (from p in tenantDBContext.tco_targets_distribution
                                                         join d in tenantDBContext.tfp_effect_target_detail on new { t = p.fk_target_id, d = p.pk_target_distribution_id }
                                                                                                        equals new { t = d.fk_target_id, d = d.fk_target_distribution_id }
                                                         where p.fk_tenant_id == userDetails.tenant_id && p.fk_target_id == master_target_id && d.fk_indicator_code == indiCode && p.is_busplan_deltar != true && d.is_bplan_indicator != true
                                                         select new { p.fk_target_id, p.org_id, p.org_level, p.service_id }).Distinct().ToList();

                                    List<string> filteredList = new List<string>();
                                    List<string> goalMappedOrgIds = GetOrgIdsConnectedToMasterGoal(tGoalId, userId, budgetYear, maxFpOrgLevel, maxFpSrvLevel, isLevel2Service);
                                    List<string> targetMappedOrgIds = GetOrgIdsConnectedToMasterTarget(tgtId, userId, budgetYear, maxFpOrgLevel, maxFpSrvLevel, isLevel2Service);

                                    var level1Detail = lstResult.FirstOrDefault(x => x.parentId == null && x.orgId == orgId);
                                    var secondlevelItems = lstResult.Where(x => x.parentId == orgId && !string.IsNullOrEmpty(x.orgId)).OrderBy(y => y.orgId).ToList();
                                    var itemlvl0 = new JObject();
                                    itemlvl0.Add("id", level1Detail.orgId);
                                    itemlvl0.Add("text", string.Join("-", level1Detail.orgId, level1Detail.orgName));
                                    itemlvl0.Add("expanded", false);
                                    itemlvl0.Add("checked", true);
                                    itemlvl0.Add("fpLevel", 1);
                                    itemlvl0.Add("unchecked", secondlevelItems.Any());// this to avoid deleting of deligated target for two level tree
                                    itemlvl0.Add("disabled", false);
                                    itemlvl0.Add("parentId", string.Empty);
                                    itemlvl0.Add("uniqId", "1_" + level1Detail.orgId);
                                    itemlvl0.Add("isConnectedElement", false);

                                    if (orgIds.Count > 0)
                                    {
                                        dynamic tenantLevelData = new JArray();
                                        if (secondlevelItems.Count > 0)
                                        {
                                            dynamic secondLevelData = new JArray();

                                            var itemlvl1 = new JObject();
                                            itemlvl1.Add("id", level1Detail.orgId);
                                            itemlvl1.Add("text", string.Join("-", level1Detail.orgId, level1Detail.orgName));
                                            itemlvl1.Add("expanded", false);

                                            if ((!string.IsNullOrEmpty(fpLevel1) && fpLevel1.Substring(0, 3) == "org"))
                                            {
                                                itemlvl1.Add("checked", targetDetails.FirstOrDefault(x => x.org_id == level1Detail.orgId && x.org_level == fpLvl1OrgLevel) == null ? false : true);
                                            }
                                            else
                                            {
                                                itemlvl1.Add("checked", targetDetails.FirstOrDefault(x => x.org_id == orgId && x.service_id == level1Detail.orgId) == null ? false : true);
                                            }

                                            itemlvl1.Add("fpLevel", 1);
                                            itemlvl1.Add("parentId", string.Empty);
                                            itemlvl1.Add("uniqId", "2_" + level1Detail.orgId);
                                            itemlvl1.Add("unchecked", false);
                                            itemlvl1.Add("disabled", level1Detail.orgId == orgId);
                                            itemlvl1.Add("items", new JArray());
                                            itemlvl1.Add("isConnectedElement", false);
                                            secondLevelData.Add(itemlvl1);

                                            foreach (var fp2items in secondlevelItems)
                                            {
                                                if (targetMappedOrgIds.Contains(fp2items.orgId) && goalMappedOrgIds.Contains(fp2items.orgId))
                                                {
                                                    itemlvl1 = new JObject();
                                                    itemlvl1.Add("id", fp2items.orgId);
                                                    itemlvl1.Add("text", string.Join("-", fp2items.orgId, fp2items.orgName));
                                                    itemlvl1.Add("expanded", false);
                                                    if ((!string.IsNullOrEmpty(fpLevel2) && fpLevel2.Substring(0, 3) == "org"))
                                                    {
                                                        itemlvl1.Add("checked", targetDetails.FirstOrDefault(x => x.org_id == fp2items.orgId && x.org_level == fLv2OrgLvl) == null ? false : true);
                                                    }
                                                    else
                                                    {
                                                        itemlvl1.Add("checked", targetDetails.FirstOrDefault(x => x.org_id == orgId && x.service_id == fp2items.orgId) == null ? false : true);
                                                    }
                                                    itemlvl1.Add("fpLevel", 2);
                                                    itemlvl1.Add("unchecked", false);
                                                    itemlvl1.Add("disabled", false);
                                                    itemlvl1.Add("parentId", orgId);
                                                    itemlvl1.Add("uniqId", orgId + "_" + fp2items.orgId);
                                                    itemlvl1.Add("items", new JArray());
                                                    itemlvl1.Add("isConnectedElement", false);
                                                    secondLevelData.Add(itemlvl1);
                                                }
                                            }
                                            itemlvl0.Add("items", secondLevelData);
                                        }
                                        else
                                        {
                                            itemlvl0["checked"] = targetDetails.FirstOrDefault(x => x.org_id == orgId) != null;   // Added this line as part of #83947
                                            itemlvl0.Add("items", new JArray());
                                        }
                                        if (fpLevel1 == "org_id_1")
                                        {
                                            hierarchyData.Add(itemlvl0);
                                        }
                                        else
                                        {
                                            tenantLevelData.Add(itemlvl0);
                                        }
                                    }
                                    if (fpLevel1 != "org_id_1")
                                    {
                                        hierarchyData.Add(itemlvl0);
                                    }
                                }
                            }
                        }
                    }
                }
                if (hierarchyData.Count > 0)
                {

                    hierarchyData[0]["disabled"] = hierarchyData[0]["items"].Count > 0 ? false : true;
                }
                return hierarchyData;
            }
        }

        private async Task<JArray> GetChapterSetupDelegationTreeTarget(string userId, int budgetYear, string orgId, List<BPDelegationTreeHelper> targetData, int orgLevel, string chapterId, List<BPDelegationTreeHelper> goalData)
        {
            JArray hierarchyData = new JArray();
            if (orgLevel > 1) return hierarchyData;
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));
            var cityLevel = orgVersionContent.lstOrgHierarchy.FirstOrDefault();
            List<string?> uniqIds = new List<string?>();
            var itemlvl0 = new JObject();
            itemlvl0.Add("id", cityLevel.org_id_1);
            itemlvl0.Add("text", string.Join("-", cityLevel.org_id_1, cityLevel.org_name_1));
            itemlvl0.Add("expanded", true);
            itemlvl0.Add("checked", cityLevel.org_id_1 == orgId);
            itemlvl0.Add("fpLevel", 0);
            itemlvl0.Add("parentId", null);
            itemlvl0.Add("uniqId", cityLevel.org_id_1);
            itemlvl0.Add("uniq_id", cityLevel.org_id_1);
            itemlvl0.Add("isNodeDisabled", cityLevel.org_id_1 == orgId);
            itemlvl0.Add("isDisabled", false);

            var dropDownDataList = await dbContext.tco_attribute_values.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.attribute_type == "CHAPTER" && x.status == 1).ToListAsync();
            if (dropDownDataList != null && dropDownDataList.Count > 0)
            {
                uniqIds = dropDownDataList.Select(x => x.pk_attribute_id).Distinct().ToList();
            }

            dynamic tenantLevelData = new JArray();

            var itemlvl0_1 = new JObject();
            itemlvl0_1.Add("id", cityLevel.org_id_1 + "_" + cityLevel.org_id_1);
            itemlvl0_1.Add("text", string.Join("-", cityLevel.org_id_1, cityLevel.org_name_1));
            itemlvl0_1.Add("expanded", true);
            itemlvl0_1.Add("checked", cityLevel.org_id_1 == orgId);
            itemlvl0_1.Add("fpLevel", 0);
            itemlvl0_1.Add("parentId", null);
            itemlvl0_1.Add("uniqId", cityLevel.org_id_1 + "_" + cityLevel.org_id_1);
            itemlvl0_1.Add("uniq_id", cityLevel.org_id_1 + "_" + cityLevel.org_id_1);
            itemlvl0_1.Add("isNodeDisabled", cityLevel.org_id_1 == orgId);
            itemlvl0_1.Add("isDisabled", true);
            tenantLevelData.Add(itemlvl0_1);
            foreach (var attrId in uniqIds)
            {
                var attributeData = dropDownDataList.FirstOrDefault(x => x.pk_attribute_id == attrId);
                if (goalData.FirstOrDefault(x => x.attributeId == attrId) != null)
                {
                    var itemlvl1 = new JObject();
                    itemlvl1.Add("id", attrId);
                    itemlvl1.Add("text", attrId + "-" + attributeData.attribute_name);
                    itemlvl1.Add("expanded", false);
                    itemlvl1.Add("checked", targetData.FirstOrDefault(x => x.attributeId == attrId) != null ? true : false);
                    itemlvl1.Add("fpLevel", 1);
                    itemlvl1.Add("parentId", cityLevel.org_id_1);
                    itemlvl1.Add("uniqId", cityLevel.org_id_1 + "_" + attrId);
                    itemlvl1.Add("uniq_id", cityLevel.org_id_1 + "_" + attrId);
                    itemlvl1.Add("isNodeDisabled", chapterId != "");
                    itemlvl1.Add("isDisabled", chapterId != "");
                    tenantLevelData.Add(itemlvl1);
                }
            }
            itemlvl0.Add("items", tenantLevelData);
            hierarchyData.Add(itemlvl0);

            return hierarchyData;
        }

        private dynamic FormatTenantLevelDataFinSetup2(string userId, List<clsOrgIdsAndServiceIds> lstResult, Guid master_goal_id)
        {
            var userDetails = _utility.GetUserDetails(userId);
            var tenantDBContext = _utility.GetTenantDBContext();
            dynamic tenantLevelData = new JArray();
            var goalDetails = (from p in tenantDBContext.tco_goals
                               join q in tenantDBContext.tco_goals_distribution on p.pk_goal_id equals q.fk_goal_id
                               where p.fk_tenant_id == userDetails.tenant_id && p.pk_goal_id == master_goal_id && !p.is_busplan_goal && !q.is_busplan_delgoa
                               select new { q.org_id, q.org_level, q.service_id }).Distinct().ToList();
            foreach (var fp1item in lstResult.Where(x => x.parentId == null).Select(y => y.orgId).Distinct().ToList())
            {
                var secondlevelItems = lstResult.Where(x => x.parentId == fp1item && !string.IsNullOrEmpty(x.orgId)).OrderBy(y => y.orgId).ToList();

                var level1Detail = lstResult.FirstOrDefault(x => x.parentId == null && x.orgId == fp1item);
                var itemlvl1 = new JObject();
                itemlvl1.Add("id", level1Detail.orgId);
                itemlvl1.Add("text", string.Join("-", level1Detail.orgId, level1Detail.orgName));
                itemlvl1.Add("expanded", false);
                itemlvl1.Add("checked", !(master_goal_id == null || goalDetails.FirstOrDefault(x => x.org_id == fp1item && (string.IsNullOrEmpty(x.service_id) || x.service_id == "ALL")) == null));
                itemlvl1.Add("fpLevel", 1);
                itemlvl1.Add("unchecked", secondlevelItems.Any());// this to avoid deleting of deligated target for two level tree
                itemlvl1.Add("parentId", "LK");
            }

            return tenantLevelData;
        }

        public Guid InsertIntoGoalHeader(int tenantId, int updatedBy)
        {
            return InsertIntoGoalHeaderAsync(tenantId, updatedBy).GetAwaiter().GetResult();
        }

        public async Task<Guid> InsertIntoGoalHeaderAsync(int tenantId, int updatedBy)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            var headerId = Guid.NewGuid();
            tco_goals_header gh = new tco_goals_header
            {
                pk_goal_header_Id = headerId,
                fk_tenant_id = tenantId,
                updated = DateTime.UtcNow,
                updated_by = updatedBy
            };
            await dbContext.tco_goals_header.AddAsync(gh);
            await dbContext.SaveChangesAsync();
            return headerId;
        }
        public void DeleteGoalHeader(Guid headerId, int tenantId)
        {
            DeleteGoalHeaderAsync(headerId, tenantId).GetAwaiter().GetResult();
        }

        public async Task DeleteGoalHeaderAsync(Guid headerId, int tenantId)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            if (!await dbContext.tco_goals.AnyAsync(x => x.fk_goal_header_Id == headerId && x.fk_tenant_id == tenantId) &&
                    await dbContext.tco_goals_header.FirstOrDefaultAsync(x => x.pk_goal_header_Id == headerId && x.fk_tenant_id == tenantId) != null)
            {
                dbContext.tco_goals_header.Remove(dbContext.tco_goals_header.FirstOrDefault(x => x.pk_goal_header_Id == headerId && x.fk_tenant_id == tenantId));
                await dbContext.SaveChangesAsync();

            }
        }

        public Guid InsertTargetHeader(int tenantId, int updatedBy)
        {
            return InsertTargetHeaderAsync(tenantId, updatedBy).GetAwaiter().GetResult();
        }

        public async Task<Guid> InsertTargetHeaderAsync(int tenantId, int updatedBy)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            var headerId = Guid.NewGuid();
            tco_targets_header th = new tco_targets_header
            {
                pk_target_header_id = headerId,
                fk_tenant_id = tenantId,
                updated = DateTime.UtcNow,
                updated_by = updatedBy
            };
            dbContext.tco_targets_header.Add(th);
            await dbContext.SaveChangesAsync();
            return headerId;
        }
        public void DeleteTargetHeader(Guid headerId, int tenantId)
        {
            DeleteTargetHeaderAsync(headerId, tenantId).GetAwaiter().GetResult();
        }

        public async Task DeleteTargetHeaderAsync(Guid headerId, int tenantId)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            if (!(await dbContext.tco_targets.AnyAsync(x => x.fk_target_header_id == headerId && x.fk_tenant_id == tenantId)) &&
                    await dbContext.tco_targets_header.FirstOrDefaultAsync(x => x.pk_target_header_id == headerId && x.fk_tenant_id == tenantId) != null)
            {
                dbContext.tco_targets_header.Remove(dbContext.tco_targets_header.FirstOrDefault(x => x.pk_target_header_id == headerId && x.fk_tenant_id == tenantId));
                await dbContext.SaveChangesAsync();
            }
        }

        private List<string> GetOrgIdsConnectedToMasterGoal(Guid goalId, string userId, int budgetYear, int maxFpOrgLevel, int maxFpSrvLevel, bool isLevel2Service)
        {
            TenantDBContext dbcontext = _utility.GetTenantDBContext();
            UserData userDetails = _utility.GetUserDetails(userId);

            List<string> orgIds = (from cg in dbcontext.tco_goals_distribution
                                   join a in dbcontext.tco_goals on cg.fk_goal_id equals a.pk_goal_id
                                   where a.pk_goal_id == goalId && a.budget_year == budgetYear && a.fk_tenant_id == userDetails.tenant_id && a.is_busplan_goal != true && cg.org_level <= maxFpOrgLevel && cg.is_busplan_delgoa != true
                                   select cg.org_id).Distinct().ToList();

            if (isLevel2Service)
            {
                List<string> servIds = (from cg in dbcontext.tco_goals_distribution
                                        join a in dbcontext.tco_goals on cg.fk_goal_id equals a.pk_goal_id
                                        where a.pk_goal_id == goalId && a.budget_year == budgetYear && a.fk_tenant_id == userDetails.tenant_id && a.is_busplan_goal != true && cg.service_level <= maxFpSrvLevel && cg.is_busplan_delgoa != true
                                           && !string.IsNullOrEmpty(cg.service_id) && cg.service_id != "ALL" && cg.service_id != "-1"
                                        select cg.service_id).Distinct().ToList();
                orgIds.AddRange(servIds);
            }

            return orgIds;
        }

        private List<string> GetOrgIdsConnectedToMasterTarget(Guid targetId, string userId, int budgetYear, int maxFpOrgLevel, int maxFpSrvLevel, bool isLevel2Service)
        {
            TenantDBContext dbcontext = _utility.GetTenantDBContext();
            UserData userDetails = _utility.GetUserDetails(userId);

            List<string> orgIds = (from cg in dbcontext.tco_targets_distribution
                                   join a in dbcontext.tco_targets on cg.fk_target_id equals a.pk_target_id
                                   where a.pk_target_id == targetId && a.budget_year == budgetYear && a.fk_tenant_id == userDetails.tenant_id && cg.org_level <= maxFpOrgLevel && cg.is_busplan_deltar != true
                                   select cg.org_id).ToList();

            if (isLevel2Service)
            {
                List<string> servIds = (from cg in dbcontext.tco_targets_distribution
                                        join a in dbcontext.tco_targets on cg.fk_target_id equals a.pk_target_id
                                        where a.pk_target_id == targetId && a.budget_year == budgetYear && a.fk_tenant_id == userDetails.tenant_id && cg.service_level <= maxFpSrvLevel && cg.is_busplan_deltar != true
                                        && !string.IsNullOrEmpty(cg.service_id) && cg.service_id != "ALL" && cg.service_id != "-1"
                                        select cg.service_id).ToList();
                orgIds.AddRange(servIds);
            }

            return orgIds;
        }

        private async Task<List<OrgServiceDataHelper>> GetAllTargetsOrgIdsConnectedToGoal(Guid goalId, int tenantId, int budgetYear, int maxFpOrgLevel, int maxFpSrvLevel, bool isLevel2Service)
        {
            TenantDBContext dbContext1 = await _utility.GetTenantDbContextForParallelReadAsync();
            TenantDBContext dbContext2 = await _utility.GetTenantDbContextForParallelReadAsync();

            var orgIdsTask = (from a in dbContext1.tco_targets_distribution
                              join b in dbContext1.tco_targets on new { a = a.fk_target_id, b = a.fk_tenant_id } equals new { a = b.pk_target_id, b = b.fk_tenant_id }
                              where b.fk_goal_id == goalId && b.budget_year == budgetYear && b.fk_tenant_id == tenantId
                                    && a.org_level <= maxFpOrgLevel && a.is_busplan_deltar != true
                              select new OrgServiceDataHelper
                              {
                                  orgId = a.org_id,
                                  serviceId = a.service_id

                              }).ToListAsync();

            Task<List<OrgServiceDataHelper>> servIdsTask = Task.FromResult(new List<OrgServiceDataHelper>());

            if (isLevel2Service)
            {
                servIdsTask = (from cg in dbContext2.tco_targets_distribution
                               join a in dbContext2.tco_targets on new { a = cg.fk_target_id, b = cg.fk_tenant_id } equals new { a = a.pk_target_id, b = a.fk_tenant_id }
                               where a.fk_goal_id == goalId && a.budget_year == budgetYear && a.fk_tenant_id == tenantId
                                     && cg.service_level <= maxFpSrvLevel && cg.is_busplan_deltar != true
                                     && !string.IsNullOrEmpty(cg.service_id) && cg.service_id != "ALL" && cg.service_id != "-1"
                               select new OrgServiceDataHelper
                               {
                                   orgId = cg.org_id,
                                   serviceId = cg.service_id
                               }).ToListAsync();
            }

            await Task.WhenAll(orgIdsTask, servIdsTask);

            var orgIds = await orgIdsTask;
            var servIds = await servIdsTask;

            var filterOrgIds = (from a in orgIds
                                select new OrgServiceDataHelper
                                {
                                    Id = a.orgId,
                                    orgId = string.IsNullOrEmpty(a.serviceId) || a.serviceId == "ALL" || a.serviceId == "-1" ? a.orgId : a.serviceId
                                }).ToList();
            filterOrgIds.AddRange(servIds.Select(x => new OrgServiceDataHelper { Id = x.orgId, orgId = x.serviceId }));
            return filterOrgIds.DistinctBy(x => x.orgId).ToList();
        }

        private async Task<List<OrgServiceDataHelper>> GetAllIndicatorOrgIdsConnectedToTarget(Guid targetId, int tenantId, int budgetYear, int maxFpOrgLevel, int maxFpSrvLevel, bool isLevel2Service)
        {
            TenantDBContext dbContext1 = await _utility.GetTenantDbContextForParallelReadAsync();
            TenantDBContext dbContext2 = await _utility.GetTenantDbContextForParallelReadAsync();

            var orgIdsTask = (from t in dbContext1.tfp_effect_target_detail
                              join a in dbContext1.tco_targets_distribution on new { a = t.fk_tenant_id, b = t.fk_target_id, c = t.fk_target_distribution_id } equals new { a = a.fk_tenant_id, b = a.fk_target_id, c = a.pk_target_distribution_id}
                              join b in dbContext1.tco_targets on new { a = a.fk_target_id, b = a.fk_tenant_id } equals new { a = b.pk_target_id, b = b.fk_tenant_id }
                              where b.pk_target_id == targetId && b.budget_year == budgetYear && b.fk_tenant_id == tenantId
                                    && a.org_level <= maxFpOrgLevel && a.is_busplan_deltar != true
                              select new OrgServiceDataHelper
                              {
                                  orgId = a.org_id,
                                  serviceId = a.service_id
                              }).ToListAsync();

            Task<List<OrgServiceDataHelper>> servIdsTask = Task.FromResult(new List<OrgServiceDataHelper>());

            if (isLevel2Service)
            {
                servIdsTask = (from t in dbContext2.tfp_effect_target_detail
                               join a in dbContext2.tco_targets_distribution on new { a = t.fk_tenant_id, b = t.fk_target_id, c = t.fk_target_distribution_id } equals new { a = a.fk_tenant_id, b = a.fk_target_id, c = a.pk_target_distribution_id }
                               join b in dbContext2.tco_targets on new { a = a.fk_target_id, b = a.fk_tenant_id } equals new { a = b.pk_target_id, b = b.fk_tenant_id }
                               where b.pk_target_id == targetId && b.budget_year == budgetYear && b.fk_tenant_id == tenantId
                                     && a.service_level <= maxFpSrvLevel && a.is_busplan_deltar != true
                                     && !string.IsNullOrEmpty(a.service_id) && a.service_id != "ALL" && a.service_id != "-1"
                               select new OrgServiceDataHelper
                               {
                                   orgId = a.org_id,
                                   serviceId = a.service_id
                               }).ToListAsync();
            }

            await Task.WhenAll(orgIdsTask, servIdsTask);

            var orgIds = await orgIdsTask;
            var servIds = await servIdsTask;

            var filterOrgIds = (from a in orgIds
                                select new OrgServiceDataHelper
                                {
                                    Id = a.orgId,
                                    orgId = string.IsNullOrEmpty(a.serviceId) || a.serviceId == "ALL" || a.serviceId == "-1" ? a.orgId : a.serviceId
                                }).ToList();
            filterOrgIds.AddRange(servIds.Select(x => new OrgServiceDataHelper { Id = x.orgId, orgId = x.serviceId }));
            return filterOrgIds.DistinctBy(x => x.orgId).ToList();
        }

        public async Task<List<GoalnDistrFth>> FetchGoalDistrPerfAsync(int tenantId, int budgetYear, string serviceId, List<string> chapterIds = null)
        {
            List<GoalnDistrFth> goalDistrList;
            TenantDBContext dbContext1 = await _utility.GetTenantDbContextForParallelReadAsync();
            var tmrCityGoalsData = await dbContext1.tmr_city_goals_status.Where(x => x.FkTenantId == tenantId && x.ForecastPeriod.ToString().StartsWith(budgetYear.ToString())).ToListAsync();

            TenantDBContext dbContext2 = await _utility.GetTenantDbContextForParallelReadAsync();
            var statusData = await dbContext2.tco_progress_status.Where(x => x.fk_tenant_id == tenantId && x.type == "MONTHREP_GOAL" && x.active == 1).ToListAsync();

            if (serviceId != null && (serviceId == "ALL" || serviceId == "-1"))
            {
                TenantDBContext dbContext = await _utility.GetTenantDbContextForParallelReadAsync();
                goalDistrList = await (from gd in dbContext.tco_goals_distribution
                                       join tg in dbContext.tco_goals on new { tnt = gd.fk_tenant_id, gl = gd.fk_goal_id }
                                                                  equals new { tnt = tg.fk_tenant_id, gl = tg.pk_goal_id }
                                       where tg.budget_year == budgetYear
                                          && tg.fk_tenant_id == tenantId
                                       select new GoalnDistrFth
                                       {
                                           goalDistrId = gd.pk_goal_distribution_id,
                                           goalId = tg.pk_goal_id,
                                           goal_name = tg.goal_name,
                                           org_created = tg.org_created,
                                           org_level_created = tg.org_level_created ?? 0,
                                           focus_area = tg.focus_area,
                                           unsd_goals = tg.unsd_goals,
                                           is_busplan_goal = tg.is_busplan_goal,
                                           is_busplan_delgoa = gd.is_busplan_delgoa,
                                           org_id = gd.org_id,
                                           org_level = gd.org_level ?? 0,
                                           service_id = gd.service_id.Trim().ToUpper() == "ALL" || gd.service_id.Trim() == "" ? "-1" : gd.service_id.Trim(),
                                           service_level = gd.service_level ?? 0,
                                           target_blob_id = gd.target_blob_id,
                                           focus_blob_id = gd.focus_blob_id,
                                           delgo_blob_id = gd.delgo_blob_id ?? Guid.Empty,
                                           tags = gd.tags,
                                           tenantId = tg.fk_tenant_id,
                                           attribute_id = gd.fk_attribute_id
                                       }).ToListAsync();
                if (chapterIds != null && chapterIds.Any())
                {
                    goalDistrList = goalDistrList.Where(x => chapterIds.Contains(x.attribute_id)).ToList();
                }
                foreach (var goal in goalDistrList)
                {
                    var goalData = GetGoalStatusData(tmrCityGoalsData, goal.goalId, goal.goalDistrId);
                    goal.statusGoalId = goalData.Status != null ? goalData.Status.Value : 0;
                    goal.statusGoal = goalData.Status != null && goalData.Status != 0 ? statusData.FirstOrDefault(x => x.status_id == goalData.Status.Value).status_description : "";
                    goal.statusGoalDescription = goalData.StatusDesc ?? "";
                }
            }
            else
            {
                TenantDBContext dbContext = await _utility.GetTenantDbContextForParallelReadAsync();
                goalDistrList = await (from gd in dbContext.tco_goals_distribution
                                       join tg in dbContext.tco_goals on new { tnt = gd.fk_tenant_id, gl = gd.fk_goal_id }
                                                                  equals new { tnt = tg.fk_tenant_id, gl = tg.pk_goal_id }
                                       where tg.budget_year == budgetYear
                                          && tg.fk_tenant_id == tenantId
                                       select new GoalnDistrFth
                                       {
                                           goalDistrId = gd.pk_goal_distribution_id,
                                           goalId = tg.pk_goal_id,
                                           goal_name = tg.goal_name,
                                           org_created = tg.org_created,
                                           org_level_created = tg.org_level_created ?? 0,
                                           focus_area = tg.focus_area,
                                           unsd_goals = tg.unsd_goals,
                                           is_busplan_goal = tg.is_busplan_goal,
                                           is_busplan_delgoa = gd.is_busplan_delgoa,
                                           org_id = gd.org_id,
                                           org_level = gd.org_level ?? 0,
                                           service_id = gd.service_id.Trim().ToUpper() == "ALL" || gd.service_id.Trim() == "" ? "-1" : gd.service_id.Trim(),
                                           service_level = gd.service_level ?? 0,
                                           target_blob_id = gd.target_blob_id,
                                           focus_blob_id = gd.focus_blob_id,
                                           delgo_blob_id = gd.delgo_blob_id ?? Guid.Empty,
                                           tags = gd.tags,
                                           tenantId = tg.fk_tenant_id,
                                           attribute_id = gd.fk_attribute_id
                                       }).ToListAsync();
                if (chapterIds != null && chapterIds.Any())
                {
                    goalDistrList = goalDistrList.Where(x => chapterIds.Contains(x.attribute_id)).ToList();
                }
                foreach (var goal in goalDistrList)
                {
                    var goalData = GetGoalStatusData(tmrCityGoalsData, goal.goalId, goal.goalDistrId);
                    goal.statusGoalId = goalData.Status != null ? goalData.Status.Value : 0;
                    goal.statusGoal = goalData.Status != null && goalData.Status != 0 ? statusData.FirstOrDefault(x => x.status_id == goalData.Status.Value).status_description : "";
                    goal.statusGoalDescription = goalData.StatusDesc ?? "";
                }
            }

            return goalDistrList;
        }

        public async Task<List<TargetnDistrFth>> FetchTargetDistrPerfAsync(int tenantId, int budgetYear, string serviceId, List<string> chapterIds = null)
        {
            List<TargetnDistrFth> targetDistrList;
            TenantDBContext dbContext1 = await _utility.GetTenantDbContextForParallelReadAsync();
            var tmrTargetStatusData = dbContext1.tmr_effect_target_status.Where(x => x.FkTenantId == tenantId && x.ForecastPeriod.ToString().StartsWith(budgetYear.ToString())).ToList();

            TenantDBContext dbContext2 = await _utility.GetTenantDbContextForParallelReadAsync();
            var statusData = dbContext2.tco_progress_status.Where(x => x.fk_tenant_id == tenantId && x.type == "MONTHREP_GOAL" && x.active == 1).ToList();

            if (serviceId != null && (serviceId == "ALL" || serviceId == "-1"))
            {
                TenantDBContext dbContext = await _utility.GetTenantDbContextForParallelReadAsync();
                targetDistrList = await (from td in dbContext.tco_targets_distribution
                                         join tt in dbContext.tco_targets on new { tnt = td.fk_tenant_id, tgt = td.fk_target_id }
                                                                      equals new { tnt = tt.fk_tenant_id, tgt = tt.pk_target_id }
                                         where tt.budget_year == budgetYear
                                            && tt.fk_tenant_id == tenantId
                                         select new TargetnDistrFth
                                         {
                                             targetDistrId = td.pk_target_distribution_id,
                                             targetId = tt.pk_target_id,
                                             goalId = tt.fk_goal_id,
                                             target_name = tt.target_name,
                                             unsd_target = tt.unsd_target,
                                             unsd_goal = tt.unsd_goal,
                                             org_created = tt.org_created,
                                             org_level_created = tt.org_level_created ?? 0,
                                             is_busplan_target = tt.is_busplan_target,
                                             org_id = td.org_id,
                                             org_level = td.org_level ?? 0,
                                             service_id = td.service_id.Trim().ToUpper() == "ALL" || td.service_id.Trim() == "" ? "-1" : td.service_id.Trim(),
                                             service_level = td.service_level ?? 0,
                                             tags = td.tags,
                                             plan_target_text = td.plan_target_text,
                                             sort_order = tt.sort_order,
                                             unsdgoal_target = tt.unsd_goal,
                                             attribute_id = td.fk_attribute_id
                                         }).ToListAsync();
                if (chapterIds != null && chapterIds.Any())
                {
                    targetDistrList = targetDistrList.Where(x => chapterIds.Contains(x.attribute_id)).ToList();
                }
                foreach (var target in targetDistrList)
                {
                    var targetData = GetTargetStatusData(tmrTargetStatusData, target.targetId, target.targetDistrId);
                    target.statusTargetId = targetData.Status != null ? targetData.Status.Value : 0;
                    target.statusTarget = targetData.Status != null && targetData.Status != 0 ? statusData.FirstOrDefault(x => x.status_id == targetData.Status.Value).status_description : "";
                    target.statusTargetDesc = targetData.target_status_desc ?? "";
                }
            }
            else
            {
                TenantDBContext dbContext = await _utility.GetTenantDbContextForParallelReadAsync();
                targetDistrList = await (from td in dbContext.tco_targets_distribution
                                         join tt in dbContext.tco_targets on new { tnt = td.fk_tenant_id, tgt = td.fk_target_id }
                                                                      equals new { tnt = tt.fk_tenant_id, tgt = tt.pk_target_id }
                                         where tt.budget_year == budgetYear
                                            && tt.fk_tenant_id == tenantId
                                         select new TargetnDistrFth
                                         {
                                             targetDistrId = td.pk_target_distribution_id,
                                             targetId = tt.pk_target_id,
                                             goalId = tt.fk_goal_id,
                                             target_name = tt.target_name,
                                             unsd_target = tt.unsd_target,
                                             unsd_goal = tt.unsd_goal,
                                             org_created = tt.org_created,
                                             org_level_created = tt.org_level_created ?? 0,
                                             is_busplan_target = tt.is_busplan_target,
                                             org_id = td.org_id,
                                             org_level = td.org_level ?? 0,
                                             service_id = td.service_id.Trim().ToUpper() == "ALL" || td.service_id.Trim() == "" ? "-1" : td.service_id.Trim(),
                                             service_level = td.service_level ?? 0,
                                             tags = td.tags,
                                             plan_target_text = td.plan_target_text,
                                             sort_order = tt.sort_order,
                                             unsdgoal_target = tt.unsd_goal,
                                             attribute_id = td.fk_attribute_id
                                         }).ToListAsync();
                if (chapterIds != null && chapterIds.Any())
                {
                    targetDistrList = targetDistrList.Where(x => chapterIds.Contains(x.attribute_id)).ToList();
                }
                foreach (var target in targetDistrList)
                {
                    var targetData = GetTargetStatusData(tmrTargetStatusData, target.targetId, target.targetDistrId);
                    target.statusTargetId = targetData.Status != null ? targetData.Status.Value : 0;
                    target.statusTarget = targetData.Status != null && targetData.Status != 0 ? statusData.FirstOrDefault(x => x.status_id == targetData.Status.Value).status_description : "";
                    target.statusTargetDesc = targetData.target_status_desc ?? "";
                }
            }

            return targetDistrList;
        }

        public async Task<Guid> InsertIntoStrategyHeaderAsync(int tenantId, int updatedBy)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            var headerId = Guid.NewGuid();
            tfp_strategy_header gh = new tfp_strategy_header
            {
                pk_strategy_header_Id = headerId,
                fk_tenant_id = tenantId,
                updated = DateTime.UtcNow,
                updated_by = updatedBy
            };
            await dbContext.tfp_strategy_header.AddAsync(gh);
            await dbContext.SaveChangesAsync();
            return headerId;
        }
    }
}