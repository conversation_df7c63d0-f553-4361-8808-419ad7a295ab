using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text.RegularExpressions;
using KeyValuePair = Framsikt.BL.Helpers.KeyValuePair;

namespace Framsikt.BL;

public partial class BudgetManagement
{


    public async Task<List<clsBMactionProposalsFilterTypes>> GetActionProposalsFilterTypesAsync(string userId, int budgetYear)
    {
        TenantDBContext DbContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod((budgetYear), 1));

        Dictionary<string, clsLanguageString> langStringValuesBudgetManagement = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetManagement");
        Dictionary<string, clsLanguageString> langStringValuesCommon = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Common");

        List<clsOrgStructure> lstOrgStructure = await _utility.GetTenantOrgStructureAsync(orgVersionContent, userId);

        clsOrgStructureLevelDetails tenantOrgLevelDetails = _utility.GetTenantOrgLevelDetails(lstOrgStructure);

        List<clsBMactionProposalsFilterTypes> lstFilters = new List<clsBMactionProposalsFilterTypes>();

        clsBMactionProposalsFilterTypes filter = null;

        if (!string.IsNullOrEmpty(tenantOrgLevelDetails.level1))
        {
            filter = new clsBMactionProposalsFilterTypes()
            {
                ColName = "Org_Level_1",
                DisplayName = ((langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == "BM_Action_Proposals_Filter_1")).Value).LangText
            };
            lstFilters.Add(filter);
        }

        if (!string.IsNullOrEmpty(tenantOrgLevelDetails.level2))
        {
            filter = new clsBMactionProposalsFilterTypes()
            {
                ColName = "Org_Level_2",
                DisplayName = ((langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == "BM_Action_Proposals_Filter_2")).Value).LangText
            };
            lstFilters.Add(filter);
        }

        filter = new clsBMactionProposalsFilterTypes()
        {
            ColName = "Action_type",
            DisplayName = ((langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == "BM_Action_Proposals_Filter_3")).Value).LangText
        };
        lstFilters.Add(filter);

        filter = new clsBMactionProposalsFilterTypes()
        {
            ColName = "Alter_Code",
            DisplayName = ((langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == "BM_Action_Proposals_Filter_4")).Value).LangText
        };
        lstFilters.Add(filter);

        filter = new clsBMactionProposalsFilterTypes()
        {
            ColName = "Adjustment_Code",
            DisplayName = ((langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == "BM_Action_Proposals_Filter_5")).Value).LangText
        };
        lstFilters.Add(filter);

        filter = new clsBMactionProposalsFilterTypes()
        {
            ColName = "Priority",
            DisplayName = ((langStringValuesCommon.FirstOrDefault(v => v.Key == "cmn_action_priority_text")).Value).LangText
        };
        lstFilters.Add(filter);

        filter = new clsBMactionProposalsFilterTypes()
        {
            ColName = "Tag",
            DisplayName = ((langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == "BM_Action_Proposals_Filter_tags")).Value).LangText
        };
        lstFilters.Add(filter);

        filter = new clsBMactionProposalsFilterTypes()
        {
            ColName = "limitCode",
            DisplayName = ((langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == "BM_Action_Proposals_Filter_limitCode")).Value).LangText
        };
        lstFilters.Add(filter);

        return lstFilters;
    }



    public async Task<FilterColumnHelper> GetActionProposalsFilterValuesAsync(string userId, int budgetYear, clsBMactionProposalsPopulateDropdowns populateDropdown)
    {
        TenantDBContext DbContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));

        FilterColumnHelper data = new FilterColumnHelper();
        data.filterColumns = new Dictionary<int, List<KeyValuePair>>();

        List<clsOrgStructure> lstOrgStructure = await _utility.GetTenantOrgStructureAsync(orgVersionContent, userId);
        clsOrgStructureLevelDetails tenantOrgLevelDetails = _utility.GetTenantOrgLevelDetails(lstOrgStructure);

        List<tco_functions> lstTcoFunctions = await DbContext.tco_functions.Where(x => x.pk_tenant_id == userDetails.tenant_id).ToListAsync();

        int count = -1;
        foreach (string key in populateDropdown.key)
        {
            count++;
            if (key == "Org_Level_1")
            {
                List<KeyValuePair> dataset = (from d in lstOrgStructure
                    where d.type == "FINPLAN_LEVEL_1"
                    select new KeyValuePair
                    {
                        key = d.id,
                        value = d.name,
                        isChecked = false
                    }).ToList();
                List<KeyValuePair> resultSet = new List<KeyValuePair>();
                foreach (KeyValuePair d in dataset)
                {
                    if (resultSet.FirstOrDefault(x => x.key == d.key) == null)
                    {
                        resultSet.Add(d);
                    }
                }
                data.filterColumns.Add(count, resultSet.OrderBy(x => x.key).ToList());
            }
            else if (key == "Org_Level_2")
            {
                List<KeyValuePair> dataset = (from d in lstOrgStructure
                    where d.type == "FINPLAN_LEVEL_2"
                    select new KeyValuePair
                    {
                        key = d.id,
                        value = d.name,
                        isChecked = false
                    }).ToList();
                List<KeyValuePair> resultSet = new List<KeyValuePair>();
                foreach (KeyValuePair d in dataset)
                {
                    if (resultSet.FirstOrDefault(x => x.key == d.key) == null)
                    {
                        resultSet.Add(d);
                    }
                }
                data.filterColumns.Add(count, resultSet.OrderBy(x => x.value).ToList());
            }
            else if (key == "Action_type")
            {
                List<KeyValuePair> actionTypes = await (from a in DbContext.gmd_action_types
                    where ((a.pk_action_type == 30 || a.pk_action_type == 31 || a.pk_action_type == 40 || a.pk_action_type == 41) && a.pk_language == userDetails.language_preference)
                    select new KeyValuePair
                    {
                        key = a.pk_action_type.ToString(),
                        value = (a.pk_action_type + "-" + a.action_type_descr),
                        isChecked = false
                    }).ToListAsync();
                data.filterColumns.Add(count, actionTypes);
            }
            else if (key == "Alter_Code")
            {
                List<KeyValuePair> alterCodes = await _consequentAdjustedBudget.GetAlterCodesListAsync(userId, new List<int>() { 30, 31, 40, 41 });
                data.filterColumns.Add(count, alterCodes);
            }
            else if (key == "Adjustment_Code")
            {
                List<KeyValuePair> alterCodes = await _consequentAdjustedBudget.GetAdjustmentCodesListAsync(userId);
                data.filterColumns.Add(count, alterCodes);
            }
            else if (key == "Priority")
            {
                var priorityData = await (from th in DbContext.tfp_trans_header
                    where th.fk_tenant_id == userDetails.tenant_id
                    select new KeyValuePair
                    {
                        key = th.priority.Value.ToString(),
                        value = th.priority.Value.ToString(),
                        isChecked = false
                    }).Distinct().ToListAsync();
                data.filterColumns.Add(count, priorityData);
            }
            else if (key == "Tag")
            {
                var tagData = await (from td in DbContext.tcoActionTags
                    where td.FkTenantId == userDetails.tenant_id
                    select new KeyValuePair
                    {
                        key = td.PkId.ToString(),
                        value = td.TagDescription
                    }).Distinct().ToListAsync();
                data.filterColumns.Add(count, tagData);
            }
            else if (key == "limitCode")
            {
                var tagData = await (from td in DbContext.tco_fp_alter_codes
                    where td.fk_tenant_id == userDetails.tenant_id
                    select new KeyValuePair
                    {
                        key = td.limit_code.ToString(),
                        value = td.limit_description
                    }).Distinct().ToListAsync();
                data.filterColumns.Add(count, tagData);
            }
        }
        return data;
    }



    public List<ReportColumnHelper> GetSimulatorFilterColumns(string userId, int budgetYear)
    {
        return GetSimulatorFilterColumnsAsync(userId, budgetYear).GetAwaiter().GetResult();
    }



    public async Task<List<ReportColumnHelper>> GetSimulatorFilterColumnsAsync(string userId, int budgetYear)
    {
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringFinPlanReport = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetManagement");

        List<ReportColumnHelper> reportColumns = new List<ReportColumnHelper>();
        ReportColumnHelper r1 = new ReportColumnHelper
        {
            ColName = "priority",
            DataType = "numeric",
            DisplayName = langStringFinPlanReport.FirstOrDefault(x => x.Key.Equals("BM_simulation_Filter_col1")).Value.LangText
        };
        reportColumns.Add(r1);
        Dictionary<string, string> orgLevelValue = await _utility.GetOrglevelsAsync(userId);
        var orgDisplayName = await GetLevelNameForFilter(userId, budgetYear);
        if (orgLevelValue.Count == 1)
        {
            ReportColumnHelper rch = new ReportColumnHelper { ColName = "orgId", DisplayName = orgDisplayName[0], DataType = "string" };
            reportColumns.Add(rch);
        }
        else
        {
            ReportColumnHelper rch = new ReportColumnHelper { ColName = "orgId", DisplayName = orgDisplayName[0], DataType = "string" };
            reportColumns.Add(rch);
            rch = new ReportColumnHelper { ColName = "serviceId", DisplayName = orgDisplayName[1], DataType = "string" };
            reportColumns.Add(rch);
        }
        ReportColumnHelper rchtag = new ReportColumnHelper { ColName = "tags", DisplayName = ((langStringFinPlanReport.FirstOrDefault(v => v.Key == "BM_Action_Tags_Filter")).Value).LangText, DataType = "string" };
        reportColumns.Add(rchtag);
        ReportColumnHelper rchaction = new ReportColumnHelper { ColName = "action", DisplayName = ((langStringFinPlanReport.FirstOrDefault(v => v.Key == "BM_Action_Action_Filter")).Value).LangText, DataType = "numeric" };
        reportColumns.Add(rchaction);
        return reportColumns;
    }



    public async Task<FilterColumnHelper> SimulatorFilterDataAsync(string userId, List<string> key, int budgetYear, int? orgLevel = null, string orgId = null, string chapterId = null)
    {
        TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        Dictionary<int, List<PriorityFilter>> priorityFilter = new Dictionary<int, List<PriorityFilter>>();
        Dictionary<int, List<KeyValuePair>> filterValues = new Dictionary<int, List<KeyValuePair>>();
        var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));
        var relationDepartments = new List<string>();
        if (!chapterId.IsNullOrEmpty())
        {
            relationDepartments = await _finUtility.GetAttributeConnectedDepartmentsForSelectedOrgAsync(userId, budgetYear, orgId, (int)orgLevel, chapterId);
        }
        int counter = 0;
        if (key != null)
        {
            List<clsOrgStructure> lstOrgStructure = await _utility.GetTenantOrgStructureAsync(orgVersionContent, userId);
            foreach (var item in key)
            {
                switch (item)
                {
                    case "priority":
                        var priorityData = await (from th in dbContext.tfp_trans_header
                            where th.fk_tenant_id == userDetails.tenant_id
                                  && th.priority != null
                            select new PriorityFilter
                            {
                                key = th.priority,
                                value = th.priority
                            }).Distinct().OrderBy(x => x.value).ToListAsync();
                        priorityFilter.Add(counter, priorityData);
                        counter++;
                        break;

                    case "orgId":
                        List<KeyValuePair> filterData = (from os in lstOrgStructure
                            where os.type == "FINPLAN_LEVEL_1"
                            select new KeyValuePair
                            {
                                key = os.id,
                                value = os.name
                            }).OrderBy(x => x.value).ToList();
                        List<KeyValuePair> uniqueOrgId = new List<KeyValuePair>();
                        foreach (KeyValuePair serviceId in filterData)
                        {
                            if (uniqueOrgId.FirstOrDefault(x => x.key == serviceId.key) == null)
                            {
                                uniqueOrgId.Add(serviceId);
                            }
                        }
                        filterValues.Add(counter, uniqueOrgId);
                        counter++;
                        break;

                    case "serviceId":
                        List<KeyValuePair> filterDataServiceId = (from os in lstOrgStructure
                            where os.type == "FINPLAN_LEVEL_2"
                            select new KeyValuePair
                            {
                                key = os.id,
                                value = os.name,
                                isChecked = false
                            }).OrderBy(x => x.value).ToList();
                        List<KeyValuePair> uniqueServiceId = new List<KeyValuePair>();
                        foreach (KeyValuePair serviceId in filterDataServiceId)
                        {
                            if (uniqueServiceId.FirstOrDefault(x => x.key == serviceId.key) == null)
                            {
                                uniqueServiceId.Add(serviceId);
                            }
                        }
                        filterValues.Add(counter, uniqueServiceId);
                        counter++;
                        break;

                    case "tags":
                        List<KeyValuePair> tags = await (from td in dbContext.tcoActionTags
                            where td.FkTenantId == userDetails.tenant_id
                            select new KeyValuePair
                            {
                                key = td.PkId.ToString(),
                                value = td.TagDescription
                            }).Distinct().ToListAsync();
                        filterValues.Add(counter, tags);
                        counter++;
                        break;

                    case "action":
                        List<KeyValuePair> actions;
                        if (string.IsNullOrEmpty(orgId))
                        {
                            actions = await (from a in dbContext.tfp_trans_detail
                                join d in dbContext.tfp_trans_header on new { a = a.fk_action_id, b = a.fk_tenant_id }
                                    equals new { a = d.pk_action_id, b = d.fk_tenant_id }
                                where a.fk_tenant_id == userDetails.tenant_id
                                      && a.budget_year == budgetYear
                                      && new List<int> { 31, 41 }.Contains(d.action_type)
                                select new KeyValuePair
                                {
                                    key = d.pk_action_id.ToString(),
                                    value = d.description
                                }).ToListAsync();
                        }
                        else
                        {
                            actions = await filterSimulatedActionFilterForSelectedOrgAsync(userDetails.tenant_id, budgetYear, orgId, orgLevel == null ? 0 : orgLevel.Value, orgVersionContent.orgVersion, relationDepartments);
                        }

                        List<KeyValuePair> uniqueactionId = new List<KeyValuePair>();
                        foreach (KeyValuePair ac in actions)
                        {
                            if (uniqueactionId.FirstOrDefault(x => x.key == ac.key) == null)
                            {
                                uniqueactionId.Add(ac);
                            }
                        }

                        filterValues.Add(counter, uniqueactionId);
                        counter++;
                        break;
                }
            }
        }
        FilterColumnHelper finalFilterData = new FilterColumnHelper { simulatorFilter = priorityFilter, filterColumns = filterValues };
        return finalFilterData;
    }



    private async Task<List<KeyValuePair>> filterSimulatedActionFilterForSelectedOrgAsync(int tenantId, int budgetYear, string orgId, int orgLevel, string orgVersion, List<string> departmentsList)
    {
        List<KeyValuePair> result;
        TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
        if (orgLevel == 1)
        {
            result = await (from a in dbContext.tfp_trans_detail
                join d in dbContext.tfp_trans_header on new { a = a.fk_action_id, b = a.fk_tenant_id }
                    equals new { a = d.pk_action_id, b = d.fk_tenant_id }
                join e in dbContext.tco_org_hierarchy on new { a = a.fk_tenant_id, b = a.department_code }
                    equals new { a = e.fk_tenant_id, b = e.fk_department_code }
                where a.fk_tenant_id == tenantId
                      && a.budget_year == budgetYear
                      && new List<int> { 31, 41 }.Contains(d.action_type)
                      && e.org_id_1 == orgId
                      && e.fk_org_version == orgVersion
                select new KeyValuePair
                {
                    key = d.pk_action_id.ToString(),
                    value = d.description,
                    //using orgId parameter for departmentcode
                    orgId = a.department_code
                }).ToListAsync();
        }
        else if (orgLevel == 2)
        {
            result = await (from a in dbContext.tfp_trans_detail
                join d in dbContext.tfp_trans_header on new { a = a.fk_action_id, b = a.fk_tenant_id }
                    equals new { a = d.pk_action_id, b = d.fk_tenant_id }
                join e in dbContext.tco_org_hierarchy on new { a = a.fk_tenant_id, b = a.department_code }
                    equals new { a = e.fk_tenant_id, b = e.fk_department_code }
                where a.fk_tenant_id == tenantId
                      && a.budget_year == budgetYear
                      && new List<int> { 31, 41 }.Contains(d.action_type)
                      && e.org_id_2 == orgId
                      && e.fk_org_version == orgVersion
                select new KeyValuePair
                {
                    key = d.pk_action_id.ToString(),
                    value = d.description,
                    //using orgId parameter for departmentcode
                    orgId = a.department_code
                }).ToListAsync();
        }
        else if (orgLevel == 3)
        {
            result = await (from a in dbContext.tfp_trans_detail
                join d in dbContext.tfp_trans_header on new { a = a.fk_action_id, b = a.fk_tenant_id }
                    equals new { a = d.pk_action_id, b = d.fk_tenant_id }
                join e in dbContext.tco_org_hierarchy on new { a = a.fk_tenant_id, b = a.department_code }
                    equals new { a = e.fk_tenant_id, b = e.fk_department_code }
                where a.fk_tenant_id == tenantId
                      && a.budget_year == budgetYear
                      && new List<int> { 31, 41 }.Contains(d.action_type)
                      && e.org_id_3 == orgId
                      && e.fk_org_version == orgVersion
                select new KeyValuePair
                {
                    key = d.pk_action_id.ToString(),
                    value = d.description,
                    //using orgId parameter for departmentcode
                    orgId = a.department_code
                }).ToListAsync();
        }
        else if (orgLevel == 4)
        {
            result = await (from a in dbContext.tfp_trans_detail
                join d in dbContext.tfp_trans_header on new { a = a.fk_action_id, b = a.fk_tenant_id }
                    equals new { a = d.pk_action_id, b = d.fk_tenant_id }
                join e in dbContext.tco_org_hierarchy on new { a = a.fk_tenant_id, b = a.department_code }
                    equals new { a = e.fk_tenant_id, b = e.fk_department_code }
                where a.fk_tenant_id == tenantId
                      && a.budget_year == budgetYear
                      && new List<int> { 31, 41 }.Contains(d.action_type)
                      && e.org_id_4 == orgId
                      && e.fk_org_version == orgVersion
                select new KeyValuePair
                {
                    key = d.pk_action_id.ToString(),
                    value = d.description,
                    //using orgId parameter for departmentcode
                    orgId = a.department_code
                }).ToListAsync();
        }
        else
        {
            result = await (from a in dbContext.tfp_trans_detail
                join d in dbContext.tfp_trans_header on new { a = a.fk_action_id, b = a.fk_tenant_id }
                    equals new { a = d.pk_action_id, b = d.fk_tenant_id }
                join e in dbContext.tco_org_hierarchy on new { a = a.fk_tenant_id, b = a.department_code }
                    equals new { a = e.fk_tenant_id, b = e.fk_department_code }
                where a.fk_tenant_id == tenantId
                      && a.budget_year == budgetYear
                      && new List<int> { 31, 41 }.Contains(d.action_type)
                      && e.org_id_5 == orgId
                      && e.fk_org_version == orgVersion
                select new KeyValuePair
                {
                    key = d.pk_action_id.ToString(),
                    value = d.description,
                    //using orgId parameter for departmentcode
                    orgId = a.department_code
                }).ToListAsync();
        }
        if (departmentsList.Any())
        {
            result = result.Where(x => departmentsList.Contains(x.orgId)).ToList();
        }
        return result;
    }



    private async Task<DataAfterApplyingSimulation> ApplySimulationAsync(string userId, SimulatorHelper simulatorData, List<ConsequenceOverViewHelper> dataToFilter, int budgetYear, List<int> lineGroup, List<string> conditionFunctionCodes, List<ConsequenceOverViewHelper> queryDataWithNegative)
    {
        TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        List<ConsequenceOverViewHelper> blistdata = new List<ConsequenceOverViewHelper>();
        List<ConsequenceOverViewHelper> Querydata = new List<ConsequenceOverViewHelper>();
        List<ConsequenceOverViewHelper> nonFilteredActionData = new List<ConsequenceOverViewHelper>();
        List<ConsequenceOverViewHelper> lstNewActionData = new List<ConsequenceOverViewHelper>();
        var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));
        if (simulatorData.BListActions.Any())
        {
            blistdata = await (from a in dbContext.tfp_temp_detail
                join d in dbContext.tfp_temp_header on a.fk_temp_id equals d.pk_temp_id
                join b in dbContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code }
                    equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                join c in dbContext.gmd_reporting_line on b.fk_kostra_account_code equals c.fk_kostra_account_code
                where (a.fk_tenant_id == userDetails.tenant_id
                       && a.budget_year == budgetYear && simulatorData.BListActions.Contains(a.fk_temp_id) && c.report == "54_OVDRIFT")
                select new ConsequenceOverViewHelper
                {
                    actionId = a.pk_id,
                    line_group_id = c.line_group_id,
                    Year1 = a.year_1_amount,
                    Year2 = a.year_2_amount,
                    Year3 = a.year_3_amount,
                    Year4 = a.year_4_amount,
                    ActionType = d.action_type,
                    Priority = d.priority,
                    tags = d.tags,
                    line_group = c.line_group,
                    FunctionCode = a.function_code,
                    departmentCode = a.department_code
                }).ToListAsync();

            if (conditionFunctionCodes.Any())
            {
                blistdata = blistdata.Where(a => conditionFunctionCodes.Contains(a.FunctionCode)).ToList();
            }
        }
        if (simulatorData.IncludeDeletedAction)
        {
            Querydata = await (from a in dbContext.tfp_delete_detail
                join d in dbContext.tfp_delete_header on new { a = a.fk_tenant_id, b = a.fk_action_id }
                    equals new { a = d.fk_tenant_id, b = d.pk_action_id }
                join b in dbContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code }
                    equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                join c in dbContext.gmd_reporting_line on b.fk_kostra_account_code equals c.fk_kostra_account_code
                where (a.fk_tenant_id == userDetails.tenant_id
                       && a.budget_year == budgetYear
                       && lineGroup.Contains(c.line_group_id) && c.report == "54_OVDRIFT")
                //group a by new { c.line_group, c.line_group_id } into g
                select new ConsequenceOverViewHelper
                {
                    actionId = a.pk_id,
                    line_group_id = c.line_group_id,
                    Year1 = a.year_1_amount,
                    Year2 = a.year_2_amount,
                    Year3 = a.year_3_amount,
                    Year4 = a.year_4_amount,
                    ActionType = d.action_type,
                    Priority = d.priority,
                    tags = d.tags,
                    line_group = c.line_group,
                    FunctionCode = a.function_code,
                    departmentCode = a.department_code
                }).ToListAsync();
            if (conditionFunctionCodes.Any())
            {
                Querydata = Querydata.Where(a => conditionFunctionCodes.Contains(a.FunctionCode)).ToList();
            }

            nonFilteredActionData = Querydata.Concat(nonFilteredActionData).ToList();
        }
        if (simulatorData.IncludeNonAllocatedCostReductionNewPrtsOngoing)
        {
            var lstCostRExt = queryDataWithNegative.Where(x => x.ActionType == 30).ToList();
            nonFilteredActionData = nonFilteredActionData.Where(x => x.ActionType != 30).ToList();
            lstNewActionData.AddRange(lstCostRExt);

            var lstNewActionExt = queryDataWithNegative.Where(x => x.ActionType == 40).ToList();
            nonFilteredActionData = nonFilteredActionData.Where(x => x.ActionType != 40).ToList();
            lstNewActionData.AddRange(lstNewActionExt);

            var lstOnGoing = queryDataWithNegative.Where(x => x.ActionType == 90).ToList();
            lstNewActionData.AddRange(lstOnGoing);
            nonFilteredActionData = nonFilteredActionData.Where(x => x.ActionType != 90).ToList();
        }

        if (simulatorData.ExcludeCostReductionNewPrtsOngoing)
        {
            var lstCostRExt = queryDataWithNegative.Where(x => x.ActionType == 31).ToList();
            nonFilteredActionData = nonFilteredActionData.Where(x => x.ActionType != 31).ToList();
            lstNewActionData.AddRange(lstCostRExt);

            var lstNewActionExt = queryDataWithNegative.Where(x => x.ActionType == 41).ToList();
            nonFilteredActionData = nonFilteredActionData.Where(x => x.ActionType != 41).ToList();
            lstNewActionData.AddRange(lstNewActionExt);
        }
        nonFilteredActionData.AddRange(blistdata);
        if (simulatorData.FilterColumn != null)
        {
            List<clsOrgStructure> lstOrgStructure = await _utility.GetTenantOrgStructureAsync(orgVersionContent, userId);
            clsOrgStructureLevelDetails tenantOrgLevelDetails = _utility.GetTenantOrgLevelDetails(lstOrgStructure);
            int counter = 0;
            foreach (var item in simulatorData.FilterColumn)
            {
                switch (item)
                {
                    case "tags":
                        List<string> tagsVal = new List<string>();
                        foreach (var conData in simulatorData.ConditionValues[counter])
                        {
                            tagsVal.Add(conData);
                        }
                        lstNewActionData = lstNewActionData.Where(x => x.tags != null && x.tags.Split(',').Intersect(tagsVal).Contains(x.tags)).ToList();
                        nonFilteredActionData = nonFilteredActionData.Where(x => x.tags != null && x.tags.Split(',').Intersect(tagsVal).Contains(x.tags)).ToList();
                        break;

                    case "action":
                        List<string> actionVal = new List<string>();
                        foreach (var conData in simulatorData.ConditionValues[counter])
                        {
                            actionVal.Add(conData);
                        }
                        lstNewActionData = lstNewActionData.Where(x => actionVal.Contains(x.FkactionId.ToString())).ToList();
                        nonFilteredActionData = nonFilteredActionData.Where(x => actionVal.Contains(x.actionId.ToString())).ToList();
                        break;

                    case "priority":
                        List<int?> conditionVal = new List<int?>();
                        foreach (var conData in simulatorData.ConditionValues[counter])
                        {
                            int i;
                            if (int.TryParse(conData, out i))
                            { conditionVal.Add(i); }
                            else
                            {
                                conditionVal.Add(null);
                            }
                        }
                        lstNewActionData = lstNewActionData.Where(x => conditionVal.Contains(x.Priority)).ToList();
                        nonFilteredActionData = nonFilteredActionData.Where(x => conditionVal.Contains(x.Priority)).ToList();
                        break;

                    case "orgId":
                        List<string> orgIds = simulatorData.ConditionValues[counter];
                        List<List<string>> DepartmentsAndFunctions = new List<List<string>>();
                        foreach (var orgIdItem in orgIds)
                        {
                            List<List<string>> DepartmentsAndFunctionsForEachOrg = _utility.GetDepartmentsAndFunctionsForOrgStructure(lstOrgStructure, orgIdItem, null, tenantOrgLevelDetails.level1, tenantOrgLevelDetails.level2);
                            DepartmentsAndFunctions.AddRange(DepartmentsAndFunctionsForEachOrg);
                        }

                        List<string> lstAllDepartments = DepartmentsAndFunctions[0];
                        List<string> lstAllFunctions = DepartmentsAndFunctions[1];

                        if (lstAllDepartments.Any() && lstAllFunctions.Any())
                        {
                            lstNewActionData = lstNewActionData.Where(x => lstAllFunctions.Contains(x.FunctionCode) && lstAllDepartments.Contains(x.departmentCode)).ToList();
                            nonFilteredActionData = nonFilteredActionData.Where(x => lstAllFunctions.Contains(x.FunctionCode) && lstAllDepartments.Contains(x.departmentCode)).ToList();
                        }
                        else if (lstAllDepartments.Any())
                        {
                            lstNewActionData = lstNewActionData.Where(x => lstAllDepartments.Contains(x.departmentCode)).ToList();
                            nonFilteredActionData = nonFilteredActionData.Where(x => lstAllDepartments.Contains(x.departmentCode)).ToList();
                        }
                        else if (lstAllFunctions.Any())
                        {
                            lstNewActionData = lstNewActionData.Where(x => lstAllFunctions.Contains(x.FunctionCode)).ToList();
                            nonFilteredActionData = nonFilteredActionData.Where(x => lstAllFunctions.Contains(x.FunctionCode)).ToList();
                        }
                        break;

                    case "serviceId":
                        List<string> serviceIds = simulatorData.ConditionValues[counter];
                        List<List<string>> DepartmentsAndFunctionsSids = new List<List<string>>();
                        foreach (var serviceIdItem in serviceIds)
                        {
                            List<List<string>> DepartmentsAndFunctionsForEachOrg = _utility.GetDepartmentsAndFunctionsForOrgStructure(lstOrgStructure, null, serviceIdItem, tenantOrgLevelDetails.level1, tenantOrgLevelDetails.level2);
                            if (DepartmentsAndFunctionsSids.Any())
                            {
                                DepartmentsAndFunctionsSids[0].AddRange(DepartmentsAndFunctionsForEachOrg[0]);
                                DepartmentsAndFunctionsSids[1].AddRange(DepartmentsAndFunctionsForEachOrg[1]);
                            }
                            else
                            {
                                DepartmentsAndFunctionsSids.AddRange(DepartmentsAndFunctionsForEachOrg);
                            }
                        }

                        List<string> lstAllDepartmentsSids = DepartmentsAndFunctionsSids[0];
                        List<string> lstAllFunctionsSids = DepartmentsAndFunctionsSids[1];

                        if (lstAllDepartmentsSids.Any() && lstAllFunctionsSids.Any())
                        {
                            lstNewActionData = lstNewActionData.Where(x => lstAllFunctionsSids.Contains(x.FunctionCode) && lstAllDepartmentsSids.Contains(x.departmentCode)).ToList();
                            nonFilteredActionData = nonFilteredActionData.Where(x => lstAllFunctionsSids.Contains(x.FunctionCode) && lstAllDepartmentsSids.Contains(x.departmentCode)).ToList();
                        }
                        else if (lstAllDepartmentsSids.Any())
                        {
                            lstNewActionData = lstNewActionData.Where(x => lstAllDepartmentsSids.Contains(x.departmentCode)).ToList();
                            nonFilteredActionData = nonFilteredActionData.Where(x => lstAllDepartmentsSids.Contains(x.departmentCode)).ToList();
                        }
                        else if (lstAllFunctionsSids.Any())
                        {
                            lstNewActionData = lstNewActionData.Where(x => lstAllFunctionsSids.Contains(x.FunctionCode)).ToList();
                            nonFilteredActionData = nonFilteredActionData.Where(x => lstAllFunctionsSids.Contains(x.FunctionCode)).ToList();
                        }
                        break;
                }
                counter++;
            }
        }
        dataToFilter = dataToFilter.Concat(nonFilteredActionData).ToList();
        if (lstNewActionData.Any())
        {
            dataToFilter = (from qn in dataToFilter
                join df in lstNewActionData on qn.actionId equals df.actionId into G1
                from lQn in G1.DefaultIfEmpty()
                select new ConsequenceOverViewHelper
                {
                    line_group_id = qn.line_group_id,
                    Year1 = lQn == null ? qn.Year1 : lQn.Year1 + qn.Year1,
                    Year2 = lQn == null ? qn.Year2 : lQn.Year2 + qn.Year2,
                    Year3 = lQn == null ? qn.Year3 : lQn.Year3 + qn.Year3,
                    Year4 = lQn == null ? qn.Year4 : lQn.Year4 + qn.Year4,
                    ActionType = qn.ActionType,
                    Priority = qn.Priority,
                    tags = qn.tags,
                    line_group = qn.line_group,
                    FunctionCode = qn.FunctionCode,
                    departmentCode = qn.departmentCode
                }).ToList();
        }
        return new DataAfterApplyingSimulation() { dataToFilter = dataToFilter, bListData = blistdata };
    }



    public async Task<string> GetActionDetailsOnMouseoverAsync(string userId, int id, string actionDataType, int budgetYear)
    {
        UserData userdata = await _utility.GetUserDetailsAsync(userId);
        int budgetyear = budgetYear;
        TenantDBContext DBContext = await _utility.GetTenantDBContextAsync();
        Dictionary<string, clsLanguageString> langStringValuesMonRep = await _utility.GetLanguageStringsAsync(userdata.language_preference, userdata.user_name, "MonthlyReport");

        string description = ((langStringValuesMonRep.FirstOrDefault(v => v.Key == "MR_ActionStatus_Description")).Value).LangText;

        var data = await (from s in DBContext.tfp_trans_header
            select new
            {
                lastchanged = "",
                actiondescription = "",
                financialplan = "",
                actionconsequence = ""
            }).FirstOrDefaultAsync();

        if (actionDataType == clsConstants.ActionDataType.TransData.ToString())
        {
            data = await (from s in DBContext.tfp_trans_header
                join tu in DBContext.vwUserDetails on s.updated_by equals tu.pk_id
                where s.pk_action_id == id && s.fk_tenant_id == userdata.tenant_id
                select new
                {
                    //lastchanged = tu.first_name + " " + tu.last_name + " " + SqlFunctions.DatePart("dd", s.updated) + "." + SqlFunctions.DatePart("mm", s.updated) + "." + SqlFunctions.DatePart("yyyy", s.updated),
                    lastchanged = tu.first_name + " " + tu.last_name + " " + s.updated.ToString("dd") + "." + s.updated.ToString("mm") + "." + s.updated.ToString("yyyy"),
                    actiondescription = s.long_description.Trim(),
                    financialplan = s.financial_plan_description.Trim(),
                    actionconsequence = s.consequence.Trim()
                }).FirstOrDefaultAsync();
        }
        else if (actionDataType == clsConstants.ActionDataType.BlistData.ToString())
        {
            data = await (from s in DBContext.tfp_temp_header
                join tu in DBContext.vwUserDetails on s.updated_by equals tu.pk_id
                where s.pk_temp_id == id && s.fk_tenant_id == userdata.tenant_id && !s.is_parked_action
                select new
                {
                    //lastchanged = tu.first_name + " " + tu.last_name + " " + SqlFunctions.DatePart("dd", s.updated) + "." + SqlFunctions.DatePart("mm", s.updated) + "." + SqlFunctions.DatePart("yyyy", s.updated),
                    lastchanged = tu.first_name + " " + tu.last_name + " " + s.updated.ToString("dd") + "." + s.updated.ToString("mm") + "." + s.updated.ToString("yyyy"),
                    actiondescription = s.long_description.Trim(),
                    financialplan = s.financial_plan_description.Trim(),
                    actionconsequence = s.consequence.Trim()
                }).FirstOrDefaultAsync();
        }
        else if (actionDataType == clsConstants.ActionDataType.DeletedData.ToString())
        {
            data = await (from s in DBContext.tfp_delete_header
                join tu in DBContext.vwUserDetails on s.updated_by equals tu.pk_id
                where s.pk_action_id == id && s.fk_tenant_id == userdata.tenant_id
                select new
                {
                    //lastchanged = tu.first_name + " " + tu.last_name + " " + SqlFunctions.DatePart("dd", s.updated) + "." + SqlFunctions.DatePart("mm", s.updated) + "." + SqlFunctions.DatePart("yyyy", s.updated),
                    lastchanged = tu.first_name + " " + tu.last_name + " " + s.updated.ToString("dd") + "." + s.updated.ToString("mm") + "." + s.updated.ToString("yyyy"),
                    actiondescription = s.long_description.Trim(),
                    financialplan = "",
                    actionconsequence = s.consequence.Trim()
                }).FirstOrDefaultAsync();
        }
        dynamic jobj = new JObject();

        if (data != null)
        {
            jobj.Add("status", true);
            jobj.Add("lastchanged", data.lastchanged);
            jobj.Add("actiondescription", Regex.Replace(string.IsNullOrEmpty(data.actiondescription) ? string.Empty : data.actiondescription, @"(<img\/?[^>]+>)", @"", RegexOptions.IgnoreCase));
            jobj.Add("financialplan", Regex.Replace(string.IsNullOrEmpty(data.financialplan) ? string.Empty : data.financialplan, @"(<img\/?[^>]+>)", @"", RegexOptions.IgnoreCase));
            jobj.Add("actionconsequence", Regex.Replace(string.IsNullOrEmpty(data.actionconsequence) ? string.Empty : data.actionconsequence, @"(<img\/?[^>]+>)", @"", RegexOptions.IgnoreCase));
        }
        else
        {
            jobj.Add("status", false);
            jobj.Add("desc", description);
        }

        string serializedObj = JsonConvert.SerializeObject(jobj);
        return serializedObj.ToString();
    }



    private async Task<List<string>> GetLevelNameForFilter(string userId, int budgetYear)
    {
        TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        Dictionary<string, string> orgLevelValue = await _utility.GetOrglevelsAsync(userId);
        var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));
        List<string> orgDisplayName = new List<string>();
        if (orgLevelValue.Count == 1)
        {
            string lvl1ParamName = orgLevelValue.FirstOrDefault().Value;
            string[] spltParamName = lvl1ParamName.Split('_');
            int levelNumber = Convert.ToInt32(spltParamName[2]);
            if (spltParamName[0] == "org")
            {
                var orgName = (from orgV in orgVersionContent.lstOrgLevel
                    where orgV.org_level == levelNumber
                    select orgV.level_name).ToList();
                orgDisplayName.AddRange(orgName);
            }
            else
            {
                var serVName = await (from serv in dbContext.tco_service_level
                    where serv.fk_tenant_id == userDetails.tenant_id && serv.service_level == levelNumber
                    select serv.level_name).ToListAsync();
                orgDisplayName.AddRange(serVName);
            }
        }
        else
        {
            string orgParamName = orgLevelValue.FirstOrDefault(x => x.Key == "FINPLAN_LEVEL_1").Value;
            string serviceParamName = orgLevelValue.FirstOrDefault(x => x.Key == "FINPLAN_LEVEL_2").Value;
            string[] orgSpltParamName = orgParamName.Split('_');
            string[] SerSpltParamName = serviceParamName.Split('_');
            int level1Number = Convert.ToInt32(orgSpltParamName[2]);
            int level2Number = Convert.ToInt32(SerSpltParamName[2]);
            List<KeyValueHelper> levelType = new List<KeyValueHelper>();
            KeyValueHelper lvl1 = new KeyValueHelper { KeyId = level1Number, ValueString = orgSpltParamName[0] };
            levelType.Add(lvl1);
            lvl1 = new KeyValueHelper { KeyId = level2Number, ValueString = SerSpltParamName[0] };
            levelType.Add(lvl1);
            foreach (var item in levelType)
            {
                if (item.ValueString == "org")
                {
                    var orgName = (from orgV in orgVersionContent.lstOrgLevel
                        where orgV.org_level == item.KeyId
                        select orgV.level_name).ToList();
                    orgDisplayName.AddRange(orgName);
                }
                else
                {
                    var serVName = await (from serv in dbContext.tco_service_level
                        where serv.fk_tenant_id == userDetails.tenant_id && serv.service_level == item.KeyId
                        select serv.level_name).ToListAsync();
                    orgDisplayName.AddRange(serVName);
                }
            }
        }
        return orgDisplayName;
    }

}