#pragma warning disable CS8600
#pragma warning disable CS8601
#pragma warning disable CS8602
#pragma warning disable CS8603
#pragma warning disable CS8604
#pragma warning disable CS8619

using Framsikt.BL.Helpers;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Microsoft.Azure.Amqp.Framing;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Data;
using System.Globalization;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using static Framsikt.BL.DocWidget;
using KeyValuePair = Framsikt.BL.Helpers.KeyValuePair;

namespace Framsikt.BL
{
    public class MRForecastReport : IMRForecastReport
    {
        private readonly IUtility _utility;
        private readonly IAppDataCache _cache;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IFinPlanReport _FPReport;

        public MRForecastReport(IUtility util, IReportingUtility reportingUtil, IAppDataCache cache, IUnitOfWork uow, IFinPlanReport FPReport)
        {
            _utility = util;
            _cache = cache;
            _unitOfWork = uow;
            _FPReport = FPReport;
        }

        public async Task<IEnumerable<gco_reporting_columns>> GetReportColumns()
        {
            TimeSpan cacheTimeOut = new TimeSpan(24, 0, 0);
            string cacheKeyForecastReportCol = "forecastReportColumns";
            string? strReportColumns = await _cache.GetStringGlobalAsync(cacheKeyForecastReportCol);
            if (string.IsNullOrEmpty(strReportColumns))
            {
                IEnumerable<gco_reporting_columns> reportColumns = await _unitOfWork.YearlyBudgetReportRepo.ReportColumns(clsConstants.ForecastReport_pageId);
                string szreportColumns = JsonConvert.SerializeObject(reportColumns);
                await _cache.SetStringGlobalAsync(cacheKeyForecastReportCol, szreportColumns, cacheTimeOut);
                return reportColumns;
            }
            else
            {
                IEnumerable<gco_reporting_columns> reportColumns = JsonConvert.DeserializeObject<IEnumerable<gco_reporting_columns>>(strReportColumns);
                return reportColumns;
            }
        }

        public async Task<IEnumerable<gco_reporting_columns>> GetFilterColumns()
        {
            TimeSpan cacheTimeOut = new TimeSpan(24, 0, 0);
            string cacheKeyForecastReportCol = "forecastReportFilterColumns";
            string? strReportColumns = await _cache.GetStringGlobalAsync(cacheKeyForecastReportCol);
            if (string.IsNullOrEmpty(strReportColumns))
            {
                IEnumerable<gco_reporting_columns> filterColumns = await _unitOfWork.YearlyBudgetReportRepo.FilterColumns(clsConstants.ForecastReport_pageId);
                string szreportColumns = JsonConvert.SerializeObject(filterColumns);
                await _cache.SetStringGlobalAsync(cacheKeyForecastReportCol, szreportColumns, cacheTimeOut);
                return filterColumns;
            }
            else
            {
                IEnumerable<gco_reporting_columns> filterColumns = JsonConvert.DeserializeObject<IEnumerable<gco_reporting_columns>>(strReportColumns);
                return filterColumns;
            }
        }

        public async Task<List<FilterResult>> GetFilterData(string userId, int tenantId, FilterDataInput filterInput)
        {
            var reportColumns = await GetReportColumns();

            IEnumerable<DataRow> filterData = await _unitOfWork.ForecastReportRepo.FilterDataParam(userId, tenantId, filterInput, reportColumns);
            var convertedData = CovertDataIntoFilterResult(filterInput.columnId, filterData, userId);

            return convertedData;
        }

        public async Task<DataTable> GetReportData(List<ReportColumnHelper> columnIds, int forecastPeriod, int tenantId, ReportInput input)
        {
            return await GetReportData(columnIds, forecastPeriod, tenantId, input, string.Empty);
        }

        public async Task<DataTable> GetReportData(List<ReportColumnHelper> columnIds, int forecastPeriod, int tenantId, ReportInput input, string userId)
        {
            UserData userDetails = new UserData();
            TenantData tenantData = await _utility.GetTenantDataAsync(tenantId);
            if (string.IsNullOrEmpty(userId))
            {
                userDetails = new UserData
                {
                    tenant_id = tenantId,
                    language_preference = tenantData.langPref
                };
            }
            else
            {
                userDetails = await _utility.GetUserDetailsAsync(userId);
            }
            List<string> dataTypes = new List<string> { "decimal", "numeric", "custom" };
            TenantLevelHelper levelData = new TenantLevelHelper { level1 = string.Empty, level2 = string.Empty };

            List<string> groupColumnsForParam = columnIds.Where(x => !dataTypes.Contains(x.DataType)).Select(x => x.ColName).ToList();
            IEnumerable<ReportColumnHelper> reportColumn = await GetReportColumnsWithCustomColumn(tenantId, input.budgetYear, userDetails);
            var whereData = SetDictinaryConditionalDataWhereClause(input, reportColumn);

            //get tenant levelSetup
            if (!string.IsNullOrEmpty(input.level1) || !string.IsNullOrEmpty(input.level2))
            {
                levelData = await _FPReport.GetTenantSpecificOrgLevelAsync(tenantId);
            }
            input.tenantLevel1 = levelData.level1;
            input.tenantLevel2 = levelData.level2;

            //get data

            IEnumerable<DataRow> reportData = await _unitOfWork.ForecastReportRepo.ReportDataByParam(tenantId, forecastPeriod, groupColumnsForParam, whereData, reportColumn, input, columnIds.Any(x => x.DataType == "custom"));

            //create data table
            DataTable rptData = new DataTable("reportDataTable");
            foreach (var col in columnIds)
            {
                rptData.Columns.Add(col.ColName, _utility.TypeIdentifier(col.DataType));
            }

            //Add Items to the Data tabe
            foreach (var dataRow in reportData)
            {
                DataRow newRow = rptData.NewRow();
                foreach (var col in columnIds)
                {
                    if (col.DataType == "custom")
                    {
                        newRow[col.ColName] = dataRow[col.DisplayName];
                    }
                    else
                    {
                        newRow[col.ColName] = dataRow[col.ColName];
                    }
                }
                rptData.Rows.Add(newRow);
            }

            return rptData;
        }

        public async Task<List<KeyValuePair>> FormatMonthYearData(string userName, string jsDateTime)
        {
            int numberOfMonthsYear = 12;
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            List<KeyValuePair> keyValueDateTimeStrVal = new List<KeyValuePair>();
            UserData userDetails = await _utility.GetUserDetailsAsync(userName);

            Dictionary<string, clsLanguageString> langStringValuesCommon = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Common");

            string[] monthFields = new string[] { ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_january_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_february_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_march_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_april_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_may_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_june_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_july_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_august_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_september_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_october_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_november_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_december_short3text")).Value).LangText,
            };

            DateTime dYearMonth = DateTime.Parse(jsDateTime);

            var latestForecast = await tenantDbContext.tmr_data_warehouse.Where(t => t.fk_tenant_id == userDetails.tenant_id).Select(z => z.forecast_period).Distinct().ToListAsync();
            int forecastPeriod = latestForecast.Count > 0 ? latestForecast.OrderByDescending(x => x).FirstOrDefault() : 0;
            if (forecastPeriod != 0)
            {
                DateTime dYearMonthLatestForecast = DateTime.Parse(forecastPeriod.ToString().Insert(4, "-"));

                for (int i = numberOfMonthsYear * -2; i < numberOfMonthsYear; i++)
                {
                    KeyValuePair yearMonthKeyVal = new KeyValuePair();

                    yearMonthKeyVal.key = dYearMonth.AddMonths(i).ToString(CultureInfo.InvariantCulture);
                    yearMonthKeyVal.value = monthFields[dYearMonth.AddMonths(i).Month - 1] + " " + dYearMonth.AddMonths(i).Year;

                    var latestForecastPeriod = monthFields[dYearMonthLatestForecast.AddMonths(0).Month - 1] + " " + dYearMonthLatestForecast.AddMonths(0).Year;
                    if (string.Equals(yearMonthKeyVal.value, latestForecastPeriod))
                    {
                        yearMonthKeyVal.isChecked = true;
                    }

                    keyValueDateTimeStrVal.Add(yearMonthKeyVal);
                }
            }

            return keyValueDateTimeStrVal;
        }

        public string createWhereClauseWithFilterColumn(ReportInput input, List<ReportColumnHelper> columnIds)
        {
            StringBuilder whereClause = new StringBuilder();
            if (input.filterColumns != null && input.filterColumns.Any() && input.filterValues != null && input.filterValues.Any())
            {
                foreach (var item in input.filterColumns)
                {
                    if (input.filterColumns.IndexOf(item) < input.filterValues.Count)
                    {
                        if (input.filterValues[input.filterColumns.IndexOf(item)] == null || !input.filterValues[input.filterColumns.IndexOf(item)].Any())
                        {
                            continue;
                        }
                        SetConditionalDataWhereClause(input,whereClause, item);
                    }
                }
            }

            return string.IsNullOrEmpty(whereClause.ToString()) ? string.Empty : " and " + whereClause.ToString().Remove(whereClause.ToString().Length - 3); //remove last appended 'and '
        }

        public JArray CreateColumnJsonForForecastReport(List<ReportColumnHelper> columnId, List<string> selectedColumns)
        {
            Assembly assembly = Assembly.GetExecutingAssembly();
            Stream configStream = null;

            configStream = assembly.GetManifestResourceStream("Framsikt.BL.Core.Config.Reporting.ForecastReport.report.json");
            StreamReader reader = new StreamReader(configStream);
            string config = reader.ReadToEnd();
            JArray colConfig = JArray.Parse(config);

            JArray updatedColConfig = new JArray();

            var stringCol = colConfig[0];
            var numberCol = colConfig[1];
            var customCol = colConfig[2];
            var valueType = colConfig[3];

            foreach (var selCol in selectedColumns)
            {
                ReportColumnHelper col = columnId.FirstOrDefault(x => x.ColName == selCol);
                switch (col.DataType)
                {
                    case "decimal":
                    case "numeric":
                        numberCol["field"] = col.ColName;
                        numberCol["title"] = col.DisplayName;
                        numberCol["dataType"] = col.DataType;
                        updatedColConfig.Add(numberCol);
                        break;

                    case "string":
                    case "datetime":
                        stringCol["field"] = col.ColName;
                        stringCol["title"] = col.DisplayName;
                        stringCol["filterable"] = true;
                        stringCol["dataType"] = col.DataType;
                        updatedColConfig.Add(stringCol);
                        break;

                    case "custom":
                        customCol["field"] = col.ColName;
                        customCol["title"] = col.DisplayName;
                        customCol["format"] = "{0:" + col.numberFormat + "}";
                        stringCol["dataType"] = "decimal";
                        updatedColConfig.Add(customCol);
                        break;

                    case "valueType":
                        valueType["field"] = col.ColName;
                        valueType["title"] = col.DisplayName;
                        valueType["template"] = "#if(" + col.ColName + " && value_type!='text'){#<span class='number-format-items'>#:kendo.toString(parseFloat(" + col.ColName + "), value_type)#</span>#}else{#<span>#=" + col.ColName + "#</span>#}#";
                        valueType["dataType"] = "custom";
                        updatedColConfig.Add(valueType);
                        break;

                    default:

                        break;
                }
            }
            return updatedColConfig;
        }

        private static void SetConditionalDataWhereClause(ReportInput input, StringBuilder whereClause, string item)
        {
            whereClause.Append("[" + item + "] in (" + string.Join(",", input.filterValues[input.filterColumns.IndexOf(item)].Select(s => "'" + s + "'")) + ") and");
        }

        private string OrderBy(List<ReportColumnHelper> columnIds)
        {
            StringBuilder orderBy = new StringBuilder();
            foreach (var item in columnIds)
            {
                switch (item.DataType)
                {
                    case "numeric":
                    case "decimal":
                    case "datetime":
                        orderBy.Append("[" + item.ColName + "],");
                        break;

                    case "custom":
                        orderBy.Append("[" + item.DisplayName + "],");
                        break;

                    default:
                        orderBy.Append("[" + item.ColName + "] collate Danish_Norwegian_CI_AS,");
                        break;
                }
            }
            return orderBy.ToString().Remove(orderBy.Length - 1);
        }

        public List<FilterResult> CovertDataIntoFilterResult(string columnId, IEnumerable<DataRow> filterData, string userId)
        {
            UserData userDetails = _utility.GetUserDetails(userId);
            Dictionary<string, clsLanguageString> langStrings = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "Common");
            List<FilterResult> convertedData = new List<FilterResult>();
            FilterResult temp;

            foreach (var b in filterData)
            {
                if (b[columnId].GetType().ToString() == "System.DateTime")
                {
                    var dateValue = b[columnId] == DBNull.Value ? ((langStrings.FirstOrDefault(v => v.Key == "Reporting_emptyString")).Value).LangText : Convert.ToDateTime(b[columnId]).ToShortDateString();
                    temp = new FilterResult() { itemKey = dateValue, item = dateValue };
                }
                else
                {
                    var stringValue = b[columnId] == DBNull.Value || b[columnId].ToString() == String.Empty ? ((langStrings.FirstOrDefault(v => v.Key == "Reporting_emptyString")).Value).LangText : b[columnId].ToString();
                    temp = new FilterResult() { itemKey = stringValue, item = stringValue };
                }

                convertedData.Add(temp);
            }
            return convertedData.GroupBy(x => x.itemKey).Select(x => x.FirstOrDefault()).ToList();
        }

        private List<ReportFiletrColumnParam> SetDictinaryConditionalDataWhereClause(ReportInput input, IEnumerable<ReportColumnHelper> columnIds)
        {
            List<ReportFiletrColumnParam> paramColumnList = new List<ReportFiletrColumnParam>();
            ReportFiletrColumnParam tempData;
            foreach (var c in input.filterColumns)
            {
                if (!paramColumnList.Any(x => x.columnId == c))
                {
                    if (!paramColumnList.Any(x => x.columnId == c))
                    {
                        tempData = new ReportFiletrColumnParam();
                        tempData.columnId = c;
                        var ind = input.filterColumns.IndexOf(c);
                        List<string> valueData = input.filterValues.Any() && input.filterColumns.IndexOf(c) < input.filterValues.Count ? input.filterValues[input.filterColumns.IndexOf(c)].Select(s => s).ToList() : new List<string>();
                        tempData.filterValue = new List<string>();
                        tempData.filterValue.AddRange(valueData);
                        tempData.isCommaSeperatedColumn = columnIds.FirstOrDefault(z => z.ColName == c).IsCommaSeperatedData;
                        FilterConditionValues filterValues = input.filterToggleConditionValues != null && input.filterToggleConditionValues.Any() && input.filterColumns.IndexOf(c) < input.filterToggleConditionValues.Count ? input.filterToggleConditionValues[ind] : new FilterConditionValues();

                        tempData.filterConditionValues = filterValues;

                        if (valueData.Any())
                        {
                            paramColumnList.Add(tempData);
                        }
                    }
                }
            }
            return paramColumnList;
        }

        private async Task<IEnumerable<ReportColumnHelper>> GetReportColumnsWithCustomColumn(int tenantId, int budgetYear, UserData userDetails)
        {
            TenantData tenantData = await _utility.GetTenantDataAsync(tenantId);

            Dictionary<string, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(tenantData.langPref, tenantData.client_id, tenantId, "ForecastReport");

            var reportColumns = await GetReportColumns();

            List<ReportColumnHelper> finalColumns = (from rc in reportColumns
                                                     select new ReportColumnHelper
                                                     {
                                                         ColName = rc.column_id,
                                                         DisplayName = rc.offset != null ? ((langString.FirstOrDefault(v => v.Key == rc.column_lang_id)).Value).LangText + ' ' + (budgetYear + rc.offset) :
                                                                                           ((langString.FirstOrDefault(v => v.Key == rc.column_lang_id)).Value).LangText,
                                                         DataType = rc.column_data_type,
                                                         type = rc.type,
                                                         IsCommaSeperatedData = rc.isCommaSeperatedData,
                                                     }).OrderBy(x => x.DisplayName).ToList();

            finalColumns.Add(new ReportColumnHelper { ColName = "external_description_html", DisplayName = "external_description_html", DataType = "string" });
            finalColumns.Add(new ReportColumnHelper { ColName = "external_description_cab", DisplayName = "external_description_cab", DataType = "string" });

            //Get Custom/formula Columns

            finalColumns.AddRange(await _FPReport.GetFormattedCustomColumns(userDetails, ReportTypes.ForecastReport, budgetYear, reportColumns.ToList()));

            return finalColumns.OrderBy(x => x.DisplayName).ToList();
        }
    }
}