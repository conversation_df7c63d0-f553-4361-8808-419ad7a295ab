#pragma warning disable CS8625
#pragma warning disable CS8634

#pragma warning disable CS8600
#pragma warning disable CS8601
#pragma warning disable CS8602
#pragma warning disable CS8604
#pragma warning disable <PERSON>86<PERSON>

using Framsikt.BL.Core;
using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Framsikt.Web.Helpers;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using System.Dynamic;
using System.Globalization;

namespace Framsikt.BL
{
    public class SustainableGoalsAnalysis : ISustainableGoalsAnalysis
    {
        private readonly IUtility _utility;
        private readonly IKostraData _kostra;

        public SustainableGoalsAnalysis(IUtility util, IKostraData kostra)
        {
            _utility = util;
            _kostra = kostra;
        }

        public List<UNGoalImageHelper> GetUNGoalImages(string userId)
        {
            UserData userDetails = _utility.GetUserDetails(userId);
            Dictionary<string, clsLanguageString> langStrings = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "SustainableGoals");
            List<UNGoalImageHelper> unGoalImages = new List<UNGoalImageHelper>();
            unGoalImages.Add(new UNGoalImageHelper { key = 1, value = "../images/UN_01_Not_selected.svg", selectedImg = "../images/UN_01_Selected.svg", selectDefault = true, GoalsText = langStrings["SGA_grid_goal_title1"].LangText });
            unGoalImages.Add(new UNGoalImageHelper { key = 2, value = "../images/UN_02_Not_selected.svg", selectedImg = "../images/UN_02_Selected.svg", selectDefault = false, GoalsText = langStrings["SGA_grid_goal_title2"].LangText });
            unGoalImages.Add(new UNGoalImageHelper { key = 3, value = "../images/UN_03_Not_selected.svg", selectedImg = "../images/UN_03_Selected.svg", selectDefault = false, GoalsText = langStrings["SGA_grid_goal_title3"].LangText });
            unGoalImages.Add(new UNGoalImageHelper { key = 4, value = "../images/UN_04_Not_selected.svg", selectedImg = "../images/UN_04_Selected.svg", selectDefault = false, GoalsText = langStrings["SGA_grid_goal_title4"].LangText });
            unGoalImages.Add(new UNGoalImageHelper { key = 5, value = "../images/UN_05_Not_selected.svg", selectedImg = "../images/UN_05_Selected.svg", selectDefault = false, GoalsText = langStrings["SGA_grid_goal_title5"].LangText });
            unGoalImages.Add(new UNGoalImageHelper { key = 6, value = "../images/UN_06_Not_selected.svg", selectedImg = "../images/UN_06_Selected.svg", selectDefault = false, GoalsText = langStrings["SGA_grid_goal_title6"].LangText });
            unGoalImages.Add(new UNGoalImageHelper { key = 7, value = "../images/UN_07_Not_selected.svg", selectedImg = "../images/UN_07_Selected.svg", selectDefault = false, GoalsText = langStrings["SGA_grid_goal_title7"].LangText });
            unGoalImages.Add(new UNGoalImageHelper { key = 8, value = "../images/UN_08_Not_selected.svg", selectedImg = "../images/UN_08_Selected.svg", selectDefault = false, GoalsText = langStrings["SGA_grid_goal_title8"].LangText });
            unGoalImages.Add(new UNGoalImageHelper { key = 9, value = "../images/UN_09_Not_selected.svg", selectedImg = "../images/UN_09_Selected.svg", selectDefault = false, GoalsText = langStrings["SGA_grid_goal_title9"].LangText });
            unGoalImages.Add(new UNGoalImageHelper { key = 10, value = "../images/UN_10_Not_selected.svg", selectedImg = "../images/UN_10_Selected.svg", selectDefault = false, GoalsText = langStrings["SGA_grid_goal_title10"].LangText });
            unGoalImages.Add(new UNGoalImageHelper { key = 11, value = "../images/UN_11_Not_selected.svg", selectedImg = "../images/UN_11_Selected.svg", selectDefault = false, GoalsText = langStrings["SGA_grid_goal_title11"].LangText });
            unGoalImages.Add(new UNGoalImageHelper { key = 12, value = "../images/UN_12_Not_selected.svg", selectedImg = "../images/UN_12_Selected.svg", selectDefault = false, GoalsText = langStrings["SGA_grid_goal_title12"].LangText });
            unGoalImages.Add(new UNGoalImageHelper { key = 13, value = "../images/UN_13_Not_selected.svg", selectedImg = "../images/UN_13_Selected.svg", selectDefault = false, GoalsText = langStrings["SGA_grid_goal_title13"].LangText });
            unGoalImages.Add(new UNGoalImageHelper { key = 14, value = "../images/UN_14_Not_selected.svg", selectedImg = "../images/UN_14_Selected.svg", selectDefault = false, GoalsText = langStrings["SGA_grid_goal_title14"].LangText });
            unGoalImages.Add(new UNGoalImageHelper { key = 15, value = "../images/UN_15_Not_selected.svg", selectedImg = "../images/UN_15_Selected.svg", selectDefault = false, GoalsText = langStrings["SGA_grid_goal_title15"].LangText });
            unGoalImages.Add(new UNGoalImageHelper { key = 16, value = "../images/UN_16_Not_selected.svg", selectedImg = "../images/UN_16_Selected.svg", selectDefault = false, GoalsText = langStrings["SGA_grid_goal_title16"].LangText });
            unGoalImages.Add(new UNGoalImageHelper { key = 17, value = "../images/UN_17_Not_selected.svg", selectedImg = "../images/UN_17_Selected.svg", selectDefault = false, GoalsText = langStrings["SGA_grid_goal_title17"].LangText });

            return unGoalImages;
        }

        public async Task<List<KeyValueNewData>> GetGridFilter()
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            List<string?> UNfactor = await dbContext.gmd_un_indicator_mapping.Where(x => x.fk_un_goal_id != null).Select(x => x.un_factor).Distinct().AsNoTracking().ToListAsync();
            List<KeyValueNewData> filterData = new List<KeyValueNewData>();
            foreach (var item in UNfactor)
            {
                KeyValueNewData filter = new KeyValueNewData
                {
                    Key = item,
                    Value = item
                };
                filterData.Add(filter);
            }
            return filterData;
        }

        public async Task<List<KeyValueNewData>> GetTab(string userId)
        {
            var dbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringValuesCommon = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Common");
            List<string> UNType = await dbContext.gmd_un_indicator_mapping.Where(x => x.fk_un_goal_id != null).Select(x => x.un_type).Distinct().AsNoTracking().ToListAsync();
            List<KeyValueNewData> tabData = new List<KeyValueNewData>();
            foreach (var item in UNType)
            {
                KeyValueNewData tab = new KeyValueNewData
                {
                    Key = item,
                    Value = item
                };
                tabData.Add(tab);
            }
            var evaTab = new KeyValueNewData { Key = "InEvaluation", Value = (langStringValuesCommon["cmn_filter_in_evaluation"]).LangText };
            var allTab = new KeyValueNewData { Key = "All", Value = (langStringValuesCommon["cmn_filter_all"]).LangText };
            tabData.Insert(0, evaTab);
            tabData.Add(allTab);
            return tabData;
        }

        public async Task<dynamic> GetGridData(GridInput input, string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            dynamic unGridData = new JObject();
            List<GridColumnHelper> columns = GetGridColumn(input, userId);
            dynamic dColumn = JToken.FromObject(columns);
            unGridData.Add("column", dColumn);
            JArray gridata = await GetDataForGrid(userId, input);
            unGridData.Add("data", gridata);

            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "SustainableGoals");
            unGridData.Add("title", (langStrings["SGA_grid_goal_title1"]).LangText);
            unGridData.Add("titleDescription", (langStrings["SGA_grid_title_tooltip"]).LangText);
            return unGridData;
        }

        private List<GridColumnHelper> GetGridColumn(GridInput input, string userId)
        {
            List<GridColumnHelper> formattedColumns = new List<GridColumnHelper>();
            UserData userDetails = _utility.GetUserDetails(userId);
            Dictionary<string, clsLanguageString> langStringValuesIndicatorType = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "IndicatorType");

            GridColumnHelper columnInfo = new GridColumnHelper
            {
                field = "indicatorDescription",
                title = " ",
                colCount = 0,
                encoded = false,
                attributes = new ColumnStyleHelper { style = "text-align:left; width:30%" },
                headerAttributes = new ColumnStyleHelper { style = "text-align:left; width:30%" },
                template = "# if (isParent === true) {#<span>#: indicatorDescription #</span> #} else { #<span class='SG-indicator-desc'></span> # }# ",
                headerTemplate = "<span class='col-md-2 padding0'><span class='marginright5 sustain-image-wrap'></span><span class='marginleft5'> <input type='checkbox' id='selectallchkbox' style='margin-top:3px'></span></span><span class='col-md-9 padding0'>Velg alle<span style='vertical-align: top; padding-left-10'> </span></span>",
            };
            formattedColumns.Add(columnInfo);
            columnInfo = new GridColumnHelper
            {
                field = "dataSource",
                title = (langStringValuesIndicatorType["data_source"]).LangText,
                colCount = 0,
                encoded = false,
                attributes = new ColumnStyleHelper { style = "text-align:right" },
                headerAttributes = new ColumnStyleHelper { style = "text-align:right" },
            };
            formattedColumns.Add(columnInfo);

            //update region data with own region information
            var dbContext = _utility.GetTenantDBContext();
            int budgetYear = _kostra.GetBudgetYear(userId);
            TenantData tenantDetails = _utility.GetTenantData(userId);
            string tenantRegionCode = tenantDetails.municipality_id;
            string municipalityName = dbContext.gco_municipalities.FirstOrDefault(x => x.pk_municipality_id == tenantRegionCode).municipality_name;
            List<clsRegion> updatedRegionData = input.regionData.ToList();
            List<int> yearFromFilter = new List<int>();
            foreach (clsRegion region in input.regionData)
            {
                if (region.RegionCode.Split('-')[0] == "year")
                {
                    yearFromFilter.Add(Convert.ToInt32(region.RegionCode.Split('-')[1]));
                    updatedRegionData.Remove(region);
                }
            }
            yearFromFilter.Add(budgetYear - 1);
            yearFromFilter.Add(budgetYear - 2);
            foreach (var item in yearFromFilter.OrderByDescending(x => x).Distinct().ToList())
            {
                updatedRegionData.Insert(0, new clsRegion { RegionCode = "year-" + item.ToString(), RegionName = municipalityName + " " + item, sortOrder = 0 });
            }

            int counter = 1;
            foreach (var item in updatedRegionData)
            {
                columnInfo = new GridColumnHelper
                {
                    field = "region" + counter,
                    title = item.RegionName,
                    colCount = 0,
                    encoded = false,
                    attributes = new ColumnStyleHelper { style = "text-align:right" },
                    headerAttributes = new ColumnStyleHelper { style = "text-align:right" },
                    template = "# if(isParent == false && region" + counter + "){ #<span>#=kendo.toString(parseFloat(region" + counter + "), numberType)#</span> #}else{#<span></span>#}#"
                };
                formattedColumns.Add(columnInfo);
                counter++;
            }
            return formattedColumns;
        }

        private async Task<JArray> GetDataForGrid(string userId, GridInput input)
        {
            int budgetYear = await _kostra.GetBudgetYearAsync(userId);
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "NumberFormats");
            List<string> regionCodes = input.regionData.Select(x => x.RegionCode).ToList();
            var dbContext = await _utility.GetTenantDBContextAsync();

            //get indicator List
            // var indicatorList = GetConnectedIndicators(input.UNimageId.ToString());

            var kostraLbl = await (from kl in dbContext.gmd_kostra_lables
                                   join gi in dbContext.gmd_un_indicator_mapping on kl.pk_indicator_code equals gi.fk_indicator_code
                                   where gi.fk_un_goal_id == input.UNimageId.ToString()
                                   orderby gi.un_type
                                   select new
                                   {
                                       kl.pk_indicator_code,
                                       kl.indicator_description,
                                       kl.data_source,
                                       kl.ssb_description,
                                       gi.un_factor,
                                       gi.un_type,
                                       kl.number_type
                                   }).ToListAsync();

            if (!string.IsNullOrEmpty(input.UNfactor))
            {
                kostraLbl = kostraLbl.Where(x => x.un_factor == input.UNfactor).ToList();
            }
            if (!string.IsNullOrEmpty(input.UNType) && input.UNType != "All" && input.UNType != "InEvaluation")
            {
                kostraLbl = kostraLbl.Where(x => x.un_type == input.UNType).ToList();
            }

            List<string> indicatorCodes = kostraLbl.Select(x => x.pk_indicator_code).ToList();

            TenantData tenantDetails = await _utility.GetTenantDataAsync(userId);
            string tenantRegionCode = tenantDetails.municipality_id;
            regionCodes.Add(tenantRegionCode);

            List<int> yearFromFilter = new List<int>();
            foreach (clsRegion region in input.regionData)
            {
                if (region.RegionCode.Split('-')[0] == "year")
                {
                    yearFromFilter.Add(Convert.ToInt32(region.RegionCode.Split('-')[1]));
                }
            }
            yearFromFilter.Add(budgetYear);
            yearFromFilter.Add(budgetYear - 1);
            yearFromFilter.Add(budgetYear - 2);

            var kostraFullData = await (from kd in dbContext.gko_kostra_data_corp
                                        where yearFromFilter.Contains(kd.year) && regionCodes.Contains(kd.fk_region_code) && indicatorCodes.Contains(kd.fk_indicator_code)
                                        select new
                                        {
                                            kd.fk_indicator_code,
                                            kd.indicator_value,
                                            kd.fk_region_code,
                                            kd.year
                                        }).ToListAsync();
            yearFromFilter.Remove(budgetYear);
            var kostraData = (from kd in kostraFullData
                              where kd.year == (budgetYear - 1) && regionCodes.Contains(kd.fk_region_code) && kd.fk_region_code != tenantRegionCode && indicatorCodes.Contains(kd.fk_indicator_code)
                              select new
                              {
                                  kd.fk_indicator_code,
                                  kd.indicator_value,
                                  kd.fk_region_code,
                                  kd.year
                              }).ToList();

            //Get indicators included in document
            var includedInDoc = await (from ind in dbContext.tko_ind_explanation
                                       where indicatorCodes.Contains(ind.fk_indicator_code) && ind.year == budgetYear && ind.fk_tenant_id == userDetails.tenant_id
                                             && ind.fk_template_id == input.templateId && ind.indicator_type == "" && ind.document_flag
                                       select new
                                       {
                                           ind.fk_indicator_code,
                                           ind.description,
                                           ind.locked_flag
                                       }).ToListAsync();

            if (input.UNType == "InEvaluation")
            {
                List<string> evaluationIndCode = includedInDoc.Select(x => x.fk_indicator_code).ToList();
                kostraLbl = kostraLbl.Where(x => evaluationIndCode.Contains(x.pk_indicator_code)).ToList();
            }
            List<string> typesGrouped = kostraLbl.Select(x => x.un_type).Distinct().ToList();

            var ownRegionData = (from kd in kostraFullData
                                 where indicatorCodes.Contains(kd.fk_indicator_code)
                                        && yearFromFilter.Contains(kd.year) && kd.fk_region_code == tenantRegionCode
                                 select new
                                 {
                                     kd.fk_indicator_code,
                                     kd.indicator_value,
                                     kd.fk_region_code,
                                     kd.year
                                 }).ToList();
            var mergedkostraData = kostraData.Concat(ownRegionData);
            string municipalityName = dbContext.gco_municipalities.FirstOrDefault(x => x.pk_municipality_id == tenantRegionCode).municipality_name;
            List<clsRegion> updatedRegionData = input.regionData.ToList();
            foreach (var item in yearFromFilter.OrderByDescending(x => x).Distinct().ToList())
            {
                updatedRegionData.Insert(0, new clsRegion { RegionCode = "year-" + item.ToString(), RegionName = municipalityName + " " + item, sortOrder = 0 });
            }
            dynamic gridData = new JArray();
            int parentId = 0;
            foreach (var unType in typesGrouped)
            {
                if (input.UNType == "InEvaluation" || input.UNType == "All")
                {
                    parentId++;
                    var dynamicObject = new ExpandoObject() as IDictionary<string, Object>;
                    dynamicObject.Add("indicatorCode", parentId.ToString());
                    dynamicObject.Add("indicatorDescription", unType);
                    dynamicObject.Add("dataSource", string.Empty);
                    dynamicObject.Add("indicatorStatus", "NA");
                    dynamicObject.Add("parentId", null);
                    dynamicObject.Add("isParent", true);
                    dynamicObject.Add("id", parentId.ToString());
                    dynamicObject.Add("checked", false);
                    gridData.Add(JToken.FromObject(dynamicObject));
                }
                foreach (var ksLbl in kostraLbl.Where(x => x.un_type == unType))
                {
                    var dynamicObject = new ExpandoObject() as IDictionary<string, Object>;
                    dynamicObject.Add("indicatorCode", ksLbl.pk_indicator_code.ToString());
                    dynamicObject.Add("indicatorDescription", ksLbl.indicator_description);
                    dynamicObject.Add("dataSource", ksLbl.data_source);
                    dynamicObject.Add("isParent", false);
                    dynamicObject.Add("ssbdescription", ksLbl.ssb_description);
                    dynamicObject.Add("id", parentId + "-" + ksLbl.pk_indicator_code);
                    int counter = 1;
                    foreach (var region in updatedRegionData)
                    {
                        if (region.RegionCode.Split('-')[0] == "year")
                        {
                            int yearValue = Convert.ToInt32(region.RegionCode.Split('-')[1]);
                            if (mergedkostraData.FirstOrDefault(x => x.fk_indicator_code == ksLbl.pk_indicator_code && x.year == yearValue && x.fk_region_code == tenantRegionCode) != null)
                            {
                                dynamicObject.Add("region" + counter, mergedkostraData.FirstOrDefault(x => x.fk_indicator_code == ksLbl.pk_indicator_code && x.year == yearValue && x.fk_region_code == tenantRegionCode).indicator_value);
                            }
                            else
                            {
                                dynamicObject.Add("region" + counter, string.Empty);
                            }
                        }
                        else
                        {
                            if (mergedkostraData.FirstOrDefault(x => x.fk_indicator_code == ksLbl.pk_indicator_code && x.fk_region_code == region.RegionCode) != null)
                            {
                                dynamicObject.Add("region" + counter, mergedkostraData.FirstOrDefault(x => x.fk_indicator_code == ksLbl.pk_indicator_code && x.fk_region_code == region.RegionCode).indicator_value);
                            }
                            else
                            {
                                dynamicObject.Add("region" + counter, string.Empty);
                            }
                        }
                        counter++;
                    }
                    string format = ((langStringValues.FirstOrDefault(v => v.Key == ksLbl.number_type)).Value).LangText;
                    dynamicObject.Add("numberType", format);
                    if (includedInDoc.Any(x => x.fk_indicator_code == ksLbl.pk_indicator_code))
                    {
                        if (includedInDoc.Any(x => string.IsNullOrEmpty(x.description)))
                        {
                            dynamicObject.Add("indicatorStatus", "NotStarted");
                        }
                        else if (includedInDoc.Any(x => string.IsNullOrEmpty(x.description) && !x.locked_flag))
                        {
                            dynamicObject.Add("indicatorStatus", "InProgress");
                        }
                        else
                        {
                            dynamicObject.Add("indicatorStatus", "Complete");
                        }
                        dynamicObject.Add("checked", true);
                    }
                    else
                    {
                        dynamicObject.Add("indicatorStatus", "NA");
                        dynamicObject.Add("checked", false);
                    }
                    if (input.UNType == "InEvaluation" || input.UNType == "All")
                    {
                        dynamicObject.Add("parentId", parentId.ToString());
                    }
                    else
                    {
                        dynamicObject.Add("parentId", null);
                    }
                    gridData.Add(JToken.FromObject(dynamicObject));
                }
            }
            return gridData;
        }

        public async Task<JsonFormatter> KostraDetailSideMenu(string userId, GridInput input)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            JsonFormatter jsonFormatter = new JsonFormatter();
            List<string> columnFields = new List<string> { "indicatorCode", "", "status" };
            jsonFormatter.columnFields.AddRange(columnFields);

            int budgetYear = await _kostra.GetBudgetYearAsync(userId);
            //get indicator List
            //var indicatorList = GetConnectedIndicators(input.UNimageId.ToString());

            var kostraLbl = await (from kl in dbContext.gmd_kostra_lables
                                   join gi in dbContext.gmd_un_indicator_mapping on kl.pk_indicator_code equals gi.fk_indicator_code
                                   where gi.fk_un_goal_id == input.UNimageId.ToString()
                                   orderby gi.un_type
                                   select new
                                   {
                                       kl.pk_indicator_code,
                                       kl.indicator_description,
                                       kl.data_source,
                                       kl.ssb_description,
                                       gi.un_factor,
                                       gi.un_type,
                                       kl.number_type
                                   }).ToListAsync();

            if (!string.IsNullOrEmpty(input.UNfactor))
            {
                kostraLbl = kostraLbl.Where(x => x.un_factor == input.UNfactor).ToList();
            }
            if (!string.IsNullOrEmpty(input.UNType) && input.UNType != "All" && input.UNType != "InEvaluation")
            {
                kostraLbl = kostraLbl.Where(x => x.un_type == input.UNType).ToList();
            }

            List<string> indicatorCodes = kostraLbl.Select(x => x.pk_indicator_code).ToList();
            var includedInDoc = (from ind in dbContext.tko_ind_explanation
                                 where indicatorCodes.Contains(ind.fk_indicator_code) && ind.year == budgetYear && ind.fk_tenant_id == userDetails.tenant_id
                                       && ind.fk_template_id == input.templateId && ind.indicator_type == "" && ind.document_flag
                                 select new
                                 {
                                     ind.fk_indicator_code,
                                     ind.description,
                                     ind.locked_flag
                                 }).ToList();
            if (input.UNType == "InEvaluation")
            {
                List<string> evaluationIndCode = includedInDoc.Select(x => x.fk_indicator_code).ToList();
                kostraLbl = kostraLbl.Where(x => evaluationIndCode.Contains(x.pk_indicator_code)).ToList();
            }
            foreach (var item in kostraLbl)
            {
                List<string> rowData = new List<string>
                {
                    Convert.ToString(item.pk_indicator_code),
                    Convert.ToString(item.indicator_description)
                };
                if (includedInDoc.Any(x => x.fk_indicator_code == item.pk_indicator_code))
                {
                    if (includedInDoc.Any(x => string.IsNullOrEmpty(x.description)))
                    {
                        rowData.Add("NotStarted");
                    }
                    else if (includedInDoc.Any(x => string.IsNullOrEmpty(x.description) && !x.locked_flag))
                    {
                        rowData.Add("InProgress");
                    }
                    else
                    {
                        rowData.Add("Complete");
                    }
                }
                else
                {
                    rowData.Add("NA");
                }

                jsonFormatter.rows.Add(rowData);
                jsonFormatter.indicatorType = Convert.ToString(input.UNType);
            }
            return jsonFormatter;
        }

        public void SaveSustainableGoalsDescription(clsIndicatorEvaluation evaluationContent, string userId)
        {
            evaluationContent.evaluation = _utility.DecodeHtmlString(evaluationContent.evaluation, userId).GetAwaiter().GetResult();
            evaluationContent.indicatorCode = "SUS_GOAL_DATA";
            _kostra.SaveIndicatorEvaluation(evaluationContent, userId);
        }

        public clsIndicatorEvaluation GetSustainableGoalsDescription(clsIndicatorEvaluation evaluationContent, string userId)
        {
            evaluationContent.indicatorCode = "SUS_GOAL_DATA";
            evaluationContent = _kostra.GetEvaluationContent(evaluationContent, userId);
            return evaluationContent;
        }

        public string ResetTemplate(string user, string oldtemplateId, string newtemplateId, bool isglobal)
        {
            var dbContext = _utility.GetTenantDBContext();
            UserData userDetails = _utility.GetUserDetails(user);
            int budgetYear = _kostra.GetBudgetYear(user);
            Guid newTemplateIdGuid = Guid.Parse(newtemplateId);
            Guid oldTemplateIdGuid = Guid.Parse(oldtemplateId);
            string result;
            try
            {
                //get indicator List
                var indicatorList = GetConnectedIndicators(string.Empty);

                //Get indicators mapped to the area
                var indicatorsSet = (from a in dbContext.gmd_kostra_lables
                                     join b in dbContext.gmd_kostra_function on a.fk_kostra_function_code equals b.pk_kostra_function_code
                                     where indicatorList.Contains(a.pk_indicator_code)
                                     select new
                                     {
                                         a.pk_indicator_code,
                                         a.indicator_description,
                                         a.fk_kostra_function_code,
                                         a.data_source,
                                         a.type
                                     });

                //get data from tko_ind_explanation
                var newTiEdata = dbContext.tko_ind_explanation.Where(x => x.fk_template_id == newTemplateIdGuid && x.fk_tenant_id == userDetails.tenant_id && x.year == budgetYear && (indicatorsSet.Select(y => y.pk_indicator_code).Contains(x.fk_indicator_code))).ToList();
                var oldTiEdata = dbContext.tko_ind_explanation.Where(x => x.fk_template_id == oldTemplateIdGuid && x.fk_tenant_id == userDetails.tenant_id && x.year == budgetYear && (indicatorsSet.Select(y => y.pk_indicator_code).Contains(x.fk_indicator_code))).ToList();

                if (!isglobal)
                {
                    foreach (var item in newTiEdata)
                    {
                        //check if exists in oldtemplate
                        var old = oldTiEdata.Where(x => x.fk_indicator_code == item.fk_indicator_code).ToList();
                        if (old.Count > 0)
                        {
                            //update
                            tko_ind_explanation tie = old.First();
                            tie.document_flag = item.document_flag;
                            dbContext.SaveChanges();
                        }
                        else
                        {
                            //insert
                            tko_ind_explanation tie = new tko_ind_explanation
                            {
                                fk_indicator_code = item.fk_indicator_code,
                                year = budgetYear,
                                fk_tenant_id = userDetails.tenant_id,
                                description = string.Empty,
                                document_flag = item.document_flag,
                                locked_flag = item.locked_flag,
                                indicator_type = item.indicator_type,
                                updated = DateTime.UtcNow,
                                updated_by = userDetails.pk_id,
                                blob_url_history = string.Empty,
                                fk_template_id = oldTemplateIdGuid
                            };
                            dbContext.tko_ind_explanation.Add(tie);
                            dbContext.SaveChanges();
                        }
                        if (item.document_flag)
                        {
                            _kostra.SaveSectionConfig(user, item.fk_indicator_code, Convert.ToString(oldTemplateIdGuid));
                        }
                    }
                    //other data in old - updatedocflag to 0
                    List<string> indinNew = newTiEdata.Select(x => x.fk_indicator_code).ToList();
                    var todata = oldTiEdata.Where(x => !indinNew.Contains(x.fk_indicator_code)).ToList();
                    todata.ForEach(a =>
                    {
                        a.document_flag = false;
                    });
                    dbContext.SaveChanges();
                }
                else
                {
                    //get data from gko_template_definitions
                    var newTgTdata = dbContext.gko_template_definitions.Where(x => x.fk_global_temp_id == newTemplateIdGuid && (indicatorsSet.Select(y => y.pk_indicator_code).Contains(x.fk_indicator_code))).ToList();
                    foreach (var item in newTgTdata)
                    {
                        //check if exists in oldtemplate
                        var old = oldTiEdata.Where(x => x.fk_indicator_code == item.fk_indicator_code).ToList();
                        if (old.Count > 0)
                        {
                            //update
                            tko_ind_explanation tie = old.First();
                            tie.document_flag = item.document_flag_ != 0;
                            dbContext.SaveChanges();
                        }
                        else
                        {
                            //insert
                            tko_ind_explanation tie = new tko_ind_explanation();
                            tie.fk_indicator_code = item.fk_indicator_code;
                            tie.year = budgetYear;
                            tie.fk_tenant_id = userDetails.tenant_id;
                            tie.description = string.Empty;
                            tie.blob_url_history = string.Empty;
                            tie.document_flag = item.document_flag_ != 0;
                            tie.locked_flag = false;
                            tie.indicator_type = item.indicator_type;
                            tie.updated = DateTime.UtcNow;
                            tie.updated_by = userDetails.pk_id;
                            tie.fk_template_id = oldTemplateIdGuid;
                            dbContext.tko_ind_explanation.Add(tie);
                            dbContext.SaveChanges();
                        }
                        if (item.document_flag_ == 1)
                        {
                            _kostra.SaveSectionConfig(user, item.fk_indicator_code, Convert.ToString(oldTemplateIdGuid));
                        }
                    }
                    //other data in old - updatedocflag to 0
                    List<string> indinNew = newTgTdata.Select(x => x.fk_indicator_code).ToList();
                    var todata = oldTiEdata.Where(x => !indinNew.Contains(x.fk_indicator_code)).ToList();
                    todata.ForEach(a =>
                    {
                        a.document_flag = false;
                    });
                    dbContext.SaveChanges();
                }

                ////////////Section Config
                var sectiondataold = dbContext.tco_section_config.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.fk_template_id == oldTemplateIdGuid && (indicatorsSet.Select(y => y.pk_indicator_code).Contains(x.fk_indicator_code))).ToList();
                var sectiondatanew = dbContext.tco_section_config.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.fk_template_id == newTemplateIdGuid && (indicatorsSet.Select(y => y.pk_indicator_code).Contains(x.fk_indicator_code))).ToList();
                foreach (var item in sectiondatanew)
                {
                    var old = sectiondataold.Where(x => x.fk_indicator_code == item.fk_indicator_code && x.section_id == item.section_id).ToList();
                    if (old.Count > 0)
                    {
                        //Delete
                        var deldata = dbContext.tco_section_config.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.fk_indicator_code == item.fk_indicator_code && x.section_id == item.section_id && x.fk_template_id == oldTemplateIdGuid).FirstOrDefault();
                        dbContext.tco_section_config.Remove(deldata);
                        dbContext.SaveChanges();
                    }
                    //Insert
                    tco_section_config tsc = new tco_section_config();
                    tsc.fk_tenant_id = userDetails.tenant_id;
                    tsc.fk_template_id = oldTemplateIdGuid;
                    tsc.fk_indicator_code = item.fk_indicator_code;
                    tsc.section_id = item.section_id;
                    tsc.config = item.config;
                    tsc.include_in_document = item.include_in_document;
                    tsc.updated = DateTime.UtcNow;
                    tsc.updated_by = userDetails.pk_id;
                    dbContext.tco_section_config.Add(tsc);
                    dbContext.SaveChanges();
                }
                result = clsConstants.OperationStatus.Success.ToString();
                return result;
            }
            catch
            {
                result = clsConstants.OperationStatus.OperationFailed.ToString();
                return result;
            }
        }

        public async Task<string> FormatDataForExportAsync(string userId, GridInput input, bool treeView)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            JArray gridata = await GetDataForGrid(userId, input);
            List<GridColumnHelper> columns = GetGridColumn(input, userId);
            JArray finalColumns = new JArray();
            dynamic retVal = new JObject();
            // JArray columns = new JArray();
            JArray dataSource = new JArray();

            var fields = columns.Select(x => x.field).ToList();
            var titles = columns.Select(x => x.title).ToList();
            List<string> DataCol = new List<string>();
            CultureInfo ci = CultureInfoFactory.CreateCulture(userDetails.language_preference);
            foreach (var item in gridata)
            {
                dynamic ds = new JObject();
                string id = item["id"].Value<string>();

                if (treeView)
                {
                    ds.id = id;
                    if (string.IsNullOrEmpty(item["parentId"].ToString()))
                    {
                        ds.parentId = null;
                    }
                    else
                    {
                        ds.parentId = item["parentId"].Value<int>();
                    }
                }
                ds.indicatorDescription = item["indicatorDescription"].ToString();

                if (item["dataSource"] != null)
                {
                    ds.dataSource = item["dataSource"].ToString();
                }
                DataCol = treeView ? new List<string>() : DataCol;

                string rowFormat = item["numberType"] != null ? item["numberType"].ToString() : string.Empty;
                int regionCounter = (titles.Count - 2);
                for (int x = 1; x <= regionCounter; x++)
                {
                    if (!string.IsNullOrEmpty(rowFormat))
                    {
                        if (item["region" + x].ToString() != "")
                        {
                            double val = double.Parse(item["region" + x].ToString());
                            ds["region" + (x)] = val.ToString(rowFormat, ci);
                        }
                        else
                        {
                            ds["region" + (x)] = " ";
                        }
                    }
                    else
                    {
                        DataCol.Add("region" + x);
                    }
                }

                dataSource.Add(ds);
            }

            for (int i = 0; i < fields.Count; i++)
            {
                dynamic col = new JObject();
                col.field = fields[i].ToString();
                col.title = titles[i].ToString();
                col.style = "";
                if (DataCol != null && DataCol.Contains(fields[i].ToString()))
                {
                    col.type = "number";
                    col.format = "decimal";
                }
                finalColumns.Add(col);
            }

            retVal.dataSource = dataSource;
            retVal.columns = finalColumns;

            return retVal.ToString();
        }

        private List<string> GetConnectedIndicators(string unGoalId)
        {
            var dbContext = _utility.GetTenantDBContext();
            if (string.IsNullOrEmpty(unGoalId))
            {
                return dbContext.gmd_un_indicator_mapping.Select(x => x.fk_indicator_code).ToList();
            }
            else
            {
                return dbContext.gmd_un_indicator_mapping.Where(x => x.fk_un_goal_id == unGoalId).Select(x => x.fk_indicator_code).ToList();
            }
        }
    }
}