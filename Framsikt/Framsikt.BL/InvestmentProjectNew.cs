#pragma warning disable CS8625
#pragma warning disable CS8629

#pragma warning disable CS8600
#pragma warning disable CS8601
#pragma warning disable CS8602
#pragma warning disable CS8603
#pragma warning disable CS8604

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Framsikt.BL.Constants;
using System.Threading.Tasks;
using Framsikt.BL.Core.Helpers;
using Framsikt.BL.Core.Helpers.ApplicationLogHelpers;
using Framsikt.BL.Helpers;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Framsikt.Entities.Core;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using static Framsikt.BL.Helpers.clsConstants;
using KeyValuePair = Framsikt.BL.Helpers.KeyValuePair;
using Framsikt.BL.Helpers.CkEditorHelpers;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Mvc;
using Serilog.Sinks.MSSqlServer;
using static Microsoft.ApplicationInsights.MetricDimensionNames.TelemetryContext;
using System.Dynamic;

using Microsoft.EntityFrameworkCore;
using System.Collections.ObjectModel;
using Aspose.Cells;
using System.Diagnostics.Metrics;
namespace Framsikt.BL.Core
{
    public class InvestmentProjectNew : IInvestmentProjectNew
    {
        private readonly IUtility _utility;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IInvestmentProject _invProj;
        private readonly IFinUtility _finUtility;
        private readonly ICKEditorExtensions _editorExtensions;
        private readonly IAppDataCache _cache;
        private readonly IInvestments _investments;
        private readonly IConsequenceAdjustedBudget _pconseq;

        private readonly IFinancingProject _finProj;

        public const int BListInvStatus = 3;
        private const int _approvedCostYear = -1;
        private const int _costEstimateYear = -2;

        public InvestmentProjectNew(IUtility util, IUnitOfWork uow, IInvestmentProject invProj, IFinUtility finUtility, ICKEditorExtensions editorExtensions, IAppDataCache cache, IConsequenceAdjustedBudget consequenceAdjusted, IFinancingProject finProj,IInvestments investments)
        {
            this._utility = util;
            this._unitOfWork = uow;
            _invProj = invProj;
            _finUtility = finUtility;
            _pconseq = consequenceAdjusted;
            _editorExtensions = editorExtensions ?? throw new ArgumentNullException(nameof(editorExtensions));
            _cache = cache;
            _investments = investments;
            _finProj = finProj;
        }
        public static class ConstantKeys
        {
            public const string budprop_inv_sel_yr = "SELECTED_YEAR_CHOICE_BUDPROP_INV";
            public const string budprop_inv_4_yrs = "BUD_PROP_INV_COLS_VIEW_4_YEARS";
            public const string budprop_inv_10years = "BUD_PROP_INV_COLS_VIEW_10_YEARS";
            public const int _approvedCostYear = -1;
            public const int _costEstimateYear = -2;
            public const string bp_inv_summ_grid_col = "BP_INV_DETAIL_SUMMARY_GRID_COL_SEL";
            public const string enableInv20Years = "Enable_INV_20_Year_column";
            public const string budprop_inv_op_expense_sel_yr = "BUD_RPOP_INV_OP_EXPENSE_COL";
            public const string budprop_inv_op_expense_4_yr = "BUD_PROP_INV_OP_EXPENSE_COLS_VIEW_4_YEARS";
            public const string budprop_inv_op_expense_10_yr = "BUD_PROP_INV_OP_EXPENSE_COLS_VIEW_10_YEARS";
            public const string bp_inv_register_grid_col = "BP_INV_DETAIL_REGISTER_GRID_COL_SEL";
    }
    #region public 
    public async Task<BudPropInvColumnSelector> GetInvestmentColumnSelectorAsync(string userId, int budgetYear, List<ColumnSelectorColumn> allColumns, string yearSelector)
        {
            UserData user = await _utility.GetUserDetailsAsync(userId);
            string paramValue = await _utility.GetParameterValueAsync(userId, "Evaluation_blist");
            bool hasEvaluationSetup = !paramValue.IsNullOrEmpty() && Convert.ToBoolean(paramValue) ? true : false;
            List<KeyValuePair> columsconfig = new List<KeyValuePair>();
            var ColumnInfoForOverViewGrid = allColumns;
            var chapterFlag =  (await _unitOfWork.InvestmentProjectRepository.GetChapterAttributeAsync(user.tenant_id, "CHAPTER", 1)).Count > 0 &&
                                (await _unitOfWork.InvestmentProjectRepository.GetChapterAttributeRelationAsync(user.tenant_id, "CHAPTER", "DEPARTMENTS")).Count > 0 ? false : true;
            if (chapterFlag)
            {
                ColumnInfoForOverViewGrid.RemoveAll(x => x.key == "Chapter");
            }
            if (!hasEvaluationSetup)
            {
                ColumnInfoForOverViewGrid.RemoveAll(x => x.key == "EvaluationStatus");
            }
            List<string> nonDefaultCol = ColumnInfoForOverViewGrid.Where(z => !z.isDefault).Select(x => x.key).ToList();
            if (hasEvaluationSetup)
            {
                nonDefaultCol.Add("EvaluationStatus");
            }
            string yearSelected = string.Empty;
            string yearFlagName = ConstantKeys.budprop_inv_sel_yr;
            var yearSelectorTenant = (await _unitOfWork.InvestmentProjectRepository.GetTcoApplicationFlagDataAsync(user.tenant_id, yearFlagName, 0, 0)).FirstOrDefault();
            if (string.IsNullOrEmpty(yearSelector) && yearSelectorTenant != null)
            {
                //get selected year value from blob when year is not sent
                yearSelectorTenant = (await _unitOfWork.InvestmentProjectRepository.GetTcoApplicationFlagDataAsync(user.tenant_id, yearFlagName, 0, 0)).FirstOrDefault();
                yearSelected = await GetYearConfigFromBlobTableAsync(user, yearSelected, yearSelectorTenant);
            }
            else if (yearSelectorTenant == null && string.IsNullOrEmpty(yearSelector))
            {
                yearSelected = "4";
            }
            else
            {
                yearSelected = yearSelector;
            }
            string flagName = string.Empty;
            if (yearSelected == "10")
            {
                flagName = ConstantKeys.budprop_inv_10years;
            }
            else
            {
                flagName = ConstantKeys.budprop_inv_4_yrs;
            } 

            List<ColumnSelectorColumn> mainColumnsConfig = new();

            //passing budget_year as 0, to get the same column config across different budget_year (bug_id : 177092)
            var activityColsConfig = await _utility.GetApplicationFlag(userId, flagName, "-1", 0);

            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(user.language_preference, user.user_name, "FinancialPlan");

            if (activityColsConfig != null)
            {
                Guid? flagGuid = activityColsConfig.flag_guid;

                if (flagGuid != null)
                {
                    clsTenantCloudTableEntity updateEntity = await _utility.GetCloudTableContentAsync("WADTenantData", user.tenant_id.ToString(), flagGuid.ToString());
                    if (updateEntity != null)
                    {
                        JArray columnsArray = new JArray();
                        columnsArray = JArray.FromObject(JsonConvert.DeserializeObject(updateEntity.data.ToString()));
                        List<ColumnSelectorColumn> columnList = columnsArray.ToObject<List<ColumnSelectorColumn>>();
                        // fetch from db and flat json  add the new colum added in flat json, set display value and default value
                        mainColumnsConfig = (from s in ColumnInfoForOverViewGrid
                                             join b in columnList on new { x = s.key, y = s.section }
                                               equals new { x = b.key, y = b.section } into grp
                                             from grp1 in grp.DefaultIfEmpty()
                                             select new ColumnSelectorColumn()
                                             {
                                                 key = grp1 != null ? grp1.key : s.key,
                                                 value = grp1 != null ? grp1.value : s.value,
                                                 isChecked = grp1 != null ? grp1.isChecked : s.isChecked,
                                                 section = grp1 != null ? grp1.section : s.section,
                                                 isDefault = grp1 != null ? grp1.isChecked : s.isChecked,
                                             }).ToList();
                    }
                }
            }
            else
            {
                //fetch from flat json and set cheked  and set display value
                mainColumnsConfig = (from s in ColumnInfoForOverViewGrid
                                     select new ColumnSelectorColumn()
                                     {
                                         key = s.key,
                                         value = s.value,
                                         isChecked = s.isDefault,
                                         section = s.section,
                                         isDefault = s.isDefault,
                                     }).ToList();
            }
            //check for 10 yrs
            //mainColumnsConfig = Checkfor10yrsSetup(yearSelected, mainColumnsConfig);
            mainColumnsConfig = await GetTranslations(user, budgetYear, mainColumnsConfig);
            List<string> section = mainColumnsConfig.Select(z => z.section).Distinct().ToList();
            bool hasPlanModule = await _utility.IsActiveModuleAsync(user.user_name, 11);
            if(!hasPlanModule)
            {
                section.Remove("planstatus");
            }
            BudPropInvColumnSelector colList = new();
            colList.ColumnSelectorSection = new List<ColumnSelectorSection>();
            foreach (var item in section)// create groupped column selector json from flat json
            {
                ColumnSelectorSection temp = new ColumnSelectorSection();
                temp.columns = mainColumnsConfig.Where(z => z.section == item).ToList();
                temp.section = item;
                switch (item) {
                    case "selectYears":
                        temp.name = langStrings["FP_investement_select_year_section"].LangText;
                        break;

                     case "nextTenYears":
                        temp.name = langStrings["FP_investement_columns_years_title"].LangText;
                        break;

                     default:
                        temp.name = langStrings["FP_investement_columns_" + item + "_title"].LangText;
                        break;
                }
                colList.ColumnSelectorSection.Add(temp);
            }
            colList.columnSelectorTitle = langStrings["FP_budprop_investement_columns_title"].LangText;
            colList.columnSelectorDescription = langStrings["FP_investement_columns_selector_desc"].LangText;
            colList.yearSelector = yearSelected;
            return colList;
        }

        public async Task<string> SaveBudPropInvColumnSelector(string userId, int budgetYear, BudPropInvColumnSelector colSelObj)
        {
            string flagName = colSelObj.yearSelector == "10" ? ConstantKeys.budprop_inv_10years : ConstantKeys.budprop_inv_4_yrs;
            if(colSelObj.yearSelector == "10")
            {
                colSelObj.ColumnSelectorSection.RemoveAll(x => x.section == "years");
            }
            else
            {
                colSelObj.ColumnSelectorSection.RemoveAll(x => x.section == "nextTenYears");
            }

            var flatcolumList = new List<ColumnSelectorColumn>();
            foreach (var item in colSelObj.ColumnSelectorSection)
            {
                flatcolumList.AddRange(item.columns);
            }

            String strJsonColumnSet = JsonConvert.SerializeObject(flatcolumList);

            //passing budget_year as 0, to save the same column config across different budget_year (bug_id : 177092)
            return await _utility.SaveColumnsConfigAsync(userId, flagName, strJsonColumnSet, -1, 0);
        }
       
        public async Task<List<KeyValuePair>> GetStatusFinplanData(string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringValuesInv = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "invdetails");
            List<KeyValuePair> filterData = new List<KeyValuePair>();

            filterData.Add(new KeyValuePair
            {
                key = "All",
                value = ((langStringValuesInv.FirstOrDefault(v => v.Key == "cmn_filter_all")).Value).LangText
            });

            filterData.Add(new KeyValuePair
            {
                key = clsConstants.InvestmentOverViewFilter.Inplan.ToString(),
                value = ((langStringValuesInv.FirstOrDefault(v => v.Key == "in_overViewFilter_InPlan")).Value).LangText,
                isChecked = true
            });
            filterData.Add(new KeyValuePair
            {
                key = clsConstants.InvestmentOverViewFilter.Required.ToString(),
                value = ((langStringValuesInv.FirstOrDefault(v => v.Key == "inv_status_RequiredInv")).Value).LangText,
                isChecked = true
            });

            filterData.Add(new KeyValuePair
            {
                key = clsConstants.InvestmentOverViewFilter.Parked.ToString(),
                value = ((langStringValuesInv.FirstOrDefault(v => v.Key == "in_overViewFilter_Parked")).Value).LangText
            });
            filterData.Add(new KeyValuePair
            {
                key = clsConstants.InvestmentOverViewFilter.Deleted.ToString(),
                value = ((langStringValuesInv.FirstOrDefault(v => v.Key == "in_overViewFilter_Deleted")).Value).LangText
            });
            return filterData;
        }
        public async Task<List<KeyValuePair>> GetInvType(string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringValuesInv = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Investment");
            List<KeyValuePair> filterData = new List<KeyValuePair>();

            filterData.Add(new KeyValuePair
            {
                key = InvBudPropAccountType.expenditure.ToString(),
                value = ((langStringValuesInv.FirstOrDefault(v => v.Key == "inv_expend_accounts")).Value).LangText,
                isChecked = true
            });

            filterData.Add(new KeyValuePair
            {
                key = InvBudPropAccountType.earnings.ToString(),
                value = ((langStringValuesInv.FirstOrDefault(v => v.Key == "inv_earnings_accounts")).Value).LangText
            });
            filterData.Add(new KeyValuePair
            {
                key = InvBudPropAccountType.funds.ToString(),
                value = ((langStringValuesInv.FirstOrDefault(v => v.Key == "inv_funds_accounts")).Value).LangText
            });

            filterData.Add(new KeyValuePair
            {
                key = InvBudPropAccountType.all.ToString(),
                value = ((langStringValuesInv.FirstOrDefault(v => v.Key == "inv_all_accounts")).Value).LangText
            });
            return filterData;
        }
        public async Task<JArray> GetInvestmentOverviewGridConfigAsync(string userId, List<KeyValuePair> colToDisplay, int budgetYear,Stream configStream,bool isChangeSelected)
        {
            StreamReader reader = new StreamReader(configStream);
            string config = await reader.ReadToEndAsync();

            JArray colConfig = JArray.Parse(config);

            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Investment");
            Dictionary<string, clsLanguageString> languageStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "invdetails");

            if (isChangeSelected)
            {
                colToDisplay = GetChangeYearColumns(colToDisplay);
            }
            List<string> selectedColumn = colToDisplay.Any()
                ? colToDisplay.Where(z => z.isChecked).Select(x => x.key).ToList()
                : new List<string>(); //get the selected columns
            colConfig = JArray.FromObject(colConfig.Where(z => selectedColumn.Contains(z["field"].ToString())).ToList()); // changes made for the bug : 173341

            //Replace language string specific properties         
            foreach (var col in colConfig)
            {
                switch ((string)col["field"])
                {
                    case "ResOrgNameServiceName":
                        col["title"] = langStrings.FirstOrDefault(x =>
                            x.Key.Equals("investment_mainGrid_resOrgNameServiceName",
                                StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "ServiceIdName":
                        col["title"] = langStrings.FirstOrDefault(x => x.Key.Equals("investment_mainGrid_serviceID",
                            StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "CreatedAt":
                        col["title"] = langStrings.FirstOrDefault(x => x.Key.Equals("investment_mainGrid_createdAt",
                            StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "MainDepartmentName":
                        col["title"] = langStrings.FirstOrDefault(x => x.Key.Equals("investment_mainGrid_serviceArea",
                            StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "StatusTitle":
                        col["title"] = langStrings.FirstOrDefault(x =>
                                x.Key.Equals("investment_mainGrid_status", StringComparison.InvariantCultureIgnoreCase))
                            .Value.LangText;
                        break;

                    case "Tag":
                        col["title"] = langStrings.FirstOrDefault(x =>
                                x.Key.Equals("investment_mainGrid_tag", StringComparison.InvariantCultureIgnoreCase))
                            .Value
                            .LangText;
                        break;

                    case "MainProjectCode":
                        col["title"] = langStrings.FirstOrDefault(x =>
                                x.Key.Equals("investment_mainProjectCode", StringComparison.InvariantCultureIgnoreCase))
                            .Value.LangText;
                        break;

                    case "MainProjectName":
                        col["title"] = langStrings.FirstOrDefault(x =>
                                x.Key.Equals("investment_mainProject", StringComparison.InvariantCultureIgnoreCase))
                            .Value
                            .LangText;
                        break;

                    case "FromPlan":
                    case "StatusPlan":
                        col["title"] = langStrings.FirstOrDefault(x =>
                            x.Key.Equals($"investment_overviewgrid_{(string)col["field"]}",
                                StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "PreviousBudgeted":
                        col["title"] = langStrings.FirstOrDefault(x =>
                                x.Key.Equals("investment_prevBudgeted", StringComparison.InvariantCultureIgnoreCase))
                            .Value
                            .LangText;
                        break;

                    case "CostEstimate":
                        col["title"] = languageStrings.FirstOrDefault(x => x.Key.Equals("hdr_cost_estimate", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "ApprovedCost":
                        col["title"] = langStrings.FirstOrDefault(x =>
                                x.Key.Equals("investment_approvedCost", StringComparison.InvariantCultureIgnoreCase))
                            .Value
                            .LangText;
                        break;

                    case "Year1Amount":
                        col["title"] = budgetYear.ToString();
                        break;

                    case "Year2Amount":
                        col["title"] = (budgetYear + 1).ToString();
                        break;

                    case "Year3Amount":
                        col["title"] = (budgetYear + 2).ToString();
                        break;

                    case "Year4Amount":
                        col["title"] = (budgetYear + 3).ToString();
                        break;

                    case "First4Years":
                        col["title"] = langStrings.FirstOrDefault(x =>
                                x.Key.Equals("investment_first4YearSum", StringComparison.InvariantCultureIgnoreCase))
                            .Value
                            .LangText;
                        break;

                    case "Year5Amount":
                        col["title"] = (budgetYear + 4).ToString();
                        break;

                    case "Year6Amount":
                        col["title"] = (budgetYear + 5).ToString();
                        break;

                    case "Year7Amount":
                        col["title"] = (budgetYear + 6).ToString();
                        break;

                    case "Year8Amount":
                        col["title"] = (budgetYear + 7).ToString();
                        break;

                    case "Year9Amount":
                        col["title"] = (budgetYear + 8).ToString();
                        break;

                    case "Year10Amount":
                        col["title"] = (budgetYear + 9).ToString();
                        break;

                    //case "next6years":
                    //    col["title"] = (BudgetYear + 4).ToString() + "-" + (BudgetYear + 9).ToString();
                    //    break;
                    case "Total":
                        col["title"] = langStrings.FirstOrDefault(x =>
                                x.Key.Equals("cmn_title_total", StringComparison.InvariantCultureIgnoreCase)).Value
                            .LangText;
                        break;

                    case "FinancingSum":
                        col["title"] = langStrings.FirstOrDefault(x =>
                                x.Key.Equals("investment_financingSum", StringComparison.InvariantCultureIgnoreCase))
                            .Value
                            .LangText;
                        break;

                    case "ChangeYear1":
                        col["title"] =
                            langStrings.FirstOrDefault(x =>
                                    x.Key.Equals("inv_title_change", StringComparison.InvariantCultureIgnoreCase)).Value
                                .LangText + " " + (budgetYear).ToString();
                        break;

                    case "ChangeYear2":
                        col["title"] =
                            langStrings.FirstOrDefault(x =>
                                    x.Key.Equals("inv_title_change", StringComparison.InvariantCultureIgnoreCase)).Value
                                .LangText + " " + (budgetYear + 1).ToString();
                        break;

                    case "ChangeYear3":
                        col["title"] =
                            langStrings.FirstOrDefault(x =>
                                    x.Key.Equals("inv_title_change", StringComparison.InvariantCultureIgnoreCase)).Value
                                .LangText + " " + (budgetYear + 2).ToString();
                        break;

                    case "ChangeYear4":
                        col["title"] =
                            langStrings.FirstOrDefault(x =>
                                    x.Key.Equals("inv_title_change", StringComparison.InvariantCultureIgnoreCase)).Value
                                .LangText + " " + (budgetYear + 3).ToString();
                        break;

                    case "ChangeYear5":
                        col["title"] =
                            langStrings.FirstOrDefault(x =>
                                    x.Key.Equals("inv_title_change", StringComparison.InvariantCultureIgnoreCase)).Value
                                .LangText + " " + (budgetYear + 4).ToString();
                        break;

                    case "ChangeYear6":
                        col["title"] =
                            langStrings.FirstOrDefault(x =>
                                    x.Key.Equals("inv_title_change", StringComparison.InvariantCultureIgnoreCase)).Value
                                .LangText + " " + (budgetYear + 5).ToString();
                        break;

                    case "ChangeYear7":
                        col["title"] =
                            langStrings.FirstOrDefault(x =>
                                    x.Key.Equals("inv_title_change", StringComparison.InvariantCultureIgnoreCase)).Value
                                .LangText + " " + (budgetYear + 6).ToString();
                        break;

                    case "ChangeYear8":
                        col["title"] =
                            langStrings.FirstOrDefault(x =>
                                    x.Key.Equals("inv_title_change", StringComparison.InvariantCultureIgnoreCase)).Value
                                .LangText + " " + (budgetYear + 7).ToString();
                        break;

                    case "ChangeYear9":
                        col["title"] =
                            langStrings.FirstOrDefault(x =>
                                    x.Key.Equals("inv_title_change", StringComparison.InvariantCultureIgnoreCase)).Value
                                .LangText + " " + (budgetYear + 8).ToString();
                        break;

                    case "ChangeYear10":
                        col["title"] =
                            langStrings.FirstOrDefault(x =>
                                    x.Key.Equals("inv_title_change", StringComparison.InvariantCultureIgnoreCase)).Value
                                .LangText + " " + (budgetYear + 9).ToString();
                        break;

                    case "SelectedYearColTotal":
                        col["title"] = langStrings.FirstOrDefault(x =>
                                x.Key.Equals("inv_selectedYearColTotal", StringComparison.InvariantCultureIgnoreCase))
                            .Value
                            .LangText;
                        break;

                    case "OperationalExpense":
                        col["title"] = langStrings.FirstOrDefault(x =>
                                x.Key.Equals("inv_operationalExpense", StringComparison.InvariantCultureIgnoreCase))
                            .Value
                            .LangText;
                        break;

                    case "Chapter":
                        col["title"] = langStrings.FirstOrDefault(x =>
                            x.Key.Equals("inv_chapter", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "InvestmentPhaseName":
                        col["title"] = langStrings.FirstOrDefault(x =>
                                x.Key.Equals("invPhaseNameColTitle", StringComparison.InvariantCultureIgnoreCase)).Value
                            .LangText;
                        break;
                }
            }
            return colConfig;
        }

        public async Task<InvProjGridResultHelper> GetInvestmentOverviewGridData(string userId, InvProjGridInputHelper input)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            InvProjGridResultHelper finalResult = new();
            var langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Investment");
            var langStringsBudgetProposal = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "BudgetProposal");
            InvProjectFormatHelperObject formatHelperObj = new InvProjectFormatHelperObject();
            int orgBudgetFlag = ((input.PageType == InvestmentPageType.FinPlanInvestment || input.PageType == InvestmentPageType.BudgetProposalInvestment) ? 1 : 0);
            formatHelperObj.orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userDetails.user_name, _utility.GetForecastPeriod(input.BudgetYear, 1));

            formatHelperObj.lstAllDepartments = await _utility.GetDepartmentsFromTcoOrgHierarchyTableAsync(formatHelperObj.orgVersionContent, userId,input.Level1OrgId, input.Level2OrgId,
                                                input.Level3OrgId, input.Level4OrgId,input.Level5OrgId);

            int budgetYear = input.BudgetYear;

            var BudgetChangeInfoResult =  _unitOfWork.InvestmentProjectNewRepository.GetAllBudgetChangeForOverViewGridAsync(userDetails.tenant_id, orgBudgetFlag, input.BudgetYear);
            var tenantOrgDataResult = _unitOfWork.InvestmentProjectNewRepository.GetMainTenantBasedonSubTenantAsync(userDetails.tenant_id);
            var userDataResult = _unitOfWork.InvestmentProjectRepository.GetUserDetailsParallelReadAsync(userDetails.tenant_id);
            var tfpDataResult = _unitOfWork.InvestmentProjectRepository.GetTfpTransDetailDataAsync(userDetails.tenant_id, budgetYear);
            var tfpDeleteDataResult = _unitOfWork.InvestmentProjectRepository.GetTfpDeleteDetailDataAsync(userDetails.tenant_id, budgetYear);
            var tfpTempDataResult = _unitOfWork.InvestmentProjectRepository.GetTfpTempDetailDataAsync(userDetails.tenant_id, budgetYear);

            var tcoServiceValues = _unitOfWork.InvestmentProjectRepository.GetTcoServiceValuesDataAsync(userDetails.tenant_id);
            var tcoActionTags = _unitOfWork.InvestmentProjectRepository.GetTcoActionTagsDataAsync(userDetails.tenant_id);
            var fp1FlagValue = _unitOfWork.InvestmentProjectRepository.GetFPLevelOneFlagValueAsync(userDetails.tenant_id);

            await Task.WhenAll(userDataResult, tfpDataResult, tfpDeleteDataResult, tfpTempDataResult, tcoServiceValues, tcoActionTags, fp1FlagValue, tenantOrgDataResult,BudgetChangeInfoResult);

            List<string> operationalExpeseDataMainProjectProject = GetOperationalExpeseDataAsync(tfpDataResult.Result, tfpDeleteDataResult.Result, tfpTempDataResult.Result);

            // get Table data
            formatHelperObj.tcoServiceValues = tcoServiceValues.Result.ToList();
            formatHelperObj.tcoActionTags = tcoActionTags.Result.ToList();
            formatHelperObj.fp1FlagValue = fp1FlagValue.Result;
            var tenantOrgData = tenantOrgDataResult.Result;
            var BudgetChangeInfoData = BudgetChangeInfoResult.Result;

            var rawdata = await GetRawbaseDataAsync(userDetails.tenant_id, budgetYear, langStringsBudgetProposal);

            IEnumerable<InvProjOverviewGridHelper> data = FormatRawDBDataToYearWiseList(rawdata, budgetYear, input.ChangeId, input.PageType, input.UserAdjustmentCode);

            ClsOrgVersionSpecificContent tenantOrgVersionContent = tenantOrgData != null ? _utility.GetOrgVersionBasedOnTenant(tenantOrgData.fk_tenant_id, _utility.GetForecastPeriod(input.BudgetYear, 1)) : new ClsOrgVersionSpecificContent();
            data = await GetOrgNameCreatedAt(data, formatHelperObj.orgVersionContent, tenantOrgVersionContent, userDetails.tenant_id);

            //Filter data based on user adjustment code only for investment changes page
            List<string> userAdjCode = await GetAdjustmentCodesAsync(userId, input, budgetYear, true);
            data = data.Where(x => userAdjCode.Contains(x.UserAdjustmentCode)).ToList();

            List<int> changeIds = BudgetChangeInfoData.Select(x => x.pk_change_id).ToList();
            data = data.Where(x => changeIds.Distinct().Contains(x.ChangeId));
            List<string> lineItemTocConsider = new List<string>() { "1010", "1020", "1030", "1040" };
            List<string> mainProjectToShow = data.Where(x => lineItemTocConsider.Contains(x.LineItemId)).Select(x => x.MainProjectCode).ToList();
            data = data.Where(x => mainProjectToShow.Contains(x.MainProjectCode));

            var result = await FormatInvestmentOverviewGridAsync(userId, data, input, formatHelperObj, userDataResult.Result, operationalExpeseDataMainProjectProject,tenantOrgVersionContent, formatHelperObj.orgVersionContent);

            var filterData = GetInvGridFilterData(result);
            finalResult.GridData = result;
            finalResult.FilterData = filterData;
            return finalResult;
        }


        public async Task<string> GenerateTemporaryMainProjCodeAsync(string userId, int budgetYear, string mainProjName, int endYear, string orgId = "", int orgLevel = 0, int? priorityKey = null)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            tco_counters counterValue = await _unitOfWork.InvestmentProjectRepository.GetCounterValueAsync(userDetails.tenant_id, CounterId.counterId);
            
            if (counterValue == null)
                return clsConstants.ERROR_CODE.NO_COUNTER_CONFIGURED.ToString();

            if (counterValue.value_to < counterValue.next_value)
                return clsConstants.ERROR_CODE.COUNTER_REACHED_MAX_VALUE.ToString();
               
            string tempMainProjCode = counterValue.prefix + "-" + counterValue.next_value;
            counterValue.next_value = counterValue.next_value + 1;
            tco_main_projects tempMainProj = GetMainProjectObject(budgetYear, mainProjName, endYear, userDetails, tempMainProjCode, orgId, orgLevel, priorityKey);
           
            _unitOfWork.GenericRepo.Add(tempMainProj);
            await _unitOfWork.CompleteAsync();
            var projectsSetupData = (await _unitOfWork.InvestmentProjectRepository.GetProjectsSetUpDataAsync(userDetails.tenant_id)).ToList();
            if (projectsSetupData.FirstOrDefault(g => g.pk_project_code == tempMainProjCode) == null)
            {
                tco_projects_setup projectSetUpData = new tco_projects_setup()
                {
                    fk_tenant_id = userDetails.tenant_id,
                    pk_project_code = tempMainProjCode,
                    is_temp = true,
                    updated = DateTime.UtcNow,
                    updated_by = userDetails.pk_id
                };
                _unitOfWork.GenericRepo.Add(projectSetUpData);
                await _unitOfWork.CompleteAsync();
            }   
            tco_projects tempProj = GetTcoProjectObject(budgetYear, mainProjName, endYear, userDetails, tempMainProjCode);
            _unitOfWork.GenericRepo.Add(tempProj);
            await _unitOfWork.CompleteAsync();
            return tempMainProjCode;
              
        }

        public async Task<bool> DeleteInvestmentAsync(string userId, string mainProjCode)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                var tcoProjResponsibleList = await _unitOfWork.InvestmentProjectManagerRepo.GetTcoProjResponsible(userDetails.tenant_id, mainProjCode);
                var tcoProjSetupList = await _unitOfWork.InvestmentProjectManagerRepo.GetTcoProjsetup(userDetails.tenant_id, mainProjCode);
                var tcoProjList = await _unitOfWork.InvestmentProjectManagerRepo.GetTcoProj(userDetails.tenant_id, mainProjCode);
                var tcoMainProjSetupList = await _unitOfWork.InvestmentProjectManagerRepo.GetTcoMainProjSetup(userDetails.tenant_id, mainProjCode);
                var tcoMainProjList = await _unitOfWork.InvestmentProjectManagerRepo.GetTcoMainProj(userDetails.tenant_id, mainProjCode);
                var tmrProjectStatus = await _unitOfWork.InvestmentProjectManagerRepo.GetTmrProjectStatus(userDetails.tenant_id, mainProjCode);

                _unitOfWork.GenericRepo.BulkDelete(tcoProjResponsibleList.ToList());
                _unitOfWork.GenericRepo.BulkDelete(tcoProjSetupList.ToList());
                _unitOfWork.GenericRepo.BulkDelete(tcoProjList.ToList());
                _unitOfWork.GenericRepo.BulkDelete(tcoMainProjSetupList.ToList());
                _unitOfWork.GenericRepo.BulkDelete(tcoMainProjList.ToList());
                _unitOfWork.GenericRepo.BulkDelete(tmrProjectStatus.ToList());

                await _unitOfWork.CompleteAsync();

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }


        public async Task<List<KeyValueString>> GetPriorityAsync(string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringValuesCommon = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Common");
            List<KeyValueString> priorityDatas = new List<KeyValueString>();
            string priorityText = ((langStringValuesCommon.FirstOrDefault(v => v.Key == "cmn_action_priority_text")).Value).LangText;
            for (int i = 0; i <= 100; i++)
            {
                KeyValueString priorityData = new KeyValueString
                {
                    KeyId = i,
                    ValueString = priorityText + "-" + i.ToString()
                };
                priorityDatas.Add(priorityData);
            }
            return priorityDatas;
        }

        public async Task<bool> UpdateInvPopUpChanges(string userId, UpdatePopUpChanges request)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            tco_main_projects mainProjectData = await _unitOfWork.InvestmentProjectRepository.GetMainProjDetailsAsync(userDetails.tenant_id, request.BudgetYear, request.MainProjectCode);

            if(mainProjectData != null)
            {
                
                mainProjectData.main_project_name = request.MainProjName;
                mainProjectData.priority = request.PriorityKey;
                mainProjectData.updated = DateTime.UtcNow;
                mainProjectData.updated_by = userDetails.pk_id;

                await _unitOfWork.CompleteAsync();
                return true;
            }
            return false;


        }

        public async Task<List<BudPropInvDetailLeftMenuData>> LeftMenuBudPropInvestmentDetailListAsync(string userId, int budgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            return (await _unitOfWork.InvestmentProjectNewRepository.GetBudPropLeftMenuInvestmentData(userDetails.tenant_id, budgetYear)).ToList();
        }
        public async Task<tco_investments_descriptions> GetInvDetailDescriptionAsync(string userId, int budgetYear, string mainProjCode, string descriptionType, Guid phaseId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            return await _unitOfWork.InvestmentProjectNewRepository
                .GetInvDetailDescriptionAsync(userDetails.tenant_id, budgetYear, mainProjCode, descriptionType,
                    phaseId);
        }
        public async Task<string> SaveInvDetailDescriptionAsync(string userId, SaveInvDetailDescription descObj)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            tco_investments_descriptions rowData = await GetInvestmentDescriptionForSaveAsync(userId, descObj.BudgetYear, descObj.MainProjCode, descObj.DescriptionType, descObj.BudgetPhaseId);
            Guid descriptionLogId = Guid.NewGuid();
            if (rowData != null)
            {
                if (rowData.description_log_id == null)
                {
                    rowData.description_log_id = descriptionLogId;
                }
                else
                {
                    descriptionLogId = rowData.description_log_id.Value;
                }

                //Get Previously saved text
                string historyData = await _utility.GetHistoryDataAsync(userId, descriptionLogId);
                List<TextEditorHelper> textEditorLog = JsonConvert.DeserializeObject<List<TextEditorHelper>>(historyData);
                if (textEditorLog == null && !string.IsNullOrEmpty(rowData.description))
                {
                    await _utility.SaveTextLogAsync(userId, descriptionLogId, rowData.description, null);
                }
                if (descObj.Description != null)
                {
                    rowData.description = descObj.Description;
                }
                rowData.updated = DateTime.UtcNow;
                rowData.updated_by = userDetails.pk_id;
            }
            else
            {
                tco_investments_descriptions newDescription = new tco_investments_descriptions
                {
                    fk_tenant_id = userDetails.tenant_id,
                    budget_year = descObj.BudgetYear,
                    fk_investment_id = -1,
                    fk_budget_phase_id = descObj.BudgetPhaseId,
                    description_type = string.IsNullOrEmpty(descObj.DescriptionType) ? "budgetphase" : descObj.DescriptionType,
                    description = string.IsNullOrEmpty(descObj.Description) ? string.Empty : descObj.Description,
                    description_log_id = descriptionLogId,
                    fk_main_project_code = descObj.MainProjCode,
                    updated = DateTime.UtcNow,
                    updated_by = userDetails.pk_id
                };
                _unitOfWork.GenericRepo.Add(newDescription);
            }
            await _unitOfWork.CompleteAsync();
            if (descObj.LogHistory)
            {
                await _utility.SaveTextLogAsync(userId, descriptionLogId, descObj.Description, null);
            }
            var result = await GetInvDetailDescriptionAsync(userId, descObj.BudgetYear, descObj.MainProjCode, "external", Guid.Empty);
            return result != null ? _editorExtensions.SanitizeDescriptionForDeletedSuggestions(System.Net.WebUtility.HtmlDecode(Regex.Replace(result.description, "<(.|\n)*?>", ""))) : string.Empty;
        }

        //Special logic while saving the record. There is some data discripancey so we are deleting the extra row from the table
        public async Task<tco_investments_descriptions> GetInvestmentDescriptionForSaveAsync(string userId, int budgetYear, string mainProjCode, string descriptionType, Guid phaseId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var allDescriptions = (await _unitOfWork.InvestmentProjectNewRepository.GetAllInvDetailDescriptionAsync(userDetails.tenant_id, budgetYear, mainProjCode, descriptionType, phaseId)).ToList();

            if (allDescriptions.Count > 1)
            {
                var singleRecord = allDescriptions.FirstOrDefault();
                foreach (var item in allDescriptions.Where(x => x.pk_id != singleRecord.pk_id))
                {
                    _unitOfWork.GenericRepo.Delete(item);
                }
                await _unitOfWork.CompleteAsync();
                return singleRecord;
            }
            else
            {
                return allDescriptions.FirstOrDefault();
            }
        }
        public async Task<JArray> GetBlistInvDetailEditorSidemenu(string userId, int budgetYear,string mainProjCode)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> lStringValuesInvestments = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Investment");
            IEnumerable<DescPopUpSideMenu> budgetPhaseItems = await _invProj.GetBudgetPhaseDataForPopUpAsync(userId, budgetYear);

            dynamic sideMenuList = new JObject();
            JArray sideMenuData = new JArray();
            int counter = 0;

            dynamic arrDataOne = new JObject();
            arrDataOne.key = ++counter;
            arrDataOne.value = ((lStringValuesInvestments.FirstOrDefault(v => v.Key == "inv_description_external")).Value).LangText;
            arrDataOne.investmentId = mainProjCode;
            arrDataOne.budgetphaseid = "";
            arrDataOne.editdesctype = clsConstants.InvestmentDescriptionType.InvestmentDescription;
            arrDataOne.highlight = 1;
            arrDataOne.descType = "IV-Detail";
            sideMenuData.Add(arrDataOne);

            arrDataOne = new JObject();
            arrDataOne.key = ++counter;
            arrDataOne.value = ((lStringValuesInvestments.FirstOrDefault(v => v.Key == "inv_description_internal")).Value).LangText;
            arrDataOne.investmentId = mainProjCode;
            arrDataOne.budgetphaseid = "";
            arrDataOne.editdesctype = clsConstants.InvestmentDescriptionType.InternalDescription;
            arrDataOne.highlight = 1;
            arrDataOne.descType = "Inv-int-desc";
            sideMenuData.Add(arrDataOne);

            JArray budgetPhaseData = new JArray();
            foreach (DescPopUpSideMenu bpItem in budgetPhaseItems)
            {
                dynamic arrDataThreeItem = new JObject();
                arrDataThreeItem.key = ++counter;
                arrDataThreeItem.value = bpItem.budgetPhaseDesc;
                arrDataThreeItem.investmentId = mainProjCode;
                arrDataThreeItem.budgetphaseid = bpItem.budgetPhaseId;
                arrDataThreeItem.editdesctype = clsConstants.InvestmentDescriptionType.BudPhaseInvDesc;
                arrDataThreeItem.highlight = bpItem.budgetPhaseStatus;
                arrDataThreeItem.descType = "";
                budgetPhaseData.Add(arrDataThreeItem);
            }
            sideMenuData.Add(budgetPhaseData);
            return sideMenuData;
        }
        public async Task<BlistInvDetailInfo> GetInvDetailInfoAsync(string userId, string mainProjCode,int budgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            string paramValue = await _utility.GetParameterValueAsync(userId, "FINPLAN_INVESTMENT_LEVEL");
            var data = (await _unitOfWork.InvestmentProjectNewRepository.GetBlistInvDetailInfoAsync(userDetails.tenant_id, mainProjCode, budgetYear, paramValue)).FirstOrDefault();
            Dictionary<string, clsLanguageString> lStringValuesInvestments = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "invdetails");
            data.StatusName = ((lStringValuesInvestments.FirstOrDefault(v => v.Key == "inv_status_EvaInv")).Value).LangText;
            if (data != null)
            {
                //estimated quater formating    
                if (data.EstimatedQuater != null)
                {
                    switch (data.EstimatedQuater.Value.Month)
                    {
                        case 1:
                        case 2:
                        case 3: data.EstimatedQuaterValue = Convert.ToInt16((data.EstimatedQuater.Value.Year) + "1"); break;
                        case 4:
                        case 5:
                        case 6: data.EstimatedQuaterValue = Convert.ToInt16((data.EstimatedQuater.Value.Year) + "2"); break;
                        case 7:
                        case 8:
                        case 9: data.EstimatedQuaterValue = Convert.ToInt16((data.EstimatedQuater.Value.Year) + "3"); break;
                        case 10:
                        case 11:
                        case 12: data.EstimatedQuaterValue = Convert.ToInt16((data.EstimatedQuater.Value.Year) + "4"); break;
                    }
                }
            }
            return data;
        }

        public async Task<BListInvDetailText> GetBlistInvDetailTextInfoAsync(string userId, string mainProjCode, int budgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            string paramValue = await _utility.GetParameterValueAsync(userId, "FINPLAN_INVESTMENT_LEVEL");
            Dictionary<string, clsLanguageString> langStringValuesCommon = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Common");
            string priorityText = ((langStringValuesCommon.FirstOrDefault(v => v.Key == "cmn_action_priority_text")).Value).LangText;
            var data = (await _unitOfWork.InvestmentProjectNewRepository.GetBlistInvDetailTextAsync(userDetails.tenant_id, mainProjCode, budgetYear, paramValue)).FirstOrDefault();
            data.Priority = data.PriorityId != -2 ? priorityText + "-" + data.Priority : "";
            return data;
        }

        public async Task<bool> UpdateInvestmentProjectAsync(BlistInvDetailInfo updateInput, string userId)
        {
            if (!string.IsNullOrEmpty(updateInput.MainProjCode))
            {
                //update main project table
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                tco_main_projects mainProjectData = await _unitOfWork.InvestmentProjectRepository.GetMainProjDetailsAsync(userDetails.tenant_id, updateInput.budgetYear, updateInput.MainProjCode);
                if (mainProjectData != null)
                {
                    string paramValue = await _utility.GetParameterValueAsync(userId, "FINPLAN_INVESTMENT_LEVEL");
                    string deptCode = string.Empty;
                    string funCode = string.Empty;
                    if (!string.IsNullOrEmpty(paramValue))
                    {
                        var paramValue_split = paramValue.Split('_').ToList();
                        switch (paramValue_split[0].ToUpper())
                        {
                            case "SERVICE":
                                funCode = updateInput.DepartmentCode;
                                deptCode = string.Empty;
                                break;

                            default:
                                funCode = string.Empty;
                                deptCode = updateInput.DepartmentCode;
                                break;
                        }
                    }
                    var completionDate = await GetDateFromQuarterValueAsync(updateInput.EstimatedQuaterValue);
                   
                    mainProjectData.fk_department_code = !string.IsNullOrEmpty(deptCode) ? deptCode : mainProjectData.fk_department_code;
                    mainProjectData.fk_Function_code = !string.IsNullOrEmpty(funCode) ? funCode : mainProjectData.fk_Function_code;
                    mainProjectData.inv_status = updateInput.Status;
                    mainProjectData.completion_date = completionDate;
                    mainProjectData.updated = DateTime.UtcNow;
                    mainProjectData.updated_by = userDetails.pk_id;
                    mainProjectData.sync_status = updateInput.sync_status;
                }
                //update setup table
                tco_main_project_setup mainProjSetup = await _unitOfWork.InvestmentProjectNewRepository.GetTcoMainProjectSetupAsync(userDetails.tenant_id, updateInput.MainProjCode);
                if (mainProjSetup != null)
                {
                    mainProjSetup.start_year = updateInput.StartYear;
                    mainProjSetup.position_long = updateInput.Position_long;
                    mainProjSetup.position_lat = updateInput.Position_lat;
                    mainProjSetup.position_address = updateInput.Position_address;
                    mainProjSetup.approval_reference = string.Empty;
                    mainProjSetup.approval_ref_url = string.Empty;
                    mainProjSetup.updated = DateTime.UtcNow;
                    mainProjSetup.updated_by = userDetails.pk_id;
                }
                else
                {
                    mainProjSetup = new tco_main_project_setup
                    {
                        pk_main_project_code = updateInput.MainProjCode,
                        start_year = updateInput.StartYear,
                        position_long = updateInput.Position_long,
                        position_lat = updateInput.Position_lat,
                        position_address = updateInput.Position_address,
                        approval_reference = string.Empty,
                        approval_ref_url = string.Empty,
                        tags = string.Empty,
                        goalId = Guid.Empty,
                        targetId = Guid.Empty,
                        strategyId = 0,
                        prev_actual = 0,
                        updated = DateTime.UtcNow,
                        updated_by = userDetails.pk_id,
                        fk_tenant_id = userDetails.tenant_id,
                        monthrep_flag = false
                    };
                    _unitOfWork.GenericRepo.Add(mainProjSetup);
                    await _unitOfWork.CompleteAsync();
                }
                
                await _unitOfWork.CompleteAsync();
                return true;
            } 
            else{
                return false;
            }
        }
        public async Task<DateTime> GetDateFromQuarterValueAsync(int estCompletionQtr)
        {
            var quartyer = estCompletionQtr % 10;
            int month = 0;
            int year = estCompletionQtr / 10;
            dynamic finisheddate = 0;
            switch (quartyer)
            {
                case 1:
                    month = 3;
                    finisheddate = new DateTime(year, month, 31); break;
                case 2:
                    month = 6;
                    finisheddate = new DateTime(year, month, 30); break;
                case 3:
                    month = 9;
                    finisheddate = new DateTime(year, month, 30); break;
                case 4:
                    month = 12;
                    finisheddate = new DateTime(year, month, 31); break;
            }
            return await Task.FromResult(finisheddate);
        }

        public async Task<ColumnSelector> GetBPInvDetailSummaryGridColumns(string userId, int budgetYear, List<ColumnSelectorColumn> allColumns)
        {
            UserData user = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(user.language_preference, user.user_name, "invdetails");
            ColumnSelector finalData = new();
            string flagName = ConstantKeys.bp_inv_summ_grid_col;

            List<ColumnSelectorColumn> columnsConfig = await GetColumnConfig(userId, flagName, budgetYear, allColumns);

            columnsConfig = GetSummaryColSelTranslations(langStrings, columnsConfig);

            List<string> sections = columnsConfig.Select(a => a.section).Distinct().ToList();

            List<ColumnSelectorSection> colList = new();
            foreach (var section in sections)
            {
                ColumnSelectorSection temp = new();
                temp.columns = columnsConfig.Where(a => a.section == section).ToList();
                temp.name = langStrings["bp_summary_col_sel_" + section].LangText;
                temp.section = section;
                colList.Add(temp);
            }
            
            finalData.ColumnSelectorSection = colList;
            finalData.columnSelectorTitle = langStrings.FirstOrDefault(x => x.Key.Equals("bp_summary_col_sel_title", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
            finalData.columnSelectorDescription = langStrings.FirstOrDefault(x => x.Key.Equals("bp_summary_col_sel_desc", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
            return finalData;
        }

        public async Task<string> SaveBudPropInvSummaryGridColumnSelector(string userId, int budgetYear, ColumnSelector colSelObj)
        {
            string flagName = ConstantKeys.bp_inv_summ_grid_col;

            List<ColumnSelectorColumn> flatcolumList = new();
            foreach (var item in colSelObj.ColumnSelectorSection)
            {
                flatcolumList.AddRange(item.columns);
            }

            String strJsonColumnSet = JsonConvert.SerializeObject(flatcolumList);
            return await _utility.SaveColumnsConfigAsync(userId, flagName, strJsonColumnSet, -1, budgetYear);
        }

        public async Task<string> SaveBudPropInvRegistrationGridColumnSelector(string userId, int budgetYear, ColumnSelector colSelObj)
        {
            string flagName = ConstantKeys.bp_inv_register_grid_col;

            List<ColumnSelectorColumn> flatcolumList = new();
            foreach (var item in colSelObj.ColumnSelectorSection)
            {
                flatcolumList.AddRange(item.columns);
            }

            String strJsonColumnSet = JsonConvert.SerializeObject(flatcolumList);
            return await _utility.SaveColumnsConfigAsync(userId, flagName, strJsonColumnSet, -1, budgetYear);
        }

        public async Task<JObject> GetSummaryTreeGridData(string userId, Stream configStream, SummaryGridInput input)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            string paramValue = await _utility.GetParameterValueAsync(userId, "FINPLAN_INVESTMENT_LEVEL");
            var mpData = (await _unitOfWork.InvestmentProjectNewRepository.GetBlistInvDetailInfoAsync(userDetails.tenant_id, input.mainProjCode, input.budgetYear, paramValue)).FirstOrDefault();
            input.startYear = mpData.StartYear == 0 ? input.budgetYear : mpData.StartYear - 1;
            input.estimatedFinish = mpData.EstimatedQuater != null ? Convert.ToInt16(mpData.EstimatedQuater.Value.Year) + 1 : input.budgetYear + 3;
            dynamic result = new JObject();
            IEnumerable<InvestmentSummaryGrid> summaryData;
            dynamic data;
            switch (input.summaryTabType)
            {
                case InvSummaryTabType.summary:
                    summaryData = await _invProj.GetDataForSummaryTabAsync(userId, input, InvSummaryTabType.summary);
                    data = await FormatSummaryTabAsync(userId, summaryData, input);
                    break;

                case InvSummaryTabType.project:
                    summaryData = await _invProj.GetDataForSummaryTabAsync(userId, input, InvSummaryTabType.project);
                    data = await FormatProjectTabAsync(userId, summaryData, input);
                    break;

                default:
                    summaryData = await _invProj.GetDataForSummaryTabAsync(userId, input,
                        input.summaryTabType.ToString() == InvSummaryTabType.alterCode.ToString()
                            ? InvSummaryTabType.alterCode
                            : InvSummaryTabType.budgetRound);
                    data = await FormatAlterOrBudgetRoundTabAsync(userId, summaryData, input);
                    break;
            }
            result.Add("columns", await FormatSummaryTabColumnsAsync(userId, configStream, input));
            result.Add("data", data);
            return result;
        }

        public async Task<List<SummaryTypeData>> GetSummaryTypeDataAsync(string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStr = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "invdetails");

            return new List<SummaryTypeData>
            {
                new() { Key = InvSummaryTabType.summary, Value = langStr["inv_summary_tab_title"].LangText },
                new() { Key = InvSummaryTabType.alterCode, Value = langStr["inv_change_code_tab_title"].LangText },
                new() { Key = InvSummaryTabType.budgetRound, Value = langStr["inv_budget_round_tab_title"].LangText },
                new() { Key = InvSummaryTabType.project, Value = langStr["inv_project_tab_title"].LangText },
            };
        }

        public async Task<dynamic> GetGoalForInvestmentAsync(string userId, int budgetYear, string orgId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var goalsAndTargetsData = await _unitOfWork.InvestmentProjectNewRepository.GetGoalForInvestmentData(userDetails.tenant_id, budgetYear, orgId);

            dynamic goalsAndTargets = new JObject();
            var targetArray = new JArray();

            var goalsDetails = goalsAndTargetsData
                .GroupBy(g => new { g.GoalId, g.GoalName })
                .Select(g => g.Key)
                .OrderBy(g => g.GoalName)
                .ToList();

            var targetsDetails = goalsAndTargetsData
                .Where(z => !string.IsNullOrEmpty(z.TargetName))
                .OrderBy(x => x.GoalName).ThenBy(y => y.TargetName)
                .Select(a => new
                {
                    TargetId = a.TargetId,
                    TargetName = a.TargetName + (!string.IsNullOrEmpty(a.GoalName) ? " (" + a.GoalName + ")" : string.Empty)
                })
                .ToList();

            foreach (var item in targetsDetails)
            {
                targetArray.Add(new JObject
                {
                    ["KeyId"] = item.TargetId,
                    ["ValueString"] = item.TargetName
                });
            }

            goalsAndTargets.Add("goalsAndTargetsData", JArray.FromObject(goalsAndTargetsData));
            goalsAndTargets.Add("goals", JArray.FromObject(goalsDetails));
            goalsAndTargets.Add("targets", JArray.FromObject(targetsDetails));
            goalsAndTargets.Add("targetsKeyList", targetArray);

            return goalsAndTargets;
        }

        public async Task<JObject> GetDataForStrategyDropdownAsync(string userId, int budgetYear, bool isBudgetProposal)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            var strategyData = await _unitOfWork.InvestmentProjectNewRepository.GetStrategyDropdownDataAsync(userDetails.tenant_id, budgetYear, isBudgetProposal);

            var strategyArray = new JArray(strategyData.Select(item => new JObject
            {
                ["KeyId"] = item.KeyId,
                ["ValueString"] = item.ValueString,
                ["GoalId"] = item.GoalId.ToString() 
            }));

            return new JObject { ["strategyList"] = strategyArray };
        }

        public async Task<List<KeyValueIntKeyDataPair>> GetActionTagsAsync(string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            var actionTags = await (from atg in tenantDbContext.tcoActionTags
                                    where atg.FkTenantId == userDetails.tenant_id
                                    select new KeyValueIntKeyDataPair
                                    {
                                        Key = atg.PkId,
                                        Value = atg.TagDescription
                                    }).OrderBy(x => x.Value).ToListAsync();
            return actionTags;
        }
        public async Task<BlistInvDetailGoalSectionInfo> GetBlistInvDetailGoalSectionInfo(string userId, int budgetYear, string mainProjectCode)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var data = (await _unitOfWork.InvestmentProjectNewRepository.GetBlistInvDetailGoalInfo(userDetails.tenant_id, budgetYear, mainProjectCode)).FirstOrDefault();
            return data ?? new BlistInvDetailGoalSectionInfo
            {
                Goal = string.Empty,
                Target = string.Empty,
                Strategy = "0",
                Tags = string.Empty
            };
        }
        public async Task<bool> UpdateBlistInvDetailGoalInfo(string userId, string mainProjectCode,BlistInvDetailGoalSectionInfo updateInput)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var mainProjectData = await _unitOfWork.InvestmentProjectNewRepository.GetTcoMainProjectSetupAsync(userDetails.tenant_id, mainProjectCode);
            if(mainProjectData != null)
            {
                mainProjectData.goalId = string.IsNullOrEmpty(updateInput.Goal) ? Guid.Empty : Guid.Parse(updateInput.Goal);
                mainProjectData.targetId = string.IsNullOrEmpty(updateInput.Target) ? Guid.Empty : Guid.Parse(updateInput.Target);
                mainProjectData.strategyId = string.IsNullOrEmpty(updateInput.Strategy) ? 0 : Int32.Parse(updateInput.Strategy);
                List<string> lstTags = null;
                if (updateInput.SaveTags != null && updateInput.SaveTags.Any())
                {
                    lstTags = await _pconseq.InsertActionTagsAsync(userId, updateInput.SaveTags);
                }
                mainProjectData.tags = string.IsNullOrEmpty(updateInput.Tags) ? "" : updateInput.Tags;
                await _unitOfWork.CompleteAsync();
                return true;
            }
            return false;
        }

        public async Task<JObject> GetBudgetRoundandPhase(string userId, string orgId, int orgLevel, int budgetYear)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference,
            userDetails.user_name, "BudgetManagement");

            dynamic obj = new JObject();
            obj.fpLevel1Type = "";
            obj.fpLevel1Value = -1;
            obj.fpLevel2Type = "";
            obj.fpLevel2Value = -1;
            IEnumerable<int> userRole = await _utility.GetUserRoleIdsAsync(userId);
                obj.displayGoalStrategyVisionOnly = userRole.Contains(14);
            obj.isRole93 = userRole.Contains(93);
            string paramValueFP1 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_1");
                string paramValueFP2 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_2");

            if (!string.IsNullOrEmpty(paramValueFP1) && !string.IsNullOrEmpty(paramValueFP2) && paramValueFP1.Contains("org_id") && paramValueFP2.Contains("org_id"))
            {
                

                obj.fpLevel1Type = "orgId";
                obj.fpLevel2Type = "orgId";

                obj.fpLevel1Value = paramValueFP1.ToLower() == "org_id_1" ? 1 :
                                    paramValueFP1.ToLower() == "org_id_2" ? 2 :
                                    paramValueFP1.ToLower() == "org_id_3" ? 3 :
                                    paramValueFP1.ToLower() == "org_id_4" ? 4 :
                                    paramValueFP1.ToLower() == "org_id_5" ? 5 : -1;

                obj.fpLevel2Value = paramValueFP2.ToLower() == "org_id_1" ? 1 :
                                    paramValueFP2.ToLower() == "org_id_2" ? 2 :
                                    paramValueFP2.ToLower() == "org_id_3" ? 3 :
                                    paramValueFP2.ToLower() == "org_id_4" ? 4 :
                                    paramValueFP2.ToLower() == "org_id_5" ? 5 : -1;

            }
            else if (!string.IsNullOrEmpty(paramValueFP1) && !string.IsNullOrEmpty(paramValueFP2) && paramValueFP1.Contains("org_id") && paramValueFP2.Contains("service_id"))
            {

                obj.fpLevel1Type = "orgId";
                obj.fpLevel2Type = "serviceId";

                obj.fpLevel1Value = paramValueFP1.ToLower() == "org_id_1" ? 1 :
                                    paramValueFP1.ToLower() == "org_id_2" ? 2 :
                                    paramValueFP1.ToLower() == "org_id_3" ? 3 :
                                    paramValueFP1.ToLower() == "org_id_4" ? 4 :
                                    paramValueFP1.ToLower() == "org_id_5" ? 5 : -1;

                obj.fpLevel2Value = paramValueFP2.ToLower() == "service_id_1" ? 1 :
                                    paramValueFP2.ToLower() == "service_id_2" ? 2 :
                                    paramValueFP2.ToLower() == "service_id_3" ? 3 :
                                    paramValueFP2.ToLower() == "service_id_4" ? 4 :
                                    paramValueFP2.ToLower() == "service_id_5" ? 5 : -1;

            }
            else if (!string.IsNullOrEmpty(paramValueFP1) && !string.IsNullOrEmpty(paramValueFP2) && paramValueFP1.Contains("service_id") && paramValueFP2.Contains("org_id"))
            {
                obj.fpLevel1Type = "serviceId";
                obj.fpLevel2Type = "orgId";

                obj.fpLevel1Value = paramValueFP2.ToLower() == "service_id_1" ? 1 :
                                    paramValueFP2.ToLower() == "service_id_2" ? 2 :
                                    paramValueFP2.ToLower() == "service_id_3" ? 3 :
                                    paramValueFP2.ToLower() == "service_id_4" ? 4 :
                                    paramValueFP2.ToLower() == "service_id_5" ? 5 : -1;

                obj.fpLevel2Value = paramValueFP1.ToLower() == "org_id_1" ? 1 :
                                    paramValueFP1.ToLower() == "org_id_2" ? 2 :
                                    paramValueFP1.ToLower() == "org_id_3" ? 3 :
                                    paramValueFP1.ToLower() == "org_id_4" ? 4 :
                                    paramValueFP1.ToLower() == "org_id_5" ? 5 : -1;

               
            }
            else if (!string.IsNullOrEmpty(paramValueFP1) && paramValueFP1.Contains("org_id") && string.IsNullOrEmpty(paramValueFP2))
            {
               

                obj.fpLevel1Type = "orgId";
                obj.fpLevel2Type = "";

                obj.fpLevel1Value = paramValueFP1.ToLower() == "org_id_1" ? 1 :
                                    paramValueFP1.ToLower() == "org_id_2" ? 2 :
                                    paramValueFP1.ToLower() == "org_id_3" ? 3 :
                                    paramValueFP1.ToLower() == "org_id_4" ? 4 :
                                    paramValueFP1.ToLower() == "org_id_5" ? 5 : -1;

               

               
            }


        var activeBudgetRoundlist = await (from t in tenantDbContext.tfp_budget_changes
                                           where t.fk_tenant_id == userDetails.tenant_id
                                              && t.budget_year == budgetYear
                                              && t.org_budget_flag == 1 && t.status == 1
                                              && (t.workflow_status == 20 || t.workflow_status == 30 || t.workflow_status == 40)
                                           select t).ToListAsync();
        obj.activeBudgetName = string.Empty;
        obj.workFlowStatus = string.Empty;
        obj.workFlowStep = 0;
        if (activeBudgetRoundlist.Any())
        {
            obj.activeBudgetName = activeBudgetRoundlist.FirstOrDefault().budget_name;
            int workFlowStatus = activeBudgetRoundlist.FirstOrDefault().workflow_status;
            obj.workFlowStep = workFlowStatus;
            if (workFlowStatus == 20)
            {
                obj.workFlowStatus = "2 - " + langStrings["WorkFlow_Step2_title"].LangText;
            }
            else if (workFlowStatus == 30)
            {
                obj.workFlowStatus = "3 - " + langStrings["WorkFlow_Step3_title"].LangText;
            }
            else if (workFlowStatus == 40)
            {
                obj.workFlowStatus = "4 - " + langStrings["WorkFlow_Step4_title"].LangText;
            }
        }
        obj.isRole35 = userRole.Contains(35);
        return obj;
        }

        public async Task<List<OperationalExpenseGridData>> GetInvOperationalExpenseGridDataAsync(string userId, OperationalExpenseGridInput inputData)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            IEnumerable<tco_projects> projectList = await _unitOfWork.InvestmentProjectNewRepository.GetProjectsLinkedToMainProjAsync(userDetails.tenant_id, inputData.budgetYear, inputData.mainProjectCode);
            tco_main_projects mainProjectDetails = await _unitOfWork.InvestmentProjectNewRepository.GetMainProjDetailsAsync(userDetails.tenant_id, inputData.budgetYear, inputData.mainProjectCode);
            var langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "Investment");
            var langStringsInv = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "BudgetManagement");

            string bListTag = langStrings["INV_OPExpence_Blist_tag"].LangText;
            List<OperationalExpenseGridData> finalData = new List<OperationalExpenseGridData>();

            int changeId = _finUtility.GetActiveWorkFlowChangeId(userId, inputData.budgetYear);

            Dictionary<string, List<string>> departmentsToHide = await _finUtility.GetDepartmentsToHideAsync(userId,
                    inputData.budgetYear, inputData.orgLevel, inputData.orgId, string.Empty, changeId.ToString())
                ;

            var BlistDeptHide = departmentsToHide.First(x => x.Key == "blistDepartments").Value.ToList();
            string langStrKey = "2b_total_sum_title";
            if (mainProjectDetails.inv_status != null && mainProjectDetails.inv_status.HasValue)
            {
                List<OperationalExpenseGridData> data;
                if(mainProjectDetails.inv_status == 3)
                {
                        data = (await _unitOfWork.InvestmentProjectNewRepository.GetBListActionDataAsync(userDetails.tenant_id,
                            inputData.budgetYear, projectList.Select(x => x.pk_project_code).ToList(), ActionTypes.BListAction,
                            changeId, bListTag, BlistDeptHide)).ToList();
                        finalData.AddRange(data.ToList());
                        langStrKey = "BM_Sum_BListActions";
                }
            }
            if (mainProjectDetails.inv_status != null && mainProjectDetails.inv_status.HasValue)
            {
                OperationalExpenseGridData blistDelSum = new OperationalExpenseGridData
                {
                    ActionId = -1,
                    StatusTag = string.Empty,
                    Status = string.Empty,
                    ActionName = langStringsInv[langStrKey].LangText,
                    Year1Amount = finalData.Where(x => x.StatusTag == "bp-tag-fin").Sum(x => x.Year1Amount),
                    Year2Amount = finalData.Where(x => x.StatusTag == "bp-tag-fin").Sum(x => x.Year2Amount),
                    Year3Amount = finalData.Where(x => x.StatusTag == "bp-tag-fin").Sum(x => x.Year3Amount),
                    Year4Amount = finalData.Where(x => x.StatusTag == "bp-tag-fin").Sum(x => x.Year4Amount),
                    Year5Amount = finalData.Where(x => x.StatusTag == "bp-tag-fin").Sum(x => x.Year5Amount),
                    Year6Amount = finalData.Where(x => x.StatusTag == "bp-tag-fin").Sum(x => x.Year6Amount),
                    Year7Amount = finalData.Where(x => x.StatusTag == "bp-tag-fin").Sum(x => x.Year7Amount),
                    Year8Amount = finalData.Where(x => x.StatusTag == "bp-tag-fin").Sum(x => x.Year8Amount),
                    Year9Amount = finalData.Where(x => x.StatusTag == "bp-tag-fin").Sum(x => x.Year9Amount),
                    Year10Amount = finalData.Where(x => x.StatusTag == "bp-tag-fin").Sum(x => x.Year10Amount)


                };
                finalData.Add(blistDelSum);
            }

            return finalData;
        }

        public async Task<BudPropInvColumnSelector> GetInvOperationalExpenseGridColAsync(string userId, int budgetYear, List<ColumnSelectorColumn> allColumns, string yearSelector)
        {
            UserData user = await _utility.GetUserDetailsAsync(userId);
            List<KeyValuePair> columsconfig = new List<KeyValuePair>();
            var ColumnInfoForOverViewGrid = allColumns;
            
            string yearSelected = string.Empty;
            string yearFlagName = ConstantKeys.budprop_inv_op_expense_sel_yr;
            var yearSelectorTenant = (await _unitOfWork.InvestmentProjectRepository.GetTcoApplicationFlagDataAsync(user.tenant_id, yearFlagName, budgetYear, 0)).FirstOrDefault();
            if (yearSelectorTenant != null)
            {
                yearSelected = await GetYearConfigFromBlobTableAsync(user, yearSelected, yearSelectorTenant);
            }
            else if (yearSelectorTenant == null && string.IsNullOrEmpty(yearSelector))
            {
                yearSelected = "4";
            }
            else
            {
                yearSelected = yearSelector;
            }
            string flagName = string.Empty;
            if (yearSelected == "10")
            {
                flagName = ConstantKeys.budprop_inv_op_expense_10_yr;
            }
            else
            {
                flagName = ConstantKeys.budprop_inv_op_expense_4_yr;
            }

            List<ColumnSelectorColumn> mainColumnsConfig = new();

            var OpExpenseColsConfig = await _utility.GetApplicationFlag(userId, flagName, "0", budgetYear);

            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(user.language_preference, user.user_name, "FinancialPlan");

            if (OpExpenseColsConfig != null)
            {
                Guid? flagGuid = OpExpenseColsConfig.flag_guid;

                if (flagGuid != null)
                {
                    clsTenantCloudTableEntity updateEntity = await _utility.GetCloudTableContentAsync("WADTenantData", user.tenant_id.ToString(), flagGuid.ToString());
                    if (updateEntity != null)
                    {
                        JArray columnsArray = new JArray();
                        columnsArray = JArray.Parse(updateEntity.data.ToString());
                        List<ColumnSelectorColumn> columnList = columnsArray.ToObject<List<ColumnSelectorColumn>>();
                        // fetch from db and flat json  add the new colum added in flat json, set display value and default value
                        mainColumnsConfig = (from s in ColumnInfoForOverViewGrid
                                             join b in columnList on new { x = s.key, y = s.section }
                                               equals new { x = b.key, y = b.section } into grp
                                             from grp1 in grp.DefaultIfEmpty()
                                             select new ColumnSelectorColumn()
                                             {
                                                 key = grp1 != null ? grp1.key : s.key,
                                                 value = grp1 != null ? grp1.value : s.value,
                                                 isChecked = grp1 != null ? grp1.isChecked : s.isChecked,
                                                 section = grp1 != null ? grp1.section : s.section,
                                                 isDefault = grp1 != null ? grp1.isChecked : s.isChecked,
                                             }).ToList();
                    }
                }
            }
            else
            {
                //fetch from flat json and set cheked  and set display value
                mainColumnsConfig = (from s in ColumnInfoForOverViewGrid
                                     select new ColumnSelectorColumn()
                                     {
                                         key = s.key,
                                         value = s.value,
                                         isChecked = s.isDefault,
                                         section = s.section,
                                         isDefault = s.isDefault,
                                     }).ToList();
            }
            //check for 10 yrs
            //mainColumnsConfig = Checkfor10yrsSetup(yearSelected, mainColumnsConfig);
            mainColumnsConfig = await GetTranslations(user, budgetYear, mainColumnsConfig);
            List<string> section = mainColumnsConfig.Select(z => z.section).Distinct().ToList();
            
            BudPropInvColumnSelector colList = new();
            colList.ColumnSelectorSection = new List<ColumnSelectorSection>();
            foreach (var item in section)// create groupped column selector json from flat json
            {
                ColumnSelectorSection temp = new ColumnSelectorSection();
                temp.columns = mainColumnsConfig.Where(z => z.section == item).ToList();
                temp.section = item;
                switch (item)
                {
                    case "selectYears":
                        temp.name = langStrings["FP_investement_select_year_section"].LangText;
                        break;

                    case "nextTenYears":
                        temp.name = langStrings["FP_investement_columns_years_title"].LangText;
                        break;

                    default:
                        temp.name = langStrings["FP_investement_columns_" + item + "_title"].LangText;
                        break;
                }
                colList.ColumnSelectorSection.Add(temp);
            }
            colList.columnSelectorTitle = langStrings["FP_budprop_investement_columns_title"].LangText;
            colList.columnSelectorDescription = langStrings["FP_investement_columns_selector_desc"].LangText;
            colList.yearSelector = yearSelected;
            return colList;
        }

        public async Task<string> saveInvOperationalExpenseGridCol(string userId, int budgetYear, BudPropInvColumnSelector colSelObj)
        {
            Guid selectedColGuid = Guid.NewGuid();
            Guid yearSelectGuid = Guid.NewGuid();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext tendbContext = await _utility.GetTenantDBContextAsync();
            string yearFlagName = ConstantKeys.budprop_inv_op_expense_sel_yr;
            var yearSelectorTenant = (await _unitOfWork.InvestmentProjectRepository.GetTcoApplicationFlagDataAsync(userDetails.tenant_id, yearFlagName, budgetYear, 0)).FirstOrDefault();
            if (yearSelectorTenant != null)
            {
                if (yearSelectorTenant.flag_guid == null)
                {
                    yearSelectorTenant.flag_guid = yearSelectGuid;
                    await tendbContext.SaveChangesAsync();
                }
                else
                {
                    yearSelectGuid = yearSelectorTenant.flag_guid.Value;
                }
            }
            else
            {
                //Create new record
                tco_application_flag tafrec = new tco_application_flag();
                tafrec.fk_tenant_id = userDetails.tenant_id;
                tafrec.flag_name = yearFlagName;
                tafrec.flag_guid = yearSelectGuid;
                tafrec.flag_key_id = "0";
                tafrec.flag_status = 0;
                tafrec.budget_year = budgetYear;
                tafrec.period = 0;
                tafrec.updated_by = userDetails.pk_id;
                tafrec.updated = DateTime.UtcNow;

                await tendbContext.tco_application_flag.AddAsync(tafrec);
                await tendbContext.SaveChangesAsync();
            }
            string flagName = colSelObj.yearSelector == "10" ? ConstantKeys.budprop_inv_op_expense_10_yr : ConstantKeys.budprop_inv_op_expense_4_yr;

            if (colSelObj.yearSelector == "10")
            {
                colSelObj.ColumnSelectorSection.RemoveAll(x => x.section == "years");
            }
            else
            {
                colSelObj.ColumnSelectorSection.RemoveAll(x => x.section == "nextTenYears");
            }

            var flatcolumList = new List<ColumnSelectorColumn>();
            var columnSelectorTenant = (await _unitOfWork.InvestmentProjectRepository.GetTcoApplicationFlagDataAsync(userDetails.tenant_id, flagName, budgetYear, 0)).FirstOrDefault();

            foreach (var item in colSelObj.ColumnSelectorSection)
            {
                flatcolumList.AddRange(item.columns);
            }
            if (columnSelectorTenant != null)
            {
                if (columnSelectorTenant.flag_guid == null)
                {
                    columnSelectorTenant.flag_guid = selectedColGuid;
                }
                else
                {
                    selectedColGuid = columnSelectorTenant.flag_guid.Value;
                }
            }
            else
            {
                //Create new record
                tco_application_flag tafrec = new tco_application_flag();
                tafrec.fk_tenant_id = userDetails.tenant_id;
                tafrec.flag_name = flagName.ToUpper();
                tafrec.flag_guid = selectedColGuid;
                tafrec.flag_key_id = "0";
                tafrec.flag_status = 0;
                tafrec.budget_year = budgetYear;
                tafrec.period = 0;
                tafrec.updated_by = userDetails.pk_id;
                tafrec.updated = DateTime.UtcNow;

                await tendbContext.tco_application_flag.AddAsync(tafrec);
                await tendbContext.SaveChangesAsync();

            }
            await tendbContext.SaveChangesAsync();
            String strJsonColumnSet = JsonConvert.SerializeObject(flatcolumList);
            await _finProj.SaveColumnSelectConfigInBlobStoreAsync(userDetails.pk_id, selectedColGuid, yearSelectGuid, strJsonColumnSet, colSelObj.yearSelector, userDetails.tenant_id);
            return "sucess";

        }


        public async Task<ColumnSelector> GetBPInvDetailRegistrationGridColumns(string userId, int budgetYear, List<ColumnSelectorColumn> allColumns, bool adjCodeExists)
        {
            UserData user = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(user.language_preference, user.user_name, "invdetails");
            ColumnSelector finalData = new();
            string flagName = ConstantKeys.bp_inv_register_grid_col;

            List<ColumnSelectorColumn> columnsConfig = await GetColumnConfig(userId, flagName, budgetYear, allColumns);

            columnsConfig = await GetRegisterColSelTranslations(userId, langStrings, columnsConfig, adjCodeExists);

            List<string> sections = columnsConfig.Select(a => a.section).Distinct().ToList();

            List<ColumnSelectorSection> colList = new();
            foreach (var section in sections)
            {
                ColumnSelectorSection temp = new();
                temp.columns = columnsConfig.Where(a => a.section == section).ToList();
                temp.name = langStrings["bp_register_col_sel_" + section].LangText;
                temp.section = section;
                colList.Add(temp);
            }

            finalData.ColumnSelectorSection = colList;
            finalData.columnSelectorTitle = langStrings.FirstOrDefault(x => x.Key.Equals("bp_register_col_sel_title", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
            finalData.columnSelectorDescription = langStrings.FirstOrDefault(x => x.Key.Equals("bp_register_col_sel_desc", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
            return finalData;
        }

        public async Task<JArray> GetRegistrationGridAsync(string userId, InvRegistrationInput detailGridInput)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(detailGridInput.budgetYear, 1));
            List<string> lstDepartments;
            
            
            lstDepartments = await _utility.GetDepartmentsFromTcoOrgHierarchyTableAsync(orgVersionContent, userId,
                detailGridInput.level1OrgId, detailGridInput.level2OrgId, detailGridInput.level3OrgId,
                detailGridInput.level4OrgId, detailGridInput.level5OrgId);

            IEnumerable<DetailData> transactionData =
                await _unitOfWork.InvestmentProjectRepository.GetProjectTransactionsAsync(userDetails.tenant_id,
                    detailGridInput.fk_main_project_code, detailGridInput.budgetYear, lstDepartments);

           
            transactionData = transactionData.Where(x => (x.budgetChangeYear < detailGridInput.budgetYear) || (x.budgetChangeYear == detailGridInput.budgetYear && x.orgFlag == 1)).ToList();
            transactionData.ToList().ForEach(x => { x.amount = x.amount / 1000; });
            var datas = await FormatDetailGridDataAsync(transactionData, detailGridInput, userId);
            return datas;
        }
        public async Task<tfp_budget_changes> GetBudpropInvBudgetRounds(string userId, int budgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var budgetRound = await _unitOfWork.InvestmentProjectNewRepository.GetBudpropInvBudgetRounds(userDetails.tenant_id, budgetYear);
            return budgetRound;
        }
        public async Task<InvRegistrationDropdownHelper> GetRegistrationDimensionsDropdowns(string userId, InvRegistrationDropdownInputHelper input)
        {
            InvRegistrationDropdownHelper dropdownData = new();
            dropdownData.AccountList = await GetInvRegAccountsList(userId, input.BudgetYear);
            dropdownData.DepartmentList = await GetInvRegDepartmentsList(userId, input);
            dropdownData.FunctionList = await GetInvRegFunctionsList(userId, input.BudgetYear);
            dropdownData.AltercodeList = await GetINvRegAltercodeList(userId);
            dropdownData.FreedimList = await GetInvRegFreedimList(userId);
            dropdownData.AdjustmentCodeList = await GetInvRegAdjustmentCodesList(userId);
            dropdownData.ProjectList = await GetInvRegProjectList(userId,input.MainProjectCode,input.BudgetYear);
            return dropdownData;
        }
        public async Task SaveInvRegistrationGridData(string userId, InvRegistrationSaveHelper input)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(input.BudgetYear, 1));

            List<tmd_acc_defaults> defaultAccounts;
            string invNetAmounts = string.Empty;

            List<string> accountTypes = new List<string> { "ACCOUNT", "DEPARTMENT", "FUNCTION" };
            List<string> linkType = new List<string> { "VAT_COST", "VAT_COMP" };
            defaultAccounts = (await _unitOfWork.InvestmentProjectRepository.GetAccountingDefaultsAsync(userDetails.tenant_id, accountTypes, linkType, orgVersionContent.orgVersion)).ToList();
            invNetAmounts = await _utility.GetParameterValueAsync(userId, "FP_INV_NET_AMOUNTS");

            bool calcVatComp = !string.IsNullOrEmpty(invNetAmounts) && Convert.ToBoolean(invNetAmounts);

            //Update Year Number
            if (input.StartYear != 0)
            {
                UpdateYear(input, input.StartYear);
            }

            //Update alter codes
            InvestmentGridType gridType = input.GridType;

            int actionType = 1000; // operational grid
            var lstAlterCode = await _pconseq.GetAlterCodesAsync(userId, actionType, true, false, false);
            string defaultAlterCode = string.Empty;
            if (lstAlterCode != null && lstAlterCode.Count == 1)
            {
                foreach (var item in lstAlterCode)
                {
                    if (Convert.ToBoolean(item.isDefault))
                    {
                        defaultAlterCode = Convert.ToString(item.key);
                    }
                }
                input.AlterCode = string.IsNullOrEmpty(input.AlterCode) ? defaultAlterCode : input.AlterCode;
            }
            
            tco_user_adjustment_codes orginalAdjCode = new tco_user_adjustment_codes();
            orginalAdjCode = await _unitOfWork.InvestmentProjectRepository.GetOriginalAdjCodeAsync(userDetails.tenant_id, input.BudgetYear);
            if (orginalAdjCode == null)
            {
                dynamic adjCode_Original;
               
                UserAdjustmentCodeInput useradjCodes = new UserAdjustmentCodeInput
                {
                    orginal_flag = true,
                    budgetYear = input.BudgetYear,
                    status = true
                };
                adjCode_Original = await _invProj.GenerateAdjustmentCodeAsync(useradjCodes, userId);
                input.UserAdjustmentCode = Convert.ToString(adjCode_Original.adjCode);
            }
            else
            {
                input.UserAdjustmentCode = Convert.ToString(orginalAdjCode.pk_adj_code);
            }
            //New transaction guid for newly added transaction
            Guid newTransId = Guid.NewGuid();
            if (input.TransId == Guid.Empty)
            {
                input.TransId = newTransId;
                //add new transaction data
                foreach (var item in input.YearAmount)
                {
                    if (item.Value != 0)
                    {
                        Guid newPkId = Guid.NewGuid();
                        tfp_proj_transactions transData = new tfp_proj_transactions
                        {
                            pk_id = newPkId,
                            trans_id = newTransId,
                            fk_tenant_id = userDetails.tenant_id,
                            fk_account_code = input.AccountCode,
                            fk_function_code = input.FunctionCode,
                            fk_department_code = input.DepartmentCode,
                            fk_project_code = input.ProjectCode,
                            free_dim_1 = input.FreeDim1,
                            free_dim_2 = input.FreeDim2,
                            free_dim_3 = input.FreeDim3,
                            free_dim_4 = input.FreeDim4,
                            vat_rate = input.VatRate,
                            vat_refund = input.VatRefund,
                            year = item.year,
                            amount = item.Value * 1000,
                            fk_change_id = input.ChangeId,
                            fk_alter_code = input.AlterCode,
                            fk_adjustment_code = input.AdjustmentCode,
                            fk_user_adjustment_code = input.UserAdjustmentCode,
                            is_vat_row = false,
                            description = input.Description,
                            updated_by = userDetails.pk_id,
                            updated = DateTime.UtcNow
                        };
                        _unitOfWork.GenericRepo.Add(transData);
                        if (item.Value != 0 && gridType != InvestmentGridType.OperationalExpense)
                        {
                            await CalculateVATAsync(userDetails, input, newTransId, newPkId, defaultAccounts, calcVatComp, item.year, item.Value);
                        }
                    }
                }

                //Add row for approved cost. Use a year value of -1
                if (input.ApprovedCost != 0)
                {
                    Guid pkIdAppCost = Guid.NewGuid();
                    tfp_proj_transactions appCostData = new tfp_proj_transactions
                    {
                        pk_id = pkIdAppCost,
                        trans_id = newTransId,
                        fk_tenant_id = userDetails.tenant_id,
                        fk_account_code = input.AccountCode,
                        fk_function_code = input.FunctionCode,
                        fk_department_code = input.DepartmentCode,
                        fk_project_code = input.ProjectCode,
                        free_dim_1 = input.FreeDim1,
                        free_dim_2 = input.FreeDim2,
                        free_dim_3 = input.FreeDim3,
                        free_dim_4 = input.FreeDim4,
                        vat_rate = input.VatRate,
                        vat_refund = input.VatRefund,
                        year = _approvedCostYear,
                        amount = input.ApprovedCost * 1000,
                        fk_change_id = input.ChangeId,
                        fk_alter_code = input.AlterCode,
                        fk_adjustment_code = input.AdjustmentCode,
                        fk_user_adjustment_code = input.UserAdjustmentCode,
                        is_vat_row = false,
                        description = input.Description,
                        updated_by = userDetails.pk_id,
                        updated = DateTime.UtcNow
                    };
                    _unitOfWork.GenericRepo.Add(appCostData);
                }
                //Add row for estimated cost. Use a year value of -2
                if (input.CostEstimate != 0)
                {
                    Guid pkIdAppCost = Guid.NewGuid();
                    tfp_proj_transactions costEstimateData = new tfp_proj_transactions
                    {
                        pk_id = pkIdAppCost,
                        trans_id = newTransId,
                        fk_tenant_id = userDetails.tenant_id,
                        fk_account_code = input.AccountCode,
                        fk_function_code = input.FunctionCode,
                        fk_department_code = input.DepartmentCode,
                        fk_project_code = input.ProjectCode,
                        free_dim_1 = input.FreeDim1,
                        free_dim_2 = input.FreeDim2,
                        free_dim_3 = input.FreeDim3,
                        free_dim_4 = input.FreeDim4,
                        vat_rate = input.VatRate,
                        vat_refund = input.VatRefund,
                        year = _costEstimateYear,
                        amount = input.CostEstimate * 1000,
                        fk_change_id = input.ChangeId,
                        fk_alter_code = input.AlterCode,
                        fk_adjustment_code = input.AdjustmentCode,
                        fk_user_adjustment_code = input.UserAdjustmentCode,
                        is_vat_row = false,
                        description = input.Description,
                        updated_by = userDetails.pk_id,
                        updated = DateTime.UtcNow
                    };
                    _unitOfWork.GenericRepo.Add(costEstimateData);
                }
            }
            else
            {
                //update transaction data
                List<tfp_proj_transactions> projTrans = (await _unitOfWork.InvestmentProjectRepository.GetTransactionByTransIdAsync(userDetails.tenant_id, input.TransId)).ToList();
                if (projTrans.Any())
                {
                    foreach (var item in input.YearAmount)
                    {
                        if (projTrans.FirstOrDefault(x => x.year == item.year) != null)
                        {
                            projTrans.FirstOrDefault(x => x.year == item.year).fk_account_code = input.AccountCode;
                            projTrans.FirstOrDefault(x => x.year == item.year).fk_department_code = input.DepartmentCode;
                            projTrans.FirstOrDefault(x => x.year == item.year).fk_function_code = input.FunctionCode;
                            projTrans.FirstOrDefault(x => x.year == item.year).fk_project_code = input.ProjectCode;
                            projTrans.FirstOrDefault(x => x.year == item.year).free_dim_1 = input.FreeDim1 == null ? string.Empty : input.FreeDim1;
                            projTrans.FirstOrDefault(x => x.year == item.year).free_dim_2 = input.FreeDim2 == null ? string.Empty : input.FreeDim2;
                            projTrans.FirstOrDefault(x => x.year == item.year).free_dim_3 = input.FreeDim3 == null ? string.Empty : input.FreeDim3;
                            projTrans.FirstOrDefault(x => x.year == item.year).free_dim_4 = input.FreeDim4 == null ? string.Empty : input.FreeDim4;
                            projTrans.FirstOrDefault(x => x.year == item.year).vat_rate = input.VatRate;
                            projTrans.FirstOrDefault(x => x.year == item.year).vat_refund = input.VatRefund;
                            projTrans.FirstOrDefault(x => x.year == item.year).is_vat_row = false;
                            projTrans.FirstOrDefault(x => x.year == item.year).fk_adjustment_code = input.AdjustmentCode;
                            projTrans.FirstOrDefault(x => x.year == item.year).fk_adjustment_code = input.UserAdjustmentCode;
                            projTrans.FirstOrDefault(x => x.year == item.year).fk_alter_code = input.AlterCode;
                            projTrans.FirstOrDefault(x => x.year == item.year).description = input.Description;
                            projTrans.FirstOrDefault(x => x.year == item.year).amount = item.Value * 1000;
                            projTrans.FirstOrDefault(x => x.year == item.year).updated = DateTime.UtcNow;
                            projTrans.FirstOrDefault(x => x.year == item.year).updated_by = userDetails.pk_id;

                            if (gridType != InvestmentGridType.OperationalExpense)
                            {
                                await CalculateVATAsync(userDetails, input, projTrans.FirstOrDefault(x => x.year == item.year).trans_id,
                                    projTrans.FirstOrDefault(x => x.year == item.year).pk_id, defaultAccounts, calcVatComp, item.year, item.Value);
                            }
                        }
                        else if (item.Value != 0)
                        {
                            Guid newPkId = Guid.NewGuid();
                            tfp_proj_transactions transData = new tfp_proj_transactions
                            {
                                pk_id = newPkId,
                                trans_id = input.TransId,
                                fk_tenant_id = userDetails.tenant_id,
                                fk_account_code = input.AccountCode,
                                fk_function_code = input.FunctionCode,
                                fk_department_code = input.DepartmentCode,
                                fk_project_code = input.ProjectCode,
                                free_dim_1 = input.FreeDim1 == null ? string.Empty : input.FreeDim1,
                                free_dim_2 = input.FreeDim2 == null ? string.Empty : input.FreeDim2,
                                free_dim_3 = input.FreeDim3 == null ? string.Empty : input.FreeDim3,
                                free_dim_4 = input.FreeDim4 == null ? string.Empty : input.FreeDim4,
                                vat_rate = input.VatRate,
                                vat_refund = input.VatRefund,
                                year = item.year,
                                amount = item.Value * 1000,
                                fk_change_id = input.ChangeId,
                                fk_alter_code = input.AlterCode,
                                fk_adjustment_code = input.AdjustmentCode,
                                fk_user_adjustment_code = input.UserAdjustmentCode,
                                is_vat_row = false,
                                description = input.Description,
                                updated_by = userDetails.pk_id,
                                updated = DateTime.UtcNow
                            };
                            _unitOfWork.GenericRepo.Add(transData);

                            if (gridType != InvestmentGridType.OperationalExpense)
                            {
                                await CalculateVATAsync(userDetails, input, newTransId, newPkId, defaultAccounts, calcVatComp, item.year, item.Value);
                            }
                        }
                    }
                    //Add row for approved cost. Use a year value of -1
                    if (projTrans.FirstOrDefault(x => x.year == _approvedCostYear) != null)
                    {
                        var approvedCostRow = projTrans.FirstOrDefault(x => x.year == _approvedCostYear);
                        approvedCostRow.fk_account_code = input.AccountCode;
                        approvedCostRow.fk_department_code = input.DepartmentCode;
                        approvedCostRow.fk_function_code = input.FunctionCode;
                        approvedCostRow.fk_project_code = input.ProjectCode;
                        approvedCostRow.free_dim_1 = input.FreeDim1 == null ? string.Empty : input.FreeDim1;
                        approvedCostRow.free_dim_2 = input.FreeDim2 == null ? string.Empty : input.FreeDim2;
                        approvedCostRow.free_dim_3 = input.FreeDim3 == null ? string.Empty : input.FreeDim3;
                        approvedCostRow.free_dim_4 = input.FreeDim4 == null ? string.Empty : input.FreeDim4;
                        approvedCostRow.vat_rate = input.VatRate;
                        approvedCostRow.vat_refund = input.VatRefund;
                        approvedCostRow.is_vat_row = false;
                        approvedCostRow.fk_adjustment_code = input.AdjustmentCode;
                        approvedCostRow.fk_user_adjustment_code = input.UserAdjustmentCode;
                        approvedCostRow.fk_alter_code = input.AlterCode;
                        approvedCostRow.description = input.Description;
                        approvedCostRow.amount = input.ApprovedCost * 1000;
                        approvedCostRow.updated = DateTime.UtcNow;
                        approvedCostRow.updated_by = userDetails.pk_id;
                    }
                    else if (input.ApprovedCost != 0)
                    {
                        Guid newPkId = Guid.NewGuid();
                        tfp_proj_transactions transData = new tfp_proj_transactions
                        {
                            pk_id = newPkId,
                            trans_id = input.TransId,
                            fk_tenant_id = userDetails.tenant_id,
                            fk_account_code = input.AccountCode,
                            fk_function_code = input.FunctionCode,
                            fk_department_code = input.DepartmentCode,
                            fk_project_code = input.ProjectCode,
                            free_dim_1 = input.FreeDim1 == null ? string.Empty : input.FreeDim1,
                            free_dim_2 = input.FreeDim2 == null ? string.Empty : input.FreeDim2,
                            free_dim_3 = input.FreeDim3 == null ? string.Empty : input.FreeDim3,
                            free_dim_4 = input.FreeDim4 == null ? string.Empty : input.FreeDim4,
                            vat_rate = input.VatRate,
                            vat_refund = input.VatRefund,
                            year = _approvedCostYear,
                            amount = input.ApprovedCost * 1000,
                            fk_change_id = input.ChangeId,
                            fk_alter_code = input.AlterCode,
                            fk_adjustment_code = input.AdjustmentCode,
                            fk_user_adjustment_code = input.UserAdjustmentCode,
                            is_vat_row = false,
                            description = input.Description,
                            updated_by = userDetails.pk_id,
                            updated = DateTime.UtcNow
                        };
                        _unitOfWork.GenericRepo.Add(transData);
                    }
                    //Add row for estimated cost. Use a year value of -2
                    if (projTrans.FirstOrDefault(x => x.year == _costEstimateYear) != null)
                    {
                        var estimatedCostRow = projTrans.FirstOrDefault(x => x.year == _costEstimateYear);
                        estimatedCostRow.fk_account_code = input.AccountCode;
                        estimatedCostRow.fk_department_code = input.DepartmentCode;
                        estimatedCostRow.fk_function_code = input.FunctionCode;
                        estimatedCostRow.fk_project_code = input.ProjectCode;
                        estimatedCostRow.free_dim_1 = input.FreeDim1 == null ? string.Empty : input.FreeDim1;
                        estimatedCostRow.free_dim_2 = input.FreeDim2 == null ? string.Empty : input.FreeDim2;
                        estimatedCostRow.free_dim_3 = input.FreeDim3 == null ? string.Empty : input.FreeDim3;
                        estimatedCostRow.free_dim_4 = input.FreeDim4 == null ? string.Empty : input.FreeDim4;
                        estimatedCostRow.vat_rate = input.VatRate;
                        estimatedCostRow.vat_refund = input.VatRefund;
                        estimatedCostRow.is_vat_row = false;
                        estimatedCostRow.fk_adjustment_code = input.AdjustmentCode;
                        estimatedCostRow.fk_user_adjustment_code = input.UserAdjustmentCode;
                        estimatedCostRow.fk_alter_code = input.AlterCode;
                        estimatedCostRow.description = input.Description;
                        estimatedCostRow.amount = input.CostEstimate * 1000;
                        estimatedCostRow.updated = DateTime.UtcNow;
                        estimatedCostRow.updated_by = userDetails.pk_id;
                    }
                    else if (input.CostEstimate != 0)
                    {
                        Guid newPkId = Guid.NewGuid();
                        tfp_proj_transactions costEstimateData = new tfp_proj_transactions
                        {
                            pk_id = newPkId,
                            trans_id = input.TransId,
                            fk_tenant_id = userDetails.tenant_id,
                            fk_account_code = input.AccountCode,
                            fk_function_code = input.FunctionCode,
                            fk_department_code = input.DepartmentCode,
                            fk_project_code = input.ProjectCode,
                            free_dim_1 = input.FreeDim1 == null ? string.Empty : input.FreeDim1,
                            free_dim_2 = input.FreeDim2 == null ? string.Empty : input.FreeDim2,
                            free_dim_3 = input.FreeDim3 == null ? string.Empty : input.FreeDim3,
                            free_dim_4 = input.FreeDim4 == null ? string.Empty : input.FreeDim4,
                            vat_rate = input.VatRate,
                            vat_refund = input.VatRefund,
                            year = _costEstimateYear,
                            amount = input.CostEstimate * 1000,
                            fk_change_id = input.ChangeId,
                            fk_alter_code = input.AlterCode,
                            fk_adjustment_code = input.AdjustmentCode,
                            fk_user_adjustment_code = input.UserAdjustmentCode,
                            is_vat_row = false,
                            description = input.Description,
                            updated_by = userDetails.pk_id,
                            updated = DateTime.UtcNow
                        };
                        _unitOfWork.GenericRepo.Add(costEstimateData);
                    }
                }
            }
            await _unitOfWork.CompleteAsync();

        }
        public async Task DeleteInvRegTransaction(Guid transId, string userId)
        {
            var userData = await _utility.GetUserDetailsAsync(userId);
           
            await _unitOfWork.InvestmentProjectNewRepository.DeleteTransactionRegInv(transId, userData.tenant_id);
            await _unitOfWork.CompleteAsync();
        }
        #endregion public 


        #region private

        private async Task<IEnumerable<InvProjOverviewGridHelper>> GetRawbaseDataAsync(int tenantId,int budgetyear, Dictionary<string, clsLanguageString> langString)
        {
            IEnumerable<InvestmentOverviewGridHelper> rawdata = await _unitOfWork.InvestmentProjectRepository.GetInvestmentOveriewDataRawBaseDataAsync(tenantId, budgetyear, langString);
            var result = rawdata.GroupBy(z => new
            {
                z.status,
                z.tag,
                z.mainProjectCode,
                z.mainProjectName,
                z.investmentPhase,
                z.investmentPhaseName,
                z.functionCode,
                z.departmentcode,
                z.userAdjustmentCode,
                z.responDeptCode,
                z.responFuncCode,
                z.changeId,
                z.year,
                z.line_item_id,
                z.line_item_Name,
                z.line_group_id,
                z.mainDepartmentCode,
                z.programCode,
                z.projectCode,
                z.attributeId,
                z.chapter,
                z.evaluationStatus,
                z.parentOrgIdCreatedAt,
                z.parentOrgLevelCreatedAt,
                z.createdAt
            }).Select(x => new InvProjOverviewGridHelper()
            {
                Status = x.Key.status,
                Tag = x.Key.tag,
                MainProjectCode = x.Key.mainProjectCode,
                MainProjectName = x.Key.mainProjectName,
                InvestmentPhase = x.Key.investmentPhase,
                InvestmentPhaseName = x.Key.investmentPhaseName,
                FunctionCode = x.Key.functionCode,
                DepartmentCode = x.Key.departmentcode,
                UserAdjustmentCode = x.Key.userAdjustmentCode,
                ResponDeptCode = x.Key.responDeptCode,
                ResponFuncCode = x.Key.responFuncCode,
                ChangeId = x.Key.changeId,
                Amount = (x.Sum(y => y.ammount)),
                Year = x.Key.year,
                LineItemId = x.Key.line_item_id.ToString(),
                LineItemName = x.Key.line_item_Name.ToString(),
                LineGroupId = x.Key.line_group_id.ToString(),
                MainDepartmentCode = x.Key.mainDepartmentCode,
                ProgramCode = x.Key.programCode,
                ProjectCode = x.Key.projectCode,
                AttributeId = x.Key.attributeId,
                Chapter = x.Key.chapter,
                EvaluationStatus = x.Key.evaluationStatus,
                ParentOrgIdCreatedAt = x.Key.parentOrgIdCreatedAt,
                ParentOrgLevelCreatedAt = x.Key.parentOrgLevelCreatedAt,
                CreatedAt = x.Key.createdAt
            });
            return result;
        }
        private IEnumerable<InvProjOverviewGridHelper> FormatRawDBDataToYearWiseList(
           IEnumerable<InvProjOverviewGridHelper> rawdata, int budgetYear, int currentChangeId,
           InvestmentPageType pageType, string userAdjustmentCode)
        {
            IEnumerable<InvProjOverviewGridHelper> data;
            data = (from d in rawdata
                    select new InvProjOverviewGridHelper()
                    {
                        Status = d.Status,
                        Tag = d.Tag,
                        MainProjectCode = d.MainProjectCode,
                        MainProjectName = d.MainProjectName,
                        InvestmentPhase = d.InvestmentPhase,
                        InvestmentPhaseName = d.InvestmentPhaseName,
                        ProjectCode = d.ProjectCode,
                        ProjectName = d.ProjectName,
                        AccountCode = d.AccountCode,
                        FunctionCode = d.FunctionCode,
                        DepartmentCode = d.DepartmentCode,
                        UserAdjustmentCode = d.UserAdjustmentCode,
                        ResponDeptCode = d.ResponDeptCode,
                        ResponFuncCode = d.ResponFuncCode,
                        ChangeId = d.ChangeId,
                        Amount = d.Amount,
                        Year = d.Year,
                        LineItemId = d.LineItemId.ToString(),
                        LineItemName = d.LineItemName.ToString(),
                        LineGroupId = d.LineGroupId.ToString(),
                        MainDepartmentCode = d.MainDepartmentCode, //tm.fk_department_code, #70757
                        ProgramCode = d.ProgramCode,
                        ApprovedCost = SetYearValue(ConstantKeys._approvedCostYear, d),
                        PreviousBudgeted = (d.Year > 0 && d.Year < budgetYear) ? d.Amount : 0,
                        Year1Amount = SetYearValue(budgetYear, d),
                        Year2Amount = SetYearValue(budgetYear + 1, d),
                        Year3Amount = SetYearValue(budgetYear + 2, d),
                        Year4Amount = SetYearValue(budgetYear + 3, d),
                        Year5Amount = SetYearValue(budgetYear + 4, d),
                        Year6Amount = SetYearValue(budgetYear + 5, d),
                        Year7Amount = SetYearValue(budgetYear + 6, d),
                        Year8Amount = SetYearValue(budgetYear + 7, d),
                        Year9Amount = SetYearValue(budgetYear + 8, d),
                        Year10Amount = SetYearValue(budgetYear + 9, d),
                        ChangeYear1 = SetChangeYearValue(budgetYear, currentChangeId, d, pageType, userAdjustmentCode),
                        ChangeYear2 = SetChangeYearValue(budgetYear + 1, currentChangeId, d, pageType, userAdjustmentCode),
                        ChangeYear3 = SetChangeYearValue(budgetYear + 2, currentChangeId, d, pageType, userAdjustmentCode),
                        ChangeYear4 = SetChangeYearValue(budgetYear + 3, currentChangeId, d, pageType, userAdjustmentCode),
                        ChangeYear5 = SetChangeYearValue(budgetYear + 4, currentChangeId, d, pageType, userAdjustmentCode),
                        ChangeYear6 = SetChangeYearValue(budgetYear + 5, currentChangeId, d, pageType, userAdjustmentCode),
                        ChangeYear7 = SetChangeYearValue(budgetYear + 6, currentChangeId, d, pageType, userAdjustmentCode),
                        ChangeYear8 = SetChangeYearValue(budgetYear + 7, currentChangeId, d, pageType, userAdjustmentCode),
                        ChangeYear9 = SetChangeYearValue(budgetYear + 8, currentChangeId, d, pageType, userAdjustmentCode),
                        ChangeYear10 = SetChangeYearValue(budgetYear + 9, currentChangeId, d, pageType, userAdjustmentCode),
                        FinancingYear1Amount = SetFinancingYearValue(budgetYear, d),
                        FinancingYear2Amount = SetFinancingYearValue(budgetYear + 1, d),
                        FinancingYear3Amount = SetFinancingYearValue(budgetYear + 2, d),
                        FinancingYear4Amount = SetFinancingYearValue(budgetYear + 3, d),
                        FinancingYear5Amount = SetFinancingYearValue(budgetYear + 4, d),
                        FinancingYear6Amount = SetFinancingYearValue(budgetYear + 5, d),
                        FinancingYear7Amount = SetFinancingYearValue(budgetYear + 6, d),
                        FinancingYear8Amount = SetFinancingYearValue(budgetYear + 7, d),
                        FinancingYear9Amount = SetFinancingYearValue(budgetYear + 8, d),
                        FinancingYear10Amount = SetFinancingYearValue(budgetYear + 9, d),
                        FinancingPreviousBudgeted = d.LineGroupId.ToString() == "20" && d.Year < budgetYear ? d.Amount : 0,
                        AttributeId = d.AttributeId,
                        Chapter = d.Chapter,
                        EvaluationStatus = d.EvaluationStatus,
                        CostEstimate = SetYearValue(ConstantKeys._costEstimateYear, d),
                        ParentOrgIdCreatedAt = d.ParentOrgIdCreatedAt,
                        ParentOrgLevelCreatedAt = d.ParentOrgLevelCreatedAt,
                        CreatedAt = d.CreatedAt
                    }).ToList();

            return data.AsEnumerable();
        }

        private List<string> GetOperationalExpeseDataAsync(List<string> tfpData, List<string> tfpDeleteData, List<string> tfpTempData)
        {
            List<string> projectCodeList = new List<string>();
            projectCodeList.AddRange(tfpData);
            projectCodeList.AddRange(tfpDeleteData);
            projectCodeList.AddRange(tfpTempData);
            return projectCodeList.Distinct().ToList();
        }
        private static decimal SetChangeYearValue(int budgetYear, int currentChangeId, InvProjOverviewGridHelper d, InvestmentPageType pageType, string userAdjustmentCode)
        {
            decimal changeVlaue;
            if (pageType == InvestmentPageType.FinPlanInvestment || pageType == InvestmentPageType.BudgetProposalInvestment)
            {
                changeVlaue = (d.ChangeId == currentChangeId) && d.Year == budgetYear ? d.Amount : 0;
            }
            else
            {
                changeVlaue = (d.UserAdjustmentCode == userAdjustmentCode) && d.Year == budgetYear ? d.Amount : 0;
            }
            return changeVlaue;
        }

        private static decimal SetYearValue(int budgetYear, InvProjOverviewGridHelper d)
        {
            return (d.Year == budgetYear) ? d.Amount : 0;
        }
        private static decimal SetFinancingYearValue(int budgetYear, InvProjOverviewGridHelper d)
        {
            return d.LineItemId.ToString() != "1010" && (d.Year == budgetYear) ? d.Amount : 0;
        }
        private async Task<IEnumerable<InvProjOverviewGridHelper>> GetOrgNameCreatedAt(IEnumerable<InvProjOverviewGridHelper> data, ClsOrgVersionSpecificContent orgVersionContent, ClsOrgVersionSpecificContent tenantOrgVersionContent, int tenantId)
        {
            List<string> invSyncData = await _unitOfWork.InvestmentProjectRepository.GetTransferredInvestment(tenantId);

            data = data.Select(a =>
            {
                if (invSyncData.Contains(a.MainProjectCode))
                {
                    a.CreatedAt = tenantOrgVersionContent.lstOrgDataLevel1.Any()
                        ? tenantOrgVersionContent.lstOrgDataLevel1.FirstOrDefault().org_name_1
                        : string.Empty;
                }
                else if (a.ParentOrgLevelCreatedAt != -1 && !string.IsNullOrEmpty(a.ParentOrgIdCreatedAt))
                {
                    switch (a.ParentOrgLevelCreatedAt)
                    {
                        case 1:
                            a.CreatedAt = orgVersionContent.lstOrgDataLevel1.Any()
                                ? orgVersionContent.lstOrgDataLevel1.FirstOrDefault().org_name_1
                                : string.Empty;
                            break;
                        case 2:
                            a.CreatedAt = orgVersionContent.lstOrgDataLevel2.Any(x => x.org_id_2 == a.ParentOrgIdCreatedAt)
                                ? orgVersionContent.lstOrgDataLevel2.FirstOrDefault(x => x.org_id_2 == a.ParentOrgIdCreatedAt).org_name_2
                                : string.Empty;
                            break;
                        case 3:
                            a.CreatedAt = orgVersionContent.lstOrgDataLevel3.Any(x => x.org_id_3 == a.ParentOrgIdCreatedAt)
                                ? orgVersionContent.lstOrgDataLevel3.FirstOrDefault(x => x.org_id_3 == a.ParentOrgIdCreatedAt).org_name_3
                                : string.Empty;
                            break;
                        case 4:
                            a.CreatedAt = orgVersionContent.lstOrgDataLevel4.Any(x => x.org_id_4 == a.ParentOrgIdCreatedAt)
                                ? orgVersionContent.lstOrgDataLevel4.FirstOrDefault(x => x.org_id_4 == a.ParentOrgIdCreatedAt).org_name_4
                                : string.Empty;
                            break;
                        case 5:
                            a.CreatedAt = orgVersionContent.lstOrgDataLevel5.Any(x => x.org_id_5 == a.ParentOrgIdCreatedAt)
                                ? orgVersionContent.lstOrgDataLevel5.FirstOrDefault(x => x.org_id_5 == a.ParentOrgIdCreatedAt).org_name_5
                                : string.Empty;
                            break;
                        case 6:
                            a.CreatedAt = orgVersionContent.lstOrgDataLevel6.Any(x => x.org_id_6 == a.ParentOrgIdCreatedAt)
                                ? orgVersionContent.lstOrgDataLevel6.FirstOrDefault(x => x.org_id_6 == a.ParentOrgIdCreatedAt).org_name_6
                                : string.Empty;
                            break;
                        case 7:
                            a.CreatedAt = orgVersionContent.lstOrgDataLevel7.Any(x => x.org_id_7 == a.ParentOrgIdCreatedAt)
                                ? orgVersionContent.lstOrgDataLevel7.FirstOrDefault(x => x.org_id_7 == a.ParentOrgIdCreatedAt).org_name_7
                                : string.Empty;
                            break;
                        case 8:
                            a.CreatedAt = orgVersionContent.lstOrgDataLevel8.Any(x => x.org_id_8 == a.ParentOrgIdCreatedAt)
                                ? orgVersionContent.lstOrgDataLevel8.FirstOrDefault(x => x.org_id_8 == a.ParentOrgIdCreatedAt).org_name_8
                                : string.Empty;
                            break;
                        default:
                            a.CreatedAt = string.Empty;
                            break;
                    }
                }
                else
                {
                    a.CreatedAt = string.Empty;
                }
                return a;
            }).ToList();

            return data;
        }
        private async Task<IEnumerable<InvProjOverviewGridHelper>> FormatInvestmentOverviewGridAsync(string userId,
                                                                      IEnumerable<InvProjOverviewGridHelper> data,
                                                                      InvProjGridInputHelper inputObject,
                                                                      InvProjectFormatHelperObject formatHelperObj,
                                                                      IEnumerable<vwUserDetail> userData, List<string> operationalExpeseDataMainProjectProject, ClsOrgVersionSpecificContent orgHierarchy, ClsOrgVersionSpecificContent orgVersionContent)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            IEnumerable<InvProjOverviewGridHelper> finalDataObject;
            Dictionary<string, clsLanguageString> langStringValuesInvDet = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "invdetails");
            //groupData
            IEnumerable<InvProjOverviewGridHelper> result = data.GroupBy(x => new
            {
                x.MainProjectCode,
                x.MainProjectName,
                x.InvestmentPhase,
                x.InvestmentPhaseName,
                x.ProjectName,
                x.ProjectCode,
                x.Tag,
                x.Status,
                x.AccountCode,
                x.AdjustmentCode,
                x.AlterCode,
                x.FunctionCode,
                x.DepartmentCode,
                x.ChangeId,
                x.Year,
                x.MainDepartmentCode,
                x.LineItemId,
                x.LineItemName,
                x.LineGroupId,
                x.ProgramCode,
                x.ResponFuncCode,
                x.ResponDeptCode,
                x.AttributeId,
                x.Chapter,
                x.EvaluationStatus,
                x.CreatedAt,
            }).Select(z => new InvProjOverviewGridHelper()
            {
                Status = z.Key.Status,
                Tag = z.Key.Tag,
                TagFilter = z.Key.Tag,
                MainProjectCode = z.Key.MainProjectCode,
                MainProjectName = z.Key.MainProjectName,
                InvestmentPhase = z.Key.InvestmentPhase,
                InvestmentPhaseName = z.Key.InvestmentPhaseName,
                ProjectCode = z.Key.ProjectCode,
                ProjectName = z.Key.ProjectName,
                AccountCode = z.Key.AccountCode,
                FunctionCode = z.Key.FunctionCode,
                DepartmentCode = z.Key.DepartmentCode,
                AlterCode = z.Key.AlterCode,
                AdjustmentCode = z.Key.AdjustmentCode,
                ChangeId = z.Key.ChangeId,
                ResponDeptCode = z.Key.ResponDeptCode,
                ResponFuncCode = z.Key.ResponFuncCode,
                Amount = 0,
                Year = z.Key.Year,
                LineItemId = z.Key.LineItemId,
                LineItemName = z.Key.LineItemName,
                LineGroupId = z.Key.LineGroupId,
                ProgramCode = z.Key.ProgramCode,
                ApprovedCost = z.Sum(y => y.ApprovedCost),
                PreviousBudgeted = z.Sum(y => y.PreviousBudgeted),
                Year1Amount = z.Sum(y => y.Year1Amount),
                Year2Amount = z.Sum(y => y.Year2Amount),
                Year3Amount = z.Sum(y => y.Year3Amount),
                Year4Amount = z.Sum(y => y.Year4Amount),
                Year5Amount = z.Sum(y => y.Year5Amount),
                Year6Amount = z.Sum(y => y.Year6Amount),
                Year7Amount = z.Sum(y => y.Year7Amount),
                Year8Amount = z.Sum(y => y.Year8Amount),
                Year9Amount = z.Sum(y => y.Year9Amount),
                Year10Amount = z.Sum(y => y.Year10Amount),
                ChangeYear1 = z.Sum(y => y.ChangeYear1),
                ChangeYear2 = z.Sum(y => y.ChangeYear2),
                ChangeYear3 = z.Sum(y => y.ChangeYear3),
                ChangeYear4 = z.Sum(y => y.ChangeYear4),
                ChangeYear5 = z.Sum(y => y.ChangeYear5),
                ChangeYear6 = z.Sum(y => y.ChangeYear6),
                ChangeYear7 = z.Sum(y => y.ChangeYear7),
                ChangeYear8 = z.Sum(y => y.ChangeYear8),
                ChangeYear9 = z.Sum(y => y.ChangeYear9),
                ChangeYear10 = z.Sum(y => y.ChangeYear10),
                MainDepartmentCode = z.Key.MainDepartmentCode,
                First4Years = (z.Sum(y => y.Year1Amount) + z.Sum(y => y.Year2Amount) + z.Sum(y => y.Year3Amount) + z.Sum(y => y.Year4Amount)),
                Next6Years = (z.Sum(y => y.Year5Amount) + z.Sum(y => y.Year6Amount) + z.Sum(y => y.Year7Amount) + z.Sum(y => y.Year8Amount) + z.Sum(y => y.Year9Amount) + z.Sum(y => y.Year10Amount)),
                FinancingSum = (z.Sum(y => y.FinancingYear1Amount) + z.Sum(y => y.FinancingYear2Amount) + z.Sum(y => y.FinancingYear3Amount) + z.Sum(y => y.FinancingYear4Amount)) +
                (z.Sum(y => y.FinancingYear5Amount) + z.Sum(y => y.FinancingYear6Amount) + z.Sum(y => y.FinancingYear7Amount) + z.Sum(y => y.FinancingYear8Amount) + z.Sum(y => y.FinancingYear9Amount) + z.Sum(y => y.FinancingYear10Amount)) + z.Sum(y => y.FinancingPreviousBudgeted),
                AttributeId = z.Key.AttributeId,
                Chapter = z.Key.Chapter,
                EvaluationStatus = z.Key.EvaluationStatus,
                CostEstimate = z.Sum(y => y.CostEstimate),
                CreatedAtFilter = z.Key.CreatedAt,
                CreatedAt = z.Key.CreatedAt != "" ? "<span class=\"org-created-tag\">" + z.Key.CreatedAt + "</span>" : "",
            });

            List<string> selectedColumns = inputObject.ColToDisplay.Where(x => x.isChecked).Select(z => z.key).ToList();

            List<FunctionConnectedTServiceIdDetails> functionDetail;
            List<DepartmentConnectedToOrgDetails> deptDetail;
            string paramValue;
            IEnumerable<InvProjOverviewGridHelper> filterResult = result;

            if (inputObject.SelectedInvType.HasValue && inputObject.SelectedInvType.Value != 0)
            {
                filterResult = filterResult.Where(x => x.ProgramCode == inputObject.SelectedInvType.ToString());
            }
            if (inputObject.AccountType.Any())
            {
                filterResult = FilterBaseDatabyAccountType(inputObject, filterResult);
            }

            if (inputObject.StatusType.Any())
            {
                filterResult = FilterBaseDataByFilterType(inputObject, filterResult);
            }

            functionDetail = null;
            deptDetail = null;
            paramValue = await _utility.GetParameterValueAsync(userId, "FINPLAN_INVESTMENT_LEVEL");
            if (!string.IsNullOrEmpty(paramValue))
            {
                var paramValue_split = paramValue.Split('_').ToList();
                switch (paramValue_split[0].ToUpper())
                {
                    case "SERVICE":
                        functionDetail =  await _invProj.GetDetailsForFunctionAsync(userId, filterResult.Select(x => x.ResponFuncCode).Distinct().ToList());
                        break;

                    default:
                        deptDetail = await _invProj.GetDetailsForDepartmentAsync(userId, inputObject.BudgetYear, filterResult.Select(x => x.ResponDeptCode).Distinct().ToList());
                        break;
                }
            }

            //filter base data on the basis of dept Specific Only Toggle
            if (inputObject.DeptSpecificOnly == true)
            {
                List<clsOrgStructure> lstOrgStructure = await _utility.GetTenantOrgStructureAsync(orgVersionContent, userId);
                clsOrgStructureLevelDetails tenantOrgLevelDetails = _utility.GetTenantOrgLevelDetails(lstOrgStructure);
                var lstDepartments = await _utility.GetDepartmentsAsync(orgVersionContent, userId, inputObject.OrgId,
                    inputObject.OrgLevel, inputObject.ServiceId, tenantOrgLevelDetails.level1,
                    tenantOrgLevelDetails.level2);

                filterResult = FilterBasedOnDepartment(filterResult, lstDepartments, inputObject.OrgId);
            }
            else
            {
                // filter the data
                filterResult = FilterBaseDataByDeptOrServiceId(inputObject, formatHelperObj, filterResult);
            }

            result = filterResult;

            var subTenantData = await _finUtility.GetSyncSubTenantData(userDetails.tenant_id);
            List<tfp_sync_main_projects> allTransfferedProjToMain = _unitOfWork.InvestmentProjectRepository.GetAllTransferredToMain(userDetails.tenant_id, !subTenantData.Any()).Result;
            var transfferedInvestments = _unitOfWork.InvestmentProjectRepository.GetAllTransferredFromMain(userDetails.tenant_id).Result;
            List<InvProjOverviewGridHelper> finalData = formatInvFinalSetData(formatHelperObj, langStringValuesInvDet, result, selectedColumns, functionDetail, deptDetail, paramValue, orgHierarchy, transfferedInvestments, allTransfferedProjToMain, !subTenantData.Any(), inputObject.BudgetYear);

            // group the data b

            finalDataObject = await GroupFinalFormatedDataAsync(langStringValuesInvDet, selectedColumns, finalData, userId, inputObject, userData, operationalExpeseDataMainProjectProject);

            return finalDataObject;
        }

        private static IEnumerable<InvProjOverviewGridHelper> FilterBaseDatabyAccountType(InvProjGridInputHelper inputObject, IEnumerable<InvProjOverviewGridHelper> result)
        {
            var filteredResult = new List<InvProjOverviewGridHelper>();
            if ((inputObject.AccountType).Contains(InvBudPropAccountType.all.ToString()))
            {
                return result;
            }
            if ((inputObject.AccountType).Contains(InvBudPropAccountType.expenditure.ToString()))
            {
                filteredResult = result.Where(x => x.LineGroupId == "10").ToList();
            }
            if ((inputObject.AccountType).Contains(InvBudPropAccountType.earnings.ToString()))
            {
                var earnings = result.Where(x => x.LineGroupId == "20").ToList();
                filteredResult.AddRange(earnings);
            }
            if ((inputObject.AccountType).Contains(InvBudPropAccountType.funds.ToString()))
            {
                var funds = result.Where(x => x.LineGroupId == "40");
                filteredResult.AddRange(funds);
            }
            return filteredResult.AsEnumerable();
        }

        private static IEnumerable<InvProjOverviewGridHelper> FilterBaseDataByFilterType(InvProjGridInputHelper inputObject, IEnumerable<InvProjOverviewGridHelper> result)
        {
            List<int> statusToConsider;
            var filteredResult = new List<InvProjOverviewGridHelper>();

            if ((inputObject.StatusType).Contains("All"))
            {
                return result.OrderBy(x => x.Status);

            }
            if ((inputObject.StatusType).Contains(InvestmentOverViewFilter.Inplan.ToString()))
            {
                statusToConsider = new List<int>() { 0, 1, 2, 7, 8 };
                filteredResult = result.Where(x => statusToConsider.Contains(x.Status)).ToList();
            }
            if ((inputObject.StatusType).Contains(InvestmentOverViewFilter.Required.ToString()))
            {
                var required = result.Where(x => x.Status == 3).ToList();
                filteredResult.AddRange(required);
            }
            if ((inputObject.StatusType).Contains(InvestmentOverViewFilter.Parked.ToString()))
            {
                var parked = result.Where(x => x.Status == 4).ToList();
                filteredResult.AddRange(parked);
            }
            if ((inputObject.StatusType).Contains(InvestmentOverViewFilter.Deleted.ToString()))
            {
                statusToConsider = new List<int>() { 5 };
                var deleted  = result.Where(x => statusToConsider.Contains(x.Status)).ToList();
                filteredResult.AddRange(deleted);
            }
            return filteredResult.AsEnumerable();
        }
        private IEnumerable<InvProjOverviewGridHelper> FilterBasedOnDepartment(IEnumerable<InvProjOverviewGridHelper> data, List<string> deptDetail, string orgId)
        {
            //var orgIddepartments = deptDetail.Where(x => x.orgId == orgId).Select(x => x.departmentCode);

            data = data.Where(x => deptDetail.Contains(x.ResponDeptCode));

            return data;
        }
        private IEnumerable<InvProjOverviewGridHelper> FilterBaseDataByDeptOrServiceId(InvProjGridInputHelper inputObject,
           InvProjectFormatHelperObject formatHelperObj, IEnumerable<InvProjOverviewGridHelper> result)
        {
            if (!string.IsNullOrEmpty(inputObject.ServiceId))
            {
                if (formatHelperObj.lstAllDepartments.Any(x => !string.IsNullOrEmpty(x)))
                {
                    result = result.Where(y => formatHelperObj.lstAllDepartments.Contains(y.DepartmentCode));
                }
            }
            else
            {
                result = result.Where(y => formatHelperObj.lstAllDepartments.Contains(y.DepartmentCode));
            }
            if (inputObject.ColToDisplay.FirstOrDefault(x => x.key.Contains("servIdName") && x.isChecked) != null && formatHelperObj.tcoServiceValues.Any())
            {
                result = GetDataforServiceIdSetup(formatHelperObj.tcoServiceValues, result);
            }

            return result;
        }
        private IEnumerable<InvProjOverviewGridHelper> GetDataforServiceIdSetup(IEnumerable<tco_service_values> tcoServiceValues, IEnumerable<InvProjOverviewGridHelper> result)
        {
            result = (from r in result
                      join ts in tcoServiceValues on r.FunctionCode equals ts.fk_function_code into lgp
                      from lgp1 in lgp.DefaultIfEmpty()
                      select new InvProjOverviewGridHelper()
                      {
                          Status = r.Status,
                          Tag = r.Tag,
                          MainProjectCode = r.MainProjectCode,
                          MainProjectName = r.MainProjectName,
                          InvestmentPhase = r.InvestmentPhase,
                          ProjectCode = r.ProjectCode,
                          ProjectName = r.ProjectName,
                          AccountCode = r.AccountCode,
                          FunctionCode = r.FunctionCode,
                          DepartmentCode = r.DepartmentCode,
                          AlterCode = r.AlterCode,
                          AdjustmentCode = r.AdjustmentCode,
                          ChangeId = r.ChangeId,
                          Amount = 0,
                          Year = r.Year,
                          LineItemId = r.LineItemId,
                          LineItemName = r.LineItemName,
                          PreviousBudgeted = r.PreviousBudgeted,
                          ApprovedCost = r.ApprovedCost,
                          Year1Amount = r.Year1Amount,
                          Year2Amount = r.Year2Amount,
                          Year3Amount = r.Year3Amount,
                          Year4Amount = r.Year4Amount,
                          Year5Amount = r.Year5Amount,
                          Year6Amount = r.Year6Amount,
                          Year7Amount = r.Year7Amount,
                          Year8Amount = r.Year8Amount,
                          Year9Amount = r.Year9Amount,
                          Year10Amount = r.Year10Amount,
                          ChangeYear1 = r.ChangeYear1,
                          ChangeYear2 = r.ChangeYear2,
                          ChangeYear3 = r.ChangeYear3,
                          ChangeYear4 = r.ChangeYear4,
                          ChangeYear5 = r.ChangeYear5,
                          ChangeYear6 = r.ChangeYear6,
                          ChangeYear7 = r.ChangeYear7,
                          ChangeYear8 = r.ChangeYear8,
                          ChangeYear9 = r.ChangeYear9,
                          ChangeYear10 = r.ChangeYear10,
                          First4Years = r.First4Years,
                          Next6Years = r.Next6Years,
                          Total = r.First4Years + r.Next6Years,
                          ServiceIdName = lgp1 != null ? lgp1.service_name_2 : String.Empty,
                          MainDepartmentCode = r.MainDepartmentCode,
                          ProgramCode = r.ProgramCode,
                          ResponDeptCode = r.ResponDeptCode,
                          ResponFuncCode = r.ResponFuncCode,
                          Chapter = r.Chapter,
                          AttributeId = r.AttributeId,
                          EvaluationStatus = r.EvaluationStatus,
                      });
            // group the data b
            result = result.GroupBy(x => new
            {
                x.MainProjectCode,
                x.MainProjectName,
                x.MainDepartmentCode,
                x.InvestmentPhase,
                x.ProjectName,
                x.ProjectCode,
                x.Tag,
                x.Status,
                x.AccountCode,
                x.AdjustmentCode,
                x.AlterCode,
                x.FunctionCode,
                x.DepartmentCode,
                x.ChangeId,
                x.Year,
                x.ServiceIdName,
                x.ProgramCode,
                x.LineItemId,
                x.LineItemName,
                x.ResponDeptCode,
                x.ResponFuncCode,
                x.AttributeId,
                x.Chapter,
                x.EvaluationStatus
            }).Select(z => new InvProjOverviewGridHelper()
            {
                Status = z.Key.Status,
                Tag = z.Key.Tag,
                MainProjectCode = z.Key.MainProjectCode,
                MainProjectName = z.Key.MainProjectName,
                InvestmentPhase = z.Key.InvestmentPhase,
                MainDepartmentCode = z.Key.MainDepartmentCode,
                ProjectCode = z.Key.ProjectCode,
                ProjectName = z.Key.ProjectName,
                AccountCode = z.Key.AccountCode,
                FunctionCode = z.Key.FunctionCode,
                DepartmentCode = z.Key.DepartmentCode,
                AlterCode = z.Key.AlterCode,
                AdjustmentCode = z.Key.AdjustmentCode,
                ChangeId = z.Key.ChangeId,
                Amount = 0,
                Year = z.Key.Year,
                LineItemId = z.Key.LineItemId,
                LineItemName = z.Key.LineItemName,
                PreviousBudgeted = z.Sum(y => y.PreviousBudgeted),
                ApprovedCost = z.Sum(y => y.ApprovedCost),
                Year1Amount = z.Sum(y => y.Year1Amount),
                Year2Amount = z.Sum(y => y.Year2Amount),
                Year3Amount = z.Sum(y => y.Year3Amount),
                Year4Amount = z.Sum(y => y.Year4Amount),
                Year5Amount = z.Sum(y => y.Year5Amount),
                Year6Amount = z.Sum(y => y.Year6Amount),
                Year7Amount = z.Sum(y => y.Year7Amount),
                Year8Amount = z.Sum(y => y.Year8Amount),
                Year9Amount = z.Sum(y => y.Year9Amount),
                Year10Amount = z.Sum(y => y.Year10Amount),
                ChangeYear1 = z.Sum(y => y.ChangeYear1),
                ChangeYear2 = z.Sum(y => y.ChangeYear2),
                ChangeYear3 = z.Sum(y => y.ChangeYear3),
                ChangeYear4 = z.Sum(y => y.ChangeYear4),
                ChangeYear5 = z.Sum(y => y.ChangeYear5),
                ChangeYear6 = z.Sum(y => y.ChangeYear6),
                ChangeYear7 = z.Sum(y => y.ChangeYear7),
                ChangeYear8 = z.Sum(y => y.ChangeYear8),
                ChangeYear9 = z.Sum(y => y.ChangeYear9),
                ChangeYear10 = z.Sum(y => y.ChangeYear10),
                First4Years = z.Sum(y => y.Year1Amount) + z.Sum(y => y.Year2Amount) + z.Sum(y => y.Year3Amount) + z.Sum(y => y.Year4Amount),
                Next6Years = z.Sum(y => y.Year5Amount) + z.Sum(y => y.Year6Amount) + z.Sum(y => y.Year7Amount) +
             z.Sum(y => y.Year8Amount) + z.Sum(y => y.Year9Amount) + z.Sum(y => y.Year10Amount),
                ServiceIdName = z.Key.ServiceIdName,
                ProgramCode = z.Key.ProgramCode,
                ResponDeptCode = z.Key.ResponDeptCode,
                ResponFuncCode = z.Key.ResponFuncCode,
                AttributeId = z.Key.AttributeId,
                Chapter = z.Key.Chapter,
                EvaluationStatus = z.Key.EvaluationStatus,
            });
            return result;
        }

        private List<InvProjOverviewGridHelper> formatInvFinalSetData(InvProjectFormatHelperObject formatHelperObj, Dictionary<string, clsLanguageString> langStringValuesInvDet, IEnumerable<InvProjOverviewGridHelper> result, List<string> selectedColumns, List<FunctionConnectedTServiceIdDetails> functionDetail, List<DepartmentConnectedToOrgDetails> deptDetail, string paramValue, ClsOrgVersionSpecificContent orgHierarchy, List<string> transfferedInvestments, List<tfp_sync_main_projects> allTransfferedProjToMain, bool isSubTenant, int budgetYear)
        {
            List<InvProjOverviewGridHelper> finalData = new List<InvProjOverviewGridHelper>();

            foreach (var d in result.ToList())
            {
                if (selectedColumns.Contains("Tag") || transfferedInvestments.Contains(d.MainProjectCode))
                {
                    FormatTagsForData(formatHelperObj, d, orgHierarchy, transfferedInvestments, allTransfferedProjToMain, isSubTenant, langStringValuesInvDet, budgetYear);
                }
                if (selectedColumns.Contains("MainDepartmentName"))
                {
                    FormatMainDeptName(formatHelperObj, d);
                }
                FormatStatusForData(langStringValuesInvDet, selectedColumns, d);

                FormatRespOrgNameServiceNameForData(functionDetail, deptDetail, paramValue, d);
                CalculateSeletedYearcolumTotal(selectedColumns, d);

                finalData.Add(d);
            }

            return finalData;
        }
        private static void FormatRespOrgNameServiceNameForData(List<FunctionConnectedTServiceIdDetails> functionDetail,
            List<DepartmentConnectedToOrgDetails> deptDetail, string paramValue, InvProjOverviewGridHelper d)
        {
            if (!string.IsNullOrEmpty(paramValue))
            {
                var paramValue_split = paramValue.Split('_').ToList();
                switch (paramValue_split[0].ToUpper())
                {
                    case "SERVICE":
                        d.ResOrgNameServiceName =
                            functionDetail.FirstOrDefault(x => x.functionCode == d.ResponFuncCode) != null
                                ? functionDetail.FirstOrDefault(x => x.functionCode == d.ResponFuncCode).serviceName
                                : string.Empty;
                        break;

                    default:
                        d.ResOrgNameServiceName =
                            deptDetail.FirstOrDefault(x => x.departmentCode == d.ResponDeptCode) != null
                                ? deptDetail.FirstOrDefault(x => x.departmentCode == d.ResponDeptCode).orgName
                                : string.Empty;
                        break;
                }
            }
        }
       
        private static void FormatStatusForData(Dictionary<string, clsLanguageString> langStringValuesInvDet, List<string> selectedColumns, InvProjOverviewGridHelper d)
        {
            d.PreviousBudgetedString = string.Empty;
            d.ShowStringPreviousBud = false;
            switch (d.Status)
            {
                case 0:
                    d.InvType = ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_Newinv")).Value).LangText;
                    d.StatusTitle = $"<span class='status-tags'><img src = './assets/images/new_inv.svg' alt='new investment action' /></span> <span>" + ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_Newinv")).Value).LangText + "</span>";
                    d.StatusFilter = ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_Newinv")).Value).LangText;
                    break;

                case 1:
                    d.InvType = ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_PrevInv")).Value).LangText;
                    d.StatusTitle = $"<span class='status-tags'><img src = './assets/images/prev_inv.svg' alt='PrevApprovedInv action' /></span> <span>" + ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_PrevInv")).Value).LangText + "</span>";
                    d.StatusFilter = ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_PrevInv")).Value).LangText;
                    break;

                case 2:
                    d.InvType = ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_Runninginv")).Value).LangText;
                    d.StatusTitle = $"<span class='status-tags'><img src = './assets/images/running_inv.svg' alt='running action' /></span> <span>" + ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_Runninginv")).Value).LangText + "</span>";
                    d.StatusFilter = ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_Runninginv")).Value).LangText;
                    if (selectedColumns.Contains("StatusTitle"))
                    {
                        d.PreviousBudgetedString = ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_Runninginv")).Value).LangText;
                        d.TotalString = ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_Runninginv")).Value).LangText;
                        d.ShowStringPreviousBud = true;
                    }
                    break;

                case 3:
                    d.InvType = ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_EvaInv")).Value).LangText;
                    d.StatusTitle = $"<span class='status-tags'><img src = './assets/images/blist_action.svg' alt='blist action' /></span> <span>" + ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_EvaInv")).Value).LangText + "</span>";
                    d.StatusFilter = ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_EvaInv")).Value).LangText;
                    break;

                case 4:
                    d.InvType = ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_ParkedInv")).Value).LangText;
                    d.StatusTitle = $"<span class='status-tags'><img src = './assets/images/parked_action.svg' alt='parked action' /></span> <span>" + ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_ParkedInv")).Value).LangText + "</span>";
                    d.StatusFilter = ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_ParkedInv")).Value).LangText;
                    break;

                case 5:
                    d.InvType = ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_DeletedInv")).Value).LangText;
                    d.StatusTitle = $"<span class='status-tags'><img src = './assets/images/deleted_action.svg' alt='deleted action' /></span> <span>" + ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_DeletedInv")).Value).LangText + "</span>";
                    d.StatusFilter = ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_DeletedInv")).Value).LangText;
                    break;

                case 6:
                    d.InvType = ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_RequiredInv")).Value).LangText;
                    d.StatusTitle = $"<span class='status-tags'><img src = './assets/images/blist_action.svg' alt=' action' /></span> <span>" + ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_RequiredInv")).Value).LangText + "</span>";
                    d.StatusFilter = ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_RequiredInv")).Value).LangText;
                    break;

                case 7:
                    d.InvType = ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_ConsequenceadjustedInv")).Value).LangText;
                    d.StatusTitle = $"<span class='status-tags'><img src = './assets/images/consequence_inv.svg' alt='deleted action' /></span> <span>" + ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_ConsequenceadjustedInv")).Value).LangText + "</span>";
                    d.StatusFilter = ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_ConsequenceadjustedInv")).Value).LangText;
                    break;

                case 8:
                    d.InvType = ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_FinishedInv")).Value).LangText;
                    d.StatusTitle = $"<span class='status-tags'><img src = './assets/images/finished_inv.svg' alt='finished action' /></span> <span>" + ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_FinishedInv")).Value).LangText + "</span>";
                    d.StatusFilter = ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_FinishedInv")).Value).LangText;
                    break;
            }
        }

        private static void FormatMainDeptName(InvProjectFormatHelperObject formatHelperObj, InvProjOverviewGridHelper d)
        {
            d.MainDepartmentName = string.Empty;
            if (d.MainDepartmentCode!= null && (formatHelperObj.orgVersionContent.lstOrgHierarchy.FirstOrDefault(x => x.fk_department_code == d.MainDepartmentCode) != null))
            {
                switch (formatHelperObj.fp1FlagValue)
                {
                    case "org_id_1":
                        d.MainDepartmentName = formatHelperObj.orgVersionContent.lstOrgHierarchy
                            .FirstOrDefault(x => x.fk_department_code == d.MainDepartmentCode).org_name_1;
                        break;

                    case "org_id_2":
                        d.MainDepartmentName = formatHelperObj.orgVersionContent.lstOrgHierarchy
                            .FirstOrDefault(x => x.fk_department_code == d.MainDepartmentCode).org_name_2;
                        break;

                    case "org_id_3":
                        d.MainDepartmentName = formatHelperObj.orgVersionContent.lstOrgHierarchy
                            .FirstOrDefault(x => x.fk_department_code == d.MainDepartmentCode).org_name_3;
                        break;

                    case "org_id_4":
                        d.MainDepartmentName = formatHelperObj.orgVersionContent.lstOrgHierarchy
                            .FirstOrDefault(x => x.fk_department_code == d.MainDepartmentCode).org_name_4;
                        break;

                    case "org_id_5":
                        d.MainDepartmentName = formatHelperObj.orgVersionContent.lstOrgHierarchy
                            .FirstOrDefault(x => x.fk_department_code == d.MainDepartmentCode).org_name_5;
                        break;
                }
            }
        }

        private void FormatTagsForData(InvProjectFormatHelperObject formatHelperObj, InvProjOverviewGridHelper d, ClsOrgVersionSpecificContent orgHierarchy, List<string> transfferedInvestments, List<tfp_sync_main_projects> allTransfferedProjToMain, bool isSubTenant, Dictionary<string, clsLanguageString> langStringValuesInvDet, int budgetYear)
        {
            StringBuilder sb = new StringBuilder();
            if (transfferedInvestments.Contains(d.MainProjectCode))

            {
                sb.Append("<span class='inv-required'>" + orgHierarchy.lstOrgDataLevel1[0].org_name_1 + "</span>&nbsp;");
                d.TagFilter = orgHierarchy.lstOrgDataLevel1[0].org_name_1;
            }
            var projData = allTransfferedProjToMain.Where(x => x.main_fk_main_project_code == d.MainProjectCode).FirstOrDefault();
            if (isSubTenant && projData != null)
            {
                // lang str for transferred
                string invTransferred = ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_transfered")).Value).LangText;
                sb.Append("<span class='inv-required'>" + invTransferred + "</span>&nbsp;");
                d.TagFilter = invTransferred;
            }
            else if (projData != null && !isSubTenant)
            {
                ClsOrgVersionSpecificContent orgData = _utility.GetOrgVersionBasedOnTenant(projData.sub_fk_tenant_id, _utility.GetForecastPeriod(budgetYear, 1));
                sb.Append("<span class='inv-required'>" + orgData.lstOrgDataLevel1[0].org_name_1 + "</span>&nbsp;");
                d.TagFilter = orgData.lstOrgDataLevel1[0].org_name_1;
            }
            if (!string.IsNullOrEmpty(d.Tag))
            {
                var tagIds = d.Tag.Split(',').ToList();
                foreach (var t in tagIds)
                {
                    if (formatHelperObj.tcoActionTags.FirstOrDefault(x => x.PkId.ToString() == t) != null)
                    {
                        sb.Append("<span class='selected-tags'>" + formatHelperObj.tcoActionTags.FirstOrDefault(x => x.PkId.ToString() == t).TagDescription + "</span>&nbsp;");
                        d.TagFilter = formatHelperObj.tcoActionTags.FirstOrDefault(x => x.PkId.ToString() == t).TagDescription;
                    }
                }
                d.Tag = sb.ToString();
            }
            else
            {
                d.Tag = sb.ToString();
            }
        }

        private static void CalculateSeletedYearcolumTotal(List<string> selectedColumns, InvProjOverviewGridHelper d)
        {
            decimal selectedYearColTotal = 0;
            // selected year total
            if (selectedColumns.Contains("Year1Amount"))
            {
                selectedYearColTotal = d.Year1Amount;
            }
            if (selectedColumns.Contains("Year2Amount"))
            {
                selectedYearColTotal = selectedYearColTotal + d.Year2Amount;
            }
            if (selectedColumns.Contains("Year3Amount"))
            {
                selectedYearColTotal = selectedYearColTotal + d.Year3Amount;
            }
            if (selectedColumns.Contains("Year4Amount"))
            {
                selectedYearColTotal = selectedYearColTotal + d.Year4Amount;
            }
            if (selectedColumns.Contains("Year5Amount"))
            {
                selectedYearColTotal = selectedYearColTotal + d.Year5Amount;
            }
            if (selectedColumns.Contains("Year6Amount"))
            {
                selectedYearColTotal = selectedYearColTotal + d.Year6Amount;
            }
            if (selectedColumns.Contains("Year7Amount"))
            {
                selectedYearColTotal = selectedYearColTotal + d.Year7Amount;
            }
            if (selectedColumns.Contains("Year8Amount"))
            {
                selectedYearColTotal = selectedYearColTotal + d.Year8Amount;
            }
            if (selectedColumns.Contains("Year9Amount"))
            {
                selectedYearColTotal = selectedYearColTotal + d.Year9Amount;
            }
            if (selectedColumns.Contains("Year10Amount"))
            {
                selectedYearColTotal = selectedYearColTotal + d.Year10Amount;
            }
            d.SelectedYearColTotal = selectedYearColTotal;
        }
        private async Task<IEnumerable<InvProjOverviewGridHelper>> GroupFinalFormatedDataAsync(Dictionary<string, clsLanguageString> langStringValuesInvDet,
                                                                             List<string> selectedColumns,
                                                                             List<InvProjOverviewGridHelper> finalData,
                                                                             string userId,
                                                                             InvProjGridInputHelper inputObject,
                                                                             IEnumerable<vwUserDetail> userData, List<string> operationalExpeseDataMainProjectProject)
        {
            int budgetYear = inputObject.BudgetYear;
            IEnumerable<InvProjOverviewGridHelper> overviewGridData;
            var userDetails = await _utility.GetUserDetailsAsync(userId);
            //get attachments
            var attachments = (await _unitOfWork.InvestmentProjectRepository
                .GetAllAttachmentDataAsync(userDetails.tenant_id, budgetYear, Modules.FINPLAN.ToString(),
                    PageId.INVESTMENT.ToString())).ToList();

            overviewGridData = finalData.GroupBy(x => new
            {
                x.MainProjectCode,
                x.MainProjectName,
                // x.resOrgNameServiceName,
                ResOrgNameServiceName = selectedColumns.Contains("ResOrgNameServiceName") ? x.ResOrgNameServiceName : null,
                // mainDepartmentCode = selectedColumns.Contains("mainDepartmentName") ? x.mainDepartmentCode : null,
                MainDepartmentName = selectedColumns.Contains("MainDepartmentName") ? x.MainDepartmentName : null,
                Tag = selectedColumns.Contains("Tag") ? x.Tag : null,
                StatusTitle = selectedColumns.Contains("StatusTitle") ? x.StatusTitle : null,
                x.Status,
                InvType = selectedColumns.Contains("InvType") ? x.InvType : null,
                //x.previousBudgeted_string,
                //x.previousBudgeted_string,
                // x.accountCode,
                ServiceIdName = selectedColumns.Contains("ServiceIdName") ? x.ServiceIdName : null,
                x.AttributeId,
                x.Chapter,
                x.InvestmentPhase,
                x.InvestmentPhaseName,
                x.ResponDeptCode,
                x.EvaluationStatus,
                x.CreatedAt,
                x.StatusFilter,
                x.TagFilter,
                x.CreatedAtFilter
                // x.programCode
            }).Select(z => new InvProjOverviewGridHelper()
            {
                StatusTitle = z.Key.StatusTitle,
                Status = z.Key.Status,
                InvType = z.Key.InvType,
                Tag = z.Key.Tag,
                MainProjectCode = z.Key.MainProjectCode,
                MainProjectName = z.Key.MainProjectName,
                // MainDepartmentCode = z.Key.MainDepartmentCode,
                MainDepartmentName = z.Key.MainDepartmentName,
                ResOrgNameServiceName = z.Key.ResOrgNameServiceName,
                InvestmentPhase = z.Key.InvestmentPhase,
                InvestmentPhaseName = z.Key.InvestmentPhaseName,
                // AccountCode = z.Key.AccountCode,
                // ProgramCode = z.Key.ProgramCode,
                ApprovedCost = z.Sum(y => y.ApprovedCost),
                PreviousBudgeted = z.Key.Status == 3 ? 0 : z.Sum(y => y.PreviousBudgeted),
                Year1Amount = z.Sum(y => y.Year1Amount),
                Year2Amount = z.Sum(y => y.Year2Amount),
                Year3Amount = z.Sum(y => y.Year3Amount),
                Year4Amount = z.Sum(y => y.Year4Amount),
                Year5Amount = z.Sum(y => y.Year5Amount),
                Year6Amount = z.Sum(y => y.Year6Amount),
                Year7Amount = z.Sum(y => y.Year7Amount),
                Year8Amount = z.Sum(y => y.Year8Amount),
                Year9Amount = z.Sum(y => y.Year9Amount),
                Year10Amount = z.Sum(y => y.Year10Amount),
                ChangeYear1 = z.Sum(y => y.ChangeYear1),
                ChangeYear2 = z.Sum(y => y.ChangeYear2),
                ChangeYear3 = z.Sum(y => y.ChangeYear3),
                ChangeYear4 = z.Sum(y => y.ChangeYear4),
                ChangeYear5 = z.Sum(y => y.ChangeYear5),
                ChangeYear6 = z.Sum(y => y.ChangeYear6),
                ChangeYear7 = z.Sum(y => y.ChangeYear7),
                ChangeYear8 = z.Sum(y => y.ChangeYear8),
                ChangeYear9 = z.Sum(y => y.ChangeYear9),
                ChangeYear10 = z.Sum(y => y.ChangeYear10),
                FinancingSum = z.Sum(x => x.FinancingSum),
                Total = Calculate1To4YearSum(z, inputObject.SelectedYearOption) +
        Calculate5To10YearSum(z, inputObject.SelectedYearOption) +
        (z.Key.Status == 3 ? 0 : z.Sum(y => y.PreviousBudgeted)),
                ServiceIdName = z.Key.ServiceIdName,
                First4Years = (z.Sum(y => y.Year1Amount) + z.Sum(y => y.Year2Amount) + z.Sum(y => y.Year3Amount) + z.Sum(y => y.Year4Amount)),
                Next6Years = (z.Sum(y => y.Year5Amount) + z.Sum(y => y.Year6Amount) + z.Sum(y => y.Year7Amount) +
              z.Sum(y => y.Year8Amount) + z.Sum(y => y.Year9Amount) + z.Sum(y => y.Year10Amount)),
                PreviousBudgetedString = z.Key.Status == 2 ? ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_Runninginv")).Value).LangText : string.Empty,
                ShowStringPreviousBud = z.Key.Status == 2,
                TotalString = z.Key.Status == 2 ? ((langStringValuesInvDet.FirstOrDefault(v => v.Key == "inv_status_Runninginv")).Value).LangText : string.Empty,
                SelectedYearColTotal = z.Sum(y => y.SelectedYearColTotal),
                AttributeId = z.Key.AttributeId,
                Chapter = z.Key.Chapter,
                ResponDeptCode = z.Key.ResponDeptCode,
                EvaluationStatus = z.Key.EvaluationStatus,
                CostEstimate = z.Sum(y => y.CostEstimate),
                CreatedAt = z.Key.CreatedAt,
                StatusFilter = z.Key.StatusFilter,
                TagFilter = z.Key.TagFilter,
                CreatedAtFilter = z.Key.CreatedAtFilter,
                MainProjectNameFilter = z.Key.MainProjectName,
                MainProjectImage = attachments.Any(x => x.parent_id == z.Key.MainProjectCode) ? $"<img src='../images/attachment.svg' />" : "",
            }).ToList();

            overviewGridData = await GetMappedDataAsync(userId, budgetYear, overviewGridData, userData, operationalExpeseDataMainProjectProject);

            return overviewGridData;
        }
        private decimal Calculate5To10YearSum(IGrouping<object, InvProjOverviewGridHelper> z, yearOptionSelector SelectedYearOption)
        {
            return (SelectedYearOption == yearOptionSelector.tenYear)
                ? (z.Sum(y => y.Year1Amount) + z.Sum(y => y.Year2Amount) + z.Sum(y => y.Year3Amount) +
                   z.Sum(y => y.Year4Amount)) + (z.Sum(y => y.Year5Amount) + z.Sum(y => y.Year6Amount) +
                                                  z.Sum(y => y.Year7Amount) + z.Sum(y => y.Year8Amount) +
                                                  z.Sum(y => y.Year9Amount) + z.Sum(y => y.Year10Amount))
                : 0;
        }

        private decimal Calculate1To4YearSum(IGrouping<object, InvProjOverviewGridHelper> z, yearOptionSelector SelectedYearOption)
        {
            return (SelectedYearOption == yearOptionSelector.fourYear)
                ? (z.Sum(y => y.Year1Amount) + z.Sum(y => y.Year2Amount) + z.Sum(y => y.Year3Amount) +
                   z.Sum(y => y.Year4Amount))
                : 0;
        }

        private async Task<IEnumerable<InvProjOverviewGridHelper>> GetMappedDataAsync(string userid, int budgetYear,
            IEnumerable<InvProjOverviewGridHelper> data, IEnumerable<vwUserDetail> userData,
            List<string> operationalExpeseDataMainProjectProject)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userid);

            var mappedInvestmentResult = _unitOfWork.InvestmentProjectNewRepository.GetTenantInvestmentMappingDataAsync(userDetails.tenant_id);
            var mainProjectsResult =  _unitOfWork.InvestmentProjectNewRepository.GetMainProjectsforInvestmentMappingAsync(userDetails.tenant_id, budgetYear);
            var mainProjectsDescResult =  _unitOfWork.InvestmentProjectNewRepository.GetPlanMappingDescriptionDataAsync(userDetails.tenant_id, budgetYear);
            var invMappingSetResult =  _unitOfWork.InvestmentProjectNewRepository.GetBPPlanInvestmentMappingDataAsync(userDetails.tenant_id);
            var strategyInvestmentMappedDataResult =  _unitOfWork.InvestmentProjectNewRepository.GetStrategyInvestmentMappingDataAsync(userDetails.tenant_id);
            var strategyAssessmentInfoResult =  _unitOfWork.InvestmentProjectNewRepository.GetStrategyAssessmentInfoAsync(userDetails.tenant_id);

            await Task.WhenAll(mappedInvestmentResult, mainProjectsResult, mainProjectsDescResult, invMappingSetResult, strategyInvestmentMappedDataResult, strategyAssessmentInfoResult);

            var mappedInvestmentData = mappedInvestmentResult.Result;
            var mainProjects = mainProjectsResult.Result;
            var mainProjectsDesc = mainProjectsDescResult.Result;
            var invMappingSet = invMappingSetResult.Result;
            var strategyInvestmentMappedData = strategyInvestmentMappedDataResult.Result;
            var strategyAssessmentInfo = strategyAssessmentInfoResult.Result;

            data = (from a in data
                    join b in mappedInvestmentData on a.MainProjectCode equals b.fk_main_project_code into x1
                    from b1 in x1.DefaultIfEmpty()
                    join s in strategyInvestmentMappedData on a.MainProjectCode equals s.fk_main_project_code into x4
                    from b4 in x4.DefaultIfEmpty()
                    join c in mainProjects on a.MainProjectCode equals c.pk_main_project_code into x2
                    from b2 in x2.DefaultIfEmpty()
                    join d in mainProjectsDesc on a.MainProjectCode equals d.fk_main_project_code into x3
                    from b3 in x3.DefaultIfEmpty()
                    select new InvProjOverviewGridHelper
                    {
                        StatusTitle = a.StatusTitle,
                        Status = a.Status,
                        InvType = a.InvType,
                        Tag = a.Tag,
                        MainProjectCode = a.MainProjectCode,
                        MainProjectName = GetFormattedProjName(invMappingSet, strategyAssessmentInfo, a.MainProjectCode, a.MainProjectName, b1, b4),
                        MainDepartmentName = a.MainDepartmentName,
                        ResOrgNameServiceName = a.ResOrgNameServiceName,
                        ApprovedCost = a.ApprovedCost,
                        PreviousBudgeted = a.PreviousBudgeted,
                        Year1Amount = a.Year1Amount,
                        Year2Amount = a.Year2Amount,
                        Year3Amount = a.Year3Amount,
                        Year4Amount = a.Year4Amount,
                        Year5Amount = a.Year5Amount,
                        Year6Amount = a.Year6Amount,
                        Year7Amount = a.Year7Amount,
                        Year8Amount = a.Year8Amount,
                        Year9Amount = a.Year9Amount,
                        Year10Amount = a.Year10Amount,
                        ChangeYear1 = a.ChangeYear1,
                        ChangeYear2 = a.ChangeYear2,
                        ChangeYear3 = a.ChangeYear3,
                        ChangeYear4 = a.ChangeYear4,
                        ChangeYear5 = a.ChangeYear5,
                        ChangeYear6 = a.ChangeYear6,
                        ChangeYear7 = a.ChangeYear7,
                        ChangeYear8 = a.ChangeYear8,
                        ChangeYear9 = a.ChangeYear9,
                        ChangeYear10 = a.ChangeYear10,
                        FinancingSum = a.FinancingSum,
                        Total = a.Total,
                        ServiceIdName = a.ServiceIdName,
                        First4Years = a.First4Years,
                        Next6Years = a.Next6Years,
                        PreviousBudgetedString = a.PreviousBudgetedString,
                        ShowStringPreviousBud = a.ShowStringPreviousBud,
                        TotalString = a.TotalString,
                        SelectedYearColTotal = a.SelectedYearColTotal,
                        FromPlan = b1 == null ? "Ikke fra plan" : GetPlanIncorporationInfo(b2),
                        StatusPlan = b1 == null ? "Ikke relevant" : "Beskrivelse mangler",
                        IsTransferredFromPlan = b1 != null,
                        InvestmentModuleType = GetInvestmentModuleType(b1, b4),
                        PlanIncorporationFilter = SetPlanIncorporationFilter(b1, b2),
                        OperationalExpense = SetOperationalExpense(operationalExpeseDataMainProjectProject, a),
                        Chapter = a.Chapter,
                        AttributeId = a.AttributeId,
                        InvestmentPhase = a.InvestmentPhase,
                        InvestmentPhaseName = a.InvestmentPhaseName,
                        ResponDeptCode = a.ResponDeptCode,
                        EvaluationStatus = a.EvaluationStatus,
                        CostEstimate = a.CostEstimate,
                        CreatedAt = a.CreatedAt,
                        StatusFilter = a.StatusFilter,
                        TagFilter = a.TagFilter,
                        CreatedAtFilter = a.CreatedAtFilter,
                        MainProjectNameFilter = a.MainProjectNameFilter,
                        MainProjectImage = a.MainProjectImage
                    });
            return data;
        }
        private string GetFormattedProjName(IEnumerable<PlanFinplanMainProjectMappingHelper> invMappingSet,
          IEnumerable<StrategyFinplanMainProjectMappingHelper> strategyAssessmentInfo, string pkId,
          string projectName, tpl_tfp_investment_mapping planInvMappedData, tsa_tfp_investment_mapping strategyInvMappedData)
        {
            if (planInvMappedData != null)
            {
                var data = invMappingSet.FirstOrDefault(x => x.FinPlanProjectId == pkId);
                if (data != null)
                {
                    return $"{projectName} </br><span class='col-md-12 padding0'><span class='theme-plan-tags' title='{data.PlanTag}'><img src='../images/plan_tag-01.svg' class='tags-img' /><span class='tags-mainroject-background'>{data.PlanTag}</span></span></span>";
                }
            }
            else if (strategyInvMappedData != null)
            {
                var data = strategyAssessmentInfo.FirstOrDefault(x => x.FinPlanProjectId == pkId);
                if (data != null)
                {
                    return $"{projectName} </br><span class='bp-tag-StgAct'>{data.assessmentName}</span>&nbsp;<span class='bp-blue-StgAct'>{data.assessmentAreaName}</span>";
                }
            }
            return projectName;
        }

        private InvestmentModuleTypes GetInvestmentModuleType(tpl_tfp_investment_mapping b1, tsa_tfp_investment_mapping b4)
        {
            if (b1 != null)
            {
                return InvestmentModuleTypes.Inv_From_Plan;
            }
            else if (b4 != null)
            {
                return InvestmentModuleTypes.Inv_From_Strategy;
            }
            return InvestmentModuleTypes.Inv_From_Finplan;
        }

        private int SetPlanIncorporationFilter(tpl_tfp_investment_mapping b1, tco_main_projects b2)
        {
            if (b1 == null)
            {
                return (int)EnumPlanInvestmentPartial.NotFromPlan;
            }
            if (b2 != null)
            {
                List<int> inactiveStatuses = new List<int> { 3, 4, 5 };
                if (inactiveStatuses.Contains(b2.inv_status.Value))
                    return (int)EnumPlanInvestmentPartial.Deleted;
                else if (!inactiveStatuses.Contains(b2.inv_status.Value) && !b2.partial_flag)
                    return (int)EnumPlanInvestmentPartial.Yes;
                else if (!inactiveStatuses.Contains(b2.inv_status.Value) && b2.partial_flag)
                    return (int)EnumPlanInvestmentPartial.Partly;
                else
                    return (int)EnumPlanInvestmentPartial.NotFromPlan;
            }
            return (int)EnumPlanInvestmentPartial.NotFromPlan;
        }

        private string GetPlanIncorporationInfo(tco_main_projects b2)
        {
            if (b2 != null)
            {
                List<int> inactiveStatuses = new List<int> { 3, 4, 5 };
                if (inactiveStatuses.Contains(b2.inv_status.Value))
                    return "Kun ønsket";
                else if (!inactiveStatuses.Contains(b2.inv_status.Value) && !b2.partial_flag)
                    return "Ja";
                else if (!inactiveStatuses.Contains(b2.inv_status.Value) && b2.partial_flag)
                    return "Delvis";
                else
                    return string.Empty;
            }
            return string.Empty;
        }
        private string SetOperationalExpense(List<string> operationExpenseMainProjectCodeList, InvProjOverviewGridHelper d)
        {
            return (operationExpenseMainProjectCodeList.Contains(d.MainProjectCode)) ? "opex.svg" : string.Empty;
        }
        private InvProjGridFilterHelper GetInvGridFilterData(IEnumerable<InvProjOverviewGridHelper> result)
        {
            var data = result.ToList();
            InvProjGridFilterHelper filterData = new();

            filterData.RespOrgServData = data.Where(x => !string.IsNullOrEmpty(x.ResOrgNameServiceName)).Select(y => new KeyValuePairString() { Key = y.ResOrgNameServiceName , Value = y.ResOrgNameServiceName}).DistinctBy(z => z.Key).ToList();
            filterData.ServiceIdNameData = data.Where(x => !string.IsNullOrEmpty(x.ServiceIdName)).Select(y => new KeyValuePairString() { Key = y.ServiceIdName, Value = y.ServiceIdName }).DistinctBy(z => z.Key).ToList();
            filterData.MainDeptNameData = data.Where(x => !string.IsNullOrEmpty(x.MainDepartmentName)).Select(y => new KeyValuePairString() { Key = y.MainDepartmentName, Value = y.MainDepartmentName }).DistinctBy(z => z.Key).ToList();
            filterData.CreatedAtData = data.Where(x => !string.IsNullOrEmpty(x.CreatedAtFilter)).Select(y => new KeyValuePairString() { Key = y.CreatedAtFilter, Value = y.CreatedAtFilter }).DistinctBy(z => z.Key).ToList();
            filterData.StatusTitleData = data.Where(x => !string.IsNullOrEmpty(x.StatusFilter)).Select(y => new KeyValuePairString() { Key = y.StatusFilter, Value = y.StatusFilter }).DistinctBy(z => z.Key).ToList();
            filterData.OperationalExpenseData = data.Where(x => !string.IsNullOrEmpty(x.OperationalExpense)).Select(y => new KeyValuePairString() { Key = y.OperationalExpense, Value = y.OperationalExpense }).DistinctBy(z => z.Key).ToList();
            filterData.TagsData = data.Where(x => !string.IsNullOrEmpty(x.TagFilter)).Select(y => new KeyValuePairString() { Key = y.TagFilter, Value = y.TagFilter }).DistinctBy(z => z.Key).ToList();
            filterData.MainProjCodeData = data.Where(x => !string.IsNullOrEmpty(x.MainProjectCode)).Select(y => new KeyValuePairString() { Key = y.MainProjectCode, Value = y.MainProjectCode }).DistinctBy(z => z.Key).ToList();
            filterData.MainProjNameData = data.Where(x => !string.IsNullOrEmpty(x.MainProjectNameFilter)).Select(y => new KeyValuePairString() { Key = y.MainProjectNameFilter, Value = y.MainProjectNameFilter }).DistinctBy(z => z.Key).ToList();
            filterData.FromPlanData = data.Where(x => !string.IsNullOrEmpty(x.FromPlan)).Select(y => new KeyValuePairString() { Key = y.FromPlan, Value = y.FromPlan }).DistinctBy(z => z.Key).ToList();

            return filterData;
        }
        private List<KeyValuePair> GetChangeYearColumns(List<KeyValuePair> colToDisplay)
        {
            if(colToDisplay.Where(x => x.key == "Year1Amount" && x.isChecked == true).Any())
            {
                colToDisplay.Add(new KeyValuePair() { key = "ChangeYear1", isChecked = true});
            }
            if (colToDisplay.Where(x => x.key == "Year2Amount" && x.isChecked == true).Any())
            {
                colToDisplay.Add(new KeyValuePair() { key = "ChangeYear2" ,isChecked = true });
            }
            if (colToDisplay.Where(x => x.key == "Year3Amount" && x.isChecked == true).Any())
            {
                colToDisplay.Add(new KeyValuePair() { key = "ChangeYear3" ,isChecked = true });
            }
            if (colToDisplay.Where(x => x.key == "Year4Amount" && x.isChecked == true).Any())
            {
                colToDisplay.Add(new KeyValuePair() { key = "ChangeYear4", isChecked = true });
            }
            if (colToDisplay.Where(x => x.key == "Year5Amount" && x.isChecked == true).Any())
            {
                colToDisplay.Add(new KeyValuePair() { key = "ChangeYear5", isChecked = true });
            }
            if (colToDisplay.Where(x => x.key == "Year6Amount" && x.isChecked == true).Any())
            {
                colToDisplay.Add(new KeyValuePair() { key = "ChangeYear6", isChecked = true });
            }
            if (colToDisplay.Where(x => x.key == "Year7Amount" && x.isChecked == true).Any())
            {
                colToDisplay.Add(new KeyValuePair() { key = "ChangeYear7", isChecked = true });
            }
            if (colToDisplay.Where(x => x.key == "Year8Amount" && x.isChecked == true).Any())
            {
                colToDisplay.Add(new KeyValuePair() { key = "ChangeYear8", isChecked = true });
            }
            if (colToDisplay.Where(x => x.key == "Year9Amount" && x.isChecked == true).Any())
            {
                colToDisplay.Add(new KeyValuePair() { key = "ChangeYear9", isChecked = true });
            }
            if (colToDisplay.Where(x => x.key == "Year10Amount" && x.isChecked == true).Any())
            {
                colToDisplay.Add(new KeyValuePair() { key = "ChangeYear10", isChecked = true });
            }
            return colToDisplay;
        }
        private async Task<List<KeyValuePair>> GetColumConfigFromBlobTableAsync(UserData user, List<KeyValuePair> columsconfig, tco_application_flag columnSelectorTenant)
        {
            if (columnSelectorTenant != null)
            {
                var pmColSel = columnSelectorTenant.flag_guid;

                if (pmColSel != null)
                {
                    //TableOperation retrieveOperation = TableOperation.Retrieve<clsTenantCloudTableEntity>(user.tenant_id.ToString(), pmColSel.ToString());
                    //TableResult retrievedResult = table.Execute(retrieveOperation);
                    clsTenantCloudTableEntity updateEntity = await _utility.GetCloudTableContentAsync("WADTenantData", user.tenant_id.ToString(), pmColSel.ToString());

                    if (updateEntity != null)
                    {
                        JArray pmInvColumnConfig = new JArray();
                        if (updateEntity.data != "null")
                        {
                            pmInvColumnConfig = JArray.FromObject(JsonConvert.DeserializeObject(updateEntity.data.ToString()));
                        }
                        columsconfig = (from s in pmInvColumnConfig
                                        select new KeyValuePair
                                        {
                                            key = (string)s["key"],
                                            value = (string)s["value"],
                                            isChecked = (bool)s["isChecked"],
                                        }).ToList();
                    }
                }
            }

            return columsconfig;
        }
        private async Task<string> GetYearConfigFromBlobTableAsync(UserData user, string yearSelected, tco_application_flag yearSelectorTenant)
        {
            if (yearSelectorTenant != null)
            {
                var pmColSel = yearSelectorTenant.flag_guid;

                if (pmColSel != null)
                {
                    clsTenantCloudTableEntity updateEntity = await _utility.GetCloudTableContentAsync("WADTenantData", user.tenant_id.ToString(), pmColSel.ToString());
                    if (updateEntity != null)
                    {
                        string pmInvColumnConfig = string.Empty;
                        if (updateEntity.data != "null")
                        {
                            pmInvColumnConfig = updateEntity.data;
                        }
                        yearSelected = pmInvColumnConfig;
                    }
                }
            }
            return yearSelected;
        }

        private async Task<List<ColumnSelectorColumn>> GetTranslations(UserData userDetails, int budgetYear, List<ColumnSelectorColumn> finalList)
        {
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Investment");
            Dictionary<string, clsLanguageString> languageStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "invdetails");
            Dictionary<string, clsLanguageString> langStringsFinPlan = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "FinancialPlan");
            //Replace language string specific properties
            foreach (var col in finalList)
            {
                switch ((string)col.key)
                {
                    case "ResOrgNameServiceName":
                        col.value = langStrings.FirstOrDefault(x =>
                            x.Key.Equals("investment_mainGrid_resOrgNameServiceName", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "ServiceIdName":
                        col.value = langStrings.FirstOrDefault(x => x.Key.Equals("investment_mainGrid_serviceID", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "CreatedAt":
                        col.value = langStrings.FirstOrDefault(x => x.Key.Equals("investment_mainGrid_createdAt", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "MainDepartmentName":
                        col.value = langStrings.FirstOrDefault(x => x.Key.Equals("investment_mainGrid_serviceArea", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "StatusTitle":
                        col.value = langStrings.FirstOrDefault(x =>
                                x.Key.Equals("investment_mainGrid_status", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "Tag":
                        col.value = langStrings.FirstOrDefault(x =>
                                x.Key.Equals("investment_mainGrid_tag", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "MainProjectCode":
                        col.value = langStrings.FirstOrDefault(x =>
                                x.Key.Equals("investment_mainProjectCode", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "MainProjectName":
                        col.value = langStrings.FirstOrDefault(x =>
                                x.Key.Equals("investment_mainProject", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "FromPlan":
                    case "StatusPlan":
                        col.value = langStrings.FirstOrDefault(x => x.Key.Equals($"investment_overviewgrid_{(string)col.key}", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "PreviousBudgeted":
                        col.value = langStrings.FirstOrDefault(x => x.Key.Equals("investment_prevBudgeted", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "CostEstimate":
                        col.value = languageStrings.FirstOrDefault(x => x.Key.Equals("hdr_cost_estimate", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "ApprovedCost":
                        col.value = langStrings.FirstOrDefault(x =>
                                x.Key.Equals("investment_approvedCost", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "Year1Amount":
                        col.value = budgetYear.ToString();
                        break;

                    case "Year2Amount":
                        col.value = (budgetYear + 1).ToString();
                        break;

                    case "Year3Amount":
                        col.value = (budgetYear + 2).ToString();
                        break;

                    case "Year4Amount":
                        col.value = (budgetYear + 3).ToString();
                        break;

                    case "First4Years":
                        col.value = langStrings.FirstOrDefault(x => x.Key.Equals("investment_first4YearSum", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "Year5Amount":
                        col.value = (budgetYear + 4).ToString();
                        break;

                    case "Year6Amount":
                        col.value = (budgetYear + 5).ToString();
                        break;

                    case "Year7Amount":
                        col.value = (budgetYear + 6).ToString();
                        break;

                    case "Year8Amount":
                        col.value = (budgetYear + 7).ToString();
                        break;

                    case "Year9Amount":
                        col.value = (budgetYear + 8).ToString();
                        break;

                    case "Year10Amount":
                        col.value = (budgetYear + 9).ToString();
                        break;

                    //case "next6years":
                    //    col["title"] = (BudgetYear + 4).ToString() + "-" + (BudgetYear + 9).ToString();
                    //    break;
                    case "Total":
                        col.value = langStrings.FirstOrDefault(x => x.Key.Equals("cmn_title_total", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "FinancingSum":
                        col.value = langStrings.FirstOrDefault(x => x.Key.Equals("investment_financingSum", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "ChangeYear1":
                        col.value = langStrings.FirstOrDefault(x => x.Key.Equals("inv_title_change", StringComparison.InvariantCultureIgnoreCase)).Value.LangText + " " + (budgetYear).ToString();
                        break;

                    case "ChangeYear2":
                        col.value = langStrings.FirstOrDefault(x => x.Key.Equals("inv_title_change", StringComparison.InvariantCultureIgnoreCase)).Value.LangText + " " + (budgetYear + 1).ToString();
                        break;

                    case "ChangeYear3":
                        col.value = langStrings.FirstOrDefault(x => x.Key.Equals("inv_title_change", StringComparison.InvariantCultureIgnoreCase)).Value.LangText + " " + (budgetYear + 2).ToString();
                        break;

                    case "ChangeYear4":
                        col.value = langStrings.FirstOrDefault(x => x.Key.Equals("inv_title_change", StringComparison.InvariantCultureIgnoreCase)).Value.LangText + " " + (budgetYear + 3).ToString();
                        break;

                    case "ChangeYear5":
                        col.value = langStrings.FirstOrDefault(x => x.Key.Equals("inv_title_change", StringComparison.InvariantCultureIgnoreCase)).Value.LangText + " " + (budgetYear + 4).ToString();
                        break;

                    case "ChangeYear6":
                        col.value = langStrings.FirstOrDefault(x => x.Key.Equals("inv_title_change", StringComparison.InvariantCultureIgnoreCase)).Value.LangText + " " + (budgetYear + 5).ToString();
                        break;

                    case "ChangeYear7":
                        col.value = langStrings.FirstOrDefault(x => x.Key.Equals("inv_title_change", StringComparison.InvariantCultureIgnoreCase)).Value.LangText + " " + (budgetYear + 6).ToString();
                        break;

                    case "ChangeYear8":
                        col.value = langStrings.FirstOrDefault(x => x.Key.Equals("inv_title_change", StringComparison.InvariantCultureIgnoreCase)).Value.LangText + " " + (budgetYear + 7).ToString();
                        break;

                    case "ChangeYear9":
                        col.value = langStrings.FirstOrDefault(x => x.Key.Equals("inv_title_change", StringComparison.InvariantCultureIgnoreCase)).Value.LangText + " " + (budgetYear + 8).ToString();
                        break;

                    case "ChangeYear10":
                        col.value = langStrings.FirstOrDefault(x => x.Key.Equals("inv_title_change", StringComparison.InvariantCultureIgnoreCase)).Value.LangText + " " + (budgetYear + 9).ToString();
                        break;

                    case "SelectedYearColTotal":
                        col.value = langStrings.FirstOrDefault(x => x.Key.Equals("inv_selectedYearColTotal", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "OperationalExpense":
                        col.value = langStrings.FirstOrDefault(x => x.Key.Equals("inv_operationalExpense", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "chapter":
                        col.value = langStrings.FirstOrDefault(x => x.Key.Equals("inv_chapter", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "InvestmentPhaseName":
                        col.value = langStrings.FirstOrDefault(x => x.Key.Equals("invPhaseNameColTitle", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "select4yrs":
                        col.value = langStringsFinPlan.FirstOrDefault(x => x.Key.Equals("FP_investement_four_year_change_column", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "select10yrs":
                        col.value = langStringsFinPlan.FirstOrDefault(x => x.Key.Equals("FP_investement_ten_year_change_column", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;
                }
            }
            return finalList;
        }
        private List<ColumnSelectorColumn> Checkfor10yrsSetup(string selectedYear, List<ColumnSelectorColumn> finalList)
        {
            if (selectedYear == "4")
            {
                finalList.RemoveAll(x => x.section == "nextTenYears");
            }
            else if (selectedYear == "10")
            {
                foreach (ColumnSelectorColumn col in finalList)
                {
                    if (col.section == "nextTenYears")
                    {
                        col.section = "years";
                    }
                }
            }
            return finalList;

        }
        private async Task<List<string>> GetAdjustmentCodesAsync(string userId, InvProjGridInputHelper inputObject, int budgetYear, bool status)
        {
            UserAdjustmentCodeInput input = new UserAdjustmentCodeInput
            {
                budgetYear = budgetYear,
                includeApproved = true
            };
            List<tco_user_adjustment_codes> tcoUserAdjCodeDataList = (await _invProj.GetUserAdjustmentCodeAsync(input, userId, false)).ToList();

            List<string> userAdjCode = new List<string>();

            userAdjCode.AddRange(tcoUserAdjCodeDataList.Where(x => x.status == status).Select(x => x.pk_adj_code).ToList());//add appproved code

            userAdjCode.AddRange(tcoUserAdjCodeDataList.Where(x => x.include_in_calculation && x.budget_year == (budgetYear - 1)).Select(x => x.pk_adj_code).ToList());// include in cal flag set

            if (!string.IsNullOrEmpty(inputObject.UserAdjustmentCode))
            {
                userAdjCode.Add(inputObject.UserAdjustmentCode);
            }

            return userAdjCode;
        }

        private tco_projects GetTcoProjectObject(int budgetYear, string mainProjName, int endYear, UserData userDetails, string tempMainProjCode)
        {
            //Generate temp Proj code
            return new tco_projects
            {
                pk_project_code = tempMainProjCode,
                fk_tenant_id = userDetails.tenant_id,
                project_name = mainProjName,
                date_from = new DateTime(budgetYear, 01, 01),
                date_to = new DateTime(2099, 12, 31),
                active = 1,
                fk_main_project_code = tempMainProjCode,
                fk_responsible_id = 0,
                fk_prog_code = "1", 
                updated = DateTime.UtcNow,
                updated_by = userDetails.pk_id,
                is_temp = true
            };
        }

        private tco_main_projects GetMainProjectObject(int budgetYear, string mainProjName, int endYear, UserData userDetails, string tempMainProjCode, string orgId, int orgLevel, int? priorityKey = null)
        {
            return new tco_main_projects
            {
                pk_main_project_code = tempMainProjCode,
                fk_tenant_id = userDetails.tenant_id,
                main_project_name = mainProjName,
                budget_year_from = new DateTime(budgetYear, 01, 01),
                budget_year_to = new DateTime(2099, 12, 31),
                status = 1,
                inv_status = BListInvStatus,
                updated = DateTime.UtcNow,
                updated_by = userDetails.pk_id,
                fk_department_code = string.Empty,
                fk_Function_code = string.Empty,
                is_temp = true,
                parentOrgId = orgId,
                parentOrgLevel = orgLevel,
                priority = priorityKey,
            };
        }

        private async Task<List<ColumnSelectorColumn>> GetColumnConfig(string userId, string flagName, int budgetYear, List<ColumnSelectorColumn> allColumns)
        {
            UserData user = await _utility.GetUserDetailsAsync(userId);
            List<ColumnSelectorColumn> columnsConfig = new();
            var tenantColConfig = await _utility.GetApplicationFlag(userId, flagName, "-1", budgetYear);
            if (tenantColConfig != null)
            {
                Guid? flagGuid = tenantColConfig.flag_guid;
                if (flagGuid != null)
                {
                    clsTenantCloudTableEntity updateEntity = await _utility.GetCloudTableContentAsync("WADTenantData", user.tenant_id.ToString(), flagGuid.ToString());
                    if (updateEntity != null)
                    {
                        JArray columnsArray = JArray.FromObject(JsonConvert.DeserializeObject(updateEntity.data));
                        List<ColumnSelectorColumn> columnsList = columnsArray.ToObject<List<ColumnSelectorColumn>>();

                        columnsConfig = (from a in allColumns
                                         join b in columnsList on a.key equals b.key into grp
                                         from grp1 in grp.DefaultIfEmpty()
                                         select new ColumnSelectorColumn()
                                         {
                                             key = grp1 != null ? grp1.key : a.key,
                                             value = grp1 != null ? grp1.value : a.value,
                                             isChecked = grp1 != null ? grp1.isChecked : a.isChecked,
                                             isDefault = grp1 != null ? grp1.isDefault : a.isDefault,
                                             section = grp1 != null ? grp1.section : a.section,
                                         }).ToList();
                    }
                    else
                    {
                        columnsConfig = allColumns;
                    }
                }
            }
            else
            {
                columnsConfig = allColumns;
            }

            return columnsConfig;
        }

        private async Task<JArray> FormatSummaryTabColumnsAsync(string userId, Stream configStream, SummaryGridInput input)
        {
            StreamReader reader = new StreamReader(configStream);
            string config = await reader.ReadToEndAsync();
            JArray colConfig = JArray.Parse(config);

            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStrings = await _utility
                .GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "invdetails")
                ;

            // fetching the selected columns from the input obj
            List<string> selectedColumns = input.columnSelector.SelectMany(x => x.columns).Where(x => x.isChecked).Select(x => x.key).ToList();
            //Get the freedim data for investment screen
            IEnumerable<freedimDefinition> freeDimDef =
                (await _utility.GetFreeDimDefinitionsBasedOnPageIdAsync(userId, "investments")
                    ).ToList();

            JArray updatedColConfig = new();
            if (input.summaryTabType == InvSummaryTabType.alterCode)
            {
                var col = colConfig.SelectTokens("$.[?(@.field =='alterCode.Value')]");
                updatedColConfig.Add(col);
            }
            if (input.summaryTabType == InvSummaryTabType.budgetRound)
            {
                var col = colConfig.SelectTokens("$.[?(@.field =='changeCode.Value')]");
                updatedColConfig.Add(col);
            }
            if (input.summaryTabType == InvSummaryTabType.project)
            {
                var col = colConfig.SelectTokens("$.[?(@.field =='projectData.Value')]");
                updatedColConfig.Add(col);
            }
            //Replace language string specific properties
            foreach (var col in colConfig)
            {
                switch ((string)col["field"])
                {
                    case "accountData.Key":
                        col["title"] = langStrings.FirstOrDefault(x =>
                            x.Key.Equals("hdr_account", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        if (input.summaryTabType != InvSummaryTabType.summary)
                        {
                            col["width"] = 60;
                        }
                        updatedColConfig.Add(col);
                        break;

                    case "departmentData.Key":
                        col["title"] = langStrings.FirstOrDefault(x =>
                            x.Key.Equals("hdr_department", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        updatedColConfig.Add(col);
                        break;

                    case "functionData.Key":
                        col["title"] = langStrings.FirstOrDefault(x =>
                            x.Key.Equals("hdr_function", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        updatedColConfig.Add(col);
                        break;

                    case "projectData.Key":
                        if (input.summaryTabType != InvSummaryTabType.project)
                        {
                            col["title"] = langStrings.FirstOrDefault(x =>
                                    x.Key.Equals("hdr_project", StringComparison.InvariantCultureIgnoreCase)).Value
                                .LangText;
                            col["width"] = 60;
                            updatedColConfig.Add(col);
                        }
                        break;

                    case "freedim1Data.Key":
                        if (selectedColumns.Contains("free_dim_1"))
                        {
                            col["title"] = freeDimDef.FirstOrDefault(x => x.freeDimColumn == "free_dim_1").freeDimHeader;
                            updatedColConfig.Add(col);
                        }
                        break;

                    case "freedim2Data.Key":
                        if (selectedColumns.Contains("free_dim_2"))
                        {
                            col["title"] = freeDimDef.FirstOrDefault(x => x.freeDimColumn == "free_dim_2").freeDimHeader;
                            updatedColConfig.Add(col);
                        }
                        break;

                    case "freedim3Data.Key":
                        if (selectedColumns.Contains("free_dim_3"))
                        {
                            col["title"] = freeDimDef.FirstOrDefault(x => x.freeDimColumn == "free_dim_3").freeDimHeader;
                            updatedColConfig.Add(col);
                        }
                        break;

                    case "freedim4Data.Key":
                        if (selectedColumns.Contains("free_dim_4"))
                        {
                            col["title"] = freeDimDef.FirstOrDefault(x => x.freeDimColumn == "free_dim_4").freeDimHeader;
                            updatedColConfig.Add(col);
                        }
                        break;

                    case "total":
                        col["title"] = "Totalt";
                        updatedColConfig.Add(col);
                        break;

                    case "costEstimate":
                        if (selectedColumns.Contains("costEstimate"))
                        {
                            col["title"] = langStrings.FirstOrDefault(x =>
                                x.Key.Equals("hdr_cost_estimate", StringComparison.InvariantCultureIgnoreCase)).Value
                            .LangText;
                            updatedColConfig.Add(col);
                        }
                        break;

                    case "approvedCost":
                        if (selectedColumns.Contains("approvalCost"))
                        {
                            col["title"] = langStrings.FirstOrDefault(x =>
                                    x.Key.Equals("hdr_approved_cost", StringComparison.InvariantCultureIgnoreCase)).Value
                                .LangText;
                            updatedColConfig.Add(col);
                        }
                        break;

                    case "year":
                        int yearDiff = (input.estimatedFinish + 1) - input.startYear;
                        for (int i = 0; i < yearDiff; i++)
                        {
                            JToken yearCol = col;
                            yearCol["field"] = "year" + (i + 1).ToString();
                            yearCol["title"] = (input.startYear + i).ToString();
                            yearCol["template"] = "#if (" + "year" + (i + 1).ToString() + " == 0 || " + "year" +
                                                  (i + 1).ToString() +
                                                  "){if(isBold == true){if(isItalic==true){#<span class='semi font-italic'>#:kendo.toString(parseFloat(" +
                                                  "year" + (i + 1).ToString() +
                                                  "), numberFormat)#</span> #}else{# <span class='semi'>#:kendo.toString(parseFloat(" +
                                                  "year" + (i + 1).ToString() +
                                                  "), numberFormat)#</span> # } }else { if(isItalic == true){#<span class='semi font-italic'>#:kendo.toString(parseFloat(" +
                                                  "year" + (i + 1).ToString() +
                                                  "), numberFormat)#</span>#}else{#<span>#:kendo.toString(parseFloat(" +
                                                  "year" + (i + 1).ToString() +
                                                  "), numberFormat)#</span>#}}}else{#<span> <span>#}#";
                            updatedColConfig.Add(col);
                        }

                        break;

                    case "programCode.Key":
                        if (selectedColumns.Contains("program"))
                        {
                            col["title"] = langStrings.FirstOrDefault(x =>
                                x.Key.Equals("inv_program_code", StringComparison.InvariantCultureIgnoreCase)).Value
                            .LangText;
                            updatedColConfig.Add(col);
                        }
                        break;

                    case "alterCode.Value":
                        if (input.summaryTabType != InvSummaryTabType.alterCode && input.summaryTabType != InvSummaryTabType.project && input.summaryTabType != InvSummaryTabType.summary)
                        {
                            col["title"] = langStrings.FirstOrDefault(x =>
                                    x.Key.Equals("hdr_alter_code", StringComparison.InvariantCultureIgnoreCase)).Value
                                .LangText;
                            col["width"] = 60;
                            updatedColConfig.Add(col);
                        }
                        break;

                    case "changeCode.Value":
                        //budgetround tab
                        if (input.summaryTabType != InvSummaryTabType.budgetRound && input.summaryTabType != InvSummaryTabType.project && input.summaryTabType != InvSummaryTabType.summary)
                        {
                            col["title"] = langStrings.FirstOrDefault(x =>
                                    x.Key.Equals("inv_budget_change", StringComparison.InvariantCultureIgnoreCase))
                                .Value
                                .LangText;
                            col["width"] = 60;
                            updatedColConfig.Add(col);
                        }
                        break;

                    case "vatRefund":
                        if (selectedColumns.Contains("vatRefund"))
                        {
                            col["title"] = langStrings.FirstOrDefault(x =>
                            x.Key.Equals("hdr_vat_refund", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                            updatedColConfig.Add(col);
                        }
                        break;

                    case "vatRate":
                        if (selectedColumns.Contains("vatRate"))
                        {
                            col["title"] = langStrings.FirstOrDefault(x =>
                            x.Key.Equals("hdr_vat_rate", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                            updatedColConfig.Add(col);
                        }
                        break;

                    case "description":
                        col["title"] = langStrings.FirstOrDefault(x =>
                                x.Key.Equals("hdr_description", StringComparison.InvariantCultureIgnoreCase)).Value
                            .LangText;
                        updatedColConfig.Add(col);
                        break;
                }
            }
            return updatedColConfig;
        }

        private async Task<JArray> FormatSummaryTabAsync(string userId, IEnumerable<InvestmentSummaryGrid> summaryData, SummaryGridInput input)
        {
            int idCounter = 1;
            List<string> accTypeLevel1 = summaryData.OrderByDescending(y => y.accountType).Select(x => x.accountType).Distinct().ToList();
            int yearDiff = (input.estimatedFinish + 1) - input.startYear;
            dynamic gridData = new JArray();
            decimal totalApprovedCost = 0;
            decimal approvedCost = 0;
            decimal totalCostEstimate = 0;
            decimal costEstimate = 0;
            decimal totalrow = 0;
            foreach (var level1Data in accTypeLevel1)
            {
                int parentId = idCounter;
                var invDetAcType = (from idt in summaryData
                                    where idt.accountType == level1Data
                                    group idt by new
                                    {
                                        fk_account_code = idt.accountData,
                                        fk_department_code = idt.departmentData,
                                        fk_function_code = idt.functionData,
                                        fk_project_code = idt.projectData,
                                        free_dim_1 = idt.freeDim1,
                                        free_dim_2 = idt.freeDim2,
                                        free_dim_3 = idt.freeDim3,
                                        free_dim_4 = idt.freeDim4,
                                        fk_program_code = idt.programCode,
                                        idt.vatRate,
                                        idt.vatRefund,
                                        idt.accountType,
                                        //idt.activeChange,
                                        //idt.changeBudgetYear
                                    } into grp
                                    select new InvestmentSummaryGrid
                                    {
                                        accountData = grp.Key.fk_account_code,
                                        departmentData = grp.Key.fk_department_code,
                                        functionData = grp.Key.fk_function_code,
                                        projectData = grp.Key.fk_project_code,
                                        freeDim1 = grp.Key.free_dim_1,
                                        freeDim2 = grp.Key.free_dim_2,
                                        freeDim3 = grp.Key.free_dim_3,
                                        freeDim4 = grp.Key.free_dim_4,
                                        programCode = grp.Key.fk_program_code,
                                        vatRate = grp.Key.vatRate,
                                        vatRefund = grp.Key.vatRefund,
                                        accountType = grp.Key.accountType,
                                        //activeChange = grp.Key.activeChange,
                                        amount = grp.Sum(x => x.amount)
                                        //changeBudgetYear = grp.Key.changeBudgetYear
                                    }).ToList();

                var dynamicObject = await GetParentDataAsync(userId, level1Data, input.summaryTabType, parentId, "");
                decimal total = 0;
                bool isYearZero = true;
                for (int i = 0; i <= yearDiff; i++)
                {
                    decimal yearAmount = 0;
                    if (summaryData.FirstOrDefault(x => x.accountType == level1Data /*&& x.changeBudgetYear == input.budgetYear*/ && x.year == input.startYear + i) != null)
                    {
                        yearAmount = summaryData.Where(x => x.accountType == level1Data /*&& x.changeBudgetYear == input.budgetYear*/ && x.year == (input.startYear + i)).Sum(y => y.amount);
                    }
                    total += yearAmount;
                    dynamicObject.Add("year" + (i + 1).ToString(), yearAmount);

                    if (yearAmount != 0)
                    {
                        isYearZero = false;
                    }
                }
                approvedCost = 0;
                if (summaryData.FirstOrDefault(x => x.accountType == level1Data && x.year == -1) != null)
                {
                    approvedCost = summaryData.Where(x => x.accountType == level1Data && x.year == -1).Sum(y => y.amount);
                }
                costEstimate = 0;
                if (summaryData.FirstOrDefault(x => x.accountType == level1Data && x.year == ConstantKeys._costEstimateYear) != null)
                {
                    costEstimate = summaryData.Where(x => x.accountType == level1Data && x.year == ConstantKeys._costEstimateYear).Sum(y => y.amount);
                }
                totalrow += total;
                totalApprovedCost += Math.Round(approvedCost);
                totalCostEstimate += Math.Round(costEstimate);
                dynamicObject.Add("total", total);
                dynamicObject.Add("costEstimate", Math.Round(costEstimate));
                dynamicObject.Add("approvedCost", Math.Round(approvedCost));

                if (!isYearZero)
                {
                    gridData.Add(JToken.FromObject(dynamicObject));
                }

                JObject data = await GetAllDataAsync(userId, summaryData, input);
                foreach (var item in invDetAcType.OrderBy(x => x.accountData))
                {
                    idCounter++;
                    dynamicObject = await FormatChildDataAsync(userId, input, parentId, idCounter, item, level1Data, data, input.summaryTabType);
                    total = 0;
                    isYearZero = true;

                    for (int i = 0; i <= yearDiff; i++)
                    {
                        decimal yearAmount = 0;
                        if (summaryData.FirstOrDefault(x => x.accountData == item.accountData && x.departmentData == item.departmentData &&
                        x.functionData == item.functionData && x.projectData == item.projectData && x.freeDim1 == item.freeDim1 && x.freeDim2 == item.freeDim2 &&
                        x.freeDim3 == item.freeDim3 && x.freeDim4 == item.freeDim4 && x.vatRate == item.vatRate && x.vatRefund == item.vatRefund && x.year == (input.startYear + i) /*&& x.changeBudgetYear == input.budgetYear*/) != null)
                        {
                            yearAmount = summaryData.FirstOrDefault(x => x.accountData == item.accountData && x.departmentData == item.departmentData &&
                                        x.functionData == item.functionData && x.projectData == item.projectData && x.freeDim1 == item.freeDim1 && x.freeDim2 == item.freeDim2 &&
                                        x.freeDim3 == item.freeDim3 && x.freeDim4 == item.freeDim4 && x.vatRate == item.vatRate && x.vatRefund == item.vatRefund && x.year == (input.startYear + i) /*&& x.changeBudgetYear == input.budgetYear*/).amount;
                        }
                        total += yearAmount;
                        dynamicObject.Add("year" + (i + 1).ToString(), yearAmount);

                        if (yearAmount != 0)
                        {
                            isYearZero = false;
                        }
                    }
                    //Get cost estimate row
                    costEstimate = 0;
                    if (summaryData.FirstOrDefault(x => x.accountData == item.accountData && x.departmentData == item.departmentData &&
                    x.functionData == item.functionData && x.projectData == item.projectData && x.freeDim1 == item.freeDim1 && x.freeDim2 == item.freeDim2 &&
                    x.freeDim3 == item.freeDim3 && x.freeDim4 == item.freeDim4 && x.vatRate == item.vatRate && x.vatRefund == item.vatRefund && x.year == ConstantKeys._costEstimateYear) != null)
                    {
                        costEstimate = summaryData.FirstOrDefault(x => x.accountData == item.accountData && x.departmentData == item.departmentData &&
                                    x.functionData == item.functionData && x.projectData == item.projectData && x.freeDim1 == item.freeDim1 && x.freeDim2 == item.freeDim2 &&
                                    x.freeDim3 == item.freeDim3 && x.freeDim4 == item.freeDim4 && x.vatRate == item.vatRate && x.vatRefund == item.vatRefund && x.year == ConstantKeys._costEstimateYear).amount;
                    }
                    //Get approved cost row
                    approvedCost = 0;
                    if (summaryData.FirstOrDefault(x => x.accountData == item.accountData && x.departmentData == item.departmentData &&
                    x.functionData == item.functionData && x.projectData == item.projectData && x.freeDim1 == item.freeDim1 && x.freeDim2 == item.freeDim2 &&
                    x.freeDim3 == item.freeDim3 && x.freeDim4 == item.freeDim4 && x.vatRate == item.vatRate && x.vatRefund == item.vatRefund && x.year == -1) != null)
                    {
                        approvedCost = summaryData.FirstOrDefault(x => x.accountData == item.accountData && x.departmentData == item.departmentData &&
                                    x.functionData == item.functionData && x.projectData == item.projectData && x.freeDim1 == item.freeDim1 && x.freeDim2 == item.freeDim2 &&
                                    x.freeDim3 == item.freeDim3 && x.freeDim4 == item.freeDim4 && x.vatRate == item.vatRate && x.vatRefund == item.vatRefund && x.year == -1).amount;
                    }
                    dynamicObject.Add("costEstimate", costEstimate);
                    dynamicObject.Add("approvedCost", approvedCost);
                    dynamicObject.Add("total", total);
                    if (!isYearZero)
                    {
                        gridData.Add(JToken.FromObject(dynamicObject));
                    }
                }
                idCounter++;
            }
            //summary line
            var resultObject = await FormatTotalRowAsync(userId);
            decimal summarytotal = 0;
            for (int i = 0; i <= yearDiff; i++)
            {
                decimal yearAmount = 0;

                if (summaryData.FirstOrDefault(x => /*x.changeBudgetYear == input.budgetYear && */x.year == (input.startYear + i)) != null)
                {
                    yearAmount = summaryData.Where(x => /*x.changeBudgetYear == input.budgetYear &&*/ x.year == (input.startYear + i)).Sum(y => y.amount);
                }
                summarytotal += yearAmount;
                resultObject.Add("year" + (i + 1).ToString(), yearAmount);
            }
            resultObject.Add("costEstimate", totalCostEstimate);
            resultObject.Add("approvedCost", totalApprovedCost);
            resultObject.Add("total", totalrow);
            gridData.Add(JToken.FromObject(resultObject));
            return gridData;
        }

        private async Task<JArray> FormatProjectTabAsync(string userId, IEnumerable<InvestmentSummaryGrid> summaryData, SummaryGridInput input)
        {
            int idCounter = 1;
            List<string> disLevel1Data = summaryData.Select(x => x.projectDesc).Distinct().ToList();
            int yearDiff = (input.estimatedFinish + 1) - input.startYear;
            dynamic gridData = new JArray();
            JObject data = await GetAllDataAsync(userId, summaryData, input);
            decimal totalApprovedCost = 0;
            decimal approvedCost = 0;
            decimal totalCostEstimate = 0;
            decimal costEstimate = 0;
            foreach (var level1Code in disLevel1Data)
            {
                int parentId = idCounter;
                string projCode = summaryData.FirstOrDefault(x => x.projectDesc == level1Code) == null ? "" : summaryData.FirstOrDefault(x => x.projectDesc == level1Code).projectData;
                var invDetAcType = (from idt in summaryData
                                    where idt.projectDesc == level1Code
                                    group idt by new
                                    {
                                        fk_account_code = idt.accountData,
                                        fk_department_code = idt.departmentData,
                                        fk_function_code = idt.functionData,
                                        fk_project_code = idt.projectData,
                                        free_dim_1 = idt.freeDim1,
                                        free_dim_2 = idt.freeDim2,
                                        free_dim_3 = idt.freeDim3,
                                        free_dim_4 = idt.freeDim4,
                                        fk_program_code = idt.programCode,
                                        idt.vatRate,
                                        idt.vatRefund,
                                        idt.accountType,
                                        idt.activeChange,
                                        //idt.changeBudgetYear,
                                        idt.projectDesc
                                    } into grp
                                    select new InvestmentSummaryGrid
                                    {
                                        accountData = grp.Key.fk_account_code,
                                        departmentData = grp.Key.fk_department_code,
                                        functionData = grp.Key.fk_function_code,
                                        projectData = grp.Key.fk_project_code,
                                        freeDim1 = grp.Key.free_dim_1,
                                        freeDim2 = grp.Key.free_dim_2,
                                        freeDim3 = grp.Key.free_dim_3,
                                        freeDim4 = grp.Key.free_dim_4,
                                        programCode = grp.Key.fk_program_code,
                                        vatRate = grp.Key.vatRate,
                                        vatRefund = grp.Key.vatRefund,
                                        accountType = grp.Key.accountType,
                                        activeChange = grp.Key.activeChange,
                                        amount = grp.Sum(x => x.amount),
                                        //changeBudgetYear = grp.Key.changeBudgetYear,
                                        projectDesc = grp.Key.projectDesc
                                    }).ToList();

                var dynamicObject = await GetParentDataAsync(userId, level1Code, input.summaryTabType, parentId, projCode);
                decimal total = 0;
                for (int i = 0; i <= yearDiff; i++)
                {
                    decimal yearAmount = 0;
                    if (summaryData.FirstOrDefault(x => x.projectDesc == level1Code /*&& x.changeBudgetYear == input.budgetYear */&& x.year == input.startYear + i) != null)
                    {
                        yearAmount = summaryData.Where(x => x.projectDesc == level1Code /*&& x.changeBudgetYear == input.budgetYear*/ && x.year == (input.startYear + i)).Sum(y => y.amount);
                    }
                    dynamicObject.Add("year" + (i + 1).ToString(), yearAmount);
                    total += yearAmount;
                }
                approvedCost = 0;
                if (summaryData.FirstOrDefault(x => x.projectDesc == level1Code && x.year == -1) != null)
                {
                    approvedCost = summaryData.Where(x => x.projectDesc == level1Code && x.year == -1).Sum(y => y.amount);
                }
                costEstimate = 0;
                if (summaryData.FirstOrDefault(x => x.projectDesc == level1Code && x.year == ConstantKeys._costEstimateYear) != null)
                {
                    costEstimate = summaryData.Where(x => x.projectDesc == level1Code && x.year == ConstantKeys._costEstimateYear).Sum(y => y.amount);
                }
                totalCostEstimate += Math.Round(costEstimate);
                totalApprovedCost += Math.Round(approvedCost);
                dynamicObject.Add("total", total);
                dynamicObject.Add("costEstimate", Math.Round(costEstimate));
                dynamicObject.Add("approvedCost", Math.Round(approvedCost));
                gridData.Add(JToken.FromObject(dynamicObject));
                approvedCost = 0;
                costEstimate = 0;
                foreach (var item in invDetAcType)
                {
                    idCounter++;
                    dynamicObject = await FormatChildDataAsync(userId, input, parentId, idCounter, item, projCode, data, input.summaryTabType);
                    total = 0;
                    approvedCost = 0;
                    costEstimate = 0;
                    for (int i = 0; i <= yearDiff; i++)
                    {
                        decimal yearAmount = 0;
                        if (summaryData.FirstOrDefault(x => x.accountData == item.accountData && x.departmentData == item.departmentData &&
                        x.functionData == item.functionData && x.projectData == item.projectData && x.freeDim1 == item.freeDim1 && x.freeDim2 == item.freeDim2 &&
                        x.freeDim3 == item.freeDim3 && x.freeDim4 == item.freeDim4 && x.vatRate == item.vatRate && x.vatRefund == item.vatRefund && x.year == (input.startYear + i) /*&& x.changeBudgetYear == input.budgetYear*/) != null)
                        {
                            yearAmount = summaryData.Where(x => x.accountData == item.accountData && x.departmentData == item.departmentData &&
                                        x.functionData == item.functionData && x.projectData == item.projectData && x.freeDim1 == item.freeDim1 && x.freeDim2 == item.freeDim2 &&
                                        x.freeDim3 == item.freeDim3 && x.freeDim4 == item.freeDim4 && x.vatRate == item.vatRate && x.vatRefund == item.vatRefund && x.year == (input.startYear + i) /*&& x.changeBudgetYear == input.budgetYear*/).Sum(x => x.amount);
                        }
                        total += yearAmount;
                        dynamicObject.Add("year" + (i + 1).ToString(), yearAmount);
                    }
                    if (summaryData.FirstOrDefault(x => x.accountData == item.accountData && x.departmentData == item.departmentData &&
                       x.functionData == item.functionData && x.projectData == item.projectData && x.freeDim1 == item.freeDim1 && x.freeDim2 == item.freeDim2 &&
                       x.freeDim3 == item.freeDim3 && x.freeDim4 == item.freeDim4 && x.vatRate == item.vatRate && x.vatRefund == item.vatRefund && x.year == -1 /*&& x.changeBudgetYear == input.budgetYear*/) != null)
                    {
                        approvedCost = summaryData.Where(x => x.accountData == item.accountData && x.departmentData == item.departmentData &&
                                    x.functionData == item.functionData && x.projectData == item.projectData && x.freeDim1 == item.freeDim1 && x.freeDim2 == item.freeDim2 &&
                                    x.freeDim3 == item.freeDim3 && x.freeDim4 == item.freeDim4 && x.vatRate == item.vatRate && x.vatRefund == item.vatRefund && x.year == -1 /*&& x.changeBudgetYear == input.budgetYear*/).Sum(x => x.amount);
                    }
                    // cost estimate change
                    if (summaryData.FirstOrDefault(x => x.accountData == item.accountData && x.departmentData == item.departmentData &&
                       x.functionData == item.functionData && x.projectData == item.projectData && x.freeDim1 == item.freeDim1 && x.freeDim2 == item.freeDim2 &&
                       x.freeDim3 == item.freeDim3 && x.freeDim4 == item.freeDim4 && x.vatRate == item.vatRate && x.vatRefund == item.vatRefund && x.year == ConstantKeys._costEstimateYear) != null)
                    {
                        costEstimate = summaryData.Where(x => x.accountData == item.accountData && x.departmentData == item.departmentData &&
                                    x.functionData == item.functionData && x.projectData == item.projectData && x.freeDim1 == item.freeDim1 && x.freeDim2 == item.freeDim2 &&
                                    x.freeDim3 == item.freeDim3 && x.freeDim4 == item.freeDim4 && x.vatRate == item.vatRate && x.vatRefund == item.vatRefund && x.year == ConstantKeys._costEstimateYear).Sum(x => x.amount);
                    }
                    dynamicObject.Add("total", total);
                    dynamicObject.Add("costEstimate", costEstimate);
                    dynamicObject.Add("approvedCost", approvedCost);
                    gridData.Add(JToken.FromObject(dynamicObject));
                }
                idCounter++;
            }
            //summary line
            decimal summarytotal = 0;
            var resultObject = await FormatTotalRowAsync(userId);
            for (int i = 0; i <= yearDiff; i++)
            {
                decimal yearAmount = 0;

                if (summaryData.FirstOrDefault(x => /*x.changeBudgetYear == input.budgetYear &&*/ x.year == input.startYear + i) != null)
                {
                    yearAmount = summaryData.Where(x => /*x.changeBudgetYear == input.budgetYear && */x.year == input.startYear + i).Sum(y => y.amount);
                }
                summarytotal += yearAmount;
                resultObject.Add("year" + (i + 1).ToString(), yearAmount);
            }
            resultObject.Add("total", summarytotal);
            resultObject.Add("costEstimate", totalCostEstimate);
            resultObject.Add("approvedCost", totalApprovedCost);
            gridData.Add(JToken.FromObject(resultObject));
            return gridData;
        }

        private async Task<JArray> FormatAlterOrBudgetRoundTabAsync(string userId, IEnumerable<InvestmentSummaryGrid> summaryData, SummaryGridInput input)
        {
            int idCounter = 1;
            List<string> disLevel1Data;
            decimal totalApprovedCost = 0;
            decimal approvedCost = 0;
            decimal totalCostEstimate = 0;
            decimal costEstimate = 0;
            if (input.summaryTabType == InvSummaryTabType.alterCode)
            {
                disLevel1Data = summaryData.Select(x => x.alterCodeDesc).Distinct().OrderBy(x => x).ToList();
            }
            else
            {
                disLevel1Data = summaryData.Select(x => x.changeDesc).Distinct().ToList();
            }
            int yearDiff = (input.estimatedFinish + 1) - input.startYear;
            dynamic gridData = new JArray();
            JObject data = await GetAllDataAsync(userId, summaryData, input);
            List<tfp_budget_changes> budgetChangeData = data.SelectToken("budgetChangeData").ToObject<List<tfp_budget_changes>>();
            foreach (var level1Code in disLevel1Data)
            {
                int parentId = idCounter;
                string levelCode = string.Empty;
                List<InvestmentSummaryGrid> invDetAcType;
                if (input.summaryTabType == InvSummaryTabType.alterCode)
                {
                    levelCode = summaryData.FirstOrDefault(x => x.alterCodeDesc == level1Code).alterCode;
                    invDetAcType = (from idt in summaryData
                                    where input.summaryTabType == InvSummaryTabType.alterCode ? idt.alterCodeDesc == level1Code : idt.changeDesc == level1Code
                                    group idt by new
                                    {
                                        fk_account_code = idt.accountData,
                                        fk_department_code = idt.departmentData,
                                        fk_function_code = idt.functionData,
                                        fk_project_code = idt.projectData,
                                        free_dim_1 = idt.freeDim1,
                                        free_dim_2 = idt.freeDim2,
                                        free_dim_3 = idt.freeDim3,
                                        free_dim_4 = idt.freeDim4,
                                        fk_program_code = idt.programCode,
                                        idt.vatRate,
                                        idt.vatRefund,
                                        //idt.changeBudgetYear,
                                        idt.alterCode
                                    } into grp
                                    select new InvestmentSummaryGrid
                                    {
                                        accountData = grp.Key.fk_account_code,
                                        departmentData = grp.Key.fk_department_code,
                                        functionData = grp.Key.fk_function_code,
                                        projectData = grp.Key.fk_project_code,
                                        freeDim1 = grp.Key.free_dim_1,
                                        freeDim2 = grp.Key.free_dim_2,
                                        freeDim3 = grp.Key.free_dim_3,
                                        freeDim4 = grp.Key.free_dim_4,
                                        programCode = grp.Key.fk_program_code,
                                        vatRate = grp.Key.vatRate,
                                        vatRefund = grp.Key.vatRefund,
                                        amount = grp.Sum(x => x.amount),
                                        //changeBudgetYear = grp.Key.changeBudgetYear,
                                        alterCode = grp.Key.alterCode
                                    }).ToList();
                }
                else
                {
                    levelCode = summaryData.FirstOrDefault(x => x.changeDesc == level1Code).changeId.ToString();
                    invDetAcType = (from idt in summaryData
                                    where input.summaryTabType == InvSummaryTabType.alterCode ? idt.alterCodeDesc == level1Code : idt.changeDesc == level1Code
                                    group idt by new
                                    {
                                        fk_account_code = idt.accountData,
                                        fk_department_code = idt.departmentData,
                                        fk_function_code = idt.functionData,
                                        fk_project_code = idt.projectData,
                                        free_dim_1 = idt.freeDim1,
                                        free_dim_2 = idt.freeDim2,
                                        free_dim_3 = idt.freeDim3,
                                        free_dim_4 = idt.freeDim4,
                                        fk_program_code = idt.programCode,
                                        idt.vatRate,
                                        idt.vatRefund,
                                        //idt.changeBudgetYear,
                                        idt.changeId
                                    } into grp
                                    select new InvestmentSummaryGrid
                                    {
                                        accountData = grp.Key.fk_account_code,
                                        departmentData = grp.Key.fk_department_code,
                                        functionData = grp.Key.fk_function_code,
                                        projectData = grp.Key.fk_project_code,
                                        freeDim1 = grp.Key.free_dim_1,
                                        freeDim2 = grp.Key.free_dim_2,
                                        freeDim3 = grp.Key.free_dim_3,
                                        freeDim4 = grp.Key.free_dim_4,
                                        programCode = grp.Key.fk_program_code,
                                        vatRate = grp.Key.vatRate,
                                        vatRefund = grp.Key.vatRefund,
                                        amount = grp.Sum(x => x.amount),
                                        //changeBudgetYear = grp.Key.changeBudgetYear,
                                        changeId = grp.Key.changeId
                                    }).ToList();
                }

                var dynamicObject = await GetParentDataAsync(userId, level1Code, input.summaryTabType, parentId, levelCode);
                dynamic KeyValue;
                if (input.summaryTabType == InvSummaryTabType.budgetRound)
                {
                    KeyValue = new JObject();
                    KeyValue.Key = levelCode;
                    KeyValue.Value = budgetChangeData.FirstOrDefault(x => x.pk_change_id == Convert.ToInt32(levelCode)).approval_reference + " " + budgetChangeData.FirstOrDefault(x => x.pk_change_id == Convert.ToInt32(levelCode)).budget_name;
                    dynamicObject.Add("changeCode", KeyValue);
                }
                decimal total = 0;
                approvedCost = 0;
                costEstimate = 0;
                for (int i = 0; i <= yearDiff; i++)
                {
                    decimal yearAmount = 0;
                    if (input.summaryTabType == InvSummaryTabType.alterCode)
                    {
                        if (summaryData.FirstOrDefault(x => x.alterCodeDesc == level1Code &&
                                                               x.year == (input.startYear + i)) != null)
                        {
                            yearAmount = summaryData.Where(x => x.alterCodeDesc == level1Code && x.year == (input.startYear + i)).Sum(y => y.amount);
                        }
                    }
                    else
                    {
                        if (summaryData.FirstOrDefault(x => x.changeDesc == level1Code &&
                                                                  x.year == (input.startYear + i)) != null)
                        {
                            yearAmount = summaryData.Where(x => x.changeDesc == level1Code &&
                                                                  x.year == (input.startYear + i)).Sum(y => y.amount);
                        }
                    }
                    dynamicObject.Add("year" + (i + 1).ToString(), yearAmount);
                    total += yearAmount;
                }
                if (summaryData.FirstOrDefault(x => input.summaryTabType == InvSummaryTabType.alterCode ? x.alterCodeDesc == level1Code : x.changeDesc == level1Code && x.year == -1) != null)
                {
                    approvedCost = input.summaryTabType == InvSummaryTabType.alterCode ? summaryData.Where(x => x.alterCodeDesc == level1Code && x.year == -1).Sum(y => y.amount) :
                                                                                         summaryData.Where(x => x.changeDesc == level1Code && x.year == -1).Sum(y => y.amount);
                }
                if (summaryData.FirstOrDefault(x => input.summaryTabType == InvSummaryTabType.alterCode ? x.alterCodeDesc == level1Code : x.changeDesc == level1Code && x.year == ConstantKeys._costEstimateYear) != null)
                {
                    costEstimate = input.summaryTabType == InvSummaryTabType.alterCode ? summaryData.Where(x => x.alterCodeDesc == level1Code && x.year == -1).Sum(y => y.amount) :
                                                                                         summaryData.Where(x => x.changeDesc == level1Code && x.year == -1).Sum(y => y.amount);
                }
                totalCostEstimate += Math.Round(costEstimate);
                totalApprovedCost += Math.Round(approvedCost);
                dynamicObject.Add("total", total);
                dynamicObject.Add("costEstimate", Math.Round(costEstimate));
                dynamicObject.Add("approvedCost", Math.Round(approvedCost));
                gridData.Add(JToken.FromObject(dynamicObject));
                foreach (var item in invDetAcType)
                {
                    idCounter++;
                    dynamicObject = await FormatChildDataAsync(userId, input, parentId, idCounter, item, level1Code, data, input.summaryTabType);
                    total = 0;
                    approvedCost = 0;
                    costEstimate = 0;
                    for (int i = 0; i <= yearDiff; i++)
                    {
                        decimal yearAmount = 0;
                        if (input.summaryTabType == InvSummaryTabType.alterCode)
                        {
                            if (summaryData.FirstOrDefault(x => x.accountData == item.accountData && x.departmentData == item.departmentData &&
                            x.functionData == item.functionData && x.projectData == item.projectData && x.freeDim1 == item.freeDim1 && x.freeDim2 == item.freeDim2 &&
                            x.freeDim3 == item.freeDim3 && x.freeDim4 == item.freeDim4 && x.vatRate == item.vatRate && x.vatRefund == item.vatRefund && x.alterCode == item.alterCode && x.year == (input.startYear + i) /*&& x.changeBudgetYear == input.budgetYear*/) != null)
                            {
                                yearAmount = summaryData.Where(x => x.accountData == item.accountData && x.departmentData == item.departmentData &&
                                            x.functionData == item.functionData && x.projectData == item.projectData && x.freeDim1 == item.freeDim1 && x.freeDim2 == item.freeDim2 &&
                                            x.freeDim3 == item.freeDim3 && x.freeDim4 == item.freeDim4 && x.vatRate == item.vatRate && x.vatRefund == item.vatRefund && x.alterCode == item.alterCode && x.year == (input.startYear + i) /*&& x.changeBudgetYear == input.budgetYear*/).Sum(x => x.amount);
                            }
                        }
                        else
                        {
                            if (summaryData.FirstOrDefault(x => x.accountData == item.accountData && x.departmentData == item.departmentData &&
                            x.functionData == item.functionData && x.projectData == item.projectData && x.freeDim1 == item.freeDim1 && x.freeDim2 == item.freeDim2 &&
                            x.freeDim3 == item.freeDim3 && x.freeDim4 == item.freeDim4 && x.vatRate == item.vatRate && x.vatRefund == item.vatRefund && x.changeId == item.changeId && x.year == (input.startYear + i) /*&& x.changeBudgetYear == input.budgetYear*/) != null)
                            {
                                yearAmount = summaryData.Where(x => x.accountData == item.accountData && x.departmentData == item.departmentData &&
                                            x.functionData == item.functionData && x.projectData == item.projectData && x.freeDim1 == item.freeDim1 && x.freeDim2 == item.freeDim2 &&
                                            x.freeDim3 == item.freeDim3 && x.freeDim4 == item.freeDim4 && x.vatRate == item.vatRate && x.vatRefund == item.vatRefund && x.changeId == item.changeId && x.year == (input.startYear + i) /*&& x.changeBudgetYear == input.budgetYear*/).Sum(x => x.amount);
                            }
                        }

                        total += yearAmount;
                        dynamicObject.Add("year" + (i + 1).ToString(), yearAmount);
                    }
                    if (input.summaryTabType == InvSummaryTabType.alterCode)
                    {
                        if (summaryData.FirstOrDefault(x => x.accountData == item.accountData && x.departmentData == item.departmentData &&
                           x.functionData == item.functionData && x.projectData == item.projectData && x.freeDim1 == item.freeDim1 && x.freeDim2 == item.freeDim2 &&
                           x.freeDim3 == item.freeDim3 && x.freeDim4 == item.freeDim4 && x.vatRate == item.vatRate && x.vatRefund == item.vatRefund && x.alterCode == item.alterCode && x.year == -1 /*&& x.changeBudgetYear == input.budgetYear*/) != null)
                        {
                            approvedCost = summaryData.Where(x => x.accountData == item.accountData && x.departmentData == item.departmentData &&
                                        x.functionData == item.functionData && x.projectData == item.projectData && x.freeDim1 == item.freeDim1 && x.freeDim2 == item.freeDim2 &&
                                        x.freeDim3 == item.freeDim3 && x.freeDim4 == item.freeDim4 && x.vatRate == item.vatRate && x.vatRefund == item.vatRefund && x.alterCode == item.alterCode && x.year == -1/*&& x.changeBudgetYear == input.budgetYear*/).Sum(x => x.amount);
                        }
                    }
                    else
                    {
                        if (summaryData.FirstOrDefault(x => x.accountData == item.accountData && x.departmentData == item.departmentData &&
                           x.functionData == item.functionData && x.projectData == item.projectData && x.freeDim1 == item.freeDim1 && x.freeDim2 == item.freeDim2 &&
                           x.freeDim3 == item.freeDim3 && x.freeDim4 == item.freeDim4 && x.vatRate == item.vatRate && x.vatRefund == item.vatRefund && x.changeId == item.changeId && x.year == -1 /*&& x.changeBudgetYear == input.budgetYear*/) != null)
                        {
                            approvedCost = summaryData.Where(x => x.accountData == item.accountData && x.departmentData == item.departmentData &&
                                        x.functionData == item.functionData && x.projectData == item.projectData && x.freeDim1 == item.freeDim1 && x.freeDim2 == item.freeDim2 &&
                                        x.freeDim3 == item.freeDim3 && x.freeDim4 == item.freeDim4 && x.vatRate == item.vatRate && x.vatRefund == item.vatRefund && x.changeId == item.changeId && x.year == -1/*&& x.changeBudgetYear == input.budgetYear*/).Sum(x => x.amount);
                        }
                    }
                    if (input.summaryTabType == InvSummaryTabType.alterCode)
                    {
                        if (summaryData.FirstOrDefault(x => x.accountData == item.accountData && x.departmentData == item.departmentData &&
                           x.functionData == item.functionData && x.projectData == item.projectData && x.freeDim1 == item.freeDim1 && x.freeDim2 == item.freeDim2 &&
                           x.freeDim3 == item.freeDim3 && x.freeDim4 == item.freeDim4 && x.vatRate == item.vatRate && x.vatRefund == item.vatRefund && x.alterCode == item.alterCode && x.year == ConstantKeys._costEstimateYear) != null)
                        {
                            costEstimate = summaryData.Where(x => x.accountData == item.accountData && x.departmentData == item.departmentData &&
                                        x.functionData == item.functionData && x.projectData == item.projectData && x.freeDim1 == item.freeDim1 && x.freeDim2 == item.freeDim2 &&
                                        x.freeDim3 == item.freeDim3 && x.freeDim4 == item.freeDim4 && x.vatRate == item.vatRate && x.vatRefund == item.vatRefund && x.alterCode == item.alterCode && x.year == ConstantKeys._costEstimateYear).Sum(x => x.amount);
                        }
                    }
                    else
                    {
                        if (summaryData.FirstOrDefault(x => x.accountData == item.accountData && x.departmentData == item.departmentData &&
                           x.functionData == item.functionData && x.projectData == item.projectData && x.freeDim1 == item.freeDim1 && x.freeDim2 == item.freeDim2 &&
                           x.freeDim3 == item.freeDim3 && x.freeDim4 == item.freeDim4 && x.vatRate == item.vatRate && x.vatRefund == item.vatRefund && x.changeId == item.changeId && x.year == ConstantKeys._costEstimateYear) != null)
                        {
                            costEstimate = summaryData.Where(x => x.accountData == item.accountData && x.departmentData == item.departmentData &&
                                        x.functionData == item.functionData && x.projectData == item.projectData && x.freeDim1 == item.freeDim1 && x.freeDim2 == item.freeDim2 &&
                                        x.freeDim3 == item.freeDim3 && x.freeDim4 == item.freeDim4 && x.vatRate == item.vatRate && x.vatRefund == item.vatRefund && x.changeId == item.changeId && x.year == ConstantKeys._costEstimateYear).Sum(x => x.amount);
                        }
                    }
                    dynamicObject.Add("total", total);
                    dynamicObject.Add("costEstimate", costEstimate);
                    dynamicObject.Add("approvedCost", approvedCost);
                    gridData.Add(JToken.FromObject(dynamicObject));
                }
                idCounter++;
            }
            //summary line
            decimal summarytotal = 0;
            var resultObject = await FormatTotalRowAsync(userId);
            for (int i = 0; i <= yearDiff; i++)
            {
                decimal yearAmount = 0;

                if (summaryData.FirstOrDefault(x => /*x.changeBudgetYear == input.budgetYear &&*/ x.year == input.startYear + i) != null)
                {
                    yearAmount = summaryData.Where(x => /*x.changeBudgetYear == input.budgetYear &&*/ x.year == input.startYear + i).Sum(y => y.amount);
                }
                summarytotal += yearAmount;
                resultObject.Add("year" + (i + 1).ToString(), yearAmount);
            }
            resultObject.Add("total", summarytotal);
            resultObject.Add("costEstimate", totalCostEstimate);
            resultObject.Add("approvedCost", totalApprovedCost);
            gridData.Add(JToken.FromObject(resultObject));
            return gridData;
        }


        private async Task<JObject> GetAllDataAsync(string userId, IEnumerable<InvestmentSummaryGrid> summaryData, SummaryGridInput input)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            int clientId = userDetails.client_id;
            TimeSpan cacheTimeOut = new TimeSpan(24, 0, 0);
            string InvAccountCache = await _cache.GetStringForUserAsync(clientId, userDetails.tenant_id, userId, "InvAcccount_Summary");
            IEnumerable<InvestmentAccountList> invAllAccount = null;
            //Account
            if (InvAccountCache == null)
            {
                invAllAccount = await _invProj.GetAccountDataAsync(userId);
            }
            else
            {
                invAllAccount = JsonConvert.DeserializeObject<List<InvestmentAccountList>>(InvAccountCache);
            }

            //department
            string saDepartment = null;
            string InvDepartmentCache = await _cache.GetStringForUserAsync(clientId, userDetails.tenant_id, userId + saDepartment, "InvDepartment_Summary");
            IEnumerable<InvestmentDepartmentList> departmentList;
            if (InvDepartmentCache == null)
            {
                var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(input.budgetYear, 1));
                List<clsOrgIdAndDepartments> lst = (await _utility.GetTenantDepartmentsForSelectedOrgIdAsync(orgVersionContent, userId, saDepartment, "investments_getalldepartment")).ToList();
                departmentList = (from l in lst
                                  select new InvestmentDepartmentList
                                  {
                                      fk_department_code = l.departmentValue,
                                      department_name = l.departmentText
                                  }).ToList();
                InvDepartmentCache = JsonConvert.SerializeObject(departmentList);

                await _cache.SetStringForUserAsync(clientId,
                    userDetails.tenant_id, userId + saDepartment, "InvDepartment_Summary", InvDepartmentCache, cacheTimeOut);
            }
            else
            {
                departmentList = JsonConvert.DeserializeObject<List<InvestmentDepartmentList>>(InvDepartmentCache);
            }
            //function
            string InvFunctionCache = await _cache.GetStringForUserAsync(clientId, userDetails.tenant_id, userId, "InvFunction_Summary");
            IEnumerable<InvestmentFunctionList> FunctionCodeList;
            if (InvFunctionCache == null)
            {
                IEnumerable<dynamic> functionCodes = _utility.GetAllServiceAreaFunctions(userId);
                FunctionCodeList = (from fn in functionCodes
                                    select new InvestmentFunctionList
                                    {
                                        fk_function_code = fn.function_code,
                                        function_name = fn.function_name
                                    }).ToList();
                InvFunctionCache = JsonConvert.SerializeObject(FunctionCodeList);
                await _cache.SetStringForUserAsync(clientId, userDetails.tenant_id, userId, "InvFunction_Summary", InvFunctionCache, cacheTimeOut);
            }
            else
            {
                FunctionCodeList = JsonConvert.DeserializeObject<List<InvestmentFunctionList>>(InvFunctionCache);
            }

            //projects
            string cExpProjData = await _cache.GetStringForUserAsync(clientId, userDetails.tenant_id, userId, "cacheFullProjData_Summary");
            List<ProjectInfo> projectCode;
            if (cExpProjData == null)
            {
                IEnumerable<tco_projects> projectCodes = await _invProj.GetAllProjectAsync(userId);
                var projectCodeKeyVal = (from p in projectCodes
                                         select new KeyValuePair
                                         {
                                             key = p.pk_project_code,
                                             value = p.pk_project_code + "-" + p.project_name
                                         }).ToList();

                projectCode = new List<ProjectInfo>();
                foreach (var item in projectCodeKeyVal)
                {
                    ProjectInfo saProject = new ProjectInfo();
                    saProject.fk_project_code = item.key;
                    saProject.project_name = item.value;
                    if (!projectCode.Select(x => x.fk_project_code).Contains(item.key))
                    {
                        projectCode.Add(saProject);
                    }
                }
                cExpProjData = JsonConvert.SerializeObject(projectCode);

                await _cache.SetStringForUserAsync(clientId, userDetails.tenant_id, userId, "cacheFullProjData_Summary", cExpProjData, cacheTimeOut);
            }
            else
            {
                projectCode = JsonConvert.DeserializeObject<List<ProjectInfo>>(cExpProjData);
            }

            List<freedimDefinition> lstFreeDimColumns = await _utility.GetFreeDimDefinitionsBasedOnPageIdAsync(userId, "investments");
            List<string> tenantFreeDimColumns = lstFreeDimColumns.Select(x => x.freeDimColumn).Distinct().ToList();
            List<tco_free_dim_values> dictFreeDimValues = (await _utility.GetFreeDimValuesAsync(userId)).ToList();
            List<InvestmentFreeDimList> FreedimCodeList = new List<InvestmentFreeDimList>();
            foreach (var item in dictFreeDimValues)
            {
                if (tenantFreeDimColumns.Contains(item.free_dim_column))
                {
                    InvestmentFreeDimList saFreedim = new InvestmentFreeDimList();
                    saFreedim.fk_freedim_code = item.free_dim_code;
                    saFreedim.freedim_name = item.free_dim_code + "-" + item.description;
                    FreedimCodeList.Add(saFreedim);
                }
            }
            List<InvestmentProgramCodeHelper> lstinvProgram = _pconseq.GetAllInvestmentPrograms(userId);
            List<InvestmentProgramCodeList> invProgramList = new List<InvestmentProgramCodeList>();
            foreach (var item in lstinvProgram)
            {
                InvestmentProgramCodeList invProgramHelper = new InvestmentProgramCodeList
                {
                    programCode = item.key,
                    programName = item.value,
                };
                invProgramList.Add(invProgramHelper);
            }
            dynamic lstAlterCode = await _pconseq.GetAlterCodesAsync(userId, -1);
            List<AlterCodeHelper> alterCodeList = new List<AlterCodeHelper>();
            foreach (var item in lstAlterCode)
            {
                AlterCodeHelper alterCodeHelper = new AlterCodeHelper
                {
                    key = item.key,
                    value = item.value,
                    isDefault = item.isDefault
                };
                alterCodeList.Add(alterCodeHelper);
            }

            List<int> changeIds = summaryData.Select(y => y.changeId).ToList();
            List<tfp_budget_changes> budgetChangeData = (await _invProj.GetAllBudgetChangeAsync(userId, input.budgetYear)).Where(x => changeIds.Contains(x.pk_change_id)).ToList();
            dynamic resultSet = new JObject();
            resultSet.Add("AccountData", JArray.FromObject(invAllAccount));
            resultSet.Add("DepartmentData", JArray.FromObject(departmentList));
            resultSet.Add("FunctitonData", JArray.FromObject(FunctionCodeList));
            resultSet.Add("ProjectData", JArray.FromObject(projectCode));
            resultSet.Add("FreedimsData", JArray.FromObject(FreedimCodeList));
            resultSet.Add("AlterecodeData", JArray.FromObject(alterCodeList));
            resultSet.Add("ProgramData", JArray.FromObject(invProgramList));
            resultSet.Add("budgetChangeData", JArray.FromObject(budgetChangeData));
            return resultSet;
        }

        private async Task<IDictionary<string, object>> GetParentDataAsync(string userId, string desc, InvSummaryTabType summaryTabType, int parentId, string optionalHeading)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringFormat = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "NumberFormats");
            string format = ((langStringFormat.FirstOrDefault(v => v.Key == "amount")).Value).LangText;
            Dictionary<string, clsLanguageString> invLanguageStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "invdetails");
            var dynamicObject = new ExpandoObject() as IDictionary<string, Object>;
            dynamic KeyValue;
            if (summaryTabType == InvSummaryTabType.summary)
            {
                KeyValue = new JObject();
                KeyValue.Key = desc;
                KeyValue.Value = desc;
                dynamicObject.Add("accountData", KeyValue);
            }
            else
            {
                KeyValue = new JObject();
                KeyValue.Key = "";
                KeyValue.Value = "";
                dynamicObject.Add("accountData", KeyValue);
            }
            KeyValue = new JObject();
            KeyValue.Key = "";
            KeyValue.Value = "";
            dynamicObject.Add("departmentData", KeyValue);

            KeyValue = new JObject();
            KeyValue.Key = "";
            KeyValue.Value = "";
            dynamicObject.Add("functionData", KeyValue);

            if (summaryTabType == InvSummaryTabType.project)
            {
                KeyValue = new JObject();
                if (optionalHeading == string.Empty)
                {
                    KeyValue.Key = invLanguageStrings.FirstOrDefault(v => v.Key == "inv_empty_project").Value.LangText;
                    KeyValue.Value = invLanguageStrings.FirstOrDefault(v => v.Key == "inv_empty_project").Value.LangText;
                }
                else
                {
                    KeyValue.Key = optionalHeading;
                    //KeyValue.Value = projectCode.FirstOrDefault(x => x.fk_project_code == optionalHeading).project_name;
                    KeyValue.Value = desc;
                }
                dynamicObject.Add("projectData", KeyValue);
            }
            else
            {
                KeyValue = new JObject();
                KeyValue.Key = "";
                KeyValue.Value = "";
                dynamicObject.Add("projectData", KeyValue);
            }

            KeyValue = new JObject();
            KeyValue.Key = "";
            KeyValue.Value = "";
            dynamicObject.Add("programCode", KeyValue);

            KeyValue = new JObject();
            KeyValue.Key = "";
            KeyValue.Value = "";
            dynamicObject.Add("freedim1Data", KeyValue);

            KeyValue = new JObject();
            KeyValue.Key = "";
            KeyValue.Value = "";
            dynamicObject.Add("freedim2Data", KeyValue);

            KeyValue = new JObject();
            KeyValue.Key = "";
            KeyValue.Value = "";
            dynamicObject.Add("freedim3Data", KeyValue);

            KeyValue = new JObject();
            KeyValue.Key = "";
            KeyValue.Value = "";
            dynamicObject.Add("freedim4Data", KeyValue);

            if (summaryTabType == InvSummaryTabType.alterCode)
            {
                KeyValue = new JObject();
                KeyValue.Key = optionalHeading;
                //KeyValue.Value = string.IsNullOrEmpty(optionalHeading) ? invLanguageStrings.FirstOrDefault(v => v.Key == "inv_empty_altercode").Value.LangText : alterCodeList.FirstOrDefault(x => x.key == optionalHeading).value;
                KeyValue.Value = desc;
                dynamicObject.Add("alterCode", KeyValue);

                KeyValue = new JObject();
                KeyValue.Key = "";
                KeyValue.Value = "";
                dynamicObject.Add("changeCode", KeyValue);
            }
            else if (summaryTabType == InvSummaryTabType.budgetRound)
            {
                KeyValue = new JObject();
                KeyValue.Key = "";
                KeyValue.Value = "";
                dynamicObject.Add("alterCode", KeyValue);
            }
            else
            {
                KeyValue = new JObject();
                KeyValue.Key = "";
                KeyValue.Value = "";
                dynamicObject.Add("alterCode", KeyValue);

                KeyValue = new JObject();
                KeyValue.Key = "";
                KeyValue.Value = "";
                dynamicObject.Add("changeCode", KeyValue);
            }

            dynamicObject.Add("id", parentId);
            dynamicObject.Add("parentId", null);
            dynamicObject.Add("level1Code", desc);
            dynamicObject.Add("vatRefund", 0);
            dynamicObject.Add("vatRate", 0);
            dynamicObject.Add("isBold", true);
            dynamicObject.Add("isItalic", true);
            dynamicObject.Add("numberFormat", format);
            dynamicObject.Add("accountType", "");
            return dynamicObject;
        }

        private async Task<IDictionary<string, object>> FormatChildDataAsync(string userId, SummaryGridInput input, int parentId,
                                    int idCounter, InvestmentSummaryGrid item, string level1Data, JObject data, InvSummaryTabType tabType)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringFormat = await _utility
                .GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "NumberFormats")
                ;
            string format = ((langStringFormat.FirstOrDefault(v => v.Key == "amount")).Value).LangText;
            IEnumerable<InvestmentAccountList> invAllAccount = data.SelectToken("AccountData").ToObject<IEnumerable<InvestmentAccountList>>();
            IEnumerable<InvestmentDepartmentList> departmentList = data.SelectToken("DepartmentData").ToObject<IEnumerable<InvestmentDepartmentList>>();
            IEnumerable<InvestmentFunctionList> FunctionCodeList = data.SelectToken("FunctitonData").ToObject<IEnumerable<InvestmentFunctionList>>();
            List<ProjectInfo> projectCode = data.SelectToken("ProjectData").ToObject<List<ProjectInfo>>();
            List<InvestmentFreeDimList> FreedimCodeList = data.SelectToken("FreedimsData").ToObject<List<InvestmentFreeDimList>>();
            List<InvestmentProgramCodeList> invProgramList = data.SelectToken("ProgramData").ToObject<List<InvestmentProgramCodeList>>();
            List<AlterCodeHelper> alterCodeList = data.SelectToken("AlterecodeData").ToObject<List<AlterCodeHelper>>();
            List<tfp_budget_changes> budgetChangeData = new List<tfp_budget_changes>();
            if (input.summaryTabType == InvSummaryTabType.alterCode || input.summaryTabType == InvSummaryTabType.budgetRound)
            {
                budgetChangeData = data.SelectToken("budgetChangeData").ToObject<List<tfp_budget_changes>>();
            }
            var dynamicObject = new ExpandoObject() as IDictionary<string, Object>;
            dynamic KeyValue = new JObject();
            var account = invAllAccount.FirstOrDefault(x => x.fk_account_code == item.accountData);
            KeyValue.Key = account != null ? account.fk_account_code : string.Empty;
            KeyValue.Value = account != null ? account.fk_account_code + "-" + account.display_name : string.Empty;
            dynamicObject.Add("accountData", KeyValue);

            KeyValue = new JObject();
            var depatment = departmentList.FirstOrDefault(x => x.fk_department_code == item.departmentData);
            KeyValue.Key = depatment != null ? depatment.fk_department_code : string.Empty;
            KeyValue.Value = depatment != null ? depatment.fk_department_code + "-" + depatment.department_name : string.Empty;
            dynamicObject.Add("departmentData", KeyValue);

            KeyValue = new JObject();
            var function = FunctionCodeList.FirstOrDefault(x => x.fk_function_code == item.functionData);
            KeyValue.Key = function != null ? function.fk_function_code : string.Empty;
            KeyValue.Value = function != null ? function.fk_function_code + "-" + function.function_name : string.Empty;
            dynamicObject.Add("functionData", KeyValue);

            KeyValue = new JObject();
            var project = projectCode.FirstOrDefault(x => x.fk_project_code == item.projectData);
            if (tabType == InvSummaryTabType.project)
            {
                KeyValue.Key = "";
                KeyValue.Value = "";
            }
            else
            {
                KeyValue.Key = project != null ? project.fk_project_code : string.Empty;
                KeyValue.Value = project != null ? project.fk_project_code + "-" + project.project_name : string.Empty;
            }
            dynamicObject.Add("projectData", KeyValue);

            KeyValue = new JObject();
            var program = invProgramList.FirstOrDefault(x => x.programCode == item.programCode);
            KeyValue.Key = program != null ? program.programCode : string.Empty;
            KeyValue.Value = program != null ? program.programName : string.Empty;
            dynamicObject.Add("programCode", KeyValue);

            KeyValue = new JObject();
            var freedims = FreedimCodeList.FirstOrDefault(x => x.fk_freedim_code == item.freeDim1);
            KeyValue.Key = freedims != null ? freedims.fk_freedim_code : string.Empty;
            KeyValue.Value = freedims != null ? freedims.fk_freedim_code + "-" + freedims.freedim_name : string.Empty;
            dynamicObject.Add("freedim1Data", KeyValue);

            KeyValue = new JObject();
            freedims = FreedimCodeList.FirstOrDefault(x => x.fk_freedim_code == item.freeDim2);
            KeyValue.Key = freedims != null ? freedims.fk_freedim_code : string.Empty;
            KeyValue.Value = freedims != null ? freedims.fk_freedim_code + "-" + freedims.freedim_name : string.Empty;
            dynamicObject.Add("freedim2Data", KeyValue);

            KeyValue = new JObject();
            freedims = FreedimCodeList.FirstOrDefault(x => x.fk_freedim_code == item.freeDim3);
            KeyValue.Key = freedims != null ? freedims.fk_freedim_code : string.Empty;
            KeyValue.Value = freedims != null ? freedims.fk_freedim_code + "-" + freedims.freedim_name : string.Empty;
            dynamicObject.Add("freedim3Data", KeyValue);

            KeyValue = new JObject();
            freedims = FreedimCodeList.FirstOrDefault(x => x.fk_freedim_code == item.freeDim4);
            KeyValue.Key = freedims != null ? freedims.fk_freedim_code : string.Empty;
            KeyValue.Value = freedims != null ? freedims.fk_freedim_code + "-" + freedims.freedim_name : string.Empty;
            dynamicObject.Add("freedim4Data", KeyValue);

            KeyValue = new JObject();
            var alterCode = alterCodeList.FirstOrDefault(x => x.key == item.alterCode);
            if (tabType == InvSummaryTabType.alterCode)
            {
                KeyValue.Key = "";
                KeyValue.Value = "";
            }
            else
            {
                KeyValue.Key = alterCode != null ? alterCode.key : string.Empty;
                KeyValue.Value = alterCode != null ? alterCode.key + "-" + alterCode.value : string.Empty;
            }
            dynamicObject.Add("alterCode", KeyValue);

            KeyValue = new JObject();
            var changeCode = budgetChangeData.FirstOrDefault(x => x.pk_change_id == item.changeId);
            if (tabType == InvSummaryTabType.budgetRound)
            {
                KeyValue.Key = "";
                KeyValue.Value = "";
            }
            else
            {
                KeyValue.Key = changeCode != null ? changeCode.pk_change_id.ToString() : string.Empty;
                KeyValue.Value = changeCode != null ? changeCode.approval_reference + "-" + changeCode.budget_name : string.Empty;
            }
            dynamicObject.Add("changeCode", KeyValue);
            dynamicObject.Add("accountType", item.accountType);
            dynamicObject.Add("id", idCounter);
            dynamicObject.Add("parentId", parentId);
            dynamicObject.Add("isBold", false);
            dynamicObject.Add("isItalic", false);
            dynamicObject.Add("level1Code", level1Data);
            dynamicObject.Add("vatRefund", item.vatRefund);
            dynamicObject.Add("vatRate", item.vatRate);
            dynamicObject.Add("numberFormat", format);
            return dynamicObject;
        }

        private async Task<IDictionary<string, object>> FormatTotalRowAsync(string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringFormat = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "NumberFormats");
            string format = ((langStringFormat.FirstOrDefault(v => v.Key == "amount")).Value).LangText;
            var resultObject = new ExpandoObject() as IDictionary<string, Object>;
            dynamic KeyValue;
            KeyValue = new JObject();
            KeyValue.Key = "";
            KeyValue.Value = "";
            resultObject.Add("accountData", KeyValue);

            KeyValue = new JObject();
            KeyValue.Key = "";
            KeyValue.Value = "";
            resultObject.Add("departmentData", KeyValue);

            KeyValue = new JObject();
            KeyValue.Key = "";
            KeyValue.Value = "";
            resultObject.Add("functionData", KeyValue);

            KeyValue = new JObject();
            KeyValue.Key = "";
            KeyValue.Value = "";
            resultObject.Add("projectData", KeyValue);

            KeyValue = new JObject();
            KeyValue.Key = "";
            KeyValue.Value = "";
            resultObject.Add("programCode", KeyValue);

            KeyValue = new JObject();
            KeyValue.Key = "";
            KeyValue.Value = "";
            resultObject.Add("freedim1Data", KeyValue);

            KeyValue = new JObject();
            KeyValue.Key = "";
            KeyValue.Value = "";
            resultObject.Add("freedim2Data", KeyValue);

            KeyValue = new JObject();
            KeyValue.Key = "";
            KeyValue.Value = "";
            resultObject.Add("freedim3Data", KeyValue);

            KeyValue = new JObject();
            KeyValue.Key = "";
            KeyValue.Value = "";
            resultObject.Add("alterCode", KeyValue);

            KeyValue = new JObject();
            KeyValue.Key = "";
            KeyValue.Value = "";
            resultObject.Add("changeCode", KeyValue);

            KeyValue = new JObject();
            KeyValue.Key = "";
            KeyValue.Value = "";
            resultObject.Add("freedim4Data", KeyValue);
            resultObject.Add("id", "-1");
            resultObject.Add("parentId", null);
            resultObject.Add("level1Code", "");
            resultObject.Add("vatRefund", 0);
            resultObject.Add("vatRate", 0);
            resultObject.Add("isBold", true);
            resultObject.Add("isItalic", true);
            resultObject.Add("numberFormat", format);
            resultObject.Add("accountType", "");
            return resultObject;
        }

        private List<ColumnSelectorColumn> GetSummaryColSelTranslations(Dictionary<string, clsLanguageString> langStrings, List<ColumnSelectorColumn> columnsConfig)
        {
            foreach(var col in columnsConfig)
            {
                col.value = col.key switch
                {
                    "costEstimate" => GetLangText(langStrings, "hdr_cost_estimate"),
                    "approvalCost" => GetLangText(langStrings, "hdr_approved_cost"),
                    "vatRate" => GetLangText(langStrings, "hdr_vat_rate"),
                    "vatRefund" => GetLangText(langStrings, "hdr_vat_refund"),
                    "program" => GetLangText(langStrings, "inv_program_code"),
                    _ => col.value
                };
            }
            return columnsConfig;
        }

        private async Task<List<ColumnSelectorColumn>> GetRegisterColSelTranslations(string userId, Dictionary<string, clsLanguageString> langStrings, List<ColumnSelectorColumn> columnsConfig, bool adjCodeExists)
        {
            var updatedColConfig = new List<ColumnSelectorColumn>();
            int yearDiff = 0;
            foreach (var col in columnsConfig)
            {
                switch (col.key)
                {
                    case "AlterCode":
                        if ((await _pconseq.GetAlterCodesAsync(userId, yearDiff != 0 ? 21 : 1000, true, false, false)).Count > 1)
                        {
                            col.value = GetLangText(langStrings, "hdr_alter_code");
                            updatedColConfig.Add(col);
                        }
                        break;

                    case "AdjustmentCode":
                        if (adjCodeExists)
                        {
                            col.value = GetLangText(langStrings, "hdr_adjustment_code");
                            updatedColConfig.Add(col);
                        }
                        break;
                    case "CostEstimate":
                        col.value = GetLangText(langStrings, "hdr_cost_estimate");
                        updatedColConfig.Add(col);
                        break;
                    case "ApprovedCost":
                        col.value = GetLangText(langStrings, "hdr_approved_cost");
                        updatedColConfig.Add(col);
                        break;
                    case "Description":
                        col.value = GetLangText(langStrings, "hdr_description");
                        updatedColConfig.Add(col);
                        break;
                    default:
                        break;
                }
            }
            return updatedColConfig.Any() ? updatedColConfig : columnsConfig;
        }

        private async Task<JArray> FormatDetailGridDataAsync(IEnumerable<DetailData> detialTrans, InvRegistrationInput detailGridInput, string userId)
        {
            var userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Investment");

            dynamic gridFormattedData = new JArray();

            int yearDiff = detailGridInput.yearDiff != 0 && detailGridInput.invStatus != 2 ? (detailGridInput.yearDiff + 1) : (detailGridInput.estimatedFinish - detailGridInput.startYear);
            decimal approvedCost = 0;
            decimal costEstimate = 0;

            //Get Accounts
            IEnumerable<InvestmentAccount> accounts;
            if (detailGridInput.yearDiff != 0)
            {
                string accountType = "operations";
                string report = "drift";
                accounts = _investments.GetAccountForOperationalEffect(userId, accountType, report, new Collection<int>(), detailGridInput.budgetYear);
            }
            else
            {
                Collection<int> fromLineItem = new Collection<int> { 220 };
                Collection<int> toLineItem = new Collection<int> { 252 };
                string accountType = "investment";
                string report = "3inv";
                accounts = _investments.GetAccount(userId, accountType, report, fromLineItem, toLineItem, "", detailGridInput.budgetYear);
            }
            detialTrans = detialTrans.Where(y => accounts.Select(x => x.fk_account_code).ToList().Contains(y.fk_account_code)).ToList();

            var grpData = detialTrans.Where(dt => accounts.Any(a => a.fk_account_code == dt.fk_account_code))
                                    .GroupBy(dt => new
                                    {
                                        dt.changeId,
                                        dt.changeName,
                                        dt.account_name,
                                        dt.fk_account_code,
                                        dt.fk_department_code,
                                        dt.department_name,
                                        dt.fk_function_code,
                                        dt.function_name,
                                        dt.fk_project_code,
                                        dt.project_name,
                                        dt.free_dim1_code,
                                        dt.free_dim1_name,
                                        dt.free_dim2_code,
                                        dt.free_dim2_name,
                                        dt.free_dim3_code,
                                        dt.free_dim3_name,
                                        dt.free_dim4_code,
                                        dt.free_dim4_name,
                                        dt.fk_adj_code,
                                        dt.adj_name,
                                        dt.fk_user_adj_code,
                                        dt.user_adj_name,
                                        dt.fk_alter_code,
                                        dt.alter_name,
                                        dt.vatRate,
                                        dt.vatRefund,
                                        dt.description,
                                        dt.budgetChangeYear
                                    })
                                    .Select(grp => new
                                    {
                                        grp.Key.changeId,
                                        grp.Key.changeName,
                                        grp.Key.fk_account_code,
                                        grp.Key.account_name,
                                        grp.Key.fk_department_code,
                                        grp.Key.department_name,
                                        grp.Key.fk_function_code,
                                        grp.Key.function_name,
                                        grp.Key.fk_project_code,
                                        grp.Key.project_name,
                                        grp.Key.free_dim1_code,
                                        grp.Key.free_dim1_name,
                                        grp.Key.free_dim2_code,
                                        grp.Key.free_dim2_name,
                                        grp.Key.free_dim3_code,
                                        grp.Key.free_dim3_name,
                                        grp.Key.free_dim4_code,
                                        grp.Key.free_dim4_name,
                                        grp.Key.fk_user_adj_code,
                                        grp.Key.user_adj_name,
                                        grp.Key.fk_adj_code,
                                        grp.Key.adj_name,
                                        grp.Key.fk_alter_code,
                                        grp.Key.alter_name,
                                        grp.Key.vatRate,
                                        grp.Key.vatRefund,
                                        grp.Key.description,
                                        grp.Key.budgetChangeYear
                                    }).OrderByDescending(x => x.changeId != detailGridInput.changeId)
                                    .ThenBy(x => x.changeId == detailGridInput.changeId).ToList();

            int idCounter = 1;
            #region previous years budget change sum
            JObject previousYearData = new JObject();
            decimal sumOfYears = 0;
            for (int i = 0; i <= yearDiff; i++)
            {
                decimal yearAmount = 0;
                var prevFilteredTrans = detialTrans.Where(x => x.budgetChangeYear < detailGridInput.budgetYear && x.year == (detailGridInput.startYear + i)).ToList();
                if (prevFilteredTrans.Any())
                {
                    yearAmount = prevFilteredTrans.Sum(y => y.amount);
                }
                previousYearData.Add("Year" + (i + 1).ToString(), yearAmount);
                sumOfYears += yearAmount;
            }
            var prevApprovedCostTransactions = detialTrans.Where(x => x.budgetChangeYear < detailGridInput.budgetYear && x.year == -1).ToList();
            if (prevApprovedCostTransactions.Any())
            {
                approvedCost = prevApprovedCostTransactions.Sum(y => y.amount);
            }
            var prevCostEstimateTransactions = detialTrans.Where(x => x.budgetChangeYear < detailGridInput.budgetYear && x.year == ConstantKeys._costEstimateYear).ToList();
            if (prevCostEstimateTransactions.Any())
            {
                costEstimate = prevCostEstimateTransactions.Sum(y => y.amount);
            }

            string previousYearCaption = ((langString.FirstOrDefault(v => v.Key == "inv_det_prev_sum")).Value.LangText) ?? string.Empty;
            previousYearData = FormatInvRegistrationGrid(null, previousYearCaption, previousYearData, costEstimate, approvedCost, sumOfYears, 0);
            previousYearData.Add("Semi", true);
            previousYearData.Add("Id", -1);
            previousYearData.Add("IsEditable", false);

            gridFormattedData.Add(JToken.FromObject(previousYearData));
            #endregion previous years budget change sum

            #region current year closed budget change

            foreach (var item in grpData.Where(x => x.changeId != detailGridInput.changeId && x.budgetChangeYear == detailGridInput.budgetYear))
            {
                JObject changeClosedData = new JObject();
                sumOfYears = 0;
                for (int i = 0; i <= yearDiff; i++)
                {
                    decimal yearAmount = 0;
                    approvedCost = 0;
                    costEstimate = 0;
                    var matchingTransactions = detialTrans.Where(x => x.changeId == item.changeId &&x.fk_account_code == item.fk_account_code &&x.fk_department_code == item.fk_department_code &&
                                                                x.fk_function_code == item.fk_function_code &&x.fk_project_code == item.fk_project_code &&x.free_dim1_code == item.free_dim1_code &&
                                                                x.free_dim2_code == item.free_dim2_code && x.free_dim3_code == item.free_dim3_code &&x.free_dim4_code == item.free_dim4_code &&
                                                                x.fk_alter_code == item.fk_alter_code &&x.fk_adj_code == item.fk_adj_code &&x.fk_user_adj_code == item.fk_user_adj_code &&x.vatRate == item.vatRate &&
                                                                x.vatRefund == item.vatRefund &&x.description == item.description &&x.year == (detailGridInput.startYear + i) &&x.budgetChangeYear == detailGridInput.budgetYear).ToList();

                    if (matchingTransactions.Any())
                    {
                        yearAmount = matchingTransactions.Sum(x => x.amount);
                    }
                    sumOfYears += yearAmount;
                    
                    changeClosedData.Add("Year" + (i + 1).ToString(), yearAmount);
                }
                var currentFilteredTransactions = detialTrans.Where(x => x.changeId == item.changeId &&x.fk_account_code == item.fk_account_code &&x.fk_department_code == item.fk_department_code &&x.fk_function_code == item.fk_function_code &&
                                                        x.fk_project_code == item.fk_project_code &&x.free_dim1_code == item.free_dim1_code &&x.free_dim2_code == item.free_dim2_code &&x.free_dim3_code == item.free_dim3_code &&
                                                        x.free_dim4_code == item.free_dim4_code &&x.fk_alter_code == item.fk_alter_code &&x.fk_adj_code == item.fk_adj_code &&x.fk_user_adj_code == item.fk_user_adj_code &&
                                                        x.vatRate == item.vatRate &&x.vatRefund == item.vatRefund &&x.description == item.description &&x.budgetChangeYear == detailGridInput.budgetYear).ToList();

                // Calculate approvedCost
                var currentApprovedCostTransactions = currentFilteredTransactions.Where(x => x.year == ConstantKeys._approvedCostYear).ToList();
                if (currentApprovedCostTransactions.Any())
                {
                    approvedCost = currentApprovedCostTransactions.Sum(x => x.amount);
                }
                // Calculate costEstimate
                var currentCostEstimateTransactions = currentFilteredTransactions.Where(x => x.year == ConstantKeys._costEstimateYear).ToList();
                if (currentCostEstimateTransactions.Any())
                {
                    costEstimate = currentCostEstimateTransactions.Sum(x => x.amount);
                }
                changeClosedData = FormatInvRegistrationGrid(item, "", changeClosedData, costEstimate, approvedCost, sumOfYears, idCounter);
                changeClosedData.Add("Semi", false);
                changeClosedData.Add("Id", idCounter);
                changeClosedData.Add("IsEditable", false);
                idCounter++;
                gridFormattedData.Add(JToken.FromObject(changeClosedData));
            }
            #endregion current year closed budget change

            #region Sum of closed budget changes
            JObject closedChangesSum = new JObject();
            sumOfYears = 0;
            approvedCost = 0;
            costEstimate = 0;
            for (int i = 0; i <= yearDiff; i++)
            {
                decimal yearAmount = 0;

                var relevantTransactions = detialTrans.Where(x => x.changeId != detailGridInput.changeId &&x.budgetChangeYear == detailGridInput.budgetYear &&x.year == (detailGridInput.startYear + i)).ToList();
                if (relevantTransactions.Any())
                {
                    yearAmount = relevantTransactions.Sum(y => y.amount);
                }
                closedChangesSum.Add("Year" + (i + 1).ToString(), yearAmount);
                sumOfYears += yearAmount;
            }
            var closedBudgetTrans = detialTrans.Where(x => x.changeId != detailGridInput.changeId &&x.budgetChangeYear == detailGridInput.budgetYear).ToList();
            var approvedCostTransactions = closedBudgetTrans.Where(x => x.year == ConstantKeys._approvedCostYear).ToList();
            if (approvedCostTransactions.Any())
            {
                approvedCost = approvedCostTransactions.Sum(y => y.amount);
            }
            var costEstimateTransactions = closedBudgetTrans.Where(x => x.year == ConstantKeys._costEstimateYear).ToList();
            if (costEstimateTransactions.Any())
            {
                costEstimate = costEstimateTransactions.Sum(y => y.amount);
            }
            string closedChangesCaption = ((langString.FirstOrDefault(v => v.Key == "inv_det_closed_sum")).Value.LangText) ?? string.Empty;
            closedChangesSum = FormatInvRegistrationGrid(null, closedChangesCaption, closedChangesSum, costEstimate, approvedCost, sumOfYears, 0);
            closedChangesSum.Add("Semi", true);
            closedChangesSum.Add("Id", -2);
            closedChangesSum.Add("IsEditable", false);
            gridFormattedData.Add(JToken.FromObject(closedChangesSum));

            #endregion Sum of closed budget changes

            #region Active budget change data
            //group data with transaction id
            decimal totalApprovedCost = 0;
            decimal totalCostEstimate = 0;
            var grpActiveData = (from dt in detialTrans
                                where accounts.Select(x => x.fk_account_code).ToList().Contains(dt.fk_account_code)
                                orderby dt.updated_date ascending
                                group dt by new
                                {
                                    dt.transId,
                                    dt.changeId,
                                    dt.changeName,
                                    dt.account_name,
                                    dt.fk_account_code,
                                    dt.fk_department_code,
                                    dt.department_name,
                                    dt.fk_function_code,
                                    dt.function_name,
                                    dt.fk_project_code,
                                    dt.project_name,
                                    dt.free_dim1_code,
                                    dt.free_dim1_name,
                                    dt.free_dim2_code,
                                    dt.free_dim2_name,
                                    dt.free_dim3_code,
                                    dt.free_dim3_name,
                                    dt.free_dim4_code,
                                    dt.free_dim4_name,
                                    dt.fk_adj_code,
                                    dt.adj_name,
                                    dt.fk_alter_code,
                                    dt.user_adj_name,
                                    dt.fk_user_adj_code,
                                    dt.alter_name,
                                    dt.vatRate,
                                    dt.vatRefund,
                                    dt.description,
                                    dt.budgetChangeYear
                                } into grp
                                select new
                                {
                                    grp.Key.transId,
                                    grp.Key.changeId,
                                    grp.Key.changeName,
                                    grp.Key.fk_account_code,
                                    grp.Key.account_name,
                                    grp.Key.fk_department_code,
                                    grp.Key.department_name,
                                    grp.Key.fk_function_code,
                                    grp.Key.function_name,
                                    grp.Key.fk_project_code,
                                    grp.Key.project_name,
                                    grp.Key.free_dim1_code,
                                    grp.Key.free_dim1_name,
                                    grp.Key.free_dim2_code,
                                    grp.Key.free_dim2_name,
                                    grp.Key.free_dim3_code,
                                    grp.Key.free_dim3_name,
                                    grp.Key.free_dim4_code,
                                    grp.Key.free_dim4_name,
                                    grp.Key.fk_adj_code,
                                    grp.Key.adj_name,
                                    grp.Key.fk_alter_code,
                                    grp.Key.user_adj_name,
                                    grp.Key.fk_user_adj_code,
                                    grp.Key.alter_name,
                                    grp.Key.vatRate,
                                    grp.Key.vatRefund,
                                    grp.Key.description,
                                    grp.Key.budgetChangeYear
                                }).ToList();

            foreach (var item in grpActiveData.Where(x => x.changeId == detailGridInput.changeId && x.budgetChangeYear == detailGridInput.budgetYear))
            {
                JObject dynamicObjectData = new JObject();

                sumOfYears = 0;
                for (int i = 0; i <= yearDiff; i++)
                {
                    decimal yearAmount = 0;

                    var activeBudTransactions = detialTrans.Where(x => x.transId == item.transId &&x.changeId == item.changeId &&x.fk_account_code == item.fk_account_code &&x.fk_department_code == item.fk_department_code &&
                                                            x.fk_function_code == item.fk_function_code &&x.fk_project_code == item.fk_project_code &&x.free_dim1_code == item.free_dim1_code &&x.free_dim2_code == item.free_dim2_code &&
                                                            x.free_dim3_code == item.free_dim3_code &&x.free_dim4_code == item.free_dim4_code &&x.fk_alter_code == item.fk_alter_code &&x.fk_adj_code == item.fk_adj_code &&
                                                            x.vatRate == item.vatRate &&x.vatRefund == item.vatRefund &&x.description == item.description &&x.year == (detailGridInput.startYear + i)).ToList();
                    if (activeBudTransactions.Any())
                    {
                        yearAmount = activeBudTransactions.Sum(x => x.amount);
                    }
                    sumOfYears += yearAmount;
                    dynamicObjectData.Add("Year" + (i + 1).ToString(), yearAmount);
                }
                //Get approved cost row
                approvedCost = 0;
                costEstimate = 0;
                var activeBudFilteredTransactions = detialTrans.Where(x => x.transId == item.transId &&x.changeId == item.changeId &&x.fk_account_code == item.fk_account_code &&x.fk_department_code == item.fk_department_code &&
                                                            x.fk_function_code == item.fk_function_code &&x.fk_project_code == item.fk_project_code &&x.free_dim1_code == item.free_dim1_code &&x.free_dim2_code == item.free_dim2_code &&
                                                            x.free_dim3_code == item.free_dim3_code && x.free_dim4_code == item.free_dim4_code &&x.fk_alter_code == item.fk_alter_code &&x.fk_adj_code == item.fk_adj_code &&
                                                            x.fk_user_adj_code == item.fk_user_adj_code &&x.vatRate == item.vatRate &&x.vatRefund == item.vatRefund &&x.description == item.description).ToList();
                var activeApprovedCostTransactions = activeBudFilteredTransactions.Where(x => x.year == ConstantKeys._approvedCostYear).ToList();
                if (activeApprovedCostTransactions.Any())
                {
                    approvedCost = activeApprovedCostTransactions.Sum(x => x.amount);
                }
                var activeCostEstimateTransactions = activeBudFilteredTransactions.Where(x => x.year == ConstantKeys._costEstimateYear).ToList();
                if (activeCostEstimateTransactions.Any())
                {
                    costEstimate = activeCostEstimateTransactions.Sum(x => x.amount);
                }
                dynamicObjectData = FormatInvRegistrationGrid(item, "", dynamicObjectData, costEstimate, approvedCost, sumOfYears, idCounter);
                dynamicObjectData.Add("Semi", false);
                dynamicObjectData.Add("Id", idCounter);
                dynamicObjectData.Add("TransId", item.transId);
                dynamicObjectData.Add("IsEditable", true);
                idCounter++;
                gridFormattedData.Add(JToken.FromObject(dynamicObjectData));
            }
            #endregion Active budget change data

            #region Active budget change sum
            JObject activeChangesSum = new JObject();
            if (detailGridInput.changeId != 0)
            {
                //get dummy row for total
                sumOfYears = 0;
                for (int i = 0; i <= yearDiff; i++)
                {
                    decimal yearAmount = 0;

                    var activeBudSumTrans = detialTrans.Where(x => x.changeId == detailGridInput.changeId &&x.budgetChangeYear == detailGridInput.budgetYear &&x.year == (detailGridInput.startYear + i)).ToList();
                    if (activeBudSumTrans.Any())
                    {
                        yearAmount = activeBudSumTrans.Sum(y => y.amount);
                    }
                    activeChangesSum.Add("Year" + (i + 1).ToString(), yearAmount);
                    sumOfYears += yearAmount;
                }
               
            }
            string activeChangesCaption = ((langString.FirstOrDefault(v => v.Key == "inv_det_active_sum")).Value.LangText) ?? string.Empty;
            activeChangesSum = FormatInvRegistrationGrid(null, activeChangesCaption, activeChangesSum, totalCostEstimate, totalApprovedCost, sumOfYears, 0);
            activeChangesSum.Add("Semi", true);
            activeChangesSum.Add("Id", -3);
            activeChangesSum.Add("IsEditable", false);
            gridFormattedData.Add(JToken.FromObject(activeChangesSum));
            #endregion Active budget change sum

            #region Over All sum
            sumOfYears = 0;
            approvedCost = 0;
            costEstimate = 0;
            JObject overallTotal = new JObject();
            for (int i = 0; i <= yearDiff; i++)
            {
                decimal yearAmount = 0;

                var totalTrans = detialTrans.Where(x => x.budgetChangeYear <= detailGridInput.budgetYear &&x.year == (detailGridInput.startYear + i)).ToList();
                if (totalTrans.Any())
                {
                    yearAmount = totalTrans.Sum(y => y.amount);
                }
                overallTotal.Add("Year" + (i + 1).ToString(), yearAmount);
                sumOfYears += yearAmount;
            }
            var totalApprovedCostTransactions = detialTrans.Where(x => x.budgetChangeYear <= detailGridInput.budgetYear && x.year == ConstantKeys._approvedCostYear).ToList();
            if (totalApprovedCostTransactions.Any())
            {
                approvedCost = totalApprovedCostTransactions.Sum(y => y.amount);
            }
            var totalCostEstimateTransactions = detialTrans.Where(x => x.budgetChangeYear <= detailGridInput.budgetYear && x.year == ConstantKeys._costEstimateYear).ToList();
            if (totalCostEstimateTransactions.Any())
            {
                costEstimate = totalCostEstimateTransactions.Sum(y => y.amount);
            }
            string overallTotalCaption = ((langString.FirstOrDefault(v => v.Key == "inv_det_overall_sum")).Value.LangText) ?? string.Empty;
            overallTotal = FormatInvRegistrationGrid(null, overallTotalCaption, overallTotal, costEstimate, approvedCost, sumOfYears, 0);
            overallTotal.Add("Semi", true);
            overallTotal.Add("Id", -4);
            overallTotal.Add("IsEditable", false);
            gridFormattedData.Add(JToken.FromObject(overallTotal));

            #endregion Over All sum

            return gridFormattedData;
        }
        private JObject FormatInvRegistrationGrid(dynamic item, string caption, JObject updatedObject, decimal costEstimate, decimal approvedCost, decimal sumOfYears, int idCounter)
        {
            if(item != null)
            {
                updatedObject.Add("AccountCode", item.fk_account_code);
                updatedObject.Add("AccountName", item.fk_account_code + "-" + item.account_name);
                updatedObject.Add("DepartmentCode", item.fk_department_code);
                updatedObject.Add("DepartmentName", item.fk_department_code + "-" + item.department_name);
                updatedObject.Add("FunctionCode", item.fk_function_code);
                updatedObject.Add("FunctionName", item.fk_function_code + "-" + item.function_name);
                updatedObject.Add("ProjectCode", item.fk_project_code);
                updatedObject.Add("ProjectName", item.project_name);
                updatedObject.Add("BudgetRound", item.changeId.ToString());
                updatedObject.Add("BudgetRoundName", item.changeId + "-" + item.changeName);
                updatedObject.Add("Freedim1Code", string.IsNullOrEmpty(item.free_dim1_code) ? string.Empty : item.free_dim1_code);
                updatedObject.Add("Freedim1Name", string.IsNullOrEmpty(item.free_dim1_code) ? string.Empty : item.free_dim1_code + "-" + item.free_dim1_name);
                updatedObject.Add("Freedim2Code", string.IsNullOrEmpty(item.free_dim2_code) ? string.Empty : item.free_dim2_code);
                updatedObject.Add("Freedim2Name", string.IsNullOrEmpty(item.free_dim2_code) ? string.Empty : item.free_dim2_code + "-" + item.free_dim2_name);
                updatedObject.Add("Freedim3Code", string.IsNullOrEmpty(item.free_dim3_code) ? string.Empty : item.free_dim3_code);
                updatedObject.Add("Freedim3Name", string.IsNullOrEmpty(item.free_dim3_code) ? string.Empty : item.free_dim3_code + "-" + item.free_dim3_name);
                updatedObject.Add("Freedim4Code", string.IsNullOrEmpty(item.free_dim4_code) ? string.Empty : item.free_dim4_code);
                updatedObject.Add("Freedim4Name", string.IsNullOrEmpty(item.free_dim4_code) ? string.Empty : item.free_dim4_code + "-" + item.free_dim4_name);
                updatedObject.Add("AdjustmentCode", string.IsNullOrEmpty(Convert.ToString(item.fk_adj_code)) ? string.Empty : Convert.ToString(item.fk_adj_code));
                updatedObject.Add("AdjustmentCodeName", string.IsNullOrEmpty(Convert.ToString(item.adj_name)) ? string.Empty : Convert.ToString(item.fk_adj_code) + "-" + Convert.ToString(item.adj_name));
                updatedObject.Add("AlterCode", string.IsNullOrEmpty(Convert.ToString(item.fk_alter_code)) ? string.Empty : Convert.ToString(item.fk_alter_code));
                updatedObject.Add("AltercodeName", string.IsNullOrEmpty(Convert.ToString(item.alter_name)) ? string.Empty : Convert.ToString(item.fk_alter_code) + "-" + Convert.ToString(item.alter_name));
                updatedObject.Add("CostEstimate", costEstimate);
                updatedObject.Add("ApprovedCost", approvedCost);
                updatedObject.Add("Total", sumOfYears);
                updatedObject.Add("VatRate", item.vatRate);
                updatedObject.Add("VatRefund", item.vatRefund);
                updatedObject.Add("Description", string.IsNullOrEmpty(Convert.ToString(item.description)) ? string.Empty : Convert.ToString(item.description));
            }
            else
            {
                updatedObject.Add("AccountCode", string.Empty);
                updatedObject.Add("AccountName", string.Empty);
                updatedObject.Add("DepartmentCode", string.Empty);
                updatedObject.Add("DepartmentName", string.Empty);
                updatedObject.Add("FunctionCode", string.Empty);
                updatedObject.Add("FunctionName", string.Empty);
                updatedObject.Add("ProjectCode", string.Empty);
                updatedObject.Add("ProjectName", string.Empty);
                updatedObject.Add("BudgetRound", caption);
                updatedObject.Add("BudgetRoundName", caption);
                updatedObject.Add("Freedim1Code", string.Empty);
                updatedObject.Add("Freedim1Name", string.Empty);
                updatedObject.Add("Freedim2Code", string.Empty);
                updatedObject.Add("Freedim2Name", string.Empty);
                updatedObject.Add("Freedim3Code", string.Empty);
                updatedObject.Add("Freedim3Name", string.Empty);
                updatedObject.Add("Freedim4Code", string.Empty);
                updatedObject.Add("Freedim4Name", string.Empty);
                updatedObject.Add("AdjustmentCode", string.Empty);
                updatedObject.Add("AdjustmentCodeName", string.Empty);
                updatedObject.Add("AlterCode", string.Empty);
                updatedObject.Add("AltercodeName", string.Empty);
                updatedObject.Add("CostEstimate", costEstimate);
                updatedObject.Add("ApprovedCost", approvedCost);
                updatedObject.Add("Total", sumOfYears);
                updatedObject.Add("VatRate", 0);
                updatedObject.Add("VatRefund", 0);
                updatedObject.Add("Description", string.Empty);
            }
            return updatedObject;
        }
        private async Task<List<KeyValuePairString>> GetInvRegAccountsList(string userId,int budgetYear)
        {

            Collection<int> fromLineItem = new Collection<int> { 220 };
            Collection<int> toLineItem = new Collection<int> { 252 };
            IEnumerable<InvestmentAccount> accounts = null;
            
            string accountType = "investment";
            string report = "3inv";
            accounts = await _investments.GetAccountAsync(userId, accountType, report, fromLineItem, toLineItem, "", budgetYear);
            List<KeyValuePairString> accountsData = new();

            //Kostra accounts to exclude based on parameter.
            List<string> kostraCode = await KostraAccountsToExclude(userId);

            //#164728
            //if (kostraCode.Count < 2)
            //{
            //    accounts = accounts.Where(x => x.invType == "i");
            //}
            // removing duplicates
            accounts = accounts.DistinctBy(x => x.fk_account_code).ToList();


            foreach (var item in accounts.Where(x => !kostraCode.Contains(x.kostra_account_code)).ToList())
            {
                KeyValuePairString data = new();
                data.Key = item.fk_account_code;
                data.Value = item.display_name;
                accountsData.Add(data);
            }
            
            return accountsData;
        }
        private async Task<List<KeyValuePairString>> GetInvRegDepartmentsList(string user,InvRegistrationDropdownInputHelper input)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(user);
            List<clsOrgIdAndDepartments> lst = new List<clsOrgIdAndDepartments>();

            int forecastPeriod = 1;

            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(user, _utility.GetForecastPeriod(input.BudgetYear, forecastPeriod));

            
            List<tco_departments> allDepartments = (await _utility.GetTenantDBContextAsync()).tco_departments.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.status == 1 && x.year_from <= input.BudgetYear && x.year_to >= input.BudgetYear).ToList();

                
                List<string> orgIdSpecificDepartments = await _utility.GetDepartmentsFromTcoOrgHierarchyTableAsync(orgVersionContent, user, input.Level1OrgId, input.Level2OrgId, input.Level3OrgId, input.Level4OrgId, input.Level5OrgId);
                var activeData = (from a in allDepartments
                                    join o in orgIdSpecificDepartments on a.pk_department_code equals o
                                    select new clsOrgIdAndDepartments
                                    {
                                        departmentValue = a.pk_department_code,
                                        departmentText = a.department_name
                                    }).OrderBy(x => x.departmentValue).ToList();

                foreach (var deptCode in activeData)
                {
                    clsOrgIdAndDepartments deptObj = new clsOrgIdAndDepartments()
                    {
                        departmentValue = deptCode.departmentValue,
                        departmentText = deptCode.departmentText
                    };
                    lst.Add(deptObj);
                }

            List<KeyValuePairString> lstData = new();
            foreach (var data in lst)
            {
                // ReSharper disable once InconsistentNaming
                KeyValuePairString depts = new();
                depts.Key = data.departmentValue;
                depts.Value = data.departmentValue + "-" + data.departmentText;
                lstData.Add(depts);
            }
            return lstData;
        }
        private async Task<List<KeyValuePairString>> GetInvRegFunctionsList(string user,int budgetYear)
        {
            TimeSpan cacheTimeOut = new TimeSpan(24, 0, 0);
            UserData userDetails = await _utility.GetUserDetailsAsync(user);
            int clientId = userDetails.client_id;
            string result = await _cache.GetStringForUserAsync(clientId, userDetails.tenant_id, user, "invProj_ServiceAreaFunction");
            List<KeyValuePairString> lstData = new();
            if (result == null)
            {
                IEnumerable<dynamic> functionCodes = await _utility.GetServiceAreaFunctionsAsync(user, budgetYear);

                foreach (dynamic data in functionCodes)
                {
                    KeyValuePairString _data = new();
                    _data.Key = data.function_code;
                    _data.Value = data.function_code + "-" + data.function_name;
                    lstData.Add(_data);
                }
                await _cache.SetStringForUserAsync(clientId, userDetails.tenant_id, user, "invProj_ServiceAreaFunction", JsonConvert.SerializeObject(lstData), cacheTimeOut);
            }
            else
            {
                lstData = JsonConvert.DeserializeObject<List<KeyValuePairString>>(result);
            }
            
            return lstData;
        }
        private async Task<InvRegistrationFreedimHelper> GetInvRegFreedimList(string userId)
        {
            InvRegistrationFreedimHelper lstAllFreeDimData = new();

            List<KeyValuePairString> freeDim1 = new();
            List<KeyValuePairString> freeDim2 = new();
            List<KeyValuePairString> freeDim3 = new();
            List<KeyValuePairString> freeDim4 = new();

            IEnumerable<freedimDefinition> freedimDef = await _utility.GetFreeDimDefinitionsBasedOnPageIdAsync(userId, "investments");
            
            foreach (var item in freedimDef)
            {
                var getAllfridims = await _utility.GetFreeDimAsync(userId, item.freeDimColumn);
                switch (item.freeDimColumn)
                {
                    case "free_dim_1":
                        FormatFreedimData(getAllfridims, freeDim1);
                        break;

                    case "free_dim_2":
                        FormatFreedimData(getAllfridims, freeDim2);
                        break;

                    case "free_dim_3":
                        FormatFreedimData(getAllfridims, freeDim3);
                        break;

                    case "free_dim_4":
                        FormatFreedimData(getAllfridims, freeDim4);
                        break;
                }
            }
            lstAllFreeDimData.Freedim1List = freeDim1;
            lstAllFreeDimData.Freedim2List = freeDim2;
            lstAllFreeDimData.Freedim3List = freeDim3;
            lstAllFreeDimData.Freedim4List = freeDim4;

            return lstAllFreeDimData;
        }
        private async Task<List<KeyValuePairString>> GetINvRegAltercodeList(string userId)
        {
            int actionType = 1000; // for registration grid
            dynamic lstResult = await _pconseq.GetAlterCodesAsync(userId, actionType, true, false, false);
            List<KeyValuePairString> datas = new();
            foreach (var item in lstResult)
            {
                KeyValuePairString data = new KeyValuePairString
                {
                    Key = item.key,
                    Value = item.value
                };
                datas.Add(data);
            }
            return datas;
        }
        private async Task<List<KeyValuePairString>> GetInvRegAdjustmentCodesList(string userId)
        {
            TenantDBContext kostraDbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            List<dynamic> lstresult = new List<dynamic>();
            var res = await (from a in kostraDbContext.tco_adjustment_codes
                                where a.fk_tenant_id == userDetails.tenant_id && a.status == 1
                                select new KeyValuePairString
                                {
                                    Key = a.pk_adjustment_code,
                                    Value = a.pk_adjustment_code + "-" + a.description,
                                }).ToListAsync();
            return res;
        }
        private async Task<List<InvRegistrationProjectDropdown>> GetInvRegProjectList(string userId,string mainProjCode, int budgetYear)
        {
            List<InvRegistrationProjectDropdown> projectList = new();
            IEnumerable<ProjectGridData> projectGridData = await _invProj.GetProjectGridDataAsync(userId, mainProjCode, budgetYear);
            foreach(var item in projectGridData)
            {
                InvRegistrationProjectDropdown project = new();
                project.key = item.projectCode;
                project.value = item.projectCode + "-" + item.projectName;
                project.VatRate = item.vatRate;
                project.VatRefund = item.vatRefund;
                projectList.Add(project);
            }

            return projectList;
        }
        private static void FormatFreedimData(IEnumerable<dynamic> getAllfridims, List<KeyValuePairString> freedim)
        {
            foreach (var freeDim in getAllfridims)
            {
                KeyValuePairString saFreedim = new KeyValuePairString { Key =  freeDim.freeDim_code, Value =  freeDim.freeDim_code + "-" + freeDim.freeDim_name };
                freedim.Add(saFreedim);
            }
        }
        private async Task<List<string>> KostraAccountsToExclude(string userId)
        {
            List<string> kostraAccountsToExclude = new List<string>() { "0429", "0729" };
            var isTaxExpenseIncluded = await _utility.GetParameterValueAsync(userId, "INV_ALLOW_TAX_EXPENSE");
            var isTaxIncomeIncluded = await _utility.GetParameterValueAsync(userId, "INV_ALLOW_TAX_INCOME");
            if (!string.IsNullOrEmpty(isTaxExpenseIncluded) && Convert.ToBoolean(isTaxExpenseIncluded))
                kostraAccountsToExclude.Remove("0429");
            if (!string.IsNullOrEmpty(isTaxIncomeIncluded) && Convert.ToBoolean(isTaxIncomeIncluded))
                kostraAccountsToExclude.Remove("0729");
            return kostraAccountsToExclude;
        }
        private async Task CalculateVATAsync(UserData userDetails, InvRegistrationSaveHelper inputData, Guid newTransId, Guid projTransId,
                                    List<tmd_acc_defaults> defaultAccounts, bool calcVatComp, int year, decimal yearAmount)
        {
            //Delete existing VAT data
            await _unitOfWork.InvestmentProjectRepository.DeleteTransactionAsync(userDetails.tenant_id, year, projTransId);
            await _unitOfWork.CompleteAsync();
            //create new transaction for VAT rows
            if (inputData.VatRate > 0 || inputData.VatRefund > 0)
            {
                GenerateVATTransaction("i", inputData, defaultAccounts, userDetails, newTransId, projTransId, year, yearAmount);
                GenerateVATTransaction("ineg", inputData, defaultAccounts, userDetails, newTransId, projTransId, year, yearAmount);
                if (calcVatComp)
                {
                    GenerateVATTransaction("f", inputData, defaultAccounts, userDetails, newTransId, projTransId, year, yearAmount);
                }
            }
            await _unitOfWork.CompleteAsync();
        }

        private void GenerateVATTransaction(string invType, InvRegistrationSaveHelper inputData, List<tmd_acc_defaults> defaultAccounts,
                                            UserData userDetails, Guid newTransId, Guid projTransId, int year, decimal yearAmount)
        {
            string accountCode = string.Empty, departmentCode = string.Empty, functionCode = string.Empty;
            if (invType == "i")
            {
                accountCode = defaultAccounts.FirstOrDefault(x => x.link_type == "VAT_COST" && x.acc_type == "ACCOUNT") != null ?
                       defaultAccounts.FirstOrDefault(x => x.link_type == "VAT_COST" && x.acc_type == "ACCOUNT").acc_value
                       : inputData.AccountCode;

                departmentCode = defaultAccounts.FirstOrDefault(x => x.link_type == "VAT_COST" && x.acc_type == "DEPARTMENT") != null ?
                                   defaultAccounts.FirstOrDefault(x => x.link_type == "VAT_COST" && x.acc_type == "DEPARTMENT").acc_value
                                   : inputData.DepartmentCode;

                functionCode = defaultAccounts.FirstOrDefault(x => x.link_type == "VAT_COST" && x.acc_type == "FUNCTION") != null ?
                                   defaultAccounts.FirstOrDefault(x => x.link_type == "VAT_COST" && x.acc_type == "FUNCTION").acc_value
                                   : inputData.FunctionCode;
            }
            else if (invType == "f")
            {
                accountCode = defaultAccounts.FirstOrDefault(x => x.link_type == "VAT_COMP" && x.acc_type == "ACCOUNT") != null ?
                       defaultAccounts.FirstOrDefault(x => x.link_type == "VAT_COMP" && x.acc_type == "ACCOUNT").acc_value
                       : inputData.AccountCode;

                departmentCode = defaultAccounts.FirstOrDefault(x => x.link_type == "VAT_COMP" && x.acc_type == "DEPARTMENT") != null ?
                                   defaultAccounts.FirstOrDefault(x => x.link_type == "VAT_COMP" && x.acc_type == "DEPARTMENT").acc_value
                                   : inputData.DepartmentCode;

                functionCode = defaultAccounts.FirstOrDefault(x => x.link_type == "VAT_COMP" && x.acc_type == "FUNCTION") != null ?
                                   defaultAccounts.FirstOrDefault(x => x.link_type == "VAT_COMP" && x.acc_type == "FUNCTION").acc_value
                                   : inputData.FunctionCode;
            }
            else
            {
                accountCode = inputData.AccountCode;
                departmentCode = inputData.DepartmentCode;
                functionCode = inputData.FunctionCode;
            }
            if (invType == "i" || invType == "f")
            {
                tfp_proj_transactions transData = new tfp_proj_transactions
                {
                    pk_id = Guid.NewGuid(),
                    trans_id = newTransId,
                    fk_tenant_id = userDetails.tenant_id,
                    fk_account_code = accountCode,
                    fk_function_code = functionCode,
                    fk_department_code = departmentCode,
                    fk_project_code = inputData.ProjectCode,
                    free_dim_1 = inputData.FreeDim1 == null ? string.Empty : inputData.FreeDim1,
                    free_dim_2 = inputData.FreeDim2 == null ? string.Empty : inputData.FreeDim2,
                    free_dim_3 = inputData.FreeDim3 == null ? string.Empty : inputData.FreeDim3,
                    free_dim_4 = inputData.FreeDim4 == null ? string.Empty : inputData.FreeDim4,
                    vat_rate = 0,
                    vat_refund = 0,
                    year = year,
                    amount = invType == "i" ? Math.Round(((yearAmount * 1000) / (1 + (inputData.VatRate / 100)) * (inputData.VatRate / 100) * (inputData.VatRefund / 100))) :
                                       Math.Round(((yearAmount * 1000) / (1 + (inputData.VatRate / 100)) * (inputData.VatRate / 100) * (inputData.VatRefund / 100)) * -1),
                    fk_change_id = inputData.ChangeId,
                    fk_alter_code = inputData.AlterCode,
                    fk_adjustment_code = inputData.AdjustmentCode,
                    fk_user_adjustment_code = inputData.UserAdjustmentCode,
                    is_vat_row = true,
                    fk_proj_trans_id = projTransId,
                    description = inputData.Description,
                    updated_by = userDetails.pk_id,
                    updated = DateTime.UtcNow
                };
                _unitOfWork.GenericRepo.Add(transData);
            }
            else
            {
                tfp_proj_transactions transData = new tfp_proj_transactions
                {
                    pk_id = Guid.NewGuid(),
                    trans_id = newTransId,
                    fk_tenant_id = userDetails.tenant_id,
                    fk_account_code = accountCode,
                    fk_function_code = functionCode,
                    fk_department_code = departmentCode,
                    fk_project_code = inputData.ProjectCode,
                    free_dim_1 = inputData.FreeDim1 == null ? string.Empty : inputData.FreeDim1,
                    free_dim_2 = inputData.FreeDim2 == null ? string.Empty : inputData.FreeDim2,
                    free_dim_3 = inputData.FreeDim3 == null ? string.Empty : inputData.FreeDim3,
                    free_dim_4 = inputData.FreeDim4 == null ? string.Empty : inputData.FreeDim4,
                    vat_rate = 0,
                    vat_refund = 0,
                    year = year,
                    amount = Math.Round(((yearAmount * 1000) / (1 + (inputData.VatRate / 100)) * (inputData.VatRate / 100) * (inputData.VatRefund / 100)) * -1),
                    fk_change_id = inputData.ChangeId,
                    fk_alter_code = inputData.AlterCode,
                    fk_adjustment_code = inputData.AdjustmentCode,
                    fk_user_adjustment_code = inputData.UserAdjustmentCode,
                    is_vat_row = true,
                    fk_proj_trans_id = projTransId,
                    description = inputData.Description,
                    updated_by = userDetails.pk_id,
                    updated = DateTime.UtcNow
                };
                _unitOfWork.GenericRepo.Add(transData);
            }
        }
        private static void UpdateYear(InvRegistrationSaveHelper input, int startYear)
        {
            
            int counter = 0;
            foreach (var yearValue in input.YearAmount.OrderBy(y => PadNumbers(y.Key)).ToList())
            {
                yearValue.year = startYear + counter;
                counter++;
            }
        }
        private static string PadNumbers(string input)
        {
            return Regex.Replace(input, "[0-9]+", match => match.Value.PadLeft(10, '0'));
        }
        public string GetLangText(Dictionary<string, clsLanguageString> langStrings, string langKey)
        {
            return langStrings.FirstOrDefault(x => x.Key.Equals(langKey, StringComparison.InvariantCultureIgnoreCase)).Value.LangText ?? string.Empty;
        }
        #endregion private
    }

}