using Framsikt.BL.Core;
using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using System.Globalization;

namespace Framsikt.BL
{
    public partial class BudgetProposals
    {
        public async Task<AssignmentInitialSectionHelper> GetAssignmentNameSectionData(string userId, Guid assignmentId,
            int budgetYear, string orgId, int orgLevel)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            AssignmentInitialSectionHelper result = new AssignmentInitialSectionHelper();
            Dictionary<string, clsLanguageString> langStrings =
                await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name,
                    "BudgetProposal");
            var isTenantSyncSetup =
                await _dataSyncUtility.IsTenantSyncSetup(userDetails.tenant_id, userDetails.client_id);
            result.PopupTitle = langStrings["budp_assignment_update_title"].LangText;
            var assignmentInfo =
                await _unitOfWork.BudgetProposalRepository.GetBudPropAssignmentInfo(userDetails.tenant_id, budgetYear,
                    assignmentId);
            if (assignmentInfo != null)
            {
                List<string> categoryTypes = new List<string>() { "sunit_bplan", "climate_action" };
                List<string> catList = assignmentInfo.category.ToLower().Split(',').ToList();

                var orgCreatedat = assignmentInfo.isDistributedtoLowerLevel.HasValue
                    ? string.IsNullOrEmpty(assignmentInfo.distributedParentOrgID)
                        ? assignmentInfo.parentOrgId
                        : assignmentInfo.distributedParentOrgID
                    : assignmentInfo.parentOrgId;
                var orgCreatedLevel = assignmentInfo.isDistributedtoLowerLevel.HasValue
                    ? !assignmentInfo.distributedParentOrgLevel.HasValue ? assignmentInfo.parentOrgLevel :
                    assignmentInfo.distributedParentOrgLevel.Value == 0 ? assignmentInfo.parentOrgLevel :
                    assignmentInfo.distributedParentOrgLevel
                    : assignmentInfo.parentOrgLevel;
                bool isEditable = (orgCreatedat == orgId && orgCreatedLevel.Value == orgLevel);

                var categories =
                    await _unitOfWork.BudgetProposalRepository.GetCategoryInfoForAssignments(userDetails.tenant_id,
                        categoryTypes);
                categories = categories.Where(x =>
                    catList.Contains(x.pk_cat_id.ToString().ToLower()) ||
                    assignmentInfo.climate_category.ToString().ToLower() == x.pk_cat_id.ToString().ToLower()).ToList();

                result.AssignmentName = assignmentInfo.assignmentName;
                result.AssignmentId = assignmentInfo.assignmentId;
                result.UniqueAssignmentId = assignmentInfo.uniqueassignmentId;
                result.ClimateCategoryId = assignmentInfo.climate_category;
                result.SortingValue = 0;
                result.ShowClimateCategorySection = assignmentInfo.climate_category != Guid.Empty;
                result.CategoryIds = categories.Where(x => x.pk_cat_id != assignmentInfo.climate_category)
                    .Select(x => x.pk_cat_id).Distinct().ToList();
                result.CategoryNames = categories.Where(x => catList.Contains(x.pk_cat_id.ToString()))
                    .Select(x => x.description).Distinct().ToList();
                result.AssignmentRefferenceId = assignmentInfo.assignmentrefid;

                var climateCategoryInfo =
                    categories.FirstOrDefault(x => x.pk_cat_id == assignmentInfo.climate_category);
                result.ClimateCategoryName =
                    climateCategoryInfo == null ? string.Empty : climateCategoryInfo.description;
                result.IsEditable = isEditable;
                KeyValueHelperData syncStatusData = new KeyValueHelperData();
                syncStatusData.key = isTenantSyncSetup ? assignmentInfo.sync_status : -1;
                syncStatusData.value = isTenantSyncSetup
                    ? await _unitOfWork.BudgetProposalRepository.GetSyncStatusValue(userDetails.tenant_id,
                        assignmentInfo.sync_status, SyncObjectStatusType.SYNC_ASSIGNMENT_STATUS)
                    : string.Empty;
                result.SyncStatus = syncStatusData;
            }

            return result;
        }

        public async Task<bool> CheckAssignmentEditability(string userId, Guid assignmentId, int budgetYear,
            string orgId, int orgLevel)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            var assignmentInfo =
                await _unitOfWork.BudgetProposalRepository.GetBudPropAssignmentInfo(userDetails.tenant_id, budgetYear,
                    assignmentId);
            if (assignmentInfo != null)
            {
                var orgCreatedat = assignmentInfo.isDistributedtoLowerLevel.HasValue
                    ? string.IsNullOrEmpty(assignmentInfo.distributedParentOrgID)
                        ? assignmentInfo.parentOrgId
                        : assignmentInfo.distributedParentOrgID
                    : assignmentInfo.parentOrgId;
                var orgCreatedLevel = assignmentInfo.isDistributedtoLowerLevel.HasValue
                    ? !assignmentInfo.distributedParentOrgLevel.HasValue ? assignmentInfo.parentOrgLevel :
                    assignmentInfo.distributedParentOrgLevel.Value == 0 ? assignmentInfo.parentOrgLevel :
                    assignmentInfo.distributedParentOrgLevel
                    : assignmentInfo.parentOrgLevel;
                bool isEditable = (orgCreatedat == orgId && orgCreatedLevel.Value == orgLevel);
                return isEditable;
            }

            return false;
        }

        public async Task<string> SaveAssignmentNameSectionData(string userId, Guid assignmentId, int budgetYear,
            AssignmentInitialSection updatedData)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            //Update delegated assignments as well.
            var assignmentsList = await _unitOfWork.BudgetProposalRepository.GetAllAssignments(userDetails.tenant_id,
                budgetYear, updatedData.UniqueAssignmentId);

            var currentAssignmentInfo = assignmentsList.FirstOrDefault(x => x.assignmentId == updatedData.AssignmentId);

            if (currentAssignmentInfo != null)
            {
                foreach (var assignment in assignmentsList)
                {
                    var assignmentInfo = assignmentsList.FirstOrDefault(x => x.assignmentId == assignment.assignmentId);
                    if (assignmentInfo != null)
                    {
                        assignmentInfo.assignmentName = updatedData.AssignmentName;
                        assignmentInfo.climate_category = updatedData.ClimateCategoryId;
                        assignmentInfo.category = string.Join(",", updatedData.CategoryIds);
                        assignmentInfo.sync_status = updatedData.SyncStatus.key;
                        _unitOfWork.GenericRepo.Update(assignmentInfo);

                        //log assignment update status for plan disc
                        await _planDiscAssignments.ValidateAndSaveAssignmentForPDAsync(userId, budgetYear,
                            assignment.orgLevel ?? 0, assignment.orgId, assignment.assignmentId,
                            PDMeetingType.UPDATE.ToString());
                    }
                }

                await _unitOfWork.CompleteAsync();
                return "success";
            }

            return "error";
        }

        public async Task<AssignmentIdHelper> CreateNewAssignment(string userId, int budgetYear,
            AssignmentCreationHelper input)
        {
            TenantDBContext tenantDBContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            int serviceLevel = await GetServiceLevel(userDetails.tenant_id);
            var assignmentRefID =
                await _busPlan.GenerateAssignmentReferenceAsync(userId, input.OrgId, budgetYear.ToString());

            tbiassignments newAssignment = new tbiassignments();
            newAssignment.tenantId = userDetails.tenant_id;
            newAssignment.assignmentId = Guid.NewGuid();
            newAssignment.uniqueassignmentId = Guid.NewGuid();
            newAssignment.parentOrgId = input.OrgId;
            newAssignment.parentOrgLevel = input.OrgLevel;
            newAssignment.orgId = input.OrgId;
            newAssignment.orgLevel = input.OrgLevel;
            newAssignment.serviceId = input.ServiceId;
            newAssignment.serviceLevel = serviceLevel;
            newAssignment.updated = DateTime.UtcNow;
            newAssignment.updatedBy = userDetails.pk_id;
            newAssignment.assignmentName = input.AssignmentName;
            newAssignment.budgetYear = budgetYear;
            newAssignment.description = string.Empty;
            newAssignment.startDate = DateTime.UtcNow;
            newAssignment.endDate = new DateTime(budgetYear, 12, 31, 00, 00, 00);
            newAssignment.userAssignedTo = 0;
            newAssignment.category = string.Join(",", input.CategoryIds);
            newAssignment.climate_category = input.ClimateCategoryId;
            newAssignment.tags = string.Empty;
            newAssignment.createdby = userDetails.pk_id;
            newAssignment.createddate = DateTime.UtcNow;
            newAssignment.updated = DateTime.UtcNow;
            newAssignment.updatedBy = userDetails.pk_id;
            newAssignment.description_history = Guid.NewGuid();
            newAssignment.external_reference = string.Empty;
            newAssignment.status = 0;
            newAssignment.is_readonly_ass = false;
            newAssignment.assignmentrefid = assignmentRefID;
            newAssignment.assignmentOwner = 0;
            newAssignment.isBudgetProposal = true;
            newAssignment.includedInMonthlyReport = true;
            newAssignment.isDistributedtoLowerLevel = false;
            newAssignment.distributedParentOrgID = string.Empty;
            newAssignment.distributedParentOrgLevel = 0;
            newAssignment.fkAttributeId = input.AttributeId;
            newAssignment.sync_status = input.SyncStatus.key;
            newAssignment.start_year_fp = budgetYear;
            newAssignment.start_year_bp = budgetYear;
            newAssignment.end_year = budgetYear;
            newAssignment.status_finplan = 0;
            newAssignment.service_id_created_at = input.ServiceId;
            await tenantDBContext.tbiassignments.AddAsync(newAssignment);
            await tenantDBContext.SaveChangesAsync();

            return new AssignmentIdHelper()
            {
                AssignmentId = newAssignment.assignmentId,
                UniqueAssignmentId = newAssignment.uniqueassignmentId
            };
        }

        public async Task<AssignmentDescriptionDataHelper> GetCurrentAssignmentDescriptionInfo(string userId,
            Guid assignmentId, int budgetYear)
        {
            TenantDBContext tenantDBContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStrings =
                await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name,
                    "BudgetProposal");

            var descriptionInfo = await tenantDBContext.tco_assignments_descriptions.FirstOrDefaultAsync(x =>
                x.fk_tenant_id == userDetails.tenant_id
                && x.fk_assignment_id == assignmentId);

            if (descriptionInfo != null)
            {
                var descriptionId = descriptionInfo.assignment_description_historyId == null ||
                                    descriptionInfo.assignment_description_historyId.Value == Guid.Empty
                    ? Guid.NewGuid()
                    : descriptionInfo.assignment_description_historyId.Value;

                var description = !string.IsNullOrEmpty(descriptionInfo.assignment_description)
                    ? descriptionInfo.assignment_description
                    : string.Empty;

                return new AssignmentDescriptionDataHelper()
                {
                    AssignmentId = assignmentId,
                    Description = await _utility.GetValidDescription(userId, descriptionId, description, budgetYear,
                        nameof(GetCurrentAssignmentDescriptionInfo), PublishTreeType.BudgetProposal),
                    DescriptionId = descriptionId,
                    IsEditorEnabled = true,
                    DescriptionTitle = langStrings["budp_assignment_desc_title"].LangText
                };
            }

            return new AssignmentDescriptionDataHelper()
            {
                AssignmentId = assignmentId,
                Description = string.Empty,
                DescriptionId = Guid.NewGuid(),
                IsEditorEnabled = true,
                DescriptionTitle = langStrings["budp_assignment_desc_title"].LangText
            };
        }

        public async Task<string> SaveAssignmentDescription(string userId, int budgetYear,
            AssignmentDescriptionDataHelper descriptionInput, bool isDelegationSave)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            try
            {
                List<BPAssignmentDescriptionsHelper> descArray = new List<BPAssignmentDescriptionsHelper>();

                var descriptionsToSave =
                    await _unitOfWork.BudgetProposalRepository.GetAssignmentDescription(userDetails.tenant_id,
                        descriptionInput.AssignmentId);

                if (string.IsNullOrEmpty(descriptionInput.Description) && descriptionsToSave == null)
                {
                    //Do not make an entry in desc table with empty string for the first time
                }
                else
                {
                    //log assignment entry for plandisc
                    await _planDiscAssignments.ValidateAndSaveAssignmentForPDAsync(userId, budgetYear,
                        descriptionInput.OrgLevel, descriptionInput.OrgId, descriptionInput.AssignmentId,
                        PDMeetingType.UPDATE.ToString());

                    BPAssignmentDescriptionsHelper descRow = new BPAssignmentDescriptionsHelper()
                    {
                        assignmentId = descriptionInput.AssignmentId,
                        descHistoryId = descriptionInput.DescriptionId == Guid.Empty
                            ? Guid.NewGuid()
                            : descriptionInput.DescriptionId,
                        descPkId = descriptionsToSave == null ? 0 : descriptionsToSave.pk_id,
                        descriptionText = !string.IsNullOrEmpty(descriptionInput.Description)
                            ? descriptionInput.Description
                            : string.Empty,
                        ConnectedUserName = new List<string>()
                    };
                    descArray.Add(descRow);
                }

                //Get all delegated assignments descriptions.
                //when creating assignment and writing description: insert desc for delegated assignments same text as parent assignment.
                // No change on delegated areas when parent text is changed/updated #91733
                if (!string.IsNullOrEmpty(descriptionInput.Description))
                {
                    List<string> lowerOrgIds = new List<string>();
                    if (!isDelegationSave)
                    {
                        var assignmentInfo =
                            await _unitOfWork.BudgetProposalRepository.GetBudPropAssignmentInfo(userDetails.tenant_id,
                                budgetYear, descriptionInput.AssignmentId);
                        var orgVersionContent =
                            await _utility.GetOrgVersionSpecificContentAsync(userId,
                                _utility.GetForecastPeriod(budgetYear, 1));
                        List<clsOrgIdsAndServiceIds> lstResult =
                            await _utility.GetOrgIdsAndServiceIdsIrrespectiveOfDataAsync(userId,
                                orgVersionContent.orgVersion);
                        if (assignmentInfo.orgLevel == 1)
                        {
                            lowerOrgIds = lstResult.Select(y => y.orgId).Distinct().ToList();
                        }
                        else
                        {
                            lowerOrgIds = lstResult
                                .Where(x => x.parentId == assignmentInfo.orgId || x.orgId == assignmentInfo.orgId)
                                .Select(y => y.orgId).Distinct().ToList();
                        }
                    }
                    //removed description delegation
                }

                if (descArray.Any())
                {
                    await _oldBudProp.SaveBpAssignmentDescriptionAsync(userId, descArray, budgetYear,
                        descriptionInput.logHistory);
                }

                return "success";
            }
            catch (Exception e)
            {
                return e.Message;
            }
        }

        public async Task<AssignmentInfoSectionHelper> GetAssignmentInfoSection(string userId, Guid assignmentId,
            int budgetYear, bool sendDataSource)
        {
            TenantDBContext tenantDBContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            AssignmentInfoSectionHelper result = new AssignmentInfoSectionHelper();

            var assignmentInfo =
                await _unitOfWork.BudgetProposalRepository.GetBudPropAssignmentInfo(userDetails.tenant_id, budgetYear,
                    assignmentId);

            if (assignmentInfo != null)
            {
                result.AssignmentId = assignmentId;
                result.UniqueAssignmentId = assignmentInfo.uniqueassignmentId;
                result.EndDate = assignmentInfo.endDate.ToString("dd.MM.yyyy");
                result.StartDate = assignmentInfo.startDate.ToString("dd.MM.yyyy");
                result.Status = assignmentInfo.status_finplan;
                result.StartYearFP = assignmentInfo.start_year_fp;
                result.EndYear = assignmentInfo.end_year;
                result.StartYearBP = assignmentInfo.start_year_bp;
                result.Responsible = assignmentInfo.userAssignedTo == null ? -1 : assignmentInfo.userAssignedTo.Value;
                result.AssignmentOwner = assignmentInfo.assignmentOwner;
                result.External_reference = string.IsNullOrEmpty(assignmentInfo.external_reference)
                    ? string.Empty
                    : assignmentInfo.external_reference;
                if (sendDataSource)
                {
                    var statusDescriptions =
                        await _unitOfWork.BudgetProposalRepository.GetProgressStatusForAssignments(
                            userDetails.tenant_id);
                    List<KeyIntValStringHelper> statusList = new List<KeyIntValStringHelper>();

                    foreach (var t in statusDescriptions)
                    {
                        KeyIntValStringHelper status = new KeyIntValStringHelper();
                        status.key = t.status_id;
                        status.value = t.status_description;
                        statusList.Add(status);
                    }

                    result.StatusList = statusList.OrderBy(x => x.key).ToList();

                    List<BusinessPlanUserData> tenantUsers = await _busPlan.GetUserOrgData(userId, budgetYear);
                    tenantUsers = tenantUsers
                        .OrderBy(x => x.FirstName, StringComparer.Create(CultureInfoFactory.CreateCulture("nb-NO"), false))
                        .ThenBy(x => x.LastName, StringComparer.Create(CultureInfoFactory.CreateCulture("nb-NO"), false)).ToList();
                    List<KeyIntValStringHelper> tenantUsersList = new List<KeyIntValStringHelper>();

                    KeyIntValStringHelper emptyRow = new();
                    emptyRow.key = 0;
                    emptyRow.value = string.Empty;
                    tenantUsersList.Add(emptyRow);

                    foreach (var t in tenantUsers)
                    {
                        string firstName = string.IsNullOrEmpty(t.FirstName) ? string.Empty : t.FirstName;
                        string lastName = string.IsNullOrEmpty(t.LastName) ? string.Empty : t.LastName;

                        KeyIntValStringHelper tenantUser = new KeyIntValStringHelper();
                        tenantUser.key = t.Id;
                        tenantUser.value = (firstName + " " + lastName).Trim();
                        tenantUsersList.Add(tenantUser);
                    }

                    result.TenantUsersList = tenantUsersList;
                }
            }

            return result;
        }

        public async Task<string> SaveAssignmentInfoSection(string userId, AssignmentInfoSectionHelper input,
            int budgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            try
            {
                var assignmentsList =
                    await _unitOfWork.BudgetProposalRepository.GetAllAssignments(userDetails.tenant_id, budgetYear,
                        input.UniqueAssignmentId);
                var assignmentInfo = assignmentsList.FirstOrDefault(x => x.assignmentId == input.AssignmentId);
                var delegatedAssignments = assignmentsList.Where(x => x.assignmentId != input.AssignmentId).ToList();
                string changeType = PDMeetingType.UPDATE.ToString();

                if (assignmentInfo != null)
                {
                    if (!await CheckAssignmentEditability(userId, input.AssignmentId, budgetYear, assignmentInfo.orgId,
                            assignmentInfo.orgLevel.Value))
                    {
                        //Update info for only below fields for the current assignment at delegated levels.
                        assignmentInfo.startDate = string.IsNullOrEmpty(input.StartDate)
                            ? DateTime.UtcNow
                            : DateTime.Parse(input.StartDate, DateTimeFormatInfo.InvariantInfo);
                        assignmentInfo.status = input.Status;
                        assignmentInfo.userAssignedTo = input.Responsible;
                        assignmentInfo.start_year_bp = input.StartYearBP;
                        assignmentInfo.end_year = input.EndYear;
                        assignmentInfo.status_finplan = input.Status;
                        _unitOfWork.GenericRepo.Update(assignmentInfo);
                    }
                    else
                    {
                        assignmentInfo.endDate = string.IsNullOrEmpty(input.EndDate)
                            ? DateTime.UtcNow
                            : DateTime.Parse(input.EndDate, DateTimeFormatInfo.InvariantInfo);
                        assignmentInfo.startDate = string.IsNullOrEmpty(input.StartDate)
                            ? DateTime.UtcNow
                            : DateTime.Parse(input.StartDate, DateTimeFormatInfo.InvariantInfo);
                        assignmentInfo.status_finplan = input.Status;
                        assignmentInfo.external_reference = string.IsNullOrEmpty(input.External_reference)
                            ? string.Empty
                            : input.External_reference;
                        assignmentInfo.userAssignedTo = input.Responsible;
                        assignmentInfo.assignmentOwner = input.AssignmentOwner;
                        assignmentInfo.start_year_bp = input.StartYearBP == 0 ? DateTime.Now.Year : input.StartYearBP;
                        assignmentInfo.end_year = input.EndYear == 0 ? DateTime.Now.Year : input.EndYear;
                        _unitOfWork.GenericRepo.Update(assignmentInfo);

                        //Update delegated assignments as well(At parent level).
                        foreach (var assignment in delegatedAssignments)
                        {
                            var currentAssignmentInTheLoop =
                                delegatedAssignments.FirstOrDefault(x => x.assignmentId == assignment.assignmentId);

                            if (currentAssignmentInTheLoop != null)
                            {
                                currentAssignmentInTheLoop.endDate = string.IsNullOrEmpty(input.EndDate)
                                    ? DateTime.UtcNow
                                    : DateTime.Parse(input.EndDate, DateTimeFormatInfo.InvariantInfo);
                                currentAssignmentInTheLoop.external_reference =
                                    string.IsNullOrEmpty(input.External_reference)
                                        ? string.Empty
                                        : input.External_reference;
                                currentAssignmentInTheLoop.assignmentOwner = input.AssignmentOwner;
                                currentAssignmentInTheLoop.start_year_bp = input.StartYearBP;
                                currentAssignmentInTheLoop.end_year = input.EndYear;
                                currentAssignmentInTheLoop.status_finplan = input.Status;

                                await _planDiscAssignments.ValidateAndSaveAssignmentForPDAsync(userId, budgetYear,
                                    input.OrgLevel, input.OrgId, assignment.assignmentId, changeType);

                                _unitOfWork.GenericRepo.Update(currentAssignmentInTheLoop);
                            }
                        }
                    }

                    await _planDiscAssignments.ValidateAndSaveAssignmentForPDAsync(userId, budgetYear, input.OrgLevel,
                        input.OrgId, input.AssignmentId, changeType);

                    await _unitOfWork.CompleteAsync();
                }
                else
                {
                    return "error";
                }

                return "success";
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<string> SaveAssignmentDelegation(string userId, int budgetYear,
            AssignmentDelegationSaveHelper input)
        {
            try
            {
                TenantDBContext tenantDBContext = await _utility.GetTenantDBContextAsync();
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                List<Guid> delegatedAssignmentIds = new List<Guid>();
                var assignmentInfo =
                    await _unitOfWork.BudgetProposalRepository.GetBudPropAssignmentInfo(userDetails.tenant_id,
                        budgetYear, input.AssignmentId);
                var allAssignments =
                    await _unitOfWork.BudgetProposalRepository.GetAllBudPropAssignment(userDetails.tenant_id,
                        budgetYear, input.UniqueAssignmentId);
                var existingAssignmentData = allAssignments.FirstOrDefault(x => !x.isDistributedtoLowerLevel.Value);
                var oldSelectedOrgList = allAssignments
                    .Where(x => x.isBudgetProposal.HasValue && x.isBudgetProposal.Value).ToList();

                int orgLevel = input.OrgLevel;
                var orgVersionContent =
                    await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));

                int serviceLevel = await GetServiceLevel(userDetails.tenant_id);
                input.ServiceId =
                    string.IsNullOrEmpty(input.ServiceId.Trim()) || input.ServiceId.ToLower() == "All".ToLower() ||
                    input.ServiceId == "-1"
                        ? string.Empty
                        : input.ServiceId;

                string paramValueFp1 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_1");
                string paramValueFp2 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_2");

                int fpLvl1OrgLevel = -1, fpLvl2OrgLevel = -1, fpLvl1ServiceLevel = -1, fpLvl2ServiceLevel = -1;
                if (!string.IsNullOrEmpty(paramValueFp1))
                {
                    if (paramValueFp1.Split('_')[0].ToUpper() == "ORG")
                    {
                        fpLvl1OrgLevel = int.Parse(paramValueFp1.Split('_')[2]);
                    }
                    else if (paramValueFp1.Split('_')[0].ToUpper() == "SERVICE")
                    {
                        fpLvl1ServiceLevel = int.Parse(paramValueFp1.Split('_')[2]);
                    }
                }

                if (!string.IsNullOrEmpty(paramValueFp2))
                {
                    if (paramValueFp2.Split('_')[0].ToUpper() == "ORG")
                    {
                        fpLvl2OrgLevel = int.Parse(paramValueFp2.Split('_')[2]);
                    }
                    else if (paramValueFp2.Split('_')[0].ToUpper() == "SERVICE")
                    {
                        fpLvl2ServiceLevel = int.Parse(paramValueFp2.Split('_')[2]);
                    }
                }

                string fpLevelTwoAllOrEmpty = !string.IsNullOrEmpty(paramValueFp2) && fpLvl2ServiceLevel != -1
                    ? "ALL"
                    : string.Empty;
                string proIdCo = string.Empty;
                List<tco_assignments_descriptions> finplanDescForAllDelegatedAssignments =
                    new List<tco_assignments_descriptions>();

                var duplicateIds = new List<string>();
                int index = 0;
                int currentLevelItems = 0;
                List<string> processedOrgs = new List<string>();
                string referenceId = string.Empty;
                bool isChapterSetup = await _finUtility.isChapterSetup(userId);

                //For delegated assignments only
                foreach (var checkedItem in input.TreeOrgItems)
                {
                    currentLevelItems++;
                    //Perform only on delegated levels since the current level info is saved already as part of auto save.
                    if (currentLevelItems <= 2)
                    {
                        index++;
                        continue;
                    }

                    int itemCount = input.TreeOrgItems.Count(x =>
                        x.fpLevel == checkedItem.fpLevel && x.id == checkedItem.id &&
                        x.parentId == checkedItem.parentId);
                    if (itemCount > 1 && !duplicateIds.Contains(checkedItem.id))
                    {
                        duplicateIds.Add(checkedItem.id);
                        continue;
                    }

                    proIdCo = checkedItem.id;

                    if (!string.IsNullOrEmpty(checkedItem.parentId))
                    {
                        proIdCo = $"{proIdCo}_{checkedItem.parentId}";
                    }

                    checkedItem.@checked = checkedItem.@checked;
                    if (!processedOrgs.Contains(proIdCo))
                    {
                        tbiassignments tcg = _oldBudProp.GetCurrentAssignmentInfo(userDetails, oldSelectedOrgList,
                            checkedItem, fpLvl1OrgLevel, fpLvl2OrgLevel, fpLevelTwoAllOrEmpty, input, false);

                        if (checkedItem.@checked)
                        {
                            if (tcg == null) //Add
                            {
                                tcg = new tbiassignments();
                                tcg.tenantId = userDetails.tenant_id;
                                tcg.assignmentId = Guid.NewGuid();
                                tcg.uniqueassignmentId = input.UniqueAssignmentId;
                                tcg.parentOrgId = input.OrgId;
                                tcg.parentOrgLevel = input.OrgLevel;
                                tcg.service_id_created_at = input.ServiceId;
                                if (checkedItem.fpLevel == 0)
                                {
                                    tcg.orgId = checkedItem.id;
                                    tcg.orgLevel = 1;
                                    tcg.serviceId = fpLevelTwoAllOrEmpty;
                                    tcg.serviceLevel = fpLvl1ServiceLevel;
                                }
                                else if (checkedItem.fpLevel == 1)
                                {
                                    if (fpLvl1OrgLevel != -1)
                                    {
                                        tcg.orgId = checkedItem.id;
                                        tcg.orgLevel = fpLvl1OrgLevel;
                                        tcg.serviceId = fpLevelTwoAllOrEmpty;
                                        tcg.serviceLevel = fpLvl1ServiceLevel;
                                    }
                                    else
                                    {
                                        tcg.orgId = string.Empty;
                                        tcg.orgLevel = 0;
                                        tcg.serviceId = checkedItem.id;
                                        tcg.serviceLevel = fpLvl1ServiceLevel;
                                    }
                                }
                                else
                                {
                                    if (fpLvl2OrgLevel != -1)
                                    {
                                        tcg.orgId = checkedItem.id;
                                        tcg.orgLevel = fpLvl2OrgLevel;
                                        tcg.serviceId = fpLevelTwoAllOrEmpty;
                                        tcg.serviceLevel = fpLvl2ServiceLevel;
                                    }
                                    else
                                    {
                                        tcg.orgId = checkedItem.parentId;
                                        tcg.orgLevel = fpLvl1OrgLevel;
                                        tcg.serviceId = checkedItem.id;
                                        tcg.serviceLevel = fpLvl2ServiceLevel;
                                    }
                                }

                                tcg.updated = DateTime.UtcNow;
                                tcg.updatedBy = userDetails.pk_id;
                                tcg.assignmentName = assignmentInfo.assignmentName;
                                tcg.budgetYear = budgetYear;
                                tcg.description = index == 0 ? assignmentInfo.description : string.Empty;
                                tcg.startDate = assignmentInfo.startDate;
                                tcg.endDate = assignmentInfo.endDate;
                                tcg.userAssignedTo = assignmentInfo.userAssignedTo;
                                tcg.category = assignmentInfo.category;
                                tcg.climate_category = assignmentInfo.climate_category;
                                tcg.tags = assignmentInfo.tags;
                                tcg.createdby = userDetails.pk_id;
                                tcg.createddate = DateTime.UtcNow;
                                tcg.updated = DateTime.UtcNow;
                                tcg.updatedBy = userDetails.pk_id;
                                tcg.description_history = Guid.NewGuid();
                                tcg.external_reference = assignmentInfo.external_reference;
                                tcg.status = assignmentInfo.status;
                                tcg.is_readonly_ass = false;
                                tcg.assignmentrefid = assignmentInfo.assignmentrefid;
                                tcg.assignmentOwner = assignmentInfo.assignmentOwner;
                                tcg.isBudgetProposal = true;
                                tcg.includedInMonthlyReport = true;
                                tcg.fkAttributeId = string.Empty;
                                tcg.isDistributedtoLowerLevel = (input.OrgId == tcg.orgId &&
                                                                 tcg.orgLevel == input.OrgLevel &&
                                                                 (input.ServiceId == tcg.serviceId ||
                                                                  (string.IsNullOrEmpty(input.ServiceId) &&
                                                                   tcg.serviceId.ToUpper() == "ALL")));
                                tcg.distributedParentOrgID =
                                    existingAssignmentData != null &&
                                    existingAssignmentData.parentOrgId != input.OrgId &&
                                    existingAssignmentData.parentOrgLevel != orgLevel
                                        ? existingAssignmentData.parentOrgId
                                        : "";
                                tcg.distributedParentOrgLevel =
                                    existingAssignmentData != null &&
                                    existingAssignmentData.parentOrgId != input.OrgId &&
                                    existingAssignmentData.parentOrgLevel != orgLevel
                                        ? existingAssignmentData.parentOrgLevel.Value
                                        : 0;
                                tcg.goalId = Guid.Empty;
                                tcg.guidance_text = string.Empty;
                                tcg.guidance_text_history = Guid.NewGuid();
                                tcg.start_year_fp = assignmentInfo.start_year_fp;
                                tcg.start_year_bp = assignmentInfo.start_year_bp;
                                tcg.end_year = assignmentInfo.end_year;
                                _unitOfWork.GenericRepo.Add(tcg);

                                delegatedAssignmentIds.Add(tcg.assignmentId);
                            }

                            processedOrgs.Add(proIdCo);
                            index++;
                        }
                        else
                        {
                            //Delete unchecked assignments if exists.
                            if (tcg != null)
                            {
                                // Fix for 95289
                                int orgLvl = 2;
                                if (checkedItem.fpLevel == orgLvl)
                                {
                                    var orgIdList = new List<string>();
                                    List<tco_org_hierarchy> lstOrgHierarchy = orgVersionContent.lstOrgHierarchy;

                                    lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_3 == checkedItem.id).ToList();
                                    orgIdList = lstOrgHierarchy.Select(o => o.org_id_4).Distinct().ToList();

                                    foreach (string orgId in orgIdList)
                                    {
                                        var orgHierarchy = lstOrgHierarchy.Where(x => x.org_id_4 == orgId).ToList();
                                        var orgList = orgHierarchy.Select(o => o.org_id_5).Distinct().ToList();
                                        orgList.Add(orgId);
                                        foreach (string org in orgList)
                                        {
                                            var assigDataToDelete = allAssignments.FirstOrDefault(x =>
                                                x.uniqueassignmentId == tcg.uniqueassignmentId && x.orgId == org);

                                            if (assigDataToDelete == null)
                                                continue;

                                            // Modified First from tbi_tasks
                                            var task = tenantDBContext.tbitasks.FirstOrDefault(x =>
                                                x.assignmentId == assigDataToDelete.assignmentId);
                                            if (task != null)
                                            {
                                                _unitOfWork.GenericRepo.Delete(task);
                                            }

                                            var goals = await _unitOfWork.BudgetProposalRepository.GetAssignmentGoals(
                                                userDetails.tenant_id, assigDataToDelete.assignmentId, budgetYear);
                                            var targets =
                                                await _unitOfWork.BudgetProposalRepository.GetAssignmentTargets(
                                                    userDetails.tenant_id, assigDataToDelete.assignmentId, budgetYear);
                                            var strategies =
                                                await _unitOfWork.BudgetProposalRepository.GetAssignmentStrategies(
                                                    userDetails.tenant_id, assigDataToDelete.assignmentId, budgetYear);

                                            //Delete goal, strategy and targets linked with assignment
                                            if (goals.Any())
                                            {
                                                _unitOfWork.GenericRepo.Delete(goals);
                                            }

                                            if (targets.Any())
                                            {
                                                _unitOfWork.GenericRepo.Delete(targets);
                                            }

                                            if (strategies.Any())
                                            {
                                                _unitOfWork.GenericRepo.Delete(strategies);
                                            }

                                            // Delete from tbi_assignments
                                            if (assigDataToDelete != null)
                                            {
                                                var descriptionsData =
                                                    await _unitOfWork.BudgetProposalRepository.GetAssignmentDescription(
                                                        userDetails.tenant_id, assigDataToDelete.assignmentId);
                                                if (descriptionsData != null)
                                                {
                                                    _unitOfWork.GenericRepo.Delete(descriptionsData);
                                                }
                                            }

                                            processedOrgs.Add(proIdCo);
                                        }
                                    }
                                }

                                // Modified First from tbi_tasks
                                var taskData =
                                    await _unitOfWork.BudgetProposalRepository.GetAssignmentTask(userDetails.tenant_id,
                                        budgetYear, tcg.assignmentId);
                                if (taskData != null)
                                {
                                    tenantDBContext.Entry(taskData).State = EntityState.Deleted;
                                    await tenantDBContext.SaveChangesAsync();
                                }

                                var goalsData =
                                    await _unitOfWork.BudgetProposalRepository.GetAssignmentGoals(userDetails.tenant_id,
                                        tcg.assignmentId, budgetYear);
                                var targetData =
                                    await _unitOfWork.BudgetProposalRepository.GetAssignmentTargets(
                                        userDetails.tenant_id, tcg.assignmentId, budgetYear);
                                var strategyData =
                                    await _unitOfWork.BudgetProposalRepository.GetAssignmentStrategies(
                                        userDetails.tenant_id, tcg.assignmentId, budgetYear);

                                //Delete goal, strategy and targets linked with assignment
                                if (goalsData.Any())
                                {
                                    foreach (var goal in goalsData)
                                    {
                                        tenantDBContext.Entry(goal).State = EntityState.Deleted;
                                    }

                                    await tenantDBContext.SaveChangesAsync();
                                }

                                if (targetData.Any())
                                {
                                    foreach (var target in targetData)
                                    {
                                        tenantDBContext.Entry(target).State = EntityState.Deleted;
                                    }

                                    await tenantDBContext.SaveChangesAsync();
                                }

                                if (strategyData.Any())
                                {
                                    foreach (var strategy in strategyData)
                                    {
                                        tenantDBContext.Entry(strategy).State = EntityState.Deleted;
                                    }

                                    await tenantDBContext.SaveChangesAsync();
                                }

                                // Delete from tbi_assignments
                                var assignmentDataToDelete =
                                    allAssignments.FirstOrDefault(x => x.assignmentId == tcg.assignmentId);
                                if (assignmentDataToDelete != null)
                                {
                                    var descriptionsData =
                                        await _unitOfWork.BudgetProposalRepository.GetAssignmentDescription(
                                            userDetails.tenant_id, tcg.assignmentId);
                                    if (descriptionsData != null)
                                    {
                                        tenantDBContext.tco_assignments_descriptions.Remove(descriptionsData);
                                        await tenantDBContext.SaveChangesAsync();
                                    }
                                }

                                tenantDBContext.tbiassignments.Remove(tcg);
                                await tenantDBContext.SaveChangesAsync();

                                processedOrgs.Add(proIdCo);
                            }
                        }
                    }
                }

                await _unitOfWork.CompleteAsync();

                //Removed description save from parent level on delegation - #163948

                //Save goal target and strategy data.
                AssignmentGoalTargetSectionHelper goalSecInput = new AssignmentGoalTargetSectionHelper()
                {
                    GoalIds = new List<Guid>(),
                    TargetIds = new List<Guid>(),
                    StrategyIds = new List<int>(),
                    AssignmentId = input.AssignmentId,
                    UniqueAssignmentId = input.UniqueAssignmentId,
                    SaveType = GoalSectionDataSaveType.GoalAndTarget
                };
                await SaveAssignmentGoalSectionInfo(userId, budgetYear, goalSecInput, delegatedAssignmentIds, true);
                return "success";
            }
            catch (Exception e)
            {
                return e.Message;
            }
        }

        public async Task SaveAssignmentGoalSectionInfo(string userId, int budgetYear,
            AssignmentGoalTargetSectionHelper input,
            List<Guid> delegetedAssignmentIds, bool isDelegationSave)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            BPAssignmentSaveHelper saveInput = new BPAssignmentSaveHelper();
            saveInput.budgetYear = budgetYear;
            var goalList = new List<Guid>();
            TbiAssignmentTarget targetToDelete = new TbiAssignmentTarget();
            List<TbiAssignmentTarget> targetsToDelete = new List<TbiAssignmentTarget>();
            var assignmentId = Guid.Empty;
            var goalId = Guid.Empty;
            bool notConnectedTarget = true;
            int count = 0;
            switch (input.SaveType)
            {
                case GoalSectionDataSaveType.Tag:
                    //Insert new tags and get the list to tag ids
                    List<string> lstTags = new List<string>();
                    if (input.ActionTags.Any())
                    {
                        lstTags = await InsertAssignmentActionTags(userId, input.ActionTags);
                    }

                    List<tbiassignments> assignments =
                        await _unitOfWork.BudgetProposalRepository.GetAllBudPropAssignment(userDetails.tenant_id,
                            budgetYear, input.UniqueAssignmentId);
                    foreach (var assignment in assignments)
                    {
                        assignment.tags = lstTags.Any() ? string.Join(",", lstTags) : string.Empty;
                        _unitOfWork.GenericRepo.Update(assignment);
                    }

                    await _unitOfWork.CompleteAsync();
                    break;

                case GoalSectionDataSaveType.GoalAndTarget:
                    await SaveAssignmentGoalTargetAndStrategies(userId, budgetYear, input, delegetedAssignmentIds,
                        isDelegationSave);
                    break;

                default: break;
            }

            if (input.TargetIds.Count > 0)
            {
                goalList = await tenantDbContext.tco_targets.Where(x => input.TargetIds.Contains(x.pk_target_id))
                    .Select(y => y.fk_goal_id).ToListAsync();
            }

            if (input.GoalIds.Count > 0 && input.TargetIds.Count > 0)
            {
                foreach (var goal in input.GoalIds)
                {
                    if (!goalList.Contains(goal))
                    {
                        notConnectedTarget = false;
                        goalId = goal;
                    }
                    else
                    {
                        count++;
                    }
                }
            }
            else if (input.GoalIds.Count < 0 && input.TargetIds.Count > 0)
            {
                foreach (var target in input.TargetIds)
                {
                    targetsToDelete.Add(
                        await tenantDbContext.TbiAssignmentTarget.FirstOrDefaultAsync(x => x.fk_target_id == target));
                }

                _unitOfWork.GenericRepo.Delete(targetsToDelete);
                await _unitOfWork.CompleteAsync();
            }

            if (!notConnectedTarget && count == 0)
            {
                assignmentId =
                    (await tenantDbContext.TbiAssignmentGoal.FirstOrDefaultAsync(x => x.fk_goal_id == goalId))
                    .fk_assignment_id;
                targetToDelete =
                    (await tenantDbContext.TbiAssignmentTarget.FirstOrDefaultAsync(x =>
                        x.fk_assignment_id == assignmentId)) ?? new TbiAssignmentTarget();
                _unitOfWork.GenericRepo.Delete(targetToDelete);
                await _unitOfWork.CompleteAsync();
            }
        }

        public List<EditorDescrionHelper> GetDescriptionsFromTopLevels(string userId, Guid uniqueAssignmentId,
            int budgetYear,
            string orgId, int orgLevel, string serviceId)
        {
            return GetDescriptionsFromTopLevelsAsync(userId, uniqueAssignmentId, budgetYear, orgId, orgLevel, serviceId)
                .GetAwaiter().GetResult();
        }

        public async Task<List<EditorDescrionHelper>> GetDescriptionsFromTopLevelsAsync(string userId,
            Guid uniqueAssignmentId,
            int budgetYear,
            string orgId, int orgLevel, string serviceId)
        {
            List<string> allServiceIds = new List<string>() { "ALL", "all", "-1", "" };
            serviceId = string.IsNullOrEmpty(serviceId) || allServiceIds.Contains(serviceId.ToLower())
                ? string.Empty
                : serviceId;
            var result = await _oldBudProp.GetDescriptionsforBudgetProposalAssignmentAsync(userId, uniqueAssignmentId,
                orgId, orgLevel, serviceId, budgetYear);
            result = result.Where(x => !x.IseditorEnabled).ToList();
            return result;
        }

        public async Task<AssignmentGoalTargetSectionHelper> GetAssignmentGoalSectionData(string userId, int budgetYear,
            Guid assignmentId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            tco_sync_company_setup subTenantCompanyData =
                await _unitOfWork.BudgetProposalRepository.GetMainTenantBasedonSubTenantAsync(userDetails.tenant_id);
            var orgHierarchy = new ClsOrgVersionSpecificContent();
            if (subTenantCompanyData != null)
            {
                orgHierarchy = _utility.GetOrgVersionBasedOnTenant(subTenantCompanyData.fk_tenant_id,
                    _utility.GetForecastPeriod(budgetYear, 1));
            }

            //Get action header info
            var assignmentInfo =
                await _unitOfWork.BudgetProposalRepository.GetBudPropAssignmentInfo(userDetails.tenant_id, budgetYear,
                    assignmentId);
            var goalsData =
                await _unitOfWork.BudgetProposalRepository.GetAssignmentGoals(userDetails.tenant_id, assignmentId,
                    budgetYear);
            var targetsData =
                await _unitOfWork.BudgetProposalRepository.GetAssignmentTargets(userDetails.tenant_id, assignmentId,
                    budgetYear);
            var strategyData =
                await _unitOfWork.BudgetProposalRepository.GetAssignmentStrategies(userDetails.tenant_id, assignmentId,
                    budgetYear);
            List<Guid> goalIds = goalsData.Any()
                ? goalsData.Select(x => x.fk_goal_id).Distinct().ToList()
                : new List<Guid>();
            List<Guid> targetIds = targetsData.Any()
                ? targetsData.Select(x => x.fk_target_id).Distinct().ToList()
                : new List<Guid>();
            List<int> strategyIds = strategyData.Any()
                ? strategyData.Select(x => x.fk_strategy_id).Distinct().ToList()
                : new List<int>();
            var goalInfo =
                await _unitOfWork.BudgetProposalRepository.GetGoalData(budgetYear, goalIds, userDetails.tenant_id);
            var targetInfo =
                await _unitOfWork.BudgetProposalRepository.GetTargetInfo(userDetails.tenant_id, budgetYear, targetIds);
            var strategyInfo =
                await _unitOfWork.BudgetProposalRepository.GetStrategyInfo(userDetails.tenant_id, budgetYear,
                    strategyIds);
            string goalName = "";
            string targetName = "";
            string strategyName = "";
            if (assignmentInfo != null)
            {
                var tagIds = !string.IsNullOrEmpty(assignmentInfo.tags)
                    ? assignmentInfo.tags.Split(',').Select(int.Parse).ToList()
                    : new List<int>();
                var actionTags =
                    await _unitOfWork.FinancialPlanOverviewRepository.GetActionTags(userDetails.tenant_id, tagIds);
                int active;
                var multiSelectInfo =
                    _utility.GetParameterValueAndActiveStatus(userId, "IS_MULTI_SELECT_GOALS", out active);
                bool isAssignmentTransfered =
                    await _unitOfWork.BudgetProposalRepository.IsAssignmentTransferred(userDetails.tenant_id,
                        assignmentId);

                var retData = new AssignmentGoalTargetSectionHelper()
                {
                    AssignmentId = assignmentInfo.assignmentId,
                    UniqueAssignmentId = assignmentInfo.uniqueassignmentId,
                    GoalIds = goalIds,
                    StrategyIds = strategyData.Any()
                        ? strategyData.Select(x => x.fk_strategy_id).Distinct().ToList()
                        : new List<int>(),
                    TargetIds = targetsData.Any()
                        ? targetsData.Select(x => x.fk_target_id).Distinct().ToList()
                        : new List<Guid>(),
                    ActionTags = actionTags != null && actionTags.Any() ? actionTags : new List<KeyValueData>(),
                    Tags = tagIds,
                    IsStrategyMultiSelect = !string.IsNullOrEmpty(multiSelectInfo) && active == 1 &&
                                            multiSelectInfo.ToLower() == "true"
                };
                if (isAssignmentTransfered && subTenantCompanyData != null)
                {
                    retData.IsTransferredAssignemnt = true;
                    retData.transferredFromMainTenantTag =
                        orgHierarchy.lstOrgDataLevel1 != null && orgHierarchy.lstOrgDataLevel1.Any() &&
                        orgHierarchy.lstOrgDataLevel1[0] != null
                            ? orgHierarchy.lstOrgDataLevel1[0].org_name_1
                            : String.Empty;
                }

                if (goalInfo.Any())
                {
                    List<string> goalNames = goalInfo.Select(x => x.value).Distinct().ToList();
                    goalName = "<p>" + string.Join("</p><p>", goalNames) + "</p>";
                }

                if (targetInfo.Any())
                {
                    List<string> targetNames = targetInfo.Select(x => x.target_name).ToList();
                    targetName = "<p>" + string.Join("</p><p>", targetNames) + "</p>";
                }

                if (strategyInfo.Any())
                {
                    List<string> strategyNames = strategyInfo.Select(x => x.strategy_name).ToList();
                    strategyName = "<p>" + string.Join("</p><p>", strategyNames) + "</p>";
                }

                retData.goalName = goalName;
                retData.targetName = targetName;
                retData.strategyName = strategyName;
                return retData;
            }

            return new AssignmentGoalTargetSectionHelper();
        }

        public async Task<PlanDescInfoHelper> GetPlanSectionInfo(string userId, int budgetYear, int actionId,
            ActionType rowType)
        {
            var planActions = await _oldBudProp.GetPlanActionsAsync(userId, budgetYear);
            var fkActionId = await ActionIdBasedOnType(userId, budgetYear, actionId, rowType);
            BudgetProposalPlanHelper actionFromPlan =
                planActions.FirstOrDefault(a => a.fk_action_id == Convert.ToInt32(fkActionId));
            var statusDescription = (actionFromPlan != null ? actionFromPlan.status_description : "");
            var StatusDescGuid =
                await _oldBudProp.GenerateGuidForPlanActionAsync(userId, Convert.ToInt32(fkActionId), budgetYear);
            var statusChecked = (actionFromPlan != null ? actionFromPlan.partial_flag : false);
            PlanDescInfoHelper PlanInfoObj = new PlanDescInfoHelper
            {
                descId = StatusDescGuid,
                desc = statusDescription,
                isChecked = statusChecked
            };
            return PlanInfoObj;
        }

        public async Task SavePlanSectionInfo(string userId, int budgetYear, planDescInfoHelper planDescInfo)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext budgetManagementDbContext = await _utility.GetTenantDBContextAsync();
            var actionId = await ActionIdBasedOnType(userId, budgetYear, planDescInfo.actionId, planDescInfo.rowType);
            var rowData = await budgetManagementDbContext.tpl_tfp_action_mapping.FirstOrDefaultAsync(x =>
                x.fk_tenant_id == userDetails.tenant_id
                && x.budget_year == budgetYear
                && x.fk_action_id == Convert.ToInt32(actionId));

            if (rowData != null)
            {
                rowData.partial_flag = planDescInfo.statusChecked;
                rowData.status_description = planDescInfo.description;
                rowData.updated = DateTime.UtcNow;
                rowData.updated_by = userDetails.pk_id;
            }

            await budgetManagementDbContext.SaveChangesAsync();
            if (planDescInfo.logHistory)
            {
                await _utility.SaveTextLogAsync(userId, planDescInfo.descId, planDescInfo.description);
            }
        }
    }
}