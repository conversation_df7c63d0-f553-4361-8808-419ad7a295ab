using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text;
using KeyValuePair = Framsikt.BL.Helpers.KeyValuePair;

namespace Framsikt.BL
{
    public partial class BudgetProposals
    {
        public async Task<bool> GetNonAllocatedActionStatus(string userId, int budgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var status =
                await _unitOfWork.BudgetProposalRepository.GetApplicationFlag(userDetails.tenant_id, nonAllocatedAction,
                    budgetYear);
            return status != null && status.flag_status == 1;
        }

        public async Task<ActionGridFormatData> GetBlistOrParkedActions(string userId, actionGridInputHelper input,
            bool isParkedAction)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langString =
                await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name,
                    "BudgetProposal");
            bool isTenantSyncSetup =
                await _dataSyncUtility.IsTenantSyncSetup(userDetails.tenant_id, userDetails.client_id);
            List<int> actionTypes = new() { 31, 41, 21 };
            await UpdateActionType(userDetails.tenant_id, input.budgetYear, actionTypes);
            TenantDBContext _parallelReadDbContext = await _utility.GetTenantDBContextAsync();
            int activechageId = -1;
            var activeBudgetRoundlist = await (from t in _parallelReadDbContext.tfp_budget_changes
                where t.fk_tenant_id == userDetails.tenant_id
                      && t.budget_year == input.budgetYear
                      && t.org_budget_flag == 1 && t.status == 1
                      && (t.workflow_status == 20 || t.workflow_status == 30 || t.workflow_status == 40)
                select t).AsNoTracking().ToListAsync();
            if (activeBudgetRoundlist.Any())
            {
                var action = activeBudgetRoundlist.FirstOrDefault();
                activechageId = action != null ? action.pk_change_id : activechageId;
            }

            var orgVersionContent =
                await _utility.GetOrgVersionSpecificContentAsync(userId,
                    _utility.GetForecastPeriod(input.budgetYear, 1));
            List<ActionGridColumns> data1Async = new();
            switch (input.orgLevel.Value)
            {
                case 1:
                    data1Async = await (from th in _parallelReadDbContext.tfp_temp_header
                        join td in _parallelReadDbContext.tfp_temp_detail on new
                                { a = th.fk_tenant_id, b = th.pk_temp_id }
                            equals new { a = td.fk_tenant_id, b = td.fk_temp_id }
                        join oh in _parallelReadDbContext.tco_org_hierarchy on new
                                { a = td.fk_tenant_id, b = td.department_code }
                            equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                        where th.fk_tenant_id == userDetails.tenant_id
                              && td.budget_year == input.budgetYear
                              && oh.fk_org_version == orgVersionContent.orgVersion
                              && actionTypes.Contains(th.action_type)
                              && th.is_parked_action == isParkedAction
                        group td by new
                        {
                            th.action_type,
                            th.pk_temp_id,
                            td.fk_account_code,
                            td.department_code,
                            td.function_code,
                            th.description,
                            th.line_order,
                            th.isManuallyAdded,
                            td.fk_change_id,
                            td.fk_alter_code,
                            td.pk_id,
                            th.updated,
                            th.updated_by,
                            th.priority,
                            td.project_code,
                            td.free_dim_1,
                            td.free_dim_2,
                            td.free_dim_3,
                            td.free_dim_4,
                            td.fk_adjustment_code,
                            th.tags,
                            th.goals_tags,
                            th.org_id,
                            th.org_level,
                            th.fk_action_id,
                            th.evaluation_status,
                            th.sync_status
                        }
                        into grp
                        select new ActionGridColumns
                        {
                            orgId = "",
                            orgName = "",
                            serviceId = "",
                            serviceName = "",
                            actionType = grp.Key.action_type,
                            actionId = grp.Key.pk_temp_id,
                            accountCode = grp.Key.fk_account_code,
                            departmentCode = grp.Key.department_code,
                            functionCode = grp.Key.function_code,
                            year1Amount = grp.Sum(x => x.year_1_amount),
                            year2Amount = grp.Sum(x => x.year_2_amount),
                            year3Amount = grp.Sum(x => x.year_3_amount),
                            year4Amount = grp.Sum(x => x.year_4_amount),
                            year5Amount = grp.Sum(x => x.year_5_amount.HasValue ? x.year_5_amount.Value : 0),
                            year6Amount = grp.Sum(x => x.year_6_amount),
                            year7Amount = grp.Sum(x => x.year_7_amount),
                            year8Amount = grp.Sum(x => x.year_8_amount),
                            year9Amount = grp.Sum(x => x.year_9_amount),
                            year10Amount = grp.Sum(x => x.year_10_amount),
                            description = grp.Key.description,
                            lineOrderId = grp.Key.line_order,
                            isManuallyAdded = grp.Key.isManuallyAdded,
                            changeId = grp.Key.fk_change_id,
                            alterCode = grp.Key.fk_alter_code,
                            tdId = grp.Key.pk_id,
                            tdUpdated = grp.Key.updated,
                            priority = grp.Key.priority ?? 0,
                            project = grp.Key.project_code,
                            freeDim1 = grp.Key.free_dim_1,
                            freeDim2 = grp.Key.free_dim_2,
                            freeDim3 = grp.Key.free_dim_3,
                            freeDim4 = grp.Key.free_dim_4,
                            adjustmentCode = grp.Key.fk_adjustment_code,
                            tags = grp.Key.tags,
                            goalTags = grp.Key.goals_tags,
                            orgIdCreatedAt = grp.Key.org_id,
                            orgLevelCreatedAt = grp.Key.org_level,
                            fkActionId = grp.Key.fk_action_id,
                            updatedBy = grp.Key.updated_by,
                            evaluationStatus = grp.Key.evaluation_status,
                            syncStatus = grp.Key.sync_status
                        }).ToListAsync();
                    break;

                case 2:
                    data1Async = await (from th in _parallelReadDbContext.tfp_temp_header
                        join td in _parallelReadDbContext.tfp_temp_detail on new
                                { a = th.fk_tenant_id, b = th.pk_temp_id }
                            equals new { a = td.fk_tenant_id, b = td.fk_temp_id }
                        join oh in _parallelReadDbContext.tco_org_hierarchy on new
                                { a = td.fk_tenant_id, b = td.department_code }
                            equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                        where th.fk_tenant_id == userDetails.tenant_id
                              && td.budget_year == input.budgetYear
                              && oh.fk_org_version == orgVersionContent.orgVersion
                              && oh.org_id_2 == input.orgId
                              && actionTypes.Contains(th.action_type)
                              && th.is_parked_action == isParkedAction
                        group td by new
                        {
                            th.action_type,
                            th.pk_temp_id,
                            td.fk_account_code,
                            td.department_code,
                            td.function_code,
                            th.description,
                            th.line_order,
                            th.isManuallyAdded,
                            td.fk_change_id,
                            td.fk_alter_code,
                            td.pk_id,
                            th.updated,
                            th.updated_by,
                            th.priority,
                            td.project_code,
                            td.free_dim_1,
                            td.free_dim_2,
                            td.free_dim_3,
                            td.free_dim_4,
                            td.fk_adjustment_code,
                            th.tags,
                            th.goals_tags,
                            th.org_id,
                            th.org_level,
                            th.fk_action_id,
                            th.evaluation_status,
                            th.sync_status,
                        }
                        into grp
                        select new ActionGridColumns
                        {
                            orgId = "",
                            orgName = "",
                            serviceId = "",
                            serviceName = "",
                            actionType = grp.Key.action_type,
                            actionId = grp.Key.pk_temp_id,
                            accountCode = grp.Key.fk_account_code,
                            departmentCode = grp.Key.department_code,
                            functionCode = grp.Key.function_code,
                            year1Amount = grp.Sum(x => x.year_1_amount),
                            year2Amount = grp.Sum(x => x.year_2_amount),
                            year3Amount = grp.Sum(x => x.year_3_amount),
                            year4Amount = grp.Sum(x => x.year_4_amount),
                            year5Amount = grp.Sum(x => x.year_5_amount.HasValue ? x.year_5_amount.Value : 0),
                            year6Amount = grp.Sum(x => x.year_6_amount),
                            year7Amount = grp.Sum(x => x.year_7_amount),
                            year8Amount = grp.Sum(x => x.year_8_amount),
                            year9Amount = grp.Sum(x => x.year_9_amount),
                            year10Amount = grp.Sum(x => x.year_10_amount),
                            description = grp.Key.description,
                            lineOrderId = grp.Key.line_order,
                            isManuallyAdded = grp.Key.isManuallyAdded,
                            changeId = grp.Key.fk_change_id,
                            alterCode = grp.Key.fk_alter_code,
                            tdId = grp.Key.pk_id,
                            tdUpdated = grp.Key.updated,
                            priority = grp.Key.priority ?? 0,
                            project = grp.Key.project_code,
                            freeDim1 = grp.Key.free_dim_1,
                            freeDim2 = grp.Key.free_dim_2,
                            freeDim3 = grp.Key.free_dim_3,
                            freeDim4 = grp.Key.free_dim_4,
                            adjustmentCode = grp.Key.fk_adjustment_code,
                            tags = grp.Key.tags,
                            goalTags = grp.Key.goals_tags,
                            orgIdCreatedAt = grp.Key.org_id,
                            orgLevelCreatedAt = grp.Key.org_level,
                            fkActionId = grp.Key.fk_action_id,
                            updatedBy = grp.Key.updated_by,
                            evaluationStatus = grp.Key.evaluation_status,
                            syncStatus = grp.Key.sync_status,
                        }).ToListAsync();
                    break;

                case 3:
                    data1Async = await (from th in _parallelReadDbContext.tfp_temp_header
                        join td in _parallelReadDbContext.tfp_temp_detail on new
                                { a = th.fk_tenant_id, b = th.pk_temp_id }
                            equals new { a = td.fk_tenant_id, b = td.fk_temp_id }
                        join oh in _parallelReadDbContext.tco_org_hierarchy on new
                                { a = td.fk_tenant_id, b = td.department_code }
                            equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                        where th.fk_tenant_id == userDetails.tenant_id
                              && td.budget_year == input.budgetYear
                              && oh.fk_org_version == orgVersionContent.orgVersion
                              && oh.org_id_3 == input.orgId
                              && actionTypes.Contains(th.action_type)
                              && th.is_parked_action == isParkedAction
                        group td by new
                        {
                            th.action_type,
                            th.pk_temp_id,
                            td.fk_account_code,
                            td.department_code,
                            td.function_code,
                            th.description,
                            th.line_order,
                            th.isManuallyAdded,
                            td.fk_change_id,
                            td.fk_alter_code,
                            td.pk_id,
                            th.updated,
                            th.updated_by,
                            th.priority,
                            td.project_code,
                            td.free_dim_1,
                            td.free_dim_2,
                            td.free_dim_3,
                            td.free_dim_4,
                            td.fk_adjustment_code,
                            th.tags,
                            th.goals_tags,
                            th.org_id,
                            th.org_level,
                            th.fk_action_id,
                            th.evaluation_status,
                            th.sync_status,
                        }
                        into grp
                        select new ActionGridColumns
                        {
                            orgId = "",
                            orgName = "",
                            serviceId = "",
                            serviceName = "",
                            actionType = grp.Key.action_type,
                            actionId = grp.Key.pk_temp_id,
                            accountCode = grp.Key.fk_account_code,
                            departmentCode = grp.Key.department_code,
                            functionCode = grp.Key.function_code,
                            year1Amount = grp.Sum(x => x.year_1_amount),
                            year2Amount = grp.Sum(x => x.year_2_amount),
                            year3Amount = grp.Sum(x => x.year_3_amount),
                            year4Amount = grp.Sum(x => x.year_4_amount),
                            year5Amount = grp.Sum(x => x.year_5_amount.HasValue ? x.year_5_amount.Value : 0),
                            year6Amount = grp.Sum(x => x.year_6_amount),
                            year7Amount = grp.Sum(x => x.year_7_amount),
                            year8Amount = grp.Sum(x => x.year_8_amount),
                            year9Amount = grp.Sum(x => x.year_9_amount),
                            year10Amount = grp.Sum(x => x.year_10_amount),
                            description = grp.Key.description,
                            lineOrderId = grp.Key.line_order,
                            isManuallyAdded = grp.Key.isManuallyAdded,
                            changeId = grp.Key.fk_change_id,
                            alterCode = grp.Key.fk_alter_code,
                            tdId = grp.Key.pk_id,
                            tdUpdated = grp.Key.updated,
                            priority = grp.Key.priority ?? 0,
                            project = grp.Key.project_code,
                            freeDim1 = grp.Key.free_dim_1,
                            freeDim2 = grp.Key.free_dim_2,
                            freeDim3 = grp.Key.free_dim_3,
                            freeDim4 = grp.Key.free_dim_4,
                            adjustmentCode = grp.Key.fk_adjustment_code,
                            tags = grp.Key.tags,
                            goalTags = grp.Key.goals_tags,
                            orgIdCreatedAt = grp.Key.org_id,
                            orgLevelCreatedAt = grp.Key.org_level,
                            fkActionId = grp.Key.fk_action_id,
                            updatedBy = grp.Key.updated_by,
                            evaluationStatus = grp.Key.evaluation_status,
                            syncStatus = grp.Key.sync_status,
                        }).ToListAsync();
                    break;

                case 4:
                    data1Async = await (from th in _parallelReadDbContext.tfp_temp_header
                        join td in _parallelReadDbContext.tfp_temp_detail on new
                                { a = th.fk_tenant_id, b = th.pk_temp_id }
                            equals new { a = td.fk_tenant_id, b = td.fk_temp_id }
                        join oh in _parallelReadDbContext.tco_org_hierarchy on new
                                { a = td.fk_tenant_id, b = td.department_code }
                            equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                        where th.fk_tenant_id == userDetails.tenant_id
                              && td.budget_year == input.budgetYear
                              && oh.fk_org_version == orgVersionContent.orgVersion
                              && oh.org_id_4 == input.orgId
                              && actionTypes.Contains(th.action_type)
                              && th.is_parked_action == isParkedAction
                        group td by new
                        {
                            th.action_type,
                            th.pk_temp_id,
                            td.fk_account_code,
                            td.department_code,
                            td.function_code,
                            th.description,
                            th.line_order,
                            th.isManuallyAdded,
                            td.fk_change_id,
                            td.fk_alter_code,
                            td.pk_id,
                            th.updated,
                            th.updated_by,
                            th.priority,
                            td.project_code,
                            td.free_dim_1,
                            td.free_dim_2,
                            td.free_dim_3,
                            td.free_dim_4,
                            td.fk_adjustment_code,
                            th.tags,
                            th.goals_tags,
                            th.org_id,
                            th.org_level,
                            th.fk_action_id,
                            th.evaluation_status,
                            th.sync_status,
                        }
                        into grp
                        select new ActionGridColumns
                        {
                            orgId = "",
                            orgName = "",
                            serviceId = "",
                            serviceName = "",
                            actionType = grp.Key.action_type,
                            actionId = grp.Key.pk_temp_id,
                            accountCode = grp.Key.fk_account_code,
                            departmentCode = grp.Key.department_code,
                            functionCode = grp.Key.function_code,
                            year1Amount = grp.Sum(x => x.year_1_amount),
                            year2Amount = grp.Sum(x => x.year_2_amount),
                            year3Amount = grp.Sum(x => x.year_3_amount),
                            year4Amount = grp.Sum(x => x.year_4_amount),
                            year5Amount = grp.Sum(x => x.year_5_amount.HasValue ? x.year_5_amount.Value : 0),
                            year6Amount = grp.Sum(x => x.year_6_amount),
                            year7Amount = grp.Sum(x => x.year_7_amount),
                            year8Amount = grp.Sum(x => x.year_8_amount),
                            year9Amount = grp.Sum(x => x.year_9_amount),
                            year10Amount = grp.Sum(x => x.year_10_amount),
                            description = grp.Key.description,
                            lineOrderId = grp.Key.line_order,
                            isManuallyAdded = grp.Key.isManuallyAdded,
                            changeId = grp.Key.fk_change_id,
                            alterCode = grp.Key.fk_alter_code,
                            tdId = grp.Key.pk_id,
                            tdUpdated = grp.Key.updated,
                            priority = grp.Key.priority ?? 0,
                            project = grp.Key.project_code,
                            freeDim1 = grp.Key.free_dim_1,
                            freeDim2 = grp.Key.free_dim_2,
                            freeDim3 = grp.Key.free_dim_3,
                            freeDim4 = grp.Key.free_dim_4,
                            adjustmentCode = grp.Key.fk_adjustment_code,
                            tags = grp.Key.tags,
                            goalTags = grp.Key.goals_tags,
                            orgIdCreatedAt = grp.Key.org_id,
                            orgLevelCreatedAt = grp.Key.org_level,
                            fkActionId = grp.Key.fk_action_id,
                            updatedBy = grp.Key.updated_by,
                            evaluationStatus = grp.Key.evaluation_status,
                            syncStatus = grp.Key.sync_status,
                        }).ToListAsync();
                    break;

                case 5:
                    data1Async = await (from th in _parallelReadDbContext.tfp_temp_header
                        join td in _parallelReadDbContext.tfp_temp_detail on new
                                { a = th.fk_tenant_id, b = th.pk_temp_id }
                            equals new { a = td.fk_tenant_id, b = td.fk_temp_id }
                        join oh in _parallelReadDbContext.tco_org_hierarchy on new
                                { a = td.fk_tenant_id, b = td.department_code }
                            equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                        join tu in _parallelReadDbContext.vw_distinct_userDetails on th.updated_by equals tu.pk_id into
                            ret
                        from r in ret.DefaultIfEmpty()
                        where th.fk_tenant_id == userDetails.tenant_id
                              && td.budget_year == input.budgetYear
                              && oh.fk_org_version == orgVersionContent.orgVersion
                              && oh.org_id_5 == input.orgId
                              && actionTypes.Contains(th.action_type)
                              && th.is_parked_action == isParkedAction
                        group td by new
                        {
                            th.action_type,
                            th.pk_temp_id,
                            td.fk_account_code,
                            td.department_code,
                            td.function_code,
                            th.description,
                            th.line_order,
                            th.isManuallyAdded,
                            td.fk_change_id,
                            td.fk_alter_code,
                            td.pk_id,
                            th.updated,
                            th.updated_by,
                            th.priority,
                            td.project_code,
                            td.free_dim_1,
                            td.free_dim_2,
                            td.free_dim_3,
                            td.free_dim_4,
                            td.fk_adjustment_code,
                            th.tags,
                            th.goals_tags,
                            th.org_id,
                            th.org_level,
                            th.fk_action_id,
                            th.evaluation_status,
                            th.sync_status,
                        }
                        into grp
                        select new ActionGridColumns
                        {
                            orgId = "",
                            orgName = "",
                            serviceId = "",
                            serviceName = "",
                            actionType = grp.Key.action_type,
                            actionId = grp.Key.pk_temp_id,
                            accountCode = grp.Key.fk_account_code,
                            departmentCode = grp.Key.department_code,
                            functionCode = grp.Key.function_code,
                            year1Amount = grp.Sum(x => x.year_1_amount),
                            year2Amount = grp.Sum(x => x.year_2_amount),
                            year3Amount = grp.Sum(x => x.year_3_amount),
                            year4Amount = grp.Sum(x => x.year_4_amount),
                            year5Amount = grp.Sum(x => x.year_5_amount.HasValue ? x.year_5_amount.Value : 0),
                            year6Amount = grp.Sum(x => x.year_6_amount),
                            year7Amount = grp.Sum(x => x.year_7_amount),
                            year8Amount = grp.Sum(x => x.year_8_amount),
                            year9Amount = grp.Sum(x => x.year_9_amount),
                            year10Amount = grp.Sum(x => x.year_10_amount),
                            description = grp.Key.description,
                            lineOrderId = grp.Key.line_order,
                            isManuallyAdded = grp.Key.isManuallyAdded,
                            changeId = grp.Key.fk_change_id,
                            alterCode = grp.Key.fk_alter_code,
                            tdId = grp.Key.pk_id,
                            tdUpdated = grp.Key.updated,
                            priority = grp.Key.priority ?? 0,
                            project = grp.Key.project_code,
                            freeDim1 = grp.Key.free_dim_1,
                            freeDim2 = grp.Key.free_dim_2,
                            freeDim3 = grp.Key.free_dim_3,
                            freeDim4 = grp.Key.free_dim_4,
                            adjustmentCode = grp.Key.fk_adjustment_code,
                            tags = grp.Key.tags,
                            goalTags = grp.Key.goals_tags,
                            orgIdCreatedAt = grp.Key.org_id,
                            orgLevelCreatedAt = grp.Key.org_level,
                            fkActionId = grp.Key.fk_action_id,
                            updatedBy = grp.Key.updated_by,
                            evaluationStatus = grp.Key.evaluation_status,
                            syncStatus = grp.Key.sync_status,
                        }).ToListAsync();
                    break;

                case 6:
                    data1Async = await (from th in _parallelReadDbContext.tfp_temp_header
                        join td in _parallelReadDbContext.tfp_temp_detail on new
                                { a = th.fk_tenant_id, b = th.pk_temp_id }
                            equals new { a = td.fk_tenant_id, b = td.fk_temp_id }
                        join oh in _parallelReadDbContext.tco_org_hierarchy on new
                                { a = td.fk_tenant_id, b = td.department_code }
                            equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                        where th.fk_tenant_id == userDetails.tenant_id
                              && td.budget_year == input.budgetYear
                              && oh.fk_org_version == orgVersionContent.orgVersion
                              && oh.org_id_6 == input.orgId
                              && actionTypes.Contains(th.action_type)
                              && th.is_parked_action == isParkedAction
                        group td by new
                        {
                            th.action_type,
                            th.pk_temp_id,
                            td.fk_account_code,
                            td.department_code,
                            td.function_code,
                            th.description,
                            th.line_order,
                            th.isManuallyAdded,
                            td.fk_change_id,
                            td.fk_alter_code,
                            td.pk_id,
                            th.updated,
                            th.updated_by,
                            th.priority,
                            td.project_code,
                            td.free_dim_1,
                            td.free_dim_2,
                            td.free_dim_3,
                            td.free_dim_4,
                            td.fk_adjustment_code,
                            th.tags,
                            th.goals_tags,
                            th.org_id,
                            th.org_level,
                            th.fk_action_id,
                            th.evaluation_status,
                            th.sync_status,
                        }
                        into grp
                        select new ActionGridColumns
                        {
                            orgId = "",
                            orgName = "",
                            serviceId = "",
                            serviceName = "",
                            actionType = grp.Key.action_type,
                            actionId = grp.Key.pk_temp_id,
                            accountCode = grp.Key.fk_account_code,
                            departmentCode = grp.Key.department_code,
                            functionCode = grp.Key.function_code,
                            year1Amount = grp.Sum(x => x.year_1_amount),
                            year2Amount = grp.Sum(x => x.year_2_amount),
                            year3Amount = grp.Sum(x => x.year_3_amount),
                            year4Amount = grp.Sum(x => x.year_4_amount),
                            year5Amount = grp.Sum(x => x.year_5_amount.HasValue ? x.year_5_amount.Value : 0),
                            year6Amount = grp.Sum(x => x.year_6_amount),
                            year7Amount = grp.Sum(x => x.year_7_amount),
                            year8Amount = grp.Sum(x => x.year_8_amount),
                            year9Amount = grp.Sum(x => x.year_9_amount),
                            year10Amount = grp.Sum(x => x.year_10_amount),
                            description = grp.Key.description,
                            lineOrderId = grp.Key.line_order,
                            isManuallyAdded = grp.Key.isManuallyAdded,
                            changeId = grp.Key.fk_change_id,
                            alterCode = grp.Key.fk_alter_code,
                            tdId = grp.Key.pk_id,
                            tdUpdated = grp.Key.updated,
                            priority = grp.Key.priority ?? 0,
                            project = grp.Key.project_code,
                            freeDim1 = grp.Key.free_dim_1,
                            freeDim2 = grp.Key.free_dim_2,
                            freeDim3 = grp.Key.free_dim_3,
                            freeDim4 = grp.Key.free_dim_4,
                            adjustmentCode = grp.Key.fk_adjustment_code,
                            tags = grp.Key.tags,
                            goalTags = grp.Key.goals_tags,
                            orgIdCreatedAt = grp.Key.org_id,
                            orgLevelCreatedAt = grp.Key.org_level,
                            fkActionId = grp.Key.fk_action_id,
                            updatedBy = grp.Key.updated_by,
                            evaluationStatus = grp.Key.evaluation_status,
                            syncStatus = grp.Key.sync_status,
                        }).ToListAsync();
                    break;

                case 7:
                    data1Async = await (from th in _parallelReadDbContext.tfp_temp_header
                        join td in _parallelReadDbContext.tfp_temp_detail on new
                                { a = th.fk_tenant_id, b = th.pk_temp_id }
                            equals new { a = td.fk_tenant_id, b = td.fk_temp_id }
                        join oh in _parallelReadDbContext.tco_org_hierarchy on new
                                { a = td.fk_tenant_id, b = td.department_code }
                            equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                        where th.fk_tenant_id == userDetails.tenant_id
                              && td.budget_year == input.budgetYear
                              && oh.fk_org_version == orgVersionContent.orgVersion
                              && oh.org_id_7 == input.orgId
                              && actionTypes.Contains(th.action_type)
                              && th.is_parked_action == isParkedAction
                        group td by new
                        {
                            th.action_type,
                            th.pk_temp_id,
                            td.fk_account_code,
                            td.department_code,
                            td.function_code,
                            th.description,
                            th.line_order,
                            th.isManuallyAdded,
                            td.fk_change_id,
                            td.fk_alter_code,
                            td.pk_id,
                            th.updated,
                            th.updated_by,
                            th.priority,
                            td.project_code,
                            td.free_dim_1,
                            td.free_dim_2,
                            td.free_dim_3,
                            td.free_dim_4,
                            td.fk_adjustment_code,
                            th.tags,
                            th.goals_tags,
                            th.org_id,
                            th.org_level,
                            th.fk_action_id,
                            th.evaluation_status,
                            th.sync_status,
                        }
                        into grp
                        select new ActionGridColumns
                        {
                            orgId = "",
                            orgName = "",
                            serviceId = "",
                            serviceName = "",
                            actionType = grp.Key.action_type,
                            actionId = grp.Key.pk_temp_id,
                            accountCode = grp.Key.fk_account_code,
                            departmentCode = grp.Key.department_code,
                            functionCode = grp.Key.function_code,
                            year1Amount = grp.Sum(x => x.year_1_amount),
                            year2Amount = grp.Sum(x => x.year_2_amount),
                            year3Amount = grp.Sum(x => x.year_3_amount),
                            year4Amount = grp.Sum(x => x.year_4_amount),
                            year5Amount = grp.Sum(x => x.year_5_amount.HasValue ? x.year_5_amount.Value : 0),
                            year6Amount = grp.Sum(x => x.year_6_amount),
                            year7Amount = grp.Sum(x => x.year_7_amount),
                            year8Amount = grp.Sum(x => x.year_8_amount),
                            year9Amount = grp.Sum(x => x.year_9_amount),
                            year10Amount = grp.Sum(x => x.year_10_amount),
                            description = grp.Key.description,
                            lineOrderId = grp.Key.line_order,
                            isManuallyAdded = grp.Key.isManuallyAdded,
                            changeId = grp.Key.fk_change_id,
                            alterCode = grp.Key.fk_alter_code,
                            tdId = grp.Key.pk_id,
                            tdUpdated = grp.Key.updated,
                            priority = grp.Key.priority ?? 0,
                            project = grp.Key.project_code,
                            freeDim1 = grp.Key.free_dim_1,
                            freeDim2 = grp.Key.free_dim_2,
                            freeDim3 = grp.Key.free_dim_3,
                            freeDim4 = grp.Key.free_dim_4,
                            adjustmentCode = grp.Key.fk_adjustment_code,
                            tags = grp.Key.tags,
                            goalTags = grp.Key.goals_tags,
                            orgIdCreatedAt = grp.Key.org_id,
                            orgLevelCreatedAt = grp.Key.org_level,
                            fkActionId = grp.Key.fk_action_id,
                            updatedBy = grp.Key.updated_by,
                            evaluationStatus = grp.Key.evaluation_status,
                            syncStatus = grp.Key.sync_status,
                        }).ToListAsync();
                    break;

                case 8:
                    data1Async = await (from th in _parallelReadDbContext.tfp_temp_header
                        join td in _parallelReadDbContext.tfp_temp_detail on new
                                { a = th.fk_tenant_id, b = th.pk_temp_id }
                            equals new { a = td.fk_tenant_id, b = td.fk_temp_id }
                        join oh in _parallelReadDbContext.tco_org_hierarchy on new
                                { a = td.fk_tenant_id, b = td.department_code }
                            equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                        where th.fk_tenant_id == userDetails.tenant_id
                              && td.budget_year == input.budgetYear
                              && oh.fk_org_version == orgVersionContent.orgVersion
                              && oh.org_id_8 == input.orgId
                              && actionTypes.Contains(th.action_type)
                              && th.is_parked_action == isParkedAction
                        group td by new
                        {
                            th.action_type,
                            th.pk_temp_id,
                            td.fk_account_code,
                            td.department_code,
                            td.function_code,
                            th.description,
                            th.line_order,
                            th.isManuallyAdded,
                            td.fk_change_id,
                            td.fk_alter_code,
                            td.pk_id,
                            th.updated,
                            th.updated_by,
                            th.priority,
                            td.project_code,
                            td.free_dim_1,
                            td.free_dim_2,
                            td.free_dim_3,
                            td.free_dim_4,
                            td.fk_adjustment_code,
                            th.tags,
                            th.goals_tags,
                            th.org_id,
                            th.org_level,
                            th.fk_action_id,
                            th.evaluation_status,
                            th.sync_status,
                        }
                        into grp
                        select new ActionGridColumns
                        {
                            orgId = "",
                            orgName = "",
                            serviceId = "",
                            serviceName = "",
                            actionType = grp.Key.action_type,
                            actionId = grp.Key.pk_temp_id,
                            accountCode = grp.Key.fk_account_code,
                            departmentCode = grp.Key.department_code,
                            functionCode = grp.Key.function_code,
                            year1Amount = grp.Sum(x => x.year_1_amount),
                            year2Amount = grp.Sum(x => x.year_2_amount),
                            year3Amount = grp.Sum(x => x.year_3_amount),
                            year4Amount = grp.Sum(x => x.year_4_amount),
                            year5Amount = grp.Sum(x => x.year_5_amount.HasValue ? x.year_5_amount.Value : 0),
                            year6Amount = grp.Sum(x => x.year_6_amount),
                            year7Amount = grp.Sum(x => x.year_7_amount),
                            year8Amount = grp.Sum(x => x.year_8_amount),
                            year9Amount = grp.Sum(x => x.year_9_amount),
                            year10Amount = grp.Sum(x => x.year_10_amount),
                            description = grp.Key.description,
                            lineOrderId = grp.Key.line_order,
                            isManuallyAdded = grp.Key.isManuallyAdded,
                            changeId = grp.Key.fk_change_id,
                            alterCode = grp.Key.fk_alter_code,
                            tdId = grp.Key.pk_id,
                            tdUpdated = grp.Key.updated,
                            priority = grp.Key.priority ?? 0,
                            project = grp.Key.project_code,
                            freeDim1 = grp.Key.free_dim_1,
                            freeDim2 = grp.Key.free_dim_2,
                            freeDim3 = grp.Key.free_dim_3,
                            freeDim4 = grp.Key.free_dim_4,
                            adjustmentCode = grp.Key.fk_adjustment_code,
                            tags = grp.Key.tags,
                            goalTags = grp.Key.goals_tags,
                            orgIdCreatedAt = grp.Key.org_id,
                            orgLevelCreatedAt = grp.Key.org_level,
                            fkActionId = grp.Key.fk_action_id,
                            updatedBy = grp.Key.updated_by,
                            evaluationStatus = grp.Key.evaluation_status,
                            syncStatus = grp.Key.sync_status,
                        }).ToListAsync();
                    break;
            }

            var datsetAsync = await (from bc in _parallelReadDbContext.tfp_budget_changes
                where bc.fk_tenant_id == userDetails.tenant_id && bc.budget_year == input.budgetYear &&
                      bc.org_budget_flag == 1
                select bc).ToListAsync();

            var datsetAsync1 = await (from bc in _parallelReadDbContext.tco_fp_alter_codes
                where bc.fk_tenant_id == userDetails.tenant_id
                select bc).ToListAsync();

            var datsetAsync3 = await (from bc in _parallelReadDbContext.tco_budget_phase
                where bc.fk_tenant_id == userDetails.tenant_id && bc.org_budget_flag == 1
                select bc).ToListAsync();
            List<UserInformation> dataset2 = new();
            int clientId = userDetails.client_id;
            string result = await _cache.GetStringForTenantAsync(clientId, userDetails.tenant_id,
                "tenantUserDetails_" + userDetails.tenant_id.ToString());
            if (!string.IsNullOrEmpty(result))
            {
                dataset2 = JsonConvert.DeserializeObject<List<UserInformation>>(result);
            }

            var dataset = datsetAsync;
            var dataset1 = datsetAsync1;
            var dataset3 = datsetAsync3;

            var dd = (from th in data1Async
                join bc in dataset on th.changeId equals bc.pk_change_id
                join bp in dataset3 on bc.fk_budget_phase_id equals bp.pk_budget_phase_id
                join ac in dataset1 on th.alterCode equals ac.pk_alter_code
                join tu in dataset2 on th.updatedBy equals tu.pk_id into ret
                from r in ret.DefaultIfEmpty()
                group th by new
                {
                    th.actionType,
                    th.actionId,
                    th.orgIdCreatedAt,
                    th.orgLevelCreatedAt,
                    th.accountCode,
                    th.departmentCode,
                    th.functionCode,
                    th.description,
                    th.lineOrderId,
                    th.isManuallyAdded,
                    th.changeId,
                    th.alterCode,
                    firstName = r == null ? String.Empty : r.first_name,
                    lastName = r == null ? String.Empty : r.last_name,
                    th.tdUpdated,
                    th.project,
                    th.freeDim1,
                    th.freeDim2,
                    th.freeDim3,
                    th.freeDim4,
                    th.adjustmentCode,
                    th.tdUpdatedBy,
                    th.priority,
                    th.tags,
                    th.goalTags,
                    bp.pk_budget_phase_id,
                    ac.limit_code,
                    th.tdId,
                    th.fkActionId,
                    th.evaluationStatus,
                    th.syncStatus,
                }
                into grp
                select new ActionGridColumns
                {
                    orgId = "",
                    orgName = "",
                    serviceId = "",
                    serviceName = "",
                    actionType = grp.Key.actionType,
                    actionId = grp.Key.actionId,
                    accountCode = grp.Key.accountCode,
                    departmentCode = grp.Key.departmentCode,
                    functionCode = grp.Key.functionCode,
                    year1Amount = grp.Sum(x => x.year1Amount),
                    year2Amount = grp.Sum(x => x.year2Amount),
                    year3Amount = grp.Sum(x => x.year3Amount),
                    year4Amount = grp.Sum(x => x.year4Amount),
                    year5Amount = grp.Sum(x => x.year5Amount),
                    year6Amount = grp.Sum(x => x.year6Amount),
                    year7Amount = grp.Sum(x => x.year7Amount),
                    year8Amount = grp.Sum(x => x.year8Amount),
                    year9Amount = grp.Sum(x => x.year9Amount),
                    year10Amount = grp.Sum(x => x.year10Amount),
                    description = grp.Key.description,
                    lineOrderId = grp.Key.lineOrderId,
                    isManuallyAdded = grp.Key.isManuallyAdded,
                    changeId = grp.Key.changeId,
                    alterCode = grp.Key.alterCode,
                    tdId = grp.Key.tdId,
                    tdUpdated = grp.Key.tdUpdated,
                    priority = grp.Key.priority,
                    tdUpdatedBy = string.IsNullOrEmpty(grp.Key.firstName)
                        ? string.Empty
                        : grp.Key.firstName + " " + grp.Key.lastName,
                    project = grp.Key.project,
                    freeDim1 = grp.Key.freeDim1,
                    freeDim2 = grp.Key.freeDim2,
                    freeDim3 = grp.Key.freeDim3,
                    freeDim4 = grp.Key.freeDim4,
                    adjustmentCode = grp.Key.adjustmentCode,
                    tags = grp.Key.tags,
                    goalTags = grp.Key.goalTags,
                    budgetPhase = grp.Key.pk_budget_phase_id,
                    limitCode = grp.Key.limit_code,
                    orgIdCreatedAt = grp.Key.orgIdCreatedAt,
                    orgLevelCreatedAt = grp.Key.orgLevelCreatedAt,
                    fkActionId = grp.Key.fkActionId,
                    evaluationStatus = grp.Key.evaluationStatus,
                    syncStatus = grp.Key.syncStatus,
                }).ToList();

            var departFunctFilterData = await GetDepartmentFuncFilterData(userId, input);

            //Get Hidden departments
            string changeId =
                (await _finUtility.GetActiveWorkFlowChangeIdAsync(userDetails.user_name, input.budgetYear)).ToString();
            Dictionary<string, List<string>> departmentsToHide =
                await _finUtility.GetDepartmentsToHideAsync(userDetails.user_name, input.budgetYear,
                    input.orgLevel.Value, input.orgId, input.serviceId, changeId);
            if (departmentsToHide.First(x => x.Key == "blistDepartments").Value.Any() && !isParkedAction)
            {
                var departments = departmentsToHide.First(x => x.Key == "blistDepartments").Value;
                dd = dd.Where(x => !departments.Contains(x.departmentCode)).ToList();
            }

            if (departmentsToHide.First(x => x.Key == "allDepartments").Value.Any() && isParkedAction)
            {
                var department = departmentsToHide.First(x => x.Key == "allDepartments").Value;
                dd = dd.Where(x => !department.Contains(x.departmentCode)).ToList();
            }

            // filter data with chapterId
            bool isChapterSetup = await _finUtility.isChapterSetup(userId);
            if (isChapterSetup && input.orgLevel == 1)
            {
                input.chapterId =
                    string.IsNullOrEmpty(input.chapterId) || input.chapterId.Trim().ToLower() == "ALL".ToLower()
                        ? string.Empty
                        : input.chapterId;
                int orgLevel = input.orgLevel != null ? (int)input.orgLevel : 0;
                var relationDepartments =
                    await _finUtility.GetAttributeConnectedDepartmentsForSelectedOrgAsync(userId, input.budgetYear,
                        input.orgId, orgLevel, input.chapterId);
                if (relationDepartments.Any(x => !string.IsNullOrEmpty(x)))
                {
                    dd = dd.Where(y => relationDepartments.Contains(y.departmentCode)).ToList();
                }
            }

            if (departFunctFilterData.functions.Any(x => !string.IsNullOrEmpty(x)))
            {
                dd = dd.Where(y => departFunctFilterData.functions.Contains(y.functionCode)).ToList();
            }

            //dd = await data.AsNoTracking().ToListAsync();
            if (input.filters.Any())
            {
                dd = GetFilteredDataSet(userId, dd, input.filters, input.orgLevel.Value, input.orgId, input.budgetYear);
            }

            List<clsAggregates> lstAggregates = GetGroupedDataSet(userId, dd, input, activechageId, input.ColToDisplay,
                isTenantSyncSetup, !isParkedAction);
            ActionGridFormatData finalDataSet = new();
            if (isParkedAction)
            {
                finalDataSet = await FormatActionGridData(userDetails.user_name,
                    langString.FirstOrDefault(v => v.Key == "BudProp_tableType_parkedBlist").Value.LangText, "TYPE-PA",
                    lstAggregates, input.budgetYear, input.filters);
            }
            else
            {
                finalDataSet = await FormatActionGridData(userDetails.user_name,
                    langString.FirstOrDefault(v => v.Key == "BP_GridColumn_type2").Value.LangText, "TYPE-BL",
                    lstAggregates, input.budgetYear, input.filters);
            }

            return finalDataSet;
        }

        public async Task<ActionGridFormatData> GetDeletedActions(string userId, actionGridInputHelper input)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langString =
                await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name,
                    "BudgetProposal");
            List<int> actionTypes = new() { 31, 41, 21 };
            await UpdateActionType(userDetails.tenant_id, input.budgetYear, actionTypes);
            TenantDBContext _parallelReadDbContext = await _utility.GetTenantDBContextAsync();
            _parallelReadDbContext.Database.SetCommandTimeout(800);
            int activechageId = -1;
            var activeBudgetRoundlist = await (from t in _parallelReadDbContext.tfp_budget_changes
                where t.fk_tenant_id == userDetails.tenant_id
                      && t.budget_year == input.budgetYear
                      && t.org_budget_flag == 1 && t.status == 1
                      && (t.workflow_status == 20 || t.workflow_status == 30 || t.workflow_status == 40)
                select t).AsNoTracking().ToListAsync();
            if (activeBudgetRoundlist.Any())
            {
                var action = activeBudgetRoundlist.FirstOrDefault();
                activechageId = action != null ? action.pk_change_id : activechageId;
            }

            var orgVersionContent =
                await _utility.GetOrgVersionSpecificContentAsync(userId,
                    _utility.GetForecastPeriod(input.budgetYear, 1));
            List<ActionGridColumns> data1Async = new();
            switch (input.orgLevel.Value)
            {
                case 1:
                    data1Async = await (from th in _parallelReadDbContext.tfp_delete_header
                        join td in _parallelReadDbContext.tfp_delete_detail on new
                                { a = th.fk_tenant_id, b = th.pk_action_id }
                            equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                        join oh in _parallelReadDbContext.tco_org_hierarchy on new
                                { a = td.fk_tenant_id, b = td.department_code }
                            equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                        where th.fk_tenant_id == userDetails.tenant_id
                              && td.budget_year == input.budgetYear
                              && oh.fk_org_version == orgVersionContent.orgVersion
                              && actionTypes.Contains(th.action_type)
                        group td by new
                        {
                            th.action_type,
                            th.pk_action_id,
                            td.fk_account_code,
                            td.department_code,
                            td.function_code,
                            td.project_code,
                            th.description,
                            th.priority,
                            th.updated,
                            th.updated_by,
                            th.delete_comment,
                            td.fk_alter_code,
                            td.free_dim_1,
                            td.free_dim_2,
                            td.free_dim_3,
                            td.free_dim_4,
                            td.fk_adjustment_code,
                            th.tags,
                            th.goals_tags,
                            td.fk_change_id,
                            th.org_id,
                            th.org_level,
                            th.fk_action_id,
                        }
                        into grp
                        select new ActionGridColumns
                        {
                            actionType = grp.Key.action_type,
                            actionId = grp.Key.pk_action_id,
                            accountCode = grp.Key.fk_account_code,
                            departmentCode = grp.Key.department_code,
                            functionCode = grp.Key.function_code,
                            project = grp.Key.project_code,
                            description = grp.Key.description,
                            priority = grp.Key.priority ?? 0,
                            tdUpdated = grp.Key.updated,
                            delete_comment = grp.Key.delete_comment,
                            year1Amount = grp.Sum(x => x.year_1_amount),
                            year2Amount = grp.Sum(x => x.year_2_amount),
                            year3Amount = grp.Sum(x => x.year_3_amount),
                            year4Amount = grp.Sum(x => x.year_4_amount),
                            year5Amount = grp.Sum(x => x.year_5_amount),
                            year6Amount = grp.Sum(x => x.year_6_amount),
                            year7Amount = grp.Sum(x => x.year_7_amount),
                            year8Amount = grp.Sum(x => x.year_8_amount),
                            year9Amount = grp.Sum(x => x.year_9_amount),
                            year10Amount = grp.Sum(x => x.year_10_amount),
                            alterCode = grp.Key.fk_alter_code,
                            tdId = grp.Key.pk_action_id,
                            freeDim1 = grp.Key.free_dim_1,
                            freeDim2 = grp.Key.free_dim_2,
                            freeDim3 = grp.Key.free_dim_3,
                            freeDim4 = grp.Key.free_dim_4,
                            adjustmentCode = grp.Key.fk_adjustment_code,
                            tags = grp.Key.tags,
                            goalTags = grp.Key.goals_tags,
                            changeId = grp.Key.fk_change_id,
                            orgIdCreatedAt = grp.Key.org_id,
                            orgLevelCreatedAt = grp.Key.org_level,
                            fkActionId = grp.Key.fk_action_id,
                            updatedBy = grp.Key.updated_by
                        }).OrderByDescending(x => x.tdUpdated).ToListAsync();
                    break;

                case 2:
                    data1Async = await (from th in _parallelReadDbContext.tfp_delete_header
                        join td in _parallelReadDbContext.tfp_delete_detail on new
                                { a = th.fk_tenant_id, b = th.pk_action_id }
                            equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                        join oh in _parallelReadDbContext.tco_org_hierarchy on new
                                { a = td.fk_tenant_id, b = td.department_code }
                            equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                        where th.fk_tenant_id == userDetails.tenant_id
                              && td.budget_year == input.budgetYear
                              && oh.fk_org_version == orgVersionContent.orgVersion
                              && oh.org_id_2 == input.orgId
                              && actionTypes.Contains(th.action_type)
                        orderby th.updated descending
                        group td by new
                        {
                            th.action_type,
                            th.pk_action_id,
                            td.fk_account_code,
                            td.department_code,
                            td.function_code,
                            td.project_code,
                            th.description,
                            th.priority,
                            th.updated,
                            th.updated_by,
                            th.delete_comment,
                            td.fk_alter_code,
                            td.free_dim_1,
                            td.free_dim_2,
                            td.free_dim_3,
                            td.free_dim_4,
                            td.fk_adjustment_code,
                            th.tags,
                            th.goals_tags,
                            td.fk_change_id,
                            th.org_id,
                            th.org_level,
                            th.fk_action_id
                        }
                        into grp
                        select new ActionGridColumns
                        {
                            actionType = grp.Key.action_type,
                            actionId = grp.Key.pk_action_id,
                            accountCode = grp.Key.fk_account_code,
                            departmentCode = grp.Key.department_code,
                            functionCode = grp.Key.function_code,
                            project = grp.Key.project_code,
                            description = grp.Key.description,
                            priority = grp.Key.priority ?? 0,
                            tdUpdated = grp.Key.updated,
                            delete_comment = grp.Key.delete_comment,
                            year1Amount = grp.Sum(x => x.year_1_amount),
                            year2Amount = grp.Sum(x => x.year_2_amount),
                            year3Amount = grp.Sum(x => x.year_3_amount),
                            year4Amount = grp.Sum(x => x.year_4_amount),
                            year5Amount = grp.Sum(x => x.year_5_amount),
                            year6Amount = grp.Sum(x => x.year_6_amount),
                            year7Amount = grp.Sum(x => x.year_7_amount),
                            year8Amount = grp.Sum(x => x.year_8_amount),
                            year9Amount = grp.Sum(x => x.year_9_amount),
                            year10Amount = grp.Sum(x => x.year_10_amount),
                            alterCode = grp.Key.fk_alter_code,
                            tdId = grp.Key.pk_action_id,
                            freeDim1 = grp.Key.free_dim_1,
                            freeDim2 = grp.Key.free_dim_2,
                            freeDim3 = grp.Key.free_dim_3,
                            freeDim4 = grp.Key.free_dim_4,
                            adjustmentCode = grp.Key.fk_adjustment_code,
                            tags = grp.Key.tags,
                            goalTags = grp.Key.goals_tags,
                            changeId = grp.Key.fk_change_id,
                            orgIdCreatedAt = grp.Key.org_id,
                            orgLevelCreatedAt = grp.Key.org_level,
                            fkActionId = grp.Key.fk_action_id,
                            updatedBy = grp.Key.updated_by
                        }).ToListAsync();
                    break;

                case 3:
                    data1Async = await (from th in _parallelReadDbContext.tfp_delete_header
                        join td in _parallelReadDbContext.tfp_delete_detail on new
                                { a = th.fk_tenant_id, b = th.pk_action_id }
                            equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                        join oh in _parallelReadDbContext.tco_org_hierarchy on new
                                { a = td.fk_tenant_id, b = td.department_code }
                            equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                        where th.fk_tenant_id == userDetails.tenant_id
                              && td.budget_year == input.budgetYear
                              && oh.fk_org_version == orgVersionContent.orgVersion
                              && oh.org_id_3 == input.orgId
                              && actionTypes.Contains(th.action_type)
                        orderby th.updated descending
                        group td by new
                        {
                            th.action_type,
                            th.pk_action_id,
                            td.fk_account_code,
                            td.department_code,
                            td.function_code,
                            td.project_code,
                            th.description,
                            th.priority,
                            th.updated,
                            th.updated_by,
                            th.delete_comment,
                            td.fk_alter_code,
                            td.free_dim_1,
                            td.free_dim_2,
                            td.free_dim_3,
                            td.free_dim_4,
                            td.fk_adjustment_code,
                            th.tags,
                            th.goals_tags,
                            td.fk_change_id,
                            th.org_id,
                            th.org_level,
                            th.fk_action_id
                        }
                        into grp
                        select new ActionGridColumns
                        {
                            actionType = grp.Key.action_type,
                            actionId = grp.Key.pk_action_id,
                            accountCode = grp.Key.fk_account_code,
                            departmentCode = grp.Key.department_code,
                            functionCode = grp.Key.function_code,
                            project = grp.Key.project_code,
                            description = grp.Key.description,
                            priority = grp.Key.priority ?? 0,
                            tdUpdated = grp.Key.updated,
                            delete_comment = grp.Key.delete_comment,
                            year1Amount = grp.Sum(x => x.year_1_amount),
                            year2Amount = grp.Sum(x => x.year_2_amount),
                            year3Amount = grp.Sum(x => x.year_3_amount),
                            year4Amount = grp.Sum(x => x.year_4_amount),
                            year5Amount = grp.Sum(x => x.year_5_amount),
                            year6Amount = grp.Sum(x => x.year_6_amount),
                            year7Amount = grp.Sum(x => x.year_7_amount),
                            year8Amount = grp.Sum(x => x.year_8_amount),
                            year9Amount = grp.Sum(x => x.year_9_amount),
                            year10Amount = grp.Sum(x => x.year_10_amount),
                            alterCode = grp.Key.fk_alter_code,
                            tdId = grp.Key.pk_action_id,
                            freeDim1 = grp.Key.free_dim_1,
                            freeDim2 = grp.Key.free_dim_2,
                            freeDim3 = grp.Key.free_dim_3,
                            freeDim4 = grp.Key.free_dim_4,
                            adjustmentCode = grp.Key.fk_adjustment_code,
                            tags = grp.Key.tags,
                            goalTags = grp.Key.goals_tags,
                            changeId = grp.Key.fk_change_id,
                            orgIdCreatedAt = grp.Key.org_id,
                            orgLevelCreatedAt = grp.Key.org_level,
                            fkActionId = grp.Key.fk_action_id,
                            updatedBy = grp.Key.updated_by
                        }).ToListAsync();
                    break;

                case 4:
                    data1Async = await (from th in _parallelReadDbContext.tfp_delete_header
                        join td in _parallelReadDbContext.tfp_delete_detail on new
                                { a = th.fk_tenant_id, b = th.pk_action_id }
                            equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                        join oh in _parallelReadDbContext.tco_org_hierarchy on new
                                { a = td.fk_tenant_id, b = td.department_code }
                            equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                        where th.fk_tenant_id == userDetails.tenant_id
                              && td.budget_year == input.budgetYear
                              && oh.fk_org_version == orgVersionContent.orgVersion
                              && oh.org_id_4 == input.orgId
                              && actionTypes.Contains(th.action_type)
                        orderby th.updated descending
                        group td by new
                        {
                            th.action_type,
                            th.pk_action_id,
                            td.fk_account_code,
                            td.department_code,
                            td.function_code,
                            td.project_code,
                            th.description,
                            th.priority,
                            th.updated,
                            th.updated_by,
                            th.delete_comment,
                            td.fk_alter_code,
                            td.free_dim_1,
                            td.free_dim_2,
                            td.free_dim_3,
                            td.free_dim_4,
                            td.fk_adjustment_code,
                            th.tags,
                            th.goals_tags,
                            td.fk_change_id,
                            th.org_id,
                            th.org_level,
                            th.fk_action_id
                        }
                        into grp
                        select new ActionGridColumns
                        {
                            actionType = grp.Key.action_type,
                            actionId = grp.Key.pk_action_id,
                            accountCode = grp.Key.fk_account_code,
                            departmentCode = grp.Key.department_code,
                            functionCode = grp.Key.function_code,
                            project = grp.Key.project_code,
                            description = grp.Key.description,
                            priority = grp.Key.priority ?? 0,
                            tdUpdated = grp.Key.updated,
                            delete_comment = grp.Key.delete_comment,
                            year1Amount = grp.Sum(x => x.year_1_amount),
                            year2Amount = grp.Sum(x => x.year_2_amount),
                            year3Amount = grp.Sum(x => x.year_3_amount),
                            year4Amount = grp.Sum(x => x.year_4_amount),
                            year5Amount = grp.Sum(x => x.year_5_amount),
                            year6Amount = grp.Sum(x => x.year_6_amount),
                            year7Amount = grp.Sum(x => x.year_7_amount),
                            year8Amount = grp.Sum(x => x.year_8_amount),
                            year9Amount = grp.Sum(x => x.year_9_amount),
                            year10Amount = grp.Sum(x => x.year_10_amount),
                            alterCode = grp.Key.fk_alter_code,
                            tdId = grp.Key.pk_action_id,
                            freeDim1 = grp.Key.free_dim_1,
                            freeDim2 = grp.Key.free_dim_2,
                            freeDim3 = grp.Key.free_dim_3,
                            freeDim4 = grp.Key.free_dim_4,
                            adjustmentCode = grp.Key.fk_adjustment_code,
                            tags = grp.Key.tags,
                            goalTags = grp.Key.goals_tags,
                            changeId = grp.Key.fk_change_id,
                            orgIdCreatedAt = grp.Key.org_id,
                            orgLevelCreatedAt = grp.Key.org_level,
                            fkActionId = grp.Key.fk_action_id,
                            updatedBy = grp.Key.updated_by
                        }).ToListAsync();
                    break;

                case 5:
                    data1Async = await (from th in _parallelReadDbContext.tfp_delete_header
                        join td in _parallelReadDbContext.tfp_delete_detail on new
                                { a = th.fk_tenant_id, b = th.pk_action_id }
                            equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                        join oh in _parallelReadDbContext.tco_org_hierarchy on new
                                { a = td.fk_tenant_id, b = td.department_code }
                            equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                        where th.fk_tenant_id == userDetails.tenant_id
                              && td.budget_year == input.budgetYear
                              && oh.fk_org_version == orgVersionContent.orgVersion
                              && oh.org_id_5 == input.orgId
                              && actionTypes.Contains(th.action_type)
                        orderby th.updated descending
                        group td by new
                        {
                            th.action_type,
                            th.pk_action_id,
                            td.fk_account_code,
                            td.department_code,
                            td.function_code,
                            td.project_code,
                            th.description,
                            th.priority,
                            th.updated,
                            th.updated_by,
                            th.delete_comment,
                            td.fk_alter_code,
                            td.free_dim_1,
                            td.free_dim_2,
                            td.free_dim_3,
                            td.free_dim_4,
                            td.fk_adjustment_code,
                            th.tags,
                            th.goals_tags,
                            td.fk_change_id,
                            th.org_id,
                            th.org_level,
                            th.fk_action_id
                        }
                        into grp
                        select new ActionGridColumns
                        {
                            actionType = grp.Key.action_type,
                            actionId = grp.Key.pk_action_id,
                            accountCode = grp.Key.fk_account_code,
                            departmentCode = grp.Key.department_code,
                            functionCode = grp.Key.function_code,
                            project = grp.Key.project_code,
                            description = grp.Key.description,
                            priority = grp.Key.priority ?? 0,
                            tdUpdated = grp.Key.updated,
                            delete_comment = grp.Key.delete_comment,
                            year1Amount = grp.Sum(x => x.year_1_amount),
                            year2Amount = grp.Sum(x => x.year_2_amount),
                            year3Amount = grp.Sum(x => x.year_3_amount),
                            year4Amount = grp.Sum(x => x.year_4_amount),
                            year5Amount = grp.Sum(x => x.year_5_amount),
                            year6Amount = grp.Sum(x => x.year_6_amount),
                            year7Amount = grp.Sum(x => x.year_7_amount),
                            year8Amount = grp.Sum(x => x.year_8_amount),
                            year9Amount = grp.Sum(x => x.year_9_amount),
                            year10Amount = grp.Sum(x => x.year_10_amount),
                            alterCode = grp.Key.fk_alter_code,
                            tdId = grp.Key.pk_action_id,
                            freeDim1 = grp.Key.free_dim_1,
                            freeDim2 = grp.Key.free_dim_2,
                            freeDim3 = grp.Key.free_dim_3,
                            freeDim4 = grp.Key.free_dim_4,
                            adjustmentCode = grp.Key.fk_adjustment_code,
                            tags = grp.Key.tags,
                            goalTags = grp.Key.goals_tags,
                            changeId = grp.Key.fk_change_id,
                            orgIdCreatedAt = grp.Key.org_id,
                            orgLevelCreatedAt = grp.Key.org_level,
                            fkActionId = grp.Key.fk_action_id,
                            updatedBy = grp.Key.updated_by
                        }).ToListAsync();
                    break;

                case 6:
                    data1Async = await (from th in _parallelReadDbContext.tfp_delete_header
                        join td in _parallelReadDbContext.tfp_delete_detail on new
                                { a = th.fk_tenant_id, b = th.pk_action_id }
                            equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                        join oh in _parallelReadDbContext.tco_org_hierarchy on new
                                { a = td.fk_tenant_id, b = td.department_code }
                            equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                        where th.fk_tenant_id == userDetails.tenant_id
                              && td.budget_year == input.budgetYear
                              && oh.fk_org_version == orgVersionContent.orgVersion
                              && oh.org_id_6 == input.orgId
                              && actionTypes.Contains(th.action_type)
                        orderby th.updated descending
                        group td by new
                        {
                            th.action_type,
                            th.pk_action_id,
                            td.fk_account_code,
                            td.department_code,
                            td.function_code,
                            td.project_code,
                            th.description,
                            th.priority,
                            th.updated,
                            th.updated_by,
                            th.delete_comment,
                            td.fk_alter_code,
                            td.free_dim_1,
                            td.free_dim_2,
                            td.free_dim_3,
                            td.free_dim_4,
                            td.fk_adjustment_code,
                            th.tags,
                            th.goals_tags,
                            td.fk_change_id,
                            th.org_id,
                            th.org_level,
                            th.fk_action_id
                        }
                        into grp
                        select new ActionGridColumns
                        {
                            actionType = grp.Key.action_type,
                            actionId = grp.Key.pk_action_id,
                            accountCode = grp.Key.fk_account_code,
                            departmentCode = grp.Key.department_code,
                            functionCode = grp.Key.function_code,
                            project = grp.Key.project_code,
                            description = grp.Key.description,
                            priority = grp.Key.priority ?? 0,
                            tdUpdated = grp.Key.updated,
                            delete_comment = grp.Key.delete_comment,
                            year1Amount = grp.Sum(x => x.year_1_amount),
                            year2Amount = grp.Sum(x => x.year_2_amount),
                            year3Amount = grp.Sum(x => x.year_3_amount),
                            year4Amount = grp.Sum(x => x.year_4_amount),
                            year5Amount = grp.Sum(x => x.year_5_amount),
                            year6Amount = grp.Sum(x => x.year_6_amount),
                            year7Amount = grp.Sum(x => x.year_7_amount),
                            year8Amount = grp.Sum(x => x.year_8_amount),
                            year9Amount = grp.Sum(x => x.year_9_amount),
                            year10Amount = grp.Sum(x => x.year_10_amount),
                            alterCode = grp.Key.fk_alter_code,
                            tdId = grp.Key.pk_action_id,
                            freeDim1 = grp.Key.free_dim_1,
                            freeDim2 = grp.Key.free_dim_2,
                            freeDim3 = grp.Key.free_dim_3,
                            freeDim4 = grp.Key.free_dim_4,
                            adjustmentCode = grp.Key.fk_adjustment_code,
                            tags = grp.Key.tags,
                            goalTags = grp.Key.goals_tags,
                            changeId = grp.Key.fk_change_id,
                            orgIdCreatedAt = grp.Key.org_id,
                            orgLevelCreatedAt = grp.Key.org_level,
                            fkActionId = grp.Key.fk_action_id,
                            updatedBy = grp.Key.updated_by
                        }).ToListAsync();
                    break;

                case 7:
                    data1Async = await (from th in _parallelReadDbContext.tfp_delete_header
                        join td in _parallelReadDbContext.tfp_delete_detail on new
                                { a = th.fk_tenant_id, b = th.pk_action_id }
                            equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                        join oh in _parallelReadDbContext.tco_org_hierarchy on new
                                { a = td.fk_tenant_id, b = td.department_code }
                            equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                        where th.fk_tenant_id == userDetails.tenant_id
                              && td.budget_year == input.budgetYear
                              && oh.fk_org_version == orgVersionContent.orgVersion
                              && oh.org_id_7 == input.orgId
                              && actionTypes.Contains(th.action_type)
                        orderby th.updated descending
                        group td by new
                        {
                            th.action_type,
                            th.pk_action_id,
                            td.fk_account_code,
                            td.department_code,
                            td.function_code,
                            td.project_code,
                            th.description,
                            th.priority,
                            th.updated,
                            th.updated_by,
                            th.delete_comment,
                            td.fk_alter_code,
                            td.free_dim_1,
                            td.free_dim_2,
                            td.free_dim_3,
                            td.free_dim_4,
                            td.fk_adjustment_code,
                            th.tags,
                            th.goals_tags,
                            td.fk_change_id,
                            th.org_id,
                            th.org_level,
                            th.fk_action_id
                        }
                        into grp
                        select new ActionGridColumns
                        {
                            actionType = grp.Key.action_type,
                            actionId = grp.Key.pk_action_id,
                            accountCode = grp.Key.fk_account_code,
                            departmentCode = grp.Key.department_code,
                            functionCode = grp.Key.function_code,
                            project = grp.Key.project_code,
                            description = grp.Key.description,
                            priority = grp.Key.priority ?? 0,
                            tdUpdated = grp.Key.updated,
                            delete_comment = grp.Key.delete_comment,
                            year1Amount = grp.Sum(x => x.year_1_amount),
                            year2Amount = grp.Sum(x => x.year_2_amount),
                            year3Amount = grp.Sum(x => x.year_3_amount),
                            year4Amount = grp.Sum(x => x.year_4_amount),
                            year5Amount = grp.Sum(x => x.year_5_amount),
                            year6Amount = grp.Sum(x => x.year_6_amount),
                            year7Amount = grp.Sum(x => x.year_7_amount),
                            year8Amount = grp.Sum(x => x.year_8_amount),
                            year9Amount = grp.Sum(x => x.year_9_amount),
                            year10Amount = grp.Sum(x => x.year_10_amount),
                            alterCode = grp.Key.fk_alter_code,
                            tdId = grp.Key.pk_action_id,
                            freeDim1 = grp.Key.free_dim_1,
                            freeDim2 = grp.Key.free_dim_2,
                            freeDim3 = grp.Key.free_dim_3,
                            freeDim4 = grp.Key.free_dim_4,
                            adjustmentCode = grp.Key.fk_adjustment_code,
                            tags = grp.Key.tags,
                            goalTags = grp.Key.goals_tags,
                            changeId = grp.Key.fk_change_id,
                            orgIdCreatedAt = grp.Key.org_id,
                            orgLevelCreatedAt = grp.Key.org_level,
                            fkActionId = grp.Key.fk_action_id,
                            updatedBy = grp.Key.updated_by
                        }).ToListAsync();
                    break;

                case 8:
                    data1Async = await (from th in _parallelReadDbContext.tfp_delete_header
                        join td in _parallelReadDbContext.tfp_delete_detail on new
                                { a = th.fk_tenant_id, b = th.pk_action_id }
                            equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                        join oh in _parallelReadDbContext.tco_org_hierarchy on new
                                { a = td.fk_tenant_id, b = td.department_code }
                            equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                        where th.fk_tenant_id == userDetails.tenant_id
                              && td.budget_year == input.budgetYear
                              && oh.fk_org_version == orgVersionContent.orgVersion
                              && oh.org_id_8 == input.orgId
                              && actionTypes.Contains(th.action_type)
                        orderby th.updated descending
                        group td by new
                        {
                            th.action_type,
                            th.pk_action_id,
                            td.fk_account_code,
                            td.department_code,
                            td.function_code,
                            td.project_code,
                            th.description,
                            th.priority,
                            th.updated,
                            th.updated_by,
                            th.delete_comment,
                            td.fk_alter_code,
                            td.free_dim_1,
                            td.free_dim_2,
                            td.free_dim_3,
                            td.free_dim_4,
                            td.fk_adjustment_code,
                            th.tags,
                            th.goals_tags,
                            td.fk_change_id,
                            th.org_id,
                            th.org_level,
                            th.fk_action_id
                        }
                        into grp
                        select new ActionGridColumns
                        {
                            actionType = grp.Key.action_type,
                            actionId = grp.Key.pk_action_id,
                            accountCode = grp.Key.fk_account_code,
                            departmentCode = grp.Key.department_code,
                            functionCode = grp.Key.function_code,
                            project = grp.Key.project_code,
                            description = grp.Key.description,
                            priority = grp.Key.priority ?? 0,
                            tdUpdated = grp.Key.updated,
                            delete_comment = grp.Key.delete_comment,
                            year1Amount = grp.Sum(x => x.year_1_amount),
                            year2Amount = grp.Sum(x => x.year_2_amount),
                            year3Amount = grp.Sum(x => x.year_3_amount),
                            year4Amount = grp.Sum(x => x.year_4_amount),
                            year5Amount = grp.Sum(x => x.year_5_amount),
                            year6Amount = grp.Sum(x => x.year_6_amount),
                            year7Amount = grp.Sum(x => x.year_7_amount),
                            year8Amount = grp.Sum(x => x.year_8_amount),
                            year9Amount = grp.Sum(x => x.year_9_amount),
                            year10Amount = grp.Sum(x => x.year_10_amount),
                            alterCode = grp.Key.fk_alter_code,
                            tdId = grp.Key.pk_action_id,
                            freeDim1 = grp.Key.free_dim_1,
                            freeDim2 = grp.Key.free_dim_2,
                            freeDim3 = grp.Key.free_dim_3,
                            freeDim4 = grp.Key.free_dim_4,
                            adjustmentCode = grp.Key.fk_adjustment_code,
                            tags = grp.Key.tags,
                            goalTags = grp.Key.goals_tags,
                            changeId = grp.Key.fk_change_id,
                            orgIdCreatedAt = grp.Key.org_id,
                            orgLevelCreatedAt = grp.Key.org_level,
                            fkActionId = grp.Key.fk_action_id,
                            updatedBy = grp.Key.updated_by
                        }).ToListAsync();
                    break;
            }

            var datsetAsync = await (from bc in _parallelReadDbContext.tfp_budget_changes
                where bc.fk_tenant_id == userDetails.tenant_id && bc.budget_year == input.budgetYear &&
                      bc.org_budget_flag == 1
                select bc).ToListAsync();

            var datsetAsync1 = await (from bc in _parallelReadDbContext.tco_fp_alter_codes
                where bc.fk_tenant_id == userDetails.tenant_id
                select bc).ToListAsync();

            var datsetAsync3 = await (from bc in _parallelReadDbContext.tco_budget_phase
                where bc.fk_tenant_id == userDetails.tenant_id && bc.org_budget_flag == 1
                select bc).ToListAsync();
            List<UserInformation> dataset2 = new();
            int clientId = userDetails.client_id;
            string result = await _cache.GetStringForTenantAsync(clientId, userDetails.tenant_id,
                "tenantUserDetails_" + userDetails.tenant_id.ToString());
            if (!string.IsNullOrEmpty(result))
            {
                dataset2 = JsonConvert.DeserializeObject<List<UserInformation>>(result);
            }

            var dataset = datsetAsync;
            var dataset1 = datsetAsync1;
            var dataset3 = datsetAsync3;

            var dd = (from th in data1Async
                join bc in dataset on th.changeId equals bc.pk_change_id
                join bp in dataset3 on bc.fk_budget_phase_id equals bp.pk_budget_phase_id
                join ac in dataset1 on th.alterCode equals ac.pk_alter_code
                join tu in dataset2 on th.updatedBy equals tu.pk_id into ret
                from r in ret.DefaultIfEmpty()
                group th by new
                {
                    th.actionType,
                    th.actionId,
                    th.orgIdCreatedAt,
                    th.orgLevelCreatedAt,
                    th.accountCode,
                    th.departmentCode,
                    th.functionCode,
                    th.description,
                    th.delete_comment,
                    th.changeId,
                    th.alterCode,
                    first_name = r != null ? r.first_name : string.Empty,
                    last_name = r != null ? r.last_name : string.Empty,
                    th.tdUpdated,
                    th.project,
                    th.freeDim1,
                    th.freeDim2,
                    th.freeDim3,
                    th.freeDim4,
                    th.adjustmentCode,
                    th.tdUpdatedBy,
                    th.priority,
                    th.tags,
                    th.goalTags,
                    bp.pk_budget_phase_id,
                    ac.limit_code,
                    th.tdId,
                    th.fkActionId
                }
                into grp
                select new ActionGridColumns
                {
                    orgId = "",
                    orgName = "",
                    serviceId = "",
                    serviceName = "",
                    actionType = grp.Key.actionType,
                    actionId = grp.Key.actionId,
                    accountCode = grp.Key.accountCode,
                    departmentCode = grp.Key.departmentCode,
                    functionCode = grp.Key.functionCode,
                    year1Amount = grp.Sum(x => x.year1Amount),
                    year2Amount = grp.Sum(x => x.year2Amount),
                    year3Amount = grp.Sum(x => x.year3Amount),
                    year4Amount = grp.Sum(x => x.year4Amount),
                    year5Amount = grp.Sum(x => x.year5Amount),
                    year6Amount = grp.Sum(x => x.year6Amount),
                    year7Amount = grp.Sum(x => x.year7Amount),
                    year8Amount = grp.Sum(x => x.year8Amount),
                    year9Amount = grp.Sum(x => x.year9Amount),
                    year10Amount = grp.Sum(x => x.year10Amount),
                    description = grp.Key.description,
                    changeId = grp.Key.changeId,
                    alterCode = grp.Key.alterCode,
                    tdId = grp.Key.tdId,
                    tdUpdated = grp.Key.tdUpdated,
                    priority = grp.Key.priority,
                    tdUpdatedBy = string.IsNullOrEmpty(grp.Key.first_name)
                        ? string.Empty
                        : grp.Key.first_name + " " + grp.Key.last_name,
                    project = grp.Key.project,
                    freeDim1 = grp.Key.freeDim1,
                    freeDim2 = grp.Key.freeDim2,
                    freeDim3 = grp.Key.freeDim3,
                    freeDim4 = grp.Key.freeDim4,
                    adjustmentCode = grp.Key.adjustmentCode,
                    tags = grp.Key.tags,
                    goalTags = grp.Key.goalTags,
                    budgetPhase = grp.Key.pk_budget_phase_id,
                    limitCode = grp.Key.limit_code,
                    orgIdCreatedAt = grp.Key.orgIdCreatedAt,
                    orgLevelCreatedAt = grp.Key.orgLevelCreatedAt,
                    fkActionId = grp.Key.fkActionId
                }).ToList();

            var departFunctFilterData = await GetDepartmentFuncFilterData(userId, input);

            //Get Hidden departments
            int changeId =
                await _finUtility.GetActiveBudgetChangeIdAsync(userDetails.user_name, "Budget_Proposal",
                    input.budgetYear);
            Dictionary<string, List<string>> departmentsToHide =
                await _finUtility.GetDepartmentsToHideAsync(userDetails.user_name, input.budgetYear,
                    input.orgLevel.Value, input.orgId, input.serviceId, Convert.ToString(changeId));
            if (departmentsToHide.First(x => x.Key == "allDepartments").Value.Any())
            {
                var departments = departmentsToHide.First(x => x.Key == "allDepartments").Value;
                dd = dd.Where(x => !departments.Contains(x.departmentCode)).ToList();
            }

            // filter data with chapterId
            bool isChapterSetup = await _finUtility.isChapterSetup(userId);
            if (isChapterSetup && input.orgLevel == 1)
            {
                input.chapterId =
                    string.IsNullOrEmpty(input.chapterId) || input.chapterId.Trim().ToLower() == "ALL".ToLower()
                        ? string.Empty
                        : input.chapterId;
                int orgLevel = input.orgLevel != null ? (int)input.orgLevel : 0;
                var relationDepartments =
                    await _finUtility.GetAttributeConnectedDepartmentsForSelectedOrgAsync(userId, input.budgetYear,
                        input.orgId, orgLevel, input.chapterId);
                dd = dd.Where(x => relationDepartments.Contains(x.departmentCode)).ToList();
            }

            if (departFunctFilterData.functions.Any(x => !string.IsNullOrEmpty(x)))
            {
                dd = dd.Where(y => departFunctFilterData.functions.Contains(y.functionCode)).ToList();
            }

            List<clsAggregates> lstAggregates =
                GetGroupedDeletedDataSet(userId, dd, input, activechageId, input.ColToDisplay);
            return await FormatActionGridData(userDetails.user_name,
                langString.FirstOrDefault(v => v.Key == "BP_GridColumn_type3").Value.LangText, "TYPE-DL", lstAggregates,
                input.budgetYear, input.filters);
        }

        public async Task<ActionGridFormatData> GetFinplanOperationalActions(string userId, actionGridInputHelper input)
        {
            StringBuilder sbLogs = new StringBuilder();
            sbLogs.Append($"Host Name {input.Domain} {Environment.NewLine}");
            sbLogs.Append($"UserName {userId} {Environment.NewLine}");

            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langString =
                await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name,
                    "BudgetProposal");
            List<int> actionTypes = new() { 31, 41, 21 };
            Start("2747 UpdateActionType", sbLogs);
            await UpdateActionType(userDetails.tenant_id, input.budgetYear, actionTypes);
            End("2747 UpdateActionType", sbLogs);

            TenantDBContext _parallelReadDbContext = await _utility.GetTenantDBContextAsync();
            var orgVersionContent =
                await _utility.GetOrgVersionSpecificContentAsync(userId,
                    _utility.GetForecastPeriod(input.budgetYear, 1));
            Start("2753: deltedactions", sbLogs);
            var deltedactions = await _parallelReadDbContext.tfp_delete_header.Where(x =>
                    x.fk_tenant_id == userDetails.tenant_id && actionTypes.Contains(x.action_type)).AsNoTracking()
                .Select(x => x.fk_action_id).ToListAsync();
            End("2753: deltedactions", sbLogs);

            Start("2758: blistactions", sbLogs);
            var blistactions = await _parallelReadDbContext.tfp_temp_header.Where(x =>
                    x.fk_tenant_id == userDetails.tenant_id && actionTypes.Contains(x.action_type)).AsNoTracking()
                .Select(x => x.fk_action_id).ToListAsync();
            End("2758: blistactions", sbLogs);

            var deletedblist = deltedactions.Concat(blistactions).Cast<int>().ToList();
            int activechageId = -1;
            Start("2765: activeBudgetRoundlist", sbLogs);
            var activeBudgetRoundlist = await (from t in _parallelReadDbContext.tfp_budget_changes
                where t.fk_tenant_id == userDetails.tenant_id
                      && t.budget_year == input.budgetYear
                      && t.org_budget_flag == 1 && t.status == 1
                      && (t.workflow_status == 20 || t.workflow_status == 30 || t.workflow_status == 40)
                select t).AsNoTracking().ToListAsync();
            End("2765: activeBudgetRoundlist", sbLogs);

            if (activeBudgetRoundlist.Any())
            {
                var action = activeBudgetRoundlist.FirstOrDefault();
                activechageId = action != null ? action.pk_change_id : activechageId;
            }

            List<ActionGridColumns> data1Async = new();
            Start("2780: data1Async", sbLogs);
            switch (input.orgLevel.Value)
            {
                case 1:
                    data1Async = await (from th in _parallelReadDbContext.tfp_trans_header
                        join td in _parallelReadDbContext.tfp_trans_detail on new
                                { a = th.fk_tenant_id, b = th.pk_action_id }
                            equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                        join oh in _parallelReadDbContext.tco_org_hierarchy on new
                                { a = td.fk_tenant_id, b = td.department_code }
                            equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                        where th.fk_tenant_id == userDetails.tenant_id
                              && td.budget_year == input.budgetYear
                              && oh.fk_org_version == orgVersionContent.orgVersion
                              && actionTypes.Contains(th.action_type)
                              && !deletedblist.Contains(th.pk_action_id)
                        group td by new
                        {
                            th.action_type,
                            th.pk_action_id,
                            th.org_id,
                            th.org_level,
                            td.department_code,
                            td.function_code,
                            th.description,
                            th.line_order,
                            th.isManuallyAdded,
                            td.fk_change_id,
                            td.fk_alter_code,
                            th.updated,
                            th.updated_by,
                            th.priority,
                            th.tags,
                            th.goals_tags
                        }
                        into grp
                        select new ActionGridColumns
                        {
                            orgId = "",
                            orgName = "",
                            serviceId = "",
                            serviceName = "",
                            actionType = grp.Key.action_type,
                            actionId = grp.Key.pk_action_id,
                            departmentCode = grp.Key.department_code,
                            functionCode = grp.Key.function_code,
                            year1Amount = grp.Sum(x => x.year_1_amount),
                            year2Amount = grp.Sum(x => x.year_2_amount),
                            year3Amount = grp.Sum(x => x.year_3_amount),
                            year4Amount = grp.Sum(x => x.year_4_amount),
                            year5Amount = grp.Sum(x => x.year_5_amount),
                            year6Amount = grp.Sum(x => x.year_6_amount),
                            year7Amount = grp.Sum(x => x.year_7_amount),
                            year8Amount = grp.Sum(x => x.year_8_amount),
                            year9Amount = grp.Sum(x => x.year_9_amount),
                            year10Amount = grp.Sum(x => x.year_10_amount),
                            description = grp.Key.description,
                            changeId = grp.Key.fk_change_id,
                            alterCode = grp.Key.fk_alter_code,
                            isManuallyAdded = grp.Key.isManuallyAdded,
                            tdUpdated = grp.Key.updated,
                            lineOrderId = grp.Key.line_order,
                            priority = grp.Key.priority ?? 0,
                            tags = grp.Key.tags,
                            goalTags = grp.Key.goals_tags,
                            orgIdCreatedAt = grp.Key.org_id,
                            orgLevelCreatedAt = grp.Key.org_level,
                            updatedBy = grp.Key.updated_by
                        }).ToListAsync();
                    break;

                case 2:
                    data1Async = await (from th in _parallelReadDbContext.tfp_trans_header
                        join td in _parallelReadDbContext.tfp_trans_detail on new
                                { a = th.fk_tenant_id, b = th.pk_action_id }
                            equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                        join oh in _parallelReadDbContext.tco_org_hierarchy on new
                                { a = td.fk_tenant_id, b = td.department_code }
                            equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                        where th.fk_tenant_id == userDetails.tenant_id
                              && td.budget_year == input.budgetYear
                              && oh.fk_org_version == orgVersionContent.orgVersion
                              && oh.org_id_2 == input.orgId
                              && actionTypes.Contains(th.action_type)
                              && !deletedblist.Contains(th.pk_action_id)
                        group td by new
                        {
                            th.action_type,
                            th.pk_action_id,
                            th.org_id,
                            th.org_level,
                            td.department_code,
                            td.function_code,
                            th.description,
                            th.line_order,
                            th.isManuallyAdded,
                            td.fk_change_id,
                            td.fk_alter_code,
                            th.updated,
                            th.updated_by,
                            th.priority,
                            th.tags,
                            th.goals_tags
                        }
                        into grp
                        select new ActionGridColumns
                        {
                            orgId = "",
                            orgName = "",
                            serviceId = "",
                            serviceName = "",
                            actionType = grp.Key.action_type,
                            actionId = grp.Key.pk_action_id,
                            departmentCode = grp.Key.department_code,
                            functionCode = grp.Key.function_code,
                            year1Amount = grp.Sum(x => x.year_1_amount),
                            year2Amount = grp.Sum(x => x.year_2_amount),
                            year3Amount = grp.Sum(x => x.year_3_amount),
                            year4Amount = grp.Sum(x => x.year_4_amount),
                            year5Amount = grp.Sum(x => x.year_5_amount),
                            year6Amount = grp.Sum(x => x.year_6_amount),
                            year7Amount = grp.Sum(x => x.year_7_amount),
                            year8Amount = grp.Sum(x => x.year_8_amount),
                            year9Amount = grp.Sum(x => x.year_9_amount),
                            year10Amount = grp.Sum(x => x.year_10_amount),
                            description = grp.Key.description,
                            changeId = grp.Key.fk_change_id,
                            alterCode = grp.Key.fk_alter_code,
                            isManuallyAdded = grp.Key.isManuallyAdded,
                            tdUpdated = grp.Key.updated,
                            lineOrderId = grp.Key.line_order,
                            priority = grp.Key.priority ?? 0,
                            tags = grp.Key.tags,
                            goalTags = grp.Key.goals_tags,
                            orgIdCreatedAt = grp.Key.org_id,
                            orgLevelCreatedAt = grp.Key.org_level,
                            updatedBy = grp.Key.updated_by
                        }).ToListAsync();
                    break;

                case 3:
                    data1Async = await (from th in _parallelReadDbContext.tfp_trans_header
                        join td in _parallelReadDbContext.tfp_trans_detail on new
                                { a = th.fk_tenant_id, b = th.pk_action_id }
                            equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                        join oh in _parallelReadDbContext.tco_org_hierarchy on new
                                { a = td.fk_tenant_id, b = td.department_code }
                            equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                        where th.fk_tenant_id == userDetails.tenant_id
                              && td.budget_year == input.budgetYear
                              && oh.fk_org_version == orgVersionContent.orgVersion
                              && oh.org_id_3 == input.orgId
                              && actionTypes.Contains(th.action_type)
                              && !deletedblist.Contains(th.pk_action_id)
                        group td by new
                        {
                            th.action_type,
                            th.pk_action_id,
                            th.org_id,
                            th.org_level,
                            td.department_code,
                            td.function_code,
                            th.description,
                            th.line_order,
                            th.isManuallyAdded,
                            td.fk_change_id,
                            td.fk_alter_code,
                            th.updated,
                            th.updated_by,
                            th.priority,
                            th.tags,
                            th.goals_tags
                        }
                        into grp
                        select new ActionGridColumns
                        {
                            orgId = "",
                            orgName = "",
                            serviceId = "",
                            serviceName = "",
                            actionType = grp.Key.action_type,
                            actionId = grp.Key.pk_action_id,
                            departmentCode = grp.Key.department_code,
                            functionCode = grp.Key.function_code,
                            year1Amount = grp.Sum(x => x.year_1_amount),
                            year2Amount = grp.Sum(x => x.year_2_amount),
                            year3Amount = grp.Sum(x => x.year_3_amount),
                            year4Amount = grp.Sum(x => x.year_4_amount),
                            year5Amount = grp.Sum(x => x.year_5_amount),
                            year6Amount = grp.Sum(x => x.year_6_amount),
                            year7Amount = grp.Sum(x => x.year_7_amount),
                            year8Amount = grp.Sum(x => x.year_8_amount),
                            year9Amount = grp.Sum(x => x.year_9_amount),
                            year10Amount = grp.Sum(x => x.year_10_amount),
                            description = grp.Key.description,
                            changeId = grp.Key.fk_change_id,
                            alterCode = grp.Key.fk_alter_code,
                            isManuallyAdded = grp.Key.isManuallyAdded,
                            tdUpdated = grp.Key.updated,
                            lineOrderId = grp.Key.line_order,
                            priority = grp.Key.priority ?? 0,
                            tags = grp.Key.tags,
                            goalTags = grp.Key.goals_tags,
                            orgIdCreatedAt = grp.Key.org_id,
                            orgLevelCreatedAt = grp.Key.org_level,
                            updatedBy = grp.Key.updated_by
                        }).ToListAsync();
                    break;

                case 4:
                    data1Async = await (from th in _parallelReadDbContext.tfp_trans_header
                        join td in _parallelReadDbContext.tfp_trans_detail on new
                                { a = th.fk_tenant_id, b = th.pk_action_id }
                            equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                        join oh in _parallelReadDbContext.tco_org_hierarchy on new
                                { a = td.fk_tenant_id, b = td.department_code }
                            equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                        where th.fk_tenant_id == userDetails.tenant_id
                              && td.budget_year == input.budgetYear
                              && oh.fk_org_version == orgVersionContent.orgVersion
                              && oh.org_id_4 == input.orgId
                              && actionTypes.Contains(th.action_type)
                              && !deletedblist.Contains(th.pk_action_id)
                        group td by new
                        {
                            th.action_type,
                            th.pk_action_id,
                            th.org_id,
                            th.org_level,
                            td.department_code,
                            td.function_code,
                            th.description,
                            th.line_order,
                            th.isManuallyAdded,
                            td.fk_change_id,
                            td.fk_alter_code,
                            th.updated,
                            th.updated_by,
                            th.priority,
                            th.tags,
                            th.goals_tags
                        }
                        into grp
                        select new ActionGridColumns
                        {
                            orgId = "",
                            orgName = "",
                            serviceId = "",
                            serviceName = "",
                            actionType = grp.Key.action_type,
                            actionId = grp.Key.pk_action_id,
                            departmentCode = grp.Key.department_code,
                            functionCode = grp.Key.function_code,
                            year1Amount = grp.Sum(x => x.year_1_amount),
                            year2Amount = grp.Sum(x => x.year_2_amount),
                            year3Amount = grp.Sum(x => x.year_3_amount),
                            year4Amount = grp.Sum(x => x.year_4_amount),
                            year5Amount = grp.Sum(x => x.year_5_amount),
                            year6Amount = grp.Sum(x => x.year_6_amount),
                            year7Amount = grp.Sum(x => x.year_7_amount),
                            year8Amount = grp.Sum(x => x.year_8_amount),
                            year9Amount = grp.Sum(x => x.year_9_amount),
                            year10Amount = grp.Sum(x => x.year_10_amount),
                            description = grp.Key.description,
                            changeId = grp.Key.fk_change_id,
                            alterCode = grp.Key.fk_alter_code,
                            isManuallyAdded = grp.Key.isManuallyAdded,
                            tdUpdated = grp.Key.updated,
                            lineOrderId = grp.Key.line_order,
                            priority = grp.Key.priority ?? 0,
                            tags = grp.Key.tags,
                            goalTags = grp.Key.goals_tags,
                            orgIdCreatedAt = grp.Key.org_id,
                            orgLevelCreatedAt = grp.Key.org_level,
                            updatedBy = grp.Key.updated_by
                        }).ToListAsync();
                    break;

                case 5:
                    data1Async = await (from th in _parallelReadDbContext.tfp_trans_header
                        join td in _parallelReadDbContext.tfp_trans_detail on new
                                { a = th.fk_tenant_id, b = th.pk_action_id }
                            equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                        join oh in _parallelReadDbContext.tco_org_hierarchy on new
                                { a = td.fk_tenant_id, b = td.department_code }
                            equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                        where th.fk_tenant_id == userDetails.tenant_id
                              && td.budget_year == input.budgetYear
                              && oh.fk_org_version == orgVersionContent.orgVersion
                              && oh.org_id_5 == input.orgId
                              && actionTypes.Contains(th.action_type)
                              && !deletedblist.Contains(th.pk_action_id)
                        group td by new
                        {
                            th.action_type,
                            th.pk_action_id,
                            th.org_id,
                            th.org_level,
                            td.department_code,
                            td.function_code,
                            th.description,
                            th.line_order,
                            th.isManuallyAdded,
                            td.fk_change_id,
                            td.fk_alter_code,
                            th.updated,
                            th.updated_by,
                            th.priority,
                            th.tags,
                            th.goals_tags
                        }
                        into grp
                        select new ActionGridColumns
                        {
                            orgId = "",
                            orgName = "",
                            serviceId = "",
                            serviceName = "",
                            actionType = grp.Key.action_type,
                            actionId = grp.Key.pk_action_id,
                            departmentCode = grp.Key.department_code,
                            functionCode = grp.Key.function_code,
                            year1Amount = grp.Sum(x => x.year_1_amount),
                            year2Amount = grp.Sum(x => x.year_2_amount),
                            year3Amount = grp.Sum(x => x.year_3_amount),
                            year4Amount = grp.Sum(x => x.year_4_amount),
                            year5Amount = grp.Sum(x => x.year_5_amount),
                            year6Amount = grp.Sum(x => x.year_6_amount),
                            year7Amount = grp.Sum(x => x.year_7_amount),
                            year8Amount = grp.Sum(x => x.year_8_amount),
                            year9Amount = grp.Sum(x => x.year_9_amount),
                            year10Amount = grp.Sum(x => x.year_10_amount),
                            description = grp.Key.description,
                            changeId = grp.Key.fk_change_id,
                            alterCode = grp.Key.fk_alter_code,
                            isManuallyAdded = grp.Key.isManuallyAdded,
                            tdUpdated = grp.Key.updated,
                            lineOrderId = grp.Key.line_order,
                            priority = grp.Key.priority ?? 0,
                            tags = grp.Key.tags,
                            goalTags = grp.Key.goals_tags,
                            orgIdCreatedAt = grp.Key.org_id,
                            orgLevelCreatedAt = grp.Key.org_level,
                            updatedBy = grp.Key.updated_by
                        }).ToListAsync();
                    break;

                case 6:
                    data1Async = await (from th in _parallelReadDbContext.tfp_trans_header
                        join td in _parallelReadDbContext.tfp_trans_detail on new
                                { a = th.fk_tenant_id, b = th.pk_action_id }
                            equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                        join oh in _parallelReadDbContext.tco_org_hierarchy on new
                                { a = td.fk_tenant_id, b = td.department_code }
                            equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                        where th.fk_tenant_id == userDetails.tenant_id
                              && td.budget_year == input.budgetYear
                              && oh.fk_org_version == orgVersionContent.orgVersion
                              && oh.org_id_6 == input.orgId
                              && actionTypes.Contains(th.action_type)
                              && !deletedblist.Contains(th.pk_action_id)
                        group td by new
                        {
                            th.action_type,
                            th.pk_action_id,
                            th.org_id,
                            th.org_level,
                            td.department_code,
                            td.function_code,
                            th.description,
                            th.line_order,
                            th.isManuallyAdded,
                            td.fk_change_id,
                            td.fk_alter_code,
                            th.updated,
                            th.updated_by,
                            th.priority,
                            th.tags,
                            th.goals_tags
                        }
                        into grp
                        select new ActionGridColumns
                        {
                            orgId = "",
                            orgName = "",
                            serviceId = "",
                            serviceName = "",
                            actionType = grp.Key.action_type,
                            actionId = grp.Key.pk_action_id,
                            departmentCode = grp.Key.department_code,
                            functionCode = grp.Key.function_code,
                            year1Amount = grp.Sum(x => x.year_1_amount),
                            year2Amount = grp.Sum(x => x.year_2_amount),
                            year3Amount = grp.Sum(x => x.year_3_amount),
                            year4Amount = grp.Sum(x => x.year_4_amount),
                            year5Amount = grp.Sum(x => x.year_5_amount),
                            year6Amount = grp.Sum(x => x.year_6_amount),
                            year7Amount = grp.Sum(x => x.year_7_amount),
                            year8Amount = grp.Sum(x => x.year_8_amount),
                            year9Amount = grp.Sum(x => x.year_9_amount),
                            year10Amount = grp.Sum(x => x.year_10_amount),
                            description = grp.Key.description,
                            changeId = grp.Key.fk_change_id,
                            alterCode = grp.Key.fk_alter_code,
                            isManuallyAdded = grp.Key.isManuallyAdded,
                            tdUpdated = grp.Key.updated,
                            lineOrderId = grp.Key.line_order,
                            priority = grp.Key.priority ?? 0,
                            tags = grp.Key.tags,
                            goalTags = grp.Key.goals_tags,
                            orgIdCreatedAt = grp.Key.org_id,
                            orgLevelCreatedAt = grp.Key.org_level,
                            updatedBy = grp.Key.updated_by
                        }).ToListAsync();
                    break;

                case 7:
                    data1Async = await (from th in _parallelReadDbContext.tfp_trans_header
                        join td in _parallelReadDbContext.tfp_trans_detail on new
                                { a = th.fk_tenant_id, b = th.pk_action_id }
                            equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                        join oh in _parallelReadDbContext.tco_org_hierarchy on new
                                { a = td.fk_tenant_id, b = td.department_code }
                            equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                        where th.fk_tenant_id == userDetails.tenant_id
                              && td.budget_year == input.budgetYear
                              && oh.fk_org_version == orgVersionContent.orgVersion
                              && oh.org_id_7 == input.orgId
                              && actionTypes.Contains(th.action_type)
                              && !deletedblist.Contains(th.pk_action_id)
                        group td by new
                        {
                            th.action_type,
                            th.pk_action_id,
                            th.org_id,
                            th.org_level,
                            td.department_code,
                            td.function_code,
                            th.description,
                            th.line_order,
                            th.isManuallyAdded,
                            td.fk_change_id,
                            td.fk_alter_code,
                            th.updated,
                            th.updated_by,
                            th.priority,
                            th.tags,
                            th.goals_tags
                        }
                        into grp
                        select new ActionGridColumns
                        {
                            orgId = "",
                            orgName = "",
                            serviceId = "",
                            serviceName = "",
                            actionType = grp.Key.action_type,
                            actionId = grp.Key.pk_action_id,
                            departmentCode = grp.Key.department_code,
                            functionCode = grp.Key.function_code,
                            year1Amount = grp.Sum(x => x.year_1_amount),
                            year2Amount = grp.Sum(x => x.year_2_amount),
                            year3Amount = grp.Sum(x => x.year_3_amount),
                            year4Amount = grp.Sum(x => x.year_4_amount),
                            year5Amount = grp.Sum(x => x.year_5_amount),
                            year6Amount = grp.Sum(x => x.year_6_amount),
                            year7Amount = grp.Sum(x => x.year_7_amount),
                            year8Amount = grp.Sum(x => x.year_8_amount),
                            year9Amount = grp.Sum(x => x.year_9_amount),
                            year10Amount = grp.Sum(x => x.year_10_amount),
                            description = grp.Key.description,
                            changeId = grp.Key.fk_change_id,
                            alterCode = grp.Key.fk_alter_code,
                            isManuallyAdded = grp.Key.isManuallyAdded,
                            tdUpdated = grp.Key.updated,
                            lineOrderId = grp.Key.line_order,
                            priority = grp.Key.priority ?? 0,
                            tags = grp.Key.tags,
                            goalTags = grp.Key.goals_tags,
                            orgIdCreatedAt = grp.Key.org_id,
                            orgLevelCreatedAt = grp.Key.org_level,
                            updatedBy = grp.Key.updated_by
                        }).ToListAsync();
                    break;

                case 8:
                    data1Async = await (from th in _parallelReadDbContext.tfp_trans_header
                        join td in _parallelReadDbContext.tfp_trans_detail on new
                                { a = th.fk_tenant_id, b = th.pk_action_id }
                            equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                        join oh in _parallelReadDbContext.tco_org_hierarchy on new
                                { a = td.fk_tenant_id, b = td.department_code }
                            equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                        where th.fk_tenant_id == userDetails.tenant_id
                              && td.budget_year == input.budgetYear
                              && oh.fk_org_version == orgVersionContent.orgVersion
                              && oh.org_id_8 == input.orgId
                              && actionTypes.Contains(th.action_type)
                              && !deletedblist.Contains(th.pk_action_id)
                        group td by new
                        {
                            th.action_type,
                            th.pk_action_id,
                            th.org_id,
                            th.org_level,
                            td.department_code,
                            td.function_code,
                            th.description,
                            th.line_order,
                            th.isManuallyAdded,
                            td.fk_change_id,
                            td.fk_alter_code,
                            th.updated,
                            th.updated_by,
                            th.priority,
                            th.tags,
                            th.goals_tags
                        }
                        into grp
                        select new ActionGridColumns
                        {
                            orgId = "",
                            orgName = "",
                            serviceId = "",
                            serviceName = "",
                            actionType = grp.Key.action_type,
                            actionId = grp.Key.pk_action_id,
                            departmentCode = grp.Key.department_code,
                            functionCode = grp.Key.function_code,
                            year1Amount = grp.Sum(x => x.year_1_amount),
                            year2Amount = grp.Sum(x => x.year_2_amount),
                            year3Amount = grp.Sum(x => x.year_3_amount),
                            year4Amount = grp.Sum(x => x.year_4_amount),
                            year5Amount = grp.Sum(x => x.year_5_amount),
                            year6Amount = grp.Sum(x => x.year_6_amount),
                            year7Amount = grp.Sum(x => x.year_7_amount),
                            year8Amount = grp.Sum(x => x.year_8_amount),
                            year9Amount = grp.Sum(x => x.year_9_amount),
                            year10Amount = grp.Sum(x => x.year_10_amount),
                            description = grp.Key.description,
                            changeId = grp.Key.fk_change_id,
                            alterCode = grp.Key.fk_alter_code,
                            isManuallyAdded = grp.Key.isManuallyAdded,
                            tdUpdated = grp.Key.updated,
                            lineOrderId = grp.Key.line_order,
                            priority = grp.Key.priority ?? 0,
                            tags = grp.Key.tags,
                            goalTags = grp.Key.goals_tags,
                            orgIdCreatedAt = grp.Key.org_id,
                            orgLevelCreatedAt = grp.Key.org_level,
                            updatedBy = grp.Key.updated_by
                        }).ToListAsync();
                    break;
            }

            End("2780: data1Async", sbLogs);

            Start("3312: datsetAsync", sbLogs);
            var datsetAsync = await (from bc in _parallelReadDbContext.tfp_budget_changes
                where bc.fk_tenant_id == userDetails.tenant_id && bc.budget_year == input.budgetYear &&
                      bc.org_budget_flag == 1
                select bc).ToListAsync();
            End("3312: datsetAsync", sbLogs);

            Start("3318: datsetAsync1", sbLogs);
            var datsetAsync1 = await (from bc in _parallelReadDbContext.tco_fp_alter_codes
                where bc.fk_tenant_id == userDetails.tenant_id
                select bc).ToListAsync();
            End("3318: datsetAsync1", sbLogs);

            List<UserInformation> dataset2 = new();
            int clientId = userDetails.client_id;
            string result = await _cache.GetStringForTenantAsync(clientId, userDetails.tenant_id,
                "tenantUserDetails_" + userDetails.tenant_id.ToString());
            if (!string.IsNullOrEmpty(result))
            {
                dataset2 = JsonConvert.DeserializeObject<List<UserInformation>>(result);
            }

            Start("3331: datsetAsync3", sbLogs);
            var datsetAsync3 = await (from bc in _parallelReadDbContext.tco_budget_phase
                where bc.fk_tenant_id == userDetails.tenant_id && bc.org_budget_flag == 1
                select bc).ToListAsync();
            End("3331: datsetAsync3", sbLogs);

            var dataset = datsetAsync;
            var dataset1 = datsetAsync1;
            var dataset3 = datsetAsync3;

            Start("3341: dd", sbLogs);
            var dd = (from th in data1Async
                join bc in dataset on th.changeId equals bc.pk_change_id
                join bp in dataset3 on bc.fk_budget_phase_id equals bp.pk_budget_phase_id
                join ac in dataset1 on th.alterCode equals ac.pk_alter_code
                join tu in dataset2 on th.updatedBy equals tu.pk_id into ret
                from r in ret.DefaultIfEmpty()
                group th by new
                {
                    th.actionType,
                    th.actionId,
                    th.orgIdCreatedAt,
                    th.orgLevelCreatedAt,
                    th.departmentCode,
                    th.functionCode,
                    th.description,
                    th.lineOrderId,
                    th.isManuallyAdded,
                    th.changeId,
                    th.alterCode,
                    th.tdUpdated,
                    first_name = r != null ? r.first_name : string.Empty,
                    last_name = r != null ? r.last_name : string.Empty,
                    th.tdUpdatedBy,
                    th.priority,
                    th.tags,
                    th.goalTags,
                    bp.pk_budget_phase_id
                }
                into grp
                select new ActionGridColumns
                {
                    orgId = "",
                    orgName = "",
                    serviceId = "",
                    serviceName = "",
                    actionType = grp.Key.actionType,
                    actionId = grp.Key.actionId,
                    departmentCode = grp.Key.departmentCode,
                    functionCode = grp.Key.functionCode,
                    year1Amount = grp.Sum(x => x.year1Amount),
                    year2Amount = grp.Sum(x => x.year2Amount),
                    year3Amount = grp.Sum(x => x.year3Amount),
                    year4Amount = grp.Sum(x => x.year4Amount),
                    year5Amount = grp.Sum(x => x.year5Amount),
                    year6Amount = grp.Sum(x => x.year6Amount),
                    year7Amount = grp.Sum(x => x.year7Amount),
                    year8Amount = grp.Sum(x => x.year8Amount),
                    year9Amount = grp.Sum(x => x.year9Amount),
                    year10Amount = grp.Sum(x => x.year10Amount),
                    description = grp.Key.description,
                    changeId = grp.Key.changeId,
                    alterCode = grp.Key.alterCode,
                    isManuallyAdded = grp.Key.isManuallyAdded,
                    tdUpdated = grp.Key.tdUpdated,
                    lineOrderId = grp.Key.lineOrderId,
                    tdUpdatedBy = string.IsNullOrEmpty(grp.Key.first_name)
                        ? string.Empty
                        : grp.Key.first_name + " " + grp.Key.last_name,
                    priority = grp.Key.priority,
                    tags = grp.Key.tags,
                    goalTags = grp.Key.goalTags,
                    orgIdCreatedAt = grp.Key.orgIdCreatedAt,
                    orgLevelCreatedAt = grp.Key.orgLevelCreatedAt,
                    budgetPhase = grp.Key.pk_budget_phase_id
                }).ToList();
            End("3341: dd", sbLogs);

            Start("3406: departFunctFilterData", sbLogs);
            var departFunctFilterData = await GetDepartmentFuncFilterData(userId, input);
            End("3406: departFunctFilterData", sbLogs);

            // filter data with chapterId
            Start("3411", sbLogs);
            bool isChapterSetup = await _finUtility.isChapterSetup(userId);
            if (isChapterSetup && input.orgLevel == 1)
            {
                input.chapterId =
                    string.IsNullOrEmpty(input.chapterId) || input.chapterId.Trim().ToLower() == "ALL".ToLower()
                        ? string.Empty
                        : input.chapterId;
                int orgLevel = input.orgLevel != null ? (int)input.orgLevel : 0;
                var relationDepartments =
                    await _finUtility.GetAttributeConnectedDepartmentsForSelectedOrgAsync(userId, input.budgetYear,
                        input.orgId, orgLevel, input.chapterId);
                if (departFunctFilterData.depatments.Any(x => !string.IsNullOrEmpty(x)))
                {
                    dd = dd.Where(y => relationDepartments.Contains(y.departmentCode)).ToList();
                }
            }

            End("3411", sbLogs);

            if (departFunctFilterData.functions.Any(x => !string.IsNullOrEmpty(x)))
            {
                dd = dd.Where(y => departFunctFilterData.functions.Contains(y.functionCode)).ToList();
            }

            Start("Line No. 3430", sbLogs);
            if (input.filters.Any())
            {
                dd = GetFilteredDataSet(userId, dd, input.filters, input.orgLevel.Value, input.orgId, input.budgetYear);
            }

            End("Line No. 3430", sbLogs);

            Start("Line No. 3437 lstAggregates", sbLogs);
            List<clsAggregates> lstAggregates =
                GetGroupedDataSet(userId, dd, input, activechageId, input.ColToDisplay, false, false);
            End("Line No. 3437 lstAggregates", sbLogs);

            ActionGridFormatData finalDataSet = new();
            Start("Line No. 3442 finplanActions", sbLogs);
            var finplanActions = await FormatActionGridData(userDetails.user_name,
                langString.FirstOrDefault(v => v.Key == "BP_GridColumn_type1").Value.LangText, "TYPE-FP",
                lstAggregates.Where(z => actionTypes.Where(y => y != 21).Contains(z.actionID)).ToList(),
                input.budgetYear, input.filters);
            End("Line No. 3442 finplanActions", sbLogs);

            Start("Line No. 3446 operationExpenseActions", sbLogs);
            var operationExpenseActions = await FormatActionGridData(userDetails.user_name,
                langString.FirstOrDefault(v => v.Key == "BP_GridColumn_type1").Value.LangText, "TYPE-OE",
                lstAggregates.Where(z => z.actionID == 21).ToList(), input.budgetYear, input.filters);
            End("Line No. 3446 operationExpenseActions", sbLogs);

            Start("Line No. 3450 finalDataSet", sbLogs);
            finalDataSet.data.AddRange(finplanActions.data);
            finalDataSet.data.AddRange(operationExpenseActions.data);
            finalDataSet.typeList.AddRange(finplanActions.typeList);
            finalDataSet.typeList.AddRange(operationExpenseActions.typeList);
            finalDataSet.alterCodeList.AddRange(finplanActions.alterCodeList);
            finalDataSet.alterCodeList.AddRange(operationExpenseActions.alterCodeList);
            finalDataSet.actionStatusList.AddRange(finplanActions.actionStatusList);
            finalDataSet.actionStatusList.AddRange(operationExpenseActions.actionStatusList);
            finalDataSet.priorityList.AddRange(finplanActions.priorityList);
            finalDataSet.priorityList.AddRange(operationExpenseActions.priorityList);
            finalDataSet.orgCreatedList.AddRange(finplanActions.orgCreatedList);
            finalDataSet.orgCreatedList.AddRange(operationExpenseActions.orgCreatedList);
            End("Line No. 3450 finalDataSet", sbLogs);
            finalDataSet.Information = sbLogs.ToString();
            return finalDataSet;
        }

        public async Task<dynamic> GetActionGridColumnSelector(string userId, int budgetYear, string noOfYearsSelector,
            bool isChangeColumns)
        {
            UserData user = await _utility.GetUserDetailsAsync(userId);
            bool isTenantSyncSetup = await _dataSyncUtility.IsTenantSyncSetup(user.tenant_id, user.client_id);
            var tenantData = await _finUtility.GetSyncSubTenantData(user.tenant_id);
            bool isMainTenant = tenantData.Any();
            dynamic columsToDisplayList = new JObject();
            dynamic columsToDisplayListNew = new JObject();
            List<KeyValuePair> columsconfig = new List<KeyValuePair>();
            List<string> allColumns = new List<string>
            {
                "type", "actionLog", "title", "orgCreatedAt", "actionStatus", "priority", "altercode", "lastUpdatedBy"
            };
            List<string> defaultCol = new List<string>()
            {
                "type", "actionLog", "title", "orgCreatedAt", "actionStatus", "altercode", "year1Amount", "year2Amount",
                "year3Amount", "year4Amount"
            };
            if (isTenantSyncSetup && isMainTenant)
            {
                allColumns.Add("evaluationStatus");
            }

            if (isTenantSyncSetup && !isMainTenant)
            {
                allColumns.Add("syncStatus");
            }

            //finYear
            for (int i = 1; i <= 10; i++)
            {
                allColumns.Add($"year{i}Amount");
            }

            for (int i = 1; i <= 10; i++)
            {
                allColumns.Add($"changeYear{i}Amount");
            }

            string noOfYearsSelected = string.Empty;
            string yearFlagName = "SELECTED_YEAR_CHOICE_ACTION_GRID";
            var yearSelectorTenant =
                (await _unitOfWork.FinancingProjectRepository.GetTcoApplicationFlagDataAsync(user.tenant_id,
                    yearFlagName, budgetYear, 0)).FirstOrDefault();
            if (string.IsNullOrEmpty(noOfYearsSelector) && yearSelectorTenant != null)
            {
                //get selected year value from blob when year is not sent
                noOfYearsSelected =
                    await _finProj.GetYearConfigFromBlobTableAsync(user, noOfYearsSelected, yearSelectorTenant);
            }
            else if (string.IsNullOrEmpty(noOfYearsSelector) && yearSelectorTenant == null)
            {
                noOfYearsSelected = "4";
            }
            else
            {
                noOfYearsSelected = noOfYearsSelector;
            }

            string flagName = GetFlagForYearSelector(noOfYearsSelected);
            //get data from db
            var columnSelectorTenant =
                (await _unitOfWork.FinancingProjectRepository.GetTcoApplicationFlagDataAsync(user.tenant_id, flagName,
                    budgetYear, 0)).FirstOrDefault();
            //get the colum selctor value form blob
            columsconfig = await _finProj.GetColumConfigFromBlobTableAsync(user, columsconfig, columnSelectorTenant);
            //format the columlist
            columsToDisplayListNew = await FormatColumSelector(userId, allColumns, defaultCol, columsconfig,
                noOfYearsSelected, isChangeColumns, budgetYear);
            return columsToDisplayListNew;
        }

        public async Task SaveActionGridColumnsConfig(string userId, string strJsonColumnSet, int budgetYear,
            string noOfYearsSelector, bool isChangeColumns)
        {
            Guid finColSelectGuid = Guid.NewGuid();
            Guid yearSelectGuid = Guid.NewGuid();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext tendbContext = await _utility.GetTenantDBContextAsync();
            string yearFlagName = "SELECTED_YEAR_CHOICE_ACTION_GRID";
            var yearSelectorTenant =
                (await _unitOfWork.FinancingProjectRepository.GetTcoApplicationFlagDataAsync(userDetails.tenant_id,
                    yearFlagName, budgetYear, 0)).FirstOrDefault();

            if (yearSelectorTenant != null)
            {
                if (yearSelectorTenant.flag_guid == null)
                {
                    yearSelectorTenant.flag_guid = yearSelectGuid;
                    await tendbContext.SaveChangesAsync();
                }
                else
                {
                    yearSelectGuid = yearSelectorTenant.flag_guid.Value;
                }
            }
            else
            {
                //Create new record
                tco_application_flag tafrec = new tco_application_flag();
                tafrec.fk_tenant_id = userDetails.tenant_id;
                tafrec.flag_name = yearFlagName;
                tafrec.flag_guid = yearSelectGuid;
                tafrec.flag_key_id = "0";
                tafrec.flag_status = 0;
                tafrec.budget_year = budgetYear;
                tafrec.period = 0;
                tafrec.updated_by = userDetails.pk_id;
                tafrec.updated = DateTime.UtcNow;

                await tendbContext.tco_application_flag.AddAsync(tafrec);
                await tendbContext.SaveChangesAsync();
            }

            string flagName = GetFlagForYearSelector(noOfYearsSelector);
            var columnSelectorTenant =
                (await _unitOfWork.InvestmentProjectRepository.GetTcoApplicationFlagDataAsync(userDetails.tenant_id,
                    flagName, budgetYear, 0)).FirstOrDefault();

            if (columnSelectorTenant != null)
            {
                if (columnSelectorTenant.flag_guid == null)
                {
                    columnSelectorTenant.flag_guid = finColSelectGuid;
                    await tendbContext.SaveChangesAsync();
                }
                else
                {
                    finColSelectGuid = columnSelectorTenant.flag_guid.Value;
                }
            }
            else
            {
                //Create new record
                tco_application_flag tafrec = new tco_application_flag();
                tafrec.fk_tenant_id = userDetails.tenant_id;
                tafrec.flag_name = flagName;
                tafrec.flag_guid = finColSelectGuid;
                tafrec.flag_key_id = "0";
                tafrec.flag_status = 0;
                tafrec.budget_year = budgetYear;
                tafrec.period = 0;
                tafrec.updated_by = userDetails.pk_id;
                tafrec.updated = DateTime.UtcNow;

                await tendbContext.tco_application_flag.AddAsync(tafrec);
                await tendbContext.SaveChangesAsync();
            }

            await _finProj.SaveColumnSelectConfigInBlobStoreAsync(userDetails.pk_id, finColSelectGuid, yearSelectGuid,
                strJsonColumnSet, noOfYearsSelector, userDetails.tenant_id);
        }
    }
}