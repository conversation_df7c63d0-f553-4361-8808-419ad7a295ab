#pragma warning disable CS8602 // Dereference of a possibly null reference.
#pragma warning disable CS8604 // Possible null reference argument.
#pragma warning disable CS8601


using Aspose.Cells;
using Framsikt.BL.Core;
using Framsikt.BL.Helpers;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.Linq.Dynamic.Core;
using System.Security.Cryptography;
using static Framsikt.BL.Helpers.clsConstants;

namespace Framsikt.BL
{
    public class ActivityIndicatorImport : IActivityIndicatorImport
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IUtility _utility;
        private readonly IBackendRequest _backendJob;
        private readonly IAzureBlobHelper _azureBlobHelper;
        private readonly IImportUtility _importUtility;
        private readonly IAzureBlobHelper _blobHelper;
        private const decimal _activityResultNullHolderValue = -1111111111111111111;

        public ActivityIndicatorImport(IUnitOfWork uow, IServiceProvider container)
        {
            _unitOfWork = uow;
            _utility = container.GetRequiredService<IUtility>();
            _backendJob = container.GetRequiredService<IBackendRequest>();
            _azureBlobHelper = container.GetRequiredService<IAzureBlobHelper>();
            _importUtility = container.GetRequiredService<IImportUtility>();
            _blobHelper = container.GetRequiredService<IAzureBlobHelper>();
        }

        public async Task<ActivityIndicatorImportDataHelper> GetActivityIndicatorImportGridData(string userId, int budgetYear, int skip, int take, bool isErrorToggle, long jobId = -1)
        {
             UserData userData = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userData.language_preference, userData.user_name, "CommonImport");
            var stagedData = await _unitOfWork.ActivityIndicatorImportRepository.GetActivityIndicatorStageData(userData.pk_id, userData.tenant_id, budgetYear, jobId);
            if (isErrorToggle)
                stagedData = stagedData.Where(x => x.error_count > 0).ToList();
            List<ActivityIndicatorImportGridDataHelper> data = (from a in stagedData
                                                                select new ActivityIndicatorImportGridDataHelper
                                                                {
                                                                    pk_id = a.pk_id,
                                                                    id = a.ID.Trim(),
                                                                    id_error = a.id_error,
                                                                    Name = string.IsNullOrEmpty(a.Name.Trim()) ? langStringValues.FirstOrDefault(v => v.Key == "import_activity_doesnot_exist").Value.LangText : a.Name.Trim(),
                                                                    orgId = a.org_id.Trim(),
                                                                    orgId_error = a.org_id_error,
                                                                    orgLevel = a.org_level,
                                                                    orgLevel_error = a.org_level_error,
                                                                    serviceId = a.service_id.Trim(),
                                                                    serviceId_error = a.service_id_error,
                                                                    period = a.period,
                                                                    period_error = a.period_error,
                                                                    result = a.result != null ? a.result  : _activityResultNullHolderValue,
                                                                    result_error = a.result_error,
                                                                    error_count = a.error_count,
                                                                    type = a.type
                                                                }).Skip(skip).Take(take).ToList();

            ActivityIndicatorImportDataHelper result = new ActivityIndicatorImportDataHelper()
            {
                data = data,
                totalCount = stagedData.Count()
            };
            return result;
        }

        public async Task DeleteStagedActivityIndicatorValuesByIds(string userId, List<long> Ids, long jobId = -1)
        {
            UserData userData = await _utility.GetUserDetailsAsync(userId);
            await _unitOfWork.ActivityIndicatorImportRepository.DeleteActivityIndicatorImportStageDataByIds(userData.pk_id, userData.tenant_id, Ids, jobId);
        }

        public async Task DeleteAllActivityIndicators(string userId, int budgetYear, long jobId = -1)
        {
            UserData userData = await _utility.GetUserDetailsAsync(userId);
            await _unitOfWork.ActivityIndicatorImportRepository.DeleteActivityIndicatorImportBudgetAllStageData(userData.pk_id, userData.tenant_id, budgetYear, jobId);
            await _unitOfWork.ActivityIndicatorImportRepository.DeleteJob(jobId);
            await _utility.DeleteCompletedJobsAsync(userId, UserTrackedJobs.ActivityIndicatorImport);
        }

        public async Task ImportExcelToActivityIndicatorStagetTable(string userId, int budgetYear, Workbook wb)
        {
            Worksheet ws = wb.Worksheets[0];
            ExportTableOptions opt = new ExportTableOptions
            {
                ExportColumnName = true,
                FormatStrategy = CellValueFormatStrategy.CellStyle,
                ExportAsString = false
            };

            // here we have to delete completed jobs
            await _utility.DeleteCompletedJobsAsync(userId, UserTrackedJobs.ActivityIndicatorImport);

            List<string> columnNames = GetActivityIndicatorColumnNames(userId).Result.ToList();
            for (int i = 0; i < columnNames.Count; i++)
            {
                ws.Cells[0, i].Value = columnNames[i];
            }

            int pkIdColNo = columnNames.IndexOf("ID");
            int orgIdColNo = columnNames.IndexOf("org_id");
            int orgLevelColNo = columnNames.IndexOf("org_level");
            int serviceIdlColNo = columnNames.IndexOf("service_id");
            int periodColNo = columnNames.IndexOf("period");
            int resultColNo = columnNames.IndexOf("result");

            string paramValueFp1 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_1");
            string paramValueFp2 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_2");
            bool isServiceSetup = (!string.IsNullOrEmpty(paramValueFp2) && (paramValueFp2.StartsWith("ser")) || (!string.IsNullOrEmpty(paramValueFp1) && (paramValueFp1.StartsWith("ser"))));
            for (int i = 0; i < ws.Cells.MaxDataRow + 1; i++)
            {
                ws.Cells[i, pkIdColNo].Value = _importUtility.ReturnString(ws, ws.Cells[i, pkIdColNo].Value, i, pkIdColNo);
                ws.Cells[i, orgLevelColNo].Value = _importUtility.ReturnString(ws, ws.Cells[i, orgLevelColNo].Value, i, orgLevelColNo);
                ws.Cells[i, orgIdColNo].Value = _importUtility.ReturnString(ws, ws.Cells[i, orgIdColNo].Value, i, orgIdColNo);
                if (isServiceSetup)
                {
                    ws.Cells[i, serviceIdlColNo].Value = _importUtility.ReturnString(ws, ws.Cells[i, serviceIdlColNo].Value, i, serviceIdlColNo);
                }
                ws.Cells[i, periodColNo].Value = _importUtility.ReturnString(ws, ws.Cells[i, periodColNo].Value, i, periodColNo);
            }

            //Determine number of rows to import
            //From the last row, move up
            //This gives the number of rows to be imported
            int rowsToImport = ws.Cells.MaxDataRow;
            var tbl = ws.Cells.ExportDataTable(0, 0, rowsToImport + 1, ws.Cells.MaxDataColumn + 1, opt);
            tbl.AcceptChanges();

            DataSet result = new DataSet();
            result.Tables.Add(tbl);

            UserData userInfo = await _utility.GetUserDetailsAsync(userId);

            //Format the data set for import
            DataColumn tenantIdCol = new DataColumn("fk_tenant_id")
            {
                DataType = typeof(int),
                DefaultValue = userInfo.tenant_id
            };
            tbl.Columns.Add(tenantIdCol);

            DataColumn userIdCol = new DataColumn("userId")
            {
                DataType = typeof(int),
                DefaultValue = userInfo.pk_id
            };
            tbl.Columns.Add(userIdCol);

            DataColumn updated = new DataColumn("updated")
            {
                DataType = typeof(DateTime),
                DefaultValue = DateTime.UtcNow
            };
            tbl.Columns.Add(updated);

            DataColumn updated_by = new DataColumn("updated_by")
            {
                DataType = typeof(int),
                DefaultValue = userInfo.pk_id
            };
            tbl.Columns.Add(updated_by);

            DataColumn Name = new DataColumn("Name")
            {
                DataType = typeof(string),
                DefaultValue = ""
            };
            tbl.Columns.Add(Name);

            DataColumn budgetyear = new DataColumn("budget_year")
            {
                DataType = typeof(int),
                DefaultValue = budgetYear
            };
            tbl.Columns.Add(budgetyear);

            DataColumn job_id = new DataColumn("fk_job_id")
            {
                DataType = typeof(long),
                DefaultValue = -1
            };
            tbl.Columns.Add(job_id);
            if (!isServiceSetup)
            {
                DataColumn serviceID = new DataColumn("service_id")
                {
                    DataType = typeof(string),
                    DefaultValue = ""
                };
                tbl.Columns.Add(serviceID);
            }
            //Import the data into the staging table
            using (SqlBulkCopy bulkCopy = new SqlBulkCopy(await _utility.GetTenantDbConnectionAsync(userInfo.tenant_id))
            { DestinationTableName = "dbo.tco_stage_activity_indicators" })
            {
                foreach (string colName in columnNames)
                {
                    bulkCopy.ColumnMappings.Add(colName, colName);
                }
                bulkCopy.ColumnMappings.Add("fk_tenant_id", "fk_tenant_id");
                bulkCopy.ColumnMappings.Add("userId", "userId");
                bulkCopy.ColumnMappings.Add("updated", "updated");
                bulkCopy.ColumnMappings.Add("updated_by", "updated_by");
                bulkCopy.ColumnMappings.Add("Name", "Name");
                bulkCopy.ColumnMappings.Add("budget_year", "budget_year");
                bulkCopy.ColumnMappings.Add("fk_job_id", "fk_job_id");
                if (!isServiceSetup)
                {
                    bulkCopy.ColumnMappings.Add("service_id", "service_id");
                }
                await bulkCopy.WriteToServerAsync(tbl);
            }
            await ValidateImportedActivityIndicatorData(userId, budgetYear);
        }

        public async Task UpdateImportedActivityIndicatorStageData(string userId, List<ActivityIndicatorImportGridDataHelper> data, int budgetYear, long jobId = -1)
        {
            UserData userData = await _utility.GetUserDetailsAsync(userId);

            var dataToUpdate = await _unitOfWork.ActivityIndicatorImportRepository.GetActivityIndicatorStageDataByIds(userData.tenant_id, data.Select(x => x.pk_id).ToList());

            foreach (var row in dataToUpdate)
            {
                var item = data.Where(x => x.pk_id == row.pk_id).FirstOrDefault();
                row.ID = item.id;
                row.service_id = string.IsNullOrEmpty(item.serviceId) ? string.Empty : item.serviceId;
                row.period = item.period;
                row.result = item.result;
                row.org_id = string.IsNullOrEmpty(item.orgId) ? "" : item.orgId;
                row.org_level = item.orgLevel;
                row.updated = DateTime.UtcNow;
            }

            await _unitOfWork.ActivityIndicatorImportRepository.SaveChanges();

            await ValidateImportedActivityIndicatorData(userId, budgetYear);
        }

        public async Task<ColumnInfo> GetActivityIndicatorColumnInfo(string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "CommonImport");
            List<string> colNames = new List<string>();
            colNames = await GetActivityIndicatorColumnNames(userId);
            ColumnInfo columnInfo = new ColumnInfo();

            foreach (string colName in colNames)
            {
                switch (colName)
                {
                    case "ID":
                        columnInfo.Fields.Add("ID");
                        columnInfo.Titles.Add("ID*");
                        columnInfo.DataTypes.Add("string");
                        break;

                    case "org_id":
                        columnInfo.Fields.Add("org_id");
                        columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "import_activity_orgId").Value.LangText + "*");
                        columnInfo.DataTypes.Add("string");
                        break;

                    case "org_level":
                        columnInfo.Fields.Add("org_level");
                        columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "import_activity_orgLevel").Value.LangText + "*");
                        columnInfo.DataTypes.Add("integer");
                        break;

                    case "service_id":
                        columnInfo.Fields.Add("service_id");
                        columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "import_activity_serviceId").Value.LangText);
                        columnInfo.DataTypes.Add("string");
                        break;

                    case "period":
                        columnInfo.Fields.Add("period");
                        columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "import_activity_period").Value.LangText);
                        columnInfo.DataTypes.Add("integer");
                        break;

                    case "result":
                        columnInfo.Fields.Add("result");
                        columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "import_activity_result").Value.LangText + "*");
                        columnInfo.DataTypes.Add("integer");
                        break;
                }
            }
            return columnInfo;
        }

        public async Task WriteToActivityIndicatorImportQueue(string userId, long jobId, int budgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            long jobIdToconsider = -1;
            bool isRequestByJobId = false;

            if (jobId == -1)
            {
                TcoJobStatus jobStatus = await _utility.GetJobProgressAsync(userId, UserTrackedJobs.ActivityIndicatorImport);
                if (jobStatus != null)
                    _unitOfWork.GenericRepo.Delete(jobStatus);

                jobStatus = new TcoJobStatus
                {
                    JobType = UserTrackedJobs.ActivityIndicatorImport.ToString(),
                    StartTime = DateTime.UtcNow,
                    TotalSteps = 100,
                    StepsCompleted = 0,
                    UserId = userDetails.pk_id,
                    TenantId = userDetails.tenant_id
                };

                _unitOfWork.GenericRepo.Add(jobStatus);
                await _unitOfWork.CompleteAsync();

                jobIdToconsider = jobStatus.PkId;
                isRequestByJobId = false;
            }
            else
            {    //else us the job it from dropdown and create job queue
                jobIdToconsider = jobId;
                isRequestByJobId = true;

                var jobStatToUpdate = await _utility.GetJobProgressByJobIdAsync(userId, UserTrackedJobs.ActivityIndicatorImport, jobId);

                if (jobStatToUpdate != null)
                {
                    jobStatToUpdate.StartTime = DateTime.UtcNow;
                    jobStatToUpdate.TotalSteps = 100;
                    jobStatToUpdate.StepsCompleted = 0;
                    jobStatToUpdate.jobStatus = StatusEnumData.InProgress.ToString();

                    await _unitOfWork.ActivityIndicatorImportRepository.SaveChanges();
                }

                //var stagingData = _unitOfWork.ActivityIndicatorImportRepository.GetActivityIndicatorStageData(userDetails.pk_id, userDetails.tenant_id, jobId, budgetYear).Result.ToList();
                //var reqObject = getDetailsFromBlob(jobStatToUpdate.request_data_blob_url);
                //ObjectValuesBlobHelper inputData = new ObjectValuesBlobHelper()
                //{
                //    numberOfRows = stagingData.Count,
                //    request_data_blob_url = jobStatToUpdate.request_data_blob_url,
                //};

                //InsertJobReqDataToBlob(inputData);
            }

            dynamic activityIndicatorImportRequest = new JObject();
            activityIndicatorImportRequest.Add("UserId", userId);
            activityIndicatorImportRequest.Add("TenantId", userDetails.tenant_id);
            activityIndicatorImportRequest.Add("JobId", jobIdToconsider);
            activityIndicatorImportRequest.Add("isRequestByJobId", isRequestByJobId);
            activityIndicatorImportRequest.Add("BudgetYear", budgetYear);

            string strActivityIndicatorImportRequest = JsonConvert.SerializeObject(activityIndicatorImportRequest);

            _backendJob.QueueMessage(userDetails, new Dictionary<string, int>(), QueueName.importactivityindicatorqueue, strActivityIndicatorImportRequest);
        }
        private List<GroupedIndicatorData> GroupStageDataBasedOnIndicatorId(List<tco_stage_activity_indicators> activityIndicators)
        {
            var data = (from a in activityIndicators
                        group new { a } by new { a.ID, a.org_id, a.org_level, a.budget_year, a.service_id, a.type } into g
                        select new GroupedIndicatorData
                        {
                            ID = g.Key.ID,
                            org_id = g.Key.org_id,
                            org_level = g.Key.org_level,
                            budgetYear = g.Key.budget_year,
                            service_id = g.Key.service_id,
                            type = g.Key.type,
                            PeriodList = g.Select(x => new PeriodValue { period = x.a.period, result = Convert.ToDecimal(x.a.result) }).ToList()
                        }).ToList();
            return data;
        }
        public async Task ImportActivityIndicatorValuesFromStaging(string userId, long jobId, bool isRequestByJobId, int budgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            CultureInfo cultIn = CultureInfoFactory.CreateCulture(userDetails.language_preference);
            var indicatorStagedData = isRequestByJobId ? await _unitOfWork.ActivityIndicatorImportRepository.GetActivityIndicatorStageData(userDetails.pk_id, userDetails.tenant_id, budgetYear, jobId)
                                                       : await _unitOfWork.ActivityIndicatorImportRepository.GetActivityIndicatorStageData(userDetails.pk_id, userDetails.tenant_id, budgetYear, -1);

            var indicatorSetUpData = isRequestByJobId ? await _unitOfWork.ActivityIndicatorImportRepository.GetActivityIndicatorSetupData(userDetails.pk_id, userDetails.tenant_id, jobId, budgetYear)
                                                      : await _unitOfWork.ActivityIndicatorImportRepository.GetActivityIndicatorSetupData(userDetails.pk_id, userDetails.tenant_id, -1, budgetYear);

           
            var TmdIndicResult = indicatorSetUpData.Where(x => x.pk_indicator_code == x.fk_indicator_code).ToList();
            if (indicatorStagedData == null)
                return;


            List<string> framsiktIndiCodelIst = indicatorStagedData.Select(z => z.ID).Distinct().ToList();
                 var allIndicatoreSetupData = await _unitOfWork.ActivityIndicatorImportRepository.GetAllActivityIndicatorSetupData(userDetails.tenant_id, framsiktIndiCodelIst);
            //Check that error count is 0
            int failedCount = indicatorStagedData.Count(x => x.error_count > 0);

            if (failedCount > 0)
            {
                throw new DataException("Cannot import as errors exist");
            }

            TcoJobStatus jobStatus = new TcoJobStatus();
            var indicatorStagedGrouped = GroupStageDataBasedOnIndicatorId(indicatorStagedData);
            jobStatus = _utility.GetJobProgressByJobId(userId, UserTrackedJobs.ActivityIndicatorImport, jobId);
            if (jobStatus != null)
            {
                jobStatus.StartTime = DateTime.UtcNow;
                jobStatus.jobStatus = (isRequestByJobId) ? StatusEnumData.InProgress.ToString() : string.Empty;
                jobStatus.EndTime = null;
                jobStatus.TotalSteps = indicatorStagedGrouped.Count();
                jobStatus.StepsCompleted = 0;
                await tenantDbContext.SaveChangesAsync();
            }
            try
            {
                List<tmd_indicator_results> newIndicators = new List<tmd_indicator_results>();
                List<tco_stage_activity_indicators> stageDataToDelete = new List<tco_stage_activity_indicators>();
                int counter = 0;
                List<tmd_indicator_results>listTocalculate= new();
                List<tmd_indicator_results>listTocalculateForType1= new();
               
                foreach (var stageRow in indicatorStagedGrouped)
                {
                    var indSetupData = allIndicatoreSetupData.Where(x => x.fk_framsikt_indicator_code == stageRow.ID.Trim()).FirstOrDefault();
                    var TcoIndicatorSetupValue = TmdIndicResult.Where(x => x.id == stageRow.ID.Trim() && x.org_id == stageRow.org_id && x.org_level == stageRow.org_level).Distinct().ToList();
                    if (!TcoIndicatorSetupValue.Any())
                    {
                        tmd_indicator_results newDataToSave = new tmd_indicator_results
                        {
                            pk_id = Guid.NewGuid(),
                            fk_indicator_id = indSetupData.pk_indicator_code,//indicatorSetUpData.Where(x => x.id == stageRow.ID.Trim()).FirstOrDefault().pk_indicator_code,
                            fk_tenant_id = userDetails.tenant_id,
                            budget_year = budgetYear,
                            org_level = stageRow.org_level,
                            org_id = stageRow.org_id,
                            service_id = stageRow.service_id,
                            updated = DateTime.UtcNow,
                            updated_by = userDetails.pk_id,
                            is_reported = false
                        };
                        if (stageRow.type == 1)
                        {
                            newDataToSave.total_year = stageRow.PeriodList.Sum(x => x.result).ToString();
                            newDataToSave.period_1 = string.Empty;
                            newDataToSave.period_2 = string.Empty;
                            newDataToSave.period_3 = string.Empty;
                            newDataToSave.period_4 = string.Empty;
                            newDataToSave.period_5 = string.Empty;
                            newDataToSave.period_6 = string.Empty;
                            newDataToSave.period_7 = string.Empty;
                            newDataToSave.period_8 = string.Empty;
                            newDataToSave.period_9 = string.Empty;
                            newDataToSave.period_10 = string.Empty;
                            newDataToSave.period_11 = string.Empty;
                            newDataToSave.period_12 = string.Empty;
                        }
                        else
                        {
                            newDataToSave.total_year = string.Empty;
                           
                            newDataToSave.period_1 = (stageRow.PeriodList.Any(x => x.period % 100 == 1)) ? (stageRow.PeriodList.FirstOrDefault(x => x.period % 100 == 1).result).ToString() : "0";
                            newDataToSave.period_2 = (stageRow.PeriodList.Any(x => x.period % 100 == 2)) ? (stageRow.PeriodList.FirstOrDefault(x => x.period % 100 == 2).result).ToString() : "0";
                            newDataToSave.period_3 = (stageRow.PeriodList.Any(x => x.period % 100 == 3)) ? (stageRow.PeriodList.FirstOrDefault(x => x.period % 100 == 3).result).ToString() : "0";
                            newDataToSave.period_4 = (stageRow.PeriodList.Any(x => x.period % 100 == 4)) ? (stageRow.PeriodList.FirstOrDefault(x => x.period % 100 == 4).result).ToString() : "0";
                            newDataToSave.period_5 = (stageRow.PeriodList.Any(x => x.period % 100 == 5)) ? (stageRow.PeriodList.FirstOrDefault(x => x.period % 100 == 5).result).ToString() : "0";
                            newDataToSave.period_6 = (stageRow.PeriodList.Any(x => x.period % 100 == 6))? (stageRow.PeriodList.FirstOrDefault(x => x.period % 100 == 6).result).ToString() : "0";
                            newDataToSave.period_7 = (stageRow.PeriodList.Any(x => x.period % 100 == 7)) ? (stageRow.PeriodList.FirstOrDefault(x => x.period % 100 == 7).result).ToString() : "0";
                            newDataToSave.period_8 = (stageRow.PeriodList.Any(x => x.period % 100 == 8)) ? (stageRow.PeriodList.FirstOrDefault(x => x.period % 100 == 8).result).ToString() : "0";
                            newDataToSave.period_9 = (stageRow.PeriodList.Any(x => x.period % 100 == 9)) ? (stageRow.PeriodList.FirstOrDefault(x => x.period % 100 == 9).result).ToString() : "0";
                            newDataToSave.period_10 = (stageRow.PeriodList.Any(x => x.period % 100 == 10)) ? (stageRow.PeriodList.FirstOrDefault(x => x.period % 100 == 10).result).ToString() : "0";
                            newDataToSave.period_11 = (stageRow.PeriodList.Any(x => x.period % 100 == 11)) ? (stageRow.PeriodList.FirstOrDefault(x => x.period % 100 == 11).result).ToString() : "0";
                            newDataToSave.period_12 = (stageRow.PeriodList.Any(x => x.period % 100 == 12))? (stageRow.PeriodList.FirstOrDefault(x => x.period % 100 == 12).result).ToString(): "0";
                            CalculateTotalYearValue(newDataToSave, stageRow, indSetupData.aggregation_activity_reporting, true);
                        }
                        newIndicators.Add(newDataToSave);

                        if (indSetupData.is_reporting_aggregation && stageRow.org_level == indSetupData.reporting_level && ((isRequestByJobId && indSetupData.result_data_source == (int)ReportingResultSource.ApiImport) || (!isRequestByJobId && indSetupData.result_data_source == (int)ReportingResultSource.ExcelImport)))// result data source is API  or ExcelImport and reporting aggregation enable in admin screen
                        {
                            if(stageRow.type == 1)
                            {
                                listTocalculateForType1.Add(newDataToSave);
                            }
                            else
                            {
                                listTocalculate.Add(newDataToSave);
                            }
                        }
                    }
                    else
                    {
                        var existingRow = await _unitOfWork.ActivityIndicatorImportRepository.GetExistingIndicatorResult(userDetails.tenant_id, budgetYear, TcoIndicatorSetupValue.FirstOrDefault().resultPkId);
                        if (existingRow != null)
                        {
                            if (stageRow.type == 1)
                            {
                                existingRow.total_year = decimal.TryParse(existingRow.total_year, out decimal amount) ? decimal.Parse(stageRow.PeriodList.Sum(x => x.result).ToString(), CultureInfo.InvariantCulture).ToString() : existingRow.total_year;
                            }
                            else
                            {
                                if (stageRow.PeriodList.Any(x => x.period % 100 == 1))
                                {
                                    existingRow.period_1 = stageRow.PeriodList.FirstOrDefault(x => x.period % 100 == 1).result.ToString();
                                }
                                if (stageRow.PeriodList.Any(x => x.period % 100 == 2))
                                {
                                    existingRow.period_2 = stageRow.PeriodList.FirstOrDefault(x => x.period % 100 == 2).result.ToString();
                                }
                                if (stageRow.PeriodList.Any(x => x.period % 100 == 3))
                                {
                                    existingRow.period_3 = stageRow.PeriodList.FirstOrDefault(x => x.period % 100 == 3).result.ToString();
                                }
                                if (stageRow.PeriodList.Any(x => x.period % 100 == 4))
                                {
                                    existingRow.period_4 = stageRow.PeriodList.FirstOrDefault(x => x.period % 100 == 4).result.ToString();
                                }
                                if (stageRow.PeriodList.Any(x => x.period % 100 == 5))
                                {
                                    existingRow.period_5 = stageRow.PeriodList.FirstOrDefault(x => x.period % 100 == 5).result.ToString();
                                }
                                if (stageRow.PeriodList.Any(x => x.period % 100 == 6))
                                {
                                    existingRow.period_6 = stageRow.PeriodList.FirstOrDefault(x => x.period % 100 == 6).result.ToString();
                                }
                                if (stageRow.PeriodList.Any(x => x.period % 100 == 7))
                                {
                                    existingRow.period_7 = stageRow.PeriodList.FirstOrDefault(x => x.period % 100 == 7).result.ToString();
                                }
                                if (stageRow.PeriodList.Any(x => x.period % 100 == 8))
                                {
                                    existingRow.period_8 = stageRow.PeriodList.FirstOrDefault(x => x.period % 100 == 8).result.ToString();
                                }
                                if (stageRow.PeriodList.Any(x => x.period % 100 == 9))
                                {
                                    existingRow.period_9 = stageRow.PeriodList.FirstOrDefault(x => x.period % 100 == 9).result.ToString();
                                }
                                if (stageRow.PeriodList.Any(x => x.period % 100 == 10))
                                {
                                    existingRow.period_10 = stageRow.PeriodList.FirstOrDefault(x => x.period % 100 == 10).result.ToString();
                                }
                                if (stageRow.PeriodList.Any(x => x.period % 100 == 11))
                                {
                                    existingRow.period_11 = stageRow.PeriodList.FirstOrDefault(x => x.period % 100 == 11).result.ToString();
                                }
                                if (stageRow.PeriodList.Any(x => x.period % 100 == 12))
                                {
                                    existingRow.period_12 = stageRow.PeriodList.FirstOrDefault(x => x.period % 100 == 12).result.ToString();
                                }
                                CalculateTotalYearValue(existingRow, stageRow, TcoIndicatorSetupValue[0].aggregation_activity_type,false);
                            }
                            if (indSetupData.is_reporting_aggregation && stageRow.org_level == indSetupData.reporting_level && ((isRequestByJobId && indSetupData.result_data_source == (int)ReportingResultSource.ApiImport) || (!isRequestByJobId && indSetupData.result_data_source == (int)ReportingResultSource.ExcelImport)))// result data source is API  or ExcelImport and reporting aggregation enable in admin screen
                            {
                                if (stageRow.type == 1)
                                {
                                    listTocalculateForType1.Add(existingRow);
                                }
                                else
                                {
                                    listTocalculate.Add(existingRow);
                                }
                            }
                            existingRow.updated = DateTime.UtcNow;
                            existingRow.updated_by = userDetails.pk_id;
                        }
                    }
                    List<tco_stage_activity_indicators> stageRows = indicatorStagedData.Where(x => x.ID == stageRow.ID).ToList();
                    stageDataToDelete.AddRange(stageRows);
                    jobStatus.StepsCompleted = jobStatus.StepsCompleted + 1;
                    await tenantDbContext.SaveChangesAsync();

                    if (++counter % 500 == 0)
                    {
                        await tenantDbContext.tmd_indicator_results.BulkInsertAsync(newIndicators);
                        await tenantDbContext.BulkDeleteAsync(stageDataToDelete);
                        await tenantDbContext.SaveChangesAsync();

                        newIndicators.RemoveRange(0, newIndicators.Count);
                        stageDataToDelete.RemoveRange(0, stageDataToDelete.Count);
                    }
                }

                await tenantDbContext.tmd_indicator_results.BulkInsertAsync(newIndicators);
                await tenantDbContext.BulkDeleteAsync(stageDataToDelete);
                var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, ((budgetYear*100)+1));

                foreach (var item in listTocalculate) {
                    var allTmDresultData = await (_unitOfWork.ActivityIndicatorImportRepository.GetAllActivityIndicatorTMDData(userDetails.tenant_id, budgetYear, item.fk_indicator_id));
                    OrgInpLevels orgInput= SetOrgInput(item.org_id,item.org_level, orgVersionContent);
                    int aggregationType = allIndicatoreSetupData.FirstOrDefault(z => z.pk_indicator_code == item.fk_indicator_id).reporting_aggregation_type;
                    int activityAggregationType = allIndicatoreSetupData.FirstOrDefault(z => z.pk_indicator_code == item.fk_indicator_id).aggregation_activity_reporting;
                    await ComputeAggregationToUppLevel(userId, item,  orgVersionContent, item.org_level,  budgetYear, allTmDresultData, aggregationType, activityAggregationType, orgInput, false);// calculate aggregation to upper level
                }

                foreach (var item in listTocalculateForType1)
                {
                    var allTmDresultData = await (_unitOfWork.ActivityIndicatorImportRepository.GetAllActivityIndicatorTMDData(userDetails.tenant_id, budgetYear, item.fk_indicator_id));
                    OrgInpLevels orgInput = SetOrgInput(item.org_id, item.org_level, orgVersionContent);
                    int aggregationType = allIndicatoreSetupData.FirstOrDefault(z => z.pk_indicator_code == item.fk_indicator_id).reporting_aggregation_type;
                    int activityAggregationType = allIndicatoreSetupData.FirstOrDefault(z => z.pk_indicator_code == item.fk_indicator_id).aggregation_activity_reporting;
                    await ComputeAggregationToUppLevel(userId, item, orgVersionContent, item.org_level, budgetYear, allTmDresultData, aggregationType, activityAggregationType, orgInput, true);// calculate aggregation to upper level
                }

                //job completed
                jobStatus.EndTime = DateTime.UtcNow;
                jobStatus.jobStatus = isRequestByJobId ? StatusEnumData.CompletedWithoutErrors.ToString() : string.Empty;
                await tenantDbContext.SaveChangesAsync();

                newIndicators.RemoveRange(0, newIndicators.Count);
                stageDataToDelete.RemoveRange(0, stageDataToDelete.Count);
            }
            catch (Exception e)
            {
                jobStatus.EndTime = DateTime.UtcNow;
                jobStatus.jobStatus = isRequestByJobId ? StatusEnumData.CompletedWithErrors.ToString() : string.Empty;
                jobStatus.message = e.Message;
                await tenantDbContext.SaveChangesAsync();
                throw;
            }
        }

        public OrgInpLevels SetOrgInput(string org_id, int org_level, ClsOrgVersionSpecificContent orgVersionContent)
        {
            OrgInpLevels orgValue= new OrgInpLevels();
            switch (org_level)
            {
                case 1:
                    orgValue.level1OrgId= org_id;break;

                case 2:
                    orgValue.level1OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z=>z.org_id_2== org_id).org_id_1;
                    orgValue.level2OrgId = org_id;
                    break;
                case 3:
                    orgValue.level1OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_3 == org_id).org_id_1;
                    orgValue.level2OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_3 == org_id).org_id_2;
                    orgValue.level3OrgId = org_id ;
                    break;
                case 4:
                    orgValue.level1OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_4 == org_id).org_id_1;
                    orgValue.level2OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_4== org_id).org_id_2;
                    orgValue.level3OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_4 == org_id).org_id_3; 
                    orgValue.level4OrgId = org_id;
                    break;
                case 5:
                    orgValue.level1OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_5 == org_id).org_id_1;
                    orgValue.level2OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_5 == org_id).org_id_2;
                    orgValue.level3OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_5 == org_id).org_id_3;
                    orgValue.level4OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_5 == org_id).org_id_4; 
                    orgValue.level5OrgId = org_id;
                    break;

                case 6:
                    orgValue.level1OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_6 == org_id).org_id_1;
                    orgValue.level2OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_6 == org_id).org_id_2;
                    orgValue.level3OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_6 == org_id).org_id_3;
                    orgValue.level4OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_6 == org_id).org_id_4;
                    orgValue.level5OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_6 == org_id).org_id_5;
                    orgValue.level6OrgId = org_id;
                    break;
                case 7:
                    orgValue.level1OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_7 == org_id).org_id_1;
                    orgValue.level2OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_7 == org_id).org_id_2;
                    orgValue.level3OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_7 == org_id).org_id_3;
                    orgValue.level4OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_7 == org_id).org_id_4;
                    orgValue.level5OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_7 == org_id).org_id_5;
                    orgValue.level6OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_7 == org_id).org_id_6;
                    orgValue.level7OrgId = org_id;
                    break;
                case 8:
                    orgValue.level1OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_8 == org_id).org_id_1;
                    orgValue.level2OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_8 == org_id).org_id_2;
                    orgValue.level3OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_8 == org_id).org_id_3;
                    orgValue.level4OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_8 == org_id).org_id_4;
                    orgValue.level5OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_8 == org_id).org_id_5;
                    orgValue.level6OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_8 == org_id).org_id_6;
                    orgValue.level7OrgId = orgVersionContent.lstOrgHierarchy.FirstOrDefault(z => z.org_id_8 == org_id).org_id_7;
                    orgValue.level8OrgId = org_id;
                    break;


            }

            return orgValue;
        }

        private void CalculateTotalYearValue(tmd_indicator_results existingRow, GroupedIndicatorData stageRow, int aggregationType, bool isNewRow)
        {
            List<PeriodValue> periodResult = new List<PeriodValue>();
            periodResult.Add(new PeriodValue { period = 1, result = (decimal.TryParse(existingRow.period_1, out decimal amt1) ? decimal.Parse(existingRow.period_1, CultureInfo.InvariantCulture) : 0) });
            periodResult.Add(new PeriodValue { period = 2, result = (decimal.TryParse(existingRow.period_2, out decimal amt2) ? decimal.Parse(existingRow.period_2, CultureInfo.InvariantCulture) : 0) });
            periodResult.Add(new PeriodValue { period = 3, result = (decimal.TryParse(existingRow.period_3, out decimal amt3) ? decimal.Parse(existingRow.period_3, CultureInfo.InvariantCulture) : 0) });
            periodResult.Add(new PeriodValue { period = 4, result = (decimal.TryParse(existingRow.period_4, out decimal amt4) ? decimal.Parse(existingRow.period_4, CultureInfo.InvariantCulture) : 0) });
            periodResult.Add(new PeriodValue { period = 5, result = (decimal.TryParse(existingRow.period_5, out decimal amt5) ? decimal.Parse(existingRow.period_5, CultureInfo.InvariantCulture) : 0) });
            periodResult.Add(new PeriodValue { period = 6, result = (decimal.TryParse(existingRow.period_6, out decimal amt6) ? decimal.Parse(existingRow.period_6, CultureInfo.InvariantCulture) : 0) });
            periodResult.Add(new PeriodValue { period = 7, result = (decimal.TryParse(existingRow.period_7, out decimal amt7) ? decimal.Parse(existingRow.period_7, CultureInfo.InvariantCulture) : 0) });
            periodResult.Add(new PeriodValue { period = 8, result = (decimal.TryParse(existingRow.period_8, out decimal amt8) ? decimal.Parse(existingRow.period_8, CultureInfo.InvariantCulture) : 0) });
            periodResult.Add(new PeriodValue { period = 9, result = (decimal.TryParse(existingRow.period_9, out decimal amt9) ? decimal.Parse(existingRow.period_9, CultureInfo.InvariantCulture) : 0) });
            periodResult.Add(new PeriodValue { period = 10, result = (decimal.TryParse(existingRow.period_10, out decimal amt10) ? decimal.Parse(existingRow.period_10, CultureInfo.InvariantCulture) : 0) });
            periodResult.Add(new PeriodValue { period = 11, result = (decimal.TryParse(existingRow.period_11, out decimal amt11) ? decimal.Parse(existingRow.period_11, CultureInfo.InvariantCulture) : 0) });
            periodResult.Add(new PeriodValue { period = 12, result = (decimal.TryParse(existingRow.period_12, out decimal amt12) ? decimal.Parse(existingRow.period_12, CultureInfo.InvariantCulture) : 0) });
            periodResult = periodResult.Where(x => x.result != 0).ToList();
            switch (aggregationType)
            {
                case (int)ActivityAggrTypes.LastReported:
                    if(periodResult.Any())
                    {
                        var lastPeriod = periodResult.Max(x => x.period) % 100;
                        var forecastPeriod = stageRow.budgetYear * 100 + lastPeriod;
                        var selectedForecastPeriod = forecastPeriod % 100;
                        existingRow.total_year = periodResult.Find(x => x.period == selectedForecastPeriod).result.ToString();
                    }
                    else
                    {
                        existingRow.total_year = string.Empty;
                    }
                    
                    break;

                case (int)ActivityAggrTypes.Average:
                    if (periodResult.Any())
                    {
                        existingRow.total_year = (periodResult.Sum(x => x.result) / periodResult.Count()).ToString();
                    }
                    else
                    {
                        existingRow.total_year = string.Empty;
                    }  
                    break;

                case (int)ActivityAggrTypes.Sum:   
                    if(stageRow.PeriodList.Any())
                    {
                        existingRow.total_year = (decimal.Parse(stageRow.PeriodList.Sum(x => x.result).ToString(), CultureInfo.InvariantCulture)).ToString();
                    }
                    else
                    {
                        existingRow.total_year = string.Empty;
                    }
                    break;
            };
        }
        public async Task ValidateImportedActivityIndicatorData(string userId, int budgetYear, long jobId = -1)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();

                var spValidate = new prcValidateImportActivityIndicator
                {
                    user_id = userDetails.pk_id,
                    tenant_id = userDetails.tenant_id,
                    job_id = jobId,
                    budget_year = budgetYear,
                };

                await _unitOfWork.ActivityIndicatorImportRepository.ExecuteActivityIndictorValidateProcedure(spValidate);
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<DataTable> GetActivityIndicatorDataForExport(string userId, int skip, int take, long jobId, int budgetYear)
        {
            UserData userData = await _utility.GetUserDetailsAsync(userId);
            List<string> columns = new List<string>();
            columns = await GetActivityIndicatorColumnNames(userId);
            var stageData = await _unitOfWork.ActivityIndicatorImportRepository.GetActivityIndicatorStageData(userData.pk_id, userData.tenant_id, budgetYear, jobId);

            var finalData = stageData.Skip(skip).Take(take).ToList();

            DataTable table = _utility.ConvertToDataTable(finalData);

            List<DataColumn> toBeDeleted = new List<DataColumn>();
            //Remove unwanted columns
            foreach (DataColumn col in table.Columns)
            {
                if (!columns.Contains(col.ColumnName))
                {
                    toBeDeleted.Add(col);
                }
            }

            foreach (DataColumn colDel in toBeDeleted)
            {
                table.Columns.Remove(colDel);
            }
            //re-order  columns as per columns collection
            int i = 0;
            foreach (string col in columns)
            {
                table.Columns[col].SetOrdinal(i);
                i++;
            }
            return table;
        }

        public async Task<List<TcoJobsHelper>> GetJobIdsActivityIndicatorimport(string userId, int budgetYear)
        {
            UserData userData = await _utility.GetUserDetailsAsync(userId);
            CultureInfo ci = CultureInfoFactory.CreateCulture(userData.language_preference);
            List<TcoJobsHelper> transactionData = new List<TcoJobsHelper>();
            List<string> jobStatusList = new List<string>() { StatusEnumData.NotStarted.ToString(), StatusEnumData.StagingValidationError.ToString(), StatusEnumData.CompletedWithErrors.ToString() };
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userData.language_preference, userData.user_name, "CommonImport");
            string importLangString = langStringValues["import_activityIndic_text"].LangText;

            var jobs = await _unitOfWork.ActivityIndicatorImportRepository.GetJobIdsDataActivityIndicatorImport(userData.tenant_id, jobStatusList);

            var tempData = (from a in jobs
                            where !string.IsNullOrEmpty(a.request_data_blob_url)
                            select new JobDataHelper
                            {
                                PkId = a.PkId.ToString(),
                                JobType = a.JobType,
                                StartTime = a.StartTime,
                                BlobUrl = a.request_data_blob_url ?? string.Empty
                            }).DistinctBy(j=>j.PkId).ToList();
            List<JobDataHelper> jobData = new List<JobDataHelper>();
            foreach (var job in tempData)
            {
                var reqObject = await GetDetailsFromBlob(job.BlobUrl);// get the blob data
                if (reqObject != null && !string.IsNullOrEmpty(reqObject["BudgetYear"].ToString()) && (int)reqObject["BudgetYear"] == budgetYear)
                {
                    transactionData.Add(new TcoJobsHelper()
                    {
                        blob_url = job.BlobUrl,
                        Key = !string.IsNullOrEmpty(job.PkId) ? job.PkId : "0",
                        Value = (job.PkId).ToString() + "-" + importLangString,
                        startTime = job.StartTime
                    });
                }
            }
            return transactionData.OrderByDescending(x => long.Parse(x.Key)).ToList();
        }

        public async Task<ImportInfo> GetImportInfo(string userId, long jobId, int budgetYear)
        {
            UserData userData = await _utility.GetUserDetailsAsync(userId);
            var result = new ImportInfo();
            var actIndicatorData = await _unitOfWork.ActivityIndicatorImportRepository.GetActivityIndicatorStageDataByJobId(userData.pk_id, userData.tenant_id, jobId, budgetYear);
            var actIndicatorErrorList = actIndicatorData.Where(x => x.error_count > 0).ToList();
            result.FailedCount = actIndicatorErrorList.Select(x => x.error_count).Sum();
            result.TotalRows = actIndicatorData.Count();
            return result;
        }

        private async Task<List<string>> GetActivityIndicatorColumnNames(string userId)
        {
            string paramValueFp1 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_1");
            string paramValueFp2 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_2");
            bool isServiceSetup = (!string.IsNullOrEmpty(paramValueFp2) && (paramValueFp2.StartsWith("ser")) || (!string.IsNullOrEmpty(paramValueFp1) && (paramValueFp1.StartsWith("ser"))));

            List<string> colNames = new List<string>();
            colNames.Add("ID");
            colNames.Add("org_level");
            colNames.Add("org_id");
            if (isServiceSetup)
            {
                colNames.Add("service_id");
            }
            colNames.Add("period");
            colNames.Add("result");
            return colNames;
        }

        private async Task<JObject> GetDetailsFromBlob(string blobURL)
        {
            try
            {
                var text = await _blobHelper.GetTextBlobAsync(StorageAccount.AppStorage, BlobContainers.TcoJobsRequestObjectData, blobURL);

                JObject json = JObject.Parse(text);
                JObject tcojobrequestobjectdata = new JObject
                {
                    { "BudgetYear", json["BudgetYear"] },
                    { "NumberOfRows", json["NumberOfRows"] },
                    { "RequestDataBlobUrl", json["RequestDataBlobUrl"] }
                };
                return tcojobrequestobjectdata;
            }
            catch (Exception e)
            {
                if (e.Message == "Blob not found")
                {
#pragma warning disable CS8603 // Possible null reference return.
                    return null;
#pragma warning restore CS8603 // Possible null reference return.
                }
                else
                {
                    throw;
                }
            }
        }

        public async Task ComputeAggregationToUppLevel(string userId, tmd_indicator_results dataToSave,  ClsOrgVersionSpecificContent orgVersionContent, int orgLevel,int budgetYear,List<tmd_indicator_results> allTmdResult,int reportingAggregationType,int activityAggregationType, OrgInpLevels orgInput, bool isIndicatorType1)
        {


            List<string> reportingOrgIds = new List<string>();
            List<string> reportingUpperOrgIds = new List<string>();
            switch (orgLevel)
            {
                case 2:
                    reportingUpperOrgIds = orgVersionContent.lstOrgHierarchy.Where(z => z.org_id_2 ==
                    orgInput.level2OrgId).Select(z => z.org_id_1).Distinct().ToList();

                    reportingOrgIds = orgVersionContent.lstOrgHierarchy.Where(z => z.org_id_1 ==
                    orgInput.level1OrgId).Select(z => z.org_id_2).Distinct().ToList(); break;

                case 3:
                    reportingUpperOrgIds = orgVersionContent.lstOrgHierarchy.Where(z => z.org_id_3 ==
                    orgInput.level3OrgId).Select(z => z.org_id_2).Distinct().ToList();

                    reportingOrgIds = orgVersionContent.lstOrgHierarchy.Where(z => z.org_id_2 ==
                    orgInput.level2OrgId).Select(z => z.org_id_3).Distinct().ToList(); break;

                case 4:
                    reportingUpperOrgIds = orgVersionContent.lstOrgHierarchy.Where(z => z.org_id_4 ==
                    orgInput.level4OrgId).Select(z => z.org_id_3).Distinct().ToList();

                    reportingOrgIds = orgVersionContent.lstOrgHierarchy.Where(z => z.org_id_3 ==
                    orgInput.level3OrgId).Select(z => z.org_id_4).Distinct().ToList(); break;

                case 5:
                    reportingUpperOrgIds = orgVersionContent.lstOrgHierarchy.Where(z => z.org_id_5 ==
                   orgInput.level5OrgId).Select(z => z.org_id_4).Distinct().ToList();

                    reportingOrgIds = orgVersionContent.lstOrgHierarchy.Where(z => z.org_id_4 ==
                    orgInput.level4OrgId).Select(z => z.org_id_5).Distinct().ToList(); break;

                case 6:
                    reportingUpperOrgIds = orgVersionContent.lstOrgHierarchy.Where(z => z.org_id_6 ==
                    orgInput.level6OrgId).Select(z => z.org_id_5).Distinct().ToList();

                    reportingOrgIds = orgVersionContent.lstOrgHierarchy.Where(z => z.org_id_5 ==
                    orgInput.level5OrgId).Select(z => z.org_id_6.ToString()).Distinct().ToList(); break;

                case 7:
                    reportingUpperOrgIds = orgVersionContent.lstOrgHierarchy.Where(z => z.org_id_7 ==
                   orgInput.level7OrgId).Select(z => z.org_id_6.ToString()).Distinct().ToList();

                    reportingOrgIds = orgVersionContent.lstOrgHierarchy.Where(z => z.org_id_6 ==
                    orgInput.level6OrgId).Select(z => z.org_id_7.ToString()).Distinct().ToList(); break;

                case 8:
                    reportingUpperOrgIds = orgVersionContent.lstOrgHierarchy.Where(z => z.org_id_8 ==
                   orgInput.level8OrgId).Select(z => z.org_id_7.ToString()).Distinct().ToList();

                    reportingOrgIds = orgVersionContent.lstOrgHierarchy.Where(z => z.org_id_7 ==
                    orgInput.level7OrgId).Select(z => z.org_id_8.ToString()).Distinct().ToList(); break;

            }

            UserData userdata = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            Guid indCode = dataToSave.fk_indicator_id;

            var reprtingActivityList = await GetReportingOrgLevelsForIndicators(userdata.tenant_id, budgetYear, orgLevel, indCode, reportingOrgIds, isIndicatorType1);
            var reprtingUpperLeveActivityList = await GetReportingOrgLevelsForIndicators(userdata.tenant_id, budgetYear, (orgLevel - 1), indCode, reportingUpperOrgIds, isIndicatorType1);

            var reportingTmdResultOrgIds = reprtingActivityList.Select(x => x.orgId).Distinct().ToList();

            List<tmd_indicator_results> toUpdate = new List<tmd_indicator_results>();
            List<tmd_indicator_results> toInsert = new List<tmd_indicator_results>();
            
                if (reprtingActivityList.Any(z => z.indicatorCode == indCode))
                {

                    var activityIdsToCompute = allTmdResult.Where(z => z.fk_indicator_id == indCode && z.org_level == orgLevel && reportingTmdResultOrgIds.Contains(z.org_id)).Select(x => x.pk_id).Distinct().ToList();// get all reported current activity at current level

                    var activityToUpdate = reprtingUpperLeveActivityList.Where(z => z.indicatorCode == indCode).Distinct().ToList();// get all upper level activity except parent activity id

                    foreach (var ac in activityToUpdate)
                    {
                        var upperLevelTmdActivity = allTmdResult.FirstOrDefault(z => z.fk_indicator_id == ac.indicatorCode && z.org_id== ac.orgId && z.org_level==ac.orgLevel && z.budget_year== budgetYear);// fetch parent level activity
                        if (upperLevelTmdActivity != null)// update if the entry exist
                        {

                            if (reportingAggregationType == (int)ActivityAggrTypes.Average)// reporting type is sum
                            {
                                if (isIndicatorType1)
                                {
                                    CommputeAverageFromLowerLevelType1(out upperLevelTmdActivity, activityIdsToCompute, allTmdResult, ac, dataToSave);
                                }
                                else
                                {
                                    CommputeAverageFromLowerLevel(out upperLevelTmdActivity, activityIdsToCompute, allTmdResult, ac, dataToSave, activityAggregationType);
                                }
                            }
                            else
                            {
                                if (isIndicatorType1)
                                {
                                    CommputeSumFromLowerLevelType1(out upperLevelTmdActivity, activityIdsToCompute, allTmdResult, ac, dataToSave);
                                }
                                else
                                {
                                    CommputeSumFromLowerLevel(out upperLevelTmdActivity, activityIdsToCompute, allTmdResult, ac, dataToSave, activityAggregationType);
                                }
                            }
                            upperLevelTmdActivity.updated = DateTime.UtcNow;
                            upperLevelTmdActivity.updated_by = userdata.pk_id;
                            toUpdate.Add(upperLevelTmdActivity);
                        }
                        else// insert new
                        {
                            tmd_indicator_results newTmdActivity = new tmd_indicator_results();
                       
                            if (reportingAggregationType == (int)ActivityAggrTypes.Average)// reporting type is average
                            {
                                if (isIndicatorType1)
                                {
                                    CommputeAverageFromLowerLevelType1(out newTmdActivity, activityIdsToCompute, allTmdResult, ac, dataToSave);
                                }
                                else
                                {
                                    CommputeAverageFromLowerLevel(out newTmdActivity, activityIdsToCompute, allTmdResult, ac, dataToSave, activityAggregationType);
                                }
                            }
                            else
                            {
                                if (isIndicatorType1)
                                {
                                    CommputeSumFromLowerLevelType1(out newTmdActivity, activityIdsToCompute, allTmdResult, ac, dataToSave);
                                }
                                else
                                {
                                    CommputeSumFromLowerLevel(out newTmdActivity, activityIdsToCompute, allTmdResult, ac, dataToSave, activityAggregationType);
                                }
                            }
                            newTmdActivity.fk_indicator_id = indCode;
                            newTmdActivity.org_id = ac.orgId;
                            newTmdActivity.org_level = ac.orgLevel;
                            newTmdActivity.service_id = activityToUpdate.FirstOrDefault().serviceId;
                            newTmdActivity.fk_attribute_id = activityToUpdate.FirstOrDefault().attributeId;
                            newTmdActivity.fk_tenant_id = userdata.tenant_id;
                            //newTmdActivity.pk_id = Guid.NewGuid();
                            newTmdActivity.updated = DateTime.UtcNow;
                            newTmdActivity.updated_by = userdata.pk_id;
                            toInsert.Add(newTmdActivity);
                        }
                    }
                }


            if (toUpdate.Any())
            {
                await dbContext.tmd_indicator_results.BulkUpdateAsync(toUpdate);
            }
            if (toInsert.Any())
            {
                await dbContext.tmd_indicator_results.BulkInsertAsync(toInsert);
            }         
            await dbContext.SaveChangesAsync();
            allTmdResult = await (_unitOfWork.ActivityIndicatorImportRepository.GetAllActivityIndicatorTMDData(userdata.tenant_id, budgetYear, indCode));
            if (orgLevel > 1)
                await ComputeAggregationToUppLevel(userId, dataToSave, orgVersionContent, (orgLevel - 1), budgetYear, allTmdResult, reportingAggregationType, activityAggregationType, orgInput, isIndicatorType1);// compute parent level ammount
        }

        private void CommputeSumFromLowerLevel(out tmd_indicator_results tmdActivity, List<Guid> activityIdsToCompute, List<tmd_indicator_results> tmdActivityList, GoalTargetIndicatorHelper tcoActivity, tmd_indicator_results dataToSave,int activityAggregationType)
        {
            List<PeriodValue> periodResult = new List<PeriodValue>();
            var tmdList = tmdActivityList.Where(z => activityIdsToCompute.Contains(z.pk_id)).ToList();

            tmdActivity = tmdActivityList.FirstOrDefault(z =>  z.fk_indicator_id == tcoActivity.indicatorCode && z.org_id == tcoActivity.orgId && z.org_level == tcoActivity.orgLevel && z.budget_year == dataToSave.budget_year) != null ? tmdActivityList.FirstOrDefault(z => z.fk_indicator_id == tcoActivity.indicatorCode && z.org_id == tcoActivity.orgId && z.org_level == tcoActivity.orgLevel && z.budget_year == dataToSave.budget_year) : new tmd_indicator_results() { fk_indicator_id = tcoActivity.indicatorCode, fk_tenant_id = tmdActivityList.FirstOrDefault().fk_tenant_id, budget_year = dataToSave.budget_year,pk_id = Guid.NewGuid() };
            foreach (var item in tmdList)
            {
                periodResult.Add(new PeriodValue { period = 1, result = (decimal.TryParse(item.period_1, out decimal amt1) ? decimal.Parse(item.period_1, CultureInfo.InvariantCulture) : 0) });
                periodResult.Add(new PeriodValue { period = 2, result = (decimal.TryParse(item.period_2, out decimal amt2) ? decimal.Parse(item.period_2, CultureInfo.InvariantCulture) : 0) });
                periodResult.Add(new PeriodValue { period = 3, result = (decimal.TryParse(item.period_3, out decimal amt3) ? decimal.Parse(item.period_3, CultureInfo.InvariantCulture) : 0) });
                periodResult.Add(new PeriodValue { period = 4, result = (decimal.TryParse(item.period_4, out decimal amt4) ? decimal.Parse(item.period_4, CultureInfo.InvariantCulture) : 0) });
                periodResult.Add(new PeriodValue { period = 5, result = (decimal.TryParse(item.period_5, out decimal amt5) ? decimal.Parse(item.period_5, CultureInfo.InvariantCulture) : 0) });
                periodResult.Add(new PeriodValue { period = 6, result = (decimal.TryParse(item.period_6, out decimal amt6) ? decimal.Parse(item.period_6, CultureInfo.InvariantCulture) : 0) });
                periodResult.Add(new PeriodValue { period = 7, result = (decimal.TryParse(item.period_7, out decimal amt7) ? decimal.Parse(item.period_7, CultureInfo.InvariantCulture) : 0) });
                periodResult.Add(new PeriodValue { period = 8, result = (decimal.TryParse(item.period_8, out decimal amt8) ? decimal.Parse(item.period_8, CultureInfo.InvariantCulture) : 0) });
                periodResult.Add(new PeriodValue { period = 9, result = (decimal.TryParse(item.period_9, out decimal amt9) ? decimal.Parse(item.period_9, CultureInfo.InvariantCulture) : 0) });
                periodResult.Add(new PeriodValue { period = 10, result = (decimal.TryParse(item.period_10, out decimal amt10) ? decimal.Parse(item.period_10, CultureInfo.InvariantCulture) : 0) });
                periodResult.Add(new PeriodValue { period = 11, result = (decimal.TryParse(item.period_11, out decimal amt11) ? decimal.Parse(item.period_11, CultureInfo.InvariantCulture) : 0) });
                periodResult.Add(new PeriodValue { period = 12, result = (decimal.TryParse(item.period_12, out decimal amt12) ? decimal.Parse(item.period_12, CultureInfo.InvariantCulture) : 0) });
                periodResult = periodResult.Where(x => x.result != 0).ToList();
            }


            tmdActivity.period_1 = (periodResult.Where(z => z.period == 1).Sum(z => z.result)).ToString();
            tmdActivity.period_2 = (periodResult.Where(z => z.period == 2).Sum(z => z.result)).ToString();
            tmdActivity.period_3 = (periodResult.Where(z => z.period == 3).Sum(z => z.result)).ToString();
            tmdActivity.period_4 = (periodResult.Where(z => z.period == 4).Sum(z => z.result)).ToString();
            tmdActivity.period_5 = (periodResult.Where(z => z.period == 5).Sum(z => z.result)).ToString();
            tmdActivity.period_6 = (periodResult.Where(z => z.period == 6).Sum(z => z.result)).ToString();
            tmdActivity.period_7 = (periodResult.Where(z => z.period == 7).Sum(z => z.result)).ToString();
            tmdActivity.period_8 = (periodResult.Where(z => z.period == 8).Sum(z => z.result)).ToString();
            tmdActivity.period_9 = (periodResult.Where(z => z.period == 9).Sum(z => z.result)).ToString();
            tmdActivity.period_10 = (periodResult.Where(z => z.period == 10).Sum(z => z.result)).ToString();
            tmdActivity.period_11 = (periodResult.Where(z => z.period == 11).Sum(z => z.result)).ToString();
            tmdActivity.period_12 = (periodResult.Where(z => z.period == 12).Sum(z => z.result)).ToString();



            tmdActivity.total_year = CalculateTotalYearValueUpperLevel(tmdActivity, dataToSave.budget_year, activityAggregationType);// (periodResult.Sum(x => x.result)).ToString();

        }

        private void CommputeAverageFromLowerLevel(out tmd_indicator_results tmdActivity,List<Guid> activityIdsToCompute,  List<tmd_indicator_results> tmdActivityList, GoalTargetIndicatorHelper tcoActivity, tmd_indicator_results dataToSave,int activityAggregationType)
        {
            List<PeriodValue> periodResult = new List<PeriodValue>();
            var tmdList = tmdActivityList.Where(z => activityIdsToCompute.Contains(z.pk_id)).ToList();
            tmdActivity = tmdActivityList.FirstOrDefault(z => z.fk_indicator_id == tcoActivity.indicatorCode && z.org_id== tcoActivity.orgId && z.org_level== tcoActivity.orgLevel && z.budget_year== dataToSave.budget_year) != null ? tmdActivityList.FirstOrDefault(z => z.fk_indicator_id == tcoActivity.indicatorCode && z.org_id == tcoActivity.orgId && z.org_level == tcoActivity.orgLevel && z.budget_year == dataToSave.budget_year) : new tmd_indicator_results() { fk_indicator_id = tcoActivity.indicatorCode, fk_tenant_id = tmdActivityList.FirstOrDefault().fk_tenant_id, budget_year = dataToSave.budget_year,pk_id = Guid.NewGuid() };
            foreach (var item in tmdList)
            {
                periodResult.Add(new PeriodValue { period = 1, result = (decimal.TryParse(item.period_1, out decimal amt1) ? decimal.Parse(item.period_1, CultureInfo.InvariantCulture) : 0) });
                periodResult.Add(new PeriodValue { period = 2, result = (decimal.TryParse(item.period_2, out decimal amt2) ? decimal.Parse(item.period_2, CultureInfo.InvariantCulture) : 0) });
                periodResult.Add(new PeriodValue { period = 3, result = (decimal.TryParse(item.period_3, out decimal amt3) ? decimal.Parse(item.period_3, CultureInfo.InvariantCulture) : 0) });
                periodResult.Add(new PeriodValue { period = 4, result = (decimal.TryParse(item.period_4, out decimal amt4) ? decimal.Parse(item.period_4, CultureInfo.InvariantCulture) : 0) });
                periodResult.Add(new PeriodValue { period = 5, result = (decimal.TryParse(item.period_5, out decimal amt5) ? decimal.Parse(item.period_5, CultureInfo.InvariantCulture) : 0) });
                periodResult.Add(new PeriodValue { period = 6, result = (decimal.TryParse(item.period_6, out decimal amt6) ? decimal.Parse(item.period_6, CultureInfo.InvariantCulture) : 0) });
                periodResult.Add(new PeriodValue { period = 7, result = (decimal.TryParse(item.period_7, out decimal amt7) ? decimal.Parse(item.period_7, CultureInfo.InvariantCulture) : 0) });
                periodResult.Add(new PeriodValue { period = 8, result = (decimal.TryParse(item.period_8, out decimal amt8) ? decimal.Parse(item.period_8, CultureInfo.InvariantCulture) : 0) });
                periodResult.Add(new PeriodValue { period = 9, result = (decimal.TryParse(item.period_9, out decimal amt9) ? decimal.Parse(item.period_9, CultureInfo.InvariantCulture) : 0) });
                periodResult.Add(new PeriodValue { period = 10, result = (decimal.TryParse(item.period_10, out decimal amt10) ? decimal.Parse(item.period_10, CultureInfo.InvariantCulture) : 0) });
                periodResult.Add(new PeriodValue { period = 11, result = (decimal.TryParse(item.period_11, out decimal amt11) ? decimal.Parse(item.period_11, CultureInfo.InvariantCulture) : 0) });
                periodResult.Add(new PeriodValue { period = 12, result = (decimal.TryParse(item.period_12, out decimal amt12) ? decimal.Parse(item.period_12, CultureInfo.InvariantCulture) : 0) });
                periodResult = periodResult.Where(x => x.result != 0).ToList();
            }

            tmdActivity.period_1 = (periodResult.Where(z => z.period == 1 && z.result != 0).Any())?(periodResult.Where(z => z.period == 1).Sum(z => z.result)/ (periodResult.Where(z => z.period == 1 && z.result!=0).Count())).ToString():"0";
            tmdActivity.period_2 = (periodResult.Where(z => z.period == 2 && z.result != 0).Any()) ? (periodResult.Where(z => z.period == 2).Sum(z => z.result) / (periodResult.Where(z => z.period == 2 && z.result != 0).Count())).ToString() : "0";
            tmdActivity.period_3 = (periodResult.Where(z => z.period == 3 && z.result != 0).Any()) ? (periodResult.Where(z => z.period == 3).Sum(z => z.result) / (periodResult.Where(z => z.period == 3 && z.result != 0).Count())).ToString() : "0";
            tmdActivity.period_4 = (periodResult.Where(z => z.period == 4 && z.result != 0).Any()) ? (periodResult.Where(z => z.period == 4).Sum(z => z.result) / (periodResult.Where(z => z.period == 4 && z.result != 0).Count())).ToString() : "0";
            tmdActivity.period_5 = (periodResult.Where(z => z.period == 5 && z.result != 0).Any()) ? (periodResult.Where(z => z.period == 5).Sum(z => z.result) / (periodResult.Where(z => z.period == 5 && z.result != 0).Count())).ToString() : "0";
            tmdActivity.period_6 = (periodResult.Where(z => z.period == 6 && z.result != 0).Any()) ? (periodResult.Where(z => z.period == 6).Sum(z => z.result) / (periodResult.Where(z => z.period == 6 && z.result != 0).Count())).ToString() : "0";
            tmdActivity.period_7 = (periodResult.Where(z => z.period == 7 && z.result != 0).Any()) ? (periodResult.Where(z => z.period == 7).Sum(z => z.result) / (periodResult.Where(z => z.period == 7 && z.result != 0).Count())).ToString() : "0";
            tmdActivity.period_8 = (periodResult.Where(z => z.period == 8 && z.result != 0).Any()) ? (periodResult.Where(z => z.period == 8).Sum(z => z.result) / (periodResult.Where(z => z.period == 8 && z.result != 0).Count())).ToString() : "0";
            tmdActivity.period_9 = (periodResult.Where(z => z.period == 9 && z.result != 0).Any()) ? (periodResult.Where(z => z.period == 9).Sum(z => z.result) / (periodResult.Where(z => z.period == 9 && z.result != 0).Count())).ToString() : "0";
            tmdActivity.period_10 = (periodResult.Where(z => z.period == 10 && z.result != 0).Any()) ? (periodResult.Where(z => z.period == 10).Sum(z => z.result) / (periodResult.Where(z => z.period == 10 && z.result != 0).Count())).ToString() : "0";
            tmdActivity.period_11 = (periodResult.Where(z => z.period == 11 && z.result != 0).Any()) ? (periodResult.Where(z => z.period == 11).Sum(z => z.result) / (periodResult.Where(z => z.period == 11 && z.result != 0).Count())).ToString() : "0";
            tmdActivity.period_12 = (periodResult.Where(z => z.period == 12 && z.result != 0).Any()) ? (periodResult.Where(z => z.period == 12).Sum(z => z.result) / (periodResult.Where(z => z.period == 12 && z.result != 0).Count())).ToString() : "0";


            tmdActivity.total_year = CalculateTotalYearValueUpperLevel(tmdActivity, dataToSave.budget_year, activityAggregationType);// (periodResult.Sum(x => x.result) / periodResult.Where(z=>z.result!=0).Count()).ToString();
        }

        private string CalculateTotalYearValueUpperLevel(tmd_indicator_results existingRow, int budgetYear, int aggregationType)
        {
            string total_year = "0";
            List<PeriodValue> periodResult = new List<PeriodValue>();
            periodResult.Add(new PeriodValue { period = 1, result = (decimal.TryParse(existingRow.period_1, out decimal amt1) ? decimal.Parse(existingRow.period_1, CultureInfo.InvariantCulture) : 0) });
            periodResult.Add(new PeriodValue { period = 2, result = (decimal.TryParse(existingRow.period_2, out decimal amt2) ? decimal.Parse(existingRow.period_2, CultureInfo.InvariantCulture) : 0) });
            periodResult.Add(new PeriodValue { period = 3, result = (decimal.TryParse(existingRow.period_3, out decimal amt3) ? decimal.Parse(existingRow.period_3, CultureInfo.InvariantCulture) : 0) });
            periodResult.Add(new PeriodValue { period = 4, result = (decimal.TryParse(existingRow.period_4, out decimal amt4) ? decimal.Parse(existingRow.period_4, CultureInfo.InvariantCulture) : 0) });
            periodResult.Add(new PeriodValue { period = 5, result = (decimal.TryParse(existingRow.period_5, out decimal amt5) ? decimal.Parse(existingRow.period_5, CultureInfo.InvariantCulture) : 0) });
            periodResult.Add(new PeriodValue { period = 6, result = (decimal.TryParse(existingRow.period_6, out decimal amt6) ? decimal.Parse(existingRow.period_6, CultureInfo.InvariantCulture) : 0) });
            periodResult.Add(new PeriodValue { period = 7, result = (decimal.TryParse(existingRow.period_7, out decimal amt7) ? decimal.Parse(existingRow.period_7, CultureInfo.InvariantCulture) : 0) });
            periodResult.Add(new PeriodValue { period = 8, result = (decimal.TryParse(existingRow.period_8, out decimal amt8) ? decimal.Parse(existingRow.period_8, CultureInfo.InvariantCulture) : 0) });
            periodResult.Add(new PeriodValue { period = 9, result = (decimal.TryParse(existingRow.period_9, out decimal amt9) ? decimal.Parse(existingRow.period_9, CultureInfo.InvariantCulture) : 0) });
            periodResult.Add(new PeriodValue { period = 10, result = (decimal.TryParse(existingRow.period_10, out decimal amt10) ? decimal.Parse(existingRow.period_10, CultureInfo.InvariantCulture) : 0) });
            periodResult.Add(new PeriodValue { period = 11, result = (decimal.TryParse(existingRow.period_11, out decimal amt11) ? decimal.Parse(existingRow.period_11, CultureInfo.InvariantCulture) : 0) });
            periodResult.Add(new PeriodValue { period = 12, result = (decimal.TryParse(existingRow.period_12, out decimal amt12) ? decimal.Parse(existingRow.period_12, CultureInfo.InvariantCulture) : 0) });
            periodResult = periodResult.Where(x => x.result != 0).ToList();
            switch (aggregationType)
            {
                case (int)ActivityAggrTypes.LastReported:
                    var lastPeriod = periodResult.Max(x => x.period) % 100;
                    var forecastPeriod = budgetYear * 100 + lastPeriod;
                    var selectedForecastPeriod = forecastPeriod % 100;
                    total_year = periodResult.Find(x => x.period == selectedForecastPeriod).result.ToString();
                    break;

                case (int)ActivityAggrTypes.Average:
                    total_year = periodResult.Any()?(periodResult.Sum(x => x.result) / periodResult.Count()).ToString():"0";
                    break;

                case (int)ActivityAggrTypes.Sum:
                   
                        total_year = (decimal.Parse(periodResult.Sum(x => x.result).ToString(), CultureInfo.InvariantCulture)).ToString();
                    break;
            };
            return total_year;
        }

        private async Task<List<GoalTargetIndicatorHelper>> GetReportingOrgLevelsForIndicators(int tenantId, int budgetYear, int orgLevel, Guid indCode, List<string> reportingOrgIds, bool isIndicatorType1)
        {
            List<GoalTargetIndicatorHelper> reprtingActivityList = new();
            if (isIndicatorType1)
            {
                reprtingActivityList = await _unitOfWork.ActivityIndicatorImportRepository.GetAllGoalTargetIndicatorForIndicatorId(tenantId, indCode, orgLevel, reportingOrgIds);
            }
            else
            {
                reprtingActivityList = await _unitOfWork.ActivityIndicatorImportRepository.GetAllActivityIndicatorForIndicatorId(tenantId, budgetYear, indCode, orgLevel, reportingOrgIds);
            }
            return reprtingActivityList;
        }

        private void CommputeAverageFromLowerLevelType1(out tmd_indicator_results tmdActivity, List<Guid> activityIdsToCompute, List<tmd_indicator_results> tmdActivityList, GoalTargetIndicatorHelper tcoActivity, tmd_indicator_results dataToSave)
        {
            List<decimal> totalResult = new();
            var tmdList = tmdActivityList.Where(z => activityIdsToCompute.Contains(z.pk_id)).ToList();
            tmdActivity = tmdActivityList.FirstOrDefault(z => z.fk_indicator_id == tcoActivity.indicatorCode && z.org_id == tcoActivity.orgId && z.org_level == tcoActivity.orgLevel && z.budget_year == dataToSave.budget_year) != null ? tmdActivityList.FirstOrDefault(z => z.fk_indicator_id == tcoActivity.indicatorCode && z.org_id == tcoActivity.orgId && z.org_level == tcoActivity.orgLevel && z.budget_year == dataToSave.budget_year) : new tmd_indicator_results() { fk_indicator_id = tcoActivity.indicatorCode, fk_tenant_id = tmdActivityList.FirstOrDefault().fk_tenant_id, budget_year = dataToSave.budget_year, pk_id = Guid.NewGuid() };
            foreach (var item in tmdList)
            {
                totalResult.Add(decimal.TryParse(item.total_year, out decimal amt1) ? decimal.Parse(item.total_year, CultureInfo.InvariantCulture) : 0);
                totalResult = totalResult.Where(x => x != 0).ToList();
            }
            tmdActivity.period_1 = string.Empty;
            tmdActivity.period_2 = string.Empty;
            tmdActivity.period_3 = string.Empty;
            tmdActivity.period_4 = string.Empty;
            tmdActivity.period_5 = string.Empty;
            tmdActivity.period_6 = string.Empty;
            tmdActivity.period_7 = string.Empty;
            tmdActivity.period_8 = string.Empty;
            tmdActivity.period_9 = string.Empty;
            tmdActivity.period_10 = string.Empty;
            tmdActivity.period_11 = string.Empty;
            tmdActivity.period_12 = string.Empty;
            tmdActivity.total_year = totalResult.Any() ? (totalResult.Sum(z => z) / totalResult.Count()).ToString() : "0";
        }

        private void CommputeSumFromLowerLevelType1(out tmd_indicator_results tmdActivity, List<Guid> activityIdsToCompute, List<tmd_indicator_results> tmdActivityList, GoalTargetIndicatorHelper tcoActivity, tmd_indicator_results dataToSave)
        {
            List<decimal> totalResult = new();
            var tmdList = tmdActivityList.Where(z => activityIdsToCompute.Contains(z.pk_id)).ToList();

            tmdActivity = tmdActivityList.FirstOrDefault(z => z.fk_indicator_id == tcoActivity.indicatorCode && z.org_id == tcoActivity.orgId && z.org_level == tcoActivity.orgLevel && z.budget_year == dataToSave.budget_year) != null ? tmdActivityList.FirstOrDefault(z => z.fk_indicator_id == tcoActivity.indicatorCode && z.org_id == tcoActivity.orgId && z.org_level == tcoActivity.orgLevel && z.budget_year == dataToSave.budget_year) : new tmd_indicator_results() { fk_indicator_id = tcoActivity.indicatorCode, fk_tenant_id = tmdActivityList.FirstOrDefault().fk_tenant_id, budget_year = dataToSave.budget_year, pk_id = Guid.NewGuid() };
            foreach (var item in tmdList)
            {
                totalResult.Add(decimal.TryParse(item.total_year, out decimal amt1) ? decimal.Parse(item.total_year, CultureInfo.InvariantCulture) : 0);
                totalResult = totalResult.Where(x => x != 0).ToList();
            }
            tmdActivity.period_1 = string.Empty;
            tmdActivity.period_2 = string.Empty;
            tmdActivity.period_3 = string.Empty;
            tmdActivity.period_4 = string.Empty;
            tmdActivity.period_5 = string.Empty;
            tmdActivity.period_6 = string.Empty;
            tmdActivity.period_7 = string.Empty;
            tmdActivity.period_8 = string.Empty;
            tmdActivity.period_9 = string.Empty;
            tmdActivity.period_10 = string.Empty;
            tmdActivity.period_11 = string.Empty;
            tmdActivity.period_12 = string.Empty;
            tmdActivity.total_year = totalResult.Any() ? totalResult.Sum(z => z).ToString() : "0";
        }
    }
}