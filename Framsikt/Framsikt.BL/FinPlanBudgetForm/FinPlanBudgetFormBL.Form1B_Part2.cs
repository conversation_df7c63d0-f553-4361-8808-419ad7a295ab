using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;

namespace Framsikt.BL;

public partial class FinPlanBudgetFormBL
{

    public JObject GetFramsikt1BDataForWithFreeDimNodes(int budgetYear, string userId, int docType, int forecastPeriod = 0, string budgetphaseId = null, bool showOnlyModified = false, string fridimData = "", List<string> budgetPhaseIDs = null, List<string> freedim2Ids = null)
    {
        return GetFramsikt1BDataForWithFreeDimNodesAsync(budgetYear, userId, docType, forecastPeriod, budgetphaseId, showOnlyModified, fridimData, budgetPhaseIDs, freedim2Ids).GetAwaiter().GetResult();
    }

    public async Task<JObject> GetFramsikt1BDataForWithFreeDimNodesAsync(int budgetYear, string userId, int docType, int forecastPeriod = 0, string budgetphaseId = null, bool showOnlyModified = false, string fridimData = "", List<string> budgetPhaseIDs = null, List<string> freedim2Ids = null)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValues = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetForm");

        dynamic header = new JObject();
        header.title = langStringValues["BudForm_Fram_1B_title"].LangText;
        header.descriptiontip = langStringValues["BudForm_Fram_1B_desc"].LangText;
        TenantDBContext dbContext = await _pUtility.GetTenantDBContextAsync();
        dbContext.Database.SetCommandTimeout(900);
        List<tbf_budget_form_comment> commentList = await GetCommentListAsync(budgetYear, userDetails, dbContext, clsConstants.budget_form_type.Framsikt1B.ToString(), docType, forecastPeriod);
        List<clsBudgetFormWithFreedimHelper> Framsikt1BData = await GetFramsikt1BDataFromDBForWithFreedimNodesAsync(budgetYear, userId, docType, forecastPeriod, fridimData, budgetPhaseIDs, freedim2Ids);
        JArray dataArray = new JArray();
        dynamic finalData = new JObject();
        List<int> LineGrpToConsider = new List<int>() { 30, 40, 50 };
        List<int> changeIds = new List<int>();

        List<clsBudgetFormHelper> dataList = new List<clsBudgetFormHelper>();
        List<clsBudgetFormHelper> data = (from a in Framsikt1BData
            //where a.year == budgetYear && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
            group a by new { a.LineGroupId, a.LineGroup } into grp
            select new clsBudgetFormHelper()
            {
                LineGroupId = grp.Key.LineGroupId,
                LineGroup = grp.Key.LineGroup,
                id = grp.Key.LineGroupId,
                budgetFormDesc = grp.Key.LineGroup,
                actualAmtYear = grp.Sum(x => x.actualAmtYear),
                revisedBudAmtYear = grp.Sum(x => x.revisedBudAmtYear),
                orgBudAmtYear = grp.Sum(x => x.orgBudAmtYear),
                actualAmtLastYear = grp.Sum(x => x.actualAmtLastYear),
                orgBudAmtLastYear = grp.Sum(x => x.orgBudAmtLastYear),
                revisedAmtLastYear = grp.Sum(x => x.revisedAmtLastYear),
                finPlanYear1 = grp.Sum(x => x.finPlanYear1),
                finPlanYear2 = grp.Sum(x => x.finPlanYear2),
                finPlanYear3 = grp.Sum(x => x.finPlanYear3),
                finPlanYear4 = grp.Sum(x => x.finPlanYear4),
                forecastAmount = grp.Sum(x => x.forecastAmount),
                deviationForecast = grp.Sum(x => x.revisedBudAmtYear) - grp.Sum(x => x.forecastAmount),
                deviationForecastPct = grp.Sum(x => x.revisedBudAmtYear) != 0 ? ((grp.Sum(x => x.revisedBudAmtYear) - grp.Sum(x => x.forecastAmount)) / grp.Sum(x => x.revisedBudAmtYear)) * 100 : 0,
                budgetChange = grp.Sum(x => x.budgetChange.Value),
                budgetYtd = grp.Sum(x => x.budgetYtd),
                accountingYtd = grp.Sum(x => x.accountingYtd),
                deviationYtd = grp.Sum(x => x.deviationYtd),
                deviationYtdPct = 0,
                numberFormat = "n0",
                semi = false,
                clickable = true,
                rowType = "DetailRow",
                deviationActionAmount = grp.Sum(x => x.deviationAction),
                forecastInclAction = grp.Sum(x => x.forecastInclDev),
                deviationAfterAction = grp.Sum(x => x.deviationInclDevAction)
            }).OrderBy(x => x.id).ThenBy(x => x.budgetFormDesc).ToList();

        var accountsData = await dbContext.tco_accounts.Where(x => x.pk_tenant_id == userDetails.tenant_id)
            .ToListAsync();
        var reportingLineData = await dbContext.gmd_reporting_line.Where(x => x.report.Trim() == "54_OVDRIFT" && LineGrpToConsider.Contains(x.line_group_id) || (x.line_group_id == 20 && x.line_item_id == 2050)).ToListAsync();
        List<clsBudgetFormHelper> data2 = (from a in Framsikt1BData
            join ac in accountsData on new { a = a.fk_account_code, b = a.fk_tenant_id }
                equals new { a = ac.pk_account_code, b = ac.pk_tenant_id }
            join rl in reportingLineData on new { a = ac.fk_kostra_account_code }
                equals new { a = rl.fk_kostra_account_code }
            where a.year == budgetYear && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType &&
                  a.year >= ac.dateFrom.Year && a.year <= ac.dateTo.Year
            group a by new { a.fk_tenant_id, rl.line_item_id, rl.line_item } into grp
            select new clsBudgetFormHelper()
            {
                LineGroupId = grp.Key.line_item_id.ToString(),
                LineGroup = grp.Key.line_item,
                id = grp.Key.line_item_id.ToString(),
                budgetFormDesc = grp.Key.line_item,
                actualAmtYear = grp.Sum(x => x.actualAmtYear),
                revisedBudAmtYear = grp.Sum(x => x.revisedBudAmtYear),
                orgBudAmtYear = grp.Sum(x => x.orgBudAmtYear),
                actualAmtLastYear = grp.Sum(x => x.actualAmtLastYear),
                orgBudAmtLastYear = grp.Sum(x => x.orgBudAmtLastYear),
                revisedAmtLastYear = grp.Sum(x => x.revisedAmtLastYear),
                finPlanYear1 = grp.Sum(x => x.finPlanYear1),
                finPlanYear2 = grp.Sum(x => x.finPlanYear2),
                finPlanYear3 = grp.Sum(x => x.finPlanYear3),
                finPlanYear4 = grp.Sum(x => x.finPlanYear4),
                forecastAmount = grp.Sum(x => x.forecastAmount),
                deviationForecast = grp.Sum(x => x.revisedBudAmtYear) - grp.Sum(x => x.forecastAmount),
                deviationForecastPct = grp.Sum(x => x.revisedBudAmtYear) != 0 ? ((grp.Sum(x => x.revisedBudAmtYear) - grp.Sum(x => x.forecastAmount)) / grp.Sum(x => x.revisedBudAmtYear)) * 100 : 0,
                budgetChange = grp.Sum(x => x.budgetChange.Value),
                budgetYtd = grp.Sum(x => x.budgetYtd),
                accountingYtd = grp.Sum(x => x.accountingYtd),
                deviationYtd = grp.Sum(x => x.deviationYtd),
                deviationYtdPct = 0,
                numberFormat = "n0",
                semi = false,
                clickable = true,
                rowType = "DetailRow",
                deviationActionAmount = grp.Sum(x => x.deviationAction),
                forecastInclAction = grp.Sum(x => x.forecastInclDev),
                deviationAfterAction = grp.Sum(x => x.deviationInclDevAction)
            }).OrderBy(x => x.id).ThenBy(x => x.budgetFormDesc).ToList();

        dynamic DataObjArray = new JArray();
        dynamic DataObj = new JObject();
        foreach (var item in data)
        {
            DataObj = new JObject();
            DataObj.LineGroupId = item.LineGroupId.ToString();
            DataObj.LineGroup = item.LineGroup;
            DataObj.id = item.id.ToString();
            DataObj.budgetFormDesc = item.budgetFormDesc;
            DataObj.actualAmtYear = item.actualAmtYear;
            DataObj.revisedBudAmtYear = item.revisedBudAmtYear;
            DataObj.orgBudAmtYear = item.orgBudAmtYear;
            DataObj.actualAmtLastYear = item.actualAmtLastYear;
            DataObj.orgBudAmtLastYear = item.orgBudAmtLastYear;
            DataObj.revisedAmtLastYear = item.revisedAmtLastYear;
            DataObj.finPlanYear1 = item.finPlanYear1;
            DataObj.finPlanYear2 = item.finPlanYear2;
            DataObj.finPlanYear3 = item.finPlanYear3;
            DataObj.finPlanYear4 = item.finPlanYear4;
            DataObj.budgetChange = item.budgetChange;
            DataObj.deviationForecastPct = item.deviationForecastPct;
            DataObj.deviationForecast = item.deviationForecast;
            DataObj.forecastAmount = item.forecastAmount;
            DataObj.note = string.Empty;
            DataObj.numberFormat = "n0";
            DataObj.semi = item.semi;
            DataObj.clickable = item.clickable;
            DataObj.rowType = item.rowType;
            DataObj.budgetYtd = item.budgetYtd;
            DataObj.accountingYtd = item.accountingYtd;
            DataObj.deviationYtd = item.deviationYtd;
            DataObj.deviationYtdPct = 0;
            DataObj.note = commentList.FirstOrDefault(x => x.line_group_id.ToString() == item.LineGroupId && x.line_item_id == item.id) != null ? commentList.FirstOrDefault(x => x.line_group_id.ToString() == item.LineGroupId && x.line_item_id == item.id).comment : string.Empty;
            DataObj.IsSecondGrid = false;
            DataObj.deviationActionAmount = item.deviationActionAmount;
            DataObj.forecastInclAction = item.forecastInclAction;
            DataObj.deviationAfterAction = item.deviationAfterAction;
            DataObjArray.Add(DataObj);
        }

        // dataList.AddRange(data);
        //sum dta1
        decimal forAmtPctData = data.Sum(x => x.revisedBudAmtYear) != 0 ? (((data.Sum(x => x.revisedBudAmtYear) - data.Sum(x => x.forecastAmount)) / data.Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
        DataObj = new JObject();
        DataObj.actualAmtLastYear = data.Sum(x => x.actualAmtLastYear);
        DataObj.orgBudAmtYear = data.Sum(x => x.orgBudAmtYear);
        DataObj.revisedBudAmtYear = data.Sum(x => x.revisedBudAmtYear);
        DataObj.actualAmtYear = data.Sum(x => x.actualAmtYear);
        DataObj.orgBudAmtLastYear = data.Sum(x => x.orgBudAmtLastYear);
        DataObj.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear);
        DataObj.finPlanYear1 = data.Sum(x => x.finPlanYear1);
        DataObj.finPlanYear2 = data.Sum(x => x.finPlanYear2);
        DataObj.finPlanYear3 = data.Sum(x => x.finPlanYear3);
        DataObj.finPlanYear4 = data.Sum(x => x.finPlanYear4);
        DataObj.budgetChange = data.Sum(x => x.budgetChange);
        DataObj.deviationForecastPct = forAmtPctData;
        DataObj.deviationForecast = data.Sum(x => x.deviationForecast);
        DataObj.forecastAmount = data.Sum(x => x.forecastAmount);
        DataObj.budgetFormDesc = langStringValues["BudForm_Sum_Fram_1B"].LangText;
        DataObj.id = "-1";
        DataObj.LineGroup = null;
        DataObj.LineGroupId = "-1";
        DataObj.numberFormat = "n0";
        DataObj.semi = true;
        DataObj.clickable = false;
        DataObj.note = null;
        DataObj.budgetYtd = data.Sum(x => x.budgetYtd);
        DataObj.accountingYtd = data.Sum(x => x.accountingYtd);
        DataObj.deviationYtd = data.Sum(x => x.deviationYtd);
        DataObj.deviationYtdPct = 0;
        DataObj.rowType = "SumRowGrandTotal";
        DataObj.deviationActionAmount = data.Sum(x => x.deviationActionAmount);
        DataObj.forecastInclAction = data.Sum(x => x.forecastInclAction);
        DataObj.deviationAfterAction = data.Sum(x => x.deviationAfterAction);
        DataObjArray.Add(DataObj);

        //empty Row
        DataObj = new JObject();
        DataObj.LineGroupId = null;
        DataObj.LineGroup = null;
        DataObj.id = null;
        DataObj.budgetFormDesc = "";
        DataObj.actualAmtYear = "";
        DataObj.revisedBudAmtYear = "";
        DataObj.orgBudAmtYear = "";
        DataObj.actualAmtLastYear = "";
        DataObj.orgBudAmtLastYear = "";
        DataObj.revisedAmtLastYear = "";
        DataObj.finPlanYear1 = "";
        DataObj.finPlanYear2 = "";
        DataObj.finPlanYear3 = "";
        DataObj.finPlanYear4 = "";
        DataObj.budgetChange = "";
        DataObj.deviationForecastPct = "";
        DataObj.deviationForecast = "";
        DataObj.forecastAmount = "";
        DataObj.budgetYtd = "";
        DataObj.accountingYtd = "";
        DataObj.deviationYtd = "";
        DataObj.deviationYtdPct = "";
        DataObj.semi = true;
        DataObj.clickable = false;
        DataObj.numberFormat = "Text";
        DataObj.note = null;
        DataObj.rowType = "DetailRow";
        DataObj.deviationActionAmount = "";
        DataObj.forecastInclAction = "";
        DataObj.deviationAfterAction = "";
        DataObjArray.Add(DataObj);

        //heading Row
        DataObj = new JObject();
        DataObj.LineGroupId = null;
        DataObj.LineGroup = null;
        DataObj.id = null;
        DataObj.budgetFormDesc = langStringValues["BudForm_Table_2_heading_Fram1B"].LangText;
        DataObj.actualAmtYear = "";
        DataObj.revisedBudAmtYear = "";
        DataObj.orgBudAmtYear = "";
        DataObj.actualAmtLastYear = "";
        DataObj.orgBudAmtLastYear = "";
        DataObj.revisedAmtLastYear = "";
        DataObj.finPlanYear1 = "";
        DataObj.finPlanYear2 = "";
        DataObj.finPlanYear3 = "";
        DataObj.finPlanYear4 = "";
        DataObj.budgetChange = "";
        DataObj.deviationForecastPct = "";
        DataObj.deviationForecast = "";
        DataObj.forecastAmount = "";
        DataObj.budgetYtd = "";
        DataObj.accountingYtd = "";
        DataObj.deviationYtd = "";
        DataObj.deviationYtdPct = "";
        DataObj.semi = true;
        DataObj.clickable = false;
        DataObj.numberFormat = "Text";
        DataObj.note = null;
        DataObj.rowType = "DetailRow";
        DataObj.deviationActionAmount = "";
        DataObj.forecastInclAction = "";
        DataObj.deviationAfterAction = "";
        DataObjArray.Add(DataObj);

        //second data list
        foreach (var item in data2)
        {
            DataObj = new JObject();
            DataObj.LineGroupId = item.LineGroupId.ToString();
            DataObj.LineGroup = item.LineGroup;
            DataObj.id = item.id.ToString();
            DataObj.budgetFormDesc = item.budgetFormDesc;
            DataObj.actualAmtYear = item.actualAmtYear;
            DataObj.revisedBudAmtYear = item.revisedBudAmtYear;
            DataObj.orgBudAmtYear = item.orgBudAmtYear;
            DataObj.actualAmtLastYear = item.actualAmtLastYear;
            DataObj.orgBudAmtLastYear = item.orgBudAmtLastYear;
            DataObj.revisedAmtLastYear = item.revisedAmtLastYear;
            DataObj.finPlanYear1 = item.finPlanYear1;
            DataObj.finPlanYear2 = item.finPlanYear2;
            DataObj.finPlanYear3 = item.finPlanYear3;
            DataObj.finPlanYear4 = item.finPlanYear4;
            DataObj.budgetChange = item.budgetChange;
            DataObj.deviationForecastPct = item.deviationForecastPct;
            DataObj.deviationForecast = item.deviationForecast;
            DataObj.forecastAmount = item.forecastAmount;
            DataObj.budgetYtd = item.budgetYtd;
            DataObj.accountingYtd = item.accountingYtd;
            DataObj.deviationYtd = item.deviationYtd;
            DataObj.deviationYtdPct = 0;
            DataObj.note = string.Empty;
            DataObj.numberFormat = "n0";
            DataObj.semi = item.semi;
            DataObj.clickable = item.clickable;
            DataObj.rowType = item.rowType;
            DataObj.note = commentList.FirstOrDefault(x => x.line_group_id.ToString() == item.LineGroupId && x.line_item_id == item.id) != null ? commentList.FirstOrDefault(x => x.line_group_id.ToString() == item.LineGroupId && x.line_item_id == item.id).comment : string.Empty;
            DataObj.IsSecondGrid = true;
            DataObj.deviationActionAmount = item.deviationActionAmount;
            DataObj.forecastInclAction = item.forecastInclAction;
            DataObj.deviationAfterAction = item.deviationAfterAction;
            DataObjArray.Add(DataObj);
        }

        //sumarry for Data 2
        decimal redBudAmt = data.Sum(x => x.revisedBudAmtYear.Value) - data2.Sum(x => x.revisedBudAmtYear.Value);
        decimal forAmt = data.Sum(x => x.forecastAmount.Value) - data2.Sum(x => x.forecastAmount.Value);
        forAmtPctData = redBudAmt != 0 ? ((redBudAmt - forAmt) / redBudAmt) * 100 : 0;
        DataObj = new JObject();
        DataObj.actualAmtLastYear = data.Sum(x => x.actualAmtLastYear) - data2.Sum(x => x.actualAmtLastYear);
        DataObj.orgBudAmtYear = data.Sum(x => x.orgBudAmtYear) - data2.Sum(x => x.orgBudAmtYear);
        DataObj.revisedBudAmtYear = data.Sum(x => x.revisedBudAmtYear) - data2.Sum(x => x.revisedBudAmtYear);
        DataObj.actualAmtYear = data.Sum(x => x.actualAmtYear) - data2.Sum(x => x.actualAmtYear);
        DataObj.orgBudAmtLastYear = data.Sum(x => x.orgBudAmtLastYear) - data2.Sum(x => x.orgBudAmtLastYear);
        DataObj.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear) - data2.Sum(x => x.revisedAmtLastYear);
        DataObj.finPlanYear1 = data.Sum(x => x.finPlanYear1) - data2.Sum(x => x.finPlanYear1);
        DataObj.finPlanYear2 = data.Sum(x => x.finPlanYear2) - data2.Sum(x => x.finPlanYear2);
        DataObj.finPlanYear3 = data.Sum(x => x.finPlanYear3) - data2.Sum(x => x.finPlanYear3);
        DataObj.finPlanYear4 = data.Sum(x => x.finPlanYear4) - data2.Sum(x => x.finPlanYear4);
        DataObj.budgetChange = data.Sum(x => x.budgetChange) - data2.Sum(x => x.budgetChange);
        DataObj.deviationForecastPct = forAmtPctData;
        DataObj.deviationForecast = data.Sum(x => x.deviationForecast) - data2.Sum(x => x.deviationForecast);
        DataObj.forecastAmount = data.Sum(x => x.forecastAmount) - data2.Sum(x => x.forecastAmount);
        DataObj.budgetYtd = data.Sum(x => x.budgetYtd) - data2.Sum(x => x.budgetYtd);
        DataObj.accountingYtd = data.Sum(x => x.accountingYtd) - data2.Sum(x => x.accountingYtd);
        DataObj.deviationYtd = data.Sum(x => x.deviationYtd) - data2.Sum(x => x.deviationYtd);
        DataObj.deviationYtdPct = 0;
        DataObj.budgetFormDesc = langStringValues["BudForm_Sum_Fram_1B_sum_2"].LangText;
        DataObj.id = "-1";
        DataObj.LineGroup = null;
        DataObj.LineGroupId = "-1";
        DataObj.numberFormat = "n0";
        DataObj.semi = true;
        DataObj.clickable = false;
        DataObj.note = null;
        DataObj.rowType = "SumRowGrandTotal";
        DataObj.deviationActionAmount = data.Sum(x => x.deviationActionAmount) - data2.Sum(x => x.deviationActionAmount);
        DataObj.forecastInclAction = data.Sum(x => x.forecastInclAction) - data2.Sum(x => x.forecastInclAction);
        DataObj.deviationAfterAction = data.Sum(x => x.deviationAfterAction) - data2.Sum(x => x.deviationAfterAction);
        DataObjArray.Add(DataObj);

        finalData.data = DataObjArray;
        finalData.columns = await GetBudgetFormColumnAsync(budgetYear, userId, "Framsikt1B", docType, forecastPeriod);
        finalData.header = header;
        return finalData;
    }

    private async Task<List<tbf_budget_form_1B>> Get1BDataFromDBAsync(int budgetYear, string userId, int docType, int forecastPeriod, string fridimData = "", List<string> budgetPhaseIDs = null, List<string> freedim2Ids = null)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        List<int> changeIds = budgetPhaseIDs != null && budgetPhaseIDs.Count > 0 ? await GetChangeIdsAsync(budgetYear, budgetPhaseIDs, userId) : new List<int>();
        if (string.IsNullOrEmpty(fridimData))
        {
            List<tbf_budget_form_1B> data = forecastPeriod == 0 ? await (from a in dbContext.tbf_budget_form_1B
                    where a.year == budgetYear && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
                    select a).ToListAsync()
                : await (from a in dbContext.tbf_budget_form_1B
                    where a.forecast_period == forecastPeriod && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
                    select a).ToListAsync();
            if (changeIds.Count > 0)
            {
                data = data.Where(x => changeIds.Contains(x.fk_change_id)).ToList();
            }
            if (freedim2Ids != null && freedim2Ids.Count > 0)
            {
                var emptyFreedimSelected = freedim2Ids.Contains("-1");
                data = emptyFreedimSelected ? data.Where(x => freedim2Ids.Contains(x.free_dim_code) || string.IsNullOrEmpty(x.free_dim_code)).ToList() : data.Where(x => freedim2Ids.Contains(x.free_dim_code)).ToList();
            }
            return data;
        }
        else
        {
            if (fridimData == DocData.EmptyFridm.ToString())
            {
                return forecastPeriod != 0 ? await (from a in dbContext.tbf_budget_form_1B
                    where a.forecast_period == forecastPeriod && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType && string.IsNullOrEmpty(a.free_dim_code)
                    select a).ToListAsync() : await (from a in dbContext.tbf_budget_form_1B
                    where a.fk_tenant_id == userDetails.tenant_id && a.year == budgetYear && a.doc_type == docType && string.IsNullOrEmpty(a.free_dim_code)
                    select a).ToListAsync();
            }
            else
            {
                return forecastPeriod != 0 ? await (from a in dbContext.tbf_budget_form_1B
                    where a.forecast_period == forecastPeriod && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType && (a.free_dim_code != null && !string.IsNullOrEmpty(a.free_dim_code.Trim()))
                    select a).ToListAsync() : await (from a in dbContext.tbf_budget_form_1B
                    where a.fk_tenant_id == userDetails.tenant_id && a.year == budgetYear && a.doc_type == docType && (a.free_dim_code != null && !string.IsNullOrEmpty(a.free_dim_code.Trim()))
                    select a).ToListAsync();
            }
        }
    }

    private async Task<List<clsBudgetFormWithFreedimHelper>> Get1BDataForWithFreedimFromDBAsync(int budgetYear, string userId, int docType, int forecastPeriod, string fridimData = "", List<string> budgetPhaseIDs = null, List<string> freedim2Ids = null)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        return forecastPeriod != 0 ? await (from a in dbContext.tbf_budget_form_1B
                join b in dbContext.tco_free_dim_values on new { a = a.fk_tenant_id, b = a.free_dim_code }
                    equals new { a = b.fk_tenant_id, b = b.free_dim_code }
                where a.forecast_period == forecastPeriod && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType && (a.free_dim_code != null && !string.IsNullOrEmpty(a.free_dim_code.Trim())) &&
                      (a.free_dim_code != null && !string.IsNullOrEmpty(a.free_dim_code.Trim())) && b.free_dim_column == "free_dim_2"
                group new { a, b } by new { aggregate_id = b.free_dim_code, aggregate_name = b.description, changeId = a.fk_change_id, fk_tenant_id = a.fk_tenant_id, fk_account_code = a.fk_account_code, year = a.year, central_acc_flag = a.central_acc_flag } into grp
                select new clsBudgetFormWithFreedimHelper()
                {
                    fk_tenant_id = grp.Key.fk_tenant_id,
                    fk_account_code = grp.Key.fk_account_code,
                    year = grp.Key.year,
                    central_acc_flag = grp.Key.central_acc_flag,
                    LineGroupId = grp.Key.aggregate_id,
                    LineGroup = grp.Key.aggregate_name,
                    id = grp.Key.aggregate_id,
                    budgetFormDesc = grp.Key.aggregate_name,
                    actualAmtYear = grp.Sum(x => x.a.actual_amt_year),
                    revisedBudAmtYear = grp.Sum(x => x.a.revised_bud_amt_year),
                    orgBudAmtYear = grp.Sum(x => x.a.org_bud_amt_year),
                    actualAmtLastYear = grp.Sum(x => x.a.actual_amt_last_year),
                    orgBudAmtLastYear = grp.Sum(x => x.a.org_bud_amt_last_year),
                    revisedAmtLastYear = grp.Sum(x => x.a.revised_bud_amt_last_year),
                    finPlanYear1 = grp.Sum(x => x.a.finplan_year_1_amount),
                    finPlanYear2 = grp.Sum(x => x.a.finplan_year_2_amount),
                    finPlanYear3 = grp.Sum(x => x.a.finplan_year_3_amount),
                    finPlanYear4 = grp.Sum(x => x.a.finplan_year_4_amount),
                    finPlanYearSum = grp.Sum(x => x.a.finplan_year_1_amount) + grp.Sum(x => x.a.finplan_year_2_amount) + grp.Sum(x => x.a.finplan_year_3_amount) + grp.Sum(x => x.a.finplan_year_4_amount),
                    numberFormat = "n0",
                    forecastAmount = grp.Sum(x => x.a.forecast_amount),
                    deviationForecast = grp.Sum(x => x.a.revised_bud_amt_year) - grp.Sum(x => x.a.forecast_amount),
                    deviationForecastPct = grp.Sum(x => x.a.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.a.revised_bud_amt_year) - grp.Sum(x => x.a.forecast_amount)) / grp.Sum(x => x.a.revised_bud_amt_year)) * 100 : 0,
                    budgetChange = grp.Sum(x => x.a.budget_change.Value),
                    semi = false,
                    clickable = true,
                    changeId = grp.Key.changeId,
                    deviation = grp.Sum(x => x.a.revised_bud_amt_year) - grp.Sum(x => x.a.actual_amt_year),
                    deviationPct = grp.Sum(x => x.a.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.a.revised_bud_amt_year) - grp.Sum(x => x.a.actual_amt_year)) / grp.Sum(x => x.a.revised_bud_amt_year)) * 100 : 0,
                }).ToListAsync() :

            await (from a in dbContext.tbf_budget_form_1B
                join b in dbContext.tco_free_dim_values on new { a = a.fk_tenant_id, b = a.free_dim_code }
                    equals new { a = b.fk_tenant_id, b = b.free_dim_code }
                where a.year == budgetYear && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType && (a.free_dim_code != null && !string.IsNullOrEmpty(a.free_dim_code.Trim())) &&
                      (a.free_dim_code != null && !string.IsNullOrEmpty(a.free_dim_code.Trim())) && b.free_dim_column == "free_dim_2"
                group new { a, b } by new { aggregate_id = b.free_dim_code, aggregate_name = b.description, changeId = a.fk_change_id, fk_tenant_id = a.fk_tenant_id, fk_account_code = a.fk_account_code, year = a.year, central_acc_flag = a.central_acc_flag } into grp
                select new clsBudgetFormWithFreedimHelper()
                {
                    fk_tenant_id = grp.Key.fk_tenant_id,
                    fk_account_code = grp.Key.fk_account_code,
                    year = grp.Key.year,
                    central_acc_flag = grp.Key.central_acc_flag,
                    LineGroupId = grp.Key.aggregate_id,
                    LineGroup = grp.Key.aggregate_name,
                    id = grp.Key.aggregate_id,
                    budgetFormDesc = grp.Key.aggregate_name,
                    actualAmtYear = grp.Sum(x => x.a.actual_amt_year),
                    revisedBudAmtYear = grp.Sum(x => x.a.revised_bud_amt_year),
                    orgBudAmtYear = grp.Sum(x => x.a.org_bud_amt_year),
                    actualAmtLastYear = grp.Sum(x => x.a.actual_amt_last_year),
                    orgBudAmtLastYear = grp.Sum(x => x.a.org_bud_amt_last_year),
                    revisedAmtLastYear = grp.Sum(x => x.a.revised_bud_amt_last_year),
                    finPlanYear1 = grp.Sum(x => x.a.finplan_year_1_amount),
                    finPlanYear2 = grp.Sum(x => x.a.finplan_year_2_amount),
                    finPlanYear3 = grp.Sum(x => x.a.finplan_year_3_amount),
                    finPlanYear4 = grp.Sum(x => x.a.finplan_year_4_amount),
                    finPlanYearSum = grp.Sum(x => x.a.finplan_year_1_amount) + grp.Sum(x => x.a.finplan_year_2_amount) + grp.Sum(x => x.a.finplan_year_3_amount) + grp.Sum(x => x.a.finplan_year_4_amount),
                    numberFormat = "n0",
                    forecastAmount = grp.Sum(x => x.a.forecast_amount),
                    deviationForecast = grp.Sum(x => x.a.revised_bud_amt_year) - grp.Sum(x => x.a.forecast_amount),
                    deviationForecastPct = grp.Sum(x => x.a.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.a.revised_bud_amt_year) - grp.Sum(x => x.a.forecast_amount)) / grp.Sum(x => x.a.revised_bud_amt_year)) * 100 : 0,
                    budgetChange = grp.Sum(x => x.a.budget_change.Value),
                    semi = false,
                    clickable = true,
                    changeId = grp.Key.changeId,
                    deviation = grp.Sum(x => x.a.revised_bud_amt_year) - grp.Sum(x => x.a.actual_amt_year),
                    deviationPct = grp.Sum(x => x.a.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.a.revised_bud_amt_year) - grp.Sum(x => x.a.actual_amt_year)) / grp.Sum(x => x.a.revised_bud_amt_year)) * 100 : 0,
                }).OrderBy(x => x.SubHearderId).ThenBy(x => x.LineGroupId).ToListAsync();
    }

    private async Task<List<tbf_budget_form_framsikt_1B>> GetFramsikt1BDataFromDBAsync(int budgetYear, string userId, int docType, int forecastPeriod, string fridimData = "", List<string> budgetPhaseIDs = null, List<string> freedim2Ids = null)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        var dbContext = await _pUtility.GetTenantDBContextAsync();

        List<int> changeIds = budgetPhaseIDs != null && budgetPhaseIDs.Any() ? await GetChangeIdsAsync(budgetYear, budgetPhaseIDs, userId) : new List<int>();

        IQueryable<tbf_budget_form_framsikt_1B> query = dbContext.tbf_budget_form_framsikt_1B.Where(a => a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType);

        if (string.IsNullOrEmpty(fridimData))
        {
            if (forecastPeriod == 0)
            {
                query = query.Where(a => a.year == budgetYear);
            }
            else
            {
                query = query.Where(a => a.forecast_period == forecastPeriod);
            }
        }
        else
        {
            bool isEmptyFridim = fridimData == DocData.EmptyFridm.ToString();
            if (forecastPeriod != 0)
            {
                query = query.Where(a => a.forecast_period == forecastPeriod && (isEmptyFridim ? string.IsNullOrEmpty(a.free_dim_code) : !string.IsNullOrEmpty(a.free_dim_code)));
            }
            else
            {
                query = query.Where(a => a.year == budgetYear && (isEmptyFridim ? string.IsNullOrEmpty(a.free_dim_code) : !string.IsNullOrEmpty(a.free_dim_code)));
            }
        }

        if (changeIds.Any())
        {
            query = query.Where(a => changeIds.Contains(a.fk_change_id));
        }

        if (freedim2Ids?.Any() == true)
        {
            bool containsEmpty = freedim2Ids.Contains("-1");
            query = query.Where(a => containsEmpty ? freedim2Ids.Contains(a.free_dim_code) || string.IsNullOrEmpty(a.free_dim_code) : freedim2Ids.Contains(a.free_dim_code));
        }

        return await query.ToListAsync();
    }
    private async Task<List<clsBudgetFormWithFreedimHelper>> GetFramsikt1BDataFromDBForWithFreedimNodesAsync(int budgetYear, string userId, int docType, int forecastPeriod, string fridimData = "", List<string> budgetPhaseIDs = null, List<string> freedim2Ids = null)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        return forecastPeriod != 0 ? await (from a in dbContext.tbf_budget_form_framsikt_1B
                join b in dbContext.tco_free_dim_values on new { a = a.fk_tenant_id, b = a.free_dim_code }
                    equals new { a = b.fk_tenant_id, b = b.free_dim_code }
                where a.forecast_period == forecastPeriod && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType && (a.free_dim_code != null && !string.IsNullOrEmpty(a.free_dim_code.Trim())) &&
                      (a.free_dim_code != null && !string.IsNullOrEmpty(a.free_dim_code.Trim())) && b.free_dim_column == "free_dim_2"
                group new { a, b } by new { aggregate_id = b.free_dim_code, aggregate_name = b.description, changeId = a.fk_change_id, fk_tenant_id = a.fk_tenant_id, fk_account_code = a.fk_account_code, year = a.year, doc_type = a.doc_type } into grp
                select new clsBudgetFormWithFreedimHelper()
                {
                    fk_tenant_id = grp.Key.fk_tenant_id,
                    fk_account_code = grp.Key.fk_account_code,
                    year = grp.Key.year,
                    doc_type = grp.Key.doc_type,
                    LineGroupId = grp.Key.aggregate_id,
                    LineGroup = grp.Key.aggregate_name,
                    id = grp.Key.aggregate_id,
                    budgetFormDesc = grp.Key.aggregate_name,
                    actualAmtYear = grp.Sum(x => x.a.actual_amt_year),
                    revisedBudAmtYear = grp.Sum(x => x.a.revised_bud_amt_year),
                    orgBudAmtYear = grp.Sum(x => x.a.org_bud_amt_year),
                    actualAmtLastYear = grp.Sum(x => x.a.actual_amt_last_year),
                    orgBudAmtLastYear = grp.Sum(x => x.a.org_bud_amt_last_year),
                    revisedAmtLastYear = grp.Sum(x => x.a.revised_bud_amt_last_year),
                    finPlanYear1 = grp.Sum(x => x.a.finplan_year_1_amount),
                    finPlanYear2 = grp.Sum(x => x.a.finplan_year_2_amount),
                    finPlanYear3 = grp.Sum(x => x.a.finplan_year_3_amount),
                    finPlanYear4 = grp.Sum(x => x.a.finplan_year_4_amount),
                    finPlanYearSum = grp.Sum(x => x.a.finplan_year_1_amount) + grp.Sum(x => x.a.finplan_year_2_amount) + grp.Sum(x => x.a.finplan_year_3_amount) + grp.Sum(x => x.a.finplan_year_4_amount),
                    numberFormat = "n0",
                    forecastAmount = grp.Sum(x => x.a.forecast_amount),
                    deviationForecast = grp.Sum(x => x.a.revised_bud_amt_year) - grp.Sum(x => x.a.forecast_amount),
                    deviationForecastPct = grp.Sum(x => x.a.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.a.revised_bud_amt_year) - grp.Sum(x => x.a.forecast_amount)) / grp.Sum(x => x.a.revised_bud_amt_year)) * 100 : 0,
                    budgetChange = grp.Sum(x => x.a.budget_change.Value),
                    semi = false,
                    clickable = true,
                    changeId = grp.Key.changeId,
                    budgetYtd = grp.Sum(x => x.a.budget_ytd),
                    accountingYtd = grp.Sum(x => x.a.accounting_ytd),
                    deviation = grp.Sum(x => x.a.revised_bud_amt_year) - grp.Sum(x => x.a.actual_amt_year),
                    deviationPct = grp.Sum(x => x.a.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.a.revised_bud_amt_year) - grp.Sum(x => x.a.actual_amt_year)) / grp.Sum(x => x.a.revised_bud_amt_year)) * 100 : 0,
                }).OrderBy(x => x.id).ThenBy(x => x.budgetFormDesc).ToListAsync() :

            await (from a in dbContext.tbf_budget_form_framsikt_1B
                join b in dbContext.tco_free_dim_values on new { a = a.fk_tenant_id, b = a.free_dim_code }
                    equals new { a = b.fk_tenant_id, b = b.free_dim_code }
                where a.year == budgetYear && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType && (a.free_dim_code != null && !string.IsNullOrEmpty(a.free_dim_code.Trim())) &&
                      (a.free_dim_code != null && !string.IsNullOrEmpty(a.free_dim_code.Trim())) && b.free_dim_column == "free_dim_2"
                group new { a, b } by new { aggregate_id = b.free_dim_code, aggregate_name = b.description, changeId = a.fk_change_id, fk_tenant_id = a.fk_tenant_id } into grp
                select new clsBudgetFormWithFreedimHelper()
                {
                    fk_tenant_id = grp.Key.fk_tenant_id,
                    LineGroupId = grp.Key.aggregate_id,
                    LineGroup = grp.Key.aggregate_name,
                    id = grp.Key.aggregate_id,
                    budgetFormDesc = grp.Key.aggregate_name,
                    actualAmtYear = grp.Sum(x => x.a.actual_amt_year),
                    revisedBudAmtYear = grp.Sum(x => x.a.revised_bud_amt_year),
                    orgBudAmtYear = grp.Sum(x => x.a.org_bud_amt_year),
                    actualAmtLastYear = grp.Sum(x => x.a.actual_amt_last_year),
                    orgBudAmtLastYear = grp.Sum(x => x.a.org_bud_amt_last_year),
                    revisedAmtLastYear = grp.Sum(x => x.a.revised_bud_amt_last_year),
                    finPlanYear1 = grp.Sum(x => x.a.finplan_year_1_amount),
                    finPlanYear2 = grp.Sum(x => x.a.finplan_year_2_amount),
                    finPlanYear3 = grp.Sum(x => x.a.finplan_year_3_amount),
                    finPlanYear4 = grp.Sum(x => x.a.finplan_year_4_amount),
                    finPlanYearSum = grp.Sum(x => x.a.finplan_year_1_amount) + grp.Sum(x => x.a.finplan_year_2_amount) + grp.Sum(x => x.a.finplan_year_3_amount) + grp.Sum(x => x.a.finplan_year_4_amount),
                    numberFormat = "n0",
                    forecastAmount = grp.Sum(x => x.a.forecast_amount),
                    deviationForecast = grp.Sum(x => x.a.revised_bud_amt_year) - grp.Sum(x => x.a.forecast_amount),
                    deviationForecastPct = grp.Sum(x => x.a.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.a.revised_bud_amt_year) - grp.Sum(x => x.a.forecast_amount)) / grp.Sum(x => x.a.revised_bud_amt_year)) * 100 : 0,
                    budgetChange = grp.Sum(x => x.a.budget_change.Value),
                    semi = false,
                    clickable = true,
                    changeId = grp.Key.changeId,
                    budgetYtd = grp.Sum(x => x.a.budget_ytd),
                    accountingYtd = grp.Sum(x => x.a.accounting_ytd),
                    deviation = grp.Sum(x => x.a.revised_bud_amt_year) - grp.Sum(x => x.a.actual_amt_year),
                    deviationPct = grp.Sum(x => x.a.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.a.revised_bud_amt_year) - grp.Sum(x => x.a.actual_amt_year)) / grp.Sum(x => x.a.revised_bud_amt_year)) * 100 : 0,
                }).OrderBy(x => x.id).ThenBy(x => x.budgetFormDesc).ToListAsync();
    }

    public JObject Get1BDataConsolidation(int budgetYear, string userId, int docType, int forecastPeriod = 0, string budgetPhaseId = null, bool showOnlyModified = false)
    {
        return Get1BDataConsolidationAsync(budgetYear, userId, docType, forecastPeriod, budgetPhaseId, showOnlyModified).GetAwaiter().GetResult();
    }



    public async Task<JObject> Get1BDataConsolidationAsync(int budgetYear, string userId, int docType, int forecastPeriod = 0, string budgetPhaseId = null, bool showOnlyModified = false)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValues = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetForm");

        dynamic header = new JObject();
        header.title = langStringValues["BudForm_1B_title"].LangText;
        header.descriptiontip = langStringValues["BudForm_1B_desc"].LangText;
        TenantDBContext dbContext = await _pUtility.GetTenantDBContextAsync();
        dbContext.Database.SetCommandTimeout(900);
        List<tbf_budget_form_comment> commentList = await GetCommentListAsync(budgetYear, userDetails, dbContext, clsConstants.budget_form_type.BudgetForm1B.ToString(), docType, forecastPeriod);
        List<vw_bmdoc_1B_consolidated> budGet1BDataFridm = await Get1BDataFromDBConsolidationAsync(budgetYear, userId, docType, forecastPeriod);
        List<tbf_budget_form_1B> budGet1BData = await Get1BDataFromDBAsync(budgetYear, userId, docType, forecastPeriod, string.Empty, null, null);
        dynamic finalData = new JObject();

        List<int> changeIds = await GetChangeIdsAsync(budgetYear, budgetPhaseId, userId, showOnlyModified);

        budGet1BData = Filter1BByChangeId(budGet1BData, changeIds);
        List<clsBudgetFormHelper> data = GetGroupedData(budGet1BData);
        List<clsBudgetFormHelper> data2 = await GetGroupedData1(budgetYear, userDetails, dbContext, budGet1BData);
        List<clsBudgetFormHelper> dataFridm = GetGroupedDataConsolidation(budGet1BDataFridm);
        List<clsBudgetFormHelper> data2Frdim = GetGroupedDataConsolidation(budgetYear, userDetails, dbContext, budGet1BDataFridm);
        dynamic DataObjArray;
        dynamic DataObj;
        DataObjArray = FormatData1(commentList, dataFridm, true);

        //sum dta1
        DataObj = new JObject();
        DataObj.actualAmtLastYear = dataFridm.Sum(x => x.actualAmtLastYear);
        DataObj.orgBudAmtYear = dataFridm.Sum(x => x.orgBudAmtYear);
        DataObj.revisedBudAmtYear = dataFridm.Sum(x => x.revisedBudAmtYear);
        DataObj.actualAmtYear = dataFridm.Sum(x => x.actualAmtYear);
        DataObj.orgBudAmtLastYear = dataFridm.Sum(x => x.orgBudAmtLastYear);
        DataObj.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear);
        DataObj.revisedAmtLastYearByAuth = data.Sum(x => x.revisedAmtLastYearByAuth);
        DataObj.revisedAmtYearByAuth = data.Sum(x => x.revisedAmtYearByAuth);
        DataObj.finPlanYear1 = dataFridm.Sum(x => x.finPlanYear1);
        DataObj.finPlanYear2 = dataFridm.Sum(x => x.finPlanYear2);
        DataObj.finPlanYear3 = dataFridm.Sum(x => x.finPlanYear3);
        DataObj.finPlanYear4 = dataFridm.Sum(x => x.finPlanYear4);
        DataObj.budgetFormDesc = langStringValues["BudForm_Sum_1B"].LangText;
        DataObj.budgetChange = dataFridm.Sum(x => x.budgetChange);

        decimal forAmtPctData = dataFridm.Sum(x => x.revisedBudAmtYear) != 0 ? (((data.Sum(x => x.revisedBudAmtYear) - dataFridm.Sum(x => x.forecastAmount)) / dataFridm.Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
        DataObj.deviationForecastPct = forAmtPctData;
        DataObj.deviationForecast = dataFridm.Sum(x => x.deviationForecast);
        DataObj.forecastAmount = dataFridm.Sum(x => x.forecastAmount);
        DataObj.id = "-1";
        DataObj.LineGroup = null;
        DataObj.LineGroupId = "-1";
        DataObj.numberFormat = "n0";
        DataObj.semi = true;
        DataObj.clickable = false;
        DataObj.note = null;
        DataObj.rowType = "SumRowGrandTotal";
        DataObj.budgetYtd = dataFridm.Sum(x => x.budgetYtd);
        DataObj.accountingYtd = dataFridm.Sum(x => x.accountingYtd);
        DataObj.deviationYtd = dataFridm.Sum(x => x.deviationYtd);
        DataObj.deviationYtdPct = 0;
        DataObj.IsSecondGrid = false;
        DataObj.deviationActionAmount = dataFridm.Sum(x => x.deviationActionAmount);
        DataObj.forecastInclAction = dataFridm.Sum(x => x.forecastInclAction);
        DataObj.deviationAfterAction = dataFridm.Sum(x => x.deviationAfterAction);
        DataObjArray.Add(DataObj);

        //empty Row
        DataObj = new JObject();
        DataObj.LineGroupId = null;
        DataObj.LineGroup = null;
        DataObj.id = null;
        DataObj.budgetFormDesc = "";
        DataObj.actualAmtYear = "";
        DataObj.revisedBudAmtYear = "";
        DataObj.orgBudAmtYear = "";
        DataObj.actualAmtLastYear = "";
        DataObj.orgBudAmtLastYear = "";
        DataObj.revisedAmtLastYear = "";
        DataObj.revisedAmtLastYearByAuth = "";
        DataObj.revisedAmtYearByAuth = "";
        DataObj.finPlanYear1 = "";
        DataObj.finPlanYear2 = "";
        DataObj.finPlanYear3 = "";
        DataObj.finPlanYear4 = "";
        DataObj.budgetChange = "";
        DataObj.deviationForecastPct = "";
        DataObj.deviationForecast = "";
        DataObj.forecastAmount = "";
        DataObj.budgetYtd = "";
        DataObj.accountingYtd = "";
        DataObj.deviationYtd = "";
        DataObj.deviationYtdPct = "";
        DataObj.semi = true;
        DataObj.clickable = false;
        DataObj.numberFormat = "Text";
        DataObj.note = null;
        DataObj.rowType = "DetailRow";
        DataObj.IsSecondGrid = false;
        DataObj.deviationActionAmount = "";
        DataObj.forecastInclAction = "";
        DataObj.deviationAfterAction = "";
        DataObjArray.Add(DataObj);

        //fridm rows
        foreach (var item in data2Frdim)
        {
            DataObj = FormateData(commentList, item);
            DataObjArray.Add(DataObj);
        }
        //sum fridm data1
        DataObj = new JObject();
        DataObj.actualAmtLastYear = data2Frdim.Sum(x => x.actualAmtLastYear);
        DataObj.orgBudAmtYear = data2Frdim.Sum(x => x.orgBudAmtYear);
        DataObj.revisedBudAmtYear = data2Frdim.Sum(x => x.revisedBudAmtYear);
        DataObj.actualAmtYear = data2Frdim.Sum(x => x.actualAmtYear);
        DataObj.orgBudAmtLastYear = data2Frdim.Sum(x => x.orgBudAmtLastYear);
        DataObj.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear);
        DataObj.revisedAmtLastYearByAuth = data2Frdim.Sum(x => x.revisedAmtLastYearByAuth);
        DataObj.revisedAmtYearByAuth = data2Frdim.Sum(x => x.revisedAmtYearByAuth);
        DataObj.finPlanYear1 = data2Frdim.Sum(x => x.finPlanYear1);
        DataObj.finPlanYear2 = data2Frdim.Sum(x => x.finPlanYear2);
        DataObj.finPlanYear3 = data2Frdim.Sum(x => x.finPlanYear3);
        DataObj.finPlanYear4 = data2Frdim.Sum(x => x.finPlanYear4);
        DataObj.budgetFormDesc = langStringValues["BudForm_Sum_1B_fridm"].LangText;
        DataObj.budgetChange = data2Frdim.Sum(x => x.budgetChange);

        forAmtPctData = data2Frdim.Sum(x => x.revisedBudAmtYear) != 0 ? (((data.Sum(x => x.revisedBudAmtYear) - data.Sum(x => x.forecastAmount)) / data.Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
        DataObj.deviationForecastPct = forAmtPctData;
        DataObj.deviationForecast = data2Frdim.Sum(x => x.deviationForecast);
        DataObj.forecastAmount = data.Sum(x => x.forecastAmount);
        DataObj.id = "-999";
        DataObj.LineGroup = null;
        DataObj.LineGroupId = "-1";
        DataObj.numberFormat = "n0";
        DataObj.semi = true;
        DataObj.clickable = false;
        DataObj.note = null;
        DataObj.rowType = "SumRowGrandTotal";
        DataObj.budgetYtd = data2Frdim.Sum(x => x.budgetYtd);
        DataObj.accountingYtd = data2Frdim.Sum(x => x.accountingYtd);
        DataObj.deviationYtd = data2Frdim.Sum(x => x.deviationYtd);
        DataObj.deviationYtdPct = 0;
        DataObj.IsSecondGrid = false;
        DataObj.deviationActionAmount = data2Frdim.Sum(x => x.deviationActionAmount);
        DataObj.forecastInclAction = data2Frdim.Sum(x => x.forecastInclAction);
        DataObj.deviationAfterAction = data2Frdim.Sum(x => x.deviationAfterAction);
        DataObjArray.Add(DataObj);

        //sum fridm dta1+ data
        DataObj = new JObject();
        DataObj.actualAmtLastYear = data2Frdim.Sum(x => x.actualAmtLastYear) + dataFridm.Sum(x => x.actualAmtLastYear);
        DataObj.orgBudAmtYear = data2Frdim.Sum(x => x.orgBudAmtYear) + dataFridm.Sum(x => x.orgBudAmtYear);
        DataObj.revisedBudAmtYear = data2Frdim.Sum(x => x.revisedBudAmtYear) + dataFridm.Sum(x => x.revisedBudAmtYear);
        DataObj.actualAmtYear = data2Frdim.Sum(x => x.actualAmtYear) + dataFridm.Sum(x => x.actualAmtYear);
        DataObj.orgBudAmtLastYear = data2Frdim.Sum(x => x.orgBudAmtLastYear) + dataFridm.Sum(x => x.orgBudAmtLastYear);
        DataObj.revisedAmtLastYear = data2Frdim.Sum(x => x.revisedAmtLastYear) + data.Sum(x => x.revisedAmtLastYear);
        DataObj.revisedAmtLastYearByAuth = data2Frdim.Sum(x => x.revisedAmtLastYearByAuth);
        DataObj.revisedAmtYearByAuth = data2Frdim.Sum(x => x.revisedAmtYearByAuth);
        DataObj.finPlanYear1 = data2Frdim.Sum(x => x.finPlanYear1) + dataFridm.Sum(x => x.finPlanYear1);
        DataObj.finPlanYear2 = data2Frdim.Sum(x => x.finPlanYear2) + dataFridm.Sum(x => x.finPlanYear2);
        DataObj.finPlanYear3 = data2Frdim.Sum(x => x.finPlanYear3) + dataFridm.Sum(x => x.finPlanYear3);
        DataObj.finPlanYear4 = data2Frdim.Sum(x => x.finPlanYear4) + dataFridm.Sum(x => x.finPlanYear4);
        DataObj.budgetFormDesc = langStringValues["BudForm_Sum_1B_with_fridm_Sum"].LangText;
        DataObj.budgetChange = data2Frdim.Sum(x => x.budgetChange) + dataFridm.Sum(x => x.budgetChange);
        var forAmtPctData3 = data2Frdim.Sum(x => x.revisedBudAmtYear) != 0 ? (((data2Frdim.Sum(x => x.revisedBudAmtYear) - data2Frdim.Sum(x => x.forecastAmount)) / data2Frdim.Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
        forAmtPctData = dataFridm.Sum(x => x.revisedBudAmtYear) != 0 ? (((dataFridm.Sum(x => x.revisedBudAmtYear) - dataFridm.Sum(x => x.forecastAmount)) / data.Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
        DataObj.deviationForecastPct = forAmtPctData + forAmtPctData3;
        DataObj.deviationForecast = data2Frdim.Sum(x => x.deviationForecast) + dataFridm.Sum(x => x.deviationForecast);
        DataObj.forecastAmount = data2Frdim.Sum(x => x.forecastAmount) + dataFridm.Sum(x => x.forecastAmount);
        DataObj.id = "-2";
        DataObj.LineGroup = null;
        DataObj.LineGroupId = "-1";
        DataObj.numberFormat = "n0";
        DataObj.semi = true;
        DataObj.clickable = false;
        DataObj.note = null;
        DataObj.rowType = "SumRowGrandTotal";
        DataObj.budgetYtd = data2Frdim.Sum(x => x.budgetYtd) + dataFridm.Sum(x => x.budgetYtd);
        DataObj.accountingYtd = data2Frdim.Sum(x => x.accountingYtd) + dataFridm.Sum(x => x.accountingYtd);
        DataObj.deviationYtd = data2Frdim.Sum(x => x.deviationYtd) + dataFridm.Sum(x => x.deviationYtd);
        DataObj.deviationYtdPct = 0;
        DataObj.IsSecondGrid = false;
        DataObj.deviationActionAmount = data2Frdim.Sum(x => x.deviationActionAmount) + dataFridm.Sum(x => x.deviationActionAmount);
        DataObj.forecastInclAction = data2Frdim.Sum(x => x.forecastInclAction) + dataFridm.Sum(x => x.forecastInclAction);
        DataObj.deviationAfterAction = data2Frdim.Sum(x => x.deviationAfterAction) + dataFridm.Sum(x => x.deviationAfterAction);
        DataObjArray.Add(DataObj);

        //empty Row
        DataObj = new JObject();
        DataObj.LineGroupId = null;
        DataObj.LineGroup = null;
        DataObj.id = null;
        DataObj.budgetFormDesc = "";
        DataObj.actualAmtYear = "";
        DataObj.revisedBudAmtYear = "";
        DataObj.orgBudAmtYear = "";
        DataObj.actualAmtLastYear = "";
        DataObj.orgBudAmtLastYear = "";
        DataObj.revisedAmtLastYear = "";
        DataObj.revisedAmtLastYearByAuth = "";
        DataObj.revisedAmtYearByAuth = "";
        DataObj.finPlanYear1 = "";
        DataObj.finPlanYear2 = "";
        DataObj.finPlanYear3 = "";
        DataObj.finPlanYear4 = "";
        DataObj.budgetChange = "";
        DataObj.deviationForecastPct = "";
        DataObj.deviationForecast = "";
        DataObj.forecastAmount = "";
        DataObj.budgetYtd = "";
        DataObj.accountingYtd = "";
        DataObj.deviationYtd = "";
        DataObj.deviationYtdPct = "";
        DataObj.semi = true;
        DataObj.clickable = false;
        DataObj.numberFormat = "Text";
        DataObj.note = null;
        DataObj.rowType = "DetailRow";
        DataObj.IsSecondGrid = false;
        DataObj.deviationActionAmount = "";
        DataObj.forecastInclAction = "";
        DataObj.deviationAfterAction = "";
        DataObjArray.Add(DataObj);

        //heading Row
        DataObj = new JObject();
        DataObj.LineGroupId = null;
        DataObj.LineGroup = null;
        DataObj.id = "-2";
        DataObj.budgetFormDesc = langStringValues["BudForm_Table_2_heading_1B"].LangText;
        DataObj.actualAmtYear = "";
        DataObj.revisedBudAmtYear = "";
        DataObj.orgBudAmtYear = "";
        DataObj.actualAmtLastYear = "";
        DataObj.orgBudAmtLastYear = "";
        DataObj.revisedAmtLastYear = "";
        DataObj.revisedAmtLastYearByAuth = "";
        DataObj.revisedAmtYearByAuth = "";
        DataObj.finPlanYear1 = "";
        DataObj.finPlanYear2 = "";
        DataObj.finPlanYear3 = "";
        DataObj.finPlanYear4 = "";
        DataObj.budgetChange = "";
        DataObj.deviationForecastPct = "";
        DataObj.deviationForecast = "";
        DataObj.forecastAmount = "";
        DataObj.semi = true;
        DataObj.clickable = false;
        DataObj.budgetYtd = "";
        DataObj.accountingYtd = "";
        DataObj.deviationYtd = "";
        DataObj.deviationYtdPct = "";
        DataObj.numberFormat = "Text";
        DataObj.note = null;
        DataObj.rowType = "DetailRow";
        DataObj.IsSecondGrid = false;
        DataObj.deviationActionAmount = "";
        DataObj.forecastInclAction = "";
        DataObj.deviationAfterAction = "";
        DataObjArray.Add(DataObj);

        //second data list
        foreach (var item in data2)
        {
            DataObj = FormateData(commentList, item);
            DataObjArray.Add(DataObj);
        }

        //sumarry for Data 2    ( data - data2)
        DataObj = new JObject();
        DataObj.actualAmtLastYear = data.Sum(x => x.actualAmtLastYear) - data2.Sum(x => x.actualAmtLastYear);
        DataObj.orgBudAmtYear = data.Sum(x => x.orgBudAmtYear) - data2.Sum(x => x.orgBudAmtYear);
        DataObj.revisedBudAmtYear = data.Sum(x => x.revisedBudAmtYear) - data2.Sum(x => x.revisedBudAmtYear);
        DataObj.actualAmtYear = data.Sum(x => x.actualAmtYear) - data2.Sum(x => x.actualAmtYear);
        DataObj.orgBudAmtLastYear = data.Sum(x => x.orgBudAmtLastYear) - data2.Sum(x => x.orgBudAmtLastYear);
        DataObj.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear) - data2.Sum(x => x.revisedAmtLastYear);
        DataObj.revisedAmtLastYearByAuth = data.Sum(x => x.revisedAmtLastYearByAuth) - data2.Sum(x => x.revisedAmtLastYearByAuth);
        DataObj.revisedAmtYearByAuth = data.Sum(x => x.revisedAmtYearByAuth) - data2.Sum(x => x.revisedAmtYearByAuth);
        DataObj.finPlanYear1 = data.Sum(x => x.finPlanYear1) - data2.Sum(x => x.finPlanYear1);
        DataObj.finPlanYear2 = data.Sum(x => x.finPlanYear2) - data2.Sum(x => x.finPlanYear2);
        DataObj.finPlanYear3 = data.Sum(x => x.finPlanYear3) - data2.Sum(x => x.finPlanYear3);
        DataObj.finPlanYear4 = data.Sum(x => x.finPlanYear4) - data2.Sum(x => x.finPlanYear4);
        DataObj.budgetChange = data.Sum(x => x.budgetChange) - data2.Sum(x => x.budgetChange);

        decimal forAmtPctData2 = data2.Sum(x => x.revisedBudAmtYear) != 0 ? (((data2.Sum(x => x.revisedBudAmtYear) - data2.Sum(x => x.forecastAmount)) / data2.Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
        forAmtPctData = data.Sum(x => x.revisedBudAmtYear) != 0 ? (((data.Sum(x => x.revisedBudAmtYear) - data.Sum(x => x.forecastAmount)) / data.Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
        DataObj.deviationForecastPct = forAmtPctData - forAmtPctData2;

        DataObj.deviationForecastPct = data.Sum(x => x.deviationForecastPct) - data2.Sum(x => x.deviationForecastPct);
        DataObj.deviationForecast = data.Sum(x => x.deviationForecast) - data2.Sum(x => x.deviationForecast);
        DataObj.forecastAmount = data.Sum(x => x.forecastAmount) - data2.Sum(x => x.forecastAmount);
        DataObj.budgetFormDesc = langStringValues["BudForm_Sum_1B_sum_2"].LangText;
        DataObj.id = "-1";
        DataObj.LineGroup = null;
        DataObj.LineGroupId = "-1";
        DataObj.numberFormat = "n0";
        DataObj.semi = true;
        DataObj.clickable = false;
        DataObj.note = null;
        DataObj.rowType = "SumRowGrandTotal";
        DataObj.budgetYtd = data.Sum(x => x.budgetYtd) - data2.Sum(x => x.budgetYtd);
        DataObj.accountingYtd = data.Sum(x => x.accountingYtd) - data2.Sum(x => x.accountingYtd);
        DataObj.deviationYtd = data.Sum(x => x.deviationYtd) - data2.Sum(x => x.deviationYtd);
        DataObj.deviationYtdPct = 0;
        DataObj.IsSecondGrid = false;
        DataObj.deviationActionAmount = data.Sum(x => x.deviationActionAmount) - data2.Sum(x => x.deviationActionAmount);
        DataObj.forecastInclAction = data.Sum(x => x.forecastInclAction) - data2.Sum(x => x.forecastInclAction);
        DataObj.deviationAfterAction = data.Sum(x => x.deviationAfterAction) - data2.Sum(x => x.deviationAfterAction);
        DataObjArray.Add(DataObj);

        finalData.data = DataObjArray;
        finalData.columns = await GetBudgetFormColumnAsync(budgetYear, userId, "1B", docType, forecastPeriod);
        finalData.header = header;
        return finalData;
    }



    private async Task<List<vw_bmdoc_1B_consolidated>> Get1BDataFromDBConsolidationAsync(int budgetYear, string userId, int docType, int forecastPeriod)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        var dbContext = await _pUtility.GetTenantDBContextAsync();

        List<vw_bmdoc_1B_consolidated> data = await (from a in dbContext.vw_bmdoc_1B_consolidated
            where a.budget_year == budgetYear && a.fk_tenant_id == userDetails.tenant_id
            select a).ToListAsync();

        return data;
    }



    private static List<clsBudgetFormHelper> GetGroupedDataConsolidation(List<vw_bmdoc_1B_consolidated> budGet1BData)
    {
        return (from a in budGet1BData
            where !a.sum_level.Contains("Foretak")
            group a by new { sum_level = a.sum_level, aggregate_id = a.aggregate_id.Trim(), aggregate_name = a.aggregate_name.Trim() } into grp
            select new clsBudgetFormHelper()
            {
                SubHearderName = grp.Key.sum_level,
                SubHearderId = grp.Key.sum_level,
                LineGroupId = grp.Key.aggregate_id,
                LineGroup = grp.Key.aggregate_name,
                id = grp.Key.aggregate_id,
                budgetFormDesc = grp.Key.aggregate_name,
                actualAmtYear = 0,// grp.Sum(x => x.actual_amt_year),
                revisedBudAmtYear = 0,// grp.sum(x => x.revised_bud_amt_year),
                orgBudAmtYear = 0,// grp.sum(x => x.org_bud_amt_year),
                actualAmtLastYear = grp.Sum(x => x.actual_amt_last_year),
                orgBudAmtLastYear = grp.Sum(x => x.org_bud_amt_last_year),
                revisedAmtLastYear = grp.Sum(x => x.revised_bud_amt_last_year),
                revisedAmtLastYearByAuth = grp.Sum(x => x.revised_bud_auth_last_year),
                revisedAmtYearByAuth = grp.Sum(x => x.revised_bud_auth_year),
                finPlanYear1 = grp.Sum(x => x.finplan_year_1_amount),
                finPlanYear2 = grp.Sum(x => x.finplan_year_2_amount),
                finPlanYear3 = grp.Sum(x => x.finplan_year_3_amount),
                finPlanYear4 = grp.Sum(x => x.finplan_year_4_amount),
                forecastAmount = 0,// grp.Sum(x => x.forecast_amount),
                deviationForecast = 0,// grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount),
                deviationForecastPct = 0,// grp.Sum(x => x.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount)) / grp.Sum(x => x.revised_bud_amt_year)) * 100 : 0,
                //     budgetChange = grp.Sum(x => x.budget_change.Value),
                budgetYtd = 0,// grp.Sum(x => x.budget_ytd),
                accountingYtd = 0,// grp.Sum(x => x.accounting_ytd),
                deviationYtd = 0,// grp.Sum(x => x.deviation_ytd),
                deviationYtdPct = 0,
                numberFormat = "n0",
                semi = false,
                clickable = true,
                rowType = "DetailRow",
                gridValue = budget_form_Grid_type.BudgetForm1BGrid1.ToString(),
                deviationActionAmount = 0,// grp.Sum(x => x.deviation_action_amount),
                forecastInclAction = 0,//grp.Sum(x => x.forecast_amount) + grp.Sum(x => x.deviation_action_amount),
                deviationAfterAction = 0// grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount) - grp.Sum(x => x.deviation_action_amount)
            }).OrderBy(x => x.id).ThenBy(x => x.budgetFormDesc).ToList();
    }



    private static List<clsBudgetFormHelper> GetGroupedDataConsolidation(int budgetYear, UserData userDetails, TenantDBContext dbContext, List<vw_bmdoc_1B_consolidated> budGet1BData)
    {
        return (from a in budGet1BData
            where a.sum_level.Contains("Foretak")
            group a by new { sum_level = a.sum_level, aggregate_id = a.aggregate_id.Trim(), aggregate_name = a.aggregate_name.Trim() } into grp
            select new clsBudgetFormHelper()
            {
                LineGroupId = grp.Key.aggregate_id,
                LineGroup = grp.Key.aggregate_name,
                id = grp.Key.aggregate_id,
                budgetFormDesc = grp.Key.aggregate_name,
                actualAmtYear = 0,// grp.Sum(x => x.actual_amt_year),
                revisedBudAmtYear = 0,// grp.sum(x => x.revised_bud_amt_year),
                orgBudAmtYear = 0,// grp.sum(x => x.org_bud_amt_year),
                actualAmtLastYear = grp.Sum(x => x.actual_amt_last_year),
                orgBudAmtLastYear = grp.Sum(x => x.org_bud_amt_last_year),
                revisedAmtLastYear = grp.Sum(x => x.revised_bud_amt_last_year),
                revisedAmtLastYearByAuth = grp.Sum(x => x.revised_bud_auth_last_year),
                revisedAmtYearByAuth = grp.Sum(x => x.revised_bud_auth_year),
                finPlanYear1 = grp.Sum(x => x.finplan_year_1_amount),
                finPlanYear2 = grp.Sum(x => x.finplan_year_2_amount),
                finPlanYear3 = grp.Sum(x => x.finplan_year_3_amount),
                finPlanYear4 = grp.Sum(x => x.finplan_year_4_amount),
                forecastAmount = 0,// grp.Sum(x => x.forecast_amount),
                deviationForecast = 0,// grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount),
                deviationForecastPct = 0,// grp.Sum(x => x.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount)) / grp.Sum(x => x.revised_bud_amt_year)) * 100 : 0,
                //     budgetChange = grp.Sum(x => x.budget_change.Value),
                budgetYtd = 0,// grp.Sum(x => x.budget_ytd),
                accountingYtd = 0,// grp.Sum(x => x.accounting_ytd),
                deviationYtd = 0,// grp.Sum(x => x.deviation_ytd),
                deviationYtdPct = 0,
                numberFormat = "n0",
                semi = false,
                clickable = true,
                rowType = "DetailRow",
                gridValue = budget_form_Grid_type.BudgetForm1BGrid1.ToString(),
                deviationActionAmount = 0,// grp.Sum(x => x.deviation_action_amount),
                forecastInclAction = 0,//grp.Sum(x => x.forecast_amount) + grp.Sum(x => x.deviation_action_amount),
                deviationAfterAction = 0// grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount) - grp.Sum(x => x.deviation_action_amount)
            }).OrderBy(x => x.id).ThenBy(x => x.budgetFormDesc).ToList();
    }


    public JObject Get1BDataConsolidationForMR(int budgetYear, string userId, int docType, int forecastPeriod = 0, string budgetPhaseId = null, bool showOnlyModified = false)
    {
        return Get1BDataConsolidationForMRAsync(budgetYear, userId, docType, forecastPeriod, budgetPhaseId, showOnlyModified).GetAwaiter().GetResult();
    }



    public async Task<JObject> Get1BDataConsolidationForMRAsync(int budgetYear, string userId, int docType, int forecastPeriod = 0, string budgetPhaseId = null, bool showOnlyModified = false)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValues = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetForm");

        dynamic header = new JObject();
        header.title = langStringValues["BudForm_1B_title"].LangText;
        header.descriptiontip = langStringValues["BudForm_1B_desc"].LangText;
        TenantDBContext dbContext = await _pUtility.GetTenantDBContextAsync();
        dbContext.Database.SetCommandTimeout(900);
        List<tbf_budget_form_comment> commentList = await GetCommentListAsync(budgetYear, userDetails, dbContext, clsConstants.budget_form_type.BudgetForm1B.ToString(), docType, forecastPeriod);
        List<vw_mrdoc_1B_consolidated> budGet1BDataFridm = await Get1BDataFromDBConsolidationForMRAsync(budgetYear, userId, docType, forecastPeriod);
        List<tbf_budget_form_1B> budGet1BData = await Get1BDataFromDBAsync(budgetYear, userId, docType, forecastPeriod, string.Empty, null, null);
        dynamic finalData = new JObject();

        List<int> changeIds = await GetChangeIdsAsync(budgetYear, budgetPhaseId, userId, showOnlyModified);

        budGet1BData = Filter1BByChangeId(budGet1BData, changeIds);
        List<clsBudgetFormHelper> data = GetGroupedData(budGet1BData);
        List<clsBudgetFormHelper> data2 = await GetGroupedData1(budgetYear, userDetails, dbContext, budGet1BData);
        List<clsBudgetFormHelper> dataFridm = GetGroupedDataConsolidationForMR(budGet1BDataFridm);
        List<clsBudgetFormHelper> data2Frdim = GetGroupedDataConsolidationForMR(budgetYear, userDetails, dbContext, budGet1BDataFridm);
        dynamic DataObjArray;
        dynamic DataObj;
        DataObjArray = FormatData1(commentList, dataFridm, true);

        //sum dta1
        DataObj = new JObject();
        DataObj.actualAmtLastYear = dataFridm.Sum(x => x.actualAmtLastYear);
        DataObj.orgBudAmtYear = dataFridm.Sum(x => x.orgBudAmtYear);
        DataObj.revisedBudAmtYear = dataFridm.Sum(x => x.revisedBudAmtYear);
        DataObj.actualAmtYear = dataFridm.Sum(x => x.actualAmtYear);
        DataObj.orgBudAmtLastYear = dataFridm.Sum(x => x.orgBudAmtLastYear);
        DataObj.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear);
        DataObj.finPlanYear1 = dataFridm.Sum(x => x.finPlanYear1);
        DataObj.finPlanYear2 = dataFridm.Sum(x => x.finPlanYear2);
        DataObj.finPlanYear3 = dataFridm.Sum(x => x.finPlanYear3);
        DataObj.finPlanYear4 = dataFridm.Sum(x => x.finPlanYear4);
        DataObj.budgetFormDesc = langStringValues["BudForm_Sum_1B"].LangText;
        DataObj.budgetChange = dataFridm.Sum(x => x.budgetChange);

        decimal forAmtPctData = dataFridm.Sum(x => x.revisedBudAmtYear) != 0 ? (((data.Sum(x => x.revisedBudAmtYear) - dataFridm.Sum(x => x.forecastAmount)) / dataFridm.Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
        DataObj.deviationForecastPct = forAmtPctData;
        DataObj.deviationForecast = dataFridm.Sum(x => x.deviationForecast);
        DataObj.forecastAmount = dataFridm.Sum(x => x.forecastAmount);
        DataObj.id = "-1";
        DataObj.LineGroup = null;
        DataObj.LineGroupId = "-1";
        DataObj.numberFormat = "n0";
        DataObj.semi = true;
        DataObj.clickable = false;
        DataObj.note = null;
        DataObj.rowType = "SumRowGrandTotal";
        DataObj.budgetYtd = dataFridm.Sum(x => x.budgetYtd);
        DataObj.accountingYtd = dataFridm.Sum(x => x.accountingYtd);
        DataObj.deviationYtd = dataFridm.Sum(x => x.deviationYtd);
        DataObj.deviationYtdPct = 0;
        DataObj.IsSecondGrid = false;
        DataObj.deviationActionAmount = dataFridm.Sum(x => x.deviationActionAmount);
        DataObj.forecastInclAction = dataFridm.Sum(x => x.forecastInclAction);
        DataObj.deviationAfterAction = dataFridm.Sum(x => x.deviationAfterAction);
        DataObjArray.Add(DataObj);

        //empty Row
        DataObj = new JObject();
        DataObj.LineGroupId = null;
        DataObj.LineGroup = null;
        DataObj.id = null;
        DataObj.budgetFormDesc = "";
        DataObj.actualAmtYear = "";
        DataObj.revisedBudAmtYear = "";
        DataObj.orgBudAmtYear = "";
        DataObj.actualAmtLastYear = "";
        DataObj.orgBudAmtLastYear = "";
        DataObj.revisedAmtLastYear = "";
        DataObj.finPlanYear1 = "";
        DataObj.finPlanYear2 = "";
        DataObj.finPlanYear3 = "";
        DataObj.finPlanYear4 = "";
        DataObj.budgetChange = "";
        DataObj.deviationForecastPct = "";
        DataObj.deviationForecast = "";
        DataObj.forecastAmount = "";
        DataObj.budgetYtd = "";
        DataObj.accountingYtd = "";
        DataObj.deviationYtd = "";
        DataObj.deviationYtdPct = "";
        DataObj.semi = true;
        DataObj.clickable = false;
        DataObj.numberFormat = "Text";
        DataObj.note = null;
        DataObj.rowType = "DetailRow";
        DataObj.IsSecondGrid = false;
        DataObj.deviationActionAmount = "";
        DataObj.forecastInclAction = "";
        DataObj.deviationAfterAction = "";
        DataObjArray.Add(DataObj);

        //fridm rows
        foreach (var item in data2Frdim)
        {
            DataObj = FormateData(commentList, item);
            DataObjArray.Add(DataObj);
        }
        //sum fridm data1
        DataObj = new JObject();
        DataObj.actualAmtLastYear = data2Frdim.Sum(x => x.actualAmtLastYear);
        DataObj.orgBudAmtYear = data2Frdim.Sum(x => x.orgBudAmtYear);
        DataObj.revisedBudAmtYear = data2Frdim.Sum(x => x.revisedBudAmtYear);
        DataObj.actualAmtYear = data2Frdim.Sum(x => x.actualAmtYear);
        DataObj.orgBudAmtLastYear = data2Frdim.Sum(x => x.orgBudAmtLastYear);
        DataObj.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear);
        DataObj.finPlanYear1 = data2Frdim.Sum(x => x.finPlanYear1);
        DataObj.finPlanYear2 = data2Frdim.Sum(x => x.finPlanYear2);
        DataObj.finPlanYear3 = data2Frdim.Sum(x => x.finPlanYear3);
        DataObj.finPlanYear4 = data2Frdim.Sum(x => x.finPlanYear4);
        DataObj.budgetFormDesc = langStringValues["BudForm_Sum_1B_fridm"].LangText;
        DataObj.budgetChange = data2Frdim.Sum(x => x.budgetChange);

        forAmtPctData = data2Frdim.Sum(x => x.revisedBudAmtYear) != 0 ? (((data.Sum(x => x.revisedBudAmtYear) - data.Sum(x => x.forecastAmount)) / data.Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
        DataObj.deviationForecastPct = forAmtPctData;
        DataObj.deviationForecast = data2Frdim.Sum(x => x.deviationForecast);
        DataObj.forecastAmount = data.Sum(x => x.forecastAmount);
        DataObj.id = "-999";
        DataObj.LineGroup = null;
        DataObj.LineGroupId = "-1";
        DataObj.numberFormat = "n0";
        DataObj.semi = true;
        DataObj.clickable = false;
        DataObj.note = null;
        DataObj.rowType = "SumRowGrandTotal";
        DataObj.budgetYtd = data2Frdim.Sum(x => x.budgetYtd);
        DataObj.accountingYtd = data2Frdim.Sum(x => x.accountingYtd);
        DataObj.deviationYtd = data2Frdim.Sum(x => x.deviationYtd);
        DataObj.deviationYtdPct = 0;
        DataObj.IsSecondGrid = false;
        DataObj.deviationActionAmount = data2Frdim.Sum(x => x.deviationActionAmount);
        DataObj.forecastInclAction = data2Frdim.Sum(x => x.forecastInclAction);
        DataObj.deviationAfterAction = data2Frdim.Sum(x => x.deviationAfterAction);
        DataObjArray.Add(DataObj);

        //sum fridm dta1+ data
        DataObj = new JObject();
        DataObj.actualAmtLastYear = data2Frdim.Sum(x => x.actualAmtLastYear) + dataFridm.Sum(x => x.actualAmtLastYear);
        DataObj.orgBudAmtYear = data2Frdim.Sum(x => x.orgBudAmtYear) + dataFridm.Sum(x => x.orgBudAmtYear);
        DataObj.revisedBudAmtYear = data2Frdim.Sum(x => x.revisedBudAmtYear) + dataFridm.Sum(x => x.revisedBudAmtYear);
        DataObj.actualAmtYear = data2Frdim.Sum(x => x.actualAmtYear) + dataFridm.Sum(x => x.actualAmtYear);
        DataObj.orgBudAmtLastYear = data2Frdim.Sum(x => x.orgBudAmtLastYear) + dataFridm.Sum(x => x.orgBudAmtLastYear);
        DataObj.revisedAmtLastYear = data2Frdim.Sum(x => x.revisedAmtLastYear) + data.Sum(x => x.revisedAmtLastYear);
        DataObj.finPlanYear1 = data2Frdim.Sum(x => x.finPlanYear1) + dataFridm.Sum(x => x.finPlanYear1);
        DataObj.finPlanYear2 = data2Frdim.Sum(x => x.finPlanYear2) + dataFridm.Sum(x => x.finPlanYear2);
        DataObj.finPlanYear3 = data2Frdim.Sum(x => x.finPlanYear3) + dataFridm.Sum(x => x.finPlanYear3);
        DataObj.finPlanYear4 = data2Frdim.Sum(x => x.finPlanYear4) + dataFridm.Sum(x => x.finPlanYear4);
        DataObj.budgetFormDesc = langStringValues["BudForm_Sum_1B_with_fridm_Sum"].LangText;
        DataObj.budgetChange = data2Frdim.Sum(x => x.budgetChange) + dataFridm.Sum(x => x.budgetChange);
        var forAmtPctData3 = data2Frdim.Sum(x => x.revisedBudAmtYear) != 0 ? (((data2Frdim.Sum(x => x.revisedBudAmtYear) - data2Frdim.Sum(x => x.forecastAmount)) / data2Frdim.Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
        forAmtPctData = dataFridm.Sum(x => x.revisedBudAmtYear) != 0 ? (((dataFridm.Sum(x => x.revisedBudAmtYear) - dataFridm.Sum(x => x.forecastAmount)) / data.Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
        DataObj.deviationForecastPct = forAmtPctData + forAmtPctData3;
        DataObj.deviationForecast = data2Frdim.Sum(x => x.deviationForecast) + dataFridm.Sum(x => x.deviationForecast);
        DataObj.forecastAmount = data2Frdim.Sum(x => x.forecastAmount) + dataFridm.Sum(x => x.forecastAmount);
        DataObj.id = "-2";
        DataObj.LineGroup = null;
        DataObj.LineGroupId = "-1";
        DataObj.numberFormat = "n0";
        DataObj.semi = true;
        DataObj.clickable = false;
        DataObj.note = null;
        DataObj.rowType = "SumRowGrandTotal";
        DataObj.budgetYtd = data2Frdim.Sum(x => x.budgetYtd) + dataFridm.Sum(x => x.budgetYtd);
        DataObj.accountingYtd = data2Frdim.Sum(x => x.accountingYtd) + dataFridm.Sum(x => x.accountingYtd);
        DataObj.deviationYtd = data2Frdim.Sum(x => x.deviationYtd) + dataFridm.Sum(x => x.deviationYtd);
        DataObj.deviationYtdPct = 0;
        DataObj.IsSecondGrid = false;
        DataObj.deviationActionAmount = data2Frdim.Sum(x => x.deviationActionAmount) + dataFridm.Sum(x => x.deviationActionAmount);
        DataObj.forecastInclAction = data2Frdim.Sum(x => x.forecastInclAction) + dataFridm.Sum(x => x.forecastInclAction);
        DataObj.deviationAfterAction = data2Frdim.Sum(x => x.deviationAfterAction) + dataFridm.Sum(x => x.deviationAfterAction);
        DataObjArray.Add(DataObj);

        //empty Row
        DataObj = new JObject();
        DataObj.LineGroupId = null;
        DataObj.LineGroup = null;
        DataObj.id = null;
        DataObj.budgetFormDesc = "";
        DataObj.actualAmtYear = "";
        DataObj.revisedBudAmtYear = "";
        DataObj.orgBudAmtYear = "";
        DataObj.actualAmtLastYear = "";
        DataObj.orgBudAmtLastYear = "";
        DataObj.revisedAmtLastYear = "";
        DataObj.finPlanYear1 = "";
        DataObj.finPlanYear2 = "";
        DataObj.finPlanYear3 = "";
        DataObj.finPlanYear4 = "";
        DataObj.budgetChange = "";
        DataObj.deviationForecastPct = "";
        DataObj.deviationForecast = "";
        DataObj.forecastAmount = "";
        DataObj.budgetYtd = "";
        DataObj.accountingYtd = "";
        DataObj.deviationYtd = "";
        DataObj.deviationYtdPct = "";
        DataObj.semi = true;
        DataObj.clickable = false;
        DataObj.numberFormat = "Text";
        DataObj.note = null;
        DataObj.rowType = "DetailRow";
        DataObj.IsSecondGrid = false;
        DataObj.deviationActionAmount = "";
        DataObj.forecastInclAction = "";
        DataObj.deviationAfterAction = "";
        DataObjArray.Add(DataObj);

        //heading Row
        DataObj = new JObject();
        DataObj.LineGroupId = null;
        DataObj.LineGroup = null;
        DataObj.id = null;
        DataObj.budgetFormDesc = langStringValues["BudForm_Table_2_heading_1B"].LangText;
        DataObj.actualAmtYear = "";
        DataObj.revisedBudAmtYear = "";
        DataObj.orgBudAmtYear = "";
        DataObj.actualAmtLastYear = "";
        DataObj.orgBudAmtLastYear = "";
        DataObj.revisedAmtLastYear = "";
        DataObj.finPlanYear1 = "";
        DataObj.finPlanYear2 = "";
        DataObj.finPlanYear3 = "";
        DataObj.finPlanYear4 = "";
        DataObj.budgetChange = "";
        DataObj.deviationForecastPct = "";
        DataObj.deviationForecast = "";
        DataObj.forecastAmount = "";
        DataObj.semi = true;
        DataObj.clickable = false;
        DataObj.budgetYtd = "";
        DataObj.accountingYtd = "";
        DataObj.deviationYtd = "";
        DataObj.deviationYtdPct = "";
        DataObj.numberFormat = "Text";
        DataObj.note = null;
        DataObj.rowType = "DetailRow";
        DataObj.IsSecondGrid = false;
        DataObj.deviationActionAmount = "";
        DataObj.forecastInclAction = "";
        DataObj.deviationAfterAction = "";
        DataObjArray.Add(DataObj);

        //second data list
        foreach (var item in data2)
        {
            DataObj = FormateData(commentList, item);
            DataObjArray.Add(DataObj);
        }

        //sumarry for Data 2    ( data - data2)
        DataObj = new JObject();
        DataObj.actualAmtLastYear = data.Sum(x => x.actualAmtLastYear) - data2.Sum(x => x.actualAmtLastYear);
        DataObj.orgBudAmtYear = data.Sum(x => x.orgBudAmtYear) - data2.Sum(x => x.orgBudAmtYear);
        DataObj.revisedBudAmtYear = data.Sum(x => x.revisedBudAmtYear) - data2.Sum(x => x.revisedBudAmtYear);
        DataObj.actualAmtYear = data.Sum(x => x.actualAmtYear) - data2.Sum(x => x.actualAmtYear);
        DataObj.orgBudAmtLastYear = data.Sum(x => x.orgBudAmtLastYear) - data2.Sum(x => x.orgBudAmtLastYear);
        DataObj.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear) - data2.Sum(x => x.revisedAmtLastYear);
        DataObj.finPlanYear1 = data.Sum(x => x.finPlanYear1) - data2.Sum(x => x.finPlanYear1);
        DataObj.finPlanYear2 = data.Sum(x => x.finPlanYear2) - data2.Sum(x => x.finPlanYear2);
        DataObj.finPlanYear3 = data.Sum(x => x.finPlanYear3) - data2.Sum(x => x.finPlanYear3);
        DataObj.finPlanYear4 = data.Sum(x => x.finPlanYear4) - data2.Sum(x => x.finPlanYear4);
        DataObj.budgetChange = data.Sum(x => x.budgetChange) - data2.Sum(x => x.budgetChange);

        decimal forAmtPctData2 = data2.Sum(x => x.revisedBudAmtYear) != 0 ? (((data2.Sum(x => x.revisedBudAmtYear) - data2.Sum(x => x.forecastAmount)) / data2.Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
        forAmtPctData = data.Sum(x => x.revisedBudAmtYear) != 0 ? (((data.Sum(x => x.revisedBudAmtYear) - data.Sum(x => x.forecastAmount)) / data.Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
        DataObj.deviationForecastPct = forAmtPctData - forAmtPctData2;

        DataObj.deviationForecastPct = data.Sum(x => x.deviationForecastPct) - data2.Sum(x => x.deviationForecastPct);
        DataObj.deviationForecast = data.Sum(x => x.deviationForecast) - data2.Sum(x => x.deviationForecast);
        DataObj.forecastAmount = data.Sum(x => x.forecastAmount) - data2.Sum(x => x.forecastAmount);
        DataObj.budgetFormDesc = langStringValues["BudForm_Sum_1B_sum_2"].LangText;
        DataObj.id = "-1";
        DataObj.LineGroup = null;
        DataObj.LineGroupId = "-1";
        DataObj.numberFormat = "n0";
        DataObj.semi = true;
        DataObj.clickable = false;
        DataObj.note = null;
        DataObj.rowType = "SumRowGrandTotal";
        DataObj.budgetYtd = data.Sum(x => x.budgetYtd) - data2.Sum(x => x.budgetYtd);
        DataObj.accountingYtd = data.Sum(x => x.accountingYtd) - data2.Sum(x => x.accountingYtd);
        DataObj.deviationYtd = data.Sum(x => x.deviationYtd) - data2.Sum(x => x.deviationYtd);
        DataObj.deviationYtdPct = 0;
        DataObj.IsSecondGrid = false;
        DataObj.deviationActionAmount = data.Sum(x => x.deviationActionAmount) - data2.Sum(x => x.deviationActionAmount);
        DataObj.forecastInclAction = data.Sum(x => x.forecastInclAction) - data2.Sum(x => x.forecastInclAction);
        DataObj.deviationAfterAction = data.Sum(x => x.deviationAfterAction) - data2.Sum(x => x.deviationAfterAction);
        DataObjArray.Add(DataObj);

        finalData.data = DataObjArray;
        finalData.columns = await GetBudgetFormColumnAsync(budgetYear, userId, "1B", docType, forecastPeriod);
        finalData.header = header;
        return finalData;
    }



    private async Task<List<vw_mrdoc_1B_consolidated>> Get1BDataFromDBConsolidationForMRAsync(int budgetYear, string userId, int docType, int forecastPeriod)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        var dbContext = await _pUtility.GetTenantDBContextAsync();

        List<vw_mrdoc_1B_consolidated> data = await (from a in dbContext.vw_mrdoc_1B_consolidated
            where a.forecast_period == forecastPeriod && a.fk_tenant_id == userDetails.tenant_id
            select a).ToListAsync();

        return data;
    }



    private static List<clsBudgetFormHelper> GetGroupedDataConsolidationForMR(int budgetYear, UserData userDetails, TenantDBContext dbContext, List<vw_mrdoc_1B_consolidated> budGet1BData)
    {
        return (from a in budGet1BData
            where a.sum_level.Contains("Foretak")
            group a by new { sum_level = a.sum_level, aggregate_id = a.aggregate_id.Trim(), aggregate_name = a.aggregate_name.Trim() } into grp
            select new clsBudgetFormHelper()
            {
                LineGroupId = grp.Key.aggregate_id,
                LineGroup = grp.Key.aggregate_name,
                id = grp.Key.aggregate_id,
                budgetFormDesc = grp.Key.aggregate_name,
                actualAmtYear = grp.Sum(x => x.actual_amt_year),
                revisedBudAmtYear = grp.Sum(x => x.revised_bud_amt_year),
                orgBudAmtYear = grp.Sum(x => x.org_bud_amt_year),
                actualAmtLastYear = grp.Sum(x => x.actual_amt_last_year),
                orgBudAmtLastYear = 0,// grp.Sum(x => x.org_bud_amt_last_year),
                revisedAmtLastYear = 0,// grp.Sum(x => x.revised_bud_amt_last_year),
                finPlanYear1 = 0,// grp.Sum(x => x.finplan_year_1_amount),
                finPlanYear2 = 0,// grp.Sum(x => x.finplan_year_2_amount),
                finPlanYear3 = 0,// grp.Sum(x => x.finplan_year_3_amount),
                finPlanYear4 = 0,//grp.Sum(x => x.finplan_year_4_amount),
                forecastAmount = 0,// grp.Sum(x => x.forecast_amount),
                deviationForecast = 0,// grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount),
                deviationForecastPct = 0,// grp.Sum(x => x.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount)) / grp.Sum(x => x.revised_bud_amt_year)) * 100 : 0,
                //     budgetChange = grp.Sum(x => x.budget_change.Value),
                budgetYtd = 0,// grp.Sum(x => x.budget_ytd),
                accountingYtd = 0,// grp.Sum(x => x.accounting_ytd),
                deviationYtd = 0,// grp.Sum(x => x.deviation_ytd),
                deviationYtdPct = 0,
                numberFormat = "n0",
                semi = false,
                clickable = true,
                rowType = "DetailRow",
                gridValue = budget_form_Grid_type.BudgetForm1BGrid1.ToString(),
                deviationActionAmount = 0,// grp.Sum(x => x.deviation_action_amount),
                forecastInclAction = 0,//grp.Sum(x => x.forecast_amount) + grp.Sum(x => x.deviation_action_amount),
                deviationAfterAction = 0// grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount) - grp.Sum(x => x.deviation_action_amount)
            }).OrderBy(x => x.id).ThenBy(x => x.budgetFormDesc).ToList();
    }



    private static List<clsBudgetFormHelper> GetGroupedDataConsolidationForMR(List<vw_mrdoc_1B_consolidated> budGet1BData)
    {
        return (from a in budGet1BData
            where !a.sum_level.Contains("Foretak")
            group a by new { sum_level = a.sum_level, aggregate_id = a.aggregate_id.Trim(), aggregate_name = a.aggregate_name.Trim() } into grp
            select new clsBudgetFormHelper()
            {
                SubHearderName = grp.Key.sum_level,
                SubHearderId = grp.Key.sum_level,
                LineGroupId = grp.Key.aggregate_id,
                LineGroup = grp.Key.aggregate_name,
                id = grp.Key.aggregate_id,
                budgetFormDesc = grp.Key.aggregate_name,
                actualAmtYear = grp.Sum(x => x.actual_amt_year),
                revisedBudAmtYear = grp.Sum(x => x.revised_bud_amt_year),
                orgBudAmtYear = grp.Sum(x => x.org_bud_amt_year),
                actualAmtLastYear = grp.Sum(x => x.actual_amt_last_year),
                orgBudAmtLastYear = 0,//grp.Sum(x => x.org_bud_amt_last_year),
                revisedAmtLastYear = 0,//grp.Sum(x => x.revised_bud_amt_last_year),
                finPlanYear1 = 0,//grp.Sum(x => x.finplan_year_1_amount),
                finPlanYear2 = 0,//grp.Sum(x => x.finplan_year_2_amount),
                finPlanYear3 = 0,//grp.Sum(x => x.finplan_year_3_amount),
                finPlanYear4 = 0,//grp.Sum(x => x.finplan_year_4_amount),
                forecastAmount = 0,// grp.Sum(x => x.forecast_amount),
                deviationForecast = 0,// grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount),
                deviationForecastPct = 0,// grp.Sum(x => x.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount)) / grp.Sum(x => x.revised_bud_amt_year)) * 100 : 0,
                //     budgetChange = grp.Sum(x => x.budget_change.Value),
                budgetYtd = 0,// grp.Sum(x => x.budget_ytd),
                accountingYtd = 0,// grp.Sum(x => x.accounting_ytd),
                deviationYtd = 0,// grp.Sum(x => x.deviation_ytd),
                deviationYtdPct = 0,
                numberFormat = "n0",
                semi = false,
                clickable = true,
                rowType = "DetailRow",
                gridValue = budget_form_Grid_type.BudgetForm1BGrid1.ToString(),
                deviationActionAmount = 0,// grp.Sum(x => x.deviation_action_amount),
                forecastInclAction = 0,//grp.Sum(x => x.forecast_amount) + grp.Sum(x => x.deviation_action_amount),
                deviationAfterAction = 0// grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount) - grp.Sum(x => x.deviation_action_amount)
            }).OrderBy(x => x.id).ThenBy(x => x.budgetFormDesc).ToList();
    }



    public JObject GetYearlyBudForm1BDataConsolidation(int budgetYear, string userId, int docType, int forecastPeriod = 0, string budgetPhaseId = null, bool showOnlyModified = false)
    {
        return GetYearlyBudForm1BDataConsolidationAsync(budgetYear, userId, docType, forecastPeriod, budgetPhaseId, showOnlyModified).GetAwaiter().GetResult();
    }



    public async Task<JObject> GetYearlyBudForm1BDataConsolidationAsync(int budgetYear, string userId, int docType, int forecastPeriod = 0, string budgetPhaseId = null, bool showOnlyModified = false)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValues = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetForm");

        dynamic header = new JObject();
        header.title = langStringValues["BudForm_1B_title"].LangText;
        header.descriptiontip = langStringValues["BudForm_1B_desc"].LangText;
        TenantDBContext dbContext = await _pUtility.GetTenantDBContextAsync();
        dbContext.Database.SetCommandTimeout(900);
        List<tbf_budget_form_comment> commentList = await GetCommentListAsync(budgetYear, userDetails, dbContext, clsConstants.budget_form_type.BudgetForm1B.ToString(), docType, forecastPeriod);
        List<vw_yb_1B_consolidated> budGet1BDataFridm = await GetYearlyBudForm1BDataFromDBConsolidationAsync(budgetYear, userId, docType, forecastPeriod);
        List<tbf_budget_form_1B> budGet1BData = await Get1BDataFromDBAsync(budgetYear, userId, docType, forecastPeriod, string.Empty, null, null);
        dynamic finalData = new JObject();

        List<int> changeIds = await GetChangeIdsAsync(budgetYear, budgetPhaseId, userId, showOnlyModified);

        budGet1BData = Filter1BByChangeId(budGet1BData, changeIds);
        List<clsBudgetFormHelper> data = GetGroupedData(budGet1BData);
        List<clsBudgetFormHelper> data2 = await GetGroupedData1(budgetYear, userDetails, dbContext, budGet1BData);
        List<clsBudgetFormHelper> dataFridm = GetYearlyBudFormGroupedDataConsolidation(budGet1BDataFridm);
        List<clsBudgetFormHelper> data2Frdim = GetYearlyBudFormGroupedDataConsolidation(budgetYear, userDetails, dbContext, budGet1BDataFridm);
        dynamic DataObjArray;
        dynamic DataObj;
        DataObjArray = FormatData1(commentList, dataFridm, true);

        //sum dta1
        DataObj = new JObject();
        DataObj.actualAmtLastYear = dataFridm.Sum(x => x.actualAmtLastYear);
        DataObj.orgBudAmtYear = dataFridm.Sum(x => x.orgBudAmtYear);
        DataObj.revisedBudAmtYear = dataFridm.Sum(x => x.revisedBudAmtYear);
        DataObj.actualAmtYear = dataFridm.Sum(x => x.actualAmtYear);
        DataObj.orgBudAmtLastYear = dataFridm.Sum(x => x.orgBudAmtLastYear);
        DataObj.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear);
        DataObj.finPlanYear1 = dataFridm.Sum(x => x.finPlanYear1);
        DataObj.finPlanYear2 = dataFridm.Sum(x => x.finPlanYear2);
        DataObj.finPlanYear3 = dataFridm.Sum(x => x.finPlanYear3);
        DataObj.finPlanYear4 = dataFridm.Sum(x => x.finPlanYear4);
        DataObj.budgetFormDesc = langStringValues["BudForm_Sum_1B"].LangText;
        DataObj.budgetChange = dataFridm.Sum(x => x.budgetChange);

        decimal forAmtPctData = dataFridm.Sum(x => x.revisedBudAmtYear) != 0 ? (((data.Sum(x => x.revisedBudAmtYear) - dataFridm.Sum(x => x.forecastAmount)) / dataFridm.Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
        DataObj.deviationForecastPct = forAmtPctData;
        DataObj.deviationForecast = dataFridm.Sum(x => x.deviationForecast);
        DataObj.forecastAmount = dataFridm.Sum(x => x.forecastAmount);
        DataObj.id = "-1";
        DataObj.LineGroup = null;
        DataObj.LineGroupId = "-1";
        DataObj.numberFormat = "n0";
        DataObj.semi = true;
        DataObj.clickable = false;
        DataObj.note = null;
        DataObj.rowType = "SumRowGrandTotal";
        DataObj.budgetYtd = dataFridm.Sum(x => x.budgetYtd);
        DataObj.accountingYtd = dataFridm.Sum(x => x.accountingYtd);
        DataObj.deviationYtd = dataFridm.Sum(x => x.deviationYtd);
        DataObj.deviationYtdPct = 0;
        DataObj.IsSecondGrid = false;
        DataObj.deviationActionAmount = dataFridm.Sum(x => x.deviationActionAmount);
        DataObj.forecastInclAction = dataFridm.Sum(x => x.forecastInclAction);
        DataObj.deviationAfterAction = dataFridm.Sum(x => x.deviationAfterAction);
        DataObjArray.Add(DataObj);

        //empty Row
        DataObj = new JObject();
        DataObj.LineGroupId = null;
        DataObj.LineGroup = null;
        DataObj.id = null;
        DataObj.budgetFormDesc = "";
        DataObj.actualAmtYear = "";
        DataObj.revisedBudAmtYear = "";
        DataObj.orgBudAmtYear = "";
        DataObj.actualAmtLastYear = "";
        DataObj.orgBudAmtLastYear = "";
        DataObj.revisedAmtLastYear = "";
        DataObj.finPlanYear1 = "";
        DataObj.finPlanYear2 = "";
        DataObj.finPlanYear3 = "";
        DataObj.finPlanYear4 = "";
        DataObj.budgetChange = "";
        DataObj.deviationForecastPct = "";
        DataObj.deviationForecast = "";
        DataObj.forecastAmount = "";
        DataObj.budgetYtd = "";
        DataObj.accountingYtd = "";
        DataObj.deviationYtd = "";
        DataObj.deviationYtdPct = "";
        DataObj.semi = true;
        DataObj.clickable = false;
        DataObj.numberFormat = "Text";
        DataObj.note = null;
        DataObj.rowType = "DetailRow";
        DataObj.IsSecondGrid = false;
        DataObj.deviationActionAmount = "";
        DataObj.forecastInclAction = "";
        DataObj.deviationAfterAction = "";
        DataObjArray.Add(DataObj);

        //fridm rows            
        foreach (var item in data2Frdim)
        {
            DataObj = FormateData(commentList, item);
            DataObjArray.Add(DataObj);
        }
        //sum fridm data1
        DataObj = new JObject();
        DataObj.actualAmtLastYear = data2Frdim.Sum(x => x.actualAmtLastYear);
        DataObj.orgBudAmtYear = data2Frdim.Sum(x => x.orgBudAmtYear);
        DataObj.revisedBudAmtYear = data2Frdim.Sum(x => x.revisedBudAmtYear);
        DataObj.actualAmtYear = data2Frdim.Sum(x => x.actualAmtYear);
        DataObj.orgBudAmtLastYear = data2Frdim.Sum(x => x.orgBudAmtLastYear);
        DataObj.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear);
        DataObj.finPlanYear1 = data2Frdim.Sum(x => x.finPlanYear1);
        DataObj.finPlanYear2 = data2Frdim.Sum(x => x.finPlanYear2);
        DataObj.finPlanYear3 = data2Frdim.Sum(x => x.finPlanYear3);
        DataObj.finPlanYear4 = data2Frdim.Sum(x => x.finPlanYear4);
        DataObj.budgetFormDesc = langStringValues["BudForm_Sum_1B_fridm"].LangText;
        DataObj.budgetChange = data2Frdim.Sum(x => x.budgetChange);

        forAmtPctData = data2Frdim.Sum(x => x.revisedBudAmtYear) != 0 ? (((data.Sum(x => x.revisedBudAmtYear) - data.Sum(x => x.forecastAmount)) / data.Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
        DataObj.deviationForecastPct = forAmtPctData;
        DataObj.deviationForecast = data2Frdim.Sum(x => x.deviationForecast);
        DataObj.forecastAmount = data.Sum(x => x.forecastAmount);
        DataObj.id = "-999";
        DataObj.LineGroup = null;
        DataObj.LineGroupId = "-1";
        DataObj.numberFormat = "n0";
        DataObj.semi = true;
        DataObj.clickable = false;
        DataObj.note = null;
        DataObj.rowType = "SumRowGrandTotal";
        DataObj.budgetYtd = data2Frdim.Sum(x => x.budgetYtd);
        DataObj.accountingYtd = data2Frdim.Sum(x => x.accountingYtd);
        DataObj.deviationYtd = data2Frdim.Sum(x => x.deviationYtd);
        DataObj.deviationYtdPct = 0;
        DataObj.IsSecondGrid = false;
        DataObj.deviationActionAmount = data2Frdim.Sum(x => x.deviationActionAmount);
        DataObj.forecastInclAction = data2Frdim.Sum(x => x.forecastInclAction);
        DataObj.deviationAfterAction = data2Frdim.Sum(x => x.deviationAfterAction);
        DataObjArray.Add(DataObj);

        //sum fridm dta1+ data
        DataObj = new JObject();
        DataObj.actualAmtLastYear = data2Frdim.Sum(x => x.actualAmtLastYear) + dataFridm.Sum(x => x.actualAmtLastYear);
        DataObj.orgBudAmtYear = data2Frdim.Sum(x => x.orgBudAmtYear) + dataFridm.Sum(x => x.orgBudAmtYear);
        DataObj.revisedBudAmtYear = data2Frdim.Sum(x => x.revisedBudAmtYear) + dataFridm.Sum(x => x.revisedBudAmtYear);
        DataObj.actualAmtYear = data2Frdim.Sum(x => x.actualAmtYear) + dataFridm.Sum(x => x.actualAmtYear);
        DataObj.orgBudAmtLastYear = data2Frdim.Sum(x => x.orgBudAmtLastYear) + dataFridm.Sum(x => x.orgBudAmtLastYear);
        DataObj.revisedAmtLastYear = data2Frdim.Sum(x => x.revisedAmtLastYear) + data.Sum(x => x.revisedAmtLastYear);
        DataObj.finPlanYear1 = data2Frdim.Sum(x => x.finPlanYear1) + dataFridm.Sum(x => x.finPlanYear1);
        DataObj.finPlanYear2 = data2Frdim.Sum(x => x.finPlanYear2) + dataFridm.Sum(x => x.finPlanYear2);
        DataObj.finPlanYear3 = data2Frdim.Sum(x => x.finPlanYear3) + dataFridm.Sum(x => x.finPlanYear3);
        DataObj.finPlanYear4 = data2Frdim.Sum(x => x.finPlanYear4) + dataFridm.Sum(x => x.finPlanYear4);
        DataObj.budgetFormDesc = langStringValues["BudForm_Sum_1B_with_fridm_Sum"].LangText;
        DataObj.budgetChange = data2Frdim.Sum(x => x.budgetChange) + dataFridm.Sum(x => x.budgetChange);
        var forAmtPctData3 = data2Frdim.Sum(x => x.revisedBudAmtYear) != 0 ? (((data2Frdim.Sum(x => x.revisedBudAmtYear) - data2Frdim.Sum(x => x.forecastAmount)) / data2Frdim.Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
        forAmtPctData = dataFridm.Sum(x => x.revisedBudAmtYear) != 0 ? (((dataFridm.Sum(x => x.revisedBudAmtYear) - dataFridm.Sum(x => x.forecastAmount)) / data.Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
        DataObj.deviationForecastPct = forAmtPctData + forAmtPctData3;
        DataObj.deviationForecast = data2Frdim.Sum(x => x.deviationForecast) + dataFridm.Sum(x => x.deviationForecast);
        DataObj.forecastAmount = data2Frdim.Sum(x => x.forecastAmount) + dataFridm.Sum(x => x.forecastAmount);
        DataObj.id = "-2";
        DataObj.LineGroup = null;
        DataObj.LineGroupId = "-1";
        DataObj.numberFormat = "n0";
        DataObj.semi = true;
        DataObj.clickable = false;
        DataObj.note = null;
        DataObj.rowType = "SumRowGrandTotal";
        DataObj.budgetYtd = data2Frdim.Sum(x => x.budgetYtd) + dataFridm.Sum(x => x.budgetYtd);
        DataObj.accountingYtd = data2Frdim.Sum(x => x.accountingYtd) + dataFridm.Sum(x => x.accountingYtd);
        DataObj.deviationYtd = data2Frdim.Sum(x => x.deviationYtd) + dataFridm.Sum(x => x.deviationYtd);
        DataObj.deviationYtdPct = 0;
        DataObj.IsSecondGrid = false;
        DataObj.deviationActionAmount = data2Frdim.Sum(x => x.deviationActionAmount) + dataFridm.Sum(x => x.deviationActionAmount);
        DataObj.forecastInclAction = data2Frdim.Sum(x => x.forecastInclAction) + dataFridm.Sum(x => x.forecastInclAction);
        DataObj.deviationAfterAction = data2Frdim.Sum(x => x.deviationAfterAction) + dataFridm.Sum(x => x.deviationAfterAction);
        DataObjArray.Add(DataObj);

        //empty Row
        DataObj = new JObject();
        DataObj.LineGroupId = null;
        DataObj.LineGroup = null;
        DataObj.id = null;
        DataObj.budgetFormDesc = "";
        DataObj.actualAmtYear = "";
        DataObj.revisedBudAmtYear = "";
        DataObj.orgBudAmtYear = "";
        DataObj.actualAmtLastYear = "";
        DataObj.orgBudAmtLastYear = "";
        DataObj.revisedAmtLastYear = "";
        DataObj.finPlanYear1 = "";
        DataObj.finPlanYear2 = "";
        DataObj.finPlanYear3 = "";
        DataObj.finPlanYear4 = "";
        DataObj.budgetChange = "";
        DataObj.deviationForecastPct = "";
        DataObj.deviationForecast = "";
        DataObj.forecastAmount = "";
        DataObj.budgetYtd = "";
        DataObj.accountingYtd = "";
        DataObj.deviationYtd = "";
        DataObj.deviationYtdPct = "";
        DataObj.semi = true;
        DataObj.clickable = false;
        DataObj.numberFormat = "Text";
        DataObj.note = null;
        DataObj.rowType = "DetailRow";
        DataObj.IsSecondGrid = false;
        DataObj.deviationActionAmount = "";
        DataObj.forecastInclAction = "";
        DataObj.deviationAfterAction = "";
        DataObjArray.Add(DataObj);


        //heading Row
        DataObj = new JObject();
        DataObj.LineGroupId = null;
        DataObj.LineGroup = null;
        DataObj.id = null;
        DataObj.budgetFormDesc = langStringValues["BudForm_Table_2_heading_1B"].LangText;
        DataObj.actualAmtYear = "";
        DataObj.revisedBudAmtYear = "";
        DataObj.orgBudAmtYear = "";
        DataObj.actualAmtLastYear = "";
        DataObj.orgBudAmtLastYear = "";
        DataObj.revisedAmtLastYear = "";
        DataObj.finPlanYear1 = "";
        DataObj.finPlanYear2 = "";
        DataObj.finPlanYear3 = "";
        DataObj.finPlanYear4 = "";
        DataObj.budgetChange = "";
        DataObj.deviationForecastPct = "";
        DataObj.deviationForecast = "";
        DataObj.forecastAmount = "";
        DataObj.semi = true;
        DataObj.clickable = false;
        DataObj.budgetYtd = "";
        DataObj.accountingYtd = "";
        DataObj.deviationYtd = "";
        DataObj.deviationYtdPct = "";
        DataObj.numberFormat = "Text";
        DataObj.note = null;
        DataObj.rowType = "DetailRow";
        DataObj.IsSecondGrid = false;
        DataObj.deviationActionAmount = "";
        DataObj.forecastInclAction = "";
        DataObj.deviationAfterAction = "";
        DataObjArray.Add(DataObj);

        //second data list
        foreach (var item in data2)
        {
            DataObj = FormateData(commentList, item);
            DataObjArray.Add(DataObj);
        }

        //sumarry for Data 2    ( data - data2)
        DataObj = new JObject();
        DataObj.actualAmtLastYear = data.Sum(x => x.actualAmtLastYear) - data2.Sum(x => x.actualAmtLastYear);
        DataObj.orgBudAmtYear = data.Sum(x => x.orgBudAmtYear) - data2.Sum(x => x.orgBudAmtYear);
        DataObj.revisedBudAmtYear = data.Sum(x => x.revisedBudAmtYear) - data2.Sum(x => x.revisedBudAmtYear);
        DataObj.actualAmtYear = data.Sum(x => x.actualAmtYear) - data2.Sum(x => x.actualAmtYear);
        DataObj.orgBudAmtLastYear = data.Sum(x => x.orgBudAmtLastYear) - data2.Sum(x => x.orgBudAmtLastYear);
        DataObj.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear) - data2.Sum(x => x.revisedAmtLastYear);
        DataObj.finPlanYear1 = data.Sum(x => x.finPlanYear1) - data2.Sum(x => x.finPlanYear1);
        DataObj.finPlanYear2 = data.Sum(x => x.finPlanYear2) - data2.Sum(x => x.finPlanYear2);
        DataObj.finPlanYear3 = data.Sum(x => x.finPlanYear3) - data2.Sum(x => x.finPlanYear3);
        DataObj.finPlanYear4 = data.Sum(x => x.finPlanYear4) - data2.Sum(x => x.finPlanYear4);
        DataObj.budgetChange = data.Sum(x => x.budgetChange) - data2.Sum(x => x.budgetChange);

        decimal forAmtPctData2 = data2.Sum(x => x.revisedBudAmtYear) != 0 ? (((data2.Sum(x => x.revisedBudAmtYear) - data2.Sum(x => x.forecastAmount)) / data2.Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
        forAmtPctData = data.Sum(x => x.revisedBudAmtYear) != 0 ? (((data.Sum(x => x.revisedBudAmtYear) - data.Sum(x => x.forecastAmount)) / data.Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
        DataObj.deviationForecastPct = forAmtPctData - forAmtPctData2;

        DataObj.deviationForecastPct = data.Sum(x => x.deviationForecastPct) - data2.Sum(x => x.deviationForecastPct);
        DataObj.deviationForecast = data.Sum(x => x.deviationForecast) - data2.Sum(x => x.deviationForecast);
        DataObj.forecastAmount = data.Sum(x => x.forecastAmount) - data2.Sum(x => x.forecastAmount);
        DataObj.budgetFormDesc = langStringValues["BudForm_Sum_1B_sum_2"].LangText;
        DataObj.id = "-1";
        DataObj.LineGroup = null;
        DataObj.LineGroupId = "-1";
        DataObj.numberFormat = "n0";
        DataObj.semi = true;
        DataObj.clickable = false;
        DataObj.note = null;
        DataObj.rowType = "SumRowGrandTotal";
        DataObj.budgetYtd = data.Sum(x => x.budgetYtd) - data2.Sum(x => x.budgetYtd);
        DataObj.accountingYtd = data.Sum(x => x.accountingYtd) - data2.Sum(x => x.accountingYtd);
        DataObj.deviationYtd = data.Sum(x => x.deviationYtd) - data2.Sum(x => x.deviationYtd);
        DataObj.deviationYtdPct = 0;
        DataObj.IsSecondGrid = false;
        DataObj.deviationActionAmount = data.Sum(x => x.deviationActionAmount) - data2.Sum(x => x.deviationActionAmount);
        DataObj.forecastInclAction = data.Sum(x => x.forecastInclAction) - data2.Sum(x => x.forecastInclAction);
        DataObj.deviationAfterAction = data.Sum(x => x.deviationAfterAction) - data2.Sum(x => x.deviationAfterAction);
        DataObjArray.Add(DataObj);

        finalData.data = DataObjArray;
        finalData.columns = await GetBudgetFormColumnAsync(budgetYear, userId, "1B", docType, forecastPeriod);
        finalData.header = header;
        return finalData;
    }



    private async Task<List<vw_yb_1B_consolidated>> GetYearlyBudForm1BDataFromDBConsolidationAsync(int budgetYear, string userId, int docType, int forecastPeriod)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        var dbContext = await _pUtility.GetTenantDBContextAsync();


        List<vw_yb_1B_consolidated> data = await (from a in dbContext.vw_yb_1B_consolidated
            where a.budget_year == budgetYear && a.fk_tenant_id == userDetails.tenant_id
            select a).ToListAsync();


        return data;
    }


    private static List<clsBudgetFormHelper> GetYearlyBudFormGroupedDataConsolidation(int budgetYear, UserData userDetails, TenantDBContext dbContext, List<vw_yb_1B_consolidated> budGet1BData)
    {
        return (from a in budGet1BData
            where a.sum_level.Contains("Foretak")
            group a by new { sum_level = a.sum_level, aggregate_id = a.aggregate_id.Trim(), aggregate_name = a.aggregate_name.Trim() } into grp
            select new clsBudgetFormHelper()
            {
                LineGroupId = grp.Key.aggregate_id,
                LineGroup = grp.Key.aggregate_name,
                id = grp.Key.aggregate_id,
                budgetFormDesc = grp.Key.aggregate_name,
                actualAmtYear = 0,// grp.Sum(x => x.actual_amt_year),
                revisedBudAmtYear = 0,// grp.sum(x => x.revised_bud_amt_year),
                orgBudAmtYear = 0,// grp.sum(x => x.org_bud_amt_year),
                actualAmtLastYear = grp.Sum(x => x.actual_amt_last_year),
                orgBudAmtLastYear = grp.Sum(x => x.org_bud_amt_last_year),
                revisedAmtLastYear = grp.Sum(x => x.revised_bud_amt_last_year),
                finPlanYear1 = 0,//grp.Sum(x => x.finplan_year_1_amount),
                finPlanYear2 = 0,//grp.Sum(x => x.finplan_year_2_amount),
                finPlanYear3 = 0,//grp.Sum(x => x.finplan_year_3_amount),
                finPlanYear4 = 0,//grp.Sum(x => x.finplan_year_4_amount),
                forecastAmount = 0,// grp.Sum(x => x.forecast_amount),
                deviationForecast = 0,// grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount),
                deviationForecastPct = 0,// grp.Sum(x => x.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount)) / grp.Sum(x => x.revised_bud_amt_year)) * 100 : 0,
                //     budgetChange = grp.Sum(x => x.budget_change.Value),
                budgetYtd = 0,// grp.Sum(x => x.budget_ytd),
                accountingYtd = 0,// grp.Sum(x => x.accounting_ytd),
                deviationYtd = 0,// grp.Sum(x => x.deviation_ytd),
                deviationYtdPct = 0,
                numberFormat = "n0",
                semi = false,
                clickable = true,
                rowType = "DetailRow",
                gridValue = budget_form_Grid_type.BudgetForm1BGrid1.ToString(),
                deviationActionAmount = 0,// grp.Sum(x => x.deviation_action_amount),
                forecastInclAction = 0,//grp.Sum(x => x.forecast_amount) + grp.Sum(x => x.deviation_action_amount),
                deviationAfterAction = 0// grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount) - grp.Sum(x => x.deviation_action_amount)
            }).OrderBy(x => x.id).ThenBy(x => x.budgetFormDesc).ToList();
    }


    private static List<clsBudgetFormHelper> GetYearlyBudFormGroupedDataConsolidation(List<vw_yb_1B_consolidated> budGet1BData)
    {
        return (from a in budGet1BData
            where !a.sum_level.Contains("Foretak")
            group a by new { sum_level = a.sum_level, aggregate_id = a.aggregate_id.Trim(), aggregate_name = a.aggregate_name.Trim() } into grp
            select new clsBudgetFormHelper()
            {
                SubHearderName = grp.Key.sum_level,
                SubHearderId = grp.Key.sum_level,
                LineGroupId = grp.Key.aggregate_id,
                LineGroup = grp.Key.aggregate_name,
                id = grp.Key.aggregate_id,
                budgetFormDesc = grp.Key.aggregate_name,
                actualAmtYear = 0,// grp.Sum(x => x.actual_amt_year),
                revisedBudAmtYear = 0,// grp.sum(x => x.revised_bud_amt_year),
                orgBudAmtYear = 0,// grp.sum(x => x.org_bud_amt_year),
                actualAmtLastYear = grp.Sum(x => x.actual_amt_last_year),
                orgBudAmtLastYear = grp.Sum(x => x.org_bud_amt_last_year),
                revisedAmtLastYear = grp.Sum(x => x.revised_bud_amt_last_year),
                finPlanYear1 = 0,//grp.Sum(x => x.finplan_year_1_amount),
                finPlanYear2 = 0,//grp.Sum(x => x.finplan_year_2_amount),
                finPlanYear3 = 0,//grp.Sum(x => x.finplan_year_3_amount),
                finPlanYear4 = 0,//grp.Sum(x => x.finplan_year_4_amount),
                forecastAmount = 0,// grp.Sum(x => x.forecast_amount),
                deviationForecast = 0,// grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount),
                deviationForecastPct = 0,// grp.Sum(x => x.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount)) / grp.Sum(x => x.revised_bud_amt_year)) * 100 : 0,
                //budgetChange = grp.Sum(x => x.budget_change.Value),
                budgetYtd = 0,// grp.Sum(x => x.budget_ytd),
                accountingYtd = 0,// grp.Sum(x => x.accounting_ytd),
                deviationYtd = 0,// grp.Sum(x => x.deviation_ytd),
                deviationYtdPct = 0,
                numberFormat = "n0",
                semi = false,
                clickable = true,
                rowType = "DetailRow",
                gridValue = budget_form_Grid_type.BudgetForm1BGrid1.ToString(),
                deviationActionAmount = 0,// grp.Sum(x => x.deviation_action_amount),
                forecastInclAction = 0,//grp.Sum(x => x.forecast_amount) + grp.Sum(x => x.deviation_action_amount),
                deviationAfterAction = 0// grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount) - grp.Sum(x => x.deviation_action_amount)
            }).OrderBy(x => x.id).ThenBy(x => x.budgetFormDesc).ToList();
    }



    public async Task<JObject> GetForm1BGrid2DataAsync(int budgetYear, string userId, string column, string id, int tableId, clsBudgetFormPopUpHelper searchHelper, int docType, int forecastPeriod = 0, List<string> budgetPhaseIDs = null, List<string> freedim2Ids = null,bool onlyData = false)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValues = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetForm");

        dynamic header = new JObject();
        header.title = langStringValues["BudForm_1BGrid2_popup_title"].LangText;
        header.descriptiontip = langStringValues["BudForm_1BGrid2_popup_desc"].LangText;
        dynamic finalData = new JObject();
        var data = new List<clsBudgetFormPopUpHelper>();
        var for58Data = await GetBudForm1BGrid2TableDataAsync(budgetYear, userId, docType, forecastPeriod, budgetPhaseIDs, freedim2Ids);
        var dbData = (from a1 in for58Data
            where id == a1.id.ToString().Trim()
            select a1).ToList();
        switch (column)
        {
            case "actualAmtYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        //   projectCode = a.fk_project_code,
                        ammountCol = a.actualAmtYear.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "revisedBudAmtYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        // projectCode = a.fk_project_code,
                        ammountCol = a.revisedBudAmtYear.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "orgBudAmtYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        //   projectCode = a.fk_project_code,
                        ammountCol = a.orgBudAmtYear.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "actualAmtLastYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.actualAmtLastYear.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "orgBudAmtLastYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.orgBudAmtLastYear.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "revisedAmtLastYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.revisedAmtLastYear.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "revisedAmtLastYearByAuth":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.revisedAmtLastYearByAuth.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "revisedAmtYearByAuth":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.revisedAmtYearByAuth.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear1":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.finPlanYear1.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear2":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.finPlanYear2.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear3":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.finPlanYear3.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear4":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.finPlanYear4.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
        }

        if (searchHelper != null)
        {
            data = filterBudgetFormData(searchHelper, data);
        }

        finalData.data = JArray.FromObject(data);
        if (!onlyData)
        {
            finalData.columns = await GetBudgetFormPopUpColumnAsync(budgetYear, userId, column, docType, forecastPeriod);
            finalData.header = header;
        }
        return finalData;
    }



    private async Task<List<clsBudgetForm1BPopUpHelper>> GetBudForm1BGrid2TableDataAsync(int budgetYear, string userId, int docType, int forecastPeriod = 0, List<string> budgetPhaseIDs = null, List<string> freedim2Ids = null)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        List<int> changeIds = budgetPhaseIDs != null && budgetPhaseIDs.Count > 0 ? await GetChangeIdsAsync(budgetYear, budgetPhaseIDs, userId) : new List<int>();
        var data = (forecastPeriod == 0) ? await (from a in dbContext.tbf_budget_form_1B
            join ac in dbContext.tco_accounts on new { a = a.fk_account_code.Trim(), b = a.fk_tenant_id }
                equals new { a = ac.pk_account_code.Trim(), b = ac.pk_tenant_id }
            join rl in dbContext.gmd_reporting_line on new { a = ac.fk_kostra_account_code.Trim() }
                equals new { a = rl.fk_kostra_account_code.Trim() }
            where a.year == budgetYear && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType &&
                  a.year >= ac.dateFrom.Year && a.year <= ac.dateTo.Year
                  && rl.report.Trim() == "54_DRIFTB"
            select new clsBudgetForm1BPopUpHelper()
            {
                id = rl.line_item_id.ToString(),
                budgetFormDesc = rl.line_item,
                actualAmtYear = a.actual_amt_year,
                revisedBudAmtYear = a.revised_bud_amt_year,
                orgBudAmtYear = a.org_bud_amt_year,
                actualAmtLastYear = a.actual_amt_last_year,
                orgBudAmtLastYear = a.org_bud_amt_last_year,
                revisedAmtLastYear = a.revised_bud_amt_last_year,
                revisedAmtYearByAuth = a.revised_bud_auth_year,
                revisedAmtLastYearByAuth = a.revised_bud_auth_last_year,
                finPlanYear1 = a.finplan_year_1_amount,
                finPlanYear2 = a.finplan_year_2_amount,
                finPlanYear3 = a.finplan_year_3_amount,
                finPlanYear4 = a.finplan_year_4_amount,
                forecastAmount = a.forecast_amount,
                deviationForecast = a.revised_bud_amt_year - a.forecast_amount,
                deviationForecastPct = a.revised_bud_amt_year != 0 ? ((a.revised_bud_amt_year - a.forecast_amount) / (a.revised_bud_amt_year)) * 100 : 0,
                budgetChange = a.budget_change.Value,
                budgetYtd = a.budget_ytd,
                accountingYtd = a.accounting_ytd,
                deviationYtd = a.deviation_ytd,
                deviationYtdPct = 0,
                accountCode = a.fk_account_code,
                deptCode = a.fk_department_code,
                functionCode = a.fk_function_code,
                freeDim = a.free_dim_code,
                changeId = a.fk_change_id
            }).OrderBy(x => x.id).ThenBy(x => x.budgetFormDesc).ToListAsync() : await (from a in dbContext.tbf_budget_form_1B
            join ac in dbContext.tco_accounts on new { a = a.fk_account_code.Trim(), b = a.fk_tenant_id }
                equals new { a = ac.pk_account_code.Trim(), b = ac.pk_tenant_id }
            join rl in dbContext.gmd_reporting_line on new { a = ac.fk_kostra_account_code.Trim() }
                equals new { a = rl.fk_kostra_account_code.Trim() }
            where a.forecast_period == forecastPeriod && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType &&
                  a.year >= ac.dateFrom.Year && a.year <= ac.dateTo.Year
                  && rl.report.Trim() == "54_DRIFTB"
            select new clsBudgetForm1BPopUpHelper()
            {
                id = rl.line_item_id.ToString(),
                budgetFormDesc = rl.line_item,
                actualAmtYear = a.actual_amt_year,
                revisedBudAmtYear = a.revised_bud_amt_year,
                orgBudAmtYear = a.org_bud_amt_year,
                actualAmtLastYear = a.actual_amt_last_year,
                orgBudAmtLastYear = a.org_bud_amt_last_year,
                revisedAmtLastYear = a.revised_bud_amt_last_year,
                revisedAmtYearByAuth = a.revised_bud_auth_year,
                revisedAmtLastYearByAuth = a.revised_bud_auth_last_year,
                finPlanYear1 = a.finplan_year_1_amount,
                finPlanYear2 = a.finplan_year_2_amount,
                finPlanYear3 = a.finplan_year_3_amount,
                finPlanYear4 = a.finplan_year_4_amount,
                forecastAmount = a.forecast_amount,
                deviationForecast = a.revised_bud_amt_year - a.forecast_amount,
                deviationForecastPct = a.revised_bud_amt_year != 0 ? ((a.revised_bud_amt_year - a.forecast_amount) / (a.revised_bud_amt_year)) * 100 : 0,
                budgetChange = a.budget_change.Value,
                budgetYtd = a.budget_ytd,
                accountingYtd = a.accounting_ytd,
                deviationYtd = a.deviation_ytd,
                deviationYtdPct = 0,
                accountCode = a.fk_account_code,
                deptCode = a.fk_department_code,
                functionCode = a.fk_function_code,
                freeDim = a.free_dim_code,
                changeId = a.fk_change_id
            }).OrderBy(x => x.id).ThenBy(x => x.budgetFormDesc).ToListAsync();

        if (changeIds.Count > 0)
        {
            data = data.Where(x => changeIds.Contains(x.changeId)).ToList();
        }
        if (freedim2Ids != null && freedim2Ids.Count > 0)
        {
            var emptyFreedimSelected = freedim2Ids.Contains("-1");
            data = emptyFreedimSelected ? data.Where(x => freedim2Ids.Contains(x.freeDim) || string.IsNullOrEmpty(x.freeDim)).ToList() : data.Where(x => freedim2Ids.Contains(x.freeDim)).ToList();
        }

        return data;
    }


    public JObject BudgetForm1BPopupData(string userId, int budgetYear, string column, string id, int docType, clsBudgetFormPopUpHelper searchHelper, int foreCastPeriod = 0, string parentId = null)
    {
        return BudgetForm1BPopupDataAsync(userId, budgetYear, column, id, docType, searchHelper, foreCastPeriod, parentId).GetAwaiter().GetResult();
    }



    public async Task<JObject> BudgetForm1BPopupDataAsync(string userId, int budgetYear, string column, string id, int docType, clsBudgetFormPopUpHelper searchHelper, int foreCastPeriod = 0, string parentId = null,bool onlyData = false)
    {
        var result = new JObject();
        if (docType == 1 && budgetYear < 2020)
        {
            result = await _budgetForm.Get1BPopUpDataAsync(budgetYear, userId, column, id, searchHelper, docType, foreCastPeriod, onlyData: onlyData);
        }
        else
        {
            result = await Get1BPopUpDataAsync(budgetYear, userId, column, id, searchHelper, docType, foreCastPeriod, searchHelper.budgetPhaseIds, searchHelper.freeDim2Ids, parentId, onlyData: onlyData);
        }
        return result;
    }


    public JObject Framsikt1BPopupData(string userId, BudgetFormPopUpHelper input, bool onlyData = false)
    {
        return Framsikt1BPopupDataAsync(userId, input, onlyData).GetAwaiter().GetResult();
    }


    public async Task<JObject> Framsikt1BPopupDataAsync(string userId, BudgetFormPopUpHelper input, bool onlyData = false)
    {
        var result = new JObject();
        switch (input.docType)
        {
            case 1:
                if (input.IsSecondGrid)
                {
                    result = await GetFramsikt1BPopUpDataGrid2Async(input.budgetYear, userId, input.column, input.id, input.searchHelper, input.docType, input.foreCastPeriod, onlyData: onlyData);
                }
                else
                {
                    result = await GetFramsikt1BPopUpDataAsync(input.budgetYear, userId, input.column, input.id, input.searchHelper, input.docType, input.foreCastPeriod, parentId: input.parentId, onlyData: onlyData);
                }
                break;
            default:
                if (input.IsSecondGrid) 
                {
                    result = await GetFramsikt1BPopUpDataGrid2Async(input.budgetYear, userId, input.column, input.id, input.searchHelper, input.docType, input.foreCastPeriod, budgetPhaseIDs: input.budgetPhaseIds, freedim2Ids: input.freeDim2Ids, onlyData : onlyData); 
                }
                else 
                {
                    result = await GetFramsikt1BPopUpDataAsync(input.budgetYear, userId, input.column, input.id, input.searchHelper, input.docType, input.foreCastPeriod, budgetPhaseIDs: input.budgetPhaseIds, freedim2Ids: input.freeDim2Ids, input.parentId, onlyData: onlyData); 
                }
                break;
        }
        return result;
    }



    public async Task<JObject> GetFramsikt1BPopUpDataAsync(int budgetYear, string userId, string column, string id, clsBudgetFormPopUpHelper searchHelper, int docType, int forecastPeriod = 0, List<string> budgetPhaseIDs = null, List<string> freedim2Ids = null, string parentId = null, bool onlyData = false)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValues = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetForm");

        dynamic header = new JObject();
        header.title = langStringValues["BudForm_Fram_1B_popUp_title"].LangText;
        header.descriptiontip = langStringValues["BudForm_Fram_1B_popUp_desc"].LangText;
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        JArray dataArray = new JArray();
        dynamic finalData = new JObject();
        List<int> LineGrpToConsider = new List<int>() { 30, 40, 50 };
        var data = new List<clsBudgetFormPopUpHelper>();
        var Framsikt1BData = await GetFramsikt1BDataFromDBAsync(budgetYear, userId, docType, forecastPeriod, budgetPhaseIDs: budgetPhaseIDs, freedim2Ids: freedim2Ids);
        List<tco_accounts> accountData = await GetAccountAsync(userDetails.tenant_id);
        List<gmd_reporting_line> reportingLine = await GetGmdReportingLineAsync();
        var dbData =  string.IsNullOrEmpty(parentId) ? (from a in Framsikt1BData
                where a.aggregate_id.ToString() == id
                select new clsBudgetForm1BPopUpHelper()
                {
                    actualAmtYear = a.actual_amt_year,
                    revisedBudAmtYear = a.revised_bud_amt_year,
                    orgBudAmtYear = a.org_bud_amt_year,
                    actualAmtLastYear = a.actual_amt_last_year,
                    orgBudAmtLastYear = a.org_bud_amt_last_year,
                    revisedAmtLastYear = a.revised_bud_amt_last_year,
                    revisedAmtYearByAuth = a.revised_bud_auth_year,
                    revisedAmtLastYearByAuth = a.revised_bud_auth_last_year,
                    finPlanYear1 = a.finplan_year_1_amount,
                    finPlanYear2 = a.finplan_year_2_amount,
                    finPlanYear3 = a.finplan_year_3_amount,
                    finPlanYear4 = a.finplan_year_4_amount,
                    forecastAmount = a.forecast_amount,
                    deviationForecast = a.revised_bud_amt_year - a.forecast_amount,
                    deviationForecastPct = a.revised_bud_amt_year != 0 ? (((a.revised_bud_amt_year) - a.forecast_amount) / a.revised_bud_amt_year) * 100 : 0,
                    budgetChange = a.budget_change.Value,
                    budgetYtd = a.budget_ytd,
                    accountingYtd = a.accounting_ytd,
                    deviationYtd = a.deviation_ytd,
                    deviationYtdPct = 0,
                    accountCode = a.fk_account_code,
                    functionCode = a.fk_function_code,
                    deptCode = a.fk_department_code,
                }).ToList()
            :  (from a in Framsikt1BData
                join ac in accountData on new { a = a.fk_account_code, b = a.fk_tenant_id }
                    equals new { a = ac.pk_account_code, b = ac.pk_tenant_id }
                join rl in reportingLine on new { a = ac.fk_kostra_account_code }
                    equals new { a = rl.fk_kostra_account_code }
                where a.year == budgetYear && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType &&
                      a.year >= ac.dateFrom.Year && a.year <= ac.dateTo.Year && (LineGrpToConsider.Contains(rl.line_group_id) || (rl.line_group_id == 20 && rl.line_item_id == 2050))
                      && a.aggregate_id == parentId && id == rl.line_item_id.ToString() 
                      && rl.report.Trim() == "54_OVDRIFT"
                select new clsBudgetForm1BPopUpHelper()
                {
                    id = rl.line_item_id.ToString(),
                    budgetFormDesc = rl.line_item,
                    actualAmtYear = a.actual_amt_year,
                    revisedBudAmtYear = a.revised_bud_amt_year,
                    orgBudAmtYear = a.org_bud_amt_year,
                    actualAmtLastYear = a.actual_amt_last_year,
                    orgBudAmtLastYear = a.org_bud_amt_last_year,
                    revisedAmtLastYear = a.revised_bud_amt_last_year,
                    revisedAmtYearByAuth = a.revised_bud_auth_year,
                    revisedAmtLastYearByAuth = a.revised_bud_auth_last_year,
                    finPlanYear1 = a.finplan_year_1_amount,
                    finPlanYear2 = a.finplan_year_2_amount,
                    finPlanYear3 = a.finplan_year_3_amount,
                    finPlanYear4 = a.finplan_year_4_amount,
                    forecastAmount = a.forecast_amount,
                    deviationForecast = a.revised_bud_amt_year - a.forecast_amount,
                    deviationForecastPct = a.revised_bud_amt_year != 0 ? (((a.revised_bud_amt_year) - a.forecast_amount) / a.revised_bud_amt_year) * 100 : 0,
                    budgetChange = a.budget_change.Value,
                    budgetYtd = a.budget_ytd,
                    accountingYtd = a.accounting_ytd,
                    deviationYtd = a.deviation_ytd,
                    deviationYtdPct = 0,
                    accountCode = a.fk_account_code,
                    functionCode = a.fk_function_code,
                    deptCode = a.fk_department_code,
                }).OrderBy(x => x.id).ThenBy(x => x.budgetFormDesc).ToList();

        switch (column)
        {
            case "actualAmtYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.actualAmtYear.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "revisedBudAmtYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.revisedBudAmtYear.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "orgBudAmtYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.orgBudAmtYear.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "actualAmtLastYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.actualAmtLastYear.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "orgBudAmtLastYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.orgBudAmtLastYear.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "revisedAmtLastYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.revisedAmtLastYear.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "revisedAmtLastYearByAuth":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.revisedAmtLastYearByAuth.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "revisedAmtYearByAuth":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.revisedAmtYearByAuth.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear1":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.finPlanYear1.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear2":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.finPlanYear2.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear3":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.finPlanYear3.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear4":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.finPlanYear4.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
        }

        if (searchHelper != null)
        {
            data = filterBudgetFormData(searchHelper, data);
        }

        finalData.data = JArray.FromObject(data);
        if (!onlyData)
        {
            finalData.columns = await GetBudgetFormPopUpColumnAsync(budgetYear, userId, column, docType);
            finalData.header = header;
        }
        return finalData;
    }



    public async Task<JObject> GetFramsikt1BPopUpDataGrid2Async(int budgetYear, string userId, string column, string id, clsBudgetFormPopUpHelper searchHelper, int docType, int forecastPeriod = 0, List<string> budgetPhaseIDs = null, List<string> freedim2Ids = null, bool onlyData = false)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValues = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetForm");

        dynamic header = new JObject();
        header.title = langStringValues["BudForm_Fram_1B_popUp_title"].LangText;
        header.descriptiontip = langStringValues["BudForm_Fram_1B_popUp_desc"].LangText;
        var dbContext = await _pUtility.GetTenantDBContextAsync();

        dynamic finalData = new JObject();
        var data = new List<clsBudgetFormPopUpHelper>();
        List<int> LineGrpToConsider = new List<int>() { 30, 40, 50 };
        var framsikt1Data = await GetFramsikt1BDataFromDBAsync(budgetYear, userId, docType, forecastPeriod, budgetPhaseIDs: budgetPhaseIDs, freedim2Ids: freedim2Ids);
        List<tco_accounts> accountData = await GetAccountAsync(userDetails.tenant_id);
        List<gmd_reporting_line> reportingLine = await GetGmdReportingLineAsync();
        var dbData = (from a in framsikt1Data
            join ac in accountData on new { a = a.fk_account_code, b = a.fk_tenant_id }
                equals new { a = ac.pk_account_code, b = ac.pk_tenant_id }
            join rl in reportingLine on new { a = ac.fk_kostra_account_code }
                equals new { a = rl.fk_kostra_account_code }
            where a.year == budgetYear && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType &&
                  a.year >= ac.dateFrom.Year && a.year <= ac.dateTo.Year && (LineGrpToConsider.Contains(rl.line_group_id) || (rl.line_group_id == 20 && rl.line_item_id == 2050))
                  && rl.line_item_id.ToString() == id
                  && rl.report.Trim() == "54_OVDRIFT"
            select new clsBudgetForm1BPopUpHelper()
            {
                id = rl.line_item_id.ToString(),
                budgetFormDesc = rl.line_item,
                actualAmtYear = a.actual_amt_year,
                revisedBudAmtYear = a.revised_bud_amt_year,
                orgBudAmtYear = a.org_bud_amt_year,
                actualAmtLastYear = a.actual_amt_last_year,
                orgBudAmtLastYear = a.org_bud_amt_last_year,
                revisedAmtLastYear = a.revised_bud_amt_last_year,
                revisedAmtYearByAuth = a.revised_bud_auth_year,
                revisedAmtLastYearByAuth = a.revised_bud_auth_last_year,
                finPlanYear1 = a.finplan_year_1_amount,
                finPlanYear2 = a.finplan_year_2_amount,
                finPlanYear3 = a.finplan_year_3_amount,
                finPlanYear4 = a.finplan_year_4_amount,
                forecastAmount = a.forecast_amount,
                deviationForecast = a.revised_bud_amt_year - a.forecast_amount,
                deviationForecastPct = a.revised_bud_amt_year != 0 ? (((a.revised_bud_amt_year) - a.forecast_amount) / a.revised_bud_amt_year) * 100 : 0,
                budgetChange = a.budget_change.Value,
                budgetYtd = a.budget_ytd,
                accountingYtd = a.accounting_ytd,
                deviationYtd = a.deviation_ytd,
                deviationYtdPct = 0,
                accountCode = a.fk_account_code,
                functionCode = a.fk_function_code,
                deptCode = a.fk_department_code,
            }).OrderBy(x => x.id).ThenBy(x => x.budgetFormDesc).ToList();
        switch (column)
        {
            case "actualAmtYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.actualAmtYear.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "revisedBudAmtYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.revisedBudAmtYear.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "orgBudAmtYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.orgBudAmtYear.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "actualAmtLastYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.actualAmtLastYear.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "orgBudAmtLastYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.orgBudAmtLastYear.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "revisedAmtLastYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.revisedAmtLastYear.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "revisedAmtLastYearByAuth":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.revisedAmtLastYearByAuth.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "revisedAmtYearByAuth":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.revisedAmtYearByAuth.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear1":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.finPlanYear1.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear2":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.finPlanYear2.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear3":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.finPlanYear3.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear4":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.accountCode,
                        deptCode = a.deptCode,
                        functionCode = a.functionCode,
                        ammountCol = a.finPlanYear4.Value,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
        }

        if (searchHelper != null)
        {
            data = filterBudgetFormData(searchHelper, data);
        }

        finalData.data = JArray.FromObject(data);
        if (!onlyData)
        {
            finalData.columns = await GetBudgetFormPopUpColumnAsync(budgetYear, userId, column, docType);
            finalData.header = header;
        }
        return finalData;
    }

    private async Task<(List<BudgetFormHelperNew>, List<BudgetFormHelperNew>, IEnumerable<BudgetForm1BGroup>)> Get1BDataNewAsync
    (int budgetYear,  int docType, int forecastPeriod, string budgetPhaseId, List<int> changeIds,
        string fridimData, List<string> budgetPhaseIDs, List<string> freedim2Ids,
        UserData userDetails, bool excludeAmortization, Func<BudgetForm1BGroup, bool> valueFilterPredicate = null, Func<BudgetForm1BGroup, bool> filterPredicate = null)
    {
        List<tbf_budget_form_1B> budGet1BData = await Get1BDataFromDBAsync(budgetYear, userDetails.user_name, docType, forecastPeriod, fridimData, budgetPhaseIDs, freedim2Ids);
        List<tco_accounts> accountData = await GetAccountAsync(userDetails.tenant_id);
        List<gmd_reporting_line> reportingLine = await GetGmdReportingLineAsync();

        budGet1BData = Filter1BByChangeId(budGet1BData, changeIds);

        var data = budGet1BData
            .GroupBy(grp => new { aggregate_id = grp.aggregate_id.Trim(), aggregate_name = grp.aggregate_name.Trim() })
            .Select(grp => new BudgetFormHelperNew()
            {
                LineGroupId = grp.Key.aggregate_id,
                LineGroup = grp.Key.aggregate_name,
                id = grp.Key.aggregate_id,
                budgetFormDesc = grp.Key.aggregate_name,
                actualAmtYear = grp.Sum(x => x.actual_amt_year),
                revisedBudAmtYear = grp.Sum(x => x.revised_bud_amt_year),
                orgBudAmtYear = grp.Sum(x => x.org_bud_amt_year),
                actualAmtLastYear = grp.Sum(x => x.actual_amt_last_year),
                orgBudAmtLastYear = grp.Sum(x => x.org_bud_amt_last_year),
                revisedAmtLastYear = grp.Sum(x => x.revised_bud_amt_last_year),
                revisedAmtYearByAuth = grp.Sum(x => x.revised_bud_auth_year),
                revisedAmtLastYearByAuth = grp.Sum(x => x.revised_bud_auth_last_year),
                finPlanYear1 = grp.Sum(x => x.finplan_year_1_amount),
                finPlanYear2 = grp.Sum(x => x.finplan_year_2_amount),
                finPlanYear3 = grp.Sum(x => x.finplan_year_3_amount),
                finPlanYear4 = grp.Sum(x => x.finplan_year_4_amount),
                forecastAmount = grp.Sum(x => x.forecast_amount),
                deviationForecast = grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount),
                deviationForecastPct = grp.Sum(x => x.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount)) / grp.Sum(x => x.revised_bud_amt_year)) * 100 : 0,
                budgetChange = grp.Sum(x => x.budget_change.Value),
                budgetYtd = grp.Sum(x => x.budget_ytd),
                accountingYtd = grp.Sum(x => x.accounting_ytd),
                deviationYtd = grp.Sum(x => x.deviation_ytd),
                deviationYtdPct = 0,
                numberFormat = "n0",
                semi = false,
                clickable = true,
                isSubHeading = false,
                rowType = "ParentRow",
                gridValue = budget_form_Grid_type.BudgetForm1BGrid1.ToString(),
                deviationActionAmount = grp.Sum(x => x.deviation_action_amount),
                forecastInclAction = grp.Sum(x => x.forecast_amount) + grp.Sum(x => x.deviation_action_amount),
                deviationAfterAction = grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount) - grp.Sum(x => x.deviation_action_amount),
                polDeviation = grp.Sum(x => x.revised_bud_auth_year) - grp.Sum(x => x.actual_amt_year),
                polDeviationPct = grp.Sum(x => x.revised_bud_auth_year) != 0 ? ((grp.Sum(x => x.revised_bud_auth_year) - grp.Sum(x => x.actual_amt_year)) / grp.Sum(x => x.revised_bud_auth_year)) * 100 : 0,
                deviation = grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.actual_amt_year),
                deviationPct = grp.Sum(x => x.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.actual_amt_year)) / grp.Sum(x => x.revised_bud_amt_year))* 100 : 0,
            })
            .OrderBy(x => x.id).ThenBy(x => x.budgetFormDesc)
            .ToList();

        var budGet1BDataquery = budGet1BData
                .Join(accountData,
                    a => new { AccountCode = a.fk_account_code, TenantId = a.fk_tenant_id },
                    ac => new { AccountCode = ac.pk_account_code, TenantId = ac.pk_tenant_id },
                    (a, ac) => new { a, ac })
                .Join(reportingLine,
                    joined => new { KostraCode = joined.ac.fk_kostra_account_code },
                    rl => new { KostraCode = rl.fk_kostra_account_code },
                    (joined, rl) => new BudgetForm1BGroup { a = joined.a, ac = joined.ac, rl = rl })
                .Where(joinedRl => joinedRl.a.year == budgetYear &&
                                   joinedRl.a.fk_tenant_id == userDetails.tenant_id &&
                                   joinedRl.a.year >= joinedRl.ac.dateFrom.Year &&
                                   joinedRl.a.year <= joinedRl.ac.dateTo.Year &&
                                   joinedRl.a.central_acc_flag == 1 &&
                                   joinedRl.rl.report == "54_DRIFTB")
            ;

        if (filterPredicate != null)
        {
            budGet1BDataquery = budGet1BDataquery.Where(filterPredicate);
        }

        var data2 = budGet1BDataquery
            .OrderBy(joined => joined.rl.line_item_id)
            .ThenBy(joined => joined.rl.line_item)
            .GroupBy(grp => new
            {
                LineItemId = grp.rl.line_item_id.ToString().Trim(),
                LineItem = grp.rl.line_item.Trim()
            }).Select(grp =>
            {
                var row = createBudgetFormHelperNew(grp, valueFilterPredicate);
                row.gridValue = budget_form_Grid_type.BudgetForm1BGrid2.ToString();
                row.LineGroupId = grp.Key.LineItemId.ToString();
                row.LineGroup = grp.Key.LineItem;
                row.id = grp.Key.LineItemId.ToString();
                row.budgetFormDesc = grp.Key.LineItem;
                row.rowType = "ParentRow";
                return row;
            }).ToList();

        return (data, data2, budGet1BDataquery);
    }
}