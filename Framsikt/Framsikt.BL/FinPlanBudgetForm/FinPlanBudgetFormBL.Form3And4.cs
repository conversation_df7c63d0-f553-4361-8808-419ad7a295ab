using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;

namespace Framsikt.BL;

public partial class FinPlanBudgetFormBL
{
    public JObject GetForm3Data(int budgetYear, string userId, int docType, int forecastPeriod = 0, string budgetPhaseId = null, bool showOnlyModified = false, string fridimData = "",
        List<string> budgetPhaseIDs = null, List<string> freedim2Ids = null, List<string>? departmentCode = null, bool onlyData = false)
    {
        return GetForm3DataAsync(budgetYear, userId, docType, forecastPeriod, budgetPhaseId, showOnlyModified, fridimData, budgetPhaseIDs, freedim2Ids, departmentCode, onlyData).GetAwaiter().GetResult();
    }

    public async Task<JObject> GetForm3DataAsync(int budgetYear, string userId, int docType, int forecastPeriod = 0, string budgetPhaseId = null, bool showOnlyModified = false, string fridimData = "",
        List<string> budgetPhaseIDs = null, List<string> freedim2Ids = null, List<string>? departmentCode = null, bool onlyData = false)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValues = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetForm");

        dynamic header = new JObject();
        header.title = langStringValues["BudForm_B3_title"].LangText;
        header.descriptiontip = langStringValues["BudForm_B3_desc"].LangText;
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        var commentList = await GetCommentListAsync(budgetYear, userDetails, dbContext, clsConstants.budget_form_type.BudgetFormB3.ToString(), docType, forecastPeriod);
        var dontshowDetail = await dbContext.vw_tco_parameters.FirstOrDefaultAsync(x => x.param_name == "BudForm_Dont_show_detail" && x.active == 1 && x.fk_tenant_id == userDetails.tenant_id);
        JArray dataArray = new JArray();
        dynamic finalData = new JObject();
        List<int> changeIds = new List<int>();

        //#region Fetch changeIds

        if (!string.IsNullOrEmpty(budgetPhaseId) && budgetPhaseId != "-1")
        {
            changeIds = await _pInvestments.GetChangeDataUsingBudgetPhaseAsync(budgetPhaseId, userId, showOnlyModified, budgetYear);
        }

        //#endregion Fetch changeIds

        List<clsBudgetFormHelper> data;
        if (changeIds.Any())
        {
            data = (from a in (await GetForm3DataFromDBAsync(budgetYear, userId, docType, forecastPeriod, fridimData, budgetPhaseIDs: budgetPhaseIDs, freedim2Ids: freedim2Ids))
                where changeIds.Contains(a.fk_change_id)
                group a by new { line_group_id = a.line_group_id.ToString().Trim(), line_group = a.line_group.ToString().Trim(), line_item_id = a.line_item_id.ToString().Trim(), line_item = a.line_item.ToString().Trim() } into grp
                select new clsBudgetFormHelper()
                {
                    LineGroupId = grp.Key.line_group_id.ToString(),
                    LineGroup = grp.Key.line_group,
                    id = grp.Key.line_item_id.ToString(),
                    budgetFormDesc = grp.Key.line_item,
                    actualAmtYear = grp.Sum(x => x.actual_amt_year),
                    revisedBudAmtYear = grp.Sum(x => x.revised_bud_amt_year),
                    orgBudAmtYear = grp.Sum(x => x.org_bud_amt_year),
                    actualAmtLastYear = grp.Sum(x => x.actual_amt_last_year),
                    orgBudAmtLastYear = grp.Sum(x => x.org_bud_amt_last_year),
                    revisedAmtLastYear = grp.Sum(x => x.revised_bud_amt_last_year),
                    revisedAmtYearByAuth = grp.Sum(x => x.revised_bud_auth_year),
                    revisedAmtLastYearByAuth = grp.Sum(x => x.revised_bud_auth_last_year),
                    finPlanYear1 = grp.Sum(x => x.finplan_year_1_amount),
                    finPlanYear2 = grp.Sum(x => x.finplan_year_2_amount),
                    finPlanYear3 = grp.Sum(x => x.finplan_year_3_amount),
                    finPlanYear4 = grp.Sum(x => x.finplan_year_4_amount),
                    forecastAmount = grp.Sum(x => x.forecast_amount),
                    deviationForecast = grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount),
                    deviationForecastPct = grp.Sum(x => x.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount)) / grp.Sum(x => x.revised_bud_amt_year)) * 100 : 0,
                    budgetChange = grp.Sum(x => x.budget_change.Value),
                    deviation = grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.actual_amt_year),
                    deviationPct = grp.Sum(x => x.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.actual_amt_year)) / grp.Sum(x => x.revised_bud_amt_year)) * 100 : 0,
                    numberFormat = "n0",
                    budgetYtd = grp.Sum(x => x.budget_ytd),
                    accountingYtd = grp.Sum(x => x.accounting_ytd),
                    deviationYtd = grp.Sum(x => x.deviation_ytd),
                    polDeviation = grp.Sum(x => x.revised_bud_auth_year) - grp.Sum(x => x.actual_amt_year),
                    polDeviationPct = grp.Sum(x => x.revised_bud_auth_year) != 0 ? ((grp.Sum(x => x.revised_bud_auth_year) - grp.Sum(x => x.actual_amt_year)) / grp.Sum(x => x.revised_bud_auth_year)) * 100 : 0,
                    deviationYtdPct = 0,
                    isSubHeading = false,
                    unAprvBudChange = grp.Sum(x => x.unaprv_bud_change),
                    budgetChanges = grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.org_bud_amt_last_year),
                    revIncUnAprvBudChange = grp.Sum(x => x.revised_bud_amt_year) + grp.Sum(x => x.unaprv_bud_change),
                    deviationActionAmount = grp.Sum(x => x.forecast_amount) + grp.Sum(x => x.deviation_action_amount),
                    devRevIncUnAprvBudChangeAndFc = (grp.Sum(x => x.revised_bud_amt_year) + grp.Sum(x => x.unaprv_bud_change)) - (grp.Sum(x => x.forecast_amount) + grp.Sum(x => x.deviation_action_amount)),
                    devBudgetYtdAcc = grp.Sum(x => x.budget_ytd) - grp.Sum(x => x.accounting_ytd)
                }).OrderBy(x => x.LineGroupId).ThenBy(x => x.LineGroup).ThenBy(x => x.id).ThenBy(x => x.budgetFormDesc).ToList();
        }
        else
        {
            data = (from a in (await GetForm3DataFromDBAsync(budgetYear, userId, docType, forecastPeriod, fridimData, budgetPhaseIDs: budgetPhaseIDs, freedim2Ids: freedim2Ids, departmentCode: departmentCode))
                group a by new { line_group_id = a.line_group_id.ToString().Trim(), line_group = a.line_group.ToString().Trim(), line_item_id = a.line_item_id.ToString().Trim(), line_item = a.line_item.ToString().Trim() } into grp
                select new clsBudgetFormHelper()
                {
                    LineGroupId = grp.Key.line_group_id.ToString(),
                    LineGroup = grp.Key.line_group,
                    id = grp.Key.line_item_id.ToString(),
                    budgetFormDesc = grp.Key.line_item,
                    actualAmtYear = grp.Sum(x => x.actual_amt_year),
                    revisedBudAmtYear = grp.Sum(x => x.revised_bud_amt_year),
                    orgBudAmtYear = grp.Sum(x => x.org_bud_amt_year),
                    actualAmtLastYear = grp.Sum(x => x.actual_amt_last_year),
                    orgBudAmtLastYear = grp.Sum(x => x.org_bud_amt_last_year),
                    revisedAmtLastYear = grp.Sum(x => x.revised_bud_amt_last_year),
                    revisedAmtYearByAuth = grp.Sum(x => x.revised_bud_auth_year),
                    revisedAmtLastYearByAuth = grp.Sum(x => x.revised_bud_auth_last_year),
                    finPlanYear1 = grp.Sum(x => x.finplan_year_1_amount),
                    finPlanYear2 = grp.Sum(x => x.finplan_year_2_amount),
                    finPlanYear3 = grp.Sum(x => x.finplan_year_3_amount),
                    finPlanYear4 = grp.Sum(x => x.finplan_year_4_amount),
                    forecastAmount = grp.Sum(x => x.forecast_amount),
                    deviationForecast = grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount),
                    deviationForecastPct = grp.Sum(x => x.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount)) / grp.Sum(x => x.revised_bud_amt_year)) * 100 : 0,
                    budgetChange = grp.Sum(x => x.budget_change.Value),
                    deviation = grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.actual_amt_year),
                    deviationPct = grp.Sum(x => x.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.actual_amt_year)) / grp.Sum(x => x.revised_bud_amt_year)) * 100 : 0,
                    numberFormat = "n0",
                    budgetYtd = grp.Sum(x => x.budget_ytd),
                    accountingYtd = grp.Sum(x => x.accounting_ytd),
                    deviationYtd = grp.Sum(x => x.deviation_ytd),
                    deviationYtdPct = 0,
                    polDeviation = grp.Sum(x => x.revised_bud_auth_year) - grp.Sum(x => x.actual_amt_year),
                    polDeviationPct = grp.Sum(x => x.revised_bud_auth_year) != 0 ? ((grp.Sum(x => x.revised_bud_auth_year) - grp.Sum(x => x.actual_amt_year)) / grp.Sum(x => x.revised_bud_auth_year)) * 100 : 0,
                    isSubHeading = false,
                    unAprvBudChange = grp.Sum(x => x.unaprv_bud_change),
                    budgetChanges = grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.org_bud_amt_last_year),
                    revIncUnAprvBudChange = grp.Sum(x => x.revised_bud_amt_year) + grp.Sum(x => x.unaprv_bud_change),
                    deviationActionAmount = grp.Sum(x => x.forecast_amount) + grp.Sum(x => x.deviation_action_amount),
                    devRevIncUnAprvBudChangeAndFc = (grp.Sum(x => x.revised_bud_amt_year) + grp.Sum(x => x.unaprv_bud_change)) - (grp.Sum(x => x.forecast_amount) + grp.Sum(x => x.deviation_action_amount)),
                    devBudgetYtdAcc = grp.Sum(x => x.budget_ytd) - grp.Sum(x => x.accounting_ytd)
                }).OrderBy(x => x.LineGroupId).ThenBy(x => x.LineGroup).ThenBy(x => x.id).ThenBy(x => x.budgetFormDesc).ToList();
        }

        //not remove all zero rows//154242
        var LineGroupIdList = new List<string>();
        decimal forAmtPctData = 0;
        decimal polDeviationPercent = 0;
        LineGroupIdList.AddRange(data.Select(x => x.LineGroupId).Distinct().ToList());
        dynamic grpData;
        int index = 0;
        int count = 0;
        foreach (var item in LineGroupIdList)
        {
            count++;
            // remove sub header #45351

            if (item == "50")
            {
                grpData = new JObject();
                grpData.LineGroupId = item;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = null;
                grpData.budgetFormDesc = langStringValues["BudForm_Fin_sub_head_50"].LangText;
                if (onlyData)
                {
                    grpData.actualAmtYear = 0;
                    grpData.revisedBudAmtYear = 0;
                    grpData.orgBudAmtYear = 0;
                    grpData.actualAmtLastYear = 0;
                    grpData.orgBudAmtLastYear = 0;
                    grpData.revisedAmtLastYear = 0;
                    grpData.revisedAmtYearByAuth = 0;
                    grpData.revisedAmtLastYearByAuth = 0;
                    grpData.finPlanYear1 = 0;
                    grpData.finPlanYear2 = 0;
                    grpData.finPlanYear3 = 0;
                    grpData.finPlanYear4 = 0;
                    grpData.budgetChange = 0;
                    grpData.deviationForecastPct = 0;
                    grpData.deviationForecast = 0;
                    grpData.forecastAmount = 0;
                    grpData.deviation = 0;
                    grpData.deviationPct = 0;
                    grpData.budgetYtd = 0;
                    grpData.accountingYtd = 0;
                    grpData.deviationYtd = 0;
                    grpData.deviationYtdPct = 0;
                    grpData.polDeviation = 0;
                    grpData.polDeviationPct = 0;
                    grpData.unAprvBudChange = 0;
                    grpData.budgetChanges = 0;
                    grpData.deviationActionAmount = 0;
                    grpData.revIncUnAprvBudChange = 0;
                    grpData.devRevIncUnAprvBudChangeAndFc = 0;
                    grpData.devBudgetYtdAcc = 0;
                }
                else
                {
                    grpData.actualAmtYear = "";
                    grpData.revisedBudAmtYear = "";
                    grpData.orgBudAmtYear = "";
                    grpData.actualAmtLastYear = "";
                    grpData.orgBudAmtLastYear = "";
                    grpData.revisedAmtLastYear = "";
                    grpData.revisedAmtYearByAuth = "";
                    grpData.revisedAmtLastYearByAuth = "";
                    grpData.finPlanYear1 = "";
                    grpData.finPlanYear2 = "";
                    grpData.finPlanYear3 = "";
                    grpData.finPlanYear4 = "";
                    grpData.budgetChange = "";
                    grpData.deviationForecastPct = "";
                    grpData.deviationForecast = "";
                    grpData.forecastAmount = "";
                    grpData.deviation = "";
                    grpData.deviationPct = "";
                    grpData.budgetYtd = "";
                    grpData.accountingYtd = "";
                    grpData.deviationYtd = "";
                    grpData.deviationYtdPct = "";
                    grpData.polDeviation = "";
                    grpData.polDeviationPct = "";
                    grpData.unAprvBudChange = "";
                    grpData.budgetChanges = "";
                    grpData.deviationActionAmount = "";
                    grpData.revIncUnAprvBudChange = "";
                    grpData.devRevIncUnAprvBudChangeAndFc = "";
                    grpData.devBudgetYtdAcc = "";
                }
                grpData.semi = true;
                grpData.clickable = false;
                grpData.isSubHeading = true;
                grpData.numberFormat = "Text";
                grpData.note = null;
                grpData.rowType = "SubHeader";
                grpData.IsSecondGrid = false;
                dataArray.Add(grpData);
            }

            if (item != "20")
            {
                foreach (var d in data.Where(x => x.LineGroupId == item).ToList())
                {
                    grpData = new JObject();
                    grpData.LineGroupId = item;
                    grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                    grpData.id = d.id;
                    grpData.budgetFormDesc = d.budgetFormDesc;
                    grpData.actualAmtYear = d.actualAmtYear;
                    grpData.revisedBudAmtYear = d.revisedBudAmtYear;
                    grpData.orgBudAmtYear = d.orgBudAmtYear;
                    grpData.actualAmtLastYear = d.actualAmtLastYear;
                    grpData.orgBudAmtLastYear = d.orgBudAmtLastYear;
                    grpData.revisedAmtLastYear = d.revisedAmtLastYear;
                    grpData.revisedAmtYearByAuth = d.revisedAmtYearByAuth;
                    grpData.revisedAmtLastYearByAuth = d.revisedAmtLastYearByAuth;
                    grpData.finPlanYear1 = d.finPlanYear1;
                    grpData.finPlanYear2 = d.finPlanYear2;
                    grpData.finPlanYear3 = d.finPlanYear3;
                    grpData.finPlanYear4 = d.finPlanYear4;
                    grpData.budgetChange = d.budgetChange;
                    grpData.deviationForecastPct = d.deviationForecastPct;
                    grpData.deviationForecast = d.deviationForecast;
                    grpData.forecastAmount = d.forecastAmount;
                    grpData.semi = false;
                    grpData.isSubHeading = false;
                    grpData.clickable = true;
                    grpData.numberFormat = d.numberFormat;
                    grpData.note = commentList.FirstOrDefault(x => x.line_group_id.ToString() == item && x.line_item_id == d.id) != null ? commentList.FirstOrDefault(x => x.line_group_id.ToString() == item && x.line_item_id == d.id).comment : string.Empty;
                    grpData.rowType = "DetailRow";
                    grpData.IsSecondGrid = false;
                    grpData.deviation = d.deviation;
                    grpData.deviationPct = d.deviationPct;
                    grpData.budgetYtd = d.budgetYtd;
                    grpData.accountingYtd = d.accountingYtd;
                    grpData.deviationYtd = d.deviationYtd;
                    grpData.deviationYtdPct = d.deviationYtd != 0 && d.budgetYtd != 0 ? ((d.budgetYtd - d.accountingYtd) / d.budgetYtd) * 100 : 0;
                    grpData.polDeviation = d.polDeviation;
                    grpData.polDeviationPct = d.polDeviationPct;
                    grpData.unAprvBudChange = d.unAprvBudChange;
                    grpData.budgetChanges = d.budgetChanges;
                    grpData.revIncUnAprvBudChange = d.revIncUnAprvBudChange;
                    grpData.deviationActionAmount = d.deviationActionAmount;
                    grpData.devRevIncUnAprvBudChangeAndFc = d.devRevIncUnAprvBudChangeAndFc;
                    grpData.devBudgetYtdAcc = d.devBudgetYtdAcc;
                    dataArray.Add(grpData);
                }
            }
            else if (dontshowDetail == null || dontshowDetail.param_value.ToLower() == "false")
            {
                foreach (var d in data.Where(x => x.LineGroupId == item).ToList())
                {
                    grpData = new JObject();
                    grpData.LineGroupId = item;
                    grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                    grpData.id = d.id;
                    grpData.budgetFormDesc = d.budgetFormDesc;
                    grpData.actualAmtYear = d.actualAmtYear;
                    grpData.revisedBudAmtYear = d.revisedBudAmtYear;
                    grpData.orgBudAmtYear = d.orgBudAmtYear;
                    grpData.actualAmtLastYear = d.actualAmtLastYear;
                    grpData.orgBudAmtLastYear = d.orgBudAmtLastYear;
                    grpData.revisedAmtLastYear = d.revisedAmtLastYear;
                    grpData.revisedAmtYearByAuth = d.revisedAmtYearByAuth;
                    grpData.revisedAmtLastYearByAuth = d.revisedAmtLastYearByAuth;
                    grpData.finPlanYear1 = d.finPlanYear1;
                    grpData.finPlanYear2 = d.finPlanYear2;
                    grpData.finPlanYear3 = d.finPlanYear3;
                    grpData.finPlanYear4 = d.finPlanYear4;
                    grpData.budgetChange = d.budgetChange;
                    grpData.deviationForecastPct = d.deviationForecastPct;
                    grpData.deviationForecast = d.deviationForecast;
                    grpData.forecastAmount = d.forecastAmount;
                    grpData.semi = false;
                    grpData.clickable = true;
                    grpData.isSubHeading = false;
                    grpData.numberFormat = d.numberFormat;
                    grpData.note = commentList.FirstOrDefault(x => x.line_group_id.ToString() == item && x.line_item_id == d.id) != null ? commentList.FirstOrDefault(x => x.line_group_id.ToString() == item && x.line_item_id == d.id).comment : string.Empty;
                    grpData.rowType = "DetailRow";
                    grpData.IsSecondGrid = false;
                    grpData.deviation = d.deviation;
                    grpData.deviationPct = d.deviationPct;
                    grpData.budgetYtd = d.budgetYtd;
                    grpData.accountingYtd = d.accountingYtd;
                    grpData.deviationYtd = d.deviationYtd;
                    grpData.deviationYtdPct = d.deviationYtd != 0 && d.budgetYtd != 0 ? ((d.budgetYtd - d.accountingYtd) / d.budgetYtd) * 100 : 0;
                    grpData.polDeviation = d.polDeviation;
                    grpData.polDeviationPct = d.polDeviationPct;
                    grpData.unAprvBudChange = d.unAprvBudChange;
                    grpData.budgetChanges = d.budgetChanges;
                    grpData.revIncUnAprvBudChange = d.revIncUnAprvBudChange;
                    grpData.deviationActionAmount = d.deviationActionAmount;
                    grpData.devRevIncUnAprvBudChangeAndFc = d.devRevIncUnAprvBudChangeAndFc;
                    grpData.devBudgetYtdAcc = d.devBudgetYtdAcc;
                    dataArray.Add(grpData);
                }
            }

            if (item != "40" /*&& item!="50"*/)
            {
                //sum row
                forAmtPctData = data.Where(x => x.LineGroupId == item).Sum(x => x.revisedBudAmtYear.Value) != 0 ? ((data.Where(x => x.LineGroupId == item).Sum(x => x.revisedBudAmtYear.Value) - data.Where(x => x.LineGroupId == item).Sum(x => x.forecastAmount.Value)) / data.Where(x => x.LineGroupId == item).Sum(x => x.revisedBudAmtYear.Value)) * 100 : 0;
                polDeviationPercent = data.Where(x => x.LineGroupId == item).Sum(x => x.revisedAmtYearByAuth.Value) != 0 ? ((data.Where(x => x.LineGroupId == item).Sum(x => x.revisedAmtYearByAuth.Value) - data.Where(x => x.LineGroupId == item).Sum(x => x.actualAmtYear.Value)) / data.Where(x => x.LineGroupId == item).Sum(x => x.revisedAmtYearByAuth.Value)) * 100 : 0;
                grpData = new JObject();
                grpData.LineGroupId = -1;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = -1;
                grpData.budgetFormDesc = /*langStringValues["BudForm_sum"].LangText + " " +*/ data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.actualAmtYear = data.Where(x => x.LineGroupId == item).Sum(x => x.actualAmtYear);
                grpData.revisedBudAmtYear = data.Where(x => x.LineGroupId == item).Sum(x => x.revisedBudAmtYear);
                grpData.orgBudAmtYear = data.Where(x => x.LineGroupId == item).Sum(x => x.orgBudAmtYear);
                grpData.actualAmtLastYear = data.Where(x => x.LineGroupId == item).Sum(x => x.actualAmtLastYear);
                grpData.orgBudAmtLastYear = data.Where(x => x.LineGroupId == item).Sum(x => x.orgBudAmtLastYear);
                grpData.revisedAmtLastYear = data.Where(x => x.LineGroupId == item).Sum(x => x.revisedAmtLastYear);
                grpData.revisedAmtYearByAuth = data.Where(x => x.LineGroupId == item).Sum(x => x.revisedAmtYearByAuth);
                grpData.revisedAmtLastYearByAuth = data.Where(x => x.LineGroupId == item).Sum(x => x.revisedAmtLastYearByAuth);
                grpData.finPlanYear1 = data.Where(x => x.LineGroupId == item).Sum(x => x.finPlanYear1);
                grpData.finPlanYear2 = data.Where(x => x.LineGroupId == item).Sum(x => x.finPlanYear2);
                grpData.finPlanYear3 = data.Where(x => x.LineGroupId == item).Sum(x => x.finPlanYear3);
                grpData.finPlanYear4 = data.Where(x => x.LineGroupId == item).Sum(x => x.finPlanYear4);
                grpData.budgetChange = data.Where(x => x.LineGroupId == item).Sum(x => x.budgetChange);
                grpData.deviationForecastPct = forAmtPctData;
                grpData.deviationForecast = data.Where(x => x.LineGroupId == item).Sum(x => x.deviationForecast);
                grpData.forecastAmount = data.Where(x => x.LineGroupId == item).Sum(x => x.forecastAmount);
                grpData.semi = true;
                grpData.clickable = false;
                grpData.isSubHeading = false;
                grpData.numberFormat = "n0";
                grpData.note = null;
                grpData.rowType = "SumRow";
                grpData.IsSecondGrid = false;
                grpData.deviation = data.Where(x => x.LineGroupId == item).Sum(x => x.deviation);
                grpData.deviationPct = data.Where(x => x.LineGroupId == item).Sum(x => x.revisedBudAmtYear) != 0 ? ((data.Where(x => x.LineGroupId == item).Sum(x => x.revisedBudAmtYear) - data.Where(x => x.LineGroupId == item).Sum(x => x.actualAmtYear)) / data.Where(x => x.LineGroupId == item).Sum(x => x.revisedBudAmtYear)) * 100 : 0;
                grpData.polDeviation = data.Where(x => x.LineGroupId == item).Sum(x => x.polDeviation);
                grpData.polDeviationPct = polDeviationPercent;
                grpData.budgetYtd = data.Where(x => x.LineGroupId == item).Sum(x => x.budgetYtd);
                grpData.accountingYtd = data.Where(x => x.LineGroupId == item).Sum(x => x.accountingYtd);
                grpData.deviationYtd = data.Where(x => x.LineGroupId == item).Sum(x => x.deviationYtd);
                grpData.deviationYtdPct = data.Where(x => x.LineGroupId == item).Sum(x => x.deviationYtd) != 0 && data.Where(x => x.LineGroupId == item).Sum(x => x.budgetYtd) != 0 ? ((data.Where(x => x.LineGroupId == item).Sum(x => x.budgetYtd) - data.Where(x => x.LineGroupId == item).Sum(x => x.accountingYtd)) / data.Where(x => x.LineGroupId == item).Sum(x => x.budgetYtd)) * 100 : 0;
                grpData.unAprvBudChange = data.Where(x => x.LineGroupId == item).Sum(x => x.unAprvBudChange);
                grpData.budgetChanges = data.Where(x => x.LineGroupId == item).Sum(x => x.budgetChanges);
                grpData.revIncUnAprvBudChange = data.Where(x => x.LineGroupId == item).Sum(x => x.revIncUnAprvBudChange);
                grpData.deviationActionAmount = data.Where(x => x.LineGroupId == item).Sum(x => x.deviationActionAmount);
                grpData.devRevIncUnAprvBudChangeAndFc = data.Where(x => x.LineGroupId == item).Sum(x => x.devRevIncUnAprvBudChangeAndFc);
                grpData.devBudgetYtdAcc = data.Where(x => x.LineGroupId == item).Sum(x => x.budgetYtd) - data.Where(x => x.LineGroupId == item).Sum(x => x.accountingYtd);
                dataArray.Add(grpData);
            }

            //empty row
            if (!onlyData)
            {
                grpData = new JObject();
                grpData.LineGroupId = null;
                grpData.LineGroup = null;
                grpData.id = null;
                grpData.budgetFormDesc = "";
                grpData.actualAmtYear = "";
                grpData.revisedBudAmtYear = "";
                grpData.orgBudAmtYear = "";
                grpData.actualAmtLastYear = "";
                grpData.orgBudAmtLastYear = "";
                grpData.revisedAmtLastYear = "";
                grpData.revisedAmtYearByAuth = "";
                grpData.revisedAmtLastYearByAuth = "";
                grpData.finPlanYear1 = "";
                grpData.finPlanYear2 = "";
                grpData.finPlanYear3 = "";
                grpData.finPlanYear4 = "";
                grpData.budgetChange = "";
                grpData.deviationForecastPct = "";
                grpData.deviationForecast = "";
                grpData.forecastAmount = "";
                grpData.semi = true;
                grpData.isSubHeading = false;
                grpData.clickable = false;
                grpData.numberFormat = "Text";
                grpData.note = null;
                grpData.rowType = "DetailRow";
                grpData.IsSecondGrid = false;
                grpData.deviation = "";
                grpData.deviationPct = "";
                grpData.budgetYtd = "";
                grpData.accountingYtd = "";
                grpData.deviationYtd = "";
                grpData.deviationYtdPct = "";
                grpData.polDeviation = "";
                grpData.polDeviationPct = "";
                grpData.unAprvBudChange = "";
                grpData.budgetChanges = "";
                grpData.revIncUnAprvBudChange = "";
                grpData.deviationActionAmount = "";
                grpData.devRevIncUnAprvBudChangeAndFc = "";
                grpData.devBudgetYtdAcc = "";
                dataArray.Add(grpData);
            }

            if (count == LineGroupIdList.Count)
            {
                ////final total row
                ////sum row
                forAmtPctData = data.Sum(x => x.revisedBudAmtYear.Value) != 0 ? ((data.Sum(x => x.revisedBudAmtYear.Value) - data.Sum(x => x.forecastAmount.Value)) / data.Sum(x => x.revisedBudAmtYear.Value)) * 100 : 0;
                polDeviationPercent = data.Sum(x => x.revisedAmtYearByAuth.Value) != 0 ? ((data.Sum(x => x.revisedAmtYearByAuth.Value) - data.Sum(x => x.actualAmtYear.Value)) / data.Sum(x => x.revisedAmtYearByAuth.Value)) * 100 : 0;
                grpData = new JObject();
                grpData.LineGroupId = -1;
                grpData.LineGroup = "";
                grpData.id = -1;
                grpData.budgetFormDesc = /*langStringValues["BudForm_sum"].LangText + " " +*/ langStringValues["BudForm_fin_Sum_B3"].LangText;
                grpData.actualAmtYear = data.Sum(x => x.actualAmtYear);
                grpData.revisedBudAmtYear = data.Sum(x => x.revisedBudAmtYear);
                grpData.orgBudAmtYear = data.Sum(x => x.orgBudAmtYear);
                grpData.actualAmtLastYear = data.Sum(x => x.actualAmtLastYear);
                grpData.orgBudAmtLastYear = data.Sum(x => x.orgBudAmtLastYear);
                grpData.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear);
                grpData.revisedAmtYearByAuth = data.Sum(x => x.revisedAmtYearByAuth);
                grpData.revisedAmtLastYearByAuth = data.Sum(x => x.revisedAmtLastYearByAuth);
                grpData.finPlanYear1 = data.Sum(x => x.finPlanYear1);
                grpData.finPlanYear2 = data.Sum(x => x.finPlanYear2);
                grpData.finPlanYear3 = data.Sum(x => x.finPlanYear3);
                grpData.finPlanYear4 = data.Sum(x => x.finPlanYear4);
                grpData.budgetChange = data.Sum(x => x.budgetChange);
                grpData.deviationForecastPct = forAmtPctData;
                grpData.deviationForecast = data.Sum(x => x.deviationForecast);
                grpData.forecastAmount = data.Sum(x => x.forecastAmount);
                grpData.semi = true;
                grpData.clickable = false;
                grpData.isSubHeading = false;
                grpData.numberFormat = "n0";
                grpData.note = null;
                grpData.rowType = "SumRowGrandTotal";
                grpData.IsSecondGrid = false;
                grpData.deviation = data.Sum(x => x.deviation);
                grpData.deviationPct = data.Sum(x => x.revisedBudAmtYear) != 0 ? ((data.Sum(x => x.revisedBudAmtYear) - data.Sum(x => x.actualAmtYear)) / data.Sum(x => x.revisedBudAmtYear)) * 100 : 0;
                grpData.budgetYtd = data.Sum(x => x.budgetYtd);
                grpData.accountingYtd = data.Sum(x => x.accountingYtd);
                grpData.deviationYtd = data.Sum(x => x.deviationYtd);
                grpData.deviationYtdPct = data.Sum(x => x.deviationYtd) != 0 && data.Sum(x => x.budgetYtd) != 0 ? ((data.Sum(x => x.budgetYtd) - data.Sum(x => x.accountingYtd)) / data.Sum(x => x.budgetYtd)) * 100 : 0;
                grpData.polDeviation = data.Sum(x => x.polDeviation);
                grpData.polDeviationPct = polDeviationPercent;
                grpData.unAprvBudChange = data.Sum(x => x.unAprvBudChange);
                grpData.budgetChanges = data.Sum(x => x.budgetChanges);
                grpData.revIncUnAprvBudChange = data.Sum(x => x.revIncUnAprvBudChange);
                grpData.deviationActionAmount = data.Sum(x => x.deviationActionAmount);
                grpData.devRevIncUnAprvBudChangeAndFc = data.Sum(x => x.devRevIncUnAprvBudChangeAndFc);
                grpData.devBudgetYtdAcc = data.Sum(x => x.devBudgetYtdAcc);
                dataArray.Add(grpData);
            }

            if (item == "20" || item == "40" || item == "50")
            {
                List<string> lgIdList = new List<string>();

                if (item == "40")
                {
                    lgIdList.Add("10");
                    lgIdList.Add("20");
                    lgIdList.Add("30");
                    lgIdList.Add("40");
                }
                else
                {
                    for (int i = 0; i <= index; i++)
                    {
                        lgIdList.Add(LineGroupIdList[i]);
                    }
                }
                if (item != "50")
                {
                    //sum row
                    forAmtPctData = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.revisedBudAmtYear.Value) != 0 ? ((data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.revisedBudAmtYear.Value) - data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.forecastAmount.Value)) / data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.revisedBudAmtYear.Value)) * 100 : 0;
                    polDeviationPercent = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.revisedAmtYearByAuth.Value) != 0 ? ((data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.revisedAmtYearByAuth.Value) - data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.actualAmtYear.Value)) / data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.revisedAmtYearByAuth.Value)) * 100 : 0;
                    grpData = new JObject();
                    grpData.LineGroupId = -1;
                    grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                    grpData.id = -1;
                    grpData.budgetFormDesc = langStringValues["BudForm_sum_fin_" + item].LangText;
                    grpData.actualAmtYear = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.actualAmtYear);
                    grpData.revisedBudAmtYear = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.revisedBudAmtYear);
                    grpData.orgBudAmtYear = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.orgBudAmtYear);
                    grpData.actualAmtLastYear = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.actualAmtLastYear);
                    grpData.orgBudAmtLastYear = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.orgBudAmtLastYear);
                    grpData.revisedAmtLastYear = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.revisedAmtLastYear);
                    grpData.revisedAmtYearByAuth = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.revisedAmtYearByAuth);
                    grpData.revisedAmtLastYearByAuth = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.revisedAmtLastYearByAuth);
                    grpData.finPlanYear1 = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.finPlanYear1);
                    grpData.finPlanYear2 = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.finPlanYear2);
                    grpData.finPlanYear3 = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.finPlanYear3);
                    grpData.finPlanYear4 = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.finPlanYear4);
                    grpData.budgetChange = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.budgetChange);
                    grpData.deviationForecastPct = forAmtPctData;
                    grpData.deviationForecast = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.deviationForecast);
                    grpData.forecastAmount = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.forecastAmount);
                    grpData.semi = true;
                    grpData.clickable = false;
                    grpData.numberFormat = "n0";
                    grpData.isSubHeading = false;
                    grpData.note = null;
                    grpData.rowType = "SumRow";
                    grpData.IsSecondGrid = false;
                    grpData.deviation = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.deviation);
                    grpData.deviationPct = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.revisedBudAmtYear) != 0 ? ((data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.revisedBudAmtYear) - data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.actualAmtYear)) / data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.revisedBudAmtYear)) * 100 : 0;
                    grpData.budgetYtd = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.budgetYtd);
                    grpData.accountingYtd = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.accountingYtd);
                    grpData.deviationYtd = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.deviationYtd);
                    grpData.deviationYtdPct = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.deviationYtd) != 0 && data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.budgetYtd) != 0 ? ((data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.budgetYtd) - data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.accountingYtd)) / data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.budgetYtd)) * 100 : 0;
                    grpData.polDeviation = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.polDeviation);
                    grpData.polDeviationPct = polDeviationPercent;
                    grpData.unAprvBudChange = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.unAprvBudChange);
                    grpData.budgetChanges = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.budgetChanges);
                    grpData.revIncUnAprvBudChange = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.revIncUnAprvBudChange);
                    grpData.deviationActionAmount = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.deviationActionAmount);
                    grpData.devRevIncUnAprvBudChangeAndFc = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.devRevIncUnAprvBudChangeAndFc);
                    grpData.devBudgetYtdAcc  = data.Where(x => lgIdList.Contains(x.LineGroupId)).Sum(x => x.devBudgetYtdAcc);
                    dataArray.Add(grpData);
                }

                //empty row
                if (!onlyData)
                {
                    grpData = new JObject();
                    grpData.LineGroupId = null;
                    grpData.LineGroup = null;
                    grpData.id = null;
                    grpData.budgetFormDesc = "";
                    grpData.actualAmtYear = "";
                    grpData.revisedBudAmtYear = "";
                    grpData.orgBudAmtYear = "";
                    grpData.actualAmtLastYear = "";
                    grpData.orgBudAmtLastYear = "";
                    grpData.revisedAmtLastYear = "";
                    grpData.revisedAmtYearByAuth = "";
                    grpData.revisedAmtLastYearByAuth = "";
                    grpData.finPlanYear1 = "";
                    grpData.finPlanYear2 = "";
                    grpData.finPlanYear3 = "";
                    grpData.finPlanYear4 = "";
                    grpData.budgetChange = "";
                    grpData.deviationForecastPct = "";
                    grpData.deviationForecast = "";
                    grpData.forecastAmount = "";
                    grpData.semi = true;
                    grpData.isSubHeading = false;
                    grpData.clickable = false;
                    grpData.numberFormat = "Text";
                    grpData.note = null;
                    grpData.rowType = "DetailRow";
                    grpData.IsSecondGrid = false;
                    grpData.deviation = "";
                    grpData.deviationPct = "";
                    grpData.budgetYtd = "";
                    grpData.accountingYtd = "";
                    grpData.deviationYtd = "";
                    grpData.deviationYtdPct = "";
                    grpData.polDeviation = "";
                    grpData.polDeviationPct = "";
                    grpData.unAprvBudChange = "";
                    grpData.budgetChanges = "";
                    grpData.revIncUnAprvBudChange = "";
                    grpData.deviationActionAmount = "";
                    grpData.devRevIncUnAprvBudChangeAndFc = "";
                    grpData.devBudgetYtdAcc = "";
                    dataArray.Add(grpData);
                }
            }

            index++;
        }

        // remove final total row #45351
        ////final total row
        ////sum row

        finalData.data = dataArray;
        if (!onlyData)
        {
            finalData.columns = await GetBudgetFormColumnAsync(budgetYear, userId, "B3", docType, forecastPeriod);
            finalData.header = header;
        }
        return finalData;
    }

    private async static Task<List<tbf_budget_form_comment>> GetCommentListAsync(int budgetYear, UserData userDetails, TenantDBContext dbContext, string TabName, int docType, int forecastPeriod = 0)
    {
        return forecastPeriod == 0 ? await dbContext.tbf_budget_form_comment.Where(x => x.budget_year == budgetYear && x.fk_tenant_id == userDetails.tenant_id && x.doc_type_id == docType && x.budget_form_type == TabName).ToListAsync() :
            await dbContext.tbf_budget_form_comment.Where(x => x.forecast_period == forecastPeriod && x.fk_tenant_id == userDetails.tenant_id && x.doc_type_id == docType && x.budget_form_type == TabName).ToListAsync();
    }

    public JObject GetForm4Data(int budgetYear, string userId, int docType, int forecastPeriod = 0, bool onlyData = false)
    {
        return GetForm4DataAsync(budgetYear, userId, docType, forecastPeriod, onlyData).GetAwaiter().GetResult();
    }
    public async Task<JObject> GetForm4DataAsync(int budgetYear, string userId, int docType, int forecastPeriod = 0, bool onlyData = false)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValues = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetForm");

        dynamic header = new JObject();
        header.title = langStringValues["BudForm_B4_title"].LangText;
        header.descriptiontip = langStringValues["BudForm_B4_desc"].LangText;
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        var commentList = await GetCommentListAsync(budgetYear, userDetails, dbContext, clsConstants.budget_form_type.BudgetFormB4.ToString(), docType, forecastPeriod);
        JArray dataArray = new JArray();
        dynamic finalData = new JObject();
        var data = (from a in (await GetForm4DataFromDBAsync(budgetYear, userId, docType, forecastPeriod))
            group a by new { line_group_id = a.line_group_id.ToString().Trim(), line_group = a.line_group.ToString().Trim(), line_item_id = a.line_item_id.ToString().Trim(), line_item = a.line_item.ToString().Trim() } into grp
            select new clsBudgetFormHelper()
            {
                LineGroupId = grp.Key.line_group_id.ToString(),
                LineGroup = grp.Key.line_group,
                id = grp.Key.line_item_id.ToString(),
                budgetFormDesc = grp.Key.line_item,
                actualAmtYear = grp.Sum(x => x.actual_amt_year),
                revisedBudAmtYear = grp.Sum(x => x.revised_bud_amt_year),
                orgBudAmtYear = grp.Sum(x => x.org_bud_amt_year),
                actualAmtLastYear = grp.Sum(x => x.actual_amt_last_year),
                orgBudAmtLastYear = grp.Sum(x => x.org_bud_amt_last_year),
                revisedAmtLastYear = grp.Sum(x => x.revised_bud_amt_last_year),
                finPlanYear1 = grp.Sum(x => x.finplan_year_1_amount),
                finPlanYear2 = grp.Sum(x => x.finplan_year_2_amount),
                finPlanYear3 = grp.Sum(x => x.finplan_year_3_amount),
                finPlanYear4 = grp.Sum(x => x.finplan_year_4_amount),
                forecastAmount = grp.Sum(x => x.forecast_amount),
                deviationForecast = grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount),
                deviationForecastPct = grp.Sum(x => x.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount)) / grp.Sum(x => x.revised_bud_amt_year)) * 100 : 0,
                budgetChange = grp.Sum(x => x.forecast_amount) - grp.Sum(x => x.revised_bud_amt_year),
                numberFormat = "n0",
                isSubHeading = false
            }).OrderBy(x => x.LineGroupId).ThenBy(x => x.LineGroup).ThenBy(x => x.id).ThenBy(x => x.budgetFormDesc).ToList();

        var LineGroupIdList = new List<string>();
        decimal forAmtPctData = 0;
        LineGroupIdList.AddRange(data.Select(x => x.LineGroupId).Distinct().ToList());
        dynamic grpData;
        foreach (var item in LineGroupIdList)
        {
            grpData = new JObject();
            grpData.LineGroupId = item;
            grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
            grpData.id = null;
            grpData.budgetFormDesc = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
            if (onlyData)
            {
                grpData.actualAmtYear = 0;
                grpData.revisedBudAmtYear = 0;
                grpData.orgBudAmtYear = 0;
                grpData.actualAmtLastYear = 0;
                grpData.orgBudAmtLastYear = 0;
                grpData.revisedAmtLastYear = 0;
                grpData.finPlanYear1 = 0;
                grpData.finPlanYear2 = 0;
                grpData.finPlanYear3 = 0;
                grpData.finPlanYear4 = 0;
                grpData.budgetChange = 0;
                grpData.deviationForecastPct = 0;
                grpData.deviationForecast = 0;
                grpData.forecastAmount = 0;
            }
            else
            {
                grpData.actualAmtYear = "";
                grpData.revisedBudAmtYear = "";
                grpData.orgBudAmtYear = "";
                grpData.actualAmtLastYear = "";
                grpData.orgBudAmtLastYear = "";
                grpData.revisedAmtLastYear = "";
                grpData.finPlanYear1 = "";
                grpData.finPlanYear2 = "";
                grpData.finPlanYear3 = "";
                grpData.finPlanYear4 = "";
                grpData.budgetChange = "";
                grpData.deviationForecastPct = "";
                grpData.deviationForecast = "";
                grpData.forecastAmount = "";
            }
            grpData.isSubHeading = true;
            grpData.semi = true;
            grpData.clickable = false;
            grpData.numberFormat = "Text";
            grpData.note = null;
            grpData.IsSecondGrid = false;
            dataArray.Add(grpData);

            foreach (var d in data.Where(x => x.LineGroupId == item).ToList())
            {
                grpData = new JObject();
                grpData.LineGroupId = item;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = d.id;
                grpData.budgetFormDesc = d.budgetFormDesc;
                grpData.actualAmtYear = d.actualAmtYear;
                grpData.revisedBudAmtYear = d.revisedBudAmtYear;
                grpData.orgBudAmtYear = d.orgBudAmtYear;
                grpData.actualAmtLastYear = d.actualAmtLastYear;
                grpData.orgBudAmtLastYear = d.orgBudAmtLastYear;
                grpData.revisedAmtLastYear = d.revisedAmtLastYear;
                grpData.finPlanYear1 = d.finPlanYear1;
                grpData.finPlanYear2 = d.finPlanYear2;
                grpData.finPlanYear3 = d.finPlanYear3;
                grpData.finPlanYear4 = d.finPlanYear4;
                grpData.budgetChange = d.budgetChange;
                grpData.deviationForecastPct = d.deviationForecastPct;
                grpData.deviationForecast = d.deviationForecast;
                grpData.forecastAmount = d.forecastAmount;
                grpData.semi = false;
                grpData.clickable = true;
                grpData.isSubHeading = false;
                grpData.numberFormat = d.numberFormat;
                grpData.note = commentList.FirstOrDefault(x => x.line_group_id.ToString() == item && x.line_item_id == d.id) != null ? commentList.FirstOrDefault(x => x.line_group_id.ToString() == item && x.line_item_id == d.id).comment : string.Empty;
                grpData.IsSecondGrid = false;
                dataArray.Add(grpData);
            }
            //sum row
            forAmtPctData = data.Where(x => x.LineGroupId == item).Sum(x => x.revisedBudAmtYear.Value) != 0 ? ((data.Where(x => x.LineGroupId == item).Sum(x => x.revisedBudAmtYear.Value) - data.Where(x => x.LineGroupId == item).Sum(x => x.forecastAmount.Value)) / data.Where(x => x.LineGroupId == item).Sum(x => x.revisedBudAmtYear.Value)) * 100 : 0;
            grpData = new JObject();
            grpData.LineGroupId = -1;
            grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
            grpData.id = -1;
            grpData.budgetFormDesc = langStringValues["BudForm_sum"].LangText + " " + data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
            grpData.actualAmtYear = data.Where(x => x.LineGroupId == item).Sum(x => x.actualAmtYear);
            grpData.revisedBudAmtYear = data.Where(x => x.LineGroupId == item).Sum(x => x.revisedBudAmtYear);
            grpData.orgBudAmtYear = data.Where(x => x.LineGroupId == item).Sum(x => x.orgBudAmtYear);
            grpData.actualAmtLastYear = data.Where(x => x.LineGroupId == item).Sum(x => x.actualAmtLastYear);
            grpData.orgBudAmtLastYear = data.Where(x => x.LineGroupId == item).Sum(x => x.orgBudAmtLastYear);
            grpData.revisedAmtLastYear = data.Where(x => x.LineGroupId == item).Sum(x => x.revisedAmtLastYear);
            grpData.finPlanYear1 = data.Where(x => x.LineGroupId == item).Sum(x => x.finPlanYear1);
            grpData.finPlanYear2 = data.Where(x => x.LineGroupId == item).Sum(x => x.finPlanYear2);
            grpData.finPlanYear3 = data.Where(x => x.LineGroupId == item).Sum(x => x.finPlanYear3);
            grpData.finPlanYear4 = data.Where(x => x.LineGroupId == item).Sum(x => x.finPlanYear4);
            grpData.budgetChange = data.Where(x => x.LineGroupId == item).Sum(x => x.budgetChange);
            grpData.deviationForecastPct = forAmtPctData;
            grpData.deviationForecast = data.Where(x => x.LineGroupId == item).Sum(x => x.deviationForecast);
            grpData.forecastAmount = data.Where(x => x.LineGroupId == item).Sum(x => x.forecastAmount);
            grpData.semi = true;
            grpData.clickable = false;
            grpData.isSubHeading = false;
            grpData.numberFormat = "n0";
            grpData.note = null;
            grpData.IsSecondGrid = false;
            dataArray.Add(grpData);

            //empty row
            if (!onlyData)
            {
                grpData = new JObject();
                grpData.LineGroupId = null;
                grpData.LineGroup = null;
                grpData.id = null;
                grpData.budgetFormDesc = "";
                grpData.actualAmtYear = "";
                grpData.revisedBudAmtYear = "";
                grpData.orgBudAmtYear = "";
                grpData.actualAmtLastYear = "";
                grpData.orgBudAmtLastYear = "";
                grpData.revisedAmtLastYear = "";
                grpData.finPlanYear1 = "";
                grpData.finPlanYear2 = "";
                grpData.finPlanYear3 = "";
                grpData.finPlanYear4 = "";
                grpData.budgetChange = "";
                grpData.deviationForecastPct = "";
                grpData.deviationForecast = "";
                grpData.forecastAmount = "";
                grpData.semi = true;
                grpData.isSubHeading = false;
                grpData.clickable = false;
                grpData.numberFormat = "Text";
                grpData.note = null;
                grpData.IsSecondGrid = false;
                dataArray.Add(grpData);
            }
        }

        //final total row
        //sum row
        forAmtPctData = data.Sum(x => x.revisedBudAmtYear.Value) != 0 ? ((data.Sum(x => x.revisedBudAmtYear.Value) - data.Sum(x => x.forecastAmount.Value)) / data.Sum(x => x.revisedBudAmtYear.Value)) * 100 : 0;
        grpData = new JObject();
        grpData.LineGroupId = -1;
        grpData.LineGroup = "";
        grpData.id = -1;
        grpData.budgetFormDesc = langStringValues["BudForm_sum"].LangText + " " + langStringValues["BudForm_Sum_B4"].LangText;
        grpData.actualAmtYear = data.Sum(x => x.actualAmtYear);
        grpData.revisedBudAmtYear = data.Sum(x => x.revisedBudAmtYear);
        grpData.orgBudAmtYear = data.Sum(x => x.orgBudAmtYear);
        grpData.actualAmtLastYear = data.Sum(x => x.actualAmtLastYear);
        grpData.orgBudAmtLastYear = data.Sum(x => x.orgBudAmtLastYear);
        grpData.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear);
        grpData.finPlanYear1 = data.Sum(x => x.finPlanYear1);
        grpData.finPlanYear2 = data.Sum(x => x.finPlanYear2);
        grpData.finPlanYear3 = data.Sum(x => x.finPlanYear3);
        grpData.finPlanYear4 = data.Sum(x => x.finPlanYear4);
        grpData.budgetChange = data.Sum(x => x.budgetChange);
        grpData.deviationForecastPct = forAmtPctData;
        grpData.deviationForecast = data.Sum(x => x.deviationForecast);
        grpData.forecastAmount = data.Sum(x => x.forecastAmount);
        grpData.semi = true;
        grpData.isSubHeading = false;
        grpData.clickable = false;
        grpData.numberFormat = "n0";
        grpData.note = null;
        grpData.IsSecondGrid = false;
        dataArray.Add(grpData);

        finalData.data = dataArray;
        if (!onlyData)
        {
            finalData.columns = await GetBudgetFormColumnAsync(budgetYear, userId, "B4", docType, forecastPeriod);
            finalData.header = header;
        }
        return finalData;
    }

    public async Task<JObject> GetForm3PopUpDataAsync(int budgetYear, string userId, string column, string id, clsBudgetFormPopUpHelper searchHelper, int docType, int forecastPeriod = 0, List<string> budgetPhaseIDs = null, List<string> freedim2Ids = null, bool onlyData = false)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValues = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetForm");

        dynamic header = new JObject();
        header.title = langStringValues["BudForm_B3_popUp_title"].LangText;
        header.descriptiontip = langStringValues["BudForm_B3_popUp_desc"].LangText;
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        JArray dataArray = new JArray();
        dynamic finalData = new JObject();
        var data = new List<clsBudgetFormPopUpHelper>();

        var dbData = (from a1 in await GetForm3DataFromDBAsync(budgetYear, userId, docType, forecastPeriod, budgetPhaseIDs: budgetPhaseIDs, freedim2Ids: freedim2Ids)
            where id == a1.line_item_id.ToString()
            select a1).ToList();
        switch (column)
        {
            case "actualAmtYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        ammountCol = a.actual_amt_year,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "revisedBudAmtYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        ammountCol = a.revised_bud_amt_year,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "orgBudAmtYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        ammountCol = a.org_bud_amt_year,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "actualAmtLastYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        ammountCol = a.actual_amt_last_year,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;

            case "orgBudAmtLastYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        ammountCol = a.org_bud_amt_last_year,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "revisedAmtLastYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        ammountCol = a.revised_bud_amt_last_year,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "revisedAmtLastYearByAuth":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        ammountCol = a.revised_bud_auth_last_year,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "revisedAmtYearByAuth":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        ammountCol = a.revised_bud_auth_year,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break; 
            case "finPlanYear1":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        ammountCol = a.finplan_year_1_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear2":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        ammountCol = a.finplan_year_2_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear3":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        ammountCol = a.finplan_year_3_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear4":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        ammountCol = a.finplan_year_4_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
        }

        if (searchHelper != null)
        {
            data = filterBudgetFormData(searchHelper, data);
        }

        finalData.data = JArray.FromObject(data);
        if (!onlyData)
        {
            finalData.columns = await GetBudgetFormPopUpColumnAsync(budgetYear, userId, column, docType);
            finalData.header = header;
        }
        return finalData;
    }



    public async Task<JObject> GetForm4PopUpDataAsync(int budgetYear, string userId, string column, string id, clsBudgetFormPopUpHelper searchHelper, int docType, int forecastPeriod = 0, bool onlyData = false)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValues = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetForm");

        dynamic header = new JObject();
        header.title = langStringValues["BudForm_B4_popUp_title"].LangText;
        header.descriptiontip = langStringValues["BudForm_B4_popUp_desc"].LangText;
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        JArray dataArray = new JArray();
        dynamic finalData = new JObject();
        var data = new List<clsBudgetFormPopUpHelper>();
        var dbData = (from a1 in await GetForm4DataFromDBAsync(budgetYear, userId, docType, forecastPeriod)
            where id == a1.line_item_id.ToString()
            select a1).ToList();
        switch (column)
        {
            case "actualAmtYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        ammountCol = a.actual_amt_year,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "revisedBudAmtYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        ammountCol = a.revised_bud_amt_year,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "orgBudAmtYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        ammountCol = a.org_bud_amt_year,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "actualAmtLastYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        ammountCol = a.actual_amt_last_year,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "orgBudAmtLastYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        ammountCol = a.org_bud_amt_last_year,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "revisedAmtLastYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        ammountCol = a.revised_bud_amt_last_year,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear1":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        ammountCol = a.finplan_year_1_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear2":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        ammountCol = a.finplan_year_2_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear3":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        ammountCol = a.finplan_year_3_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear4":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        ammountCol = a.finplan_year_4_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
        }

        if (searchHelper != null)
        {
            data = filterBudgetFormData(searchHelper, data);
        }

        finalData.data = JArray.FromObject(data);
        if (!onlyData)
        {
            finalData.columns = await GetBudgetFormPopUpColumnAsync(budgetYear, userId, column, docType);
            finalData.header = header;
        }
        return finalData;
    }



    private async Task< List<tbf_budget_form_B3>> GetForm3DataFromDBAsync(int budgetYear, string userId, int docType, int forecastPeriod, string fridimData = "", List<string> budgetPhaseIDs = null, List<string> freedim2Ids = null, List<string>? departmentCode = null)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        List<int> changeIds = budgetPhaseIDs != null && budgetPhaseIDs.Count > 0 ? await GetChangeIdsAsync(budgetYear, budgetPhaseIDs, userId) : new List<int>();
        if (string.IsNullOrEmpty(fridimData))
        {
            List<tbf_budget_form_B3> data = forecastPeriod == 0 ? await (from a in dbContext.tbf_budget_form_B3
                    where a.year == budgetYear && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
                    select a).ToListAsync()
                :await (from a in dbContext.tbf_budget_form_B3
                    where a.forecast_period == forecastPeriod && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
                    select a).ToListAsync();
            if (changeIds.Count > 0)
            {
                data = data.Where(x => changeIds.Contains(x.fk_change_id)).ToList();
            }
            if (freedim2Ids != null && freedim2Ids.Count > 0)
            {
                var emptyFreedimSelected = freedim2Ids.Contains("-1");
                data = emptyFreedimSelected ? data.Where(x => freedim2Ids.Contains(x.free_dim_code) || string.IsNullOrEmpty(x.free_dim_code)).ToList() : data.Where(x => freedim2Ids.Contains(x.free_dim_code)).ToList();
            }

            if (departmentCode != null && departmentCode.Any())
            {
                data = data.Where(x => departmentCode.Contains(x.fk_department_code)).ToList();
            }
            return data;
        }
        else
        {
            List<tbf_budget_form_B3> dataSet = new();
            if (fridimData == DocData.EmptyFridm.ToString())
            {
                dataSet = forecastPeriod != 0 ? await (from a in dbContext.tbf_budget_form_B3
                    where a.forecast_period == forecastPeriod && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType && string.IsNullOrEmpty(a.free_dim_code)
                    select a).ToListAsync() : await (from a in dbContext.tbf_budget_form_B3
                    where a.fk_tenant_id == userDetails.tenant_id && a.year == budgetYear && a.doc_type == docType && string.IsNullOrEmpty(a.free_dim_code)
                    select a).ToListAsync();
            }
            else
            {
                dataSet = forecastPeriod != 0 ? await (from a in dbContext.tbf_budget_form_B3
                    where a.forecast_period == forecastPeriod && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType && (a.free_dim_code != null && !string.IsNullOrEmpty(a.free_dim_code.Trim()))
                    select a).ToListAsync() : await (from a in dbContext.tbf_budget_form_B3
                    where a.fk_tenant_id == userDetails.tenant_id && a.year == budgetYear && a.doc_type == docType && (a.free_dim_code != null && !string.IsNullOrEmpty(a.free_dim_code.Trim()))
                    select a).ToListAsync();
            }

            if(departmentCode !=null && departmentCode.Any())
            {
                dataSet = dataSet.Where(x => departmentCode.Contains(x.fk_department_code)).ToList();
            }
            return dataSet;
        }
    }



    private async Task<List<tbf_budget_form_B4>> GetForm4DataFromDBAsync(int budgetYear, string userId, int docType, int forecastPeriod)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        List<tbf_budget_form_B4> data = forecastPeriod == 0 ? await (from a in dbContext.tbf_budget_form_B4
                where a.year == budgetYear && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
                select a).ToListAsync()
            : await (from a in dbContext.tbf_budget_form_B4
                where a.forecast_period == forecastPeriod && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
                select a).ToListAsync();
        return data;
    }

}