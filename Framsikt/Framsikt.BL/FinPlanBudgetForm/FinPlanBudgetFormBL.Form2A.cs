using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;

namespace Framsikt.BL;

public partial class FinPlanBudgetFormBL
{

    public JObject Get2AData(int budgetYear, string userId, int docType, int forecastPeriod = 0, string budgetPhaseId = null, bool showOnlyModified = false, int yearColumnsSelection = 0, GroupingYear groupingYearSelection = GroupingYear.NoGrouping, string fridimData = "", List<string> budgetPhaseIDs = null, List<string> freedim2Ids = null, bool onlyData = false)
    {
        return Get2ADataAsync(budgetYear, userId, docType, forecastPeriod, budgetPhaseId, showOnlyModified, yearColumnsSelection, groupingYearSelection, fridimData, budgetPhaseIDs, freedim2Ids, onlyData).GetAwaiter().GetResult();
    }

    public async Task<JObject> Get2ADataAsync(int budgetYear, string userId, int docType, int forecastPeriod = 0, string budgetPhaseId = null, bool showOnlyModified = false, int yearColumnsSelection = 0, GroupingYear groupingYearSelection = GroupingYear.NoGrouping, string fridimData = "", List<string> budgetPhaseIDs = null, List<string> freedim2Ids = null, bool onlyData = false)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValues = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetForm");

        dynamic header = new JObject();
        header.title = langStringValues["BudForm_2A_title"].LangText;
        header.descriptiontip = langStringValues["BudForm_2A_desc"].LangText;
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        var commentList = await GetCommentListAsync(budgetYear, userDetails, dbContext, clsConstants.budget_form_type.BudgetForm2A.ToString(), docType, forecastPeriod);
        List<int> changeIds = new List<int>();

        //#region Fetch changeIds

        if (!string.IsNullOrEmpty(budgetPhaseId) && budgetPhaseId != "-1")
        {
            changeIds = await _pInvestments.GetChangeDataUsingBudgetPhaseAsync(budgetPhaseId, userId, showOnlyModified, budgetYear);
        }

        //#endregion Fetch changeIds
        List<clsBudgetFormHelper> data2B;
        List<clsBudgetFormHelper> data;
        if (changeIds.Any())
        {
            data2B = (from a in (await Get2BDataFromDBAsync(budgetYear, userId, docType, forecastPeriod, fridimData, budgetPhaseIDs, freedim2Ids))
                where changeIds.Contains(a.fk_change_id)
                group a by new { aggregate_id = a.aggregate_id.Trim(), aggregate_name = a.aggregate_name.Trim() } into grp
                select new clsBudgetFormHelper()
                {
                    LineGroupId = grp.Key.aggregate_id,
                    LineGroup = grp.Key.aggregate_name,
                    id = grp.Key.aggregate_id,
                    budgetFormDesc = grp.Key.aggregate_name,
                    actualAmtYear = grp.Sum(x => x.actual_amt_year),
                    revisedBudAmtYear = grp.Sum(x => x.revised_bud_amt_year),
                    orgBudAmtYear = grp.Sum(x => x.org_bud_amt_year),
                    actualAmtLastYear = grp.Sum(x => x.actual_amt_last_year),
                    orgBudAmtLastYear = grp.Sum(x => x.org_bud_amt_last_year),
                    revisedAmtLastYear = grp.Sum(x => x.revised_bud_amt_last_year),
                    revisedAmtYearByAuth = grp.Sum(x => x.revised_bud_auth_year),
                    revisedAmtLastYearByAuth = grp.Sum(x => x.revised_bud_auth_last_year),
                    finPlanYear1 = grp.Sum(x => x.finplan_year_1_amount),
                    finPlanYear2 = grp.Sum(x => x.finplan_year_2_amount),
                    finPlanYear3 = grp.Sum(x => x.finplan_year_3_amount),
                    finPlanYear4 = grp.Sum(x => x.finplan_year_4_amount),
                    finPlanYear5 = grp.Sum(x => x.finplan_year_5_amount),
                    finPlanYear6 = grp.Sum(x => x.finplan_year_6_amount),
                    finPlanYear7 = grp.Sum(x => x.finplan_year_7_amount),
                    finPlanYear8 = grp.Sum(x => x.finplan_year_8_amount),
                    finPlanYear9 = grp.Sum(x => x.finplan_year_9_amount),
                    finPlanYear10 = grp.Sum(x => x.finplan_year_10_amount),
                    finPlanYear11 = grp.Sum(x => x.finplan_year_11_amount),
                    finPlanYear12 = grp.Sum(x => x.finplan_year_12_amount),
                    finPlanYear13 = grp.Sum(x => x.finplan_year_13_amount),
                    finPlanYear14 = grp.Sum(x => x.finplan_year_14_amount),
                    finPlanYear15 = grp.Sum(x => x.finplan_year_15_amount),
                    finPlanYear16 = grp.Sum(x => x.finplan_year_16_amount),
                    finPlanYear17 = grp.Sum(x => x.finplan_year_17_amount),
                    finPlanYear18 = grp.Sum(x => x.finplan_year_18_amount),
                    finPlanYear19 = grp.Sum(x => x.finplan_year_19_amount),
                    finPlanYear20 = grp.Sum(x => x.finplan_year_20_amount),
                    forecastAmount = grp.Sum(x => x.forecast_amount),
                    deviationForecast = grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount),
                    deviationForecastPct = grp.Sum(x => x.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount)) / grp.Sum(x => x.revised_bud_amt_year)) * 100 : 0,
                    budgetChange = grp.Sum(x => x.budget_change.Value),
                    numberFormat = "n0",
                    semi = false,
                    clickable = true,
                    isSubHeading = false,
                    budChangeYear1 = grp.Sum(x => x.bud_change_y1),
                    budChangeYear2 = grp.Sum(x => x.bud_change_y2),
                    budChangeYear3 = grp.Sum(x => x.bud_change_y3),
                    budChangeYear4 = grp.Sum(x => x.bud_change_y4),
                    budChangeYearTotal = grp.Sum(x => x.bud_change_y1) + grp.Sum(x => x.bud_change_y2) + grp.Sum(x => x.bud_change_y3) + grp.Sum(x => x.bud_change_y4),
                    deviation = grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.actual_amt_year),
                    deviationPct = grp.Sum(x => x.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.actual_amt_year)) / grp.Sum(x => x.revised_bud_amt_year)) * 100 : 0,
                    polDeviation = grp.Sum(x => x.revised_bud_auth_year) - grp.Sum(x => x.actual_amt_year),
                    polDeviationPct = grp.Sum(x => x.revised_bud_auth_year) != 0 ? ((grp.Sum(x => x.revised_bud_auth_year) - grp.Sum(x => x.actual_amt_year)) / grp.Sum(x => x.revised_bud_auth_year)) * 100 : 0,
                }).OrderBy(x => x.id).ThenBy(x => x.budgetFormDesc).ToList();
        }
        else
        {
            data2B = (from a in (await Get2BDataFromDBAsync(budgetYear, userId, docType, forecastPeriod, fridimData, budgetPhaseIDs, freedim2Ids))
                group a by new { aggregate_id = a.aggregate_id.Trim(), aggregate_name = a.aggregate_name.Trim() } into grp
                select new clsBudgetFormHelper()
                {
                    LineGroupId = grp.Key.aggregate_id,
                    LineGroup = grp.Key.aggregate_name,
                    id = grp.Key.aggregate_id,
                    budgetFormDesc = grp.Key.aggregate_name,
                    actualAmtYear = grp.Sum(x => x.actual_amt_year),
                    revisedBudAmtYear = grp.Sum(x => x.revised_bud_amt_year),
                    orgBudAmtYear = grp.Sum(x => x.org_bud_amt_year),
                    actualAmtLastYear = grp.Sum(x => x.actual_amt_last_year),
                    orgBudAmtLastYear = grp.Sum(x => x.org_bud_amt_last_year),
                    revisedAmtLastYear = grp.Sum(x => x.revised_bud_amt_last_year),
                    revisedAmtYearByAuth = grp.Sum(x => x.revised_bud_auth_year),
                    revisedAmtLastYearByAuth = grp.Sum(x => x.revised_bud_auth_last_year),
                    finPlanYear1 = grp.Sum(x => x.finplan_year_1_amount),
                    finPlanYear2 = grp.Sum(x => x.finplan_year_2_amount),
                    finPlanYear3 = grp.Sum(x => x.finplan_year_3_amount),
                    finPlanYear4 = grp.Sum(x => x.finplan_year_4_amount),
                    finPlanYear5 = grp.Sum(x => x.finplan_year_5_amount),
                    finPlanYear6 = grp.Sum(x => x.finplan_year_6_amount),
                    finPlanYear7 = grp.Sum(x => x.finplan_year_7_amount),
                    finPlanYear8 = grp.Sum(x => x.finplan_year_8_amount),
                    finPlanYear9 = grp.Sum(x => x.finplan_year_9_amount),
                    finPlanYear10 = grp.Sum(x => x.finplan_year_10_amount),
                    finPlanYear11 = grp.Sum(x => x.finplan_year_11_amount),
                    finPlanYear12 = grp.Sum(x => x.finplan_year_12_amount),
                    finPlanYear13 = grp.Sum(x => x.finplan_year_13_amount),
                    finPlanYear14 = grp.Sum(x => x.finplan_year_14_amount),
                    finPlanYear15 = grp.Sum(x => x.finplan_year_15_amount),
                    finPlanYear16 = grp.Sum(x => x.finplan_year_16_amount),
                    finPlanYear17 = grp.Sum(x => x.finplan_year_17_amount),
                    finPlanYear18 = grp.Sum(x => x.finplan_year_18_amount),
                    finPlanYear19 = grp.Sum(x => x.finplan_year_19_amount),
                    finPlanYear20 = grp.Sum(x => x.finplan_year_20_amount),
                    forecastAmount = grp.Sum(x => x.forecast_amount),
                    deviationForecast = grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount),
                    deviationForecastPct = grp.Sum(x => x.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount)) / grp.Sum(x => x.revised_bud_amt_year)) * 100 : 0,
                    budgetChange = grp.Sum(x => x.budget_change.Value),
                    numberFormat = "n0",
                    semi = false,
                    clickable = true,
                    isSubHeading = false,
                    budChangeYear1 = grp.Sum(x => x.bud_change_y1),
                    budChangeYear2 = grp.Sum(x => x.bud_change_y2),
                    budChangeYear3 = grp.Sum(x => x.bud_change_y3),
                    budChangeYear4 = grp.Sum(x => x.bud_change_y4),
                    budChangeYearTotal = grp.Sum(x => x.bud_change_y1) + grp.Sum(x => x.bud_change_y2) + grp.Sum(x => x.bud_change_y3) + grp.Sum(x => x.bud_change_y4),
                    deviation = grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.actual_amt_year),
                    deviationPct = grp.Sum(x => x.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.actual_amt_year)) / grp.Sum(x => x.revised_bud_amt_year)) * 100 : 0,
                    polDeviation = grp.Sum(x => x.revised_bud_auth_year) - grp.Sum(x => x.actual_amt_year),
                    polDeviationPct = grp.Sum(x => x.revised_bud_auth_year) != 0 ? ((grp.Sum(x => x.revised_bud_auth_year) - grp.Sum(x => x.actual_amt_year)) / grp.Sum(x => x.revised_bud_auth_year)) * 100 : 0
                }).OrderBy(x => x.id).ThenBy(x => x.budgetFormDesc).ToList();
        }

        JArray dataArray = new JArray();
        dynamic finalData = new JObject();
        if (changeIds.Any())
        {
            data = (from a in (await Get2ADataFromDBAsync(budgetYear, userId, docType, forecastPeriod, fridimData, budgetPhaseIDs, freedim2Ids))
                where changeIds.Contains(a.fk_change_id)
                group a by new { line_group_id = a.line_group_id.ToString().Trim(), line_group = a.line_group.ToString().Trim(), line_item_id = a.line_item_id.ToString().Trim(), line_item = a.line_item.ToString().Trim() } into grp
                select new clsBudgetFormHelper()
                {
                    LineGroupId = grp.Key.line_group_id.ToString(),
                    LineGroup = grp.Key.line_group,
                    id = grp.Key.line_item_id.ToString(),
                    budgetFormDesc = grp.Key.line_item,
                    actualAmtYear = grp.Sum(x => x.actual_amt_year),
                    revisedBudAmtYear = grp.Sum(x => x.revised_bud_amt_year),
                    orgBudAmtYear = grp.Sum(x => x.org_bud_amt_year),
                    actualAmtLastYear = grp.Sum(x => x.actual_amt_last_year),
                    orgBudAmtLastYear = grp.Sum(x => x.org_bud_amt_last_year),
                    revisedAmtLastYear = grp.Sum(x => x.revised_bud_amt_last_year),
                    revisedAmtYearByAuth = grp.Sum(x => x.revised_bud_auth_year),
                    revisedAmtLastYearByAuth = grp.Sum(x => x.revised_bud_auth_last_year),
                    finPlanYear1 = grp.Sum(x => x.finplan_year_1_amount),
                    finPlanYear2 = grp.Sum(x => x.finplan_year_2_amount),
                    finPlanYear3 = grp.Sum(x => x.finplan_year_3_amount),
                    finPlanYear4 = grp.Sum(x => x.finplan_year_4_amount),
                    finPlanYear5 = grp.Sum(x => x.finplan_year_5_amount),
                    finPlanYear6 = grp.Sum(x => x.finplan_year_6_amount),
                    finPlanYear7 = grp.Sum(x => x.finplan_year_7_amount),
                    finPlanYear8 = grp.Sum(x => x.finplan_year_8_amount),
                    finPlanYear9 = grp.Sum(x => x.finplan_year_9_amount),
                    finPlanYear10 = grp.Sum(x => x.finplan_year_10_amount),
                    finPlanYear11 = grp.Sum(x => x.finplan_year_11_amount),
                    finPlanYear12 = grp.Sum(x => x.finplan_year_12_amount),
                    finPlanYear13 = grp.Sum(x => x.finplan_year_13_amount),
                    finPlanYear14 = grp.Sum(x => x.finplan_year_14_amount),
                    finPlanYear15 = grp.Sum(x => x.finplan_year_15_amount),
                    finPlanYear16 = grp.Sum(x => x.finplan_year_16_amount),
                    finPlanYear17 = grp.Sum(x => x.finplan_year_17_amount),
                    finPlanYear18 = grp.Sum(x => x.finplan_year_18_amount),
                    finPlanYear19 = grp.Sum(x => x.finplan_year_19_amount),
                    finPlanYear20 = grp.Sum(x => x.finplan_year_20_amount),
                    numberFormat = "n0",
                    isSubHeading = false,
                    forecastAmount = grp.Sum(x => x.forecast_amount),
                    deviationForecast = grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount),
                    deviationForecastPct = grp.Sum(x => x.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount)) / grp.Sum(x => x.revised_bud_amt_year)) * 100 : 0,
                    budgetChange = grp.Sum(x => x.budget_change.Value),
                    budChangeYear1 = grp.Sum(x => x.bud_change_y1),
                    budChangeYear2 = grp.Sum(x => x.bud_change_y2),
                    budChangeYear3 = grp.Sum(x => x.bud_change_y3),
                    budChangeYear4 = grp.Sum(x => x.bud_change_y4),
                    budChangeYearTotal = grp.Sum(x => x.bud_change_y1) + grp.Sum(x => x.bud_change_y2) + grp.Sum(x => x.bud_change_y3) + grp.Sum(x => x.bud_change_y4),
                    deviation = grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.actual_amt_year),
                    deviationPct = grp.Sum(x => x.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.actual_amt_year)) / grp.Sum(x => x.revised_bud_amt_year)) * 100 : 0,
                    polDeviation = grp.Sum(x => x.revised_bud_auth_year) - grp.Sum(x => x.actual_amt_year),
                    polDeviationPct = grp.Sum(x => x.revised_bud_auth_year) != 0 ? ((grp.Sum(x => x.revised_bud_auth_year) - grp.Sum(x => x.actual_amt_year)) / grp.Sum(x => x.revised_bud_auth_year)) * 100 : 0,
                }).OrderBy(x => x.LineGroupId).ThenBy(x => x.LineGroup).ThenBy(x => x.id).ThenBy(x => x.budgetFormDesc).ToList();
        }
        else
        {
            data = (from a in (await Get2ADataFromDBAsync(budgetYear, userId, docType, forecastPeriod, fridimData, budgetPhaseIDs, freedim2Ids))
                group a by new { line_group_id = a.line_group_id.ToString().Trim(), line_group = a.line_group.ToString().Trim(), line_item_id = a.line_item_id.ToString().Trim(), line_item = a.line_item.ToString().Trim() } into grp
                select new clsBudgetFormHelper()
                {
                    LineGroupId = grp.Key.line_group_id.ToString(),
                    LineGroup = grp.Key.line_group,
                    id = grp.Key.line_item_id.ToString(),
                    budgetFormDesc = grp.Key.line_item,
                    actualAmtYear = grp.Sum(x => x.actual_amt_year),
                    revisedBudAmtYear = grp.Sum(x => x.revised_bud_amt_year),
                    orgBudAmtYear = grp.Sum(x => x.org_bud_amt_year),
                    actualAmtLastYear = grp.Sum(x => x.actual_amt_last_year),
                    orgBudAmtLastYear = grp.Sum(x => x.org_bud_amt_last_year),
                    revisedAmtLastYear = grp.Sum(x => x.revised_bud_amt_last_year),
                    revisedAmtYearByAuth = grp.Sum(x => x.revised_bud_auth_year),
                    revisedAmtLastYearByAuth = grp.Sum(x => x.revised_bud_auth_last_year),
                    finPlanYear1 = grp.Sum(x => x.finplan_year_1_amount),
                    finPlanYear2 = grp.Sum(x => x.finplan_year_2_amount),
                    finPlanYear3 = grp.Sum(x => x.finplan_year_3_amount),
                    finPlanYear4 = grp.Sum(x => x.finplan_year_4_amount),
                    finPlanYear5 = grp.Sum(x => x.finplan_year_5_amount),
                    finPlanYear6 = grp.Sum(x => x.finplan_year_6_amount),
                    finPlanYear7 = grp.Sum(x => x.finplan_year_7_amount),
                    finPlanYear8 = grp.Sum(x => x.finplan_year_8_amount),
                    finPlanYear9 = grp.Sum(x => x.finplan_year_9_amount),
                    finPlanYear10 = grp.Sum(x => x.finplan_year_10_amount),
                    finPlanYear11 = grp.Sum(x => x.finplan_year_11_amount),
                    finPlanYear12 = grp.Sum(x => x.finplan_year_12_amount),
                    finPlanYear13 = grp.Sum(x => x.finplan_year_13_amount),
                    finPlanYear14 = grp.Sum(x => x.finplan_year_14_amount),
                    finPlanYear15 = grp.Sum(x => x.finplan_year_15_amount),
                    finPlanYear16 = grp.Sum(x => x.finplan_year_16_amount),
                    finPlanYear17 = grp.Sum(x => x.finplan_year_17_amount),
                    finPlanYear18 = grp.Sum(x => x.finplan_year_18_amount),
                    finPlanYear19 = grp.Sum(x => x.finplan_year_19_amount),
                    finPlanYear20 = grp.Sum(x => x.finplan_year_20_amount),
                    numberFormat = "n0",
                    isSubHeading = false,
                    forecastAmount = grp.Sum(x => x.forecast_amount),
                    deviationForecast = grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount),
                    deviationForecastPct = grp.Sum(x => x.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.forecast_amount)) / grp.Sum(x => x.revised_bud_amt_year)) * 100 : 0,
                    budgetChange = grp.Sum(x => x.budget_change.Value),
                    budChangeYear1 = grp.Sum(x => x.bud_change_y1),
                    budChangeYear2 = grp.Sum(x => x.bud_change_y2),
                    budChangeYear3 = grp.Sum(x => x.bud_change_y3),
                    budChangeYear4 = grp.Sum(x => x.bud_change_y4),
                    budChangeYearTotal = grp.Sum(x => x.bud_change_y1) + grp.Sum(x => x.bud_change_y2) + grp.Sum(x => x.bud_change_y3) + grp.Sum(x => x.bud_change_y4),
                    deviation = grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.actual_amt_year),
                    deviationPct = grp.Sum(x => x.revised_bud_amt_year) != 0 ? ((grp.Sum(x => x.revised_bud_amt_year) - grp.Sum(x => x.actual_amt_year)) / grp.Sum(x => x.revised_bud_amt_year)) * 100 : 0,
                    polDeviation = grp.Sum(x => x.revised_bud_auth_year) - grp.Sum(x => x.actual_amt_year),
                    polDeviationPct = grp.Sum(x => x.revised_bud_auth_year) != 0 ? ((grp.Sum(x => x.revised_bud_auth_year) - grp.Sum(x => x.actual_amt_year)) / grp.Sum(x => x.revised_bud_auth_year)) * 100 : 0
                }).OrderBy(x => x.LineGroupId).ThenBy(x => x.LineGroup).ThenBy(x => x.id).ThenBy(x => x.budgetFormDesc).ToList();
        }

        var LineGroupIdList = new List<string>();
        LineGroupIdList = data.Select(x => x.LineGroupId).Distinct().ToList();
        dynamic grpData;
        decimal forAmtPct2bData;
        decimal forAmtPctData;
        decimal deviation2bData;
        decimal deviationData;
        decimal polDeviationData;
        decimal polDeviation2bData;
        foreach (var item in LineGroupIdList)
        {
            foreach (var d in data.Where(x => x.LineGroupId == item).ToList())
            {
                grpData = new JObject();
                grpData.LineGroupId = item;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = d.id;
                grpData.budgetFormDesc = d.budgetFormDesc;
                grpData.actualAmtYear = d.actualAmtYear;
                grpData.revisedBudAmtYear = d.revisedBudAmtYear;
                grpData.orgBudAmtYear = d.orgBudAmtYear;
                grpData.actualAmtLastYear = d.actualAmtLastYear;
                grpData.orgBudAmtLastYear = d.orgBudAmtLastYear;
                grpData.revisedAmtLastYear = d.revisedAmtLastYear;
                grpData.revisedAmtYearByAuth = d.revisedAmtYearByAuth;
                grpData.revisedAmtLastYearByAuth = d.revisedAmtLastYearByAuth;
                grpData.finPlanYear1 = d.finPlanYear1;
                grpData.finPlanYear2 = d.finPlanYear2;
                grpData.finPlanYear3 = d.finPlanYear3;
                grpData.finPlanYear4 = d.finPlanYear4;
                grpData.finPlanYear5 = d.finPlanYear5;
                grpData.finPlanYear6 = d.finPlanYear6;
                grpData.finPlanYear7 = d.finPlanYear7;
                grpData.finPlanYear8 = d.finPlanYear8;
                grpData.finPlanYear9 = d.finPlanYear9;
                grpData.finPlanYear10 = d.finPlanYear10;
                grpData.finPlanYear11 = d.finPlanYear11;
                grpData.finPlanYear12 = d.finPlanYear12;
                grpData.finPlanYear13 = d.finPlanYear13;
                grpData.finPlanYear14 = d.finPlanYear14;
                grpData.finPlanYear15 = d.finPlanYear15;
                grpData.finPlanYear16 = d.finPlanYear16;
                grpData.finPlanYear17 = d.finPlanYear17;
                grpData.finPlanYear18 = d.finPlanYear18;
                grpData.finPlanYear19 = d.finPlanYear19;
                grpData.finPlanYear20 = d.finPlanYear20;
                grpData.budgetChange = d.budgetChange;
                grpData.deviationForecastPct = d.deviationForecastPct;
                grpData.deviationForecast = d.deviationForecast;
                grpData.forecastAmount = d.forecastAmount;
                grpData.semi = false;
                grpData.clickable = true;
                grpData.isSubHeading = false;
                grpData.numberFormat = d.numberFormat;
                grpData.note = commentList.FirstOrDefault(x => x.line_group_id.ToString() == item && x.line_item_id == d.id) != null ? commentList.FirstOrDefault(x => x.line_group_id.ToString() == item && x.line_item_id == d.id).comment : string.Empty;
                grpData.rowType = "DetailRow";
                grpData.IsSecondGrid = false;
                grpData.budChangeYear1 = d.budChangeYear1;
                grpData.budChangeYear2 = d.budChangeYear2;
                grpData.budChangeYear3 = d.budChangeYear3;
                grpData.budChangeYear4 = d.budChangeYear4;
                grpData.budChangeYearTotal = d.budChangeYearTotal;
                grpData.deviation = d.deviation;
                grpData.deviationPct = d.deviationPct;
                grpData.polDeviation = d.polDeviation;
                grpData.polDeviationPct = d.polDeviationPct;
                dataArray.Add(grpData);
            }
            if (item == "4000000")
            {
                //sum row 1
                grpData = new JObject();
                grpData.LineGroupId = item;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = -1;
                grpData.budgetFormDesc = langStringValues["BudForm_sum"].LangText + " " + "Til fordeling drift";
                grpData.actualAmtYear = data.Sum(x => x.actualAmtYear);
                grpData.revisedBudAmtYear = data.Sum(x => x.revisedBudAmtYear);
                grpData.orgBudAmtYear = data.Sum(x => x.orgBudAmtYear);
                grpData.actualAmtLastYear = data.Sum(x => x.actualAmtLastYear);
                grpData.orgBudAmtLastYear = data.Sum(x => x.orgBudAmtLastYear);
                grpData.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear);
                grpData.revisedAmtYearByAuth = data.Sum(x => x.revisedAmtYearByAuth);
                grpData.revisedAmtLastYearByAuth = data.Sum(x => x.revisedAmtLastYearByAuth);
                grpData.finPlanYear1 = data.Sum(x => x.finPlanYear1);
                grpData.finPlanYear2 = data.Sum(x => x.finPlanYear2);
                grpData.finPlanYear3 = data.Sum(x => x.finPlanYear3);
                grpData.finPlanYear4 = data.Sum(x => x.finPlanYear4);
                grpData.finPlanYear5 = data.Sum(x => x.finPlanYear5);
                grpData.finPlanYear6 = data.Sum(x => x.finPlanYear6);
                grpData.finPlanYear7 = data.Sum(x => x.finPlanYear7);
                grpData.finPlanYear8 = data.Sum(x => x.finPlanYear8);
                grpData.finPlanYear9 = data.Sum(x => x.finPlanYear9);
                grpData.finPlanYear10 = data.Sum(x => x.finPlanYear10);
                grpData.finPlanYear11 = data.Sum(x => x.finPlanYear11);
                grpData.finPlanYear12 = data.Sum(x => x.finPlanYear12);
                grpData.finPlanYear13 = data.Sum(x => x.finPlanYear13);
                grpData.finPlanYear14 = data.Sum(x => x.finPlanYear14);
                grpData.finPlanYear15 = data.Sum(x => x.finPlanYear15);
                grpData.finPlanYear16 = data.Sum(x => x.finPlanYear16);
                grpData.finPlanYear17 = data.Sum(x => x.finPlanYear17);
                grpData.finPlanYear18 = data.Sum(x => x.finPlanYear18);
                grpData.finPlanYear19 = data.Sum(x => x.finPlanYear19);
                grpData.finPlanYear20 = data.Sum(x => x.finPlanYear20);
                grpData.budgetChange = data.Sum(x => x.budgetChange);
                grpData.deviationForecastPct = data.Sum(x => x.deviationForecastPct);
                grpData.deviationForecast = data.Sum(x => x.deviationForecast);
                grpData.forecastAmount = data.Sum(x => x.forecastAmount);
                grpData.semi = true;
                grpData.clickable = false;
                grpData.isSubHeading = false;
                grpData.numberFormat = "n0";
                grpData.note = null;
                grpData.rowType = "SumRow";
                grpData.IsSecondGrid = false;
                grpData.budChangeYear1 = data.Sum(x => x.budChangeYear1);
                grpData.budChangeYear2 = data.Sum(x => x.budChangeYear2);
                grpData.budChangeYear3 = data.Sum(x => x.budChangeYear3);
                grpData.budChangeYear4 = data.Sum(x => x.budChangeYear4);
                grpData.budChangeYearTotal = data.Sum(x => x.budChangeYear1) + data.Sum(x => x.budChangeYear2) + data.Sum(x => x.budChangeYear3) + data.Sum(x => x.budChangeYear4);
                grpData.deviation = data.Sum(x => x.deviation);
                grpData.deviationPct = data.Sum(x => x.deviationPct);
                grpData.polDeviation = data.Sum(x => x.polDeviation);
                grpData.polDeviationPct = data.Sum(x=> x.polDeviationPct);
                dataArray.Add(grpData);

                //sum row 2b
                grpData = new JObject();
                grpData.LineGroupId = item;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = -1;
                grpData.budgetFormDesc = langStringValues["BudForm_sum"].LangText + " " + "fordelt til drift (fra skjema 2B)";
                grpData.actualAmtYear = data2B.Sum(x => x.actualAmtYear);
                grpData.revisedBudAmtYear = data2B.Sum(x => x.revisedBudAmtYear);
                grpData.orgBudAmtYear = data2B.Sum(x => x.orgBudAmtYear);
                grpData.actualAmtLastYear = data2B.Sum(x => x.actualAmtLastYear);
                grpData.orgBudAmtLastYear = data2B.Sum(x => x.orgBudAmtLastYear);
                grpData.revisedAmtLastYear = data2B.Sum(x => x.revisedAmtLastYear);
                grpData.revisedAmtYearByAuth = data2B.Sum(x => x.revisedAmtYearByAuth);
                grpData.revisedAmtLastYearByAuth = data2B.Sum(x => x.revisedAmtLastYearByAuth);
                grpData.finPlanYear1 = data2B.Sum(x => x.finPlanYear1);
                grpData.finPlanYear2 = data2B.Sum(x => x.finPlanYear2);
                grpData.finPlanYear3 = data2B.Sum(x => x.finPlanYear3);
                grpData.finPlanYear4 = data2B.Sum(x => x.finPlanYear4);
                grpData.finPlanYear5 = data2B.Sum(x => x.finPlanYear5);
                grpData.finPlanYear6 = data2B.Sum(x => x.finPlanYear6);
                grpData.finPlanYear7 = data2B.Sum(x => x.finPlanYear7);
                grpData.finPlanYear8 = data2B.Sum(x => x.finPlanYear8);
                grpData.finPlanYear9 = data2B.Sum(x => x.finPlanYear9);
                grpData.finPlanYear10 = data2B.Sum(x => x.finPlanYear10);
                grpData.finPlanYear11 = data2B.Sum(x => x.finPlanYear11);
                grpData.finPlanYear12 = data2B.Sum(x => x.finPlanYear12);
                grpData.finPlanYear13 = data2B.Sum(x => x.finPlanYear13);
                grpData.finPlanYear14 = data2B.Sum(x => x.finPlanYear14);
                grpData.finPlanYear15 = data2B.Sum(x => x.finPlanYear15);
                grpData.finPlanYear16 = data2B.Sum(x => x.finPlanYear16);
                grpData.finPlanYear17 = data2B.Sum(x => x.finPlanYear17);
                grpData.finPlanYear18 = data2B.Sum(x => x.finPlanYear18);
                grpData.finPlanYear19 = data2B.Sum(x => x.finPlanYear19);
                grpData.finPlanYear20 = data2B.Sum(x => x.finPlanYear20);
                grpData.budgetChange = data2B.Sum(x => x.budgetChange);
                grpData.budChangeYear1 = data2B.Sum(x => x.budChangeYear1);
                grpData.budChangeYear2 = data2B.Sum(x => x.budChangeYear2);
                grpData.budChangeYear3 = data2B.Sum(x => x.budChangeYear3);
                grpData.budChangeYear4 = data2B.Sum(x => x.budChangeYear4);
                grpData.budChangeYearTotal = data2B.Sum(x => x.budChangeYear1) + data2B.Sum(x => x.budChangeYear2) + data2B.Sum(x => x.budChangeYear3) + data2B.Sum(x => x.budChangeYear4);
                forAmtPct2bData = data2B.Sum(x => x.revisedBudAmtYear) != 0 ? (((data2B.Sum(x => x.revisedBudAmtYear) - data2B.Sum(x => x.forecastAmount)) / data2B.Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
                grpData.deviationForecastPct = forAmtPct2bData;

                // grpData.deviationForecastPct = data2B.Sum(x => x.deviationForecastPct);
                grpData.deviationForecast = data2B.Sum(x => x.deviationForecast);
                grpData.forecastAmount = data2B.Sum(x => x.forecastAmount);
                grpData.deviation = data2B.Sum(x => x.deviation);
                grpData.deviationPct = data2B.Sum(x => x.revisedBudAmtYear) != 0 ? ((data2B.Sum(x => x.revisedBudAmtYear) - data2B.Sum(x => x.actualAmtYear)) / data2B.Sum(x => x.revisedBudAmtYear)) * 100 : 0;
                grpData.polDeviation = data2B.Sum(x => x.polDeviation);
                grpData.polDeviationPct = data2B.Sum(x => x.revisedAmtYearByAuth) != 0 ? ((data2B.Sum(x => x.revisedAmtYearByAuth) - data2B.Sum(x => x.actualAmtYear)) / data2B.Sum(x => x.revisedAmtYearByAuth)) * 100 : 0;
                grpData.semi = true;
                grpData.clickable = false;
                grpData.isSubHeading = false;
                grpData.numberFormat = "n0";
                grpData.note = null;
                grpData.rowType = "SumRow";
                grpData.IsSecondGrid = false;
                dataArray.Add(grpData);

                //sum total
                grpData = new JObject();
                grpData.LineGroupId = item;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = -1;
                grpData.budgetFormDesc = langStringValues["BudForm_sum"].LangText + " " + data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.actualAmtYear = data.Sum(x => x.actualAmtYear) + data2B.Sum(x => x.actualAmtYear);
                grpData.revisedBudAmtYear = data.Sum(x => x.revisedBudAmtYear) + data2B.Sum(x => x.revisedBudAmtYear);
                grpData.orgBudAmtYear = data.Sum(x => x.orgBudAmtYear) + data2B.Sum(x => x.orgBudAmtYear);
                grpData.actualAmtLastYear = data.Sum(x => x.actualAmtLastYear) + data2B.Sum(x => x.actualAmtLastYear);
                grpData.orgBudAmtLastYear = data.Sum(x => x.orgBudAmtLastYear) + data2B.Sum(x => x.orgBudAmtLastYear);
                grpData.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear) + data2B.Sum(x => x.revisedAmtLastYear);
                grpData.revisedAmtYearByAuth = data.Sum(x => x.revisedAmtYearByAuth) + data2B.Sum(x => x.revisedAmtYearByAuth);
                grpData.revisedAmtLastYearByAuth = data.Sum(x => x.revisedAmtLastYearByAuth) + data2B.Sum(x => x.revisedAmtLastYearByAuth);
                grpData.finPlanYear1 = data.Sum(x => x.finPlanYear1) + data2B.Sum(x => x.finPlanYear1);
                grpData.finPlanYear2 = data.Sum(x => x.finPlanYear2) + data2B.Sum(x => x.finPlanYear2);
                grpData.finPlanYear3 = data.Sum(x => x.finPlanYear3) + data2B.Sum(x => x.finPlanYear3);
                grpData.finPlanYear4 = data.Sum(x => x.finPlanYear4) + data2B.Sum(x => x.finPlanYear4);
                grpData.finPlanYear5 = data.Sum(x => x.finPlanYear5) + data2B.Sum(x => x.finPlanYear5);
                grpData.finPlanYear6 = data.Sum(x => x.finPlanYear6) + data2B.Sum(x => x.finPlanYear6);
                grpData.finPlanYear7 = data.Sum(x => x.finPlanYear7) + data2B.Sum(x => x.finPlanYear7);
                grpData.finPlanYear8 = data.Sum(x => x.finPlanYear8) + data2B.Sum(x => x.finPlanYear8);
                grpData.finPlanYear9 = data.Sum(x => x.finPlanYear9) + data2B.Sum(x => x.finPlanYear9);
                grpData.finPlanYear10 = data.Sum(x => x.finPlanYear10) + data2B.Sum(x => x.finPlanYear10);
                grpData.finPlanYear11 = data.Sum(x => x.finPlanYear11) + data2B.Sum(x => x.finPlanYear11);
                grpData.finPlanYear12 = data.Sum(x => x.finPlanYear12) + data2B.Sum(x => x.finPlanYear12);
                grpData.finPlanYear13 = data.Sum(x => x.finPlanYear13) + data2B.Sum(x => x.finPlanYear13);
                grpData.finPlanYear14 = data.Sum(x => x.finPlanYear14) + data2B.Sum(x => x.finPlanYear14);
                grpData.finPlanYear15 = data.Sum(x => x.finPlanYear15) + data2B.Sum(x => x.finPlanYear15);
                grpData.finPlanYear16 = data.Sum(x => x.finPlanYear16) + data2B.Sum(x => x.finPlanYear16);
                grpData.finPlanYear17 = data.Sum(x => x.finPlanYear17) + data2B.Sum(x => x.finPlanYear17);
                grpData.finPlanYear18 = data.Sum(x => x.finPlanYear18) + data2B.Sum(x => x.finPlanYear18);
                grpData.finPlanYear19 = data.Sum(x => x.finPlanYear19) + data2B.Sum(x => x.finPlanYear19);
                grpData.finPlanYear20 = data.Sum(x => x.finPlanYear20) + data2B.Sum(x => x.finPlanYear20);
                grpData.budgetChange = data.Sum(x => x.budgetChange) + data2B.Sum(x => x.budgetChange);
                grpData.budChangeYear1 = data.Sum(x => x.budChangeYear1) + data2B.Sum(x => x.budChangeYear1);
                grpData.budChangeYear2 = data.Sum(x => x.budChangeYear2) + data2B.Sum(x => x.budChangeYear2);
                grpData.budChangeYear3 = data.Sum(x => x.budChangeYear3) + data2B.Sum(x => x.budChangeYear3);
                grpData.budChangeYear4 = data.Sum(x => x.budChangeYear4) + data2B.Sum(x => x.budChangeYear4);
                grpData.budChangeYearTotal = data.Sum(x => x.budChangeYear1) + data2B.Sum(x => x.budChangeYear1) + data.Sum(x => x.budChangeYear2) + data2B.Sum(x => x.budChangeYear2)
                                             + data.Sum(x => x.budChangeYear3) + data2B.Sum(x => x.budChangeYear3) + data.Sum(x => x.budChangeYear4) + data2B.Sum(x => x.budChangeYear4);
                forAmtPct2bData = data2B.Sum(x => x.revisedBudAmtYear) != 0 ? (((data2B.Sum(x => x.revisedBudAmtYear) - data2B.Sum(x => x.forecastAmount)) / data2B.Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
                forAmtPctData = data.Sum(x => x.revisedBudAmtYear) != 0 ? (((data.Sum(x => x.revisedBudAmtYear) - data.Sum(x => x.forecastAmount)) / data.Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
                grpData.deviationForecastPct = forAmtPctData + forAmtPct2bData;

                // grpData.deviationForecastPct = data.Sum(x => x.deviationForecastPct)+ data2B.Sum(x => x.deviationForecastPct);
                grpData.deviationForecast = data.Sum(x => x.deviationForecast) + data2B.Sum(x => x.deviationForecast);
                polDeviation2bData = data2B.Sum(x => x.revisedAmtYearByAuth) != 0 ? (((data2B.Sum(x => x.revisedAmtYearByAuth) - data2B.Sum(x => x.actualAmtYear)) / data2B.Sum(x => x.revisedAmtYearByAuth)) * 100).Value : 0;
                polDeviationData = data.Sum(x => x.revisedAmtYearByAuth) != 0 ? (((data.Sum(x => x.revisedAmtYearByAuth) - data.Sum(x => x.actualAmtYear)) / data.Sum(x => x.revisedAmtYearByAuth)) * 100).Value : 0;

                grpData.polDeviation = data.Sum(x => x.polDeviation) + data2B.Sum(x => x.polDeviation);
                grpData.polDeviationPct = polDeviationData + polDeviation2bData;
                grpData.forecastAmount = data.Sum(x => x.forecastAmount) + data2B.Sum(x => x.forecastAmount);
                grpData.deviation = data.Sum(x => x.deviation) + data2B.Sum(x => x.deviation);
                deviation2bData = data2B.Sum(x => x.revisedBudAmtYear) != 0 ? (((data2B.Sum(x => x.revisedBudAmtYear) - data2B.Sum(x => x.actualAmtYear)) / data2B.Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
                deviationData = data.Sum(x => x.revisedBudAmtYear) != 0 ? (((data.Sum(x => x.revisedBudAmtYear) - data.Sum(x => x.actualAmtYear)) / data.Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
                grpData.deviationPct = deviationData + deviation2bData;
                grpData.semi = true;
                grpData.clickable = false;
                grpData.isSubHeading = false;
                grpData.numberFormat = "n0";
                grpData.note = null;
                grpData.rowType = "SumRow";
                grpData.IsSecondGrid = false;
                dataArray.Add(grpData);
            }
            else
            {
                //sum row
                grpData = new JObject();
                grpData.LineGroupId = -1;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = -1;
                grpData.budgetFormDesc = /*langStringValues["BudForm_sum"].LangText + " " +*/ data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;// remove sum text #45351
                grpData.actualAmtYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.actualAmtYear);
                grpData.revisedBudAmtYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.revisedBudAmtYear);
                grpData.orgBudAmtYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.orgBudAmtYear);
                grpData.actualAmtLastYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.actualAmtLastYear);
                grpData.orgBudAmtLastYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.orgBudAmtLastYear);
                grpData.revisedAmtLastYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.revisedAmtLastYear);
                grpData.revisedAmtYearByAuth = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.revisedAmtYearByAuth);
                grpData.revisedAmtLastYearByAuth = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.revisedAmtLastYearByAuth);
                grpData.finPlanYear1 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear1);
                grpData.finPlanYear2 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear2);
                grpData.finPlanYear3 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear3);
                grpData.finPlanYear4 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear4);
                grpData.finPlanYear5 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear5);
                grpData.finPlanYear6 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear6);
                grpData.finPlanYear7 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear7);
                grpData.finPlanYear8 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear8);
                grpData.finPlanYear9 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear9);
                grpData.finPlanYear10 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear10);
                grpData.finPlanYear11 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear11);
                grpData.finPlanYear12 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear12);
                grpData.finPlanYear13 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear13);
                grpData.finPlanYear14 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear14);
                grpData.finPlanYear15 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear15);
                grpData.finPlanYear16 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear16);
                grpData.finPlanYear17 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear17);
                grpData.finPlanYear18 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear18);
                grpData.finPlanYear19 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear19);
                grpData.finPlanYear20 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear20);
                grpData.budgetChange = data.Where(x => x.LineGroupId == item).Sum(x => x.budgetChange);

                forAmtPctData = data.Where(x => x.LineGroupId == item).Sum(x => x.revisedBudAmtYear) != 0 ? (((data.Where(x => x.LineGroupId == item).Sum(x => x.revisedBudAmtYear) - data.Where(x => x.LineGroupId == item).Sum(x => x.forecastAmount)) / data.Where(x => x.LineGroupId == item).Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
                grpData.deviationForecastPct = forAmtPctData;

                // grpData.deviationForecastPct = data.Where(x => x.LineGroupId == item).Sum(x => x.deviationForecastPct);
                grpData.deviationForecast = data.Where(x => x.LineGroupId == item).Sum(x => x.deviationForecast);
                grpData.forecastAmount = data.Where(x => x.LineGroupId == item).Sum(x => x.forecastAmount);
                grpData.deviation = data.Where(x => x.LineGroupId == item).Sum(x => x.deviation);
                deviationData = data.Where(x => x.LineGroupId == item).Sum(x => x.revisedBudAmtYear) != 0 ? (((data.Where(x => x.LineGroupId == item).Sum(x => x.revisedBudAmtYear) - data.Where(x => x.LineGroupId == item).Sum(x => x.actualAmtYear)) / data.Where(x => x.LineGroupId == item).Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
                grpData.deviationPct = deviationData;
                grpData.polDeviation =  data.Where(x => x.LineGroupId == item).Sum(x => x.polDeviation);
                grpData.polDeviationPct = data.Where(x => x.LineGroupId == item).Sum(x => x.revisedAmtYearByAuth) != 0 ? (((data.Where(x => x.LineGroupId == item).Sum(x => x.revisedAmtYearByAuth) - data.Where(x => x.LineGroupId == item).Sum(x => x.actualAmtYear)) / data.Where(x => x.LineGroupId == item).Sum(x => x.revisedAmtYearByAuth)) * 100).Value : 0;
                grpData.semi = true;
                grpData.clickable = false;
                grpData.isSubHeading = false;   
                grpData.numberFormat = "n0";
                grpData.note = null;
                grpData.rowType = "SumRow";
                grpData.IsSecondGrid = false;
                grpData.budChangeYear1 = data.Where(x => x.LineGroupId == item).AsEnumerable().Sum(x => x.budChangeYear1);
                grpData.budChangeYear2 = data.Where(x => x.LineGroupId == item).AsEnumerable().Sum(x => x.budChangeYear2);
                grpData.budChangeYear3 = data.Where(x => x.LineGroupId == item).AsEnumerable().Sum(x => x.budChangeYear3);
                grpData.budChangeYear4 = data.Where(x => x.LineGroupId == item).AsEnumerable().Sum(x => x.budChangeYear4);
                grpData.budChangeYearTotal = data.Where(x => x.LineGroupId == item).AsEnumerable().Sum(x => x.budChangeYear1) + data.Where(x => x.LineGroupId == item).AsEnumerable().Sum(x => x.budChangeYear2)
                    + data.Where(x => x.LineGroupId == item).AsEnumerable().Sum(x => x.budChangeYear3) + data.Where(x => x.LineGroupId == item).AsEnumerable().Sum(x => x.budChangeYear4);
                dataArray.Add(grpData);
            }

            //empty row
            if (!onlyData)
            {
                grpData = new JObject();
                grpData.LineGroupId = null;
                grpData.LineGroup = null;
                grpData.id = null;
                grpData.budgetFormDesc = "";
                grpData.actualAmtYear = "";
                grpData.revisedBudAmtYear = "";
                grpData.orgBudAmtYear = "";
                grpData.actualAmtLastYear = "";
                grpData.orgBudAmtLastYear = "";
                grpData.revisedAmtLastYear = "";
                grpData.revisedAmtYearByAuth = "";
                grpData.revisedAmtLastYearByAuth = "";
                grpData.finPlanYear1 = "";
                grpData.finPlanYear2 = "";
                grpData.finPlanYear3 = "";
                grpData.finPlanYear4 = "";
                grpData.finPlanYear5 = "";
                grpData.finPlanYear6 = "";
                grpData.finPlanYear7 = "";
                grpData.finPlanYear8 = "";
                grpData.finPlanYear9 = "";
                grpData.finPlanYear10 = "";
                grpData.finPlanYear11 = "";
                grpData.finPlanYear12 = "";
                grpData.finPlanYear13 = "";
                grpData.finPlanYear14 = "";
                grpData.finPlanYear15 = "";
                grpData.finPlanYear16 = "";
                grpData.finPlanYear17 = "";
                grpData.finPlanYear18 = "";
                grpData.finPlanYear19 = "";
                grpData.finPlanYear20 = "";
                grpData.budgetChange = "";
                grpData.deviationForecastPct = "";
                grpData.deviationForecast = "";
                grpData.forecastAmount = "";
                grpData.deviation = "";
                grpData.deviationPct = "";
                grpData.semi = true;
                grpData.clickable = false;
                grpData.isSubHeading = false;
                grpData.numberFormat = "Text";
                grpData.note = null;
                grpData.rowType = "DetailRow";
                grpData.IsSecondGrid = false;
                grpData.budChangeYear1 = "";
                grpData.budChangeYear2 = "";
                grpData.budChangeYear3 = "";
                grpData.budChangeYear4 = "";
                grpData.budChangeYearTotal = "";
                grpData.polDeviation = "";
                grpData.polDeviationPct = "";
                dataArray.Add(grpData);
            }
        }

        //final total row
        //sum row
        grpData = new JObject();
        grpData.LineGroupId = -1;
        grpData.LineGroup = "";
        grpData.id = -1;
        grpData.budgetFormDesc = langStringValues["BudForm_fin_sum_2A"].LangText;
        grpData.actualAmtYear = data.Sum(x => x.actualAmtYear);
        grpData.revisedBudAmtYear = data.Sum(x => x.revisedBudAmtYear);
        grpData.orgBudAmtYear = data.Sum(x => x.orgBudAmtYear);
        grpData.actualAmtLastYear = data.Sum(x => x.actualAmtLastYear);
        grpData.orgBudAmtLastYear = data.Sum(x => x.orgBudAmtLastYear);
        grpData.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear);
        grpData.revisedAmtYearByAuth = data.Sum(x => x.revisedAmtYearByAuth);
        grpData.revisedAmtLastYearByAuth = data.Sum(x => x.revisedAmtLastYearByAuth);
        grpData.finPlanYear1 = data.Sum(x => x.finPlanYear1);
        grpData.finPlanYear2 = data.Sum(x => x.finPlanYear2);
        grpData.finPlanYear3 = data.Sum(x => x.finPlanYear3);
        grpData.finPlanYear4 = data.Sum(x => x.finPlanYear4);
        grpData.finPlanYear5 = data.Sum(x => x.finPlanYear5);
        grpData.finPlanYear6 = data.Sum(x => x.finPlanYear6);
        grpData.finPlanYear7 = data.Sum(x => x.finPlanYear7);
        grpData.finPlanYear8 = data.Sum(x => x.finPlanYear8);
        grpData.finPlanYear9 = data.Sum(x => x.finPlanYear9);
        grpData.finPlanYear10 = data.Sum(x => x.finPlanYear10);
        grpData.finPlanYear11 = data.Sum(x => x.finPlanYear11);
        grpData.finPlanYear12 = data.Sum(x => x.finPlanYear12);
        grpData.finPlanYear13 = data.Sum(x => x.finPlanYear13);
        grpData.finPlanYear14 = data.Sum(x => x.finPlanYear14);
        grpData.finPlanYear15 = data.Sum(x => x.finPlanYear15);
        grpData.finPlanYear16 = data.Sum(x => x.finPlanYear16);
        grpData.finPlanYear17 = data.Sum(x => x.finPlanYear17);
        grpData.finPlanYear18 = data.Sum(x => x.finPlanYear18);
        grpData.finPlanYear19 = data.Sum(x => x.finPlanYear19);
        grpData.finPlanYear20 = data.Sum(x => x.finPlanYear20);
        grpData.budgetChange = data.Sum(x => x.budgetChange);

        forAmtPctData = data.Sum(x => x.revisedBudAmtYear) != 0 ? (((data.Sum(x => x.revisedBudAmtYear) - data.Sum(x => x.forecastAmount)) / data.Sum(x => x.revisedBudAmtYear)) * 100).Value : 0;
        grpData.deviationForecastPct = forAmtPctData;
        //grpData.deviationForecastPct = data.Sum(x => x.deviationForecastPct);
        grpData.deviationForecast = data.Sum(x => x.deviationForecast);
        grpData.forecastAmount = data.Sum(x => x.forecastAmount);
        grpData.deviation = data.Sum(x => x.deviation);
        grpData.deviationPct = data.Sum(x => x.revisedBudAmtYear) != 0 ? (((data.Sum(x => x.revisedBudAmtYear) - data.Sum(x => x.actualAmtYear)) / data.Sum(x => x.revisedBudAmtYear)) * 100) : 0;
        grpData.polDeviation = data.Sum(x => x.polDeviation); ;
        grpData.polDeviationPct = data.Sum(x => x.revisedAmtYearByAuth) != 0 ? (((data.Sum(x => x.revisedAmtYearByAuth) - data.Sum(x => x.actualAmtYear)) / data.Sum(x => x.revisedAmtYearByAuth)) * 100) : 0;
        grpData.semi = true;
        grpData.clickable = false;
        grpData.isSubHeading = false;
        grpData.numberFormat = "n0";
        grpData.note = null;
        grpData.rowType = "SumRow";
        grpData.IsSecondGrid = false;
        grpData.budChangeYear1 = data.Sum(x => x.budChangeYear1);
        grpData.budChangeYear2 = data.Sum(x => x.budChangeYear2);
        grpData.budChangeYear3 = data.Sum(x => x.budChangeYear3);
        grpData.budChangeYear4 = data.Sum(x => x.budChangeYear4);
        grpData.budChangeYearTotal = data.Sum(x => x.budChangeYear1) + data.Sum(x => x.budChangeYear2) + data.Sum(x => x.budChangeYear3) + data.Sum(x => x.budChangeYear4);
        dataArray.Add(grpData);

        finalData.data = Get2AGroupingData(dataArray, yearColumnsSelection, groupingYearSelection, budgetYear);
        if (!onlyData)
        {
            finalData.columns = await Get2AGroupingYearColumnsAsync(budgetYear, userId, "2A", docType, forecastPeriod, yearColumnsSelection, groupingYearSelection);
            finalData.header = header;
        }
        return finalData;
    }

    private async Task<JArray> Get2AGroupingYearColumnsAsync(int budgetYear, string userId, string tabName, int docType, int forecastPeriod, int yearColumnsSelection, GroupingYear groupingYearSelection)
    {
        JArray columnConfig = new JArray();
        switch (groupingYearSelection)
        {
            case GroupingYear.NoGrouping:
            case GroupingYear.AllYear:
                columnConfig = await GetBudgetFormColumnAsync(budgetYear, userId, tabName, docType, forecastPeriod, yearColumnsSelection);
                break;

            case GroupingYear.FourYear:
            case GroupingYear.FiveYear:
                if (docType == 2 && (tabName == "2A" || tabName == "2B"))
                {
                    columnConfig = await GetBudgetFormColumnForGroupingYearAsync(budgetYear, userId, forecastPeriod, tabName, yearColumnsSelection, Convert.ToInt32(groupingYearSelection));
                }
                break;
        }
        return columnConfig;
    }

    private async Task<JArray> GetBudgetFormColumnForGroupingYearAsync(int budgetYear, string userId, int forecastPeriod, string tabName, int yearColumnsSelection, int groupingYearSelection)
    {
        List<GridColumnHelper> formattedColumns = new List<GridColumnHelper>();
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValues = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetForm");
        budgetYear = forecastPeriod == 0 ? budgetYear : forecastPeriod / 100;
        ColumnStyleHelper csh = new ColumnStyleHelper();
        ColumnStyleHelper cshHeader = new ColumnStyleHelper();
        csh.style = "text-align:left; border-left:none;width:0px";
        cshHeader.style = "text-align:left; border-left:none;width:0px";
        GridColumnHelper columnInfo = new GridColumnHelper
        {
            field = "id",
            title = " ",
            colCount = 0,
            encoded = false,
            hidden = true,
            width = 50,
            attributes = csh,
            headerAttributes = csh
        };
        formattedColumns.Add(columnInfo);

        csh = new ColumnStyleHelper();
        cshHeader = new ColumnStyleHelper();
        csh.style = "text-align:left;white-space:normal; border-left:none;width:300px";
        cshHeader.style = "text-align:left; border-left:none;width:300px";
        columnInfo = new GridColumnHelper
        {
            field = "budgetFormDesc",
            title = " ",
            colCount = 0,
            encoded = false,
            hidden = false,
            width = 300,
            attributes = csh,
            headerAttributes = csh,
            locked = true,
            template = "#if(budgetFormDesc !== ''){ if(semi) { # <span class='semi'>#= budgetFormDesc #</span> #} else {# #= budgetFormDesc # #}}else{# <span style='display:block;height:18px;'> </span> #}#"
        };
        formattedColumns.Add(columnInfo);

        int noofGroups = yearColumnsSelection / groupingYearSelection;
        int remainingColumns = yearColumnsSelection % groupingYearSelection;
        csh = new ColumnStyleHelper();
        cshHeader = new ColumnStyleHelper();
        csh.style = "text-align:right;white-space:normal;border-left: none;width:100px;";
        cshHeader.style = "text-align:right;border-left: none;width:100px;";
        for (int i = 1; i <= noofGroups; i++)
        {
            string columnFieldName = "finPlanYear" + i;
            columnInfo = new GridColumnHelper
            {
                field = "finPlanYear" + i,
                title = langStringValues["BudForm_finPlanYear"].LangText + " " + (budgetYear) + "-" + (budgetYear + groupingYearSelection - 1),
                colCount = 0,
                encoded = false,
                hidden = false,
                width = 120,
                attributes = csh,
                headerAttributes = csh,
                format = "{0:n0}",
                template = tabName == "1B" ? "#if(numberFormat!='Text' && finPlanYear" + i + " !== ''){ if(clickable) { if(semi) { # <span class='semi'>#= kendo.toString(parseFloat(finPlanYear" + i + "), numberFormat)#</span> # } else { # <span>#= kendo.toString(parseFloat(finPlanYear" + i + "), numberFormat)#</span> # } } else { if(semi) { # <span class='semi'> #=kendo.toString(parseFloat(finPlanYear" + i + "), numberFormat)# </span> #} else { # <span>#=kendo.toString(parseFloat(finPlanYear" + i + "), numberFormat)# </span> #} } } else { # <span style='display:block;height:18px;'> </span> # }#" : "#if(numberFormat!='Text' && finPlanYear" + i + " !== ''){ if(clickable) { if(semi) { # <span class='semi'>#= kendo.toString(parseFloat(finPlanYear" + i + "), numberFormat)#</span> # } else { # <span>#= kendo.toString(parseFloat(finPlanYear" + i + "), numberFormat)#</span> # } } else { if(semi) { # <span class='semi'> #=kendo.toString(parseFloat(finPlanYear" + i + "), numberFormat)# </span> #} else { # <span>#=kendo.toString(parseFloat(finPlanYear" + i + "), numberFormat)# </span> #} } } else { # <span style='display:block;height:18px;'> </span> # }#"
            };
            formattedColumns.Add(columnInfo);
            budgetYear = budgetYear + groupingYearSelection;
        }

        if (remainingColumns > 0)
        {
            columnInfo = new GridColumnHelper
            {
                field = "finPlanYear" + (noofGroups + 1),
                title = langStringValues["BudForm_finPlanYear"].LangText + " " + (budgetYear) + "-" + (budgetYear + remainingColumns - 1),
                colCount = 0,
                encoded = false,
                hidden = false,
                width = 120,
                attributes = csh,
                headerAttributes = csh,
                format = "{0:n0}",
                template = tabName == "1B" ? "#if(numberFormat!='Text' && finPlanYear" + (noofGroups + 1) + " !== ''){ if(clickable) { if(semi) { # <span class='semi'>#= kendo.toString(parseFloat(finPlanYear" + (noofGroups + 1) + "), numberFormat)#</span> # } else { # <span>#= kendo.toString(parseFloat(finPlanYear" + (noofGroups + 1) + "), numberFormat)#</span> # } } else { if(semi) { # <span class='semi'> #=kendo.toString(parseFloat(finPlanYear" + (noofGroups + 1) + "), numberFormat)# </span> #} else { # <span>#=kendo.toString(parseFloat(finPlanYear" + (noofGroups + 1) + "), numberFormat)# </span> #} } } else { # <span style='display:block;height:18px;'> </span> # }#" : "#if(numberFormat!='Text' && finPlanYear" + (noofGroups + 1) + " !== ''){ if(clickable) { if(semi) { # <span class='semi'>#= kendo.toString(parseFloat(finPlanYear" + (noofGroups + 1) + "), numberFormat)#</span> # } else { # <span>#= kendo.toString(parseFloat(finPlanYear" + (noofGroups + 1) + "), numberFormat)#</span> # } } else { if(semi) { # <span class='semi'> #=kendo.toString(parseFloat(finPlanYear" + (noofGroups + 1) + "), numberFormat)# </span> #} else { # <span>#=kendo.toString(parseFloat(finPlanYear" + (noofGroups + 1) + "), numberFormat)# </span> #} } } else { # <span style='display:block;height:18px;'> </span> # }#"
            };
            formattedColumns.Add(columnInfo);                    
                
        }
        csh = new ColumnStyleHelper();
        cshHeader = new ColumnStyleHelper();
        csh.style = "text-align:right;white-space:normal;border-left: none;width:100px;";
        cshHeader.style = "text-align:right;border-left: none;width:100px;";
        columnInfo = new GridColumnHelper
        {
            field = "polDeviation",
            title = langStringValues["BudForm_polDeviation_ui"].LangText,
            colCount = 0,
            encoded = false,
            hidden = false,
            width = 120,
            attributes = csh,
            headerAttributes = csh,
            format = "{0:n0}",
            template = tabName == "1B" ? "#if(numberFormat!='Text' && polDeviation !== ''){ if(clickable) { if(semi) { # <a class='semi' onclick=\"BFPopupData('#= id #','polDeviation','#= polDeviation #','#= gridValue #')\">#= kendo.toString(parseFloat(polDeviation), numberFormat)#</a> # } else { # <a onclick=\"BFPopupData('#= id #','polDeviation','#= polDeviation #','#= gridValue #')\">#= kendo.toString(parseFloat(polDeviation), numberFormat)#</a> # } } else { if(semi) { # <span class='semi'> #=kendo.toString(parseFloat(polDeviation), numberFormat)# </span> #} else { # <span>#=kendo.toString(parseFloat(polDeviation), numberFormat)# </span> #} } } else { # <span style='display:block;height:18px;'> </span> # }#" : "#if(numberFormat!='Text' && polDeviation !== ''){ if(clickable) { if(semi) { # <a class='semi' onclick=\"BFPopupData('#= id #','polDeviation','#= polDeviation #','#=IsSecondGrid#')\">#= kendo.toString(parseFloat(polDeviation), numberFormat)#</a> # } else { # <a onclick=\"BFPopupData('#= id #','polDeviation','#= polDeviation #','#=IsSecondGrid#')\">#= kendo.toString(parseFloat(polDeviation), numberFormat)#</a> # } } else { if(semi) { # <span class='semi'> #=kendo.toString(parseFloat(polDeviation), numberFormat)# </span> #} else { # <span>#=kendo.toString(parseFloat(polDeviation), numberFormat)# </span> #} } } else { # <span style='display:block;height:18px;'> </span> # }#"
        };
        formattedColumns.Add(columnInfo);

        csh = new ColumnStyleHelper();
        cshHeader = new ColumnStyleHelper();
        csh.style = "text-align:left;white-space:normal; border-left:none;width:300px";
        cshHeader.style = "text-align:left; border-left:none;width:300px";
        columnInfo = new GridColumnHelper
        {
            field = "polDeviationPct",
            title = langStringValues["BudForm_polDeviationPct"].LangText,
            colCount = 0,
            encoded = false,
            hidden = false,
            width = 120,
            attributes = csh,
            headerAttributes = csh,
            format = "{0:n2} %",
        };
        formattedColumns.Add(columnInfo);

        csh = new ColumnStyleHelper();
        cshHeader = new ColumnStyleHelper();
        csh.style = "text-align:right;white-space:normal;border-left: none;width:100px;";
        cshHeader.style = "text-align:right;border-left: none;width:100px;";
        columnInfo = new GridColumnHelper
        {
            field = "deviation",
            title = langStringValues["BudForm_deviation"].LangText,
            colCount = 0,
            encoded = false,
            hidden = false,
            width = 120,
            attributes = csh,
            headerAttributes = csh,
            format = "{0:n0}",
            template = tabName == "1B" ? "#if(numberFormat!='Text' && deviation !== ''){ if(clickable) { if(semi) { # <a class='semi' onclick=\"BFPopupData('#= id #','deviation','#= deviation #','#= gridValue #')\">#= kendo.toString(parseFloat(deviation), numberFormat)#</a> # } else { # <a onclick=\"BFPopupData('#= id #','deviation','#= deviation #','#= gridValue #')\">#= kendo.toString(parseFloat(deviation), numberFormat)#</a> # } } else { if(semi) { # <span class='semi'> #=kendo.toString(parseFloat(deviation), numberFormat)# </span> #} else { # <span>#=kendo.toString(parseFloat(deviation), numberFormat)# </span> #} } } else { # <span style='display:block;height:18px;'> </span> # }#" : "#if(numberFormat!='Text' && deviation !== ''){ if(clickable) { if(semi) { # <a class='semi' onclick=\"BFPopupData('#= id #','deviation','#= deviation #','#=IsSecondGrid#')\">#= kendo.toString(parseFloat(deviation), numberFormat)#</a> # } else { # <a onclick=\"BFPopupData('#= id #','deviation','#= deviation #','#=IsSecondGrid#')\">#= kendo.toString(parseFloat(deviation), numberFormat)#</a> # } } else { if(semi) { # <span class='semi'> #=kendo.toString(parseFloat(deviation), numberFormat)# </span> #} else { # <span>#=kendo.toString(parseFloat(deviation), numberFormat)# </span> #} } } else { # <span style='display:block;height:18px;'> </span> # }#"
        };
        formattedColumns.Add(columnInfo);
        csh = new ColumnStyleHelper();
        cshHeader = new ColumnStyleHelper();
        csh.style = "text-align:left;white-space:normal; border-left:none;width:300px";
        cshHeader.style = "text-align:left; border-left:none;width:300px";
        columnInfo = new GridColumnHelper
        {
            field = "deviationPct",
            title = langStringValues["BudForm_deviationPct"].LangText,
            colCount = 0,
            encoded = false,
            hidden = false,
            width = 120,
            attributes = csh,
            headerAttributes = csh,
            format = "{0:n2} %",
        };
        formattedColumns.Add(columnInfo);

        return JArray.FromObject(formattedColumns);
    }

    private JArray Convert2AYearDataToGroupingData(dynamic rptData, int yearColumnsSelection, int groupingYearSelection, int budgetYear)
    {
        dynamic groupYearData;
        JArray dataArray = new JArray();
        int noofGroups = yearColumnsSelection / groupingYearSelection;
        int remainingColumns = yearColumnsSelection % groupingYearSelection;
        foreach (var item in rptData)
        {
            int columnYear = budgetYear;
            groupYearData = new JObject();
            groupYearData.IsSecondGrid = false;
            groupYearData.LineGroup = item.LineGroup;
            groupYearData.LineGroupId = item.LineGroupId;
            groupYearData.accountingYtd = item.accountingYtd;
            groupYearData.actualAmtLastYear = item.actualAmtLastYear;
            groupYearData.actualAmtYear = item.actualAmtYear;
            groupYearData.budgetChange = item.budgetChange;
            groupYearData.budgetFormDesc = item.budgetFormDesc;
            groupYearData.budgetYtd = item.budgetYtd;
            groupYearData.clickable = false; //in grouping always columns shouldn't be clickable
            groupYearData.deviationActionAmount = item.deviationActionAmount;
            groupYearData.deviationAfterAction = item.deviationAfterAction;
            groupYearData.deviationForecast = item.deviationForecast;
            groupYearData.deviationForecastPct = item.deviationForecastPct;
            groupYearData.polDeviation = item.polDeviation;
            groupYearData.polDeviationPct = item.polDeviationPct;
            groupYearData.deviationYtd = item.deviationYtd;
            groupYearData.deviationYtdPct = item.deviationYtdPct;
            groupYearData.forecastAmount = item.forecastAmount;
            groupYearData.forecastInclAction = item.forecastInclAction;
            groupYearData.id = item.id;
            groupYearData.note = item.note;
            groupYearData.numberFormat = item.numberFormat;
            groupYearData.orgBudAmtLastYear = item.orgBudAmtLastYear;
            groupYearData.orgBudAmtYear = item.orgBudAmtYear;
            groupYearData.revisedAmtLastYear = item.revisedAmtLastYear;
            groupYearData.revisedBudAmtYear = item.revisedBudAmtYear;
            groupYearData.revisedAmtYearByAuth = item.revisedAmtYearByAuth;
            groupYearData.revisedAmtLastYearByAuth = item.revisedAmtLastYearByAuth;
            groupYearData.rowType = item.rowType;
            groupYearData.semi = item.semi;
            groupYearData.isSubHeading = item.isSubHeading;

            for (int i = 1; i <= noofGroups; i++)
            {
                List<long> sumOfGroupingYears = new List<long>();
                for (int j = 1; j <= groupingYearSelection; j++)
                {
                    long yearSum = 0;
                    yearSum = item["finPlanYear" + (columnYear - budgetYear + j)].ToString() != "" ? item["finPlanYear" + (columnYear - budgetYear + j)] : 0;
                    sumOfGroupingYears.Add(yearSum);
                }
                groupYearData["finPlanYear" + i] = sumOfGroupingYears.Sum();

                columnYear = columnYear + groupingYearSelection;
            }

            if (remainingColumns > 0)
            {
                List<long> sumofRemngYears = new List<long>();
                for (int i = 1; i <= remainingColumns; i++)
                {
                    long finplanYearValue = (item["finPlanYear" + (columnYear - budgetYear + i)].ToString() != "" ? item["finPlanYear" + (columnYear - budgetYear + i)] : 0);
                    sumofRemngYears.Add(finplanYearValue);
                }
                groupYearData["finPlanYear" + noofGroups + 1] = sumofRemngYears.Sum();
            }
            dataArray.Add(groupYearData);
        }
        return dataArray;
    }

    private JArray Get2AGroupingData(dynamic rptData, int yearColumnsSelection, GroupingYear groupingYearSelection, int budgetYear)
    {
        switch (groupingYearSelection)
        {
            case GroupingYear.NoGrouping:
            case GroupingYear.AllYear:
                return rptData;

            case GroupingYear.FourYear:
            case GroupingYear.FiveYear:
                return Convert2AYearDataToGroupingData(rptData, yearColumnsSelection, Convert.ToInt32(groupingYearSelection), budgetYear);

            default:
                return rptData;
        }
    }

    public async Task<JObject> Get2APopUpDataAsync(int budgetYear, string userId, string column, string id, clsBudgetFormPopUpHelper searchHelper, int docType, int forecastPeriod = 0, int yearColumnSelection = 0, List<string> budgetPhaseIDs = null, List<string> freedim2Ids = null, bool onlyData = false)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValues = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetForm");

        dynamic header = new JObject();
        header.title = langStringValues["BudForm_2A_popUp_title"].LangText;
        header.descriptiontip = langStringValues["BudForm_2A_popUp_desc"].LangText;
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        JArray dataArray = new JArray();
        dynamic finalData = new JObject();
        var data = new List<clsBudgetFormPopUpHelper>();

        var dbData = (from a1 in await Get2ADataFromDBAsync(budgetYear, userId, docType, forecastPeriod, budgetPhaseIDs: budgetPhaseIDs, freedim2Ids: freedim2Ids)
            where id == a1.line_item_id.ToString()
            select a1).ToList();
        switch (column)
        {
            case "actualAmtYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.actual_amt_year,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "revisedBudAmtYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.revised_bud_amt_year,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "orgBudAmtYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.org_bud_amt_year,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "actualAmtLastYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.actual_amt_last_year,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "orgBudAmtLastYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.org_bud_amt_last_year,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "revisedAmtLastYear":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.revised_bud_amt_last_year,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "revisedAmtLastYearByAuth":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.revised_bud_auth_last_year,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "revisedAmtYearByAuth":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.revised_bud_auth_year,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear1":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.finplan_year_1_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear2":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.finplan_year_2_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear3":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.finplan_year_3_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear4":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.finplan_year_4_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear5":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.finplan_year_5_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear6":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.finplan_year_6_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear7":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.finplan_year_7_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear8":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.finplan_year_8_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear9":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.finplan_year_9_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear10":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.finplan_year_10_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear11":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.finplan_year_11_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear12":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.finplan_year_12_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear13":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.finplan_year_13_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear14":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.finplan_year_14_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear15":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.finplan_year_15_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear16":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.finplan_year_16_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear17":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.finplan_year_17_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear18":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.finplan_year_18_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear19":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.finplan_year_19_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
            case "finPlanYear20":
                data = (from a in dbData
                    select new clsBudgetFormPopUpHelper()
                    {
                        accountCode = a.fk_account_code,
                        deptCode = a.fk_department_code,
                        functionCode = a.fk_function_code,
                        projectCode = a.fk_project_code,
                        ammountCol = a.finplan_year_20_amount,
                        format = "n0",
                        search = "",
                    }).OrderBy(x => x.accountCode).ThenBy(x => x.deptCode).ThenBy(x => x.functionCode).ToList(); break;
        }

        if (searchHelper != null)
        {
            data = filterBudgetFormData(searchHelper, data);
        }

        finalData.data = JArray.FromObject(data);
        if (!onlyData)
        {
            finalData.columns = await GetBudgetFormPopUpColumnAsync(budgetYear, userId, column, docType, forecastPeriod, "BudgetForm2A", yearColumnSelection);
            finalData.header = header;
        }
        return finalData;
    }



    private async Task<List<tbf_budget_form_2A>> Get2ADataFromDBAsync(int budgetYear, string userId, int docType, int forecastPeriod, string fridimData = "", List<string> budgetPhaseIDs = null, List<string> freedim2Ids = null)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        List<int> changeIds = budgetPhaseIDs != null && budgetPhaseIDs.Count > 0 ? await GetChangeIdsAsync(budgetYear, budgetPhaseIDs, userId) : new List<int>();
        if (string.IsNullOrEmpty(fridimData))
        {
            List<tbf_budget_form_2A> data = forecastPeriod == 0 ? await (from a in dbContext.tbf_budget_form_2A
                    where a.year == budgetYear && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
                    select a).ToListAsync()
                : await (from a in dbContext.tbf_budget_form_2A
                    where a.forecast_period == forecastPeriod && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
                    select a).ToListAsync();
            if (changeIds.Count > 0)
            {
                data = data.Where(x => changeIds.Contains(x.fk_change_id)).ToList();
            }
            if (freedim2Ids != null && freedim2Ids.Count > 0)
            {
                var emptyFreedimSelected = freedim2Ids.Contains("-1");
                data = emptyFreedimSelected ? data.Where(x => freedim2Ids.Contains(x.free_dim_code) || string.IsNullOrEmpty(x.free_dim_code)).ToList() : data.Where(x => freedim2Ids.Contains(x.free_dim_code)).ToList();
            }
            return data;
        }
        else
        {
            if (fridimData == DocData.EmptyFridm.ToString())
            {
                return forecastPeriod != 0 ? await (from a in dbContext.tbf_budget_form_2A
                    where a.forecast_period == forecastPeriod && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType && string.IsNullOrEmpty(a.free_dim_code)
                    select a).ToListAsync() : await (from a in dbContext.tbf_budget_form_2A
                    where a.fk_tenant_id == userDetails.tenant_id && a.year == budgetYear && a.doc_type == docType && string.IsNullOrEmpty(a.free_dim_code)
                    select a).ToListAsync();
            }
            else
            {
                return forecastPeriod != 0 ? await (from a in dbContext.tbf_budget_form_2A
                    where a.forecast_period == forecastPeriod && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType && (a.free_dim_code != null && !string.IsNullOrEmpty(a.free_dim_code.Trim()))
                    select a).ToListAsync() : await (from a in dbContext.tbf_budget_form_2A
                    where a.fk_tenant_id == userDetails.tenant_id && a.year == budgetYear && a.doc_type == docType && (a.free_dim_code != null && !string.IsNullOrEmpty(a.free_dim_code.Trim()))
                    select a).ToListAsync();
            }
        }
    }

}