#pragma warning disable CS8600
#pragma warning disable CS8602
#pragma warning disable CS8603
#pragma warning disable CS8625

using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Framsikt.BL
{
    public class ClimateAnalysisData : IClimateAnalysisData
    {
        private readonly IUtility _utility;
        private readonly IKostraData _kostra;

        public ClimateAnalysisData(IServiceProvider container)
        {
            _utility = container.GetRequiredService<IUtility>();
            _kostra = container.GetRequiredService<IKostraData>();
        }

        public JArray GetYearSelector(string userId)
        {
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            int minYear = (from a in tenantDbContext.gco_climate_data
                           select a.year).Min();
            int maxYear = (from a in tenantDbContext.gco_climate_data
                           select a.year).Max();
            JArray MinMaxYear = new JArray();
            MinMaxYear.Add(minYear);
            MinMaxYear.Add(maxYear);
            return MinMaxYear;
        }

        public async Task<JObject> GetMunicipalityGraph(string userId, int budgetYear, List<clsRegion> data)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            string currentMunicipalityId = dbContext.gco_tenants.FirstOrDefault(x => x.pk_id == userDetails.tenant_id).municipality_id;
            List<string> emissionTypes = GetParameterValue(userId);
            Dictionary<string, string> lstComparingCites;
            lstComparingCites = _kostra.GetComparableCities(data, userId);
            var result = FetchClimateData(budgetYear, currentMunicipalityId, lstComparingCites, emissionTypes);
            await Task.WhenAll(result);
            JObject formattedGraphData = formateGraphData(userId, result.Result, currentMunicipalityId, lstComparingCites, emissionTypes);
            return formattedGraphData;
        }

        public async Task<JObject> GetYearGraph(string userId, int budgetYear, List<clsRegion> data)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            string currentMunicipalityId = (await _utility.GetTenantDataAsync(userId)).municipality_id;
            List<string> emissionTypes = GetParameterValue(userId);
            string currentMunicipalityName = dbContext.gco_region_codes.FirstOrDefault(x => x.pk_region_code == currentMunicipalityId).region_name;
            Dictionary<string, string> lstComparingCites;
            List<int> Last10YearList = new List<int>();
            lstComparingCites = new Dictionary<string, string> { { currentMunicipalityId, currentMunicipalityName } };
            for (int i = 10; i >= 0; i--)
            {
                if (dbContext.gco_climate_data.FirstOrDefault(x => x.year == (budgetYear - i)) != null)
                {
                    Last10YearList.Add(budgetYear - i);
                }
            }
            var result = FetchDataForYearGraph(lstComparingCites, Last10YearList, emissionTypes);
            await Task.WhenAll(result);
            JObject formattedGraphData = formateYearGraphData(userId, result.Result, Last10YearList, emissionTypes);
            return formattedGraphData;
        }

        public async Task<JObject> GetPerGasTypeGraph(string userId, int budgetYear, List<clsRegion> data)
        {
            string currentMunicipalityId = (await _utility.GetTenantDataAsync(userId)).municipality_id;
            List<string> emissionTypes = GetParameterValue(userId);
            var result = FetchDataForGasTypeGraph(currentMunicipalityId, budgetYear, emissionTypes);
            await Task.WhenAll(result);
            JObject formattedGraphData = formateGasTypeGraphData(userId, result.Result, emissionTypes);
            return formattedGraphData;
        }

        public async Task<JObject> GetPerCitizenGraph(string userId, int budgetYear, List<clsRegion> data)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            List<string> emissionTypes = GetParameterValue(userId);
            string currentMunicipalityId = (await _utility.GetTenantDataAsync(userId)).municipality_id;
            string currentMunicipalityName = dbContext.gco_region_codes.FirstOrDefault(x => x.pk_region_code == currentMunicipalityId).region_name;
            Dictionary<string, string> lstComparingCites;
            lstComparingCites = _kostra.GetComparableCities(data, userId);
            if (!lstComparingCites.ContainsKey(currentMunicipalityId))
            {
                lstComparingCites = (new Dictionary<string, string> { { currentMunicipalityId, currentMunicipalityName } }).Concat(lstComparingCites).ToDictionary(k => k.Key, v => v.Value);
            }
            List<int> Last10YearList = new List<int>();
            for (int i = 10; i >= 0; i--)
            {
                if (dbContext.gco_climate_data.FirstOrDefault(x => x.year == (budgetYear - i)) != null)
                {
                    Last10YearList.Add(budgetYear - i);
                }
            }
            var result = FetchDataForPerCitzenGraph(lstComparingCites, Last10YearList, emissionTypes);
            await Task.WhenAll(result);
            JObject formattedGraphData = formatePerCitizenGraphData(userId, lstComparingCites, result.Result, Last10YearList);
            return formattedGraphData;
        }

        private async Task<List<ClimateDataAnalysisHelper>> FetchClimateData(int budgetYear, string currentMunicipalityId, Dictionary<string, string> data, List<string> emissionTypes = null)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            var result = !emissionTypes.Any() ? await (from a in dbContext.gco_climate_data
                                                       where a.year == budgetYear && currentMunicipalityId == a.fk_region_code
                                                       group a by new { a.fk_region_code, a.sector } into res
                                                       select new ClimateDataAnalysisHelper
                                                       {
                                                           regionCode = res.Key.fk_region_code,
                                                           sector = res.Key.sector,
                                                           emissionValue = res.Sum(x => x.emission_value)
                                                       }).OrderBy(x => x.sector).AsNoTracking().ToListAsync()
                                : await (from a in dbContext.gco_climate_data
                                         where a.year == budgetYear && currentMunicipalityId == a.fk_region_code && !emissionTypes.Contains(a.sector)
                                         group a by new { a.fk_region_code, a.sector } into res
                                         select new ClimateDataAnalysisHelper
                                         {
                                             regionCode = res.Key.fk_region_code,
                                             sector = res.Key.sector,
                                             emissionValue = res.Sum(x => x.emission_value)
                                         }).OrderBy(x => x.sector).AsNoTracking().ToListAsync();
            if (data.Any())
            {
                var result1 = !emissionTypes.Any() ? await (from a in dbContext.gco_climate_data
                                                            where a.year == budgetYear && data.Keys.Contains(a.fk_region_code)
                                                            group a by new { a.fk_region_code, a.sector } into res
                                                            select new ClimateDataAnalysisHelper
                                                            {
                                                                regionCode = res.Key.fk_region_code,
                                                                sector = res.Key.sector,
                                                                emissionValue = res.Sum(x => x.emission_value)
                                                            }).OrderBy(x => x.sector).AsNoTracking().ToListAsync()
                                     : await (from a in dbContext.gco_climate_data
                                              where a.year == budgetYear && data.Keys.Contains(a.fk_region_code) && !emissionTypes.Contains(a.sector)
                                              group a by new { a.fk_region_code, a.sector } into res
                                              select new ClimateDataAnalysisHelper
                                              {
                                                  regionCode = res.Key.fk_region_code,
                                                  sector = res.Key.sector,
                                                  emissionValue = res.Sum(x => x.emission_value)
                                              }).OrderBy(x => x.sector).AsNoTracking().ToListAsync();
                result.AddRange(result1);
            }
            return result;
        }

        private async Task<List<ClimateDataAnalysisHelper>> FetchDataForYearGraph(Dictionary<string, string> cities, List<int> Last10Years, List<string> emissionTypes)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            return !emissionTypes.Any() ? await (from a in dbContext.gco_climate_data
                                                 where Last10Years.Contains(a.year) && cities.Keys.Contains(a.fk_region_code)
                                                 group a by new { a.year, a.sector } into res
                                                 select new ClimateDataAnalysisHelper
                                                 {
                                                     year = res.Key.year,
                                                     sector = res.Key.sector,
                                                     emissionValue = res.Sum(x => x.emission_value)
                                                 }).OrderBy(x => x.year).AsNoTracking().ToListAsync()
                          : await (from a in dbContext.gco_climate_data
                                   where Last10Years.Contains(a.year) && cities.Keys.Contains(a.fk_region_code) && !emissionTypes.Contains(a.sector)
                                   group a by new { a.year, a.sector } into res
                                   select new ClimateDataAnalysisHelper
                                   {
                                       year = res.Key.year,
                                       sector = res.Key.sector,
                                       emissionValue = res.Sum(x => x.emission_value)
                                   }).OrderBy(x => x.year).AsNoTracking().ToListAsync();
        }

        private async Task<List<ClimateDataAnalysisHelper>> FetchDataForGasTypeGraph(string currentMunicipalityId, int budgetYear, List<string> emissionTypes)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            List<ClimateDataAnalysisHelper> distinctTotalEmission = !emissionTypes.Any() ? await (from a in dbContext.gco_climate_data
                                                                                                  where a.year == budgetYear && a.fk_region_code == currentMunicipalityId
                                                                                                  group a by new { a.sector } into res
                                                                                                  select new ClimateDataAnalysisHelper
                                                                                                  {
                                                                                                      sector = res.Key.sector,
                                                                                                      emissionValue = res.Sum(x => x.emission_value)
                                                                                                  }).OrderBy(x => x.sector).AsNoTracking().ToListAsync()
                                                                           : await (from a in dbContext.gco_climate_data
                                                                                    where a.year == budgetYear && a.fk_region_code == currentMunicipalityId && !emissionTypes.Contains(a.sector)
                                                                                    group a by new { a.sector } into res
                                                                                    select new ClimateDataAnalysisHelper
                                                                                    {
                                                                                        sector = res.Key.sector,
                                                                                        emissionValue = res.Sum(x => x.emission_value)
                                                                                    }).OrderBy(x => x.sector).AsNoTracking().ToListAsync();

            List<ClimateDataAnalysisHelper> emissionValueByGasType = !emissionTypes.Any() ? await (from a in dbContext.gco_climate_data
                                                                                                   where a.year == budgetYear && a.fk_region_code == currentMunicipalityId
                                                                                                   group a by new { a.sector, a.gass_type } into res
                                                                                                   select new ClimateDataAnalysisHelper
                                                                                                   {
                                                                                                       sector = res.Key.sector,
                                                                                                       gassType = res.Key.gass_type,
                                                                                                       emissionValue = res.Sum(x => x.emission_value)
                                                                                                   }).OrderBy(x => x.sector).AsNoTracking().ToListAsync()
                                                                            : await (from a in dbContext.gco_climate_data
                                                                                     where a.year == budgetYear && a.fk_region_code == currentMunicipalityId && !emissionTypes.Contains(a.sector)
                                                                                     group a by new { a.sector, a.gass_type } into res
                                                                                     select new ClimateDataAnalysisHelper
                                                                                     {
                                                                                         sector = res.Key.sector,
                                                                                         gassType = res.Key.gass_type,
                                                                                         emissionValue = res.Sum(x => x.emission_value)
                                                                                     }).OrderBy(x => x.sector).AsNoTracking().ToListAsync();
            List<ClimateDataAnalysisHelper> result = (from a in emissionValueByGasType
                                                      select new ClimateDataAnalysisHelper
                                                      {
                                                          sector = a.sector,
                                                          gassType = a.gassType,
                                                          perGasEmission = a.emissionValue,
                                                          emissionValue = (distinctTotalEmission.FirstOrDefault(x => x.sector == a.sector) != null && distinctTotalEmission.FirstOrDefault(x => x.sector == a.sector).emissionValue != 0) ? Math.Round((a.emissionValue / distinctTotalEmission.FirstOrDefault(x => x.sector == a.sector).emissionValue) * 100, 1) : 0
                                                      }).ToList();
            return result;
        }

        private JObject formateGraphData(string userId, List<ClimateDataAnalysisHelper> result, string currentMunicipalityId, Dictionary<string, string> regionData, List<string> emissionTypes)
        {
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            List<string> distinctSector = dbContext.gco_climate_data.OrderBy(x => x.sector).Select(x => x.sector).Distinct().ToList();
            if (emissionTypes.Any())
            {
                distinctSector = distinctSector.Where(x => !emissionTypes.Contains(x)).ToList();
            }
            dynamic municipalityGraph = new JObject();
            dynamic municipalityData = new JArray();
            Dictionary<string, string> colorsForGraph = _utility.GetColors(userId, ColorsFor.Graph);
            string regionName = dbContext.gco_region_codes.FirstOrDefault(x => x.pk_region_code == currentMunicipalityId).region_name;
            if (!regionData.ContainsKey(currentMunicipalityId))
            {
                regionData = (new Dictionary<string, string> { { currentMunicipalityId, regionName } }).Concat(regionData).ToDictionary(k => k.Key, v => v.Value);
            }
            int i = 0;
            foreach (string sector in distinctSector)
            {
                List<decimal> chartData = new List<decimal>();
                foreach (string region in regionData.Keys)
                {
                    chartData.Add(result.FirstOrDefault(x => x.sector == sector && x.regionCode == region) != null ? result.FirstOrDefault(x => x.sector == sector && x.regionCode == region).emissionValue : 0);
                }
                JObject data = new JObject() { new JProperty("name", sector),
                                               new JProperty("data", chartData),
                                               new JProperty("color", colorsForGraph[i.ToString()])};
                municipalityData.Add(data);
                i++;
            }
            municipalityGraph.series = municipalityData;
            municipalityGraph.legend = new JObject() {
                new JProperty("position", "bottom")
            };
            municipalityGraph.seriesDefaults = new JObject() {
                new JProperty("type", "column"),
                new JProperty("stack", true),
                new JProperty("overlay", new JObject(){ new JProperty("gradient", "none") })
            };
            municipalityGraph.categoryAxis = new JObject() {
                new JProperty("categories", regionData.Values),
                new JProperty("majorGridLines", new JObject(){ new JProperty("visible", false)})
            };
            municipalityGraph.valueAxis = new JObject() {
                new JProperty("labels", new JObject(){ new JProperty("template", "#=(kendo.toString(value,'n0'))#")})
            };
            municipalityGraph.tooltip = new JObject() {
                new JProperty("visible", true),
                new JProperty("template", "#= series.name #: #= (kendo.toString(value,'n0')) #")
            };
            return municipalityGraph;
        }

        private async Task<List<ClimateDataAnalysisHelper>> FetchDataForPerCitzenGraph(Dictionary<string, string> cities, List<int> Last10Years, List<string> emissionTypes)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            List<ClimateDataAnalysisHelper> climateDataFor4Year = !emissionTypes.Any() ? await (from a in dbContext.gco_climate_data
                                                                                                where Last10Years.Contains(a.year) && cities.Keys.Contains(a.fk_region_code)
                                                                                                group a by new { a.fk_region_code, a.year } into res
                                                                                                select new ClimateDataAnalysisHelper
                                                                                                {
                                                                                                    year = res.Key.year,
                                                                                                    regionCode = res.Key.fk_region_code,
                                                                                                    emissionValue = res.Sum(x => x.emission_value_per_inh)
                                                                                                }).OrderBy(x => x.year).AsNoTracking().ToListAsync()
                                                                         : await (from a in dbContext.gco_climate_data
                                                                                  where Last10Years.Contains(a.year) && cities.Keys.Contains(a.fk_region_code) && !emissionTypes.Contains(a.sector)
                                                                                  group a by new { a.fk_region_code, a.year } into res
                                                                                  select new ClimateDataAnalysisHelper
                                                                                  {
                                                                                      year = res.Key.year,
                                                                                      regionCode = res.Key.fk_region_code,
                                                                                      emissionValue = res.Sum(x => x.emission_value_per_inh)
                                                                                  }).OrderBy(x => x.year).AsNoTracking().ToListAsync();
            return climateDataFor4Year;
        }

        private JObject formateYearGraphData(string userId, List<ClimateDataAnalysisHelper> result, List<int> last10years, List<string> emissionTypes)
        {
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            List<string> distinctSector = dbContext.gco_climate_data.OrderBy(x => x.sector).Select(x => x.sector).Distinct().ToList();
            if (emissionTypes.Any())
            {
                distinctSector = distinctSector.Where(x => !emissionTypes.Contains(x)).ToList();
            }
            dynamic yearGraph = new JObject();
            dynamic yearData = new JArray();
            Dictionary<string, string> colorsForGraph = _utility.GetColors(userId, ColorsFor.Graph);
            int i = 0;
            foreach (string sector in distinctSector)
            {
                List<decimal> chartData = new List<decimal>();
                foreach (int year in last10years)
                {
                    chartData.Add(result.FirstOrDefault(x => x.sector == sector && x.year == year) != null ? result.FirstOrDefault(x => x.sector == sector && x.year == year).emissionValue : 0);
                }
                JObject data = new JObject() { new JProperty("name", sector),
                                               new JProperty("data", chartData),
                                               new JProperty("color", colorsForGraph[i.ToString()])};
                yearData.Add(data);
                i++;
            }
            yearGraph.series = yearData;
            yearGraph.legend = new JObject() {
                new JProperty("position", "bottom")
            };
            yearGraph.seriesDefaults = new JObject() {
                new JProperty("type", "column"),
                new JProperty("stack", true),
                 new JProperty("overlay", new JObject(){ new JProperty("gradient", "none") })
            };
            yearGraph.categoryAxis = new JObject() {
                new JProperty("categories", last10years),
                new JProperty("majorGridLines", new JObject(){ new JProperty("visible", false)})
            };
            yearGraph.valueAxis = new JObject() {
                new JProperty("labels", new JObject(){ new JProperty("template", "#=(kendo.toString(value,'n0'))#")})
            };
            yearGraph.tooltip = new JObject() {
                new JProperty("visible", true),
                new JProperty("template", "#= series.name #: #= (kendo.toString(value,'n0')) #")
            };
            return yearGraph;
        }

        private JObject formateGasTypeGraphData(string userId, List<ClimateDataAnalysisHelper> result, List<string> emissionTypes)
        {
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            List<string> distinctSector = dbContext.gco_climate_data.OrderBy(x => x.sector).Select(x => x.sector).Distinct().ToList();
            if (emissionTypes.Any())
            {
                distinctSector = distinctSector.Where(x => !emissionTypes.Contains(x)).ToList();
            }
            List<string> distinctGas = dbContext.gco_climate_data.OrderBy(x => x.gass_type).Select(x => x.gass_type).Distinct().ToList();
            dynamic perGasTypeGraph = new JObject();
            dynamic dataSource = new JObject();
            dynamic perGasData = new JArray();
            dynamic seriesColors = new JArray();
            Dictionary<string, string> colorsForGraph = _utility.GetColors(userId, ColorsFor.Graph);
            seriesColors.Add(colorsForGraph[0.ToString()]);
            seriesColors.Add(colorsForGraph[1.ToString()]);
            seriesColors.Add(colorsForGraph[2.ToString()]);

            int i = 0;
            decimal chartData;
            decimal toolTipData;
            foreach (string gas in distinctGas)
            {
                foreach (string sector in distinctSector)
                {
                    chartData = result.FirstOrDefault(x => x.sector == sector && x.gassType == gas) != null ? result.FirstOrDefault(x => x.sector == sector && x.gassType == gas).emissionValue : 0;
                    toolTipData = result.FirstOrDefault(x => x.sector == sector && x.gassType == gas) != null ? result.FirstOrDefault(x => x.sector == sector && x.gassType == gas).perGasEmission : 0;
                    JObject data = new JObject() { new JProperty("name", gas),
                                               new JProperty("data", chartData),
                                               new JProperty("tooltipdata", toolTipData),
                                               new JProperty("sector", sector)};
                    perGasData.Add(data);
                }
                i++;
            }
            dataSource.data = perGasData;
            dataSource.group = new JObject{
                new JProperty("field", "name")};
            perGasTypeGraph.dataSource = dataSource;
            perGasTypeGraph.series = perGasTypeGraph.seriesDefaults = new JArray() {new JObject{
                new JProperty("field", "data"),
                new JProperty("categoryField", "sector")
            }};
            perGasTypeGraph.legend = new JObject() {
                new JProperty("position", "bottom")
            };
            perGasTypeGraph.seriesColors = seriesColors;
            perGasTypeGraph.seriesDefaults = new JObject() {
                new JProperty("type", "bar"),
                new JProperty("stack", new JObject(){new JProperty("type","100%") }),
                new JProperty("overlay", new JObject(){ new JProperty("gradient", "none") })
            };
            perGasTypeGraph.categoryAxis = new JObject() {
                new JProperty("categories", distinctSector),
                new JProperty("majorGridLines", new JObject(){ new JProperty("visible", false)})
            };
            perGasTypeGraph.tooltip = new JObject() {
                new JProperty("visible", true),
                new JProperty("template",   "#= dataItem.name #: #=kendo.toString(dataItem.tooltipdata,'n2')#")
            };
            return perGasTypeGraph;
        }

        private JObject formatePerCitizenGraphData(string userId, Dictionary<string, string> cities, List<ClimateDataAnalysisHelper> result, List<int> last10years)
        {
            dynamic citizenGraph = new JObject();
            dynamic citizenData = new JArray();
            Dictionary<string, string> colorsForGraph = _utility.GetColors(userId, ColorsFor.Graph);
            int i = 0;
            foreach (KeyValuePair<string, string> sector in cities)
            {
                List<decimal> chartData = new List<decimal>();
                foreach (int year in last10years)
                {
                    if (result.FirstOrDefault(x => x.regionCode == sector.Key && x.year == year) != null && result.FirstOrDefault(x => x.regionCode == sector.Key && x.year == year).emissionValue != 0)
                    {
                        chartData.Add(result.FirstOrDefault(x => x.regionCode == sector.Key && x.year == year).emissionValue);
                    }
                }
                if (chartData.Any())
                {
                    JObject data = new JObject() { new JProperty("name", sector.Value),
                                               new JProperty("data", chartData),
                                               new JProperty("color", colorsForGraph[i.ToString()])};

                    citizenData.Add(data);
                    i++;
                }
            }
            citizenGraph.series = citizenData;
            citizenGraph.legend = new JObject() {
                new JProperty("position", "bottom")
            };
            citizenGraph.seriesDefaults = new JObject() {
                new JProperty("type", "line"),
                new JProperty("style", "normal"),
                new JProperty("markers", new JObject(){ new JProperty("size", 5),new JProperty("visible", true)})
            };
            citizenGraph.valueAxis = new JObject() {
                new JProperty("labels", new JObject(){ new JProperty("color","#000")}),
            };
            citizenGraph.categoryAxis = new JObject() {
                new JProperty("categories", last10years),
                new JProperty("majorGridLines", new JObject(){ new JProperty("visible", false)})
            };
            citizenGraph.tooltip = new JObject() {
                new JProperty("visible", true),
                new JProperty("template",  "#= series.name #: #= (kendo.toString(value,'n1')) #")
            };
            return citizenGraph;
        }

        public async Task<JObject> GetGridSection(string userId, int budgetYear, List<clsRegion> cites, int graphTabIndex, int gridTabIndex)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, string> cityList = _kostra.GetComparableCities(cites, userId);
            string currentMunicipalityId = dbContext.gco_tenants.FirstOrDefault(x => x.pk_id == userDetails.tenant_id).municipality_id;

            List<string> emissionTypes = GetParameterValue(userId);
            if (graphTabIndex == 1)
            {
                var gridData = GetGridSectionForMunicipality(budgetYear, currentMunicipalityId, cityList, gridTabIndex, emissionTypes);
                await Task.WhenAll(gridData);
                return gridData.Result;
            }
            if (graphTabIndex == 2)
            {
                var gridData = GetGridSectionForYear(budgetYear, currentMunicipalityId, cityList, gridTabIndex, emissionTypes);
                await Task.WhenAll(gridData);
                return gridData.Result;
            }
            if (graphTabIndex == 3)
            {
                var gridData = GetGridSectionForPerGasType(budgetYear, currentMunicipalityId, cityList, gridTabIndex, emissionTypes);
                await Task.WhenAll(gridData);
                return gridData.Result;
            }
            if (graphTabIndex == 4)
            {
                var gridData = GetGridSectionForPerCitizen(budgetYear, currentMunicipalityId, cityList, gridTabIndex, emissionTypes);
                await Task.WhenAll(gridData);
                return gridData.Result;
            }
            return new JObject();
        }

        private async Task<JObject> GetGridSectionForMunicipality(int budgetYear, string currentMunicipalityId, Dictionary<string, string> cities, int gridTabIndex, List<string> emissionTypes)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            string currentMunicipalityName = dbContext.gco_region_codes.FirstOrDefault(x => x.pk_region_code == currentMunicipalityId).region_name;
            dynamic municipalityGridData = new JObject();
            switch (gridTabIndex)
            {
                case 1:
                    var tab1Result = FetchClimateData(budgetYear, currentMunicipalityId, cities, emissionTypes);
                    await Task.WhenAll(tab1Result);
                    if (!cities.ContainsKey(currentMunicipalityId))
                    {
                        cities = (new Dictionary<string, string> { { currentMunicipalityId, currentMunicipalityName } }).Concat(cities).ToDictionary(k => k.Key, v => v.Value);
                    }
                    return formateTab1GridData(tab1Result.Result, cities, emissionTypes);

                case 2:
                    var tab2Result = FetchClimateDataPerGasType(budgetYear, clsConstants.cliamte_data_analysis_gas_type.CO2.ToString(), currentMunicipalityId, cities, emissionTypes);
                    await Task.WhenAll(tab2Result);
                    if (!cities.ContainsKey(currentMunicipalityId))
                    {
                        cities = (new Dictionary<string, string> { { currentMunicipalityId, currentMunicipalityName } }).Concat(cities).ToDictionary(k => k.Key, v => v.Value);
                    }
                    return formateTab1GridData(tab2Result.Result, cities, emissionTypes);

                case 3:
                    var tab3Result = FetchClimateDataPerGasType(budgetYear, clsConstants.cliamte_data_analysis_gas_type.CH4.ToString(), currentMunicipalityId, cities, emissionTypes);
                    await Task.WhenAll(tab3Result);
                    if (!cities.ContainsKey(currentMunicipalityId))
                    {
                        cities = (new Dictionary<string, string> { { currentMunicipalityId, currentMunicipalityName } }).Concat(cities).ToDictionary(k => k.Key, v => v.Value);
                    }
                    return formateTab1GridData(tab3Result.Result, cities, emissionTypes);

                case 4:
                    var tab4Result = FetchClimateDataPerGasType(budgetYear, clsConstants.cliamte_data_analysis_gas_type.N2O.ToString(), currentMunicipalityId, cities, emissionTypes);
                    await Task.WhenAll(tab4Result);
                    if (!cities.ContainsKey(currentMunicipalityId))
                    {
                        cities = (new Dictionary<string, string> { { currentMunicipalityId, currentMunicipalityName } }).Concat(cities).ToDictionary(k => k.Key, v => v.Value);
                    }
                    return formateTab1GridData(tab4Result.Result, cities, emissionTypes);
            }
            return municipalityGridData;
        }

        private async Task<JObject> GetGridSectionForYear(int budgetYear, string currentMunicipalityId, Dictionary<string, string> cities, int gridTabIndex, List<string> emissionTypes)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            string currentMunicipalityName = dbContext.gco_region_codes.FirstOrDefault(x => x.pk_region_code == currentMunicipalityId).region_name;
            List<int> Last10YearList = new List<int>();
            for (int i = 10; i >= 0; i--)
            {
                if (dbContext.gco_climate_data.FirstOrDefault(x => x.year == (budgetYear - i)) != null)
                {
                    Last10YearList.Add(budgetYear - i);
                }
            }
            cities = new Dictionary<string, string> { { currentMunicipalityId, currentMunicipalityName } };
            dynamic municipalityGridData = new JObject();
            switch (gridTabIndex)
            {
                case 1:
                    var tab1Result = FetchDataForYearGraph(cities, Last10YearList, emissionTypes);
                    await Task.WhenAll(tab1Result);
                    return formateTab2GridData(tab1Result.Result, Last10YearList, emissionTypes);

                case 2:
                    var tab2Result = FetchClimateDataForYearByGasType(Last10YearList, clsConstants.cliamte_data_analysis_gas_type.CO2.ToString(), cities, emissionTypes);
                    await Task.WhenAll(tab2Result);
                    return formateTab2GridData(tab2Result.Result, Last10YearList, emissionTypes);

                case 3:
                    var tab3Result = FetchClimateDataForYearByGasType(Last10YearList, clsConstants.cliamte_data_analysis_gas_type.CH4.ToString(), cities, emissionTypes);
                    await Task.WhenAll(tab3Result);
                    return formateTab2GridData(tab3Result.Result, Last10YearList, emissionTypes);

                case 4:
                    var tab4Result = FetchClimateDataForYearByGasType(Last10YearList, clsConstants.cliamte_data_analysis_gas_type.N2O.ToString(), cities, emissionTypes);
                    await Task.WhenAll(tab4Result);
                    return formateTab2GridData(tab4Result.Result, Last10YearList, emissionTypes);
            }
            return municipalityGridData;
        }

        private async Task<JObject> GetGridSectionForPerGasType(int budgetYear, string currentMunicipalityId, Dictionary<string, string> cities, int gridTabIndex, List<string> emissionTypes)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            string currentMunicipalityName = dbContext.gco_region_codes.FirstOrDefault(x => x.pk_region_code == currentMunicipalityId).region_name;
            if (!cities.ContainsKey(currentMunicipalityId))
            {
                cities = (new Dictionary<string, string> { { currentMunicipalityId, currentMunicipalityName } }).Concat(cities).ToDictionary(k => k.Key, v => v.Value);
            }
            dynamic municipalityGridData = new JObject();
            switch (gridTabIndex)
            {
                case 1:
                    var tab1Result = FetchDataForGasTypeGraph(currentMunicipalityId, budgetYear, emissionTypes);
                    await Task.WhenAll(tab1Result);
                    return formateTab3GridData(tab1Result.Result, budgetYear, "", emissionTypes);

                case 2:
                    var tab2Result = FetchDataForGasTypeGraph(currentMunicipalityId, budgetYear, emissionTypes);
                    await Task.WhenAll(tab2Result);
                    return formateTab3GridData(tab2Result.Result, budgetYear, clsConstants.cliamte_data_analysis_gas_type.CO2.ToString(), emissionTypes);

                case 3:
                    var tab3Result = FetchDataForGasTypeGraph(currentMunicipalityId, budgetYear, emissionTypes);
                    await Task.WhenAll(tab3Result);
                    return formateTab3GridData(tab3Result.Result, budgetYear, clsConstants.cliamte_data_analysis_gas_type.CH4.ToString(), emissionTypes);

                case 4:
                    var tab4Result = FetchDataForGasTypeGraph(currentMunicipalityId, budgetYear, emissionTypes);
                    await Task.WhenAll(tab4Result);
                    return formateTab3GridData(tab4Result.Result, budgetYear, clsConstants.cliamte_data_analysis_gas_type.N2O.ToString(), emissionTypes);
            }
            return municipalityGridData;
        }

        private async Task<List<ClimateDataAnalysisHelper>> FetchDataForPerCitzenGridByGasType(Dictionary<string, string> cities, List<int> Last10Years, string gassType, List<string> emissionTypes)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();

            List<ClimateDataAnalysisHelper> climateDataFor10Year = !emissionTypes.Any() ? await (from a in dbContext.gco_climate_data
                                                                                                 where Last10Years.Contains(a.year) && cities.Keys.Contains(a.fk_region_code)
                                                                                                 && a.gass_type == gassType
                                                                                                 group a by new { a.fk_region_code, a.year } into res
                                                                                                 select new ClimateDataAnalysisHelper
                                                                                                 {
                                                                                                     year = res.Key.year,
                                                                                                     regionCode = res.Key.fk_region_code,
                                                                                                     emissionValue = res.Sum(x => x.emission_value_per_inh)
                                                                                                 }).OrderBy(x => x.year).AsNoTracking().ToListAsync()
                                                                          : await (from a in dbContext.gco_climate_data
                                                                                   where Last10Years.Contains(a.year) && cities.Keys.Contains(a.fk_region_code) && !emissionTypes.Contains(a.sector)
                                                                                   && a.gass_type == gassType
                                                                                   group a by new { a.fk_region_code, a.year } into res
                                                                                   select new ClimateDataAnalysisHelper
                                                                                   {
                                                                                       year = res.Key.year,
                                                                                       regionCode = res.Key.fk_region_code,
                                                                                       emissionValue = res.Sum(x => x.emission_value_per_inh)
                                                                                   }).OrderBy(x => x.year).AsNoTracking().ToListAsync();
            return climateDataFor10Year;
        }

        private async Task<JObject> GetGridSectionForPerCitizen(int budgetYear, string currentMunicipalityId, Dictionary<string, string> cities, int gridTabIndex, List<string> emissionTypes)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            string currentMunicipalityName = dbContext.gco_region_codes.FirstOrDefault(x => x.pk_region_code == currentMunicipalityId).region_name;
            if (!cities.ContainsKey(currentMunicipalityId))
            {
                cities = (new Dictionary<string, string> { { currentMunicipalityId, currentMunicipalityName } }).Concat(cities).ToDictionary(k => k.Key, v => v.Value);
            }
            List<int> Last10YearList = new List<int>();
            for (int i = 10; i >= 0; i--)
            {
                if (dbContext.gco_climate_data.FirstOrDefault(x => x.year == (budgetYear - i)) != null)
                {
                    Last10YearList.Add(budgetYear - i);
                }
            }
            dynamic municipalityGridData = new JObject();
            Dictionary<string, string> citiesWithData = new Dictionary<string, string>();
            //fetch cities with data
            foreach (var city in cities)
            {
                if (dbContext.gco_climate_data.FirstOrDefault(x => x.fk_region_code == city.Key) != null)
                {
                    citiesWithData.Add(city.Key, city.Value);
                }
            }
            switch (gridTabIndex)
            {
                case 1:
                    var tab1Result = FetchDataForPerCitzenGraph(citiesWithData, Last10YearList, emissionTypes);
                    await Task.WhenAll(tab1Result);
                    return formateTab4GridData(tab1Result.Result, citiesWithData, Last10YearList);

                case 2:
                    var tab2Result = FetchDataForPerCitzenGridByGasType(citiesWithData, Last10YearList, clsConstants.cliamte_data_analysis_gas_type.CO2.ToString(), emissionTypes);
                    await Task.WhenAll(tab2Result);
                    return formateTab4GridData(tab2Result.Result, citiesWithData, Last10YearList);

                case 3:
                    var tab3Result = FetchDataForPerCitzenGridByGasType(citiesWithData, Last10YearList, clsConstants.cliamte_data_analysis_gas_type.CH4.ToString(), emissionTypes);
                    await Task.WhenAll(tab3Result);
                    return formateTab4GridData(tab3Result.Result, citiesWithData, Last10YearList);

                case 4:
                    var tab4Result = FetchDataForPerCitzenGridByGasType(citiesWithData, Last10YearList, clsConstants.cliamte_data_analysis_gas_type.N2O.ToString(), emissionTypes);
                    await Task.WhenAll(tab4Result);
                    return formateTab4GridData(tab4Result.Result, citiesWithData, Last10YearList);
            }
            return municipalityGridData;
        }

        private JObject formateTab1GridData(List<ClimateDataAnalysisHelper> result, Dictionary<string, string> cities, List<string> emissionTypes = null)
        {
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            dynamic finalresult = new JArray();
            dynamic municipalityGridData = new JObject();
            List<string> distinctSector = dbContext.gco_climate_data.OrderBy(x => x.sector).Select(x => x.sector).Distinct().ToList();
            if (emissionTypes.Any())
            {
                distinctSector = distinctSector.Where(x => !emissionTypes.Contains(x)).ToList();
            }
            foreach (string sector in distinctSector)
            {
                int cityCount = 1;
                JObject gridData = new JObject(new JObject() { new JProperty("sector", sector) });
                foreach (string region in cities.Keys)
                {
                    var emissionValue = result.FirstOrDefault(x => x.sector == sector && x.regionCode == region) != null ? result.FirstOrDefault(x => x.sector == sector && x.regionCode == region).emissionValue : 0;
                    gridData.Add(new JProperty("city" + cityCount, emissionValue));
                    cityCount++;
                }
                finalresult.Add(gridData);
            }
            JObject sumRowData = new JObject(new JObject() { new JProperty("sector", "Sum") });
            int index = 1;
            foreach (string city in cities.Keys)
            {
                var sum = result.Where(x => x.regionCode == city) != null ? result.Where(x => x.regionCode == city).Sum(x => x.emissionValue) : 0;
                sumRowData.Add(new JProperty("city" + index, sum));
                index++;
            }
            finalresult.Add(sumRowData);
            municipalityGridData.dataSource = JArray.FromObject(finalresult);
            municipalityGridData.columns = getMunicipalityGridColumn(cities);
            return municipalityGridData;
        }

        private JObject formateTab2GridData(List<ClimateDataAnalysisHelper> result, List<int> last5Years, List<string> emissionTypes)
        {
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            dynamic finalresult = new JArray();
            dynamic municipalityGridData = new JObject();
            List<string> distinctSector = dbContext.gco_climate_data.OrderBy(x => x.sector).Select(x => x.sector).Distinct().ToList();
            if (emissionTypes.Any())
            {
                distinctSector = distinctSector.Where(x => !emissionTypes.Contains(x)).ToList();
            }
            foreach (string sector in distinctSector)
            {
                int cityCount = 1;
                JObject gridData = new JObject(new JObject() { new JProperty("sector", sector) });
                foreach (int year in last5Years)
                {
                    var emissionValue = result.FirstOrDefault(x => x.sector == sector && x.year == year) != null ? result.FirstOrDefault(x => x.sector == sector && x.year == year).emissionValue : 0;
                    gridData.Add(new JProperty("year" + cityCount, emissionValue));
                    cityCount++;
                }
                finalresult.Add(gridData);
            }
            JObject sumRowData = new JObject(new JObject() { new JProperty("sector", "Sum") });
            int index = 1;
            foreach (int year in last5Years)
            {
                var sum = result.Where(x => x.year == year) != null ? result.Where(x => x.year == year).Sum(x => x.emissionValue) : 0;
                sumRowData.Add(new JProperty("year" + index, sum));
                index++;
            }
            finalresult.Add(sumRowData);
            municipalityGridData.dataSource = JArray.FromObject(finalresult);
            municipalityGridData.columns = getYearGridColumn(last5Years);
            return municipalityGridData;
        }

        private JObject formateTab3GridData(List<ClimateDataAnalysisHelper> result, int budgetYear, string gassType, List<string> emissionTypes)
        {
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            dynamic finalresult = new JArray();
            dynamic municipalityGridData = new JObject();
            List<string> distinctSector = dbContext.gco_climate_data.OrderBy(x => x.sector).Select(x => x.sector).Distinct().ToList();
            if (emissionTypes.Any())
            {
                distinctSector = distinctSector.Where(x => !emissionTypes.Contains(x)).ToList();
            }
            decimal emissionValue;
            foreach (string sector in distinctSector)
            {
                JObject gridData = new JObject(new JObject() { new JProperty("sector", sector) });
                if (!string.IsNullOrEmpty(gassType))
                {
                    emissionValue = result.FirstOrDefault(x => x.sector == sector && x.gassType == gassType) != null ? result.Where(x => x.sector == sector && x.gassType == gassType).Sum(x => x.perGasEmission) : 0;
                }
                else
                {
                    emissionValue = result.FirstOrDefault(x => x.sector == sector) != null ? result.Where(x => x.sector == sector).Sum(x => x.perGasEmission) : 0;
                }

                gridData.Add(new JProperty("totalEmission", emissionValue));
                finalresult.Add(gridData);
            }
            JObject sumRowData = new JObject(new JObject() { new JProperty("sector", "Sum") });
            if (!string.IsNullOrEmpty(gassType))
            {
                sumRowData.Add(new JProperty("totalEmission", result.Where(x => x.gassType == gassType) != null ? result.Where(x => x.gassType == gassType).Sum(x => x.perGasEmission) : 0));
            }
            else
            {
                sumRowData.Add(new JProperty("totalEmission", result != null ? result.Sum(x => x.perGasEmission) : 0));
            }

            finalresult.Add(sumRowData);
            municipalityGridData.dataSource = JArray.FromObject(finalresult);
            municipalityGridData.columns = getPerGasTypeGridColumn(budgetYear);
            return municipalityGridData;
        }

        private JObject formateTab4GridData(List<ClimateDataAnalysisHelper> result, Dictionary<string, string> cities, List<int> last10Years)
        {
            dynamic finalresult = new JArray();
            dynamic municipalityGridData = new JObject();
            foreach (KeyValuePair<string, string> city in cities)
            {
                int cityCount = 1;
                JObject gridData = new JObject(new JObject() { new JProperty("city", city.Value) });
                foreach (int year in last10Years)
                {
                    var emissionValue = result.FirstOrDefault(x => x.regionCode == city.Key && x.year == year) != null ? result.FirstOrDefault(x => x.regionCode == city.Key && x.year == year).emissionValue : 0;
                    gridData.Add(new JProperty("year" + cityCount, emissionValue));
                    cityCount++;
                }
                finalresult.Add(gridData);
            }
            JObject sumRowData = new JObject(new JObject() { new JProperty("city", "Sum") });
            int index = 1;
            foreach (int year in last10Years)
            {
                var sum = result.Where(x => x.year == year) != null ? result.Where(x => x.year == year).Sum(x => x.emissionValue) : 0;
                sumRowData.Add(new JProperty("year" + index, sum));
                index++;
            }
            finalresult.Add(sumRowData);
            municipalityGridData.dataSource = JArray.FromObject(finalresult);
            municipalityGridData.columns = getPerCitizenGridColumn(last10Years);
            return municipalityGridData;
        }

        private async Task<List<ClimateDataAnalysisHelper>> FetchClimateDataPerGasType(int budgetYear, string gassType, string currentMunicipalityId, Dictionary<string, string> cities, List<string> emissionTypes = null)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            var result = !emissionTypes.Any() ? await (from a in dbContext.gco_climate_data
                                                       where a.year == budgetYear && currentMunicipalityId == a.fk_region_code && a.gass_type == gassType
                                                       group a by new { a.fk_region_code, a.sector } into res
                                                       select new ClimateDataAnalysisHelper
                                                       {
                                                           regionCode = res.Key.fk_region_code,
                                                           sector = res.Key.sector,
                                                           emissionValue = res.Sum(x => x.emission_value)
                                                       }).OrderBy(x => x.sector).AsNoTracking().ToListAsync()
                                : await (from a in dbContext.gco_climate_data
                                         where a.year == budgetYear && currentMunicipalityId == a.fk_region_code && a.gass_type == gassType && !emissionTypes.Contains(a.sector)
                                         group a by new { a.fk_region_code, a.sector } into res
                                         select new ClimateDataAnalysisHelper
                                         {
                                             regionCode = res.Key.fk_region_code,
                                             sector = res.Key.sector,
                                             emissionValue = res.Sum(x => x.emission_value)
                                         }).OrderBy(x => x.sector).AsNoTracking().ToListAsync();
            if (cities.Any())
            {
                var result1 = !emissionTypes.Any() ? await (from a in dbContext.gco_climate_data
                                                            where a.year == budgetYear && cities.Keys.Contains(a.fk_region_code) && a.gass_type == gassType
                                                            group a by new { a.fk_region_code, a.sector } into res
                                                            select new ClimateDataAnalysisHelper
                                                            {
                                                                regionCode = res.Key.fk_region_code,
                                                                sector = res.Key.sector,
                                                                emissionValue = res.Sum(x => x.emission_value)
                                                            }).OrderBy(x => x.sector).AsNoTracking().ToListAsync()
                                     : await (from a in dbContext.gco_climate_data
                                              where a.year == budgetYear && cities.Keys.Contains(a.fk_region_code) && a.gass_type == gassType && !emissionTypes.Contains(a.sector)
                                              group a by new { a.fk_region_code, a.sector } into res
                                              select new ClimateDataAnalysisHelper
                                              {
                                                  regionCode = res.Key.fk_region_code,
                                                  sector = res.Key.sector,
                                                  emissionValue = res.Sum(x => x.emission_value)
                                              }).OrderBy(x => x.sector).AsNoTracking().ToListAsync();

                result.AddRange(result1);
            }
            return result;
        }

        private async Task<List<ClimateDataAnalysisHelper>> FetchClimateDataForYearByGasType(List<int> Last5Years, string gassType, Dictionary<string, string> cities, List<string> emissionTypes)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            return !emissionTypes.Any() ? await (from a in dbContext.gco_climate_data
                                                 where Last5Years.Contains(a.year) && cities.Keys.Contains(a.fk_region_code)
                                                 && a.gass_type == gassType
                                                 group a by new { a.year, a.sector } into res
                                                 select new ClimateDataAnalysisHelper
                                                 {
                                                     year = res.Key.year,
                                                     sector = res.Key.sector,
                                                     emissionValue = res.Sum(x => x.emission_value)
                                                 }).OrderBy(x => x.year).AsNoTracking().ToListAsync()
                          : await (from a in dbContext.gco_climate_data
                                   where Last5Years.Contains(a.year) && cities.Keys.Contains(a.fk_region_code) && !emissionTypes.Contains(a.sector)
                                   && a.gass_type == gassType
                                   group a by new { a.year, a.sector } into res
                                   select new ClimateDataAnalysisHelper
                                   {
                                       year = res.Key.year,
                                       sector = res.Key.sector,
                                       emissionValue = res.Sum(x => x.emission_value)
                                   }).OrderBy(x => x.year).AsNoTracking().ToListAsync();
        }

        private JArray getMunicipalityGridColumn(Dictionary<string, string> cities)
        {
            int colCount = 0;
            dynamic columns = new JArray();
            cities = (new Dictionary<string, string> { { "sector", " " } }).Concat(cities).ToDictionary(k => k.Key, v => v.Value);
            foreach (var item in cities)
            {
                dynamic column = new JObject();
                column.title = item.Value;
                if (item.Key == "sector")
                {
                    column.field = "sector";
                    column.width = 40;
                    dynamic attributes = new JObject();
                    attributes.style = "text-align:left;white-space:normal;";
                    column.attributes = attributes;
                    dynamic headerAttributes = new JObject();
                    headerAttributes.style = "text-align:left;";
                    column.headerAttributes = headerAttributes;
                    column.template = null;
                }
                else
                {
                    column.field = "city" + colCount;
                    dynamic attributes = new JObject();
                    attributes.style = "text-align:right;white-space:normal;";
                    column.attributes = attributes;
                    dynamic headerAttributes = new JObject();
                    headerAttributes.style = "text-align:right;";
                    column.headerAttributes = headerAttributes;
                    column.width = 30;
                    column.template = "#= (kendo.toString(" + column.field + " ,'n1')) #";
                }
                column.colCount = colCount;
                column.encoded = false;
                column.format = null;
                column.expandable = false;
                column.hidden = false;

                dynamic filterable = new JObject();
                dynamic cell = new JObject();
                cell.enabled = true;
                cell.showOperators = false;
                dynamic dataSource = new JArray();
                cell.dataSource = dataSource;
                cell.operators = "contains";
                filterable.cell = cell;
                column.filterable = filterable;
                columns.Add(column);
                colCount++;
            }
            return columns;
        }

        private JArray getYearGridColumn(List<int> last5Years)
        {
            int colCount = 0;
            dynamic columns = new JArray();
            last5Years.Insert(0, 1);
            foreach (var item in last5Years)
            {
                dynamic column = new JObject();

                if (item == 1)
                {
                    column.title = " ";
                    column.field = "sector";
                    column.width = 40;
                    dynamic attributes = new JObject();
                    attributes.style = "text-align:left;white-space:normal;";
                    column.attributes = attributes;
                    dynamic headerAttributes = new JObject();
                    headerAttributes.style = "text-align:left;";
                    column.headerAttributes = headerAttributes;
                    column.template = null;
                }
                else
                {
                    column.title = item.ToString();
                    column.field = "year" + colCount;
                    dynamic attributes = new JObject();
                    attributes.style = "text-align:right;white-space:normal;";
                    column.attributes = attributes;
                    dynamic headerAttributes = new JObject();
                    headerAttributes.style = "text-align:right;";
                    column.headerAttributes = headerAttributes;
                    column.width = 30;
                    column.template = "#= (kendo.toString(" + column.field + " ,'n1')) #";
                }
                column.colCount = colCount;
                column.encoded = false;
                column.format = null;
                column.expandable = false;
                column.hidden = false;

                dynamic filterable = new JObject();
                dynamic cell = new JObject();
                cell.enabled = true;
                cell.showOperators = false;
                dynamic dataSource = new JArray();
                cell.dataSource = dataSource;
                cell.operators = "contains";
                filterable.cell = cell;
                column.filterable = filterable;
                columns.Add(column);
                colCount++;
            }
            return columns;
        }

        private JArray getPerGasTypeGridColumn(int budgetYear)
        {
            List<string> gasTypeColumn = new List<string> { "sector", "totalEmission" };
            int colCount = 0;
            dynamic columns = new JArray();
            foreach (var item in gasTypeColumn)
            {
                dynamic column = new JObject();

                if (item == "sector")
                {
                    column.title = " ";
                    column.field = "sector";
                    column.width = 40;
                    dynamic attributes = new JObject();
                    attributes.style = "text-align:left;white-space:normal;";
                    column.attributes = attributes;
                    dynamic headerAttributes = new JObject();
                    headerAttributes.style = "text-align:left;";
                    column.headerAttributes = headerAttributes;
                    column.template = null;
                }
                else
                {
                    column.title = budgetYear.ToString();
                    column.field = item;
                    dynamic attributes = new JObject();
                    attributes.style = "text-align:right;white-space:normal;";
                    column.attributes = attributes;
                    dynamic headerAttributes = new JObject();
                    headerAttributes.style = "text-align:right;";
                    column.headerAttributes = headerAttributes;
                    column.width = 30;
                    column.template = "#= (kendo.toString(" + column.field + " ,'n2')) #";
                }
                column.colCount = colCount;
                column.encoded = false;
                column.format = null;
                column.expandable = false;
                column.hidden = false;

                dynamic filterable = new JObject();
                dynamic cell = new JObject();
                cell.enabled = true;
                cell.showOperators = false;
                dynamic dataSource = new JArray();
                cell.dataSource = dataSource;
                cell.operators = "contains";
                filterable.cell = cell;
                column.filterable = filterable;
                columns.Add(column);
                colCount++;
            }
            return columns;
        }

        private JArray getPerCitizenGridColumn(List<int> last4Years)
        {
            int colCount = 0;
            dynamic columns = new JArray();
            last4Years.Insert(0, 1);
            foreach (var item in last4Years)
            {
                dynamic column = new JObject();

                if (item == 1)
                {
                    column.title = " ";
                    column.field = "city";
                    column.width = 40;
                    dynamic attributes = new JObject();
                    attributes.style = "text-align:left;white-space:normal;";
                    column.attributes = attributes;
                    dynamic headerAttributes = new JObject();
                    headerAttributes.style = "text-align:left;";
                    column.headerAttributes = headerAttributes;
                    column.template = null;
                }
                else
                {
                    column.title = item.ToString();
                    column.field = "year" + colCount;
                    dynamic attributes = new JObject();
                    attributes.style = "text-align:right;white-space:normal;";
                    column.attributes = attributes;
                    dynamic headerAttributes = new JObject();
                    headerAttributes.style = "text-align:right;";
                    column.headerAttributes = headerAttributes;
                    column.width = 30;
                    column.template = "#= (kendo.toString(" + column.field + " ,'n1')) #";
                }
                column.colCount = colCount;
                column.encoded = false;
                column.format = null;
                column.expandable = false;
                column.hidden = false;

                dynamic filterable = new JObject();
                dynamic cell = new JObject();
                cell.enabled = true;
                cell.showOperators = false;
                dynamic dataSource = new JArray();
                cell.dataSource = dataSource;
                cell.operators = "contains";
                filterable.cell = cell;
                column.filterable = filterable;
                columns.Add(column);
                colCount++;
            }
            return columns;
        }

        public string SaveClimateDataAnalysisDescription(clsIndicatorEvaluation evaluationContent, string userId)
        {
            evaluationContent.evaluation = _utility.DecodeHtmlString(evaluationContent.evaluation, userId).GetAwaiter().GetResult();
            evaluationContent.indicatorCode = "CLIMATE_DATA_ANALYSIS";
            int budgetYear = Convert.ToInt32(evaluationContent.quarterValue.ToString().Substring(0, 4));
            _kostra.SaveIndicatorEvaluation(evaluationContent, userId, budgetYear);
            return "SUCCESS";
        }

        public clsIndicatorEvaluation GetClimateDataAnalysisDescription(clsIndicatorEvaluation evaluationContent, string userId)
        {
            evaluationContent.indicatorCode = "CLIMATE_DATA_ANALYSIS";
            int budgetYear = Convert.ToInt32(evaluationContent.quarterValue.ToString().Substring(0, 4));
            evaluationContent = _kostra.GetEvaluationContent(evaluationContent, userId, budgetYear);
            return evaluationContent;
        }

        public void SaveSectionConfig(string userId, ClimateDataAnalysisSectionConfig config)
        {
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            UserData userDetails = _utility.GetUserDetails(userId);
            string sectionId = "CLIMATE_DATA_ANALYSIS";
            tco_section_config sectionConfig = dbContext.tco_section_config.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id &&
                                                          x.fk_template_id == config.templateId && x.fk_indicator_code == config.budgetYear.ToString() && x.section_id == sectionId);
            if (sectionConfig != null)
            {
                sectionConfig.include_in_document = config.includeInDoc;
                sectionConfig.updated = DateTime.UtcNow;
                sectionConfig.updated_by = userDetails.pk_id;
                sectionConfig.config = JsonConvert.SerializeObject(config);
            }
            else
            {
                sectionConfig = new tco_section_config
                {
                    fk_tenant_id = userDetails.tenant_id,
                    config = JsonConvert.SerializeObject(config),
                    fk_indicator_code = config.budgetYear.ToString(),
                    fk_template_id = config.templateId,
                    section_id = sectionId,
                    include_in_document = config.includeInDoc,
                    updated = DateTime.UtcNow,
                    updated_by = userDetails.pk_id
                };
                dbContext.tco_section_config.Add(sectionConfig);
            }
            dbContext.SaveChanges();
        }

        public ClimateDataAnalysisSectionConfig GetSectionConfig(string userId, Guid templateId, int budgetYear)
        {
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            UserData userDetails = _utility.GetUserDetails(userId);
            string sectionId = "CLIMATE_DATA_ANALYSIS";
            tco_section_config sectionConfig = dbContext.tco_section_config.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id &&
                                                          x.fk_template_id == templateId && x.fk_indicator_code == budgetYear.ToString() && x.section_id == sectionId);
            if (sectionConfig != null)
            {
                var result = JsonConvert.DeserializeObject<ClimateDataAnalysisSectionConfig>(sectionConfig.config);
                result.includeMunicipalityTables.total = result.includeMunicipalityGraph == true ? true : result.includeMunicipalityTables.total;
                result.includePerCitizenTables.total = result.includePerCitizenGraph == true ? true : result.includePerCitizenTables.total;
                result.includePerGassTables.total = result.includePerGassTypeGraph == true ? true : result.includePerGassTables.total;
                result.includeYearTables.total = result.includeYearGraph == true ? true : result.includeYearTables.total;
                sectionConfig.config = JsonConvert.SerializeObject(result);
                dbContext.SaveChanges();
                return result;
            }
            else
            {
                return new ClimateDataAnalysisSectionConfig { templateId = templateId };
            }
        }

        private List<string> GetParameterValue(string userId)
        {
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            UserData userDetails = _utility.GetUserDetails(userId);
            List<string> emissionTypes = dbContext.tco_parameters.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.param_name == "HIDE_EMISSION_TYPE" && x.active == 1 && !string.IsNullOrEmpty(x.param_value)).Select(x => x.param_value).ToList();
            return emissionTypes;
        }
    }
}