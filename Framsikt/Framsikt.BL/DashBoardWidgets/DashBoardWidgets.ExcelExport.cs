using Framsikt.BL.Helpers;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Framsikt.BL;

public partial class DashBoardWidgets : IDashBoardWidgets
{


    //#region Dashbodard Excel Export
    /// <summary>
    /// write excel export request o queue
    /// </summary>
    /// <param name="userName"></param>
    /// <param name="input"></param>
    ///  <param name="columnList"></param>
    public async Task WriteToExportToExcelqueue(string userName, AccountStmtExcelExportQueueInput input, List<KeyValueExcelExport> columnList)
    {
        try
        {
            UserData userDetails = _utility.GetUserDetails(userName);


            string jobType = string.Empty;
            if (input.isPerPeriodGrid)// check if its period grid
            {
                jobType = "DasBoardWidgetExcelExport_perPeriod";
            }
            else
            {

                jobType = "DasBoardWidgetExcelExport_View_" + input.viewType;// diffrent job for view type


            }



            dynamic dashBoardExcelExportRequest = new JObject();
            dashBoardExcelExportRequest.Add("userName", userName);
            dashBoardExcelExportRequest.Add("tenantId", userDetails.tenant_id);
            dashBoardExcelExportRequest.Add("budgetYear", input.budgetYear);
            dashBoardExcelExportRequest.Add("forecastPeriod", input.forecastPeriod);
            dashBoardExcelExportRequest.Add("isPerPeriodGrid", input.isPerPeriodGrid);
            dashBoardExcelExportRequest.Add("viewType", input.viewType);
            dashBoardExcelExportRequest.Add("orgId", input.orgId);
            dashBoardExcelExportRequest.Add("orgLevel", input.orgLevel);
            dashBoardExcelExportRequest.Add("columnsList", JArray.FromObject(columnList));

            TcoJobStatus jobStatus = new TcoJobStatus
            {
                JobType = jobType,
                StartTime = DateTime.UtcNow,
                TotalSteps = 3, //setting this to a high default value
                StepsCompleted = 0,
                UserId = userDetails.pk_id,
                TenantId = userDetails.tenant_id,
                request_data_blob_url = JsonConvert.SerializeObject(dashBoardExcelExportRequest)

            };
            _unitOfWork.GenericRepo.Add(jobStatus);
            await _unitOfWork.CompleteAsync();


            dashBoardExcelExportRequest.Add("jobId", jobStatus.PkId);

            string strDashBoardExcelExportRequest = JsonConvert.SerializeObject(dashBoardExcelExportRequest);


            _backendJob.QueueMessage(userDetails, new Dictionary<string, int>(), QueueName.dashboradwidgetexportqueue, strDashBoardExcelExportRequest);
        }
        catch (Exception e)
        {
            Console.Write(e.ToString());
        }
    }



    public async Task<IEnumerable<AccStmtExcelExportBaseData>> GetDataForExcelExport(AccountStmtExcelExportInput input)
    {
        try
        {
            string type = "LOCK_ORIGINAL_BUDGET";
            ClsOrgVersionSpecificContent orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(input.userName, input.tenantId, input.forecastPeriod);
            bool isLocked = await _utility.CheckOnYearlyBudgetLockByBudgetYearAsync(orgVersionContent, input.tenantId, input.userName, input.budgetYear, input.orgId, input.orgLevel.ToString(), type);
            List<AccStmtExcelExportBaseData> mergedData = new List<AccStmtExcelExportBaseData>();
            List<AccStmtExcelExportBaseData> finalData = new List<AccStmtExcelExportBaseData>();
            int accountLevel = _unitOfWork.DashBoardWidgetsRepo.FetchValidAccountLevel(input.tenantId, "ACCSTATE_ACCOUNTS_DISP_LEVEL");


            AccStmtBudgetAndAccountDataForExcel data = await _unitOfWork.DashBoardWidgetsRepo.GetAccountStatmentDataForExcel(input, isLocked);
            var deviation = await _unitOfWork.DashBoardWidgetsRepo.GetMonthrepDevitaion(input.tenantId);
            mergedData = MergeAccAndBudget_ForExcel(data.accData, data.budgetData, data.originalBudgetData, input.viewType, input.forecastPeriod);// merge the tow data set into 1

            if (input.viewType == "0" || input.viewType == "2")
            {
                List<AccStmtExcelExportBaseData> mergedDataMissingDept_OrgSetup = MergeAccAndBudgetMissingDept_OrgSetup_ForExcel(data.accDataMissingDept_OrgSetup, data.budgetDataMissingDept_OrgSetup, data.originalBudgetDataMissingDept_OrgSetup, deviation.ToDictionary(x => x.param_name, x => x.param_value), input);// merge the tow data set into 1

                mergedData.InsertRange(0, mergedDataMissingDept_OrgSetup);
            }


            int prevYrSamePeriod = input.forecastPeriod - 100;
            int prevYr = input.budgetYear - 1;
            List<AccStmtExcelExportBaseData> previousYear = mergedData.Where(x => x.BudgetYear == prevYr).ToList();
            List<AccStmtExcelExportBaseData> currentPeriodData = mergedData.Where(x => x.Period == input.forecastPeriod).ToList();
            if (currentPeriodData.Any())
            {
                List<AccStmtExcelExportBaseData> dataBeforeRemovingRowsWillAllZero = new List<AccStmtExcelExportBaseData>();
                dataBeforeRemovingRowsWillAllZero.AddRange(currentPeriodData);

                dataBeforeRemovingRowsWillAllZero.ForEach(x => x.AccLastYr = previousYear.Where(y => y.orgId == x.orgId && y.Org_id_nextLevel == x.Org_name_nextLevel && x.Level_1_id == y.Level_1_id && x.Level_2_id == y.Level_2_id && x.Level_3_id == y.Level_3_id && x.Level_4_id == y.Level_4_id && x.Level_5_id == y.Level_5_id && y.BudgetYear == prevYr && y.org_setup_missing == x.org_setup_missing && y.department_setup_missing == x.department_setup_missing).Sum(z => z.AccPeriod));
                dataBeforeRemovingRowsWillAllZero.ForEach(x => x.AccLastYrYtd = previousYear.Where(y => y.orgId == x.orgId && y.Org_id_nextLevel == x.Org_name_nextLevel && x.Level_1_id == y.Level_1_id && x.Level_2_id == y.Level_2_id && x.Level_3_id == y.Level_3_id && x.Level_4_id == y.Level_4_id && x.Level_5_id == y.Level_5_id && y.BudgetYear == prevYr && y.Period <= prevYrSamePeriod && y.org_setup_missing == x.org_setup_missing && y.department_setup_missing == x.department_setup_missing).Sum(z => z.AccPeriod));

                List<AccStmtExcelExportBaseData> dataAfterRemovingAllZeroColumnsData = NonZeroDataRowsExcel(dataBeforeRemovingRowsWillAllZero); // remove all reows with zero
                finalData.AddRange(dataAfterRemovingAllZeroColumnsData);
            }





            return finalData;
        }
        catch (Exception ex)
        {

            throw new Exception(ex.Message);
        }

    }



    private List<AccStmtExcelExportBaseData> MergeAccAndBudget_ForExcel(List<AccStmtExcelExportBaseData> accounting, List<AccStmtExcelExportBaseData> budget, List<AccStmtExcelExportBaseData> originalBudget, string viewType, int forecastPeriod)
    {
        //Common items
        var merged = (from acc in accounting
            join bud in budget
                on new { acc.orgId, acc.Org_id_nextLevel, acc.Id, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, acc.Period, acc.BudgetYear }
                equals new { bud.orgId, bud.Org_id_nextLevel, bud.Id, bud.Level_1_id, bud.Level_2_id, bud.Level_3_id, bud.Level_4_id, bud.Level_5_id, bud.Period, bud.BudgetYear }
            //join obud in originalBudget
            // on new { acc.Id, acc.Period, acc.BudgetYear } equals new { obud.Id, obud.Period, obud.BudgetYear }
            select new AccStmtExcelExportBaseData()
            {
                orgId = acc.orgId.Trim(),
                Org_id_nextLevel = acc.Org_id_nextLevel.Trim(),
                Org_name = acc.Org_name.Trim(),
                Org_name_nextLevel = acc.Org_name_nextLevel.Trim(),
                Level_1_id = acc.Level_1_id,
                Level_1_description = acc.Level_1_description.Trim(),
                Level_2_id = acc.Level_2_id.Trim(),
                Level_2_description = acc.Level_2_description.Trim(),
                Level_3_id = acc.Level_3_id.Trim(),
                Level_3_description = acc.Level_3_description.Trim(),
                Level_4_id = acc.Level_4_id.Trim(),
                Level_4_description = acc.Level_4_description.Trim(),
                Level_5_id = acc.Level_5_id.Trim(),
                Level_5_description = acc.Level_5_description.Trim(),
                Id = acc.Id,
                Parent = acc.Parent,
                HasChildren = acc.HasChildren,
                Name = acc.Name,
                Period = acc.Period,
                AccPeriod = acc.AccPeriod,
                BudPeriod = bud.BudPeriod,
                BudgetYear = bud.BudgetYear,
                DevPeriod = 0,
                DevPeriodPct = 0,
                BudYtd = bud.BudYtd,
                AccYtd = acc.AccYtd,
                DevBudgetYtd = 0,
                DevBudgetYtdPct = 0,
                AnnualBudget = bud.AnnualBudget,
                UsagePct = 0,
                AccLastYr = 0,
                AccLastYrYtd = 0,
                //originalBudgetPeriod=obud.originalBudgetPeriod
            }).ToList();

        AccStmtExcelDataComparer comparer = new AccStmtExcelDataComparer();
        //Items only in accounting
        var onlyAcc = accounting.Except(budget, comparer).ToList();

        //Items only in budget
        var onlyBud = budget.Except(accounting, comparer).ToList();

        merged.AddRange(onlyAcc);
        merged.AddRange(onlyBud);

        merged = (from acc in merged
            join obud in originalBudget
                on new { acc.orgId, acc.Org_id_nextLevel, acc.Id, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, acc.Period, acc.BudgetYear }
                equals new { obud.orgId, obud.Org_id_nextLevel, obud.Id, obud.Level_1_id, obud.Level_2_id, obud.Level_3_id, obud.Level_4_id, obud.Level_5_id, obud.Period, obud.BudgetYear } into grp
            from grp1 in grp.DefaultIfEmpty()
            select new AccStmtExcelExportBaseData()
            {
                orgId = acc.orgId.Trim(),
                Org_id_nextLevel = acc.Org_id_nextLevel.Trim(),
                Org_name = acc.Org_name.Trim(),
                Org_name_nextLevel = acc.Org_name_nextLevel.Trim(),
                Level_1_id = acc.Level_1_id.Trim(),
                Level_1_description = acc.Level_1_description.Trim(),
                Level_2_id = acc.Level_2_id.Trim(),
                Level_2_description = acc.Level_2_description.Trim(),
                Level_3_id = acc.Level_3_id.Trim(),
                Level_3_description = acc.Level_3_description.Trim(),
                Level_4_id = acc.Level_4_id.Trim(),
                Level_4_description = acc.Level_4_description.Trim(),
                Level_5_id = acc.Level_5_id.Trim(),
                Level_5_description = acc.Level_5_description.Trim(),
                Id = acc.Id,
                Parent = acc.Parent,
                HasChildren = acc.HasChildren,
                Name = acc.Name,
                Period = acc.Period,
                AccPeriod = acc.AccPeriod,
                BudPeriod = acc.BudPeriod,
                BudgetYear = acc.BudgetYear,
                DevPeriod = 0,
                DevPeriodPct = 0,
                BudYtd = acc.BudYtd,
                AccYtd = acc.AccYtd,
                DevBudgetYtd = 0,
                DevBudgetYtdPct = 0,
                AnnualBudget = acc.AnnualBudget,
                UsagePct = 0,
                AccLastYr = 0,
                AccLastYrYtd = 0,
                originalBudgetPeriod = grp1?.originalBudgetPeriod ?? 0
            }).ToList();

        //Item only in OriginalBug
        var onlyOrignalBud = originalBudget.Except(merged, comparer).ToList();
        merged.AddRange(onlyOrignalBud);

        List<AccStmtExcelExportBaseData> missIdataForCurrentPeriod = new List<AccStmtExcelExportBaseData>();
        AccStmtExcelExportBaseData temp = null;
        foreach (var lineItem in merged)
        {
            if (!merged.Any(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.Period == forecastPeriod) && !missIdataForCurrentPeriod.Any(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id)) // check if the item is not pressent for current , if not there then add
            {

                var budYtd = merged.Where(z => z.Period <= forecastPeriod && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100)).Sum(x=> x.BudPeriod);
                var annualBudget = (merged.Any(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100) && z.AnnualBudget != 0)) ? merged.FirstOrDefault(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100) && z.AnnualBudget != 0).AnnualBudget : 0;

                var AccYtd = merged.Where(z => z.Period <= forecastPeriod && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100)).Sum(z => z.AccPeriod);

                var validOriginalPeriod = merged.Where(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.Period <= forecastPeriod && z.BudgetYear == (forecastPeriod / 100) && z.originalBudgetPeriod != 0).Any() ?
                    merged.Where(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.Period <= forecastPeriod && z.originalBudgetPeriod != 0).Max(x => x.Period) :
                    forecastPeriod;// last period with valid data for accounting this is done when there is no data in table for current table then the ytd will be the value of the previous period

                var originalBudgetPeriod = (merged.FirstOrDefault(z => z.Period == validOriginalPeriod && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100)) != null && merged.FirstOrDefault(z => z.Period == validOriginalPeriod && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100)).originalBudgetPeriod != 0) ?
                    merged.FirstOrDefault(z => z.Period == validOriginalPeriod && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100)).originalBudgetPeriod
                    : 0;
                temp = new AccStmtExcelExportBaseData()
                {
                    orgId = lineItem.orgId,
                    Org_id_nextLevel = lineItem.Org_id_nextLevel,
                    Org_name = lineItem.Org_name,
                    Org_name_nextLevel = lineItem.Org_name_nextLevel,
                    Level_1_id = lineItem.Level_1_id,
                    Level_1_description = lineItem.Level_1_description,
                    Level_2_id = lineItem.Level_2_id,
                    Level_2_description = lineItem.Level_2_description,
                    Level_3_id = lineItem.Level_3_id,
                    Level_3_description = lineItem.Level_3_description,
                    Level_4_id = lineItem.Level_4_id,
                    Level_4_description = lineItem.Level_4_description,
                    Level_5_id = lineItem.Level_5_id,
                    Level_5_description = lineItem.Level_5_description,
                    Id = lineItem.Id,
                    Parent = lineItem.Parent,
                    HasChildren = lineItem.HasChildren,
                    Name = lineItem.Name,
                    Period = forecastPeriod,
                    AccPeriod = 0,
                    BudPeriod = 0,
                    BudgetYear = forecastPeriod / 100,
                    DevPeriod = 0,
                    DevPeriodPct = 0,
                    BudYtd = budYtd,
                    AccYtd = AccYtd,
                    DevBudgetYtd = 0,
                    DevBudgetYtdPct = 0,
                    AnnualBudget = annualBudget,
                    UsagePct = 0,
                    AccLastYr = 0,
                    AccLastYrYtd = 0,
                    originalBudgetPeriod = originalBudgetPeriod,
                    // level = requestedTreeLevel == 0 ? 1 : requestedTreeLevel,
                    // isItalic = SetItalicProperty(viewType, requestedTreeLevel),
                };
                missIdataForCurrentPeriod.Add(temp);
            }

            if (lineItem.Period == forecastPeriod)// this is for data missing in bud or accounting causing  ytd be zero for current period
            {
                lineItem.BudYtd = merged.Where(z => z.Period <= forecastPeriod && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100)).Sum(z => z.BudPeriod);

                lineItem.AnnualBudget = (lineItem.AnnualBudget == 0 && merged.Any(z => z.BudgetYear == (forecastPeriod / 100) && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.AnnualBudget != 0)) ? merged.FirstOrDefault(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100) && z.AnnualBudget != 0).AnnualBudget : lineItem.AnnualBudget;

                lineItem.AccYtd = merged.Where(z => z.Period <= forecastPeriod && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100)).Sum(z => z.AccPeriod);
            }

        }

        if (missIdataForCurrentPeriod.Any())
        {
            merged.AddRange(missIdataForCurrentPeriod);// ad the missing data to the list
        }

        // calculate deviation pct and ytd for the merged data
        merged.ForEach(lineItem =>
        {
            lineItem.DevPeriod = lineItem.BudPeriod - lineItem.AccPeriod;
            lineItem.DevPeriodPct = lineItem.BudPeriod != 0 ? ((lineItem.BudPeriod - lineItem.AccPeriod) / (Math.Abs(lineItem.BudPeriod))) : 0;
            lineItem.DevBudgetYtd = lineItem.BudYtd - lineItem.AccYtd;
            lineItem.DevBudgetYtdPct = lineItem.BudYtd != 0 ? ((lineItem.BudYtd - lineItem.AccYtd) / (Math.Abs(lineItem.BudYtd))) : 0;
            lineItem.UsagePct = lineItem.AnnualBudget != 0 ? ((lineItem.AccYtd) / (Math.Abs(lineItem.AnnualBudget))) : 0;
            //  lineItem.trafficLight = Math.Abs(lineItem.BudYtd) > clsConstants.mrBudgetBlankLimitAmount ? GetTrafficLight(((lineItem.BudYtd - lineItem.AccYtd) / Math.Abs(lineItem.BudYtd)) * 100, deviationsKey) : string.Empty;
            //  lineItem.level = requestedTreeLevel == 0 ? 1 : requestedTreeLevel;
            //lineItem.isItalic = SetItalicProperty(viewType, requestedTreeLevel);
            lineItem.budgetChange = lineItem.AnnualBudget - lineItem.originalBudgetPeriod;
        });

        return merged.OrderBy(z => z.orgId).ThenBy(z => z.Org_id_nextLevel).ThenBy(z => z.Level_1_id).ThenBy(z => z.Level_2_id).ThenBy(z => z.Level_3_id).ThenBy(z => z.Level_4_id).ThenBy(z => z.Level_5_id).ToList();
    }




    private List<AccStmtExcelExportBaseData> MergeAccAndBudgetMissingDept_OrgSetup_ForExcel(List<AccStmtExcelExportBaseData> accounting, List<AccStmtExcelExportBaseData> budget, List<AccStmtExcelExportBaseData> originalBudget, Dictionary<string, string> deviationsKey, AccountStmtExcelExportInput gridInput)
    {
        int forecastPeriod = gridInput.forecastPeriod;
        //  int requestedTreeLevel = gridInput.RequestedTreeLevel;
        string viewType = gridInput.viewType;
        //Common items
        var merged = (from acc in accounting
            join bud in budget on new { acc.orgId, acc.Org_id_nextLevel, acc.Id, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, acc.Period, acc.BudgetYear, acc.department_setup_missing, acc.org_setup_missing }
                equals new { bud.orgId, bud.Org_id_nextLevel, bud.Id, bud.Level_1_id, bud.Level_2_id, bud.Level_3_id, bud.Level_4_id, bud.Level_5_id, bud.Period, bud.BudgetYear, bud.department_setup_missing, bud.org_setup_missing }
            //join obud in originalBudget
            // on new { acc.Id, acc.Period, acc.BudgetYear } equals new { obud.Id, obud.Period, obud.BudgetYear }
            select new AccStmtExcelExportBaseData()
            {
                orgId = acc.orgId,
                Org_id_nextLevel = acc.Org_id_nextLevel,
                Org_name = acc.Org_name,
                Org_name_nextLevel = acc.Org_name_nextLevel,
                Level_1_id = acc.Level_1_id,
                Level_1_description = acc.Level_1_description,
                Level_2_id = acc.Level_2_id,
                Level_2_description = acc.Level_2_description,
                Level_3_id = acc.Level_3_id,
                Level_3_description = acc.Level_3_description,
                Level_4_id = acc.Level_4_id,
                Level_4_description = acc.Level_4_description,
                Level_5_id = acc.Level_5_id,
                Level_5_description = acc.Level_5_description,
                Id = acc.Id,
                Parent = acc.Parent,
                HasChildren = acc.HasChildren,
                Name = acc.Name,
                Period = acc.Period,
                AccPeriod = acc.AccPeriod,
                BudPeriod = bud.BudPeriod,
                BudgetYear = bud.BudgetYear,
                DevPeriod = 0,
                DevPeriodPct = 0,
                BudYtd = bud.BudYtd,
                AccYtd = acc.AccYtd,
                DevBudgetYtd = 0,
                DevBudgetYtdPct = 0,
                AnnualBudget = bud.AnnualBudget,
                UsagePct = 0,
                AccLastYr = 0,
                AccLastYrYtd = 0,
                // orgId = acc.orgId,
                department_setup_missing = acc.department_setup_missing,
                org_setup_missing = acc.org_setup_missing,
            }).ToList();

        AccStmtExcelDataComparer comparer = new AccStmtExcelDataComparer();
        //Items only in accounting
        var onlyAcc = accounting.Except(budget, comparer).ToList();

        //Items only in budget
        var onlyBud = budget.Except(accounting, comparer).ToList();

        merged.AddRange(onlyAcc);
        merged.AddRange(onlyBud);

        merged = (from acc in merged
            join obud in originalBudget
                on new { acc.orgId, acc.Org_id_nextLevel, acc.Id, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, acc.Period, acc.BudgetYear, acc.department_setup_missing, acc.org_setup_missing }
                equals new { obud.orgId, obud.Org_id_nextLevel, obud.Id, obud.Level_1_id, obud.Level_2_id, obud.Level_3_id, obud.Level_4_id, obud.Level_5_id, obud.Period, obud.BudgetYear, obud.department_setup_missing, obud.org_setup_missing } into grp
            from grp1 in grp.DefaultIfEmpty()
            select new AccStmtExcelExportBaseData()
            {

                orgId = acc.orgId,
                Org_id_nextLevel = acc.Org_id_nextLevel,
                Org_name = acc.Org_name,
                Org_name_nextLevel = acc.Org_name_nextLevel,
                Level_1_id = acc.Level_1_id,
                Level_1_description = acc.Level_1_description,
                Level_2_id = acc.Level_2_id,
                Level_2_description = acc.Level_2_description,
                Level_3_id = acc.Level_3_id,
                Level_3_description = acc.Level_3_description,
                Level_4_id = acc.Level_4_id,
                Level_4_description = acc.Level_4_description,
                Level_5_id = acc.Level_5_id,
                Level_5_description = acc.Level_5_description,
                Id = acc.Id,
                Parent = acc.Parent,
                HasChildren = acc.HasChildren,
                Name = acc.Name,
                Period = acc.Period,
                AccPeriod = acc.AccPeriod,
                BudPeriod = acc.BudPeriod,
                BudgetYear = acc.BudgetYear,
                DevPeriod = 0,
                DevPeriodPct = 0,
                BudYtd = acc.BudYtd,
                AccYtd = acc.AccYtd,
                DevBudgetYtd = 0,
                DevBudgetYtdPct = 0,
                AnnualBudget = acc.AnnualBudget,
                UsagePct = 0,
                AccLastYr = 0,
                AccLastYrYtd = 0,
                originalBudgetPeriod = grp1?.originalBudgetPeriod ?? 0,
                //orgId = acc.orgId,
                org_setup_missing = acc.org_setup_missing,
                department_setup_missing = acc.department_setup_missing
            }).ToList();

        //Item only in OriginalBug
        var onlyOrignalBud = originalBudget.Except(merged, comparer).ToList();
        merged.AddRange(onlyOrignalBud);

        List<AccStmtExcelExportBaseData> missIdataForCurrentPeriod = new List<AccStmtExcelExportBaseData>();
        AccStmtExcelExportBaseData temp = null;

        if (gridInput.orgLevel != 1)
        {
            merged = merged.Where(x => x.orgId == gridInput.orgId).ToList();
        }

        foreach (var lineItem in merged)// calculate deviation pct and ytd for the merged data
        {
            if (!merged.Any(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.Period == forecastPeriod && z.department_setup_missing == lineItem.department_setup_missing && z.org_setup_missing == lineItem.org_setup_missing) && !missIdataForCurrentPeriod.Any(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.department_setup_missing == lineItem.department_setup_missing && z.org_setup_missing == lineItem.org_setup_missing)) // check if the item is not pressent for current , if not there then add
            {
                var budYtd = merged.Where(z => z.Period <= forecastPeriod && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100) && z.department_setup_missing == lineItem.department_setup_missing && z.org_setup_missing == lineItem.org_setup_missing).Sum(x => x.BudPeriod);
                var AccYtd = merged.Where(z => z.Period <= forecastPeriod && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100) && z.department_setup_missing == lineItem.department_setup_missing && z.org_setup_missing == lineItem.org_setup_missing).Sum(x => x.AccPeriod);
                temp = new AccStmtExcelExportBaseData()
                {

                    orgId = lineItem.orgId,
                    Org_id_nextLevel = lineItem.Org_id_nextLevel,
                    Org_name = lineItem.Org_name,
                    Org_name_nextLevel = lineItem.Org_name_nextLevel,
                    Level_1_id = lineItem.Level_1_id,
                    Level_1_description = lineItem.Level_1_description,
                    Level_2_id = lineItem.Level_2_id,
                    Level_2_description = lineItem.Level_2_description,
                    Level_3_id = lineItem.Level_3_id,
                    Level_3_description = lineItem.Level_3_description,
                    Level_4_id = lineItem.Level_4_id,
                    Level_4_description = lineItem.Level_4_description,
                    Level_5_id = lineItem.Level_5_id,
                    Level_5_description = lineItem.Level_5_description,
                    Id = lineItem.Id,
                    Parent = lineItem.Parent,
                    HasChildren = lineItem.HasChildren,
                    Name = lineItem.Name,
                    Period = forecastPeriod,
                    AccPeriod = 0,
                    BudPeriod = 0,
                    BudgetYear = forecastPeriod / 100,
                    DevPeriod = 0,
                    DevPeriodPct = 0,
                    BudYtd = budYtd,
                    AccYtd = AccYtd,
                    DevBudgetYtd = 0,
                    DevBudgetYtdPct = 0,
                    AnnualBudget = 0,
                    UsagePct = 0,
                    AccLastYr = 0,
                    AccLastYrYtd = 0,
                    originalBudgetPeriod = 0,
                    //level = requestedTreeLevel == 0 ? 1 : requestedTreeLevel,
                    isItalic = false,
                    department_setup_missing = lineItem.department_setup_missing,
                    org_setup_missing = lineItem.org_setup_missing
                };
                missIdataForCurrentPeriod.Add(temp);
            }

            if (lineItem.Period == forecastPeriod)// this is for data missing in bud or accounting causing  ytd be zero for current period
            {
                lineItem.BudYtd = merged.Where(z => z.Period <= forecastPeriod && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100) && z.department_setup_missing == lineItem.department_setup_missing && z.org_setup_missing == lineItem.org_setup_missing).Sum(x => x.BudPeriod);
                lineItem.AccYtd = merged.Where(z => z.Period <= forecastPeriod && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100) && z.department_setup_missing == lineItem.department_setup_missing && z.org_setup_missing == lineItem.org_setup_missing).Sum(x => x.AccPeriod);
            }
            lineItem.DevPeriod = lineItem.BudPeriod - lineItem.AccPeriod;
            lineItem.DevPeriodPct = lineItem.BudPeriod != 0 ? ((lineItem.BudPeriod - lineItem.AccPeriod) / (Math.Abs(lineItem.BudPeriod))) : 0;
            lineItem.DevBudgetYtd = lineItem.BudYtd - lineItem.AccYtd;
            lineItem.DevBudgetYtdPct = lineItem.BudYtd != 0 ? ((lineItem.BudYtd - lineItem.AccYtd) / (Math.Abs(lineItem.BudYtd))) : 0;
            lineItem.UsagePct = lineItem.AnnualBudget != 0 ? ((lineItem.AccYtd) / (Math.Abs(lineItem.AnnualBudget))) : 0;
            // lineItem.trafficLight = Math.Abs(lineItem.BudYtd) > clsConstants.mrBudgetBlankLimitAmount ? GetTrafficLight(((lineItem.BudYtd - lineItem.AccYtd) / Math.Abs(lineItem.BudYtd)) * 100, deviationsKey) : string.Empty;
            // lineItem.level = requestedTreeLevel == 0 ? 1 : requestedTreeLevel;
            lineItem.isItalic = false;
            lineItem.budgetChange = lineItem.AnnualBudget - lineItem.originalBudgetPeriod;
        }

        if (missIdataForCurrentPeriod.Any())
        {
            merged.AddRange(missIdataForCurrentPeriod);// ad the missing data to the list
        }
        return merged;
    }




    public IEnumerable<AccountStatementTreeData_excel> CreateParentChildDataList(IEnumerable<AccStmtExcelExportBaseData> serialzedData, AccountStmtExcelExportInput input, int accountDisplayLevel)
    {


        List<AccountStatementTreeData_excel> orgData = new();
        var orgDataIncludedViews = new List<string> { "0", "2" };

        if (orgDataIncludedViews.Contains(input.viewType))
        {
            orgData = (from a in serialzedData
                group a by new { a.orgId, a.Org_name } into g1
                select new AccountStatementTreeData_excel
                {
                    Id = $"{g1.Key.orgId}-1", // Level 1 (OrgId)
                    Parent = string.Empty,     // Parent ID Empty
                    Level = 1,
                    Name = (g1.Key.orgId.Contains("00-orgMiss-") || g1.Key.orgId.Contains("00-deptMiss-")) ? $"{g1.Key.Org_name}" : $"{g1.Key.orgId} {g1.Key.Org_name}",
                    BudPeriod = g1.Sum(x => x.BudPeriod),
                    AccPeriod = g1.Sum(x => x.AccPeriod),
                    DevPeriod = g1.Sum(x => x.BudPeriod) - g1.Sum(x => x.AccPeriod),
                    DevPeriodPct = g1.Sum(x => x.BudPeriod) != 0 ? ((g1.Sum(x => x.BudPeriod) - g1.Sum(x => x.AccPeriod)) / (Math.Abs(g1.Sum(x => x.BudPeriod)))) : 0,
                    BudYtd = g1.Sum(x => x.BudYtd),
                    AccYtd = g1.Sum(x => x.AccYtd),
                    DevBudgetYtd = g1.Sum(x => x.BudYtd) - g1.Sum(x => x.AccYtd),
                    DevBudgetYtdPct = g1.Sum(x => x.BudYtd) != 0 ? ((g1.Sum(x => x.BudYtd) - g1.Sum(x => x.AccYtd)) / (Math.Abs(g1.Sum(x => x.BudYtd)))) : 0,
                    originalBudgetPeriod = g1.Sum(x => x.originalBudgetPeriod),
                    budgetChange = g1.Sum(x => x.AnnualBudget) - g1.Sum(x => x.originalBudgetPeriod),
                    AnnualBudget = g1.Sum(x => x.AnnualBudget),
                    UsagePct = g1.Sum(x => x.AnnualBudget) != 0 ? ((g1.Sum(x => x.AccYtd) / (Math.Abs(g1.Sum(x => x.AnnualBudget))))) : 0,
                    AccLastYr = g1.Sum(x => x.AccLastYr),
                    AccLastYrYtd = g1.Sum(x => x.AccLastYrYtd),

                }).ToList();
        }
        var level1Data = (from a in serialzedData
            group a by new { a.orgId, a.Level_1_id, a.Level_1_description } into g1
            select new AccountStatementTreeData_excel
            {
                Id = FormIdParentNameBasedOnViewType(input.viewType, 1, g1, DashBoardDynamicFormType.Id),   // Level 2
                Parent = FormIdParentNameBasedOnViewType(input.viewType, 1, g1, DashBoardDynamicFormType.Parent), // Parent ID Level 1
                Level = 2,
                Name = FormIdParentNameBasedOnViewType(input.viewType, 1, g1, DashBoardDynamicFormType.Name),
                BudPeriod = g1.Sum(x => x.BudPeriod),
                AccPeriod = g1.Sum(x => x.AccPeriod),
                DevPeriod = g1.Sum(x => x.BudPeriod) - g1.Sum(x => x.AccPeriod),
                DevPeriodPct = g1.Sum(x => x.BudPeriod) != 0 ? ((g1.Sum(x => x.BudPeriod) - g1.Sum(x => x.AccPeriod)) / (Math.Abs(g1.Sum(x => x.BudPeriod)))) : 0,
                BudYtd = g1.Sum(x => x.BudYtd),
                AccYtd = g1.Sum(x => x.AccYtd),
                DevBudgetYtd = g1.Sum(x => x.BudYtd) - g1.Sum(x => x.AccYtd),
                DevBudgetYtdPct = g1.Sum(x => x.BudYtd) != 0 ? ((g1.Sum(x => x.BudYtd) - g1.Sum(x => x.AccYtd)) / (Math.Abs(g1.Sum(x => x.BudYtd)))) : 0,
                originalBudgetPeriod = g1.Sum(x => x.originalBudgetPeriod),
                budgetChange = g1.Sum(x => x.AnnualBudget) - g1.Sum(x => x.originalBudgetPeriod),
                AnnualBudget = g1.Sum(x => x.AnnualBudget),
                UsagePct = g1.Sum(x => x.AnnualBudget) != 0 ? ((g1.Sum(x => x.AccYtd) / (Math.Abs(g1.Sum(x => x.AnnualBudget))))) : 0,
                AccLastYr = g1.Sum(x => x.AccLastYr),
                AccLastYrYtd = g1.Sum(x => x.AccLastYrYtd),
            }).ToList();

        var level2Data = (from a in serialzedData
            group a by new { a.orgId, a.Level_1_id, a.Level_2_id, a.Level_2_description } into g1
            select new AccountStatementTreeData_excel
            {
                Id = FormIdParentNameBasedOnViewType(input.viewType, 2, g1, DashBoardDynamicFormType.Id),   // Level 3
                Parent = FormIdParentNameBasedOnViewType(input.viewType, 2, g1, DashBoardDynamicFormType.Parent),     // Parent ID Level 2
                Level = 3,
                Name = FormIdParentNameBasedOnViewType(input.viewType, 2, g1, DashBoardDynamicFormType.Name),
                BudPeriod = g1.Sum(x => x.BudPeriod),
                AccPeriod = g1.Sum(x => x.AccPeriod),
                DevPeriod = g1.Sum(x => x.BudPeriod) - g1.Sum(x => x.AccPeriod),
                DevPeriodPct = g1.Sum(x => x.BudPeriod) != 0 ? ((g1.Sum(x => x.BudPeriod) - g1.Sum(x => x.AccPeriod)) / (Math.Abs(g1.Sum(x => x.BudPeriod)))) : 0,
                BudYtd = g1.Sum(x => x.BudYtd),
                AccYtd = g1.Sum(x => x.AccYtd),
                DevBudgetYtd = g1.Sum(x => x.BudYtd) - g1.Sum(x => x.AccYtd),
                DevBudgetYtdPct = g1.Sum(x => x.BudYtd) != 0 ? ((g1.Sum(x => x.BudYtd) - g1.Sum(x => x.AccYtd)) / (Math.Abs(g1.Sum(x => x.BudYtd)))) : 0,
                originalBudgetPeriod = g1.Sum(x => x.originalBudgetPeriod),
                budgetChange = g1.Sum(x => x.AnnualBudget) - g1.Sum(x => x.originalBudgetPeriod),
                AnnualBudget = g1.Sum(x => x.AnnualBudget),
                UsagePct = g1.Sum(x => x.AnnualBudget) != 0 ? ((g1.Sum(x => x.AccYtd) / (Math.Abs(g1.Sum(x => x.AnnualBudget))))) : 0,
                AccLastYr = g1.Sum(x => x.AccLastYr),
                AccLastYrYtd = g1.Sum(x => x.AccLastYrYtd),
            }).ToList();

        var level3Data = (from a in serialzedData
            group a by new { a.orgId, a.Level_1_id, a.Level_2_id, a.Level_3_id, a.Level_3_description } into g1
            select new AccountStatementTreeData_excel
            {
                Id = FormIdParentNameBasedOnViewType(input.viewType, 3, g1, DashBoardDynamicFormType.Id),   // Level 4
                Parent = FormIdParentNameBasedOnViewType(input.viewType, 3, g1, DashBoardDynamicFormType.Parent),     // Parent ID Level 3
                Level = 4,
                Name = FormIdParentNameBasedOnViewType(input.viewType, 3, g1, DashBoardDynamicFormType.Name),
                BudPeriod = g1.Sum(x => x.BudPeriod),
                AccPeriod = g1.Sum(x => x.AccPeriod),
                DevPeriod = g1.Sum(x => x.BudPeriod) - g1.Sum(x => x.AccPeriod),
                DevPeriodPct = g1.Sum(x => x.BudPeriod) != 0 ? ((g1.Sum(x => x.BudPeriod) - g1.Sum(x => x.AccPeriod)) / (Math.Abs(g1.Sum(x => x.BudPeriod)))) : 0,
                BudYtd = g1.Sum(x => x.BudYtd),
                AccYtd = g1.Sum(x => x.AccYtd),
                DevBudgetYtd = g1.Sum(x => x.BudYtd) - g1.Sum(x => x.AccYtd),
                DevBudgetYtdPct = g1.Sum(x => x.BudYtd) != 0 ? ((g1.Sum(x => x.BudYtd) - g1.Sum(x => x.AccYtd)) / (Math.Abs(g1.Sum(x => x.BudYtd)))) : 0,
                originalBudgetPeriod = g1.Sum(x => x.originalBudgetPeriod),
                budgetChange = g1.Sum(x => x.AnnualBudget) - g1.Sum(x => x.originalBudgetPeriod),
                AnnualBudget = g1.Sum(x => x.AnnualBudget),
                UsagePct = g1.Sum(x => x.AnnualBudget) != 0 ? ((g1.Sum(x => x.AccYtd) / (Math.Abs(g1.Sum(x => x.AnnualBudget))))) : 0,
                AccLastYr = g1.Sum(x => x.AccLastYr),
                AccLastYrYtd = g1.Sum(x => x.AccLastYrYtd),
            }).ToList();

        var level4Data = (from a in serialzedData
            group a by new { a.orgId, a.Level_1_id, a.Level_2_id, a.Level_3_id, a.Level_4_id, a.Level_4_description } into g1
            select new AccountStatementTreeData_excel
            {
                Id = FormIdParentNameBasedOnViewType(input.viewType, 4, g1, DashBoardDynamicFormType.Id),   // Level 5
                Parent = FormIdParentNameBasedOnViewType(input.viewType, 4, g1, DashBoardDynamicFormType.Parent),   // Parent ID Level 4
                Level = 5,
                Name = FormIdParentNameBasedOnViewType(input.viewType, 4, g1, DashBoardDynamicFormType.Name),
                BudPeriod = g1.Sum(x => x.BudPeriod),
                AccPeriod = g1.Sum(x => x.AccPeriod),
                DevPeriod = g1.Sum(x => x.BudPeriod) - g1.Sum(x => x.AccPeriod),
                DevPeriodPct = g1.Sum(x => x.BudPeriod) != 0 ? ((g1.Sum(x => x.BudPeriod) - g1.Sum(x => x.AccPeriod)) / (Math.Abs(g1.Sum(x => x.BudPeriod)))) : 0,
                BudYtd = g1.Sum(x => x.BudYtd),
                AccYtd = g1.Sum(x => x.AccYtd),
                DevBudgetYtd = g1.Sum(x => x.BudYtd) - g1.Sum(x => x.AccYtd),
                DevBudgetYtdPct = g1.Sum(x => x.BudYtd) != 0 ? ((g1.Sum(x => x.BudYtd) - g1.Sum(x => x.AccYtd)) / (Math.Abs(g1.Sum(x => x.BudYtd)))) : 0,
                originalBudgetPeriod = g1.Sum(x => x.originalBudgetPeriod),
                budgetChange = g1.Sum(x => x.AnnualBudget) - g1.Sum(x => x.originalBudgetPeriod),
                AnnualBudget = g1.Sum(x => x.AnnualBudget),
                UsagePct = g1.Sum(x => x.AnnualBudget) != 0 ? ((g1.Sum(x => x.AccYtd) / (Math.Abs(g1.Sum(x => x.AnnualBudget))))) : 0,
                AccLastYr = g1.Sum(x => x.AccLastYr),
                AccLastYrYtd = g1.Sum(x => x.AccLastYrYtd),
            }).ToList();

        var level5Data = (from a in serialzedData
            group a by new { a.orgId, a.Level_1_id, a.Level_2_id, a.Level_3_id, a.Level_4_id, a.Level_5_id, a.Level_5_description } into g1
            select new AccountStatementTreeData_excel
            {
                Id = FormIdParentNameBasedOnViewType(input.viewType, 5, g1, DashBoardDynamicFormType.Id),   // Level 5
                Parent = FormIdParentNameBasedOnViewType(input.viewType, 5, g1, DashBoardDynamicFormType.Parent),     // Parent ID Level 4
                Level = 5,
                Name = FormIdParentNameBasedOnViewType(input.viewType, 5, g1, DashBoardDynamicFormType.Name),
                BudPeriod = g1.Sum(x => x.BudPeriod),
                AccPeriod = g1.Sum(x => x.AccPeriod),
                DevPeriod = g1.Sum(x => x.BudPeriod) - g1.Sum(x => x.AccPeriod),
                DevPeriodPct = g1.Sum(x => x.BudPeriod) != 0 ? ((g1.Sum(x => x.BudPeriod) - g1.Sum(x => x.AccPeriod)) / (Math.Abs(g1.Sum(x => x.BudPeriod)))) : 0,
                BudYtd = g1.Sum(x => x.BudYtd),
                AccYtd = g1.Sum(x => x.AccYtd),
                DevBudgetYtd = g1.Sum(x => x.BudYtd) - g1.Sum(x => x.AccYtd),
                DevBudgetYtdPct = g1.Sum(x => x.BudYtd) != 0 ? ((g1.Sum(x => x.BudYtd) - g1.Sum(x => x.AccYtd)) / (Math.Abs(g1.Sum(x => x.BudYtd)))) : 0,
                originalBudgetPeriod = g1.Sum(x => x.originalBudgetPeriod),
                budgetChange = g1.Sum(x => x.AnnualBudget) - g1.Sum(x => x.originalBudgetPeriod),
                AnnualBudget = g1.Sum(x => x.AnnualBudget),
                UsagePct = g1.Sum(x => x.AnnualBudget) != 0 ? ((g1.Sum(x => x.AccYtd) / (Math.Abs(g1.Sum(x => x.AnnualBudget))))) : 0,
                AccLastYr = g1.Sum(x => x.AccLastYr),
                AccLastYrYtd = g1.Sum(x => x.AccLastYrYtd),
            }).ToList();


        switch (accountDisplayLevel)
        {

            case 1: orgData.AddRange(level1Data); break;
            case 2:
                orgData.AddRange(level1Data);
                orgData.AddRange(level2Data);
                ; break;

            case 3:
                orgData.AddRange(level1Data);
                orgData.AddRange(level2Data);
                orgData.AddRange(level3Data);
                ; break;
            case 4:
                orgData.AddRange(level1Data);
                orgData.AddRange(level2Data);
                orgData.AddRange(level3Data);
                orgData.AddRange(level4Data);
                ; break;
            case 5:
                orgData.AddRange(level1Data);
                orgData.AddRange(level2Data);
                orgData.AddRange(level3Data);
                orgData.AddRange(level4Data);
                orgData.AddRange(level5Data);
                ; break;
        }

        return orgData;
    }



    private string FormIdParentNameBasedOnViewType(string viewType, int hierarchyLevel, IGrouping<dynamic, AccStmtExcelExportBaseData> g1, DashBoardDynamicFormType returnType)
    {
        string emptyString = " ";
        string finalResult = string.Empty;
        string resultId = string.Empty;
        string resultParent = string.Empty;
        string resultName = string.Empty;
        switch (viewType)
        {
            case "0":
                switch (hierarchyLevel)
                {
                    case 1:
                        resultId = $"{g1.Key.orgId}-{g1.Key.Level_1_id}-2";
                        resultParent = $"{g1.Key.orgId}-1";
                        resultName = $"-{emptyString}{emptyString}{g1.Key.Level_1_id} {g1.Key.Level_1_description}";
                        break;
                    case 2:
                        resultId = $"{g1.Key.orgId}-{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-3";
                        resultParent = $"{g1.Key.orgId}-{g1.Key.Level_1_id}-2";
                        resultName = $"-{emptyString}{emptyString}{emptyString}{g1.Key.Level_2_id} {g1.Key.Level_2_description}";
                        break;
                    case 3:
                        resultId = $"{g1.Key.orgId}-{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-{g1.Key.Level_3_id}-4";
                        resultParent = $"{g1.Key.orgId}-{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-3";
                        resultName = $"-{emptyString}{emptyString}{emptyString}{emptyString}{g1.Key.Level_3_id} {g1.Key.Level_3_description}";
                        break;
                    case 4:
                        resultId = $"{g1.Key.orgId}-{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-{g1.Key.Level_3_id}-{g1.Key.Level_4_id}-5";
                        resultParent = $"{g1.Key.orgId}-{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-{g1.Key.Level_3_id}-4";
                        resultName = $"-{emptyString}{emptyString}{emptyString}{emptyString}{emptyString}{g1.Key.Level_4_id} {g1.Key.Level_4_description}";
                        break;
                    case 5:
                        resultId = $"{g1.Key.orgId}-{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-{g1.Key.Level_3_id}-{g1.Key.Level_4_id}-{g1.Key.Level_5_id}-6";
                        resultParent = $"{g1.Key.orgId}-{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-{g1.Key.Level_3_id}-{g1.Key.Level_4_id}-5";
                        resultName = $"-{emptyString}{emptyString}{emptyString}{emptyString}{emptyString}{emptyString}{g1.Key.Level_5_id} {g1.Key.Level_5_description}";
                        break;
                }
                break;
            case "1":
                switch (hierarchyLevel)
                {
                    case 1:
                        resultId = $"{g1.Key.Level_1_id}-1";
                        resultParent = string.Empty;
                        resultName = $"{g1.Key.Level_1_id} {g1.Key.Level_1_description}";
                        break;
                    case 2:
                        resultId = $"{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-2";
                        resultParent = $"{g1.Key.Level_1_id}-1";
                        resultName = $"-{emptyString}{emptyString}{g1.Key.Level_2_id} {g1.Key.Level_2_description}";
                        break;
                    case 3:
                        resultId = $"{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-{g1.Key.Level_3_id}-3";
                        resultParent = $"{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-2";
                        resultName = $"-{emptyString}{emptyString}{emptyString}{g1.Key.Level_3_id} {g1.Key.Level_3_description}";
                        break;
                    case 4:
                        resultId = $"{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-{g1.Key.Level_3_id}-{g1.Key.Level_4_id}-4";
                        resultParent = $"{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-{g1.Key.Level_3_id}-3";
                        resultName = $"-{emptyString}{emptyString}{emptyString}{emptyString}{g1.Key.Level_4_id} {g1.Key.Level_4_description}";
                        break;
                    case 5:
                        resultId = $"{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-{g1.Key.Level_3_id}-{g1.Key.Level_4_id}-{g1.Key.Level_5_id}-5";
                        resultParent = $"{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-{g1.Key.Level_3_id}-{g1.Key.Level_4_id}-4";
                        resultName = $"-{emptyString}{emptyString}{emptyString}{emptyString}{emptyString}{g1.Key.Level_5_id} {g1.Key.Level_5_description}";
                        break;
                }
                break;
        }
        switch (returnType)
        {
            case DashBoardDynamicFormType.Id:
                finalResult = resultId;
                break;
            case DashBoardDynamicFormType.Parent:
                finalResult = resultParent;
                break;
            case DashBoardDynamicFormType.Name:
                finalResult = resultName;
                break;
            default:
                break;
        }
        return finalResult;
    }




    private static List<AccStmtExcelExportBaseData> NonZeroDataRowsExcel(List<AccStmtExcelExportBaseData> acRowData)
    {
        List<AccStmtExcelExportBaseData> nzRowData = new List<AccStmtExcelExportBaseData>();

        decimal sumZero = 0;
        List<bool> rowWiseZeroData = new List<bool>();
        bool currentRow = false;
        foreach (var ard in acRowData)
        {
            sumZero = 0;
            rowWiseZeroData = new List<bool>();
            //budgetPeriod
            sumZero += ard.BudPeriod;
            currentRow = ard.BudPeriod != 0;
            rowWiseZeroData.Add(currentRow);


            //AccountingPeriod
            sumZero += ard.AccPeriod;
            currentRow = ard.AccPeriod != 0;
            rowWiseZeroData.Add(currentRow);

            //budgetYtd
            sumZero += ard.BudYtd;
            currentRow = ard.BudYtd != 0;
            rowWiseZeroData.Add(currentRow);

            //acTTD                
            sumZero += ard.AccYtd;
            currentRow = ard.AccYtd != 0;
            rowWiseZeroData.Add(currentRow);

            //("aB"))                
            sumZero += ard.AnnualBudget;
            currentRow = ard.AnnualBudget != 0;
            rowWiseZeroData.Add(currentRow);

            //("uP"))                
            sumZero += ard.UsagePct;
            currentRow = ard.UsagePct != 0;
            rowWiseZeroData.Add(currentRow);

            //("acLYr"))                
            sumZero += ard.AccLastYr;
            currentRow = ard.AccLastYr != 0;
            rowWiseZeroData.Add(currentRow);

            //"acLYrYTD"                
            sumZero += ard.AccLastYrYtd;
            currentRow = ard.AccLastYrYtd != 0;
            rowWiseZeroData.Add(currentRow);

            //("oB"))                
            sumZero += ard.originalBudgetPeriod;
            currentRow = ard.originalBudgetPeriod != 0;
            rowWiseZeroData.Add(currentRow);


            if (rowWiseZeroData.Any(z => z)) //(Math.Floor(sumZero) != 0)
                nzRowData.Add(ard);
        }
        return nzRowData;
    }


    public async Task<IEnumerable<AccStmtDataByPeriod_Excel>> GetAccSatementDataPerPeriod_Excel(AccountStmtExcelExportInput gridInput)
    {

        var type = "LOCK_ORIGINAL_BUDGET";
        var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(gridInput.userName, gridInput.tenantId, gridInput.forecastPeriod);

        var isLocked = await _utility.CheckOnYearlyBudgetLockByBudgetYearAsync(orgVersionContent, gridInput.tenantId, gridInput.userName, gridInput.budgetYear, gridInput.orgId, gridInput.orgLevel.ToString(), type);
        var data = await _unitOfWork.DashBoardWidgetsRepo.GetAccSatementDataPerPeriod_Excel(gridInput.userName, gridInput.tenantId, gridInput, isLocked);


        return MergeDataSet_Excel(data.accData, data.budgetData, data.originalBudgetData, gridInput.viewType, gridInput.forecastPeriod, gridInput.budgetYear);

    }



    private IEnumerable<AccStmtDataByPeriod_Excel> MergeDataSet_Excel(List<AccStmtExcelExportBaseData> accounting, List<AccStmtExcelExportBaseData> budget, List<AccStmtExcelExportBaseData> originalBudgetData, string viewType, int forecastPeriod, int budgetYear)
    {
        var merged = (from acc in accounting
            join bud in budget
                on new { acc.orgId, acc.Org_id_nextLevel, acc.Id, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, acc.Period, acc.BudgetYear }
                equals new { bud.orgId, bud.Org_id_nextLevel, bud.Id, bud.Level_1_id, bud.Level_2_id, bud.Level_3_id, bud.Level_4_id, bud.Level_5_id, bud.Period, bud.BudgetYear }
            //join obud in originalBudget
            // on new { acc.Id, acc.Period, acc.BudgetYear } equals new { obud.Id, obud.Period, obud.BudgetYear }
            select new AccStmtExcelExportBaseData()
            {
                orgId = acc.orgId,
                Org_id_nextLevel = acc.Org_id_nextLevel,
                Org_name = acc.Org_name,
                Org_name_nextLevel = acc.Org_name_nextLevel,
                Level_1_id = acc.Level_1_id,
                Level_1_description = acc.Level_1_description,
                Level_2_id = acc.Level_2_id,
                Level_2_description = acc.Level_2_description,
                Level_3_id = acc.Level_3_id,
                Level_3_description = acc.Level_3_description,
                Level_4_id = acc.Level_4_id,
                Level_4_description = acc.Level_4_description,
                Level_5_id = acc.Level_5_id,
                Level_5_description = acc.Level_5_description,
                Id = acc.Id,
                Parent = acc.Parent,
                HasChildren = acc.HasChildren,
                Name = acc.Name,
                Period = acc.Period,
                AccPeriod = acc.AccPeriod,
                BudPeriod = bud.BudPeriod,
                BudgetYear = bud.BudgetYear,
                DevPeriod = 0,
                DevPeriodPct = 0,
                BudYtd = bud.BudYtd,
                AccYtd = acc.AccYtd,
                DevBudgetYtd = 0,
                DevBudgetYtdPct = 0,
                AnnualBudget = bud.AnnualBudget,
                UsagePct = 0,
                AccLastYr = 0,
                AccLastYrYtd = 0,
                //originalBudgetPeriod=obud.originalBudgetPeriod
            }).ToList();

        AccStmtExcelDataComparer comparer = new AccStmtExcelDataComparer();
        //Items only in accounting
        var onlyAcc = accounting.Except(budget, comparer).ToList();

        //Items only in budget
        var onlyBud = budget.Except(accounting, comparer).ToList();

        merged.AddRange(onlyAcc);
        merged.AddRange(onlyBud);


        merged = (from acc in merged
            join obud in originalBudgetData
                on new { acc.orgId, acc.Org_id_nextLevel, acc.Id, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, acc.Period, acc.BudgetYear }
                equals new { obud.orgId, obud.Org_id_nextLevel, obud.Id, obud.Level_1_id, obud.Level_2_id, obud.Level_3_id, obud.Level_4_id, obud.Level_5_id, obud.Period, obud.BudgetYear } into grp
            from grp1 in grp.DefaultIfEmpty()
            select new AccStmtExcelExportBaseData()
            {
                orgId = acc.orgId,
                Org_id_nextLevel = acc.Org_id_nextLevel,
                Org_name = acc.Org_name,
                Org_name_nextLevel = acc.Org_name_nextLevel,
                Level_1_id = acc.Level_1_id,
                Level_1_description = acc.Level_1_description,
                Level_2_id = acc.Level_2_id,
                Level_2_description = acc.Level_2_description,
                Level_3_id = acc.Level_3_id,
                Level_3_description = acc.Level_3_description,
                Level_4_id = acc.Level_4_id,
                Level_4_description = acc.Level_4_description,
                Level_5_id = acc.Level_5_id,
                Level_5_description = acc.Level_5_description,
                Id = acc.Id,
                Parent = acc.Parent,
                HasChildren = acc.HasChildren,
                Name = acc.Name,
                Period = acc.Period,
                AccPeriod = acc.AccPeriod,
                BudPeriod = acc.BudPeriod,
                BudgetYear = acc.BudgetYear,
                DevPeriod = 0,
                DevPeriodPct = 0,
                BudYtd = acc.BudYtd,
                AccYtd = acc.AccYtd,
                DevBudgetYtd = 0,
                DevBudgetYtdPct = 0,
                AnnualBudget = acc.AnnualBudget,
                UsagePct = 0,
                AccLastYr = 0,
                AccLastYrYtd = 0,
                originalBudgetPeriod = grp1?.originalBudgetPeriod ?? 0
            }).ToList();

        //Item only in OriginalBug
        var onlyOrignalBud = originalBudgetData.Except(merged, comparer).ToList();
        merged.AddRange(onlyOrignalBud);
        List<int> dataByPeriod = new();
        for (int i = 1; i <= 12; i++)
        {
            int currentPeriod = budgetYear * 100 + i;
            dataByPeriod.Add(currentPeriod);
        }
        int requestedTreeLevel = 0;
        int LastPeriodWithValidData = 0;
        List<AccStmtExcelExportBaseData> missIdataForCurrentPeriod = new List<AccStmtExcelExportBaseData>();
        AccStmtExcelExportBaseData temp = null;
        foreach (var lineItem in merged)
        {
            if (!merged.Any(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.Period == forecastPeriod) && !missIdataForCurrentPeriod.Any(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id)) // check if the item is not pressent for current , if not there then add
            {
                var validBudytdPeriod = merged.Where(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.Period <= forecastPeriod && z.BudgetYear == (forecastPeriod / 100) && z.BudYtd != 0).Any() ? merged.Where(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.Period <= forecastPeriod && z.BudYtd != 0).Max(x => x.Period) : forecastPeriod;

                var budYtd = (merged.FirstOrDefault(z => z.Period == validBudytdPeriod && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100)) != null && merged.FirstOrDefault(z => z.Period == validBudytdPeriod && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100)).BudYtd != 0) ? merged.FirstOrDefault(z => z.Period == validBudytdPeriod && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100)).BudYtd : 0;
                var annualBudget = (merged.Any(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100) && z.AnnualBudget != 0)) ? merged.FirstOrDefault(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100) && z.AnnualBudget != 0).AnnualBudget : 0;

                var validAccytdPeriod = merged.Where(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.Period <= forecastPeriod && z.BudgetYear == (forecastPeriod / 100) && z.AccYtd != 0).Any() ? merged.Where(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.Period <= forecastPeriod && z.AccYtd != 0).Max(x => x.Period) : forecastPeriod;// last period with valid data for accounting this is done when there is no data in table for current table then the ytd will be the value of the previous period

                var AccYtd = (merged.FirstOrDefault(z => z.Period == validAccytdPeriod && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100)) != null && merged.FirstOrDefault(z => z.Period == validAccytdPeriod && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100)).AccYtd != 0) ? merged.Where(z => z.Period <= forecastPeriod && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100)).Sum(z => z.AccPeriod) : 0;

                var validOriginalPeriod = merged.Where(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.Period <= forecastPeriod && z.BudgetYear == (forecastPeriod / 100) && z.originalBudgetPeriod != 0).Any() ?
                    merged.Where(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.Period <= forecastPeriod && z.originalBudgetPeriod != 0).Max(x => x.Period) :
                    forecastPeriod;// last period with valid data for accounting this is done when there is no data in table for current table then the ytd will be the value of the previous period

                var originalBudgetPeriod = (merged.FirstOrDefault(z => z.Period == validOriginalPeriod && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100)) != null && merged.FirstOrDefault(z => z.Period == validOriginalPeriod && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100)).originalBudgetPeriod != 0) ?
                    merged.FirstOrDefault(z => z.Period == validOriginalPeriod && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100)).originalBudgetPeriod
                    : 0;
                temp = new AccStmtExcelExportBaseData()
                {
                    orgId = lineItem.orgId,
                    Org_id_nextLevel = lineItem.Org_id_nextLevel,
                    Org_name = lineItem.Org_name,
                    Org_name_nextLevel = lineItem.Org_name_nextLevel,
                    Level_1_id = lineItem.Level_1_id,
                    Level_1_description = lineItem.Level_1_description,
                    Level_2_id = lineItem.Level_2_id,
                    Level_2_description = lineItem.Level_2_description,
                    Level_3_id = lineItem.Level_3_id,
                    Level_3_description = lineItem.Level_3_description,
                    Level_4_id = lineItem.Level_4_id,
                    Level_4_description = lineItem.Level_4_description,
                    Level_5_id = lineItem.Level_5_id,
                    Level_5_description = lineItem.Level_5_description,
                    Id = lineItem.Id,
                    Parent = lineItem.Parent,
                    HasChildren = lineItem.HasChildren,
                    Name = lineItem.Name,
                    Period = forecastPeriod,
                    AccPeriod = 0,
                    BudPeriod = 0,
                    BudgetYear = forecastPeriod / 100,
                    DevPeriod = 0,
                    DevPeriodPct = 0,
                    BudYtd = budYtd,
                    AccYtd = AccYtd,
                    DevBudgetYtd = 0,
                    DevBudgetYtdPct = 0,
                    AnnualBudget = annualBudget,
                    UsagePct = 0,
                    AccLastYr = 0,
                    AccLastYrYtd = 0,
                    originalBudgetPeriod = originalBudgetPeriod,
                    // level = requestedTreeLevel == 0 ? 1 : requestedTreeLevel,
                    // isItalic = SetItalicProperty(viewType, requestedTreeLevel),
                };
                missIdataForCurrentPeriod.Add(temp);
            }

            if (lineItem.Period == forecastPeriod)// this is for data missing in bud or accounting causing  ytd be zero for current period
            {
                if ((lineItem.BudPeriod == 0 && lineItem.BudYtd == 0) && budget.Any(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.Period == forecastPeriod))
                {
                    LastPeriodWithValidData = forecastPeriod;
                }
                else
                {
                    LastPeriodWithValidData = lineItem.BudPeriod == 0 && merged.Any(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.Period <= forecastPeriod && z.BudgetYear == (forecastPeriod / 100) && z.BudYtd != 0 && z.BudgetYear == (forecastPeriod / 100)) ?
                        merged.Where(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.Period <= forecastPeriod && z.BudYtd != 0 && z.BudgetYear == (forecastPeriod / 100)).Max(x => x.Period) : forecastPeriod; // last period with valid data for bud this is done when there is no data in table for current table then the ytd will be the value of the previous period

                    lineItem.BudYtd = (lineItem.BudYtd == 0 && merged.FirstOrDefault(z => z.Period == LastPeriodWithValidData && z.BudgetYear == (forecastPeriod / 100) && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100)).BudYtd != 0) ? merged.FirstOrDefault(z => z.Period == LastPeriodWithValidData && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100)).BudYtd : lineItem.BudYtd;
                }




                lineItem.AnnualBudget = (lineItem.AnnualBudget == 0 && merged.Any(z => z.BudgetYear == (forecastPeriod / 100) && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.AnnualBudget != 0)) ? merged.FirstOrDefault(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100) && z.AnnualBudget != 0).AnnualBudget : lineItem.AnnualBudget;

                if ((lineItem.AccPeriod == 0 && lineItem.AccYtd == 0) && accounting.Any(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.Period == forecastPeriod))
                {
                    LastPeriodWithValidData = forecastPeriod;
                }
                else
                {
                    LastPeriodWithValidData = lineItem.AccPeriod == 0 && merged.Any(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.Period <= forecastPeriod && z.BudgetYear == (forecastPeriod / 100) && z.AccYtd != 0) ? merged.Where(z => z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.Period <= forecastPeriod && z.AccYtd != 0 && z.BudgetYear == (forecastPeriod / 100)).Max(x => x.Period) : forecastPeriod;// last period with valid data for accounting this is done when there is no data in table for current table then the ytd will be the value of the previous period
                }

                lineItem.AccYtd = (lineItem.AccYtd == 0 && merged.FirstOrDefault(z => z.Period == LastPeriodWithValidData && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100)).AccYtd != 0) ? merged.FirstOrDefault(z => z.Period == LastPeriodWithValidData && z.orgId == lineItem.orgId && z.Org_id_nextLevel == lineItem.Org_id_nextLevel && z.Level_1_id == lineItem.Level_1_id && z.Level_2_id == lineItem.Level_2_id && z.Level_3_id == lineItem.Level_3_id && z.Level_4_id == lineItem.Level_4_id && z.Level_5_id == lineItem.Level_5_id && z.BudgetYear == (forecastPeriod / 100)).AccYtd : lineItem.AccYtd;
            }

        }

        if (missIdataForCurrentPeriod.Any())
        {
            merged.AddRange(missIdataForCurrentPeriod);// ad the missing data to the list
        }



        var data = (from acc in merged
            select new AccStmtDataByPeriod_Excel
            {
                orgId = acc.orgId,
                Org_id_nextLevel = acc.Org_id_nextLevel,
                Org_name = acc.Org_name,
                Org_name_nextLevel = acc.Org_name_nextLevel,
                Level_1_id = acc.Level_1_id,
                Level_1_description = acc.Level_1_description,
                Level_2_id = acc.Level_2_id,
                Level_2_description = acc.Level_2_description,
                Level_3_id = acc.Level_3_id,
                Level_3_description = acc.Level_3_description,
                Level_4_id = acc.Level_4_id,
                Level_4_description = acc.Level_4_description,
                Level_5_id = acc.Level_5_id,
                Level_5_description = acc.Level_5_description,
                Id = acc.Id,
                Parent = acc.Parent,
                HasChildren = acc.HasChildren,
                Name = acc.Name,
                Period = acc.Period,
                Level = requestedTreeLevel == 0 ? 1 : requestedTreeLevel,
                jan_origbudget = GetValues_Excel(ref merged, dataByPeriod[0], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.origbudget),
                jan_budget = GetValues_Excel(ref merged, dataByPeriod[0], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budget),
                jan_account = GetValues_Excel(ref merged, dataByPeriod[0], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.account),
                jan_budgetDeviation = GetValues_Excel(ref merged, dataByPeriod[0], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviation),
                jan_budgetDeviationPct = GetValues_Excel(ref merged, dataByPeriod[0], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviationPct),
                jan_accountYtd = GetValues_Excel(ref merged, dataByPeriod[0], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.accountYtd),
                jan_usagePctYtd = GetValues_Excel(ref merged, dataByPeriod[0], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePctYtd),
                jan_usagePct = GetValues_Excel(ref merged, dataByPeriod[0], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePct),

                feb_origbudget = GetValues_Excel(ref merged, dataByPeriod[1], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.origbudget),
                feb_budget = GetValues_Excel(ref merged, dataByPeriod[1], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budget),
                feb_account = GetValues_Excel(ref merged, dataByPeriod[1], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.account),
                feb_budgetDeviation = GetValues_Excel(ref merged, dataByPeriod[1], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviation),
                feb_budgetDeviationPct = GetValues_Excel(ref merged, dataByPeriod[1], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviationPct),
                feb_accountYtd = GetValues_Excel(ref merged, dataByPeriod[1], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.accountYtd),
                feb_usagePctYtd = GetValues_Excel(ref merged, dataByPeriod[1], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePctYtd),
                feb_usagePct = GetValues_Excel(ref merged, dataByPeriod[1], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePct),

                mar_origbudget = GetValues_Excel(ref merged, dataByPeriod[2], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.origbudget),
                mar_budget = GetValues_Excel(ref merged, dataByPeriod[2], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budget),
                mar_account = GetValues_Excel(ref merged, dataByPeriod[2], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.account),
                mar_budgetDeviation = GetValues_Excel(ref merged, dataByPeriod[2], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviation),
                mar_budgetDeviationPct = GetValues_Excel(ref merged, dataByPeriod[2], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviationPct),
                mar_accountYtd = GetValues_Excel(ref merged, dataByPeriod[2], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.accountYtd),
                mar_usagePctYtd = GetValues_Excel(ref merged, dataByPeriod[2], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePctYtd),
                mar_usagePct = GetValues_Excel(ref merged, dataByPeriod[2], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePct),

                apr_origbudget = GetValues_Excel(ref merged, dataByPeriod[3], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.origbudget),
                apr_budget = GetValues_Excel(ref merged, dataByPeriod[3], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budget),
                apr_account = GetValues_Excel(ref merged, dataByPeriod[3], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.account),
                apr_budgetDeviation = GetValues_Excel(ref merged, dataByPeriod[3], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviation),
                apr_budgetDeviationPct = GetValues_Excel(ref merged, dataByPeriod[3], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviationPct),
                apr_accountYtd = GetValues_Excel(ref merged, dataByPeriod[3], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.accountYtd),
                apr_usagePctYtd = GetValues_Excel(ref merged, dataByPeriod[3], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePctYtd),
                apr_usagePct = GetValues_Excel(ref merged, dataByPeriod[3], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePct),

                may_origbudget = GetValues_Excel(ref merged, dataByPeriod[4], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.origbudget),
                may_budget = GetValues_Excel(ref merged, dataByPeriod[4], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budget),
                may_account = GetValues_Excel(ref merged, dataByPeriod[4], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.account),
                may_budgetDeviation = GetValues_Excel(ref merged, dataByPeriod[4], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviation),
                may_budgetDeviationPct = GetValues_Excel(ref merged, dataByPeriod[4], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviationPct),
                may_accountYtd = GetValues_Excel(ref merged, dataByPeriod[4], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.accountYtd),
                may_usagePctYtd = GetValues_Excel(ref merged, dataByPeriod[4], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePctYtd),
                may_usagePct = GetValues_Excel(ref merged, dataByPeriod[4], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePct),

                jun_origbudget = GetValues_Excel(ref merged, dataByPeriod[5], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.origbudget),
                jun_budget = GetValues_Excel(ref merged, dataByPeriod[5], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budget),
                jun_account = GetValues_Excel(ref merged, dataByPeriod[5], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.account),
                jun_budgetDeviation = GetValues_Excel(ref merged, dataByPeriod[5], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviation),
                jun_budgetDeviationPct = GetValues_Excel(ref merged, dataByPeriod[5], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviationPct),
                jun_accountYtd = GetValues_Excel(ref merged, dataByPeriod[5], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.accountYtd),
                jun_usagePctYtd = GetValues_Excel(ref merged, dataByPeriod[5], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePctYtd),
                jun_usagePct = GetValues_Excel(ref merged, dataByPeriod[5], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePct),

                jul_origbudget = GetValues_Excel(ref merged, dataByPeriod[6], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.origbudget),
                jul_budget = GetValues_Excel(ref merged, dataByPeriod[6], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budget),
                jul_account = GetValues_Excel(ref merged, dataByPeriod[6], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.account),
                jul_budgetDeviation = GetValues_Excel(ref merged, dataByPeriod[6], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviation),
                jul_budgetDeviationPct = GetValues_Excel(ref merged, dataByPeriod[6], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviationPct),
                jul_accountYtd = GetValues_Excel(ref merged, dataByPeriod[6], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.accountYtd),
                jul_usagePctYtd = GetValues_Excel(ref merged, dataByPeriod[6], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePctYtd),
                jul_usagePct = GetValues_Excel(ref merged, dataByPeriod[6], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePct),

                aug_origbudget = GetValues_Excel(ref merged, dataByPeriod[7], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.origbudget),
                aug_budget = GetValues_Excel(ref merged, dataByPeriod[7], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budget),
                aug_account = GetValues_Excel(ref merged, dataByPeriod[7], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.account),
                aug_budgetDeviation = GetValues_Excel(ref merged, dataByPeriod[7], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviation),
                aug_budgetDeviationPct = GetValues_Excel(ref merged, dataByPeriod[7], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviationPct),
                aug_accountYtd = GetValues_Excel(ref merged, dataByPeriod[7], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.accountYtd),
                aug_usagePctYtd = GetValues_Excel(ref merged, dataByPeriod[7], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePctYtd),
                aug_usagePct = GetValues_Excel(ref merged, dataByPeriod[7], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePct),

                sep_origbudget = GetValues_Excel(ref merged, dataByPeriod[8], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.origbudget),
                sep_budget = GetValues_Excel(ref merged, dataByPeriod[8], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budget),
                sep_account = GetValues_Excel(ref merged, dataByPeriod[8], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.account),
                sep_budgetDeviation = GetValues_Excel(ref merged, dataByPeriod[8], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviation),
                sep_budgetDeviationPct = GetValues_Excel(ref merged, dataByPeriod[8], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviationPct),
                sep_accountYtd = GetValues_Excel(ref merged, dataByPeriod[8], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.accountYtd),
                sep_usagePctYtd = GetValues_Excel(ref merged, dataByPeriod[8], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePctYtd),
                sep_usagePct = GetValues_Excel(ref merged, dataByPeriod[8], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePct),

                oct_origbudget = GetValues_Excel(ref merged, dataByPeriod[9], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.origbudget),
                oct_budget = GetValues_Excel(ref merged, dataByPeriod[9], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budget),
                oct_account = GetValues_Excel(ref merged, dataByPeriod[9], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.account),
                oct_budgetDeviation = GetValues_Excel(ref merged, dataByPeriod[9], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviation),
                oct_budgetDeviationPct = GetValues_Excel(ref merged, dataByPeriod[9], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviationPct),
                oct_accountYtd = GetValues_Excel(ref merged, dataByPeriod[9], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.accountYtd),
                oct_usagePctYtd = GetValues_Excel(ref merged, dataByPeriod[9], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePctYtd),
                oct_usagePct = GetValues_Excel(ref merged, dataByPeriod[9], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePct),

                nov_origbudget = GetValues_Excel(ref merged, dataByPeriod[10], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.origbudget),
                nov_budget = GetValues_Excel(ref merged, dataByPeriod[10], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budget),
                nov_account = GetValues_Excel(ref merged, dataByPeriod[10], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.account),
                nov_budgetDeviation = GetValues_Excel(ref merged, dataByPeriod[10], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviation),
                nov_budgetDeviationPct = GetValues_Excel(ref merged, dataByPeriod[10], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviationPct),
                nov_accountYtd = GetValues_Excel(ref merged, dataByPeriod[10], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.accountYtd),
                nov_usagePctYtd = GetValues_Excel(ref merged, dataByPeriod[10], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePctYtd),
                nov_usagePct = GetValues_Excel(ref merged, dataByPeriod[10], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePct),

                dec_origbudget = GetValues_Excel(ref merged, dataByPeriod[11], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.origbudget),
                dec_budget = GetValues_Excel(ref merged, dataByPeriod[11], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budget),
                dec_account = GetValues_Excel(ref merged, dataByPeriod[11], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.account),
                dec_budgetDeviation = GetValues_Excel(ref merged, dataByPeriod[11], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviation),
                dec_budgetDeviationPct = GetValues_Excel(ref merged, dataByPeriod[11], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviationPct),
                dec_accountYtd = GetValues_Excel(ref merged, dataByPeriod[11], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.accountYtd),
                dec_usagePctYtd = GetValues_Excel(ref merged, dataByPeriod[11], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePctYtd),
                dec_usagePct = GetValues_Excel(ref merged, dataByPeriod[11], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePct),

                yar_origbudget = GetSumValues_Excel(DataSetExcel(merged, acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, dataByPeriod[11]), DataSetExcel(merged, acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, dataByPeriod[11]), dataByPeriod[11], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.origbudget, true),
                yar_budget = GetSumValues_Excel(DataSetExcel(merged, acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, dataByPeriod[11]), DataSetExcel(merged, acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, dataByPeriod[11]), dataByPeriod[11], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budget, true),
                yar_account = GetSumValues_Excel(DataSetExcel(merged, acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, dataByPeriod[11]), DataSetExcel(merged, acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, dataByPeriod[11]), dataByPeriod[11], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.account, true),
                yar_budgetDeviation = GetSumValues_Excel(DataSetExcel(merged, acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, dataByPeriod[11]), DataSetExcel(merged, acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, dataByPeriod[11]), dataByPeriod[11], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviation, true),
                yar_budgetDeviationPct = GetSumValues_Excel(DataSetExcel(merged, acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, dataByPeriod[11]), DataSetExcel(merged, acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, dataByPeriod[11]), dataByPeriod[11], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.budgetDeviationPct, true),
                yar_accountYtd = GetSumValues_Excel(DataSetExcel(merged, acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, forecastPeriod), DataSetExcel(merged, acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, forecastPeriod), forecastPeriod, acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.accountYtd, true),
                yar_usagePctYtd = GetSumValues_Excel(DataSetExcel(merged, acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, dataByPeriod[11]), DataSetExcel(merged, acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, dataByPeriod[11]), dataByPeriod[11], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePctYtd, true),
                yar_usagePct = GetSumValues_Excel(DataSetExcel(merged, acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, dataByPeriod[11]), DataSetExcel(merged, acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, dataByPeriod[11]), dataByPeriod[11], acc.orgId, acc.Level_1_id, acc.Level_2_id, acc.Level_3_id, acc.Level_4_id, acc.Level_5_id, AmountType.usagePct, true)
            }).ToList();
        var currentPeriodData = data.Where(x => x.Period == forecastPeriod).OrderBy(z => z.Level_1_id).ThenBy(z => z.Level_2_id).ThenBy(z => z.Level_3_id).ThenBy(z => z.Level_4_id).ThenBy(z => z.Level_5_id).ToList();

        return currentPeriodData;
    }



    private decimal GetYearSumValues_Excel(List<AccStmtExcelExportBaseData> merged, int period, string orgId, string level1Id, string level2Id, string level3Id, string level4Id, string level5Id, AmountType type)
    {
        return GetSumValues_Excel(merged.Where(z => z.orgId == orgId && z.Level_1_id == level1Id && z.Level_2_id == level2Id && z.Level_3_id == level3Id && z.Level_4_id == level4Id && z.Level_5_id == level5Id && z.Period <= period).ToList(), merged, period, orgId, level1Id, level2Id, level3Id, level4Id, level5Id, type, true);
    }



    private decimal GetSumPerPeriodValues_excel(List<AccStmtExcelExportBaseData> merged, int period, string orgId, string level1Id, string level2Id, string level3Id, string level4Id, string level5Id, AmountType type)
    {
        return GetSumValues_Excel(merged.Where(z => z.orgId == orgId && z.Level_1_id == level1Id && z.Level_2_id == level2Id && z.Level_3_id == level3Id && z.Level_4_id == level4Id && z.Level_5_id == level5Id && z.Period == period).ToList(), merged, period, orgId, level1Id, level2Id, level3Id, level4Id, level5Id, type, false);
    }



    private decimal GetSumValues_Excel(List<AccStmtExcelExportBaseData> item, List<AccStmtExcelExportBaseData> merged, int period, string orgId, string level1Id, string level2Id, string level3Id, string level4Id, string level5Id, AmountType type, bool fromTotal)
    {
        decimal accYtd = 0;
        decimal budYtd = 0;
        switch (type)
        {
            case AmountType.origbudget:
                return item.Any() ? item.Sum(x => x.originalBudgetPeriod) : 0;

            case AmountType.budget:
                return item.Any() ? item.Sum(x => x.BudPeriod) : 0;

            case AmountType.account:
                return item.Any() ? item.Sum(x => x.AccPeriod) : 0;

            case AmountType.budgetDeviation:
                return item.Any() ? item.Sum(x => x.BudPeriod) - item.Sum(x => x.AccPeriod) : 0;

            case AmountType.budgetDeviationPct:
                if (fromTotal)
                {
                    accYtd = merged.Where(z => z.orgId == orgId && z.Level_1_id == level1Id && z.Level_2_id == level2Id && z.Level_3_id == level3Id && z.Level_4_id == level4Id && z.Level_5_id == level5Id && z.Period <= period).Sum(y => y.AccPeriod);
                    budYtd = merged.Where(z => z.orgId == orgId && z.Level_1_id == level1Id && z.Level_2_id == level2Id && z.Level_3_id == level3Id && z.Level_4_id == level4Id && z.Level_5_id == level5Id && z.Period <= period).Sum(y => y.BudPeriod);
                    return budYtd != 0 ? ((budYtd - accYtd) / Math.Abs(budYtd)) : 0;
                }
                else
                {
                    accYtd = merged.Where(z => z.orgId == orgId && z.Level_1_id == level1Id && z.Level_2_id == level2Id && z.Level_3_id == level3Id && z.Level_4_id == level4Id && z.Level_5_id == level5Id && z.Period == period).Sum(y => y.AccPeriod);
                    budYtd = merged.Where(z => z.orgId == orgId && z.Level_1_id == level1Id && z.Level_2_id == level2Id && z.Level_3_id == level3Id && z.Level_4_id == level4Id && z.Level_5_id == level5Id && z.Period == period).Sum(y => y.BudPeriod);
                    return budYtd != 0 ? ((budYtd - accYtd) / Math.Abs(budYtd)) : 0;
                }

            case AmountType.accountYtd:
                return /*fromTotal ? merged.Where(x => x.Period <= period).Sum(y => y.AccYtd) : */merged.Where(z => z.orgId == orgId && z.Level_1_id == level1Id && z.Level_2_id == level2Id && z.Level_3_id == level3Id && z.Level_4_id == level4Id && z.Level_5_id == level5Id && z.Period == period).Sum(y => y.AccYtd);

            case AmountType.usagePctYtd:
                if (fromTotal)
                {
                    accYtd = merged.Where(z => z.orgId == orgId && z.Level_1_id == level1Id && z.Level_2_id == level2Id && z.Level_3_id == level3Id && z.Level_4_id == level4Id && z.Level_5_id == level5Id && z.Period <= period).Sum(y => y.AccPeriod);
                    budYtd = merged.Where(z => z.orgId == orgId && z.Level_1_id == level1Id && z.Level_2_id == level2Id && z.Level_3_id == level3Id && z.Level_4_id == level4Id && z.Level_5_id == level5Id && z.Period <= period).Sum(y => y.BudPeriod);
                }
                else
                {
                    accYtd = merged.Where(z => z.orgId == orgId && z.Level_1_id == level1Id && z.Level_2_id == level2Id && z.Level_3_id == level3Id && z.Level_4_id == level4Id && z.Level_5_id == level5Id && z.Period <= period).Sum(y => y.AccPeriod);
                    budYtd = merged.Where(z => z.orgId == orgId && z.Level_1_id == level1Id && z.Level_2_id == level2Id && z.Level_3_id == level3Id && z.Level_4_id == level4Id && z.Level_5_id == level5Id && z.Period == period).Sum(y => y.BudPeriod);
                }
                return budYtd != 0 ? (accYtd / Math.Abs(budYtd)) : 0;

            case AmountType.usagePct:
                if (fromTotal)
                {
                    accYtd = merged.Sum(y => y.AccPeriod);
                }
                else
                {
                    accYtd = merged.Where(z => z.orgId == orgId && z.Level_1_id == level1Id && z.Level_2_id == level2Id && z.Level_3_id == level3Id && z.Level_4_id == level4Id && z.Level_5_id == level5Id && z.Period <= period).Sum(y => y.AccPeriod);
                }
                budYtd = merged.Sum(x => x.BudPeriod);
                return budYtd != 0 ? (accYtd / Math.Abs(budYtd)) : 0;


            default:
                break;
        }
        return 0;
    }



    private decimal GetValues_Excel(ref List<AccStmtExcelExportBaseData> merged, int period, string orgId, string level1Id, string level2Id, string level3Id, string level4Id, string level5Id, AmountType type)
    {
        var item = merged.FirstOrDefault(z => z.orgId == orgId && z.Level_1_id == level1Id && z.Level_2_id == level2Id && z.Level_3_id == level3Id && z.Level_4_id == level4Id && z.Level_5_id == level5Id && z.Period == period);
        decimal accYtd = 0;
        decimal budYtd = 0;


        switch (type)
        {
            case AmountType.origbudget:
                return item != null ? item.originalBudgetPeriod : 0;

            case AmountType.budget:
                return item != null ? item.BudPeriod : 0;

            case AmountType.account:
                return item != null ? item.AccPeriod : 0;

            case AmountType.budgetDeviation:
                return item != null ? item.BudPeriod - item.AccPeriod : 0;

            case AmountType.budgetDeviationPct:
                var deviation = item != null ? item.BudPeriod - item.AccPeriod : 0;
                return item != null && item.BudPeriod != 0 ? ((deviation) / Math.Abs(item.BudPeriod)) * 100 : 0;

            case AmountType.accountYtd:
                return merged.Where(z => z.orgId == orgId && z.Level_1_id == level1Id && z.Level_2_id == level2Id && z.Level_3_id == level3Id && z.Level_4_id == level4Id && z.Level_5_id == level5Id && z.Period == period).Sum(y => y.AccYtd);

            case AmountType.usagePctYtd:
                accYtd = merged.Where(z => z.orgId == orgId && z.Level_1_id == level1Id && z.Level_2_id == level2Id && z.Level_3_id == level3Id && z.Level_4_id == level4Id && z.Level_5_id == level5Id && z.Period <= period).Sum(y => y.AccPeriod);
                budYtd = merged.Where(z => z.orgId == orgId && z.Level_1_id == level1Id && z.Level_2_id == level2Id && z.Level_3_id == level3Id && z.Level_4_id == level4Id && z.Level_5_id == level5Id && z.Period == period).Sum(y => y.BudPeriod);
                return budYtd != 0 ? (accYtd / Math.Abs(budYtd)) * 100 : 0;

            case AmountType.usagePct:
                accYtd = merged.Where(z => z.orgId == orgId && z.Level_1_id == level1Id && z.Level_2_id == level2Id && z.Level_3_id == level3Id && z.Level_4_id == level4Id && z.Level_5_id == level5Id && z.Period == period).Sum(y => y.AccYtd);
                budYtd = merged.Where(z => z.orgId == orgId && z.Level_1_id == level1Id && z.Level_2_id == level2Id && z.Level_3_id == level3Id && z.Level_4_id == level4Id && z.Level_5_id == level5Id && z.Period == period).Sum(y => y.AnnualBudget);
                return budYtd != 0 ? (accYtd / Math.Abs(budYtd)) * 100 : 0;

            default:
                break;
        }
        return 0;
    }



    private List<AccStmtExcelExportBaseData> DataSetExcel(List<AccStmtExcelExportBaseData> merged, string orgId, string level1Id, string level2Id, string level3Id, string level4Id, string level5Id, int period)
    {
        return merged.Where(z => z.orgId == orgId && z.Level_1_id == level1Id && z.Level_2_id == level2Id && z.Level_3_id == level3Id && z.Level_4_id == level4Id && z.Level_5_id == level5Id && z.Period <= period).ToList();
    }





    public IEnumerable<AccStmtDataByPeriod_Excel> CreateParentChildDataListPerPeriod(IEnumerable<AccStmtDataByPeriod_Excel> serialzedData, AccountStmtExcelExportInput input, int accountDisplayLevel)
    {

        string emptyString = " ";

        List<AccStmtDataByPeriod_Excel> orgData = new List<AccStmtDataByPeriod_Excel>();

        var level1Data = (from a in serialzedData
            group a by new { a.Level_1_id, a.Level_1_description } into g1
            select new AccStmtDataByPeriod_Excel
            {
                Id = $"{g1.Key.Level_1_id}-1",   // Level 2
                Parent = string.Empty, // Parent ID Level 1
                Level = 1,
                Name = $"{g1.Key.Level_1_id} {g1.Key.Level_1_description}",
                jan_origbudget = g1.Sum(x => x.jan_origbudget),
                jan_budget = g1.Sum(x => x.jan_budget),
                jan_account = g1.Sum(x => x.jan_account),
                jan_budgetDeviation = g1.Sum(x => x.jan_budgetDeviation),
                jan_budgetDeviationPct = g1.Sum(x => x.jan_budget) != 0 ? (g1.Sum(x => x.jan_budgetDeviation) / Math.Abs(g1.Sum(x => x.jan_budget))) * 100 : 0,
                jan_accountYtd = g1.Sum(x => x.jan_accountYtd),
                jan_usagePctYtd = g1.Sum(x => x.jan_budget) != 0 ? (g1.Sum(x => x.jan_accountYtd) / Math.Abs(g1.Sum(x => x.jan_budget))) * 100 : 0,
                jan_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.jan_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                feb_origbudget = g1.Sum(x => x.feb_origbudget),
                feb_budget = g1.Sum(x => x.feb_budget),
                feb_account = g1.Sum(x => x.feb_account),
                feb_budgetDeviation = g1.Sum(x => x.feb_budgetDeviation),
                feb_budgetDeviationPct = g1.Sum(x => x.feb_budget) != 0 ? (g1.Sum(x => x.feb_budgetDeviation) / Math.Abs(g1.Sum(x => x.feb_budget))) * 100 : 0,
                feb_accountYtd = g1.Sum(x => x.feb_accountYtd),
                feb_usagePctYtd = g1.Sum(x => x.feb_budget) != 0 ? (g1.Sum(x => x.feb_accountYtd) / Math.Abs(g1.Sum(x => x.feb_budget))) * 100 : 0,
                feb_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.feb_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                mar_origbudget = g1.Sum(x => x.mar_origbudget),
                mar_budget = g1.Sum(x => x.mar_budget),
                mar_account = g1.Sum(x => x.mar_account),
                mar_budgetDeviation = g1.Sum(x => x.mar_budgetDeviation),
                mar_budgetDeviationPct = g1.Sum(x => x.mar_budget) != 0 ? (g1.Sum(x => x.mar_budgetDeviation) / Math.Abs(g1.Sum(x => x.mar_budget))) * 100 : 0,
                mar_accountYtd = g1.Sum(x => x.mar_accountYtd),
                mar_usagePctYtd = g1.Sum(x => x.mar_budget) != 0 ? (g1.Sum(x => x.mar_account) / Math.Abs(g1.Sum(x => x.mar_budget))) * 100 : 0,
                mar_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.mar_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                apr_origbudget = g1.Sum(x => x.apr_origbudget),
                apr_budget = g1.Sum(x => x.apr_budget),
                apr_account = g1.Sum(x => x.apr_account),
                apr_budgetDeviation = g1.Sum(x => x.apr_budgetDeviation),
                apr_budgetDeviationPct = g1.Sum(x => x.apr_budget) != 0 ? (g1.Sum(x => x.apr_budgetDeviation) / Math.Abs(g1.Sum(x => x.apr_budget))) * 100 : 0,
                apr_accountYtd = g1.Sum(x => x.apr_accountYtd),
                apr_usagePctYtd = g1.Sum(x => x.apr_budget) != 0 ? (g1.Sum(x => x.apr_accountYtd) / Math.Abs(g1.Sum(x => x.apr_budget))) * 100 : 0,
                apr_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.apr_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                may_origbudget = g1.Sum(x => x.may_origbudget),
                may_budget = g1.Sum(x => x.may_budget),
                may_account = g1.Sum(x => x.may_account),
                may_budgetDeviation = g1.Sum(x => x.may_budgetDeviation),
                may_budgetDeviationPct = g1.Sum(x => x.may_budget) != 0 ? (g1.Sum(x => x.may_budgetDeviation) / Math.Abs(g1.Sum(x => x.may_budget))) * 100 : 0,
                may_accountYtd = g1.Sum(x => x.may_accountYtd),
                may_usagePctYtd = g1.Sum(x => x.may_budget) != 0 ? (g1.Sum(x => x.may_accountYtd) / Math.Abs(g1.Sum(x => x.may_budget))) * 100 : 0,
                may_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.may_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                jun_origbudget = g1.Sum(x => x.jun_origbudget),
                jun_budget = g1.Sum(x => x.jun_budget),
                jun_account = g1.Sum(x => x.jun_account),
                jun_budgetDeviation = g1.Sum(x => x.jun_budgetDeviation),
                jun_budgetDeviationPct = g1.Sum(x => x.jun_budget) != 0 ? (g1.Sum(x => x.jun_budgetDeviation) / Math.Abs(g1.Sum(x => x.jun_budget))) * 100 : 0,
                jun_accountYtd = g1.Sum(x => x.jun_accountYtd),
                jun_usagePctYtd = g1.Sum(x => x.jun_budget) != 0 ? (g1.Sum(x => x.jun_accountYtd) / Math.Abs(g1.Sum(x => x.jun_budget))) * 100 : 0,
                jun_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.jun_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                jul_origbudget = g1.Sum(x => x.jul_origbudget),
                jul_budget = g1.Sum(x => x.jul_budget),
                jul_account = g1.Sum(x => x.jul_account),
                jul_budgetDeviation = g1.Sum(x => x.jul_budgetDeviation),
                jul_budgetDeviationPct = g1.Sum(x => x.jul_budget) != 0 ? (g1.Sum(x => x.jul_budgetDeviation) / Math.Abs(g1.Sum(x => x.jul_budget))) * 100 : 0,
                jul_accountYtd = g1.Sum(x => x.jul_accountYtd),
                jul_usagePctYtd = g1.Sum(x => x.jul_budget) != 0 ? (g1.Sum(x => x.jul_accountYtd) / Math.Abs(g1.Sum(x => x.jul_budget))) * 100 : 0,
                jul_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.jul_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                aug_origbudget = g1.Sum(x => x.aug_origbudget),
                aug_budget = g1.Sum(x => x.aug_budget),
                aug_account = g1.Sum(x => x.aug_account),
                aug_budgetDeviation = g1.Sum(x => x.aug_budgetDeviation),
                aug_budgetDeviationPct = g1.Sum(x => x.aug_budget) != 0 ? (g1.Sum(x => x.aug_budgetDeviation) / Math.Abs(g1.Sum(x => x.aug_budget))) * 100 : 0,
                aug_accountYtd = g1.Sum(x => x.aug_accountYtd),
                aug_usagePctYtd = g1.Sum(x => x.aug_budget) != 0 ? (g1.Sum(x => x.aug_accountYtd) / Math.Abs(g1.Sum(x => x.aug_budget))) * 100 : 0,
                aug_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.aug_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                sep_origbudget = g1.Sum(x => x.sep_origbudget),
                sep_budget = g1.Sum(x => x.sep_budget),
                sep_account = g1.Sum(x => x.sep_account),
                sep_budgetDeviation = g1.Sum(x => x.sep_budgetDeviation),
                sep_budgetDeviationPct = g1.Sum(x => x.sep_budget) != 0 ? (g1.Sum(x => x.sep_budgetDeviation) / Math.Abs(g1.Sum(x => x.sep_budget))) * 100 : 0,
                sep_accountYtd = g1.Sum(x => x.sep_accountYtd),
                sep_usagePctYtd = g1.Sum(x => x.sep_budget) != 0 ? (g1.Sum(x => x.sep_accountYtd) / Math.Abs(g1.Sum(x => x.sep_budget))) * 100 : 0,
                sep_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.sep_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                oct_origbudget = g1.Sum(x => x.oct_origbudget),
                oct_budget = g1.Sum(x => x.oct_budget),
                oct_account = g1.Sum(x => x.oct_account),
                oct_budgetDeviation = g1.Sum(x => x.oct_budgetDeviation),
                oct_budgetDeviationPct = g1.Sum(x => x.oct_budget) != 0 ? (g1.Sum(x => x.oct_budgetDeviation) / Math.Abs(g1.Sum(x => x.oct_budget))) * 100 : 0,
                oct_accountYtd = g1.Sum(x => x.oct_accountYtd),
                oct_usagePctYtd = g1.Sum(x => x.oct_budget) != 0 ? (g1.Sum(x => x.oct_accountYtd) / Math.Abs(g1.Sum(x => x.oct_budget))) * 100 : 0,
                oct_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.oct_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                nov_origbudget = g1.Sum(x => x.nov_origbudget),
                nov_budget = g1.Sum(x => x.nov_budget),
                nov_account = g1.Sum(x => x.nov_account),
                nov_budgetDeviation = g1.Sum(x => x.nov_budgetDeviation),
                nov_budgetDeviationPct = g1.Sum(x => x.nov_budget) != 0 ? (g1.Sum(x => x.nov_budgetDeviation) / Math.Abs(g1.Sum(x => x.nov_budget))) * 100 : 0,
                nov_accountYtd = g1.Sum(x => x.nov_accountYtd),
                nov_usagePctYtd = g1.Sum(x => x.nov_budget) != 0 ? (g1.Sum(x => x.nov_accountYtd) / Math.Abs(g1.Sum(x => x.nov_budget))) * 100 : 0,
                nov_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.nov_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                dec_origbudget = g1.Sum(x => x.dec_origbudget),
                dec_budget = g1.Sum(x => x.dec_budget),
                dec_account = g1.Sum(x => x.dec_account),
                dec_budgetDeviation = g1.Sum(x => x.dec_budgetDeviation),
                dec_budgetDeviationPct = g1.Sum(x => x.dec_budget) != 0 ? (g1.Sum(x => x.dec_budgetDeviation) / Math.Abs(g1.Sum(x => x.dec_budget))) * 100 : 0,
                dec_accountYtd = g1.Sum(x => x.dec_accountYtd),
                dec_usagePctYtd = g1.Sum(x => x.dec_budget) != 0 ? (g1.Sum(x => x.dec_accountYtd) / Math.Abs(g1.Sum(x => x.dec_budget))) * 100 : 0,
                dec_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.dec_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                yar_origbudget = g1.Sum(x => x.yar_origbudget),
                yar_budget = g1.Sum(x => x.yar_budget),
                yar_account = g1.Sum(x => x.yar_account),
                yar_budgetDeviation = g1.Sum(x => x.yar_budgetDeviation),
                yar_budgetDeviationPct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.yar_budgetDeviation) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,
                yar_accountYtd = g1.Sum(x => x.yar_accountYtd),
                yar_usagePctYtd = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.yar_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,
                yar_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.yar_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,
            }).ToList();

        var level2Data = (from a in serialzedData
            group a by new { a.Level_1_id, a.Level_2_id, a.Level_2_description } into g1
            select new AccStmtDataByPeriod_Excel
            {
                Id = $"{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-2",   // Level 3
                Parent = $"{g1.Key.Level_1_id}-1",     // Parent ID Level 2
                Level = 2,
                Name = $"-{emptyString}{emptyString}{emptyString}{g1.Key.Level_2_id} {g1.Key.Level_2_description}",
                jan_origbudget = g1.Sum(x => x.jan_origbudget),
                jan_budget = g1.Sum(x => x.jan_budget),
                jan_account = g1.Sum(x => x.jan_account),
                jan_budgetDeviation = g1.Sum(x => x.jan_budgetDeviation),
                jan_budgetDeviationPct = g1.Sum(x => x.jan_budget) != 0 ? (g1.Sum(x => x.jan_budgetDeviation) / Math.Abs(g1.Sum(x => x.jan_budget))) * 100 : 0,
                jan_accountYtd = g1.Sum(x => x.jan_accountYtd),
                jan_usagePctYtd = g1.Sum(x => x.jan_budget) != 0 ? (g1.Sum(x => x.jan_accountYtd) / Math.Abs(g1.Sum(x => x.jan_budget))) * 100 : 0,
                jan_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.jan_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                feb_origbudget = g1.Sum(x => x.feb_origbudget),
                feb_budget = g1.Sum(x => x.feb_budget),
                feb_account = g1.Sum(x => x.feb_account),
                feb_budgetDeviation = g1.Sum(x => x.feb_budgetDeviation),
                feb_budgetDeviationPct = g1.Sum(x => x.feb_budget) != 0 ? (g1.Sum(x => x.feb_budgetDeviation) / Math.Abs(g1.Sum(x => x.feb_budget))) * 100 : 0,
                feb_accountYtd = g1.Sum(x => x.feb_accountYtd),
                feb_usagePctYtd = g1.Sum(x => x.feb_budget) != 0 ? (g1.Sum(x => x.feb_accountYtd) / Math.Abs(g1.Sum(x => x.feb_budget))) * 100 : 0,
                feb_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.feb_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                mar_origbudget = g1.Sum(x => x.mar_origbudget),
                mar_budget = g1.Sum(x => x.mar_budget),
                mar_account = g1.Sum(x => x.mar_account),
                mar_budgetDeviation = g1.Sum(x => x.mar_budgetDeviation),
                mar_budgetDeviationPct = g1.Sum(x => x.mar_budget) != 0 ? (g1.Sum(x => x.mar_budgetDeviation) / Math.Abs(g1.Sum(x => x.mar_budget))) * 100 : 0,
                mar_accountYtd = g1.Sum(x => x.mar_accountYtd),
                mar_usagePctYtd = g1.Sum(x => x.mar_budget) != 0 ? (g1.Sum(x => x.mar_account) / Math.Abs(g1.Sum(x => x.mar_budget))) * 100 : 0,
                mar_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.mar_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                apr_origbudget = g1.Sum(x => x.apr_origbudget),
                apr_budget = g1.Sum(x => x.apr_budget),
                apr_account = g1.Sum(x => x.apr_account),
                apr_budgetDeviation = g1.Sum(x => x.apr_budgetDeviation),
                apr_budgetDeviationPct = g1.Sum(x => x.apr_budget) != 0 ? (g1.Sum(x => x.apr_budgetDeviation) / Math.Abs(g1.Sum(x => x.apr_budget))) * 100 : 0,
                apr_accountYtd = g1.Sum(x => x.apr_accountYtd),
                apr_usagePctYtd = g1.Sum(x => x.apr_budget) != 0 ? (g1.Sum(x => x.apr_accountYtd) / Math.Abs(g1.Sum(x => x.apr_budget))) * 100 : 0,
                apr_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.apr_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                may_origbudget = g1.Sum(x => x.may_origbudget),
                may_budget = g1.Sum(x => x.may_budget),
                may_account = g1.Sum(x => x.may_account),
                may_budgetDeviation = g1.Sum(x => x.may_budgetDeviation),
                may_budgetDeviationPct = g1.Sum(x => x.may_budget) != 0 ? (g1.Sum(x => x.may_budgetDeviation) / Math.Abs(g1.Sum(x => x.may_budget))) * 100 : 0,
                may_accountYtd = g1.Sum(x => x.may_accountYtd),
                may_usagePctYtd = g1.Sum(x => x.may_budget) != 0 ? (g1.Sum(x => x.may_accountYtd) / Math.Abs(g1.Sum(x => x.may_budget))) * 100 : 0,
                may_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.may_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                jun_origbudget = g1.Sum(x => x.jun_origbudget),
                jun_budget = g1.Sum(x => x.jun_budget),
                jun_account = g1.Sum(x => x.jun_account),
                jun_budgetDeviation = g1.Sum(x => x.jun_budgetDeviation),
                jun_budgetDeviationPct = g1.Sum(x => x.jun_budget) != 0 ? (g1.Sum(x => x.jun_budgetDeviation) / Math.Abs(g1.Sum(x => x.jun_budget))) * 100 : 0,
                jun_accountYtd = g1.Sum(x => x.jun_accountYtd),
                jun_usagePctYtd = g1.Sum(x => x.jun_budget) != 0 ? (g1.Sum(x => x.jun_accountYtd) / Math.Abs(g1.Sum(x => x.jun_budget))) * 100 : 0,
                jun_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.jun_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                jul_origbudget = g1.Sum(x => x.jul_origbudget),
                jul_budget = g1.Sum(x => x.jul_budget),
                jul_account = g1.Sum(x => x.jul_account),
                jul_budgetDeviation = g1.Sum(x => x.jul_budgetDeviation),
                jul_budgetDeviationPct = g1.Sum(x => x.jul_budget) != 0 ? (g1.Sum(x => x.jul_budgetDeviation) / Math.Abs(g1.Sum(x => x.jul_budget))) * 100 : 0,
                jul_accountYtd = g1.Sum(x => x.jul_accountYtd),
                jul_usagePctYtd = g1.Sum(x => x.jul_budget) != 0 ? (g1.Sum(x => x.jul_accountYtd) / Math.Abs(g1.Sum(x => x.jul_budget))) * 100 : 0,
                jul_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.jul_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                aug_origbudget = g1.Sum(x => x.aug_origbudget),
                aug_budget = g1.Sum(x => x.aug_budget),
                aug_account = g1.Sum(x => x.aug_account),
                aug_budgetDeviation = g1.Sum(x => x.aug_budgetDeviation),
                aug_budgetDeviationPct = g1.Sum(x => x.aug_budget) != 0 ? (g1.Sum(x => x.aug_budgetDeviation) / Math.Abs(g1.Sum(x => x.aug_budget))) * 100 : 0,
                aug_accountYtd = g1.Sum(x => x.aug_accountYtd),
                aug_usagePctYtd = g1.Sum(x => x.aug_budget) != 0 ? (g1.Sum(x => x.aug_accountYtd) / Math.Abs(g1.Sum(x => x.aug_budget))) * 100 : 0,
                aug_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.aug_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                sep_origbudget = g1.Sum(x => x.sep_origbudget),
                sep_budget = g1.Sum(x => x.sep_budget),
                sep_account = g1.Sum(x => x.sep_account),
                sep_budgetDeviation = g1.Sum(x => x.sep_budgetDeviation),
                sep_budgetDeviationPct = g1.Sum(x => x.sep_budget) != 0 ? (g1.Sum(x => x.sep_budgetDeviation) / Math.Abs(g1.Sum(x => x.sep_budget))) * 100 : 0,
                sep_accountYtd = g1.Sum(x => x.sep_accountYtd),
                sep_usagePctYtd = g1.Sum(x => x.sep_budget) != 0 ? (g1.Sum(x => x.sep_accountYtd) / Math.Abs(g1.Sum(x => x.sep_budget))) * 100 : 0,
                sep_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.sep_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                oct_origbudget = g1.Sum(x => x.oct_origbudget),
                oct_budget = g1.Sum(x => x.oct_budget),
                oct_account = g1.Sum(x => x.oct_account),
                oct_budgetDeviation = g1.Sum(x => x.oct_budgetDeviation),
                oct_budgetDeviationPct = g1.Sum(x => x.oct_budget) != 0 ? (g1.Sum(x => x.oct_budgetDeviation) / Math.Abs(g1.Sum(x => x.oct_budget))) * 100 : 0,
                oct_accountYtd = g1.Sum(x => x.oct_accountYtd),
                oct_usagePctYtd = g1.Sum(x => x.oct_budget) != 0 ? (g1.Sum(x => x.oct_accountYtd) / Math.Abs(g1.Sum(x => x.oct_budget))) * 100 : 0,
                oct_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.oct_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                nov_origbudget = g1.Sum(x => x.nov_origbudget),
                nov_budget = g1.Sum(x => x.nov_budget),
                nov_account = g1.Sum(x => x.nov_account),
                nov_budgetDeviation = g1.Sum(x => x.nov_budgetDeviation),
                nov_budgetDeviationPct = g1.Sum(x => x.nov_budget) != 0 ? (g1.Sum(x => x.nov_budgetDeviation) / Math.Abs(g1.Sum(x => x.nov_budget))) * 100 : 0,
                nov_accountYtd = g1.Sum(x => x.nov_accountYtd),
                nov_usagePctYtd = g1.Sum(x => x.nov_budget) != 0 ? (g1.Sum(x => x.nov_accountYtd) / Math.Abs(g1.Sum(x => x.nov_budget))) * 100 : 0,
                nov_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.nov_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                dec_origbudget = g1.Sum(x => x.dec_origbudget),
                dec_budget = g1.Sum(x => x.dec_budget),
                dec_account = g1.Sum(x => x.dec_account),
                dec_budgetDeviation = g1.Sum(x => x.dec_budgetDeviation),
                dec_budgetDeviationPct = g1.Sum(x => x.dec_budget) != 0 ? (g1.Sum(x => x.dec_budgetDeviation) / Math.Abs(g1.Sum(x => x.dec_budget))) * 100 : 0,
                dec_accountYtd = g1.Sum(x => x.dec_accountYtd),
                dec_usagePctYtd = g1.Sum(x => x.dec_budget) != 0 ? (g1.Sum(x => x.dec_accountYtd) / Math.Abs(g1.Sum(x => x.dec_budget))) * 100 : 0,
                dec_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.dec_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                yar_origbudget = g1.Sum(x => x.yar_origbudget),
                yar_budget = g1.Sum(x => x.yar_budget),
                yar_account = g1.Sum(x => x.yar_account),
                yar_budgetDeviation = g1.Sum(x => x.yar_budgetDeviation),
                yar_budgetDeviationPct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.yar_budgetDeviation) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,
                yar_accountYtd = g1.Sum(x => x.yar_accountYtd),
                yar_usagePctYtd = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.yar_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,
                yar_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.yar_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,
            }).ToList();

        var level3Data = (from a in serialzedData
            group a by new { a.Level_1_id, a.Level_2_id, a.Level_3_id, a.Level_3_description } into g1
            select new AccStmtDataByPeriod_Excel
            {
                Id = $"{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-{g1.Key.Level_3_id}-3",   // Level 4
                Parent = $"{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-2",     // Parent ID Level 3
                Level = 3,
                Name = $"-{emptyString}{emptyString}{emptyString}{emptyString}{g1.Key.Level_3_id} {g1.Key.Level_3_description}",
                jan_origbudget = g1.Sum(x => x.jan_origbudget),
                jan_budget = g1.Sum(x => x.jan_budget),
                jan_account = g1.Sum(x => x.jan_account),
                jan_budgetDeviation = g1.Sum(x => x.jan_budgetDeviation),
                jan_budgetDeviationPct = g1.Sum(x => x.jan_budget) != 0 ? (g1.Sum(x => x.jan_budgetDeviation) / Math.Abs(g1.Sum(x => x.jan_budget))) * 100 : 0,
                jan_accountYtd = g1.Sum(x => x.jan_accountYtd),
                jan_usagePctYtd = g1.Sum(x => x.jan_budget) != 0 ? (g1.Sum(x => x.jan_accountYtd) / Math.Abs(g1.Sum(x => x.jan_budget))) * 100 : 0,
                jan_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.jan_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                feb_origbudget = g1.Sum(x => x.feb_origbudget),
                feb_budget = g1.Sum(x => x.feb_budget),
                feb_account = g1.Sum(x => x.feb_account),
                feb_budgetDeviation = g1.Sum(x => x.feb_budgetDeviation),
                feb_budgetDeviationPct = g1.Sum(x => x.feb_budget) != 0 ? (g1.Sum(x => x.feb_budgetDeviation) / Math.Abs(g1.Sum(x => x.feb_budget))) * 100 : 0,
                feb_accountYtd = g1.Sum(x => x.feb_accountYtd),
                feb_usagePctYtd = g1.Sum(x => x.feb_budget) != 0 ? (g1.Sum(x => x.feb_accountYtd) / Math.Abs(g1.Sum(x => x.feb_budget))) * 100 : 0,
                feb_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.feb_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                mar_origbudget = g1.Sum(x => x.mar_origbudget),
                mar_budget = g1.Sum(x => x.mar_budget),
                mar_account = g1.Sum(x => x.mar_account),
                mar_budgetDeviation = g1.Sum(x => x.mar_budgetDeviation),
                mar_budgetDeviationPct = g1.Sum(x => x.mar_budget) != 0 ? (g1.Sum(x => x.mar_budgetDeviation) / Math.Abs(g1.Sum(x => x.mar_budget))) * 100 : 0,
                mar_accountYtd = g1.Sum(x => x.mar_accountYtd),
                mar_usagePctYtd = g1.Sum(x => x.mar_budget) != 0 ? (g1.Sum(x => x.mar_account) / Math.Abs(g1.Sum(x => x.mar_budget))) * 100 : 0,
                mar_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.mar_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                apr_origbudget = g1.Sum(x => x.apr_origbudget),
                apr_budget = g1.Sum(x => x.apr_budget),
                apr_account = g1.Sum(x => x.apr_account),
                apr_budgetDeviation = g1.Sum(x => x.apr_budgetDeviation),
                apr_budgetDeviationPct = g1.Sum(x => x.apr_budget) != 0 ? (g1.Sum(x => x.apr_budgetDeviation) / Math.Abs(g1.Sum(x => x.apr_budget))) * 100 : 0,
                apr_accountYtd = g1.Sum(x => x.apr_accountYtd),
                apr_usagePctYtd = g1.Sum(x => x.apr_budget) != 0 ? (g1.Sum(x => x.apr_accountYtd) / Math.Abs(g1.Sum(x => x.apr_budget))) * 100 : 0,
                apr_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.apr_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                may_origbudget = g1.Sum(x => x.may_origbudget),
                may_budget = g1.Sum(x => x.may_budget),
                may_account = g1.Sum(x => x.may_account),
                may_budgetDeviation = g1.Sum(x => x.may_budgetDeviation),
                may_budgetDeviationPct = g1.Sum(x => x.may_budget) != 0 ? (g1.Sum(x => x.may_budgetDeviation) / Math.Abs(g1.Sum(x => x.may_budget))) * 100 : 0,
                may_accountYtd = g1.Sum(x => x.may_accountYtd),
                may_usagePctYtd = g1.Sum(x => x.may_budget) != 0 ? (g1.Sum(x => x.may_accountYtd) / Math.Abs(g1.Sum(x => x.may_budget))) * 100 : 0,
                may_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.may_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                jun_origbudget = g1.Sum(x => x.jun_origbudget),
                jun_budget = g1.Sum(x => x.jun_budget),
                jun_account = g1.Sum(x => x.jun_account),
                jun_budgetDeviation = g1.Sum(x => x.jun_budgetDeviation),
                jun_budgetDeviationPct = g1.Sum(x => x.jun_budget) != 0 ? (g1.Sum(x => x.jun_budgetDeviation) / Math.Abs(g1.Sum(x => x.jun_budget))) * 100 : 0,
                jun_accountYtd = g1.Sum(x => x.jun_accountYtd),
                jun_usagePctYtd = g1.Sum(x => x.jun_budget) != 0 ? (g1.Sum(x => x.jun_accountYtd) / Math.Abs(g1.Sum(x => x.jun_budget))) * 100 : 0,
                jun_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.jun_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                jul_origbudget = g1.Sum(x => x.jul_origbudget),
                jul_budget = g1.Sum(x => x.jul_budget),
                jul_account = g1.Sum(x => x.jul_account),
                jul_budgetDeviation = g1.Sum(x => x.jul_budgetDeviation),
                jul_budgetDeviationPct = g1.Sum(x => x.jul_budget) != 0 ? (g1.Sum(x => x.jul_budgetDeviation) / Math.Abs(g1.Sum(x => x.jul_budget))) * 100 : 0,
                jul_accountYtd = g1.Sum(x => x.jul_accountYtd),
                jul_usagePctYtd = g1.Sum(x => x.jul_budget) != 0 ? (g1.Sum(x => x.jul_accountYtd) / Math.Abs(g1.Sum(x => x.jul_budget))) * 100 : 0,
                jul_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.jul_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                aug_origbudget = g1.Sum(x => x.aug_origbudget),
                aug_budget = g1.Sum(x => x.aug_budget),
                aug_account = g1.Sum(x => x.aug_account),
                aug_budgetDeviation = g1.Sum(x => x.aug_budgetDeviation),
                aug_budgetDeviationPct = g1.Sum(x => x.aug_budget) != 0 ? (g1.Sum(x => x.aug_budgetDeviation) / Math.Abs(g1.Sum(x => x.aug_budget))) * 100 : 0,
                aug_accountYtd = g1.Sum(x => x.aug_accountYtd),
                aug_usagePctYtd = g1.Sum(x => x.aug_budget) != 0 ? (g1.Sum(x => x.aug_accountYtd) / Math.Abs(g1.Sum(x => x.aug_budget))) * 100 : 0,
                aug_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.aug_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                sep_origbudget = g1.Sum(x => x.sep_origbudget),
                sep_budget = g1.Sum(x => x.sep_budget),
                sep_account = g1.Sum(x => x.sep_account),
                sep_budgetDeviation = g1.Sum(x => x.sep_budgetDeviation),
                sep_budgetDeviationPct = g1.Sum(x => x.sep_budget) != 0 ? (g1.Sum(x => x.sep_budgetDeviation) / Math.Abs(g1.Sum(x => x.sep_budget))) * 100 : 0,
                sep_accountYtd = g1.Sum(x => x.sep_accountYtd),
                sep_usagePctYtd = g1.Sum(x => x.sep_budget) != 0 ? (g1.Sum(x => x.sep_accountYtd) / Math.Abs(g1.Sum(x => x.sep_budget))) * 100 : 0,
                sep_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.sep_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                oct_origbudget = g1.Sum(x => x.oct_origbudget),
                oct_budget = g1.Sum(x => x.oct_budget),
                oct_account = g1.Sum(x => x.oct_account),
                oct_budgetDeviation = g1.Sum(x => x.oct_budgetDeviation),
                oct_budgetDeviationPct = g1.Sum(x => x.oct_budget) != 0 ? (g1.Sum(x => x.oct_budgetDeviation) / Math.Abs(g1.Sum(x => x.oct_budget))) * 100 : 0,
                oct_accountYtd = g1.Sum(x => x.oct_accountYtd),
                oct_usagePctYtd = g1.Sum(x => x.oct_budget) != 0 ? (g1.Sum(x => x.oct_accountYtd) / Math.Abs(g1.Sum(x => x.oct_budget))) * 100 : 0,
                oct_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.oct_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                nov_origbudget = g1.Sum(x => x.nov_origbudget),
                nov_budget = g1.Sum(x => x.nov_budget),
                nov_account = g1.Sum(x => x.nov_account),
                nov_budgetDeviation = g1.Sum(x => x.nov_budgetDeviation),
                nov_budgetDeviationPct = g1.Sum(x => x.nov_budget) != 0 ? (g1.Sum(x => x.nov_budgetDeviation) / Math.Abs(g1.Sum(x => x.nov_budget))) * 100 : 0,
                nov_accountYtd = g1.Sum(x => x.nov_accountYtd),
                nov_usagePctYtd = g1.Sum(x => x.nov_budget) != 0 ? (g1.Sum(x => x.nov_accountYtd) / Math.Abs(g1.Sum(x => x.nov_budget))) * 100 : 0,
                nov_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.nov_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                dec_origbudget = g1.Sum(x => x.dec_origbudget),
                dec_budget = g1.Sum(x => x.dec_budget),
                dec_account = g1.Sum(x => x.dec_account),
                dec_budgetDeviation = g1.Sum(x => x.dec_budgetDeviation),
                dec_budgetDeviationPct = g1.Sum(x => x.dec_budget) != 0 ? (g1.Sum(x => x.dec_budgetDeviation) / Math.Abs(g1.Sum(x => x.dec_budget))) * 100 : 0,
                dec_accountYtd = g1.Sum(x => x.dec_accountYtd),
                dec_usagePctYtd = g1.Sum(x => x.dec_budget) != 0 ? (g1.Sum(x => x.dec_accountYtd) / Math.Abs(g1.Sum(x => x.dec_budget))) * 100 : 0,
                dec_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.dec_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                yar_origbudget = g1.Sum(x => x.yar_origbudget),
                yar_budget = g1.Sum(x => x.yar_budget),
                yar_account = g1.Sum(x => x.yar_account),
                yar_budgetDeviation = g1.Sum(x => x.yar_budgetDeviation),
                yar_budgetDeviationPct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.yar_budgetDeviation) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,
                yar_accountYtd = g1.Sum(x => x.yar_accountYtd),
                yar_usagePctYtd = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.yar_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,
                yar_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.yar_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,
            }).ToList();

        var level4Data = (from a in serialzedData
            group a by new { a.Level_1_id, a.Level_2_id, a.Level_3_id, a.Level_4_id, a.Level_4_description } into g1
            select new AccStmtDataByPeriod_Excel
            {
                Id = $"{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-{g1.Key.Level_3_id}-{g1.Key.Level_4_id}-4",   // Level 5
                Parent = $"{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-{g1.Key.Level_3_id}-3",     // Parent ID Level 4
                Level = 4,
                Name = $"-{emptyString}{emptyString}{emptyString}{emptyString}{emptyString}{g1.Key.Level_4_id} {g1.Key.Level_4_description}",
                jan_origbudget = g1.Sum(x => x.jan_origbudget),
                jan_budget = g1.Sum(x => x.jan_budget),
                jan_account = g1.Sum(x => x.jan_account),
                jan_budgetDeviation = g1.Sum(x => x.jan_budgetDeviation),
                jan_budgetDeviationPct = g1.Sum(x => x.jan_budget) != 0 ? (g1.Sum(x => x.jan_budgetDeviation) / Math.Abs(g1.Sum(x => x.jan_budget))) * 100 : 0,
                jan_accountYtd = g1.Sum(x => x.jan_accountYtd),
                jan_usagePctYtd = g1.Sum(x => x.jan_budget) != 0 ? (g1.Sum(x => x.jan_accountYtd) / Math.Abs(g1.Sum(x => x.jan_budget))) * 100 : 0,
                jan_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.jan_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                feb_origbudget = g1.Sum(x => x.feb_origbudget),
                feb_budget = g1.Sum(x => x.feb_budget),
                feb_account = g1.Sum(x => x.feb_account),
                feb_budgetDeviation = g1.Sum(x => x.feb_budgetDeviation),
                feb_budgetDeviationPct = g1.Sum(x => x.feb_budget) != 0 ? (g1.Sum(x => x.feb_budgetDeviation) / Math.Abs(g1.Sum(x => x.feb_budget))) * 100 : 0,
                feb_accountYtd = g1.Sum(x => x.feb_accountYtd),
                feb_usagePctYtd = g1.Sum(x => x.feb_budget) != 0 ? (g1.Sum(x => x.feb_accountYtd) / Math.Abs(g1.Sum(x => x.feb_budget))) * 100 : 0,
                feb_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.feb_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                mar_origbudget = g1.Sum(x => x.mar_origbudget),
                mar_budget = g1.Sum(x => x.mar_budget),
                mar_account = g1.Sum(x => x.mar_account),
                mar_budgetDeviation = g1.Sum(x => x.mar_budgetDeviation),
                mar_budgetDeviationPct = g1.Sum(x => x.mar_budget) != 0 ? (g1.Sum(x => x.mar_budgetDeviation) / Math.Abs(g1.Sum(x => x.mar_budget))) * 100 : 0,
                mar_accountYtd = g1.Sum(x => x.mar_accountYtd),
                mar_usagePctYtd = g1.Sum(x => x.mar_budget) != 0 ? (g1.Sum(x => x.mar_account) / Math.Abs(g1.Sum(x => x.mar_budget))) * 100 : 0,
                mar_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.mar_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                apr_origbudget = g1.Sum(x => x.apr_origbudget),
                apr_budget = g1.Sum(x => x.apr_budget),
                apr_account = g1.Sum(x => x.apr_account),
                apr_budgetDeviation = g1.Sum(x => x.apr_budgetDeviation),
                apr_budgetDeviationPct = g1.Sum(x => x.apr_budget) != 0 ? (g1.Sum(x => x.apr_budgetDeviation) / Math.Abs(g1.Sum(x => x.apr_budget))) * 100 : 0,
                apr_accountYtd = g1.Sum(x => x.apr_accountYtd),
                apr_usagePctYtd = g1.Sum(x => x.apr_budget) != 0 ? (g1.Sum(x => x.apr_accountYtd) / Math.Abs(g1.Sum(x => x.apr_budget))) * 100 : 0,
                apr_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.apr_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                may_origbudget = g1.Sum(x => x.may_origbudget),
                may_budget = g1.Sum(x => x.may_budget),
                may_account = g1.Sum(x => x.may_account),
                may_budgetDeviation = g1.Sum(x => x.may_budgetDeviation),
                may_budgetDeviationPct = g1.Sum(x => x.may_budget) != 0 ? (g1.Sum(x => x.may_budgetDeviation) / Math.Abs(g1.Sum(x => x.may_budget))) * 100 : 0,
                may_accountYtd = g1.Sum(x => x.may_accountYtd),
                may_usagePctYtd = g1.Sum(x => x.may_budget) != 0 ? (g1.Sum(x => x.may_accountYtd) / Math.Abs(g1.Sum(x => x.may_budget))) * 100 : 0,
                may_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.may_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                jun_origbudget = g1.Sum(x => x.jun_origbudget),
                jun_budget = g1.Sum(x => x.jun_budget),
                jun_account = g1.Sum(x => x.jun_account),
                jun_budgetDeviation = g1.Sum(x => x.jun_budgetDeviation),
                jun_budgetDeviationPct = g1.Sum(x => x.jun_budget) != 0 ? (g1.Sum(x => x.jun_budgetDeviation) / Math.Abs(g1.Sum(x => x.jun_budget))) * 100 : 0,
                jun_accountYtd = g1.Sum(x => x.jun_accountYtd),
                jun_usagePctYtd = g1.Sum(x => x.jun_budget) != 0 ? (g1.Sum(x => x.jun_accountYtd) / Math.Abs(g1.Sum(x => x.jun_budget))) * 100 : 0,
                jun_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.jun_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                jul_origbudget = g1.Sum(x => x.jul_origbudget),
                jul_budget = g1.Sum(x => x.jul_budget),
                jul_account = g1.Sum(x => x.jul_account),
                jul_budgetDeviation = g1.Sum(x => x.jul_budgetDeviation),
                jul_budgetDeviationPct = g1.Sum(x => x.jul_budget) != 0 ? (g1.Sum(x => x.jul_budgetDeviation) / Math.Abs(g1.Sum(x => x.jul_budget))) * 100 : 0,
                jul_accountYtd = g1.Sum(x => x.jul_accountYtd),
                jul_usagePctYtd = g1.Sum(x => x.jul_budget) != 0 ? (g1.Sum(x => x.jul_accountYtd) / Math.Abs(g1.Sum(x => x.jul_budget))) * 100 : 0,
                jul_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.jul_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                aug_origbudget = g1.Sum(x => x.aug_origbudget),
                aug_budget = g1.Sum(x => x.aug_budget),
                aug_account = g1.Sum(x => x.aug_account),
                aug_budgetDeviation = g1.Sum(x => x.aug_budgetDeviation),
                aug_budgetDeviationPct = g1.Sum(x => x.aug_budget) != 0 ? (g1.Sum(x => x.aug_budgetDeviation) / Math.Abs(g1.Sum(x => x.aug_budget))) * 100 : 0,
                aug_accountYtd = g1.Sum(x => x.aug_accountYtd),
                aug_usagePctYtd = g1.Sum(x => x.aug_budget) != 0 ? (g1.Sum(x => x.aug_accountYtd) / Math.Abs(g1.Sum(x => x.aug_budget))) * 100 : 0,
                aug_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.aug_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                sep_origbudget = g1.Sum(x => x.sep_origbudget),
                sep_budget = g1.Sum(x => x.sep_budget),
                sep_account = g1.Sum(x => x.sep_account),
                sep_budgetDeviation = g1.Sum(x => x.sep_budgetDeviation),
                sep_budgetDeviationPct = g1.Sum(x => x.sep_budget) != 0 ? (g1.Sum(x => x.sep_budgetDeviation) / Math.Abs(g1.Sum(x => x.sep_budget))) * 100 : 0,
                sep_accountYtd = g1.Sum(x => x.sep_accountYtd),
                sep_usagePctYtd = g1.Sum(x => x.sep_budget) != 0 ? (g1.Sum(x => x.sep_accountYtd) / Math.Abs(g1.Sum(x => x.sep_budget))) * 100 : 0,
                sep_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.sep_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                oct_origbudget = g1.Sum(x => x.oct_origbudget),
                oct_budget = g1.Sum(x => x.oct_budget),
                oct_account = g1.Sum(x => x.oct_account),
                oct_budgetDeviation = g1.Sum(x => x.oct_budgetDeviation),
                oct_budgetDeviationPct = g1.Sum(x => x.oct_budget) != 0 ? (g1.Sum(x => x.oct_budgetDeviation) / Math.Abs(g1.Sum(x => x.oct_budget))) * 100 : 0,
                oct_accountYtd = g1.Sum(x => x.oct_accountYtd),
                oct_usagePctYtd = g1.Sum(x => x.oct_budget) != 0 ? (g1.Sum(x => x.oct_accountYtd) / Math.Abs(g1.Sum(x => x.oct_budget))) * 100 : 0,
                oct_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.oct_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                nov_origbudget = g1.Sum(x => x.nov_origbudget),
                nov_budget = g1.Sum(x => x.nov_budget),
                nov_account = g1.Sum(x => x.nov_account),
                nov_budgetDeviation = g1.Sum(x => x.nov_budgetDeviation),
                nov_budgetDeviationPct = g1.Sum(x => x.nov_budget) != 0 ? (g1.Sum(x => x.nov_budgetDeviation) / Math.Abs(g1.Sum(x => x.nov_budget))) * 100 : 0,
                nov_accountYtd = g1.Sum(x => x.nov_accountYtd),
                nov_usagePctYtd = g1.Sum(x => x.nov_budget) != 0 ? (g1.Sum(x => x.nov_accountYtd) / Math.Abs(g1.Sum(x => x.nov_budget))) * 100 : 0,
                nov_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.nov_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                dec_origbudget = g1.Sum(x => x.dec_origbudget),
                dec_budget = g1.Sum(x => x.dec_budget),
                dec_account = g1.Sum(x => x.dec_account),
                dec_budgetDeviation = g1.Sum(x => x.dec_budgetDeviation),
                dec_budgetDeviationPct = g1.Sum(x => x.dec_budget) != 0 ? (g1.Sum(x => x.dec_budgetDeviation) / Math.Abs(g1.Sum(x => x.dec_budget))) * 100 : 0,
                dec_accountYtd = g1.Sum(x => x.dec_accountYtd),
                dec_usagePctYtd = g1.Sum(x => x.dec_budget) != 0 ? (g1.Sum(x => x.dec_accountYtd) / Math.Abs(g1.Sum(x => x.dec_budget))) * 100 : 0,
                dec_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.dec_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                yar_origbudget = g1.Sum(x => x.yar_origbudget),
                yar_budget = g1.Sum(x => x.yar_budget),
                yar_account = g1.Sum(x => x.yar_account),
                yar_budgetDeviation = g1.Sum(x => x.yar_budgetDeviation),
                yar_budgetDeviationPct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.yar_budgetDeviation) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,
                yar_accountYtd = g1.Sum(x => x.yar_accountYtd),
                yar_usagePctYtd = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.yar_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,
                yar_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.yar_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,
            }).ToList();

        var level5Data = (from a in serialzedData
            group a by new { a.Level_1_id, a.Level_2_id, a.Level_3_id, a.Level_4_id, a.Level_5_id, a.Level_5_description } into g1
            select new AccStmtDataByPeriod_Excel
            {
                Id = $"{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-{g1.Key.Level_3_id}-{g1.Key.Level_4_id}-{g1.Key.Level_5_id}-5",   // Level 5
                Parent = $"{g1.Key.Level_1_id}-{g1.Key.Level_2_id}-{g1.Key.Level_3_id}-{g1.Key.Level_4_id}-4",     // Parent ID Level 4
                Level = 5,
                Name = $"-{emptyString}{emptyString}{emptyString}{emptyString}{emptyString}{emptyString}{g1.Key.Level_5_id} {g1.Key.Level_5_description}",
                jan_origbudget = g1.Sum(x => x.jan_origbudget),
                jan_budget = g1.Sum(x => x.jan_budget),
                jan_account = g1.Sum(x => x.jan_account),
                jan_budgetDeviation = g1.Sum(x => x.jan_budgetDeviation),
                jan_budgetDeviationPct = g1.Sum(x => x.jan_budget) != 0 ? (g1.Sum(x => x.jan_budgetDeviation) / Math.Abs(g1.Sum(x => x.jan_budget))) * 100 : 0,
                jan_accountYtd = g1.Sum(x => x.jan_accountYtd),
                jan_usagePctYtd = g1.Sum(x => x.jan_budget) != 0 ? (g1.Sum(x => x.jan_accountYtd) / Math.Abs(g1.Sum(x => x.jan_budget))) * 100 : 0,
                jan_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.jan_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                feb_origbudget = g1.Sum(x => x.feb_origbudget),
                feb_budget = g1.Sum(x => x.feb_budget),
                feb_account = g1.Sum(x => x.feb_account),
                feb_budgetDeviation = g1.Sum(x => x.feb_budgetDeviation),
                feb_budgetDeviationPct = g1.Sum(x => x.feb_budget) != 0 ? (g1.Sum(x => x.feb_budgetDeviation) / Math.Abs(g1.Sum(x => x.feb_budget))) * 100 : 0,
                feb_accountYtd = g1.Sum(x => x.feb_accountYtd),
                feb_usagePctYtd = g1.Sum(x => x.feb_budget) != 0 ? (g1.Sum(x => x.feb_accountYtd) / Math.Abs(g1.Sum(x => x.feb_budget))) * 100 : 0,
                feb_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.feb_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                mar_origbudget = g1.Sum(x => x.mar_origbudget),
                mar_budget = g1.Sum(x => x.mar_budget),
                mar_account = g1.Sum(x => x.mar_account),
                mar_budgetDeviation = g1.Sum(x => x.mar_budgetDeviation),
                mar_budgetDeviationPct = g1.Sum(x => x.mar_budget) != 0 ? (g1.Sum(x => x.mar_budgetDeviation) / Math.Abs(g1.Sum(x => x.mar_budget))) * 100 : 0,
                mar_accountYtd = g1.Sum(x => x.mar_accountYtd),
                mar_usagePctYtd = g1.Sum(x => x.mar_budget) != 0 ? (g1.Sum(x => x.mar_account) / Math.Abs(g1.Sum(x => x.mar_budget))) * 100 : 0,
                mar_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.mar_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                apr_origbudget = g1.Sum(x => x.apr_origbudget),
                apr_budget = g1.Sum(x => x.apr_budget),
                apr_account = g1.Sum(x => x.apr_account),
                apr_budgetDeviation = g1.Sum(x => x.apr_budgetDeviation),
                apr_budgetDeviationPct = g1.Sum(x => x.apr_budget) != 0 ? (g1.Sum(x => x.apr_budgetDeviation) / Math.Abs(g1.Sum(x => x.apr_budget))) * 100 : 0,
                apr_accountYtd = g1.Sum(x => x.apr_accountYtd),
                apr_usagePctYtd = g1.Sum(x => x.apr_budget) != 0 ? (g1.Sum(x => x.apr_accountYtd) / Math.Abs(g1.Sum(x => x.apr_budget))) * 100 : 0,
                apr_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.apr_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                may_origbudget = g1.Sum(x => x.may_origbudget),
                may_budget = g1.Sum(x => x.may_budget),
                may_account = g1.Sum(x => x.may_account),
                may_budgetDeviation = g1.Sum(x => x.may_budgetDeviation),
                may_budgetDeviationPct = g1.Sum(x => x.may_budget) != 0 ? (g1.Sum(x => x.may_budgetDeviation) / Math.Abs(g1.Sum(x => x.may_budget))) * 100 : 0,
                may_accountYtd = g1.Sum(x => x.may_accountYtd),
                may_usagePctYtd = g1.Sum(x => x.may_budget) != 0 ? (g1.Sum(x => x.may_accountYtd) / Math.Abs(g1.Sum(x => x.may_budget))) * 100 : 0,
                may_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.may_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                jun_origbudget = g1.Sum(x => x.jun_origbudget),
                jun_budget = g1.Sum(x => x.jun_budget),
                jun_account = g1.Sum(x => x.jun_account),
                jun_budgetDeviation = g1.Sum(x => x.jun_budgetDeviation),
                jun_budgetDeviationPct = g1.Sum(x => x.jun_budget) != 0 ? (g1.Sum(x => x.jun_budgetDeviation) / Math.Abs(g1.Sum(x => x.jun_budget))) * 100 : 0,
                jun_accountYtd = g1.Sum(x => x.jun_accountYtd),
                jun_usagePctYtd = g1.Sum(x => x.jun_budget) != 0 ? (g1.Sum(x => x.jun_accountYtd) / Math.Abs(g1.Sum(x => x.jun_budget))) * 100 : 0,
                jun_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.jun_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                jul_origbudget = g1.Sum(x => x.jul_origbudget),
                jul_budget = g1.Sum(x => x.jul_budget),
                jul_account = g1.Sum(x => x.jul_account),
                jul_budgetDeviation = g1.Sum(x => x.jul_budgetDeviation),
                jul_budgetDeviationPct = g1.Sum(x => x.jul_budget) != 0 ? (g1.Sum(x => x.jul_budgetDeviation) / Math.Abs(g1.Sum(x => x.jul_budget))) * 100 : 0,
                jul_accountYtd = g1.Sum(x => x.jul_accountYtd),
                jul_usagePctYtd = g1.Sum(x => x.jul_budget) != 0 ? (g1.Sum(x => x.jul_accountYtd) / Math.Abs(g1.Sum(x => x.jul_budget))) * 100 : 0,
                jul_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.jul_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                aug_origbudget = g1.Sum(x => x.aug_origbudget),
                aug_budget = g1.Sum(x => x.aug_budget),
                aug_account = g1.Sum(x => x.aug_account),
                aug_budgetDeviation = g1.Sum(x => x.aug_budgetDeviation),
                aug_budgetDeviationPct = g1.Sum(x => x.aug_budget) != 0 ? (g1.Sum(x => x.aug_budgetDeviation) / Math.Abs(g1.Sum(x => x.aug_budget))) * 100 : 0,
                aug_accountYtd = g1.Sum(x => x.aug_accountYtd),
                aug_usagePctYtd = g1.Sum(x => x.aug_budget) != 0 ? (g1.Sum(x => x.aug_accountYtd) / Math.Abs(g1.Sum(x => x.aug_budget))) * 100 : 0,
                aug_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.aug_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                sep_origbudget = g1.Sum(x => x.sep_origbudget),
                sep_budget = g1.Sum(x => x.sep_budget),
                sep_account = g1.Sum(x => x.sep_account),
                sep_budgetDeviation = g1.Sum(x => x.sep_budgetDeviation),
                sep_budgetDeviationPct = g1.Sum(x => x.sep_budget) != 0 ? (g1.Sum(x => x.sep_budgetDeviation) / Math.Abs(g1.Sum(x => x.sep_budget))) * 100 : 0,
                sep_accountYtd = g1.Sum(x => x.sep_accountYtd),
                sep_usagePctYtd = g1.Sum(x => x.sep_budget) != 0 ? (g1.Sum(x => x.sep_accountYtd) / Math.Abs(g1.Sum(x => x.sep_budget))) * 100 : 0,
                sep_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.sep_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                oct_origbudget = g1.Sum(x => x.oct_origbudget),
                oct_budget = g1.Sum(x => x.oct_budget),
                oct_account = g1.Sum(x => x.oct_account),
                oct_budgetDeviation = g1.Sum(x => x.oct_budgetDeviation),
                oct_budgetDeviationPct = g1.Sum(x => x.oct_budget) != 0 ? (g1.Sum(x => x.oct_budgetDeviation) / Math.Abs(g1.Sum(x => x.oct_budget))) * 100 : 0,
                oct_accountYtd = g1.Sum(x => x.oct_accountYtd),
                oct_usagePctYtd = g1.Sum(x => x.oct_budget) != 0 ? (g1.Sum(x => x.oct_accountYtd) / Math.Abs(g1.Sum(x => x.oct_budget))) * 100 : 0,
                oct_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.oct_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                nov_origbudget = g1.Sum(x => x.nov_origbudget),
                nov_budget = g1.Sum(x => x.nov_budget),
                nov_account = g1.Sum(x => x.nov_account),
                nov_budgetDeviation = g1.Sum(x => x.nov_budgetDeviation),
                nov_budgetDeviationPct = g1.Sum(x => x.nov_budget) != 0 ? (g1.Sum(x => x.nov_budgetDeviation) / Math.Abs(g1.Sum(x => x.nov_budget))) * 100 : 0,
                nov_accountYtd = g1.Sum(x => x.nov_accountYtd),
                nov_usagePctYtd = g1.Sum(x => x.nov_budget) != 0 ? (g1.Sum(x => x.nov_accountYtd) / Math.Abs(g1.Sum(x => x.nov_budget))) * 100 : 0,
                nov_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.nov_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                dec_origbudget = g1.Sum(x => x.dec_origbudget),
                dec_budget = g1.Sum(x => x.dec_budget),
                dec_account = g1.Sum(x => x.dec_account),
                dec_budgetDeviation = g1.Sum(x => x.dec_budgetDeviation),
                dec_budgetDeviationPct = g1.Sum(x => x.dec_budget) != 0 ? (g1.Sum(x => x.dec_budgetDeviation) / Math.Abs(g1.Sum(x => x.dec_budget))) * 100 : 0,
                dec_accountYtd = g1.Sum(x => x.dec_accountYtd),
                dec_usagePctYtd = g1.Sum(x => x.dec_budget) != 0 ? (g1.Sum(x => x.dec_accountYtd) / Math.Abs(g1.Sum(x => x.dec_budget))) * 100 : 0,
                dec_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.dec_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,

                yar_origbudget = g1.Sum(x => x.yar_origbudget),
                yar_budget = g1.Sum(x => x.yar_budget),
                yar_account = g1.Sum(x => x.yar_account),
                yar_budgetDeviation = g1.Sum(x => x.yar_budgetDeviation),
                yar_budgetDeviationPct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.yar_budgetDeviation) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,
                yar_accountYtd = g1.Sum(x => x.yar_accountYtd),
                yar_usagePctYtd = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.yar_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,
                yar_usagePct = g1.Sum(x => x.yar_budget) != 0 ? (g1.Sum(x => x.yar_accountYtd) / Math.Abs(g1.Sum(x => x.yar_budget))) * 100 : 0,
            }).ToList();


        switch (accountDisplayLevel)
        {

            case 1: orgData.AddRange(level1Data); break;
            case 2:
                orgData.AddRange(level1Data);
                orgData.AddRange(level2Data);
                ; break;

            case 3:
                orgData.AddRange(level1Data);
                orgData.AddRange(level2Data);
                orgData.AddRange(level3Data);
                ; break;
            case 4:
                orgData.AddRange(level1Data);
                orgData.AddRange(level2Data);
                orgData.AddRange(level3Data);
                orgData.AddRange(level4Data);
                ; break;
            case 5:
                orgData.AddRange(level1Data);
                orgData.AddRange(level2Data);
                orgData.AddRange(level3Data);
                orgData.AddRange(level4Data);
                orgData.AddRange(level5Data);
                ; break;
        }

        return orgData;
    }




    public async Task UpdateExcelExportStatusToJobsTable(AccountStmtExcelExportInput gridInput, string Status, String errormsg, int steps)
    {
        var data = await _unitOfWork.DashBoardWidgetsRepo.GetTcoJobStatusData(gridInput.tenantId, gridInput.jobId);

        if (data != null)
        {

            data.jobStatus = Status;
            data.StepsCompleted = steps;
            data.message = errormsg;
            data.EndTime = DateTime.UtcNow;
        }
        await _unitOfWork.CompleteAsync();
    }

}