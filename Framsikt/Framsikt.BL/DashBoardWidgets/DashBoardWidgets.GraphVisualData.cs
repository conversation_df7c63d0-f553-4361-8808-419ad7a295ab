using Framsikt.BL.Core;
using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Data;
using System.Globalization;

namespace Framsikt.BL;

public partial class DashBoardWidgets : IDashBoardWidgets
{


    //#endregion GetCumulativeAccBudget

    //#region org level gragh -Budsjettavvik per eining/ansvar

    public async Task<AccBudPerPeriodData> GetAccGraphOrgLevelData(AccBudPeriodInput input, string userId)
    {
        string orgVersion = await _utility.GetActiveOrgVersion(userId, input.period);
        input.orgVersion = orgVersion;
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);

        //get budget lock status
        var type = "LOCK_ORIGINAL_BUDGET";
        var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, input.period);
        input.budgetYear = input.period / 100;
        var isLocked = await _utility.CheckOnYearlyBudgetLockByBudgetYearAsync(orgVersionContent, userDetails.tenant_id, userId, input.budgetYear, input.orgId, input.orgLevel.ToString(), type);

        var budget = _unitOfWork.DashBoardWidgetsRepo.GetBudget(input, userDetails.tenant_id, userId, isLocked);
        var account = _unitOfWork.DashBoardWidgetsRepo.GetAccountingData(input, userDetails.tenant_id, userId, isLocked);
        await Task.WhenAll(budget, account);

        var budData = budget.Result;
        var accData = account.Result;

        AccBudPerPeriodData finalData = new AccBudPerPeriodData
        {
            accountingData = (from acc in accData
                select new AccBudPerPeriodHelper()
                {
                    period = acc.Field<int>("period"),
                    accountingData = acc.Field<decimal?>("acc")
                }).ToList(),

            budgetData = (from bud in budData
                select new AccBudPerPeriodHelper()
                {
                    period = bud.Field<int>("period"),
                    budget = bud.Field<decimal?>("bud")
                }).ToList()
        };
        return finalData;
    }


    public async Task<List<AccServiceResultData>> GetAccGraphServiceLevelData(AccBudServiceInput input, string userId)
    {
        string orgVersion = await _utility.GetActiveOrgVersion(userId, input.period);
        input.orgVersion = orgVersion;
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        input.budgetYear = input.period / 100;
        //get budget lock status
        var type = "LOCK_ORIGINAL_BUDGET";
        var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, input.period);
        var isLocked = await _utility.CheckOnYearlyBudgetLockByBudgetYearAsync(orgVersionContent, userDetails.tenant_id, userId, input.budgetYear, input.orgId, input.orgLevel.ToString(), type);

        var accountAsync = _unitOfWork.DashBoardWidgetsRepo.GetServiceAccountingDataNew(input, userDetails.tenant_id, userId, isLocked, false);
        var budgetAsync = _unitOfWork.DashBoardWidgetsRepo.GetServiceBudgetNew(input, userDetails.tenant_id, userId, isLocked);
        //removed previous data for story #140644
        var reportingLineDatatableCurrentYearAsync = _unitOfWork.DashBoardWidgetsRepo.GetReportingLineData(true, false, userId, new tco_org_version() { pk_org_version = orgVersionContent.orgVersion }, input.budgetYear, userDetails.tenant_id, new AccStatmentWidgetInput() { OrgId = "", Filters = input.filters }, isLocked, -1);
        var results = await Task.WhenAll(budgetAsync, accountAsync, reportingLineDatatableCurrentYearAsync);

        var accountDatatable = accountAsync.Result;
        var budgetDatatable = budgetAsync.Result;

        var accountDatatable2 = (from r in accountDatatable.AsEnumerable()
            select new
            {
                fk_account_code = r["fk_account_code"].ToString(),
                fk_function_code = r["fk_function_code"].ToString(),
                period = Convert.ToInt32(r["period"]),
                budget_year = input.budgetYear,
                acc = Convert.ToDecimal(r["acc"])
            }).ToList();

        var budgetDatatable2 = (from r in budgetDatatable.AsEnumerable()
            select new
            {
                fk_account_code = r["fk_account_code"].ToString(),
                fk_function_code = r["fk_function_code"].ToString(),
                period = Convert.ToInt32(r["period"]),
                budget_year = input.budgetYear,
                bud = Convert.ToDecimal(r["bud"])
            }).ToList();

        var reportingLineDataLstCY = (from r in reportingLineDatatableCurrentYearAsync.Result.AsEnumerable()
            select new
            {
                fk_account_code = r["pk_account_code"] == null ? string.Empty : r["pk_account_code"].ToString(),
                level_1_id = r["level_1_id"] == null ? string.Empty : r["level_1_id"].ToString(),
                level_2_id = r["level_2_id"] == null ? string.Empty : r["level_2_id"].ToString(),
                level_3_id = r["level_3_id"] == null ? string.Empty : r["level_3_id"].ToString(),
                level_4_id = r["level_4_id"] == null ? string.Empty : r["level_4_id"].ToString(),
                level_5_id = r["level_5_id"] == null ? string.Empty : r["level_5_id"].ToString(),
                level_1_description = r["level_1_description"] == null ? string.Empty : r["level_1_description"].ToString(),
                level_2_description = r["level_2_description"] == null ? string.Empty : r["level_2_description"].ToString(),
                level_3_description = r["level_3_description"] == null ? string.Empty : r["level_3_description"].ToString(),
                level_4_description = r["level_4_description"] == null ? string.Empty : r["level_4_description"].ToString(),
                level_5_description = r["level_5_description"] == null ? string.Empty : r["level_5_description"].ToString()
            }).ToList();

        var accountDatatable3 = (from a in accountDatatable2
            join b in reportingLineDataLstCY on a.fk_account_code equals b.fk_account_code
            select new
            {
                fk_account_code = a.fk_account_code,
                fk_function_code = a.fk_function_code,
                period = a.period,
                budget_year = a.budget_year,
                acc = a.acc
            });

        var budgetDatatable3 = (from a in budgetDatatable2
            join b in reportingLineDataLstCY on a.fk_account_code equals b.fk_account_code
            select new
            {
                fk_account_code = a.fk_account_code,
                fk_function_code = a.fk_function_code,
                period = a.period,
                budget_year = a.budget_year,
                bud = a.bud
            });

        var account = (from acc in accountDatatable3
            select new AccServiceResultData()
            {
                functionCode = acc.fk_function_code,
                Period = acc.period,
                AccountingPeriod = acc.acc,
                budgetYear = acc.budget_year
            }).ToList();

        var budget = (from acc in budgetDatatable3
            select new AccServiceResultData()
            {
                functionCode = acc.fk_function_code,
                Period = acc.period,
                BudgetPeriod = acc.bud,
                budgetYear = input.budgetYear
            }).ToList();

        List<AccServiceResultData> combinedDataSet = new List<AccServiceResultData>();
        combinedDataSet.AddRange(account);
        combinedDataSet.AddRange(budget);
        return combinedDataSet;
    }



    public List<JArray> GetAccBudgetPerOrgSeriesData(List<AccStmtData> data, int dividedBy, string orgName)
    {
        data = data.OrderBy(x => x.DevBudgetYtd).ToList();
        data = data.Where(x => x.Id != "-1").ToList();
        if (data.Any() && data.Count == 1)
        {
            data.FirstOrDefault().Name = orgName;
        }
        var seriesData = new JArray();
        var seriesResponsiveData = new JArray();
        List<decimal> dataValues = new List<decimal>();
        List<JArray> seriesInfo = new List<JArray>();
        bool isPositive = false;
        foreach (var item in data)
        {
            dataValues = new List<decimal> { Math.Round(item.DevBudgetYtd / dividedBy) };
            isPositive = Math.Round(item.DevBudgetYtd / dividedBy) > 0;
            var seriesItem = new JObject()
            {
                new JProperty("name", item.Name),
                new JProperty("color", isPositive ? "#367e6f" : "#d85555"),
                new JProperty("className", isPositive ? "positive-color" : "negative-color"),
                new JProperty("data", dataValues),
                new JProperty("showInLegend", false)
            };
            var seriesResponsiveItem = new JObject()
            {
                new JProperty("name", item.Name),
                new JProperty("color", isPositive ? "#367e6f" : "#d85555"),
                new JProperty("className", isPositive ? "positive-color" : "negative-color"),
                new JProperty("data", dataValues),
                new JProperty("showInLegend", true)
            };
            seriesData.Add(seriesItem);
            seriesResponsiveData.Add(seriesResponsiveItem);
        }
        seriesInfo = new List<JArray> { seriesData, seriesResponsiveData };
        return seriesInfo;
    }


    public List<JArray> GetAccBudgetPerServiceSeriesData(List<AccStateServiceHelper> data, int dividedBy)
    {
        data = data.OrderBy(x => x.budDevYtd).ToList();
        data = data.Where(x => x.orgId != "-1").ToList();
        var seriesData = new JArray();
        var seriesResponsiveData = new JArray();
        List<decimal> dataValues = new List<decimal>();
        List<JArray> seriesInfo = new List<JArray>();
        bool isPositive = false;
        foreach (var item in data)
        {
            if (item.budDevYtd != 0)
            {
                dataValues = new List<decimal> { Math.Round(item.budDevYtd / dividedBy) };
                isPositive = Math.Round(item.budDevYtd / dividedBy) > 0;
                var seriesItem = new JObject()
                {
                    new JProperty("name", item.orgName),
                    new JProperty("color", isPositive ? "#367e6f" : "#d85555"),
                    new JProperty("className", isPositive ? "positive-color" : "negative-color"),
                    new JProperty("data", dataValues),
                    new JProperty("showInLegend", false)
                };
                var seriesResponsiveItem = new JObject()
                {
                    new JProperty("name", item.orgName),
                    new JProperty("color", isPositive ? "#367e6f" : "#d85555"),
                    new JProperty("className", isPositive ? "positive-color" : "negative-color"),
                    new JProperty("data", dataValues),
                    new JProperty("showInLegend", true)
                };
                seriesData.Add(seriesItem);
                seriesResponsiveData.Add(seriesResponsiveItem);
            }
        }
        seriesInfo = new List<JArray> { seriesData, seriesResponsiveData };
        return seriesInfo;
    }



    //#endregion org level gragh -Budsjettavvik per eining/ansvar

    //#region Accounting budget per level

    public async Task<AccStmtGraphBudgetAndAccountData> GetAccountingBudgetPerLevelGraphData(string userId, AccStatmentWidgetInput input)
    {
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();

        var currOrgVersion = await tenantDbContext.tco_org_version.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.period_from <= input.Period && x.period_to >= input.Period);
        vw_tco_parameters accLvlAggrParam = await tenantDbContext.vw_tco_parameters.FirstOrDefaultAsync(t => t.fk_tenant_id == userDetails.tenant_id && string.Equals(t.param_name, "ACCSTATE_GRAPH_ACCOUNT_LEVEL"));

        //get budget lock status
        var type = "LOCK_ORIGINAL_BUDGET";
        var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, input.Period);
        input.BudgetYear = input.Period / 100;
        var isLocked = await _utility.CheckOnYearlyBudgetLockByBudgetYearAsync(orgVersionContent, userDetails.tenant_id, userId, input.BudgetYear, input.OrgId, input.OrgLevel.ToString(), type);
        input.RequestedTreeLevel = input.OrgLevel;
        var finalData = await _unitOfWork.DashBoardWidgetsRepo.GetAccountingBudgetPerLevelGraphData(userId, userDetails.tenant_id, input, isLocked, accLvlAggrParam, currOrgVersion);
        return finalData;
    }



    //#endregion Accounting budget per level

    //#region absence per org lower graph - Fravær per enhet/ansvar

    public async Task<List<AbsenceOrgGraphHelper>> GetAbsenceDetailsData(string userName, AbsenceGraphWidgetInput input)
    {
        var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userName, input.Period);
        UserData userDetails = await _utility.GetUserDetailsAsync(userName);
        TenantDBContext monthlyReportDbContext = await _utility.GetTenantDBContextAsync();
        List<string> relevantDepartmentsList = new List<string>();
        List<clsOrgIdAndDepartments> nextLevelOrgIdDepts = new List<clsOrgIdAndDepartments>();
        List<AbsenceOrgGraphHelper> absDataDetail = new List<AbsenceOrgGraphHelper>();
        int period = (input.Period % input.BudgetYear);
        List<int> absDataDetailYears = new List<int> { input.BudgetYear, input.BudgetYear - 1, input.BudgetYear - 2 };
        List<int> absDataDetailPeriods = new List<int> { (input.BudgetYear * 100) + period , ((input.BudgetYear - 1)* 100) + period, ((input.BudgetYear - 2)*100) + period };
        List<string> relevantFunctionsList = new List<string>();
        OrgInpLevels orgInput = new OrgInpLevels(input.level1OrgId, input.level2OrgId, input.level3OrgId, input.level4OrgId, input.level5OrgId, input.level6OrgId, input.level7OrgId, input.level8OrgId);

        relevantDepartmentsList = await _orgUtility.GetDepartmentsForOrgIdHierLvlsAsync(orgVersionContent, userName, input.BudgetYear, orgInput);
        nextLevelOrgIdDepts = await GetOrgIdNextLevelDepts(orgVersionContent, userName, input.Period, orgInput);

        //filter data based on service areas filter with relevant function codes
        if (input.Filters.Any(x => x.FilterId == FilterId.ServiceFilter))
        {
            List<string> filterValues = input.Filters.FirstOrDefault(x => x.FilterId == FilterId.ServiceFilter).FilterValues.ToList();
            if (!filterValues.Contains(string.Empty))
            {
                relevantFunctionsList = await GetFunctionsForOrgIdHierLvls(userName, filterValues);
            }
        }
        var absDataDetailAsync1 = _unitOfWork.DashBoardWidgetsRepo.GetAbsenceAggreggatedData(userDetails.tenant_id, absDataDetailYears[0], relevantDepartmentsList, relevantFunctionsList, absDataDetailPeriods[0], input.OrgLevel, input.OrgId, orgVersionContent.orgVersion);
        var absDataDetailAsync2 = _unitOfWork.DashBoardWidgetsRepo.GetAbsenceAggreggatedData(userDetails.tenant_id, absDataDetailYears[1], relevantDepartmentsList, relevantFunctionsList, absDataDetailPeriods[1], input.OrgLevel, input.OrgId, orgVersionContent.orgVersion);
        var absDataDetailAsync3 = _unitOfWork.DashBoardWidgetsRepo.GetAbsenceAggreggatedData(userDetails.tenant_id, absDataDetailYears[2], relevantDepartmentsList, relevantFunctionsList, absDataDetailPeriods[2], input.OrgLevel, input.OrgId, orgVersionContent.orgVersion);

        await Task.WhenAll(absDataDetailAsync1, absDataDetailAsync2, absDataDetailAsync3);

        var absDataDetail1 = absDataDetailAsync1.Result;
        absDataDetail1.ForEach(x =>
        {
            x.year = absDataDetailYears[0].ToString();
        });

        var absDataDetail2 = absDataDetailAsync2.Result;
        absDataDetail2.ForEach(x =>
        {
            x.year = absDataDetailYears[1].ToString();
        });

        var absDataDetail3 = absDataDetailAsync3.Result;
        absDataDetail3.ForEach(x =>
        {
            x.year = absDataDetailYears[2].ToString();
        });

        absDataDetail.AddRange(absDataDetail1);
        absDataDetail.AddRange(absDataDetail2);
        absDataDetail.AddRange(absDataDetail3);

        if (relevantDepartmentsList.Any())
        {
            absDataDetail = absDataDetail.Where(x => relevantDepartmentsList.Contains(x.departmentCode)).ToList();
        }

        if (relevantFunctionsList.Any())
        {
            absDataDetail = absDataDetail.Where(x => relevantFunctionsList.Contains(x.functionCode)).ToList();
        }

        absDataDetail = (from abs in absDataDetail
            join dis in nextLevelOrgIdDepts on abs.departmentCode equals dis.departmentValue into org
            from org1 in org.DefaultIfEmpty()
            select new AbsenceOrgGraphHelper
            {
                absOrgId = org1 != null ? org1.orgId : "0",
                absOrgName = org1 != null ? org1.orgName : "",
                departmentCode = abs.departmentCode,
                functionCode = abs.functionCode,
                absHrsShort = abs.absHrsShort,
                absHrsLong = abs.absHrsLong,
                workingHrs = abs.workingHrs,
                year = abs.year
            }).ToList();
        var distinctOrgData = nextLevelOrgIdDepts.Select(x => new KeyValueStringPair { key = x.orgId, value = x.orgName }).Distinct().ToList();
        var distinctOrgIds = distinctOrgData.Select(x => x.key).OrderBy(x => x).Distinct().ToList();
        var dummyData = new List<AbsenceOrgGraphHelper>();
        foreach (var orgId in distinctOrgIds)
        {
            var isDataYear1 = absDataDetail.Any(x => x.absOrgId == orgId && x.year == absDataDetailYears[2].ToString());
            var isDataYear2 = absDataDetail.Any(x => x.absOrgId == orgId && x.year == absDataDetailYears[1].ToString());
            var isDataYear3 = absDataDetail.Any(x => x.absOrgId == orgId && x.year == absDataDetailYears[0].ToString());
            if (!isDataYear1 || !isDataYear2 || !isDataYear3)
            {
                if (!isDataYear1)
                {
                    var defaultDataValue = new AbsenceOrgGraphHelper
                    {
                        absOrgId = orgId,
                        absOrgName = distinctOrgData.Any(x => x.key == orgId) ? distinctOrgData.FirstOrDefault(x => x.key == orgId).value : string.Empty,
                        departmentCode = "",
                        functionCode = "",
                        absHrsShort = 0,
                        absHrsLong = 0,
                        workingHrs = 0,
                        year = absDataDetailYears[2].ToString()
                    };
                    dummyData.Add(defaultDataValue);
                }
                if (!isDataYear2)
                {
                    var defaultDataValue = new AbsenceOrgGraphHelper
                    {
                        absOrgId = orgId,
                        absOrgName = distinctOrgData.Any(x => x.key == orgId) ? distinctOrgData.FirstOrDefault(x => x.key == orgId).value : string.Empty,
                        departmentCode = "",
                        functionCode = "",
                        absHrsShort = 0,
                        absHrsLong = 0,
                        workingHrs = 0,
                        year = absDataDetailYears[1].ToString()
                    };
                    dummyData.Add(defaultDataValue);
                }
                if (!isDataYear3)
                {
                    var defaultDataValue = new AbsenceOrgGraphHelper
                    {
                        absOrgId = orgId,
                        absOrgName = distinctOrgData.Any(x => x.key == orgId) ? distinctOrgData.FirstOrDefault(x => x.key == orgId).value : string.Empty,
                        departmentCode = "",
                        functionCode = "",
                        absHrsShort = 0,
                        absHrsLong = 0,
                        workingHrs = 0,
                        year = absDataDetailYears[0].ToString()
                    };
                    dummyData.Add(defaultDataValue);
                }
            }
        }
        absDataDetail.AddRange(dummyData);
        absDataDetail = FilterAbsenceData(absDataDetail, input.Filters);
        return absDataDetail;
    }



    public List<AbsenceOrgGraphHelper> FilterAbsenceData(List<AbsenceOrgGraphHelper> data, IEnumerable<DashboardFilterInput> filters)
    {
        List<string> filterValues = new List<string>();
        if (filters.Any(x => x.FilterId == FilterId.DepartmentFilter))
        {
            filterValues = filters.FirstOrDefault(x => x.FilterId == FilterId.DepartmentFilter).FilterValues.ToList();
            data = data.Where(z => filterValues.Contains(z.departmentCode)).ToList();
        }
        if (filters.Any(x => x.FilterId == FilterId.FunctionFilter))
        {
            filterValues = filters.FirstOrDefault(x => x.FilterId == FilterId.FunctionFilter).FilterValues.ToList();
            data = data.Where(z => filterValues.Contains(z.functionCode)).ToList();
        }
        if (filters.Any(x => x.FilterId == FilterId.OrgFilter))
        {
            filterValues = filters.FirstOrDefault(x => x.FilterId == FilterId.OrgFilter).FilterValues.ToList();
            data = data.Where(z => filterValues.Contains(z.absOrgId)).ToList();
        }
        return data;
    }



    public List<tco_service_values> GetServiceValues(List<string> serviceIds, List<tco_service_values> lstServiceValues, int? serviceIdLevelNo)
    {
        switch (serviceIdLevelNo)
        {
            case 6:
                lstServiceValues = lstServiceValues.Where(x => serviceIds.Contains(x.fk_function_code)).ToList();
                break;

            case 5:
                lstServiceValues = lstServiceValues.Where(x => serviceIds.Contains(x.service_id_5)).ToList();
                break;

            case 4:
                lstServiceValues = lstServiceValues.Where(x => serviceIds.Contains(x.service_id_4)).ToList();
                break;

            case 3:
                lstServiceValues = lstServiceValues.Where(x => serviceIds.Contains(x.service_id_3)).ToList();
                break;

            case 2:
                lstServiceValues = lstServiceValues.Where(x => serviceIds.Contains(x.service_id_2)).ToList();
                break;

            default:
                lstServiceValues = lstServiceValues.Where(x => serviceIds.Contains(x.service_id_1)).ToList();
                break;
        }
        return lstServiceValues;
    }



    public async Task<List<JArray>> GetAbsenceOrgDetailsGraph(string userName, AbsenceGraphWidgetInput input, int dividedBy)
    {
        List<JArray> result = new List<JArray>();
        var absenceData = await GetAbsenceDetailsData(userName, input);
        UserData userDetails = await _utility.GetUserDetailsAsync(userName);
        CultureInfo culture = CultureInfo.CreateSpecificCulture(userDetails.language_preference);
        absenceData = (from admy in absenceData
            orderby admy.year, admy.absOrgId
            group admy by new { admy.year, admy.absOrgId, admy.absOrgName } into admyGrp
            select new AbsenceOrgGraphHelper
            {
                year = admyGrp.Key.year,
                absOrgId = admyGrp.Key.absOrgId,
                absOrgName = admyGrp.Key.absOrgName,
                absHrsShort = admyGrp.Sum(x => x.absHrsShort),
                absHrsLong = admyGrp.Sum(x => x.absHrsLong),
                workingHrs = admyGrp.Sum(x => x.workingHrs),
                overallAbsence = admyGrp.Sum(x => x.absHrsShort) + admyGrp.Sum(x => x.absHrsLong),
                overallAbsencePct = admyGrp.Sum(x => x.workingHrs) != 0 ? Math.Round(((admyGrp.Sum(x => x.absHrsShort) + admyGrp.Sum(x => x.absHrsLong)) / (admyGrp.Sum(x => x.workingHrs)) * 100), 2) : 0
            }).ToList();

        AbsenceOrgGraphHelper absCurrOrgBudYr = (from admy in absenceData
            group admy by new { admy.year } into admyGrp
            select new AbsenceOrgGraphHelper
            {
                year = admyGrp.Key.year,
                absHrsShort = admyGrp.Sum(x => x.absHrsShort),
                absHrsLong = admyGrp.Sum(x => x.absHrsLong),
                workingHrs = admyGrp.Sum(x => x.workingHrs)
            }).ToList().Where(x => x.year == input.BudgetYear.ToString()).FirstOrDefault();
        decimal overallPctCurrOrg = absCurrOrgBudYr != null && absCurrOrgBudYr.workingHrs != 0 ? Math.Round(((absCurrOrgBudYr.absHrsShort + absCurrOrgBudYr.absHrsLong) / absCurrOrgBudYr.workingHrs * 100), 2) : 0;
        absenceData = (from a in absenceData
            select new AbsenceOrgGraphHelper
            {
                year = a.year,
                absOrgId = a.absOrgId,
                absOrgName = a.absOrgName,
                absHrsShort = a.absHrsShort,
                absHrsLong = a.absHrsLong,
                workingHrs = a.workingHrs,
                overallAbsence = a.absHrsShort + a.absHrsLong,
                overallAbsencePct = a.workingHrs != 0 ? Math.Round(((a.absHrsShort + a.absHrsLong) / a.workingHrs * 100), 2) : 0
            }).ToList();
        result = GetAbsencePerOrgSeriesData(absenceData, dividedBy, input.BudgetYear, overallPctCurrOrg.ToString(culture.NumberFormat));
        var norwayTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Central European Standard Time");
        CultureInfo ci = CultureInfoFactory.CreateCulture(userDetails.language_preference);
        var lastUpdated = await _unitOfWork.DashBoardWidgetsRepo.GetAbsenceLastUpdated(userDetails.tenant_id);
        var dateTimeArr = lastUpdated == DateTime.MinValue ? string.Empty : ((TimeZoneInfo.ConvertTimeFromUtc(lastUpdated, norwayTimeZone).ToString("D", ci))+" "+ (TimeZoneInfo.ConvertTimeFromUtc(lastUpdated, norwayTimeZone).ToString("HH:mm", ci)));
        result.Add(JArray.FromObject(new List<string> { dateTimeArr }));
        return result;
    }



    public List<JArray> GetAbsencePerOrgSeriesData(List<AbsenceOrgGraphHelper> data, int dividedBy, int budgetYear, string overallPctCurrOrg)
    {
        var seriesData = new JArray();
        var seriesResponsiveData = new JArray();
        var categories = new JArray();
        List<string> totalSum = new List<string> { overallPctCurrOrg };
        List<decimal> dataValues = new List<decimal>();
        List<JArray> seriesInfo = new List<JArray>();
        categories = JArray.FromObject(data.OrderBy(x => x.absOrgId).Select(x => x.absOrgName).Distinct().ToList());
        Dictionary<int, string> colors = new Dictionary<int, string> { { budgetYear - 2, "#124B96" }, { budgetYear - 1, "#934D47" }, { budgetYear, "#4E723B" } };
        var patternObj = new JObject()
        {
            new JProperty("path", new JObject(){
                new JProperty("d", "M 0 0 L 10 10 M 9 -1 L 11 1 M -1 9 L 1 11"),
                new JProperty("strokeWidth", 1)
            }),
            new JProperty("width", 10),
            new JProperty("height",10),
            //new JProperty("opacity", 0.4),
            new JProperty("color", "#E0E0E0"),
            new JProperty("backgroundColor", colors.FirstOrDefault(x => x.Key == budgetYear - 1).Value)
        };
        for (var i = budgetYear - 2; i < budgetYear + 1; i++)
        {
            dataValues = data.Where(x => x.year == i.ToString()).OrderBy(x => x.absOrgId).Select(x => x.overallAbsencePct).ToList();
            var nonZeroDataValues = dataValues.Where(x => Math.Round(x) != 0).ToList();
            if (nonZeroDataValues.Count == 0)
            {
                dataValues = new();
            }
            var seriesItem = new JObject()
            {
                new JProperty("name", i.ToString()),
                i == budgetYear - 1 ? new JProperty("color", new JObject() { new JProperty("pattern", patternObj) }) : new JProperty("color", colors.FirstOrDefault(x => x.Key == i).Value),
                new JProperty("data", dataValues),
                new JProperty("stack", i)
            };
            var seriesResponsiveItem = new JObject()
            {
                new JProperty("name", i.ToString()),
                i == budgetYear - 1 ? new JProperty("color", new JObject() { new JProperty("pattern", patternObj) }) : new JProperty("color", colors.FirstOrDefault(x => x.Key == i).Value),
                new JProperty("data", dataValues),
                new JProperty("stack", i)
            };
            if (i == budgetYear)
            {
                // seriesItem.Add(new JProperty("borderColor", "#000000"));
                //  seriesResponsiveItem.Add(new JProperty("borderColor", "#000000"));
            }
            seriesData.Add(seriesItem);
            seriesResponsiveData.Add(seriesResponsiveItem);
        }
        seriesInfo = new List<JArray> { seriesData, seriesResponsiveData, categories, JArray.FromObject(totalSum) };
        return seriesInfo;
    }


    public async Task<List<AccStateServiceHelper>> GetAccBudServiceGraphFtd(string userId, AccBudServiceInput inObj, List<AccServiceResultData> baseData)
    {
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, inObj.period);
        string divideByWhat = _utility.CheckOnDivideByWhat(userId, "ACCSTATE_GRAPHS_DIVIDEBY");

        int divideByNumber;

        switch (divideByWhat.ToLower())
        {
            case "million":
                divideByNumber = 1000000;
                break;

            case "thousand":
                divideByNumber = 1000;
                break;

            default:
                divideByNumber = 1;
                break;
        }
        List<clsOrgIdAndDepartments> serviceIdsFunctions = await GetServiceIdsFunctionsAsync(orgVersionContent, userId, inObj.orgId, inObj.orgLevel);
        List<AccStOrgGrpData> accServiceData = GroupServiceData(baseData, inObj, serviceIdsFunctions);

        List<AccStateServiceHelper> acPlData = (from aod in accServiceData
            select new AccStateServiceHelper
            {
                orgId = aod.orgId,
                orgName = aod.orgName,
                accYtd = aod.accountingPeriod,
                revBudYtd = aod.budgetPeriod
            }).ToList();

        foreach (var item in acPlData)
        {
            item.accYtd = Math.Round(item.accYtd / divideByNumber);
            item.revBudYtd = Math.Round(item.revBudYtd / divideByNumber);
            item.budDevYtd = item.revBudYtd - item.accYtd;
            item.deviation = item.revBudYtd - item.accYtd;
            item.actionPlanDeviation = string.Empty;
            item.finplanDeviation = string.Empty;
            item.forecastComment = string.Empty;
            item.forecastReportComment = string.Empty;
        }

        if (acPlData.Count == 1 && acPlData.ElementAt(0).orgName == string.Empty)
        {
            acPlData.ElementAt(0).orgName = inObj.orgName;
        }

        acPlData = acPlData.OrderBy(x => x.budDevYtd).ToList();
        acPlData.Add(new AccStateServiceHelper()
        {
            orgId = "-1",
            orgName = "",
            accYtd = 0,
            accLastYrYtd = 0,
            revBudYtd = 0,
            budDevYtd = acPlData.Sum(x => x.budDevYtd),
            deviation = 0,
            actionPlanDeviation = string.Empty,
            finplanDeviation = string.Empty,
            forecastReportComment = string.Empty
        });
        return acPlData;
    }


    private static List<AccStOrgGrpData> GroupServiceData(List<AccServiceResultData> acStRowData, AccBudServiceInput inObj, List<clsOrgIdAndDepartments> serviceIdsFunctions)
    {
        return (from acData in acStRowData
            join rl in serviceIdsFunctions on acData.functionCode equals rl.functionValue
            group acData by new { rl.serviceId, rl.serviceName } into grp
            select new AccStOrgGrpData
            {
                orgId = grp.Key.serviceId,
                orgName = grp.Key.serviceName,
                budgetPeriod = grp.Sum(x => x.BudgetPeriod) ?? 0,
                accountingPeriod = grp.Sum(x => x.AccountingPeriod) ?? 0,
            }).ToList();
    }




    //#region absence per period graph
    public async Task<List<AbsPerPeriodDataHelper>> GetMrAbsenceDetailGraphBaseData(string userName, AbsPerPeriodGraphInput input)
    {
        var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userName, input.forecastPeriod);
        UserData userDetails = await _utility.GetUserDetailsAsync(userName);
        TenantDBContext monthlyReportDbContext = await _utility.GetTenantDBContextAsync();
        List<string> relevantDepartmentsList = new();
        List<clsOrgIdAndDepartments> nextLevelOrgIdDepts = new();
        List<string> relevantFunctionsList = new();
        List<AbsPerPeriodDataHelper> absDataDetail = new();
        List<int> absDataDetailYears = new List<int> { input.budgetYear, input.budgetYear - 1, input.budgetYear - 2 };

        if (input.isChapterSetup)
        {
            if (!string.IsNullOrEmpty(input.attributeId) && input.attributeId != "-1")
            {
                relevantDepartmentsList = await _finUtility.GetAttributeConnectedDepartmentsForSelectedOrgAsync(userDetails.user_name, input.budgetYear, input.orgId, input.orgLevel, input.attributeId);
            }
        }
        else
        {
            relevantDepartmentsList = await _orgUtility.GetDepartmentsForOrgIdHierLvlsAsync(orgVersionContent, userName, input.budgetYear, input.orgInput);
        }

        nextLevelOrgIdDepts = await GetOrgIdNextLevelDepts(orgVersionContent, userName, input.forecastPeriod, input.orgInput);

        //filter data based on service areas filter with relevant function codes
        List<string> filterValues = new List<string>();
        if (input.filters.Any(x => x.FilterId == FilterId.ServiceFilter))
        {
            filterValues = input.filters.FirstOrDefault(x => x.FilterId == FilterId.ServiceFilter).FilterValues.ToList();
            if (!filterValues.Contains(string.Empty))
            {
                relevantFunctionsList = await GetFunctionsForOrgIdHierLvls(userName, filterValues);
            }
        }

        absDataDetail = await _unitOfWork.DashBoardWidgetsRepo.GetAbsPerPeriodData(userDetails.tenant_id, absDataDetailYears, relevantDepartmentsList, relevantFunctionsList);
        absDataDetail.ForEach(x =>
        {
            var dis = nextLevelOrgIdDepts.FirstOrDefault(y => y.departmentValue == x.departmentCode);
            x.absOrgId = dis != null ? dis.orgId : "0";
            x.absOrgName = dis != null ? dis.orgName : "";
        });

        //format data based on filters applied
        if (input.filters.Any(x => x.FilterId == FilterId.DepartmentFilter))
        {
            filterValues = input.filters.FirstOrDefault(x => x.FilterId == FilterId.DepartmentFilter).FilterValues.ToList();
            absDataDetail = absDataDetail.Where(z => filterValues.Contains(z.departmentCode)).ToList();
        }
        if (input.filters.Any(x => x.FilterId == FilterId.FunctionFilter))
        {
            filterValues = input.filters.FirstOrDefault(x => x.FilterId == FilterId.FunctionFilter).FilterValues.ToList();
            absDataDetail = absDataDetail.Where(z => filterValues.Contains(z.functionCode)).ToList();
        }
        if (input.filters.Any(x => x.FilterId == FilterId.OrgFilter))
        {
            filterValues = input.filters.FirstOrDefault(x => x.FilterId == FilterId.OrgFilter).FilterValues.ToList();
            absDataDetail = absDataDetail.Where(z => filterValues.Contains(z.absOrgId)).ToList();
        }

        //return formatted graph data;
        var finalData = GetAbsPerPeriodFormattedData(absDataDetail ?? new());
        return finalData;
    }



    public async Task<string> GetAbsenceLastUpdated(string userName)
    {
        UserData userDetails = await _utility.GetUserDetailsAsync(userName);
        var norwayTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Central European Standard Time");
        CultureInfo ci = CultureInfoFactory.CreateCulture(userDetails.language_preference);
        var updated =  await _unitOfWork.DashBoardWidgetsRepo.GetAbsenceLastUpdated(userDetails.tenant_id);
        var dateTimeArr = updated == DateTime.MinValue ? string.Empty : ((TimeZoneInfo.ConvertTimeFromUtc(updated, norwayTimeZone).ToString("D", ci)) + " " + (TimeZoneInfo.ConvertTimeFromUtc(updated, norwayTimeZone).ToString("HH:mm", ci)));
        return dateTimeArr;
    }


    private List<AbsPerPeriodDataHelper> GetAbsPerPeriodFormattedData(List<AbsPerPeriodDataHelper> data)
    {
        data = (from admy in data
            group admy by new { admy.year, admy.period } into admyGrp
            select new AbsPerPeriodDataHelper
            {
                year = admyGrp.Key.year,
                period = admyGrp.Key.period,
                absHrsShort = admyGrp.Sum(x => x.absHrsShort),
                absHrsLong = admyGrp.Sum(x => x.absHrsLong),
                workingHrs = admyGrp.Sum(x => x.workingHrs)
            }).ToList();

        return (from admy in data
            select new AbsPerPeriodDataHelper
            {
                year = admy.year,
                period = admy.period,
                absHrsShort = admy.absHrsShort,
                absHrsLong = admy.absHrsLong,
                workingHrs = admy.workingHrs,
                overallAbsence = admy.absHrsShort + admy.absHrsLong,
                overallAbsencePct = admy.workingHrs != 0 ? Math.Round(((admy.absHrsShort + admy.absHrsLong) / admy.workingHrs * 100), 2) : 0,
                shortTermAbsencePct = admy.workingHrs != 0 ? Math.Round((admy.absHrsShort / admy.workingHrs * 100), 2) : 0,
                longTermAbsencePct = admy.workingHrs != 0 ? Math.Round((admy.absHrsLong / admy.workingHrs * 100), 2) : 0
            }).ToList();
    }



    //#endregion  absence per period graph

    //#region absence per age group graph

    public async Task<List<JArray>> GetMrAbsenceDetailPerAgeGroupGraphBaseData(string userName, AbsPerPeriodGraphInput input)
    {
        // Fetch necessary data concurrently
        var orgVersionTask = _utility.GetOrgVersionSpecificContentAsync(userName, input.forecastPeriod);
        var userDetailsTask = _utility.GetUserDetailsAsync(userName);

        await Task.WhenAll(orgVersionTask, userDetailsTask);
        var orgVersionContent = await orgVersionTask;
        var userDetails = await userDetailsTask;


        List<string> relevantDepartmentsList = new();
        List<clsOrgIdAndDepartments> nextLevelOrgIdDepts = new();
        List<string> relevantFunctionsList = new();
        List<RptGraphAbsHelper> absDataDetail = new();
        List<int> absDataDetailYears = new List<int> { input.budgetYear, input.budgetYear - 1, input.budgetYear - 2 };

        if (input.isChapterSetup)
        {
            if (!string.IsNullOrEmpty(input.attributeId) && input.attributeId != "-1")
            {
                relevantDepartmentsList = await _finUtility.GetAttributeConnectedDepartmentsForSelectedOrgAsync(userDetails.user_name, input.budgetYear, input.orgId, input.orgLevel, input.attributeId);
            }
        }
        else
        {
            relevantDepartmentsList = await _orgUtility.GetDepartmentsForOrgIdHierLvlsAsync(orgVersionContent, userName, input.budgetYear, input.orgInput);
        }

        nextLevelOrgIdDepts = await GetOrgIdNextLevelDepts(orgVersionContent, userName, input.forecastPeriod, input.orgInput);

        //filter data based on service areas filter with relevant function codes
        List<string> filterValues = new List<string>();
        if (input.filters.Any(x => x.FilterId == FilterId.ServiceFilter))
        {
            filterValues = input.filters.FirstOrDefault(x => x.FilterId == FilterId.ServiceFilter).FilterValues.ToList();
            if (!filterValues.Contains(string.Empty))
            {
                relevantFunctionsList = await GetFunctionsForOrgIdHierLvls(userName, filterValues);
            }
        }

        absDataDetail = await _unitOfWork.DashBoardWidgetsRepo.GetAbsPerAgeGroupData(userDetails.tenant_id, absDataDetailYears, relevantDepartmentsList, relevantFunctionsList);

        // Create a dictionary for fast department lookup
        var departmentDictionary = nextLevelOrgIdDepts.ToDictionary(y => y.departmentValue);

        foreach (var x in absDataDetail)
        {
            if (departmentDictionary.TryGetValue(x.departmentCode, out var dept))
            {
                x.absOrgId = dept.orgId;
                x.absOrgName = dept.orgName;
            }
            else
            {
                x.absOrgId = "0";
                x.absOrgName = "";
            }
        }

        //format data based on filters applied
        if (input.filters.Any(x => x.FilterId == FilterId.DepartmentFilter))
        {
            filterValues = input.filters.FirstOrDefault(x => x.FilterId == FilterId.DepartmentFilter).FilterValues.ToList();
            absDataDetail = absDataDetail.Where(z => filterValues.Contains(z.departmentCode)).ToList();
        }
        if (input.filters.Any(x => x.FilterId == FilterId.FunctionFilter))
        {
            filterValues = input.filters.FirstOrDefault(x => x.FilterId == FilterId.FunctionFilter).FilterValues.ToList();
            absDataDetail = absDataDetail.Where(z => filterValues.Contains(z.functionCode)).ToList();
        }
        if (input.filters.Any(x => x.FilterId == FilterId.OrgFilter))
        {
            filterValues = input.filters.FirstOrDefault(x => x.FilterId == FilterId.OrgFilter).FilterValues.ToList();
            absDataDetail = absDataDetail.Where(z => filterValues.Contains(z.absOrgId)).ToList();
        }

        //formatting

        List<tmr_absence_age_groups> ageGroupsList = await _unitOfWork.DashBoardWidgetsRepo.FetchAgeGroupIds(userDetails.tenant_id);
        var absAgeGroups = ageGroupsList.Select(x => x.pk_id.ToString()).ToList();
        var strAbsGrpList = JsonConvert.SerializeObject(absAgeGroups);

        var reportPeriod = input.forecastPeriod % 100;

        var absAgeGrp2 = (from admy in absDataDetail
            where admy.period % 100 <= reportPeriod
            group admy by new { admy.year, admy.ageGroup } into admyGrp
            select new RptGraphAbsHelper
            {
                year = admyGrp.Key.year,
                ageGroup = admyGrp.Key.ageGroup,
                absHrsShort = admyGrp.Sum(x => x.absHrsShort),
                absHrsLong = admyGrp.Sum(x => x.absHrsLong),
                workingHrs = admyGrp.Sum(x => x.workingHrs)
            }).ToList();

        // List of years - Budget year and two past years and for each budget year the age groups

        List<RptGraphAbsHelper> absAgeGrpMould = (from ag in absAgeGroups
            from yr in absDataDetailYears
            select new RptGraphAbsHelper
            {
                ageGroup = ag,
                year = yr
            }).ToList();

        absAgeGrpMould.ForEach(x =>
        {
            var adm = absAgeGrp2.Where(y => y.ageGroup == x.ageGroup && y.year == x.year).FirstOrDefault();

            x.overallAbsence = adm != null ? adm.absHrsShort + adm.absHrsLong : 0;
            x.overallAbsencePct = adm != null && adm.workingHrs != 0 ? Math.Round(((adm.absHrsShort + adm.absHrsLong) / adm.workingHrs * 100), 2) : 0;
        });

        List<string> ageGrpDisp = ageGroupsList.Select(x => x.description).ToList();

        return FormatAbsencePerAgeGroupSeriesData(absAgeGrpMould, input.budgetYear, ageGrpDisp);

    }



    private List<JArray> FormatAbsencePerAgeGroupSeriesData(List<RptGraphAbsHelper> data, int budgetYear, List<string> ageCategories)
    {
        var seriesData = new JArray();
        var seriesResponsiveData = new JArray();
        List<decimal> dataValues = new List<decimal>();
        List<JArray> seriesInfo = new List<JArray>();
        JArray categories = JArray.FromObject(ageCategories);
        Dictionary<int, string> colors = new Dictionary<int, string> { { budgetYear - 2, "#124B96" }, { budgetYear - 1, "#934D47" }, { budgetYear, "#4E723B" } };
        var patternObj = new JObject()
        {
            new JProperty("path", new JObject(){
                new JProperty("d", "M 0 0 L 10 10 M 9 -1 L 11 1 M -1 9 L 1 11"),
                new JProperty("strokeWidth", 1)
            }),
            new JProperty("width", 10),
            new JProperty("height",10),
            //new JProperty("opacity", 0.4),
            new JProperty("color", "#E0E0E0"),
            new JProperty("backgroundColor", colors.FirstOrDefault(x => x.Key == budgetYear - 1).Value)
        };
        for (var i = budgetYear - 2; i < budgetYear + 1; i++)
        {
            dataValues = data.Where(x => x.year == i).Select(x => x.overallAbsencePct).ToList();
            var nonZeroDataValues = dataValues.Where(x => Math.Round(x,2) != 0).ToList();
            if (nonZeroDataValues.Count == 0)
            {
                dataValues = new();
            }
            var seriesItem = new JObject()
            {
                new JProperty("name", i.ToString()),
                i == budgetYear - 1 ? new JProperty("color", new JObject() { new JProperty("pattern", patternObj) }) : new JProperty("color", colors.FirstOrDefault(x => x.Key == i).Value),
                new JProperty("data", dataValues),
                new JProperty("stack", i)
            };
            var seriesResponsiveItem = new JObject()
            {
                new JProperty("name", i.ToString()),
                i == budgetYear - 1 ? new JProperty("color", new JObject() { new JProperty("pattern", patternObj) }) : new JProperty("color", colors.FirstOrDefault(x => x.Key == i).Value),
                new JProperty("data", dataValues),
                new JProperty("stack", i)
            };
            seriesData.Add(seriesItem);
            seriesResponsiveData.Add(seriesResponsiveItem);
        }
        seriesInfo = new List<JArray> { seriesData, seriesResponsiveData, categories};
        return seriesInfo;
    }



    public async Task<string> GetAbsenceLastUpdatedPerAgeGroup(UserData userDetails)
    {
        var norwayTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Central European Standard Time");
        CultureInfo ci = CultureInfoFactory.CreateCulture(userDetails.language_preference);
        var updated = await _unitOfWork.DashBoardWidgetsRepo.GetAbsencePerAgeGroupLastUpdated(userDetails.tenant_id);
        var dateTimeArr = updated == DateTime.MinValue ? string.Empty : ((TimeZoneInfo.ConvertTimeFromUtc(updated, norwayTimeZone).ToString("D", ci)) + " " + (TimeZoneInfo.ConvertTimeFromUtc(updated, norwayTimeZone).ToString("HH:mm", ci)));
        return dateTimeArr;
    }



    //#endregion absence per age group graph

    //#region absence total by age pie chart

    public async Task<List<JArray>> GetMrAbsenceDetailPerTotalByAgepGraphBaseData(string userName, AbsPerPeriodGraphInput input)
    {
        // Fetch necessary data concurrently
        var orgVersionTask = _utility.GetOrgVersionSpecificContentAsync(userName, input.forecastPeriod);
        var userDetailsTask = _utility.GetUserDetailsAsync(userName);

        await Task.WhenAll(orgVersionTask, userDetailsTask);
        var orgVersionContent = await orgVersionTask;
        var userDetails = await userDetailsTask;


        List<string> relevantDepartmentsList = new();
        List<clsOrgIdAndDepartments> nextLevelOrgIdDepts = new();
        List<string> relevantFunctionsList = new();
        List<RptGraphAbsHelper> absDataDetail = new();
        List<int> absDataDetailYears = new List<int> { input.budgetYear, input.budgetYear - 1, input.budgetYear - 2 };

        if (input.isChapterSetup)
        {
            if (!string.IsNullOrEmpty(input.attributeId) && input.attributeId != "-1")
            {
                relevantDepartmentsList = await _finUtility.GetAttributeConnectedDepartmentsForSelectedOrgAsync(userDetails.user_name, input.budgetYear, input.orgId, input.orgLevel, input.attributeId);
            }
        }
        else
        {
            relevantDepartmentsList = await _orgUtility.GetDepartmentsForOrgIdHierLvlsAsync(orgVersionContent, userName, input.budgetYear, input.orgInput);
        }

        nextLevelOrgIdDepts = await GetOrgIdNextLevelDepts(orgVersionContent, userName, input.forecastPeriod, input.orgInput);

        //filter data based on service areas filter with relevant function codes
        List<string> filterValues = new List<string>();
        if (input.filters.Any(x => x.FilterId == FilterId.ServiceFilter))
        {
            filterValues = input.filters.FirstOrDefault(x => x.FilterId == FilterId.ServiceFilter).FilterValues.ToList();
            if (!filterValues.Contains(string.Empty))
            {
                relevantFunctionsList = await GetFunctionsForOrgIdHierLvls(userName, filterValues);
            }
        }

        absDataDetail = await _unitOfWork.DashBoardWidgetsRepo.GetAbsPerAgeGroupData(userDetails.tenant_id, absDataDetailYears, relevantDepartmentsList, relevantFunctionsList);

        // Create a dictionary for fast department lookup
        var departmentDictionary = nextLevelOrgIdDepts.ToDictionary(y => y.departmentValue);

        foreach (var x in absDataDetail)
        {
            if (departmentDictionary.TryGetValue(x.departmentCode, out var dept))
            {
                x.absOrgId = dept.orgId;
                x.absOrgName = dept.orgName;
            }
            else
            {
                x.absOrgId = "0";
                x.absOrgName = "";
            }
        }

        //format data based on filters applied
        if (input.filters.Any(x => x.FilterId == FilterId.DepartmentFilter))
        {
            filterValues = input.filters.FirstOrDefault(x => x.FilterId == FilterId.DepartmentFilter).FilterValues.ToList();
            absDataDetail = absDataDetail.Where(z => filterValues.Contains(z.departmentCode)).ToList();
        }
        if (input.filters.Any(x => x.FilterId == FilterId.FunctionFilter))
        {
            filterValues = input.filters.FirstOrDefault(x => x.FilterId == FilterId.FunctionFilter).FilterValues.ToList();
            absDataDetail = absDataDetail.Where(z => filterValues.Contains(z.functionCode)).ToList();
        }
        if (input.filters.Any(x => x.FilterId == FilterId.OrgFilter))
        {
            filterValues = input.filters.FirstOrDefault(x => x.FilterId == FilterId.OrgFilter).FilterValues.ToList();
            absDataDetail = absDataDetail.Where(z => filterValues.Contains(z.absOrgId)).ToList();
        }

        //formatting

        List<tmr_absence_age_groups> ageGroupsList = await _unitOfWork.DashBoardWidgetsRepo.FetchAgeGroupIds(userDetails.tenant_id);
        var absAgeGroups = ageGroupsList.Select(x => x.pk_id.ToString()).ToList();
        var strAbsGrpList = JsonConvert.SerializeObject(absAgeGroups);

        var reportPeriod = input.forecastPeriod % 100;

        var absAgeGrp2 = absDataDetail
            .Where(admy => admy.period % 100 <= reportPeriod)
            .GroupBy(admy => admy.ageGroup)
            .Select(admyGrp => new RptGraphAbsHelper
            {
                ageGroup = admyGrp.Key,
                overallAbsence = admyGrp.Sum(x => x.absHrsShort + x.absHrsLong)
            })
            .OrderBy(x => int.Parse(x.ageGroup)).ToList();

        decimal totalOverallAbs = absAgeGrp2.Sum(x => x.overallAbsence);

        absAgeGrp2.ForEach(x =>
        {
            x.overallAbsencePct = totalOverallAbs != 0 ? Math.Round((x.overallAbsence / totalOverallAbs) * 100, 2) : 0;
        });

        Dictionary<string, string> ageGrpDisp = ageGroupsList.Select(x => new KeyValuePair<string, string>(x.pk_id.ToString(), x.description)).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

        //Formatting
        return FormatAbsencePerTotalAgePieChartData(absAgeGrp2, ageGrpDisp);

    }



    private List<JArray> FormatAbsencePerTotalAgePieChartData(List<RptGraphAbsHelper> data, Dictionary<string, string> ageCategories)
    {
        var seriesData = new JArray();
        var seriesResponsiveData = new JArray();
        List<decimal> dataValues = new List<decimal>();
        List<JArray> seriesInfo = new List<JArray>();
        List<dynamic> colourOptions = new List<dynamic>
        {
            new JObject
            {
                ["pattern"] = new JObject
                {
                    ["path"] = new JObject
                    {
                        ["d"] = "M 24 0 L 0 24",
                        ["strokeWidth"] = 1
                    },
                    ["width"] = 24,
                    ["height"] = 24,
                    ["color"] = "#000000",
                    ["backgroundColor"] = "#FBEBE1"
                }
            },
            new JObject
            {
                ["pattern"] = new JObject
                {
                    ["path"] = new JObject
                    {
                        ["d"] = "M 0 0 L 17 17",
                        ["strokeWidth"] = 1
                    },
                    ["width"] = 17,
                    ["height"] = 17,
                    ["color"] = "#000000",
                    ["backgroundColor"] = "#FDFCE3"
                }
            },
            new JObject
            {
                ["pattern"] = new JObject
                {
                    ["path"] = new JObject
                    {
                        ["d"] = "M 14 0 L 0 14",
                        ["strokeWidth"] = 1
                    },
                    ["width"] = 14,
                    ["height"] = 14,
                    ["color"] = "#000000",
                    ["backgroundColor"] = "#E4F5D7"
                }
            },
            new JObject
            {
                ["pattern"] = new JObject
                {
                    ["path"] = new JObject
                    {
                        ["d"] = "M 0 0 L 11 11",
                        ["strokeWidth"] = 1
                    },
                    ["width"] = 11,
                    ["height"] = 11,
                    ["color"] = "#000000",
                    ["backgroundColor"] = "#C7EFD1"
                }
            },
            new JObject
            {
                ["pattern"] = new JObject
                {
                    ["path"] = new JObject
                    {
                        ["d"] = "M 8 0 L 0 8",
                        ["strokeWidth"] = 1
                    },
                    ["width"] = 8,
                    ["height"] = 8,
                    ["color"] = "#000000",
                    ["backgroundColor"] = "#9EE0CC"
                }
            },
            new JObject
            {
                ["pattern"] = new JObject
                {
                    ["path"] = new JObject
                    {
                        ["d"] = "M 0 0 L 5 5",
                        ["strokeWidth"] = 1
                    },
                    ["width"] = 5,
                    ["height"] = 5,
                    ["color"] = "#000000",
                    ["backgroundColor"] = "#98D6DB"
                }
            },
            new JObject
            {
                ["pattern"] = new JObject
                {
                    ["path"] = new JObject
                    {
                        ["d"] = "M 0 0 L 10 10 M 9 -1 L 11 1 M -1 9 L 1 11",
                        ["strokeWidth"] = 1
                    },
                    ["width"] = 10,
                    ["height"] = 10,
                    ["color"] = "#000000",
                    ["backgroundColor"] = "#94BDC1"
                }
            }
        };
        try
        {
            foreach (var (item, index) in data.Select((value, i) => (value, i)))
            {
                var seriesItem = new JObject()
                {
                    new JProperty("name", ageCategories[item.ageGroup]),
                    new JProperty("color", colourOptions[index]),
                    new JProperty("y", item.overallAbsencePct)
                };
                seriesData.Add(seriesItem);
                seriesResponsiveData.Add(seriesItem);
            }
            seriesInfo = new List<JArray> { seriesData, seriesResponsiveData };
        }
        catch(Exception ex)
        {
            Console.WriteLine(ex);
        }
            
        return seriesInfo;
    }

}