using System.Collections.Generic;
#pragma warning disable CS8618
namespace Framsikt.BL.Helpers
{
    public class ClsServiceUnitBudgetGridParameters
    {
        public string monthYear { get; set; }
        public string orgId { get; set; }
        public int orgLevel { get; set; }
        public string serviceId { get; set; }
        public string chapterId { get; set; }
        public string level1OrgId { get; set; }
        public string level2OrgId { get; set; }
        public string level3OrgId { get; set; }
        public string level4OrgId { get; set; }
        public string level5OrgId { get; set; }
        public string viewType { get; set; }
        public string detailViewType { get; set; }
        public int groupId { get; set; }
        public int actionId { get; set; }
        public string notAllocated { get; set; }
        public int BudgetYear { get; set; }
        public bool isEditChange { get; set; }
        public bool isAngular { get; set; }
        public bool isWholeValueChecked { get; set; }
        public List<KeyValuePair> selectedColumns { get; set; }
    }
    
    public class ClsGetServiceUnitUnlockedBudgetInputHelper
    {
        public int BudgetYear { get; set; }
        public int BudgetChangeId { get; set; }
        public string level1OrgId { get; set; }
        public string level2OrgId { get; set; }
        public string level3OrgId { get; set; }
        public string level4OrgId { get; set; }
        public string level5OrgId { get; set; }
    }

    public class ClsGetServiceUnitUnlockedBudgetOutputHelper
    {
        public int ActionId { get; set; }
        public string ActionType { get; set; }
        public string ActionName { get; set; }
        public decimal Year1Amount { get; set; }
        public decimal Year2Amount { get; set; }
        public decimal Year3Amount { get; set; }
        public decimal Year4Amount { get; set; }
        public decimal Year5Amount { get; set; }
    }

    public class ClsGetServiceUnitUnlockedBudgetPopupInputHelper
    {
        public int BudgetYear { get; set; }
        public int BudgetChangeId { get; set; }
        public string Level1OrgId { get; set; }
        public string Level2OrgId { get; set; }
        public string Level3OrgId { get; set; }
        public string Level4OrgId { get; set; }
        public string Level5OrgId { get; set; }
        public int ActionId { get; set; }
        public string OrgId { get; set; }
    }
}
