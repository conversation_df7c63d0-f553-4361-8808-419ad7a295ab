using Framsikt.Entities;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
#pragma warning disable CS8618

#pragma warning disable CS8618
namespace Framsikt.BL.Helpers
{
    public class OwnerDetails
    {
        [JsonProperty("owners")]
        public IEnumerable<tco_org_access_setup> Owners { get; set; }

        [JsonProperty("executives")]
        public IEnumerable<AlertOrgExecutive> Executives { get; set; }
        public OwnerDetails()
        {
            Owners = new List<tco_org_access_setup>();
            Executives = new List<AlertOrgExecutive>();
        }
    }
    public class BMCExportHelper
    {
        public List<PublishTreeNode> exportParameters { get; set; }
        public bool includeInternalDesc { get; set; }
        public string budgetPhaseId { get; set; }
        public bool showOnlyModified { get; set; }
        public bool displayOnlyTables { get; set; }
        public int BudgetYear { get; set; }
        public string orgId { get; set; } = string.Empty;
        public string orgName { get; set; } = string.Empty;
        public bool includeOnlyServiceArea { get; set; } = false;
        public bool includeBlistInternalDesc { get; set; } = false;
    }

    public class OrgAccessDetails
    {
        [JsonProperty("ownerDetails")]
        public IEnumerable<tco_org_access_setup> OwnerDetails { get; set; }

        [JsonProperty("orgTree")]
        public List<ClsOrgStructureContent> OrgTree { get; set; }
        public OrgAccessDetails()
        {
            OwnerDetails = new List<tco_org_access_setup>();
            OrgTree = new List<ClsOrgStructureContent>();
        }
    }

    public class UpdateOwnerDetails
    {
        [JsonProperty("username")]
        public string Username { get; set; }

        [JsonProperty("pk_id")]
        public int Pk_id { get; set; }

        [JsonProperty("isOwner")]
        public bool IsOwner { get; set; }

        [JsonProperty("roles")]
        public List<int> Roles { get; set; }
        public UpdateOwnerDetails()
        {
            Roles = new List<int>();
        }
    }
    public class GetOwnerList
    {
        public List<UpdateOwnerDetails> OwnerDetails { get; set; }

        public List<tco_auth_user_role_mapping> UserRoles { get; set; }
        public GetOwnerList()
        {
            OwnerDetails = new List<UpdateOwnerDetails>();
            UserRoles = new List<tco_auth_user_role_mapping>();
        }
    }

    public class SaveOrgAccessGridPopUp
    {
        [JsonProperty("orgId")]
        public string OrgId { get; set; }

        [JsonProperty("orgLevel")]
        public int OrgLevel { get; set; }

        [JsonProperty("orgVersion")]
        public string OrgVersion { get; set; }

        [JsonProperty("updatedData")]
        public List<UpdateOwnerDetails> UpdatedData { get; set; }
        public SaveOrgAccessGridPopUp()
        {
            UpdatedData = new List<UpdateOwnerDetails>();
        }
    }

    public class OrgAccessTreeData
    {
        [JsonProperty("parentId")]
        public int? ParentId { get; set; }

        [JsonProperty("id")]
        public int Id { get; set; }

        [JsonProperty("orgId")]
        public string OrgId { get; set; }

        [JsonProperty("orgLevel")]
        public int OrgLevel { get; set; }

        [JsonProperty("orgName")]
        public string OrgName { get; set; }

        [JsonProperty("owners")]
        public string Owners { get; set; }

        [JsonProperty("expanded")]
        public bool Expanded { get; set; }
    }
}
