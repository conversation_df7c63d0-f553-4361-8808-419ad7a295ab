using Framsikt.Entities;

#pragma warning disable CS8618

namespace Framsikt.BL.Helpers
{
    public class actionGridInputHelper
    {
        public string orgId { get; set; }
        public int? orgLevel { get; set; }
        public string serviceId { get; set; }
        public int budgetYear { get; set; }
        public int skip { get; set; }
        public int pageSize { get; set; }
        public string chapterId { get; set; } = string.Empty;

        [System.ComponentModel.DefaultValue(false)]
        public bool isPopUp { get; set; }

        [System.ComponentModel.DefaultValue(null)]
        public string actionTypeFilter { get; set; }

        public ActionType actionType { get; set; }

        public BudgetProposalActionFilterInput actionFilter { get; set; }
        public BudgetProposalActionSearchHelper searchActionInput { get; set; }
        public List<YearlyBudgetColumnSelector> ColToDisplay { get; set; }

        public IEnumerable<DashboardFilterInput> filters { get; set; }

        public BPActionGridFilterHelper approvalActionFilter { get; set; }
        public bool isAngular { get; set; } = true;
        public bool isApprovalprocess { get; set; }
        public Guid processId { get; set; }
        public actionGridInputHelper()
        {
            ColToDisplay = new List<YearlyBudgetColumnSelector>();
            actionFilter = new BudgetProposalActionFilterInput();
            searchActionInput = new BudgetProposalActionSearchHelper();
            filters = new List<DashboardFilterInput>();
            approvalActionFilter = new BPActionGridFilterHelper();
        }
        public string Domain { get; set; }

    }

    public class FormatGridInput
    {
        public IEnumerable<tco_attachments> finPlanAttachments { get; set; }
        public IEnumerable<tco_attachments> blistAttachments { get; set; }
        public IEnumerable<tco_attachments> deletedAttachments { get; set; }
        public List<KeyValueData> lstActionTags { get; set; }
        public List<keyvaluewithGuid> goalsData { get; set; }
        public List<StrategyActionsTagHelper> assessmentActionHeaderDetail { get; set; }
        public List<clsAlterCodes> alterCodes { get; set; }
        public ClsOrgVersionSpecificContent orgVersion { get; set; }

        public FormatGridInput()
        {
            finPlanAttachments = new List<tco_attachments>();
            blistAttachments = new List<tco_attachments>();
            deletedAttachments = new List<tco_attachments>();
            lstActionTags = new List<KeyValueData>();
            goalsData = new List<keyvaluewithGuid>();
            assessmentActionHeaderDetail = new List<StrategyActionsTagHelper>();
            alterCodes = new List<clsAlterCodes>();
            orgVersion = new ClsOrgVersionSpecificContent();
        }
    }

}