using System.Collections.Generic;

#pragma warning disable CS8618
namespace Framsikt.BL.Helpers
{
    public class OverviewSubChapter
    {
        public string aggregate_id { get; set; }
        public string aggregate_name { get; set; }
        public int tenantId { get; set; }
        public int line_item_id { get; set; }
        public string line_item { get; set; }
        public int line_group_id { get; set; }
        public string line_group { get; set; }
        public decimal? sum1 { get; set; }
        public decimal? sum2 { get; set; }
        public decimal? sum3 { get; set; }
        public decimal? sum4 { get; set; }
        public decimal? sum5 { get; set; }
        public decimal? sum6 { get; set; }
        public decimal? bud_amt_ytd { get; set; }
        public decimal? bud_amt_period { get; set; }
        public decimal? actual_amt_year { get; set; }
        public decimal? actual_amt_ytd { get; set; }
        public decimal? actual_amt_period { get; set; }
        public decimal? org_bud_amt_last_year { get; set; }
        public decimal? actual_amt_last_ytd { get; set; }
        public decimal? budget_changes { get; set; }
        public decimal? deviation_Ytd { get; set; }
        public decimal? deviation_Ytd_Pct { get; set; }
        public decimal? forecast_amount { get; set; }
        public decimal? revised_budget { get; set; }
        public decimal? original_budget { get; set; }
        public decimal? deviation_Forecast { get; set; }
        public decimal? forecastexpence { get; set; }
        public decimal? forecastdeviation { get; set; }
        public decimal? revisedbud_fcstincldeviation_diff { get; set; }
        public decimal? revbudfctinclaction_diff_pct_expence { get; set; }
        public decimal? Column1 { get; set; }
        public decimal? Column2 { get; set; }
        public decimal? Column3 { get; set; }
        public decimal? Column4 { get; set; }
        public decimal? Column5 { get; set; }
        public decimal? Column6 { get; set; }
        public decimal? Column7 { get; set; }
        public decimal? Column8 { get; set; }
        public decimal? Column9 { get; set; }
        public decimal? Column10 { get; set; }
        public decimal? Column11 { get; set; }
        public decimal? Column12 { get; set; }
        public decimal? Column13 { get; set; }
        public decimal? Column14 { get; set; }
        public decimal? Column15 { get; set; }
        public decimal? Column16 { get; set; }
        public decimal? Column17 { get; set; }
        public decimal? Column18 { get; set; }
        public decimal? Column19 { get; set; }
        public decimal? Column20 { get; set; }
        public decimal? Column21 { get; set; }
        public decimal? Column22 { get; set; }

        public decimal? Column23 { get; set; }
        public decimal? Column24 { get; set; }
        public decimal? Column25 { get; set; }
        public decimal? acctdeviation { get; set; }
        public decimal? acct_dev_pct { get; set; }
        public decimal? acct_dpct_expense { get; set; }
        public decimal? actual_amt_ytd_expence { get; set; }
        public decimal? actual_amt_ytd_income { get; set; }
        public decimal? acctDeviationpct { get; set; }
        public decimal? deviation_amount { get; set; }
        public decimal? forecast_incl_deviation { get; set; }
        public decimal? deviation_action_amount { get; set; }
        public decimal? forecast_deviation_pct { get; set; }
        public decimal? revised_budget_expence { get; set; }
        public decimal? revised_budget_income { get; set; }
        public decimal actual_amt_last_year { get; set; }
        public decimal? unaprv_bud_change { get; set; }
        public decimal? rev_inc_unaprv_bud_change { get; set; }
        public decimal? dev_rev_inc_unaprv_bud_change_and_fc { get; set; }
    }

    public class IntroSubChaptersHelper
    {
        public int line_item_id { get; set; }
        public string line_item { get; set; }
        public int line_group_id { get; set; }
        public string line_group { get; set; }
        public string type { get; set; }
        public string prog_code { get; set; }
        public string description { get; set; }
        public string org_id { get; set; }
        public string org_name { get; set; }
        public decimal org_bud { get; set; }
        public decimal rev_bud { get; set; }
        public decimal? acc_data { get; set; }
        public decimal forecast_amt { get; set; }
        public decimal? bud_changes { get; set; }
        public decimal deviation { get; set; }
        public decimal deviation_pct { get; set; }
        public decimal? Column1 { get; set; }
        public decimal? Column2 { get; set; }
        public decimal? Column3 { get; set; }
        public decimal? Column4 { get; set; }
        public decimal? Column5 { get; set; }
        public decimal? Column6 { get; set; }
        public decimal? Column7 { get; set; }
        public decimal? Column8 { get; set; }
        public decimal? gl_amount_last_year { get; set; }
    }

    public class AccountingSubChapterHelper
    {
        public string aggregate_id { get; set; }
        public string aggregate_name { get; set; }
        public string sub_level_id { get; set; }
        public string sub_level_name { get; set; }
        public int tenantId { get; set; }
        public int line_item_id { get; set; }
        public string line_item { get; set; }
        public int line_group_id { get; set; }
        public string line_group { get; set; }

        public decimal? actual_amt_year { get; set; }
        public decimal? actual_amt_last_ytd { get; set; }
        public decimal? revised_budget { get; set; }
        public decimal? original_budget { get; set; }

        public decimal? gl_amount { get; set; }
        public decimal? gl_amount_last_year { get; set; }


        public decimal? Column1 { get; set; }
        public decimal? Column2 { get; set; }
        public decimal? Column3 { get; set; }
        public decimal? Column4 { get; set; }
        public decimal? Column5 { get; set; }
        public string type { get; set; }
        public string prog_code { get; set; }
        public string description { get; set; }
        public decimal? actual_amt_last_year { get; set; }
    }


    public class IntroSubChapters1A1BHelper
    {
        public int line_item_id { get; set; }
        public string line_item { get; set; }
        public int line_group_id { get; set; }
        public string line_group { get; set; }
        public int show_flag { get; set; }
        public string aggregate_id { get; set; }
        public string aggregate_name { get; set; }
        public string sub_level_id { get; set; }
        public string sub_level_name { get; set; }
        public decimal forecast_amount { get; set; }
        public decimal original_budget { get; set; }
        public decimal? bud_amt_ytd { get; set; }
        public decimal bud_amt_period { get; set; }
        public decimal actual_amt_year { get; set; }
        public decimal actual_amt_ytd { get; set; }
        public decimal actual_amt_period { get; set; }
        public decimal forecast_incl_deviation { get; set; }
        public decimal org_bud_amt_last_year { get; set; }
        public decimal actual_amt_last_ytd { get; set; }
        public decimal revised_budget { get; set; }
        public decimal budget_changes { get; set; }
        public decimal revised_budget_income { get; set; }
        public decimal revised_budget_expence { get; set; }
        public decimal forecast_expence { get; set; }
        public decimal actual_amt_ytd_income { get; set; }
        public decimal actual_amt_ytd_expence { get; set; }
        public decimal deviation_ytd_amount { get; set; }
        public decimal deviation_amount { get; set; }
        public decimal deviation_action_amount { get; set; }
        public decimal deviation_ytd_pct { get; set; }
        public decimal deviation_pct { get; set; }
        public decimal deviation_forincldev { get; set; }
        public decimal deviation_forincldev_pct { get; set; }
        public decimal? Column1 { get; set; }
        public decimal? Column2 { get; set; }
        public decimal? Column3 { get; set; }
        public decimal? Column4 { get; set; }

        public decimal? Column5 { get; set; }
        public decimal? Column6 { get; set; }
        public decimal? Column7 { get; set; }
        public decimal? Column8 { get; set; }

        public decimal? Column9 { get; set; }
        public decimal? Column10 { get; set; }
        public decimal? Column11 { get; set; }
        public decimal? Column12 { get; set; }

        public decimal? Column13 { get; set; }
        public decimal? Column14 { get; set; }
        public decimal? Column15 { get; set; }
        public decimal? Column16 { get; set; }
        public decimal? Column17 { get; set; }
        public decimal? Column18 { get; set; }
        public decimal? Column19 { get; set; }

    }

    public class DecisionTotalFinStatHelper 
    {
        public int line_item_id { get; set; }
        public string line_item { get; set; }
        public int line_group_id { get; set; }
        public string line_group { get; set; }
        public string rowType { get; set; }
        public decimal? budget_ytd { get; set; }
        public decimal? accounting_ytd { get; set; }
        public decimal? deviation_ytd { get; set; }
        public decimal? actual_amt_last_year { get; set; }
        public decimal? actual_amt_year { get; set; }
        public decimal? org_bud_amt_year { get; set; }
        public decimal? revised_bud_amt_year { get; set; }
        public decimal? budget_change { get; set; }
        public decimal? new_budget { get; set; }
        public decimal? forecast_amount { get; set; }
        public decimal? deviation_forecast { get; set; }
        public decimal? deviation_forecast_percent { get; set; }
        public string Parameter { get; set; }
    }

    public class DecTotFinStatDataInsertHelper
    {
        public string NumberFormat { get; set; }
        public string DecimaleFormat { get; set; }
        public string PercentageFormat { get; set; }
        public string TotalString { get; set; }
        public bool DivideByMillions { get; set; }
        public bool IsPercentageType { get; set; }
        public List<DecisionTotalFinStatHelper> Result { get; set; }
        public List<PublishHelpers.ColumnDetails> LstColumnDetails { get; set; }
    }
}
