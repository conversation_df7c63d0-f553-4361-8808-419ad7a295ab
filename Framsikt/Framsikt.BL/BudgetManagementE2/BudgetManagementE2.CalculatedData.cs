using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Framsikt.BL;

public partial class BudgetManagement
{


    public async Task<string> GetCalculatedDataAsync(string description, string userId, string yearSelected, int budgetYear)
    {
        TenantDBContext budgetManagementDBContext = await _utility.GetTenantDBContextAsync();
        var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));

        List<tco_bud_prop_finished_status> orgIdStatus = await GetBudgetStatusofOrgIdsAsync(userId, budgetYear);

        var paramFinplanLevel1 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_1");
        var paramFinplanLevel2 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_2");

        string level1Type = string.Empty;
        int level1val = -1;
        string level2Type = string.Empty;
        int level2val = -1;

        if (!string.IsNullOrEmpty(paramFinplanLevel1) && paramFinplanLevel1.ToLower() == "org_id_2")
        {
            level1Type = "org_id";
            level1val = 2;
        }
        else if (!string.IsNullOrEmpty(paramFinplanLevel1) && paramFinplanLevel1.ToLower() == "org_id_3")
        {
            level1Type = "org_id";
            level1val = 3;
        }
        else if (!string.IsNullOrEmpty(paramFinplanLevel1) && paramFinplanLevel1.ToLower() == "org_id_4")
        {
            level1Type = "org_id";
            level1val = 4;
        }

        if (!string.IsNullOrEmpty(paramFinplanLevel2) && paramFinplanLevel2.ToLower() == "org_id_2")
        {
            level2Type = "org_id";
            level2val = 2;
        }
        else if (!string.IsNullOrEmpty(paramFinplanLevel2) && paramFinplanLevel2.ToLower() == "org_id_3")
        {
            level2Type = "org_id";
            level2val = 3;
        }
        else if (!string.IsNullOrEmpty(paramFinplanLevel2) && paramFinplanLevel2.ToLower() == "org_id_4")
        {
            level2Type = "org_id";
            level2val = 4;
        }

        if (!string.IsNullOrEmpty(paramFinplanLevel2) && paramFinplanLevel2.ToLower() == "service_id_2")
        {
            level2Type = "service_id";
            level2val = 2;
        }
        else if (!string.IsNullOrEmpty(paramFinplanLevel2) && paramFinplanLevel2.ToLower() == "service_id_3")
        {
            level2Type = "service_id";
            level2val = 3;
        }
        else if (!string.IsNullOrEmpty(paramFinplanLevel2) && paramFinplanLevel2.ToLower() == "service_id_4")
        {
            level2Type = "service_id";
            level2val = 4;
        }

        List<clsOrgStructure> lstOrgStructure = await _utility.GetTenantOrgStructureAsync(orgVersionContent, userId);
        clsOrgStructureLevelDetails tenantOrgLevelDetails = _utility.GetTenantOrgLevelDetails(lstOrgStructure);
        List<List<string>> DepartmentsAndFunctions = _utility.GetDepartmentsAndFunctionsForOrgStructure(lstOrgStructure, null, null, tenantOrgLevelDetails.level1, tenantOrgLevelDetails.level2);
        List<string> lstAllDepartments = DepartmentsAndFunctions[0];
        List<string> lstAllFunctions = DepartmentsAndFunctions[1];

        string firstLevel = string.Empty;
        string secondLevel = string.Empty;

        if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_1").Where(a => !string.IsNullOrEmpty(a.departmentCode)).Select(y => y.departmentCode).Count() > 0)
        {
            firstLevel = "dept";
        }
        else if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_1").Count(y => !string.IsNullOrEmpty(y.functionCode)) > 0)
        {
            firstLevel = "function";
        }

        if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_2").Where(a => !string.IsNullOrEmpty(a.departmentCode)).Select(y => y.departmentCode).Count() > 0)
        {
            secondLevel = "dept";
        }
        else if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_2").Where(a => !string.IsNullOrEmpty(a.functionCode)).Select(y => y.functionCode).Count() > 0)
        {
            secondLevel = "function";
        }

        var jObj = (JObject)JsonConvert.DeserializeObject(description);
        if (jObj != null)
        {
            int ArrayCounter = 0;
            long tempCostReductionTotal = 0, NewPrioritiesTotal = 0, tempNewPrioritiesTotal = 0, propoCostReducTotal = 0, propoNewprioritiesTotal = 0,
                proposedNewPriorityOfRow = 0, onGoingActionsRow = 0, onGoingActionsTotal = 0, opeffinvestmentsRow = 0,
                opeffinvestmentsTotal = 0, notAllocatedSummaryRow = 0, notAllocatedSummaryTotals = 0, agggregateOfLimitCodesWithFlag1 = 0;
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            var dbDatasetDataFor30And40And90 = (from bl in budgetManagementDBContext.tfp_budget_limits
                where bl.fk_tenant_id == userDetails.tenant_id
                      && (bl.budget_year == budgetYear)
                      && (bl.action_type == 30 || bl.action_type == 40 || bl.action_type == 90)
                select new
                {
                    level1 = bl.fp_level_1_value,
                    level2 = bl.fp_level_2_value,
                    year1 = bl.year_1_limit,
                    year2 = bl.year_2_limit,
                    year3 = bl.year_3_limit,
                    year4 = bl.year_4_limit,
                    actionType = bl.action_type
                });

            var tempDbDatasetDataFor30And40 = (from bl in budgetManagementDBContext.tfp_temp_budget_limits
                where bl.fk_tenant_id == userDetails.tenant_id
                      && (bl.budget_year == budgetYear)
                      && (bl.action_type == 30 || bl.action_type == 40)
                select new
                {
                    level1 = bl.fp_level_1_value,
                    level2 = bl.fp_level_2_value,
                    year1 = bl.year_1_limit,
                    year2 = bl.year_2_limit,
                    year3 = bl.year_3_limit,
                    year4 = bl.year_4_limit,
                    actionType = bl.action_type
                });

            IQueryable<clsServiceAreaData> dbDatasetDataForServiceAreaBudgetData = (from th in budgetManagementDBContext.tfp_trans_header
                join td in budgetManagementDBContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                    equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                where th.fk_tenant_id == userDetails.tenant_id
                      && (td.budget_year == budgetYear)
                      && (th.action_type == 30 || th.action_type == 40 || th.action_type == 21 || th.action_type == 9 || th.action_type == 31 || th.action_type == 41 || th.action_type == 9 || th.action_type == 90 || th.action_type == 20)
                select new clsServiceAreaData
                {
                    orgId = "",
                    orgName = "",
                    serviceId = "",
                    serviceName = "",
                    actionType = th.action_type,
                    actionId = th.pk_action_id,
                    accountCode = td.fk_account_code,
                    departmentCode = td.department_code,
                    functionCode = td.function_code,
                    year1Amount = td.year_1_amount,
                    year2Amount = td.year_2_amount,
                    year3Amount = td.year_3_amount,
                    year4Amount = td.year_4_amount,
                    description = th.description,
                    lineOrderId = th.line_order,
                    isManuallyAdded = th.isManuallyAdded,
                    alterCode = td.fk_alter_code,
                    changeId = td.fk_change_id
                });

            if (lstAllDepartments.Where(x => !string.IsNullOrEmpty(x)).Count() > 0 && lstAllFunctions.Where(x => !string.IsNullOrEmpty(x)).Count() > 0)
            {
                dbDatasetDataForServiceAreaBudgetData = dbDatasetDataForServiceAreaBudgetData.Where(y => lstAllDepartments.Contains(y.departmentCode) && lstAllFunctions.Contains(y.functionCode));
            }
            else if (lstAllFunctions.Where(x => !string.IsNullOrEmpty(x)).Count() > 0)
            {
                dbDatasetDataForServiceAreaBudgetData = dbDatasetDataForServiceAreaBudgetData.Where(y => lstAllFunctions.Contains(y.functionCode));
            }
            else if (lstAllDepartments.Where(x => !string.IsNullOrEmpty(x)).Count() > 0)
            {
                dbDatasetDataForServiceAreaBudgetData = dbDatasetDataForServiceAreaBudgetData.Where(y => lstAllDepartments.Contains(y.departmentCode));
            }

            var mainDatasetDatafor30And40nd90A = await (from bl in dbDatasetDataFor30And40And90
                select new
                {
                    level1 = bl.level1,
                    level2 = bl.level2,
                    year1 = bl.year1,
                    year2 = bl.year2,
                    year3 = bl.year3,
                    year4 = bl.year4,
                    actionType = bl.actionType
                }).ToListAsync();

            var tempMainDatasetDatafor30And40 = await (from bl in tempDbDatasetDataFor30And40
                select new
                {
                    level1 = bl.level1,
                    level2 = bl.level2,
                    year1 = bl.year1,
                    year2 = bl.year2,
                    year3 = bl.year3,
                    year4 = bl.year4,
                    actionType = bl.actionType
                }).ToListAsync();

            List<clsServiceAreaData> mainDatasetDataforServiceAreaBudgetData = await (from d in dbDatasetDataForServiceAreaBudgetData
                select new clsServiceAreaData
                {
                    orgId = "",
                    orgName = "",
                    serviceId = "",
                    serviceName = "",
                    actionType = d.actionType,
                    actionId = d.actionId,
                    accountCode = d.accountCode,
                    departmentCode = d.departmentCode,
                    functionCode = d.functionCode,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    description = d.description,
                    lineOrderId = d.lineOrderId,
                    isManuallyAdded = d.isManuallyAdded,
                    alterCode = d.alterCode,
                    changeId = d.changeId
                }).ToListAsync();

            mainDatasetDataforServiceAreaBudgetData = _utility.AssignOrgIdAndServiceIdToResultSet(orgVersionContent, mainDatasetDataforServiceAreaBudgetData, lstOrgStructure, firstLevel, secondLevel);

            tco_users_settings userSettings = await budgetManagementDBContext.tco_users_settings.FirstOrDefaultAsync(x =>
                x.fk_user_id == userDetails.pk_id && x.tenant_id == userDetails.tenant_id && x.is_active_tenant);

            var dataActiveBudgetChanges = await (from t in budgetManagementDBContext.tfp_budget_changes
                where t.fk_tenant_id == userDetails.tenant_id
                      && t.budget_year == budgetYear
                      && t.status == 1
                      && t.org_budget_flag == 1
                orderby t.change_date descending
                select t).ToListAsync();

            int activeChangeId = dataActiveBudgetChanges.Count() == 0 ? -1 :
                userSettings == null ? dataActiveBudgetChanges.FirstOrDefault().pk_change_id : userSettings.active_change_id == null ? dataActiveBudgetChanges.FirstOrDefault().pk_change_id :
                dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == userSettings.active_change_id) == null ? dataActiveBudgetChanges.FirstOrDefault().pk_change_id :
                dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == userSettings.active_change_id).pk_change_id;

            var costReductionOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.isManuallyAdded == -1
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    accountCode = d.accountCode,
                    departmentCode = d.departmentCode,
                    functionCode = d.functionCode,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    description = d.description,
                    lineOrderId = d.lineOrderId,
                    isManuallyAdded = d.isManuallyAdded,
                    changeId = d.changeId
                });

            if (activeChangeId != -1)
            {
                costReductionOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.changeId == activeChangeId && d.actionType == 31
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        accountCode = d.accountCode,
                        departmentCode = d.departmentCode,
                        functionCode = d.functionCode,
                        year1Amount = d.year1Amount,
                        year2Amount = d.year2Amount,
                        year3Amount = d.year3Amount,
                        year4Amount = d.year4Amount,
                        description = d.description,
                        lineOrderId = d.lineOrderId,
                        isManuallyAdded = d.isManuallyAdded,
                        changeId = d.changeId
                    });
            }
            else
            {
                costReductionOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.actionType == 31
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        accountCode = d.accountCode,
                        departmentCode = d.departmentCode,
                        functionCode = d.functionCode,
                        year1Amount = 0.0M,
                        year2Amount = 0.0M,
                        year3Amount = 0.0M,
                        year4Amount = 0.0M,
                        description = d.description,
                        lineOrderId = d.lineOrderId,
                        isManuallyAdded = d.isManuallyAdded,
                        changeId = d.changeId
                    });
            }

            var NewPrioritiesOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.isManuallyAdded == -1
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    accountCode = d.accountCode,
                    departmentCode = d.departmentCode,
                    functionCode = d.functionCode,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    description = d.description,
                    lineOrderId = d.lineOrderId,
                    isManuallyAdded = d.isManuallyAdded,
                    changeId = d.changeId
                });

            if (activeChangeId != -1)
            {
                NewPrioritiesOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.changeId == activeChangeId && d.actionType == 41
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        accountCode = d.accountCode,
                        departmentCode = d.departmentCode,
                        functionCode = d.functionCode,
                        year1Amount = d.year1Amount,
                        year2Amount = d.year2Amount,
                        year3Amount = d.year3Amount,
                        year4Amount = d.year4Amount,
                        description = d.description,
                        lineOrderId = d.lineOrderId,
                        isManuallyAdded = d.isManuallyAdded,
                        changeId = d.changeId
                    });
            }
            else
            {
                NewPrioritiesOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.actionType == 41
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        accountCode = d.accountCode,
                        departmentCode = d.departmentCode,
                        functionCode = d.functionCode,
                        year1Amount = 0.0M,
                        year2Amount = 0.0M,
                        year3Amount = 0.0M,
                        year4Amount = 0.0M,
                        description = d.description,
                        lineOrderId = d.lineOrderId,
                        isManuallyAdded = d.isManuallyAdded,
                        changeId = d.changeId
                    });
            }

            var onGoingActionsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.isManuallyAdded == -1
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    accountCode = d.accountCode,
                    departmentCode = d.departmentCode,
                    functionCode = d.functionCode,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    description = d.description,
                    lineOrderId = d.lineOrderId,
                    isManuallyAdded = d.isManuallyAdded,
                    changeId = d.changeId
                });

            if (activeChangeId != -1)
            {
                onGoingActionsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.changeId == activeChangeId && d.actionType == 9
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        accountCode = d.accountCode,
                        departmentCode = d.departmentCode,
                        functionCode = d.functionCode,
                        year1Amount = d.year1Amount,
                        year2Amount = d.year2Amount,
                        year3Amount = d.year3Amount,
                        year4Amount = d.year4Amount,
                        description = d.description,
                        lineOrderId = d.lineOrderId,
                        isManuallyAdded = d.isManuallyAdded,
                        changeId = d.changeId
                    });
            }
            else
            {
                onGoingActionsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.actionType == 9
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        accountCode = d.accountCode,
                        departmentCode = d.departmentCode,
                        functionCode = d.functionCode,
                        year1Amount = 0.0M,
                        year2Amount = 0.0M,
                        year3Amount = 0.0M,
                        year4Amount = 0.0M,
                        description = d.description,
                        lineOrderId = d.lineOrderId,
                        isManuallyAdded = d.isManuallyAdded,
                        changeId = d.changeId
                    });
            }
            var opeffInvestmentsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.isManuallyAdded == -1
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    accountCode = d.accountCode,
                    departmentCode = d.departmentCode,
                    functionCode = d.functionCode,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    description = d.description,
                    lineOrderId = d.lineOrderId,
                    isManuallyAdded = d.isManuallyAdded,
                    changeId = d.changeId
                });

            if (activeChangeId != -1)
            {
                opeffInvestmentsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.changeId == activeChangeId && d.actionType == 21
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        accountCode = d.accountCode,
                        departmentCode = d.departmentCode,
                        functionCode = d.functionCode,
                        year1Amount = d.year1Amount,
                        year2Amount = d.year2Amount,
                        year3Amount = d.year3Amount,
                        year4Amount = d.year4Amount,
                        description = d.description,
                        lineOrderId = d.lineOrderId,
                        isManuallyAdded = d.isManuallyAdded,
                        changeId = d.changeId
                    });
            }
            else
            {
                opeffInvestmentsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.actionType == 21
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        accountCode = d.accountCode,
                        departmentCode = d.departmentCode,
                        functionCode = d.functionCode,
                        year1Amount = 0.0M,
                        year2Amount = 0.0M,
                        year3Amount = 0.0M,
                        year4Amount = 0.0M,
                        description = d.description,
                        lineOrderId = d.lineOrderId,
                        isManuallyAdded = d.isManuallyAdded,
                        changeId = d.changeId
                    });
            }

            var propoCostReduc = (from bl in mainDatasetDatafor30And40nd90A
                where bl.actionType == 30
                select new
                {
                    level1 = bl.level1,
                    level2 = bl.level2,
                    year1 = bl.year1,
                    year2 = bl.year2,
                    year3 = bl.year3,
                    year4 = bl.year4,
                    actionType = bl.actionType
                }).ToList();

            var propoNewpriorities = (from bl in mainDatasetDatafor30And40nd90A
                where bl.actionType == 40
                select new
                {
                    level1 = bl.level1,
                    level2 = bl.level2,
                    year1 = bl.year1,
                    year2 = bl.year2,
                    year3 = bl.year3,
                    year4 = bl.year4,
                    actionType = bl.actionType
                }).ToList();

            var propoOnGoingActions = (from bl in mainDatasetDatafor30And40nd90A
                where bl.actionType == 90
                select new
                {
                    level1 = bl.level1,
                    level2 = bl.level2,
                    year1 = bl.year1,
                    year2 = bl.year2,
                    year3 = bl.year3,
                    year4 = bl.year4,
                    actionType = bl.actionType
                }).ToList();

            var notAllocatedProposedCostReduction = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 30
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    accountCode = d.accountCode,
                    departmentCode = d.departmentCode,
                    functionCode = d.functionCode,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    description = d.description,
                    lineOrderId = d.lineOrderId,
                    isManuallyAdded = d.isManuallyAdded,
                    changeId = d.changeId
                });

            var notAllocatedProposedNewPriorities = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 40
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    accountCode = d.accountCode,
                    departmentCode = d.departmentCode,
                    functionCode = d.functionCode,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    description = d.description,
                    lineOrderId = d.lineOrderId,
                    isManuallyAdded = d.isManuallyAdded,
                    changeId = d.changeId
                });

            var notAllocatedProposedOnGoingActions = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 90
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    accountCode = d.accountCode,
                    departmentCode = d.departmentCode,
                    functionCode = d.functionCode,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    description = d.description,
                    lineOrderId = d.lineOrderId,
                    isManuallyAdded = d.isManuallyAdded,
                    changeId = d.changeId
                });
            var notAllocatedopeffinvestments = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 20
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    accountCode = d.accountCode,
                    departmentCode = d.departmentCode,
                    functionCode = d.functionCode,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    description = d.description,
                    lineOrderId = d.lineOrderId,
                    isManuallyAdded = d.isManuallyAdded,
                    changeId = d.changeId
                });

            var tempPropoCostReduc = (from bl in tempMainDatasetDatafor30And40
                where bl.actionType == 30
                select new
                {
                    level1 = bl.level1,
                    level2 = bl.level2,
                    year1 = bl.year1,
                    year2 = bl.year2,
                    year3 = bl.year3,
                    year4 = bl.year4,
                    actionType = bl.actionType
                }).ToList();

            var tempPropoNewpriorities = (from bl in tempMainDatasetDatafor30And40
                where bl.actionType == 40
                select new
                {
                    level1 = bl.level1,
                    level2 = bl.level2,
                    year1 = bl.year1,
                    year2 = bl.year2,
                    year3 = bl.year3,
                    year4 = bl.year4,
                    actionType = bl.actionType
                }).ToList();

            // budget status from db and departments of tenant to evaluate
            foreach (var item in jObj["jsonData"])
            {
                long limitCodeSum = 0;
                long limitCodeSum0 = 0;
                int indexOfagggregateOfLimitCodesWithFlag1 = 0;

                for (int i = 4; i < jObj["Fields"].Count(); i++)
                {
                    if ((string)jObj["Fields"][i] == "agggregateOfLimitCodesWithFlag1")
                    {
                        indexOfagggregateOfLimitCodesWithFlag1 = i;
                    }
                }
                for (int i = 4; i < jObj["Fields"].Count(); i++)
                {
                    if (((string)jObj["Fields"][i]).StartsWith("limitCode"))
                    {
                        if (i > indexOfagggregateOfLimitCodesWithFlag1)
                        {
                            limitCodeSum0 = limitCodeSum0 + (long)item["Data"][i - 4];
                        }
                        limitCodeSum = limitCodeSum + (long)item["Data"][i - 4];
                    }
                }
                for (int i = 4; i < jObj["Fields"].Count(); i++)
                {
                    if (((string)item["ServiceAreaId"]) != null && (item["Data"].Count() > (i - 4)))
                    {
                        if ((string)jObj["Fields"][i] == "agggregateOfLimitCodesWithFlag1" && (item["Data"].Count() > (i - 4)))
                        {
                            if ((string)item["parentId"] == null)
                            {
                                agggregateOfLimitCodesWithFlag1 = agggregateOfLimitCodesWithFlag1 + (long)item["Data"][i - 4];
                            }
                        }
                        if ((string)jObj["Fields"][i] == "tempCostReduction")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((tempPropoCostReduc.Where(x => x.level1 == (string)item["ServiceAreaId"])).Sum(y => y.year1)) / 1000); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((tempPropoCostReduc.Where(x => x.level1 == (string)item["ServiceAreaId"])).Sum(y => y.year2)) / 1000); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((tempPropoCostReduc.Where(x => x.level1 == (string)item["ServiceAreaId"])).Sum(y => y.year3)) / 1000); }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((tempPropoCostReduc.Where(x => x.level1 == (string)item["ServiceAreaId"])).Sum(y => y.year4)) / 1000); }

                                tempCostReductionTotal = tempCostReductionTotal + (long)item["Data"][i - 4];
                            }
                            else
                            {
                                string parentId = Convert.ToString(item["parentId"]);
                                JArray arr = (JArray)jObj["jsonData"];
                                string level1 = (string)arr.FirstOrDefault(x => (string)x["id"] == parentId && (string)x["parentId"] == null)["ServiceAreaId"];

                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((tempPropoCostReduc.Where(x => x.level1 == level1 && x.level2 == (string)item["ServiceAreaId"])).Sum(y => y.year1)) / 1000); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((tempPropoCostReduc.Where(x => x.level1 == level1 && x.level2 == (string)item["ServiceAreaId"])).Sum(y => y.year2)) / 1000); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((tempPropoCostReduc.Where(x => x.level1 == level1 && x.level2 == (string)item["ServiceAreaId"])).Sum(y => y.year3)) / 1000); }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((tempPropoCostReduc.Where(x => x.level1 == level1 && x.level2 == (string)item["ServiceAreaId"])).Sum(y => y.year4)) / 1000); }
                            }
                        }
                        if ((string)jObj["Fields"][i] == "tempNewPriorities")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(Convert.ToDecimal(((tempPropoNewpriorities.Where(x => x.level1 == (string)item["ServiceAreaId"])).Sum(y => y.year1)) / 1000), 0); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(Convert.ToDecimal(((tempPropoNewpriorities.Where(x => x.level1 == (string)item["ServiceAreaId"])).Sum(y => y.year2)) / 1000), 0); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(Convert.ToDecimal(((tempPropoNewpriorities.Where(x => x.level1 == (string)item["ServiceAreaId"])).Sum(y => y.year3)) / 1000), 0); }
                                else if (yearSelected == "Year4")
                                {
                                    item["Data"][i - 4] = Math.Round(Convert.ToDecimal(((tempPropoNewpriorities.Where(x => x.level1 == (string)item["ServiceAreaId"])).Sum(y => y.year4)) / 1000), 0);
                                }

                                tempNewPrioritiesTotal = tempNewPrioritiesTotal + (long)item["Data"][i - 4];
                            }
                            else
                            {
                                string parentId = Convert.ToString(item["parentId"]);
                                JArray arr = (JArray)jObj["jsonData"];
                                string level1 = (string)arr.FirstOrDefault(x => (string)x["id"] == parentId && (string)x["parentId"] == null)["ServiceAreaId"];

                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((tempPropoNewpriorities.Where(x => x.level1 == level1 && x.level2 == (string)item["ServiceAreaId"])).Sum(y => y.year1)) / 1000); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((tempPropoNewpriorities.Where(x => x.level1 == level1 && x.level2 == (string)item["ServiceAreaId"])).Sum(y => y.year2)) / 1000); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((tempPropoNewpriorities.Where(x => x.level1 == level1 && x.level2 == (string)item["ServiceAreaId"])).Sum(y => y.year3)) / 1000); }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((tempPropoNewpriorities.Where(x => x.level1 == level1 && x.level2 == (string)item["ServiceAreaId"])).Sum(y => y.year4)) / 1000); }
                            }
                        }
                        else if ((string)jObj["Fields"][i] == "newPriorities")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1") { item["Data"][i - 4] = limitCodeSum0; }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = limitCodeSum0; }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = limitCodeSum0; }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = limitCodeSum0; }

                                NewPrioritiesTotal = NewPrioritiesTotal + (long)item["Data"][i - 4];
                            }
                            else
                            {
                                string parentId = Convert.ToString(item["parentId"]);
                                JArray arr = (JArray)jObj["jsonData"];
                                string level1 = (string)arr.FirstOrDefault(x => (string)x["id"] == parentId && (string)x["parentId"] == null)["ServiceAreaId"];

                                if (yearSelected == "Year1") { item["Data"][i - 4] = limitCodeSum0; }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = limitCodeSum0; }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = limitCodeSum0; }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = limitCodeSum0; }
                            }
                        }
                        else if ((string)jObj["Fields"][i] == "proposedNewPriority")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000);
                                }
                                else if (yearSelected == "Year2")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000);
                                }
                                else if (yearSelected == "Year3")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000);
                                }
                                else if (yearSelected == "Year4")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000);
                                }
                                proposedNewPriorityOfRow = (long)item["Data"][i - 4];
                                propoNewprioritiesTotal = propoNewprioritiesTotal + (long)item["Data"][i - 4];
                            }
                            else
                            {
                                string parentId = Convert.ToString(item["parentId"]);
                                JArray arr = (JArray)jObj["jsonData"];
                                string level1 = (string)arr.FirstOrDefault(x => (string)x["id"] == parentId && (string)x["parentId"] == null)["ServiceAreaId"];

                                if (yearSelected == "Year1")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000);
                                }
                                else if (yearSelected == "Year2")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000);
                                }
                                else if (yearSelected == "Year3")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000);
                                }
                                else if (yearSelected == "Year4")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000);
                                }
                                proposedNewPriorityOfRow = (long)item["Data"][i - 4];
                            }
                        }
                        else if ((string)jObj["Fields"][i] == "onGoingActions")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000); }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000); }
                                onGoingActionsRow = (long)item["Data"][i - 4];
                                onGoingActionsTotal = onGoingActionsTotal + (long)item["Data"][i - 4];
                            }
                            else
                            {
                                string parentId = Convert.ToString(item["parentId"]);
                                JArray arr = (JArray)jObj["jsonData"];
                                string level1 = (string)arr.FirstOrDefault(x => (string)x["id"] == parentId && (string)x["parentId"] == null)["ServiceAreaId"];

                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000); }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000); }
                                onGoingActionsRow = (long)item["Data"][i - 4];
                            }
                        }
                        else if ((string)jObj["Fields"][i] == "operationaleffectInvestments")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000); }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000); }
                                opeffinvestmentsRow = (long)item["Data"][i - 4];
                                opeffinvestmentsTotal = opeffinvestmentsTotal + (long)item["Data"][i - 4];
                            }
                            else
                            {
                                string parentId = Convert.ToString(item["parentId"]);
                                JArray arr = (JArray)jObj["jsonData"];
                                string level1 = (string)arr.FirstOrDefault(x => (string)x["id"] == parentId && (string)x["parentId"] == null)["ServiceAreaId"];

                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000); }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000); }
                                opeffinvestmentsRow = (long)item["Data"][i - 4];
                            }
                        }

                        if ((string)jObj["Fields"][i] == "notAllocatedDataSummary")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000);
                                }
                                else if (yearSelected == "Year2")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000);
                                }
                                else if (yearSelected == "Year3")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000);
                                }
                                else if (yearSelected == "Year4")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000);
                                }

                                notAllocatedSummaryRow = (long)item["Data"][i - 4];
                                notAllocatedSummaryTotals = notAllocatedSummaryTotals + (long)item["Data"][i - 4];
                            }
                            else
                            {
                                string parentId = Convert.ToString(item["parentId"]);
                                JArray arr = (JArray)jObj["jsonData"];
                                string level1 = (string)arr.FirstOrDefault(x => (string)x["id"] == parentId && (string)x["parentId"] == null)["ServiceAreaId"];

                                if (yearSelected == "Year1")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000);
                                }
                                else if (yearSelected == "Year2")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000);
                                }
                                else if (yearSelected == "Year3")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000);
                                }
                                else if (yearSelected == "Year4")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000);
                                }

                                notAllocatedSummaryRow = (long)item["Data"][i - 4];
                            }
                        }
                        else if ((string)jObj["Fields"][i] == "summary")
                        {
                            item["Data"][i - 4] = (long)item["Data"][i - 4 - 1] + limitCodeSum;
                        }
                    }
                    else if (item["Data"].Count() > (i - 4))
                    {
                        if ((string)jObj["Fields"][i] == "tempCostReduction")
                        {
                            item["Data"][i - 4] = tempCostReductionTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "agggregateOfLimitCodesWithFlag1")
                        {
                            item["Data"][i - 4] = agggregateOfLimitCodesWithFlag1;
                        }
                        else if ((string)jObj["Fields"][i] == "tempNewPriorities")
                        {
                            if (yearSelected == "Year1")
                            {
                                item["Data"][i - 4] = Math.Round(Convert.ToDecimal((tempPropoNewpriorities.Sum(y => y.year1)) / 1000), 0);
                            }
                            else if (yearSelected == "Year2")
                            {
                                item["Data"][i - 4] = Math.Round(Convert.ToDecimal((tempPropoNewpriorities.Sum(y => y.year2)) / 1000), 0);
                            }
                            else if (yearSelected == "Year3")
                            {
                                item["Data"][i - 4] = Math.Round(Convert.ToDecimal((tempPropoNewpriorities.Sum(y => y.year3)) / 1000), 0);
                            }
                            if (yearSelected == "Year4")
                            {
                                item["Data"][i - 4] = Math.Round(Convert.ToDecimal((tempPropoNewpriorities.Sum(y => y.year4)) / 1000), 0);
                            }
                        }
                        else if (((string)jObj["Fields"][i] == "newPriorities") && (item["Data"].Count() > (i - 4)))
                        {
                            item["Data"][i - 4] = limitCodeSum0;
                        }
                        else if ((string)jObj["Fields"][i] == "proposedCostReduction" && (item["Data"].Count() > (i - 4)))
                        {
                            item["Data"][i - 4] = propoCostReducTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "proposedNewPriority" && (item["Data"].Count() > (i - 4)))
                        {
                            item["Data"][i - 4] = propoNewprioritiesTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "onGoingActions" && (item["Data"].Count() > (i - 4)))
                        {
                            item["Data"][i - 4] = onGoingActionsTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "operationaleffectInvestments" && (item["Data"].Count() > (i - 4)))
                        {
                            item["Data"][i - 4] = opeffinvestmentsTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "summary" && (item["Data"].Count() > (i - 4)))
                        {
                            item["Data"][i - 4] = (long)item["Data"][i - 4 - 1] + limitCodeSum;
                        }
                        else if ((string)jObj["Fields"][i] == "notAllocatedDataSummary" && (item["Data"].Count() > (i - 4)))
                        {
                            item["Data"][i - 4] = notAllocatedSummaryTotals;
                        }
                    }
                }

                if (!string.IsNullOrEmpty(((string)item["ServiceAreaId"])))
                {
                    if (level1Type == "org_id" && string.IsNullOrEmpty(level2Type))
                    {
                        item["IndicatorStatus"] = GetIndicatorStatus((string)item["ServiceAreaId"], level1val, string.Empty, -1, activeChangeId, orgIdStatus);
                    }
                    else if (level1Type == "org_id" && level2Type == "org_id")
                    {
                        if (string.IsNullOrEmpty(((string)item["parentId"])))
                        {
                            item["IndicatorStatus"] = GetIndicatorStatus((string)item["ServiceAreaId"], level1val, string.Empty, -1, activeChangeId, orgIdStatus);
                        }
                        else
                        {
                            item["IndicatorStatus"] = GetIndicatorStatus((string)item["ServiceAreaId"], level2val, string.Empty, -1, activeChangeId, orgIdStatus);
                        }
                    }
                    else if (level1Type == "org_id" && level2Type == "service_id")
                    {
                        if (string.IsNullOrEmpty(((string)item["parentId"])))
                        {
                            item["IndicatorStatus"] = GetIndicatorStatus((string)item["ServiceAreaId"], level1val, string.Empty, -1, activeChangeId, orgIdStatus);
                        }
                        else
                        {
                            item["IndicatorStatus"] = GetIndicatorStatus((string)item["parentId"], level1val, (string)item["ServiceAreaId"], level2val, activeChangeId, orgIdStatus);
                        }
                    }
                }
                else
                {
                    item["IndicatorStatus"] = "NotStarted";
                }

                ArrayCounter = ArrayCounter + 1;
                proposedNewPriorityOfRow = 0; opeffinvestmentsRow = 0;
            }
        }
        return Convert.ToString(jObj);
    }



    public async Task<string> GetCalculatedDataForBudgetPhaseAsync(string description, string userId, string yearSelected, int budgetYear)
    {
        TenantDBContext budgetManagementDBContext = await _utility.GetTenantDBContextAsync();
        var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));

        List<tco_bud_prop_finished_status> orgIdStatus = await GetBudgetStatusofOrgIdsAsync(userId, budgetYear);

        var paramFinplanLevel1 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_1");
        var paramFinplanLevel2 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_2");

        string level1Type = string.Empty;
        int level1val = -1;
        string level2Type = string.Empty;
        int level2val = -1;

        if (!string.IsNullOrEmpty(paramFinplanLevel1) && paramFinplanLevel1.ToLower() == "org_id_2")
        {
            level1Type = "org_id";
            level1val = 2;
        }
        else if (!string.IsNullOrEmpty(paramFinplanLevel1) && paramFinplanLevel1.ToLower() == "org_id_3")
        {
            level1Type = "org_id";
            level1val = 3;
        }
        else if (!string.IsNullOrEmpty(paramFinplanLevel1) && paramFinplanLevel1.ToLower() == "org_id_4")
        {
            level1Type = "org_id";
            level1val = 4;
        }

        if (!string.IsNullOrEmpty(paramFinplanLevel2) && paramFinplanLevel2.ToLower() == "org_id_2")
        {
            level2Type = "org_id";
            level2val = 2;
        }
        else if (!string.IsNullOrEmpty(paramFinplanLevel2) && paramFinplanLevel2.ToLower() == "org_id_3")
        {
            level2Type = "org_id";
            level2val = 3;
        }
        else if (!string.IsNullOrEmpty(paramFinplanLevel2) && paramFinplanLevel2.ToLower() == "org_id_4")
        {
            level2Type = "org_id";
            level2val = 4;
        }

        if (!string.IsNullOrEmpty(paramFinplanLevel2) && paramFinplanLevel2.ToLower() == "service_id_2")
        {
            level2Type = "service_id";
            level2val = 2;
        }
        else if (!string.IsNullOrEmpty(paramFinplanLevel2) && paramFinplanLevel2.ToLower() == "service_id_3")
        {
            level2Type = "service_id";
            level2val = 3;
        }
        else if (!string.IsNullOrEmpty(paramFinplanLevel2) && paramFinplanLevel2.ToLower() == "service_id_4")
        {
            level2Type = "service_id";
            level2val = 4;
        }

        List<clsOrgStructure> lstOrgStructure = await _utility.GetTenantOrgStructureAsync(orgVersionContent, userId);
        clsOrgStructureLevelDetails tenantOrgLevelDetails = _utility.GetTenantOrgLevelDetails(lstOrgStructure);
        List<List<string>> DepartmentsAndFunctions = _utility.GetDepartmentsAndFunctionsForOrgStructure(lstOrgStructure, null, null, tenantOrgLevelDetails.level1, tenantOrgLevelDetails.level2);
        List<string> lstAllDepartments = DepartmentsAndFunctions[0];
        List<string> lstAllFunctions = DepartmentsAndFunctions[1];

        string firstLevel = string.Empty;
        string secondLevel = string.Empty;

        if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_1").Where(a => !string.IsNullOrEmpty(a.departmentCode)).Select(y => y.departmentCode).Count() > 0)
        {
            firstLevel = "dept";
        }
        else if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_1").Count(y => !string.IsNullOrEmpty(y.functionCode)) > 0)
        {
            firstLevel = "function";
        }

        if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_2").Where(a => !string.IsNullOrEmpty(a.departmentCode)).Select(y => y.departmentCode).Count() > 0)
        {
            secondLevel = "dept";
        }
        else if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_2").Where(a => !string.IsNullOrEmpty(a.functionCode)).Select(y => y.functionCode).Count() > 0)
        {
            secondLevel = "function";
        }

        var jObj = (JObject)JsonConvert.DeserializeObject(description);
        if (jObj != null)
        {
            int ArrayCounter = 0;
            long tempCostReductionTotal = 0, NewPrioritiesTotal = 0, tempNewPrioritiesTotal = 0, propoCostReducTotal = 0, propoNewprioritiesTotal = 0,
                proposedNewPriorityOfRow = 0, onGoingActionsRow = 0, onGoingActionsTotal = 0, opeffinvestmentsRow = 0,
                opeffinvestmentsTotal = 0, notAllocatedSummaryRow = 0, notAllocatedSummaryTotals = 0, agggregateOfLimitCodesWithFlag1 = 0;
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            var dbDatasetDataFor30And40And90 = (from bl in budgetManagementDBContext.tfp_budget_limits
                where bl.fk_tenant_id == userDetails.tenant_id
                      && (bl.budget_year == budgetYear)
                      && (bl.action_type == 30 || bl.action_type == 40 || bl.action_type == 90)
                select new
                {
                    level1 = bl.fp_level_1_value,
                    level2 = bl.fp_level_2_value,
                    year1 = bl.year_1_limit,
                    year2 = bl.year_2_limit,
                    year3 = bl.year_3_limit,
                    year4 = bl.year_4_limit,
                    actionType = bl.action_type
                });

            var tempDbDatasetDataFor30And40 = (from bl in budgetManagementDBContext.tfp_temp_budget_limits
                where bl.fk_tenant_id == userDetails.tenant_id
                      && (bl.budget_year == budgetYear)
                      && (bl.action_type == 30 || bl.action_type == 40)
                select new
                {
                    level1 = bl.fp_level_1_value,
                    level2 = bl.fp_level_2_value,
                    year1 = bl.year_1_limit,
                    year2 = bl.year_2_limit,
                    year3 = bl.year_3_limit,
                    year4 = bl.year_4_limit,
                    actionType = bl.action_type
                });

            IQueryable<clsServiceAreaData> dbDatasetDataForServiceAreaBudgetData = (from th in budgetManagementDBContext.tfp_trans_header
                join td in budgetManagementDBContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                    equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                where th.fk_tenant_id == userDetails.tenant_id
                      && (td.budget_year == budgetYear)
                      && (th.action_type == 30 || th.action_type == 40 || th.action_type == 21 || th.action_type == 9 || th.action_type == 31 || th.action_type == 41 || th.action_type == 9 || th.action_type == 90 || th.action_type == 20)
                select new clsServiceAreaData
                {
                    orgId = "",
                    orgName = "",
                    serviceId = "",
                    serviceName = "",
                    actionType = th.action_type,
                    actionId = th.pk_action_id,
                    accountCode = td.fk_account_code,
                    departmentCode = td.department_code,
                    functionCode = td.function_code,
                    year1Amount = td.year_1_amount,
                    year2Amount = td.year_2_amount,
                    year3Amount = td.year_3_amount,
                    year4Amount = td.year_4_amount,
                    description = th.description,
                    lineOrderId = th.line_order,
                    isManuallyAdded = th.isManuallyAdded,
                    alterCode = td.fk_alter_code,
                    changeId = td.fk_change_id
                });

            if (lstAllDepartments.Where(x => !string.IsNullOrEmpty(x)).Count() > 0 && lstAllFunctions.Where(x => !string.IsNullOrEmpty(x)).Count() > 0)
            {
                dbDatasetDataForServiceAreaBudgetData = dbDatasetDataForServiceAreaBudgetData.Where(y => lstAllDepartments.Contains(y.departmentCode) && lstAllFunctions.Contains(y.functionCode));
            }
            else if (lstAllFunctions.Where(x => !string.IsNullOrEmpty(x)).Count() > 0)
            {
                dbDatasetDataForServiceAreaBudgetData = dbDatasetDataForServiceAreaBudgetData.Where(y => lstAllFunctions.Contains(y.functionCode));
            }
            else if (lstAllDepartments.Where(x => !string.IsNullOrEmpty(x)).Count() > 0)
            {
                dbDatasetDataForServiceAreaBudgetData = dbDatasetDataForServiceAreaBudgetData.Where(y => lstAllDepartments.Contains(y.departmentCode));
            }

            var mainDatasetDatafor30And40nd90A = (from bl in dbDatasetDataFor30And40And90
                select new
                {
                    level1 = bl.level1,
                    level2 = bl.level2,
                    year1 = bl.year1,
                    year2 = bl.year2,
                    year3 = bl.year3,
                    year4 = bl.year4,
                    actionType = bl.actionType
                }).ToList();

            var tempMainDatasetDatafor30And40 = (from bl in tempDbDatasetDataFor30And40
                select new
                {
                    level1 = bl.level1,
                    level2 = bl.level2,
                    year1 = bl.year1,
                    year2 = bl.year2,
                    year3 = bl.year3,
                    year4 = bl.year4,
                    actionType = bl.actionType
                }).ToList();

            List<clsServiceAreaData> mainDatasetDataforServiceAreaBudgetData = (from d in dbDatasetDataForServiceAreaBudgetData
                select new clsServiceAreaData
                {
                    orgId = "",
                    orgName = "",
                    serviceId = "",
                    serviceName = "",
                    actionType = d.actionType,
                    actionId = d.actionId,
                    accountCode = d.accountCode,
                    departmentCode = d.departmentCode,
                    functionCode = d.functionCode,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    description = d.description,
                    lineOrderId = d.lineOrderId,
                    isManuallyAdded = d.isManuallyAdded,
                    alterCode = d.alterCode,
                    changeId = d.changeId
                }).ToList();

            mainDatasetDataforServiceAreaBudgetData = _utility.AssignOrgIdAndServiceIdToResultSet(orgVersionContent, mainDatasetDataforServiceAreaBudgetData, lstOrgStructure, firstLevel, secondLevel);

            tco_users_settings userSettings = budgetManagementDBContext.tco_users_settings.FirstOrDefault(x =>
                x.fk_user_id == userDetails.pk_id && x.tenant_id == userDetails.tenant_id && x.is_active_tenant);

            var dataActiveBudgetChanges = (from t in budgetManagementDBContext.tfp_budget_changes
                where t.fk_tenant_id == userDetails.tenant_id
                      && t.budget_year == budgetYear
                      && t.status == 1
                      && t.org_budget_flag == 1
                orderby t.change_date descending
                select t).ToList();

            int activeChangeId = dataActiveBudgetChanges.Count() == 0 ? -1 :
                userSettings == null ? dataActiveBudgetChanges.FirstOrDefault().pk_change_id : userSettings.active_change_id == null ? dataActiveBudgetChanges.FirstOrDefault().pk_change_id :
                dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == userSettings.active_change_id) == null ? dataActiveBudgetChanges.FirstOrDefault().pk_change_id :
                dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == userSettings.active_change_id).pk_change_id;

            var costReductionOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.isManuallyAdded == -1
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    accountCode = d.accountCode,
                    departmentCode = d.departmentCode,
                    functionCode = d.functionCode,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    description = d.description,
                    lineOrderId = d.lineOrderId,
                    isManuallyAdded = d.isManuallyAdded,
                    changeId = d.changeId
                });

            if (activeChangeId != -1)
            {
                costReductionOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.changeId == activeChangeId && d.actionType == 31
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        accountCode = d.accountCode,
                        departmentCode = d.departmentCode,
                        functionCode = d.functionCode,
                        year1Amount = d.year1Amount,
                        year2Amount = d.year2Amount,
                        year3Amount = d.year3Amount,
                        year4Amount = d.year4Amount,
                        description = d.description,
                        lineOrderId = d.lineOrderId,
                        isManuallyAdded = d.isManuallyAdded,
                        changeId = d.changeId
                    });
            }
            else
            {
                costReductionOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.actionType == 31
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        accountCode = d.accountCode,
                        departmentCode = d.departmentCode,
                        functionCode = d.functionCode,
                        year1Amount = 0.0M,
                        year2Amount = 0.0M,
                        year3Amount = 0.0M,
                        year4Amount = 0.0M,
                        description = d.description,
                        lineOrderId = d.lineOrderId,
                        isManuallyAdded = d.isManuallyAdded,
                        changeId = d.changeId
                    });
            }

            var NewPrioritiesOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.isManuallyAdded == -1
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    accountCode = d.accountCode,
                    departmentCode = d.departmentCode,
                    functionCode = d.functionCode,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    description = d.description,
                    lineOrderId = d.lineOrderId,
                    isManuallyAdded = d.isManuallyAdded,
                    changeId = d.changeId
                });

            if (activeChangeId != -1)
            {
                NewPrioritiesOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.changeId == activeChangeId && d.actionType == 41
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        accountCode = d.accountCode,
                        departmentCode = d.departmentCode,
                        functionCode = d.functionCode,
                        year1Amount = d.year1Amount,
                        year2Amount = d.year2Amount,
                        year3Amount = d.year3Amount,
                        year4Amount = d.year4Amount,
                        description = d.description,
                        lineOrderId = d.lineOrderId,
                        isManuallyAdded = d.isManuallyAdded,
                        changeId = d.changeId
                    });
            }
            else
            {
                NewPrioritiesOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.actionType == 41
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        accountCode = d.accountCode,
                        departmentCode = d.departmentCode,
                        functionCode = d.functionCode,
                        year1Amount = 0.0M,
                        year2Amount = 0.0M,
                        year3Amount = 0.0M,
                        year4Amount = 0.0M,
                        description = d.description,
                        lineOrderId = d.lineOrderId,
                        isManuallyAdded = d.isManuallyAdded,
                        changeId = d.changeId
                    });
            }

            var onGoingActionsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.isManuallyAdded == -1
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    accountCode = d.accountCode,
                    departmentCode = d.departmentCode,
                    functionCode = d.functionCode,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    description = d.description,
                    lineOrderId = d.lineOrderId,
                    isManuallyAdded = d.isManuallyAdded,
                    changeId = d.changeId
                });

            if (activeChangeId != -1)
            {
                onGoingActionsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.changeId == activeChangeId && d.actionType == 9
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        accountCode = d.accountCode,
                        departmentCode = d.departmentCode,
                        functionCode = d.functionCode,
                        year1Amount = d.year1Amount,
                        year2Amount = d.year2Amount,
                        year3Amount = d.year3Amount,
                        year4Amount = d.year4Amount,
                        description = d.description,
                        lineOrderId = d.lineOrderId,
                        isManuallyAdded = d.isManuallyAdded,
                        changeId = d.changeId
                    });
            }
            else
            {
                onGoingActionsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.actionType == 9
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        accountCode = d.accountCode,
                        departmentCode = d.departmentCode,
                        functionCode = d.functionCode,
                        year1Amount = 0.0M,
                        year2Amount = 0.0M,
                        year3Amount = 0.0M,
                        year4Amount = 0.0M,
                        description = d.description,
                        lineOrderId = d.lineOrderId,
                        isManuallyAdded = d.isManuallyAdded,
                        changeId = d.changeId
                    });
            }
            var opeffInvestmentsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.isManuallyAdded == -1
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    accountCode = d.accountCode,
                    departmentCode = d.departmentCode,
                    functionCode = d.functionCode,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    description = d.description,
                    lineOrderId = d.lineOrderId,
                    isManuallyAdded = d.isManuallyAdded,
                    changeId = d.changeId
                });

            if (activeChangeId != -1)
            {
                opeffInvestmentsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.changeId == activeChangeId && d.actionType == 21
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        accountCode = d.accountCode,
                        departmentCode = d.departmentCode,
                        functionCode = d.functionCode,
                        year1Amount = d.year1Amount,
                        year2Amount = d.year2Amount,
                        year3Amount = d.year3Amount,
                        year4Amount = d.year4Amount,
                        description = d.description,
                        lineOrderId = d.lineOrderId,
                        isManuallyAdded = d.isManuallyAdded,
                        changeId = d.changeId
                    });
            }
            else
            {
                opeffInvestmentsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.actionType == 21
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        accountCode = d.accountCode,
                        departmentCode = d.departmentCode,
                        functionCode = d.functionCode,
                        year1Amount = 0.0M,
                        year2Amount = 0.0M,
                        year3Amount = 0.0M,
                        year4Amount = 0.0M,
                        description = d.description,
                        lineOrderId = d.lineOrderId,
                        isManuallyAdded = d.isManuallyAdded,
                        changeId = d.changeId
                    });
            }

            var propoCostReduc = (from bl in mainDatasetDatafor30And40nd90A
                where bl.actionType == 30
                select new
                {
                    level1 = bl.level1,
                    level2 = bl.level2,
                    year1 = bl.year1,
                    year2 = bl.year2,
                    year3 = bl.year3,
                    year4 = bl.year4,
                    actionType = bl.actionType
                }).ToList();

            var propoNewpriorities = (from bl in mainDatasetDatafor30And40nd90A
                where bl.actionType == 40
                select new
                {
                    level1 = bl.level1,
                    level2 = bl.level2,
                    year1 = bl.year1,
                    year2 = bl.year2,
                    year3 = bl.year3,
                    year4 = bl.year4,
                    actionType = bl.actionType
                }).ToList();

            var propoOnGoingActions = (from bl in mainDatasetDatafor30And40nd90A
                where bl.actionType == 90
                select new
                {
                    level1 = bl.level1,
                    level2 = bl.level2,
                    year1 = bl.year1,
                    year2 = bl.year2,
                    year3 = bl.year3,
                    year4 = bl.year4,
                    actionType = bl.actionType
                }).ToList();

            var notAllocatedProposedCostReduction = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 30
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    accountCode = d.accountCode,
                    departmentCode = d.departmentCode,
                    functionCode = d.functionCode,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    description = d.description,
                    lineOrderId = d.lineOrderId,
                    isManuallyAdded = d.isManuallyAdded,
                    changeId = d.changeId
                });

            var notAllocatedProposedNewPriorities = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 40
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    accountCode = d.accountCode,
                    departmentCode = d.departmentCode,
                    functionCode = d.functionCode,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    description = d.description,
                    lineOrderId = d.lineOrderId,
                    isManuallyAdded = d.isManuallyAdded,
                    changeId = d.changeId
                });

            var notAllocatedProposedOnGoingActions = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 90
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    accountCode = d.accountCode,
                    departmentCode = d.departmentCode,
                    functionCode = d.functionCode,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    description = d.description,
                    lineOrderId = d.lineOrderId,
                    isManuallyAdded = d.isManuallyAdded,
                    changeId = d.changeId
                });
            var notAllocatedopeffinvestments = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 20
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    accountCode = d.accountCode,
                    departmentCode = d.departmentCode,
                    functionCode = d.functionCode,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    description = d.description,
                    lineOrderId = d.lineOrderId,
                    isManuallyAdded = d.isManuallyAdded,
                    changeId = d.changeId
                });

            var tempPropoCostReduc = (from bl in tempMainDatasetDatafor30And40
                where bl.actionType == 30
                select new
                {
                    level1 = bl.level1,
                    level2 = bl.level2,
                    year1 = bl.year1,
                    year2 = bl.year2,
                    year3 = bl.year3,
                    year4 = bl.year4,
                    actionType = bl.actionType
                }).ToList();

            var tempPropoNewpriorities = (from bl in tempMainDatasetDatafor30And40
                where bl.actionType == 40
                select new
                {
                    level1 = bl.level1,
                    level2 = bl.level2,
                    year1 = bl.year1,
                    year2 = bl.year2,
                    year3 = bl.year3,
                    year4 = bl.year4,
                    actionType = bl.actionType
                }).ToList();

            // budget status from db and departments of tenant to evaluate

            foreach (var item in jObj["jsonData"])
            {
                long limitCodeSum = 0;
                long limitCodeSum0 = 0;
                int indexOfagggregateOfLimitCodesWithFlag1 = 0;

                for (int i = 4; i < jObj["Fields"].Count(); i++)
                {
                    if ((string)jObj["Fields"][i] == "agggregateOfLimitCodesWithFlag1")
                    {
                        indexOfagggregateOfLimitCodesWithFlag1 = i;
                    }
                }
                for (int i = 4; i < jObj["Fields"].Count(); i++)
                {
                    if (((string)jObj["Fields"][i]).StartsWith("limitCode"))
                    {
                        if (i > indexOfagggregateOfLimitCodesWithFlag1)
                        {
                            limitCodeSum0 = limitCodeSum0 + (long)item["Data"][i - 4];
                        }
                        limitCodeSum = limitCodeSum + (long)item["Data"][i - 4];
                    }
                }
                for (int i = 4; i < jObj["Fields"].Count(); i++)
                {
                    if (((string)item["ServiceAreaId"]) != null)
                    {
                        if ((string)jObj["Fields"][i] == "agggregateOfLimitCodesWithFlag1")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                agggregateOfLimitCodesWithFlag1 = agggregateOfLimitCodesWithFlag1 + (long)item["Data"][i - 4];
                            }
                        }
                        if ((string)jObj["Fields"][i] == "tempCostReduction")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((tempPropoCostReduc.Where(x => x.level1 == (string)item["ServiceAreaId"])).Sum(y => y.year1)) / 1000); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((tempPropoCostReduc.Where(x => x.level1 == (string)item["ServiceAreaId"])).Sum(y => y.year2)) / 1000); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((tempPropoCostReduc.Where(x => x.level1 == (string)item["ServiceAreaId"])).Sum(y => y.year3)) / 1000); }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((tempPropoCostReduc.Where(x => x.level1 == (string)item["ServiceAreaId"])).Sum(y => y.year4)) / 1000); }

                                tempCostReductionTotal = tempCostReductionTotal + (long)item["Data"][i - 4];
                            }
                            else
                            {
                                string parentId = Convert.ToString(item["parentId"]);
                                JArray arr = (JArray)jObj["jsonData"];
                                string level1 = (string)arr.FirstOrDefault(x => (string)x["id"] == parentId && (string)x["parentId"] == null)["ServiceAreaId"];

                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((tempPropoCostReduc.Where(x => x.level1 == level1 && x.level2 == (string)item["ServiceAreaId"])).Sum(y => y.year1)) / 1000); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((tempPropoCostReduc.Where(x => x.level1 == level1 && x.level2 == (string)item["ServiceAreaId"])).Sum(y => y.year2)) / 1000); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((tempPropoCostReduc.Where(x => x.level1 == level1 && x.level2 == (string)item["ServiceAreaId"])).Sum(y => y.year3)) / 1000); }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((tempPropoCostReduc.Where(x => x.level1 == level1 && x.level2 == (string)item["ServiceAreaId"])).Sum(y => y.year4)) / 1000); }
                            }
                        }
                        if ((string)jObj["Fields"][i] == "tempNewPriorities")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(Convert.ToDecimal(((tempPropoNewpriorities.Where(x => x.level1 == (string)item["ServiceAreaId"])).Sum(y => y.year1)) / 1000), 0); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(Convert.ToDecimal(((tempPropoNewpriorities.Where(x => x.level1 == (string)item["ServiceAreaId"])).Sum(y => y.year2)) / 1000), 0); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(Convert.ToDecimal(((tempPropoNewpriorities.Where(x => x.level1 == (string)item["ServiceAreaId"])).Sum(y => y.year3)) / 1000), 0); }
                                else if (yearSelected == "Year4")
                                {
                                    item["Data"][i - 4] = Math.Round(Convert.ToDecimal(((tempPropoNewpriorities.Where(x => x.level1 == (string)item["ServiceAreaId"])).Sum(y => y.year4)) / 1000), 0);
                                }

                                tempNewPrioritiesTotal = tempNewPrioritiesTotal + (long)item["Data"][i - 4];
                            }
                            else
                            {
                                string parentId = Convert.ToString(item["parentId"]);
                                JArray arr = (JArray)jObj["jsonData"];
                                string level1 = (string)arr.FirstOrDefault(x => (string)x["id"] == parentId && (string)x["parentId"] == null)["ServiceAreaId"];

                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((tempPropoNewpriorities.Where(x => x.level1 == level1 && x.level2 == (string)item["ServiceAreaId"])).Sum(y => y.year1)) / 1000); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((tempPropoNewpriorities.Where(x => x.level1 == level1 && x.level2 == (string)item["ServiceAreaId"])).Sum(y => y.year2)) / 1000); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((tempPropoNewpriorities.Where(x => x.level1 == level1 && x.level2 == (string)item["ServiceAreaId"])).Sum(y => y.year3)) / 1000); }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((tempPropoNewpriorities.Where(x => x.level1 == level1 && x.level2 == (string)item["ServiceAreaId"])).Sum(y => y.year4)) / 1000); }
                            }
                        }
                        else if ((string)jObj["Fields"][i] == "newPriorities")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1") { item["Data"][i - 4] = limitCodeSum0; }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = limitCodeSum0; }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = limitCodeSum0; }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = limitCodeSum0; }

                                NewPrioritiesTotal = NewPrioritiesTotal + (long)item["Data"][i - 4];
                            }
                            else
                            {
                                string parentId = Convert.ToString(item["parentId"]);
                                JArray arr = (JArray)jObj["jsonData"];
                                string level1 = (string)arr.FirstOrDefault(x => (string)x["id"] == parentId && (string)x["parentId"] == null)["ServiceAreaId"];

                                if (yearSelected == "Year1") { item["Data"][i - 4] = limitCodeSum0; }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = limitCodeSum0; }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = limitCodeSum0; }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = limitCodeSum0; }
                            }
                        }
                        else if ((string)jObj["Fields"][i] == "proposedNewPriority")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000);
                                }
                                else if (yearSelected == "Year2")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000);
                                }
                                else if (yearSelected == "Year3")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000);
                                }
                                else if (yearSelected == "Year4")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000);
                                }
                                proposedNewPriorityOfRow = (long)item["Data"][i - 4];
                                propoNewprioritiesTotal = propoNewprioritiesTotal + (long)item["Data"][i - 4];
                            }
                            else
                            {
                                string parentId = Convert.ToString(item["parentId"]);
                                JArray arr = (JArray)jObj["jsonData"];
                                string level1 = (string)arr.FirstOrDefault(x => (string)x["id"] == parentId && (string)x["parentId"] == null)["ServiceAreaId"];

                                if (yearSelected == "Year1")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000);
                                }
                                else if (yearSelected == "Year2")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000);
                                }
                                else if (yearSelected == "Year3")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000);
                                }
                                else if (yearSelected == "Year4")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000);
                                }
                                proposedNewPriorityOfRow = (long)item["Data"][i - 4];
                            }
                        }
                        else if ((string)jObj["Fields"][i] == "onGoingActions")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000); }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000); }
                                onGoingActionsRow = (long)item["Data"][i - 4];
                                onGoingActionsTotal = onGoingActionsTotal + (long)item["Data"][i - 4];
                            }
                            else
                            {
                                string parentId = Convert.ToString(item["parentId"]);
                                JArray arr = (JArray)jObj["jsonData"];
                                string level1 = (string)arr.FirstOrDefault(x => (string)x["id"] == parentId && (string)x["parentId"] == null)["ServiceAreaId"];

                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000); }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000); }
                                onGoingActionsRow = (long)item["Data"][i - 4];
                            }
                        }
                        else if ((string)jObj["Fields"][i] == "operationaleffectInvestments")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000); }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000); }
                                opeffinvestmentsRow = (long)item["Data"][i - 4];
                                opeffinvestmentsTotal = opeffinvestmentsTotal + (long)item["Data"][i - 4];
                            }
                            else
                            {
                                string parentId = Convert.ToString(item["parentId"]);
                                JArray arr = (JArray)jObj["jsonData"];
                                string level1 = (string)arr.FirstOrDefault(x => (string)x["id"] == parentId && (string)x["parentId"] == null)["ServiceAreaId"];

                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000); }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000); }
                                opeffinvestmentsRow = (long)item["Data"][i - 4];
                            }
                        }

                        if ((string)jObj["Fields"][i] == "notAllocatedDataSummary")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000);
                                }
                                else if (yearSelected == "Year2")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000);
                                }
                                else if (yearSelected == "Year3")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000);
                                }
                                else if (yearSelected == "Year4")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000);
                                }

                                notAllocatedSummaryRow = (long)item["Data"][i - 4];
                                notAllocatedSummaryTotals = notAllocatedSummaryTotals + (long)item["Data"][i - 4];
                            }
                            else
                            {
                                string parentId = Convert.ToString(item["parentId"]);
                                JArray arr = (JArray)jObj["jsonData"];
                                string level1 = (string)arr.FirstOrDefault(x => (string)x["id"] == parentId && (string)x["parentId"] == null)["ServiceAreaId"];

                                if (yearSelected == "Year1")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000);
                                }
                                else if (yearSelected == "Year2")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000);
                                }
                                else if (yearSelected == "Year3")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000);
                                }
                                else if (yearSelected == "Year4")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000);
                                }

                                notAllocatedSummaryRow = (long)item["Data"][i - 4];
                            }
                        }
                        else if ((string)jObj["Fields"][i] == "summary")
                        {
                            item["Data"][i - 4] = (long)item["Data"][i - 4 - 1] + limitCodeSum;
                        }
                    }
                    else
                    {
                        if ((string)jObj["Fields"][i] == "tempCostReduction")
                        {
                            item["Data"][i - 4] = tempCostReductionTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "agggregateOfLimitCodesWithFlag1")
                        {
                            item["Data"][i - 4] = agggregateOfLimitCodesWithFlag1;
                        }
                        else if ((string)jObj["Fields"][i] == "tempNewPriorities")
                        {
                            if (yearSelected == "Year1")
                            {
                                item["Data"][i - 4] = Math.Round(Convert.ToDecimal((tempPropoNewpriorities.Sum(y => y.year1)) / 1000), 0);
                            }
                            else if (yearSelected == "Year2")
                            {
                                item["Data"][i - 4] = Math.Round(Convert.ToDecimal((tempPropoNewpriorities.Sum(y => y.year2)) / 1000), 0);
                            }
                            else if (yearSelected == "Year3")
                            {
                                item["Data"][i - 4] = Math.Round(Convert.ToDecimal((tempPropoNewpriorities.Sum(y => y.year3)) / 1000), 0);
                            }
                            if (yearSelected == "Year4")
                            {
                                item["Data"][i - 4] = Math.Round(Convert.ToDecimal((tempPropoNewpriorities.Sum(y => y.year4)) / 1000), 0);
                            }
                        }
                        else if ((string)jObj["Fields"][i] == "newPriorities")
                        {
                            item["Data"][i - 4] = limitCodeSum0;
                        }
                        else if ((string)jObj["Fields"][i] == "proposedCostReduction")
                        {
                            item["Data"][i - 4] = propoCostReducTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "proposedNewPriority")
                        {
                            item["Data"][i - 4] = propoNewprioritiesTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "onGoingActions")
                        {
                            item["Data"][i - 4] = onGoingActionsTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "operationaleffectInvestments")
                        {
                            item["Data"][i - 4] = opeffinvestmentsTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "summary")
                        {
                            item["Data"][i - 4] = (long)item["Data"][i - 4 - 1] + limitCodeSum;
                        }
                        else if ((string)jObj["Fields"][i] == "notAllocatedDataSummary")
                        {
                            item["Data"][i - 4] = notAllocatedSummaryTotals;
                        }
                    }
                }

                if (!string.IsNullOrEmpty(((string)item["ServiceAreaId"])))
                {
                    if (level1Type == "org_id" && string.IsNullOrEmpty(level2Type))
                    {
                        item["IndicatorStatus"] = GetIndicatorStatus((string)item["ServiceAreaId"], level1val, string.Empty, -1, activeChangeId, orgIdStatus);
                    }
                    else if (level1Type == "org_id" && level2Type == "org_id")
                    {
                        if (string.IsNullOrEmpty(((string)item["parentId"])))
                        {
                            item["IndicatorStatus"] = GetIndicatorStatus((string)item["ServiceAreaId"], level1val, string.Empty, -1, activeChangeId, orgIdStatus);
                        }
                        else
                        {
                            item["IndicatorStatus"] = GetIndicatorStatus((string)item["ServiceAreaId"], level2val, string.Empty, -1, activeChangeId, orgIdStatus);
                        }
                    }
                    else if (level1Type == "org_id" && level2Type == "service_id")
                    {
                        if (string.IsNullOrEmpty(((string)item["parentId"])))
                        {
                            item["IndicatorStatus"] = GetIndicatorStatus((string)item["ServiceAreaId"], level1val, string.Empty, -1, activeChangeId, orgIdStatus);
                        }
                        else
                        {
                            item["IndicatorStatus"] = GetIndicatorStatus((string)item["parentId"], level1val, (string)item["ServiceAreaId"], level2val, activeChangeId, orgIdStatus);
                        }
                    }
                }
                else
                {
                    item["IndicatorStatus"] = "NotStarted";
                }

                ArrayCounter = ArrayCounter + 1;
                proposedNewPriorityOfRow = 0; opeffinvestmentsRow = 0;
            }
        }
        return Convert.ToString(jObj);
    }



    private async Task<List<tco_bud_prop_finished_status>> GetBudgetStatusofOrgIdsAsync(string userName, int budgetYear)
    {
        UserData userDetails = await _utility.GetUserDetailsAsync(userName);
        TenantDBContext budgetManagementDbContext = await _utility.GetTenantDBContextAsync();
        var lst = await budgetManagementDbContext.tco_bud_prop_finished_status.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear).ToListAsync();
        return lst;
    }



    private string GetIndicatorStatus(string orgId, int orgLevel, string serviceId, int serviceLevel, int activeChangeId, List<tco_bud_prop_finished_status> orgIdStatus)
    {
        string IndicatorStatus = "NotStarted";

        var row = orgIdStatus.FirstOrDefault(x => x.org_id == orgId &&
                                                  x.org_level == orgLevel &&
                                                  x.service_id == serviceId &&
                                                  x.service_level == serviceLevel &&
                                                  x.fk_change_id == activeChangeId);

        if (row != null)
        {
            if (row.status == 0)
            {
                IndicatorStatus = "NotStarted";
            }
            else if (row.status == 1)
            {
                IndicatorStatus = "Complete";
            }
            else if (row.status == 2)
            {
                IndicatorStatus = "InProgress";
            }
            else if (row.status == 4)
            {
                IndicatorStatus = "Returned";
            }
            else
            {
                IndicatorStatus = "NotStarted";
            }
        }

        return IndicatorStatus;
    }



    public async Task<string> GetCalculatedDataBasedOnDynamicOrgStructureAsync(string description, string userId, string yearSelected, int budgetYear, bool isChapterSetUp)
    {
        TenantDBContext budgetManagementDBContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));

        string viewTypeKeyLvl1 = await _utility.GetParameterValueAsync(userId, "FP_ORG_1_IN_SCREEN");
        string viewTypeKeyLvl2 = await _utility.GetParameterValueAsync(userId, "FP_ORG_2_IN_SCREEN");

        string orgId1 = string.Empty;
        string orgId2 = string.Empty;

        if (string.IsNullOrEmpty(viewTypeKeyLvl1))
        {
            orgId1 = "org_id_2";
            orgId2 = "org_id_3";
        }
        else
        {
            orgId1 = viewTypeKeyLvl1.ToLower();
            if (string.IsNullOrEmpty(viewTypeKeyLvl2))
            {
                orgId2 = orgId1 == "org_id_1" ? "org_id_2" :
                    orgId1 == "org_id_2" ? "org_id_3" :
                    orgId1 == "org_id_3" ? "org_id_4" : "org_id_5";
            }
            else
            {
                orgId2 = viewTypeKeyLvl2.ToLower();
            }
        }

        var jObj = (JObject)JsonConvert.DeserializeObject(description);
        if (jObj != null)
        {
            int ArrayCounter = 0;
            long tempCostReductionTotal = 0, NewPrioritiesTotal = 0, propoCostReducTotal = 0, propoNewprioritiesTotal = 0,
                proposedNewPriorityOfRow = 0, onGoingActionsRow = 0, onGoingActionsTotal = 0, opeffinvestmentsRow = 0, opeffinvestmentsTotal = 0, notAllocatedSummaryRow = 0, notAllocatedSummaryTotals = 0;

            List<clsServiceAreaData> dbDataset = null;
            List<clsServiceAreaData> dbDatasetFilter = null;

            dbDataset = await (from th in budgetManagementDBContext.tfp_trans_header
                join td in budgetManagementDBContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                    equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                where th.fk_tenant_id == userDetails.tenant_id
                      && (td.budget_year == budgetYear)
                      && (th.action_type == 30 || th.action_type == 40 || th.action_type == 21 || th.action_type == 9 || th.action_type == 31 || th.action_type == 41 || th.action_type == 9 || th.action_type == 90 || th.action_type == 20)
                select new clsServiceAreaData
                {
                    departmentCode = td.department_code,
                    actionType = th.action_type,
                    actionId = th.pk_action_id,
                    year1Amount = td.year_1_amount,
                    year2Amount = td.year_2_amount,
                    year3Amount = td.year_3_amount,
                    year4Amount = td.year_4_amount,
                    alterCode = td.fk_alter_code,
                    changeId = td.fk_change_id
                }).ToListAsync();
            if (isChapterSetUp)
            {
                var attributeData = await _finUtility.GetAttributeConnectedDepartmentsAsync(userId, budgetYear);
                dbDatasetFilter = (from a in dbDataset
                    join b in attributeData on a.departmentCode equals b.departmentCode
                    select new clsServiceAreaData //same class is used to work with exiting grid binding
                    {
                        orgId = b.attributeId,
                        orgName = b.formattedAttributeName,
                        serviceId = b.orgId,
                        serviceName = b.orgName,
                        actionType = a.actionType,
                        accountCode = a.accountCode,
                        year1Amount = a.year1Amount,
                        year2Amount = a.year2Amount,
                        year3Amount = a.year3Amount,
                        year4Amount = a.year4Amount,
                        sumCode = a.sumCode,
                        sumDescription = a.sumDescription,
                        limitCode = a.limitCode,
                        limitDescription = a.limitDescription,
                        cabFlag = a.cabFlag,
                        departmentCode = a.departmentCode,
                        budgetPhaseId = a.budgetPhaseId,
                        budgetPhaseDescription = a.budgetPhaseDescription,
                        sort_order = a.sort_order
                    }).ToList();
            }
            else if (!string.IsNullOrEmpty(orgId1))
            {
                dbDatasetFilter = (from a in dbDataset
                    join b in orgVersionContent.lstOrgHierarchy on new { a = a.departmentCode }
                        equals new { a = b.fk_department_code }
                    select new clsServiceAreaData
                    {
                        orgId = orgId1.ToLower() == "org_id_1" ? b.org_id_1 :
                            orgId1.ToLower() == "org_id_2" ? b.org_id_2 :
                            orgId1.ToLower() == "org_id_3" ? b.org_id_3 :
                            orgId1.ToLower() == "org_id_4" ? b.org_id_4 : b.org_id_1,
                        orgName = orgId1.ToLower() == "org_id_1" ? b.org_name_1 :
                            orgId1.ToLower() == "org_id_2" ? b.org_name_2 :
                            orgId1.ToLower() == "org_id_3" ? b.org_name_3 :
                            orgId1.ToLower() == "org_id_4" ? b.org_name_4 : b.org_name_1,
                        actionType = a.actionType,
                        year1Amount = a.year1Amount,
                        year2Amount = a.year2Amount,
                        year3Amount = a.year3Amount,
                        year4Amount = a.year4Amount,
                        alterCode = a.alterCode,
                        changeId = a.changeId,
                        departmentCode = a.departmentCode
                    }).ToList();

                if (!string.IsNullOrEmpty(orgId2))
                {
                    dbDatasetFilter = (from a in dbDatasetFilter
                        join b in orgVersionContent.lstOrgHierarchy on new { a = a.departmentCode }
                            equals new { a = b.fk_department_code }
                        select new clsServiceAreaData
                        {
                            orgId = a.orgId,
                            orgName = a.orgName,
                            serviceId = orgId2.ToLower() == "org_id_1" ? b.org_id_1 :
                                orgId2.ToLower() == "org_id_2" ? b.org_id_2 :
                                orgId2.ToLower() == "org_id_3" ? b.org_id_3 :
                                orgId2.ToLower() == "org_id_4" ? b.org_id_4 : b.org_id_1,
                            serviceName = orgId2.ToLower() == "org_id_1" ? b.org_name_1 :
                                orgId2.ToLower() == "org_id_2" ? b.org_name_2 :
                                orgId2.ToLower() == "org_id_3" ? b.org_name_3 :
                                orgId2.ToLower() == "org_id_4" ? b.org_name_4 : b.org_name_1,
                            actionType = a.actionType,
                            year1Amount = a.year1Amount,
                            year2Amount = a.year2Amount,
                            year3Amount = a.year3Amount,
                            year4Amount = a.year4Amount,
                            alterCode = a.alterCode,
                            changeId = a.changeId,
                            departmentCode = a.departmentCode
                        }).ToList();
                }
            }

            List<clsServiceAreaData> mainDatasetDataforServiceAreaBudgetData = (from d in dbDatasetFilter
                select new clsServiceAreaData
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    alterCode = d.alterCode,
                    changeId = d.changeId
                }).ToList();

            tco_users_settings userSettings = await budgetManagementDBContext.tco_users_settings.FirstOrDefaultAsync(x =>
                x.fk_user_id == userDetails.pk_id && x.tenant_id == userDetails.tenant_id && x.is_active_tenant);

            var dataActiveBudgetChanges = await (from t in budgetManagementDBContext.tfp_budget_changes
                where t.fk_tenant_id == userDetails.tenant_id
                      && t.budget_year == budgetYear
                      && t.status == 1
                      && t.org_budget_flag == 1
                orderby t.change_date descending
                select t).ToListAsync();

            int activeChangeId = dataActiveBudgetChanges.Count() == 0 ? -1 :
                userSettings == null ? dataActiveBudgetChanges.FirstOrDefault().pk_change_id : userSettings.active_change_id == null ? dataActiveBudgetChanges.FirstOrDefault().pk_change_id :
                dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == userSettings.active_change_id) == null ? dataActiveBudgetChanges.FirstOrDefault().pk_change_id :
                dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == userSettings.active_change_id).pk_change_id;

            var costReductionOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.isManuallyAdded == -1
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    changeId = d.changeId
                });

            if (activeChangeId != -1)
            {
                costReductionOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.changeId == activeChangeId && d.actionType == 31
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        year1Amount = d.year1Amount,
                        year2Amount = d.year2Amount,
                        year3Amount = d.year3Amount,
                        year4Amount = d.year4Amount,
                        changeId = d.changeId
                    });
            }
            else
            {
                costReductionOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.actionType == 31
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        year1Amount = 0.0M,
                        year2Amount = 0.0M,
                        year3Amount = 0.0M,
                        year4Amount = 0.0M,
                        changeId = d.changeId
                    });
            }

            var NewPrioritiesOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.isManuallyAdded == -1
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    changeId = d.changeId
                });

            if (activeChangeId != -1)
            {
                NewPrioritiesOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.changeId == activeChangeId && d.actionType == 41
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        year1Amount = d.year1Amount,
                        year2Amount = d.year2Amount,
                        year3Amount = d.year3Amount,
                        year4Amount = d.year4Amount,
                        changeId = d.changeId
                    });
            }
            else
            {
                NewPrioritiesOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.actionType == 41
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        year1Amount = 0.0M,
                        year2Amount = 0.0M,
                        year3Amount = 0.0M,
                        year4Amount = 0.0M,
                        changeId = d.changeId
                    });
            }

            var onGoingActionsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.isManuallyAdded == -1
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    changeId = d.changeId
                });

            if (activeChangeId != -1)
            {
                onGoingActionsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.changeId == activeChangeId && d.actionType == 9
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        year1Amount = d.year1Amount,
                        year2Amount = d.year2Amount,
                        year3Amount = d.year3Amount,
                        year4Amount = d.year4Amount,
                        changeId = d.changeId
                    });
            }
            else
            {
                onGoingActionsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.actionType == 9
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        year1Amount = 0.0M,
                        year2Amount = 0.0M,
                        year3Amount = 0.0M,
                        year4Amount = 0.0M,
                        changeId = d.changeId
                    });
            }
            var opeffInvestmentsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.isManuallyAdded == -1
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    changeId = d.changeId
                });

            if (activeChangeId != -1)
            {
                opeffInvestmentsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.changeId == activeChangeId && d.actionType == 21
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        year1Amount = d.year1Amount,
                        year2Amount = d.year2Amount,
                        year3Amount = d.year3Amount,
                        year4Amount = d.year4Amount,
                        changeId = d.changeId
                    });
            }
            else
            {
                opeffInvestmentsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.actionType == 21
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        year1Amount = 0.0M,
                        year2Amount = 0.0M,
                        year3Amount = 0.0M,
                        year4Amount = 0.0M,
                        changeId = d.changeId
                    });
            }

            var notAllocatedProposedCostReduction = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 30
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    changeId = d.changeId
                });

            var notAllocatedProposedNewPriorities = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 40
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    changeId = d.changeId
                });

            var notAllocatedProposedOnGoingActions = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 90
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    changeId = d.changeId
                });
            var notAllocatedopeffinvestments = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 20
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    changeId = d.changeId
                });

            foreach (var item in jObj["jsonData"])
            {
                long limitCodeSum = 0;
                for (int i = 4; i < jObj["Fields"].Count(); i++)
                {
                    if (((string)jObj["Fields"][i]).StartsWith("limitCode"))
                    {
                        limitCodeSum = limitCodeSum + (long)item["Data"][i - 4];
                    }
                }
                for (int i = 4; i < jObj["Fields"].Count(); i++)
                {
                    if (((string)item["ServiceAreaId"]) != null)
                    {
                        if ((string)jObj["Fields"][i] == "proposedNewPriority")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000);
                                }
                                else if (yearSelected == "Year2")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000);
                                }
                                else if (yearSelected == "Year3")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000);
                                }
                                else if (yearSelected == "Year4")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000);
                                }
                                proposedNewPriorityOfRow = (long)item["Data"][i - 4];
                                propoNewprioritiesTotal = propoNewprioritiesTotal + (long)item["Data"][i - 4];
                            }
                            else
                            {
                                string parentId = Convert.ToString(item["parentId"]);
                                JArray arr = (JArray)jObj["jsonData"];
                                string level1 = (string)arr.FirstOrDefault(x => (string)x["id"] == parentId && (string)x["parentId"] == null)["ServiceAreaId"];

                                if (yearSelected == "Year1")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000);
                                }
                                else if (yearSelected == "Year2")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000);
                                }
                                else if (yearSelected == "Year3")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000);
                                }
                                else if (yearSelected == "Year4")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000);
                                }
                                proposedNewPriorityOfRow = (long)item["Data"][i - 4];
                            }
                        }
                        else if ((string)jObj["Fields"][i] == "onGoingActions")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000); }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000); }
                                onGoingActionsRow = (long)item["Data"][i - 4];
                                onGoingActionsTotal = onGoingActionsTotal + (long)item["Data"][i - 4];
                            }
                            else
                            {
                                string parentId = Convert.ToString(item["parentId"]);
                                JArray arr = (JArray)jObj["jsonData"];
                                string level1 = (string)arr.FirstOrDefault(x => (string)x["id"] == parentId && (string)x["parentId"] == null)["ServiceAreaId"];

                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000); }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000); }
                                onGoingActionsRow = (long)item["Data"][i - 4];
                            }
                        }
                        else if ((string)jObj["Fields"][i] == "operationaleffectInvestments")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000); }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000); }
                                opeffinvestmentsRow = (long)item["Data"][i - 4];
                                opeffinvestmentsTotal = opeffinvestmentsTotal + (long)item["Data"][i - 4];
                            }
                            else
                            {
                                string parentId = Convert.ToString(item["parentId"]);
                                JArray arr = (JArray)jObj["jsonData"];
                                string level1 = (string)arr.FirstOrDefault(x => (string)x["id"] == parentId && (string)x["parentId"] == null)["ServiceAreaId"];

                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000); }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000); }
                                opeffinvestmentsRow = (long)item["Data"][i - 4];
                            }
                        }

                        if ((string)jObj["Fields"][i] == "notAllocatedDataSummary")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000);
                                }
                                else if (yearSelected == "Year2")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000);
                                }
                                else if (yearSelected == "Year3")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000);
                                }
                                else if (yearSelected == "Year4")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000);
                                }

                                notAllocatedSummaryRow = (long)item["Data"][i - 4];
                                notAllocatedSummaryTotals = notAllocatedSummaryTotals + (long)item["Data"][i - 4];
                            }
                            else
                            {
                                string parentId = Convert.ToString(item["parentId"]);
                                JArray arr = (JArray)jObj["jsonData"];
                                string level1 = (string)arr.FirstOrDefault(x => (string)x["id"] == parentId && (string)x["parentId"] == null)["ServiceAreaId"];

                                if (yearSelected == "Year1")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000);
                                }
                                else if (yearSelected == "Year2")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000);
                                }
                                else if (yearSelected == "Year3")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000);
                                }
                                else if (yearSelected == "Year4")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000);
                                }

                                notAllocatedSummaryRow = (long)item["Data"][i - 4];
                            }
                        }
                        else if ((string)jObj["Fields"][i] == "summary")
                        {
                            item["Data"][i - 4] = (long)item["Data"][i - 4 - 1] + limitCodeSum;
                        }
                    }
                    else
                    {
                        if ((string)jObj["Fields"][i] == "tempCostReduction")
                        {
                            item["Data"][i - 4] = tempCostReductionTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "newPriorities")
                        {
                            item["Data"][i - 4] = NewPrioritiesTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "proposedCostReduction")
                        {
                            item["Data"][i - 4] = propoCostReducTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "proposedNewPriority")
                        {
                            item["Data"][i - 4] = propoNewprioritiesTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "onGoingActions")
                        {
                            item["Data"][i - 4] = onGoingActionsTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "operationaleffectInvestments")
                        {
                            item["Data"][i - 4] = opeffinvestmentsTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "summary")
                        {
                            item["Data"][i - 4] = (long)item["Data"][i - 4 - 1] + limitCodeSum;
                        }
                        else if ((string)jObj["Fields"][i] == "notAllocatedDataSummary")
                        {
                            item["Data"][i - 4] = notAllocatedSummaryTotals;
                        }
                    }
                }

                ArrayCounter = ArrayCounter + 1;
                proposedNewPriorityOfRow = 0; opeffinvestmentsRow = 0;
            }
        }
        return Convert.ToString(jObj);
    }



    public async Task<string> GetCalculatedDataBasedOnDynamicOrgStructureForBudgetPhaseAsync(string description, string userId, string yearSelected, int budgetYear, bool isChapterSetUp)
    {
        TenantDBContext budgetManagementDBContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));

        string viewTypeKeyLvl1 = await _utility.GetParameterValueAsync(userId, "FP_ORG_1_IN_SCREEN");
        string viewTypeKeyLvl2 = await _utility.GetParameterValueAsync(userId, "FP_ORG_2_IN_SCREEN");

        string orgId1 = string.Empty;
        string orgId2 = string.Empty;

        if (string.IsNullOrEmpty(viewTypeKeyLvl1))
        {
            orgId1 = "org_id_2";
            orgId2 = "org_id_3";
        }
        else
        {
            orgId1 = viewTypeKeyLvl1.ToLower();
            if (string.IsNullOrEmpty(viewTypeKeyLvl2))
            {
                orgId2 = orgId1 == "org_id_1" ? "org_id_2" :
                    orgId1 == "org_id_2" ? "org_id_3" :
                    orgId1 == "org_id_3" ? "org_id_4" : "org_id_5";
            }
            else
            {
                orgId2 = viewTypeKeyLvl2.ToLower();
            }
        }

        var jObj = (JObject)JsonConvert.DeserializeObject(description);
        if (jObj != null)
        {
            int ArrayCounter = 0;
            long tempCostReductionTotal = 0, NewPrioritiesTotal = 0, propoCostReducTotal = 0, propoNewprioritiesTotal = 0,
                proposedNewPriorityOfRow = 0, onGoingActionsRow = 0, onGoingActionsTotal = 0, opeffinvestmentsRow = 0, opeffinvestmentsTotal = 0, notAllocatedSummaryRow = 0, notAllocatedSummaryTotals = 0;

            List<clsServiceAreaData> dbDataset = null;
            List<clsServiceAreaData> dbDatasetFilter = null;

            dbDataset = (from th in budgetManagementDBContext.tfp_trans_header
                join td in budgetManagementDBContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                    equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                where th.fk_tenant_id == userDetails.tenant_id
                      && (td.budget_year == budgetYear)
                      && (th.action_type == 30 || th.action_type == 40 || th.action_type == 21 || th.action_type == 9 || th.action_type == 31 || th.action_type == 41 || th.action_type == 9 || th.action_type == 90 || th.action_type == 20)
                select new clsServiceAreaData
                {
                    departmentCode = td.department_code,
                    actionType = th.action_type,
                    actionId = th.pk_action_id,
                    year1Amount = td.year_1_amount,
                    year2Amount = td.year_2_amount,
                    year3Amount = td.year_3_amount,
                    year4Amount = td.year_4_amount,
                    alterCode = td.fk_alter_code,
                    changeId = td.fk_change_id
                }).ToList();
            if (isChapterSetUp)
            {
                var attributeData = await _finUtility.GetAttributeConnectedDepartmentsAsync(userId, budgetYear);
                dbDatasetFilter = (from a in dbDataset
                    join b in attributeData on a.departmentCode equals b.departmentCode
                    select new clsServiceAreaData //same class is used to work with exiting grid binding
                    {
                        orgId = b.attributeId,
                        orgName = b.formattedAttributeName,
                        serviceId = b.orgId,
                        serviceName = b.orgName,
                        actionType = a.actionType,
                        accountCode = a.accountCode,
                        year1Amount = a.year1Amount,
                        year2Amount = a.year2Amount,
                        year3Amount = a.year3Amount,
                        year4Amount = a.year4Amount,
                        sumCode = a.sumCode,
                        sumDescription = a.sumDescription,
                        limitCode = a.limitCode,
                        limitDescription = a.limitDescription,
                        cabFlag = a.cabFlag,
                        departmentCode = a.departmentCode,
                        budgetPhaseId = a.budgetPhaseId,
                        budgetPhaseDescription = a.budgetPhaseDescription,
                        sort_order = a.sort_order
                    }).ToList();
            }
            else if (!string.IsNullOrEmpty(orgId1))
            {
                dbDatasetFilter = (from a in dbDataset
                    join b in orgVersionContent.lstOrgHierarchy on new { a = a.departmentCode }
                        equals new { a = b.fk_department_code }
                    select new clsServiceAreaData
                    {
                        orgId = orgId1.ToLower() == "org_id_1" ? b.org_id_1 :
                            orgId1.ToLower() == "org_id_2" ? b.org_id_2 :
                            orgId1.ToLower() == "org_id_3" ? b.org_id_3 :
                            orgId1.ToLower() == "org_id_4" ? b.org_id_4 : b.org_id_1,
                        orgName = orgId1.ToLower() == "org_id_1" ? b.org_name_1 :
                            orgId1.ToLower() == "org_id_2" ? b.org_name_2 :
                            orgId1.ToLower() == "org_id_3" ? b.org_name_3 :
                            orgId1.ToLower() == "org_id_4" ? b.org_name_4 : b.org_name_1,
                        actionType = a.actionType,
                        year1Amount = a.year1Amount,
                        year2Amount = a.year2Amount,
                        year3Amount = a.year3Amount,
                        year4Amount = a.year4Amount,
                        alterCode = a.alterCode,
                        changeId = a.changeId,
                        departmentCode = a.departmentCode
                    }).ToList();

                if (!string.IsNullOrEmpty(orgId2))
                {
                    dbDatasetFilter = (from a in dbDatasetFilter
                        join b in orgVersionContent.lstOrgHierarchy on new { a = a.departmentCode }
                            equals new { a = b.fk_department_code }
                        select new clsServiceAreaData
                        {
                            orgId = a.orgId,
                            orgName = a.orgName,
                            serviceId = orgId2.ToLower() == "org_id_1" ? b.org_id_1 :
                                orgId2.ToLower() == "org_id_2" ? b.org_id_2 :
                                orgId2.ToLower() == "org_id_3" ? b.org_id_3 :
                                orgId2.ToLower() == "org_id_4" ? b.org_id_4 : b.org_id_1,
                            serviceName = orgId2.ToLower() == "org_id_1" ? b.org_name_1 :
                                orgId2.ToLower() == "org_id_2" ? b.org_name_2 :
                                orgId2.ToLower() == "org_id_3" ? b.org_name_3 :
                                orgId2.ToLower() == "org_id_4" ? b.org_name_4 : b.org_name_1,
                            actionType = a.actionType,
                            year1Amount = a.year1Amount,
                            year2Amount = a.year2Amount,
                            year3Amount = a.year3Amount,
                            year4Amount = a.year4Amount,
                            alterCode = a.alterCode,
                            changeId = a.changeId,
                            departmentCode = a.departmentCode
                        }).ToList();
                }
            }

            List<clsServiceAreaData> mainDatasetDataforServiceAreaBudgetData = (from d in dbDatasetFilter
                select new clsServiceAreaData
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    alterCode = d.alterCode,
                    changeId = d.changeId
                }).ToList();

            tco_users_settings userSettings = await budgetManagementDBContext.tco_users_settings.FirstOrDefaultAsync(x =>
                x.fk_user_id == userDetails.pk_id && x.tenant_id == userDetails.tenant_id && x.is_active_tenant);

            var dataActiveBudgetChanges = await (from t in budgetManagementDBContext.tfp_budget_changes
                where t.fk_tenant_id == userDetails.tenant_id
                      && t.budget_year == budgetYear
                      && t.status == 1
                      && t.org_budget_flag == 1
                orderby t.change_date descending
                select t).ToListAsync();

            int activeChangeId = dataActiveBudgetChanges.Count() == 0 ? -1 :
                userSettings == null ? dataActiveBudgetChanges.FirstOrDefault().pk_change_id : userSettings.active_change_id == null ? dataActiveBudgetChanges.FirstOrDefault().pk_change_id :
                dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == userSettings.active_change_id) == null ? dataActiveBudgetChanges.FirstOrDefault().pk_change_id :
                dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == userSettings.active_change_id).pk_change_id;

            var costReductionOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.isManuallyAdded == -1
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    changeId = d.changeId
                });

            if (activeChangeId != -1)
            {
                costReductionOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.changeId == activeChangeId && d.actionType == 31
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        year1Amount = d.year1Amount,
                        year2Amount = d.year2Amount,
                        year3Amount = d.year3Amount,
                        year4Amount = d.year4Amount,
                        changeId = d.changeId
                    });
            }
            else
            {
                costReductionOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.actionType == 31
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        year1Amount = 0.0M,
                        year2Amount = 0.0M,
                        year3Amount = 0.0M,
                        year4Amount = 0.0M,
                        changeId = d.changeId
                    });
            }

            var NewPrioritiesOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.isManuallyAdded == -1
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    changeId = d.changeId
                });

            if (activeChangeId != -1)
            {
                NewPrioritiesOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.changeId == activeChangeId && d.actionType == 41
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        year1Amount = d.year1Amount,
                        year2Amount = d.year2Amount,
                        year3Amount = d.year3Amount,
                        year4Amount = d.year4Amount,
                        changeId = d.changeId
                    });
            }
            else
            {
                NewPrioritiesOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.actionType == 41
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        year1Amount = 0.0M,
                        year2Amount = 0.0M,
                        year3Amount = 0.0M,
                        year4Amount = 0.0M,
                        changeId = d.changeId
                    });
            }

            var onGoingActionsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.isManuallyAdded == -1
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    changeId = d.changeId
                });

            if (activeChangeId != -1)
            {
                onGoingActionsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.changeId == activeChangeId && d.actionType == 9
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        year1Amount = d.year1Amount,
                        year2Amount = d.year2Amount,
                        year3Amount = d.year3Amount,
                        year4Amount = d.year4Amount,
                        changeId = d.changeId
                    });
            }
            else
            {
                onGoingActionsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.actionType == 9
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        year1Amount = 0.0M,
                        year2Amount = 0.0M,
                        year3Amount = 0.0M,
                        year4Amount = 0.0M,
                        changeId = d.changeId
                    });
            }
            var opeffInvestmentsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.isManuallyAdded == -1
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    changeId = d.changeId
                });

            if (activeChangeId != -1)
            {
                opeffInvestmentsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.changeId == activeChangeId && d.actionType == 21
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        year1Amount = d.year1Amount,
                        year2Amount = d.year2Amount,
                        year3Amount = d.year3Amount,
                        year4Amount = d.year4Amount,
                        changeId = d.changeId
                    });
            }
            else
            {
                opeffInvestmentsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.actionType == 21
                    select new
                    {
                        orgId = d.orgId,
                        orgName = d.orgName,
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        actionId = d.actionId,
                        year1Amount = 0.0M,
                        year2Amount = 0.0M,
                        year3Amount = 0.0M,
                        year4Amount = 0.0M,
                        changeId = d.changeId
                    });
            }

            var notAllocatedProposedCostReduction = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 30
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    changeId = d.changeId
                });

            var notAllocatedProposedNewPriorities = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 40
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    changeId = d.changeId
                });

            var notAllocatedProposedOnGoingActions = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 90
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    changeId = d.changeId
                });
            var notAllocatedopeffinvestments = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 20
                select new
                {
                    orgId = d.orgId,
                    orgName = d.orgName,
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    actionId = d.actionId,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    changeId = d.changeId
                });

            foreach (var item in jObj["jsonData"])
            {
                long limitCodeSum = 0;
                for (int i = 4; i < jObj["Fields"].Count(); i++)
                {
                    if (((string)jObj["Fields"][i]).StartsWith("limitCode"))
                    {
                        limitCodeSum = limitCodeSum + (long)item["Data"][i - 4];
                    }
                }
                for (int i = 4; i < jObj["Fields"].Count(); i++)
                {
                    if (((string)item["ServiceAreaId"]) != null)
                    {
                        if ((string)jObj["Fields"][i] == "proposedNewPriority")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000);
                                }
                                else if (yearSelected == "Year2")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000);
                                }
                                else if (yearSelected == "Year3")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000);
                                }
                                else if (yearSelected == "Year4")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000);
                                }
                                proposedNewPriorityOfRow = (long)item["Data"][i - 4];
                                propoNewprioritiesTotal = propoNewprioritiesTotal + (long)item["Data"][i - 4];
                            }
                            else
                            {
                                string parentId = Convert.ToString(item["parentId"]);
                                JArray arr = (JArray)jObj["jsonData"];
                                string level1 = (string)arr.FirstOrDefault(x => (string)x["id"] == parentId && (string)x["parentId"] == null)["ServiceAreaId"];

                                if (yearSelected == "Year1")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000);
                                }
                                else if (yearSelected == "Year2")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000);
                                }
                                else if (yearSelected == "Year3")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000);
                                }
                                else if (yearSelected == "Year4")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000);
                                }
                                proposedNewPriorityOfRow = (long)item["Data"][i - 4];
                            }
                        }
                        else if ((string)jObj["Fields"][i] == "onGoingActions")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000); }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000); }
                                onGoingActionsRow = (long)item["Data"][i - 4];
                                onGoingActionsTotal = onGoingActionsTotal + (long)item["Data"][i - 4];
                            }
                            else
                            {
                                string parentId = Convert.ToString(item["parentId"]);
                                JArray arr = (JArray)jObj["jsonData"];
                                string level1 = (string)arr.FirstOrDefault(x => (string)x["id"] == parentId && (string)x["parentId"] == null)["ServiceAreaId"];

                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000); }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000); }
                                onGoingActionsRow = (long)item["Data"][i - 4];
                            }
                        }
                        else if ((string)jObj["Fields"][i] == "operationaleffectInvestments")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000); }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000); }
                                opeffinvestmentsRow = (long)item["Data"][i - 4];
                                opeffinvestmentsTotal = opeffinvestmentsTotal + (long)item["Data"][i - 4];
                            }
                            else
                            {
                                string parentId = Convert.ToString(item["parentId"]);
                                JArray arr = (JArray)jObj["jsonData"];
                                string level1 = (string)arr.FirstOrDefault(x => (string)x["id"] == parentId && (string)x["parentId"] == null)["ServiceAreaId"];

                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000); }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000); }
                                opeffinvestmentsRow = (long)item["Data"][i - 4];
                            }
                        }

                        if ((string)jObj["Fields"][i] == "notAllocatedDataSummary")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000);
                                }
                                else if (yearSelected == "Year2")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000);
                                }
                                else if (yearSelected == "Year3")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000);
                                }
                                else if (yearSelected == "Year4")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000);
                                }

                                notAllocatedSummaryRow = (long)item["Data"][i - 4];
                                notAllocatedSummaryTotals = notAllocatedSummaryTotals + (long)item["Data"][i - 4];
                            }
                            else
                            {
                                string parentId = Convert.ToString(item["parentId"]);
                                JArray arr = (JArray)jObj["jsonData"];
                                string level1 = (string)arr.FirstOrDefault(x => (string)x["id"] == parentId && (string)x["parentId"] == null)["ServiceAreaId"];

                                if (yearSelected == "Year1")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000);
                                }
                                else if (yearSelected == "Year2")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000);
                                }
                                else if (yearSelected == "Year3")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000);
                                }
                                else if (yearSelected == "Year4")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.orgId == level1 && x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000);
                                }

                                notAllocatedSummaryRow = (long)item["Data"][i - 4];
                            }
                        }
                        else if ((string)jObj["Fields"][i] == "summary")
                        {
                            item["Data"][i - 4] = (long)item["Data"][i - 4 - 1] + limitCodeSum;
                        }
                    }
                    else
                    {
                        if ((string)jObj["Fields"][i] == "tempCostReduction")
                        {
                            item["Data"][i - 4] = tempCostReductionTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "newPriorities")
                        {
                            item["Data"][i - 4] = NewPrioritiesTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "proposedCostReduction")
                        {
                            item["Data"][i - 4] = propoCostReducTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "proposedNewPriority")
                        {
                            item["Data"][i - 4] = propoNewprioritiesTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "onGoingActions")
                        {
                            item["Data"][i - 4] = onGoingActionsTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "operationaleffectInvestments")
                        {
                            item["Data"][i - 4] = opeffinvestmentsTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "summary")
                        {
                            item["Data"][i - 4] = (long)item["Data"][i - 4 - 1] + limitCodeSum;
                        }
                        else if ((string)jObj["Fields"][i] == "notAllocatedDataSummary")
                        {
                            item["Data"][i - 4] = notAllocatedSummaryTotals;
                        }
                    }
                }

                ArrayCounter = ArrayCounter + 1;
                proposedNewPriorityOfRow = 0; opeffinvestmentsRow = 0;
            }
        }
        return Convert.ToString(jObj);
    }



    public async Task<string> GetCalculatedDataBasedOnFunctionViewNewAsync(string description, string userId, string yearSelected, int budgetYear)
    {
        TenantDBContext budgetManagementDBContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);

        string viewTypeKey = await _utility.GetParameterValueAsync(userId, "FP_SERVICE_IN_SCREEN");

        var jObj = (JObject)JsonConvert.DeserializeObject(description);
        if (jObj != null)
        {
            int ArrayCounter = 0;
            long tempCostReductionTotal = 0, NewPrioritiesTotal = 0, propoCostReducTotal = 0, propoNewprioritiesTotal = 0,
                onGoingActionsTotal = 0, opeffinvestmentsTotal = 0, notAllocatedSummaryTotals = 0;

            List<clsServiceAreaData> dbDatasetDataForServiceAreaBudgetData = null;

            if (!string.IsNullOrEmpty(viewTypeKey) && viewTypeKey.ToLower().StartsWith("service_id") && viewTypeKey.ToLower() == "service_id_1")
            {
                dbDatasetDataForServiceAreaBudgetData = await (from th in budgetManagementDBContext.tfp_trans_header
                    join td in budgetManagementDBContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                        equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                    join tf in budgetManagementDBContext.tco_service_values on new { a = td.fk_tenant_id, b = td.function_code }
                        equals new { a = tf.fk_tenant_id, b = tf.fk_function_code }
                    where th.fk_tenant_id == userDetails.tenant_id
                          && (td.budget_year == budgetYear)
                          && (th.action_type == 30 || th.action_type == 40 || th.action_type == 21 || th.action_type == 9 || th.action_type == 31 || th.action_type == 41 || th.action_type == 9 || th.action_type == 90 || th.action_type == 20)
                    select new clsServiceAreaData
                    {
                        serviceId = tf.service_id_1,
                        serviceName = tf.service_name_1,
                        actionType = th.action_type,
                        year1Amount = td.year_1_amount,
                        year2Amount = td.year_2_amount,
                        year3Amount = td.year_3_amount,
                        year4Amount = td.year_4_amount,
                        lineOrderId = th.line_order,
                        alterCode = td.fk_alter_code,
                        changeId = td.fk_change_id
                    }).ToListAsync();
            }
            else if (!string.IsNullOrEmpty(viewTypeKey) && viewTypeKey.ToLower().StartsWith("service_id") && viewTypeKey.ToLower() == "service_id_2")
            {
                dbDatasetDataForServiceAreaBudgetData = await (from th in budgetManagementDBContext.tfp_trans_header
                    join td in budgetManagementDBContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                        equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                    join tf in budgetManagementDBContext.tco_service_values on new { a = td.fk_tenant_id, b = td.function_code }
                        equals new { a = tf.fk_tenant_id, b = tf.fk_function_code }
                    where th.fk_tenant_id == userDetails.tenant_id
                          && (td.budget_year == budgetYear)
                          && (th.action_type == 30 || th.action_type == 40 || th.action_type == 21 || th.action_type == 9 || th.action_type == 31 || th.action_type == 41 || th.action_type == 9 || th.action_type == 90 || th.action_type == 20)
                    select new clsServiceAreaData
                    {
                        serviceId = tf.service_id_2,
                        serviceName = tf.service_name_2,
                        actionType = th.action_type,
                        year1Amount = td.year_1_amount,
                        year2Amount = td.year_2_amount,
                        year3Amount = td.year_3_amount,
                        year4Amount = td.year_4_amount,
                        lineOrderId = th.line_order,
                        alterCode = td.fk_alter_code,
                        changeId = td.fk_change_id
                    }).ToListAsync();
            }
            else if (!string.IsNullOrEmpty(viewTypeKey) && viewTypeKey.ToLower().StartsWith("service_id") && viewTypeKey.ToLower() == "service_id_3")
            {
                dbDatasetDataForServiceAreaBudgetData = await (from th in budgetManagementDBContext.tfp_trans_header
                    join td in budgetManagementDBContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                        equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                    join tf in budgetManagementDBContext.tco_service_values on new { a = td.fk_tenant_id, b = td.function_code }
                        equals new { a = tf.fk_tenant_id, b = tf.fk_function_code }
                    where th.fk_tenant_id == userDetails.tenant_id
                          && (td.budget_year == budgetYear)
                          && (th.action_type == 30 || th.action_type == 40 || th.action_type == 21 || th.action_type == 9 || th.action_type == 31 || th.action_type == 41 || th.action_type == 9 || th.action_type == 90 || th.action_type == 20)
                    select new clsServiceAreaData
                    {
                        serviceId = tf.service_id_3,
                        serviceName = tf.service_name_3,
                        actionType = th.action_type,
                        year1Amount = td.year_1_amount,
                        year2Amount = td.year_2_amount,
                        year3Amount = td.year_3_amount,
                        year4Amount = td.year_4_amount,
                        lineOrderId = th.line_order,
                        alterCode = td.fk_alter_code,
                        changeId = td.fk_change_id
                    }).ToListAsync();
            }
            else if (!string.IsNullOrEmpty(viewTypeKey) && viewTypeKey.ToLower().StartsWith("service_id") && viewTypeKey.ToLower() == "service_id_4")
            {
                dbDatasetDataForServiceAreaBudgetData = await (from th in budgetManagementDBContext.tfp_trans_header
                    join td in budgetManagementDBContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                        equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                    join tf in budgetManagementDBContext.tco_service_values on new { a = td.fk_tenant_id, b = td.function_code }
                        equals new { a = tf.fk_tenant_id, b = tf.fk_function_code }
                    where th.fk_tenant_id == userDetails.tenant_id
                          && (td.budget_year == budgetYear)
                          && (th.action_type == 30 || th.action_type == 40 || th.action_type == 21 || th.action_type == 9 || th.action_type == 31 || th.action_type == 41 || th.action_type == 9 || th.action_type == 90 || th.action_type == 20)
                    select new clsServiceAreaData
                    {
                        serviceId = tf.service_id_4,
                        serviceName = tf.service_name_4,
                        actionType = th.action_type,
                        year1Amount = td.year_1_amount,
                        year2Amount = td.year_2_amount,
                        year3Amount = td.year_3_amount,
                        year4Amount = td.year_4_amount,
                        lineOrderId = th.line_order,
                        alterCode = td.fk_alter_code,
                        changeId = td.fk_change_id
                    }).ToListAsync();
            }
            else if (!string.IsNullOrEmpty(viewTypeKey) && viewTypeKey.ToLower().StartsWith("service_id") && viewTypeKey.ToLower() == "service_id_5")
            {
                dbDatasetDataForServiceAreaBudgetData = await (from th in budgetManagementDBContext.tfp_trans_header
                    join td in budgetManagementDBContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                        equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                    join tf in budgetManagementDBContext.tco_service_values on new { a = td.fk_tenant_id, b = td.function_code }
                        equals new { a = tf.fk_tenant_id, b = tf.fk_function_code }
                    where th.fk_tenant_id == userDetails.tenant_id
                          && (td.budget_year == budgetYear)
                          && (th.action_type == 30 || th.action_type == 40 || th.action_type == 21 || th.action_type == 9 || th.action_type == 31 || th.action_type == 41 || th.action_type == 9 || th.action_type == 90 || th.action_type == 20)
                    select new clsServiceAreaData
                    {
                        serviceId = tf.service_id_5,
                        serviceName = tf.service_name_5,
                        actionType = th.action_type,
                        year1Amount = td.year_1_amount,
                        year2Amount = td.year_2_amount,
                        year3Amount = td.year_3_amount,
                        year4Amount = td.year_4_amount,
                        lineOrderId = th.line_order,
                        alterCode = td.fk_alter_code,
                        changeId = td.fk_change_id
                    }).ToListAsync();
            }
            else
            {
                dbDatasetDataForServiceAreaBudgetData = await (from th in budgetManagementDBContext.tfp_trans_header
                    join td in budgetManagementDBContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                        equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                    join tf in budgetManagementDBContext.tco_functions on new { a = td.fk_tenant_id, b = td.function_code }
                        equals new { a = tf.pk_tenant_id, b = tf.pk_Function_code }
                    where th.fk_tenant_id == userDetails.tenant_id
                          && (td.budget_year == budgetYear)
                          && (th.action_type == 30 || th.action_type == 40 || th.action_type == 21 || th.action_type == 9 || th.action_type == 31 || th.action_type == 41 || th.action_type == 9 || th.action_type == 90 || th.action_type == 20)
                    select new clsServiceAreaData
                    {
                        serviceId = tf.pk_Function_code,
                        serviceName = tf.display_name,
                        actionType = th.action_type,
                        year1Amount = td.year_1_amount,
                        year2Amount = td.year_2_amount,
                        year3Amount = td.year_3_amount,
                        year4Amount = td.year_4_amount,
                        lineOrderId = th.line_order,
                        alterCode = td.fk_alter_code,
                        changeId = td.fk_change_id
                    }).ToListAsync();
            }

            List<clsServiceAreaData> mainDatasetDataforServiceAreaBudgetData = (from d in dbDatasetDataForServiceAreaBudgetData
                select new clsServiceAreaData
                {
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    lineOrderId = d.lineOrderId,
                    alterCode = d.alterCode,
                    changeId = d.changeId
                }).ToList();

            tco_users_settings userSettings = budgetManagementDBContext.tco_users_settings.FirstOrDefault(x =>
                x.fk_user_id == userDetails.pk_id && x.tenant_id == userDetails.tenant_id && x.is_active_tenant);

            var dataActiveBudgetChanges = (from t in budgetManagementDBContext.tfp_budget_changes
                where t.fk_tenant_id == userDetails.tenant_id
                      && t.budget_year == budgetYear
                      && t.status == 1
                      && t.org_budget_flag == 1
                orderby t.change_date descending
                select t).ToList();

            int activeChangeId = dataActiveBudgetChanges.Any() ? GetActiveChangeId(userSettings, dataActiveBudgetChanges) : -1;

            var costReductionOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 31
                select new
                {
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    year1Amount = 0.0M,
                    year2Amount = 0.0M,
                    year3Amount = 0.0M,
                    year4Amount = 0.0M,
                    lineOrderId = d.lineOrderId,
                    changeId = d.changeId
                });

            if (activeChangeId != -1)
            {
                costReductionOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.changeId == activeChangeId && d.actionType == 31
                    select new
                    {
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        year1Amount = d.year1Amount,
                        year2Amount = d.year2Amount,
                        year3Amount = d.year3Amount,
                        year4Amount = d.year4Amount,
                        lineOrderId = d.lineOrderId,
                        changeId = d.changeId
                    });
            }

            var NewPrioritiesOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 41
                select new
                {
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    year1Amount = 0.0M,
                    year2Amount = 0.0M,
                    year3Amount = 0.0M,
                    year4Amount = 0.0M,
                    lineOrderId = d.lineOrderId,
                    changeId = d.changeId
                });

            if (activeChangeId != -1)
            {
                NewPrioritiesOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.changeId == activeChangeId && d.actionType == 41
                    select new
                    {
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        year1Amount = d.year1Amount,
                        year2Amount = d.year2Amount,
                        year3Amount = d.year3Amount,
                        year4Amount = d.year4Amount,
                        lineOrderId = d.lineOrderId,
                        changeId = d.changeId
                    });
            }

            var onGoingActionsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 9
                select new
                {
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    year1Amount = 0.0M,
                    year2Amount = 0.0M,
                    year3Amount = 0.0M,
                    year4Amount = 0.0M,
                    lineOrderId = d.lineOrderId,
                    changeId = d.changeId
                });

            if (activeChangeId != -1)
            {
                onGoingActionsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.changeId == activeChangeId && d.actionType == 9
                    select new
                    {
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        year1Amount = d.year1Amount,
                        year2Amount = d.year2Amount,
                        year3Amount = d.year3Amount,
                        year4Amount = d.year4Amount,
                        lineOrderId = d.lineOrderId,
                        changeId = d.changeId
                    });
            }

            var opeffInvestmentsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 21
                select new
                {
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    year1Amount = 0.0M,
                    year2Amount = 0.0M,
                    year3Amount = 0.0M,
                    year4Amount = 0.0M,
                    lineOrderId = d.lineOrderId,
                    changeId = d.changeId
                });

            if (activeChangeId != -1)
            {
                opeffInvestmentsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.changeId == activeChangeId && d.actionType == 21
                    select new
                    {
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        year1Amount = d.year1Amount,
                        year2Amount = d.year2Amount,
                        year3Amount = d.year3Amount,
                        year4Amount = d.year4Amount,
                        lineOrderId = d.lineOrderId,
                        changeId = d.changeId
                    });
            }

            var notAllocatedProposedCostReduction = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 30
                select new
                {
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    lineOrderId = d.lineOrderId,
                    changeId = d.changeId
                });

            var notAllocatedProposedNewPriorities = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 40
                select new
                {
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    lineOrderId = d.lineOrderId,
                    changeId = d.changeId
                });

            var notAllocatedProposedOnGoingActions = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 90
                select new
                {
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    lineOrderId = d.lineOrderId,
                    changeId = d.changeId
                });
            var notAllocatedopeffinvestments = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 20
                select new
                {
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    lineOrderId = d.lineOrderId,
                    changeId = d.changeId
                });

            foreach (var item in jObj["jsonData"])
            {
                long limitCodeSum = 0;
                for (int i = 4; i < jObj["Fields"].Count(); i++)
                {
                    if (((string)jObj["Fields"][i]).StartsWith("limitCode"))
                    {
                        limitCodeSum = limitCodeSum + (long)item["Data"][i - 4];
                    }
                }
                for (int i = 4; i < jObj["Fields"].Count(); i++)
                {
                    if (((string)item["ServiceAreaId"]) != null)
                    {
                        if ((string)jObj["Fields"][i] == "proposedNewPriority")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000);
                                }
                                else if (yearSelected == "Year2")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000);
                                }
                                else if (yearSelected == "Year3")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000);
                                }
                                else if (yearSelected == "Year4")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000);
                                }
                                propoNewprioritiesTotal = propoNewprioritiesTotal + (long)item["Data"][i - 4];
                            }
                        }
                        else if ((string)jObj["Fields"][i] == "onGoingActions")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000); }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000); }
                                onGoingActionsTotal = onGoingActionsTotal + (long)item["Data"][i - 4];
                            }
                        }
                        else if ((string)jObj["Fields"][i] == "operationaleffectInvestments" && (string)item["parentId"] == null)
                        {
                            if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000); }
                            else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000); }
                            else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000); }
                            else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000); }
                            opeffinvestmentsTotal = opeffinvestmentsTotal + (long)item["Data"][i - 4];
                        }

                        if ((string)jObj["Fields"][i] == "notAllocatedDataSummary")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000);
                                }
                                else if (yearSelected == "Year2")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000);
                                }
                                else if (yearSelected == "Year3")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000);
                                }
                                else if (yearSelected == "Year4")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000);
                                }

                                notAllocatedSummaryTotals = notAllocatedSummaryTotals + (long)item["Data"][i - 4];
                            }
                        }
                        else if ((string)jObj["Fields"][i] == "summary")
                        {
                            item["Data"][i - 4] = (long)item["Data"][i - 4 - 1] + limitCodeSum;
                        }
                    }
                    else
                    {
                        if ((string)jObj["Fields"][i] == "tempCostReduction")
                        {
                            item["Data"][i - 4] = tempCostReductionTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "newPriorities")
                        {
                            item["Data"][i - 4] = NewPrioritiesTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "proposedCostReduction")
                        {
                            item["Data"][i - 4] = propoCostReducTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "proposedNewPriority")
                        {
                            item["Data"][i - 4] = propoNewprioritiesTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "onGoingActions")
                        {
                            item["Data"][i - 4] = onGoingActionsTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "operationaleffectInvestments")
                        {
                            item["Data"][i - 4] = opeffinvestmentsTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "summary")
                        {
                            item["Data"][i - 4] = (long)item["Data"][i - 4 - 1] + limitCodeSum;
                        }
                        else if ((string)jObj["Fields"][i] == "notAllocatedDataSummary")
                        {
                            item["Data"][i - 4] = notAllocatedSummaryTotals;
                        }
                    }
                }

                ArrayCounter = ArrayCounter + 1;
            }
        }
        return Convert.ToString(jObj);
    }



    private int GetActiveChangeId(tco_users_settings userSettings, List<tfp_budget_changes> dataActiveBudgetChanges)
    {
        if (userSettings != null && userSettings.active_change_id != null)
        {
            var activeBudgetChangeInfo = dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == userSettings.active_change_id);
            int activeChangeId = activeBudgetChangeInfo == null ? dataActiveBudgetChanges.FirstOrDefault().pk_change_id : activeBudgetChangeInfo.pk_change_id;
            return activeChangeId;
        }
        else
        {
            return dataActiveBudgetChanges.FirstOrDefault().pk_change_id;
        }
    }



    public async Task<string> GetCalculatedDataBasedOnFunctionViewNewForBudgetPhase(string description, string userId, string yearSelected, int budgetYear)
    {
        TenantDBContext budgetManagementDBContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);

        string viewTypeKey = await _utility.GetParameterValueAsync(userId, "FP_SERVICE_IN_SCREEN");

        var jObj = (JObject)JsonConvert.DeserializeObject(description);
        if (jObj != null)
        {
            int ArrayCounter = 0;
            long tempCostReductionTotal = 0, NewPrioritiesTotal = 0, propoCostReducTotal = 0, propoNewprioritiesTotal = 0,
                proposedNewPriorityOfRow = 0, onGoingActionsRow = 0, onGoingActionsTotal = 0, opeffinvestmentsRow = 0, opeffinvestmentsTotal = 0, notAllocatedSummaryRow = 0, notAllocatedSummaryTotals = 0;

            List<clsServiceAreaData> dbDatasetDataForServiceAreaBudgetData = null;

            if (!string.IsNullOrEmpty(viewTypeKey) && viewTypeKey.ToLower().StartsWith("service_id") && viewTypeKey.ToLower() == "service_id_1")
            {
                dbDatasetDataForServiceAreaBudgetData = await (from th in budgetManagementDBContext.tfp_trans_header
                    join td in budgetManagementDBContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                        equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                    join tf in budgetManagementDBContext.tco_service_values on new { a = td.fk_tenant_id, b = td.function_code }
                        equals new { a = tf.fk_tenant_id, b = tf.fk_function_code }
                    where th.fk_tenant_id == userDetails.tenant_id
                          && (td.budget_year == budgetYear)
                          && (th.action_type == 30 || th.action_type == 40 || th.action_type == 21 || th.action_type == 9 || th.action_type == 31 || th.action_type == 41 || th.action_type == 9 || th.action_type == 90 || th.action_type == 20)
                    select new clsServiceAreaData
                    {
                        serviceId = tf.service_id_1,
                        serviceName = tf.service_name_1,
                        actionType = th.action_type,
                        year1Amount = td.year_1_amount,
                        year2Amount = td.year_2_amount,
                        year3Amount = td.year_3_amount,
                        year4Amount = td.year_4_amount,
                        lineOrderId = th.line_order,
                        alterCode = td.fk_alter_code,
                        changeId = td.fk_change_id
                    }).ToListAsync();
            }
            else if (!string.IsNullOrEmpty(viewTypeKey) && viewTypeKey.ToLower().StartsWith("service_id") && viewTypeKey.ToLower() == "service_id_2")
            {
                dbDatasetDataForServiceAreaBudgetData = await (from th in budgetManagementDBContext.tfp_trans_header
                    join td in budgetManagementDBContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                        equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                    join tf in budgetManagementDBContext.tco_service_values on new { a = td.fk_tenant_id, b = td.function_code }
                        equals new { a = tf.fk_tenant_id, b = tf.fk_function_code }
                    where th.fk_tenant_id == userDetails.tenant_id
                          && (td.budget_year == budgetYear)
                          && (th.action_type == 30 || th.action_type == 40 || th.action_type == 21 || th.action_type == 9 || th.action_type == 31 || th.action_type == 41 || th.action_type == 9 || th.action_type == 90 || th.action_type == 20)
                    select new clsServiceAreaData
                    {
                        serviceId = tf.service_id_2,
                        serviceName = tf.service_name_2,
                        actionType = th.action_type,
                        year1Amount = td.year_1_amount,
                        year2Amount = td.year_2_amount,
                        year3Amount = td.year_3_amount,
                        year4Amount = td.year_4_amount,
                        lineOrderId = th.line_order,
                        alterCode = td.fk_alter_code,
                        changeId = td.fk_change_id
                    }).ToListAsync();
            }
            else if (!string.IsNullOrEmpty(viewTypeKey) && viewTypeKey.ToLower().StartsWith("service_id") && viewTypeKey.ToLower() == "service_id_3")
            {
                dbDatasetDataForServiceAreaBudgetData = await (from th in budgetManagementDBContext.tfp_trans_header
                    join td in budgetManagementDBContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                        equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                    join tf in budgetManagementDBContext.tco_service_values on new { a = td.fk_tenant_id, b = td.function_code }
                        equals new { a = tf.fk_tenant_id, b = tf.fk_function_code }
                    where th.fk_tenant_id == userDetails.tenant_id
                          && (td.budget_year == budgetYear)
                          && (th.action_type == 30 || th.action_type == 40 || th.action_type == 21 || th.action_type == 9 || th.action_type == 31 || th.action_type == 41 || th.action_type == 9 || th.action_type == 90 || th.action_type == 20)
                    select new clsServiceAreaData
                    {
                        serviceId = tf.service_id_3,
                        serviceName = tf.service_name_3,
                        actionType = th.action_type,
                        year1Amount = td.year_1_amount,
                        year2Amount = td.year_2_amount,
                        year3Amount = td.year_3_amount,
                        year4Amount = td.year_4_amount,
                        lineOrderId = th.line_order,
                        alterCode = td.fk_alter_code,
                        changeId = td.fk_change_id
                    }).ToListAsync();
            }
            else if (!string.IsNullOrEmpty(viewTypeKey) && viewTypeKey.ToLower().StartsWith("service_id") && viewTypeKey.ToLower() == "service_id_4")
            {
                dbDatasetDataForServiceAreaBudgetData = await (from th in budgetManagementDBContext.tfp_trans_header
                    join td in budgetManagementDBContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                        equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                    join tf in budgetManagementDBContext.tco_service_values on new { a = td.fk_tenant_id, b = td.function_code }
                        equals new { a = tf.fk_tenant_id, b = tf.fk_function_code }
                    where th.fk_tenant_id == userDetails.tenant_id
                          && (td.budget_year == budgetYear)
                          && (th.action_type == 30 || th.action_type == 40 || th.action_type == 21 || th.action_type == 9 || th.action_type == 31 || th.action_type == 41 || th.action_type == 9 || th.action_type == 90 || th.action_type == 20)
                    select new clsServiceAreaData
                    {
                        serviceId = tf.service_id_4,
                        serviceName = tf.service_name_4,
                        actionType = th.action_type,
                        year1Amount = td.year_1_amount,
                        year2Amount = td.year_2_amount,
                        year3Amount = td.year_3_amount,
                        year4Amount = td.year_4_amount,
                        lineOrderId = th.line_order,
                        alterCode = td.fk_alter_code,
                        changeId = td.fk_change_id
                    }).ToListAsync();
            }
            else if (!string.IsNullOrEmpty(viewTypeKey) && viewTypeKey.ToLower().StartsWith("service_id") && viewTypeKey.ToLower() == "service_id_5")
            {
                dbDatasetDataForServiceAreaBudgetData = await (from th in budgetManagementDBContext.tfp_trans_header
                    join td in budgetManagementDBContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                        equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                    join tf in budgetManagementDBContext.tco_service_values on new { a = td.fk_tenant_id, b = td.function_code }
                        equals new { a = tf.fk_tenant_id, b = tf.fk_function_code }
                    where th.fk_tenant_id == userDetails.tenant_id
                          && (td.budget_year == budgetYear)
                          && (th.action_type == 30 || th.action_type == 40 || th.action_type == 21 || th.action_type == 9 || th.action_type == 31 || th.action_type == 41 || th.action_type == 9 || th.action_type == 90 || th.action_type == 20)
                    select new clsServiceAreaData
                    {
                        serviceId = tf.service_id_5,
                        serviceName = tf.service_name_5,
                        actionType = th.action_type,
                        year1Amount = td.year_1_amount,
                        year2Amount = td.year_2_amount,
                        year3Amount = td.year_3_amount,
                        year4Amount = td.year_4_amount,
                        lineOrderId = th.line_order,
                        alterCode = td.fk_alter_code,
                        changeId = td.fk_change_id
                    }).ToListAsync();
            }
            else
            {
                dbDatasetDataForServiceAreaBudgetData = await (from th in budgetManagementDBContext.tfp_trans_header
                    join td in budgetManagementDBContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                        equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                    join tf in budgetManagementDBContext.tco_functions on new { a = td.fk_tenant_id, b = td.function_code }
                        equals new { a = tf.pk_tenant_id, b = tf.pk_Function_code }
                    where th.fk_tenant_id == userDetails.tenant_id
                          && (td.budget_year == budgetYear)
                          && (th.action_type == 30 || th.action_type == 40 || th.action_type == 21 || th.action_type == 9 || th.action_type == 31 || th.action_type == 41 || th.action_type == 9 || th.action_type == 90 || th.action_type == 20)
                    select new clsServiceAreaData
                    {
                        serviceId = tf.pk_Function_code,
                        serviceName = tf.display_name,
                        actionType = th.action_type,
                        year1Amount = td.year_1_amount,
                        year2Amount = td.year_2_amount,
                        year3Amount = td.year_3_amount,
                        year4Amount = td.year_4_amount,
                        lineOrderId = th.line_order,
                        alterCode = td.fk_alter_code,
                        changeId = td.fk_change_id
                    }).ToListAsync();
            }

            List<clsServiceAreaData> mainDatasetDataforServiceAreaBudgetData = (from d in dbDatasetDataForServiceAreaBudgetData
                select new clsServiceAreaData
                {
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    lineOrderId = d.lineOrderId,
                    alterCode = d.alterCode,
                    changeId = d.changeId
                }).ToList();

            tco_users_settings userSettings = await budgetManagementDBContext.tco_users_settings.FirstOrDefaultAsync(x =>
                x.fk_user_id == userDetails.pk_id && x.tenant_id == userDetails.tenant_id && x.is_active_tenant);

            var dataActiveBudgetChanges = await (from t in budgetManagementDBContext.tfp_budget_changes
                where t.fk_tenant_id == userDetails.tenant_id
                      && t.budget_year == budgetYear
                      && t.status == 1
                      && t.org_budget_flag == 1
                orderby t.change_date descending
                select t).ToListAsync();

            int activeChangeId = dataActiveBudgetChanges.Count() == 0 ? -1 :
                userSettings == null ? dataActiveBudgetChanges.FirstOrDefault().pk_change_id : userSettings.active_change_id == null ? dataActiveBudgetChanges.FirstOrDefault().pk_change_id :
                dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == userSettings.active_change_id) == null ? dataActiveBudgetChanges.FirstOrDefault().pk_change_id :
                dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == userSettings.active_change_id).pk_change_id;

            var costReductionOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.isManuallyAdded == -1
                select new
                {
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    lineOrderId = d.lineOrderId,
                    changeId = d.changeId
                });

            if (activeChangeId != -1)
            {
                costReductionOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.changeId == activeChangeId && d.actionType == 31
                    select new
                    {
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        year1Amount = d.year1Amount,
                        year2Amount = d.year2Amount,
                        year3Amount = d.year3Amount,
                        year4Amount = d.year4Amount,
                        lineOrderId = d.lineOrderId,
                        changeId = d.changeId
                    });
            }
            else
            {
                costReductionOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.actionType == 31
                    select new
                    {
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        year1Amount = 0.0M,
                        year2Amount = 0.0M,
                        year3Amount = 0.0M,
                        year4Amount = 0.0M,
                        lineOrderId = d.lineOrderId,
                        changeId = d.changeId
                    });
            }

            var NewPrioritiesOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.isManuallyAdded == -1
                select new
                {
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    lineOrderId = d.lineOrderId,
                    changeId = d.changeId
                });

            if (activeChangeId != -1)
            {
                NewPrioritiesOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.changeId == activeChangeId && d.actionType == 41
                    select new
                    {
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        year1Amount = d.year1Amount,
                        year2Amount = d.year2Amount,
                        year3Amount = d.year3Amount,
                        year4Amount = d.year4Amount,
                        lineOrderId = d.lineOrderId,
                        changeId = d.changeId
                    });
            }
            else
            {
                NewPrioritiesOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.actionType == 41
                    select new
                    {
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        year1Amount = 0.0M,
                        year2Amount = 0.0M,
                        year3Amount = 0.0M,
                        year4Amount = 0.0M,
                        lineOrderId = d.lineOrderId,
                        changeId = d.changeId
                    });
            }

            var onGoingActionsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.isManuallyAdded == -1
                select new
                {
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    lineOrderId = d.lineOrderId,
                    changeId = d.changeId
                });

            if (activeChangeId != -1)
            {
                onGoingActionsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.changeId == activeChangeId && d.actionType == 9
                    select new
                    {
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        year1Amount = d.year1Amount,
                        year2Amount = d.year2Amount,
                        year3Amount = d.year3Amount,
                        year4Amount = d.year4Amount,
                        lineOrderId = d.lineOrderId,
                        changeId = d.changeId
                    });
            }
            else
            {
                onGoingActionsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.actionType == 9
                    select new
                    {
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        year1Amount = 0.0M,
                        year2Amount = 0.0M,
                        year3Amount = 0.0M,
                        year4Amount = 0.0M,
                        lineOrderId = d.lineOrderId,
                        changeId = d.changeId
                    });
            }
            var opeffInvestmentsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.isManuallyAdded == -1
                select new
                {
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    lineOrderId = d.lineOrderId,
                    changeId = d.changeId
                });

            if (activeChangeId != -1)
            {
                opeffInvestmentsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.changeId == activeChangeId && d.actionType == 21
                    select new
                    {
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        year1Amount = d.year1Amount,
                        year2Amount = d.year2Amount,
                        year3Amount = d.year3Amount,
                        year4Amount = d.year4Amount,
                        lineOrderId = d.lineOrderId,
                        changeId = d.changeId
                    });
            }
            else
            {
                opeffInvestmentsOnWorkFlowStatus = (from d in mainDatasetDataforServiceAreaBudgetData
                    where d.actionType == 21
                    select new
                    {
                        serviceId = d.serviceId,
                        serviceName = d.serviceName,
                        actionType = d.actionType,
                        year1Amount = 0.0M,
                        year2Amount = 0.0M,
                        year3Amount = 0.0M,
                        year4Amount = 0.0M,
                        lineOrderId = d.lineOrderId,
                        changeId = d.changeId
                    });
            }

            var notAllocatedProposedCostReduction = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 30
                select new
                {
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    lineOrderId = d.lineOrderId,
                    changeId = d.changeId
                });

            var notAllocatedProposedNewPriorities = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 40
                select new
                {
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    lineOrderId = d.lineOrderId,
                    changeId = d.changeId
                });

            var notAllocatedProposedOnGoingActions = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 90
                select new
                {
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    lineOrderId = d.lineOrderId,
                    changeId = d.changeId
                });
            var notAllocatedopeffinvestments = (from d in mainDatasetDataforServiceAreaBudgetData
                where d.actionType == 20
                select new
                {
                    serviceId = d.serviceId,
                    serviceName = d.serviceName,
                    actionType = d.actionType,
                    year1Amount = d.year1Amount,
                    year2Amount = d.year2Amount,
                    year3Amount = d.year3Amount,
                    year4Amount = d.year4Amount,
                    lineOrderId = d.lineOrderId,
                    changeId = d.changeId
                });

            foreach (var item in jObj["jsonData"])
            {
                long limitCodeSum = 0;
                for (int i = 4; i < jObj["Fields"].Count(); i++)
                {
                    if (((string)jObj["Fields"][i]).StartsWith("limitCode"))
                    {
                        limitCodeSum = limitCodeSum + (long)item["Data"][i - 4];
                    }
                }
                for (int i = 4; i < jObj["Fields"].Count(); i++)
                {
                    if (((string)item["ServiceAreaId"]) != null)
                    {
                        if ((string)jObj["Fields"][i] == "proposedNewPriority")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000);
                                }
                                else if (yearSelected == "Year2")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000);
                                }
                                else if (yearSelected == "Year3")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000);
                                }
                                else if (yearSelected == "Year4")
                                {
                                    item["Data"][i - 4] = Math.Round(((NewPrioritiesOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((costReductionOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000);
                                }
                                proposedNewPriorityOfRow = (long)item["Data"][i - 4];
                                propoNewprioritiesTotal = propoNewprioritiesTotal + (long)item["Data"][i - 4];
                            }
                        }
                        else if ((string)jObj["Fields"][i] == "onGoingActions")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000); }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((onGoingActionsOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000); }
                                onGoingActionsRow = (long)item["Data"][i - 4];
                                onGoingActionsTotal = onGoingActionsTotal + (long)item["Data"][i - 4];
                            }
                        }
                        else if ((string)jObj["Fields"][i] == "operationaleffectInvestments")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000); }
                                else if (yearSelected == "Year2") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000); }
                                else if (yearSelected == "Year3") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000); }
                                else if (yearSelected == "Year4") { item["Data"][i - 4] = Math.Round(((opeffInvestmentsOnWorkFlowStatus.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000); }
                                opeffinvestmentsRow = (long)item["Data"][i - 4];
                                opeffinvestmentsTotal = opeffinvestmentsTotal + (long)item["Data"][i - 4];
                            }
                        }

                        if ((string)jObj["Fields"][i] == "notAllocatedDataSummary")
                        {
                            if ((string)item["parentId"] == null)
                            {
                                if (yearSelected == "Year1")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year1Amount)) / 1000);
                                }
                                else if (yearSelected == "Year2")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year2Amount)) / 1000);
                                }
                                else if (yearSelected == "Year3")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year3Amount)) / 1000);
                                }
                                else if (yearSelected == "Year4")
                                {
                                    item["Data"][i - 4] = Math.Round(((notAllocatedProposedCostReduction.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedNewPriorities.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedProposedOnGoingActions.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000) +
                                                          Math.Round(((notAllocatedopeffinvestments.Where(x => x.serviceId == (string)item["ServiceAreaId"])).Sum(y => y.year4Amount)) / 1000);
                                }

                                notAllocatedSummaryRow = (long)item["Data"][i - 4];
                                notAllocatedSummaryTotals = notAllocatedSummaryTotals + (long)item["Data"][i - 4];
                            }
                        }
                        else if ((string)jObj["Fields"][i] == "summary")
                        {
                            item["Data"][i - 4] = (long)item["Data"][i - 4 - 1] + limitCodeSum;
                        }
                    }
                    else
                    {
                        if ((string)jObj["Fields"][i] == "tempCostReduction")
                        {
                            item["Data"][i - 4] = tempCostReductionTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "newPriorities")
                        {
                            item["Data"][i - 4] = NewPrioritiesTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "proposedCostReduction")
                        {
                            item["Data"][i - 4] = propoCostReducTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "proposedNewPriority")
                        {
                            item["Data"][i - 4] = propoNewprioritiesTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "onGoingActions")
                        {
                            item["Data"][i - 4] = onGoingActionsTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "operationaleffectInvestments")
                        {
                            item["Data"][i - 4] = opeffinvestmentsTotal;
                        }
                        else if ((string)jObj["Fields"][i] == "summary")
                        {
                            item["Data"][i - 4] = (long)item["Data"][i - 4 - 1] + limitCodeSum;
                        }
                        else if ((string)jObj["Fields"][i] == "notAllocatedDataSummary")
                        {
                            item["Data"][i - 4] = notAllocatedSummaryTotals;
                        }
                    }
                }

                ArrayCounter = ArrayCounter + 1;
                proposedNewPriorityOfRow = 0; opeffinvestmentsRow = 0;
            }
        }
        return Convert.ToString(jObj);
    }



    public List<DocTreeActionHelper> NonFilteredActionNames(string userId, List<int> actionType,
        int budgetYear)
    {
        return NonFilteredActionNamesAsync(userId, actionType, budgetYear).GetAwaiter().GetResult();
    }



    public async Task<List<DocTreeActionHelper>> NonFilteredActionNamesAsync(string userId, List<int> actionType, int budgetYear)
    {
        TenantDBContext DbContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        int budYear = budgetYear;
        var deltedactions = DbContext.tfp_delete_header.Where(x => x.fk_tenant_id == userDetails.tenant_id && actionType.Contains(x.action_type)).Select(x => x.fk_action_id).ToList();
        var blistactions = DbContext.tfp_temp_header.Where(x => x.fk_tenant_id == userDetails.tenant_id && actionType.Contains(x.action_type)).Select(x => x.fk_action_id).ToList();
        var deletedblist = deltedactions.Concat(blistactions).ToList();
        List<DocTreeActionHelper> dbDataset = await (from td in DbContext.tfp_trans_detail
            join th in DbContext.tfp_trans_header on td.fk_action_id equals th.pk_action_id
            where (td.fk_tenant_id == userDetails.tenant_id && th.fk_tenant_id == userDetails.tenant_id
                                                            && td.budget_year == budYear && actionType.Contains(th.action_type)
                                                            && (!deletedblist.Contains(th.pk_action_id)))
            orderby th.priority, th.description descending
            select new DocTreeActionHelper
            {
                actionId = th.pk_action_id,
                departmentCode = td.department_code,
                functionCode = td.function_code,
                description = th.description,
                priority = th.priority,
                actionType = th.action_type,
                AlterCode = td.fk_alter_code,
                BudgetYear = td.budget_year,
                Year1Amount = td.year_1_amount,
                Year2Amount = td.year_2_amount,
                Year3Amount = td.year_3_amount,
                Year4Amount = td.year_4_amount,
                changeId = td.fk_change_id
            }).ToListAsync();

        return dbDataset;
    }



    public List<DocTreeActionHelper> NonFilteredBList(string userId, List<int> actionType, int budgetYear)
    {
        TenantDBContext DbContext = _utility.GetTenantDBContext();
        UserData userDetails = _utility.GetUserDetails(userId);
        int budYear = budgetYear;
        List<DocTreeActionHelper> dbDataset = (from td in DbContext.tfp_temp_detail
            join th in DbContext.tfp_temp_header on new { a = td.fk_tenant_id, b = td.fk_temp_id }
                equals new { a = th.fk_tenant_id, b = th.pk_temp_id }
            where (td.fk_tenant_id == userDetails.tenant_id && th.fk_tenant_id == userDetails.tenant_id && td.budget_year == budYear && actionType.Contains(th.action_type)
                   && !th.is_parked_action && (td.year_1_amount != 0 || td.year_2_amount != 0 || td.year_3_amount != 0 || td.year_4_amount != 0))
            orderby th.priority, th.description descending
            select new DocTreeActionHelper
            {
                actionId = th.pk_temp_id,
                departmentCode = td.department_code,
                functionCode = td.function_code,
                description = th.description,
                priority = th.priority,
                actionType = th.action_type,
                syncStatus = th.sync_status
            }).Distinct().ToList();
        return dbDataset;
    }



    public List<DocTreeActionHelper> NonFilteredDeletedAction(string userId, List<int> actionType, int budgetYear)
    {
        return NonFilteredDeletedActionAsync(userId, actionType, budgetYear).GetAwaiter().GetResult();
    }



    public async Task<List<DocTreeActionHelper>> NonFilteredDeletedActionAsync(string userId, List<int> actionType, int budgetYear)
    {
        TenantDBContext DbContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        int budYear = budgetYear;
        List<DocTreeActionHelper> dbDataset = await (from td in DbContext.tfp_delete_detail
            join th in DbContext.tfp_delete_header on new { a = td.fk_tenant_id, b = td.fk_action_id }
                equals new { a = th.fk_tenant_id, b = th.pk_action_id }
            where (td.fk_tenant_id == userDetails.tenant_id && th.fk_tenant_id == userDetails.tenant_id && td.budget_year == budYear && actionType.Contains(th.action_type))
            orderby th.priority, th.description descending
            select new DocTreeActionHelper
            {
                actionId = th.pk_action_id,
                departmentCode = td.department_code,
                functionCode = td.function_code,
                description = th.description,
                priority = th.priority,
                actionType = th.action_type
            }).Distinct().ToListAsync();
        return dbDataset;
    }

}