#pragma warning disable CS8629
#pragma warning disable CS8600
#pragma warning disable CS8602
#pragma warning disable CS8603
#pragma warning disable CS8604

using Azure.Storage;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Framsikt.BL.Core;
using Framsikt.BL.Core.Helpers;
using Framsikt.BL.Helpers;
using Framsikt.Entities;
using HtmlAgilityPack;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using System.Globalization;

namespace Framsikt.BL
{
    public class HelpCenter : IHelpCenter
    {
        private readonly IUtility _utility;
        private readonly IHelpcenterSearch _HSerach;
        private readonly BlobServiceClient _blobServiceClient;

        public HelpCenter(IUtility util, IHelpcenterSearch hsearch, IAzureStorageAccountClient azureStorageAccountClient)
        {
            _utility = util ?? throw new ArgumentNullException(nameof(util));
            _HSerach = hsearch ?? throw new ArgumentNullException(nameof(hsearch));
            _blobServiceClient = azureStorageAccountClient.CreateBlobServiceAppClient();
        }

        public List<KeyValue> userRoles(string userId)
        {
            return userRolesAsync(userId).GetAwaiter().GetResult();
        }
        public async Task<List<KeyValue>> userRolesAsync(string userId)
        {
            AuthenticationDBContext authDbContext = _utility.GetAuthenticationContext();
            List<KeyValue> roles = new List<KeyValue>();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringValuesUserAdmin = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "LangStringAdmin");
            var user_roles = await (from ar in authDbContext.gmd_auth_roles
                              select new
                              {
                                  roleId = ar.pk_id,
                                  rolelangstringId = ar.fk_langstring_id,
                              }).OrderBy(x => x.roleId).ToListAsync();

            var roleflag = await authDbContext.tco_auth_user_role_mapping.AnyAsync(t => t.fk_user_id == userDetails.pk_id && t.tenant_id == userDetails.tenant_id && t.fk_role_id == 1);
            if (!roleflag)
            {
                user_roles.RemoveAll(x => x.roleId == 1);
            }

            foreach (var keyvalue in user_roles)
            {
                KeyValue roleitems = new KeyValue()
                {
                    Key = keyvalue.roleId.ToString(),
                    Value = keyvalue.roleId.ToString() + " " + ((langStringValuesUserAdmin.FirstOrDefault(v => v.Key == keyvalue.rolelangstringId)).Value).LangText
                };
                roles.Add(roleitems);
            }
            return roles;
        }

        public List<KeyValueInt> GetMainProcess()
        {
            return GetMainProcessAsync().GetAwaiter().GetResult();
        }

        public async Task<List<KeyValueInt>> GetMainProcessAsync()
        {
            HelpCenterDBContext helpDbContext = _utility.GetHelpCenterContext();
            var mainProcess = await (from mp in helpDbContext.ghc_main_process
                                     select new KeyValueInt
                                     {
                                         Key = mp.pk_id,
                                         Value = mp.process_name
                                     }).ToListAsync();
            return mainProcess.OrderBy(x => x.Value, StringComparer.Create(CultureInfoFactory.CreateCulture("nb-NO"), false)).ToList();
        }

        public List<KeyValueInt> GetSubProcess(int mainProcessId)
        {
            return GetSubProcessAsync(mainProcessId).GetAwaiter().GetResult();
        }

        public async Task<List<KeyValueInt>> GetSubProcessAsync(int mainProcessId)
        {
            HelpCenterDBContext helpDbContext = _utility.GetHelpCenterContext();
            var subProcess = await (from sp in helpDbContext.ghc_sub_process
                                    where sp.fk_mainprocess_id == mainProcessId
                                    select new KeyValueInt
                                    {
                                        Key = sp.pk_id,
                                        Value = sp.process_name
                                    }).ToListAsync();
            return subProcess.OrderBy(x => x.Value, StringComparer.Create(CultureInfoFactory.CreateCulture("nb-NO"), false)).ToList();
        }

        public async Task<List<KeyValueInt>> GetScreenIds()
        {
            HelpCenterDBContext helpDbContext = _utility.GetHelpCenterContext();
            var screenIds = await (from sc in helpDbContext.ghc_screen_info
                select new KeyValueInt
                {
                    Key = sc.pk_id,
                    Value = sc.sub_module_id
                }).ToListAsync();
            return screenIds;
        }

        public string SaveContent(HelpcenterHelper inputData, string userId)
        {
            return SaveContentAsync(inputData, userId).GetAwaiter().GetResult();
        }

        public async Task<string> SaveContentAsync(HelpcenterHelper inputData, string userId)
        {
            HelpCenterDBContext helpDbContext = _utility.GetHelpCenterContext();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            ghc_content_header contentHeader;
            //validate master id
            string valid = await ValidateMasterIdAsync(inputData.masterId, userDetails, inputData.tenantIds, inputData.contentId);

            if (!string.IsNullOrEmpty(valid)) return valid;

            if (inputData.contentId == 0)
            {
                contentHeader = await InsertHelpcenterContentAsync(inputData, userId, userDetails, helpDbContext);
            }
            else
            {
                contentHeader = await UpdateHelpcenterContentAsync(inputData, userId, userDetails, helpDbContext);
            }

            //update tenant content if any
            await UpdateTenantContentAsync(contentHeader.pk_id, inputData.tenantIds, inputData.masterId, userDetails, helpDbContext);

            //Save sort order for selected screen Ids for a given content

            await SaveContentScreenSortingAsync(userId, inputData, helpDbContext);

            await _HSerach.PopulateIndexForHelpcenterAsync("helpcenterindex", contentHeader != null ? contentHeader.pk_id : 0, userId);
            return valid;
        }

        private async Task<ghc_content_header> UpdateHelpcenterContentAsync(HelpcenterHelper inputData, string userId, UserData userDetails, HelpCenterDBContext helpDbContext)
        {
            ghc_content_header contentHeader = await helpDbContext.ghc_content_header.FirstOrDefaultAsync(x => x.pk_id == inputData.contentId);
            if (contentHeader != null)
            {
                contentHeader.content_name = inputData.contentName;
                contentHeader.content_type = inputData.contentType;
                contentHeader.description = inputData.description;
                contentHeader.fk_role_id = String.Join(",", inputData.roles);
                contentHeader.fk_screen_id = String.Join(",", inputData.screenId);
                contentHeader.fk_tab_id = String.Join(",", inputData.tabIds);
                contentHeader.is_active = inputData.isActive;
                contentHeader.is_shortlist = inputData.isShortlist;
                contentHeader.most_popular = inputData.mostPopular;
                contentHeader.sort_order = inputData.sortOrder;
                contentHeader.media_file_id = inputData.mediaFileId;
                contentHeader.fk_module_id = string.Join(",", inputData.moduleIds);
                if (inputData.isUpdateTs)
                {
                    DateTime trackUpdateTs;
                    bool validDate = DateTime.TryParseExact(inputData.updatedTs, "dd'.'MM'.'yyyy", DateTimeFormatInfo.InvariantInfo, DateTimeStyles.None, out trackUpdateTs);
                    contentHeader.track_updated = !string.IsNullOrEmpty(inputData.updatedTs) && validDate ? trackUpdateTs : DateTime.UtcNow;
                }
                contentHeader.updated = DateTime.UtcNow;
                contentHeader.updated_by = userDetails.pk_id;

                int mainProcessId = 0;
                if (inputData.mainProcessId.Any() && inputData.mainProcessId.FirstOrDefault().Key == 0)
                {
                    mainProcessId = await CreateMainProcessAsync(inputData.mainProcessId.FirstOrDefault().Value, userId);
                    contentHeader.fk_main_process = mainProcessId;
                }
                else
                {
                    contentHeader.fk_main_process = inputData.mainProcessId.FirstOrDefault().Key;
                }
                if (inputData.subProcessId.Any() && inputData.subProcessId.FirstOrDefault().Key == 0)
                {
                    contentHeader.fk_sub_process = await CreateSubProcessAsync(inputData.subProcessId.FirstOrDefault().Value, userId, mainProcessId);
                }
                else
                {
                    contentHeader.fk_sub_process = inputData.subProcessId.FirstOrDefault().Key;
                }

                await helpDbContext.SaveChangesAsync();
                await _utility.SaveTextLogAsync(userId, contentHeader.descriptionId, contentHeader.description, null);
            }

            return contentHeader;
        }

        private async Task<ghc_content_header> InsertHelpcenterContentAsync(HelpcenterHelper inputData, string userId, UserData userDetails, HelpCenterDBContext helpDbContext)
        {
            ghc_content_header contentHeader = new ghc_content_header
            {
                content_name = inputData.contentName,
                content_type = inputData.contentType,
                description = inputData.description,
                descriptionId = Guid.NewGuid(),
                fk_role_id = inputData.roles != null ? String.Join(",", inputData.roles) : string.Empty,
                fk_screen_id = inputData.screenId != null ? String.Join(",", inputData.screenId) : string.Empty,
                fk_tab_id = inputData.tabIds != null ? String.Join(",", inputData.tabIds) : string.Empty,
                is_active = inputData.isActive,
                is_shortlist = inputData.isShortlist,
                most_popular = inputData.mostPopular,
                sort_order = inputData.sortOrder,
                media_file_id = inputData.mediaFileId,
                updated = DateTime.UtcNow,
                track_updated = DateTime.UtcNow,
                fk_module_id = inputData.moduleIds != null ? string.Join(",", inputData.moduleIds) : string.Empty,
                updated_by = userDetails.pk_id
            };
            int mainProcessId = 0;
            if (inputData.mainProcessId.Any() && inputData.mainProcessId.FirstOrDefault().Key == 0)
            {
                mainProcessId = await CreateMainProcessAsync(inputData.mainProcessId.FirstOrDefault().Value, userId);
                contentHeader.fk_main_process = mainProcessId;
            }
            else
            {
                contentHeader.fk_main_process = inputData.mainProcessId.FirstOrDefault().Key;
            }
            if (inputData.subProcessId.Any() && inputData.subProcessId.FirstOrDefault().Key == 0)
            {
                contentHeader.fk_sub_process = await CreateSubProcessAsync(inputData.subProcessId.FirstOrDefault().Value, userId, mainProcessId);
            }
            else
            {
                contentHeader.fk_sub_process = inputData.subProcessId.FirstOrDefault().Key;
            }
            helpDbContext.ghc_content_header.Add(contentHeader);
            await helpDbContext.SaveChangesAsync();
            inputData.contentId = contentHeader.pk_id;
            await _utility.SaveTextLogAsync(userId, contentHeader.descriptionId, contentHeader.description, null);
            return contentHeader;
        }

        public async Task SaveContentScreenSortingAsync(string userId, HelpcenterHelper inputData, HelpCenterDBContext helpDbContext)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            //using (HelpCenterDBContext helpDbContext = _utility.GetHelpCenterContext())
            //{
                //Save sort order for selected screen Ids for given content.
            var existingScreenSortingData = await helpDbContext.ghc_content_screen_sorting.Where(x => x.fk_content_id == inputData.contentId).ToListAsync();
            if (existingScreenSortingData.Any())
            {
                helpDbContext.ghc_content_screen_sorting.RemoveRange(existingScreenSortingData);
                await helpDbContext.SaveChangesAsync();
            }
            if (inputData.selectedScreenIdsInfo != null)
            {
                foreach (var a in inputData.selectedScreenIdsInfo)
                {
                    ghc_content_screen_sorting newContentSortingData = new ghc_content_screen_sorting();
                    newContentSortingData.fk_content_id = inputData.contentId;
                    newContentSortingData.fk_screen_id = a.Id.ToString();
                    newContentSortingData.sort_order = a.Key;
                    newContentSortingData.updated = DateTime.UtcNow;
                    newContentSortingData.updated_by = userDetails.pk_id;

                    helpDbContext.ghc_content_screen_sorting.Add(newContentSortingData);
                }
                await helpDbContext.SaveChangesAsync();
            }
            //}
        }

        private async Task<int> CreateMainProcessAsync(string processName, string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            HelpCenterDBContext helpDbContext = _utility.GetHelpCenterContext();
            ghc_main_process mp = new ghc_main_process
            {
                process_name = processName,
                updated = DateTime.UtcNow,
                updated_by = userDetails.pk_id
            };
            helpDbContext.ghc_main_process.Add(mp);
            await helpDbContext.SaveChangesAsync();
            return mp.pk_id;
        }
        private async Task<int> CreateSubProcessAsync(string processName, string userId, int mainProcessId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            HelpCenterDBContext helpDbContext = _utility.GetHelpCenterContext();
            ghc_sub_process sp = new ghc_sub_process
            {
                process_name = processName,
                fk_mainprocess_id = mainProcessId,
                updated = DateTime.UtcNow,
                updated_by = userDetails.pk_id
            };
            helpDbContext.ghc_sub_process.Add(sp);
            await helpDbContext.SaveChangesAsync();
            return sp.pk_id;
        }
        public async Task<dynamic> GetHelpcenterGridDataAsync(ContentFilter filter)
        {
            HelpCenterDBContext helpDbContext = _utility.GetHelpCenterContext();
            List<ContentHelper> helpCenterData = new List<ContentHelper>();
            List<string> search = new List<string>();
            dynamic gridData = new JObject();
            if (!string.IsNullOrEmpty(filter.contentName))
            {
                search.AddRange(filter.contentName.Split(' ').Where(y => !string.IsNullOrEmpty(y)).Select(x=>x.ToLower()).ToList());
            }

            var helpcenterContent = await (from ch in helpDbContext.ghc_content_header
                                           select new
                                           {
                                               ch.pk_id,
                                               ch.content_name,
                                               ch.content_type,
                                               ch.fk_main_process,
                                               ch.fk_sub_process,
                                               ch.fk_screen_id,
                                               ch.fk_tab_id,
                                               ch.fk_role_id,
                                               ch.is_active,
                                               ch.is_shortlist,
                                               ch.sort_order,
                                               ch.most_popular,
                                               ch.description,
                                               ch.descriptionId,
                                               ch.media_file_id,
                                               ch.fk_module_id,
                                               ch.track_updated
                                           }).ToListAsync();
            if (search.Any())
            {
                helpcenterContent = (from hc in helpcenterContent
                                     where search.All(s => hc.content_name.ToLower().Contains(s))
                                     select hc).ToList();
            }

            //Get screenIds info
            List<string> screenIdsFromBaseData = new List<string>();
            foreach (var a in helpcenterContent.Distinct())
            {
                screenIdsFromBaseData.AddRange(a.fk_screen_id.Split(',').ToList());
            }
            screenIdsFromBaseData = screenIdsFromBaseData.Distinct().ToList();
            var screenIdsInfo = await (from a in helpDbContext.ghc_screen_info
                                    join b in helpDbContext.ghc_content_screen_sorting on a.pk_id.ToString() equals b.fk_screen_id into res1
                                    from res in res1.DefaultIfEmpty()
                                    where screenIdsFromBaseData.Contains(a.pk_id.ToString())
                                    select new
                                    {
                                        sortOrder = res != null ? res.sort_order : 0,
                                        screenId = a.pk_id,
                                        screenDesc = a.sub_module_id,
                                        contentId = res != null ? res.fk_content_id : 0
                                    }).Distinct().AsNoTracking().ToListAsync();

            //get all tenant content information 
            var contentIds = helpcenterContent.Select(x => x.pk_id).ToList();
            var tenantMapping = await GetMasterContentData(contentIds);

            foreach (var item in helpcenterContent)
            {
                ContentHelper main = new ContentHelper
                {
                    contentId = item.pk_id,
                    contentName = item.content_name,
                };
                dynamic fileInfo = new JObject();
                List<dynamic> mediaFileInfo = new List<dynamic>();
                if (item.media_file_id != 0)
                {
                    var mediaFile = await helpDbContext.ghc_media_files.FirstOrDefaultAsync(x => x.pk_id == item.media_file_id);
                    if (mediaFile != null)
                    {
                        fileInfo.name = mediaFile.display_name;
                        fileInfo.size = string.Empty;
                        fileInfo.id = mediaFile.pk_id;
                        fileInfo.extension = mediaFile.media_location.Substring(mediaFile.media_location.LastIndexOf('.') + 1);
                        mediaFileInfo.Add(fileInfo);
                    }
                }

                //Get description and sort ortder for selected screen IDs.
                List<ScreenIdsInfoHelper> selectedScreenIdsInfo = new List<ScreenIdsInfoHelper>();
                if (screenIdsInfo != null)
                {
                    foreach (var id in item.fk_screen_id.Split(',').ToList())
                    {
                        if (!string.IsNullOrEmpty(id))
                        {
                            var screenInfo = screenIdsInfo.FirstOrDefault(x => x.screenId.ToString() == id && x.contentId == item.pk_id);
                            ScreenIdsInfoHelper screenIdInfObj = new ScreenIdsInfoHelper
                            {
                                Key = screenInfo != null ? screenInfo.sortOrder : 0,
                                Value = screenInfo != null ? screenInfo.screenDesc : string.Empty,
                                Id = screenInfo != null ? screenInfo.screenId : 0
                            };
                            selectedScreenIdsInfo.Add(screenIdInfObj);
                        }
                    }
                }

                ContentSubItemHelper subItem = new ContentSubItemHelper
                {
                    contentName = item.content_name,
                    contentType = item.content_type,
                    isActive = item.is_active,
                    isShortlist = item.is_shortlist,
                    updatedTs = item.track_updated.ToString("dd'.'MM'.'yyyy", CultureInfo.InvariantCulture),
                    mainProcessId = new List<int> { item.fk_main_process },
                    subProcessId = new List<int> { item.fk_sub_process },
                    mostPopular = item.most_popular,
                    roles = item.fk_role_id.Split(',').ToList(),
                    screenId = item.fk_screen_id.Split(',').ToList(),
                    sortOrder = item.sort_order,
                    descriptionId = item.descriptionId,
                    mediaFileName = mediaFileInfo,
                    selectedScreenIdsInfo = selectedScreenIdsInfo,
                    moduleIds = item.fk_module_id.Split(',').ToList(),
                    tabIds = item.fk_tab_id.Split(',').ToList(),
                    description = item.description
                };

                //tenant content information

                if (tenantMapping.Any(x => x.fk_content_id == item.pk_id))
                {
                    subItem.tenantIds.AddRange(tenantMapping.Where(y => y.fk_content_id == item.pk_id).Select(x => x.fk_tenant_id).Distinct().ToList());
                    subItem.masterId = tenantMapping.FirstOrDefault(y => y.fk_content_id == item.pk_id).master_content_id;
                }

                main.subItem.Add(subItem);
                helpCenterData.Add(main);

            }
            gridData.dataCount = helpCenterData.Count;
            dynamic hcData = JArray.FromObject(helpCenterData.Skip(filter.skip).Take(filter.take).ToList());
            gridData.helpCenterData = hcData;
            return gridData;
        }
        public async Task DeleteContentAsync(int contentId, string userId)
        {
            HelpCenterDBContext helpDbContext = _utility.GetHelpCenterContext();
            if (await helpDbContext.ghc_content_header.FirstOrDefaultAsync(x => x.pk_id == contentId) != null)
            {
                //Delete screenIds and sorting data which linked to given contentId
                var existingScreenSortingData = await helpDbContext.ghc_content_screen_sorting.Where(x => x.fk_content_id == contentId).ToListAsync();
                if (existingScreenSortingData.Any())
                {
                    helpDbContext.ghc_content_screen_sorting.RemoveRange(existingScreenSortingData);
                    await helpDbContext.SaveChangesAsync();
                }

                helpDbContext.ghc_content_header.Remove(await helpDbContext.ghc_content_header.FirstOrDefaultAsync(x => x.pk_id == contentId));
                await helpDbContext.SaveChangesAsync();

                //update tenant mapping table
                if (await helpDbContext.thc_tenant_content.AnyAsync(x => x.master_content_id == contentId))
                {
                    var tenantContent = await helpDbContext.thc_tenant_content.Where(x => x.master_content_id == contentId).ToListAsync();
                    foreach (var item in tenantContent)
                    {
                        item.master_content_id = null;
                    }
                    await helpDbContext.SaveChangesAsync();
                }
                if (await helpDbContext.thc_tenant_content.AnyAsync(x => x.fk_content_id == contentId))
                {
                    helpDbContext.thc_tenant_content.RemoveRange(await helpDbContext.thc_tenant_content.Where(x => x.fk_content_id == contentId).ToListAsync());
                    await helpDbContext.SaveChangesAsync();
                }
                await _HSerach.PopulateIndexForHelpcenterAsync("helpcenterindex", contentId, userId);
            }
        }
        public async Task< dynamic> UploadVideoAsync(IFormFile fileupload, string userId)
        {
            TimeSpan backOffPeriod = TimeSpan.FromSeconds(2);
            UserData userdetails = await _utility.GetUserDetailsAsync(userId);
            
            string newFileName = Guid.NewGuid().ToString() + Path.GetExtension(fileupload.FileName);
            
            var blobContainer = _blobServiceClient.GetBlobContainerClient("helpcentervideos");
            
            if (!(await blobContainer.ExistsAsync()))
            {
                await blobContainer.CreateAsync();
            }            

            await blobContainer.SetAccessPolicyAsync(Azure.Storage.Blobs.Models.PublicAccessType.None);

            BlobClient blob = blobContainer.GetBlobClient(newFileName.ToLower());
            
            BlobUploadOptions options = new BlobUploadOptions
            {
                TransferOptions = new StorageTransferOptions
                {
                    // Set the maximum number of workers that 
                    // may be used in a parallel transfer.
                    MaximumConcurrency = 8,

                    // Set the maximum length of a transfer to 50MB.
                    MaximumTransferSize = 256 * 1024
                }
            };

            using (var fileStream = fileupload.OpenReadStream())
            {
                if (await blob.ExistsAsync())
                {
                    await blob.DeleteAsync();
                    await blob.UploadAsync(fileStream, options);
                }
                else
                {
                    await blob.UploadAsync(fileStream, options);
                }
            }

            //create the an entry in DB
            HelpCenterDBContext helpDbContext = _utility.GetHelpCenterContext();
            ghc_media_files gmf = new ghc_media_files
            {
                media_location = newFileName,
                display_name = fileupload.FileName,
                updated = DateTime.UtcNow,
                updated_by = userdetails.pk_id
            };
            helpDbContext.ghc_media_files.Add(gmf);
            await helpDbContext.SaveChangesAsync();

            dynamic fileInfo = new JObject();
            fileInfo.name = gmf.display_name;
            fileInfo.size = string.Empty;
            fileInfo.extension = Path.GetExtension(fileupload.FileName);
            fileInfo.id = gmf.pk_id;

            return fileInfo;
        }

        public async Task<JObject> GetCategoryWiseDataAsync(string userId, string moduleName, string RequestType, string moduleId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "HelpCenter");
            HelpCenterDBContext helpDbContext = _utility.GetHelpCenterContext();
            var allContent = await (from ch in helpDbContext.ghc_content_header
                                join mp in helpDbContext.ghc_main_process on ch.fk_main_process equals mp.pk_id
                                join sp in helpDbContext.ghc_sub_process on ch.fk_sub_process equals sp.pk_id
                                where ch.is_active
                                orderby ch.sort_order
                                select new
                                {
                                    ch.pk_id,
                                    ch.content_name,
                                    ch.sort_order,
                                    ch.fk_role_id,
                                    main_process = mp.process_name,
                                    sub_process = sp.process_name,
                                    ch.is_shortlist,
                                    ch.fk_module_id,
                                    mainProcessSortOrder = mp.sort_order,
                                    subprocessSortOrder = sp.sort_order.Value
                                }).AsNoTracking().ToListAsync();

            //tenant specific content
            var tenantContent = await _HSerach.GetAllTenantContentAsync();
            //master data connected content
            var mstrDataReplacement = tenantContent.Where(x => x.master_content_id != null && x.fk_tenant_id == userDetails.tenant_id).Select(y => y.master_content_id).ToList();
            var mstrContent = allContent.Where(x => mstrDataReplacement.Contains(x.pk_id)).ToList();
            //remove master data connected content
            var getAllContent = allContent.Except(mstrContent);
            //own tenant data
            var ownTenantContent = tenantContent.Where(x => x.fk_tenant_id == userDetails.tenant_id).Select(x => x.fk_content_id).ToList();
            //remove other tenant data
            var otherTenantContent = tenantContent.Where(x => x.fk_tenant_id != userDetails.tenant_id && !ownTenantContent.Contains(x.fk_content_id)).Select(x => x.fk_content_id).ToList();

            getAllContent = getAllContent.Where(x => !otherTenantContent.Contains(x.pk_id)).ToList();


            //Filter base data based on module ID
            if (!string.IsNullOrEmpty(moduleId))
            {
                getAllContent = getAllContent.Where(x => x.fk_module_id.Contains(moduleId)).ToList();
                List<int> contentIds = new List<int>();
                foreach (var c in getAllContent)
                {
                    List<string> moduleIds = new List<string>();
                    foreach (var m in c.fk_module_id.Split(',').ToList())
                    {
                        if (m == moduleId)
                            moduleIds.Add(m);
                    }
                    if (moduleIds.Any())
                    {
                        contentIds.Add(c.pk_id);
                    }
                }
                getAllContent = getAllContent.Where(x => contentIds.Contains(x.pk_id)).ToList(); //Filtered data
            }

            switch (RequestType)
            {
                case "subProcess":
                    getAllContent = getAllContent.Where(x => x.sub_process == moduleName).ToList();
                    break;
                case "mainProcess":
                    getAllContent = getAllContent.Where(x => x.main_process == moduleName).ToList();
                    //setting the variable value to subProcess to get all the subprocess connected to main(view all option)
                    RequestType = "subProcess";
                    break;
                default:
                    getAllContent = getAllContent.Where(x => x.is_shortlist).ToList();
                    break;
            }

            IEnumerable<int> roles = await _utility.GetUserRoleIdsAsync(userId);
            List<int> contentId = new List<int>();
            foreach (var item in getAllContent.Where(x => x.fk_role_id != null).ToList())
            {
                if (string.IsNullOrEmpty(item.fk_role_id))
                {
                    continue;
                }

                List<int> mappedRoleIds = item.fk_role_id.Split(',').Select(int.Parse).ToList();
                foreach (var roleId in mappedRoleIds)
                {
                    if (roles.Contains(roleId))
                    {
                        contentId.Add(item.pk_id);
                        break;
                    }
                }
            }
            getAllContent = getAllContent.Where(x => contentId.Contains(x.pk_id)).OrderBy(x => x.mainProcessSortOrder).ToList();

            List<string> mainProcess = getAllContent.Select(x => x.main_process).Distinct().ToList();
            dynamic fullData = new JObject();
            dynamic hData = new JArray();
            foreach (var item in mainProcess)
            {
                dynamic data = new JArray();

                dynamic mainData = new JObject();
                mainData.header = item;

                List<string> subprocessConnected = getAllContent.Where(x => x.main_process == item).OrderBy(x => x.subprocessSortOrder).ThenByDescending(x => x.pk_id).Select(x => x.sub_process).ToList();

                foreach (var subPro in subprocessConnected.Distinct().ToList())
                {
                    //Order contents by sortOrder first #72979
                    var connectedContent = getAllContent.Where(x => x.sub_process == subPro && x.main_process == item).OrderBy(x => x.sort_order).ThenBy(x => x.subprocessSortOrder).ThenByDescending(x => x.pk_id).ToList();
                    dynamic content = new JObject();
                    content.key = subPro;
                    content.value = subPro;
                    content.title = RequestType == "subProcess";
                    content.isContent = false;
                    data.Add(content);
                    if (RequestType == "subProcess")
                    {
                        foreach (var cContent in connectedContent)
                        {
                            content = new JObject();
                            content.key = cContent.pk_id;
                            content.value = cContent.content_name;
                            content.title = false;
                            content.isContent = true;
                            content.isTenantContent = tenantContent.Any(x => x.fk_tenant_id == userDetails.tenant_id && x.fk_content_id == cContent.pk_id);
                            data.Add(content);

                        }
                    }

                }
                mainData.data = data;

                dynamic viewObj = new JObject();
                viewObj.key = item;
                viewObj.value = langString["HC_View_All"].LangText;
                mainData.view = viewObj;
                hData.Add(mainData);
            }
            dynamic autoComplete = new JArray();
            autoComplete.Add(getAllContent.Select(x => x.content_name).Distinct().ToList());
            fullData.hData = hData;
            fullData.autoComplete = autoComplete;
            return fullData;
        }

        public async Task<JObject> GetContentAsync(int contentId, int tenantId)
        {
            dynamic content = new JObject();
            HelpCenterDBContext helpDbContext = _utility.GetHelpCenterContext();
            var data = await (from ch in helpDbContext.ghc_content_header
                        join mp in helpDbContext.ghc_main_process on ch.fk_main_process equals mp.pk_id
                        join sp in helpDbContext.ghc_sub_process on ch.fk_sub_process equals sp.pk_id
                        where ch.pk_id == contentId
                        select new
                        {
                            ch.pk_id,
                            ch.content_name,
                            ch.description,
                            ch.track_updated,
                            main_process = mp.process_name,
                            sub_process = sp.process_name
                        }).ToListAsync();

            if (data.Any())
            {
                //tenant specific content
                var tenantContent = await _HSerach.GetAllTenantContentAsync();

                content.key = contentId;
                string description = data.FirstOrDefault().description;
                if (data.FirstOrDefault().description.Contains("videodetector"))
                {
                    var htmlDoc = new HtmlDocument();
                    htmlDoc.LoadHtml(data.FirstOrDefault().description);
                    var htmlNodes = htmlDoc.DocumentNode.SelectNodes("//iframe");
                    if (htmlNodes != null)
                    {
                        htmlNodes[0].Attributes.Add("style", "height:461px;width:854px");
                        htmlNodes[0].Attributes.Add("allowfullscreen", "true");
                    }
                    description = htmlDoc.DocumentNode.InnerHtml.ToString();
                }
                content.value = description;
                Uri validUrl;
                bool success = Uri.TryCreate("", UriKind.RelativeOrAbsolute, out validUrl);
                if (success)
                {
                    string domainName = "<a href='" + validUrl.ToString() + "/HelpCenter/HelpCenterDetailContent?";
                    content.value = description.Replace("&lt;FramsiktInternalLink&gt;", domainName).Replace("&lt;/FramsiktInternalLink&gt;", "'>").Replace("&lt;FramsiktInternalLinkName&gt;", "")
                                    .Replace("&lt;/FramsiktInternalLinkName&gt;", "</a>");
                }
                content.breadCrumbL1 = data.FirstOrDefault().main_process;
                content.breadCrumbL2 = data.FirstOrDefault().sub_process;
                content.breadCrumbL3 = data.FirstOrDefault().content_name;
                content.updated = data.FirstOrDefault().track_updated.ToString("dd'.'MM'.'yyyy", CultureInfo.InvariantCulture);
                content.isTenantContent = tenantContent.Any(x => x.fk_tenant_id == tenantId && x.fk_content_id == contentId);

            }
            else
            {
                content.key = contentId;
                content.value = string.Empty;
                content.breadCrumbL1 = string.Empty;
                content.breadCrumbL2 = string.Empty;
                content.breadCrumbL3 = string.Empty;
                content.updated = string.Empty;
                content.isTenantContent = false;
            }
            return content;
        }

        public async Task<JObject> GetPageSpecificHelp(string pagePath, string userId, string tabId)
        {
            HelpCenterDBContext helpDbContext = _utility.GetHelpCenterContext();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            var screenData = await helpDbContext.ghc_screen_info.FirstOrDefaultAsync(x => x.module_path == pagePath);
            string screenInfo = screenData != null ? screenData.pk_id.ToString() : string.Empty;

            List<ScreenIdContentSortingHelper> allContent = await (from ch in helpDbContext.ghc_content_header
                                                                   join cs in helpDbContext.ghc_content_screen_sorting on ch.pk_id equals cs.fk_content_id into res
                                                                   from res1 in res.DefaultIfEmpty()
                                                                   where ch.is_active && res1.fk_screen_id == screenInfo
                                                                   select new ScreenIdContentSortingHelper
                                                                   {
                                                                       content_type = ch.content_type,
                                                                       pk_id = ch.pk_id,
                                                                       content_name = ch.content_name,
                                                                       fk_screen_id = ch.fk_screen_id,
                                                                       most_popular = ch.most_popular,
                                                                       fk_role_id = ch.fk_role_id,
                                                                       description = ch.description,
                                                                       sort_order = res1 == null ? 0 : res1.sort_order,
                                                                       tabIds = ch.fk_tab_id
                                                                   }).AsNoTracking()
                                                                    .OrderBy(x => x.sort_order)
                                                                    .ThenByDescending(x => x.pk_id)
                                                                    .ThenBy(x => x.content_name)
                                                                    .ToListAsync();

            //tenant specific content
            var tenantContent = await _HSerach.GetAllTenantContentAsync();
            //master data connected content
            var mstrDataReplacement = tenantContent.Where(x => x.master_content_id != null && x.fk_tenant_id == userDetails.tenant_id).Select(y => y.master_content_id).ToList();
            var mstrContent = allContent.Where(x => mstrDataReplacement.Contains(x.pk_id)).ToList();
            //remove master data connected content
            var getAllContent = allContent.Except(mstrContent).ToList();
            //own tenant data
            var ownTenantContent = tenantContent.Where(x => x.fk_tenant_id == userDetails.tenant_id).Select(x => x.fk_content_id).ToList();
            //remove other tenant data
            var otherTenantContent = tenantContent.Where(x => x.fk_tenant_id != userDetails.tenant_id && !ownTenantContent.Contains(x.fk_content_id)).Select(x => x.fk_content_id).ToList();

            getAllContent = getAllContent.Where(x => !otherTenantContent.Contains(x.pk_id)).ToList();


            //filter tab specific content
            List<int> contentId = new List<int>();
            if (!string.IsNullOrEmpty(tabId))
            {
                foreach (var item in getAllContent.Where(x => !string.IsNullOrEmpty(x.tabIds)).ToList())
                {
                    List<string> tabIds = item.tabIds.Split(',').Select(x => x).ToList();
                    foreach (var tid in tabIds)
                    {
                        if (tid == tabId)
                        {
                            contentId.Add(item.pk_id);
                            break;
                        }
                    }
                }
                getAllContent = getAllContent.Where(x => contentId.Contains(x.pk_id) || string.IsNullOrEmpty(x.tabIds)).ToList();
            }

            //filter data based on role
            IEnumerable<int> roles = await _utility.GetUserRoleIdsAsync(userId);
            contentId = new List<int>();
            foreach (var item in getAllContent.Where(x => x.fk_role_id != null).ToList())
            {
                if (string.IsNullOrEmpty(item.fk_role_id))
                {
                    continue;
                }

                List<int> mappedRoleIds = item.fk_role_id.Split(',').Select(int.Parse).ToList();
                foreach (var roleId in mappedRoleIds)
                {
                    if (roles.Contains(roleId))
                    {
                        contentId.Add(item.pk_id);
                        break;
                    }
                }
            }
            getAllContent = getAllContent.Where(x => contentId.Contains(x.pk_id)).ToList();

            dynamic helpContent = new JObject();
            if (getAllContent.Any())
            {
                var intoContentInfo = getAllContent.FirstOrDefault();

                //remove first content(lowest sort order) from list since it'll be displayed as part of intro at the top #96254
                getAllContent.RemoveAt(0);

                var quickGuideContent = getAllContent.Where(x => x.content_type == "QuickGuide").ToList();
                var articleContent = getAllContent.Where(x => x.content_type == "Article").ToList();
                var videoContent = getAllContent.Where(x => x.content_type == "Video").ToList();
                var FAQContent = getAllContent.Where(x => x.content_type == "FAQ").ToList();

                var filteredContent = quickGuideContent.Concat(articleContent).Concat(videoContent).Concat(FAQContent).ToList();

                Dictionary<string, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "HelpCenter");


                helpContent.header = intoContentInfo.content_name;
                string description = intoContentInfo.description;
                Uri validUrl;
                bool success = Uri.TryCreate("", UriKind.RelativeOrAbsolute, out validUrl);
                if (success)
                {
                    string domainName = "<a href='" + validUrl.ToString() + "/HelpCenter/HelpCenterDetailContent?";
                    helpContent.headerInfo = description.Replace("&lt;FramsiktInternalLink&gt;", domainName).Replace("&lt;/FramsiktInternalLink&gt;", "'>").Replace("&lt;FramsiktInternalLinkName&gt;", "")
                                            .Replace("&lt;/FramsiktInternalLinkName&gt;", "</a>");
                }
                else
                {
                    helpContent.headerInfo = description;
                }
                helpContent.contentId = intoContentInfo.pk_id;
                helpContent.image = "document-export-settings.svg";
                List<string> contentTypes = new List<string> { "TaskOverview", "QuickGuide", "Article", "Video", "FAQ" };
                dynamic fullData = new JArray();
                foreach (var cType in contentTypes)
                {
                    if (filteredContent.Any(x => x.content_type == cType))
                    {
                        dynamic content = new JObject();
                        string contentType = string.Empty;
                        switch (cType)
                        {
                            case "QuickGuide":
                                contentType = langString["HC_contentType_quickguide"].LangText;
                                content.image = "Hurtigguide.svg";
                                break;
                            case "TaskOverview":
                                contentType = "TaskOverview"; /*langString["HC_contentType_taskoverview"].LangText;*/
                                content.image = "Oppgaveoverblikk.svg";
                                break;
                            case "FAQ":
                                contentType = langString["HC_contentType_faq"].LangText;
                                content.image = "frequently-asked-questions.svg";
                                break;
                            case "Video":
                                contentType = langString["HC_contentType_video"].LangText;
                                content.image = "video.svg";
                                break;
                            default:
                                contentType = langString["HC_contentType_article"].LangText;
                                content.image = "article.svg";
                                break;
                        }
                        content.title = contentType;
                        dynamic cTypeContents = new JArray();
                        foreach (var item in filteredContent.Where(x => x.content_type == cType).ToList())
                        {
                            dynamic cTypeContent = new JObject();
                            cTypeContent.contentId = item.pk_id;
                            cTypeContent.title = item.content_name;
                            cTypeContent.isTenantContent = tenantContent.Any(x => x.fk_tenant_id == userDetails.tenant_id && x.fk_content_id == item.pk_id);
                            cTypeContents.Add(cTypeContent);
                        }
                        content.contentData = cTypeContents;
                        fullData.Add(content);
                    }
                }
                helpContent.data = fullData;
            }
            else
            {
                helpContent.header = string.Empty;
                helpContent.headerInfo = string.Empty;
                helpContent.contentId = string.Empty;
                helpContent.data = string.Empty;
            }
            return helpContent;
        }

        public async Task<dynamic> GetHomePageItemsAsync(string searchInput)
        {
            var moduleInfo = await GetDataFromModuleHeaderAsync(); //Get all modules

            List<int> staticModuleSortOrderList = new List<int> { 1, 2, 3, 4 };
            ContentModuleHelper dataObject = new ContentModuleHelper();

            List<ContentModuleHelper> moduleData = (from m in moduleInfo
                                                    select new ContentModuleHelper
                                                    {
                                                        moduleId = m.pk_module_id,
                                                        moduleName = m.module_name,
                                                        imageName = m.image_name,
                                                        sortOrder = m.sort_order,
                                                    }).Distinct().OrderBy(x => x.sortOrder).ToList();

            if (moduleData.Any())
            {
                //homepage module search
                if (!string.IsNullOrEmpty(searchInput))
                {
                    moduleData = moduleData.Where(x => x.moduleName.ToLower().Contains(searchInput.ToLower())).ToList();
                }
                dataObject.helpCenterStaticModuleList = moduleData.Where(x => staticModuleSortOrderList.Contains(x.sortOrder)).ToList();
                dataObject.helpCenterDynamicModuleList = moduleData.Where(x => !staticModuleSortOrderList.Contains(x.sortOrder)).ToList();
            }
            return dataObject;
        }

        public async Task<List<KeyValueNewData>> GetHelpCenterModulesAsync()
        {
            var moduleInfo = await GetDataFromModuleHeaderAsync(); //Get all modules
            List<KeyValueNewData> moduleData = (from m in moduleInfo
                                                select new KeyValueNewData
                                                {
                                                    Key = m.pk_module_id,
                                                    Value = m.module_name,
                                                }).Distinct().ToList();
            return moduleData;
        }
        public async Task<List<KeyValueNewData>> GetTabNamesAsync(List<string> screenId)
        {
            using (HelpCenterDBContext helpDbContext = _utility.GetHelpCenterContext())
            {
                var tabData = await (from th in helpDbContext.ghc_tab_header
                    join tm in helpDbContext.ghc_screen_tab_mapping on th.pk_tab_id equals tm.fk_tab_id
                    where screenId.Contains(tm.fk_screen_id)
                    select new KeyValueNewData
                    {
                        Key = th.pk_tab_id,
                        Value = th.tab_name
                    }).ToListAsync();
                return tabData;
            }
        }
        private async Task<IEnumerable<ghc_module_header>> GetDataFromModuleHeaderAsync()
        {
            using (HelpCenterDBContext helpDbContext = _utility.GetHelpCenterContext())
            {
                var moduleData = await helpDbContext.ghc_module_header.AsNoTracking().ToListAsync();
                return moduleData;
            }
        }
        private async Task UpdateTenantContentAsync(int contentId, List<int> tenantId, int? masterContentId, UserData userDetails, HelpCenterDBContext helpDbContext)
        {
            //using (HelpCenterDBContext helpDbContext = _utility.GetHelpCenterContext())
            //{
            var tenantContent = await helpDbContext.thc_tenant_content.Where(x => x.fk_content_id == contentId).ToListAsync();
            List<thc_tenant_content> newDatas = new List<thc_tenant_content>();
            if (tenantContent.Any())
            {
                var dataToDelete = tenantContent.Where(x => !tenantId.Contains(x.fk_tenant_id)).ToList();
                if (dataToDelete.Any())
                {
                    await helpDbContext.BulkDeleteAsync(dataToDelete);
                    await helpDbContext.SaveChangesAsync();
                }
                foreach (var item in tenantId)
                {
                    if (tenantContent.FirstOrDefault(x => x.fk_tenant_id == item) == null)
                    {
                        AddDataToTenantContent(contentId, masterContentId, userDetails, newDatas, item);
                    }
                    else
                    {
                        tenantContent.FirstOrDefault(x => x.fk_tenant_id == item).master_content_id = masterContentId;
                        await helpDbContext.BulkSaveChangesAsync();
                    }
                }
            }
            else
            {

                foreach (var item in tenantId)
                    AddDataToTenantContent(contentId, masterContentId, userDetails, newDatas, item);

            }
            if (newDatas.Any())
            {
                await helpDbContext.BulkInsertAsync(newDatas);
                await helpDbContext.SaveChangesAsync();
            }
            //}
        }

        private static void AddDataToTenantContent(int contentId, int? masterContentId, UserData userDetails, List<thc_tenant_content> newDatas, int item)
        {
            thc_tenant_content newData = new thc_tenant_content
            {
                fk_content_id = contentId,
                fk_tenant_id = item,
                master_content_id = masterContentId,
                updated = DateTime.UtcNow,
                updated_by = userDetails.pk_id
            };
            newDatas.Add(newData);
        }
        private async Task<string> ValidateMasterIdAsync(int? masterId, UserData userDetails, List<int> tenantIds, int contentIdFromUI)
        {
            if (masterId != null)
            {
                HelpCenterDBContext helpDbContext = _utility.GetHelpCenterContext();
                Dictionary<string, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "HelpCenter");

                //validate it is valid content id
                var contentId = await helpDbContext.ghc_content_header.FirstOrDefaultAsync(x => x.pk_id == masterId.Value);

                if (contentId == null && tenantIds.Any())
                {
                    return langString["HC_Invalid_masterId"].LangText;
                }
                else if (contentIdFromUI != 0 && (masterId.Value == contentIdFromUI))
                {
                    return langString["HC_masterId_contentId_same"].LangText;
                }
            }
            return string.Empty;
        }
        private async Task<List<thc_tenant_content>> GetMasterContentData(List<int> contentId)
        {
            HelpCenterDBContext helpDbContext = _utility.GetHelpCenterContext();
            var tenantContent = await helpDbContext.thc_tenant_content.Where(x => contentId.Contains(x.fk_content_id)).ToListAsync();
            return tenantContent;
        }

    }
}
