//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Framsikt.BL.Acos {
    using System.Runtime.Serialization;
    using System;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Kontekst", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.MatrikkelKontekst))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.PlanKontekst))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.ArkivKontekst))]
    public partial class Kontekst : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string spraakField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string klientnavnField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string klientversjonField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string systemversjonField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string spraak {
            get {
                return this.spraakField;
            }
            set {
                if ((object.ReferenceEquals(this.spraakField, value) != true)) {
                    this.spraakField = value;
                    this.RaisePropertyChanged("spraak");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string klientnavn {
            get {
                return this.klientnavnField;
            }
            set {
                if ((object.ReferenceEquals(this.klientnavnField, value) != true)) {
                    this.klientnavnField = value;
                    this.RaisePropertyChanged("klientnavn");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string klientversjon {
            get {
                return this.klientversjonField;
            }
            set {
                if ((object.ReferenceEquals(this.klientversjonField, value) != true)) {
                    this.klientversjonField = value;
                    this.RaisePropertyChanged("klientversjon");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string systemversjon {
            get {
                return this.systemversjonField;
            }
            set {
                if ((object.ReferenceEquals(this.systemversjonField, value) != true)) {
                    this.systemversjonField = value;
                    this.RaisePropertyChanged("systemversjon");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="MatrikkelKontekst", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class MatrikkelKontekst : Framsikt.BL.Acos.Kontekst {
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.KoordinatsystemKode koordinatsystemField;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public Framsikt.BL.Acos.KoordinatsystemKode koordinatsystem {
            get {
                return this.koordinatsystemField;
            }
            set {
                if ((object.ReferenceEquals(this.koordinatsystemField, value) != true)) {
                    this.koordinatsystemField = value;
                    this.RaisePropertyChanged("koordinatsystem");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="PlanKontekst", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class PlanKontekst : Framsikt.BL.Acos.Kontekst {
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.KoordinatsystemKode koordinatsystemField;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public Framsikt.BL.Acos.KoordinatsystemKode koordinatsystem {
            get {
                return this.koordinatsystemField;
            }
            set {
                if ((object.ReferenceEquals(this.koordinatsystemField, value) != true)) {
                    this.koordinatsystemField = value;
                    this.RaisePropertyChanged("koordinatsystem");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ArkivKontekst", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class ArkivKontekst : Framsikt.BL.Acos.Kontekst {
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.KoordinatsystemKode koordinatsystemField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string referanseoppsettField;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public Framsikt.BL.Acos.KoordinatsystemKode koordinatsystem {
            get {
                return this.koordinatsystemField;
            }
            set {
                if ((object.ReferenceEquals(this.koordinatsystemField, value) != true)) {
                    this.koordinatsystemField = value;
                    this.RaisePropertyChanged("koordinatsystem");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string referanseoppsett {
            get {
                return this.referanseoppsettField;
            }
            set {
                if ((object.ReferenceEquals(this.referanseoppsettField, value) != true)) {
                    this.referanseoppsettField = value;
                    this.RaisePropertyChanged("referanseoppsett");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="KoordinatsystemKode", Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class KoordinatsystemKode : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Kode", Namespace="http://rep.geointegrasjon.no/Felles/Kodeliste/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.EnkelAdressetype))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Landkode))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.SakspartRolle))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Arkivdel))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Avskrivningsmaate))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Dokumentmedium))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Forsendelsesmaate))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Informasjonstype))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Journalenhet))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Journalposttype))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Journalstatus))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Kassasjonsvedtak))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Klassifikasjonssystem))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Korrespondanseparttype))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Mappetype))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Saksstatus))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.SkjermingOpphorerAksjon))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.SkjermingsHjemmel))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Tilgangsrestriksjon))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.PersonidentifikatorType))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Dokumenttype))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Dokumentstatus))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Format))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.TilknyttetRegistreringSom))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Variantformat))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.KoordinatsystemKode))]
    public partial class Kode : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private string kodeverdiField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string kodebeskrivelseField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private bool erGyldigField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public string kodeverdi {
            get {
                return this.kodeverdiField;
            }
            set {
                if ((object.ReferenceEquals(this.kodeverdiField, value) != true)) {
                    this.kodeverdiField = value;
                    this.RaisePropertyChanged("kodeverdi");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string kodebeskrivelse {
            get {
                return this.kodebeskrivelseField;
            }
            set {
                if ((object.ReferenceEquals(this.kodebeskrivelseField, value) != true)) {
                    this.kodebeskrivelseField = value;
                    this.RaisePropertyChanged("kodebeskrivelse");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=2)]
        public bool erGyldig {
            get {
                return this.erGyldigField;
            }
            set {
                if ((this.erGyldigField.Equals(value) != true)) {
                    this.erGyldigField = value;
                    this.RaisePropertyChanged("erGyldig");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="EnkelAdressetype", Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class EnkelAdressetype : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Landkode", Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Landkode : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SakspartRolle", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class SakspartRolle : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Arkivdel", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Arkivdel : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Avskrivningsmaate", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Avskrivningsmaate : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Dokumentmedium", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Dokumentmedium : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Forsendelsesmaate", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Forsendelsesmaate : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Informasjonstype", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Informasjonstype : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Journalenhet", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Journalenhet : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Journalposttype", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Journalposttype : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Journalstatus", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Journalstatus : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Kassasjonsvedtak", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Kassasjonsvedtak : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Klassifikasjonssystem", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Klassifikasjonssystem : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Korrespondanseparttype", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Korrespondanseparttype : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Mappetype", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Mappetype : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Saksstatus", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Saksstatus : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SkjermingOpphorerAksjon", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class SkjermingOpphorerAksjon : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SkjermingsHjemmel", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class SkjermingsHjemmel : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Tilgangsrestriksjon", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Tilgangsrestriksjon : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="PersonidentifikatorType", Namespace="http://rep.geointegrasjon.no/Felles/Kontakt/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class PersonidentifikatorType : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Dokumenttype", Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Dokumenttype : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Dokumentstatus", Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Dokumentstatus : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Format", Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Format : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="TilknyttetRegistreringSom", Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class TilknyttetRegistreringSom : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Variantformat", Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Variantformat : Framsikt.BL.Acos.Kode {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.CollectionDataContractAttribute(Name="KodeListe", Namespace="http://rep.geointegrasjon.no/Felles/Kodeliste/xml.schema/2012.01.31", ItemName="liste")]
    [System.SerializableAttribute()]
    public class KodeListe : System.Collections.Generic.List<Framsikt.BL.Acos.Kode> {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GeointegrasjonFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.ApplicationFault))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.FinderFault))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.ValidationFault))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.SystemFault))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.ImplementationFault))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.OperationalFault))]
    public partial class GeointegrasjonFault : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private string feilKodeField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string feilBeskrivelseField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.StringListe feilDetaljerField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public string feilKode {
            get {
                return this.feilKodeField;
            }
            set {
                if ((object.ReferenceEquals(this.feilKodeField, value) != true)) {
                    this.feilKodeField = value;
                    this.RaisePropertyChanged("feilKode");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string feilBeskrivelse {
            get {
                return this.feilBeskrivelseField;
            }
            set {
                if ((object.ReferenceEquals(this.feilBeskrivelseField, value) != true)) {
                    this.feilBeskrivelseField = value;
                    this.RaisePropertyChanged("feilBeskrivelse");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public Framsikt.BL.Acos.StringListe feilDetaljer {
            get {
                return this.feilDetaljerField;
            }
            set {
                if ((object.ReferenceEquals(this.feilDetaljerField, value) != true)) {
                    this.feilDetaljerField = value;
                    this.RaisePropertyChanged("feilDetaljer");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.FinderFault))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.ValidationFault))]
    public partial class ApplicationFault : Framsikt.BL.Acos.GeointegrasjonFault {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class FinderFault : Framsikt.BL.Acos.ApplicationFault {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class ValidationFault : Framsikt.BL.Acos.ApplicationFault {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.ImplementationFault))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.OperationalFault))]
    public partial class SystemFault : Framsikt.BL.Acos.GeointegrasjonFault {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class ImplementationFault : Framsikt.BL.Acos.SystemFault {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class OperationalFault : Framsikt.BL.Acos.SystemFault {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.CollectionDataContractAttribute(Name="StringListe", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31", ItemName="liste")]
    [System.SerializableAttribute()]
    public class StringListe : System.Collections.Generic.List<string> {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SystemID", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class SystemID : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private string idField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public string id {
            get {
                return this.idField;
            }
            set {
                if ((object.ReferenceEquals(this.idField, value) != true)) {
                    this.idField = value;
                    this.RaisePropertyChanged("id");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Fil", Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Filinnhold))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Filreferanse))]
    public partial class Fil : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private string filnavnField;
        
        private string mimeTypeField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public string filnavn {
            get {
                return this.filnavnField;
            }
            set {
                if ((object.ReferenceEquals(this.filnavnField, value) != true)) {
                    this.filnavnField = value;
                    this.RaisePropertyChanged("filnavn");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public string mimeType {
            get {
                return this.mimeTypeField;
            }
            set {
                if ((object.ReferenceEquals(this.mimeTypeField, value) != true)) {
                    this.mimeTypeField = value;
                    this.RaisePropertyChanged("mimeType");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Filinnhold", Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Filinnhold : Framsikt.BL.Acos.Fil {
        
        private byte[] base64Field;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public byte[] base64 {
            get {
                return this.base64Field;
            }
            set {
                if ((object.ReferenceEquals(this.base64Field, value) != true)) {
                    this.base64Field = value;
                    this.RaisePropertyChanged("base64");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Filreferanse", Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Filreferanse : Framsikt.BL.Acos.Fil {
        
        private System.Uri uriField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Uri kvitteringUriField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public System.Uri uri {
            get {
                return this.uriField;
            }
            set {
                if ((object.ReferenceEquals(this.uriField, value) != true)) {
                    this.uriField = value;
                    this.RaisePropertyChanged("uri");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public System.Uri kvitteringUri {
            get {
                return this.kvitteringUriField;
            }
            set {
                if ((object.ReferenceEquals(this.kvitteringUriField, value) != true)) {
                    this.kvitteringUriField = value;
                    this.RaisePropertyChanged("kvitteringUri");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Saksnoekkel", Namespace="http://rep.geointegrasjon.no/Arkiv/Felles/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Saksnummer))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.SakEksternNoekkel))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.SakSystemId))]
    public partial class Saksnoekkel : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Saksnummer", Namespace="http://rep.geointegrasjon.no/Arkiv/Felles/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Saksnummer : Framsikt.BL.Acos.Saksnoekkel {
        
        private long saksaarField;
        
        private long sakssekvensnummerField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public long saksaar {
            get {
                return this.saksaarField;
            }
            set {
                if ((this.saksaarField.Equals(value) != true)) {
                    this.saksaarField = value;
                    this.RaisePropertyChanged("saksaar");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public long sakssekvensnummer {
            get {
                return this.sakssekvensnummerField;
            }
            set {
                if ((this.sakssekvensnummerField.Equals(value) != true)) {
                    this.sakssekvensnummerField = value;
                    this.RaisePropertyChanged("sakssekvensnummer");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SakEksternNoekkel", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class SakEksternNoekkel : Framsikt.BL.Acos.Saksnoekkel {
        
        private Framsikt.BL.Acos.EksternNoekkel eksternnoekkelField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public Framsikt.BL.Acos.EksternNoekkel eksternnoekkel {
            get {
                return this.eksternnoekkelField;
            }
            set {
                if ((object.ReferenceEquals(this.eksternnoekkelField, value) != true)) {
                    this.eksternnoekkelField = value;
                    this.RaisePropertyChanged("eksternnoekkel");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SakSystemId", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class SakSystemId : Framsikt.BL.Acos.Saksnoekkel {
        
        private Framsikt.BL.Acos.SystemID systemIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public Framsikt.BL.Acos.SystemID systemID {
            get {
                return this.systemIDField;
            }
            set {
                if ((object.ReferenceEquals(this.systemIDField, value) != true)) {
                    this.systemIDField = value;
                    this.RaisePropertyChanged("systemID");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="EksternNoekkel", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class EksternNoekkel : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private string fagsystemField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string noekkelField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public string fagsystem {
            get {
                return this.fagsystemField;
            }
            set {
                if ((object.ReferenceEquals(this.fagsystemField, value) != true)) {
                    this.fagsystemField = value;
                    this.RaisePropertyChanged("fagsystem");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string noekkel {
            get {
                return this.noekkelField;
            }
            set {
                if ((object.ReferenceEquals(this.noekkelField, value) != true)) {
                    this.noekkelField = value;
                    this.RaisePropertyChanged("noekkel");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.CollectionDataContractAttribute(Name="DokumenttypeListe", Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31", ItemName="liste")]
    [System.SerializableAttribute()]
    public class DokumenttypeListe : System.Collections.Generic.List<Framsikt.BL.Acos.Dokumenttype> {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Journpostnoekkel", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Dokumentnummer))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Journalnummer))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.JournpostEksternNoekkel))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.JournpostSystemID))]
    public partial class Journpostnoekkel : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Dokumentnummer", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Dokumentnummer : Framsikt.BL.Acos.Journpostnoekkel {
        
        private long saksaarField;
        
        private long sakssekvensnummerField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private long journalpostnummerField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public long saksaar {
            get {
                return this.saksaarField;
            }
            set {
                if ((this.saksaarField.Equals(value) != true)) {
                    this.saksaarField = value;
                    this.RaisePropertyChanged("saksaar");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public long sakssekvensnummer {
            get {
                return this.sakssekvensnummerField;
            }
            set {
                if ((this.sakssekvensnummerField.Equals(value) != true)) {
                    this.sakssekvensnummerField = value;
                    this.RaisePropertyChanged("sakssekvensnummer");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=2)]
        public long journalpostnummer {
            get {
                return this.journalpostnummerField;
            }
            set {
                if ((this.journalpostnummerField.Equals(value) != true)) {
                    this.journalpostnummerField = value;
                    this.RaisePropertyChanged("journalpostnummer");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Journalnummer", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Journalnummer : Framsikt.BL.Acos.Journpostnoekkel {
        
        private long journalaarField;
        
        private long journalsekvensnummerField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public long journalaar {
            get {
                return this.journalaarField;
            }
            set {
                if ((this.journalaarField.Equals(value) != true)) {
                    this.journalaarField = value;
                    this.RaisePropertyChanged("journalaar");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public long journalsekvensnummer {
            get {
                return this.journalsekvensnummerField;
            }
            set {
                if ((this.journalsekvensnummerField.Equals(value) != true)) {
                    this.journalsekvensnummerField = value;
                    this.RaisePropertyChanged("journalsekvensnummer");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="JournpostEksternNoekkel", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class JournpostEksternNoekkel : Framsikt.BL.Acos.Journpostnoekkel {
        
        private Framsikt.BL.Acos.EksternNoekkel eksternnoekkelField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public Framsikt.BL.Acos.EksternNoekkel eksternnoekkel {
            get {
                return this.eksternnoekkelField;
            }
            set {
                if ((object.ReferenceEquals(this.eksternnoekkelField, value) != true)) {
                    this.eksternnoekkelField = value;
                    this.RaisePropertyChanged("eksternnoekkel");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="JournpostSystemID", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class JournpostSystemID : Framsikt.BL.Acos.Journpostnoekkel {
        
        private Framsikt.BL.Acos.SystemID systemIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public Framsikt.BL.Acos.SystemID systemID {
            get {
                return this.systemIDField;
            }
            set {
                if ((object.ReferenceEquals(this.systemIDField, value) != true)) {
                    this.systemIDField = value;
                    this.RaisePropertyChanged("systemID");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.CollectionDataContractAttribute(Name="JournalpostListe", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31", ItemName="liste")]
    [System.SerializableAttribute()]
    public class JournalpostListe : System.Collections.Generic.List<Framsikt.BL.Acos.Journalpost> {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Journalpost", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Journalpost : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string systemIDField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Journalnummer journalnummerField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string journalpostnummerField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.DateTime journaldatoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Journalposttype journalposttypeField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.DateTime dokumentetsDatoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Journalstatus journalstatusField;
        
        private string tittelField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private bool skjermetTittelField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.DateTime forfallsdatoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Skjerming skjermingField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Arkivdel referanseArkivdelField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string tilleggskodeField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string antallVedleggField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string offentligTittelField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Saksnummer saksnrField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string tilgangsgruppeNavnField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.SakSystemId referanseSakSystemIDField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.KorrespondansepartListe korrespondansepartField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.EksternNoekkel referanseEksternNoekkelField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.EksternNoekkel referanseMappeEksternNoekkelField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.AvskrivningListe referanseAvskrivningerField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.MerknadListe merknaderField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.TilleggsinformasjonListe tilleggsinformasjonField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string systemID {
            get {
                return this.systemIDField;
            }
            set {
                if ((object.ReferenceEquals(this.systemIDField, value) != true)) {
                    this.systemIDField = value;
                    this.RaisePropertyChanged("systemID");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public Framsikt.BL.Acos.Journalnummer journalnummer {
            get {
                return this.journalnummerField;
            }
            set {
                if ((object.ReferenceEquals(this.journalnummerField, value) != true)) {
                    this.journalnummerField = value;
                    this.RaisePropertyChanged("journalnummer");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string journalpostnummer {
            get {
                return this.journalpostnummerField;
            }
            set {
                if ((object.ReferenceEquals(this.journalpostnummerField, value) != true)) {
                    this.journalpostnummerField = value;
                    this.RaisePropertyChanged("journalpostnummer");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=3)]
        public System.DateTime journaldato {
            get {
                return this.journaldatoField;
            }
            set {
                if ((this.journaldatoField.Equals(value) != true)) {
                    this.journaldatoField = value;
                    this.RaisePropertyChanged("journaldato");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public Framsikt.BL.Acos.Journalposttype journalposttype {
            get {
                return this.journalposttypeField;
            }
            set {
                if ((object.ReferenceEquals(this.journalposttypeField, value) != true)) {
                    this.journalposttypeField = value;
                    this.RaisePropertyChanged("journalposttype");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=5)]
        public System.DateTime dokumentetsDato {
            get {
                return this.dokumentetsDatoField;
            }
            set {
                if ((this.dokumentetsDatoField.Equals(value) != true)) {
                    this.dokumentetsDatoField = value;
                    this.RaisePropertyChanged("dokumentetsDato");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=6)]
        public Framsikt.BL.Acos.Journalstatus journalstatus {
            get {
                return this.journalstatusField;
            }
            set {
                if ((object.ReferenceEquals(this.journalstatusField, value) != true)) {
                    this.journalstatusField = value;
                    this.RaisePropertyChanged("journalstatus");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false, Order=7)]
        public string tittel {
            get {
                return this.tittelField;
            }
            set {
                if ((object.ReferenceEquals(this.tittelField, value) != true)) {
                    this.tittelField = value;
                    this.RaisePropertyChanged("tittel");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=8)]
        public bool skjermetTittel {
            get {
                return this.skjermetTittelField;
            }
            set {
                if ((this.skjermetTittelField.Equals(value) != true)) {
                    this.skjermetTittelField = value;
                    this.RaisePropertyChanged("skjermetTittel");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=9)]
        public System.DateTime forfallsdato {
            get {
                return this.forfallsdatoField;
            }
            set {
                if ((this.forfallsdatoField.Equals(value) != true)) {
                    this.forfallsdatoField = value;
                    this.RaisePropertyChanged("forfallsdato");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=10)]
        public Framsikt.BL.Acos.Skjerming skjerming {
            get {
                return this.skjermingField;
            }
            set {
                if ((object.ReferenceEquals(this.skjermingField, value) != true)) {
                    this.skjermingField = value;
                    this.RaisePropertyChanged("skjerming");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=11)]
        public Framsikt.BL.Acos.Arkivdel referanseArkivdel {
            get {
                return this.referanseArkivdelField;
            }
            set {
                if ((object.ReferenceEquals(this.referanseArkivdelField, value) != true)) {
                    this.referanseArkivdelField = value;
                    this.RaisePropertyChanged("referanseArkivdel");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=12)]
        public string tilleggskode {
            get {
                return this.tilleggskodeField;
            }
            set {
                if ((object.ReferenceEquals(this.tilleggskodeField, value) != true)) {
                    this.tilleggskodeField = value;
                    this.RaisePropertyChanged("tilleggskode");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=13)]
        public string antallVedlegg {
            get {
                return this.antallVedleggField;
            }
            set {
                if ((object.ReferenceEquals(this.antallVedleggField, value) != true)) {
                    this.antallVedleggField = value;
                    this.RaisePropertyChanged("antallVedlegg");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=14)]
        public string offentligTittel {
            get {
                return this.offentligTittelField;
            }
            set {
                if ((object.ReferenceEquals(this.offentligTittelField, value) != true)) {
                    this.offentligTittelField = value;
                    this.RaisePropertyChanged("offentligTittel");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=15)]
        public Framsikt.BL.Acos.Saksnummer saksnr {
            get {
                return this.saksnrField;
            }
            set {
                if ((object.ReferenceEquals(this.saksnrField, value) != true)) {
                    this.saksnrField = value;
                    this.RaisePropertyChanged("saksnr");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=16)]
        public string tilgangsgruppeNavn {
            get {
                return this.tilgangsgruppeNavnField;
            }
            set {
                if ((object.ReferenceEquals(this.tilgangsgruppeNavnField, value) != true)) {
                    this.tilgangsgruppeNavnField = value;
                    this.RaisePropertyChanged("tilgangsgruppeNavn");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=17)]
        public Framsikt.BL.Acos.SakSystemId referanseSakSystemID {
            get {
                return this.referanseSakSystemIDField;
            }
            set {
                if ((object.ReferenceEquals(this.referanseSakSystemIDField, value) != true)) {
                    this.referanseSakSystemIDField = value;
                    this.RaisePropertyChanged("referanseSakSystemID");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=18)]
        public Framsikt.BL.Acos.KorrespondansepartListe korrespondansepart {
            get {
                return this.korrespondansepartField;
            }
            set {
                if ((object.ReferenceEquals(this.korrespondansepartField, value) != true)) {
                    this.korrespondansepartField = value;
                    this.RaisePropertyChanged("korrespondansepart");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=19)]
        public Framsikt.BL.Acos.EksternNoekkel referanseEksternNoekkel {
            get {
                return this.referanseEksternNoekkelField;
            }
            set {
                if ((object.ReferenceEquals(this.referanseEksternNoekkelField, value) != true)) {
                    this.referanseEksternNoekkelField = value;
                    this.RaisePropertyChanged("referanseEksternNoekkel");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=20)]
        public Framsikt.BL.Acos.EksternNoekkel referanseMappeEksternNoekkel {
            get {
                return this.referanseMappeEksternNoekkelField;
            }
            set {
                if ((object.ReferenceEquals(this.referanseMappeEksternNoekkelField, value) != true)) {
                    this.referanseMappeEksternNoekkelField = value;
                    this.RaisePropertyChanged("referanseMappeEksternNoekkel");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=21)]
        public Framsikt.BL.Acos.AvskrivningListe referanseAvskrivninger {
            get {
                return this.referanseAvskrivningerField;
            }
            set {
                if ((object.ReferenceEquals(this.referanseAvskrivningerField, value) != true)) {
                    this.referanseAvskrivningerField = value;
                    this.RaisePropertyChanged("referanseAvskrivninger");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=22)]
        public Framsikt.BL.Acos.MerknadListe merknader {
            get {
                return this.merknaderField;
            }
            set {
                if ((object.ReferenceEquals(this.merknaderField, value) != true)) {
                    this.merknaderField = value;
                    this.RaisePropertyChanged("merknader");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=23)]
        public Framsikt.BL.Acos.TilleggsinformasjonListe tilleggsinformasjon {
            get {
                return this.tilleggsinformasjonField;
            }
            set {
                if ((object.ReferenceEquals(this.tilleggsinformasjonField, value) != true)) {
                    this.tilleggsinformasjonField = value;
                    this.RaisePropertyChanged("tilleggsinformasjon");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Skjerming", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Skjerming : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Tilgangsrestriksjon tilgangsrestriksjonField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string skjermingshjemmelField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.DateTime skjermingOpphoererDatoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.SkjermingOpphorerAksjon skjermingOpphoererAksjonField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public Framsikt.BL.Acos.Tilgangsrestriksjon tilgangsrestriksjon {
            get {
                return this.tilgangsrestriksjonField;
            }
            set {
                if ((object.ReferenceEquals(this.tilgangsrestriksjonField, value) != true)) {
                    this.tilgangsrestriksjonField = value;
                    this.RaisePropertyChanged("tilgangsrestriksjon");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string skjermingshjemmel {
            get {
                return this.skjermingshjemmelField;
            }
            set {
                if ((object.ReferenceEquals(this.skjermingshjemmelField, value) != true)) {
                    this.skjermingshjemmelField = value;
                    this.RaisePropertyChanged("skjermingshjemmel");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=2)]
        public System.DateTime skjermingOpphoererDato {
            get {
                return this.skjermingOpphoererDatoField;
            }
            set {
                if ((this.skjermingOpphoererDatoField.Equals(value) != true)) {
                    this.skjermingOpphoererDatoField = value;
                    this.RaisePropertyChanged("skjermingOpphoererDato");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public Framsikt.BL.Acos.SkjermingOpphorerAksjon skjermingOpphoererAksjon {
            get {
                return this.skjermingOpphoererAksjonField;
            }
            set {
                if ((object.ReferenceEquals(this.skjermingOpphoererAksjonField, value) != true)) {
                    this.skjermingOpphoererAksjonField = value;
                    this.RaisePropertyChanged("skjermingOpphoererAksjon");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.CollectionDataContractAttribute(Name="KorrespondansepartListe", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31", ItemName="liste")]
    [System.SerializableAttribute()]
    public class KorrespondansepartListe : System.Collections.Generic.List<Framsikt.BL.Acos.Korrespondansepart> {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.CollectionDataContractAttribute(Name="AvskrivningListe", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31", ItemName="liste")]
    [System.SerializableAttribute()]
    public class AvskrivningListe : System.Collections.Generic.List<Framsikt.BL.Acos.Avskrivning> {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.CollectionDataContractAttribute(Name="MerknadListe", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31", ItemName="liste")]
    [System.SerializableAttribute()]
    public class MerknadListe : System.Collections.Generic.List<Framsikt.BL.Acos.Merknad> {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.CollectionDataContractAttribute(Name="TilleggsinformasjonListe", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31", ItemName="liste")]
    [System.SerializableAttribute()]
    public class TilleggsinformasjonListe : System.Collections.Generic.List<Framsikt.BL.Acos.Tilleggsinformasjon> {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Korrespondansepart", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Korrespondansepart : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string systemIDField;
        
        private Framsikt.BL.Acos.Korrespondanseparttype korrespondanseparttypeField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string behandlingsansvarligField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private bool skjermetKorrespondansepartField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string kortnavnField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string deresReferanseField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Journalenhet journalenhetField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.DateTime fristBesvarelseField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Forsendelsesmaate forsendelsesmaateField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string administrativEnhetInitField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string administrativEnhetField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string saksbehandlerInitField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string saksbehandlerField;
        
        private Framsikt.BL.Acos.Kontakt KontaktField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string systemID {
            get {
                return this.systemIDField;
            }
            set {
                if ((object.ReferenceEquals(this.systemIDField, value) != true)) {
                    this.systemIDField = value;
                    this.RaisePropertyChanged("systemID");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false, Order=1)]
        public Framsikt.BL.Acos.Korrespondanseparttype korrespondanseparttype {
            get {
                return this.korrespondanseparttypeField;
            }
            set {
                if ((object.ReferenceEquals(this.korrespondanseparttypeField, value) != true)) {
                    this.korrespondanseparttypeField = value;
                    this.RaisePropertyChanged("korrespondanseparttype");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string behandlingsansvarlig {
            get {
                return this.behandlingsansvarligField;
            }
            set {
                if ((object.ReferenceEquals(this.behandlingsansvarligField, value) != true)) {
                    this.behandlingsansvarligField = value;
                    this.RaisePropertyChanged("behandlingsansvarlig");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=3)]
        public bool skjermetKorrespondansepart {
            get {
                return this.skjermetKorrespondansepartField;
            }
            set {
                if ((this.skjermetKorrespondansepartField.Equals(value) != true)) {
                    this.skjermetKorrespondansepartField = value;
                    this.RaisePropertyChanged("skjermetKorrespondansepart");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public string kortnavn {
            get {
                return this.kortnavnField;
            }
            set {
                if ((object.ReferenceEquals(this.kortnavnField, value) != true)) {
                    this.kortnavnField = value;
                    this.RaisePropertyChanged("kortnavn");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public string deresReferanse {
            get {
                return this.deresReferanseField;
            }
            set {
                if ((object.ReferenceEquals(this.deresReferanseField, value) != true)) {
                    this.deresReferanseField = value;
                    this.RaisePropertyChanged("deresReferanse");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=6)]
        public Framsikt.BL.Acos.Journalenhet journalenhet {
            get {
                return this.journalenhetField;
            }
            set {
                if ((object.ReferenceEquals(this.journalenhetField, value) != true)) {
                    this.journalenhetField = value;
                    this.RaisePropertyChanged("journalenhet");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=7)]
        public System.DateTime fristBesvarelse {
            get {
                return this.fristBesvarelseField;
            }
            set {
                if ((this.fristBesvarelseField.Equals(value) != true)) {
                    this.fristBesvarelseField = value;
                    this.RaisePropertyChanged("fristBesvarelse");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=8)]
        public Framsikt.BL.Acos.Forsendelsesmaate forsendelsesmaate {
            get {
                return this.forsendelsesmaateField;
            }
            set {
                if ((object.ReferenceEquals(this.forsendelsesmaateField, value) != true)) {
                    this.forsendelsesmaateField = value;
                    this.RaisePropertyChanged("forsendelsesmaate");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=9)]
        public string administrativEnhetInit {
            get {
                return this.administrativEnhetInitField;
            }
            set {
                if ((object.ReferenceEquals(this.administrativEnhetInitField, value) != true)) {
                    this.administrativEnhetInitField = value;
                    this.RaisePropertyChanged("administrativEnhetInit");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=10)]
        public string administrativEnhet {
            get {
                return this.administrativEnhetField;
            }
            set {
                if ((object.ReferenceEquals(this.administrativEnhetField, value) != true)) {
                    this.administrativEnhetField = value;
                    this.RaisePropertyChanged("administrativEnhet");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=11)]
        public string saksbehandlerInit {
            get {
                return this.saksbehandlerInitField;
            }
            set {
                if ((object.ReferenceEquals(this.saksbehandlerInitField, value) != true)) {
                    this.saksbehandlerInitField = value;
                    this.RaisePropertyChanged("saksbehandlerInit");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=12)]
        public string saksbehandler {
            get {
                return this.saksbehandlerField;
            }
            set {
                if ((object.ReferenceEquals(this.saksbehandlerField, value) != true)) {
                    this.saksbehandlerField = value;
                    this.RaisePropertyChanged("saksbehandler");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false, Order=13)]
        public Framsikt.BL.Acos.Kontakt Kontakt {
            get {
                return this.KontaktField;
            }
            set {
                if ((object.ReferenceEquals(this.KontaktField, value) != true)) {
                    this.KontaktField = value;
                    this.RaisePropertyChanged("Kontakt");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Kontakt", Namespace="http://rep.geointegrasjon.no/Felles/Kontakt/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Organisasjon))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Person))]
    public partial class Kontakt : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private string navnField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.EnkelAdresseListe adresserField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.ElektroniskAdresseListe elektroniskeAdresserField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public string navn {
            get {
                return this.navnField;
            }
            set {
                if ((object.ReferenceEquals(this.navnField, value) != true)) {
                    this.navnField = value;
                    this.RaisePropertyChanged("navn");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public Framsikt.BL.Acos.EnkelAdresseListe adresser {
            get {
                return this.adresserField;
            }
            set {
                if ((object.ReferenceEquals(this.adresserField, value) != true)) {
                    this.adresserField = value;
                    this.RaisePropertyChanged("adresser");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public Framsikt.BL.Acos.ElektroniskAdresseListe elektroniskeAdresser {
            get {
                return this.elektroniskeAdresserField;
            }
            set {
                if ((object.ReferenceEquals(this.elektroniskeAdresserField, value) != true)) {
                    this.elektroniskeAdresserField = value;
                    this.RaisePropertyChanged("elektroniskeAdresser");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Organisasjon", Namespace="http://rep.geointegrasjon.no/Felles/Kontakt/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Organisasjon : Framsikt.BL.Acos.Kontakt {
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string organisasjonsnummerField;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string organisasjonsnummer {
            get {
                return this.organisasjonsnummerField;
            }
            set {
                if ((object.ReferenceEquals(this.organisasjonsnummerField, value) != true)) {
                    this.organisasjonsnummerField = value;
                    this.RaisePropertyChanged("organisasjonsnummer");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Person", Namespace="http://rep.geointegrasjon.no/Felles/Kontakt/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Person : Framsikt.BL.Acos.Kontakt {
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Personidentifikator personidField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string etternavnField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string fornavnField;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public Framsikt.BL.Acos.Personidentifikator personid {
            get {
                return this.personidField;
            }
            set {
                if ((object.ReferenceEquals(this.personidField, value) != true)) {
                    this.personidField = value;
                    this.RaisePropertyChanged("personid");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string etternavn {
            get {
                return this.etternavnField;
            }
            set {
                if ((object.ReferenceEquals(this.etternavnField, value) != true)) {
                    this.etternavnField = value;
                    this.RaisePropertyChanged("etternavn");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string fornavn {
            get {
                return this.fornavnField;
            }
            set {
                if ((object.ReferenceEquals(this.fornavnField, value) != true)) {
                    this.fornavnField = value;
                    this.RaisePropertyChanged("fornavn");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.CollectionDataContractAttribute(Name="EnkelAdresseListe", Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31", ItemName="liste")]
    [System.SerializableAttribute()]
    public class EnkelAdresseListe : System.Collections.Generic.List<Framsikt.BL.Acos.EnkelAdresse> {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.CollectionDataContractAttribute(Name="ElektroniskAdresseListe", Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31", ItemName="liste")]
    [System.SerializableAttribute()]
    public class ElektroniskAdresseListe : System.Collections.Generic.List<Framsikt.BL.Acos.ElektroniskAdresse> {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="EnkelAdresse", Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class EnkelAdresse : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private Framsikt.BL.Acos.EnkelAdressetype adressetypeField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string adresselinje1Field;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string adresselinje2Field;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.PostadministrativeOmraader postadresseField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Landkode landkodeField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public Framsikt.BL.Acos.EnkelAdressetype adressetype {
            get {
                return this.adressetypeField;
            }
            set {
                if ((object.ReferenceEquals(this.adressetypeField, value) != true)) {
                    this.adressetypeField = value;
                    this.RaisePropertyChanged("adressetype");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string adresselinje1 {
            get {
                return this.adresselinje1Field;
            }
            set {
                if ((object.ReferenceEquals(this.adresselinje1Field, value) != true)) {
                    this.adresselinje1Field = value;
                    this.RaisePropertyChanged("adresselinje1");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string adresselinje2 {
            get {
                return this.adresselinje2Field;
            }
            set {
                if ((object.ReferenceEquals(this.adresselinje2Field, value) != true)) {
                    this.adresselinje2Field = value;
                    this.RaisePropertyChanged("adresselinje2");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public Framsikt.BL.Acos.PostadministrativeOmraader postadresse {
            get {
                return this.postadresseField;
            }
            set {
                if ((object.ReferenceEquals(this.postadresseField, value) != true)) {
                    this.postadresseField = value;
                    this.RaisePropertyChanged("postadresse");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public Framsikt.BL.Acos.Landkode landkode {
            get {
                return this.landkodeField;
            }
            set {
                if ((object.ReferenceEquals(this.landkodeField, value) != true)) {
                    this.landkodeField = value;
                    this.RaisePropertyChanged("landkode");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="PostadministrativeOmraader", Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class PostadministrativeOmraader : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string postnummerField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string poststedField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string postnummer {
            get {
                return this.postnummerField;
            }
            set {
                if ((object.ReferenceEquals(this.postnummerField, value) != true)) {
                    this.postnummerField = value;
                    this.RaisePropertyChanged("postnummer");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string poststed {
            get {
                return this.poststedField;
            }
            set {
                if ((object.ReferenceEquals(this.poststedField, value) != true)) {
                    this.poststedField = value;
                    this.RaisePropertyChanged("poststed");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ElektroniskAdresse", Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Epost))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Faks))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Telefon))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Meldingsboks))]
    public partial class ElektroniskAdresse : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Epost", Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Epost : Framsikt.BL.Acos.ElektroniskAdresse {
        
        private string epostadresseField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public string epostadresse {
            get {
                return this.epostadresseField;
            }
            set {
                if ((object.ReferenceEquals(this.epostadresseField, value) != true)) {
                    this.epostadresseField = value;
                    this.RaisePropertyChanged("epostadresse");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Faks", Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Faks : Framsikt.BL.Acos.ElektroniskAdresse {
        
        private string faksnummerField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public string faksnummer {
            get {
                return this.faksnummerField;
            }
            set {
                if ((object.ReferenceEquals(this.faksnummerField, value) != true)) {
                    this.faksnummerField = value;
                    this.RaisePropertyChanged("faksnummer");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Telefon", Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Telefon : Framsikt.BL.Acos.ElektroniskAdresse {
        
        private string telefonnummerField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public string telefonnummer {
            get {
                return this.telefonnummerField;
            }
            set {
                if ((object.ReferenceEquals(this.telefonnummerField, value) != true)) {
                    this.telefonnummerField = value;
                    this.RaisePropertyChanged("telefonnummer");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Meldingsboks", Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Meldingsboks : Framsikt.BL.Acos.ElektroniskAdresse {
        
        private string tilbyderField;
        
        private string meldingsboksadresseField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public string tilbyder {
            get {
                return this.tilbyderField;
            }
            set {
                if ((object.ReferenceEquals(this.tilbyderField, value) != true)) {
                    this.tilbyderField = value;
                    this.RaisePropertyChanged("tilbyder");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false, Order=1)]
        public string meldingsboksadresse {
            get {
                return this.meldingsboksadresseField;
            }
            set {
                if ((object.ReferenceEquals(this.meldingsboksadresseField, value) != true)) {
                    this.meldingsboksadresseField = value;
                    this.RaisePropertyChanged("meldingsboksadresse");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Personidentifikator", Namespace="http://rep.geointegrasjon.no/Felles/Kontakt/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Personidentifikator : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string personidentifikatorNrField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.PersonidentifikatorType personidentifikatorTypeField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string personidentifikatorNr {
            get {
                return this.personidentifikatorNrField;
            }
            set {
                if ((object.ReferenceEquals(this.personidentifikatorNrField, value) != true)) {
                    this.personidentifikatorNrField = value;
                    this.RaisePropertyChanged("personidentifikatorNr");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public Framsikt.BL.Acos.PersonidentifikatorType personidentifikatorType {
            get {
                return this.personidentifikatorTypeField;
            }
            set {
                if ((object.ReferenceEquals(this.personidentifikatorTypeField, value) != true)) {
                    this.personidentifikatorTypeField = value;
                    this.RaisePropertyChanged("personidentifikatorType");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Avskrivning", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Avskrivning : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string systemIDField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.DateTime avskrivningsdatoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string avskrevetAvField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Avskrivningsmaate avskrivningsmaateField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Journalnummer referanseAvskriverJournalnummerField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Journalnummer referanseAvskrivesAvJournalnummerField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.EksternNoekkel referanseAvskriverEksternNoekkelField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.EksternNoekkel referanseAvskrivesAvEksternNoekkelField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string systemID {
            get {
                return this.systemIDField;
            }
            set {
                if ((object.ReferenceEquals(this.systemIDField, value) != true)) {
                    this.systemIDField = value;
                    this.RaisePropertyChanged("systemID");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=1)]
        public System.DateTime avskrivningsdato {
            get {
                return this.avskrivningsdatoField;
            }
            set {
                if ((this.avskrivningsdatoField.Equals(value) != true)) {
                    this.avskrivningsdatoField = value;
                    this.RaisePropertyChanged("avskrivningsdato");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string avskrevetAv {
            get {
                return this.avskrevetAvField;
            }
            set {
                if ((object.ReferenceEquals(this.avskrevetAvField, value) != true)) {
                    this.avskrevetAvField = value;
                    this.RaisePropertyChanged("avskrevetAv");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public Framsikt.BL.Acos.Avskrivningsmaate avskrivningsmaate {
            get {
                return this.avskrivningsmaateField;
            }
            set {
                if ((object.ReferenceEquals(this.avskrivningsmaateField, value) != true)) {
                    this.avskrivningsmaateField = value;
                    this.RaisePropertyChanged("avskrivningsmaate");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public Framsikt.BL.Acos.Journalnummer referanseAvskriverJournalnummer {
            get {
                return this.referanseAvskriverJournalnummerField;
            }
            set {
                if ((object.ReferenceEquals(this.referanseAvskriverJournalnummerField, value) != true)) {
                    this.referanseAvskriverJournalnummerField = value;
                    this.RaisePropertyChanged("referanseAvskriverJournalnummer");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public Framsikt.BL.Acos.Journalnummer referanseAvskrivesAvJournalnummer {
            get {
                return this.referanseAvskrivesAvJournalnummerField;
            }
            set {
                if ((object.ReferenceEquals(this.referanseAvskrivesAvJournalnummerField, value) != true)) {
                    this.referanseAvskrivesAvJournalnummerField = value;
                    this.RaisePropertyChanged("referanseAvskrivesAvJournalnummer");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=6)]
        public Framsikt.BL.Acos.EksternNoekkel referanseAvskriverEksternNoekkel {
            get {
                return this.referanseAvskriverEksternNoekkelField;
            }
            set {
                if ((object.ReferenceEquals(this.referanseAvskriverEksternNoekkelField, value) != true)) {
                    this.referanseAvskriverEksternNoekkelField = value;
                    this.RaisePropertyChanged("referanseAvskriverEksternNoekkel");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=7)]
        public Framsikt.BL.Acos.EksternNoekkel referanseAvskrivesAvEksternNoekkel {
            get {
                return this.referanseAvskrivesAvEksternNoekkelField;
            }
            set {
                if ((object.ReferenceEquals(this.referanseAvskrivesAvEksternNoekkelField, value) != true)) {
                    this.referanseAvskrivesAvEksternNoekkelField = value;
                    this.RaisePropertyChanged("referanseAvskrivesAvEksternNoekkel");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Merknad", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Merknad : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string systemIDField;
        
        private string merknadstekstField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string merknadstypeField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.DateTime merknadsdatoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string merknadRegistrertAvField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string merknadRegistrertAvInitField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string systemID {
            get {
                return this.systemIDField;
            }
            set {
                if ((object.ReferenceEquals(this.systemIDField, value) != true)) {
                    this.systemIDField = value;
                    this.RaisePropertyChanged("systemID");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false, Order=1)]
        public string merknadstekst {
            get {
                return this.merknadstekstField;
            }
            set {
                if ((object.ReferenceEquals(this.merknadstekstField, value) != true)) {
                    this.merknadstekstField = value;
                    this.RaisePropertyChanged("merknadstekst");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string merknadstype {
            get {
                return this.merknadstypeField;
            }
            set {
                if ((object.ReferenceEquals(this.merknadstypeField, value) != true)) {
                    this.merknadstypeField = value;
                    this.RaisePropertyChanged("merknadstype");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=3)]
        public System.DateTime merknadsdato {
            get {
                return this.merknadsdatoField;
            }
            set {
                if ((this.merknadsdatoField.Equals(value) != true)) {
                    this.merknadsdatoField = value;
                    this.RaisePropertyChanged("merknadsdato");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public string merknadRegistrertAv {
            get {
                return this.merknadRegistrertAvField;
            }
            set {
                if ((object.ReferenceEquals(this.merknadRegistrertAvField, value) != true)) {
                    this.merknadRegistrertAvField = value;
                    this.RaisePropertyChanged("merknadRegistrertAv");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public string merknadRegistrertAvInit {
            get {
                return this.merknadRegistrertAvInitField;
            }
            set {
                if ((object.ReferenceEquals(this.merknadRegistrertAvInitField, value) != true)) {
                    this.merknadRegistrertAvInitField = value;
                    this.RaisePropertyChanged("merknadRegistrertAvInit");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Tilleggsinformasjon", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Tilleggsinformasjon : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string systemIDField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string rekkefoelgeField;
        
        private Framsikt.BL.Acos.Informasjonstype informasjonstypeField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Tilgangsrestriksjon tilgangsrestriksjonField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.DateTime oppbevaresTilDatoField;
        
        private string informasjonField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string tilgangsgruppeNavnField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.DateTime registrertDatoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string registrertAvField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string registrertAvInitField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string systemID {
            get {
                return this.systemIDField;
            }
            set {
                if ((object.ReferenceEquals(this.systemIDField, value) != true)) {
                    this.systemIDField = value;
                    this.RaisePropertyChanged("systemID");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string rekkefoelge {
            get {
                return this.rekkefoelgeField;
            }
            set {
                if ((object.ReferenceEquals(this.rekkefoelgeField, value) != true)) {
                    this.rekkefoelgeField = value;
                    this.RaisePropertyChanged("rekkefoelge");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false, Order=2)]
        public Framsikt.BL.Acos.Informasjonstype informasjonstype {
            get {
                return this.informasjonstypeField;
            }
            set {
                if ((object.ReferenceEquals(this.informasjonstypeField, value) != true)) {
                    this.informasjonstypeField = value;
                    this.RaisePropertyChanged("informasjonstype");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public Framsikt.BL.Acos.Tilgangsrestriksjon tilgangsrestriksjon {
            get {
                return this.tilgangsrestriksjonField;
            }
            set {
                if ((object.ReferenceEquals(this.tilgangsrestriksjonField, value) != true)) {
                    this.tilgangsrestriksjonField = value;
                    this.RaisePropertyChanged("tilgangsrestriksjon");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=4)]
        public System.DateTime oppbevaresTilDato {
            get {
                return this.oppbevaresTilDatoField;
            }
            set {
                if ((this.oppbevaresTilDatoField.Equals(value) != true)) {
                    this.oppbevaresTilDatoField = value;
                    this.RaisePropertyChanged("oppbevaresTilDato");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false, Order=5)]
        public string informasjon {
            get {
                return this.informasjonField;
            }
            set {
                if ((object.ReferenceEquals(this.informasjonField, value) != true)) {
                    this.informasjonField = value;
                    this.RaisePropertyChanged("informasjon");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=6)]
        public string tilgangsgruppeNavn {
            get {
                return this.tilgangsgruppeNavnField;
            }
            set {
                if ((object.ReferenceEquals(this.tilgangsgruppeNavnField, value) != true)) {
                    this.tilgangsgruppeNavnField = value;
                    this.RaisePropertyChanged("tilgangsgruppeNavn");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=7)]
        public System.DateTime registrertDato {
            get {
                return this.registrertDatoField;
            }
            set {
                if ((this.registrertDatoField.Equals(value) != true)) {
                    this.registrertDatoField = value;
                    this.RaisePropertyChanged("registrertDato");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=8)]
        public string registrertAv {
            get {
                return this.registrertAvField;
            }
            set {
                if ((object.ReferenceEquals(this.registrertAvField, value) != true)) {
                    this.registrertAvField = value;
                    this.RaisePropertyChanged("registrertAv");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=9)]
        public string registrertAvInit {
            get {
                return this.registrertAvInitField;
            }
            set {
                if ((object.ReferenceEquals(this.registrertAvInitField, value) != true)) {
                    this.registrertAvInitField = value;
                    this.RaisePropertyChanged("registrertAvInit");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.CollectionDataContractAttribute(Name="SoekskriterieListe", Namespace="http://rep.geointegrasjon.no/Felles/Filter/xml.schema/2012.01.31", ItemName="liste")]
    [System.SerializableAttribute()]
    public class SoekskriterieListe : System.Collections.Generic.List<Framsikt.BL.Acos.Soekskriterie> {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Soekskriterie", Namespace="http://rep.geointegrasjon.no/Felles/Filter/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Soekskriterie : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private Framsikt.BL.Acos.SoekeOperatorEnum operatorField;
        
        private Framsikt.BL.Acos.Kriterie KriterieField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public Framsikt.BL.Acos.SoekeOperatorEnum @operator {
            get {
                return this.operatorField;
            }
            set {
                if ((this.operatorField.Equals(value) != true)) {
                    this.operatorField = value;
                    this.RaisePropertyChanged("operator");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false, Order=1)]
        public Framsikt.BL.Acos.Kriterie Kriterie {
            get {
                return this.KriterieField;
            }
            set {
                if ((object.ReferenceEquals(this.KriterieField, value) != true)) {
                    this.KriterieField = value;
                    this.RaisePropertyChanged("Kriterie");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Kriterie", Namespace="http://rep.geointegrasjon.no/Felles/Filter/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.BboxKriterie))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Ansvarlig))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Soekefelt))]
    public partial class Kriterie : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SoekeOperatorEnum", Namespace="http://rep.geointegrasjon.no/Felles/Filter/xml.schema/2012.01.31")]
    public enum SoekeOperatorEnum : int {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        LT = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        LE = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        EQ = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        GE = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        GT = 4,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="BboxKriterie", Namespace="http://rep.geointegrasjon.no/Felles/Filter/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class BboxKriterie : Framsikt.BL.Acos.Kriterie {
        
        private Framsikt.BL.Acos.Bbox bboxField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public Framsikt.BL.Acos.Bbox bbox {
            get {
                return this.bboxField;
            }
            set {
                if ((object.ReferenceEquals(this.bboxField, value) != true)) {
                    this.bboxField = value;
                    this.RaisePropertyChanged("bbox");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Ansvarlig", Namespace="http://rep.geointegrasjon.no/Felles/Filter/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Ansvarlig : Framsikt.BL.Acos.Kriterie {
        
        private Framsikt.BL.Acos.AnsvarligEnum eierField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public Framsikt.BL.Acos.AnsvarligEnum eier {
            get {
                return this.eierField;
            }
            set {
                if ((this.eierField.Equals(value) != true)) {
                    this.eierField = value;
                    this.RaisePropertyChanged("eier");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Soekefelt", Namespace="http://rep.geointegrasjon.no/Felles/Filter/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Soekefelt : Framsikt.BL.Acos.Kriterie {
        
        private string feltnavnField;
        
        private string feltverdiField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public string feltnavn {
            get {
                return this.feltnavnField;
            }
            set {
                if ((object.ReferenceEquals(this.feltnavnField, value) != true)) {
                    this.feltnavnField = value;
                    this.RaisePropertyChanged("feltnavn");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public string feltverdi {
            get {
                return this.feltverdiField;
            }
            set {
                if ((object.ReferenceEquals(this.feltverdiField, value) != true)) {
                    this.feltverdiField = value;
                    this.RaisePropertyChanged("feltverdi");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Bbox", Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Bbox : Framsikt.BL.Acos.Geometri {
        
        private Framsikt.BL.Acos.Koordinat nedreVenstreField;
        
        private Framsikt.BL.Acos.Koordinat oevreHoeyreField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public Framsikt.BL.Acos.Koordinat nedreVenstre {
            get {
                return this.nedreVenstreField;
            }
            set {
                if ((object.ReferenceEquals(this.nedreVenstreField, value) != true)) {
                    this.nedreVenstreField = value;
                    this.RaisePropertyChanged("nedreVenstre");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public Framsikt.BL.Acos.Koordinat oevreHoeyre {
            get {
                return this.oevreHoeyreField;
            }
            set {
                if ((object.ReferenceEquals(this.oevreHoeyreField, value) != true)) {
                    this.oevreHoeyreField = value;
                    this.RaisePropertyChanged("oevreHoeyre");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Geometri", Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Flate))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Kurve))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Punkt))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Bbox))]
    public partial class Geometri : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private Framsikt.BL.Acos.KoordinatsystemKode koordinatsystemField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public Framsikt.BL.Acos.KoordinatsystemKode koordinatsystem {
            get {
                return this.koordinatsystemField;
            }
            set {
                if ((object.ReferenceEquals(this.koordinatsystemField, value) != true)) {
                    this.koordinatsystemField = value;
                    this.RaisePropertyChanged("koordinatsystem");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Flate", Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Flate : Framsikt.BL.Acos.Geometri {
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.RingListe indreAvgrensningField;
        
        private Framsikt.BL.Acos.Ring ytreAvgrensningField;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public Framsikt.BL.Acos.RingListe indreAvgrensning {
            get {
                return this.indreAvgrensningField;
            }
            set {
                if ((object.ReferenceEquals(this.indreAvgrensningField, value) != true)) {
                    this.indreAvgrensningField = value;
                    this.RaisePropertyChanged("indreAvgrensning");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public Framsikt.BL.Acos.Ring ytreAvgrensning {
            get {
                return this.ytreAvgrensningField;
            }
            set {
                if ((object.ReferenceEquals(this.ytreAvgrensningField, value) != true)) {
                    this.ytreAvgrensningField = value;
                    this.RaisePropertyChanged("ytreAvgrensning");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Kurve", Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Kurve : Framsikt.BL.Acos.Geometri {
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.KoordinatListe linjeField;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public Framsikt.BL.Acos.KoordinatListe linje {
            get {
                return this.linjeField;
            }
            set {
                if ((object.ReferenceEquals(this.linjeField, value) != true)) {
                    this.linjeField = value;
                    this.RaisePropertyChanged("linje");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Punkt", Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Punkt : Framsikt.BL.Acos.Geometri {
        
        private Framsikt.BL.Acos.Koordinat posisjonField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public Framsikt.BL.Acos.Koordinat posisjon {
            get {
                return this.posisjonField;
            }
            set {
                if ((object.ReferenceEquals(this.posisjonField, value) != true)) {
                    this.posisjonField = value;
                    this.RaisePropertyChanged("posisjon");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Koordinat", Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Koordinat : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private double xField;
        
        private double yField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private double zField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public double x {
            get {
                return this.xField;
            }
            set {
                if ((this.xField.Equals(value) != true)) {
                    this.xField = value;
                    this.RaisePropertyChanged("x");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public double y {
            get {
                return this.yField;
            }
            set {
                if ((this.yField.Equals(value) != true)) {
                    this.yField = value;
                    this.RaisePropertyChanged("y");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double z {
            get {
                return this.zField;
            }
            set {
                if ((this.zField.Equals(value) != true)) {
                    this.zField = value;
                    this.RaisePropertyChanged("z");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Ring", Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Ring : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.KoordinatListe lukketKurveField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public Framsikt.BL.Acos.KoordinatListe lukketKurve {
            get {
                return this.lukketKurveField;
            }
            set {
                if ((object.ReferenceEquals(this.lukketKurveField, value) != true)) {
                    this.lukketKurveField = value;
                    this.RaisePropertyChanged("lukketKurve");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.CollectionDataContractAttribute(Name="RingListe", Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31", ItemName="liste")]
    [System.SerializableAttribute()]
    public class RingListe : System.Collections.Generic.List<Framsikt.BL.Acos.Ring> {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.CollectionDataContractAttribute(Name="KoordinatListe", Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31", ItemName="liste")]
    [System.SerializableAttribute()]
    public class KoordinatListe : System.Collections.Generic.List<Framsikt.BL.Acos.Koordinat> {
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AnsvarligEnum", Namespace="http://rep.geointegrasjon.no/Felles/Filter/xml.schema/2012.01.31")]
    public enum AnsvarligEnum : int {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        EGEN = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        EGENENHET = 1,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.CollectionDataContractAttribute(Name="SaksmappeListe", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31", ItemName="liste")]
    [System.SerializableAttribute()]
    public class SaksmappeListe : System.Collections.Generic.List<Framsikt.BL.Acos.Saksmappe> {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Saksmappe", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Saksmappe : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string systemIDField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Saksnummer saksnrField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Mappetype mappetypeField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.DateTime saksdatoField;
        
        private string tittelField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string offentligTittelField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private bool skjermetTittelField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Skjerming skjermingField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Saksstatus saksstatusField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Dokumentmedium dokumentmediumField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Arkivdel referanseArkivdelField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Journalenhet journalenhetField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string bevaringstidField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Kassasjonsvedtak kassasjonsvedtakField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.DateTime kassasjonsdatoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string prosjektField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string administrativEnhetInitField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string administrativEnhetField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string saksansvarligInitField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string saksansvarligField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string tilgangsgruppeNavnField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.MatrikkelnummerListe MatrikkelnummerField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.KlasseListe klasseField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.SakspartListe sakspartField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.PunktListe PunktField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.TilleggsinformasjonListe tilleggsinformasjonField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.ByggIdentListe ByggIdentField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.EksternNoekkel referanseEksternNoekkelField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.MerknadListe merknaderField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.NasjonalArealplanId planIdentField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string systemID {
            get {
                return this.systemIDField;
            }
            set {
                if ((object.ReferenceEquals(this.systemIDField, value) != true)) {
                    this.systemIDField = value;
                    this.RaisePropertyChanged("systemID");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public Framsikt.BL.Acos.Saksnummer saksnr {
            get {
                return this.saksnrField;
            }
            set {
                if ((object.ReferenceEquals(this.saksnrField, value) != true)) {
                    this.saksnrField = value;
                    this.RaisePropertyChanged("saksnr");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public Framsikt.BL.Acos.Mappetype mappetype {
            get {
                return this.mappetypeField;
            }
            set {
                if ((object.ReferenceEquals(this.mappetypeField, value) != true)) {
                    this.mappetypeField = value;
                    this.RaisePropertyChanged("mappetype");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=3)]
        public System.DateTime saksdato {
            get {
                return this.saksdatoField;
            }
            set {
                if ((this.saksdatoField.Equals(value) != true)) {
                    this.saksdatoField = value;
                    this.RaisePropertyChanged("saksdato");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false, Order=4)]
        public string tittel {
            get {
                return this.tittelField;
            }
            set {
                if ((object.ReferenceEquals(this.tittelField, value) != true)) {
                    this.tittelField = value;
                    this.RaisePropertyChanged("tittel");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public string offentligTittel {
            get {
                return this.offentligTittelField;
            }
            set {
                if ((object.ReferenceEquals(this.offentligTittelField, value) != true)) {
                    this.offentligTittelField = value;
                    this.RaisePropertyChanged("offentligTittel");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=6)]
        public bool skjermetTittel {
            get {
                return this.skjermetTittelField;
            }
            set {
                if ((this.skjermetTittelField.Equals(value) != true)) {
                    this.skjermetTittelField = value;
                    this.RaisePropertyChanged("skjermetTittel");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=7)]
        public Framsikt.BL.Acos.Skjerming skjerming {
            get {
                return this.skjermingField;
            }
            set {
                if ((object.ReferenceEquals(this.skjermingField, value) != true)) {
                    this.skjermingField = value;
                    this.RaisePropertyChanged("skjerming");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=8)]
        public Framsikt.BL.Acos.Saksstatus saksstatus {
            get {
                return this.saksstatusField;
            }
            set {
                if ((object.ReferenceEquals(this.saksstatusField, value) != true)) {
                    this.saksstatusField = value;
                    this.RaisePropertyChanged("saksstatus");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=9)]
        public Framsikt.BL.Acos.Dokumentmedium dokumentmedium {
            get {
                return this.dokumentmediumField;
            }
            set {
                if ((object.ReferenceEquals(this.dokumentmediumField, value) != true)) {
                    this.dokumentmediumField = value;
                    this.RaisePropertyChanged("dokumentmedium");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=10)]
        public Framsikt.BL.Acos.Arkivdel referanseArkivdel {
            get {
                return this.referanseArkivdelField;
            }
            set {
                if ((object.ReferenceEquals(this.referanseArkivdelField, value) != true)) {
                    this.referanseArkivdelField = value;
                    this.RaisePropertyChanged("referanseArkivdel");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=11)]
        public Framsikt.BL.Acos.Journalenhet journalenhet {
            get {
                return this.journalenhetField;
            }
            set {
                if ((object.ReferenceEquals(this.journalenhetField, value) != true)) {
                    this.journalenhetField = value;
                    this.RaisePropertyChanged("journalenhet");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=12)]
        public string bevaringstid {
            get {
                return this.bevaringstidField;
            }
            set {
                if ((object.ReferenceEquals(this.bevaringstidField, value) != true)) {
                    this.bevaringstidField = value;
                    this.RaisePropertyChanged("bevaringstid");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=13)]
        public Framsikt.BL.Acos.Kassasjonsvedtak kassasjonsvedtak {
            get {
                return this.kassasjonsvedtakField;
            }
            set {
                if ((object.ReferenceEquals(this.kassasjonsvedtakField, value) != true)) {
                    this.kassasjonsvedtakField = value;
                    this.RaisePropertyChanged("kassasjonsvedtak");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=14)]
        public System.DateTime kassasjonsdato {
            get {
                return this.kassasjonsdatoField;
            }
            set {
                if ((this.kassasjonsdatoField.Equals(value) != true)) {
                    this.kassasjonsdatoField = value;
                    this.RaisePropertyChanged("kassasjonsdato");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=15)]
        public string prosjekt {
            get {
                return this.prosjektField;
            }
            set {
                if ((object.ReferenceEquals(this.prosjektField, value) != true)) {
                    this.prosjektField = value;
                    this.RaisePropertyChanged("prosjekt");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=16)]
        public string administrativEnhetInit {
            get {
                return this.administrativEnhetInitField;
            }
            set {
                if ((object.ReferenceEquals(this.administrativEnhetInitField, value) != true)) {
                    this.administrativEnhetInitField = value;
                    this.RaisePropertyChanged("administrativEnhetInit");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=17)]
        public string administrativEnhet {
            get {
                return this.administrativEnhetField;
            }
            set {
                if ((object.ReferenceEquals(this.administrativEnhetField, value) != true)) {
                    this.administrativEnhetField = value;
                    this.RaisePropertyChanged("administrativEnhet");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=18)]
        public string saksansvarligInit {
            get {
                return this.saksansvarligInitField;
            }
            set {
                if ((object.ReferenceEquals(this.saksansvarligInitField, value) != true)) {
                    this.saksansvarligInitField = value;
                    this.RaisePropertyChanged("saksansvarligInit");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=19)]
        public string saksansvarlig {
            get {
                return this.saksansvarligField;
            }
            set {
                if ((object.ReferenceEquals(this.saksansvarligField, value) != true)) {
                    this.saksansvarligField = value;
                    this.RaisePropertyChanged("saksansvarlig");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=20)]
        public string tilgangsgruppeNavn {
            get {
                return this.tilgangsgruppeNavnField;
            }
            set {
                if ((object.ReferenceEquals(this.tilgangsgruppeNavnField, value) != true)) {
                    this.tilgangsgruppeNavnField = value;
                    this.RaisePropertyChanged("tilgangsgruppeNavn");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=21)]
        public Framsikt.BL.Acos.MatrikkelnummerListe Matrikkelnummer {
            get {
                return this.MatrikkelnummerField;
            }
            set {
                if ((object.ReferenceEquals(this.MatrikkelnummerField, value) != true)) {
                    this.MatrikkelnummerField = value;
                    this.RaisePropertyChanged("Matrikkelnummer");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=22)]
        public Framsikt.BL.Acos.KlasseListe klasse {
            get {
                return this.klasseField;
            }
            set {
                if ((object.ReferenceEquals(this.klasseField, value) != true)) {
                    this.klasseField = value;
                    this.RaisePropertyChanged("klasse");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=23)]
        public Framsikt.BL.Acos.SakspartListe sakspart {
            get {
                return this.sakspartField;
            }
            set {
                if ((object.ReferenceEquals(this.sakspartField, value) != true)) {
                    this.sakspartField = value;
                    this.RaisePropertyChanged("sakspart");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=24)]
        public Framsikt.BL.Acos.PunktListe Punkt {
            get {
                return this.PunktField;
            }
            set {
                if ((object.ReferenceEquals(this.PunktField, value) != true)) {
                    this.PunktField = value;
                    this.RaisePropertyChanged("Punkt");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=25)]
        public Framsikt.BL.Acos.TilleggsinformasjonListe tilleggsinformasjon {
            get {
                return this.tilleggsinformasjonField;
            }
            set {
                if ((object.ReferenceEquals(this.tilleggsinformasjonField, value) != true)) {
                    this.tilleggsinformasjonField = value;
                    this.RaisePropertyChanged("tilleggsinformasjon");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=26)]
        public Framsikt.BL.Acos.ByggIdentListe ByggIdent {
            get {
                return this.ByggIdentField;
            }
            set {
                if ((object.ReferenceEquals(this.ByggIdentField, value) != true)) {
                    this.ByggIdentField = value;
                    this.RaisePropertyChanged("ByggIdent");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=27)]
        public Framsikt.BL.Acos.EksternNoekkel referanseEksternNoekkel {
            get {
                return this.referanseEksternNoekkelField;
            }
            set {
                if ((object.ReferenceEquals(this.referanseEksternNoekkelField, value) != true)) {
                    this.referanseEksternNoekkelField = value;
                    this.RaisePropertyChanged("referanseEksternNoekkel");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=28)]
        public Framsikt.BL.Acos.MerknadListe merknader {
            get {
                return this.merknaderField;
            }
            set {
                if ((object.ReferenceEquals(this.merknaderField, value) != true)) {
                    this.merknaderField = value;
                    this.RaisePropertyChanged("merknader");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=29)]
        public Framsikt.BL.Acos.NasjonalArealplanId planIdent {
            get {
                return this.planIdentField;
            }
            set {
                if ((object.ReferenceEquals(this.planIdentField, value) != true)) {
                    this.planIdentField = value;
                    this.RaisePropertyChanged("planIdent");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="NasjonalArealplanId", Namespace="http://rep.geointegrasjon.no/Plan/Felles/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class NasjonalArealplanId : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private Framsikt.BL.Acos.Administrativenhetsnummer nummerField;
        
        private string planidentifikasjonField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public Framsikt.BL.Acos.Administrativenhetsnummer nummer {
            get {
                return this.nummerField;
            }
            set {
                if ((object.ReferenceEquals(this.nummerField, value) != true)) {
                    this.nummerField = value;
                    this.RaisePropertyChanged("nummer");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public string planidentifikasjon {
            get {
                return this.planidentifikasjonField;
            }
            set {
                if ((object.ReferenceEquals(this.planidentifikasjonField, value) != true)) {
                    this.planidentifikasjonField = value;
                    this.RaisePropertyChanged("planidentifikasjon");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.CollectionDataContractAttribute(Name="MatrikkelnummerListe", Namespace="http://rep.geointegrasjon.no/Matrikkel/Felles/xml.schema/2012.01.31", ItemName="liste")]
    [System.SerializableAttribute()]
    public class MatrikkelnummerListe : System.Collections.Generic.List<Framsikt.BL.Acos.Matrikkelnummer> {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.CollectionDataContractAttribute(Name="KlasseListe", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31", ItemName="liste")]
    [System.SerializableAttribute()]
    public class KlasseListe : System.Collections.Generic.List<Framsikt.BL.Acos.Klasse> {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.CollectionDataContractAttribute(Name="SakspartListe", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31", ItemName="liste")]
    [System.SerializableAttribute()]
    public class SakspartListe : System.Collections.Generic.List<Framsikt.BL.Acos.Sakspart> {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.CollectionDataContractAttribute(Name="PunktListe", Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31", ItemName="liste")]
    [System.SerializableAttribute()]
    public class PunktListe : System.Collections.Generic.List<Framsikt.BL.Acos.Punkt> {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.CollectionDataContractAttribute(Name="ByggIdentListe", Namespace="http://rep.geointegrasjon.no/Matrikkel/Felles/xml.schema/2012.01.31", ItemName="liste")]
    [System.SerializableAttribute()]
    public class ByggIdentListe : System.Collections.Generic.List<Framsikt.BL.Acos.ByggIdent> {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Matrikkelnummer", Namespace="http://rep.geointegrasjon.no/Matrikkel/Felles/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Matrikkelnummer : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private string kommunenummerField;
        
        private long gaardsnummerField;
        
        private long bruksnummerField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private long festenummerField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private long seksjonsnummerField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public string kommunenummer {
            get {
                return this.kommunenummerField;
            }
            set {
                if ((object.ReferenceEquals(this.kommunenummerField, value) != true)) {
                    this.kommunenummerField = value;
                    this.RaisePropertyChanged("kommunenummer");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=1)]
        public long gaardsnummer {
            get {
                return this.gaardsnummerField;
            }
            set {
                if ((this.gaardsnummerField.Equals(value) != true)) {
                    this.gaardsnummerField = value;
                    this.RaisePropertyChanged("gaardsnummer");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=2)]
        public long bruksnummer {
            get {
                return this.bruksnummerField;
            }
            set {
                if ((this.bruksnummerField.Equals(value) != true)) {
                    this.bruksnummerField = value;
                    this.RaisePropertyChanged("bruksnummer");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=3)]
        public long festenummer {
            get {
                return this.festenummerField;
            }
            set {
                if ((this.festenummerField.Equals(value) != true)) {
                    this.festenummerField = value;
                    this.RaisePropertyChanged("festenummer");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=4)]
        public long seksjonsnummer {
            get {
                return this.seksjonsnummerField;
            }
            set {
                if ((this.seksjonsnummerField.Equals(value) != true)) {
                    this.seksjonsnummerField = value;
                    this.RaisePropertyChanged("seksjonsnummer");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Klasse", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Klasse : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string rekkefoelgeField;
        
        private Framsikt.BL.Acos.Klassifikasjonssystem klassifikasjonssystemField;
        
        private string klasseIDField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private bool skjermetKlasseField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string ledetekstField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string tittelField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string rekkefoelge {
            get {
                return this.rekkefoelgeField;
            }
            set {
                if ((object.ReferenceEquals(this.rekkefoelgeField, value) != true)) {
                    this.rekkefoelgeField = value;
                    this.RaisePropertyChanged("rekkefoelge");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false, Order=1)]
        public Framsikt.BL.Acos.Klassifikasjonssystem klassifikasjonssystem {
            get {
                return this.klassifikasjonssystemField;
            }
            set {
                if ((object.ReferenceEquals(this.klassifikasjonssystemField, value) != true)) {
                    this.klassifikasjonssystemField = value;
                    this.RaisePropertyChanged("klassifikasjonssystem");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false, Order=2)]
        public string klasseID {
            get {
                return this.klasseIDField;
            }
            set {
                if ((object.ReferenceEquals(this.klasseIDField, value) != true)) {
                    this.klasseIDField = value;
                    this.RaisePropertyChanged("klasseID");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=3)]
        public bool skjermetKlasse {
            get {
                return this.skjermetKlasseField;
            }
            set {
                if ((this.skjermetKlasseField.Equals(value) != true)) {
                    this.skjermetKlasseField = value;
                    this.RaisePropertyChanged("skjermetKlasse");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public string ledetekst {
            get {
                return this.ledetekstField;
            }
            set {
                if ((object.ReferenceEquals(this.ledetekstField, value) != true)) {
                    this.ledetekstField = value;
                    this.RaisePropertyChanged("ledetekst");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public string tittel {
            get {
                return this.tittelField;
            }
            set {
                if ((object.ReferenceEquals(this.tittelField, value) != true)) {
                    this.tittelField = value;
                    this.RaisePropertyChanged("tittel");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Sakspart", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Sakspart : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string systemIDField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private bool skjermetSakspartField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string kortnavnField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string kontaktpersonField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.SakspartRolle sakspartRolleField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string merknadField;
        
        private Framsikt.BL.Acos.Kontakt KontaktField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string systemID {
            get {
                return this.systemIDField;
            }
            set {
                if ((object.ReferenceEquals(this.systemIDField, value) != true)) {
                    this.systemIDField = value;
                    this.RaisePropertyChanged("systemID");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=1)]
        public bool skjermetSakspart {
            get {
                return this.skjermetSakspartField;
            }
            set {
                if ((this.skjermetSakspartField.Equals(value) != true)) {
                    this.skjermetSakspartField = value;
                    this.RaisePropertyChanged("skjermetSakspart");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string kortnavn {
            get {
                return this.kortnavnField;
            }
            set {
                if ((object.ReferenceEquals(this.kortnavnField, value) != true)) {
                    this.kortnavnField = value;
                    this.RaisePropertyChanged("kortnavn");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string kontaktperson {
            get {
                return this.kontaktpersonField;
            }
            set {
                if ((object.ReferenceEquals(this.kontaktpersonField, value) != true)) {
                    this.kontaktpersonField = value;
                    this.RaisePropertyChanged("kontaktperson");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public Framsikt.BL.Acos.SakspartRolle sakspartRolle {
            get {
                return this.sakspartRolleField;
            }
            set {
                if ((object.ReferenceEquals(this.sakspartRolleField, value) != true)) {
                    this.sakspartRolleField = value;
                    this.RaisePropertyChanged("sakspartRolle");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public string merknad {
            get {
                return this.merknadField;
            }
            set {
                if ((object.ReferenceEquals(this.merknadField, value) != true)) {
                    this.merknadField = value;
                    this.RaisePropertyChanged("merknad");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false, Order=6)]
        public Framsikt.BL.Acos.Kontakt Kontakt {
            get {
                return this.KontaktField;
            }
            set {
                if ((object.ReferenceEquals(this.KontaktField, value) != true)) {
                    this.KontaktField = value;
                    this.RaisePropertyChanged("Kontakt");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ByggIdent", Namespace="http://rep.geointegrasjon.no/Matrikkel/Felles/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class ByggIdent : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private long bygningsNummerField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private long endringsloepenummerField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public long bygningsNummer {
            get {
                return this.bygningsNummerField;
            }
            set {
                if ((this.bygningsNummerField.Equals(value) != true)) {
                    this.bygningsNummerField = value;
                    this.RaisePropertyChanged("bygningsNummer");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public long endringsloepenummer {
            get {
                return this.endringsloepenummerField;
            }
            set {
                if ((this.endringsloepenummerField.Equals(value) != true)) {
                    this.endringsloepenummerField = value;
                    this.RaisePropertyChanged("endringsloepenummer");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Administrativenhetsnummer", Namespace="http://rep.geointegrasjon.no/Plan/Felles/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Fylke))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Kommune))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(Framsikt.BL.Acos.Stat))]
    public partial class Administrativenhetsnummer : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Fylke", Namespace="http://rep.geointegrasjon.no/Plan/Felles/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Fylke : Framsikt.BL.Acos.Administrativenhetsnummer {
        
        private string fylkesnummerField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public string fylkesnummer {
            get {
                return this.fylkesnummerField;
            }
            set {
                if ((object.ReferenceEquals(this.fylkesnummerField, value) != true)) {
                    this.fylkesnummerField = value;
                    this.RaisePropertyChanged("fylkesnummer");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Kommune", Namespace="http://rep.geointegrasjon.no/Plan/Felles/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Kommune : Framsikt.BL.Acos.Administrativenhetsnummer {
        
        private string kommunenummerField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public string kommunenummer {
            get {
                return this.kommunenummerField;
            }
            set {
                if ((object.ReferenceEquals(this.kommunenummerField, value) != true)) {
                    this.kommunenummerField = value;
                    this.RaisePropertyChanged("kommunenummer");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Stat", Namespace="http://rep.geointegrasjon.no/Plan/Felles/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Stat : Framsikt.BL.Acos.Administrativenhetsnummer {
        
        private string landskodeField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public string landskode {
            get {
                return this.landskodeField;
            }
            set {
                if ((object.ReferenceEquals(this.landskodeField, value) != true)) {
                    this.landskodeField = value;
                    this.RaisePropertyChanged("landskode");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.CollectionDataContractAttribute(Name="DokumentListe", Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31", ItemName="liste")]
    [System.SerializableAttribute()]
    public class DokumentListe : System.Collections.Generic.List<Framsikt.BL.Acos.Dokument> {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Dokument", Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    [System.SerializableAttribute()]
    public partial class Dokument : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string systemIDField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string dokumentnummerField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.TilknyttetRegistreringSom tilknyttetRegistreringSomField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Dokumenttype dokumenttypeField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string tittelField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Dokumentstatus dokumentstatusField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Variantformat variantformatField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Format formatField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string referanseJournalpostSystemIDField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private Framsikt.BL.Acos.Fil FilField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string systemID {
            get {
                return this.systemIDField;
            }
            set {
                if ((object.ReferenceEquals(this.systemIDField, value) != true)) {
                    this.systemIDField = value;
                    this.RaisePropertyChanged("systemID");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string dokumentnummer {
            get {
                return this.dokumentnummerField;
            }
            set {
                if ((object.ReferenceEquals(this.dokumentnummerField, value) != true)) {
                    this.dokumentnummerField = value;
                    this.RaisePropertyChanged("dokumentnummer");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public Framsikt.BL.Acos.TilknyttetRegistreringSom tilknyttetRegistreringSom {
            get {
                return this.tilknyttetRegistreringSomField;
            }
            set {
                if ((object.ReferenceEquals(this.tilknyttetRegistreringSomField, value) != true)) {
                    this.tilknyttetRegistreringSomField = value;
                    this.RaisePropertyChanged("tilknyttetRegistreringSom");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public Framsikt.BL.Acos.Dokumenttype dokumenttype {
            get {
                return this.dokumenttypeField;
            }
            set {
                if ((object.ReferenceEquals(this.dokumenttypeField, value) != true)) {
                    this.dokumenttypeField = value;
                    this.RaisePropertyChanged("dokumenttype");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public string tittel {
            get {
                return this.tittelField;
            }
            set {
                if ((object.ReferenceEquals(this.tittelField, value) != true)) {
                    this.tittelField = value;
                    this.RaisePropertyChanged("tittel");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public Framsikt.BL.Acos.Dokumentstatus dokumentstatus {
            get {
                return this.dokumentstatusField;
            }
            set {
                if ((object.ReferenceEquals(this.dokumentstatusField, value) != true)) {
                    this.dokumentstatusField = value;
                    this.RaisePropertyChanged("dokumentstatus");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=6)]
        public Framsikt.BL.Acos.Variantformat variantformat {
            get {
                return this.variantformatField;
            }
            set {
                if ((object.ReferenceEquals(this.variantformatField, value) != true)) {
                    this.variantformatField = value;
                    this.RaisePropertyChanged("variantformat");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=7)]
        public Framsikt.BL.Acos.Format format {
            get {
                return this.formatField;
            }
            set {
                if ((object.ReferenceEquals(this.formatField, value) != true)) {
                    this.formatField = value;
                    this.RaisePropertyChanged("format");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=8)]
        public string referanseJournalpostSystemID {
            get {
                return this.referanseJournalpostSystemIDField;
            }
            set {
                if ((object.ReferenceEquals(this.referanseJournalpostSystemIDField, value) != true)) {
                    this.referanseJournalpostSystemIDField = value;
                    this.RaisePropertyChanged("referanseJournalpostSystemID");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=9)]
        public Framsikt.BL.Acos.Fil Fil {
            get {
                return this.FilField;
            }
            set {
                if ((object.ReferenceEquals(this.FilField, value) != true)) {
                    this.FilField = value;
                    this.RaisePropertyChanged("Fil");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", ConfigurationName="Acos.ArkivInnsynPort")]
    public interface ArkivInnsynPort {
        
        // CODEGEN: Generating message contract since element name kodelistenavn from namespace http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31 is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentKodeliste", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentKodeliste", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentKodeliste", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentKodeliste", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentKodeliste", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentKodeliste", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentKodeliste", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        Framsikt.BL.Acos.HentKodelisteResponse HentKodeliste(Framsikt.BL.Acos.HentKodeliste request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentKodeliste", ReplyAction="*")]
        System.Threading.Tasks.Task<Framsikt.BL.Acos.HentKodelisteResponse> HentKodelisteAsync(Framsikt.BL.Acos.HentKodeliste request);
        
        // CODEGEN: Generating message contract since element name systemid from namespace http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31 is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentFil", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentFil", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentFil", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentFil", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentFil", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentFil", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentFil", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        Framsikt.BL.Acos.HentFilResponse HentFil(Framsikt.BL.Acos.HentFil request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentFil", ReplyAction="*")]
        System.Threading.Tasks.Task<Framsikt.BL.Acos.HentFilResponse> HentFilAsync(Framsikt.BL.Acos.HentFil request);
        
        // CODEGEN: Generating message contract since element name saksnokkel from namespace http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31 is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenttyper", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenttyper", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenttyper", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenttyper", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenttyper", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenttyper", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenttyper", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        Framsikt.BL.Acos.FinnDokumenttyperResponse FinnDokumenttyper(Framsikt.BL.Acos.FinnDokumenttyper request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenttyper", ReplyAction="*")]
        System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnDokumenttyperResponse> FinnDokumenttyperAsync(Framsikt.BL.Acos.FinnDokumenttyper request);
        
        // CODEGEN: Generating message contract since element name nokkel from namespace http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31 is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittNoekkel", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittNoekkel", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittNoekkel", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittNoekkel", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittNoekkel", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittNoekkel", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittNoekkel", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        Framsikt.BL.Acos.FinnJournalposterGittNoekkelResponse FinnJournalposterGittNoekkel(Framsikt.BL.Acos.FinnJournalposterGittNoekkel request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittNoekkel", ReplyAction="*")]
        System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnJournalposterGittNoekkelResponse> FinnJournalposterGittNoekkelAsync(Framsikt.BL.Acos.FinnJournalposterGittNoekkel request);
        
        // CODEGEN: Generating message contract since element name nokkel from namespace http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31 is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittSaksmappeNoekkel", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittSaksmappeNoekkel", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittSaksmappeNoekkel", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittSaksmappeNoekkel", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittSaksmappeNoekkel", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittSaksmappeNoekkel", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittSaksmappeNoekkel", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        Framsikt.BL.Acos.FinnJournalposterGittSaksmappeNoekkelResponse FinnJournalposterGittSaksmappeNoekkel(Framsikt.BL.Acos.FinnJournalposterGittSaksmappeNoekkel request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittSaksmappeNoekkel", ReplyAction="*")]
        System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnJournalposterGittSaksmappeNoekkelResponse> FinnJournalposterGittSaksmappeNoekkelAsync(Framsikt.BL.Acos.FinnJournalposterGittSaksmappeNoekkel request);
        
        // CODEGEN: Generating message contract since element name sok from namespace http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31 is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposter", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposter", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposter", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposter", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposter", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposter", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposter", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        Framsikt.BL.Acos.FinnJournalposterResponse FinnJournalposter(Framsikt.BL.Acos.FinnJournalposter request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposter", ReplyAction="*")]
        System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnJournalposterResponse> FinnJournalposterAsync(Framsikt.BL.Acos.FinnJournalposter request);
        
        // CODEGEN: Generating message contract since element name nokkel from namespace http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31 is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapperGitt" +
            "Noekkel", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapperGitt" +
            "Noekkel", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapperGitt" +
            "Noekkel", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapperGitt" +
            "Noekkel", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapperGitt" +
            "Noekkel", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapperGitt" +
            "Noekkel", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapperGitt" +
            "Noekkel", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        Framsikt.BL.Acos.FinnSaksmapperGittNoekkelResponse FinnSaksmapperGittNoekkel(Framsikt.BL.Acos.FinnSaksmapperGittNoekkel request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapperGitt" +
            "Noekkel", ReplyAction="*")]
        System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnSaksmapperGittNoekkelResponse> FinnSaksmapperGittNoekkelAsync(Framsikt.BL.Acos.FinnSaksmapperGittNoekkel request);
        
        // CODEGEN: Generating message contract since element name sok from namespace http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31 is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapper", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapper", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapper", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapper", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapper", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapper", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapper", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        Framsikt.BL.Acos.FinnSaksmapperResponse FinnSaksmapper(Framsikt.BL.Acos.FinnSaksmapper request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapper", ReplyAction="*")]
        System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnSaksmapperResponse> FinnSaksmapperAsync(Framsikt.BL.Acos.FinnSaksmapper request);
        
        // CODEGEN: Generating message contract since element name journpostnokkel from namespace http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31 is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Journalpostnoekkel", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Journalpostnoekkel", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Journalpostnoekkel", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Journalpostnoekkel", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Journalpostnoekkel", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Journalpostnoekkel", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Journalpostnoekkel", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        Framsikt.BL.Acos.FinnDokumenterGittJournalpostnoekkelResponse FinnDokumenterGittJournalpostnoekkel(Framsikt.BL.Acos.FinnDokumenterGittJournalpostnoekkel request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Journalpostnoekkel", ReplyAction="*")]
        System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnDokumenterGittJournalpostnoekkelResponse> FinnDokumenterGittJournalpostnoekkelAsync(Framsikt.BL.Acos.FinnDokumenterGittJournalpostnoekkel request);
        
        // CODEGEN: Generating message contract since element name saksnokkel from namespace http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31 is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Saksnoekkel", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Saksnoekkel", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Saksnoekkel", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Saksnoekkel", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Saksnoekkel", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Saksnoekkel", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Saksnoekkel", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        Framsikt.BL.Acos.FinnDokumenterGittSaksnoekkelResponse FinnDokumenterGittSaksnoekkel(Framsikt.BL.Acos.FinnDokumenterGittSaksnoekkel request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Saksnoekkel", ReplyAction="*")]
        System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnDokumenterGittSaksnoekkelResponse> FinnDokumenterGittSaksnoekkelAsync(Framsikt.BL.Acos.FinnDokumenterGittSaksnoekkel request);
        
        // CODEGEN: Generating message contract since element name sok from namespace http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31 is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenter", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenter", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenter", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenter", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenter", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenter", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(Framsikt.BL.Acos.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenter", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        Framsikt.BL.Acos.FinnDokumenterResponse FinnDokumenter(Framsikt.BL.Acos.FinnDokumenter request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenter", ReplyAction="*")]
        System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnDokumenterResponse> FinnDokumenterAsync(Framsikt.BL.Acos.FinnDokumenter request);
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class HentKodeliste {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="HentKodeliste", Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public Framsikt.BL.Acos.HentKodelisteBody Body;
        
        public HentKodeliste() {
        }
        
        public HentKodeliste(Framsikt.BL.Acos.HentKodelisteBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31")]
    public partial class HentKodelisteBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string kodelistenavn;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public Framsikt.BL.Acos.ArkivKontekst kontekst;
        
        public HentKodelisteBody() {
        }
        
        public HentKodelisteBody(string kodelistenavn, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            this.kodelistenavn = kodelistenavn;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class HentKodelisteResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="HentKodelisteResponse", Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public Framsikt.BL.Acos.HentKodelisteResponseBody Body;
        
        public HentKodelisteResponse() {
        }
        
        public HentKodelisteResponse(Framsikt.BL.Acos.HentKodelisteResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31")]
    public partial class HentKodelisteResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public Framsikt.BL.Acos.KodeListe @return;
        
        public HentKodelisteResponseBody() {
        }
        
        public HentKodelisteResponseBody(Framsikt.BL.Acos.KodeListe @return) {
            this.@return = @return;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class HentFil {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="HentFil", Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public Framsikt.BL.Acos.HentFilBody Body;
        
        public HentFil() {
        }
        
        public HentFil(Framsikt.BL.Acos.HentFilBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31")]
    public partial class HentFilBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public Framsikt.BL.Acos.SystemID systemid;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public Framsikt.BL.Acos.ArkivKontekst kontekst;
        
        public HentFilBody() {
        }
        
        public HentFilBody(Framsikt.BL.Acos.SystemID systemid, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            this.systemid = systemid;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class HentFilResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="HentFilResponse", Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public Framsikt.BL.Acos.HentFilResponseBody Body;
        
        public HentFilResponse() {
        }
        
        public HentFilResponse(Framsikt.BL.Acos.HentFilResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31")]
    public partial class HentFilResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public Framsikt.BL.Acos.Fil @return;
        
        public HentFilResponseBody() {
        }
        
        public HentFilResponseBody(Framsikt.BL.Acos.Fil @return) {
            this.@return = @return;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class FinnDokumenttyper {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="FinnDokumenttyper", Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public Framsikt.BL.Acos.FinnDokumenttyperBody Body;
        
        public FinnDokumenttyper() {
        }
        
        public FinnDokumenttyper(Framsikt.BL.Acos.FinnDokumenttyperBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31")]
    public partial class FinnDokumenttyperBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public Framsikt.BL.Acos.Saksnoekkel saksnokkel;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public Framsikt.BL.Acos.ArkivKontekst kontekst;
        
        public FinnDokumenttyperBody() {
        }
        
        public FinnDokumenttyperBody(Framsikt.BL.Acos.Saksnoekkel saksnokkel, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            this.saksnokkel = saksnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class FinnDokumenttyperResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="FinnDokumenttyperResponse", Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public Framsikt.BL.Acos.FinnDokumenttyperResponseBody Body;
        
        public FinnDokumenttyperResponse() {
        }
        
        public FinnDokumenttyperResponse(Framsikt.BL.Acos.FinnDokumenttyperResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31")]
    public partial class FinnDokumenttyperResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public Framsikt.BL.Acos.DokumenttypeListe @return;
        
        public FinnDokumenttyperResponseBody() {
        }
        
        public FinnDokumenttyperResponseBody(Framsikt.BL.Acos.DokumenttypeListe @return) {
            this.@return = @return;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class FinnJournalposterGittNoekkel {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="FinnJournalposterGittNoekkel", Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public Framsikt.BL.Acos.FinnJournalposterGittNoekkelBody Body;
        
        public FinnJournalposterGittNoekkel() {
        }
        
        public FinnJournalposterGittNoekkel(Framsikt.BL.Acos.FinnJournalposterGittNoekkelBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31")]
    public partial class FinnJournalposterGittNoekkelBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public Framsikt.BL.Acos.Journpostnoekkel nokkel;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=1)]
        public bool returnerMerknad;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=2)]
        public bool returnerTilleggsinformasjon;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=3)]
        public bool returnerKorrespondansepart;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=4)]
        public bool returnerAvskrivning;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public Framsikt.BL.Acos.ArkivKontekst kontekst;
        
        public FinnJournalposterGittNoekkelBody() {
        }
        
        public FinnJournalposterGittNoekkelBody(Framsikt.BL.Acos.Journpostnoekkel nokkel, bool returnerMerknad, bool returnerTilleggsinformasjon, bool returnerKorrespondansepart, bool returnerAvskrivning, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            this.nokkel = nokkel;
            this.returnerMerknad = returnerMerknad;
            this.returnerTilleggsinformasjon = returnerTilleggsinformasjon;
            this.returnerKorrespondansepart = returnerKorrespondansepart;
            this.returnerAvskrivning = returnerAvskrivning;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class FinnJournalposterGittNoekkelResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="FinnJournalposterGittNoekkelResponse", Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public Framsikt.BL.Acos.FinnJournalposterGittNoekkelResponseBody Body;
        
        public FinnJournalposterGittNoekkelResponse() {
        }
        
        public FinnJournalposterGittNoekkelResponse(Framsikt.BL.Acos.FinnJournalposterGittNoekkelResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31")]
    public partial class FinnJournalposterGittNoekkelResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public Framsikt.BL.Acos.JournalpostListe @return;
        
        public FinnJournalposterGittNoekkelResponseBody() {
        }
        
        public FinnJournalposterGittNoekkelResponseBody(Framsikt.BL.Acos.JournalpostListe @return) {
            this.@return = @return;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class FinnJournalposterGittSaksmappeNoekkel {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="FinnJournalposterGittSaksmappeNoekkel", Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public Framsikt.BL.Acos.FinnJournalposterGittSaksmappeNoekkelBody Body;
        
        public FinnJournalposterGittSaksmappeNoekkel() {
        }
        
        public FinnJournalposterGittSaksmappeNoekkel(Framsikt.BL.Acos.FinnJournalposterGittSaksmappeNoekkelBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31")]
    public partial class FinnJournalposterGittSaksmappeNoekkelBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public Framsikt.BL.Acos.Saksnoekkel nokkel;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=1)]
        public bool returnerMerknad;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=2)]
        public bool returnerTilleggsinformasjon;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=3)]
        public bool returnerKorrespondansepart;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=4)]
        public bool returnerAvskrivning;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public Framsikt.BL.Acos.ArkivKontekst kontekst;
        
        public FinnJournalposterGittSaksmappeNoekkelBody() {
        }
        
        public FinnJournalposterGittSaksmappeNoekkelBody(Framsikt.BL.Acos.Saksnoekkel nokkel, bool returnerMerknad, bool returnerTilleggsinformasjon, bool returnerKorrespondansepart, bool returnerAvskrivning, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            this.nokkel = nokkel;
            this.returnerMerknad = returnerMerknad;
            this.returnerTilleggsinformasjon = returnerTilleggsinformasjon;
            this.returnerKorrespondansepart = returnerKorrespondansepart;
            this.returnerAvskrivning = returnerAvskrivning;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class FinnJournalposterGittSaksmappeNoekkelResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="FinnJournalposterGittSaksmappeNoekkelResponse", Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public Framsikt.BL.Acos.FinnJournalposterGittSaksmappeNoekkelResponseBody Body;
        
        public FinnJournalposterGittSaksmappeNoekkelResponse() {
        }
        
        public FinnJournalposterGittSaksmappeNoekkelResponse(Framsikt.BL.Acos.FinnJournalposterGittSaksmappeNoekkelResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31")]
    public partial class FinnJournalposterGittSaksmappeNoekkelResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public Framsikt.BL.Acos.JournalpostListe @return;
        
        public FinnJournalposterGittSaksmappeNoekkelResponseBody() {
        }
        
        public FinnJournalposterGittSaksmappeNoekkelResponseBody(Framsikt.BL.Acos.JournalpostListe @return) {
            this.@return = @return;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class FinnJournalposter {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="FinnJournalposter", Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public Framsikt.BL.Acos.FinnJournalposterBody Body;
        
        public FinnJournalposter() {
        }
        
        public FinnJournalposter(Framsikt.BL.Acos.FinnJournalposterBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31")]
    public partial class FinnJournalposterBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public Framsikt.BL.Acos.SoekskriterieListe sok;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=1)]
        public bool returnerMerknad;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=2)]
        public bool returnerTilleggsinformasjon;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=3)]
        public bool returnerKorrespondansepart;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=4)]
        public bool returnerAvskrivning;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public Framsikt.BL.Acos.ArkivKontekst kontekst;
        
        public FinnJournalposterBody() {
        }
        
        public FinnJournalposterBody(Framsikt.BL.Acos.SoekskriterieListe sok, bool returnerMerknad, bool returnerTilleggsinformasjon, bool returnerKorrespondansepart, bool returnerAvskrivning, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            this.sok = sok;
            this.returnerMerknad = returnerMerknad;
            this.returnerTilleggsinformasjon = returnerTilleggsinformasjon;
            this.returnerKorrespondansepart = returnerKorrespondansepart;
            this.returnerAvskrivning = returnerAvskrivning;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class FinnJournalposterResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="FinnJournalposterResponse", Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public Framsikt.BL.Acos.FinnJournalposterResponseBody Body;
        
        public FinnJournalposterResponse() {
        }
        
        public FinnJournalposterResponse(Framsikt.BL.Acos.FinnJournalposterResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31")]
    public partial class FinnJournalposterResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public Framsikt.BL.Acos.JournalpostListe @return;
        
        public FinnJournalposterResponseBody() {
        }
        
        public FinnJournalposterResponseBody(Framsikt.BL.Acos.JournalpostListe @return) {
            this.@return = @return;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class FinnSaksmapperGittNoekkel {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="FinnSaksmapperGittNoekkel", Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public Framsikt.BL.Acos.FinnSaksmapperGittNoekkelBody Body;
        
        public FinnSaksmapperGittNoekkel() {
        }
        
        public FinnSaksmapperGittNoekkel(Framsikt.BL.Acos.FinnSaksmapperGittNoekkelBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31")]
    public partial class FinnSaksmapperGittNoekkelBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public Framsikt.BL.Acos.Saksnoekkel nokkel;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=1)]
        public bool returnerMerknad;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=2)]
        public bool returnerTilleggsinformasjon;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=3)]
        public bool returnerSakspart;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=4)]
        public bool returnerKlasse;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public Framsikt.BL.Acos.ArkivKontekst kontekst;
        
        public FinnSaksmapperGittNoekkelBody() {
        }
        
        public FinnSaksmapperGittNoekkelBody(Framsikt.BL.Acos.Saksnoekkel nokkel, bool returnerMerknad, bool returnerTilleggsinformasjon, bool returnerSakspart, bool returnerKlasse, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            this.nokkel = nokkel;
            this.returnerMerknad = returnerMerknad;
            this.returnerTilleggsinformasjon = returnerTilleggsinformasjon;
            this.returnerSakspart = returnerSakspart;
            this.returnerKlasse = returnerKlasse;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class FinnSaksmapperGittNoekkelResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="FinnSaksmapperGittNoekkelResponse", Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public Framsikt.BL.Acos.FinnSaksmapperGittNoekkelResponseBody Body;
        
        public FinnSaksmapperGittNoekkelResponse() {
        }
        
        public FinnSaksmapperGittNoekkelResponse(Framsikt.BL.Acos.FinnSaksmapperGittNoekkelResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31")]
    public partial class FinnSaksmapperGittNoekkelResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public Framsikt.BL.Acos.SaksmappeListe @return;
        
        public FinnSaksmapperGittNoekkelResponseBody() {
        }
        
        public FinnSaksmapperGittNoekkelResponseBody(Framsikt.BL.Acos.SaksmappeListe @return) {
            this.@return = @return;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class FinnSaksmapper {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="FinnSaksmapper", Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public Framsikt.BL.Acos.FinnSaksmapperBody Body;
        
        public FinnSaksmapper() {
        }
        
        public FinnSaksmapper(Framsikt.BL.Acos.FinnSaksmapperBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31")]
    public partial class FinnSaksmapperBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public Framsikt.BL.Acos.SoekskriterieListe sok;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=1)]
        public bool returnerMerknad;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=2)]
        public bool returnerTilleggsinformasjon;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=3)]
        public bool returnerSakspart;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=4)]
        public bool returnerKlasse;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public Framsikt.BL.Acos.ArkivKontekst kontekst;
        
        public FinnSaksmapperBody() {
        }
        
        public FinnSaksmapperBody(Framsikt.BL.Acos.SoekskriterieListe sok, bool returnerMerknad, bool returnerTilleggsinformasjon, bool returnerSakspart, bool returnerKlasse, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            this.sok = sok;
            this.returnerMerknad = returnerMerknad;
            this.returnerTilleggsinformasjon = returnerTilleggsinformasjon;
            this.returnerSakspart = returnerSakspart;
            this.returnerKlasse = returnerKlasse;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class FinnSaksmapperResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="FinnSaksmapperResponse", Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public Framsikt.BL.Acos.FinnSaksmapperResponseBody Body;
        
        public FinnSaksmapperResponse() {
        }
        
        public FinnSaksmapperResponse(Framsikt.BL.Acos.FinnSaksmapperResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31")]
    public partial class FinnSaksmapperResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public Framsikt.BL.Acos.SaksmappeListe @return;
        
        public FinnSaksmapperResponseBody() {
        }
        
        public FinnSaksmapperResponseBody(Framsikt.BL.Acos.SaksmappeListe @return) {
            this.@return = @return;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class FinnDokumenterGittJournalpostnoekkel {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="FinnDokumenterGittJournalpostnoekkel", Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public Framsikt.BL.Acos.FinnDokumenterGittJournalpostnoekkelBody Body;
        
        public FinnDokumenterGittJournalpostnoekkel() {
        }
        
        public FinnDokumenterGittJournalpostnoekkel(Framsikt.BL.Acos.FinnDokumenterGittJournalpostnoekkelBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31")]
    public partial class FinnDokumenterGittJournalpostnoekkelBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public Framsikt.BL.Acos.Journpostnoekkel journpostnokkel;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=1)]
        public bool returnerFil;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public Framsikt.BL.Acos.ArkivKontekst kontekst;
        
        public FinnDokumenterGittJournalpostnoekkelBody() {
        }
        
        public FinnDokumenterGittJournalpostnoekkelBody(Framsikt.BL.Acos.Journpostnoekkel journpostnokkel, bool returnerFil, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            this.journpostnokkel = journpostnokkel;
            this.returnerFil = returnerFil;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class FinnDokumenterGittJournalpostnoekkelResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="FinnDokumenterGittJournalpostnoekkelResponse", Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public Framsikt.BL.Acos.FinnDokumenterGittJournalpostnoekkelResponseBody Body;
        
        public FinnDokumenterGittJournalpostnoekkelResponse() {
        }
        
        public FinnDokumenterGittJournalpostnoekkelResponse(Framsikt.BL.Acos.FinnDokumenterGittJournalpostnoekkelResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31")]
    public partial class FinnDokumenterGittJournalpostnoekkelResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public Framsikt.BL.Acos.DokumentListe @return;
        
        public FinnDokumenterGittJournalpostnoekkelResponseBody() {
        }
        
        public FinnDokumenterGittJournalpostnoekkelResponseBody(Framsikt.BL.Acos.DokumentListe @return) {
            this.@return = @return;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class FinnDokumenterGittSaksnoekkel {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="FinnDokumenterGittSaksnoekkel", Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public Framsikt.BL.Acos.FinnDokumenterGittSaksnoekkelBody Body;
        
        public FinnDokumenterGittSaksnoekkel() {
        }
        
        public FinnDokumenterGittSaksnoekkel(Framsikt.BL.Acos.FinnDokumenterGittSaksnoekkelBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31")]
    public partial class FinnDokumenterGittSaksnoekkelBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public Framsikt.BL.Acos.Saksnoekkel saksnokkel;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=1)]
        public bool returnerFil;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public Framsikt.BL.Acos.ArkivKontekst kontekst;
        
        public FinnDokumenterGittSaksnoekkelBody() {
        }
        
        public FinnDokumenterGittSaksnoekkelBody(Framsikt.BL.Acos.Saksnoekkel saksnokkel, bool returnerFil, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            this.saksnokkel = saksnokkel;
            this.returnerFil = returnerFil;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class FinnDokumenterGittSaksnoekkelResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="FinnDokumenterGittSaksnoekkelResponse", Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public Framsikt.BL.Acos.FinnDokumenterGittSaksnoekkelResponseBody Body;
        
        public FinnDokumenterGittSaksnoekkelResponse() {
        }
        
        public FinnDokumenterGittSaksnoekkelResponse(Framsikt.BL.Acos.FinnDokumenterGittSaksnoekkelResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31")]
    public partial class FinnDokumenterGittSaksnoekkelResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public Framsikt.BL.Acos.DokumentListe @return;
        
        public FinnDokumenterGittSaksnoekkelResponseBody() {
        }
        
        public FinnDokumenterGittSaksnoekkelResponseBody(Framsikt.BL.Acos.DokumentListe @return) {
            this.@return = @return;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class FinnDokumenter {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="FinnDokumenter", Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public Framsikt.BL.Acos.FinnDokumenterBody Body;
        
        public FinnDokumenter() {
        }
        
        public FinnDokumenter(Framsikt.BL.Acos.FinnDokumenterBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31")]
    public partial class FinnDokumenterBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public Framsikt.BL.Acos.SoekskriterieListe sok;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=1)]
        public bool returnerFil;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public Framsikt.BL.Acos.ArkivKontekst kontekst;
        
        public FinnDokumenterBody() {
        }
        
        public FinnDokumenterBody(Framsikt.BL.Acos.SoekskriterieListe sok, bool returnerFil, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            this.sok = sok;
            this.returnerFil = returnerFil;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class FinnDokumenterResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="FinnDokumenterResponse", Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public Framsikt.BL.Acos.FinnDokumenterResponseBody Body;
        
        public FinnDokumenterResponse() {
        }
        
        public FinnDokumenterResponse(Framsikt.BL.Acos.FinnDokumenterResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31")]
    public partial class FinnDokumenterResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public Framsikt.BL.Acos.DokumentListe @return;
        
        public FinnDokumenterResponseBody() {
        }
        
        public FinnDokumenterResponseBody(Framsikt.BL.Acos.DokumentListe @return) {
            this.@return = @return;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public interface ArkivInnsynPortChannel : Framsikt.BL.Acos.ArkivInnsynPort, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public partial class ArkivInnsynPortClient : System.ServiceModel.ClientBase<Framsikt.BL.Acos.ArkivInnsynPort>, Framsikt.BL.Acos.ArkivInnsynPort {
        
        public ArkivInnsynPortClient() {
        }
        
        public ArkivInnsynPortClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public ArkivInnsynPortClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public ArkivInnsynPortClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public ArkivInnsynPortClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Framsikt.BL.Acos.HentKodelisteResponse Framsikt.BL.Acos.ArkivInnsynPort.HentKodeliste(Framsikt.BL.Acos.HentKodeliste request) {
            return base.Channel.HentKodeliste(request);
        }
        
        public Framsikt.BL.Acos.KodeListe HentKodeliste(string kodelistenavn, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            Framsikt.BL.Acos.HentKodeliste inValue = new Framsikt.BL.Acos.HentKodeliste();
            inValue.Body = new Framsikt.BL.Acos.HentKodelisteBody();
            inValue.Body.kodelistenavn = kodelistenavn;
            inValue.Body.kontekst = kontekst;
            Framsikt.BL.Acos.HentKodelisteResponse retVal = ((Framsikt.BL.Acos.ArkivInnsynPort)(this)).HentKodeliste(inValue);
            return retVal.Body.@return;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Framsikt.BL.Acos.HentKodelisteResponse> Framsikt.BL.Acos.ArkivInnsynPort.HentKodelisteAsync(Framsikt.BL.Acos.HentKodeliste request) {
            return base.Channel.HentKodelisteAsync(request);
        }
        
        public System.Threading.Tasks.Task<Framsikt.BL.Acos.HentKodelisteResponse> HentKodelisteAsync(string kodelistenavn, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            Framsikt.BL.Acos.HentKodeliste inValue = new Framsikt.BL.Acos.HentKodeliste();
            inValue.Body = new Framsikt.BL.Acos.HentKodelisteBody();
            inValue.Body.kodelistenavn = kodelistenavn;
            inValue.Body.kontekst = kontekst;
            return ((Framsikt.BL.Acos.ArkivInnsynPort)(this)).HentKodelisteAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Framsikt.BL.Acos.HentFilResponse Framsikt.BL.Acos.ArkivInnsynPort.HentFil(Framsikt.BL.Acos.HentFil request) {
            return base.Channel.HentFil(request);
        }
        
        public Framsikt.BL.Acos.Fil HentFil(Framsikt.BL.Acos.SystemID systemid, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            Framsikt.BL.Acos.HentFil inValue = new Framsikt.BL.Acos.HentFil();
            inValue.Body = new Framsikt.BL.Acos.HentFilBody();
            inValue.Body.systemid = systemid;
            inValue.Body.kontekst = kontekst;
            Framsikt.BL.Acos.HentFilResponse retVal = ((Framsikt.BL.Acos.ArkivInnsynPort)(this)).HentFil(inValue);
            return retVal.Body.@return;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Framsikt.BL.Acos.HentFilResponse> Framsikt.BL.Acos.ArkivInnsynPort.HentFilAsync(Framsikt.BL.Acos.HentFil request) {
            return base.Channel.HentFilAsync(request);
        }
        
        public System.Threading.Tasks.Task<Framsikt.BL.Acos.HentFilResponse> HentFilAsync(Framsikt.BL.Acos.SystemID systemid, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            Framsikt.BL.Acos.HentFil inValue = new Framsikt.BL.Acos.HentFil();
            inValue.Body = new Framsikt.BL.Acos.HentFilBody();
            inValue.Body.systemid = systemid;
            inValue.Body.kontekst = kontekst;
            return ((Framsikt.BL.Acos.ArkivInnsynPort)(this)).HentFilAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Framsikt.BL.Acos.FinnDokumenttyperResponse Framsikt.BL.Acos.ArkivInnsynPort.FinnDokumenttyper(Framsikt.BL.Acos.FinnDokumenttyper request) {
            return base.Channel.FinnDokumenttyper(request);
        }
        
        public Framsikt.BL.Acos.DokumenttypeListe FinnDokumenttyper(Framsikt.BL.Acos.Saksnoekkel saksnokkel, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            Framsikt.BL.Acos.FinnDokumenttyper inValue = new Framsikt.BL.Acos.FinnDokumenttyper();
            inValue.Body = new Framsikt.BL.Acos.FinnDokumenttyperBody();
            inValue.Body.saksnokkel = saksnokkel;
            inValue.Body.kontekst = kontekst;
            Framsikt.BL.Acos.FinnDokumenttyperResponse retVal = ((Framsikt.BL.Acos.ArkivInnsynPort)(this)).FinnDokumenttyper(inValue);
            return retVal.Body.@return;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnDokumenttyperResponse> Framsikt.BL.Acos.ArkivInnsynPort.FinnDokumenttyperAsync(Framsikt.BL.Acos.FinnDokumenttyper request) {
            return base.Channel.FinnDokumenttyperAsync(request);
        }
        
        public System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnDokumenttyperResponse> FinnDokumenttyperAsync(Framsikt.BL.Acos.Saksnoekkel saksnokkel, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            Framsikt.BL.Acos.FinnDokumenttyper inValue = new Framsikt.BL.Acos.FinnDokumenttyper();
            inValue.Body = new Framsikt.BL.Acos.FinnDokumenttyperBody();
            inValue.Body.saksnokkel = saksnokkel;
            inValue.Body.kontekst = kontekst;
            return ((Framsikt.BL.Acos.ArkivInnsynPort)(this)).FinnDokumenttyperAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Framsikt.BL.Acos.FinnJournalposterGittNoekkelResponse Framsikt.BL.Acos.ArkivInnsynPort.FinnJournalposterGittNoekkel(Framsikt.BL.Acos.FinnJournalposterGittNoekkel request) {
            return base.Channel.FinnJournalposterGittNoekkel(request);
        }
        
        public Framsikt.BL.Acos.JournalpostListe FinnJournalposterGittNoekkel(Framsikt.BL.Acos.Journpostnoekkel nokkel, bool returnerMerknad, bool returnerTilleggsinformasjon, bool returnerKorrespondansepart, bool returnerAvskrivning, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            Framsikt.BL.Acos.FinnJournalposterGittNoekkel inValue = new Framsikt.BL.Acos.FinnJournalposterGittNoekkel();
            inValue.Body = new Framsikt.BL.Acos.FinnJournalposterGittNoekkelBody();
            inValue.Body.nokkel = nokkel;
            inValue.Body.returnerMerknad = returnerMerknad;
            inValue.Body.returnerTilleggsinformasjon = returnerTilleggsinformasjon;
            inValue.Body.returnerKorrespondansepart = returnerKorrespondansepart;
            inValue.Body.returnerAvskrivning = returnerAvskrivning;
            inValue.Body.kontekst = kontekst;
            Framsikt.BL.Acos.FinnJournalposterGittNoekkelResponse retVal = ((Framsikt.BL.Acos.ArkivInnsynPort)(this)).FinnJournalposterGittNoekkel(inValue);
            return retVal.Body.@return;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnJournalposterGittNoekkelResponse> Framsikt.BL.Acos.ArkivInnsynPort.FinnJournalposterGittNoekkelAsync(Framsikt.BL.Acos.FinnJournalposterGittNoekkel request) {
            return base.Channel.FinnJournalposterGittNoekkelAsync(request);
        }
        
        public System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnJournalposterGittNoekkelResponse> FinnJournalposterGittNoekkelAsync(Framsikt.BL.Acos.Journpostnoekkel nokkel, bool returnerMerknad, bool returnerTilleggsinformasjon, bool returnerKorrespondansepart, bool returnerAvskrivning, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            Framsikt.BL.Acos.FinnJournalposterGittNoekkel inValue = new Framsikt.BL.Acos.FinnJournalposterGittNoekkel();
            inValue.Body = new Framsikt.BL.Acos.FinnJournalposterGittNoekkelBody();
            inValue.Body.nokkel = nokkel;
            inValue.Body.returnerMerknad = returnerMerknad;
            inValue.Body.returnerTilleggsinformasjon = returnerTilleggsinformasjon;
            inValue.Body.returnerKorrespondansepart = returnerKorrespondansepart;
            inValue.Body.returnerAvskrivning = returnerAvskrivning;
            inValue.Body.kontekst = kontekst;
            return ((Framsikt.BL.Acos.ArkivInnsynPort)(this)).FinnJournalposterGittNoekkelAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Framsikt.BL.Acos.FinnJournalposterGittSaksmappeNoekkelResponse Framsikt.BL.Acos.ArkivInnsynPort.FinnJournalposterGittSaksmappeNoekkel(Framsikt.BL.Acos.FinnJournalposterGittSaksmappeNoekkel request) {
            return base.Channel.FinnJournalposterGittSaksmappeNoekkel(request);
        }
        
        public Framsikt.BL.Acos.JournalpostListe FinnJournalposterGittSaksmappeNoekkel(Framsikt.BL.Acos.Saksnoekkel nokkel, bool returnerMerknad, bool returnerTilleggsinformasjon, bool returnerKorrespondansepart, bool returnerAvskrivning, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            Framsikt.BL.Acos.FinnJournalposterGittSaksmappeNoekkel inValue = new Framsikt.BL.Acos.FinnJournalposterGittSaksmappeNoekkel();
            inValue.Body = new Framsikt.BL.Acos.FinnJournalposterGittSaksmappeNoekkelBody();
            inValue.Body.nokkel = nokkel;
            inValue.Body.returnerMerknad = returnerMerknad;
            inValue.Body.returnerTilleggsinformasjon = returnerTilleggsinformasjon;
            inValue.Body.returnerKorrespondansepart = returnerKorrespondansepart;
            inValue.Body.returnerAvskrivning = returnerAvskrivning;
            inValue.Body.kontekst = kontekst;
            Framsikt.BL.Acos.FinnJournalposterGittSaksmappeNoekkelResponse retVal = ((Framsikt.BL.Acos.ArkivInnsynPort)(this)).FinnJournalposterGittSaksmappeNoekkel(inValue);
            return retVal.Body.@return;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnJournalposterGittSaksmappeNoekkelResponse> Framsikt.BL.Acos.ArkivInnsynPort.FinnJournalposterGittSaksmappeNoekkelAsync(Framsikt.BL.Acos.FinnJournalposterGittSaksmappeNoekkel request) {
            return base.Channel.FinnJournalposterGittSaksmappeNoekkelAsync(request);
        }
        
        public System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnJournalposterGittSaksmappeNoekkelResponse> FinnJournalposterGittSaksmappeNoekkelAsync(Framsikt.BL.Acos.Saksnoekkel nokkel, bool returnerMerknad, bool returnerTilleggsinformasjon, bool returnerKorrespondansepart, bool returnerAvskrivning, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            Framsikt.BL.Acos.FinnJournalposterGittSaksmappeNoekkel inValue = new Framsikt.BL.Acos.FinnJournalposterGittSaksmappeNoekkel();
            inValue.Body = new Framsikt.BL.Acos.FinnJournalposterGittSaksmappeNoekkelBody();
            inValue.Body.nokkel = nokkel;
            inValue.Body.returnerMerknad = returnerMerknad;
            inValue.Body.returnerTilleggsinformasjon = returnerTilleggsinformasjon;
            inValue.Body.returnerKorrespondansepart = returnerKorrespondansepart;
            inValue.Body.returnerAvskrivning = returnerAvskrivning;
            inValue.Body.kontekst = kontekst;
            return ((Framsikt.BL.Acos.ArkivInnsynPort)(this)).FinnJournalposterGittSaksmappeNoekkelAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Framsikt.BL.Acos.FinnJournalposterResponse Framsikt.BL.Acos.ArkivInnsynPort.FinnJournalposter(Framsikt.BL.Acos.FinnJournalposter request) {
            return base.Channel.FinnJournalposter(request);
        }
        
        public Framsikt.BL.Acos.JournalpostListe FinnJournalposter(Framsikt.BL.Acos.SoekskriterieListe sok, bool returnerMerknad, bool returnerTilleggsinformasjon, bool returnerKorrespondansepart, bool returnerAvskrivning, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            Framsikt.BL.Acos.FinnJournalposter inValue = new Framsikt.BL.Acos.FinnJournalposter();
            inValue.Body = new Framsikt.BL.Acos.FinnJournalposterBody();
            inValue.Body.sok = sok;
            inValue.Body.returnerMerknad = returnerMerknad;
            inValue.Body.returnerTilleggsinformasjon = returnerTilleggsinformasjon;
            inValue.Body.returnerKorrespondansepart = returnerKorrespondansepart;
            inValue.Body.returnerAvskrivning = returnerAvskrivning;
            inValue.Body.kontekst = kontekst;
            Framsikt.BL.Acos.FinnJournalposterResponse retVal = ((Framsikt.BL.Acos.ArkivInnsynPort)(this)).FinnJournalposter(inValue);
            return retVal.Body.@return;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnJournalposterResponse> Framsikt.BL.Acos.ArkivInnsynPort.FinnJournalposterAsync(Framsikt.BL.Acos.FinnJournalposter request) {
            return base.Channel.FinnJournalposterAsync(request);
        }
        
        public System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnJournalposterResponse> FinnJournalposterAsync(Framsikt.BL.Acos.SoekskriterieListe sok, bool returnerMerknad, bool returnerTilleggsinformasjon, bool returnerKorrespondansepart, bool returnerAvskrivning, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            Framsikt.BL.Acos.FinnJournalposter inValue = new Framsikt.BL.Acos.FinnJournalposter();
            inValue.Body = new Framsikt.BL.Acos.FinnJournalposterBody();
            inValue.Body.sok = sok;
            inValue.Body.returnerMerknad = returnerMerknad;
            inValue.Body.returnerTilleggsinformasjon = returnerTilleggsinformasjon;
            inValue.Body.returnerKorrespondansepart = returnerKorrespondansepart;
            inValue.Body.returnerAvskrivning = returnerAvskrivning;
            inValue.Body.kontekst = kontekst;
            return ((Framsikt.BL.Acos.ArkivInnsynPort)(this)).FinnJournalposterAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Framsikt.BL.Acos.FinnSaksmapperGittNoekkelResponse Framsikt.BL.Acos.ArkivInnsynPort.FinnSaksmapperGittNoekkel(Framsikt.BL.Acos.FinnSaksmapperGittNoekkel request) {
            return base.Channel.FinnSaksmapperGittNoekkel(request);
        }
        
        public Framsikt.BL.Acos.SaksmappeListe FinnSaksmapperGittNoekkel(Framsikt.BL.Acos.Saksnoekkel nokkel, bool returnerMerknad, bool returnerTilleggsinformasjon, bool returnerSakspart, bool returnerKlasse, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            Framsikt.BL.Acos.FinnSaksmapperGittNoekkel inValue = new Framsikt.BL.Acos.FinnSaksmapperGittNoekkel();
            inValue.Body = new Framsikt.BL.Acos.FinnSaksmapperGittNoekkelBody();
            inValue.Body.nokkel = nokkel;
            inValue.Body.returnerMerknad = returnerMerknad;
            inValue.Body.returnerTilleggsinformasjon = returnerTilleggsinformasjon;
            inValue.Body.returnerSakspart = returnerSakspart;
            inValue.Body.returnerKlasse = returnerKlasse;
            inValue.Body.kontekst = kontekst;
            Framsikt.BL.Acos.FinnSaksmapperGittNoekkelResponse retVal = ((Framsikt.BL.Acos.ArkivInnsynPort)(this)).FinnSaksmapperGittNoekkel(inValue);
            return retVal.Body.@return;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnSaksmapperGittNoekkelResponse> Framsikt.BL.Acos.ArkivInnsynPort.FinnSaksmapperGittNoekkelAsync(Framsikt.BL.Acos.FinnSaksmapperGittNoekkel request) {
            return base.Channel.FinnSaksmapperGittNoekkelAsync(request);
        }
        
        public System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnSaksmapperGittNoekkelResponse> FinnSaksmapperGittNoekkelAsync(Framsikt.BL.Acos.Saksnoekkel nokkel, bool returnerMerknad, bool returnerTilleggsinformasjon, bool returnerSakspart, bool returnerKlasse, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            Framsikt.BL.Acos.FinnSaksmapperGittNoekkel inValue = new Framsikt.BL.Acos.FinnSaksmapperGittNoekkel();
            inValue.Body = new Framsikt.BL.Acos.FinnSaksmapperGittNoekkelBody();
            inValue.Body.nokkel = nokkel;
            inValue.Body.returnerMerknad = returnerMerknad;
            inValue.Body.returnerTilleggsinformasjon = returnerTilleggsinformasjon;
            inValue.Body.returnerSakspart = returnerSakspart;
            inValue.Body.returnerKlasse = returnerKlasse;
            inValue.Body.kontekst = kontekst;
            return ((Framsikt.BL.Acos.ArkivInnsynPort)(this)).FinnSaksmapperGittNoekkelAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Framsikt.BL.Acos.FinnSaksmapperResponse Framsikt.BL.Acos.ArkivInnsynPort.FinnSaksmapper(Framsikt.BL.Acos.FinnSaksmapper request) {
            return base.Channel.FinnSaksmapper(request);
        }
        
        public Framsikt.BL.Acos.SaksmappeListe FinnSaksmapper(Framsikt.BL.Acos.SoekskriterieListe sok, bool returnerMerknad, bool returnerTilleggsinformasjon, bool returnerSakspart, bool returnerKlasse, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            Framsikt.BL.Acos.FinnSaksmapper inValue = new Framsikt.BL.Acos.FinnSaksmapper();
            inValue.Body = new Framsikt.BL.Acos.FinnSaksmapperBody();
            inValue.Body.sok = sok;
            inValue.Body.returnerMerknad = returnerMerknad;
            inValue.Body.returnerTilleggsinformasjon = returnerTilleggsinformasjon;
            inValue.Body.returnerSakspart = returnerSakspart;
            inValue.Body.returnerKlasse = returnerKlasse;
            inValue.Body.kontekst = kontekst;
            Framsikt.BL.Acos.FinnSaksmapperResponse retVal = ((Framsikt.BL.Acos.ArkivInnsynPort)(this)).FinnSaksmapper(inValue);
            return retVal.Body.@return;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnSaksmapperResponse> Framsikt.BL.Acos.ArkivInnsynPort.FinnSaksmapperAsync(Framsikt.BL.Acos.FinnSaksmapper request) {
            return base.Channel.FinnSaksmapperAsync(request);
        }
        
        public System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnSaksmapperResponse> FinnSaksmapperAsync(Framsikt.BL.Acos.SoekskriterieListe sok, bool returnerMerknad, bool returnerTilleggsinformasjon, bool returnerSakspart, bool returnerKlasse, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            Framsikt.BL.Acos.FinnSaksmapper inValue = new Framsikt.BL.Acos.FinnSaksmapper();
            inValue.Body = new Framsikt.BL.Acos.FinnSaksmapperBody();
            inValue.Body.sok = sok;
            inValue.Body.returnerMerknad = returnerMerknad;
            inValue.Body.returnerTilleggsinformasjon = returnerTilleggsinformasjon;
            inValue.Body.returnerSakspart = returnerSakspart;
            inValue.Body.returnerKlasse = returnerKlasse;
            inValue.Body.kontekst = kontekst;
            return ((Framsikt.BL.Acos.ArkivInnsynPort)(this)).FinnSaksmapperAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Framsikt.BL.Acos.FinnDokumenterGittJournalpostnoekkelResponse Framsikt.BL.Acos.ArkivInnsynPort.FinnDokumenterGittJournalpostnoekkel(Framsikt.BL.Acos.FinnDokumenterGittJournalpostnoekkel request) {
            return base.Channel.FinnDokumenterGittJournalpostnoekkel(request);
        }
        
        public Framsikt.BL.Acos.DokumentListe FinnDokumenterGittJournalpostnoekkel(Framsikt.BL.Acos.Journpostnoekkel journpostnokkel, bool returnerFil, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            Framsikt.BL.Acos.FinnDokumenterGittJournalpostnoekkel inValue = new Framsikt.BL.Acos.FinnDokumenterGittJournalpostnoekkel();
            inValue.Body = new Framsikt.BL.Acos.FinnDokumenterGittJournalpostnoekkelBody();
            inValue.Body.journpostnokkel = journpostnokkel;
            inValue.Body.returnerFil = returnerFil;
            inValue.Body.kontekst = kontekst;
            Framsikt.BL.Acos.FinnDokumenterGittJournalpostnoekkelResponse retVal = ((Framsikt.BL.Acos.ArkivInnsynPort)(this)).FinnDokumenterGittJournalpostnoekkel(inValue);
            return retVal.Body.@return;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnDokumenterGittJournalpostnoekkelResponse> Framsikt.BL.Acos.ArkivInnsynPort.FinnDokumenterGittJournalpostnoekkelAsync(Framsikt.BL.Acos.FinnDokumenterGittJournalpostnoekkel request) {
            return base.Channel.FinnDokumenterGittJournalpostnoekkelAsync(request);
        }
        
        public System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnDokumenterGittJournalpostnoekkelResponse> FinnDokumenterGittJournalpostnoekkelAsync(Framsikt.BL.Acos.Journpostnoekkel journpostnokkel, bool returnerFil, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            Framsikt.BL.Acos.FinnDokumenterGittJournalpostnoekkel inValue = new Framsikt.BL.Acos.FinnDokumenterGittJournalpostnoekkel();
            inValue.Body = new Framsikt.BL.Acos.FinnDokumenterGittJournalpostnoekkelBody();
            inValue.Body.journpostnokkel = journpostnokkel;
            inValue.Body.returnerFil = returnerFil;
            inValue.Body.kontekst = kontekst;
            return ((Framsikt.BL.Acos.ArkivInnsynPort)(this)).FinnDokumenterGittJournalpostnoekkelAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Framsikt.BL.Acos.FinnDokumenterGittSaksnoekkelResponse Framsikt.BL.Acos.ArkivInnsynPort.FinnDokumenterGittSaksnoekkel(Framsikt.BL.Acos.FinnDokumenterGittSaksnoekkel request) {
            return base.Channel.FinnDokumenterGittSaksnoekkel(request);
        }
        
        public Framsikt.BL.Acos.DokumentListe FinnDokumenterGittSaksnoekkel(Framsikt.BL.Acos.Saksnoekkel saksnokkel, bool returnerFil, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            Framsikt.BL.Acos.FinnDokumenterGittSaksnoekkel inValue = new Framsikt.BL.Acos.FinnDokumenterGittSaksnoekkel();
            inValue.Body = new Framsikt.BL.Acos.FinnDokumenterGittSaksnoekkelBody();
            inValue.Body.saksnokkel = saksnokkel;
            inValue.Body.returnerFil = returnerFil;
            inValue.Body.kontekst = kontekst;
            Framsikt.BL.Acos.FinnDokumenterGittSaksnoekkelResponse retVal = ((Framsikt.BL.Acos.ArkivInnsynPort)(this)).FinnDokumenterGittSaksnoekkel(inValue);
            return retVal.Body.@return;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnDokumenterGittSaksnoekkelResponse> Framsikt.BL.Acos.ArkivInnsynPort.FinnDokumenterGittSaksnoekkelAsync(Framsikt.BL.Acos.FinnDokumenterGittSaksnoekkel request) {
            return base.Channel.FinnDokumenterGittSaksnoekkelAsync(request);
        }
        
        public System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnDokumenterGittSaksnoekkelResponse> FinnDokumenterGittSaksnoekkelAsync(Framsikt.BL.Acos.Saksnoekkel saksnokkel, bool returnerFil, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            Framsikt.BL.Acos.FinnDokumenterGittSaksnoekkel inValue = new Framsikt.BL.Acos.FinnDokumenterGittSaksnoekkel();
            inValue.Body = new Framsikt.BL.Acos.FinnDokumenterGittSaksnoekkelBody();
            inValue.Body.saksnokkel = saksnokkel;
            inValue.Body.returnerFil = returnerFil;
            inValue.Body.kontekst = kontekst;
            return ((Framsikt.BL.Acos.ArkivInnsynPort)(this)).FinnDokumenterGittSaksnoekkelAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Framsikt.BL.Acos.FinnDokumenterResponse Framsikt.BL.Acos.ArkivInnsynPort.FinnDokumenter(Framsikt.BL.Acos.FinnDokumenter request) {
            return base.Channel.FinnDokumenter(request);
        }
        
        public Framsikt.BL.Acos.DokumentListe FinnDokumenter(Framsikt.BL.Acos.SoekskriterieListe sok, bool returnerFil, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            Framsikt.BL.Acos.FinnDokumenter inValue = new Framsikt.BL.Acos.FinnDokumenter();
            inValue.Body = new Framsikt.BL.Acos.FinnDokumenterBody();
            inValue.Body.sok = sok;
            inValue.Body.returnerFil = returnerFil;
            inValue.Body.kontekst = kontekst;
            Framsikt.BL.Acos.FinnDokumenterResponse retVal = ((Framsikt.BL.Acos.ArkivInnsynPort)(this)).FinnDokumenter(inValue);
            return retVal.Body.@return;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnDokumenterResponse> Framsikt.BL.Acos.ArkivInnsynPort.FinnDokumenterAsync(Framsikt.BL.Acos.FinnDokumenter request) {
            return base.Channel.FinnDokumenterAsync(request);
        }
        
        public System.Threading.Tasks.Task<Framsikt.BL.Acos.FinnDokumenterResponse> FinnDokumenterAsync(Framsikt.BL.Acos.SoekskriterieListe sok, bool returnerFil, Framsikt.BL.Acos.ArkivKontekst kontekst) {
            Framsikt.BL.Acos.FinnDokumenter inValue = new Framsikt.BL.Acos.FinnDokumenter();
            inValue.Body = new Framsikt.BL.Acos.FinnDokumenterBody();
            inValue.Body.sok = sok;
            inValue.Body.returnerFil = returnerFil;
            inValue.Body.kontekst = kontekst;
            return ((Framsikt.BL.Acos.ArkivInnsynPort)(this)).FinnDokumenterAsync(inValue);
        }
    }
}
