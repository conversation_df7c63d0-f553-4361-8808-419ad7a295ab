#pragma warning disable CS8629

#pragma warning disable CS8600
#pragma warning disable CS8601
#pragma warning disable CS8602
#pragma warning disable CS8603
#pragma warning disable CS8604
#pragma warning disable CS8625

using Framsikt.BL.Helpers;
using Framsikt.BL.Helpers.CkEditorHelpers;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using System.Data;
using System.Linq.Dynamic.Core;

namespace Framsikt.BL
{
    public partial class ConsequenceAdjustedBudget : IConsequenceAdjustedBudget
    {
        private readonly IUtility _utility;
        private readonly IFinUtility _finUtility;
        private readonly IPopulationStatisticsData _popStatistics;
        private readonly IBackendRequest _backendJob;
        private readonly List<string> _errors;
        private readonly IStaffPlanning _staffPlanning;
        private readonly IAppDataCache _cache;
        private readonly ICKEditorExtensions _editorExtensions;
        public int saGridWidth = 0;

        public ConsequenceAdjustedBudget(IUtility util, IPopulationStatisticsData popStatistics,
            IBackendRequest beJob, IStaffPlanning staff, IAppDataCache cache, IFinUtility finUtility, ICKEditorExtensions editorExtensions)
        {
            _utility = util;
            _staffPlanning = staff;
            _popStatistics = popStatistics;
            _backendJob = beJob;
            _errors = new List<string>();
            _cache = cache;
            _finUtility = finUtility;
            _editorExtensions = editorExtensions ?? throw new ArgumentNullException(nameof(editorExtensions));
        }

        private async Task<List<clsServiceAreaData>> GetFinancialDataWithServiceAreaAsync(string userID, string section, int BudgetYear)
        {
            return await GetFinancialDataWithoutServiceAreaAsync(userID, section, false, BudgetYear);
        }

        private async Task<List<clsServiceAreaData>> GetFinancialDataWithoutServiceAreaAsync(string userID, string section, bool showNewActions, int BudgetYear)
        {
            int budgetYear = BudgetYear;
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userID, _utility.GetForecastPeriod(budgetYear, 1));
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();

            List<clsOrgStructure> lstOrgStructure = await _utility.GetTenantOrgStructureAsync(orgVersionContent, userID);
            clsOrgStructureLevelDetails tenantOrgLevelDetails = _utility.GetTenantOrgLevelDetails(lstOrgStructure);

            var dataset = new List<clsServiceAreaData>();

            if (string.IsNullOrEmpty(section))
            {
                dataset = await (from td in consequenceAdjustedBudgetDbContext.tfp_trans_detail
                           join th in consequenceAdjustedBudgetDbContext.tfp_trans_header on new { a = td.fk_tenant_id, b = td.fk_action_id }
                                                                                      equals new { a = th.fk_tenant_id, b = th.pk_action_id }
                                                                                      //join gat in consequenceAdjustedBudgetDbContext.gmd_action_types on th.action_type equals gat.pk_action_type
                           where (td.fk_tenant_id == userDetails.tenant_id && th.fk_tenant_id == userDetails.tenant_id && td.budget_year == budgetYear)
                           select new clsServiceAreaData
                           {
                               departmentCode = td.department_code,
                               functionCode = td.function_code,
                               actionType = th.action_type,//gat.pk_action_type,
                               actionId = th.pk_action_id,
                               description = th.description,
                               lineOrderId = th.line_order,
                               isManuallyAdded = th.isManuallyAdded,
                               BudgetYear = td.budget_year,
                               year1Amount = td.year_1_amount,
                               year2Amount = td.year_2_amount,
                               year3Amount = td.year_3_amount,
                               year4Amount = td.year_4_amount,
                               priority = th.priority == null ? 0 : th.priority.Value,
                               fk_area_id = th.fk_area_id == null ? 0 : th.fk_area_id.Value,
                               tag = th.tag,
                               updated = th.updated,
                               updatedBy = th.updated_by,
                               changeId = td.fk_change_id,
                           }).ToListAsync();
                dataset = _utility.AssignOrgIdAndServiceIdToResultSet(orgVersionContent, dataset, lstOrgStructure, tenantOrgLevelDetails.level1, tenantOrgLevelDetails.level2);
            }
            else
            {
                dataset = await (from td in consequenceAdjustedBudgetDbContext.tfp_trans_detail
                           join th in consequenceAdjustedBudgetDbContext.tfp_trans_header on new { a = td.fk_tenant_id, b = td.fk_action_id }
                                                                                      equals new { a = th.fk_tenant_id, b = th.pk_action_id }
                                                                                      //join gat in consequenceAdjustedBudgetDbContext.gmd_action_types on th.action_type equals gat.pk_action_type
                           where (td.fk_tenant_id == userDetails.tenant_id && th.fk_tenant_id == userDetails.tenant_id && td.budget_year == budgetYear &&
                           (section.ToLower() == clsConstants.ActionType.DistributionAndOperation.ToString().ToLower() ? (th.action_type >= 5 && th.action_type <= 20 && th.action_type != 100) :
                            section.ToLower() == clsConstants.ActionType.FinancialPlan.ToString().ToLower() ? (th.action_type >= 5 && th.action_type != 100) :
                            section.ToLower() == clsConstants.ActionType.FreeIncomeAvailable.ToString().ToLower() ? (th.action_type == 1) :
                            section.ToLower() == clsConstants.ActionType.FinancialIncomeAndExpenses.ToString().ToLower() ? (th.action_type == 2) :
                            section.ToLower() == clsConstants.ActionType.Provisions.ToString().ToLower() ? (th.action_type == 3) :
                            section.ToLower() == clsConstants.ActionType.TransferredtoInvestmentAccounts.ToString().ToLower() ? (th.action_type == 4) :
                            section.ToLower() == clsConstants.ActionType.OriginalAndTechnicalAdjustedBudget.ToString().ToLower() ? (th.action_type == 5) :
                            section.ToLower() == clsConstants.ActionType.DemographicBudgetAdjustments.ToString().ToLower() ? (th.action_type == 6) :
                            section.ToLower() == clsConstants.ActionType.BudgetAdjustmentsFromCurrentYear.ToString().ToLower() ? (th.action_type == 7) :
                            section.ToLower() == clsConstants.ActionType.BudgetChangesFromLastApprovedFinancialPlan.ToString().ToLower() ? (th.action_type == 9) :
                            section.ToLower() == clsConstants.ActionType.CostReductions.ToString().ToLower() ? (th.action_type == 31 || th.action_type == 30) :
                            section.ToLower() == clsConstants.ActionType.NewPriorities.ToString().ToLower() ? (th.action_type == 41 || th.action_type == 40) :
                            section.ToLower() == clsConstants.ActionType.PoliticalProcess.ToString().ToLower() ? (th.action_type == 25) :
                            section.ToLower() == clsConstants.ActionType.CentralExpenses.ToString().ToLower() ? (th.action_type == 100) :
                            section.ToLower() == clsConstants.ActionType.OperationalExpenses.ToString().ToLower() ? (th.action_type == 21) : (th.action_type == 0)))
                           select new clsServiceAreaData
                           {
                               departmentCode = td.department_code,
                               functionCode = td.function_code,
                               actionType = th.action_type,//gat.pk_action_type,
                               actionId = th.pk_action_id,
                               description = th.description,
                               lineOrderId = th.line_order,
                               isManuallyAdded = th.isManuallyAdded,
                               BudgetYear = td.budget_year,
                               year1Amount = td.year_1_amount,
                               year2Amount = td.year_2_amount,
                               year3Amount = td.year_3_amount,
                               year4Amount = td.year_4_amount,
                               priority = th.priority == null ? 0 : th.priority.Value,
                               fk_area_id = th.fk_area_id == null ? 0 : th.fk_area_id.Value,
                               tag = th.tag,
                               updated = th.updated,
                               updatedBy = th.updated_by,
                               changeId = td.fk_change_id,
                           }).ToListAsync();
                dataset = _utility.AssignOrgIdAndServiceIdToResultSet(orgVersionContent, dataset, lstOrgStructure, tenantOrgLevelDetails.level1, tenantOrgLevelDetails.level2);
            }

            var result = (from d in dataset
                          group d by new { d.orgId, d.orgName, d.departmentCode, d.actionType, d.actionId, d.description, d.lineOrderId, d.isManuallyAdded, d.BudgetYear, d.priority, d.fk_area_id, d.tag, d.updated, d.changeId, d.updatedBy } into g
                          select new clsServiceAreaData
                          {
                              departmentCode = g.Key.departmentCode,
                              actionType = g.Key.actionType,
                              actionId = g.Key.actionId,
                              description = g.Key.description,
                              lineOrderId = g.Key.lineOrderId,
                              isManuallyAdded = g.Key.isManuallyAdded,
                              BudgetYear = g.Key.BudgetYear,
                              year1Amount = g.Sum(x => x.year1Amount),
                              year2Amount = g.Sum(x => x.year2Amount),
                              year3Amount = g.Sum(x => x.year3Amount),
                              year4Amount = g.Sum(x => x.year4Amount),
                              priority = g.Key.priority,
                              fk_area_id = g.Key.fk_area_id,
                              tag = g.Key.tag,
                              updated = g.Key.updated,
                              updatedBy = g.Key.updatedBy,
                              changeId = g.Key.changeId,
                          }).ToList();

            if (showNewActions == true)
            {
                var budgetMeetingDetails = (await (from t in consequenceAdjustedBudgetDbContext.tbm_budget_meeting
                                            where t.fk_tenant_id == userDetails.tenant_id && t.active == 2
                                            select t.updated).ToListAsync()).OrderByDescending(x => x).ToList();

                if (budgetMeetingDetails != null && budgetMeetingDetails.Count > 0)
                {
                    result = result.Where(x => x.updated >= budgetMeetingDetails.FirstOrDefault()).ToList();
                }
            }

            return result;
        }

        public IEnumerable<BudgetData> GetCurrentBudgetData(string userID, string section, int BudgetYear)
        {
            return GetCurrentBudgetDataAsync(userID, section, BudgetYear).GetAwaiter().GetResult();
        }
        public async Task<IEnumerable<BudgetData>> GetCurrentBudgetDataAsync(string userID, string section, int BudgetYear)
        {
            int budgetYear = BudgetYear - 1;

            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();

            List<BudgetData> budgetData = await (from b in consequenceAdjustedBudgetDbContext.tbu_trans_detail_original
                                           where b.fk_tenant_id == userDetails.tenant_id
                                              && b.budget_year == budgetYear
                                              && (section.ToLower() == clsConstants.ActionType.FreeIncomeAvailable.ToString().ToLower() ? (b.action_type == 1) :
                                                  section.ToLower() == clsConstants.ActionType.DistributionAndOperation.ToString().ToLower() ? (b.action_type >= 5 && b.action_type <= 20 && b.action_type != 100) :
                                                  section.ToLower() == clsConstants.ActionType.FinancialPlan.ToString().ToLower() ? (b.action_type >= 5 && b.action_type != 100) :
                                                  section.ToLower() == clsConstants.ActionType.FinancialIncomeAndExpenses.ToString().ToLower() ? (b.action_type == 2) :
                                                  section.ToLower() == clsConstants.ActionType.Provisions.ToString().ToLower() ? (b.action_type == 3) :
                                                  section.ToLower() == clsConstants.ActionType.CentralExpenses.ToString().ToLower() ? (b.action_type == 100) :
                                                  section.ToLower() == clsConstants.ActionType.TransferredtoInvestmentAccounts.ToString().ToLower() ? (b.action_type == 4) : b.action_type == 0)
                                           select new BudgetData
                                           {
                                               actionType = b.action_type,
                                               lineOrder = b.line_order,
                                               accountCode = b.fk_account_code,
                                               departmentCode = b.department_code,
                                               functionCode = b.fk_function_code,
                                               description = b.description,
                                               year1Amount = b.amount_year_1,
                                               //year2Amount = b.amount_year_2,
                                               //year3Amount = b.amount_year_3,
                                               //year4Amount = b.amount_year_4
                                           }).ToListAsync();

            return budgetData;
        }

        public dynamic GetData(string userID, string section, string serviceAreaID, bool isGraphData, bool isBudgetAdjustments, bool isBudgetProposal, bool isFinancialPage, int BudgetYear, bool divideByMillions = false)
        {
            return GetDataAsync(userID, section, serviceAreaID, isGraphData, isBudgetAdjustments, isBudgetProposal, isFinancialPage, BudgetYear, divideByMillions).GetAwaiter().GetResult();
        }
        public async Task<dynamic> GetDataAsync(string userID, string section, string serviceAreaID, bool isGraphData, bool isBudgetAdjustments, bool isBudgetProposal, bool isFinancialPage, int BudgetYear, bool divideByMillions = false)
        {
            List<Result> lstResult = new List<Result>();
            int ID = 0;
            IEnumerable<BudgetData> budgetData = await GetCurrentBudgetDataAsync(userID, section, BudgetYear);
            int budgetYear = BudgetYear;
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userID, _utility.GetForecastPeriod(budgetYear, 1));

            List<clsOrgStructure> lstOrgStructure = await _utility.GetTenantOrgStructureAsync(orgVersionContent, userID);
            clsOrgStructureLevelDetails tenantOrgLevelDetails = _utility.GetTenantOrgLevelDetails(lstOrgStructure);
            List<Department> lstDepartments = (await _utility.GetTenantDepartmentsAsync(userID)).ToList();

            List<clsServiceAreaData> financialDataWithSA = await GetFinancialDataWithServiceAreaAsync(userID, section, BudgetYear);

            List<dynamic> lstFinancialDataWithSA = new List<dynamic>();
            foreach (var r in financialDataWithSA)
            {
                dynamic _data = new DynamicDictionary();
                _data.departmentCode = r.departmentCode;
                _data.serviceAreaID = r.orgId; ;
                _data.serviceAreaName = r.orgName;
                _data.pk_action_type = r.actionType;
                _data.pk_action_id = r.actionId;
                _data.description = r.description;
                _data.line_order = r.lineOrderId;
                _data.isManuallyAdded = r.isManuallyAdded;
                _data.budget_year = r.BudgetYear;
                _data.year_1_amount = r.year1Amount;
                _data.year_2_amount = r.year2Amount;
                _data.year_3_amount = r.year3Amount;
                _data.year_4_amount = r.year3Amount;
                _data.priority = r.priority;
                _data.fk_area_id = r.fk_area_id;
                _data.tag = r.tag;
                _data.updated = r.updated;
                _data.updated_by = r.updatedBy;
                lstFinancialDataWithSA.Add(_data);
            }

            List<int> lineGroupID = new List<int>();
            int actionType = 0;
            int subActionType = 0;

            List<string> distinctOrgIds = financialDataWithSA.Select(x => x.orgId).Distinct().ToList();

            if (section.ToLower() == clsConstants.ActionType.DistributionAndOperation.ToString().ToLower())//DistributionAndOperation
            {
                foreach (var v in distinctOrgIds)
                {
                    Result r = new Result();
                    r.ID = v;
                    List<ServiceAreaHelper> lstDepts = (from f in financialDataWithSA
                                                        where f.orgId == v
                                                        select new ServiceAreaHelper
                                                        {
                                                            department_code = f.departmentCode
                                                        }).ToList();

                    List<string> departments = new List<string>();
                    foreach (dynamic d in lstDepts)
                    {
                        string deptCode = d.department_code;
                        departments.Add(deptCode);
                    }

                    r.actionID = actionType;
                    r.description = financialDataWithSA.FirstOrDefault(x => x.orgId == v).orgName;
                    r.accounting = 0;
                    r.budget = budgetData.Where(x => departments.Contains(x.departmentCode)).ToList().Count() > 0 ? budgetData.Where(x => departments.Contains(x.departmentCode)).Sum(x => x.year1Amount) : 0;
                    r.budgetAdjustment = 0;
                    r.forecast = 0;
                    r.year1Amount = 0.0M;
                    r.year2Amount = 0.0M;
                    r.year3Amount = 0.0M;
                    r.year4Amount = 0.0M;
                    foreach (var c in lstFinancialDataWithSA.Where(x => x.serviceAreaID == v).ToList())
                    {
                        r.year1Amount = r.year1Amount.Value + Convert.ToDecimal(c.year_1_amount);
                        r.year2Amount = r.year2Amount.Value + Convert.ToDecimal(c.year_2_amount);
                        r.year3Amount = r.year3Amount.Value + Convert.ToDecimal(c.year_3_amount);
                        r.year4Amount = r.year4Amount.Value + Convert.ToDecimal(c.year_4_amount);
                    }
                    lstResult.Add(r);
                }
                return FormatOverviewGridData(lstResult, section, actionType, 0, isGraphData, userID, isFinancialPage == false ? true : false, BudgetYear, divideByMillions);
            }
            else if (section.ToLower() == clsConstants.ActionType.FinancialPlan.ToString().ToLower())//DistributionAndOperation
            {
                foreach (var v in distinctOrgIds)
                {
                    Result r = new Result();
                    r.ID = v;
                    List<ServiceAreaHelper> lstDepts = (from f in financialDataWithSA
                                                        where f.orgId == v
                                                        select new ServiceAreaHelper
                                                        {
                                                            department_code = f.departmentCode
                                                        }).ToList();
                    List<string> departments = new List<string>();
                    foreach (dynamic d in lstDepts)
                    {
                        string deptCode = d.department_code;
                        departments.Add(deptCode);
                    }

                    r.actionID = actionType;
                    r.description = financialDataWithSA.FirstOrDefault(x => x.orgId == v).orgName;
                    r.accounting = 0;
                    r.budget = budgetData.Where(x => departments.Contains(x.departmentCode)).ToList().Count() > 0 ? budgetData.Where(x => departments.Contains(x.departmentCode)).Sum(x => x.year1Amount) : 0;
                    r.budgetAdjustment = 0;
                    r.forecast = 0;
                    r.year1Amount = 0.0M;
                    r.year2Amount = 0.0M;
                    r.year3Amount = 0.0M;
                    r.year4Amount = 0.0M;
                    foreach (var c in lstFinancialDataWithSA.Where(x => x.serviceAreaID == v).ToList())
                    {
                        r.year1Amount = r.year1Amount.Value + Convert.ToDecimal(c.year_1_amount);
                        r.year2Amount = r.year2Amount.Value + Convert.ToDecimal(c.year_2_amount);
                        r.year3Amount = r.year3Amount.Value + Convert.ToDecimal(c.year_3_amount);
                        r.year4Amount = r.year4Amount.Value + Convert.ToDecimal(c.year_4_amount);
                    }
                    lstResult.Add(r);
                }
                return FormatOverviewGridData(lstResult, section, -1, 0, isGraphData, userID, isFinancialPage == false ? true : false, BudgetYear, divideByMillions);
            }
            else if (section.ToLower() == clsConstants.ActionType.FreeIncomeAvailable.ToString().ToLower())//FreeIncomeAvailable
            {
                lineGroupID.Add(1);
                actionType = Convert.ToInt32(clsConstants.ActionType.FreeIncomeAvailable);//1
            }
            else if (section.ToLower() == clsConstants.ActionType.FinancialIncomeAndExpenses.ToString().ToLower())//FinancialIncomeAndExpenses
            {
                lineGroupID.Add(2);
                actionType = Convert.ToInt32(clsConstants.ActionType.FinancialIncomeAndExpenses);//2
            }
            else if (section.ToLower() == clsConstants.ActionType.Provisions.ToString().ToLower())//Provisions
            {
                lineGroupID.Add(3);
                actionType = Convert.ToInt32(clsConstants.ActionType.Provisions);//3
            }
            else if (section.ToLower() == clsConstants.ActionType.TransferredtoInvestmentAccounts.ToString().ToLower())//TransferredtoInvestmentAccounts
            {
                lineGroupID.Add(4);
                actionType = Convert.ToInt32(clsConstants.ActionType.TransferredtoInvestmentAccounts);//4
            }
            else if (section.ToLower() == clsConstants.ActionType.OriginalAndTechnicalAdjustedBudget.ToString().ToLower())//OriginalAndTechnicalAdjustedBudget
            {
                actionType = Convert.ToInt32(clsConstants.ActionType.OriginalAndTechnicalAdjustedBudget);//5
            }
            else if (section.ToLower() == clsConstants.ActionType.DemographicBudgetAdjustments.ToString().ToLower())//DemographicBudgetAdjustments
            {
                actionType = Convert.ToInt32(clsConstants.ActionType.DemographicBudgetAdjustments);//6
            }
            else if (section.ToLower() == clsConstants.ActionType.BudgetAdjustmentsFromCurrentYear.ToString().ToLower())//BudgetAdjustmentsFromCurrentYear
            {
                actionType = Convert.ToInt32(clsConstants.ActionType.BudgetAdjustmentsFromCurrentYear);//7
            }
            else if (section.ToLower() == clsConstants.ActionType.BudgetChangesFromLastApprovedFinancialPlan.ToString().ToLower())//BudgetChangesFromLastApprovedFinancialPlan
            {
                actionType = Convert.ToInt32(clsConstants.ActionType.BudgetChangesFromLastApprovedFinancialPlan);//9
            }
            else if (section.ToLower() == clsConstants.ActionType.CostReductions.ToString().ToLower())//CostReductions
            {
                actionType = Convert.ToInt32(clsConstants.ActionType.CostReductions);//31
                subActionType = 30;
            }
            else if (section.ToLower() == clsConstants.ActionType.NewPriorities.ToString().ToLower())//NewPriorities
            {
                actionType = Convert.ToInt32(clsConstants.ActionType.NewPriorities);//41
                subActionType = 40;
            }
            else if (section.ToLower() == clsConstants.ActionType.PoliticalProcess.ToString().ToLower())//PoliticalProcess
            {
                actionType = Convert.ToInt32(clsConstants.ActionType.PoliticalProcess);//25
            }
            else if (section.ToLower() == clsConstants.ActionType.OperationalExpenses.ToString().ToLower())//OperationalExpenses
            {
                actionType = Convert.ToInt32(clsConstants.ActionType.OperationalExpenses);//21
            }
            else if (section.ToLower() == clsConstants.ActionType.CentralExpenses.ToString().ToLower())//CentralExpenses
            {
                actionType = Convert.ToInt32(clsConstants.ActionType.CentralExpenses);//100
            }

            List<dynamic> lstFinancialDataCommon = lstFinancialDataWithSA;
            List<Result> lstFinancialDataFIA = new List<Result>();
            if ((section.ToLower() == clsConstants.ActionType.OriginalAndTechnicalAdjustedBudget.ToString().ToLower() ||
                 section.ToLower() == clsConstants.ActionType.BudgetAdjustmentsFromCurrentYear.ToString().ToLower() ||
                 section.ToLower() == clsConstants.ActionType.BudgetChangesFromLastApprovedFinancialPlan.ToString().ToLower() ||
                 section.ToLower() == clsConstants.ActionType.DemographicBudgetAdjustments.ToString().ToLower() ||
                 section.ToLower() == clsConstants.ActionType.CostReductions.ToString().ToLower() ||
                 section.ToLower() == clsConstants.ActionType.NewPriorities.ToString().ToLower() ||
                 section.ToLower() == clsConstants.ActionType.PoliticalProcess.ToString().ToLower() ||
                 section.ToLower() == clsConstants.ActionType.OperationalExpenses.ToString().ToLower()) && !string.IsNullOrEmpty(serviceAreaID))
            {
                lstFinancialDataCommon = lstFinancialDataCommon.Where(x => x.serviceAreaID == serviceAreaID).ToList();
            }

            if (section.ToLower() == clsConstants.ActionType.CostReductions.ToString().ToLower() || section.ToLower() == clsConstants.ActionType.NewPriorities.ToString().ToLower())
            {
                lstFinancialDataFIA = (from fd in lstFinancialDataCommon
                                       where ((fd.pk_action_type == actionType || fd.pk_action_type == subActionType) && fd.budget_year == budgetYear)
                                       select new Result
                                       {
                                           ID = fd.pk_action_id.ToString(),
                                           actionID = fd.pk_action_type,
                                           lineOrderID = fd.line_order,
                                           description = fd.description,
                                           isManuallyAdded = fd.isManuallyAdded,
                                           year1Amount = fd.year_1_amount,
                                           year2Amount = fd.year_2_amount,
                                           year3Amount = fd.year_3_amount,
                                           year4Amount = fd.year_4_amount,
                                           priority = fd.priority,
                                           fk_area_id = fd.fk_area_id,
                                           tag = fd.tag,
                                           updated = fd.updated,
                                           updatedBy = fd.updated_by
                                       }).ToList()
                                                .GroupBy(x => new { x.actionID, x.ID, x.lineOrderID, x.description, x.isManuallyAdded, x.priority, x.fk_area_id, x.tag, x.updated, x.updatedBy })
                                                .Select(y => new Result
                                                {
                                                    ID = y.Key.ID,
                                                    actionID = y.Key.actionID,
                                                    lineOrderID = y.Key.lineOrderID,
                                                    description = y.Key.description,
                                                    isManuallyAdded = y.Key.isManuallyAdded,
                                                    year1Amount = y.Sum(z => z.year1Amount),
                                                    year2Amount = y.Sum(z => z.year2Amount),
                                                    year3Amount = y.Sum(z => z.year3Amount),
                                                    year4Amount = y.Sum(z => z.year4Amount),
                                                    priority = y.Key.priority,
                                                    fk_area_id = y.Key.fk_area_id,
                                                    tag = y.Key.tag,
                                                    updated = y.Key.updated,
                                                    updatedBy = y.Key.updatedBy
                                                }).ToList().OrderBy(x => x.actionID).ThenBy(x => x.isManuallyAdded).ThenBy(x => x.lineOrderID).ThenBy(x => x.description).ToList();
            }
            else
            {
                lstFinancialDataFIA = (from fd in lstFinancialDataCommon
                                       where (fd.pk_action_type == actionType && fd.budget_year == budgetYear)
                                       select new Result
                                       {
                                           ID = fd.pk_action_id.ToString(),
                                           actionID = fd.pk_action_type,
                                           lineOrderID = fd.line_order,
                                           description = fd.description,
                                           isManuallyAdded = fd.isManuallyAdded,
                                           year1Amount = fd.year_1_amount,
                                           year2Amount = fd.year_2_amount,
                                           year3Amount = fd.year_3_amount,
                                           year4Amount = fd.year_4_amount,
                                           priority = fd.priority,
                                           fk_area_id = fd.fk_area_id,
                                           tag = fd.tag,
                                           updated = fd.updated,
                                           updatedBy = fd.updated_by
                                       }).ToList()
                                                .GroupBy(x => new { x.actionID, x.ID, x.lineOrderID, x.description, x.isManuallyAdded, x.priority, x.fk_area_id, x.tag, x.updated, x.updatedBy })
                                                .Select(y => new Result
                                                {
                                                    ID = y.Key.ID,
                                                    actionID = y.Key.actionID,
                                                    lineOrderID = y.Key.lineOrderID,
                                                    description = y.Key.description,
                                                    isManuallyAdded = y.Key.isManuallyAdded,
                                                    year1Amount = y.Sum(z => z.year1Amount),
                                                    year2Amount = y.Sum(z => z.year2Amount),
                                                    year3Amount = y.Sum(z => z.year3Amount),
                                                    year4Amount = y.Sum(z => z.year4Amount),
                                                    priority = y.Key.priority,
                                                    fk_area_id = y.Key.fk_area_id,
                                                    tag = y.Key.tag,
                                                    updated = y.Key.updated,
                                                    updatedBy = y.Key.updatedBy
                                                }).ToList().OrderBy(x => x.actionID).ThenBy(x => x.isManuallyAdded).ThenBy(x => x.lineOrderID).ThenBy(x => x.description).ToList();
            }

            if (section.ToLower() == clsConstants.ActionType.TransferredtoInvestmentAccounts.ToString().ToLower())
            {
                ID = lstFinancialDataFIA.Count() > 0 ? Convert.ToInt32(lstFinancialDataFIA.FirstOrDefault().ID) : 0;
            }

            foreach (Result r in lstFinancialDataFIA)
            {
                if (r.lineOrderID.HasValue && r.lineOrderID.Value > 0)
                {
                    lstResult.Add(new Result()
                    {
                        ID = r.ID,
                        actionID = r.actionID,
                        description = r.description,
                        accounting = 0,
                        budget = budgetData.Where(x => x.lineOrder == r.lineOrderID.Value).Any() ? budgetData.Where(x => x.lineOrder == r.lineOrderID.Value).Sum(x => x.year1Amount) : 0,
                        budgetAdjustment = 0,
                        forecast = 0,
                        lineOrderID = (lstFinancialDataFIA.FirstOrDefault(x => x.ID == r.ID)).lineOrderID,
                        isManuallyAdded = (lstFinancialDataFIA.FirstOrDefault(x => x.ID == r.ID)).isManuallyAdded,
                        year1Amount = (lstFinancialDataFIA.FirstOrDefault(x => x.ID == r.ID)).year1Amount,
                        year2Amount = (lstFinancialDataFIA.FirstOrDefault(x => x.ID == r.ID)).year2Amount,
                        year3Amount = (lstFinancialDataFIA.FirstOrDefault(x => x.ID == r.ID)).year3Amount,
                        year4Amount = (lstFinancialDataFIA.FirstOrDefault(x => x.ID == r.ID)).year4Amount,
                        priority = r.priority,
                        fk_area_id = r.fk_area_id,
                        tag = r.tag,
                        updated = r.updated,
                        updatedBy = r.updatedBy
                    });
                }
            }
            if (section.ToLower() == clsConstants.ActionType.OriginalAndTechnicalAdjustedBudget.ToString().ToLower() ||
                section.ToLower() == clsConstants.ActionType.DemographicBudgetAdjustments.ToString().ToLower() ||
                section.ToLower() == clsConstants.ActionType.CostReductions.ToString().ToLower() ||
                section.ToLower() == clsConstants.ActionType.NewPriorities.ToString().ToLower() ||
                section.ToLower() == clsConstants.ActionType.PoliticalProcess.ToString().ToLower() ||
                section.ToLower() == clsConstants.ActionType.OperationalExpenses.ToString().ToLower())
            {
                if (isBudgetProposal)
                {
                    return FomatBudgetProposalCostReductionAndNewPriorities(lstResult, section, userID, actionType, serviceAreaID, BudgetYear, divideByMillions);
                }
                else
                {
                    return FormatDetailsGridData(lstResult, section, userID, actionType, serviceAreaID, isBudgetAdjustments, BudgetYear, divideByMillions);
                }
            }
            if (section.ToLower() == clsConstants.ActionType.BudgetAdjustmentsFromCurrentYear.ToString().ToLower() ||
               section.ToLower() == clsConstants.ActionType.BudgetChangesFromLastApprovedFinancialPlan.ToString().ToLower())
            {
                return FormatDetailsTreeGridData(lstResult, section, userID, actionType, serviceAreaID, isBudgetAdjustments, BudgetYear, divideByMillions);
            }

            return FormatOverviewGridData(lstResult, section, actionType, ID, isGraphData, userID, isFinancialPage == false ? true : false, BudgetYear, divideByMillions);
        }

        private dynamic FormatOverviewGridData(IEnumerable<Result> lstResult, string section, int actionType, int ID, bool isGraphData, string userID, bool isBudgetChangePage, int BudgetYear, bool divideByMillions = false)
        {
            return FormatOverviewGridDataAsync(lstResult, section, actionType, ID, isGraphData, userID, isBudgetChangePage, BudgetYear, divideByMillions).GetAwaiter().GetResult();
        }
        private async Task<dynamic> FormatOverviewGridDataAsync(IEnumerable<Result> lstResult, string section, int actionType, int ID, bool isGraphData, string userID, bool isBudgetChangePage, int BudgetYear, bool divideByMillions = false)
        {
            int? budgetYear = BudgetYear;

            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            dynamic columnTitles = new JArray();
            dynamic columnFields = new JArray();
            dynamic group = new JObject();
            dynamic jsonData = new JArray();
            dynamic lst = new JArray();
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();

            try
            {
                Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ConsequenceAdjustedBudget");
                Dictionary<string, clsLanguageString> langStringValuesCommon = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Common");

                List<clsAggregates> lstAggregates = (from r in lstResult
                                                     group r by new { r.description, r.ID, r.actionID } into g
                                                     select new clsAggregates
                                                     {
                                                         actionID = g.Key.actionID,
                                                         id = g.Key.ID,
                                                         description = g.Key.description,
                                                         accounting = g.Sum(x => x.accounting),
                                                         budget = g.Sum(x => x.budget),
                                                         budgetAdjustments = g.Sum(x => x.budgetAdjustment),
                                                         Forecast = g.Sum(x => x.forecast),
                                                         year1Amount = g.Sum(x => x.year1Amount),
                                                         year2Amount = g.Sum(x => x.year2Amount),
                                                         year3Amount = g.Sum(x => x.year3Amount),
                                                         year4Amount = g.Sum(x => x.year4Amount)
                                                     }).ToList();

                int id = 0;
                List<tco_budget_totals> lstActions = await consequenceAdjustedBudgetDbContext.tco_budget_totals.ToListAsync();
                if (lstActions.Count() > 0)
                {
                    lstActions.Sort((v1, v2) => v1.pk_id.CompareTo(v2.pk_id));
                    id = ((lstActions.LastOrDefault()).pk_id) + 1;
                }
                else
                {
                    id = 100;
                }
                tco_budget_totals totalRow = await consequenceAdjustedBudgetDbContext.tco_budget_totals.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                                      && x.fk_action_type.Value == actionType
                                                                                                      && x.budget_year == budgetYear).FirstOrDefaultAsync();
                if (totalRow != null)
                {
                    totalRow.actual_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.accounting) : 0.0M;
                    totalRow.budget_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.budget) : 0.0M;
                    totalRow.revised_budget_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.budgetAdjustments) : 0.0M;
                    totalRow.forecast_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.Forecast) : 0.0M;
                    totalRow.fin_plan_year1 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year1Amount) : 0.0M;
                    totalRow.fin_plan_year2 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year2Amount) : 0.0M;
                    totalRow.fin_plan_year3 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year3Amount) : 0.0M;
                    totalRow.fin_plan_year4 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year4Amount) : 0.0M;

                    await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                }
                else
                {
                    await consequenceAdjustedBudgetDbContext.tco_budget_totals.AddAsync(new tco_budget_totals()
                    {
                        pk_id = id,
                        fk_tenant_id = userDetails.tenant_id,
                        fk_action_type = actionType,
                        budget_year = budgetYear,
                        actual_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.accounting) : 0.0M,
                        budget_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.budget) : 0.0M,
                        revised_budget_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.budgetAdjustments) : 0.0M,
                        forecast_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.Forecast) : 0.0M,
                        fin_plan_year1 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year1Amount) : 0.0M,
                        fin_plan_year2 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year2Amount) : 0.0M,
                        fin_plan_year3 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year3Amount) : 0.0M,
                        fin_plan_year4 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year4Amount) : 0.0M
                    });
                    await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                }

                string totalText = section.ToLower() == clsConstants.ActionType.FreeIncomeAvailable.ToString().ToLower() ? ((langStringValues.FirstOrDefault(v => v.Key == "total_revenues_text")).Value).LangText :
                                   section.ToLower() == clsConstants.ActionType.DistributionAndOperation.ToString().ToLower() ? ((langStringValues.FirstOrDefault(v => v.Key == "Opreational_expences_by_service_area")).Value).LangText :
                                   section.ToLower() == clsConstants.ActionType.FinancialPlan.ToString().ToLower() ? ((langStringValues.FirstOrDefault(v => v.Key == "Opreational_expences_by_service_area")).Value).LangText :
                                   section.ToLower() == clsConstants.ActionType.FinancialIncomeAndExpenses.ToString().ToLower() ? ((langStringValues.FirstOrDefault(v => v.Key == "net_financial_expenses_text")).Value).LangText :
                                   section.ToLower() == clsConstants.ActionType.Provisions.ToString().ToLower() ? ((langStringValues.FirstOrDefault(v => v.Key == "net_year_end_items_text")).Value).LangText :
                                   section.ToLower() == clsConstants.ActionType.CentralExpenses.ToString().ToLower() ? ((langStringValues.FirstOrDefault(v => v.Key == "total_central_Expenses_text")).Value).LangText :
                                   section.ToLower() == clsConstants.ActionType.TransferredtoInvestmentAccounts.ToString().ToLower() ? ((langStringValues.FirstOrDefault(v => v.Key == "transfered_inv_budget_text")).Value).LangText : "";

                lstAggregates.Add(new clsAggregates()
                {
                    actionID = lstAggregates.Count() > 0 ? lstAggregates.FirstOrDefault().actionID : 0,
                    id = null,
                    description = totalText == null ? "" : totalText,
                    accounting = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.accounting) : 0,
                    budget = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.budget) : 0,
                    budgetAdjustments = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.budgetAdjustments) : 0,
                    Forecast = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.Forecast) : 0,
                    year1Amount = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year1Amount) : 0,
                    year2Amount = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year2Amount) : 0,
                    year3Amount = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year3Amount) : 0,
                    year4Amount = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year4Amount) : 0,
                });

                columnTitles.Add("id");
                columnTitles.Add(" ");
                columnTitles.Add(((langStringValuesCommon.FirstOrDefault(v => v.Key == "cmn_budget_text")).Value).LangText + " " + (budgetYear - 1).ToString());
                columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "financial_plan_text")).Value).LangText + " " + budgetYear.ToString());
                columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "financial_plan_text")).Value).LangText + " " + (budgetYear + 1).ToString());
                columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "financial_plan_text")).Value).LangText + " " + (budgetYear + 2).ToString());
                columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "financial_plan_text")).Value).LangText + " " + (budgetYear + 3).ToString());

                columnFields.Add("id");
                columnFields.Add("serviceArea");
                columnFields.Add("budget");
                columnFields.Add("year1Amount");
                columnFields.Add("year2Amount");
                columnFields.Add("year3Amount");
                columnFields.Add("year4Amount");

                if (section.ToLower() == clsConstants.ActionType.TransferredtoInvestmentAccounts.ToString().ToLower())
                {
                    group = new JObject();
                    group.id = ID;
                    group.serviceArea = lstAggregates.Count() > 0 ? lstAggregates.LastOrDefault().description : "";
                    group.key = clsConstants.ActionType.TransferredtoInvestmentAccounts.ToString().ToLower();
                    lst = new JArray();
                    dynamic gridData = new JArray();
                    gridData.Add(lstAggregates.Count() > 0 ? FormatValue(lstAggregates.LastOrDefault().budget.Value, divideByMillions) : 0);//x.budget.Value
                    gridData.Add(lstAggregates.Count() > 0 ? FormatValue(lstAggregates.LastOrDefault().year1Amount.Value, divideByMillions) : 0);//x.year1Amount.Value
                    gridData.Add(lstAggregates.Count() > 0 ? FormatValue(lstAggregates.LastOrDefault().year2Amount.Value, divideByMillions) : 0);//x.year2Amount.Value
                    gridData.Add(lstAggregates.Count() > 0 ? FormatValue(lstAggregates.LastOrDefault().year3Amount.Value, divideByMillions) : 0);//x.year3Amount.Value
                    gridData.Add(lstAggregates.Count() > 0 ? FormatValue(lstAggregates.LastOrDefault().year4Amount.Value, divideByMillions) : 0);//x.year4Amount.Value
                    lst.Add(gridData);
                    group.gridData = lst;
                    jsonData.Add(group);

                    /////////////Yearly profit////////////
                    //Yearly profit =Net profit (KOSTRA) + Total year end items + Transferred to investment budget
                    dynamic yearlyprofit = await GetyearlyprofitAsync(userID, lstAggregates, isBudgetChangePage, BudgetYear);
                    foreach (var v in yearlyprofit)
                    {
                        group = new JObject();
                        group.id = null;
                        group.serviceArea = v["name"];
                        group.key = "yearlyprofit";

                        lst = new JArray();
                        gridData = new JArray();
                        foreach (var g in v["griData"])
                        {
                            gridData.Add(Convert.ToDecimal(g));
                        }
                        lst.Add(gridData);
                        group.gridData = lst;
                        jsonData.Add(group);
                    }

                    ////////////////////

                    dynamic dataRow = new JObject();
                    dataRow.Add("columnTitles", columnTitles);
                    dataRow.Add("columnFields", columnFields);
                    dataRow.Add("rows", jsonData);
                    JObject jsonCfgRow = JObject.Parse(await _utility.GetApplicationSettingAsync("GetBudgetAndFinancialData_grid_config"));
                    dataRow.gridConfig = jsonCfgRow;
                    dataRow.groupId = actionType;
                    dataRow.amountFormat = "#,##";
                    return dataRow;
                }

                foreach (clsAggregates x in lstAggregates)
                {
                    group = new JObject();

                    group.id = x.id;
                    group.serviceArea = x.description;
                    group.key = "aggregates";
                    lst = new JArray();
                    dynamic gridData = new JArray();
                    gridData.Add(FormatValue(x.budget.Value, divideByMillions));
                    gridData.Add(FormatValue(x.year1Amount.Value, divideByMillions));
                    gridData.Add(FormatValue(x.year2Amount.Value, divideByMillions));
                    gridData.Add(FormatValue(x.year3Amount.Value, divideByMillions));
                    gridData.Add(FormatValue(x.year4Amount.Value, divideByMillions));
                    lst.Add(gridData);
                    group.gridData = lst;
                    jsonData.Add(group);
                }

                if ((section.ToLower() == clsConstants.ActionType.FinancialPlan.ToString().ToLower()) || (section.ToLower() == clsConstants.ActionType.DistributionAndOperation.ToString().ToLower()))
                {
                    lst = new JArray();
                    dynamic gridData = new JArray();

                    dynamic currdata = await GetCurrFinancialExpenseAndYearendItemsAsync(userID, BudgetYear);

                    foreach (var v in currdata)
                    {
                        group = new JObject();
                        group.id = null;
                        group.serviceArea = v["name"];
                        group.key = section.ToLower();
                        lst = new JArray();
                        gridData = new JArray();
                        foreach (var g in v["griData"])
                        {
                            gridData.Add(Convert.ToDecimal(g));
                        }
                        lst.Add(gridData);
                        group.gridData = lst;
                        jsonData.Add(group);
                    }
                    //////////////Net Profit Kostra///////////
                    dynamic profit = await GetGrossProfitKostraAsync(userID, lstAggregates, currdata, BudgetYear);
                    foreach (var v in profit)
                    {
                        group = new JObject();
                        group.id = null;
                        group.serviceArea = v["name"];
                        group.key = "grossprofit";
                        lst = new JArray();
                        gridData = new JArray();
                        foreach (var g in v["griData"])
                        {
                            gridData.Add(Convert.ToDecimal(g));
                        }
                        lst.Add(gridData);
                        group.gridData = lst;
                        jsonData.Add(group);
                    }

                    /////////////////////////////////////////////////////////////////////
                }

                if (section.ToLower() == clsConstants.ActionType.FinancialIncomeAndExpenses.ToString().ToLower())
                {
                    ///////Financial expenses in service areas
                    dynamic Financialexpencesdata = await GetFinancialexpencesinserviceareasAsync(userID, BudgetYear);
                    lst = new JArray();
                    dynamic gridData = new JArray();
                    foreach (var v in Financialexpencesdata)
                    {
                        group = new JObject();
                        group.id = null;
                        group.serviceArea = v["name"];
                        group.key = "financialexpenses";
                        lst = new JArray();
                        gridData = new JArray();
                        foreach (var g in v["griData"])
                        {
                            gridData.Add(Convert.ToDecimal(g));
                        }
                        lst.Add(gridData);
                        group.gridData = lst;
                        jsonData.Add(group);
                    }

                    //////////Total financial expenses =Financial expenses + Financial expenses in service areas///////////
                    dynamic Totalfinancialexpenses = await GetTotalfinancialexpensesAsync(userID, lstAggregates, BudgetYear);
                    foreach (var v in Totalfinancialexpenses)
                    {
                        group = new JObject();
                        group.id = null;
                        group.serviceArea = v["name"];
                        group.key = "totalfinancialexpenses";
                        lst = new JArray();
                        gridData = new JArray();
                        foreach (var g in v["griData"])
                        {
                            gridData.Add(Convert.ToDecimal(g));
                        }
                        lst.Add(gridData);
                        group.gridData = lst;
                        jsonData.Add(group);
                    }

                    ///////////////////////Depreciation income - Display the totals for action_type = 101
                    dynamic DepreciationData = await GetDepreciationincomeAsync(userID, BudgetYear);
                    group = new JObject();
                    group.id = null;
                    group.serviceArea = ((langStringValues.FirstOrDefault(v => v.Key == "Depreciation_income")).Value).LangText;
                    group.key = "depreciation";
                    lst = new JArray();
                    gridData = new JArray();
                    if (DepreciationData.Count > 0)
                    {
                        gridData.Add(DepreciationData.FirstOrDefault().budget);
                        gridData.Add(DepreciationData.FirstOrDefault().year1);
                        gridData.Add(DepreciationData.FirstOrDefault().year2);
                        gridData.Add(DepreciationData.FirstOrDefault().year3);
                        gridData.Add(DepreciationData.FirstOrDefault().year4);
                    }
                    else
                    {
                        gridData.Add(0);
                        gridData.Add(0);
                        gridData.Add(0);
                        gridData.Add(0);
                        gridData.Add(0);
                    }
                    lst.Add(gridData);
                    group.gridData = lst;
                    jsonData.Add(group);

                    ////////////////////////// Net profit (KOSTRA) =Total financial expenses + Depreciation income
                    dynamic NetprofitKOSTRAtotal = await GetNetprofitKOSTRAtotalAsync(userID, Totalfinancialexpenses, DepreciationData, BudgetYear);
                    foreach (var v in NetprofitKOSTRAtotal)
                    {
                        group = new JObject();
                        group.id = null;
                        group.serviceArea = v["name"];
                        group.key = "netprofitkostratotal";

                        lst = new JArray();
                        gridData = new JArray();
                        foreach (var g in v["griData"])
                        {
                            gridData.Add(Convert.ToDecimal(g));
                        }
                        lst.Add(gridData);
                        group.gridData = lst;
                        jsonData.Add(group);
                    }

                    ////////////////////////////////////
                }
                if (section.ToLower() == clsConstants.ActionType.Provisions.ToString().ToLower())
                {
                    ///Net year end items in service areas
                    dynamic Netyearenditemsinserviceareasdata = await GetNetyearenditemsinserviceareasAsync(userID, BudgetYear);
                    lst = new JArray();
                    dynamic gridData = new JArray();
                    foreach (var v in Netyearenditemsinserviceareasdata)
                    {
                        group = new JObject();
                        group.id = null;
                        group.serviceArea = v["name"];
                        group.key = "yearenditems";
                        lst = new JArray();
                        gridData = new JArray();
                        foreach (var g in v["griData"])
                        {
                            gridData.Add(Convert.ToDecimal(g));
                        }
                        lst.Add(gridData);
                        group.gridData = lst;
                        jsonData.Add(group);
                    }

                    ///Total year-end items
                    dynamic TotalYearendItems = await GetTotalYearendItemsAsync(userID, lstAggregates, Netyearenditemsinserviceareasdata);
                    foreach (var v in TotalYearendItems)
                    {
                        group = new JObject();
                        group.id = null;
                        group.serviceArea = v["name"];
                        group.key = "totalyearenditems";
                        lst = new JArray();
                        gridData = new JArray();
                        foreach (var g in v["griData"])
                        {
                            gridData.Add(Convert.ToDecimal(g));
                        }
                        lst.Add(gridData);
                        group.gridData = lst;
                        jsonData.Add(group);
                    }
                }

                if (isGraphData)
                {
                    return await FormatGraphDataAsync(jsonData, userID, section, BudgetYear);
                }

                dynamic data = new JObject();
                data.Add("columnTitles", columnTitles);
                data.Add("columnFields", columnFields);
                data.Add("rows", jsonData);

                data.groupId = actionType;
                data.amountFormat = "#,##";
                return data;
            }
            //catch (DbEntityValidationException)
            //{
            //    throw;
            //}
            catch
            {
                throw;
            }
        }

        private dynamic FormatGraphData(dynamic jsonData, string userID, string section, int BudgetYear, bool isNewGetdata = false)
        {
            return FormatGraphDataAsync(jsonData, userID, section, BudgetYear, isNewGetdata).GetAwaiter().GetResult();
        }

        private async Task<dynamic> FormatGraphDataAsync(dynamic jsonData, string userID, string section, int BudgetYear, bool isNewGetdata = false)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ConsequenceAdjustedBudget");
            Dictionary<string, string> colorsForGraph = await _utility.GetColorsAsync(userID, ColorsFor.Graph);
            string serviceAreaName = string.Empty, colour = string.Empty;
            dynamic data = new JObject();

            dynamic lst = new JArray();
            int rowCount = 0;
            List<decimal> othersData = new List<decimal>() { 0, 0, 0, 0 };
            int budgetYear = BudgetYear;
            List<int> years = new List<int>() { (budgetYear - 1), budgetYear, budgetYear + 1, budgetYear + 2, budgetYear + 3 };

            if (section.ToLower() == clsConstants.ActionType.FreeIncomeAvailable.ToString().ToLower())
            {
                int count = ((JArray)jsonData).Count;
                ((JArray)jsonData).RemoveAt(count - 1);
            }

            foreach (dynamic row in jsonData)
            {
                dynamic obj = new JObject();
                if (rowCount <= 9)
                {
                    if (section == "FreeIncomeAvailable")
                    {
                        serviceAreaName = row.serviceArea;
                        colour = colorsForGraph[(rowCount).ToString()];
                    }
                    else
                    {
                        obj.name = row.serviceArea;
                        obj.color = colorsForGraph[(rowCount).ToString()];
                    }
                    dynamic chartData = new JArray();
                    foreach (dynamic gd in row)
                    {
                        if (gd.Name == "prevOrigBudget" || gd.Name == "year1Amount" || gd.Name == "year2Amount" || gd.Name == "year3Amount" || gd.Name == "year4Amount")
                        {
                            if (isNewGetdata)
                            {
                                if (section == "FinancialIncomeAndExpenses")
                                {
                                    chartData.Add(Math.Abs(Convert.ToDecimal(gd.Value)));
                                }
                                else
                                {
                                    chartData.Add(gd.Value);
                                }
                            }
                            else
                            {
                                if (section == "FreeIncomeAvailable")
                                {
                                    if (!string.IsNullOrEmpty(serviceAreaName))
                                    {
                                        obj.name = serviceAreaName;
                                        obj.color = colour;
                                        serviceAreaName = ""; colour = "";
                                    }
                                    chartData.Add(Math.Abs(Convert.ToDecimal(gd.Value)));
                                }
                                else
                                {
                                    chartData.Add(gd.Value);
                                }
                            }
                        }
                    }
                    obj.Add("chartData", chartData);
                    lst.Add(obj);
                }
                else
                {
                    dynamic gridData = row;
                    othersData[0] = othersData[0] + Convert.ToDecimal(gridData["prevOrigBudget"]);
                    othersData[1] = othersData[1] + Convert.ToDecimal(gridData["prevRevBudget"]);
                    othersData[2] = othersData[2] + Convert.ToDecimal(gridData["year1Amount"]);
                    othersData[3] = othersData[3] + Convert.ToDecimal(gridData["year2Amount"]);
                }
                rowCount++;
            }

            if (rowCount > 9)
            {
                dynamic objOthers = new JObject();
                objOthers.name = ((langStringValues.FirstOrDefault(v => v.Key == "others_text")).Value).LangText;
                objOthers.color = colorsForGraph[(rowCount).ToString()];
                dynamic othersChartData = new JArray() { othersData[0] * (-1), othersData[1] * (-1), othersData[2] * (-1), othersData[3] * (-1) };
                objOthers.Add("chartData", othersChartData);
                lst.Add(objOthers);
            }

            data.Add("jsonData", lst);
            data.Add("years", new JArray() { years[0], years[1], years[2], years[3], years[4] });

            return data;
        }

        private dynamic FormatDetailsGridData(IEnumerable<Result> lstResult, string section, string userID, int actionType, string serviceAreaID, bool isBudgetAdjustments, int BudgetYear, bool divideByMillions = false)
        {
            UserData userDetails = _utility.GetUserDetails(userID);
            dynamic columnTitles = new JArray();
            dynamic columnFields = new JArray();
            dynamic group = new JObject();
            dynamic jsonData = new JArray();
            dynamic lst = new JArray();
            int? budgetYear = BudgetYear;

            TenantDBContext consequenceAdjustedBudgetDbContext = _utility.GetTenantDBContext();
            try
            {
                Dictionary<string, clsLanguageString> langStringValues = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "ConsequenceAdjustedBudget");

                if (section.ToLower() == clsConstants.ActionType.DemographicBudgetAdjustments.ToString().ToLower())
                {
                    lstResult = lstResult.Where(x => x.isManuallyAdded == 1).ToList();
                }

                List<clsAggregates> lstAggregates = new List<clsAggregates>();
                if (section.ToLower() == clsConstants.ActionType.OperationalExpenses.ToString().ToLower())
                {
                    lstAggregates = (from r in lstResult
                                     group r by new { r.description, r.actionID, r.lineOrderID, r.isManuallyAdded } into g
                                     select new clsAggregates
                                     {
                                         actionID = g.Key.actionID,
                                         id = null,
                                         lineOrderID = g.Key.lineOrderID.Value,
                                         isManuallyAdded = g.Key.isManuallyAdded.Value,
                                         description = g.Key.description,
                                         accounting = g.Sum(x => x.accounting),
                                         budget = g.Sum(x => x.budget),
                                         budgetAdjustments = g.Sum(x => x.budgetAdjustment),
                                         Forecast = g.Sum(x => x.forecast),
                                         year1Amount = g.Sum(x => x.year1Amount),
                                         year2Amount = g.Sum(x => x.year2Amount),
                                         year3Amount = g.Sum(x => x.year3Amount),
                                         year4Amount = g.Sum(x => x.year4Amount)
                                     }).ToList();
                }
                else
                {
                    lstAggregates = (from r in lstResult
                                     group r by new { r.description, r.ID, r.actionID, r.lineOrderID, r.isManuallyAdded } into g
                                     select new clsAggregates
                                     {
                                         actionID = g.Key.actionID,
                                         id = g.Key.ID,
                                         lineOrderID = g.Key.lineOrderID.Value,
                                         isManuallyAdded = g.Key.isManuallyAdded.Value,
                                         description = g.Key.description,
                                         accounting = g.Sum(x => x.accounting),
                                         budget = g.Sum(x => x.budget),
                                         budgetAdjustments = g.Sum(x => x.budgetAdjustment),
                                         Forecast = g.Sum(x => x.forecast),
                                         year1Amount = g.Sum(x => x.year1Amount),
                                         year2Amount = g.Sum(x => x.year2Amount),
                                         year3Amount = g.Sum(x => x.year3Amount),
                                         year4Amount = g.Sum(x => x.year4Amount)
                                     }).ToList();
                }

                int id = 0;
                List<tco_budget_totals> lstActions = consequenceAdjustedBudgetDbContext.tco_budget_totals.ToList();
                if (lstActions.Count() > 0)
                {
                    lstActions.Sort((v1, v2) => v1.pk_id.CompareTo(v2.pk_id));
                    id = ((lstActions.LastOrDefault()).pk_id) + 1;
                }
                else
                {
                    id = 100;
                }

                if (section.ToLower() == clsConstants.ActionType.OriginalAndTechnicalAdjustedBudget.ToString().ToLower())
                {
                    List<clsAggregates> lstAggregatesOriginalBudget = lstAggregates.Where(x => x.actionID == 5 && x.lineOrderID == 0 && x.isManuallyAdded == 0).ToList();
                    List<clsAggregates> lstAggregatesTechnicalAdjustedBudget = lstAggregates.Where(x => x.actionID == 5 && (x.isManuallyAdded == 0 && x.lineOrderID != 0 || x.isManuallyAdded == 1)).ToList();

                    if (isBudgetAdjustments)
                    {
                        //lstAggregatesOriginalBudget
                        //lstAggregatesTechnicalAdjustedBudget
                        var originalBudgetData = (from o in lstAggregatesOriginalBudget
                                                  group o by new { o.actionID } into originalBData
                                                  select new
                                                  {
                                                      year1Amount = originalBData.Sum(x => x.year1Amount),
                                                      year2Amount = originalBData.Sum(x => x.year2Amount),
                                                      year3Amount = originalBData.Sum(x => x.year3Amount),
                                                      year4Amount = originalBData.Sum(x => x.year4Amount)
                                                  }).ToList();

                        var technicalAdjustedBudgetData = (from o in lstAggregatesTechnicalAdjustedBudget
                                                           group o by new { o.actionID } into technicalAdjustedBData
                                                           select new
                                                           {
                                                               year1Amount = technicalAdjustedBData.Sum(x => x.year1Amount),
                                                               year2Amount = technicalAdjustedBData.Sum(x => x.year2Amount),
                                                               year3Amount = technicalAdjustedBData.Sum(x => x.year3Amount),
                                                               year4Amount = technicalAdjustedBData.Sum(x => x.year4Amount)
                                                           }).ToList();
                        dynamic budgetAdjustmentsResult = new JArray();
                        dynamic budgetAdjustmentRow = new JObject();
                        budgetAdjustmentRow.ID = "OriginalBudget";
                        budgetAdjustmentRow.year1Amount = originalBudgetData.FirstOrDefault() == null ? 0 : originalBudgetData.FirstOrDefault().year1Amount;
                        budgetAdjustmentRow.year2Amount = originalBudgetData.FirstOrDefault() == null ? 0 : originalBudgetData.FirstOrDefault().year2Amount;
                        budgetAdjustmentRow.year3Amount = originalBudgetData.FirstOrDefault() == null ? 0 : originalBudgetData.FirstOrDefault().year3Amount;
                        budgetAdjustmentRow.year4Amount = originalBudgetData.FirstOrDefault() == null ? 0 : originalBudgetData.FirstOrDefault().year4Amount;
                        budgetAdjustmentsResult.Add(budgetAdjustmentRow);

                        budgetAdjustmentRow = new JObject();
                        budgetAdjustmentRow.ID = "TechnicalBudget";
                        budgetAdjustmentRow.year1Amount = technicalAdjustedBudgetData.FirstOrDefault() == null ? 0 : technicalAdjustedBudgetData.FirstOrDefault().year1Amount;
                        budgetAdjustmentRow.year2Amount = technicalAdjustedBudgetData.FirstOrDefault() == null ? 0 : technicalAdjustedBudgetData.FirstOrDefault().year2Amount;
                        budgetAdjustmentRow.year3Amount = technicalAdjustedBudgetData.FirstOrDefault() == null ? 0 : technicalAdjustedBudgetData.FirstOrDefault().year3Amount;
                        budgetAdjustmentRow.year4Amount = technicalAdjustedBudgetData.FirstOrDefault() == null ? 0 : technicalAdjustedBudgetData.FirstOrDefault().year4Amount;
                        budgetAdjustmentsResult.Add(budgetAdjustmentRow);

                        return budgetAdjustmentsResult;
                    }

                    tco_budget_totals totalRow = consequenceAdjustedBudgetDbContext.tco_budget_totals.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                                          && x.fk_action_type.Value == 5001
                                                                                                          && x.serviceAreaID == serviceAreaID
                                                                                                          && x.budget_year == budgetYear).FirstOrDefault();
                    if (totalRow != null)
                    {
                        totalRow.actual_data = lstAggregatesOriginalBudget.Count() > 0 ? lstAggregatesOriginalBudget.Sum(x => x.accounting) : 0.0M;
                        totalRow.budget_data = lstAggregatesOriginalBudget.Count() > 0 ? lstAggregatesOriginalBudget.Sum(x => x.budget) : 0.0M;
                        totalRow.revised_budget_data = lstAggregatesOriginalBudget.Count() > 0 ? lstAggregatesOriginalBudget.Sum(x => x.budgetAdjustments) : 0.0M;
                        totalRow.forecast_data = lstAggregatesOriginalBudget.Count() > 0 ? lstAggregatesOriginalBudget.Sum(x => x.Forecast) : 0.0M;
                        totalRow.fin_plan_year1 = lstAggregatesOriginalBudget.Count() > 0 ? lstAggregatesOriginalBudget.Sum(x => x.year1Amount) : 0.0M;
                        totalRow.fin_plan_year2 = lstAggregatesOriginalBudget.Count() > 0 ? lstAggregatesOriginalBudget.Sum(x => x.year2Amount) : 0.0M;
                        totalRow.fin_plan_year3 = lstAggregatesOriginalBudget.Count() > 0 ? lstAggregatesOriginalBudget.Sum(x => x.year3Amount) : 0.0M;
                        totalRow.fin_plan_year4 = lstAggregatesOriginalBudget.Count() > 0 ? lstAggregatesOriginalBudget.Sum(x => x.year4Amount) : 0.0M;

                        consequenceAdjustedBudgetDbContext.SaveChanges();
                    }
                    else
                    {
                        actionType = 5001;
                        consequenceAdjustedBudgetDbContext.tco_budget_totals.Add(new tco_budget_totals()
                        {
                            pk_id = id,
                            fk_tenant_id = userDetails.tenant_id,
                            serviceAreaID = serviceAreaID,
                            fk_action_type = actionType,
                            budget_year = budgetYear,
                            actual_data = lstAggregatesOriginalBudget.Count() > 0 ? lstAggregatesOriginalBudget.Sum(x => x.accounting) : 0.0M,
                            budget_data = lstAggregatesOriginalBudget.Count() > 0 ? lstAggregatesOriginalBudget.Sum(x => x.budget) : 0.0M,
                            revised_budget_data = lstAggregatesOriginalBudget.Count() > 0 ? lstAggregatesOriginalBudget.Sum(x => x.budgetAdjustments) : 0.0M,
                            forecast_data = lstAggregatesOriginalBudget.Count() > 0 ? lstAggregatesOriginalBudget.Sum(x => x.Forecast) : 0.0M,
                            fin_plan_year1 = lstAggregatesOriginalBudget.Count() > 0 ? lstAggregatesOriginalBudget.Sum(x => x.year1Amount) : 0.0M,
                            fin_plan_year2 = lstAggregatesOriginalBudget.Count() > 0 ? lstAggregatesOriginalBudget.Sum(x => x.year2Amount) : 0.0M,
                            fin_plan_year3 = lstAggregatesOriginalBudget.Count() > 0 ? lstAggregatesOriginalBudget.Sum(x => x.year3Amount) : 0.0M,
                            fin_plan_year4 = lstAggregatesOriginalBudget.Count() > 0 ? lstAggregatesOriginalBudget.Sum(x => x.year4Amount) : 0.0M
                        });
                        consequenceAdjustedBudgetDbContext.SaveChanges();
                    }

                    totalRow = consequenceAdjustedBudgetDbContext.tco_budget_totals.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                                          && x.fk_action_type.Value == 5002
                                                                                                          && x.serviceAreaID == serviceAreaID
                                                                                                          && x.budget_year == budgetYear).FirstOrDefault();
                    if (totalRow != null)
                    {
                        totalRow.actual_data = lstAggregatesTechnicalAdjustedBudget.Count() > 0 ? lstAggregatesTechnicalAdjustedBudget.Sum(x => x.accounting) : 0.0M;
                        totalRow.budget_data = lstAggregatesTechnicalAdjustedBudget.Count() > 0 ? lstAggregatesTechnicalAdjustedBudget.Sum(x => x.budget) : 0.0M;
                        totalRow.revised_budget_data = lstAggregatesTechnicalAdjustedBudget.Count() > 0 ? lstAggregatesTechnicalAdjustedBudget.Sum(x => x.budgetAdjustments) : 0.0M;
                        totalRow.forecast_data = lstAggregatesTechnicalAdjustedBudget.Count() > 0 ? lstAggregatesTechnicalAdjustedBudget.Sum(x => x.Forecast) : 0.0M;
                        totalRow.fin_plan_year1 = lstAggregatesTechnicalAdjustedBudget.Count() > 0 ? lstAggregatesTechnicalAdjustedBudget.Sum(x => x.year1Amount) : 0.0M;
                        totalRow.fin_plan_year2 = lstAggregatesTechnicalAdjustedBudget.Count() > 0 ? lstAggregatesTechnicalAdjustedBudget.Sum(x => x.year2Amount) : 0.0M;
                        totalRow.fin_plan_year3 = lstAggregatesTechnicalAdjustedBudget.Count() > 0 ? lstAggregatesTechnicalAdjustedBudget.Sum(x => x.year3Amount) : 0.0M;
                        totalRow.fin_plan_year4 = lstAggregatesTechnicalAdjustedBudget.Count() > 0 ? lstAggregatesTechnicalAdjustedBudget.Sum(x => x.year4Amount) : 0.0M;

                        consequenceAdjustedBudgetDbContext.SaveChanges();
                    }
                    else
                    {
                        actionType = 5002;
                        lstActions = consequenceAdjustedBudgetDbContext.tco_budget_totals.ToList();
                        if (lstActions.Count() > 0)
                        {
                            lstActions.Sort((v1, v2) => v1.pk_id.CompareTo(v2.pk_id));
                            id = ((lstActions.LastOrDefault()).pk_id) + 1;
                        }
                        else
                        {
                            id = 100;
                        }
                        consequenceAdjustedBudgetDbContext.tco_budget_totals.Add(new tco_budget_totals()
                        {
                            pk_id = id,
                            fk_tenant_id = userDetails.tenant_id,
                            serviceAreaID = serviceAreaID,
                            fk_action_type = actionType,
                            budget_year = budgetYear,
                            actual_data = lstAggregatesTechnicalAdjustedBudget.Count() > 0 ? lstAggregatesTechnicalAdjustedBudget.Sum(x => x.accounting) : 0.0M,
                            budget_data = lstAggregatesTechnicalAdjustedBudget.Count() > 0 ? lstAggregatesTechnicalAdjustedBudget.Sum(x => x.budget) : 0.0M,
                            revised_budget_data = lstAggregatesTechnicalAdjustedBudget.Count() > 0 ? lstAggregatesTechnicalAdjustedBudget.Sum(x => x.budgetAdjustments) : 0.0M,
                            forecast_data = lstAggregatesTechnicalAdjustedBudget.Count() > 0 ? lstAggregatesTechnicalAdjustedBudget.Sum(x => x.Forecast) : 0.0M,
                            fin_plan_year1 = lstAggregatesTechnicalAdjustedBudget.Count() > 0 ? lstAggregatesTechnicalAdjustedBudget.Sum(x => x.year1Amount) : 0.0M,
                            fin_plan_year2 = lstAggregatesTechnicalAdjustedBudget.Count() > 0 ? lstAggregatesTechnicalAdjustedBudget.Sum(x => x.year2Amount) : 0.0M,
                            fin_plan_year3 = lstAggregatesTechnicalAdjustedBudget.Count() > 0 ? lstAggregatesTechnicalAdjustedBudget.Sum(x => x.year3Amount) : 0.0M,
                            fin_plan_year4 = lstAggregatesTechnicalAdjustedBudget.Count() > 0 ? lstAggregatesTechnicalAdjustedBudget.Sum(x => x.year4Amount) : 0.0M
                        });
                        consequenceAdjustedBudgetDbContext.SaveChanges();
                    }

                    actionType = Convert.ToInt32(clsConstants.ActionType.OriginalAndTechnicalAdjustedBudget);
                }
                else
                {
                    lstActions = consequenceAdjustedBudgetDbContext.tco_budget_totals.ToList();
                    if (lstActions.Count() > 0)
                    {
                        lstActions.Sort((v1, v2) => v1.pk_id.CompareTo(v2.pk_id));
                        id = ((lstActions.LastOrDefault()).pk_id) + 1;
                    }
                    else
                    {
                        id = 100;
                    }
                    tco_budget_totals totalRow = consequenceAdjustedBudgetDbContext.tco_budget_totals.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                                          && x.fk_action_type.Value == actionType
                                                                                                          && x.serviceAreaID == serviceAreaID
                                                                                                          && x.budget_year == budgetYear).FirstOrDefault();
                    if (totalRow != null)
                    {
                        totalRow.actual_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.accounting) : 0.0M;
                        totalRow.budget_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.budget) : 0.0M;
                        totalRow.revised_budget_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.budgetAdjustments) : 0.0M;
                        totalRow.forecast_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.Forecast) : 0.0M;
                        totalRow.fin_plan_year1 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year1Amount) : 0.0M;
                        totalRow.fin_plan_year2 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year2Amount) : 0.0M;
                        totalRow.fin_plan_year3 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year3Amount) : 0.0M;
                        totalRow.fin_plan_year4 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year4Amount) : 0.0M;

                        consequenceAdjustedBudgetDbContext.SaveChanges();
                    }
                    else
                    {
                        consequenceAdjustedBudgetDbContext.tco_budget_totals.Add(new tco_budget_totals()
                        {
                            pk_id = id,
                            fk_tenant_id = userDetails.tenant_id,
                            serviceAreaID = serviceAreaID,
                            fk_action_type = actionType,
                            budget_year = budgetYear,
                            actual_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.accounting) : 0.0M,
                            budget_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.budget) : 0.0M,
                            revised_budget_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.budgetAdjustments) : 0.0M,
                            forecast_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.Forecast) : 0.0M,
                            fin_plan_year1 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year1Amount) : 0.0M,
                            fin_plan_year2 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year2Amount) : 0.0M,
                            fin_plan_year3 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year3Amount) : 0.0M,
                            fin_plan_year4 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year4Amount) : 0.0M
                        });
                        consequenceAdjustedBudgetDbContext.SaveChanges();
                    }
                }

                string totalText = section.ToLower() == clsConstants.ActionType.OriginalAndTechnicalAdjustedBudget.ToString().ToLower() ? ((langStringValues.FirstOrDefault(v => v.Key == "technical_adjusted_budget_text")).Value).LangText :
                                  section.ToLower() == clsConstants.ActionType.BudgetAdjustmentsFromCurrentYear.ToString().ToLower() ? ((langStringValues.FirstOrDefault(v => v.Key == "total_budget_changes_from_current_year_text")).Value).LangText :
                                  section.ToLower() == clsConstants.ActionType.BudgetChangesFromLastApprovedFinancialPlan.ToString().ToLower() ? ((langStringValues.FirstOrDefault(v => v.Key == "total_budget_changes_from_last_plan_text")).Value).LangText :
                                  langStringValues.FirstOrDefault(v => v.Key == "cmn_title_total").Value.LangText;

                if (section.ToLower() != clsConstants.ActionType.DemographicBudgetAdjustments.ToString().ToLower())
                {
                    if (lstAggregates.Count() > 0)
                    {
                        lstAggregates.Add(new clsAggregates()
                        {
                            actionID = -1,
                            id = null,
                            description = totalText,
                            accounting = (from r in lstAggregates
                                          select r.accounting).Sum(),
                            budget = (from r in lstAggregates
                                      select r.budget).Sum(),
                            budgetAdjustments = (from r in lstAggregates
                                                 select r.budgetAdjustments).Sum(),
                            isManuallyAdded = 0,
                            Forecast = (from r in lstAggregates
                                        select r.Forecast).Sum(),
                            year1Amount = (from r in lstAggregates
                                           select r.year1Amount).Sum(),
                            year2Amount = (from r in lstAggregates
                                           select r.year2Amount).Sum(),
                            year3Amount = (from r in lstAggregates
                                           select r.year3Amount).Sum(),
                            year4Amount = (from r in lstAggregates
                                           select r.year4Amount).Sum()
                        });
                    }
                }

                columnTitles.Add("id");
                columnTitles.Add(" ");
                columnTitles.Add(" ");

                columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "plan_text")).Value).LangText + " " + budgetYear.ToString());
                columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "plan_text")).Value).LangText + " " + (budgetYear + 1).ToString());
                columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "plan_text")).Value).LangText + " " + (budgetYear + 2).ToString());
                columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "plan_text")).Value).LangText + " " + (budgetYear + 3).ToString());

                columnFields.Add("id");
                columnFields.Add("serviceArea");
                columnFields.Add("isClickable");

                columnFields.Add("year1Amount");
                columnFields.Add("year2Amount");
                columnFields.Add("year3Amount");
                columnFields.Add("year4Amount");

                foreach (clsAggregates x in lstAggregates)
                {
                    group = new JObject();

                    group.id = x.id;
                    group.serviceArea = x.description;
                    if ((section.ToLower() == clsConstants.ActionType.OperationalExpenses.ToString().ToLower()) && (x.description != totalText) && (x.year1Amount == 0 && x.year2Amount == 0 && x.year3Amount == 0 && x.year4Amount == 0))
                    {
                        continue;
                    }

                    if (section.ToLower() == clsConstants.ActionType.OperationalExpenses.ToString().ToLower())
                    {
                        group.isClickable = false;
                    }
                    else
                    {
                        if (section.ToLower() == clsConstants.ActionType.OriginalAndTechnicalAdjustedBudget.ToString().ToLower())
                        {
                            if (x.isManuallyAdded == 0 || x.id == null)
                            {
                                group.isClickable = false;
                            }
                            else
                            {
                                group.isClickable = true;
                            }
                        }
                        else if (section.ToLower() == clsConstants.ActionType.CostReductions.ToString().ToLower() || section.ToLower() == clsConstants.ActionType.NewPriorities.ToString().ToLower())
                        {
                            if (x.actionID == 30 || x.actionID == 40)
                            {
                                group.isClickable = false;
                            }
                            else
                            {
                                group.isClickable = true;
                            }
                        }
                        else
                        {
                            if (x.description == totalText)
                            {
                                group.isClickable = false;
                            }
                            else
                            {
                                group.isClickable = true;
                            }
                        }
                    }
                    lst = new JArray();
                    dynamic gridData = new JArray();

                    if (section.ToLower() == clsConstants.ActionType.DemographicBudgetAdjustments.ToString().ToLower())
                    {
                        gridData.Add(x.year1Amount.Value);
                        gridData.Add(x.year2Amount.Value);
                        gridData.Add(x.year3Amount.Value);
                        gridData.Add(x.year4Amount.Value);
                    }
                    else
                    {
                        gridData.Add(FormatValue(x.year1Amount.Value, divideByMillions));
                        gridData.Add(FormatValue(x.year2Amount.Value, divideByMillions));
                        gridData.Add(FormatValue(x.year3Amount.Value, divideByMillions));
                        gridData.Add(FormatValue(x.year4Amount.Value, divideByMillions));
                    }

                    lst.Add(gridData);
                    group.gridData = lst;
                    jsonData.Add(group);
                }

                if (section.ToLower() == clsConstants.ActionType.DemographicBudgetAdjustments.ToString().ToLower())
                {
                    return jsonData;
                }

                if (lstAggregates.Count() == 0)
                {
                    group = new JObject();

                    group.id = null;
                    group.serviceArea = totalText;

                    lst = new JArray();
                    dynamic gridData = new JArray();

                    gridData.Add(0);
                    gridData.Add(0);
                    gridData.Add(0);
                    gridData.Add(0);
                    lst.Add(gridData);
                    group.gridData = lst;
                    jsonData.Add(group);
                }

                dynamic data = new JObject();
                data.Add("columnTitles", columnTitles);
                data.Add("columnFields", columnFields);
                data.Add("rows", jsonData);
                JObject jsonCfg = JObject.Parse(_utility.GetApplicationSetting("GetBudgetAndFinancialData_grid_config"));
                data.gridConfig = jsonCfg;
                data.groupId = actionType;
                data.amountFormat = "#,##";
                return data;
            }
            //catch (DbEntityValidationException dbEx)
            //{
            //    dynamic data = new JObject();
            //    data.error = dbEx.Message;
            //    return data;
            //}
            catch (Exception ex)
            {
                dynamic data = new JObject();
                data.error = ex.Message;
                return data;
            }
        }

        private dynamic FomatBudgetProposalCostReductionAndNewPriorities(IEnumerable<Result> lstResult, string section, string userID, int actionType, string serviceAreaID, int BudgetYear, bool divideByMillions = false)
        {
            UserData userDetails = _utility.GetUserDetails(userID);
            dynamic columnTitles = new JArray();
            dynamic columnFields = new JArray();
            dynamic group = new JObject();
            dynamic jsonData = new JArray();
            dynamic lst = new JArray();
            int budgetYear = BudgetYear;

            List<gmd_assessment_areas> assessmentCategories = _utility.GetTenantDBContext().gmd_assessment_areas.Where(x => x.language_code == userDetails.language_preference).ToList();

            TenantDBContext consequenceAdjustedBudgetDbContext = _utility.GetTenantDBContext();

            try
            {
                Dictionary<string, clsLanguageString> langStringValues = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "ConsequenceAdjustedBudget");
                Dictionary<string, clsLanguageString> langStringValuesBudgetProposal = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "BudgetProposal");

                if (section.ToLower() == clsConstants.ActionType.DemographicBudgetAdjustments.ToString().ToLower())
                {
                    lstResult = lstResult.Where(x => x.isManuallyAdded == 1).ToList();
                }

                List<clsAggregates> lstAggregates = (from r in lstResult
                                                     group r by new { r.description, r.ID, r.actionID, r.lineOrderID, r.isManuallyAdded, r.priority, r.fk_area_id, r.tag, r.updated, r.updatedBy } into g
                                                     select new clsAggregates
                                                     {
                                                         actionID = g.Key.actionID,
                                                         id = g.Key.ID,
                                                         lineOrderID = g.Key.lineOrderID.Value,
                                                         isManuallyAdded = g.Key.isManuallyAdded.Value,
                                                         description = g.Key.description,
                                                         accounting = g.Sum(x => x.accounting),
                                                         budget = g.Sum(x => x.budget),
                                                         budgetAdjustments = g.Sum(x => x.budgetAdjustment),
                                                         Forecast = g.Sum(x => x.forecast),
                                                         year1Amount = g.Sum(x => x.year1Amount),
                                                         year2Amount = g.Sum(x => x.year2Amount),
                                                         year3Amount = g.Sum(x => x.year3Amount),
                                                         year4Amount = g.Sum(x => x.year4Amount),
                                                         priority = g.Key.priority,
                                                         fk_area_id = g.Key.fk_area_id,
                                                         tag = g.Key.tag,
                                                         updated = g.Key.updated,
                                                         updatedBy = g.Key.updatedBy
                                                     }).ToList();
                int id = 0;
                List<tco_budget_totals> lstActions = consequenceAdjustedBudgetDbContext.tco_budget_totals.ToList();
                if (lstActions.Count() > 0)
                {
                    lstActions.Sort((v1, v2) => v1.pk_id.CompareTo(v2.pk_id));
                    id = ((lstActions.LastOrDefault()).pk_id) + 1;
                }
                else
                {
                    id = 100;
                }

                lstActions = consequenceAdjustedBudgetDbContext.tco_budget_totals.ToList();
                if (lstActions.Count() > 0)
                {
                    lstActions.Sort((v1, v2) => v1.pk_id.CompareTo(v2.pk_id));
                    id = ((lstActions.LastOrDefault()).pk_id) + 1;
                }
                else
                {
                    id = 100;
                }
                tco_budget_totals totalRow = consequenceAdjustedBudgetDbContext.tco_budget_totals.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                                      && x.fk_action_type.Value == actionType
                                                                                                      && x.serviceAreaID == serviceAreaID
                                                                                                      && x.budget_year == budgetYear).FirstOrDefault();
                if (totalRow != null)
                {
                    totalRow.actual_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.accounting) : 0.0M;
                    totalRow.budget_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.budget) : 0.0M;
                    totalRow.revised_budget_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.budgetAdjustments) : 0.0M;
                    totalRow.forecast_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.Forecast) : 0.0M;
                    totalRow.fin_plan_year1 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year1Amount) : 0.0M;
                    totalRow.fin_plan_year2 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year2Amount) : 0.0M;
                    totalRow.fin_plan_year3 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year3Amount) : 0.0M;
                    totalRow.fin_plan_year4 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year4Amount) : 0.0M;

                    consequenceAdjustedBudgetDbContext.SaveChanges();
                }
                else
                {
                    consequenceAdjustedBudgetDbContext.tco_budget_totals.Add(new tco_budget_totals()
                    {
                        pk_id = id,
                        fk_tenant_id = userDetails.tenant_id,
                        serviceAreaID = serviceAreaID,
                        fk_action_type = actionType,
                        budget_year = budgetYear,
                        actual_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.accounting) : 0.0M,
                        budget_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.budget) : 0.0M,
                        revised_budget_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.budgetAdjustments) : 0.0M,
                        forecast_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.Forecast) : 0.0M,
                        fin_plan_year1 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year1Amount) : 0.0M,
                        fin_plan_year2 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year2Amount) : 0.0M,
                        fin_plan_year3 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year3Amount) : 0.0M,
                        fin_plan_year4 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year4Amount) : 0.0M
                    });
                    consequenceAdjustedBudgetDbContext.SaveChanges();
                }

                string totalText = ((langStringValuesBudgetProposal.FirstOrDefault(v => v.Key == "BP_CR_NP_total_text")).Value).LangText;

                if (lstAggregates.Count() > 0)
                {
                    lstAggregates = lstAggregates.OrderBy(x => x.actionID).ThenBy(x => x.priority).ThenBy(x => x.description).ToList();

                    lstAggregates.Add(new clsAggregates()
                    {
                        actionID = -1,
                        id = null,
                        description = totalText,
                        accounting = (from r in lstAggregates
                                      select r.accounting).Sum(),
                        budget = (from r in lstAggregates
                                  select r.budget).Sum(),
                        budgetAdjustments = (from r in lstAggregates
                                             select r.budgetAdjustments).Sum(),
                        isManuallyAdded = 0,
                        Forecast = (from r in lstAggregates
                                    where r.actionID != 30 && r.actionID != 40
                                    select r.Forecast).Sum(),
                        year1Amount = (from r in lstAggregates
                                       where r.actionID != 30 && r.actionID != 40
                                       select r.year1Amount).Sum(),
                        year2Amount = (from r in lstAggregates
                                       where r.actionID != 30 && r.actionID != 40
                                       select r.year2Amount).Sum(),
                        year3Amount = (from r in lstAggregates
                                       where r.actionID != 30 && r.actionID != 40
                                       select r.year3Amount).Sum(),
                        year4Amount = (from r in lstAggregates
                                       where r.actionID != 30 && r.actionID != 40
                                       select r.year4Amount).Sum()
                    });
                }

                columnTitles.Add("id");
                columnTitles.Add(" ");

                columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "cmn_filter_priority")).Value).LangText);
                columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "cmn_category")).Value).LangText);
                columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "cmn_last_modified")).Value).LangText);

                columnTitles.Add(budgetYear.ToString());
                columnTitles.Add((budgetYear + 1).ToString());
                columnTitles.Add((budgetYear + 2).ToString());
                columnTitles.Add((budgetYear + 3).ToString());
                columnTitles.Add(" ");
                columnTitles.Add(" ");

                columnFields.Add("id");
                columnFields.Add("serviceArea");

                columnFields.Add("priority");
                columnFields.Add("category");
                columnFields.Add("lastModified");

                columnFields.Add("year1Amount");
                columnFields.Add("year2Amount");
                columnFields.Add("year3Amount");
                columnFields.Add("year4Amount");
                columnFields.Add("isClickable");
                columnFields.Add("isNew");

                bool isEditable = true;
                bool bcCheck = _utility.BudgetChangesCheck(userID, budgetYear);
                if (bcCheck == false)
                {
                    isEditable = false;
                }

                var tbm = consequenceAdjustedBudgetDbContext.tbm_budget_meeting.Where(x => (x.fk_tenant_id == userDetails.tenant_id && x.active == 2)).OrderByDescending(x => x.updated).Select(x => x).ToList();

                foreach (clsAggregates x in lstAggregates)
                {
                    var tgtUserDetails = _utility.GetUserDetailsById(userID, x.updatedBy);
                    group = new JObject();

                    group.id = x.id;
                    group.serviceArea = x.description;

                    if (x.description == totalText)
                    {
                        group.priority = "";

                        group.category = "";

                        group.lastUpdated = "";
                    }
                    else
                    {
                        group.priority = (x.actionID == 30 || x.actionID == 40) ? "" : Convert.ToString(x.priority);
                        if (x.fk_area_id == 0)
                        {
                            group.category = "";
                        }
                        else
                        {
                            group.category = assessmentCategories.FirstOrDefault(y => y.area_id == x.fk_area_id).area_name;
                        }

                        group.lastUpdated = (x.actionID == 30 || x.actionID == 40) ? "" :
                                             tgtUserDetails == null ? "" : tgtUserDetails.first_name + " " + tgtUserDetails.last_name + " " + x.updated.ToShortDateString();
                    }

                    lst = new JArray();
                    dynamic gridData = new JArray();

                    if (section.ToLower() == clsConstants.ActionType.DemographicBudgetAdjustments.ToString().ToLower())
                    {
                        gridData.Add(x.year1Amount.Value);
                        gridData.Add(x.year2Amount.Value);
                        gridData.Add(x.year3Amount.Value);
                        gridData.Add(x.year4Amount.Value);
                    }
                    else
                    {
                        gridData.Add(FormatValue(x.year1Amount.Value, divideByMillions));
                        gridData.Add(FormatValue(x.year2Amount.Value, divideByMillions));
                        gridData.Add(FormatValue(x.year3Amount.Value, divideByMillions));
                        gridData.Add(FormatValue(x.year4Amount.Value, divideByMillions));
                    }

                    lst.Add(gridData);
                    group.gridData = lst;

                    if (section.ToLower() == clsConstants.ActionType.CostReductions.ToString().ToLower() || section.ToLower() == clsConstants.ActionType.NewPriorities.ToString().ToLower())
                    {
                        if (x.actionID == 30 || x.actionID == 40)
                        {
                            group.isClickable = false;
                        }
                        else
                        {
                            if (x.description == totalText)
                            {
                                group.isClickable = false;
                            }
                            else
                            {
                                group.isClickable = true;
                            }
                        }
                    }
                    else
                    {
                        if (x.description == totalText)
                        {
                            group.isClickable = false;
                        }
                        else
                        {
                            group.isClickable = true;
                        }
                    }

                    ///////////For identifying new action///////////
                    bool isNew = false;
                    if (section.ToLower() == clsConstants.ActionType.CostReductions.ToString().ToLower() || section.ToLower() == clsConstants.ActionType.NewPriorities.ToString().ToLower())
                    {
                        if ((x.actionID != Convert.ToInt32(clsConstants.ActionType.CostReductionsSubType)) && (x.actionID != Convert.ToInt32(clsConstants.ActionType.NewPrioritiesSubType)))
                        {
                            if (tbm.Count > 0)
                            {
                                DateTime tbmdt = tbm.First().updated;
                                if (x.updated > tbmdt)
                                {
                                    isNew = true;
                                }
                                else
                                {
                                    isNew = false;
                                }
                            }
                            else
                            {
                                isNew = true;
                            }
                        }
                    }
                    group.isNew = isNew;
                    /////////////////////////////////
                    jsonData.Add(group);
                }

                if (lstAggregates.Count() == 0)
                {
                    group = new JObject();

                    group.id = null;
                    group.serviceArea = totalText;

                    lst = new JArray();
                    dynamic gridData = new JArray();

                    gridData.Add(0);
                    gridData.Add(0);
                    gridData.Add(0);
                    gridData.Add(0);
                    lst.Add(gridData);
                    group.gridData = lst;
                    jsonData.Add(group);
                }

                dynamic data = new JObject();
                data.Add("columnTitles", columnTitles);
                data.Add("columnFields", columnFields);
                data.Add("rows", jsonData);
                JObject jsonCfg = JObject.Parse(_utility.GetApplicationSetting("GetBudgetAndFinancialData_grid_config"));
                data.gridConfig = jsonCfg;
                data.groupId = actionType;
                data.amountFormat = "#,##";
                data.isEditable = isEditable;
                data.changeid = GetBudgetChangeId(userID, BudgetYear);
                return data;
            }
            //catch (DbEntityValidationException dbEx)
            //{
            //    dynamic data = new JObject();
            //    data.error = dbEx.Message;
            //    return data;
            //}
            catch (Exception ex)
            {
                dynamic data = new JObject();
                data.error = ex.Message;
                return data;
            }
        }

        private dynamic FormatDetailsTreeGridData(IEnumerable<Result> lstResult, string section, string userID, int actionType, string serviceAreaID, bool isBudgetAdjustments, int BudgetYear, bool divideByMillions = false)
        {
            UserData userDetails = _utility.GetUserDetails(userID);
            dynamic jsonData = new JArray();
            dynamic group = new JObject();
            dynamic gridData = new JArray();
            dynamic columnTitles = new JArray();
            dynamic columnFields = new JArray();

            int? budgetYear = BudgetYear;

            if (isBudgetAdjustments)
            {
                if (section.ToLower() == clsConstants.ActionType.BudgetAdjustmentsFromCurrentYear.ToString().ToLower())
                {
                    var currentBudgetData = (from o in lstResult
                                             group o by new { o.actionID } into currentBData
                                             select new
                                             {
                                                 year1Amount = currentBData.Sum(x => x.year1Amount),
                                                 year2Amount = currentBData.Sum(x => x.year2Amount),
                                                 year3Amount = currentBData.Sum(x => x.year3Amount),
                                                 year4Amount = currentBData.Sum(x => x.year4Amount)
                                             }).ToList();

                    dynamic budgetAdjustmentsResult = new JArray();
                    dynamic budgetAdjustmentRow = new JObject();
                    budgetAdjustmentRow.ID = "BudgetAdjustmentsFromCurrentYear";
                    budgetAdjustmentRow.year1Amount = currentBudgetData.FirstOrDefault() == null ? 0 : currentBudgetData.FirstOrDefault().year1Amount;
                    budgetAdjustmentRow.year2Amount = currentBudgetData.FirstOrDefault() == null ? 0 : currentBudgetData.FirstOrDefault().year2Amount;
                    budgetAdjustmentRow.year3Amount = currentBudgetData.FirstOrDefault() == null ? 0 : currentBudgetData.FirstOrDefault().year3Amount;
                    budgetAdjustmentRow.year4Amount = currentBudgetData.FirstOrDefault() == null ? 0 : currentBudgetData.FirstOrDefault().year4Amount;
                    budgetAdjustmentsResult.Add(budgetAdjustmentRow);

                    return budgetAdjustmentsResult;
                }
                if (section.ToLower() == clsConstants.ActionType.BudgetChangesFromLastApprovedFinancialPlan.ToString().ToLower())
                {
                    var lastApprovedBudgetData = (from o in lstResult
                                                  group o by new { o.actionID } into lastApprovedBData
                                                  select new
                                                  {
                                                      year1Amount = lastApprovedBData.Sum(x => x.year1Amount),
                                                      year2Amount = lastApprovedBData.Sum(x => x.year2Amount),
                                                      year3Amount = lastApprovedBData.Sum(x => x.year3Amount),
                                                      year4Amount = lastApprovedBData.Sum(x => x.year4Amount)
                                                  }).ToList();

                    dynamic budgetAdjustmentsResult = new JArray();
                    dynamic budgetAdjustmentRow = new JObject();
                    budgetAdjustmentRow.ID = "BudgetChangesFromLastApprovedFinancialPlan";
                    budgetAdjustmentRow.year1Amount = lastApprovedBudgetData.FirstOrDefault() == null ? 0 : lastApprovedBudgetData.FirstOrDefault().year1Amount;
                    budgetAdjustmentRow.year2Amount = lastApprovedBudgetData.FirstOrDefault() == null ? 0 : lastApprovedBudgetData.FirstOrDefault().year2Amount;
                    budgetAdjustmentRow.year3Amount = lastApprovedBudgetData.FirstOrDefault() == null ? 0 : lastApprovedBudgetData.FirstOrDefault().year3Amount;
                    budgetAdjustmentRow.year4Amount = lastApprovedBudgetData.FirstOrDefault() == null ? 0 : lastApprovedBudgetData.FirstOrDefault().year4Amount;
                    budgetAdjustmentsResult.Add(budgetAdjustmentRow);

                    return budgetAdjustmentsResult;
                }
            }

            TenantDBContext consequenceAdjustedBudgetDbContext = _utility.GetTenantDBContext();

            try
            {
                Dictionary<string, clsLanguageString> langStringValues = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "ConsequenceAdjustedBudget");

                List<clsAggregates> lstAggregates = (from r in lstResult
                                                     group r by new { r.description, r.ID, r.actionID } into g
                                                     select new clsAggregates
                                                     {
                                                         actionID = g.Key.actionID,
                                                         id = g.Key.ID,
                                                         description = g.Key.description,
                                                         accounting = g.Sum(x => x.accounting),
                                                         budget = g.Sum(x => x.budget),
                                                         budgetAdjustments = g.Sum(x => x.budgetAdjustment),
                                                         Forecast = g.Sum(x => x.forecast),
                                                         year1Amount = g.Sum(x => x.year1Amount),
                                                         year2Amount = g.Sum(x => x.year2Amount),
                                                         year3Amount = g.Sum(x => x.year3Amount),
                                                         year4Amount = g.Sum(x => x.year4Amount)
                                                     }).ToList();

                int id = 0;
                List<tco_budget_totals> lstActions = consequenceAdjustedBudgetDbContext.tco_budget_totals.ToList();
                if (lstActions.Count() > 0)
                {
                    lstActions.Sort((v1, v2) => v1.pk_id.CompareTo(v2.pk_id));
                    id = ((lstActions.LastOrDefault()).pk_id) + 1;
                }
                else
                {
                    id = 100;
                }
                tco_budget_totals totalRow = consequenceAdjustedBudgetDbContext.tco_budget_totals.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                                      && x.fk_action_type.Value == actionType
                                                                                                      && x.serviceAreaID == serviceAreaID
                                                                                                      && x.budget_year == budgetYear).FirstOrDefault();
                if (totalRow != null)
                {
                    totalRow.actual_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.accounting) : 0.0M;
                    totalRow.budget_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.budget) : 0.0M;
                    totalRow.revised_budget_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.budgetAdjustments) : 0.0M;
                    totalRow.forecast_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.Forecast) : 0.0M;
                    totalRow.fin_plan_year1 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year1Amount) : 0.0M;
                    totalRow.fin_plan_year2 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year2Amount) : 0.0M;
                    totalRow.fin_plan_year3 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year3Amount) : 0.0M;
                    totalRow.fin_plan_year4 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year4Amount) : 0.0M;

                    consequenceAdjustedBudgetDbContext.SaveChanges();
                }
                else
                {
                    consequenceAdjustedBudgetDbContext.tco_budget_totals.Add(new tco_budget_totals()
                    {
                        pk_id = id,
                        fk_tenant_id = userDetails.tenant_id,
                        serviceAreaID = serviceAreaID,
                        fk_action_type = actionType,
                        budget_year = budgetYear,
                        actual_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.accounting) : 0.0M,
                        budget_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.budget) : 0.0M,
                        revised_budget_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.budgetAdjustments) : 0.0M,
                        forecast_data = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.Forecast) : 0.0M,
                        fin_plan_year1 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year1Amount) : 0.0M,
                        fin_plan_year2 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year2Amount) : 0.0M,
                        fin_plan_year3 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year3Amount) : 0.0M,
                        fin_plan_year4 = lstAggregates.Count() > 0 ? lstAggregates.Sum(x => x.year4Amount) : 0.0M
                    });
                    consequenceAdjustedBudgetDbContext.SaveChanges();
                }

                string totalText = section.ToLower() == clsConstants.ActionType.OriginalAndTechnicalAdjustedBudget.ToString().ToLower() ? ((langStringValues.FirstOrDefault(v => v.Key == "technical_adjusted_budget_text")).Value).LangText :
                                  section.ToLower() == clsConstants.ActionType.BudgetAdjustmentsFromCurrentYear.ToString().ToLower() ? ((langStringValues.FirstOrDefault(v => v.Key == "total_budget_changes_from_current_year_text")).Value).LangText :
                                  section.ToLower() == clsConstants.ActionType.BudgetChangesFromLastApprovedFinancialPlan.ToString().ToLower() ? ((langStringValues.FirstOrDefault(v => v.Key == "total_budget_changes_from_last_plan_text")).Value).LangText : "";

                if (lstAggregates.Count > 0)
                {
                    lstAggregates.Add(new clsAggregates()
                    {
                        actionID = lstAggregates.FirstOrDefault().actionID,
                        id = null,
                        description = totalText,
                        accounting = (from r in lstAggregates
                                      select r.accounting).Sum(),
                        budget = (from r in lstAggregates
                                  select r.budget).Sum(),
                        budgetAdjustments = (from r in lstAggregates
                                             select r.budgetAdjustments).Sum(),
                        isManuallyAdded = 0,
                        Forecast = (from r in lstAggregates
                                    select r.Forecast).Sum(),
                        year1Amount = (from r in lstAggregates
                                       select r.year1Amount).Sum(),
                        year2Amount = (from r in lstAggregates
                                       select r.year2Amount).Sum(),
                        year3Amount = (from r in lstAggregates
                                       select r.year3Amount).Sum(),
                        year4Amount = (from r in lstAggregates
                                       select r.year4Amount).Sum()
                    });
                }

                group.id = 0;
                group.serviceArea = section.ToLower() == clsConstants.ActionType.BudgetAdjustmentsFromCurrentYear.ToString().ToLower() ? ((langStringValues.FirstOrDefault(v => v.Key == "budget_adjustment_text")).Value).LangText :
                                                         ((langStringValues.FirstOrDefault(v => v.Key == "budget_changes_from_last_plan_text")).Value).LangText;
                group.numberType = "";

                gridData.Add("");
                gridData.Add("");
                gridData.Add("");
                gridData.Add("");
                group.Add("gridData", gridData);
                group.parentId = null;
                jsonData.Add(group);

                foreach (clsAggregates x in lstAggregates)
                {
                    if (x.description != totalText)
                    {
                        group = new JObject();
                        gridData = new JArray();

                        group.id = x.id;
                        group.serviceArea = x.description;
                        group.numberType = "#,##";

                        gridData.Add(FormatValue(x.year1Amount.Value, divideByMillions));
                        gridData.Add(FormatValue(x.year2Amount.Value, divideByMillions));
                        gridData.Add(FormatValue(x.year3Amount.Value, divideByMillions));
                        gridData.Add(FormatValue(x.year4Amount.Value, divideByMillions));
                        group.Add("gridData", gridData);
                        group.parentId = 0;
                        jsonData.Add(group);
                    }
                }

                group = new JObject();
                gridData = new JArray();
                if (lstAggregates.Count() > 0)
                {
                    group.id = 1;
                    group.serviceArea = lstAggregates.LastOrDefault().description;
                    group.numberType = "#,##";

                    gridData.Add(FormatValue(lstAggregates.LastOrDefault().year1Amount.Value, divideByMillions));
                    gridData.Add(FormatValue(lstAggregates.LastOrDefault().year2Amount.Value, divideByMillions));
                    gridData.Add(FormatValue(lstAggregates.LastOrDefault().year3Amount.Value, divideByMillions));
                    gridData.Add(FormatValue(lstAggregates.LastOrDefault().year4Amount.Value, divideByMillions));
                    group.Add("gridData", gridData);
                    group.parentId = null;
                    jsonData.Add(group);
                }
                else
                {
                    group.id = 1;
                    group.serviceArea = totalText;
                    group.numberType = "#,##";

                    gridData.Add(0);
                    gridData.Add(0);
                    gridData.Add(0);
                    gridData.Add(0);
                    group.Add("gridData", gridData);
                    group.parentId = null;
                    jsonData.Add(group);
                }

                columnTitles.Add(" ");
                columnTitles.Add(" ");
                columnTitles.Add(" ");

                columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "plan_text")).Value).LangText + " " + budgetYear.ToString());
                columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "plan_text")).Value).LangText + " " + (budgetYear + 1).ToString());
                columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "plan_text")).Value).LangText + " " + (budgetYear + 2).ToString());
                columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "plan_text")).Value).LangText + " " + (budgetYear + 3).ToString());

                columnFields.Add("id");
                columnFields.Add("parentId");
                columnFields.Add("serviceArea");

                columnFields.Add("year1Amount");
                columnFields.Add("year2Amount");
                columnFields.Add("year3Amount");
                columnFields.Add("year4Amount");

                dynamic data = new JObject();
                data.Add("columnTitles", columnTitles);
                data.Add("columnFields", columnFields);
                data.Add("rows", jsonData);
                JObject jsonCfg = JObject.Parse(_utility.GetApplicationSetting("GetBudgetAndFinancialData_grid_config"));
                data.gridConfig = jsonCfg;
                data.groupId = actionType;
                return data;
            }
            //catch (DbEntityValidationException dbEx)
            //{
            //    dynamic data = new JObject();
            //    data.error = dbEx.Message;
            //    return data;
            //}
            catch (Exception ex)
            {
                dynamic data = new JObject();
                data.error = ex.Message;
                return data;
            }
        }

        private async Task<dynamic> GetCurrFinancialExpenseAndYearendItemsAsync(string userID, int BudgetYear, bool isBudgetChange = false)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ConsequenceAdjustedBudget");
            int budgetYear = BudgetYear;
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            List<int> CurrFinDataGrpId = new List<int>() { 8, 9 };
            List<int> CurrYearendDataGrpId = new List<int>() { 11, 12 };
            var CurrentBudgetdataCurrFinData = await (from a in consequenceAdjustedBudgetDbContext.tbu_trans_detail
                                                join b in consequenceAdjustedBudgetDbContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code }
                                                                                                      equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                                                join c in consequenceAdjustedBudgetDbContext.gmd_reporting_line on b.fk_kostra_account_code equals c.fk_kostra_account_code
                                                where (a.fk_tenant_id == userDetails.tenant_id
                                                    && a.budget_year == (budgetYear - 1)
                                                         && CurrFinDataGrpId.Contains(c.line_group_id) && b.isActive == true && c.report == "Drift")
                                                group a by new { c.line_group, c.line_group_id } into g
                                                select new
                                                {
                                                    g.Key.line_group_id,
                                                    Year1 = g.Sum(x => x.amount_year_1),
                                                    Year2 = g.Sum(x => x.amount_year_2),
                                                    Year3 = g.Sum(x => x.amount_year_3),
                                                    Year4 = g.Sum(x => x.amount_year_4)
                                                }).ToListAsync();
            var CurrentBudgetdataCurrYearendData = await (from a in consequenceAdjustedBudgetDbContext.tbu_trans_detail
                                                    join b in consequenceAdjustedBudgetDbContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code }
                                                                                                          equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                                                    join c in consequenceAdjustedBudgetDbContext.gmd_reporting_line on b.fk_kostra_account_code equals c.fk_kostra_account_code
                                                    where (a.fk_tenant_id == userDetails.tenant_id
                                                        && a.budget_year == (budgetYear - 1)
                                                             && CurrYearendDataGrpId.Contains(c.line_group_id) && b.isActive == true && c.report == "Drift")
                                                    group a by new { c.line_group, c.line_group_id } into g
                                                    select new
                                                    {
                                                        g.Key.line_group_id,
                                                        Year1 = g.Sum(x => x.amount_year_1),
                                                        Year2 = g.Sum(x => x.amount_year_2),
                                                        Year3 = g.Sum(x => x.amount_year_3),
                                                        Year4 = g.Sum(x => x.amount_year_4)
                                                    }).ToListAsync();
            var CurrFinData = await (from d in consequenceAdjustedBudgetDbContext.tfp_trans_header
                               join a in consequenceAdjustedBudgetDbContext.tfp_trans_detail on new { a = d.fk_tenant_id, b = d.pk_action_id }
                                                                                         equals new { a = a.fk_tenant_id, b = a.fk_action_id }
                               join b in consequenceAdjustedBudgetDbContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code }
                                                                                     equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                               join c in consequenceAdjustedBudgetDbContext.gmd_reporting_line on b.fk_kostra_account_code equals c.fk_kostra_account_code
                               where (a.fk_tenant_id == userDetails.tenant_id
                                   && a.budget_year == budgetYear
                                       && CurrFinDataGrpId.Contains(c.line_group_id) && b.isActive == true && c.report == "Drift" && d.action_type >= 5)
                               group a by new { c.line_group, c.line_group_id } into g
                               select new
                               {
                                   g.Key.line_group_id,
                                   Year1 = (g.Sum(x => x.year_1_amount) * -1) / 1000,
                                   Year2 = (g.Sum(x => x.year_2_amount) * -1) / 1000,
                                   Year3 = (g.Sum(x => x.year_3_amount) * -1) / 1000,
                                   Year4 = (g.Sum(x => x.year_4_amount) * -1) / 1000
                               }).ToListAsync();
            var newbdtbdtchange = await consequenceAdjustedBudgetDbContext.tfp_budget_changes.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear && x.status == 4 && x.org_budget_flag == 1).Select(x => x.pk_change_id).ToListAsync();
            var newbudgetCurrFinData = await (from d in consequenceAdjustedBudgetDbContext.tfp_trans_header
                                        join a in consequenceAdjustedBudgetDbContext.tfp_trans_detail on new { a = d.fk_tenant_id, b = d.pk_action_id }
                                                                                                  equals new { a = a.fk_tenant_id, b = a.fk_action_id }
                                        join b in consequenceAdjustedBudgetDbContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code }
                                                                                              equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                                        join c in consequenceAdjustedBudgetDbContext.gmd_reporting_line on b.fk_kostra_account_code equals c.fk_kostra_account_code
                                        where (a.fk_tenant_id == userDetails.tenant_id
                                            && a.budget_year == budgetYear
                                                && CurrFinDataGrpId.Contains(c.line_group_id) && b.isActive == true && c.report == "Drift" && d.action_type >= 5
                                                && (a.fk_change_id == 0 || newbdtbdtchange.Contains(a.fk_change_id))
                                                )
                                        group a by new { c.line_group, c.line_group_id } into g
                                        select new
                                        {
                                            g.Key.line_group_id,
                                            Year1 = (g.Sum(x => x.year_1_amount) * -1) / 1000,
                                            Year2 = (g.Sum(x => x.year_2_amount) * -1) / 1000,
                                            Year3 = (g.Sum(x => x.year_3_amount) * -1) / 1000,
                                            Year4 = (g.Sum(x => x.year_4_amount) * -1) / 1000
                                        }).ToListAsync();
            var lastfpbdtchange = await consequenceAdjustedBudgetDbContext.tfp_budget_changes.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear && x.status == 4).Select(x => x.pk_change_id).ToListAsync();
            var lastfpCurrFinData = await (from d in consequenceAdjustedBudgetDbContext.tfp_trans_header
                                     join a in consequenceAdjustedBudgetDbContext.tfp_trans_detail on new { a = d.fk_tenant_id, b = d.pk_action_id }
                                                                                               equals new { a = a.fk_tenant_id, b = a.fk_action_id }
                                     join b in consequenceAdjustedBudgetDbContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code }
                                                                                           equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                                     join c in consequenceAdjustedBudgetDbContext.gmd_reporting_line on b.fk_kostra_account_code equals c.fk_kostra_account_code
                                     where (a.fk_tenant_id == userDetails.tenant_id
                                         && a.budget_year == budgetYear
                                             && CurrFinDataGrpId.Contains(c.line_group_id) && b.isActive == true && c.report == "Drift" && d.action_type >= 5
                                             && (a.fk_change_id == 0 || lastfpbdtchange.Contains(a.fk_change_id))
                                             )
                                     group a by new { c.line_group, c.line_group_id } into g
                                     select new
                                     {
                                         g.Key.line_group_id,
                                         Year1 = (g.Sum(x => x.year_1_amount) * -1) / 1000,
                                         Year2 = (g.Sum(x => x.year_2_amount) * -1) / 1000,
                                         Year3 = (g.Sum(x => x.year_3_amount) * -1) / 1000,
                                         Year4 = (g.Sum(x => x.year_4_amount) * -1) / 1000
                                     }).ToListAsync();
            var changebdtchange = await consequenceAdjustedBudgetDbContext.tfp_budget_changes.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear && x.status == 4).Select(x => x.pk_change_id).ToListAsync();
            var changeCurrFinData = await (from d in consequenceAdjustedBudgetDbContext.tfp_trans_header
                                     join a in consequenceAdjustedBudgetDbContext.tfp_trans_detail on new { a = d.fk_tenant_id, b = d.pk_action_id }
                                                                                               equals new { a = a.fk_tenant_id, b = a.fk_action_id }
                                     join b in consequenceAdjustedBudgetDbContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code }
                                                                                           equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                                     join c in consequenceAdjustedBudgetDbContext.gmd_reporting_line on b.fk_kostra_account_code equals c.fk_kostra_account_code
                                     where (a.fk_tenant_id == userDetails.tenant_id
                                         && a.budget_year == budgetYear
                                             && CurrFinDataGrpId.Contains(c.line_group_id) && b.isActive == true && c.report == "Drift" && d.action_type >= 5
                                              && (changebdtchange.Contains(a.fk_change_id))
                                             )
                                     group a by new { c.line_group, c.line_group_id } into g
                                     select new
                                     {
                                         g.Key.line_group_id,
                                         Year1 = (g.Sum(x => x.year_1_amount) * -1) / 1000,
                                         Year2 = (g.Sum(x => x.year_2_amount) * -1) / 1000,
                                         Year3 = (g.Sum(x => x.year_3_amount) * -1) / 1000,
                                         Year4 = (g.Sum(x => x.year_4_amount) * -1) / 1000
                                     }).ToListAsync();

            var CurrYearendData = await (from d in consequenceAdjustedBudgetDbContext.tfp_trans_header
                                   join a in consequenceAdjustedBudgetDbContext.tfp_trans_detail on new { a = d.fk_tenant_id, b = d.pk_action_id }
                                                                                             equals new { a = a.fk_tenant_id, b = a.fk_action_id }
                                   join b in consequenceAdjustedBudgetDbContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code }
                                                                                         equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                                   join c in consequenceAdjustedBudgetDbContext.gmd_reporting_line on b.fk_kostra_account_code equals c.fk_kostra_account_code
                                   where (a.fk_tenant_id == userDetails.tenant_id
                                       && a.budget_year == budgetYear
                                           && CurrYearendDataGrpId.Contains(c.line_group_id) && b.isActive == true && c.report == "Drift" && d.action_type >= 5)
                                   group a by new { c.line_group, c.line_group_id } into g
                                   select new
                                   {
                                       g.Key.line_group_id,
                                       Year1 = (g.Sum(x => x.year_1_amount) * -1) / 1000,
                                       Year2 = (g.Sum(x => x.year_2_amount) * -1) / 1000,
                                       Year3 = (g.Sum(x => x.year_3_amount) * -1) / 1000,
                                       Year4 = (g.Sum(x => x.year_4_amount) * -1) / 1000
                                   }).ToListAsync();

            var newbudgetCurrYearendFinData = await (from d in consequenceAdjustedBudgetDbContext.tfp_trans_header
                                               join a in consequenceAdjustedBudgetDbContext.tfp_trans_detail on new { a = d.fk_tenant_id, b = d.pk_action_id }
                                                                                                         equals new { a = a.fk_tenant_id, b = a.fk_action_id }
                                               join b in consequenceAdjustedBudgetDbContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code }
                                                                                                     equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                                               join c in consequenceAdjustedBudgetDbContext.gmd_reporting_line on b.fk_kostra_account_code equals c.fk_kostra_account_code
                                               where (a.fk_tenant_id == userDetails.tenant_id
                                                   && a.budget_year == budgetYear
                                                       && CurrYearendDataGrpId.Contains(c.line_group_id) && b.isActive == true && c.report == "Drift" && d.action_type >= 5
                                                       && (a.fk_change_id == 0 || newbdtbdtchange.Contains(a.fk_change_id))
                                                       )
                                               group a by new { c.line_group, c.line_group_id } into g
                                               select new
                                               {
                                                   g.Key.line_group_id,
                                                   Year1 = (g.Sum(x => x.year_1_amount) * -1) / 1000,
                                                   Year2 = (g.Sum(x => x.year_2_amount) * -1) / 1000,
                                                   Year3 = (g.Sum(x => x.year_3_amount) * -1) / 1000,
                                                   Year4 = (g.Sum(x => x.year_4_amount) * -1) / 1000
                                               }).ToListAsync();

            var lastfpCurrFinYearendData = await (from d in consequenceAdjustedBudgetDbContext.tfp_trans_header
                                            join a in consequenceAdjustedBudgetDbContext.tfp_trans_detail on new { a = d.fk_tenant_id, b = d.pk_action_id }
                                                                                                      equals new { a = a.fk_tenant_id, b = a.fk_action_id }
                                            join b in consequenceAdjustedBudgetDbContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code }
                                                                                                  equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                                            join c in consequenceAdjustedBudgetDbContext.gmd_reporting_line on b.fk_kostra_account_code equals c.fk_kostra_account_code
                                            where (a.fk_tenant_id == userDetails.tenant_id
                                                && a.budget_year == budgetYear
                                                    && CurrYearendDataGrpId.Contains(c.line_group_id) && b.isActive == true && c.report == "Drift" && d.action_type >= 5
                                                    && (a.fk_change_id == 0 || lastfpbdtchange.Contains(a.fk_change_id))
                                                    )
                                            group a by new { c.line_group, c.line_group_id } into g
                                            select new
                                            {
                                                g.Key.line_group_id,
                                                Year1 = (g.Sum(x => x.year_1_amount) * -1) / 1000,
                                                Year2 = (g.Sum(x => x.year_2_amount) * -1) / 1000,
                                                Year3 = (g.Sum(x => x.year_3_amount) * -1) / 1000,
                                                Year4 = (g.Sum(x => x.year_4_amount) * -1) / 1000
                                            }).ToListAsync();
            var changeCurrFinYearendData = await (from d in consequenceAdjustedBudgetDbContext.tfp_trans_header
                                            join a in consequenceAdjustedBudgetDbContext.tfp_trans_detail on new { a = d.fk_tenant_id, b = d.pk_action_id }
                                                                                                      equals new { a = a.fk_tenant_id, b = a.fk_action_id }
                                            join b in consequenceAdjustedBudgetDbContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code }
                                                                                                  equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                                            join c in consequenceAdjustedBudgetDbContext.gmd_reporting_line on b.fk_kostra_account_code equals c.fk_kostra_account_code
                                            where (a.fk_tenant_id == userDetails.tenant_id
                                                && a.budget_year == budgetYear
                                                    && CurrYearendDataGrpId.Contains(c.line_group_id) && b.isActive == true && c.report == "Drift" && d.action_type >= 5
                                                     && (changebdtchange.Contains(a.fk_change_id))
                                                    )
                                            group a by new { c.line_group, c.line_group_id } into g
                                            select new
                                            {
                                                g.Key.line_group_id,
                                                Year1 = (g.Sum(x => x.year_1_amount) * -1) / 1000,
                                                Year2 = (g.Sum(x => x.year_2_amount) * -1) / 1000,
                                                Year3 = (g.Sum(x => x.year_3_amount) * -1) / 1000,
                                                Year4 = (g.Sum(x => x.year_4_amount) * -1) / 1000
                                            }).ToListAsync();
            dynamic jsonData = new JArray();

            dynamic row = new JObject();
            row.id = 1;
            row.name = ((langStringValues.FirstOrDefault(v => v.Key == "Corr_financial_expences")).Value).LangText;
            dynamic gridData = new JArray();
            gridData.Add((CurrentBudgetdataCurrFinData.FirstOrDefault(x => x.line_group_id == 6) == null ? 0 : Math.Round((CurrentBudgetdataCurrFinData.FirstOrDefault(x => x.line_group_id ==

 6).Year1) / 1000)));
            if (isBudgetChange)
            {
                if (CurrFinData.Count > 0)
                {
                    gridData.Add((newbudgetCurrFinData.FirstOrDefault(x => x.line_group_id == 6) == null ? 0 : Math.Round((newbudgetCurrFinData.FirstOrDefault(x => x.line_group_id == 6).Year1) / 1000)));
                    gridData.Add((lastfpCurrFinData.FirstOrDefault(x => x.line_group_id == 6) == null ? 0 : Math.Round((lastfpCurrFinData.FirstOrDefault(x => x.line_group_id == 6).Year1) / 1000)));
                    gridData.Add((changeCurrFinData.FirstOrDefault(x => x.line_group_id == 6) == null ? 0 : Math.Round((changeCurrFinData.FirstOrDefault(x => x.line_group_id == 6).Year1) / 1000)));
                    gridData.Add(CurrFinData[0].Year1);
                    gridData.Add(CurrFinData[0].Year2);
                    gridData.Add(CurrFinData[0].Year3);
                    gridData.Add(CurrFinData[0].Year4);
                }
                else
                {
                    gridData.Add(0);
                    gridData.Add(0);
                    gridData.Add(0);
                    gridData.Add(0);
                    gridData.Add(0);
                    gridData.Add(0);
                    gridData.Add(0);
                }
            }
            else
            {
                if (CurrFinData.Count > 0)
                {
                    gridData.Add(CurrFinData[0].Year1);
                    gridData.Add(CurrFinData[0].Year2);
                    gridData.Add(CurrFinData[0].Year3);
                    gridData.Add(CurrFinData[0].Year4);
                }
                else
                {
                    gridData.Add(0);
                    gridData.Add(0);
                    gridData.Add(0);
                    gridData.Add(0);
                }
            }
            row.Add("griData", gridData);
            jsonData.Add(row);

            row = new JObject();
            row.id = 2;
            row.name = ((langStringValues.FirstOrDefault(v => v.Key == "Corr_year_end_items")).Value).LangText;
            gridData = new JArray();
            gridData.Add((CurrentBudgetdataCurrYearendData.FirstOrDefault(x => x.line_group_id == 6) == null ? 0 : Math.Round((CurrentBudgetdataCurrYearendData.FirstOrDefault(x => x.line_group_id ==

 6).Year1) / 1000)));
            if (isBudgetChange)
            {
                if (CurrYearendData.Count > 0)
                {
                    gridData.Add((newbudgetCurrYearendFinData.FirstOrDefault(x => x.line_group_id == 6) == null ? 0 : Math.Round((newbudgetCurrYearendFinData.FirstOrDefault(x => x.line_group_id == 6).Year1) / 1000)));
                    gridData.Add((lastfpCurrFinYearendData.FirstOrDefault(x => x.line_group_id == 6) == null ? 0 : Math.Round((lastfpCurrFinYearendData.FirstOrDefault(x => x.line_group_id == 6).Year1) / 1000)));
                    gridData.Add((changeCurrFinYearendData.FirstOrDefault(x => x.line_group_id == 6) == null ? 0 : Math.Round((changeCurrFinYearendData.FirstOrDefault(x => x.line_group_id == 6).Year1) / 1000)));
                    gridData.Add(CurrYearendData[0].Year1);
                    gridData.Add(CurrYearendData[0].Year2);
                    gridData.Add(CurrYearendData[0].Year3);
                    gridData.Add(CurrYearendData[0].Year4);
                }
                else
                {
                    gridData.Add(0);
                    gridData.Add(0);
                    gridData.Add(0);
                    gridData.Add(0);
                    gridData.Add(0);
                    gridData.Add(0);
                    gridData.Add(0);
                }
            }
            else
            {
                if (CurrYearendData.Count > 0)
                {
                    gridData.Add(CurrYearendData[0].Year1);
                    gridData.Add(CurrYearendData[0].Year2);
                    gridData.Add(CurrYearendData[0].Year3);
                    gridData.Add(CurrYearendData[0].Year4);
                }
                else
                {
                    gridData.Add(0);
                    gridData.Add(0);
                    gridData.Add(0);
                    gridData.Add(0);
                }
            }
            row.Add("griData", gridData);
            jsonData.Add(row);

            return jsonData;
        }

        private async Task<dynamic> GetGrossProfitKostraAsync(string userID, IEnumerable<clsAggregates> lstAggregates, dynamic currFinancialExpenseAndYearendItems, int BudgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            int budgetYear = BudgetYear;
            TenantDBContext tenantDbContext =await _utility.GetTenantDBContextAsync();
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ConsequenceAdjustedBudget");
            //Gross profit (KOSTRA) =
            //Total revenues + Total central expenses + Operational expences service areas + Curr.financial expence in servcie areas + Curr year end items in service areas
            tco_budget_totals freeIncomeAvailable = await tenantDbContext.tco_budget_totals.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear
                                                                                                     && x.fk_action_type == 1 && x.serviceAreaID == null);
            decimal revenuebudget = Convert.ToDecimal(freeIncomeAvailable == null ? 0 : freeIncomeAvailable.budget_data / 1000);
            decimal revenueyear1 = Convert.ToDecimal(freeIncomeAvailable == null ? 0 : freeIncomeAvailable.fin_plan_year1 / 1000);
            decimal revenueyear2 = Convert.ToDecimal(freeIncomeAvailable == null ? 0 : freeIncomeAvailable.fin_plan_year2 / 1000);
            decimal revenueyear3 = Convert.ToDecimal(freeIncomeAvailable == null ? 0 : freeIncomeAvailable.fin_plan_year3 / 1000);
            decimal revenueyear4 = Convert.ToDecimal(freeIncomeAvailable == null ? 0 : freeIncomeAvailable.fin_plan_year4 / 1000);

            tco_budget_totals centralExpense = await tenantDbContext.tco_budget_totals.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear
                                                                                                     && x.fk_action_type == 100 && x.serviceAreaID == null);
            decimal centralexpensebudget = Convert.ToDecimal(centralExpense == null ? 0 : centralExpense.budget_data / 1000);
            decimal centralexpenseyear1 = Convert.ToDecimal(centralExpense == null ? 0 : centralExpense.fin_plan_year1 / 1000);
            decimal centralexpenseyear2 = Convert.ToDecimal(centralExpense == null ? 0 : centralExpense.fin_plan_year2 / 1000);
            decimal centralexpenseyear3 = Convert.ToDecimal(centralExpense == null ? 0 : centralExpense.fin_plan_year3 / 1000);
            decimal centralexpenseyear4 = Convert.ToDecimal(centralExpense == null ? 0 : centralExpense.fin_plan_year4 / 1000);

            //Get  Operational expences service areas totals
            decimal operationalbudget = lstAggregates.Count() > 0 ? lstAggregates.LastOrDefault().budget.Value / 1000 : 0;
            decimal operationalyear1 = lstAggregates.Count() > 0 ? lstAggregates.LastOrDefault().year1Amount.Value / 1000 : 0;
            decimal operationalyear2 = lstAggregates.Count() > 0 ? lstAggregates.LastOrDefault().year2Amount.Value / 1000 : 0;
            decimal operationalyear3 = lstAggregates.Count() > 0 ? lstAggregates.LastOrDefault().year3Amount.Value / 1000 : 0;
            decimal operationalyear4 = lstAggregates.Count() > 0 ? lstAggregates.LastOrDefault().year4Amount.Value / 1000 : 0;

            //Get Curr.financial expence in servcie areas
            dynamic currdata = currFinancialExpenseAndYearendItems;
            decimal currfinancialexpbudget = Convert.ToDecimal(currdata[0].griData[0]);
            decimal currfinancialexpyear1 = Convert.ToDecimal(currdata[0].griData[1]);
            decimal currfinancialexpyear2 = Convert.ToDecimal(currdata[0].griData[2]);
            decimal currfinancialexpyear3 = Convert.ToDecimal(currdata[0].griData[3]);
            decimal currfinancialexpyear4 = Convert.ToDecimal(currdata[0].griData[4]);

            //Get Curr year end items in service areas
            decimal curryearendbudget = Convert.ToDecimal(currdata[1].griData[0]);
            decimal curryearendyear1 = Convert.ToDecimal(currdata[1].griData[1]);
            decimal curryearendyear2 = Convert.ToDecimal(currdata[1].griData[2]);
            decimal curryearendyear3 = Convert.ToDecimal(currdata[1].griData[3]);
            decimal curryearendyear4 = Convert.ToDecimal(currdata[1].griData[4]);

            ///Get Gross profit
            decimal grossprofitbudget = revenuebudget + centralexpensebudget + operationalbudget + currfinancialexpbudget + curryearendbudget;
            decimal grossprofityear1 = revenueyear1 + centralexpenseyear1 + operationalyear1 + currfinancialexpyear1 + curryearendyear1;
            decimal grossprofityear2 = revenueyear2 + centralexpenseyear2 + operationalyear2 + currfinancialexpyear2 + curryearendyear2;
            decimal grossprofityear3 = revenueyear3 + centralexpenseyear3 + operationalyear3 + currfinancialexpyear3 + curryearendyear3;
            decimal grossprofityear4 = revenueyear4 + centralexpenseyear4 + operationalyear4 + currfinancialexpyear4 + curryearendyear4;

            dynamic jsonData = new JArray();

            dynamic row = new JObject();
            row.id = null;
            row.name = ((langStringValues.FirstOrDefault(v => v.Key == "Gross_profit_KOSTRA")).Value).LangText;
            dynamic gridData = new JArray();
            gridData.Add(grossprofitbudget);
            gridData.Add(grossprofityear1);
            gridData.Add(grossprofityear2);
            gridData.Add(grossprofityear3);
            gridData.Add(grossprofityear4);
            row.Add("griData", gridData);
            jsonData.Add(row);

            return jsonData;
        }

    
        private async Task<dynamic> GetFinancialexpencesinserviceareasAsync(string userID, int BudgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ConsequenceAdjustedBudget");
            int budgetYear = BudgetYear;
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            List<int> GrpId = new List<int>() { 8, 9 };
            var CurrentBudgetdata = await (from a in consequenceAdjustedBudgetDbContext.tbu_trans_detail
                                     join b in consequenceAdjustedBudgetDbContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code }
                                                                                           equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                                     join c in consequenceAdjustedBudgetDbContext.gmd_reporting_line on b.fk_kostra_account_code equals c.fk_kostra_account_code
                                     where (a.fk_tenant_id == userDetails.tenant_id
                                         && a.budget_year == (budgetYear - 1)
                                              && GrpId.Contains(c.line_group_id) && b.isActive == true && c.report == "Drift")
                                     group a by new { c.line_group, c.line_group_id } into g
                                     select new
                                     {
                                         g.Key.line_group_id,
                                         Year1 = g.Sum(x => x.amount_year_1),
                                         Year2 = g.Sum(x => x.amount_year_2),
                                         Year3 = g.Sum(x => x.amount_year_3),
                                         Year4 = g.Sum(x => x.amount_year_4)
                                     }).ToListAsync();
            var FinancialexpencesData = await (from d in consequenceAdjustedBudgetDbContext.tfp_trans_header
                                         join a in consequenceAdjustedBudgetDbContext.tfp_trans_detail on new { a = d.fk_tenant_id, b = d.pk_action_id }
                                                                                                   equals new { a = a.fk_tenant_id, b = a.fk_action_id }
                                         join b in consequenceAdjustedBudgetDbContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code }
                                                                                               equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                                         join c in consequenceAdjustedBudgetDbContext.gmd_reporting_line on b.fk_kostra_account_code equals c.fk_kostra_account_code
                                         where (a.fk_tenant_id == userDetails.tenant_id
                                             && a.budget_year == budgetYear
                                                 && GrpId.Contains(c.line_group_id) && b.isActive == true && c.report == "Drift" && d.action_type >= 5)
                                         group a by new { c.line_group, c.line_group_id } into g
                                         select new
                                         {
                                             g.Key.line_group_id,
                                             Year1 = g.Sum(x => x.year_1_amount) / 1000,
                                             Year2 = g.Sum(x => x.year_2_amount) / 1000,
                                             Year3 = g.Sum(x => x.year_3_amount) / 1000,
                                             Year4 = g.Sum(x => x.year_4_amount) / 1000
                                         }).ToListAsync();
            dynamic jsonData = new JArray();

            dynamic row = new JObject();
            row.id = null;
            row.name = ((langStringValues.FirstOrDefault(v => v.Key == "Financial_expences_in_service_areas")).Value).LangText;
            dynamic gridData = new JArray();
            gridData.Add((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 6) == null ? 0 : Math.Round((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 6).Year1) / 1000)));
            if (FinancialexpencesData.Count > 0)
            {
                gridData.Add(FinancialexpencesData[0].Year1);
                gridData.Add(FinancialexpencesData[0].Year2);
                gridData.Add(FinancialexpencesData[0].Year3);
                gridData.Add(FinancialexpencesData[0].Year4);
            }
            else
            {
                gridData.Add(0);
                gridData.Add(0);
                gridData.Add(0);
                gridData.Add(0);
            }
            row.Add("griData", gridData);
            jsonData.Add(row);
            return jsonData;
        }

        private async Task<dynamic> GetTotalfinancialexpensesAsync(string userID, IEnumerable<clsAggregates> lstAggregates, int BudgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ConsequenceAdjustedBudget");
            //Get Financial expenses totals
            decimal Financialexpensesbudget = lstAggregates.Count() > 0 ? lstAggregates.LastOrDefault().budget.Value / 1000 : 0;
            decimal Financialexpensesyear1 = lstAggregates.Count() > 0 ? lstAggregates.LastOrDefault().year1Amount.Value / 1000 : 0;
            decimal Financialexpensesyear2 = lstAggregates.Count() > 0 ? lstAggregates.LastOrDefault().year2Amount.Value / 1000 : 0;
            decimal Financialexpensesyear3 = lstAggregates.Count() > 0 ? lstAggregates.LastOrDefault().year3Amount.Value / 1000 : 0;
            decimal Financialexpensesyear4 = lstAggregates.Count() > 0 ? lstAggregates.LastOrDefault().year4Amount.Value / 1000 : 0;

            //GetFinancialexpencesinserviceareas totals
            dynamic Financialexpencesinserviceareasdata = await GetFinancialexpencesinserviceareasAsync(userID, BudgetYear);
            decimal Financialexpencesinserviceareasbudget = Convert.ToDecimal(Financialexpencesinserviceareasdata[0].griData[0]);
            decimal Financialexpencesinserviceareasyear1 = Convert.ToDecimal(Financialexpencesinserviceareasdata[0].griData[1]);

            //Total financial expenses =Financial expenses + Financial expenses in service areas
            decimal Totalfinancialexpensesbudget = Financialexpensesbudget + Financialexpencesinserviceareasbudget;
            decimal Totalfinancialexpensesyear1 = Financialexpensesyear1 + Financialexpencesinserviceareasyear1;
            decimal Totalfinancialexpensesyear2 = Financialexpensesyear2 + Financialexpencesinserviceareasyear1;
            decimal Totalfinancialexpensesyear3 = Financialexpensesyear3 + Financialexpencesinserviceareasyear1;
            decimal Totalfinancialexpensesyear4 = Financialexpensesyear4 + Financialexpencesinserviceareasyear1;

            dynamic jsonData = new JArray();

            dynamic row = new JObject();
            row.id = null;
            row.name = ((langStringValues.FirstOrDefault(v => v.Key == "Total_financial_expenses")).Value).LangText;
            dynamic gridData = new JArray();
            gridData.Add(Totalfinancialexpensesbudget);
            gridData.Add(Totalfinancialexpensesyear1);
            gridData.Add(Totalfinancialexpensesyear2);
            gridData.Add(Totalfinancialexpensesyear3);
            gridData.Add(Totalfinancialexpensesyear4);
            row.Add("griData", gridData);
            jsonData.Add(row);
            return jsonData;
        }

        private async Task<dynamic> GetDepreciationincomeAsync(string userID, int BudgetYear)
        {
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            int? budgetYear = BudgetYear;
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            dynamic DepreciationData = await (from td in consequenceAdjustedBudgetDbContext.tfp_trans_detail
                                        join th in consequenceAdjustedBudgetDbContext.tfp_trans_header on new { a = td.fk_tenant_id, b = td.fk_action_id }
                                                                                                   equals new { a = th.fk_tenant_id, b = th.pk_action_id }
                                        where (td.fk_tenant_id == userDetails.tenant_id && th.fk_tenant_id == userDetails.tenant_id && td.budget_year == budgetYear && th.action_type == 101)
                                        group td by new { th.action_type } into g
                                        select new
                                        {
                                            budget = 0,
                                            year1 = g.Sum(x => x.year_1_amount) / 1000,
                                            year2 = g.Sum(x => x.year_2_amount) / 1000,
                                            year3 = g.Sum(x => x.year_3_amount) / 1000,
                                            year4 = g.Sum(x => x.year_4_amount) / 1000
                                        }).ToListAsync();
            return DepreciationData;
        }

        private async Task<dynamic> GetNetprofitKOSTRAtotalAsync(string userID, dynamic Totalfinancialexpenses, dynamic DepreciationData, int BudgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ConsequenceAdjustedBudget");
            ///Gross result (KOSTRA)
            dynamic Totalgrossprofit = await GetDataAsync(userID, "FinancialPlan", null, false, false, false, false, BudgetYear);
            decimal Totalgrossprofitbudget = Convert.ToDecimal(Totalgrossprofit.rows.Last.gridData[0][0]);
            decimal Totalgrossprofityear1 = Convert.ToDecimal(Totalgrossprofit.rows.Last.gridData[0][1]);
            decimal Totalgrossprofityear2 = Convert.ToDecimal(Totalgrossprofit.rows.Last.gridData[0][2]);
            decimal Totalgrossprofityear3 = Convert.ToDecimal(Totalgrossprofit.rows.Last.gridData[0][3]);
            decimal Totalgrossprofityear4 = Convert.ToDecimal(Totalgrossprofit.rows.Last.gridData[0][4]);

            //Total financial expenses
            decimal Totalfinancialexpensesbudget = Convert.ToDecimal(Totalfinancialexpenses[0].griData[0]);
            decimal Totalfinancialexpensesyear1 = Convert.ToDecimal(Totalfinancialexpenses[0].griData[1]);
            decimal Totalfinancialexpensesyear2 = Convert.ToDecimal(Totalfinancialexpenses[0].griData[2]);
            decimal Totalfinancialexpensesyear3 = Convert.ToDecimal(Totalfinancialexpenses[0].griData[3]);
            decimal Totalfinancialexpensesyear4 = Convert.ToDecimal(Totalfinancialexpenses[0].griData[4]);

            //Depreciation income
            decimal TotalDepreciationbudget = DepreciationData.Count == 0 ? 0 : Convert.ToDecimal(DepreciationData[0].griData[0]);
            decimal TotalDepreciationyear1 = DepreciationData.Count == 0 ? 0 : Convert.ToDecimal(DepreciationData[0].griData[1]);
            decimal TotalDepreciationyear2 = DepreciationData.Count == 0 ? 0 : Convert.ToDecimal(DepreciationData[0].griData[2]);
            decimal TotalDepreciationyear3 = DepreciationData.Count == 0 ? 0 : Convert.ToDecimal(DepreciationData[0].griData[3]);
            decimal TotalDepreciationyear4 = DepreciationData.Count == 0 ? 0 : Convert.ToDecimal(DepreciationData[0].griData[4]);

            //Net profit (KOSTRA) = Gross result (KOSTRA) + Total financial expenses + Depreciation income
            decimal Totalnetprofitbudget = Totalgrossprofitbudget + Totalfinancialexpensesbudget + TotalDepreciationbudget;
            decimal Totalnetprofityear1 = Totalgrossprofityear1 + Totalfinancialexpensesyear1 + TotalDepreciationyear1;
            decimal Totalnetprofityear2 = Totalgrossprofityear2 + Totalfinancialexpensesyear2 + TotalDepreciationyear2;
            decimal Totalnetprofityear3 = Totalgrossprofityear3 + Totalfinancialexpensesyear3 + TotalDepreciationyear3;
            decimal Totalnetprofityear4 = Totalgrossprofityear4 + Totalfinancialexpensesyear4 + TotalDepreciationyear4;

            dynamic jsonData = new JArray();

            dynamic row = new JObject();
            row.id = null;
            row.name = ((langStringValues.FirstOrDefault(v => v.Key == "Net_profit_KOSTRA")).Value).LangText;
            dynamic gridData = new JArray();
            gridData.Add(Totalnetprofitbudget);
            gridData.Add(Totalnetprofityear1);
            gridData.Add(Totalnetprofityear2);
            gridData.Add(Totalnetprofityear3);
            gridData.Add(Totalnetprofityear4);
            row.Add("griData", gridData);
            jsonData.Add(row);

            return jsonData;
        }

        private async Task<dynamic> GetNetyearenditemsinserviceareasAsync(string userID, int BudgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ConsequenceAdjustedBudget");
            int budgetYear = BudgetYear;
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            List<int> GrpId = new List<int>() { 11, 12 };
            var CurrentBudgetdata = await (from a in consequenceAdjustedBudgetDbContext.tbu_trans_detail
                                     join b in consequenceAdjustedBudgetDbContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code }
                                                                                           equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                                     join c in consequenceAdjustedBudgetDbContext.gmd_reporting_line on b.fk_kostra_account_code equals c.fk_kostra_account_code
                                     where (a.fk_tenant_id == userDetails.tenant_id
                                         && a.budget_year == (budgetYear - 1)
                                              && GrpId.Contains(c.line_group_id) && b.isActive == true && c.report == "Drift")
                                     group a by new { c.line_group, c.line_group_id } into g
                                     select new
                                     {
                                         g.Key.line_group_id,
                                         Year1 = g.Sum(x => x.amount_year_1),
                                         Year2 = g.Sum(x => x.amount_year_2),
                                         Year3 = g.Sum(x => x.amount_year_3),
                                         Year4 = g.Sum(x => x.amount_year_4)
                                     }).ToListAsync();
            var NetyearenditemsData = await (from d in consequenceAdjustedBudgetDbContext.tfp_trans_header
                                       join a in consequenceAdjustedBudgetDbContext.tfp_trans_detail on new { a = d.fk_tenant_id, b = d.pk_action_id }
                                                                                                 equals new { a = a.fk_tenant_id, b = a.fk_action_id }
                                       join b in consequenceAdjustedBudgetDbContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code }
                                                                                             equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                                       join c in consequenceAdjustedBudgetDbContext.gmd_reporting_line on b.fk_kostra_account_code equals c.fk_kostra_account_code
                                       where (a.fk_tenant_id == userDetails.tenant_id
                                           && a.budget_year == budgetYear
                                               && GrpId.Contains(c.line_group_id) && b.isActive == true && c.report == "Drift" && d.action_type >= 5)
                                       group a by new { c.line_group, c.line_group_id } into g
                                       select new
                                       {
                                           g.Key.line_group_id,
                                           Year1 = g.Sum(x => x.year_1_amount) / 1000,
                                           Year2 = g.Sum(x => x.year_2_amount) / 1000,
                                           Year3 = g.Sum(x => x.year_3_amount) / 1000,
                                           Year4 = g.Sum(x => x.year_4_amount) / 1000
                                       }).ToListAsync();

            dynamic jsonData = new JArray();

            dynamic row = new JObject();
            row.id = null;
            row.name = ((langStringValues.FirstOrDefault(v => v.Key == "Year_end_items_in_servicearea")).Value).LangText;
            dynamic gridData = new JArray();
            gridData.Add((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 6) == null ? 0 : Math.Round((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 6).Year1) / 1000)));
            if (NetyearenditemsData.Count > 0)
            {
                gridData.Add(NetyearenditemsData[0].Year1);
                gridData.Add(NetyearenditemsData[0].Year2);
                gridData.Add(NetyearenditemsData[0].Year3);
                gridData.Add(NetyearenditemsData[0].Year4);
            }
            else
            {
                gridData.Add(0);
                gridData.Add(0);
                gridData.Add(0);
                gridData.Add(0);
            }
            row.Add("griData", gridData);
            jsonData.Add(row);
            return jsonData;
        }

        private async Task<dynamic> GetTotalYearendItemsAsync(string userID, IEnumerable<clsAggregates> lstAggregates, dynamic Netyearenditemsinserviceareasdata)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ConsequenceAdjustedBudget");
            //Total Year end items
            decimal yearenditemsbudget = lstAggregates.Count() > 0 ? lstAggregates.LastOrDefault().budget.Value / 1000 : 0;
            decimal yearenditemsyear1 = lstAggregates.Count() > 0 ? lstAggregates.LastOrDefault().year1Amount.Value / 1000 : 0;
            decimal yearenditemsyear2 = lstAggregates.Count() > 0 ? lstAggregates.LastOrDefault().year2Amount.Value / 1000 : 0;
            decimal yearenditemsyear3 = lstAggregates.Count() > 0 ? lstAggregates.LastOrDefault().year3Amount.Value / 1000 : 0;
            decimal yearenditemsyear4 = lstAggregates.Count() > 0 ? lstAggregates.LastOrDefault().year4Amount.Value / 1000 : 0;

            //total Net year end items in service areas
            decimal Totalnetyearendbudget = Convert.ToDecimal(Netyearenditemsinserviceareasdata[0].griData[0]);
            decimal Totalnetyearendyear1 = Convert.ToDecimal(Netyearenditemsinserviceareasdata[0].griData[1]);
            decimal Totalnetyearendyear2 = Convert.ToDecimal(Netyearenditemsinserviceareasdata[0].griData[2]);
            decimal Totalnetyearendyear3 = Convert.ToDecimal(Netyearenditemsinserviceareasdata[0].griData[3]);
            decimal Totalnetyearendyear4 = Convert.ToDecimal(Netyearenditemsinserviceareasdata[0].griData[4]);

            /// Total year end items = Year end items + Net year end items in service areas
            decimal Totalyearendbudget = yearenditemsbudget + Totalnetyearendbudget;
            decimal Totalyearendyear1 = yearenditemsyear1 + Totalnetyearendyear1;
            decimal Totalyearendyear2 = yearenditemsyear2 + Totalnetyearendyear2;
            decimal Totalyearendyear3 = yearenditemsyear3 + Totalnetyearendyear3;
            decimal Totalyearendyear4 = yearenditemsyear4 + Totalnetyearendyear4;

            dynamic jsonData = new JArray();

            dynamic row = new JObject();
            row.id = null;
            row.name = ((langStringValues.FirstOrDefault(v => v.Key == "Total_year_end_items")).Value).LangText;
            dynamic gridData = new JArray();
            gridData.Add(Totalyearendbudget);
            gridData.Add(Totalyearendyear1);
            gridData.Add(Totalyearendyear2);
            gridData.Add(Totalyearendyear3);
            gridData.Add(Totalyearendyear4);
            row.Add("griData", gridData);
            jsonData.Add(row);

            return jsonData;
        }

        private async Task<dynamic> GetyearlyprofitAsync(string userID, IEnumerable<clsAggregates> lstAggregates, bool isBudgetChangePage, int BudgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ConsequenceAdjustedBudget");

            ///Net profit (KOSTRA)
            dynamic lstnetprofit = await GetDataForFinancialPlanSectionsAsync(userID, "FinancialIncomeAndExpenses", isBudgetChangePage, BudgetYear);
            decimal lstnetprofitbudget = Convert.ToDecimal(lstnetprofit.rows.Last.gridData[0][0]);
            decimal lstnetprofityear1 = Convert.ToDecimal(lstnetprofit.rows.Last.gridData[0][1]);
            decimal lstnetprofityear2 = Convert.ToDecimal(lstnetprofit.rows.Last.gridData[0][2]);
            decimal lstnetprofityear3 = Convert.ToDecimal(lstnetprofit.rows.Last.gridData[0][3]);
            decimal lstnetprofityear4 = Convert.ToDecimal(lstnetprofit.rows.Last.gridData[0][4]);

            //Total year end items
            dynamic lsttotalyearend = await GetDataAsync(userID, "Provisions", null, false, false, false, false, BudgetYear);
            decimal lsttotalyearendbudget = Convert.ToDecimal(lsttotalyearend.rows.Last.gridData[0][0]);
            decimal lsttotalyearendyear1 = Convert.ToDecimal(lsttotalyearend.rows.Last.gridData[0][1]);
            decimal lsttotalyearendyear2 = Convert.ToDecimal(lsttotalyearend.rows.Last.gridData[0][2]);
            decimal lsttotalyearendyear3 = Convert.ToDecimal(lsttotalyearend.rows.Last.gridData[0][3]);
            decimal lsttotalyearendyear4 = Convert.ToDecimal(lsttotalyearend.rows.Last.gridData[0][4]);

            //Transferred to investment budget
            decimal transferedbudget = lstAggregates.Count() > 0 ? lstAggregates.LastOrDefault().budget.Value / 1000 : 0;
            decimal transferedyear1 = lstAggregates.Count() > 0 ? lstAggregates.LastOrDefault().year1Amount.Value / 1000 : 0;
            decimal transferedyear2 = lstAggregates.Count() > 0 ? lstAggregates.LastOrDefault().year2Amount.Value / 1000 : 0;
            decimal transferedyear3 = lstAggregates.Count() > 0 ? lstAggregates.LastOrDefault().year3Amount.Value / 1000 : 0;
            decimal transferedyear4 = lstAggregates.Count() > 0 ? lstAggregates.LastOrDefault().year4Amount.Value / 1000 : 0;

            ////Yearly profit =Net profit (KOSTRA) + Total year end items + Transferred to investment budget
            decimal yearlyprofitbudget = lstnetprofitbudget + lsttotalyearendbudget + transferedbudget;
            decimal yearlyprofityear1 = lstnetprofityear1 + lsttotalyearendyear1 + transferedyear1;
            decimal yearlyprofityear2 = lstnetprofityear2 + lsttotalyearendyear2 + transferedyear2;
            decimal yearlyprofityear3 = lstnetprofityear3 + lsttotalyearendyear3 + transferedyear3;
            decimal yearlyprofityear4 = lstnetprofityear4 + lsttotalyearendyear4 + transferedyear4;

            dynamic jsonData = new JArray();

            dynamic row = new JObject();
            row.id = null;
            row.name = ((langStringValues.FirstOrDefault(v => v.Key == "yearly_profit_text")).Value).LangText;
            dynamic gridData = new JArray();
            gridData.Add(yearlyprofitbudget);
            gridData.Add(yearlyprofityear1);
            gridData.Add(yearlyprofityear2);
            gridData.Add(yearlyprofityear3);
            gridData.Add(yearlyprofityear4);
            row.Add("griData", gridData);
            jsonData.Add(row);

            return jsonData;
        }

        public IEnumerable<dynamic> GetDepartments(string userID, string serviceAreaID, int budgetYear, string pageName = null)
        {
            return GetDepartmentsAsync(userID, serviceAreaID, budgetYear, pageName).GetAwaiter().GetResult();
        }
        public async Task<IEnumerable<dynamic>> GetDepartmentsAsync(string userID, string serviceAreaID, int budgetYear, string pageName = null)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userID, _utility.GetForecastPeriod(budgetYear, 1));
            List<clsOrgIdAndDepartments> lstOrgIdAndDepartments = new List<clsOrgIdAndDepartments>();

            if (string.IsNullOrEmpty(serviceAreaID))
            {
                lstOrgIdAndDepartments = (await _utility.GetTenantDepartmentsForSelectedOrgIdAsync(orgVersionContent, userID, null, null, false, false)).ToList();
            }
            else
            {
                lstOrgIdAndDepartments = (await _utility.GetTenantDepartmentsForSelectedOrgIdAsync(orgVersionContent, userID, serviceAreaID, null, false, false)).ToList();
            }

            List<dynamic> lstDepartments = new List<dynamic>();
            dynamic _data = new DynamicDictionary();
            Dictionary<string, clsLanguageString> langStringValuesCommon = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Common");
            if (pageName != null && pageName.ToUpper() == "budgetProposal".ToUpper())
            {
                _data.departmentText = (langStringValuesCommon["cmn_filter_all"]).LangText;
                _data.departmentValue = "00";
                lstDepartments.Add(_data);
            }

            foreach (var dept in lstOrgIdAndDepartments)
            {
                _data = new DynamicDictionary();
                _data.departmentText = dept.departmentText;
                _data.departmentValue = dept.departmentValue;
                lstDepartments.Add(_data);
            }
            return lstDepartments;
        }
        public IEnumerable<dynamic> GetDepartmentsChapteSA(string userID, string serviceAreaID, int budgetYear, string chapterId = null)
        {
            return GetDepartmentsChapteSAAsync(userID, serviceAreaID, budgetYear, chapterId).GetAwaiter().GetResult();
        }
        public async Task<IEnumerable<dynamic>> GetDepartmentsChapteSAAsync(string userID, string serviceAreaID, int budgetYear, string chapterId = null)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userID, _utility.GetForecastPeriod(budgetYear, 1));
            List<clsOrgIdAndDepartments> lstOrgIdAndDepartments = new List<clsOrgIdAndDepartments>();

            if (string.IsNullOrEmpty(serviceAreaID))
            {
                lstOrgIdAndDepartments = (await _utility.GetTenantDepartmentsForSelectedOrgIdAsync(orgVersionContent, userID, null, null, false, false)).ToList();
            }
            else
            {
                lstOrgIdAndDepartments = (await _utility.GetTenantDepartmentsForSelectedOrgIdAsync(orgVersionContent, userID, serviceAreaID, null, false, false)).ToList();
            }
            var relationValues = await GetRelationValuesByAttrTypeAsync(userDetails.tenant_id, chapterId, budgetYear);
            List<dynamic> lstDepartments = new List<dynamic>();
            dynamic _data = new DynamicDictionary();
            Dictionary<string, clsLanguageString> langStringValuesCommon = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Common");

            foreach (var dept in lstOrgIdAndDepartments)
            {
                if (chapterId != "ALL")
                {
                    foreach (var relval in relationValues)
                    {
                        int chkDeptFrom = string.Compare(dept.departmentValue, relval.relation_value_from); ;
                        int chkDeptTo = string.Compare(dept.departmentValue, relval.relation_value_to);

                        if (chkDeptFrom >= 0 && chkDeptTo <= 0)
                        {
                            _data = new DynamicDictionary();
                            _data.departmentText = dept.departmentText;
                            _data.departmentValue = dept.departmentValue;
                            lstDepartments.Add(_data);
                        }
                    }
                }
                else
                {
                    _data = new DynamicDictionary();
                    _data.departmentText = dept.departmentText;
                    _data.departmentValue = dept.departmentValue;
                    lstDepartments.Add(_data);
                }
            }
            return lstDepartments;
        }

        private async Task<List<tco_relation_values>> GetRelationValuesByAttrTypeAsync(int tenantId, string attributeVal, int budgetYear)
        {
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            if (attributeVal == "ALL")
            {
                return await consequenceAdjustedBudgetDbContext.tco_relation_values.Where(x => x.fk_tenant_id == tenantId && x.status == 1 && !string.IsNullOrEmpty(x.attribute_type) && x.attribute_type.ToUpper() == "CHAPTER").ToListAsync();
            }
            else
            {
                return await consequenceAdjustedBudgetDbContext.tco_relation_values.Where(x => x.fk_tenant_id == tenantId && x.status == 1 && !string.IsNullOrEmpty(x.attribute_type) && x.attribute_type.ToUpper() == "CHAPTER" && x.attribute_value == attributeVal).ToListAsync();
            }
        }

        public IEnumerable<dynamic> GetAccounts(string userID, int actionType, string serviceArea, string orgId = null,
            string serviceId = null, bool getOnlyActiveAccounts = true, bool getAllAccounts = false,
            string accountType = null, string assessmentActionType = null, int? actionId = null, int? budgetYear = null)
        {
            var result = GetAccountsAsync(userID, actionType, serviceArea, orgId, serviceId, getOnlyActiveAccounts,
                getAllAccounts, accountType, assessmentActionType, actionId, budgetYear).GetAwaiter().GetResult();
            return result;
        }

        public async Task<IEnumerable<dynamic>> GetAccountsAsync(string userID, int actionType, string serviceArea, string orgId = null, string serviceId = null, bool getOnlyActiveAccounts = true, bool getAllAccounts = false, string accountType = null, string assessmentActionType = null, int? actionId = null, int? budgetYear = null)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            List<dynamic> lstaccounts = new List<dynamic>();
            var accounts = (from v in lstaccounts
                            select new
                            {
                                accountText = string.Empty,
                                accountValue = string.Empty,
                                isActive = false,
                                dateFrom = new DateTime(),
                                dateTo = new DateTime()
                            }).Distinct().ToList();

            tfp_trans_header th = null;

            if (actionId != null)
            {
                th = await consequenceAdjustedBudgetDbContext.tfp_trans_header.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_action_id == actionId.Value);
            }
            if (th != null && th.line_order == -1)
            {
                accounts = await (from ac in consequenceAdjustedBudgetDbContext.tco_accounts
                                  where ac.pk_tenant_id == userDetails.tenant_id
                                  && ac.isActive
                                  && budgetYear.Value >= ac.dateFrom.Year && budgetYear.Value <= ac.dateTo.Year
                                  && ac.fk_kostra_account_code == "1990"
                                  orderby ac.pk_account_code
                                  select new
                                  {
                                      accountText = ac.display_name,
                                      accountValue = ac.pk_account_code,
                                      isActive = ac.isActive,
                                      dateFrom = ac.dateFrom,
                                      dateTo = ac.dateTo
                                  }).Distinct().ToListAsync();

                foreach (var ac in accounts)
                {
                    dynamic _data = new DynamicDictionary();
                    _data.accountText = ac.accountText;
                    _data.accountValue = ac.accountValue;
                    lstaccounts.Add(_data);
                }
                return lstaccounts;
            }

            if (!string.IsNullOrEmpty(accountType))
            {
                if (accountType.ToLower() == "income")
                {
                    accounts = string.IsNullOrEmpty(assessmentActionType) ? await (from ac in consequenceAdjustedBudgetDbContext.tco_accounts
                                                                                   join ka in consequenceAdjustedBudgetDbContext.gco_kostra_accounts on ac.fk_kostra_account_code equals ka.pk_kostra_account_code
                                                                                   where (ac.pk_tenant_id == userDetails.tenant_id) && (ka.income_flag == 1)
                                                                                   orderby ac.pk_account_code
                                                                                   select new
                                                                                   {
                                                                                       accountText = ac.display_name,
                                                                                       accountValue = ac.pk_account_code,
                                                                                       isActive = ac.isActive,
                                                                                       dateFrom = ac.dateFrom,
                                                                                       dateTo = ac.dateTo
                                                                                   }).Distinct().ToListAsync()
                                :
                                await (from ac in consequenceAdjustedBudgetDbContext.tco_accounts
                                       join ka in consequenceAdjustedBudgetDbContext.gco_kostra_accounts on ac.fk_kostra_account_code equals ka.pk_kostra_account_code
                                       where (ac.pk_tenant_id == userDetails.tenant_id) && (ka.income_flag == 1) && ka.type == assessmentActionType
                                       orderby ac.pk_account_code
                                       select new
                                       {
                                           accountText = ac.display_name,
                                           accountValue = ac.pk_account_code,
                                           isActive = ac.isActive,
                                           dateFrom = ac.dateFrom,
                                           dateTo = ac.dateTo
                                       }).Distinct().ToListAsync();
                }
                else
                {
                    accounts = string.IsNullOrEmpty(assessmentActionType) ? await (from ac in consequenceAdjustedBudgetDbContext.tco_accounts
                                                                                   join ka in consequenceAdjustedBudgetDbContext.gco_kostra_accounts on ac.fk_kostra_account_code equals ka.pk_kostra_account_code
                                                                                   where (ac.pk_tenant_id == userDetails.tenant_id) && (ka.income_flag == 0)
                                                                                   orderby ac.pk_account_code
                                                                                   select new
                                                                                   {
                                                                                       accountText = ac.display_name,
                                                                                       accountValue = ac.pk_account_code,
                                                                                       isActive = ac.isActive,
                                                                                       dateFrom = ac.dateFrom,
                                                                                       dateTo = ac.dateTo
                                                                                   }).Distinct().ToListAsync()
                                :
                                 await (from ac in consequenceAdjustedBudgetDbContext.tco_accounts
                                        join ka in consequenceAdjustedBudgetDbContext.gco_kostra_accounts on ac.fk_kostra_account_code equals ka.pk_kostra_account_code
                                        where (ac.pk_tenant_id == userDetails.tenant_id) && (ka.income_flag == 0) && ka.type == assessmentActionType
                                        orderby ac.pk_account_code
                                        select new
                                        {
                                            accountText = ac.display_name,
                                            accountValue = ac.pk_account_code,
                                            isActive = ac.isActive,
                                            dateFrom = ac.dateFrom,
                                            dateTo = ac.dateTo
                                        }).Distinct().ToListAsync();
                }
            }
            else if (getAllAccounts)
            {
                accounts = await (from ac in consequenceAdjustedBudgetDbContext.tco_accounts
                                  join ka in consequenceAdjustedBudgetDbContext.gco_kostra_accounts on ac.fk_kostra_account_code equals ka.pk_kostra_account_code
                                  where (ac.pk_tenant_id == userDetails.tenant_id) && ka.type == "operations"
                                  orderby ac.pk_account_code
                                  select new
                                  {
                                      accountText = ac.display_name,
                                      accountValue = ac.pk_account_code,
                                      isActive = ac.isActive,
                                      dateFrom = ac.dateFrom,
                                      dateTo = ac.dateTo
                                  }).Distinct().ToListAsync();

                if (getOnlyActiveAccounts == false)
                {
                    //do nothing
                }
                else
                {
                    if (budgetYear != null)
                    {
                        accounts = accounts.Where(x => x.isActive && budgetYear.Value >= x.dateFrom.Year && budgetYear <= x.dateTo.Year).ToList();
                    }
                    else
                    {
                        accounts = accounts.Where(x => x.isActive).ToList();
                    }
                }
            }
            else if (!string.IsNullOrEmpty(orgId))
            {
                accounts = await (from rl in consequenceAdjustedBudgetDbContext.gmd_reporting_line
                                  join ac in consequenceAdjustedBudgetDbContext.tco_accounts on rl.fk_kostra_account_code equals ac.fk_kostra_account_code
                                  where (ac.pk_tenant_id == userDetails.tenant_id
                                         && rl.report.ToLower() == "FelthjDrift" || (rl.line_group_id == 60 && ac.pk_tenant_id == userDetails.tenant_id))
                                  orderby ac.pk_account_code
                                  select new
                                  {
                                      accountText = ac.display_name,
                                      accountValue = ac.pk_account_code,
                                      isActive = ac.isActive,
                                      dateFrom = ac.dateFrom,
                                      dateTo = ac.dateTo
                                  }).Distinct().ToListAsync();

                if (getOnlyActiveAccounts == false)
                {
                    //do nothing
                }
                else
                {
                    if (budgetYear != null)
                    {
                        accounts = accounts.Where(x => x.isActive && budgetYear.Value >= x.dateFrom.Year && budgetYear <= x.dateTo.Year).ToList();
                    }
                    else
                    {
                        accounts = accounts.Where(x => x.isActive).ToList();
                    }
                }
            }
            else if (string.IsNullOrEmpty(serviceArea))
            {
                accounts = await (from rl in consequenceAdjustedBudgetDbContext.gmd_reporting_line
                                  join ac in consequenceAdjustedBudgetDbContext.tco_accounts on rl.fk_kostra_account_code equals ac.fk_kostra_account_code
                                  where (ac.pk_tenant_id == userDetails.tenant_id && rl.line_group_id == actionType
                                          || (rl.line_group_id == 60 && ac.pk_tenant_id == userDetails.tenant_id))
                                  orderby ac.pk_account_code
                                  select new
                                  {
                                      accountText = ac.display_name,
                                      accountValue = ac.pk_account_code,
                                      isActive = ac.isActive,
                                      dateFrom = ac.dateFrom,
                                      dateTo = ac.dateTo
                                  }).Distinct().ToListAsync();

                if (getOnlyActiveAccounts == false)
                {
                    //do nothing
                }
                else
                {
                    if (budgetYear != null)
                    {
                        accounts = accounts.Where(x => x.isActive && budgetYear.Value >= x.dateFrom.Year && budgetYear <= x.dateTo.Year).ToList();
                    }
                    else
                    {
                        accounts = accounts.Where(x => x.isActive).ToList();
                    }
                }
            }
            else
            {
                accounts = await (from rl in consequenceAdjustedBudgetDbContext.gmd_reporting_line
                                  join ac in consequenceAdjustedBudgetDbContext.tco_accounts on rl.fk_kostra_account_code equals ac.fk_kostra_account_code
                                  where (ac.pk_tenant_id == userDetails.tenant_id
                                         && rl.report.ToLower() == "FelthjDrift" || (rl.line_group_id == 60 && ac.pk_tenant_id == userDetails.tenant_id))
                                  orderby ac.pk_account_code
                                  select new
                                  {
                                      accountText = ac.display_name,
                                      accountValue = ac.pk_account_code,
                                      isActive = ac.isActive,
                                      dateFrom = ac.dateFrom,
                                      dateTo = ac.dateTo
                                  }).Distinct().ToListAsync();

                if (getOnlyActiveAccounts == false)
                {
                    //do nothing
                }
                else
                {
                    if (budgetYear != null)
                    {
                        accounts = accounts.Where(x => x.isActive && budgetYear.Value >= x.dateFrom.Year && budgetYear <= x.dateTo.Year).ToList();
                    }
                    else
                    {
                        accounts = accounts.Where(x => x.isActive).ToList();
                    }
                }
            }
            foreach (var ac in accounts)
            {
                dynamic _data = new DynamicDictionary();
                _data.accountText = ac.accountText;
                _data.accountValue = ac.accountValue;
                lstaccounts.Add(_data);
            }
            return lstaccounts.OrderBy(o => o.accountValue).ToList();
        }

        public IEnumerable<dynamic> GetFunctions(string userID, string departmentCode,
            bool getonlyActiveFunctions = true, int? budgetYear = null)
        {
            var results = GetFunctionsAsync(userID, departmentCode, getonlyActiveFunctions, budgetYear).GetAwaiter()
                .GetResult();
            return results;
        }

        public async Task<IEnumerable<dynamic>> GetFunctionsAsync(string userID, string departmentCode, bool getonlyActiveFunctions = true, int? budgetYear = null)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            var functions = await (from a in consequenceAdjustedBudgetDbContext.vwUserDetails
                                   where a.pk_id == -1 && a.tenant_id==userDetails.tenant_id && a.client_id==userDetails.client_id
                                   select new
                                   {
                                       functionText = string.Empty,
                                       functionValue = string.Empty,
                                       isActive = false,
                                       dateFrom = new DateTime(),
                                       dateTo = new DateTime()
                                   }).ToListAsync();
            var relcheck = await consequenceAdjustedBudgetDbContext.tco_relation_definition.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.status == 1 && x.attribute_type == "DEPARTMENT" && x.relation_type == "FUNCTION").ToListAsync();
            if (string.IsNullOrEmpty(departmentCode) || relcheck.Count == 0)
            {
                functions = await (from tf in consequenceAdjustedBudgetDbContext.tco_functions
                                   where (tf.pk_tenant_id == userDetails.tenant_id)
                                   select new
                                   {
                                       functionText = tf.display_name,
                                       functionValue = tf.pk_Function_code,
                                       isActive = tf.isActive,
                                       dateFrom = tf.dateFrom,
                                       dateTo = tf.dateTo,
                                   }).Distinct().ToListAsync();

                if (getonlyActiveFunctions == false)
                {
                    //do nothing
                }
                else
                {
                    if (budgetYear != null)
                    {
                        functions = functions.Where(x => x.isActive == true && budgetYear >= x.dateFrom.Year && budgetYear <= x.dateTo.Year).ToList();
                    }
                    else
                    {
                        functions = functions.Where(x => x.isActive == true && DateTime.UtcNow >= x.dateFrom && DateTime.UtcNow <= x.dateTo).ToList();
                    }
                }
            }
            else if (relcheck.Count > 0 && !string.IsNullOrEmpty(departmentCode))
            {
                functions = await (from tf in consequenceAdjustedBudgetDbContext.tco_functions
                                   from tr in consequenceAdjustedBudgetDbContext.tco_relation_values
                                   where tr.attribute_type == "DEPARTMENT" && tr.relation_type == "FUNCTION" && tr.status == 1 && tr.fk_tenant_id == tf.pk_tenant_id
                                    && tr.fk_tenant_id == userDetails.tenant_id && tr.attribute_value == departmentCode
                                    && (tf.pk_Function_code.CompareTo(tr.relation_value_from) >= 0) && (tf.pk_Function_code.CompareTo(tr.relation_value_to) <= 0)
                                   select new
                                   {
                                       functionText = tf.display_name,
                                       functionValue = tf.pk_Function_code,
                                       isActive = tf.isActive,
                                       dateFrom = tf.dateFrom,
                                       dateTo = tf.dateTo
                                   }).Distinct().ToListAsync();
                if (getonlyActiveFunctions == false)
                {
                    //do nothing
                }
                else
                {
                    if (budgetYear != null)
                    {
                        functions = functions.Where(x => x.isActive == true && budgetYear >= x.dateFrom.Year && budgetYear <= x.dateTo.Year).ToList();
                    }
                    else
                    {
                        functions = functions.Where(x => x.isActive == true && DateTime.UtcNow >= x.dateFrom && DateTime.UtcNow <= x.dateTo).ToList();
                    }
                }
            }
            List<dynamic> lstFunctions = new List<dynamic>();
            foreach (var function in functions)
            {
                dynamic _data = new DynamicDictionary();
                _data.functionText = function.functionText;
                _data.functionValue = function.functionValue;
                lstFunctions.Add(_data);
            }
            return lstFunctions;
        }

        private async Task<tco_functions> GetFunctionFirstOrDefault(string userID, string departmentCode, string funcCode)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            var relcheck = await consequenceAdjustedBudgetDbContext.tco_relation_definition.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.status == 1 && x.attribute_type == "DEPARTMENT" && x.relation_type == "FUNCTION").ToListAsync();
            if (string.IsNullOrEmpty(departmentCode) || relcheck.Count == 0)
            {
                return await (from tf in consequenceAdjustedBudgetDbContext.tco_functions
                              where (tf.pk_tenant_id == userDetails.tenant_id && tf.pk_Function_code == funcCode)
                              select tf).FirstOrDefaultAsync();
            }
            else if (relcheck.Count > 0 && !string.IsNullOrEmpty(departmentCode))
            {
                return await (from tf in consequenceAdjustedBudgetDbContext.tco_functions
                              from tr in consequenceAdjustedBudgetDbContext.tco_relation_values
                              where tr.attribute_type == "DEPARTMENT" && tr.relation_type == "FUNCTION" && tr.status == 1 && tr.fk_tenant_id == tf.pk_tenant_id
                              && tr.fk_tenant_id == userDetails.tenant_id && tr.attribute_value == departmentCode
                              && (tf.pk_Function_code.CompareTo(tr.relation_value_from) >= 0) && (tf.pk_Function_code.CompareTo(tr.relation_value_to) <= 0)
                              select tf).FirstOrDefaultAsync();
            }
            else
            {
                return null;
            }
        }

        public dynamic GetDefaults(int actionType, string userID, string serviceAreaID, int BudgetYear, string page)
        {
            var result = GetDefaultsAsync(actionType, userID, serviceAreaID, BudgetYear, page).GetAwaiter().GetResult();
            return result;
        }

        public async Task<dynamic> GetDefaultsAsync(int actionType, string userID, string serviceAreaID, int BudgetYear, string page)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            int budgetYear = BudgetYear;
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userID, _utility.GetForecastPeriod(budgetYear, 1));
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Common");
            Dictionary<string, clsLanguageString> langStringValuesCAB = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ConsequenceAdjustedBudget");
            Dictionary<string, string> defaults = new Dictionary<string, string>();
            defaults.Add("accountText", ((langStringValues.FirstOrDefault(v => v.Key == "select_account_text")).Value).LangText);
            defaults.Add("accountValue", "");
            defaults.Add("departmentText", ((langStringValues.FirstOrDefault(v => v.Key == "select_department_text")).Value).LangText);
            defaults.Add("departmentValue", "");
            defaults.Add("functionText", ((langStringValues.FirstOrDefault(v => v.Key == "select_function_text")).Value).LangText);
            defaults.Add("functionValue", "");

            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();

            string default_serviceArea = await _utility.GetParameterValueAsync(userID, "FP_SERVICE_AREA_COMMON");

            /* Account */
            string acc_defaults_1 = await (from a in consequenceAdjustedBudgetDbContext.tmd_acc_defaults
                                           where a.fk_tenant_id == userDetails.tenant_id && a.module == "FP" && a.acc_type == "ACCOUNT" && a.link_type == "ACTIONTYPE" && a.link_value == actionType.ToString()
                                           && a.fk_org_version == orgVersionContent.orgVersion
                                           select a.acc_value).FirstOrDefaultAsync();

            if (string.IsNullOrEmpty(acc_defaults_1))
            {
                string acc_defaults_2 = string.Empty;

                if (string.IsNullOrEmpty(serviceAreaID))
                {
                    acc_defaults_2 = await (from a in consequenceAdjustedBudgetDbContext.tmd_acc_defaults
                                            where a.fk_tenant_id == userDetails.tenant_id && a.module == "FP" && a.acc_type == "ACCOUNT" && a.link_type == "SERVICEAREA" && a.link_value == default_serviceArea
                                            && a.fk_org_version == orgVersionContent.orgVersion
                                            select a.acc_value).FirstOrDefaultAsync();
                }
                else
                {
                    acc_defaults_2 = await (from a in consequenceAdjustedBudgetDbContext.tmd_acc_defaults
                                            where a.fk_tenant_id == userDetails.tenant_id && a.module == "FP" && a.acc_type == "ACCOUNT" && a.link_type == "SERVICEAREA" && a.link_value == serviceAreaID
                                            && a.fk_org_version == orgVersionContent.orgVersion
                                            select a.acc_value).FirstOrDefaultAsync();
                }
                if (!string.IsNullOrEmpty(acc_defaults_2))
                {
                    if ((await GetAccountsAsync(userID, actionType, serviceAreaID, null, null, false)).FirstOrDefault(x => x.accountValue == acc_defaults_2) != null)
                    {
                        string account_text = await (from a in consequenceAdjustedBudgetDbContext.tco_accounts
                                                     where a.pk_tenant_id == userDetails.tenant_id && a.pk_account_code == acc_defaults_2
                                                     select a.display_name).FirstOrDefaultAsync();
                        defaults["accountText"] = account_text;
                        defaults["accountValue"] = acc_defaults_2;
                    }
                }
            }
            else
            {
                if ((await GetAccountsAsync(userID, actionType, serviceAreaID, null, null, false)).FirstOrDefault(x => x.accountValue == acc_defaults_1) != null)
                {
                    string account_text = await (from a in consequenceAdjustedBudgetDbContext.tco_accounts
                                                 where a.pk_tenant_id == userDetails.tenant_id && a.pk_account_code == acc_defaults_1
                                                 select a.display_name).FirstOrDefaultAsync();
                    defaults["accountText"] = account_text;
                    defaults["accountValue"] = acc_defaults_1;
                }
            }

            /* Department */
            string dept_defaults = string.Empty;
            if (string.IsNullOrEmpty(serviceAreaID))
            {
                dept_defaults = await (from a in consequenceAdjustedBudgetDbContext.tmd_acc_defaults
                                       where a.fk_tenant_id == userDetails.tenant_id && a.module == "FP" && a.acc_type == "DEPARTMENT" && a.link_type == "SERVICEAREA" && a.link_value == default_serviceArea
                                       && a.fk_org_version == orgVersionContent.orgVersion
                                       select a.acc_value).FirstOrDefaultAsync();
            }
            else
            {
                dept_defaults = await (from a in consequenceAdjustedBudgetDbContext.tmd_acc_defaults
                                       where a.fk_tenant_id == userDetails.tenant_id && a.module == "FP" && a.acc_type == "DEPARTMENT" && a.link_type == "SERVICEAREA" && a.link_value == serviceAreaID
                                       && a.fk_org_version == orgVersionContent.orgVersion
                                       select a.acc_value).FirstOrDefaultAsync();
            }
            if (!string.IsNullOrEmpty(dept_defaults))
            {
                string dept_text = string.Empty;
                if (serviceAreaID != null)
                {
                    clsOrgIdAndDepartments deptForServiceAreaId = (await _utility.GetTenantDepartmentsForSelectedOrgIdAsync(orgVersionContent, userID, serviceAreaID)).FirstOrDefault(x => x.departmentValue == dept_defaults);
                    dept_text = deptForServiceAreaId == null ? string.Empty : deptForServiceAreaId.departmentText;
                }
                else
                {
                    clsOrgIdAndDepartments deptForServiceAreaId = (await _utility.GetTenantDepartmentsForSelectedOrgIdAsync(orgVersionContent, userID, default_serviceArea)).FirstOrDefault(x => x.departmentValue == dept_defaults);
                    dept_text = deptForServiceAreaId == null ? string.Empty : deptForServiceAreaId.departmentText;
                }
                if (!string.IsNullOrEmpty(dept_text))
                {
                    defaults["departmentText"] = dept_text;
                    defaults["departmentValue"] = dept_defaults;
                }
            }

            /* Function */
            string function_defaults = string.Empty;

            if (string.IsNullOrEmpty(serviceAreaID))
            {
                function_defaults = await (from a in consequenceAdjustedBudgetDbContext.tmd_acc_defaults
                                           where a.fk_tenant_id == userDetails.tenant_id && a.module == "FP" && a.acc_type == "FUNCTION" && a.link_type == "SERVICEAREA" && a.link_value == default_serviceArea
                                           && a.fk_org_version == orgVersionContent.orgVersion
                                           select a.acc_value).FirstOrDefaultAsync();
            }
            else
            {
                function_defaults = await (from a in consequenceAdjustedBudgetDbContext.tmd_acc_defaults
                                           where a.fk_tenant_id == userDetails.tenant_id && a.module == "FP" && a.acc_type == "FUNCTION" && a.link_type == "SERVICEAREA" && a.link_value == serviceAreaID
                                           && a.fk_org_version == orgVersionContent.orgVersion
                                           select a.acc_value).FirstOrDefaultAsync();
            }

            if (!string.IsNullOrEmpty(function_defaults))
            {
                string function_text = (await GetFunctionsAsync(userID, "", false)).Where(x => x.functionValue == function_defaults).FirstOrDefault().functionText;
                defaults["functionText"] = function_text;
                defaults["functionValue"] = function_defaults;
            }

            dynamic lstAjstmt = await GetAdjustmentCodesAsync(userID, false);
            dynamic lstAlter = await GetAlterCodesAsync(userID, actionType, false, false, false, page);

            string alterKey_defaults = string.Empty;
            string alterValue_defaults = string.Empty;
            foreach (var item in lstAlter)
            {
                if (item.isDefault == true)
                {
                    alterKey_defaults = item.key;
                    alterValue_defaults = item.value;
                    break;
                }
            }

            ////projects
            defaults["projectText"] = "";
            defaults["projectValue"] = "";

            ////////fridim
            IEnumerable<freedimDefinition> FridimData = await _utility.GetFreeDimDefinitionAsync(userID);
            List<freedimDefinition> lstFreeDim = FridimData.ToList();

            dynamic jsonData = new JObject();
            jsonData.id = null;

            dynamic accountData = new JObject();
            accountData.accountText = defaults["accountValue"] + "-" + defaults["accountText"];
            accountData.accountValue = defaults["accountValue"];
            jsonData.account = accountData;

            dynamic deptData = new JObject();
            deptData.departmentText = defaults["departmentValue"] + "-" + defaults["departmentText"];
            deptData.departmentValue = defaults["departmentValue"];
            jsonData.department = deptData;

            dynamic functionData = new JObject();
            functionData.functionText = defaults["functionValue"] + "-" + defaults["functionText"];
            functionData.functionValue = defaults["functionValue"];
            jsonData.functionn = functionData;

            jsonData.project = GenerateDynamicProjectDimObject("", "");

            if (lstFreeDim.Count() == 1)
            {
                dynamic freeDim1 = new JObject();
                freeDim1.freedim_name = "";
                freeDim1.fk_freedim_code = "";
                jsonData.freeDim1 = freeDim1;
            }

            if (lstFreeDim.Count() == 2)
            {
                dynamic freeDim1 = new JObject();
                freeDim1.freedim_name = "";
                freeDim1.fk_freedim_code = "";
                jsonData.freeDim1 = freeDim1;

                dynamic freeDim2 = new JObject();
                freeDim2.freedim_name = "";
                freeDim2.fk_freedim_code = "";
                jsonData.freeDim2 = freeDim2;
            }

            if (lstFreeDim.Count() == 3)
            {
                dynamic freeDim1 = new JObject();
                freeDim1.freedim_name = "";
                freeDim1.fk_freedim_code = "";
                jsonData.freeDim1 = freeDim1;

                dynamic freeDim2 = new JObject();
                freeDim2.freedim_name = "";
                freeDim2.fk_freedim_code = "";
                jsonData.freeDim2 = freeDim2;

                dynamic freeDim3 = new JObject();
                freeDim3.freedim_name = "";
                freeDim3.fk_freedim_code = "";
                jsonData.freeDim3 = freeDim3;
            }

            if (lstFreeDim.Count() == 4)
            {
                dynamic freeDim1 = new JObject();
                freeDim1.freedim_name = "";
                freeDim1.fk_freedim_code = "";
                jsonData.freeDim1 = freeDim1;

                dynamic freeDim2 = new JObject();
                freeDim2.freedim_name = "";
                freeDim2.fk_freedim_code = "";
                jsonData.freeDim2 = freeDim2;

                dynamic freeDim3 = new JObject();
                freeDim3.freedim_name = "";
                freeDim3.fk_freedim_code = "";
                jsonData.freeDim3 = freeDim3;

                dynamic freeDim4 = new JObject();
                freeDim4.freedim_name = "";
                freeDim4.fk_freedim_code = "";
                jsonData.freeDim4 = freeDim4;
            }

            if (lstAjstmt.Count > 1)
            {
                jsonData.adjustmentCode = GenerateDynamicAdjustmentCodeObject("", "");
            }
            if (lstAlter.Count > 1)
            {
                jsonData.alterCode = GenerateDynamicAlterCodeObject(alterKey_defaults, alterValue_defaults);
            }

            jsonData.year1 = 0;
            jsonData.year2 = 0;
            jsonData.year3 = 0;
            jsonData.year4 = 0;

            dynamic columnFields = new JArray();
            columnFields.Add("id");
            columnFields.Add("account");
            columnFields.Add("department");
            columnFields.Add("functionn");
            columnFields.Add("project");

            for (int i = 1; i <= lstFreeDim.Count; i++)
            {
                columnFields.Add("freeDim" + i);
            }

            if (lstAjstmt.Count > 1)
            {
                columnFields.Add("adjustmentCode");
            }
            if (lstAlter.Count > 1)
            {
                columnFields.Add("alterCode");
            }

            columnFields.Add("year1");
            columnFields.Add("year2");
            columnFields.Add("year3");
            columnFields.Add("year4");
            jsonData.columnFields = columnFields;

            dynamic columnTitles = new JArray();
            columnTitles.Add("id");
            columnTitles.Add(((langStringValuesCAB.FirstOrDefault(v => v.Key == "account_text")).Value).LangText);
            columnTitles.Add(((langStringValuesCAB.FirstOrDefault(v => v.Key == "department_text")).Value).LangText);
            columnTitles.Add(((langStringValuesCAB.FirstOrDefault(v => v.Key == "function_text")).Value).LangText);
            columnTitles.Add(((langStringValuesCAB.FirstOrDefault(v => v.Key == "fp_project_text")).Value).LangText);
            for (int i = 1; i <= lstFreeDim.Count; i++)
            {
                string freedim = "free_dim_" + i;
                if (lstFreeDim.Any(x => x.freeDimColumn == freedim))
                    columnTitles.Add(lstFreeDim.Where(x => x.freeDimColumn == freedim).Select(x => x.freeDimHeader).First().ToString());
            }

            Dictionary<string, clsLanguageString> langStringValuesFP = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "FinancialPlan");
            if (lstAjstmt.Count > 1)
            {
                columnTitles.Add(((langStringValuesFP.FirstOrDefault(v => v.Key == "fp_adjustment_code")).Value).LangText);
            }
            if (lstAlter.Count > 1)
            {
                columnTitles.Add(((langStringValuesFP.FirstOrDefault(v => v.Key == "fp_alter_code_text")).Value).LangText);
            }

            columnTitles.Add(budgetYear.ToString());
            columnTitles.Add((budgetYear + 1).ToString());
            columnTitles.Add((budgetYear + 2).ToString());
            columnTitles.Add((budgetYear + 3).ToString());
            jsonData.columnTitles = columnTitles;

            return jsonData;
        }

        private dynamic GetDefaults(int actionType, string userID, string orgId, string serviceId, int BudgetYear)
        {
            var result = GetDefaultsAsync(actionType, userID, orgId, serviceId, BudgetYear).GetAwaiter().GetResult();
            return result;
        }

        private async Task<dynamic> GetDefaultsAsync(int actionType, string userID, string orgId, string serviceId, int BudgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            int budgetYear = BudgetYear;
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userID, _utility.GetForecastPeriod(BudgetYear, 1));
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Common");
            Dictionary<string, clsLanguageString> langStringValuesCAB = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ConsequenceAdjustedBudget");
            Dictionary<string, string> defaults = new Dictionary<string, string>();
            defaults.Add("accountText", ((langStringValues.FirstOrDefault(v => v.Key == "select_account_text")).Value).LangText);
            defaults.Add("accountValue", "");
            defaults.Add("departmentText", ((langStringValues.FirstOrDefault(v => v.Key == "select_department_text")).Value).LangText);
            defaults.Add("departmentValue", "");
            defaults.Add("functionText", ((langStringValues.FirstOrDefault(v => v.Key == "select_function_text")).Value).LangText);
            defaults.Add("functionValue", "");
            var data = await tenantDbContext.tmd_fp_level_defaults.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.fk_org_version == orgVersionContent.orgVersion && x.fp_level_1_value == orgId && x.fp_level_2_value == serviceId).FirstOrDefaultAsync();

            dynamic jsonData = new JObject();
            if (data != null)
            {
                var accountInfo = await tenantDbContext.tco_accounts.FirstOrDefaultAsync(x => x.pk_tenant_id == userDetails.tenant_id && x.pk_account_code == data.fk_account_code);
                defaults["accountText"] = accountInfo != null ? accountInfo.display_name : string.Empty;
                defaults["accountValue"] = data.fk_account_code;

                var deptInfo = await tenantDbContext.tco_departments.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_department_code == data.fk_department_code);
                defaults["departmentText"] = deptInfo != null ? deptInfo.department_name : string.Empty;
                defaults["departmentValue"] = data.fk_department_code;

                var functionText = await GetFunctionFirstOrDefault(userID, string.Empty, data.fk_function_code);
                defaults["functionText"] = functionText == null ? string.Empty : functionText.display_name;
                defaults["functionValue"] = data.fk_function_code;

                var projInfo = await tenantDbContext.tco_projects.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_project_code == data.fk_project_code);
                defaults["projectText"] = projInfo != null ? projInfo.project_name : string.Empty;
                defaults["projectValue"] = data.fk_project_code;

                jsonData.id = null;

                dynamic accountData = new JObject();
                accountData.accountText = defaults["accountText"];
                accountData.accountValue = defaults["accountValue"];
                jsonData.account = accountData;

                dynamic deptData = new JObject();
                deptData.departmentText = defaults["departmentText"];
                deptData.departmentValue = defaults["departmentValue"];
                jsonData.department = deptData;

                dynamic functionData = new JObject();
                functionData.functionText = defaults["functionText"];
                functionData.functionValue = defaults["functionValue"];
                jsonData.functionn = functionData;

                dynamic projectData = new JObject();
                projectData.project_name = defaults["projectText"];
                projectData.fk_project_code = defaults["projectValue"];
                jsonData.project = projectData;

                IEnumerable<freedimDefinition> FridimData = await _utility.GetFreeDimDefinitionAsync(userID);
                List<freedimDefinition> lstFreeDim = FridimData.ToList();

                if (lstFreeDim.Count() == 1)
                {
                    dynamic freeDim1 = new JObject();
                    freeDim1.freedim_name = "";
                    freeDim1.fk_freedim_code = data.free_dim_1;
                    jsonData.freeDim1 = freeDim1;
                }

                if (lstFreeDim.Count() == 2)
                {
                    dynamic freeDim1 = new JObject();
                    freeDim1.freedim_name = "";
                    freeDim1.fk_freedim_code = data.free_dim_1;
                    jsonData.freeDim1 = freeDim1;

                    dynamic freeDim2 = new JObject();
                    freeDim2.freedim_name = "";
                    freeDim2.fk_freedim_code = data.free_dim_2;
                    jsonData.freeDim2 = freeDim2;
                }

                if (lstFreeDim.Count() == 3)
                {
                    dynamic freeDim1 = new JObject();
                    freeDim1.freedim_name = "";
                    freeDim1.fk_freedim_code = data.free_dim_1;
                    jsonData.freeDim1 = freeDim1;

                    dynamic freeDim2 = new JObject();
                    freeDim2.freedim_name = "";
                    freeDim2.fk_freedim_code = data.free_dim_2;
                    jsonData.freeDim2 = freeDim2;

                    dynamic freeDim3 = new JObject();
                    freeDim3.freedim_name = "";
                    freeDim3.fk_freedim_code = data.free_dim_3;
                    jsonData.freeDim3 = freeDim3;
                }

                if (lstFreeDim.Count() == 4)
                {
                    dynamic freeDim1 = new JObject();
                    freeDim1.freedim_name = "";
                    freeDim1.fk_freedim_code = data.free_dim_1;
                    jsonData.freeDim1 = freeDim1;

                    dynamic freeDim2 = new JObject();
                    freeDim2.freedim_name = "";
                    freeDim2.fk_freedim_code = data.free_dim_2;
                    jsonData.freeDim2 = freeDim2;

                    dynamic freeDim3 = new JObject();
                    freeDim3.freedim_name = "";
                    freeDim3.fk_freedim_code = data.free_dim_3;
                    jsonData.freeDim3 = freeDim3;

                    dynamic freeDim4 = new JObject();
                    freeDim4.freedim_name = "";
                    freeDim4.fk_freedim_code = data.free_dim_4;
                    jsonData.freeDim4 = freeDim4;
                }
            }
            return jsonData;
        }

        public dynamic GetActionChildItemssOfActionType(int actionType, int actionId, string serviceAreaID, string userID, int BudgetYear)
        {
            return GetActionChildItemssOfActionTypeAsync(actionType, actionId, serviceAreaID, userID, BudgetYear).GetAwaiter().GetResult();
        }

        public async Task<dynamic> GetActionChildItemssOfActionTypeAsync(int actionType, int actionId, string serviceAreaID, string userID, int BudgetYear)
        {
            int budgetYear = BudgetYear;

            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ConsequenceAdjustedBudget");

            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userID, _utility.GetForecastPeriod(budgetYear, 1));

            List<clsOrgIdAndDepartments> lstServiceAreaDepts = (await _utility.GetTenantDepartmentsForSelectedOrgIdAsync(orgVersionContent, userID, serviceAreaID)).ToList();
            List<string> departmentsList = new List<string>();
            foreach (var d in lstServiceAreaDepts)
            {
                string deptCode = d.departmentValue;
                departmentsList.Add(deptCode);
            }
            List<tco_free_dim_values> lstFreeDimValues = (await _utility.GetFreeDimValuesAsync(userID)).ToList();

            List<clsActions> lst = new List<clsActions>();

            if (actionType == 4)
            {
                lst = await (from th in consequenceAdjustedBudgetDbContext.tfp_trans_header
                       join td in consequenceAdjustedBudgetDbContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                                                                                  equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                       join ta in consequenceAdjustedBudgetDbContext.tco_accounts on new { b = td.fk_account_code, c = th.fk_tenant_id }
                                                                              equals new { b = ta.pk_account_code, c = ta.pk_tenant_id }
                       join f1 in consequenceAdjustedBudgetDbContext.tco_functions on new { a = td.function_code, b = th.fk_tenant_id }
                                                                               equals new { a = f1.pk_Function_code, b = f1.pk_tenant_id }
                       join p1 in consequenceAdjustedBudgetDbContext.tco_projects on new { a = td.project_code, b = th.fk_tenant_id }
                                                                              equals new { a = p1.pk_project_code, b = p1.fk_tenant_id } into lojproj
                       from pj in lojproj.DefaultIfEmpty()
                       where (th.action_type == actionType && th.fk_tenant_id == userDetails.tenant_id && td.budget_year == budgetYear && departmentsList.Contains(td.department_code))
                       select new clsActions
                       {
                           actionID = th.pk_action_id,
                           actionDescription = th.description,
                           consequence = th.consequence,
                           pk_id = td.pk_id,
                           accountCode = ta.pk_account_code,
                           accountName = ta.display_name,
                           deptCode = td.department_code,
                           functionCode = td.function_code,
                           functionName = f1.display_name,
                           projectCode = td.project_code == null ? String.Empty : td.project_code,
                           projectName = (pj == null ? String.Empty : pj.project_name),
                           freedimCode1 = td.free_dim_1 == null ? String.Empty : td.free_dim_1,
                           freedimName1 = lstFreeDimValues.Where(x => x.free_dim_code == td.free_dim_1).Select(x => x.description).ToList().Count == 0 ? string.Empty : lstFreeDimValues.Where(x => x.free_dim_code == td.free_dim_1).Select(x => x.description).First().ToString(),

                           freedimCode2 = td.free_dim_2 == null ? String.Empty : td.free_dim_2,
                           freedimName2 = lstFreeDimValues.Where(x => x.free_dim_code == td.free_dim_1).Select(x => x.description).ToList().Count == 0 ? string.Empty : lstFreeDimValues.Where(x => x.free_dim_code == td.free_dim_1).Select(x => x.description).First().ToString(),

                           freedimCode3 = td.free_dim_3 == null ? String.Empty : td.free_dim_3,
                           freedimName3 = lstFreeDimValues.Where(x => x.free_dim_code == td.free_dim_1).Select(x => x.description).ToList().Count == 0 ? string.Empty : lstFreeDimValues.Where(x => x.free_dim_code == td.free_dim_1).Select(x => x.description).First().ToString(),

                           freedimCode4 = td.free_dim_4 == null ? String.Empty : td.free_dim_4,
                           freedimName4 = lstFreeDimValues.Where(x => x.free_dim_code == td.free_dim_1).Select(x => x.description).ToList().Count == 0 ? string.Empty : lstFreeDimValues.Where(x => x.free_dim_code == td.free_dim_1).Select(x => x.description).First().ToString(),

                           year1Ammount = td.year_1_amount,
                           year2Ammount = td.year_2_amount,
                           year3Ammount = td.year_3_amount,
                           year4Ammount = td.year_4_amount,
                           fk_area_id = th.fk_area_id == null ? 0 : th.fk_area_id.Value,
                           tag = th.tag == null ? "" : th.tag,
                           priority = th.priority == null ? 0 : th.priority.Value,
                           long_description = th.long_description == null ? "" : th.long_description,
                           financial_plan_description = th.financial_plan_description == null ? "" : th.financial_plan_description,
                           isExternalDesc = th.different_external_description_flag,
                           isFPDisplayed = th.display_financial_plan_flag
                       }).Distinct().ToListAsync();
            }
            else
            {
                lst = await (from th in consequenceAdjustedBudgetDbContext.tfp_trans_header
                       join td in consequenceAdjustedBudgetDbContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                                                                                  equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                       join ta in consequenceAdjustedBudgetDbContext.tco_accounts on new { b = td.fk_account_code, c = th.fk_tenant_id }
                                                                              equals new { b = ta.pk_account_code, c = ta.pk_tenant_id }
                       join f1 in consequenceAdjustedBudgetDbContext.tco_functions on new { a = td.function_code, b = th.fk_tenant_id }
                                                                               equals new { a = f1.pk_Function_code, b = f1.pk_tenant_id }
                       join p1 in consequenceAdjustedBudgetDbContext.tco_projects on new { a = td.project_code, b = th.fk_tenant_id }
                                                                              equals new { a = p1.pk_project_code, b = p1.fk_tenant_id } into lojproj
                       from pj in lojproj.DefaultIfEmpty()
                       where (th.pk_action_id == actionId && th.fk_tenant_id == userDetails.tenant_id && td.budget_year == budgetYear && departmentsList.Contains(td.department_code))
                       select new clsActions
                       {
                           actionID = th.pk_action_id,
                           actionDescription = th.description,
                           consequence = th.consequence,
                           pk_id = td.pk_id,
                           accountCode = ta.pk_account_code,
                           accountName = ta.display_name,
                           deptCode = td.department_code,
                           functionCode = td.function_code,
                           functionName = f1.display_name,
                           projectCode = td.project_code == null ? String.Empty : td.project_code,
                           projectName = (pj == null ? String.Empty : pj.project_name),
                           freedimCode1 = td.free_dim_1 == null ? String.Empty : td.free_dim_1,
                           freedimName1 = string.Empty,

                           freedimCode2 = td.free_dim_2 == null ? String.Empty : td.free_dim_2,
                           freedimName2 = string.Empty,

                           freedimCode3 = td.free_dim_3 == null ? String.Empty : td.free_dim_3,
                           freedimName3 = string.Empty,

                           freedimCode4 = td.free_dim_4 == null ? String.Empty : td.free_dim_4,
                           freedimName4 = string.Empty,

                           year1Ammount = td.year_1_amount,
                           year2Ammount = td.year_2_amount,
                           year3Ammount = td.year_3_amount,
                           year4Ammount = td.year_4_amount,
                           fk_area_id = th.fk_area_id == null ? 0 : th.fk_area_id.Value,
                           tag = th.tag == null ? "" : th.tag,
                           priority = th.priority == null ? 0 : th.priority.Value,
                           long_description = th.long_description == null ? "" : th.long_description,
                           financial_plan_description = th.financial_plan_description == null ? "" : th.financial_plan_description,
                           isExternalDesc = th.different_external_description_flag,
                           isFPDisplayed = th.display_financial_plan_flag
                       }).Distinct().ToListAsync();
            }
            lst.ForEach(x =>
            {
                x.freedimName1 = lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode1) == null ? string.Empty :
                                         lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode1).description;
                x.freedimName2 = lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode2) == null ? string.Empty :
                                 lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode2).description;
                x.freedimName3 = lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode3) == null ? string.Empty :
                                 lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode3).description;
                x.freedimName4 = lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode4) == null ? string.Empty :
                                 lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode4).description;
            });

            Dictionary<string, string> departments = new Dictionary<string, string>();
            if (!string.IsNullOrEmpty(serviceAreaID))
            {
                List<clsOrgIdAndDepartments> lstDepartments = (await _utility.GetTenantDepartmentsForSelectedOrgIdAsync(orgVersionContent, userID, serviceAreaID)).ToList();
                foreach (var data in lstDepartments)
                {
                    string deptID = data.departmentValue;
                    string deptName = data.departmentText;
                    if (!departments.ContainsKey(deptID))
                    {
                        departments.Add(deptID, deptName);
                    }
                }
            }
            else
            {
                List<clsOrgIdAndDepartments> lstOrgIdAndDepartments = new List<clsOrgIdAndDepartments>();

                if (string.IsNullOrEmpty(serviceAreaID))
                {
                    lstOrgIdAndDepartments = (await _utility.GetTenantDepartmentsForSelectedOrgIdAsync(orgVersionContent, userID, null)).ToList();
                }
                else
                {
                    lstOrgIdAndDepartments = (await _utility.GetTenantDepartmentsForSelectedOrgIdAsync(orgVersionContent, userID, serviceAreaID)).ToList();
                }
                foreach (var data in lstOrgIdAndDepartments)
                {
                    string deptID = data.departmentValue;
                    string deptName = data.departmentText;
                    if (!departments.ContainsKey(deptID))
                    {
                        departments.Add(deptID, deptName);
                    }
                }
            }

            List<clsActions> result = new List<clsActions>();

            result = (from a in lst
                      join b in departments on a.deptCode equals b.Key
                      select new clsActions
                      {
                          actionID = a.actionID,
                          actionDescription = a.actionDescription,
                          pk_id = a.pk_id,
                          accountCode = a.accountCode,
                          accountName = a.accountName,
                          deptCode = a.deptCode,
                          deptName = b.Value,
                          functionCode = a.functionCode,
                          functionName = a.functionName,
                          projectCode = a.projectCode,
                          projectName = a.projectName,
                          freedimCode1 = a.freedimCode1,
                          freedimName1 = a.freedimName1,
                          freedimCode2 = a.freedimCode2,
                          freedimName2 = a.freedimName2,
                          freedimCode3 = a.freedimCode3,
                          freedimName3 = a.freedimName3,
                          freedimCode4 = a.freedimCode4,
                          freedimName4 = a.freedimName4,
                          year1Ammount = a.year1Ammount,
                          year2Ammount = a.year2Ammount,
                          year3Ammount = a.year3Ammount,
                          year4Ammount = a.year4Ammount,
                          isExternalDesc = a.isExternalDesc,
                          isFPDisplayed = a.isFPDisplayed
                      }).ToList();
            IEnumerable<freedimDefinition> freeDims = await _utility.GetFreeDimDefinitionAsync(userID);
            List<freedimDefinition> freeDimColumns = freeDims.ToList();
            dynamic actions = new JObject();
            gmd_action_types actionTypeDesc = await consequenceAdjustedBudgetDbContext.gmd_action_types.FirstOrDefaultAsync(x => x.pk_language == userDetails.language_preference && x.pk_action_type == actionType);

            if (lst.Count > 0)
            {
                actions.actionId = actionId;
                actions.actionDescription = (lst.FirstOrDefault()).actionDescription;
                actions.consequence = (lst.FirstOrDefault()).consequence;
                actions.fk_area_id = (lst.FirstOrDefault()).fk_area_id;
                actions.tag = (lst.FirstOrDefault()).tag;
                actions.priority = (lst.FirstOrDefault()).priority;
                actions.long_description = (lst.FirstOrDefault()).long_description;
                actions.financial_plan_description = lst.FirstOrDefault().financial_plan_description;
                actions.groupId = actionType.ToString();
                actions.groupName = actionTypeDesc == null ? "" : actionTypeDesc.action_type_descr;
                actions.isFPDisplayed = (lst.FirstOrDefault()).isFPDisplayed;
                dynamic gridData = new JArray();
                foreach (var a in result)
                {
                    dynamic ActionDetails = new JObject();
                    ActionDetails.id = a.pk_id;

                    ActionDetails.account = GenerateDynamicAccountObject(a.accountCode, a.accountName);

                    ActionDetails.department = GenerateDynamicDepartmentObject(a.deptCode, a.deptName);

                    ActionDetails.functionn = GenerateDynamicFunctionObject(a.functionCode, a.functionName);

                    ActionDetails.project = GenerateDynamicProjectDimObject(a.projectCode, a.projectName);

                    if (freeDimColumns.Count() > 0)
                    {
                        ActionDetails.freeDim1 = GenerateDynamicFreeDimObject(a.freedimCode1, a.freedimName1);
                    }
                    if (freeDimColumns.Count() > 1)
                    {
                        ActionDetails.freeDim2 = GenerateDynamicFreeDimObject(a.freedimCode2, a.freedimName2);
                    }
                    if (freeDimColumns.Count() > 2)
                    {
                        ActionDetails.freeDim3 = GenerateDynamicFreeDimObject(a.freedimCode3, a.freedimName3);
                    }
                    if (freeDimColumns.Count() > 3)
                    {
                        ActionDetails.freeDim4 = GenerateDynamicFreeDimObject(a.freedimCode4, a.freedimName4);
                    }
                    ActionDetails.year1 = a.year1Ammount / 1000;
                    ActionDetails.year2 = a.year2Ammount / 1000;
                    ActionDetails.year3 = a.year3Ammount / 1000;
                    ActionDetails.year4 = a.year4Ammount / 1000;
                    ActionDetails.isExternalDesc = a.isExternalDesc;
                    gridData.Add(ActionDetails);
                }
                actions.Add("gridData", gridData);
            }

            dynamic columnFields = new JArray();
            columnFields.Add("id");
            columnFields.Add("account");
            columnFields.Add("department");
            columnFields.Add("functionn");
            columnFields.Add("project");
            //for (int i = 1; i <= freeDimColumns.Count; i++)
            //{
            //    columnFields.Add("freeDim" + i);
            //}
            foreach (var item in freeDimColumns)
            {
                columnFields.Add("freeDim" + item.freeDimColumn.ToString().Substring(item.freeDimColumn.ToString().Length - 1, 1));
            }

            columnFields.Add("year1");
            columnFields.Add("year2");
            columnFields.Add("year3");
            columnFields.Add("year4");

            actions.Add("columnFields", columnFields);

            dynamic columnTitles = new JArray();
            columnTitles.Add("id");
            columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "account_text")).Value).LangText);
            columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "department_text")).Value).LangText);
            columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "function_text")).Value).LangText);
            columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "fp_project_text")).Value).LangText);
            //for (int i = 1; i <= freeDimColumns.Count; i++)
            //{
            //    string freedim = "free_dim_" + i;
            //    columnTitles.Add(freeDimColumns.Where(x => x.freeDimColumn == freedim).Select(x => x.freeDimHeader).First().ToString());
            //}
            foreach (var item in freeDimColumns)
            {
                columnTitles.Add(item.freeDimHeader);
            }
            columnTitles.Add(budgetYear.ToString());
            columnTitles.Add((budgetYear + 1).ToString());
            columnTitles.Add((budgetYear + 2).ToString());
            columnTitles.Add((budgetYear + 3).ToString());

            actions.Add("columnTitles", columnTitles);

            actions.amountFormat = "#,##";
            return actions;
        }

        private dynamic GenerateDynamicAccountObject(string accCode, string accValue)
        {
            dynamic accDetails = new JObject();
            if (accCode == "" && accValue == "")
            {
                accDetails.accountText = "";
            }
            else
            {
                accDetails.accountText = accCode + "-" + accValue;
            }

            if (accCode == "0")
            {
                accDetails.accountValue = "";
            }
            else
            {
                accDetails.accountValue = accCode;
            }
            return accDetails;
        }

        private dynamic GenerateDynamicDepartmentObject(string deptCode, string deptValue)
        {
            dynamic deptDetails = new JObject();
            if (deptCode == "" && deptValue == "")
            {
                deptDetails.departmentText = "";
            }
            else
            {
                deptDetails.departmentText = deptCode + "-" + deptValue;
            }

            if (deptCode == "0")
            {
                deptDetails.departmentValue = "";
            }
            else
            {
                deptDetails.departmentValue = deptCode;
            }
            return deptDetails;
        }

        private dynamic GenerateDynamicFunctionObject(string funcCode, string funcValue)
        {
            dynamic funcDetails = new JObject();
            if (funcCode == "" && funcValue == "")
            {
                funcDetails.functionText = "";
            }
            else
            {
                funcDetails.functionText = funcCode + "-" + funcValue;
            }

            if (funcCode == "0")
            {
                funcDetails.functionValue = "";
            }
            else
            {
                funcDetails.functionValue = funcCode;
            }
            return funcDetails;
        }

        private dynamic GenerateDynamicProjectDimObject(string projCode, string projValue)
        {
            dynamic projDetails = new JObject();
            if (projCode == "" && projValue == "")
            {
                projDetails.project_name = "";
            }
            else
            {
                projDetails.project_name = projCode + "-" + projValue;
            }
            projDetails.fk_project_code = projCode;
            return projDetails;
        }

        private dynamic GenerateDynamicPeriodicKeyObject(string periodicKey, string periodicVal)
        {
            dynamic periodicKeyObj = new JObject();
            if (periodicKey == "" && periodicVal == "")
            {
                periodicKeyObj.value = "";
            }
            else
            {
                periodicKeyObj.value = periodicKey + "-" + periodicVal;
            }
            periodicKeyObj.key = periodicKey;
            return periodicKeyObj;
        }

        private dynamic GenerateDynamicFreeDimObject(string freeDimCode, string freeDimValue)
        {
            dynamic freeDimDetails = new JObject();
            if (freeDimCode == "" && freeDimValue == "")
            {
                freeDimDetails.freedim_name = "";
            }
            else
            {
                freeDimDetails.freedim_name = freeDimCode + "-" + freeDimValue;
            }
            freeDimDetails.fk_freedim_code = freeDimCode;
            return freeDimDetails;
        }

        private dynamic GenerateDynamicAdjustmentCodeObject(string adjustmentCode, string adjustmentValue)
        {
            dynamic adjustmentDetails = new JObject();
            if (adjustmentCode == "" && adjustmentValue == "")
            {
                adjustmentDetails.value = "";
            }
            else
            {
                adjustmentDetails.value = adjustmentCode + "-" + adjustmentValue;
            }

            if (adjustmentCode == "0")
            {
                adjustmentDetails.key = "";
            }
            else
            {
                adjustmentDetails.key = adjustmentCode;
            }
            return adjustmentDetails;
        }

        private dynamic GenerateDynamicAlterCodeObject(string alterCode, string alterValue)
        {
            dynamic alterDetails = new JObject();
            if (alterCode == "" && alterValue == "")
            {
                alterDetails.value = "";
            }
            else
            {
                alterDetails.value = alterCode + "-" + alterValue;
            }

            if (alterCode == "0")
            {
                alterDetails.key = "";
            }
            else
            {
                alterDetails.key = alterCode;
            }
            return alterDetails;
        }

        public IEnumerable<clsAssessmentAreas> GetAssessmentAreas(string userID, int actionType)
        {
            return GetAssessmentAreasAsync(userID, actionType).GetAwaiter().GetResult();
        }
        public async Task<IEnumerable<clsAssessmentAreas>> GetAssessmentAreasAsync(string userID, int actionType)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            List<gmd_assessment_areas> assessmentCategories =  await consequenceAdjustedBudgetDbContext.gmd_assessment_areas.Where(x => x.language_code == userDetails.language_preference && x.fk_action_type == actionType).ToListAsync();

            List<clsAssessmentAreas> categories = new List<clsAssessmentAreas>();
            foreach (gmd_assessment_areas a in assessmentCategories)
            {
                categories.Add(new clsAssessmentAreas() { fk_area_id = a.area_id.ToString(), fk_area_name = a.area_name });
            }

            return categories;
        }

        public string CreateActionAndChildItems(Framsikt.BL.Helpers.Action lst, string userID, int budgetYear,
            bool isFinancialPlan = false, bool isWholeValueChecked = false)
        {
            return CreateActionAndChildItemsAsync(lst, userID, budgetYear, isFinancialPlan, isWholeValueChecked).GetAwaiter().GetResult();
        }

        public async Task<string> CreateActionAndChildItemsAsync(Framsikt.BL.Helpers.Action lst, string userID, int budgetYear, bool isFinancialPlan = false, bool isWholeValueChecked = false)
        {
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userID, _utility.GetForecastPeriod(budgetYear, 1));
            var multiplyByThis = isWholeValueChecked ? 1 : 1000;
            List<string> departmentsList = new List<string>();

            if (!string.IsNullOrEmpty(lst.orgId))
            {
                List<clsOrgStructure> lstOrgStructure = await _utility.GetTenantOrgStructureAsync(orgVersionContent, userID);
                clsOrgStructureLevelDetails tenantOrgLevelDetails = _utility.GetTenantOrgLevelDetails(lstOrgStructure);
                List<List<string>> DepartmentsAndFunctions = _utility.GetDepartmentsAndFunctionsForOrgStructure(lstOrgStructure, lst.orgId, lst.serviceId, tenantOrgLevelDetails.level1, tenantOrgLevelDetails.level2);
                departmentsList = DepartmentsAndFunctions[0];
            }
            else
            {
                List<clsOrgIdAndDepartments> lstServiceAreaDepts = string.IsNullOrEmpty(lst.serviceAreaID) ? (await _utility.GetTenantDepartmentsForSelectedOrgIdAsync(orgVersionContent, userID, null)).ToList() : (await _utility.GetTenantDepartmentsForSelectedOrgIdAsync(orgVersionContent, userID, lst.serviceAreaID)).ToList();

                foreach (var d in lstServiceAreaDepts)
                {
                    string deptCode = d.departmentValue;
                    departmentsList.Add(deptCode);
                }
            }

            int logactionId = 0;
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userID);
                List<tfp_trans_header> lstActions = await consequenceAdjustedBudgetDbContext.tfp_trans_header.ToListAsync();
                int actionID;
                if (lstActions.Count > 0)
                {
                    lstActions.Sort((v1, v2) => v1.pk_action_id.CompareTo(v2.pk_action_id));
                    actionID = ((lstActions.LastOrDefault()).pk_action_id) + 1;
                }
                else
                {
                    actionID = 2000;
                }
                logactionId = actionID;

                decimal action_data_old_year1 = 0.0M;
                decimal action_data_old_year2 = 0.0M;
                decimal action_data_old_year3 = 0.0M;
                decimal action_data_old_year4 = 0.0M;

                decimal action_data_new_year1 = 0.0M;
                decimal action_data_new_year2 = 0.0M;
                decimal action_data_new_year3 = 0.0M;
                decimal action_data_new_year4 = 0.0M;

                decimal action_data_change_year1 = 0.0M;
                decimal action_data_change_year2 = 0.0M;
                decimal action_data_change_year3 = 0.0M;
                decimal action_data_change_year4 = 0.0M;
                if (await consequenceAdjustedBudgetDbContext.tfp_trans_header.FirstOrDefaultAsync(x => x.pk_action_id == lst.actionId) == null)
                {
                    await consequenceAdjustedBudgetDbContext.tfp_trans_header.AddAsync(new tfp_trans_header()
                    {
                        pk_action_id = actionID,
                        fk_tenant_id = userDetails.tenant_id,
                        line_order = 0,
                        isManuallyAdded = 1,
                        description = lst.actionDescription == null ? string.Empty : lst.actionDescription,
                        consequence = lst.consequence == null ? string.Empty : lst.consequence,
                        start_date = DateTime.UtcNow,
                        action_type = Convert.ToInt32(lst.groupId),
                        action_source = 0,
                        title = string.Empty,
                        fk_area_id = lst.fk_area_id,
                        tag = lst.tag,
                        priority = lst.priority,
                        long_description = lst.long_description ?? string.Empty,
                        updated = DateTime.UtcNow,
                        updated_by = userDetails.pk_id,
                        display_financial_plan_flag = false,
                        display_description_apendix_flag = false,
                        fk_cat_id = Guid.Empty,
                        financial_plan_description = lst.financial_plan_description ?? string.Empty,
                    });
                    foreach (Framsikt.BL.Helpers.ActionDetails a in lst.gridData.Where(x => x.isDeleted == false).ToList())
                    {
                        action_data_new_year1 += a.year1 * multiplyByThis;
                        action_data_new_year2 += a.year2 * multiplyByThis;
                        action_data_new_year3 += a.year3 * multiplyByThis;
                        action_data_new_year4 += a.year4 * multiplyByThis;

                        await consequenceAdjustedBudgetDbContext.tfp_trans_detail.AddAsync(
                        new tfp_trans_detail()
                        {
                            fk_action_id = actionID,
                            fk_tenant_id = userDetails.tenant_id,
                            budget_year = budgetYear,
                            fk_account_code = a.account.accountValue,
                            department_code = a.department.departmentValue,
                            function_code = a.functionn.functionValue,
                            project_code = a.project == null ? string.Empty : string.IsNullOrEmpty(a.project.fk_project_code) ? string.Empty : a.project.fk_project_code,
                            asset_code = string.Empty,
                            year_1_amount = a.year1 * multiplyByThis,
                            year_2_amount = a.year2 * multiplyByThis,
                            year_3_amount = a.year3 * multiplyByThis,
                            year_4_amount = a.year4 * multiplyByThis,
                            updated = DateTime.UtcNow,
                            updated_by = userDetails.pk_id,
                            free_dim_1 = a.freedim1 == null ? string.Empty : string.IsNullOrEmpty(a.freedim1.fk_freedim_code) ? string.Empty : a.freedim1.fk_freedim_code,
                            free_dim_2 = a.freedim2 == null ? string.Empty : string.IsNullOrEmpty(a.freedim2.fk_freedim_code) ? string.Empty : a.freedim2.fk_freedim_code,
                            free_dim_3 = a.freedim3 == null ? string.Empty : string.IsNullOrEmpty(a.freedim3.fk_freedim_code) ? string.Empty : a.freedim3.fk_freedim_code,
                            free_dim_4 = a.freedim4 == null ? string.Empty : string.IsNullOrEmpty(a.freedim4.fk_freedim_code) ? string.Empty : a.freedim4.fk_freedim_code,
                            fk_change_id = Convert.ToInt32(lst.changeid),
                            description = null,
                            fk_adjustment_code = string.Empty,
                            fk_adj_code = string.Empty,
                            fk_alter_code = string.IsNullOrEmpty(lst.fk_alter_code) ? string.Empty : lst.fk_alter_code
                        });
                    }
                    await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                    //Actions added from opportunity assesment
                    if (lst.OpportunityActionId != 0)
                    {
                        TenantDBContext opportunityAssessmentDbContext = await _utility.GetTenantDBContextAsync();
                        tsa_assessment_actions tsaActions = await opportunityAssessmentDbContext.tsa_assessment_actions.FirstOrDefaultAsync(x => x.pk_action_id == lst.OpportunityActionId);

                        tsaActions.status = 2;
                        tsaActions.fk_fp_action_id = actionID;
                        await opportunityAssessmentDbContext.SaveChangesAsync();
                    }
                }
                else
                {
                    actionID = (await consequenceAdjustedBudgetDbContext.tfp_trans_header.FirstOrDefaultAsync(x => x.pk_action_id == lst.actionId)).pk_action_id;
                    tfp_trans_header tfp_trans_header_data = await consequenceAdjustedBudgetDbContext.tfp_trans_header.FirstOrDefaultAsync(x => x.pk_action_id == lst.actionId);

                    tfp_trans_header_data.description = lst.actionDescription == null ? string.Empty : lst.actionDescription;
                    tfp_trans_header_data.consequence = lst.consequence == null ? string.Empty : lst.consequence;
                    tfp_trans_header_data.action_type = Convert.ToInt32(lst.groupId);
                    tfp_trans_header_data.title = string.Empty;
                    tfp_trans_header_data.fk_area_id = lst.fk_area_id;
                    tfp_trans_header_data.tag = lst.tag;
                    tfp_trans_header_data.priority = lst.priority;
                    tfp_trans_header_data.long_description = lst.long_description == null ? string.Empty : lst.long_description;
                    tfp_trans_header_data.updated = DateTime.UtcNow;
                    tfp_trans_header_data.updated_by = userDetails.pk_id;
                    tfp_trans_header_data.display_financial_plan_flag = false;
                    tfp_trans_header_data.display_description_apendix_flag = false;
                    tfp_trans_header_data.fk_cat_id = Guid.Empty;
                    tfp_trans_header_data.financial_plan_description = string.Empty;
                    await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                    foreach (Framsikt.BL.Helpers.ActionDetails a in lst.gridData.Where(x => x.isDeleted == false).ToList())
                    {
                        tfp_trans_detail td = await consequenceAdjustedBudgetDbContext.tfp_trans_detail.FirstOrDefaultAsync(x => x.pk_id == a.id);
                        if (td != null)
                        {
                            action_data_old_year1 += td.year_1_amount;
                            action_data_old_year2 += td.year_2_amount;
                            action_data_old_year3 += td.year_3_amount;
                            action_data_old_year4 += td.year_4_amount;

                            action_data_new_year1 += a.year1 * multiplyByThis;
                            action_data_new_year2 += a.year2 * multiplyByThis;
                            action_data_new_year3 += a.year3 * multiplyByThis;
                            action_data_new_year4 += a.year4 * multiplyByThis;

                            td.fk_account_code = a.account.accountValue;
                            td.department_code = a.department.departmentValue;
                            td.function_code = a.functionn.functionValue;
                            td.project_code = a.project == null ? string.Empty : string.IsNullOrEmpty(a.project.fk_project_code) ? string.Empty : a.project.fk_project_code;
                            td.asset_code = string.Empty;
                            td.year_1_amount = a.year1 * multiplyByThis;
                            td.year_2_amount = a.year2 * multiplyByThis;
                            td.year_3_amount = a.year3 * multiplyByThis;
                            td.year_4_amount = a.year4 * multiplyByThis;
                            td.updated = DateTime.UtcNow;
                            td.updated_by = userDetails.pk_id;
                            td.free_dim_1 = a.freedim1 == null ? string.Empty : string.IsNullOrEmpty(a.freedim1.fk_freedim_code) ? string.Empty : a.freedim1.fk_freedim_code;
                            td.free_dim_2 = a.freedim2 == null ? string.Empty : string.IsNullOrEmpty(a.freedim2.fk_freedim_code) ? string.Empty : a.freedim2.fk_freedim_code;
                            td.free_dim_3 = a.freedim3 == null ? string.Empty : string.IsNullOrEmpty(a.freedim3.fk_freedim_code) ? string.Empty : a.freedim3.fk_freedim_code;
                            td.free_dim_4 = a.freedim4 == null ? string.Empty : string.IsNullOrEmpty(a.freedim4.fk_freedim_code) ? string.Empty : a.freedim4.fk_freedim_code;
                            td.fk_change_id = Convert.ToInt32(lst.changeid);
                            td.description = null;
                            td.fk_adjustment_code = string.Empty;
                            td.fk_alter_code = string.IsNullOrEmpty(lst.fk_alter_code) ? string.Empty : lst.fk_alter_code;
                        }
                        else
                        {
                            action_data_new_year1 += a.year1 * multiplyByThis;
                            action_data_new_year2 += a.year2 * multiplyByThis;
                            action_data_new_year3 += a.year3 * multiplyByThis;
                            action_data_new_year4 += a.year4 * multiplyByThis;

                            consequenceAdjustedBudgetDbContext.tfp_trans_detail.Add(
                            new tfp_trans_detail()
                            {
                                fk_action_id = actionID,
                                fk_tenant_id = userDetails.tenant_id,
                                budget_year = budgetYear,
                                fk_account_code = a.account.accountValue,
                                department_code = a.department.departmentValue,
                                function_code = a.functionn.functionValue,
                                project_code = a.project == null ? string.Empty : string.IsNullOrEmpty(a.project.fk_project_code) ? string.Empty : a.project.fk_project_code,
                                asset_code = string.Empty,
                                year_1_amount = a.year1 * multiplyByThis,
                                year_2_amount = a.year2 * multiplyByThis,
                                year_3_amount = a.year3 * multiplyByThis,
                                year_4_amount = a.year4 * multiplyByThis,
                                updated = DateTime.UtcNow,
                                updated_by = userDetails.pk_id,
                                free_dim_1 = a.freedim1 == null ? string.Empty : string.IsNullOrEmpty(a.freedim1.fk_freedim_code) ? string.Empty : a.freedim1.fk_freedim_code,
                                free_dim_2 = a.freedim2 == null ? string.Empty : string.IsNullOrEmpty(a.freedim2.fk_freedim_code) ? string.Empty : a.freedim2.fk_freedim_code,
                                free_dim_3 = a.freedim3 == null ? string.Empty : string.IsNullOrEmpty(a.freedim3.fk_freedim_code) ? string.Empty : a.freedim3.fk_freedim_code,
                                free_dim_4 = a.freedim4 == null ? string.Empty : string.IsNullOrEmpty(a.freedim4.fk_freedim_code) ? string.Empty : a.freedim4.fk_freedim_code,
                                fk_change_id = Convert.ToInt32(lst.changeid),
                                description = null,
                                fk_adjustment_code = string.Empty,
                                fk_adj_code = string.Empty,
                                fk_alter_code = string.IsNullOrEmpty(lst.fk_alter_code) ? string.Empty : lst.fk_alter_code
                            });
                        }
                        await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                    }
                    foreach (Framsikt.BL.Helpers.ActionDetails a in lst.gridData.Where(x => x.isDeleted == true).ToList())
                    {
                        tfp_trans_detail td = await consequenceAdjustedBudgetDbContext.tfp_trans_detail.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                                                   && x.pk_id == a.id
                                                                                                                   && x.fk_action_id == actionID
                                                                                                                   && x.department_code == a.department.departmentValue
                                                                                                                   && x.function_code == a.functionn.functionValue
                                                                                                                   && x.fk_account_code == a.account.accountValue
                                                                                                                   && departmentsList.Contains(a.department.departmentValue));
                        if (td != null)
                        {
                            consequenceAdjustedBudgetDbContext.tfp_trans_detail.Remove(td);
                            await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                        }
                    }
                }

                if (isFinancialPlan == true && lst.groupId == "31" || lst.groupId == "41")
                {
                    action_data_change_year1 = action_data_new_year1 - action_data_old_year1;
                    action_data_change_year2 = action_data_new_year2 - action_data_old_year2;
                    action_data_change_year3 = action_data_new_year3 - action_data_old_year3;
                    action_data_change_year4 = action_data_new_year4 - action_data_old_year4;

                    int actionTypeToInsert = lst.groupId == "31" ? 30 :
                                             lst.groupId == "41" ? 40 : 0;

                    await UpdateBudgetLimitsAsync(userID, lst.serviceAreaID, actionTypeToInsert, action_data_change_year1, action_data_change_year2, action_data_change_year3, action_data_change_year4, budgetYear);
                }

                //log action in tfp_action_log
                if (lst.screenId != null)
                {
                    string result;
                    decimal lyear1 = 0, lyear2 = 0, lyear3 = 0, lyear4 = 0;
                    foreach (dynamic item in lst.gridData)
                    {
                        lyear1 = (lyear1 + item.year1);
                        lyear2 = (lyear2 + item.year2);
                        lyear3 = (lyear3 + item.year3);
                        lyear4 = (lyear4 + item.year4);
                    }
                    if (lst.actionId != 0)
                    {
                        result = await _utility.SaveActionLogAsync(userID, actionID, lst.actionDescription, Convert.ToInt32(lst.groupId), lyear1 * multiplyByThis, lyear2 * multiplyByThis, lyear3 * multiplyByThis, lyear4 * multiplyByThis, lst.screenId, clsConstants.ActionTransaction.UPD.ToString(), budgetYear);
                    }
                    else
                    {
                        result = await _utility.SaveActionLogAsync(userID, actionID, lst.actionDescription, Convert.ToInt32(lst.groupId), lyear1 * multiplyByThis, lyear2 * multiplyByThis, lyear3 * multiplyByThis, lyear4 * multiplyByThis, lst.screenId, clsConstants.ActionTransaction.ADD.ToString(), budgetYear);
                    }
                    return result;
                }
                return "Success";
            }
            catch (Exception ex)
            {
                string result = await _utility.SaveActionLogAsync(userID, logactionId, lst.actionDescription, Convert.ToInt32(lst.groupId), 0, 0, 0, 0, lst.screenId, clsConstants.ActionTransaction.ADD.ToString(), budgetYear);
                return "Failed- " + ex.InnerException.ToString();
            }
        }

        private async Task UpdateBudgetLimitsAsync(string userId, string serviceAreaId, int actionType, decimal year_1_amount, decimal year_2_amount, decimal year_3_amount, decimal year_4_amount, int BudgetYear)
        {
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            int budgetYear = BudgetYear;

            tfp_budget_limits budget_limit =await consequenceAdjustedBudgetDbContext.tfp_budget_limits.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                                                 && x.budget_year == budgetYear
                                                                                                                 && x.fk_org_id == serviceAreaId
                                                                                                                 && x.action_type == actionType);

            if (budget_limit != null)
            {
                decimal existing_budget_year_limit_1 = budget_limit.year_1_limit;
                decimal existing_budget_year_limit_2 = budget_limit.year_2_limit;
                decimal existing_budget_year_limit_3 = budget_limit.year_3_limit;
                decimal existing_budget_year_limit_4 = budget_limit.year_4_limit;

                budget_limit.year_1_limit = existing_budget_year_limit_1 + year_1_amount;
                budget_limit.year_2_limit = existing_budget_year_limit_2 + year_2_amount;
                budget_limit.year_3_limit = existing_budget_year_limit_3 + year_3_amount;
                budget_limit.year_4_limit = existing_budget_year_limit_4 + year_4_amount;
                budget_limit.updated = DateTime.UtcNow;
                budget_limit.updated_by = userDetails.pk_id;
            }
            else
            {
                budget_limit = new tfp_budget_limits()
                {
                    fk_tenant_id = userDetails.tenant_id,
                    budget_year = budgetYear,
                    fk_org_id = serviceAreaId,
                    action_type = actionType,
                    year_1_limit = year_1_amount,
                    year_2_limit = year_2_amount,
                    year_3_limit = year_3_amount,
                    year_4_limit = year_4_amount,
                    updated = DateTime.UtcNow,
                    updated_by = userDetails.pk_id
                };
            }

            await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
        }

        public async Task<dynamic> GetFunctionCodeDetails(string userID, string orgId, string serviceId, bool isBudgetChangePage, int BudgetYear)
        {
            int budgetYear = BudgetYear;
            int actionType = Convert.ToInt32(clsConstants.ActionType.DemographicBudgetAdjustments);
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userID, _utility.GetForecastPeriod(budgetYear, 1));
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ConsequenceAdjustedBudget");

            dynamic data = new JObject();
            dynamic headerYears = new JArray();
            dynamic serviceAreaData = new JArray();
            dynamic actionDetails = new JArray();
            dynamic totalData = new JArray();
            try
            {
                dynamic headerRow = new JObject();
                headerRow.planYear1 = ((langStringValues.FirstOrDefault(v => v.Key == "plan_text")).Value).LangText + budgetYear.ToString();
                headerRow.planYear2 = ((langStringValues.FirstOrDefault(v => v.Key == "plan_text")).Value).LangText + (budgetYear + 1).ToString();
                headerRow.planYear3 = ((langStringValues.FirstOrDefault(v => v.Key == "plan_text")).Value).LangText + (budgetYear + 2).ToString();
                headerRow.planYear4 = ((langStringValues.FirstOrDefault(v => v.Key == "plan_text")).Value).LangText + (budgetYear + 3).ToString();

                headerYears.Add(headerRow);

                var functionCodes = await (from a in consequenceAdjustedBudgetDbContext.tmd_demographic_mapping
                                     join b in consequenceAdjustedBudgetDbContext.tco_functions on new { a = a.fk_function_code, b = a.fk_tenant_id }
                                                                                            equals new { a = b.pk_Function_code, b = b.pk_tenant_id }
                                     where (a.fk_tenant_id == userDetails.tenant_id && a.budget_year == budgetYear)
                                     orderby a.fk_function_code
                                     select new
                                     {
                                         a.fk_function_code,
                                         b.display_name,
                                         a.fk_interval_id,
                                         a.fk_forecast_type,
                                         a.fk_forecast_type_2,
                                         a.coverage_pct_1,
                                         a.coverage_pct_2,
                                         a.coverage_pct_3,
                                         a.coverage_pct_4,
                                         a.compensation_pct_1,
                                         a.compensation_pct_2,
                                         a.compensation_pct_3,
                                         a.compensation_pct_4,
                                         a.unit_cost_1,
                                         a.unit_cost_2,
                                         a.unit_cost_3,
                                         a.unit_cost_4,
                                         a.active,
                                         a.activity_adjustment,
                                         a.activty_change_1,
                                         a.activty_change_2,
                                         a.activty_change_3,
                                         a.activty_change_4,
                                         a.fk_department_code,
                                         a.fk_account_code,
                                         a.free_dim_1,
                                         a.free_dim_2,
                                         a.free_dim_3,
                                         a.free_dim_4,
                                         a.fk_alter_code,
                                         a.fk_adjustment_code,
                                         a.fk_project_code
                                     }).ToListAsync();

                List<clsOrgStructure> lstOrgStructure = await _utility.GetTenantOrgStructureAsync(orgVersionContent, userID);
                clsOrgStructureLevelDetails tenantOrgLevelDetails = _utility.GetTenantOrgLevelDetails(lstOrgStructure);
                List<List<string>> DepartmentsAndFunctions = _utility.GetDepartmentsAndFunctionsForOrgStructure(lstOrgStructure, orgId, serviceId, tenantOrgLevelDetails.level1, tenantOrgLevelDetails.level2);
                List<string> lstAllDepartments = DepartmentsAndFunctions[0];
                List<string> lstAllFunctions = DepartmentsAndFunctions[1];

                List<int> changeIDs = await consequenceAdjustedBudgetDbContext.tfp_budget_changes.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                              && x.budget_year == budgetYear
                                                                              && x.org_budget_flag == 1).Select(x => x.pk_change_id).Distinct().ToListAsync();

                if (lstAllDepartments.Count() > 0 && lstAllFunctions.Count() > 0)
                {
                    functionCodes = functionCodes.Where(x => lstAllFunctions.Contains(x.fk_function_code) && lstAllDepartments.Contains(x.fk_department_code)).ToList();
                }
                else if (lstAllDepartments.Count() > 0)
                {
                    functionCodes = functionCodes.Where(x => lstAllDepartments.Contains(x.fk_department_code)).ToList();
                }
                else if (lstAllFunctions.Count() > 0)
                {
                    functionCodes = functionCodes.Where(x => lstAllFunctions.Contains(x.fk_function_code)).ToList();
                }

                List<decimal> totals = new List<decimal>() { 0.0M, 0.0M, 0.0M, 0.0M };
                foreach (var v in functionCodes)
                {
                    var td = await (from t in consequenceAdjustedBudgetDbContext.vwUserDetails
                              where t.pk_id == -1 && t.tenant_id==userDetails.tenant_id && t.client_id==userDetails.client_id
                              select new
                              {
                                  pk_action_id = 0,
                                  year_1_amount = 0.0M,
                                  year_2_amount = 0.0M,
                                  year_3_amount = 0.0M,
                                  year_4_amount = 0.0M
                              }).FirstOrDefaultAsync();

                    if (lstAllDepartments.Count() > 0)
                    {
                        td = (await (from h in consequenceAdjustedBudgetDbContext.tfp_trans_header
                              join d in consequenceAdjustedBudgetDbContext.tfp_trans_detail on new { a = h.pk_action_id, b = h.fk_tenant_id }
                                                                                        equals new { a = d.fk_action_id, b = d.fk_tenant_id }
                              where (d.fk_tenant_id == userDetails.tenant_id
                                  && h.action_type == actionType
                                  && h.isManuallyAdded == 0
                                  && d.budget_year == budgetYear
                                  && d.function_code == v.fk_function_code
                                  && lstAllDepartments.Contains(v.fk_department_code)
                                  && d.department_code == v.fk_department_code
                                  && (changeIDs.Contains(d.fk_change_id) || d.fk_change_id == 0))
                              group d by new { h.pk_action_id } into d2
                              select new
                              {
                                  pk_action_id = d2.Key.pk_action_id,
                                  year_1_amount = d2.Sum(x => x.year_1_amount),
                                  year_2_amount = d2.Sum(x => x.year_2_amount),
                                  year_3_amount = d2.Sum(x => x.year_3_amount),
                                  year_4_amount = d2.Sum(x => x.year_4_amount)
                              }).ToListAsync()).FirstOrDefault();
                    }
                    else
                    {
                        td = (await (from h in consequenceAdjustedBudgetDbContext.tfp_trans_header
                              join d in consequenceAdjustedBudgetDbContext.tfp_trans_detail on new { a = h.pk_action_id, b = h.fk_tenant_id }
                                                                                        equals new { a = d.fk_action_id, b = d.fk_tenant_id }
                              where (d.fk_tenant_id == userDetails.tenant_id
                                  && h.action_type == actionType
                                  && h.isManuallyAdded == 0
                                  && d.budget_year == budgetYear
                                  && d.function_code == v.fk_function_code
                                  && (changeIDs.Contains(d.fk_change_id) || d.fk_change_id == 0))
                              group d by new { h.pk_action_id } into d2
                              select new
                              {
                                  pk_action_id = d2.Key.pk_action_id,
                                  year_1_amount = d2.Sum(x => x.year_1_amount),
                                  year_2_amount = d2.Sum(x => x.year_2_amount),
                                  year_3_amount = d2.Sum(x => x.year_3_amount),
                                  year_4_amount = d2.Sum(x => x.year_4_amount)
                              }).ToListAsync()).FirstOrDefault();
                    }

                    dynamic row = new JObject();
                    row.id = v.fk_function_code;
                    row.name = v.display_name;
                    string fCode = v.fk_function_code;
                    string desc = (await _utility.GetActionTypeDescriptionAsync(userID, orgId, fCode, "CAB-Detail", null, budgetYear, "")).Value;
                    row.textAreaData = desc;
                    row.textAreaDataId = (await _utility.GetActionTypeDescriptionAsync(userID, orgId, fCode, "CAB-Detail", null, budgetYear, "")).Key;
                    row.amountYear1 = (td == null ? 0 : td.year_1_amount);
                    totals[0] = totals[0] + (td == null ? 0 : td.year_1_amount);
                    row.amountYear2 = (td == null ? 0 : td.year_2_amount);
                    totals[1] = totals[1] + (td == null ? 0 : td.year_2_amount);
                    row.amountYear3 = (td == null ? 0 : td.year_3_amount);
                    totals[2] = totals[2] + (td == null ? 0 : td.year_3_amount);
                    row.amountYear4 = (td == null ? 0 : td.year_4_amount);
                    totals[3] = totals[3] + (td == null ? 0 : td.year_4_amount);
                    row.chkDemographicAdjustment = v.active == 1 ? true : false;
                    row.chkDemographicAdjustmentText = ((langStringValues.FirstOrDefault(p => p.Key == "cab_demographic_adjustment")).Value).LangText;
                    row.Coverage1 = v.coverage_pct_1;
                    row.Coverage2 = v.coverage_pct_2;
                    row.Coverage3 = v.coverage_pct_3;
                    row.Coverage4 = v.coverage_pct_4;
                    row.compensation1 = v.compensation_pct_1;
                    row.compensation2 = v.compensation_pct_2;
                    row.compensation3 = v.compensation_pct_3;
                    row.compensation4 = v.compensation_pct_4;
                    row.unitCost1 = v.unit_cost_1;
                    row.unitCost2 = v.unit_cost_2;
                    row.unitCost3 = v.unit_cost_3;
                    row.unitCost4 = v.unit_cost_4;

                    dynamic forecastType = new JArray();
                    dynamic ft = new JObject();
                    ft.ForecastTypeCode = v.fk_forecast_type;
                    string forecastName1 = (await GetForecastType(userID)).FirstOrDefault(x => x.Key == v.fk_forecast_type).Value;
                    ft.ForecastName = forecastName1 == null ? "" : forecastName1;
                    forecastType.Add(ft);
                    row.Add("forecastType", forecastType);

                    dynamic forecastType2 = new JArray();
                    dynamic ft2 = new JObject();
                    ft2.ForecastTypeCode = v.fk_forecast_type_2;
                    string forecastName2 = (await GetForecastType(userID)).FirstOrDefault(x => x.Key == v.fk_forecast_type_2).Value;
                    ft2.ForecastName = forecastName2 == null ? "" : forecastName2;
                    forecastType2.Add(ft2);
                    row.Add("forecastType2", forecastType2);

                    dynamic ageInterval = new JArray();
                    dynamic ai = new JObject();
                    ai.AgeIntervalCode = (await GetAgeIntervalsAsync(v.fk_interval_id, userID)).FirstOrDefault().Key;
                    ai.AgeIntervalName = (await GetAgeIntervalsAsync(v.fk_interval_id, userID)).FirstOrDefault().Value;
                    ageInterval.Add(ai);
                    row.Add("ageInterval", ageInterval);

                    row.chkActivityAdjustment = v.activity_adjustment == 1 ? true : false;
                    row.chkActivityAdjustmentText = ((langStringValues.FirstOrDefault(p => p.Key == "cab_activity_adjustment")).Value).LangText;
                    row.activityChange1 = v.activty_change_1;
                    row.activityChange2 = v.activty_change_2;
                    row.activityChange3 = v.activty_change_3;
                    row.activityChange4 = v.activty_change_4;

                    if (td == null)
                    {
                        List<tfp_trans_header> lstTransActions = await consequenceAdjustedBudgetDbContext.tfp_trans_header.ToListAsync();
                        int actionID;
                        if (lstTransActions.Count > 0)
                        {
                            lstTransActions.Sort((v1, v2) => v1.pk_action_id.CompareTo(v2.pk_action_id));
                            actionID = ((lstTransActions.LastOrDefault()).pk_action_id) + 1;
                        }
                        else
                        {
                            actionID = 2000;
                        }

                        await consequenceAdjustedBudgetDbContext.tfp_trans_header.AddAsync(new tfp_trans_header()
                        {
                            pk_action_id = actionID,
                            fk_tenant_id = userDetails.tenant_id,
                            line_order = 0,
                            isManuallyAdded = 0,
                            description = ((langStringValues.FirstOrDefault(x => x.Key == "new_budget_adj_for_func_code_text")).Value).LangText + " - " + v.display_name,
                            consequence = string.Empty,
                            start_date = DateTime.UtcNow,
                            action_type = 6,
                            action_source = 0,
                            title = string.Empty,
                            updated = DateTime.UtcNow,
                            updated_by = userDetails.pk_id,
                            display_financial_plan_flag = false,
                            display_description_apendix_flag = false,
                            fk_cat_id = Guid.Empty,
                            financial_plan_description = string.Empty,
                        });

                        await consequenceAdjustedBudgetDbContext.tfp_trans_detail.AddAsync(
                        new tfp_trans_detail()
                        {
                            fk_action_id = actionID,
                            fk_tenant_id = userDetails.tenant_id,
                            budget_year = BudgetYear,
                            fk_account_code = v.fk_account_code,
                            department_code = v.fk_department_code,
                            function_code = v.fk_function_code,
                            project_code = string.Empty,
                            asset_code = string.Empty,
                            year_1_amount = 0,
                            year_2_amount = 0,
                            year_3_amount = 0,
                            year_4_amount = 0,
                            updated = DateTime.UtcNow,
                            updated_by = userDetails.pk_id,
                            free_dim_1 = string.Empty,
                            free_dim_2 = string.Empty,
                            free_dim_3 = string.Empty,
                            free_dim_4 = string.Empty,

                            fk_change_id = 0,
                            description = string.Empty,
                            fk_adjustment_code = v.fk_adjustment_code,
                            fk_alter_code = v.fk_alter_code,
                            fk_adj_code = string.Empty
                        });

                        await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                        row.actionId = actionID;
                    }
                    else
                    {
                        row.actionId = td.pk_action_id;
                    }
                    row.groupId = actionType;
                    row.department_code = v.fk_department_code;
                    row.free_dim_1 = v.free_dim_1;
                    row.free_dim_2 = v.free_dim_2;
                    row.free_dim_3 = v.free_dim_3;
                    row.free_dim_4 = v.free_dim_4;
                    row.fk_adjustment_code = v.fk_adjustment_code;
                    row.fk_alter_code = v.fk_alter_code;
                    row.fk_account_code = v.fk_account_code;
                    row.fk_project_code = v.fk_project_code;

                    serviceAreaData.Add(row);
                }

                dynamic actionData = await GetDataForSelectedOrgIdAndServiceIdAsync(userID, orgId, serviceId == null ? null : serviceId.ToUpper() == "ALL" ? null : serviceId, "DemographicBudgetAdjustments", isBudgetChangePage, BudgetYear);

                dynamic actionRow = new JObject();

                foreach (var a in actionData["rows"])
                {
                    actionRow = new JObject();
                    actionRow.id = a["id"];
                    actionRow.name = a["serviceArea"];

                    actionRow.amountYear1 = a["gridData"][0] / 1000;
                    totals[0] = totals[0] + Convert.ToDecimal(a["gridData"][0]) / 1000;
                    actionRow.amountYear2 = a["gridData"][1] / 1000;
                    totals[1] = totals[1] + Convert.ToDecimal(a["gridData"][1]) / 1000;
                    actionRow.amountYear3 = a["gridData"][2] / 1000;
                    totals[2] = totals[2] + Convert.ToDecimal(a["gridData"][2]) / 1000;
                    actionRow.amountYear4 = a["gridData"][3] / 1000;
                    totals[3] = totals[3] + Convert.ToDecimal(a["gridData"][3]) / 1000;

                    actionDetails.Add(actionRow);
                }

                dynamic totalRow = new JObject();
                totalRow.id = null;
                totalRow.name = ((langStringValues.FirstOrDefault(v => v.Key == "total_budget_adjustments_demographic_text")).Value).LangText;
                totalRow.amountYear1 = totals[0];
                totalRow.amountYear2 = totals[1];
                totalRow.amountYear3 = totals[2];
                totalRow.amountYear4 = totals[3];
                totalData.Add(totalRow);

                data.Add("headerYears", headerYears);
                data.Add("serviceAreaData", serviceAreaData);
                data.Add("actionData", actionDetails);
                data.Add("totalData", totalData);
                data.Add("IsEditable", await ActiveBudgetChangeExistsAsync(userID, false, BudgetYear));
                data.amountFormat = "#,##";

                int id = 0;
                List<tco_budget_totals> lstActions = await consequenceAdjustedBudgetDbContext.tco_budget_totals.ToListAsync();
                if (lstActions.Count() > 0)
                {
                    lstActions.Sort((v1, v2) => v1.pk_id.CompareTo(v2.pk_id));
                    id = ((lstActions.LastOrDefault()).pk_id) + 1;
                }
                else
                {
                    id = 100;
                }
                tco_budget_totals totalBudgetRow = await consequenceAdjustedBudgetDbContext.tco_budget_totals.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                                      && x.fk_action_type.Value == actionType
                                                                                                      && x.budget_year == budgetYear
                                                                                                      && x.FINPLAN_LEVEL_1 == orgId
                                                                                                      && x.FINPLAN_LEVEL_2 == serviceId).FirstOrDefaultAsync();
                if (totalBudgetRow != null)
                {
                    totalBudgetRow.fin_plan_year1 = totals[0];
                    totalBudgetRow.fin_plan_year2 = totals[1];
                    totalBudgetRow.fin_plan_year3 = totals[2];
                    totalBudgetRow.fin_plan_year4 = totals[3];

                    await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                }
                else
                {
                    await consequenceAdjustedBudgetDbContext.tco_budget_totals.AddAsync(new tco_budget_totals()
                    {
                        pk_id = id,
                        fk_tenant_id = userDetails.tenant_id,
                        fk_action_type = actionType,
                        budget_year = budgetYear,
                        FINPLAN_LEVEL_1 = orgId,
                        FINPLAN_LEVEL_2 = serviceId,
                        fin_plan_year1 = totals[0],
                        fin_plan_year2 = totals[1],
                        fin_plan_year3 = totals[2],
                        fin_plan_year4 = totals[3]
                    });
                    await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                }

                return data;
            }
            //catch (DbEntityValidationException)
            //{
            //    data.Add("headerYears", headerYears);
            //    data.Add("serviceAreaData", serviceAreaData);
            //    data.Add("actionData", actionDetails);
            //    data.Add("totalData", totalData);
            //    data.amountFormat = "#,##";

            //    return data;
            //}
            catch (Exception)
            {
                data.Add("headerYears", headerYears);
                data.Add("serviceAreaData", serviceAreaData);
                data.Add("actionData", actionDetails);
                data.Add("totalData", totalData);
                data.amountFormat = "#,##";

                return data;
            }
        }

        public async Task<Dictionary<string, string>> GetForecastType(string userID)
        {
            List<ForecastType> lstResultSet = new List<ForecastType>();
            IEnumerable<ForecastType> DefaultForecastType = await _popStatistics.GetDefaultForcastTypeAsync(userID);
            lstResultSet.AddRange(DefaultForecastType);
            string StrDefaultForecastType = DefaultForecastType.FirstOrDefault().ForecastTypeCode.ToString();
            IEnumerable<ForecastType> lstForecastType = await _popStatistics.GetAllForcastTypeExceptDefaultAsync(userID, StrDefaultForecastType);
            lstResultSet.AddRange(lstForecastType);
            Dictionary<string, string> dictForecastTypes = (from a in lstResultSet
                                                            select new
                                                            {
                                                                a.ForecastTypeCode,
                                                                a.ForecastName
                                                            }).Distinct().ToDictionary(x => x.ForecastTypeCode, x => x.ForecastName);
            return dictForecastTypes;
        }

        public Dictionary<int, string> GetAgeIntervals(int? intervalID, string userID)
        {
            return GetAgeIntervalsAsync(intervalID, userID).GetAwaiter().GetResult();
        }
        public async Task<Dictionary<int, string>> GetAgeIntervalsAsync(int? intervalID, string userID)
        {
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            Dictionary<int, string> ageIntervals = new Dictionary<int, string>();
            if (intervalID == null)
            {
                Dictionary<int, string> lst = await (from a in consequenceAdjustedBudgetDbContext.gmd_fp_demographic_intervals
                                               where (a.active == 1)
                                               select new
                                               {
                                                   a.interval_id,
                                                   a.interval_name
                                               }).Distinct().ToDictionaryAsync(x => x.interval_id, x => x.interval_name);
                foreach (KeyValuePair<int, string> s in lst)
                {
                    ageIntervals.Add(s.Key, s.Value);
                }
            }
            else
            {
                Dictionary<int, string> lst = await (from a in consequenceAdjustedBudgetDbContext.gmd_fp_demographic_intervals
                                               where (a.interval_id == intervalID && a.active == 1)
                                               select new
                                               {
                                                   a.interval_id,
                                                   a.interval_name
                                               }).Distinct().ToDictionaryAsync(x => x.interval_id, x => x.interval_name);
                foreach (KeyValuePair<int, string> s in lst)
                {
                    ageIntervals.Add(s.Key, s.Value);
                }
            }
            return ageIntervals;
        }

        public async Task<JObject> AddIntoTcoUserAdjCode(string userId, int budgetYear, string tcoAdjCode)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            tco_users_settings userSettings =
                await consequenceAdjustedBudgetDbContext.tco_users_settings.FirstOrDefaultAsync(x =>
                    x.fk_user_id == userDetails.pk_id && x.tenant_id == userDetails.tenant_id && x.is_active_tenant);
            List<clsBudgetChangesData> data = await (from tfpbc in consequenceAdjustedBudgetDbContext.tfp_budget_changes
                                                     where tfpbc.budget_year == budgetYear && tfpbc.fk_tenant_id == userDetails.tenant_id && tfpbc.org_budget_flag == 0
                                                     orderby tfpbc.change_date ascending
                                                     select new clsBudgetChangesData
                                                     {
                                                         id = tfpbc.pk_change_id,
                                                         budget_change = tfpbc.budget_name,
                                                         approval_reference = tfpbc.approval_reference,
                                                         date = tfpbc.change_date,
                                                         statusVal = tfpbc.status,
                                                         isActive = 0,
                                                         workflowStatus = tfpbc.workflow_status,
                                                         statusCode = tfpbc.status,
                                                         isSelected = tfpbc.is_selected,
                                                         not_approved = tfpbc.not_approved
                                                     }).ToListAsync();
            data.ForEach(x =>
            {
                x.isActive = userSettings == null ? 0 : userSettings.active_change_id_budget_changes == null ? 0 : userSettings.active_change_id_budget_changes == x.id ? 1 : 0;
            });
            clsBudgetChangesData idObject = data.FirstOrDefault(x => x.isActive == 1);
            tco_user_adjustment_codes userAdjCode = await consequenceAdjustedBudgetDbContext.tco_user_adjustment_codes.FirstOrDefaultAsync(x => x.pk_adj_code == tcoAdjCode && x.fk_tenant_id == userDetails.tenant_id);
            tco_adjustment_codes existingAdjCode = await consequenceAdjustedBudgetDbContext.tco_adjustment_codes.FirstOrDefaultAsync(x => x.pk_adjustment_code == tcoAdjCode && x.fk_tenant_id == userDetails.tenant_id);
            dynamic obj = new JObject();
            if (userAdjCode == null)
            {
                tco_user_adjustment_codes newAdjCode = new tco_user_adjustment_codes
                {
                    fk_tenant_id = userDetails.tenant_id,
                    pk_adj_code = tcoAdjCode,
                    description = existingAdjCode.description,
                    description_id = Guid.NewGuid(),
                    status = !idObject.not_approved,
                    fk_user_id = userDetails.pk_id,
                    updated = DateTime.UtcNow,
                    org_level_value = userDetails.tenant_id.ToString(),
                    budget_year = budgetYear,
                    org_level = 1,
                    is_original_flag = false,
                    prefix_adjCode = null,
                    inv_adj_code = false,
                    fk_main_project_code = null,
                    fk_change_id = idObject.id,
                    include_in_calculation = false
                };
                consequenceAdjustedBudgetDbContext.tco_user_adjustment_codes.Add(newAdjCode);
                await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                obj.adjCode = tcoAdjCode;
                obj.msg = "";
                obj.id = newAdjCode.Id;
                return obj;
            }
            obj.adjCode = tcoAdjCode;
            obj.msg = "";
            obj.id = userAdjCode.Id;
            return obj;
        }
    }

    public static class pageType
    {
        public const string finplanOverview = "FINPLANOVERVIEW";
    }
}