#pragma warning disable CS8629

#pragma warning disable CS8600
#pragma warning disable CS8601
#pragma warning disable CS8602
#pragma warning disable CS8604
#pragma warning disable CS8625

using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Data;
using System.Globalization;
using System.Linq.Dynamic.Core;
using System.Text;
using KeyValuePair = Framsikt.BL.Helpers.KeyValuePair;

namespace Framsikt.BL
{
    public partial class ConsequenceAdjustedBudget
    {
        public async Task<dynamic> GetDemographicData(clsDemographicData demographicData, string userID)
        {
            int budgetYear = demographicData.BudgetYear;

            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            TenantDBContext populationStatisticsDbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            TenantData tenantDetails = await _utility.GetTenantDataAsync(userID);
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ConsequenceAdjustedBudget");
            string regionCode = tenantDetails.municipality_id;

            dynamic data = new JObject();
            dynamic columnTitles = new JArray();
            dynamic columnFields = new JArray();
            dynamic gridData = new JArray();
            dynamic row = new JObject();

            List<string> ageIntervals = await (from a in consequenceAdjustedBudgetDbContext.gmd_fp_demographic_intervals
                                               where a.interval_id == demographicData.intervalID && a.active == 1
                                               select a.fk_age_interval).ToListAsync();

            var previousYearPopForecast = await (from p in populationStatisticsDbContext.gco_pop_forecast
                                                 where (p.forecast_type == demographicData.forecastType2 && p.fk_municipality_id == regionCode && p.year == (budgetYear - 1) && ageIntervals.Contains(p.age_interval))
                                                 select new
                                                 {
                                                     p.fk_municipality_id,
                                                     p.age_interval,
                                                     p.year,
                                                     p.forecast_type,
                                                     p.forecast
                                                 }).ToListAsync();

            var previousYearData = (from p in previousYearPopForecast
                                    group p by new { p.year } into g
                                    select new
                                    {
                                        year = g.Key.year,
                                        forecast = g.Sum(p => p.forecast)
                                    }).OrderBy(x => x.year).ToList();

            List<int> years = new List<int>() { budgetYear, budgetYear + 1, budgetYear + 2, budgetYear + 3 };
            var popForecast = await (from p in populationStatisticsDbContext.gco_pop_forecast
                                     where (p.forecast_type == demographicData.forecastType && p.fk_municipality_id == regionCode && years.Contains(p.year) && ageIntervals.Contains(p.age_interval))
                                     select new
                                     {
                                         p.fk_municipality_id,
                                         p.age_interval,
                                         p.year,
                                         p.forecast_type,
                                         p.forecast
                                     }).ToListAsync();

            var allYearData = (from p in popForecast
                               group p by new { p.year } into g
                               select new
                               {
                                   year = g.Key.year,
                                   forecast = g.Sum(p => p.forecast)
                               }).OrderBy(x => x.year).ToList();

            int year1Amount = (allYearData.Count >= 1 ? allYearData[0].forecast : 0) - (previousYearData.Count >= 1 ? previousYearData[0].forecast : 0);

            int year2Amount = (allYearData.Count >= 2 ? allYearData[1].forecast : 0) - (previousYearData.Count >= 1 ? previousYearData[0].forecast : 0);

            int year3Amount = (allYearData.Count >= 3 ? allYearData[2].forecast : 0) - (previousYearData.Count >= 1 ? previousYearData[0].forecast : 0);

            int year4Amount = (allYearData.Count >= 4 ? allYearData[3].forecast : 0) - (previousYearData.Count >= 1 ? previousYearData[0].forecast : 0);

            columnTitles.Add(" ");
            columnTitles.Add(" ");
            columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "plan_text")).Value).LangText + budgetYear.ToString());
            columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "plan_text")).Value).LangText + (budgetYear + 1).ToString());
            columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "plan_text")).Value).LangText + (budgetYear + 2).ToString());
            columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "plan_text")).Value).LangText + (budgetYear + 3).ToString());

            columnFields.Add("id");
            columnFields.Add("serviceAreaDescription");
            columnFields.Add("year1Amount");
            columnFields.Add("year2Amount");
            columnFields.Add("year3Amount");
            columnFields.Add("year4Amount");

            //0 row
            row.id = 1;
            row.isEditable = false;
            row.serviceAreaDescription = ((langStringValues.FirstOrDefault(v => v.Key == "population_change_text")).Value).LangText;
            row.year1 = year1Amount;
            row.year2 = year2Amount;
            row.year3 = year3Amount;
            row.year4 = year4Amount;
            gridData.Add(row);

            //1 row
            row = new JObject();
            row.id = 2;
            row.isEditable = true;
            row.serviceAreaDescription = ((langStringValues.FirstOrDefault(v => v.Key == "coverage_percentage_text")).Value).LangText;
            row.year1 = demographicData.coverage1;
            row.year2 = demographicData.coverage2;
            row.year3 = demographicData.coverage3;
            row.year4 = demographicData.coverage4;
            gridData.Add(row);

            //2 row
            row = new JObject();
            row.id = 3;
            row.isEditable = false;
            row.serviceAreaDescription = ((langStringValues.FirstOrDefault(v => v.Key == "activity_change_text")).Value).LangText;
            row.year1 = demographicData.coverage1 * year1Amount / 100;
            row.year2 = demographicData.coverage2 * year2Amount / 100;
            row.year3 = demographicData.coverage3 * year3Amount / 100;
            row.year4 = demographicData.coverage4 * year4Amount / 100;
            gridData.Add(row);

            //3 row
            row = new JObject();
            row.id = 4;
            row.isEditable = true;
            row.serviceAreaDescription = ((langStringValues.FirstOrDefault(v => v.Key == "marginal_unit_cost_text")).Value).LangText;
            row.year1 = demographicData.unitCost1;
            row.year2 = demographicData.unitCost2;
            row.year3 = demographicData.unitCost3;
            row.year4 = demographicData.unitCost4;
            gridData.Add(row);

            //4 row
            row = new JObject();
            row.id = 5;
            row.isEditable = false;
            row.serviceAreaDescription = ((langStringValues.FirstOrDefault(v => v.Key == "gross_budget_adjustments_text")).Value).LangText;
            row.year1 = (demographicData.unitCost1 * (demographicData.coverage1 * year1Amount / 100) / 100) * 100;
            row.year2 = (demographicData.unitCost2 * (demographicData.coverage2 * year2Amount / 100) / 100) * 100;
            row.year3 = (demographicData.unitCost3 * (demographicData.coverage3 * year3Amount / 100) / 100) * 100;
            row.year4 = (demographicData.unitCost4 * (demographicData.coverage4 * year4Amount / 100) / 100) * 100;
            gridData.Add(row);

            //5 row
            row = new JObject();
            row.id = 6;
            row.isEditable = true;
            row.serviceAreaDescription = ((langStringValues.FirstOrDefault(v => v.Key == "demographic_adj_com_per_text")).Value).LangText;
            row.year1 = demographicData.compensation1;
            row.year2 = demographicData.compensation2;
            row.year3 = demographicData.compensation3;
            row.year4 = demographicData.compensation4;
            gridData.Add(row);

            //6 row
            row = new JObject();
            row.id = 7;
            row.isEditable = false;
            row.serviceAreaDescription = ((langStringValues.FirstOrDefault(v => v.Key == "new_budget_adj_for_func_code_text")).Value).LangText + demographicData.functionName;
            row.year1 = (demographicData.compensation1 * (demographicData.unitCost1 * (demographicData.coverage1 * year1Amount / 100) / 100) / 100) * 100;
            row.year2 = (demographicData.compensation2 * (demographicData.unitCost2 * (demographicData.coverage2 * year2Amount / 100) / 100) / 100) * 100;
            row.year3 = (demographicData.compensation3 * (demographicData.unitCost3 * (demographicData.coverage3 * year3Amount / 100) / 100) / 100) * 100;
            row.year4 = (demographicData.compensation4 * (demographicData.unitCost4 * (demographicData.coverage4 * year4Amount / 100) / 100) / 100) * 100;
            gridData.Add(row);

            data.Add("columnTitles", columnTitles);
            data.Add("columnFields", columnFields);
            data.Add("gridData", gridData);

            return data;
        }

        public async Task<string> CreateUpdateDemographicData(clsDemographicData demographicData, string userID)
        {
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            int budgetYear = demographicData.BudgetYear;
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userID);
                tmd_demographic_mapping tmd_demo_map = consequenceAdjustedBudgetDbContext.tmd_demographic_mapping
                                                     .FirstOrDefault(x => x.fk_department_code == demographicData.department_code
                                                                          && x.fk_function_code == demographicData.functionCode
                                                                          && x.fk_tenant_id == userDetails.tenant_id
                                                                          && x.budget_year == budgetYear);
                Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ConsequenceAdjustedBudget");

                if (tmd_demo_map != null)
                {
                    tmd_demo_map.fk_forecast_type = demographicData.forecastType;
                    tmd_demo_map.fk_forecast_type_2 = demographicData.forecastType2;
                    tmd_demo_map.fk_interval_id = demographicData.intervalID;

                    tmd_demo_map.coverage_pct_1 = demographicData.coverage1;
                    tmd_demo_map.coverage_pct_2 = demographicData.coverage2;
                    tmd_demo_map.coverage_pct_3 = demographicData.coverage3;
                    tmd_demo_map.coverage_pct_4 = demographicData.coverage4;

                    tmd_demo_map.compensation_pct_1 = demographicData.compensation1;
                    tmd_demo_map.compensation_pct_2 = demographicData.compensation2;
                    tmd_demo_map.compensation_pct_3 = demographicData.compensation3;
                    tmd_demo_map.compensation_pct_4 = demographicData.compensation4;

                    tmd_demo_map.unit_cost_1 = demographicData.unitCost1;
                    tmd_demo_map.unit_cost_2 = demographicData.unitCost2;
                    tmd_demo_map.unit_cost_3 = demographicData.unitCost3;
                    tmd_demo_map.unit_cost_4 = demographicData.unitCost4;

                    tmd_demo_map.active = demographicData.active == true ? 1 : 0;

                    tmd_demo_map.activity_adjustment = demographicData.activityAdjustment == true ? 1 : 0;

                    tmd_demo_map.activty_change_1 = demographicData.activityChange1;
                    tmd_demo_map.activty_change_2 = demographicData.activityChange2;
                    tmd_demo_map.activty_change_3 = demographicData.activityChange3;
                    tmd_demo_map.activty_change_4 = demographicData.activityChange4;
                    tmd_demo_map.fk_account_code = string.IsNullOrEmpty(demographicData.fk_account_code) ? string.Empty : demographicData.fk_account_code;
                    tmd_demo_map.fk_department_code = string.IsNullOrEmpty(demographicData.department_code) ? string.Empty : demographicData.department_code;
                    tmd_demo_map.fk_function_code = string.IsNullOrEmpty(demographicData.functionCode) ? string.Empty : demographicData.functionCode;
                    tmd_demo_map.fk_project_code = string.IsNullOrEmpty(demographicData.fk_project_code) ? string.Empty : demographicData.fk_project_code;
                    tmd_demo_map.free_dim_1 = string.IsNullOrEmpty(demographicData.free_dim_1) ? string.Empty : demographicData.free_dim_1;
                    tmd_demo_map.free_dim_2 = string.IsNullOrEmpty(demographicData.free_dim_2) ? string.Empty : demographicData.free_dim_2;
                    tmd_demo_map.free_dim_3 = string.IsNullOrEmpty(demographicData.free_dim_3) ? string.Empty : demographicData.free_dim_3;
                    tmd_demo_map.free_dim_4 = string.IsNullOrEmpty(demographicData.free_dim_4) ? string.Empty : demographicData.free_dim_4;
                    tmd_demo_map.fk_alter_code = string.IsNullOrEmpty(demographicData.fk_alter_code) ? string.Empty : demographicData.fk_alter_code;
                    tmd_demo_map.fk_adjustment_code = string.IsNullOrEmpty(demographicData.fk_adjustment_code) ? string.Empty : demographicData.fk_adjustment_code;
                    tmd_demo_map.updated = DateTime.UtcNow;
                    tmd_demo_map.updated_by = userDetails.pk_id.ToString();

                    await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                }

                int activeChangeId = -1;
                activeChangeId = await GetFinplanOverviewActiveBudgetChangeAsync(userID, false, demographicData.BudgetYear);

                List<int> actionIdsOfActiveBudgetChangeData = (from th in consequenceAdjustedBudgetDbContext.tfp_trans_header
                                                               join td in consequenceAdjustedBudgetDbContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                                                                                                                          equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                                                               where th.fk_tenant_id == userDetails.tenant_id
                                                                  && th.action_type == 6
                                                                  && td.budget_year == budgetYear
                                                                  && td.function_code == demographicData.functionCode
                                                                  && td.department_code == demographicData.department_code
                                                                  && td.fk_change_id == activeChangeId
                                                               select
                                                                   td.fk_action_id
                                                               ).ToList();

                consequenceAdjustedBudgetDbContext.tfp_trans_detail.RemoveRange(consequenceAdjustedBudgetDbContext.tfp_trans_detail.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                    && x.budget_year == budgetYear
                                                                                    && x.fk_change_id == activeChangeId
                                                                                    && actionIdsOfActiveBudgetChangeData.Contains(x.fk_action_id)));
                await consequenceAdjustedBudgetDbContext.SaveChangesAsync();

                List<tfp_trans_detail> lstTransDetailData = (from th in consequenceAdjustedBudgetDbContext.tfp_trans_header
                                                             join td in consequenceAdjustedBudgetDbContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                                                                                                                        equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                                                             where th.fk_tenant_id == userDetails.tenant_id
                                                                   && th.action_type == 6
                                                                   && td.budget_year == budgetYear
                                                                   && td.function_code == demographicData.functionCode
                                                                   && td.department_code == demographicData.department_code
                                                                   && activeChangeId != -1
                                                                   && td.fk_change_id != activeChangeId
                                                             select td).ToList();

                foreach (var v in lstTransDetailData)
                {
                    tfp_trans_detail td = new tfp_trans_detail()
                    {
                        fk_action_id = v.fk_action_id,
                        fk_tenant_id = v.fk_tenant_id,
                        budget_year = v.budget_year,
                        fk_account_code = v.fk_account_code,
                        department_code = v.department_code,
                        function_code = v.function_code,
                        project_code = v.project_code,
                        asset_code = v.asset_code,
                        fk_investment_id = v.fk_investment_id,
                        investment_row_id = v.investment_row_id,
                        year_1_amount = v.year_1_amount * (-1),
                        year_2_amount = v.year_2_amount * (-1),
                        year_3_amount = v.year_3_amount * (-1),
                        year_4_amount = v.year_4_amount * (-1),
                        updated = DateTime.UtcNow,
                        updated_by = userDetails.pk_id,
                        fk_change_id = activeChangeId,
                        free_dim_1 = v.free_dim_1,
                        free_dim_2 = v.free_dim_2,
                        free_dim_3 = v.free_dim_3,
                        free_dim_4 = v.free_dim_4,
                        description = v.description,
                        fk_adjustment_code = string.IsNullOrEmpty(demographicData.fk_adjustment_code) ? string.Empty : demographicData.fk_adjustment_code,
                        fk_alter_code = string.IsNullOrEmpty(demographicData.fk_alter_code) ? string.Empty : demographicData.fk_alter_code,
                        fk_adj_code = string.Empty
                    };
                    consequenceAdjustedBudgetDbContext.tfp_trans_detail.Add(td);
                    await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                }

                var transDetailData = (from th in consequenceAdjustedBudgetDbContext.tfp_trans_header
                                       join td in consequenceAdjustedBudgetDbContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                                                                                                  equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                                       where th.fk_tenant_id == userDetails.tenant_id && th.action_type == 6 && td.budget_year == budgetYear && td.function_code == demographicData.functionCode
                                       && td.department_code == demographicData.department_code
                                       select new
                                       {
                                           th.pk_action_id,
                                           td.pk_id,
                                           td.year_1_amount,
                                           td.year_2_amount,
                                           td.year_3_amount,
                                           td.year_4_amount,
                                       }).ToList();

                List<int> pkIDs = (from t in transDetailData
                                   select t.pk_id).ToList();

                if (pkIDs.Count > 0)
                {
                    var data = (from t in transDetailData
                                group t by new { t.pk_action_id } into t2
                                select new
                                {
                                    t2.Key.pk_action_id,
                                    year_1_amount = t2.Sum(x => x.year_1_amount),
                                    year_2_amount = t2.Sum(x => x.year_2_amount),
                                    year_3_amount = t2.Sum(x => x.year_3_amount),
                                    year_4_amount = t2.Sum(x => x.year_4_amount),
                                }).ToList();
                    int actionId = data.FirstOrDefault().pk_action_id;

                    if (demographicData.active == true)
                    {
                        consequenceAdjustedBudgetDbContext.tfp_trans_detail.Add(
                        new tfp_trans_detail()
                        {
                            fk_action_id = actionId,
                            fk_tenant_id = userDetails.tenant_id,
                            budget_year = demographicData.BudgetYear,
                            fk_account_code = demographicData.fk_account_code,
                            department_code = demographicData.department_code,
                            function_code = demographicData.functionCode,
                            project_code = string.IsNullOrEmpty(demographicData.fk_project_code) ? string.Empty : demographicData.fk_project_code,
                            asset_code = string.Empty,
                            year_1_amount = demographicData.yearAmount1 - data.FirstOrDefault().year_1_amount,
                            year_2_amount = demographicData.yearAmount2 - data.FirstOrDefault().year_2_amount,
                            year_3_amount = demographicData.yearAmount3 - data.FirstOrDefault().year_3_amount,
                            year_4_amount = demographicData.yearAmount4 - data.FirstOrDefault().year_4_amount,
                            updated = DateTime.UtcNow,
                            updated_by = userDetails.pk_id,
                            free_dim_1 = string.IsNullOrEmpty(demographicData.free_dim_1) ? string.Empty : demographicData.free_dim_1,
                            free_dim_2 = string.IsNullOrEmpty(demographicData.free_dim_2) ? string.Empty : demographicData.free_dim_2,
                            free_dim_3 = string.IsNullOrEmpty(demographicData.free_dim_3) ? string.Empty : demographicData.free_dim_3,
                            free_dim_4 = string.IsNullOrEmpty(demographicData.free_dim_4) ? string.Empty : demographicData.free_dim_4,
                            fk_change_id = activeChangeId,
                            description = null,
                            fk_adjustment_code = string.IsNullOrEmpty(demographicData.fk_adjustment_code) ? string.Empty : demographicData.fk_adjustment_code,
                            fk_alter_code = string.IsNullOrEmpty(demographicData.fk_alter_code) ? string.Empty : demographicData.fk_alter_code,
                            fk_adj_code = string.Empty
                        });
                    }
                    else
                    {
                        int pkActionID = actionId;
                        consequenceAdjustedBudgetDbContext.tfp_trans_detail.RemoveRange(consequenceAdjustedBudgetDbContext.tfp_trans_detail.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                                                                                  && x.budget_year == budgetYear
                                                                                                                                                  && x.fk_action_id == pkActionID));
                        tfp_trans_header tfpTransHeader = consequenceAdjustedBudgetDbContext.tfp_trans_header.FirstOrDefault(x => x.pk_action_id == pkActionID);
                        if (tfpTransHeader != null)
                        {
                            consequenceAdjustedBudgetDbContext.tfp_trans_header.Remove(tfpTransHeader);
                        }
                    }
                    await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                }
                else
                {
                    if (demographicData.active == true)
                    {
                        int actionID;
                        if (consequenceAdjustedBudgetDbContext.tfp_trans_header.Any())
                        {
                            actionID = consequenceAdjustedBudgetDbContext.tfp_trans_header.Max(x => x.pk_action_id) + 1;
                        }
                        else
                        {
                            actionID = 2000;
                        }

                        consequenceAdjustedBudgetDbContext.tfp_trans_header.Add(new tfp_trans_header()
                        {
                            pk_action_id = actionID,
                            fk_tenant_id = userDetails.tenant_id,
                            line_order = 0,
                            isManuallyAdded = 0,
                            description = ((langStringValues.FirstOrDefault(v => v.Key == "new_budget_adj_for_func_code_text")).Value).LangText + " - " + demographicData.functionName,
                            consequence = string.Empty,
                            start_date = DateTime.UtcNow,
                            action_type = 6,
                            action_source = 0,
                            title = string.Empty,
                            updated = DateTime.UtcNow,
                            updated_by = userDetails.pk_id,
                            display_financial_plan_flag = false,
                            display_description_apendix_flag = false,
                            fk_cat_id = Guid.Empty,
                            financial_plan_description = string.Empty,
                        });

                        consequenceAdjustedBudgetDbContext.tfp_trans_detail.Add(
                        new tfp_trans_detail()
                        {
                            fk_action_id = actionID,
                            fk_tenant_id = userDetails.tenant_id,
                            budget_year = demographicData.BudgetYear,
                            fk_account_code = demographicData.fk_account_code,
                            department_code = demographicData.department_code,
                            function_code = demographicData.functionCode,
                            project_code = string.IsNullOrEmpty(demographicData.fk_project_code) ? string.Empty : demographicData.fk_project_code,
                            asset_code = string.Empty,
                            year_1_amount = demographicData.yearAmount1,
                            year_2_amount = demographicData.yearAmount2,
                            year_3_amount = demographicData.yearAmount3,
                            year_4_amount = demographicData.yearAmount4,
                            updated = DateTime.UtcNow,
                            updated_by = userDetails.pk_id,
                            free_dim_1 = string.IsNullOrEmpty(demographicData.free_dim_1) ? string.Empty : demographicData.free_dim_1,
                            free_dim_2 = string.IsNullOrEmpty(demographicData.free_dim_2) ? string.Empty : demographicData.free_dim_2,
                            free_dim_3 = string.IsNullOrEmpty(demographicData.free_dim_3) ? string.Empty : demographicData.free_dim_3,
                            free_dim_4 = string.IsNullOrEmpty(demographicData.free_dim_4) ? string.Empty : demographicData.free_dim_4,
                            fk_change_id = activeChangeId,
                            description = null,
                            fk_adjustment_code = string.IsNullOrEmpty(demographicData.fk_adjustment_code) ? string.Empty : demographicData.fk_adjustment_code,
                            fk_alter_code = string.IsNullOrEmpty(demographicData.fk_alter_code) ? string.Empty : demographicData.fk_alter_code,
                            fk_adj_code = string.Empty
                        });

                        await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                    }
                }
            }
            catch
            {
                throw;
            }
            return "Success";
        }

        public async Task<dynamic> GetDetailGridsTotal(string orgId, string serviceId, bool isFinancialPlan, string userID, int BudgetYear)
        {
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ConsequenceAdjustedBudget");
            Dictionary<string, clsLanguageString> langStringValuesFP = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "FinancialPlan");
            Dictionary<string, clsLanguageString> langStringValuesCommon = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "common");
            dynamic columnTitles = new JArray();
            dynamic columnFields = new JArray();
            dynamic group = new JObject();
            dynamic jsonData = new JArray();

            int? budgetYear = BudgetYear;

            var lstresult = (await (from t in consequenceAdjustedBudgetDbContext.tco_budget_totals
                                    where t.fk_tenant_id == userDetails.tenant_id
                                       //&& t.serviceAreaID == serviceAreaID
                                       && t.FINPLAN_LEVEL_1 == orgId
                                       && t.FINPLAN_LEVEL_2 == serviceId
                                       && t.budget_year == budgetYear
                                       && (t.fk_action_type == 5001 || t.fk_action_type == 5002 || t.fk_action_type == 7 || t.fk_action_type == 9 || t.fk_action_type == 6
                                           || t.fk_action_type == 31 || t.fk_action_type == 41 || t.fk_action_type == 25 || t.fk_action_type == 21)
                                    select new
                                    {
                                        t.fk_action_type,
                                        t.fin_plan_year1,
                                        t.fin_plan_year2,
                                        t.fin_plan_year3,
                                        t.fin_plan_year4,
                                        order = t.fk_action_type == 5001 ? 1 :
                                              t.fk_action_type == 5002 ? 2 :
                                              t.fk_action_type == 7 ? 3 :
                                              t.fk_action_type == 9 ? 4 :
                                              t.fk_action_type == 6 ? 5 :
                                              t.fk_action_type == 31 ? 6 :
                                              t.fk_action_type == 41 ? 7 :
                                              t.fk_action_type == 25 ? 8 :
                                              t.fk_action_type == 21 ? 9 : 0
                                    }).ToListAsync()).OrderBy(x => x.order).ToList();

            List<List<decimal>> ceoTotals = new List<List<decimal>>();

            var actionType21DataOperationalEffect = lstresult.FirstOrDefault(x => x.fk_action_type == 21);
            List<decimal> operationalEffectTotals = new List<decimal>();
            operationalEffectTotals.Add(actionType21DataOperationalEffect == null ? 0.0M : (actionType21DataOperationalEffect.fin_plan_year1 / 1000).Value);
            operationalEffectTotals.Add(actionType21DataOperationalEffect == null ? 0.0M : (actionType21DataOperationalEffect.fin_plan_year2 / 1000).Value);
            operationalEffectTotals.Add(actionType21DataOperationalEffect == null ? 0.0M : (actionType21DataOperationalEffect.fin_plan_year3 / 1000).Value);
            operationalEffectTotals.Add(actionType21DataOperationalEffect == null ? 0.0M : (actionType21DataOperationalEffect.fin_plan_year4 / 1000).Value);
            ceoTotals.Add(operationalEffectTotals);

            var costReductionTotals = lstresult.FirstOrDefault(x => x.fk_action_type == 31);
            List<decimal> lstCostRedTotals = new List<decimal>();
            lstCostRedTotals.Add(costReductionTotals == null ? 0.0M : (costReductionTotals.fin_plan_year1 / 1000).Value);
            lstCostRedTotals.Add(costReductionTotals == null ? 0.0M : (costReductionTotals.fin_plan_year2 / 1000).Value);
            lstCostRedTotals.Add(costReductionTotals == null ? 0.0M : (costReductionTotals.fin_plan_year3 / 1000).Value);
            lstCostRedTotals.Add(costReductionTotals == null ? 0.0M : (costReductionTotals.fin_plan_year4 / 1000).Value);
            ceoTotals.Add(lstCostRedTotals);

            var newPrioritiesTotals = lstresult.FirstOrDefault(x => x.fk_action_type == 41);
            List<decimal> lstnewPrioritiesTotals = new List<decimal>();
            lstnewPrioritiesTotals.Add(newPrioritiesTotals == null ? 0.0M : (newPrioritiesTotals.fin_plan_year1 / 1000).Value);
            lstnewPrioritiesTotals.Add(newPrioritiesTotals == null ? 0.0M : (newPrioritiesTotals.fin_plan_year2 / 1000).Value);
            lstnewPrioritiesTotals.Add(newPrioritiesTotals == null ? 0.0M : (newPrioritiesTotals.fin_plan_year3 / 1000).Value);
            lstnewPrioritiesTotals.Add(newPrioritiesTotals == null ? 0.0M : (newPrioritiesTotals.fin_plan_year4 / 1000).Value);
            ceoTotals.Add(lstnewPrioritiesTotals);

            var politicalProcessTotals = lstresult.FirstOrDefault(x => x.fk_action_type == 25);
            List<decimal> lstpoliticalProcessTotals = new List<decimal>();
            lstpoliticalProcessTotals.Add(politicalProcessTotals == null ? 0.0M : (politicalProcessTotals.fin_plan_year1 / 1000).Value);
            lstpoliticalProcessTotals.Add(politicalProcessTotals == null ? 0.0M : (politicalProcessTotals.fin_plan_year2 / 1000).Value);
            lstpoliticalProcessTotals.Add(politicalProcessTotals == null ? 0.0M : (politicalProcessTotals.fin_plan_year3 / 1000).Value);
            lstpoliticalProcessTotals.Add(politicalProcessTotals == null ? 0.0M : (politicalProcessTotals.fin_plan_year4 / 1000).Value);
            ceoTotals.Add(lstpoliticalProcessTotals);

            dynamic getBudgetChangesTotals = await GetActionsWithBudgetChange(userID, orgId, serviceId, "FinancialPlan", BudgetYear, true);

            columnTitles.Add("id");
            columnTitles.Add(" ");

            columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "plan_text")).Value).LangText + " " + budgetYear.ToString());
            columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "plan_text")).Value).LangText + " " + (budgetYear + 1).ToString());
            columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "plan_text")).Value).LangText + " " + (budgetYear + 2).ToString());
            columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "plan_text")).Value).LangText + " " + (budgetYear + 3).ToString());

            columnFields.Add("id");
            columnFields.Add("serviceArea");

            columnFields.Add("year1Amount");
            columnFields.Add("year2Amount");
            columnFields.Add("year3Amount");
            columnFields.Add("year4Amount");

            List<decimal> totals = new List<decimal>() { 0, 0, 0, 0 };

            foreach (var d in lstresult)
            {
                if (d.fk_action_type == 21)
                {
                    continue;
                }

                if (d.order == 6 || d.order == 7 || d.order == 8)
                {
                    if (!isFinancialPlan)
                    {
                        continue;
                    }
                }
                group = new JObject();
                group.id = d.order;
                group.serviceArea = d.order == 1 ? ((langStringValues.FirstOrDefault(v => v.Key == "original_budget_text")).Value).LangText :
                                    d.order == 2 ? ((langStringValues.FirstOrDefault(v => v.Key == "technical_adjusted_budget_total_text")).Value).LangText :
                                    d.order == 3 ? ((langStringValues.FirstOrDefault(v => v.Key == "cab_budget_adjustment")).Value).LangText :
                                    d.order == 4 ? ((langStringValues.FirstOrDefault(v => v.Key == "cab_budget_changes_financial")).Value).LangText :
                                    d.order == 5 ? ((langStringValues.FirstOrDefault(v => v.Key == "cab_budget_adjustment_financial")).Value).LangText :
                                    d.order == 6 ? ((langStringValuesFP.FirstOrDefault(v => v.Key == "fp_cost_reduction_total_text")).Value).LangText ://"Cost Reductions" :
                                    d.order == 7 ? ((langStringValuesFP.FirstOrDefault(v => v.Key == "fp_new_priorities_total_text")).Value).LangText ://"New Priorities" :
                                    d.order == 8 ? ((langStringValuesFP.FirstOrDefault(v => v.Key == "fp_political_process_text")).Value).LangText : "";//"Political Process" : "";
                group.ActionType = d.fk_action_type;
                if (d.order == 1)
                {
                    group.isBold = true;
                }
                else
                {
                    group.isBold = false;
                }
                dynamic lst = new JArray();
                dynamic gridData = new JArray();
                gridData = new JArray();
                var row = lstresult.FirstOrDefault(x => x.fk_action_type == d.fk_action_type);

                decimal value = row != null ? Convert.ToDecimal(row.fin_plan_year1) : 0;
                gridData.Add(value / 1000);
                totals[0] = totals[0] + (row != null ? row.fin_plan_year1.Value : 0);
                value = row != null ? Convert.ToDecimal(row.fin_plan_year2) : 0;
                gridData.Add(value / 1000);
                totals[1] = totals[1] + (row != null ? row.fin_plan_year2.Value : 0);
                value = row != null ? Convert.ToDecimal(row.fin_plan_year3) : 0;
                gridData.Add(value / 1000);
                totals[2] = totals[2] + (row != null ? row.fin_plan_year3.Value : 0);
                value = row != null ? Convert.ToDecimal(row.fin_plan_year4) : 0;
                gridData.Add(value / 1000);
                totals[3] = totals[3] + (row != null ? row.fin_plan_year4.Value : 0);
                lst.Add(gridData);
                group.gridData = lst;
                jsonData.Add(group);

                if (isFinancialPlan)
                {
                    if (d.order == 5)
                    {
                        group = new JObject();
                        group.id = 0;
                        group.serviceArea = ((langStringValuesCommon.FirstOrDefault(v => v.Key == "consequence_adjusted_budget_title_text")).Value).LangText;
                        group.ActionType = -1;
                        group.isBold = true;
                        lst = new JArray();
                        gridData = new JArray();
                        gridData.Add(totals[0] / 1000);
                        gridData.Add(totals[1] / 1000);
                        gridData.Add(totals[2] / 1000);
                        gridData.Add(totals[3] / 1000);
                        lst.Add(gridData);
                        group.gridData = lst;
                        jsonData.Add(group);

                        List<decimal> lstConsequenceAdjustedBudgetTotals = new List<decimal>();
                        lstConsequenceAdjustedBudgetTotals.Add(totals[0] / 1000);
                        lstConsequenceAdjustedBudgetTotals.Add(totals[1] / 1000);
                        lstConsequenceAdjustedBudgetTotals.Add(totals[2] / 1000);
                        lstConsequenceAdjustedBudgetTotals.Add(totals[3] / 1000);
                        ceoTotals.Add(lstConsequenceAdjustedBudgetTotals);

                        var actionType21Data = lstresult.FirstOrDefault(x => x.fk_action_type == 21);

                        group = new JObject();
                        group.id = 0;
                        group.serviceArea = ((langStringValuesCommon.FirstOrDefault(v => v.Key == "fp_operational_effect_text")).Value).LangText;
                        group.ActionType = actionType21Data == null ? -1 : actionType21Data.fk_action_type;
                        group.isBold = false;
                        lst = new JArray();
                        gridData = new JArray();
                        gridData.Add(actionType21Data == null ? 0 : (actionType21Data.fin_plan_year1 / 1000));
                        totals[0] = totals[0] + (actionType21Data == null ? 0 : (actionType21Data.fin_plan_year1.Value));
                        gridData.Add(actionType21Data == null ? 0 : (actionType21Data.fin_plan_year2 / 1000));
                        totals[1] = totals[1] + (actionType21Data == null ? 0 : (actionType21Data.fin_plan_year2.Value));
                        gridData.Add(actionType21Data == null ? 0 : (actionType21Data.fin_plan_year3 / 1000));
                        totals[2] = totals[2] + (actionType21Data == null ? 0 : (actionType21Data.fin_plan_year3.Value));
                        gridData.Add(actionType21Data == null ? 0 : (actionType21Data.fin_plan_year4 / 1000));
                        totals[3] = totals[3] + (actionType21Data == null ? 0 : (actionType21Data.fin_plan_year4.Value));
                        lst.Add(gridData);
                        group.gridData = lst;
                        jsonData.Add(group);
                    }
                }
            }
            dynamic lstBudgetChange = new JArray();
            dynamic gridDataBudgetChange = new JArray();
            dynamic ceoArray = new JArray();
            /*CEO proposd budget for 2016 - 2019*/
            group = new JObject();
            group.id = null;
            group.serviceArea = ((langStringValuesCommon.FirstOrDefault(v => v.Key == "cmn_ceo_proposed_bdt")).Value).LangText + " " + budgetYear.ToString() + "-" + (budgetYear + 3).ToString();//"CEO proposd budget for 2016 - 2019";
            group.isBold = true;
            decimal ceoYear1Data, ceoYear2Data, ceoYear3Data, ceoYear4Data;
            if (isFinancialPlan)
            {
                ceoYear1Data = ceoTotals[0][0] + ceoTotals[1][0] + ceoTotals[2][0] + ceoTotals[3][0] + ceoTotals[4][0];
                ceoYear2Data = ceoTotals[0][1] + ceoTotals[1][1] + ceoTotals[2][1] + ceoTotals[3][1] + ceoTotals[4][1];
                ceoYear3Data = ceoTotals[0][2] + ceoTotals[1][2] + ceoTotals[2][2] + ceoTotals[3][2] + ceoTotals[4][2];
                ceoYear4Data = ceoTotals[0][3] + ceoTotals[1][3] + ceoTotals[2][3] + ceoTotals[3][3] + ceoTotals[4][3];
            }
            else
            {
                ceoYear1Data = ceoTotals[0][0] + ceoTotals[1][0] + ceoTotals[2][0] + ceoTotals[3][0];
                ceoYear2Data = ceoTotals[0][1] + ceoTotals[1][1] + ceoTotals[2][1] + ceoTotals[3][1];
                ceoYear3Data = ceoTotals[0][2] + ceoTotals[1][2] + ceoTotals[2][2] + ceoTotals[3][2];
                ceoYear4Data = ceoTotals[0][3] + ceoTotals[1][3] + ceoTotals[2][3] + ceoTotals[3][3];
            }
            gridDataBudgetChange.Add(ceoYear1Data);
            gridDataBudgetChange.Add(ceoYear2Data);
            gridDataBudgetChange.Add(ceoYear3Data);
            gridDataBudgetChange.Add(ceoYear4Data);

            lstBudgetChange.Add(gridDataBudgetChange);
            group.gridData = lstBudgetChange;
            ceoArray.Add(group);

            foreach (var v in ceoArray)
            {
                jsonData.Add(v);
            }
            decimal allBudgetChangesYear1 = 0;
            decimal allBudgetChangesYear2 = 0;
            decimal allBudgetChangesYear3 = 0;
            decimal allBudgetChangesYear4 = 0;
            foreach (var v in getBudgetChangesTotals)
            {
                group = new JObject();
                group.id = null;
                group.serviceArea = v["approvalReference"];
                group.isBold = false;
                lstBudgetChange = new JArray();
                gridDataBudgetChange = new JArray();
                allBudgetChangesYear1 = allBudgetChangesYear1 + Convert.ToDecimal(v["year1"]);
                gridDataBudgetChange.Add(Convert.ToDecimal(v["year1"]));

                allBudgetChangesYear2 = allBudgetChangesYear2 + Convert.ToDecimal(v["year2"]);
                gridDataBudgetChange.Add(Convert.ToDecimal(v["year2"]));

                allBudgetChangesYear3 = allBudgetChangesYear3 + Convert.ToDecimal(v["year3"]);
                gridDataBudgetChange.Add(Convert.ToDecimal(v["year3"]));

                allBudgetChangesYear4 = allBudgetChangesYear4 + Convert.ToDecimal(v["year4"]);
                gridDataBudgetChange.Add(Convert.ToDecimal(v["year4"]));
                lstBudgetChange.Add(gridDataBudgetChange);
                group.gridData = lstBudgetChange;
                jsonData.Add(group);
            }

            /*Current orginal budget 2016-2019*/
            group = new JObject();
            group.id = null;
            group.serviceArea = ((langStringValuesCommon.FirstOrDefault(v => v.Key == "cmn_current_org_bdt")).Value).LangText + " " + budgetYear.ToString() + "-" + (budgetYear + 3).ToString();//"Current orginal budget 2016-2019";
            group.isBold = true;
            lstBudgetChange = new JArray();
            gridDataBudgetChange = new JArray();
            gridDataBudgetChange.Add(ceoYear1Data + (allBudgetChangesYear1));
            gridDataBudgetChange.Add(ceoYear2Data + (allBudgetChangesYear2));
            gridDataBudgetChange.Add(ceoYear3Data + (allBudgetChangesYear3));
            gridDataBudgetChange.Add(ceoYear4Data + (allBudgetChangesYear4));
            lstBudgetChange.Add(gridDataBudgetChange);
            group.gridData = lstBudgetChange;
            jsonData.Add(group);

            dynamic data = new JObject();
            data.Add("columnTitles", columnTitles);
            data.Add("columnFields", columnFields);
            data.Add("rows", jsonData);
            JObject jsonCfg = JObject.Parse(await _utility.GetApplicationSettingAsync("GetBudgetAndFinancialData_grid_config"));
            data.gridConfig = jsonCfg;
            data.groupId = 0;
            data.amountFormat = "#,##";
            return data;
        }

        public dynamic AddUpdateBudgetChanges(string UserId, BudgetChangesdata data)
        {
            var result = AddUpdateBudgetChangesAsync(UserId, data).GetAwaiter().GetResult();
            return result;
        }

        public async Task<dynamic> AddUpdateBudgetChangesAsync(string UserId, BudgetChangesdata data)
        {
            string type = string.IsNullOrEmpty(data.type) ? string.Empty : data.type;
            int changeId;
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            try
            {
                //validation before insert/Update
                dynamic validationcheck = await BudgetChangeValidation(UserId, data);
                if (validationcheck == "0")
                {
                    int budgetYear = data.BudgetYear;
                    UserData userdata = await _utility.GetUserDetailsAsync(UserId);
                    if (data.id == 0)
                    {
                        if ((data.budgetphaseid == null || data.budgetphaseid == Guid.Empty.ToString()) && (type == "budget_changes" || type == "investment_changes"))
                        {
                            dynamic lstResult = await GetBudgetPhasesAsync(UserId, 0, 0);
                            if (lstResult != null && lstResult.Data.Count > 0)
                            {
                                data.budgetphaseid = lstResult.Data[0].key;
                            }
                        }

                        //insert...
                        tfp_budget_changes tbc = new tfp_budget_changes();
                        tbc.approval_reference = data.approval_reference;
                        tbc.fk_tenant_id = userdata.tenant_id;
                        tbc.budget_year = budgetYear;
                        tbc.change_date = data.datetoSave;
                        tbc.description = data.description == null ? String.Empty : data.description;
                        tbc.status = data.status;
                        tbc.org_budget_flag = (type == "budget_changes" || type == "investment_changes") ? 0 : 1;
                        tbc.updated = DateTime.UtcNow;
                        tbc.updated_by = userdata.pk_id;
                        tbc.fk_budget_phase_id = data.budgetphaseid == null ? Guid.Empty : new Guid(data.budgetphaseid);
                        tbc.workflow_status = 1;
                        tbc.budget_name = data.budget_name == null ? String.Empty : data.budget_name;
                        tbc.year_1_amount = 0;
                        tbc.year_2_amount = 0;
                        tbc.year_3_amount = 0;
                        tbc.year_4_amount = 0;
                        tbc.rebudget_approved = data.reBudget;
                        tbc.not_approved = type == "budget_changes" ? data.unApprov : false;
                        consequenceAdjustedBudgetDbContext.tfp_budget_changes.Add(tbc);
                        await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                        changeId = tbc.pk_change_id;
                        tco_users_settings tcoUserSettings =
                            await consequenceAdjustedBudgetDbContext.tco_users_settings.FirstOrDefaultAsync(x =>
                                x.tenant_id == userdata.tenant_id &&
                                x.fk_user_id == userdata.pk_id && x.is_active_tenant);
                        if (tcoUserSettings != null)
                        {
                            if (data.type == "budget_changes" || data.type == "investment_changes")
                            {
                                tcoUserSettings.active_change_id_budget_changes = tbc.pk_change_id;
                                await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                            }
                            else
                            {
                                tcoUserSettings.active_change_id = tbc.pk_change_id;
                                await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                            }
                            await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                        }
                    }
                    else
                    {
                        //update...
                        tfp_budget_changes tbc = await consequenceAdjustedBudgetDbContext.tfp_budget_changes.Where(x => x.pk_change_id == data.id).FirstOrDefaultAsync();

                        if (data.status == 1)
                        {
                            /*coming from ui, meaning, we are activating the budget round*/
                            /*before we active the status to 1 which is coming from UI, we first have to check on if there is already any active budget change in step 3.
                             we should not allow the round to be active if another round already exist in step 20, 30 or 40*/

                            dynamic obj = await AllowToWorkflow20or30or40Async(UserId, data.id, data.BudgetYear);
                            if (Convert.ToBoolean(obj.allowToWorkflow20or30or40) == false && (tbc.workflow_status == 20 || tbc.workflow_status == 30 || tbc.workflow_status == 40))
                            {
                                return "another_change_with_step_3_exist";
                            }
                        }

                        /*before we update the status to 1 which is coming from UI, we first have to check on if there is already any active budget change in step 3*/

                        if (tbc != null)
                        {
                            tbc.fk_tenant_id = userdata.tenant_id;
                            tbc.approval_reference = data.approval_reference;
                            tbc.budget_year = budgetYear;
                            tbc.change_date = data.datetoSave;
                            tbc.description = data.description == null ? String.Empty : data.description;
                            tbc.status = data.status;
                            tbc.org_budget_flag = (type == "budget_changes" || type == "investment_changes") ? 0 : 1;
                            tbc.updated = DateTime.UtcNow;
                            tbc.updated_by = userdata.pk_id;
                            tbc.rebudget_approved = data.reBudget;
                            tbc.not_approved = type == "budget_changes" ? data.unApprov : false;
                            if (type != "investment_changes")
                            {
                                tbc.fk_budget_phase_id = data.budgetphaseid == null ? Guid.Empty : new Guid(data.budgetphaseid);
                            }
                            tbc.budget_name = data.budget_name == null ? String.Empty : data.budget_name;
                        }
                        changeId = tbc.pk_change_id;
                        await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                    }

                    //if the status has been changed to inactive, we need to update the active change id to the latest open change
                    if (data.status == 4) //closed
                    {
                        tco_users_settings userSettings = await consequenceAdjustedBudgetDbContext.tco_users_settings.FirstOrDefaultAsync(
                            x => x.fk_user_id == userdata.pk_id && x.tenant_id == userdata.tenant_id && x.is_active_tenant);

                        if (data.type == "budget_changes" || data.type == "investment_changes")
                        {
                            if (userSettings?.active_change_id_budget_changes == data.id)
                            {
                                //The current change has gone to inactive but active_change_id is set to this
                                //Find the latest change that is open
                                tfp_budget_changes latestOpenChange = await consequenceAdjustedBudgetDbContext.tfp_budget_changes
                                    .Where(x => x.fk_tenant_id == userdata.tenant_id && x.budget_year == budgetYear && x.status == 1)
                                    .OrderByDescending(y => y.change_date).FirstOrDefaultAsync();
                                if (latestOpenChange != null)
                                {
                                    userSettings.active_change_id_budget_changes = latestOpenChange.pk_change_id;
                                    await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                                }
                            }
                        }
                        else
                        {
                            if (userSettings?.active_change_id == data.id)
                            {
                                //The current change has gone to inactive but active_change_id is set to this
                                //Find the latest change that is open
                                tfp_budget_changes latestOpenChange = await consequenceAdjustedBudgetDbContext.tfp_budget_changes
                                    .Where(x => x.fk_tenant_id == userdata.tenant_id && x.budget_year == budgetYear && x.status == 1)
                                    .OrderByDescending(y => y.change_date).FirstOrDefaultAsync();
                                if (latestOpenChange != null)
                                {
                                    userSettings.active_change_id = latestOpenChange.pk_change_id;
                                    await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                                }
                            }
                        }
                    }
                    if (data.pageId == "BudgetChanges")
                    {
                        await UpdateUserAdjustmentCode(changeId, data.unApprov, userdata.tenant_id);
                    }
                    if (data.pageId == "aFinancing")//angular financing screen
                    {
                        if (data.isActive)
                        {
                            await SaveActiveChangeAsync(UserId, changeId, data.pageId, budgetYear);
                        }
                        else
                        {
                            tco_users_settings userSettings =
                                await consequenceAdjustedBudgetDbContext.tco_users_settings.FirstOrDefaultAsync(x =>
                                    x.tenant_id == userdata.tenant_id &&
                                    x.fk_user_id == userdata.pk_id && x.is_active_tenant);
                            if (userSettings.active_change_id == data.id)
                            {
                                userSettings.active_change_id = null;
                            }
                        }
                    }
                    return "success";
                }
                else
                {
                    return validationcheck;
                }
            }
            catch
            {
                throw;
            }
        }

        private async Task UpdateUserAdjustmentCode(int changeId, bool notApproved, int tenantId)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            var AdjCodeList = await dbContext.tco_user_adjustment_codes.Where(x => x.fk_tenant_id == tenantId && x.fk_change_id == changeId).ToListAsync();
            AdjCodeList.ForEach(x => { x.status = !notApproved; });
            await dbContext.SaveChangesAsync();
        }

        private async Task<dynamic> BudgetChangeValidation(string UserId, BudgetChangesdata data)
        {
            ////Validate refercence id is uniq
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            int budgetYear = data.BudgetYear;
            UserData userdata = await _utility.GetUserDetailsAsync(UserId);
            Dictionary<string, clsLanguageString> langStringValuesFP = await _utility.GetLanguageStringsAsync(userdata.language_preference, userdata.user_name, "FinancialPlan");
            if (data.id == 0)
            {
                var budgetchangedata = await (from bc in consequenceAdjustedBudgetDbContext.vwUserDetails
                                              where bc.pk_id == -1 && bc.tenant_id == userdata.tenant_id && bc.client_id == userdata.client_id
                                              select new
                                              {
                                                  pk_change_id = 0,
                                                  approval_reference = string.Empty,
                                                  fk_tenant_id = 0,
                                                  budget_year = 0,
                                                  change_date = DateTime.UtcNow,
                                                  description = string.Empty,
                                                  status = 0,
                                                  org_budget_flag = 0,
                                                  updated = DateTime.UtcNow,
                                                  updated_by = 0
                                              }).ToListAsync();

                var budgetchangedataactive = await (from bc in consequenceAdjustedBudgetDbContext.vwUserDetails
                                                    where bc.pk_id == -1 && bc.tenant_id == userdata.tenant_id && bc.client_id == userdata.client_id
                                                    select new
                                                    {
                                                        pk_change_id = 0,
                                                        approval_reference = string.Empty,
                                                        fk_tenant_id = 0,
                                                        budget_year = 0,
                                                        change_date = DateTime.UtcNow,
                                                        description = string.Empty,
                                                        status = 0,
                                                        org_budget_flag = 0,
                                                        updated = DateTime.UtcNow,
                                                        updated_by = 0
                                                    }).ToListAsync();

                var budgetchangedataCheckOnApprovalReference = await (from bc in consequenceAdjustedBudgetDbContext.tfp_budget_changes
                                                                      where bc.fk_tenant_id == userdata.tenant_id && bc.budget_year == budgetYear && bc.approval_reference.Trim() == data.approval_reference.Trim()
                                                                      select new
                                                                      {
                                                                          pk_change_id = 0,
                                                                          approval_reference = string.Empty,
                                                                          fk_tenant_id = 0,
                                                                          budget_year = 0,
                                                                          change_date = DateTime.UtcNow,
                                                                          description = string.Empty,
                                                                          status = 0,
                                                                          org_budget_flag = 0,
                                                                          updated = DateTime.UtcNow,
                                                                          updated_by = 0
                                                                      }).ToListAsync();

                if (budgetchangedataCheckOnApprovalReference.Any())
                {
                    return (langStringValuesFP["FP_refaproval_msg"]).LangText;
                }

                if (!string.IsNullOrEmpty(data.type) && (data.type == "budget_changes" || data.type == "investment_changes"))
                {
                    budgetchangedata = await (from bc in consequenceAdjustedBudgetDbContext.tfp_budget_changes
                                              where bc.fk_tenant_id == userdata.tenant_id && bc.budget_year == budgetYear && bc.approval_reference.Trim() == data.approval_reference.Trim() && bc.org_budget_flag == 0
                                              select new
                                              {
                                                  pk_change_id = 0,
                                                  approval_reference = string.Empty,
                                                  fk_tenant_id = 0,
                                                  budget_year = 0,
                                                  change_date = DateTime.UtcNow,
                                                  description = string.Empty,
                                                  status = 0,
                                                  org_budget_flag = 0,
                                                  updated = DateTime.UtcNow,
                                                  updated_by = 0
                                              }).ToListAsync();
                }
                else
                {
                    budgetchangedata = await (from bc in consequenceAdjustedBudgetDbContext.tfp_budget_changes
                                              where bc.fk_tenant_id == userdata.tenant_id && bc.budget_year == budgetYear && bc.approval_reference.Trim() == data.approval_reference.Trim() && bc.org_budget_flag == 1
                                              select new
                                              {
                                                  pk_change_id = 0,
                                                  approval_reference = string.Empty,
                                                  fk_tenant_id = 0,
                                                  budget_year = 0,
                                                  change_date = DateTime.UtcNow,
                                                  description = string.Empty,
                                                  status = 0,
                                                  org_budget_flag = 0,
                                                  updated = DateTime.UtcNow,
                                                  updated_by = 0
                                              }).ToListAsync();
                }

                int statuscheck = 0;

                if (budgetchangedataactive.Count == 1 && data.status == 1)
                {
                    statuscheck = 1;
                }

                if (budgetchangedata.Count == 0 && statuscheck == 0)
                {
                    return "0";
                }
                else
                {
                    string msg = string.Empty;
                    if (budgetchangedata.Count > 0 && statuscheck == 1)
                    {
                        return (langStringValuesFP["FP_refaproval_active_msg"]).LangText;
                    }
                    if (budgetchangedata.Count > 0)
                    {
                        return (langStringValuesFP["FP_refaproval_msg"]).LangText;
                    }
                    if (statuscheck == 1)
                    {
                        return (langStringValuesFP["FP_active_msg"]).LangText;
                    }
                    return msg;
                }
            }
            else
            {    //Update validation
                var budgetchangedata = await (from bc in consequenceAdjustedBudgetDbContext.vwUserDetails
                                              where bc.pk_id == -1 && bc.tenant_id == userdata.tenant_id && bc.client_id == userdata.client_id
                                              select new
                                              {
                                                  pk_change_id = 0,
                                                  approval_reference = string.Empty,
                                                  fk_tenant_id = 0,
                                                  budget_year = 0,
                                                  change_date = DateTime.UtcNow,
                                                  description = string.Empty,
                                                  status = 0,
                                                  org_budget_flag = 0,
                                                  updated = DateTime.UtcNow,
                                                  updated_by = 0
                                              }).ToListAsync();

                var budgetchangedataactive = await (from bc in consequenceAdjustedBudgetDbContext.vwUserDetails
                                                    where bc.pk_id == -1 && bc.tenant_id == userdata.tenant_id && bc.client_id == userdata.client_id
                                                    select new
                                                    {
                                                        pk_change_id = 0,
                                                        approval_reference = string.Empty,
                                                        fk_tenant_id = 0,
                                                        budget_year = 0,
                                                        change_date = DateTime.UtcNow,
                                                        description = string.Empty,
                                                        status = 0,
                                                        org_budget_flag = 0,
                                                        updated = DateTime.UtcNow,
                                                        updated_by = 0
                                                    }).ToListAsync();
                if (!string.IsNullOrEmpty(data.type) && (data.type == "budget_changes" || data.type == "investment_changes"))
                {
                    budgetchangedata = await (from bc in consequenceAdjustedBudgetDbContext.tfp_budget_changes
                                              where bc.fk_tenant_id == userdata.tenant_id && bc.pk_change_id == data.id && bc.org_budget_flag == 0
                                              select new
                                              {
                                                  pk_change_id = 0,
                                                  approval_reference = string.Empty,
                                                  fk_tenant_id = 0,
                                                  budget_year = 0,
                                                  change_date = DateTime.UtcNow,
                                                  description = string.Empty,
                                                  status = 0,
                                                  org_budget_flag = 0,
                                                  updated = DateTime.UtcNow,
                                                  updated_by = 0
                                              }).ToListAsync();
                }
                else
                {
                    budgetchangedata = await (from bc in consequenceAdjustedBudgetDbContext.tfp_budget_changes
                                              where bc.fk_tenant_id == userdata.tenant_id && bc.pk_change_id == data.id && bc.org_budget_flag == 1
                                              select new
                                              {
                                                  pk_change_id = 0,
                                                  approval_reference = string.Empty,
                                                  fk_tenant_id = 0,
                                                  budget_year = 0,
                                                  change_date = DateTime.UtcNow,
                                                  description = string.Empty,
                                                  status = 0,
                                                  org_budget_flag = 0,
                                                  updated = DateTime.UtcNow,
                                                  updated_by = 0
                                              }).ToListAsync();
                }

                int actcheck = 0;
                if (budgetchangedata[0].approval_reference == data.approval_reference)
                {
                    //no need to validate
                }
                else
                {
                    //validate approval referrance
                    var budgetchangedata1 = await consequenceAdjustedBudgetDbContext.tfp_budget_changes.Where(x => x.pk_change_id != data.id
                                                                                                          && x.approval_reference == data.approval_reference
                                                                                                          && x.budget_year == budgetYear
                                                                                                          && x.fk_tenant_id == userdata.tenant_id).ToListAsync();
                    if (budgetchangedata1.Count > 0)
                    {
                        actcheck = 1;
                    }

                    var selectedBudgetRound = await consequenceAdjustedBudgetDbContext.tfp_budget_changes.Where(x => x.pk_change_id == data.id
                                                                                                         && x.budget_year == budgetYear
                                                                                                         && x.fk_tenant_id == userdata.tenant_id).ToListAsync();

                    if (budgetchangedata1.Count == 0 && selectedBudgetRound.FirstOrDefault().status == data.status)
                    {
                        return "0";
                    }
                }

                int statuscheck = 0;
                if (budgetchangedataactive.Count == 1 && data.status == 1 && budgetchangedata[0].status != 1)
                {
                    statuscheck = 1;
                }

                if (actcheck == 0 && statuscheck == 0)
                {
                    return "0";
                }
                else
                {
                    string msg = string.Empty;
                    if (actcheck == 1 && statuscheck == 1)
                    {
                        return (langStringValuesFP["FP_refaproval_active_msg"]).LangText;
                    }
                    if (actcheck == 1)
                    {
                        return (langStringValuesFP["FP_refaproval_msg"]).LangText;
                    }
                    if (statuscheck == 1)
                    {
                        return (langStringValuesFP["FP_active_msg"]).LangText;
                    }
                    return msg;
                }
            }
        }

        public dynamic GetBudgetChangesData(string UserId, string type, string budgetphaseid, int BudgetYear,
            bool isBudAss)
        {
            var result = GetBudgetChangesDataAsync(UserId, type, budgetphaseid,
                BudgetYear, isBudAss).GetAwaiter().GetResult();
            return result;
        }

        public async Task<dynamic> GetBudgetChangesDataAsync(string UserId, string type, string budgetphaseid,
            int BudgetYear, bool isBudAss)
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendLine($"1 @ {DateTime.UtcNow}");
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            int budgetYear = BudgetYear;
            UserData userdata = await _utility.GetUserDetailsAsync(UserId);
            tco_users_settings userSettings =
                await consequenceAdjustedBudgetDbContext.tco_users_settings.FirstOrDefaultAsync(x =>
                    x.tenant_id == userdata.tenant_id && x.fk_user_id == userdata.pk_id && x.is_active_tenant);
            sb.AppendLine($"2 @ {DateTime.UtcNow}");
            Dictionary<string, clsLanguageString> langStringValuesFP = await _utility.GetLanguageStringsAsync(userdata.language_preference, userdata.user_name, "FinancialPlan");
            Dictionary<string, clsLanguageString> langStringValuesBAtype = new Dictionary<string, clsLanguageString>();
            langStringValuesBAtype = await _utility.GetLanguageStringsAsync(userdata.language_preference, userdata.user_name, "BudgetManagement");
            string active = (langStringValuesBAtype["BM_Active"]).LangText;
            sb.AppendLine($"3 @ {DateTime.UtcNow}");
            string approved = (langStringValuesBAtype["BM_Approved"]).LangText;
            Dictionary<int, string> workflowStatusFormatted = new Dictionary<int, string>
            {
                { 0, "0" + " - invalid"},
                { 1, "1" + " - " + (langStringValuesBAtype["WorkFlow_Step1_title"]).LangText },
                { 20, "2" + " - " + (langStringValuesBAtype["WorkFlow_Step2_title"]).LangText },
                { 30, "3" + " - " + (langStringValuesBAtype["WorkFlow_Step3_title"]).LangText },
                { 40, "4" + " - " + (langStringValuesBAtype["WorkFlow_Step4_title"]).LangText }
            };
            if (budgetphaseid == null) { budgetphaseid = "All"; }
            var data = new List<clsBudgetChanges>();

            if (!string.IsNullOrEmpty(type) && (type.ToLower() == "budget_changes" || type.ToLower() == "investment_changes"))
            {
                sb.AppendLine($"4 @ {DateTime.UtcNow}");
                data = await (from tfpbc in consequenceAdjustedBudgetDbContext.tfp_budget_changes
                              join tbf in consequenceAdjustedBudgetDbContext.tco_budget_phase on tfpbc.fk_budget_phase_id equals tbf.pk_budget_phase_id into bgrp
                              from tbfGrp in bgrp.DefaultIfEmpty()
                              where tfpbc.fk_tenant_id == userdata.tenant_id && tfpbc.budget_year == budgetYear && tfpbc.org_budget_flag == 0
                              orderby tfpbc.change_date ascending
                              select new clsBudgetChanges
                              {
                                  id = tfpbc.pk_change_id,
                                  budget_change = tfpbc.budget_name,
                                  approval_reference = tfpbc.approval_reference,
                                  date = tfpbc.change_date,
                                  statusVal = tfpbc.status,
                                  status = tfpbc.status == 1 ? active : approved,
                                  isActive = 0,
                                  workflowStatus = tfpbc.workflow_status,
                                  statusCode = tfpbc.status,
                                  isSelected = tfpbc.is_selected,
                                  lockStatus = (LockStatusSync)tfpbc.lock_status,
                                  budgetphaseName = tbfGrp == null ? string.Empty : tbfGrp.description,
                                  description = tfpbc.description
                              }).ToListAsync();
                sb.AppendLine($"5 @ {DateTime.UtcNow}");
            }
            else
            {
                if (budgetphaseid == "All")
                {
                    sb.AppendLine($"6 @ {DateTime.UtcNow}");
                    data = await (from tfpbc in consequenceAdjustedBudgetDbContext.tfp_budget_changes
                                  join tbf in consequenceAdjustedBudgetDbContext.tco_budget_phase on tfpbc.fk_budget_phase_id equals tbf.pk_budget_phase_id into bgrp
                                  from tbfGrp in bgrp.DefaultIfEmpty()
                                  where tfpbc.fk_tenant_id == userdata.tenant_id && tfpbc.budget_year == budgetYear && tfpbc.org_budget_flag == 1
                                  orderby tfpbc.change_date ascending
                                  select new clsBudgetChanges
                                  {
                                      id = tfpbc.pk_change_id,
                                      budget_change = tfpbc.budget_name,
                                      approval_reference = tfpbc.approval_reference,
                                      date = tfpbc.change_date,
                                      statusVal = tfpbc.status,
                                      status = tfpbc.status == 1 ? active : approved,
                                      //isActive = userSettings == null ? 0 : userSettings.active_change_id == null ? 0 : userSettings.active_change_id == tfpbc.pk_change_id ? 1 : 0,
                                      isActive = 0,
                                      workflowStatus = tfpbc.workflow_status,
                                      statusCode = tfpbc.status,
                                      isSelected = tfpbc.is_selected,
                                      lockStatus = (LockStatusSync)tfpbc.lock_status,

                                      budgetphaseName = tbfGrp == null ? string.Empty : tbfGrp.description,
                                      description = tfpbc.description
                                  }).ToListAsync();
                    sb.AppendLine($"7 @ {DateTime.UtcNow}");
                }
                else
                {
                    sb.AppendLine($"8 @ {DateTime.UtcNow}");
                    Guid bdtphid = new Guid(budgetphaseid);
                    data = await (from tfpbc in consequenceAdjustedBudgetDbContext.tfp_budget_changes
                                  join tbf in consequenceAdjustedBudgetDbContext.tco_budget_phase on tfpbc.fk_budget_phase_id equals tbf.pk_budget_phase_id into bgrp
                                  from tbfGrp in bgrp.DefaultIfEmpty()
                                  where tfpbc.fk_tenant_id == userdata.tenant_id && tfpbc.budget_year == budgetYear && tfpbc.org_budget_flag == 1 && tfpbc.fk_budget_phase_id == bdtphid
                                  orderby tfpbc.change_date ascending
                                  select new clsBudgetChanges
                                  {
                                      id = tfpbc.pk_change_id,
                                      budget_change = tfpbc.budget_name,
                                      approval_reference = tfpbc.approval_reference,
                                      date = tfpbc.change_date,
                                      statusVal = tfpbc.status,
                                      status = tfpbc.status == 1 ? active : approved,
                                      //isActive = userSettings == null ? 0 : userSettings.active_change_id == null ? 0 : userSettings.active_change_id == tfpbc.pk_change_id ? 1 : 0,
                                      isActive = 0,
                                      workflowStatus = tfpbc.workflow_status,
                                      statusCode = tfpbc.status,
                                      isSelected = tfpbc.is_selected,
                                      lockStatus = (LockStatusSync)tfpbc.lock_status,

                                      budgetphaseName = tbfGrp == null ? string.Empty : tbfGrp.description,
                                      description = tfpbc.description
                                  }).ToListAsync();
                    sb.AppendLine($"9 @ {DateTime.UtcNow}");
                }
            }
            sb.AppendLine($"10 @ {DateTime.UtcNow}");
            if (!string.IsNullOrEmpty(type) && (type.ToLower() == "budget_changes" || type.ToLower() == "investment_changes"))
            {
                data.ForEach(x =>
                {
                    x.isActive = userSettings == null ? 0 : userSettings.active_change_id_budget_changes == null ? 0 : userSettings.active_change_id_budget_changes == x.id ? 1 : 0;
                });
            }
            else if (isBudAss)
            {
                data.ForEach(x =>
                {
                    x.isActive = userSettings == null ? 0 : userSettings.active_change_id_budget_assumption == null ? 0 : userSettings.active_change_id_budget_assumption == x.id ? 1 : 0;
                });
            }
            else
            {
                data.ForEach(x =>
                {
                    x.isActive = userSettings == null ? 0 : userSettings.active_change_id == null ? 0 : userSettings.active_change_id == x.id ? 1 : 0; // bud management overview
                    x.workflowStatusDescription = workflowStatusFormatted[x.workflowStatus];
                });
            }
            sb.AppendLine($"11 @ {DateTime.UtcNow}");
            if (data.Where(x => x.isActive == 1).Count() == 0)
            {
                sb.AppendLine($"12 @ {DateTime.UtcNow}");
                int activeChangeId = -1;
                if (!string.IsNullOrEmpty(type) && (type.ToLower() == "budget_changes" || type.ToLower() == "investment_changes"))
                {
                    var activedata = await consequenceAdjustedBudgetDbContext.tfp_budget_changes.Where(x => x.fk_tenant_id == userdata.tenant_id
                                                                                                    && x.budget_year == budgetYear
                                                                                                    && x.org_budget_flag == 0
                                                                                                    && x.status == 1) // for BudgetManagementOverview screen
                                                                                                    .OrderByDescending(y => y.pk_change_id)
                                                                                                    .ToListAsync();

                    if (activedata.Count() > 0)
                    {
                        activeChangeId = activedata.FirstOrDefault().pk_change_id;
                    }
                    data.ForEach(x =>
                    {
                        if (x.id == activeChangeId)
                        {
                            x.isActive = 1;
                        }
                    });
                    sb.AppendLine($"13 @ {DateTime.UtcNow}");
                }
                else
                {
                    sb.AppendLine($"14 @ {DateTime.UtcNow}");
                    var activedata = await consequenceAdjustedBudgetDbContext.tfp_budget_changes.Where(x => x.fk_tenant_id == userdata.tenant_id
                                                                                                && x.budget_year == budgetYear
                                                                                                && x.org_budget_flag == 1
                                                                                                && x.status == 1)
                                                                                                .OrderByDescending(y => y.pk_change_id) // for BudgetManagementOverview screen
                                                                                                .ToListAsync();

                    if (activedata.Count() > 0)
                    {
                        activeChangeId = activedata.FirstOrDefault().pk_change_id;
                    }
                    data.ForEach(x =>
                    {
                        if (x.id == activeChangeId)
                        {
                            x.isActive = 1;
                        }
                    });
                    sb.AppendLine($"15 @ {DateTime.UtcNow}");
                }
                if (activeChangeId != -1)
                {
                    sb.AppendLine($"16 @ {DateTime.UtcNow}");
                    tco_users_settings tcoUserSettings =
                        await consequenceAdjustedBudgetDbContext.tco_users_settings.FirstOrDefaultAsync(x =>
                            x.tenant_id == userdata.tenant_id &&
                            x.fk_user_id == userdata.pk_id && x.is_active_tenant);
                    if (tcoUserSettings != null)
                    {
                        if (!string.IsNullOrEmpty(type) && (type.ToLower() == "budget_changes" || type.ToLower() == "investment_changes"))
                        {
                            tcoUserSettings.active_change_id_budget_changes = activeChangeId;
                        }
                        else
                        {
                            tcoUserSettings.active_change_id = activeChangeId;
                        }
                        await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                    }
                    sb.AppendLine($"17 @ {DateTime.UtcNow}");
                }
            }

            dynamic JGetBudgetMetings = new JObject();
            dynamic titlesArray = new JArray();
            titlesArray.Add("");
            titlesArray.Add((langStringValuesFP["fp_budget_change"]).LangText);
            titlesArray.Add((langStringValuesFP["FP_approval_reference"]).LangText);
            titlesArray.Add((langStringValuesFP["FP_budgetchange_date"]).LangText);
            titlesArray.Add((langStringValuesFP["FP_budgetchange_status"]).LangText);
            titlesArray.Add((langStringValuesFP["cmn_budget_changes_isActive"]).LangText);
            if (isBudAss)
            {
                titlesArray.Add((langStringValuesFP["cmn_budget_changes_isSelected"]).LangText);
            }
            if (!string.IsNullOrEmpty(type) && (type.ToLower() == "budget_management"))
            {
                titlesArray.Add((langStringValuesFP["cmn_budget_changes_docVersion"]).LangText);
                titlesArray.Add((langStringValuesFP["cmn_budget_changes_workflowStatus"]).LangText);
                titlesArray.Add((langStringValuesFP["cmn_budget_changes_description"]).LangText);
                titlesArray.Add((langStringValuesFP["budget_changes_changeId"]).LangText);
            }
            //if (!string.IsNullOrEmpty(type) && type.ToLower() == "investment_changes")
            //{
            //    titlesArray.Add((langStringValuesFP["FP_budgetchange_rebudget"]).LangText);
            //}
            dynamic fieldsArray = new JArray();
            fieldsArray.Add("id");
            fieldsArray.Add("budget_change");
            fieldsArray.Add("approval_reference");
            fieldsArray.Add("date");
            fieldsArray.Add("status");
            fieldsArray.Add("isActive");
            if (isBudAss)
            {
                fieldsArray.Add("isSelected");
            }
            if (!string.IsNullOrEmpty(type) && (type.ToLower() == "budget_management"))
            {
                fieldsArray.Add("budgetphaseName");
                fieldsArray.Add("workflowStatus");
                fieldsArray.Add("description");
                fieldsArray.Add("change_id");
            }
            //if (!string.IsNullOrEmpty(type) && type.ToLower() == "investment_changes")
            //{
            //    fieldsArray.Add("rebudget_flag");
            //}
            JGetBudgetMetings.Add("Fields", fieldsArray);
            JGetBudgetMetings.Add("Titles", titlesArray);
            bool isactivexist = false;
            int org_flag;
            if (!string.IsNullOrEmpty(type) && (type.ToLower() == "budget_changes" || type.ToLower() == "investment_changes"))
            {
                org_flag = 0;
                var activedata = await consequenceAdjustedBudgetDbContext.tfp_budget_changes.Where(x => x.fk_tenant_id == userdata.tenant_id
                                                                                                && x.budget_year == budgetYear
                                                                                                && x.status == 1
                                                                                                && x.org_budget_flag == org_flag).ToListAsync();
                if (activedata.Count > 0)
                {
                    isactivexist = true;
                }
            }
            else
            {
                isactivexist = false;
            }

            dynamic dataArray = new JArray();

            sb.AppendLine($"18 @ {DateTime.UtcNow}");

            if (isBudAss)
            {
                data = data.OrderBy(j => j.approval_reference).ToList();
                var item = data.FirstOrDefault();
                if (item != null && !item.isSelected && !data.Any(x => x.isSelected))
                {
                    var row_data = await consequenceAdjustedBudgetDbContext.tfp_budget_changes.FirstOrDefaultAsync(x => x.fk_tenant_id == userdata.tenant_id && x.budget_year == budgetYear && x.pk_change_id == item.id);
                    if (row_data != null)
                    {
                        row_data.is_selected = true;
                        row_data.updated_by = userdata.pk_id;
                        row_data.updated = DateTime.UtcNow;
                        await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                        data.FirstOrDefault().isSelected = true;
                    }
                }
            }

            sb.AppendLine($"19 @ {DateTime.UtcNow}");

            data = data.OrderBy(x => x.approval_reference).ToList();
            foreach (var d in data)
            {
                dynamic obj = new JObject();
                obj.id = d.id;
                obj.budget_change = d.budget_change;
                obj.approval_reference = d.approval_reference;
                obj.date = d.date;
                obj.status = d.status;
                obj.statusCode = d.statusCode;
                obj.isActive = d.isActive;
                obj.isSelected = d.isSelected;
                obj.rebudget_flag = d.rebudget_approved;

                if (!string.IsNullOrEmpty(type) && (type.ToLower() == "budget_management"))
                {
                    obj.budgetphaseName = d.budgetphaseName;
                    obj.description = d.description;
                    obj.workflowStatus = d.workflowStatusDescription;
                }

                obj.lockStatus = d.lockStatus;
                dataArray.Add(obj);
            }
            sb.AppendLine($"20 @ {DateTime.UtcNow}");
            JGetBudgetMetings.Add("Data", dataArray);
            JGetBudgetMetings.Add("IsActiveExists", isactivexist);
            JGetBudgetMetings.Add("allowToWorkflow20or30or40", data.Count(x => x.statusVal == 1 && (x.workflowStatus == 20 || x.workflowStatus == 30 || x.workflowStatus == 40)) > 0 ? false : true);
            JGetBudgetMetings.performanceLog = sb.ToString();
            return JGetBudgetMetings;
        }

        public string SaveActiveChange(string userId, int changeId, string pageId, int BudgetYear)
        {
            var result = SaveActiveChangeAsync(userId, changeId, pageId, BudgetYear).GetAwaiter().GetResult();
            return result;
        }

        public async Task<string> SaveActiveChangeAsync(string userId, int changeId, string pageId, int BudgetYear)
        {
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            UserData userdata = await _utility.GetUserDetailsAsync(userId);
            int budgetYear = BudgetYear;

            List<tfp_budget_changes> data = new List<tfp_budget_changes>();

            data = await consequenceAdjustedBudgetDbContext.tfp_budget_changes.Where(x =>
                x.fk_tenant_id == userdata.tenant_id &&
                x.budget_year == budgetYear && x.pk_change_id == changeId).ToListAsync();

            if (data.Count() > 0 && data.FirstOrDefault(x => x.pk_change_id == changeId) != null)
            {
                tco_users_settings userSettings =
                    await consequenceAdjustedBudgetDbContext.tco_users_settings.FirstOrDefaultAsync(x =>
                        x.tenant_id == userdata.tenant_id &&
                        x.fk_user_id == userdata.pk_id && x.is_active_tenant);
                if (userSettings != null)
                {
                    if (pageId == "budget_changes" || pageId == "YB_INVESTMENT" || pageId == "YB_FINANCING")
                    {
                        userSettings.active_change_id_budget_changes = changeId;
                    }
                    else if (pageId == "bud_ass")
                    {
                        userSettings.active_change_id_budget_assumption = changeId;
                    }
                    else
                    {
                        userSettings.active_change_id = changeId;
                    }
                    await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                }
            }

            return "success";
        }

        public dynamic GetBudgetChangesDataById(string UserId, int Id, string type, int BudgetYear)
        {
            var result = GetBudgetChangesDataByIdAsync(UserId, Id, type, BudgetYear).GetAwaiter().GetResult();
            return result;
        }

        public async Task<dynamic> GetBudgetChangesDataByIdAsync(string UserId, int Id, string type, int BudgetYear)
        {
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            int budgetYear = BudgetYear;
            UserData userdata = await _utility.GetUserDetailsAsync(UserId);

            var data = await (from d in consequenceAdjustedBudgetDbContext.vwUserDetails
                              where d.pk_id == -1 && d.tenant_id == userdata.tenant_id && d.client_id == userdata.client_id
                              select new
                              {
                                  Id = 0,
                                  date = DateTime.UtcNow,
                                  approval_reference = string.Empty,
                                  description = string.Empty,
                                  status = 0,
                                  budgetphaseid = Guid.Empty,
                                  budget_name = string.Empty,
                                  rebudget_approved = false,
                                  unApprov = false,
                                  lockStatus = (int)LockStatusSync.Unlocked
                              }).ToListAsync();

            if (!string.IsNullOrEmpty(type) && type.ToLower() == "budget_changes")
            {
                data = await (from a in consequenceAdjustedBudgetDbContext.tfp_budget_changes
                              where a.fk_tenant_id == userdata.tenant_id && a.budget_year == budgetYear && a.pk_change_id == Id && a.org_budget_flag == 0
                              select new
                              {
                                  Id = a.pk_change_id,
                                  date = a.change_date,
                                  approval_reference = a.approval_reference,
                                  description = a.description,
                                  status = a.status,
                                  budgetphaseid = a.fk_budget_phase_id,
                                  budget_name = a.budget_name,
                                  rebudget_approved = a.rebudget_approved,
                                  unApprov = a.not_approved,
                                  lockStatus = a.lock_status

                              }).ToListAsync();
            }
            else
            {
                data = await (from a in consequenceAdjustedBudgetDbContext.tfp_budget_changes
                              where a.fk_tenant_id == userdata.tenant_id && a.budget_year == budgetYear && a.pk_change_id == Id && a.org_budget_flag == 1
                              select new
                              {
                                  Id = a.pk_change_id,
                                  date = a.change_date,
                                  approval_reference = a.approval_reference,
                                  description = a.description,
                                  status = a.status,
                                  budgetphaseid = a.fk_budget_phase_id,
                                  budget_name = a.budget_name,
                                  rebudget_approved = a.rebudget_approved,
                                  unApprov = a.not_approved,
                                  lockStatus = a.lock_status
                              }).ToListAsync();
            }

            dynamic JGetresult = new JObject();
            dynamic dataArray = JArray.FromObject(data);
            JGetresult.Add("Data", dataArray);
            string serializedObj = JsonConvert.SerializeObject(JGetresult);
            return serializedObj.ToString();
        }

        public dynamic BudgetChangeCheck(string UserId, int BudgetYear, string pageId = null)
        {
            var result = BudgetChangeCheckAsync(UserId, BudgetYear, pageId).GetAwaiter().GetResult();
            return result;
        }

        public async Task<dynamic> BudgetChangeCheckAsync(string UserId, int BudgetYear, string pageId = null)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(UserId);
            tco_users_settings userSettings = await tenantDbContext.tco_users_settings.FirstOrDefaultAsync(x =>
                x.fk_user_id == userDetails.pk_id && x.tenant_id == userDetails.tenant_id && x.is_active_tenant);
            int budgetYear = BudgetYear;
            UserData userdata = await _utility.GetUserDetailsAsync(UserId);
            if (pageId != null && pageId == "BudgetChanges")
            {
                int activeChangeId = -1;
                if (userSettings != null && userSettings.active_change_id_budget_changes != null)
                {
                    var dataActiveBudgetChanges = await (from t in tenantDbContext.tfp_budget_changes
                                                         where t.fk_tenant_id == userDetails.tenant_id
                                                            && t.budget_year == budgetYear
                                                            && t.org_budget_flag == 0
                                                         orderby t.change_date descending
                                                         select t).ToListAsync();

                    activeChangeId = dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == userSettings.active_change_id_budget_changes) != null ? dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == userSettings.active_change_id_budget_changes).pk_change_id : (dataActiveBudgetChanges.Count == 1 ? dataActiveBudgetChanges[0].pk_change_id : 0);
                }
                else
                {
                    var dataActiveBudgetChanges = await (from t in tenantDbContext.tfp_budget_changes
                                                         where t.fk_tenant_id == userDetails.tenant_id
                                                            && t.budget_year == budgetYear
                                                            && t.status == 1
                                                            && t.org_budget_flag == 0
                                                         orderby t.change_date descending
                                                         select t).ToListAsync();
                    if (dataActiveBudgetChanges.Count != 0)
                    {
                        activeChangeId = dataActiveBudgetChanges.FirstOrDefault().pk_change_id;
                    }
                }

                dynamic data = await tenantDbContext.tfp_budget_changes.Where(x => x.fk_tenant_id == userdata.tenant_id && x.budget_year == budgetYear && x.pk_change_id == activeChangeId && x.status == 1 && x.org_budget_flag == 0).MaxAsync(x => (Int32?)(x.pk_change_id)) ?? 0;
                if (data != 0)
                {
                    return "true";
                }
                else
                {
                    return "false";
                }
            }
            else
            {
                int activeChangeId = -1;
                if (userSettings != null && userSettings.active_change_id != null)
                {
                    var dataActiveBudgetChanges = await (from t in tenantDbContext.tfp_budget_changes
                                                         where t.fk_tenant_id == userDetails.tenant_id
                                                            && t.budget_year == budgetYear
                                                            && t.org_budget_flag == 1
                                                         orderby t.change_date descending
                                                         select t).ToListAsync();

                    activeChangeId = dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == userSettings.active_change_id) != null ? dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == userSettings.active_change_id).pk_change_id : (dataActiveBudgetChanges.Count == 1 ? dataActiveBudgetChanges[0].pk_change_id : 0);
                }
                else
                {
                    var dataActiveBudgetChanges = await (from t in tenantDbContext.tfp_budget_changes
                                                         where t.fk_tenant_id == userDetails.tenant_id
                                                            && t.budget_year == budgetYear
                                                            && t.status == 1
                                                            && t.org_budget_flag == 1
                                                         orderby t.change_date descending
                                                         select t).ToListAsync();
                    if (dataActiveBudgetChanges.Count != 0)
                    {
                        activeChangeId = dataActiveBudgetChanges.FirstOrDefault().pk_change_id;
                    }
                }

                dynamic data = await tenantDbContext.tfp_budget_changes.Where(x => x.fk_tenant_id == userdata.tenant_id && x.budget_year == budgetYear && x.pk_change_id == activeChangeId && x.status == 1 && x.org_budget_flag == 1).MaxAsync(x => (Int32?)(x.pk_change_id)) ?? 0;
                if (data != 0)
                {
                    return "true";
                }
                else
                {
                    return "false";
                }
            }
        }

        public bool ActiveBudgetChangeExists(string userID, bool isBudgetChangePage, int BudgetYear)
        {
            return ActiveBudgetChangeExistsAsync(userID, isBudgetChangePage, BudgetYear).GetAwaiter().GetResult();
        }
        public async Task<bool> ActiveBudgetChangeExistsAsync(string userID, bool isBudgetChangePage, int BudgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            int budgetYear = BudgetYear;
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();

            dynamic data;
            tco_users_settings userSettings = await consequenceAdjustedBudgetDbContext.tco_users_settings.FirstOrDefaultAsync(x =>
                x.fk_user_id == userDetails.pk_id &&
                x.tenant_id == userDetails.tenant_id && x.is_active_tenant);
            if (isBudgetChangePage == false)
            {
                if (userSettings == null || userSettings.active_change_id == null)
                {
                    data = await consequenceAdjustedBudgetDbContext.tfp_budget_changes.Where(x => x.budget_year == budgetYear && x.fk_tenant_id == userDetails.tenant_id && x.status == 1 && x.org_budget_flag == 1).ToListAsync();
                }
                else
                {
                    data = await consequenceAdjustedBudgetDbContext.tfp_budget_changes.Where(x => x.budget_year == budgetYear && x.fk_tenant_id == userDetails.tenant_id && x.status == 1 && x.org_budget_flag == 1 && x.pk_change_id == userSettings.active_change_id).ToListAsync();
                }
            }
            else
            {
                if (userSettings.active_change_id_budget_changes == null)
                {
                    data = await consequenceAdjustedBudgetDbContext.tfp_budget_changes.Where(x => x.budget_year == budgetYear && x.fk_tenant_id == userDetails.tenant_id && x.status == 1 && x.org_budget_flag == 0).ToListAsync();
                }
                else
                {
                    data = await consequenceAdjustedBudgetDbContext.tfp_budget_changes.Where(x => x.budget_year == budgetYear && x.fk_tenant_id == userDetails.tenant_id && x.status == 1 && x.org_budget_flag == 0 && x.pk_change_id == userSettings.active_change_id_budget_changes).ToListAsync();
                }
            }
            if (data.Count > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public async Task<dynamic> GetBudgetChangeId(string UserId, int BudgetYear)
        {
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            int budgetYear = BudgetYear;
            UserData userdata = await _utility.GetUserDetailsAsync(UserId);
            var data = await consequenceAdjustedBudgetDbContext.tfp_budget_changes.Where(x => x.fk_tenant_id == userdata.tenant_id && x.budget_year == budgetYear && x.status == 1 && x.org_budget_flag == 1).Select(x => x.pk_change_id).ToListAsync();
            if (data.Count > 0)
            {
                return data.First();
            }
            else
            {
                return 0;
            }
        }

        //private async Task AddIntoTcoUserAdjCode(string userId, int budgetYear, string tcoAdjCode)
        //{
        //    UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        //    TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
        //    tco_users_settings userSettings = await consequenceAdjustedBudgetDbContext.tco_users_settings.FirstOrDefaultAsync(x => x.fk_user_id == userDetails.pk_id && x.is_active_tenant);
        //    List<clsBudgetChangesData> data = await (from tfpbc in consequenceAdjustedBudgetDbContext.tfp_budget_changes
        //                                             where tfpbc.budget_year == budgetYear && tfpbc.fk_tenant_id == userDetails.tenant_id && tfpbc.org_budget_flag == 0
        //                                             orderby tfpbc.change_date ascending
        //                                             select new clsBudgetChangesData
        //                                             {
        //                                                 id = tfpbc.pk_change_id,
        //                                                 budget_change = tfpbc.budget_name,
        //                                                 approval_reference = tfpbc.approval_reference,
        //                                                 date = tfpbc.change_date,
        //                                                 statusVal = tfpbc.status,
        //                                                 isActive = 0,
        //                                                 workflowStatus = tfpbc.workflow_status,
        //                                                 statusCode = tfpbc.status,
        //                                                 isSelected = tfpbc.is_selected,
        //                                                 not_approved = tfpbc.not_approved
        //                                             }).ToListAsync();
        //    data.ForEach(x =>
        //    {
        //        x.isActive = userSettings == null ? 0 : userSettings.active_change_id_budget_changes == null ? 0 : userSettings.active_change_id_budget_changes == x.id ? 1 : 0;
        //    });
        //    clsBudgetChangesData idObject = data.FirstOrDefault(x => x.isActive == 1);
        //    tco_user_adjustment_codes userAdjCode = await consequenceAdjustedBudgetDbContext.tco_user_adjustment_codes.FirstOrDefaultAsync(x => x.pk_adj_code == tcoAdjCode && x.fk_tenant_id == userDetails.tenant_id);
        //    tco_adjustment_codes existingAdjCode = await consequenceAdjustedBudgetDbContext.tco_adjustment_codes.FirstOrDefaultAsync(x => x.pk_adjustment_code == tcoAdjCode && x.fk_tenant_id == userDetails.tenant_id);
        //    dynamic obj = new JObject();
        //    if (userAdjCode == null)
        //    {
        //        tco_user_adjustment_codes newAdjCode = new tco_user_adjustment_codes
        //        {
        //            fk_tenant_id = userDetails.tenant_id,
        //            pk_adj_code = tcoAdjCode,
        //            description = existingAdjCode.description,
        //            description_id = Guid.NewGuid(),
        //            status = !idObject.not_approved,
        //            fk_user_id = userDetails.pk_id,
        //            updated = DateTime.UtcNow,
        //            org_level_value = userDetails.tenant_id.ToString(),
        //            budget_year = budgetYear,
        //            org_level = 1,
        //            is_original_flag = false,
        //            prefix_adjCode = null,
        //            inv_adj_code = false,
        //            fk_main_project_code = null,
        //            fk_change_id = idObject.id,
        //            include_in_calculation = false
        //        };
        //        consequenceAdjustedBudgetDbContext.tco_user_adjustment_codes.Add(newAdjCode);
        //        await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
        //        obj.adjCode = tcoAdjCode;
        //        obj.msg = "";
        //        obj.id = newAdjCode.Id;
        //        return obj;
        //    }
        //    obj.adjCode = tcoAdjCode;
        //    obj.msg = "";
        //    obj.id = userAdjCode.Id;
        //    return obj;
        //}

        public dynamic GetActionDetailsWithBudgetChange(int actionId, int changeid, int actionType, string userId,
            string orgId, string serviceId, string gridNameForDOS = null, string serviceUnitId = "",
            string pageId = null, int? orgLevel = null, int budgetYear = 0, string orgIdLevel1 = "",
            string orgIdLevel2 = "", string orgIdLevel3 = "", string orgIdLevel4 = "", string orgIdLevel5 = "",
            string adjCode = null, string tcoAdjCode = null)
        {
            var result = GetActionDetailsWithBudgetChangeAsync(actionId, changeid, actionType,
                userId, orgId, serviceId, gridNameForDOS, serviceUnitId,
                pageId, orgLevel, budgetYear, orgIdLevel1,
                orgIdLevel2, orgIdLevel3, orgIdLevel4, orgIdLevel5,
                adjCode, tcoAdjCode).GetAwaiter().GetResult();
            return result;
        }

        public async Task<dynamic> GetActionDetailsWithBudgetChangeAsync(int actionId, int changeid, int actionType,
            string userId, string orgId, string serviceId, string gridNameForDOS = null, string serviceUnitId = "",
            string pageId = null, int? orgLevel = null, int budgetYear = 0, string orgIdLevel1 = "",
            string orgIdLevel2 = "", string orgIdLevel3 = "", string orgIdLevel4 = "", string orgIdLevel5 = "",
            string adjCode = null, string tcoAdjCode = null)
        {
            serviceId = string.IsNullOrEmpty(serviceId) ? string.Empty : serviceId == "undefined" ? string.Empty : serviceId == "null" ? string.Empty : serviceId;

            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var clientId = userDetails.client_id;
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));
            Guid longDescriptionId = actionId == 0 ? Guid.Empty : await GenerateLongDescGuid(actionId, userId);
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            tco_users_settings userSettings =
                await consequenceAdjustedBudgetDbContext.tco_users_settings.FirstOrDefaultAsync(x =>
                    x.fk_user_id == userDetails.pk_id && x.tenant_id == userDetails.tenant_id && x.is_active_tenant);

            var chageids = new List<int>();
            if (!string.IsNullOrEmpty(pageId) && (pageId == "BudChangeOverview" || pageId == "BudChangeDetail"))
            {
                chageids = await consequenceAdjustedBudgetDbContext.tfp_budget_changes.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear && x.pk_change_id != changeid).Select(x => x.pk_change_id).ToListAsync();
                if (tcoAdjCode != null)
                {
                    await AddIntoTcoUserAdjCode(userId, budgetYear, tcoAdjCode);
                }
            }
            else if (pageId == "yearly_budget")
            {
                chageids = await consequenceAdjustedBudgetDbContext.tfp_budget_changes.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear && x.pk_change_id != changeid).Select(x => x.pk_change_id).ToListAsync();
            }
            else if (pageId == "serviceUnitBudget")
            {
                chageids = await consequenceAdjustedBudgetDbContext.tfp_budget_changes.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear && x.pk_change_id != changeid).Select(x => x.pk_change_id).ToListAsync();
            }
            else if (pageId == "ServiceUnitForUnlockedBudget")
            {
                chageids = await consequenceAdjustedBudgetDbContext.tfp_budget_changes.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear && x.status == 1 && x.org_budget_flag == 0).Select(x => x.pk_change_id).ToListAsync();
            }
            else
            {
                chageids = await consequenceAdjustedBudgetDbContext.tfp_budget_changes.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.org_budget_flag == 1 && x.budget_year == budgetYear && ((x.status != 1) || (x.status == 1 && x.pk_change_id != changeid))).Select(x => x.pk_change_id).ToListAsync();

                if (userSettings != null && userSettings.active_change_id != null && userSettings.active_change_id == changeid)
                {
                    if (await consequenceAdjustedBudgetDbContext.tfp_budget_changes.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear && x.pk_change_id == changeid && x.status == 1) == null)
                    {
                        /*closed budget round but active for logged in user*/
                        chageids.Remove(chageids.FirstOrDefault(x => x == changeid));
                    }
                }
            }

            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ConsequenceAdjustedBudget");
            List<string> departmentsList = new List<string>();
            List<string> functionsList = new List<string>();
            List<clsOrgStructure> lstOrgStructure = new List<clsOrgStructure>();
            clsOrgStructureLevelDetails tenantOrgLevelDetails = null;
            List<List<string>> DepartmentsAndFunctions = new List<List<string>>();

            List<clsOrgIdAndDepartments> lstAllServiceAreaDepts = await (from t in consequenceAdjustedBudgetDbContext.tco_departments
                                                                         where t.fk_tenant_id == userDetails.tenant_id &&
                                                                               t.year_from <= budgetYear &&
                                                                               t.year_to >= budgetYear
                                                                         select new clsOrgIdAndDepartments
                                                                         {
                                                                             departmentValue = t.pk_department_code,
                                                                             departmentText = t.department_name
                                                                         }).ToListAsync();

            List<clsOrgIdAndDepartments> lstOrgIdDepartments = new List<clsOrgIdAndDepartments>();

            vw_tco_parameters parameterizedFinPlanLevel1 = null;
            vw_tco_parameters parameterizedFinPlanLevel2 = null;
            string strFinplanLevel1 = string.Empty;
            string strFinplanLevel2 = string.Empty;

            if (gridNameForDOS != null && gridNameForDOS == "grid_actions_proposals")
            {
                parameterizedFinPlanLevel1 = await (from t in consequenceAdjustedBudgetDbContext.vw_tco_parameters
                                                    where t.fk_tenant_id == userDetails.tenant_id && t.active == 1 && t.param_name == "BUDMAN_ACTION_LEVEL_1"
                                                    select t).FirstOrDefaultAsync();

                parameterizedFinPlanLevel2 = await (from t in consequenceAdjustedBudgetDbContext.vw_tco_parameters
                                                    where t.fk_tenant_id == userDetails.tenant_id && t.active == 1 && t.param_name == "BUDMAN_ACTION_LEVEL_2"
                                                    select t).FirstOrDefaultAsync();

                if (parameterizedFinPlanLevel1 != null && parameterizedFinPlanLevel2 != null)
                {
                    strFinplanLevel1 = "BUDMAN_ACTION_LEVEL_1";
                    strFinplanLevel2 = "BUDMAN_ACTION_LEVEL_2";
                    lstOrgStructure = await _utility.GetTenantOrgStructureAsync(orgVersionContent, userId, strFinplanLevel1, strFinplanLevel2);
                }
                else if (parameterizedFinPlanLevel1 != null)
                {
                    strFinplanLevel1 = "BUDMAN_ACTION_LEVEL_1";
                    strFinplanLevel2 = string.Empty;
                    lstOrgStructure = await _utility.GetTenantOrgStructureAsync(orgVersionContent, userId, strFinplanLevel1, strFinplanLevel2);
                }
                else if (parameterizedFinPlanLevel2 != null)
                {
                    strFinplanLevel1 = string.Empty;
                    strFinplanLevel2 = "BUDMAN_ACTION_LEVEL_2";
                    lstOrgStructure = await _utility.GetTenantOrgStructureAsync(orgVersionContent, userId, strFinplanLevel1, strFinplanLevel2);
                }
                else
                {
                    strFinplanLevel1 = "FINPLAN_LEVEL_1";
                    strFinplanLevel2 = "FINPLAN_LEVEL_2";
                    lstOrgStructure = await _utility.GetTenantOrgStructureAsync(orgVersionContent, userId, strFinplanLevel1, strFinplanLevel2);
                }
            }
            else
            {
                strFinplanLevel1 = "FINPLAN_LEVEL_1";
                strFinplanLevel2 = "FINPLAN_LEVEL_2";
                lstOrgStructure = await _utility.GetTenantOrgStructureAsync(orgVersionContent, userId, strFinplanLevel1, strFinplanLevel2);
            }

            if (!string.IsNullOrEmpty(pageId) && pageId == "yearly_budget")
            {
                string filterData = _staffPlanning.ReadFilterData("FilterData_Tenant_" + userDetails.tenant_id.ToString() + "_" + budgetYear + "/" + orgLevel.Value + "-" + orgId + ".json");
                StaffPlanningFilterCondition dep = JsonConvert.DeserializeObject<StaffPlanningFilterCondition>(filterData);
                List<KeyValuePair> dDepartments = (from dF in dep.Departments
                                                   orderby dF.Key
                                                   select new KeyValuePair { key = dF.Key, value = dF.Value }).Distinct().ToList();
                departmentsList = dep.Departments.Select(x => x.Key.ToUpper()).ToList().Distinct().ToList();
            }
            else if (!string.IsNullOrEmpty(pageId) && (pageId == "serviceUnitBudget" || pageId == "ServiceUnitForUnlockedBudget"))
            {
                departmentsList = await _utility.GetDepartmentsFromTcoOrgHierarchyTableAsync(orgVersionContent, userId, orgIdLevel1, orgIdLevel2, orgIdLevel3, orgIdLevel4, orgIdLevel5);
            }
            else
            {
                tenantOrgLevelDetails = _utility.GetTenantOrgLevelDetails(lstOrgStructure, strFinplanLevel1, strFinplanLevel2);
                if (string.IsNullOrEmpty(orgId) && string.IsNullOrEmpty(serviceId) && !string.IsNullOrEmpty(serviceUnitId))
                {
                    Dictionary<string, string> dictServiceAreaDepartments = await _staffPlanning.GetServiceAreaDepartmentsForBudgetEntriesAsync(userId, serviceUnitId, budgetYear);
                    List<string> departmentCodes = dictServiceAreaDepartments.Select(x => x.Key.ToUpper()).ToList();
                    DepartmentsAndFunctions.Add(departmentCodes);
                    departmentsList = DepartmentsAndFunctions[0];
                }
                else
                {
                    if (parameterizedFinPlanLevel1 != null || parameterizedFinPlanLevel2 != null)
                    {
                        DepartmentsAndFunctions = _utility.GetDepartmentsAndFunctionsForOrgStructureForDynamicLevels(lstOrgStructure, string.IsNullOrEmpty(orgId) ? null : orgId, serviceId, tenantOrgLevelDetails.level1, tenantOrgLevelDetails.level2, strFinplanLevel1, strFinplanLevel2);
                    }
                    else
                    {
                        DepartmentsAndFunctions = _utility.GetDepartmentsAndFunctionsForOrgStructure(lstOrgStructure, string.IsNullOrEmpty(orgId) ? null : orgId, serviceId, tenantOrgLevelDetails.level1, tenantOrgLevelDetails.level2);
                    }
                    departmentsList = DepartmentsAndFunctions[0];
                    functionsList = DepartmentsAndFunctions[1];
                }
            }

            lstOrgIdDepartments = !(string.IsNullOrEmpty(pageId)) ?
                (from a in lstAllServiceAreaDepts
                 join d in departmentsList on a.departmentValue equals d
                 group a by new { a.departmentValue, a.departmentText } into g
                 select new clsOrgIdAndDepartments
                 {
                     departmentValue = g.Key.departmentValue,
                     departmentText = g.Key.departmentText
                 }).ToList()
                 :
                 (from a in lstAllServiceAreaDepts
                  join d in departmentsList on a.departmentValue equals d
                  group a by new { a.departmentValue } into g
                  select new clsOrgIdAndDepartments
                  {
                      departmentValue = g.Key.departmentValue
                  }).ToList();

            dynamic lstAjstmt = await GetAdjustmentCodesAsync(userId);
            dynamic lstAlter = await GetAlterCodesAsync(userId, actionType, true, false, false, pageId);

            var paramLockOriginalBudget = await consequenceAdjustedBudgetDbContext.vw_tco_parameters.FirstOrDefaultAsync(t => t.fk_tenant_id == userDetails.tenant_id
                                                                                                           && t.param_name == "LOCK_ORIGINAL_BUDGET"
                                                                                                           && t.param_value == budgetYear.ToString()
                                                                                                           && t.active == 1);

            var lstProjects = await consequenceAdjustedBudgetDbContext.tco_projects.Where(x => x.fk_tenant_id == userDetails.tenant_id && (budgetYear >= x.date_from.Year && budgetYear <= x.date_to.Year)).ToListAsync();
            List<ActDetailsBudChange> totalData = new List<ActDetailsBudChange>();
            var dbDataset = (from td in consequenceAdjustedBudgetDbContext.tfp_trans_detail
                             join th in consequenceAdjustedBudgetDbContext.tfp_trans_header on new { a = td.fk_tenant_id, b = td.fk_action_id }
                                                                                        equals new { a = th.fk_tenant_id, b = th.pk_action_id }
                             join ta in consequenceAdjustedBudgetDbContext.tco_accounts.Where(x => x.pk_tenant_id == userDetails.tenant_id && x.dateFrom.Year <= budgetYear && x.dateTo.Year >= budgetYear)
                                                                                                  on new { a = td.fk_tenant_id, b = td.fk_account_code }
                                                                                              equals new { a = ta.pk_tenant_id, b = ta.pk_account_code } into tal
                             from ta1 in tal.DefaultIfEmpty()
                             join tf in consequenceAdjustedBudgetDbContext.tco_functions.Where(x => x.pk_tenant_id == userDetails.tenant_id && x.dateFrom.Year <= budgetYear && x.dateTo.Year >= budgetYear)
                                                                                                   on new { a = td.fk_tenant_id, b = td.function_code }
                                                                                               equals new { a = tf.pk_tenant_id, b = tf.pk_Function_code } into tfl
                             from tf1 in tfl.DefaultIfEmpty()
                             join tad in consequenceAdjustedBudgetDbContext.tco_adjustment_codes on new { a = td.fk_tenant_id, b = td.fk_adjustment_code }
                                                                                             equals new { a = tad.fk_tenant_id, b = tad.pk_adjustment_code } into tadl
                             from tad1 in tadl.DefaultIfEmpty()
                             join tac in consequenceAdjustedBudgetDbContext.tco_fp_alter_codes on new { a = td.fk_tenant_id, b = td.fk_alter_code }
                                                                                           equals new { a = tac.fk_tenant_id, b = tac.pk_alter_code } into tacl
                             from tac1 in tacl.DefaultIfEmpty()
                             join tfd1 in consequenceAdjustedBudgetDbContext.tco_free_dim_values.Where(x => x.free_dim_column == "free_dim_1")
                                                                                                     on new { a = td.fk_tenant_id, b = td.free_dim_1 }
                                                                                                 equals new { a = tfd1.fk_tenant_id, b = tfd1.free_dim_code } into tfd1l
                             from tfd11 in tfd1l.DefaultIfEmpty()

                             join tfd2 in consequenceAdjustedBudgetDbContext.tco_free_dim_values.Where(x => x.free_dim_column == "free_dim_2")
                                                                                                         on new { a = td.fk_tenant_id, b = td.free_dim_2 }
                                                                                                     equals new { a = tfd2.fk_tenant_id, b = tfd2.free_dim_code } into tfd2l
                             from tfd21 in tfd2l.DefaultIfEmpty()

                             join tfd3 in consequenceAdjustedBudgetDbContext.tco_free_dim_values.Where(x => x.free_dim_column == "free_dim_3")
                                                                                                         on new { a = td.fk_tenant_id, b = td.free_dim_3 }
                                                                                                     equals new { a = tfd3.fk_tenant_id, b = tfd3.free_dim_code } into tfd3l
                             from tfd31 in tfd3l.DefaultIfEmpty()

                             join tfd4 in consequenceAdjustedBudgetDbContext.tco_free_dim_values.Where(x => x.free_dim_column == "free_dim_4")
                                                                                                         on new { a = td.fk_tenant_id, b = td.free_dim_4 }
                                                                                                     equals new { a = tfd4.fk_tenant_id, b = tfd4.free_dim_code } into tfd4l
                             from tfd41 in tfd4l.DefaultIfEmpty()
                             where (th.pk_action_id == actionId && th.fk_tenant_id == userDetails.tenant_id && td.budget_year == budgetYear)
                             select new ActDetailsBudChange
                             {
                                 fk_tenant_id = th.fk_tenant_id,
                                 budget_year = td.budget_year,
                                 fk_change_id = td.fk_change_id,
                                 fk_account_code = td.fk_account_code,
                                 accountName = ta1 == null ? string.Empty : ta1.display_name,
                                 function_code = td.function_code,
                                 functionName = tf1 == null ? string.Empty : tf1.display_name,
                                 project_code = td.project_code,
                                 projectName = "",
                                 department_code = td.department_code,

                                 free_dim_1 = td.free_dim_1,
                                 freedim1Name = tfd11 == null ? string.Empty : tfd11.description,

                                 free_dim_2 = td.free_dim_2,
                                 freedim2Name = tfd21 == null ? string.Empty : tfd21.description,

                                 free_dim_3 = td.free_dim_3,
                                 freedim3Name = tfd31 == null ? string.Empty : tfd31.description,

                                 free_dim_4 = td.free_dim_4,
                                 freedim4Name = tfd41 == null ? string.Empty : tfd41.description,

                                 description = td.description,
                                 pk_action_id = th.pk_action_id,
                                 consequence = th.consequence,
                                 fk_area_id = th.fk_area_id,
                                 tag = th.tag,
                                 priority = th.priority,
                                 long_description = th.long_description,
                                 updated = th.updated,
                                 updated_by = th.updated_by,
                                 display_description_apendix_flag = th.display_description_apendix_flag,
                                 display_financial_plan_flag = th.display_financial_plan_flag,
                                 display_cab_flag = th.display_cab_flag,
                                 fk_cat_id = th.fk_cat_id,
                                 thdes = th.description,
                                 financial_plan_description = th.financial_plan_description,
                                 fk_adjustment_code = td.fk_adjustment_code,
                                 fk_user_adj_code = td.fk_adj_code,
                                 adjustmentName = tad1 == null ? string.Empty : tad1.description,
                                 fk_alter_code = td.fk_alter_code,
                                 alterCodeName = tac1 == null ? string.Empty : tac1.alter_description,
                                 consequence_flag = th.consequence_flag,
                                 different_external_description_flag = th.different_external_description_flag,
                                 change_text_flag = th.change_text_flag,
                                 year_1_amount = td.year_1_amount,
                                 year_2_amount = td.year_2_amount,
                                 year_3_amount = td.year_3_amount,
                                 year_4_amount = td.year_4_amount,
                                 year_5_amount = td.year_5_amount,
                                 pk_id = td.pk_id,
                                 tdUpdated = td.updated,
                                 tags = th.tags,
                                 action_type = th.action_type,
                                 finished_date = th.finished_date,
                                 fk_key_id = td.fk_key_id == null ? 0 : td.fk_key_id.Value,
                                 display_zero_action = th.display_zero_action,
                                 update_next_year_finplan = th.update_next_year_finplan,
                                 update_annual_budget = th.update_annual_budget,
                                 update_annual_budget_next_year = th.update_annual_budget_next_year,
                                 goalsTags = th.goals_tags,
                                 targetTags = th.targets_tags,
                                 strategyTags = th.strategies_tags,
                             });
            totalData = await dbDataset.ToListAsync();
            //71612
            if (pageId == pageType.finplanOverview && departmentsList.Count > 0)
            {
                dbDataset = dbDataset.Where(x => departmentsList.Contains(x.department_code));
            }
            else
            {
                if (departmentsList.Count > 0 && functionsList.Count > 0)
                {
                    dbDataset = dbDataset.Where(x => departmentsList.Contains(x.department_code) && functionsList.Contains(x.function_code));
                }
                else if (departmentsList.Count > 0)
                {
                    dbDataset = dbDataset.Where(x => departmentsList.Contains(x.department_code));
                }
                else if (functionsList.Count > 0)
                {
                    dbDataset = dbDataset.Where(x => functionsList.Contains(x.function_code));
                }
            }

            var main = await (from d in dbDataset
                              select new
                              {
                                  d.fk_tenant_id,
                                  d.budget_year,
                                  d.fk_change_id,
                                  d.fk_account_code,
                                  d.accountName,
                                  d.function_code,
                                  d.functionName,
                                  d.project_code,
                                  d.projectName,
                                  d.department_code,

                                  d.free_dim_1,
                                  d.freedim1Name,

                                  d.free_dim_2,
                                  d.freedim2Name,

                                  d.free_dim_3,
                                  d.freedim3Name,

                                  d.free_dim_4,
                                  d.freedim4Name,

                                  d.description,
                                  d.pk_action_id,
                                  d.consequence,
                                  d.fk_area_id,
                                  d.tag,
                                  d.priority,
                                  d.long_description,
                                  d.updated,
                                  d.updated_by,
                                  d.display_description_apendix_flag,
                                  d.display_financial_plan_flag,
                                  d.display_cab_flag,
                                  d.fk_cat_id,
                                  d.thdes,
                                  d.financial_plan_description,
                                  d.fk_adjustment_code,
                                  d.fk_user_adj_code,
                                  d.adjustmentName,
                                  d.fk_alter_code,
                                  d.alterCodeName,
                                  d.consequence_flag,
                                  d.different_external_description_flag,
                                  d.change_text_flag,
                                  d.year_1_amount,
                                  d.year_2_amount,
                                  d.year_3_amount,
                                  d.year_4_amount,
                                  d.year_5_amount,
                                  d.pk_id,
                                  d.tdUpdated,
                                  d.tags,
                                  d.action_type,
                                  d.finished_date,
                                  d.fk_key_id,
                                  d.display_zero_action,
                                  d.update_annual_budget,
                                  d.update_annual_budget_next_year,
                                  d.update_next_year_finplan,
                              }).Distinct().ToListAsync();

            var lst = (from m in main
                       group m by new
                       {
                           m.fk_change_id,
                           m.fk_account_code,
                           m.accountName,
                           m.function_code,
                           m.functionName,
                           m.project_code,
                           m.projectName,
                           m.department_code,

                           m.free_dim_1,
                           m.freedim1Name,

                           m.free_dim_2,
                           m.freedim2Name,

                           m.free_dim_3,
                           m.freedim3Name,

                           m.free_dim_4,
                           m.freedim4Name,

                           m.description,
                           m.pk_action_id,
                           m.consequence,
                           m.fk_area_id,
                           m.tag,
                           m.tags,
                           m.priority,
                           m.long_description,
                           m.updated,
                           m.updated_by,
                           m.display_description_apendix_flag,
                           m.display_financial_plan_flag,
                           m.display_cab_flag,
                           m.fk_cat_id,
                           m.thdes,
                           m.financial_plan_description,
                           m.fk_adjustment_code,
                           m.fk_user_adj_code,
                           m.adjustmentName,
                           m.fk_alter_code,
                           m.alterCodeName,
                           m.consequence_flag,
                           m.different_external_description_flag,
                           m.change_text_flag,
                           m.year_1_amount,
                           m.year_2_amount,
                           m.year_3_amount,
                           m.year_4_amount,
                           m.year_5_amount,
                           m.tdUpdated,
                           m.finished_date,
                           m.fk_key_id,
                           m.display_zero_action,
                           m.update_annual_budget,
                           m.update_annual_budget_next_year,
                           m.update_next_year_finplan,
                       } into g
                       select new clsBudgetChangesActions
                       {
                           changeId = g.Key.fk_change_id.Equals(null) ? 0 : g.Key.fk_change_id,
                           actionID = g.Key.pk_action_id,
                           actionDescription = g.Key.thdes,
                           consequence = g.Key.consequence,
                           accountCode = g.Key.fk_account_code,
                           accountName = g.Key.accountName,
                           deptCode = g.Key.department_code,
                           deptName = string.Empty,
                           functionCode = g.Key.function_code,
                           functionName = g.Key.functionName,
                           projectCode = g.Key.project_code,
                           projectName = g.Key.projectName,

                           freedimCode1 = g.Key.free_dim_1,
                           freedimName1 = g.Key.freedim1Name,

                           freedimCode2 = g.Key.free_dim_2,
                           freedimName2 = g.Key.freedim2Name,

                           freedimCode3 = g.Key.free_dim_3,
                           freedimName3 = g.Key.freedim3Name,

                           freedimCode4 = g.Key.free_dim_4,
                           freedimName4 = g.Key.freedim4Name,

                           year1Ammount = g.Sum(x => x.year_1_amount),
                           year2Ammount = g.Sum(x => x.year_2_amount),
                           year3Ammount = g.Sum(x => x.year_3_amount),
                           year4Ammount = g.Sum(x => x.year_4_amount),
                           year5Ammount = g.Sum(x => x.year_5_amount),
                           fk_area_id = g.Key.fk_area_id == null ? 0 : g.Key.fk_area_id.Value,
                           tag = g.Key.tag == null ? "" : g.Key.tag,
                           tags = g.Key.tags == null ? "" : g.Key.tags,
                           priority = g.Key.priority == null ? 0 : g.Key.priority.Value,
                           long_description = g.Key.long_description == null ? "" : g.Key.long_description,
                           user = g.Key.updated_by,
                           lastupdated = g.Key.updated,
                           display_financial_plan_flag = g.Key.display_financial_plan_flag,
                           display_cab_flag = g.Key.display_cab_flag.HasValue ? g.Key.display_cab_flag.Value : false,
                           display_description_apendix_flag = g.Key.display_description_apendix_flag,
                           description = g.Key.description == null ? String.Empty : g.Key.description,
                           cat_id = g.Key.fk_cat_id,
                           financial_plan_description = g.Key.financial_plan_description,
                           fk_adjustment_code = g.Key.fk_adjustment_code,
                           fk_user_adj_code = g.Key.fk_user_adj_code,
                           adjustmentCodeName = g.Key.adjustmentName,
                           fk_alter_code = g.Key.fk_alter_code,
                           alterCodeName = g.Key.alterCodeName,
                           consequence_flag = g.Key.consequence_flag,
                           different_external_description_flag = g.Key.different_external_description_flag,
                           change_text_flag = g.Key.change_text_flag,
                           tdUpdated = g.Key.tdUpdated,
                           finished_date = g.Key.finished_date,
                           periodicKey = g.Key.fk_key_id.ToString(),
                           DisplayZeroAction = g.Key.display_zero_action,
                           update_annual_budget = g.Key.update_annual_budget,
                           update_next_year_finplan = g.Key.update_next_year_finplan,
                           update_annual_budget_next_year = g.Key.update_annual_budget_next_year,
                           page_origin = 0,
                       }).ToList();

            lst.ForEach(x =>
            {
                x.projectName = lstProjects.FirstOrDefault(p => p.pk_project_code == x.projectCode) == null ? "" : lstProjects.FirstOrDefault(p => p.pk_project_code == x.projectCode).project_name;
            });

            dynamic actions = new JObject();
            List<int> tagIdsLst = new List<int>();
            if (lst.Any())
            {
                List<int> TagIds = new List<int>();
                if (!string.IsNullOrEmpty((lst.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).tags))
                {
                    TagIds = (lst.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).tags.Split(',').Select(int.Parse).ToList();
                }
                actions.Add("tags", JToken.FromObject(TagIds));
                tagIdsLst = TagIds;
            }

            if (pageId == "BudChangeOverview" && adjCode != null)
            {
                lst = lst.Where(x => ((chageids.Contains(x.changeId.Value) || x.changeId.Value == 0 || (x.changeId.Value == changeid && (x.fk_user_adj_code != adjCode))))).ToList();
            }
            else
            {
                lst = lst.Where(x => ((chageids.Contains(x.changeId.Value)) || (x.changeId.Value == 0))).ToList();
            }

            List<clsBudgetChangesActions> result = new List<clsBudgetChangesActions>();
            result = (from a in lst
                      join b in lstOrgIdDepartments on a.deptCode.ToUpper() equals b.departmentValue.ToUpper()
                      select new clsBudgetChangesActions
                      {
                          changeId = a.changeId.Value,
                          actionID = a.actionID,
                          actionDescription = a.actionDescription,
                          //pk_id = a.page_origin == 1 ? a.pk_id : 0,
                          accountCode = a.accountCode,
                          accountName = a.accountName,
                          deptCode = a.deptCode,
                          deptName = b.departmentText,
                          functionCode = a.functionCode,
                          functionName = a.functionName,
                          projectCode = a.projectCode,
                          projectName = a.projectName,
                          freedimCode1 = a.freedimCode1,
                          freedimName1 = a.freedimName1,

                          freedimCode2 = a.freedimCode2,
                          freedimName2 = a.freedimName2,

                          freedimCode3 = a.freedimCode3,
                          freedimName3 = a.freedimName3,

                          freedimCode4 = a.freedimCode4,
                          freedimName4 = a.freedimName4,

                          year1Ammount = a.year1Ammount,
                          year2Ammount = a.year2Ammount,
                          year3Ammount = a.year3Ammount,
                          year4Ammount = a.year4Ammount,
                          year5Ammount = a.year5Ammount,
                          user = a.user,
                          lastupdated = a.lastupdated,
                          display_financial_plan_flag = a.display_financial_plan_flag,
                          display_description_apendix_flag = a.display_description_apendix_flag,
                          description = a.description,
                          cat_id = a.cat_id,
                          financial_plan_description = a.financial_plan_description,
                          fk_adjustment_code = a.fk_adjustment_code,
                          fk_user_adj_code = a.fk_user_adj_code,
                          fk_alter_code = a.fk_alter_code,
                          adjustmentCodeName = a.adjustmentCodeName,
                          alterCodeName = a.alterCodeName,
                          consequence_flag = a.consequence_flag,
                          different_external_description_flag = a.different_external_description_flag,
                          change_text_flag = a.change_text_flag,
                          tdUpdated = a.tdUpdated,
                          finished_date = a.finished_date,
                          periodicKey = a.periodicKey,
                          DisplayZeroAction = a.DisplayZeroAction,
                          update_annual_budget = a.update_annual_budget,
                          update_annual_budget_next_year = a.update_annual_budget_next_year,
                          update_next_year_finplan = a.update_next_year_finplan,
                          page_origin = a.page_origin,
                      }).ToList();
            List<clsBudgetChangesActions> resultActive = result.Where(x => x.page_origin == 1).ToList();
            List<freedimDefinition> freeDims = await _utility.GetFreeDimDefinitionsBasedOnPageIdAsync(userId, string.Empty);
            List<freedimDefinition> freeDimColumns = freeDims.ToList();
            gmd_action_types actionTypeDesc = await consequenceAdjustedBudgetDbContext.gmd_action_types.FirstOrDefaultAsync(x => x.pk_language == userDetails.language_preference && x.pk_action_type == actionType);

            ///Get total lines
            dynamic ActionDetailsTotal = new JObject();
            decimal year1total = 0; decimal year2total = 0; decimal year3total = 0; decimal year4total = 0; decimal year5total = 0;
            decimal year1totalbdtchange = 0; decimal year2totalbdtchange = 0; decimal year3totalbdtchange = 0; decimal year4totalbdtchange = 0; decimal year5totalbdtchange = 0;
            dynamic ActionDetailsTotalbdtchange = new JObject();
            dynamic ActionDetailsTotalfinaltotal = new JObject();
            ActionDetailsTotal.id = "total";
            ActionDetailsTotalbdtchange.id = "total";
            ActionDetailsTotalfinaltotal.id = "total";
            dynamic accDetails = new JObject();
            accDetails.accountText = langStringValues.FirstOrDefault(v => v.Key == "cmn_title_total").Value.LangText;
            accDetails.accountValue = langStringValues.FirstOrDefault(v => v.Key == "cmn_title_total").Value.LangText;
            ActionDetailsTotal.account = accDetails;
            dynamic accDetailsfinaltotal = new JObject();
            accDetailsfinaltotal.accountText = langStringValues.FirstOrDefault(v => v.Key == "total_incl_curr_bud_chg").Value.LangText;
            accDetailsfinaltotal.accountValue = "";

            ActionDetailsTotal.account = accDetails;
            ActionDetailsTotal.department = GenerateDynamicDepartmentObject("", "");
            ActionDetailsTotal.functionn = GenerateDynamicFunctionObject("", "");
            ActionDetailsTotal.project = GenerateDynamicProjectDimObject("", "");

            ActionDetailsTotalbdtchange.account = accDetails;
            ActionDetailsTotalbdtchange.department = GenerateDynamicDepartmentObject("", "");
            ActionDetailsTotalbdtchange.functionn = GenerateDynamicFunctionObject("", "");
            ActionDetailsTotalbdtchange.project = GenerateDynamicProjectDimObject("", "");

            ActionDetailsTotalfinaltotal.account = accDetailsfinaltotal;
            ActionDetailsTotalfinaltotal.department = GenerateDynamicDepartmentObject("", "");
            ActionDetailsTotalfinaltotal.functionn = GenerateDynamicFunctionObject("", "");
            ActionDetailsTotalfinaltotal.project = GenerateDynamicProjectDimObject("", "");

            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_1") != null)
            {
                ActionDetailsTotal.freeDim1 = GenerateDynamicFreeDimObject("", "");
                ActionDetailsTotalbdtchange.freeDim1 = GenerateDynamicFreeDimObject("", "");
                ActionDetailsTotalfinaltotal.freeDim1 = GenerateDynamicFreeDimObject("", "");
            }
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_2") != null)
            {
                ActionDetailsTotal.freeDim2 = GenerateDynamicFreeDimObject("", "");
                ActionDetailsTotalbdtchange.freeDim2 = GenerateDynamicFreeDimObject("", "");
                ActionDetailsTotalfinaltotal.freeDim2 = GenerateDynamicFreeDimObject("", "");
            }
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_3") != null)
            {
                ActionDetailsTotal.freeDim3 = GenerateDynamicFreeDimObject("", "");
                ActionDetailsTotalbdtchange.freeDim3 = GenerateDynamicFreeDimObject("", "");
                ActionDetailsTotalfinaltotal.freeDim3 = GenerateDynamicFreeDimObject("", "");
            }
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_4") != null)
            {
                ActionDetailsTotal.freeDim4 = GenerateDynamicFreeDimObject("", "");
                ActionDetailsTotalbdtchange.freeDim4 = GenerateDynamicFreeDimObject("", "");
                ActionDetailsTotalfinaltotal.freeDim4 = GenerateDynamicFreeDimObject("", "");
            }

            if (lstAjstmt.Count > 1)
            {
                ActionDetailsTotal.adjustmentCode = GenerateDynamicAdjustmentCodeObject("", "");
                ActionDetailsTotalbdtchange.adjustmentCode = GenerateDynamicAdjustmentCodeObject("", "");
                ActionDetailsTotalfinaltotal.adjustmentCode = GenerateDynamicAdjustmentCodeObject("", "");
            }

            string alterKey_defaults = string.Empty;
            string alterValue_defaults = string.Empty;
            foreach (var item in lstAlter)
            {
                if (item.isDefault == true)
                {
                    alterKey_defaults = item.key;
                    alterValue_defaults = item.value;
                    break;
                }
            }

            if (lstAlter.Count > 1)
            {
                ActionDetailsTotal.alterCode = GenerateDynamicAlterCodeObject("", "");
                ActionDetailsTotalbdtchange.alterCode = GenerateDynamicAlterCodeObject("", "");
                ActionDetailsTotalfinaltotal.alterCode = GenerateDynamicAlterCodeObject("", "");
            }

            if (!string.IsNullOrEmpty(pageId) && (pageId == "BudChangeOverview" || pageId == "BudChangeDetail" || pageId == "ServiceUnitForUnlockedBudget"))
            {
                ActionDetailsTotal.periodicKey = GenerateDynamicPeriodicKeyObject("", "");
                ActionDetailsTotalbdtchange.periodicKey = GenerateDynamicPeriodicKeyObject("", "");
                ActionDetailsTotalfinaltotal.periodicKey = GenerateDynamicPeriodicKeyObject("", "");
            }

            if (actionId == 0)
            {
                actions.change_text_flag = false;
                actions.display_financial_plan_flag = false;
                actions.finishedDate = new DateTime(2099, 12, 31);
            }

            var actionName = string.Empty;
            if (actionId != 0)
            {
                actionName = (await consequenceAdjustedBudgetDbContext.tfp_trans_header.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_action_id == actionId)).description;
            }

            actions.actionId = actionId;
            actions.finishedDate = new DateTime(2099, 12, 31);
            actions.actionDescription = actionName;
            actions.consequence = string.Empty;
            actions.fk_area_id = 0;
            actions.tag = string.Empty;
            actions.priority = 0;
            actions.long_description = string.Empty;
            actions.groupId = actionType.ToString();
            actions.groupName = actionTypeDesc == null ? "" : actionTypeDesc.action_type_descr;
            actions.display_financial_plan_flag = false;
            actions.display_cab_flag = false;
            actions.display_description_apendix_flag = false;
            actions.cat_id = Guid.Empty;
            actions.financial_plan_description = string.Empty;
            actions.fk_adjustment_code = string.Empty;
            actions.fk_alter_code = string.Empty;
            actions.consequence_flag = false;
            actions.different_external_description_flag = false;
            actions.change_text_flag = false;
            actions.DisplayZeroAction = false;

            if (lst.Count > 0)
            {
                actions.actionId = actionId;
                actions.finishedDate = (lst.FirstOrDefault() == null || lst.FirstOrDefault().finished_date == null) ? new DateTime(2099, 12, 31) : lst.FirstOrDefault().finished_date;
                actions.actionDescription = (lst.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).actionDescription;
                actions.consequence = (lst.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).consequence;
                actions.fk_area_id = (lst.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).fk_area_id.Value;
                actions.tag = (lst.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).tag;
                actions.priority = (lst.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).priority.Value;
                actions.long_description = (lst.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).long_description;
                actions.groupId = actionType.ToString();
                actions.groupName = actionTypeDesc == null ? "" : actionTypeDesc.action_type_descr;
                actions.display_financial_plan_flag = actionId == 0 ? false : (lst.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).display_financial_plan_flag;
                actions.display_cab_flag = actionId == 0 ? false : (lst.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).display_cab_flag;
                actions.display_description_apendix_flag = (lst.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).display_description_apendix_flag;
                actions.cat_id = (lst.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).cat_id;
                actions.financial_plan_description = (lst.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).financial_plan_description;
                actions.fk_adjustment_code = (lst.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).fk_adjustment_code;
                actions.fk_alter_code = (lst.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).fk_alter_code;
                actions.consequence_flag = (lst.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).consequence_flag;
                actions.different_external_description_flag = (lst.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).different_external_description_flag;
                actions.change_text_flag = actionId == 0 ? false : (lst.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).change_text_flag;
                actions.DisplayZeroAction = (lst.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).DisplayZeroAction;

                dynamic gridData = new JArray();
                if (!string.IsNullOrEmpty(pageId) && pageId == "ServiceUnitForUnlockedBudget")
                {
                    result = result.Where(x => x.page_origin != 1).ToList();
                }
                result = (from r in result
                          group r by new
                          {
                              r.accountCode,
                              r.accountName,
                              r.deptCode,
                              r.deptName,
                              r.functionCode,
                              r.functionName,
                              r.projectCode,
                              r.projectName,
                              r.freedimCode1,
                              r.freedimName1,
                              r.freedimCode2,
                              r.freedimName2,
                              r.freedimCode3,
                              r.freedimName3,
                              r.freedimCode4,
                              r.freedimName4,
                              r.description,
                              r.fk_alter_code,
                              r.alterCodeName,
                              r.fk_adjustment_code,
                              r.fk_user_adj_code,
                              r.adjustmentCodeName,
                              r.periodicKey,
                          } into g
                          select new clsBudgetChangesActions
                          {
                              accountCode = g.Key.accountCode,
                              accountName = g.Key.accountName,
                              deptCode = g.Key.deptCode,
                              deptName = g.Key.deptName,
                              functionCode = g.Key.functionCode,
                              functionName = g.Key.functionName,
                              projectCode = g.Key.projectCode,
                              projectName = g.Key.projectName,
                              freedimCode1 = g.Key.freedimCode1,
                              freedimCode2 = g.Key.freedimCode2,
                              freedimCode3 = g.Key.freedimCode3,
                              freedimCode4 = g.Key.freedimCode4,
                              freedimName1 = g.Key.freedimName1,
                              freedimName2 = g.Key.freedimName2,
                              freedimName3 = g.Key.freedimName3,
                              freedimName4 = g.Key.freedimName4,
                              description = g.Key.description,
                              year1Ammount = g.Sum(x => x.year1Ammount),
                              year2Ammount = g.Sum(x => x.year2Ammount),
                              year3Ammount = g.Sum(x => x.year3Ammount),
                              year4Ammount = g.Sum(x => x.year4Ammount),
                              year5Ammount = g.Sum(x => x.year5Ammount),
                              fk_alter_code = g.Key.fk_alter_code,
                              alterCodeName = g.Key.alterCodeName,
                              fk_adjustment_code = g.Key.fk_adjustment_code,
                              adjustmentCodeName = g.Key.adjustmentCodeName,
                              periodicKey = g.Key.periodicKey,
                          }).ToList();

                if (!string.IsNullOrEmpty(pageId) && pageId == "serviceUnitBudget")
                {
                    result = result.Where(x => x.year1Ammount != 0 || x.year2Ammount != 0 || x.year3Ammount != 0 || x.year4Ammount != 0 /*|| x.year5Ammount != 0*/).ToList();
                    //note some have values like 0.200 or 0.05 which will be stil shown in screen
                }
                if (!string.IsNullOrEmpty(pageId) && pageId == "ServiceUnitForUnlockedBudget")
                {
                    result = result.Where(x => x.year1Ammount != 0 || x.year2Ammount != 0 || x.year3Ammount != 0 || x.year4Ammount != 0 || x.year5Ammount != 0).ToList();
                }
                foreach (var a in result)
                {
                    dynamic ActionDetails = new JObject();

                    ActionDetails.account = GenerateDynamicAccountObject(a.accountCode, a.accountName);

                    ActionDetails.department = GenerateDynamicDepartmentObject(a.deptCode, a.deptName);

                    ActionDetails.functionn = GenerateDynamicFunctionObject(a.functionCode, a.functionName);

                    ActionDetails.project = GenerateDynamicProjectDimObject(a.projectCode, a.projectName);

                    if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_1") != null)
                    {
                        ActionDetails.freeDim1 = GenerateDynamicFreeDimObject(a.freedimCode1, a.freedimName1);
                    }
                    if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_2") != null)
                    {
                        ActionDetails.freeDim2 = GenerateDynamicFreeDimObject(a.freedimCode2, a.freedimName2);
                    }
                    if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_3") != null)
                    {
                        ActionDetails.freeDim3 = GenerateDynamicFreeDimObject(a.freedimCode3, a.freedimName3);
                    }
                    if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_4") != null)
                    {
                        ActionDetails.freeDim4 = GenerateDynamicFreeDimObject(a.freedimCode4, a.freedimName4);
                    }

                    if (lstAjstmt.Count > 1)
                    {
                        ActionDetails.adjustmentCode = GenerateDynamicAdjustmentCodeObject(a.fk_adjustment_code, a.adjustmentCodeName);
                    }
                    if (lstAlter.Count > 1)
                    {
                        ActionDetails.alterCode = GenerateDynamicAlterCodeObject(a.fk_alter_code, a.alterCodeName);
                    }

                    if (!string.IsNullOrEmpty(pageId) && (pageId == "BudChangeOverview" || pageId == "BudChangeDetail" || pageId == "ServiceUnitForUnlockedBudget"))
                    {
                        var pdKeyVal = (await _utility.GetTenantDBContextAsync()).tco_periodic_key.FirstOrDefault(x => x.key_id.ToString() == a.periodicKey.ToString());
                        a.periodicVal = pdKeyVal != null ? pdKeyVal.key_description : "";
                        dynamic perKeyText = GenerateDynamicPeriodicKeyObject(a.periodicKey, a.periodicVal);
                        ActionDetails.periodicKey = GenerateDynamicPeriodicKeyObject(a.periodicKey, a.periodicVal);
                    }

                    ActionDetails.year1 = a.year1Ammount.Value / 1000;
                    ActionDetails.year2 = a.year2Ammount.Value / 1000;
                    ActionDetails.year3 = a.year3Ammount.Value / 1000;
                    ActionDetails.year4 = a.year4Ammount.Value / 1000;
                    if (!string.IsNullOrEmpty(pageId) && (pageId == "BudChangeOverview" || pageId == "BudChangeDetail" || pageId == "ServiceUnitForUnlockedBudget"))
                    {
                        ActionDetails.year5 = a.year5Ammount.Value / 1000;
                    }
                    ActionDetails.description = a.description;

                    year1total = year1total + (a.year1Ammount.Value / 1000);
                    year2total = year2total + (a.year2Ammount.Value / 1000);
                    year3total = year3total + (a.year3Ammount.Value / 1000);
                    year4total = year4total + (a.year4Ammount.Value / 1000);
                    year5total = year5total + (a.year5Ammount.Value / 1000);

                    gridData.Add(ActionDetails);
                }
                if (!string.IsNullOrEmpty(pageId) && (pageId == "ServiceUnitForUnlockedBudget"))
                {
                    year1total = result.Sum(x => x.year1Ammount ?? 0) / 1000;
                    year2total = result.Sum(x => x.year2Ammount ?? 0) / 1000;
                    year3total = result.Sum(x => x.year3Ammount ?? 0) / 1000;
                    year4total = result.Sum(x => x.year4Ammount ?? 0) / 1000;
                    year5total = result.Sum(x => x.year5Ammount ?? 0) / 1000;
                }
                ///Get total
                ActionDetailsTotal.year1 = year1total;
                ActionDetailsTotal.year2 = year2total;
                ActionDetailsTotal.year3 = year3total;
                ActionDetailsTotal.year4 = year4total;
                if (!string.IsNullOrEmpty(pageId) && (pageId == "BudChangeOverview" || pageId == "BudChangeDetail" || pageId == "ServiceUnitForUnlockedBudget"))
                {
                    ActionDetailsTotal.year5 = year5total;
                }
                ActionDetailsTotal.description = "";
                gridData.Add(ActionDetailsTotal);

                actions.Add("nonActiveChangeData", gridData);
            }

            int colCount = 0;
            dynamic columnFields1 = new JArray();
            dynamic columnFields2 = new JArray();

            columnFields1.Add("account");
            columnFields1.Add("department");
            columnFields1.Add("functionn");
            columnFields1.Add("project");

            columnFields2.Add("account");
            columnFields2.Add("department");
            columnFields2.Add("functionn");
            columnFields2.Add("project");

            foreach (var fd in freeDimColumns)
            {
                if (fd.freeDimColumn == "free_dim_1")
                {
                    columnFields1.Add("freeDim" + 1.ToString());
                    columnFields2.Add("freeDim" + 1.ToString());
                }

                if (fd.freeDimColumn == "free_dim_2")
                {
                    columnFields1.Add("freeDim" + 2.ToString());
                    columnFields2.Add("freeDim" + 2.ToString());
                }

                if (fd.freeDimColumn == "free_dim_3")
                {
                    columnFields1.Add("freeDim" + 3.ToString());
                    columnFields2.Add("freeDim" + 3.ToString());
                }

                if (fd.freeDimColumn == "free_dim_4")
                {
                    columnFields1.Add("freeDim" + 4.ToString());
                    columnFields2.Add("freeDim" + 4.ToString());
                }
            }

            if (lstAjstmt.Count > 1)
            {
                columnFields1.Add("adjustmentCode");
                columnFields2.Add("adjustmentCode");
            }
            if (lstAlter.Count > 1)
            {
                columnFields1.Add("alterCode");
                columnFields2.Add("alterCode");
            }

            if (!string.IsNullOrEmpty(pageId) && (pageId == "BudChangeOverview" || pageId == "BudChangeDetail" || pageId == "ServiceUnitForUnlockedBudget"))
            {
                columnFields1.Add("periodicKey");
                columnFields2.Add("periodicKey");
            }

            columnFields1.Add("year1");
            columnFields1.Add("year2");
            columnFields1.Add("year3");
            columnFields1.Add("year4");
            if (!string.IsNullOrEmpty(pageId) && (pageId == "BudChangeOverview" || pageId == "BudChangeDetail" || pageId == "ServiceUnitForUnlockedBudget"))
            {
                columnFields1.Add("year5");
            }
            columnFields1.Add("description");

            columnFields2.Add("year1");
            columnFields2.Add("year2");
            columnFields2.Add("year3");
            columnFields2.Add("year4");
            if (!string.IsNullOrEmpty(pageId) && (pageId == "BudChangeOverview" || pageId == "BudChangeDetail" || pageId == "ServiceUnitForUnlockedBudget"))
            {
                columnFields2.Add("year5");
            }
            columnFields2.Add("description");

            dynamic columnTitles1 = new JArray();
            dynamic columnTitles2 = new JArray();

            columnTitles1.Add(((langStringValues.FirstOrDefault(v => v.Key == "account_text")).Value).LangText);
            columnTitles1.Add(((langStringValues.FirstOrDefault(v => v.Key == "department_text")).Value).LangText);
            columnTitles1.Add(((langStringValues.FirstOrDefault(v => v.Key == "function_text")).Value).LangText);
            columnTitles1.Add(((langStringValues.FirstOrDefault(v => v.Key == "fp_project_text")).Value).LangText);

            columnTitles2.Add(((langStringValues.FirstOrDefault(v => v.Key == "account_text")).Value).LangText);
            columnTitles2.Add(((langStringValues.FirstOrDefault(v => v.Key == "department_text")).Value).LangText);
            columnTitles2.Add(((langStringValues.FirstOrDefault(v => v.Key == "function_text")).Value).LangText);
            columnTitles2.Add(((langStringValues.FirstOrDefault(v => v.Key == "fp_project_text")).Value).LangText);

            foreach (var fd in freeDimColumns)
            {
                if (fd.freeDimColumn == "free_dim_1")
                {
                    string freedim = "free_dim_" + 1;
                    columnTitles1.Add(freeDimColumns.Where(x => x.freeDimColumn == freedim).Select(x => x.freeDimHeader).First().ToString());
                    columnTitles2.Add(freeDimColumns.Where(x => x.freeDimColumn == freedim).Select(x => x.freeDimHeader).First().ToString());
                }
                if (fd.freeDimColumn == "free_dim_2")
                {
                    string freedim = "free_dim_" + 2;
                    columnTitles1.Add(freeDimColumns.Where(x => x.freeDimColumn == freedim).Select(x => x.freeDimHeader).First().ToString());
                    columnTitles2.Add(freeDimColumns.Where(x => x.freeDimColumn == freedim).Select(x => x.freeDimHeader).First().ToString());
                }
                if (fd.freeDimColumn == "free_dim_3")
                {
                    string freedim = "free_dim_" + 3;
                    columnTitles1.Add(freeDimColumns.Where(x => x.freeDimColumn == freedim).Select(x => x.freeDimHeader).First().ToString());
                    columnTitles2.Add(freeDimColumns.Where(x => x.freeDimColumn == freedim).Select(x => x.freeDimHeader).First().ToString());
                }
                if (fd.freeDimColumn == "free_dim_4")
                {
                    string freedim = "free_dim_" + 4;
                    columnTitles1.Add(freeDimColumns.Where(x => x.freeDimColumn == freedim).Select(x => x.freeDimHeader).First().ToString());
                    columnTitles2.Add(freeDimColumns.Where(x => x.freeDimColumn == freedim).Select(x => x.freeDimHeader).First().ToString());
                }
            }

            Dictionary<string, clsLanguageString> langStringValuesFP = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "FinancialPlan");
            if (lstAjstmt.Count > 1)
            {
                columnTitles1.Add(((langStringValuesFP.FirstOrDefault(v => v.Key == "fp_adjustment_code")).Value).LangText);
                columnTitles2.Add(((langStringValuesFP.FirstOrDefault(v => v.Key == "fp_adjustment_code")).Value).LangText);
            }
            if (lstAlter.Count > 1)
            {
                columnTitles1.Add(((langStringValuesFP.FirstOrDefault(v => v.Key == "fp_alter_code_text")).Value).LangText);
                columnTitles2.Add(((langStringValuesFP.FirstOrDefault(v => v.Key == "fp_alter_code_text")).Value).LangText);
            }

            if (!string.IsNullOrEmpty(pageId) && (pageId == "BudChangeOverview" || pageId == "BudChangeDetail" || pageId == "ServiceUnitForUnlockedBudget"))
            {
                columnTitles1.Add(((langStringValuesFP.FirstOrDefault(v => v.Key == "fp_periodic_key")).Value).LangText);
                columnTitles2.Add(((langStringValuesFP.FirstOrDefault(v => v.Key == "fp_periodic_key")).Value).LangText);
            }

            columnTitles1.Add(budgetYear.ToString());
            columnTitles1.Add((budgetYear + 1).ToString());
            columnTitles1.Add((budgetYear + 2).ToString());
            columnTitles1.Add((budgetYear + 3).ToString());
            if (!string.IsNullOrEmpty(pageId) && (pageId == "BudChangeOverview" || pageId == "BudChangeDetail" || pageId == "ServiceUnitForUnlockedBudget"))
            {
                columnTitles1.Add((budgetYear + 4).ToString());
            }
            columnTitles1.Add(langStringValues.FirstOrDefault(v => v.Key == "CAB_description").Value.LangText);

            columnTitles2.Add(budgetYear.ToString());
            columnTitles2.Add((budgetYear + 1).ToString());
            columnTitles2.Add((budgetYear + 2).ToString());
            columnTitles2.Add((budgetYear + 3).ToString());
            if (!string.IsNullOrEmpty(pageId) && (pageId == "BudChangeOverview" || pageId == "BudChangeDetail" || pageId == "ServiceUnitForUnlockedBudget"))
            {
                columnTitles2.Add((budgetYear + 4).ToString());
            }
            columnTitles2.Add(langStringValues.FirstOrDefault(v => v.Key == "CAB_description").Value.LangText);

            dynamic activeChangeColumns = new JArray();
            foreach (var col in columnFields2)
            {
                dynamic column = new JObject();
                column.title = columnTitles2[colCount];
                column.field = col;

                dynamic attributes = new JObject();
                dynamic headerAttributes = new JObject();

                if (col == "account")
                {
                    column.headerTemplate = "<span id='financingAccount'>" + columnTitles2[colCount] + "</span><span class='red'>*</span>";
                    column.template = "<span title = '#=account.accountText#' >#=account.accountValue#</span>";

                    attributes.style = "border-left:none;text-align:left";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "department")
                {
                    column.headerTemplate = "<span id='financingDepartment'>" + columnTitles2[colCount] + "</span><span class='red'>*</span>";
                    column.template = "<span title = '#=department.departmentText#' >#=department.departmentValue#</span>";

                    attributes.style = "border-left:none;text-align:left";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "functionn")
                {
                    column.headerTemplate = "<span id='financingFunction'>" + columnTitles2[colCount] + "</span><span class='red'>*</span>";
                    column.template = "<span title = '#=functionn.functionText#' >#=functionn.functionValue#</span>";

                    attributes.style = "border-left: none; text-align:left;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "project")
                {
                    column.headerTemplate = "<span>" + columnTitles2[colCount] + "</span>";
                    column.template = "<span title = '#=project.project_name#' >#=project.fk_project_code#</span>";

                    attributes.style = "border-left: none; text-align:left;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "freeDim1")
                {
                    column.headerTemplate = "<span id='financingFreeDim1'>" + columnTitles2[colCount] + "</span>";
                    column.template = "<span title = '#=freeDim1.freedim_name#' >#=freeDim1.fk_freedim_code#</span>";

                    attributes.style = "border-left: none; text-align:left;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "freeDim2")
                {
                    column.headerTemplate = "<span id='financingFreeDim2'>" + columnTitles2[colCount] + "</span>"; ;
                    column.template = "<span title = '#=freeDim2.freedim_name#' >#=freeDim2.fk_freedim_code#</span>";

                    attributes.style = "border-left: none; text-align:left;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "freeDim3")
                {
                    column.headerTemplate = "<span id='financingFreeDim3'>" + columnTitles2[colCount] + "</span>"; ;
                    column.template = "<span title = '#=freeDim2.freedim_name#' >#=freeDim3.fk_freedim_code#</span>";

                    attributes.style = "border-left: none; text-align:left;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "freeDim4")
                {
                    column.headerTemplate = "<span id='financingFreeDim4'>" + columnTitles2[colCount] + "</span>"; ;
                    column.template = "<span title = '#=freeDim4.freedim_name#' >#=freeDim4.fk_freedim_code#</span>";

                    attributes.style = "border-left: none; text-align:left;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "adjustmentCode")
                {
                    column.headerTemplate = "<span id='financingAdjCode'>" + columnTitles2[colCount] + "</span><span class='red' id='commonFinAdjustmentCodeMandatory'></span>";
                    column.template = "<span title = '#=adjustmentCode.value#' >#=adjustmentCode.key#</span>";

                    attributes.style = "border-left: none; text-align:left;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "alterCode")
                {
                    column.headerTemplate = "<span id='financingAlterCode'>" + columnTitles2[colCount] + "</span><span class='red'>*</span>";
                    column.template = "<span title='#=alterCode.value#'>#=alterCode.key#</span>";

                    attributes.style = "border-left: none; text-align:left;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "periodicKey")
                {
                    column.headerTemplate = "<span id='financingperiodicKey'>" + columnTitles2[colCount] + "</span><span class='red'>*</span>";
                    column.template = "<span title='#=periodicKey.value#'>#=periodicKey.key#</span>";

                    attributes.style = "border-left: none; text-align:left;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "year1")
                {
                    column.headerTemplate = "<span id='MODULESYear1'>" + columnTitles2[colCount] + "</span>";
                    column.template = "#= (year1 == null) ? '0' :  kendo.toString(year1, 'n0')  #";

                    attributes.style = "border-left: none; text-align:right;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    column.width = 100;
                }
                if (col == "year2")
                {
                    column.headerTemplate = "<span id='MODULESYear2'>" + columnTitles2[colCount] + "</span>";
                    column.template = "#= (year2 == null) ? '0' :  kendo.toString(year2, 'n0')  #";

                    attributes.style = "border-left: none; text-align:right;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    column.width = 100;
                }
                if (col == "year3")
                {
                    column.headerTemplate = "<span id='MODULESYear3'>" + columnTitles2[colCount] + "</span>";
                    column.template = "#= (year3 == null) ? '0' :  kendo.toString(year3, 'n0')  #";

                    attributes.style = "border-left: none; text-align:right;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    column.width = 100;
                }
                if (col == "year4")
                {
                    column.headerTemplate = "<span id='MODULESYear4'>" + columnTitles2[colCount] + "</span>";
                    column.template = "#= (year4 == null) ? '0' :  kendo.toString(year4, 'n0')  #";

                    attributes.style = "border-left: none; text-align:right;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    column.width = 100;
                }
                if (col == "year5")
                {
                    column.headerTemplate = "<span id='MODULESYear5'>" + columnTitles2[colCount] + "</span>";
                    column.template = "#= (year5 == null) ? '0' :  kendo.toString(year5, 'n0')  #";

                    attributes.style = "border-left: none; text-align:right;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    column.width = 100;
                }
                if (col == "description")
                {
                    column.headerTemplate = "<span>" + columnTitles2[colCount] + "</span>";
                    column.template = "#= (description == '') ? 'Legg inn tekst':description#";

                    attributes.style = "border-left: none;white-space:normal;text-align:left;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    column.width = 150;
                }
                column.format = null;
                activeChangeColumns.Add(column);
                colCount++;
            }
            actions.activeChangeColumns = activeChangeColumns;
            colCount = 0;
            dynamic nonActiveChangeColumns = new JArray();
            foreach (var col in columnFields1)
            {
                dynamic column = new JObject();
                column.title = columnTitles1[colCount];
                column.field = col;

                dynamic attributes = new JObject();
                dynamic headerAttributes = new JObject();

                if (col == "account")
                {
                    column.template = "<span title = '#=account.accountText#' >#=account.accountValue#</span>";

                    attributes.style = "text-align:left;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "department")
                {
                    column.template = "<span title = '#=department.departmentText#' >#=department.departmentValue#</span>";

                    attributes.style = "text-align:left;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "functionn")
                {
                    column.template = "<span title = '#=functionn.functionText#' >#=functionn.functionValue#</span>";

                    attributes.style = "text-align:left;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "project")
                {
                    column.template = "<span title = '#=project.project_name#' >#=project.fk_project_code#</span>";

                    attributes.style = "text-align:left;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "freeDim1")
                {
                    column.template = "<span title = '#=freeDim1.freedim_name#' >#=freeDim1.fk_freedim_code#</span>";

                    attributes.style = "text-align:left;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "freeDim2")
                {
                    column.template = "<span title = '#=freeDim2.freedim_name#' >#=freeDim2.fk_freedim_code#</span>";

                    attributes.style = "text-align:left;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "freeDim3")
                {
                    column.template = "<span title = '#=freeDim3.freedim_name#' >#=freeDim3.fk_freedim_code#</span>";

                    attributes.style = "text-align:left;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "freeDim4")
                {
                    column.template = "<span title = '#=freeDim4.freedim_name#' >#=freeDim4.fk_freedim_code#</span>";

                    attributes.style = "text-align:left;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "adjustmentCode")
                {
                    column.template = "<span title = '#=adjustmentCode.value#' >#=adjustmentCode.key#</span>";

                    attributes.style = "text-align:left;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "alterCode")
                {
                    column.template = "<span title='#=alterCode.value#'>#=alterCode.key#</span>";

                    attributes.style = "text-align:left;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "periodicKey")
                {
                    column.template = "<span title='#=periodicKey.value#'>#=periodicKey.key#</span>";
                    attributes.style = "text-align:left;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "year1")
                {
                    column.template = "#= (year1 == null) ? '0' :  kendo.toString(year1, 'n0')  #";

                    attributes.style = "text-align:right;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    column.width = 100;
                }
                if (col == "year2")
                {
                    column.template = "#= (year2 == null) ? '0' :  kendo.toString(year2, 'n0')  #";

                    attributes.style = "text-align:right;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    column.width = 100;
                }
                if (col == "year3")
                {
                    column.template = "#= (year3 == null) ? '0' :  kendo.toString(year3, 'n0')  #";

                    attributes.style = "text-align:right;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    column.width = 100;
                }
                if (col == "year4")
                {
                    column.template = "#= (year4 == null) ? '0' :  kendo.toString(year4, 'n0')  #";

                    attributes.style = "text-align:right;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    column.width = 100;
                }
                if (col == "year5")
                {
                    column.template = "#= (year5 == null) ? '0' :  kendo.toString(year5, 'n0')  #";

                    attributes.style = "text-align:right;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    column.width = 100;
                }
                if (col == "description")
                {
                    column.template = "#=description#";

                    attributes.style = "text-align:left;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    column.width = 150;
                }
                column.headerTemplate = null;
                column.format = null;
                nonActiveChangeColumns.Add(column);
                colCount++;
            }
            actions.nonActiveChangeColumns = nonActiveChangeColumns;

            actions.amountFormat = "#,##";
            string username = "";
            string lastupdated = "";
            if (lst.Count > 0)
            {
                int userid = Convert.ToInt32((lst.FirstOrDefault()).user.ToString());
                var user = await consequenceAdjustedBudgetDbContext.vwUserDetails.Where(x => x.pk_id == userid && x.client_id == clientId && x.tenant_id == userDetails.tenant_id && x.is_active_tenant).ToListAsync();
                if (user != null && user.Any())
                {
                    if (!string.IsNullOrEmpty(user.FirstOrDefault().first_name) && !string.IsNullOrEmpty(user.FirstOrDefault().last_name))
                    {
                        username = user.First().first_name + " " + user.First().last_name;
                    }
                    else if (!string.IsNullOrEmpty(user.FirstOrDefault().first_name))
                    {
                        username = user.First().first_name;
                    }
                    else if (!string.IsNullOrEmpty(user.FirstOrDefault().last_name))
                    {
                        username = user.First().last_name;
                    }
                }
                else
                {
                    username = "";
                }

                lastupdated = (lst.FirstOrDefault()).lastupdated.ToString("dd.MM.yyyy");
            }

            actions.user = username;
            actions.lastUpdated = lastupdated;

            dynamic defaultGriData = await GetDefaultsForBudgetChangeAsync(actionType, userId, orgId, serviceId, pageId, budgetYear);
            var lstchange = (pageId != "ServiceUnitForUnlockedBudget") ?
                            (from m in main
                             where (m.pk_action_id == actionId && m.fk_tenant_id == userDetails.tenant_id && m.budget_year == budgetYear && departmentsList.Contains(m.department_code.ToUpper())
                             && m.fk_change_id == changeid && m.fk_change_id != 0)
                             select new clsBudgetChangesActions
                             {
                                 actionID = m.pk_action_id,
                                 actionDescription = m.thdes,
                                 consequence = m.consequence,
                                 pk_id = m.pk_id,
                                 accountCode = m.fk_account_code,
                                 accountName = m.accountName,
                                 deptCode = m.department_code,
                                 functionCode = m.function_code,
                                 functionName = m.functionName,
                                 projectCode = m.project_code,
                                 projectName = m.projectName,
                                 freedimCode1 = m.free_dim_1,
                                 freedimName1 = m.freedim1Name,

                                 freedimCode2 = m.free_dim_2,
                                 freedimName2 = m.freedim2Name,

                                 freedimCode3 = m.free_dim_3,
                                 freedimName3 = m.freedim3Name,

                                 freedimCode4 = m.free_dim_4,
                                 freedimName4 = m.freedim4Name,

                                 year1Ammount = m.year_1_amount,
                                 year2Ammount = m.year_2_amount,
                                 year3Ammount = m.year_3_amount,
                                 year4Ammount = m.year_4_amount,
                                 year5Ammount = m.year_5_amount,
                                 fk_area_id = m.fk_area_id == null ? 0 : m.fk_area_id.Value,
                                 tag = m.tag == null ? "" : m.tag,
                                 tags = m.tags == null ? "" : m.tags,
                                 priority = m.priority == null ? 0 : m.priority.Value,
                                 long_description = m.long_description == null ? "" : m.long_description,
                                 user = m.updated_by,
                                 lastupdated = m.updated,
                                 display_financial_plan_flag = m.display_financial_plan_flag,
                                 display_cab_flag = m.display_cab_flag.HasValue ? m.display_cab_flag.Value : false,
                                 display_description_apendix_flag = m.display_description_apendix_flag,
                                 description = m.description == null ? String.Empty : m.description,
                                 cat_id = m.fk_cat_id,
                                 financial_plan_description = m.financial_plan_description,
                                 fk_adjustment_code = m.fk_adjustment_code,
                                 fk_user_adj_code = m.fk_user_adj_code,
                                 adjustmentCodeName = m.adjustmentName,
                                 fk_alter_code = m.fk_alter_code,
                                 alterCodeName = m.alterCodeName,
                                 consequence_flag = m.consequence_flag,
                                 different_external_description_flag = m.different_external_description_flag,
                                 change_text_flag = m.change_text_flag,
                                 tdUpdated = m.tdUpdated,
                                 finished_date = m.finished_date,
                                 periodicKey = m.fk_key_id.ToString(),
                                 DisplayZeroAction = m.display_zero_action,
                                 update_annual_budget = m.update_annual_budget,
                                 update_annual_budget_next_year = m.update_annual_budget_next_year,
                                 update_next_year_finplan = m.update_next_year_finplan
                             }).Distinct().ToList()
                             :
                             new List<clsBudgetChangesActions>();

            lstchange.RemoveAll(x => (x.year1Ammount == 0 && x.year2Ammount == 0 && x.year3Ammount == 0 && x.year4Ammount == 0));

            if (pageId == "BudChangeOverview" && adjCode != null)
            {
                lstchange = lstchange.Where(x => x.fk_user_adj_code == adjCode).ToList();
            }

            var changeresult = (from a in lstOrgIdDepartments
                                select new clsBudgetChangesActions
                                {
                                    actionID = 0,
                                    actionDescription = string.Empty,
                                    pk_id = 0,
                                    accountCode = string.Empty,
                                    accountName = string.Empty,
                                    deptCode = string.Empty,
                                    deptName = string.Empty,
                                    functionCode = string.Empty,
                                    functionName = string.Empty,
                                    projectCode = string.Empty,
                                    projectName = string.Empty,
                                    freedimCode1 = string.Empty,
                                    freedimName1 = string.Empty,

                                    freedimCode2 = string.Empty,
                                    freedimName2 = string.Empty,

                                    freedimCode3 = string.Empty,
                                    freedimName3 = string.Empty,

                                    freedimCode4 = string.Empty,
                                    freedimName4 = string.Empty,

                                    year1Ammount = 0.0M,
                                    year2Ammount = 0.0M,
                                    year3Ammount = 0.0M,
                                    year4Ammount = 0.0M,
                                    year5Ammount = 0.0M,
                                    user = 0,
                                    lastupdated = DateTime.UtcNow,
                                    display_financial_plan_flag = false,
                                    display_cab_flag = false,
                                    display_description_apendix_flag = false,
                                    description = string.Empty,
                                    cat_id = Guid.Empty,
                                    financial_plan_description = string.Empty,
                                    fk_adjustment_code = string.Empty,
                                    fk_alter_code = string.Empty,
                                    consequence_flag = false,
                                    different_external_description_flag = false,
                                    change_text_flag = false,
                                    finished_date = DateTime.UtcNow.Date,
                                    periodicKey = string.Empty,
                                    DisplayZeroAction = false
                                }).ToList();

            changeresult = (from a in lstchange
                            join b in lstOrgIdDepartments on a.deptCode.ToUpper() equals b.departmentValue.ToUpper()
                            select new clsBudgetChangesActions
                            {
                                actionID = a.actionID,
                                actionDescription = a.actionDescription,
                                pk_id = a.pk_id,
                                accountCode = a.accountCode,
                                accountName = a.accountName,
                                deptCode = a.deptCode,
                                deptName = b.departmentText,
                                functionCode = a.functionCode,
                                functionName = a.functionName,
                                projectCode = a.projectCode,
                                projectName = a.projectName,
                                freedimCode1 = a.freedimCode1,
                                freedimName1 = a.freedimName1,

                                freedimCode2 = a.freedimCode2,
                                freedimName2 = a.freedimName2,

                                freedimCode3 = a.freedimCode3,
                                freedimName3 = a.freedimName3,

                                freedimCode4 = a.freedimCode4,
                                freedimName4 = a.freedimName4,

                                year1Ammount = a.year1Ammount,
                                year2Ammount = a.year2Ammount,
                                year3Ammount = a.year3Ammount,
                                year4Ammount = a.year4Ammount,
                                year5Ammount = a.year5Ammount,
                                user = a.user,
                                lastupdated = a.lastupdated,
                                display_financial_plan_flag = a.display_financial_plan_flag,
                                display_cab_flag = a.display_cab_flag,
                                display_description_apendix_flag = a.display_description_apendix_flag,
                                description = a.description,
                                cat_id = a.cat_id,
                                financial_plan_description = a.financial_plan_description,
                                fk_adjustment_code = a.fk_adjustment_code,
                                fk_alter_code = a.fk_alter_code,
                                adjustmentCodeName = a.adjustmentCodeName,
                                alterCodeName = a.alterCodeName,
                                consequence_flag = a.consequence_flag,
                                different_external_description_flag = a.different_external_description_flag,
                                change_text_flag = a.change_text_flag,
                                tdUpdated = a.tdUpdated,
                                finished_date = a.finished_date,
                                periodicKey = a.periodicKey,
                                DisplayZeroAction = a.DisplayZeroAction
                            }).ToList();

            ActionDetailsTotalbdtchange.year1 = 0;
            ActionDetailsTotalbdtchange.year2 = 0;
            ActionDetailsTotalbdtchange.year3 = 0;
            ActionDetailsTotalbdtchange.year4 = 0;
            if (!string.IsNullOrEmpty(pageId) && (pageId == "BudChangeOverview" || pageId == "BudChangeDetail" || pageId == "ServiceUnitForUnlockedBudget"))
            {
                ActionDetailsTotalbdtchange.year5 = 0;
            }

            if (lstchange.Count > 0)
            {
                consequenceAdjustedBudgetDbContext.Database.SetCommandTimeout(600);
                List<tbu_trans_detail> lstTbuTransDetail = await consequenceAdjustedBudgetDbContext.tbu_trans_detail.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                                                   && x.budget_year == budgetYear
                                                                                                                   && x.fk_action_id == actionId).ToListAsync();

                actions.actionId = actionId;
                actions.finishedDate = (lst.FirstOrDefault() == null || lst.FirstOrDefault().finished_date == null) ? new DateTime(2099, 12, 31) : lstchange.FirstOrDefault().finished_date;
                actions.actionDescription = (lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).actionDescription;
                actions.consequence = (lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).consequence;
                actions.fk_area_id = (lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).fk_area_id;
                actions.tag = (lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).tag;
                actions.priority = (lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).priority;
                actions.long_description = (lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).long_description;
                actions.groupId = actionType.ToString();
                actions.groupName = actionTypeDesc == null ? "" : actionTypeDesc.action_type_descr;
                actions.display_financial_plan_flag = actionId == 0 ? false : (lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).display_financial_plan_flag;
                actions.display_cab_flag = actionId == 0 ? false : (lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).display_cab_flag;
                actions.display_description_apendix_flag = (lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).display_description_apendix_flag;
                actions.cat_id = (lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).cat_id;
                actions.financial_plan_description = (lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).financial_plan_description;
                actions.fk_adjustment_code = (lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).fk_adjustment_code;
                actions.fk_alter_code = (lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).fk_alter_code;
                actions.consequence_flag = (lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).consequence_flag;
                actions.different_external_description_flag = (lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).different_external_description_flag;
                actions.change_text_flag = actionId == 0 ? false : (lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).change_text_flag;
                actions.DisplayZeroAction = (lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).DisplayZeroAction;
                dynamic gridDatabdtchange = new JArray();
                if (!string.IsNullOrEmpty(pageId) && (pageId == "ServiceUnitForUnlockedBudget"))
                {
                    foreach (var a in changeresult)
                    {
                        dynamic ActionDetailsWithBudgetChange = new JObject();
                        ActionDetailsWithBudgetChange.id = a.pk_id;
                        ActionDetailsWithBudgetChange.guid = lstTbuTransDetail.FirstOrDefault(x => x.fk_action_id == actionId
                                                                      && x.fk_account_code == a.accountCode
                                                                      && x.department_code == a.deptCode
                                                                      && x.fk_function_code == a.functionCode
                                                                      && x.fk_project_code == a.projectCode
                                                                      && x.free_dim_1 == a.freedimCode1) == null ? Guid.NewGuid() :
                                   lstTbuTransDetail.FirstOrDefault(x => x.fk_action_id == actionId
                                                                      && x.fk_account_code == a.accountCode
                                                                      && x.department_code == a.deptCode
                                                                      && x.fk_function_code == a.functionCode
                                                                      && x.fk_project_code == a.projectCode
                                                                      && x.free_dim_1 == a.freedimCode1).pk_id;
                        ActionDetailsWithBudgetChange.account = GenerateDynamicAccountObject(a.accountCode, a.accountName);

                        ActionDetailsWithBudgetChange.department = GenerateDynamicDepartmentObject(a.deptCode, a.deptName);

                        ActionDetailsWithBudgetChange.functionn = GenerateDynamicFunctionObject(a.functionCode, a.functionName);

                        ActionDetailsWithBudgetChange.project = GenerateDynamicProjectDimObject(a.projectCode, a.projectName);

                        if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_1") != null)
                        {
                            ActionDetailsWithBudgetChange.freeDim1 = GenerateDynamicFreeDimObject(a.freedimCode1, a.freedimName1);
                        }
                        if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_2") != null)
                        {
                            ActionDetailsWithBudgetChange.freeDim2 = GenerateDynamicFreeDimObject(a.freedimCode2, a.freedimName2);
                        }
                        if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_3") != null)
                        {
                            ActionDetailsWithBudgetChange.freeDim3 = GenerateDynamicFreeDimObject(a.freedimCode3, a.freedimName3);
                        }
                        if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_4") != null)
                        {
                            ActionDetailsWithBudgetChange.freeDim4 = GenerateDynamicFreeDimObject(a.freedimCode4, a.freedimName4);
                        }

                        if (lstAjstmt.Count > 1)
                        {
                            ActionDetailsWithBudgetChange.adjustmentCode = GenerateDynamicAdjustmentCodeObject(a.fk_adjustment_code, a.adjustmentCodeName);
                        }
                        if (lstAlter.Count > 1)
                        {
                            ActionDetailsWithBudgetChange.alterCode = GenerateDynamicAlterCodeObject(a.fk_alter_code, a.alterCodeName);
                        }
                        if (!string.IsNullOrEmpty(pageId) && (pageId == "BudChangeOverview" || pageId == "BudChangeDetail" || pageId == "ServiceUnitForUnlockedBudget"))
                        {
                            var pdKeyVal = (await _utility.GetTenantDBContextAsync()).tco_periodic_key.FirstOrDefault(x => x.key_id.ToString() == a.periodicKey.ToString());
                            a.periodicVal = pdKeyVal != null ? pdKeyVal.key_description : "";
                            ActionDetailsWithBudgetChange.periodicKey = GenerateDynamicPeriodicKeyObject(a.periodicKey, a.periodicVal);
                        }

                        ActionDetailsWithBudgetChange.year1 = a.year1Ammount.Value / 1000;
                        ActionDetailsWithBudgetChange.year2 = a.year2Ammount.Value / 1000;
                        ActionDetailsWithBudgetChange.year3 = a.year3Ammount.Value / 1000;
                        ActionDetailsWithBudgetChange.year4 = a.year4Ammount.Value / 1000;
                        ActionDetailsWithBudgetChange.year5 = a.year5Ammount.Value / 1000;
                        ActionDetailsWithBudgetChange.description = a.description;

                        gridDatabdtchange.Add(ActionDetailsWithBudgetChange);
                    }

                    year1totalbdtchange = resultActive.Sum(x => x.year1Ammount ?? 0) / 1000;
                    year2totalbdtchange = resultActive.Sum(x => x.year2Ammount ?? 0) / 1000;
                    year3totalbdtchange = resultActive.Sum(x => x.year3Ammount ?? 0) / 1000;
                    year4totalbdtchange = resultActive.Sum(x => x.year4Ammount ?? 0) / 1000;
                    year5totalbdtchange = resultActive.Sum(x => x.year5Ammount ?? 0) / 1000;
                }
                else
                {
                    foreach (var a in changeresult)
                    {
                        dynamic ActionDetailsWithBudgetChange = new JObject();
                        ActionDetailsWithBudgetChange.id = a.pk_id;
                        ActionDetailsWithBudgetChange.guid = lstTbuTransDetail.FirstOrDefault(x => x.fk_action_id == actionId
                                                                      && x.fk_account_code == a.accountCode
                                                                      && x.department_code == a.deptCode
                                                                      && x.fk_function_code == a.functionCode
                                                                      && x.fk_project_code == a.projectCode
                                                                      && x.free_dim_1 == a.freedimCode1) == null ? Guid.NewGuid() :
                                   lstTbuTransDetail.FirstOrDefault(x => x.fk_action_id == actionId
                                                                      && x.fk_account_code == a.accountCode
                                                                      && x.department_code == a.deptCode
                                                                      && x.fk_function_code == a.functionCode
                                                                      && x.fk_project_code == a.projectCode
                                                                      && x.free_dim_1 == a.freedimCode1).pk_id;
                        ActionDetailsWithBudgetChange.account = GenerateDynamicAccountObject(a.accountCode, a.accountName);

                        ActionDetailsWithBudgetChange.department = GenerateDynamicDepartmentObject(a.deptCode, a.deptName);

                        ActionDetailsWithBudgetChange.functionn = GenerateDynamicFunctionObject(a.functionCode, a.functionName);

                        ActionDetailsWithBudgetChange.project = GenerateDynamicProjectDimObject(a.projectCode, a.projectName);

                        if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_1") != null)
                        {
                            ActionDetailsWithBudgetChange.freeDim1 = GenerateDynamicFreeDimObject(a.freedimCode1, a.freedimName1);
                        }
                        if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_2") != null)
                        {
                            ActionDetailsWithBudgetChange.freeDim2 = GenerateDynamicFreeDimObject(a.freedimCode2, a.freedimName2);
                        }
                        if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_3") != null)
                        {
                            ActionDetailsWithBudgetChange.freeDim3 = GenerateDynamicFreeDimObject(a.freedimCode3, a.freedimName3);
                        }
                        if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_4") != null)
                        {
                            ActionDetailsWithBudgetChange.freeDim4 = GenerateDynamicFreeDimObject(a.freedimCode4, a.freedimName4);
                        }

                        if (lstAjstmt.Count > 1)
                        {
                            //if( adjCode == null)
                            //{
                            ActionDetailsWithBudgetChange.adjustmentCode = GenerateDynamicAdjustmentCodeObject(a.fk_adjustment_code, a.adjustmentCodeName);
                            //}
                            //else
                            //{
                            //  ActionDetailsWithBudgetChange.adjustmentCode = GenerateDynamicAdjustmentCodeObject(adjCode, adjCode);
                            //}
                        }
                        if (lstAlter.Count > 1)
                        {
                            ActionDetailsWithBudgetChange.alterCode = GenerateDynamicAlterCodeObject(a.fk_alter_code, a.alterCodeName);
                        }
                        if (!string.IsNullOrEmpty(pageId) && (pageId == "BudChangeOverview" || pageId == "BudChangeDetail"))
                        {
                            var pdKeyVal = (await _utility.GetTenantDBContextAsync()).tco_periodic_key.FirstOrDefault(x => x.key_id.ToString() == a.periodicKey.ToString());
                            a.periodicVal = pdKeyVal != null ? pdKeyVal.key_description : "";
                            ActionDetailsWithBudgetChange.periodicKey = GenerateDynamicPeriodicKeyObject(a.periodicKey, a.periodicVal);
                        }

                        ActionDetailsWithBudgetChange.year1 = a.year1Ammount.Value / 1000;
                        ActionDetailsWithBudgetChange.year2 = a.year2Ammount.Value / 1000;
                        ActionDetailsWithBudgetChange.year3 = a.year3Ammount.Value / 1000;
                        ActionDetailsWithBudgetChange.year4 = a.year4Ammount.Value / 1000;
                        if (!string.IsNullOrEmpty(pageId) && (pageId == "BudChangeOverview" || pageId == "BudChangeDetail"))
                        {
                            ActionDetailsWithBudgetChange.year5 = a.year5Ammount.Value / 1000;
                        }
                        ActionDetailsWithBudgetChange.description = a.description;

                        year1totalbdtchange = year1totalbdtchange + (a.year1Ammount.Value / 1000);
                        year2totalbdtchange = year2totalbdtchange + (a.year2Ammount.Value / 1000);
                        year3totalbdtchange = year3totalbdtchange + (a.year3Ammount.Value / 1000);
                        year4totalbdtchange = year4totalbdtchange + (a.year4Ammount.Value / 1000);
                        year5totalbdtchange = year5totalbdtchange + (a.year5Ammount.Value / 1000);

                        gridDatabdtchange.Add(ActionDetailsWithBudgetChange);
                    }
                }
                ActionDetailsTotalbdtchange.year1 = year1totalbdtchange;
                ActionDetailsTotalbdtchange.year2 = year2totalbdtchange;
                ActionDetailsTotalbdtchange.year3 = year3totalbdtchange;
                ActionDetailsTotalbdtchange.year4 = year4totalbdtchange;
                if (!string.IsNullOrEmpty(pageId) && (pageId == "BudChangeOverview" || pageId == "BudChangeDetail" || pageId == "ServiceUnitForUnlockedBudget"))
                {
                    ActionDetailsTotalbdtchange.year5 = year5totalbdtchange;
                }

                actions.Add("activeChangeData", gridDatabdtchange);
                actions.isDefaultData = false;
            }
            ActionDetailsTotalfinaltotal.year1 = year1total + year1totalbdtchange;
            ActionDetailsTotalfinaltotal.year2 = year2total + year2totalbdtchange;
            ActionDetailsTotalfinaltotal.year3 = year3total + year3totalbdtchange;
            ActionDetailsTotalfinaltotal.year4 = year4total + year4totalbdtchange;
            if (!string.IsNullOrEmpty(pageId) && (pageId == "BudChangeOverview" || pageId == "BudChangeDetail" || pageId == "ServiceUnitForUnlockedBudget"))
            {
                ActionDetailsTotalfinaltotal.year5 = year5total + year5totalbdtchange;
            }

            dynamic totalsDataArray = new JArray();
            ActionDetailsTotalbdtchange.Add("description", "");
            ActionDetailsTotalfinaltotal.Add("description", "");
            totalsDataArray.Add(ActionDetailsTotalbdtchange);

            totalsDataArray.Add(ActionDetailsTotalfinaltotal);
            actions.Add("totalsData", totalsDataArray);

            actions.Add("defaultModel", defaultGriData.gridData);
            actions.updateYearlyBudget = (pageId == "BudChangeOverview" || pageId == "BudChangeDetail" || pageId == "ServiceUnitForUnlockedBudget") ? true : false;
            actions.updateNextYearFinplan = lstchange.Any() && lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault() != null ? (lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).update_next_year_finplan : false;
            actions.updateYearlyBudgetNxtYear = lstchange.Any() && lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault() != null ? (lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).update_annual_budget_next_year : false;
            actions.activeChangeIdDesc = await GetBudgetPhaseIdDescriptionsAsync(userId, budgetYear, actionId, changeid);

            if (!string.IsNullOrEmpty(pageId) && (pageId == "BudChangeOverview" || pageId == "BudChangeDetail"))
            {
                var dataActiveBudgetChanges = await (from t in consequenceAdjustedBudgetDbContext.tfp_budget_changes
                                                     where t.fk_tenant_id == userDetails.tenant_id
                                                        && t.budget_year == (budgetYear + 1)
                                                        && t.status == 1
                                                        && t.org_budget_flag == 1
                                                     orderby t.change_date descending
                                                     select t).ToListAsync();

                var nextYearData = await (from th in consequenceAdjustedBudgetDbContext.tfp_trans_header
                                          join td in consequenceAdjustedBudgetDbContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                                                                                                     equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                                          where th.fk_tenant_id == userDetails.tenant_id && td.budget_year == (budgetYear + 1) && th.previous_pk_action_id == actionId
                                          select td.fk_change_id).ToListAsync();

                if (nextYearData != null && nextYearData.Count() > 0)
                {
                    dynamic selectedChangeId = new JObject();
                    selectedChangeId.key = nextYearData.FirstOrDefault();
                    selectedChangeId.value = dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == nextYearData.FirstOrDefault()) == null ? "" : dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == nextYearData.FirstOrDefault()).budget_name;
                    actions.selectedChangeId = selectedChangeId;
                }
                else
                {
                    dynamic selectedChangeId = new JObject();
                    selectedChangeId.key = -1;
                    selectedChangeId.value = string.Empty;
                    actions.selectedChangeId = selectedChangeId;
                }
            }
            else
            {
                dynamic selectedChangeId = new JObject();
                selectedChangeId.key = -1;
                selectedChangeId.value = string.Empty;
                actions.selectedChangeId = selectedChangeId;
            }

            actions.createNextYearData = lstchange.Any() ? lstchange.FirstOrDefault().update_next_year_finplan : false;

            vw_tco_parameters finplanEstablished = await consequenceAdjustedBudgetDbContext.vw_tco_parameters.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                                                && x.param_name == "FINPLAN_ESTABLISHED"
                                                                                                                && x.param_value == (budgetYear + 1).ToString() && x.active == 1);
            actions.Add("longDescriptionId", longDescriptionId);
            actions.showBudgetChangeControls = finplanEstablished == null ? false : true;
            var goalData = totalData.Any() ? totalData.FirstOrDefault().goalsTags : null;
            Guid actgoalId = (actionId != 0 ? (goalData != null ? (Guid)goalData : Guid.Empty) : Guid.Empty);
            actions.Add("goalTagList", JToken.FromObject(actgoalId));

            var targetData = totalData.Any() ? totalData.FirstOrDefault().targetTags : null;
            Guid acttargetId = (actionId != 0 ? (targetData != null ? (Guid)targetData : Guid.Empty) : Guid.Empty);
            actions.Add("targetTagList", JToken.FromObject(acttargetId));

            var strategyData = totalData.Any() ? totalData.FirstOrDefault().strategyTags : null;
            int actstrategyId = actionId != 0 ? Convert.ToInt32(strategyData) : 0;
            actions.Add("strategyTagList", JToken.FromObject(actstrategyId));

            // name of goal and list of ungoal and focus area name
            await GetGoalFocusAreaName(userDetails.tenant_id, actgoalId, acttargetId, actstrategyId, actions);
            var tagList = await ActionTagsList(userDetails.tenant_id, tagIdsLst);
            actions.Add("tagList", JToken.FromObject(tagList));
            return actions;
        }

        public dynamic GetStrategyActionDetails(Guid actionId, string userID, string orgId, string serviceId,
            string serviceUnitId = "", int? orgLevel = null, int budgetYear = 0)
        {
            var result =
                GetStrategyActionDetailsAsync(actionId, userID, orgId, serviceId, serviceUnitId, orgLevel, budgetYear)
                    .GetAwaiter().GetResult();
            return result;
        }

        private async Task<Guid> GenerateLongDescGuid(int actionId, string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            tfp_trans_header tfp = await dbContext.tfp_trans_header.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_action_id == actionId);
            if (string.IsNullOrEmpty(tfp.long_description_id.ToString()))
            {
                Guid guid = Guid.NewGuid();
                tfp.long_description_id = guid;
                tfp.updated = DateTime.UtcNow;
                tfp.updated_by = userDetails.pk_id;
                await dbContext.SaveChangesAsync();
                return guid;
            }
            return tfp.long_description_id.Value;
        }

        public async Task<dynamic> GetStrategyActionDetailsAsync(Guid actionId, string userID, string orgId, string serviceId, string serviceUnitId = "", int? orgLevel = null, int budgetYear = 0)
        {
            if (string.IsNullOrEmpty(serviceId) || serviceId == "undefined" || serviceId == "null")
            {
                serviceId = string.Empty;
            }
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userID, _utility.GetForecastPeriod(budgetYear, 1));

            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();

            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ConsequenceAdjustedBudget");
            List<string> departmentsList = new List<string>();
            List<string> functionsList = new List<string>();
            List<clsOrgStructure> lstOrgStructure;
            clsOrgStructureLevelDetails tenantOrgLevelDetails = null;
            List<List<string>> DepartmentsAndFunctions = new List<List<string>>();

            List<clsOrgIdAndDepartments> lstAllServiceAreaDepts = await (from t in consequenceAdjustedBudgetDbContext.tco_departments
                                                                         where t.fk_tenant_id == userDetails.tenant_id
                                                                         select new clsOrgIdAndDepartments
                                                                         {
                                                                             departmentValue = t.pk_department_code,
                                                                             departmentText = t.department_name
                                                                         }).ToListAsync();

            List<clsOrgIdAndDepartments> lstOrgIdDepartments = new List<clsOrgIdAndDepartments>();

            vw_tco_parameters parameterizedFinPlanLevel1 = null;
            vw_tco_parameters parameterizedFinPlanLevel2 = null;
            string strFinplanLevel1 = string.Empty;
            string strFinplanLevel2 = string.Empty;

            strFinplanLevel1 = "FINPLAN_LEVEL_1";
            strFinplanLevel2 = "FINPLAN_LEVEL_2";
            lstOrgStructure = await _utility.GetTenantOrgStructureAsync(orgVersionContent, userID, strFinplanLevel1, strFinplanLevel2);

            tenantOrgLevelDetails = _utility.GetTenantOrgLevelDetails(lstOrgStructure, strFinplanLevel1, strFinplanLevel2);
            if (string.IsNullOrEmpty(orgId) && string.IsNullOrEmpty(serviceId) && !string.IsNullOrEmpty(serviceUnitId))
            {
                Dictionary<string, string> dictServiceAreaDepartments = await _staffPlanning.GetServiceAreaDepartmentsForBudgetEntriesAsync(userID, serviceUnitId, budgetYear);
                List<string> departmentCodes = dictServiceAreaDepartments.Select(x => x.Key).ToList();
                DepartmentsAndFunctions.Add(departmentCodes);
                departmentsList = DepartmentsAndFunctions[0];
            }
            else
            {
                if (parameterizedFinPlanLevel1 != null || parameterizedFinPlanLevel2 != null)
                {
                    DepartmentsAndFunctions = _utility.GetDepartmentsAndFunctionsForOrgStructureForDynamicLevels(lstOrgStructure, string.IsNullOrEmpty(orgId) ? null : orgId, serviceId, tenantOrgLevelDetails.level1, tenantOrgLevelDetails.level2, strFinplanLevel1, strFinplanLevel2);
                }
                else
                {
                    DepartmentsAndFunctions = _utility.GetDepartmentsAndFunctionsForOrgStructure(lstOrgStructure, string.IsNullOrEmpty(orgId) ? null : orgId, serviceId, tenantOrgLevelDetails.level1, tenantOrgLevelDetails.level2);
                }
                departmentsList = DepartmentsAndFunctions[0];
                functionsList = DepartmentsAndFunctions[1];
            }

            lstOrgIdDepartments = (from a in lstAllServiceAreaDepts
                                       //join d in departmentsList on a.departmentValue equals d
                                   select new clsOrgIdAndDepartments
                                   {
                                       departmentValue = a.departmentValue,
                                       departmentText = a.departmentText
                                   }).ToList();

            dynamic lstAjstmt = await GetAdjustmentCodesAsync(userID);
            dynamic lstAlter = await GetAlterCodesAsync(userID, 0, true, false, false, "");

            var paramLockOriginalBudget = await consequenceAdjustedBudgetDbContext.vw_tco_parameters.FirstOrDefaultAsync(t => t.fk_tenant_id == userDetails.tenant_id
                                                                                                           && t.param_name == "LOCK_ORIGINAL_BUDGET"
                                                                                                           && t.param_value == budgetYear.ToString()
                                                                                                           && t.active == 1);

            var lstProjects = await consequenceAdjustedBudgetDbContext.tco_projects.Where(x => x.fk_tenant_id == userDetails.tenant_id && (budgetYear >= x.date_from.Year && budgetYear <= x.date_to.Year)).ToListAsync();

            var dbDataset = await (from td in consequenceAdjustedBudgetDbContext.tsa_assessment_area_actions_detail
                                   join th in consequenceAdjustedBudgetDbContext.tsa_assessment_area_actions on new { a = td.fk_tenant_id, b = td.fk_action_id }
                                                                                                         equals new { a = th.fk_tenant_id, b = th.pk_action_id }
                                   join ta in consequenceAdjustedBudgetDbContext.tco_accounts on new { a = td.fk_tenant_id, b = td.fk_account_code }
                                                                                          equals new { a = ta.pk_tenant_id, b = ta.pk_account_code } into tal
                                   from ta1 in tal.DefaultIfEmpty()

                                   join tf in consequenceAdjustedBudgetDbContext.tco_functions on new { a = td.fk_tenant_id, b = td.fk_function_code }
                                                                                           equals new { a = tf.pk_tenant_id, b = tf.pk_Function_code } into tfl
                                   from tf1 in tfl.DefaultIfEmpty()

                                   join tac in consequenceAdjustedBudgetDbContext.tco_fp_alter_codes on new { a = td.fk_tenant_id, b = td.fk_alter_code }
                                                                                                 equals new { a = tac.fk_tenant_id, b = tac.pk_alter_code } into tacl
                                   from tac1 in tacl.DefaultIfEmpty()

                                   join tfd1 in consequenceAdjustedBudgetDbContext.tco_free_dim_values.Where(x => x.free_dim_column == "free_dim_1")
                                                                                                               on new { a = td.fk_tenant_id, b = td.free_dim_1 }
                                                                                                           equals new { a = tfd1.fk_tenant_id, b = tfd1.free_dim_code } into tfd1l
                                   from tfd11 in tfd1l.DefaultIfEmpty()

                                   join tfd2 in consequenceAdjustedBudgetDbContext.tco_free_dim_values.Where(x => x.free_dim_column == "free_dim_2")
                                                                                                                    on new { a = td.fk_tenant_id, b = td.free_dim_2 }
                                                                                                                equals new { a = tfd2.fk_tenant_id, b = tfd2.free_dim_code } into tfd2l
                                   from tfd21 in tfd2l.DefaultIfEmpty()

                                   join tfd3 in consequenceAdjustedBudgetDbContext.tco_free_dim_values.Where(x => x.free_dim_column == "free_dim_3")
                                                                                                                   on new { a = td.fk_tenant_id, b = td.free_dim_3 }
                                                                                                               equals new { a = tfd3.fk_tenant_id, b = tfd3.free_dim_code } into tfd3l
                                   from tfd31 in tfd3l.DefaultIfEmpty()

                                   join tfd4 in consequenceAdjustedBudgetDbContext.tco_free_dim_values.Where(x => x.free_dim_column == "free_dim_4")
                                                                                                                   on new { a = td.fk_tenant_id, b = td.free_dim_4 }
                                                                                                               equals new { a = tfd4.fk_tenant_id, b = tfd4.free_dim_code } into tfd4l
                                   from tfd41 in tfd4l.DefaultIfEmpty()

                                   where (th.pk_action_id == actionId && th.fk_tenant_id == userDetails.tenant_id && td.budget_year == budgetYear)
                                   select new
                                   {
                                       th.fk_tenant_id,
                                       td.budget_year,
                                       td.fk_account_code,
                                       accountName = ta1 == null ? string.Empty : ta1.display_name,
                                       td.fk_function_code,
                                       functionName = tf1 == null ? string.Empty : tf1.display_name,
                                       td.fk_project_code,
                                       projectName = "",
                                       td.fk_department_code,
                                       td.free_dim_1,
                                       freedim1Name = tfd11 == null ? string.Empty : tfd11.description,
                                       td.free_dim_2,
                                       freedim2Name = tfd21 == null ? string.Empty : tfd21.description,
                                       td.free_dim_3,
                                       freedim3Name = tfd31 == null ? string.Empty : tfd31.description,
                                       td.free_dim_4,
                                       freedim4Name = tfd41 == null ? string.Empty : tfd41.description,
                                       th.description,
                                       th.description_consequence,
                                       th.pk_action_id,
                                       th.tags,
                                       th.updated,
                                       th.updated_by,
                                       thdes = th.name,
                                       td.fk_alter_code,
                                       alterCodeName = tac1 == null ? string.Empty : tac1.alter_description,
                                       td.year_1_amount,
                                       td.year_2_amount,
                                       td.year_3_amount,
                                       td.year_4_amount,
                                       td.year_5_amount,
                                       td.pk_id,
                                       tdUpdated = td.updated,
                                   }).Distinct().ToListAsync();

            if (departmentsList.Count > 0 && functionsList.Count > 0)
            {
                dbDataset = dbDataset.Where(x => departmentsList.Contains(x.fk_department_code) && functionsList.Contains(x.fk_function_code)).ToList();
            }
            else if (departmentsList.Count > 0)
            {
                dbDataset = dbDataset.Where(x => departmentsList.Contains(x.fk_department_code)).ToList();
            }
            else if (functionsList.Count > 0)
            {
                dbDataset = dbDataset.Where(x => functionsList.Contains(x.fk_function_code)).ToList();
            }

            var lst = (from m in dbDataset
                       group m by new
                       {
                           m.fk_account_code,
                           m.accountName,
                           m.fk_function_code,
                           m.functionName,
                           m.fk_project_code,
                           m.projectName,
                           m.fk_department_code,
                           m.free_dim_1,
                           m.freedim1Name,
                           m.free_dim_2,
                           m.freedim2Name,
                           m.free_dim_3,
                           m.freedim3Name,
                           m.free_dim_4,
                           m.freedim4Name,
                           m.description,
                           m.description_consequence,
                           m.pk_action_id,
                           m.tags,
                           m.updated,
                           m.updated_by,
                           m.thdes,
                           m.fk_alter_code,
                           m.alterCodeName,
                           m.year_1_amount,
                           m.year_2_amount,
                           m.year_3_amount,
                           m.year_4_amount,
                           m.year_5_amount,
                           m.tdUpdated,
                       } into g
                       select new clsBudgetChangesActions
                       {
                           strategyActionId = g.Key.pk_action_id,
                           actionDescription = g.Key.thdes,
                           accountCode = g.Key.fk_account_code,
                           accountName = g.Key.accountName,
                           deptCode = g.Key.fk_department_code,
                           deptName = string.Empty,
                           functionCode = g.Key.fk_function_code,
                           functionName = g.Key.functionName,
                           projectCode = g.Key.fk_project_code,
                           projectName = g.Key.projectName,

                           freedimCode1 = g.Key.free_dim_1,
                           freedimName1 = g.Key.freedim1Name,

                           freedimCode2 = g.Key.free_dim_2,
                           freedimName2 = g.Key.freedim2Name,

                           freedimCode3 = g.Key.free_dim_3,
                           freedimName3 = g.Key.freedim3Name,

                           freedimCode4 = g.Key.free_dim_4,
                           freedimName4 = g.Key.freedim4Name,

                           year1Ammount = g.Sum(x => x.year_1_amount),
                           year2Ammount = g.Sum(x => x.year_2_amount),
                           year3Ammount = g.Sum(x => x.year_3_amount),
                           year4Ammount = g.Sum(x => x.year_4_amount),
                           year5Ammount = g.Sum(x => x.year_5_amount),
                           tags = g.Key.tags == null ? "" : g.Key.tags,
                           priority = 0,
                           user = g.Key.updated_by,
                           lastupdated = g.Key.updated,
                           fk_alter_code = g.Key.fk_alter_code,
                           alterCodeName = g.Key.alterCodeName,
                           tdUpdated = g.Key.tdUpdated,
                           update_annual_budget = false,
                           update_next_year_finplan = false,
                           update_annual_budget_next_year = false,
                           long_description = g.Key.description,
                           consequence = g.Key.description_consequence,
                           description = string.Empty
                       }).ToList();

            lst.ForEach(x =>
            {
                x.projectName = lstProjects.FirstOrDefault(p => p.pk_project_code == x.projectCode) == null ? "" : lstProjects.FirstOrDefault(p => p.pk_project_code == x.projectCode).project_name;
            });

            dynamic actions = new JObject();

            if (lst.Any())
            {
                List<int> TagIds = new List<int>();
                if (!string.IsNullOrEmpty((lst.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).tags))
                {
                    TagIds = (lst.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).tags.Split(',').Select(int.Parse).ToList();
                }
                actions.Add("tags", JToken.FromObject(TagIds));
            }
            List<freedimDefinition> freeDims = await _utility.GetFreeDimDefinitionsBasedOnPageIdAsync(userID, string.Empty);
            List<freedimDefinition> freeDimColumns = freeDims.ToList();

            ///Get total lines
            dynamic ActionDetailsTotal = new JObject();
            decimal year1total = 0; decimal year2total = 0; decimal year3total = 0; decimal year4total = 0;
            decimal year1totalbdtchange = 0; decimal year2totalbdtchange = 0; decimal year3totalbdtchange = 0; decimal year4totalbdtchange = 0; decimal year5totalbdtchange = 0;
            dynamic ActionDetailsTotalbdtchange = new JObject();
            dynamic ActionDetailsTotalfinaltotal = new JObject();
            ActionDetailsTotal.id = "total";
            ActionDetailsTotalbdtchange.id = "total";
            ActionDetailsTotalfinaltotal.id = "total";
            dynamic accDetails = new JObject();
            accDetails.accountText = string.Empty;
            accDetails.accountValue = langStringValues.FirstOrDefault(v => v.Key == "cmn_title_total").Value.LangText;
            ActionDetailsTotal.account = accDetails;

            ActionDetailsTotal.account = accDetails;
            ActionDetailsTotal.department = GenerateDynamicDepartmentObject("", "");
            ActionDetailsTotal.functionn = GenerateDynamicFunctionObject("", "");
            ActionDetailsTotal.project = GenerateDynamicProjectDimObject("", "");

            ActionDetailsTotalbdtchange.account = accDetails;
            ActionDetailsTotalbdtchange.department = GenerateDynamicDepartmentObject("", "");
            ActionDetailsTotalbdtchange.functionn = GenerateDynamicFunctionObject("", "");
            ActionDetailsTotalbdtchange.project = GenerateDynamicProjectDimObject("", "");

            ActionDetailsTotalfinaltotal.account = accDetails;
            ActionDetailsTotalfinaltotal.department = GenerateDynamicDepartmentObject("", "");
            ActionDetailsTotalfinaltotal.functionn = GenerateDynamicFunctionObject("", "");
            ActionDetailsTotalfinaltotal.project = GenerateDynamicProjectDimObject("", "");

            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_1") != null)
            {
                ActionDetailsTotal.freeDim1 = GenerateDynamicFreeDimObject("", "");
                ActionDetailsTotalbdtchange.freeDim1 = GenerateDynamicFreeDimObject("", "");
                ActionDetailsTotalfinaltotal.freeDim1 = GenerateDynamicFreeDimObject("", "");
            }
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_2") != null)
            {
                ActionDetailsTotal.freeDim2 = GenerateDynamicFreeDimObject("", "");
                ActionDetailsTotalbdtchange.freeDim2 = GenerateDynamicFreeDimObject("", "");
                ActionDetailsTotalfinaltotal.freeDim2 = GenerateDynamicFreeDimObject("", "");
            }
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_3") != null)
            {
                ActionDetailsTotal.freeDim3 = GenerateDynamicFreeDimObject("", "");
                ActionDetailsTotalbdtchange.freeDim3 = GenerateDynamicFreeDimObject("", "");
                ActionDetailsTotalfinaltotal.freeDim3 = GenerateDynamicFreeDimObject("", "");
            }
            if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_4") != null)
            {
                ActionDetailsTotal.freeDim4 = GenerateDynamicFreeDimObject("", "");
                ActionDetailsTotalbdtchange.freeDim4 = GenerateDynamicFreeDimObject("", "");
                ActionDetailsTotalfinaltotal.freeDim4 = GenerateDynamicFreeDimObject("", "");
            }

            if (lstAjstmt.Count > 1)
            {
                ActionDetailsTotal.adjustmentCode = GenerateDynamicAdjustmentCodeObject("", "");
                ActionDetailsTotalbdtchange.adjustmentCode = GenerateDynamicAdjustmentCodeObject("", "");
                ActionDetailsTotalfinaltotal.adjustmentCode = GenerateDynamicAdjustmentCodeObject("", "");
            }

            if (lstAlter.Count > 1)
            {
                ActionDetailsTotal.alterCode = GenerateDynamicAlterCodeObject("", "");
                ActionDetailsTotalbdtchange.alterCode = GenerateDynamicAlterCodeObject("", "");
                ActionDetailsTotalfinaltotal.alterCode = GenerateDynamicAlterCodeObject("", "");
            }

            if (lst.Count > 0)
            {
                actions.actionId = actionId;
                actions.strategyActionId = actionId;
                actions.finishedDate = (lst.FirstOrDefault() == null || lst.FirstOrDefault().finished_date == null) ? new DateTime(2099, 12, 31) : lst.FirstOrDefault().finished_date;
                actions.actionDescription = (lst.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).actionDescription;
                actions.consequence = string.Empty;
                actions.fk_area_id = 0;
                actions.tag = "";
                actions.priority = 0;
                actions.long_description = "";
                actions.groupId = "41";
                actions.groupName = "";
                actions.display_financial_plan_flag = false;
                actions.display_cab_flag = false;
                actions.display_description_apendix_flag = false;
                actions.cat_id = Guid.Empty;
                actions.financial_plan_description = "";
                actions.fk_adjustment_code = "";
                actions.fk_alter_code = (lst.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).fk_alter_code;
                actions.consequence_flag = false;
                actions.different_external_description_flag = false;
                actions.change_text_flag = false;
                actions.DisplayZeroAction = false;

                JArray nonActiveGridTotalData = new JArray();
                ActionDetailsTotal.year1 = 0;
                ActionDetailsTotal.year2 = 0;
                ActionDetailsTotal.year3 = 0;
                ActionDetailsTotal.year4 = 0;
                ActionDetailsTotal.description = string.Empty;
                nonActiveGridTotalData.Add(ActionDetailsTotal);
                actions.Add("nonActiveChangeData", nonActiveGridTotalData);
            }

            int colCount = 0;
            dynamic columnFields1 = new JArray();
            dynamic columnFields2 = new JArray();

            columnFields1.Add("account");
            columnFields1.Add("department");
            columnFields1.Add("functionn");
            columnFields1.Add("project");

            columnFields2.Add("account");
            columnFields2.Add("department");
            columnFields2.Add("functionn");
            columnFields2.Add("project");

            foreach (var fd in freeDimColumns)
            {
                if (fd.freeDimColumn == "free_dim_1")
                {
                    columnFields1.Add("freeDim" + 1.ToString());
                    columnFields2.Add("freeDim" + 1.ToString());
                }

                if (fd.freeDimColumn == "free_dim_2")
                {
                    columnFields1.Add("freeDim" + 2.ToString());
                    columnFields2.Add("freeDim" + 2.ToString());
                }

                if (fd.freeDimColumn == "free_dim_3")
                {
                    columnFields1.Add("freeDim" + 3.ToString());
                    columnFields2.Add("freeDim" + 3.ToString());
                }

                if (fd.freeDimColumn == "free_dim_4")
                {
                    columnFields1.Add("freeDim" + 4.ToString());
                    columnFields2.Add("freeDim" + 4.ToString());
                }
            }

            if (lstAjstmt.Count > 1)
            {
                columnFields1.Add("adjustmentCode");
                columnFields2.Add("adjustmentCode");
            }
            if (lstAlter.Count > 1)
            {
                columnFields1.Add("alterCode");
                columnFields2.Add("alterCode");
            }

            columnFields1.Add("year1");
            columnFields1.Add("year2");
            columnFields1.Add("year3");
            columnFields1.Add("year4");

            columnFields1.Add("description");

            columnFields2.Add("year1");
            columnFields2.Add("year2");
            columnFields2.Add("year3");
            columnFields2.Add("year4");

            columnFields2.Add("description");

            dynamic columnTitles1 = new JArray();
            dynamic columnTitles2 = new JArray();

            columnTitles1.Add(((langStringValues.FirstOrDefault(v => v.Key == "account_text")).Value).LangText);
            columnTitles1.Add(((langStringValues.FirstOrDefault(v => v.Key == "department_text")).Value).LangText);
            columnTitles1.Add(((langStringValues.FirstOrDefault(v => v.Key == "function_text")).Value).LangText);
            columnTitles1.Add(((langStringValues.FirstOrDefault(v => v.Key == "fp_project_text")).Value).LangText);

            columnTitles2.Add(((langStringValues.FirstOrDefault(v => v.Key == "account_text")).Value).LangText);
            columnTitles2.Add(((langStringValues.FirstOrDefault(v => v.Key == "department_text")).Value).LangText);
            columnTitles2.Add(((langStringValues.FirstOrDefault(v => v.Key == "function_text")).Value).LangText);
            columnTitles2.Add(((langStringValues.FirstOrDefault(v => v.Key == "fp_project_text")).Value).LangText);

            foreach (var fd in freeDimColumns)
            {
                if (fd.freeDimColumn == "free_dim_1")
                {
                    string freedim = "free_dim_" + 1;
                    columnTitles1.Add(freeDimColumns.Where(x => x.freeDimColumn == freedim).Select(x => x.freeDimHeader).First().ToString());
                    columnTitles2.Add(freeDimColumns.Where(x => x.freeDimColumn == freedim).Select(x => x.freeDimHeader).First().ToString());
                }
                if (fd.freeDimColumn == "free_dim_2")
                {
                    string freedim = "free_dim_" + 2;
                    columnTitles1.Add(freeDimColumns.Where(x => x.freeDimColumn == freedim).Select(x => x.freeDimHeader).First().ToString());
                    columnTitles2.Add(freeDimColumns.Where(x => x.freeDimColumn == freedim).Select(x => x.freeDimHeader).First().ToString());
                }
                if (fd.freeDimColumn == "free_dim_3")
                {
                    string freedim = "free_dim_" + 3;
                    columnTitles1.Add(freeDimColumns.Where(x => x.freeDimColumn == freedim).Select(x => x.freeDimHeader).First().ToString());
                    columnTitles2.Add(freeDimColumns.Where(x => x.freeDimColumn == freedim).Select(x => x.freeDimHeader).First().ToString());
                }
                if (fd.freeDimColumn == "free_dim_4")
                {
                    string freedim = "free_dim_" + 4;
                    columnTitles1.Add(freeDimColumns.Where(x => x.freeDimColumn == freedim).Select(x => x.freeDimHeader).First().ToString());
                    columnTitles2.Add(freeDimColumns.Where(x => x.freeDimColumn == freedim).Select(x => x.freeDimHeader).First().ToString());
                }
            }

            Dictionary<string, clsLanguageString> langStringValuesFP = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "FinancialPlan");
            if (lstAjstmt.Count > 1)
            {
                columnTitles1.Add(((langStringValuesFP.FirstOrDefault(v => v.Key == "fp_adjustment_code")).Value).LangText);
                columnTitles2.Add(((langStringValuesFP.FirstOrDefault(v => v.Key == "fp_adjustment_code")).Value).LangText);
            }
            if (lstAlter.Count > 1)
            {
                columnTitles1.Add(((langStringValuesFP.FirstOrDefault(v => v.Key == "fp_alter_code_text")).Value).LangText);
                columnTitles2.Add(((langStringValuesFP.FirstOrDefault(v => v.Key == "fp_alter_code_text")).Value).LangText);
            }

            columnTitles1.Add(budgetYear.ToString());
            columnTitles1.Add((budgetYear + 1).ToString());
            columnTitles1.Add((budgetYear + 2).ToString());
            columnTitles1.Add((budgetYear + 3).ToString());
            columnTitles1.Add(langStringValues.FirstOrDefault(v => v.Key == "CAB_description").Value.LangText);

            columnTitles2.Add(budgetYear.ToString());
            columnTitles2.Add((budgetYear + 1).ToString());
            columnTitles2.Add((budgetYear + 2).ToString());
            columnTitles2.Add((budgetYear + 3).ToString());

            columnTitles2.Add(langStringValues.FirstOrDefault(v => v.Key == "CAB_description").Value.LangText);

            dynamic activeChangeColumns = new JArray();
            foreach (var col in columnFields2)
            {
                dynamic column = new JObject();
                column.title = columnTitles2[colCount];
                column.field = col;

                dynamic attributes = new JObject();
                dynamic headerAttributes = new JObject();

                if (col == "account")
                {
                    column.headerTemplate = "<span id='financingAccount'>" + columnTitles2[colCount] + "</span><span class='red'>*</span>";
                    column.template = "<span title = '#=account.accountText#' >#=account.accountValue#</span>";

                    attributes.style = "border-left:none;text-align:left";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "department")
                {
                    column.headerTemplate = "<span id='financingDepartment'>" + columnTitles2[colCount] + "</span><span class='red'>*</span>";
                    column.template = "<span title = '#=department.departmentText#' >#=department.departmentValue#</span>";

                    attributes.style = "border-left:none;text-align:left";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "functionn")
                {
                    column.headerTemplate = "<span id='financingFunction'>" + columnTitles2[colCount] + "</span><span class='red'>*</span>";
                    column.template = "<span title = '#=functionn.functionText#' >#=functionn.functionValue#</span>";

                    attributes.style = "border-left: none; text-align:left;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "project")
                {
                    column.headerTemplate = "<span>" + columnTitles2[colCount] + "</span>";
                    column.template = "<span title = '#=project.project_name#' >#=project.fk_project_code#</span>";

                    attributes.style = "border-left: none; text-align:left;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "freeDim1")
                {
                    column.headerTemplate = "<span id='financingFreeDim1'>" + columnTitles2[colCount] + "</span>";
                    column.template = "<span title = '#=freeDim1.freedim_name#' >#=freeDim1.fk_freedim_code#</span>";

                    attributes.style = "border-left: none; text-align:left;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "freeDim2")
                {
                    column.headerTemplate = "<span id='financingFreeDim2'>" + columnTitles2[colCount] + "</span>";
                    column.template = "<span title = '#=freeDim2.freedim_name#' >#=freeDim2.fk_freedim_code#</span>";

                    attributes.style = "border-left: none; text-align:left;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "freeDim3")
                {
                    column.headerTemplate = "<span id='financingFreeDim3'>" + columnTitles2[colCount] + "</span>";
                    column.template = "<span title = '#=freeDim2.freedim_name#' >#=freeDim3.fk_freedim_code#</span>";

                    attributes.style = "border-left: none; text-align:left;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "freeDim4")
                {
                    column.headerTemplate = "<span id='financingFreeDim4'>" + columnTitles2[colCount] + "</span>";
                    column.template = "<span title = '#=freeDim4.freedim_name#' >#=freeDim4.fk_freedim_code#</span>";

                    attributes.style = "border-left: none; text-align:left;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "adjustmentCode")
                {
                    column.headerTemplate = "<span id='financingAdjCode'>" + columnTitles2[colCount] + "</span><span class='red' id='commonFinAdjustmentCodeMandatory'></span>";
                    column.template = "<span title = '#=adjustmentCode.value#' >#=adjustmentCode.key#</span>";

                    attributes.style = "border-left: none; text-align:left;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "alterCode")
                {
                    column.headerTemplate = "<span id='financingAlterCode'>" + columnTitles2[colCount] + "</span><span class='red'>*</span>";
                    column.template = "<span title='#=alterCode.value#'>#=alterCode.key#</span>";

                    attributes.style = "border-left: none; text-align:left;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "periodicKey")
                {
                    column.headerTemplate = "<span id='financingperiodicKey'>" + columnTitles2[colCount] + "</span><span class='red'>*</span>";
                    column.template = "<span title='#=periodicKey.value#'>#=periodicKey.key#</span>";

                    attributes.style = "border-left: none; text-align:left;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "year1")
                {
                    column.headerTemplate = "<span id='MODULESYear1'>" + columnTitles2[colCount] + "</span>";
                    column.template = "#= (year1 == null) ? '0' :  kendo.toString(year1, 'n0')  #";

                    attributes.style = "border-left: none; text-align:right;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    column.width = 100;
                }
                if (col == "year2")
                {
                    column.headerTemplate = "<span id='MODULESYear2'>" + columnTitles2[colCount] + "</span>";
                    column.template = "#= (year2 == null) ? '0' :  kendo.toString(year2, 'n0')  #";

                    attributes.style = "border-left: none; text-align:right;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    column.width = 100;
                }
                if (col == "year3")
                {
                    column.headerTemplate = "<span id='MODULESYear3'>" + columnTitles2[colCount] + "</span>";
                    column.template = "#= (year3 == null) ? '0' :  kendo.toString(year3, 'n0')  #";

                    attributes.style = "border-left: none; text-align:right;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    column.width = 100;
                }
                if (col == "year4")
                {
                    column.headerTemplate = "<span id='MODULESYear4'>" + columnTitles2[colCount] + "</span>";
                    column.template = "#= (year4 == null) ? '0' :  kendo.toString(year4, 'n0')  #";

                    attributes.style = "border-left: none; text-align:right;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    column.width = 100;
                }
                if (col == "year5")
                {
                    column.headerTemplate = "<span id='MODULESYear5'>" + columnTitles2[colCount] + "</span>";
                    column.template = "#= (year5 == null) ? '0' :  kendo.toString(year5, 'n0')  #";

                    attributes.style = "border-left: none; text-align:right;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    column.width = 100;
                }
                if (col == "description")
                {
                    column.headerTemplate = "<span>" + columnTitles2[colCount] + "</span>";
                    column.template = "#= (description == '') ? 'Legg inn tekst':description#";

                    attributes.style = "border-left: none;white-space:normal;text-align:left;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    column.width = 150;
                }
                column.format = null;
                activeChangeColumns.Add(column);
                colCount++;
            }
            actions.activeChangeColumns = activeChangeColumns;
            colCount = 0;
            dynamic nonActiveChangeColumns = new JArray();
            foreach (var col in columnFields1)
            {
                dynamic column = new JObject();
                column.title = columnTitles1[colCount];
                column.field = col;

                dynamic attributes = new JObject();
                dynamic headerAttributes = new JObject();

                if (col == "account")
                {
                    column.template = "<span title = '#=account.accountText#' >#=account.accountValue#</span>";

                    attributes.style = "text-align:left;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "department")
                {
                    column.template = "<span title = '#=department.departmentText#' >#=department.departmentValue#</span>";

                    attributes.style = "text-align:left;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "functionn")
                {
                    column.template = "<span title = '#=functionn.functionText#' >#=functionn.functionValue#</span>";

                    attributes.style = "text-align:left;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "project")
                {
                    column.template = "<span title = '#=project.project_name#' >#=project.fk_project_code#</span>";

                    attributes.style = "text-align:left;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "freeDim1")
                {
                    column.template = "<span title = '#=freeDim1.freedim_name#' >#=freeDim1.fk_freedim_code#</span>";

                    attributes.style = "text-align:left;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "freeDim2")
                {
                    column.template = "<span title = '#=freeDim2.freedim_name#' >#=freeDim2.fk_freedim_code#</span>";

                    attributes.style = "text-align:left;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "freeDim3")
                {
                    column.template = "<span title = '#=freeDim3.freedim_name#' >#=freeDim3.fk_freedim_code#</span>";

                    attributes.style = "text-align:left;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "freeDim4")
                {
                    column.template = "<span title = '#=freeDim4.freedim_name#' >#=freeDim4.fk_freedim_code#</span>";

                    attributes.style = "text-align:left;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "adjustmentCode")
                {
                    column.template = "<span title = '#=adjustmentCode.value#' >#=adjustmentCode.key#</span>";

                    attributes.style = "text-align:left;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "alterCode")
                {
                    column.template = "<span title='#=alterCode.value#'>#=alterCode.key#</span>";

                    attributes.style = "text-align:left;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "periodicKey")
                {
                    column.template = "<span title='#=periodicKey.value#'>#=periodicKey.key#</span>";
                    attributes.style = "text-align:left;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                }
                if (col == "year1")
                {
                    column.template = "#= (year1 == null) ? '0' :  kendo.toString(year1, 'n0')  #";

                    attributes.style = "text-align:right;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    column.width = 100;
                }
                if (col == "year2")
                {
                    column.template = "#= (year2 == null) ? '0' :  kendo.toString(year2, 'n0')  #";

                    attributes.style = "text-align:right;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    column.width = 100;
                }
                if (col == "year3")
                {
                    column.template = "#= (year3 == null) ? '0' :  kendo.toString(year3, 'n0')  #";

                    attributes.style = "text-align:right;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    column.width = 100;
                }
                if (col == "year4")
                {
                    column.template = "#= (year4 == null) ? '0' :  kendo.toString(year4, 'n0')  #";

                    attributes.style = "text-align:right;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    column.width = 100;
                }
                if (col == "year5")
                {
                    column.template = "#= (year5 == null) ? '0' :  kendo.toString(year5, 'n0')  #";

                    attributes.style = "text-align:right;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    column.width = 100;
                }
                if (col == "description")
                {
                    column.template = "#=description#";

                    attributes.style = "text-align:left;border-left: none;white-space:normal;";
                    column.attributes = attributes;

                    headerAttributes.style = "text-align:left;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    column.width = 150;
                }
                column.headerTemplate = null;
                column.format = null;
                nonActiveChangeColumns.Add(column);
                colCount++;
            }
            actions.nonActiveChangeColumns = nonActiveChangeColumns;

            actions.amountFormat = "#,##";
            string username = "";
            string lastupdated = "";
            if (lst.Count > 0)
            {
                int userid = Convert.ToInt32((lst.FirstOrDefault()).user.ToString());
                var user = await consequenceAdjustedBudgetDbContext.vwUserDetails.Where(x => x.pk_id == userid && x.tenant_id == userDetails.tenant_id && x.is_active_tenant && x.client_id == userDetails.client_id).ToListAsync();
                if (user != null && user.Any())
                {
                    if (!string.IsNullOrEmpty(user.FirstOrDefault().first_name) && !string.IsNullOrEmpty(user.FirstOrDefault().last_name))
                    {
                        username = user.First().first_name + " " + user.First().last_name;
                    }
                    else if (!string.IsNullOrEmpty(user.FirstOrDefault().first_name))
                    {
                        username = user.First().first_name;
                    }
                    else if (!string.IsNullOrEmpty(user.FirstOrDefault().last_name))
                    {
                        username = user.First().last_name;
                    }
                }
                else
                {
                    username = "";
                }

                lastupdated = (lst.FirstOrDefault()).lastupdated.ToString("dd.MM.yyyy");
            }

            actions.user = username;
            actions.lastUpdated = lastupdated;

            dynamic defaultGriData = await GetDefaultsForBudgetChangeAsync(0, userID, orgId, serviceId, "", budgetYear);
            var lstchange = (from m in dbDataset
                             where (m.pk_action_id == actionId && m.fk_tenant_id == userDetails.tenant_id && m.budget_year == budgetYear /*&& departmentsList.Contains(m.fk_department_code)*/
                             )
                             select new clsBudgetChangesActions
                             {
                                 actionID = -1,
                                 strategyActionId = m.pk_action_id,
                                 actionDescription = m.thdes,
                                 consequence = m.description_consequence,
                                 pk_id = m.pk_id,
                                 accountCode = m.fk_account_code,
                                 accountName = m.accountName,
                                 deptCode = m.fk_department_code,
                                 functionCode = m.fk_function_code,
                                 functionName = m.functionName,
                                 projectCode = m.fk_project_code,
                                 projectName = m.projectName,
                                 freedimCode1 = m.free_dim_1,
                                 freedimName1 = m.freedim1Name,

                                 freedimCode2 = m.free_dim_2,
                                 freedimName2 = m.freedim2Name,

                                 freedimCode3 = m.free_dim_3,
                                 freedimName3 = m.freedim3Name,

                                 freedimCode4 = m.free_dim_4,
                                 freedimName4 = m.freedim4Name,

                                 year1Ammount = m.year_1_amount,
                                 year2Ammount = m.year_2_amount,
                                 year3Ammount = m.year_3_amount,
                                 year4Ammount = m.year_4_amount,
                                 year5Ammount = m.year_5_amount,
                                 fk_area_id = 0,
                                 tag = "",
                                 tags = m.tags == null ? "" : m.tags,
                                 priority = 0,
                                 long_description = m.description == null ? String.Empty : m.description,
                                 user = m.updated_by,
                                 lastupdated = m.updated,
                                 display_financial_plan_flag = false,
                                 display_cab_flag = false,
                                 display_description_apendix_flag = false,
                                 description = string.Empty,
                                 cat_id = Guid.Empty,
                                 financial_plan_description = string.Empty,
                                 fk_adjustment_code = "",
                                 adjustmentCodeName = "",
                                 fk_alter_code = m.fk_alter_code,
                                 alterCodeName = m.alterCodeName,
                                 consequence_flag = false,
                                 different_external_description_flag = false,
                                 change_text_flag = false,
                                 tdUpdated = m.tdUpdated,
                                 finished_date = null,
                                 periodicKey = "0",
                                 DisplayZeroAction = false,
                                 update_annual_budget = false,
                                 update_annual_budget_next_year = false,
                                 update_next_year_finplan = false
                             }).Distinct().ToList();

            var changeresult = (from a in lstchange
                                join b in lstOrgIdDepartments on a.deptCode equals b.departmentValue
                                select new clsBudgetChangesActions
                                {
                                    actionID = a.actionID,
                                    actionDescription = a.actionDescription,
                                    pk_id = a.pk_id,
                                    accountCode = a.accountCode,
                                    accountName = a.accountName,
                                    deptCode = a.deptCode,
                                    deptName = b.departmentText,
                                    functionCode = a.functionCode,
                                    functionName = a.functionName,
                                    projectCode = a.projectCode,
                                    projectName = a.projectName,
                                    freedimCode1 = a.freedimCode1,
                                    freedimName1 = a.freedimName1,

                                    freedimCode2 = a.freedimCode2,
                                    freedimName2 = a.freedimName2,

                                    freedimCode3 = a.freedimCode3,
                                    freedimName3 = a.freedimName3,

                                    freedimCode4 = a.freedimCode4,
                                    freedimName4 = a.freedimName4,

                                    year1Ammount = a.year1Ammount,
                                    year2Ammount = a.year2Ammount,
                                    year3Ammount = a.year3Ammount,
                                    year4Ammount = a.year4Ammount,
                                    year5Ammount = a.year5Ammount,
                                    user = a.user,
                                    lastupdated = a.lastupdated,
                                    display_financial_plan_flag = a.display_financial_plan_flag,
                                    display_cab_flag = a.display_cab_flag,
                                    display_description_apendix_flag = a.display_description_apendix_flag,
                                    description = a.description,
                                    cat_id = a.cat_id,
                                    financial_plan_description = a.financial_plan_description,
                                    fk_adjustment_code = a.fk_adjustment_code,
                                    fk_alter_code = a.fk_alter_code,
                                    adjustmentCodeName = a.adjustmentCodeName,
                                    alterCodeName = a.alterCodeName,
                                    consequence_flag = a.consequence_flag,
                                    different_external_description_flag = a.different_external_description_flag,
                                    change_text_flag = a.change_text_flag,
                                    tdUpdated = a.tdUpdated,
                                    finished_date = a.finished_date,
                                    periodicKey = a.periodicKey,
                                    DisplayZeroAction = a.DisplayZeroAction,
                                    long_description = a.long_description,
                                    consequence = a.consequence
                                }).ToList();
            //Active change data
            if (lstchange.Count > 0)
            {
                consequenceAdjustedBudgetDbContext.Database.SetCommandTimeout(600);

                actions.actionId = actionId;
                actions.strategyActionId = actionId;
                actions.finishedDate = new DateTime(2099, 12, 31);
                actions.actionDescription = (lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).actionDescription;
                actions.consequence = (lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).consequence;
                actions.fk_area_id = 0;
                actions.tag = "";
                actions.priority = 0;
                actions.long_description = "";
                actions.groupId = "41";
                actions.groupName = "";
                actions.display_financial_plan_flag = false;
                actions.display_cab_flag = false;
                actions.display_description_apendix_flag = false;
                actions.cat_id = Guid.Empty;
                actions.financial_plan_description = string.Empty;
                actions.fk_adjustment_code = "";
                actions.fk_alter_code = (lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).fk_alter_code;
                actions.consequence_flag = false;
                actions.different_external_description_flag = false;
                actions.change_text_flag = false;
                actions.DisplayZeroAction = false;
                actions.long_description = (lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).long_description;
                dynamic gridDatabdtchange = new JArray();
                foreach (var a in changeresult)
                {
                    dynamic ActionDetailsWithBudgetChange = new JObject();
                    ActionDetailsWithBudgetChange.id = a.pk_id;
                    ActionDetailsWithBudgetChange.guid = actionId;

                    ActionDetailsWithBudgetChange.account = GenerateDynamicAccountObject(a.accountCode, a.accountName);

                    ActionDetailsWithBudgetChange.department = GenerateDynamicDepartmentObject(a.deptCode, a.deptName);

                    ActionDetailsWithBudgetChange.functionn = GenerateDynamicFunctionObject(a.functionCode, a.functionName);

                    ActionDetailsWithBudgetChange.project = GenerateDynamicProjectDimObject(a.projectCode, a.projectName);

                    if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_1") != null)
                    {
                        ActionDetailsWithBudgetChange.freeDim1 = GenerateDynamicFreeDimObject(a.freedimCode1, a.freedimName1);
                    }
                    if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_2") != null)
                    {
                        ActionDetailsWithBudgetChange.freeDim2 = GenerateDynamicFreeDimObject(a.freedimCode2, a.freedimName2);
                    }
                    if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_3") != null)
                    {
                        ActionDetailsWithBudgetChange.freeDim3 = GenerateDynamicFreeDimObject(a.freedimCode3, a.freedimName3);
                    }
                    if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_4") != null)
                    {
                        ActionDetailsWithBudgetChange.freeDim4 = GenerateDynamicFreeDimObject(a.freedimCode4, a.freedimName4);
                    }

                    if (lstAjstmt.Count > 1)
                    {
                        ActionDetailsWithBudgetChange.adjustmentCode = GenerateDynamicAdjustmentCodeObject(a.fk_adjustment_code, a.adjustmentCodeName);
                    }
                    if (lstAlter.Count > 1)
                    {
                        ActionDetailsWithBudgetChange.alterCode = GenerateDynamicAlterCodeObject(a.fk_alter_code, a.alterCodeName);
                    }

                    ActionDetailsWithBudgetChange.year1 = a.year1Ammount.Value / 1000;
                    ActionDetailsWithBudgetChange.year2 = a.year2Ammount.Value / 1000;
                    ActionDetailsWithBudgetChange.year3 = a.year3Ammount.Value / 1000;
                    ActionDetailsWithBudgetChange.year4 = a.year4Ammount.Value / 1000;

                    ActionDetailsWithBudgetChange.description = a.description;

                    year1totalbdtchange = year1totalbdtchange + (a.year1Ammount.Value / 1000);
                    year2totalbdtchange = year2totalbdtchange + (a.year2Ammount.Value / 1000);
                    year3totalbdtchange = year3totalbdtchange + (a.year3Ammount.Value / 1000);
                    year4totalbdtchange = year4totalbdtchange + (a.year4Ammount.Value / 1000);
                    year5totalbdtchange = year5totalbdtchange + (a.year5Ammount.Value / 1000);

                    gridDatabdtchange.Add(ActionDetailsWithBudgetChange);
                }
                ActionDetailsTotalbdtchange.year1 = year1totalbdtchange;
                ActionDetailsTotalbdtchange.year2 = year2totalbdtchange;
                ActionDetailsTotalbdtchange.year3 = year3totalbdtchange;
                ActionDetailsTotalbdtchange.year4 = year4totalbdtchange;

                actions.Add("activeChangeData", gridDatabdtchange);
                actions.isDefaultData = false;
            }
            ActionDetailsTotalfinaltotal.year1 = year1total + year1totalbdtchange;
            ActionDetailsTotalfinaltotal.year2 = year2total + year2totalbdtchange;
            ActionDetailsTotalfinaltotal.year3 = year3total + year3totalbdtchange;
            ActionDetailsTotalfinaltotal.year4 = year4total + year4totalbdtchange;

            dynamic totalsDataArray = new JArray();
            ActionDetailsTotalbdtchange.Add("description", "");
            ActionDetailsTotalfinaltotal.Add("description", "");
            totalsDataArray.Add(ActionDetailsTotalbdtchange);

            totalsDataArray.Add(ActionDetailsTotalfinaltotal);
            actions.Add("totalsData", totalsDataArray);

            actions.Add("defaultModel", defaultGriData.gridData);
            actions.updateYearlyBudget = paramLockOriginalBudget != null;
            actions.updateNextYearFinplan = lstchange.Any() && lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault() != null ? (lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).update_next_year_finplan : false;
            actions.updateYearlyBudgetNxtYear = lstchange.Any() && lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault() != null ? (lstchange.OrderByDescending(x => x.tdUpdated).FirstOrDefault()).update_annual_budget_next_year : false;
            actions.activeChangeIdDesc = string.Empty;

            dynamic selectedChangeId = new JObject();
            selectedChangeId.key = -1;
            selectedChangeId.value = string.Empty;
            actions.selectedChangeId = selectedChangeId;

            actions.createNextYearData = true;
            actions.showBudgetChangeControls = false;
            List<string> actiongoalIdList = new List<string>();
            actions.Add("goalTagList", JToken.FromObject(actiongoalIdList));
            List<string> actiontargetIdList = new List<string>();
            actions.Add("targetTagList", JToken.FromObject(actiontargetIdList));
            List<string> actionstrategyIdList = new List<string>();
            actions.Add("strategyTagList", JToken.FromObject(actionstrategyIdList));

            return actions;
        }

        public string AddActionDetailsWithBudgetChange(ActionWithBudgetChange lst, string userID,
            bool isUpdateUserdetails = false)
        {
            var result = AddActionDetailsWithBudgetChangeAsync(lst, userID, isUpdateUserdetails).GetAwaiter()
                .GetResult();
            return result;
        }

        public async Task<string> AddActionDetailsWithBudgetChangeAsync(ActionWithBudgetChange lst, string userID, bool isUpdateUserdetails = false)
        {
            TenantDBContext consequenceAdjustedBudgetDbContext = await _utility.GetTenantDBContextAsync();
            UserData user = await _utility.GetUserDetailsAsync(userID);

            int budgetYear = lst.BudgetYear;
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userID, _utility.GetForecastPeriod(budgetYear, 1));
            string orgIdToInsertInHeader = string.Empty;
            int orgLevelToInsertInHeader = -1;

            var orgIdAndLevel = await ReturnOrgIdAndOrgLevelAsync(userID, lst.pageId, orgVersionContent, lst.orgId, lst.orgLevel);
            orgIdToInsertInHeader = orgIdAndLevel.orgIdToInsertInHeader;
            orgLevelToInsertInHeader = orgIdAndLevel.orgLevelToInsertInHeader;

            bool isStrategyAction = lst.strategyActionId != Guid.Empty;

            //Consider strategy action having action with type 'type-SA' as new action & make a entries in trans tables
            if (isStrategyAction)
            {
                lst.actionId = 0;
                lst.groupId = "41";

                var strategyActionsDetail = await GetStrategyActionDetailsAsync(lst.strategyActionId, userID, lst.orgId, lst.serviceId, "", lst.orgLevel, lst.BudgetYear);
                foreach (var item in strategyActionsDetail.activeChangeData)
                {
                    var actionDetail = JsonConvert.DeserializeObject<ActionDetailsWithBudgetChange>(item.ToString());
                    lst.gridDatabdtchange.Add(actionDetail);
                    lst.actionDescription = strategyActionsDetail.actionDescription;
                }
                lst.long_description = strategyActionsDetail.long_description;
                lst.consequence = strategyActionsDetail.consequence;
                string[] tagsList = strategyActionsDetail.tags.ToObject<string[]>();
                string tags = string.Join(",", tagsList);
                if (!lst.tagsData.Any() && !string.IsNullOrEmpty(tags))
                {
                    //Get tags for strategy action while moving to finplan using activate action option
                    List<KeyValueData> tagIdsList = new List<KeyValueData>();
                    var tagsDetail = await consequenceAdjustedBudgetDbContext.tcoActionTags.Where(x => x.FkTenantId == user.tenant_id && tags.Contains(x.PkId.ToString())).ToListAsync();
                    foreach (var tag in tags.Split(',').ToList())
                    {
                        if (tagsDetail.FirstOrDefault(x => x.PkId.ToString() == tag) != null)
                        {
                            KeyValueData tagInfo = new KeyValueData();
                            tagInfo.KeyId = int.Parse(tag);
                            tagInfo.ValueString = tagsDetail.FirstOrDefault(x => x.PkId.ToString() == tag).TagDescription;
                            tagIdsList.Add(tagInfo);
                        }
                    }
                    lst.tagsData = tagIdsList;
                }
            }

            int actionType = int.Parse(lst.groupId);
            List<clsLine_OrderValidation> line_orderForActions = new List<clsLine_OrderValidation>();
            int line_order = 0;
            List<string> finplanActionTypes = new List<string> { "1", "100", "2", "3" };

            Dictionary<string, clsLanguageString> langStringValuesFinPlan = await _utility.GetLanguageStringsAsync(user.language_preference, user.user_name, "FinancialPlan");

            List<AccountingInfo> lstExistingData = new List<AccountingInfo>();
            List<AccountingInfo> lstExistingDataNextYear = new List<AccountingInfo>();

            //#57392 -FP-when adding new central actions set display_financial_plan_flag = 1 so that theese actions will show in export.
            lst.display_financial_plan_flag = (lst.pageId == "FinplanOverview" && finplanActionTypes.Contains(actionType.ToString())) ? true : lst.display_financial_plan_flag;

            if (lst.actionId == 0 && (lst.pageId == "FinplanOverview" || lst.pageId == "BudChangeOverview"))
            {
                List<clsLine_OrderValidation> line_order_validate = new List<clsLine_OrderValidation>();

                line_order_validate = await (from s in consequenceAdjustedBudgetDbContext.tmd_finplan_line_setup
                                             where s.fk_tenant_id == user.tenant_id && s.budget_year == budgetYear && s.action_type == actionType && s.action_name == lst.actionDescription
                                             orderby s.priority ascending
                                             select new clsLine_OrderValidation
                                             {
                                                 action_type = s.action_type,
                                                 line_order = s.line_order,
                                                 fk_account_code = s.fk_account_code,
                                                 fk_department_code_from = string.IsNullOrEmpty(s.fk_department_code_from) ? "" : s.fk_department_code_from,
                                                 fk_department_code_to = string.IsNullOrEmpty(s.fk_department_code_to) ? "" : s.fk_department_code_to,
                                                 fk_function_code_from = string.IsNullOrEmpty(s.fk_function_code_from) ? "" : s.fk_function_code_from,
                                                 fk_function_code_to = string.IsNullOrEmpty(s.fk_function_code_to) ? "" : s.fk_function_code_to,
                                                 fk_project_code_from = string.IsNullOrEmpty(s.fk_project_code_from) ? "" : s.fk_project_code_from,
                                                 fk_project_code_to = string.IsNullOrEmpty(s.fk_project_code_to) ? "" : s.fk_project_code_to,
                                                 free_dim_1_from = string.IsNullOrEmpty(s.free_dim_1_from) ? "" : s.free_dim_1_from,
                                                 free_dim_1_to = string.IsNullOrEmpty(s.free_dim_1_to) ? "" : s.free_dim_1_to,
                                                 free_dim_2_from = string.IsNullOrEmpty(s.free_dim_2_from) ? "" : s.free_dim_2_from,
                                                 free_dim_2_to = string.IsNullOrEmpty(s.free_dim_2_to) ? "" : s.free_dim_2_to,
                                                 free_dim_3_from = string.IsNullOrEmpty(s.free_dim_3_from) ? "" : s.free_dim_3_from,
                                                 free_dim_3_to = string.IsNullOrEmpty(s.free_dim_3_to) ? "" : s.free_dim_3_to,
                                                 free_dim_4_from = string.IsNullOrEmpty(s.free_dim_4_from) ? "" : s.free_dim_4_from,
                                                 free_dim_4_to = string.IsNullOrEmpty(s.free_dim_4_to) ? "" : s.free_dim_4_to,
                                                 priority = s.priority
                                             }).ToListAsync();

                List<ActionDetailsWithBudgetChange> lstgridDatabdtchangeCopy = lst.gridDatabdtchange.ToList();
                lstgridDatabdtchangeCopy.ForEach(x =>
                {
                    x.account.accountValue = string.IsNullOrEmpty(x.account.accountValue) ? "" : x.account.accountValue;
                    x.department.departmentValue = string.IsNullOrEmpty(x.department.departmentValue) ? "" : x.department.departmentValue;
                    x.functionn.functionValue = string.IsNullOrEmpty(x.functionn.functionValue) ? "" : x.functionn.functionValue;
                    x.project.fk_project_code = string.IsNullOrEmpty(x.project.fk_project_code) ? "" : x.project.fk_project_code;
                    x.freedim1 = new Freedim() { fk_freedim_code = x.freedim1 == null ? "" : string.IsNullOrEmpty(x.freedim1.fk_freedim_code) ? "" : x.freedim1.fk_freedim_code, freedim_name = "" };
                    x.freedim2 = new Freedim() { fk_freedim_code = x.freedim2 == null ? "" : string.IsNullOrEmpty(x.freedim2.fk_freedim_code) ? "" : x.freedim2.fk_freedim_code, freedim_name = "" };
                    x.freedim3 = new Freedim() { fk_freedim_code = x.freedim3 == null ? "" : string.IsNullOrEmpty(x.freedim3.fk_freedim_code) ? "" : x.freedim3.fk_freedim_code, freedim_name = "" };
                    x.freedim4 = new Freedim() { fk_freedim_code = x.freedim4 == null ? "" : string.IsNullOrEmpty(x.freedim4.fk_freedim_code) ? "" : x.freedim4.fk_freedim_code, freedim_name = "" };
                });

                if (line_order_validate.Count > 0)
                {
                    List<clsLine_OrderValidation> lstResult = new List<clsLine_OrderValidation>();

                    foreach (var strA in lstgridDatabdtchangeCopy)
                    {
                        foreach (var strB in line_order_validate)
                        {
                            int chkDeptFrom = string.Compare(strA.department.departmentValue, strB.fk_department_code_from);
                            int chkDeptTo = string.Compare(strA.department.departmentValue, strB.fk_department_code_to);

                            int chkFuncFrom = string.Compare(strA.functionn.functionValue, strB.fk_function_code_from);
                            int chkFuncTo = string.Compare(strA.functionn.functionValue, strB.fk_function_code_to);

                            int chkProjFrom = string.Compare(strA.project.fk_project_code, strB.fk_project_code_from);
                            int chkProjTo = string.Compare(strA.project.fk_project_code, strB.fk_project_code_to);

                            int chkFD1From = string.Compare(strA.freedim1.fk_freedim_code, strB.free_dim_1_from);
                            int chkFD1To = string.Compare(strA.freedim1.fk_freedim_code, strB.free_dim_1_to);

                            int chkFD2From = string.Compare(strA.freedim2.fk_freedim_code, strB.free_dim_2_from);
                            int chkFD2To = string.Compare(strA.freedim2.fk_freedim_code, strB.free_dim_2_to);

                            int chkFD3From = string.Compare(strA.freedim3.fk_freedim_code, strB.free_dim_3_from);
                            int chkFD3To = string.Compare(strA.freedim3.fk_freedim_code, strB.free_dim_3_to);

                            int chkFD4From = string.Compare(strA.freedim4.fk_freedim_code, strB.free_dim_4_from);
                            int chkFD4To = string.Compare(strA.freedim4.fk_freedim_code, strB.free_dim_4_to);

                            if (strA.account.accountValue == strB.fk_account_code
                             && (chkDeptFrom >= 0 && chkDeptTo <= 0)
                             && (chkFuncFrom >= 0 && chkFuncTo <= 0)
                             && (chkProjFrom >= 0 && chkProjTo <= 0)
                             && (chkFD1From >= 0 && chkFD1To <= 0)
                             && (chkFD2From >= 0 && chkFD2To <= 0)
                             && (chkFD3From >= 0 && chkFD3To <= 0)
                             && (chkFD4From >= 0 && chkFD4To <= 0))
                            {
                                lstResult.Add(strB);
                            }
                        }
                    }

                    lstResult = lstResult.OrderBy(x => x.priority).ToList();

                    if (lstResult.Count() > 0)
                    {
                        int action_type = lstResult.FirstOrDefault().action_type;
                        var line_order_forActionType = await (from th in consequenceAdjustedBudgetDbContext.tfp_trans_header
                                                              join td in consequenceAdjustedBudgetDbContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                                                                                                                         equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                                                              where (th.fk_tenant_id == user.tenant_id
                                                                    && td.budget_year == budgetYear
                                                                    && th.action_type == action_type)
                                                              select th.line_order).ToListAsync();

                        if (line_order_forActionType.Count() > 0)
                        {
                            if (line_order_forActionType.Contains(lstResult.FirstOrDefault().line_order))
                            {
                                return lst.errorDescription = "error-" + ((langStringValuesFinPlan.FirstOrDefault(v => v.Key == "FP_Line_Order_Validation_Error")).Value).LangText;
                            }
                            else
                            {
                                line_order = lstResult.FirstOrDefault().line_order;
                            }
                        }
                    }
                    else
                    {
                        List<int> validActionTypeTOsaveNewLineOrder = new List<int>() { 1, 2, 3, 4, 100 };
                        if (lst.pageId == "FinplanOverview" && (validActionTypeTOsaveNewLineOrder.Contains(actionType)))
                        {
                            line_order = await InsertNewLineOrderAsync(user, budgetYear, actionType, lst.actionDescription, lstgridDatabdtchangeCopy);
                        }
                    }
                }
                else
                {
                    List<int> validActionTypeTOsaveNewLineOrder = new List<int>() { 1, 2, 3, 4, 100 };
                    if (lst.pageId == "FinplanOverview" && (validActionTypeTOsaveNewLineOrder.Contains(actionType)))
                    {
                        line_order = await InsertNewLineOrderAsync(user, budgetYear, actionType, lst.actionDescription, lstgridDatabdtchangeCopy);
                    }
                }
            }
            else
            {
                if (lst.pageId == "FinplanOverview")
                {
                    List<clsLine_OrderValidation> line_order_validate = new List<clsLine_OrderValidation>();

                    line_order_validate = await (from s in consequenceAdjustedBudgetDbContext.tmd_finplan_line_setup
                                                 where s.fk_tenant_id == user.tenant_id && s.budget_year == budgetYear && s.action_type == actionType && s.action_name == lst.actionDescription
                                                 orderby s.priority ascending
                                                 select new clsLine_OrderValidation
                                                 {
                                                     action_type = s.action_type,
                                                     line_order = s.line_order,
                                                     fk_account_code = s.fk_account_code,
                                                     fk_department_code_from = string.IsNullOrEmpty(s.fk_department_code_from) ? "" : s.fk_department_code_from,
                                                     fk_department_code_to = string.IsNullOrEmpty(s.fk_department_code_to) ? "" : s.fk_department_code_to,
                                                     fk_function_code_from = string.IsNullOrEmpty(s.fk_function_code_from) ? "" : s.fk_function_code_from,
                                                     fk_function_code_to = string.IsNullOrEmpty(s.fk_function_code_to) ? "" : s.fk_function_code_to,
                                                     fk_project_code_from = string.IsNullOrEmpty(s.fk_project_code_from) ? "" : s.fk_project_code_from,
                                                     fk_project_code_to = string.IsNullOrEmpty(s.fk_project_code_to) ? "" : s.fk_project_code_to,
                                                     free_dim_1_from = string.IsNullOrEmpty(s.free_dim_1_from) ? "" : s.free_dim_1_from,
                                                     free_dim_1_to = string.IsNullOrEmpty(s.free_dim_1_to) ? "" : s.free_dim_1_to,
                                                     free_dim_2_from = string.IsNullOrEmpty(s.free_dim_2_from) ? "" : s.free_dim_2_from,
                                                     free_dim_2_to = string.IsNullOrEmpty(s.free_dim_2_to) ? "" : s.free_dim_2_to,
                                                     free_dim_3_from = string.IsNullOrEmpty(s.free_dim_3_from) ? "" : s.free_dim_3_from,
                                                     free_dim_3_to = string.IsNullOrEmpty(s.free_dim_3_to) ? "" : s.free_dim_3_to,
                                                     free_dim_4_from = string.IsNullOrEmpty(s.free_dim_4_from) ? "" : s.free_dim_4_from,
                                                     free_dim_4_to = string.IsNullOrEmpty(s.free_dim_4_to) ? "" : s.free_dim_4_to,
                                                     priority = s.priority
                                                 }).ToListAsync();

                    List<ActionDetailsWithBudgetChange> lstgridDatabdtchangeCopy = lst.gridDatabdtchange.ToList();
                    lstgridDatabdtchangeCopy.ForEach(x =>
                    {
                        x.account.accountValue = string.IsNullOrEmpty(x.account.accountValue) ? "" : x.account.accountValue;
                        x.department.departmentValue = string.IsNullOrEmpty(x.department.departmentValue) ? "" : x.department.departmentValue;
                        x.functionn.functionValue = string.IsNullOrEmpty(x.functionn.functionValue) ? "" : x.functionn.functionValue;
                        x.project.fk_project_code = string.IsNullOrEmpty(x.project.fk_project_code) ? "" : x.project.fk_project_code;
                        x.freedim1 = new Freedim() { fk_freedim_code = x.freedim1 == null ? "" : string.IsNullOrEmpty(x.freedim1.fk_freedim_code) ? "" : x.freedim1.fk_freedim_code, freedim_name = "" };
                        x.freedim2 = new Freedim() { fk_freedim_code = x.freedim2 == null ? "" : string.IsNullOrEmpty(x.freedim2.fk_freedim_code) ? "" : x.freedim2.fk_freedim_code, freedim_name = "" };
                        x.freedim3 = new Freedim() { fk_freedim_code = x.freedim3 == null ? "" : string.IsNullOrEmpty(x.freedim3.fk_freedim_code) ? "" : x.freedim3.fk_freedim_code, freedim_name = "" };
                        x.freedim4 = new Freedim() { fk_freedim_code = x.freedim4 == null ? "" : string.IsNullOrEmpty(x.freedim4.fk_freedim_code) ? "" : x.freedim4.fk_freedim_code, freedim_name = "" };
                    });
                    List<ActionDetailsWithBudgetChange> datatoAddinTmdFinLineOrder = new List<ActionDetailsWithBudgetChange>();
                    datatoAddinTmdFinLineOrder.AddRange(lstgridDatabdtchangeCopy);

                    if (line_order_validate.Count > 0)
                    {
                        List<clsLine_OrderValidation> newlstResult = new List<clsLine_OrderValidation>();

                        foreach (var strA in lstgridDatabdtchangeCopy)
                        {
                            foreach (var strB in line_order_validate)
                            {
                                int chkDeptFrom = string.Compare(strA.department.departmentValue, strB.fk_department_code_from);
                                int chkDeptTo = string.Compare(strA.department.departmentValue, strB.fk_department_code_to);

                                int chkFuncFrom = string.Compare(strA.functionn.functionValue, strB.fk_function_code_from);
                                int chkFuncTo = string.Compare(strA.functionn.functionValue, strB.fk_function_code_to);

                                int chkProjFrom = string.Compare(strA.project.fk_project_code, strB.fk_project_code_from);
                                int chkProjTo = string.Compare(strA.project.fk_project_code, strB.fk_project_code_to);

                                int chkFD1From = string.Compare(strA.freedim1.fk_freedim_code, strB.free_dim_1_from);
                                int chkFD1To = string.Compare(strA.freedim1.fk_freedim_code, strB.free_dim_1_to);

                                int chkFD2From = string.Compare(strA.freedim2.fk_freedim_code, strB.free_dim_2_from);
                                int chkFD2To = string.Compare(strA.freedim2.fk_freedim_code, strB.free_dim_2_to);

                                int chkFD3From = string.Compare(strA.freedim3.fk_freedim_code, strB.free_dim_3_from);
                                int chkFD3To = string.Compare(strA.freedim3.fk_freedim_code, strB.free_dim_3_to);

                                int chkFD4From = string.Compare(strA.freedim4.fk_freedim_code, strB.free_dim_4_from);
                                int chkFD4To = string.Compare(strA.freedim4.fk_freedim_code, strB.free_dim_4_to);

                                if (strA.account.accountValue == strB.fk_account_code
                                 && (chkDeptFrom >= 0 && chkDeptTo <= 0)
                                 && (chkFuncFrom >= 0 && chkFuncTo <= 0)
                                 && (chkProjFrom >= 0 && chkProjTo <= 0)
                                 && (chkFD1From >= 0 && chkFD1To <= 0)
                                 && (chkFD2From >= 0 && chkFD2To <= 0)
                                 && (chkFD3From >= 0 && chkFD3To <= 0)
                                 && (chkFD4From >= 0 && chkFD4To <= 0))
                                {
                                    newlstResult.Add(strB);
                                    datatoAddinTmdFinLineOrder.Remove(strA);
                                }
                            }
                        }

                        newlstResult = newlstResult.OrderBy(x => x.priority).ToList();

                        if (datatoAddinTmdFinLineOrder.Count > 0)
                        {
                            List<int> validActionTypeTOsaveNewLineOrder = new List<int>() { 1, 2, 3, 4, 100 };
                            if (lst.pageId == "FinplanOverview" && (validActionTypeTOsaveNewLineOrder.Contains(actionType)))
                            {
                                line_order = await InsertNewLineOrderAsync(user, budgetYear, actionType, lst.actionDescription, datatoAddinTmdFinLineOrder, true, line_order_validate.FirstOrDefault().line_order);
                            }
                        }
                    }
                    else
                    {
                        List<int> validActionTypeTOsaveNewLineOrder = new List<int>() { 1, 2, 3, 4, 100 };
                        if (lst.pageId == "FinplanOverview" && (validActionTypeTOsaveNewLineOrder.Contains(actionType)))
                        {
                            line_order = await InsertNewLineOrderAsync(user, budgetYear, actionType, lst.actionDescription, datatoAddinTmdFinLineOrder);
                        }
                    }
                }
                else
                {
                    line_order = 0;
                }
            }

            if (lst.serviceId == null || lst.serviceId.ToLower() == "undefined" || lst.serviceId == "null" || lst.serviceId.ToLower() == "all")
            {
                lst.serviceId = string.Empty;
            }
            else
            {
                lst.serviceId = lst.serviceId.Trim();
            }

            List<string> departmentsList = new List<string>();
            List<string> pkIdList = new List<string>();
            lst.gridDatabdtchange.Where(x => x.id != 0).ToList().ForEach(x => pkIdList.Add(x.id.ToString()));
            List<clsOrgStructure> lstOrgStructure = await _utility.GetTenantOrgStructureAsync(orgVersionContent, userID);
            if (string.IsNullOrEmpty(lst.orgId))
            {
                List<clsOrgIdAndDepartments> lstServiceAreaDepts = string.IsNullOrEmpty(lst.serviceAreaID) ? (await _utility.GetTenantDepartmentsForSelectedOrgIdAsync(orgVersionContent, userID, null, null, true)).ToList() : (await _utility.GetTenantDepartmentsForSelectedOrgIdAsync(orgVersionContent, userID, lst.serviceAreaID, null, true)).ToList();

                foreach (var d in lstServiceAreaDepts)
                {
                    string deptCode = d.departmentValue;
                    departmentsList.Add(deptCode);
                }
            }
            else if (string.IsNullOrEmpty(lst.serviceId) && string.IsNullOrEmpty(lst.orgId))
            {
                //If service unit id passed from yearly budget
                departmentsList = new List<string>();
            }
            else
            {
                clsOrgStructureLevelDetails tenantOrgLevelDetails = _utility.GetTenantOrgLevelDetails(lstOrgStructure);
                List<List<string>> DepartmentsAndFunctions = _utility.GetDepartmentsAndFunctionsForOrgStructure(lstOrgStructure, lst.orgId, lst.serviceId, tenantOrgLevelDetails.level1, tenantOrgLevelDetails.level2);
                departmentsList = DepartmentsAndFunctions[0];
            }

            if (lst.gridDatabdtchange.Any())
            {
                int actionTye = Convert.ToInt32(lst.groupId);
                int defaultActionType = 60;  //Default action type(i.e 60) to get altercodes for 'BudgetChange' when altercode is null
                dynamic lstAlter;
                if (lst.pageId == "BudChangeOverview" || lst.pageId == "BudChangeDetail" || lst.pageId == "ServiceUnitForUnlockedBudget") //for BudgetChange screen
                {
                    lstAlter = await GetAlterCodesAsync(userID, defaultActionType);
                }
                else
                {
                    lstAlter = await GetAlterCodesAsync(userID, actionTye);
                }
                foreach (var item in lstAlter)
                {
                    if (item.isDefault || lstAlter.Count == 1)
                    {
                        lst.gridDatabdtchange.ToList().ForEach(x =>
                        {
                            if (x.alterCode == null || x.alterCode.key == null)
                            {
                                x.alterCode = new clsAlterCode { key = item.key, value = item.value };
                            }
                        });
                        break;
                    }
                }
            }

            int logactionId = 0;
            List<tfp_trans_detail> lstData = new List<tfp_trans_detail>();
            try
            {
                string actionName = string.Empty;
                string actionLongDesc = string.Empty;

                UserData userDetails = await _utility.GetUserDetailsAsync(userID);

                int actionID = await consequenceAdjustedBudgetDbContext.tfp_trans_header.MaxAsync(x => x.pk_action_id);

                if (actionID > 0)
                {
                    actionID = actionID + 1;
                }
                else
                {
                    actionID = 2000;
                }
                logactionId = actionID;
                if (string.IsNullOrEmpty(lst.fk_alter_code))
                {
                    //add default altercode
                    int actiontype = Convert.ToInt32(lst.groupId);
                    var defaultaltcode = await consequenceAdjustedBudgetDbContext.tco_fp_alter_codes.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.status == 1 && x.default_action_type == actiontype).GroupBy(x => x.pk_alter_code).Select(x => x.Min(y => y.pk_alter_code)).ToListAsync();
                    lst.fk_alter_code = defaultaltcode.Count == 0 ? "" : defaultaltcode.First().ToString();
                }

                //insert new tags and get the list to tag ids
                List<string> lstTags = null;
                if (lst.tagsData.Any())
                {
                    lstTags = await InsertActionTagsAsync(userID, lst.tagsData);
                }
                //bool isNewAction = false;
                List<clsServiceAreaData> lstBudgetLimitsUpdate = new List<Helpers.clsServiceAreaData>();
                if (await consequenceAdjustedBudgetDbContext.tfp_trans_header.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_action_id == lst.actionId) == null)
                {
                    tfp_trans_header tfpTransHeaderData = new tfp_trans_header()
                    {
                        pk_action_id = actionID,
                        fk_tenant_id = userDetails.tenant_id,
                        line_order = line_order,
                        isManuallyAdded = 1,
                        description = lst.actionDescription,
                        consequence = lst.consequence == null ? string.Empty : _editorExtensions.RemoveCommentSuggestionTags(lst.consequence),
                        start_date = DateTime.UtcNow,
                        action_type = Convert.ToInt32(lst.groupId),
                        action_source = 0,
                        title = string.Empty,
                        fk_area_id = lst.fk_area_id,
                        tag = lst.tag,
                        tags = lstTags == null ? string.Empty : String.Join(",", lstTags),
                        priority = lst.priority,
                        long_description = string.IsNullOrEmpty(lst.long_description) ? string.Empty : _editorExtensions.RemoveCommentSuggestionTags(lst.long_description),
                        updated = DateTime.UtcNow,
                        updated_by = userDetails.pk_id,
                        display_financial_plan_flag = lst.display_financial_plan_flag,
                        display_cab_flag = lst.display_cab_flag,
                        display_description_apendix_flag = lst.display_description_apendix_flag,
                        fk_cat_id = new Guid(lst.cat_id.ToString()),
                        financial_plan_description = lst.financial_plan_description == null ? string.Empty : _editorExtensions.RemoveCommentSuggestionTags(lst.financial_plan_description),
                        consequence_flag = lst.consequence_flag,
                        different_external_description_flag = lst.different_external_description_flag,
                        change_text_flag = lst.change_text_flag,
                        finished_date = lst.finishedDate == null ? DateTime.UtcNow.Date : Convert.ToDateTime(lst.finishedDate),
                        display_zero_action = lst.DisplayZeroAction,
                        goals_tags = lst.goalsTagsData,
                        targets_tags = lst.targetsTagsData,
                        strategies_tags = lst.strategiesTagsData,
                        update_annual_budget = lst.updateYearlyBudget,
                        update_annual_budget_next_year = lst.updateYearlyBudgetNxtYear,
                        update_next_year_finplan = lst.selectedChangeId.key != 0 ? true : false,
                        log_id = Guid.NewGuid(),
                        org_id = orgIdToInsertInHeader,
                        org_level = orgLevelToInsertInHeader,
                        long_description_id = Guid.NewGuid(),
                        financial_plan_description_id = Guid.NewGuid(),
                        consequence_id = Guid.NewGuid()
                    };
                    consequenceAdjustedBudgetDbContext.tfp_trans_header.Add(tfpTransHeaderData);
                    await _utility.LogFinplanActionChangesAsync(userDetails, budgetYear, consequenceAdjustedBudgetDbContext, tfpTransHeaderData, lst.changeid, false);
                    await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                    List<ActionDetailsWithBudgetChange> addedActions = lst.gridDatabdtchange.Where(x => x.isDeleted == false).ToList();
                    List<int> addedActionIds = addedActions.Select(x => x.id).ToList();
                    List<tfp_trans_detail> details =
                        await consequenceAdjustedBudgetDbContext.tfp_trans_detail.Where(
                            x => x.fk_tenant_id == userDetails.tenant_id &&
                                 x.fk_action_id == actionID && addedActionIds.Contains(x.pk_id)).ToListAsync();

                    foreach (ActionDetailsWithBudgetChange a in addedActions)
                    {
                        tfp_trans_detail td = details.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id &&
                            x.fk_action_id == actionID && x.pk_id == a.id);
                        if (td == null)
                        {
                            //int invDetailId = 0;
                            Guid invTransId = Guid.NewGuid();
                            if (lst.groupId == "21")
                            {
                                //invDetailId = UpdateInvestmentDetailForOEaction(a, userID, budgetYear, lst.changeid, Convert.ToInt32(lst.investmentId));
                                await UpdateProjectInvestmentDetailForOEactionAsync(a, userID, budgetYear, lst.changeid, invTransId);
                            }
                            lstData.Add(
                            new tfp_trans_detail()
                            {
                                fk_action_id = actionID,
                                fk_tenant_id = userDetails.tenant_id,
                                budget_year = lst.BudgetYear,
                                fk_account_code = a.account.accountValue,
                                department_code = a.department.departmentValue,
                                function_code = a.functionn.functionValue,
                                asset_code = string.Empty,
                                year_1_amount = a.year1 * 1000,
                                year_2_amount = a.year2 * 1000,
                                year_3_amount = a.year3 * 1000,
                                year_4_amount = a.year4 * 1000,
                                year_5_amount = a.year5 * 1000,
                                updated = DateTime.UtcNow,
                                updated_by = userDetails.pk_id,
                                fk_change_id = lst.changeid,
                                project_code = string.IsNullOrEmpty(a.project.fk_project_code) ? string.Empty : a.project.fk_project_code,
                                free_dim_1 = a.freedim1 == null ? string.Empty : string.IsNullOrEmpty(a.freedim1.fk_freedim_code) ? string.Empty : a.freedim1.fk_freedim_code,
                                free_dim_2 = a.freedim2 == null ? string.Empty : string.IsNullOrEmpty(a.freedim2.fk_freedim_code) ? string.Empty : a.freedim2.fk_freedim_code,
                                free_dim_3 = a.freedim3 == null ? string.Empty : string.IsNullOrEmpty(a.freedim3.fk_freedim_code) ? string.Empty : a.freedim3.fk_freedim_code,
                                free_dim_4 = a.freedim4 == null ? string.Empty : string.IsNullOrEmpty(a.freedim4.fk_freedim_code) ? string.Empty : a.freedim4.fk_freedim_code,
                                description = a.description,
                                fk_adjustment_code = a.adjustmentCode == null ? string.Empty : string.IsNullOrEmpty(a.adjustmentCode.key) ? string.Empty : a.adjustmentCode.key,
                                fk_alter_code = a.alterCode == null ? string.Empty : string.IsNullOrEmpty(a.alterCode.key) ? string.Empty : a.alterCode.key,
                                fk_key_id = a.periodicKey == null ? 0 : string.IsNullOrEmpty(a.periodicKey.key) ? 0 : Convert.ToInt32(a.periodicKey.key),
                                fk_investment_id = 0,
                                investment_row_id = 0,
                                inv_trans_id = invTransId,
                                fk_main_project_code = lst.mainProjectCode,
                                fk_adj_code = lst.fk_adjustment_code == null ? string.Empty : lst.fk_adjustment_code
                            });

                            lstBudgetLimitsUpdate.Add(new Helpers.clsServiceAreaData()
                            {
                                PkID = 0,
                                departmentCode = a.department.departmentValue,
                                functionCode = a.functionn.functionValue,
                                newAmount1 = a.year1,
                                newAmount2 = a.year2,
                                newAmount3 = a.year3,
                                newAmount4 = a.year4
                            });
                        }
                    }

                    await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                }
                else
                {
                    if (lst.pageId != "ServiceUnitForUnlockedBudget")
                    {
                        tfp_trans_header actionHeader = await consequenceAdjustedBudgetDbContext.tfp_trans_header.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_action_id == lst.actionId);
                        actionHeader.description = string.IsNullOrEmpty(lst.actionDescription) ? string.Empty : lst.actionDescription;
                        if (!string.IsNullOrEmpty(lst.long_description))
                        {
                            actionHeader.long_description = _editorExtensions.RemoveCommentSuggestionTags(lst.long_description);
                        }
                        if (!string.IsNullOrEmpty(lst.consequence))
                        {
                            actionHeader.consequence = _editorExtensions.RemoveCommentSuggestionTags(lst.consequence);
                        }
                        actionHeader.display_financial_plan_flag = lst.display_financial_plan_flag;
                        actionHeader.display_cab_flag = lst.display_cab_flag;
                        actionHeader.display_description_apendix_flag = lst.display_description_apendix_flag;
                        actionHeader.fk_cat_id = new Guid(lst.cat_id.ToString());
                        if (!string.IsNullOrEmpty(lst.financial_plan_description))
                        {
                            actionHeader.financial_plan_description = _editorExtensions.RemoveCommentSuggestionTags(lst.financial_plan_description);
                        }
                        actionHeader.priority = lst.priority;
                        actionHeader.consequence_flag = lst.consequence_flag;
                        actionHeader.different_external_description_flag = lst.different_external_description_flag;
                        actionHeader.tag = lst.tag;
                        actionHeader.tags = lstTags == null ? string.Empty : String.Join(",", lstTags);
                        actionHeader.goals_tags = lst.goalsTagsData;
                        actionHeader.targets_tags = lst.targetsTagsData;
                        actionHeader.strategies_tags = lst.strategiesTagsData;
                        actionHeader.change_text_flag = lst.change_text_flag;
                        actionHeader.finished_date = lst.finishedDate == null ? DateTime.UtcNow.Date : Convert.ToDateTime(lst.finishedDate);
                        actionHeader.display_zero_action = lst.DisplayZeroAction;
                        actionHeader.update_annual_budget = lst.updateYearlyBudget;
                        actionHeader.update_annual_budget_next_year = lst.updateYearlyBudgetNxtYear;
                        actionHeader.update_next_year_finplan = lst.createNextYearData;
                        if (!isUpdateUserdetails)
                        {
                            actionHeader.updated = DateTime.UtcNow;
                            actionHeader.updated_by = userDetails.pk_id;
                        }
                        await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                        actionID = actionHeader.pk_action_id;
                        actionName = actionHeader.description;
                        actionLongDesc = actionHeader.long_description;
                    }
                    else
                    {
                        actionID = lst.actionId;
                        actionName = lst.actionDescription;
                        actionLongDesc = lst.long_description;
                    }
                    List<ActionDetailsWithBudgetChange> addedActions = lst.gridDatabdtchange.Where(x => x.isDeleted == false).ToList();
                    List<int> addedActionIds = addedActions.Select(x => x.id).ToList();
                    List<tfp_trans_detail> allDetails = await consequenceAdjustedBudgetDbContext.tfp_trans_detail.Where(
                            x => x.fk_tenant_id == userDetails.tenant_id &&
                                 x.fk_action_id == actionID &&
                                 x.budget_year == budgetYear).ToListAsync();

                    var pkIdsCurrentYear = (allDetails != null && allDetails.Any()) ? allDetails.Select(x => x.pk_id).Distinct().ToList() : new List<int>();

                    lstExistingData.AddRange((from a in allDetails
                                              select new AccountingInfo
                                              {
                                                  fk_account_code = a.fk_account_code,
                                                  department_code = a.department_code,
                                                  function_code = a.function_code,
                                                  project_code = a.project_code,
                                                  free_dim_1 = a.free_dim_1,
                                                  free_dim_2 = a.free_dim_2,
                                                  free_dim_3 = a.free_dim_3,
                                                  free_dim_4 = a.free_dim_4,
                                                  fk_key_id = a.fk_key_id.HasValue ? a.fk_key_id.Value : 0,
                                                  description = a.description,
                                                  fk_alter_code = a.fk_alter_code,
                                                  fk_adjustment_code = a.fk_adjustment_code,
                                                  year_1_amount = a.year_1_amount,
                                                  year_2_amount = a.year_2_amount,
                                                  year_3_amount = a.year_3_amount,
                                                  year_4_amount = a.year_4_amount,
                                                  year_5_amount = a.year_5_amount,
                                                  type = "before"
                                              }).ToList());

                    var newYearActionData = await consequenceAdjustedBudgetDbContext.tfp_trans_detail.Where(
                            x => x.fk_tenant_id == userDetails.tenant_id &&
                                 x.budget_year == (budgetYear + 1) &&
                                 pkIdsCurrentYear.Contains(x.previous_pk_id.Value)).ToListAsync();

                    if (newYearActionData.Any())
                    {
                        int newYearActionId = newYearActionData.FirstOrDefault().fk_action_id;

                        List<tfp_trans_detail> allDetailsNextYear =
                        await consequenceAdjustedBudgetDbContext.tfp_trans_detail.Where(
                            x => x.fk_tenant_id == userDetails.tenant_id &&
                                 x.budget_year == (budgetYear + 1) &&
                                 x.fk_action_id == newYearActionId).ToListAsync();

                        lstExistingDataNextYear.AddRange((from a in allDetailsNextYear
                                                          select new AccountingInfo
                                                          {
                                                              fk_account_code = a.fk_account_code,
                                                              department_code = a.department_code,
                                                              function_code = a.function_code,
                                                              project_code = a.project_code,
                                                              free_dim_1 = a.free_dim_1,
                                                              free_dim_2 = a.free_dim_2,
                                                              free_dim_3 = a.free_dim_3,
                                                              free_dim_4 = a.free_dim_4,
                                                              fk_key_id = a.fk_key_id.HasValue ? a.fk_key_id.Value : 0,
                                                              description = a.description,
                                                              fk_alter_code = a.fk_alter_code,
                                                              fk_adjustment_code = a.fk_adjustment_code,
                                                              year_1_amount = a.year_1_amount,
                                                              type = "before"
                                                          }).ToList());
                    }

                    List<tfp_trans_detail> details =
                        allDetails.Where(
                            x => x.fk_tenant_id == userDetails.tenant_id &&
                                 x.fk_action_id == actionID && addedActionIds.Contains(x.pk_id)).ToList();
                    allDetails.ForEach(x =>
                    {
                        lstBudgetLimitsUpdate.Add(new Helpers.clsServiceAreaData()
                        {
                            PkID = x.pk_id,
                            departmentCode = x.department_code,
                            functionCode = x.function_code,
                            oldAmount1 = x.year_1_amount / 1000,
                            newAmount1 = x.year_1_amount / 1000,
                            oldAmount2 = x.year_2_amount / 1000,
                            newAmount2 = x.year_2_amount / 1000,
                            oldAmount3 = x.year_3_amount / 1000,
                            newAmount3 = x.year_3_amount / 1000,
                            oldAmount4 = x.year_4_amount / 1000,
                            newAmount4 = x.year_4_amount / 1000,
                        });
                    });

                    foreach (ActionDetailsWithBudgetChange a in addedActions)
                    {
                        tfp_trans_detail td = details.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id &&
                                                        x.fk_action_id == actionID && x.pk_id == a.id);
                        Guid invTransId = Guid.NewGuid();
                        if (td != null)
                        {
                            invTransId = td.inv_trans_id ?? Guid.Empty;
                            td.fk_account_code = a.account.accountValue;
                            td.department_code = a.department.departmentValue;
                            td.function_code = a.functionn.functionValue;
                            td.project_code = string.Empty;
                            td.asset_code = string.Empty;
                            td.year_1_amount = a.year1 * 1000;
                            td.year_2_amount = a.year2 * 1000;
                            td.year_3_amount = a.year3 * 1000;
                            td.year_4_amount = a.year4 * 1000;
                            td.year_5_amount = a.year5 * 1000;
                            if (!isUpdateUserdetails)
                            {
                                td.updated = DateTime.UtcNow;
                                td.updated_by = userDetails.pk_id;
                            }
                            td.fk_change_id = lst.changeid;
                            td.project_code = string.IsNullOrEmpty(a.project.fk_project_code) ? string.Empty : a.project.fk_project_code;
                            td.free_dim_1 = a.freedim1 == null ? string.Empty : string.IsNullOrEmpty(a.freedim1.fk_freedim_code) ? string.Empty : a.freedim1.fk_freedim_code;
                            td.free_dim_2 = a.freedim2 == null ? string.Empty : string.IsNullOrEmpty(a.freedim2.fk_freedim_code) ? string.Empty : a.freedim2.fk_freedim_code;
                            td.free_dim_3 = a.freedim3 == null ? string.Empty : string.IsNullOrEmpty(a.freedim3.fk_freedim_code) ? string.Empty : a.freedim3.fk_freedim_code;
                            td.free_dim_4 = a.freedim4 == null ? string.Empty : string.IsNullOrEmpty(a.freedim4.fk_freedim_code) ? string.Empty : a.freedim4.fk_freedim_code;
                            td.description = a.description;
                            td.fk_adjustment_code = a.adjustmentCode == null ? string.Empty : string.IsNullOrEmpty(a.adjustmentCode.key) ? string.Empty : a.adjustmentCode.key;
                            td.fk_alter_code = a.alterCode == null ? string.Empty : string.IsNullOrEmpty(a.alterCode.key) ? string.Empty : a.alterCode.key;
                            td.fk_key_id = a.periodicKey == null ? 0 : string.IsNullOrEmpty(a.periodicKey.key) ? 0 : Convert.ToInt32(a.periodicKey.key);
                            td.fk_main_project_code = lst.mainProjectCode;
                            td.fk_adj_code = lst.fk_adjustment_code == null ? string.Empty : lst.fk_adjustment_code;
                            lstBudgetLimitsUpdate.ForEach(x =>
                            {
                                if (x.PkID == td.pk_id)
                                {
                                    x.newAmount1 = a.year1;
                                    x.newAmount2 = a.year2;
                                    x.newAmount3 = a.year3;
                                    x.newAmount4 = a.year4;
                                }
                            });
                        }
                        else
                        {
                            //int invDetailId = 0;
                            //if (lst.groupId == "21")
                            //{
                            //    invDetailId = UpdateInvestmentDetailForOEaction(a, userID, budgetYear, lst.changeid, Convert.ToInt32(lst.investmentId));
                            //}
                            lstData.Add(
                            new tfp_trans_detail()
                            {
                                fk_action_id = actionID,
                                fk_tenant_id = userDetails.tenant_id,
                                budget_year = lst.BudgetYear,
                                fk_account_code = a.account.accountValue,
                                department_code = a.department.departmentValue,
                                function_code = a.functionn.functionValue,
                                asset_code = string.Empty,
                                year_1_amount = a.year1 * 1000,
                                year_2_amount = a.year2 * 1000,
                                year_3_amount = a.year3 * 1000,
                                year_4_amount = a.year4 * 1000,
                                year_5_amount = a.year5 * 1000,
                                updated = DateTime.UtcNow,
                                updated_by = userDetails.pk_id,
                                fk_change_id = lst.changeid,
                                project_code = string.IsNullOrEmpty(a.project.fk_project_code) ? string.Empty : a.project.fk_project_code,
                                free_dim_1 = a.freedim1 == null ? string.Empty : string.IsNullOrEmpty(a.freedim1.fk_freedim_code) ? string.Empty : a.freedim1.fk_freedim_code,
                                free_dim_2 = a.freedim2 == null ? string.Empty : string.IsNullOrEmpty(a.freedim2.fk_freedim_code) ? string.Empty : a.freedim2.fk_freedim_code,
                                free_dim_3 = a.freedim3 == null ? string.Empty : string.IsNullOrEmpty(a.freedim3.fk_freedim_code) ? string.Empty : a.freedim3.fk_freedim_code,
                                free_dim_4 = a.freedim4 == null ? string.Empty : string.IsNullOrEmpty(a.freedim4.fk_freedim_code) ? string.Empty : a.freedim4.fk_freedim_code,
                                description = a.description,
                                fk_adjustment_code = a.adjustmentCode == null ? string.Empty : string.IsNullOrEmpty(a.adjustmentCode.key) ? string.Empty : a.adjustmentCode.key,
                                fk_alter_code = a.alterCode == null ? string.Empty : string.IsNullOrEmpty(a.alterCode.key) ? string.Empty : a.alterCode.key,
                                fk_key_id = a.periodicKey == null ? 0 : string.IsNullOrEmpty(a.periodicKey.key) ? 0 : Convert.ToInt32(a.periodicKey.key),
                                fk_main_project_code = lst.mainProjectCode,
                                investment_row_id = 0,
                                inv_trans_id = invTransId,
                                fk_adj_code = lst.fk_adjustment_code == null ? string.Empty : lst.fk_adjustment_code
                            });

                            lstBudgetLimitsUpdate.Add(new Helpers.clsServiceAreaData()
                            {
                                PkID = 0,
                                departmentCode = a.department.departmentValue,
                                functionCode = a.functionn.functionValue,
                                newAmount1 = a.year1,
                                newAmount2 = a.year2,
                                newAmount3 = a.year3,
                                newAmount4 = a.year4
                            });
                        }
                        //Update project transactions based on transaction id
                        if (lst.groupId == "21")
                        {
                            await UpdateProjectInvestmentDetailForOEactionAsync(a, userID, budgetYear, lst.changeid, invTransId);
                        }
                        await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                    }
                    foreach (Framsikt.BL.Helpers.ActionDetailsWithBudgetChange a in lst.gridDatabdtchange.Where(x => x.isDeleted == true).ToList())
                    {
                        int? orgLevel = lst.orgLevel;
                        tfp_trans_detail td = orgLevel == 1 ? await consequenceAdjustedBudgetDbContext.tfp_trans_detail.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                                                   && x.pk_id == a.id
                                                                                                                   && x.fk_action_id == actionID
                                                                                                                   && x.department_code == a.department.departmentValue
                                                                                                                   && x.function_code == a.functionn.functionValue
                                                                                                                   && x.fk_account_code == a.account.accountValue) :
                                                              await consequenceAdjustedBudgetDbContext.tfp_trans_detail.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                                                   && x.pk_id == a.id
                                                                                                                   && x.fk_action_id == actionID
                                                                                                                   && x.department_code == a.department.departmentValue
                                                                                                                   && x.function_code == a.functionn.functionValue
                                                                                                                   && x.fk_account_code == a.account.accountValue
                                                                                                                   && departmentsList.Contains(a.department.departmentValue));
                        if (td != null)
                        {
                            consequenceAdjustedBudgetDbContext.tfp_trans_detail.Remove(td);
                            await consequenceAdjustedBudgetDbContext.SaveChangesAsync();

                            lstBudgetLimitsUpdate.ForEach(x =>
                            {
                                if (x.PkID == td.pk_id)
                                {
                                    x.oldAmount1 = td.year_1_amount / 1000;
                                    x.oldAmount2 = td.year_2_amount / 1000;
                                    x.oldAmount3 = td.year_3_amount / 1000;
                                    x.oldAmount4 = td.year_4_amount / 1000;
                                    x.newAmount1 = 0;
                                    x.newAmount2 = 0;
                                    x.newAmount3 = 0;
                                    x.newAmount4 = 0;
                                }
                            });
                        }
                        //Delete project transactions based on transactionId
                        if (lst.groupId == "21")
                        {
                            consequenceAdjustedBudgetDbContext.tfp_proj_transactions.RemoveRange(consequenceAdjustedBudgetDbContext.tfp_proj_transactions.Where(x => x.trans_id == td.inv_trans_id && x.fk_tenant_id == userDetails.tenant_id));
                        }
                    }
                    await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                }
                consequenceAdjustedBudgetDbContext.AddRange(lstData);
                await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                pkIdList.AddRange(lstData.Select(x => x.pk_id.ToString()).ToList());
                if (lst.change_text_flag == true && lst.changeid != -1)
                {
                    await SaveBudgetPhaseIdDescriptionsAsync(userID, budgetYear, actionID, lst.changeid, lst.activeChangeIdDesc);
                }

                if (lst.pageId == "BudChangeOverview" || lst.pageId == "BudChangeDetail" || lst.pageId == "FinplanOverview" || lst.pageId == "FinplanDetail" || lst.pageId == "ServiceUnitForUnlockedBudget")
                {
                    string resultOfInsertIntoTbuTransDetailWhenYearlyBudgetChecked = await InsertIntoTbuTransDetailWhenYearlyBudgetCheckedAsync(userID, lst, actionID, new List<tbu_budget_changes>(), actionName == string.Empty ? lst.actionDescription : actionName, actionLongDesc, line_order, Convert.ToInt32(lst.groupId), lst.BudgetYear, false, false, false, lstExistingData);

                    if (resultOfInsertIntoTbuTransDetailWhenYearlyBudgetChecked != "Success")
                    {
                        throw new InvalidDataException();
                    }
                }

                await _utility.SaveActionLogAsync(userID, actionID, lst.actionDescription, Convert.ToInt32(lst.groupId), lst.gridDatabdtchange.Where(x => x.isDeleted == true).Sum(x => x.year1) * 1000,
                    lst.gridDatabdtchange.Where(x => x.isDeleted == true).Sum(x => x.year2) * 1000,
                    lst.gridDatabdtchange.Where(x => x.isDeleted == true).Sum(x => x.year3) * 1000,
                    lst.gridDatabdtchange.Where(x => x.isDeleted == true).Sum(x => x.year4) * 1000,
                    string.Empty, clsConstants.ActionTransaction.DEL.ToString(), budgetYear);

                if (lst.pageId == "FinplanDetail")
                {
                    string firstLevel = string.Empty;
                    string secondLevel = string.Empty;

                    if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_1").Where(a => !string.IsNullOrEmpty(a.departmentCode)).Select(y => y.departmentCode).Count() > 0)
                    {
                        firstLevel = "dept";
                    }
                    else if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_1").Count(y => !string.IsNullOrEmpty(y.functionCode)) > 0)
                    {
                        firstLevel = "function";
                    }

                    if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_2").Where(a => !string.IsNullOrEmpty(a.departmentCode)).Select(y => y.departmentCode).Count() > 0)
                    {
                        secondLevel = "dept";
                    }
                    else if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_2").Where(a => !string.IsNullOrEmpty(a.functionCode)).Select(y => y.functionCode).Count() > 0)
                    {
                        secondLevel = "function";
                    }

                    lstBudgetLimitsUpdate = _utility.AssignOrgIdAndServiceIdToResultSet(orgVersionContent, lstBudgetLimitsUpdate, lstOrgStructure, firstLevel, secondLevel);

                    lstBudgetLimitsUpdate.ForEach(x =>
                    {
                        x.differenceYear1 = x.newAmount1 - x.oldAmount1;
                        x.differenceYear2 = x.newAmount2 - x.oldAmount2;
                        x.differenceYear3 = x.newAmount3 - x.oldAmount3;
                        x.differenceYear4 = x.newAmount4 - x.oldAmount4;
                    });

                    var groupedData = (from l in lstBudgetLimitsUpdate
                                       group l by new { l.orgId, l.serviceId } into g
                                       select new
                                       {
                                           orgId = g.Key.orgId,
                                           serviceId = g.Key.serviceId,
                                           differenceYear1 = g.Sum(x => x.differenceYear1),
                                           differenceYear2 = g.Sum(x => x.differenceYear2),
                                           differenceYear3 = g.Sum(x => x.differenceYear3),
                                           differenceYear4 = g.Sum(x => x.differenceYear4)
                                       }).ToList();

                    //groupedData.ForEach(x =>
                    //{
                    //});

                    foreach (var x in groupedData)
                    {
                        int action_type = lst.groupId == "9" ? 90 : lst.groupId == "31" ? 30 : lst.groupId == "41" ? 40 : 0;
                        tfp_budget_limits tfpBudgetLimit = await consequenceAdjustedBudgetDbContext.tfp_budget_limits.FirstOrDefaultAsync(y => y.fk_tenant_id == userDetails.tenant_id
                            && y.budget_year == budgetYear
                            && y.action_type == action_type
                            && y.fp_level_1_value == x.orgId
                            && y.fp_level_2_value == x.serviceId);

                        if (tfpBudgetLimit != null)
                        {
                            tfpBudgetLimit.year_1_limit = tfpBudgetLimit.year_1_limit + (x.differenceYear1 * 1000);
                            tfpBudgetLimit.year_2_limit = tfpBudgetLimit.year_2_limit + (x.differenceYear2 * 1000);
                            tfpBudgetLimit.year_3_limit = tfpBudgetLimit.year_3_limit + (x.differenceYear3 * 1000);
                            tfpBudgetLimit.year_4_limit = tfpBudgetLimit.year_4_limit + (x.differenceYear4 * 1000);

                            tfpBudgetLimit.updated = DateTime.UtcNow;
                            tfpBudgetLimit.updated_by = userDetails.pk_id;

                            consequenceAdjustedBudgetDbContext.SaveChanges();
                        }
                    }
                }

                await UpdateCostReductionAndNonallocatedDSAsync(userID, budgetYear, orgVersionContent, lst);

                vw_tco_parameters finplanEstablished = await consequenceAdjustedBudgetDbContext.vw_tco_parameters.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.param_name == "FINPLAN_ESTABLISHED" && x.param_value == (budgetYear + 1).ToString());

                if (!string.IsNullOrEmpty(lst.pageId) && (lst.pageId == "BudChangeOverview" || lst.pageId == "BudChangeDetail") && (finplanEstablished != null))
                {
                    int nextYearActionId = 0;
                    if (lst.pageId == "BudChangeOverview")
                    {
                        consequenceAdjustedBudgetDbContext.tco_finplan_adj_codes_actions_temp.Add(new tco_finplan_adj_codes_actions_temp()
                        {
                            fk_tenant_id = userDetails.tenant_id,
                            budget_year = budgetYear,
                            finplan_user_adj_code = lst.fk_adjustment_code,
                            fk_temp_id = 0,
                            fk_action_id = actionID,
                            is_sam = false,
                            is_finplan = true,
                            updated = DateTime.UtcNow,
                            updated_by = userDetails.pk_id,
                            is_transferred = false,
                            pkIdList = string.Join(",", pkIdList)
                        });
                        await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                    }
                    else
                    {
                        consequenceAdjustedBudgetDbContext.tco_finplan_adj_codes_actions_temp.Add(new tco_finplan_adj_codes_actions_temp()
                        {
                            fk_tenant_id = userDetails.tenant_id,
                            budget_year = budgetYear,
                            finplan_user_adj_code = lst.fk_adjustment_code,
                            fk_temp_id = -1,
                            fk_action_id = actionID,
                            is_sam = false,
                            is_finplan = true,
                            updated = DateTime.UtcNow,
                            updated_by = userDetails.pk_id,
                            is_transferred = false,
                            pkIdList = string.Join(",", pkIdList)
                        });
                        await consequenceAdjustedBudgetDbContext.SaveChangesAsync();
                    }

                    if (lst.updateYearlyBudgetNxtYear)
                    {
                        lst.updateYearlyBudget = true;
                        string resultOfInsertIntoTbuTransDetailWhenYearlyBudgetChecked =
                            await InsertIntoTbuTransDetailWhenYearlyBudgetCheckedAsync(userID, lst, nextYearActionId,
                                new List<tbu_budget_changes>(),
                                actionName == string.Empty ? lst.actionDescription : actionName, actionLongDesc,
                                line_order, Convert.ToInt32(lst.groupId), lst.BudgetYear + 1, false, true, true,
                                lstExistingDataNextYear);

                        if (resultOfInsertIntoTbuTransDetailWhenYearlyBudgetChecked != "Success")
                        {
                            throw new InvalidDataException();
                        }
                    }
                }

                if (isStrategyAction)
                {
                    //Update actionId column in strategy table for strategy actions
                    await UpdateStrategyTable(user, actionID, lst.strategyActionId, lst.BudgetYear);
                }

                if (lst.pageId == "BudChangeDetail" || lst.pageId == "BudChangeOverview")
                {
                    var finplanLevel1Value = await _utility.GetParameterValueAsync(userID, "FINPLAN_LEVEL_1");
                    if (!string.IsNullOrEmpty(finplanLevel1Value) && finplanLevel1Value.StartsWith("org_id_"))
                    {
                        if (finplanLevel1Value == "org_id_1")
                        {
                            await _staffPlanning.CreateSpFilterJobAsync(userID, budgetYear, 1, lst.orgId, (lst.createNextYearData && lst.updateYearlyBudgetNxtYear));
                        }
                        else if (finplanLevel1Value == "org_id_2")
                        {
                            await _staffPlanning.CreateSpFilterJobAsync(userID, budgetYear, 2, lst.orgId, (lst.createNextYearData && lst.updateYearlyBudgetNxtYear));
                        }
                        else if (finplanLevel1Value == "org_id_3")
                        {
                            await _staffPlanning.CreateSpFilterJobAsync(userID, budgetYear, 3, lst.orgId, (lst.createNextYearData && lst.updateYearlyBudgetNxtYear));
                        }
                        else if (finplanLevel1Value == "org_id_4")
                        {
                            await _staffPlanning.CreateSpFilterJobAsync(userID, budgetYear, 4, lst.orgId, (lst.createNextYearData && lst.updateYearlyBudgetNxtYear));
                        }
                    }
                }

                return lst.actionId == 0 ? actionID.ToString() : lst.actionId.ToString();
            }
            catch (Exception)
            {
                await _utility.SaveActionLogAsync(userID, logactionId, lst.actionDescription, Convert.ToInt32(lst.groupId), 0, 0, 0, 0, lst.screenId, clsConstants.ActionTransaction.ADD.ToString(), lst.BudgetYear);
                throw;
            }
        }

        public string UpdateCostReductionAndNonallocatedDS(string userID, int budgetYear,
            ClsOrgVersionSpecificContent orgVersionContent, ActionWithBudgetChange lst)
        {
            var result = UpdateCostReductionAndNonallocatedDSAsync(userID, budgetYear, orgVersionContent, lst)
                .GetAwaiter().GetResult();
            return result;
        }

        public async Task<string> UpdateCostReductionAndNonallocatedDSAsync(string userID, int budgetYear, ClsOrgVersionSpecificContent orgVersionContent, ActionWithBudgetChange lst)
        {
            string message = string.Empty;
            if (lst.gridDatabdtchange.Any() && lst.pageId != "FinplanDetail")
            {
                if (lst.groupId == "31" || lst.groupId == "41" || lst.groupId == "9")
                {
                    if (string.IsNullOrEmpty(lst.serviceId) || lst.serviceId == "ALL")
                    {
                        if (lst.orgLevel != null)
                        {
                            var displayControl = await DisplayFinplanLevel2ItemsAsync(userID, lst.orgId, lst.orgLevel.Value);
                            {
                                if (Convert.ToBoolean(displayControl["displayAllBudgetProposalTabs"]) == true)
                                {
                                    if (Convert.ToString(displayControl["fpLevel1Type"]) == "orgId" && Convert.ToString(displayControl["fpLevel2Type"]) == "orgId")
                                    {
                                        if (Convert.ToInt32(displayControl["fpLevel1Value"]) == lst.orgLevel.Value)
                                        {
                                            message = await UpdateNonAllocatedDosAsync(userID, lst.groupId, lst.orgId, budgetYear, lst.fk_alter_code, lst.changeid, null);
                                        }
                                        else if (Convert.ToInt32(displayControl["fpLevel2Value"]) == lst.orgLevel.Value)
                                        {
                                            JArray orgStructure = await _utility.GetOrgStructureAsync(orgVersionContent, userID);
                                            List<ClsOrgStructureContent> lstOrgStructureContent = orgStructure.ToObject<List<ClsOrgStructureContent>>();
                                            List<ClsOrgStructureFlat> lstResult = _utility.GetFlatOrgStructure(lstOrgStructureContent, new List<ClsOrgStructureFlat>(), null);

                                            string level1OrgId = string.Empty;
                                            string level2ServiceId = string.Empty;
                                            if (lst.orgLevel.Value == 5)
                                            {
                                                lstResult = lstResult.Where(x => x.level5Id == lst.orgId).ToList();
                                                if (lstResult.Any())
                                                {
                                                    level1OrgId = lstResult.FirstOrDefault().level4Id;
                                                }
                                            }
                                            else if (lst.orgLevel.Value == 4)
                                            {
                                                lstResult = lstResult.Where(x => x.level4Id == lst.orgId).ToList();
                                                if (lstResult.Any())
                                                {
                                                    level1OrgId = lstResult.FirstOrDefault().level3Id;
                                                }
                                            }
                                            else if (lst.orgLevel.Value == 3)
                                            {
                                                lstResult = lstResult.Where(x => x.level3Id == lst.orgId).ToList();
                                                if (lstResult.Any())
                                                {
                                                    level1OrgId = lstResult.FirstOrDefault().level2Id;
                                                }
                                            }
                                            else if (lst.orgLevel.Value == 2)
                                            {
                                                lstResult = lstResult.Where(x => x.level2Id == lst.orgId).ToList();
                                                if (lstResult.Any())
                                                {
                                                    level1OrgId = lstResult.FirstOrDefault().level1Id;
                                                }
                                            }

                                            if (string.IsNullOrEmpty(level1OrgId))
                                            {
                                                ClsOrgStructureFlat of = new ClsOrgStructureFlat();
                                                LoopThroughOrgHeirarchy(orgStructure, of, lst.orgId, lst.orgLevel.Value);
                                                if (lst.orgLevel.Value == 5)
                                                {
                                                    level1OrgId = of.level4Id;
                                                }
                                                else if (lst.orgLevel.Value == 4)
                                                {
                                                    level1OrgId = of.level3Id;
                                                }
                                                else if (lst.orgLevel.Value == 3)
                                                {
                                                    level1OrgId = of.level2Id;
                                                }
                                                else if (lst.orgLevel.Value == 2)
                                                {
                                                    level1OrgId = of.level1Id;
                                                }
                                            }

                                            level2ServiceId = lst.orgId;

                                            message = await UpdateNonAllocatedDosAsync(userID, lst.groupId, level1OrgId, budgetYear, lst.fk_alter_code, lst.changeid, null);
                                        }
                                    }
                                    else if (Convert.ToString(displayControl["fpLevel1Type"]) == "orgId" && Convert.ToString(displayControl["fpLevel2Type"]) == "serviceId")
                                    {
                                        message = await UpdateNonAllocatedDosAsync(userID, lst.groupId, lst.orgId, budgetYear, lst.fk_alter_code, lst.changeid, null);
                                    }
                                    else if (Convert.ToString(displayControl["fpLevel1Type"]) == "orgId" && Convert.ToString(displayControl["fpLevel2Type"]) == "")
                                    {
                                        message = await UpdateNonAllocatedDosAsync(userID, lst.groupId, lst.orgId, budgetYear, lst.fk_alter_code, lst.changeid, null);
                                    }
                                }
                            }
                        }
                        else
                        {
                            message = await UpdateNewPrioritiesAndCostReductionsForMultipleOrgAndServiceIdsCombinationsForDOSAsync(userID, lst.orgId, Convert.ToInt32(lst.groupId),
                                string.IsNullOrEmpty(lst.fk_alter_code) ? string.Empty : lst.fk_alter_code, lst.changeid, lst.BudgetYear, null);
                        }
                    }
                    else
                    {
                        if (lst.orgLevel != null)
                        {
                            var displayControl = await DisplayFinplanLevel2ItemsAsync(userID, lst.orgId, lst.orgLevel.Value);
                            {
                                if (Convert.ToBoolean(displayControl["displayAllBudgetProposalTabs"]) == true)
                                {
                                    if (Convert.ToString(displayControl["fpLevel1Type"]) == "orgId" && Convert.ToString(displayControl["fpLevel2Type"]) == "orgId")
                                    {
                                        if (Convert.ToInt32(displayControl["fpLevel1Value"]) == lst.orgLevel.Value)
                                        {
                                            message = await UpdateNonAllocatedAsync(userID, lst.groupId, lst.orgId, lst.serviceId, budgetYear, lst.fk_alter_code, lst.changeid);
                                        }
                                        else if (Convert.ToInt32(displayControl["fpLevel2Value"]) == lst.orgLevel.Value)
                                        {
                                            JArray orgStructure = await _utility.GetOrgStructureAsync(orgVersionContent, userID);
                                            List<ClsOrgStructureContent> lstOrgStructureContent = orgStructure.ToObject<List<ClsOrgStructureContent>>();
                                            List<ClsOrgStructureFlat> lstResult = _utility.GetFlatOrgStructure(lstOrgStructureContent, new List<ClsOrgStructureFlat>(), null);

                                            string level1OrgId = string.Empty;
                                            string level2ServiceId = string.Empty;
                                            if (lst.orgLevel.Value == 5)
                                            {
                                                lstResult = lstResult.Where(x => x.level5Id == lst.orgId).ToList();
                                                if (lstResult.Any())
                                                {
                                                    level1OrgId = lstResult.FirstOrDefault().level4Id;
                                                }
                                            }
                                            else if (lst.orgLevel.Value == 4)
                                            {
                                                lstResult = lstResult.Where(x => x.level4Id == lst.orgId).ToList();
                                                if (lstResult.Any())
                                                {
                                                    level1OrgId = lstResult.FirstOrDefault().level3Id;
                                                }
                                            }
                                            else if (lst.orgLevel.Value == 3)
                                            {
                                                lstResult = lstResult.Where(x => x.level3Id == lst.orgId).ToList();
                                                if (lstResult.Any())
                                                {
                                                    level1OrgId = lstResult.FirstOrDefault().level2Id;
                                                }
                                            }
                                            else if (lst.orgLevel.Value == 2)
                                            {
                                                lstResult = lstResult.Where(x => x.level2Id == lst.orgId).ToList();
                                                if (lstResult.Any())
                                                {
                                                    level1OrgId = lstResult.FirstOrDefault().level1Id;
                                                }
                                            }
                                            level2ServiceId = lst.orgId;

                                            message = await UpdateNonAllocatedAsync(userID, lst.groupId, level1OrgId, level2ServiceId, budgetYear, lst.fk_alter_code, lst.changeid);
                                        }
                                    }
                                    else if (Convert.ToString(displayControl["fpLevel1Type"]) == "orgId" && Convert.ToString(displayControl["fpLevel2Type"]) == "serviceId")
                                    {
                                        message = await UpdateNonAllocatedAsync(userID, lst.groupId, lst.orgId, lst.serviceId, budgetYear, lst.fk_alter_code, lst.changeid);
                                    }
                                    else if (Convert.ToString(displayControl["fpLevel1Type"]) == "orgId" && Convert.ToString(displayControl["fpLevel2Type"]) == "")
                                    {
                                        message = await UpdateNonAllocatedAsync(userID, lst.groupId, lst.orgId, "", budgetYear, lst.fk_alter_code, lst.changeid);
                                    }
                                }
                            }
                        }
                        else
                        {
                            message = await UpdateNewPrioritiesAndCostReductionsForMultipleOrgAndServiceIdsCombinationsAsync(userID, new List<clsOrgAndServiceDetails>()
                            {
                                new clsOrgAndServiceDetails()
                                {
                                    orgId = lst.orgId,
                                    serviceId = (string.IsNullOrEmpty(lst.serviceId) || lst.serviceId.ToLower() == "ALL".ToLower() || lst.serviceId.ToLower() == "null".ToLower()) ? string.Empty : lst.serviceId,
                                    actionType = Convert.ToInt32( lst.groupId)
                                }
                            }, string.IsNullOrEmpty(lst.fk_alter_code) ? string.Empty : lst.fk_alter_code, lst.changeid, lst.BudgetYear);
                        }
                    }
                }
            }
            return message;
        }

        public OrgIdAndLevel ReturnOrgIdAndOrgLevel(string userID, string pageId,
            ClsOrgVersionSpecificContent orgVersionContent, string orgId, int? orgLevel)
        {
            var result = ReturnOrgIdAndOrgLevelAsync(userID, pageId, orgVersionContent, orgId, orgLevel).GetAwaiter()
                .GetResult();
            return result;
        }

        public async Task<OrgIdAndLevel> ReturnOrgIdAndOrgLevelAsync(string userID, string pageId, ClsOrgVersionSpecificContent orgVersionContent, string orgId, int? orgLevel)
        {
            OrgIdAndLevel orgDetails = new OrgIdAndLevel();
            orgDetails.orgIdToInsertInHeader = string.Empty;
            orgDetails.orgLevelToInsertInHeader = -1;

            if ((string.IsNullOrEmpty(pageId) && !string.IsNullOrEmpty(orgId) && orgLevel == null) ||
                pageId == "FinplanDetail" ||
                pageId == "BudChangeDetail")
            {
                orgDetails.orgIdToInsertInHeader = orgId;
                var fpLevel1 = await _utility.GetParameterValueAsync(userID, "FINPLAN_LEVEL_1");
                if (!string.IsNullOrEmpty(fpLevel1) && fpLevel1.ToLower() == "org_id_1")
                {
                    orgDetails.orgLevelToInsertInHeader = 1;
                }
                else if (!string.IsNullOrEmpty(fpLevel1) && fpLevel1.ToLower() == "org_id_2")
                {
                    orgDetails.orgLevelToInsertInHeader = 2;
                }
                else if (!string.IsNullOrEmpty(fpLevel1) && fpLevel1.ToLower() == "org_id_3")
                {
                    orgDetails.orgLevelToInsertInHeader = 3;
                }
                else if (!string.IsNullOrEmpty(fpLevel1) && fpLevel1.ToLower() == "org_id_4")
                {
                    orgDetails.orgLevelToInsertInHeader = 4;
                }
                else if (!string.IsNullOrEmpty(fpLevel1) && fpLevel1.ToLower() == "org_id_5")
                {
                    orgDetails.orgLevelToInsertInHeader = 5;
                }
            }
            if (!string.IsNullOrEmpty(orgId) && orgLevel != null && orgLevel.HasValue && orgLevel.Value != -1)
            {
                orgDetails.orgIdToInsertInHeader = orgId;
                orgDetails.orgLevelToInsertInHeader = orgLevel.Value;
            }
            else if (pageId == "FinplanOverview" || pageId == "BudChangeOverview" || string.IsNullOrEmpty(orgId))
            {
                orgDetails.orgIdToInsertInHeader = orgVersionContent.lstOrgHierarchy.FirstOrDefault().org_id_1;
                orgDetails.orgLevelToInsertInHeader = 1;
            }

            return orgDetails;
        }

        private async Task UpdateStrategyTable(UserData user, int actionId, Guid strategyActionId, int budgetYear)
        {
            TenantDBContext tenantdbContext = await _utility.GetTenantDBContextAsync();
            var strategyActionHeaderData = await tenantdbContext.tsa_assessment_area_actions.FirstOrDefaultAsync(x => x.fk_tenant_id == user.tenant_id && x.budget_year == budgetYear
                                                && x.pk_action_id == strategyActionId);
            strategyActionHeaderData.fk_action_id = actionId;
            await tenantdbContext.SaveChangesAsync();
        }

        public dynamic GetBudgetReductionChartData(string userId, int BudgetYear, string budgetPhaseId, bool divideByMillions = false)
        {
            return GetBudgetReductionChartDataAsync(userId, BudgetYear, budgetPhaseId, divideByMillions).GetAwaiter().GetResult();
        }

        public async Task<dynamic> GetBudgetReductionChartDataAsync(string userId, int BudgetYear, string budgetPhaseId, bool divideByMillions = false)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            int budgetYear = BudgetYear;
            //var originalTimeout = tenantDbContext.Database.CommandTimeout;
            //increase sql db time out
            tenantDbContext.Database.SetCommandTimeout(700);

            var changeGraph = tenantDbContext.vw_doc_change_graphs.Where(x => x.fk_tenant_id == userDetails.tenant_id);
            if (!string.IsNullOrEmpty(budgetPhaseId) && budgetPhaseId != "-1")
            {
                List<int> changeIds = _utility.GetChangeDataUsingBudgetPhase(budgetPhaseId, userDetails.tenant_id, budgetYear);
                if (changeIds.Any())
                {
                    changeGraph = changeGraph.Where(x => changeIds.Contains(x.fk_change_id));
                }
            }

            var budgetReductionData = await (from dg in changeGraph
                                             where dg.fk_tenant_id == userDetails.tenant_id && dg.budget_year == budgetYear
                                             orderby dg.graph descending
                                             orderby dg.limit_code ascending
                                             group dg by new { dg.graph, dg.limit_code, dg.limit_description, dg.pk_alter_code, dg.alter_description } into grp
                                             select new
                                             {
                                                 grp.Key.graph,
                                                 limitDesc = grp.Key.alter_description,
                                                 limitCode = grp.Key.limit_code,
                                                 graphValue = divideByMillions == true ? grp.Sum(x => x.year_1_amount).Value / 1000000 : grp.Sum(x => x.year_1_amount).Value / 1000,
                                                 alterCode = grp.Key.pk_alter_code,
                                                 alterDesc = grp.Key.alter_description
                                             }).ToListAsync();

            budgetReductionData = (from a in budgetReductionData
                                   select new
                                   {
                                       graph = a.graph,
                                       limitDesc = a.limitDesc,
                                       limitCode = a.limitCode,
                                       graphValue = Math.Abs(a.graphValue),
                                       alterCode = a.alterCode,
                                       alterDesc = a.alterDesc
                                   }).OrderBy(x => x.graph).ThenBy(x => x.limitCode).ThenBy(x => x.alterDesc).ThenByDescending(x => x.graphValue).ToList();

            //tenantDbContext.Database.CommandTimeout = originalTimeout;
            dynamic lst = new JArray();
            dynamic bdgtChartData = new JObject();
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "NumberFormats");
            Dictionary<string, clsLanguageString> langStringFinPlan = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "FinancialPlan");
            string format = ((langStringValues.FirstOrDefault(v => v.Key == "amount")).Value).LangText;
            Dictionary<string, string> colorsForGraph = await _utility.GetColorsAsync(userId, ColorsFor.BudgetReduction);
            int count = 1;

            List<KeyValuePair> limitCodes = (from br in budgetReductionData
                                             select new
                                             {
                                                 key = br.limitCode,
                                                 value = br.alterCode
                                             }).Distinct().Select(x => new KeyValuePair
                                             {
                                                 key = x.key,
                                                 value = x.value
                                             }).ToList();

            string limitCode = string.Empty;
            string colourCode = string.Empty;
            foreach (var item in limitCodes)
            {
                dynamic graphStack = new JObject();
                graphStack.Name = budgetReductionData.FirstOrDefault(x => x.limitCode == item.key && x.alterCode == item.value).limitDesc;
                graphStack.alterCode = budgetReductionData.FirstOrDefault(x => x.limitCode == item.key && x.alterCode == item.value).alterCode;
                graphStack.number_type = format;
                List<decimal?> dataValues = new List<decimal?>();
                if (budgetReductionData.FirstOrDefault(x => x.graph == "Revenue" && x.limitCode == item.key && x.alterCode == item.value) != null)
                {
                    dataValues.Add(Math.Abs(Convert.ToDecimal(budgetReductionData.FirstOrDefault(x => x.graph == "Revenue" && x.limitCode == item.key && x.alterCode == item.value).graphValue)));
                }
                else
                {
                    dataValues.Add(0);
                }
                if (budgetReductionData.FirstOrDefault(x => x.graph == "Cost" && x.limitCode == item.key && x.alterCode == item.value) != null)
                {
                    dataValues.Add(Math.Abs(Convert.ToDecimal(budgetReductionData.FirstOrDefault(x => x.graph == "Cost" && x.limitCode == item.key && x.alterCode == item.value).graphValue)));
                }
                else
                {
                    dataValues.Add(0);
                }
                dynamic dataArray = new JArray();
                foreach (var val in dataValues)
                {
                    dataArray.Add(Convert.ToDecimal(val));
                }
                graphStack.Add("gridData", dataArray);
                graphStack.Add("chartData", dataArray);
                if (item.key != limitCode)
                {
                    if (count >= colorsForGraph.Count)
                    {
                        count = 0;
                    }

                    colourCode = colorsForGraph[count.ToString()];
                    graphStack.color = colorsForGraph[count.ToString()];
                    count++;
                }
                else
                {
                    graphStack.color = colourCode;
                }
                lst.Add(graphStack);
                limitCode = item.key;
            }
            bdgtChartData.Add("jsonData", lst);
            bdgtChartData.chartConfig = JObject.Parse(await _utility.GetApplicationSettingAsync("GetBudgetReduction_chart_config"));
            dynamic labelArray = new JArray() { ((langStringFinPlan.FirstOrDefault(v => v.Key == "BMDOC_budget_reduction_revenue")).Value).LangText,
                                                ((langStringFinPlan.FirstOrDefault(v => v.Key == "BMDOC_budget_reduction_cost")).Value).LangText };
            bdgtChartData.Add("Labels", labelArray);
            bdgtChartData.webTittle = ((langStringFinPlan.FirstOrDefault(v => v.Key == "BMWEB_budget_reduction_revenue")).Value).LangText;
            return bdgtChartData;
        }

        public DrillDownSummary GetBudgetReductionSummary(string userId, int BudgetYear, bool divideByMillions)
        {
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            UserData userDetails = _utility.GetUserDetails(userId);
            int budgetYear = BudgetYear;
            Dictionary<string, clsLanguageString> langStringFinPlan =
                _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "docexport");

            Dictionary<string, clsLanguageString> langStringValuesNumberFormat = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "NumberFormats");
            string formatN0 = ((langStringValuesNumberFormat.FirstOrDefault(v => v.Key == "amount")).Value).LangText;
            string formatN1 = ((langStringValuesNumberFormat.FirstOrDefault(v => v.Key == "dec1")).Value).LangText;
            var displaySortFilterParameter = (from displayparams in tenantDbContext.vw_tco_parameters
                                              where displayparams.fk_tenant_id == userDetails.tenant_id && displayparams.param_name.ToLower() == "BMWEB_GRAPH_HIDE_ORGVIEW".ToLower() && displayparams.active == 1
                                              select displayparams).FirstOrDefault();
            var hideDeptSortandFilter = displaySortFilterParameter == null ? false : displaySortFilterParameter.param_value.ToLower() == "TRUE".ToLower() ? true : false;

            DrillDownSummary result = new DrillDownSummary
            {
                PageTitle = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_page_title").Value.LangText,
                TreeTitle1 = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_tree_title_1").Value.LangText,
                TreeTitle2 = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_tree_title_2").Value.LangText,
                Comment = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_comment").Value.LangText,
                Description1 = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_description_1").Value.LangText,
                Description2 = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_description_2").Value.LangText,
                Description3 = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_description_3").Value.LangText,
                Summary = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_summary").Value.LangText,
                backtoHomepage = langStringFinPlan.FirstOrDefault(x => x.Key == "BM_graph_backtohomepage").Value.LangText
            };

            //Last year's budget
            vw_twh_budget_totals previousYearTotals =
                tenantDbContext.vw_twh_budget_totals.FirstOrDefault(x => x.budget_year == (budgetYear - 1));

            //Get difference
            dynamic budgetReductionData = GetBudgetReductionChartDataAsync(userId, BudgetYear, string.Empty, divideByMillions).GetAwaiter().GetResult();
            decimal costTotal = 0;
            foreach (var dataPoint in budgetReductionData.jsonData)
            {
                costTotal = costTotal + dataPoint.chartData[1].Value;
            }

            double lastYearsBudget = 0;
            if (previousYearTotals?.year_1_amount != null)
            {
                lastYearsBudget = (double)previousYearTotals.year_1_amount;
            }

            double difference = (double)costTotal;
            double thisYearsBudget;

            CultureInfo ci = Thread.CurrentThread.CurrentCulture;
            if (divideByMillions)
            {
                //If divide by millions is true, we need to show value 1 and 2 in billions (the number is already in thousands)
                //differnce is already in millions
                thisYearsBudget = lastYearsBudget + (difference * 1000000);

                lastYearsBudget = lastYearsBudget / 1000000000;
                thisYearsBudget = thisYearsBudget / 1000000000;

                result.Value1 = lastYearsBudget.ToString(divideByMillions ? formatN1 : formatN0, ci) + " " +
                                langStringFinPlan.FirstOrDefault(x => x.Key == "brg_value_1_millions_postfix").Value.LangText;

                result.Value2 = thisYearsBudget.ToString(divideByMillions ? formatN1 : formatN0, ci) + " " +
                                langStringFinPlan.FirstOrDefault(x => x.Key == "brg_value_2_millions_postfix").Value.LangText;

                result.Value3 = difference.ToString(divideByMillions ? formatN1 : formatN0, ci) + " " +
                                langStringFinPlan.FirstOrDefault(x => x.Key == "brg_value_3_millions_postfix").Value.LangText;
            }
            else
            {
                //If divide by millions is false, we need to show value 1 and 2 in millions (the number is already in thousands)
                //differnce is already in thousands
                thisYearsBudget = lastYearsBudget + (difference * 1000);

                lastYearsBudget = lastYearsBudget / 1000000;
                thisYearsBudget = thisYearsBudget / 1000000;

                result.Value1 = lastYearsBudget.ToString(divideByMillions ? formatN1 : formatN0, ci) + " " +
                                langStringFinPlan.FirstOrDefault(x => x.Key == "brg_value_1_thousands_postfix").Value.LangText;
                result.Value2 = thisYearsBudget.ToString(divideByMillions ? formatN1 : formatN0, ci) + " " +
                                langStringFinPlan.FirstOrDefault(x => x.Key == "brg_value_2_thousands_postfix").Value.LangText;

                result.Value3 = difference.ToString(divideByMillions ? formatN1 : formatN0, ci) + " " +
                                langStringFinPlan.FirstOrDefault(x => x.Key == "brg_value_3_thousands_postfix").Value.LangText;
            }
            result.HideDeptSortandFilter = hideDeptSortandFilter;
            return result;
        }

        public dynamic GetBudgetReductionChartDataActionLevel(string userId, int actionId, string serviceId, string alterCode,
            string graph, int BudgetYear, bool divideByMillions = false)
        {
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            UserData userDetails = _utility.GetUserDetails(userId);
            Dictionary<string, clsLanguageString> langStringFinPlan = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "docexport");

            Dictionary<string, clsLanguageString> langStringValuesNumberFormat = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "NumberFormats");
            string formatN0 = ((langStringValuesNumberFormat.FirstOrDefault(v => v.Key == "amount")).Value).LangText;
            string formatN1 = ((langStringValuesNumberFormat.FirstOrDefault(v => v.Key == "dec1")).Value).LangText;

            string postfix;
            if (divideByMillions)
            {
                postfix = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_total_millions_post_fix").Value.LangText;
            }
            else
            {
                postfix = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_value_thousands_post_fix").Value.LangText;
            }
            CultureInfo ci = Thread.CurrentThread.CurrentCulture;

            int budgetYear = BudgetYear;
            dynamic bdgtActionData = new JObject();

            dynamic label = new JArray();
            label.Add((budgetYear));
            label.Add((budgetYear + 1));
            label.Add((budgetYear + 2));
            label.Add((budgetYear + 3));
            //var originalTimeout = tenantDbContext.Database.CommandTimeout;
            //increase sql db time out
            tenantDbContext.Database.SetCommandTimeout(700);
            var budgetReductionData = (from dg in tenantDbContext.vw_doc_change_graphs
                                       where dg.fk_tenant_id == userDetails.tenant_id &&
                                            dg.budget_year == budgetYear && dg.pk_action_id == actionId &&
                                            dg.service_id == serviceId && dg.pk_alter_code == alterCode &&
                                            dg.graph == graph
                                       group dg by new { dg.long_description, dg.action_description } into grp
                                       select new
                                       {
                                           actionDesc = grp.Key.action_description,
                                           longDesc = grp.Key.long_description,
                                           year1Value = divideByMillions ? grp.Sum(x => x.year_1_amount).Value / 1000000 : grp.Sum(x => x.year_1_amount).Value / 1000,
                                           year2Value = divideByMillions ? grp.Sum(x => x.year_2_amount).Value / 1000000 : grp.Sum(x => x.year_2_amount).Value / 1000,
                                           year3Value = divideByMillions ? grp.Sum(x => x.year_3_amount).Value / 1000000 : grp.Sum(x => x.year_3_amount).Value / 1000,
                                           year4Value = divideByMillions ? grp.Sum(x => x.year_4_amount).Value / 1000000 : grp.Sum(x => x.year_4_amount).Value / 1000
                                       }).FirstOrDefault();
            JArray formattedArray = new JArray();
            if (budgetReductionData != null)
            {
                formattedArray.Add($"{budgetReductionData.year1Value.ToString(divideByMillions ? formatN1 : formatN0, ci)} {postfix}");
                formattedArray.Add($"{budgetReductionData.year2Value.ToString(divideByMillions ? formatN1 : formatN0, ci)} {postfix}");
                formattedArray.Add($"{budgetReductionData.year3Value.ToString(divideByMillions ? formatN1 : formatN0, ci)} {postfix}");
                formattedArray.Add($"{budgetReductionData.year4Value.ToString(divideByMillions ? formatN1 : formatN0, ci)} {postfix}");

                bdgtActionData.Add("ActionDesc", budgetReductionData.actionDesc);
                bdgtActionData.Add("FinPlanDesc", budgetReductionData.longDesc);
            }
            else
            {
                formattedArray.Add($"{0.ToString(divideByMillions ? formatN1 : formatN0, ci)} {postfix}");
                formattedArray.Add($"{0.ToString(divideByMillions ? formatN1 : formatN0, ci)} {postfix}");
                formattedArray.Add($"{0.ToString(divideByMillions ? formatN1 : formatN0, ci)} {postfix}");
                formattedArray.Add($"{0.ToString(divideByMillions ? formatN1 : formatN0, ci)} {postfix}");

                bdgtActionData.Add("ActionDesc", string.Empty);
                bdgtActionData.Add("FinPlanDesc", string.Empty);
            }

            //tenantDbContext.Database.CommandTimeout = originalTimeout;
            bdgtActionData.Add("Data", JArray.FromObject(formattedArray));
            bdgtActionData.Add("Labels", label);
            bdgtActionData.WebUrlLink = ((langStringFinPlan.FirstOrDefault(v => v.Key == "BMWEB_redirect_servicelevel")).Value).LangText;
            bdgtActionData.Add("showPopupDescription", true);
            return bdgtActionData;
        }

        public dynamic GetBudgetReductionChartDataForAlterCode(string userId, string alterCode, string graph,
            string color, int budgetYear, bool divideByMillions = false)
        {
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            UserData userDetails = _utility.GetUserDetails(userId);
            List<PublishGraphColorHelper> _graphColors = _utility.GetGraphColors(userId, ColorsFor.PublishGraphColors).ToList();
            Dictionary<string, clsLanguageString> langStringFinPlan =
                _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "docexport");
            CultureInfo ci = Thread.CurrentThread.CurrentCulture;

            Dictionary<string, clsLanguageString> langStringValuesNumberFormat = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "NumberFormats");
            string formatN0 = ((langStringValuesNumberFormat.FirstOrDefault(v => v.Key == "amount")).Value).LangText;
            string formatN1 = ((langStringValuesNumberFormat.FirstOrDefault(v => v.Key == "dec1")).Value).LangText;

            //var originalTimeout = tenantDbContext.Database.CommandTimeout;
            //increase sql db time out
            tenantDbContext.Database.SetCommandTimeout(700);
            var serviceIdData = (from dg in tenantDbContext.vw_doc_change_graphs
                                 where dg.fk_tenant_id == userDetails.tenant_id && dg.budget_year == budgetYear
                                     && dg.pk_alter_code == alterCode && dg.graph == graph
                                 orderby dg.limit_code ascending
                                 group dg by new { dg.service_id, dg.service_name, dg.pk_alter_code, dg.show_flag } into grp
                                 select new
                                 {
                                     serviceDesc = grp.Key.service_name,
                                     serviceId = grp.Key.service_id,
                                     graphValue = divideByMillions == true ? grp.Sum(x => x.year_1_amount).Value / 1000000 : grp.Sum(x => x.year_1_amount).Value / 1000,
                                     showActionList = grp.Key.show_flag
                                 }).ToList();

            serviceIdData = (from p in serviceIdData
                             select new
                             {
                                 serviceDesc = p.serviceDesc,
                                 serviceId = p.serviceId,
                                 graphValue = Math.Abs(p.graphValue),
                                 showActionList = p.showActionList,
                             }).OrderByDescending(x => x.graphValue).ToList();

            //tenantDbContext.Database.CommandTimeout = originalTimeout;

            JArray graphData = new JArray();
            JArray dataItems = new JArray();
            decimal total = 0;
            string postfix = string.Empty;
            if (divideByMillions)
            {
                postfix = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_value_millions_post_fix").Value.LangText;
            }
            else
            {
                postfix = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_value_thousands_post_fix").Value.LangText;
            }
            int count = 0;
            foreach (var serviceId in serviceIdData)
            {
                if (Math.Abs(serviceId.graphValue) > 0)
                {
                    if (count >= _graphColors.Count)
                    {
                        count = 0;
                    }

                    JObject dataItem = new JObject();

                    var title = _graphColors.FirstOrDefault(x => x.Index == count).IsTextWhite ? $"<span class=\"bold white\">{(serviceId.serviceDesc ?? "")}</span><br><br>"
                                                                                               : $"<span class=\"bold\">{(serviceId.serviceDesc ?? "")}</span><br><br>";

                    var value = _graphColors.FirstOrDefault(x => x.Index == count).IsTextWhite ? $"<span class=\"font30\" style=\"color:white\">{Math.Abs(serviceId.graphValue).ToString(divideByMillions ? formatN1 : formatN0, ci)} {postfix}</span>"
                                                                                               : $"<span class=\"font30\">{Math.Abs(serviceId.graphValue).ToString(divideByMillions ? formatN1 : formatN0, ci)} {postfix}</span>";

                    string cellTitle = title + value;

                    dataItem.Add("cellTitle", cellTitle);

                    dataItem.Add("name", serviceId.serviceDesc ?? "");

                    dataItem.Add("value", Math.Round(Math.Abs(serviceId.graphValue), 1));

                    dataItem.Add("tooltipvalue", $"{Math.Abs(serviceId.graphValue).ToString(divideByMillions ? formatN1 : formatN0, ci)} {postfix}");

                    dataItem.Add("color", _graphColors.FirstOrDefault(x => x.Index == count).ColorCode);

                    dataItem.Add("serviceId", serviceId.serviceId);

                    dataItem.Add("showActionList", serviceId.showActionList);
                    dataItems.Add(dataItem);
                    total = total + serviceId.graphValue;
                    count++;
                }
            }

            JObject itemsObject = new JObject { { "items", dataItems } };

            graphData.Add(itemsObject);

            string totalPostfix = string.Empty;
            if (divideByMillions)
            {
                totalPostfix = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_total_millions_post_fix").Value.LangText;
            }
            else
            {
                totalPostfix = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_total_thousands_post_fix").Value.LangText;
            }

            JObject publishData = new JObject
            {
                { "graphData", graphData },
                { "total", $"{total.ToString(divideByMillions ? formatN1 : formatN0, ci)} {totalPostfix}" }
            };

            return publishData;
        }

        public dynamic GetBudgetReductionChartDataForServiceId(string userId, string alter_code, string graph,
            string serviceId, int BudgetYear, bool divideByMillions = false)
        {
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            UserData userDetails = _utility.GetUserDetails(userId);
            Dictionary<string, clsLanguageString> langStringFinPlan =
                _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "docexport");

            Dictionary<string, clsLanguageString> langStringValuesNumberFormat = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "NumberFormats");
            string formatN0 = ((langStringValuesNumberFormat.FirstOrDefault(v => v.Key == "amount")).Value).LangText;
            string formatN1 = ((langStringValuesNumberFormat.FirstOrDefault(v => v.Key == "dec1")).Value).LangText;

            string totalPostfix = string.Empty;
            if (divideByMillions)
            {
                totalPostfix = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_total_millions_post_fix").Value.LangText;
            }
            else
            {
                totalPostfix = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_value_thousands_post_fix").Value.LangText;
            }
            CultureInfo ci = Thread.CurrentThread.CurrentCulture;

            int budgetYear = BudgetYear;
            //var originalTimeout = tenantDbContext.Database.CommandTimeout;
            //increase sql db time out
            tenantDbContext.Database.SetCommandTimeout(700);
            decimal year1Total = 0;
            decimal year2Total = 0;
            decimal year3Total = 0;
            decimal year4Total = 0;

            var actionData = (from dg in tenantDbContext.vw_doc_change_graphs
                              where dg.fk_tenant_id == userDetails.tenant_id && dg.budget_year == budgetYear
                                  && dg.pk_alter_code == alter_code && dg.graph == graph
                                  && dg.service_id == serviceId
                              orderby dg.limit_code ascending
                              group dg by new
                              {
                                  dg.pk_action_id,
                                  dg.service_id,
                                  dg.service_name,
                                  dg.pk_alter_code,
                                  dg.action_description
                              } into grp
                              select new
                              {
                                  actionId = grp.Key.pk_action_id,
                                  actionTitle = grp.Key.action_description,
                                  serviceId = grp.Key.service_id,
                                  year1Amount = divideByMillions ? grp.Sum(x => x.year_1_amount).Value / 1000000 :
                                      grp.Sum(x => x.year_1_amount).Value / 1000,
                                  year2Amount = divideByMillions ? grp.Sum(x => x.year_2_amount).Value / 1000000 :
                                      grp.Sum(x => x.year_2_amount).Value / 1000,
                                  year3Amount = divideByMillions ? grp.Sum(x => x.year_3_amount).Value / 1000000 :
                                      grp.Sum(x => x.year_3_amount).Value / 1000,
                                  year4Amount = divideByMillions ? grp.Sum(x => x.year_4_amount).Value / 1000000 :
                                      grp.Sum(x => x.year_4_amount).Value / 1000,
                              }).ToList();

            actionData = actionData.Where(x => ((x.year1Amount + x.year2Amount + x.year3Amount + x.year4Amount) != 0)).ToList();

            //tenantDbContext.Database.CommandTimeout = originalTimeout;

            JArray dataItems = new JArray();
            JObject dataItem;
            string actionTitle;

            foreach (var action in actionData)
            {
                dataItem = new JObject();

                actionTitle = $"<a onClick = 'openProposalPopup({action.actionId}, \"{action.serviceId}\")' onkeypress = 'openProposalPopup({action.actionId}, \"{action.serviceId}\")' href='javascript:void(0)' title=\"(Sprettoppvindu) {action.actionTitle}\">{action.actionTitle} <span class='sr-only'>Lenke åpnes i sprettoppvindu</span></a>";
                dataItem.Add("action", actionTitle);
                dataItem.Add("actionId", action.actionId);
                dataItem.Add("year1", $"{action.year1Amount.ToString(divideByMillions ? formatN1 : formatN0, ci)} {totalPostfix}");
                dataItem.Add("year2", $"{action.year2Amount.ToString(divideByMillions ? formatN1 : formatN0, ci)} {totalPostfix}");
                dataItem.Add("year3", $"{action.year3Amount.ToString(divideByMillions ? formatN1 : formatN0, ci)} {totalPostfix}");
                dataItem.Add("year4", $"{action.year4Amount.ToString(divideByMillions ? formatN1 : formatN0, ci)} {totalPostfix}");
                year1Total = year1Total + action.year1Amount;
                year2Total = year2Total + action.year2Amount;
                year3Total = year3Total + action.year3Amount;
                year4Total = year4Total + action.year4Amount;

                dataItems.Add(dataItem);
            }

            //Add Total Row
            dataItem = new JObject();
            actionTitle = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_sum_title").Value.LangText;
            dataItem.Add("action", $"{actionTitle}");
            dataItem.Add("actionId", " ");
            dataItem.Add("year1", $"{year1Total.ToString(divideByMillions ? formatN1 : formatN0, ci)} {totalPostfix}");
            dataItem.Add("year2", $"{year2Total.ToString(divideByMillions ? formatN1 : formatN0, ci)} {totalPostfix}");
            dataItem.Add("year3", $"{year3Total.ToString(divideByMillions ? formatN1 : formatN0, ci)} {totalPostfix}");
            dataItem.Add("year4", $"{year4Total.ToString(divideByMillions ? formatN1 : formatN0, ci)} {totalPostfix}");
            dataItems.Add(dataItem);

            JArray columns = new JArray();
            JObject column = new JObject();
            column.Add("title", " ");
            column.Add("field", "action");
            column.Add("width", 350);
            column.Add("encoded", false);
            columns.Add(column);

            for (int i = 0; i < 4; i++)
            {
                column = new JObject
                {
                    {"title", $"{budgetYear + i}"},
                    {"headerTemplate", $"<span class=\"bold\">{budgetYear + i}</span>"},
                    {"field", $"year{i + 1}"},
                    {"attributes", new JObject {{"style", "text-align:right;border-left:0px;"}}},
                    {"headerAttributes", new JObject {{"style", "text-align:right;"}}},
                    {"format", divideByMillions ? "{0:n1}" : "{0:n0}"},
                    {"width", 150},
                    {"encoded", false}
                };
                columns.Add(column);
            }

            totalPostfix = string.Empty;
            if (divideByMillions)
            {
                totalPostfix = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_total_millions_post_fix").Value.LangText;
            }
            else
            {
                totalPostfix = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_total_thousands_post_fix").Value.LangText;
            }

            JObject gridData = new JObject
            {
                { "data", dataItems },
                { "columns", columns },
                { "total", $"{year1Total.ToString(divideByMillions ? formatN1 : formatN0, ci)} {totalPostfix}" }
            };

            return gridData;
        }

        public dynamic GetKeyFiguresData(string userId, int BudgetYear)
        {
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            UserData userDetails = _utility.GetUserDetails(userId);
            int budgetYear = BudgetYear;
            Dictionary<string, clsLanguageString> langStringFinPlan =
                _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "FinancialPlan");
            var yearToShowInWeb = _utility.GetParameterValue(userId, "BP_ACT_Web_Year");
            CultureInfo ci = Thread.CurrentThread.CurrentCulture;

            List<InfoBlobSourceAndDestination> lstImages = new List<InfoBlobSourceAndDestination>();

            var tcoKeyFigs = tenantDbContext.tco_key_figures.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear && x.active == true).OrderBy(y => y.sort_order).ToList();

            dynamic keyFigReturn = new JObject();
            keyFigReturn.pageTitle = langStringFinPlan.FirstOrDefault(x => x.Key == "WebPub_KeyFigures_title").Value.LangText;
            keyFigReturn.keyFigures = "BM";
            JArray keyFigDataArr = new JArray();
            foreach (var data in tcoKeyFigs)
            {
                dynamic obj = new JObject();
                obj.key_figure_description = data.key_name;

                if (!string.IsNullOrEmpty(data.postfix) && data.value_type.Contains(data.postfix))
                {
                    data.postfix = string.Empty;
                }

                obj.value = (data.year_1_value / data.divide_by).ToString(data.value_type) + " " + data.postfix;
                obj.imageUrl = string.IsNullOrEmpty(data.image_icon) ? "" : "/content/appimages/" + data.image_icon;

                if (!string.IsNullOrEmpty(data.image_icon))
                {
                    if (lstImages.FirstOrDefault(x => x.source.ToLower().Contains(data.image_icon.ToLower())) == null)
                    {
                        lstImages.Add(new InfoBlobSourceAndDestination()
                        {
                            source = "global/images/keyfigures/" + data.image_icon,
                            destination = "/content/appimages/" + data.image_icon
                        });
                    }
                }

                keyFigDataArr.Add(obj);
            }

            var datasetIndicator = (from a in tenantDbContext.tco_activity_indicator
                                    join b in tenantDbContext.tco_indicator_setup on new { a = a.fk_tenant_id, b = a.fk_indicator_code }
                                                                              equals new { a = b.fk_tenant_id, b = b.pk_indicator_code }
                                    where a.fk_tenant_id == userDetails.tenant_id
                                    && a.budget_year == budgetYear
                                    && a.show_in_key_figure == true
                                    select new
                                    {
                                        key_name = b.measurment_criteria,
                                        value_type = (!string.IsNullOrEmpty(b.value_type) && b.value_type.ToLower() == "text") ? "" : b.value_type,
                                        image_icon = string.IsNullOrEmpty(a.image_icon) ? "" : a.image_icon,
                                        year_1_value = a.year_1_value,
                                        postfix = string.Empty,
                                        divide_by = 1,
                                        result = a.result,
                                        year1value = a.year_1_value,
                                        year2value = a.year_2_value,
                                        year3value = a.year_3_value,
                                        year4value = a.year_4_value,
                                        kostraYear1 = a.kostra_1_value,
                                        kostraYear2 = a.kostra_2_value,
                                        kostraYear3 = a.kostra_3_value,
                                        kostraYear4 = a.kostra_4_value,
                                        column_to_show_in_front_page = (!string.IsNullOrEmpty(yearToShowInWeb)) ? yearToShowInWeb : "year1value"
                                    }).ToList();

            foreach (var data in datasetIndicator)
            {
                dynamic obj = new JObject();
                obj.key_figure_description = data.key_name;

                switch (data.column_to_show_in_front_page)
                {
                    case "year1value": obj.value = (data.year1value / data.divide_by).ToString(data.value_type) + " " + data.postfix; break;
                    case "year2value": obj.value = (data.year2value / data.divide_by).ToString(data.value_type) + " " + data.postfix; break;
                    case "year3value": obj.value = (data.year3value / data.divide_by).ToString(data.value_type) + " " + data.postfix; break;
                    case "year4value": obj.value = (data.year4value / data.divide_by).ToString(data.value_type) + " " + data.postfix; break;
                    case "kostraYear1": obj.value = (data.kostraYear1 / data.divide_by).ToString(data.value_type) + " " + data.postfix; break;
                    case "kostraYear2": obj.value = (data.kostraYear2 / data.divide_by).ToString(data.value_type) + " " + data.postfix; break;
                    case "kostraYear3": obj.value = (data.kostraYear3 / data.divide_by).ToString(data.value_type) + " " + data.postfix; break;
                    case "kostraYear4": obj.value = (data.kostraYear4 / data.divide_by).ToString(data.value_type) + " " + data.postfix; break;
                    case "result": obj.value = (data.result / data.divide_by).ToString(data.value_type) + " " + data.postfix; break;

                    default: obj.value = (data.year_1_value / data.divide_by).ToString(data.value_type) + " " + data.postfix; break;
                }

                obj.imageUrl = string.IsNullOrEmpty(data.image_icon) ? "" : "/content/appimages/" + data.image_icon;

                if (!string.IsNullOrEmpty(data.image_icon))
                {
                    if (lstImages.FirstOrDefault(x => x.source.ToLower().Contains(data.image_icon.ToLower())) == null)
                    {
                        lstImages.Add(new InfoBlobSourceAndDestination()
                        {
                            source = "global/images/keyfigures/" + data.image_icon,
                            destination = "/content/appimages/" + data.image_icon
                        });
                    }
                }

                keyFigDataArr.Add(obj);
            }

            keyFigReturn.data = keyFigDataArr;
            keyFigReturn.lstImages = JArray.FromObject(lstImages);

            return keyFigReturn;
        }

        public dynamic GetBudgetReductionChartDataServiceSetup(string userId, int BudgetYear, List<BudgetReductionGraphHelper> resultSet, bool divideByMillions = false, string orgID = null)
        {
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            UserData userDetails = _utility.GetUserDetails(userId);
            int budgetYear = BudgetYear;
            //var originalTimeout = tenantDbContext.Database.CommandTimeout;
            //increase sql db time out
            tenantDbContext.Database.SetCommandTimeout(700);
            var budgetReductionData = orgID == "0" ? (from dg in resultSet
                                                      orderby dg.graph descending
                                                      orderby dg.limit_code ascending
                                                      group dg by new { dg.graph, dg.limit_code, dg.limit_description, dg.pk_alter_code, dg.alter_description } into grp
                                                      select new
                                                      {
                                                          grp.Key.graph,
                                                          limitDesc = grp.Key.alter_description,
                                                          limitCode = grp.Key.limit_code,
                                                          graphValue = divideByMillions == true ? grp.Sum(x => x.year_1_amount).Value / 1000000 : grp.Sum(x => x.year_1_amount).Value / 1000,
                                                          alterCode = grp.Key.pk_alter_code,
                                                          alterDesc = grp.Key.alter_description
                                                      }).ToList() :
                                        (from dg in resultSet
                                         where dg.org_id == orgID
                                         orderby dg.graph descending
                                         orderby dg.limit_code ascending
                                         group dg by new { dg.graph, dg.limit_code, dg.limit_description, dg.pk_alter_code, dg.alter_description } into grp
                                         select new
                                         {
                                             grp.Key.graph,
                                             limitDesc = grp.Key.alter_description,
                                             limitCode = grp.Key.limit_code,
                                             graphValue = divideByMillions == true ? grp.Sum(x => x.year_1_amount).Value / 1000000 : grp.Sum(x => x.year_1_amount).Value / 1000,
                                             alterCode = grp.Key.pk_alter_code,
                                             alterDesc = grp.Key.alter_description
                                         }).ToList();
            //tenantDbContext.Database.CommandTimeout = originalTimeout;
            dynamic lst = new JArray();
            dynamic bdgtChartData = new JObject();
            Dictionary<string, clsLanguageString> langStringValues = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "NumberFormats");
            Dictionary<string, clsLanguageString> langStringFinPlan = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "FinancialPlan");
            string format = ((langStringValues.FirstOrDefault(v => v.Key == "amount")).Value).LangText;
            Dictionary<string, string> colorsForGraph = _utility.GetColors(userId, ColorsFor.BudgetReduction);
            int count = 1;

            List<KeyValuePair> limitCodes = (from br in budgetReductionData
                                             select new
                                             {
                                                 key = br.limitCode,
                                                 value = br.alterCode
                                             }).Distinct().Select(x => new KeyValuePair
                                             {
                                                 key = x.key,
                                                 value = x.value
                                             }).ToList();

            string limitCode = string.Empty;
            string colourCode = string.Empty;
            foreach (var item in limitCodes)
            {
                dynamic graphStack = new JObject();
                graphStack.Name = budgetReductionData.FirstOrDefault(x => x.limitCode == item.key && x.alterCode == item.value).limitDesc;
                graphStack.alterCode = budgetReductionData.FirstOrDefault(x => x.limitCode == item.key && x.alterCode == item.value).alterCode;
                graphStack.number_type = format;
                List<decimal?> dataValues = new List<decimal?>();
                if (budgetReductionData.FirstOrDefault(x => x.graph == "Revenue" && x.limitCode == item.key && x.alterCode == item.value) != null)
                {
                    dataValues.Add(Math.Abs(Convert.ToDecimal(budgetReductionData.FirstOrDefault(x => x.graph == "Revenue" && x.limitCode == item.key && x.alterCode == item.value).graphValue)));
                }
                else
                {
                    dataValues.Add(0);
                }
                if (budgetReductionData.FirstOrDefault(x => x.graph == "Cost" && x.limitCode == item.key && x.alterCode == item.value) != null)
                {
                    dataValues.Add(Math.Abs(Convert.ToDecimal(budgetReductionData.FirstOrDefault(x => x.graph == "Cost" && x.limitCode == item.key && x.alterCode == item.value).graphValue)));
                }
                else
                {
                    dataValues.Add(0);
                }
                dynamic dataArray = new JArray();
                foreach (var val in dataValues)
                {
                    dataArray.Add(Convert.ToDecimal(val));
                }
                graphStack.Add("gridData", dataArray);
                graphStack.Add("chartData", dataArray);
                if (item.key != limitCode)
                {
                    if (count >= colorsForGraph.Count)
                    {
                        count = 0;
                    }

                    colourCode = colorsForGraph[count.ToString()];
                    graphStack.color = colorsForGraph[count.ToString()];
                    count++;
                }
                else
                {
                    graphStack.color = colourCode;
                }
                lst.Add(graphStack);
                limitCode = item.key;
            }
            bdgtChartData.Add("jsonData", lst);
            bdgtChartData.chartConfig = JObject.Parse(_utility.GetApplicationSetting("GetBudgetReduction_chart_config"));
            dynamic labelArray = new JArray() { ((langStringFinPlan.FirstOrDefault(v => v.Key == "BMDOC_budget_reduction_revenue")).Value).LangText,
                                                ((langStringFinPlan.FirstOrDefault(v => v.Key == "BMDOC_budget_reduction_cost")).Value).LangText };
            bdgtChartData.Add("Labels", labelArray);
            bdgtChartData.webTittle = ((langStringFinPlan.FirstOrDefault(v => v.Key == "BMWEB_budget_reduction_revenue")).Value).LangText;
            return bdgtChartData;
        }

        public dynamic GetBudgetReductionChartDataForAlterCodeServiceSetUp(string userId, string alterCode, string orgId, string graph,
            string color, int budgetYear, List<BudgetReductionGraphHelper> resultSet, bool divideByMillions = false)
        {
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            UserData userDetails = _utility.GetUserDetails(userId);
            List<PublishGraphColorHelper> _graphColors = _utility.GetGraphColors(userId, ColorsFor.PublishGraphColors).ToList();
            Dictionary<string, clsLanguageString> langStringFinPlan =
                _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "docexport");
            CultureInfo ci = Thread.CurrentThread.CurrentCulture;

            Dictionary<string, clsLanguageString> langStringValuesNumberFormat = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "NumberFormats");
            string formatN0 = ((langStringValuesNumberFormat.FirstOrDefault(v => v.Key == "amount")).Value).LangText;
            string formatN1 = ((langStringValuesNumberFormat.FirstOrDefault(v => v.Key == "dec1")).Value).LangText;

            //var originalTimeout = tenantDbContext.Database.CommandTimeout;
            //increase sql db time out
            tenantDbContext.Database.SetCommandTimeout(700);
            var serviceIdData = orgId == "0" ? (from dg in resultSet
                                                where dg.pk_alter_code == alterCode && dg.graph == graph
                                                orderby dg.limit_code ascending
                                                group dg by new { dg.service_id, dg.service_name, dg.pk_alter_code, dg.show_flag } into grp
                                                select new
                                                {
                                                    serviceDesc = grp.Key.service_name,
                                                    serviceId = grp.Key.service_id,
                                                    graphValue = divideByMillions == true ? grp.Sum(x => x.year_1_amount).Value / 1000000 : grp.Sum(x => x.year_1_amount).Value / 1000,
                                                    showActionList = grp.Key.show_flag
                                                }).ToList() :
                                                (from dg in resultSet
                                                 where dg.pk_alter_code == alterCode && dg.graph == graph && dg.org_id == orgId
                                                 orderby dg.limit_code ascending
                                                 group dg by new { dg.service_id, dg.service_name, dg.pk_alter_code, dg.show_flag } into grp
                                                 select new
                                                 {
                                                     serviceDesc = grp.Key.service_name,
                                                     serviceId = grp.Key.service_id,
                                                     graphValue = divideByMillions == true ? grp.Sum(x => x.year_1_amount).Value / 1000000 : grp.Sum(x => x.year_1_amount).Value / 1000,
                                                     showActionList = grp.Key.show_flag
                                                 }).ToList();

            serviceIdData = (from p in serviceIdData
                             select new
                             {
                                 serviceDesc = p.serviceDesc,
                                 serviceId = p.serviceId,
                                 graphValue = Math.Abs(p.graphValue),
                                 showActionList = p.showActionList,
                             }).OrderByDescending(x => x.graphValue).ToList();

            //tenantDbContext.Database.CommandTimeout = originalTimeout;

            JArray graphData = new JArray();
            JArray dataItems = new JArray();
            decimal total = 0;
            string postfix = string.Empty;
            if (divideByMillions)
            {
                postfix = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_value_millions_post_fix").Value.LangText;
            }
            else
            {
                postfix = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_value_thousands_post_fix").Value.LangText;
            }
            int count = 0;
            foreach (var serviceId in serviceIdData)
            {
                if (Math.Abs(serviceId.graphValue) > 0)
                {
                    if (count >= _graphColors.Count)
                    {
                        count = 0;
                    }

                    JObject dataItem = new JObject();

                    var title = _graphColors.FirstOrDefault(x => x.Index == count).IsTextWhite ? $"<span class=\"bold white\">{(serviceId.serviceDesc ?? "")}</span><br><br>"
                                                                                               : $"<span class=\"bold\">{(serviceId.serviceDesc ?? "")}</span><br><br>";

                    var value = _graphColors.FirstOrDefault(x => x.Index == count).IsTextWhite ? $"<span class=\"font30\" style=\"color:white\">{Math.Abs(serviceId.graphValue).ToString(divideByMillions ? formatN1 : formatN0, ci)} {postfix}</span>"
                                                                                               : $"<span class=\"font30\">{Math.Abs(serviceId.graphValue).ToString(divideByMillions ? formatN1 : formatN0, ci)} {postfix}</span>";

                    string cellTitle = title + value;

                    dataItem.Add("cellTitle", cellTitle);

                    dataItem.Add("name", serviceId.serviceDesc ?? "");

                    dataItem.Add("value", Math.Round(Math.Abs(serviceId.graphValue), 1));

                    dataItem.Add("tooltipvalue", $"{Math.Abs(serviceId.graphValue).ToString(divideByMillions ? formatN1 : formatN0, ci)} {postfix}");

                    dataItem.Add("color", _graphColors.FirstOrDefault(x => x.Index == count).ColorCode);

                    dataItem.Add("serviceId", serviceId.serviceId);

                    dataItem.Add("showActionList", serviceId.showActionList);
                    dataItems.Add(dataItem);
                    total = total + serviceId.graphValue;
                    count++;
                }
            }

            JObject itemsObject = new JObject { { "items", dataItems } };

            graphData.Add(itemsObject);

            string totalPostfix = string.Empty;
            if (divideByMillions)
            {
                totalPostfix = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_total_millions_post_fix").Value.LangText;
            }
            else
            {
                totalPostfix = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_total_thousands_post_fix").Value.LangText;
            }

            JObject publishData = new JObject
            {
                { "graphData", graphData },
                { "total", $"{total.ToString(divideByMillions ? formatN1 : formatN0, ci)} {totalPostfix}" }
            };

            return publishData;
        }

        public dynamic GetBudgetReductionChartDataForServiceIdServiceSetUp(string userId, string alter_code, string graph,
            string serviceId, int BudgetYear, bool divideByMillions = false, string orgID = null, List<BudgetReductionGraphHelper> resultSet = null)
        {
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            UserData userDetails = _utility.GetUserDetails(userId);
            Dictionary<string, clsLanguageString> langStringFinPlan =
                _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "docexport");

            Dictionary<string, clsLanguageString> langStringValuesNumberFormat = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "NumberFormats");
            string formatN0 = ((langStringValuesNumberFormat.FirstOrDefault(v => v.Key == "amount")).Value).LangText;
            string formatN1 = ((langStringValuesNumberFormat.FirstOrDefault(v => v.Key == "dec1")).Value).LangText;

            string totalPostfix = string.Empty;
            if (divideByMillions)
            {
                totalPostfix = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_total_millions_post_fix").Value.LangText;
            }
            else
            {
                totalPostfix = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_value_thousands_post_fix").Value.LangText;
            }
            CultureInfo ci = Thread.CurrentThread.CurrentCulture;

            int budgetYear = BudgetYear;
            //var originalTimeout = tenantDbContext.Database.CommandTimeout;
            //increase sql db time out
            tenantDbContext.Database.SetCommandTimeout(700);
            decimal year1Total = 0;
            decimal year2Total = 0;
            decimal year3Total = 0;
            decimal year4Total = 0;

            var actionData = orgID == "0" ?
                            (from dg in resultSet
                             where dg.pk_alter_code == alter_code && dg.graph == graph && dg.service_id == serviceId
                             orderby dg.limit_code ascending
                             group dg by new
                             {
                                 dg.pk_action_id,
                                 dg.service_id,
                                 dg.service_name,
                                 dg.pk_alter_code,
                                 dg.action_description
                             } into grp
                             select new
                             {
                                 actionId = grp.Key.pk_action_id,
                                 actionTitle = grp.Key.action_description,
                                 serviceId = grp.Key.service_id,
                                 year1Amount = divideByMillions ? grp.Sum(x => x.year_1_amount).Value / 1000000 :
                                     grp.Sum(x => x.year_1_amount).Value / 1000,
                                 year2Amount = divideByMillions ? grp.Sum(x => x.year_2_amount).Value / 1000000 :
                                     grp.Sum(x => x.year_2_amount).Value / 1000,
                                 year3Amount = divideByMillions ? grp.Sum(x => x.year_3_amount).Value / 1000000 :
                                     grp.Sum(x => x.year_3_amount).Value / 1000,
                                 year4Amount = divideByMillions ? grp.Sum(x => x.year_4_amount).Value / 1000000 :
                                     grp.Sum(x => x.year_4_amount).Value / 1000,
                             }).ToList() :
                             (from dg in resultSet
                              where dg.pk_alter_code == alter_code && dg.graph == graph && dg.service_id == serviceId && dg.org_id == orgID
                              orderby dg.limit_code ascending
                              group dg by new
                              {
                                  dg.pk_action_id,
                                  dg.service_id,
                                  dg.service_name,
                                  dg.pk_alter_code,
                                  dg.action_description
                              } into grp
                              select new
                              {
                                  actionId = grp.Key.pk_action_id,
                                  actionTitle = grp.Key.action_description,
                                  serviceId = grp.Key.service_id,
                                  year1Amount = divideByMillions ? grp.Sum(x => x.year_1_amount).Value / 1000000 :
                                      grp.Sum(x => x.year_1_amount).Value / 1000,
                                  year2Amount = divideByMillions ? grp.Sum(x => x.year_2_amount).Value / 1000000 :
                                      grp.Sum(x => x.year_2_amount).Value / 1000,
                                  year3Amount = divideByMillions ? grp.Sum(x => x.year_3_amount).Value / 1000000 :
                                      grp.Sum(x => x.year_3_amount).Value / 1000,
                                  year4Amount = divideByMillions ? grp.Sum(x => x.year_4_amount).Value / 1000000 :
                                      grp.Sum(x => x.year_4_amount).Value / 1000,
                              }).ToList();

            actionData = actionData.Where(x => ((x.year1Amount + x.year2Amount + x.year3Amount + x.year4Amount) != 0)).ToList();

            //tenantDbContext.Database.CommandTimeout = originalTimeout;

            JArray dataItems = new JArray();
            JObject dataItem;
            string actionTitle;

            foreach (var action in actionData)
            {
                dataItem = new JObject();

                actionTitle = $"<a onClick = 'openProposalPopup({action.actionId}, \"{action.serviceId}\")' onkeypress = 'openProposalPopup({action.actionId}, \"{action.serviceId}\")' href='javascript:void(0)' title=\"(Sprettoppvindu) {action.actionTitle}\">{action.actionTitle}<span class='sr-only'>Lenke åpnes i sprettoppvindu</span></a>";
                dataItem.Add("action", actionTitle);
                dataItem.Add("actionId", action.actionId);
                dataItem.Add("year1", $"{action.year1Amount.ToString(divideByMillions ? formatN1 : formatN0, ci)} {totalPostfix}");
                dataItem.Add("year2", $"{action.year2Amount.ToString(divideByMillions ? formatN1 : formatN0, ci)} {totalPostfix}");
                dataItem.Add("year3", $"{action.year3Amount.ToString(divideByMillions ? formatN1 : formatN0, ci)} {totalPostfix}");
                dataItem.Add("year4", $"{action.year4Amount.ToString(divideByMillions ? formatN1 : formatN0, ci)} {totalPostfix}");
                year1Total = year1Total + action.year1Amount;
                year2Total = year2Total + action.year2Amount;
                year3Total = year3Total + action.year3Amount;
                year4Total = year4Total + action.year4Amount;

                dataItems.Add(dataItem);
            }

            //Add Total Row
            dataItem = new JObject();
            actionTitle = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_sum_title").Value.LangText;
            dataItem.Add("action", $"{actionTitle}");
            dataItem.Add("actionId", " ");
            dataItem.Add("year1", $"{year1Total.ToString(divideByMillions ? formatN1 : formatN0, ci)} {totalPostfix}");
            dataItem.Add("year2", $"{year2Total.ToString(divideByMillions ? formatN1 : formatN0, ci)} {totalPostfix}");
            dataItem.Add("year3", $"{year3Total.ToString(divideByMillions ? formatN1 : formatN0, ci)} {totalPostfix}");
            dataItem.Add("year4", $"{year4Total.ToString(divideByMillions ? formatN1 : formatN0, ci)} {totalPostfix}");
            dataItems.Add(dataItem);

            JArray columns = new JArray();
            JObject column = new JObject();
            column.Add("title", " ");
            column.Add("field", "action");
            column.Add("width", 350);
            column.Add("encoded", false);
            columns.Add(column);

            for (int i = 0; i < 4; i++)
            {
                column = new JObject
                {
                    {"title", $"{budgetYear + i}"},
                    {"headerTemplate", $"<span class=\"bold\">{budgetYear + i}</span>"},
                    {"field", $"year{i + 1}"},
                    {"attributes", new JObject {{"style", "text-align:right;border-left:0px;"}}},
                    {"headerAttributes", new JObject {{"style", "text-align:right;"}}},
                    {"format", divideByMillions ? "{0:n1}" : "{0:n0}"},
                    {"width", 150},
                    {"encoded", false}
                };
                columns.Add(column);
            }

            totalPostfix = string.Empty;
            if (divideByMillions)
            {
                totalPostfix = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_total_millions_post_fix").Value.LangText;
            }
            else
            {
                totalPostfix = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_total_thousands_post_fix").Value.LangText;
            }

            JObject gridData = new JObject
            {
                { "data", dataItems },
                { "columns", columns },
                { "total", $"{year1Total.ToString(divideByMillions ? formatN1 : formatN0, ci)} {totalPostfix}" }
            };

            return gridData;
        }

        public dynamic GetBudgetReductionChartDataActionLevelServiceSetup(string userId, int actionId, string serviceId, string alterCode,
           string graph, int BudgetYear, bool divideByMillions = false, string orgID = null, List<BudgetReductionGraphHelper> resultSet = null)
        {
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            UserData userDetails = _utility.GetUserDetails(userId);
            Dictionary<string, clsLanguageString> langStringFinPlan = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "docexport");

            Dictionary<string, clsLanguageString> langStringValuesNumberFormat = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "NumberFormats");
            string formatN0 = ((langStringValuesNumberFormat.FirstOrDefault(v => v.Key == "amount")).Value).LangText;
            string formatN1 = ((langStringValuesNumberFormat.FirstOrDefault(v => v.Key == "dec1")).Value).LangText;

            string postfix;
            if (divideByMillions)
            {
                postfix = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_total_millions_post_fix").Value.LangText;
            }
            else
            {
                postfix = langStringFinPlan.FirstOrDefault(x => x.Key == "brg_value_thousands_post_fix").Value.LangText;
            }
            CultureInfo ci = Thread.CurrentThread.CurrentCulture;

            int budgetYear = BudgetYear;
            dynamic bdgtActionData = new JObject();

            dynamic label = new JArray();
            label.Add((budgetYear));
            label.Add((budgetYear + 1));
            label.Add((budgetYear + 2));
            label.Add((budgetYear + 3));
            //var originalTimeout = tenantDbContext.Database.CommandTimeout;
            //increase sql db time out
            tenantDbContext.Database.SetCommandTimeout(700);
            var budgetReductionData = orgID == "0" ?
                                       (from dg in resultSet
                                        where dg.pk_action_id == actionId && dg.service_id == serviceId && dg.pk_alter_code == alterCode && dg.graph == graph
                                        group dg by new { dg.long_description, dg.action_description } into grp
                                        select new
                                        {
                                            actionDesc = grp.Key.action_description,
                                            longDesc = grp.Key.long_description,
                                            year1Value = divideByMillions ? grp.Sum(x => x.year_1_amount).Value / 1000000 : grp.Sum(x => x.year_1_amount).Value / 1000,
                                            year2Value = divideByMillions ? grp.Sum(x => x.year_2_amount).Value / 1000000 : grp.Sum(x => x.year_2_amount).Value / 1000,
                                            year3Value = divideByMillions ? grp.Sum(x => x.year_3_amount).Value / 1000000 : grp.Sum(x => x.year_3_amount).Value / 1000,
                                            year4Value = divideByMillions ? grp.Sum(x => x.year_4_amount).Value / 1000000 : grp.Sum(x => x.year_4_amount).Value / 1000
                                        }).FirstOrDefault() :
                                         (from dg in resultSet
                                          where dg.pk_action_id == actionId && dg.service_id == serviceId && dg.pk_alter_code == alterCode && dg.graph == graph && dg.org_id == orgID
                                          group dg by new { dg.long_description, dg.action_description } into grp
                                          select new
                                          {
                                              actionDesc = grp.Key.action_description,
                                              longDesc = grp.Key.long_description,
                                              year1Value = divideByMillions ? grp.Sum(x => x.year_1_amount).Value / 1000000 : grp.Sum(x => x.year_1_amount).Value / 1000,
                                              year2Value = divideByMillions ? grp.Sum(x => x.year_2_amount).Value / 1000000 : grp.Sum(x => x.year_2_amount).Value / 1000,
                                              year3Value = divideByMillions ? grp.Sum(x => x.year_3_amount).Value / 1000000 : grp.Sum(x => x.year_3_amount).Value / 1000,
                                              year4Value = divideByMillions ? grp.Sum(x => x.year_4_amount).Value / 1000000 : grp.Sum(x => x.year_4_amount).Value / 1000
                                          }).FirstOrDefault(); ;
            JArray formattedArray = new JArray();
            if (budgetReductionData != null)
            {
                formattedArray.Add($"{budgetReductionData.year1Value.ToString(divideByMillions ? formatN1 : formatN0, ci)} {postfix}");
                formattedArray.Add($"{budgetReductionData.year2Value.ToString(divideByMillions ? formatN1 : formatN0, ci)} {postfix}");
                formattedArray.Add($"{budgetReductionData.year3Value.ToString(divideByMillions ? formatN1 : formatN0, ci)} {postfix}");
                formattedArray.Add($"{budgetReductionData.year4Value.ToString(divideByMillions ? formatN1 : formatN0, ci)} {postfix}");

                bdgtActionData.Add("ActionDesc", budgetReductionData.actionDesc);
                bdgtActionData.Add("FinPlanDesc", budgetReductionData.longDesc);
            }
            else
            {
                formattedArray.Add($"{0.ToString(divideByMillions ? formatN1 : formatN0, ci)} {postfix}");
                formattedArray.Add($"{0.ToString(divideByMillions ? formatN1 : formatN0, ci)} {postfix}");
                formattedArray.Add($"{0.ToString(divideByMillions ? formatN1 : formatN0, ci)} {postfix}");
                formattedArray.Add($"{0.ToString(divideByMillions ? formatN1 : formatN0, ci)} {postfix}");

                bdgtActionData.Add("ActionDesc", string.Empty);
                bdgtActionData.Add("FinPlanDesc", string.Empty);
            }

            //tenantDbContext.Database.CommandTimeout = originalTimeout;
            bdgtActionData.Add("Data", JArray.FromObject(formattedArray));
            bdgtActionData.Add("Labels", label);
            bdgtActionData.WebUrlLink = ((langStringFinPlan.FirstOrDefault(v => v.Key == "BMWEB_redirect_servicelevel")).Value).LangText;
            bdgtActionData.Add("showPopupDescription", true);
            return bdgtActionData;
        }

        private async Task<List<KeyValueString>> ActionTagsList(int tenantId, List<int> tagIds)
        {
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            var data = await tenantDbContext.tcoActionTags.Where(x => x.FkTenantId == tenantId && tagIds.Contains(x.PkId))
                                                    .Select(x => new KeyValueString { KeyId = x.PkId, ValueString = x.TagDescription }).ToListAsync();
            return data;
        }

        private async Task GetGoalFocusAreaName(int tenantId, Guid goalId, Guid targetId, int strategyId, dynamic actions)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            var goalData = await tenantDbContext.tco_goals.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.pk_goal_id == goalId);
            var targetData = await tenantDbContext.tco_targets.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.pk_target_id == targetId);
            var strategyData = await tenantDbContext.tfp_strategy_text.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.pk_strategy_id == strategyId);
            string goalName = string.Empty;
            string focusAreaDescription = string.Empty;
            List<string> unsGoalsDescriptionList = new List<string>();
            string unsGoalsDescription = string.Empty;
            string targetName = targetData is not null ? targetData.target_name : string.Empty;
            string strategyName = strategyData is not null ? strategyData.strategy_name : string.Empty;
            if (goalData is not null)
            {
                goalName = goalData.goal_name;
                if (!string.IsNullOrEmpty(goalData.focus_area))
                {
                    var fa = await tenantDbContext.tco_focusarea.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.pk_id.ToString() == goalData.focus_area);
                    if (fa is not null)
                    {
                        if (fa.focusarea_description.Count() > 32)
                        {
                            focusAreaDescription = "<span class=\"bp-red-tag\" title=\"" + fa.focusarea_description + "\">" + fa.focusarea_description.Substring(0, 32) + "...</span>";
                        }
                        else
                        {
                            focusAreaDescription = "<span class=\"bp-red-tag\">" + fa.focusarea_description + "</span>";
                        }
                    }
                }
                if (!string.IsNullOrEmpty(goalData.unsd_goals))
                {
                    var unGoalIds = goalData.unsd_goals.Split(',').Distinct().ToList();
                    var unGoalData = await tenantDbContext.gco_un_susdev_goals.Where(x => unGoalIds.Contains(x.pk_goal_id)).ToListAsync();
                    foreach (var uns in unGoalData.OrderBy(x => x.pk_goal_id))
                    {
                        unsGoalsDescriptionList.Add("<span class=\"bp-blue-tag cursor\" title=\"" + uns.goal_name + "\">" + uns.goal_name.Substring(0, 5) + uns.goal_short_name + "</span>");
                    }
                    if (unsGoalsDescriptionList.Any())
                    {
                        unsGoalsDescriptionList = unsGoalsDescriptionList.OrderBy(x => x).ToList();
                        unsGoalsDescription = string.Join("", unsGoalsDescriptionList);
                    }
                }
            }

            actions.Add("goalName", JToken.FromObject(goalName));
            actions.Add("focusAreaDescription", JToken.FromObject(focusAreaDescription));
            actions.Add("unsGoalsDescription", JToken.FromObject(unsGoalsDescription));
            actions.Add("targetName", JToken.FromObject(targetName));
            actions.Add("strategyName", JToken.FromObject(strategyName));
        }
    }
}