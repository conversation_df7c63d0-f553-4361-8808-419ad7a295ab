#pragma warning disable CS8629

#pragma warning disable CS8600
#pragma warning disable CS8602
#pragma warning disable CS8603
#pragma warning disable CS8604
#pragma warning disable CS8625

using Framsikt.BL.Core;
using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Dynamic;
using System.Globalization;

namespace Framsikt.BL
{
    public class CombinedIndicators : ICombinedIndicators
    {
        private readonly IUtility _utility;
        private readonly IAppDataCache _cache;
        private readonly IKostraData _kostraData;

        public CombinedIndicators(IUtility util, IAppDataCache cache, IKostraData kostra)
        {
            _utility = util;
            _cache = cache;
            _kostraData = kostra;
        }

        public dynamic GetSavedGraphsList(string userId)
        {
            dynamic graphsList = new JObject();
            List<GridColumnHelper> column = GetColumns();
            List<GraphList> formattedData = GetSavedGraphData(userId);
            dynamic dColumn = JToken.FromObject(column);
            dynamic dData = JToken.FromObject(formattedData);

            graphsList.Add("column", dColumn);
            graphsList.Add("data", dData);
            return graphsList;
        }

        private List<GridColumnHelper> GetColumns()
        {
            List<GridColumnHelper> formattedColumns = new List<GridColumnHelper>();
            GridColumnHelper columnInfo = new GridColumnHelper
            {
                field = "key",
                title = " ",
                colCount = 0,
                hidden = true,
                encoded = false,
                attributes = new ColumnStyleHelper { style = "display:none;" },
                headerAttributes = new ColumnStyleHelper { style = "display:none;" }
            };
            formattedColumns.Add(columnInfo);
            columnInfo = new GridColumnHelper
            {
                field = "value",
                title = " ",
                colCount = 0,
                encoded = false,
                attributes = new ColumnStyleHelper { style = "text-align:left;border:none;padding-top: 10px;" },
                headerAttributes = new ColumnStyleHelper { style = "display:none;" },
                template = "<div class='combined-indicator-items'></div>"
            };
            formattedColumns.Add(columnInfo);
            return formattedColumns;
        }

        private List<GraphList> GetSavedGraphData(string userId)
        {
            UserData userDetails = _utility.GetUserDetails(userId);
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            int budgetYear = _kostraData.GetBudgetYear(userId);
            var graphList = (from ch in dbContext.tco_combined_indicator_header
                             where ch.Fk_tenant_id == userDetails.tenant_id && ch.budget_year == budgetYear
                             select new GraphList
                             {
                                 key = ch.Pk_Id,
                                 value = ch.graph_name,
                                 adj_deflator = ch.adj_def,
                                 adj_expense = ch.adj_exp
                             }).ToList();
            return graphList;
        }

        public List<indicator> GetIndicators(string userId, string indicatorType)
        {
            UserData userDetails = _utility.GetUserDetails(userId);
            int clientId = userDetails.client_id;
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            if (indicatorType == "Kostra")
            {
                TimeSpan cacheTimeOut = new TimeSpan(200, 0, 0);
                string strKostraInd = _cache.GetStringForTenant(clientId, userDetails.tenant_id, "cacheKostraIndicators");
                if (string.IsNullOrEmpty(strKostraInd))
                {
                    var indicator = (from a in dbContext.gmd_kostra_lables
                                     where a.active == true
                                     select new indicator
                                     {
                                         indicator_code = a.pk_indicator_code,
                                         indicator_description = a.indicator_description,
                                         indicatorType = "Kostra",
                                         numberFormat1 = a.number_type.Trim(),
                                         adjust_deflator = a.deflator_flag == 1,
                                         adjust_expence = a.exp_flag == 1
                                     }).ToList();
                    Dictionary<string, clsLanguageString> numberFormats = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "NumberFormats");

                    indicator.ForEach(x =>
                    {
                        if ((Convert.ToString((numberFormats.FirstOrDefault(w => w.Key == x.numberFormat1).Value).LangText)) != null)
                        {
                            x.numberFormat = (Convert.ToString((numberFormats.FirstOrDefault(w => w.Key == x.numberFormat1).Value).LangText));
                        }
                    });

                    if (indicator != null)
                    {
                        string szindicator = JsonConvert.SerializeObject(indicator);
                        _cache.SetStringForTenant(clientId, userDetails.tenant_id, "cacheKostraIndicators", szindicator, cacheTimeOut);
                    }
                    return indicator;
                }
                else
                {
                    List<indicator> indicatorCacheDetails = JsonConvert.DeserializeObject<List<indicator>>(strKostraInd);
                    return indicatorCacheDetails;
                }
            }
            else if (indicatorType == "Activity")
            {
                var activityIndicator = (from a in dbContext.tco_indicator_setup
                                         where a.fk_tenant_id == userDetails.tenant_id && a.indicator_type == (int)clsConstants.indicator_type.activity
                                         && a.value_type != "text"
                                         select new indicator
                                         {
                                             indicator_code = a.pk_indicator_code.ToString(),
                                             indicator_description = a.measurment_criteria,
                                             indicatorType = "Activity",
                                             numberFormat = a.value_type
                                         }).ToList();
                return activityIndicator;
            }
            else
            {
                return new List<indicator>();
            }
        }

        public void saveDescription(string userId, combinedIndicatorDescription descInput)
        {
            descInput.description = _utility.DecodeHtmlString(descInput.description, userId).GetAwaiter().GetResult();
            UserData userDetails = _utility.GetUserDetails(userId);
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            int budgetYear = _kostraData.GetBudgetYear(userId);
            tco_combined_indicators_description indDesc = dbContext.tco_combined_indicators_description.FirstOrDefault(x => x.fk_combined_indicator == descInput.graphId
                                                                            && x.Fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear && x.fk_template_id == descInput.templateId);
            Dictionary<string, clsLanguageString> langStrings = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "CombinedIndicators");
            if (indDesc == null)
            {
                Guid descGuid = Guid.NewGuid();
                indDesc = new tco_combined_indicators_description
                {
                    budget_year = budgetYear,
                    combined_indicator_description = descInput.description,
                    description_Id = descGuid,
                    fk_combined_indicator = descInput.graphId,
                    Fk_tenant_id = userDetails.tenant_id,
                    updated = DateTime.UtcNow,
                    updated_by = userDetails.pk_id,
                    fk_template_id = descInput.templateId
                };
                dbContext.tco_combined_indicators_description.Add(indDesc);
                dbContext.SaveChanges();
                _utility.SaveTextLog(userId, descGuid, descInput.description, null, ((langStrings.FirstOrDefault(v => v.Key == "cmn_text_prevData_title")).Value).LangText);
            }
            else
            {
                indDesc.combined_indicator_description = descInput.description;
                indDesc.updated = DateTime.UtcNow;
                indDesc.updated_by = userDetails.pk_id;
                dbContext.SaveChanges();
                _utility.SaveTextLog(userId, indDesc.description_Id, descInput.description, null, ((langStrings.FirstOrDefault(v => v.Key == "cmn_text_prevData_title")).Value).LangText);
            }
        }

        public dynamic GetDescription(string userId, combinedIndicatorDescription descInput)
        {
            UserData userDetails = _utility.GetUserDetails(userId);
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            int budgetYear = _kostraData.GetBudgetYear(userId);
            tco_combined_indicators_description indDesc = dbContext.tco_combined_indicators_description.FirstOrDefault(x => x.fk_combined_indicator == descInput.graphId
                                                            && x.Fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear && x.fk_template_id == descInput.templateId);
            dynamic descriptionData = new JObject();
            if (indDesc != null)
            {
                descriptionData.Add("desc", indDesc.combined_indicator_description);
                descriptionData.Add("descId", indDesc.description_Id);
                return descriptionData;
            }
            else
            {
                descriptionData.Add("desc", string.Empty);
                descriptionData.Add("descId", Guid.Empty);
                return descriptionData;
            }
        }

        public dynamic GetIndicatorGrid(string userId, int graphId)
        {
            UserData userDetails = _utility.GetUserDetails(userId);
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            dynamic indicatorGrid = new JObject();
            List<GridColumnHelper> column = GetIndicatorGridColumns(userId);
            List<selectedIndicatorList> formattedData = new List<selectedIndicatorList>();
            if (graphId != 0)
            {
                formattedData = GetIndicatorGridData(userId, graphId);
            }

            dynamic dColumn = JToken.FromObject(column);
            dynamic dData = JToken.FromObject(formattedData);

            indicatorGrid.Add("column", dColumn);
            indicatorGrid.Add("data", dData);
            tco_combined_indicator_header hdr = dbContext.tco_combined_indicator_header.FirstOrDefault(x => x.Pk_Id == graphId && x.Fk_tenant_id == userDetails.tenant_id);
            if (hdr != null)
            {
                indicatorGrid.adj_deflator = hdr.adj_def;
                indicatorGrid.adj_expense = hdr.adj_exp;
            }
            else
            {
                indicatorGrid.adj_deflator = false;
                indicatorGrid.adj_expense = false;
            }
            return indicatorGrid;
        }

        private List<GridColumnHelper> GetIndicatorGridColumns(string userId)
        {
            UserData userDetails = _utility.GetUserDetails(userId);
            Dictionary<string, clsLanguageString> langStrings = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "CombinedIndicators");
            List<GridColumnHelper> formattedColumns = new List<GridColumnHelper>();
            GridColumnHelper columnInfo = new GridColumnHelper
            {
                field = "indDetailId",
                title = " ",
                colCount = 0,
                hidden = true,
                encoded = false,
                attributes = new ColumnStyleHelper { style = "display:none;" },
                headerAttributes = new ColumnStyleHelper { style = "display:none;" }
            };
            formattedColumns.Add(columnInfo);
            columnInfo = new GridColumnHelper
            {
                field = "indicator_code",
                title = " ",
                colCount = 0,
                hidden = true,
                encoded = false,
                attributes = new ColumnStyleHelper { style = "display:none;" },
                headerAttributes = new ColumnStyleHelper { style = "display:none;" }
            };
            formattedColumns.Add(columnInfo);
            columnInfo = new GridColumnHelper
            {
                field = "indicator_description",
                title = ((langStrings.FirstOrDefault(v => v.Key == "CI_indicator_name")).Value).LangText,
                colCount = 0,
                encoded = false,
                attributes = new ColumnStyleHelper { style = "text-align:left;border:none;width:400px;" },
                headerAttributes = new ColumnStyleHelper { style = "text-align:left;border:none;width:400px;" }
            };
            formattedColumns.Add(columnInfo);
            columnInfo = new GridColumnHelper
            {
                field = "graphType",
                title = ((langStrings.FirstOrDefault(v => v.Key == "CI_graph_type")).Value).LangText,
                colCount = 0,
                encoded = false,
                attributes = new ColumnStyleHelper { style = "text-align:center;border:none;width:100px;" },
                headerAttributes = new ColumnStyleHelper { style = "text-align:center;border:none;width:100px;" },
                template = "<div class='ci-graph-type'></div>"
            };
            formattedColumns.Add(columnInfo);
            columnInfo = new GridColumnHelper
            {
                field = "delete",
                title = ((langStrings.FirstOrDefault(v => v.Key == "CI_delete_indicator")).Value).LangText,
                colCount = 0,
                encoded = false,
                attributes = new ColumnStyleHelper { style = "text-align:right;border:none;white-space:normal;width:30px;" },
                headerAttributes = new ColumnStyleHelper { style = "text-align:center;border:none;width:30px;" },
                template = "<span class='col-md-9 padding0 align-center'><a class='ci-ind-delete'><img src='../images/close_small.png'></a></span>"
            };
            formattedColumns.Add(columnInfo);
            return formattedColumns;
        }

        private List<selectedIndicatorList> GetIndicatorGridData(string userId, int graphId)
        {
            UserData userDetails = _utility.GetUserDetails(userId);
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            int budgetYear = _kostraData.GetBudgetYear(userId);
            List<selectedIndicatorList> finalData = new List<selectedIndicatorList>();
            var indicatorData = (from cd in dbContext.tco_combined_indicator_detail
                                 join kl in dbContext.gmd_kostra_lables on cd.indicator_code equals kl.pk_indicator_code
                                 where cd.fk_combined_indicator == graphId && cd.Fk_tenant_id == userDetails.tenant_id && cd.budget_year == budgetYear && cd.org_id == ""
                                 orderby cd.sort_order descending
                                 select new selectedIndicatorList
                                 {
                                     indDetailId = cd.Pk_Id,
                                     indicator_code = cd.indicator_code,
                                     indicator_description = cd.indicator_description,
                                     orgId = cd.org_id,
                                     orgLevel = cd.org_level,
                                     sortOrder = cd.sort_order,
                                     barChartSelected = cd.graph_type == "Bar" ? true : false,
                                     lineChartSelected = cd.graph_type == "Line" ? true : false,
                                     indicatorType = cd.org_level == 0 ? "Kostra" : "Activity",
                                     numberFormat = cd.number_type,
                                     adj_deflator = kl.deflator_flag == 1,
                                     adj_expense = kl.exp_flag == 1
                                 }).ToList();

            var activityIndData = (from cd in dbContext.tco_combined_indicator_detail
                                   join kl in dbContext.tco_indicator_setup on cd.indicator_code equals kl.pk_indicator_code.ToString()
                                   where cd.fk_combined_indicator == graphId && cd.Fk_tenant_id == userDetails.tenant_id && cd.budget_year == budgetYear && cd.org_id != ""
                                   orderby cd.sort_order descending
                                   select new selectedIndicatorList
                                   {
                                       indDetailId = cd.Pk_Id,
                                       indicator_code = cd.indicator_code,
                                       indicator_description = cd.indicator_description,
                                       orgId = cd.org_id,
                                       orgLevel = cd.org_level,
                                       sortOrder = cd.sort_order,
                                       barChartSelected = cd.graph_type == "Bar" ? true : false,
                                       lineChartSelected = cd.graph_type == "Line" ? true : false,
                                       indicatorType = cd.org_level == 0 ? "Kostra" : "Activity",
                                       numberFormat = cd.number_type,
                                       adj_deflator = false,
                                       adj_expense = false
                                   }).ToList();

            finalData.AddRange(indicatorData);
            finalData.AddRange(activityIndData);

            return finalData;
        }

        public void DeleteGraph(string userId, int graphId)
        {
            UserData userDetails = _utility.GetUserDetails(userId);
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            //delete description
            if ((dbContext.tco_combined_indicators_description.FirstOrDefault(x => x.fk_combined_indicator == graphId && x.Fk_tenant_id == userDetails.tenant_id)) != null)
            {
                dbContext.tco_combined_indicators_description.RemoveRange(dbContext.tco_combined_indicators_description.Where(x => x.fk_combined_indicator == graphId && x.Fk_tenant_id == userDetails.tenant_id));
                dbContext.SaveChanges();
            }
            //delete from detail table
            if ((dbContext.tco_combined_indicator_detail.FirstOrDefault(x => x.fk_combined_indicator == graphId && x.Fk_tenant_id == userDetails.tenant_id)) != null)
            {
                dbContext.tco_combined_indicator_detail.RemoveRange(dbContext.tco_combined_indicator_detail.Where(x => x.fk_combined_indicator == graphId && x.Fk_tenant_id == userDetails.tenant_id));
                dbContext.SaveChanges();
            }
            //delete from header table
            if ((dbContext.tco_combined_indicator_header.FirstOrDefault(x => x.Pk_Id == graphId && x.Fk_tenant_id == userDetails.tenant_id)) != null)
            {
                dbContext.tco_combined_indicator_header.RemoveRange(dbContext.tco_combined_indicator_header.Where(x => x.Pk_Id == graphId && x.Fk_tenant_id == userDetails.tenant_id));
                dbContext.SaveChanges();
            }
        }

        public int CreateGraph(CombinedIndicatorHelper inputData, string userId)
        {
            UserData userDetails = _utility.GetUserDetails(userId);
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            int budgetYear = _kostraData.GetBudgetYear(userId);
            //add new graph
            int newGraphId = inputData.graphId;
            if (inputData.graphId == 0)
            {
                //save in header table
                tco_combined_indicator_header headerData = new tco_combined_indicator_header
                {
                    adj_def = inputData.adj_deflator,
                    adj_exp = inputData.adj_expense,
                    budget_year = budgetYear,
                    Fk_tenant_id = userDetails.tenant_id,
                    graph_name = inputData.graphName,
                    isPrivate = inputData.isPrivate,
                    updated = DateTime.UtcNow,
                    updated_by = userDetails.pk_id
                };
                dbContext.tco_combined_indicator_header.Add(headerData);
                dbContext.SaveChanges();
                newGraphId = headerData.Pk_Id;
                //save in detail Data
                List<tco_combined_indicator_detail> detailDatas = new List<tco_combined_indicator_detail>();
                tco_combined_indicator_detail detailData;
                foreach (var item in inputData.indicatorList.Where(x => x.isDeleted == false).ToList())
                {
                    detailData = new tco_combined_indicator_detail
                    {
                        budget_year = budgetYear,
                        Fk_tenant_id = userDetails.tenant_id,
                        fk_combined_indicator = newGraphId,
                        graph_type = item.barChartSelected ? "Bar" : "Line",
                        indicator_code = item.indicator_code,
                        org_id = string.IsNullOrEmpty(item.orgId) ? "" : item.orgId,
                        org_level = item.orgLevel,
                        sort_order = item.sortOrder,
                        indicator_description = item.indicator_description,
                        updated = DateTime.UtcNow,
                        updated_by = userDetails.pk_id,
                        number_type = item.numberFormat
                    };
                    detailDatas.Add(detailData);
                }
                if (detailDatas.Any())
                {
                    dbContext.BulkInsert(detailDatas);
                }
            }
            else//update existing graph
            {
                //header data update
                tco_combined_indicator_header headerData = dbContext.tco_combined_indicator_header.FirstOrDefault(x => x.Pk_Id == inputData.graphId && x.Fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear);
                if (headerData != null)
                {
                    headerData.adj_def = inputData.adj_deflator;
                    headerData.adj_exp = inputData.adj_expense;
                    headerData.updated = DateTime.UtcNow;
                    headerData.updated_by = userDetails.pk_id;
                    dbContext.SaveChanges();
                }
                //detail data update
                var detailData = dbContext.tco_combined_indicator_detail.Where(x => x.fk_combined_indicator == inputData.graphId && x.Fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear).ToList();
                if (detailData.Any())
                {
                    //delete data from table which are deleted from UI
                    List<tco_combined_indicator_detail> dataTodelete = new List<tco_combined_indicator_detail>();
                    foreach (var item in inputData.indicatorList.Where(x => x.isDeleted == true && x.indDetailId != 0).ToList())
                    {
                        dataTodelete.Add(detailData.FirstOrDefault(x => x.Pk_Id == item.indDetailId && x.Fk_tenant_id == userDetails.tenant_id));
                    }
                    if (dataTodelete.Any())
                    {
                        dbContext.BulkDelete(dataTodelete);
                    }
                    //Add indicator
                    List<tco_combined_indicator_detail> newDetailDatas = new List<tco_combined_indicator_detail>();
                    tco_combined_indicator_detail newDetailData;
                    foreach (var item in inputData.indicatorList.Where(x => x.indDetailId == 0 && x.isDeleted == false).ToList())
                    {
                        newDetailData = new tco_combined_indicator_detail
                        {
                            budget_year = budgetYear,
                            Fk_tenant_id = userDetails.tenant_id,
                            fk_combined_indicator = inputData.graphId,
                            graph_type = item.barChartSelected ? "Bar" : "Line",
                            indicator_code = item.indicator_code,
                            org_id = item.orgId == null ? string.Empty : item.orgId,
                            org_level = item.orgLevel,
                            sort_order = item.sortOrder,
                            indicator_description = item.indicator_description,
                            updated = DateTime.UtcNow,
                            updated_by = userDetails.pk_id,
                            number_type = item.numberFormat
                        };
                        newDetailDatas.Add(newDetailData);
                    }
                    if (newDetailDatas.Any())
                    {
                        dbContext.BulkInsert(newDetailDatas);
                    }
                    //Update Existing indicator
                    foreach (var item in inputData.indicatorList.Where(x => x.indDetailId != 0 && x.isDeleted == false).ToList())
                    {
                        var dataToUpdate = detailData.FirstOrDefault(x => x.Pk_Id == item.indDetailId && x.Fk_tenant_id == userDetails.tenant_id);
                        if (dataToUpdate != null)
                        {
                            dataToUpdate.graph_type = item.barChartSelected ? "Bar" : "Line";
                            dataToUpdate.org_id = !string.IsNullOrEmpty(item.orgId) ? item.orgId : string.Empty;
                            dataToUpdate.org_level = item.orgLevel;
                            dataToUpdate.sort_order = item.sortOrder;
                            dataToUpdate.updated = DateTime.UtcNow;
                            dataToUpdate.updated_by = userDetails.pk_id;
                        }
                        dbContext.SaveChanges();
                    }
                }
            }
            return newGraphId;
        }

        public dynamic GenerateGraphAndGrid(string userId, CombinedIndicatorHelper inputData)
        {
            UserData userDetails = _utility.GetUserDetails(userId);
            dynamic graphAndGrid = new JObject();
            dynamic chartDataArray = new JArray();
            dynamic chartData = new JObject();
            dynamic tableData = new JArray();

            #region Graph config

            dynamic legend = new JObject();
            legend.position = "bottom";
            legend.visible = true;
            chartData.legend = legend;

            dynamic majorGridLines = new JObject();
            majorGridLines.visible = false;
            chartData.majorGridLines = majorGridLines;

            dynamic valueAxis = new JObject();
            dynamic labels = new JObject();
            labels.visible = false;
            valueAxis.labels = labels;
            dynamic line = new JObject();
            line.visible = true;
            valueAxis.line = line;
            majorGridLines = new JObject();
            majorGridLines.visible = false;
            valueAxis.majorGridLines = majorGridLines;
            chartData.valueAxis = valueAxis;

            #endregion Graph config

            #region chart Data

            GenerateTenantGraphData(userId, inputData, chartData, tableData);

            #endregion chart Data

            chartDataArray.Add(chartData);
            dynamic ImageHelper = new JObject();
            dynamic AdjustmentSetup = new JArray();
            Dictionary<string, clsLanguageString> langStrings = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "CombinedIndicators");
            string adjExp = ((langStrings.FirstOrDefault(v => v.Key == "CI_CombinedIndicatorAdjustNeeds")).Value).LangText;
            string adjDef = ((langStrings.FirstOrDefault(v => v.Key == "CI_CombinedIndicatorDeflatorNeeds")).Value).LangText;
            AdjustmentSetup.Add(inputData.adj_expense ? adjExp : string.Empty);
            AdjustmentSetup.Add(inputData.adj_deflator ? adjDef : string.Empty);
            ImageHelper.AdjustmentSetup = AdjustmentSetup;
            chartDataArray.Add(ImageHelper);

            //Grid service
            dynamic indicatorGrid = new JObject();
            List<GridColumnHelper> column = GetGraphGridColumns(userId, inputData);
            dynamic dColumn = JToken.FromObject(column);
            dynamic dData = new JArray();
            indicatorGrid.Add("column", dColumn);
            indicatorGrid.Add("data", tableData);
            graphAndGrid.Add("chartData", chartDataArray);
            graphAndGrid.Add("gridData", indicatorGrid);
            return graphAndGrid;
        }

        public dynamic GenerateGraphAndGridYear(string userId, CombinedIndicatorHelper inputData)
        {
            UserData userDetails = _utility.GetUserDetails(userId);
            dynamic graphAndGrid = new JObject();
            dynamic chartDataArray = new JArray();
            dynamic chartData = new JObject();
            dynamic tableData = new JArray();

            #region Graph config

            dynamic legend = new JObject();
            legend.position = "bottom";
            legend.visible = true;
            chartData.legend = legend;

            dynamic majorGridLines = new JObject();
            majorGridLines.visible = false;
            chartData.majorGridLines = majorGridLines;

            dynamic valueAxis = new JObject();
            dynamic labels = new JObject();
            labels.visible = false;
            valueAxis.labels = labels;
            dynamic line = new JObject();
            line.visible = true;
            valueAxis.line = line;
            majorGridLines = new JObject();
            majorGridLines.visible = false;
            valueAxis.majorGridLines = majorGridLines;
            chartData.valueAxis = valueAxis;

            #endregion Graph config

            #region chart Data

            GenerateTenantGraphYearData(userId, inputData, chartData, tableData);

            #endregion chart Data

            chartDataArray.Add(chartData);
            dynamic ImageHelper = new JObject();
            dynamic AdjustmentSetup = new JArray();
            Dictionary<string, clsLanguageString> langStrings = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "CombinedIndicators");
            string adjExp = ((langStrings.FirstOrDefault(v => v.Key == "CI_CombinedIndicatorAdjustNeeds")).Value).LangText;
            string adjDef = ((langStrings.FirstOrDefault(v => v.Key == "CI_CombinedIndicatorDeflatorNeeds")).Value).LangText;
            AdjustmentSetup.Add(inputData.adj_expense ? adjExp : string.Empty);
            AdjustmentSetup.Add(inputData.adj_deflator ? adjDef : string.Empty);
            ImageHelper.AdjustmentSetup = AdjustmentSetup;
            chartDataArray.Add(ImageHelper);

            //Grid service
            dynamic indicatorGrid = new JObject();
            List<GridColumnHelper> column = GetGraphGridColumns(userId, inputData);
            dynamic dColumn = JToken.FromObject(column);
            dynamic dData = new JArray();
            indicatorGrid.Add("column", dColumn);
            indicatorGrid.Add("data", tableData);
            graphAndGrid.Add("chartData", chartDataArray);
            graphAndGrid.Add("gridData", indicatorGrid);
            return graphAndGrid;
        }

        private List<GridColumnHelper> GetGraphGridColumns(string userId, CombinedIndicatorHelper inputData)
        {
            UserData userDetails = _utility.GetUserDetails(userId);
            Dictionary<string, clsLanguageString> langStrings = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "CombinedIndicators");
            List<GridColumnHelper> formattedColumns = new List<GridColumnHelper>();
            GridColumnHelper columnInfo = new GridColumnHelper
            {
                field = "region",
                title = " ",
                colCount = 0,
                width = 250,
                encoded = false,
                attributes = new ColumnStyleHelper { style = "text-align:left;border-left:none;width:250px;" },
                headerAttributes = new ColumnStyleHelper { style = "text-align:left;border-left:none;width:250px;" },
                headerTemplate = null
            };
            formattedColumns.Add(columnInfo);

            int Counter = 1;
            foreach (var item in inputData.indicatorList.OrderBy(x => x.sortOrder).ToList())
            {
                columnInfo = new GridColumnHelper
                {
                    field = "indicator" + Counter,
                    title = item.indicator_description,
                    width = 200,
                    encoded = false,
                    attributes = new ColumnStyleHelper { style = "text-align:right;border-left:none;width:200px;" },
                    headerAttributes = new ColumnStyleHelper { style = "text-align:right;border-left:none;width:200px;" },
                    headerTemplate = "<span class='semi'>" + item.indicator_description + "</span>"
                };
                if (item.numberFormat == "##0.00 \\%")
                { //two decimal place
                    columnInfo.format = "{0:n2}%";
                }
                else if (item.numberFormat == "##0.0 \\%")
                { //single decimal place
                    columnInfo.format = "{0:n1}%";
                }
                else
                {
                    columnInfo.format = "{0:" + item.numberFormat + "}";
                }
                formattedColumns.Add(columnInfo);
                Counter++;
            }

            return formattedColumns;
        }

        private void GenerateTenantGraphData(string userId, CombinedIndicatorHelper inputData, dynamic chartData, JArray tableData)
        {
            UserData userDetails = _utility.GetUserDetails(userId);
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            int budgetYear = _kostraData.GetBudgetYear(userId);
            TenantData tenantDetails = _utility.GetTenantData(userId);
            string ownRegion = tenantDetails.municipality_id;
            List<citySelction> lstRegion = new List<citySelction>();
            List<citySelction> currentCities = (from city in dbContext.gco_region_codes
                                                where city.pk_region_code == tenantDetails.municipality_id

                                                select new citySelction
                                                {
                                                    cityID = city.pk_region_code,
                                                    cityName = city.region_name
                                                }).ToList();

            lstRegion.AddRange(currentCities);

            if (inputData.selectedRegions != null && inputData.selectedRegions.Count > 0)
            {
                List<citySelction> lstCities = (from ru in inputData.selectedRegions
                                                select new citySelction
                                                {
                                                    cityID = ru.RegionCode,
                                                    cityName = ru.RegionName
                                                }).ToList();
                lstRegion.AddRange(lstCities);
            }
            //set a serial number to the cities to maintain the order.
            int SlNo = 1;
            lstRegion.ForEach(x =>
            {
                x.serialNumber = SlNo;
                SlNo++;
            });

            dynamic categoryAxis = new JObject();
            dynamic categories = new JArray();
            foreach (var item in lstRegion)
            {
                categories.Add(item.cityName);
            }
            dynamic majorGridLines = new JObject();
            majorGridLines.visible = false;
            categoryAxis.categories = categories;
            categoryAxis.majorGridLines = majorGridLines;
            chartData.categoryAxis = categoryAxis;

            List<kostraValue> kostraValue = GetKostraIndicatorValue(inputData.indicatorList.Where(x => x.indicatorType == "Kostra").OrderBy(x => x.sortOrder).Select(x => x.indicator_code).ToList(),
                                                            lstRegion, userId, inputData.adj_expense, inputData.adj_deflator, new List<int> { (budgetYear - 1) });
            List<ActivityData> activityData = GetActivityIndicatorData(userId, inputData.indicatorList.Where(x => x.indicatorType == "Activity").OrderBy(x => x.sortOrder).Select(x => x.indicator_code).ToList(), new List<int> { (budgetYear - 1) });
            Dictionary<string, string> colorsForGraph = _utility.GetColors(userId, ColorsFor.Graph);
            dynamic valueAxes = new JArray();
            dynamic series = new JObject();
            dynamic seriesArray = new JArray();
            int counter = 1;
            foreach (var item in inputData.indicatorList.OrderBy(x => x.sortOrder).ToList())
            {
                series = new JObject();
                dynamic indData = new JObject();
                string type = _kostraData.GetAxisName(item.indicator_code, userId);

                indData.name = "line" + counter;
                indData.color = colorsForGraph[counter.ToString()];
                dynamic title = new JObject();
                title.text = item.indicator_description + " (" + type + ")";
                title.font = "14px sans-serif";
                indData.title = title;

                valueAxes.Add(indData);

                series.tooltipText = item.numberFormat;
                series.name = item.indicator_description + " (" + type + ")";
                series.color = colorsForGraph[counter.ToString()];
                series.type = item.barChartSelected ? "column" : "line";
                series.visible = true;
                series.spacing = 0;
                series.axis = "line" + counter;
                series.width = 1;
                series.gap = 3;

                dynamic seriesData = new JArray();
                foreach (var region in lstRegion)
                {
                    if (item.indicatorType == "Kostra")
                    {
                        if (kostraValue.FirstOrDefault(x => x.indicatorCode == item.indicator_code && x.fk_region_code==region.cityID)!=null)
                        {
                            seriesData.Add(kostraValue.FirstOrDefault(x => x.indicatorCode == item.indicator_code && x.fk_region_code == region.cityID).indicator_value);
                        }
                        else
                        {
                               seriesData.Add(0);
                        }
                    }
                    else
                    {
                        if (activityData.Where(x => x.indicatorCode == item.indicator_code && x.orgLevel == item.orgLevel && x.orgValue == item.orgId && ownRegion == region.cityID).Select(x => x.indicator_value).Any())
                        {
                            List<string> activityValue = activityData.Where(x => x.indicatorCode == item.indicator_code && x.orgLevel == item.orgLevel && x.orgValue == item.orgId).Select(x => x.indicator_value).ToList();
                            List<decimal> convertedActivityValue = new List<decimal>();
                            foreach (var aValue in activityValue)
                            {
                                decimal indicatorValue = 0;
                                bool success = decimal.TryParse(aValue, out indicatorValue);

                                convertedActivityValue.Add(indicatorValue);
                            }
                            seriesData.Add(convertedActivityValue.Sum(x => x));
                        }
                        else
                        {
                            seriesData.Add(0);
                        }
                        for (int i = 1; i < lstRegion.Count(); i++)
                        {
                            seriesData.Add(0);
                        }
                    }
                }
                series.data = seriesData;
                seriesArray.Add(series);

                dynamic markers = new JObject();
                markers.visible = false;
                series.markers = markers;

                dynamic tooltip = new JObject();
                tooltip.visible = true;
                tooltip.template = "#= series.name #: #= (kendo.toString(value,series.tooltipText)) #";
                series.tooltip = tooltip;

                counter++;
            }
            chartData.series = seriesArray;
            chartData.valueAxes = valueAxes;
    
            foreach (var item in lstRegion)
            {
                int indCounter = 1;
                var dynamicObject = new ExpandoObject() as IDictionary<string, Object>;
                dynamicObject.Add("region", item.cityName);
                foreach (var indicator in inputData.indicatorList.OrderBy(x => x.sortOrder).ToList())
                {
                    if (indicator.indicatorType == "Kostra")
                    {
                        if (kostraValue.Where(x => x.indicatorCode == indicator.indicator_code && x.fk_region_code == item.cityID).Select(x => x.indicator_value).Any())
                        {
                            dynamicObject.Add("indicator" + indCounter, kostraValue.FirstOrDefault(x => x.indicatorCode == indicator.indicator_code && x.fk_region_code == item.cityID).indicator_value);
                        }
                        else
                        {
                            dynamicObject.Add("indicator" + indCounter, 0);
                        }
                    }
                    else
                    {
                        if (activityData.Where(x => x.indicatorCode == indicator.indicator_code && x.orgLevel == indicator.orgLevel && x.orgValue == indicator.orgId).Select(x => x.indicator_value).Any() && ownRegion == item.cityID)
                        {
                            List<string> activityValue = activityData.Where(x => x.indicatorCode == indicator.indicator_code && x.orgLevel == indicator.orgLevel && x.orgValue == indicator.orgId).Select(x => x.indicator_value).ToList();
                            List<decimal> convertedActivityValue = new List<decimal>();
                            foreach (var aValue in activityValue)
                            {
                                decimal indicatorValue = 0;
                                bool success = decimal.TryParse(aValue, out indicatorValue);

                                convertedActivityValue.Add(indicatorValue);
                            }
                            dynamicObject.Add("indicator" + indCounter, convertedActivityValue.Sum(x => x));
                        }
                        else
                        {
                            dynamicObject.Add("indicator" + indCounter, 0);
                        }
                    }
                    indCounter++;
                }
                tableData.Add(JToken.FromObject(dynamicObject));
            }
        }

        private void GenerateTenantGraphYearData(string userId, CombinedIndicatorHelper inputData, dynamic chartData, JArray tableData)
        {
            UserData userDetails = _utility.GetUserDetails(userId);
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            int budgetYear = _kostraData.GetBudgetYear(userId);
            TenantData tenantDetails = _utility.GetTenantData(userId);

            List<citySelction> lstRegion = new List<citySelction>();
            List<citySelction> currentCities = (from city in dbContext.gco_region_codes
                                                where city.pk_region_code == tenantDetails.municipality_id

                                                select new citySelction
                                                {
                                                    cityID = city.pk_region_code,
                                                    cityName = city.region_name
                                                }).ToList();

            lstRegion.AddRange(currentCities);

            if (inputData.selectedRegions != null && inputData.selectedRegions.Count > 0)
            {
                List<citySelction> lstCities = (from ru in inputData.selectedRegions
                                                select new citySelction
                                                {
                                                    cityID = ru.RegionCode,
                                                    cityName = ru.RegionName
                                                }).ToList();
                lstRegion.AddRange(lstCities);
            }
            //set a serial number to the cities to maintain the order.
            int SlNo = 1;
            lstRegion.ForEach(x =>
            {
                x.serialNumber = SlNo;
                SlNo++;
            });

            List<int> lstYear = new List<int> { (budgetYear + 1), (budgetYear), (budgetYear - 1), (budgetYear - 2), (budgetYear - 3), (budgetYear - 4) };

            dynamic categoryAxis = new JObject();
            dynamic categories = new JArray();
            foreach (var item in lstYear.OrderBy(x => x).ToList())
            {
                categories.Add(item);
            }
            dynamic majorGridLines = new JObject();
            majorGridLines.visible = false;
            categoryAxis.categories = categories;
            categoryAxis.majorGridLines = majorGridLines;
            chartData.categoryAxis = categoryAxis;

            List<kostraValue> kostraValue = GetKostraIndicatorValue(inputData.indicatorList.Where(x => x.indicatorType == "Kostra").OrderBy(x => x.sortOrder).Select(x => x.indicator_code).ToList(),
                                                            lstRegion, userId, inputData.adj_expense, inputData.adj_deflator, lstYear);
            List<ActivityData> activityData = GetActivityIndicatorData(userId, inputData.indicatorList.Where(x => x.indicatorType == "Activity").OrderBy(x => x.sortOrder).Select(x => x.indicator_code).ToList(), lstYear);
            Dictionary<string, string> colorsForGraph = _utility.GetColors(userId, ColorsFor.Graph);
            dynamic valueAxes = new JArray();
            dynamic series = new JObject();
            dynamic seriesArray = new JArray();
            int counter = 1;
            foreach (var item in inputData.indicatorList.OrderBy(x => x.sortOrder).ToList())
            {
                series = new JObject();
                dynamic indData = new JObject();
                string type = _kostraData.GetAxisName(item.indicator_code, userId);

                indData.name = "line" + counter;
                indData.color = colorsForGraph[counter.ToString()];
                dynamic title = new JObject();
                title.text = item.indicator_description + " (" + type + ")";
                title.font = "14px sans-serif";
                indData.title = title;

                valueAxes.Add(indData);

                series.tooltipText = item.numberFormat;
                series.name = item.indicator_description + " (" + type + ")";
                series.color = colorsForGraph[counter.ToString()];
                series.type = item.barChartSelected ? "column" : "line";
                series.visible = true;
                series.spacing = 0;
                series.axis = "line" + counter;
                series.width = 1;
                series.gap = 3;

                dynamic seriesData = new JArray();
                if (item.indicatorType == "Kostra")
                {
                    if (kostraValue.Where(x => x.indicatorCode == item.indicator_code).Select(x => x.indicator_value).Any())
                    {
                        seriesData.Add(kostraValue.Where(x => x.indicatorCode == item.indicator_code).GroupBy(x => x.year).Select(x => x.Sum(y => y.indicator_value)).ToList());
                    }
                    else
                    {
                        for (int i = 1; i <= lstRegion.Count(); i++)
                        {
                            seriesData.Add(0);
                        }
                    }
                }
                else
                {
                    int dataCount = activityData.Where(x => x.indicatorCode == item.indicator_code && x.orgLevel == item.orgLevel && x.orgValue == item.orgId).Select(x => x.indicator_value).Count();
                    foreach (var year in lstYear.OrderBy(x => x).ToList())
                    {
                        if (activityData.Where(x => x.indicatorCode == item.indicator_code && x.orgLevel == item.orgLevel && x.orgValue == item.orgId && x.year == year).Select(x => x.indicator_value).Any())
                        {
                            seriesData.Add(activityData.Where(x => x.indicatorCode == item.indicator_code && x.orgLevel == item.orgLevel && x.orgValue == item.orgId && x.year == year).Sum(x => decimal.Parse(x.indicator_value)));
                        }
                        else
                        {
                            seriesData.Add(0);
                        }
                    }
                }
                series.data = seriesData;
                seriesArray.Add(series);

                dynamic markers = new JObject();
                markers.visible = false;
                series.markers = markers;

                dynamic tooltip = new JObject();
                tooltip.visible = true;
                tooltip.template = "#= series.name #: #= (kendo.toString(value,series.tooltipText)) #";
                series.tooltip = tooltip;

                counter++;
            }
            chartData.series = seriesArray;
            chartData.valueAxes = valueAxes;

            foreach (var item in lstYear.OrderBy(x => x).ToList())
            {
                int indCounter = 1;
                var dynamicObject = new ExpandoObject() as IDictionary<string, Object>;
                dynamicObject.Add("region", item);
                foreach (var indicator in inputData.indicatorList.OrderBy(x => x.sortOrder).ToList())
                {
                    if (indicator.indicatorType == "Kostra")
                    {
                        if (kostraValue.Where(x => x.indicatorCode == indicator.indicator_code && x.year == item && x.fk_region_code == tenantDetails.municipality_id).Select(x => x.indicator_value).Any())
                        {
                            dynamicObject.Add("indicator" + indCounter, kostraValue.Where(x => x.indicatorCode == indicator.indicator_code && x.year == item && x.fk_region_code == tenantDetails.municipality_id).GroupBy(x => x.year).Select(x => x.Sum(y => y.indicator_value)).First());
                        }
                        else
                        {
                            dynamicObject.Add("indicator" + indCounter, 0);
                        }
                    }
                    else
                    {
                        if (activityData.Where(x => x.indicatorCode == indicator.indicator_code && x.orgLevel == indicator.orgLevel && x.orgValue == indicator.orgId && x.year == item).Select(x => x.indicator_value).Any())
                        {
                            dynamicObject.Add("indicator" + indCounter, activityData.Where(x => x.indicatorCode == indicator.indicator_code && x.orgLevel == indicator.orgLevel && x.orgValue == indicator.orgId && x.year == item).Sum(x => decimal.Parse(x.indicator_value)));
                        }
                        else
                        {
                            dynamicObject.Add("indicator" + indCounter, 0);
                        }
                    }
                    indCounter++;
                }
                tableData.Add(JToken.FromObject(dynamicObject));
            }
        }

        private List<kostraValue> GetKostraIndicatorValue(List<string> indicatorCode, List<citySelction> cityData, string userId, bool adjExp, bool adjDflr, List<int> lstYears)
        {
            UserData userDetails = _utility.GetUserDetails(userId);
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            List<kostraValue> kostraData = new List<kostraValue>();
            List<string> cityId = cityData.Select(x => x.cityID).ToList();
            if (adjExp == false && adjDflr == false)
            {
                kostraData = (from kc in dbContext.gko_kostra_data_corp
                              where cityId.Contains(kc.fk_region_code) && lstYears.Contains(kc.year) && indicatorCode.Contains(kc.fk_indicator_code)
                              select new kostraValue
                              {
                                  indicatorCode = kc.fk_indicator_code,
                                  indicator_value = kc.indicator_value,
                                  fk_region_code = kc.fk_region_code,
                                  year = kc.year
                              }).ToList();
            }
            else if (adjExp && adjDflr == false)
            {
                kostraData = (from kc in dbContext.gko_kostra_data_corp
                              where cityId.Contains(kc.fk_region_code) && lstYears.Contains(kc.year) && indicatorCode.Contains(kc.fk_indicator_code)
                              select new kostraValue
                              {
                                  indicatorCode = kc.fk_indicator_code,
                                  indicator_value = kc.indicator_value + kc.exp_adj_value,
                                  fk_region_code = kc.fk_region_code,
                                  year = kc.year
                              }).ToList();
            }
            else if (adjDflr)
            {
                List<clsIndicators> IndicatorData = GetDeflatorValuesForMultipleIndicatorsWithMultipleRegionsAndYears(userId, cityId, indicatorCode, adjExp, adjDflr, lstYears);
                kostraData = (from id in IndicatorData
                              select new kostraValue
                              {
                                  indicatorCode = id.indicatorCode,
                                  indicator_value = id.indicatorValue.Value,
                                  fk_region_code = id.RegionCode,
                                  year = id.year
                              }).ToList();
            }
            var sortedData = (from kd in kostraData
                              join cd in cityData on kd.fk_region_code equals cd.cityID
                              orderby cd.serialNumber ascending
                              select new kostraValue
                              {
                                  indicatorCode = kd.indicatorCode,
                                  indicator_value = kd.indicator_value,
                                  fk_region_code = kd.fk_region_code,
                                  year = kd.year
                              }).ToList();
            return sortedData;
        }

        private List<ActivityData> GetActivityIndicatorData(string userId, List<string> indicatorCode, List<int> lstYears)
        {
            UserData userDetails = _utility.GetUserDetails(userId);
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            var activityIndData = (from ir in dbContext.tmd_indicator_results
                                   join ins in dbContext.tco_indicator_setup on new { a = ir.fk_indicator_id, b = ir.fk_tenant_id }
                                                                         equals new { a = ins.pk_indicator_code, b = ins.fk_tenant_id }
                                   where indicatorCode.Contains(ir.fk_indicator_id.ToString()) && ir.fk_tenant_id == userDetails.tenant_id && lstYears.Contains(ir.budget_year)
                                   select new ActivityData
                                   {
                                       indicatorCode = ir.fk_indicator_id.ToString(),
                                       orgValue = ir.org_id,
                                       orgLevel = ir.org_level,
                                       indicator_value = ir.total_year,
                                       valueType = ins.value_type,
                                       year = ir.budget_year
                                   }).ToList();

            return activityIndData;
        }

        public List<clsIndicators> GetDeflatorValuesForMultipleIndicatorsWithMultipleRegionsAndYears(string userId, List<string> municipalityIds, List<string> IndicatorCodes, bool expenseNeed, bool deflator, List<int> lstYear)
        {
            AuthenticationDBContext authenticationDbContext = _utility.GetAuthenticationContext();

            TenantDBContext dbContext = _utility.GetTenantDBContext();
            int budgetYear = _kostraData.GetBudgetYear(userId);
            var A = Convert.ToDecimal(authenticationDbContext.gco_application_settings.First(x => x.Key == "Deflator_" + (budgetYear - 1)).Value, CultureInfoFactory.CreateCulture("en-US"));

            List<string> deflatoryears = new List<string>();
            foreach (var item in lstYear)
            {
                deflatoryears.Add("Deflator_" + item);
            }

            var B = (from a in authenticationDbContext.gco_application_settings
                     where deflatoryears.Contains(a.Key)
                     select new
                     {
                         year = a.Key.Remove(0, 9),
                         value = a.Value,
                     }).ToList();
            List<clsIndicators> details;

            details = (from kc in dbContext.gko_kostra_data_corp
                       where municipalityIds.Contains(kc.fk_region_code) && lstYear.Contains(kc.year) && IndicatorCodes.Contains(kc.fk_indicator_code)
                       select new clsIndicators
                       {
                           indicatorCode = kc.fk_indicator_code,
                           indicatorValue = kc.indicator_value,
                           RegionCode = kc.fk_region_code,
                           expadjvalue = kc.exp_adj_value,
                           year = kc.year
                       }).ToList();

            foreach (var item in details)
            {
                foreach (var d in B)
                {
                    if (item.year == Convert.ToInt32(d.year))
                    {
                        if (deflator && expenseNeed == false)
                        {
                            item.indicatorValue = ((A / Convert.ToDecimal(d.value, CultureInfoFactory.CreateCulture("en-US"))) * item.indicatorValue);
                        }
                        else if (deflator && expenseNeed)
                        {
                            item.indicatorValue = ((A / Convert.ToDecimal(d.value, CultureInfoFactory.CreateCulture("en-US"))) * item.indicatorValue) + item.expadjvalue;
                        }
                    }
                }
            }

            return details;
        }

        public dynamic GetOrgHierarchy(string userId)
        {
            int budgetYear = _kostraData.GetBudgetYear(userId);
            var orgVersionContent = _utility.GetOrgVersionSpecificContent(userId, _utility.GetForecastPeriod(budgetYear, 1));

            List<tco_org_level> lstOrgLevels = orgVersionContent.lstOrgLevel.OrderBy(y => y.org_level).ToList();

            List<tco_org_hierarchy> lstOrgHierarchy = orgVersionContent.lstOrgHierarchy;

            List<OrgIdLevelHierarchyHelper> businessplanHierarchyList = _utility.GetOrgIdLevelHierarchyStruct(orgVersionContent, userId, false);

            var userSpecificDataSet = (from ol in lstOrgLevels
                                       join orl in businessplanHierarchyList on ol.org_level equals orl.hierarchy_level
                                       select new OrgIdLevelHierarchyHelper
                                       {
                                           hierarchy_level = ol.org_level,
                                           fk_org_id = orl.fk_org_id
                                       }).ToList();

            int suLevel = lstOrgLevels.FirstOrDefault(x => x.su_flag == 1).org_level;

            var lstUserSpecificOrgLevels = (from l in userSpecificDataSet
                                            orderby l.hierarchy_level
                                            select l.hierarchy_level).Distinct().ToList();

            int orgLevel = 1;

            dynamic orgStructure = FormatLevel(lstUserSpecificOrgLevels, lstOrgHierarchy, lstUserSpecificOrgLevels.OrderBy(y => y).FirstOrDefault(), userSpecificDataSet, suLevel, orgLevel, null);
            return orgStructure;
        }

        private JArray FormatLevel(List<int> lstOrgLevels, List<tco_org_hierarchy> lstOrgHierarchy, int orgLevel, List<OrgIdLevelHierarchyHelper> lstUserSpecificDataSet, int sulevel,
         int initialOrgLevel, List<string> orgList = null)
        {
            var levelValues = (from h in lstOrgHierarchy
                               select new
                               {
                                   id = orgLevel == 1 ? h.org_id_1 :
                                        orgLevel == 2 ? h.org_id_2 :
                                        orgLevel == 3 ? h.org_id_3 :
                                        orgLevel == 4 ? h.org_id_4 :
                                        orgLevel == 5 ? h.org_id_5 : "",

                                   name = orgLevel == 1 ? h.org_name_1 :
                                          orgLevel == 2 ? h.org_name_2 :
                                          orgLevel == 3 ? h.org_name_3 :
                                          orgLevel == 4 ? h.org_name_4 :
                                          orgLevel == 5 ? h.org_name_5 : "",

                                   levelNo = orgLevel
                               }).Distinct().OrderBy(x => x.id).ToList();

            var levelItems = new JArray();
            foreach (var l in levelValues)
            {
                if ((orgLevel == 1) || (lstUserSpecificDataSet.Select(x => x.hierarchy_level).Distinct().Contains(orgLevel) && lstUserSpecificDataSet.Select(x => x.fk_org_id).Distinct().Contains(l.id)))
                {
                    var item = new JObject();
                    item.Add("id", l.id);
                    item.Add("text", l.levelNo != 1 ? l.id + " " + l.name : l.name);
                    item.Add("expanded", initialOrgLevel == 1 ? false : true);
                    item.Add("orgLevel", l.levelNo);
                    item.Add("checked", false);
                    var subset = lstOrgHierarchy.Where(x => (l.levelNo == 1 ? x.org_id_1 == l.id && x.org_name_1.Trim().ToLower() == l.name.Trim().ToLower() :
                                                             l.levelNo == 2 ? x.org_id_2 == l.id && x.org_name_2.Trim().ToLower() == l.name.Trim().ToLower() :
                                                             l.levelNo == 3 ? x.org_id_3 == l.id && x.org_name_3.Trim().ToLower() == l.name.Trim().ToLower() :
                                                             l.levelNo == 4 ? x.org_id_4 == l.id && x.org_name_4.Trim().ToLower() == l.name.Trim().ToLower() :
                                                             l.levelNo == 5 ? x.org_id_5 == l.id && x.org_name_5.Trim().ToLower() == l.name.Trim().ToLower() : x.org_id_1 == l.id && x.org_name_1.Trim().ToLower() == l.name.Trim().ToLower())).ToList();

                    if (lstOrgLevels.Any(x => x > orgLevel))
                    {
                        var subItems = FormatLevel(lstOrgLevels, lstOrgHierarchy.Where(x => (orgLevel == 1 ? x.org_id_1 == l.id && x.org_name_1.Trim().ToLower() == l.name.Trim().ToLower() :
                                                                                                            orgLevel == 2 ? x.org_id_2 == l.id && x.org_name_2.Trim().ToLower() == l.name.Trim().ToLower() :
                                                                                                            orgLevel == 3 ? x.org_id_3 == l.id && x.org_name_3.Trim().ToLower() == l.name.Trim().ToLower() :
                                                                                                            orgLevel == 4 ? x.org_id_4 == l.id && x.org_name_4.Trim().ToLower() == l.name.Trim().ToLower() :
                                                                                                            orgLevel == 5 ? x.org_id_5 == l.id && x.org_name_5.Trim().ToLower() == l.name.Trim().ToLower() : x.org_id_1 == l.id && x.org_name_1.Trim().ToLower() == l.name.Trim().ToLower()
                                                                                            )).ToList(), lstOrgLevels.Where(x => x > orgLevel).OrderBy(y => y).FirstOrDefault(),
                                                                                            lstUserSpecificDataSet,
                                                                                            sulevel, initialOrgLevel, orgList);
                        item.Add("items", subItems);
                    }
                    else
                    {
                        item.Add("items", new JArray());
                    }
                    levelItems.Add(item);
                }
            }

            return levelItems;
        }
    }
}