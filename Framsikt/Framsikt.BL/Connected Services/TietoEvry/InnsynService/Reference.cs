//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace TietoEvryInnsynService
{
    
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(OperationalFault))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ImplementationFault))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    public partial class SystemFault : GeointegrasjonFault
    {
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SystemFault))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(OperationalFault))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ImplementationFault))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ApplicationFault))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ValidationFault))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FinderFault))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    public partial class GeointegrasjonFault : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string feilKodeField;
        
        private string feilBeskrivelseField;
        
        private string[] feilDetaljerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string feilKode
        {
            get
            {
                return this.feilKodeField;
            }
            set
            {
                this.feilKodeField = value;
                this.RaisePropertyChanged("feilKode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string feilBeskrivelse
        {
            get
            {
                return this.feilBeskrivelseField;
            }
            set
            {
                this.feilBeskrivelseField = value;
                this.RaisePropertyChanged("feilBeskrivelse");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=2)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", IsNullable=false)]
        public string[] feilDetaljer
        {
            get
            {
                return this.feilDetaljerField;
            }
            set
            {
                this.feilDetaljerField = value;
                this.RaisePropertyChanged("feilDetaljer");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PersonidentifikatorType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Tilgangsrestriksjon))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SkjermingsHjemmel))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SkjermingOpphorerAksjon))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Saksstatus))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Mappetype))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Korrespondanseparttype))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Klassifikasjonssystem))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Kassasjonsvedtak))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Journalstatus))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Journalposttype))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Journalenhet))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Informasjonstype))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Forsendelsesmaate))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Dokumentmedium))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Avskrivningsmaate))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Arkivdel))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SakspartRolle))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Variantformat))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(TilknyttetRegistreringSom))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Format))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Dokumentstatus))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Dokumenttype))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Landkode))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(EnkelAdressetype))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(KoordinatsystemKode))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Kodeliste/xml.schema/2012.01.31")]
    public partial class Kode : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string kodeverdiField;
        
        private string kodebeskrivelseField;
        
        private bool erGyldigField;
        
        private bool erGyldigFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string kodeverdi
        {
            get
            {
                return this.kodeverdiField;
            }
            set
            {
                this.kodeverdiField = value;
                this.RaisePropertyChanged("kodeverdi");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string kodebeskrivelse
        {
            get
            {
                return this.kodebeskrivelseField;
            }
            set
            {
                this.kodebeskrivelseField = value;
                this.RaisePropertyChanged("kodebeskrivelse");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public bool erGyldig
        {
            get
            {
                return this.erGyldigField;
            }
            set
            {
                this.erGyldigField = value;
                this.RaisePropertyChanged("erGyldig");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool erGyldigSpecified
        {
            get
            {
                return this.erGyldigFieldSpecified;
            }
            set
            {
                this.erGyldigFieldSpecified = value;
                this.RaisePropertyChanged("erGyldigSpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Kontakt/xml.schema/2012.01.31")]
    public partial class PersonidentifikatorType : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Tilgangsrestriksjon : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class SkjermingsHjemmel : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class SkjermingOpphorerAksjon : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Saksstatus : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Mappetype : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Korrespondanseparttype : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Klassifikasjonssystem : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Kassasjonsvedtak : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Journalstatus : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Journalposttype : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Journalenhet : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Informasjonstype : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Forsendelsesmaate : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Dokumentmedium : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Avskrivningsmaate : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Arkivdel : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class SakspartRolle : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    public partial class Variantformat : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    public partial class TilknyttetRegistreringSom : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    public partial class Format : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    public partial class Dokumentstatus : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    public partial class Dokumenttype : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    public partial class Landkode : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    public partial class EnkelAdressetype : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31")]
    public partial class KoordinatsystemKode : Kode
    {
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PlanKontekst))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MatrikkelKontekst))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ArkivKontekst))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    public partial class Kontekst : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string spraakField;
        
        private string klientnavnField;
        
        private string klientversjonField;
        
        private string systemversjonField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string spraak
        {
            get
            {
                return this.spraakField;
            }
            set
            {
                this.spraakField = value;
                this.RaisePropertyChanged("spraak");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string klientnavn
        {
            get
            {
                return this.klientnavnField;
            }
            set
            {
                this.klientnavnField = value;
                this.RaisePropertyChanged("klientnavn");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string klientversjon
        {
            get
            {
                return this.klientversjonField;
            }
            set
            {
                this.klientversjonField = value;
                this.RaisePropertyChanged("klientversjon");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string systemversjon
        {
            get
            {
                return this.systemversjonField;
            }
            set
            {
                this.systemversjonField = value;
                this.RaisePropertyChanged("systemversjon");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    public partial class PlanKontekst : Kontekst
    {
        
        private KoordinatsystemKode koordinatsystemField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public KoordinatsystemKode koordinatsystem
        {
            get
            {
                return this.koordinatsystemField;
            }
            set
            {
                this.koordinatsystemField = value;
                this.RaisePropertyChanged("koordinatsystem");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    public partial class MatrikkelKontekst : Kontekst
    {
        
        private KoordinatsystemKode koordinatsystemField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public KoordinatsystemKode koordinatsystem
        {
            get
            {
                return this.koordinatsystemField;
            }
            set
            {
                this.koordinatsystemField = value;
                this.RaisePropertyChanged("koordinatsystem");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    public partial class ArkivKontekst : Kontekst
    {
        
        private KoordinatsystemKode koordinatsystemField;
        
        private string referanseoppsettField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public KoordinatsystemKode koordinatsystem
        {
            get
            {
                return this.koordinatsystemField;
            }
            set
            {
                this.koordinatsystemField = value;
                this.RaisePropertyChanged("koordinatsystem");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string referanseoppsett
        {
            get
            {
                return this.referanseoppsettField;
            }
            set
            {
                this.referanseoppsettField = value;
                this.RaisePropertyChanged("referanseoppsett");
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ValidationFault))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FinderFault))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    public partial class ApplicationFault : GeointegrasjonFault
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    public partial class ValidationFault : ApplicationFault
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    public partial class FinderFault : ApplicationFault
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    public partial class OperationalFault : SystemFault
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    public partial class ImplementationFault : SystemFault
    {
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", ConfigurationName="TietoEvryInnsynService.ArkivInnsynPort")]
    public interface ArkivInnsynPort
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentKodeliste", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentKodeliste", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentKodeliste", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentKodeliste", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentKodeliste", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentKodeliste", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentKodeliste", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        TietoEvryInnsynService.HentKodelisteResponse HentKodeliste(TietoEvryInnsynService.HentKodeliste request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentKodeliste", ReplyAction="*")]
        System.Threading.Tasks.Task<TietoEvryInnsynService.HentKodelisteResponse> HentKodelisteAsync(TietoEvryInnsynService.HentKodeliste request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentFil", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentFil", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentFil", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentFil", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentFil", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentFil", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentFil", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        TietoEvryInnsynService.HentFilResponse HentFil(TietoEvryInnsynService.HentFil request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#HentFil", ReplyAction="*")]
        System.Threading.Tasks.Task<TietoEvryInnsynService.HentFilResponse> HentFilAsync(TietoEvryInnsynService.HentFil request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenttyper", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenttyper", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenttyper", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenttyper", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenttyper", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenttyper", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenttyper", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        TietoEvryInnsynService.FinnDokumenttyperResponse FinnDokumenttyper(TietoEvryInnsynService.FinnDokumenttyper request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenttyper", ReplyAction="*")]
        System.Threading.Tasks.Task<TietoEvryInnsynService.FinnDokumenttyperResponse> FinnDokumenttyperAsync(TietoEvryInnsynService.FinnDokumenttyper request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittNoekkel", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittNoekkel", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittNoekkel", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittNoekkel", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittNoekkel", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittNoekkel", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittNoekkel", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        TietoEvryInnsynService.FinnJournalposterGittNoekkelResponse FinnJournalposterGittNoekkel(TietoEvryInnsynService.FinnJournalposterGittNoekkel request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittNoekkel", ReplyAction="*")]
        System.Threading.Tasks.Task<TietoEvryInnsynService.FinnJournalposterGittNoekkelResponse> FinnJournalposterGittNoekkelAsync(TietoEvryInnsynService.FinnJournalposterGittNoekkel request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittSaksmappeNoekkel", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittSaksmappeNoekkel", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittSaksmappeNoekkel", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittSaksmappeNoekkel", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittSaksmappeNoekkel", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittSaksmappeNoekkel", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittSaksmappeNoekkel", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        TietoEvryInnsynService.FinnJournalposterGittSaksmappeNoekkelResponse FinnJournalposterGittSaksmappeNoekkel(TietoEvryInnsynService.FinnJournalposterGittSaksmappeNoekkel request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposterG" +
            "ittSaksmappeNoekkel", ReplyAction="*")]
        System.Threading.Tasks.Task<TietoEvryInnsynService.FinnJournalposterGittSaksmappeNoekkelResponse> FinnJournalposterGittSaksmappeNoekkelAsync(TietoEvryInnsynService.FinnJournalposterGittSaksmappeNoekkel request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposter", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposter", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposter", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposter", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposter", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposter", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposter", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        TietoEvryInnsynService.FinnJournalposterResponse FinnJournalposter(TietoEvryInnsynService.FinnJournalposter request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnJournalposter", ReplyAction="*")]
        System.Threading.Tasks.Task<TietoEvryInnsynService.FinnJournalposterResponse> FinnJournalposterAsync(TietoEvryInnsynService.FinnJournalposter request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapperGitt" +
            "Noekkel", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapperGitt" +
            "Noekkel", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapperGitt" +
            "Noekkel", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapperGitt" +
            "Noekkel", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapperGitt" +
            "Noekkel", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapperGitt" +
            "Noekkel", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapperGitt" +
            "Noekkel", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        TietoEvryInnsynService.FinnSaksmapperGittNoekkelResponse FinnSaksmapperGittNoekkel(TietoEvryInnsynService.FinnSaksmapperGittNoekkel request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapperGitt" +
            "Noekkel", ReplyAction="*")]
        System.Threading.Tasks.Task<TietoEvryInnsynService.FinnSaksmapperGittNoekkelResponse> FinnSaksmapperGittNoekkelAsync(TietoEvryInnsynService.FinnSaksmapperGittNoekkel request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapper", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapper", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapper", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapper", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapper", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapper", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapper", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        TietoEvryInnsynService.FinnSaksmapperResponse FinnSaksmapper(TietoEvryInnsynService.FinnSaksmapper request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnSaksmapper", ReplyAction="*")]
        System.Threading.Tasks.Task<TietoEvryInnsynService.FinnSaksmapperResponse> FinnSaksmapperAsync(TietoEvryInnsynService.FinnSaksmapper request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Journalpostnoekkel", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Journalpostnoekkel", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Journalpostnoekkel", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Journalpostnoekkel", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Journalpostnoekkel", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Journalpostnoekkel", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Journalpostnoekkel", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        TietoEvryInnsynService.FinnDokumenterGittJournalpostnoekkelResponse FinnDokumenterGittJournalpostnoekkel(TietoEvryInnsynService.FinnDokumenterGittJournalpostnoekkel request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Journalpostnoekkel", ReplyAction="*")]
        System.Threading.Tasks.Task<TietoEvryInnsynService.FinnDokumenterGittJournalpostnoekkelResponse> FinnDokumenterGittJournalpostnoekkelAsync(TietoEvryInnsynService.FinnDokumenterGittJournalpostnoekkel request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Saksnoekkel", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Saksnoekkel", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Saksnoekkel", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Saksnoekkel", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Saksnoekkel", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Saksnoekkel", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Saksnoekkel", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        TietoEvryInnsynService.FinnDokumenterGittSaksnoekkelResponse FinnDokumenterGittSaksnoekkel(TietoEvryInnsynService.FinnDokumenterGittSaksnoekkel request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenterGitt" +
            "Saksnoekkel", ReplyAction="*")]
        System.Threading.Tasks.Task<TietoEvryInnsynService.FinnDokumenterGittSaksnoekkelResponse> FinnDokumenterGittSaksnoekkelAsync(TietoEvryInnsynService.FinnDokumenterGittSaksnoekkel request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenter", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenter", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenter", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenter", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenter", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenter", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(TietoEvryInnsynService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenter", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        TietoEvryInnsynService.FinnDokumenterResponse FinnDokumenter(TietoEvryInnsynService.FinnDokumenter request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31/#FinnDokumenter", ReplyAction="*")]
        System.Threading.Tasks.Task<TietoEvryInnsynService.FinnDokumenterResponse> FinnDokumenterAsync(TietoEvryInnsynService.FinnDokumenter request);
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="HentKodeliste", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class HentKodeliste
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public string kodelistenavn;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=1)]
        public TietoEvryInnsynService.ArkivKontekst kontekst;
        
        public HentKodeliste()
        {
        }
        
        public HentKodeliste(string kodelistenavn, TietoEvryInnsynService.ArkivKontekst kontekst)
        {
            this.kodelistenavn = kodelistenavn;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="HentKodelisteResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class HentKodelisteResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Felles/Kodeliste/xml.schema/2012.01.31", IsNullable=false)]
        public TietoEvryInnsynService.Kode[] @return;
        
        public HentKodelisteResponse()
        {
        }
        
        public HentKodelisteResponse(TietoEvryInnsynService.Kode[] @return)
        {
            this.@return = @return;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class SystemID : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string idField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
                this.RaisePropertyChanged("id");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Filreferanse))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Filinnhold))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    public partial class Fil : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string filnavnField;
        
        private string mimeTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string filnavn
        {
            get
            {
                return this.filnavnField;
            }
            set
            {
                this.filnavnField = value;
                this.RaisePropertyChanged("filnavn");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string mimeType
        {
            get
            {
                return this.mimeTypeField;
            }
            set
            {
                this.mimeTypeField = value;
                this.RaisePropertyChanged("mimeType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    public partial class Filreferanse : Fil
    {
        
        private string uriField;
        
        private string kvitteringUriField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="anyURI", Order=0)]
        public string uri
        {
            get
            {
                return this.uriField;
            }
            set
            {
                this.uriField = value;
                this.RaisePropertyChanged("uri");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="anyURI", Order=1)]
        public string kvitteringUri
        {
            get
            {
                return this.kvitteringUriField;
            }
            set
            {
                this.kvitteringUriField = value;
                this.RaisePropertyChanged("kvitteringUri");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    public partial class Filinnhold : Fil
    {
        
        private byte[] base64Field;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=0)]
        public byte[] base64
        {
            get
            {
                return this.base64Field;
            }
            set
            {
                this.base64Field = value;
                this.RaisePropertyChanged("base64");
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="HentFil", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class HentFil
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public TietoEvryInnsynService.SystemID systemid;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=1)]
        public TietoEvryInnsynService.ArkivKontekst kontekst;
        
        public HentFil()
        {
        }
        
        public HentFil(TietoEvryInnsynService.SystemID systemid, TietoEvryInnsynService.ArkivKontekst kontekst)
        {
            this.systemid = systemid;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="HentFilResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class HentFilResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public TietoEvryInnsynService.Fil @return;
        
        public HentFilResponse()
        {
        }
        
        public HentFilResponse(TietoEvryInnsynService.Fil @return)
        {
            this.@return = @return;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SakSystemId))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SakEksternNoekkel))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Saksnummer))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Felles/xml.schema/2012.01.31")]
    public partial class Saksnoekkel : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class SakSystemId : Saksnoekkel
    {
        
        private SystemID systemIDField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public SystemID systemID
        {
            get
            {
                return this.systemIDField;
            }
            set
            {
                this.systemIDField = value;
                this.RaisePropertyChanged("systemID");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class SakEksternNoekkel : Saksnoekkel
    {
        
        private EksternNoekkel eksternnoekkelField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public EksternNoekkel eksternnoekkel
        {
            get
            {
                return this.eksternnoekkelField;
            }
            set
            {
                this.eksternnoekkelField = value;
                this.RaisePropertyChanged("eksternnoekkel");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class EksternNoekkel : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string fagsystemField;
        
        private string noekkelField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string fagsystem
        {
            get
            {
                return this.fagsystemField;
            }
            set
            {
                this.fagsystemField = value;
                this.RaisePropertyChanged("fagsystem");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string noekkel
        {
            get
            {
                return this.noekkelField;
            }
            set
            {
                this.noekkelField = value;
                this.RaisePropertyChanged("noekkel");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Felles/xml.schema/2012.01.31")]
    public partial class Saksnummer : Saksnoekkel
    {
        
        private string saksaarField;
        
        private string sakssekvensnummerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=0)]
        public string saksaar
        {
            get
            {
                return this.saksaarField;
            }
            set
            {
                this.saksaarField = value;
                this.RaisePropertyChanged("saksaar");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=1)]
        public string sakssekvensnummer
        {
            get
            {
                return this.sakssekvensnummerField;
            }
            set
            {
                this.sakssekvensnummerField = value;
                this.RaisePropertyChanged("sakssekvensnummer");
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="FinnDokumenttyper", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class FinnDokumenttyper
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public TietoEvryInnsynService.Saksnoekkel saksnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=1)]
        public TietoEvryInnsynService.ArkivKontekst kontekst;
        
        public FinnDokumenttyper()
        {
        }
        
        public FinnDokumenttyper(TietoEvryInnsynService.Saksnoekkel saksnokkel, TietoEvryInnsynService.ArkivKontekst kontekst)
        {
            this.saksnokkel = saksnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="FinnDokumenttyperResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class FinnDokumenttyperResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31", IsNullable=false)]
        public TietoEvryInnsynService.Dokumenttype[] @return;
        
        public FinnDokumenttyperResponse()
        {
        }
        
        public FinnDokumenttyperResponse(TietoEvryInnsynService.Dokumenttype[] @return)
        {
            this.@return = @return;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(JournpostSystemID))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(JournpostEksternNoekkel))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Journalnummer))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Dokumentnummer))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Journpostnoekkel : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class JournpostSystemID : Journpostnoekkel
    {
        
        private SystemID systemIDField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public SystemID systemID
        {
            get
            {
                return this.systemIDField;
            }
            set
            {
                this.systemIDField = value;
                this.RaisePropertyChanged("systemID");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class JournpostEksternNoekkel : Journpostnoekkel
    {
        
        private EksternNoekkel eksternnoekkelField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public EksternNoekkel eksternnoekkel
        {
            get
            {
                return this.eksternnoekkelField;
            }
            set
            {
                this.eksternnoekkelField = value;
                this.RaisePropertyChanged("eksternnoekkel");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Journalnummer : Journpostnoekkel
    {
        
        private string journalaarField;
        
        private string journalsekvensnummerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=0)]
        public string journalaar
        {
            get
            {
                return this.journalaarField;
            }
            set
            {
                this.journalaarField = value;
                this.RaisePropertyChanged("journalaar");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=1)]
        public string journalsekvensnummer
        {
            get
            {
                return this.journalsekvensnummerField;
            }
            set
            {
                this.journalsekvensnummerField = value;
                this.RaisePropertyChanged("journalsekvensnummer");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Dokumentnummer : Journpostnoekkel
    {
        
        private string saksaarField;
        
        private string sakssekvensnummerField;
        
        private string journalpostnummerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=0)]
        public string saksaar
        {
            get
            {
                return this.saksaarField;
            }
            set
            {
                this.saksaarField = value;
                this.RaisePropertyChanged("saksaar");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=1)]
        public string sakssekvensnummer
        {
            get
            {
                return this.sakssekvensnummerField;
            }
            set
            {
                this.sakssekvensnummerField = value;
                this.RaisePropertyChanged("sakssekvensnummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=2)]
        public string journalpostnummer
        {
            get
            {
                return this.journalpostnummerField;
            }
            set
            {
                this.journalpostnummerField = value;
                this.RaisePropertyChanged("journalpostnummer");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Journalpost : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string systemIDField;
        
        private Journalnummer journalnummerField;
        
        private string journalpostnummerField;
        
        private System.DateTime journaldatoField;
        
        private bool journaldatoFieldSpecified;
        
        private Journalposttype journalposttypeField;
        
        private System.DateTime dokumentetsDatoField;
        
        private bool dokumentetsDatoFieldSpecified;
        
        private Journalstatus journalstatusField;
        
        private string tittelField;
        
        private bool skjermetTittelField;
        
        private bool skjermetTittelFieldSpecified;
        
        private System.DateTime forfallsdatoField;
        
        private bool forfallsdatoFieldSpecified;
        
        private Skjerming skjermingField;
        
        private Arkivdel referanseArkivdelField;
        
        private string tilleggskodeField;
        
        private string antallVedleggField;
        
        private string offentligTittelField;
        
        private Saksnummer saksnrField;
        
        private string tilgangsgruppeNavnField;
        
        private SakSystemId referanseSakSystemIDField;
        
        private Korrespondansepart[] korrespondansepartField;
        
        private EksternNoekkel referanseEksternNoekkelField;
        
        private EksternNoekkel referanseMappeEksternNoekkelField;
        
        private Avskrivning[] referanseAvskrivningerField;
        
        private Merknad[] merknaderField;
        
        private Tilleggsinformasjon[] tilleggsinformasjonField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string systemID
        {
            get
            {
                return this.systemIDField;
            }
            set
            {
                this.systemIDField = value;
                this.RaisePropertyChanged("systemID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public Journalnummer journalnummer
        {
            get
            {
                return this.journalnummerField;
            }
            set
            {
                this.journalnummerField = value;
                this.RaisePropertyChanged("journalnummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string journalpostnummer
        {
            get
            {
                return this.journalpostnummerField;
            }
            set
            {
                this.journalpostnummerField = value;
                this.RaisePropertyChanged("journalpostnummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public System.DateTime journaldato
        {
            get
            {
                return this.journaldatoField;
            }
            set
            {
                this.journaldatoField = value;
                this.RaisePropertyChanged("journaldato");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool journaldatoSpecified
        {
            get
            {
                return this.journaldatoFieldSpecified;
            }
            set
            {
                this.journaldatoFieldSpecified = value;
                this.RaisePropertyChanged("journaldatoSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public Journalposttype journalposttype
        {
            get
            {
                return this.journalposttypeField;
            }
            set
            {
                this.journalposttypeField = value;
                this.RaisePropertyChanged("journalposttype");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public System.DateTime dokumentetsDato
        {
            get
            {
                return this.dokumentetsDatoField;
            }
            set
            {
                this.dokumentetsDatoField = value;
                this.RaisePropertyChanged("dokumentetsDato");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool dokumentetsDatoSpecified
        {
            get
            {
                return this.dokumentetsDatoFieldSpecified;
            }
            set
            {
                this.dokumentetsDatoFieldSpecified = value;
                this.RaisePropertyChanged("dokumentetsDatoSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public Journalstatus journalstatus
        {
            get
            {
                return this.journalstatusField;
            }
            set
            {
                this.journalstatusField = value;
                this.RaisePropertyChanged("journalstatus");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public string tittel
        {
            get
            {
                return this.tittelField;
            }
            set
            {
                this.tittelField = value;
                this.RaisePropertyChanged("tittel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public bool skjermetTittel
        {
            get
            {
                return this.skjermetTittelField;
            }
            set
            {
                this.skjermetTittelField = value;
                this.RaisePropertyChanged("skjermetTittel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool skjermetTittelSpecified
        {
            get
            {
                return this.skjermetTittelFieldSpecified;
            }
            set
            {
                this.skjermetTittelFieldSpecified = value;
                this.RaisePropertyChanged("skjermetTittelSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public System.DateTime forfallsdato
        {
            get
            {
                return this.forfallsdatoField;
            }
            set
            {
                this.forfallsdatoField = value;
                this.RaisePropertyChanged("forfallsdato");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool forfallsdatoSpecified
        {
            get
            {
                return this.forfallsdatoFieldSpecified;
            }
            set
            {
                this.forfallsdatoFieldSpecified = value;
                this.RaisePropertyChanged("forfallsdatoSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=10)]
        public Skjerming skjerming
        {
            get
            {
                return this.skjermingField;
            }
            set
            {
                this.skjermingField = value;
                this.RaisePropertyChanged("skjerming");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=11)]
        public Arkivdel referanseArkivdel
        {
            get
            {
                return this.referanseArkivdelField;
            }
            set
            {
                this.referanseArkivdelField = value;
                this.RaisePropertyChanged("referanseArkivdel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=12)]
        public string tilleggskode
        {
            get
            {
                return this.tilleggskodeField;
            }
            set
            {
                this.tilleggskodeField = value;
                this.RaisePropertyChanged("tilleggskode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=13)]
        public string antallVedlegg
        {
            get
            {
                return this.antallVedleggField;
            }
            set
            {
                this.antallVedleggField = value;
                this.RaisePropertyChanged("antallVedlegg");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=14)]
        public string offentligTittel
        {
            get
            {
                return this.offentligTittelField;
            }
            set
            {
                this.offentligTittelField = value;
                this.RaisePropertyChanged("offentligTittel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=15)]
        public Saksnummer saksnr
        {
            get
            {
                return this.saksnrField;
            }
            set
            {
                this.saksnrField = value;
                this.RaisePropertyChanged("saksnr");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=16)]
        public string tilgangsgruppeNavn
        {
            get
            {
                return this.tilgangsgruppeNavnField;
            }
            set
            {
                this.tilgangsgruppeNavnField = value;
                this.RaisePropertyChanged("tilgangsgruppeNavn");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=17)]
        public SakSystemId referanseSakSystemID
        {
            get
            {
                return this.referanseSakSystemIDField;
            }
            set
            {
                this.referanseSakSystemIDField = value;
                this.RaisePropertyChanged("referanseSakSystemID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=18)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", IsNullable=false)]
        public Korrespondansepart[] korrespondansepart
        {
            get
            {
                return this.korrespondansepartField;
            }
            set
            {
                this.korrespondansepartField = value;
                this.RaisePropertyChanged("korrespondansepart");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=19)]
        public EksternNoekkel referanseEksternNoekkel
        {
            get
            {
                return this.referanseEksternNoekkelField;
            }
            set
            {
                this.referanseEksternNoekkelField = value;
                this.RaisePropertyChanged("referanseEksternNoekkel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=20)]
        public EksternNoekkel referanseMappeEksternNoekkel
        {
            get
            {
                return this.referanseMappeEksternNoekkelField;
            }
            set
            {
                this.referanseMappeEksternNoekkelField = value;
                this.RaisePropertyChanged("referanseMappeEksternNoekkel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=21)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", IsNullable=false)]
        public Avskrivning[] referanseAvskrivninger
        {
            get
            {
                return this.referanseAvskrivningerField;
            }
            set
            {
                this.referanseAvskrivningerField = value;
                this.RaisePropertyChanged("referanseAvskrivninger");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=22)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", IsNullable=false)]
        public Merknad[] merknader
        {
            get
            {
                return this.merknaderField;
            }
            set
            {
                this.merknaderField = value;
                this.RaisePropertyChanged("merknader");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=23)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", IsNullable=false)]
        public Tilleggsinformasjon[] tilleggsinformasjon
        {
            get
            {
                return this.tilleggsinformasjonField;
            }
            set
            {
                this.tilleggsinformasjonField = value;
                this.RaisePropertyChanged("tilleggsinformasjon");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Skjerming : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private Tilgangsrestriksjon tilgangsrestriksjonField;
        
        private string skjermingshjemmelField;
        
        private System.DateTime skjermingOpphoererDatoField;
        
        private bool skjermingOpphoererDatoFieldSpecified;
        
        private SkjermingOpphorerAksjon skjermingOpphoererAksjonField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public Tilgangsrestriksjon tilgangsrestriksjon
        {
            get
            {
                return this.tilgangsrestriksjonField;
            }
            set
            {
                this.tilgangsrestriksjonField = value;
                this.RaisePropertyChanged("tilgangsrestriksjon");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string skjermingshjemmel
        {
            get
            {
                return this.skjermingshjemmelField;
            }
            set
            {
                this.skjermingshjemmelField = value;
                this.RaisePropertyChanged("skjermingshjemmel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public System.DateTime skjermingOpphoererDato
        {
            get
            {
                return this.skjermingOpphoererDatoField;
            }
            set
            {
                this.skjermingOpphoererDatoField = value;
                this.RaisePropertyChanged("skjermingOpphoererDato");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool skjermingOpphoererDatoSpecified
        {
            get
            {
                return this.skjermingOpphoererDatoFieldSpecified;
            }
            set
            {
                this.skjermingOpphoererDatoFieldSpecified = value;
                this.RaisePropertyChanged("skjermingOpphoererDatoSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public SkjermingOpphorerAksjon skjermingOpphoererAksjon
        {
            get
            {
                return this.skjermingOpphoererAksjonField;
            }
            set
            {
                this.skjermingOpphoererAksjonField = value;
                this.RaisePropertyChanged("skjermingOpphoererAksjon");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Korrespondansepart : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string systemIDField;
        
        private Korrespondanseparttype korrespondanseparttypeField;
        
        private string behandlingsansvarligField;
        
        private bool skjermetKorrespondansepartField;
        
        private bool skjermetKorrespondansepartFieldSpecified;
        
        private string kortnavnField;
        
        private string deresReferanseField;
        
        private Journalenhet journalenhetField;
        
        private System.DateTime fristBesvarelseField;
        
        private bool fristBesvarelseFieldSpecified;
        
        private Forsendelsesmaate forsendelsesmaateField;
        
        private string administrativEnhetInitField;
        
        private string administrativEnhetField;
        
        private string saksbehandlerInitField;
        
        private string saksbehandlerField;
        
        private Kontakt kontaktField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string systemID
        {
            get
            {
                return this.systemIDField;
            }
            set
            {
                this.systemIDField = value;
                this.RaisePropertyChanged("systemID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public Korrespondanseparttype korrespondanseparttype
        {
            get
            {
                return this.korrespondanseparttypeField;
            }
            set
            {
                this.korrespondanseparttypeField = value;
                this.RaisePropertyChanged("korrespondanseparttype");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string behandlingsansvarlig
        {
            get
            {
                return this.behandlingsansvarligField;
            }
            set
            {
                this.behandlingsansvarligField = value;
                this.RaisePropertyChanged("behandlingsansvarlig");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public bool skjermetKorrespondansepart
        {
            get
            {
                return this.skjermetKorrespondansepartField;
            }
            set
            {
                this.skjermetKorrespondansepartField = value;
                this.RaisePropertyChanged("skjermetKorrespondansepart");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool skjermetKorrespondansepartSpecified
        {
            get
            {
                return this.skjermetKorrespondansepartFieldSpecified;
            }
            set
            {
                this.skjermetKorrespondansepartFieldSpecified = value;
                this.RaisePropertyChanged("skjermetKorrespondansepartSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string kortnavn
        {
            get
            {
                return this.kortnavnField;
            }
            set
            {
                this.kortnavnField = value;
                this.RaisePropertyChanged("kortnavn");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string deresReferanse
        {
            get
            {
                return this.deresReferanseField;
            }
            set
            {
                this.deresReferanseField = value;
                this.RaisePropertyChanged("deresReferanse");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public Journalenhet journalenhet
        {
            get
            {
                return this.journalenhetField;
            }
            set
            {
                this.journalenhetField = value;
                this.RaisePropertyChanged("journalenhet");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public System.DateTime fristBesvarelse
        {
            get
            {
                return this.fristBesvarelseField;
            }
            set
            {
                this.fristBesvarelseField = value;
                this.RaisePropertyChanged("fristBesvarelse");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool fristBesvarelseSpecified
        {
            get
            {
                return this.fristBesvarelseFieldSpecified;
            }
            set
            {
                this.fristBesvarelseFieldSpecified = value;
                this.RaisePropertyChanged("fristBesvarelseSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public Forsendelsesmaate forsendelsesmaate
        {
            get
            {
                return this.forsendelsesmaateField;
            }
            set
            {
                this.forsendelsesmaateField = value;
                this.RaisePropertyChanged("forsendelsesmaate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public string administrativEnhetInit
        {
            get
            {
                return this.administrativEnhetInitField;
            }
            set
            {
                this.administrativEnhetInitField = value;
                this.RaisePropertyChanged("administrativEnhetInit");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=10)]
        public string administrativEnhet
        {
            get
            {
                return this.administrativEnhetField;
            }
            set
            {
                this.administrativEnhetField = value;
                this.RaisePropertyChanged("administrativEnhet");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=11)]
        public string saksbehandlerInit
        {
            get
            {
                return this.saksbehandlerInitField;
            }
            set
            {
                this.saksbehandlerInitField = value;
                this.RaisePropertyChanged("saksbehandlerInit");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=12)]
        public string saksbehandler
        {
            get
            {
                return this.saksbehandlerField;
            }
            set
            {
                this.saksbehandlerField = value;
                this.RaisePropertyChanged("saksbehandler");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=13)]
        public Kontakt Kontakt
        {
            get
            {
                return this.kontaktField;
            }
            set
            {
                this.kontaktField = value;
                this.RaisePropertyChanged("Kontakt");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Person))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Organisasjon))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Kontakt/xml.schema/2012.01.31")]
    public partial class Kontakt : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string navnField;
        
        private EnkelAdresse[] adresserField;
        
        private ElektroniskAdresse[] elektroniskeAdresserField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string navn
        {
            get
            {
                return this.navnField;
            }
            set
            {
                this.navnField = value;
                this.RaisePropertyChanged("navn");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=1)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31", IsNullable=false)]
        public EnkelAdresse[] adresser
        {
            get
            {
                return this.adresserField;
            }
            set
            {
                this.adresserField = value;
                this.RaisePropertyChanged("adresser");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=2)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31", IsNullable=false)]
        public ElektroniskAdresse[] elektroniskeAdresser
        {
            get
            {
                return this.elektroniskeAdresserField;
            }
            set
            {
                this.elektroniskeAdresserField = value;
                this.RaisePropertyChanged("elektroniskeAdresser");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    public partial class EnkelAdresse : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private EnkelAdressetype adressetypeField;
        
        private string adresselinje1Field;
        
        private string adresselinje2Field;
        
        private PostadministrativeOmraader postadresseField;
        
        private Landkode landkodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public EnkelAdressetype adressetype
        {
            get
            {
                return this.adressetypeField;
            }
            set
            {
                this.adressetypeField = value;
                this.RaisePropertyChanged("adressetype");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string adresselinje1
        {
            get
            {
                return this.adresselinje1Field;
            }
            set
            {
                this.adresselinje1Field = value;
                this.RaisePropertyChanged("adresselinje1");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string adresselinje2
        {
            get
            {
                return this.adresselinje2Field;
            }
            set
            {
                this.adresselinje2Field = value;
                this.RaisePropertyChanged("adresselinje2");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public PostadministrativeOmraader postadresse
        {
            get
            {
                return this.postadresseField;
            }
            set
            {
                this.postadresseField = value;
                this.RaisePropertyChanged("postadresse");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public Landkode landkode
        {
            get
            {
                return this.landkodeField;
            }
            set
            {
                this.landkodeField = value;
                this.RaisePropertyChanged("landkode");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    public partial class PostadministrativeOmraader : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string postnummerField;
        
        private string poststedField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string postnummer
        {
            get
            {
                return this.postnummerField;
            }
            set
            {
                this.postnummerField = value;
                this.RaisePropertyChanged("postnummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string poststed
        {
            get
            {
                return this.poststedField;
            }
            set
            {
                this.poststedField = value;
                this.RaisePropertyChanged("poststed");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Meldingsboks))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Telefon))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Faks))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Epost))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    public partial class ElektroniskAdresse : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    public partial class Meldingsboks : ElektroniskAdresse
    {
        
        private string tilbyderField;
        
        private string meldingsboksadresseField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string tilbyder
        {
            get
            {
                return this.tilbyderField;
            }
            set
            {
                this.tilbyderField = value;
                this.RaisePropertyChanged("tilbyder");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string meldingsboksadresse
        {
            get
            {
                return this.meldingsboksadresseField;
            }
            set
            {
                this.meldingsboksadresseField = value;
                this.RaisePropertyChanged("meldingsboksadresse");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    public partial class Telefon : ElektroniskAdresse
    {
        
        private string telefonnummerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string telefonnummer
        {
            get
            {
                return this.telefonnummerField;
            }
            set
            {
                this.telefonnummerField = value;
                this.RaisePropertyChanged("telefonnummer");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    public partial class Faks : ElektroniskAdresse
    {
        
        private string faksnummerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string faksnummer
        {
            get
            {
                return this.faksnummerField;
            }
            set
            {
                this.faksnummerField = value;
                this.RaisePropertyChanged("faksnummer");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    public partial class Epost : ElektroniskAdresse
    {
        
        private string epostadresseField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string epostadresse
        {
            get
            {
                return this.epostadresseField;
            }
            set
            {
                this.epostadresseField = value;
                this.RaisePropertyChanged("epostadresse");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Kontakt/xml.schema/2012.01.31")]
    public partial class Person : Kontakt
    {
        
        private Personidentifikator personidField;
        
        private string etternavnField;
        
        private string fornavnField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public Personidentifikator personid
        {
            get
            {
                return this.personidField;
            }
            set
            {
                this.personidField = value;
                this.RaisePropertyChanged("personid");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string etternavn
        {
            get
            {
                return this.etternavnField;
            }
            set
            {
                this.etternavnField = value;
                this.RaisePropertyChanged("etternavn");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string fornavn
        {
            get
            {
                return this.fornavnField;
            }
            set
            {
                this.fornavnField = value;
                this.RaisePropertyChanged("fornavn");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Kontakt/xml.schema/2012.01.31")]
    public partial class Personidentifikator : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string personidentifikatorNrField;
        
        private PersonidentifikatorType personidentifikatorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string personidentifikatorNr
        {
            get
            {
                return this.personidentifikatorNrField;
            }
            set
            {
                this.personidentifikatorNrField = value;
                this.RaisePropertyChanged("personidentifikatorNr");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public PersonidentifikatorType personidentifikatorType
        {
            get
            {
                return this.personidentifikatorTypeField;
            }
            set
            {
                this.personidentifikatorTypeField = value;
                this.RaisePropertyChanged("personidentifikatorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Kontakt/xml.schema/2012.01.31")]
    public partial class Organisasjon : Kontakt
    {
        
        private string organisasjonsnummerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string organisasjonsnummer
        {
            get
            {
                return this.organisasjonsnummerField;
            }
            set
            {
                this.organisasjonsnummerField = value;
                this.RaisePropertyChanged("organisasjonsnummer");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Avskrivning : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string systemIDField;
        
        private System.DateTime avskrivningsdatoField;
        
        private bool avskrivningsdatoFieldSpecified;
        
        private string avskrevetAvField;
        
        private Avskrivningsmaate avskrivningsmaateField;
        
        private Journalnummer referanseAvskriverJournalnummerField;
        
        private Journalnummer referanseAvskrivesAvJournalnummerField;
        
        private EksternNoekkel referanseAvskriverEksternNoekkelField;
        
        private EksternNoekkel referanseAvskrivesAvEksternNoekkelField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string systemID
        {
            get
            {
                return this.systemIDField;
            }
            set
            {
                this.systemIDField = value;
                this.RaisePropertyChanged("systemID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public System.DateTime avskrivningsdato
        {
            get
            {
                return this.avskrivningsdatoField;
            }
            set
            {
                this.avskrivningsdatoField = value;
                this.RaisePropertyChanged("avskrivningsdato");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool avskrivningsdatoSpecified
        {
            get
            {
                return this.avskrivningsdatoFieldSpecified;
            }
            set
            {
                this.avskrivningsdatoFieldSpecified = value;
                this.RaisePropertyChanged("avskrivningsdatoSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string avskrevetAv
        {
            get
            {
                return this.avskrevetAvField;
            }
            set
            {
                this.avskrevetAvField = value;
                this.RaisePropertyChanged("avskrevetAv");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public Avskrivningsmaate avskrivningsmaate
        {
            get
            {
                return this.avskrivningsmaateField;
            }
            set
            {
                this.avskrivningsmaateField = value;
                this.RaisePropertyChanged("avskrivningsmaate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public Journalnummer referanseAvskriverJournalnummer
        {
            get
            {
                return this.referanseAvskriverJournalnummerField;
            }
            set
            {
                this.referanseAvskriverJournalnummerField = value;
                this.RaisePropertyChanged("referanseAvskriverJournalnummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public Journalnummer referanseAvskrivesAvJournalnummer
        {
            get
            {
                return this.referanseAvskrivesAvJournalnummerField;
            }
            set
            {
                this.referanseAvskrivesAvJournalnummerField = value;
                this.RaisePropertyChanged("referanseAvskrivesAvJournalnummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public EksternNoekkel referanseAvskriverEksternNoekkel
        {
            get
            {
                return this.referanseAvskriverEksternNoekkelField;
            }
            set
            {
                this.referanseAvskriverEksternNoekkelField = value;
                this.RaisePropertyChanged("referanseAvskriverEksternNoekkel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public EksternNoekkel referanseAvskrivesAvEksternNoekkel
        {
            get
            {
                return this.referanseAvskrivesAvEksternNoekkelField;
            }
            set
            {
                this.referanseAvskrivesAvEksternNoekkelField = value;
                this.RaisePropertyChanged("referanseAvskrivesAvEksternNoekkel");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Merknad : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string systemIDField;
        
        private string merknadstekstField;
        
        private string merknadstypeField;
        
        private System.DateTime merknadsdatoField;
        
        private bool merknadsdatoFieldSpecified;
        
        private string merknadRegistrertAvField;
        
        private string merknadRegistrertAvInitField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string systemID
        {
            get
            {
                return this.systemIDField;
            }
            set
            {
                this.systemIDField = value;
                this.RaisePropertyChanged("systemID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string merknadstekst
        {
            get
            {
                return this.merknadstekstField;
            }
            set
            {
                this.merknadstekstField = value;
                this.RaisePropertyChanged("merknadstekst");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string merknadstype
        {
            get
            {
                return this.merknadstypeField;
            }
            set
            {
                this.merknadstypeField = value;
                this.RaisePropertyChanged("merknadstype");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public System.DateTime merknadsdato
        {
            get
            {
                return this.merknadsdatoField;
            }
            set
            {
                this.merknadsdatoField = value;
                this.RaisePropertyChanged("merknadsdato");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool merknadsdatoSpecified
        {
            get
            {
                return this.merknadsdatoFieldSpecified;
            }
            set
            {
                this.merknadsdatoFieldSpecified = value;
                this.RaisePropertyChanged("merknadsdatoSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string merknadRegistrertAv
        {
            get
            {
                return this.merknadRegistrertAvField;
            }
            set
            {
                this.merknadRegistrertAvField = value;
                this.RaisePropertyChanged("merknadRegistrertAv");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string merknadRegistrertAvInit
        {
            get
            {
                return this.merknadRegistrertAvInitField;
            }
            set
            {
                this.merknadRegistrertAvInitField = value;
                this.RaisePropertyChanged("merknadRegistrertAvInit");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Tilleggsinformasjon : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string systemIDField;
        
        private string rekkefoelgeField;
        
        private Informasjonstype informasjonstypeField;
        
        private Tilgangsrestriksjon tilgangsrestriksjonField;
        
        private System.DateTime oppbevaresTilDatoField;
        
        private bool oppbevaresTilDatoFieldSpecified;
        
        private string informasjonField;
        
        private string tilgangsgruppeNavnField;
        
        private System.DateTime registrertDatoField;
        
        private bool registrertDatoFieldSpecified;
        
        private string registrertAvField;
        
        private string registrertAvInitField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string systemID
        {
            get
            {
                return this.systemIDField;
            }
            set
            {
                this.systemIDField = value;
                this.RaisePropertyChanged("systemID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string rekkefoelge
        {
            get
            {
                return this.rekkefoelgeField;
            }
            set
            {
                this.rekkefoelgeField = value;
                this.RaisePropertyChanged("rekkefoelge");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public Informasjonstype informasjonstype
        {
            get
            {
                return this.informasjonstypeField;
            }
            set
            {
                this.informasjonstypeField = value;
                this.RaisePropertyChanged("informasjonstype");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public Tilgangsrestriksjon tilgangsrestriksjon
        {
            get
            {
                return this.tilgangsrestriksjonField;
            }
            set
            {
                this.tilgangsrestriksjonField = value;
                this.RaisePropertyChanged("tilgangsrestriksjon");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public System.DateTime oppbevaresTilDato
        {
            get
            {
                return this.oppbevaresTilDatoField;
            }
            set
            {
                this.oppbevaresTilDatoField = value;
                this.RaisePropertyChanged("oppbevaresTilDato");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool oppbevaresTilDatoSpecified
        {
            get
            {
                return this.oppbevaresTilDatoFieldSpecified;
            }
            set
            {
                this.oppbevaresTilDatoFieldSpecified = value;
                this.RaisePropertyChanged("oppbevaresTilDatoSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string informasjon
        {
            get
            {
                return this.informasjonField;
            }
            set
            {
                this.informasjonField = value;
                this.RaisePropertyChanged("informasjon");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public string tilgangsgruppeNavn
        {
            get
            {
                return this.tilgangsgruppeNavnField;
            }
            set
            {
                this.tilgangsgruppeNavnField = value;
                this.RaisePropertyChanged("tilgangsgruppeNavn");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public System.DateTime registrertDato
        {
            get
            {
                return this.registrertDatoField;
            }
            set
            {
                this.registrertDatoField = value;
                this.RaisePropertyChanged("registrertDato");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool registrertDatoSpecified
        {
            get
            {
                return this.registrertDatoFieldSpecified;
            }
            set
            {
                this.registrertDatoFieldSpecified = value;
                this.RaisePropertyChanged("registrertDatoSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public string registrertAv
        {
            get
            {
                return this.registrertAvField;
            }
            set
            {
                this.registrertAvField = value;
                this.RaisePropertyChanged("registrertAv");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public string registrertAvInit
        {
            get
            {
                return this.registrertAvInitField;
            }
            set
            {
                this.registrertAvInitField = value;
                this.RaisePropertyChanged("registrertAvInit");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="FinnJournalposterGittNoekkel", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class FinnJournalposterGittNoekkel
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public TietoEvryInnsynService.Journpostnoekkel nokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=1)]
        public bool returnerMerknad;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=2)]
        public bool returnerTilleggsinformasjon;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=3)]
        public bool returnerKorrespondansepart;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=4)]
        public bool returnerAvskrivning;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=5)]
        public TietoEvryInnsynService.ArkivKontekst kontekst;
        
        public FinnJournalposterGittNoekkel()
        {
        }
        
        public FinnJournalposterGittNoekkel(TietoEvryInnsynService.Journpostnoekkel nokkel, bool returnerMerknad, bool returnerTilleggsinformasjon, bool returnerKorrespondansepart, bool returnerAvskrivning, TietoEvryInnsynService.ArkivKontekst kontekst)
        {
            this.nokkel = nokkel;
            this.returnerMerknad = returnerMerknad;
            this.returnerTilleggsinformasjon = returnerTilleggsinformasjon;
            this.returnerKorrespondansepart = returnerKorrespondansepart;
            this.returnerAvskrivning = returnerAvskrivning;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="FinnJournalposterGittNoekkelResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class FinnJournalposterGittNoekkelResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31", IsNullable=false)]
        public TietoEvryInnsynService.Journalpost[] @return;
        
        public FinnJournalposterGittNoekkelResponse()
        {
        }
        
        public FinnJournalposterGittNoekkelResponse(TietoEvryInnsynService.Journalpost[] @return)
        {
            this.@return = @return;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="FinnJournalposterGittSaksmappeNoekkel", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class FinnJournalposterGittSaksmappeNoekkel
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public TietoEvryInnsynService.Saksnoekkel nokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=1)]
        public bool returnerMerknad;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=2)]
        public bool returnerTilleggsinformasjon;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=3)]
        public bool returnerKorrespondansepart;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=4)]
        public bool returnerAvskrivning;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=5)]
        public TietoEvryInnsynService.ArkivKontekst kontekst;
        
        public FinnJournalposterGittSaksmappeNoekkel()
        {
        }
        
        public FinnJournalposterGittSaksmappeNoekkel(TietoEvryInnsynService.Saksnoekkel nokkel, bool returnerMerknad, bool returnerTilleggsinformasjon, bool returnerKorrespondansepart, bool returnerAvskrivning, TietoEvryInnsynService.ArkivKontekst kontekst)
        {
            this.nokkel = nokkel;
            this.returnerMerknad = returnerMerknad;
            this.returnerTilleggsinformasjon = returnerTilleggsinformasjon;
            this.returnerKorrespondansepart = returnerKorrespondansepart;
            this.returnerAvskrivning = returnerAvskrivning;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="FinnJournalposterGittSaksmappeNoekkelResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class FinnJournalposterGittSaksmappeNoekkelResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31", IsNullable=false)]
        public TietoEvryInnsynService.Journalpost[] @return;
        
        public FinnJournalposterGittSaksmappeNoekkelResponse()
        {
        }
        
        public FinnJournalposterGittSaksmappeNoekkelResponse(TietoEvryInnsynService.Journalpost[] @return)
        {
            this.@return = @return;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Filter/xml.schema/2012.01.31")]
    public partial class Soekskriterie : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private SoekeOperatorEnum operatorField;
        
        private Kriterie kriterieField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public SoekeOperatorEnum @operator
        {
            get
            {
                return this.operatorField;
            }
            set
            {
                this.operatorField = value;
                this.RaisePropertyChanged("operator");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public Kriterie Kriterie
        {
            get
            {
                return this.kriterieField;
            }
            set
            {
                this.kriterieField = value;
                this.RaisePropertyChanged("Kriterie");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Filter/xml.schema/2012.01.31")]
    public enum SoekeOperatorEnum
    {
        
        /// <remarks/>
        LT,
        
        /// <remarks/>
        LE,
        
        /// <remarks/>
        EQ,
        
        /// <remarks/>
        GE,
        
        /// <remarks/>
        GT,
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Soekefelt))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Ansvarlig))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BboxKriterie))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Filter/xml.schema/2012.01.31")]
    public partial class Kriterie : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Filter/xml.schema/2012.01.31")]
    public partial class Soekefelt : Kriterie
    {
        
        private string feltnavnField;
        
        private string feltverdiField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string feltnavn
        {
            get
            {
                return this.feltnavnField;
            }
            set
            {
                this.feltnavnField = value;
                this.RaisePropertyChanged("feltnavn");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string feltverdi
        {
            get
            {
                return this.feltverdiField;
            }
            set
            {
                this.feltverdiField = value;
                this.RaisePropertyChanged("feltverdi");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Filter/xml.schema/2012.01.31")]
    public partial class Ansvarlig : Kriterie
    {
        
        private AnsvarligEnum eierField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public AnsvarligEnum eier
        {
            get
            {
                return this.eierField;
            }
            set
            {
                this.eierField = value;
                this.RaisePropertyChanged("eier");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Filter/xml.schema/2012.01.31")]
    public enum AnsvarligEnum
    {
        
        /// <remarks/>
        EGEN,
        
        /// <remarks/>
        EGENENHET,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Filter/xml.schema/2012.01.31")]
    public partial class BboxKriterie : Kriterie
    {
        
        private Bbox bboxField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public Bbox bbox
        {
            get
            {
                return this.bboxField;
            }
            set
            {
                this.bboxField = value;
                this.RaisePropertyChanged("bbox");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31")]
    public partial class Bbox : Geometri
    {
        
        private Koordinat nedreVenstreField;
        
        private Koordinat oevreHoeyreField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public Koordinat nedreVenstre
        {
            get
            {
                return this.nedreVenstreField;
            }
            set
            {
                this.nedreVenstreField = value;
                this.RaisePropertyChanged("nedreVenstre");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public Koordinat oevreHoeyre
        {
            get
            {
                return this.oevreHoeyreField;
            }
            set
            {
                this.oevreHoeyreField = value;
                this.RaisePropertyChanged("oevreHoeyre");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31")]
    public partial class Koordinat : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private double xField;
        
        private double yField;
        
        private double zField;
        
        private bool zFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public double x
        {
            get
            {
                return this.xField;
            }
            set
            {
                this.xField = value;
                this.RaisePropertyChanged("x");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public double y
        {
            get
            {
                return this.yField;
            }
            set
            {
                this.yField = value;
                this.RaisePropertyChanged("y");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public double z
        {
            get
            {
                return this.zField;
            }
            set
            {
                this.zField = value;
                this.RaisePropertyChanged("z");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool zSpecified
        {
            get
            {
                return this.zFieldSpecified;
            }
            set
            {
                this.zFieldSpecified = value;
                this.RaisePropertyChanged("zSpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Bbox))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Punkt))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Kurve))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Flate))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31")]
    public partial class Geometri : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private KoordinatsystemKode koordinatsystemField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public KoordinatsystemKode koordinatsystem
        {
            get
            {
                return this.koordinatsystemField;
            }
            set
            {
                this.koordinatsystemField = value;
                this.RaisePropertyChanged("koordinatsystem");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31")]
    public partial class Punkt : Geometri
    {
        
        private Koordinat posisjonField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public Koordinat posisjon
        {
            get
            {
                return this.posisjonField;
            }
            set
            {
                this.posisjonField = value;
                this.RaisePropertyChanged("posisjon");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31")]
    public partial class Kurve : Geometri
    {
        
        private Koordinat[] linjeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", IsNullable=false)]
        public Koordinat[] linje
        {
            get
            {
                return this.linjeField;
            }
            set
            {
                this.linjeField = value;
                this.RaisePropertyChanged("linje");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31")]
    public partial class Flate : Geometri
    {
        
        private Ring[] indreAvgrensningField;
        
        private Ring ytreAvgrensningField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", IsNullable=false)]
        public Ring[] indreAvgrensning
        {
            get
            {
                return this.indreAvgrensningField;
            }
            set
            {
                this.indreAvgrensningField = value;
                this.RaisePropertyChanged("indreAvgrensning");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public Ring ytreAvgrensning
        {
            get
            {
                return this.ytreAvgrensningField;
            }
            set
            {
                this.ytreAvgrensningField = value;
                this.RaisePropertyChanged("ytreAvgrensning");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31")]
    public partial class Ring : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private Koordinat[] lukketKurveField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", IsNullable=false)]
        public Koordinat[] lukketKurve
        {
            get
            {
                return this.lukketKurveField;
            }
            set
            {
                this.lukketKurveField = value;
                this.RaisePropertyChanged("lukketKurve");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="FinnJournalposter", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class FinnJournalposter
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Felles/Filter/xml.schema/2012.01.31", IsNullable=false)]
        public TietoEvryInnsynService.Soekskriterie[] sok;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=1)]
        public bool returnerMerknad;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=2)]
        public bool returnerTilleggsinformasjon;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=3)]
        public bool returnerKorrespondansepart;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=4)]
        public bool returnerAvskrivning;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=5)]
        public TietoEvryInnsynService.ArkivKontekst kontekst;
        
        public FinnJournalposter()
        {
        }
        
        public FinnJournalposter(TietoEvryInnsynService.Soekskriterie[] sok, bool returnerMerknad, bool returnerTilleggsinformasjon, bool returnerKorrespondansepart, bool returnerAvskrivning, TietoEvryInnsynService.ArkivKontekst kontekst)
        {
            this.sok = sok;
            this.returnerMerknad = returnerMerknad;
            this.returnerTilleggsinformasjon = returnerTilleggsinformasjon;
            this.returnerKorrespondansepart = returnerKorrespondansepart;
            this.returnerAvskrivning = returnerAvskrivning;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="FinnJournalposterResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class FinnJournalposterResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31", IsNullable=false)]
        public TietoEvryInnsynService.Journalpost[] @return;
        
        public FinnJournalposterResponse()
        {
        }
        
        public FinnJournalposterResponse(TietoEvryInnsynService.Journalpost[] @return)
        {
            this.@return = @return;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Saksmappe : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string systemIDField;
        
        private Saksnummer saksnrField;
        
        private Mappetype mappetypeField;
        
        private System.DateTime saksdatoField;
        
        private bool saksdatoFieldSpecified;
        
        private string tittelField;
        
        private string offentligTittelField;
        
        private bool skjermetTittelField;
        
        private bool skjermetTittelFieldSpecified;
        
        private Skjerming skjermingField;
        
        private Saksstatus saksstatusField;
        
        private Dokumentmedium dokumentmediumField;
        
        private Arkivdel referanseArkivdelField;
        
        private Journalenhet journalenhetField;
        
        private string bevaringstidField;
        
        private Kassasjonsvedtak kassasjonsvedtakField;
        
        private System.DateTime kassasjonsdatoField;
        
        private bool kassasjonsdatoFieldSpecified;
        
        private string prosjektField;
        
        private string administrativEnhetInitField;
        
        private string administrativEnhetField;
        
        private string saksansvarligInitField;
        
        private string saksansvarligField;
        
        private string tilgangsgruppeNavnField;
        
        private Matrikkelnummer[] matrikkelnummerField;
        
        private Klasse[] klasseField;
        
        private Sakspart[] sakspartField;
        
        private Punkt[] punktField;
        
        private Tilleggsinformasjon[] tilleggsinformasjonField;
        
        private ByggIdent[] byggIdentField;
        
        private EksternNoekkel referanseEksternNoekkelField;
        
        private Merknad[] merknaderField;
        
        private NasjonalArealplanId planIdentField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string systemID
        {
            get
            {
                return this.systemIDField;
            }
            set
            {
                this.systemIDField = value;
                this.RaisePropertyChanged("systemID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public Saksnummer saksnr
        {
            get
            {
                return this.saksnrField;
            }
            set
            {
                this.saksnrField = value;
                this.RaisePropertyChanged("saksnr");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public Mappetype mappetype
        {
            get
            {
                return this.mappetypeField;
            }
            set
            {
                this.mappetypeField = value;
                this.RaisePropertyChanged("mappetype");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public System.DateTime saksdato
        {
            get
            {
                return this.saksdatoField;
            }
            set
            {
                this.saksdatoField = value;
                this.RaisePropertyChanged("saksdato");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool saksdatoSpecified
        {
            get
            {
                return this.saksdatoFieldSpecified;
            }
            set
            {
                this.saksdatoFieldSpecified = value;
                this.RaisePropertyChanged("saksdatoSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string tittel
        {
            get
            {
                return this.tittelField;
            }
            set
            {
                this.tittelField = value;
                this.RaisePropertyChanged("tittel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string offentligTittel
        {
            get
            {
                return this.offentligTittelField;
            }
            set
            {
                this.offentligTittelField = value;
                this.RaisePropertyChanged("offentligTittel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public bool skjermetTittel
        {
            get
            {
                return this.skjermetTittelField;
            }
            set
            {
                this.skjermetTittelField = value;
                this.RaisePropertyChanged("skjermetTittel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool skjermetTittelSpecified
        {
            get
            {
                return this.skjermetTittelFieldSpecified;
            }
            set
            {
                this.skjermetTittelFieldSpecified = value;
                this.RaisePropertyChanged("skjermetTittelSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public Skjerming skjerming
        {
            get
            {
                return this.skjermingField;
            }
            set
            {
                this.skjermingField = value;
                this.RaisePropertyChanged("skjerming");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public Saksstatus saksstatus
        {
            get
            {
                return this.saksstatusField;
            }
            set
            {
                this.saksstatusField = value;
                this.RaisePropertyChanged("saksstatus");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public Dokumentmedium dokumentmedium
        {
            get
            {
                return this.dokumentmediumField;
            }
            set
            {
                this.dokumentmediumField = value;
                this.RaisePropertyChanged("dokumentmedium");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=10)]
        public Arkivdel referanseArkivdel
        {
            get
            {
                return this.referanseArkivdelField;
            }
            set
            {
                this.referanseArkivdelField = value;
                this.RaisePropertyChanged("referanseArkivdel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=11)]
        public Journalenhet journalenhet
        {
            get
            {
                return this.journalenhetField;
            }
            set
            {
                this.journalenhetField = value;
                this.RaisePropertyChanged("journalenhet");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=12)]
        public string bevaringstid
        {
            get
            {
                return this.bevaringstidField;
            }
            set
            {
                this.bevaringstidField = value;
                this.RaisePropertyChanged("bevaringstid");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=13)]
        public Kassasjonsvedtak kassasjonsvedtak
        {
            get
            {
                return this.kassasjonsvedtakField;
            }
            set
            {
                this.kassasjonsvedtakField = value;
                this.RaisePropertyChanged("kassasjonsvedtak");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=14)]
        public System.DateTime kassasjonsdato
        {
            get
            {
                return this.kassasjonsdatoField;
            }
            set
            {
                this.kassasjonsdatoField = value;
                this.RaisePropertyChanged("kassasjonsdato");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool kassasjonsdatoSpecified
        {
            get
            {
                return this.kassasjonsdatoFieldSpecified;
            }
            set
            {
                this.kassasjonsdatoFieldSpecified = value;
                this.RaisePropertyChanged("kassasjonsdatoSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=15)]
        public string prosjekt
        {
            get
            {
                return this.prosjektField;
            }
            set
            {
                this.prosjektField = value;
                this.RaisePropertyChanged("prosjekt");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=16)]
        public string administrativEnhetInit
        {
            get
            {
                return this.administrativEnhetInitField;
            }
            set
            {
                this.administrativEnhetInitField = value;
                this.RaisePropertyChanged("administrativEnhetInit");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=17)]
        public string administrativEnhet
        {
            get
            {
                return this.administrativEnhetField;
            }
            set
            {
                this.administrativEnhetField = value;
                this.RaisePropertyChanged("administrativEnhet");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=18)]
        public string saksansvarligInit
        {
            get
            {
                return this.saksansvarligInitField;
            }
            set
            {
                this.saksansvarligInitField = value;
                this.RaisePropertyChanged("saksansvarligInit");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=19)]
        public string saksansvarlig
        {
            get
            {
                return this.saksansvarligField;
            }
            set
            {
                this.saksansvarligField = value;
                this.RaisePropertyChanged("saksansvarlig");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=20)]
        public string tilgangsgruppeNavn
        {
            get
            {
                return this.tilgangsgruppeNavnField;
            }
            set
            {
                this.tilgangsgruppeNavnField = value;
                this.RaisePropertyChanged("tilgangsgruppeNavn");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=21)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Matrikkel/Felles/xml.schema/2012.01.31", IsNullable=false)]
        public Matrikkelnummer[] Matrikkelnummer
        {
            get
            {
                return this.matrikkelnummerField;
            }
            set
            {
                this.matrikkelnummerField = value;
                this.RaisePropertyChanged("Matrikkelnummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=22)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", IsNullable=false)]
        public Klasse[] klasse
        {
            get
            {
                return this.klasseField;
            }
            set
            {
                this.klasseField = value;
                this.RaisePropertyChanged("klasse");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=23)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", IsNullable=false)]
        public Sakspart[] sakspart
        {
            get
            {
                return this.sakspartField;
            }
            set
            {
                this.sakspartField = value;
                this.RaisePropertyChanged("sakspart");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=24)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31", IsNullable=false)]
        public Punkt[] Punkt
        {
            get
            {
                return this.punktField;
            }
            set
            {
                this.punktField = value;
                this.RaisePropertyChanged("Punkt");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=25)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", IsNullable=false)]
        public Tilleggsinformasjon[] tilleggsinformasjon
        {
            get
            {
                return this.tilleggsinformasjonField;
            }
            set
            {
                this.tilleggsinformasjonField = value;
                this.RaisePropertyChanged("tilleggsinformasjon");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=26)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Matrikkel/Felles/xml.schema/2012.01.31", IsNullable=false)]
        public ByggIdent[] ByggIdent
        {
            get
            {
                return this.byggIdentField;
            }
            set
            {
                this.byggIdentField = value;
                this.RaisePropertyChanged("ByggIdent");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=27)]
        public EksternNoekkel referanseEksternNoekkel
        {
            get
            {
                return this.referanseEksternNoekkelField;
            }
            set
            {
                this.referanseEksternNoekkelField = value;
                this.RaisePropertyChanged("referanseEksternNoekkel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=28)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", IsNullable=false)]
        public Merknad[] merknader
        {
            get
            {
                return this.merknaderField;
            }
            set
            {
                this.merknaderField = value;
                this.RaisePropertyChanged("merknader");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=29)]
        public NasjonalArealplanId planIdent
        {
            get
            {
                return this.planIdentField;
            }
            set
            {
                this.planIdentField = value;
                this.RaisePropertyChanged("planIdent");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Matrikkel/Felles/xml.schema/2012.01.31")]
    public partial class Matrikkelnummer : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string kommunenummerField;
        
        private string gaardsnummerField;
        
        private string bruksnummerField;
        
        private string festenummerField;
        
        private string seksjonsnummerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string kommunenummer
        {
            get
            {
                return this.kommunenummerField;
            }
            set
            {
                this.kommunenummerField = value;
                this.RaisePropertyChanged("kommunenummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=1)]
        public string gaardsnummer
        {
            get
            {
                return this.gaardsnummerField;
            }
            set
            {
                this.gaardsnummerField = value;
                this.RaisePropertyChanged("gaardsnummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=2)]
        public string bruksnummer
        {
            get
            {
                return this.bruksnummerField;
            }
            set
            {
                this.bruksnummerField = value;
                this.RaisePropertyChanged("bruksnummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=3)]
        public string festenummer
        {
            get
            {
                return this.festenummerField;
            }
            set
            {
                this.festenummerField = value;
                this.RaisePropertyChanged("festenummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=4)]
        public string seksjonsnummer
        {
            get
            {
                return this.seksjonsnummerField;
            }
            set
            {
                this.seksjonsnummerField = value;
                this.RaisePropertyChanged("seksjonsnummer");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Klasse : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string rekkefoelgeField;
        
        private Klassifikasjonssystem klassifikasjonssystemField;
        
        private string klasseIDField;
        
        private bool skjermetKlasseField;
        
        private bool skjermetKlasseFieldSpecified;
        
        private string ledetekstField;
        
        private string tittelField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string rekkefoelge
        {
            get
            {
                return this.rekkefoelgeField;
            }
            set
            {
                this.rekkefoelgeField = value;
                this.RaisePropertyChanged("rekkefoelge");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public Klassifikasjonssystem klassifikasjonssystem
        {
            get
            {
                return this.klassifikasjonssystemField;
            }
            set
            {
                this.klassifikasjonssystemField = value;
                this.RaisePropertyChanged("klassifikasjonssystem");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string klasseID
        {
            get
            {
                return this.klasseIDField;
            }
            set
            {
                this.klasseIDField = value;
                this.RaisePropertyChanged("klasseID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public bool skjermetKlasse
        {
            get
            {
                return this.skjermetKlasseField;
            }
            set
            {
                this.skjermetKlasseField = value;
                this.RaisePropertyChanged("skjermetKlasse");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool skjermetKlasseSpecified
        {
            get
            {
                return this.skjermetKlasseFieldSpecified;
            }
            set
            {
                this.skjermetKlasseFieldSpecified = value;
                this.RaisePropertyChanged("skjermetKlasseSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string ledetekst
        {
            get
            {
                return this.ledetekstField;
            }
            set
            {
                this.ledetekstField = value;
                this.RaisePropertyChanged("ledetekst");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string tittel
        {
            get
            {
                return this.tittelField;
            }
            set
            {
                this.tittelField = value;
                this.RaisePropertyChanged("tittel");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Sakspart : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string systemIDField;
        
        private bool skjermetSakspartField;
        
        private bool skjermetSakspartFieldSpecified;
        
        private string kortnavnField;
        
        private string kontaktpersonField;
        
        private SakspartRolle sakspartRolleField;
        
        private string merknadField;
        
        private Kontakt kontaktField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string systemID
        {
            get
            {
                return this.systemIDField;
            }
            set
            {
                this.systemIDField = value;
                this.RaisePropertyChanged("systemID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public bool skjermetSakspart
        {
            get
            {
                return this.skjermetSakspartField;
            }
            set
            {
                this.skjermetSakspartField = value;
                this.RaisePropertyChanged("skjermetSakspart");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool skjermetSakspartSpecified
        {
            get
            {
                return this.skjermetSakspartFieldSpecified;
            }
            set
            {
                this.skjermetSakspartFieldSpecified = value;
                this.RaisePropertyChanged("skjermetSakspartSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string kortnavn
        {
            get
            {
                return this.kortnavnField;
            }
            set
            {
                this.kortnavnField = value;
                this.RaisePropertyChanged("kortnavn");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string kontaktperson
        {
            get
            {
                return this.kontaktpersonField;
            }
            set
            {
                this.kontaktpersonField = value;
                this.RaisePropertyChanged("kontaktperson");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public SakspartRolle sakspartRolle
        {
            get
            {
                return this.sakspartRolleField;
            }
            set
            {
                this.sakspartRolleField = value;
                this.RaisePropertyChanged("sakspartRolle");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string merknad
        {
            get
            {
                return this.merknadField;
            }
            set
            {
                this.merknadField = value;
                this.RaisePropertyChanged("merknad");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public Kontakt Kontakt
        {
            get
            {
                return this.kontaktField;
            }
            set
            {
                this.kontaktField = value;
                this.RaisePropertyChanged("Kontakt");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Matrikkel/Felles/xml.schema/2012.01.31")]
    public partial class ByggIdent : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string bygningsNummerField;
        
        private string endringsloepenummerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=0)]
        public string bygningsNummer
        {
            get
            {
                return this.bygningsNummerField;
            }
            set
            {
                this.bygningsNummerField = value;
                this.RaisePropertyChanged("bygningsNummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=1)]
        public string endringsloepenummer
        {
            get
            {
                return this.endringsloepenummerField;
            }
            set
            {
                this.endringsloepenummerField = value;
                this.RaisePropertyChanged("endringsloepenummer");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Plan/Felles/xml.schema/2012.01.31")]
    public partial class NasjonalArealplanId : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private Administrativenhetsnummer nummerField;
        
        private string planidentifikasjonField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public Administrativenhetsnummer nummer
        {
            get
            {
                return this.nummerField;
            }
            set
            {
                this.nummerField = value;
                this.RaisePropertyChanged("nummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string planidentifikasjon
        {
            get
            {
                return this.planidentifikasjonField;
            }
            set
            {
                this.planidentifikasjonField = value;
                this.RaisePropertyChanged("planidentifikasjon");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Stat))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Kommune))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Fylke))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Plan/Felles/xml.schema/2012.01.31")]
    public partial class Administrativenhetsnummer : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Plan/Felles/xml.schema/2012.01.31")]
    public partial class Stat : Administrativenhetsnummer
    {
        
        private string landskodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string landskode
        {
            get
            {
                return this.landskodeField;
            }
            set
            {
                this.landskodeField = value;
                this.RaisePropertyChanged("landskode");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Plan/Felles/xml.schema/2012.01.31")]
    public partial class Kommune : Administrativenhetsnummer
    {
        
        private string kommunenummerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string kommunenummer
        {
            get
            {
                return this.kommunenummerField;
            }
            set
            {
                this.kommunenummerField = value;
                this.RaisePropertyChanged("kommunenummer");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Plan/Felles/xml.schema/2012.01.31")]
    public partial class Fylke : Administrativenhetsnummer
    {
        
        private string fylkesnummerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string fylkesnummer
        {
            get
            {
                return this.fylkesnummerField;
            }
            set
            {
                this.fylkesnummerField = value;
                this.RaisePropertyChanged("fylkesnummer");
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="FinnSaksmapperGittNoekkel", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class FinnSaksmapperGittNoekkel
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public TietoEvryInnsynService.Saksnoekkel nokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=1)]
        public bool returnerMerknad;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=2)]
        public bool returnerTilleggsinformasjon;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=3)]
        public bool returnerSakspart;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=4)]
        public bool returnerKlasse;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=5)]
        public TietoEvryInnsynService.ArkivKontekst kontekst;
        
        public FinnSaksmapperGittNoekkel()
        {
        }
        
        public FinnSaksmapperGittNoekkel(TietoEvryInnsynService.Saksnoekkel nokkel, bool returnerMerknad, bool returnerTilleggsinformasjon, bool returnerSakspart, bool returnerKlasse, TietoEvryInnsynService.ArkivKontekst kontekst)
        {
            this.nokkel = nokkel;
            this.returnerMerknad = returnerMerknad;
            this.returnerTilleggsinformasjon = returnerTilleggsinformasjon;
            this.returnerSakspart = returnerSakspart;
            this.returnerKlasse = returnerKlasse;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="FinnSaksmapperGittNoekkelResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class FinnSaksmapperGittNoekkelResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31", IsNullable=false)]
        public TietoEvryInnsynService.Saksmappe[] @return;
        
        public FinnSaksmapperGittNoekkelResponse()
        {
        }
        
        public FinnSaksmapperGittNoekkelResponse(TietoEvryInnsynService.Saksmappe[] @return)
        {
            this.@return = @return;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="FinnSaksmapper", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class FinnSaksmapper
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Felles/Filter/xml.schema/2012.01.31", IsNullable=false)]
        public TietoEvryInnsynService.Soekskriterie[] sok;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=1)]
        public bool returnerMerknad;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=2)]
        public bool returnerTilleggsinformasjon;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=3)]
        public bool returnerSakspart;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=4)]
        public bool returnerKlasse;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=5)]
        public TietoEvryInnsynService.ArkivKontekst kontekst;
        
        public FinnSaksmapper()
        {
        }
        
        public FinnSaksmapper(TietoEvryInnsynService.Soekskriterie[] sok, bool returnerMerknad, bool returnerTilleggsinformasjon, bool returnerSakspart, bool returnerKlasse, TietoEvryInnsynService.ArkivKontekst kontekst)
        {
            this.sok = sok;
            this.returnerMerknad = returnerMerknad;
            this.returnerTilleggsinformasjon = returnerTilleggsinformasjon;
            this.returnerSakspart = returnerSakspart;
            this.returnerKlasse = returnerKlasse;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="FinnSaksmapperResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class FinnSaksmapperResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31", IsNullable=false)]
        public TietoEvryInnsynService.Saksmappe[] @return;
        
        public FinnSaksmapperResponse()
        {
        }
        
        public FinnSaksmapperResponse(TietoEvryInnsynService.Saksmappe[] @return)
        {
            this.@return = @return;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    public partial class Dokument : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string systemIDField;
        
        private string dokumentnummerField;
        
        private TilknyttetRegistreringSom tilknyttetRegistreringSomField;
        
        private Dokumenttype dokumenttypeField;
        
        private string tittelField;
        
        private Dokumentstatus dokumentstatusField;
        
        private Variantformat variantformatField;
        
        private Format formatField;
        
        private string referanseJournalpostSystemIDField;
        
        private Fil filField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string systemID
        {
            get
            {
                return this.systemIDField;
            }
            set
            {
                this.systemIDField = value;
                this.RaisePropertyChanged("systemID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string dokumentnummer
        {
            get
            {
                return this.dokumentnummerField;
            }
            set
            {
                this.dokumentnummerField = value;
                this.RaisePropertyChanged("dokumentnummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public TilknyttetRegistreringSom tilknyttetRegistreringSom
        {
            get
            {
                return this.tilknyttetRegistreringSomField;
            }
            set
            {
                this.tilknyttetRegistreringSomField = value;
                this.RaisePropertyChanged("tilknyttetRegistreringSom");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public Dokumenttype dokumenttype
        {
            get
            {
                return this.dokumenttypeField;
            }
            set
            {
                this.dokumenttypeField = value;
                this.RaisePropertyChanged("dokumenttype");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string tittel
        {
            get
            {
                return this.tittelField;
            }
            set
            {
                this.tittelField = value;
                this.RaisePropertyChanged("tittel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public Dokumentstatus dokumentstatus
        {
            get
            {
                return this.dokumentstatusField;
            }
            set
            {
                this.dokumentstatusField = value;
                this.RaisePropertyChanged("dokumentstatus");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public Variantformat variantformat
        {
            get
            {
                return this.variantformatField;
            }
            set
            {
                this.variantformatField = value;
                this.RaisePropertyChanged("variantformat");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public Format format
        {
            get
            {
                return this.formatField;
            }
            set
            {
                this.formatField = value;
                this.RaisePropertyChanged("format");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public string referanseJournalpostSystemID
        {
            get
            {
                return this.referanseJournalpostSystemIDField;
            }
            set
            {
                this.referanseJournalpostSystemIDField = value;
                this.RaisePropertyChanged("referanseJournalpostSystemID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public Fil Fil
        {
            get
            {
                return this.filField;
            }
            set
            {
                this.filField = value;
                this.RaisePropertyChanged("Fil");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="FinnDokumenterGittJournalpostnoekkel", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class FinnDokumenterGittJournalpostnoekkel
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public TietoEvryInnsynService.Journpostnoekkel journpostnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=1)]
        public bool returnerFil;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=2)]
        public TietoEvryInnsynService.ArkivKontekst kontekst;
        
        public FinnDokumenterGittJournalpostnoekkel()
        {
        }
        
        public FinnDokumenterGittJournalpostnoekkel(TietoEvryInnsynService.Journpostnoekkel journpostnokkel, bool returnerFil, TietoEvryInnsynService.ArkivKontekst kontekst)
        {
            this.journpostnokkel = journpostnokkel;
            this.returnerFil = returnerFil;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="FinnDokumenterGittJournalpostnoekkelResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class FinnDokumenterGittJournalpostnoekkelResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31", IsNullable=false)]
        public TietoEvryInnsynService.Dokument[] @return;
        
        public FinnDokumenterGittJournalpostnoekkelResponse()
        {
        }
        
        public FinnDokumenterGittJournalpostnoekkelResponse(TietoEvryInnsynService.Dokument[] @return)
        {
            this.@return = @return;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="FinnDokumenterGittSaksnoekkel", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class FinnDokumenterGittSaksnoekkel
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        public TietoEvryInnsynService.Saksnoekkel saksnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=1)]
        public bool returnerFil;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=2)]
        public TietoEvryInnsynService.ArkivKontekst kontekst;
        
        public FinnDokumenterGittSaksnoekkel()
        {
        }
        
        public FinnDokumenterGittSaksnoekkel(TietoEvryInnsynService.Saksnoekkel saksnokkel, bool returnerFil, TietoEvryInnsynService.ArkivKontekst kontekst)
        {
            this.saksnokkel = saksnokkel;
            this.returnerFil = returnerFil;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="FinnDokumenterGittSaksnoekkelResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class FinnDokumenterGittSaksnoekkelResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31", IsNullable=false)]
        public TietoEvryInnsynService.Dokument[] @return;
        
        public FinnDokumenterGittSaksnoekkelResponse()
        {
        }
        
        public FinnDokumenterGittSaksnoekkelResponse(TietoEvryInnsynService.Dokument[] @return)
        {
            this.@return = @return;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="FinnDokumenter", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class FinnDokumenter
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Felles/Filter/xml.schema/2012.01.31", IsNullable=false)]
        public TietoEvryInnsynService.Soekskriterie[] sok;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=1)]
        public bool returnerFil;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=2)]
        public TietoEvryInnsynService.ArkivKontekst kontekst;
        
        public FinnDokumenter()
        {
        }
        
        public FinnDokumenter(TietoEvryInnsynService.Soekskriterie[] sok, bool returnerFil, TietoEvryInnsynService.ArkivKontekst kontekst)
        {
            this.sok = sok;
            this.returnerFil = returnerFil;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="FinnDokumenterResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class FinnDokumenterResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31", IsNullable=false)]
        public TietoEvryInnsynService.Dokument[] @return;
        
        public FinnDokumenterResponse()
        {
        }
        
        public FinnDokumenterResponse(TietoEvryInnsynService.Dokument[] @return)
        {
            this.@return = @return;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    public interface ArkivInnsynPortChannel : TietoEvryInnsynService.ArkivInnsynPort, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    public partial class ArkivInnsynPortClient : System.ServiceModel.ClientBase<TietoEvryInnsynService.ArkivInnsynPort>, TietoEvryInnsynService.ArkivInnsynPort
    {
        
        /// <summary>
        /// Implement this partial method to configure the service endpoint.
        /// </summary>
        /// <param name="serviceEndpoint">The endpoint to configure</param>
        /// <param name="clientCredentials">The client credentials</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public ArkivInnsynPortClient() : 
                base(ArkivInnsynPortClient.GetDefaultBinding(), ArkivInnsynPortClient.GetDefaultEndpointAddress())
        {
            this.Endpoint.Name = EndpointConfiguration.ArkivInnsyn.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public ArkivInnsynPortClient(EndpointConfiguration endpointConfiguration) : 
                base(ArkivInnsynPortClient.GetBindingForEndpoint(endpointConfiguration), ArkivInnsynPortClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public ArkivInnsynPortClient(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(ArkivInnsynPortClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public ArkivInnsynPortClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(ArkivInnsynPortClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public ArkivInnsynPortClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        public TietoEvryInnsynService.HentKodelisteResponse HentKodeliste(TietoEvryInnsynService.HentKodeliste request)
        {
            return base.Channel.HentKodeliste(request);
        }
        
        public System.Threading.Tasks.Task<TietoEvryInnsynService.HentKodelisteResponse> HentKodelisteAsync(TietoEvryInnsynService.HentKodeliste request)
        {
            return base.Channel.HentKodelisteAsync(request);
        }
        
        public TietoEvryInnsynService.HentFilResponse HentFil(TietoEvryInnsynService.HentFil request)
        {
            return base.Channel.HentFil(request);
        }
        
        public System.Threading.Tasks.Task<TietoEvryInnsynService.HentFilResponse> HentFilAsync(TietoEvryInnsynService.HentFil request)
        {
            return base.Channel.HentFilAsync(request);
        }
        
        public TietoEvryInnsynService.FinnDokumenttyperResponse FinnDokumenttyper(TietoEvryInnsynService.FinnDokumenttyper request)
        {
            return base.Channel.FinnDokumenttyper(request);
        }
        
        public System.Threading.Tasks.Task<TietoEvryInnsynService.FinnDokumenttyperResponse> FinnDokumenttyperAsync(TietoEvryInnsynService.FinnDokumenttyper request)
        {
            return base.Channel.FinnDokumenttyperAsync(request);
        }
        
        public TietoEvryInnsynService.FinnJournalposterGittNoekkelResponse FinnJournalposterGittNoekkel(TietoEvryInnsynService.FinnJournalposterGittNoekkel request)
        {
            return base.Channel.FinnJournalposterGittNoekkel(request);
        }
        
        public System.Threading.Tasks.Task<TietoEvryInnsynService.FinnJournalposterGittNoekkelResponse> FinnJournalposterGittNoekkelAsync(TietoEvryInnsynService.FinnJournalposterGittNoekkel request)
        {
            return base.Channel.FinnJournalposterGittNoekkelAsync(request);
        }
        
        public TietoEvryInnsynService.FinnJournalposterGittSaksmappeNoekkelResponse FinnJournalposterGittSaksmappeNoekkel(TietoEvryInnsynService.FinnJournalposterGittSaksmappeNoekkel request)
        {
            return base.Channel.FinnJournalposterGittSaksmappeNoekkel(request);
        }
        
        public System.Threading.Tasks.Task<TietoEvryInnsynService.FinnJournalposterGittSaksmappeNoekkelResponse> FinnJournalposterGittSaksmappeNoekkelAsync(TietoEvryInnsynService.FinnJournalposterGittSaksmappeNoekkel request)
        {
            return base.Channel.FinnJournalposterGittSaksmappeNoekkelAsync(request);
        }
        
        public TietoEvryInnsynService.FinnJournalposterResponse FinnJournalposter(TietoEvryInnsynService.FinnJournalposter request)
        {
            return base.Channel.FinnJournalposter(request);
        }
        
        public System.Threading.Tasks.Task<TietoEvryInnsynService.FinnJournalposterResponse> FinnJournalposterAsync(TietoEvryInnsynService.FinnJournalposter request)
        {
            return base.Channel.FinnJournalposterAsync(request);
        }
        
        public TietoEvryInnsynService.FinnSaksmapperGittNoekkelResponse FinnSaksmapperGittNoekkel(TietoEvryInnsynService.FinnSaksmapperGittNoekkel request)
        {
            return base.Channel.FinnSaksmapperGittNoekkel(request);
        }
        
        public System.Threading.Tasks.Task<TietoEvryInnsynService.FinnSaksmapperGittNoekkelResponse> FinnSaksmapperGittNoekkelAsync(TietoEvryInnsynService.FinnSaksmapperGittNoekkel request)
        {
            return base.Channel.FinnSaksmapperGittNoekkelAsync(request);
        }
        
        public TietoEvryInnsynService.FinnSaksmapperResponse FinnSaksmapper(TietoEvryInnsynService.FinnSaksmapper request)
        {
            return base.Channel.FinnSaksmapper(request);
        }
        
        public System.Threading.Tasks.Task<TietoEvryInnsynService.FinnSaksmapperResponse> FinnSaksmapperAsync(TietoEvryInnsynService.FinnSaksmapper request)
        {
            return base.Channel.FinnSaksmapperAsync(request);
        }
        
        public TietoEvryInnsynService.FinnDokumenterGittJournalpostnoekkelResponse FinnDokumenterGittJournalpostnoekkel(TietoEvryInnsynService.FinnDokumenterGittJournalpostnoekkel request)
        {
            return base.Channel.FinnDokumenterGittJournalpostnoekkel(request);
        }
        
        public System.Threading.Tasks.Task<TietoEvryInnsynService.FinnDokumenterGittJournalpostnoekkelResponse> FinnDokumenterGittJournalpostnoekkelAsync(TietoEvryInnsynService.FinnDokumenterGittJournalpostnoekkel request)
        {
            return base.Channel.FinnDokumenterGittJournalpostnoekkelAsync(request);
        }
        
        public TietoEvryInnsynService.FinnDokumenterGittSaksnoekkelResponse FinnDokumenterGittSaksnoekkel(TietoEvryInnsynService.FinnDokumenterGittSaksnoekkel request)
        {
            return base.Channel.FinnDokumenterGittSaksnoekkel(request);
        }
        
        public System.Threading.Tasks.Task<TietoEvryInnsynService.FinnDokumenterGittSaksnoekkelResponse> FinnDokumenterGittSaksnoekkelAsync(TietoEvryInnsynService.FinnDokumenterGittSaksnoekkel request)
        {
            return base.Channel.FinnDokumenterGittSaksnoekkelAsync(request);
        }
        
        public TietoEvryInnsynService.FinnDokumenterResponse FinnDokumenter(TietoEvryInnsynService.FinnDokumenter request)
        {
            return base.Channel.FinnDokumenter(request);
        }
        
        public System.Threading.Tasks.Task<TietoEvryInnsynService.FinnDokumenterResponse> FinnDokumenterAsync(TietoEvryInnsynService.FinnDokumenter request)
        {
            return base.Channel.FinnDokumenterAsync(request);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.ArkivInnsyn))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.ArkivInnsyn))
            {
                return new System.ServiceModel.EndpointAddress("http://rep.geointegrasjon.no/Arkiv/Innsyn/xml.wsdl/2012.01.31");
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.Channels.Binding GetDefaultBinding()
        {
            return ArkivInnsynPortClient.GetBindingForEndpoint(EndpointConfiguration.ArkivInnsyn);
        }
        
        private static System.ServiceModel.EndpointAddress GetDefaultEndpointAddress()
        {
            return ArkivInnsynPortClient.GetEndpointAddress(EndpointConfiguration.ArkivInnsyn);
        }
        
        public enum EndpointConfiguration
        {
            
            ArkivInnsyn,
        }
    }
}
