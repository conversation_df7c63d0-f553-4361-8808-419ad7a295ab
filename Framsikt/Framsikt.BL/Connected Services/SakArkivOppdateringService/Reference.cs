//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace SakArkivOppdateringService
{
    
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(OperationalFault))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ImplementationFault))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    public partial class SystemFault : GeointegrasjonFault
    {
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ApplicationFault))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ValidationFault))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FinderFault))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SystemFault))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(OperationalFault))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ImplementationFault))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    public partial class GeointegrasjonFault : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string feilKodeField;
        
        private string feilBeskrivelseField;
        
        private string[] feilDetaljerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string feilKode
        {
            get
            {
                return this.feilKodeField;
            }
            set
            {
                this.feilKodeField = value;
                this.RaisePropertyChanged("feilKode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string feilBeskrivelse
        {
            get
            {
                return this.feilBeskrivelseField;
            }
            set
            {
                this.feilBeskrivelseField = value;
                this.RaisePropertyChanged("feilBeskrivelse");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=2)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste")]
        public string[] feilDetaljer
        {
            get
            {
                return this.feilDetaljerField;
            }
            set
            {
                this.feilDetaljerField = value;
                this.RaisePropertyChanged("feilDetaljer");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FaseType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FaseStatus))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(KoordinatsystemKode))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Tilgangsrestriksjon))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SkjermingsHjemmel))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SkjermingOpphorerAksjon))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Saksstatus))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Mappetype))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Korrespondanseparttype))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Klassifikasjonssystem))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Kassasjonsvedtak))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Journalstatus))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Journalposttype))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Journalenhet))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Informasjonstype))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Forsendelsesmaate))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Dokumentmedium))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Avskrivningsmaate))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Arkivdel))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SakspartRolle))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PersonidentifikatorType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Landkode))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(EnkelAdressetype))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Variantformat))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(TilknyttetRegistreringSom))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Format))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Dokumentstatus))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Dokumenttype))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Kodeliste/xml.schema/2012.01.31")]
    public partial class Kode : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string kodeverdiField;
        
        private string kodebeskrivelseField;
        
        private bool erGyldigField;
        
        private bool erGyldigFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string kodeverdi
        {
            get
            {
                return this.kodeverdiField;
            }
            set
            {
                this.kodeverdiField = value;
                this.RaisePropertyChanged("kodeverdi");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string kodebeskrivelse
        {
            get
            {
                return this.kodebeskrivelseField;
            }
            set
            {
                this.kodebeskrivelseField = value;
                this.RaisePropertyChanged("kodebeskrivelse");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public bool erGyldig
        {
            get
            {
                return this.erGyldigField;
            }
            set
            {
                this.erGyldigField = value;
                this.RaisePropertyChanged("erGyldig");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool erGyldigSpecified
        {
            get
            {
                return this.erGyldigFieldSpecified;
            }
            set
            {
                this.erGyldigFieldSpecified = value;
                this.RaisePropertyChanged("erGyldigSpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Sak/Faser/xml.schema/2012.01.31")]
    public partial class FaseType : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Sak/Faser/xml.schema/2012.01.31")]
    public partial class FaseStatus : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31")]
    public partial class KoordinatsystemKode : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Tilgangsrestriksjon : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class SkjermingsHjemmel : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class SkjermingOpphorerAksjon : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Saksstatus : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Mappetype : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Korrespondanseparttype : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Klassifikasjonssystem : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Kassasjonsvedtak : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Journalstatus : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Journalposttype : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Journalenhet : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Informasjonstype : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Forsendelsesmaate : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Dokumentmedium : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Avskrivningsmaate : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Arkivdel : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class SakspartRolle : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Kontakt/xml.schema/2012.01.31")]
    public partial class PersonidentifikatorType : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    public partial class Landkode : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    public partial class EnkelAdressetype : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    public partial class Variantformat : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    public partial class TilknyttetRegistreringSom : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    public partial class Format : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    public partial class Dokumentstatus : Kode
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    public partial class Dokumenttype : Kode
    {
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PlanKontekst))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MatrikkelKontekst))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ArkivKontekst))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    public partial class Kontekst : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string spraakField;
        
        private string klientnavnField;
        
        private string klientversjonField;
        
        private string systemversjonField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string spraak
        {
            get
            {
                return this.spraakField;
            }
            set
            {
                this.spraakField = value;
                this.RaisePropertyChanged("spraak");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string klientnavn
        {
            get
            {
                return this.klientnavnField;
            }
            set
            {
                this.klientnavnField = value;
                this.RaisePropertyChanged("klientnavn");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string klientversjon
        {
            get
            {
                return this.klientversjonField;
            }
            set
            {
                this.klientversjonField = value;
                this.RaisePropertyChanged("klientversjon");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string systemversjon
        {
            get
            {
                return this.systemversjonField;
            }
            set
            {
                this.systemversjonField = value;
                this.RaisePropertyChanged("systemversjon");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    public partial class PlanKontekst : Kontekst
    {
        
        private KoordinatsystemKode koordinatsystemField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public KoordinatsystemKode koordinatsystem
        {
            get
            {
                return this.koordinatsystemField;
            }
            set
            {
                this.koordinatsystemField = value;
                this.RaisePropertyChanged("koordinatsystem");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    public partial class MatrikkelKontekst : Kontekst
    {
        
        private KoordinatsystemKode koordinatsystemField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public KoordinatsystemKode koordinatsystem
        {
            get
            {
                return this.koordinatsystemField;
            }
            set
            {
                this.koordinatsystemField = value;
                this.RaisePropertyChanged("koordinatsystem");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    public partial class ArkivKontekst : Kontekst
    {
        
        private KoordinatsystemKode koordinatsystemField;
        
        private string referanseoppsettField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public KoordinatsystemKode koordinatsystem
        {
            get
            {
                return this.koordinatsystemField;
            }
            set
            {
                this.koordinatsystemField = value;
                this.RaisePropertyChanged("koordinatsystem");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string referanseoppsett
        {
            get
            {
                return this.referanseoppsettField;
            }
            set
            {
                this.referanseoppsettField = value;
                this.RaisePropertyChanged("referanseoppsett");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class SystemID : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string idField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
                this.RaisePropertyChanged("id");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class EksternNoekkel : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string fagsystemField;
        
        private string noekkelField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string fagsystem
        {
            get
            {
                return this.fagsystemField;
            }
            set
            {
                this.fagsystemField = value;
                this.RaisePropertyChanged("fagsystem");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string noekkel
        {
            get
            {
                return this.noekkelField;
            }
            set
            {
                this.noekkelField = value;
                this.RaisePropertyChanged("noekkel");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SakSystemId))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SakEksternNoekkel))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Saksnummer))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Felles/xml.schema/2012.01.31")]
    public partial class Saksnoekkel : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class SakSystemId : Saksnoekkel
    {
        
        private SystemID systemIDField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public SystemID systemID
        {
            get
            {
                return this.systemIDField;
            }
            set
            {
                this.systemIDField = value;
                this.RaisePropertyChanged("systemID");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class SakEksternNoekkel : Saksnoekkel
    {
        
        private EksternNoekkel eksternnoekkelField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public EksternNoekkel eksternnoekkel
        {
            get
            {
                return this.eksternnoekkelField;
            }
            set
            {
                this.eksternnoekkelField = value;
                this.RaisePropertyChanged("eksternnoekkel");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Felles/xml.schema/2012.01.31")]
    public partial class Saksnummer : Saksnoekkel
    {
        
        private string saksaarField;
        
        private string sakssekvensnummerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=0)]
        public string saksaar
        {
            get
            {
                return this.saksaarField;
            }
            set
            {
                this.saksaarField = value;
                this.RaisePropertyChanged("saksaar");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=1)]
        public string sakssekvensnummer
        {
            get
            {
                return this.sakssekvensnummerField;
            }
            set
            {
                this.sakssekvensnummerField = value;
                this.RaisePropertyChanged("sakssekvensnummer");
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ValidationFault))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FinderFault))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    public partial class ApplicationFault : GeointegrasjonFault
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    public partial class ValidationFault : ApplicationFault
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    public partial class FinderFault : ApplicationFault
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    public partial class OperationalFault : SystemFault
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
    public partial class ImplementationFault : SystemFault
    {
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", ConfigurationName="SakArkivOppdateringService.ISakArkivOppdateringPort")]
    public interface ISakArkivOppdateringPort
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettSaksmapp" +
            "eTilleggsinformasjon", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/SlettSaksmappeTilleggsinformasjonResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettSaksmapp" +
            "eTilleggsinformasjon", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettSaksmapp" +
            "eTilleggsinformasjon", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettSaksmapp" +
            "eTilleggsinformasjon", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettSaksmapp" +
            "eTilleggsinformasjon", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettSaksmapp" +
            "eTilleggsinformasjon", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettSaksmapp" +
            "eTilleggsinformasjon", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.SlettSaksmappeTilleggsinformasjonResponse SlettSaksmappeTilleggsinformasjon(SakArkivOppdateringService.SlettSaksmappeTilleggsinformasjon request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettSaksmapp" +
            "eTilleggsinformasjon", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/SlettSaksmappeTilleggsinformasjonResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.SlettSaksmappeTilleggsinformasjonResponse> SlettSaksmappeTilleggsinformasjonAsync(SakArkivOppdateringService.SlettSaksmappeTilleggsinformasjon request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettSaksmapp" +
            "eMerknad", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/SlettSaksmappeMerknadResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettSaksmapp" +
            "eMerknad", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettSaksmapp" +
            "eMerknad", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettSaksmapp" +
            "eMerknad", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettSaksmapp" +
            "eMerknad", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettSaksmapp" +
            "eMerknad", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettSaksmapp" +
            "eMerknad", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.SlettSaksmappeMerknadResponse SlettSaksmappeMerknad(SakArkivOppdateringService.SlettSaksmappeMerknad request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettSaksmapp" +
            "eMerknad", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/SlettSaksmappeMerknadResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.SlettSaksmappeMerknadResponse> SlettSaksmappeMerknadAsync(SakArkivOppdateringService.SlettSaksmappeMerknad request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettJournalp" +
            "ostTilleggsinformasjon", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/SlettJournalpostTilleggsinformasjonResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettJournalp" +
            "ostTilleggsinformasjon", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettJournalp" +
            "ostTilleggsinformasjon", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettJournalp" +
            "ostTilleggsinformasjon", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettJournalp" +
            "ostTilleggsinformasjon", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettJournalp" +
            "ostTilleggsinformasjon", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettJournalp" +
            "ostTilleggsinformasjon", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.SlettJournalpostTilleggsinformasjonResponse SlettJournalpostTilleggsinformasjon(SakArkivOppdateringService.SlettJournalpostTilleggsinformasjon request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettJournalp" +
            "ostTilleggsinformasjon", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/SlettJournalpostTilleggsinformasjonResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.SlettJournalpostTilleggsinformasjonResponse> SlettJournalpostTilleggsinformasjonAsync(SakArkivOppdateringService.SlettJournalpostTilleggsinformasjon request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettJournalp" +
            "ostMerknad", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/SlettJournalpostMerknadResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettJournalp" +
            "ostMerknad", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettJournalp" +
            "ostMerknad", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettJournalp" +
            "ostMerknad", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettJournalp" +
            "ostMerknad", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettJournalp" +
            "ostMerknad", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettJournalp" +
            "ostMerknad", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.SlettJournalpostMerknadResponse SlettJournalpostMerknad(SakArkivOppdateringService.SlettJournalpostMerknad request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettJournalp" +
            "ostMerknad", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/SlettJournalpostMerknadResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.SlettJournalpostMerknadResponse> SlettJournalpostMerknadAsync(SakArkivOppdateringService.SlettJournalpostMerknad request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySaksmappeTi" +
            "lleggsinformasjon", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NySaksmappeTilleggsinformasjonResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySaksmappeTi" +
            "lleggsinformasjon", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySaksmappeTi" +
            "lleggsinformasjon", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySaksmappeTi" +
            "lleggsinformasjon", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySaksmappeTi" +
            "lleggsinformasjon", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySaksmappeTi" +
            "lleggsinformasjon", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySaksmappeTi" +
            "lleggsinformasjon", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.NySaksmappeTilleggsinformasjonResponse NySaksmappeTilleggsinformasjon(SakArkivOppdateringService.NySaksmappeTilleggsinformasjon request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySaksmappeTi" +
            "lleggsinformasjon", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NySaksmappeTilleggsinformasjonResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.NySaksmappeTilleggsinformasjonResponse> NySaksmappeTilleggsinformasjonAsync(SakArkivOppdateringService.NySaksmappeTilleggsinformasjon request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySaksmappeMe" +
            "rknad", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NySaksmappeMerknadResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySaksmappeMe" +
            "rknad", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySaksmappeMe" +
            "rknad", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySaksmappeMe" +
            "rknad", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySaksmappeMe" +
            "rknad", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySaksmappeMe" +
            "rknad", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySaksmappeMe" +
            "rknad", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.NySaksmappeMerknadResponse NySaksmappeMerknad(SakArkivOppdateringService.NySaksmappeMerknad request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySaksmappeMe" +
            "rknad", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NySaksmappeMerknadResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.NySaksmappeMerknadResponse> NySaksmappeMerknadAsync(SakArkivOppdateringService.NySaksmappeMerknad request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyJournalpost" +
            "Tilleggsinformasjon", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NyJournalpostTilleggsinformasjonResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyJournalpost" +
            "Tilleggsinformasjon", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyJournalpost" +
            "Tilleggsinformasjon", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyJournalpost" +
            "Tilleggsinformasjon", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyJournalpost" +
            "Tilleggsinformasjon", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyJournalpost" +
            "Tilleggsinformasjon", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyJournalpost" +
            "Tilleggsinformasjon", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.NyJournalpostTilleggsinformasjonResponse NyJournalpostTilleggsinformasjon(SakArkivOppdateringService.NyJournalpostTilleggsinformasjon request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyJournalpost" +
            "Tilleggsinformasjon", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NyJournalpostTilleggsinformasjonResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.NyJournalpostTilleggsinformasjonResponse> NyJournalpostTilleggsinformasjonAsync(SakArkivOppdateringService.NyJournalpostTilleggsinformasjon request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyJournalpost" +
            "Merknad", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NyJournalpostMerknadResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyJournalpost" +
            "Merknad", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyJournalpost" +
            "Merknad", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyJournalpost" +
            "Merknad", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyJournalpost" +
            "Merknad", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyJournalpost" +
            "Merknad", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyJournalpost" +
            "Merknad", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.NyJournalpostMerknadResponse NyJournalpostMerknad(SakArkivOppdateringService.NyJournalpostMerknad request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyJournalpost" +
            "Merknad", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NyJournalpostMerknadResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.NyJournalpostMerknadResponse> NyJournalpostMerknadAsync(SakArkivOppdateringService.NyJournalpostMerknad request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettAvskrivn" +
            "ing", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/SlettAvskrivningResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettAvskrivn" +
            "ing", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettAvskrivn" +
            "ing", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettAvskrivn" +
            "ing", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettAvskrivn" +
            "ing", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettAvskrivn" +
            "ing", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettAvskrivn" +
            "ing", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.SlettAvskrivningResponse SlettAvskrivning(SakArkivOppdateringService.SlettAvskrivning request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettAvskrivn" +
            "ing", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/SlettAvskrivningResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.SlettAvskrivningResponse> SlettAvskrivningAsync(SakArkivOppdateringService.SlettAvskrivning request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyAvskrivning" +
            "", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NyAvskrivningResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyAvskrivning" +
            "", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyAvskrivning" +
            "", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyAvskrivning" +
            "", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyAvskrivning" +
            "", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyAvskrivning" +
            "", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyAvskrivning" +
            "", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.NyAvskrivningResponse NyAvskrivning(SakArkivOppdateringService.NyAvskrivning request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyAvskrivning" +
            "", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NyAvskrivningResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.NyAvskrivningResponse> NyAvskrivningAsync(SakArkivOppdateringService.NyAvskrivning request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterJourn" +
            "alpostStatus", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/OppdaterJournalpostStatusResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterJourn" +
            "alpostStatus", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterJourn" +
            "alpostStatus", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterJourn" +
            "alpostStatus", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterJourn" +
            "alpostStatus", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterJourn" +
            "alpostStatus", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterJourn" +
            "alpostStatus", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.OppdaterJournalpostStatusResponse OppdaterJournalpostStatus(SakArkivOppdateringService.OppdaterJournalpostStatusRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterJourn" +
            "alpostStatus", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/OppdaterJournalpostStatusResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.OppdaterJournalpostStatusResponse> OppdaterJournalpostStatusAsync(SakArkivOppdateringService.OppdaterJournalpostStatusRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterJourn" +
            "alpostEksternNoekkel", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/OppdaterJournalpostEksternNoekkelResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterJourn" +
            "alpostEksternNoekkel", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterJourn" +
            "alpostEksternNoekkel", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterJourn" +
            "alpostEksternNoekkel", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterJourn" +
            "alpostEksternNoekkel", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterJourn" +
            "alpostEksternNoekkel", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterJourn" +
            "alpostEksternNoekkel", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.OppdaterJournalpostEksternNoekkelResponse OppdaterJournalpostEksternNoekkel(SakArkivOppdateringService.OppdaterJournalpostEksternNoekkelRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterJourn" +
            "alpostEksternNoekkel", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/OppdaterJournalpostEksternNoekkelResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.OppdaterJournalpostEksternNoekkelResponse> OppdaterJournalpostEksternNoekkelAsync(SakArkivOppdateringService.OppdaterJournalpostEksternNoekkelRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyDokument", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NyDokumentResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyDokument", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyDokument", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyDokument", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyDokument", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyDokument", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyDokument", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.NyDokumentResponse NyDokument(SakArkivOppdateringService.NyDokumentRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyDokument", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NyDokumentResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.NyDokumentResponse> NyDokumentAsync(SakArkivOppdateringService.NyDokumentRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettKorrespo" +
            "ndansepart", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/SlettKorrespondansepartResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettKorrespo" +
            "ndansepart", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettKorrespo" +
            "ndansepart", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettKorrespo" +
            "ndansepart", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettKorrespo" +
            "ndansepart", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettKorrespo" +
            "ndansepart", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettKorrespo" +
            "ndansepart", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.SlettKorrespondansepartResponse SlettKorrespondansepart(SakArkivOppdateringService.SlettKorrespondansepart request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettKorrespo" +
            "ndansepart", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/SlettKorrespondansepartResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.SlettKorrespondansepartResponse> SlettKorrespondansepartAsync(SakArkivOppdateringService.SlettKorrespondansepart request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyKorresponda" +
            "nsepart", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NyKorrespondansepartResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyKorresponda" +
            "nsepart", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyKorresponda" +
            "nsepart", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyKorresponda" +
            "nsepart", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyKorresponda" +
            "nsepart", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyKorresponda" +
            "nsepart", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyKorresponda" +
            "nsepart", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.NyKorrespondansepartResponse NyKorrespondansepart(SakArkivOppdateringService.NyKorrespondansepart request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyKorresponda" +
            "nsepart", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NyKorrespondansepartResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.NyKorrespondansepartResponse> NyKorrespondansepartAsync(SakArkivOppdateringService.NyKorrespondansepart request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterJourn" +
            "alpostAnsvarlig", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/OppdaterJournalpostAnsvarligResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterJourn" +
            "alpostAnsvarlig", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterJourn" +
            "alpostAnsvarlig", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterJourn" +
            "alpostAnsvarlig", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterJourn" +
            "alpostAnsvarlig", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterJourn" +
            "alpostAnsvarlig", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterJourn" +
            "alpostAnsvarlig", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.OppdaterJournalpostAnsvarligResponse OppdaterJournalpostAnsvarlig(SakArkivOppdateringService.OppdaterJournalpostAnsvarligRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterJourn" +
            "alpostAnsvarlig", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/OppdaterJournalpostAnsvarligResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.OppdaterJournalpostAnsvarligResponse> OppdaterJournalpostAnsvarligAsync(SakArkivOppdateringService.OppdaterJournalpostAnsvarligRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyJournalpost" +
            "", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NyJournalpostResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyJournalpost" +
            "", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyJournalpost" +
            "", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyJournalpost" +
            "", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyJournalpost" +
            "", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyJournalpost" +
            "", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyJournalpost" +
            "", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.NyJournalpostResponse NyJournalpost(SakArkivOppdateringService.NyJournalpostRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyJournalpost" +
            "", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NyJournalpostResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.NyJournalpostResponse> NyJournalpostAsync(SakArkivOppdateringService.NyJournalpostRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#FinnJournalpo" +
            "sterUnderArbeid", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/FinnJournalposterUnderArbeidResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#FinnJournalpo" +
            "sterUnderArbeid", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#FinnJournalpo" +
            "sterUnderArbeid", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#FinnJournalpo" +
            "sterUnderArbeid", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#FinnJournalpo" +
            "sterUnderArbeid", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#FinnJournalpo" +
            "sterUnderArbeid", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#FinnJournalpo" +
            "sterUnderArbeid", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.FinnJournalposterUnderArbeidResponse FinnJournalposterUnderArbeid(SakArkivOppdateringService.FinnJournalposterUnderArbeid request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#FinnJournalpo" +
            "sterUnderArbeid", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/FinnJournalposterUnderArbeidResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.FinnJournalposterUnderArbeidResponse> FinnJournalposterUnderArbeidAsync(SakArkivOppdateringService.FinnJournalposterUnderArbeid request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#FinnJournalpo" +
            "stRestanser", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/FinnJournalpostRestanserResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#FinnJournalpo" +
            "stRestanser", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#FinnJournalpo" +
            "stRestanser", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#FinnJournalpo" +
            "stRestanser", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#FinnJournalpo" +
            "stRestanser", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#FinnJournalpo" +
            "stRestanser", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#FinnJournalpo" +
            "stRestanser", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.FinnJournalpostRestanserResponse FinnJournalpostRestanser(SakArkivOppdateringService.FinnJournalpostRestanser request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#FinnJournalpo" +
            "stRestanser", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/FinnJournalpostRestanserResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.FinnJournalpostRestanserResponse> FinnJournalpostRestanserAsync(SakArkivOppdateringService.FinnJournalpostRestanser request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterPlan", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/OppdaterPlanResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterPlan", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterPlan", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterPlan", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterPlan", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterPlan", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterPlan", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.OppdaterPlanResponse OppdaterPlan(SakArkivOppdateringService.OppdaterPlanRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterPlan", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/OppdaterPlanResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.OppdaterPlanResponse> OppdaterPlanAsync(SakArkivOppdateringService.OppdaterPlanRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettSakspart" +
            "", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/SlettSakspartResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettSakspart" +
            "", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettSakspart" +
            "", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettSakspart" +
            "", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettSakspart" +
            "", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettSakspart" +
            "", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettSakspart" +
            "", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.SlettSakspartResponse SlettSakspart(SakArkivOppdateringService.SlettSakspart request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettSakspart" +
            "", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/SlettSakspartResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.SlettSakspartResponse> SlettSakspartAsync(SakArkivOppdateringService.SlettSakspart request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySakspart", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NySakspartResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySakspart", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySakspart", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySakspart", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySakspart", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySakspart", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySakspart", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.NySakspartResponse NySakspart(SakArkivOppdateringService.NySakspart request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySakspart", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NySakspartResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.NySakspartResponse> NySakspartAsync(SakArkivOppdateringService.NySakspart request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettPunkt", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/SlettPunktResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettPunkt", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettPunkt", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettPunkt", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettPunkt", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettPunkt", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettPunkt", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.SlettPunktResponse SlettPunkt(SakArkivOppdateringService.SlettPunkt request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettPunkt", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/SlettPunktResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.SlettPunktResponse> SlettPunktAsync(SakArkivOppdateringService.SlettPunkt request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyPunkt", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NyPunktResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyPunkt", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyPunkt", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyPunkt", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyPunkt", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyPunkt", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyPunkt", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.NyPunktResponse NyPunkt(SakArkivOppdateringService.NyPunkt request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyPunkt", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NyPunktResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.NyPunktResponse> NyPunktAsync(SakArkivOppdateringService.NyPunkt request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettBygning", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/SlettBygningResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettBygning", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettBygning", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettBygning", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettBygning", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettBygning", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettBygning", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.SlettBygningResponse SlettBygning(SakArkivOppdateringService.SlettBygning request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettBygning", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/SlettBygningResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.SlettBygningResponse> SlettBygningAsync(SakArkivOppdateringService.SlettBygning request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyBygning", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NyBygningResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyBygning", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyBygning", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyBygning", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyBygning", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyBygning", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyBygning", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.NyBygningResponse NyBygning(SakArkivOppdateringService.NyBygning request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyBygning", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NyBygningResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.NyBygningResponse> NyBygningAsync(SakArkivOppdateringService.NyBygning request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettMatrikke" +
            "lnummer", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/SlettMatrikkelnummerResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettMatrikke" +
            "lnummer", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettMatrikke" +
            "lnummer", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettMatrikke" +
            "lnummer", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettMatrikke" +
            "lnummer", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettMatrikke" +
            "lnummer", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettMatrikke" +
            "lnummer", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.SlettMatrikkelnummerResponse SlettMatrikkelnummer(SakArkivOppdateringService.SlettMatrikkelnummer request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#SlettMatrikke" +
            "lnummer", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/SlettMatrikkelnummerResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.SlettMatrikkelnummerResponse> SlettMatrikkelnummerAsync(SakArkivOppdateringService.SlettMatrikkelnummer request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyMatrikkelnu" +
            "mmer", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NyMatrikkelnummerResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyMatrikkelnu" +
            "mmer", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyMatrikkelnu" +
            "mmer", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyMatrikkelnu" +
            "mmer", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyMatrikkelnu" +
            "mmer", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyMatrikkelnu" +
            "mmer", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyMatrikkelnu" +
            "mmer", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.NyMatrikkelnummerResponse NyMatrikkelnummer(SakArkivOppdateringService.NyMatrikkelnummer request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NyMatrikkelnu" +
            "mmer", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NyMatrikkelnummerResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.NyMatrikkelnummerResponse> NyMatrikkelnummerAsync(SakArkivOppdateringService.NyMatrikkelnummer request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterMappe" +
            "Ansvarlig", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/OppdaterMappeAnsvarligResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterMappe" +
            "Ansvarlig", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterMappe" +
            "Ansvarlig", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterMappe" +
            "Ansvarlig", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterMappe" +
            "Ansvarlig", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterMappe" +
            "Ansvarlig", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterMappe" +
            "Ansvarlig", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.OppdaterMappeAnsvarligResponse OppdaterMappeAnsvarlig(SakArkivOppdateringService.OppdaterMappeAnsvarligRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterMappe" +
            "Ansvarlig", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/OppdaterMappeAnsvarligResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.OppdaterMappeAnsvarligResponse> OppdaterMappeAnsvarligAsync(SakArkivOppdateringService.OppdaterMappeAnsvarligRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterMappe" +
            "EksternNoekkel", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/OppdaterMappeEksternNoekkelResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterMappe" +
            "EksternNoekkel", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterMappe" +
            "EksternNoekkel", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterMappe" +
            "EksternNoekkel", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterMappe" +
            "EksternNoekkel", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterMappe" +
            "EksternNoekkel", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterMappe" +
            "EksternNoekkel", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.OppdaterMappeEksternNoekkelResponse OppdaterMappeEksternNoekkel(SakArkivOppdateringService.OppdaterMappeEksternNoekkelRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterMappe" +
            "EksternNoekkel", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/OppdaterMappeEksternNoekkelResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.OppdaterMappeEksternNoekkelResponse> OppdaterMappeEksternNoekkelAsync(SakArkivOppdateringService.OppdaterMappeEksternNoekkelRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterMappe" +
            "Status", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/OppdaterMappeStatusResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterMappe" +
            "Status", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterMappe" +
            "Status", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterMappe" +
            "Status", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterMappe" +
            "Status", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterMappe" +
            "Status", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterMappe" +
            "Status", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.OppdaterMappeStatusResponse OppdaterMappeStatus(SakArkivOppdateringService.OppdaterMappeStatusRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#OppdaterMappe" +
            "Status", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/OppdaterMappeStatusResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.OppdaterMappeStatusResponse> OppdaterMappeStatusAsync(SakArkivOppdateringService.OppdaterMappeStatusRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySaksmappe", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NySaksmappeResponse")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.SystemFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySaksmappe", Name="SystemFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ImplementationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySaksmappe", Name="ImplementationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.OperationalFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySaksmappe", Name="OperationalFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ApplicationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySaksmappe", Name="ApplicationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.FinderFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySaksmappe", Name="FinderFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.FaultContractAttribute(typeof(SakArkivOppdateringService.ValidationFault), Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySaksmappe", Name="ValidationFault", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GeointegrasjonFault))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kode))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(Kontekst))]
        SakArkivOppdateringService.NySaksmappeResponse NySaksmappe(SakArkivOppdateringService.NySaksmappeRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/#NySaksmappe", ReplyAction="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31/ISakArkivOppda" +
            "teringPort/NySaksmappeResponse")]
        System.Threading.Tasks.Task<SakArkivOppdateringService.NySaksmappeResponse> NySaksmappeAsync(SakArkivOppdateringService.NySaksmappeRequest request);
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SlettSaksmappeTilleggsinformasjon", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class SlettSaksmappeTilleggsinformasjon
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31", IsNullable=false)]
        public string[] systemID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.Saksnoekkel saksnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public SlettSaksmappeTilleggsinformasjon()
        {
        }
        
        public SlettSaksmappeTilleggsinformasjon(string[] systemID, SakArkivOppdateringService.Saksnoekkel saksnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.systemID = systemID;
            this.saksnokkel = saksnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SlettSaksmappeTilleggsinformasjonResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class SlettSaksmappeTilleggsinformasjonResponse
    {
        
        public SlettSaksmappeTilleggsinformasjonResponse()
        {
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SlettSaksmappeMerknad", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class SlettSaksmappeMerknad
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31", IsNullable=false)]
        public string[] systemID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.Saksnoekkel saksnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public SlettSaksmappeMerknad()
        {
        }
        
        public SlettSaksmappeMerknad(string[] systemID, SakArkivOppdateringService.Saksnoekkel saksnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.systemID = systemID;
            this.saksnokkel = saksnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SlettSaksmappeMerknadResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class SlettSaksmappeMerknadResponse
    {
        
        public SlettSaksmappeMerknadResponse()
        {
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(JournpostSystemID))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(JournpostEksternNoekkel))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Journalnummer))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Dokumentnummer))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Journpostnoekkel : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class JournpostSystemID : Journpostnoekkel
    {
        
        private SystemID systemIDField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public SystemID systemID
        {
            get
            {
                return this.systemIDField;
            }
            set
            {
                this.systemIDField = value;
                this.RaisePropertyChanged("systemID");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class JournpostEksternNoekkel : Journpostnoekkel
    {
        
        private EksternNoekkel eksternnoekkelField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public EksternNoekkel eksternnoekkel
        {
            get
            {
                return this.eksternnoekkelField;
            }
            set
            {
                this.eksternnoekkelField = value;
                this.RaisePropertyChanged("eksternnoekkel");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Journalnummer : Journpostnoekkel
    {
        
        private string journalaarField;
        
        private string journalsekvensnummerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=0)]
        public string journalaar
        {
            get
            {
                return this.journalaarField;
            }
            set
            {
                this.journalaarField = value;
                this.RaisePropertyChanged("journalaar");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=1)]
        public string journalsekvensnummer
        {
            get
            {
                return this.journalsekvensnummerField;
            }
            set
            {
                this.journalsekvensnummerField = value;
                this.RaisePropertyChanged("journalsekvensnummer");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Dokumentnummer : Journpostnoekkel
    {
        
        private string saksaarField;
        
        private string sakssekvensnummerField;
        
        private string journalpostnummerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=0)]
        public string saksaar
        {
            get
            {
                return this.saksaarField;
            }
            set
            {
                this.saksaarField = value;
                this.RaisePropertyChanged("saksaar");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=1)]
        public string sakssekvensnummer
        {
            get
            {
                return this.sakssekvensnummerField;
            }
            set
            {
                this.sakssekvensnummerField = value;
                this.RaisePropertyChanged("sakssekvensnummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=2)]
        public string journalpostnummer
        {
            get
            {
                return this.journalpostnummerField;
            }
            set
            {
                this.journalpostnummerField = value;
                this.RaisePropertyChanged("journalpostnummer");
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SlettJournalpostTilleggsinformasjon", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class SlettJournalpostTilleggsinformasjon
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31", IsNullable=false)]
        public string[] systemID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.Journpostnoekkel journalnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public SlettJournalpostTilleggsinformasjon()
        {
        }
        
        public SlettJournalpostTilleggsinformasjon(string[] systemID, SakArkivOppdateringService.Journpostnoekkel journalnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.systemID = systemID;
            this.journalnokkel = journalnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SlettJournalpostTilleggsinformasjonResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class SlettJournalpostTilleggsinformasjonResponse
    {
        
        public SlettJournalpostTilleggsinformasjonResponse()
        {
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SlettJournalpostMerknad", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class SlettJournalpostMerknad
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31", IsNullable=false)]
        public string[] systemID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.Journpostnoekkel journalnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public SlettJournalpostMerknad()
        {
        }
        
        public SlettJournalpostMerknad(string[] systemID, SakArkivOppdateringService.Journpostnoekkel journalnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.systemID = systemID;
            this.journalnokkel = journalnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SlettJournalpostMerknadResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class SlettJournalpostMerknadResponse
    {
        
        public SlettJournalpostMerknadResponse()
        {
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Tilleggsinformasjon : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string systemIDField;
        
        private string rekkefoelgeField;
        
        private Informasjonstype informasjonstypeField;
        
        private Tilgangsrestriksjon tilgangsrestriksjonField;
        
        private System.DateTime oppbevaresTilDatoField;
        
        private bool oppbevaresTilDatoFieldSpecified;
        
        private string informasjonField;
        
        private string tilgangsgruppeNavnField;
        
        private System.DateTime registrertDatoField;
        
        private bool registrertDatoFieldSpecified;
        
        private string registrertAvField;
        
        private string registrertAvInitField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string systemID
        {
            get
            {
                return this.systemIDField;
            }
            set
            {
                this.systemIDField = value;
                this.RaisePropertyChanged("systemID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string rekkefoelge
        {
            get
            {
                return this.rekkefoelgeField;
            }
            set
            {
                this.rekkefoelgeField = value;
                this.RaisePropertyChanged("rekkefoelge");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public Informasjonstype informasjonstype
        {
            get
            {
                return this.informasjonstypeField;
            }
            set
            {
                this.informasjonstypeField = value;
                this.RaisePropertyChanged("informasjonstype");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public Tilgangsrestriksjon tilgangsrestriksjon
        {
            get
            {
                return this.tilgangsrestriksjonField;
            }
            set
            {
                this.tilgangsrestriksjonField = value;
                this.RaisePropertyChanged("tilgangsrestriksjon");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public System.DateTime oppbevaresTilDato
        {
            get
            {
                return this.oppbevaresTilDatoField;
            }
            set
            {
                this.oppbevaresTilDatoField = value;
                this.RaisePropertyChanged("oppbevaresTilDato");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool oppbevaresTilDatoSpecified
        {
            get
            {
                return this.oppbevaresTilDatoFieldSpecified;
            }
            set
            {
                this.oppbevaresTilDatoFieldSpecified = value;
                this.RaisePropertyChanged("oppbevaresTilDatoSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string informasjon
        {
            get
            {
                return this.informasjonField;
            }
            set
            {
                this.informasjonField = value;
                this.RaisePropertyChanged("informasjon");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public string tilgangsgruppeNavn
        {
            get
            {
                return this.tilgangsgruppeNavnField;
            }
            set
            {
                this.tilgangsgruppeNavnField = value;
                this.RaisePropertyChanged("tilgangsgruppeNavn");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public System.DateTime registrertDato
        {
            get
            {
                return this.registrertDatoField;
            }
            set
            {
                this.registrertDatoField = value;
                this.RaisePropertyChanged("registrertDato");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool registrertDatoSpecified
        {
            get
            {
                return this.registrertDatoFieldSpecified;
            }
            set
            {
                this.registrertDatoFieldSpecified = value;
                this.RaisePropertyChanged("registrertDatoSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public string registrertAv
        {
            get
            {
                return this.registrertAvField;
            }
            set
            {
                this.registrertAvField = value;
                this.RaisePropertyChanged("registrertAv");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public string registrertAvInit
        {
            get
            {
                return this.registrertAvInitField;
            }
            set
            {
                this.registrertAvInitField = value;
                this.RaisePropertyChanged("registrertAvInit");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NySaksmappeTilleggsinformasjon", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NySaksmappeTilleggsinformasjon
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31", IsNullable=false)]
        public SakArkivOppdateringService.Tilleggsinformasjon[] tilleggsinfo;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.Saksnoekkel saksnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public NySaksmappeTilleggsinformasjon()
        {
        }
        
        public NySaksmappeTilleggsinformasjon(SakArkivOppdateringService.Tilleggsinformasjon[] tilleggsinfo, SakArkivOppdateringService.Saksnoekkel saksnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.tilleggsinfo = tilleggsinfo;
            this.saksnokkel = saksnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NySaksmappeTilleggsinformasjonResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NySaksmappeTilleggsinformasjonResponse
    {
        
        public NySaksmappeTilleggsinformasjonResponse()
        {
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Merknad : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string systemIDField;
        
        private string merknadstekstField;
        
        private string merknadstypeField;
        
        private System.DateTime merknadsdatoField;
        
        private bool merknadsdatoFieldSpecified;
        
        private string merknadRegistrertAvField;
        
        private string merknadRegistrertAvInitField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string systemID
        {
            get
            {
                return this.systemIDField;
            }
            set
            {
                this.systemIDField = value;
                this.RaisePropertyChanged("systemID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string merknadstekst
        {
            get
            {
                return this.merknadstekstField;
            }
            set
            {
                this.merknadstekstField = value;
                this.RaisePropertyChanged("merknadstekst");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string merknadstype
        {
            get
            {
                return this.merknadstypeField;
            }
            set
            {
                this.merknadstypeField = value;
                this.RaisePropertyChanged("merknadstype");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public System.DateTime merknadsdato
        {
            get
            {
                return this.merknadsdatoField;
            }
            set
            {
                this.merknadsdatoField = value;
                this.RaisePropertyChanged("merknadsdato");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool merknadsdatoSpecified
        {
            get
            {
                return this.merknadsdatoFieldSpecified;
            }
            set
            {
                this.merknadsdatoFieldSpecified = value;
                this.RaisePropertyChanged("merknadsdatoSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string merknadRegistrertAv
        {
            get
            {
                return this.merknadRegistrertAvField;
            }
            set
            {
                this.merknadRegistrertAvField = value;
                this.RaisePropertyChanged("merknadRegistrertAv");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string merknadRegistrertAvInit
        {
            get
            {
                return this.merknadRegistrertAvInitField;
            }
            set
            {
                this.merknadRegistrertAvInitField = value;
                this.RaisePropertyChanged("merknadRegistrertAvInit");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NySaksmappeMerknad", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NySaksmappeMerknad
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31", IsNullable=false)]
        public SakArkivOppdateringService.Merknad[] merknad;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.Saksnoekkel saksnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public NySaksmappeMerknad()
        {
        }
        
        public NySaksmappeMerknad(SakArkivOppdateringService.Merknad[] merknad, SakArkivOppdateringService.Saksnoekkel saksnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.merknad = merknad;
            this.saksnokkel = saksnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NySaksmappeMerknadResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NySaksmappeMerknadResponse
    {
        
        public NySaksmappeMerknadResponse()
        {
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NyJournalpostTilleggsinformasjon", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NyJournalpostTilleggsinformasjon
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31", IsNullable=false)]
        public SakArkivOppdateringService.Tilleggsinformasjon[] tilleggsinfo;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.Journpostnoekkel journalnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public NyJournalpostTilleggsinformasjon()
        {
        }
        
        public NyJournalpostTilleggsinformasjon(SakArkivOppdateringService.Tilleggsinformasjon[] tilleggsinfo, SakArkivOppdateringService.Journpostnoekkel journalnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.tilleggsinfo = tilleggsinfo;
            this.journalnokkel = journalnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NyJournalpostTilleggsinformasjonResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NyJournalpostTilleggsinformasjonResponse
    {
        
        public NyJournalpostTilleggsinformasjonResponse()
        {
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NyJournalpostMerknad", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NyJournalpostMerknad
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31", IsNullable=false)]
        public SakArkivOppdateringService.Merknad[] merknad;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.Journpostnoekkel journalnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public NyJournalpostMerknad()
        {
        }
        
        public NyJournalpostMerknad(SakArkivOppdateringService.Merknad[] merknad, SakArkivOppdateringService.Journpostnoekkel journalnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.merknad = merknad;
            this.journalnokkel = journalnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NyJournalpostMerknadResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NyJournalpostMerknadResponse
    {
        
        public NyJournalpostMerknadResponse()
        {
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SlettAvskrivning", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class SlettAvskrivning
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31", IsNullable=false)]
        public string[] systemID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.Journpostnoekkel journalnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public SlettAvskrivning()
        {
        }
        
        public SlettAvskrivning(string[] systemID, SakArkivOppdateringService.Journpostnoekkel journalnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.systemID = systemID;
            this.journalnokkel = journalnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SlettAvskrivningResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class SlettAvskrivningResponse
    {
        
        public SlettAvskrivningResponse()
        {
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Avskrivning : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string systemIDField;
        
        private System.DateTime avskrivningsdatoField;
        
        private bool avskrivningsdatoFieldSpecified;
        
        private string avskrevetAvField;
        
        private Avskrivningsmaate avskrivningsmaateField;
        
        private Journalnummer referanseAvskriverJournalnummerField;
        
        private Journalnummer referanseAvskrivesAvJournalnummerField;
        
        private EksternNoekkel referanseAvskriverEksternNoekkelField;
        
        private EksternNoekkel referanseAvskrivesAvEksternNoekkelField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string systemID
        {
            get
            {
                return this.systemIDField;
            }
            set
            {
                this.systemIDField = value;
                this.RaisePropertyChanged("systemID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public System.DateTime avskrivningsdato
        {
            get
            {
                return this.avskrivningsdatoField;
            }
            set
            {
                this.avskrivningsdatoField = value;
                this.RaisePropertyChanged("avskrivningsdato");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool avskrivningsdatoSpecified
        {
            get
            {
                return this.avskrivningsdatoFieldSpecified;
            }
            set
            {
                this.avskrivningsdatoFieldSpecified = value;
                this.RaisePropertyChanged("avskrivningsdatoSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string avskrevetAv
        {
            get
            {
                return this.avskrevetAvField;
            }
            set
            {
                this.avskrevetAvField = value;
                this.RaisePropertyChanged("avskrevetAv");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public Avskrivningsmaate avskrivningsmaate
        {
            get
            {
                return this.avskrivningsmaateField;
            }
            set
            {
                this.avskrivningsmaateField = value;
                this.RaisePropertyChanged("avskrivningsmaate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public Journalnummer referanseAvskriverJournalnummer
        {
            get
            {
                return this.referanseAvskriverJournalnummerField;
            }
            set
            {
                this.referanseAvskriverJournalnummerField = value;
                this.RaisePropertyChanged("referanseAvskriverJournalnummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public Journalnummer referanseAvskrivesAvJournalnummer
        {
            get
            {
                return this.referanseAvskrivesAvJournalnummerField;
            }
            set
            {
                this.referanseAvskrivesAvJournalnummerField = value;
                this.RaisePropertyChanged("referanseAvskrivesAvJournalnummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public EksternNoekkel referanseAvskriverEksternNoekkel
        {
            get
            {
                return this.referanseAvskriverEksternNoekkelField;
            }
            set
            {
                this.referanseAvskriverEksternNoekkelField = value;
                this.RaisePropertyChanged("referanseAvskriverEksternNoekkel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public EksternNoekkel referanseAvskrivesAvEksternNoekkel
        {
            get
            {
                return this.referanseAvskrivesAvEksternNoekkelField;
            }
            set
            {
                this.referanseAvskrivesAvEksternNoekkelField = value;
                this.RaisePropertyChanged("referanseAvskrivesAvEksternNoekkel");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NyAvskrivning", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NyAvskrivning
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31", IsNullable=false)]
        public SakArkivOppdateringService.Avskrivning[] avskrivning;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.Journpostnoekkel journalnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public NyAvskrivning()
        {
        }
        
        public NyAvskrivning(SakArkivOppdateringService.Avskrivning[] avskrivning, SakArkivOppdateringService.Journpostnoekkel journalnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.avskrivning = avskrivning;
            this.journalnokkel = journalnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NyAvskrivningResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NyAvskrivningResponse
    {
        
        public NyAvskrivningResponse()
        {
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="OppdaterJournalpostStatus", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class OppdaterJournalpostStatusRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        public SakArkivOppdateringService.Journalstatus journalstatus;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.Journpostnoekkel journalpostnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public OppdaterJournalpostStatusRequest()
        {
        }
        
        public OppdaterJournalpostStatusRequest(SakArkivOppdateringService.Journalstatus journalstatus, SakArkivOppdateringService.Journpostnoekkel journalpostnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.journalstatus = journalstatus;
            this.journalpostnokkel = journalpostnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="OppdaterJournalpostStatusResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class OppdaterJournalpostStatusResponse
    {
        
        public OppdaterJournalpostStatusResponse()
        {
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Journalpost : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string systemIDField;
        
        private Journalnummer journalnummerField;
        
        private string journalpostnummerField;
        
        private System.DateTime journaldatoField;
        
        private bool journaldatoFieldSpecified;
        
        private Journalposttype journalposttypeField;
        
        private System.DateTime dokumentetsDatoField;
        
        private bool dokumentetsDatoFieldSpecified;
        
        private Journalstatus journalstatusField;
        
        private string tittelField;
        
        private bool skjermetTittelField;
        
        private bool skjermetTittelFieldSpecified;
        
        private System.DateTime forfallsdatoField;
        
        private bool forfallsdatoFieldSpecified;
        
        private Skjerming skjermingField;
        
        private Arkivdel referanseArkivdelField;
        
        private string tilleggskodeField;
        
        private string antallVedleggField;
        
        private string offentligTittelField;
        
        private Saksnummer saksnrField;
        
        private string tilgangsgruppeNavnField;
        
        private SakSystemId referanseSakSystemIDField;
        
        private Korrespondansepart[] korrespondansepartField;
        
        private EksternNoekkel referanseEksternNoekkelField;
        
        private EksternNoekkel referanseMappeEksternNoekkelField;
        
        private Avskrivning[] referanseAvskrivningerField;
        
        private Merknad[] merknaderField;
        
        private Tilleggsinformasjon[] tilleggsinformasjonField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string systemID
        {
            get
            {
                return this.systemIDField;
            }
            set
            {
                this.systemIDField = value;
                this.RaisePropertyChanged("systemID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public Journalnummer journalnummer
        {
            get
            {
                return this.journalnummerField;
            }
            set
            {
                this.journalnummerField = value;
                this.RaisePropertyChanged("journalnummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string journalpostnummer
        {
            get
            {
                return this.journalpostnummerField;
            }
            set
            {
                this.journalpostnummerField = value;
                this.RaisePropertyChanged("journalpostnummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public System.DateTime journaldato
        {
            get
            {
                return this.journaldatoField;
            }
            set
            {
                this.journaldatoField = value;
                this.RaisePropertyChanged("journaldato");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool journaldatoSpecified
        {
            get
            {
                return this.journaldatoFieldSpecified;
            }
            set
            {
                this.journaldatoFieldSpecified = value;
                this.RaisePropertyChanged("journaldatoSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public Journalposttype journalposttype
        {
            get
            {
                return this.journalposttypeField;
            }
            set
            {
                this.journalposttypeField = value;
                this.RaisePropertyChanged("journalposttype");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public System.DateTime dokumentetsDato
        {
            get
            {
                return this.dokumentetsDatoField;
            }
            set
            {
                this.dokumentetsDatoField = value;
                this.RaisePropertyChanged("dokumentetsDato");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool dokumentetsDatoSpecified
        {
            get
            {
                return this.dokumentetsDatoFieldSpecified;
            }
            set
            {
                this.dokumentetsDatoFieldSpecified = value;
                this.RaisePropertyChanged("dokumentetsDatoSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public Journalstatus journalstatus
        {
            get
            {
                return this.journalstatusField;
            }
            set
            {
                this.journalstatusField = value;
                this.RaisePropertyChanged("journalstatus");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public string tittel
        {
            get
            {
                return this.tittelField;
            }
            set
            {
                this.tittelField = value;
                this.RaisePropertyChanged("tittel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public bool skjermetTittel
        {
            get
            {
                return this.skjermetTittelField;
            }
            set
            {
                this.skjermetTittelField = value;
                this.RaisePropertyChanged("skjermetTittel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool skjermetTittelSpecified
        {
            get
            {
                return this.skjermetTittelFieldSpecified;
            }
            set
            {
                this.skjermetTittelFieldSpecified = value;
                this.RaisePropertyChanged("skjermetTittelSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public System.DateTime forfallsdato
        {
            get
            {
                return this.forfallsdatoField;
            }
            set
            {
                this.forfallsdatoField = value;
                this.RaisePropertyChanged("forfallsdato");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool forfallsdatoSpecified
        {
            get
            {
                return this.forfallsdatoFieldSpecified;
            }
            set
            {
                this.forfallsdatoFieldSpecified = value;
                this.RaisePropertyChanged("forfallsdatoSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=10)]
        public Skjerming skjerming
        {
            get
            {
                return this.skjermingField;
            }
            set
            {
                this.skjermingField = value;
                this.RaisePropertyChanged("skjerming");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=11)]
        public Arkivdel referanseArkivdel
        {
            get
            {
                return this.referanseArkivdelField;
            }
            set
            {
                this.referanseArkivdelField = value;
                this.RaisePropertyChanged("referanseArkivdel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=12)]
        public string tilleggskode
        {
            get
            {
                return this.tilleggskodeField;
            }
            set
            {
                this.tilleggskodeField = value;
                this.RaisePropertyChanged("tilleggskode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=13)]
        public string antallVedlegg
        {
            get
            {
                return this.antallVedleggField;
            }
            set
            {
                this.antallVedleggField = value;
                this.RaisePropertyChanged("antallVedlegg");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=14)]
        public string offentligTittel
        {
            get
            {
                return this.offentligTittelField;
            }
            set
            {
                this.offentligTittelField = value;
                this.RaisePropertyChanged("offentligTittel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=15)]
        public Saksnummer saksnr
        {
            get
            {
                return this.saksnrField;
            }
            set
            {
                this.saksnrField = value;
                this.RaisePropertyChanged("saksnr");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=16)]
        public string tilgangsgruppeNavn
        {
            get
            {
                return this.tilgangsgruppeNavnField;
            }
            set
            {
                this.tilgangsgruppeNavnField = value;
                this.RaisePropertyChanged("tilgangsgruppeNavn");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=17)]
        public SakSystemId referanseSakSystemID
        {
            get
            {
                return this.referanseSakSystemIDField;
            }
            set
            {
                this.referanseSakSystemIDField = value;
                this.RaisePropertyChanged("referanseSakSystemID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=18)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", IsNullable=false)]
        public Korrespondansepart[] korrespondansepart
        {
            get
            {
                return this.korrespondansepartField;
            }
            set
            {
                this.korrespondansepartField = value;
                this.RaisePropertyChanged("korrespondansepart");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=19)]
        public EksternNoekkel referanseEksternNoekkel
        {
            get
            {
                return this.referanseEksternNoekkelField;
            }
            set
            {
                this.referanseEksternNoekkelField = value;
                this.RaisePropertyChanged("referanseEksternNoekkel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=20)]
        public EksternNoekkel referanseMappeEksternNoekkel
        {
            get
            {
                return this.referanseMappeEksternNoekkelField;
            }
            set
            {
                this.referanseMappeEksternNoekkelField = value;
                this.RaisePropertyChanged("referanseMappeEksternNoekkel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=21)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", IsNullable=false)]
        public Avskrivning[] referanseAvskrivninger
        {
            get
            {
                return this.referanseAvskrivningerField;
            }
            set
            {
                this.referanseAvskrivningerField = value;
                this.RaisePropertyChanged("referanseAvskrivninger");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=22)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", IsNullable=false)]
        public Merknad[] merknader
        {
            get
            {
                return this.merknaderField;
            }
            set
            {
                this.merknaderField = value;
                this.RaisePropertyChanged("merknader");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=23)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", IsNullable=false)]
        public Tilleggsinformasjon[] tilleggsinformasjon
        {
            get
            {
                return this.tilleggsinformasjonField;
            }
            set
            {
                this.tilleggsinformasjonField = value;
                this.RaisePropertyChanged("tilleggsinformasjon");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Skjerming : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private Tilgangsrestriksjon tilgangsrestriksjonField;
        
        private string skjermingshjemmelField;
        
        private System.DateTime skjermingOpphoererDatoField;
        
        private bool skjermingOpphoererDatoFieldSpecified;
        
        private SkjermingOpphorerAksjon skjermingOpphoererAksjonField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public Tilgangsrestriksjon tilgangsrestriksjon
        {
            get
            {
                return this.tilgangsrestriksjonField;
            }
            set
            {
                this.tilgangsrestriksjonField = value;
                this.RaisePropertyChanged("tilgangsrestriksjon");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string skjermingshjemmel
        {
            get
            {
                return this.skjermingshjemmelField;
            }
            set
            {
                this.skjermingshjemmelField = value;
                this.RaisePropertyChanged("skjermingshjemmel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public System.DateTime skjermingOpphoererDato
        {
            get
            {
                return this.skjermingOpphoererDatoField;
            }
            set
            {
                this.skjermingOpphoererDatoField = value;
                this.RaisePropertyChanged("skjermingOpphoererDato");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool skjermingOpphoererDatoSpecified
        {
            get
            {
                return this.skjermingOpphoererDatoFieldSpecified;
            }
            set
            {
                this.skjermingOpphoererDatoFieldSpecified = value;
                this.RaisePropertyChanged("skjermingOpphoererDatoSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public SkjermingOpphorerAksjon skjermingOpphoererAksjon
        {
            get
            {
                return this.skjermingOpphoererAksjonField;
            }
            set
            {
                this.skjermingOpphoererAksjonField = value;
                this.RaisePropertyChanged("skjermingOpphoererAksjon");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Korrespondansepart : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string systemIDField;
        
        private Korrespondanseparttype korrespondanseparttypeField;
        
        private string behandlingsansvarligField;
        
        private bool skjermetKorrespondansepartField;
        
        private bool skjermetKorrespondansepartFieldSpecified;
        
        private string kortnavnField;
        
        private string deresReferanseField;
        
        private Journalenhet journalenhetField;
        
        private System.DateTime fristBesvarelseField;
        
        private bool fristBesvarelseFieldSpecified;
        
        private Forsendelsesmaate forsendelsesmaateField;
        
        private string administrativEnhetInitField;
        
        private string administrativEnhetField;
        
        private string saksbehandlerInitField;
        
        private string saksbehandlerField;
        
        private Kontakt kontaktField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string systemID
        {
            get
            {
                return this.systemIDField;
            }
            set
            {
                this.systemIDField = value;
                this.RaisePropertyChanged("systemID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public Korrespondanseparttype korrespondanseparttype
        {
            get
            {
                return this.korrespondanseparttypeField;
            }
            set
            {
                this.korrespondanseparttypeField = value;
                this.RaisePropertyChanged("korrespondanseparttype");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string behandlingsansvarlig
        {
            get
            {
                return this.behandlingsansvarligField;
            }
            set
            {
                this.behandlingsansvarligField = value;
                this.RaisePropertyChanged("behandlingsansvarlig");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public bool skjermetKorrespondansepart
        {
            get
            {
                return this.skjermetKorrespondansepartField;
            }
            set
            {
                this.skjermetKorrespondansepartField = value;
                this.RaisePropertyChanged("skjermetKorrespondansepart");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool skjermetKorrespondansepartSpecified
        {
            get
            {
                return this.skjermetKorrespondansepartFieldSpecified;
            }
            set
            {
                this.skjermetKorrespondansepartFieldSpecified = value;
                this.RaisePropertyChanged("skjermetKorrespondansepartSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string kortnavn
        {
            get
            {
                return this.kortnavnField;
            }
            set
            {
                this.kortnavnField = value;
                this.RaisePropertyChanged("kortnavn");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string deresReferanse
        {
            get
            {
                return this.deresReferanseField;
            }
            set
            {
                this.deresReferanseField = value;
                this.RaisePropertyChanged("deresReferanse");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public Journalenhet journalenhet
        {
            get
            {
                return this.journalenhetField;
            }
            set
            {
                this.journalenhetField = value;
                this.RaisePropertyChanged("journalenhet");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public System.DateTime fristBesvarelse
        {
            get
            {
                return this.fristBesvarelseField;
            }
            set
            {
                this.fristBesvarelseField = value;
                this.RaisePropertyChanged("fristBesvarelse");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool fristBesvarelseSpecified
        {
            get
            {
                return this.fristBesvarelseFieldSpecified;
            }
            set
            {
                this.fristBesvarelseFieldSpecified = value;
                this.RaisePropertyChanged("fristBesvarelseSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public Forsendelsesmaate forsendelsesmaate
        {
            get
            {
                return this.forsendelsesmaateField;
            }
            set
            {
                this.forsendelsesmaateField = value;
                this.RaisePropertyChanged("forsendelsesmaate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public string administrativEnhetInit
        {
            get
            {
                return this.administrativEnhetInitField;
            }
            set
            {
                this.administrativEnhetInitField = value;
                this.RaisePropertyChanged("administrativEnhetInit");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=10)]
        public string administrativEnhet
        {
            get
            {
                return this.administrativEnhetField;
            }
            set
            {
                this.administrativEnhetField = value;
                this.RaisePropertyChanged("administrativEnhet");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=11)]
        public string saksbehandlerInit
        {
            get
            {
                return this.saksbehandlerInitField;
            }
            set
            {
                this.saksbehandlerInitField = value;
                this.RaisePropertyChanged("saksbehandlerInit");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=12)]
        public string saksbehandler
        {
            get
            {
                return this.saksbehandlerField;
            }
            set
            {
                this.saksbehandlerField = value;
                this.RaisePropertyChanged("saksbehandler");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=13)]
        public Kontakt Kontakt
        {
            get
            {
                return this.kontaktField;
            }
            set
            {
                this.kontaktField = value;
                this.RaisePropertyChanged("Kontakt");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Person))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Organisasjon))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Kontakt/xml.schema/2012.01.31")]
    public partial class Kontakt : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string navnField;
        
        private EnkelAdresse[] adresserField;
        
        private ElektroniskAdresse[] elektroniskeAdresserField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string navn
        {
            get
            {
                return this.navnField;
            }
            set
            {
                this.navnField = value;
                this.RaisePropertyChanged("navn");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=1)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31", IsNullable=false)]
        public EnkelAdresse[] adresser
        {
            get
            {
                return this.adresserField;
            }
            set
            {
                this.adresserField = value;
                this.RaisePropertyChanged("adresser");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=2)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31", IsNullable=false)]
        public ElektroniskAdresse[] elektroniskeAdresser
        {
            get
            {
                return this.elektroniskeAdresserField;
            }
            set
            {
                this.elektroniskeAdresserField = value;
                this.RaisePropertyChanged("elektroniskeAdresser");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    public partial class EnkelAdresse : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private EnkelAdressetype adressetypeField;
        
        private string adresselinje1Field;
        
        private string adresselinje2Field;
        
        private PostadministrativeOmraader postadresseField;
        
        private Landkode landkodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public EnkelAdressetype adressetype
        {
            get
            {
                return this.adressetypeField;
            }
            set
            {
                this.adressetypeField = value;
                this.RaisePropertyChanged("adressetype");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string adresselinje1
        {
            get
            {
                return this.adresselinje1Field;
            }
            set
            {
                this.adresselinje1Field = value;
                this.RaisePropertyChanged("adresselinje1");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string adresselinje2
        {
            get
            {
                return this.adresselinje2Field;
            }
            set
            {
                this.adresselinje2Field = value;
                this.RaisePropertyChanged("adresselinje2");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public PostadministrativeOmraader postadresse
        {
            get
            {
                return this.postadresseField;
            }
            set
            {
                this.postadresseField = value;
                this.RaisePropertyChanged("postadresse");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public Landkode landkode
        {
            get
            {
                return this.landkodeField;
            }
            set
            {
                this.landkodeField = value;
                this.RaisePropertyChanged("landkode");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    public partial class PostadministrativeOmraader : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string postnummerField;
        
        private string poststedField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string postnummer
        {
            get
            {
                return this.postnummerField;
            }
            set
            {
                this.postnummerField = value;
                this.RaisePropertyChanged("postnummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string poststed
        {
            get
            {
                return this.poststedField;
            }
            set
            {
                this.poststedField = value;
                this.RaisePropertyChanged("poststed");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Meldingsboks))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Telefon))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Faks))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Epost))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    public partial class ElektroniskAdresse : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    public partial class Meldingsboks : ElektroniskAdresse
    {
        
        private string tilbyderField;
        
        private string meldingsboksadresseField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string tilbyder
        {
            get
            {
                return this.tilbyderField;
            }
            set
            {
                this.tilbyderField = value;
                this.RaisePropertyChanged("tilbyder");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string meldingsboksadresse
        {
            get
            {
                return this.meldingsboksadresseField;
            }
            set
            {
                this.meldingsboksadresseField = value;
                this.RaisePropertyChanged("meldingsboksadresse");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    public partial class Telefon : ElektroniskAdresse
    {
        
        private string telefonnummerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string telefonnummer
        {
            get
            {
                return this.telefonnummerField;
            }
            set
            {
                this.telefonnummerField = value;
                this.RaisePropertyChanged("telefonnummer");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    public partial class Faks : ElektroniskAdresse
    {
        
        private string faksnummerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string faksnummer
        {
            get
            {
                return this.faksnummerField;
            }
            set
            {
                this.faksnummerField = value;
                this.RaisePropertyChanged("faksnummer");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Adresse/xml.schema/2012.01.31")]
    public partial class Epost : ElektroniskAdresse
    {
        
        private string epostadresseField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string epostadresse
        {
            get
            {
                return this.epostadresseField;
            }
            set
            {
                this.epostadresseField = value;
                this.RaisePropertyChanged("epostadresse");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Kontakt/xml.schema/2012.01.31")]
    public partial class Person : Kontakt
    {
        
        private Personidentifikator personidField;
        
        private string etternavnField;
        
        private string fornavnField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public Personidentifikator personid
        {
            get
            {
                return this.personidField;
            }
            set
            {
                this.personidField = value;
                this.RaisePropertyChanged("personid");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string etternavn
        {
            get
            {
                return this.etternavnField;
            }
            set
            {
                this.etternavnField = value;
                this.RaisePropertyChanged("etternavn");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string fornavn
        {
            get
            {
                return this.fornavnField;
            }
            set
            {
                this.fornavnField = value;
                this.RaisePropertyChanged("fornavn");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Kontakt/xml.schema/2012.01.31")]
    public partial class Personidentifikator : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string personidentifikatorNrField;
        
        private PersonidentifikatorType personidentifikatorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string personidentifikatorNr
        {
            get
            {
                return this.personidentifikatorNrField;
            }
            set
            {
                this.personidentifikatorNrField = value;
                this.RaisePropertyChanged("personidentifikatorNr");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public PersonidentifikatorType personidentifikatorType
        {
            get
            {
                return this.personidentifikatorTypeField;
            }
            set
            {
                this.personidentifikatorTypeField = value;
                this.RaisePropertyChanged("personidentifikatorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Kontakt/xml.schema/2012.01.31")]
    public partial class Organisasjon : Kontakt
    {
        
        private string organisasjonsnummerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string organisasjonsnummer
        {
            get
            {
                return this.organisasjonsnummerField;
            }
            set
            {
                this.organisasjonsnummerField = value;
                this.RaisePropertyChanged("organisasjonsnummer");
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="OppdaterJournalpostEksternNoekkel", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class OppdaterJournalpostEksternNoekkelRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        public SakArkivOppdateringService.EksternNoekkel nokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.Journpostnoekkel journalpostnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public OppdaterJournalpostEksternNoekkelRequest()
        {
        }
        
        public OppdaterJournalpostEksternNoekkelRequest(SakArkivOppdateringService.EksternNoekkel nokkel, SakArkivOppdateringService.Journpostnoekkel journalpostnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.nokkel = nokkel;
            this.journalpostnokkel = journalpostnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="OppdaterJournalpostEksternNoekkelResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class OppdaterJournalpostEksternNoekkelResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        public SakArkivOppdateringService.Journalpost @return;
        
        public OppdaterJournalpostEksternNoekkelResponse()
        {
        }
        
        public OppdaterJournalpostEksternNoekkelResponse(SakArkivOppdateringService.Journalpost @return)
        {
            this.@return = @return;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    public partial class Dokument : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string systemIDField;
        
        private string dokumentnummerField;
        
        private TilknyttetRegistreringSom tilknyttetRegistreringSomField;
        
        private Dokumenttype dokumenttypeField;
        
        private string tittelField;
        
        private Dokumentstatus dokumentstatusField;
        
        private Variantformat variantformatField;
        
        private Format formatField;
        
        private string referanseJournalpostSystemIDField;
        
        private Fil filField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string systemID
        {
            get
            {
                return this.systemIDField;
            }
            set
            {
                this.systemIDField = value;
                this.RaisePropertyChanged("systemID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string dokumentnummer
        {
            get
            {
                return this.dokumentnummerField;
            }
            set
            {
                this.dokumentnummerField = value;
                this.RaisePropertyChanged("dokumentnummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public TilknyttetRegistreringSom tilknyttetRegistreringSom
        {
            get
            {
                return this.tilknyttetRegistreringSomField;
            }
            set
            {
                this.tilknyttetRegistreringSomField = value;
                this.RaisePropertyChanged("tilknyttetRegistreringSom");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public Dokumenttype dokumenttype
        {
            get
            {
                return this.dokumenttypeField;
            }
            set
            {
                this.dokumenttypeField = value;
                this.RaisePropertyChanged("dokumenttype");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string tittel
        {
            get
            {
                return this.tittelField;
            }
            set
            {
                this.tittelField = value;
                this.RaisePropertyChanged("tittel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public Dokumentstatus dokumentstatus
        {
            get
            {
                return this.dokumentstatusField;
            }
            set
            {
                this.dokumentstatusField = value;
                this.RaisePropertyChanged("dokumentstatus");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public Variantformat variantformat
        {
            get
            {
                return this.variantformatField;
            }
            set
            {
                this.variantformatField = value;
                this.RaisePropertyChanged("variantformat");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public Format format
        {
            get
            {
                return this.formatField;
            }
            set
            {
                this.formatField = value;
                this.RaisePropertyChanged("format");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public string referanseJournalpostSystemID
        {
            get
            {
                return this.referanseJournalpostSystemIDField;
            }
            set
            {
                this.referanseJournalpostSystemIDField = value;
                this.RaisePropertyChanged("referanseJournalpostSystemID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public Fil Fil
        {
            get
            {
                return this.filField;
            }
            set
            {
                this.filField = value;
                this.RaisePropertyChanged("Fil");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Filreferanse))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Filinnhold))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    public partial class Fil : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string filnavnField;
        
        private string mimeTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string filnavn
        {
            get
            {
                return this.filnavnField;
            }
            set
            {
                this.filnavnField = value;
                this.RaisePropertyChanged("filnavn");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string mimeType
        {
            get
            {
                return this.mimeTypeField;
            }
            set
            {
                this.mimeTypeField = value;
                this.RaisePropertyChanged("mimeType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    public partial class Filreferanse : Fil
    {
        
        private string uriField;
        
        private string kvitteringUriField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="anyURI", Order=0)]
        public string uri
        {
            get
            {
                return this.uriField;
            }
            set
            {
                this.uriField = value;
                this.RaisePropertyChanged("uri");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="anyURI", Order=1)]
        public string kvitteringUri
        {
            get
            {
                return this.kvitteringUriField;
            }
            set
            {
                this.kvitteringUriField = value;
                this.RaisePropertyChanged("kvitteringUri");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Dokument/xml.schema/2012.01.31")]
    public partial class Filinnhold : Fil
    {
        
        private byte[] base64Field;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=0)]
        public byte[] base64
        {
            get
            {
                return this.base64Field;
            }
            set
            {
                this.base64Field = value;
                this.RaisePropertyChanged("base64");
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NyDokument", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NyDokumentRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        public SakArkivOppdateringService.Dokument dokument;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public bool returnerFil;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public NyDokumentRequest()
        {
        }
        
        public NyDokumentRequest(SakArkivOppdateringService.Dokument dokument, bool returnerFil, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.dokument = dokument;
            this.returnerFil = returnerFil;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NyDokumentResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NyDokumentResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        public SakArkivOppdateringService.Dokument @return;
        
        public NyDokumentResponse()
        {
        }
        
        public NyDokumentResponse(SakArkivOppdateringService.Dokument @return)
        {
            this.@return = @return;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SlettKorrespondansepart", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class SlettKorrespondansepart
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31", IsNullable=false)]
        public string[] systemID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.Journpostnoekkel journalpostnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public SlettKorrespondansepart()
        {
        }
        
        public SlettKorrespondansepart(string[] systemID, SakArkivOppdateringService.Journpostnoekkel journalpostnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.systemID = systemID;
            this.journalpostnokkel = journalpostnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SlettKorrespondansepartResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class SlettKorrespondansepartResponse
    {
        
        public SlettKorrespondansepartResponse()
        {
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NyKorrespondansepart", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NyKorrespondansepart
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31", IsNullable=false)]
        public SakArkivOppdateringService.Korrespondansepart[] part;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.Journpostnoekkel journalpostnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public NyKorrespondansepart()
        {
        }
        
        public NyKorrespondansepart(SakArkivOppdateringService.Korrespondansepart[] part, SakArkivOppdateringService.Journpostnoekkel journalpostnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.part = part;
            this.journalpostnokkel = journalpostnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NyKorrespondansepartResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NyKorrespondansepartResponse
    {
        
        public NyKorrespondansepartResponse()
        {
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="OppdaterJournalpostAnsvarlig", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class OppdaterJournalpostAnsvarligRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        public string nyAdministrativEnhetKode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public string nySaksbehandlerInit;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.Journpostnoekkel journalpostnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=3)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public OppdaterJournalpostAnsvarligRequest()
        {
        }
        
        public OppdaterJournalpostAnsvarligRequest(string nyAdministrativEnhetKode, string nySaksbehandlerInit, SakArkivOppdateringService.Journpostnoekkel journalpostnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.nyAdministrativEnhetKode = nyAdministrativEnhetKode;
            this.nySaksbehandlerInit = nySaksbehandlerInit;
            this.journalpostnokkel = journalpostnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="OppdaterJournalpostAnsvarligResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class OppdaterJournalpostAnsvarligResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        public SakArkivOppdateringService.Journalpost @return;
        
        public OppdaterJournalpostAnsvarligResponse()
        {
        }
        
        public OppdaterJournalpostAnsvarligResponse(SakArkivOppdateringService.Journalpost @return)
        {
            this.@return = @return;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NyJournalpost", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NyJournalpostRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        public SakArkivOppdateringService.Journalpost journalpost;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public NyJournalpostRequest()
        {
        }
        
        public NyJournalpostRequest(SakArkivOppdateringService.Journalpost journalpost, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.journalpost = journalpost;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NyJournalpostResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NyJournalpostResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        public SakArkivOppdateringService.Journalpost @return;
        
        public NyJournalpostResponse()
        {
        }
        
        public NyJournalpostResponse(SakArkivOppdateringService.Journalpost @return)
        {
            this.@return = @return;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Filter/xml.schema/2012.01.31")]
    public partial class Ansvarlig : Kriterie
    {
        
        private AnsvarligEnum eierField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public AnsvarligEnum eier
        {
            get
            {
                return this.eierField;
            }
            set
            {
                this.eierField = value;
                this.RaisePropertyChanged("eier");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Filter/xml.schema/2012.01.31")]
    public enum AnsvarligEnum
    {
        
        /// <remarks/>
        EGEN,
        
        /// <remarks/>
        EGENENHET,
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Soekefelt))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BboxKriterie))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Ansvarlig))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Filter/xml.schema/2012.01.31")]
    public partial class Kriterie : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Filter/xml.schema/2012.01.31")]
    public partial class Soekefelt : Kriterie
    {
        
        private string feltnavnField;
        
        private string feltverdiField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string feltnavn
        {
            get
            {
                return this.feltnavnField;
            }
            set
            {
                this.feltnavnField = value;
                this.RaisePropertyChanged("feltnavn");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string feltverdi
        {
            get
            {
                return this.feltverdiField;
            }
            set
            {
                this.feltverdiField = value;
                this.RaisePropertyChanged("feltverdi");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Filter/xml.schema/2012.01.31")]
    public partial class BboxKriterie : Kriterie
    {
        
        private Bbox bboxField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public Bbox bbox
        {
            get
            {
                return this.bboxField;
            }
            set
            {
                this.bboxField = value;
                this.RaisePropertyChanged("bbox");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31")]
    public partial class Bbox : Geometri
    {
        
        private Koordinat nedreVenstreField;
        
        private Koordinat oevreHoeyreField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public Koordinat nedreVenstre
        {
            get
            {
                return this.nedreVenstreField;
            }
            set
            {
                this.nedreVenstreField = value;
                this.RaisePropertyChanged("nedreVenstre");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public Koordinat oevreHoeyre
        {
            get
            {
                return this.oevreHoeyreField;
            }
            set
            {
                this.oevreHoeyreField = value;
                this.RaisePropertyChanged("oevreHoeyre");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31")]
    public partial class Koordinat : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private double xField;
        
        private double yField;
        
        private double zField;
        
        private bool zFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public double x
        {
            get
            {
                return this.xField;
            }
            set
            {
                this.xField = value;
                this.RaisePropertyChanged("x");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public double y
        {
            get
            {
                return this.yField;
            }
            set
            {
                this.yField = value;
                this.RaisePropertyChanged("y");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public double z
        {
            get
            {
                return this.zField;
            }
            set
            {
                this.zField = value;
                this.RaisePropertyChanged("z");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool zSpecified
        {
            get
            {
                return this.zFieldSpecified;
            }
            set
            {
                this.zFieldSpecified = value;
                this.RaisePropertyChanged("zSpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Punkt))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Kurve))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Flate))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Bbox))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31")]
    public partial class Geometri : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private KoordinatsystemKode koordinatsystemField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public KoordinatsystemKode koordinatsystem
        {
            get
            {
                return this.koordinatsystemField;
            }
            set
            {
                this.koordinatsystemField = value;
                this.RaisePropertyChanged("koordinatsystem");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31")]
    public partial class Punkt : Geometri
    {
        
        private Koordinat posisjonField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public Koordinat posisjon
        {
            get
            {
                return this.posisjonField;
            }
            set
            {
                this.posisjonField = value;
                this.RaisePropertyChanged("posisjon");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31")]
    public partial class Kurve : Geometri
    {
        
        private Koordinat[] linjeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", IsNullable=false)]
        public Koordinat[] linje
        {
            get
            {
                return this.linjeField;
            }
            set
            {
                this.linjeField = value;
                this.RaisePropertyChanged("linje");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31")]
    public partial class Flate : Geometri
    {
        
        private Ring[] indreAvgrensningField;
        
        private Ring ytreAvgrensningField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", IsNullable=false)]
        public Ring[] indreAvgrensning
        {
            get
            {
                return this.indreAvgrensningField;
            }
            set
            {
                this.indreAvgrensningField = value;
                this.RaisePropertyChanged("indreAvgrensning");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public Ring ytreAvgrensning
        {
            get
            {
                return this.ytreAvgrensningField;
            }
            set
            {
                this.ytreAvgrensningField = value;
                this.RaisePropertyChanged("ytreAvgrensning");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31")]
    public partial class Ring : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private Koordinat[] lukketKurveField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", IsNullable=false)]
        public Koordinat[] lukketKurve
        {
            get
            {
                return this.lukketKurveField;
            }
            set
            {
                this.lukketKurveField = value;
                this.RaisePropertyChanged("lukketKurve");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="FinnJournalposterUnderArbeid", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class FinnJournalposterUnderArbeid
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        public SakArkivOppdateringService.Ansvarlig ansvarlig;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public bool returnerMerknad;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public bool returnerTilleggsinformasjon;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=3)]
        public bool returnerKorrespondansepart;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=4)]
        public bool returnerAvskrivning;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=5)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public FinnJournalposterUnderArbeid()
        {
        }
        
        public FinnJournalposterUnderArbeid(SakArkivOppdateringService.Ansvarlig ansvarlig, bool returnerMerknad, bool returnerTilleggsinformasjon, bool returnerKorrespondansepart, bool returnerAvskrivning, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.ansvarlig = ansvarlig;
            this.returnerMerknad = returnerMerknad;
            this.returnerTilleggsinformasjon = returnerTilleggsinformasjon;
            this.returnerKorrespondansepart = returnerKorrespondansepart;
            this.returnerAvskrivning = returnerAvskrivning;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="FinnJournalposterUnderArbeidResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class FinnJournalposterUnderArbeidResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31", IsNullable=false)]
        public SakArkivOppdateringService.Journalpost[] @return;
        
        public FinnJournalposterUnderArbeidResponse()
        {
        }
        
        public FinnJournalposterUnderArbeidResponse(SakArkivOppdateringService.Journalpost[] @return)
        {
            this.@return = @return;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="FinnJournalpostRestanser", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class FinnJournalpostRestanser
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        public SakArkivOppdateringService.Ansvarlig ansvarlig;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public bool returnerMerknad;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public bool returnerTilleggsinformasjon;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=3)]
        public bool returnerKorrespondansepart;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=4)]
        public bool returnerAvskrivning;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=5)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public FinnJournalpostRestanser()
        {
        }
        
        public FinnJournalpostRestanser(SakArkivOppdateringService.Ansvarlig ansvarlig, bool returnerMerknad, bool returnerTilleggsinformasjon, bool returnerKorrespondansepart, bool returnerAvskrivning, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.ansvarlig = ansvarlig;
            this.returnerMerknad = returnerMerknad;
            this.returnerTilleggsinformasjon = returnerTilleggsinformasjon;
            this.returnerKorrespondansepart = returnerKorrespondansepart;
            this.returnerAvskrivning = returnerAvskrivning;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="FinnJournalpostRestanserResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class FinnJournalpostRestanserResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31", IsNullable=false)]
        public SakArkivOppdateringService.Journalpost[] @return;
        
        public FinnJournalpostRestanserResponse()
        {
        }
        
        public FinnJournalpostRestanserResponse(SakArkivOppdateringService.Journalpost[] @return)
        {
            this.@return = @return;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Plan/Felles/xml.schema/2012.01.31")]
    public partial class NasjonalArealplanId : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private Administrativenhetsnummer nummerField;
        
        private string planidentifikasjonField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public Administrativenhetsnummer nummer
        {
            get
            {
                return this.nummerField;
            }
            set
            {
                this.nummerField = value;
                this.RaisePropertyChanged("nummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string planidentifikasjon
        {
            get
            {
                return this.planidentifikasjonField;
            }
            set
            {
                this.planidentifikasjonField = value;
                this.RaisePropertyChanged("planidentifikasjon");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Stat))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Kommune))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Fylke))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Plan/Felles/xml.schema/2012.01.31")]
    public partial class Administrativenhetsnummer : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Plan/Felles/xml.schema/2012.01.31")]
    public partial class Stat : Administrativenhetsnummer
    {
        
        private string landskodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string landskode
        {
            get
            {
                return this.landskodeField;
            }
            set
            {
                this.landskodeField = value;
                this.RaisePropertyChanged("landskode");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Plan/Felles/xml.schema/2012.01.31")]
    public partial class Kommune : Administrativenhetsnummer
    {
        
        private string kommunenummerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string kommunenummer
        {
            get
            {
                return this.kommunenummerField;
            }
            set
            {
                this.kommunenummerField = value;
                this.RaisePropertyChanged("kommunenummer");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Plan/Felles/xml.schema/2012.01.31")]
    public partial class Fylke : Administrativenhetsnummer
    {
        
        private string fylkesnummerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string fylkesnummer
        {
            get
            {
                return this.fylkesnummerField;
            }
            set
            {
                this.fylkesnummerField = value;
                this.RaisePropertyChanged("fylkesnummer");
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="OppdaterPlan", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class OppdaterPlanRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        public SakArkivOppdateringService.NasjonalArealplanId planid;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.Saksnoekkel saksnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public OppdaterPlanRequest()
        {
        }
        
        public OppdaterPlanRequest(SakArkivOppdateringService.NasjonalArealplanId planid, SakArkivOppdateringService.Saksnoekkel saksnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.planid = planid;
            this.saksnokkel = saksnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="OppdaterPlanResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class OppdaterPlanResponse
    {
        
        public OppdaterPlanResponse()
        {
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SlettSakspart", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class SlettSakspart
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Felles/Teknisk/xml.schema/2012.01.31", IsNullable=false)]
        public string[] systemID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.Saksnoekkel saksnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public SlettSakspart()
        {
        }
        
        public SlettSakspart(string[] systemID, SakArkivOppdateringService.Saksnoekkel saksnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.systemID = systemID;
            this.saksnokkel = saksnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SlettSakspartResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class SlettSakspartResponse
    {
        
        public SlettSakspartResponse()
        {
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Sakspart : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string systemIDField;
        
        private bool skjermetSakspartField;
        
        private bool skjermetSakspartFieldSpecified;
        
        private string kortnavnField;
        
        private string kontaktpersonField;
        
        private SakspartRolle sakspartRolleField;
        
        private string merknadField;
        
        private Kontakt kontaktField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string systemID
        {
            get
            {
                return this.systemIDField;
            }
            set
            {
                this.systemIDField = value;
                this.RaisePropertyChanged("systemID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public bool skjermetSakspart
        {
            get
            {
                return this.skjermetSakspartField;
            }
            set
            {
                this.skjermetSakspartField = value;
                this.RaisePropertyChanged("skjermetSakspart");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool skjermetSakspartSpecified
        {
            get
            {
                return this.skjermetSakspartFieldSpecified;
            }
            set
            {
                this.skjermetSakspartFieldSpecified = value;
                this.RaisePropertyChanged("skjermetSakspartSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string kortnavn
        {
            get
            {
                return this.kortnavnField;
            }
            set
            {
                this.kortnavnField = value;
                this.RaisePropertyChanged("kortnavn");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string kontaktperson
        {
            get
            {
                return this.kontaktpersonField;
            }
            set
            {
                this.kontaktpersonField = value;
                this.RaisePropertyChanged("kontaktperson");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public SakspartRolle sakspartRolle
        {
            get
            {
                return this.sakspartRolleField;
            }
            set
            {
                this.sakspartRolleField = value;
                this.RaisePropertyChanged("sakspartRolle");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string merknad
        {
            get
            {
                return this.merknadField;
            }
            set
            {
                this.merknadField = value;
                this.RaisePropertyChanged("merknad");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public Kontakt Kontakt
        {
            get
            {
                return this.kontaktField;
            }
            set
            {
                this.kontaktField = value;
                this.RaisePropertyChanged("Kontakt");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NySakspart", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NySakspart
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31", IsNullable=false)]
        public SakArkivOppdateringService.Sakspart[] sakspart;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.Saksnoekkel saksnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public NySakspart()
        {
        }
        
        public NySakspart(SakArkivOppdateringService.Sakspart[] sakspart, SakArkivOppdateringService.Saksnoekkel saksnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.sakspart = sakspart;
            this.saksnokkel = saksnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NySakspartResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NySakspartResponse
    {
        
        public NySakspartResponse()
        {
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SlettPunkt", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class SlettPunkt
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31", IsNullable=false)]
        public SakArkivOppdateringService.Punkt[] posisjon;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.Saksnoekkel saksnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public SlettPunkt()
        {
        }
        
        public SlettPunkt(SakArkivOppdateringService.Punkt[] posisjon, SakArkivOppdateringService.Saksnoekkel saksnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.posisjon = posisjon;
            this.saksnokkel = saksnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SlettPunktResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class SlettPunktResponse
    {
        
        public SlettPunktResponse()
        {
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NyPunkt", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NyPunkt
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31", IsNullable=false)]
        public SakArkivOppdateringService.Punkt[] posisjon;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.Saksnoekkel saksnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public NyPunkt()
        {
        }
        
        public NyPunkt(SakArkivOppdateringService.Punkt[] posisjon, SakArkivOppdateringService.Saksnoekkel saksnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.posisjon = posisjon;
            this.saksnokkel = saksnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NyPunktResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NyPunktResponse
    {
        
        public NyPunktResponse()
        {
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Matrikkel/Felles/xml.schema/2012.01.31")]
    public partial class ByggIdent : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string bygningsNummerField;
        
        private string endringsloepenummerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=0)]
        public string bygningsNummer
        {
            get
            {
                return this.bygningsNummerField;
            }
            set
            {
                this.bygningsNummerField = value;
                this.RaisePropertyChanged("bygningsNummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=1)]
        public string endringsloepenummer
        {
            get
            {
                return this.endringsloepenummerField;
            }
            set
            {
                this.endringsloepenummerField = value;
                this.RaisePropertyChanged("endringsloepenummer");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SlettBygning", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class SlettBygning
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Matrikkel/Felles/xml.schema/2012.01.31", IsNullable=false)]
        public SakArkivOppdateringService.ByggIdent[] bygninger;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.Saksnoekkel saksnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public SlettBygning()
        {
        }
        
        public SlettBygning(SakArkivOppdateringService.ByggIdent[] bygninger, SakArkivOppdateringService.Saksnoekkel saksnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.bygninger = bygninger;
            this.saksnokkel = saksnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SlettBygningResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class SlettBygningResponse
    {
        
        public SlettBygningResponse()
        {
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NyBygning", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NyBygning
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Matrikkel/Felles/xml.schema/2012.01.31", IsNullable=false)]
        public SakArkivOppdateringService.ByggIdent[] bygninger;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.Saksnoekkel saksnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public NyBygning()
        {
        }
        
        public NyBygning(SakArkivOppdateringService.ByggIdent[] bygninger, SakArkivOppdateringService.Saksnoekkel saksnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.bygninger = bygninger;
            this.saksnokkel = saksnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NyBygningResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NyBygningResponse
    {
        
        public NyBygningResponse()
        {
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Matrikkel/Felles/xml.schema/2012.01.31")]
    public partial class Matrikkelnummer : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string kommunenummerField;
        
        private string gaardsnummerField;
        
        private string bruksnummerField;
        
        private string festenummerField;
        
        private string seksjonsnummerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string kommunenummer
        {
            get
            {
                return this.kommunenummerField;
            }
            set
            {
                this.kommunenummerField = value;
                this.RaisePropertyChanged("kommunenummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=1)]
        public string gaardsnummer
        {
            get
            {
                return this.gaardsnummerField;
            }
            set
            {
                this.gaardsnummerField = value;
                this.RaisePropertyChanged("gaardsnummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=2)]
        public string bruksnummer
        {
            get
            {
                return this.bruksnummerField;
            }
            set
            {
                this.bruksnummerField = value;
                this.RaisePropertyChanged("bruksnummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=3)]
        public string festenummer
        {
            get
            {
                return this.festenummerField;
            }
            set
            {
                this.festenummerField = value;
                this.RaisePropertyChanged("festenummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer", Order=4)]
        public string seksjonsnummer
        {
            get
            {
                return this.seksjonsnummerField;
            }
            set
            {
                this.seksjonsnummerField = value;
                this.RaisePropertyChanged("seksjonsnummer");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SlettMatrikkelnummer", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class SlettMatrikkelnummer
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Matrikkel/Felles/xml.schema/2012.01.31", IsNullable=false)]
        public SakArkivOppdateringService.Matrikkelnummer[] matrikkelnr;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.Saksnoekkel saksnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public SlettMatrikkelnummer()
        {
        }
        
        public SlettMatrikkelnummer(SakArkivOppdateringService.Matrikkelnummer[] matrikkelnr, SakArkivOppdateringService.Saksnoekkel saksnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.matrikkelnr = matrikkelnr;
            this.saksnokkel = saksnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SlettMatrikkelnummerResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class SlettMatrikkelnummerResponse
    {
        
        public SlettMatrikkelnummerResponse()
        {
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NyMatrikkelnummer", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NyMatrikkelnummer
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Matrikkel/Felles/xml.schema/2012.01.31", IsNullable=false)]
        public SakArkivOppdateringService.Matrikkelnummer[] matrikkelnr;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.Saksnoekkel saksnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public NyMatrikkelnummer()
        {
        }
        
        public NyMatrikkelnummer(SakArkivOppdateringService.Matrikkelnummer[] matrikkelnr, SakArkivOppdateringService.Saksnoekkel saksnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.matrikkelnr = matrikkelnr;
            this.saksnokkel = saksnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NyMatrikkelnummerResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NyMatrikkelnummerResponse
    {
        
        public NyMatrikkelnummerResponse()
        {
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Saksmappe : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string systemIDField;
        
        private Saksnummer saksnrField;
        
        private Mappetype mappetypeField;
        
        private System.DateTime saksdatoField;
        
        private bool saksdatoFieldSpecified;
        
        private string tittelField;
        
        private string offentligTittelField;
        
        private bool skjermetTittelField;
        
        private bool skjermetTittelFieldSpecified;
        
        private Skjerming skjermingField;
        
        private Saksstatus saksstatusField;
        
        private Dokumentmedium dokumentmediumField;
        
        private Arkivdel referanseArkivdelField;
        
        private Journalenhet journalenhetField;
        
        private string bevaringstidField;
        
        private Kassasjonsvedtak kassasjonsvedtakField;
        
        private System.DateTime kassasjonsdatoField;
        
        private bool kassasjonsdatoFieldSpecified;
        
        private string prosjektField;
        
        private string administrativEnhetInitField;
        
        private string administrativEnhetField;
        
        private string saksansvarligInitField;
        
        private string saksansvarligField;
        
        private string tilgangsgruppeNavnField;
        
        private Matrikkelnummer[] matrikkelnummerField;
        
        private Klasse[] klasseField;
        
        private Sakspart[] sakspartField;
        
        private Punkt[] punktField;
        
        private Tilleggsinformasjon[] tilleggsinformasjonField;
        
        private ByggIdent[] byggIdentField;
        
        private EksternNoekkel referanseEksternNoekkelField;
        
        private Merknad[] merknaderField;
        
        private NasjonalArealplanId planIdentField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string systemID
        {
            get
            {
                return this.systemIDField;
            }
            set
            {
                this.systemIDField = value;
                this.RaisePropertyChanged("systemID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public Saksnummer saksnr
        {
            get
            {
                return this.saksnrField;
            }
            set
            {
                this.saksnrField = value;
                this.RaisePropertyChanged("saksnr");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public Mappetype mappetype
        {
            get
            {
                return this.mappetypeField;
            }
            set
            {
                this.mappetypeField = value;
                this.RaisePropertyChanged("mappetype");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public System.DateTime saksdato
        {
            get
            {
                return this.saksdatoField;
            }
            set
            {
                this.saksdatoField = value;
                this.RaisePropertyChanged("saksdato");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool saksdatoSpecified
        {
            get
            {
                return this.saksdatoFieldSpecified;
            }
            set
            {
                this.saksdatoFieldSpecified = value;
                this.RaisePropertyChanged("saksdatoSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string tittel
        {
            get
            {
                return this.tittelField;
            }
            set
            {
                this.tittelField = value;
                this.RaisePropertyChanged("tittel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string offentligTittel
        {
            get
            {
                return this.offentligTittelField;
            }
            set
            {
                this.offentligTittelField = value;
                this.RaisePropertyChanged("offentligTittel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public bool skjermetTittel
        {
            get
            {
                return this.skjermetTittelField;
            }
            set
            {
                this.skjermetTittelField = value;
                this.RaisePropertyChanged("skjermetTittel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool skjermetTittelSpecified
        {
            get
            {
                return this.skjermetTittelFieldSpecified;
            }
            set
            {
                this.skjermetTittelFieldSpecified = value;
                this.RaisePropertyChanged("skjermetTittelSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public Skjerming skjerming
        {
            get
            {
                return this.skjermingField;
            }
            set
            {
                this.skjermingField = value;
                this.RaisePropertyChanged("skjerming");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public Saksstatus saksstatus
        {
            get
            {
                return this.saksstatusField;
            }
            set
            {
                this.saksstatusField = value;
                this.RaisePropertyChanged("saksstatus");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public Dokumentmedium dokumentmedium
        {
            get
            {
                return this.dokumentmediumField;
            }
            set
            {
                this.dokumentmediumField = value;
                this.RaisePropertyChanged("dokumentmedium");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=10)]
        public Arkivdel referanseArkivdel
        {
            get
            {
                return this.referanseArkivdelField;
            }
            set
            {
                this.referanseArkivdelField = value;
                this.RaisePropertyChanged("referanseArkivdel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=11)]
        public Journalenhet journalenhet
        {
            get
            {
                return this.journalenhetField;
            }
            set
            {
                this.journalenhetField = value;
                this.RaisePropertyChanged("journalenhet");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=12)]
        public string bevaringstid
        {
            get
            {
                return this.bevaringstidField;
            }
            set
            {
                this.bevaringstidField = value;
                this.RaisePropertyChanged("bevaringstid");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=13)]
        public Kassasjonsvedtak kassasjonsvedtak
        {
            get
            {
                return this.kassasjonsvedtakField;
            }
            set
            {
                this.kassasjonsvedtakField = value;
                this.RaisePropertyChanged("kassasjonsvedtak");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=14)]
        public System.DateTime kassasjonsdato
        {
            get
            {
                return this.kassasjonsdatoField;
            }
            set
            {
                this.kassasjonsdatoField = value;
                this.RaisePropertyChanged("kassasjonsdato");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool kassasjonsdatoSpecified
        {
            get
            {
                return this.kassasjonsdatoFieldSpecified;
            }
            set
            {
                this.kassasjonsdatoFieldSpecified = value;
                this.RaisePropertyChanged("kassasjonsdatoSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=15)]
        public string prosjekt
        {
            get
            {
                return this.prosjektField;
            }
            set
            {
                this.prosjektField = value;
                this.RaisePropertyChanged("prosjekt");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=16)]
        public string administrativEnhetInit
        {
            get
            {
                return this.administrativEnhetInitField;
            }
            set
            {
                this.administrativEnhetInitField = value;
                this.RaisePropertyChanged("administrativEnhetInit");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=17)]
        public string administrativEnhet
        {
            get
            {
                return this.administrativEnhetField;
            }
            set
            {
                this.administrativEnhetField = value;
                this.RaisePropertyChanged("administrativEnhet");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=18)]
        public string saksansvarligInit
        {
            get
            {
                return this.saksansvarligInitField;
            }
            set
            {
                this.saksansvarligInitField = value;
                this.RaisePropertyChanged("saksansvarligInit");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=19)]
        public string saksansvarlig
        {
            get
            {
                return this.saksansvarligField;
            }
            set
            {
                this.saksansvarligField = value;
                this.RaisePropertyChanged("saksansvarlig");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=20)]
        public string tilgangsgruppeNavn
        {
            get
            {
                return this.tilgangsgruppeNavnField;
            }
            set
            {
                this.tilgangsgruppeNavnField = value;
                this.RaisePropertyChanged("tilgangsgruppeNavn");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=21)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Matrikkel/Felles/xml.schema/2012.01.31", IsNullable=false)]
        public Matrikkelnummer[] Matrikkelnummer
        {
            get
            {
                return this.matrikkelnummerField;
            }
            set
            {
                this.matrikkelnummerField = value;
                this.RaisePropertyChanged("Matrikkelnummer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=22)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", IsNullable=false)]
        public Klasse[] klasse
        {
            get
            {
                return this.klasseField;
            }
            set
            {
                this.klasseField = value;
                this.RaisePropertyChanged("klasse");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=23)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", IsNullable=false)]
        public Sakspart[] sakspart
        {
            get
            {
                return this.sakspartField;
            }
            set
            {
                this.sakspartField = value;
                this.RaisePropertyChanged("sakspart");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=24)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31", IsNullable=false)]
        public Punkt[] Punkt
        {
            get
            {
                return this.punktField;
            }
            set
            {
                this.punktField = value;
                this.RaisePropertyChanged("Punkt");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=25)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", IsNullable=false)]
        public Tilleggsinformasjon[] tilleggsinformasjon
        {
            get
            {
                return this.tilleggsinformasjonField;
            }
            set
            {
                this.tilleggsinformasjonField = value;
                this.RaisePropertyChanged("tilleggsinformasjon");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=26)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", Namespace="http://rep.geointegrasjon.no/Matrikkel/Felles/xml.schema/2012.01.31", IsNullable=false)]
        public ByggIdent[] ByggIdent
        {
            get
            {
                return this.byggIdentField;
            }
            set
            {
                this.byggIdentField = value;
                this.RaisePropertyChanged("ByggIdent");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=27)]
        public EksternNoekkel referanseEksternNoekkel
        {
            get
            {
                return this.referanseEksternNoekkelField;
            }
            set
            {
                this.referanseEksternNoekkelField = value;
                this.RaisePropertyChanged("referanseEksternNoekkel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=28)]
        [System.Xml.Serialization.XmlArrayItemAttribute("liste", IsNullable=false)]
        public Merknad[] merknader
        {
            get
            {
                return this.merknaderField;
            }
            set
            {
                this.merknaderField = value;
                this.RaisePropertyChanged("merknader");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=29)]
        public NasjonalArealplanId planIdent
        {
            get
            {
                return this.planIdentField;
            }
            set
            {
                this.planIdentField = value;
                this.RaisePropertyChanged("planIdent");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31")]
    public partial class Klasse : object, System.ComponentModel.INotifyPropertyChanged
    {
        
        private string rekkefoelgeField;
        
        private Klassifikasjonssystem klassifikasjonssystemField;
        
        private string klasseIDField;
        
        private bool skjermetKlasseField;
        
        private bool skjermetKlasseFieldSpecified;
        
        private string ledetekstField;
        
        private string tittelField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string rekkefoelge
        {
            get
            {
                return this.rekkefoelgeField;
            }
            set
            {
                this.rekkefoelgeField = value;
                this.RaisePropertyChanged("rekkefoelge");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public Klassifikasjonssystem klassifikasjonssystem
        {
            get
            {
                return this.klassifikasjonssystemField;
            }
            set
            {
                this.klassifikasjonssystemField = value;
                this.RaisePropertyChanged("klassifikasjonssystem");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string klasseID
        {
            get
            {
                return this.klasseIDField;
            }
            set
            {
                this.klasseIDField = value;
                this.RaisePropertyChanged("klasseID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public bool skjermetKlasse
        {
            get
            {
                return this.skjermetKlasseField;
            }
            set
            {
                this.skjermetKlasseField = value;
                this.RaisePropertyChanged("skjermetKlasse");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool skjermetKlasseSpecified
        {
            get
            {
                return this.skjermetKlasseFieldSpecified;
            }
            set
            {
                this.skjermetKlasseFieldSpecified = value;
                this.RaisePropertyChanged("skjermetKlasseSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string ledetekst
        {
            get
            {
                return this.ledetekstField;
            }
            set
            {
                this.ledetekstField = value;
                this.RaisePropertyChanged("ledetekst");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string tittel
        {
            get
            {
                return this.tittelField;
            }
            set
            {
                this.tittelField = value;
                this.RaisePropertyChanged("tittel");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName)
        {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null))
            {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="OppdaterMappeAnsvarlig", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class OppdaterMappeAnsvarligRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        public string nyAdministrativEnhetKode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public string nySaksbehandlerInit;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.Saksnoekkel saksnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=3)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public OppdaterMappeAnsvarligRequest()
        {
        }
        
        public OppdaterMappeAnsvarligRequest(string nyAdministrativEnhetKode, string nySaksbehandlerInit, SakArkivOppdateringService.Saksnoekkel saksnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.nyAdministrativEnhetKode = nyAdministrativEnhetKode;
            this.nySaksbehandlerInit = nySaksbehandlerInit;
            this.saksnokkel = saksnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="OppdaterMappeAnsvarligResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class OppdaterMappeAnsvarligResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        public SakArkivOppdateringService.Saksmappe @return;
        
        public OppdaterMappeAnsvarligResponse()
        {
        }
        
        public OppdaterMappeAnsvarligResponse(SakArkivOppdateringService.Saksmappe @return)
        {
            this.@return = @return;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="OppdaterMappeEksternNoekkel", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class OppdaterMappeEksternNoekkelRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        public SakArkivOppdateringService.EksternNoekkel nokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.Saksnoekkel saksnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public OppdaterMappeEksternNoekkelRequest()
        {
        }
        
        public OppdaterMappeEksternNoekkelRequest(SakArkivOppdateringService.EksternNoekkel nokkel, SakArkivOppdateringService.Saksnoekkel saksnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.nokkel = nokkel;
            this.saksnokkel = saksnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="OppdaterMappeEksternNoekkelResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class OppdaterMappeEksternNoekkelResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        public SakArkivOppdateringService.Saksmappe @return;
        
        public OppdaterMappeEksternNoekkelResponse()
        {
        }
        
        public OppdaterMappeEksternNoekkelResponse(SakArkivOppdateringService.Saksmappe @return)
        {
            this.@return = @return;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="OppdaterMappeStatus", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class OppdaterMappeStatusRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        public SakArkivOppdateringService.Saksstatus saksstatuskode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.Saksnoekkel saksnokkel;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=2)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public OppdaterMappeStatusRequest()
        {
        }
        
        public OppdaterMappeStatusRequest(SakArkivOppdateringService.Saksstatus saksstatuskode, SakArkivOppdateringService.Saksnoekkel saksnokkel, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.saksstatuskode = saksstatuskode;
            this.saksnokkel = saksnokkel;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="OppdaterMappeStatusResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class OppdaterMappeStatusResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        public SakArkivOppdateringService.Saksmappe @return;
        
        public OppdaterMappeStatusResponse()
        {
        }
        
        public OppdaterMappeStatusResponse(SakArkivOppdateringService.Saksmappe @return)
        {
            this.@return = @return;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NySaksmappe", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NySaksmappeRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        public SakArkivOppdateringService.Saksmappe mappe;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=1)]
        public SakArkivOppdateringService.ArkivKontekst kontekst;
        
        public NySaksmappeRequest()
        {
        }
        
        public NySaksmappeRequest(SakArkivOppdateringService.Saksmappe mappe, SakArkivOppdateringService.ArkivKontekst kontekst)
        {
            this.mappe = mappe;
            this.kontekst = kontekst;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="NySaksmappeResponse", WrapperNamespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", IsWrapped=true)]
    public partial class NySaksmappeResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://rep.geointegrasjon.no/Arkiv/Oppdatering/xml.wsdl/2012.01.31", Order=0)]
        public SakArkivOppdateringService.Saksmappe @return;
        
        public NySaksmappeResponse()
        {
        }
        
        public NySaksmappeResponse(SakArkivOppdateringService.Saksmappe @return)
        {
            this.@return = @return;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    public interface ISakArkivOppdateringPortChannel : SakArkivOppdateringService.ISakArkivOppdateringPort, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    public partial class SakArkivOppdateringPortClient : System.ServiceModel.ClientBase<SakArkivOppdateringService.ISakArkivOppdateringPort>, SakArkivOppdateringService.ISakArkivOppdateringPort
    {
        
        /// <summary>
        /// Implement this partial method to configure the service endpoint.
        /// </summary>
        /// <param name="serviceEndpoint">The endpoint to configure</param>
        /// <param name="clientCredentials">The client credentials</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public SakArkivOppdateringPortClient() : 
                base(SakArkivOppdateringPortClient.GetDefaultBinding(), SakArkivOppdateringPortClient.GetDefaultEndpointAddress())
        {
            this.Endpoint.Name = EndpointConfiguration.BasicHttpBinding_ISakArkivOppdateringPort.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public SakArkivOppdateringPortClient(EndpointConfiguration endpointConfiguration) : 
                base(SakArkivOppdateringPortClient.GetBindingForEndpoint(endpointConfiguration), SakArkivOppdateringPortClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public SakArkivOppdateringPortClient(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(SakArkivOppdateringPortClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public SakArkivOppdateringPortClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(SakArkivOppdateringPortClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public SakArkivOppdateringPortClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        public SakArkivOppdateringService.SlettSaksmappeTilleggsinformasjonResponse SlettSaksmappeTilleggsinformasjon(SakArkivOppdateringService.SlettSaksmappeTilleggsinformasjon request)
        {
            return base.Channel.SlettSaksmappeTilleggsinformasjon(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.SlettSaksmappeTilleggsinformasjonResponse> SlettSaksmappeTilleggsinformasjonAsync(SakArkivOppdateringService.SlettSaksmappeTilleggsinformasjon request)
        {
            return base.Channel.SlettSaksmappeTilleggsinformasjonAsync(request);
        }
        
        public SakArkivOppdateringService.SlettSaksmappeMerknadResponse SlettSaksmappeMerknad(SakArkivOppdateringService.SlettSaksmappeMerknad request)
        {
            return base.Channel.SlettSaksmappeMerknad(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.SlettSaksmappeMerknadResponse> SlettSaksmappeMerknadAsync(SakArkivOppdateringService.SlettSaksmappeMerknad request)
        {
            return base.Channel.SlettSaksmappeMerknadAsync(request);
        }
        
        public SakArkivOppdateringService.SlettJournalpostTilleggsinformasjonResponse SlettJournalpostTilleggsinformasjon(SakArkivOppdateringService.SlettJournalpostTilleggsinformasjon request)
        {
            return base.Channel.SlettJournalpostTilleggsinformasjon(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.SlettJournalpostTilleggsinformasjonResponse> SlettJournalpostTilleggsinformasjonAsync(SakArkivOppdateringService.SlettJournalpostTilleggsinformasjon request)
        {
            return base.Channel.SlettJournalpostTilleggsinformasjonAsync(request);
        }
        
        public SakArkivOppdateringService.SlettJournalpostMerknadResponse SlettJournalpostMerknad(SakArkivOppdateringService.SlettJournalpostMerknad request)
        {
            return base.Channel.SlettJournalpostMerknad(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.SlettJournalpostMerknadResponse> SlettJournalpostMerknadAsync(SakArkivOppdateringService.SlettJournalpostMerknad request)
        {
            return base.Channel.SlettJournalpostMerknadAsync(request);
        }
        
        public SakArkivOppdateringService.NySaksmappeTilleggsinformasjonResponse NySaksmappeTilleggsinformasjon(SakArkivOppdateringService.NySaksmappeTilleggsinformasjon request)
        {
            return base.Channel.NySaksmappeTilleggsinformasjon(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.NySaksmappeTilleggsinformasjonResponse> NySaksmappeTilleggsinformasjonAsync(SakArkivOppdateringService.NySaksmappeTilleggsinformasjon request)
        {
            return base.Channel.NySaksmappeTilleggsinformasjonAsync(request);
        }
        
        public SakArkivOppdateringService.NySaksmappeMerknadResponse NySaksmappeMerknad(SakArkivOppdateringService.NySaksmappeMerknad request)
        {
            return base.Channel.NySaksmappeMerknad(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.NySaksmappeMerknadResponse> NySaksmappeMerknadAsync(SakArkivOppdateringService.NySaksmappeMerknad request)
        {
            return base.Channel.NySaksmappeMerknadAsync(request);
        }
        
        public SakArkivOppdateringService.NyJournalpostTilleggsinformasjonResponse NyJournalpostTilleggsinformasjon(SakArkivOppdateringService.NyJournalpostTilleggsinformasjon request)
        {
            return base.Channel.NyJournalpostTilleggsinformasjon(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.NyJournalpostTilleggsinformasjonResponse> NyJournalpostTilleggsinformasjonAsync(SakArkivOppdateringService.NyJournalpostTilleggsinformasjon request)
        {
            return base.Channel.NyJournalpostTilleggsinformasjonAsync(request);
        }
        
        public SakArkivOppdateringService.NyJournalpostMerknadResponse NyJournalpostMerknad(SakArkivOppdateringService.NyJournalpostMerknad request)
        {
            return base.Channel.NyJournalpostMerknad(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.NyJournalpostMerknadResponse> NyJournalpostMerknadAsync(SakArkivOppdateringService.NyJournalpostMerknad request)
        {
            return base.Channel.NyJournalpostMerknadAsync(request);
        }
        
        public SakArkivOppdateringService.SlettAvskrivningResponse SlettAvskrivning(SakArkivOppdateringService.SlettAvskrivning request)
        {
            return base.Channel.SlettAvskrivning(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.SlettAvskrivningResponse> SlettAvskrivningAsync(SakArkivOppdateringService.SlettAvskrivning request)
        {
            return base.Channel.SlettAvskrivningAsync(request);
        }
        
        public SakArkivOppdateringService.NyAvskrivningResponse NyAvskrivning(SakArkivOppdateringService.NyAvskrivning request)
        {
            return base.Channel.NyAvskrivning(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.NyAvskrivningResponse> NyAvskrivningAsync(SakArkivOppdateringService.NyAvskrivning request)
        {
            return base.Channel.NyAvskrivningAsync(request);
        }
        
        public SakArkivOppdateringService.OppdaterJournalpostStatusResponse OppdaterJournalpostStatus(SakArkivOppdateringService.OppdaterJournalpostStatusRequest request)
        {
            return base.Channel.OppdaterJournalpostStatus(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.OppdaterJournalpostStatusResponse> OppdaterJournalpostStatusAsync(SakArkivOppdateringService.OppdaterJournalpostStatusRequest request)
        {
            return base.Channel.OppdaterJournalpostStatusAsync(request);
        }
        
        public SakArkivOppdateringService.OppdaterJournalpostEksternNoekkelResponse OppdaterJournalpostEksternNoekkel(SakArkivOppdateringService.OppdaterJournalpostEksternNoekkelRequest request)
        {
            return base.Channel.OppdaterJournalpostEksternNoekkel(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.OppdaterJournalpostEksternNoekkelResponse> OppdaterJournalpostEksternNoekkelAsync(SakArkivOppdateringService.OppdaterJournalpostEksternNoekkelRequest request)
        {
            return base.Channel.OppdaterJournalpostEksternNoekkelAsync(request);
        }
        
        public SakArkivOppdateringService.NyDokumentResponse NyDokument(SakArkivOppdateringService.NyDokumentRequest request)
        {
            return base.Channel.NyDokument(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.NyDokumentResponse> NyDokumentAsync(SakArkivOppdateringService.NyDokumentRequest request)
        {
            return base.Channel.NyDokumentAsync(request);
        }
        
        public SakArkivOppdateringService.SlettKorrespondansepartResponse SlettKorrespondansepart(SakArkivOppdateringService.SlettKorrespondansepart request)
        {
            return base.Channel.SlettKorrespondansepart(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.SlettKorrespondansepartResponse> SlettKorrespondansepartAsync(SakArkivOppdateringService.SlettKorrespondansepart request)
        {
            return base.Channel.SlettKorrespondansepartAsync(request);
        }
        
        public SakArkivOppdateringService.NyKorrespondansepartResponse NyKorrespondansepart(SakArkivOppdateringService.NyKorrespondansepart request)
        {
            return base.Channel.NyKorrespondansepart(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.NyKorrespondansepartResponse> NyKorrespondansepartAsync(SakArkivOppdateringService.NyKorrespondansepart request)
        {
            return base.Channel.NyKorrespondansepartAsync(request);
        }
        
        public SakArkivOppdateringService.OppdaterJournalpostAnsvarligResponse OppdaterJournalpostAnsvarlig(SakArkivOppdateringService.OppdaterJournalpostAnsvarligRequest request)
        {
            return base.Channel.OppdaterJournalpostAnsvarlig(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.OppdaterJournalpostAnsvarligResponse> OppdaterJournalpostAnsvarligAsync(SakArkivOppdateringService.OppdaterJournalpostAnsvarligRequest request)
        {
            return base.Channel.OppdaterJournalpostAnsvarligAsync(request);
        }
        
        public SakArkivOppdateringService.NyJournalpostResponse NyJournalpost(SakArkivOppdateringService.NyJournalpostRequest request)
        {
            return base.Channel.NyJournalpost(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.NyJournalpostResponse> NyJournalpostAsync(SakArkivOppdateringService.NyJournalpostRequest request)
        {
            return base.Channel.NyJournalpostAsync(request);
        }
        
        public SakArkivOppdateringService.FinnJournalposterUnderArbeidResponse FinnJournalposterUnderArbeid(SakArkivOppdateringService.FinnJournalposterUnderArbeid request)
        {
            return base.Channel.FinnJournalposterUnderArbeid(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.FinnJournalposterUnderArbeidResponse> FinnJournalposterUnderArbeidAsync(SakArkivOppdateringService.FinnJournalposterUnderArbeid request)
        {
            return base.Channel.FinnJournalposterUnderArbeidAsync(request);
        }
        
        public SakArkivOppdateringService.FinnJournalpostRestanserResponse FinnJournalpostRestanser(SakArkivOppdateringService.FinnJournalpostRestanser request)
        {
            return base.Channel.FinnJournalpostRestanser(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.FinnJournalpostRestanserResponse> FinnJournalpostRestanserAsync(SakArkivOppdateringService.FinnJournalpostRestanser request)
        {
            return base.Channel.FinnJournalpostRestanserAsync(request);
        }
        
        public SakArkivOppdateringService.OppdaterPlanResponse OppdaterPlan(SakArkivOppdateringService.OppdaterPlanRequest request)
        {
            return base.Channel.OppdaterPlan(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.OppdaterPlanResponse> OppdaterPlanAsync(SakArkivOppdateringService.OppdaterPlanRequest request)
        {
            return base.Channel.OppdaterPlanAsync(request);
        }
        
        public SakArkivOppdateringService.SlettSakspartResponse SlettSakspart(SakArkivOppdateringService.SlettSakspart request)
        {
            return base.Channel.SlettSakspart(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.SlettSakspartResponse> SlettSakspartAsync(SakArkivOppdateringService.SlettSakspart request)
        {
            return base.Channel.SlettSakspartAsync(request);
        }
        
        public SakArkivOppdateringService.NySakspartResponse NySakspart(SakArkivOppdateringService.NySakspart request)
        {
            return base.Channel.NySakspart(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.NySakspartResponse> NySakspartAsync(SakArkivOppdateringService.NySakspart request)
        {
            return base.Channel.NySakspartAsync(request);
        }
        
        public SakArkivOppdateringService.SlettPunktResponse SlettPunkt(SakArkivOppdateringService.SlettPunkt request)
        {
            return base.Channel.SlettPunkt(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.SlettPunktResponse> SlettPunktAsync(SakArkivOppdateringService.SlettPunkt request)
        {
            return base.Channel.SlettPunktAsync(request);
        }
        
        public SakArkivOppdateringService.NyPunktResponse NyPunkt(SakArkivOppdateringService.NyPunkt request)
        {
            return base.Channel.NyPunkt(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.NyPunktResponse> NyPunktAsync(SakArkivOppdateringService.NyPunkt request)
        {
            return base.Channel.NyPunktAsync(request);
        }
        
        public SakArkivOppdateringService.SlettBygningResponse SlettBygning(SakArkivOppdateringService.SlettBygning request)
        {
            return base.Channel.SlettBygning(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.SlettBygningResponse> SlettBygningAsync(SakArkivOppdateringService.SlettBygning request)
        {
            return base.Channel.SlettBygningAsync(request);
        }
        
        public SakArkivOppdateringService.NyBygningResponse NyBygning(SakArkivOppdateringService.NyBygning request)
        {
            return base.Channel.NyBygning(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.NyBygningResponse> NyBygningAsync(SakArkivOppdateringService.NyBygning request)
        {
            return base.Channel.NyBygningAsync(request);
        }
        
        public SakArkivOppdateringService.SlettMatrikkelnummerResponse SlettMatrikkelnummer(SakArkivOppdateringService.SlettMatrikkelnummer request)
        {
            return base.Channel.SlettMatrikkelnummer(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.SlettMatrikkelnummerResponse> SlettMatrikkelnummerAsync(SakArkivOppdateringService.SlettMatrikkelnummer request)
        {
            return base.Channel.SlettMatrikkelnummerAsync(request);
        }
        
        public SakArkivOppdateringService.NyMatrikkelnummerResponse NyMatrikkelnummer(SakArkivOppdateringService.NyMatrikkelnummer request)
        {
            return base.Channel.NyMatrikkelnummer(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.NyMatrikkelnummerResponse> NyMatrikkelnummerAsync(SakArkivOppdateringService.NyMatrikkelnummer request)
        {
            return base.Channel.NyMatrikkelnummerAsync(request);
        }
        
        public SakArkivOppdateringService.OppdaterMappeAnsvarligResponse OppdaterMappeAnsvarlig(SakArkivOppdateringService.OppdaterMappeAnsvarligRequest request)
        {
            return base.Channel.OppdaterMappeAnsvarlig(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.OppdaterMappeAnsvarligResponse> OppdaterMappeAnsvarligAsync(SakArkivOppdateringService.OppdaterMappeAnsvarligRequest request)
        {
            return base.Channel.OppdaterMappeAnsvarligAsync(request);
        }
        
        public SakArkivOppdateringService.OppdaterMappeEksternNoekkelResponse OppdaterMappeEksternNoekkel(SakArkivOppdateringService.OppdaterMappeEksternNoekkelRequest request)
        {
            return base.Channel.OppdaterMappeEksternNoekkel(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.OppdaterMappeEksternNoekkelResponse> OppdaterMappeEksternNoekkelAsync(SakArkivOppdateringService.OppdaterMappeEksternNoekkelRequest request)
        {
            return base.Channel.OppdaterMappeEksternNoekkelAsync(request);
        }
        
        public SakArkivOppdateringService.OppdaterMappeStatusResponse OppdaterMappeStatus(SakArkivOppdateringService.OppdaterMappeStatusRequest request)
        {
            return base.Channel.OppdaterMappeStatus(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.OppdaterMappeStatusResponse> OppdaterMappeStatusAsync(SakArkivOppdateringService.OppdaterMappeStatusRequest request)
        {
            return base.Channel.OppdaterMappeStatusAsync(request);
        }
        
        public SakArkivOppdateringService.NySaksmappeResponse NySaksmappe(SakArkivOppdateringService.NySaksmappeRequest request)
        {
            return base.Channel.NySaksmappe(request);
        }
        
        public System.Threading.Tasks.Task<SakArkivOppdateringService.NySaksmappeResponse> NySaksmappeAsync(SakArkivOppdateringService.NySaksmappeRequest request)
        {
            return base.Channel.NySaksmappeAsync(request);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpBinding_ISakArkivOppdateringPort))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpBinding_ISakArkivOppdateringPort))
            {
                return new System.ServiceModel.EndpointAddress("http://test01.elementscloud.no/geointegration/Services/GeoIntegration/V1.1/SakArk" +
                        "ivOppdateringService.svc");
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.Channels.Binding GetDefaultBinding()
        {
            return SakArkivOppdateringPortClient.GetBindingForEndpoint(EndpointConfiguration.BasicHttpBinding_ISakArkivOppdateringPort);
        }
        
        private static System.ServiceModel.EndpointAddress GetDefaultEndpointAddress()
        {
            return SakArkivOppdateringPortClient.GetEndpointAddress(EndpointConfiguration.BasicHttpBinding_ISakArkivOppdateringPort);
        }
        
        public enum EndpointConfiguration
        {
            
            BasicHttpBinding_ISakArkivOppdateringPort,
        }
    }
}
