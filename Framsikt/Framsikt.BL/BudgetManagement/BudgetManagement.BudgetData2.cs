using EntityFrameworkExtras.EFCore;
using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using System.Globalization;

namespace Framsikt.BL;

public partial class BudgetManagement
{


    public async Task<dynamic> GetServiceAreaBudgetDataAsync(string yearSelected, string userId, string langPref, int budgetYear, bool splitData = false)
    {
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();

        tco_users_settings userSettings = await tenantDbContext.tco_users_settings.FirstOrDefaultAsync(x =>
            x.fk_user_id == userDetails.pk_id && x.tenant_id == userDetails.tenant_id && x.is_active_tenant);
        int budYear = budgetYear;
        var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budYear, 1));
        Dictionary<string, clsLanguageString> langStringValues = new Dictionary<string, clsLanguageString>();
        if (string.IsNullOrEmpty(langPref))
        {
            langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "NumberFormats");
        }
        else
        {
            langStringValues = await _utility.GetLanguageStringsAsync(langPref, userDetails.user_name, "NumberFormats");
        }
        string format = ((langStringValues.FirstOrDefault(v => v.Key == "amount")).Value).LangText;

        List<clsOrgStructure> lstOrgStructure = await _utility.GetTenantOrgStructureAsync(orgVersionContent, userId);
        clsOrgStructureLevelDetails tenantOrgLevelDetails = _utility.GetTenantOrgLevelDetails(lstOrgStructure);
        List<List<string>> DepartmentsAndFunctions = _utility.GetDepartmentsAndFunctionsForOrgStructure(lstOrgStructure, null, null, tenantOrgLevelDetails.level1, tenantOrgLevelDetails.level2);
        List<string> lstAllDepartments = DepartmentsAndFunctions[0];
        List<string> lstAllFunctions = DepartmentsAndFunctions[1];

        var dataActiveBudgetChanges = await (from t in tenantDbContext.tfp_budget_changes
            where t.fk_tenant_id == userDetails.tenant_id
                  && t.budget_year == budYear
                  && t.status == 1
                  && t.org_budget_flag == 1
            orderby t.change_date descending
            select t).ToListAsync();

        int activeChangeId = dataActiveBudgetChanges.Count() == 0 ? -1 :
            userSettings == null ? dataActiveBudgetChanges.FirstOrDefault().pk_change_id : userSettings.active_change_id == null ? dataActiveBudgetChanges.FirstOrDefault().pk_change_id :
            dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == userSettings.active_change_id) == null ? dataActiveBudgetChanges.FirstOrDefault().pk_change_id :
            dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == userSettings.active_change_id).pk_change_id;

        var activeChangeData = dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == activeChangeId);

        bool displayTempColumns = false;

        if (activeChangeData != null)
        {
            if (activeChangeData.workflow_status == 1 || activeChangeData.workflow_status == 20)
            {
                displayTempColumns = true;
            }
            else
            {
                displayTempColumns = false;
            }
        }
        else
        {
            displayTempColumns = true;
        }

        List<int> actionTypes = new List<int>() { 20, 30, 40, 60, 70, 80, 90, 100, 101 };

        var alterCodesLst = await tenantDbContext.tco_fp_alter_codes.Where(x => x.fk_tenant_id == userDetails.tenant_id).ToListAsync();

        List<clsServiceAreaData> dbDataset = await (from th in tenantDbContext.tfp_trans_header
            join td in tenantDbContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                equals new { a = td.fk_tenant_id, b = td.fk_action_id }
            //join ac in tenantDbContext.tco_fp_alter_codes on new { a = td.fk_tenant_id, b = td.fk_alter_code } equals new { a = ac.fk_tenant_id, b = ac.pk_alter_code } into ac1
            //from ac2 in ac1.DefaultIfEmpty()
            where th.fk_tenant_id == userDetails.tenant_id
                  && (td.budget_year == budYear)
                  && th.action_type >= 5
                  && !actionTypes.Contains(th.action_type)
            select new clsServiceAreaData
            {
                actionType = th.action_type,
                accountCode = td.fk_account_code.Trim(),
                departmentCode = td.department_code.Trim(),
                functionCode = td.function_code.Trim(),
                year1Amount = td.year_1_amount,
                year2Amount = td.year_2_amount,
                year3Amount = td.year_3_amount,
                year4Amount = td.year_4_amount,
                lineOrderId = th.line_order,
                alterCode = td.fk_alter_code
            }).ToListAsync();

        dbDataset = (from a in dbDataset
            join ac in alterCodesLst on new { b = a.alterCode } equals new { b = ac.pk_alter_code } into ac1
            from ac2 in ac1.DefaultIfEmpty()
            select new clsServiceAreaData
            {
                actionType = a.actionType,
                accountCode = a.accountCode,
                departmentCode = a.departmentCode,
                functionCode = a.functionCode,
                year1Amount = a.year1Amount,
                year2Amount = a.year2Amount,
                year3Amount = a.year3Amount,
                year4Amount = a.year4Amount,
                lineOrderId = a.lineOrderId,
                sumCode = (ac2 == null || string.IsNullOrEmpty(ac2.sum_code)) ? string.Empty : ac2.sum_code,
                sumDescription = (ac2 == null || string.IsNullOrEmpty(ac2.sum_description)) ? string.Empty : ac2.sum_description,
                limitCode = (ac2 == null || string.IsNullOrEmpty(ac2.limit_code)) ? string.Empty : ac2.limit_code,
                limitDescription = (ac2 == null || string.IsNullOrEmpty(ac2.limit_description)) ? string.Empty : ac2.limit_description,
                cabFlag = (ac2 == null || ac2.cab_flag.Equals(null)) ? 0 : ac2.cab_flag
            }).ToList();

        if (lstAllDepartments.Where(x => !string.IsNullOrEmpty(x)).Count() > 0 && lstAllFunctions.Where(x => !string.IsNullOrEmpty(x)).Count() > 0)
        {
            dbDataset = dbDataset.Where(y => lstAllDepartments.Contains(y.departmentCode) && lstAllFunctions.Contains(y.functionCode)).ToList();
        }
        else if (lstAllFunctions.Where(x => !string.IsNullOrEmpty(x)).Count() > 0)
        {
            dbDataset = dbDataset.Where(y => lstAllFunctions.Contains(y.functionCode)).ToList();
        }
        else if (lstAllDepartments.Where(x => !string.IsNullOrEmpty(x)).Count() > 0)
        {
            dbDataset = dbDataset.Where(y => lstAllDepartments.Contains(y.departmentCode)).ToList();
        }

        List<clsServiceAreaData> mainDataset = (from d in dbDataset
            select new clsServiceAreaData
            {
                orgId = "",
                orgName = "",
                serviceId = "",
                serviceName = "",
                actionType = d.actionType,
                actionId = d.actionId,
                accountCode = d.accountCode,
                departmentCode = d.departmentCode,
                functionCode = d.functionCode,
                year1Amount = d.year1Amount,
                year2Amount = d.year2Amount,
                year3Amount = d.year3Amount,
                year4Amount = d.year4Amount,
                description = d.description,
                lineOrderId = d.lineOrderId,
                sumCode = d.sumCode,
                sumDescription = d.sumDescription,
                limitCode = d.limitCode,
                limitDescription = d.limitDescription,
                cabFlag = d.cabFlag
            }).ToList();

        string firstLevel = string.Empty;
        string secondLevel = string.Empty;

        if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_1").Where(a => !string.IsNullOrEmpty(a.departmentCode)).Select(y => y.departmentCode).Count() > 0)
        {
            firstLevel = "dept";
        }
        else if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_1").Count(y => !string.IsNullOrEmpty(y.functionCode)) > 0)
        {
            firstLevel = "function";
        }

        if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_2").Where(a => !string.IsNullOrEmpty(a.departmentCode)).Select(y => y.departmentCode).Count() > 0)
        {
            secondLevel = "dept";
        }
        else if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_2").Where(a => !string.IsNullOrEmpty(a.functionCode)).Select(y => y.functionCode).Count() > 0)
        {
            secondLevel = "function";
        }

        mainDataset = _utility.AssignOrgIdAndServiceIdToResultSet(orgVersionContent, mainDataset, lstOrgStructure, firstLevel, secondLevel);

        Dictionary<string, clsLanguageString> langStringValuesBAtype = new Dictionary<string, clsLanguageString>();
        if (string.IsNullOrEmpty(langPref))
        {
            langStringValuesBAtype = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetManagement");
        }
        else
        {
            langStringValuesBAtype = await _utility.GetLanguageStringsAsync(langPref, userDetails.user_name, "BudgetManagement");
        }

        List<string> sumCodesWithCabFlag1 = mainDataset.Where(w => w.cabFlag == 1 && !string.IsNullOrEmpty(w.sumCode)).Select(x => x.sumCode).Distinct().OrderBy(z => z).ToList();
        List<string> sumCodesWithCabFlag0 = mainDataset.Where(w => w.cabFlag == 0 && !string.IsNullOrEmpty(w.sumCode)).Select(x => x.sumCode).Distinct().OrderBy(z => z).ToList();

        List<clsLimitCodeDetails> lstLimitCodesWithCabFlag1 = new List<clsLimitCodeDetails>();
        List<clsLimitCodeDetails> lstLimitCodesWithCabFlag0 = new List<clsLimitCodeDetails>();

        Dictionary<string, decimal> dictTotals = new Dictionary<string, decimal>();
        dynamic ServiceAreaBudgetData = new JObject();

        JArray fieldsArray = new JArray();
        Dictionary<string, int> dictFields = new Dictionary<string, int>();

        int fieldCount = 0;

        fieldsArray.Add("id");
        fieldCount++;
        dictFields.Add("id", fieldCount);

        fieldsArray.Add("parentId");
        fieldCount++;
        dictFields.Add("parentId", fieldCount);

        fieldsArray.Add("ServiceAreaId");
        fieldCount++;
        dictFields.Add("ServiceAreaId", fieldCount);

        fieldsArray.Add("ServiceArea");
        fieldCount++;
        dictFields.Add("ServiceArea", fieldCount);

        int limitCount = 0;

        decimal agggregateOfLimitCodesWithFlag1OnAllRows = 0;

        foreach (string s in sumCodesWithCabFlag1)
        {
            List<string> limitCodes = mainDataset.Where(x => x.cabFlag == 1 && x.sumCode == s && !string.IsNullOrEmpty(x.limitCode)).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList();
            foreach (string l in limitCodes)
            {
                limitCount++;
                if (!dictTotals.ContainsKey(l))
                {
                    dictTotals.Add(l, 0);
                }

                clsLimitCodeDetails lCode = new clsLimitCodeDetails()
                {
                    sumCode = s,
                    limitCode = l,
                    limitDescription = mainDataset.FirstOrDefault(x => x.limitCode == l).limitDescription
                };
                lstLimitCodesWithCabFlag1.Add(lCode);
                fieldsArray.Add("limitCode" + l);
                fieldCount++;
                if (!dictFields.ContainsKey(("limitCode" + l)))
                {
                    dictFields.Add(("limitCode" + l), fieldCount);
                }
            }
        }
        fieldsArray.Add("agggregateOfLimitCodesWithFlag1");
        fieldCount++;
        dictFields.Add("agggregateOfLimitCodesWithFlag1", fieldCount);

        dictTotals.Add("agggregateOfLimitCodesWithFlag1", 0);

        foreach (string s in sumCodesWithCabFlag0)
        {
            List<string> limitCodes = mainDataset.Where(x => x.cabFlag == 0 && x.sumCode == s && !string.IsNullOrEmpty(x.limitCode)).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList();
            foreach (string l in limitCodes)
            {
                limitCount++;
                if (!dictTotals.ContainsKey(l))
                {
                    dictTotals.Add(l, 0);
                }

                clsLimitCodeDetails lCode = new clsLimitCodeDetails()
                {
                    sumCode = s,
                    limitCode = l,
                    limitDescription = mainDataset.FirstOrDefault(x => x.limitCode == l).limitDescription
                };
                lstLimitCodesWithCabFlag0.Add(lCode);
                fieldsArray.Add("limitCode" + l);
                fieldCount++;
                if (!dictFields.ContainsKey(("limitCode" + l)))
                {
                    dictFields.Add(("limitCode" + l), fieldCount);
                }
            }
        }

        fieldsArray.Add("notAllocatedDataSummary");
        fieldCount++;
        dictFields.Add("notAllocatedDataSummary", fieldCount);

        fieldsArray.Add("summary");
        fieldCount++;
        dictFields.Add("summary", fieldCount);

        /*cacculated from DB*/
        if (displayTempColumns == true)
        {
            fieldsArray.Add("tempCostReduction");
            fieldCount++;
            dictFields.Add("tempCostReduction", fieldCount);

            fieldsArray.Add("tempNewPriorities");
            fieldCount++;
            dictFields.Add("tempNewPriorities", fieldCount);
        }
        else
        {
            fieldsArray.Add("proposedNewPriority");
            fieldCount++;
            dictFields.Add("proposedNewPriority", fieldCount);

            fieldsArray.Add("onGoingActions"); //  remainingCostReduction +   remainingNewPriority + ongoingactions
            fieldCount++;
            dictFields.Add("onGoingActions", fieldCount);

            fieldsArray.Add("operationaleffectInvestments"); //  remainingCostReduction +   remainingNewPriority + ongoingactions
            fieldCount++;
            dictFields.Add("operationaleffectInvestments", fieldCount);

            fieldsArray.Add("newPriorities");
            fieldCount++;
            dictFields.Add("newPriorities", fieldCount);
        }

        ServiceAreaBudgetData.Add("Fields", fieldsArray);

        dynamic titlesArray = new JArray();
        titlesArray.Add(" ");
        titlesArray.Add(" ");
        titlesArray.Add(" ");
        titlesArray.Add((langStringValuesBAtype["cmn_service_area"]).LangText);
        foreach (string s in sumCodesWithCabFlag1)
        {
            List<string> limitCodes = lstLimitCodesWithCabFlag1.Where(x => x.sumCode == s).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList();
            foreach (string l in limitCodes)
            {
                titlesArray.Add(lstLimitCodesWithCabFlag1.FirstOrDefault(x => x.limitCode == l).limitDescription);
            }
        }
        titlesArray.Add((langStringValuesBAtype["BM_SAbudget_limitCodeAgg_1"]).LangText);

        foreach (string s in sumCodesWithCabFlag0)
        {
            List<string> limitCodes = lstLimitCodesWithCabFlag0.Where(x => x.sumCode == s).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList();
            foreach (string l in limitCodes)
            {
                titlesArray.Add(lstLimitCodesWithCabFlag0.FirstOrDefault(x => x.limitCode == l).limitDescription);
            }
        }
        /*cacculated from DB*/
        titlesArray.Add((langStringValuesBAtype["BM_not_allocated_Data_summary_text"]).LangText);
        titlesArray.Add((langStringValuesBAtype["BM_SAbudget_currBdgt"]).LangText + budYear.ToString() + "-" + (budYear + 3).ToString());

        if (displayTempColumns == true)
        {
            titlesArray.Add((langStringValuesBAtype["BM_SAbudget_crr_temp"]).LangText);
            titlesArray.Add((langStringValuesBAtype["BM_SAbudget_newPriorities_temp"]).LangText);
        }
        else
        {
            titlesArray.Add((langStringValuesBAtype["BM_SAbudget_proposedNewPrio"]).LangText);
            titlesArray.Add((langStringValuesBAtype["BM_ongoing_action_summary_text"]).LangText);
            titlesArray.Add((langStringValuesBAtype["BM_operational_effect_investments_text"]).LangText);
            titlesArray.Add((langStringValuesBAtype["BM_rem_amt_new_prts_text"]).LangText);
        }

        ServiceAreaBudgetData.Add("Titles", titlesArray);

        Dictionary<string, string> orgIds = new Dictionary<string, string>();

        lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_1").ToList().ForEach(a =>
        {
            if (!orgIds.Keys.Contains(a.id))
            {
                orgIds.Add(a.id, a.name);
            }
        });

        var filteredOrdStructure = (from o in lstOrgStructure
            where o.type == "FINPLAN_LEVEL_2" && (o.id != null || o.id != "")
            select new
            {
                o.id,
                o.name,
                o.functionCode,
                o.departmentCode
            }).ToList();

        dynamic finalData = new JArray();

        int childId = 2407;

        foreach (string orgId in orgIds.Keys.Distinct().Where(a => !string.IsNullOrEmpty(a)).OrderBy(x => x).ToList())
        {
            decimal totalSumCodes = 0;
            List<string> lstOrgIdDepartsments = lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_1" && z.id == orgId).Where(a => !string.IsNullOrEmpty(a.departmentCode)).Select(y => y.departmentCode).Distinct().ToList();
            List<string> lstOrgIdFunctions = lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_1" && z.id == orgId).Where(a => !string.IsNullOrEmpty(a.functionCode)).Select(y => y.functionCode).Distinct().ToList();

            dynamic data = new JArray();

            dynamic ServiceArea = new JObject();
            ServiceArea.Add("id", orgId);
            ServiceArea.Add("parentId", null);
            ServiceArea.Add("ServiceAreaId", orgId);
            ServiceArea.Add("ServiceArea", orgIds[orgId]);

            List<clsServiceAreaData> mainDatasetFirstLevel = new List<clsServiceAreaData>();

            if (firstLevel == "dept")
            {
                mainDatasetFirstLevel = mainDataset.Where(x => lstOrgIdDepartsments.Contains(x.departmentCode.Trim())).ToList();
            }
            else if (firstLevel == "function")
            {
                mainDatasetFirstLevel = mainDataset.Where(x => lstOrgIdFunctions.Contains(x.functionCode.Trim())).ToList();
            }

            if (yearSelected == "Year1")
            {
                foreach (string sCode in sumCodesWithCabFlag1)
                {
                    decimal val = mainDatasetFirstLevel.Where(x => x.sumCode == sCode).Sum(x => x.year1Amount) / 1000;
                    totalSumCodes = totalSumCodes + val;
                    foreach (var lcode in lstLimitCodesWithCabFlag1.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                    {
                        val = mainDatasetFirstLevel.Where(x => x.limitCode == lcode).Sum(x => x.year1Amount) / 1000;
                        data.Add(val);
                        dictTotals[lcode] = dictTotals[lcode] + val;
                    }
                }

                agggregateOfLimitCodesWithFlag1OnAllRows = agggregateOfLimitCodesWithFlag1OnAllRows + totalSumCodes;
                data.Add(totalSumCodes);
                dictTotals["agggregateOfLimitCodesWithFlag1"] = dictTotals["agggregateOfLimitCodesWithFlag1"] + totalSumCodes;

                foreach (string sCode in sumCodesWithCabFlag0)
                {
                    decimal val = mainDatasetFirstLevel.Where(x => x.sumCode == sCode).Sum(x => x.year1Amount) / 1000;
                    totalSumCodes = totalSumCodes + val;
                    foreach (var lcode in lstLimitCodesWithCabFlag0.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                    {
                        val = mainDatasetFirstLevel.Where(x => x.limitCode == lcode).Sum(x => x.year1Amount) / 1000;
                        data.Add(val);
                        dictTotals[lcode] = dictTotals[lcode] + val;
                    }
                }

                // Below are DB Columns
                data.Add(0);
                data.Add(0);
                if (displayTempColumns == true)
                {
                    data.Add(0);
                    data.Add(0);
                }
                else
                {
                    data.Add(0);
                    data.Add(0);
                    data.Add(0);
                    data.Add(0);
                }
            }
            else if (yearSelected == "Year2")
            {
                foreach (string sCode in sumCodesWithCabFlag1)
                {
                    decimal val = mainDatasetFirstLevel.Where(x => x.sumCode == sCode).Sum(x => x.year2Amount) / 1000;
                    totalSumCodes = totalSumCodes + val;
                    foreach (var lcode in lstLimitCodesWithCabFlag1.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                    {
                        val = mainDatasetFirstLevel.Where(x => x.limitCode == lcode).Sum(x => x.year2Amount) / 1000;
                        data.Add(val);
                        dictTotals[lcode] = dictTotals[lcode] + val;
                    }
                }
                agggregateOfLimitCodesWithFlag1OnAllRows = agggregateOfLimitCodesWithFlag1OnAllRows + totalSumCodes;
                data.Add(totalSumCodes);
                dictTotals["agggregateOfLimitCodesWithFlag1"] = dictTotals["agggregateOfLimitCodesWithFlag1"] + totalSumCodes;

                foreach (string sCode in sumCodesWithCabFlag0)
                {
                    decimal val = mainDatasetFirstLevel.Where(x => x.sumCode == sCode).Sum(x => x.year2Amount) / 1000;
                    totalSumCodes = totalSumCodes + val;
                    foreach (var lcode in lstLimitCodesWithCabFlag0.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                    {
                        val = mainDatasetFirstLevel.Where(x => x.limitCode == lcode).Sum(x => x.year2Amount) / 1000;
                        data.Add(val);
                        dictTotals[lcode] = dictTotals[lcode] + val;
                    }
                }
                // Below are DB Columns
                data.Add(0);
                data.Add(0);
                if (displayTempColumns == true)
                {
                    data.Add(0);
                    data.Add(0);
                }
                else
                {
                    data.Add(0);
                    data.Add(0);
                    data.Add(0);
                    data.Add(0);
                }
            }
            else if (yearSelected == "Year3")
            {
                foreach (string sCode in sumCodesWithCabFlag1)
                {
                    decimal val = mainDatasetFirstLevel.Where(x => x.sumCode == sCode).Sum(x => x.year3Amount) / 1000;
                    totalSumCodes = totalSumCodes + val;
                    foreach (var lcode in lstLimitCodesWithCabFlag1.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                    {
                        val = mainDatasetFirstLevel.Where(x => x.limitCode == lcode).Sum(x => x.year3Amount) / 1000;
                        data.Add(val);
                        dictTotals[lcode] = dictTotals[lcode] + val;
                    }
                }
                agggregateOfLimitCodesWithFlag1OnAllRows = agggregateOfLimitCodesWithFlag1OnAllRows + totalSumCodes;
                data.Add(totalSumCodes);
                dictTotals["agggregateOfLimitCodesWithFlag1"] = dictTotals["agggregateOfLimitCodesWithFlag1"] + totalSumCodes;

                foreach (string sCode in sumCodesWithCabFlag0)
                {
                    decimal val = mainDatasetFirstLevel.Where(x => x.sumCode == sCode).Sum(x => x.year3Amount) / 1000;
                    totalSumCodes = totalSumCodes + val;
                    foreach (var lcode in lstLimitCodesWithCabFlag0.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                    {
                        val = mainDatasetFirstLevel.Where(x => x.limitCode == lcode).Sum(x => x.year3Amount) / 1000;
                        data.Add(val);
                        dictTotals[lcode] = dictTotals[lcode] + val;
                    }
                }
                // Below are DB Columns
                data.Add(0);
                data.Add(0);
                if (displayTempColumns == true)
                {
                    data.Add(0);
                    data.Add(0);
                }
                else
                {
                    data.Add(0);
                    data.Add(0);
                    data.Add(0);
                    data.Add(0);
                }
            }
            else if (yearSelected == "Year4")
            {
                foreach (string sCode in sumCodesWithCabFlag1)
                {
                    decimal val = mainDatasetFirstLevel.Where(x => x.sumCode == sCode).Sum(x => x.year4Amount) / 1000;
                    totalSumCodes = totalSumCodes + val;
                    foreach (var lcode in lstLimitCodesWithCabFlag1.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                    {
                        val = mainDatasetFirstLevel.Where(x => x.limitCode == lcode).Sum(x => x.year4Amount) / 1000;
                        data.Add(val);
                        dictTotals[lcode] = dictTotals[lcode] + val;
                    }
                }
                agggregateOfLimitCodesWithFlag1OnAllRows = agggregateOfLimitCodesWithFlag1OnAllRows + totalSumCodes;
                data.Add(totalSumCodes);
                dictTotals["agggregateOfLimitCodesWithFlag1"] = dictTotals["agggregateOfLimitCodesWithFlag1"] + totalSumCodes;

                foreach (string sCode in sumCodesWithCabFlag0)
                {
                    decimal val = mainDatasetFirstLevel.Where(x => x.sumCode == sCode).Sum(x => x.year4Amount) / 1000;
                    totalSumCodes = totalSumCodes + val;
                    foreach (var lcode in lstLimitCodesWithCabFlag0.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                    {
                        val = mainDatasetFirstLevel.Where(x => x.limitCode == lcode).Sum(x => x.year4Amount) / 1000;
                        data.Add(val);
                        dictTotals[lcode] = dictTotals[lcode] + val;
                    }
                }
                // Below are DB Columns
                data.Add(0);
                data.Add(0);
                if (displayTempColumns == true)
                {
                    data.Add(0);
                    data.Add(0);
                }
                else
                {
                    data.Add(0);
                    data.Add(0);
                    data.Add(0);
                    data.Add(0);
                }
            }

            ServiceArea.Add("Data", data);
            finalData.Add(ServiceArea);

            Dictionary<string, string> serviceIdsForCurrentOrgId = new Dictionary<string, string>();
            mainDataset.Where(x => x.orgId == orgId).ToList().ForEach(y =>
            {
                if (!serviceIdsForCurrentOrgId.Keys.Contains(y.serviceId))
                {
                    serviceIdsForCurrentOrgId.Add(y.serviceId, y.serviceName);
                }
            });

            foreach (string serviceId in serviceIdsForCurrentOrgId.Keys.Distinct().Where(a => !string.IsNullOrEmpty(a)).OrderBy(x => x).ToList())
            {
                totalSumCodes = 0;
                childId++;

                ServiceArea = new JObject();
                data = new JArray();
                ServiceArea.Add("id", childId.ToString());
                ServiceArea.Add("parentId", orgId);
                ServiceArea.Add("ServiceAreaId", serviceId);
                ServiceArea.Add("ServiceArea", serviceIdsForCurrentOrgId[serviceId]);

                List<string> lstServiceIdDepartments = filteredOrdStructure.Where(x => x.id == serviceId).Select(y => y.departmentCode).Distinct().ToList();
                List<string> lstServiceIdFunctions = filteredOrdStructure.Where(x => x.id == serviceId).Select(y => y.functionCode).Distinct().ToList();

                string hierarchy = string.Empty;

                if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_1").Where(a => !string.IsNullOrEmpty(a.departmentCode)).Select(y => y.departmentCode).Count() > 0 &&
                    lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_2").Where(a => !string.IsNullOrEmpty(a.departmentCode)).Select(y => y.departmentCode).Count() > 0)
                {
                    hierarchy = "dept-dept";
                }
                else if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_1").Where(a => !string.IsNullOrEmpty(a.departmentCode)).Select(y => y.departmentCode).Count() > 0 &&
                         lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_2").Where(a => !string.IsNullOrEmpty(a.functionCode)).Select(y => y.functionCode).Count() > 0)
                {
                    hierarchy = "dept-function";
                }
                else if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_1").Where(a => !string.IsNullOrEmpty(a.functionCode)).Select(y => y.functionCode).Count() > 0 &&
                         lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_2").Where(a => !string.IsNullOrEmpty(a.departmentCode)).Select(y => y.departmentCode).Count() > 0)
                {
                    hierarchy = "function-dept";
                }
                else if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_1").Where(a => !string.IsNullOrEmpty(a.functionCode)).Select(y => y.functionCode).Count() > 0 &&
                         lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_2").Where(a => !string.IsNullOrEmpty(a.functionCode)).Select(y => y.functionCode).Count() > 0)
                {
                    hierarchy = "function-function";
                }

                List<clsServiceAreaData> mainDatasetSecondLevel = new List<clsServiceAreaData>();

                if (hierarchy == "dept-dept")
                {
                    mainDatasetSecondLevel = mainDatasetFirstLevel.Where(x => lstServiceIdDepartments.Contains(x.departmentCode)).ToList();
                }
                else if (hierarchy == "dept-function")
                {
                    mainDatasetSecondLevel = mainDatasetFirstLevel.Where(x => lstServiceIdFunctions.Contains(x.functionCode)).ToList();
                }
                else if (hierarchy == "function-dept")
                {
                    mainDatasetSecondLevel = mainDatasetFirstLevel.Where(x => lstServiceIdDepartments.Contains(x.departmentCode)).ToList();
                }
                else if (hierarchy == "function-function")
                {
                    mainDatasetSecondLevel = mainDatasetFirstLevel.Where(x => lstServiceIdFunctions.Contains(x.functionCode)).ToList();
                }

                if (yearSelected == "Year1")
                {
                    foreach (string sCode in sumCodesWithCabFlag1)
                    {
                        decimal val = mainDatasetSecondLevel.Where(x => x.sumCode == sCode).Sum(x => x.year1Amount) / 1000;
                        totalSumCodes = totalSumCodes + val;
                        foreach (var lcode in lstLimitCodesWithCabFlag1.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                        {
                            val = mainDatasetSecondLevel.Where(x => x.limitCode == lcode).Sum(x => x.year1Amount) / 1000;
                            data.Add(val);
                        }
                    }
                    data.Add(totalSumCodes);

                    foreach (string sCode in sumCodesWithCabFlag0)
                    {
                        decimal val = mainDatasetSecondLevel.Where(x => x.sumCode == sCode).Sum(x => x.year1Amount) / 1000;
                        totalSumCodes = totalSumCodes + val;
                        foreach (var lcode in lstLimitCodesWithCabFlag0.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                        {
                            val = mainDatasetSecondLevel.Where(x => x.limitCode == lcode).Sum(x => x.year1Amount) / 1000;
                            data.Add(val);
                        }
                    }
                    // Below are DB Columns
                    data.Add(0);
                    data.Add(0);
                    if (displayTempColumns == true)
                    {
                        data.Add(0);
                        data.Add(0);
                    }
                    else
                    {
                        data.Add(0);
                        data.Add(0);
                        data.Add(0);
                        data.Add(0);
                    }
                }
                else if (yearSelected == "Year2")
                {
                    foreach (string sCode in sumCodesWithCabFlag1)
                    {
                        decimal val = mainDatasetSecondLevel.Where(x => x.sumCode == sCode).Sum(x => x.year2Amount) / 1000;
                        totalSumCodes = totalSumCodes + val;
                        foreach (var lcode in lstLimitCodesWithCabFlag1.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                        {
                            val = mainDatasetSecondLevel.Where(x => x.limitCode == lcode).Sum(x => x.year2Amount) / 1000;
                            data.Add(val);
                        }
                    }
                    data.Add(totalSumCodes);

                    foreach (string sCode in sumCodesWithCabFlag0)
                    {
                        decimal val = mainDatasetSecondLevel.Where(x => x.sumCode == sCode).Sum(x => x.year2Amount) / 1000;
                        totalSumCodes = totalSumCodes + val;
                        foreach (var lcode in lstLimitCodesWithCabFlag0.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                        {
                            val = mainDatasetSecondLevel.Where(x => x.limitCode == lcode).Sum(x => x.year2Amount) / 1000;
                            data.Add(val);
                        }
                    }
                    // Below are DB Columns
                    data.Add(0);
                    data.Add(0);
                    if (displayTempColumns == true)
                    {
                        data.Add(0);
                        data.Add(0);
                    }
                    else
                    {
                        data.Add(0);
                        data.Add(0);
                        data.Add(0);
                        data.Add(0);
                    }
                }
                else if (yearSelected == "Year3")
                {
                    foreach (string sCode in sumCodesWithCabFlag1)
                    {
                        decimal val = mainDatasetSecondLevel.Where(x => x.sumCode == sCode).Sum(x => x.year3Amount) / 1000;
                        totalSumCodes = totalSumCodes + val;
                        foreach (var lcode in lstLimitCodesWithCabFlag1.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                        {
                            val = mainDatasetSecondLevel.Where(x => x.limitCode == lcode).Sum(x => x.year3Amount) / 1000;
                            data.Add(val);
                        }
                    }
                    data.Add(totalSumCodes);

                    foreach (string sCode in sumCodesWithCabFlag0)
                    {
                        decimal val = mainDatasetSecondLevel.Where(x => x.sumCode == sCode).Sum(x => x.year3Amount) / 1000;
                        totalSumCodes = totalSumCodes + val;
                        foreach (var lcode in lstLimitCodesWithCabFlag0.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                        {
                            val = mainDatasetSecondLevel.Where(x => x.limitCode == lcode).Sum(x => x.year3Amount) / 1000;
                            data.Add(val);
                        }
                    }
                    // Below are DB Columns
                    data.Add(0);
                    data.Add(0);
                    if (displayTempColumns == true)
                    {
                        data.Add(0);
                        data.Add(0);
                    }
                    else
                    {
                        data.Add(0);
                        data.Add(0);
                        data.Add(0);
                        data.Add(0);
                    }
                }
                else if (yearSelected == "Year4")
                {
                    foreach (string sCode in sumCodesWithCabFlag1)
                    {
                        decimal val = mainDatasetSecondLevel.Where(x => x.sumCode == sCode).Sum(x => x.year4Amount) / 1000;
                        totalSumCodes = totalSumCodes + val;
                        foreach (var lcode in lstLimitCodesWithCabFlag1.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                        {
                            val = mainDatasetSecondLevel.Where(x => x.limitCode == lcode).Sum(x => x.year4Amount) / 1000;
                            data.Add(val);
                        }
                    }
                    data.Add(totalSumCodes);

                    foreach (string sCode in sumCodesWithCabFlag0)
                    {
                        decimal val = mainDatasetSecondLevel.Where(x => x.sumCode == sCode).Sum(x => x.year4Amount) / 1000;
                        totalSumCodes = totalSumCodes + val;
                        foreach (var lcode in lstLimitCodesWithCabFlag0.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                        {
                            val = mainDatasetSecondLevel.Where(x => x.limitCode == lcode).Sum(x => x.year4Amount) / 1000;
                            data.Add(val);
                        }
                    }
                    // Below are DB Columns
                    data.Add(0);
                    data.Add(0);
                    if (displayTempColumns == true)
                    {
                        data.Add(0);
                        data.Add(0);
                    }
                    else
                    {
                        data.Add(0);
                        data.Add(0);
                        data.Add(0);
                        data.Add(0);
                    }
                }
                ServiceArea.Add("Data", data);
                finalData.Add(ServiceArea);
            }
        }

        //Tab String
        dynamic TabStrings = new JArray() { ((langStringValuesBAtype["BM_meeting_tab"]).LangText + " " + budYear), ((langStringValuesBAtype["BM_meeting_tab"]).LangText + " " + (budYear + 1)), ((langStringValuesBAtype["BM_meeting_tab"]).LangText + " " + (budYear + 2)), ((langStringValuesBAtype["BM_meeting_tab"]).LangText + " " + (budYear + 3)) };
        //Add total to Json
        dynamic TotalObject = new JObject();
        dynamic TotalData = new JArray() { dictTotals.Values.ToList() };
        TotalData.Add(0);
        TotalData.Add(0);
        if (displayTempColumns == true)
        {
            TotalData.Add(0);
            TotalData.Add(0);
        }
        else
        {
            TotalData.Add(0);
            TotalData.Add(0);
            TotalData.Add(0);
            TotalData.Add(0);
        }

        TotalObject.Add("id", null);
        TotalObject.Add("parentId", null);
        TotalObject.Add("ServiceAreaId", null);
        TotalObject.Add("ServiceArea", (langStringValuesBAtype["cmn_title_total"]).LangText);
        TotalObject.Add("Data", TotalData);
        finalData.Add(TotalObject);
        JObject jsonCfgRow = JObject.Parse(await _utility.GetApplicationSettingAsync("GetBudgetAndFinancialData_grid_config"));
        dynamic backgroudIndex = new JArray();

        if (limitCount > 0)
        {
            backgroudIndex.Add(1);
            backgroudIndex.Add(dictFields["agggregateOfLimitCodesWithFlag1"] - 4);
            backgroudIndex.Add(dictFields["summary"] - 4);
        }
        else
        {
            backgroudIndex.Add(dictFields["agggregateOfLimitCodesWithFlag1"] - 4);
            backgroudIndex.Add(dictFields["summary"] - 4);
        }

        ServiceAreaBudgetData.gridConfig = jsonCfgRow;
        ServiceAreaBudgetData.amountFormat = format;
        ServiceAreaBudgetData.Add("backgroudIndex", backgroudIndex);
        ServiceAreaBudgetData.Add("tabCaption", TabStrings);
        ServiceAreaBudgetData.Add("jsonData", finalData);

        CultureInfo ci = CultureInfo.InvariantCulture;
        ServiceAreaBudgetData.modifiedAt = (string)DateTime.UtcNow.ToString(ci);

        string defaultKey = await _utility.GetParameterValueAsync(userId, "IS_ONGOINGACTION_AVAILABLE");
        bool isOngoingAActionsAvailable = false;
        if (!string.IsNullOrEmpty(defaultKey.Trim()))
        {
            isOngoingAActionsAvailable = System.Convert.ToBoolean(defaultKey.Trim());
        }

        ServiceAreaBudgetData.showOngoingActions = isOngoingAActionsAvailable;
        ServiceAreaBudgetData.agggregateOfLimitCodesWithFlag1OnAllRows = agggregateOfLimitCodesWithFlag1OnAllRows;

        if (splitData == true)
        {
            var lst = (from u in tenantDbContext.vwUserDetails
                where u.pk_id == -1
                select new clsServiceAreaData
                {
                    orgId = string.Empty,
                    serviceId = string.Empty,
                    year1Amount = 0,
                    year2Amount = 0,
                    year3Amount = 0,
                    year4Amount = 0
                }).ToList();

            foreach (var item in ServiceAreaBudgetData["jsonData"])
            {
                if ((string)item["ServiceAreaId"] != null)
                {
                    for (int i = 4; i < ((JArray)ServiceAreaBudgetData["Fields"]).Count(); i++)
                    {
                        if ((string)ServiceAreaBudgetData["Fields"][i] == "tempNewPriorities")
                        {
                            int indexOfagggregateOfLimitCodesWithFlag1 = ((JArray)ServiceAreaBudgetData["Fields"]).ToObject<List<string>>().IndexOf("agggregateOfLimitCodesWithFlag1");

                            if ((string)item["parentId"] == null && secondLevel == string.Empty)
                            {
                                if (yearSelected == "Year1")
                                {
                                    lst.Add(new clsServiceAreaData()
                                    {
                                        orgId = (string)item["ServiceAreaId"],
                                        serviceId = string.Empty,
                                        year1Amount = Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]) >= 0 && Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]) < 1 ? Convert.ToDecimal(0.00) : Math.Round((Convert.ToDecimal(activeChangeData.year_1_amount / 1000) * Convert.ToDecimal(Convert.ToDecimal(item["Data"][indexOfagggregateOfLimitCodesWithFlag1 - 4]) / Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]))))
                                    });
                                }
                                else if (yearSelected == "Year2")
                                {
                                    lst.Add(new clsServiceAreaData()
                                    {
                                        orgId = (string)item["ServiceAreaId"],
                                        serviceId = string.Empty,
                                        year2Amount = Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]) >= 0 && Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]) < 1 ? Convert.ToDecimal(0.00) : Math.Round((Convert.ToDecimal(activeChangeData.year_2_amount / 1000) * (Convert.ToDecimal(item["Data"][indexOfagggregateOfLimitCodesWithFlag1 - 4]) / Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]))))
                                    });
                                }
                                else if (yearSelected == "Year3")
                                {
                                    lst.Add(new clsServiceAreaData()
                                    {
                                        orgId = (string)item["ServiceAreaId"],
                                        serviceId = string.Empty,
                                        year3Amount = Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]) >= 0 && Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]) < 1 ? Convert.ToDecimal(0.00) : Math.Round((Convert.ToDecimal(activeChangeData.year_3_amount / 1000) * (Convert.ToDecimal(item["Data"][indexOfagggregateOfLimitCodesWithFlag1 - 4]) / Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]))))
                                    });
                                }
                                else if (yearSelected == "Year4")
                                {
                                    lst.Add(new clsServiceAreaData()
                                    {
                                        orgId = (string)item["ServiceAreaId"],
                                        serviceId = string.Empty,
                                        year4Amount = Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]) >= 0 && Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]) < 1 ? Convert.ToDecimal(0.00) : Math.Round((Convert.ToDecimal(activeChangeData.year_4_amount / 1000) * (Convert.ToDecimal(item["Data"][indexOfagggregateOfLimitCodesWithFlag1 - 4]) / Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]))))
                                    });
                                }
                            }
                            else
                            {
                                if ((string)item["parentId"] == null)
                                {
                                    continue;
                                }

                                string parentId = Convert.ToString(item["parentId"]);
                                JArray arr = (JArray)ServiceAreaBudgetData["jsonData"];
                                string level1 = (string)arr.FirstOrDefault(x => (string)x["id"] == parentId && (string)x["parentId"] == null)["ServiceAreaId"];

                                if (yearSelected == "Year1")
                                {
                                    lst.Add(new clsServiceAreaData()
                                    {
                                        orgId = level1,
                                        serviceId = (string)item["ServiceAreaId"],
                                        year1Amount = Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]) >= 0 && Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]) < 1 ? Convert.ToDecimal(0.00) : Math.Round(Convert.ToDecimal(activeChangeData.year_1_amount / 1000) * (Convert.ToDecimal(item["Data"][indexOfagggregateOfLimitCodesWithFlag1 - 4]) / Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"])))
                                    });
                                }
                                else if (yearSelected == "Year2")
                                {
                                    lst.Add(new clsServiceAreaData()
                                    {
                                        orgId = level1,
                                        serviceId = (string)item["ServiceAreaId"],
                                        year2Amount = Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]) >= 0 && Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]) < 1 ? Convert.ToDecimal(0.00) : Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]) >= 0 && Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]) < 1 ? Convert.ToDecimal(0.00) : Math.Round(Convert.ToDecimal(activeChangeData.year_2_amount / 1000) * (Convert.ToDecimal(item["Data"][indexOfagggregateOfLimitCodesWithFlag1 - 4]) / Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"])))
                                    });
                                }
                                else if (yearSelected == "Year3")
                                {
                                    lst.Add(new clsServiceAreaData()
                                    {
                                        orgId = level1,
                                        serviceId = (string)item["ServiceAreaId"],
                                        year3Amount = Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]) >= 0 && Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]) < 1 ? Convert.ToDecimal(0.00) : Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]) >= 0 && Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]) < 1 ? Convert.ToDecimal(0.00) : Math.Round(Convert.ToDecimal(activeChangeData.year_3_amount / 1000) * (Convert.ToDecimal(item["Data"][indexOfagggregateOfLimitCodesWithFlag1 - 4]) / Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"])))
                                    });
                                }
                                else if (yearSelected == "Year4")
                                {
                                    lst.Add(new clsServiceAreaData()
                                    {
                                        orgId = level1,
                                        serviceId = (string)item["ServiceAreaId"],
                                        year4Amount = Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]) >= 0 && Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]) < 1 ? Convert.ToDecimal(0.00) : Math.Round(Convert.ToDecimal(activeChangeData.year_4_amount / 1000) * (Convert.ToDecimal(item["Data"][indexOfagggregateOfLimitCodesWithFlag1 - 4]) / Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"])))
                                    });
                                }
                            }
                        }
                    }
                }
            }

            List<tfp_temp_budget_limits> lstTfpTempBudgetLimits = await tenantDbContext.tfp_temp_budget_limits.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budYear && x.action_type == 40).ToListAsync();

            List<udtTfpTempBudgetLimits> limitsData = new List<udtTfpTempBudgetLimits>();
            int count = 0;
            decimal totalYear1 = 0;
            decimal totalYear2 = 0;
            decimal totalYear3 = 0;
            decimal totalYear4 = 0;
            foreach (var l in lst)
            {
                udtTfpTempBudgetLimits limit = new udtTfpTempBudgetLimits();
                limit.fk_tenant_id = userDetails.tenant_id;
                limit.budget_year = budYear;
                limit.fp_level_1_value = l.orgId;
                limit.fp_level_2_value = l.serviceId;
                limit.action_type = 40;

                count++;

                if (count == lst.Count())
                {
                    limit.year_1_limit = activeChangeData.year_1_amount - totalYear1;

                    limit.year_2_limit = activeChangeData.year_2_amount - totalYear2;

                    limit.year_3_limit = activeChangeData.year_3_amount - totalYear3;

                    limit.year_4_limit = activeChangeData.year_4_amount - totalYear4;
                }
                else
                {
                    limit.year_1_limit = (yearSelected == "Year2" || yearSelected == "Year3" || yearSelected == "Year4") ?
                        lstTfpTempBudgetLimits.FirstOrDefault(x => x.fp_level_1_value == l.orgId && x.fp_level_2_value == l.serviceId) == null ? 0 :
                            lstTfpTempBudgetLimits.FirstOrDefault(x => x.fp_level_1_value == l.orgId && x.fp_level_2_value == l.serviceId).year_1_limit : l.year1Amount * 1000;
                    totalYear1 += limit.year_1_limit;

                    limit.year_2_limit = (yearSelected == "Year3" || yearSelected == "Year4" || yearSelected == "Year1") ?
                        lstTfpTempBudgetLimits.FirstOrDefault(x => x.fp_level_1_value == l.orgId && x.fp_level_2_value == l.serviceId) == null ? 0 :
                            lstTfpTempBudgetLimits.FirstOrDefault(x => x.fp_level_1_value == l.orgId && x.fp_level_2_value == l.serviceId).year_2_limit : l.year2Amount * 1000;
                    totalYear2 += limit.year_2_limit;

                    limit.year_3_limit = (yearSelected == "Year4" || yearSelected == "Year1" || yearSelected == "Year2") ?
                        lstTfpTempBudgetLimits.FirstOrDefault(x => x.fp_level_1_value == l.orgId && x.fp_level_2_value == l.serviceId) == null ? 0 :
                            lstTfpTempBudgetLimits.FirstOrDefault(x => x.fp_level_1_value == l.orgId && x.fp_level_2_value == l.serviceId).year_3_limit : l.year3Amount * 1000;
                    totalYear3 += limit.year_3_limit;

                    limit.year_4_limit = (yearSelected == "Year1" || yearSelected == "Year2" || yearSelected == "Year3") ?
                        lstTfpTempBudgetLimits.FirstOrDefault(x => x.fp_level_1_value == l.orgId && x.fp_level_2_value == l.serviceId) == null ? 0 :
                            lstTfpTempBudgetLimits.FirstOrDefault(x => x.fp_level_1_value == l.orgId && x.fp_level_2_value == l.serviceId).year_4_limit : l.year4Amount * 1000;
                    totalYear4 += limit.year_4_limit;
                }

                limit.updated = DateTime.UtcNow;
                limit.updated_by = userDetails.pk_id;
                limitsData.Add(limit);
            }

            prcInsertIntoTfpTempBudgetLimits spInsert = new prcInsertIntoTfpTempBudgetLimits();
            spInsert.udtTfpTempBudgetLimits = new List<udtTfpTempBudgetLimits>();
            spInsert.udtTfpTempBudgetLimits.AddRange(limitsData);
            spInsert.fk_tenant_id = userDetails.tenant_id;
            spInsert.budget_year = budYear;
            await tenantDbContext.Database.ExecuteStoredProcedureAsync(spInsert);

            ServiceAreaBudgetData.splitData = true;
            return ServiceAreaBudgetData;
        }

        ServiceAreaBudgetData.splitData = false;
        return ServiceAreaBudgetData;
    }



    public async Task<dynamic> GetServiceAreaBudgetDataForBudgetPhaseAsync(string yearSelected, string userId, string langPref, int budgetYear, bool splitData = false)
    {
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();

        tco_users_settings userSettings = await tenantDbContext.tco_users_settings.FirstOrDefaultAsync(x =>
            x.fk_user_id == userDetails.pk_id && x.tenant_id == userDetails.tenant_id && x.is_active_tenant);
        int budYear = budgetYear;
        var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budYear, 1));
        Dictionary<string, clsLanguageString> langStringValues = null;
        if (string.IsNullOrEmpty(langPref))
        {
            langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "NumberFormats");
        }
        else
        {
            langStringValues = await _utility.GetLanguageStringsAsync(langPref, userDetails.user_name, "NumberFormats");
        }
        string format = ((langStringValues.FirstOrDefault(v => v.Key == "amount")).Value).LangText;

        List<clsOrgStructure> lstOrgStructure = await _utility.GetTenantOrgStructureAsync(orgVersionContent, userId);
        clsOrgStructureLevelDetails tenantOrgLevelDetails = _utility.GetTenantOrgLevelDetails(lstOrgStructure);
        List<List<string>> DepartmentsAndFunctions = _utility.GetDepartmentsAndFunctionsForOrgStructure(lstOrgStructure, null, null, tenantOrgLevelDetails.level1, tenantOrgLevelDetails.level2);
        List<string> lstAllDepartments = DepartmentsAndFunctions[0];
        List<string> lstAllFunctions = DepartmentsAndFunctions[1];

        var dataActiveBudgetChanges = await (from t in tenantDbContext.tfp_budget_changes
            where t.fk_tenant_id == userDetails.tenant_id
                  && t.budget_year == budYear
                  && t.status == 1
                  && t.org_budget_flag == 1
            orderby t.change_date descending
            select t).ToListAsync();

        int activeChangeId = -1;
        if (dataActiveBudgetChanges.Any())
        {
            if (userSettings == null || userSettings.active_change_id == null || dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == userSettings.active_change_id) == null)
            {
                activeChangeId = dataActiveBudgetChanges.FirstOrDefault().pk_change_id;
            }
            else
            {
                activeChangeId = dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == userSettings.active_change_id).pk_change_id;
            }
        }

        var activeChangeData = dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == activeChangeId);

        bool displayTempColumns = false;

        if (activeChangeData != null)
        {
            if (activeChangeData.workflow_status == 1 || activeChangeData.workflow_status == 20)
            {
                displayTempColumns = true;
            }
            else
            {
                displayTempColumns = false;
            }
        }
        else
        {
            displayTempColumns = true;
        }

        List<int> actionTypes = new List<int>() { 20, 30, 40, 60, 70, 80, 90, 100, 101 };

        var alterCodesLst = await tenantDbContext.tco_fp_alter_codes.Where(x => x.fk_tenant_id == userDetails.tenant_id).ToListAsync();

        var minSumCode = alterCodesLst.Min(x => Convert.ToInt32(x.sum_code));
        var limitCodeForMinSumCode = alterCodesLst.FirstOrDefault(x => x.sum_code == minSumCode.ToString()).limit_code;

        List<clsServiceAreaData> dbDataset = await (from th in tenantDbContext.tfp_trans_header
            join td in tenantDbContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                equals new { a = td.fk_tenant_id, b = td.fk_action_id }
            join bc in tenantDbContext.tfp_budget_changes on new { a = td.fk_tenant_id, b = td.fk_change_id }
                equals new { a = bc.fk_tenant_id, b = bc.pk_change_id }
            join bp in tenantDbContext.tco_budget_phase on new { a = bc.fk_tenant_id, b = bc.fk_budget_phase_id }
                equals new { a = bp.fk_tenant_id, b = bp.pk_budget_phase_id }
            where th.fk_tenant_id == userDetails.tenant_id
                  && (td.budget_year == budYear)
                  && th.action_type >= 5
                  && !actionTypes.Contains(th.action_type)
                  && bp.org_budget_flag != 0
            select new clsServiceAreaData
            {
                actionType = th.action_type,
                accountCode = td.fk_account_code.Trim(),
                departmentCode = td.department_code.Trim(),
                functionCode = td.function_code.Trim(),
                year1Amount = td.year_1_amount,
                year2Amount = td.year_2_amount,
                year3Amount = td.year_3_amount,
                year4Amount = td.year_4_amount,
                lineOrderId = th.line_order,
                alterCode = td.fk_alter_code,
                budgetPhaseId = bp.pk_budget_phase_id,
                budgetPhaseDescription = bp.description,
                sort_order = bp.sort_order
            }).ToListAsync();

        dbDataset = (from a in dbDataset
            join ac in alterCodesLst on new { b = a.alterCode } equals new { b = ac.pk_alter_code } into ac1
            from ac2 in ac1.DefaultIfEmpty()
            select new clsServiceAreaData
            {
                actionType = a.actionType,
                accountCode = a.accountCode,
                departmentCode = a.departmentCode,
                functionCode = a.functionCode,
                year1Amount = a.year1Amount,
                year2Amount = a.year2Amount,
                year3Amount = a.year3Amount,
                year4Amount = a.year4Amount,
                lineOrderId = a.lineOrderId,
                sumCode = (ac2 == null || string.IsNullOrEmpty(ac2.sum_code)) ? string.Empty : ac2.sum_code,
                sumDescription = (ac2 == null || string.IsNullOrEmpty(ac2.sum_description)) ? string.Empty : ac2.sum_description,
                limitCode = (ac2 == null || string.IsNullOrEmpty(ac2.limit_code)) ? string.Empty : ac2.limit_code,
                limitDescription = (ac2 == null || string.IsNullOrEmpty(ac2.limit_description)) ? string.Empty : ac2.limit_description,
                cabFlag = (ac2 == null || ac2.cab_flag.Equals(null)) ? 0 : ac2.cab_flag,
                budgetPhaseId = a.budgetPhaseId,
                budgetPhaseDescription = a.budgetPhaseDescription,
                sort_order = a.sort_order
            }).ToList();

        if (lstAllDepartments.Any(x => !string.IsNullOrEmpty(x)) && lstAllFunctions.Any(x => !string.IsNullOrEmpty(x)))
        {
            dbDataset = dbDataset.Where(y => lstAllDepartments.Contains(y.departmentCode) && lstAllFunctions.Contains(y.functionCode)).ToList();
        }
        else if (lstAllFunctions.Any(x => !string.IsNullOrEmpty(x)))
        {
            dbDataset = dbDataset.Where(y => lstAllFunctions.Contains(y.functionCode)).ToList();
        }
        else if (lstAllDepartments.Any(x => !string.IsNullOrEmpty(x)))
        {
            dbDataset = dbDataset.Where(y => lstAllDepartments.Contains(y.departmentCode)).ToList();
        }

        List<clsServiceAreaData> mainDataset = (from d in dbDataset
            select new clsServiceAreaData
            {
                orgId = "",
                orgName = "",
                serviceId = "",
                serviceName = "",
                actionType = d.actionType,
                actionId = d.actionId,
                accountCode = d.accountCode,
                departmentCode = d.departmentCode,
                functionCode = d.functionCode,
                year1Amount = d.year1Amount,
                year2Amount = d.year2Amount,
                year3Amount = d.year3Amount,
                year4Amount = d.year4Amount,
                description = d.description,
                lineOrderId = d.lineOrderId,
                sumCode = d.sumCode,
                sumDescription = d.sumDescription,
                limitCode = d.limitCode,
                limitDescription = d.limitDescription,
                cabFlag = d.cabFlag,
                budgetPhaseId = d.budgetPhaseId,
                budgetPhaseDescription = d.budgetPhaseDescription,
                sort_order = d.sort_order
            }).ToList();

        string firstLevel = string.Empty;
        string secondLevel = string.Empty;

        if (lstOrgStructure.Any(z => z.type == "FINPLAN_LEVEL_1" && !string.IsNullOrEmpty(z.departmentCode)))
        {
            firstLevel = "dept";
        }
        else if (lstOrgStructure.Any(z => z.type == "FINPLAN_LEVEL_1" && !string.IsNullOrEmpty(z.functionCode)))
        {
            firstLevel = "function";
        }

        if (lstOrgStructure.Any(z => z.type == "FINPLAN_LEVEL_2" && !string.IsNullOrEmpty(z.departmentCode)))
        {
            secondLevel = "dept";
        }
        else if (lstOrgStructure.Any(z => z.type == "FINPLAN_LEVEL_2" && !string.IsNullOrEmpty(z.functionCode)))
        {
            secondLevel = "function";
        }

        mainDataset = _utility.AssignOrgIdAndServiceIdToResultSet(orgVersionContent, mainDataset, lstOrgStructure, firstLevel, secondLevel);

        Dictionary<string, clsLanguageString> langStringValuesBAtype = null;
        if (string.IsNullOrEmpty(langPref))
        {
            langStringValuesBAtype = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetManagement");
        }
        else
        {
            langStringValuesBAtype = await _utility.GetLanguageStringsAsync(langPref, userDetails.user_name, "BudgetManagement");
        }

        List<string> budgetPhases = mainDataset.OrderBy(w => w.sort_order).Select(x => x.budgetPhaseId.ToString()).Distinct().ToList();

        List<clsLimitCodeDetails> lstLimitCodesWithCabFlag1 = new List<clsLimitCodeDetails>();

        Dictionary<string, decimal> dictTotals = new Dictionary<string, decimal>();
        dynamic ServiceAreaBudgetData = new JObject();

        JArray fieldsArray = new JArray();
        Dictionary<string, int> dictFields = new Dictionary<string, int>();

        int fieldCount = 0;

        fieldsArray.Add("id");
        fieldCount++;
        dictFields.Add("id", fieldCount);

        fieldsArray.Add("parentId");
        fieldCount++;
        dictFields.Add("parentId", fieldCount);

        fieldsArray.Add("ServiceAreaId");
        fieldCount++;
        dictFields.Add("ServiceAreaId", fieldCount);

        fieldsArray.Add("ServiceArea");
        fieldCount++;
        dictFields.Add("ServiceArea", fieldCount);

        int limitCount = 0;

        decimal agggregateOfLimitCodesWithFlag1OnAllRows = 0;

        int countBudgetPhase = 0;
        foreach (string s in budgetPhases)
        {
            countBudgetPhase++;
            List<string> limitCodes = mainDataset.Where(x => x.budgetPhaseId.ToString() == s && !string.IsNullOrEmpty(x.limitCode)).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList();
            foreach (string l in limitCodes)
            {
                limitCount++;
                if (!dictTotals.ContainsKey($"{l}{countBudgetPhase}"))
                {
                    dictTotals.Add($"{l}{countBudgetPhase}", 0);
                }

                clsLimitCodeDetails lCode = new clsLimitCodeDetails()
                {
                    sumCode = s,
                    limitCode = $"{l}",
                    limitDescription = mainDataset.FirstOrDefault(x => x.limitCode == l).limitDescription
                };
                lstLimitCodesWithCabFlag1.Add(lCode);
                fieldsArray.Add($"limitCode{l}{countBudgetPhase}");
                fieldCount++;
                if (!dictFields.ContainsKey(($"limitCode{l}{countBudgetPhase}")))
                {
                    dictFields.Add(($"limitCode{l}{countBudgetPhase}"), fieldCount);
                }
            }

            fieldsArray.Add($"agggregateOfLimitCodesWithBudgetPhase{countBudgetPhase}");
            fieldCount++;
            dictFields.Add($"agggregateOfLimitCodesWithBudgetPhase{countBudgetPhase}", fieldCount);

            dictTotals.Add($"agggregateOfLimitCodesWithBudgetPhase{countBudgetPhase}", 0);
        }

        fieldsArray.Add("TotalOfAllBudgetPhases");
        dictTotals.Add("TotalOfAllBudgetPhases", 0);

        fieldsArray.Add("notAllocatedDataSummary");
        fieldCount++;
        dictFields.Add("notAllocatedDataSummary", fieldCount);

        fieldsArray.Add("summary");
        fieldCount++;
        dictFields.Add("summary", fieldCount);

        /*cacculated from DB*/
        if (displayTempColumns)
        {
            fieldsArray.Add("tempCostReduction");
            fieldCount++;
            dictFields.Add("tempCostReduction", fieldCount);

            fieldsArray.Add("tempNewPriorities");
            fieldCount++;
            dictFields.Add("tempNewPriorities", fieldCount);
        }
        else
        {
            fieldsArray.Add("proposedNewPriority");
            fieldCount++;
            dictFields.Add("proposedNewPriority", fieldCount);

            fieldsArray.Add("onGoingActions");
            fieldCount++;
            dictFields.Add("onGoingActions", fieldCount);

            fieldsArray.Add("operationaleffectInvestments");
            fieldCount++;
            dictFields.Add("operationaleffectInvestments", fieldCount);

            fieldsArray.Add("newPriorities");
            fieldCount++;
            dictFields.Add("newPriorities", fieldCount);
        }

        ServiceAreaBudgetData.Add("Fields", fieldsArray);

        dynamic titlesArray = new JArray();
        titlesArray.Add(" ");
        titlesArray.Add(" ");
        titlesArray.Add(" ");
        titlesArray.Add((langStringValuesBAtype["cmn_service_area"]).LangText);
        foreach (string s in budgetPhases)
        {
            List<string> limitCodes = lstLimitCodesWithCabFlag1.Where(x => x.sumCode == s).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList();
            foreach (string l in limitCodes)
            {
                titlesArray.Add(lstLimitCodesWithCabFlag1.FirstOrDefault(x => x.limitCode == l).limitDescription);
            }
            titlesArray.Add(mainDataset.FirstOrDefault(x => x.budgetPhaseId.ToString() == s).budgetPhaseDescription);
        }

        titlesArray.Add((langStringValuesBAtype["total_of_all_budget_phases"]).LangText);

        /*cacculated from DB*/
        titlesArray.Add((langStringValuesBAtype["BM_not_allocated_Data_summary_text"]).LangText);
        titlesArray.Add((langStringValuesBAtype["BM_SAbudget_currBdgt"]).LangText + budYear.ToString() + "-" + (budYear + 3).ToString());

        if (displayTempColumns)
        {
            titlesArray.Add((langStringValuesBAtype["BM_SAbudget_crr_temp"]).LangText);
            titlesArray.Add((langStringValuesBAtype["BM_SAbudget_newPriorities_temp"]).LangText);
        }
        else
        {
            titlesArray.Add((langStringValuesBAtype["BM_SAbudget_proposedNewPrio"]).LangText);
            titlesArray.Add((langStringValuesBAtype["BM_ongoing_action_summary_text"]).LangText);
            titlesArray.Add((langStringValuesBAtype["BM_operational_effect_investments_text"]).LangText);
            titlesArray.Add((langStringValuesBAtype["BM_rem_amt_new_prts_text"]).LangText);
        }

        ServiceAreaBudgetData.Add("Titles", titlesArray);

        Dictionary<string, string> orgIds = new Dictionary<string, string>();

        lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_1").ToList().ForEach(a =>
        {
            if (!orgIds.Keys.Contains(a.id))
            {
                orgIds.Add(a.id, a.name);
            }
        });

        var filteredOrdStructure = (from o in lstOrgStructure
            where o.type == "FINPLAN_LEVEL_2" && (o.id != null || o.id != "")
            select new
            {
                o.id,
                o.name,
                o.functionCode,
                o.departmentCode
            }).ToList();

        dynamic finalData = new JArray();

        int childId = 2407;

        foreach (string orgId in orgIds.Keys.Distinct().Where(a => !string.IsNullOrEmpty(a)).OrderBy(x => x).ToList())
        {
            decimal totalSumCodes = 0;
            decimal budgetPhaseSpecificTotal = 0;
            List<string> lstOrgIdDepartsments = lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_1" && z.id == orgId).Where(a => !string.IsNullOrEmpty(a.departmentCode)).Select(y => y.departmentCode).Distinct().ToList();
            List<string> lstOrgIdFunctions = lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_1" && z.id == orgId).Where(a => !string.IsNullOrEmpty(a.functionCode)).Select(y => y.functionCode).Distinct().ToList();

            dynamic data = new JArray();

            dynamic ServiceArea = new JObject();
            ServiceArea.Add("id", orgId);
            ServiceArea.Add("parentId", null);
            ServiceArea.Add("ServiceAreaId", orgId);
            ServiceArea.Add("ServiceArea", orgIds[orgId]);

            List<clsServiceAreaData> mainDatasetFirstLevel = new List<clsServiceAreaData>();

            if (firstLevel == "dept")
            {
                mainDatasetFirstLevel = mainDataset.Where(x => lstOrgIdDepartsments.Contains(x.departmentCode.Trim())).ToList();
            }
            else if (firstLevel == "function")
            {
                mainDatasetFirstLevel = mainDataset.Where(x => lstOrgIdFunctions.Contains(x.functionCode.Trim())).ToList();
            }

            if (yearSelected == "Year1")
            {
                countBudgetPhase = 0;
                foreach (string sCode in budgetPhases)
                {
                    countBudgetPhase++;
                    budgetPhaseSpecificTotal = 0;
                    decimal val = mainDatasetFirstLevel.Where(x => x.budgetPhaseId.ToString() == sCode).Sum(x => x.year1Amount) / 1000;
                    dictTotals["TotalOfAllBudgetPhases"] = dictTotals["TotalOfAllBudgetPhases"] + val;
                    totalSumCodes = totalSumCodes + val;
                    foreach (var lcode in lstLimitCodesWithCabFlag1.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                    {
                        val = mainDatasetFirstLevel.Where(x => x.budgetPhaseId.ToString() == sCode && x.limitCode == lcode).Sum(x => x.year1Amount) / 1000;
                        data.Add(val);
                        dictTotals[$"{lcode}{countBudgetPhase}"] = dictTotals[$"{lcode}{countBudgetPhase}"] + val;
                        if (lcode == limitCodeForMinSumCode)
                        {
                        }
                        else
                        {
                            budgetPhaseSpecificTotal = budgetPhaseSpecificTotal + val;
                        }
                    }

                    agggregateOfLimitCodesWithFlag1OnAllRows = agggregateOfLimitCodesWithFlag1OnAllRows + totalSumCodes;
                    data.Add(budgetPhaseSpecificTotal);
                    dictTotals[$"agggregateOfLimitCodesWithBudgetPhase{countBudgetPhase}"] = dictTotals[$"agggregateOfLimitCodesWithBudgetPhase{countBudgetPhase}"] + budgetPhaseSpecificTotal;
                    budgetPhaseSpecificTotal = 0;
                }
                data.Add(totalSumCodes);

                // Below are DB Columns
                data.Add(0);
                data.Add(0);
                if (displayTempColumns)
                {
                    data.Add(0);
                    data.Add(0);
                }
                else
                {
                    data.Add(0);
                    data.Add(0);
                    data.Add(0);
                    data.Add(0);
                }
            }
            else if (yearSelected == "Year2")
            {
                countBudgetPhase = 0;
                foreach (string sCode in budgetPhases)
                {
                    countBudgetPhase++;
                    budgetPhaseSpecificTotal = 0;
                    decimal val = mainDatasetFirstLevel.Where(x => x.budgetPhaseId.ToString() == sCode).Sum(x => x.year2Amount) / 1000;
                    dictTotals["TotalOfAllBudgetPhases"] = dictTotals["TotalOfAllBudgetPhases"] + val;
                    totalSumCodes = totalSumCodes + val;
                    foreach (var lcode in lstLimitCodesWithCabFlag1.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                    {
                        val = mainDatasetFirstLevel.Where(x => x.budgetPhaseId.ToString() == sCode && x.limitCode == lcode).Sum(x => x.year2Amount) / 1000;
                        data.Add(val);
                        dictTotals[$"{lcode}{countBudgetPhase}"] = dictTotals[$"{lcode}{countBudgetPhase}"] + val;
                        if (lcode == limitCodeForMinSumCode)
                        {
                        }
                        else
                        {
                            budgetPhaseSpecificTotal = budgetPhaseSpecificTotal + val;
                        }
                    }

                    agggregateOfLimitCodesWithFlag1OnAllRows = agggregateOfLimitCodesWithFlag1OnAllRows + totalSumCodes;
                    data.Add(budgetPhaseSpecificTotal);
                    dictTotals[$"agggregateOfLimitCodesWithBudgetPhase{countBudgetPhase}"] = dictTotals[$"agggregateOfLimitCodesWithBudgetPhase{countBudgetPhase}"] + budgetPhaseSpecificTotal;
                    budgetPhaseSpecificTotal = 0;
                }
                data.Add(totalSumCodes);

                // Below are DB Columns
                data.Add(0);
                data.Add(0);
                if (displayTempColumns)
                {
                    data.Add(0);
                    data.Add(0);
                }
                else
                {
                    data.Add(0);
                    data.Add(0);
                    data.Add(0);
                    data.Add(0);
                }
            }
            else if (yearSelected == "Year3")
            {
                countBudgetPhase = 0;
                foreach (string sCode in budgetPhases)
                {
                    countBudgetPhase++;
                    budgetPhaseSpecificTotal = 0;
                    decimal val = mainDatasetFirstLevel.Where(x => x.budgetPhaseId.ToString() == sCode).Sum(x => x.year3Amount) / 1000;
                    dictTotals["TotalOfAllBudgetPhases"] = dictTotals["TotalOfAllBudgetPhases"] + val;
                    totalSumCodes = totalSumCodes + val;
                    foreach (var lcode in lstLimitCodesWithCabFlag1.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                    {
                        val = mainDatasetFirstLevel.Where(x => x.budgetPhaseId.ToString() == sCode && x.limitCode == lcode).Sum(x => x.year3Amount) / 1000;
                        data.Add(val);
                        dictTotals[$"{lcode}{countBudgetPhase}"] = dictTotals[$"{lcode}{countBudgetPhase}"] + val;
                        if (lcode == limitCodeForMinSumCode)
                        {
                        }
                        else
                        {
                            budgetPhaseSpecificTotal = budgetPhaseSpecificTotal + val;
                        }
                    }

                    agggregateOfLimitCodesWithFlag1OnAllRows = agggregateOfLimitCodesWithFlag1OnAllRows + totalSumCodes;
                    data.Add(budgetPhaseSpecificTotal);
                    dictTotals[$"agggregateOfLimitCodesWithBudgetPhase{countBudgetPhase}"] = dictTotals[$"agggregateOfLimitCodesWithBudgetPhase{countBudgetPhase}"] + budgetPhaseSpecificTotal;
                    budgetPhaseSpecificTotal = 0;
                }
                data.Add(totalSumCodes);

                // Below are DB Columns
                data.Add(0);
                data.Add(0);
                if (displayTempColumns)
                {
                    data.Add(0);
                    data.Add(0);
                }
                else
                {
                    data.Add(0);
                    data.Add(0);
                    data.Add(0);
                    data.Add(0);
                }
            }
            else if (yearSelected == "Year4")
            {
                countBudgetPhase = 0;
                foreach (string sCode in budgetPhases)
                {
                    countBudgetPhase++;
                    budgetPhaseSpecificTotal = 0;
                    decimal val = mainDatasetFirstLevel.Where(x => x.budgetPhaseId.ToString() == sCode).Sum(x => x.year4Amount) / 1000;
                    dictTotals["TotalOfAllBudgetPhases"] = dictTotals["TotalOfAllBudgetPhases"] + val;
                    totalSumCodes = totalSumCodes + val;
                    foreach (var lcode in lstLimitCodesWithCabFlag1.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                    {
                        val = mainDatasetFirstLevel.Where(x => x.budgetPhaseId.ToString() == sCode && x.limitCode == lcode).Sum(x => x.year4Amount) / 1000;
                        data.Add(val);
                        dictTotals[$"{lcode}{countBudgetPhase}"] = dictTotals[$"{lcode}{countBudgetPhase}"] + val;
                        if (lcode == limitCodeForMinSumCode)
                        {
                        }
                        else
                        {
                            budgetPhaseSpecificTotal = budgetPhaseSpecificTotal + val;
                        }
                    }

                    agggregateOfLimitCodesWithFlag1OnAllRows = agggregateOfLimitCodesWithFlag1OnAllRows + totalSumCodes;
                    data.Add(budgetPhaseSpecificTotal);
                    dictTotals[$"agggregateOfLimitCodesWithBudgetPhase{countBudgetPhase}"] = dictTotals[$"agggregateOfLimitCodesWithBudgetPhase{countBudgetPhase}"] + budgetPhaseSpecificTotal;
                    budgetPhaseSpecificTotal = 0;
                }
                data.Add(totalSumCodes);

                // Below are DB Columns
                data.Add(0);
                data.Add(0);
                if (displayTempColumns)
                {
                    data.Add(0);
                    data.Add(0);
                }
                else
                {
                    data.Add(0);
                    data.Add(0);
                    data.Add(0);
                    data.Add(0);
                }
            }

            ServiceArea.Add("Data", data);
            finalData.Add(ServiceArea);

            Dictionary<string, string> serviceIdsForCurrentOrgId = new Dictionary<string, string>();
            mainDataset.Where(x => x.orgId == orgId).ToList().ForEach(y =>
            {
                if (!serviceIdsForCurrentOrgId.Keys.Contains(y.serviceId))
                {
                    serviceIdsForCurrentOrgId.Add(y.serviceId, y.serviceName);
                }
            });

            foreach (string serviceId in serviceIdsForCurrentOrgId.Keys.Distinct().Where(a => !string.IsNullOrEmpty(a)).OrderBy(x => x).ToList())
            {
                totalSumCodes = 0;
                childId++;

                ServiceArea = new JObject();
                data = new JArray();
                ServiceArea.Add("id", childId.ToString());
                ServiceArea.Add("parentId", orgId);
                ServiceArea.Add("ServiceAreaId", serviceId);
                ServiceArea.Add("ServiceArea", serviceIdsForCurrentOrgId[serviceId]);

                List<string> lstServiceIdDepartments = filteredOrdStructure.Where(x => x.id == serviceId).Select(y => y.departmentCode).Distinct().ToList();
                List<string> lstServiceIdFunctions = filteredOrdStructure.Where(x => x.id == serviceId).Select(y => y.functionCode).Distinct().ToList();

                string hierarchy = string.Empty;

                if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_1").Where(a => !string.IsNullOrEmpty(a.departmentCode)).Select(y => y.departmentCode).Count() > 0 &&
                    lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_2").Where(a => !string.IsNullOrEmpty(a.departmentCode)).Select(y => y.departmentCode).Count() > 0)
                {
                    hierarchy = "dept-dept";
                }
                else if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_1").Where(a => !string.IsNullOrEmpty(a.departmentCode)).Select(y => y.departmentCode).Count() > 0 &&
                         lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_2").Where(a => !string.IsNullOrEmpty(a.functionCode)).Select(y => y.functionCode).Count() > 0)
                {
                    hierarchy = "dept-function";
                }
                else if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_1").Where(a => !string.IsNullOrEmpty(a.functionCode)).Select(y => y.functionCode).Count() > 0 &&
                         lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_2").Where(a => !string.IsNullOrEmpty(a.departmentCode)).Select(y => y.departmentCode).Count() > 0)
                {
                    hierarchy = "function-dept";
                }
                else if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_1").Where(a => !string.IsNullOrEmpty(a.functionCode)).Select(y => y.functionCode).Count() > 0 &&
                         lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_2").Where(a => !string.IsNullOrEmpty(a.functionCode)).Select(y => y.functionCode).Count() > 0)
                {
                    hierarchy = "function-function";
                }

                List<clsServiceAreaData> mainDatasetSecondLevel = new List<clsServiceAreaData>();

                if (hierarchy == "dept-dept")
                {
                    mainDatasetSecondLevel = mainDatasetFirstLevel.Where(x => lstServiceIdDepartments.Contains(x.departmentCode)).ToList();
                }
                else if (hierarchy == "dept-function")
                {
                    mainDatasetSecondLevel = mainDatasetFirstLevel.Where(x => lstServiceIdFunctions.Contains(x.functionCode)).ToList();
                }
                else if (hierarchy == "function-dept")
                {
                    mainDatasetSecondLevel = mainDatasetFirstLevel.Where(x => lstServiceIdDepartments.Contains(x.departmentCode)).ToList();
                }
                else if (hierarchy == "function-function")
                {
                    mainDatasetSecondLevel = mainDatasetFirstLevel.Where(x => lstServiceIdFunctions.Contains(x.functionCode)).ToList();
                }

                if (yearSelected == "Year1")
                {
                    foreach (string sCode in budgetPhases)
                    {
                        decimal val = mainDatasetSecondLevel.Where(x => x.budgetPhaseId.ToString() == sCode).Sum(x => x.year1Amount) / 1000;
                        totalSumCodes = totalSumCodes + val;
                        budgetPhaseSpecificTotal = 0;
                        foreach (var lcode in lstLimitCodesWithCabFlag1.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                        {
                            val = mainDatasetSecondLevel.Where(x => x.budgetPhaseId.ToString() == sCode && x.limitCode == lcode).Sum(x => x.year1Amount) / 1000;
                            data.Add(val);
                            if (lcode == limitCodeForMinSumCode)
                            {
                            }
                            else
                            {
                                budgetPhaseSpecificTotal = budgetPhaseSpecificTotal + val;
                            }
                        }
                        data.Add(budgetPhaseSpecificTotal);
                        budgetPhaseSpecificTotal = 0;
                    }
                    data.Add(totalSumCodes);

                    // Below are DB Columns
                    data.Add(0);
                    data.Add(0);
                    if (displayTempColumns)
                    {
                        data.Add(0);
                        data.Add(0);
                    }
                    else
                    {
                        data.Add(0);
                        data.Add(0);
                        data.Add(0);
                        data.Add(0);
                    }
                }
                else if (yearSelected == "Year2")
                {
                    foreach (string sCode in budgetPhases)
                    {
                        decimal val = mainDatasetSecondLevel.Where(x => x.budgetPhaseId.ToString() == sCode).Sum(x => x.year2Amount) / 1000;
                        totalSumCodes = totalSumCodes + val;
                        budgetPhaseSpecificTotal = 0;
                        foreach (var lcode in lstLimitCodesWithCabFlag1.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                        {
                            val = mainDatasetSecondLevel.Where(x => x.budgetPhaseId.ToString() == sCode && x.limitCode == lcode).Sum(x => x.year2Amount) / 1000;
                            data.Add(val);
                            if (lcode == limitCodeForMinSumCode)
                            {
                            }
                            else
                            {
                                budgetPhaseSpecificTotal = budgetPhaseSpecificTotal + val;
                            }
                        }
                        data.Add(budgetPhaseSpecificTotal);
                        budgetPhaseSpecificTotal = 0;
                    }
                    data.Add(totalSumCodes);

                    // Below are DB Columns
                    data.Add(0);
                    data.Add(0);
                    if (displayTempColumns)
                    {
                        data.Add(0);
                        data.Add(0);
                    }
                    else
                    {
                        data.Add(0);
                        data.Add(0);
                        data.Add(0);
                        data.Add(0);
                    }
                }
                else if (yearSelected == "Year3")
                {
                    foreach (string sCode in budgetPhases)
                    {
                        decimal val = mainDatasetSecondLevel.Where(x => x.budgetPhaseId.ToString() == sCode).Sum(x => x.year3Amount) / 1000;
                        totalSumCodes = totalSumCodes + val;
                        budgetPhaseSpecificTotal = 0;
                        foreach (var lcode in lstLimitCodesWithCabFlag1.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                        {
                            val = mainDatasetSecondLevel.Where(x => x.budgetPhaseId.ToString() == sCode && x.limitCode == lcode).Sum(x => x.year3Amount) / 1000;
                            data.Add(val);
                            if (lcode == limitCodeForMinSumCode)
                            {
                            }
                            else
                            {
                                budgetPhaseSpecificTotal = budgetPhaseSpecificTotal + val;
                            }
                        }
                        data.Add(budgetPhaseSpecificTotal);
                        budgetPhaseSpecificTotal = 0;
                    }
                    data.Add(totalSumCodes);

                    // Below are DB Columns
                    data.Add(0);
                    data.Add(0);
                    if (displayTempColumns)
                    {
                        data.Add(0);
                        data.Add(0);
                    }
                    else
                    {
                        data.Add(0);
                        data.Add(0);
                        data.Add(0);
                        data.Add(0);
                    }
                }
                else if (yearSelected == "Year4")
                {
                    foreach (string sCode in budgetPhases)
                    {
                        decimal val = mainDatasetSecondLevel.Where(x => x.budgetPhaseId.ToString() == sCode).Sum(x => x.year4Amount) / 1000;
                        totalSumCodes = totalSumCodes + val;
                        budgetPhaseSpecificTotal = 0;
                        foreach (var lcode in lstLimitCodesWithCabFlag1.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                        {
                            val = mainDatasetSecondLevel.Where(x => x.budgetPhaseId.ToString() == sCode && x.limitCode == lcode).Sum(x => x.year4Amount) / 1000;
                            data.Add(val);
                            if (lcode == limitCodeForMinSumCode)
                            {
                            }
                            else
                            {
                                budgetPhaseSpecificTotal = budgetPhaseSpecificTotal + val;
                            }
                        }
                        data.Add(budgetPhaseSpecificTotal);
                        budgetPhaseSpecificTotal = 0;
                    }
                    data.Add(totalSumCodes);

                    // Below are DB Columns
                    data.Add(0);
                    data.Add(0);
                    if (displayTempColumns)
                    {
                        data.Add(0);
                        data.Add(0);
                    }
                    else
                    {
                        data.Add(0);
                        data.Add(0);
                        data.Add(0);
                        data.Add(0);
                    }
                }
                ServiceArea.Add("Data", data);
                finalData.Add(ServiceArea);
            }
        }

        //Tab String
        dynamic TabStrings = new JArray() { ((langStringValuesBAtype["BM_meeting_tab"]).LangText + " " + budYear), ((langStringValuesBAtype["BM_meeting_tab"]).LangText + " " + (budYear + 1)), ((langStringValuesBAtype["BM_meeting_tab"]).LangText + " " + (budYear + 2)), ((langStringValuesBAtype["BM_meeting_tab"]).LangText + " " + (budYear + 3)) };
        //Add total to Json
        dynamic TotalObject = new JObject();
        dynamic TotalData = new JArray() { dictTotals.Values.ToList() };
        TotalData.Add(0);
        TotalData.Add(0);
        if (displayTempColumns)
        {
            TotalData.Add(0);
            TotalData.Add(0);
        }
        else
        {
            TotalData.Add(0);
            TotalData.Add(0);
            TotalData.Add(0);
            TotalData.Add(0);
        }

        TotalObject.Add("id", null);
        TotalObject.Add("parentId", null);
        TotalObject.Add("ServiceAreaId", null);
        TotalObject.Add("ServiceArea", (langStringValuesBAtype["cmn_title_total"]).LangText);
        TotalObject.Add("Data", TotalData);
        finalData.Add(TotalObject);
        JObject jsonCfgRow = JObject.Parse(await _utility.GetApplicationSettingAsync("GetBudgetAndFinancialData_grid_config"));
        dynamic backgroudIndex = new JArray();

        int fieldIndexCount = 0;
        foreach (var d in fieldsArray)
        {
            var val = Convert.ToString(((Newtonsoft.Json.Linq.JValue)d).Value);
            if (val.StartsWith($"limitCode{limitCodeForMinSumCode}") ||
                val.StartsWith("agggregateOfLimitCodesWithBudgetPhase") ||
                val == "TotalOfAllBudgetPhases" ||
                val == "summary")
            {
                backgroudIndex.Add(fieldIndexCount - 3);
            }

            fieldIndexCount++;
        }

        ServiceAreaBudgetData.gridConfig = jsonCfgRow;
        ServiceAreaBudgetData.amountFormat = format;
        ServiceAreaBudgetData.Add("backgroudIndex", backgroudIndex);
        ServiceAreaBudgetData.Add("tabCaption", TabStrings);
        ServiceAreaBudgetData.Add("jsonData", finalData);

        CultureInfo ci = CultureInfo.InvariantCulture;
        ServiceAreaBudgetData.modifiedAt = (string)DateTime.UtcNow.ToString(ci);

        string defaultKey = await _utility.GetParameterValueAsync(userId, "IS_ONGOINGACTION_AVAILABLE");
        bool isOngoingAActionsAvailable = false;
        if (!string.IsNullOrEmpty(defaultKey.Trim()))
        {
            isOngoingAActionsAvailable = System.Convert.ToBoolean(defaultKey.Trim());
        }

        ServiceAreaBudgetData.showOngoingActions = isOngoingAActionsAvailable;
        ServiceAreaBudgetData.agggregateOfLimitCodesWithFlag1OnAllRows = agggregateOfLimitCodesWithFlag1OnAllRows;

        if (splitData == true)
        {
            var lst = (from u in tenantDbContext.vwUserDetails
                where u.pk_id == -1
                select new clsServiceAreaData
                {
                    orgId = string.Empty,
                    serviceId = string.Empty,
                    year1Amount = 0,
                    year2Amount = 0,
                    year3Amount = 0,
                    year4Amount = 0
                }).ToList();

            foreach (var item in ServiceAreaBudgetData["jsonData"])
            {
                if ((string)item["ServiceAreaId"] != null)
                {
                    for (int i = 4; i < ((JArray)ServiceAreaBudgetData["Fields"]).Count(); i++)
                    {
                        if ((string)ServiceAreaBudgetData["Fields"][i] == "tempNewPriorities")
                        {
                            int indexOfagggregateOfLimitCodesWithFlag1 = ((JArray)ServiceAreaBudgetData["Fields"]).ToObject<List<string>>().IndexOf("agggregateOfLimitCodesWithFlag1");

                            if ((string)item["parentId"] == null && secondLevel == string.Empty)
                            {
                                if (yearSelected == "Year1")
                                {
                                    lst.Add(new clsServiceAreaData()
                                    {
                                        orgId = (string)item["ServiceAreaId"],
                                        serviceId = string.Empty,
                                        year1Amount = Math.Round((Convert.ToDecimal(activeChangeData.year_1_amount / 1000) * Convert.ToDecimal(Convert.ToDecimal(item["Data"][indexOfagggregateOfLimitCodesWithFlag1 - 4]) / Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]))))
                                    });
                                }
                                else if (yearSelected == "Year2")
                                {
                                    lst.Add(new clsServiceAreaData()
                                    {
                                        orgId = (string)item["ServiceAreaId"],
                                        serviceId = string.Empty,
                                        year2Amount = Math.Round((Convert.ToDecimal(activeChangeData.year_2_amount / 1000) * (Convert.ToDecimal(item["Data"][indexOfagggregateOfLimitCodesWithFlag1 - 4]) / Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]))))
                                    });
                                }
                                else if (yearSelected == "Year3")
                                {
                                    lst.Add(new clsServiceAreaData()
                                    {
                                        orgId = (string)item["ServiceAreaId"],
                                        serviceId = string.Empty,
                                        year3Amount = Math.Round((Convert.ToDecimal(activeChangeData.year_3_amount / 1000) * (Convert.ToDecimal(item["Data"][indexOfagggregateOfLimitCodesWithFlag1 - 4]) / Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]))))
                                    });
                                }
                                else if (yearSelected == "Year4")
                                {
                                    lst.Add(new clsServiceAreaData()
                                    {
                                        orgId = (string)item["ServiceAreaId"],
                                        serviceId = string.Empty,
                                        year4Amount = Math.Round((Convert.ToDecimal(activeChangeData.year_4_amount / 1000) * (Convert.ToDecimal(item["Data"][indexOfagggregateOfLimitCodesWithFlag1 - 4]) / Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]))))
                                    });
                                }
                            }
                            else
                            {
                                if ((string)item["parentId"] == null)
                                {
                                    continue;
                                }

                                string parentId = Convert.ToString(item["parentId"]);
                                JArray arr = (JArray)ServiceAreaBudgetData["jsonData"];
                                string level1 = (string)arr.FirstOrDefault(x => (string)x["id"] == parentId && (string)x["parentId"] == null)["ServiceAreaId"];

                                if (yearSelected == "Year1")
                                {
                                    lst.Add(new clsServiceAreaData()
                                    {
                                        orgId = level1,
                                        serviceId = (string)item["ServiceAreaId"],
                                        year1Amount = Math.Round(Convert.ToDecimal(activeChangeData.year_1_amount / 1000) * (Convert.ToDecimal(item["Data"][indexOfagggregateOfLimitCodesWithFlag1 - 4]) / Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"])))
                                    });
                                }
                                else if (yearSelected == "Year2")
                                {
                                    lst.Add(new clsServiceAreaData()
                                    {
                                        orgId = level1,
                                        serviceId = (string)item["ServiceAreaId"],
                                        year2Amount = Math.Round(Convert.ToDecimal(activeChangeData.year_2_amount / 1000) * (Convert.ToDecimal(item["Data"][indexOfagggregateOfLimitCodesWithFlag1 - 4]) / Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"])))
                                    });
                                }
                                else if (yearSelected == "Year3")
                                {
                                    lst.Add(new clsServiceAreaData()
                                    {
                                        orgId = level1,
                                        serviceId = (string)item["ServiceAreaId"],
                                        year3Amount = Math.Round(Convert.ToDecimal(activeChangeData.year_3_amount / 1000) * (Convert.ToDecimal(item["Data"][indexOfagggregateOfLimitCodesWithFlag1 - 4]) / Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"])))
                                    });
                                }
                                else if (yearSelected == "Year4")
                                {
                                    lst.Add(new clsServiceAreaData()
                                    {
                                        orgId = level1,
                                        serviceId = (string)item["ServiceAreaId"],
                                        year4Amount = Math.Round(Convert.ToDecimal(activeChangeData.year_4_amount / 1000) * (Convert.ToDecimal(item["Data"][indexOfagggregateOfLimitCodesWithFlag1 - 4]) / Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"])))
                                    });
                                }
                            }
                        }
                    }
                }
            }

            List<tfp_temp_budget_limits> lstTfpTempBudgetLimits = tenantDbContext.tfp_temp_budget_limits.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budYear && x.action_type == 40).ToList();

            List<udtTfpTempBudgetLimits> limitsData = new List<udtTfpTempBudgetLimits>();
            int count = 0;
            decimal totalYear1 = 0;
            decimal totalYear2 = 0;
            decimal totalYear3 = 0;
            decimal totalYear4 = 0;
            foreach (var l in lst)
            {
                udtTfpTempBudgetLimits limit = new udtTfpTempBudgetLimits();
                limit.fk_tenant_id = userDetails.tenant_id;
                limit.budget_year = budYear;
                limit.fp_level_1_value = l.orgId;
                limit.fp_level_2_value = l.serviceId;
                limit.action_type = 40;

                count++;

                if (count == lst.Count())
                {
                    limit.year_1_limit = activeChangeData.year_1_amount - totalYear1;

                    limit.year_2_limit = activeChangeData.year_2_amount - totalYear2;

                    limit.year_3_limit = activeChangeData.year_3_amount - totalYear3;

                    limit.year_4_limit = activeChangeData.year_4_amount - totalYear4;
                }
                else
                {
                    limit.year_1_limit = (yearSelected == "Year2" || yearSelected == "Year3" || yearSelected == "Year4") ?
                        lstTfpTempBudgetLimits.FirstOrDefault(x => x.fp_level_1_value == l.orgId && x.fp_level_2_value == l.serviceId) == null ? 0 :
                            lstTfpTempBudgetLimits.FirstOrDefault(x => x.fp_level_1_value == l.orgId && x.fp_level_2_value == l.serviceId).year_1_limit : l.year1Amount * 1000;
                    totalYear1 += limit.year_1_limit;

                    limit.year_2_limit = (yearSelected == "Year3" || yearSelected == "Year4" || yearSelected == "Year1") ?
                        lstTfpTempBudgetLimits.FirstOrDefault(x => x.fp_level_1_value == l.orgId && x.fp_level_2_value == l.serviceId) == null ? 0 :
                            lstTfpTempBudgetLimits.FirstOrDefault(x => x.fp_level_1_value == l.orgId && x.fp_level_2_value == l.serviceId).year_2_limit : l.year2Amount * 1000;
                    totalYear2 += limit.year_2_limit;

                    limit.year_3_limit = (yearSelected == "Year4" || yearSelected == "Year1" || yearSelected == "Year2") ?
                        lstTfpTempBudgetLimits.FirstOrDefault(x => x.fp_level_1_value == l.orgId && x.fp_level_2_value == l.serviceId) == null ? 0 :
                            lstTfpTempBudgetLimits.FirstOrDefault(x => x.fp_level_1_value == l.orgId && x.fp_level_2_value == l.serviceId).year_3_limit : l.year3Amount * 1000;
                    totalYear3 += limit.year_3_limit;

                    limit.year_4_limit = (yearSelected == "Year1" || yearSelected == "Year2" || yearSelected == "Year3") ?
                        lstTfpTempBudgetLimits.FirstOrDefault(x => x.fp_level_1_value == l.orgId && x.fp_level_2_value == l.serviceId) == null ? 0 :
                            lstTfpTempBudgetLimits.FirstOrDefault(x => x.fp_level_1_value == l.orgId && x.fp_level_2_value == l.serviceId).year_4_limit : l.year4Amount * 1000;
                    totalYear4 += limit.year_4_limit;
                }

                limit.updated = DateTime.UtcNow;
                limit.updated_by = userDetails.pk_id;
                limitsData.Add(limit);
            }

            prcInsertIntoTfpTempBudgetLimits spInsert = new prcInsertIntoTfpTempBudgetLimits();
            spInsert.udtTfpTempBudgetLimits = new List<udtTfpTempBudgetLimits>();
            spInsert.udtTfpTempBudgetLimits.AddRange(limitsData);
            spInsert.fk_tenant_id = userDetails.tenant_id;
            spInsert.budget_year = budYear;
            await tenantDbContext.Database.ExecuteStoredProcedureAsync(spInsert);

            ServiceAreaBudgetData.splitData = true;
            return ServiceAreaBudgetData;
        }

        ServiceAreaBudgetData.splitData = false;
        return ServiceAreaBudgetData;
    }



    public dynamic GetServiceAreaBudgetDataBasedOnFunctionView(string yearSelected, string userId, string langPref,
        int budgetYear, bool splitData = false)
    {
        return GetServiceAreaBudgetDataBasedOnFunctionViewAsync(yearSelected, userId, langPref, budgetYear, splitData).GetAwaiter().GetResult();
    }



    public async Task<dynamic> GetServiceAreaBudgetDataBasedOnFunctionViewAsync(string yearSelected, string userId, string langPref, int budgetYear, bool splitData = false)
    {
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
        tco_users_settings userSettings = await tenantDbContext.tco_users_settings.FirstOrDefaultAsync(x =>
            x.fk_user_id == userDetails.pk_id && x.tenant_id == userDetails.tenant_id && x.is_active_tenant);
        int budYear = budgetYear;
        var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));
        Dictionary<string, clsLanguageString> langStringValues = new Dictionary<string, clsLanguageString>();
        if (string.IsNullOrEmpty(langPref))
        {
            langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "NumberFormats");
        }
        else
        {
            langStringValues = await _utility.GetLanguageStringsAsync(langPref, userDetails.user_name, "NumberFormats");
        }
        string format = ((langStringValues.FirstOrDefault(v => v.Key == "amount")).Value).LangText;

        string viewTypeKey = "BUDMAN_FUNCTION_VIEW";
        vw_tco_parameters paramFunctionView = await tenantDbContext.vw_tco_parameters.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.param_name == viewTypeKey && x.active == 1);

        if (paramFunctionView == null)
        {
            viewTypeKey = "FINPLAN_LEVEL_1";
            paramFunctionView = await tenantDbContext.vw_tco_parameters.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.param_name == viewTypeKey && x.active == 1);
        }

        List<clsOrgStructure> lstOrgStructure = null;
        if (paramFunctionView != null)
        {
            lstOrgStructure = await _utility.GetTenantOrgStructureAsync(orgVersionContent, userId, viewTypeKey);
        }
        else
        {
            lstOrgStructure = new List<Helpers.clsOrgStructure>();
        }

        clsOrgStructureLevelDetails tenantOrgLevelDetails = null;
        if (paramFunctionView != null)
        {
            tenantOrgLevelDetails = _utility.GetTenantOrgLevelDetails(lstOrgStructure, viewTypeKey);
        }
        else
        {
            tenantOrgLevelDetails = _utility.GetTenantOrgLevelDetails(lstOrgStructure);
        }
        List<List<string>> DepartmentsAndFunctions = _utility.GetDepartmentsAndFunctionsForOrgStructureForDynamicLevels(lstOrgStructure, null, null, tenantOrgLevelDetails.level1, tenantOrgLevelDetails.level2, viewTypeKey);
        List<string> lstAllDepartments = DepartmentsAndFunctions[0];
        List<string> lstAllFunctions = DepartmentsAndFunctions[1];

        var dataActiveBudgetChanges = await (from t in tenantDbContext.tfp_budget_changes
            where t.fk_tenant_id == userDetails.tenant_id
                  && t.budget_year == budYear
                  && t.status == 1
                  && t.org_budget_flag == 1
            orderby t.change_date descending
            select t).ToListAsync();

        int activeChangeId = dataActiveBudgetChanges.Count() == 0 ? -1 :
            userSettings == null ? dataActiveBudgetChanges.FirstOrDefault().pk_change_id : userSettings.active_change_id == null ? dataActiveBudgetChanges.FirstOrDefault().pk_change_id :
            dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == userSettings.active_change_id) == null ? dataActiveBudgetChanges.FirstOrDefault().pk_change_id :
            dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == userSettings.active_change_id).pk_change_id;

        var activeChangeData = dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == activeChangeId);

        bool displayTempColumns = false;

        if (activeChangeData != null)
        {
            if (activeChangeData.workflow_status == 1 || activeChangeData.workflow_status == 20)
            {
                displayTempColumns = true;
            }
            else
            {
                displayTempColumns = false;
            }
        }
        else
        {
            displayTempColumns = true;
        }

        List<int> actionTypes = new List<int>() { 30, 40, 90, 100, 20, 101 };

        IQueryable<clsServiceAreaData> dbDataset = null;

        if (paramFunctionView != null)
        {
            dbDataset = (from th in tenantDbContext.tfp_trans_header
                join td in tenantDbContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                    equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                join tf in tenantDbContext.tco_functions on new { a = td.fk_tenant_id, b = td.function_code }
                    equals new { a = tf.pk_tenant_id, b = tf.pk_Function_code }
                join ac in tenantDbContext.tco_fp_alter_codes on new { a = td.fk_tenant_id, b = td.fk_alter_code }
                    equals new { a = ac.fk_tenant_id, b = ac.pk_alter_code } into ac1
                from ac2 in ac1.DefaultIfEmpty()
                where th.fk_tenant_id == userDetails.tenant_id
                      && (td.budget_year == budYear)
                      && th.action_type >= 5
                      && !actionTypes.Contains(th.action_type)
                select new clsServiceAreaData
                {
                    orgId = "",
                    orgName = "",
                    serviceId = tf.pk_Function_code,
                    serviceName = tf.display_name,
                    actionType = th.action_type,
                    actionId = th.pk_action_id,
                    accountCode = td.fk_account_code,
                    departmentCode = td.department_code,
                    functionCode = td.function_code,
                    year1Amount = td.year_1_amount,
                    year2Amount = td.year_2_amount,
                    year3Amount = td.year_3_amount,
                    year4Amount = td.year_4_amount,
                    description = th.description,
                    lineOrderId = th.line_order,
                    isManuallyAdded = th.isManuallyAdded,
                    sumCode = string.IsNullOrEmpty(ac2.sum_code) ? string.Empty : ac2.sum_code,
                    sumDescription = string.IsNullOrEmpty(ac2.sum_description) ? string.Empty : ac2.sum_description,
                    limitCode = string.IsNullOrEmpty(ac2.limit_code) ? string.Empty : ac2.limit_code,
                    limitDescription = string.IsNullOrEmpty(ac2.limit_description) ? string.Empty : ac2.limit_description,
                    cabFlag = ac2.cab_flag.Equals(null) ? 0 : ac2.cab_flag
                });
        }
        else
        {
            dbDataset = (from th in tenantDbContext.tfp_trans_header
                join td in tenantDbContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                    equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                join tf in tenantDbContext.tco_functions on new { a = td.fk_tenant_id, b = td.function_code }
                    equals new { a = tf.pk_tenant_id, b = tf.pk_Function_code }
                join ac in tenantDbContext.tco_fp_alter_codes on new { a = td.fk_tenant_id, b = td.fk_alter_code }
                    equals new { a = ac.fk_tenant_id, b = ac.pk_alter_code } into ac1
                from ac2 in ac1.DefaultIfEmpty()
                where th.fk_tenant_id == userDetails.tenant_id
                      && (td.budget_year == budYear)
                      && th.action_type >= 5
                      && !actionTypes.Contains(th.action_type)
                select new clsServiceAreaData
                {
                    orgId = tf.pk_Function_code,
                    orgName = tf.display_name,
                    serviceId = "",
                    serviceName = "",
                    actionType = th.action_type,
                    actionId = th.pk_action_id,
                    accountCode = td.fk_account_code,
                    departmentCode = td.department_code,
                    functionCode = td.function_code,
                    year1Amount = td.year_1_amount,
                    year2Amount = td.year_2_amount,
                    year3Amount = td.year_3_amount,
                    year4Amount = td.year_4_amount,
                    description = th.description,
                    lineOrderId = th.line_order,
                    isManuallyAdded = th.isManuallyAdded,
                    sumCode = string.IsNullOrEmpty(ac2.sum_code) ? string.Empty : ac2.sum_code,
                    sumDescription = string.IsNullOrEmpty(ac2.sum_description) ? string.Empty : ac2.sum_description,
                    limitCode = string.IsNullOrEmpty(ac2.limit_code) ? string.Empty : ac2.limit_code,
                    limitDescription = string.IsNullOrEmpty(ac2.limit_description) ? string.Empty : ac2.limit_description,
                    cabFlag = ac2.cab_flag.Equals(null) ? 0 : ac2.cab_flag
                });
        }

        if (lstAllDepartments.Where(x => !string.IsNullOrEmpty(x)).Count() > 0 && lstAllFunctions.Where(x => !string.IsNullOrEmpty(x)).Count() > 0)
        {
            dbDataset = dbDataset.Where(y => lstAllDepartments.Contains(y.departmentCode) && lstAllFunctions.Contains(y.functionCode));
        }
        else if (lstAllFunctions.Where(x => !string.IsNullOrEmpty(x)).Count() > 0)
        {
            dbDataset = dbDataset.Where(y => lstAllFunctions.Contains(y.functionCode));
        }
        else if (lstAllDepartments.Where(x => !string.IsNullOrEmpty(x)).Count() > 0)
        {
            dbDataset = dbDataset.Where(y => lstAllDepartments.Contains(y.departmentCode));
        }

        List<clsServiceAreaData> mainDataset = await (from d in dbDataset
            select new clsServiceAreaData
            {
                orgId = d.orgId,
                orgName = d.orgName,
                serviceId = d.serviceId,
                serviceName = d.serviceName,
                actionType = d.actionType,
                actionId = d.actionId,
                accountCode = d.accountCode,
                departmentCode = d.departmentCode,
                functionCode = d.functionCode,
                year1Amount = d.year1Amount,
                year2Amount = d.year2Amount,
                year3Amount = d.year3Amount,
                year4Amount = d.year4Amount,
                description = d.description,
                lineOrderId = d.lineOrderId,
                isManuallyAdded = d.isManuallyAdded,
                sumCode = d.sumCode,
                sumDescription = d.sumDescription,
                limitCode = d.limitCode,
                limitDescription = d.limitDescription,
                cabFlag = d.cabFlag
            }).ToListAsync();

        string firstLevel = string.Empty;
        string secondLevel = string.Empty;

        if (lstOrgStructure.Where(z => z.type == viewTypeKey).Where(a => !string.IsNullOrEmpty(a.departmentCode)).Select(y => y.departmentCode).Count() > 0)
        {
            firstLevel = "dept";
        }
        else if (lstOrgStructure.Where(z => z.type == viewTypeKey).Count(y => !string.IsNullOrEmpty(y.functionCode)) > 0)
        {
            firstLevel = "function";
        }

        mainDataset = _utility.AssignOrgIdAndServiceIdToResultSetForDynamicLevels(mainDataset, lstOrgStructure, firstLevel, secondLevel, viewTypeKey);

        Dictionary<string, clsLanguageString> langStringValuesBAtype = new Dictionary<string, clsLanguageString>();
        if (string.IsNullOrEmpty(langPref))
        {
            langStringValuesBAtype = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetManagement");
        }
        else
        {
            langStringValuesBAtype = await _utility.GetLanguageStringsAsync(langPref, userDetails.user_name, "BudgetManagement");
        }

        List<string> sumCodesWithCabFlag1 = mainDataset.Where(w => w.cabFlag == 1 && !string.IsNullOrEmpty(w.sumCode)).Select(x => x.sumCode).Distinct().OrderBy(z => z).ToList();
        List<string> sumCodesWithCabFlag0 = mainDataset.Where(w => w.cabFlag == 0 && !string.IsNullOrEmpty(w.sumCode)).Select(x => x.sumCode).Distinct().OrderBy(z => z).ToList();

        List<clsSumCodeDetails> lstSumCodes = new List<clsSumCodeDetails>();
        List<clsLimitCodeDetails> lstLimitCodes = new List<clsLimitCodeDetails>();

        Dictionary<string, decimal> dictTotals = new Dictionary<string, decimal>();
        dynamic ServiceAreaBudgetData = new JObject();

        JArray fieldsArray = new JArray();
        Dictionary<string, int> dictFields = new Dictionary<string, int>();

        int fieldCount = 0;

        fieldsArray.Add("id");
        fieldCount++;
        dictFields.Add("id", fieldCount);

        fieldsArray.Add("parentId");
        fieldCount++;
        dictFields.Add("parentId", fieldCount);

        fieldsArray.Add("ServiceAreaId");
        fieldCount++;
        dictFields.Add("ServiceAreaId", fieldCount);

        fieldsArray.Add("ServiceArea");
        fieldCount++;
        dictFields.Add("ServiceArea", fieldCount);

        int limitCount = 0;

        decimal agggregateOfLimitCodesWithFlag1OnAllRows = 0;

        foreach (string s in sumCodesWithCabFlag1)
        {
            clsSumCodeDetails sCode = new clsSumCodeDetails()
            {
                sumCode = s,
                sumDescription = mainDataset.FirstOrDefault(x => x.sumCode == s).sumDescription
            };
            lstSumCodes.Add(sCode);

            List<string> limitCodes = mainDataset.Where(x => x.sumCode == s && !string.IsNullOrEmpty(x.limitCode)).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList();
            foreach (string l in limitCodes)
            {
                limitCount++;
                dictTotals.Add(l, 0);

                clsLimitCodeDetails lCode = new clsLimitCodeDetails()
                {
                    sumCode = s,
                    limitCode = l,
                    limitDescription = mainDataset.FirstOrDefault(x => x.limitCode == l).limitDescription
                };
                lstLimitCodes.Add(lCode);
                fieldsArray.Add("limitCode" + l);
                fieldCount++;
                dictFields.Add(("limitCode" + l), fieldCount);
            }
        }
        fieldsArray.Add("agggregateOfLimitCodesWithFlag1");
        fieldCount++;
        dictFields.Add("agggregateOfLimitCodesWithFlag1", fieldCount);

        dictTotals.Add("agggregateOfLimitCodesWithFlag1", 0);

        foreach (string s in sumCodesWithCabFlag0)
        {
            clsSumCodeDetails sCode = new clsSumCodeDetails()
            {
                sumCode = s,
                sumDescription = mainDataset.FirstOrDefault(x => x.sumCode == s).sumDescription
            };
            lstSumCodes.Add(sCode);

            List<string> limitCodes = mainDataset.Where(x => x.sumCode == s && !string.IsNullOrEmpty(x.limitCode)).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList();
            foreach (string l in limitCodes)
            {
                limitCount++;
                dictTotals.Add(l, 0);

                clsLimitCodeDetails lCode = new clsLimitCodeDetails()
                {
                    sumCode = s,
                    limitCode = l,
                    limitDescription = mainDataset.FirstOrDefault(x => x.limitCode == l).limitDescription
                };
                lstLimitCodes.Add(lCode);
                fieldsArray.Add("limitCode" + l);
                fieldCount++;
                dictFields.Add(("limitCode" + l), fieldCount);
            }
        }

        fieldsArray.Add("notAllocatedDataSummary");
        fieldCount++;
        dictFields.Add("notAllocatedDataSummary", fieldCount);

        fieldsArray.Add("summary");
        fieldCount++;
        dictFields.Add("summary", fieldCount);

        /*cacculated from DB*/
        if (!displayTempColumns)
        {
            fieldsArray.Add("proposedNewPriority");
            fieldCount++;
            dictFields.Add("proposedNewPriority", fieldCount);

            fieldsArray.Add("onGoingActions");
            fieldCount++;
            dictFields.Add("onGoingActions", fieldCount);

            fieldsArray.Add("operationaleffectInvestments");
            fieldCount++;
            dictFields.Add("operationaleffectInvestments", fieldCount);
        }

        ServiceAreaBudgetData.Add("Fields", fieldsArray);

        dynamic titlesArray = new JArray();
        titlesArray.Add(" ");
        titlesArray.Add(" ");
        titlesArray.Add(" ");
        titlesArray.Add((langStringValuesBAtype["cmn_service_area"]).LangText);
        foreach (string s in sumCodesWithCabFlag1)
        {
            List<string> limitCodes = lstLimitCodes.Where(x => x.sumCode == s).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList();
            foreach (string l in limitCodes)
            {
                titlesArray.Add(lstLimitCodes.FirstOrDefault(x => x.limitCode == l).limitDescription);
            }
        }
        titlesArray.Add((langStringValuesBAtype["BM_SAbudget_limitCodeAgg_1"]).LangText);

        foreach (string s in sumCodesWithCabFlag0)
        {
            List<string> limitCodes = lstLimitCodes.Where(x => x.sumCode == s).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList();
            foreach (string l in limitCodes)
            {
                titlesArray.Add(lstLimitCodes.FirstOrDefault(x => x.limitCode == l).limitDescription);
            }
        }

        /*cacculated from DB*/
        titlesArray.Add((langStringValuesBAtype["BM_not_allocated_Data_summary_text"]).LangText);
        titlesArray.Add((langStringValuesBAtype["BM_SAbudget_currBdgt"]).LangText + budYear.ToString() + "-" + (budYear + 3).ToString());

        if (!displayTempColumns)
        {
            titlesArray.Add((langStringValuesBAtype["BM_SAbudget_proposedNewPrio"]).LangText);
            titlesArray.Add((langStringValuesBAtype["BM_ongoing_action_summary_text"]).LangText);
            titlesArray.Add((langStringValuesBAtype["BM_operational_effect_investments_text"]).LangText);
        }

        ServiceAreaBudgetData.Add("Titles", titlesArray);

        Dictionary<string, string> orgIds = new Dictionary<string, string>();

        if (paramFunctionView != null)
        {
            lstOrgStructure.Where(z => z.type == viewTypeKey).ToList().ForEach(a =>
            {
                if (!orgIds.Keys.Contains(a.id))
                {
                    orgIds.Add(a.id, a.name);
                }
            });
        }
        else
        {
            mainDataset.ForEach(a =>
            {
                if (!orgIds.Keys.Contains(a.orgId))
                {
                    orgIds.Add(a.orgId, a.orgName);
                }
            });
        }

        var filteredOrdStructure1 = (from o in lstOrgStructure
            where o.type == "FINPLAN_LEVEL_2" && (o.id != null || o.id != "")
            select new
            {
                o.id,
                o.name,
                o.functionCode,
                o.departmentCode
            }).ToList();

        dynamic finalData = new JArray();

        int parentId = 9389;
        int childId = 2407;

        foreach (string orgId in orgIds.Keys.Distinct().Where(a => !string.IsNullOrEmpty(a)).OrderBy(x => x).ToList())
        {
            parentId++;
            decimal totalSumCodes = 0;
            List<string> lstOrgIdDepartsments = new List<string>();
            List<string> lstOrgIdFunctions = new List<string>();

            if (lstOrgStructure.Count() > 0)
            {
                lstOrgIdDepartsments = lstOrgStructure.Where(z => z.type == viewTypeKey && z.id == orgId).Where(a => !string.IsNullOrEmpty(a.departmentCode)).Select(y => y.departmentCode).Distinct().ToList();
                lstOrgIdFunctions = lstOrgStructure.Where(z => z.type == viewTypeKey && z.id == orgId).Where(a => !string.IsNullOrEmpty(a.functionCode)).Select(y => y.functionCode).Distinct().ToList();
            }
            else
            {
                lstOrgIdFunctions.Add(orgId);
            }

            dynamic data = new JArray();

            dynamic ServiceArea = new JObject();
            ServiceArea.Add("id", parentId);
            ServiceArea.Add("parentId", null);
            ServiceArea.Add("ServiceAreaId", orgId);
            ServiceArea.Add("ServiceArea", orgIds[orgId]);

            List<clsServiceAreaData> mainDatasetFirstLevel = new List<clsServiceAreaData>();

            if (firstLevel == "dept")
            {
                mainDatasetFirstLevel = mainDataset.Where(x => lstOrgIdDepartsments.Contains(x.departmentCode)).ToList();
            }
            else if (firstLevel == "function")
            {
                mainDatasetFirstLevel = mainDataset.Where(x => lstOrgIdFunctions.Contains(x.functionCode)).ToList();
            }
            else if (lstOrgIdDepartsments.Count() > 0 && lstOrgIdFunctions.Count() > 0)
            {
                mainDatasetFirstLevel = mainDataset.Where(x => lstOrgIdFunctions.Contains(x.functionCode) && lstOrgIdDepartsments.Contains(x.departmentCode)).ToList();
            }
            else if (lstOrgIdDepartsments.Count() > 0)
            {
                mainDatasetFirstLevel = mainDataset.Where(x => lstOrgIdDepartsments.Contains(x.departmentCode)).ToList();
            }
            else if (lstOrgIdFunctions.Count() > 0)
            {
                mainDatasetFirstLevel = mainDataset.Where(x => lstOrgIdFunctions.Contains(x.functionCode)).ToList();
            }

            if (yearSelected == "Year1")
            {
                foreach (string sCode in sumCodesWithCabFlag1)
                {
                    decimal val = mainDatasetFirstLevel.Where(x => x.sumCode == sCode).Sum(x => x.year1Amount) / 1000;
                    totalSumCodes = totalSumCodes + val;
                    foreach (var lcode in lstLimitCodes.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                    {
                        val = mainDatasetFirstLevel.Where(x => x.limitCode == lcode).Sum(x => x.year1Amount) / 1000;
                        data.Add(val);
                        dictTotals[lcode] = dictTotals[lcode] + val;
                    }
                }

                agggregateOfLimitCodesWithFlag1OnAllRows = agggregateOfLimitCodesWithFlag1OnAllRows + totalSumCodes;
                data.Add(totalSumCodes);
                dictTotals["agggregateOfLimitCodesWithFlag1"] = dictTotals["agggregateOfLimitCodesWithFlag1"] + totalSumCodes;

                foreach (string sCode in sumCodesWithCabFlag0)
                {
                    decimal val = mainDatasetFirstLevel.Where(x => x.sumCode == sCode).Sum(x => x.year1Amount) / 1000;
                    totalSumCodes = totalSumCodes + val;
                    foreach (var lcode in lstLimitCodes.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                    {
                        val = mainDatasetFirstLevel.Where(x => x.limitCode == lcode).Sum(x => x.year1Amount) / 1000;
                        data.Add(val);
                        dictTotals[lcode] = dictTotals[lcode] + val;
                    }
                }
                // Below are DB Columns
                data.Add(0);
                data.Add(0);
                if (displayTempColumns == true)
                {
                    data.Add(0);
                    data.Add(0);
                }
                else
                {
                    data.Add(0);
                    data.Add(0);
                    data.Add(0);
                    data.Add(0);
                }
            }
            else if (yearSelected == "Year2")
            {
                foreach (string sCode in sumCodesWithCabFlag1)
                {
                    decimal val = mainDatasetFirstLevel.Where(x => x.sumCode == sCode).Sum(x => x.year2Amount) / 1000;
                    totalSumCodes = totalSumCodes + val;
                    foreach (var lcode in lstLimitCodes.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                    {
                        val = mainDatasetFirstLevel.Where(x => x.limitCode == lcode).Sum(x => x.year2Amount) / 1000;
                        data.Add(val);
                        dictTotals[lcode] = dictTotals[lcode] + val;
                    }
                }
                agggregateOfLimitCodesWithFlag1OnAllRows = agggregateOfLimitCodesWithFlag1OnAllRows + totalSumCodes;
                data.Add(totalSumCodes);
                dictTotals["agggregateOfLimitCodesWithFlag1"] = dictTotals["agggregateOfLimitCodesWithFlag1"] + totalSumCodes;

                foreach (string sCode in sumCodesWithCabFlag0)
                {
                    decimal val = mainDatasetFirstLevel.Where(x => x.sumCode == sCode).Sum(x => x.year2Amount) / 1000;
                    totalSumCodes = totalSumCodes + val;
                    foreach (var lcode in lstLimitCodes.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                    {
                        val = mainDatasetFirstLevel.Where(x => x.limitCode == lcode).Sum(x => x.year2Amount) / 1000;
                        data.Add(val);
                        dictTotals[lcode] = dictTotals[lcode] + val;
                    }
                }
                // Below are DB Columns
                data.Add(0);
                data.Add(0);
                if (displayTempColumns == true)
                {
                    data.Add(0);
                    data.Add(0);
                }
                else
                {
                    data.Add(0);
                    data.Add(0);
                    data.Add(0);
                    data.Add(0);
                }
            }
            else if (yearSelected == "Year3")
            {
                foreach (string sCode in sumCodesWithCabFlag1)
                {
                    decimal val = mainDatasetFirstLevel.Where(x => x.sumCode == sCode).Sum(x => x.year3Amount) / 1000;
                    totalSumCodes = totalSumCodes + val;
                    foreach (var lcode in lstLimitCodes.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                    {
                        val = mainDatasetFirstLevel.Where(x => x.limitCode == lcode).Sum(x => x.year3Amount) / 1000;
                        data.Add(val);
                        dictTotals[lcode] = dictTotals[lcode] + val;
                    }
                }
                agggregateOfLimitCodesWithFlag1OnAllRows = agggregateOfLimitCodesWithFlag1OnAllRows + totalSumCodes;
                data.Add(totalSumCodes);
                dictTotals["agggregateOfLimitCodesWithFlag1"] = dictTotals["agggregateOfLimitCodesWithFlag1"] + totalSumCodes;

                foreach (string sCode in sumCodesWithCabFlag0)
                {
                    decimal val = mainDatasetFirstLevel.Where(x => x.sumCode == sCode).Sum(x => x.year3Amount) / 1000;
                    totalSumCodes = totalSumCodes + val;
                    foreach (var lcode in lstLimitCodes.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                    {
                        val = mainDatasetFirstLevel.Where(x => x.limitCode == lcode).Sum(x => x.year3Amount) / 1000;
                        data.Add(val);
                        dictTotals[lcode] = dictTotals[lcode] + val;
                    }
                }
                // Below are DB Columns
                data.Add(0);
                data.Add(0);
                if (displayTempColumns == true)
                {
                    data.Add(0);
                    data.Add(0);
                }
                else
                {
                    data.Add(0);
                    data.Add(0);
                    data.Add(0);
                    data.Add(0);
                }
            }
            else if (yearSelected == "Year4")
            {
                foreach (string sCode in sumCodesWithCabFlag1)
                {
                    decimal val = mainDatasetFirstLevel.Where(x => x.sumCode == sCode).Sum(x => x.year4Amount) / 1000;
                    totalSumCodes = totalSumCodes + val;
                    foreach (var lcode in lstLimitCodes.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                    {
                        val = mainDatasetFirstLevel.Where(x => x.limitCode == lcode).Sum(x => x.year4Amount) / 1000;
                        data.Add(val);
                        dictTotals[lcode] = dictTotals[lcode] + val;
                    }
                }
                agggregateOfLimitCodesWithFlag1OnAllRows = agggregateOfLimitCodesWithFlag1OnAllRows + totalSumCodes;
                data.Add(totalSumCodes);
                dictTotals["agggregateOfLimitCodesWithFlag1"] = dictTotals["agggregateOfLimitCodesWithFlag1"] + totalSumCodes;

                foreach (string sCode in sumCodesWithCabFlag0)
                {
                    decimal val = mainDatasetFirstLevel.Where(x => x.sumCode == sCode).Sum(x => x.year4Amount) / 1000;
                    totalSumCodes = totalSumCodes + val;
                    foreach (var lcode in lstLimitCodes.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                    {
                        val = mainDatasetFirstLevel.Where(x => x.limitCode == lcode).Sum(x => x.year4Amount) / 1000;
                        data.Add(val);
                        dictTotals[lcode] = dictTotals[lcode] + val;
                    }
                }
                // Below are DB Columns
                data.Add(0);
                data.Add(0);
                if (displayTempColumns == true)
                {
                    data.Add(0);
                    data.Add(0);
                }
                else
                {
                    data.Add(0);
                    data.Add(0);
                    data.Add(0);
                    data.Add(0);
                }
            }

            ServiceArea.Add("Data", data);
            finalData.Add(ServiceArea);

            Dictionary<string, string> serviceIdsForCurrentOrgId = new Dictionary<string, string>();
            if (paramFunctionView != null)
            {
                mainDataset.Where(x => x.orgId == orgId).ToList().ForEach(y =>
                {
                    if (!serviceIdsForCurrentOrgId.Keys.Contains(y.serviceId))
                    {
                        serviceIdsForCurrentOrgId.Add(y.serviceId, y.serviceName);
                    }
                });
            }

            foreach (string serviceId in serviceIdsForCurrentOrgId.Keys.Distinct().Where(a => !string.IsNullOrEmpty(a)).OrderBy(x => x).ToList())
            {
                totalSumCodes = 0;
                childId++;

                ServiceArea = new JObject();
                data = new JArray();
                ServiceArea.Add("id", childId.ToString());
                ServiceArea.Add("parentId", parentId);
                ServiceArea.Add("ServiceAreaId", serviceId);
                ServiceArea.Add("ServiceArea", serviceIdsForCurrentOrgId[serviceId]);

                List<string> lstServiceIdFunctions = new List<string>();

                lstServiceIdFunctions.Add(serviceId);

                List<clsServiceAreaData> mainDatasetSecondLevel = new List<clsServiceAreaData>();

                mainDatasetSecondLevel = mainDatasetFirstLevel.Where(x => lstServiceIdFunctions.Contains(x.functionCode)).ToList();

                if (yearSelected == "Year1")
                {
                    foreach (string sCode in sumCodesWithCabFlag1)
                    {
                        decimal val = mainDatasetSecondLevel.Where(x => x.sumCode == sCode).Sum(x => x.year1Amount) / 1000;
                        totalSumCodes = totalSumCodes + val;
                        foreach (var lcode in lstLimitCodes.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                        {
                            val = mainDatasetSecondLevel.Where(x => x.limitCode == lcode).Sum(x => x.year1Amount) / 1000;
                            data.Add(val);
                        }
                    }
                    data.Add(totalSumCodes);

                    foreach (string sCode in sumCodesWithCabFlag0)
                    {
                        decimal val = mainDatasetSecondLevel.Where(x => x.sumCode == sCode).Sum(x => x.year1Amount) / 1000;
                        totalSumCodes = totalSumCodes + val;
                        foreach (var lcode in lstLimitCodes.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                        {
                            val = mainDatasetSecondLevel.Where(x => x.limitCode == lcode).Sum(x => x.year1Amount) / 1000;
                            data.Add(val);
                        }
                    }
                    // Below are DB Columns
                    data.Add(0);
                    data.Add(0);
                    if (displayTempColumns == true)
                    {
                        data.Add(0);
                        data.Add(0);
                    }
                    else
                    {
                        data.Add(0);
                        data.Add(0);
                        data.Add(0);
                        data.Add(0);
                    }
                }
                else if (yearSelected == "Year2")
                {
                    foreach (string sCode in sumCodesWithCabFlag1)
                    {
                        decimal val = mainDatasetSecondLevel.Where(x => x.sumCode == sCode).Sum(x => x.year2Amount) / 1000;
                        totalSumCodes = totalSumCodes + val;
                        foreach (var lcode in lstLimitCodes.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                        {
                            val = mainDatasetSecondLevel.Where(x => x.limitCode == lcode).Sum(x => x.year2Amount) / 1000;
                            data.Add(val);
                        }
                    }
                    data.Add(totalSumCodes);

                    foreach (string sCode in sumCodesWithCabFlag0)
                    {
                        decimal val = mainDatasetSecondLevel.Where(x => x.sumCode == sCode).Sum(x => x.year2Amount) / 1000;
                        totalSumCodes = totalSumCodes + val;
                        foreach (var lcode in lstLimitCodes.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                        {
                            val = mainDatasetSecondLevel.Where(x => x.limitCode == lcode).Sum(x => x.year2Amount) / 1000;
                            data.Add(val);
                        }
                    }
                    // Below are DB Columns
                    data.Add(0);
                    data.Add(0);
                    if (displayTempColumns == true)
                    {
                        data.Add(0);
                        data.Add(0);
                    }
                    else
                    {
                        data.Add(0);
                        data.Add(0);
                        data.Add(0);
                        data.Add(0);
                    }
                }
                else if (yearSelected == "Year3")
                {
                    foreach (string sCode in sumCodesWithCabFlag1)
                    {
                        decimal val = mainDatasetSecondLevel.Where(x => x.sumCode == sCode).Sum(x => x.year3Amount) / 1000;
                        totalSumCodes = totalSumCodes + val;
                        foreach (var lcode in lstLimitCodes.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                        {
                            val = mainDatasetSecondLevel.Where(x => x.limitCode == lcode).Sum(x => x.year3Amount) / 1000;
                            data.Add(val);
                        }
                    }
                    data.Add(totalSumCodes);

                    foreach (string sCode in sumCodesWithCabFlag0)
                    {
                        decimal val = mainDatasetSecondLevel.Where(x => x.sumCode == sCode).Sum(x => x.year3Amount) / 1000;
                        totalSumCodes = totalSumCodes + val;
                        foreach (var lcode in lstLimitCodes.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                        {
                            val = mainDatasetSecondLevel.Where(x => x.limitCode == lcode).Sum(x => x.year3Amount) / 1000;
                            data.Add(val);
                        }
                    }
                    // Below are DB Columns
                    data.Add(0);
                    data.Add(0);
                    if (displayTempColumns == true)
                    {
                        data.Add(0);
                        data.Add(0);
                    }
                    else
                    {
                        data.Add(0);
                        data.Add(0);
                        data.Add(0);
                        data.Add(0);
                    }
                }
                else if (yearSelected == "Year4")
                {
                    foreach (string sCode in sumCodesWithCabFlag1)
                    {
                        decimal val = mainDatasetSecondLevel.Where(x => x.sumCode == sCode).Sum(x => x.year4Amount) / 1000;
                        totalSumCodes = totalSumCodes + val;
                        foreach (var lcode in lstLimitCodes.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                        {
                            val = mainDatasetSecondLevel.Where(x => x.limitCode == lcode).Sum(x => x.year4Amount) / 1000;
                            data.Add(val);
                        }
                    }
                    data.Add(totalSumCodes);

                    foreach (string sCode in sumCodesWithCabFlag0)
                    {
                        decimal val = mainDatasetSecondLevel.Where(x => x.sumCode == sCode).Sum(x => x.year4Amount) / 1000;
                        totalSumCodes = totalSumCodes + val;
                        foreach (var lcode in lstLimitCodes.Where(x => x.sumCode == sCode).Select(y => y.limitCode).Distinct().OrderBy(z => z).ToList())
                        {
                            val = mainDatasetSecondLevel.Where(x => x.limitCode == lcode).Sum(x => x.year4Amount) / 1000;
                            data.Add(val);
                        }
                    }
                    // Below are DB Columns
                    data.Add(0);
                    data.Add(0);
                    if (displayTempColumns == true)
                    {
                        data.Add(0);
                        data.Add(0);
                    }
                    else
                    {
                        data.Add(0);
                        data.Add(0);
                        data.Add(0);
                        data.Add(0);
                    }
                }
                ServiceArea.Add("Data", data);
                finalData.Add(ServiceArea);
            }
        }

        //Tab String
        dynamic TabStrings = new JArray() { ((langStringValuesBAtype["BM_meeting_tab"]).LangText + " " + budYear), ((langStringValuesBAtype["BM_meeting_tab"]).LangText + " " + (budYear + 1)), ((langStringValuesBAtype["BM_meeting_tab"]).LangText + " " + (budYear + 2)), ((langStringValuesBAtype["BM_meeting_tab"]).LangText + " " + (budYear + 3)) };
        //Add total to Json
        dynamic TotalObject = new JObject();
        dynamic TotalData = new JArray() { dictTotals.Values.ToList() };
        TotalData.Add(0);
        TotalData.Add(0);
        if (displayTempColumns == true)
        {
            TotalData.Add(0);
            TotalData.Add(0);
        }
        else
        {
            TotalData.Add(0);
            TotalData.Add(0);
            TotalData.Add(0);
            TotalData.Add(0);
        }

        TotalObject.Add("id", null);
        TotalObject.Add("parentId", null);
        TotalObject.Add("ServiceAreaId", null);
        TotalObject.Add("ServiceArea", (langStringValuesBAtype["cmn_title_total"]).LangText);
        TotalObject.Add("Data", TotalData);
        finalData.Add(TotalObject);
        JObject jsonCfgRow = JObject.Parse(await _utility.GetApplicationSettingAsync("GetBudgetAndFinancialData_grid_config"));
        dynamic backgroudIndex = new JArray();

        if (limitCount > 0)
        {
            backgroudIndex.Add(1);
            backgroudIndex.Add(dictFields["agggregateOfLimitCodesWithFlag1"] - 4);
            backgroudIndex.Add(dictFields["summary"] - 4);
        }
        else
        {
            backgroudIndex.Add(dictFields["agggregateOfLimitCodesWithFlag1"] - 4);
            backgroudIndex.Add(dictFields["summary"] - 4);
        }

        ServiceAreaBudgetData.gridConfig = jsonCfgRow;
        ServiceAreaBudgetData.amountFormat = format;
        ServiceAreaBudgetData.Add("backgroudIndex", backgroudIndex);
        ServiceAreaBudgetData.Add("tabCaption", TabStrings);
        ServiceAreaBudgetData.Add("jsonData", finalData);

        CultureInfo ci = CultureInfo.InvariantCulture;
        ServiceAreaBudgetData.modifiedAt = (string)DateTime.UtcNow.ToString(ci);

        string defaultKey = await _utility.GetParameterValueAsync(userId, "IS_ONGOINGACTION_AVAILABLE");
        bool isOngoingAActionsAvailable = false;
        if (!string.IsNullOrEmpty(defaultKey.Trim()))
        {
            isOngoingAActionsAvailable = System.Convert.ToBoolean(defaultKey.Trim());
        }

        ServiceAreaBudgetData.showOngoingActions = isOngoingAActionsAvailable;
        ServiceAreaBudgetData.agggregateOfLimitCodesWithFlag1OnAllRows = agggregateOfLimitCodesWithFlag1OnAllRows;

        if (splitData == true)
        {
            parentId = 0;
            var lst = await (from u in tenantDbContext.vwUserDetails
                where u.pk_id == -1
                select new clsServiceAreaData
                {
                    orgId = string.Empty,
                    serviceId = string.Empty,
                    year1Amount = 0,
                    year2Amount = 0,
                    year3Amount = 0,
                    year4Amount = 0
                }).ToListAsync();

            foreach (var item in ServiceAreaBudgetData["jsonData"])
            {
                if ((string)item["ServiceAreaId"] != null)
                {
                    for (int i = 4; i < ((JArray)ServiceAreaBudgetData["Fields"]).Count(); i++)
                    {
                        if ((string)ServiceAreaBudgetData["Fields"][i] == "tempNewPriorities")
                        {
                            int indexOfagggregateOfLimitCodesWithFlag1 = ((JArray)ServiceAreaBudgetData["Fields"]).ToObject<List<string>>().IndexOf("agggregateOfLimitCodesWithFlag1");

                            if ((string)item["parentId"] == null && secondLevel == string.Empty)
                            {
                                if (yearSelected == "Year1")
                                {
                                    lst.Add(new clsServiceAreaData()
                                    {
                                        orgId = (string)item["ServiceAreaId"],
                                        serviceId = string.Empty,
                                        year1Amount = Math.Round((Convert.ToDecimal(activeChangeData.year_1_amount / 1000) * Convert.ToDecimal(Convert.ToDecimal(item["Data"][indexOfagggregateOfLimitCodesWithFlag1 - 4]) / Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]))))
                                    });
                                }
                                else if (yearSelected == "Year2")
                                {
                                    lst.Add(new clsServiceAreaData()
                                    {
                                        orgId = (string)item["ServiceAreaId"],
                                        serviceId = string.Empty,
                                        year2Amount = Math.Round((Convert.ToDecimal(activeChangeData.year_2_amount / 1000) * (Convert.ToDecimal(item["Data"][indexOfagggregateOfLimitCodesWithFlag1 - 4]) / Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]))))
                                    });
                                }
                                else if (yearSelected == "Year3")
                                {
                                    lst.Add(new clsServiceAreaData()
                                    {
                                        orgId = (string)item["ServiceAreaId"],
                                        serviceId = string.Empty,
                                        year3Amount = Math.Round((Convert.ToDecimal(activeChangeData.year_3_amount / 1000) * (Convert.ToDecimal(item["Data"][indexOfagggregateOfLimitCodesWithFlag1 - 4]) / Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]))))
                                    });
                                }
                                else if (yearSelected == "Year4")
                                {
                                    lst.Add(new clsServiceAreaData()
                                    {
                                        orgId = (string)item["ServiceAreaId"],
                                        serviceId = string.Empty,
                                        year4Amount = Math.Round((Convert.ToDecimal(activeChangeData.year_4_amount / 1000) * (Convert.ToDecimal(item["Data"][indexOfagggregateOfLimitCodesWithFlag1 - 4]) / Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"]))))
                                    });
                                }
                            }
                            else
                            {
                                if ((string)item["parentId"] == null)
                                {
                                    continue;
                                }

                                parentId = Convert.ToInt32(item["parentId"]);
                                JArray arr = (JArray)ServiceAreaBudgetData["jsonData"];
                                string level1 = (string)arr.FirstOrDefault(x => (int)x["id"] == parentId && (string)x["parentId"] == null)["ServiceAreaId"];

                                if (yearSelected == "Year1")
                                {
                                    lst.Add(new clsServiceAreaData()
                                    {
                                        orgId = level1,
                                        serviceId = (string)item["ServiceAreaId"],
                                        year1Amount = Math.Round(Convert.ToDecimal(activeChangeData.year_1_amount / 1000) * (Convert.ToDecimal(item["Data"][indexOfagggregateOfLimitCodesWithFlag1 - 4]) / Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"])))
                                    });
                                }
                                else if (yearSelected == "Year2")
                                {
                                    lst.Add(new clsServiceAreaData()
                                    {
                                        orgId = level1,
                                        serviceId = (string)item["ServiceAreaId"],
                                        year2Amount = Math.Round(Convert.ToDecimal(activeChangeData.year_2_amount / 1000) * (Convert.ToDecimal(item["Data"][indexOfagggregateOfLimitCodesWithFlag1 - 4]) / Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"])))
                                    });
                                }
                                else if (yearSelected == "Year3")
                                {
                                    lst.Add(new clsServiceAreaData()
                                    {
                                        orgId = level1,
                                        serviceId = (string)item["ServiceAreaId"],
                                        year3Amount = Math.Round(Convert.ToDecimal(activeChangeData.year_3_amount / 1000) * (Convert.ToDecimal(item["Data"][indexOfagggregateOfLimitCodesWithFlag1 - 4]) / Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"])))
                                    });
                                }
                                else if (yearSelected == "Year4")
                                {
                                    lst.Add(new clsServiceAreaData()
                                    {
                                        orgId = level1,
                                        serviceId = (string)item["ServiceAreaId"],
                                        year4Amount = Math.Round(Convert.ToDecimal(activeChangeData.year_4_amount / 1000) * (Convert.ToDecimal(item["Data"][indexOfagggregateOfLimitCodesWithFlag1 - 4]) / Convert.ToDecimal(ServiceAreaBudgetData["agggregateOfLimitCodesWithFlag1OnAllRows"])))
                                    });
                                }
                            }
                        }
                    }
                }
            }

            List<tfp_temp_budget_limits> lstTfpTempBudgetLimits = await tenantDbContext.tfp_temp_budget_limits.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budYear && x.action_type == 40).ToListAsync();

            List<udtTfpTempBudgetLimits> limitsData = new List<udtTfpTempBudgetLimits>();
            int count = 0;
            decimal totalYear1 = 0;
            decimal totalYear2 = 0;
            decimal totalYear3 = 0;
            decimal totalYear4 = 0;
            foreach (var l in lst)
            {
                udtTfpTempBudgetLimits limit = new udtTfpTempBudgetLimits();
                limit.fk_tenant_id = userDetails.tenant_id;
                limit.budget_year = budYear;
                limit.fp_level_1_value = l.orgId;
                limit.fp_level_2_value = l.serviceId;
                limit.action_type = 40;

                count++;

                if (count == lst.Count())
                {
                    limit.year_1_limit = activeChangeData.year_1_amount - totalYear1;

                    limit.year_2_limit = activeChangeData.year_2_amount - totalYear2;

                    limit.year_3_limit = activeChangeData.year_3_amount - totalYear3;

                    limit.year_4_limit = activeChangeData.year_4_amount - totalYear4;
                }
                else
                {
                    limit.year_1_limit = (yearSelected == "Year2" || yearSelected == "Year3" || yearSelected == "Year4") ?
                        lstTfpTempBudgetLimits.FirstOrDefault(x => x.fp_level_1_value == l.orgId && x.fp_level_2_value == l.serviceId) == null ? 0 :
                            lstTfpTempBudgetLimits.FirstOrDefault(x => x.fp_level_1_value == l.orgId && x.fp_level_2_value == l.serviceId).year_1_limit : l.year1Amount * 1000;
                    totalYear1 += limit.year_1_limit;

                    limit.year_2_limit = (yearSelected == "Year3" || yearSelected == "Year4" || yearSelected == "Year1") ?
                        lstTfpTempBudgetLimits.FirstOrDefault(x => x.fp_level_1_value == l.orgId && x.fp_level_2_value == l.serviceId) == null ? 0 :
                            lstTfpTempBudgetLimits.FirstOrDefault(x => x.fp_level_1_value == l.orgId && x.fp_level_2_value == l.serviceId).year_2_limit : l.year2Amount * 1000;
                    totalYear2 += limit.year_2_limit;

                    limit.year_3_limit = (yearSelected == "Year4" || yearSelected == "Year1" || yearSelected == "Year2") ?
                        lstTfpTempBudgetLimits.FirstOrDefault(x => x.fp_level_1_value == l.orgId && x.fp_level_2_value == l.serviceId) == null ? 0 :
                            lstTfpTempBudgetLimits.FirstOrDefault(x => x.fp_level_1_value == l.orgId && x.fp_level_2_value == l.serviceId).year_3_limit : l.year3Amount * 1000;
                    totalYear3 += limit.year_3_limit;

                    limit.year_4_limit = (yearSelected == "Year1" || yearSelected == "Year2" || yearSelected == "Year3") ?
                        lstTfpTempBudgetLimits.FirstOrDefault(x => x.fp_level_1_value == l.orgId && x.fp_level_2_value == l.serviceId) == null ? 0 :
                            lstTfpTempBudgetLimits.FirstOrDefault(x => x.fp_level_1_value == l.orgId && x.fp_level_2_value == l.serviceId).year_4_limit : l.year4Amount * 1000;
                    totalYear4 += limit.year_4_limit;
                }

                limit.updated = DateTime.UtcNow;
                limit.updated_by = userDetails.pk_id;
                limitsData.Add(limit);
            }

            prcInsertIntoTfpTempBudgetLimits spInsert = new prcInsertIntoTfpTempBudgetLimits();
            spInsert.udtTfpTempBudgetLimits = new List<udtTfpTempBudgetLimits>();
            spInsert.udtTfpTempBudgetLimits.AddRange(limitsData);
            spInsert.fk_tenant_id = userDetails.tenant_id;
            spInsert.budget_year = budYear;
            await tenantDbContext.Database.ExecuteStoredProcedureAsync(spInsert);

            ServiceAreaBudgetData.splitData = true;
            return ServiceAreaBudgetData;
        }

        ServiceAreaBudgetData.splitData = false;
        return ServiceAreaBudgetData;
    }

}