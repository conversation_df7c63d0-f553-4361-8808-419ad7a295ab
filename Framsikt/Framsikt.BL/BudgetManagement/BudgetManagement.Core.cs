#pragma warning disable CS8629
#pragma warning disable CS8600
#pragma warning disable CS8601
#pragma warning disable CS8602
#pragma warning disable CS8604
#pragma warning disable CS8625

using Azure.Data.Tables;
using Framsikt.BL.Core.Helpers;
using Framsikt.BL.Helpers;
using Framsikt.BL.Helpers.CkEditorHelpers;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace Framsikt.BL;

public partial class BudgetManagement : IBudgetManagement
{
    private readonly IUtility _utility;
    private readonly IPopulationStatisticsData _popStatistics;
    private readonly IConsequenceAdjustedBudget _consequentAdjustedBudget;
    private readonly INotification _notification;
    private readonly IBackendRequest _backendJob;
    private readonly IAppDataCache _cache;
    private readonly IKostraData _kostraData;
    private List<gmd_publish_tree_node_definitions> _treeNodeDefs;
    private Dictionary<string, clsLanguageString> _langStringValuesBudgetManagement;
    private List<string> _dataUserRolesAndActivities;
    private List<clsOrgLevelOneAndTwoDetails> lstLevel1AndTwoDetailsnonFilteredDeleted;
    private readonly IAzureBlobHelper _blobHelper;
    private readonly IPublishTemplateManager _publishTemplateManager;
    private readonly ICustomNodeManager _customNodeManager;
    private List<clsOrgStructure> lstOrgStructure;
    private readonly TemplateUtility _templateUtilty;
    private readonly IDocTableConfig _docTableConfig;
    private readonly IBMFocusAreaTemplate _bmFocusArea;
    private readonly IBusinessPlanTemplate _busPlanTemplate;
    private readonly IFinUtility _finUtility;
    private readonly ICKEditorExtensions _editorExtensions;
    private List<bool> nodeSelectionStatus = null;
    private readonly IDataSyncUtility _dataSyncUtility;
    private readonly IUnitOfWork _unitOfWork;
    private readonly LockManager _lockManager;
    private readonly TableServiceClient _tableServicePublishProdClient;
    private readonly TableServiceClient _tableServicePublishStageClient;

#pragma warning disable CS8618

    public BudgetManagement(IUtility util, IPopulationStatisticsData popStatistics, IConsequenceAdjustedBudget consequentAdjustedBudget,
        IBackendRequest backendJob, INotification notification, IAppDataCache cache, IKostraData kostraData, IAzureBlobHelper blobHelper,
        IPublishTemplateManager publishTemplateManager, ICustomNodeManager customNodeManager, IBMFocusAreaTemplate bmFocusArea,
        IBusinessPlanTemplate busPlanTemplate, IFinUtility finUtility,
        ICKEditorExtensions editorExtensions, IDataSyncUtility dataSyncUtility, IUnitOfWork uow, LockManager lockManager, IAzureStorageAccountClient azureStorageAccountClient)
#pragma warning restore CS8618
    {
        _utility = util;
        _popStatistics = popStatistics;
        _consequentAdjustedBudget = consequentAdjustedBudget;
        _backendJob = backendJob;
        _notification = notification;
        _cache = cache;
        _kostraData = kostraData;
        _blobHelper = blobHelper;
        _publishTemplateManager = publishTemplateManager;
        _customNodeManager = customNodeManager;
        _templateUtilty = new TemplateUtility();
        _docTableConfig = new DocTableConfig(_utility);
        _bmFocusArea = bmFocusArea;
        _busPlanTemplate = busPlanTemplate;
        _finUtility = finUtility;
        _editorExtensions = editorExtensions ?? throw new ArgumentNullException(nameof(editorExtensions));
        _dataSyncUtility = dataSyncUtility;
        _unitOfWork = uow;
        _lockManager = lockManager ?? throw new ArgumentNullException(nameof(lockManager));
        _tableServicePublishProdClient = azureStorageAccountClient.CreateTableServicePublishProdClient();
        _tableServicePublishStageClient = azureStorageAccountClient.CreateTableServicePublishStageClient();
    }

}