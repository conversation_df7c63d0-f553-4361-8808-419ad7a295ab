using Framsikt.BL.Helpers;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Framsikt.BL;

public partial class BudgetManagement
{


    public dynamic GetConsequenceAdjustedBudgetOverview(string userId, int budgetYear, string budgetPhaseId, bool divideByMillions = false)
    {
        return GetConsequenceAdjustedBudgetOverviewAsync(userId, budgetYear, budgetPhaseId, divideByMillions).GetAwaiter().GetResult();
    }



    public async Task<dynamic> GetConsequenceAdjustedBudgetOverviewAsync
        (string userId, int budgetYear, string budgetPhaseId, bool divideByMillions = false)
    {
        SimulatorHelper simulatorData = new SimulatorHelper { Type = "All" };
        simulatorData.BudgetYear = budgetYear;
        return await GetConsequenceAdjustedBudgetOverviewAsync(userId, simulatorData, budgetPhaseId, divideByMillions);
    }



    public async Task<dynamic> GetConsequenceAdjustedBudgetOverviewAsync(string userId, SimulatorHelper simulatorData, string budgetPhaseId, bool divideByMillions = false)
    {
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
        dbContext.Database.SetCommandTimeout(500);
        Dictionary<string, clsLanguageString> langStringValuesBudgetManagement = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetManagement");
        int budgetYear = simulatorData.BudgetYear;

        List<ConsequenceOverViewHelper> CurrentBudgetdata;
        List<ConsequenceOverViewHelper> QuerydataWithNegative;
        List<ConsequenceOverViewHelper> Querydata = new List<ConsequenceOverViewHelper>();

        List<string> conditionFunctionCodes = new List<string>();
        List<int> lineGroup = new List<int>() { 10, 20, 30, 40, 50 };

        bool currentbdttable = await BudgetFetchCheckAsync(userId);

        var transData = (from a in dbContext.tfp_trans_header
            join b in dbContext.tfp_trans_detail on new { a = a.fk_tenant_id, b = a.pk_action_id }
                equals new { a = b.fk_tenant_id, b = b.fk_action_id }
            where a.fk_tenant_id == userDetails.tenant_id && b.budget_year == budgetYear
            select b);
        var transDataCopy = transData;
        if (simulatorData.FinPlanActions.Any())
        {
            transData = transData.Where(x => !simulatorData.FinPlanActions.Contains(x.fk_action_id));
        }
        if (!string.IsNullOrEmpty(budgetPhaseId) && budgetPhaseId != "-1")
        {
            List<int> changeIds = await _utility.GetChangeDataUsingBudgetPhaseAsync(budgetPhaseId, userDetails.tenant_id, budgetYear);
            if (changeIds.Any())
            {
                transData = transData.Where(x => changeIds.Contains(x.fk_change_id));
            }
        }

        if (simulatorData.Type.ToLower() == "All".ToLower())
        {
            if (currentbdttable)
            {
                CurrentBudgetdata = await (from a in dbContext.tbu_trans_detail
                    join b in dbContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code }
                        equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                    join c in dbContext.gmd_reporting_line on b.fk_kostra_account_code equals c.fk_kostra_account_code
                    where (a.fk_tenant_id == userDetails.tenant_id
                           && a.budget_year == (budgetYear - 1)
                           && lineGroup.Contains(c.line_group_id) && c.report == "54_OVDRIFT")
                          && b.dateFrom.Year <= budgetYear && budgetYear <= b.dateTo.Year
                    group a by new { c.line_group, c.line_group_id } into g
                    select new ConsequenceOverViewHelper
                    {
                        line_group_id = g.Key.line_group_id,
                        Year1 = g.Sum(x => x.amount_year_1)
                    }).ToListAsync();
            }
            else
            {
                CurrentBudgetdata = await (from a in dbContext.tbu_trans_detail_original
                    join b in dbContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code }
                        equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                    join c in dbContext.gmd_reporting_line on b.fk_kostra_account_code equals c.fk_kostra_account_code
                    where (a.fk_tenant_id == userDetails.tenant_id
                           && a.budget_year == (budgetYear - 1)
                           && lineGroup.Contains(c.line_group_id) && c.report == "54_OVDRIFT")
                          && b.dateFrom.Year <= budgetYear && budgetYear <= b.dateTo.Year
                    group a by new { c.line_group, c.line_group_id } into g
                    select new ConsequenceOverViewHelper
                    {
                        line_group_id = g.Key.line_group_id,
                        Year1 = g.Sum(x => x.amount_year_1)
                    }).ToListAsync();
            }
        }
        else
        {
            conditionFunctionCodes = await (from t in dbContext.tco_functions
                    join b in dbContext.gmd_kostra_function on new { a = t.fk_kostra_function_code } equals new { a = b.pk_kostra_function_code }
                    where t.pk_tenant_id == userDetails.tenant_id && b.type_flag.ToLower() == simulatorData.Type.ToLower()
                    select t.pk_Function_code
                ).ToListAsync();
            if (currentbdttable)
            {
                CurrentBudgetdata = await (from a in dbContext.tbu_trans_detail
                    join b in dbContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code }
                        equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                    join c in dbContext.gmd_reporting_line on b.fk_kostra_account_code equals c.fk_kostra_account_code
                    where (a.fk_tenant_id == userDetails.tenant_id
                           && a.budget_year == (budgetYear - 1)
                           && lineGroup.Contains(c.line_group_id) && c.report == "54_OVDRIFT" && conditionFunctionCodes.Contains(a.fk_function_code))
                          && b.dateFrom.Year <= budgetYear && budgetYear <= b.dateTo.Year
                    group a by new { c.line_group, c.line_group_id } into g
                    select new ConsequenceOverViewHelper
                    {
                        line_group_id = g.Key.line_group_id,
                        Year1 = g.Sum(x => x.amount_year_1)
                    }).ToListAsync();
            }
            else
            {
                CurrentBudgetdata = await (from a in dbContext.tbu_trans_detail_original
                    join b in dbContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code }
                        equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                    join c in dbContext.gmd_reporting_line on b.fk_kostra_account_code equals c.fk_kostra_account_code
                    where (a.fk_tenant_id == userDetails.tenant_id
                           && a.budget_year == (budgetYear - 1)
                           && lineGroup.Contains(c.line_group_id) && c.report == "54_OVDRIFT" && conditionFunctionCodes.Contains(a.fk_function_code))
                          && b.dateFrom.Year <= budgetYear && budgetYear <= b.dateTo.Year
                    group a by new { c.line_group, c.line_group_id } into g
                    select new ConsequenceOverViewHelper
                    {
                        line_group_id = g.Key.line_group_id,
                        Year1 = g.Sum(x => x.amount_year_1)
                    }).ToListAsync();
            }
        }
        conditionFunctionCodes = new List<string>();
        if (simulatorData.Type.ToLower() == "All".ToLower())
        {
            Querydata = await (from a in transData
                join bc in dbContext.tfp_budget_changes on new { a = a.fk_tenant_id, b = a.fk_change_id }
                    equals new { a = bc.fk_tenant_id, b = bc.pk_change_id }
                join d in dbContext.tfp_trans_header on new { a = a.fk_tenant_id, b = a.fk_action_id }
                    equals new { a = d.fk_tenant_id, b = d.pk_action_id }
                join b in dbContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code }
                    equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                join c in dbContext.gmd_reporting_line on b.fk_kostra_account_code equals c.fk_kostra_account_code
                where (a.fk_tenant_id == userDetails.tenant_id
                       && a.budget_year == budgetYear
                       && lineGroup.Contains(c.line_group_id) && c.report == "54_OVDRIFT")
                      && b.dateFrom.Year <= budgetYear && budgetYear <= b.dateTo.Year
                      && bc.org_budget_flag == 1
                select new ConsequenceOverViewHelper
                {
                    actionId = a.pk_id,
                    FkactionId = a.fk_action_id,
                    line_group_id = c.line_group_id,
                    Year1 = a.year_1_amount,
                    Year2 = a.year_2_amount,
                    Year3 = a.year_3_amount,
                    Year4 = a.year_4_amount,
                    ActionType = d.action_type,
                    Priority = d.priority,
                    tags = d.tags,
                    line_group = c.line_group,
                    FunctionCode = a.function_code,
                    departmentCode = a.department_code
                }).ToListAsync();

            QuerydataWithNegative = await (from a in transData
                join bc in dbContext.tfp_budget_changes on new { a = a.fk_tenant_id, b = a.fk_change_id }
                    equals new { a = bc.fk_tenant_id, b = bc.pk_change_id }
                join d in dbContext.tfp_trans_header on new { a = a.fk_tenant_id, b = a.fk_action_id }
                    equals new { a = d.fk_tenant_id, b = d.pk_action_id }
                join b in dbContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code }
                    equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                join c in dbContext.gmd_reporting_line on b.fk_kostra_account_code equals c.fk_kostra_account_code
                where (a.fk_tenant_id == userDetails.tenant_id
                       && a.budget_year == budgetYear
                       && lineGroup.Contains(c.line_group_id) && c.report == "54_OVDRIFT")
                      && b.dateFrom.Year <= budgetYear && budgetYear <= b.dateTo.Year
                      && bc.org_budget_flag == 1
                select new ConsequenceOverViewHelper
                {
                    actionId = a.pk_id,
                    FkactionId = a.fk_action_id,
                    line_group_id = c.line_group_id,
                    Year1 = a.year_1_amount * -1,
                    Year2 = a.year_2_amount * -1,
                    Year3 = a.year_3_amount * -1,
                    Year4 = a.year_4_amount * -1,
                    ActionType = d.action_type,
                    Priority = d.priority,
                    tags = d.tags,
                    line_group = c.line_group,
                    FunctionCode = a.function_code,
                    departmentCode = a.department_code
                }).ToListAsync();
        }
        else
        {
            conditionFunctionCodes = await (from t in dbContext.tco_functions
                    join b in dbContext.gmd_kostra_function on new { a = t.fk_kostra_function_code } equals new { a = b.pk_kostra_function_code }
                    where t.pk_tenant_id == userDetails.tenant_id && b.type_flag.ToLower() == simulatorData.Type.ToLower()
                    select t.pk_Function_code
                ).ToListAsync();
            Querydata = await (from a in transData
                join d in dbContext.tfp_trans_header on new { a = a.fk_tenant_id, b = a.fk_action_id }
                    equals new { a = d.fk_tenant_id, b = d.pk_action_id }
                join b in dbContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code }
                    equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                join c in dbContext.gmd_reporting_line on b.fk_kostra_account_code equals c.fk_kostra_account_code
                where (a.fk_tenant_id == userDetails.tenant_id
                       && a.budget_year == budgetYear
                       && lineGroup.Contains(c.line_group_id) && c.report == "54_OVDRIFT" && conditionFunctionCodes.Contains(a.function_code))
                      && b.dateFrom.Year <= budgetYear && budgetYear <= b.dateTo.Year

                select new ConsequenceOverViewHelper
                {
                    actionId = a.pk_id,
                    FkactionId = a.fk_action_id,
                    line_group_id = c.line_group_id,
                    Year1 = a.year_1_amount,
                    Year2 = a.year_2_amount,
                    Year3 = a.year_3_amount,
                    Year4 = a.year_4_amount,
                    ActionType = d.action_type,
                    Priority = d.priority,
                    tags = d.tags,
                    line_group = c.line_group,
                    FunctionCode = a.function_code,
                    departmentCode = a.department_code
                }).ToListAsync();

            QuerydataWithNegative = await (from a in transData
                join d in dbContext.tfp_trans_header on new { a = a.fk_tenant_id, b = a.fk_action_id }
                    equals new { a = d.fk_tenant_id, b = d.pk_action_id }
                join b in dbContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code }
                    equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                join c in dbContext.gmd_reporting_line on b.fk_kostra_account_code equals c.fk_kostra_account_code
                where (a.fk_tenant_id == userDetails.tenant_id
                       && a.budget_year == budgetYear
                       && lineGroup.Contains(c.line_group_id) && c.report == "54_OVDRIFT" && conditionFunctionCodes.Contains(a.function_code))
                      && b.dateFrom.Year <= budgetYear && budgetYear <= b.dateTo.Year

                select new ConsequenceOverViewHelper
                {
                    actionId = a.pk_id,
                    FkactionId = a.fk_action_id,
                    line_group_id = c.line_group_id,
                    Year1 = a.year_1_amount * -1,
                    Year2 = a.year_2_amount * -1,
                    Year3 = a.year_3_amount * -1,
                    Year4 = a.year_4_amount * -1,
                    ActionType = d.action_type,
                    Priority = d.priority,
                    tags = d.tags,
                    line_group = c.line_group,
                    FunctionCode = a.function_code,
                    departmentCode = a.department_code
                }).ToListAsync();
        }

        var dataAfterSimulation = await ApplySimulationAsync(userId, simulatorData, Querydata, budgetYear, lineGroup, conditionFunctionCodes, QuerydataWithNegative);

        Querydata = (from a in dataAfterSimulation.dataToFilter
            group a by new { a.line_group_id } into g
            select new ConsequenceOverViewHelper
            {
                line_group_id = g.Key.line_group_id,
                Year1 = g.Sum(x => x.Year1),
                Year2 = g.Sum(x => x.Year2),
                Year3 = g.Sum(x => x.Year3),
                Year4 = g.Sum(x => x.Year4)
            }).ToList();

        dynamic data = new JObject();
        dynamic jsonData = new JArray();
        dynamic jsonDataSimulation = new JArray();

        dynamic row = new JObject();
        row.id = 1;
        row.name = ((langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == "revenues_text_bm")).Value).LangText;
        dynamic gridData = new JArray();
        gridData.Add((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 10) == null ? 0 : FormatValue((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 10).Year1), divideByMillions)));

        gridData.Add((Querydata.FirstOrDefault(x => x.line_group_id == 10) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 10).Year1), divideByMillions)));

        gridData.Add((Querydata.FirstOrDefault(x => x.line_group_id == 10) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 10).Year2), divideByMillions)));

        gridData.Add((Querydata.FirstOrDefault(x => x.line_group_id == 10) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 10).Year3), divideByMillions)));

        gridData.Add((Querydata.FirstOrDefault(x => x.line_group_id == 10) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 10).Year4), divideByMillions)));
        row.Add("griData", gridData);
        jsonData.Add(row);

        row = new JObject();
        row.id = 2;
        row.name = ((langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == "bm_operational_expenses_text")).Value).LangText;
        gridData = new JArray();

        gridData.Add((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 20) == null ? 0 : FormatValue((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 20).Year1), divideByMillions)));

        gridData.Add((Querydata.FirstOrDefault(x => x.line_group_id == 20) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 20).Year1), divideByMillions)));

        gridData.Add((Querydata.FirstOrDefault(x => x.line_group_id == 20) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 20).Year2), divideByMillions)));

        gridData.Add((Querydata.FirstOrDefault(x => x.line_group_id == 20) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 20).Year3), divideByMillions)));

        gridData.Add((Querydata.FirstOrDefault(x => x.line_group_id == 20) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 20).Year4), divideByMillions)));

        row.Add("griData", gridData);
        jsonData.Add(row);

        row = new JObject();
        row.id = 0;
        row.name = ((langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == "bm_gross_profit_text")).Value).LangText;
        gridData = new JArray();
        gridData.Add(
            (CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 10) == null ? 0 : FormatValue((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 10).Year1), divideByMillions)) +
            (CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 20) == null ? 0 : FormatValue((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 20).Year1), divideByMillions)));

        gridData.Add(
            (Querydata.FirstOrDefault(x => x.line_group_id == 10) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 10).Year1), divideByMillions)) +
            (Querydata.FirstOrDefault(x => x.line_group_id == 20) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 20).Year1), divideByMillions)));

        gridData.Add(
            (Querydata.FirstOrDefault(x => x.line_group_id == 10) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 10).Year2), divideByMillions)) +
            (Querydata.FirstOrDefault(x => x.line_group_id == 20) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 20).Year2), divideByMillions)));

        gridData.Add(
            (Querydata.FirstOrDefault(x => x.line_group_id == 10) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 10).Year3), divideByMillions)) +
            (Querydata.FirstOrDefault(x => x.line_group_id == 20) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 20).Year3), divideByMillions)));

        gridData.Add(
            (Querydata.FirstOrDefault(x => x.line_group_id == 10) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 10).Year4), divideByMillions)) +
            (Querydata.FirstOrDefault(x => x.line_group_id == 20) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 20).Year4), divideByMillions)));
        row.Add("griData", gridData);
        jsonData.Add(row);

        row = new JObject();
        row.id = 4;
        row.name = ((langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == "bm_financial_expenses_text")).Value).LangText;
        gridData = new JArray();
        gridData.Add(
            (CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 30) == null ? 0 : FormatValue((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 30).Year1), divideByMillions)));

        gridData.Add(

            (Querydata.FirstOrDefault(x => x.line_group_id == 30) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 30).Year1), divideByMillions)));

        gridData.Add(

            (Querydata.FirstOrDefault(x => x.line_group_id == 30) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 30).Year2), divideByMillions)));

        gridData.Add(

            (Querydata.FirstOrDefault(x => x.line_group_id == 30) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 30).Year3), divideByMillions)));

        gridData.Add(

            (Querydata.FirstOrDefault(x => x.line_group_id == 30) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 30).Year4), divideByMillions)));
        row.Add("griData", gridData);
        jsonData.Add(row);

        row = new JObject();
        row.id = 10;
        row.name = ((langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == "bm_Motpost_avskrivinger_text")).Value).LangText;
        gridData = new JArray();
        gridData.Add(
            (CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 40) == null ? 0 : FormatValue((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 40).Year1), divideByMillions)));

        gridData.Add(
            (Querydata.FirstOrDefault(x => x.line_group_id == 40) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 40).Year1), divideByMillions)));

        gridData.Add(
            (Querydata.FirstOrDefault(x => x.line_group_id == 40) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 40).Year2), divideByMillions)));

        gridData.Add(
            (Querydata.FirstOrDefault(x => x.line_group_id == 40) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 40).Year3), divideByMillions)));

        gridData.Add(
            (Querydata.FirstOrDefault(x => x.line_group_id == 40) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 40).Year4), divideByMillions)));
        row.Add("griData", gridData);
        jsonData.Add(row);

        row = new JObject();
        row.id = 0;
        row.name = ((langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == "bm_net_profit_text")).Value).LangText;
        gridData = new JArray();
        gridData.Add(
            ((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 10) == null ? 0 : FormatValue((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 10).Year1), divideByMillions)) +
             (CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 20) == null ? 0 : FormatValue((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 20).Year1), divideByMillions)) +
             (CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 30) == null ? 0 : FormatValue((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 30).Year1), divideByMillions)) +
             (CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 40) == null ? 0 : FormatValue((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 40).Year1), divideByMillions))));

        gridData.Add(
            ((Querydata.FirstOrDefault(x => x.line_group_id == 10) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 10).Year1), divideByMillions)) +
             (Querydata.FirstOrDefault(x => x.line_group_id == 20) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 20).Year1), divideByMillions)) +
             (Querydata.FirstOrDefault(x => x.line_group_id == 30) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 30).Year1), divideByMillions)) +
             (Querydata.FirstOrDefault(x => x.line_group_id == 40) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 40).Year1), divideByMillions))));

        gridData.Add(((Querydata.FirstOrDefault(x => x.line_group_id == 10) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 10).Year2), divideByMillions)) +
                      (Querydata.FirstOrDefault(x => x.line_group_id == 20) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 20).Year2), divideByMillions)) +
                      (Querydata.FirstOrDefault(x => x.line_group_id == 30) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 30).Year2), divideByMillions)) +
                      (Querydata.FirstOrDefault(x => x.line_group_id == 40) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 40).Year2), divideByMillions))));

        gridData.Add(((Querydata.FirstOrDefault(x => x.line_group_id == 10) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 10).Year3), divideByMillions)) +
                      (Querydata.FirstOrDefault(x => x.line_group_id == 20) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 20).Year3), divideByMillions)) +
                      (Querydata.FirstOrDefault(x => x.line_group_id == 30) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 30).Year3), divideByMillions)) +
                      (Querydata.FirstOrDefault(x => x.line_group_id == 40) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 40).Year3), divideByMillions))));

        gridData.Add(((Querydata.FirstOrDefault(x => x.line_group_id == 10) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 10).Year4), divideByMillions)) +
                      (Querydata.FirstOrDefault(x => x.line_group_id == 20) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 20).Year4), divideByMillions)) +
                      (Querydata.FirstOrDefault(x => x.line_group_id == 30) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 30).Year4), divideByMillions)) +
                      (Querydata.FirstOrDefault(x => x.line_group_id == 40) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 40).Year4), divideByMillions))));
        row.Add("griData", gridData);
        jsonData.Add(row);

        row = new JObject();
        row.id = 6;
        row.name = ((langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == "bm_transfered_to_investments_text")).Value).LangText;
        gridData = new JArray();
        gridData.Add((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 50) == null ? 0 : FormatValue((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 50).Year1), divideByMillions)));
        gridData.Add((Querydata.FirstOrDefault(x => x.line_group_id == 50) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 50).Year1), divideByMillions)));
        gridData.Add((Querydata.FirstOrDefault(x => x.line_group_id == 50) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 50).Year2), divideByMillions)));
        gridData.Add((Querydata.FirstOrDefault(x => x.line_group_id == 50) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 50).Year3), divideByMillions)));
        gridData.Add((Querydata.FirstOrDefault(x => x.line_group_id == 50) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 50).Year4), divideByMillions)));
        row.Add("griData", gridData);
        jsonData.Add(row);

        row = new JObject();
        row.id = 0;
        row.name = ((langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == "bm_yearly_profit_text")).Value).LangText;
        gridData = new JArray();
        gridData.Add(
            (((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 10) == null ? 0 : FormatValue((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 10).Year1), divideByMillions)) +
              (CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 20) == null ? 0 : FormatValue((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 20).Year1), divideByMillions))) +
             ((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 30) == null ? 0 : FormatValue((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 30).Year1), divideByMillions)) +
              (CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 40) == null ? 0 : FormatValue((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 40).Year1), divideByMillions))) +
             (CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 50) == null ? 0 : FormatValue((CurrentBudgetdata.FirstOrDefault(x => x.line_group_id == 50).Year1), divideByMillions))));

        gridData.Add(
            (Querydata.FirstOrDefault(x => x.line_group_id == 10) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 10).Year1), divideByMillions)) +
            (Querydata.FirstOrDefault(x => x.line_group_id == 20) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 20).Year1), divideByMillions)) +
            (Querydata.FirstOrDefault(x => x.line_group_id == 30) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 30).Year1), divideByMillions)) +
            (Querydata.FirstOrDefault(x => x.line_group_id == 40) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 40).Year1), divideByMillions)) +
            (Querydata.FirstOrDefault(x => x.line_group_id == 50) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 50).Year1), divideByMillions)));

        gridData.Add(
            (Querydata.FirstOrDefault(x => x.line_group_id == 10) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 10).Year2), divideByMillions)) +
            (Querydata.FirstOrDefault(x => x.line_group_id == 20) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 20).Year2), divideByMillions)) +
            (Querydata.FirstOrDefault(x => x.line_group_id == 30) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 30).Year2), divideByMillions)) +
            (Querydata.FirstOrDefault(x => x.line_group_id == 40) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 40).Year2), divideByMillions)) +
            (Querydata.FirstOrDefault(x => x.line_group_id == 50) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 50).Year2), divideByMillions)));

        gridData.Add(
            (Querydata.FirstOrDefault(x => x.line_group_id == 10) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 10).Year3), divideByMillions)) +
            (Querydata.FirstOrDefault(x => x.line_group_id == 20) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 20).Year3), divideByMillions)) +
            (Querydata.FirstOrDefault(x => x.line_group_id == 30) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 30).Year3), divideByMillions)) +
            (Querydata.FirstOrDefault(x => x.line_group_id == 40) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 40).Year3), divideByMillions)) +
            (Querydata.FirstOrDefault(x => x.line_group_id == 50) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 50).Year3), divideByMillions)));

        gridData.Add(
            (Querydata.FirstOrDefault(x => x.line_group_id == 10) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 10).Year4), divideByMillions)) +
            (Querydata.FirstOrDefault(x => x.line_group_id == 20) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 20).Year4), divideByMillions)) +
            (Querydata.FirstOrDefault(x => x.line_group_id == 30) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 30).Year4), divideByMillions)) +
            (Querydata.FirstOrDefault(x => x.line_group_id == 40) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 40).Year4), divideByMillions)) +
            (Querydata.FirstOrDefault(x => x.line_group_id == 50) == null ? 0 : FormatValue((Querydata.FirstOrDefault(x => x.line_group_id == 50).Year4), divideByMillions)));

        row.Add("griData", gridData);
        jsonData.Add(row);

        row = new JObject();
        row.id = -1;
        row.name = ((langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == "bm_Net_result_per_text")).Value).LangText;
        gridData = new JArray();
        gridData.Add(GetNetRestultPercentage(CurrentBudgetdata, 1, divideByMillions));

        gridData.Add(GetNetRestultPercentage(Querydata, 1, divideByMillions));

        gridData.Add(GetNetRestultPercentage(Querydata, 2, divideByMillions));

        gridData.Add(GetNetRestultPercentage(Querydata, 3, divideByMillions));

        gridData.Add(GetNetRestultPercentage(Querydata, 4, divideByMillions));
        row.Add("griData", gridData);
        jsonData.Add(row);

        tco_users_settings userSettings = await dbContext.tco_users_settings.FirstOrDefaultAsync(x =>
            x.fk_user_id == userDetails.pk_id && x.tenant_id == userDetails.tenant_id && x.is_active_tenant);
        var dataActiveBudgetChanges = await (from t in dbContext.tfp_budget_changes
            where t.fk_tenant_id == userDetails.tenant_id
                  && t.budget_year == budgetYear
                  && t.status == 1
                  && t.org_budget_flag == 1
            orderby t.change_date descending
            select t).ToListAsync();

        int activeChangeId = dataActiveBudgetChanges.Count() == 0 ? -1 :
            userSettings == null ? dataActiveBudgetChanges.FirstOrDefault().pk_change_id : userSettings.active_change_id == null ? dataActiveBudgetChanges.FirstOrDefault().pk_change_id :
            dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == userSettings.active_change_id) == null ? dataActiveBudgetChanges.FirstOrDefault().pk_change_id :
            dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == userSettings.active_change_id).pk_change_id;

        var tfpBudgetChanges = dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == activeChangeId);

        row = new JObject();
        row.id = 8;
        gridData = new JArray();
        gridData.Add(0);
        gridData.Add((tfpBudgetChanges == null ? 0 : FormatValue((tfpBudgetChanges.year_1_amount), divideByMillions)));
        gridData.Add((tfpBudgetChanges == null ? 0 : FormatValue((tfpBudgetChanges.year_2_amount), divideByMillions)));
        gridData.Add((tfpBudgetChanges == null ? 0 : FormatValue((tfpBudgetChanges.year_3_amount), divideByMillions)));
        gridData.Add((tfpBudgetChanges == null ? 0 : FormatValue((tfpBudgetChanges.year_4_amount), divideByMillions)));
        row.name = ((langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == "new_budget_req_text")).Value).LangText;
        row.Add("griData", gridData);
        jsonData.Add(row);

        //#region temp sum rows

        List<TempDetails> tempData = await (from a in dbContext.tfp_temp_header
            join b in dbContext.tfp_temp_detail on new { a = a.fk_tenant_id, b = a.pk_temp_id }
                equals new { a = b.fk_tenant_id, b = b.fk_temp_id }
            where a.fk_tenant_id == userDetails.tenant_id && b.budget_year == budgetYear
            select new TempDetails
            {
                isParkedAction = a.is_parked_action,
                year1 = b.year_1_amount,
                year2 = b.year_2_amount,
                year3 = b.year_3_amount,
                year4 = b.year_4_amount
            }).ToListAsync();

        row = new JObject();
        row.id = 9;
        gridData = new JArray();
        gridData.Add("");
        gridData.Add(FormatValue(tempData.Where(x => !x.isParkedAction).Sum(x => x.year1), divideByMillions));
        gridData.Add(FormatValue(tempData.Where(x => !x.isParkedAction).Sum(x => x.year2), divideByMillions));
        gridData.Add(FormatValue(tempData.Where(x => !x.isParkedAction).Sum(x => x.year3), divideByMillions));
        gridData.Add(FormatValue(tempData.Where(x => !x.isParkedAction).Sum(x => x.year4), divideByMillions));
        row.name = ((langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == "BM_sum_temp_action")).Value).LangText;
        row.Add("griData", gridData);
        jsonDataSimulation.Add(row);

        //parked action
        row = new JObject();
        row.id = 10;
        gridData = new JArray();
        gridData.Add("");
        gridData.Add(FormatValue(tempData.Where(x => x.isParkedAction).Sum(x => x.year1), divideByMillions));
        gridData.Add(FormatValue(tempData.Where(x => x.isParkedAction).Sum(x => x.year2), divideByMillions));
        gridData.Add(FormatValue(tempData.Where(x => x.isParkedAction).Sum(x => x.year3), divideByMillions));
        gridData.Add(FormatValue(tempData.Where(x => x.isParkedAction).Sum(x => x.year4), divideByMillions));
        row.name = ((langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == "BM_sum_temp_parked_action")).Value).LangText;
        row.Add("griData", gridData);
        jsonDataSimulation.Add(row);

        row = new JObject();
        row.id = 11;
        gridData = new JArray();
        gridData.Add("");
        decimal year1Amount = 0, year2Amount = 0, year3Amount = 0, year4Amount = 0;
        if (simulatorData.FinPlanActions.Any())
        {
            year1Amount = FormatValue(transDataCopy.Where(z => simulatorData.FinPlanActions.Contains(z.fk_action_id)).Sum(x => x.year_1_amount), divideByMillions) * -1;
            year2Amount = FormatValue(transDataCopy.Where(z => simulatorData.FinPlanActions.Contains(z.fk_action_id)).Sum(x => x.year_2_amount), divideByMillions) * -1;
            year3Amount = FormatValue(transDataCopy.Where(z => simulatorData.FinPlanActions.Contains(z.fk_action_id)).Sum(x => x.year_3_amount), divideByMillions) * -1;
            year4Amount = FormatValue(transDataCopy.Where(z => simulatorData.FinPlanActions.Contains(z.fk_action_id)).Sum(x => x.year_4_amount), divideByMillions) * -1;
        }
        gridData.Add(year1Amount);
        gridData.Add(year2Amount);
        gridData.Add(year3Amount);
        gridData.Add(year4Amount);
        row.name = ((langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == "BM_changeAmount_after_simulation")).Value).LangText;
        row.Add("griData", gridData);
        jsonDataSimulation.Add(row);

        row = new JObject();
        row.id = 12;
        gridData = new JArray();
        gridData.Add("");
        gridData.Add(FormatValue(dataAfterSimulation.bListData.Sum(x => x.Year1), divideByMillions));
        gridData.Add(FormatValue(dataAfterSimulation.bListData.Sum(x => x.Year2), divideByMillions));
        gridData.Add(FormatValue(dataAfterSimulation.bListData.Sum(x => x.Year3), divideByMillions));
        gridData.Add(FormatValue(dataAfterSimulation.bListData.Sum(x => x.Year4), divideByMillions));
        row.name = ((langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == "BM_changeAmount_after_blist_simulation")).Value).LangText;
        row.Add("griData", gridData);
        jsonDataSimulation.Add(row);

        row = new JObject();
        row.id = 13;
        gridData = new JArray();
        gridData.Add("");
        gridData.Add(FormatValue(dataAfterSimulation.bListData.Sum(x => x.Year1), divideByMillions) + year1Amount);
        gridData.Add(FormatValue(dataAfterSimulation.bListData.Sum(x => x.Year2), divideByMillions) + year2Amount);
        gridData.Add(FormatValue(dataAfterSimulation.bListData.Sum(x => x.Year3), divideByMillions) + year3Amount);
        gridData.Add(FormatValue(dataAfterSimulation.bListData.Sum(x => x.Year4), divideByMillions) + year4Amount);
        row.name = ((langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == "BM_overall_amount_simulation")).Value).LangText;
        row.Add("griData", gridData);
        jsonDataSimulation.Add(row);

        //#endregion temp sum rows

        dynamic fields = new JArray() { "id", "name", "budgetyear", "year1", "year2", "year3", "year4" };
        dynamic titles = new JArray() { " ", " ", ((langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == "BM_Budget")).Value).LangText + " " + (budgetYear - 1).ToString(), (budgetYear).ToString(), (budgetYear + 1).ToString(), (budgetYear + 2).ToString(), (budgetYear + 3).ToString() };

        data.Add("fields", fields);
        data.Add("titles", titles);
        data.Add("jsonData", jsonData);
        data.Add("jsonDataSimulation", jsonDataSimulation);
        data.amountFormat = "#,##";
        string budgetMeetingNews = string.Empty;
        string budgetMeetingProposal = string.Empty;
        data.MeetingNews = budgetMeetingNews;
        data.MeetingProposal = budgetMeetingProposal;
        data.GridTitle = ((langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == "bm_overview_title")).Value).LangText + " " + budgetYear.ToString() + "-" + (budgetYear + 3).ToString();
        if (dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == activeChangeId) == null)
        {
            data.isReadonly = true;
        }
        else
        {
            if (dataActiveBudgetChanges.FirstOrDefault(x => x.pk_change_id == activeChangeId).workflow_status == 20)
            {
                data.isReadonly = false;
            }
            else
            {
                data.isReadonly = true;
            }
        }
        return data;
    }



    private decimal GetNetRestultPercentage(List<ConsequenceOverViewHelper> Budgetdata, int year, bool divideByMillions)
    {
        List<int> lineGroup = new List<int>() { 10, 20, 30, 40 };
        if (Budgetdata.FirstOrDefault(x => x.line_group_id == 10) != null)
        {
            decimal percent = 0;
            if (Budgetdata.Where(x => x.line_group_id == 10).Sum(z => z.Year1) != 0)
            {
                switch (year)
                {
                    case 1: percent = (FormatValue(Budgetdata.Where(x => lineGroup.Contains(x.line_group_id)).Sum(z => z.Year1), divideByMillions) / FormatValue(Budgetdata.Where(x => x.line_group_id == 10).Sum(z => z.Year1), divideByMillions)) * 100; break;
                    case 2: percent = (FormatValue(Budgetdata.Where(x => lineGroup.Contains(x.line_group_id)).Sum(z => z.Year2), divideByMillions) / FormatValue(Budgetdata.Where(x => x.line_group_id == 10).Sum(z => z.Year2), divideByMillions)) * 100; break;
                    case 3: percent = (FormatValue(Budgetdata.Where(x => lineGroup.Contains(x.line_group_id)).Sum(z => z.Year3), divideByMillions) / FormatValue(Budgetdata.Where(x => x.line_group_id == 10).Sum(z => z.Year3), divideByMillions)) * 100; break;
                    case 4: percent = (FormatValue(Budgetdata.Where(x => lineGroup.Contains(x.line_group_id)).Sum(z => z.Year4), divideByMillions) / FormatValue(Budgetdata.Where(x => x.line_group_id == 10).Sum(z => z.Year4), divideByMillions)) * 100; break;
                }
            }
            return percent;
        }
        else
        {
            return 0;
        }
    }



    public string GetPctTotalGrossExpenses(string userId, int budgetYear, string orgId, string serviceId, string orgIdText, string serviceIdText)
    {
        try
        {
            TenantDBContext tenantDbConext = _utility.GetTenantDBContext();
            UserData userDetails = _utility.GetUserDetails(userId);
            var orgVersionContent = _utility.GetOrgVersionSpecificContent(userId, _utility.GetForecastPeriod(budgetYear, 1));

            Dictionary<string, clsLanguageString> langStringValues = new Dictionary<string, clsLanguageString>();

            langStringValues = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "BudgetManagement");

            var divideByMillions = _utility.GetParameterValue(userId, "BM_FINPLAN_INFOGRAPHICS_DIVIDE_BY_MILL");

            List<clsOrgStructure> lstOrgStructure = _utility.GetTenantOrgStructure(orgVersionContent, userId);
            clsOrgStructureLevelDetails tenantOrgLevelDetails = _utility.GetTenantOrgLevelDetails(lstOrgStructure);

            List<List<string>> DepartmentsAndFunctions = _utility.GetDepartmentsAndFunctionsForOrgStructure(lstOrgStructure, orgId, serviceId, tenantOrgLevelDetails.level1, tenantOrgLevelDetails.level2);

            List<string> lstAllDepartments = DepartmentsAndFunctions[0];
            List<string> lstAllFunctions = DepartmentsAndFunctions[1];

            var querydata = (from a in tenantDbConext.tfp_trans_detail
                join d in tenantDbConext.tfp_trans_header on new { a = a.fk_tenant_id, b = a.fk_action_id }
                    equals new { a = d.fk_tenant_id, b = d.pk_action_id }
                join b in tenantDbConext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code }
                    equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                join c in tenantDbConext.gmd_reporting_line on b.fk_kostra_account_code equals c.fk_kostra_account_code
                where (a.fk_tenant_id == userDetails.tenant_id && b.dateFrom.Year <= budgetYear && b.dateTo.Year >= budgetYear
                       && a.budget_year == budgetYear
                       && (c.line_group_id == 6) && c.report == "Drift")
                select new ConsequenceOverViewHelper
                {
                    actionId = a.pk_id,
                    FkactionId = a.fk_action_id,
                    line_group_id = c.line_group_id,
                    Year1 = (!string.IsNullOrEmpty(divideByMillions) && divideByMillions.ToLower() == "true") ? (a.year_1_amount / 1000000) : (a.year_1_amount / 1000),
                    Year2 = (!string.IsNullOrEmpty(divideByMillions) && divideByMillions.ToLower() == "true") ? (a.year_2_amount / 1000000) : (a.year_2_amount / 1000),
                    Year3 = (!string.IsNullOrEmpty(divideByMillions) && divideByMillions.ToLower() == "true") ? (a.year_3_amount / 1000000) : (a.year_3_amount / 1000),
                    Year4 = (!string.IsNullOrEmpty(divideByMillions) && divideByMillions.ToLower() == "true") ? (a.year_4_amount / 1000000) : (a.year_4_amount / 1000),
                    ActionType = d.action_type,
                    Priority = d.priority,
                    tags = d.tags,
                    line_group = c.line_group,
                    FunctionCode = a.function_code,
                    departmentCode = a.department_code
                }).ToList();

            if (!querydata.Any())
            {
                return "";
            }

            var totalAmount = querydata.Sum(x => x.Year1);

            if (lstAllFunctions.Any(x => !string.IsNullOrEmpty(x)))
            {
                querydata = querydata.Where(y => lstAllFunctions.Contains(y.FunctionCode)).ToList();
            }
            if (lstAllDepartments.Any(x => !string.IsNullOrEmpty(x)))
            {
                querydata = querydata.Where(y => lstAllDepartments.Contains(y.departmentCode)).ToList();
            }

            var orgSpecificAmount = querydata.Sum(x => x.Year1);

            var orgSpecificPct = (orgSpecificAmount / totalAmount) * 100;
            var othersPct = 100 - orgSpecificPct;

            dynamic obj = new JObject();
            dynamic plotArea = new JObject();
            dynamic plotAreaMargin = new JObject();
            plotAreaMargin.top = 20;
            plotArea.margin = plotAreaMargin;

            obj.plotArea = plotArea;

            dynamic lgnd = new JObject();
            lgnd.position = "bottom";
            lgnd.orientation = "vertical";
            obj.legend = lgnd;

            dynamic dSource = new JObject();
            dynamic data = new JArray();

            dynamic data1 = new JObject();
            data1.name = string.IsNullOrEmpty(serviceIdText) ? (orgIdText) : serviceIdText;
            data1.value = orgSpecificPct;
            data1.tooltipValue = orgSpecificAmount;
            data1.explode = true;
            data.Add(data1);

            data1 = new JObject();
            data1.name = ((langStringValues.FirstOrDefault(v => v.Key == "PctTotalGrossExpenses_TenantDesc")).Value).LangText;
            data1.value = othersPct;
            data1.tooltipValue = totalAmount - orgSpecificAmount;
            data.Add(data1);

            dSource.data = data;
            obj.dataSource = dSource;

            dynamic seriesDefaults = new JObject();
            seriesDefaults.overlay = "gradient";

            obj.seriesDefaults = seriesDefaults;

            dynamic series = new JArray();

            dynamic seriesObj = new JObject();
            seriesObj.padding = 0;
            seriesObj.type = "pie";
            seriesObj.field = "value";
            seriesObj.categoryField = "name";
            seriesObj.explodeField = "explode";

            dynamic seriesLabels = new JObject();
            seriesLabels.font = "13px regularFont, sans-serif";
            seriesLabels.background = "#fff";
            seriesLabels.opacity = 0.99;
            seriesLabels.color = "#000";
            seriesLabels.visible = true;
            seriesLabels.position = "center";
            seriesLabels.padding = 10;
            seriesLabels.template = "${kendo.toString(value, 'n2')}%";

            seriesObj.labels = seriesLabels;

            series.Add(seriesObj);
            obj.series = series;

            var colors = _utility.GetColors(userId, ColorsFor.PctTotalGrossExpenses);

            dynamic seriesColors = new JArray();
            if (colors.Any())
            {
                seriesColors.Add(colors["0"]);
                seriesColors.Add(colors["1"]);
            }
            else
            {
                seriesColors.Add("#0e334e");
                seriesColors.Add("#71a6da");
            }

            obj.seriesColors = seriesColors;

            dynamic tTip = new JObject();
            tTip.visible = true;
            tTip.template = "${ category } - ${ kendo.toString(dataItem.tooltipValue,'n0')}" + ((!string.IsNullOrEmpty(divideByMillions) && divideByMillions.ToLower() == "true") ? " mill" : "");
            obj.tooltip = tTip;

            obj.type = "PctTotalGrossExpenses";
            obj.transitions = false;

            return JsonConvert.SerializeObject(obj);
        }
        catch
        {
            return string.Empty;
        }
    }



    public string GetBudgetByService(string userId, int budgetYear, string orgId, string serviceId, bool isWebHelper, bool isChapterSetup = false)
    {
        UserData userDetails = _utility.GetUserDetails(userId);
        Dictionary<string, clsLanguageString> langStrings = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "BudgetProposal");
        List<BudgetByServiceDataHelper> functionsDataSet = new List<BudgetByServiceDataHelper>();
        if (isChapterSetup)
        {
            functionsDataSet = GetChapterDataSet(userId, budgetYear, orgId, serviceId);
        }
        else
        {
            functionsDataSet = GetFunctionsDataSet(userId, budgetYear, orgId, serviceId);
        }

        var colors = _utility.GetColors(userId, ColorsFor.BudgetByService);

        if (!functionsDataSet.Any())
        {
            return "";
        }
        //format data
        dynamic chartData = new JObject();

        dynamic legend = new JObject();
        legend.visible = false;
        chartData.legend = legend;

        dynamic chartArea = new JObject();
        chartArea.height = 500;
        if (!isWebHelper)
        {
            chartArea.width = 700;
        }
        chartData.chartArea = chartArea;

        chartData.transitions = false;

        dynamic seriesDefaults = new JObject();
        seriesDefaults.type = "bar";
        seriesDefaults.gap = 0.5;
        dynamic overlay = new JObject();
        overlay.gradient = "none";
        seriesDefaults.overlay = overlay;

        dynamic labels1 = new JObject();
        labels1.visible = true;
        labels1.font = "13px regularFont, sans-serif";
        labels1.opacity = 0.99;
        labels1.position = "insideBase";
        labels1.format = "{0}";
        dynamic margin = new JObject();
        margin.left = 5;
        margin.right = 15;
        labels1.margin = margin;
        labels1.template = "#= dataItem.name #: #=(kendo.toString(dataItem.data,dataItem.tooltipText))#";
        labels1.background = "transparent";
        labels1.opacity = 0.99;
        labels1.color = "#000";
        seriesDefaults.labels = labels1;
        chartData.seriesDefaults = seriesDefaults;

        dynamic categoryAxis = new JObject();
        dynamic majorGridLines = new JObject();
        majorGridLines.visible = false;
        categoryAxis.majorGridLines = majorGridLines;
        dynamic labels2 = new JObject();
        labels2.rotation = -90;
        categoryAxis.labels = labels2;
        categoryAxis.min = 0;
        categoryAxis.max = 16;
        categoryAxis.visible = true;
        chartData.categoryAxis = categoryAxis;

        dynamic valueAxis = new JObject();
        dynamic labels3 = new JObject();
        labels3.visible = true;
        labels3.template = "#=kendo.toString(value,'n0')#";
        if (colors.Any())
        {
            labels3.color = colors["0"];
        }
        else
        {
            labels3.color = "#606060";
        }

        labels3.font = "14px regularFont, sans-serif";
        valueAxis.labels = labels3;
        dynamic minorGridLines = new JObject();
        minorGridLines.visible = false;
        categoryAxis.minorGridLines = minorGridLines;
        chartData.valueAxis = valueAxis;

        dynamic seriesObj = new JObject();
        dynamic series = new JArray();
        seriesObj.type = "bar";
        seriesObj.field = "data";
        series.Add(seriesObj);
        chartData.series = series;

        chartData.renderAs = "canvas";
        dynamic pannable = new JObject() { new JProperty("lock", "x") };
        chartData.pannable = pannable;
        chartData.zoomable = false;

        dynamic tooltip = new JObject();
        tooltip.visible = true;
        tooltip.format = "{0}%";
        dynamic padding = new JObject();
        padding.right = -10;
        tooltip.padding = padding;
        tooltip.template = "#= dataItem.name #: #=(kendo.toString(dataItem.data,dataItem.tooltipText))#";
        chartData.tooltip = tooltip;

        bool isDevideByMillions = _utility.CheckOnDivideByMillions(userId, "Million_BM_doc_budget_service_title");
        string numberType = isDevideByMillions ? "n1" : "n0";
        int devideAmount = isDevideByMillions ? 1000000 : 1000;

        var functions = new List<KeyValueDecimal>();
        if (isChapterSetup)
        {
            functions = functionsDataSet.GroupBy(x => new { x.attrinuteId, x.attributeName })
                .Select(g =>
                    new KeyValueDecimal
                    {
                        Value = (g.Sum(x => x.year1Amount)) / devideAmount,
                        Key = g.Key.attributeName,
                    }).Distinct().OrderByDescending(x => x.Value).ToList();

            functions = functions.Where(x => Math.Round(Convert.ToDecimal(x.Value)) != 0).ToList();
        }
        else
        {
            functions = functionsDataSet.GroupBy(x => new { x.fkFunctionCode, x.displayName })
                .Select(g =>
                    new KeyValueDecimal
                    {
                        Value = (g.Sum(x => x.year1Amount)) / devideAmount,
                        Key = g.Key.displayName,
                    }).Distinct().OrderByDescending(x => x.Value).ToList();

            functions = functions.Where(x => Math.Round(Convert.ToDecimal(x.Value)) != 0).ToList();
        }
        var functionsList = isWebHelper ? functions : functions.Take(10);

        dynamic dataSource = new JArray();
        int maxRecordsForPublish = 15;
        bool isMax = false;
        decimal? amount = 0;
        int dataCount = 0;

        foreach (var f in functionsList)
        {
            dataCount++;
            if (dataCount <= maxRecordsForPublish)
            {
                dynamic dataSourceObj = new JObject();
                dataSourceObj.name = f.Key;
                dataSourceObj.data = f.Value;
                if (colors.Any())
                {
                    dataSourceObj.color = colors["1"];
                }
                else
                {
                    dataSourceObj.color = "#71a6da";
                }

                dataSourceObj.opacity = 0.2;
                dataSourceObj.tooltipText = numberType;
                dataSource.Add(dataSourceObj);
            }
            else
            {
                isMax = true;
                amount = amount + f.Value;
            }
        }
        if (isMax)
        {
            dynamic dataSourceObj = new JObject();
            dataSourceObj.name = langStrings["graph_othersText"].LangText;
            dataSourceObj.data = amount;
            if (colors.Any())
            {
                dataSourceObj.color = colors["2"];
            }
            else
            {
                dataSourceObj.color = "#71a6da";
            }

            dataSourceObj.opacity = 0.2;
            dataSourceObj.tooltipText = numberType;
            dataSource.Add(dataSourceObj);
        }
        chartData.dataSource = dataSource;
        return JsonConvert.SerializeObject(chartData);
    }



    private List<BudgetByServiceDataHelper> GetFunctionsDataSet(string UserId, int BudgetYear, string orgId, string serviceId)
    {
        TenantDBContext tenantDbConext = _utility.GetTenantDBContext();
        UserData userDetails = _utility.GetUserDetails(UserId);
        List<BudgetByServiceDataHelper> functionsDataSet;

        if (string.IsNullOrEmpty(serviceId) || (serviceId == "All"))
        {
            functionsDataSet = (from a in tenantDbConext.vw_doc_cost_graphs_sa
                where a.fk_tenant_id == userDetails.tenant_id && a.budget_year == BudgetYear
                                                              && a.fp_level_1_value == orgId
                select new BudgetByServiceDataHelper
                {
                    fpLevel1Value = a.fp_level_1_value,
                    fpLevel2Value = a.fp_level_2_value,
                    orgId = a.org_id,
                    orgName = a.org_name,
                    fkFunctionCode = a.fk_function_code,
                    displayName = a.display_name,
                    year1Amount = a.year_1_amount,
                    year2Amount = a.year_2_amount,
                    year3Amount = a.year_3_amount,
                    year4Amount = a.year_4_amount
                }).ToList();
        }
        else if (!string.IsNullOrEmpty(serviceId) && string.IsNullOrEmpty(orgId))
        {
            functionsDataSet = (from a in tenantDbConext.vw_doc_cost_graphs_sa
                where a.fk_tenant_id == userDetails.tenant_id && a.budget_year == BudgetYear
                                                              && a.fp_level_2_value == serviceId
                select new BudgetByServiceDataHelper
                {
                    fpLevel1Value = a.fp_level_1_value,
                    fpLevel2Value = a.fp_level_2_value,
                    orgId = a.org_id,
                    orgName = a.org_name,
                    fkFunctionCode = a.fk_function_code,
                    displayName = a.display_name,
                    year1Amount = a.year_1_amount,
                    year2Amount = a.year_2_amount,
                    year3Amount = a.year_3_amount,
                    year4Amount = a.year_4_amount
                }).ToList();
        }
        else
        {
            functionsDataSet = (from a in tenantDbConext.vw_doc_cost_graphs_sa
                where a.fk_tenant_id == userDetails.tenant_id && a.budget_year == BudgetYear
                                                              && a.fp_level_1_value == orgId && a.fp_level_2_value == serviceId
                select new BudgetByServiceDataHelper
                {
                    fpLevel1Value = a.fp_level_1_value,
                    fpLevel2Value = a.fp_level_2_value,
                    orgId = a.org_id,
                    orgName = a.org_name,
                    fkFunctionCode = a.fk_function_code,
                    displayName = a.display_name,
                    year1Amount = a.year_1_amount,
                    year2Amount = a.year_2_amount,
                    year3Amount = a.year_3_amount,
                    year4Amount = a.year_4_amount
                }).ToList();
        }
        return functionsDataSet;
    }



    private List<BudgetByServiceDataHelper> GetChapterDataSet(string UserId, int BudgetYear, string orgId, string serviceId)
    {
        TenantDBContext tenantDbConext = _utility.GetTenantDBContext();
        UserData userDetails = _utility.GetUserDetails(UserId);
        List<BudgetByServiceDataHelper> chapterDataSet;

        if (string.IsNullOrEmpty(serviceId) || (serviceId == "All"))
        {
            chapterDataSet = (from a in tenantDbConext.vw_doc_cost_graphs_sa
                where a.fk_tenant_id == userDetails.tenant_id && a.budget_year == BudgetYear
                                                              && a.fp_level_1_value == orgId
                select new BudgetByServiceDataHelper
                {
                    attrinuteId = a.attribute_value,
                    attributeName = a.attribute_value + '-' + a.attribute_name,
                    year1Amount = a.year_1_amount,
                    year2Amount = a.year_2_amount,
                    year3Amount = a.year_3_amount,
                    year4Amount = a.year_4_amount
                }).ToList();
        }
        else if (!string.IsNullOrEmpty(serviceId) && string.IsNullOrEmpty(orgId))
        {
            chapterDataSet = (from a in tenantDbConext.vw_doc_cost_graphs_sa
                where a.fk_tenant_id == userDetails.tenant_id && a.budget_year == BudgetYear
                                                              && a.fp_level_2_value == serviceId
                select new BudgetByServiceDataHelper
                {
                    attrinuteId = a.attribute_value,
                    attributeName = a.attribute_value + '-' + a.attribute_name,
                    year1Amount = a.year_1_amount,
                    year2Amount = a.year_2_amount,
                    year3Amount = a.year_3_amount,
                    year4Amount = a.year_4_amount
                }).ToList();
        }
        else
        {
            chapterDataSet = (from a in tenantDbConext.vw_doc_cost_graphs_sa
                where a.fk_tenant_id == userDetails.tenant_id && a.budget_year == BudgetYear
                                                              && a.fp_level_1_value == orgId && a.fp_level_2_value == serviceId
                select new BudgetByServiceDataHelper
                {
                    attrinuteId = a.attribute_value,
                    attributeName = a.attribute_value + '-' + a.attribute_name,
                    year1Amount = a.year_1_amount,
                    year2Amount = a.year_2_amount,
                    year3Amount = a.year_3_amount,
                    year4Amount = a.year_4_amount
                }).ToList();
        }
        return chapterDataSet.OrderBy(x => x.attrinuteId).ToList();
    }



    private List<BudgetByServiceDataHelper> GetDepartmentsDataSet(string UserId, int BudgetYear, string orgId, string serviceId, string budgetPhaseId)
    {
        TenantDBContext tenantDbConext = _utility.GetTenantDBContext();
        UserData userDetails = _utility.GetUserDetails(UserId);
        List<BudgetByServiceDataHelper> departmentsDataSet;
        var costGraphSa = tenantDbConext.vw_doc_cost_graphs_sa.Where(a => a.fk_tenant_id == userDetails.tenant_id && a.budget_year == BudgetYear);
            
        if (!string.IsNullOrEmpty(budgetPhaseId) && budgetPhaseId != "-1")
        {
            List<int> changeIds = _utility.GetChangeDataUsingBudgetPhase(budgetPhaseId, userDetails.tenant_id, BudgetYear);
            if (changeIds.Any())
            {
                costGraphSa = costGraphSa.Where(x => changeIds.Contains(x.fk_change_id));
            }
        }
            
        if (string.IsNullOrEmpty(serviceId) || serviceId == "All")
        {
            departmentsDataSet = (from a in costGraphSa
                where a.fk_tenant_id == userDetails.tenant_id && a.budget_year == BudgetYear
                                                              && a.fp_level_1_value == orgId
                select new BudgetByServiceDataHelper
                {
                    fpLevel1Value = a.fp_level_1_value,
                    fpLevel2Value = a.fp_level_2_value,
                    orgId = a.org_id,
                    orgName = a.org_name,
                    orgIdL2 = a.org_id_l2,
                    orgNameL2 = a.org_name_l2,
                    fkFunctionCode = a.fk_function_code,
                    displayName = a.display_name,
                    year1Amount = a.year_1_amount,
                    year2Amount = a.year_2_amount,
                    year3Amount = a.year_3_amount,
                    year4Amount = a.year_4_amount,
                }).ToList();
        }
        else if (string.IsNullOrEmpty(orgId))
        {
            departmentsDataSet = (from a in costGraphSa
                where a.fk_tenant_id == userDetails.tenant_id && a.budget_year == BudgetYear
                                                              && a.fp_level_2_value == serviceId
                select new BudgetByServiceDataHelper
                {
                    fpLevel1Value = a.fp_level_1_value,
                    fpLevel2Value = a.fp_level_2_value,
                    orgId = a.org_id,
                    orgName = a.org_name,
                    orgIdL2 = a.org_id_l2,
                    orgNameL2 = a.org_name_l2,
                    fkFunctionCode = a.fk_function_code,
                    displayName = a.display_name,
                    year1Amount = a.year_1_amount,
                    year2Amount = a.year_2_amount,
                    year3Amount = a.year_3_amount,
                    year4Amount = a.year_4_amount,
                }).ToList();
        }
        else
        {
            departmentsDataSet = (from a in costGraphSa
                where a.fk_tenant_id == userDetails.tenant_id && a.budget_year == BudgetYear
                                                              && a.fp_level_1_value == orgId && a.fp_level_2_value == serviceId
                select new BudgetByServiceDataHelper
                {
                    fpLevel1Value = a.fp_level_1_value,
                    fpLevel2Value = a.fp_level_2_value,
                    orgId = a.org_id,
                    orgName = a.org_name,
                    orgIdL2 = a.org_id_l2,
                    orgNameL2 = a.org_name_l2,
                    fkFunctionCode = a.fk_function_code,
                    displayName = a.display_name,
                    year1Amount = a.year_1_amount,
                    year2Amount = a.year_2_amount,
                    year3Amount = a.year_3_amount,
                    year4Amount = a.year_4_amount,
                }).ToList();
        }
        return departmentsDataSet;
    }



    public string GetBudgetByDepartment(string userId, int budgetYear, string orgId, string serviceId, bool isWebHelper, string budgetPhaseId)
    {
        UserData userDetails = _utility.GetUserDetails(userId);
        Dictionary<string, clsLanguageString> langStrings = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "BudgetProposal");

        var departmentsDataSet = GetDepartmentsDataSet(userId, budgetYear, orgId, serviceId, budgetPhaseId);

        var colors = _utility.GetColors(userId, ColorsFor.BudgetByDepartment);

        if (!departmentsDataSet.Any())
        {
            return "";
        }

        //format data
        dynamic chartData = new JObject();

        dynamic legend = new JObject();
        legend.visible = false;
        chartData.legend = legend;

        dynamic chartArea = new JObject();
        chartArea.height = 500;
        if (!isWebHelper)
        {
            chartArea.width = 700;
        }

        chartData.chartArea = chartArea;

        chartData.transitions = false;

        dynamic seriesDefaults = new JObject();
        seriesDefaults.type = "bar";
        seriesDefaults.gap = 0.5;
        dynamic overlay = new JObject();
        overlay.gradient = "none";
        seriesDefaults.overlay = overlay;

        dynamic labels1 = new JObject();
        labels1.visible = true;
        labels1.font = "13px regularFont, sans-serif";
        labels1.opacity = 0.99;
        labels1.position = "right";
        labels1.format = "{0}";
        dynamic margin = new JObject();
        margin.left = 5;
        margin.right = 15;
        labels1.margin = margin;
        labels1.template = "#= dataItem.name #: #=(kendo.toString(dataItem.data,dataItem.tooltipText))#";
        labels1.background = "transparent";
        labels1.color = "#000";
        seriesDefaults.labels = labels1;
        chartData.seriesDefaults = seriesDefaults;

        dynamic categoryAxis = new JObject();
        dynamic majorGridLines = new JObject();
        majorGridLines.visible = false;
        categoryAxis.majorGridLines = majorGridLines;
        dynamic labels2 = new JObject();
        labels2.rotation = -90;
        categoryAxis.labels = labels2;
        categoryAxis.min = 0;
        categoryAxis.max = 16;
        categoryAxis.visible = true;
        chartData.categoryAxis = categoryAxis;

        dynamic valueAxis = new JObject();
        dynamic labels3 = new JObject();
        labels3.visible = true;
        labels3.template = "#=kendo.toString(value,'n0')#";
        if (colors.Any())
        {
            labels3.color = colors["0"];
        }
        else
        {
            labels3.color = "#606060";
        }
        labels3.font = "14px regularFont, sans-serif";
        valueAxis.labels = labels3;
        dynamic minorGridLines = new JObject();
        minorGridLines.visible = false;
        categoryAxis.minorGridLines = minorGridLines;
        chartData.valueAxis = valueAxis;

        dynamic seriesObj = new JObject();
        dynamic series = new JArray();
        seriesObj.type = "bar";
        seriesObj.field = "data";
        series.Add(seriesObj);
        chartData.series = series;

        chartData.renderAs = "canvas";
        dynamic pannable = new JObject() { new JProperty("lock", "x") };
        chartData.pannable = pannable;

        chartData.zoomable = false;

        dynamic tooltip = new JObject();
        tooltip.visible = true;
        tooltip.format = "{0}%";
        dynamic padding = new JObject();
        padding.right = -10;
        tooltip.padding = padding;
        tooltip.template = "#= dataItem.name #: #=(kendo.toString(dataItem.data,dataItem.tooltipText))#";
        chartData.tooltip = tooltip;

        bool isDevideByMillions = _utility.CheckOnDivideByMillions(userId, "Million_BM_doc_budget_organization_title");
        string numberType = isDevideByMillions ? "n1" : "n0";
        int devideAmount = isDevideByMillions ? 1000000 : 1000;

        bool isServiceIdNull = ((string.IsNullOrEmpty(serviceId) || (serviceId == "All")) || (string.IsNullOrEmpty(orgId) && !string.IsNullOrEmpty(serviceId)));

        //if it's fp_level_1 - group by orgId, orgname else (fp_level_2) gropu by org_id_l2, org_nameL2.
        var departments = isServiceIdNull ? departmentsDataSet.GroupBy(x => new { x.orgId, x.orgName })
                .Select(g =>
                    new
                    {
                        amount = (g.Sum(x => x.year1Amount)) / devideAmount,
                        name = g.Key.orgName
                    }).Distinct().OrderByDescending(x => x.amount).ToList()
            :
            departmentsDataSet.GroupBy(x => new { x.orgIdL2, x.orgNameL2 })
                .Select(g =>
                    new
                    {
                        amount = (g.Sum(x => x.year1Amount)) / devideAmount,
                        name = g.Key.orgNameL2
                    }).Distinct().OrderByDescending(x => x.amount).ToList();
        departments = departments.Where(x => Math.Round(Convert.ToDecimal(x.amount)) != 0).ToList();
        //fetch only top 10 records for document.
        var departmentList = isWebHelper ? departments : departments.Take(10);

        dynamic dataSource = new JArray();
        int maxRecordsForPublish = 15;
        bool isMax = false;
        decimal? amount = 0;
        int dataCount = 0;

        foreach (var d in departmentList)
        {
            dataCount++;
            if (dataCount <= maxRecordsForPublish) //In web display only first 15 records and rest display as 'Others'.
            {
                dynamic dataSourceObj = new JObject();
                dataSourceObj.name = d.name;
                dataSourceObj.data = d.amount;
                if (colors.Any())
                {
                    dataSourceObj.color = colors["1"];
                }
                else
                {
                    dataSourceObj.color = "#71a6da";
                }

                dataSourceObj.opacity = 0.2;
                dataSourceObj.tooltipText = numberType;
                dataSource.Add(dataSourceObj);
            }
            else
            {
                isMax = true;
                amount = amount + d.amount;
            }
        }
        if (isMax)
        {
            dynamic dataSourceObj = new JObject();
            dataSourceObj.name = dataSourceObj.name = langStrings["graph_othersText"].LangText;
            dataSourceObj.data = amount;
            if (colors.Any())
            {
                dataSourceObj.color = colors["2"];
            }
            else
            {
                dataSourceObj.color = "#71a6da";
            }

            dataSourceObj.opacity = 0.2;
            dataSourceObj.tooltipText = numberType;
            dataSource.Add(dataSourceObj);
        }
        chartData.dataSource = dataSource;
        return JsonConvert.SerializeObject(chartData);
    }



    public void WriteToDocDelReqQueue(string docUrl, string userId)
    {
        WriteToDocDelReqQueueAsync(docUrl, userId).GetAwaiter().GetResult();
    }



    public async Task WriteToDocDelReqQueueAsync(string docUrl, string userId)
    {
        UserData ud = await _utility.GetUserDetailsAsync(userId);
        //the message shall be processed after 5 minutes
        await _backendJob.QueueMessageAsync(ud, QueueName.bmedeletedocqueue, docUrl, 5);
    }



    public dynamic GetServiceAreaBudgetData(string yearSelected, string userId, int BudgetYear)
    {
        return GetServiceAreaBudgetData(yearSelected, userId, "", BudgetYear);
    }



    public dynamic GetServiceAreaBudgetData(string yearSelected, string userId, string langPref, int budgetYear, bool splitData = false)
    {
        return GetServiceAreaBudgetDataAsync(yearSelected, userId, langPref, budgetYear, splitData).GetAwaiter().GetResult();
    }

}