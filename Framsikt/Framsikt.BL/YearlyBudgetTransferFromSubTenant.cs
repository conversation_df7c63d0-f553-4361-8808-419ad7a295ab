using Framsikt.BL.Helpers;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Framsikt.Entities.Core;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Globalization;
using System.Linq.Dynamic.Core;

namespace Framsikt.BL.Core
{
    public class YearlyBudgetTransferFromSubTenant : IYearlyBudgetTransferFromSubTenant
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IUtility _utility;
        private readonly IDataSyncUtility _dataSyncUtility;
        private readonly IBackendRequest _backendJob;
        private readonly ISubTentFpAdmin _stFpAdmin;
        private readonly IBudgetProposalProcess _budgetProposalProcess;
        private readonly IBudgetTransferInvestment _budgetTransInv;
        private readonly IFinUtility _finUtility;
        private readonly IJobLogger _logger;
        private readonly LockManager _lockManager;
        private readonly IConsequenceAdjustedBudget _consequenceAdjustedBudget;
        private readonly IAppDataCache _cache;
        private readonly IBudgetProposals _budgetProposals;

        public YearlyBudgetTransferFromSubTenant(IServiceProvider container, IBackendRequest backendJob, LockManager lockManager)
        {
            _unitOfWork = container.GetRequiredService<IUnitOfWork>();
            _utility = container.GetRequiredService<IUtility>();
            _dataSyncUtility = container.GetRequiredService<IDataSyncUtility>();
            _stFpAdmin = container.GetRequiredService<ISubTentFpAdmin>();
            _budgetProposalProcess = container.GetRequiredService<IBudgetProposalProcess>();
            _budgetTransInv = container.GetRequiredService<IBudgetTransferInvestment>();
            _backendJob = backendJob;
            _finUtility = container.GetRequiredService<IFinUtility>();
            _logger = container.GetRequiredService<IJobLogger>();
            _lockManager = lockManager;
            _consequenceAdjustedBudget = container.GetRequiredService<IConsequenceAdjustedBudget>();
            _cache = container.GetRequiredService<IAppDataCache>();
            _budgetProposals = container.GetRequiredService<IBudgetProposals>();
        }

        public async Task TransferYearlyBudgetQueue(string userId, int budgetYear, List<YbOriginalBudHelper> inputObj, BudgetProcessStage processStage)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, int> modBudgetYears = new Dictionary<string, int>() { };

            if (processStage == BudgetProcessStage.YB_OriginalBudget)
            {
                TenantDBContext dBContext = await _utility.GetTenantDBContextAsync();

                if (await dBContext.tfp_sync_background_job_status
                    .AnyAsync(x => x.main_tenant_id == userDetails.tenant_id
                                  && inputObj.Select(i => i.SubTenantId).Contains(x.sub_tenant_id)
                                  && x.budget_year == budgetYear
                                  && x.process_stage == processStage.ToString()
                                  && x.fk_budget_phase_id == Guid.Empty
                                  && x.job_status == SyncJobStatus.InProgress.ToString()))
                {
                    throw new InvalidOperationException("A sync operation is already pending for this tenant.");
                }
            }

            foreach (var item in inputObj)
            {
                dynamic ybTransferRequest = new JObject();

                ybTransferRequest.Add("userId", userId);
                ybTransferRequest.Add("mainTenantId", userDetails.tenant_id);
                ybTransferRequest.Add("subTenantId", item.SubTenantId);
                ybTransferRequest.Add("budgetYear", budgetYear);
                ybTransferRequest.Add("orgLevel", item.OrgLevel);
                ybTransferRequest.Add("processStage", processStage.ToString());

                string strYBTransferRequest = JsonConvert.SerializeObject(ybTransferRequest);

                await _backendJob.QueueMessageAsync(userDetails, modBudgetYears, QueueName.syncyearlybudgettransferqueue, strYBTransferRequest);
                await _budgetProposalProcess.SaveSyncBkJobLog(userId, budgetYear, userDetails.tenant_id, item.SubTenantId, SyncJobStatus.InProgress.ToString(), processStage, Guid.Empty);
            }
        }

        public async Task<List<AdjustmentDataHelper>> GetBudAuthorityCodeValues(string userId, int budgetYear, int subTenantId = 0)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            List<AdjustmentDataHelper> adjustmentsListInfo = await _unitOfWork.YbTransferRepo.BudgetAdjustmentsListData(userDetails.tenant_id, budgetYear, subTenantId);
            return adjustmentsListInfo;
        }

        public async Task<YbAdjDataDropdownHelper> GetApprovedBudgetAdjInvestments(string userId, int budgetYear, List<AdjustmentDataHelper> adjustmentsListInfo, string orgId, int orgLevel)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));
            var orgVersion = orgVersionContent.orgVersion;
            var orgIds = await _unitOfWork.BudgetProposalProcessRepository.getSubOrgId(userDetails.tenant_id, orgId, orgLevel, orgVersion);
            var gridData = await _unitOfWork.YbTransferRepo.ApprovedBudAdjInvestmentsData(userDetails.tenant_id, budgetYear, orgIds, orgVersion);
            List<string> authCodeList = adjustmentsListInfo.Select(x => x.budgetAuthorityCode).Distinct().ToList();
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetProposalProcess");
            int i = 1;
            YbAdjDataDropdownHelper result = new YbAdjDataDropdownHelper();
            foreach (var obj in gridData)
            {
                var currSubTenantInfo = adjustmentsListInfo.Where(x => x.subTenantId == obj.subTenantId).ToList();
                obj.adjustmentCodeTotalSum = currSubTenantInfo.Sum(x => x.year1Sum);
                obj.adjustmentCodeTotalCount = currSubTenantInfo.GroupBy(x => new { x.useradjustmentCode, x.projectCode }).Count();
                obj.standardBudgetAmount = await _unitOfWork.YbTransferRepo.GetCentralDeptInfoForTransactions(obj.subTenantId, budgetYear);
                if (!string.IsNullOrEmpty(obj.transferStatus))
                {
                    switch (Enum.Parse(typeof(SyncJobStatus), obj.transferStatus))
                    {
                        case SyncJobStatus.NotStarted:
                            obj.transferStatus = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_notStarted", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                            obj.transferStatusValue = new KeyValueHelperData
                            {
                                key = Convert.ToInt32(SyncJobStatus.NotStarted),
                                value = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_notStarted", StringComparison.InvariantCultureIgnoreCase)).Value.LangText
                            };
                            obj.updated = null;
                            obj.updatedBy = string.Empty;
                            break;

                        case SyncJobStatus.InProgress:
                            obj.transferStatus = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_inProgress", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                            obj.transferStatusValue = new KeyValueHelperData
                            {
                                key = Convert.ToInt32(SyncJobStatus.InProgress),
                                value = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_inProgress", StringComparison.InvariantCultureIgnoreCase)).Value.LangText
                            };
                            break;

                        case SyncJobStatus.Completed:
                            obj.transferStatus = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_completed", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                            obj.transferStatusValue = new KeyValueHelperData
                            {
                                key = Convert.ToInt32(SyncJobStatus.Completed),
                                value = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_completed", StringComparison.InvariantCultureIgnoreCase)).Value.LangText
                            };
                            break;

                        case SyncJobStatus.Error:
                            obj.transferStatus = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_error", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                            obj.transferStatusValue = new KeyValueHelperData
                            {
                                key = Convert.ToInt32(SyncJobStatus.Error),
                                value = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_error", StringComparison.InvariantCultureIgnoreCase)).Value.LangText
                            };
                            break;

                        default:
                            obj.transferStatus = string.Empty;
                            obj.transferStatusValue = new KeyValueHelperData
                            {
                                key = Convert.ToInt32(SyncJobStatus.NotStarted),
                                value = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_notStarted", StringComparison.InvariantCultureIgnoreCase)).Value.LangText
                            };
                            break;
                    }
                }
                else
                {
                    obj.transferStatus = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_notStarted", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                    obj.transferStatusValue = new KeyValueHelperData
                    {
                        key = Convert.ToInt32(SyncJobStatus.NotStarted),
                        value = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_notStarted", StringComparison.InvariantCultureIgnoreCase)).Value.LangText
                    };
                    obj.updated = null;
                    obj.updatedBy = string.Empty;
                }
            }
            var subTenantIds = gridData.Select(x => x.subTenantId).ToList();
            List<string> financingAccounts = await _unitOfWork.YbTransferRepo.GetFinancingAccountInfo(userDetails.tenant_id, budgetYear);

            JArray data = JArray.FromObject(gridData);
            foreach (var row in data)
            {
                int subTenantId = (int)(row["subTenantId"] ?? 0);
                if (subTenantId != 0)
                {
                    var currSubTenantInfo = adjustmentsListInfo.Where(x => x.subTenantId == subTenantId).ToList();
                    var adjCodesInfoBasedOnAuthCode = GroupingDataOnBudgetAuthorityCode(currSubTenantInfo, authCodeList);
                    i = 1;
                    adjCodesInfoBasedOnAuthCode.ForEach(x =>
                    {
                        row["authAmt" + i.ToString()] = x.adjCodeSum;
                        row["authCount" + i.ToString()] = x.adjCodesCount;
                        row["authFinAmt" + i.ToString()] = currSubTenantInfo.Where(y => y.bud_auth_code.Equals(x.budAuthCode)).Sum(z => z.year1Sum);
                        i++;
                    });
                }
            }
            result.data = data;
            result.TenantNameList = gridData.Select(x => new KeyValueHelperData { key = x.subTenantId, value = x.subTenant }).ToList();
            result.UpdatedDropdownList = gridData.Select(x => new KeyValueHelperData { key = x.subTenantId, value = x.updatedBy }).ToList();
            return result;
        }

        public async Task<YbAdjDataDropdownHelper> GetApprovedBudgetAdjustments(string userId, int budgetYear, List<AdjustmentDataHelper> adjustmentsListInfo, string orgId, int orgLevel)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));
            var orgVersion = orgVersionContent.orgVersion;
            var orgIds = await _unitOfWork.BudgetProposalProcessRepository.getSubOrgId(userDetails.tenant_id, orgId, orgLevel, orgVersion);
            List<UserInformation> ?users = new();
            string ?usersResult = await _cache.GetStringForTenantAsync(userDetails.client_id, userDetails.tenant_id, "tenantUserDetails_" + userDetails.tenant_id);
            if (!string.IsNullOrEmpty(usersResult))
            {
                users = JsonConvert.DeserializeObject<List<UserInformation>>(usersResult);
            }
            else
            {
                await _budgetProposals.UpdateUserDetailCache(userId);
                users = JsonConvert.DeserializeObject<List<UserInformation>>(await _cache.GetStringForTenantAsync(userDetails.client_id, userDetails.tenant_id, "tenantUserDetails_" + userDetails.tenant_id.ToString()));
            }

            var gridData = await _unitOfWork.YbTransferRepo.ApprovedBudAdjustmentsData(userDetails.tenant_id, budgetYear, orgIds, orgVersion, users);
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetProposalProcess");

            dynamic finalData = new JArray();   

            var centralDeptYear1Sum = await _unitOfWork.YbTransferRepo.GetCentralDeptSumBySubTenantIds(budgetYear, gridData.Select(x => x.subTenantId).Distinct().ToList());

            foreach (var obj in gridData)
            {
                var currSubTenantInfo = adjustmentsListInfo.Where(x => x.subTenantId == obj.subTenantId).ToList();
                obj.adjustmentCodeTotalSum = currSubTenantInfo.Sum(x => x.year1Sum);
                obj.adjustmentCodeTotalCount = currSubTenantInfo.GroupBy(x => new { x.useradjustmentCode, x.actionId }).Count();
                obj.standardBudgetAmount = centralDeptYear1Sum.GetValueOrDefault(obj.subTenantId);
                obj.isAdjustedFPAmtDeviation = obj.deviation != obj.adjustmentCodeTotalSum;
                if (!string.IsNullOrEmpty(obj.transferStatus))
                {
                    switch (Enum.Parse(typeof(SyncJobStatus), obj.transferStatus))
                    {
                        case SyncJobStatus.NotStarted:
                            obj.transferStatus = langStrings["BP_transferStatus_notStarted"].LangText;
                            obj.transferStatusValue = new KeyValueHelperData
                            {
                                key = Convert.ToInt32(SyncJobStatus.NotStarted),
                                value = langStrings["BP_transferStatus_notStarted"].LangText
                            };
                            obj.updated = null;
                            obj.updatedBy = string.Empty;
                            break;

                        case SyncJobStatus.InProgress:
                            obj.transferStatus = langStrings["BP_transferStatus_inProgress"].LangText;
                            obj.transferStatusValue = new KeyValueHelperData
                            {
                                key = Convert.ToInt32(SyncJobStatus.InProgress),
                                value = langStrings["BP_transferStatus_inProgress"].LangText
                            };
                            break;

                        case SyncJobStatus.Completed:
                            obj.transferStatus = langStrings["BP_transferStatus_completed"].LangText;
                            obj.transferStatusValue = new KeyValueHelperData
                            {
                                key = Convert.ToInt32(SyncJobStatus.Completed),
                                value = langStrings["BP_transferStatus_completed"].LangText
                            };
                            break;

                        case SyncJobStatus.Error:
                            obj.transferStatus = langStrings["BP_transferStatus_error"].LangText;
                            obj.transferStatusValue = new KeyValueHelperData
                            {
                                key = Convert.ToInt32(SyncJobStatus.Error),
                                value = langStrings["BP_transferStatus_error"].LangText
                            };
                            break;

                        default:
                            obj.transferStatus = string.Empty;
                            obj.transferStatusValue = new KeyValueHelperData
                            {
                                key = Convert.ToInt32(SyncJobStatus.NotStarted),
                                value = langStrings["BP_transferStatus_notStarted"].LangText
                            };
                            break;
                    }
                }
                else
                {
                    obj.transferStatus = langStrings["BP_transferStatus_notStarted"].LangText;
                    obj.transferStatusValue = new KeyValueHelperData
                    {
                        key = Convert.ToInt32(SyncJobStatus.NotStarted),
                        value = langStrings["BP_transferStatus_notStarted"].LangText
                    };
                    obj.updated = null;
                    obj.updatedBy = string.Empty;
                }
            }

            var subTenantIds = gridData.Select(x => x.subTenantId).ToList();
            List<string> authCodeList = adjustmentsListInfo.Select(x => x.budgetAuthorityCode).Distinct().ToList();

            JArray data = JArray.FromObject(gridData);

            int i = 1;
            foreach (var row in data)
            {
                int subTenantId = (int)(row["subTenantId"] ?? 0);
                if (subTenantId != 0)
                {
                    var currSubTenantInfo = adjustmentsListInfo.Where(x => x.subTenantId == subTenantId).ToList();
                    var adjCodesInfoBasedOnAuthCode = GroupingDataOnBudgetAuthorityCode(currSubTenantInfo, authCodeList);
                    i = 1;
                    adjCodesInfoBasedOnAuthCode.ForEach(x =>
                    {
                        row["authAmt" + i.ToString()] = x.adjCodeSum;
                        row["authCount" + i.ToString()] = x.adjCodesCount;
                        i++;
                    });
                }
            }

            YbAdjDataDropdownHelper result = new YbAdjDataDropdownHelper();
            result.data = data;
            result.TenantNameList = gridData.Select(x => new KeyValueHelperData { key = x.subTenantId, value = x.subTenant }).ToList();
            result.UpdatedDropdownList = gridData.Select(x => new KeyValueHelperData { key = x.subTenantId, value = x.updatedBy }).ToList();
            return result;
        }

        private List<budAuthCodeCountHelper> GroupingDataOnBudgetAuthorityCode(List<AdjustmentDataHelper> adjustmentsListInfo, List<string> authCodes)
        {
            List<budAuthCodeCountHelper> result = new List<budAuthCodeCountHelper>();
            authCodes.ForEach(a =>
            {
                result.Add(new budAuthCodeCountHelper()
                {
                    adjCodesCount = adjustmentsListInfo.Where(x => x.budgetAuthorityCode == a).GroupBy(x => new { x.useradjustmentCode, x.projectCode, x.actionId }).Count(),
                    adjCodeSum = adjustmentsListInfo.Where(x => x.budgetAuthorityCode == a).Sum(x => x.year1Sum),
                    budAuthCode = a
                });
            });
            return result;
        }

        public async Task<List<YbOriginalBudHelper>> GetOriginalBudgetTransferData(string userId, int budgetYear, string orgId, int orgLevel)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var orgVersionContent = _utility.GetOrgVersionBasedOnTenant(userDetails.tenant_id, _utility.GetForecastPeriod(budgetYear, 1));
            var orgVersion = orgVersionContent.orgVersion;
            var orgIds = await _unitOfWork.BudgetProposalProcessRepository.getSubOrgId(userDetails.tenant_id, orgId, orgLevel, orgVersion);
            List<int> subTenantLst = await _unitOfWork.BudgetProposalProcessRepository.GetMainTenantConnectedSubTenantInfo(userDetails.tenant_id, orgVersion, orgIds);
            var gridData = await _unitOfWork.YbTransferRepo.OriginalBudData(userDetails.tenant_id, budgetYear, subTenantLst, orgVersion);

            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetProposalProcess");
            foreach (var item in gridData)
            {
                if (!string.IsNullOrEmpty(item.TransferStatus))
                {
                    switch (Enum.Parse(typeof(SyncJobStatus), item.TransferStatus))
                    {
                        case SyncJobStatus.NotStarted:
                            item.TransferStatus = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_notStarted", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                            item.TransferStatusValue = new KeyValueHelperData
                            {
                                key = Convert.ToInt32(SyncJobStatus.NotStarted),
                                value = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_notStarted", StringComparison.InvariantCultureIgnoreCase)).Value.LangText
                            };
                            item.Updated = null;
                            item.UpdatedBy = string.Empty;
                            break;

                        case SyncJobStatus.InProgress:
                            item.TransferStatus = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_inProgress", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                            item.TransferStatusValue = new KeyValueHelperData
                            {
                                key = Convert.ToInt32(SyncJobStatus.InProgress),
                                value = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_inProgress", StringComparison.InvariantCultureIgnoreCase)).Value.LangText
                            };
                            break;

                        case SyncJobStatus.Completed:
                            item.TransferStatus = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_completed", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                            item.TransferStatusValue = new KeyValueHelperData
                            {
                                key = Convert.ToInt32(SyncJobStatus.Completed),
                                value = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_completed", StringComparison.InvariantCultureIgnoreCase)).Value.LangText
                            };
                            break;

                        case SyncJobStatus.Error:
                            item.TransferStatus = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_error", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                            item.TransferStatusValue = new KeyValueHelperData
                            {
                                key = Convert.ToInt32(SyncJobStatus.Error),
                                value = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_error", StringComparison.InvariantCultureIgnoreCase)).Value.LangText
                            };
                            break;

                        default:
                            item.TransferStatus = string.Empty;
                            item.TransferStatusValue = new KeyValueHelperData
                            {
                                key = Convert.ToInt32(SyncJobStatus.NotStarted),
                                value = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_notStarted", StringComparison.InvariantCultureIgnoreCase)).Value.LangText
                            };
                            break;
                    }
                }
                else
                {
                    item.TransferStatus = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_notStarted", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                    item.TransferStatusValue = new KeyValueHelperData
                    {
                        key = Convert.ToInt32(SyncJobStatus.NotStarted),
                        value = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_notStarted", StringComparison.InvariantCultureIgnoreCase)).Value.LangText
                    };
                    item.Updated = null;
                    item.UpdatedBy = string.Empty;
                }

                double d2, d3;
                double.TryParse(item.DeviationFpAmounts, NumberStyles.Number, CultureInfo.InvariantCulture, out d2);
                double.TryParse(item.DeviationFpYb, NumberStyles.Number, CultureInfo.InvariantCulture, out d3);
                item.DeviationFpAmounts = item.IsFpAmtDeviation ? "<span>" + d2.ToString("N0", CultureInfo.InvariantCulture).Replace(",", " ").Replace(".", ",") + "  " + "<img src = './assets/images/red_alert_icon.svg' alt='deviation'></span>" : d2.ToString("N0", CultureInfo.InvariantCulture).Replace(",", " ").Replace(".", ",");
                item.DeviationFpYb = item.IsFpYbAmtDeviation ? "<span>" + d3.ToString("N0", CultureInfo.InvariantCulture).Replace(",", " ").Replace(".", ",") + "  " + "<img src = './assets/images/red_alert_icon.svg' alt='deviation'></span>" : d3.ToString("N0", CultureInfo.InvariantCulture).Replace(",", " ").Replace(".", ",");
                var parametersData = await _unitOfWork.YbTransferRepo.GetParametersData(item.SubTenantId, budgetYear, "LOCK_ORIGINAL_BUDGET");
                if (parametersData != null)
                {
                    item.BudLocked = parametersData.active == 0 ? "<span><img src = './assets/images/lock_open.svg' alt=''> " + langStrings.FirstOrDefault(x => x.Key.Equals("BP_status_open", StringComparison.InvariantCultureIgnoreCase)).Value.LangText + "</span>" : "<span><img src = './assets/images/lock_closed.svg' alt=''> " + langStrings.FirstOrDefault(x => x.Key.Equals("BP_status_close", StringComparison.InvariantCultureIgnoreCase)).Value.LangText + "</span>";
                }
                else
                {
                    item.BudLocked = "<span><img src = './assets/images/lock_open.svg' alt=''> " + langStrings.FirstOrDefault(x => x.Key.Equals("BP_status_open", StringComparison.InvariantCultureIgnoreCase)).Value.LangText + "</span>";
                }
            }
            return gridData;
        }

        public async Task<DateTime?> GetLastRefreshedDate(string userId, int budgetYear)
        {
            UserData userDetails = _utility.GetUserDetails(userId);
            //List<string> deptCodeList = new List<string>() { "003" };
            //var dataToTransfer = await TransferYbApprovedBudAdjActionsFromSubTenantAsync(userId, 2024, 7893, 7684, "003", new Guid(), 3, 98);
            var data = await _unitOfWork.YbTransferRepo.GetLastRefreshedDate(userDetails.tenant_id, budgetYear);
            if (data == null)
            {
                return null;
            }
            else
            {
                return data.updated;
            }
        }

        public async Task<bool> TransferYBDataFromSubTenant(string userId, int budgetYear, int subTenantId, int mainTenantId, Guid jobId, int orgLevel, BudgetProcessStage processStage, ProcessTransferStep step)
        {
            string lockId = $"{mainTenantId}:{subTenantId}:{budgetYear}:{processStage.ToString()}:{step.ToString()}:{jobId.ToString()}";

            try
            {
                if (_lockManager.IsLocked("TransferYBDataFromSubTenant", lockId))
                {
                    // If locked, Wait for the ongoing Datasync to finish and skip thte current duplicate fetch
                    do
                    {
                        await Task.Delay(1000);
                    } while (_lockManager.IsLocked("TransferYBDataFromSubTenant", lockId));

                    return true;
                }

                _lockManager.GetLock("TransferYBDataFromSubTenant", lockId, new TimeSpan(0, 5, 0));

                var orgversionData = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));
                List<tco_org_hierarchy> lstTcoOrgValues = orgversionData.lstOrgHierarchy;
                var orgId1 = lstTcoOrgValues.First().org_id_1;

                await _dataSyncUtility.SaveSyncBTJobStatus(userId, jobId, step, SyncJobStatus.InProgress);
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                var orgversionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));
                var orgData = await _unitOfWork.BudgetProposalProcessRepository.GetOrgIdsConnToSubTenant(subTenantId, userDetails.tenant_id, orgversionContent.orgVersion);
                List<string> lstOrgId3 = orgData.Select(x => x.orgId).ToList();
                bool isChapterSetup = await _utility.IsTenantChapterSetup(subTenantId);

                List<string> deptCodeList = new();
                if (isChapterSetup)
                {
                    deptCodeList = await _stFpAdmin.GetAttributeConnectedDeptForTenant(subTenantId, lstOrgId3, budgetYear);
                }

                bool isSubTenantLocked = false;
                var parametersData = await _unitOfWork.YbTransferRepo.GetParametersData(subTenantId, budgetYear, "LOCK_ORIGINAL_BUDGET");
                if (parametersData != null)
                {
                    isSubTenantLocked = parametersData.active != 0;
                }
                if (isSubTenantLocked)
                {
                    var dataToTransfer = _unitOfWork.YbTransferRepo.GetYBOriginalDataToTransfer(subTenantId, budgetYear, deptCodeList, isChapterSetup);
                    if (dataToTransfer != null && dataToTransfer.Any())
                    {
                        // validation and error log
                        bool isValid = await YBOriginalDataValidation(userId, dataToTransfer, mainTenantId, subTenantId, budgetYear, jobId, step, processStage);

                        // transfer data from sub to main tenant
                        if (!isValid)
                        {
                            _lockManager.ReleaseLock("TransferYBDataFromSubTenant", lockId);
                            return false;
                        }

                        var subtenantDeptCodes = dataToTransfer.Select(x => new InvestmentPopupDataHelper
                        {
                            departCodeSub = x.department_code,
                        }).ToList();

                        MRSyncFetchtHelper fetchObj = new MRSyncFetchtHelper
                        {
                            targetTenantId = mainTenantId,
                            sourceTenantId = subTenantId
                        };
                        var departData = await _dataSyncUtility.GetMappedDeptCodeForMainTenant(orgversionContent.orgVersion, fetchObj, subtenantDeptCodes, isChapterSetup);
                        string adjCode = await _utility.GenerateAdjustmentCodeForUserAsync(false, userId, orgId1, budgetYear, 1);
                        await _unitOfWork.YbTransferRepo.TransferYBData(mainTenantId, budgetYear, subTenantId, dataToTransfer, userDetails.pk_id, lstOrgId3, orgversionContent.orgVersion, isChapterSetup, departData, adjCode);

                        // transfer run procedure
                        _unitOfWork.YbTransferRepo.ExecuteOrginalBudProcedure(userDetails.pk_id, mainTenantId, budgetYear); // procedure
                    }

                    SyncInputHelper input = new SyncInputHelper
                    {
                        subTenantId = subTenantId,
                        processStage = processStage,
                        budgetYear = budgetYear,
                        budgetPhaseId = Guid.Empty,
                        forecastPeriod = 0,
                        changeId = 0,
                        status = SyncTransferStatus.Transfer,
                        mainTenantId = mainTenantId,
                    };
                    await _dataSyncUtility.SaveSyncTansferHistory(userId, input);
                    await _dataSyncUtility.SaveSyncBTJobStatus(userId, jobId, step, SyncJobStatus.Completed);

                    _lockManager.ReleaseLock("TransferYBDataFromSubTenant", lockId);
                    return true;
                }

                _lockManager.ReleaseLock("TransferYBDataFromSubTenant", lockId);
                return false;
            }
            catch (Exception ex)
            {
                _lockManager.ReleaseLock("TransferYBDataFromSubTenant", lockId);
                List<string> ErrorMessage = new List<string> { JsonConvert.SerializeObject(ex) };
                await _dataSyncUtility.SaveSyncBTJobStatus(userId, jobId, step, SyncJobStatus.Error);
                await _dataSyncUtility.InsertTransferErrorLog(userId, jobId, budgetYear, ProcessTransferStep.Action, ObjectType.Department, ErrorType.Exception, "", subTenantId, processStage, ErrorMessage);
                _logger.LogPublishJobError(ex.ToString() + DateTime.UtcNow);
                return false;
            }
        }

        public async Task<bool> TransferYbApprovedBudActionsFromSubTenantAsync(string userId, int budgetYear, int subTenantId, int mainTenantId, Guid jobId, BudgetProcessStage processStage, ProcessTransferStep step)
        { // new transfer
            try
            {
                await _dataSyncUtility.SaveSyncBTJobStatus(userId, jobId, step, SyncJobStatus.InProgress);
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));
                List<AttributeConnectedDeptHelper> deptCodeData = await _stFpAdmin.GetAllAttributeConnectedDeptForTenant(subTenantId, budgetYear);
                List<string> deptCodeList = deptCodeData.Select(x => x.departmentCode).Distinct().ToList();
                var dataToTransfer = await _unitOfWork.YbTransferRepo.GetTbuTransDetailDataToTransfer(subTenantId, budgetYear, deptCodeList);

                if (dataToTransfer != null && dataToTransfer.Any())
                {
                    var paramCheck = _utility.GetParameterValueAndActiveStatus(userId, "LOCK_ORIGINAL_BUDGET", out int active);
                    bool isValid = !string.IsNullOrEmpty(paramCheck) && active == 1;
                    if (isValid)
                    {
                        bool isChapterSetup = await _utility.IsTenantChapterSetup(subTenantId);
                        List<AttributeDepartmentMap> deptConnectedToTenant = new List<AttributeDepartmentMap>();
                        if (isChapterSetup)
                        {
                            deptConnectedToTenant = await _finUtility.GetAttributeConnectedDeptsByTenantId(userId, subTenantId, budgetYear);
                        }
                        await _unitOfWork.YbTransferRepo.TransferYbApprovedBudActionsFromSubTenantAsync(mainTenantId, budgetYear, subTenantId, dataToTransfer, userDetails.pk_id, isChapterSetup, deptConnectedToTenant, orgVersionContent.orgVersion);
                        await _dataSyncUtility.SaveSyncBTJobStatus(userId, jobId, step, SyncJobStatus.Completed);
                    }
                    else
                    {
                        await _dataSyncUtility.SaveSyncBTJobStatus(userId, jobId, step, SyncJobStatus.Error);
                        return false;
                    }
                }
                SyncInputHelper input = new SyncInputHelper
                {
                    subTenantId = subTenantId,
                    processStage = processStage,
                    budgetYear = budgetYear,
                    budgetPhaseId = Guid.Empty,
                    forecastPeriod = 0,
                    changeId = 0,
                    status = SyncTransferStatus.Transfer,
                    mainTenantId = mainTenantId,
                };
                await _dataSyncUtility.SaveSyncTansferHistory(userId, input);
                await _dataSyncUtility.SaveSyncBTJobStatus(userId, jobId, step, SyncJobStatus.Completed);
                return true;
            }
            catch (Exception)
            {
                await _dataSyncUtility.SaveSyncBTJobStatus(userId, jobId, step, SyncJobStatus.Error);
                return false;
            }
        }

        public async Task<bool> TransferYbApprovedBudAdjActionsToSubTenantAsync(string userId, int budgetYear, int subTenantId, int mainTenantId, Guid jobId, int orgLevel, ProcessTransferStep step, BudgetProcessStage processStage)
        {
            try
            {
                await _dataSyncUtility.SaveSyncBTJobStatus(userId, jobId, step, SyncJobStatus.InProgress);
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));
                var deptCodeList = _unitOfWork.BudgetProposalProcessRepository.GetMainTenantMappedDepartCodes(userDetails.tenant_id, subTenantId, orgVersionContent.orgVersion);
                var dataToTransfer = await _unitOfWork.YbTransferRepo.GetYBActionsDataToTransfer(mainTenantId, subTenantId, budgetYear, orgVersionContent.orgVersion);
                dataToTransfer = dataToTransfer.Where(x => deptCodeList.Contains(x.DepartmentCode)).DistinctBy(x => x.PkActionId).ToList();
                string subTenantOrgVersion = _utility.GetOrgVersionBasedOnTenant(subTenantId, _utility.GetForecastPeriod(budgetYear, 1)).orgVersion;
                var subTenantOrgId = _unitOfWork.BudgetTransferRepository.GetOrgIdForSubTenant(subTenantId, subTenantOrgVersion);
                var subTenantBudChanges = await _unitOfWork.YbTransferRepo.GetSubTenantBudChanges(subTenantId, budgetYear);
                var subTenantIdList = new List<int> { subTenantId };
                TransferBudgetHelper input = new TransferBudgetHelper()
                {
                    budgetPhaseId = Guid.Empty,
                    budgetYear = budgetYear,
                    subTenantId = subTenantIdList,
                    processStage = processStage
                };
                //transfer budget round
                await _dataSyncUtility.SaveSyncBTJobStatus(userId, jobId, ProcessTransferStep.BudgetRound, SyncJobStatus.InProgress);
                await _dataSyncUtility.DeleteTransferErrorLog(userId, jobId, budgetYear, ProcessTransferStep.BudgetRound, ObjectType.ApprovalRef, subTenantId);
                var subChangeId = await _budgetProposalProcess.TransferBudgetRound(userId, input, Guid.Empty, processStage, jobId);
                SyncInputHelper logInput = new SyncInputHelper
                {
                    subTenantId = subTenantId,
                    processStage = processStage,
                    budgetYear = budgetYear,
                    budgetPhaseId = Guid.Empty,
                    forecastPeriod = 0,
                    changeId = subChangeId,
                    status = SyncTransferStatus.Transfer,
                    mainTenantId = mainTenantId,
                };
                if (dataToTransfer != null && dataToTransfer.Any())
                {
                    bool isValid = await _budgetProposalProcess.ActionValidation(userId, jobId, budgetYear, dataToTransfer.Select(x => x.PkActionId).ToList(), subTenantId, processStage, deptCodeList, new ActionTransferQueueHeler());
                    // transfer data from main to sub tenant
                    if (isValid)
                    {
                        var userAdjCodes = await _unitOfWork.YbTransferRepo.GetUserAdjCodes(mainTenantId, dataToTransfer.Select(x => x.AdjCode).ToList(), false);
                        var transferedAdjCodes = await _unitOfWork.YbTransferRepo.GetUserAdjCodes(subTenantId, dataToTransfer.Select(x => x.AdjCode).ToList(), false);
                        var transfAdjList = transferedAdjCodes.Select(x => x.pk_adj_code);
                        var adjCodesToTransf = userAdjCodes.Where(x => !transfAdjList.Contains(x.pk_adj_code)).ToList();
                        if (userAdjCodes.Any())
                        {
                            await TransferUserAdjCodesAsync(userId, budgetYear, subTenantId, subTenantOrgId, adjCodesToTransf, subChangeId);
                        }

                        string id = $"{userDetails.tenant_id}";
                        TimeSpan validity = new TimeSpan(0, 30, 0);

                        while (dataToTransfer.Any())
                        {
                            bool isLocked = _lockManager.IsLocked("SyncActionTransferLock", id);
                            if (isLocked)
                            {
                                Thread.Sleep(20000);
                            }
                            else
                            {
                                try
                                {
                                    bool gotLock = _lockManager.GetLock("SyncActionTransferLock", id, validity);
                                    if (gotLock)
                                    {
                                        foreach (var data in dataToTransfer)
                                        {
                                            List<AccountingInfo> lstExistingData = new List<AccountingInfo>();
                                            if (data.PkActionId == -1) continue;
                                            YbActionDetails details = await _unitOfWork.YbTransferRepo.GetDetailsToTransfer(subTenantId, budgetYear, data);
                                            details.ActionDetails = details.ActionDetails.Where(x => deptCodeList.Contains(x.department_code)).ToList();
                                            int maxValue = await _unitOfWork.BudgetTransferRepository.GetActionIdForSubTenant();
                                            var tcoCompanyData = await _unitOfWork.BudgetProposalProcessRepository.GetMappedMainDeptFromSubAsync(mainTenantId, orgVersionContent.orgVersion, subTenantId);
                                            tfp_trans_header newActionHeader = new tfp_trans_header
                                            {
                                                pk_action_id = maxValue,
                                                fk_tenant_id = subTenantId,
                                                description = details.ActionHeader.description,
                                                consequence = details.ActionHeader.consequence,
                                                start_date = details.ActionHeader.start_date,
                                                action_type = details.ActionHeader.action_type,
                                                action_source = details.ActionHeader.action_source,
                                                line_order = details.ActionHeader.line_order,
                                                isManuallyAdded = details.ActionHeader.isManuallyAdded,
                                                title = details.ActionHeader.title,
                                                fk_area_id = details.ActionHeader.fk_area_id,
                                                tag = details.ActionHeader.tag,
                                                long_description = details.ActionHeader.long_description,
                                                priority = details.ActionHeader.priority,
                                                display_financial_plan_flag = details.ActionHeader.display_financial_plan_flag,
                                                financial_plan_description = string.Empty,
                                                consequence_flag = details.ActionHeader.consequence_flag,
                                                different_external_description_flag = details.ActionHeader.different_external_description_flag,
                                                change_text_flag = details.ActionHeader.change_text_flag,
                                                tags = details.ActionHeader.tags,
                                                monthly_report_flag = details.ActionHeader.monthly_report_flag,
                                                finished_date = details.ActionHeader.finished_date,
                                                display_cab_flag = details.ActionHeader.display_cab_flag,
                                                display_zero_action = details.ActionHeader.display_zero_action,
                                                update_annual_budget = details.ActionHeader.update_annual_budget,
                                                update_annual_budget_next_year = details.ActionHeader.update_annual_budget_next_year,
                                                update_next_year_finplan = details.ActionHeader.update_next_year_finplan,
                                                org_id = subTenantOrgId,
                                                org_level = 1,
                                                log_id = Guid.NewGuid(),
                                                long_description_id = Guid.NewGuid(),
                                                financial_plan_description_id = Guid.NewGuid(),
                                                updated = DateTime.UtcNow,
                                                updated_by = userDetails.pk_id
                                            };
                                            _unitOfWork.GenericRepo.Add(newActionHeader);
                                            await _unitOfWork.CompleteAsync();
                                            List<tfp_trans_detail> list = new();
                                            foreach (var item in details.ActionDetails)
                                            {
                                                var subTenantDeptCode = tcoCompanyData.FirstOrDefault(x => x.default_department_main == item.department_code);
                                                tfp_trans_detail newActionDetail = new tfp_trans_detail
                                                {
                                                    fk_action_id = maxValue,
                                                    fk_tenant_id = subTenantId,
                                                    budget_year = budgetYear,
                                                    fk_account_code = item.fk_account_code,
                                                    department_code = subTenantDeptCode != null && subTenantDeptCode.default_department_sub != null ? subTenantDeptCode.default_department_sub : string.Empty,
                                                    function_code = item.function_code,
                                                    project_code = item.project_code,
                                                    asset_code = item.asset_code,
                                                    fk_investment_id = 0,
                                                    year_1_amount = item.year_1_amount,
                                                    year_2_amount = item.year_2_amount,
                                                    year_3_amount = item.year_3_amount,
                                                    year_4_amount = item.year_4_amount,
                                                    year_5_amount = item.year_5_amount,
                                                    year_6_amount = item.year_6_amount,
                                                    year_7_amount = item.year_7_amount,
                                                    year_8_amount = item.year_8_amount,
                                                    year_9_amount = item.year_9_amount,
                                                    year_10_amount = item.year_10_amount,
                                                    fk_change_id = subChangeId,
                                                    free_dim_1 = item.free_dim_1,
                                                    free_dim_2 = item.free_dim_2,
                                                    free_dim_3 = item.free_dim_3,
                                                    free_dim_4 = item.free_dim_4,
                                                    description = item.description,
                                                    fk_adjustment_code = item.fk_adjustment_code,
                                                    fk_alter_code = item.fk_alter_code,
                                                    fk_main_project_code = string.Empty,
                                                    fk_adj_code = item.fk_adj_code,
                                                    updated = DateTime.UtcNow,
                                                    updated_by = userDetails.pk_id,
                                                    fk_key_id = 1
                                                };
                                                _unitOfWork.GenericRepo.Add(newActionDetail);
                                                list.Add(newActionDetail);
                                            }
                                            await TransferTbuActionDataAsync(userId, subTenantId, budgetYear, list);
                                            tfp_sync_actions syncAction = new tfp_sync_actions
                                            {
                                                main_fk_tenant_id = mainTenantId,
                                                main_fk_action_id = data.PkActionId,
                                                sub_fk_action_id = maxValue,
                                                sub_fk_tenant_id = subTenantId,
                                                process_stage = processStage.ToString(),
                                                updated = DateTime.UtcNow,
                                                updated_by = userDetails.pk_id
                                            };
                                            _unitOfWork.GenericRepo.Add(syncAction);
                                            await _unitOfWork.CompleteAsync();
                                        }
                                        await _dataSyncUtility.SaveSyncBTJobStatus(userId, jobId, step, SyncJobStatus.Completed);
                                        _lockManager.ReleaseLock("SyncActionTransferLock", id);
                                        await _dataSyncUtility.SaveSyncTansferHistory(userId, logInput);
                                        return true;
                                        //break;
                                    }
                                }
                                catch (Exception ex)
                                {
                                    List<string> ErrorMessage = new List<string> { JsonConvert.SerializeObject(ex) };
                                    await _dataSyncUtility.InsertTransferErrorLog(userId, jobId, budgetYear, step, ObjectType.NotApplicable, ErrorType.Exception, "", subTenantId, processStage, ErrorMessage);
                                    await _dataSyncUtility.SaveSyncBTJobStatus(userId, jobId, step, SyncJobStatus.Error);
                                    _lockManager.ReleaseLock("SyncActionTransferLock", id);
                                    return false;
                                }
                            }
                        }
                    }
                    else
                        await _dataSyncUtility.SaveSyncBTJobStatus(userId, jobId, step, SyncJobStatus.Error);
                    return false;
                }
                await _dataSyncUtility.SaveSyncTansferHistory(userId, logInput);
                return true;
            }
            catch (Exception ex)
            {
                List<string> ErrorMessage = new List<string> { JsonConvert.SerializeObject(ex) };
                await _dataSyncUtility.InsertTransferErrorLog(userId, jobId, budgetYear, step, ObjectType.NotApplicable, ErrorType.Exception, "", subTenantId, processStage, ErrorMessage);
                await _dataSyncUtility.SaveSyncBTJobStatus(userId, jobId, step, SyncJobStatus.Error);
                return false;
            }
        }

        public async Task ExecuteSyncWarehouseYB(string userId, int budgetYear)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);

                _unitOfWork.YbTransferRepo.ExecuteOrginalBudProcedure(userDetails.pk_id, userDetails.tenant_id, budgetYear);
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<bool> UnlockOriginalBudget(int subTenantId, int budgetYear, string userId)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                var orgVersionContent = _utility.GetOrgVersionBasedOnTenant(subTenantId, _utility.GetForecastPeriod(budgetYear, 1));

                await _unitOfWork.YbTransferRepo.DeleteApplicationFlagData(subTenantId, budgetYear, "LOCK_ORIGINAL_BUDGET"); // delete from tcoapplication flag

                var parametersData = await _unitOfWork.YbTransferRepo.GetParametersData(subTenantId, budgetYear, "LOCK_ORIGINAL_BUDGET"); // update tco parameters
                if (parametersData != null)
                {
                    parametersData.active = 0;
                    parametersData.updated = DateTime.UtcNow;
                    parametersData.updated_by = userDetails.pk_id;
                    await _unitOfWork.CompleteAsync();
                }
                var adjCodes = await _unitOfWork.YbTransferRepo.GetAdjustmentCodeData(subTenantId, budgetYear, orgVersionContent.orgVersion); //update user adj codes
                var userAdjCodes = await _unitOfWork.YbTransferRepo.GetUserAdjustmentCodeData(subTenantId, adjCodes);
                if (userAdjCodes.Any())
                {
                    foreach (var userAdjCode in userAdjCodes)
                    {
                        userAdjCode.is_original_flag = true;
                        userAdjCode.updated = DateTime.UtcNow;
                    }
                    await _unitOfWork.CompleteAsync();
                }

                SyncInputHelper input = new SyncInputHelper
                {
                    subTenantId = subTenantId,
                    processStage = BudgetProcessStage.YB_OriginalBudget,
                    budgetYear = budgetYear,
                    budgetPhaseId = Guid.Empty,
                    forecastPeriod = 0,
                    changeId = 0,
                    status = SyncTransferStatus.UnLockBudget,
                    mainTenantId = userDetails.tenant_id
                };
                await _dataSyncUtility.SaveSyncTansferHistory(userId, input);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<List<YBSyncOriginalBudgetColHelper>> GetSyncDataMainYearlyDetailColumn(string userId, int budgetYear, string orgId, int orgLevel)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var orgVersionContent = _utility.GetOrgVersionBasedOnTenant(userDetails.tenant_id, _utility.GetForecastPeriod(budgetYear, 1));
            var orgVersion = orgVersionContent.orgVersion;
            var orgIds = await _unitOfWork.BudgetProposalProcessRepository.getSubOrgId(userDetails.tenant_id, orgId, orgLevel, orgVersion);
            List<int> subTenantLst = await _unitOfWork.BudgetProposalProcessRepository.GetMainTenantConnectedSubTenantInfo(userDetails.tenant_id, orgVersion, orgIds);
            var gridData = await _unitOfWork.YbTransferRepo.OriginalBudData(userDetails.tenant_id, budgetYear, subTenantLst, orgVersion);
            var mainYearlyDetailData = (from a in gridData
                                        orderby a.SubTenant
                                        select new YBSyncOriginalBudgetColHelper
                                        {
                                            subTenantId = a.SubTenantId,
                                            mainDetailedYb = a.MainDetailedYb,
                                        }).ToList();
            return mainYearlyDetailData;
        }

        public async Task<YBTransDetailDropdownHelper> GetTransDetailGridData(string userId, int budgetYear, List<AdjustmentDataHelper> adjustmentsListInfo, string orgId, int orgLevel)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var orgVersionContent = _utility.GetOrgVersionBasedOnTenant(userDetails.tenant_id, _utility.GetForecastPeriod(budgetYear, 1));
            var orgVersion = orgVersionContent.orgVersion;
            var orgIds = await _unitOfWork.BudgetProposalProcessRepository.getSubOrgId(userDetails.tenant_id, orgId, orgLevel, orgVersion);
            var gridData = await _unitOfWork.YbTransferRepo.GetTransDetailData(userDetails.tenant_id, budgetYear, userDetails.client_id, orgIds, orgVersion);
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetProposalProcess");
            YBTransDetailDropdownHelper result = new();
            foreach (var item in gridData)
            {
                var currSubTenantInfo = adjustmentsListInfo.Where(x => x.subTenantId == item.subTenantId).ToList();
                int adjustmentCodeTotalCount = currSubTenantInfo.GroupBy(x => new { x.useradjustmentCode, x.projectCode }).Count();
                item.showDeviationIcon = adjustmentCodeTotalCount > 0 ? true : false;
                if (!string.IsNullOrEmpty(item.transferStatus))
                {
                    switch (Enum.Parse(typeof(SyncJobStatus), item.transferStatus))
                    {
                        case SyncJobStatus.NotStarted:
                            item.transferStatus = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_notStarted", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                            item.transferStatusValue = new KeyValueHelperData
                            {
                                key = Convert.ToInt32(SyncJobStatus.NotStarted),
                                value = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_notStarted", StringComparison.InvariantCultureIgnoreCase)).Value.LangText
                            };
                            item.updated = null;
                            item.updatedBy = string.Empty;
                            break;

                        case SyncJobStatus.InProgress:
                            item.transferStatus = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_inProgress", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                            item.transferStatusValue = new KeyValueHelperData
                            {
                                key = Convert.ToInt32(SyncJobStatus.InProgress),
                                value = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_inProgress", StringComparison.InvariantCultureIgnoreCase)).Value.LangText
                            };
                            break;

                        case SyncJobStatus.Completed:
                            item.transferStatus = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_completed", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                            item.transferStatusValue = new KeyValueHelperData
                            {
                                key = Convert.ToInt32(SyncJobStatus.Completed),
                                value = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_completed", StringComparison.InvariantCultureIgnoreCase)).Value.LangText
                            };
                            break;

                        case SyncJobStatus.Error:
                            item.transferStatus = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_error", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                            item.transferStatusValue = new KeyValueHelperData
                            {
                                key = Convert.ToInt32(SyncJobStatus.Error),
                                value = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_error", StringComparison.InvariantCultureIgnoreCase)).Value.LangText
                            };
                            break;

                        default:
                            item.transferStatus = string.Empty;
                            item.transferStatusValue = new KeyValueHelperData
                            {
                                key = Convert.ToInt32(SyncJobStatus.NotStarted),
                                value = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_notStarted", StringComparison.InvariantCultureIgnoreCase)).Value.LangText
                            };
                            break;
                    }
                }
                else
                {
                    item.transferStatus = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_notStarted", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                    item.transferStatusValue = new KeyValueHelperData
                    {
                        key = Convert.ToInt32(SyncJobStatus.NotStarted),
                        value = langStrings.FirstOrDefault(x => x.Key.Equals("BP_transferStatus_notStarted", StringComparison.InvariantCultureIgnoreCase)).Value.LangText
                    };
                    item.updated = null;
                    item.updatedBy = string.Empty;
                }
            }
            result.data = gridData;
            result.TenantNameList = gridData.Select(x => new KeyValueHelperData { key = x.subTenantId, value = x.subTenant }).ToList();
            result.UpdatedDropdownList = gridData.Select(x => new KeyValueHelperData { key = x.subTenantId, value = x.updatedBy }).ToList();
            return result;
        }

        #region Private

        private async Task<bool> YBOriginalDataValidation(string userId, IQueryable<tbu_trans_detail_original> dataToTransfer, int mainTenantId, int subTenantId, int budgetYear, Guid jobId, ProcessTransferStep step, BudgetProcessStage processStage)
        {
            var aysncFunctionCodeData = _unitOfWork.BudgetProposalProcessRepository.GetFunctionData(mainTenantId, budgetYear);
            var asyncAccountCodeData = _unitOfWork.BudgetProposalProcessRepository.GetAccountData(mainTenantId, budgetYear);

            await Task.WhenAll(aysncFunctionCodeData, asyncAccountCodeData);
            var functionCodeData = aysncFunctionCodeData.Result;
            var accountCodeData = asyncAccountCodeData.Result;

            ProcessTransferCodesHelper subTransferTypeCode = new ProcessTransferCodesHelper();
            subTransferTypeCode.functionCodes = dataToTransfer.Where(x => x.fk_function_code != " " && !string.IsNullOrEmpty(x.fk_function_code) && x.fk_function_code != "999").Select(x => x.fk_function_code).Distinct().ToList();
            subTransferTypeCode.accountCodes = dataToTransfer.Where(x => x.fk_account_code != " " && !string.IsNullOrEmpty(x.fk_account_code)).Select(x => x.fk_account_code).Distinct().ToList();

            bool isCodeValid = true;

            ProcessTransferCodesHelper mainTransferTypeCode = new ProcessTransferCodesHelper();
            mainTransferTypeCode.functionCodes = functionCodeData.Where(x => subTransferTypeCode.functionCodes.Contains(x)).ToList();
            mainTransferTypeCode.accountCodes = accountCodeData.Where(x => subTransferTypeCode.accountCodes.Contains(x)).ToList();

            await _dataSyncUtility.DeleteTransferErrorLog(userId, jobId, budgetYear, step, ObjectType.Function, subTenantId);
            await _dataSyncUtility.DeleteTransferErrorLog(userId, jobId, budgetYear, step, ObjectType.Account, subTenantId);

            if (mainTransferTypeCode != null)
            {
                if (mainTransferTypeCode.functionCodes.Count < subTransferTypeCode.functionCodes.Count)
                {
                    isCodeValid = false;
                    subTransferTypeCode.functionCodes.RemoveAll(x => mainTransferTypeCode.functionCodes.Contains(x));
                    await _dataSyncUtility.InsertTransferErrorLog(userId, jobId, budgetYear, step, ObjectType.Function, ErrorType.NotFound, string.Empty, subTenantId, processStage, subTransferTypeCode.functionCodes);
                }

                if (mainTransferTypeCode.accountCodes.Count < subTransferTypeCode.accountCodes.Count)
                {
                    isCodeValid = false;
                    subTransferTypeCode.accountCodes.RemoveAll(x => mainTransferTypeCode.accountCodes.Contains(x));
                    await _dataSyncUtility.InsertTransferErrorLog(userId, jobId, budgetYear, step, ObjectType.Account, ErrorType.NotFound, string.Empty, subTenantId, processStage, subTransferTypeCode.accountCodes);
                }
            }
            else
                return false;

            return isCodeValid;
        }

        private async Task TransferTbuActionDataAsync(string userId, int subTenantId, int budgetYear, List<tfp_trans_detail> newActionDetail)
        {
            try
            {
                foreach (var item in newActionDetail)
                {
                    await DoAllocation(userId, subTenantId, budgetYear, item);
                }
            }
            catch (Exception ex)
            {
            }
        }

        private async Task DoAllocation(string userId, int subTenantId, int budgetYear, tfp_trans_detail newActionDetail)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            int allocationValue = 1;

            var periods = await dbContext.tco_periodic_key
                .Where(p => (p.fk_tenant_id == subTenantId || p.fk_tenant_id == 0) && p.key_id == allocationValue)
                .Select(p => new { p.period, p.allocation_pct })
                .ToListAsync();

            var period = await _unitOfWork.BudgetProposalProcessRepository.GetAllPeriodicKey(subTenantId, allocationValue);
            var allocationPeriod = period.Select(p => new { p.period, p.allocation_pct });
            if (!allocationPeriod.Any())
            {
                return; // Exit if no periods found
            }

            PeriodicCalcHelper pc = new()
            {
                TotalAmount = Convert.ToInt64(newActionDetail.year_1_amount)
            };

            foreach (var p in allocationPeriod)
            {
                pc.PeriodNAllocation.Add(p.period, p.allocation_pct);
            }

            IEnumerable<PeriodicCalcResult> pcResult = _utility.CalculatePeriodicAllocation(pc, true);
            var buTransId = Guid.NewGuid();

            List<tbu_trans_detail> lstDataToInsert = new List<tbu_trans_detail>();

            if (pc.TotalAmount != 0)
            {
                foreach (var pr in pcResult)
                {
                    int periodToInsert = pr.Period;
                    tbu_trans_detail tbuTransDetail = new tbu_trans_detail
                    {
                        pk_id = Guid.NewGuid(),
                        bu_trans_id = buTransId,
                        fk_tenant_id = subTenantId,
                        action_type = 60,
                        line_order = 0,
                        fk_account_code = newActionDetail.fk_account_code,
                        department_code = newActionDetail.department_code,
                        fk_function_code = newActionDetail.function_code,
                        fk_project_code = newActionDetail.project_code,
                        free_dim_1 = newActionDetail.free_dim_1,
                        free_dim_2 = newActionDetail.free_dim_2,
                        free_dim_3 = newActionDetail.free_dim_3,
                        free_dim_4 = newActionDetail.free_dim_4,
                        resource_id = string.Empty,
                        fk_employment_id = 0,
                        description = string.IsNullOrEmpty(newActionDetail.description) ? string.Empty : newActionDetail.description,
                        budget_year = budgetYear,
                        period = budgetYear * 100 + periodToInsert,
                        budget_type = 1,
                        amount_year_1 = pr.AllocatedAmount,
                        amount_year_2 = 0,
                        amount_year_3 = 0,
                        amount_year_4 = 0,
                        fk_key_id = allocationValue,
                        updated = DateTime.UtcNow,
                        updated_by = userDetails.pk_id,
                        allocation_pct = pr.AllocationPercent,
                        total_amount = pc.TotalAmount,
                        tax_flag = 0,
                        holiday_flag = 0,
                        fk_pension_type = string.Empty,
                        fk_action_id = newActionDetail.fk_action_id,
                        fk_alter_code = newActionDetail.fk_alter_code,
                        fk_adjustment_code = newActionDetail.fk_adj_code
                    };
                    lstDataToInsert.Add(tbuTransDetail);
                }

                if (lstDataToInsert.Any())
                {
                    var totalSum = lstDataToInsert.Sum(y => y.amount_year_1);
                    foreach (var item in lstDataToInsert.OrderBy(x => x.period))
                    {
                        item.total_amount = totalSum;
                        item.allocation_pct = totalSum != 0 ? (item.amount_year_1 * 100m) / totalSum : 0;
                    }

                    if (lstDataToInsert.Any())
                    {
                        List<List<tbu_trans_detail>> splittedListOfLstTbuTransDetail = _consequenceAdjustedBudget.SplitEnumerableList(lstDataToInsert, 500);
                        foreach (var tbuTransDetail in splittedListOfLstTbuTransDetail)
                        {
                            if (tbuTransDetail.Any())
                            {
                                await dbContext.BulkInsertAsync(tbuTransDetail);
                            }
                        }
                    }
                }
            }
        }

        public async Task<bool> TransferYBApprovedBudAdjustmentInvestmentsAsync(string userId, int budgetYear, int subTenantId, int mainTenantId, Guid jobId, int orgLevel, BudgetProcessStage processStage, ProcessTransferStep transferType)
        {
            try
            {
                await _dataSyncUtility.SaveSyncBTJobStatus(userId, jobId, transferType, SyncJobStatus.InProgress);
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                var orgversionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));
                string subTenantOrgVersion = _utility.GetOrgVersionBasedOnTenant(subTenantId, _utility.GetForecastPeriod(budgetYear, 1)).orgVersion;
                List<tco_org_hierarchy> lstTcoOrgValues = orgversionContent.lstOrgHierarchy;
                var subTenantOrgId = _unitOfWork.BudgetTransferRepository.GetOrgIdForSubTenant(subTenantId, subTenantOrgVersion);
                var dataToTransfer = await _unitOfWork.YbTransferRepo.GetYBInvestmentDataToTransfer(budgetYear, mainTenantId, orgversionContent.orgVersion, processStage, subTenantId);
                if (dataToTransfer.Any())
                {
                    var dataForValidation = GetDataForValidationYBInvestment(dataToTransfer);
                    var mainProjCodes = dataForValidation.Select(x => x.mainProjectCode).Distinct().ToList();
                    var changeIds = dataToTransfer.Select(x => x.changeId).Distinct().ToList();
                    var userAdjCodesList = dataToTransfer.Select(x => x.userAdjCode).Distinct().ToList();
                    //clearing previous error log
                    await _dataSyncUtility.DeleteTransferErrorLog(userId, jobId, budgetYear, transferType, ObjectType.Function, subTenantId);
                    await _dataSyncUtility.DeleteTransferErrorLog(userId, jobId, budgetYear, transferType, ObjectType.Account, subTenantId);
                    await _dataSyncUtility.DeleteTransferErrorLog(userId, jobId, budgetYear, transferType, ObjectType.AlertCode, subTenantId);
                    int targetTenantId = subTenantId; //InvestmentsValidation is applicable for both transfers from main to sub and sub to main
                    bool isValid = await _dataSyncUtility.InvestmentsValidation(userId, jobId, budgetYear, subTenantId, processStage, mainProjCodes, transferType, dataForValidation, targetTenantId);

                    int subChangeId = 0;

                    SyncInputHelper logInput = new SyncInputHelper
                    {
                        subTenantId = subTenantId,
                        processStage = processStage,
                        budgetYear = budgetYear,
                        budgetPhaseId = Guid.Empty,
                        forecastPeriod = 0,
                        changeId = subChangeId,
                        status = SyncTransferStatus.Transfer,
                        mainTenantId = mainTenantId,
                    };

                    if (isValid)
                    {
                        subChangeId = await TransferBudgetRoundAsync(userId, jobId, logInput);
                        isValid = subChangeId != -1;
                    }

                    if (isValid)
                    {
                        var userAdjCodes = await _unitOfWork.YbTransferRepo.GetUserAdjCodes(mainTenantId, userAdjCodesList, true);
                        await TransferUserAdjCodesAsync(userId, budgetYear, subTenantId, subTenantOrgId, userAdjCodes, subChangeId);
                        var invToTransfer = dataToTransfer.Select(x => new InvestmentPopupDataHelper
                        {
                            departCode = x.departCode,
                            mainProjCode = x.mainProjCode
                        }).ToList();
                        ActionTransferQueueHeler connectedEleHelper = new ActionTransferQueueHeler
                        {
                            isGoalSelected = false,
                            isTagSelected = false,
                            subTenantIds = new List<int>()
                        };
                        List<string> orgIdsSubTenant = await _unitOfWork.BudgetProposalProcessRepository.getOrgIdsBasedOnSubTenant(mainTenantId, subTenantId, orgversionContent.orgVersion);
                        List<TransferInvHelper> owningDepartments = _unitOfWork.BudgetTransferInvestmentRepository.GetDepartmentCodeReplacementbyOrgId(invToTransfer, mainTenantId, budgetYear, orgversionContent.orgVersion, subTenantId, lstTcoOrgValues, orgIdsSubTenant);
                        var isTransfer = await _budgetTransInv.TransferInvestmentsNew(userId, userDetails.tenant_id, subTenantId, processStage.ToString(), budgetYear, jobId, mainProjCodes, subChangeId, owningDepartments, changeIds, new List<tfp_sync_goals>(), new List<tfp_sync_targets>(), new List<tfp_sync_tags>(), connectedEleHelper, new List<tfp_sync_strategy>(), transferType, false);
                        if (isTransfer)
                        {
                            // await _budgetTransInv.LockNCloseBudgetRounds(changeIds, userDetails.pk_id, mainTenantId, processStage); locking budgetround is remvoed as part of the bug #163571
                            await _budgetTransInv.LogSyncProjBudgetRounds(changeIds, userDetails.pk_id, mainTenantId, subTenantId, budgetYear, processStage.ToString());
                            await _dataSyncUtility.SaveSyncTansferHistory(userId, logInput);
                            await _dataSyncUtility.SaveSyncBTJobStatus(userId, jobId, transferType, SyncJobStatus.Completed);
                            return true;
                        }
                        else
                        {
                            await _dataSyncUtility.SaveSyncBTJobStatus(userId, jobId, transferType, SyncJobStatus.Error);
                            return false;
                        }
                    }
                    else
                    {
                        await _dataSyncUtility.SaveSyncBTJobStatus(userId, jobId, transferType, SyncJobStatus.Error);
                        return false;
                    }
                }
                else
                {
                    await _dataSyncUtility.SaveSyncBTJobStatus(userId, jobId, transferType, SyncJobStatus.Completed);
                    return true;
                }
            }
            catch (Exception)
            {
                await _dataSyncUtility.SaveSyncBTJobStatus(userId, jobId, transferType, SyncJobStatus.Error);
                return false;
            }
        }

        public async Task<bool> GetYbInvestmentsToTransfer(string userId, int budgetYear, int subTenantId, int mainTenantId, string orgId3, Guid jobId, int orgLevel, BudgetProcessStage processStage)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                var orgversionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));
                var dataToTransfer = await _unitOfWork.YbTransferRepo.GetYBInvestmentDataToTransfer(budgetYear, mainTenantId, orgversionContent.orgVersion, processStage);

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<List<AdjustmentDataHelper>> GetYBInvestmentPopUpInfo(string userId, int budgetYear, BudgetProcessStage processStage, int subTenantId = 0)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var orgversionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));
            var dataToTransfer = await _unitOfWork.YbTransferRepo.GetYBInvestmentDataToTransfer(budgetYear, userDetails.tenant_id, orgversionContent.orgVersion, processStage, subTenantId);
            List<AdjustmentDataHelper> popupData = (from dt in dataToTransfer
                                                    group dt by new
                                                    {
                                                        dt.subTenantId,
                                                        dt.userAdjCode,
                                                        dt.changeId,
                                                        dt.budgetRoundName,
                                                        dt.mainProjCode,
                                                        dt.mainProjCodeName,
                                                        dt.accountCode,
                                                        dt.accountCodeName,
                                                        dt.departCode,
                                                        dt.functionCode,
                                                        dt.updatedBy,
                                                        dt.caseNumber,
                                                        dt.longDesc,
                                                        dt.budAuthCode,
                                                        dt.twinCode
                                                    } into res
                                                    select new AdjustmentDataHelper
                                                    {
                                                        subTenantId = res.Key.subTenantId,
                                                        useradjustmentCode = res.Key.userAdjCode,
                                                        budgetAuthorityCode = res.Key.budAuthCode,
                                                        budgetRoundName = res.Key.budgetRoundName,
                                                        caseNumber = res.Key.caseNumber,
                                                        investmentName = res.Key.mainProjCodeName,
                                                        longDescription = res.Key.longDesc,
                                                        accountCode = res.Key.accountCode,
                                                        accountName = res.Key.accountCodeName,
                                                        projectCode = res.Key.mainProjCode,
                                                        year1Sum = (decimal)res.Sum(x => x.year1Sum),
                                                        year2Sum = (decimal)res.Sum(x => x.year2Sum),
                                                        year3Sum = (decimal)res.Sum(x => x.year3Sum),
                                                        year4Sum = (decimal)res.Sum(x => x.year4Sum),
                                                        updated = res.Max(z => z.updated),
                                                        updatedBy = res.Key.updatedBy,
                                                        approvedCost = (decimal)res.Sum(x => x.approvedCost),
                                                        costEstimated = (decimal)res.Sum(x => x.costEstimated),
                                                        twinCode = res.Key.twinCode
                                                    }).ToList();
            return popupData;
        }

        private List<InvestmentDetailHelper> GetDataForValidationYBInvestment(IEnumerable<YBInvestmentPopupDataHelper> dataToTransfer)
        {
            var data = new List<InvestmentDetailHelper>();
            data = dataToTransfer.Select(x => new InvestmentDetailHelper()
            {
                functionCode = x.functionCode,
                departmentCode = x.departCode,
                accountCode = x.accountCode,
                alterCode = String.Empty,
                mainProjectCode = x.mainProjCode
            }).ToList();
            return data;
        }

        public async Task<bool> TransferUserAdjCodesAsync(string userId, int budgetYear, int subTenantId, string orgValue, List<tco_user_adjustment_codes> userAdjCodes, int subTenantChangeId)
        {
            try
            {
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                var data = System.Text.Json.JsonSerializer.Serialize(userAdjCodes);
                List<tco_user_adjustment_codes>? newList = JsonConvert.DeserializeObject<List<tco_user_adjustment_codes>>(data);

                if (newList != null)
                {
                    for (int i = 0; i < newList.Count; i++)
                    {
                        var uac = newList[i];
                        uac.fk_tenant_id = subTenantId;
                        uac.org_level = 1;
                        uac.org_level_value = orgValue;
                        uac.description_id = Guid.NewGuid();
                        uac.budget_year = budgetYear;
                        uac.fk_change_id = subTenantChangeId;

                        _unitOfWork.GenericRepo.Add(uac);

                        //Transfer attachments
                        var attachHelper = new SyncAttachmentHelper
                        {
                            targetTenantId = subTenantId,
                            targetParentId = uac.pk_adj_code,
                            budgetYear = budgetYear,
                            sourceParentId = uac.pk_adj_code,
                            sourceTenantId = userDetails.tenant_id,
                            userId = userDetails.user_name,
                            moduleId = Modules.YEARLYBUDGET,
                            pageId = PageId.YearlyBudgetBudgetChanges
                        };
                        await _dataSyncUtility.TransferSyncAttachment(attachHelper);
                    }
                }
                await _unitOfWork.CompleteAsync();

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public int TransferBudgetRoundToSubTenant(string userId, int budgetYear, int subTenantId, int mainTenantId, BudgetProcessStage processStage, Guid jobId)
        {
            try
            {
                int changeBudgetId = 0;
                var subTenantIdList = new List<int>();
                subTenantIdList.Add(subTenantId);
                TransferBudgetHelper input = new TransferBudgetHelper()
                {
                    budgetPhaseId = Guid.Empty,
                    budgetYear = budgetYear,
                    subTenantId = subTenantIdList,
                    processStage = processStage
                };
                changeBudgetId = _budgetProposalProcess.TransferBudgetRound(userId, input, Guid.Empty, processStage, jobId).GetAwaiter().GetResult();
                return changeBudgetId;
            }
            catch (Exception)
            {
                return -1;
            }
        }

        public async Task<int> TransferBudgetRoundAsync(string userId, Guid jobId, SyncInputHelper logInput)
        {
            int subChangeId = 0;
            var subTenantIdList = new List<int> { logInput.subTenantId };
            TransferBudgetHelper input = new TransferBudgetHelper
            {
                budgetPhaseId = Guid.Empty,
                budgetYear = logInput.budgetYear,
                subTenantId = subTenantIdList,
                processStage = logInput.processStage
            };
            // Transfer budget round
            await _dataSyncUtility.SaveSyncBTJobStatus(userId, jobId, ProcessTransferStep.BudgetRound, SyncJobStatus.InProgress);
            await _dataSyncUtility.DeleteTransferErrorLog(userId, jobId, logInput.budgetYear, ProcessTransferStep.BudgetRound, ObjectType.ApprovalRef, logInput.subTenantId);
            subChangeId = await _budgetProposalProcess.TransferBudgetRound(userId, input, Guid.Empty, logInput.processStage, jobId);

            await _dataSyncUtility.SaveSyncBTJobStatus(userId, jobId, ProcessTransferStep.BudgetRound, subChangeId == -1 ? SyncJobStatus.Error : SyncJobStatus.Completed);
            return subChangeId;
        }
    }

    #endregion Private
}