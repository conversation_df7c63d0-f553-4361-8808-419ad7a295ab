<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31" elementFormDefault="qualified" targetNamespace="http://rep.geointegrasjon.no/Arkiv/Kjerne/xml.schema/2012.01.31" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://si360cloud.westeurope.cloudapp.azure.com:8088/SI.WS.Core/Integration/GI11.svc?xsd=xsd3" namespace="http://rep.geointegrasjon.no/Arkiv/Felles/xml.schema/2012.01.31" />
  <xs:import schemaLocation="http://si360cloud.westeurope.cloudapp.azure.com:8088/SI.WS.Core/Integration/GI11.svc?xsd=xsd6" namespace="http://rep.geointegrasjon.no/Felles/Kodeliste/xml.schema/2012.01.31" />
  <xs:import schemaLocation="http://si360cloud.westeurope.cloudapp.azure.com:8088/SI.WS.Core/Integration/GI11.svc?xsd=xsd9" namespace="http://rep.geointegrasjon.no/Felles/Kontakt/xml.schema/2012.01.31" />
  <xs:import schemaLocation="http://si360cloud.westeurope.cloudapp.azure.com:8088/SI.WS.Core/Integration/GI11.svc?xsd=xsd13" namespace="http://rep.geointegrasjon.no/Matrikkel/Felles/xml.schema/2012.01.31" />
  <xs:import schemaLocation="http://si360cloud.westeurope.cloudapp.azure.com:8088/SI.WS.Core/Integration/GI11.svc?xsd=xsd5" namespace="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31" />
  <xs:import schemaLocation="http://si360cloud.westeurope.cloudapp.azure.com:8088/SI.WS.Core/Integration/GI11.svc?xsd=xsd12" namespace="http://rep.geointegrasjon.no/Plan/Felles/xml.schema/2012.01.31" />
  <xs:complexType name="SakSystemId">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q1="http://rep.geointegrasjon.no/Arkiv/Felles/xml.schema/2012.01.31" base="q1:Saksnoekkel">
        <xs:sequence>
          <xs:element name="systemID" nillable="true" type="tns:SystemID">
            <xs:annotation>
              <xs:appinfo>
                <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="SakSystemId" nillable="true" type="tns:SakSystemId" />
  <xs:complexType name="SystemID">
    <xs:sequence>
      <xs:element name="id" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SystemID" nillable="true" type="tns:SystemID" />
  <xs:complexType name="SakEksternNoekkel">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q2="http://rep.geointegrasjon.no/Arkiv/Felles/xml.schema/2012.01.31" base="q2:Saksnoekkel">
        <xs:sequence>
          <xs:element name="eksternnoekkel" nillable="true" type="tns:EksternNoekkel">
            <xs:annotation>
              <xs:appinfo>
                <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="SakEksternNoekkel" nillable="true" type="tns:SakEksternNoekkel" />
  <xs:complexType name="EksternNoekkel">
    <xs:sequence>
      <xs:element name="fagsystem" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="noekkel" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="EksternNoekkel" nillable="true" type="tns:EksternNoekkel" />
  <xs:complexType name="Forsendelsesmaate">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q3="http://rep.geointegrasjon.no/Felles/Kodeliste/xml.schema/2012.01.31" base="q3:Kode">
        <xs:sequence />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Forsendelsesmaate" nillable="true" type="tns:Forsendelsesmaate" />
  <xs:complexType name="Dokumentmedium">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q4="http://rep.geointegrasjon.no/Felles/Kodeliste/xml.schema/2012.01.31" base="q4:Kode">
        <xs:sequence />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Dokumentmedium" nillable="true" type="tns:Dokumentmedium" />
  <xs:complexType name="Journalstatus">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q5="http://rep.geointegrasjon.no/Felles/Kodeliste/xml.schema/2012.01.31" base="q5:Kode">
        <xs:sequence />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Journalstatus" nillable="true" type="tns:Journalstatus" />
  <xs:complexType name="Tilgangsrestriksjon">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q6="http://rep.geointegrasjon.no/Felles/Kodeliste/xml.schema/2012.01.31" base="q6:Kode">
        <xs:sequence />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Tilgangsrestriksjon" nillable="true" type="tns:Tilgangsrestriksjon" />
  <xs:complexType name="Arkivdel">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q7="http://rep.geointegrasjon.no/Felles/Kodeliste/xml.schema/2012.01.31" base="q7:Kode">
        <xs:sequence />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Arkivdel" nillable="true" type="tns:Arkivdel" />
  <xs:complexType name="Avskrivningsmaate">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q8="http://rep.geointegrasjon.no/Felles/Kodeliste/xml.schema/2012.01.31" base="q8:Kode">
        <xs:sequence />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Avskrivningsmaate" nillable="true" type="tns:Avskrivningsmaate" />
  <xs:complexType name="Informasjonstype">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q9="http://rep.geointegrasjon.no/Felles/Kodeliste/xml.schema/2012.01.31" base="q9:Kode">
        <xs:sequence />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Informasjonstype" nillable="true" type="tns:Informasjonstype" />
  <xs:complexType name="Journalenhet">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q10="http://rep.geointegrasjon.no/Felles/Kodeliste/xml.schema/2012.01.31" base="q10:Kode">
        <xs:sequence />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Journalenhet" nillable="true" type="tns:Journalenhet" />
  <xs:complexType name="Journalposttype">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q11="http://rep.geointegrasjon.no/Felles/Kodeliste/xml.schema/2012.01.31" base="q11:Kode">
        <xs:sequence />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Journalposttype" nillable="true" type="tns:Journalposttype" />
  <xs:complexType name="SakspartRolle">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q12="http://rep.geointegrasjon.no/Felles/Kodeliste/xml.schema/2012.01.31" base="q12:Kode">
        <xs:sequence />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="SakspartRolle" nillable="true" type="tns:SakspartRolle" />
  <xs:complexType name="Kassasjonsvedtak">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q13="http://rep.geointegrasjon.no/Felles/Kodeliste/xml.schema/2012.01.31" base="q13:Kode">
        <xs:sequence />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Kassasjonsvedtak" nillable="true" type="tns:Kassasjonsvedtak" />
  <xs:complexType name="Klassifikasjonssystem">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q14="http://rep.geointegrasjon.no/Felles/Kodeliste/xml.schema/2012.01.31" base="q14:Kode">
        <xs:sequence />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Klassifikasjonssystem" nillable="true" type="tns:Klassifikasjonssystem" />
  <xs:complexType name="Korrespondanseparttype">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q15="http://rep.geointegrasjon.no/Felles/Kodeliste/xml.schema/2012.01.31" base="q15:Kode">
        <xs:sequence />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Korrespondanseparttype" nillable="true" type="tns:Korrespondanseparttype" />
  <xs:complexType name="Mappetype">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q16="http://rep.geointegrasjon.no/Felles/Kodeliste/xml.schema/2012.01.31" base="q16:Kode">
        <xs:sequence />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Mappetype" nillable="true" type="tns:Mappetype" />
  <xs:complexType name="Saksstatus">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q17="http://rep.geointegrasjon.no/Felles/Kodeliste/xml.schema/2012.01.31" base="q17:Kode">
        <xs:sequence />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Saksstatus" nillable="true" type="tns:Saksstatus" />
  <xs:complexType name="SkjermingOpphorerAksjon">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q18="http://rep.geointegrasjon.no/Felles/Kodeliste/xml.schema/2012.01.31" base="q18:Kode">
        <xs:sequence />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="SkjermingOpphorerAksjon" nillable="true" type="tns:SkjermingOpphorerAksjon" />
  <xs:complexType name="SkjermingsHjemmel">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q19="http://rep.geointegrasjon.no/Felles/Kodeliste/xml.schema/2012.01.31" base="q19:Kode">
        <xs:sequence />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="SkjermingsHjemmel" nillable="true" type="tns:SkjermingsHjemmel" />
  <xs:complexType name="Journpostnoekkel">
    <xs:sequence />
  </xs:complexType>
  <xs:element name="Journpostnoekkel" nillable="true" type="tns:Journpostnoekkel" />
  <xs:complexType name="Dokumentnummer">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:Journpostnoekkel">
        <xs:sequence>
          <xs:element name="saksaar" type="xs:long" />
          <xs:element name="sakssekvensnummer" type="xs:long" />
          <xs:element minOccurs="0" name="journalpostnummer" type="xs:long" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Dokumentnummer" nillable="true" type="tns:Dokumentnummer" />
  <xs:complexType name="JournpostEksternNoekkel">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:Journpostnoekkel">
        <xs:sequence>
          <xs:element name="eksternnoekkel" nillable="true" type="tns:EksternNoekkel">
            <xs:annotation>
              <xs:appinfo>
                <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="JournpostEksternNoekkel" nillable="true" type="tns:JournpostEksternNoekkel" />
  <xs:complexType name="JournpostSystemID">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:Journpostnoekkel">
        <xs:sequence>
          <xs:element name="systemID" nillable="true" type="tns:SystemID">
            <xs:annotation>
              <xs:appinfo>
                <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="JournpostSystemID" nillable="true" type="tns:JournpostSystemID" />
  <xs:complexType name="Journalnummer">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:Journpostnoekkel">
        <xs:sequence>
          <xs:element name="journalaar" type="xs:long" />
          <xs:element name="journalsekvensnummer" type="xs:long" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Journalnummer" nillable="true" type="tns:Journalnummer" />
  <xs:complexType name="TilleggsinformasjonListe">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="liste" nillable="true" type="tns:Tilleggsinformasjon" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="TilleggsinformasjonListe" nillable="true" type="tns:TilleggsinformasjonListe" />
  <xs:complexType name="Tilleggsinformasjon">
    <xs:sequence>
      <xs:element minOccurs="0" name="systemID" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="rekkefoelge" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element name="informasjonstype" nillable="true" type="tns:Informasjonstype">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="tilgangsrestriksjon" nillable="true" type="tns:Tilgangsrestriksjon">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="oppbevaresTilDato" type="xs:dateTime" />
      <xs:element name="informasjon" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="tilgangsgruppeNavn" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="registrertDato" type="xs:dateTime" />
      <xs:element minOccurs="0" name="registrertAv" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="registrertAvInit" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Tilleggsinformasjon" nillable="true" type="tns:Tilleggsinformasjon" />
  <xs:complexType name="MerknadListe">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="liste" nillable="true" type="tns:Merknad" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="MerknadListe" nillable="true" type="tns:MerknadListe" />
  <xs:complexType name="Merknad">
    <xs:sequence>
      <xs:element minOccurs="0" name="systemID" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element name="merknadstekst" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="merknadstype" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="merknadsdato" type="xs:dateTime" />
      <xs:element minOccurs="0" name="merknadRegistrertAv" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="merknadRegistrertAvInit" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Merknad" nillable="true" type="tns:Merknad" />
  <xs:complexType name="AvskrivningListe">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="liste" nillable="true" type="tns:Avskrivning" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="AvskrivningListe" nillable="true" type="tns:AvskrivningListe" />
  <xs:complexType name="Avskrivning">
    <xs:sequence>
      <xs:element minOccurs="0" name="systemID" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="avskrivningsdato" type="xs:dateTime" />
      <xs:element minOccurs="0" name="avskrevetAv" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="avskrivningsmaate" nillable="true" type="tns:Avskrivningsmaate">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="referanseAvskriverJournalnummer" nillable="true" type="tns:Journalnummer">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="referanseAvskrivesAvJournalnummer" nillable="true" type="tns:Journalnummer">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="referanseAvskriverEksternNoekkel" nillable="true" type="tns:EksternNoekkel">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="referanseAvskrivesAvEksternNoekkel" nillable="true" type="tns:EksternNoekkel">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Avskrivning" nillable="true" type="tns:Avskrivning" />
  <xs:complexType name="Journalpost">
    <xs:sequence>
      <xs:element minOccurs="0" name="systemID" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="journalnummer" nillable="true" type="tns:Journalnummer">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="journalpostnummer" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="journaldato" type="xs:dateTime" />
      <xs:element minOccurs="0" name="journalposttype" nillable="true" type="tns:Journalposttype">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="dokumentetsDato" type="xs:dateTime" />
      <xs:element minOccurs="0" name="journalstatus" nillable="true" type="tns:Journalstatus">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element name="tittel" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="skjermetTittel" type="xs:boolean" />
      <xs:element minOccurs="0" name="forfallsdato" type="xs:dateTime" />
      <xs:element minOccurs="0" name="skjerming" nillable="true" type="tns:Skjerming">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="referanseArkivdel" nillable="true" type="tns:Arkivdel">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="tilleggskode" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="antallVedlegg" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="offentligTittel" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element xmlns:q20="http://rep.geointegrasjon.no/Arkiv/Felles/xml.schema/2012.01.31" minOccurs="0" name="saksnr" nillable="true" type="q20:Saksnummer">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="tilgangsgruppeNavn" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="referanseSakSystemID" nillable="true" type="tns:SakSystemId">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="korrespondansepart" nillable="true" type="tns:KorrespondansepartListe">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="referanseEksternNoekkel" nillable="true" type="tns:EksternNoekkel">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="referanseMappeEksternNoekkel" nillable="true" type="tns:EksternNoekkel">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="referanseAvskrivninger" nillable="true" type="tns:AvskrivningListe">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="merknader" nillable="true" type="tns:MerknadListe">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="tilleggsinformasjon" nillable="true" type="tns:TilleggsinformasjonListe">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Journalpost" nillable="true" type="tns:Journalpost" />
  <xs:complexType name="Skjerming">
    <xs:sequence>
      <xs:element minOccurs="0" name="tilgangsrestriksjon" nillable="true" type="tns:Tilgangsrestriksjon">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="skjermingshjemmel" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="skjermingOpphoererDato" type="xs:dateTime" />
      <xs:element minOccurs="0" name="skjermingOpphoererAksjon" nillable="true" type="tns:SkjermingOpphorerAksjon">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Skjerming" nillable="true" type="tns:Skjerming" />
  <xs:complexType name="KorrespondansepartListe">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="liste" nillable="true" type="tns:Korrespondansepart" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="KorrespondansepartListe" nillable="true" type="tns:KorrespondansepartListe" />
  <xs:complexType name="Korrespondansepart">
    <xs:sequence>
      <xs:element minOccurs="0" name="systemID" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element name="korrespondanseparttype" nillable="true" type="tns:Korrespondanseparttype">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="behandlingsansvarlig" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="skjermetKorrespondansepart" type="xs:boolean" />
      <xs:element minOccurs="0" name="kortnavn" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="deresReferanse" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="journalenhet" nillable="true" type="tns:Journalenhet">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="fristBesvarelse" type="xs:dateTime" />
      <xs:element minOccurs="0" name="forsendelsesmaate" nillable="true" type="tns:Forsendelsesmaate">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="administrativEnhetInit" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="administrativEnhet" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="saksbehandlerInit" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="saksbehandler" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element xmlns:q21="http://rep.geointegrasjon.no/Felles/Kontakt/xml.schema/2012.01.31" name="Kontakt" nillable="true" type="q21:Kontakt">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Korrespondansepart" nillable="true" type="tns:Korrespondansepart" />
  <xs:complexType name="JournalpostListe">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="liste" nillable="true" type="tns:Journalpost" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="JournalpostListe" nillable="true" type="tns:JournalpostListe" />
  <xs:complexType name="SakspartListe">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="liste" nillable="true" type="tns:Sakspart" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SakspartListe" nillable="true" type="tns:SakspartListe" />
  <xs:complexType name="Sakspart">
    <xs:sequence>
      <xs:element minOccurs="0" name="systemID" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="skjermetSakspart" type="xs:boolean" />
      <xs:element minOccurs="0" name="kortnavn" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="kontaktperson" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="sakspartRolle" nillable="true" type="tns:SakspartRolle">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="merknad" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element xmlns:q22="http://rep.geointegrasjon.no/Felles/Kontakt/xml.schema/2012.01.31" name="Kontakt" nillable="true" type="q22:Kontakt">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Sakspart" nillable="true" type="tns:Sakspart" />
  <xs:complexType name="Saksmappe">
    <xs:sequence>
      <xs:element minOccurs="0" name="systemID" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element xmlns:q23="http://rep.geointegrasjon.no/Arkiv/Felles/xml.schema/2012.01.31" minOccurs="0" name="saksnr" nillable="true" type="q23:Saksnummer">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="mappetype" nillable="true" type="tns:Mappetype">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="saksdato" type="xs:dateTime" />
      <xs:element name="tittel" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="offentligTittel" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="skjermetTittel" type="xs:boolean" />
      <xs:element minOccurs="0" name="skjerming" nillable="true" type="tns:Skjerming">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="saksstatus" nillable="true" type="tns:Saksstatus">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="dokumentmedium" nillable="true" type="tns:Dokumentmedium">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="referanseArkivdel" nillable="true" type="tns:Arkivdel">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="journalenhet" nillable="true" type="tns:Journalenhet">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="bevaringstid" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="kassasjonsvedtak" nillable="true" type="tns:Kassasjonsvedtak">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="kassasjonsdato" type="xs:dateTime" />
      <xs:element minOccurs="0" name="prosjekt" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="administrativEnhetInit" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="administrativEnhet" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="saksansvarligInit" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="saksansvarlig" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="tilgangsgruppeNavn" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element xmlns:q24="http://rep.geointegrasjon.no/Matrikkel/Felles/xml.schema/2012.01.31" minOccurs="0" name="Matrikkelnummer" nillable="true" type="q24:MatrikkelnummerListe">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="klasse" nillable="true" type="tns:KlasseListe">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="sakspart" nillable="true" type="tns:SakspartListe">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element xmlns:q25="http://rep.geointegrasjon.no/Felles/Geometri/xml.schema/2012.01.31" minOccurs="0" name="Punkt" nillable="true" type="q25:PunktListe">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="tilleggsinformasjon" nillable="true" type="tns:TilleggsinformasjonListe">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element xmlns:q26="http://rep.geointegrasjon.no/Matrikkel/Felles/xml.schema/2012.01.31" minOccurs="0" name="ByggIdent" nillable="true" type="q26:ByggIdentListe">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="referanseEksternNoekkel" nillable="true" type="tns:EksternNoekkel">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="merknader" nillable="true" type="tns:MerknadListe">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element xmlns:q27="http://rep.geointegrasjon.no/Plan/Felles/xml.schema/2012.01.31" minOccurs="0" name="planIdent" nillable="true" type="q27:NasjonalArealplanId">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Saksmappe" nillable="true" type="tns:Saksmappe" />
  <xs:complexType name="KlasseListe">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="liste" nillable="true" type="tns:Klasse" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="KlasseListe" nillable="true" type="tns:KlasseListe" />
  <xs:complexType name="Klasse">
    <xs:sequence>
      <xs:element minOccurs="0" name="rekkefoelge" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element name="klassifikasjonssystem" nillable="true" type="tns:Klassifikasjonssystem">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element name="klasseID" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="skjermetKlasse" type="xs:boolean" />
      <xs:element minOccurs="0" name="ledetekst" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="tittel" nillable="true" type="xs:string">
        <xs:annotation>
          <xs:appinfo>
            <DefaultValue EmitDefaultValue="false" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
          </xs:appinfo>
        </xs:annotation>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Klasse" nillable="true" type="tns:Klasse" />
  <xs:complexType name="SaksmappeListe">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="liste" nillable="true" type="tns:Saksmappe" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SaksmappeListe" nillable="true" type="tns:SaksmappeListe" />
</xs:schema>