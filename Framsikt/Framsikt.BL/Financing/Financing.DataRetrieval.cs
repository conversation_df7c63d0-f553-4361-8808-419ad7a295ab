using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using System.Collections.ObjectModel;

namespace Framsikt.BL;

public partial class Financing
{
    public dynamic GetNeedForFinancingData(string userID, string pageId, string InvestmentProgram, int budgetYear)
    {
        return GetNeedForFinancingDataAsync(userID, pageId, InvestmentProgram, budgetYear).GetAwaiter().GetResult();
    }

    public async Task<dynamic> GetNeedForFinancingDataAsync(string userID, string pageId, string InvestmentProgram, int budgetYear)
    {
        UserData userDetails = await pUtility.GetUserDetailsAsync(userID);
        TenantDBContext IDBContext = await pUtility.GetTenantDBContextAsync();
        Dictionary<string, clsLanguageString> langStringValuesCommon = await pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Common");

        var orgVersionContent = await pUtility.GetOrgVersionSpecificContentAsync(userID, pUtility.GetForecastPeriod(budgetYear, 1));

        IEnumerable<KeyValueHelper> budgetChangeData = await GetActiveBudgetChangeIdAsync(userID, pageId, budgetYear);
        int activeChangeId = budgetChangeData.Any() ? budgetChangeData.FirstOrDefault().KeyId : -2;

        //Get investment program data
        List<string> lstprogramCodes = InvestmentProgram.ToString() == "0" ? (await pUtility.GetInvestmentsProgramsAsync(userID)).Select(x => x.key).ToList() : new List<string>() { InvestmentProgram.ToString() };
        lstprogramCodes = lstprogramCodes.Count() == 0 ? new List<string> { "" } : lstprogramCodes;

        //New and Existing Investments for logged in tenant
        List<clsFinancing> tenantData = await GetTenantDataAsync(IDBContext, userDetails, budgetYear, lstprogramCodes, pageId, activeChangeId);
        List<clsFinancing> tenantDataForActiveChange = await GetTenantDataForActiveChangeAsync(IDBContext, userDetails, budgetYear, activeChangeId, lstprogramCodes);

        List<clsFinancing> manuallyAddedTenantData = await GetManuallyAddedTenantDataAsync(IDBContext, userDetails, budgetYear, lstprogramCodes, pageId, activeChangeId);
        List<clsFinancing> manuallyAddedTenantDataForActiveChange = await GetManuallyAddedTenantDataForActiveChangeAsync(IDBContext, userDetails, budgetYear, activeChangeId, lstprogramCodes);

        //New and Existing Investments for sub tenant
        List<clsFinancing> subTenantData = new List<Helpers.clsFinancing>();
        List<clsFinancing> subTenantDataForActiveChange = new List<Helpers.clsFinancing>();

        dynamic defaults = await GetDefaultsAsync(userID, 1, budgetYear);

        dynamic departmentData = defaults.department;
        dynamic functionData = defaults.functionn;
        string acc_defaults_1 = await (from a in IDBContext.tmd_acc_defaults
            where a.fk_tenant_id == userDetails.tenant_id && a.module == "INV" && a.acc_type == "ACCOUNT" && a.link_type == "INVACTIONTYPE" && a.link_value == "1"
                  && a.fk_org_version == orgVersionContent.orgVersion
            select a.acc_value).FirstOrDefaultAsync();

        string account_text = string.Empty;
        if (!string.IsNullOrEmpty(acc_defaults_1))
        {
            account_text = await (from a in IDBContext.tco_accounts
                where a.pk_tenant_id == userDetails.tenant_id && a.pk_account_code == acc_defaults_1
                select a.display_name).FirstOrDefaultAsync();
        }

        dynamic result = new JObject();
        if (departmentData.fk_department_code == "0" || functionData.fk_function_code == "0" || string.IsNullOrEmpty(account_text))
        {
            result.error = true;
            result.message = ((langStringValuesCommon.FirstOrDefault(v => v.Key == "financing_pre_requisites")).Value).LangText;
            return result;
        }

        string acc_defaults_actionType1_lineOrder10 = await (from a in IDBContext.tmd_acc_defaults
            where a.link_type == "ACTIONTYPE_LINE"
                  && a.acc_type == "ACCOUNT"
                  && a.module == "INV"
                  && a.active == 1
                  && a.fk_tenant_id == userDetails.tenant_id
                  && a.link_value == "1_10"
                  && a.fk_org_version == orgVersionContent.orgVersion
            select a.acc_value).FirstOrDefaultAsync();

        tenantData = await InsertDefaultLineOrdersAsync(userID, 10, tenantData, IDBContext, userDetails, departmentData, functionData, string.IsNullOrEmpty(acc_defaults_actionType1_lineOrder10) ? acc_defaults_1 : acc_defaults_actionType1_lineOrder10, 1, budgetYear);
        string acc_defaults_actionType1_lineOrder20 = await (from a in IDBContext.tmd_acc_defaults
            where a.link_type == "ACTIONTYPE_LINE"
                  && a.acc_type == "ACCOUNT"
                  && a.module == "INV"
                  && a.active == 1
                  && a.fk_tenant_id == userDetails.tenant_id
                  && a.link_value == "1_20"
                  && a.fk_org_version == orgVersionContent.orgVersion
            select a.acc_value).FirstOrDefaultAsync();
        tenantData = await InsertDefaultLineOrdersAsync(userID, 20, tenantData, IDBContext, userDetails, departmentData, functionData, string.IsNullOrEmpty(acc_defaults_actionType1_lineOrder20) ? acc_defaults_1 : acc_defaults_actionType1_lineOrder20, 2, budgetYear);
        string acc_defaults_actionType1_lineOrder30 = await (from a in IDBContext.tmd_acc_defaults
            where a.link_type == "ACTIONTYPE_LINE"
                  && a.acc_type == "ACCOUNT"
                  && a.module == "INV"
                  && a.active == 1
                  && a.fk_tenant_id == userDetails.tenant_id
                  && a.link_value == "1_30"
                  && a.fk_org_version == orgVersionContent.orgVersion
            select a.acc_value).FirstOrDefaultAsync();
        return await FormatNeedForFinancingGridAsync(userID, budgetYear, tenantData, subTenantData, manuallyAddedTenantData, tenantDataForActiveChange, subTenantDataForActiveChange, manuallyAddedTenantDataForActiveChange, activeChangeId);
    }



    public dynamic GetInternalAndExternalFinancingData(string userID, string pageId, string InvestmentProgram, int budgetYear)
    {
        return GetInternalAndExternalFinancingDataAsync(userID, pageId, InvestmentProgram, budgetYear).GetAwaiter().GetResult();
    }



    public async Task<dynamic> GetInternalAndExternalFinancingDataAsync(string userID, string pageId, string InvestmentProgram, int budgetYear)
    {
        UserData userDetails = await pUtility.GetUserDetailsAsync(userID);
        TenantDBContext IDBContext = await pUtility.GetTenantDBContextAsync();
        TenantDBContext consequenceAdjustedBudgetDbContext = await pUtility.GetTenantDBContextAsync();

        var orgVersionContent = await pUtility.GetOrgVersionSpecificContentAsync(userID, pUtility.GetForecastPeriod(budgetYear, 1));

        IEnumerable<KeyValueHelper> budgetChangeData = await GetActiveBudgetChangeIdAsync(userID, pageId, budgetYear);
        int activeChangeId = budgetChangeData.Any() ? budgetChangeData.FirstOrDefault().KeyId : -2;

        dynamic result = new JObject();

        Dictionary<string, clsLanguageString> langStringValuesCommon = await pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Common");
        Dictionary<string, clsLanguageString> langStringFormat = await pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "NumberFormats");
        List<string> lstprogramCodes = InvestmentProgram.ToString() == "0" ? (await pUtility.GetInvestmentsProgramsAsync(userID)).Select(x => x.key).ToList() : new List<string>() { InvestmentProgram.ToString() };
        lstprogramCodes = lstprogramCodes.Count() == 0 ? new List<string> { "" } : lstprogramCodes;

        List<int> FinancingActionTypes = new List<int>()
        {
            Convert.ToInt32(clsConstants.FinancingActionType.External),
            Convert.ToInt32(clsConstants.FinancingActionType.Internal)
        }.OrderBy(x => x).ToList();

        var dataBeforeFilter = await (from ih in IDBContext.tfp_inv_header
            join it in IDBContext.tfp_inv_transactions on new { a = ih.fk_tenant_id, b = ih.pk_inv_action_id }
                equals new { a = it.fk_tenant_id, b = it.fk_inv_action_id } into group1
            from g1 in group1.DefaultIfEmpty()
            join tbc in IDBContext.tfp_budget_changes on new { a = g1.fk_tenant_id, b = g1.fk_change_id }
                equals new { a = tbc.fk_tenant_id, b = tbc.pk_change_id } into group2
            from g2 in group2.DefaultIfEmpty()
            where (ih.fk_tenant_id == userDetails.tenant_id
                   && ih.budget_year == budgetYear
                   && FinancingActionTypes.Contains(ih.action_type))
            select new
            {
                action_type = ih.action_type,
                investment_id = ih.pk_inv_action_id,
                caption_name = ih.action_name,
                line_order = ih.line_order,
                isManuallyAdded = ih.isManuallyAdded,
                year_1_amount = (g1.year_1_amount.Equals(null) ? 0 : g1.year_1_amount),
                year_2_amount = (g1.year_2_amount.Equals(null) ? 0 : g1.year_2_amount),
                year_3_amount = (g1.year_3_amount.Equals(null) ? 0 : g1.year_3_amount),
                year_4_amount = (g1.year_4_amount.Equals(null) ? 0 : g1.year_4_amount),
                accountCode = g1.fk_account_code,
                departmentCode = g1.department_code_,
                functioncode = g1.fk_function_code_,
                projectCode = g1.fk_project_code,
                freedim1 = g1.free_dim_1,
                freedim2 = g1.free_dim_2,
                freedim3 = g1.free_dim_3,
                freedim4 = g1.free_dim_4,
                programCode = g1.fk_prog_code,
                orgBudgetFlag = g2 != null ? g2.org_budget_flag : -1
            }).OrderBy(x => x.isManuallyAdded).ThenBy(x => x.line_order).ToListAsync();

        if (pageId != "YB_FINANCING")
        {
            dataBeforeFilter = dataBeforeFilter.Where(x => x.orgBudgetFlag != 0).ToList();
        }

        if (InvestmentProgram.ToString() != "0")
        {
            dataBeforeFilter = dataBeforeFilter.Where(x => lstprogramCodes.Contains(x.programCode)).OrderBy(x => x.line_order).ToList();
        }

        List<clsFinancing> data = (from d in dataBeforeFilter
            group d by new { d.investment_id, d.caption_name, d.action_type, d.line_order, d.isManuallyAdded } into groupData
            select new clsFinancing
            {
                action_type = groupData.Key.action_type,
                investment_id = groupData.Key.investment_id,
                caption_name = groupData.Key.caption_name,
                line_order = groupData.Key.line_order,
                isManuallyAdded = groupData.Key.isManuallyAdded,
                year_1_amount = groupData.Sum(x => x.year_1_amount) / 1000,
                year_2_amount = groupData.Sum(x => x.year_2_amount) / 1000,
                year_3_amount = groupData.Sum(x => x.year_3_amount) / 1000,
                year_4_amount = groupData.Sum(x => x.year_4_amount) / 1000,
                year_1_amount_with_fk_change_id_zero = 0
            }).OrderBy(x => x.isManuallyAdded).ThenBy(x => x.line_order).ToList();

        var ChangeDataBeforeFilter = await (from ih in IDBContext.tfp_inv_header
            join it in IDBContext.tfp_inv_transactions on new { a = ih.fk_tenant_id, b = ih.pk_inv_action_id }
                equals new { a = it.fk_tenant_id, b = it.fk_inv_action_id } into group1
            from g1 in group1.DefaultIfEmpty()
            join tbc in IDBContext.tfp_budget_changes on new { a = g1.fk_tenant_id, b = g1.fk_change_id }
                equals new { a = tbc.fk_tenant_id, b = tbc.pk_change_id } into group2
            from g2 in group2.DefaultIfEmpty()
            where (ih.fk_tenant_id == userDetails.tenant_id && g1.fk_change_id == activeChangeId
                                                            && ih.budget_year == budgetYear
                                                            && FinancingActionTypes.Contains(ih.action_type))
            select new
            {
                action_type = ih.action_type,
                investment_id = ih.pk_inv_action_id,
                caption_name = ih.action_name,
                line_order = ih.line_order,
                isManuallyAdded = ih.isManuallyAdded,
                year_1_amount = (g1.year_1_amount.Equals(null) ? 0 : g1.year_1_amount),
                year_2_amount = (g1.year_2_amount.Equals(null) ? 0 : g1.year_2_amount),
                year_3_amount = (g1.year_3_amount.Equals(null) ? 0 : g1.year_3_amount),
                year_4_amount = (g1.year_4_amount.Equals(null) ? 0 : g1.year_4_amount),
                changeId = g1.fk_change_id,
                accountCode = g1.fk_account_code,
                departmentCode = g1.department_code_,
                functioncode = g1.fk_function_code_,
                projectCode = g1.fk_project_code,
                freedim1 = g1.free_dim_1,
                freedim2 = g1.free_dim_2,
                freedim3 = g1.free_dim_3,
                freedim4 = g1.free_dim_4,
                programCode = g1.fk_prog_code,
                orgBudgetFlag = g2 != null ? g2.org_budget_flag : -1
            }).OrderBy(x => x.isManuallyAdded).ThenBy(x => x.line_order).ToListAsync();

        if (pageId != "YB_FINANCING")
        {
            ChangeDataBeforeFilter = ChangeDataBeforeFilter.Where(x => x.orgBudgetFlag != 0).ToList();
        }
        if (InvestmentProgram.ToString() != "0")
        {
            ChangeDataBeforeFilter = ChangeDataBeforeFilter.Where(x => lstprogramCodes.Contains(x.programCode)).OrderBy(x => x.line_order).ToList();
        }
        List<clsFinancing> ChangeData = (from d in ChangeDataBeforeFilter
            group d by new { d.investment_id, d.caption_name, d.action_type, d.line_order, d.isManuallyAdded, d.changeId } into groupData
            select new clsFinancing
            {
                action_type = groupData.Key.action_type,
                investment_id = groupData.Key.investment_id,
                caption_name = groupData.Key.caption_name,
                line_order = groupData.Key.line_order,
                isManuallyAdded = groupData.Key.isManuallyAdded,
                year_1_amount = groupData.Sum(x => x.year_1_amount) / 1000,
                year_2_amount = groupData.Sum(x => x.year_2_amount) / 1000,
                year_3_amount = groupData.Sum(x => x.year_3_amount) / 1000,
                year_4_amount = groupData.Sum(x => x.year_4_amount) / 1000,
                fk_change_id = groupData.Key.changeId
            }).OrderBy(x => x.isManuallyAdded).ThenBy(x => x.line_order).ToList();

        dynamic defaults_actionType_2 = GetDefaults(userID, 2, budgetYear);
        dynamic defaults_actionType_3 = GetDefaults(userID, 3, budgetYear);

        dynamic departmentData_action_type_2 = defaults_actionType_2.department;
        dynamic functionData_action_type_2 = defaults_actionType_2.functionn;

        dynamic departmentData_action_type_3 = defaults_actionType_3.department;
        dynamic functionData_action_type_3 = defaults_actionType_3.functionn;

        string acc_defaults_2 = await (from a in consequenceAdjustedBudgetDbContext.tmd_acc_defaults
            where a.fk_tenant_id == userDetails.tenant_id && a.module == "INV" && a.acc_type == "ACCOUNT" && a.link_type == "INVACTIONTYPE" && a.link_value == "2"
                  && a.fk_org_version == orgVersionContent.orgVersion
            select a.acc_value).FirstOrDefaultAsync();
        string acc_defaults_3 = await (from a in consequenceAdjustedBudgetDbContext.tmd_acc_defaults
            where a.fk_tenant_id == userDetails.tenant_id && a.module == "INV" && a.acc_type == "ACCOUNT" && a.link_type == "INVACTIONTYPE" && a.link_value == "3"
                  && a.fk_org_version == orgVersionContent.orgVersion
            select a.acc_value).FirstOrDefaultAsync();

        string account_text_2 = string.Empty;
        string account_text_3 = string.Empty;
        if (!string.IsNullOrEmpty(acc_defaults_2))
        {
            account_text_2 = await (from a in consequenceAdjustedBudgetDbContext.tco_accounts
                where a.pk_tenant_id == userDetails.tenant_id && a.pk_account_code == acc_defaults_2
                select a.display_name).FirstOrDefaultAsync();
        }
        if (!string.IsNullOrEmpty(acc_defaults_3))
        {
            account_text_3 = await (from a in consequenceAdjustedBudgetDbContext.tco_accounts
                where a.pk_tenant_id == userDetails.tenant_id && a.pk_account_code == acc_defaults_3
                select a.display_name).FirstOrDefaultAsync();
        }

        if (departmentData_action_type_2.fk_department_code == "0" || functionData_action_type_2.fk_function_code == "0" ||
            departmentData_action_type_3.fk_department_code == "0" || functionData_action_type_3.fk_function_code == "0" ||
            string.IsNullOrEmpty(account_text_2) || string.IsNullOrEmpty(account_text_3))
        {
            result.error = true;
            result.message = ((langStringValuesCommon.FirstOrDefault(v => v.Key == "financing_pre_requisites")).Value).LangText;
            return result;
        }
        string acc_defaults_actionType2_lineOrder10 = await (from a in IDBContext.tmd_acc_defaults
            where a.link_type == "ACTIONTYPE_LINE"
                  && a.acc_type == "ACCOUNT"
                  && a.module == "INV"
                  && a.active == 1
                  && a.fk_tenant_id == userDetails.tenant_id
                  && a.link_value == "2_10"
                  && a.fk_org_version == orgVersionContent.orgVersion
            select a.acc_value).FirstOrDefaultAsync();

        data = await InsertDefaultLineOrdersAsync(userID, 10, data, IDBContext, userDetails, departmentData_action_type_2, functionData_action_type_2, string.IsNullOrEmpty(acc_defaults_actionType2_lineOrder10) ? acc_defaults_2 : acc_defaults_actionType2_lineOrder10, 2, budgetYear);

        string acc_defaults_actionType2_lineOrder20 = await (from a in IDBContext.tmd_acc_defaults
            where a.link_type == "ACTIONTYPE_LINE"
                  && a.acc_type == "ACCOUNT"
                  && a.module == "INV"
                  && a.active == 1
                  && a.fk_tenant_id == userDetails.tenant_id
                  && a.link_value == "2_20"
                  && a.fk_org_version == orgVersionContent.orgVersion
            select a.acc_value).FirstOrDefaultAsync();

        data = await InsertDefaultLineOrdersAsync(userID, 20, data, IDBContext, userDetails, departmentData_action_type_2, functionData_action_type_2, string.IsNullOrEmpty(acc_defaults_actionType2_lineOrder20) ? acc_defaults_2 : acc_defaults_actionType2_lineOrder20, 2, budgetYear);
        string acc_defaults_actionType2_lineOrder1 = await (from a in IDBContext.tmd_acc_defaults
            where a.link_type == "ACTIONTYPE_LINE"
                  && a.acc_type == "ACCOUNT"
                  && a.module == "INV"
                  && a.active == 1
                  && a.fk_tenant_id == userDetails.tenant_id
                  && a.link_value == "2_1"
                  && a.fk_org_version == orgVersionContent.orgVersion
            select a.acc_value).FirstOrDefaultAsync();
        data = await InsertDefaultLineOrdersAsync(userID, 1, data, IDBContext, userDetails, departmentData_action_type_2, functionData_action_type_2, string.IsNullOrEmpty(acc_defaults_actionType2_lineOrder1) ? acc_defaults_2 : acc_defaults_actionType2_lineOrder1, 2, budgetYear);

        string acc_defaults_actionType2_lineOrder80 = await (from a in IDBContext.tmd_acc_defaults
            where a.link_type == "ACTIONTYPE_LINE"
                  && a.acc_type == "ACCOUNT"
                  && a.module == "INV"
                  && a.active == 1
                  && a.fk_tenant_id == userDetails.tenant_id
                  && a.link_value == "2_80"
                  && a.fk_org_version == orgVersionContent.orgVersion
            select a.acc_value).FirstOrDefaultAsync();

        data = await InsertDefaultLineOrdersAsync(userID, 80, data, IDBContext, userDetails, departmentData_action_type_2, functionData_action_type_2, string.IsNullOrEmpty(acc_defaults_actionType2_lineOrder80) ? acc_defaults_2 : acc_defaults_actionType2_lineOrder80, 2, budgetYear);

        string acc_defaults_actionType3_lineOrder10 = await (from a in IDBContext.tmd_acc_defaults
            where a.link_type == "ACTIONTYPE_LINE"
                  && a.acc_type == "ACCOUNT"
                  && a.module == "INV"
                  && a.active == 1
                  && a.fk_tenant_id == userDetails.tenant_id
                  && a.link_value == "3_10"
                  && a.fk_org_version == orgVersionContent.orgVersion
            select a.acc_value).FirstOrDefaultAsync();
        data = await InsertDefaultLineOrdersAsync(userID, 10, data, IDBContext, userDetails, departmentData_action_type_3, functionData_action_type_3, string.IsNullOrEmpty(acc_defaults_actionType3_lineOrder10) ? acc_defaults_3 : acc_defaults_actionType3_lineOrder10, 3, budgetYear);

        string acc_defaults_actionType3_lineOrder20 = await (from a in IDBContext.tmd_acc_defaults
            where a.link_type == "ACTIONTYPE_LINE"
                  && a.acc_type == "ACCOUNT"
                  && a.module == "INV"
                  && a.active == 1
                  && a.fk_tenant_id == userDetails.tenant_id
                  && a.link_value == "3_20"
                  && a.fk_org_version == orgVersionContent.orgVersion
            select a.acc_value).FirstOrDefaultAsync();

        data = await InsertDefaultLineOrdersAsync(userID, 20, data, IDBContext, userDetails, departmentData_action_type_3, functionData_action_type_3, string.IsNullOrEmpty(acc_defaults_actionType3_lineOrder20) ? acc_defaults_3 : acc_defaults_actionType3_lineOrder20, 3, budgetYear);

        data = data.OrderBy(x => x.isManuallyAdded).ThenBy(x => x.line_order).ToList();

        dynamic columnFields = new JArray();

        columnFields.Add("id");
        columnFields.Add("investmentDesc");
        columnFields.Add("isClickable");
        columnFields.Add("type");
        columnFields.Add("refreshStatus");
        columnFields.Add("lineOrders");
        columnFields.Add("year1Amount");
        columnFields.Add("year1Percentage");
        columnFields.Add("year2Amount");
        columnFields.Add("year2Percentage");
        columnFields.Add("year3Amount");
        columnFields.Add("year3Percentage");
        columnFields.Add("year4Amount");
        columnFields.Add("year4Percentage");
        columnFields.Add("changeYear1");
        columnFields.Add("changeYear2");
        columnFields.Add("changeYear3");
        columnFields.Add("changeYear4");
        dynamic columnTitles = new JArray();

        Dictionary<string, clsLanguageString> langStringValuesFinancing = await pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Financing");
        Dictionary<string, clsLanguageString> langStringValuesBM = await pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetManagement");

        columnTitles.Add(" ");
        columnTitles.Add(((langStringValuesCommon.FirstOrDefault(v => v.Key == "cmn_action_description_text")).Value).LangText);
        columnTitles.Add(" ");
        columnTitles.Add(" ");
        columnTitles.Add(" ");
        columnTitles.Add(" ");
        columnTitles.Add(((langStringValuesBM.FirstOrDefault(v => v.Key == "BM_meeting_tab")).Value).LangText + " " + budgetYear.ToString());
        columnTitles.Add(" ");
        columnTitles.Add(((langStringValuesBM.FirstOrDefault(v => v.Key == "BM_meeting_tab")).Value).LangText + " " + (budgetYear + 1).ToString());
        columnTitles.Add(" ");
        columnTitles.Add(((langStringValuesBM.FirstOrDefault(v => v.Key == "BM_meeting_tab")).Value).LangText + " " + (budgetYear + 2).ToString());
        columnTitles.Add(" ");
        columnTitles.Add(((langStringValuesBM.FirstOrDefault(v => v.Key == "BM_meeting_tab")).Value).LangText + " " + (budgetYear + 3).ToString());
        columnTitles.Add(" ");
        columnTitles.Add(((langStringValuesFinancing.FirstOrDefault(v => v.Key == "fin_budget_change")).Value).LangText + " " + budgetYear.ToString());
        columnTitles.Add(((langStringValuesFinancing.FirstOrDefault(v => v.Key == "fin_budget_change")).Value).LangText + " " + (budgetYear + 1).ToString());
        columnTitles.Add(((langStringValuesFinancing.FirstOrDefault(v => v.Key == "fin_budget_change")).Value).LangText + " " + (budgetYear + 2).ToString());
        columnTitles.Add(((langStringValuesFinancing.FirstOrDefault(v => v.Key == "fin_budget_change")).Value).LangText + " " + (budgetYear + 3).ToString());

        result.Add("columnTitles", columnTitles);
        result.Add("columnFields", columnFields);
        dynamic rowsExternal = FormatInternalAndExternalFinancingData(data, data.Where(x => x.action_type == FinancingActionTypes[0]).ToList(), FinancingActionTypes[0], false, ((langStringValuesCommon.FirstOrDefault(v => v.Key == "cmn_total_external_fin")).Value).LangText, activeChangeId, ChangeData, pUtility, userID);
        result.Add("rowsExternal", rowsExternal);
        dynamic rowsInternal = FormatInternalAndExternalFinancingData(data, data.Where(x => x.action_type == FinancingActionTypes[1]).ToList(), FinancingActionTypes[1], false, ((langStringValuesCommon.FirstOrDefault(v => v.Key == "cmn_total_internal_fin")).Value).LangText, activeChangeId, ChangeData, pUtility, userID);
        result.Add("rowsInternal", rowsInternal);
        dynamic rowsGridTotal = FormatInternalAndExternalFinancingData(data, data.Where(x => x.action_type == FinancingActionTypes[1]).ToList(), FinancingActionTypes[1], true, ((langStringValuesCommon.FirstOrDefault(v => v.Key == "cmn_total_fin")).Value).LangText, activeChangeId, ChangeData, pUtility, userID);
        result.Add("rowsGridTotal", rowsGridTotal);
        JObject jsonCfgRow = JObject.Parse(await pUtility.GetApplicationSettingAsync("GetNeedForFinancing_grid_config"));
        result.gridConfig = jsonCfgRow;
        result.amountFormat = ((langStringFormat.FirstOrDefault(v => v.Key == "amount")).Value).LangText;

        result.actionTypeInternal = Convert.ToInt32(clsConstants.FinancingActionType.Internal);
        result.actionTypeExternal = Convert.ToInt32(clsConstants.FinancingActionType.External);
        result.activeChangeId = activeChangeId;
        var isActiveBudgetChange = await IDBContext.tfp_budget_changes.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_change_id == activeChangeId && x.budget_year == budgetYear && x.status == 1);
        result.isEditable = isActiveBudgetChange == null ? false : true;
        return result;
    }



    public dynamic GetDataForClientIdAndLineOrder(string userID, int clientID, Collection<int> lineOrders, int? actionType, string pageId, int statusId, int budgetYear, string programCode)
    {
        return GetDataForClientIdAndLineOrderAsync(userID, clientID, lineOrders, actionType, pageId, statusId, budgetYear, programCode).GetAwaiter().GetResult();
    }



    public async Task<dynamic> GetDataForClientIdAndLineOrderAsync(string userID, int clientID, Collection<int> lineOrders, int? actionType, string pageId, int statusId, int budgetYear, string programCode)
    {
        UserData userDetails = await pUtility.GetUserDetailsAsync(userID);
        TenantDBContext IDBContext = await pUtility.GetTenantDBContextAsync();
        TenantDBContext kostraDbContext = await pUtility.GetTenantDBContextAsync();

        var orgVersionContent = await pUtility.GetOrgVersionSpecificContentAsync(userID, pUtility.GetForecastPeriod(budgetYear, 1));

        List<int> statusIdList = new List<int>();
        if (statusId != -1 && statusId != -2)
        {
            statusIdList.Add(statusId);
        }
        else
        {
            statusIdList = new List<int> { 0, 1, 2, 7 };// by default filter on this status ids.
        }

        List<string> progCode = new List<string>();
        if (!string.IsNullOrEmpty(programCode) && programCode != "0")
        {
            progCode.Add(programCode);
        }
        else
        {
            Dictionary<string, string> programCodes = _inv.GetProgramCodes(userID);
            progCode.AddRange(programCodes.Select(x => x.Key).ToList());
        }
        if (clientID == 0)
        {
            List<clsFinancing> data = await (from t in IDBContext.vwUserDetails
                where t.pk_id == userDetails.pk_id
                select new clsFinancing
                {
                    investment_id = 0,
                    caption_name = string.Empty,
                    year_1_amount = 0.0M,
                    year_2_amount = 0.0M,
                    year_3_amount = 0.0M,
                    year_4_amount = 0.0M,
                    changeID = 0,
                    tenantID = 0,
                    budgetYear = 0
                }).ToListAsync();
            if (lineOrders.Contains(80))
            {
                string invNetAmounts = await pUtility.GetParameterValueAsync(userID, "FP_INV_NET_AMOUNTS");
                bool calcVatComp = !string.IsNullOrEmpty(invNetAmounts) ? Convert.ToBoolean(invNetAmounts) : false;
                var defaultAccounts = await (from tad in IDBContext.tmd_acc_defaults
                    where tad.fk_tenant_id == userDetails.tenant_id && tad.acc_type == "ACCOUNT"
                                                                    && tad.module == "INV" && tad.link_type == "VAT_COMP"
                                                                    && tad.fk_org_version == orgVersionContent.orgVersion
                    select tad).ToListAsync();
                if (calcVatComp && defaultAccounts.Any())
                {
                    string accountCode = defaultAccounts.FirstOrDefault().acc_value;
                    data = await (from ti in IDBContext.tco_investments
                        join tr in IDBContext.tfp_inv_transactions on new { a = ti.pk_investment_id, b = ti.fk_tenant_id }
                            equals new { a = tr.fk_investment_id, b = tr.fk_tenant_id }
                        join ib in IDBContext.tco_inv_budgetyear_config on new { a = ti.pk_investment_id, b = ti.fk_tenant_id, c = tr.budget_year }
                            equals new { a = ib.fk_investment_id, b = ib.fk_tenant_id, c = ib.budget_year }
                        where ti.fk_tenant_id == userDetails.tenant_id && tr.budget_year == budgetYear && ib.budget_year == budgetYear
                              && statusIdList.Contains(ib.inv_status) && tr.fk_account_code == accountCode && progCode.Contains(tr.fk_prog_code)
                        group tr by new { tr.fk_investment_id, ib.investment_name, tr.fk_change_id } into groupData
                        select new clsFinancing
                        {
                            investment_id = groupData.Key.fk_investment_id,
                            caption_name = groupData.Key.investment_name,
                            year_1_amount = groupData.Sum(x => x.year_1_amount),
                            year_2_amount = groupData.Sum(x => x.year_2_amount),
                            year_3_amount = groupData.Sum(x => x.year_3_amount),
                            year_4_amount = groupData.Sum(x => x.year_4_amount),
                            changeID = groupData.Key.fk_change_id,
                            tenantID = userDetails.tenant_id,
                            budgetYear = budgetYear
                        }).ToListAsync();
                }
                else
                {
                    data = await (from c in IDBContext.tfp_inv_header
                        join d in IDBContext.tfp_inv_transactions on new { a = c.pk_inv_action_id, b = c.fk_tenant_id, c = c.budget_year }
                            equals new { a = d.fk_inv_action_id, b = d.fk_tenant_id, c = d.budget_year }
                        join e in IDBContext.tco_investments on new { a = d.fk_investment_id, b = d.fk_tenant_id }
                            equals new { a = e.pk_investment_id, b = e.fk_tenant_id }
                        join bc in IDBContext.tco_inv_budgetyear_config on new { a = d.fk_investment_id, b = d.fk_tenant_id, c = d.budget_year }
                            equals new { a = bc.fk_investment_id, b = bc.fk_tenant_id, c = bc.budget_year }
                        join ib in IDBContext.tco_inv_budgetyear_config
                            on new { a = e.pk_investment_id, b = e.fk_tenant_id, c = bc.budget_year }
                            equals new { a = ib.fk_investment_id, b = ib.fk_tenant_id, c = ib.budget_year }
                        where c.fk_tenant_id == userDetails.tenant_id
                              && d.type == "i"
                              && d.vat_refund != 0
                              && c.budget_year == budgetYear
                              && statusIdList.Contains(bc.inv_status)
                              && progCode.Contains(d.fk_prog_code)
                        group d by new { d.fk_investment_id, ib.investment_name, d.fk_change_id } into groupData
                        select new clsFinancing
                        {
                            investment_id = groupData.Key.fk_investment_id,
                            caption_name = groupData.Key.investment_name,
                            year_1_amount = groupData.Sum(x => ((x.vat_refund / 100) * (x.year_1_amount / (1 + (x.vat_rate / 100))) * (x.vat_rate / 100) * (-1))),
                            year_2_amount = groupData.Sum(x => ((x.vat_refund / 100) * (x.year_2_amount / (1 + (x.vat_rate / 100))) * (x.vat_rate / 100) * (-1))),
                            year_3_amount = groupData.Sum(x => ((x.vat_refund / 100) * (x.year_3_amount / (1 + (x.vat_rate / 100))) * (x.vat_rate / 100) * (-1))),
                            year_4_amount = groupData.Sum(x => ((x.vat_refund / 100) * (x.year_4_amount / (1 + (x.vat_rate / 100))) * (x.vat_rate / 100) * (-1))),
                            changeID = groupData.Key.fk_change_id,
                            tenantID = userDetails.tenant_id,
                            budgetYear = budgetYear
                        }).ToListAsync();
                }
            }
            else
            {
                if (statusIdList.Contains(-2))
                {
                    data = await (from c in IDBContext.tfp_inv_header
                        join d in IDBContext.tfp_inv_transactions on new { a = c.pk_inv_action_id, b = c.fk_tenant_id, c = c.budget_year }
                            equals new { a = d.fk_inv_action_id, b = d.fk_tenant_id, c = d.budget_year }
                        join e in IDBContext.tco_investments on new { a = d.fk_investment_id, b = d.fk_tenant_id }
                            equals new { a = e.pk_investment_id, b = e.fk_tenant_id }
                        join bc in IDBContext.tco_inv_budgetyear_config on new { a = d.fk_investment_id, b = d.fk_tenant_id, c = d.budget_year }
                            equals new { a = bc.fk_investment_id, b = bc.fk_tenant_id, c = bc.budget_year }
                        join ib in IDBContext.tco_inv_budgetyear_config
                            on new { a = e.pk_investment_id, b = e.fk_tenant_id, c = bc.budget_year }
                            equals new { a = ib.fk_investment_id, b = ib.fk_tenant_id, c = ib.budget_year }
                        where c.fk_tenant_id == userDetails.tenant_id
                              && c.action_type == actionType.Value
                              && c.isManuallyAdded == 0
                              && lineOrders.Contains(c.line_order)
                              && c.budget_year == budgetYear
                              && progCode.Contains(d.fk_prog_code)
                        group d by new { d.fk_investment_id, ib.investment_name, d.fk_change_id } into groupData
                        select new clsFinancing
                        {
                            investment_id = groupData.Key.fk_investment_id,
                            caption_name = groupData.Key.investment_name,
                            year_1_amount = groupData.Sum(x => x.year_1_amount),
                            year_2_amount = groupData.Sum(x => x.year_2_amount),
                            year_3_amount = groupData.Sum(x => x.year_3_amount),
                            year_4_amount = groupData.Sum(x => x.year_4_amount),
                            changeID = groupData.Key.fk_change_id,
                            tenantID = userDetails.tenant_id,
                            budgetYear = budgetYear
                        }).ToListAsync();
                }
                else
                {
                    data = await (from c in IDBContext.tfp_inv_header
                        join d in IDBContext.tfp_inv_transactions on new { a = c.pk_inv_action_id, b = c.fk_tenant_id, c = c.budget_year }
                            equals new { a = d.fk_inv_action_id, b = d.fk_tenant_id, c = d.budget_year }
                        join e in IDBContext.tco_investments on new { a = d.fk_investment_id, b = d.fk_tenant_id }
                            equals new { a = e.pk_investment_id, b = e.fk_tenant_id }
                        join bc in IDBContext.tco_inv_budgetyear_config on new { a = d.fk_investment_id, b = d.fk_tenant_id, c = d.budget_year }
                            equals new { a = bc.fk_investment_id, b = bc.fk_tenant_id, c = bc.budget_year }
                        join ib in IDBContext.tco_inv_budgetyear_config
                            on new { a = e.pk_investment_id, b = e.fk_tenant_id, c = bc.budget_year }
                            equals new { a = ib.fk_investment_id, b = ib.fk_tenant_id, c = ib.budget_year }
                        where c.fk_tenant_id == userDetails.tenant_id
                              && c.action_type == actionType.Value
                              && c.isManuallyAdded == 0
                              && lineOrders.Contains(c.line_order)
                              && c.budget_year == budgetYear
                              && progCode.Contains(d.fk_prog_code)
                              && statusIdList.Contains(bc.inv_status)
                        group d by new { d.fk_investment_id, ib.investment_name, d.fk_change_id } into groupData
                        select new clsFinancing
                        {
                            investment_id = groupData.Key.fk_investment_id,
                            caption_name = groupData.Key.investment_name,
                            year_1_amount = groupData.Sum(x => x.year_1_amount),
                            year_2_amount = groupData.Sum(x => x.year_2_amount),
                            year_3_amount = groupData.Sum(x => x.year_3_amount),
                            year_4_amount = groupData.Sum(x => x.year_4_amount),
                            changeID = groupData.Key.fk_change_id,
                            tenantID = userDetails.tenant_id,
                            budgetYear = budgetYear
                        }).ToListAsync();
                }
            }

            data = (from d in data
                join tbc in IDBContext.tfp_budget_changes on new { a = d.tenantID, b = d.changeID, c = d.budgetYear }
                    equals new { a = tbc.fk_tenant_id, b = tbc.pk_change_id, c = tbc.budget_year } into group2
                from g2 in group2.DefaultIfEmpty()
                select new clsFinancing
                {
                    investment_id = d.investment_id,
                    caption_name = d.caption_name,
                    year_1_amount = d.year_1_amount,
                    year_2_amount = d.year_2_amount,
                    year_3_amount = d.year_3_amount,
                    year_4_amount = d.year_4_amount,
                    orgBudgetFlag = g2 != null ? g2.org_budget_flag : -1,
                }).ToList();

            if (pageId != "YB_FINANCING")
            {
                data = data.Where(x => x.orgBudgetFlag != 0).ToList();
            }

            data = (from d in data
                group d by new { d.investment_id, d.caption_name } into groupData
                select new clsFinancing
                {
                    investment_id = groupData.Key.investment_id,
                    caption_name = groupData.Key.caption_name,
                    year_1_amount = groupData.Sum(x => x.year_1_amount),
                    year_2_amount = groupData.Sum(x => x.year_2_amount),
                    year_3_amount = groupData.Sum(x => x.year_3_amount),
                    year_4_amount = groupData.Sum(x => x.year_4_amount)
                }).ToList();

            data = data.Where(x => x.year_1_amount != 0 || x.year_2_amount != 0 || x.year_3_amount != 0 || x.year_4_amount != 0).ToList();
            return await FormatGetDataForClientIdAndLineOrderAsync(userID, data, budgetYear);
        }
        else
        {
            List<gco_tenants> gcoTenant = await (from g in kostraDbContext.gco_tenants
                select g).ToListAsync();
            List<clsFinancing> data = (from a in gcoTenant
                join b in gcoTenant on new { a = a.pk_id } equals new { a = b.client_id }
                join c in IDBContext.tfp_inv_header on new { a = a.pk_id } equals new { a = c.fk_tenant_id }
                join d in IDBContext.tfp_inv_transactions on new { a = c.fk_tenant_id, b = c.pk_inv_action_id, c = c.budget_year }
                    equals new { a = d.fk_tenant_id, b = d.fk_inv_action_id, c = d.budget_year }
                join e in IDBContext.tco_investments on new { a = d.fk_investment_id, b = d.fk_tenant_id }
                    equals new { a = e.pk_investment_id, b = e.fk_tenant_id }
                join bc in IDBContext.tco_inv_budgetyear_config on new { a = d.fk_investment_id, b = d.fk_tenant_id, c = d.budget_year }
                    equals new { a = bc.fk_investment_id, b = bc.fk_tenant_id, c = bc.budget_year }
                join ib in IDBContext.tco_inv_budgetyear_config
                    on new { a = e.pk_investment_id, b = e.fk_tenant_id, c = bc.budget_year }
                    equals new { a = ib.fk_investment_id, b = ib.fk_tenant_id, c = ib.budget_year }
                where (a.client_id == a.pk_id
                       && a.pk_id != b.pk_id
                       && a.pk_id == userDetails.tenant_id
                       && c.action_type == 1
                       && c.isManuallyAdded == 0
                       && lineOrders.Contains(c.line_order)
                       && c.budget_year == budgetYear
                       && e.fk_tenant_id == clientID
                       && progCode.Contains(d.fk_prog_code)
                       && statusIdList.Contains(bc.inv_status)
                    )
                group d by new { b.pk_id, b.tenant_name, d.fk_investment_id, ib.investment_name, d.fk_change_id } into groupData
                select new clsFinancing
                {
                    investment_id = groupData.Key.fk_investment_id,
                    caption_name = groupData.Key.investment_name,
                    year_1_amount = groupData.Sum(x => x.year_1_amount),
                    year_2_amount = groupData.Sum(x => x.year_2_amount),
                    year_3_amount = groupData.Sum(x => x.year_3_amount),
                    year_4_amount = groupData.Sum(x => x.year_4_amount),
                    changeID = groupData.Key.fk_change_id,
                    tenantID = userDetails.tenant_id,
                    budgetYear = budgetYear
                }).ToList();

            data = (from d in data
                join tbc in IDBContext.tfp_budget_changes on new { a = d.tenantID, b = d.changeID, c = d.budgetYear }
                    equals new { a = tbc.fk_tenant_id, b = tbc.pk_change_id, c = tbc.budget_year } into group2
                from g2 in group2.DefaultIfEmpty()
                select new clsFinancing
                {
                    investment_id = d.investment_id,
                    caption_name = d.caption_name,
                    year_1_amount = d.year_1_amount,
                    year_2_amount = d.year_2_amount,
                    year_3_amount = d.year_3_amount,
                    year_4_amount = d.year_4_amount,
                    orgBudgetFlag = g2 != null ? g2.org_budget_flag : -1,
                }).ToList();

            if (pageId != "YB_FINANCING")
            {
                data = data.Where(x => x.orgBudgetFlag != 0).ToList();
            }

            data = (from d in data
                group d by new { d.investment_id, d.caption_name } into groupData
                select new clsFinancing
                {
                    investment_id = groupData.Key.investment_id,
                    caption_name = groupData.Key.caption_name,
                    year_1_amount = groupData.Sum(x => x.year_1_amount),
                    year_2_amount = groupData.Sum(x => x.year_2_amount),
                    year_3_amount = groupData.Sum(x => x.year_3_amount),
                    year_4_amount = groupData.Sum(x => x.year_4_amount)
                }).ToList();

            data = data.Where(x => x.year_1_amount != 0 || x.year_2_amount != 0 || x.year_3_amount != 0 || x.year_4_amount != 0).ToList();
            return await FormatGetDataForClientIdAndLineOrderAsync(userID, data, budgetYear);
        }
    }



    public dynamic GetData(string userID, int lineOrder, int? invActionId, int actionType, int budgetYear, int activeChangeId = 0)
    {
        return GetDataAsync(userID, lineOrder, invActionId, actionType, budgetYear, activeChangeId).GetAwaiter().GetResult();
    }



    public async Task<dynamic> GetDataAsync(string userID, int lineOrder, int? invActionId, int actionType, int budgetYear, int activeChangeId = 0)
    {
        UserData userDetails = await pUtility.GetUserDetailsAsync(userID);
        var orgVersionContent = await pUtility.GetOrgVersionSpecificContentAsync(userID, pUtility.GetForecastPeriod(budgetYear, 1));
        TenantDBContext IDBContext = await pUtility.GetTenantDBContextAsync();
        Dictionary<string, clsLanguageString> langStringValues = await pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ConsequenceAdjustedBudget");
        Dictionary<string, clsLanguageString> langStringFormat = await pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "NumberFormats");
        string source = string.Empty;
        dynamic defaults = await GetDefaultsBasedOnActionTypeAndLineOrderAsync(userID, actionType, lineOrder, budgetYear);
        dynamic accountData = defaults.account;
        dynamic departmentData = defaults.department;
        dynamic functionData = defaults.functionn;

        dynamic lstAjstmt = pConsequenceAdjustedBudget.GetAdjustmentCodes(userID);
        dynamic lstAlter = pConsequenceAdjustedBudget.GetAlterCodes(userID, actionType);

        List<tco_free_dim_values> lstFreeDimValues = (await pUtility.GetFreeDimValuesAsync(userID)).ToList();

        var changeIds = await IDBContext.tfp_budget_changes.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear && x.status != 1).Select(x => x.pk_change_id).ToListAsync();

        List<clsFinancingPopupData> data = new List<clsFinancingPopupData>();

        source = "data1";
        if (lineOrder == 0 && invActionId != null)
        {
            data = await (from a in IDBContext.tfp_inv_header
                where a.fk_tenant_id == userDetails.tenant_id && a.line_order == lineOrder && a.isManuallyAdded == 1 && a.action_type == actionType && a.pk_inv_action_id == invActionId.Value && a.budget_year == budgetYear
                select new clsFinancingPopupData
                {
                    pk_inv_action_id = a.pk_inv_action_id,
                    action_name = a.action_name,
                    description = a.description,
                    pk_id = 0,
                    fk_account_code = string.Empty,
                    department_code_ = string.Empty,
                    fk_function_code_ = string.Empty,
                    year_1_amount = 0.0M,
                    year_2_amount = 0.0M,
                    year_3_amount = 0.0M,
                    year_4_amount = 0.0M,
                    fk_change_id = 0,
                    fk_project_code = string.Empty,
                    freedimCode1 = string.Empty,
                    freedimName1 = string.Empty,
                    freedimCode2 = string.Empty,
                    freedimName2 = string.Empty,
                    freedimCode3 = string.Empty,
                    freedimName3 = string.Empty,
                    freedimCode4 = string.Empty,
                    freedimName4 = string.Empty,
                    adjustmentCode = new AdjustmentCodeHelper() { key = string.Empty, value = string.Empty },
                    alterCode = new AlterCodeHelper() { key = string.Empty, value = string.Empty }
                }).ToListAsync();
            source = "data2";
        }

        List<freedimDefinition> freeDimColumns = (await pUtility.GetFreeDimDefinitionsBasedOnPageIdAsync(userID, "financing")).ToList();

        dynamic actions = new JObject();
        dynamic emptyAd = new JObject();

        emptyAd.id = 0;
        string accCode = accountData.fk_account_code;
        string accName = accountData.display_name;
        emptyAd.account = GenerateDynamicAccountObject(accCode, accName);

        string deptCode = departmentData.fk_department_code;
        string deptName = departmentData.department_name;
        emptyAd.department = GenerateDynamicDepartmentObject(deptCode, deptName);

        string funcCode = functionData.fk_function_code;
        string funcName = functionData.function_name;
        emptyAd.functionn = GenerateDynamicFunctionObject(funcCode, funcName);

        emptyAd.project = GenerateDynamicProjectDimObject("", "");

        if (freeDimColumns.Count() > 0)
        {
            emptyAd.freeDim1 = GenerateDynamicFreeDimObject("", "");
        }
        if (freeDimColumns.Count() > 1)
        {
            emptyAd.freeDim2 = GenerateDynamicFreeDimObject("", "");
        }
        else if (freeDimColumns.Count() > 2)
        {
            emptyAd.freeDim3 = GenerateDynamicFreeDimObject("", "");
        }
        else if (freeDimColumns.Count() > 3)
        {
            emptyAd.freeDim4 = GenerateDynamicFreeDimObject("", "");
        }

        if (lstAjstmt.Count > 1)
        {
            emptyAd.adjustmentCode = GenerateDynamicAdjustmentCodeObject("", "");
        }

        if (lstAlter.Count > 1)
        {
            emptyAd.alterCode = GenerateDynamicAdjustmentCodeObject("", "");
        }

        emptyAd.year1Amount = 0;
        emptyAd.year2Amount = 0;
        emptyAd.year3Amount = 0;
        emptyAd.year4Amount = 0;

        dynamic gridData = new JArray();
        dynamic gridactiveBtData = new JArray();

        if (data.Count() > 0)
        {
            actions.actionId = data.FirstOrDefault().pk_inv_action_id;
            actions.actionName = data.FirstOrDefault().action_name;
            actions.actionDescription = await pUtility.GetDataFromAzureAsync(userID, data.FirstOrDefault().description.ToString(), "WADTenantData");
            actions.isEditable = lineOrder == 0 ? true : false;
            if (lineOrder == 0 && invActionId != null)
            {
                data = await (from a in IDBContext.tfp_inv_header
                    join b in IDBContext.tfp_inv_transactions on new { a = a.fk_tenant_id, b = a.pk_inv_action_id }
                        equals new { a = b.fk_tenant_id, b = b.fk_inv_action_id }
                    join p1 in IDBContext.tco_projects on new { a = b.fk_project_code, b = a.fk_tenant_id }
                        equals new { a = p1.pk_project_code, b = p1.fk_tenant_id } into lojproj
                    from pj in lojproj.DefaultIfEmpty()
                    where a.fk_tenant_id == userDetails.tenant_id && a.line_order == lineOrder && a.isManuallyAdded == 1 && a.action_type == actionType && a.pk_inv_action_id == invActionId
                          && a.budget_year == budgetYear
                    select new clsFinancingPopupData
                    {
                        pk_inv_action_id = a.pk_inv_action_id,
                        action_name = a.action_name,
                        description = a.description,
                        pk_id = b.pk_id,
                        fk_account_code = b.fk_account_code,
                        department_code_ = b.department_code_,
                        fk_function_code_ = b.fk_function_code_,
                        year_1_amount = b.year_1_amount,
                        year_2_amount = b.year_2_amount,
                        year_3_amount = b.year_3_amount,
                        year_4_amount = b.year_4_amount,
                        fk_change_id = b.fk_change_id,
                        fk_project_code = b.fk_project_code,
                        freedimCode1 = b.free_dim_1 == null ? String.Empty : b.free_dim_1,
                        freedimName1 = string.Empty,

                        freedimCode2 = b.free_dim_2 == null ? String.Empty : b.free_dim_2,
                        freedimName2 = string.Empty,

                        freedimCode3 = b.free_dim_3 == null ? String.Empty : b.free_dim_3,
                        freedimName3 = string.Empty,

                        freedimCode4 = b.free_dim_4 == null ? String.Empty : b.free_dim_4,
                        freedimName4 = string.Empty,
                    }).ToListAsync();

                data.ForEach(x =>
                {
                    x.freedimName1 = lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode1) == null ? string.Empty :
                        lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode1).description;

                    x.freedimName2 = lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode2) == null ? string.Empty :
                        lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode2).description;

                    x.freedimName3 = lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode3) == null ? string.Empty :
                        lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode3).description;

                    x.freedimName4 = lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode4) == null ? string.Empty :
                        lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode4).description;
                });
            }
            else
            {
                data = await (from a in IDBContext.tfp_inv_header
                    join b in IDBContext.tfp_inv_transactions on new { a = a.fk_tenant_id, b = a.pk_inv_action_id }
                        equals new { a = b.fk_tenant_id, b = b.fk_inv_action_id }
                    join p1 in IDBContext.tco_projects on new { a = b.fk_project_code, b = a.fk_tenant_id }
                        equals new { a = p1.pk_project_code, b = p1.fk_tenant_id } into lojproj
                    from pj in lojproj.DefaultIfEmpty()
                    where a.fk_tenant_id == userDetails.tenant_id && a.line_order == lineOrder && a.isManuallyAdded == 0 && a.action_type == actionType && a.budget_year == budgetYear
                    select new clsFinancingPopupData
                    {
                        pk_inv_action_id = a.pk_inv_action_id,
                        action_name = a.action_name,
                        description = a.description,
                        pk_id = b.pk_id,
                        fk_account_code = b.fk_account_code,
                        department_code_ = b.department_code_,
                        fk_function_code_ = b.fk_function_code_,
                        year_1_amount = b.year_1_amount,
                        year_2_amount = b.year_2_amount,
                        year_3_amount = b.year_3_amount,
                        year_4_amount = b.year_4_amount,
                        fk_change_id = b.fk_change_id,
                        fk_project_code = b.fk_project_code,
                        freedimCode1 = b.free_dim_1 == null ? String.Empty : b.free_dim_1,
                        freedimName1 = string.Empty,

                        freedimCode2 = b.free_dim_2 == null ? String.Empty : b.free_dim_2,
                        freedimName2 = string.Empty,

                        freedimCode3 = b.free_dim_3 == null ? String.Empty : b.free_dim_3,
                        freedimName3 = string.Empty,

                        freedimCode4 = b.free_dim_4 == null ? String.Empty : b.free_dim_4,
                        freedimName4 = string.Empty,
                        adjustmentCode = b.fk_adjustment_code == null ? new Helpers.AdjustmentCodeHelper() { key = string.Empty, value = string.Empty } : new Helpers.AdjustmentCodeHelper() { key = b.fk_adjustment_code, value = b.fk_adjustment_code },
                        alterCode = b.fk_alter_code == null ? new Helpers.AlterCodeHelper() { key = string.Empty, value = string.Empty } : new Helpers.AlterCodeHelper() { key = b.fk_alter_code, value = b.fk_alter_code },
                    }).ToListAsync();

                data.ForEach(x =>
                {
                    x.freedimName1 = lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode1) == null ? string.Empty :
                        lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode1).description;

                    x.freedimName2 = lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode2) == null ? string.Empty :
                        lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode2).description;

                    x.freedimName3 = lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode3) == null ? string.Empty :
                        lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode3).description;

                    x.freedimName4 = lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode4) == null ? string.Empty :
                        lstFreeDimValues.FirstOrDefault(y => y.free_dim_code == x.freedimCode4).description;
                });
            }

            if (data.Count() == 0)
            {
                gridData.Add(emptyAd);
                actions.Add("gridData", gridData);

                gridactiveBtData.Add(emptyAd);
                actions.Add("gridactiveBtData", gridactiveBtData);
            }
            else
            {
                var nonActiveBudgetChangeData = data.Where(x => (changeIds.Contains(x.fk_change_id) || x.fk_change_id == 0));
                var activeBudgetChangeData = data.Where(x => (x.fk_change_id == activeChangeId));

                gridData = new JArray();
                foreach (var a in nonActiveBudgetChangeData)
                {
                    dynamic ad = new JObject();
                    ad.id = a.pk_id;

                    ad.account = GenerateDynamicAccountObject(a.fk_account_code, (await IDBContext.tco_accounts.FirstOrDefaultAsync(x => x.pk_tenant_id == userDetails.tenant_id && x.pk_account_code == a.fk_account_code)).display_name);

                    ad.department = GenerateDynamicDepartmentObject(a.department_code_, a.department_code_ + "-" + ((await pUtility.GetTenantDepartmentsForSelectedOrgIdAsync(orgVersionContent, userID, null)).Where(x => x.departmentValue == a.department_code_).FirstOrDefault() != null ?
                        (await pUtility.GetTenantDepartmentsForSelectedOrgIdAsync(orgVersionContent, userID, null)).Where(x => x.departmentValue == a.department_code_).FirstOrDefault().departmentText : string.Empty));

                    ad.functionn = GenerateDynamicFunctionObject(a.fk_function_code_, a.fk_function_code_ + "-" + pConsequenceAdjustedBudget.GetFunctions(userID, "").Where(x => x.functionValue == a.fk_function_code_).FirstOrDefault().functionText);

                    ad.project = GenerateDynamicProjectDimObject(a.fk_project_code, a.fk_project_code);

                    if (freeDimColumns.Count() > 0)
                    {
                        ad.freeDim1 = GenerateDynamicFreeDimObject(a.freedimCode1, a.freedimName1);
                    }
                    if (freeDimColumns.Count() > 1)
                    {
                        ad.freeDim2 = GenerateDynamicFreeDimObject(a.freedimCode2, a.freedimName2);
                    }
                    else if (freeDimColumns.Count() > 2)
                    {
                        ad.freeDim3 = GenerateDynamicFreeDimObject(a.freedimCode3, a.freedimName3);
                    }
                    else if (freeDimColumns.Count() > 3)
                    {
                        ad.freeDim4 = GenerateDynamicFreeDimObject(a.freedimCode4, a.freedimName4);
                    }

                    ad.year1Amount = a.year_1_amount / 1000;
                    ad.year2Amount = a.year_2_amount / 1000;
                    ad.year3Amount = a.year_3_amount / 1000;
                    ad.year4Amount = a.year_4_amount / 1000;
                    gridData.Add(ad);
                }
                actions.Add("gridData", gridData);

                gridactiveBtData = new JArray();
                foreach (var a in activeBudgetChangeData)
                {
                    dynamic ad = new JObject();
                    ad.id = a.pk_id;

                    ad.account = GenerateDynamicAccountObject(a.fk_account_code, (await IDBContext.tco_accounts.FirstOrDefaultAsync(x => x.pk_tenant_id == userDetails.tenant_id && x.pk_account_code == a.fk_account_code)).display_name);

                    ad.department = GenerateDynamicDepartmentObject(a.department_code_, a.department_code_ + "-" + ((await pUtility.GetTenantDepartmentsForSelectedOrgIdAsync(orgVersionContent, userID, null)).Where(x => x.departmentValue == a.department_code_).FirstOrDefault() != null ?
                        (await pUtility.GetTenantDepartmentsForSelectedOrgIdAsync(orgVersionContent, userID, null)).Where(x => x.departmentValue == a.department_code_).FirstOrDefault().departmentText : string.Empty));

                    ad.functionn = GenerateDynamicFunctionObject(a.fk_function_code_, a.fk_function_code_ + "-" + pConsequenceAdjustedBudget.GetFunctions(userID, "").Where(x => x.functionValue == a.fk_function_code_).FirstOrDefault().functionText);

                    ad.project = GenerateDynamicProjectDimObject(a.fk_project_code, a.fk_project_code);

                    if (freeDimColumns.Count() > 0)
                    {
                        ad.freeDim1 = GenerateDynamicFreeDimObject(a.freedimCode1, a.freedimName1);
                    }
                    if (freeDimColumns.Count() > 1)
                    {
                        ad.freeDim2 = GenerateDynamicFreeDimObject(a.freedimCode2, a.freedimName2);
                    }
                    else if (freeDimColumns.Count() > 2)
                    {
                        ad.freeDim3 = GenerateDynamicFreeDimObject(a.freedimCode3, a.freedimName3);
                    }
                    else if (freeDimColumns.Count() > 3)
                    {
                        ad.freeDim4 = GenerateDynamicFreeDimObject(a.freedimCode4, a.freedimName4);
                    }

                    if (lstAjstmt.Count > 1)
                    {
                        ad.adjustmentCode = GenerateDynamicAdjustmentCodeObject(a.adjustmentCode.key, a.adjustmentCode.value);
                    }
                    if (lstAlter.Count > 1)
                    {
                        ad.alterCode = GenerateDynamicAlterCodeObject(a.alterCode.key, a.alterCode.value);
                    }

                    ad.year1Amount = a.year_1_amount / 1000;
                    ad.year2Amount = a.year_2_amount / 1000;
                    ad.year3Amount = a.year_3_amount / 1000;
                    ad.year4Amount = a.year_4_amount / 1000;
                    gridactiveBtData.Add(ad);
                }
                actions.Add("gridactiveBtData", gridactiveBtData);
            }
        }
        else
        {
            if (source == "data1")
            {
                actions.actionId = (await IDBContext.tfp_inv_header.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.line_order == lineOrder && x.action_type == actionType && x.budget_year == budgetYear)).pk_inv_action_id;
                actions.actionName = (await IDBContext.tfp_inv_header.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.line_order == lineOrder && x.action_type == actionType && x.budget_year == budgetYear)).action_name;
                actions.actionDescription = await pUtility.GetDataFromAzureAsync(userID, (await IDBContext.tfp_inv_header.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.line_order == lineOrder && x.action_type == actionType && x.budget_year == budgetYear)).description.ToString(), "WADTenantData");
                actions.isEditable = lineOrder == 0 ? true : false;

                gridData = new JArray();
                gridData.Add(emptyAd);
                actions.Add("gridData", gridData);

                gridactiveBtData = new JArray();
                gridactiveBtData.Add(emptyAd);
                actions.Add("gridactiveBtData", gridactiveBtData);
            }
        }

        dynamic columnFields = new JArray();

        columnFields.Add("id");
        columnFields.Add("account");
        columnFields.Add("department");
        columnFields.Add("functionn");
        columnFields.Add("project");

        for (int i = 1; i <= freeDimColumns.Count(); i++)
        {
            columnFields.Add("freeDim" + i.ToString());
        }

        if (lstAjstmt.Count > 1)
        {
            columnFields.Add("adjustmentCode"); ;
        }
        if (lstAlter.Count > 1)
        {
            columnFields.Add("alterCode");
        }

        columnFields.Add("year1Amount");
        columnFields.Add("year2Amount");
        columnFields.Add("year3Amount");
        columnFields.Add("year4Amount");

        actions.Add("columnFields", columnFields);

        dynamic columnTitles = new JArray();
        columnTitles.Add("id");
        columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "account_text")).Value).LangText);
        columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "department_text")).Value).LangText);
        columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "function_text")).Value).LangText);
        columnTitles.Add(((langStringValues.FirstOrDefault(v => v.Key == "fp_project_text")).Value).LangText);
        for (int i = 1; i <= freeDimColumns.Count; i++)
        {
            string freedim = "free_dim_" + i;
            columnTitles.Add(freeDimColumns.Where(x => x.freeDimColumn == freedim).Select(x => x.freeDimHeader).First().ToString());
        }

        Dictionary<string, clsLanguageString> langStringValuesFP = await pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "FinancialPlan");

        if (lstAjstmt.Count > 1)
        {
            columnTitles.Add(((langStringValuesFP.FirstOrDefault(v => v.Key == "fp_adjustment_code")).Value).LangText);
        }
        if (lstAlter.Count > 1)
        {
            columnTitles.Add(((langStringValuesFP.FirstOrDefault(v => v.Key == "fp_alter_code_text")).Value).LangText);
        }
        columnTitles.Add(budgetYear.ToString());
        columnTitles.Add((budgetYear + 1).ToString());
        columnTitles.Add((budgetYear + 2).ToString());
        columnTitles.Add((budgetYear + 3).ToString());

        actions.Add("columnTitles", columnTitles);

        actions.amountFormat = ((langStringFormat.FirstOrDefault(v => v.Key == "amount")).Value).LangText;
        return actions;
    }



    public dynamic GetFinancingSummary(string userID, string pageId, string InvestmentProgram, int budgetYear)
    {
        return GetFinancingSummaryAsync(userID, pageId, InvestmentProgram, budgetYear).GetAwaiter().GetResult();
    }



    public async Task<dynamic> GetFinancingSummaryAsync(string userID, string pageId, string InvestmentProgram, int budgetYear)
    {
        UserData userDetails = await pUtility.GetUserDetailsAsync(userID);
        TenantDBContext IDBContext = await pUtility.GetTenantDBContextAsync();
        Dictionary<string, clsLanguageString> langStringValuesCommon = await pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Common");
        Dictionary<string, clsLanguageString> langStringFormat = await pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "NumberFormats");

        IEnumerable<KeyValueHelper> budgetChangeData = await GetActiveBudgetChangeIdAsync(userID, pageId, budgetYear);
        int activeChangeId = budgetChangeData.Any() ? budgetChangeData.FirstOrDefault().KeyId : -2;

        List<string> lstprogramCodes = InvestmentProgram.ToString() == "0" ? (await pUtility.GetInvestmentsProgramsAsync(userID)).Select(x => x.key).ToList() : new List<string>() { InvestmentProgram.ToString() };
        lstprogramCodes = lstprogramCodes.Count() == 0 ? new List<string> { "" } : lstprogramCodes;

        //New and Existing Investments for logged in tenant
        List<clsFinancing> tenantData = await GetTenantDataAsync(IDBContext, userDetails, budgetYear, lstprogramCodes, pageId, activeChangeId);

        List<clsFinancing> manuallyAddedTenantData = await GetManuallyAddedTenantDataAsync(IDBContext, userDetails, budgetYear, lstprogramCodes, pageId, activeChangeId);

        //New and Existing Investments for sub tenant
        List<clsFinancing> subTenantData = new List<Helpers.clsFinancing>();

        List<clsFinancing> tenantDataForActiveChange = await GetTenantDataForActiveChangeAsync(IDBContext, userDetails, budgetYear, activeChangeId, lstprogramCodes);
        List<clsFinancing> manuallyAddedTenantDataForActiveChange = await GetManuallyAddedTenantDataForActiveChangeAsync(IDBContext, userDetails, budgetYear, activeChangeId, lstprogramCodes);
        List<clsFinancing> subTenantDataForActiveChange = new List<Helpers.clsFinancing>();

        List<int> FinancingActionTypes = new List<int>()
        {
            Convert.ToInt32(clsConstants.FinancingActionType.External),
            Convert.ToInt32(clsConstants.FinancingActionType.Internal)
        }.OrderBy(x => x).ToList();

        var dataBeforeFilter = await (from ih in IDBContext.tfp_inv_header
            join it in IDBContext.tfp_inv_transactions on new { a = ih.fk_tenant_id, b = ih.pk_inv_action_id }
                equals new { a = it.fk_tenant_id, b = it.fk_inv_action_id }
            join tbc in IDBContext.tfp_budget_changes on new { a = it.fk_tenant_id, b = it.fk_change_id }
                equals new { a = tbc.fk_tenant_id, b = tbc.pk_change_id } into group2
            from g2 in group2.DefaultIfEmpty()
            where (ih.fk_tenant_id == userDetails.tenant_id
                   && ih.budget_year == budgetYear
                   && FinancingActionTypes.Contains(ih.action_type))
            select new
            {
                action_type = ih.action_type,
                investment_id = ih.pk_inv_action_id,
                caption_name = ih.action_name,
                line_order = ih.line_order,
                isManuallyAdded = ih.isManuallyAdded,
                year_1_amount = (it.year_1_amount),
                year_2_amount = (it.year_2_amount),
                year_3_amount = (it.year_3_amount),
                year_4_amount = (it.year_4_amount),
                accountCode = it.fk_account_code,
                departmentCode = it.department_code_,
                functioncode = it.fk_function_code_,
                projectCode = it.fk_project_code,
                freedim1 = it.free_dim_1,
                freedim2 = it.free_dim_2,
                freedim3 = it.free_dim_3,
                freedim4 = it.free_dim_4,
                programCode = it.fk_prog_code,
                orgBudgetFlag = g2 != null ? g2.org_budget_flag : -1
            }).OrderBy(x => x.isManuallyAdded).ThenBy(x => x.line_order).ToListAsync();

        if (pageId != "YB_FINANCING")
        {
            dataBeforeFilter = dataBeforeFilter.Where(x => x.orgBudgetFlag != 0).ToList();
        }
        dataBeforeFilter = dataBeforeFilter.Where(x => lstprogramCodes.Contains(x.programCode)).OrderBy(x => x.line_order).ToList();

        var dataBeforFilterFroChange = await (from ih in IDBContext.tfp_inv_header
            join it in IDBContext.tfp_inv_transactions on new { a = ih.fk_tenant_id, b = ih.pk_inv_action_id }
                equals new { a = it.fk_tenant_id, b = it.fk_inv_action_id }
            join tbc in IDBContext.tfp_budget_changes on new { a = it.fk_tenant_id, b = it.fk_change_id }
                equals new { a = tbc.fk_tenant_id, b = tbc.pk_change_id } into group2
            from g2 in group2.DefaultIfEmpty()
            where (ih.fk_tenant_id == userDetails.tenant_id
                   && ih.budget_year == budgetYear && it.fk_change_id == activeChangeId
                   && FinancingActionTypes.Contains(ih.action_type))
            select new
            {
                action_type = ih.action_type,
                investment_id = ih.pk_inv_action_id,
                caption_name = ih.action_name,
                line_order = ih.line_order,
                isManuallyAdded = ih.isManuallyAdded,
                year_1_amount = (it.year_1_amount),
                year_2_amount = (it.year_2_amount),
                year_3_amount = (it.year_3_amount),
                year_4_amount = (it.year_4_amount),
                changeId = it.fk_change_id,
                accountCode = it.fk_account_code,
                departmentCode = it.department_code_,
                functioncode = it.fk_function_code_,
                projectCode = it.fk_project_code,
                freedim1 = it.free_dim_1,
                freedim2 = it.free_dim_2,
                freedim3 = it.free_dim_3,
                freedim4 = it.free_dim_4,
                programCode = it.fk_prog_code,
                orgBudgetFlag = g2 != null ? g2.org_budget_flag : -1
            }).OrderBy(x => x.isManuallyAdded).ThenBy(x => x.line_order).ToListAsync();

        if (pageId != "YB_FINANCING")
        {
            dataBeforFilterFroChange = dataBeforFilterFroChange.Where(x => x.orgBudgetFlag != 0).ToList();
        }
        dataBeforFilterFroChange = dataBeforFilterFroChange.Where(x => lstprogramCodes.Contains(x.programCode)).OrderBy(x => x.line_order).ToList();
        List<clsFinancing> internalAndExternalFinancingdata = (from d in dataBeforeFilter
            group d by new { d.investment_id, d.caption_name, d.action_type, d.line_order, d.isManuallyAdded } into groupData
            select new clsFinancing
            {
                action_type = groupData.Key.action_type,
                investment_id = groupData.Key.investment_id,
                caption_name = groupData.Key.caption_name,
                line_order = groupData.Key.line_order,
                isManuallyAdded = groupData.Key.isManuallyAdded,
                year_1_amount = groupData.Sum(x => x.year_1_amount) / 1000,
                year_2_amount = groupData.Sum(x => x.year_2_amount) / 1000,
                year_3_amount = groupData.Sum(x => x.year_3_amount) / 1000,
                year_4_amount = groupData.Sum(x => x.year_4_amount) / 1000
            }).OrderBy(x => x.isManuallyAdded).ThenBy(x => x.line_order).ToList();

        List<clsFinancing> internalAndExternalFinancingdataChange = (from d in dataBeforFilterFroChange
            group d by new { d.investment_id, d.caption_name, d.action_type, d.line_order, d.isManuallyAdded, d.changeId } into groupData
            select new clsFinancing
            {
                action_type = groupData.Key.action_type,
                investment_id = groupData.Key.investment_id,
                caption_name = groupData.Key.caption_name,
                line_order = groupData.Key.line_order,
                isManuallyAdded = groupData.Key.isManuallyAdded,
                year_1_amount = groupData.Sum(x => x.year_1_amount) / 1000,
                year_2_amount = groupData.Sum(x => x.year_2_amount) / 1000,
                year_3_amount = groupData.Sum(x => x.year_3_amount) / 1000,
                year_4_amount = groupData.Sum(x => x.year_4_amount) / 1000,
                year_1_amount_with_fk_change_id_zero = 0,
                fk_change_id = groupData.Key.changeId
            }).OrderBy(x => x.isManuallyAdded).ThenBy(x => x.line_order).ToList();

        List<clsFinancing> internalFinancingData = internalAndExternalFinancingdata.Where(x => x.action_type == FinancingActionTypes[1]).ToList();
        List<clsFinancing> externalFinancingData = internalAndExternalFinancingdata.Where(x => x.action_type == FinancingActionTypes[0]).ToList();

        List<clsFinancing> internalFinancingDataChange = internalAndExternalFinancingdataChange.Where(x => x.action_type == FinancingActionTypes[1]).ToList();
        List<clsFinancing> externalFinancingDataChange = internalAndExternalFinancingdataChange.Where(x => x.action_type == FinancingActionTypes[0]).ToList();

        Dictionary<string, clsLanguageString> langStringValuesFinancing = await pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Financing");

        dynamic data = new JObject();

        dynamic columnTitles = new JArray();

        columnTitles.Add(((langStringValuesFinancing.FirstOrDefault(v => v.Key == "financingSummaryDesc")).Value).LangText);
        columnTitles.Add(budgetYear.ToString());
        columnTitles.Add(" ");
        columnTitles.Add((budgetYear + 1).ToString());
        columnTitles.Add(" ");
        columnTitles.Add((budgetYear + 2).ToString());
        columnTitles.Add(" ");
        columnTitles.Add((budgetYear + 3).ToString());
        columnTitles.Add(" ");
        columnTitles.Add(((langStringValuesFinancing.FirstOrDefault(v => v.Key == "fin_budget_change")).Value).LangText + " " + budgetYear.ToString());
        columnTitles.Add(((langStringValuesFinancing.FirstOrDefault(v => v.Key == "fin_budget_change")).Value).LangText + " " + (budgetYear + 1).ToString());
        columnTitles.Add(((langStringValuesFinancing.FirstOrDefault(v => v.Key == "fin_budget_change")).Value).LangText + " " + (budgetYear + 2).ToString());
        columnTitles.Add(((langStringValuesFinancing.FirstOrDefault(v => v.Key == "fin_budget_change")).Value).LangText + " " + (budgetYear + 3).ToString());
        dynamic columnFields = new JArray();

        columnFields.Add("description");
        columnFields.Add("year1Amount");
        columnFields.Add("year1Percentage");
        columnFields.Add("year2Amount");
        columnFields.Add("year2Percentage");
        columnFields.Add("year3Amount");
        columnFields.Add("year3Percentage");
        columnFields.Add("year4Amount");
        columnFields.Add("year4Percentage");
        columnFields.Add("changeYear1");
        columnFields.Add("changeYear2");
        columnFields.Add("changeYear3");
        columnFields.Add("changeYear4");
        dynamic jsonData = new JArray();

        List<decimal> needForFinancingTotals = new List<decimal>()
        {
            (tenantData.Count() > 0 ? tenantData.Sum(x=>x.year_1_amount ) : 0) +( manuallyAddedTenantData.Count() > 0 ? manuallyAddedTenantData.Sum(x=>x.year_1_amount) : 0) + (subTenantData.Count() > 0 ? subTenantData.Sum(x=>x.year_1_amount) : 0),
            (tenantData.Count() > 0 ? tenantData.Sum(x=>x.year_2_amount ) : 0) +( manuallyAddedTenantData.Count() > 0 ? manuallyAddedTenantData.Sum(x=>x.year_2_amount) : 0) + (subTenantData.Count() > 0 ? subTenantData.Sum(x=>x.year_2_amount) : 0),
            (tenantData.Count() > 0 ? tenantData.Sum(x=>x.year_3_amount ) : 0) +( manuallyAddedTenantData.Count() > 0 ? manuallyAddedTenantData.Sum(x=>x.year_3_amount) : 0) + (subTenantData.Count() > 0 ? subTenantData.Sum(x=>x.year_3_amount) : 0),
            (tenantData.Count() > 0 ? tenantData.Sum(x=>x.year_4_amount ) : 0) +( manuallyAddedTenantData.Count() > 0 ? manuallyAddedTenantData.Sum(x=>x.year_4_amount) : 0) + (subTenantData.Count() > 0 ? subTenantData.Sum(x=>x.year_4_amount) : 0),
            (tenantData.Count() > 0 ? tenantData.Sum(x=>x.year_1_amount_with_fk_change_id_zero ) : 0) +( manuallyAddedTenantData.Count() > 0 ? manuallyAddedTenantData.Sum(x=>x.year_1_amount_with_fk_change_id_zero) : 0) + (subTenantData.Count() > 0 ? subTenantData.Sum(x=>x.year_1_amount_with_fk_change_id_zero) : 0),
            (tenantData.Count() > 0 ? tenantData.Sum(x=>x.activeBudgetChangeAmount ) : 0) +( manuallyAddedTenantData.Count() > 0 ? manuallyAddedTenantData.Sum(x=>x.activeBudgetChangeAmount) : 0) + (subTenantData.Count() > 0 ? subTenantData.Sum(x=>x.activeBudgetChangeAmount) : 0)
        };
        List<decimal> needForFinancingChangeTotals = new List<decimal>()
        {
            (tenantDataForActiveChange.Count() > 0 ? tenantDataForActiveChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.year_1_amount ) : 0) +( manuallyAddedTenantDataForActiveChange.Count() > 0 ? manuallyAddedTenantDataForActiveChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.year_1_amount) : 0) + (subTenantDataForActiveChange.Count() > 0 ? subTenantDataForActiveChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.year_1_amount) : 0),
            (tenantDataForActiveChange.Count() > 0 ? tenantDataForActiveChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.year_2_amount ) : 0) +( manuallyAddedTenantDataForActiveChange.Count() > 0 ? manuallyAddedTenantDataForActiveChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.year_2_amount) : 0) + (subTenantDataForActiveChange.Count() > 0 ? subTenantDataForActiveChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.year_2_amount) : 0),
            (tenantDataForActiveChange.Count() > 0 ? tenantDataForActiveChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.year_3_amount ) : 0) +( manuallyAddedTenantDataForActiveChange.Count() > 0 ? manuallyAddedTenantDataForActiveChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.year_3_amount) : 0) + (subTenantDataForActiveChange.Count() > 0 ? subTenantDataForActiveChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.year_3_amount) : 0),
            (tenantDataForActiveChange.Count() > 0 ? tenantDataForActiveChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.year_4_amount ) : 0) +( manuallyAddedTenantDataForActiveChange.Count() > 0 ? manuallyAddedTenantDataForActiveChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.year_4_amount) : 0) + (subTenantDataForActiveChange.Count() > 0 ? subTenantDataForActiveChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.year_4_amount) : 0),
            (tenantDataForActiveChange.Count() > 0 ? tenantDataForActiveChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.year_1_amount_with_fk_change_id_zero ) : 0) +( manuallyAddedTenantDataForActiveChange.Count() > 0 ? manuallyAddedTenantDataForActiveChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.year_1_amount_with_fk_change_id_zero) : 0) + (subTenantDataForActiveChange.Count() > 0 ? subTenantDataForActiveChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.year_1_amount_with_fk_change_id_zero) : 0),
            (tenantDataForActiveChange.Count() > 0 ? tenantDataForActiveChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.activeBudgetChangeAmount ) : 0) +( manuallyAddedTenantDataForActiveChange.Count() > 0 ? manuallyAddedTenantDataForActiveChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.activeBudgetChangeAmount) : 0) + (subTenantDataForActiveChange.Count() > 0 ? subTenantDataForActiveChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.activeBudgetChangeAmount) : 0)
        };
        List<decimal> internalFinancingTotals = new List<decimal>()
        {
            (internalFinancingData.Count() > 0 ? internalFinancingData.Sum(x=>x.year_1_amount) : 0),
            (internalFinancingData.Count() > 0 ? internalFinancingData.Sum(x=>x.year_2_amount) : 0),
            (internalFinancingData.Count() > 0 ? internalFinancingData.Sum(x=>x.year_3_amount) : 0),
            (internalFinancingData.Count() > 0 ? internalFinancingData.Sum(x=>x.year_4_amount) : 0),
            (internalFinancingData.Count() > 0 ? internalFinancingData.Sum(x=>x.year_1_amount_with_fk_change_id_zero) : 0),
            (internalFinancingData.Count() > 0 ? internalFinancingData.Sum(x=>x.activeBudgetChangeAmount) : 0)
        };
        List<decimal> internalFinancingChangeTotals = new List<decimal>()
        {
            (internalFinancingDataChange.Count() > 0 ? internalFinancingDataChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.year_1_amount) : 0),
            (internalFinancingDataChange.Count() > 0 ? internalFinancingDataChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.year_2_amount) : 0),
            (internalFinancingDataChange.Count() > 0 ? internalFinancingDataChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.year_3_amount) : 0),
            (internalFinancingDataChange.Count() > 0 ? internalFinancingDataChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.year_4_amount) : 0),
            (internalFinancingDataChange.Count() > 0 ? internalFinancingDataChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.year_1_amount_with_fk_change_id_zero) : 0),
            (internalFinancingDataChange.Count() > 0 ? internalFinancingDataChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.activeBudgetChangeAmount) : 0)
        };
        List<decimal> externalFinancingTotals = new List<decimal>()
        {
            (externalFinancingData.Count() > 0 ? externalFinancingData.Sum(x=>x.year_1_amount) : 0),
            (externalFinancingData.Count() > 0 ? externalFinancingData.Sum(x=>x.year_2_amount) : 0),
            (externalFinancingData.Count() > 0 ? externalFinancingData.Sum(x=>x.year_3_amount) : 0),
            (externalFinancingData.Count() > 0 ? externalFinancingData.Sum(x=>x.year_4_amount) : 0),
            (externalFinancingData.Count() > 0 ? externalFinancingData.Sum(x=>x.year_1_amount_with_fk_change_id_zero) : 0),
            (externalFinancingData.Count() > 0 ? externalFinancingData.Sum(x=>x.activeBudgetChangeAmount) : 0)
        };
        List<decimal> externalFinancingChangeTotals = new List<decimal>()
        {
            (externalFinancingDataChange.Count() > 0 ? externalFinancingDataChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.year_1_amount) : 0),
            (externalFinancingDataChange.Count() > 0 ? externalFinancingDataChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.year_2_amount) : 0),
            (externalFinancingDataChange.Count() > 0 ? externalFinancingDataChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.year_3_amount) : 0),
            (externalFinancingDataChange.Count() > 0 ? externalFinancingDataChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.year_4_amount) : 0),
            (externalFinancingDataChange.Count() > 0 ? externalFinancingDataChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.year_1_amount_with_fk_change_id_zero) : 0),
            (externalFinancingDataChange.Count() > 0 ? externalFinancingDataChange.Where(x=>x.fk_change_id==activeChangeId).Sum(x=>x.activeBudgetChangeAmount) : 0)
        };
        dynamic row = new JObject();
        row.description = ((langStringValuesCommon.FirstOrDefault(v => v.Key == "financing_need_for_financing")).Value).LangText;// "Need For Financing";
        dynamic gridData = new JArray();
        gridData.Add(needForFinancingTotals[0]);
        gridData.Add("100%");
        gridData.Add(needForFinancingTotals[1]);
        gridData.Add("100%");
        gridData.Add(needForFinancingTotals[2]);
        gridData.Add("100%");
        gridData.Add(needForFinancingTotals[3]);
        gridData.Add("100%");
        gridData.Add(needForFinancingChangeTotals[0]);
        gridData.Add(needForFinancingChangeTotals[1]);
        gridData.Add(needForFinancingChangeTotals[2]);
        gridData.Add(needForFinancingChangeTotals[3]);
        row.gridData = gridData;
        jsonData.Add(row);

        row = new JObject();
        row.description = ((langStringValuesCommon.FirstOrDefault(v => v.Key == "financing_external_financing")).Value).LangText;// "External Financing";
        gridData = new JArray();

        gridData.Add(externalFinancingTotals[0]);
        gridData.Add(needForFinancingTotals[0] == 0 ? "0%" : (Math.Round(((externalFinancingTotals[0] / needForFinancingTotals[0]) * 100), 0) < 0 ? (Math.Round(((externalFinancingTotals[0] / needForFinancingTotals[0]) * 100), 0) * (-1)).ToString() + "%" :
            Math.Round(((externalFinancingTotals[0] / needForFinancingTotals[0]) * 100), 0).ToString() + "%"));
        gridData.Add(externalFinancingTotals[1]);
        gridData.Add(needForFinancingTotals[1] == 0 ? "0%" : (Math.Round(((externalFinancingTotals[1] / needForFinancingTotals[1]) * 100), 0) < 0 ? (Math.Round(((externalFinancingTotals[1] / needForFinancingTotals[1]) * 100), 0) * (-1)).ToString() + "%" :
            Math.Round(((externalFinancingTotals[1] / needForFinancingTotals[1]) * 100), 0).ToString() + "%"));
        gridData.Add(externalFinancingTotals[2]);
        gridData.Add(needForFinancingTotals[2] == 0 ? "0%" : (Math.Round(((externalFinancingTotals[2] / needForFinancingTotals[2]) * 100), 0) < 0 ? (Math.Round(((externalFinancingTotals[2] / needForFinancingTotals[2]) * 100), 0) * (-1)).ToString() + "%" :
            Math.Round(((externalFinancingTotals[2] / needForFinancingTotals[2]) * 100), 0).ToString() + "%"));
        gridData.Add(externalFinancingTotals[3]);
        gridData.Add(needForFinancingTotals[3] == 0 ? "0%" : (Math.Round(((externalFinancingTotals[3] / needForFinancingTotals[3]) * 100), 0) < 0 ? (Math.Round(((externalFinancingTotals[3] / needForFinancingTotals[3]) * 100), 0) * (-1)).ToString() + "%" :
            Math.Round(((externalFinancingTotals[3] / needForFinancingTotals[3]) * 100), 0).ToString() + "%"));
        gridData.Add(externalFinancingChangeTotals[0]);
        gridData.Add(externalFinancingChangeTotals[1]);
        gridData.Add(externalFinancingChangeTotals[2]);
        gridData.Add(externalFinancingChangeTotals[3]);
        row.gridData = gridData;
        jsonData.Add(row);

        row = new JObject();
        row.description = ((langStringValuesCommon.FirstOrDefault(v => v.Key == "financing_internal_financing")).Value).LangText;// "Internal Financing";
        gridData = new JArray();

        gridData.Add(internalFinancingTotals[0]);
        gridData.Add(needForFinancingTotals[0] == 0 ? "0%" : (Math.Round(((internalFinancingTotals[0] / needForFinancingTotals[0]) * 100), 0) < 0 ? (Math.Round(((internalFinancingTotals[0] / needForFinancingTotals[0]) * 100), 0) * (-1)).ToString() + "%" :
            Math.Round(((internalFinancingTotals[0] / needForFinancingTotals[0]) * 100), 0).ToString() + "%"));
        gridData.Add(internalFinancingTotals[1]);
        gridData.Add(needForFinancingTotals[1] == 0 ? "0%" : (Math.Round(((internalFinancingTotals[1] / needForFinancingTotals[1]) * 100), 0) < 0 ? (Math.Round(((internalFinancingTotals[1] / needForFinancingTotals[1]) * 100), 0) * (-1)).ToString() + "%" :
            Math.Round(((internalFinancingTotals[1] / needForFinancingTotals[1]) * 100), 0).ToString() + "%"));
        gridData.Add(internalFinancingTotals[2]);
        gridData.Add(needForFinancingTotals[2] == 0 ? "0%" : (Math.Round(((internalFinancingTotals[2] / needForFinancingTotals[2]) * 100), 0) < 0 ? (Math.Round(((internalFinancingTotals[2] / needForFinancingTotals[2]) * 100), 0) * (-1)).ToString() + "%" :
            Math.Round(((internalFinancingTotals[2] / needForFinancingTotals[2]) * 100), 0).ToString() + "%"));
        gridData.Add(internalFinancingTotals[3]);
        gridData.Add(needForFinancingTotals[3] == 0 ? "0%" : (Math.Round(((internalFinancingTotals[3] / needForFinancingTotals[3]) * 100), 0) < 0 ? (Math.Round(((internalFinancingTotals[3] / needForFinancingTotals[3]) * 100), 0) * (-1)).ToString() + "%" :
            Math.Round(((internalFinancingTotals[3] / needForFinancingTotals[3]) * 100), 0).ToString() + "%"));
        gridData.Add(internalFinancingChangeTotals[0]);
        gridData.Add(internalFinancingChangeTotals[1]);
        gridData.Add(internalFinancingChangeTotals[2]);
        gridData.Add(internalFinancingChangeTotals[3]);
        row.gridData = gridData;
        jsonData.Add(row);

        row = new JObject();
        row.description = ((langStringValuesCommon.FirstOrDefault(v => v.Key == "financing_financing_not_allowed")).Value).LangText;// "Financing Not Recovred";
        gridData = new JArray();

        gridData.Add(needForFinancingTotals[0] + externalFinancingTotals[0] + internalFinancingTotals[0]);
        gridData.Add(needForFinancingTotals[0] == 0 ? "0%" : Math.Round((((needForFinancingTotals[0] + externalFinancingTotals[0] + internalFinancingTotals[0]) / needForFinancingTotals[0]) * 100), 0) < 0 ?
            (Math.Round((((needForFinancingTotals[0] + externalFinancingTotals[0] + internalFinancingTotals[0]) / needForFinancingTotals[0]) * 100), 0) * (-1)).ToString() + "%" :
            Math.Round((((needForFinancingTotals[0] + externalFinancingTotals[0] + internalFinancingTotals[0]) / needForFinancingTotals[0]) * 100), 0).ToString() + "%");
        gridData.Add(needForFinancingTotals[1] + externalFinancingTotals[1] + internalFinancingTotals[1]);
        gridData.Add(needForFinancingTotals[1] == 0 ? "0%" : Math.Round((((needForFinancingTotals[1] + externalFinancingTotals[1] + internalFinancingTotals[1]) / needForFinancingTotals[1]) * 100), 0) < 0 ?
            (Math.Round((((needForFinancingTotals[1] + externalFinancingTotals[1] + internalFinancingTotals[1]) / needForFinancingTotals[1]) * 100), 0) * (-1)).ToString() + "%" :
            Math.Round((((needForFinancingTotals[1] + externalFinancingTotals[1] + internalFinancingTotals[1]) / needForFinancingTotals[1]) * 100), 0).ToString() + "%");
        gridData.Add(needForFinancingTotals[2] + externalFinancingTotals[2] + internalFinancingTotals[2]);
        gridData.Add(needForFinancingTotals[2] == 0 ? "0%" : Math.Round((((needForFinancingTotals[2] + externalFinancingTotals[2] + internalFinancingTotals[2]) / needForFinancingTotals[2]) * 100), 0) < 0 ?
            (Math.Round((((needForFinancingTotals[2] + externalFinancingTotals[2] + internalFinancingTotals[2]) / needForFinancingTotals[2]) * 100), 0) * (-1)).ToString() + "%" :
            Math.Round((((needForFinancingTotals[2] + externalFinancingTotals[2] + internalFinancingTotals[2]) / needForFinancingTotals[2]) * 100), 0).ToString() + "%");
        gridData.Add(needForFinancingTotals[3] + externalFinancingTotals[3] + internalFinancingTotals[3]);
        gridData.Add(needForFinancingTotals[3] == 0 ? "0%" : Math.Round((((needForFinancingTotals[3] + externalFinancingTotals[3] + internalFinancingTotals[3]) / needForFinancingTotals[3]) * 100), 0) < 0 ?
            (Math.Round((((needForFinancingTotals[3] + externalFinancingTotals[3] + internalFinancingTotals[3]) / needForFinancingTotals[3]) * 100), 0) * (-1)).ToString() + "%" :
            Math.Round((((needForFinancingTotals[3] + externalFinancingTotals[3] + internalFinancingTotals[3]) / needForFinancingTotals[3]) * 100), 0).ToString() + "%");

        gridData.Add(needForFinancingChangeTotals[0] + externalFinancingChangeTotals[0] + internalFinancingChangeTotals[0]);
        gridData.Add(needForFinancingChangeTotals[1] + externalFinancingChangeTotals[1] + internalFinancingChangeTotals[1]);
        gridData.Add(needForFinancingChangeTotals[2] + externalFinancingChangeTotals[2] + internalFinancingChangeTotals[2]);
        gridData.Add(needForFinancingChangeTotals[3] + externalFinancingChangeTotals[3] + internalFinancingChangeTotals[3]);
        row.gridData = gridData;
        jsonData.Add(row);

        data.Add("columnFields", columnFields);
        data.Add("columnTitles", columnTitles);
        data.Add("jsonData", jsonData);
        data.amountFormat = ((langStringFormat.FirstOrDefault(v => v.Key == "amount")).Value).LangText;
        JObject jsonCfgRow = JObject.Parse(await pUtility.GetApplicationSettingAsync("GetNeedForFinancing_grid_config"));
        data.gridConfig = jsonCfgRow;
        data.description = (await pUtility.GetActionTypeDescriptionAsync(userID, null, null, Framsikt.BL.Helpers.clsConstants.ActionType_FieldId["FINANCING_Summary"], null, budgetYear, "")).Value;
        data.descriptionId = (await pUtility.GetActionTypeDescriptionAsync(userID, null, null, Framsikt.BL.Helpers.clsConstants.ActionType_FieldId["FINANCING_Summary"], null, budgetYear, "")).Key;
        data.activeChangeId = activeChangeId;
        var isActiveBudgetChange = await IDBContext.tfp_budget_changes.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_change_id == activeChangeId && x.budget_year == budgetYear && x.status == 1);
        data.isEditable = isActiveBudgetChange == null ? false : true;
        return data;
    }



    private List<clsFinancing> GetTenantData(TenantDBContext IDBContext, UserData userDetails, int budgetYear, List<string> InvestmentProgramData, string pageId, int activeChangeId = -1)
    {
        return GetTenantDataAsync(IDBContext, userDetails, budgetYear, InvestmentProgramData, pageId, activeChangeId).GetAwaiter().GetResult();
    }



    private async Task<List<clsFinancing>> GetTenantDataAsync(TenantDBContext IDBContext, UserData userDetails, int budgetYear, List<string> InvestmentProgramData, string pageId, int activeChangeId = -1)
    {
        List<int> lineOrder = new List<int> { 1, 2, 3, 4, 10, 20, 30 };
        var data = await (from ih in IDBContext.tfp_inv_header
            join it in IDBContext.tfp_inv_transactions on new { a = ih.fk_tenant_id, b = ih.pk_inv_action_id, c = ih.budget_year }
                equals new { a = it.fk_tenant_id, b = it.fk_inv_action_id, c = it.budget_year }
            join tbc in IDBContext.tfp_budget_changes on new { a = it.fk_tenant_id, b = it.fk_change_id, c = it.budget_year }
                equals new { a = tbc.fk_tenant_id, b = tbc.pk_change_id, c = tbc.budget_year } into group2
            from g2 in group2.DefaultIfEmpty()
            where (ih.fk_tenant_id == userDetails.tenant_id
                   && ih.budget_year == budgetYear
                   && ih.action_type == 1
                   && ih.isManuallyAdded == 0
                   && lineOrder.Contains(ih.line_order))
            select new
            {
                line_order = ih.line_order,
                isManuallyAdded = ih.isManuallyAdded,
                year_1_amount = (it.year_1_amount),
                year_2_amount = (it.year_2_amount),
                year_3_amount = (it.year_3_amount),
                year_4_amount = (it.year_4_amount),
                accountCode = it.fk_account_code,
                departmentCode = it.department_code_,
                functioncode = it.fk_function_code_,
                projectCode = it.fk_project_code,
                freedim1 = it.free_dim_1,
                freedim2 = it.free_dim_2,
                freedim3 = it.free_dim_3,
                freedim4 = it.free_dim_4,
                programCode = it.fk_prog_code,
                changeId = it.fk_change_id,
                orgBudgetFlag = g2 != null ? g2.org_budget_flag : -1,
                invId = it.fk_investment_id
            }).OrderBy(x => x.line_order).ToListAsync();

        if (pageId != "YB_FINANCING")
        {
            data = data.Where(x => x.orgBudgetFlag != 0).ToList();
        }
        data = data.Where(x => InvestmentProgramData.Contains(x.programCode)).OrderBy(x => x.line_order).ToList();
        var invIdlIst = data.Select(x => x.invId).ToList();
        var invIdStatusId = await (from t in IDBContext.tco_inv_budgetyear_config
            where t.fk_tenant_id == userDetails.tenant_id && t.budget_year == budgetYear && invIdlIst.Contains(t.fk_investment_id)
            select new
            {
                key = t.fk_investment_id,
                value = t.inv_status
            }).ToListAsync();

        var statusList = _inv.GetInvestmentStatus(userDetails.user_name);

        var invStatusList = (from ins in invIdStatusId
            join sl in statusList on ins.value equals sl.KeyId
            select new KeyValueInvStatusHelper()
            {
                invId = ins.key,
                statusValue = sl.ValueString,
                statusId = sl.KeyId
            }).ToList();
        invStatusList.Add(new KeyValueInvStatusHelper()
        {
            invId = 0,
            statusId = -1,
            statusValue = "finacingType"
        });

        List<clsFinancing> tenantData = (from d in data
            join isl in invStatusList on d.invId equals isl.invId
            group d by new { d.line_order, d.isManuallyAdded, isl.statusValue, isl.statusId } into groupData
            select new clsFinancing
            {
                line_order = groupData.Key.line_order,
                isManuallyAdded = groupData.Key.isManuallyAdded,
                year_1_amount = groupData.Sum(x => x.year_1_amount) / 1000,
                year_2_amount = groupData.Sum(x => x.year_2_amount) / 1000,
                year_3_amount = groupData.Sum(x => x.year_3_amount) / 1000,
                year_4_amount = groupData.Sum(x => x.year_4_amount) / 1000,
                status = groupData.Key.statusValue,
                statusId = groupData.Key.statusId,
            }).OrderBy(x => x.status).ToList();

        return tenantData;
    }



    private List<clsFinancing> GetTenantDataForActiveChange(TenantDBContext IDBContext, UserData userDetails, int budgetYear, int activeChangeId, List<string> InvestmentProgramData)
    {
        return GetTenantDataForActiveChangeAsync(IDBContext, userDetails, budgetYear, activeChangeId, InvestmentProgramData).GetAwaiter().GetResult();
    }



    private async Task< List<clsFinancing>> GetTenantDataForActiveChangeAsync(TenantDBContext IDBContext, UserData userDetails, int budgetYear, int activeChangeId, List<string> InvestmentProgramData)
    {
        var data = await (from ih in IDBContext.tfp_inv_header
            join it in IDBContext.tfp_inv_transactions on new { a = ih.fk_tenant_id, b = ih.pk_inv_action_id }
                equals new { a = it.fk_tenant_id, b = it.fk_inv_action_id }
            where (ih.fk_tenant_id == userDetails.tenant_id
                   && it.fk_change_id == activeChangeId
                   && ih.budget_year == budgetYear
                   && ih.action_type == 1
                   && ih.isManuallyAdded == 0
                   && (ih.line_order == 1 || ih.line_order == 2 || ih.line_order == 3 || ih.line_order == 4 || ih.line_order == 10 || ih.line_order == 20 || ih.line_order == 30))
            select new
            {
                line_order = ih.line_order,
                isManuallyAdded = ih.isManuallyAdded,
                year_1_amount = (it.year_1_amount),
                year_2_amount = (it.year_2_amount),
                year_3_amount = (it.year_3_amount),
                year_4_amount = (it.year_4_amount),
                changeId = it.fk_change_id,
                accountCode = it.fk_account_code,
                departmentCode = it.department_code_,
                functioncode = it.fk_function_code_,
                projectCode = it.fk_project_code,
                freedim1 = it.free_dim_1,
                freedim2 = it.free_dim_2,
                freedim3 = it.free_dim_3,
                freedim4 = it.free_dim_4,
                programCode = it.fk_prog_code,
                invId = it.fk_investment_id
            }).OrderBy(x => x.line_order).ToListAsync();

        data = data.Where(x => InvestmentProgramData.Contains(x.programCode)).OrderBy(x => x.line_order).ToList();
        var invIdlIst = data.Select(x => x.invId).ToList();
        var invIdStatusId = await (from t in IDBContext.tco_inv_budgetyear_config
            where t.fk_tenant_id == userDetails.tenant_id && t.budget_year == budgetYear && invIdlIst.Contains(t.fk_investment_id)
            select new
            {
                key = t.fk_investment_id,
                value = t.inv_status
            }).ToListAsync();

        var statusList = await _inv.GetInvestmentStatusAsync(userDetails.user_name);

        var invStatusList = (from ins in invIdStatusId
            join sl in statusList on ins.value equals sl.KeyId
            select new KeyValueInvStatusHelper()
            {
                invId = ins.key,
                statusValue = sl.ValueString,
                statusId = sl.KeyId
            }).ToList();
        invStatusList.Add(new KeyValueInvStatusHelper()
        {
            invId = 0,
            statusId = -1,
            statusValue = "finacingType"
        });

        List<clsFinancing> tenantData = (from d in data
            join isl in invStatusList on d.invId equals isl.invId
            group d by new { d.line_order, d.isManuallyAdded, d.changeId, isl.statusValue, isl.statusId } into groupData
            select new clsFinancing
            {
                line_order = groupData.Key.line_order,
                isManuallyAdded = groupData.Key.isManuallyAdded,
                fk_change_id = groupData.Key.changeId,
                year_1_amount = groupData.Sum(d => d.year_1_amount) / 1000,
                year_2_amount = groupData.Sum(d => d.year_2_amount) / 1000,
                year_3_amount = groupData.Sum(d => d.year_3_amount) / 1000,
                year_4_amount = groupData.Sum(d => d.year_4_amount) / 1000,
                status = groupData.Key.statusValue,
                statusId = groupData.Key.statusId,
            }).OrderBy(x => x.line_order).ToList();

        return tenantData;
    }



    private List<clsFinancing> GetManuallyAddedTenantData(TenantDBContext IDBContext, UserData userDetails, int budgetYear, List<string> InvestmentProgramData, string pageId, int activeChangeId = -1)
    {
        return GetManuallyAddedTenantDataAsync(IDBContext, userDetails, budgetYear, InvestmentProgramData, pageId, activeChangeId).GetAwaiter().GetResult();
    }



    private async Task<List<clsFinancing>> GetManuallyAddedTenantDataAsync(TenantDBContext IDBContext, UserData userDetails, int budgetYear, List<string> InvestmentProgramData, string pageId, int activeChangeId = -1)
    {
        var manuallyAddedData = await (from ih in IDBContext.tfp_inv_header
            join it in IDBContext.tfp_inv_transactions on new { a = ih.fk_tenant_id, b = ih.pk_inv_action_id }
                equals new { a = it.fk_tenant_id, b = it.fk_inv_action_id }
            join tbc in IDBContext.tfp_budget_changes on new { a = it.fk_tenant_id, b = it.fk_change_id, c = it.budget_year }
                equals new { a = tbc.fk_tenant_id, b = tbc.pk_change_id, c = tbc.budget_year } into group2
            from g2 in group2.DefaultIfEmpty()
            where (ih.fk_tenant_id == userDetails.tenant_id
                   && ih.budget_year == budgetYear
                   && ih.action_type == 1
                   && ih.isManuallyAdded == 1
                   && (ih.line_order == 0))
            select new
            {
                invActionId = ih.pk_inv_action_id,
                caption_name = ih.action_name,
                line_order = ih.line_order,
                isManuallyAdded = ih.isManuallyAdded,
                year_1_amount = (it.year_1_amount),
                year_2_amount = (it.year_2_amount),
                year_3_amount = (it.year_3_amount),
                year_4_amount = (it.year_4_amount),
                accountCode = it.fk_account_code,
                departmentCode = it.department_code_,
                functioncode = it.fk_function_code_,
                projectCode = it.fk_project_code,
                freedim1 = it.free_dim_1,
                freedim2 = it.free_dim_2,
                freedim3 = it.free_dim_3,
                freedim4 = it.free_dim_4,
                programCode = it.fk_prog_code,
                changeId = it.fk_change_id,
                orgBudgetFlag = g2 != null ? g2.org_budget_flag : -1,
            }).OrderBy(x => x.line_order).ToListAsync();

        if (pageId != "YB_FINANCING")
        {
            manuallyAddedData = manuallyAddedData.Where(x => x.orgBudgetFlag != 0).ToList();
        }
        manuallyAddedData = manuallyAddedData.Where(x => InvestmentProgramData.Contains(x.programCode)).OrderBy(x => x.line_order).ToList();
        List<clsFinancing> manuallyAddedTenantData = (from m in manuallyAddedData
            group m by new { m.invActionId, m.caption_name, m.line_order, m.isManuallyAdded } into groupData
            select new clsFinancing
            {
                invActionId = groupData.Key.invActionId,
                caption_name = groupData.Key.caption_name,
                line_order = groupData.Key.line_order,
                isManuallyAdded = groupData.Key.isManuallyAdded,
                year_1_amount = groupData.Sum(m => m.year_1_amount) / 1000,
                year_2_amount = groupData.Sum(m => m.year_2_amount) / 1000,
                year_3_amount = groupData.Sum(m => m.year_3_amount) / 1000,
                year_4_amount = groupData.Sum(m => m.year_4_amount) / 1000
            }).OrderBy(x => x.line_order).ToList();

        return manuallyAddedTenantData;
    }



    private static List<clsFinancing> GetManuallyAddedTenantDataForActiveChange(TenantDBContext IDBContext, UserData userDetails, int budgetYear, int activeChangeId, List<string> InvestmentProgramData)
    {
        return GetManuallyAddedTenantDataForActiveChangeAsync(IDBContext, userDetails, budgetYear, activeChangeId, InvestmentProgramData).GetAwaiter().GetResult();
    }



    private async static Task<List<clsFinancing>> GetManuallyAddedTenantDataForActiveChangeAsync(TenantDBContext IDBContext, UserData userDetails, int budgetYear, int activeChangeId, List<string> InvestmentProgramData)
    {
        var manuallyAddedData = await (from ih in IDBContext.tfp_inv_header
            join it in IDBContext.tfp_inv_transactions on new { a = ih.fk_tenant_id, b = ih.pk_inv_action_id }
                equals new { a = it.fk_tenant_id, b = it.fk_inv_action_id }
            where (ih.fk_tenant_id == userDetails.tenant_id
                   && it.fk_change_id == activeChangeId
                   && ih.budget_year == budgetYear
                   && ih.action_type == 1
                   && ih.isManuallyAdded == 1
                   && (ih.line_order == 0))
            select new
            {
                invActionId = ih.pk_inv_action_id,
                caption_name = ih.action_name,
                line_order = ih.line_order,
                changeId = it.fk_change_id,
                isManuallyAdded = ih.isManuallyAdded,
                year_1_amount = (it.year_1_amount),
                year_2_amount = (it.year_2_amount),
                year_3_amount = (it.year_3_amount),
                year_4_amount = (it.year_4_amount),
                accountCode = it.fk_account_code,
                departmentCode = it.department_code_,
                functioncode = it.fk_function_code_,
                projectCode = it.fk_project_code,
                freedim1 = it.free_dim_1,
                freedim2 = it.free_dim_2,
                freedim3 = it.free_dim_3,
                freedim4 = it.free_dim_4,
                programCode = it.fk_prog_code
            }).OrderBy(x => x.line_order).ToListAsync();

        manuallyAddedData = manuallyAddedData.Where(x => InvestmentProgramData.Contains(x.programCode)).OrderBy(x => x.line_order).ToList();
        List<clsFinancing> manuallyAddedTenantData = (from m in manuallyAddedData
            group m by new { m.invActionId, m.caption_name, m.line_order, m.isManuallyAdded, m.changeId } into groupData
            select new clsFinancing
            {
                invActionId = groupData.Key.invActionId,
                caption_name = groupData.Key.caption_name,
                line_order = groupData.Key.line_order,
                fk_change_id = groupData.Key.changeId,
                isManuallyAdded = groupData.Key.isManuallyAdded,
                year_1_amount = groupData.Sum(m => m.year_1_amount) / 1000,
                year_2_amount = groupData.Sum(m => m.year_2_amount) / 1000,
                year_3_amount = groupData.Sum(m => m.year_3_amount) / 1000,
                year_4_amount = groupData.Sum(m => m.year_4_amount) / 1000
            }).OrderBy(x => x.line_order).ToList();

        return manuallyAddedTenantData;
    }

}