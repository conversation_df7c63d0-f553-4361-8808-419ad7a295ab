<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Configurations>Debug;Release;SAST</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Properties\**" />
    <EmbeddedResource Remove="Properties\**" />
    <None Remove="Properties\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="BPDeleteResponseMessageHelper.cs" />
    <Compile Remove="CacheCreator.cs" />
    <Compile Remove="Helpers\UserCacheEntity.cs" />
    <Compile Remove="InvestmentProject.cs.cs" />
    <Compile Remove="Repository\ActiveDirectoryManager.cs" />
    <Compile Remove="Repository\IIdentityManager.cs" />
    <Compile Remove="Web References\Noark5\Reference.cs" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Config\BudgetProposal\BPActionDescGridColumn.json" />
    <None Remove="Config\DashBoardWidget\ClimateDuckyData.json" />
    <None Remove="Config\DocWidget\BarChart.json" />
    <None Remove="Config\DocWidget\ColumnChart.json" />
    <None Remove="Config\DocWidget\DocReportingGrid.json" />
    <None Remove="Config\DocWidget\LineChart.json" />
    <None Remove="Config\DocWidget\PieChart.json" />
    <None Remove="Config\FinancialPlanSetup\BudgetPhaseStatusColumnConfig.json" />
    <None Remove="Config\FinPlanAdminActionApproval\FinPlanApprovalEditActionDescription.json" />
    <None Remove="Config\FintJournal\Journal.json" />
    <None Remove="Config\Reporting\FinplanActionsReport\report.json" />
    <None Remove="Config\Reporting\FinPlanReporting\report.json" />
    <None Remove="Config\Reporting\ForecastReport\report.json" />
    <None Remove="Config\Reporting\InvestmentReporting\report.json" />
    <None Remove="Config\Reporting\YearlyBudgetReporting\report.json" />
    <None Remove="Config\YearlyBudget\AddonDetail.json" />
    <None Remove="Config\YearlyBudget\AdminAccountSetupGrid.json" />
    <None Remove="Config\YearlyBudget\BudgetAccountDetail.json" />
    <None Remove="Config\YearlyBudget\EmployeeTypeGridColConfig.json" />
    <None Remove="Config\YearlyBudget\PAllocDetail.json" />
    <None Remove="Config\YearlyBudget\percentageAddOnColumn.json" />
    <None Remove="Config\YearlyBudget\PeriodicAllocationColumn.json" />
    <None Remove="Config\YearlyBudget\UserAdjCodeFileds.json" />
    <None Remove="Config\YearlyBudget\YBAccountGridColumn.json" />
    <None Remove="css\contents.css" />
    <None Remove="css\customstyles.css" />
    <None Remove="css\editorStyles.css" />
    <None Remove="css\editorStyles5.css" />
    <None Remove="Images\1_low_12.png" />
    <None Remove="Images\2_moderate_12.png" />
    <None Remove="Images\3_good_12.png" />
    <None Remove="Images\4_no_target_12.png" />
    <None Remove="Images\fee_down_arrow.png" />
    <None Remove="Images\fee_down_arrow.svg" />
    <None Remove="Images\fee_stable_arrow.png" />
    <None Remove="Images\fee_stable_arrow.svg" />
    <None Remove="Images\fee_up_arrow.png" />
    <None Remove="Images\fee_up_arrow.svg" />
    <None Remove="Images\icon-below-expect.png" />
    <None Remove="Images\icon-better.png" />
    <None Remove="Images\icon-expected.png" />
    <None Remove="Images\trafficlights-01.png" />
    <None Remove="Images\trafficlights-01.svg" />
    <None Remove="Images\trafficlights-02.png" />
    <None Remove="Images\trafficlights-02.svg" />
    <None Remove="Images\trafficlights-03.png" />
    <None Remove="Images\trafficlights-03.svg" />
    <None Remove="packages.config" />
    <None Remove="Web References\Noark5\Reference.map" />
  </ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="Config\FinancialPlanSetup\BudgetPhaseStatusColumnConfig.json" />
	</ItemGroup>

	<ItemGroup>
    <EmbeddedResource Include="Config\BudgetProposal\BPActionDescGridColumn.json" />
    <EmbeddedResource Include="Config\DashBoardWidget\ClimateDuckyData.json" />
    <EmbeddedResource Include="Config\DocWidget\BarChart.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Config\DocWidget\ColumnChart.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Config\DocWidget\DocReportingGrid.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Config\DocWidget\LineChart.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Config\DocWidget\PieChart.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Config\FinPlanAdminActionApproval\FinPlanApprovalEditActionDescription.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Config\FintJournal\Journal.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Config\Reporting\FinplanActionsReport\report.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Config\Reporting\FinPlanReporting\report.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Config\Reporting\ForecastReport\report.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Config\Reporting\InvestmentReporting\report.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Config\Reporting\YearlyBudgetReporting\report.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Config\YearlyBudget\AddonDetail.json" />
    <EmbeddedResource Include="Config\YearlyBudget\AdminAccountSetupGrid.json" />
    <EmbeddedResource Include="Config\YearlyBudget\BudgetAccountDetail.json" />
    <EmbeddedResource Include="Config\YearlyBudget\EmployeeTypeGridColConfig.json" />
    <EmbeddedResource Include="Config\YearlyBudget\PAllocDetail.json" />
    <EmbeddedResource Include="Config\YearlyBudget\percentageAddOnColumn.json" />
    <EmbeddedResource Include="Config\YearlyBudget\PeriodicAllocationColumn.json" />
    <EmbeddedResource Include="Config\YearlyBudget\UserAdjCodeFileds.json" />
    <EmbeddedResource Include="Config\YearlyBudget\YBAccountGridColumn.json" />
    <EmbeddedResource Include="css\contents.css">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="css\customstyles.css">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="css\editorStyles.css">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="css\editorStyles5.css">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Images\1_low_12.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Images\2_moderate_12.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Images\3_good_12.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Images\4_no_target_12.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Images\fee_down_arrow.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Images\fee_down_arrow.svg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Images\fee_stable_arrow.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Images\fee_stable_arrow.svg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Images\fee_up_arrow.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Images\fee_up_arrow.svg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Images\icon-below-expect.png" />
    <EmbeddedResource Include="Images\icon-better.png" />
    <EmbeddedResource Include="Images\icon-expected.png" />
    <EmbeddedResource Include="Images\trafficlights-01.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Images\trafficlights-01.svg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Images\trafficlights-02.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Images\trafficlights-02.svg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Images\trafficlights-03.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Images\trafficlights-03.svg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Aspose.Cells" Version="24.8.0" />
    <PackageReference Include="Aspose.Words" Version="24.8.0" />
    <PackageReference Include="Azure.Data.Tables" Version="12.8.0" />
    <PackageReference Include="Azure.Identity" Version="1.12.0" />
    <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.0.0" />
    <PackageReference Include="Azure.Search.Documents" Version="11.4.0" />
    <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.4.0" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.16.0" />
    <PackageReference Include="Azure.Storage.Queues" Version="12.13.1" />
    <PackageReference Include="Framsikt.Api.Shared.Models" Version="0.3.17" />
    <PackageReference Include="HtmlAgilityPack" Version="1.11.24" />
    <PackageReference Include="HtmlSanitizer" Version="8.0.795" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.22.0" />
    <PackageReference Include="Microsoft.Extensions.Azure" Version="1.7.4" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="6.0.1" />
    <PackageReference Include="Microsoft.FeatureManagement" Version="4.0.0" />
    <PackageReference Include="Microsoft.FeatureManagement.AspNetCore" Version="4.0.0" />
    <PackageReference Include="nClam" Version="7.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
    <PackageReference Include="PuppeteerSharp" Version="9.0.2" />
    <PackageReference Include="RestSharp" Version="106.15.0" />
    <PackageReference Include="Serilog" Version="4.0.1" />
    <PackageReference Include="Serilog.Sinks.MSSqlServer" Version="6.3.0" />
    <PackageReference Include="SharpScss" Version="2.0.0" />
    <PackageReference Include="SkiaSharp" Version="3.119.0" />
    <PackageReference Include="SkiaSharp.NativeAssets.Linux" Version="3.119.0" />
    <PackageReference Include="StackExchange.Redis" Version="2.6.96" />
    <PackageReference Include="System.Linq.Dynamic.Core" Version="1.6.2" />
    <PackageReference Include="System.ServiceModel.Duplex" Version="6.0.*" />
    <PackageReference Include="System.ServiceModel.Federation" Version="6.0.*" />
    <PackageReference Include="System.ServiceModel.Http" Version="6.2.0" />
    <PackageReference Include="System.ServiceModel.NetNamedPipe" Version="6.0.*" />
    <PackageReference Include="System.ServiceModel.NetTcp" Version="6.0.*" />
    <PackageReference Include="System.ServiceModel.Security" Version="6.0.*" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Framsikt.Entities\Framsikt.Entities.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Config\DocWidget\BarChart.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Config\DocWidget\ColumnChart.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Config\DocWidget\DocReportingGrid.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Config\DocWidget\LineChart.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Config\DocWidget\PieChart.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Config\Reporting\FinplanActionsReport\report.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Config\Reporting\FinPlanReporting\report.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Config\Reporting\ForecastReport\report.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Config\Reporting\InvestmentReporting\report.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Config\Reporting\YearlyBudgetReporting\report.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
  </ItemGroup>
</Project>
