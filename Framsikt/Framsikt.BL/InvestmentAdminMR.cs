#pragma warning disable CS8625
#pragma warning disable CS8600
#pragma warning disable CS8601
#pragma warning disable CS8602
#pragma warning disable CS8603
#pragma warning disable CS8604

using Aspose.Cells;
using Framsikt.BL.Helpers;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using KeyValuePair = Framsikt.BL.Helpers.KeyValuePair;

namespace Framsikt.BL
{
    public class InvestmentAdminMR : IInvestmentAdminMR
    {
        private readonly IUtility _pUtility;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IAppDataCache _cache;
        private readonly IConsequenceAdjustedBudget _pconseq;
        private readonly IInvestmentProject _invProj;

        public InvestmentAdminMR(IUtility util, IUnitOfWork unitOfWork, IAppDataCache cache, IConsequenceAdjustedBudget pconseq, IInvestmentProject invProj)
        {
            _pUtility = util;
            _unitOfWork = unitOfWork;
            _cache = cache;
            _pconseq = pconseq;
            _invProj = invProj;
        }

        public async Task<InvestmentAdminGridHelper> GetGridDataForINVAdminAsync(string userId, int Period, List<KeyValuePair> SelectedCols, AdminInvSearchHelper searchInput)
        {
            UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
            InvestmentAdminGridHelper finalDataSet = new InvestmentAdminGridHelper();
            Dictionary<string, clsLanguageString> langStringValuesFinancing = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Financingproject");
            IEnumerable<InvestmentAdminBaseGridHelper> baseData = await _unitOfWork.InvestmentAdminMRRepository.GetAdminInvMRBaseDataAsync(userDetails.tenant_id, Period);
            List<string> activeColms = SelectedCols.Where(x => x.isChecked).Select(x => x.key).ToList();
            var inValidProjectList = await _unitOfWork.InvestmentAdminMRRepository.GetInvaliDProjectDataAsync(userDetails.tenant_id, Period, langStringValuesFinancing);
            var yearWisedformatedDataAsync = await FormatDataYearWiseAndGroupAsync(baseData, Period, userDetails);

            finalDataSet.inValidProjectList = inValidProjectList;

            var grid1data = FormatAdminInvGrid1(yearWisedformatedDataAsync, Period, activeColms, searchInput);
            finalDataSet.Grid1 = grid1data.Grid1;
            finalDataSet.progCodeDS = grid1data.progCodeDS;
            finalDataSet.invStatusNameDS = grid1data.invStatusNameDS;
            finalDataSet.Grid2 = FormatAdminInvGrid2(yearWisedformatedDataAsync);
            return finalDataSet;
        }

        public async Task<string> AddUpdateAdminInvDataAsync(string userId, int Period, List<InvestmentAdminBaseGridHelper> dataToAdd)
        {
            int budgetYear = Period / 100;
            UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);

            int orgVersionPeriod = Period;
            if ((Period - budgetYear * 100) % 13 == 0)
                orgVersionPeriod = orgVersionPeriod - 1;

            AdminInvGridVatCalHelper vatCalInputAData = new AdminInvGridVatCalHelper();
            var orgVersionContent = await _pUtility.GetOrgVersionSpecificContentAsync(userId, orgVersionPeriod);
            List<string> accountTypes = new() { "ACCOUNT", "DEPARTMENT", "FUNCTION" };
            List<string> linkType = new() { "VAT_COST", "VAT_COMP" };
            vatCalInputAData.defaultAccounts = (await _unitOfWork.InvestmentProjectRepository.GetAccountingDefaultsAsync(userDetails.tenant_id, accountTypes, linkType, orgVersionContent.orgVersion)).ToList();
            string invNetAmounts = await _pUtility.GetParameterValueAsync(userDetails.user_name, "FP_INV_NET_AMOUNTS");
            vatCalInputAData.calcVatComp = (!string.IsNullOrEmpty(invNetAmounts) && Convert.ToBoolean(invNetAmounts));
            List<tmr_proj_bud_changes> baseData = (await _unitOfWork.InvestmentAdminMRRepository.GetllTmrProjBudChangesDataAsync(userDetails.tenant_id, Period)).ToList();
            tmr_proj_bud_changes newData;
            List<tmr_proj_bud_changes> matchingDataSet;
            List<tmr_proj_bud_changes> mVatDataSet;
            int yearColumns = (Period % 100 == 13) ? 2 : 5;
            bool isIncomeAccount = false;
            foreach (var d in dataToAdd)
            {
                isIncomeAccount = await _unitOfWork.InvestmentAdminMRRepository.IsIncomeAccountAsync(userDetails.tenant_id, d.AccCode);
                d.AlterCode = (d.AlterCode.Contains("Velg endringskode")) ? string.Empty : d.AlterCode;//#88495
                for (int i = 0; i < yearColumns; i++)
                {
                    decimal ammountValue = GetAmtValueForGivenIterration(i, d);
                    tmr_proj_bud_changes currentData = null;
                    
                    matchingDataSet = baseData.Where(x => x.fk_project_code == d.prevProjCode && x.fk_account_code == d.prevAccCode && x.fk_function_code == d.prevFuncCode && x.fk_department_code == d.prevDeptCode && x.fk_alter_code == d.prevAlterCode && x.year == (budgetYear + i) && x.vat_rate == d.prevVatRate && x.vat_refund == d.prevVatRefund && x.forecast_period == Period).ToList();// get matching data

                    if (!isIncomeAccount && d.AccCode != d.prevAccCode)
                    {
                        await _unitOfWork.InvestmentAdminMRRepository.DeleteMatchingTmrProjBudDataAsync(userDetails.tenant_id, matchingDataSet.Select(x => x.pk_id).ToList());
                    }
                    else
                    {
                        // if more then one matching data delete everything else select the one
                        if (matchingDataSet.Count > 1)
                        {
                            await _unitOfWork.InvestmentAdminMRRepository.DeleteMatchingTmrProjBudDataAsync(userDetails.tenant_id, matchingDataSet.Select(x => x.pk_id).ToList());
                        }
                        else if (matchingDataSet.Count == 1) // Delete VAT rows and update the one matching row.
                        {
                            Guid currentMatchingPk_id = matchingDataSet.FirstOrDefault().pk_id;
                            mVatDataSet = baseData.Where(x => x.fk_proj_trans_id == currentMatchingPk_id).ToList();
                            await _unitOfWork.InvestmentAdminMRRepository.DeleteMatchingTmrProjBudDataAsync(userDetails.tenant_id, mVatDataSet.Select(x => x.pk_id).ToList());
                            currentData = matchingDataSet.FirstOrDefault();
                        }
                        else
                        {
                            currentData = matchingDataSet.FirstOrDefault();
                        }
                    }

                    InvestmentAdminBaseGridHelper iData = new()
                    {
                        VatRate = d.VatRate,
                        VatRefund = d.VatRefund,
                        ProjCode = d.ProjCode
                    };

                    if (!isIncomeAccount && d.AccCode != d.prevAccCode)
                    {
                        IEnumerable<tco_projects> tcoProjectData = await _unitOfWork.InvestmentProjectManagerRepo.GetTcoProjectDataAsync(userDetails.tenant_id);
                        SetVatRateAndRefund(iData, tcoProjectData);
                    }

                    if (currentData != null)
                    {
                        currentData.amount = ammountValue;
                        currentData.description = d.Desc;
                        currentData.fk_alter_code = (d.AlterCode.Contains("Velg endringskode")) ? string.Empty : d.AlterCode;
                        currentData.fk_account_code = d.AccCode;
                        currentData.fk_function_code = d.FuncCode;
                        currentData.fk_department_code = d.DeptCode;
                        currentData.fk_project_code = d.ProjCode;
                        currentData.vat_rate = !isIncomeAccount && d.AccCode != d.prevAccCode && !string.IsNullOrEmpty(d.prevAccCode) ? iData.VatRate : d.VatRate;
                        currentData.vat_refund = !isIncomeAccount && d.AccCode != d.prevAccCode && !string.IsNullOrEmpty(d.prevAccCode) ? iData.VatRefund : d.VatRefund;

                        if ((d.VatRate != 0 && d.VatRefund != 0) || (!isIncomeAccount && d.AccCode != d.prevAccCode && !string.IsNullOrEmpty(d.prevAccCode) && iData.VatRate != 0 && iData.VatRefund != 0))
                        {
                            vatCalInputAData.pk_id = currentData.pk_id;
                            vatCalInputAData.trans_id = currentData.trans_id;
                            vatCalInputAData.amount = ammountValue;
                            vatCalInputAData.forecast_period = Period;
                            vatCalInputAData.year = (budgetYear + i);
                            vatCalInputAData.free_dim_1 = currentData.free_dim_1;
                            vatCalInputAData.free_dim_2 = currentData.free_dim_2;
                            vatCalInputAData.free_dim_3 = currentData.free_dim_3;
                            vatCalInputAData.free_dim_4 = currentData.free_dim_4;

                            await CalculateGridVATAsync(userDetails, d, vatCalInputAData);
                        }
                    }
                    else
                    {
                        newData = new tmr_proj_bud_changes()
                        {
                            amount = ammountValue,
                            description = d.Desc,
                            fk_account_code = d.AccCode,
                            fk_function_code = d.FuncCode,
                            fk_alter_code = (d.AlterCode.Contains("Velg endringskode")) ? string.Empty : d.AlterCode,
                            fk_department_code = d.DeptCode,
                            fk_project_code = d.ProjCode,
                            forecast_period = Period,
                            year = (budgetYear + i),
                            free_dim_1 = string.Empty,
                            free_dim_2 = string.Empty,
                            free_dim_3 = string.Empty,
                            free_dim_4 = string.Empty,
                            vat_rate = !isIncomeAccount && d.AccCode != d.prevAccCode && !string.IsNullOrEmpty(d.prevAccCode) ? iData.VatRate : d.VatRate,
                            vat_refund = !isIncomeAccount && d.AccCode != d.prevAccCode && !string.IsNullOrEmpty(d.prevAccCode) ? iData.VatRefund : d.VatRefund,
                            fk_tenant_id = userDetails.tenant_id,
                            is_vat_row = false,
                            fk_proj_trans_id = Guid.Empty,
                            pk_id = Guid.NewGuid(),
                            trans_id = Guid.NewGuid(),
                            updated = DateTime.UtcNow,
                            updated_by = userDetails.pk_id
                        };
                        _unitOfWork.GenericRepo.Add(newData);

                        // calculate vat from the transaction only if both VAT Rate and VAT Refund are not zero
                        if ((d.VatRate != 0 && d.VatRefund != 0) || (!isIncomeAccount && d.AccCode != d.prevAccCode && !string.IsNullOrEmpty(d.prevAccCode) && iData.VatRate != 0 && iData.VatRefund != 0))
                        {
                            vatCalInputAData.pk_id = newData.pk_id;
                            vatCalInputAData.trans_id = newData.trans_id;
                            vatCalInputAData.amount = ammountValue;
                            vatCalInputAData.forecast_period = Period;
                            vatCalInputAData.year = (budgetYear + i);
                            await CalculateGridVATAsync(userDetails, d, vatCalInputAData);
                        }
                    }
                }
            }
            await _unitOfWork.CompleteAsync();
            return "sucessfull";
        }

        public async Task<InvestmentAdminLoanVatCalMessage> CalculateVATAsync(string userId, int period)
        {
            InvestmentAdminLoanVatCalMessage result = new InvestmentAdminLoanVatCalMessage();
            int budgetYear = period / 100;

            UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringValuesFinancing = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Financingproject");

            //get the amount for the VAT
            var VATrowData = (await _unitOfWork.InvestmentAdminMRRepository.GetVatAndLoanCalDataAsync(userDetails.tenant_id, period, false)).ToList();

            // update with default program code whereever we have null
            string defaultProgCode = await _unitOfWork.FinancingProjectRepository.GetDefaultProgramCodeAsync(userDetails.tenant_id);
            VATrowData.ForEach(x => { x.program_code = string.IsNullOrEmpty(x.program_code) ? defaultProgCode : x.program_code; });

            //group the data
            var VATData = (from vr in VATrowData
                           group vr by new { vr.program_code, vr.year } into grp
                           select new AdminInvLoanVatCalHelper
                           {
                               program_code = grp.Key.program_code,
                               year = grp.Key.year,
                               amount = grp.Sum(x => x.amount),
                           }).ToList();

            int orgVersionPeriod = period;
            if ((period - budgetYear * 100) % 13 == 0)
                orgVersionPeriod = orgVersionPeriod - 1;

            //get accounting defaults
            ClsOrgVersionSpecificContent orgVersionContent = await _pUtility.GetOrgVersionSpecificContentAsync(userDetails.user_name, orgVersionPeriod);

            string module = "INV";
            string linkType = "VAT_REF_PROG_CODE";
            string orgVersion = orgVersionContent.orgVersion;
            IEnumerable<tmd_acc_defaults> defaultAccountingInfo = await _unitOfWork.FinancingProjectRepository.GetdefaultAccountingInfoAsync(userDetails.tenant_id, module, linkType, orgVersion);

            //validate defaults
            string defaultSetup = await ValidateDefaultsAsync(defaultAccountingInfo, VATData.Select(x => x.program_code).Distinct().ToList(), userId, budgetYear);
            if (!string.IsNullOrEmpty(defaultSetup))
            {
                result.Error = true;
                result.Message = defaultSetup;
                return result;
            }
            //insert Vat or loan data in DB
            InsertLoanVatDataInDB(userDetails, VATData, defaultAccountingInfo, period);
            await _unitOfWork.CompleteAsync();

            result.Error = false;
            result.Message = ((langStringValuesFinancing.FirstOrDefault(v => v.Key == "finproj_VAT_updated")).Value).LangText;
            return result;
        }

        public async Task<InvestmentAdminLoanVatCalMessage> CalculateLoanAsync(string userId, int period)
        {
            InvestmentAdminLoanVatCalMessage result = new InvestmentAdminLoanVatCalMessage();
            int budgetYear = period / 100;

            UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);

            Dictionary<string, clsLanguageString> langStringValuesFinancing = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Financingproject");

            //get the loan amount
            IEnumerable<AdminInvLoanVatCalHelper> loanRowData = await _unitOfWork.InvestmentAdminMRRepository.GetVatAndLoanCalDataAsync(userDetails.tenant_id, period, true);

            // update with default program code whereever we have null
            string defaultProgCode = await _unitOfWork.FinancingProjectRepository.GetDefaultProgramCodeAsync(userDetails.tenant_id);
            foreach (var item in loanRowData)
            {
                if (string.IsNullOrEmpty(item.program_code) || item.program_code == "null")
                {
                    item.program_code = defaultProgCode;
                }
            }
            //group data based on program code

            int orgVersionPeriod = period;
            if ((period - budgetYear * 100) % 13 == 0)
                orgVersionPeriod = orgVersionPeriod - 1;

            List<AdminInvLoanVatCalHelper> loanData = (from vr in loanRowData
                                                       group vr by new { vr.program_code, vr.year } into grp
                                                       select new AdminInvLoanVatCalHelper
                                                       {
                                                           program_code = grp.Key.program_code,
                                                           year = grp.Key.year,
                                                           amount = grp.Sum(x => x.amount)
                                                       }).ToList();

            //get accounting defaults
            ClsOrgVersionSpecificContent orgVersionContent = await _pUtility.GetOrgVersionSpecificContentAsync(userDetails.user_name, orgVersionPeriod);
            string module = "INV";
            string linkType = "LOAN_PROG_CODE";
            string orgVersion = orgVersionContent.orgVersion;
            IEnumerable<tmd_acc_defaults> defaultAccountingInfo = await _unitOfWork.FinancingProjectRepository.GetdefaultAccountingInfoAsync(userDetails.tenant_id, module, linkType, orgVersion);

            //validate defaults
            string defaultSetup = await ValidateDefaultsAsync(defaultAccountingInfo, loanData.Select(x => x.program_code).Distinct().ToList(), userId, budgetYear);
            if (!string.IsNullOrEmpty(defaultSetup))
            {
                result.Error = true;
                result.Message = defaultSetup;
                return result;
            }
            //insert Vat or loan data in DB
            InsertLoanVatDataInDB(userDetails, loanData, defaultAccountingInfo, period);

            await _unitOfWork.CompleteAsync();
            result.Error = false;
            result.Message = ((langStringValuesFinancing.FirstOrDefault(v => v.Key == "finproj_loan_updated")).Value).LangText;
            return result;
        }

        public async Task<string> ValidateDefaultsAsync(IEnumerable<tmd_acc_defaults> defaultAccountingInfo, List<string> programCode, string userId, int budgetYear)
        {
            UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStringValuesFinancing = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Financingproject");
            StringBuilder sb = new StringBuilder();
            foreach (var item in programCode)
            {
                if (string.IsNullOrEmpty(GetValidMatchingData(defaultAccountingInfo, item, "ACCOUNT")))
                {
                    sb.Append(((langStringValuesFinancing.FirstOrDefault(v => v.Key == "finproj_default_ac_missing")).Value).LangText + item + "<br/>");
                }
                if (string.IsNullOrEmpty(GetValidMatchingData(defaultAccountingInfo, item, "DEPARTMENT")))
                {
                    sb.Append(((langStringValuesFinancing.FirstOrDefault(v => v.Key == "finproj_default_dep_missing")).Value).LangText + item + "<br/>");
                }
                if (string.IsNullOrEmpty(GetValidMatchingData(defaultAccountingInfo, item, "FUNCTION")))
                {
                    sb.Append(((langStringValuesFinancing.FirstOrDefault(v => v.Key == "finproj_default_fun_missing")).Value).LangText + item + "<br/>");
                }

                if (string.IsNullOrEmpty(GetValidMatchingData(defaultAccountingInfo, item, "PROJECT")))
                {
                    sb.Append(((langStringValuesFinancing.FirstOrDefault(v => v.Key == "finproj_default_proj_missing")).Value).LangText + item + "<br/>");
                }
                else
                {
                    tco_projects projData = await _unitOfWork.InvestmentProjectRepository.GetProjDetailsAsync(userDetails.tenant_id, budgetYear,
                        defaultAccountingInfo.FirstOrDefault(x => x.acc_type == "PROJECT" && x.link_value == item).acc_value);
                    if (projData != null && string.IsNullOrEmpty(projData.fk_prog_code))
                    {
                        sb.Append(((langStringValuesFinancing.FirstOrDefault(v => v.Key == "finproj_proj_programCode_missing")).Value).LangText + item + "<br/>");
                    }
                }
            }
            return sb.ToString();
        }

        public async Task<List<KeyValuePair>> GetAdminInvestmentColumnSelectorAsync(string userId, int Period, List<GridColumnHelper> allColumns)
        {
            UserData user = await _pUtility.GetUserDetailsAsync(userId);
            List<KeyValuePair> columsToDisplayList = new List<KeyValuePair>();
            List<KeyValuePair> columsconfig = new List<KeyValuePair>();
            var ColumnInfoForOverViewGrid = allColumns;
            string flagName = "Admin_inv_bud_change_col_view";

            //get data from db
            var columnSelectorTenant = (await _unitOfWork.InvestmentAdminMRRepository.GetTcoApplicationFlagDataMRAsync(user.tenant_id, flagName, Period, 0)).FirstOrDefault();
            //get the colum selctor value form blob
            columsconfig = await GetColumConfigFromBlobTableAsync(user, columsconfig, columnSelectorTenant);

            //format the columlist
            FormatColumSelectorList(columsToDisplayList, columsconfig, ColumnInfoForOverViewGrid);

            return columsToDisplayList;
        }

        public async Task<string> SaveColumnsConfigForMrAdminInvestmentAsync(string userId, List<KeyValuePair> invCols, int period)
        {
            String strJsonColumnSet = JsonConvert.SerializeObject(invCols);

            try
            {
                Guid invColSelectGuid = Guid.NewGuid();
                await SaveColumnSelectConfigForTenantAsync(userId, strJsonColumnSet, invColSelectGuid, 0, period);
                return "success";
            }
            catch (InvalidOperationException ex)
            {
                throw new InvalidOperationException(ex.Message);
            }
        }

        public async Task<string> GenerateBaseDataAsync(string userId, int period)
        {
            UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
            return await _unitOfWork.InvestmentAdminMRRepository.GenerateBaseDataInDBAsync(userDetails.tenant_id, period, userDetails.pk_id);
        }

        public async Task<string> CopyDataToFinancialPlanAsync(string userId, InvestmentAdminCopyDataToFPHelper input)
        {
            UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);

            input.userPkId = userDetails.pk_id;
            input.tenantId = userDetails.tenant_id;
            //set the user Adj code status to 1 for selected code
            await UpdateUserAdjCodePrefixAsync(userDetails.tenant_id, input.userAdjustmentCode, input.adjustmentCode);
            return await _unitOfWork.InvestmentAdminMRRepository.CopyProjBudDataToFPTablesAsync(input);
        }

        public async Task UpdateUserAdjCodePrefixAsync(int tenant_id, string userAdjCode, String adjCode)
        {
            tco_user_adjustment_codes selectedUserAdjCode = await _unitOfWork.InvestmentAdminMRRepository.GetSelectedUserAdjustmentCodeAsync(tenant_id, userAdjCode);
            if (selectedUserAdjCode != null)
            {
                selectedUserAdjCode.prefix_adjCode = adjCode;
            }
            await _unitOfWork.CompleteAsync();
        }

        public async Task<InvestmentAdminDefault> GetAddNewDefaultDataAsync(string userId, int period)
        {
            UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
            int clientId = userDetails.client_id;
            TimeSpan cacheTimeOut = new TimeSpan(24, 0, 0);
            InvestmentAdminDefault defultData = new InvestmentAdminDefault();

            int kalYear = period / 100;
            if ((period - kalYear * 100) % 13 == 0)
                period = period - 1;

            var orgVersionContent = await _pUtility.GetOrgVersionSpecificContentAsync(userDetails.user_name, period);
            //altercode
            defultData.AlterCodeList = await GetAlterCodeListAsync(userDetails);
            //department
            defultData.DepartmentList = (await GetDeptListAsync(userDetails, clientId, cacheTimeOut, orgVersionContent, null)).ToList();

            var FunctionListAsync = GetFunctionListAsync(userDetails, clientId, cacheTimeOut);
            var ProjectAsync = GetProjectListAsync(userDetails, clientId, cacheTimeOut, kalYear);
            var AccountAsync = GetAccountListAsync(userDetails, clientId, cacheTimeOut);
            var StatusAsync = GetStatusListAsync(userDetails, clientId, cacheTimeOut);

            await Task.WhenAll(FunctionListAsync, ProjectAsync, AccountAsync, StatusAsync);
            //function
            defultData.FunctionList = FunctionListAsync.Result.ToList();
            //project
            defultData.ProjectList = ProjectAsync.Result.ToList();
            //account
            defultData.AccountDataList = AccountAsync.Result.ToList();
            //status
            defultData.StatusList = StatusAsync.Result.ToList();

            return defultData;
        }

        public async Task<List<KeyValueInt>> GetBudgetChangeDropDownListAsync(string userId, int period)
        {
            UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);

            return await _unitOfWork.InvestmentAdminMRRepository.GetBudgetChangesAsync(userDetails.tenant_id, period);
        }

        public async Task<List<InvestmentAdminForecastGridHelper>> GetAdminInvestmentForecastGridData(string userId, int period)
        {
            UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
            List<InvestmentAdminForecastGridHelper> resultData = await _unitOfWork.InvestmentAdminMRRepository.GetAdminInvestmentForecastGridBaseDataAsync(userDetails.tenant_id, period);
            Dictionary<string, clsLanguageString> langStringValuesFinancing = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Financingproject");
            return FormatAdminInvestmentForecastGridData(resultData, langStringValuesFinancing);
        }

        public async Task<MemoryStream> ExportAdminInvGridAsync(string userId, AdminInvGridExcelInputHelper dataToExport)
        {
            UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
            MemoryStream stream = new MemoryStream();

            using (Workbook wb = new Workbook())
            {
                Worksheet sheet = wb.Worksheets[0];
                InsertAdminInvHeaderRow(userId, wb, dataToExport.columns);
                InsertAdminInvChildRows(dataToExport, sheet);
                wb.Save(stream, SaveFormat.Xlsx);

                return stream;
            }
        }

        public async Task<string> DeleteAdminInvGridDataAsync(string userId, int Period, AdminInvDeleteHelper input)
        {
            try
            {
                int budgetYear = Period / 100;
                UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);

                List<tmr_proj_bud_changes> baseData = (await _unitOfWork.InvestmentAdminMRRepository.GetllTmrProjBudChangesDataAsync(userDetails.tenant_id, Period)).ToList();// get all the data for period

                List<tmr_proj_bud_changes> matchingDataSet = null;
                List<int> yearValueList = (Period % 100 == 13) ? new List<int>() { budgetYear, budgetYear + 1 } : new List<int>() { budgetYear, budgetYear + 1, budgetYear + 2, budgetYear + 3, budgetYear + 4 };
                //year to consider based on period
                foreach (var d in input.DataToDelete)
                {
                    matchingDataSet = baseData.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.fk_project_code == d.prevProjCode && x.fk_account_code == d.prevAccCode && x.fk_function_code == d.prevFuncCode && x.fk_department_code == d.prevDeptCode && x.fk_alter_code == d.prevAlterCode && yearValueList.Contains(x.year) && (x.description == null ? string.Empty : x.description) == d.prevDesc && x.vat_rate == d.prevVatRate && x.vat_refund == d.prevVatRefund && x.forecast_period == Period && (!x.is_vat_row)).ToList();// get matching data

                    await _unitOfWork.InvestmentAdminMRRepository.DeleteMatchingTmrProjBudDataAsync(userDetails.tenant_id, matchingDataSet.Select(x => x.pk_id).ToList());// remove data
                }
                await _unitOfWork.CompleteAsync();
                return "success";
            }
            catch (Exception)
            {
                throw new InvalidOperationException();
            }
        }

        #region private

        private List<InvestmentAdminForecastGridHelper> FormatAdminInvestmentForecastGridData(List<InvestmentAdminForecastGridHelper> baseData, Dictionary<string, clsLanguageString> langStringValuesFinancing)
        {
            List<InvestmentAdminForecastGridHelper> formatedData = new List<InvestmentAdminForecastGridHelper>();

            if (baseData != null && baseData.Any())
            {
                int dataLength = baseData.Count();
                int count = 0;
                string groupitemName = baseData[0].GroupItemName;
                foreach (InvestmentAdminForecastGridHelper iaf in baseData)
                {
                    count++;
                    if (iaf.LineItemName == "Utdeling fra selskaper") continue;
                    if (groupitemName != iaf.GroupItemName || count == dataLength)
                    {
                        if (count == dataLength) formatedData.Add(iaf);
                        formatedData.Add(new InvestmentAdminForecastGridHelper
                        {
                            LineItemId = -1,
                            LineItemName = groupitemName,
                            Year1Amount = baseData.Where(x => x.GroupItemName == groupitemName).Sum(x => x.Year1Amount),
                            Year2Amount = baseData.Where(x => x.GroupItemName == groupitemName).Sum(x => x.Year2Amount),
                            Year3Amount = baseData.Where(x => x.GroupItemName == groupitemName).Sum(x => x.Year3Amount),
                            Year4Amount = baseData.Where(x => x.GroupItemName == groupitemName).Sum(x => x.Year4Amount),
                            Changeyear1amt = baseData.Where(x => x.GroupItemName == groupitemName).Sum(x => x.Changeyear1amt),
                            Changeyear2amt = baseData.Where(x => x.GroupItemName == groupitemName).Sum(x => x.Changeyear2amt),
                            Changeyear3amt = baseData.Where(x => x.GroupItemName == groupitemName).Sum(x => x.Changeyear3amt),
                            Changeyear4amt = baseData.Where(x => x.GroupItemName == groupitemName).Sum(x => x.Changeyear4amt)
                        });
                        if (count != dataLength)
                        {
                            formatedData.Add(iaf);
                            groupitemName = iaf.GroupItemName;
                        }
                    }
                    else
                    {
                        formatedData.Add(iaf);
                    }
                }
                formatedData.Add(new InvestmentAdminForecastGridHelper
                {
                    LineItemId = -1,
                    LineItemName = ((langStringValuesFinancing.FirstOrDefault(v => v.Key == "finproj_proj_Admin_Summary")).Value).LangText,
                    Year1Amount = baseData.Sum(x => x.Year1Amount),
                    Year2Amount = baseData.Sum(x => x.Year2Amount),
                    Year3Amount = baseData.Sum(x => x.Year3Amount),
                    Year4Amount = baseData.Sum(x => x.Year4Amount),
                    Changeyear1amt = baseData.Sum(x => x.Changeyear1amt),
                    Changeyear2amt = baseData.Sum(x => x.Changeyear2amt),
                    Changeyear3amt = baseData.Sum(x => x.Changeyear3amt),
                    Changeyear4amt = baseData.Sum(x => x.Changeyear4amt)
                });
            }
            return formatedData;
        }

        private async Task SaveColumnSelectConfigForTenantAsync(string userId, string strJsonColumnSet, Guid invColSelectGuid, int orgLevel, int period)
        {
            UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
            TenantDBContext tendbContext = await _pUtility.GetTenantDBContextAsync();

            string flagName = "Admin_inv_bud_change_col_view";
            var columnSelectorTenant = (await _unitOfWork.InvestmentAdminMRRepository.GetTcoApplicationFlagDataMRAsync(userDetails.tenant_id, flagName, period, orgLevel)).FirstOrDefault();

            if (columnSelectorTenant != null)
            {
                if (columnSelectorTenant.flag_guid == null)
                {
                    columnSelectorTenant.flag_guid = invColSelectGuid;
                    tendbContext.SaveChanges();
                }
                else
                {
                    invColSelectGuid = columnSelectorTenant.flag_guid.Value;
                }
            }
            else
            {
                //Create new record
                tco_application_flag tafrec = new tco_application_flag();
                tafrec.fk_tenant_id = userDetails.tenant_id;
                tafrec.flag_name = flagName;
                tafrec.flag_guid = invColSelectGuid;
                tafrec.flag_key_id = orgLevel.ToString();
                tafrec.flag_status = 0;
                tafrec.period = period;
                tafrec.budget_year = 0;
                tafrec.updated_by = userDetails.pk_id;
                tafrec.updated = DateTime.UtcNow;

                tendbContext.tco_application_flag.Add(tafrec);
                tendbContext.SaveChanges();
            }
            await SaveColumnSelectConfigInBlobStore(userDetails.pk_id, invColSelectGuid, strJsonColumnSet, userDetails.tenant_id);
        }

        private async Task SaveColumnSelectConfigInBlobStore(int userpkId, Guid invColSelectGuid, dynamic strJsonColumnSet, int tenant_id)
        {
            clsTenantCloudTableEntity updateEntity = await _pUtility.GetCloudTableContentAsync("WADTenantData", tenant_id.ToString(), invColSelectGuid.ToString());
            if (updateEntity != null)
            {
                updateEntity.data = strJsonColumnSet;
                await _pUtility.UpdateCloudTableContentAsync(userpkId, "WADTenantData", tenant_id.ToString(), invColSelectGuid.ToString(), strJsonColumnSet);
            }
            else
            {
                clsTenantCloudTableEntity _clsTenantCloudTableEntity = new clsTenantCloudTableEntity(tenant_id.ToString(), invColSelectGuid.ToString());
                _clsTenantCloudTableEntity.data = strJsonColumnSet;
                _clsTenantCloudTableEntity.Timestamp = DateTime.UtcNow;
                await _pUtility.InsertCloudTableContentAsync(userpkId, "WADTenantData", tenant_id.ToString(), invColSelectGuid.ToString(), _clsTenantCloudTableEntity);
            }
        }

        private List<InvestmentAdminBaseGrid2Helper> FormatAdminInvGrid2(IEnumerable<InvestmentAdminBaseGridHelper> baseData)
        {
            List<InvestmentAdminBaseGrid2Helper> groupedData = (from b in baseData
                                                                group b by new
                                                                {
                                                                    LineGroupId = b.LineGroupId,
                                                                    LineGroupName = b.LineGroupName
                                                                } into grp
                                                                select new InvestmentAdminBaseGrid2Helper()
                                                                {
                                                                    LineGroupId = grp.Key.LineGroupId,
                                                                    LineGroupName = grp.Key.LineGroupName,
                                                                    Year1Amt = grp.Sum(z => z.Year1Amt),
                                                                    Year2Amt = grp.Sum(z => z.Year2Amt),
                                                                    Year3Amt = grp.Sum(z => z.Year3Amt),
                                                                    Year4Amt = grp.Sum(z => z.Year4Amt),
                                                                    Year5Amt = grp.Sum(z => z.Year5Amt),
                                                                }).OrderBy(x => x.LineGroupName).ToList();

            groupedData.Add(new InvestmentAdminBaseGrid2Helper()
            {
                LineGroupId = -1,
                LineGroupName = "Sum",
                Year1Amt = groupedData.Sum(z => z.Year1Amt),
                Year2Amt = groupedData.Sum(z => z.Year2Amt),
                Year3Amt = groupedData.Sum(z => z.Year3Amt),
                Year4Amt = groupedData.Sum(z => z.Year4Amt),
                Year5Amt = groupedData.Sum(z => z.Year5Amt),
            });

            return groupedData;
        }

        private InvestmentAdminGridHelper FormatAdminInvGrid1(IEnumerable<InvestmentAdminBaseGridHelper> baseData, int Period, List<string> SelectedCols, AdminInvSearchHelper searchInput)
        {
            InvestmentAdminGridHelper resultData = new InvestmentAdminGridHelper();

            List<InvestmentAdminBaseGridHelper> groupedData;
            IEnumerable<InvestmentAdminBaseGridHelper> data;
            if (searchInput.isHideVAT)
            {
                data = baseData.Where(x => x.isVatRow == false);
            }
            else
            {
                data = baseData;
            }
            groupedData = (from b in data
                           group b by new
                           {
                               b.MainProjCode,
                               b.MainProjName,
                               b.MainProjStatus,
                               b.MainProjStatusDes,
                               b.MainProjDesc,
                               b.AccCode,
                               b.AccName,
                               b.ProjCode,
                               b.ProjName,
                               b.ProjStatus,
                               b.ProjStatusDes,
                               b.ProjDesc,
                               b.DeptCode,
                               b.DeptName,
                               b.FuncCode,
                               b.FuncName,
                               b.AlterCode,
                               b.AlterCodeName,
                               b.VatRate,
                               b.VatRefund,
                               b.isVatAccount,
                               b.isVatRow,
                               Desc = SelectedCols.Contains("Desc") ? b.Desc : "",
                               LineItemId = SelectedCols.Contains("LineItemName") ? b.LineItemId : 0,
                               LineItemName = SelectedCols.Contains("LineItemName") ? b.LineItemName : "",
                               b.progCode,
                               b.invStatusName,
                               b.invStatus,
                               b.progCodeId
                           } into grp
                           select new InvestmentAdminBaseGridHelper()
                           {
                               AlterCode = (grp.Key.AlterCode.Contains("Velg endringskode")) ? string.Empty : grp.Key.AlterCode,
                               AlterCodeName = grp.Key.AlterCodeName,
                               MainProjCode = grp.Key.MainProjCode,
                               MainProjName = grp.Key.MainProjName,
                               MainProjStatus = grp.Key.MainProjStatus,
                               MainProjStatusDes = grp.Key.MainProjStatusDes,
                               MainProjDesc = grp.Key.MainProjDesc,
                               FuncCode = grp.Key.FuncCode,
                               FuncName = grp.Key.FuncName,
                               DeptCode = grp.Key.DeptCode,
                               DeptName = grp.Key.DeptName,
                               ProjCode = grp.Key.ProjCode,
                               ProjName = grp.Key.ProjName,
                               ProjStatus = grp.Key.ProjStatus,
                               ProjStatusDes = grp.Key.ProjStatusDes,
                               ProjDesc = grp.Key.ProjDesc,
                               Desc = grp.Key.Desc,
                               LineItemId = grp.Key.LineItemId,
                               LineItemName = grp.Key.LineItemName,
                               AccCode = grp.Key.AccCode,
                               AccName = grp.Key.AccName,
                               VatRefund = grp.Key.VatRefund,
                               VatRate = grp.Key.VatRate,
                               AccStatus = grp.Key.isVatAccount ? "1" : "0",
                               prevVatRefund = grp.Key.VatRefund,
                               prevVatRate = grp.Key.VatRate,
                               prevAccCode = grp.Key.AccCode,
                               prevAlterCode = grp.Key.AlterCode,
                               prevDeptCode = grp.Key.DeptCode,
                               prevDesc = grp.Key.Desc,
                               prevFuncCode = grp.Key.FuncCode,
                               prevProjCode = grp.Key.ProjCode,
                               isVatRow = grp.Key.isVatRow,
                               Year1Amt = grp.Sum(z => z.Year1Amt),
                               Year2Amt = grp.Sum(z => z.Year2Amt),
                               Year3Amt = grp.Sum(z => z.Year3Amt),
                               Year4Amt = grp.Sum(z => z.Year4Amt),
                               Year5Amt = grp.Sum(z => z.Year5Amt),
                               progCode = grp.Key.progCode,
                               invStatusName = grp.Key.invStatusName,
                               progCodeId = grp.Key.progCodeId,
                               invStatus = grp.Key.invStatus,
                           }).OrderBy(x => x.LineItemId).ThenBy(x => x.MainProjCode).ThenBy(x => x.AccCode).ThenBy(x => x.DeptCode).ThenBy(x => x.FuncCode).ThenBy(x => x.ProjCode).ThenBy(x => x.progCode).ToList();

            List<KeyValue> progCodeDS = new List<KeyValue>();
            var progCodeList = groupedData.Where(x => !string.IsNullOrEmpty(x.ProjCode)).Select(x => new { x.progCodeId, x.progCode }).Distinct();
            foreach (var progData in progCodeList)
            {
                if (!string.IsNullOrWhiteSpace(progData.progCode))
                {
                    progCodeDS.Add(new KeyValue
                    {
                        Key = progData.progCodeId,
                        Value = progData.progCode
                    });
                }
            }

            List<KeyValue> invStatusNameDS = new List<KeyValue>();
            var statusList = groupedData.Where(x => x.invStatus != null).Select(x => new { x.invStatus, x.invStatusName }).Distinct();
            foreach (var statusData in statusList)
            {
                invStatusNameDS.Add(new KeyValue
                {
                    Key = statusData.invStatus.ToString(),
                    Value = statusData.invStatusName
                });
            }

            groupedData = SearchFilterData(searchInput, groupedData).ToList();

            groupedData.Add(new InvestmentAdminBaseGridHelper()
            {
                AlterCode = string.Empty,
                AlterCodeName = string.Empty,
                MainProjCode = string.Empty,
                MainProjName = string.Empty,
                MainProjStatus = 0,
                MainProjStatusDes = string.Empty,
                MainProjDesc = string.Empty,
                FuncCode = string.Empty,
                FuncName = string.Empty,
                DeptCode = string.Empty,
                DeptName = string.Empty,
                ProjCode = string.Empty,
                ProjName = string.Empty,
                ProjStatus = 0,
                ProjStatusDes = string.Empty,
                ProjDesc = string.Empty,
                Desc = string.Empty,
                LineItemId = -1,
                LineItemName = "Sum",
                AccCode = string.Empty,
                AccName = string.Empty,
                VatRefund = 0,
                VatRate = 0,
                AccStatus = "1",
                prevVatRefund = 0,
                prevVatRate = 0,
                prevAccCode = string.Empty,
                prevAlterCode = string.Empty,
                prevDeptCode = string.Empty,
                prevDesc = string.Empty,
                prevFuncCode = string.Empty,
                prevProjCode = string.Empty,
                Year1Amt = groupedData.Sum(z => z.Year1Amt),
                Year2Amt = groupedData.Sum(z => z.Year2Amt),
                Year3Amt = groupedData.Sum(z => z.Year3Amt),
                Year4Amt = groupedData.Sum(z => z.Year4Amt),
                Year5Amt = groupedData.Sum(z => z.Year5Amt),
                progCode = string.Empty,
                invStatusName = string.Empty,
                invStatus = null,
                progCodeId = string.Empty
            });

            resultData.Grid1 = groupedData.ToList();
            resultData.progCodeDS = progCodeDS.ToList();
            resultData.invStatusNameDS = invStatusNameDS.ToList();

            return resultData;
        }

        private IEnumerable<InvestmentAdminBaseGridHelper> SearchFilterData(AdminInvSearchHelper searchInput, IEnumerable<InvestmentAdminBaseGridHelper> result)
        {
            if (!string.IsNullOrEmpty(searchInput.LineItemName))
            {
                result = result.Where(x => x.LineItemName.ToLower().Contains(searchInput.LineItemName.ToLower()));
            }
            if (!string.IsNullOrEmpty(searchInput.MainProjCode))
            {
                result = result.Where(x => x.MainProjCode.ToLower().Contains(searchInput.MainProjCode.ToLower()));
            }
            if (!string.IsNullOrEmpty(searchInput.AccCode))
            {
                result = result.Where(x => x.AccCode.ToLower().Contains(searchInput.AccCode.ToLower()));
            }
            if (!string.IsNullOrEmpty(searchInput.DeptCode))
            {
                result = result.Where(x => x.DeptCode.ToLower().Contains(searchInput.DeptCode.ToLower()));
            }
            if (!string.IsNullOrEmpty(searchInput.FuncCode))
            {
                result = result.Where(x => x.FuncCode.Trim().ToLower().Contains(searchInput.FuncCode.Trim().ToLower()));
            }
            if (!string.IsNullOrEmpty(searchInput.ProjCode))
            {
                result = result.Where(x => x.ProjCode.Trim().ToLower().Contains(searchInput.ProjCode.Trim().ToLower()));
            }
            if (!string.IsNullOrEmpty(searchInput.AlterCode))
            {
                result = result.Where(x => x.AlterCode.ToLower().Contains(searchInput.AlterCode.ToLower()));
            }
            if (!string.IsNullOrEmpty(searchInput.Desc))
            {
                result = result.Where(x => x.Desc.ToLower().Contains(searchInput.Desc.ToLower()));
            }
            if (!string.IsNullOrEmpty(searchInput.VatRate.ToString()))
            {
                result = result.Where(x => x.VatRate.ToString().ToLower().Contains(searchInput.VatRate.ToString()));
            }
            if (!string.IsNullOrEmpty(searchInput.VatRefund.ToString()))
            {
                result = result.Where(x => x.VatRefund.ToString().ToLower().Contains(searchInput.VatRefund.ToString()));
            }
            if (!string.IsNullOrEmpty(searchInput.MainProjStatus))
            {
                result = result.Where(x => x.MainProjStatusDes.ToLower().Contains(searchInput.MainProjStatus.ToLower()));
            }
            if (!string.IsNullOrEmpty(searchInput.MainProjDesc))
            {
                result = result.Where(x => x.MainProjDesc.ToLower().Contains(searchInput.MainProjDesc.ToLower()));
            }
            if (!string.IsNullOrEmpty(searchInput.ProjStatus))
            {
                result = result.Where(x => x.ProjStatusDes.ToLower().Contains(searchInput.ProjStatus.ToLower()));
            }
            if (!string.IsNullOrEmpty(searchInput.ProjDesc))
            {
                result = result.Where(x => x.ProjDesc.ToLower().Contains(searchInput.ProjDesc.ToLower()));
            }
            if (searchInput.progCode.Count > 0)
            {
                result = result.Where(x => searchInput.progCode.Any(g => g.Equals(x.progCodeId)));
            }
            if (searchInput.invStatusName.Count > 0)
            {
                result = result.Where(x => searchInput.invStatusName.Any(g => g.Equals(x.invStatus.ToString())));
            }
            return result;
        }

        private async Task<List<InvestmentAdminBaseGridHelper>> FormatDataYearWiseAndGroupAsync(IEnumerable<InvestmentAdminBaseGridHelper> baseData, int Period, UserData userDetails)
        {
            int budgetYear = Period / 100;
            int clientId = userDetails.client_id;
            TimeSpan cacheTimeOut = new TimeSpan(24, 0, 0);
            int orgVersionPeriod = Period;
            if ((Period - budgetYear * 100) % 13 == 0)
                orgVersionPeriod = orgVersionPeriod - 1;

            var orgVersionContent = await _pUtility.GetOrgVersionSpecificContentAsync(userDetails.user_name, orgVersionPeriod);
            TenantDBContext dbContext = await _pUtility.GetTenantDBContextAsync();

            //altercode
            List<KeyValueStringData> alterCodeList = await GetAlterCodeListAsync(userDetails);

            //department
            List<KeyValueStringData> departmentList = (await GetDeptListAsync(userDetails, clientId, cacheTimeOut, orgVersionContent, null)).ToList();

            //function
            var functionCodeListAsync = await GetFunctionListAsync(userDetails, clientId, cacheTimeOut);
            List<KeyValueStringData> functionCodeList = functionCodeListAsync.ToList();

            //invStatus
            var invStatusList = (await GetInvStatusListAsync(userDetails, clientId, cacheTimeOut)).ToList();

            //program
            var programList = (await GetProgramListAsync(userDetails, clientId, cacheTimeOut)).ToList();

            List<InvestmentAdminBaseGridHelper> unFormatedData;
            int finalPeriodCheckValue = Period;
            if (Period % 100 == 13)
            {// #118371 - for period 13, we should check on previous forecast period
                finalPeriodCheckValue = Period - 1;
            }

            List<tmr_proj_status> tpsMainProjList = dbContext.tmr_proj_status.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.forecast_period == finalPeriodCheckValue && x.level == "MP").ToList();
            List<tmr_proj_status> tpsProjList = dbContext.tmr_proj_status.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.forecast_period == finalPeriodCheckValue && x.level == "P").ToList();
            //status
            var statusListAsync = await GetStatusListAsync(userDetails, clientId, cacheTimeOut);
            List<KeyValueStringData> statusList = statusListAsync.ToList();

            unFormatedData = (from b in baseData
                              join tps in tpsMainProjList on b.MainProjCode equals tps.level_id into tpsGr
                              from tpsMain in tpsGr.DefaultIfEmpty()
                              join tpsPl in tpsProjList on b.ProjCode equals tpsPl.level_id into tpsPlGr
                              from tpsProj in tpsPlGr.DefaultIfEmpty()
                              select new InvestmentAdminBaseGridHelper()
                              {
                                  AlterCode = b.AlterCode,
                                  AlterCodeName = GetItemValueForGivenKey(alterCodeList, b.AlterCode),
                                  MainProjCode = b.MainProjCode,
                                  MainProjName = b.MainProjName,
                                  MainProjStatus = tpsMain != null ? tpsMain.status : 0,
                                  MainProjStatusDes = tpsMain != null ? GetItemValueForGivenKey(statusList, tpsMain.status.ToString()) : string.Empty,
                                  MainProjDesc = tpsMain != null ? Regex.Replace(tpsMain.status_desc, "<.*?>", String.Empty) : string.Empty,
                                  FuncCode = b.FuncCode,
                                  FuncName = GetItemValueForGivenKey(functionCodeList, b.FuncCode),
                                  DeptCode = b.DeptCode,
                                  DeptName = GetItemValueForGivenKey(departmentList, b.DeptCode),
                                  ProjCode = b.ProjCode,
                                  ProjName = b.ProjName,
                                  ProjStatus = tpsProj != null ? tpsProj.status : 0,
                                  ProjStatusDes = tpsProj != null ? GetItemValueForGivenKey(statusList, tpsProj.status.ToString()) : string.Empty,
                                  ProjDesc = tpsProj != null ? Regex.Replace(tpsProj.status_desc, "<.*?>", String.Empty) : string.Empty,
                                  Desc = (!string.IsNullOrEmpty(b.Desc)) ? b.Desc : string.Empty,
                                  LineItemId = b.LineItemId,
                                  LineItemName = b.LineItemName,
                                  LineGroupId = b.LineGroupId,
                                  LineGroupName = b.LineGroupName,
                                  AccCode = b.AccCode,
                                  AccName = b.AccName,
                                  Year1Amt = SetYearValue(budgetYear, b),
                                  Year2Amt = SetYearValue(budgetYear + 1, b),
                                  Year3Amt = SetYearValue(budgetYear + 2, b),
                                  Year4Amt = SetYearValue(budgetYear + 3, b),
                                  Year5Amt = SetYearValue(budgetYear + 4, b),
                                  VatRate = b.VatRate,
                                  VatRefund = b.VatRefund,
                                  isVatAccount = b.isVatAccount,
                                  isVatRow = b.isVatRow,
                                  invStatusName = b.invStatus != null ? GetItemValueForGivenKey(invStatusList, b.invStatus.ToString()) : string.Empty,
                                  progCode = GetItemValueForGivenKey(programList, b.progCodeId),
                                  progCodeId = b.progCodeId,
                                  invStatus = b.invStatus
                              }).ToList();

            return unFormatedData;
        }

        private static decimal SetYearValue(int budgetYear, InvestmentAdminBaseGridHelper d)
        {
            return Math.Round((d.Year == budgetYear) ? d.Amount : 0);
        }

        private static decimal GetAmtValueForGivenIterration(int i, InvestmentAdminBaseGridHelper d)
        {
            decimal amount = 0;
            switch (i)
            {
                case 0: amount = d.Year1Amt; break;
                case 1: amount = d.Year2Amt; break;
                case 2: amount = d.Year3Amt; break;
                case 3: amount = d.Year4Amt; break;
                case 4: amount = d.Year5Amt; break;
            }
            return amount;
        }

        private string GetItemValueForGivenKey(List<KeyValueStringData> ItemList, string Itemkey)
        {
            return ItemList.FirstOrDefault(x => x.Key == Itemkey) != null ? ItemList.FirstOrDefault(x => x.Key == Itemkey).Value : string.Empty;
        }

        private string GetItemValueForGivenMPKey(List<KeyValueStringData> ItemList, string Itemkey)
        {
            return ItemList.FirstOrDefault(x => x.Key == Itemkey) != null ? ItemList.FirstOrDefault(x => x.Key == Itemkey).Value : string.Empty;
        }

        private string GetItemValueForGivenProjectKey(List<KeyValueStringData> ItemList, string Itemkey)
        {
            return ItemList.FirstOrDefault(x => x.Key == Itemkey) != null ? ItemList.FirstOrDefault(x => x.Key == Itemkey).Value : string.Empty;
        }

        private async Task<List<KeyValueStringData>> GetAlterCodeListAsync(UserData userDetails)
        {
            dynamic lstAlterCode = await _pconseq.GetAlterCodesAsync(userDetails.user_name, 1000);
            List<KeyValueStringData> alterCodeList = new List<KeyValueStringData>();
            if (lstAlterCode != null)
            {
                foreach (var item in lstAlterCode)
                {
                    KeyValueStringData alterCodeHelper = new KeyValueStringData
                    {
                        Key = item.key,
                        Value = item.value,
                    };
                    alterCodeList.Add(alterCodeHelper);
                }
            }
            return alterCodeList;
        }

        private async Task<List<ProjectKeyVatRef>> GetProjectListAsync(UserData userDetails, int clientId, TimeSpan cacheTimeOut, int budgetYear)
        {
            List<ProjectKeyVatRef> projectCodeList;
            TenantDBContext dbContext = await _pUtility.GetTenantDBContextAsync();
            string cExpProjData = await _cache.GetStringForUserAsync(clientId, userDetails.tenant_id, userDetails.user_name, "AdminINV_MR_Project_" + budgetYear);
            if (cExpProjData == null)
            {
                //invStatus
                var invStatusList = (await GetInvStatusListAsync(userDetails, clientId, cacheTimeOut)).ToList();

                //program
                var programList = (await GetProgramListAsync(userDetails, clientId, cacheTimeOut)).ToList();

                IEnumerable<tco_projects> projectCodesAsync = await _pUtility.GetProjectsBaseProjectTypeSetupAsync(userDetails.user_name, (int)ProjectUsageType.Inv_type);
                var projectCodeKeyVal = (from p in projectCodesAsync.Where(x => budgetYear >= x.date_from.Year && budgetYear <= x.date_to.Year)
                                         join tmp in dbContext.tco_main_projects.Where(x => x.fk_tenant_id == userDetails.tenant_id && budgetYear >= x.budget_year_from.Year && budgetYear <= x.budget_year_to.Year)
                                                                                                             on new { a = p.fk_main_project_code, b = p.fk_tenant_id }
                                                                                                         equals new { a = tmp.pk_main_project_code, b = tmp.fk_tenant_id } into tmgrp
                                         from lftMp in tmgrp.DefaultIfEmpty()
                                         select new ProjectKeyVatRef
                                         {
                                             key = p.pk_project_code,
                                             value = p.pk_project_code + "-" + p.project_name,
                                             vat_rate = p.vat_rate,
                                             vat_refund = p.vat_refund,
                                             mainProjCode = lftMp != null ? lftMp.pk_main_project_code : string.Empty,
                                             mainProjName = lftMp != null ? lftMp.pk_main_project_code + "-" + lftMp.main_project_name : string.Empty,
                                             progCode = p.fk_prog_code,
                                             invStatus = lftMp != null ? (lftMp.inv_status != null ? lftMp.inv_status.Value.ToString() : "0") : string.Empty
                                         }).ToList();

                projectCodeList = new List<ProjectKeyVatRef>();
                foreach (var item in projectCodeKeyVal)
                {
                    ProjectKeyVatRef saProject = new ProjectKeyVatRef();
                    saProject.key = item.key;
                    saProject.value = item.value;
                    saProject.vat_rate = item.vat_rate;
                    saProject.vat_refund = item.vat_refund;
                    saProject.mainProjCode = item.mainProjCode;
                    saProject.mainProjName = item.mainProjName;
                    saProject.progCode = GetItemValueForGivenKey(programList, item.progCode);
                    saProject.invStatus = GetItemValueForGivenKey(invStatusList, item.invStatus.ToString());

                    if (!projectCodeList.Select(x => x.key).Contains(item.key))
                    {
                        projectCodeList.Add(saProject);
                    }
                }
                cExpProjData = JsonConvert.SerializeObject(projectCodeList);

                await _cache.SetStringForUserAsync(clientId, userDetails.tenant_id, userDetails.user_name, "AdminINV_MR_Project_" + budgetYear, cExpProjData, cacheTimeOut);
            }
            else
            {
                projectCodeList = JsonConvert.DeserializeObject<List<ProjectKeyVatRef>>(cExpProjData);
            }

            return projectCodeList;
        }

        private async Task<IEnumerable<KeyValueStringData>> GetFunctionListAsync(UserData userDetails, int clientId, TimeSpan cacheTimeOut)
        {
            IEnumerable<KeyValueStringData> FunctionCodeList;
            string InvFunctionCache = await _cache.GetStringForUserAsync(clientId, userDetails.tenant_id, userDetails.user_name, "AdminINV_MR_Function");
            if (string.IsNullOrEmpty(InvFunctionCache))
            {
                IEnumerable<InvestmentFunctionList> functionCodes = await _pUtility.GetAllServiceAreaFunctionsAdminAsync(userDetails.user_name);
                FunctionCodeList = (from fn in functionCodes
                                    select new KeyValueStringData
                                    {
                                        Key = fn.fk_function_code,
                                        Value = fn.fk_function_code + "-" + fn.function_name
                                    }).ToList();
                InvFunctionCache = JsonConvert.SerializeObject(FunctionCodeList);
                await _cache.SetStringForUserAsync(clientId, userDetails.tenant_id, userDetails.user_name, "AdminINV_MR_Function", InvFunctionCache, cacheTimeOut);
            }
            else
            {
                FunctionCodeList = JsonConvert.DeserializeObject<List<KeyValueStringData>>(InvFunctionCache);
            }

            return FunctionCodeList;
        }

        private async Task<IEnumerable<KeyValueStringData>> GetDeptListAsync(UserData userDetails, int clientId, TimeSpan cacheTimeOut, ClsOrgVersionSpecificContent orgVersionContent, string saDepartment)
        {
            IEnumerable<KeyValueStringData> departmentList;
            string InvDepartmentCache = await _cache.GetStringForUserAsync(clientId, userDetails.tenant_id, userDetails.user_name + saDepartment, "AdminINV_MR_Department");
            if (string.IsNullOrEmpty(InvDepartmentCache))
            {
                List<clsOrgIdAndDepartments> lst = _pUtility.GetTenantDepartmentsForSelectedOrgId(orgVersionContent, userDetails.user_name, saDepartment, "investments_getalldepartment").ToList();
                departmentList = (from l in lst
                                  select new KeyValueStringData
                                  {
                                      Key = l.departmentValue,
                                      Value = l.departmentValue + "-" + l.departmentText
                                  }).ToList();
                InvDepartmentCache = JsonConvert.SerializeObject(departmentList);

                await _cache.SetStringForUserAsync(clientId,
                     userDetails.tenant_id, userDetails.user_name + saDepartment, "AdminINV_MR_Department", InvDepartmentCache, cacheTimeOut);
            }
            else
            {
                departmentList = JsonConvert.DeserializeObject<List<KeyValueStringData>>(InvDepartmentCache);
            }

            return departmentList;
        }

        private async Task<IEnumerable<KeyValueStringData>> GetAccountListAsync(UserData userDetails, int clientId, TimeSpan cacheTimeOut)
        {
            IEnumerable<KeyValueStringData> invAllAccount;
            string InvAccountCache = await _cache.GetStringForUserAsync(clientId, userDetails.tenant_id, userDetails.user_name, "AdminINV_MR_Account");
            //Account
            if (string.IsNullOrEmpty(InvAccountCache))
            {
                IEnumerable<InvestmentAccountList> accountCodeListAsync = await _pUtility.GetInvAccountDataAsync(userDetails.user_name);
                invAllAccount = (from a in accountCodeListAsync
                                 select new KeyValueStringData()
                                 {
                                     Key = a.fk_account_code,
                                     Value = a.fk_account_code + "-" + a.display_name,
                                     status = a.is_vat_account ? 1 : 0
                                 }).ToList();
                await _cache.SetStringForUserAsync(clientId, userDetails.tenant_id, userDetails.user_name, "AdminINV_MR_Account", InvAccountCache, cacheTimeOut);
            }
            else
            {
                invAllAccount = JsonConvert.DeserializeObject<List<KeyValueStringData>>(InvAccountCache);
            }

            return invAllAccount;
        }

        private async Task<IEnumerable<KeyValueStringData>> GetStatusListAsync(UserData userDetails, int clientId, TimeSpan cacheTimeOut)
        {
            IEnumerable<KeyValueStringData> statusList;
            string InvStatusCache = await _cache.GetStringForUserAsync(clientId, userDetails.tenant_id, userDetails.user_name, "AdminINV_MR_Status");
            //Status
            if (string.IsNullOrEmpty(InvStatusCache))
            {
                IEnumerable<ProgressStatus> tpsListAsync = await _pUtility.GetAllMrInvStatusDataAsync(userDetails.user_name);
                statusList = (from d in tpsListAsync
                              select new KeyValueStringData
                              {
                                  Key = d.Key.ToString(),
                                  Value = d.Value
                              }).ToList();

                InvStatusCache = JsonConvert.SerializeObject(statusList);
                await _cache.SetStringForUserAsync(clientId, userDetails.tenant_id, userDetails.user_name, "AdminINV_MR_Status", InvStatusCache, cacheTimeOut);
            }
            else
            {
                statusList = JsonConvert.DeserializeObject<List<KeyValueStringData>>(InvStatusCache);
            }

            return statusList;
        }

        private async Task<IEnumerable<KeyValueStringData>> GetProgramListAsync(UserData userDetails, int clientId, TimeSpan cacheTimeOut)
        {
            IEnumerable<KeyValueStringData> programList;
            string InvProgramCache = await _cache.GetStringForUserAsync(clientId, userDetails.tenant_id, userDetails.user_name, "AdminINV_MR_Program");
            //Program
            if (string.IsNullOrEmpty(InvProgramCache))
            {
                IEnumerable<InvestmentProgramCodeHelper> programListData = await _pUtility.GetInvestmentsProgramsAsync(userDetails.user_name, true);
                programList = (from d in programListData
                               select new KeyValueStringData
                               {
                                   Key = d.key.ToString(),
                                   Value = d.value
                               }).ToList();

                InvProgramCache = JsonConvert.SerializeObject(programList);
                await _cache.SetStringForUserAsync(clientId, userDetails.tenant_id, userDetails.user_name, "AdminINV_MR_Program", InvProgramCache, cacheTimeOut);
            }
            else
            {
                programList = JsonConvert.DeserializeObject<List<KeyValueStringData>>(InvProgramCache);
            }

            return programList;
        }

        private async Task<IEnumerable<KeyValueStringData>> GetInvStatusListAsync(UserData userDetails, int clientId, TimeSpan cacheTimeOut)
        {
            IEnumerable<KeyValueStringData> invStatusList;
            string InvStatusCache = await _cache.GetStringForUserAsync(clientId, userDetails.tenant_id, userDetails.user_name, "AdminINV_MR_InvStatusList");
            //Program
            if (string.IsNullOrEmpty(InvStatusCache))
            {
                IEnumerable<KeyValueData> invStatusListData = await _invProj.GetInvestmentStatusAsync(userDetails.user_name);
                invStatusList = (from d in invStatusListData
                                 select new KeyValueStringData
                                 {
                                     Key = d.KeyId.ToString(),
                                     Value = d.ValueString
                                 }).ToList();

                InvStatusCache = JsonConvert.SerializeObject(invStatusList);
                await _cache.SetStringForUserAsync(clientId, userDetails.tenant_id, userDetails.user_name, "AdminINV_MR_InvStatusList", InvStatusCache, cacheTimeOut);
            }
            else
            {
                invStatusList = JsonConvert.DeserializeObject<List<KeyValueStringData>>(InvStatusCache);
            }

            return invStatusList;
        }

        private async Task<List<KeyValuePair>> GetColumConfigFromBlobTableAsync(UserData user, List<KeyValuePair> columsconfig, tco_application_flag columnSelectorTenant)
        {
            if (columnSelectorTenant != null)
            {
                var pmColSel = columnSelectorTenant.flag_guid;

                if (pmColSel != null)
                {
                    clsTenantCloudTableEntity updateEntity = await _pUtility.GetCloudTableContentAsync("WADTenantData", user.tenant_id.ToString(), pmColSel.ToString());
                    if (updateEntity != null)
                    {
                        JArray pmInvColumnConfig = new JArray();
                        pmInvColumnConfig = JArray.FromObject(JsonConvert.DeserializeObject(updateEntity.data.ToString()));

                        columsconfig = (from s in pmInvColumnConfig
                                        select new KeyValuePair
                                        {
                                            key = (string)s["key"],
                                            value = (string)s["value"],
                                            isChecked = (bool)s["isChecked"],
                                        }).ToList();
                    }
                }
            }

            return columsconfig;
        }

        private static void FormatColumSelectorList(List<KeyValuePair> columsToDisplayList, List<KeyValuePair> columsconfig, List<GridColumnHelper> ColumnInfoForOverViewGrid)
        {
            foreach (var item in ColumnInfoForOverViewGrid)
            {
                if (item.field.ToLower() == "id")
                {
                    continue;
                }

                KeyValuePair temp = new KeyValuePair();
                temp.key = item.field;
                temp.value = item.title;
                if (columsconfig.Count > 0 && columsconfig.Select(x => x.key).Contains(item.field))
                {
                    temp.isChecked = columsconfig.FirstOrDefault(x => x.key == item.field).isChecked;
                }
                else
                {
                    temp.isChecked = true;
                }

                columsToDisplayList.Add(temp);
            }
        }

        private async Task CalculateGridVATAsync(UserData userDetails, InvestmentAdminBaseGridHelper inputData, AdminInvGridVatCalHelper vatCalInputAData)
        {
            //Delete existing VAT data
            await _unitOfWork.InvestmentAdminMRRepository.DeleteTmrProjBudChangesTransactionAsync(vatCalInputAData.pk_id, userDetails.tenant_id);
            await _unitOfWork.CompleteAsync();
            //create new transaction for VAT rows
            await GenerateVATTransactionAsync("i", inputData, vatCalInputAData, userDetails);
            await GenerateVATTransactionAsync("ineg", inputData, vatCalInputAData, userDetails);
            if (vatCalInputAData.calcVatComp)
            {
                await GenerateVATTransactionAsync("f", inputData, vatCalInputAData, userDetails);
            }
            await _unitOfWork.CompleteAsync();
        }

        private async Task GenerateVATTransactionAsync(string invType, InvestmentAdminBaseGridHelper inputData, AdminInvGridVatCalHelper vatCalInputAData,
                                            UserData userDetails)
        {
            IEnumerable<tco_projects> tcoProjectData = await _unitOfWork.InvestmentProjectManagerRepo.GetTcoProjectDataAsync(userDetails.tenant_id);
            SetVatRateAndRefund(inputData, tcoProjectData);
            string accountCode = string.Empty, departmentCode = string.Empty, functionCode = string.Empty;
            SetAccDeptFunValue(invType, inputData, vatCalInputAData, out accountCode, out departmentCode, out functionCode);
            if (invType == "i" || invType == "f")
            {
                tmr_proj_bud_changes transData = new tmr_proj_bud_changes
                {
                    pk_id = Guid.NewGuid(),
                    trans_id = vatCalInputAData.trans_id,
                    fk_tenant_id = userDetails.tenant_id,
                    fk_account_code = accountCode,
                    fk_function_code = functionCode,
                    fk_department_code = departmentCode,
                    fk_project_code = inputData.ProjCode,
                    free_dim_1 = vatCalInputAData.free_dim_1,
                    free_dim_2 = vatCalInputAData.free_dim_2,
                    free_dim_3 = vatCalInputAData.free_dim_3,
                    free_dim_4 = vatCalInputAData.free_dim_4,
                    vat_rate = 0,
                    vat_refund = 0,
                    year = vatCalInputAData.year,
                    amount = invType == "i" ? ((vatCalInputAData.amount) / (1 + (inputData.VatRate / 100)) * (inputData.VatRate / 100) * (inputData.VatRefund / 100)) :
                                       ((vatCalInputAData.amount) / (1 + (inputData.VatRate / 100)) * (inputData.VatRate / 100) * (inputData.VatRefund / 100)) * -1,

                    fk_alter_code = (!string.IsNullOrEmpty(inputData.AlterCode)) ? inputData.AlterCode : string.Empty,
                    is_vat_row = true,
                    fk_proj_trans_id = vatCalInputAData.pk_id,
                    description = string.Empty,
                    updated_by = userDetails.pk_id,
                    updated = DateTime.UtcNow,
                    forecast_period = vatCalInputAData.forecast_period,
                };
                _unitOfWork.GenericRepo.Add(transData);
            }
            else
            {
                tmr_proj_bud_changes transData = new tmr_proj_bud_changes
                {
                    pk_id = Guid.NewGuid(),
                    trans_id = vatCalInputAData.trans_id,
                    fk_tenant_id = userDetails.tenant_id,
                    fk_account_code = accountCode,
                    fk_function_code = functionCode,
                    fk_department_code = departmentCode,
                    fk_project_code = inputData.ProjCode,
                    free_dim_1 = vatCalInputAData.free_dim_1,
                    free_dim_2 = vatCalInputAData.free_dim_2,
                    free_dim_3 = vatCalInputAData.free_dim_3,
                    free_dim_4 = vatCalInputAData.free_dim_4,
                    vat_rate = 0,
                    vat_refund = 0,
                    year = vatCalInputAData.year,
                    amount = ((vatCalInputAData.amount) / (1 + (inputData.VatRate / 100)) * (inputData.VatRate / 100) * (inputData.VatRefund / 100)) * -1,

                    fk_alter_code = (!string.IsNullOrEmpty(inputData.AlterCode)) ? inputData.AlterCode : string.Empty,
                    is_vat_row = true,
                    fk_proj_trans_id = vatCalInputAData.pk_id,
                    description = string.Empty,
                    updated_by = userDetails.pk_id,
                    updated = DateTime.UtcNow,
                    forecast_period = vatCalInputAData.forecast_period,
                };
                _unitOfWork.GenericRepo.Add(transData);
            }
            await _unitOfWork.CompleteAsync();
        }

        private static void SetAccDeptFunValue(string invType, InvestmentAdminBaseGridHelper inputData, AdminInvGridVatCalHelper vatCalInputAData, out string accountCode, out string departmentCode, out string functionCode)
        {
            if (invType == "i")
            {
                accountCode = SetAccCodeValue(inputData, vatCalInputAData, "VAT_COST");

                departmentCode = SetDeptCodeValue(inputData, vatCalInputAData, "VAT_COST");
                functionCode = SetFunctionCodeValue(inputData, vatCalInputAData, "VAT_COST");
            }
            else if (invType == "f")
            {
                accountCode = SetAccCodeValue(inputData, vatCalInputAData, "VAT_COMP");

                departmentCode = SetDeptCodeValue(inputData, vatCalInputAData, "VAT_COMP");

                functionCode = SetFunctionCodeValue(inputData, vatCalInputAData, "VAT_COMP");
            }
            else
            {
                accountCode = inputData.AccCode;
                departmentCode = inputData.DeptCode;
                functionCode = inputData.FuncCode;
            }
        }

        private static string SetFunctionCodeValue(InvestmentAdminBaseGridHelper inputData, AdminInvGridVatCalHelper vatCalInputAData, string link_type)
        {
            return vatCalInputAData.defaultAccounts.FirstOrDefault(x => x.link_type == link_type && x.acc_type == "FUNCTION") != null ?
                               vatCalInputAData.defaultAccounts.FirstOrDefault(x => x.link_type == link_type && x.acc_type == "FUNCTION").acc_value
                               : inputData.FuncCode;
        }

        private static dynamic SetDeptCodeValue(InvestmentAdminBaseGridHelper inputData, AdminInvGridVatCalHelper vatCalInputAData, string link_type)
        {
            return vatCalInputAData.defaultAccounts.FirstOrDefault(x => x.link_type == link_type && x.acc_type == "DEPARTMENT") != null ?
                                               vatCalInputAData.defaultAccounts.FirstOrDefault(x => x.link_type == link_type && x.acc_type == "DEPARTMENT").acc_value
                                               : inputData.DeptCode;
        }

        private static dynamic SetAccCodeValue(InvestmentAdminBaseGridHelper inputData, AdminInvGridVatCalHelper vatCalInputAData, string link_type)
        {
            return vatCalInputAData.defaultAccounts.FirstOrDefault(x => x.link_type == link_type && x.acc_type == "ACCOUNT") != null ?
                                   vatCalInputAData.defaultAccounts.FirstOrDefault(x => x.link_type == link_type && x.acc_type == "ACCOUNT").acc_value
                                   : inputData.AccCode;
        }

        private static void SetVatRateAndRefund(InvestmentAdminBaseGridHelper inputData, IEnumerable<tco_projects> tcoProjectData)
        {
            if (inputData.VatRate == 0 && tcoProjectData.FirstOrDefault(x => x.pk_project_code == inputData.ProjCode) != null)
            {
                inputData.VatRate = tcoProjectData.FirstOrDefault(x => x.pk_project_code == inputData.ProjCode).vat_rate;
            }
            if (inputData.VatRefund == 0 && tcoProjectData.FirstOrDefault(x => x.pk_project_code == inputData.ProjCode) != null)
            {
                inputData.VatRefund = tcoProjectData.FirstOrDefault(x => x.pk_project_code == inputData.ProjCode).vat_refund;
            }
        }

        private void InsertLoanVatDataInDB(UserData userDetails, List<AdminInvLoanVatCalHelper> VATData, IEnumerable<tmd_acc_defaults> defaultAccountingInfo, int period)
        {
            Guid transId = Guid.NewGuid();
            foreach (var item in VATData)
            {
                if (item.amount != 0)
                {
                    tmr_proj_bud_changes loanTransaction = new tmr_proj_bud_changes
                    {
                        pk_id = Guid.NewGuid(),
                        trans_id = transId,
                        fk_account_code = GetValidMatchingData(defaultAccountingInfo, item.program_code, "ACCOUNT"),
                        fk_department_code = GetValidMatchingData(defaultAccountingInfo, item.program_code, "DEPARTMENT"),
                        fk_function_code = GetValidMatchingData(defaultAccountingInfo, item.program_code, "FUNCTION"),
                        fk_project_code = GetValidMatchingData(defaultAccountingInfo, item.program_code, "PROJECT"),
                        free_dim_1 = GetValidMatchingData(defaultAccountingInfo, item.program_code, "FREE_DIM_1"),
                        free_dim_2 = GetValidMatchingData(defaultAccountingInfo, item.program_code, "FREE_DIM_2"),
                        free_dim_3 = GetValidMatchingData(defaultAccountingInfo, item.program_code, "FREE_DIM_3"),
                        free_dim_4 = GetValidMatchingData(defaultAccountingInfo, item.program_code, "FREE_DIM_4"),
                        fk_alter_code = GetValidMatchingData(defaultAccountingInfo, item.program_code, "ALTER_CODE"),
                        fk_proj_trans_id = null,
                        forecast_period = period,
                        year = item.year,
                        amount = item.amount * -1,
                        fk_tenant_id = userDetails.tenant_id,
                        description = string.Empty,
                        vat_rate = 0,
                        vat_refund = 0,
                        is_vat_row = false,
                        updated = DateTime.UtcNow,
                        updated_by = userDetails.pk_id
                    };
                    _unitOfWork.GenericRepo.Add(loanTransaction);
                }
            }
        }

        private static string GetValidMatchingData(IEnumerable<tmd_acc_defaults> defaultAccountingInfo, string program_code, string acc_type)
        {
            return defaultAccountingInfo.FirstOrDefault(x => x.acc_type == acc_type && x.link_value == program_code) != null ? defaultAccountingInfo.FirstOrDefault(x => x.acc_type == acc_type && x.link_value == program_code).acc_value : string.Empty;
        }

        private void InsertAdminInvHeaderRow(string userId, Workbook wb, dynamic columns)
        {
            Style textStyle = wb.CreateStyle();
            textStyle.Number = 49;

            Style intStyle = wb.CreateStyle();
            intStyle.Number = 1;

            StyleFlag styleFlag = new StyleFlag { NumberFormat = true };
            Worksheet sheet = wb.Worksheets[0];
            int i = 0;
            int j = 0;
            List<string> numericColumns = new List<string> { "Year1Amt", "Year2Amt", "Year3Amt", "Year4Amt", "Year5Amt", "VatRate", "VatRefund" };
            foreach (var col in columns)
            {
                string colName = col["field"].ToString();
                if (numericColumns.Contains(colName))
                {
                    sheet.Cells[i, j].Value = col["title"].ToString();
                    sheet.Cells[i, j].SetStyle(intStyle, styleFlag);
                }
                else
                {
                    sheet.Cells[i, j].Value = col["title"].ToString();
                    sheet.Cells.Columns[j].ApplyStyle(textStyle, styleFlag);
                }
                j++;
            }
        }

        private void InsertAdminInvChildRows(AdminInvGridExcelInputHelper adminInvData, Worksheet sheet)
        {
            if (adminInvData == null)
            {
                return;
            }
            int i = 1;
            var numericStyle = sheet.Cells[0, 0].GetStyle();
            numericStyle.Custom = "0";
            foreach (var rowData in adminInvData.dataSource)
            {
                InsertActiveRows(sheet, adminInvData.columns, rowData, i, numericStyle);
                i++;
            }
        }

        private void InsertActiveRows(Worksheet sheet, JArray colDef, dynamic rowData, int rowNum, Style numericStyle)
        {

            int colNum = 0;
            foreach (var col in colDef)
            {
                string colField = col["field"].ToString();
                switch (colField)
                {
                    case "LineItemName":
                        sheet.Cells[rowNum, colNum].Value = rowData.LineItemName.ToString();
                        colNum++;
                        break;

                    case "MainProjCode":
                        sheet.Cells[rowNum, colNum].Value = rowData.MainProjCode.ToString();
                        colNum++;
                        break;

                    case "AccCode":
                        sheet.Cells[rowNum, colNum].Value = rowData.AccCode.ToString();
                        colNum++;
                        break;

                    case "DeptCode":
                        sheet.Cells[rowNum, colNum].Value = rowData.DeptCode.ToString();
                        colNum++;
                        break;

                    case "FuncCode":
                        sheet.Cells[rowNum, colNum].Value = rowData.FuncCode.ToString();
                        colNum++;
                        break;

                    case "ProjCode":
                        sheet.Cells[rowNum, colNum].Value = rowData.ProjCode.ToString();
                        colNum++;
                        break;

                    case "progCode":
                        sheet.Cells[rowNum, colNum].Value = rowData.progCode.ToString();
                        colNum++;
                        break;

                    case "ProjStatus":
                        sheet.Cells[rowNum, colNum].Value = rowData.ProjStatusDes.ToString();
                        colNum++;
                        break;

                    case "ProjDesc":
                        sheet.Cells[rowNum, colNum].Value = WebUtility.HtmlDecode(rowData.ProjDesc.ToString());
                        colNum++;
                        break;

                    case "invStatusName":
                        sheet.Cells[rowNum, colNum].Value = rowData.invStatusName.ToString();
                        colNum++;
                        break;

                    case "Year1Amt":
                        sheet.Cells[rowNum, colNum].Value = rowData.Year1Amt;
                        sheet.Cells[rowNum, colNum].SetStyle(numericStyle);
                        colNum++;
                        break;

                    case "Year2Amt":
                        sheet.Cells[rowNum, colNum].Value = rowData.Year2Amt;
                        sheet.Cells[rowNum, colNum].SetStyle(numericStyle);
                        colNum++;
                        break;

                    case "Year3Amt":
                        sheet.Cells[rowNum, colNum].Value = rowData.Year3Amt;
                        sheet.Cells[rowNum, colNum].SetStyle(numericStyle);
                        colNum++;
                        break;

                    case "Year4Amt":
                        sheet.Cells[rowNum, colNum].Value = rowData.Year4Amt;
                        sheet.Cells[rowNum, colNum].SetStyle(numericStyle);
                        colNum++;
                        break;

                    case "Year5Amt":
                        sheet.Cells[rowNum, colNum].Value = rowData.Year5Amt;
                        sheet.Cells[rowNum, colNum].SetStyle(numericStyle);
                        colNum++;
                        break;

                    case "VatRate":
                        sheet.Cells[rowNum, colNum].Value = rowData.VatRate;
                        sheet.Cells[rowNum, colNum].SetStyle(numericStyle);
                        colNum++;
                        break;

                    case "VatRefund":
                        sheet.Cells[rowNum, colNum].Value = rowData.VatRefund;
                        sheet.Cells[rowNum, colNum].SetStyle(numericStyle);
                        colNum++;
                        break;

                    case "AlterCode":
                        sheet.Cells[rowNum, colNum].Value = rowData.AlterCode.ToString();
                        colNum++;
                        break;

                    case "Desc":
                        sheet.Cells[rowNum, colNum].Value = rowData.Desc.ToString();
                        colNum++;
                        break;

                    case "MainProjStatus":
                        sheet.Cells[rowNum, colNum].Value = rowData.MainProjStatusDes.ToString();
                        colNum++;
                        break;

                    case "MainProjDesc":
                        sheet.Cells[rowNum, colNum].Value = FormatMainProjectDescription(rowData.MainProjDesc.ToString());
                        colNum++;
                        break;


                    default:
                        colNum++;
                        break;
                }
            }
        }

        private object FormatMainProjectDescription(string mainProjectDesc)
        {
            if (!String.IsNullOrEmpty(mainProjectDesc.Trim()))
            {
                return HttpUtility.HtmlDecode(Regex.Replace((mainProjectDesc.ToString()), "<(.|\n)*?>", ""));
            }
            else
            {
                return mainProjectDesc;
            }
        }
        #endregion
    }
}