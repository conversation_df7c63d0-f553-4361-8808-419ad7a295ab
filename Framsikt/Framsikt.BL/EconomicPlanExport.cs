#pragma warning disable CS8629

#pragma warning disable CS8600
#pragma warning disable CS8601
#pragma warning disable CS8602
#pragma warning disable CS8603

using Framsikt.BL.Core;
using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System.Data;
using System.Globalization;

#pragma warning disable CS8618

namespace Framsikt.BL
{
    public class EconomicPlanExport : IEconomicPlanExport
    {
        private readonly IUtility _utility;
        private readonly IFinUtility _finUtility;
        private readonly IDataSyncUtility _dataSyncUtility;
        private readonly IAppDataCache _appDataCache;

        public EconomicPlanExport(IUtility util, IFinUtility finUtility, IDataSyncUtility dataSyncUtility, IAppDataCache appDataCache)
        {
            _utility = util;
            _finUtility = finUtility;
            _dataSyncUtility = dataSyncUtility;
            _appDataCache = appDataCache;
        }

        public IEnumerable<InvestmentTotalProjectFinNetYearsOverview> GetInvestmentTotalProjectFinNetYears(int tenantId, string orgId, string serId, int budgetYear, ReportType reportType, List<int> budgetPhaseChangeId, string userId, bool divideByMillions = false, string? chapterIds = null, bool isSyncExport = false)
        {
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            UserData userDetails = _utility.GetUserDetails(userId);
            decimal oneMillionUnitsDivide = 1000000;
            decimal thousandUnitsDivide = 1000;
            decimal divisor = divideByMillions ? oneMillionUnitsDivide : thousandUnitsDivide;

            List<string> relationDepartments = new List<string>();
            bool isChapterSetup = false;

            if (!string.IsNullOrEmpty(chapterIds))
            {
                isChapterSetup = _finUtility.isChapterSetup(userId).GetAwaiter().GetResult();
                if (isChapterSetup)
                {
                    relationDepartments = _finUtility.GetAttributeConnectedDepartmentsForSelectedOrgAsync(userId, budgetYear, orgId, 1, chapterIds).GetAwaiter().GetResult().ToList();
                }
            }

            var baseQuery = tenantDbContext.vw_doc_investment_overview.AsNoTracking()
                .Where(a => a.fk_tenant_id == tenantId && a.budget_year == budgetYear && budgetPhaseChangeId.Contains(a.fk_change_id));

            if (!string.IsNullOrEmpty(chapterIds) && isChapterSetup && relationDepartments.Any())
            {
                baseQuery = baseQuery.Where(a => relationDepartments.Contains(a.fk_department_code));
            }

            if (ReportType.Level1 == reportType)
            {
                if (!isSyncExport)
                {
                    baseQuery = baseQuery.Where(a => a.fp_level_1_value == orgId);
                }
            }
            else if (ReportType.Level2 == reportType)
            {
                baseQuery = baseQuery.Where(a => a.fp_level_2_value == serId);
            }
            else if (ReportType.MultiLevel == reportType)
            {
                baseQuery = baseQuery.Where(a => a.fp_level_1_value == orgId && a.fp_level_2_value == serId);
            }
            var invstTotProFinNetYrData = baseQuery
                .GroupBy(x => new
                {
                    x.approval_ref_url,
                    x.investment_name,
                    x.finished_year,
                    x.display_proj_columns,
                    x.sum_level,
                    x.sum_code,
                    x.pk_investment_id,
                    x.inv_status,
                    x.sub_level_sa_code,
                    x.sub_level_sa_description,
                    x.approval_reference,
                    x.inv_phase,
                    x.oe_flag,
                    x.dynamic_gr_1,
                    x.dynamic_gr_2,
                 
                })
                .Select(y => new InvestmentTotalProjectFinNetYearsOverview
                {
                    captionName = y.Key.investment_name,
                    totalProjectCost = y.Key.display_proj_columns == 1 ? (y.Sum(z => z.gross_cost.Value) / divisor) : 0,
                    totalFinancingCost = y.Key.display_proj_columns == 1 ? (y.Sum(z => z.financed_amount.Value) / divisor) : 0,
                    totalNetCost = y.Key.display_proj_columns == 1 ? (y.Sum(z => z.net_cost.Value) / divisor) : 0,
                    year1Amount = y.Sum(z => z.year_1_amount) / divisor,
                    year2Amount = y.Sum(z => z.year_2_amount) / divisor,
                    year3Amount = y.Sum(z => z.year_3_amount) / divisor,
                    year4Amount = y.Sum(z => z.year_4_amount) / divisor,
                    year5Amount = y.Sum(z => z.year_5_amount) / divisor,
                    year6Amount = y.Sum(z => z.year_6_amount) / divisor,
                    year7Amount = y.Sum(z => z.year_7_amount) / divisor,
                    year8Amount = y.Sum(z => z.year_8_amount) / divisor,
                    year9Amount = y.Sum(z => z.year_9_amount) / divisor,
                    year10Amount = y.Sum(z => z.year_10_amount) / divisor,
                    finyear1Amount = y.Sum(z => z.fin_year_1_amount) / divisor,
                    finyear2Amount = y.Sum(z => z.fin_year_2_amount) / divisor,
                    finyear3Amount = y.Sum(z => z.fin_year_3_amount) / divisor,
                    finyear4Amount = y.Sum(z => z.fin_year_4_amount) / divisor,
                    fintotal4YearsSum = y.Key.display_proj_columns == 1 ? (y.Sum(z => z.fin_total_amount.Value) / divisor) : 0,
                    total4YearsSum = y.Key.display_proj_columns == 1 ? (y.Sum(z => z.sum_finplan.Value) / divisor) : 0,
                    plannedFinishYear = y.Key.finished_year.Value,
                    displayprojcolumns = y.Key.display_proj_columns,
                    sumLevel = y.Key.sum_level,
                    sumCode = y.Key.sum_code,
                    investmentId = y.Key.pk_investment_id,
                    InvAppStatus = y.Key.inv_status,
                    subLevelSACode = y.Key.sub_level_sa_code,
                    subLevelSADescription = y.Key.sub_level_sa_description,
                    approvalcost = y.Sum(z => z.approved_cost) / divisor,
                    approvalreferenence = y.Key.approval_reference,
                    previouslyBudgeted = y.Sum(z => z.previously_budgeted) / divisor,
                    approvalreferenence_url = y.Key.approval_ref_url,
                    investmentPhase = y.Key.inv_phase,
                    operExps = y.Key.oe_flag,
                    dynamicGroup1 = y.Key.dynamic_gr_1,
                    dynamicGroup2 = y.Key.dynamic_gr_2,
                    costEstimateP50 = y.Sum(z => z.cost_estimate_p50) / divisor
                }).OrderBy(z => z.captionName);

            var data = invstTotProFinNetYrData.ToList();
            CultureInfo culture = CultureInfoFactory.CreateCulture(userDetails.language_preference);
            return data.OrderBy(x => x.captionName, StringComparer.Create(culture, false)).ToList();
        }

        public IEnumerable<InvestmentTotalProjectFinNetYearsOverview> GetInvestmentTotalProjectFinNetYearsRequiredInv(int tenantId, string orgId, string serId, int budgetYear, ReportType reportType, List<int> budgetPhaseChangeId, string userId, bool divideByMillions = false, string? chapterIds = null, bool isSyncExport = false)
        {
            UserData userDetails = _utility.GetUserDetails(userId);
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            IEnumerable<InvestmentTotalProjectFinNetYearsOverview> invstTotProFinNetYrData;
            decimal oneMillionUnitsDivide = 1000000;
            var relationDepartments = new List<string>();
            var syncStatus = tenantDbContext.tco_progress_status.FirstOrDefault(x => x.fk_tenant_id == tenantId && x.type == "SYNC_INVESTMENT_STATUS" && !x.is_local)?.status_id ?? 5;
            if (!string.IsNullOrEmpty(chapterIds))
            {
                //filter data with chapterId
                bool isChapterSetup = _finUtility.isChapterSetup(userId).GetAwaiter().GetResult();
                if (isChapterSetup)
                {
                    relationDepartments.AddRange(_finUtility.GetAttributeConnectedDepartmentsForSelectedOrgAsync(userId, budgetYear, orgId, 1, chapterIds).GetAwaiter().GetResult());
                }
            }
            if (ReportType.Level1 == reportType)
            {
                var dataForInvstTotProFinNetYrData1 = (!string.IsNullOrEmpty(chapterIds) && relationDepartments.Any()) ? (from a in tenantDbContext.vw_doc_investments_blist
                                                                                                                          where a.fk_tenant_id == tenantId
                                                                                                                             && a.budget_year == budgetYear
                                                                                                                             //&& a.fp_level_1_value == orgId
                                                                                                                             && relationDepartments.Contains(a.fk_department_code)
                                                                                                                             && budgetPhaseChangeId.Contains(a.fk_change_id)
                                                                                                                          //orderby new
                                                                                                                          //{
                                                                                                                          //    a.gross_cost
                                                                                                                          //}
                                                                                                                          select new
                                                                                                                          {
                                                                                                                              a.fp_level_1_value,
                                                                                                                              a.investment_name,
                                                                                                                              a.gross_cost,
                                                                                                                              a.financed_amount,
                                                                                                                              a.net_cost,
                                                                                                                              a.year_1_amount,
                                                                                                                              a.year_2_amount,
                                                                                                                              a.year_3_amount,
                                                                                                                              a.year_4_amount,
                                                                                                                              a.year_5_amount,
                                                                                                                              a.year_6_amount,
                                                                                                                              a.year_7_amount,
                                                                                                                              a.year_8_amount,
                                                                                                                              a.year_9_amount,
                                                                                                                              a.year_10_amount,
                                                                                                                              a.fin_year_1_amount,
                                                                                                                              a.fin_year_2_amount,
                                                                                                                              a.fin_year_3_amount,
                                                                                                                              a.fin_year_4_amount,
                                                                                                                              a.fin_total_amount,
                                                                                                                              a.sum_finplan,
                                                                                                                              a.finished_year,
                                                                                                                              a.display_proj_columns,
                                                                                                                              a.sum_level,
                                                                                                                              a.sum_code,
                                                                                                                              a.pk_investment_id,
                                                                                                                              a.inv_status,
                                                                                                                              a.sub_level_sa_code,
                                                                                                                              a.sub_level_sa_description,
                                                                                                                              a.approved_cost,
                                                                                                                              a.approval_reference,
                                                                                                                              a.previously_budgeted,
                                                                                                                              a.approval_ref_url,
                                                                                                                              a.inv_phase,
                                                                                                                              a.oe_flag,
                                                                                                                              a.dynamic_gr_1,
                                                                                                                              a.dynamic_gr_2,
                                                                                                                              a.cost_estimate_p50,
                                                                                                                              a.sync_status
                                                                                                                          }) :
                                                        (from a in tenantDbContext.vw_doc_investments_blist
                                                         where a.fk_tenant_id == tenantId
                                                            && a.budget_year == budgetYear
                                                            //  && a.fp_level_1_value == orgId
                                                            && budgetPhaseChangeId.Contains(a.fk_change_id)
                                                         //orderby new
                                                         //{
                                                         //    a.gross_cost
                                                         //}
                                                         select new
                                                         {
                                                             a.fp_level_1_value,
                                                             a.investment_name,
                                                             a.gross_cost,
                                                             a.financed_amount,
                                                             a.net_cost,
                                                             a.year_1_amount,
                                                             a.year_2_amount,
                                                             a.year_3_amount,
                                                             a.year_4_amount,
                                                             a.year_5_amount,
                                                             a.year_6_amount,
                                                             a.year_7_amount,
                                                             a.year_8_amount,
                                                             a.year_9_amount,
                                                             a.year_10_amount,
                                                             a.fin_year_1_amount,
                                                             a.fin_year_2_amount,
                                                             a.fin_year_3_amount,
                                                             a.fin_year_4_amount,
                                                             a.fin_total_amount,
                                                             a.sum_finplan,
                                                             a.finished_year,
                                                             a.display_proj_columns,
                                                             a.sum_level,
                                                             a.sum_code,
                                                             a.pk_investment_id,
                                                             a.inv_status,
                                                             a.sub_level_sa_code,
                                                             a.sub_level_sa_description,
                                                             a.approved_cost,
                                                             a.approval_reference,
                                                             a.previously_budgeted,
                                                             a.approval_ref_url,
                                                             a.inv_phase,
                                                             a.oe_flag,
                                                             a.dynamic_gr_1,
                                                             a.dynamic_gr_2,
                                                             a.cost_estimate_p50,
                                                             a.sync_status
                                                         });
                dataForInvstTotProFinNetYrData1 = isSyncExport ? dataForInvstTotProFinNetYrData1.Where(x => x.sync_status == syncStatus) : dataForInvstTotProFinNetYrData1;
                var dataForInvstTotProFinNetYrData = isSyncExport ? dataForInvstTotProFinNetYrData1.ToList() : dataForInvstTotProFinNetYrData1.Where(z => z.sub_level_sa_code == orgId).ToList();

                dataForInvstTotProFinNetYrData = dataForInvstTotProFinNetYrData.OrderBy(x => x.gross_cost).ToList();

                invstTotProFinNetYrData = dataForInvstTotProFinNetYrData.GroupBy(x => new { x.approval_ref_url, x.investment_name, x.finished_year, x.display_proj_columns, x.sum_level, x.sum_code, x.pk_investment_id, x.inv_status, x.sub_level_sa_code, x.sub_level_sa_description, x.approval_reference, x.inv_phase, x.oe_flag, x.dynamic_gr_1, x.dynamic_gr_2 })
                                                        .Select(y => new InvestmentTotalProjectFinNetYearsOverview
                                                        {
                                                            captionName = y.Key.investment_name,
                                                            totalProjectCost = y.Key.display_proj_columns == 1 ? (divideByMillions == true ? y.Sum(z => z.gross_cost.Value) / oneMillionUnitsDivide : y.Sum(z => z.gross_cost.Value) / 1000) : 0,
                                                            totalFinancingCost = y.Key.display_proj_columns == 1 ? (divideByMillions == true ? y.Sum(z => z.financed_amount.Value) / oneMillionUnitsDivide : y.Sum(z => z.financed_amount.Value) / 1000) : 0,
                                                            totalNetCost = y.Key.display_proj_columns == 1 ? (divideByMillions == true ? y.Sum(z => z.net_cost.Value) / oneMillionUnitsDivide : y.Sum(z => z.net_cost.Value) / 1000) : 0,
                                                            year1Amount = divideByMillions == true ? y.Sum(z => z.year_1_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_1_amount) / 1000,
                                                            year2Amount = divideByMillions == true ? y.Sum(z => z.year_2_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_2_amount) / 1000,
                                                            year3Amount = divideByMillions == true ? y.Sum(z => z.year_3_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_3_amount) / 1000,
                                                            year4Amount = divideByMillions == true ? y.Sum(z => z.year_4_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_4_amount) / 1000,
                                                            year5Amount = divideByMillions == true ? y.Sum(z => z.year_5_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_5_amount) / 1000,
                                                            year6Amount = divideByMillions == true ? y.Sum(z => z.year_6_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_6_amount) / 1000,
                                                            year7Amount = divideByMillions == true ? y.Sum(z => z.year_7_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_7_amount) / 1000,
                                                            year8Amount = divideByMillions == true ? y.Sum(z => z.year_8_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_8_amount) / 1000,
                                                            year9Amount = divideByMillions == true ? y.Sum(z => z.year_9_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_9_amount) / 1000,
                                                            year10Amount = divideByMillions == true ? y.Sum(z => z.year_10_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_10_amount) / 1000,
                                                            finyear1Amount = divideByMillions == true ? y.Sum(z => z.fin_year_1_amount) / oneMillionUnitsDivide : y.Sum(z => z.fin_year_1_amount) / 1000,
                                                            finyear2Amount = divideByMillions == true ? y.Sum(z => z.fin_year_2_amount) / oneMillionUnitsDivide : y.Sum(z => z.fin_year_2_amount) / 1000,
                                                            finyear3Amount = divideByMillions == true ? y.Sum(z => z.fin_year_3_amount) / oneMillionUnitsDivide : y.Sum(z => z.fin_year_3_amount) / 1000,
                                                            finyear4Amount = divideByMillions == true ? y.Sum(z => z.fin_year_4_amount) / oneMillionUnitsDivide : y.Sum(z => z.fin_year_4_amount) / 1000,
                                                            fintotal4YearsSum = divideByMillions == true ? y.Sum(z => z.fin_total_amount.Value) / oneMillionUnitsDivide : y.Sum(z => z.fin_total_amount.Value) / 1000,
                                                            total4YearsSum = divideByMillions == true ? y.Sum(z => z.sum_finplan.Value) / oneMillionUnitsDivide : y.Sum(z => z.sum_finplan.Value) / 1000,
                                                            plannedFinishYear = y.Key.finished_year.Value,
                                                            displayprojcolumns = y.Key.display_proj_columns,
                                                            sumLevel = y.Key.sum_level,
                                                            sumCode = y.Key.sum_code,
                                                            investmentId = y.Key.pk_investment_id,
                                                            InvAppStatus = y.Key.inv_status,
                                                            subLevelSACode = y.Key.sub_level_sa_code,
                                                            subLevelSADescription = y.Key.sub_level_sa_description,
                                                            approvalcost = divideByMillions == true ? y.Sum(z => z.approved_cost) / oneMillionUnitsDivide : y.Sum(z => z.approved_cost) / 1000,
                                                            approvalreferenence = y.Key.approval_reference,
                                                            previouslyBudgeted = divideByMillions == true ? y.Sum(z => z.previously_budgeted) / oneMillionUnitsDivide : y.Sum(z => z.previously_budgeted) / 1000,
                                                            approvalreferenence_url = y.Key.approval_ref_url,
                                                            investmentPhase = y.Key.inv_phase,
                                                            operExps = y.Key.oe_flag,
                                                            dynamicGroup1 = y.Key.dynamic_gr_1,
                                                            dynamicGroup2 = y.Key.dynamic_gr_2,
                                                            costEstimateP50 = divideByMillions == true ? y.Sum(z => z.cost_estimate_p50) / oneMillionUnitsDivide : y.Sum(z => z.cost_estimate_p50) / 1000,
                                                        }).OrderBy(x => x.captionName)
                                                        .ToList();
            }
            else if (ReportType.Level2 == reportType)
            {
                var dataForInvstTotProFinNetYrData = (!string.IsNullOrEmpty(chapterIds) && relationDepartments.Any()) ?
                     (from a in tenantDbContext.vw_doc_investments_blist
                      where a.fk_tenant_id == tenantId
                          && a.budget_year == budgetYear
                          && a.fp_level_2_value.Contains(serId)
                          && relationDepartments.Contains(a.fk_department_code)
                          && budgetPhaseChangeId.Contains(a.fk_change_id)
                      //orderby new
                      //{
                      //    a.gross_cost
                      //}
                      select new
                      {
                          a.investment_name,
                          a.gross_cost,
                          a.financed_amount,
                          a.net_cost,
                          a.year_1_amount,
                          a.year_2_amount,
                          a.year_3_amount,
                          a.year_4_amount,
                          a.year_5_amount,
                          a.year_6_amount,
                          a.year_7_amount,
                          a.year_8_amount,
                          a.year_9_amount,
                          a.year_10_amount,
                          a.fin_year_1_amount,
                          a.fin_year_2_amount,
                          a.fin_year_3_amount,
                          a.fin_year_4_amount,
                          a.fin_total_amount,
                          a.sum_finplan,
                          a.finished_year,
                          a.display_proj_columns,
                          a.sum_level,
                          a.sum_code,
                          a.pk_investment_id,
                          a.inv_status,
                          a.sub_level_sa_code,
                          a.sub_level_sa_description,
                          a.approved_cost,
                          a.approval_reference,
                          a.previously_budgeted,
                          a.approval_ref_url,
                          a.inv_phase,
                          a.oe_flag,
                          a.dynamic_gr_1,
                          a.dynamic_gr_2,
                          a.cost_estimate_p50
                      }).ToList() : (from a in tenantDbContext.vw_doc_investments_blist
                                     where a.fk_tenant_id == tenantId
                                         && a.budget_year == budgetYear
                                         && a.fp_level_2_value.Contains(serId)
                                         && budgetPhaseChangeId.Contains(a.fk_change_id)
                                     //orderby new
                                     //{
                                     //    a.gross_cost
                                     //}
                                     select new
                                     {
                                         a.investment_name,
                                         a.gross_cost,
                                         a.financed_amount,
                                         a.net_cost,
                                         a.year_1_amount,
                                         a.year_2_amount,
                                         a.year_3_amount,
                                         a.year_4_amount,
                                         a.year_5_amount,
                                         a.year_6_amount,
                                         a.year_7_amount,
                                         a.year_8_amount,
                                         a.year_9_amount,
                                         a.year_10_amount,
                                         a.fin_year_1_amount,
                                         a.fin_year_2_amount,
                                         a.fin_year_3_amount,
                                         a.fin_year_4_amount,
                                         a.fin_total_amount,
                                         a.sum_finplan,
                                         a.finished_year,
                                         a.display_proj_columns,
                                         a.sum_level,
                                         a.sum_code,
                                         a.pk_investment_id,
                                         a.inv_status,
                                         a.sub_level_sa_code,
                                         a.sub_level_sa_description,
                                         a.approved_cost,
                                         a.approval_reference,
                                         a.previously_budgeted,
                                         a.approval_ref_url,
                                         a.inv_phase,
                                         a.oe_flag,
                                         a.dynamic_gr_1,
                                         a.dynamic_gr_2,
                                         a.cost_estimate_p50
                                     }).ToList();
                dataForInvstTotProFinNetYrData = dataForInvstTotProFinNetYrData.OrderBy(x => x.gross_cost).ToList();

                invstTotProFinNetYrData = dataForInvstTotProFinNetYrData.GroupBy(x => new { x.approval_ref_url, x.investment_name, x.finished_year, x.display_proj_columns, x.sum_level, x.sum_code, x.pk_investment_id, x.inv_status, x.sub_level_sa_code, x.sub_level_sa_description, x.approval_reference, x.inv_phase, x.oe_flag, x.dynamic_gr_1, x.dynamic_gr_2 })
                                                .Select(y => new InvestmentTotalProjectFinNetYearsOverview
                                                {
                                                    captionName = y.Key.investment_name,
                                                    totalProjectCost = y.Key.display_proj_columns == 1 ? (divideByMillions == true ? y.Sum(z => z.gross_cost.Value) / oneMillionUnitsDivide : y.Sum(z => z.gross_cost.Value) / 1000) : 0,
                                                    totalFinancingCost = y.Key.display_proj_columns == 1 ? (divideByMillions == true ? y.Sum(z => z.financed_amount.Value) / oneMillionUnitsDivide : y.Sum(z => z.financed_amount.Value) / 1000) : 0,
                                                    totalNetCost = y.Key.display_proj_columns == 1 ? (divideByMillions == true ? y.Sum(z => z.net_cost.Value) / oneMillionUnitsDivide : y.Sum(z => z.net_cost.Value) / 1000) : 0,
                                                    year1Amount = divideByMillions == true ? y.Sum(z => z.year_1_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_1_amount) / 1000,
                                                    year2Amount = divideByMillions == true ? y.Sum(z => z.year_2_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_2_amount) / 1000,
                                                    year3Amount = divideByMillions == true ? y.Sum(z => z.year_3_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_3_amount) / 1000,
                                                    year4Amount = divideByMillions == true ? y.Sum(z => z.year_4_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_4_amount) / 1000,
                                                    year5Amount = divideByMillions == true ? y.Sum(z => z.year_5_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_5_amount) / 1000,
                                                    year6Amount = divideByMillions == true ? y.Sum(z => z.year_6_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_6_amount) / 1000,
                                                    year7Amount = divideByMillions == true ? y.Sum(z => z.year_7_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_7_amount) / 1000,
                                                    year8Amount = divideByMillions == true ? y.Sum(z => z.year_8_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_8_amount) / 1000,
                                                    year9Amount = divideByMillions == true ? y.Sum(z => z.year_9_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_9_amount) / 1000,
                                                    year10Amount = divideByMillions == true ? y.Sum(z => z.year_10_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_10_amount) / 1000,
                                                    finyear1Amount = divideByMillions == true ? y.Sum(z => z.fin_year_1_amount) / oneMillionUnitsDivide : y.Sum(z => z.fin_year_1_amount) / 1000,
                                                    finyear2Amount = divideByMillions == true ? y.Sum(z => z.fin_year_2_amount) / oneMillionUnitsDivide : y.Sum(z => z.fin_year_2_amount) / 1000,
                                                    finyear3Amount = divideByMillions == true ? y.Sum(z => z.fin_year_3_amount) / oneMillionUnitsDivide : y.Sum(z => z.fin_year_3_amount) / 1000,
                                                    finyear4Amount = divideByMillions == true ? y.Sum(z => z.fin_year_4_amount) / oneMillionUnitsDivide : y.Sum(z => z.fin_year_4_amount) / 1000,
                                                    fintotal4YearsSum = divideByMillions == true ? y.Sum(z => z.fin_total_amount.Value) / oneMillionUnitsDivide : y.Sum(z => z.fin_total_amount.Value) / 1000,
                                                    total4YearsSum = divideByMillions == true ? y.Sum(z => z.sum_finplan.Value) / oneMillionUnitsDivide : y.Sum(z => z.sum_finplan.Value) / 1000,
                                                    plannedFinishYear = y.Key.finished_year.Value,
                                                    displayprojcolumns = y.Key.display_proj_columns,
                                                    sumLevel = y.Key.sum_level,
                                                    sumCode = y.Key.sum_code,
                                                    investmentId = y.Key.pk_investment_id,
                                                    InvAppStatus = y.Key.inv_status,
                                                    subLevelSACode = y.Key.sub_level_sa_code,
                                                    subLevelSADescription = y.Key.sub_level_sa_description,
                                                    approvalcost = divideByMillions == true ? y.Sum(z => z.approved_cost) / oneMillionUnitsDivide : y.Sum(z => z.approved_cost) / 1000,
                                                    approvalreferenence = y.Key.approval_reference,
                                                    previouslyBudgeted = divideByMillions == true ? y.Sum(z => z.previously_budgeted) / oneMillionUnitsDivide : y.Sum(z => z.previously_budgeted) / 1000,
                                                    approvalreferenence_url = y.Key.approval_ref_url,
                                                    investmentPhase = y.Key.inv_phase,
                                                    operExps = y.Key.oe_flag,
                                                    dynamicGroup1 = y.Key.dynamic_gr_1,
                                                    dynamicGroup2 = y.Key.dynamic_gr_2,
                                                    costEstimateP50 = divideByMillions == true ? y.Sum(z => z.cost_estimate_p50) / oneMillionUnitsDivide : y.Sum(z => z.cost_estimate_p50) / 1000
                                                }).OrderBy(x => x.captionName)
                                                .ToList();
            }
            else if (ReportType.MultiLevel == reportType)
            {
                var dataForInvstTotProFinNetYrData = (!string.IsNullOrEmpty(chapterIds) && relationDepartments.Any()) ? (from a in tenantDbContext.vw_doc_investments_blist
                                                                                                                         where a.fk_tenant_id == tenantId
                                                                                                                            && a.budget_year == budgetYear
                                                                                                                            && a.fp_level_1_value.Contains(orgId)
                                                                                                                            && a.fp_level_2_value.Contains(serId)
                                                                                                                            && relationDepartments.Contains(a.fk_department_code)
                                                                                                                            && budgetPhaseChangeId.Contains(a.fk_change_id)
                                                                                                                         //orderby new
                                                                                                                         //{
                                                                                                                         //    a.gross_cost
                                                                                                                         //}
                                                                                                                         select new
                                                                                                                         {
                                                                                                                             a.investment_name,
                                                                                                                             a.gross_cost,
                                                                                                                             a.financed_amount,
                                                                                                                             a.net_cost,
                                                                                                                             a.year_1_amount,
                                                                                                                             a.year_2_amount,
                                                                                                                             a.year_3_amount,
                                                                                                                             a.year_4_amount,
                                                                                                                             a.year_5_amount,
                                                                                                                             a.year_6_amount,
                                                                                                                             a.year_7_amount,
                                                                                                                             a.year_8_amount,
                                                                                                                             a.year_9_amount,
                                                                                                                             a.year_10_amount,
                                                                                                                             a.fin_year_1_amount,
                                                                                                                             a.fin_year_2_amount,
                                                                                                                             a.fin_year_3_amount,
                                                                                                                             a.fin_year_4_amount,
                                                                                                                             a.fin_total_amount,
                                                                                                                             a.sum_finplan,
                                                                                                                             a.finished_year,
                                                                                                                             a.display_proj_columns,
                                                                                                                             a.sum_level,
                                                                                                                             a.sum_code,
                                                                                                                             a.pk_investment_id,
                                                                                                                             a.inv_status,
                                                                                                                             a.sub_level_sa_code,
                                                                                                                             a.sub_level_sa_description,
                                                                                                                             a.approved_cost,
                                                                                                                             a.approval_reference,
                                                                                                                             a.previously_budgeted,
                                                                                                                             a.approval_ref_url,
                                                                                                                             a.inv_phase,
                                                                                                                             a.oe_flag,
                                                                                                                             a.dynamic_gr_1,
                                                                                                                             a.dynamic_gr_2,
                                                                                                                             a.cost_estimate_p50
                                                                                                                         }).ToList() : (from a in tenantDbContext.vw_doc_investments_blist
                                                                                                                                        where a.fk_tenant_id == tenantId
                                                                                                                                           && a.budget_year == budgetYear
                                                                                                                                           && a.fp_level_1_value.Contains(orgId)
                                                                                                                                           && a.fp_level_2_value.Contains(serId)
                                                                                                                                           && budgetPhaseChangeId.Contains(a.fk_change_id)
                                                                                                                                        //orderby new
                                                                                                                                        //{
                                                                                                                                        //    a.gross_cost
                                                                                                                                        //}
                                                                                                                                        select new
                                                                                                                                        {
                                                                                                                                            a.investment_name,
                                                                                                                                            a.gross_cost,
                                                                                                                                            a.financed_amount,
                                                                                                                                            a.net_cost,
                                                                                                                                            a.year_1_amount,
                                                                                                                                            a.year_2_amount,
                                                                                                                                            a.year_3_amount,
                                                                                                                                            a.year_4_amount,
                                                                                                                                            a.year_5_amount,
                                                                                                                                            a.year_6_amount,
                                                                                                                                            a.year_7_amount,
                                                                                                                                            a.year_8_amount,
                                                                                                                                            a.year_9_amount,
                                                                                                                                            a.year_10_amount,
                                                                                                                                            a.fin_year_1_amount,
                                                                                                                                            a.fin_year_2_amount,
                                                                                                                                            a.fin_year_3_amount,
                                                                                                                                            a.fin_year_4_amount,
                                                                                                                                            a.fin_total_amount,
                                                                                                                                            a.sum_finplan,
                                                                                                                                            a.finished_year,
                                                                                                                                            a.display_proj_columns,
                                                                                                                                            a.sum_level,
                                                                                                                                            a.sum_code,
                                                                                                                                            a.pk_investment_id,
                                                                                                                                            a.inv_status,
                                                                                                                                            a.sub_level_sa_code,
                                                                                                                                            a.sub_level_sa_description,
                                                                                                                                            a.approved_cost,
                                                                                                                                            a.approval_reference,
                                                                                                                                            a.previously_budgeted,
                                                                                                                                            a.approval_ref_url,
                                                                                                                                            a.inv_phase,
                                                                                                                                            a.oe_flag,
                                                                                                                                            a.dynamic_gr_1,
                                                                                                                                            a.dynamic_gr_2,
                                                                                                                                            a.cost_estimate_p50
                                                                                                                                        }).ToList();

                dataForInvstTotProFinNetYrData = dataForInvstTotProFinNetYrData.OrderBy(x => x.gross_cost).ToList();

                invstTotProFinNetYrData = dataForInvstTotProFinNetYrData.GroupBy(x => new { x.approval_ref_url, x.investment_name, x.finished_year, x.display_proj_columns, x.sum_level, x.sum_code, x.pk_investment_id, x.inv_status, x.sub_level_sa_code, x.sub_level_sa_description, x.approval_reference, x.inv_phase, x.oe_flag, x.dynamic_gr_1, x.dynamic_gr_2 })
                                                        .Select(y => new InvestmentTotalProjectFinNetYearsOverview
                                                        {
                                                            captionName = y.Key.investment_name,
                                                            totalProjectCost = y.Key.display_proj_columns == 1 ? (divideByMillions == true ? y.Sum(z => z.gross_cost.Value) / oneMillionUnitsDivide : y.Sum(z => z.gross_cost.Value) / 1000) : 0,
                                                            totalFinancingCost = y.Key.display_proj_columns == 1 ? (divideByMillions == true ? y.Sum(z => z.financed_amount.Value) / oneMillionUnitsDivide : y.Sum(z => z.financed_amount.Value) / 1000) : 0,
                                                            totalNetCost = y.Key.display_proj_columns == 1 ? (divideByMillions == true ? y.Sum(z => z.net_cost.Value) / oneMillionUnitsDivide : y.Sum(z => z.net_cost.Value) / 1000) : 0,
                                                            year1Amount = divideByMillions == true ? y.Sum(z => z.year_1_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_1_amount) / 1000,
                                                            year2Amount = divideByMillions == true ? y.Sum(z => z.year_2_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_2_amount) / 1000,
                                                            year3Amount = divideByMillions == true ? y.Sum(z => z.year_3_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_3_amount) / 1000,
                                                            year4Amount = divideByMillions == true ? y.Sum(z => z.year_4_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_4_amount) / 1000,
                                                            year5Amount = divideByMillions == true ? y.Sum(z => z.year_5_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_5_amount) / 1000,
                                                            year6Amount = divideByMillions == true ? y.Sum(z => z.year_6_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_6_amount) / 1000,
                                                            year7Amount = divideByMillions == true ? y.Sum(z => z.year_7_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_7_amount) / 1000,
                                                            year8Amount = divideByMillions == true ? y.Sum(z => z.year_8_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_8_amount) / 1000,
                                                            year9Amount = divideByMillions == true ? y.Sum(z => z.year_9_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_9_amount) / 1000,
                                                            year10Amount = divideByMillions == true ? y.Sum(z => z.year_10_amount) / oneMillionUnitsDivide : y.Sum(z => z.year_10_amount) / 1000,
                                                            finyear1Amount = divideByMillions == true ? y.Sum(z => z.fin_year_1_amount) / oneMillionUnitsDivide : y.Sum(z => z.fin_year_1_amount) / 1000,
                                                            finyear2Amount = divideByMillions == true ? y.Sum(z => z.fin_year_2_amount) / oneMillionUnitsDivide : y.Sum(z => z.fin_year_2_amount) / 1000,
                                                            finyear3Amount = divideByMillions == true ? y.Sum(z => z.fin_year_3_amount) / oneMillionUnitsDivide : y.Sum(z => z.fin_year_3_amount) / 1000,
                                                            finyear4Amount = divideByMillions == true ? y.Sum(z => z.fin_year_4_amount) / oneMillionUnitsDivide : y.Sum(z => z.fin_year_4_amount) / 1000,
                                                            fintotal4YearsSum = divideByMillions == true ? y.Sum(z => z.fin_total_amount.Value) / oneMillionUnitsDivide : y.Sum(z => z.fin_total_amount.Value) / 1000,
                                                            total4YearsSum = divideByMillions == true ? y.Sum(z => z.sum_finplan.Value) / oneMillionUnitsDivide : y.Sum(z => z.sum_finplan.Value) / 1000,
                                                            plannedFinishYear = y.Key.finished_year.Value,
                                                            displayprojcolumns = y.Key.display_proj_columns,
                                                            sumLevel = y.Key.sum_level,
                                                            sumCode = y.Key.sum_code,
                                                            investmentId = y.Key.pk_investment_id,
                                                            InvAppStatus = y.Key.inv_status,
                                                            subLevelSACode = y.Key.sub_level_sa_code,
                                                            subLevelSADescription = y.Key.sub_level_sa_description,
                                                            approvalcost = divideByMillions == true ? y.Sum(z => z.approved_cost) / oneMillionUnitsDivide : y.Sum(z => z.approved_cost) / 1000,
                                                            approvalreferenence = y.Key.approval_reference,
                                                            previouslyBudgeted = divideByMillions == true ? y.Sum(z => z.previously_budgeted) / oneMillionUnitsDivide : y.Sum(z => z.previously_budgeted) / 1000,
                                                            approvalreferenence_url = y.Key.approval_ref_url,
                                                            investmentPhase = y.Key.inv_phase,
                                                            operExps = y.Key.oe_flag,
                                                            dynamicGroup1 = y.Key.dynamic_gr_1,
                                                            dynamicGroup2 = y.Key.dynamic_gr_2,
                                                            costEstimateP50 = divideByMillions == true ? y.Sum(z => z.cost_estimate_p50) / oneMillionUnitsDivide : y.Sum(z => z.cost_estimate_p50) / 1000
                                                        }).OrderBy(x => x.captionName)
                                                        .ToList();
            }
            else
            {
                invstTotProFinNetYrData = null;
                return invstTotProFinNetYrData;
            }
            var data = invstTotProFinNetYrData.ToList();
            CultureInfo culture = CultureInfoFactory.CreateCulture(userDetails.language_preference);
            return data.OrderBy(x => x.captionName, StringComparer.Create(culture, false)).ToList();
        }

        public List<BudgetByAccount> GetBudgetByAccountGroup(string userId, int tenantId, string orgId, string serId, int budgetYear, string budgetPhaseId,
        bool divideByMillions, bool isServiceSetUp, string chapterIds, Guid cacheKey)
        {
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            tenantDbContext.Database.SetCommandTimeout(900);
            int divisor = divideByMillions ? 1000000 : 1000;

            UserData userDetails = _utility.GetUserDetails(userId);
            List<BudgetByAccount> budgetQuery;
            string result = _appDataCache.GetStringForUser(userDetails.client_id, userDetails.tenant_id, userDetails.user_name, $"{cacheKey}-vw_doc_accgrp_report");
            List<AccountReport> data = result == null ? new List<AccountReport>() :
                            JsonConvert.DeserializeObject<List<AccountReport>>(result);
            if (_finUtility.isChapterSetup(userId).GetAwaiter().GetResult() && !string.IsNullOrEmpty(chapterIds))
            {
                data = data.Where(x => x.AttributeId == chapterIds).ToList();
            }
            else
            {
                if (!string.IsNullOrEmpty(orgId))
                {
                    data = data.Where(x => x.FpLevel1Value == orgId).ToList();

                    if (!string.IsNullOrEmpty(serId) && serId != "All")
                    {
                        string sId = serId.Trim().ToLower();
                        data = data.Where(x => x.FpLevel2Value == sId).ToList();
                    }
                }
                else if (!string.IsNullOrEmpty(serId) && serId != "All")
                {
                    string sId = serId.Trim();
                    data = data.Where(x => x.FpLevel2Value == sId).ToList();
                }
            }

            if (!string.IsNullOrEmpty(budgetPhaseId) && budgetPhaseId != "-1")
            {
                var changeIds = _utility.GetChangeDataUsingBudgetPhase(budgetPhaseId, tenantId, budgetYear);
                if (changeIds.Any())
                {
                    data = data.Where(x => changeIds.Contains(x.ChangeId)).ToList();
                }
            }

            if (isServiceSetUp)
            {
                budgetQuery = data.Select(x => new
                {
                    x.LineItem,
                    x.GlAmount,
                    x.BudgetAmount,
                    x.RevisedBudgetAmount,
                    x.Year1Amount,
                    x.Year2Amount,
                    x.Year3Amount,
                    x.Year4Amount,
                    x.LineGroupId,
                    x.LineGroup,
                    x.LineItemId,
                    x.OrgId,
                    x.OrgName
                }).Select(y => new BudgetByAccount
                {
                    Description = y.LineItem,
                    ActualAmount = y.GlAmount,
                    BudgetAmount = y.BudgetAmount,
                    RevisedBudget = y.RevisedBudgetAmount,
                    Year1Amount = y.Year1Amount,
                    Year2Amount = y.Year2Amount,
                    Year3Amount = y.Year3Amount,
                    Year4Amount = y.Year4Amount,
                    lineGroupId = y.LineGroupId,
                    lineGroupDesc = y.LineGroup,
                    lineItemId = y.LineItemId,
                    lineItemDesc = y.LineItem,
                    orgId = y.OrgId,
                    orgName = y.OrgName
                })
                .OrderBy(x => x.lineGroupId)
                .ThenBy(z => z.lineItemId)
                .ToList();
            }
            else
            {
                budgetQuery = data.Select(x => new
                {
                    x.LineItem,
                    x.GlAmount,
                    x.BudgetAmount,
                    x.RevisedBudgetAmount,
                    x.Year1Amount,
                    x.Year2Amount,
                    x.Year3Amount,
                    x.Year4Amount,
                    x.LineGroupId,
                    x.LineGroup,
                    x.LineItemId,
                    x.OrgId,
                    x.OrgName
                }).GroupBy(x => new { x.LineGroupId, x.LineGroup, x.LineItemId, x.LineItem })
                    .Select(y => new BudgetByAccount
                    {
                        Description = y.Key.LineItem,
                        ActualAmount = y.Sum(z => z.GlAmount) / divisor,
                        BudgetAmount = y.Sum(z => z.BudgetAmount) / divisor,
                        RevisedBudget = y.Sum(z => z.RevisedBudgetAmount) / divisor,
                        Year1Amount = y.Sum(z => z.Year1Amount) / divisor,
                        Year2Amount = y.Sum(z => z.Year2Amount) / divisor,
                        Year3Amount = y.Sum(z => z.Year3Amount) / divisor,
                        Year4Amount = y.Sum(z => z.Year4Amount) / divisor,
                        lineGroupId = y.Key.LineGroupId,
                        lineGroupDesc = y.Key.LineGroup,
                        lineItemId = y.Key.LineItemId,
                        lineItemDesc = y.Key.LineGroup
                    })
                    .OrderBy(x => x.lineGroupId)
                    .ThenBy(z => z.lineItemId)
                    .ToList();
            }

            return budgetQuery;
        }

        public IEnumerable<AppendixFinancePlan> GetFinancePlanByAccountGroup(int tenantId, int budgetYear, string budgetPhaseId, bool divideByMillions = false)
        {
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            var accGrpData = tenantDbContext.vw_doc_org_2_lvls.Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear).ToList();
            if (!string.IsNullOrEmpty(budgetPhaseId) && budgetPhaseId != "-1")
            {
                List<int> changeIds = _utility.GetChangeDataUsingBudgetPhase(budgetPhaseId, tenantId, budgetYear);
                if (changeIds.Any())
                {
                    accGrpData = accGrpData.Where(x => changeIds.Contains(x.fk_change_id)).ToList();
                }
            }

            int divisor = 1000;
            if (divideByMillions)
            {
                divisor = 1000000;
            }

            var budgetQuery = accGrpData.GroupBy(x => new { x.org_id_1, x.org_name_1, x.org_id_2, x.org_name_2 })
                                .OrderBy(x => x.Key.org_id_1)
                                .ThenBy(x => x.Key.org_id_2)
                                .Select(x => new
                                {
                                    org_id_1 = x.Key.org_id_1,
                                    org_name_1 = x.Key.org_name_1,
                                    org_id_2 = x.Key.org_id_2,
                                    org_name_2 = x.Key.org_name_2,
                                    gl_amount = x.Sum(y => y.gl_amount) / divisor,
                                    budget_amount = x.Sum(y => y.budget_amount) / divisor,
                                    year_1_amount = x.Sum(y => y.year_1_amount) / divisor,
                                    year_2_amount = x.Sum(y => y.year_2_amount) / divisor,
                                    year_3_amount = x.Sum(y => y.year_3_amount) / divisor,
                                    year_4_amount = x.Sum(y => y.year_4_amount) / divisor,
                                })
                                .Select(y => new AppendixFinancePlan
                                {
                                    //Description = y.Key.line_item,
                                    org_name_1 = y.org_name_1,
                                    org_name_2 = y.org_name_2,
                                    GL_Ammount = y.gl_amount,
                                    BudgetAmount = y.budget_amount,
                                    Year1Amount = y.year_1_amount,
                                    Year2Amount = y.year_2_amount,
                                    Year3Amount = y.year_3_amount,
                                    Year4Amount = y.year_4_amount,
                                    org_id_1 = y.org_id_1,
                                    org_id_2 = y.org_id_2,
                                    //orgGroupId = y.Key.line_group_id,
                                    //lineGroupDesc = y.Key.line_group,
                                    //lineItemId = y.Key.line_item_id,
                                    //lineItemDesc = y.Key.line_item
                                }).ToList();
            return budgetQuery.AsEnumerable();
        }

        public List<Budget> GetBudgetByService(string userId, int tenantId, string orgId, string serId, int budgetYear, string budgetPhaseId, bool divideByMillions, bool isServiceSetUp, Guid cacheKey, string? chapterIds = null)
        {
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            tenantDbContext.Database.SetCommandTimeout(900);
            int divisor = divideByMillions ? 1000000 : 1000;
            UserData userDetails = _utility.GetUserDetails(userId);
            string result = _appDataCache.GetStringForUser(userDetails.client_id, userDetails.tenant_id, userDetails.user_name, $"{cacheKey}-vw_doc_function_report");
            List<FunctionReport> data = result == null ? new List<FunctionReport>() :
                            JsonConvert.DeserializeObject<List<FunctionReport>>(result);

            if (!string.IsNullOrEmpty(chapterIds) && _finUtility.isChapterSetup(userId).GetAwaiter().GetResult())
            {
                data = data.Where(x => x.AttributeId == chapterIds).ToList();
            }
            else
            {
                if (!string.IsNullOrEmpty(orgId))
                {
                    data = data.Where(x => x.FpLevel1Value == orgId).ToList();
                }

                if (!string.IsNullOrEmpty(serId) && serId != "All")
                {
                    serId = serId.Trim();
                    data = data.Where(x => x.FpLevel2Value == serId).ToList();
                }
            }

            if (!string.IsNullOrEmpty(budgetPhaseId) && budgetPhaseId != "-1")
            {
                var changeIds = _utility.GetChangeDataUsingBudgetPhase(budgetPhaseId, tenantId, budgetYear);
                if (changeIds.Any())
                {
                    data = data.Where(x => changeIds.Contains(x.ChangeId)).ToList();
                }
            }

            List<Budget> budgetQuery;
            if (isServiceSetUp)
            {
                budgetQuery = data.Select(y => new Budget
                {
                    Description = y.DisplayName,
                    FunctionCode = y.FunctionCode,
                    OrgId = y.OrgId,
                    orgName = y.OrgName,
                    ActualAmount = y.GlAmount,
                    BudgetAmount = y.BudgetAmount,
                    RevisedBudgetAmount = y.RevisedBudgetAmount,
                    Year1Amount = y.Year1Amount,
                    Year2Amount = y.Year2Amount,
                    Year3Amount = y.Year3Amount,
                    Year4Amount = y.Year4Amount
                }).OrderBy(x => x.FunctionCode).ToList();
            }
            else
            {
                budgetQuery = data.GroupBy(y => new { y.FunctionCode, y.DisplayName })
                    .Select(y => new Budget
                    {
                        Description = y.Key.DisplayName,
                        FunctionCode = y.Key.FunctionCode, 
                        ActualAmount = y.Sum(z => z.GlAmount) / divisor,
                        BudgetAmount = y.Sum(z => z.BudgetAmount) / divisor,
                        RevisedBudgetAmount = y.Sum(z => z.RevisedBudgetAmount) / divisor,
                        Year1Amount = y.Sum(z => z.Year1Amount) / divisor,
                        Year2Amount = y.Sum(z => z.Year2Amount) / divisor,
                        Year3Amount = y.Sum(z => z.Year3Amount) / divisor,
                        Year4Amount = y.Sum(z => z.Year4Amount) / divisor
                    }).OrderBy(x => x.FunctionCode).ToList();
            }
            return budgetQuery;
        }

        public List<Budget> GetBudgetByOrganization(string userId, int tenantId, string orgId, string serId, int budgetYear, string budgetPhaseId,
                    Guid cacheKey ,bool divideByMillions = false, string? chapterId = null)
        {
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            tenantDbContext.Database.SetCommandTimeout(300);
            int divisor = divideByMillions ? 1000000 : 1000;
            UserData userDetails = _utility.GetUserDetails(userId);

            string result = _appDataCache.GetStringForUser(userDetails.client_id, userDetails.tenant_id, userDetails.user_name, $"{cacheKey}-vw_doc_org_report");
            List<AccountReport> data = result == null ? new List<AccountReport>() :
                            JsonConvert.DeserializeObject<List<AccountReport>>(result);
           
            if (_finUtility.isChapterSetup(userId).GetAwaiter().GetResult() && !string.IsNullOrEmpty(chapterId))
            {
                data = data.Where(x => x.AttributeId == chapterId).ToList();
            }
            else
            {
                if (!string.IsNullOrEmpty(orgId))
                {
                    data = data.Where(x => x.FpLevel1Value == orgId).ToList();
                }

                if (!string.IsNullOrEmpty(serId) && serId != "All")
                {
                    string sId = serId.Trim();
                    data = data.Where(x => x.FpLevel2Value == sId).ToList();
                }
            }

            List<int> changeIds = null;
            if (!string.IsNullOrEmpty(budgetPhaseId) && budgetPhaseId != "-1")
            {
                changeIds = _utility.GetChangeDataUsingBudgetPhase(budgetPhaseId, tenantId, budgetYear);
                if (changeIds.Any())
                {
                    data = data.Where(x => changeIds.Contains(x.ChangeId)).ToList();
                }
            }

            return data
                    .Select(x => new
                    {
                        x.FpLevel1Value,
                        x.OrgName,
                        x.GlAmount,
                        x.BudgetAmount,
                        x.RevisedBudgetAmount,
                        x.Year1Amount,
                        x.Year2Amount,
                        x.Year3Amount,
                        x.Year4Amount,
                        x.OrgId,
                        x.ChangeId
                    })
                    .GroupBy(x => new { x.OrgName, x.OrgId })
                    .Select(y => new Budget
                    {
                        Description = y.Key.OrgName,
                        OrgId = y.Key.OrgId,
                        ActualAmount = y.Sum(z => z.GlAmount) / divisor,
                        BudgetAmount = y.Sum(z => z.BudgetAmount) / divisor,
                        RevisedBudgetAmount = y.Sum(z => z.RevisedBudgetAmount) / divisor,
                        Year1Amount = y.Sum(z => z.Year1Amount) / divisor,
                        Year2Amount = y.Sum(z => z.Year2Amount) / divisor,
                        Year3Amount = y.Sum(z => z.Year3Amount) / divisor,
                        Year4Amount = y.Sum(z => z.Year4Amount) / divisor
                    })
                    .OrderBy(p => p.OrgId)
                    .ToList();
        }

        public List<Budget> GetBudgetByChapter(string userId, int tenantId, string orgId, string serId, int budgetYear, string budgetPhaseId,
            bool divideByMillions, string? chapterIds = null)
        {
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            tenantDbContext.Database.SetCommandTimeout(300);
            decimal divisor = divideByMillions ? 1000000 : 1000;

            var budgetByOrganizationQuery = tenantDbContext.vw_doc_function_report
                .AsNoTracking()
                .Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear);

            if (!string.IsNullOrEmpty(budgetPhaseId) && budgetPhaseId != "-1")
            {
                List<int> changeIds = _utility.GetChangeDataUsingBudgetPhase(budgetPhaseId, tenantId, budgetYear);
                if (changeIds.Any())
                {
                    budgetByOrganizationQuery = budgetByOrganizationQuery.Where(x => changeIds.Contains(x.fk_change_id));
                }
            }

            if (!string.IsNullOrEmpty(orgId))
            {
                budgetByOrganizationQuery = budgetByOrganizationQuery.Where(x => x.fp_level_1_value == orgId);
            }

            if (serId != null && serId != "All")
            {
                budgetByOrganizationQuery = budgetByOrganizationQuery.Where(x => x.fp_level_2_value == serId);
            }

            bool isChapterSetup = _finUtility.isChapterSetup(userId).GetAwaiter().GetResult();
            if (isChapterSetup && !string.IsNullOrEmpty(chapterIds))
            {
                budgetByOrganizationQuery = budgetByOrganizationQuery.Where(x => x.attribute_id == chapterIds);
            }

            var budgetQuery = budgetByOrganizationQuery
                .GroupBy(x => new { x.attribute_id, x.attribute_name })
                .Select(y => new Budget
                {
                    attributeId = y.Key.attribute_id,
                    Description = y.Key.attribute_id + '-' + y.Key.attribute_name,
                    ActualAmount = y.Sum(z => z.gl_amount) / divisor,
                    BudgetAmount = y.Sum(z => z.budget_amount) / divisor,
                    Year1Amount = y.Sum(z => z.year_1_amount) / divisor,
                    Year2Amount = y.Sum(z => z.year_2_amount) / divisor,
                    Year3Amount = y.Sum(z => z.year_3_amount) / divisor,
                    Year4Amount = y.Sum(z => z.year_4_amount) / divisor
                })
                .OrderBy(p => p.attributeId)
                .ToList();

            return budgetQuery;
        }

        public IEnumerable<InvestmentTotalProjectFinNetYearsOverview> GetInvestmentTotalInvProgramwise(int tenantId, int budgetYear, List<int> budgetPhaseChangeId, string userId, bool divideByMillions = false)
        {
            UserData userDetails = _utility.GetUserDetails(userId);
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            CultureInfo culture = CultureInfoFactory.CreateCulture(userDetails.language_preference);
            int divideBy = divideByMillions ? 1000000 : 1000;

            var baseQuery = tenantDbContext.vw_doc_investment_overview
                .AsNoTracking()
                .Where(a => a.fk_tenant_id == tenantId && a.budget_year == budgetYear && budgetPhaseChangeId.Contains(a.fk_change_id));

            baseQuery = baseQuery.Where(a => a.year_1_amount != 0 || a.year_2_amount != 0 || a.year_3_amount != 0 || a.year_4_amount != 0 || a.year_5_amount != 0
                                              || a.year_6_amount != 0 || a.year_7_amount != 0 || a.year_8_amount != 0 || a.year_9_amount != 0 || a.year_10_amount != 0 || a.net_cost != 0);

            var invstTotProFinNetYrDataQuery = baseQuery
                .GroupBy(x => new
                {
                    x.approval_ref_url,
                    x.investment_name,
                    x.finished_year,
                    x.display_proj_columns,
                    x.sum_level,
                    x.sum_code,
                    x.pk_investment_id,
                    x.fk_program_code,
                    x.inv_status,
                    x.sum_description_2,
                    x.sum_code_2,
                    x.sub_header_code,
                    x.sub_header_description,
                    x.approval_reference,
                    x.inv_phase,
                    x.oe_flag,
                })
                .Select(y => new InvestmentTotalProjectFinNetYearsOverview
                {
                    captionName = y.Key.investment_name,
                    totalProjectCost = y.Key.display_proj_columns == 1 ? y.Sum(z => z.gross_cost.Value) / divideBy : 0,
                    totalFinancingCost = y.Key.display_proj_columns == 1 ? y.Sum(z => z.financed_amount.Value) / divideBy : 0,
                    totalNetCost = y.Key.display_proj_columns == 1 ? y.Sum(z => z.net_cost.Value) / divideBy : 0,
                    year1Amount = y.Sum(z => z.year_1_amount) / divideBy,
                    year2Amount = y.Sum(z => z.year_2_amount) / divideBy,
                    year3Amount = y.Sum(z => z.year_3_amount) / divideBy,
                    year4Amount = y.Sum(z => z.year_4_amount) / divideBy,
                    year5Amount = y.Sum(z => z.year_5_amount) / divideBy,
                    year6Amount = y.Sum(z => z.year_6_amount) / divideBy,
                    year7Amount = y.Sum(z => z.year_7_amount) / divideBy,
                    year8Amount = y.Sum(z => z.year_8_amount) / divideBy,
                    year9Amount = y.Sum(z => z.year_9_amount) / divideBy,
                    year10Amount = y.Sum(z => z.year_10_amount) / divideBy,
                    finyear1Amount = y.Sum(z => z.fin_year_1_amount) / divideBy,
                    finyear2Amount = y.Sum(z => z.fin_year_2_amount) / divideBy,
                    finyear3Amount = y.Sum(z => z.fin_year_3_amount) / divideBy,
                    finyear4Amount = y.Sum(z => z.fin_year_4_amount) / divideBy,
                    fintotal4YearsSum = y.Key.display_proj_columns == 1 ? y.Sum(z => z.fin_total_amount.Value) / divideBy : 0,
                    total4YearsSum = y.Key.display_proj_columns == 1 ? y.Sum(z => z.sum_finplan.Value) / divideBy : 0,
                    plannedFinishYear = y.Key.finished_year ?? 0,
                    displayprojcolumns = y.Key.display_proj_columns,
                    sumLevel = y.Key.sum_level,
                    sumCode = y.Key.sum_code,
                    investmentId = y.Key.pk_investment_id,
                    programCode = y.Key.fk_program_code,
                    InvAppStatus = y.Key.inv_status,
                    sumCode_2 = y.Key.sum_code_2,
                    sumDescription_2 = y.Key.sum_description_2,
                    subHeaderCode = y.Key.sub_header_code,
                    subHeaderDescription = y.Key.sub_header_description,

                    approvalcost = y.Sum(z => z.approved_cost) / divideBy,
                    approvalreferenence = y.Key.approval_reference,
                    previouslyBudgeted = y.Sum(z => z.previously_budgeted) / divideBy,
                    approvalreferenence_url = y.Key.approval_ref_url,
                    investmentPhase = y.Key.inv_phase,
                    operExps = y.Key.oe_flag,
                    costEstimateP50 = y.Sum(z => z.cost_estimate_p50) / divideBy
                }).OrderBy(x => x.subHeaderCode);
            var data = invstTotProFinNetYrDataQuery.ToList();
            return data.OrderBy(x => x.captionName, StringComparer.Create(culture, false)).ToList();
        }

        public IEnumerable<InvestmentTotalProjectFinNetYearsOverview> GetAllInvestmentTotalProjectFinNetYears(
        int tenantId, string orgId, string serId, int budgetYear, ReportType reportType,
        List<int> budgetPhaseChangeId, string userId, bool divideByMillions = false)
        {
            UserData userDetails = _utility.GetUserDetails(userId);
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            decimal oneMillionUnitsDivide = 1000000;
            decimal thousandUnitsDivide = 1000;
            decimal divisor = divideByMillions ? oneMillionUnitsDivide : thousandUnitsDivide;
            var baseQuery = tenantDbContext.vw_doc_investment_overview
                        .AsNoTracking()
                        .Where(a => a.fk_tenant_id == tenantId && a.budget_year == budgetYear && budgetPhaseChangeId.Contains(a.fk_change_id));

            if (ReportType.Level1 == reportType)
            {
                baseQuery = baseQuery.Where(a => a.fp_level_1_value == orgId);
            }
            else if (ReportType.Level2 == reportType)
            {
                if (!string.IsNullOrEmpty(serId))
                {
                    baseQuery = baseQuery.Where(a => a.fp_level_2_value.Contains(serId));
                }
                else
                {
                    return Enumerable.Empty<InvestmentTotalProjectFinNetYearsOverview>();
                }
            }
            else if (ReportType.MultiLevel == reportType)
            {
                baseQuery = baseQuery.Where(a => a.fp_level_1_value.Contains(orgId) && a.fp_level_2_value.Contains(serId));
            }
            else
            {
                return Enumerable.Empty<InvestmentTotalProjectFinNetYearsOverview>();
            }

            var invstTotProFinNetYrData = baseQuery
                .GroupBy(x => new
                {
                    x.investment_name,
                    x.finished_year,
                    x.display_proj_columns,
                    x.sum_level,
                    x.sum_code,
                    x.pk_investment_id,
                    x.inv_status,
                    x.sub_level_sa_code,
                    x.sub_level_sa_description,
                    x.oe_flag
                })
                .Select(y => new InvestmentTotalProjectFinNetYearsOverview
                {
                    captionName = y.Key.investment_name,
                    totalProjectCost = y.Key.display_proj_columns == 1 ? y.Sum(z => z.gross_cost.Value) / divisor : 0,
                    totalFinancingCost = y.Key.display_proj_columns == 1 ? y.Sum(z => z.financed_amount.Value) / divisor : 0,
                    totalNetCost = y.Key.display_proj_columns == 1 ? y.Sum(z => z.net_cost.Value) / divisor : 0,
                    year1Amount = y.Sum(z => z.year_1_amount) / divisor,
                    year2Amount = y.Sum(z => z.year_2_amount) / divisor,
                    year3Amount = y.Sum(z => z.year_3_amount) / divisor,
                    year4Amount = y.Sum(z => z.year_4_amount) / divisor,
                    year5Amount = y.Sum(z => z.year_5_amount) / divisor,
                    year6Amount = y.Sum(z => z.year_6_amount) / divisor,
                    year7Amount = y.Sum(z => z.year_7_amount) / divisor,
                    year8Amount = y.Sum(z => z.year_8_amount) / divisor,
                    year9Amount = y.Sum(z => z.year_9_amount) / divisor,
                    year10Amount = y.Sum(z => z.year_10_amount) / divisor,
                    finyear1Amount = y.Sum(z => z.fin_year_1_amount) / divisor,
                    finyear2Amount = y.Sum(z => z.fin_year_2_amount) / divisor,
                    finyear3Amount = y.Sum(z => z.fin_year_3_amount) / divisor,
                    finyear4Amount = y.Sum(z => z.fin_year_4_amount) / divisor,
                    fintotal4YearsSum = y.Sum(z => z.fin_total_amount.Value) / divisor,
                    total4YearsSum = y.Sum(z => z.sum_finplan.Value) / divisor,
                    plannedFinishYear = y.Key.finished_year.Value,
                    displayprojcolumns = y.Key.display_proj_columns,
                    sumLevel = y.Key.sum_level,
                    sumCode = y.Key.sum_code,
                    investmentId = y.Key.pk_investment_id,
                    InvAppStatus = y.Key.inv_status,
                    subLevelSACode = y.Key.sub_level_sa_code,
                    subLevelSADescription = y.Key.sub_level_sa_description,
                    operExps = y.Key.oe_flag
                }).OrderBy(z => z.totalProjectCost);

            var data = invstTotProFinNetYrData.ToList();

            CultureInfo culture = new(userDetails.language_preference);
            return data.OrderBy(x => x.captionName, StringComparer.Create(culture, false)).ToList();
        }

        public async Task UpdateViewDocs(string userId, Guid cacheKey, string chapterType, int budgetYear)
        {
            var chapterTypeToReportMap = new List<string> { "vw_doc_accgrp_report", "vw_doc_function_report", "vw_doc_org_report" };
            if (!chapterTypeToReportMap.Contains(chapterType))
            {
                throw new ArgumentException("Invalid chapter type provided.");
            }
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TimeSpan cacheTimeOut = new TimeSpan(24, 0, 0);//clear once the publish is done later modification
            var dataReterived = await FetchAndProcessData($"SELECT * FROM {chapterType} where fk_tenant_id = @tenantID and budget_year = @budgetYear", userId, chapterType, budgetYear);
            string data = JsonConvert.SerializeObject(dataReterived);
            await _appDataCache.SetStringForUserAsync(userDetails.client_id, userDetails.tenant_id, userId, $"{cacheKey}-{chapterType}", data, cacheTimeOut);
        }

        private async Task<List<object>> FetchAndProcessData(string query, string userId, string chapterType, int budgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            var dataTable = new DataTable();
            var connection = (await _utility.GetTenantDbContextForParallelReadAsync()).Database.GetDbConnection() as SqlConnection;
            await using var command = new SqlCommand(query, connection);
            command.CommandTimeout = 900;
            command.Parameters.AddWithValue("@tenantID", userDetails.tenant_id);
            command.Parameters.AddWithValue("@budgetYear", budgetYear);
            SqlDataAdapter adapter = new SqlDataAdapter(command);
            adapter.Fill(dataTable);
            switch (chapterType)
            {
                case "vw_doc_accgrp_report":
                case "vw_doc_org_report":
                    bool orgReport = chapterType == "vw_doc_org_report";
                    var reportItems = dataTable.AsEnumerable().Select(row => new AccountReport
                    {
                        LineGroupId = orgReport ? 0 : row.Field<int>("line_group_id"),
                        LineGroup = orgReport ? string.Empty : row.Field<string>("line_group"),
                        LineItemId = orgReport ? 0 : row.Field<int>("line_item_id"),
                        LineItem = orgReport ? string.Empty : row.Field<string>("line_item"),
                        GlAmount = row.Field<decimal>("gl_amount"),
                        BudgetAmount = row.Field<decimal>("budget_amount"),
                        RevisedBudgetAmount = row.Field<decimal>("revised_budget_amount"),
                        Year1Amount = row.Field<decimal>("year_1_amount"),
                        Year2Amount = row.Field<decimal>("year_2_amount"),
                        Year3Amount = row.Field<decimal>("year_3_amount"),
                        Year4Amount = row.Field<decimal>("year_4_amount"),
                        ChangeId = row.Field<int>("fk_change_id"),
                        AttributeId = row.Field<string>("attribute_id"),
                        AttributeName = orgReport ? string.Empty : row.Field<string>("attribute_id"),
                        FpLevel1Value = row.Field<string>("fp_level_1_value"),
                        FpLevel2Value = row.Field<string>("fp_level_2_value"),
                        OrgId = row.Field<string>("org_id"),
                        OrgName = row.Field<string>("org_name"),
                    }).Cast<object>().ToList();
                    return reportItems;

                case "vw_doc_function_report":
                    var Items = dataTable.AsEnumerable().Select(row => new FunctionReport
                    {
                        FunctionCode = row.Field<string>("fk_function_code"),
                        DisplayName = row.Field<string>("display_name"),
                        GlAmount = row.Field<decimal>("gl_amount"),
                        BudgetAmount = row.Field<decimal>("budget_amount"),
                        RevisedBudgetAmount = row.Field<decimal>("revised_budget_amount"),
                        Year1Amount = row.Field<decimal>("year_1_amount"),
                        Year2Amount = row.Field<decimal>("year_2_amount"),
                        Year3Amount = row.Field<decimal>("year_3_amount"),
                        Year4Amount = row.Field<decimal>("year_4_amount"),
                        ChangeId = row.Field<int>("fk_change_id"),
                        AttributeId = row.Field<string>("attribute_id"),
                        AttributeName = row.Field<string>("attribute_id"),
                        FpLevel1Value = row.Field<string>("fp_level_1_value"),
                        FpLevel2Value = row.Field<string>("fp_level_2_value"),
                        OrgId = row.Field<string>("org_id"),
                        OrgName = row.Field<string>("org_name"),
                    }).Cast<object>().ToList();
                    return Items;

                default:
                    throw new ArgumentException("Unsupported chapter type");
            }
        }

        public class OtherRequiredFields
        {
            public string FpLevel1Value { get; set; }
            public string FpLevel2Value { get; set; }

            public string OrgId { get; set; }
            public string OrgName { get; set; }
            public decimal GlAmount { get; set; }
            public decimal BudgetAmount { get; set; }
            public decimal RevisedBudgetAmount { get; set; }
            public decimal Year1Amount { get; set; }
            public decimal Year2Amount { get; set; }
            public decimal Year3Amount { get; set; }
            public decimal Year4Amount { get; set; }
            public int ChangeId { get; set; }
            public string AttributeId { get; set; }
            public string AttributeName { get; set; }
        }

        public class AccountReport : OtherRequiredFields
        {
            public int LineGroupId { get; set; }
            public string LineGroup { get; set; }
            public int LineItemId { get; set; }
            public string LineItem { get; set; }
        }

        public class FunctionReport : OtherRequiredFields
        {
            public string FunctionCode { get; set; }
            public string DisplayName { get; set; }
        }
    }
}