#pragma warning disable CS8629
#pragma warning disable CS8600
#pragma warning disable CS8601
#pragma warning disable CS8602
#pragma warning disable CS8603
#pragma warning disable CS8604
#pragma warning disable CS8625

using EntityFrameworkExtras.EFCore;
using Framsikt.BL.Exceptions;
using Framsikt.BL.Helpers;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Framsikt.Entities.Core;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Globalization;
using System.Linq.Dynamic.Core;
using System.Text;
using static Framsikt.BL.DocWidget;
using static Framsikt.BL.Helpers.clsConstants;
using KeyValuePair = Framsikt.BL.Helpers.KeyValuePair;

namespace Framsikt.BL
{
    public enum TreeLevel
    {
        LevelOne,
        BothLevels
    }

    public partial class BudgetManagement
    {
        public async Task<List<BListActionsBudman>> GetGridDataBListAsync(string userId, int budgetYear, List<KeyValuePair> selectedColumns)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext budgetManagementDbContext = await _utility.GetTenantDBContextAsync();
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));
            string budmanBlistActionsGridLevel1 = await _utility.GetParameterValueAsync(userId, "BUDMAN_GROUPING_L1");
            string budmanBlistActionsGridLevel2 = await _utility.GetParameterValueAsync(userId, "BUDMAN_GROUPING_L2");
            List<KeyValueData> lstActionTags = await GetActionTagsAsync(userId);
            int? changeId = await GetChangeIdAsync(userDetails.pk_id, userDetails.tenant_id, userDetails.client_id);

            List<int> lstActionTypes = new List<int>() { 31, 41, 9 };

            var dataSet = await (from a in budgetManagementDbContext.tfp_temp_header
                                 join b in budgetManagementDbContext.tfp_temp_detail on new { a = a.fk_tenant_id, b = a.pk_temp_id }
                                                                                 equals new { a = b.fk_tenant_id, b = b.fk_temp_id }
                                 join c in budgetManagementDbContext.tco_fp_alter_codes on new { a = b.fk_tenant_id, b = b.fk_alter_code }
                                                                                    equals new { a = c.fk_tenant_id, b = c.pk_alter_code } into c1
                                 from c2 in c1.DefaultIfEmpty()
                                 where a.fk_tenant_id == userDetails.tenant_id
                                 && b.budget_year == budgetYear
                                 && !a.is_parked_action
                                 && (b.forecast_period == null || b.forecast_period.Value == 0)
                                 && lstActionTypes.Contains(a.action_type)
                                 select new BListActionsBudman
                                 {
                                     actionId = a.pk_temp_id,
                                     actionName = a.description,
                                     departmentCode = b.department_code,
                                     functionCode = b.function_code,
                                     tags = a.tags,
                                     alterCodeId = string.IsNullOrEmpty(b.fk_alter_code) ? "" : b.fk_alter_code,
                                     alterCode = (c2 == null || string.IsNullOrEmpty(c2.alter_description)) ? b.fk_alter_code : (b.fk_alter_code + "-" + c2.alter_description),
                                     priority = a.priority.HasValue ? a.priority.Value.ToString() : string.Empty,
                                     year1Amount = b.year_1_amount / 1000,
                                     year2Amount = b.year_2_amount / 1000,
                                     year3Amount = b.year_3_amount / 1000,
                                     year4Amount = b.year_4_amount / 1000,
                                     changeId = b.fk_change_id,
                                     tooltiptext = string.IsNullOrEmpty(a.long_description) ? string.Empty : a.long_description
                                 }).ToListAsync();

            //Get Hidden departments
            List<string> departmentsToHide = await _finUtility.GetDepartmentsToHideForBListKommuneAsync(userId, budgetYear,
                Convert.ToString(await _finUtility.GetActiveBudgetChangeIdAsync(userId, "Budget_Proposal", budgetYear)));

            if (departmentsToHide.Any())
            {
                List<string> departmentsToShow = dataSet.Select(x => x.departmentCode).Except(departmentsToHide).ToList();
                dataSet = (from d in dataSet
                           join f in departmentsToShow on d.departmentCode equals f
                           select d).ToList();
            }
            var tcoServiceValues = await budgetManagementDbContext.tco_service_values.Where(x => x.fk_tenant_id == userDetails.tenant_id).ToListAsync();

            List<BListActionsBudman> modifiedDataset = null;

            /*org & org*/
            if (!string.IsNullOrEmpty(budmanBlistActionsGridLevel1)
                && !string.IsNullOrEmpty(budmanBlistActionsGridLevel2)
                && budmanBlistActionsGridLevel1.StartsWith("org")
                && budmanBlistActionsGridLevel2.StartsWith("org"))
            {
                modifiedDataset = DisplayOrgAndOrg(orgVersionContent, dataSet, budmanBlistActionsGridLevel1, budmanBlistActionsGridLevel2);
            }

            /*org & service*/
            if (!string.IsNullOrEmpty(budmanBlistActionsGridLevel1)
                && !string.IsNullOrEmpty(budmanBlistActionsGridLevel2)
                && budmanBlistActionsGridLevel1.StartsWith("org")
                && budmanBlistActionsGridLevel2.StartsWith("service"))
            {
                modifiedDataset = DisplayOrgAndService(orgVersionContent, dataSet, budmanBlistActionsGridLevel1, budmanBlistActionsGridLevel2, tcoServiceValues);
            }

            /*org & empty*/
            if (!string.IsNullOrEmpty(budmanBlistActionsGridLevel1)
                && string.IsNullOrEmpty(budmanBlistActionsGridLevel2)
                && budmanBlistActionsGridLevel1.StartsWith("org"))
            {
                modifiedDataset = DisplayOrgAndEmpty(orgVersionContent, dataSet, budmanBlistActionsGridLevel1);
            }

            /*service & org*/
            if (!string.IsNullOrEmpty(budmanBlistActionsGridLevel1)
                && !string.IsNullOrEmpty(budmanBlistActionsGridLevel2)
                && budmanBlistActionsGridLevel1.StartsWith("service")
                && budmanBlistActionsGridLevel2.StartsWith("org"))
            {
                modifiedDataset = DisplayServiceAndOrg(orgVersionContent, dataSet, budmanBlistActionsGridLevel1, budmanBlistActionsGridLevel2, tcoServiceValues);
            }

            /*no set up*/
            if (string.IsNullOrEmpty(budmanBlistActionsGridLevel1)
                && string.IsNullOrEmpty(budmanBlistActionsGridLevel2))
            {
                modifiedDataset = DisplayDefaultSetUp(orgVersionContent, dataSet);
            }

            var changeIdDataSet = modifiedDataset.Where(x => x.changeId == changeId).ToList();

            List<string> col = selectedColumns != null ? selectedColumns.Where(x => x.isChecked == true).Select(x => x.key).ToList() : new List<string>();
            var groupedDataset = (from m in modifiedDataset
                                  group m by new
                                  {
                                      m.actionId,
                                      level1Id = col.Contains("level1Obj.key") ? m.level1Id : string.Empty,
                                      level1 = col.Contains("level1Obj.key") ? m.level1 : string.Empty,
                                      m.orgLevel,
                                      m.actionName,
                                      level2Id = col.Contains("level2Obj.key") ? m.level2Id : string.Empty,
                                      level2 = col.Contains("level2Obj.key") ? m.level2 : string.Empty,
                                      tags = col.Contains("tags") ? m.tags : string.Empty,
                                      alterCodeId = col.Contains("alterCodeObj.key") ? m.alterCodeId : string.Empty,
                                      alterCode = col.Contains("alterCodeObj.key") ? m.alterCode : string.Empty,
                                      priority = col.Contains("priorityObj.key") ? m.priority : string.Empty,
                                      m.tooltiptext
                                  } into g
                                  select new BListActionsBudman
                                  {
                                      actionId = g.Key.actionId,
                                      level1Id = g.Key.level1Id,
                                      level1 = g.Key.level1,
                                      orgLevel = g.Key.orgLevel,
                                      level2Id = g.Key.level2Id,
                                      level2 = g.Key.level2,
                                      actionName = g.Key.actionName,
                                      tags = g.Key.tags,
                                      alterCodeId = g.Key.alterCodeId,
                                      alterCode = g.Key.alterCode,
                                      priority = g.Key.priority,
                                      year1Amount = g.Sum(x => x.year1Amount),
                                      year2Amount = g.Sum(x => x.year2Amount),
                                      year3Amount = g.Sum(x => x.year3Amount),
                                      year4Amount = g.Sum(x => x.year4Amount),
                                      changeyear1 = changeIdDataSet.Where(x => x.actionId == g.Key.actionId).Sum(c => c.year1Amount),
                                      changeyear2 = changeIdDataSet.Where(x => x.actionId == g.Key.actionId).Sum(c => c.year2Amount),
                                      changeyear3 = changeIdDataSet.Where(x => x.actionId == g.Key.actionId).Sum(c => c.year3Amount),
                                      changeyear4 = changeIdDataSet.Where(x => x.actionId == g.Key.actionId).Sum(c => c.year4Amount),
                                      tooltiptext = g.Key.tooltiptext
                                  }).ToList();

            groupedDataset = groupedDataset.OrderBy(x => x.level1Id).ToList();

            var level1Ids = groupedDataset.OrderBy(a => a.level1Id).Select(x => x.level1Id).Distinct().ToList();

            foreach (var level1Id in level1Ids)
            {
                groupedDataset.Add(new BListActionsBudman()
                {
                    level1Id = level1Id,
                    level1 = groupedDataset.FirstOrDefault(x => x.level1Id == level1Id).level1,
                    orgLevel = groupedDataset.FirstOrDefault(x => x.level1Id == level1Id).orgLevel,
                    level2Id = string.Empty,
                    level2 = string.Empty,
                    actionName = string.Empty,
                    tags = string.Empty,
                    alterCode = string.Empty,
                    alterCodeId = string.Empty,
                    priority = string.Empty,
                    year1Amount = groupedDataset.Where(x => x.level1Id == level1Id).Sum(s => s.year1Amount),
                    year2Amount = groupedDataset.Where(x => x.level1Id == level1Id).Sum(s => s.year2Amount),
                    year3Amount = groupedDataset.Where(x => x.level1Id == level1Id).Sum(s => s.year3Amount),
                    year4Amount = groupedDataset.Where(x => x.level1Id == level1Id).Sum(s => s.year4Amount),
                    changeyear1 = groupedDataset.Where(x => x.level1Id == level1Id).Sum(s => s.changeyear1),
                    changeyear2 = groupedDataset.Where(x => x.level1Id == level1Id).Sum(s => s.changeyear2),
                    changeyear3 = groupedDataset.Where(x => x.level1Id == level1Id).Sum(s => s.changeyear3),
                    changeyear4 = groupedDataset.Where(x => x.level1Id == level1Id).Sum(s => s.changeyear4),
                    isBold = true
                });
            }

            int id = 0;
            groupedDataset.ForEach(x =>
            {
                id++;
                x.pkId = id;

                if (!string.IsNullOrEmpty(x.tags))
                {
                    var TagIds = x.tags.Split(',').Select(int.Parse).ToList();
                    StringBuilder sb = new StringBuilder();
                    foreach (var tags in TagIds)
                    {
                        var firstOrDefault = lstActionTags.FirstOrDefault(l => l.KeyId == tags);
                        if (firstOrDefault != null)
                        {
                            sb.Append("<span class='bp-grey-tag'>" + firstOrDefault.ValueString + "</span>");
                            x.tagsLst.Add(firstOrDefault.ValueString);
                        }
                    }
                    x.tags = sb.ToString();
                }
                else
                {
                    x.tags = string.Empty;
                }
            });

            return groupedDataset;
        }

        public async Task<bool> GetBMBListFPGridEditabilityAsync(string userId, int budgetYear, int changeId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext budgetManagementDbContext = await _utility.GetTenantDBContextAsync();

            var change = await budgetManagementDbContext.tfp_budget_changes.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id
                                                                          && x.budget_year == budgetYear
                                                                          && x.pk_change_id == changeId
                                                                          && x.status == 1);

            var nonAllocatedStatus = await budgetManagementDbContext.tco_application_flag.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                                     && x.budget_year == budgetYear
                                                                                                     && x.flag_name.ToLower() == "Dont_Update_Non_Allocated_Actions".ToLower()
                                                                                                     && x.flag_status == 1);

            if (nonAllocatedStatus == null)
            {
                /*using non allocated*/
            }

            if (change == null || (change != null && (change.workflow_status == 1 || change.workflow_status == 2)))
            {
                return false;
            }
            else if (change.workflow_status == 40)
            {
                return true;
            }
            else if (change.workflow_status == 30 && nonAllocatedStatus != null)
            {
                return true;
            }
            else if (change.workflow_status == 30 && nonAllocatedStatus == null)
            {
                return false;
            }
            else
            {
                return false;
            }
        }

        public async Task<List<LevelIds>> GetLevelIdBListAsync(string userId, int budgetYear)
        {
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));
            string budmanBlistActionsGridLevel1 = await _utility.GetParameterValueAsync(userId, "BUDMAN_GROUPING_L1");
            string budmanBlistActionsGridLevel2 = await _utility.GetParameterValueAsync(userId, "BUDMAN_GROUPING_L2");
            List<LevelIds> LevelIdDropDown = null;

            if (!string.IsNullOrEmpty(budmanBlistActionsGridLevel1)
                && !string.IsNullOrEmpty(budmanBlistActionsGridLevel2)
                && budmanBlistActionsGridLevel1.StartsWith("org")
                && budmanBlistActionsGridLevel2.StartsWith("org"))
            {
                LevelIdDropDown = GetLevelIdOrgAndOrg(orgVersionContent, budmanBlistActionsGridLevel1, budmanBlistActionsGridLevel2);
            }

            if (!string.IsNullOrEmpty(budmanBlistActionsGridLevel1)
                && !string.IsNullOrEmpty(budmanBlistActionsGridLevel2)
                && budmanBlistActionsGridLevel1.StartsWith("org")
                && budmanBlistActionsGridLevel2.StartsWith("service"))
            {
                LevelIdDropDown = await GetLevelIdOrgAndServiceAsync(orgVersionContent, budmanBlistActionsGridLevel1, budmanBlistActionsGridLevel2, userId);
            }

            if (!string.IsNullOrEmpty(budmanBlistActionsGridLevel1)
                && string.IsNullOrEmpty(budmanBlistActionsGridLevel2)
                && budmanBlistActionsGridLevel1.StartsWith("org"))
            {
                LevelIdDropDown = GetLevelIdOrgAndEmpty(orgVersionContent, budmanBlistActionsGridLevel1);
            }

            if (!string.IsNullOrEmpty(budmanBlistActionsGridLevel1)
                && !string.IsNullOrEmpty(budmanBlistActionsGridLevel2)
                && budmanBlistActionsGridLevel1.StartsWith("service")
                && budmanBlistActionsGridLevel2.StartsWith("org"))
            {
                LevelIdDropDown = await GetLevelIdServiceAndOrgAsync(orgVersionContent, budmanBlistActionsGridLevel1, budmanBlistActionsGridLevel2, userId);
            }

            if (string.IsNullOrEmpty(budmanBlistActionsGridLevel1)
                && string.IsNullOrEmpty(budmanBlistActionsGridLevel2))
            {
                LevelIdDropDown = GetLevelIdDefaultSetUp(orgVersionContent);
            }

            return LevelIdDropDown;
        }

        public Task<List<KeyValueData>> GetActionTagsDropDownAsync(string userId)
        {
            return GetActionTagsAsync(userId);
        }

        public async Task<List<ClsAlterCodeData>> GetAlterCodesAsync(string userId, int actionType)
        {
            List<ClsAlterCodeData> alterCodesData = null;
            alterCodesData = await _consequentAdjustedBudget.GetAlterCodesAsync(userId, actionType);
            return alterCodesData;
        }

        public List<KeyValueNewData> GetPriorities()
        {
            List<KeyValueNewData> PrioritiesList = new List<KeyValueNewData>();
            for (int i = 1; i <= 40; i++)
            {
                PrioritiesList.Add(
                    new KeyValueNewData
                    {
                        Key = i.ToString(),
                        Value = i.ToString(),
                    }
                );
            }
            return PrioritiesList;
        }

        public async Task<JObject> InsertIntoBlistActionsAsync(List<ActionDataObj> dataobj, string userId)
        {
            dynamic returnInfo = new JObject();
            TenantDBContext budgetManagementDbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetManagement");
            foreach (ActionDataObj obj in dataobj)
            {
                //default setup values
                dynamic accountingCombination = await _consequentAdjustedBudget.GetDefaultsForBudgetChangeAsync(41, userId, obj.level1Id, obj.level2Id, string.Empty, obj.budgetYear);

                dynamic defaultObj = (JObject)accountingCombination["gridDatabdtchange"][0];

                List<string> departmentsLstToHide = await _finUtility.GetDepartmentsToHideForBListKommuneAsync(userId, obj.budgetYear,
                    Convert.ToString(await _finUtility.GetActiveBudgetChangeIdAsync(userId, "Budget_Proposal", obj.budgetYear)));

                if (obj.changeId == -1 || obj.changeId == 0)
                {
                    returnInfo.Add("result", "error");
                    returnInfo.Add("message", ((langStrings.FirstOrDefault(v => v.Key == "ChangeIdMissing")).Value).LangText);
                    return returnInfo;
                }

                if (string.IsNullOrEmpty((string)defaultObj["department"]["departmentValue"]))
                {
                    returnInfo.Add("result", "error");
                    returnInfo.Add("message", ((langStrings.FirstOrDefault(v => v.Key == "DepartmentSetUpMissing")).Value).LangText);
                    return returnInfo;
                }

                //hide the department
                if (departmentsLstToHide.Any() && !string.IsNullOrEmpty((string)defaultObj["department"]["departmentValue"]))
                {
                    if (string.IsNullOrEmpty((string)defaultObj.department.departmentValue) && departmentsLstToHide.Contains((string)defaultObj.department.departmentValue))
                    {
                        returnInfo.Add("result", "error");
                        returnInfo.Add("message", ((langStrings.FirstOrDefault(v => v.Key == "DepartmentSetUpMissing")).Value).LangText);
                        return returnInfo;
                    }
                }

                if (string.IsNullOrEmpty((string)defaultObj["account"]["accountValue"]))
                {
                    returnInfo.Add("result", "error");
                    returnInfo.Add("message", ((langStrings.FirstOrDefault(v => v.Key == "AccountSetUpMissing")).Value).LangText);
                    return returnInfo;
                }

                if (string.IsNullOrEmpty((string)defaultObj["functionn"]["functionValue"]))
                {
                    returnInfo.Add("result", "error");
                    returnInfo.Add("message", ((langStrings.FirstOrDefault(v => v.Key == "FunctionSetUpMissing")).Value).LangText);
                    return returnInfo;
                }

                var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(obj.budgetYear, 1));
                int actionId;
                tfp_temp_header tfp = new tfp_temp_header();
                tfp.description = string.IsNullOrEmpty(obj.description) ? string.Empty : obj.description;
                tfp.fk_tenant_id = userDetails.tenant_id;
                tfp.consequence = string.Empty;
                tfp.start_date = DateTime.UtcNow;
                tfp.action_type = 41;
                tfp.action_source = 0;
                tfp.line_order = 0;
                tfp.isManuallyAdded = 1;
                tfp.title = string.Empty;
                tfp.status = 0;
                tfp.updated = DateTime.UtcNow;
                tfp.updated_by = userDetails.pk_id;
                tfp.priority = 0;
                tfp.long_description = string.Empty;
                tfp.tag = string.Empty;
                tfp.fk_area_id = 0;
                tfp.fk_action_id = 0;
                tfp.financial_plan_description = string.Empty;
                tfp.tags = obj.tag;
                tfp.display_financial_plan_flag = true;
                tfp.display_cab_flag = null;
                tfp.is_imported = null;
                tfp.display_zero_action = false;
                tfp.goals_tags = null;
                tfp.is_MROverview_flag = false;
                tfp.is_parked_action = false;
                tfp.log_id = Guid.NewGuid();
                tfp.org_id = orgVersionContent.lstOrgHierarchy.FirstOrDefault().org_id_1;
                tfp.org_level = 1;
                budgetManagementDbContext.tfp_temp_header.Add(tfp);
                await budgetManagementDbContext.SaveChangesAsync();
                await _utility.LogBlistOrParkedActionChangesAsync(userDetails, obj.budgetYear, budgetManagementDbContext, tfp, obj.changeId, false, string.Empty);
                actionId = tfp.pk_temp_id;

                string freedim1 = string.Empty;
                string freedim2 = string.Empty;
                string freedim3 = string.Empty;
                string freedim4 = string.Empty;
                string adjustmentCode = string.Empty;

                try
                {
                    if (string.IsNullOrEmpty((string)defaultObj["freeDim1"]["fk_freedim_code"]))
                    {
                        freedim1 = (string)defaultObj["freeDim1"]["fk_freedim_code"];
                    }
                }
                catch (Exception)
                {
                    freedim1 = string.Empty;
                }

                try
                {
                    if (string.IsNullOrEmpty((string)defaultObj["freeDim2"]["fk_freedim_code"]))
                    {
                        freedim2 = (string)defaultObj["freeDim2"]["fk_freedim_code"];
                    }
                }
                catch (Exception)
                {
                    freedim2 = string.Empty;
                }

                try
                {
                    if (string.IsNullOrEmpty((string)defaultObj["freeDim3"]["fk_freedim_code"]))
                    {
                        freedim3 = (string)defaultObj["freeDim3"]["fk_freedim_code"];
                    }
                }
                catch (Exception)
                {
                    freedim3 = string.Empty;
                }

                try
                {
                    if (string.IsNullOrEmpty((string)defaultObj["freeDim4"]["fk_freedim_code"]))
                    {
                        freedim4 = (string)defaultObj["freeDim4"]["fk_freedim_code"];
                    }
                }
                catch (Exception)
                {
                    freedim4 = string.Empty;
                }

                try
                {
                    if (string.IsNullOrEmpty((string)defaultObj["adjustmentCode"]["value"]))
                    {
                        adjustmentCode = (string)defaultObj["adjustmentCode"]["value"];
                    }
                }
                catch (Exception)
                {
                    adjustmentCode = string.Empty;
                }

                tfp_temp_detail tfpDetail = new tfp_temp_detail();
                tfpDetail.fk_temp_id = actionId;
                tfpDetail.fk_tenant_id = userDetails.tenant_id;
                tfpDetail.budget_year = obj.budgetYear;
                tfpDetail.fk_account_code = (string)defaultObj["account"]["accountValue"];
                tfpDetail.department_code = (string)defaultObj["department"]["departmentValue"];
                tfpDetail.function_code = (string)defaultObj["functionn"]["functionValue"];
                tfpDetail.project_code = (string)defaultObj["project"]["fk_project_code"];
                tfpDetail.asset_code = string.Empty;
                tfpDetail.fk_investment_id = 0;
                tfpDetail.investment_row_id = 0;
                tfpDetail.year_1_amount = obj.year1Amount * 1000;
                tfpDetail.year_2_amount = obj.year2Amount * 1000;
                tfpDetail.year_3_amount = obj.year3Amount * 1000;
                tfpDetail.year_4_amount = obj.year4Amount * 1000;
                tfpDetail.updated = DateTime.UtcNow;
                tfpDetail.updated_by = userDetails.pk_id;
                tfpDetail.free_dim_1 = freedim1;
                tfpDetail.free_dim_2 = freedim2;
                tfpDetail.free_dim_3 = freedim3;
                tfpDetail.free_dim_4 = freedim4;
                tfpDetail.fk_alter_code = obj.alterCode;
                tfpDetail.fk_adjustment_code = adjustmentCode;
                tfpDetail.fk_change_id = obj.changeId;
                tfpDetail.description = null;
                tfpDetail.year_5_amount = 0;
                tfpDetail.fk_key_id = null;
                tfpDetail.forecast_period = 0;
                budgetManagementDbContext.tfp_temp_detail.Add(tfpDetail);
                await budgetManagementDbContext.SaveChangesAsync();
            }

            returnInfo.Add("result", "success");
            returnInfo.Add("message", "");
            return returnInfo;
        }

        public async Task<JObject> InsertIntoFinPlanActionsAsync(List<ActionDataObj> dataobj, string userId)
        {
            TenantDBContext budgetManagementDbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetManagement");
            dynamic returnInfo = new JObject();
            foreach (ActionDataObj obj in dataobj)
            {
                var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(obj.budgetYear, 1));

                //default setup values
                dynamic accountingCombination = await _consequentAdjustedBudget.GetDefaultsForBudgetChangeAsync(41, userId, obj.level1Id, obj.level2Id, string.Empty, obj.budgetYear);

                dynamic defaultObj = (JObject)accountingCombination["gridDatabdtchange"][0];

                if (obj.changeId == -1 || obj.changeId == 0)
                {
                    returnInfo.Add("result", "error");
                    returnInfo.Add("message", ((langStrings.FirstOrDefault(v => v.Key == "ChangeIdMissing")).Value).LangText);
                    return returnInfo;
                }

                if (string.IsNullOrEmpty((string)defaultObj["account"]["accountValue"]))
                {
                    returnInfo.Add("result", "error");
                    returnInfo.Add("message", ((langStrings.FirstOrDefault(v => v.Key == "AccountSetUpMissing")).Value).LangText);
                    return returnInfo;
                }

                if (string.IsNullOrEmpty((string)defaultObj["department"]["departmentValue"]))
                {
                    returnInfo.Add("result", "error");
                    returnInfo.Add("message", ((langStrings.FirstOrDefault(v => v.Key == "DepartmentSetUpMissing")).Value).LangText);
                    return returnInfo;
                }

                if (string.IsNullOrEmpty((string)defaultObj["functionn"]["functionValue"]))
                {
                    returnInfo.Add("result", "error");
                    returnInfo.Add("message", ((langStrings.FirstOrDefault(v => v.Key == "FunctionSetUpMissing")).Value).LangText);
                    return returnInfo;
                }

                int actionId = (await budgetManagementDbContext.tfp_trans_header.MaxAsync(x => x.pk_action_id)) + 1;
                tfp_trans_header tfp = new tfp_trans_header();
                tfp.pk_action_id = actionId;
                tfp.description = string.IsNullOrEmpty(obj.description) ? string.Empty : obj.description;
                tfp.fk_tenant_id = userDetails.tenant_id;
                tfp.consequence = string.Empty;
                tfp.start_date = DateTime.UtcNow;
                tfp.action_type = 41;
                tfp.action_source = 0;
                tfp.line_order = 0;
                tfp.isManuallyAdded = 1;
                tfp.title = string.Empty;
                tfp.updated = DateTime.UtcNow;
                tfp.updated_by = userDetails.pk_id;
                tfp.fk_area_id = 0;
                tfp.tag = string.Empty;
                tfp.long_description = string.Empty;
                tfp.priority = obj.priority;
                tfp.display_financial_plan_flag = true;
                tfp.display_description_apendix_flag = false;
                tfp.fk_cat_id = Guid.Empty;
                tfp.financial_plan_description = string.Empty;
                tfp.consequence_flag = false;
                tfp.different_external_description_flag = false;
                tfp.change_text_flag = false;
                tfp.tags = obj.tag;
                tfp.monthly_report_flag = false;
                tfp.previous_pk_action_id = null;
                tfp.finished_date = DateTime.UtcNow.Date;
                tfp.display_cab_flag = true;
                tfp.display_zero_action = false;
                tfp.long_description_id = Guid.Empty;
                tfp.financial_plan_description_id = Guid.Empty;
                tfp.consequence_id = Guid.Empty;
                tfp.update_annual_budget = false;
                tfp.update_annual_budget_next_year = false;
                tfp.update_next_year_finplan = false;
                tfp.log_id = Guid.NewGuid();
                tfp.org_id = orgVersionContent.lstOrgHierarchy.FirstOrDefault().org_id_1;
                tfp.org_level = 1;
                budgetManagementDbContext.tfp_trans_header.Add(tfp);
                await budgetManagementDbContext.SaveChangesAsync();
                await _utility.LogFinplanActionChangesAsync(userDetails, obj.budgetYear, budgetManagementDbContext, tfp, obj.changeId, false, string.Empty);

                string freedim1 = string.Empty;
                string freedim2 = string.Empty;
                string freedim3 = string.Empty;
                string freedim4 = string.Empty;
                string adjustmentCode = string.Empty;

                try
                {
                    if (string.IsNullOrEmpty((string)defaultObj["freeDim1"]["fk_freedim_code"]))
                    {
                        freedim1 = (string)defaultObj["freeDim1"]["fk_freedim_code"];
                    }
                }
                catch (Exception)
                {
                    freedim1 = string.Empty;
                }

                try
                {
                    if (string.IsNullOrEmpty((string)defaultObj["freeDim2"]["fk_freedim_code"]))
                    {
                        freedim2 = (string)defaultObj["freeDim2"]["fk_freedim_code"];
                    }
                }
                catch (Exception)
                {
                    freedim2 = string.Empty;
                }

                try
                {
                    if (string.IsNullOrEmpty((string)defaultObj["freeDim3"]["fk_freedim_code"]))
                    {
                        freedim3 = (string)defaultObj["freeDim3"]["fk_freedim_code"];
                    }
                }
                catch (Exception)
                {
                    freedim3 = string.Empty;
                }

                try
                {
                    if (string.IsNullOrEmpty((string)defaultObj["freeDim4"]["fk_freedim_code"]))
                    {
                        freedim4 = (string)defaultObj["freeDim4"]["fk_freedim_code"];
                    }
                }
                catch (Exception)
                {
                    freedim4 = string.Empty;
                }

                try
                {
                    if (string.IsNullOrEmpty((string)defaultObj["adjustmentCode"]["value"]))
                    {
                        adjustmentCode = (string)defaultObj["adjustmentCode"]["value"];
                    }
                }
                catch (Exception)
                {
                    adjustmentCode = string.Empty;
                }

                tfp_trans_detail tfpDetail = new tfp_trans_detail();
                tfpDetail.fk_action_id = actionId;
                tfpDetail.fk_tenant_id = userDetails.tenant_id;
                tfpDetail.budget_year = obj.budgetYear;
                tfpDetail.fk_account_code = (string)defaultObj["account"]["accountValue"];
                tfpDetail.department_code = (string)defaultObj["department"]["departmentValue"];
                tfpDetail.function_code = (string)defaultObj["functionn"]["functionValue"];
                tfpDetail.project_code = (string)defaultObj["project"]["fk_project_code"];
                tfpDetail.asset_code = string.Empty;
                tfpDetail.fk_investment_id = 0;
                tfpDetail.investment_row_id = 0;
                tfpDetail.year_1_amount = obj.year1Amount * 1000;
                tfpDetail.year_2_amount = obj.year2Amount * 1000;
                tfpDetail.year_3_amount = obj.year3Amount * 1000;
                tfpDetail.year_4_amount = obj.year4Amount * 1000;
                tfpDetail.updated = DateTime.UtcNow;
                tfpDetail.updated_by = userDetails.pk_id;
                tfpDetail.fk_change_id = obj.changeId;
                tfpDetail.free_dim_1 = freedim1;
                tfpDetail.free_dim_2 = freedim2;
                tfpDetail.free_dim_3 = freedim3;
                tfpDetail.free_dim_4 = freedim4;
                tfpDetail.description = string.Empty;
                tfpDetail.fk_adjustment_code = adjustmentCode;
                tfpDetail.fk_alter_code = obj.alterCode;
                tfpDetail.previous_pk_id = null;
                tfpDetail.year_5_amount = 0;
                tfpDetail.fk_key_id = null;
                tfpDetail.fk_main_project_code = string.Empty;
                tfpDetail.inv_trans_id = Guid.Empty;
                tfpDetail.fk_adj_code = string.Empty;
                budgetManagementDbContext.tfp_trans_detail.Add(tfpDetail);
                await budgetManagementDbContext.SaveChangesAsync();
            }
            returnInfo.Add("result", "success");
            returnInfo.Add("message", "");
            return returnInfo;
        }

        public async Task<List<BListActionsBudman>> GetGridDataActionListAsync(string userId, int budgetYear, List<KeyValuePair> selectedColumns)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext budgetManagementDbContext = await _utility.GetTenantDBContextAsync();
            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));
            string budmanBlistActionsGridLevel1 = await _utility.GetParameterValueAsync(userId, "BUDMAN_GROUPING_L1");
            string budmanBlistActionsGridLevel2 = await _utility.GetParameterValueAsync(userId, "BUDMAN_GROUPING_L2");
            List<KeyValueData> lstActionTags = await GetActionTagsAsync(userId);

            int? changeId = await GetChangeIdAsync(userDetails.pk_id, userDetails.tenant_id, userDetails.client_id);

            List<int> lstActionTypes = new List<int>() { 31, 41, 9 };
            List<BListActionsBudman> dataset = await GetBaseActionDataSet(budgetYear, userDetails, budgetManagementDbContext, lstActionTypes);

            List<BListActionsBudman> bListActions = await GetBlistActions(budgetYear, userDetails, budgetManagementDbContext, dataset.Select(x => x.actionId).Distinct().ToList());

            var blistActionIDs = bListActions.Select(x => x.actionId).ToList();
            dataset = dataset.Where(p => !blistActionIDs.Contains(p.actionId)).ToList();
            var tcoServiceValues = await budgetManagementDbContext.tco_service_values.Where(x => x.fk_tenant_id == userDetails.tenant_id).ToListAsync();

            List<BListActionsBudman> modifiedDataset = FilterDataByOrgServiceId(orgVersionContent, budmanBlistActionsGridLevel1, budmanBlistActionsGridLevel2, dataset, tcoServiceValues);

            var changeIdDataSet = modifiedDataset.Where(x => x.changeId == changeId).ToList();
            List<string> col = selectedColumns != null ? selectedColumns.Where(x => x.isChecked == true).Select(x => x.key).ToList() : new List<string>();
            var groupedDataset = (from m in modifiedDataset
                                  group m by new
                                  {
                                      m.actionId,
                                      level1Id = col.Contains("level1Obj.key") ? m.level1Id : string.Empty,
                                      level1 = col.Contains("level1Obj.key") ? m.level1 : string.Empty,
                                      m.orgLevel,
                                      m.actionName,
                                      level2Id = col.Contains("level2Obj.key") ? m.level2Id : string.Empty,
                                      level2 = col.Contains("level2Obj.key") ? m.level2 : string.Empty,
                                      tags = col.Contains("tags") ? m.tags : string.Empty,
                                      alterCodeId = col.Contains("alterCodeObj.key") ? m.alterCodeId : string.Empty,
                                      alterCode = col.Contains("alterCodeObj.key") ? m.alterCode : string.Empty,
                                      priority = col.Contains("priorityObj.key") ? m.priority : string.Empty,
                                      m.actionType,
                                      m.tooltiptext
                                  } into g
                                  select new BListActionsBudman
                                  {
                                      actionId = g.Key.actionId,
                                      level1Id = g.Key.level1Id,
                                      level1 = g.Key.level1,
                                      orgLevel = g.Key.orgLevel,
                                      level2Id = g.Key.level2Id,
                                      level2 = g.Key.level2,
                                      actionName = g.Key.actionName,
                                      tags = g.Key.tags,
                                      alterCode = g.Key.alterCode,
                                      priority = g.Key.priority,
                                      year1Amount = g.Sum(x => x.year1Amount),
                                      year2Amount = g.Sum(x => x.year2Amount),
                                      year3Amount = g.Sum(x => x.year3Amount),
                                      year4Amount = g.Sum(x => x.year4Amount),
                                      changeyear1 = g.Where(x => x.actionId == g.Key.actionId && x.changeId == changeId).Sum(c => c.year1Amount),
                                      changeyear2 = g.Where(x => x.actionId == g.Key.actionId && x.changeId == changeId).Sum(c => c.year2Amount),
                                      changeyear3 = g.Where(x => x.actionId == g.Key.actionId && x.changeId == changeId).Sum(c => c.year3Amount),
                                      changeyear4 = g.Where(x => x.actionId == g.Key.actionId && x.changeId == changeId).Sum(c => c.year4Amount),
                                      actionType = g.Key.actionType,
                                      alterCodeId = g.Key.alterCodeId,
                                      tooltiptext = g.Key.tooltiptext
                                  }).ToList();

            var deletedActions = await (from a in budgetManagementDbContext.tfp_delete_header
                                        join b in budgetManagementDbContext.tfp_delete_detail on new { a = a.fk_tenant_id, b = a.pk_action_id }
                                                                                          equals new { a = b.fk_tenant_id, b = b.fk_action_id }
                                        where a.fk_tenant_id == userDetails.tenant_id
                                        && b.budget_year == budgetYear
                                        select new
                                        {
                                            id = a.fk_action_id
                                        }).ToListAsync();

            List<long> cActionsData = new List<long>();
            foreach (var c in deletedActions)
            {
                cActionsData.Add(Convert.ToInt64(c.id));
            }

            groupedDataset = groupedDataset.Where(x => !cActionsData.Contains(x.actionId)).ToList();

            groupedDataset = groupedDataset.OrderBy(x => x.level1Id).ToList();

            var level1Ids = groupedDataset.OrderBy(a => a.level1Id).Select(x => x.level1Id).Distinct().ToList();

            foreach (var level1Id in level1Ids)
            {
                groupedDataset.Add(new BListActionsBudman()
                {
                    level1Id = level1Id,
                    level1 = groupedDataset.FirstOrDefault(x => x.level1Id == level1Id).level1,
                    orgLevel = groupedDataset.FirstOrDefault(x => x.level1Id == level1Id).orgLevel,
                    level2Id = string.Empty,
                    level2 = string.Empty,
                    actionName = string.Empty,
                    tags = string.Empty,
                    alterCode = string.Empty,
                    alterCodeId = string.Empty,
                    priority = string.Empty,
                    year1Amount = groupedDataset.Where(x => x.level1Id == level1Id).Sum(s => s.year1Amount),
                    year2Amount = groupedDataset.Where(x => x.level1Id == level1Id).Sum(s => s.year2Amount),
                    year3Amount = groupedDataset.Where(x => x.level1Id == level1Id).Sum(s => s.year3Amount),
                    year4Amount = groupedDataset.Where(x => x.level1Id == level1Id).Sum(s => s.year4Amount),
                    changeyear1 = groupedDataset.Where(x => x.level1Id == level1Id).Sum(s => s.changeyear1),
                    changeyear2 = groupedDataset.Where(x => x.level1Id == level1Id).Sum(s => s.changeyear2),
                    changeyear3 = groupedDataset.Where(x => x.level1Id == level1Id).Sum(s => s.changeyear3),
                    changeyear4 = groupedDataset.Where(x => x.level1Id == level1Id).Sum(s => s.changeyear4),
                    isBold = true
                });
            }

            int id = 0;
            groupedDataset.ForEach(x =>
            {
                id++;
                x.pkId = id;

                if (!string.IsNullOrEmpty(x.tags))
                {
                    var TagIds = x.tags.Split(',').Select(int.Parse).ToList();
                    StringBuilder sb = new StringBuilder();
                    foreach (var tags in TagIds)
                    {
                        var firstOrDefault = lstActionTags.FirstOrDefault(l => l.KeyId == tags);
                        if (firstOrDefault != null)
                        {
                            sb.Append("<span class='bp-grey-tag'>" + firstOrDefault.ValueString + "</span>");
                            x.tagsLst.Add(firstOrDefault.ValueString);
                        }
                    }
                    x.tags = sb.ToString();
                }
                else
                {
                    x.tags = string.Empty;
                }
            });

            return groupedDataset;
        }

        private List<BListActionsBudman> HideDataWithAllZero(List<BListActionsBudman> groupedDataset)
        {
            return groupedDataset.Where(x => x.year1Amount != 0 || x.year2Amount != 0 || x.year3Amount != 0 || x.year4Amount != 0).ToList();
        }

        private async Task<int?> GetChangeIdAsync(int userId, int tenantId, int clientId)
        {
            TenantDBContext budgetManagementDbContext = await _utility.GetTenantDBContextAsync();

            int? changeId = await budgetManagementDbContext.tco_users_settings
                .Where(x => x.fk_user_id == userId && x.tenant_id == tenantId)
                .Select(x => x.active_change_id).FirstOrDefaultAsync();

            if (changeId != null)
            {
                int? activeChangeId = await budgetManagementDbContext.tfp_budget_changes.Where(x => x.pk_change_id == changeId && x.org_budget_flag == 1).Select(c => c.pk_change_id).FirstOrDefaultAsync();
                changeId = activeChangeId != null ? activeChangeId : -1;
            }
            else
            {
                changeId = -1;
            }
            return changeId;
        }

        private List<BListActionsBudman> FilterDataByOrgServiceId(ClsOrgVersionSpecificContent orgVersionContent, string budmanBlistActionsGridLevel1, string budmanBlistActionsGridLevel2, List<BListActionsBudman> dataset, List<tco_service_values> tcoServiceValues)
        {
            List<BListActionsBudman> modifiedDataset = null;

            /*org & org*/
            if (!string.IsNullOrEmpty(budmanBlistActionsGridLevel1)
                && !string.IsNullOrEmpty(budmanBlistActionsGridLevel2)
                && budmanBlistActionsGridLevel1.StartsWith("org")
                && budmanBlistActionsGridLevel2.StartsWith("org"))
            {
                modifiedDataset = DisplayOrgAndOrg(orgVersionContent, dataset, budmanBlistActionsGridLevel1, budmanBlistActionsGridLevel2);
            }

            /*org & service*/
            if (!string.IsNullOrEmpty(budmanBlistActionsGridLevel1)
                && !string.IsNullOrEmpty(budmanBlistActionsGridLevel2)
                && budmanBlistActionsGridLevel1.StartsWith("org")
                && budmanBlistActionsGridLevel2.StartsWith("service"))
            {
                modifiedDataset = DisplayOrgAndService(orgVersionContent, dataset, budmanBlistActionsGridLevel1, budmanBlistActionsGridLevel2, tcoServiceValues);
            }

            /*org & empty*/
            if (!string.IsNullOrEmpty(budmanBlistActionsGridLevel1)
                && string.IsNullOrEmpty(budmanBlistActionsGridLevel2)
                && budmanBlistActionsGridLevel1.StartsWith("org"))
            {
                modifiedDataset = DisplayOrgAndEmpty(orgVersionContent, dataset, budmanBlistActionsGridLevel1);
            }

            /*service & org*/
            if (!string.IsNullOrEmpty(budmanBlistActionsGridLevel1)
                && !string.IsNullOrEmpty(budmanBlistActionsGridLevel2)
                && budmanBlistActionsGridLevel1.StartsWith("service")
                && budmanBlistActionsGridLevel2.StartsWith("org"))
            {
                modifiedDataset = DisplayServiceAndOrg(orgVersionContent, dataset, budmanBlistActionsGridLevel1, budmanBlistActionsGridLevel2, tcoServiceValues);
            }

            /*no set up*/
            if (string.IsNullOrEmpty(budmanBlistActionsGridLevel1)
                && string.IsNullOrEmpty(budmanBlistActionsGridLevel2))
            {
                modifiedDataset = DisplayDefaultSetUp(orgVersionContent, dataset);
            }

            return modifiedDataset;
        }

        private static async Task<List<BListActionsBudman>> GetBaseActionDataSet(int budgetYear, UserData userDetails, TenantDBContext budgetManagementDbContext, List<int> lstActionTypes)
        {
            return await (from a in budgetManagementDbContext.tfp_trans_header
                          join b in budgetManagementDbContext.tfp_trans_detail on new { a = a.fk_tenant_id, b = a.pk_action_id }
                                                                           equals new { a = b.fk_tenant_id, b = b.fk_action_id }
                          join c in budgetManagementDbContext.tco_fp_alter_codes on new { a = b.fk_tenant_id, b = b.fk_alter_code }
                                                                             equals new { a = c.fk_tenant_id, b = c.pk_alter_code } into c1
                          from c2 in c1.DefaultIfEmpty()
                          where a.fk_tenant_id == userDetails.tenant_id
                          && b.budget_year == budgetYear
                          && lstActionTypes.Contains(a.action_type)
                          select new BListActionsBudman
                          {
                              actionId = a.pk_action_id,
                              actionName = a.description,
                              departmentCode = b.department_code,
                              functionCode = b.function_code,
                              tags = a.tags,
                              alterCode = (c2 == null || string.IsNullOrEmpty(c2.alter_description)) ? b.fk_alter_code : (b.fk_alter_code + "-" + c2.alter_description),
                              priority = a.priority.HasValue ? a.priority.Value.ToString() : string.Empty,
                              year1Amount = b.year_1_amount / 1000,
                              year2Amount = b.year_2_amount / 1000,
                              year3Amount = b.year_3_amount / 1000,
                              year4Amount = b.year_4_amount / 1000,
                              changeId = b.fk_change_id,
                              actionType = a.action_type,
                              alterCodeId = b.fk_alter_code,
                              tooltiptext = string.IsNullOrEmpty(a.long_description) ? string.Empty : a.long_description
                          }).ToListAsync();
        }

        private static async Task<List<BListActionsBudman>> GetBlistActions(int budgetYear, UserData userDetails, TenantDBContext budgetManagementDbContext, List<long> lstAtions)
        {
            return await (from a in budgetManagementDbContext.tfp_temp_header
                          join b in budgetManagementDbContext.tfp_temp_detail on new { a = a.fk_tenant_id, b = a.pk_temp_id }
                                                                          equals new { a = b.fk_tenant_id, b = b.fk_temp_id }
                          where a.fk_tenant_id == userDetails.tenant_id
                          && b.budget_year == budgetYear
                          && lstAtions.Contains(a.fk_action_id)
                          select new BListActionsBudman
                          {
                              actionId = a.fk_action_id,
                          }).ToListAsync();
        }

        private List<BListActionsBudman> DisplayOrgAndOrg(ClsOrgVersionSpecificContent orgVersionContent, List<BListActionsBudman> dataset, string budmanBlistActionsGridLevel1, string budmanBlistActionsGridLevel2)
        {
            if (budmanBlistActionsGridLevel1.ToLower() == "org_id_2" && budmanBlistActionsGridLevel2.ToLower() == "org_id_3")
            {
                var modifiedDataset = (from a in dataset
                                       join b in orgVersionContent.lstOrgHierarchy on a.departmentCode equals b.fk_department_code
                                       join c in orgVersionContent.lstOrgHierarchy on a.departmentCode equals c.fk_department_code
                                       select new BListActionsBudman
                                       {
                                           actionId = a.actionId,
                                           level1Id = b.org_id_2.Trim(),
                                           level1 = $"{b.org_id_2.Trim()}-{b.org_name_2.Trim()}",
                                           orgLevel = 2,
                                           actionName = a.actionName,
                                           tags = a.tags,
                                           level2Id = b.org_id_3.Trim(),
                                           level2 = $"{c.org_id_3.Trim()}-{c.org_name_3.Trim()}",
                                           alterCode = a.alterCode,
                                           priority = a.priority,
                                           year1Amount = a.year1Amount,
                                           year2Amount = a.year2Amount,
                                           year3Amount = a.year3Amount,
                                           year4Amount = a.year4Amount,
                                           changeId = a.changeId,
                                           actionType = a.actionType,
                                           alterCodeId = a.alterCodeId,
                                           tooltiptext = a.tooltiptext
                                       }).ToList();
                return modifiedDataset;
            }
            else
            {
                return new List<BListActionsBudman>();
            }
        }

        private List<BListActionsBudman> DisplayOrgAndService(ClsOrgVersionSpecificContent orgVersionContent, List<BListActionsBudman> dataset, string budmanBlistActionsGridLevel1, string budmanBlistActionsGridLevel2, List<tco_service_values> tcoServiceValues)
        {
            if (budmanBlistActionsGridLevel1.ToLower() == "org_id_2" && budmanBlistActionsGridLevel2.ToLower() == "service_id_2")
            {
                var modifiedDataset = (from a in dataset
                                       join b in orgVersionContent.lstOrgHierarchy on a.departmentCode equals b.fk_department_code
                                       join c in tcoServiceValues on a.functionCode equals c.fk_function_code
                                       select new BListActionsBudman
                                       {
                                           actionId = a.actionId,
                                           level1Id = b.org_id_2.Trim(),
                                           level1 = $"{b.org_id_2.Trim()}-{b.org_name_2.Trim()}",
                                           orgLevel = 2,
                                           actionName = a.actionName,
                                           tags = a.tags,
                                           level2Id = c.service_id_2.Trim(),
                                           level2 = $"{c.service_id_2.Trim()}-{c.service_name_2.Trim()}",
                                           alterCode = a.alterCode,
                                           priority = a.priority,
                                           year1Amount = a.year1Amount,
                                           year2Amount = a.year2Amount,
                                           year3Amount = a.year3Amount,
                                           year4Amount = a.year4Amount,
                                           changeId = a.changeId,
                                           actionType = a.actionType,
                                           alterCodeId = a.alterCodeId,
                                           tooltiptext = a.tooltiptext,
                                       }).ToList();
                return modifiedDataset;
            }
            else
            {
                return new List<BListActionsBudman>();
            }
        }

        private List<BListActionsBudman> DisplayOrgAndEmpty(ClsOrgVersionSpecificContent orgVersionContent, List<BListActionsBudman> dataset, string budmanBlistActionsGridLevel1)
        {
            if (budmanBlistActionsGridLevel1.ToLower() == "org_id_2")
            {
                var modifiedDataset = (from a in dataset
                                       join b in orgVersionContent.lstOrgHierarchy on a.departmentCode equals b.fk_department_code
                                       select new BListActionsBudman
                                       {
                                           actionId = a.actionId,
                                           level1Id = b.org_id_2.Trim(),
                                           level1 = $"{b.org_id_2.Trim()}-{b.org_name_2.Trim()}",
                                           orgLevel = 2,
                                           actionName = a.actionName,
                                           tags = a.tags,
                                           level2Id = string.Empty,
                                           level2 = string.Empty,
                                           alterCode = a.alterCode,
                                           priority = a.priority,
                                           year1Amount = a.year1Amount,
                                           year2Amount = a.year2Amount,
                                           year3Amount = a.year3Amount,
                                           year4Amount = a.year4Amount,
                                           changeId = a.changeId,
                                           actionType = a.actionType,
                                           alterCodeId = a.alterCodeId,
                                           tooltiptext = a.tooltiptext,
                                       }).ToList();
                return modifiedDataset;
            }
            else
            {
                return new List<BListActionsBudman>();
            }
        }

        private List<BListActionsBudman> DisplayServiceAndOrg(ClsOrgVersionSpecificContent orgVersionContent, List<BListActionsBudman> dataset, string budmanBlistActionsGridLevel1, string budmanBlistActionsGridLevel2, List<tco_service_values> tcoServiceValues)
        {
            if (budmanBlistActionsGridLevel1.ToLower() == "service_id_2" && budmanBlistActionsGridLevel2.ToLower() == "org_id_2")
            {
                var modifiedDataset = (from a in dataset
                                       join c in tcoServiceValues on a.functionCode equals c.fk_function_code
                                       join b in orgVersionContent.lstOrgHierarchy on a.departmentCode equals b.fk_department_code
                                       select new BListActionsBudman
                                       {
                                           actionId = a.actionId,
                                           level1Id = c.service_id_2.Trim(),
                                           level1 = $"{c.service_id_2.Trim()}-{c.service_name_2.Trim()}",
                                           actionName = a.actionName,
                                           tags = a.tags,
                                           level2Id = b.org_id_2.Trim(),
                                           level2 = $"{b.org_id_2.Trim()}-{b.org_name_2.Trim()}",
                                           alterCode = a.alterCode,
                                           priority = a.priority,
                                           year1Amount = a.year1Amount,
                                           year2Amount = a.year2Amount,
                                           year3Amount = a.year3Amount,
                                           year4Amount = a.year4Amount,
                                           changeId = a.changeId,
                                           actionType = a.actionType,
                                           alterCodeId = a.alterCodeId,
                                           tooltiptext = a.tooltiptext
                                       }).ToList();
                return modifiedDataset;
            }
            else
            {
                return new List<BListActionsBudman>();
            }
        }

        private List<BListActionsBudman> DisplayDefaultSetUp(ClsOrgVersionSpecificContent orgVersionContent, List<BListActionsBudman> dataset)
        {
            var modifiedDataset = (from a in dataset
                                   join b in orgVersionContent.lstOrgHierarchy on a.departmentCode equals b.fk_department_code
                                   select new BListActionsBudman
                                   {
                                       actionId = a.actionId,
                                       level1Id = b.org_id_2.Trim(),
                                       level1 = $"{b.org_id_2.Trim()}-{b.org_name_2.Trim()}",
                                       orgLevel = 2,
                                       actionName = a.actionName,
                                       tags = a.tags,
                                       level2Id = string.Empty,
                                       level2 = string.Empty,
                                       alterCode = a.alterCode,
                                       priority = a.priority,
                                       year1Amount = a.year1Amount,
                                       year2Amount = a.year2Amount,
                                       year3Amount = a.year3Amount,
                                       year4Amount = a.year4Amount,
                                       changeId = a.changeId,
                                       actionType = a.actionType,
                                       alterCodeId = a.alterCodeId,
                                       tooltiptext = a.tooltiptext
                                   }).ToList();
            return modifiedDataset;
        }

        private List<LevelIds> GetLevelIdOrgAndOrg(ClsOrgVersionSpecificContent orgVersionContent, string budmanBlistActionsGridLevel1, string budmanBlistActionsGridLevel2)
        {
            if (budmanBlistActionsGridLevel1.ToLower() == "org_id_2" && budmanBlistActionsGridLevel2.ToLower() == "org_id_3")
            {
                List<LevelIds> LevelIdDropDown = null;
                LevelIdDropDown = (from a in orgVersionContent.lstOrgHierarchy
                                   select new LevelIds
                                   {
                                       level1Id = a.org_id_2.Trim(),
                                       level1 = $"{a.org_id_2.Trim()}-{a.org_name_2.Trim()}",
                                       level2Id = a.org_id_3.Trim(),
                                       level2 = $"{a.org_id_3.Trim()}-{a.org_name_3.Trim()}",
                                   }).ToList();
                return LevelIdDropDown;
            }
            else
            {
                return new List<LevelIds>();
            }
        }

        private async Task<List<LevelIds>> GetLevelIdOrgAndServiceAsync(ClsOrgVersionSpecificContent orgVersionContent, string budmanBlistActionsGridLevel1, string budmanBlistActionsGridLevel2, string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext budgetManagementDbContext = await _utility.GetTenantDBContextAsync();
            var tcoServiceValues = await budgetManagementDbContext.tco_service_values.Where(x => x.fk_tenant_id == userDetails.tenant_id).ToListAsync();
            if (budmanBlistActionsGridLevel1.ToLower() == "org_id_2" && budmanBlistActionsGridLevel2.ToLower() == "service_id_2")
            {
                List<LevelIds> LevelIdDropDown = null;
                LevelIdDropDown = (from b in orgVersionContent.lstOrgHierarchy
                                   from c in tcoServiceValues
                                   select new LevelIds
                                   {
                                       level1Id = b.org_id_2.Trim(),
                                       level1 = $"{b.org_id_2.Trim()}-{b.org_name_2.Trim()}",
                                       level2Id = c.service_id_2.Trim(),
                                       level2 = $"{c.service_id_2.Trim()}-{c.service_name_2.Trim()}",
                                   }).ToList();
                return LevelIdDropDown;
            }
            else
            {
                return new List<LevelIds>();
            }
        }

        private List<LevelIds> GetLevelIdOrgAndEmpty(ClsOrgVersionSpecificContent orgVersionContent, string budmanBlistActionsGridLevel1)
        {
            if (budmanBlistActionsGridLevel1.ToLower() == "org_id_2")
            {
                List<LevelIds> LevelIdDropDown = null;
                LevelIdDropDown = (from b in orgVersionContent.lstOrgHierarchy
                                   select new LevelIds
                                   {
                                       level1Id = b.org_id_2.Trim(),
                                       level1 = $"{b.org_id_2.Trim()}-{b.org_name_2.Trim()}",
                                       level2Id = string.Empty,
                                       level2 = string.Empty,
                                   }).ToList();
                return LevelIdDropDown;
            }
            else
            {
                return new List<LevelIds>();
            }
        }

        private async Task<List<LevelIds>> GetLevelIdServiceAndOrgAsync(ClsOrgVersionSpecificContent orgVersionContent, string budmanBlistActionsGridLevel1, string budmanBlistActionsGridLevel2, string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext budgetManagementDbContext = await _utility.GetTenantDBContextAsync();
            var tcoServiceValues = await budgetManagementDbContext.tco_service_values.Where(x => x.fk_tenant_id == userDetails.tenant_id).ToListAsync();
            if (budmanBlistActionsGridLevel1.ToLower() == "service_id_2" && budmanBlistActionsGridLevel2.ToLower() == "org_id_2")
            {
                List<LevelIds> LevelIdDropDown = null;
                LevelIdDropDown = (from c in tcoServiceValues
                                   from b in orgVersionContent.lstOrgHierarchy
                                   select new LevelIds
                                   {
                                       level1Id = c.service_id_2.Trim(),
                                       level1 = $"{c.service_id_2.Trim()}-{c.service_name_2.Trim()}",
                                       level2Id = b.org_id_2.Trim(),
                                       level2 = $"{b.org_id_2.Trim()}-{b.org_name_2.Trim()}",
                                   }).ToList();
                return LevelIdDropDown;
            }
            else
            {
                return new List<LevelIds>();
            }
        }

        private List<LevelIds> GetLevelIdDefaultSetUp(ClsOrgVersionSpecificContent orgVersionContent)
        {
            List<LevelIds> LevelIdDropDown = null;
            LevelIdDropDown = (from b in orgVersionContent.lstOrgHierarchy
                               select new LevelIds
                               {
                                   level1Id = b.org_id_2.Trim(),
                                   level1 = $"{b.org_id_2.Trim()}-{b.org_name_2.Trim()}",
                                   level2Id = string.Empty,
                                   level2 = string.Empty,
                               }).ToList();
            return LevelIdDropDown;
        }

        private async Task<List<KeyValueData>> GetActionTagsAsync(string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            var actionTags = await (from atg in tenantDbContext.tcoActionTags
                                    where atg.FkTenantId == userDetails.tenant_id
                                    select new KeyValueData
                                    {
                                        KeyId = atg.PkId,
                                        ValueString = atg.TagDescription
                                    }).ToListAsync();
            return actionTags;
        }

        private async Task<IEnumerable<ImageNode>> GetPlanPublishChaptersAsync(string userId, string planId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            tpl_plan planData = await GetPlanbyIdAsync(userDetails.tenant_id, Guid.Parse(planId));
            var pubConfig = await GetPublishConfigAsync(userDetails.tenant_id, planData.publish_template_id);
            if (pubConfig == null)
            {
                throw new InvalidOperationException("Publish config does not exist");
            }

            IEnumerable<string> blobNames = await _blobHelper.GetListOfBlobsAsync(StorageAccount.AppStorage, BlobContainers.PublishImages, pubConfig.stg_publish_url);
            List<ImageNode> imageNodes = new List<ImageNode>();

            // Home banner
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetManagement");
            ImageNode homeNode = new ImageNode()
            {
                Name = (langStrings["web_home_image"]).LangText,
                Id = "home",
                HasImage = _utility.NodeHasImage(blobNames, "home"),
                Path = "homebanner/home"
            };

            imageNodes.Add(homeNode);

            // Logo
            var userRoles = (await _utility.GetUserRolesAsync(userId)).ToList();
            ImageNode logoNode = new ImageNode()
            {
                Name = (langStrings["web_logo"]).LangText,
                Id = "logo",
                HasImage = _utility.NodeHasImage(blobNames, "logo"),
                Path = "logo",
                Disabled = false,
            };
            imageNodes.Add(logoNode);
            logoNode = new ImageNode()
            {
                Name = (langStrings["web_logo_menu"]).LangText,
                Id = "logo_menu",
                HasImage = _utility.NodeHasImage(blobNames, "logo_menu"),
                Path = "logo_menu",
                Disabled = false
            };
            imageNodes.Add(logoNode);
            //Facebook Image
            ImageNode facebookNode = new ImageNode()
            {
                Name = $"{langStrings["web_facebook"].LangText} {langStrings["web_fb_image_info"].LangText}",
                Id = "facebook-image",
                HasImage = _utility.NodeHasImage(blobNames, "facebook-image"),
                Path = "facebook-image"
            };
            imageNodes.Add(facebookNode);

            // Main Chapters
            var mainChapters = planData.tpl_plan_template_details.Where(x => x.status.Value && x.parent_node_id == Guid.Empty).OrderBy(z => z.node_order).ToList();
            foreach (var page in mainChapters)
            {
                ImageNode node = new ImageNode
                {
                    Id = page.node_id.ToString(),
                    Name = page.node_name,
                    HasImage = _utility.NodeHasImage(blobNames, page.node_id.ToString()),
                    Path = $"summary/{page.node_id.ToString()}"
                };
                imageNodes.Add(node);
            }

            return imageNodes;
        }

        private async Task<tpl_plan> GetPlanbyIdAsync(int tenantId, Guid planId)
        {
            var tenantDbContext = await _utility.GetTenantDBContextAsync();
            return await tenantDbContext.tpl_plan.Where(x => x.fk_tenant_id == tenantId && x.pk_plan_id == planId).Include(x => x.tpl_plan_participants)
                .Include(x => x.tpl_plan_template_details).FirstOrDefaultAsync();
        }

        private tco_publish_config GetPublishConfig(int tenantId, int templateId)
        {
            var publishConfig = GetPublishConfigAsync(tenantId, templateId).GetAwaiter().GetResult();
            return publishConfig;
        }

        public dynamic GetYearlyBudgetB3Data(string userId, bool divideByMillions, List<YearlybudetB3Helper> result)
        {
            return GetYearlyBudgetB3DataAsync(userId, divideByMillions, result).GetAwaiter().GetResult();
        }

        public async Task<dynamic> GetYearlyBudgetB3DataAsync(string userId, bool divideByMillions, List<YearlybudetB3Helper> result)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            Dictionary<string, clsLanguageString> langStringValuesBAtype = new Dictionary<string, clsLanguageString>();
            langStringValuesBAtype = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetManagement");

            ///////////Get distinct line_group_id frm result////////////////
            var linegrpids = result.Select(x => x.line_group_id).Distinct().ToList();
            dynamic group = new JObject();
            dynamic jsonData = null;
            dynamic jsonData1 = new JArray();
            dynamic jfinalresult = new JArray();
            dynamic JResult = new JObject();
            decimal? sumlegendyr100 = 0, sumbdtyr100 = 0, sumyr1100 = 0;
            decimal? sumlegendyr300 = 0, sumbdtyr300 = 0, sumyr1300 = 0;
            decimal? sumlegendyr400 = 0, sumbdtyr400 = 0, sumyr1400 = 0;
            decimal? sumlegendyrbrutto = 0, sumbdtyrbrutto = 0, sumyr1brutto = 0;
            decimal? sumlegendyrresultat = 0, sumbdtyrresultat = 0, sumyr1resultat = 0;
            decimal? sumlegendyr500 = 0, sumbdtyr500 = 0, sumyr1500 = 0;
            decimal? sumlegendyrnetto = 0, sumbdtyrnetto = 0, sumyr1netto = 0;
            decimal? sumlegendyr600 = 0, sumbdtyr600 = 0, sumyr1600 = 0;
            decimal? sumlegendyr700 = 0, sumbdtyr700 = 0, sumyr1700 = 0;

            ///if line item 50 is not found
            if (!linegrpids.Contains(50))
            {
                if (linegrpids.Count() >= 4)
                {
                    linegrpids.Insert(4, 50);
                }
                else
                {
                    linegrpids.Insert(linegrpids.Count(), 50);
                }
            }

            foreach (var item in linegrpids)
            {
                decimal? legendtotal = 0, budgetyeartotal = 0, year1total = 0;
                var res = result.Where(x => x.line_group_id == item).Distinct().ToList();
                jsonData = new JArray();
                dynamic JGetResult = new JObject();
                if (res.Count > 0)
                {
                    JGetResult.Add("title", res.First().line_group.ToString());
                    foreach (var i in res)
                    {
                        group = new JObject();
                        group.line_group_id = item.ToString();
                        group.line_group_type = "data";
                        group.line_item = i.line_item;
                        group.ledgeryear = i.ledgeryear;
                        group.currentbudgetyear = i.currentbudgetyear;
                        group.Year1 = i.Year1;
                        jsonData.Add(group);
                    }
                    ///Total
                    foreach (var r in res)
                    {
                        legendtotal = legendtotal + r.ledgeryear;
                        budgetyeartotal = budgetyeartotal + r.currentbudgetyear;
                        year1total = year1total + r.Year1;
                    }

                    string totaltext = "";
                    
                    string langstring = "SUM_TOTAL_B3_" + item;
                    if (langStringValuesBAtype.ContainsKey(langstring))
                    {
                        totaltext = (langStringValuesBAtype[langstring]).LangText;
                    }
                    else
                    {
                        totaltext = "Error in inserting sum text for B3 " + item;
                    }
                    
                    dynamic TotalObject = new JObject();
                    TotalObject.line_group_id = item.ToString();
                    TotalObject.line_group_type = "total";
                    TotalObject.line_item = totaltext;
                    TotalObject.ledgeryear = legendtotal;
                    TotalObject.currentbudgetyear = budgetyeartotal;
                    TotalObject.Year1 = year1total;
                    jsonData.Add(TotalObject);
                }

                if (item == 10)
                {
                    sumlegendyr100 = legendtotal;
                    sumbdtyr100 = budgetyeartotal;
                    sumyr1100 = year1total;
                }
                if (item == 20)
                {
                    sumlegendyrbrutto = sumlegendyr100 + legendtotal;
                    sumbdtyrbrutto = sumbdtyr100 + budgetyeartotal;
                    sumyr1brutto = sumyr1100 + year1total;
                    dynamic BruttoObject = new JObject();
                    BruttoObject.line_group_id = "20";
                    BruttoObject.line_group_type = "newsum";
                    BruttoObject.line_item = (langStringValuesBAtype["BM_Brutto_B3"]).LangText;
                    BruttoObject.ledgeryear = sumlegendyrbrutto;
                    BruttoObject.currentbudgetyear = sumbdtyrbrutto;
                    BruttoObject.Year1 = sumyr1brutto;
                    jsonData.Add(BruttoObject);
                }
                if (item == 30)
                {
                    sumlegendyr300 = legendtotal;
                    sumbdtyr300 = budgetyeartotal;
                    sumyr1300 = year1total;
                }
                if (item == 40)
                {
                    sumlegendyr400 = legendtotal;
                    sumbdtyr400 = budgetyeartotal;
                    sumyr1400 = year1total;

                    sumlegendyrresultat = sumlegendyr300 + sumlegendyr400;
                    sumbdtyrresultat = sumbdtyr300 + sumbdtyr400;
                    sumyr1resultat = sumyr1300 + sumyr1400;

                    dynamic NewObject = new JObject();
                    NewObject.line_group_id = "40";
                    NewObject.line_group_type = "newsum";
                    NewObject.line_item = (langStringValuesBAtype["BM_BrutResultat_B3"]).LangText;
                    NewObject.ledgeryear = sumlegendyrresultat;
                    NewObject.currentbudgetyear = sumbdtyrresultat;
                    NewObject.Year1 = sumyr1resultat;
                    jsonData.Add(NewObject);
                }

                if (item == 50)
                {
                    if (res.Count == 0)
                    {
                        JGetResult.Add("title", "");
                    }
                    sumlegendyr500 = legendtotal;
                    sumbdtyr500 = budgetyeartotal;
                    sumyr1500 = year1total;

                    sumlegendyrnetto = sumlegendyrbrutto + sumlegendyrresultat + sumlegendyr500;
                    sumbdtyrnetto = sumbdtyrbrutto + sumbdtyrresultat + sumbdtyr500;
                    sumyr1netto = sumyr1brutto + sumyr1resultat + sumyr1500;

                    dynamic NewObject = new JObject();
                    NewObject.line_group_id = "50";
                    NewObject.line_group_type = "newsum";
                    NewObject.line_item = (langStringValuesBAtype["BM_Netto_B3"]).LangText;
                    NewObject.ledgeryear = sumlegendyrnetto;
                    NewObject.currentbudgetyear = sumbdtyrnetto;
                    NewObject.Year1 = sumyr1netto;
                    jsonData.Add(NewObject);
                }

                if (item == 60)
                {
                    sumlegendyr600 = legendtotal;
                    sumbdtyr600 = budgetyeartotal;
                    sumyr1600 = year1total;
                }
                if (item == 70)
                {
                    sumlegendyr700 = legendtotal;
                    sumbdtyr700 = budgetyeartotal;
                    sumyr1700 = year1total;

                    dynamic NewObject = new JObject();
                    NewObject.line_group_id = "70";
                    NewObject.line_group_type = "newsum";
                    NewObject.line_item = (langStringValuesBAtype["BM_Regnskapsmessig_B3"]).LangText;
                    NewObject.ledgeryear = sumlegendyrnetto + sumlegendyr600 + sumlegendyr700;
                    NewObject.currentbudgetyear = sumbdtyrnetto + sumbdtyr600 + sumbdtyr700;
                    NewObject.Year1 = sumyr1netto + sumyr1600 + sumyr1700;
                    jsonData.Add(NewObject);
                }
                
                jsonData1.Add(jsonData);
                JGetResult.Add("Data", jsonData);
                jfinalresult.Add(JGetResult);
            }
            JResult.Add("result", jfinalresult);

            return JResult;
        }

        public PublishTreeNode AddCustomNodeForMasterTemplate(string userId, string id, string type, string text, string internalDesc = "")
        {
            PublishTreeNode treeNode = new PublishTreeNode
            {
                id = id,
                text = text,
                type = type,
                expanded = false,
                @checked = true,
                isEditableNode = true,
                isAdmin = true
            };
            if (!string.IsNullOrEmpty(internalDesc))
            {
                treeNode.parameters = new Dictionary<string, string> { { "internalDesc", internalDesc } };
            }
            return treeNode;
        }

        public async Task CreateCustomNodeAsync(string userId, int budgetYear, PublishTreeNode customNode, BudgetPhaseTree budgetPhaseTree)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            int docConfig = await GetDocumentConfigurationAsync(userId);

            List<clsOrgIdsAndServiceIds> orgSerIds = await _consequentAdjustedBudget.GetOrgIdsAndServiceIdsAsync(userId, false, budgetYear);
            List<clsOrgIdsAndServiceIds> orgIds = orgSerIds.Where(x => x.parentId == null).OrderBy(x => x.orgId).ToList();
            List<string> budgetPhaseList = budgetPhaseTree?.items.Where(item => item.isChecked)
                                                                 .Select(item => item.key)
                                                                 .ToList() ?? new List<string>();

            TcoPublishTemplate template = await dbContext.TcoPublishTemplate.FirstOrDefaultAsync(x => x.FkTenantId == userDetails.tenant_id &&
                               x.TreeType.Equals(PublishTreeType.BMMasterTemplate.ToString()) && x.BudgetYear == budgetYear);
            List<tco_custom_node_master> cnInstances = await dbContext.tco_custom_node_master.Where(x => x.fk_pub_temp_id == template.PkId).ToListAsync();
            List<tco_custom_node_budphase_mapping> budPhaseMapping = await dbContext.tco_custom_node_budphase_mapping.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear).ToListAsync();
            string internalDesc = customNode.parameters?.GetValueOrDefault("internalDesc", string.Empty);

            List<tco_custom_node_master> cnLinksToBeCreated = new List<tco_custom_node_master>();
            List<tco_custom_node_budphase_mapping> cnBudPhaseMapping = new List<tco_custom_node_budphase_mapping>();
            List<TcoCustomNode> cnToBeCreated = new List<TcoCustomNode>();

            if (docConfig == 1)
            {
                foreach (var orgId in orgIds)
                {
                    ProcessNodes(orgId.orgId, string.Empty, customNode, budgetYear, userDetails, template, budgetPhaseList, cnInstances, budPhaseMapping, cnLinksToBeCreated, cnBudPhaseMapping, cnToBeCreated, internalDesc);
                }
            }
            else if (docConfig == 2)
            {
                List<string> serviceIds = orgSerIds.Where(x => x.parentId != null).Select(x => x.orgId).Distinct().OrderBy(x => x).ToList();
                foreach (var serId in serviceIds)
                {
                    ProcessNodes(serId, string.Empty, customNode, budgetYear, userDetails, template, budgetPhaseList, cnInstances, budPhaseMapping, cnLinksToBeCreated, cnBudPhaseMapping, cnToBeCreated, internalDesc);
                }
            }
            else
            {
                foreach (var orgId in orgIds)
                {
                    ProcessNodes(orgId.orgId, string.Empty, customNode, budgetYear, userDetails, template, budgetPhaseList, cnInstances, budPhaseMapping, cnLinksToBeCreated, cnBudPhaseMapping, cnToBeCreated, internalDesc);
                    var serviceIds = orgSerIds.Where(x => x.parentId == orgId.id.ToString() && !string.IsNullOrEmpty(x.orgId))
                                              .Select(y => y.orgId).Distinct().ToList();
                    foreach (var serId in serviceIds)
                    {
                        ProcessNodes(orgId.orgId, serId, customNode, budgetYear, userDetails, template, budgetPhaseList, cnInstances, budPhaseMapping, cnLinksToBeCreated, cnBudPhaseMapping, cnToBeCreated, internalDesc);
                    }
                }
            }

            await Task.WhenAll(
                dbContext.tco_custom_node_master.AddRangeAsync(cnLinksToBeCreated),
                dbContext.TcoCustomNode.AddRangeAsync(cnToBeCreated),
                dbContext.tco_custom_node_budphase_mapping.AddRangeAsync(cnBudPhaseMapping)
            );
            await dbContext.BulkSaveChangesAsync();
        }

        public async Task CreateCustomNodeInstancesAsync(string userId, PublishTemplateHelper templateData)
        {
            //determine doc config type
            //create instances of custom nodes
            //update custom node - master template mapping table
            int docConfig = await GetDocumentConfigurationAsync(userId);
            switch (docConfig)
            {
                case 1:
                    await CreateNodesForConfig1Async(userId, templateData);
                    break;

                case 2:
                    await CreateNodesForConfig2Async(userId, templateData);
                    break;

                default:
                    await CreateNodesForConfig3Async(userId, templateData);
                    break;
            }
        }

        public async Task<PublishTreeNode> SaveBMMasterTemplateWidgetListNodeAndConfiguration(string userId, MasterTemplateWidgetNodeHelper widgetNodeData)
        {
            tco_widget_node_master widgetNodeMasterData = new tco_widget_node_master();
            int docConfig = await GetDocumentConfigurationAsync(userId);
            switch (docConfig)
            {
                case 1:
                    widgetNodeMasterData = await CreateBMMasterTemplateWidgetListNodesForConfig1(userId, widgetNodeData);
                    break;

                case 2:
                    widgetNodeMasterData = await CreateBMMasterTemplateWidgetListNodesForConfig2(userId, widgetNodeData);
                    break;

                default:
                    widgetNodeMasterData = await CreateBMMasterTemplateWidgetListNodesForConfig3(userId, widgetNodeData);
                    break;
            }

            PublishTreeNode treeNode = new PublishTreeNode
            {
                id = widgetNodeMasterData.pk_node_master_id.ToString(),
                text = widgetNodeMasterData.node_title,
                type = BM_Tree_Types.reportingWidget.ToString(),
                expanded = false,
                @checked = true,
                isEditableNode = true,
                isAdmin = true
            };
            return treeNode;
        }

        private async Task<IEnumerable<ServiceAreaBudget>> GetBudgetLimit(UserData userDetails, List<int> ActionTypes, int BudgetYear)
        {
            int budgetYear = BudgetYear;
            TenantDBContext budgetManagementDBContext = await _utility.GetTenantDBContextAsync();
            List<ServiceAreaBudget> serviceAreaBudget = new List<ServiceAreaBudget>();
            serviceAreaBudget = await (from bl in budgetManagementDBContext.tfp_budget_limits
                                       where bl.budget_year == budgetYear && bl.fk_tenant_id == userDetails.tenant_id && ActionTypes.Contains(bl.action_type)//bl.action_type == ActionType
                                       select new ServiceAreaBudget
                                       {
                                           orgLevel1 = bl.fp_level_1_value,
                                           orgLevel2 = bl.fp_level_2_value,
                                           Year1 = bl.year_1_limit,
                                           Year2 = bl.year_2_limit,
                                           Year3 = bl.year_3_limit,
                                           Year4 = bl.year_4_limit,
                                           actionType = bl.action_type
                                       }).ToListAsync();
            return serviceAreaBudget;
        }

        public async Task<List<ServiceAreaBudget>> GetDataForBudgetProposalAsync(UserData userDetails, string userId, int action_type, int BudgetYear)
        {
            int budgetYear = BudgetYear;
            TenantDBContext budgetManagementDBContext = await _utility.GetTenantDBContextAsync();
            List<ServiceAreaBudget> serviceAreaBudgetData = await (from th in budgetManagementDBContext.tfp_temp_budget_limits
                                                                   where th.fk_tenant_id == userDetails.tenant_id && th.budget_year == budgetYear
                                                                        && (th.action_type == action_type)
                                                                   select new ServiceAreaBudget
                                                                   {
                                                                       orgLevel1 = th.fp_level_1_value,
                                                                       orgLevel2 = th.fp_level_2_value,
                                                                       Year1 = th.year_1_limit,
                                                                       Year2 = th.year_2_limit,
                                                                       Year3 = th.year_3_limit,
                                                                       Year4 = th.year_4_limit
                                                                   }).ToListAsync();

            return serviceAreaBudgetData;
        }

        public IQueryable<BudgetManagementData> GetAzureKeyForNewsAndProposal(string userId, int budgetYear)
        {
            return GetAzureKeyForNewsAndProposalAsync(userId, budgetYear).GetAwaiter().GetResult();
        }

        public async Task<IQueryable<BudgetManagementData>> GetAzureKeyForNewsAndProposalAsync(string userId, int budgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext budgetManagementDBContext = await _utility.GetTenantDBContextAsync();
            IQueryable<BudgetManagementData> meetingData = from bm in budgetManagementDBContext.tbm_budget_meeting
                                                           where bm.active == 1 && bm.budget_year == budgetYear && bm.fk_tenant_id == userDetails.tenant_id
                                                           select new BudgetManagementData
                                                           {
                                                               meeting_id = bm.pk_meeting_id,
                                                               aztableNewsId = bm.fk_aztable_news,
                                                               aztableProposalID = bm.fk_aztable_proposal
                                                           };
            return meetingData;
        }

        private async Task<tco_widget_node_master> GetWidgetNodeMasterEntity(string userId, MasterTemplateWidgetNodeHelper widgetNodeData)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            return new tco_widget_node_master()
            {
                pk_node_master_id = Guid.NewGuid(),
                fk_tenant_id = userDetails.tenant_id,
                budget_year = widgetNodeData.budgetYear,
                fk_pub_temp_id = widgetNodeData.adminTemplateId,
                fk_report_template_id = widgetNodeData.reportTemplateId,
                node_title = widgetNodeData.nodeTitle,
                node_type = GetNodeTypeBasedOnReportType(widgetNodeData),
                tree_type = PublishTreeType.BudgetManagement.ToString(),
                updated = DateTime.UtcNow,
                updated_by = userDetails.pk_id
            };
        }

        private string GetNodeTypeBasedOnReportType(MasterTemplateWidgetNodeHelper widgetNodeData)
        {
            switch (widgetNodeData.reportType.ToString())
            {
                case nameof(ReportTypes.FinPlanReport):
                    return BM_Tree_Types.ActionListWidget.ToString();

                case nameof(ReportTypes.InvestmentReport):
                    return BM_Tree_Types.InvestmentListWidget.ToString();

                default:
                    return string.Empty;
            }
        }

        private async Task<tco_custom_node_budphase_mapping> GetWidgetNodeBudgetPhaseMappingEntity(string userId, Guid budgetPhaseId, tco_widget_node_master masterNodeData)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            return new tco_custom_node_budphase_mapping
            {
                budget_year = masterNodeData.budget_year,
                fk_tenant_id = userDetails.tenant_id,
                fk_budget_phase_id = budgetPhaseId,
                fk_node_id = masterNodeData.pk_node_master_id,
                is_connected = true,
                pk_mapping_id = Guid.NewGuid(),
                updated = DateTime.UtcNow,
                updated_by = userDetails.pk_id
            };
        }

        private async Task<tco_widget_node_delegation> GetWidgetNodeDelegationEntity(string userId, OrgIdOrgLevelServiceIdServiceLevel orgServiceInfo, Guid masterNodeId, int budgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            return new tco_widget_node_delegation
            {
                pk_node_id = Guid.NewGuid(),
                fk_master_node_id = masterNodeId,
                fk_tenant_id = userDetails.tenant_id,
                budget_year = budgetYear,
                description = string.Empty,
                org_id = orgServiceInfo.orgId,
                org_level = orgServiceInfo.orgLevel ?? 0,
                service_id = orgServiceInfo.serviceId,
                service_level = orgServiceInfo.serviceLevel,
                updated = DateTime.UtcNow,
                updated_by = userDetails.pk_id
            };
        }

        private async Task CreateNodesForConfig1Async(string userId, PublishTemplateHelper templateData)
        {
            List<clsOrgIdsAndServiceIds> orgSerIds = await _consequentAdjustedBudget.GetOrgIdsAndServiceIdsAsync(userId, false, templateData.BudgetYear);
            List<clsOrgIdsAndServiceIds> orgIds = orgSerIds.Where(x => x.parentId == null).OrderBy(x => x.orgId).ToList();
            TenantData tenantData = await _utility.GetTenantDataAsync(userId);
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            //Get list of master custom node instances for this template
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            List<tco_custom_node_master> cnInstances = await dbContext.tco_custom_node_master.Where(x => x.fk_pub_temp_id == templateData.Id).ToListAsync();
            IEnumerable<PublishTreeNode> cnInTemplate = _utility.GetAllCustomNodes(templateData.Tree);

            List<tco_custom_node_master> cnLinksToBeCreated = new List<tco_custom_node_master>();
            List<TcoCustomNode> cnToBeCreated = new List<TcoCustomNode>();
            List<tco_custom_node_budphase_mapping> cnBudPhaseMapping = new List<tco_custom_node_budphase_mapping>();
            Dictionary<Guid, string> budgetPhases = new Dictionary<Guid, string>();

            string budPhaseParamValue = await _utility.GetParameterValueAsync(userId, "BM_CUSTOM_NODE_DEF_UNCHECKED");
            if (string.IsNullOrEmpty(budPhaseParamValue) || budPhaseParamValue.ToLower() == "false")
            {
                budgetPhases = await dbContext.tco_budget_phase.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.status == 1 && x.org_budget_flag == 1).OrderBy(x => x.sort_order).Select(x => new { x.pk_budget_phase_id, x.description }).ToDictionaryAsync(x => x.pk_budget_phase_id, x => x.description);
            }
            var masterNodeIds = cnInstances.Select(x => x.pk_master_node_id).ToList();
            var templateMapping = await dbContext.tco_custom_node_budphase_mapping.Where(x => x.fk_tenant_id == userDetails.tenant_id && masterNodeIds.Contains(x.fk_node_id)).ToListAsync();
            //For each custom node in the template, we have to create the instances for each org id
            foreach (var nodeInTemplate in cnInTemplate)
            {
                foreach (var orgId in orgIds)
                {
                    tco_custom_node_master instance = cnInstances.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id &&
                        x.pk_master_node_id.Equals(Guid.Parse(nodeInTemplate.id)) && x.org_id == orgId.orgId);
                    //If the instance does not exist, create it
                    if (instance == null)
                    {
                        Guid instanceId = Guid.NewGuid();
                        string internalDesc = string.Empty;
                        if (nodeInTemplate.parameters != null)
                        {
                            if (nodeInTemplate.parameters.ContainsKey("internalDesc"))
                            {
                                internalDesc = nodeInTemplate.parameters["internalDesc"];
                            }
                        }
                        tco_custom_node_master newNode = new tco_custom_node_master
                        {
                            pk_master_node_id = Guid.Parse(nodeInTemplate.id),
                            fk_pub_temp_id = templateData.Id,
                            cn_instance_id = instanceId,
                            fk_tenant_id = userDetails.tenant_id,
                            budget_year = templateData.BudgetYear,
                            org_id = orgId.orgId,
                            service_id = string.Empty,
                            tree_type = PublishTreeType.BudgetManagement.ToString(),
                            forecast_period = 0
                        };
                        cnLinksToBeCreated.Add(newNode);
                        //save budgetPhase mapping
                        if (!cnBudPhaseMapping.Any(x => x.fk_node_id == Guid.Parse(nodeInTemplate.id)))
                        {
                            foreach (var item in budgetPhases)
                            {
                                if (!templateMapping.Any(x => x.fk_node_id == Guid.Parse(nodeInTemplate.id) && x.fk_budget_phase_id == item.Key))
                                {
                                    tco_custom_node_budphase_mapping mappingData = new tco_custom_node_budphase_mapping
                                    {
                                        budget_year = templateData.BudgetYear,
                                        fk_tenant_id = userDetails.tenant_id,
                                        fk_budget_phase_id = item.Key,
                                        fk_node_id = Guid.Parse(nodeInTemplate.id),
                                        is_connected = true,
                                        pk_mapping_id = Guid.NewGuid(),
                                        updated = DateTime.UtcNow,
                                        updated_by = userDetails.pk_id
                                    };
                                    cnBudPhaseMapping.Add(mappingData);
                                }
                            }
                        }
                        TcoCustomNode customNode = new TcoCustomNode
                        {
                            PkNodeId = instanceId,
                            BudgetYear = templateData.BudgetYear,
                            FkTenantId = userDetails.tenant_id,
                            NodeTitle = nodeInTemplate.text,
                            NodeTitleForDoc = nodeInTemplate.text,
                            AbstractText = string.Empty,
                            Description = string.Empty,
                            NodePath = string.Empty,
                            BlobPath = string.Empty,
                            TreeType = PublishTreeType.BudgetManagement.ToString(),
                            Position = 0,
                            Updated = DateTime.UtcNow,
                            UpdatedBy = userDetails.pk_id,
                            DisplayLandscape = false,
                            Param1 = string.Empty,
                            ForecastPeriod = 0,
                            NodeType = CustomNodeType.Text.ToString(),
                            GuidanceText = string.Empty,
                            GuidanceTextId = null,
                            InternalDescription = internalDesc
                        };
                        cnToBeCreated.Add(customNode);
                    }
                }
            }
            dbContext.tco_custom_node_master.AddRange(cnLinksToBeCreated);
            dbContext.TcoCustomNode.AddRange(cnToBeCreated);
            dbContext.tco_custom_node_budphase_mapping.AddRange(cnBudPhaseMapping);
            await dbContext.BulkSaveChangesAsync();
        }

        private async Task CreateNodesForConfig2Async(string userId, PublishTemplateHelper templateData)
        {
            List<clsOrgIdsAndServiceIds> orgSerIds = await _consequentAdjustedBudget.GetOrgIdsAndServiceIdsAsync(userId, false, templateData.BudgetYear);
            List<string> serviceIds = orgSerIds.Where(x => x.parentId != null).Select(x => x.orgId).Distinct().OrderBy(x => x).ToList();
            TenantData tenantData = await _utility.GetTenantDataAsync(userId);
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            //Get list of master custom node instances for this template
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            List<tco_custom_node_master> cnInstances = await dbContext.tco_custom_node_master.Where(x => x.fk_pub_temp_id == templateData.Id).ToListAsync();
            IEnumerable<PublishTreeNode> cnInTemplate = _utility.GetAllCustomNodes(templateData.Tree);

            List<tco_custom_node_master> cnLinksToBeCreated = new List<tco_custom_node_master>();
            List<TcoCustomNode> cnToBeCreated = new List<TcoCustomNode>();
            List<tco_custom_node_budphase_mapping> cnBudPhaseMapping = new List<tco_custom_node_budphase_mapping>();
            Dictionary<Guid, string> budgetPhases = new Dictionary<Guid, string>();

            var masterNodeIds = cnInstances.Select(x => x.pk_master_node_id).Distinct().ToList();
            var templateMapping = dbContext.tco_custom_node_budphase_mapping.Where(x => x.fk_tenant_id == userDetails.tenant_id && masterNodeIds.Contains(x.fk_node_id)).ToList();

            string budPhaseParamValue = await _utility.GetParameterValueAsync(userId, "BM_CUSTOM_NODE_DEF_UNCHECKED");
            if (string.IsNullOrEmpty(budPhaseParamValue) || budPhaseParamValue.ToLower() == "false")
            {
                budgetPhases = dbContext.tco_budget_phase.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.status == 1 && x.org_budget_flag == 1).OrderBy(x => x.sort_order).Select(x => new { x.pk_budget_phase_id, x.description }).ToDictionary(x => x.pk_budget_phase_id, x => x.description);
            }

            //For each custom node in the template, we have to create the instances for each org id
            foreach (var nodeInTemplate in cnInTemplate)
            {
                foreach (var serId in serviceIds)
                {
                    tco_custom_node_master instance = cnInstances.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id &&
                        x.pk_master_node_id.Equals(Guid.Parse(nodeInTemplate.id)) &&
                        x.org_id == serId);
                    //If the instance does not exist, create it
                    if (instance == null)
                    {
                        Guid instanceId = Guid.NewGuid();
                        string internalDesc = string.Empty;
                        if (nodeInTemplate.parameters != null)
                        {
                            if (nodeInTemplate.parameters.ContainsKey("internalDesc"))
                            {
                                internalDesc = nodeInTemplate.parameters["internalDesc"];
                            }
                        }
                        tco_custom_node_master newNode = new tco_custom_node_master
                        {
                            pk_master_node_id = Guid.Parse(nodeInTemplate.id),
                            fk_pub_temp_id = templateData.Id,
                            cn_instance_id = instanceId,
                            fk_tenant_id = userDetails.tenant_id,
                            budget_year = templateData.BudgetYear,
                            org_id = serId,
                            service_id = string.Empty,
                            tree_type = PublishTreeType.BudgetManagement.ToString(),
                            forecast_period = 0
                        };
                        cnLinksToBeCreated.Add(newNode);
                        //save budgetPhase mapping
                        if (!cnBudPhaseMapping.Any(x => x.fk_node_id == Guid.Parse(nodeInTemplate.id)))
                        {
                            foreach (var item in budgetPhases)
                            {
                                if (!templateMapping.Any(x => x.fk_node_id == Guid.Parse(nodeInTemplate.id) && x.fk_budget_phase_id == item.Key))
                                {
                                    tco_custom_node_budphase_mapping mappingData = new tco_custom_node_budphase_mapping
                                    {
                                        budget_year = templateData.BudgetYear,
                                        fk_tenant_id = userDetails.tenant_id,
                                        fk_budget_phase_id = item.Key,
                                        fk_node_id = Guid.Parse(nodeInTemplate.id),
                                        is_connected = true,
                                        pk_mapping_id = Guid.NewGuid(),
                                        updated = DateTime.UtcNow,
                                        updated_by = userDetails.pk_id
                                    };
                                    cnBudPhaseMapping.Add(mappingData);
                                }
                            }
                        }
                        TcoCustomNode customNode = new TcoCustomNode
                        {
                            PkNodeId = instanceId,
                            BudgetYear = templateData.BudgetYear,
                            FkTenantId = userDetails.tenant_id,
                            NodeTitle = nodeInTemplate.text,
                            NodeTitleForDoc = nodeInTemplate.text,
                            AbstractText = string.Empty,
                            Description = string.Empty,
                            NodePath = string.Empty,
                            BlobPath = string.Empty,
                            TreeType = PublishTreeType.BudgetManagement.ToString(),
                            Position = 0,
                            Updated = DateTime.UtcNow,
                            UpdatedBy = userDetails.pk_id,
                            DisplayLandscape = false,
                            Param1 = string.Empty,
                            ForecastPeriod = 0,
                            NodeType = CustomNodeType.Text.ToString(),
                            GuidanceText = string.Empty,
                            GuidanceTextId = null,
                            InternalDescription = internalDesc
                        };
                        cnToBeCreated.Add(customNode);
                    }
                }
            }
            dbContext.tco_custom_node_master.AddRange(cnLinksToBeCreated);
            dbContext.TcoCustomNode.AddRange(cnToBeCreated);
            dbContext.tco_custom_node_budphase_mapping.AddRange(cnBudPhaseMapping);
            await dbContext.BulkSaveChangesAsync();
        }

        private async Task CreateNodesForConfig3Async(string userId, PublishTemplateHelper templateData)
        {
            List<clsOrgIdsAndServiceIds> orgSerIds = await _consequentAdjustedBudget.GetOrgIdsAndServiceIdsAsync(userId, false, templateData.BudgetYear);
            List<clsOrgIdsAndServiceIds> orgIds = orgSerIds.Where(x => x.parentId == null).OrderBy(x => x.orgId).ToList();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            //Get list of master custom node instances for this template
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            List<tco_custom_node_master> cnInstances = await dbContext.tco_custom_node_master.Where(x => x.fk_pub_temp_id == templateData.Id).ToListAsync();
            List<PublishTreeNode> level1Template = templateData.Tree.ToList();
            PublishTreeNode serUnitNode =
                templateData.Tree.FirstOrDefault(x => x.type.Equals("ServiceId", StringComparison.InvariantCultureIgnoreCase));
            List<PublishTreeNode> level2Template = null;
            IEnumerable<PublishTreeNode> cnInL2Template = null;
            if (serUnitNode != null)
            {
                level2Template = serUnitNode.items;
                level1Template.Remove(serUnitNode);
                cnInL2Template = _utility.GetAllCustomNodes(level2Template);
            }

            IEnumerable<PublishTreeNode> cnInL1Template = _utility.GetAllCustomNodes(level1Template);

            List<tco_custom_node_master> cnLinksToBeCreated = new List<tco_custom_node_master>();
            List<TcoCustomNode> cnToBeCreated = new List<TcoCustomNode>();
            List<tco_custom_node_budphase_mapping> cnBudPhaseMapping = new List<tco_custom_node_budphase_mapping>();
            Dictionary<Guid, string> budgetPhases = new Dictionary<Guid, string>();

            var masterNodeIds = cnInstances.Select(x => x.pk_master_node_id).ToList();
            var templateMapping = await dbContext.tco_custom_node_budphase_mapping.Where(x => x.fk_tenant_id == userDetails.tenant_id && masterNodeIds.Contains(x.fk_node_id)).ToListAsync();

            string budPhaseParamValue = await _utility.GetParameterValueAsync(userId, "BM_CUSTOM_NODE_DEF_UNCHECKED");
            List<tco_custom_node_budphase_mapping> budPhaseMapping = new List<tco_custom_node_budphase_mapping>();
            if (string.IsNullOrEmpty(budPhaseParamValue) || budPhaseParamValue.ToLower() == "false")
            {
                budgetPhases = await dbContext.tco_budget_phase.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.status == 1 && x.org_budget_flag == 1).OrderBy(x => x.sort_order).Select(x => new { x.pk_budget_phase_id, x.description }).ToDictionaryAsync(x => x.pk_budget_phase_id, x => x.description);
                budPhaseMapping = await dbContext.tco_custom_node_budphase_mapping.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == templateData.BudgetYear).ToListAsync();
            }
            //Process the first level nodes
            //For each custom node in the template, we have to create the instances for each org id
            foreach (var l1Node in cnInL1Template)
            {
                foreach (var orgId in orgIds)
                {
                    tco_custom_node_master instance = cnInstances.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id &&
                        x.pk_master_node_id.Equals(Guid.Parse(l1Node.id)) && x.org_id == orgId.orgId);
                    //If the instance does not exist, create it
                    if (instance == null)
                    {
                        Guid instanceId = Guid.NewGuid();
                        string internalDesc = string.Empty;
                        if (l1Node.parameters != null)
                        {
                            if (l1Node.parameters.ContainsKey("internalDesc"))
                            {
                                internalDesc = l1Node.parameters["internalDesc"];
                            }
                        }
                        tco_custom_node_master newNode = new tco_custom_node_master
                        {
                            pk_master_node_id = Guid.Parse(l1Node.id),
                            fk_pub_temp_id = templateData.Id,
                            cn_instance_id = instanceId,
                            fk_tenant_id = userDetails.tenant_id,
                            budget_year = templateData.BudgetYear,
                            org_id = orgId.orgId,
                            service_id = string.Empty,
                            tree_type = PublishTreeType.BudgetManagement.ToString(),
                            forecast_period = 0
                        };
                        cnLinksToBeCreated.Add(newNode);
                        cnInstances.Add(newNode);

                        //save budgetPhase mapping
                        if (!cnBudPhaseMapping.Any(x => x.fk_node_id == Guid.Parse(l1Node.id)) && !budPhaseMapping.Any(x => x.fk_node_id == Guid.Parse(l1Node.id)))
                        {
                            foreach (var item in budgetPhases)
                            {
                                if (!templateMapping.Any(x => x.fk_node_id == Guid.Parse(l1Node.id) && x.fk_budget_phase_id == item.Key))
                                {
                                    tco_custom_node_budphase_mapping mappingData = new tco_custom_node_budphase_mapping
                                    {
                                        budget_year = templateData.BudgetYear,
                                        fk_tenant_id = userDetails.tenant_id,
                                        fk_budget_phase_id = item.Key,
                                        fk_node_id = Guid.Parse(l1Node.id),
                                        is_connected = true,
                                        pk_mapping_id = Guid.NewGuid(),
                                        updated = DateTime.UtcNow,
                                        updated_by = userDetails.pk_id
                                    };
                                    cnBudPhaseMapping.Add(mappingData);
                                }
                            }
                        }
                        TcoCustomNode customNode = new TcoCustomNode
                        {
                            PkNodeId = instanceId,
                            BudgetYear = templateData.BudgetYear,
                            FkTenantId = userDetails.tenant_id,
                            NodeTitle = l1Node.text,
                            NodeTitleForDoc = l1Node.text,
                            AbstractText = string.Empty,
                            Description = string.Empty,
                            NodePath = string.Empty,
                            BlobPath = string.Empty,
                            TreeType = PublishTreeType.BudgetManagement.ToString(),
                            Position = 0,
                            Updated = DateTime.UtcNow,
                            UpdatedBy = userDetails.pk_id,
                            DisplayLandscape = false,
                            Param1 = string.Empty,
                            ForecastPeriod = 0,
                            NodeType = CustomNodeType.Text.ToString(),
                            GuidanceText = string.Empty,
                            GuidanceTextId = null,
                            InternalDescription = internalDesc
                        };
                        cnToBeCreated.Add(customNode);
                    }
                }
            }
            foreach (var orgId in orgIds)
            {
                var serviceIds = orgSerIds.Where(x => x.parentId == orgId.id.ToString() && !string.IsNullOrEmpty(x.orgId)).Select(y => y.orgId).OrderBy(z => z).Distinct().ToList();
                if (cnInL2Template != null)
                {
                    //Process level 2 template for this org
                    foreach (var l2Node in cnInL2Template)
                    {
                        foreach (var serId in serviceIds)
                        {
                            tco_custom_node_master instance2 = cnInstances.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id &&
                                x.pk_master_node_id.Equals(Guid.Parse(l2Node.id)) && x.org_id == orgId.orgId && x.service_id == serId);
                            //If the instance does not exist, create it
                            if (instance2 == null)
                            {
                                Guid instanceId = Guid.NewGuid();
                                tco_custom_node_master newNode = new tco_custom_node_master
                                {
                                    pk_master_node_id = Guid.Parse(l2Node.id),
                                    fk_pub_temp_id = templateData.Id,
                                    cn_instance_id = instanceId,
                                    fk_tenant_id = userDetails.tenant_id,
                                    budget_year = templateData.BudgetYear,
                                    org_id = orgId.orgId,
                                    service_id = serId,
                                    tree_type = PublishTreeType.BudgetManagement.ToString(),
                                    forecast_period = 0
                                };
                                cnLinksToBeCreated.Add(newNode);
                                cnInstances.Add(newNode);
                                //save budgetPhase mapping
                                if (!cnBudPhaseMapping.Any(x => x.fk_node_id == Guid.Parse(l2Node.id)) && !budPhaseMapping.Any(x => x.fk_node_id == Guid.Parse(l2Node.id)))
                                {
                                    foreach (var item in budgetPhases)
                                    {
                                        tco_custom_node_budphase_mapping mappingData = new tco_custom_node_budphase_mapping
                                        {
                                            budget_year = templateData.BudgetYear,
                                            fk_tenant_id = userDetails.tenant_id,
                                            fk_budget_phase_id = item.Key,
                                            fk_node_id = Guid.Parse(l2Node.id),
                                            is_connected = true,
                                            pk_mapping_id = Guid.NewGuid(),
                                            updated = DateTime.UtcNow,
                                            updated_by = userDetails.pk_id
                                        };
                                        cnBudPhaseMapping.Add(mappingData);
                                    }
                                }
                                TcoCustomNode customNode = new TcoCustomNode
                                {
                                    PkNodeId = instanceId,
                                    BudgetYear = templateData.BudgetYear,
                                    FkTenantId = userDetails.tenant_id,
                                    NodeTitle = l2Node.text,
                                    NodeTitleForDoc = l2Node.text,
                                    AbstractText = string.Empty,
                                    Description = string.Empty,
                                    NodePath = string.Empty,
                                    BlobPath = string.Empty,
                                    TreeType = PublishTreeType.BudgetManagement.ToString(),
                                    Position = 0,
                                    Updated = DateTime.UtcNow,
                                    UpdatedBy = userDetails.pk_id,
                                    DisplayLandscape = false,
                                    Param1 = string.Empty,
                                    ForecastPeriod = 0,
                                    NodeType = CustomNodeType.Text.ToString(),
                                    GuidanceText = string.Empty,
                                    GuidanceTextId = null,
                                };
                                cnToBeCreated.Add(customNode);
                            }
                        }
                    }
                }
            }
            dbContext.tco_custom_node_master.AddRange(cnLinksToBeCreated);
            dbContext.TcoCustomNode.AddRange(cnToBeCreated);
            dbContext.tco_custom_node_budphase_mapping.AddRange(cnBudPhaseMapping);
            await dbContext.BulkSaveChangesAsync();
        }

        private async Task<tco_widget_node_master> CreateBMMasterTemplateWidgetListNodesForConfig1(string userId, MasterTemplateWidgetNodeHelper widgetNodeData)
        {
            List<clsOrgIdsAndServiceIds> orgSerIds = await _consequentAdjustedBudget.GetOrgIdsAndServiceIdsAsync(userId, false, widgetNodeData.budgetYear);
            List<string> orgIdLst = orgSerIds.Where(x => x.parentId == null).OrderBy(x => x.orgId).Select(x => x.orgId).ToList();
            tco_widget_node_master widgetNodeMasterData = await CreateWidgetNodeInstances(userId, true, orgIdLst, widgetNodeData);
            return widgetNodeMasterData;
        }

        private async Task<tco_widget_node_master> CreateBMMasterTemplateWidgetListNodesForConfig2(string userId, MasterTemplateWidgetNodeHelper widgetNodeData)
        {
            List<clsOrgIdsAndServiceIds> orgSerIds = await _consequentAdjustedBudget.GetOrgIdsAndServiceIdsAsync(userId, false, widgetNodeData.budgetYear);
            List<string> serviceIds = orgSerIds.Where(x => x.parentId != null).Select(x => x.orgId).Distinct().OrderBy(x => x).ToList();
            tco_widget_node_master widgetNodeMasterData = await CreateWidgetNodeInstances(userId, false, serviceIds, widgetNodeData);
            return widgetNodeMasterData;
        }

        private async Task<tco_widget_node_master> CreateBMMasterTemplateWidgetListNodesForConfig3(string userId, MasterTemplateWidgetNodeHelper widgetNodeData)
        {
            List<clsOrgIdsAndServiceIds> orgSerIds = await _consequentAdjustedBudget.GetOrgIdsAndServiceIdsAsync(userId, false, widgetNodeData.budgetYear);
            List<string> orgIdLst = orgSerIds.Where(x => x.parentId == null).OrderBy(x => x.orgId).Select(x => x.orgId).ToList();
            if (!widgetNodeData.isLevel2TreeData)
            {
                return await CreateWidgetNodeInstances(userId, true, orgIdLst, widgetNodeData);
            }
            else
            {
                return await CreateWidgetNodeInstancesForLevel2Tree(userId, widgetNodeData, orgSerIds);
            }
        }

        private async Task<tco_widget_node_master> CreateWidgetNodeInstancesForLevel2Tree(string userId, MasterTemplateWidgetNodeHelper widgetNodeData, List<clsOrgIdsAndServiceIds> orgSerIds)
        {
            List<tco_widget_node_delegation> widgetDelegationNodeList = new List<tco_widget_node_delegation>();
            Dictionary<Guid, string> budgetPhases = new Dictionary<Guid, string>();

            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            List<clsOrgIdsAndServiceIds> orgIds = orgSerIds.Where(x => x.parentId == null).OrderBy(x => x.orgId).ToList();
            string budPhaseParamValue = await _utility.GetParameterValueAsync(userId, "BM_CUSTOM_NODE_DEF_UNCHECKED");
            //if parameter is false, node should be present for all docversions, otherwise should not be available for all docversions
            if (string.IsNullOrEmpty(budPhaseParamValue) || budPhaseParamValue.ToLower() == "false")
            {
                if (widgetNodeData.budgetPhaseTree == null)
                {
                    List<tco_budget_phase> budgetPhaseList = await _unitOfWork.BudgetProposalRepository.GetBudgetPhaseList(userDetails.tenant_id);
                    budgetPhases = budgetPhaseList != null && budgetPhaseList.Any() ?
                                            budgetPhaseList.Select(x => new { x.pk_budget_phase_id, x.description })
                                                            .ToDictionary(x => x.pk_budget_phase_id, x => x.description) :
                                                            new Dictionary<Guid, string>();
                }
                else
                {
                    budgetPhases = widgetNodeData.budgetPhaseTree.items
                           .Where(item => item.isChecked)
                           .ToDictionary(item => Guid.Parse(item.key), item => string.Empty);
                }
            }

            //creating entity for master node
            tco_widget_node_master widgetNodeMasterData = await GetWidgetNodeMasterEntity(userId, widgetNodeData);
            _unitOfWork.GenericRepo.Add(widgetNodeMasterData);
            //creating mapping to masternode for all budgetphases
            foreach (var item in budgetPhases)
            {
                tco_custom_node_budphase_mapping mappingData = await GetWidgetNodeBudgetPhaseMappingEntity(userId, item.Key, widgetNodeMasterData);
                _unitOfWork.GenericRepo.Add(mappingData);
            }

            OrgIdOrgLevelServiceIdServiceLevel orgServiceLevelInfo = await GetOrgAndServiceLevelInfoBasedOnConfig(userId);

            //creating delegation nodes for org and service id list
            foreach (var orgId in orgIds)
            {
                var serviceIds = orgSerIds.Where(x => x.parentId == orgId.id.ToString() && !string.IsNullOrEmpty(x.orgId)).Select(y => y.orgId).OrderBy(z => z).Distinct().ToList();
                foreach (var serId in serviceIds)
                {
                    orgServiceLevelInfo.orgId = orgId.orgId;
                    orgServiceLevelInfo.serviceId = serId;
                    tco_widget_node_delegation widgetNodeDelegation = await GetWidgetNodeDelegationEntity(userId, orgServiceLevelInfo, widgetNodeMasterData.pk_node_master_id, widgetNodeMasterData.budget_year);
                    widgetDelegationNodeList.Add(widgetNodeDelegation);
                    _unitOfWork.GenericRepo.Add(widgetNodeDelegation);
                }
            }
            await _unitOfWork.CompleteAsync();

            //creating doc widget configuration for master and delegation nodes
            await CreateDocWidgetForMasterAndDelegationNodes(userId, widgetNodeData, widgetNodeMasterData, widgetDelegationNodeList);
            return widgetNodeMasterData;
        }

        private async Task<OrgIdOrgLevelServiceIdServiceLevel> GetOrgAndServiceLevelInfoBasedOnConfig(string userId)
        {
            OrgIdOrgLevelServiceIdServiceLevel orgAndServiceLevelInfo = new OrgIdOrgLevelServiceIdServiceLevel();
            string paramValueFP1 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_1");
            string paramValueFP2 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_2");
            int docConfig = await GetDocumentConfigurationAsync(userId);

            switch (docConfig)
            {
                case 1:

                    orgAndServiceLevelInfo = GetOrgOrServiceLevelByFinplanParameterValue(paramValueFP1);
                    break;

                case 2:

                    orgAndServiceLevelInfo = GetOrgOrServiceLevelByFinplanParameterValue(paramValueFP2);
                    break;

                default:

                    OrgIdOrgLevelServiceIdServiceLevel finplanLevel1Value = GetOrgOrServiceLevelByFinplanParameterValue(paramValueFP1);
                    OrgIdOrgLevelServiceIdServiceLevel finplanLevel2Value = GetOrgOrServiceLevelByFinplanParameterValue(paramValueFP2);

                    //for two level tree, assign both org and service level based on the parameter setup.
                    orgAndServiceLevelInfo.orgLevel = paramValueFP1.ToLower().StartsWith("org") ?
                                                                    finplanLevel1Value.orgLevel :
                                                                    finplanLevel1Value.serviceLevel;

                    orgAndServiceLevelInfo.serviceLevel = paramValueFP2.ToLower().StartsWith("service") ?
                                                                    finplanLevel2Value.serviceLevel :
                                                                    (finplanLevel2Value.orgLevel ?? 0);
                    break;
            }
            return orgAndServiceLevelInfo;
        }

        private OrgIdOrgLevelServiceIdServiceLevel GetOrgOrServiceLevelByFinplanParameterValue(string finplanParameterValue)
        {
            OrgIdOrgLevelServiceIdServiceLevel orgAndServiceInfo = new OrgIdOrgLevelServiceIdServiceLevel();
            if (finplanParameterValue.ToLower().StartsWith("org"))
            {
                orgAndServiceInfo.orgLevel = GetOrgLevelByFinplanSetupParameter(finplanParameterValue);
                orgAndServiceInfo.serviceLevel = 0;
            }
            if (finplanParameterValue.ToLower().StartsWith("service"))
            {
                orgAndServiceInfo.serviceLevel = GetServiceLevelByFinplanSetupParameter(finplanParameterValue);
                orgAndServiceInfo.orgLevel = 0;
            }
            return orgAndServiceInfo;
        }

        private int GetOrgLevelByFinplanSetupParameter(string finplanParameterValue)
        {
            switch (finplanParameterValue.ToLower())
            {
                case "org_id_1":
                    return 1;

                case "org_id_2":
                    return 2;

                case "org_id_3":
                    return 3;

                case "org_id_4":
                    return 4;

                case "org_id_5":
                    return 5;

                default:
                    return -1;//return -1 as invalid if org_id does not match
            }
        }

        private int GetServiceLevelByFinplanSetupParameter(string finplanParameterValue)
        {
            switch (finplanParameterValue)
            {
                case "service_id_1": return 1;
                case "service_id_2": return 2;
                case "service_id_3": return 3;
                case "service_id_4": return 4;
                case "service_id_5": return 5;
                default: return -1;//return -1 as invalid if service_id does not match
            }
        }

        private async Task<tco_widget_node_master> CreateWidgetNodeInstances(string userId, bool isOrgSetup, List<string> orgOrServiceIdLst, MasterTemplateWidgetNodeHelper widgetNodeInput)
        {
            Dictionary<Guid, string> budgetPhases = new Dictionary<Guid, string>();

            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            int docConfig = await GetDocumentConfigurationAsync(userId);
            List<tco_widget_node_delegation> widgetDelegationNodeList = new List<tco_widget_node_delegation>();
            List<tco_custom_node_budphase_mapping> cnBudPhaseMapping = new List<tco_custom_node_budphase_mapping>();

            string budPhaseParamValue = await _utility.GetParameterValueAsync(userId, "BM_CUSTOM_NODE_DEF_UNCHECKED");
            //if parameter is false, node should be present for all docversions, otherwise should not be available for all docversions
            if (string.IsNullOrEmpty(budPhaseParamValue) || budPhaseParamValue.ToLower() == "false")
            {
                if (widgetNodeInput.budgetPhaseTree == null)
                {
                    List<tco_budget_phase> budgetPhaseList = await _unitOfWork.BudgetProposalRepository.GetBudgetPhaseList(userDetails.tenant_id);
                    budgetPhases = budgetPhaseList != null && budgetPhaseList.Any() ?
                                                budgetPhaseList.Select(x => new { x.pk_budget_phase_id, x.description })
                                                                .ToDictionary(x => x.pk_budget_phase_id, x => x.description) :
                                                                new Dictionary<Guid, string>();
                }
                else
                {
                    budgetPhases = widgetNodeInput.budgetPhaseTree.items
                                               .Where(item => item.isChecked)
                                               .ToDictionary(item => Guid.Parse(item.key), item => string.Empty);
                }
            }

            //creating entity for master node
            tco_widget_node_master widgetNodeMasterData = await GetWidgetNodeMasterEntity(userId, widgetNodeInput);
            _unitOfWork.GenericRepo.Add(widgetNodeMasterData);
            //creating mapping to masternode for all budgetphases
            foreach (var item in budgetPhases)
            {
                tco_custom_node_budphase_mapping mappingData = await GetWidgetNodeBudgetPhaseMappingEntity(userId, item.Key, widgetNodeMasterData);
                _unitOfWork.GenericRepo.Add(mappingData);
            }

            OrgIdOrgLevelServiceIdServiceLevel orgServiceLevelInfo = await GetOrgAndServiceLevelInfoBasedOnConfig(userId);

            if (docConfig == 0)
            {
                //for two level tree, if node is getting added at the first level, service level will be zero
                orgServiceLevelInfo.serviceLevel = 0;
            }

            //creating delegation nodes either for org or service id list
            foreach (var orgOrServiceInfo in orgOrServiceIdLst)
            {
                orgServiceLevelInfo.orgId = isOrgSetup ? orgOrServiceInfo : string.Empty;
                orgServiceLevelInfo.serviceId = isOrgSetup ? string.Empty : orgOrServiceInfo;
                tco_widget_node_delegation widgetDelegationNode = await GetWidgetNodeDelegationEntity(userId, orgServiceLevelInfo, widgetNodeMasterData.pk_node_master_id, widgetNodeMasterData.budget_year);
                widgetDelegationNodeList.Add(widgetDelegationNode);
                _unitOfWork.GenericRepo.Add(widgetDelegationNode);
            }

            await _unitOfWork.CompleteAsync();
            //creating doc widget configuration for master and delegation nodes
            await CreateDocWidgetForMasterAndDelegationNodes(userId, widgetNodeInput, widgetNodeMasterData, widgetDelegationNodeList);
            return widgetNodeMasterData;
        }

        public async Task CreateDocWidgetForMasterAndDelegationNodes(string userId, MasterTemplateWidgetNodeHelper widgetNodeInput, tco_widget_node_master widgetNodeMasterData, List<tco_widget_node_delegation> widgetDelegationNodeList)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            await CreateDocWidgetForMasterNode(userId, widgetNodeInput, widgetNodeMasterData);
            await CreateDocWidgetForDelegationNodes(userId, widgetNodeInput, widgetDelegationNodeList);
            await _unitOfWork.CompleteAsync();//widget nodes are getting added to generic repos and a single complete is used here
        }

        private async Task CreateDocWidgetForMasterNode(string userId, MasterTemplateWidgetNodeHelper widgetNodeInput, tco_widget_node_master widgetNodeMasterData)
        {
            widgetNodeInput.widgetConfig.gridWidgetConfig.widgetId = widgetNodeMasterData.pk_node_master_id;
            widgetNodeInput.widgetConfig.gridWidgetConfig.contentId = widgetNodeMasterData.pk_node_master_id;
            widgetNodeInput.widgetConfig.gridWidgetConfig.widgetType = widgetNodeInput.widgetConfig.widgetType;
            await CreateDocWidget(userId, widgetNodeInput);
        }

        private async Task CreateDocWidgetForDelegationNodes(string userId, MasterTemplateWidgetNodeHelper widgetNodeInput, List<tco_widget_node_delegation> widgetDelegationNodeList)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            foreach (var item in widgetDelegationNodeList)
            {
                widgetNodeInput.widgetConfig.gridWidgetConfig.level1 = item.org_id;
                widgetNodeInput.widgetConfig.gridWidgetConfig.level2 = item.service_id;
                widgetNodeInput.widgetConfig.gridWidgetConfig.widgetId = Guid.NewGuid();
                widgetNodeInput.widgetConfig.gridWidgetConfig.contentId = item.pk_node_id;
                widgetNodeInput.widgetConfig.gridWidgetConfig.widgetType = widgetNodeInput.widgetConfig.widgetType;
                await CreateDocWidget(userId, widgetNodeInput);
            }
        }

        public async Task CreateDocWidget(string userId, MasterTemplateWidgetNodeHelper widgetNodeInput)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            tco_doc_widget dw = new tco_doc_widget
            {
                budget_year = widgetNodeInput.widgetConfig.budgetYear,
                fk_tenant_id = userDetails.tenant_id,
                pk_id = widgetNodeInput.widgetConfig.gridWidgetConfig.widgetId,
                config_json = JsonConvert.SerializeObject(widgetNodeInput.widgetConfig.gridWidgetConfig),
                type = widgetNodeInput.widgetConfig.widgetType.ToString(),
                content_id = widgetNodeInput.widgetConfig.gridWidgetConfig.contentId,
                divided_by = widgetNodeInput.widgetConfig.gridWidgetConfig.dividedBy,
                updated = DateTime.UtcNow,
                updated_by = userDetails.pk_id,
                fk_report_template_id = widgetNodeInput.reportTemplateId
            };
            _unitOfWork.GenericRepo.Add(dw);
            await UpdateReportingTemplate(userId, widgetNodeInput.reportTemplateId);
        }

        public async Task<tco_doc_widget> CreateDocWidget(string userId, tco_doc_widget masterNodeWidgetData, tco_widget_node_delegation delegationNodeData)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            tco_doc_widget dw = new tco_doc_widget
            {
                budget_year = masterNodeWidgetData.budget_year,
                fk_tenant_id = userDetails.tenant_id,
                pk_id = delegationNodeData.pk_node_id,//reconfirm
                config_json = masterNodeWidgetData.config_json,
                type = masterNodeWidgetData.type,
                content_id = delegationNodeData.pk_node_id,
                divided_by = masterNodeWidgetData.divided_by,
                updated = DateTime.UtcNow,
                updated_by = userDetails.pk_id,
                fk_report_template_id = masterNodeWidgetData.fk_report_template_id
            };
            await UpdateReportingTemplate(userId, masterNodeWidgetData.fk_report_template_id);
            return dw;
        }

        private async Task UpdateReportingTemplate(string userID, int templateId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            tco_reporting_template reportingTemplate = await dbContext.tco_reporting_template.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.Id == templateId);
            reportingTemplate.is_locked = true;
            reportingTemplate.updated = DateTime.UtcNow;
            reportingTemplate.updated_by = userDetails.pk_id;
            await dbContext.SaveChangesAsync();
        }

        public async Task<JObject> GetAllFocusAreaDetailsNew(string userId, int budgetYear)
        {
            TenantDBContext budgetManagementDbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            dynamic result = new JObject();
            var isTenantSyncSetup = await _dataSyncUtility.IsTenantSyncSetup(userDetails.tenant_id, userDetails.client_id);
            List<tco_progress_status> syncStatusAllData = await GetSyncStatusDropDown(userDetails.tenant_id, SyncObjectStatusType.SYNC_OBJECT_STATUS);
            var focus_areas = await (from a in budgetManagementDbContext.tco_focusarea
                                     join
                               b in budgetManagementDbContext.tpl_tfp_focusarea_mapping on new { a = a.fk_tenant_id, b = a.budget_year, c = a.pk_id } equals new { a = b.fk_tenant_id, b = b.budget_year, c = b.fk_finplan_focusArea_id } into g1
                                     from g in g1.DefaultIfEmpty()
                                     where a.fk_tenant_id == userDetails.tenant_id && a.budget_year == budgetYear
                                     select new BMFocusAreaHelper
                                     {
                                         id = a.pk_id,
                                         focusArea = a.focusarea_description.Length > 250 ? (a.focusarea_description.Substring(0, 250) + "...") : a.focusarea_description,
                                         description = a.focusarea_longdescription,
                                         sorting = a.sort_order,
                                         isPlanFocusArea = g == null ? false : true,
                                         syncStatus = new KeyValueHelperData
                                         {
                                             key = isTenantSyncSetup ? a.sync_status : -1,
                                             value = GetSyncStatus(isTenantSyncSetup, syncStatusAllData, a.sync_status)
                                         }
                                     }).OrderBy(x => x.sorting).ThenBy(x => x.id).ToListAsync();

            if (focus_areas.Any())
            {
                result.Add("data", JArray.FromObject(focus_areas));
            }
            return result;
        }

        public async Task<JObject> GetAllFocusAreaDetailsAsync(string userId, int budgetYear)
        {
            try
            {
                TenantDBContext budgetManagementDbContext = await _utility.GetTenantDBContextAsync();
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                Dictionary<string, clsLanguageString> langStringValuesBAtype = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetManagement");

                dynamic result = new JObject();
                dynamic headerTitle = new JObject();

                headerTitle.title = ((langStringValuesBAtype.FirstOrDefault(v => v.Key == "BM_FA_title")).Value).LangText;
                headerTitle.descriptiontip = ((langStringValuesBAtype.FirstOrDefault(v => v.Key == "BM_FA_descriptiontip")).Value).LangText;

                var focus_areas = await budgetManagementDbContext.tco_focusarea.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear).OrderBy(x => x.sort_order).ThenBy(x => x.pk_id).ToListAsync();

                JArray gridData = new JArray();
                for (int i = 0; i < focus_areas.Count; i++)
                {
                    dynamic row = new JObject();
                    row.id = focus_areas[i].pk_id;
                    row.focusAreaText = focus_areas[i].focusarea_description.Length > 250 ? focus_areas[i].focusarea_description.Substring(0, 250) + "..." : focus_areas[i].focusarea_description;
                    row.focusarea_longdesc = focus_areas[i].focusarea_longdescription.Length > 250 ? focus_areas[i].focusarea_longdescription.Substring(0, 250) + "..." : focus_areas[i].focusarea_longdescription;
                    row.focusarea_mr_desc = focus_areas[i].focusarea_monthly_report_description.Length > 250 ? focus_areas[i].focusarea_monthly_report_description.Substring(0, 250) + "..." : focus_areas[i].focusarea_monthly_report_description;
                    row.focusarea_mr_desc2 = focus_areas[i].focusarea_monthly_report_description2.Length > 250 ? focus_areas[i].focusarea_monthly_report_description2.Substring(0, 250) + "..." : focus_areas[i].focusarea_monthly_report_description2;
                    row.focusarea_longdescfull = focus_areas[i].focusarea_longdescription;
                    row.focusarea_mr_descfull = focus_areas[i].focusarea_monthly_report_description;
                    row.focusarea_mr_desc2full = focus_areas[i].focusarea_monthly_report_description2;
                    gridData.Add(row);
                }

                List<GridColumnHelper> column = FormatColumnsofFocusAreaGrid(userId, langStringValuesBAtype);
                dynamic dColumn = JToken.FromObject(column);
                result.Add("header", headerTitle);
                result.Add("columns", dColumn);
                result.Add("data", gridData);
                return result;
            }
            catch (Exception)
            {
                return null;
            }
        }

        public async Task<JArray> GetFocusAreasAsync(string userId, int budgetYear)
        {
            try
            {
                TenantDBContext budgetManagementDbContext = await _utility.GetTenantDBContextAsync();
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                Dictionary<string, clsLanguageString> langStringValuesBAtype = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetManagement");

                dynamic result = new JObject();

                var focus_areas = await budgetManagementDbContext.tco_focusarea.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear).OrderBy(x => x.sort_order).ThenBy(x => x.pk_id).ToListAsync();
                dynamic arr = new JArray();

                foreach (var f in focus_areas)
                {
                    dynamic obj = new JObject();
                    obj.key = f.pk_id;
                    obj.value = f.focusarea_description;
                    arr.Add(obj);
                }

                return arr;
            }
            catch (Exception)
            {
                return null;
            }
        }

        public async Task<JArray> GetFocusAreaTextBoxesAsync(string userId, int budgetYear, int focusAreaId)
        {
            TenantDBContext budgetManagementDbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            Dictionary<string, clsLanguageString> langStringValuesBAtype = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetManagement");

            var focusAreaRow = await budgetManagementDbContext.tco_focusarea.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                        && x.budget_year == budgetYear
                                                                                        && x.pk_id == focusAreaId);

            var templateToApply = await _publishTemplateManager.GetMasterTemplateAsync(userId, budgetYear, PublishTreeType.BMFocusAreaTemplate, 0);

            var dataset = await (from a in budgetManagementDbContext.tco_focus_area_custom_node_master
                                 join b in budgetManagementDbContext.TcoCustomNode
                                        on new { a = a.fk_tenant_id, b = a.tree_type, c = a.cn_instance_id }
                                    equals new { a = b.FkTenantId, b = b.TreeType, c = b.PkNodeId }
                                 where a.fk_tenant_id == userDetails.tenant_id
                                 && b.BudgetYear == budgetYear
                                 && a.tree_type == "BudgetManagement"
                                 && a.focusarea_id == focusAreaId
                                 select new
                                 {
                                     a.pk_master_node_id,
                                     nodeId = b.PkNodeId,
                                     nodeName = b.NodeTitle,
                                     description = b.Description
                                 }).ToListAsync();

            dynamic arr = new JArray();
            dynamic obj = new JObject();

            if (focusAreaRow != null)
            {
                if (focusAreaRow.desc_guid == null || focusAreaRow.desc_guid.Value == Guid.Empty)
                {
                    focusAreaRow.desc_guid = Guid.NewGuid();
                    await budgetManagementDbContext.SaveChangesAsync();
                }

                obj = new JObject();
                await GetFocusAreaRowDescription(userId, budgetYear, userDetails, focusAreaRow, obj, langStringValuesBAtype);
                arr.Add(obj);
            }

            if (templateToApply != null && templateToApply.Tree.Any())
            {
                foreach (var n in templateToApply.Tree)
                {
                    if (!(n.type == "FocusAreaLongDescription" || n.type == "GoalsCityLvl" || n.type == "TargetsCityLvl" || n.type == "AssignmentsCityLvl" || n.type == "StrategyCityLvl"))
                    {
                        var node = dataset.FirstOrDefault(x => x.pk_master_node_id.ToString() == n.id);
                        if (node != null)
                        {
                            obj = new JObject();
                            Guid nodeId = node.nodeId;
                            string nodeName = node.nodeName;
                            string nodeDesc = node.description;

                            await GetFocusAreaCustomNodeDesc(userId, budgetYear, userDetails, obj, nodeId, nodeName, nodeDesc);
                            arr.Add(obj);
                        }
                    }
                }
            }

            return arr;
        }

        private async Task GetFocusAreaCustomNodeDesc(string userId, int budgetYear, UserData userDetails, dynamic obj, Guid nodeId, string nodeName, string nodeDesc)
        {
            var description = await _utility.GetValidDescription(userId, nodeId, nodeDesc, budgetYear, nameof(GetFocusAreaCustomNodeDesc), PublishTreeType.BudgetManagement);
            obj.nodeId = nodeId;
            obj.nodeName = nodeName;
            obj.description = description;
            obj.userCommentsPreview = _editorExtensions.SanitizeDescriptionForDeletedSuggestions(description);
            obj.type = "CustomNode";
        }

        private async Task GetFocusAreaRowDescription(string userId,
                                                int budgetYear, UserData userDetails, tco_focusarea? focusAreaRow, dynamic obj,
                                                Dictionary<string, clsLanguageString> langStringValuesBAtype)
        {
            var description = await _utility.GetValidDescription(userId, focusAreaRow.desc_guid ?? Guid.Empty, focusAreaRow.focusarea_longdescription, budgetYear, nameof(GetFocusAreaRowDescription), PublishTreeType.BudgetManagement);
            obj.nodeId = focusAreaRow.desc_guid ?? Guid.Empty;
            obj.nodeName = ((langStringValuesBAtype.FirstOrDefault(v => v.Key == "BM_ficus_area_long_desc_title")).Value).LangText;
            obj.description = description;
            obj.userCommentsPreview = _editorExtensions.SanitizeDescriptionForDeletedSuggestions(description);
            obj.type = "FocusAreaLongDescription";
        }

        public async Task<string> SaveFocusAreaTestBoxDescriptionAsync(string userId, int budgetYear, int focusAreaId, string type, Guid nodeId, string description, List<string> connectedUserNames, bool LogHistory)
        {
            TenantDBContext budgetManagementDbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            if (type == "FocusAreaLongDescription")
            {
                var focusAreaRow = await budgetManagementDbContext.tco_focusarea.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                        && x.budget_year == budgetYear
                                                                                        && x.pk_id == focusAreaId);
                if (focusAreaRow != null)
                {
                    focusAreaRow.focusarea_longdescription = string.IsNullOrEmpty(description) ? string.Empty : description.Trim();
                    focusAreaRow.updated = DateTime.UtcNow;
                    focusAreaRow.updated_by = userDetails.pk_id;
                    await budgetManagementDbContext.SaveChangesAsync();
                    if (LogHistory)
                        await _utility.SaveTextLogAsync(userId, focusAreaRow.desc_guid.Value, description, null, "", connectedUserNames);
                }

                return "success";
            }
            else if (type == "CustomNode")
            {
                var dataset = await (from a in budgetManagementDbContext.tco_focus_area_custom_node_master
                                     join b in budgetManagementDbContext.TcoCustomNode
                                            on new { a = a.fk_tenant_id, b = a.tree_type, c = a.cn_instance_id }
                                        equals new { a = b.FkTenantId, b = b.TreeType, c = b.PkNodeId }
                                     where a.fk_tenant_id == userDetails.tenant_id
                                     && b.BudgetYear == budgetYear
                                     && a.tree_type == "BudgetManagement"
                                     && a.focusarea_id == focusAreaId
                                     && b.PkNodeId == nodeId
                                     select b).FirstOrDefaultAsync();

                if (dataset != null)
                {
                    dataset.Description = description;
                    dataset.Updated = DateTime.UtcNow;
                    dataset.UpdatedBy = userDetails.pk_id;

                    await budgetManagementDbContext.SaveChangesAsync();
                    if (LogHistory)
                        await _utility.SaveTextLogAsync(userId, dataset.PkNodeId, description, null, "", connectedUserNames);
                }

                return "success";
            }
            else
            {
                return "";
            }
        }

        public async Task<JObject> LoadFocusAreaDetailsAsync(string userId, int focusAreaId, int budgetYear)
        {
            try
            {
                TenantDBContext budgetManagementDbContext = await _utility.GetTenantDBContextAsync();
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);

                dynamic result = new JObject();
                var focus_area_flag = await budgetManagementDbContext.tco_focusarea.FirstOrDefaultAsync(x => x.pk_id == focusAreaId && x.budget_year == budgetYear);

                if (focus_area_flag != null)
                {
                    if (focus_area_flag.desc_guid == null)
                    {
                        focus_area_flag.desc_guid = Guid.NewGuid();
                        await budgetManagementDbContext.SaveChangesAsync();
                    }
                    result.Add("focusAreaText", focus_area_flag.focusarea_description);
                    result.Add("focusarea_longdesc", focus_area_flag.focusarea_longdescription);
                    result.Add("focusarea_mr_desc", focus_area_flag.focusarea_monthly_report_description);
                    result.Add("focusarea_mr_desc2", focus_area_flag.focusarea_monthly_report_description2);
                    result.Add("focusarea_mr_desc_id", focus_area_flag.desc_guid);
                    result.Add("focusarea_longdesc_user_comments_preview", _editorExtensions.SanitizeDescriptionForDeletedSuggestions(focus_area_flag.focusarea_longdescription));
                    result.Add("sortOrder", focus_area_flag.sort_order);
                }
                else
                {
                    result.Add("focusAreaText", string.Empty);
                    result.Add("focusarea_longdesc", string.Empty);
                    result.Add("focusarea_mr_desc", string.Empty);
                    result.Add("focusarea_mr_desc2", string.Empty);
                    result.Add("focusarea_mr_desc_id", "");
                    result.Add("sortOrder", 0);
                }

                return result;
            }
            catch (Exception)
            {
                return null;
            }
        }

        public async Task<bool> SaveFocusAreaDetailsAsync(string userId, FocusAreaHelper input)
        {
            TenantDBContext budgetManagementDbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            var focus_area_flag = await budgetManagementDbContext.tco_focusarea.FirstOrDefaultAsync(x => x.pk_id == input.id && x.budget_year == input.budgetYear);

            if (focus_area_flag != null)
            {
                focus_area_flag.focusarea_description = input.focusAreaText;
                focus_area_flag.focusarea_longdescription = input.focusarea_longdesc == null ? string.Empty : input.focusarea_longdesc;
                focus_area_flag.focusarea_monthly_report_description = input.focusarea_mr_desc == null ? string.Empty : input.focusarea_mr_desc;
                focus_area_flag.focusarea_monthly_report_description2 = input.focusarea_mr_desc2 == null ? string.Empty : input.focusarea_mr_desc2;
                focus_area_flag.updated = DateTime.UtcNow;
                focus_area_flag.updated_by = userDetails.pk_id;
                focus_area_flag.sort_order = input.sortOrder;
                focus_area_flag.sync_status = input.syncStatus == null ? focus_area_flag.sync_status : input.syncStatus.key;
                await budgetManagementDbContext.SaveChangesAsync();
            }
            else
            {
                budgetManagementDbContext.tco_focusarea.Add(new tco_focusarea()
                {
                    fk_tenant_id = userDetails.tenant_id,
                    budget_year = input.budgetYear,
                    focusarea_description = input.focusAreaText,
                    focusarea_longdescription = input.focusarea_longdesc == null ? string.Empty : input.focusarea_longdesc,
                    focusarea_monthly_report_description = input.focusarea_mr_desc == null ? string.Empty : input.focusarea_mr_desc,
                    focusarea_monthly_report_description2 = input.focusarea_mr_desc2 == null ? string.Empty : input.focusarea_mr_desc2,
                    updated = DateTime.UtcNow,
                    updated_by = userDetails.pk_id,
                    sort_order = input.sortOrder,
                    sync_status = input.syncStatus.key
                });

                await budgetManagementDbContext.SaveChangesAsync();

                PublishTemplateHelper pubTemplateHelperFa = await _publishTemplateManager.GetMasterTemplateAsync(userId, input.budgetYear, PublishTreeType.BMFocusAreaTemplate, 0);
                if (pubTemplateHelperFa != null)
                {
                    _bmFocusArea.CreateFocusAreaCustomNodeInstances(userId, pubTemplateHelperFa);
                }
            }
            return true;
        }

        public async Task<bool> DeleteFocusAreaDetailsAsync(string userId, int focusAreaId, int budgetYear)
        {
            TenantDBContext budgetManagementDbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            var focus_area_flag = await budgetManagementDbContext.tco_focusarea.FirstOrDefaultAsync(x => x.pk_id == focusAreaId && x.budget_year == budgetYear);

            if (focus_area_flag != null)
            {
                budgetManagementDbContext.tco_focusarea.Remove(focus_area_flag);
                await budgetManagementDbContext.SaveChangesAsync();

                // deleting entry from plan finplan focus area mapping table
                var planMappingFocusArea = await budgetManagementDbContext.tpl_tfp_focusarea_mapping.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear && x.fk_finplan_focusArea_id == focusAreaId);
                if (planMappingFocusArea != null)
                {
                    budgetManagementDbContext.tpl_tfp_focusarea_mapping.Remove(planMappingFocusArea);
                    await budgetManagementDbContext.SaveChangesAsync();
                }
                return true;
            }
            else
            {
                return false;
            }
        }

        private List<GridColumnHelper> FormatColumnsofFocusAreaGrid(string userId, Dictionary<string, clsLanguageString> langStrings)
        {
            List<GridColumnHelper> formattedColumns = new List<GridColumnHelper>();

            ColumnStyleHelper csh = new ColumnStyleHelper();
            ColumnStyleHelper cshHeader = new ColumnStyleHelper();
            commandHelper command = new commandHelper();

            csh.style = "text-align:left; border-left:none;width:0px;";
            cshHeader.style = "text-align:left; border-left:none;width:0px;";
            GridColumnHelper columnInfo = new GridColumnHelper
            {
                field = "id",
                title = string.Empty,

                encoded = true,
                width = 0,
                hidden = true,
                attributes = csh,
                headerAttributes = cshHeader,
            };

            csh = new ColumnStyleHelper();
            cshHeader = new ColumnStyleHelper();
            csh.style = "text-align:left;white-space:normal;border-left: none;width:200px;";
            cshHeader.style = "text-align:left; border-left: none;width:200px;";
            columnInfo = new GridColumnHelper
            {
                field = "focusAreaText",
                title =
                    ((langStrings.FirstOrDefault(v => v.Key == "BM_FA_col_text")).Value)
                        .LangText,
                width = 200,
                encoded = false,
                template = "<span class='focus-area-template'></span>",
                attributes = csh,
                headerAttributes = cshHeader,
            };
            formattedColumns.Add(columnInfo);

            csh = new ColumnStyleHelper();
            cshHeader = new ColumnStyleHelper();
            csh.style = "text-align:center;white-space:normal;border-left: none;width:30px;";
            cshHeader.style = "text-align:center; border-left: none;width:30px;";
            command.name = "destroydata";
            command.className = "bm-foc-delete";
            columnInfo = new GridColumnHelper
            {
                field = "deleteField",
                title = " ",
                width = 30,
                encoded = false,
                command = command,
                attributes = csh,
                headerAttributes = cshHeader,
            };
            formattedColumns.Add(columnInfo);

            return formattedColumns;
        }

        public dynamic Get1BReportFromViewForDocSummary(string userId, string reportType, bool divideByMillions, int budgetYear, string budgetPhaseId, bool showOnlyModified)
        {
            TenantDBContext budgetManagementDbContext = _utility.GetTenantDBContext();
            budgetManagementDbContext.Database.SetCommandTimeout(300);
            UserData userDetails = _utility.GetUserDetails(userId);
            int budYear = budgetYear;
            Dictionary<string, clsLanguageString> langStringValuesBAtype = new Dictionary<string, clsLanguageString>();
            langStringValuesBAtype = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "BudgetManagement");

            var resultWithChangeId = (from a in budgetManagementDbContext.vw_doc_1B_finplan
                                      where a.fk_tenant_id == userDetails.tenant_id && a.budget_year == budYear
                                      group a by new { a.aggregate_id, a.aggregate_name, a.fk_change_id } into g
                                      select new clsReport
                                      {
                                          serviceAreaId = g.Key.aggregate_id,
                                          ServiceAreaName = g.Key.aggregate_name,
                                          ledgeryear = g.Sum(x => x.gl_amount),
                                          currentbudgetyear = g.Sum(x => x.budget_amount),
                                          Year1 = g.Sum(x => x.year_1_amount),
                                          Year2 = g.Sum(x => x.year_2_amount),
                                          Year3 = g.Sum(x => x.year_3_amount),
                                          Year4 = g.Sum(x => x.year_4_amount),
                                          changeId = g.Key.fk_change_id
                                      }).ToList();

            if (showOnlyModified)
            {
                List<int> budgetPhaseChangeId = _consequentAdjustedBudget.GetChangeDataUsingBudgetPhase(budgetPhaseId, userId, showOnlyModified, budYear);
                resultWithChangeId = resultWithChangeId.Where(x => budgetPhaseChangeId.Contains(x.changeId)).ToList();
            }

            var resultWithNoCheckOnMillions = (from a in resultWithChangeId
                                               group a by new { a.serviceAreaId, a.ServiceAreaName } into g
                                               select new clsReport
                                               {
                                                   serviceAreaId = g.Key.serviceAreaId,
                                                   ServiceAreaName = g.Key.ServiceAreaName,
                                                   ledgeryear = g.Sum(x => x.ledgeryear),
                                                   currentbudgetyear = g.Sum(x => x.currentbudgetyear),
                                                   Year1 = g.Sum(x => x.Year1),
                                                   Year2 = g.Sum(x => x.Year2),
                                                   Year3 = g.Sum(x => x.Year3),
                                                   Year4 = g.Sum(x => x.Year4)
                                               }).ToList();

            var resultWithCheckOnMillions = (from a in resultWithNoCheckOnMillions
                                             group a by new { a.serviceAreaId, a.ServiceAreaName } into g
                                             select new clsReport
                                             {
                                                 serviceAreaId = string.IsNullOrEmpty(g.Key.serviceAreaId) ? string.Empty : g.Key.serviceAreaId,
                                                 ServiceAreaName = g.Key.ServiceAreaName,
                                                 ledgeryear = divideByMillions == true ? g.Sum(x => x.ledgeryear) / 1000000 : g.Sum(x => x.ledgeryear) / 1000,
                                                 currentbudgetyear = divideByMillions == true ? g.Sum(x => x.currentbudgetyear) / 1000000 : g.Sum(x => x.currentbudgetyear) / 1000,
                                                 Year1 = divideByMillions == true ? g.Sum(x => x.Year1) / 1000000 : g.Sum(x => x.Year1) / 1000,
                                                 Year2 = divideByMillions == true ? g.Sum(x => x.Year2) / 1000000 : g.Sum(x => x.Year2) / 1000,
                                                 Year3 = divideByMillions == true ? g.Sum(x => x.Year3) / 1000000 : g.Sum(x => x.Year3) / 1000,
                                                 Year4 = divideByMillions == true ? g.Sum(x => x.Year4) / 1000000 : g.Sum(x => x.Year4) / 1000
                                             }).ToList();

            var data = (from r in resultWithCheckOnMillions
                        orderby r.serviceAreaId
                        select new
                        {
                            ServiceAreaName = r.ServiceAreaName,
                            ledgeryear = r.ledgeryear,
                            currentbudgetyear = r.currentbudgetyear,
                            Year1 = r.Year1,
                            Year2 = r.Year2,
                            Year3 = r.Year3,
                            Year4 = r.Year4
                        }).ToList();

            dynamic JGetResult = new JObject();
            dynamic dataArray = new JArray();
            dataArray = JArray.FromObject(data);
            decimal ledgeryeartotal = 0, currentbudgetyeartotal = 0, year1total = 0, year2total = 0, year3total = 0, year4total = 0;
            foreach (var item in data)
            {
                ledgeryeartotal = ledgeryeartotal + item.ledgeryear;
                currentbudgetyeartotal = currentbudgetyeartotal + item.currentbudgetyear;
                year1total = year1total + item.Year1;
                year2total = year2total + item.Year2;
                year3total = year3total + item.Year3;
                year4total = year4total + item.Year4;
            }

            dynamic TotalObject = new JObject();
            TotalObject.Add("ServiceAreaName", (langStringValuesBAtype["SUM_TOTAL_1B"]).LangText);
            TotalObject.Add("ledgeryear", ledgeryeartotal);
            TotalObject.Add("currentbudgetyear", currentbudgetyeartotal);
            TotalObject.Add("Year1", year1total);
            TotalObject.Add("Year2", year2total);
            TotalObject.Add("Year3", year3total);
            TotalObject.Add("Year4", year4total);
            dataArray.Add(TotalObject);
            JGetResult.Add("Data", dataArray);
            return JGetResult;
        }

        private async Task DistributeAndUpdateBudgetLimitsAsync(string userId, int budgetYear, string orgId, int actionType, ServiceAreaBudget newbudget)
        {
            TenantDBContext dBContext = await _utility.GetTenantDBContextAsync();
            dBContext.Database.SetCommandTimeout(300);
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));

            List<int> actionTypes = new List<int>() { 20, 30, 40, 60, 70, 80, 90, 100, 101 };
            var dbDataset = await (from th in dBContext.tfp_trans_header
                                   join td in dBContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                                                                     equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                                   join ac in dBContext.tco_fp_alter_codes on new { a = td.fk_tenant_id, b = td.fk_alter_code }
                                                                       equals new { a = ac.fk_tenant_id, b = ac.pk_alter_code } into ac1
                                   from ac2 in ac1.DefaultIfEmpty()
                                   where th.fk_tenant_id == userDetails.tenant_id
                                       && (td.budget_year == budgetYear)
                                       && th.action_type >= 5
                                       && !actionTypes.Contains(th.action_type)
                                   select new
                                   {
                                       accountCode = td.fk_account_code,
                                       departmentCode = td.department_code,
                                       functionCode = td.function_code,
                                       year1Amount = td.year_1_amount,
                                       year2Amount = td.year_2_amount,
                                       year3Amount = td.year_3_amount,
                                       year4Amount = td.year_4_amount,
                                       sumCode = string.IsNullOrEmpty(ac2.sum_code) ? string.Empty : ac2.sum_code,
                                       cabFlag = ac2.cab_flag.Equals(null) ? 0 : ac2.cab_flag
                                   }).ToListAsync();

            List<clsOrgStructure> lstOrgStructure = await _utility.GetTenantOrgStructureAsync(orgVersionContent, userId);
            clsOrgStructureLevelDetails tenantOrgLevelDetails = _utility.GetTenantOrgLevelDetails(lstOrgStructure);
            List<List<string>> DepartmentsAndFunctions = _utility.GetDepartmentsAndFunctionsForOrgStructure(lstOrgStructure, orgId, null, tenantOrgLevelDetails.level1, tenantOrgLevelDetails.level2);
            List<string> lstAllDepartments = DepartmentsAndFunctions[0];
            List<string> lstAllFunctions = DepartmentsAndFunctions[1];

            if (lstAllDepartments.Where(x => !string.IsNullOrEmpty(x)).Count() > 0 && lstAllFunctions.Where(x => !string.IsNullOrEmpty(x)).Count() > 0)
            {
                dbDataset = dbDataset.Where(y => lstAllDepartments.Contains(y.departmentCode) && lstAllFunctions.Contains(y.functionCode)).ToList();
            }
            else if (lstAllFunctions.Where(x => !string.IsNullOrEmpty(x)).Count() > 0)
            {
                dbDataset = dbDataset.Where(y => lstAllFunctions.Contains(y.functionCode)).ToList();
            }
            else if (lstAllDepartments.Where(x => !string.IsNullOrEmpty(x)).Count() > 0)
            {
                dbDataset = dbDataset.Where(y => lstAllDepartments.Contains(y.departmentCode)).ToList();
            }

            List<clsServiceAreaData> mainDataset = (from d in dbDataset
                                                    select new clsServiceAreaData
                                                    {
                                                        orgId = "",
                                                        orgName = "",
                                                        serviceId = "",
                                                        serviceName = "",
                                                        actionType = 0,
                                                        actionId = 0,
                                                        accountCode = d.accountCode,
                                                        departmentCode = d.departmentCode,
                                                        functionCode = d.functionCode,
                                                        year1Amount = d.year1Amount,
                                                        year2Amount = d.year2Amount,
                                                        year3Amount = d.year3Amount,
                                                        year4Amount = d.year4Amount,
                                                        description = string.Empty,
                                                        lineOrderId = 0,
                                                        isManuallyAdded = 0,
                                                        sumCode = d.sumCode,
                                                        sumDescription = string.Empty,
                                                        limitCode = string.Empty,
                                                        limitDescription = string.Empty,
                                                        cabFlag = d.cabFlag
                                                    }).ToList();
            string firstLevel = string.Empty;
            string secondLevel = string.Empty;

            if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_1").Where(a => !string.IsNullOrEmpty(a.departmentCode)).Select(y => y.departmentCode).Count() > 0)
            {
                firstLevel = "dept";
            }
            else if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_1").Count(y => !string.IsNullOrEmpty(y.functionCode)) > 0)
            {
                firstLevel = "function";
            }

            if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_2").Where(a => !string.IsNullOrEmpty(a.departmentCode)).Select(y => y.departmentCode).Count() > 0)
            {
                secondLevel = "dept";
            }
            else if (lstOrgStructure.Where(z => z.type == "FINPLAN_LEVEL_2").Where(a => !string.IsNullOrEmpty(a.functionCode)).Select(y => y.functionCode).Count() > 0)
            {
                secondLevel = "function";
            }
            mainDataset = _utility.AssignOrgIdAndServiceIdToResultSet(orgVersionContent, mainDataset, lstOrgStructure, firstLevel, secondLevel);
            mainDataset = mainDataset.Where(x => x.orgId == orgId).ToList();
            List<string> sumCodesWithCabFlag1 = mainDataset.Where(w => w.cabFlag == 1 && !string.IsNullOrEmpty(w.sumCode)).Select(x => x.sumCode).Distinct().OrderBy(z => z).ToList();

            List<clsOrgIdsAndServiceIds> lstResult = await _consequentAdjustedBudget.GetOrgIdsAndServiceIdsAsync(userId, false, budgetYear);
            var orgIdsAndServiceIds = lstResult.FirstOrDefault(x => x.parentId == null && x.orgId == orgId);
            int id = orgIdsAndServiceIds.id;
            List<clsOrgIdsAndServiceIds> orgIds =
                lstResult.Where(x => x.parentId == id.ToString() && !string.IsNullOrEmpty(x.orgId)).ToList();

            decimal totalOrgLevelYear1 = Convert.ToInt64(Math.Round(mainDataset.Where(x => sumCodesWithCabFlag1.Contains(x.sumCode)).Sum(x => x.year1Amount)));
            decimal totalOrgLevelYear2 = Convert.ToInt64(Math.Round(mainDataset.Where(x => sumCodesWithCabFlag1.Contains(x.sumCode)).Sum(x => x.year2Amount)));
            decimal totalOrgLevelYear3 = Convert.ToInt64(Math.Round(mainDataset.Where(x => sumCodesWithCabFlag1.Contains(x.sumCode)).Sum(x => x.year3Amount)));
            decimal totalOrgLevelYear4 = Convert.ToInt64(Math.Round(mainDataset.Where(x => sumCodesWithCabFlag1.Contains(x.sumCode)).Sum(x => x.year4Amount)));

            decimal newOrgLevelTotalYear1 = 0, newOrgLevelTotalYear2 = 0, newOrgLevelTotalYear3 = 0, newOrgLevelTotalYear4 = 0;
            int totalOrgIds = orgIds.Count();
            int counter = 1;
            foreach (var item in orgIds)
            {
                decimal totalServiceIdYear1 = Convert.ToInt64(Math.Round(mainDataset.Where(x => sumCodesWithCabFlag1.Contains(x.sumCode) && x.serviceId == item.orgId).Sum(x => x.year1Amount)));
                decimal totalServiceIdYear2 = Convert.ToInt64(Math.Round(mainDataset.Where(x => sumCodesWithCabFlag1.Contains(x.sumCode) && x.serviceId == item.orgId).Sum(x => x.year2Amount)));
                decimal totalServiceIdYear3 = Convert.ToInt64(Math.Round(mainDataset.Where(x => sumCodesWithCabFlag1.Contains(x.sumCode) && x.serviceId == item.orgId).Sum(x => x.year3Amount)));
                decimal totalServiceIdYear4 = Convert.ToInt64(Math.Round(mainDataset.Where(x => sumCodesWithCabFlag1.Contains(x.sumCode) && x.serviceId == item.orgId).Sum(x => x.year4Amount)));

                decimal year1Percent = totalOrgLevelYear1 != 0 ? Math.Round(totalServiceIdYear1 / totalOrgLevelYear1, 2) : 0;
                decimal year2Percent = totalOrgLevelYear2 != 0 ? Math.Round(totalServiceIdYear2 / totalOrgLevelYear2, 2) : 0;
                decimal year3Percent = totalOrgLevelYear3 != 0 ? Math.Round(totalServiceIdYear3 / totalOrgLevelYear3, 2) : 0;
                decimal year4Percent = totalOrgLevelYear4 != 0 ? Math.Round(totalServiceIdYear4 / totalOrgLevelYear4, 2) : 0;

                decimal updatedNewBudgetYear1 = 0;
                decimal updatedNewBudgetYear2 = 0;
                decimal updatedNewBudgetYear3 = 0;
                decimal updatedNewBudgetYear4 = 0;
                if (totalOrgIds == counter)
                {
                    updatedNewBudgetYear1 = Math.Round(newbudget.Year1 - newOrgLevelTotalYear1);
                    updatedNewBudgetYear2 = Math.Round(newbudget.Year2 - newOrgLevelTotalYear2);
                    updatedNewBudgetYear3 = Math.Round(newbudget.Year3 - newOrgLevelTotalYear3);
                    updatedNewBudgetYear4 = Math.Round(newbudget.Year4 - newOrgLevelTotalYear4);
                }
                else
                {
                    updatedNewBudgetYear1 = Math.Round(newbudget.Year1 * year1Percent);
                    updatedNewBudgetYear2 = Math.Round(newbudget.Year2 * year2Percent);
                    updatedNewBudgetYear3 = Math.Round(newbudget.Year3 * year3Percent);
                    updatedNewBudgetYear4 = Math.Round(newbudget.Year4 * year4Percent);
                }
                tfp_temp_budget_limits existingData = await dBContext.tfp_temp_budget_limits.FirstOrDefaultAsync(x => x.fp_level_1_value == orgId && x.fp_level_2_value == item.orgId && x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear && x.action_type == actionType);
                newOrgLevelTotalYear1 = newOrgLevelTotalYear1 + updatedNewBudgetYear1;
                newOrgLevelTotalYear2 = newOrgLevelTotalYear2 + updatedNewBudgetYear2;
                newOrgLevelTotalYear3 = newOrgLevelTotalYear3 + updatedNewBudgetYear3;
                newOrgLevelTotalYear4 = newOrgLevelTotalYear4 + updatedNewBudgetYear4;
                if (existingData != null)
                {
                    existingData.year_1_limit = updatedNewBudgetYear1 * 1000;
                    existingData.year_2_limit = updatedNewBudgetYear2 * 1000;
                    existingData.year_3_limit = updatedNewBudgetYear3 * 1000;
                    existingData.year_4_limit = updatedNewBudgetYear4 * 1000;
                    existingData.updated = DateTime.UtcNow;
                    existingData.updated_by = userDetails.pk_id;
                }
                else
                {
                    tfp_temp_budget_limits tempDetail = new tfp_temp_budget_limits();
                    tempDetail.fk_tenant_id = userDetails.tenant_id;
                    tempDetail.budget_year = budgetYear;
                    tempDetail.fp_level_1_value = orgId;
                    tempDetail.fp_level_2_value = item.orgId;
                    tempDetail.action_type = actionType;
                    tempDetail.year_1_limit = updatedNewBudgetYear1 * 1000;
                    tempDetail.year_2_limit = updatedNewBudgetYear2 * 1000;
                    tempDetail.year_3_limit = updatedNewBudgetYear3 * 1000;
                    tempDetail.year_4_limit = updatedNewBudgetYear4 * 1000;
                    tempDetail.updated = DateTime.UtcNow;
                    tempDetail.updated_by = userDetails.pk_id;
                    dBContext.tfp_temp_budget_limits.Add(tempDetail);
                }
                await dBContext.SaveChangesAsync();
                counter++;
            }
        }

        public List<cabGridHelper> CabGridData(string userId, string orgId, string serviceId, int? orgLevel, int budgetYear, List<int> budgetPhaseChangeId, bool useBudPhase = false, string chapterId = "")
        {
            return CabGridDataAsync(userId, orgId, serviceId, orgLevel, budgetYear, budgetPhaseChangeId, useBudPhase, chapterId).GetAwaiter().GetResult();
        }

        public async Task<List<cabGridHelper>> CabGridDataAsync(string userId, string orgId, string serviceId, int? orgLevel, int budgetYear, List<int> budgetPhaseChangeId, bool useBudPhase = false, string chapterId = "")
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext budgetManagementDBContext = await _utility.GetTenantDBContextAsync();
            List<string> lstAllDepartments = new List<string>();
            List<string> lstAllFunctions = new List<string>();

            var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));

            List<clsOrgStructure> lstOrgStructure = await _utility.GetTenantOrgStructureAsync(orgVersionContent, userId);
            clsOrgStructureLevelDetails tenantOrgLevelDetails = _utility.GetTenantOrgLevelDetails(lstOrgStructure);

            List<List<string>> DepartmentsAndFunctions = _utility.GetDepartmentsAndFunctionsForOrgStructure(lstOrgStructure, orgId, serviceId, tenantOrgLevelDetails.level1, tenantOrgLevelDetails.level2);

            lstAllDepartments = DepartmentsAndFunctions[0];
            lstAllFunctions = DepartmentsAndFunctions[1];

            if (orgLevel != null)
            {
                lstAllDepartments = await _utility.GetAllDepartmentsIrrespectiveOfAccessAsync(orgVersionContent, userId, orgId, orgLevel.Value, serviceId, tenantOrgLevelDetails.level1, tenantOrgLevelDetails.level2);
            }

            if (!string.IsNullOrEmpty(chapterId))
            {
                lstAllDepartments = await _finUtility.GetAttributeConnectedDepartmentsForSelectedOrgAsync(userId, budgetYear, orgId, 1, chapterId);
                lstAllFunctions = new();
            }

            var baseQuery = budgetManagementDBContext.tfp_trans_header
                                            .Join(budgetManagementDBContext.tfp_trans_detail,
                                                  th => th.pk_action_id,
                                                  td => td.fk_action_id,
                                                  (th, td) => new { th, td })
                                            .Join(budgetManagementDBContext.tfp_budget_changes,
                                                  tthtd => tthtd.td.fk_change_id,
                                                  bc => bc.pk_change_id,
                                                  (tthtd, bc) => new { tthtd.th, tthtd.td, bc })
                                            .Join(budgetManagementDBContext.tco_budget_phase,
                                                  thtdbc => new { a = thtdbc.bc.fk_tenant_id, b = thtdbc.bc.org_budget_flag, c = thtdbc.bc.fk_budget_phase_id },
                                                  bp => new { a = bp.fk_tenant_id, b = bp.org_budget_flag, c = bp.pk_budget_phase_id },
                                                  (thtdbc, bp) => new { thtdbc.th, thtdbc.td, thtdbc.bc, bp })
                                            .Join(budgetManagementDBContext.tco_fp_alter_codes,
                                                  thtdbcbp => new { a = thtdbcbp.td.fk_tenant_id, b = thtdbcbp.td.fk_alter_code },
                                                  ac => new { a = ac.fk_tenant_id, b = ac.pk_alter_code },
                                                  (thtdbcbp, ac) => new { thtdbcbp.th, thtdbcbp.td, thtdbcbp.bc, thtdbcbp.bp, ac })
                                            .Where(w => w.th.fk_tenant_id == w.td.fk_tenant_id
                                                   && w.th.fk_tenant_id == userDetails.tenant_id && w.td.budget_year == budgetYear);

            if (lstAllDepartments.Count() > 0)
            {
                baseQuery = baseQuery.Where(w => lstAllDepartments.Contains(w.td.department_code));
            }

            if (lstAllFunctions.Count() > 0)
            {
                baseQuery = baseQuery.Where(w => lstAllFunctions.Contains(w.td.function_code));
            }

            if (budgetPhaseChangeId != null && budgetPhaseChangeId.Any())
            {
                baseQuery = baseQuery.Where(w => budgetPhaseChangeId.Contains(w.td.fk_change_id));
            }

            List<cabGridHelper> serviceAreaBudgetData = await baseQuery.Select(s => new cabGridHelper
            {
                actionId = s.th.pk_action_id,
                actionType = s.th.action_type,
                actionName = s.th.description,
                lineOrder = s.th.line_order,
                isManuallyAdded = s.th.isManuallyAdded,
                departmentCode = s.td.department_code,
                functionCode = s.td.function_code,
                Year1 = s.td.year_1_amount,
                Year2 = s.td.year_2_amount,
                Year3 = s.td.year_3_amount,
                Year4 = s.td.year_4_amount,
                sumCode = s.ac == null ? string.Empty : s.ac.sum_code,
                sumDescription = s.ac == null ? string.Empty : s.ac.sum_description,
                limitCode = s.ac == null ? string.Empty : s.ac.limit_code,
                limitDescription = s.ac == null ? string.Empty : s.ac.limit_description,
                altercode = s.td.fk_alter_code,
                updated = s.td.updated,
                cabFlag = s.ac == null ? 0 : s.ac.cab_flag,
                changeId = s.td.fk_change_id,
                budgetPhaseCode = s.bp.bud_phase_short_code,
                DisplayActionInBMDoc = s.th.display_cab_flag.HasValue ? s.th.display_cab_flag.Value : false,
                DisplayZeroAction = s.th.display_zero_action,
                sort_order = s.bp.sort_order,
                tech_adj = s.ac != null ? s.ac.tech_adj_flag : false,
                goals_id = s.th.goals_tags,
                goalId_tags = s.th.goals_tags.ToString(),
                Priority = s.th.priority,
                tags = s.th.tags,
                budgetPhaseId = s.bc.fk_budget_phase_id,
                budgetPhaseName = s.bp.description
            }).OrderByDescending(x => x.cabFlag).ThenBy(y => y.sumCode).ThenBy(z => z.limitCode).ToListAsync();

            return serviceAreaBudgetData;
        }

        public string GetGoalsDescriptions(string goalTags, List<tco_goals> goalsList)
        {
            string result = string.Empty;
            if (!string.IsNullOrEmpty(goalTags))
            {
                List<string> goals_ids = goalTags.Split(',').ToList();
                foreach (var id in goals_ids)
                {
                    if (goalsList.Any(x => x.pk_goal_id == Guid.Parse(id)))
                    {
                        result = string.Join(",", goalsList.FirstOrDefault(x => x.pk_goal_id == Guid.Parse(id)).goal_name);
                    }
                }
            }
            return result;
        }

        public async Task<string> FormatServiceAreaKeyFigures(string userId, string baseStoragePath, int budgetYear, string orgId, string serviceId)
        {
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            UserData userDetails = _utility.GetUserDetails(userId);
            Dictionary<string, clsLanguageString> lsDocExport = _utility.GetLanguageStrings(userDetails.language_preference,
                userDetails.user_name, "docexport");
            JArray dataArray = new JArray();

            List<InfoBlobSourceAndDestination> lstImages = new List<InfoBlobSourceAndDestination>();
            var yearToShowInWeb = _utility.GetParameterValue(userId, "BP_ACT_Web_Year");
            var finplanLevel1 = _utility.GetParameterValue(userId, "FINPLAN_LEVEL_1");
            var finplanLevel2 = _utility.GetParameterValue(userId, "FINPLAN_LEVEL_2");

          

            var datasetIndicator = (from a in dbContext.vwUserDetails
                                    where a.tenant_id == -1
                                    select new
                                    {
                                        key_name = "",
                                        value_type = "",
                                        image_icon = "",
                                        year_1_value = 0.0M,
                                        postfix = "",
                                        divide_by = 0,
                                        result = 0.0M,
                                        year1value = 0.0M,
                                        year2value = 0.0M,
                                        year3value = 0.0M,
                                        year4value = 0.0M,
                                        kostraYear1 = 0.0M,
                                        kostraYear2 = 0.0M,
                                        kostraYear3 = 0.0M,
                                        kostraYear4 = 0.0M,
                                        column_to_show_in_front_page = (!string.IsNullOrEmpty(yearToShowInWeb)) ? yearToShowInWeb : "year1value"
                                    }).ToList();

            if (await _utility.IsFeatureEnabled(FeatureFlags.ck_Editor))
            {
                serviceId = await _utility.RefineServiceIdValue(serviceId, userId);
            }


            if (!string.IsNullOrEmpty(finplanLevel1) && !string.IsNullOrEmpty(finplanLevel2) && finplanLevel1.ToLower().Contains("org_id") && finplanLevel2.ToLower().Contains("org_id"))
            {
                var level1 = finplanLevel1.ToLower() == "org_id_1" ? 1 :
                                finplanLevel1.ToLower() == "org_id_2" ? 2 :
                                finplanLevel1.ToLower() == "org_id_3" ? 3 :
                                finplanLevel1.ToLower() == "org_id_4" ? 1 : 5;

                var level2 = finplanLevel2.ToLower() == "org_id_1" ? 1 :
                                finplanLevel2.ToLower() == "org_id_2" ? 2 :
                                finplanLevel2.ToLower() == "org_id_3" ? 3 :
                                finplanLevel2.ToLower() == "org_id_4" ? 1 : 5;

              

                if (!string.IsNullOrEmpty(orgId) && !string.IsNullOrEmpty(serviceId))
                {
                    /*meaning, we need to use service id as orgId and value of level2 in condition*/                  

                    var newOrgId = serviceId;
                    var newOrgLevel = level2;

                    datasetIndicator = (from a in dbContext.tco_activity_indicator
                                        join b in dbContext.tco_indicator_setup on new { a = a.fk_tenant_id, b = a.fk_indicator_code }
                                                                            equals new { a = b.fk_tenant_id, b = b.pk_indicator_code }
                                        where a.fk_tenant_id == userDetails.tenant_id
                                        && a.budget_year == budgetYear
                                        && a.org_id == newOrgId
                                        && a.org_level == newOrgLevel
                                        && a.show_in_service_area == true
                                        select new
                                        {
                                            key_name = b.measurment_criteria,
                                            value_type = (!string.IsNullOrEmpty(b.value_type) && b.value_type.ToLower() == "text") ? "" : b.value_type,
                                            image_icon = string.IsNullOrEmpty(a.image_icon) ? "" : a.image_icon,
                                            year_1_value = a.year_1_value,
                                            postfix = string.Empty,
                                            divide_by = 1,
                                            result = a.result,
                                            year1value = a.year_1_value,
                                            year2value = a.year_2_value,
                                            year3value = a.year_3_value,
                                            year4value = a.year_4_value,
                                            kostraYear1 = a.kostra_1_value,
                                            kostraYear2 = a.kostra_2_value,
                                            kostraYear3 = a.kostra_3_value,
                                            kostraYear4 = a.kostra_4_value,
                                            column_to_show_in_front_page = (!string.IsNullOrEmpty(yearToShowInWeb)) ? yearToShowInWeb : "year1value"
                                        }).ToList();
                }
                else if (!string.IsNullOrEmpty(orgId) && string.IsNullOrEmpty(serviceId))
                {
                    var newOrgId = orgId;
                    var newOrgLevel = level1;

                    datasetIndicator = (from a in dbContext.tco_activity_indicator
                                        join b in dbContext.tco_indicator_setup on new { a = a.fk_tenant_id, b = a.fk_indicator_code }
                                                                            equals new { a = b.fk_tenant_id, b = b.pk_indicator_code }
                                        where a.fk_tenant_id == userDetails.tenant_id
                                        && a.budget_year == budgetYear
                                        && a.org_id == newOrgId
                                        && a.org_level == newOrgLevel
                                        && a.show_in_service_area == true
                                        select new
                                        {
                                            key_name = b.measurment_criteria,
                                            value_type = (!string.IsNullOrEmpty(b.value_type) && b.value_type.ToLower() == "text") ? "" : b.value_type,
                                            image_icon = string.IsNullOrEmpty(a.image_icon) ? "" : a.image_icon,
                                            year_1_value = a.year_1_value,
                                            postfix = string.Empty,
                                            divide_by = 1,
                                            result = a.result,
                                            year1value = a.year_1_value,
                                            year2value = a.year_2_value,
                                            year3value = a.year_3_value,
                                            year4value = a.year_4_value,
                                            kostraYear1 = a.kostra_1_value,
                                            kostraYear2 = a.kostra_2_value,
                                            kostraYear3 = a.kostra_3_value,
                                            kostraYear4 = a.kostra_4_value,
                                            column_to_show_in_front_page = (!string.IsNullOrEmpty(yearToShowInWeb)) ? yearToShowInWeb : "year1value"
                                        }).ToList();
                }
            }
            else
            {
                datasetIndicator = (!string.IsNullOrEmpty(orgId) && !string.IsNullOrEmpty(serviceId)) ?
                                       (from a in dbContext.tco_activity_indicator
                                        join b in dbContext.tco_indicator_setup on new { a = a.fk_tenant_id, b = a.fk_indicator_code }
                                                                            equals new { a = b.fk_tenant_id, b = b.pk_indicator_code }
                                        where a.fk_tenant_id == userDetails.tenant_id
                                        && a.budget_year == budgetYear
                                        && a.org_id == orgId
                                        && a.service_id == serviceId
                                        && a.show_in_service_area == true
                                        select new
                                        {
                                            key_name = b.measurment_criteria,
                                            value_type = (!string.IsNullOrEmpty(b.value_type) && b.value_type.ToLower() == "text") ? "" : b.value_type,
                                            image_icon = string.IsNullOrEmpty(a.image_icon) ? "" : a.image_icon,
                                            year_1_value = a.year_1_value,
                                            postfix = string.Empty,
                                            divide_by = 1,
                                            result = a.result,
                                            year1value = a.year_1_value,
                                            year2value = a.year_2_value,
                                            year3value = a.year_3_value,
                                            year4value = a.year_4_value,
                                            kostraYear1 = a.kostra_1_value,
                                            kostraYear2 = a.kostra_2_value,
                                            kostraYear3 = a.kostra_3_value,
                                            kostraYear4 = a.kostra_4_value,
                                            column_to_show_in_front_page = (!string.IsNullOrEmpty(yearToShowInWeb)) ? yearToShowInWeb : "year1value"
                                        }).ToList() : (!string.IsNullOrEmpty(orgId) && string.IsNullOrEmpty(serviceId)) ?
                                       (from a in dbContext.tco_activity_indicator
                                        join b in dbContext.tco_indicator_setup on new { a = a.fk_tenant_id, b = a.fk_indicator_code }
                                                                            equals new { a = b.fk_tenant_id, b = b.pk_indicator_code }
                                        where a.fk_tenant_id == userDetails.tenant_id
                                        && a.budget_year == budgetYear
                                       && a.org_id == orgId
                                       && a.show_in_key_figure
                                        select new
                                        {
                                            key_name = b.measurment_criteria,
                                            value_type = (!string.IsNullOrEmpty(b.value_type) && b.value_type.ToLower() == "text") ? "" : b.value_type,
                                            image_icon = string.IsNullOrEmpty(a.image_icon) ? "" : a.image_icon,
                                            year_1_value = a.year_1_value,
                                            postfix = string.Empty,
                                            divide_by = 1,
                                            result = a.result,
                                            year1value = a.year_1_value,
                                            year2value = a.year_2_value,
                                            year3value = a.year_3_value,
                                            year4value = a.year_4_value,
                                            kostraYear1 = a.kostra_1_value,
                                            kostraYear2 = a.kostra_2_value,
                                            kostraYear3 = a.kostra_3_value,
                                            kostraYear4 = a.kostra_4_value,
                                            column_to_show_in_front_page = (!string.IsNullOrEmpty(yearToShowInWeb)) ? yearToShowInWeb : "year1value"
                                        }).ToList() : (string.IsNullOrEmpty(orgId) && !string.IsNullOrEmpty(serviceId)) ?
                                       (from a in dbContext.tco_activity_indicator
                                        join b in dbContext.tco_indicator_setup on new { a = a.fk_tenant_id, b = a.fk_indicator_code }
                                                                            equals new { a = b.fk_tenant_id, b = b.pk_indicator_code }
                                        where a.fk_tenant_id == userDetails.tenant_id
                                        && a.budget_year == budgetYear
                                       && a.service_id == serviceId
                                       && a.show_in_key_figure
                                        select new
                                        {
                                            key_name = b.measurment_criteria,
                                            value_type = (!string.IsNullOrEmpty(b.value_type) && b.value_type.ToLower() == "text") ? "" : b.value_type,
                                            image_icon = string.IsNullOrEmpty(a.image_icon) ? "" : a.image_icon,
                                            year_1_value = a.year_1_value,
                                            postfix = string.Empty,
                                            divide_by = 1,
                                            result = a.result,
                                            year1value = a.year_1_value,
                                            year2value = a.year_2_value,
                                            year3value = a.year_3_value,
                                            year4value = a.year_4_value,
                                            kostraYear1 = a.kostra_1_value,
                                            kostraYear2 = a.kostra_2_value,
                                            kostraYear3 = a.kostra_3_value,
                                            kostraYear4 = a.kostra_4_value,
                                            column_to_show_in_front_page = (!string.IsNullOrEmpty(yearToShowInWeb)) ? yearToShowInWeb : "year1value"
                                        }).ToList() : (from a in dbContext.tco_activity_indicator
                                                       join b in dbContext.tco_indicator_setup on new { a = a.fk_tenant_id, b = a.fk_indicator_code }
                                                                                           equals new { a = b.fk_tenant_id, b = b.pk_indicator_code }
                                                       where a.fk_tenant_id == -1
                                                       select new
                                                       {
                                                           key_name = "",
                                                           value_type = "",
                                                           image_icon = "",
                                                           year_1_value = 0.0M,
                                                           postfix = string.Empty,
                                                           divide_by = 1,
                                                           result = 0.0M,
                                                           year1value = 0.0M,
                                                           year2value = 0.0M,
                                                           year3value = 0.0M,
                                                           year4value = 0.0M,
                                                           kostraYear1 = 0.0M,
                                                           kostraYear2 = 0.0M,
                                                           kostraYear3 = 0.0M,
                                                           kostraYear4 = 0.0M,
                                                           column_to_show_in_front_page = (!string.IsNullOrEmpty(yearToShowInWeb)) ? yearToShowInWeb : "year1value"
                                                       }).ToList();
            }

            foreach (var data in datasetIndicator)
            {
                dynamic obj = new JObject();
                obj.key_figure_description = data.key_name;
                switch (data.column_to_show_in_front_page)
                {
                    case "year1value": obj.value = (data.year1value / data.divide_by).ToString(data.value_type) + " " + data.postfix; break;
                    case "year2value": obj.value = (data.year2value / data.divide_by).ToString(data.value_type) + " " + data.postfix; break;
                    case "year3value": obj.value = (data.year3value / data.divide_by).ToString(data.value_type) + " " + data.postfix; break;
                    case "year4value": obj.value = (data.year4value / data.divide_by).ToString(data.value_type) + " " + data.postfix; break;
                    case "kostraYear1": obj.value = (data.kostraYear1 / data.divide_by).ToString(data.value_type) + " " + data.postfix; break;
                    case "kostraYear2": obj.value = (data.kostraYear2 / data.divide_by).ToString(data.value_type) + " " + data.postfix; break;
                    case "kostraYear3": obj.value = (data.kostraYear3 / data.divide_by).ToString(data.value_type) + " " + data.postfix; break;
                    case "kostraYear4": obj.value = (data.kostraYear4 / data.divide_by).ToString(data.value_type) + " " + data.postfix; break;
                    case "result": obj.value = (data.result / data.divide_by).ToString(data.value_type) + " " + data.postfix; break;

                    default: obj.value = (data.year_1_value / data.divide_by).ToString(data.value_type) + " " + data.postfix; break;
                }
                
                obj.imageUrl = string.IsNullOrEmpty(data.image_icon) ? "" : "/content/appimages/" + data.image_icon;

                if (!string.IsNullOrEmpty(data.image_icon))
                {
                    if (lstImages.FirstOrDefault(x => x.source.ToLower().Contains(data.image_icon.ToLower())) == null)
                    {
                        lstImages.Add(new InfoBlobSourceAndDestination()
                        {
                            source = "global/images/keyfigures/" + data.image_icon,
                            destination = baseStoragePath + "/content/appimages/" + data.image_icon
                        });
                    }
                }

                dataArray.Add(obj);
            }

            _blobHelper.CopyBlob(StorageAccount.AppStorage, BlobContainers.MediaLibrary, StorageAccount.PublishStage, BlobContainers.BmContent, lstImages);

            JObject retVal = new JObject();
            retVal.Add("title", lsDocExport.FirstOrDefault(x => x.Key.Equals("bm_sa_key_figures_title", StringComparison.InvariantCultureIgnoreCase)).Value.LangText);
            retVal.Add("data", dataArray);

            return JsonConvert.SerializeObject(retVal);
        }

        public PublishTemplateHelper GetPoliticalSimulationTemplate(string userId)
        {
            return GetPoliticalSimulationTemplateAsync(userId).GetAwaiter().GetResult();
        }

        // Currently we dont have Tree Structure for PS web publish, Henceforth we have to use the default  set up as below
        public async Task<PublishTemplateHelper> GetPoliticalSimulationTemplateAsync(string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            List<PublishTreeNode> data = new List<PublishTreeNode>();
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "PoliticalSimulation");

            PublishTreeNode introChapterText = await AddStaticTreeNodeforPSAsync(userId, clsConstants.PoliticalSimulationNodeType.IntroChapterText.ToString(), clsConstants.PoliticalSimulationNodeType.IntroChapterText.ToString(), langStringValues);
            data.Add(introChapterText);

            PublishTreeNode changesinActions = await AddStaticTreeNodeforPSAsync(userId, clsConstants.PoliticalSimulationNodeType.ChangeinActions.ToString(), clsConstants.PoliticalSimulationNodeType.ChangeinActions.ToString(), langStringValues);
            data.Add(changesinActions);

            PublishTreeNode actionSummary = await AddStaticTreeNodeforPSAsync(userId, clsConstants.PoliticalSimulationNodeType.ActionSummary.ToString(), clsConstants.PoliticalSimulationNodeType.ActionSummary.ToString(), langStringValues);
            data.Add(actionSummary);

            PublishTreeNode changesinInvestments = await AddStaticTreeNodeforPSAsync(userId, clsConstants.PoliticalSimulationNodeType.ChangeinInvestments.ToString(), clsConstants.PoliticalSimulationNodeType.ChangeinInvestments.ToString(), langStringValues);
            data.Add(changesinInvestments);

            PublishTreeNode investmentSummary = await AddStaticTreeNodeforPSAsync(userId, clsConstants.PoliticalSimulationNodeType.InvestmentSummary.ToString(), clsConstants.PoliticalSimulationNodeType.InvestmentSummary.ToString(), langStringValues);
            data.Add(investmentSummary);

            //Assignments
            PublishTreeNode assignments = await AddStaticTreeNodeforPSAsync(userId, clsConstants.PoliticalSimulationNodeType.Assignments.ToString(), clsConstants.PoliticalSimulationNodeType.Assignments.ToString(), langStringValues);
            data.Add(assignments);

            PublishTreeNode operatingMeasures1A = await AddStaticTreeNodeforPSAsync(userId, clsConstants.PoliticalSimulationNodeType.OperatingMeasures1A.ToString(), clsConstants.PoliticalSimulationNodeType.OperatingMeasures1A.ToString(), langStringValues);
            data.Add(operatingMeasures1A);

            PublishTreeNode operatingMeasures1B = await AddStaticTreeNodeforPSAsync(userId, clsConstants.PoliticalSimulationNodeType.OperatingMeasures1B.ToString(), clsConstants.PoliticalSimulationNodeType.OperatingMeasures1B.ToString(), langStringValues);
            data.Add(operatingMeasures1B);

            PublishTreeNode investmentMeasures2A = await AddStaticTreeNodeforPSAsync(userId, clsConstants.PoliticalSimulationNodeType.InvestmentMeasures2A.ToString(), clsConstants.PoliticalSimulationNodeType.InvestmentMeasures2A.ToString(), langStringValues);
            data.Add(investmentMeasures2A);

            PublishTreeNode investmentMeasures2B = await AddStaticTreeNodeforPSAsync(userId, clsConstants.PoliticalSimulationNodeType.InvestmentMeasures2B.ToString(), clsConstants.PoliticalSimulationNodeType.InvestmentMeasures2B.ToString(), langStringValues);
            data.Add(investmentMeasures2B);

            PublishTemplateHelper exportTree = new PublishTemplateHelper
            {
                Id = -1,
                Name = PublishTreeType.PoliticalSimulation.ToString(),
                ShortName = PublishTreeType.PoliticalSimulation.ToString(),
                IsGlobal = true,
                IsDefault = true,
                Tree = data
            };

            return exportTree;
        }

        private async Task<PublishTreeNode> AddStaticTreeNodeforPSAsync(string userId, string id, string type, Dictionary<string, clsLanguageString> langStringValues)
        {
            UserData userdetails = await _utility.GetUserDetailsAsync(userId);
            IEnumerable<gmd_publish_tree_node_definitions> treeNodeDefs = await _utility.GetPulishNodeDefinitionsAsync(userId, PublishTreeType.PoliticalSimulation);

            gmd_publish_tree_node_definitions current = treeNodeDefs.FirstOrDefault(x => x.type == type);
            if (current == null)
            {
                throw new DataNotFoundException($"Specified type - {type} was not present in metadata");
            }

            PublishTreeNode treeNode = new PublishTreeNode
            {
                id = id,
                text = langStringValues.FirstOrDefault(v => v.Key == current.title_key).Value.LangText,
                type = type,
                expanded = current.expanded,
                @checked = current.is_checked,
                isDisabled = false,
                isEditableNode = current.is_editable,
                drag = false,
                isAdmin = false,
                isChapter = true,
                Uid = Guid.NewGuid().ToString()
            };
            return treeNode;
        }

        public string UpdateInvestmentDataForNewModel(string userId, int budgetYear)
        {
            return UpdateInvestmentDataForNewModelAsync(userId, budgetYear).GetAwaiter().GetResult();
        }

        public async Task<string> UpdateInvestmentDataForNewModelAsync(string userId, int budgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            string id = $"{userDetails.tenant_id}:{budgetYear}:{userDetails.pk_id}";

            if (_lockManager.IsLocked("prcGenerateInvestmentFinplandoc", id))
            {
                // If locked, Wait for the ongoing procedure to finish and skip the current procedure executiong
                do
                {
                    await Task.Delay(1000);
                } while (_lockManager.IsLocked("prcGenerateInvestmentFinplandoc", id));
            }
            else if (_lockManager.GetLock("prcGenerateInvestmentFinplandoc", id, new TimeSpan(0, 10, 0)))
            {
                TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
                tenantDbContext.Database.SetCommandTimeout(1000);
                var spValidate = new prcGenerateInvestmentFinplandoc
                {
                    budget_year = budgetYear,
                    fk_tenant_id = userDetails.tenant_id,
                    user_id = userDetails.pk_id,
                };

                await tenantDbContext.Database.ExecuteStoredProcedureAsync(spValidate);
                _lockManager.ReleaseLock("prcGenerateInvestmentFinplandoc", id);
            }

            return "Success";
        }

        public PublishTreeNode UpdateStrategyNode(string userId, PublishTreeNode strategyNode, string orgId, string serviceId, int orgLevel, int budgetYear)
        {
            return UpdateStrategyNodeAsync(userId, strategyNode, orgId, serviceId, orgLevel, budgetYear).GetAwaiter().GetResult();
        }

        public async Task<PublishTreeNode> UpdateStrategyNodeAsync(string userId, PublishTreeNode strategyNode, string orgId, string serviceId, int orgLevel, int budgetYear)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> _languageStringsBudgetManagement = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetManagement");

            List<KeyValueGuidHelper> assessmentInfo;
            string orgInfo = orgId + "_" + orgLevel + "_" + serviceId;
            if (!string.IsNullOrEmpty(serviceId))
            {
                assessmentInfo = await (from a in dbContext.tsa_assessments
                                        join b in dbContext.tsa_assessment_delegation on new { a = a.pk_assessment_id, b = a.fk_tenant_id }
                                                                                  equals new { a = b.fk_assessment_id, b = b.fk_tenant_id }
                                        where a.fk_tenant_id == userDetails.tenant_id && a.budget_year == budgetYear && b.org_id == orgId && b.service_id == serviceId
                                        && b.org_level == orgLevel && b.is_visible == 1 && a.status != 0
                                        select new KeyValueGuidHelper
                                        {
                                            key = a.pk_assessment_id,
                                            value = a.assessment_name,
                                            isOwner = b.is_owner ?? false
                                        }).OrderBy(x => x.value).Distinct().ToListAsync();
            }
            else
            {
                assessmentInfo = await (from a in dbContext.tsa_assessments
                                        join b in dbContext.tsa_assessment_delegation on new { a = a.pk_assessment_id, b = a.fk_tenant_id }
                                                                                  equals new { a = b.fk_assessment_id, b = b.fk_tenant_id }
                                        where a.fk_tenant_id == userDetails.tenant_id && a.budget_year == budgetYear && b.org_id == orgId
                                        && b.org_level == orgLevel && b.is_visible == 1 && a.status != 0
                                        select new KeyValueGuidHelper
                                        {
                                            key = a.pk_assessment_id,
                                            value = a.assessment_name,
                                            isOwner = b.is_owner ?? false
                                        }).OrderBy(x => x.value).Distinct().ToListAsync();
            }

            List<Guid> assessmentIds = assessmentInfo.Select(x => x.key).ToList();

            var assessmentsAreas = await (from a in dbContext.tsa_assessment_areas
                                          join b in dbContext.tsa_assessment_area_mapping on new { a = a.fk_tenant_id, b = a.pk_area_id }
                                                                                      equals new { a = b.fk_tenant_id, b = b.fk_area_id }
                                          join c in dbContext.tsa_assessments on new { a = a.fk_tenant_id, b = b.fk_assessment_id }
                                                                          equals new { a = c.fk_tenant_id, b = c.pk_assessment_id }
                                          where c.fk_tenant_id == userDetails.tenant_id && c.budget_year == budgetYear && assessmentIds.Contains(c.pk_assessment_id)
                                          group a by new { a.pk_area_id, a.area_name, b.fk_assessment_id } into g
                                          select new KeyValueGuidHelper()
                                          {
                                              key = g.Key.pk_area_id,
                                              value = g.Key.area_name,
                                              id = g.Key.fk_assessment_id
                                          }).OrderBy(x => x.value).AsNoTracking().ToListAsync();
            foreach (var assessment in assessmentInfo.OrderBy(x => x.value).ToList())
            {
                PublishTreeNode assessmentNode = await AddTreeNodeAsync(userId, "StrategyAssessmentA" + assessment.key, assessment.value, clsConstants.BM_Tree_Types.BPStrategyAssessment.ToString());

                if (assessment.isOwner)
                {
                    assessmentNode.items.Add(await AddTreeNodeAsync(userId, "GuideLine_" + assessment.key.ToString() + "_" + orgInfo, _languageStringsBudgetManagement["BP_AssessmentGuidelineDescription_node_text"].LangText, clsConstants.BM_Tree_Types.BPAssessmentGuidelineDescription.ToString()));
                }

                assessmentNode.items.Add(await AddTreeNodeAsync(userId, "Desc_" + assessment.key.ToString() + "_" + orgInfo, _languageStringsBudgetManagement["BP_assessmentDescription_node_text"].LangText, clsConstants.BM_Tree_Types.BPAssessmentDescription.ToString()));

                //Assessment conclusion text node
                assessmentNode.items.Add(await AddTreeNodeAsync(userId, "ConclusionDesc_" + assessment.key.ToString() + "_" + orgInfo, _languageStringsBudgetManagement["BP_assessmentConclusionDescription_node_text"].LangText, clsConstants.BM_Tree_Types.BPAssessmentConclusionDescription.ToString()));

                if (assessment.isOwner)
                {
                    //Assessment net result effect summery node
                    assessmentNode.items.Add(await AddTreeNodeAsync(userId, "SummeryA_" + assessment.key.ToString(), _languageStringsBudgetManagement["BP_netResultEffectSummery_node_text"].LangText, clsConstants.BM_Tree_Types.BPNetResultEffectSummery.ToString()));

                    //Assessment operations actions summery node
                    assessmentNode.items.Add(await AddTreeNodeAsync(userId, "SummeryB_" + assessment.key.ToString(), _languageStringsBudgetManagement["BP_operationActionSummery_node_text"].LangText, clsConstants.BM_Tree_Types.BPOperationActionSummery.ToString()));

                    //Assessment operations actions reduction summery node
                    assessmentNode.items.Add(await AddTreeNodeAsync(userId, "SummeryC_" + assessment.key.ToString(), _languageStringsBudgetManagement["BP_operationActionsReductionSummery_node_text"].LangText, clsConstants.BM_Tree_Types.BPOperationActionsReductionSummery.ToString()));

                    //Assessment operations actions decrement summery node
                    assessmentNode.items.Add(await AddTreeNodeAsync(userId, "SummeryD_" + assessment.key.ToString(), _languageStringsBudgetManagement["BP_operationActionsIncrementSummery_node_text"].LangText, clsConstants.BM_Tree_Types.BPOperationActionsIncrementSummery.ToString()));
                }
                var assessmentAreaInfo = assessmentsAreas.Where(x => x.id == assessment.key).OrderBy(x => x.value).ToList();

                //Add perspectives
                foreach (var area in assessmentAreaInfo)
                {
                    PublishTreeNode assessmentArea = await AddTreeNodeAsync(userId, "SAA_" + area.id.ToString() + "_" + area.key.ToString(), area.value, clsConstants.BM_Tree_Types.BPAssessmentArea.ToString());
                    assessmentArea.items.Add(await AddTreeNodeAsync(userId, "SATableA_" + area.key.ToString() + "_" + orgInfo + "_" + assessment.key.ToString(), _languageStringsBudgetManagement["BP_assessmentAreaActionsTable_node_text"].LangText, clsConstants.BM_Tree_Types.BPAssessmentAreaActionsTable.ToString()));
                    assessmentArea.items.Add(await AddTreeNodeAsync(userId, "SADescA_" + area.key.ToString() + "_" + orgInfo + "_" + assessment.key.ToString(), _languageStringsBudgetManagement["BP_assessmentAreaDescription_node_text"].LangText, clsConstants.BM_Tree_Types.BPAssessmentAreaDescription.ToString()));

                    assessmentNode.items.Add(assessmentArea);
                }

                strategyNode.items.Add(assessmentNode);
            }
            return strategyNode;
        }

        private async Task<List<PublishTreeNode>> InsertAssessmentNodesBasedOnFinplanLevelsAsync(string userId, tsa_assessment_delegation ownerInfo, List<clsOrgIdsAndServiceIds> secondlevelItems,
                                                                  List<KeyValueGuidHelper> assessmentsAreas, bool isServiceIdSetup, Guid assessmentId)
        {
            List<PublishTreeNode> fpLevel1ItemInfo = new List<PublishTreeNode>();
            clsOrgIdsAndServiceIds fp1item = secondlevelItems.First();
            bool isOwner = (ownerInfo != null && ownerInfo.org_id == fp1item.orgId && ownerInfo.service_level == -1);
            string ownerOrgId = string.Empty, ownerServiceId = string.Empty;
            int ownerServiceLevel = 0, fplevel1Index = 0;

            if (ownerInfo != null)
            {
                ownerOrgId = ownerInfo.org_id;
                ownerServiceId = ownerInfo.service_id;
                ownerServiceLevel = ownerInfo.service_level;
                int owningOrgIndex = isServiceIdSetup && !string.IsNullOrEmpty(ownerServiceId) ? secondlevelItems.FindIndex(x => x.orgId == ownerServiceId) : secondlevelItems.FindIndex(x => x.orgId == ownerOrgId);
                var owner = isServiceIdSetup && !string.IsNullOrEmpty(ownerServiceId) ? secondlevelItems.FirstOrDefault(x => x.orgId == ownerServiceId) : secondlevelItems.FirstOrDefault(x => x.orgId == ownerOrgId);
                if (owningOrgIndex != -1 && owner != null)
                {
                    secondlevelItems.RemoveAt(owningOrgIndex);
                    secondlevelItems.Insert(0, owner);
                    if (string.IsNullOrEmpty(ownerServiceId) && ownerOrgId == fp1item.orgId)
                    {
                        fplevel1Index = 0;
                    }
                    else
                    {
                        fplevel1Index = 1;
                    }
                }
            }
            //follow the same order we display orgIds in Strategy Admin popup tree. Keep owning level at the top while inserting nodes
            int subItemsCount = 0;
            //Insert second level
            foreach (var fp2item in secondlevelItems)
            {
                string stringToAppendNodeId = fp2item.orgId + "_2_" + fp1item.orgId;
                bool isFplevelOwner = isServiceIdSetup ? (ownerServiceId == fp2item.orgId) : (ownerServiceLevel == -1 && ownerOrgId == fp2item.orgId);

                bool isFpLevel1Org = subItemsCount == fplevel1Index;
                string nodeName = fp2item.orgId + "-" + fp2item.orgName;

                if (isFpLevel1Org)
                {
                    stringToAppendNodeId = fp1item.orgId + "_1_";
                    isFplevelOwner = isOwner;
                }
                fpLevel1ItemInfo.Add(await InsertStrategyAssessmentNodesAsync(userId, assessmentId, assessmentsAreas.Where(x => x.id == assessmentId).ToList(), nodeName, isFplevelOwner, stringToAppendNodeId));

                subItemsCount++;
            }
            return fpLevel1ItemInfo;
        }

        private PublishTreeNode InsertStrategyAssessmentNodes(string userId, Guid assessmentId, List<KeyValueGuidHelper> assessmentAreas, string nodeName, bool isOwner, string nodeId)
        {
            return InsertStrategyAssessmentNodesAsync(userId, assessmentId, assessmentAreas, nodeName, isOwner, nodeId).GetAwaiter().GetResult();
        }

        private async Task<PublishTreeNode> InsertStrategyAssessmentNodesAsync(string userId, Guid assessmentId, List<KeyValueGuidHelper> assessmentAreas, string nodeName, bool isOwner, string nodeId)
        {
            PublishTreeNode strategyServiceArea = await AddTreeNodeAsync(userId, "StrategySA_" + nodeId, nodeName, clsConstants.BM_Tree_Types.StrategyServiceArea.ToString(), false);

            if (isOwner)
            {
                //Assssment guidline text node
                strategyServiceArea.items.Add(await AddTreeNodeAsync(userId, "GuidlineDesc_" + assessmentId.ToString() + "_" + nodeId, _langStringValuesBudgetManagement["AssessmentGuidlineDescription_node_text"].LangText, clsConstants.BM_Tree_Types.AssessmentGuidlineDescription.ToString(), false));
            }

            //Assessment text node
            strategyServiceArea.items.Add(await AddTreeNodeAsync(userId, "Desc_" + assessmentId.ToString() + "_" + nodeId, _langStringValuesBudgetManagement["AssessmentDescription_node_text"].LangText, clsConstants.BM_Tree_Types.AssessmentDescription.ToString(), false));

            //Assessment conclusion text node
            strategyServiceArea.items.Add(await AddTreeNodeAsync(userId, "ConclusionDesc_" + assessmentId.ToString() + "_" + nodeId, _langStringValuesBudgetManagement["AssessmentConclusionDescription_node_text"].LangText, clsConstants.BM_Tree_Types.AssessmentConclusionDescription.ToString(), false));

            if (isOwner)
            {
                //Assessment net result effect summery node
                strategyServiceArea.items.Add(await AddTreeNodeAsync(userId, "SummeryA_" + assessmentId.ToString() + "_" + nodeId, _langStringValuesBudgetManagement["NetResultEffectSummery_node_text"].LangText, clsConstants.BM_Tree_Types.NetResultEffectSummery.ToString(), false));

                //Assessment operations actions summery node
                strategyServiceArea.items.Add(await AddTreeNodeAsync(userId, "SummeryB_" + assessmentId.ToString() + "_" + nodeId, _langStringValuesBudgetManagement["OperationActionSummery_node_text"].LangText, clsConstants.BM_Tree_Types.OperationActionSummery.ToString(), false));

                //Assessment operations actions reduction summery node
                strategyServiceArea.items.Add(await AddTreeNodeAsync(userId, "SummeryC_" + assessmentId.ToString() + "_" + nodeId, _langStringValuesBudgetManagement["OperationActionsReductionSummery_node_text"].LangText, clsConstants.BM_Tree_Types.OperationActionsReductionSummery.ToString(), false));

                //Assessment operations actions decrement summery node
                strategyServiceArea.items.Add(await AddTreeNodeAsync(userId, "SummeryD_" + assessmentId.ToString() + "_" + nodeId, _langStringValuesBudgetManagement["OperationActionsIncrementSummery_node_text"].LangText, clsConstants.BM_Tree_Types.OperationActionsIncrementSummery.ToString(), false));
            }
            //Add perspectives
            int i = 0;
            foreach (var area in assessmentAreas.OrderBy(x => x.value).ToList())
            {
                i++;
                PublishTreeNode assessmentArea = await AddTreeNodeAsync(userId, $"SA{i}_{area.id}_{nodeId}", area.value, clsConstants.BM_Tree_Types.AssessmentArea.ToString(), false);
                assessmentArea.items.Add(await AddTreeNodeAsync(userId, $"SATable_{area.key}_{nodeId}", _langStringValuesBudgetManagement["AssessmentAreaActionsTable_node_text"].LangText, clsConstants.BM_Tree_Types.AssessmentAreaActionsTable.ToString(), false));
                assessmentArea.items.Add(await AddTreeNodeAsync(userId, $"SADesc_{area.key}_{nodeId}", _langStringValuesBudgetManagement["AssessmentAreaDescription_node_text"].LangText, clsConstants.BM_Tree_Types.AssessmentAreaDescription.ToString(), false));

                strategyServiceArea.items.Add(assessmentArea);
            }
            return strategyServiceArea;
        }

        public async Task ArchiveDocumentExternalAsync(string userId, PublishArchive archiveInfo)
        {
            //validate that the user is from the same tenant as the template id
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            TcoPublishTemplate publishTemplate = await dbContext.TcoPublishTemplate.FirstOrDefaultAsync(x => x.FkTenantId == userDetails.tenant_id && x.PkId == archiveInfo.publishTemplateID && x.BudgetYear == archiveInfo.BudgetYear);
            if (publishTemplate != null)
            {
                Dictionary<string, int> modBudgetYears = _utility.GetBudgetYearsForExport(userId, archiveInfo.BudgetYear);
                ExportRequest request = new ExportRequest()
                {
                    ConfigId = 0,
                    UserId = userId,
                    TenantId = userDetails.tenant_id,
                    RequestType = PublishType.ArchiveDocumentExt,
                    CaseNum = archiveInfo.CaseNum,
                    CaseYear = archiveInfo.CaseYear,
                    UserParameters = archiveInfo.UserParameters,
                    PublishTemplateID = archiveInfo.publishTemplateID
                };

                string strRequest = JsonConvert.SerializeObject(request, new Newtonsoft.Json.Converters.StringEnumConverter());
                await _backendJob.QueueMessageAsync(userDetails, modBudgetYears, QueueName.bmexportrequestqueue, strRequest);
            }
            else
            {
                throw new AccessViolationException("User does not have access to this templateId");
            }
        }

        public async Task WriteToExpReqQueueAsync(string data, string userId, bool isPublish, int bmDocTreeTemplateId, int budgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            string exportTreePath = await InsertJsonRequestAsync(data, userId);
            dynamic jsonExport = JToken.Parse(data);
            dynamic jsonGuid = new JObject();
            jsonGuid.Add("ExportTreePath", exportTreePath);
            jsonGuid.Add("RequestTreePath", jsonExport.RequestTreePath);
            jsonGuid.Add("RequestType", isPublish ? PublishType.PublishWebsiteToStaging.ToString() : PublishType.CreateDocumentForConfig.ToString());
            jsonGuid.Add("UserId", userId);
            jsonGuid.Add("TenantId", userDetails.tenant_id);
            jsonGuid.Add("PublishTemplateID", bmDocTreeTemplateId);
            jsonGuid.Add("budgetPhaseId", jsonExport.budgetPhaseId);
            jsonGuid.Add("showOnlyModified", jsonExport.showOnlyModified);
            jsonGuid.Add("displayOnlyTables", jsonExport.displayOnlyTables);
            jsonGuid.Add("ConfigId", jsonExport.ConfigId!=null ? jsonExport.ConfigId : -1);
            jsonGuid.Add("treeType", jsonExport.treeType);
            jsonGuid.Add("BudgetYear", budgetYear);
            String strJsonGuid = JsonConvert.SerializeObject(jsonGuid);
            Dictionary<string, int> modBudgetYears = GetBudgetYearsForExport(userId, budgetYear);
            if (isPublish)
            {
                await _backendJob.QueueMessageAsync(userDetails, modBudgetYears, QueueName.bmpublishrequestqueue, strJsonGuid);
            }
            else
            {
                await _backendJob.QueueMessageAsync(userDetails, modBudgetYears, QueueName.bmexportrequestqueue, strJsonGuid);
            }
        }

        public async Task ArchiveDocumentAsync(string userId, IEnumerable<PublishTreeNode> exportParameters, string description, bool includeInternalDesc, int budgetYear,
            string budgetPhaseId, bool showOnlyModified, bool displayOnlyTables)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            dynamic jsonExpParams = JToken.Parse(JsonConvert.SerializeObject(exportParameters));
            dynamic jsonBlobReq = new JObject();
            jsonBlobReq.Add("UserId", userId);
            jsonBlobReq.Add("TenantId", userDetails.tenant_id);
            jsonBlobReq.Add("ExportParameters", jsonExpParams);
            string strBlobReq = JsonConvert.SerializeObject(jsonBlobReq);
            string exportTreePath = await InsertJsonRequestAsync(strBlobReq, userId);

            dynamic jsonQueueReq = new JObject();
            jsonQueueReq.Add("ExportTreePath", exportTreePath);
            jsonQueueReq.Add("RequestType", PublishType.ArchiveDocument.ToString());
            jsonQueueReq.Add("UserId", userId);
            jsonQueueReq.Add("TenantId", userDetails.tenant_id);
            jsonQueueReq.Add("description", description);
            jsonQueueReq.Add("budgetPhaseId", budgetPhaseId);
            jsonQueueReq.Add("showOnlyModified", showOnlyModified);
            jsonQueueReq.Add("displayOnlyTables", displayOnlyTables);
            jsonQueueReq.Add("isPublish", true);

            string strQueueReq = JsonConvert.SerializeObject(jsonQueueReq);

            Dictionary<string, int> modBudgetYears = GetBudgetYearsForExport(userId, budgetYear);
            await _backendJob.QueueMessageAsync(userDetails, modBudgetYears, QueueName.bmexportrequestqueue, strQueueReq);
        }

        private async Task RequestPublishToStagingAsync(PublishTemplateHelper template, string userId, int configId, int BudgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            string strTemplate = JsonConvert.SerializeObject(template);
            string exportTreePath = await InsertJsonRequestAsync(strTemplate, userId);

            dynamic queueMessage = new JObject();
            queueMessage.Add("RequestType", PublishType.PublishWebsiteToStaging.ToString());
            queueMessage.Add("RequestTreePath", exportTreePath);
            queueMessage.Add("UserId", userId);
            queueMessage.Add("TenantId", userDetails.tenant_id);
            queueMessage.Add("ConfigId", configId);

            string strQueueMessage = JsonConvert.SerializeObject(queueMessage);
            Dictionary<string, int> modBudgetYears = GetBudgetYearsForExport(userId, BudgetYear);

            await _backendJob.QueueMessageAsync(userDetails, modBudgetYears, QueueName.bmpublishrequestqueue, strQueueMessage);
        }

        private async Task RequestPublishToProdAsync(string userId, int configId, int BudgetYear)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            dynamic queueMessage = new JObject();
            queueMessage.Add("RequestType", PublishType.PublishWebsiteToProd.ToString());
            queueMessage.Add("UserId", userId);
            queueMessage.Add("TenantId", userDetails.tenant_id);
            queueMessage.Add("ConfigId", configId);

            string strQueueMessage = JsonConvert.SerializeObject(queueMessage);
            Dictionary<string, int> modBudgetYears = GetBudgetYearsForExport(userId, BudgetYear);

            await _backendJob.QueueMessageAsync(userDetails, modBudgetYears, QueueName.bmpublishrequestqueue, strQueueMessage);
        }

        private async Task RequestDeleteProdWebsiteAsync(string userId, string publishPath, string publishShortName)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            dynamic queueMessage = new JObject();
            queueMessage.Add("RequestType", PublishType.DeleteProdPublish.ToString());
            queueMessage.Add("UserId", userId);
            queueMessage.Add("TenantId", userDetails.tenant_id);
            queueMessage.Add("PublishPath", publishPath);
            queueMessage.Add("ShortName", publishShortName);

            string strQueueMessage = JsonConvert.SerializeObject(queueMessage);
            Dictionary<string, int> modBudgetYears = new Dictionary<string, int>();

            await _backendJob.QueueMessageAsync(userDetails, modBudgetYears, QueueName.bmpublishrequestqueue, strQueueMessage);
        }

        private async Task RequestDeleteStagWebsiteAsync(string userId, string publishPath, string publishShortName)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            dynamic queueMessage = new JObject();
            queueMessage.Add("RequestType", PublishType.DeleteStagPublish.ToString());
            queueMessage.Add("UserId", userId);
            queueMessage.Add("TenantId", userDetails.tenant_id);
            queueMessage.Add("PublishPath", publishPath);
            queueMessage.Add("ShortName", publishShortName);

            string strQueueMessage = JsonConvert.SerializeObject(queueMessage);
            Dictionary<string, int> modBudgetYears = new Dictionary<string, int>();

            await _backendJob.QueueMessageAsync(userDetails, modBudgetYears, QueueName.bmpublishrequestqueue, strQueueMessage);
        }

        public Dictionary<string, int> GetBudgetYearsForExport(string userId, int budgetYear)
        {
            Dictionary<string, int> modBudgetYears = new Dictionary<string, int>();
            modBudgetYears.Add("KOSTRA_BUDGET_YEAR", budgetYear - 1);
            modBudgetYears.Add("POPSTAT_BUDGET_YEAR", budgetYear - 1);
            modBudgetYears.Add("BUDGETTASK_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("UTILITY_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("BUDGETASSUMPTION_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("BUDMAN_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("BUDPROP_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("CAB_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("INVESTMENTS_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("FINANCING_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("KPIDATA_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("OPPASSMNT_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("STAFFPLAN_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("YEARLYBUDGET_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("BUDGETREGULATION_BUDGET_YEAR", budgetYear);
            modBudgetYears.Add("POPFCAST_POPEXP_GROWTH_STARTYR", budgetYear - 1);
            return modBudgetYears;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="configId"></param>
        /// <param name="target"></param>
        public async Task ReindexSearchAsync(string userId, int configId, PublishSiteType target)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            tco_publish_config pubConfig = await dbContext.tco_publish_config.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id &&
                x.pk_id == configId && x.tree_type == "BUDMAN");

            if (pubConfig == null)
            {
                throw new InvalidDataException($"User does not have access to config {configId} or config does not exist");
            }

            dynamic queueMessage = new JObject();
            switch (target)
            {
                case PublishSiteType.Staging:
                    queueMessage.Add("RequestType", PublishType.ReindexStaging.ToString());
                    break;

                case PublishSiteType.Production:
                    queueMessage.Add("RequestType", PublishType.ReindexProd.ToString());
                    break;
            }
            queueMessage.Add("UserId", userId);
            queueMessage.Add("ConfigId", configId);

            string strQueueMessage = JsonConvert.SerializeObject(queueMessage);

            await _backendJob.QueueMessageAsync(userDetails, null, QueueName.bmpublishrequestqueue, strQueueMessage);
        }

        public async Task DeleteSearchIndexAsync(string userId, int configId, PublishSiteType target)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            tco_publish_config pubConfig = dbContext.tco_publish_config.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id &&
                x.pk_id == configId && x.tree_type == "BUDMAN");

            if (pubConfig == null)
            {
                throw new InvalidDataException($"User does not have access to config {configId} or config does not exist");
            }

            dynamic queueMessage = new JObject();
            switch (target)
            {
                case PublishSiteType.Staging:
                    queueMessage.Add("RequestType", PublishType.DeleteStagIndex.ToString());
                    break;

                case PublishSiteType.Production:
                    queueMessage.Add("RequestType", PublishType.DeleteProdIndex.ToString());
                    break;
            }
            queueMessage.Add("UserId", userId);
            queueMessage.Add("ConfigId", configId);

            string strQueueMessage = JsonConvert.SerializeObject(queueMessage);

            await _backendJob.QueueMessageAsync(userDetails, null, QueueName.bmpublishrequestqueue, strQueueMessage);
        }

        public async Task<string> GetDeadLineDateAsync(int budgetYear, string userId, string pageType)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            int activeChangeId = await _finUtility.GetActiveBudgetChangeIdAsync(userId, pageType, budgetYear);
            var data = await dbContext.tfp_budget_changes.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_change_id == activeChangeId && x.budget_year == budgetYear);
            if (data != null && data.deadline_date.HasValue)
            {
                return data.deadline_date.Value.ToString("dd'.'MM'.'yyyy", CultureInfo.InvariantCulture);
            }
            else
            {
                return string.Empty;
            }
        }

        public async Task SaveDeadLineDateAsync(int budgetYear, string userId, DateTime? deadLinedate)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            int activeChangeId = await _finUtility.GetActiveBudgetChangeIdAsync(userId, "Budget_Management", budgetYear);
            tfp_budget_changes tfp = await dbContext.tfp_budget_changes.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_change_id == activeChangeId && x.budget_year == budgetYear);
            if (tfp != null)
            {
                tfp.deadline_date = deadLinedate;
                await dbContext.SaveChangesAsync();
            }
        }

        private IEnumerable<PublishTreeNode> GetOrgTree(string userId, int budgetYear, TreeLevel requestedLevel)
        {
            int docConfig = GetDocumentConfiguration(userId);

            List<clsOrgIdsAndServiceIds> lstResult = _consequentAdjustedBudget.GetOrgIdsAndServiceIds(userId, false, budgetYear);

            List<clsOrgIdsAndServiceIds> orgs = lstResult.Where(x => x.parentId == null).OrderBy(x => x.orgId).ToList();
            List<PublishTreeNode> orgTree = new List<PublishTreeNode>();

            switch (docConfig)
            {
                case 1:
                    foreach (var org in orgs)
                    {
                        var node = new PublishTreeNode()
                        {
                            id = org.orgId,
                            text = org.orgName,
                            @checked = true,
                            type = clsConstants.BM_Tree_Types.Organization.ToString()
                        };
                        orgTree.Add(node);
                    }

                    break;

                case 2:
                    List<string> serviceIdList = new List<string>();
                    foreach (var org in orgs)
                    {
                        var services = lstResult.Where(x => x.parentId == org.id.ToString() && !string.IsNullOrEmpty(x.orgId))
                                                .OrderBy(x => x.orgId).ToList();
                        
                        foreach (var service in services)
                        {
                            if (!serviceIdList.Contains(service.orgId))
                            {
                                var serviceNode = new PublishTreeNode()
                                {
                                    id = service.orgId,
                                    text = $"{service.orgId} - {service.orgName}",
                                    @checked = true,
                                    type = clsConstants.BM_Tree_Types.Organization.ToString()
                                };
                                orgTree.Add(serviceNode);
                            }
                        }

                        serviceIdList.AddRange(services.Select(x => x.orgId).OrderBy(x => x).Distinct().ToList());
                    }

                    break;

                default:
                    foreach (var org in orgs)
                    {
                        var serviceArea = new PublishTreeNode()
                        {
                            id = org.orgId,
                            text = org.orgName,
                            @checked = true,
                            type = clsConstants.BM_Tree_Types.Organization.ToString()
                        };
                        
                        if (requestedLevel == TreeLevel.BothLevels)
                        {
                            var services = lstResult.Where(x => x.parentId == org.id.ToString() && !string.IsNullOrEmpty(x.orgId))
                                                    .OrderBy(x => x.orgId).ToList();
                            
                            foreach (var service in services)
                            {
                                var serviceNode = new PublishTreeNode()
                                {
                                    id = service.orgId,
                                    text = service.orgName,
                                    @checked = true,
                                    type = clsConstants.BM_Tree_Types.Organization.ToString()
                                };
                                serviceArea.items.Add(serviceNode);
                            }
                        }
                        orgTree.Add(serviceArea);
                    }

                    break;
            }

            orgTree = orgTree.OrderBy(x => x.id).ToList();

            return orgTree;
        }

        public void SaveOrgTreeForMasterNode(string userId, IEnumerable<PublishTreeNodeMin> orgTree, int budgetYear,
            Guid nodeId)
        {
            UserData userDetails = _utility.GetUserDetails(userId);
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            TcoPublishTemplate template = dbContext.TcoPublishTemplate.FirstOrDefault(x => x.FkTenantId == userDetails.tenant_id &&
                x.TreeType == PublishTreeType.BMMasterTemplate.ToString() &&
                x.BudgetYear == budgetYear);
            if (template == null)
            {
                throw new InvalidDataException("Master template does not exist for this year");
            }

            string strOrgTreeMin = JsonConvert.SerializeObject(orgTree);
            var orgTreeFull = JsonConvert.DeserializeObject<IEnumerable<PublishTreeNode>>(strOrgTreeMin);
            string strOrgTree = JsonConvert.SerializeObject(orgTreeFull);
            var allOrgsUnChecked = true;
            foreach (var node in orgTreeFull)
            {
                if (!_utility.AreAnyChildrenChecked(node)) continue;
                allOrgsUnChecked = false;
                break;
            }

            //Uncheck the node in the master template
            var masterTemplate = GetTreeForBmMasterTemplate(userId, budgetYear, false);
            var flatMasterTemplate = _templateUtilty.FlattenTree(masterTemplate.Tree, 0, null, null, false);
            var masterNode = flatMasterTemplate.FirstOrDefault(x =>
                x.node.id.Equals(nodeId.ToString(), StringComparison.InvariantCultureIgnoreCase));
            if (masterNode != null)
            {
                masterNode.node.@checked = !allOrgsUnChecked;
            }

            UpdateMasterTemplate(userId, masterTemplate);

            string path = $"{userDetails.tenant_id}/{template.PkId}/{nodeId}.json".ToLower();
            _blobHelper.UploadTextBlob(StorageAccount.AppStorage, BlobContainers.BmMasterTemplate, path, strOrgTree);
        }

        public async Task SaveOrgTreeForMultipleMasterNode(string userId, IEnumerable<PublishTreeNodeMin> orgTree, int budgetYear, int level,
            List<string> nodeIds, Guid budgetPhaseId)
        {
            List<NodeContainer> reportingWidgetNodeLst = new();

            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            TcoPublishTemplate template = await dbContext.TcoPublishTemplate.FirstOrDefaultAsync(x => x.FkTenantId == userDetails.tenant_id &&
                                        x.TreeType == PublishTreeType.BMMasterTemplate.ToString() &&
                                        x.BudgetYear == budgetYear);
            if (template == null)
            {
                throw new InvalidDataException("Master template does not exist for this year");
            }

            string strOrgTreeMin = JsonConvert.SerializeObject(orgTree);
            var orgTreeFull = JsonConvert.DeserializeObject<IEnumerable<PublishTreeNode>>(strOrgTreeMin);
            string strOrgTree = JsonConvert.SerializeObject(orgTreeFull);
            var allOrgsUnChecked = true;
            foreach (var node in orgTreeFull)
            {
                if (!_utility.AreAnyChildrenChecked(node)) continue;
                allOrgsUnChecked = false;
                break;
            }

            //Uncheck the node in the master template
            var masterTemplate = GetTreeForBmMasterTemplate(userId, budgetYear, false, isBudgetPropTexts: true);
            
            // Process tree data
            var level1Results = masterTemplate.Tree.Where(x => x.id != "BM_doc_ServiceUnit").ToList();
            var level2Results = masterTemplate.Tree.Where(x => x.id == "BM_doc_ServiceUnit").ToList();

            var flatMasterTemplateLevel1 = _templateUtilty.FlattenTree(level1Results, 0, null, null, false);
            var flatMasterTemplateLevel2 = level2Results.Any() ? _templateUtilty.FlattenTree(level2Results.FirstOrDefault().items, 0, null, null, false) : new List<NodeContainer>();

            foreach (var nodeId in nodeIds)
            {
                var nodeIdStr = nodeId.ToString();
                var masterNode = level == 1
                               ? flatMasterTemplateLevel1.FirstOrDefault(x => x.node.type.Equals(nodeIdStr, StringComparison.InvariantCultureIgnoreCase))
                               : flatMasterTemplateLevel2.FirstOrDefault(x => x.node.type.Equals(nodeIdStr, StringComparison.InvariantCultureIgnoreCase));

                if (masterNode != null)
                {
                    masterNode.node.@checked = !allOrgsUnChecked;
                    if (masterNode.nodeType == clsConstants.BM_Tree_Types.reportingWidget.ToString())
                    {
                        reportingWidgetNodeLst.Add(masterNode);
                    }
                }
            }

            List<PublishTreeNode> level1Tree = flatMasterTemplateLevel1.Select(container => container.node)
                                                               .Where(node => node != null)
                                                               .ToList();
            List<PublishTreeNode> finalTreeData = level1Tree;
            if (level2Results.Any())
            {
                PublishTreeNode secondLevelParentNode = await _publishTemplateManager.GetSecondLevelParentNodeAsync(userDetails.user_name);
                secondLevelParentNode.items = flatMasterTemplateLevel2.Select(container => container.node)
                                                                .Where(node => node != null)
                                                                .ToList();
                finalTreeData = level1Tree.Append(secondLevelParentNode).ToList();
            }
            masterTemplate.Tree = finalTreeData;

            UpdateMasterTemplate(userId, masterTemplate);
            string path = string.Empty;
            List<Guid> budgetPhaseIds = new List<Guid>();
            
            if (budgetPhaseId != Guid.Empty)
            { 
                budgetPhaseIds.Add(budgetPhaseId);
            }
            else
            {
                var budgetPhases = await _finUtility.GetBudgetPhasePublishStatusDataAsync(userId, budgetYear);
                budgetPhaseIds = budgetPhases.Select(bp => bp.budget_phase_id).ToList();
                budgetPhaseIds.Add(Guid.Empty);
            }
            
            foreach (var nodeId in nodeIds)
            {
                foreach (var budgetPhase in budgetPhaseIds)
                {
                    path = getOrgTreeBlobPath(userDetails.tenant_id, template.PkId, nodeId, budgetPhase, level);
                    await _blobHelper.UploadTextBlobAsync(StorageAccount.AppStorage, BlobContainers.BmMasterTemplate, path, strOrgTree);
                }
            }
            
            if (reportingWidgetNodeLst.Any())
            {
                UpdateReportingWidgetDelegationNodeData(userId, budgetYear, level == 2, reportingWidgetNodeLst, orgTreeFull).GetAwaiter().GetResult();
            }
        }

        public string getOrgTreeBlobPath(int tenantId, int templateId, string nodeId, Guid budgetPhaseId, int level)
        {
            string path = string.Empty;
            if (!Guid.TryParse(nodeId, out _))
            {
                if (budgetPhaseId == Guid.Empty)
                {
                    path = $"{tenantId}/{templateId}/{level}/{nodeId}.json".ToLower();
                }
                else
                {
                    path = $"{tenantId}/{templateId}/{level}/{budgetPhaseId}/{nodeId}.json".ToLower();
                }
            }
            else
            {
                if (budgetPhaseId == Guid.Empty)
                {
                    path = $"{tenantId}/{templateId}/{nodeId}.json".ToLower();
                }
                else
                {
                    path = $"{tenantId}/{templateId}/{budgetPhaseId}/{nodeId}.json".ToLower();

                }
            }
            return path;
        }

        private async Task UpdateReportingWidgetDelegationNodeData(string userId, int budgetYear, bool isLevel2Tree, List<NodeContainer> reportingWidgetNodeLst, IEnumerable<PublishTreeNode> delegationOrgTree)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            int docconfig = await GetDocumentConfigurationAsync(userId);
            foreach (var item in reportingWidgetNodeLst)
            {
                List<tco_widget_node_delegation> reportingWidgetDelegationData = await dbContext.tco_widget_node_delegation
                                                                                                .Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                                            && x.budget_year == budgetYear
                                                                                                            && x.fk_master_node_id == Guid.Parse(item.node.id)
                                                                                                      ).ToListAsync();
                List<tco_doc_widget> widgetLst = new List<tco_doc_widget>();
                tco_doc_widget masterNodeWidgetData = new tco_doc_widget();
                List<Guid> widgetContentIdLst = new List<Guid>();
                if (reportingWidgetDelegationData != null && reportingWidgetDelegationData.Any())
                {
                    widgetContentIdLst = reportingWidgetDelegationData.Select(x => x.pk_node_id).ToList();
                }
                widgetContentIdLst.Add(Guid.Parse(item.node.id));
                widgetLst = await dbContext.tco_doc_widget.Where(x => x.fk_tenant_id == userDetails.tenant_id
                                                                          && x.budget_year == budgetYear
                                                                          && widgetContentIdLst.Contains(x.content_id))
                                                              .ToListAsync();
                
                //use master node widget data to update widget config for newly adding delegation nodes
                masterNodeWidgetData = widgetLst.FirstOrDefault(x => x.content_id == Guid.Parse(item.node.id));
                List<string> checkedOrgIdLst = delegationOrgTree.Where(x => x.@checked).Select(x => x.id).ToList();
                List<tco_widget_node_delegation> removableDelegationData = new List<tco_widget_node_delegation>();
                List<tco_doc_widget> removableWidgetData = new List<tco_doc_widget>();
                List<tco_widget_node_delegation> addDelegationData = new List<tco_widget_node_delegation>();
                List<tco_doc_widget> addWidgetData = new List<tco_doc_widget>();
                
                switch (docconfig)
                {
                    case 0:
                    case 1:
                        removableDelegationData = reportingWidgetDelegationData.Where(x => !checkedOrgIdLst.Contains(x.org_id)).ToList();
                        break;

                    case 2:
                        removableDelegationData = reportingWidgetDelegationData.Where(x => !checkedOrgIdLst.Contains(x.service_id)).ToList();
                        break;

                    default:
                        removableDelegationData = new List<tco_widget_node_delegation>();
                        break;
                }

                List<Guid> contentIdLst = removableDelegationData.Select(x => x.pk_node_id).ToList();
                removableWidgetData = widgetLst.Where(x => contentIdLst.Contains(x.content_id)).ToList();
                dbContext.tco_widget_node_delegation.RemoveRange(removableDelegationData);
                dbContext.tco_doc_widget.RemoveRange(removableWidgetData);

                OrgIdOrgLevelServiceIdServiceLevel orgServiceLevelInfo = await GetOrgAndServiceLevelInfoBasedOnConfig(userId);

                if (docconfig == 0)
                {
                    //for two level tree, if node is getting added at the first level, service level will be zero
                    orgServiceLevelInfo.serviceLevel = 0;
                }

                List<string> allDelegatedNodeOrgIdList = reportingWidgetDelegationData.Select(x => x.org_id).ToList();
                if (docconfig == 0 || docconfig == 1)
                {
                    checkedOrgIdLst = checkedOrgIdLst.Where(x => !allDelegatedNodeOrgIdList.Contains(x)).ToList();
                }
                else if (docconfig == 2)
                {
                    checkedOrgIdLst = checkedOrgIdLst.Where(x => !allDelegatedNodeOrgIdList.Contains(x)).ToList();
                }

                bool isOrgTree = false;
                if (docconfig == 1 || (docconfig == 0 && !isLevel2Tree))
                {
                    isOrgTree = true;
                }

                if (docconfig == 1 || docconfig == 2 || (docconfig == 0 && !isLevel2Tree))
                {
                    //creating delegation nodes either for org or service id list
                    foreach (var orgOrServiceInfo in checkedOrgIdLst)
                    {
                        orgServiceLevelInfo.orgId = isOrgTree ? orgOrServiceInfo : string.Empty;
                        orgServiceLevelInfo.serviceId = isOrgTree ? string.Empty : orgOrServiceInfo;
                        tco_widget_node_delegation widgetDelegation = await GetWidgetNodeDelegationEntity(userId, orgServiceLevelInfo, Guid.Parse(item.node.id), budgetYear);
                        addDelegationData.Add(widgetDelegation);
                        tco_doc_widget widgetData = await CreateDocWidget(userId, masterNodeWidgetData, widgetDelegation);
                        addWidgetData.Add(widgetData);
                    }
                    dbContext.tco_widget_node_delegation.AddRange(addDelegationData);
                    dbContext.tco_doc_widget.AddRange(addWidgetData);
                }
                else
                {
                    List<clsOrgIdsAndServiceIds> orgSerIds = await _consequentAdjustedBudget.GetOrgIdsAndServiceIdsAsync(userId, false, budgetYear);
                    //creating delegation nodes either for org or service id list
                    foreach (var orgId in checkedOrgIdLst)
                    {
                        var serviceIds = orgSerIds.Where(x => x.parentId == orgId.ToString() && !string.IsNullOrEmpty(x.orgId)).Select(y => y.orgId).OrderBy(z => z).Distinct().ToList();
                        foreach (var serId in serviceIds)
                        {
                            orgServiceLevelInfo.orgId = orgId;
                            orgServiceLevelInfo.serviceId = serId;
                            tco_widget_node_delegation widgetDelegation = await GetWidgetNodeDelegationEntity(userId, orgServiceLevelInfo, Guid.Parse(item.node.id), budgetYear);
                            addDelegationData.Add(widgetDelegation);
                            tco_doc_widget widgetData = await CreateDocWidget(userId, masterNodeWidgetData, widgetDelegation);
                            addWidgetData.Add(widgetData);
                        }
                    }
                    dbContext.tco_widget_node_delegation.AddRange(addDelegationData);
                    dbContext.tco_doc_widget.AddRange(addWidgetData);
                }
            }
            await dbContext.SaveChangesAsync();
        }

        public void DeleteOrgTreeForMasterNode(string userId, int budgetYear, Guid nodeId)
        {
            UserData userDetails = _utility.GetUserDetails(userId);
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            TcoPublishTemplate template = dbContext.TcoPublishTemplate.FirstOrDefault(x => x.FkTenantId == userDetails.tenant_id &&
                x.TreeType == PublishTreeType.BMMasterTemplate.ToString() &&
                x.BudgetYear == budgetYear);

            if (template == null)
            {
                return;
            }

            string orgTreePath = $"{userDetails.tenant_id}/{template.PkId}/{nodeId}.json".ToLower();
            _blobHelper.DeleteBlob(StorageAccount.AppStorage, BlobContainers.BmMasterTemplate, orgTreePath);
        }

        public void DeleteBudPhaseCustomNodeMapping(string userId, int budgetYear, Guid nodeId)
        {
            UserData userDetails = _utility.GetUserDetails(userId);
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            var budPhaseMapping = dbContext.tco_custom_node_budphase_mapping.Where(x => x.fk_node_id == nodeId && x.budget_year == budgetYear && x.fk_tenant_id == userDetails.tenant_id);
            dbContext.tco_custom_node_budphase_mapping.RemoveRange(budPhaseMapping);
            dbContext.SaveChanges();
        }


      
        public IEnumerable<PublishTreeNodeMin> GetOrgTreeForMasterNode(string userId, int budgetYear, string nodeId, int level)
        {
            return GetOrgTreeForMasterNodeAdmin(userId, budgetYear, nodeId, level, Guid.Empty);
        }

        public IEnumerable<PublishTreeNodeMin> GetOrgTreeForMasterNodeAdmin(string userId, int budgetYear, string nodeId, int level, Guid budgetPhaseId)
        {
            UserData userDetails = _utility.GetUserDetails(userId);
            TenantDBContext dbContext = _utility.GetTenantDBContext();
            TcoPublishTemplate template = dbContext.TcoPublishTemplate.FirstOrDefault(x => x.FkTenantId == userDetails.tenant_id &&
                x.TreeType == PublishTreeType.BMMasterTemplate.ToString() &&
                x.BudgetYear == budgetYear);

            if (template == null)
            {
                throw new InvalidDataException("Master template does not exist for this year");
            }

            var baseTree = GetOrgTree(userId, budgetYear, level == 1 ? TreeLevel.LevelOne: TreeLevel.BothLevels);

            string budgetPhaseOrgTreePath = getOrgTreeBlobPath(userDetails.tenant_id, template.PkId, nodeId, budgetPhaseId, level);

            try
            {
                string strSavedOrgTree = _blobHelper.GetTextBlob(StorageAccount.AppStorage, BlobContainers.BmMasterTemplate, budgetPhaseOrgTreePath);
                IEnumerable<PublishTreeNode> savedOrgTree = JsonConvert.DeserializeObject<IEnumerable<PublishTreeNode>>(strSavedOrgTree);
                var updatedTree = _publishTemplateManager.ApplyTemplate2(baseTree, savedOrgTree);

                string strUpdatedTree = JsonConvert.SerializeObject(updatedTree);
                var updatedTreeMin = JsonConvert.DeserializeObject<IEnumerable<PublishTreeNodeMin>>(strUpdatedTree);
                return updatedTreeMin;
            }
            catch
            {
                //saved org tree does not exist
                string strBaseTree = JsonConvert.SerializeObject(baseTree);
                var baseTreeMin = JsonConvert.DeserializeObject<IEnumerable<PublishTreeNodeMin>>(strBaseTree);

                return baseTreeMin;
            }
        }

        public async Task<IEnumerable<PublishTreeNodeMin>> GetOrgTreeForMasterNodeAsync(string userId, int budgetYear, Guid nodeId, IEnumerable<PublishTreeNode> baseTree, int publishTemplateId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            string orgTreePath = $"{userDetails.tenant_id}/{publishTemplateId}/{nodeId}.json".ToLower();
            try
            {
                string strSavedOrgTree = await _blobHelper.GetTextBlobAsync(StorageAccount.AppStorage, BlobContainers.BmMasterTemplate, orgTreePath);
                IEnumerable<PublishTreeNode> savedOrgTree = JsonConvert.DeserializeObject<IEnumerable<PublishTreeNode>>(strSavedOrgTree);
                
                var updatedTree = _publishTemplateManager.ApplyTemplate2(baseTree, savedOrgTree);

                string strUpdatedTree = JsonConvert.SerializeObject(updatedTree);
                var updatedTreeMin = JsonConvert.DeserializeObject<IEnumerable<PublishTreeNodeMin>>(strUpdatedTree);
                
                return updatedTreeMin;
            }
            catch
            {
                //saved org tree does not exist
                string strBaseTree = JsonConvert.SerializeObject(baseTree);
                var baseTreeMin = JsonConvert.DeserializeObject<IEnumerable<PublishTreeNodeMin>>(strBaseTree);

                return baseTreeMin;
            }
        }

        public IEnumerable<PublishTreeNode> RebuildTree(string userId, IEnumerable<PublishTreeNode> inputTree,
            int budgetYear)
        {
            var userDetails = _utility.GetUserDetails(userId);
            var rebuiltTree = inputTree.ToList();
            var flatInputTree = _templateUtilty.FlattenTree(rebuiltTree, 0, null, null, false);
            var langStringValuesBudgetManagement = _utility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "BudgetManagement");
            var treeNodeDefs = _utility.GetPulishNodeDefinitions(userId, PublishTreeType.BudgetManagement);

            var dynamicNodeTypes = new List<string>()
            {
                "TargetsCityLvl",
                "AssignmentsCityLvl",
                "AssessmentFPLevel1",
                "StrategyAssessment",
                "ClimateActionCategory",
                "ClimateActionCategoryTable",
                "ClimateActionsDescriptionCL",
                "ClimateActionBlistCategory",
                "ActionType",
                "Action",
                "StrategyServiceArea",
                "PopulationGraph",
                "PopulationTable",
                "PopulationStatHistorySection",
                "PopStatHistoryGrowthSection",
                "PopStatForecastSection",
                "PopStatForecastGrowthSection",
                "PopStatForecastExpenseSection",
                "PopulationDevelopmentSection",
                "PopulationText",
                "RevenueData",
                "ExpensesData",
                "FinancialExpensesAndRevenues",
                "FinancialProvisions",
                "BPStrategyAssessment",
                "BPAssessmentGuidelineDescription",
                "BPAssessmentDescription",
                "BPAssessmentConclusionDescription",
                "BPNetResultEffectSummery",
                "BPOperationActionSummery",
                "BPOperationActionsReductionSummery",
                "BPOperationActionsIncrementSummery",
                "BPAssessmentAreaActionsTable",
                "BPAssessmentArea",
                "BPAssessmentAreaActionsTable",
                "BPAssessmentAreaActionsTable",
                "AssessmentGuidlineDescription",
                "AssessmentDescription",
                "AssessmentConclusionDescription",
                "NetResultEffectSummery",
                "OperationActionSummery",
                "OperationActionsReductionSummery",
                "OperationActionsIncrementSummery",
                "AssessmentArea",
                "AssessmentAreaActionsTable",
                "AssessmentAreaDescription",
                "StrategyCityLvl"                  };

            var customLogicNodes = new List<string>
            {
                //AddTreeNode
                "InvestmentMainTableLvl1",//Convert to Static
                "InvestmentMainDetailLvl1",//Convert to Static
                "InvestmentMainStatusTableLvl1", //Convert to Static
                "InvestmentMainDetailStatusLvl1",//Convert to Static
            };

            var masterTemplateNodes = new List<string>
            {
                "FocusArea",
                "FocusAreaLongDescription",
                "ServiceId",
                "ServiceArea"
            };

            foreach (var nodeContainer in flatInputTree)
            {
                if (nodeContainer.node.type == "customNode")
                {
                    continue;
                }
                if (dynamicNodeTypes.Contains(nodeContainer.node.type) || customLogicNodes.Contains(nodeContainer.node.type))
                {
                    nodeContainer.node.text = string.Empty;
                    continue;
                }
                gmd_publish_tree_node_definitions current = treeNodeDefs.FirstOrDefault(x => x.type == nodeContainer.node.type);
                if (current == null)
                {
                    throw new DataNotFoundException($"Specified type - {nodeContainer.node.type} was not present in metadata");
                }

                string nodeText = string.Empty;
                switch (nodeContainer.node.type)
                {
                    case "FpTotalGraph":
                    case "FpInvestmentGraph":
                        nodeText = string.Join(" ",
                            langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == current.title_key).Value
                                .LangText, budgetYear);
                        break;

                    default:
                        nodeText = langStringValuesBudgetManagement.FirstOrDefault(v => v.Key == current.title_key)
                            .Value.LangText;
                        break;
                }

                nodeContainer.node.text = nodeText;
            }
            return rebuiltTree;
        }

        public async Task<List<KeyValuePair>> GetBMColumnSelectorAsync(string userId, int budgetYear, string gridType)
        {
            UserData user = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(user.language_preference, user.user_name, "BudgetManagement");
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            
            var flagName = gridType == "blist" ? "BM_B_LIST_COL_SEL" : "BM_ACTION_LIST_COL_SEL";
            string budmanBlistActionsGridLevel2 = await _utility.GetParameterValueAsync(userId, "BUDMAN_GROUPING_L2");
            List<KeyValuePair> columsconfig = new List<KeyValuePair>();
            List<KeyValuePair> columsToDisplayList = new List<KeyValuePair>();

            List<string> defaultCol = new List<string>() { "actionName", "year1Amount", "year2Amount", "year3Amount", "year4Amount", "includeInFinplan" };
            var BMColumnConfig = await tenantDbContext.tco_application_flag.Where(x => x.fk_tenant_id == user.tenant_id && x.flag_name == flagName && x.budget_year == budgetYear && (x.flag_key_id == "-1" || x.flag_key_id == user.pk_id.ToString())).ToListAsync();

            var tenantColumnConfig = BMColumnConfig.Any() ? BMColumnConfig.FirstOrDefault(x => x.flag_key_id == "-1") : null;
            var userColumnConfig = BMColumnConfig.Any() ? BMColumnConfig.FirstOrDefault(x => x.flag_key_id == user.pk_id.ToString()) : null;

            if (tenantColumnConfig != null || userColumnConfig != null)
            {
                var flagGuid = userColumnConfig != null ? userColumnConfig.flag_guid : tenantColumnConfig.flag_guid;
                if (flagGuid != null)
                {
                    clsTenantCloudTableEntity updateEntity = await _utility.GetCloudTableContentAsync("WADTenantData", user.tenant_id.ToString(), flagGuid.ToString());
                    if (updateEntity != null)
                    {
                        JArray pmInvColumnConfig = new JArray();
                        pmInvColumnConfig = JArray.FromObject(JsonConvert.DeserializeObject(updateEntity.data.ToString()));

                        columsconfig = (from s in pmInvColumnConfig
                                        select new KeyValuePair
                                        {
                                            key = (string)s["key"],
                                            value = (string)s["value"],
                                            isChecked = (bool)s["isChecked"],
                                        }).ToList();
                    }
                }
            }
            bool isChecked = true;
            string title = "";
            var allColumns = new JArray { "level1Obj.key", "tags", "level2Obj.key", "alterCodeObj.key", "priorityObj.key", "changeyear1", "changeyear2", "changeyear3", "changeyear4" };
            foreach (var item in allColumns)
            {
                switch (item.ToString())
                {
                    case "level1Obj.key":
                        title = langStrings.FirstOrDefault(x => x.Key.Equals("budman_blist_lvl1", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "tags":
                        title = langStrings.FirstOrDefault(x => x.Key.Equals("budman_blist_tags", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "level2Obj.key":
                        if (!string.IsNullOrEmpty(budmanBlistActionsGridLevel2))
                        {
                            title = langStrings.FirstOrDefault(x => x.Key.Equals("budman_blist_lvl2", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        }
                        break;

                    case "alterCodeObj.key":
                        title = langStrings.FirstOrDefault(x => x.Key.Equals("budman_blist_alt_code", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "priorityObj.key":
                        title = langStrings.FirstOrDefault(x => x.Key.Equals("budman_blist_priority", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                        break;

                    case "changeyear1":
                        title = langStrings.FirstOrDefault(x => x.Key.Equals("budman_change", StringComparison.InvariantCultureIgnoreCase)).Value.LangText + " " + budgetYear;
                        break;

                    case "changeyear2":
                        title = langStrings.FirstOrDefault(x => x.Key.Equals("budman_change", StringComparison.InvariantCultureIgnoreCase)).Value.LangText + " " + (budgetYear + 1);
                        break;

                    case "changeyear3":
                        title = langStrings.FirstOrDefault(x => x.Key.Equals("budman_change", StringComparison.InvariantCultureIgnoreCase)).Value.LangText + " " + (budgetYear + 2);
                        break;

                    case "changeyear4":
                        title = langStrings.FirstOrDefault(x => x.Key.Equals("budman_change", StringComparison.InvariantCultureIgnoreCase)).Value.LangText + " " + (budgetYear + 3);
                        break;
                }
                if (tenantColumnConfig != null || userColumnConfig != null)
                {
                    if (columsconfig.Count > 0 && columsconfig.Select(x => x.key).Contains(item.ToString()))
                    {
                        isChecked = columsconfig.FirstOrDefault(x => x.key == item.ToString()).isChecked;
                    }
                }
                else
                {
                    if (defaultCol.Contains(item.ToString()))
                    {
                        isChecked = true;
                    }
                }
                if (string.IsNullOrEmpty(budmanBlistActionsGridLevel2) && item.ToString() == "level2Obj.key")
                {
                    continue;
                }
                KeyValuePair temp = new KeyValuePair()
                {
                    key = item.ToString(),
                    value = title,
                    isChecked = isChecked
                };

                columsToDisplayList.Add(temp);
            }
            return columsToDisplayList;
        }

        public async Task SaveBMColumnSelectorAsync(string userId, BMColSelHelper columnSelectorInput, string gridType)
        {
            UserData user = await _utility.GetUserDetailsAsync(userId);
            string selectedColumns = JsonConvert.SerializeObject(columnSelectorInput.selectedColumns);
            var flagName = gridType == "blist" ? "BM_B_LIST_COL_SEL" : "BM_ACTION_LIST_COL_SEL";
            if (columnSelectorInput.isTenantSpecificColConfigSave)
            {
                await DeleteUserSpecificColumnConfigAsync(userId, flagName, columnSelectorInput.budgetYear);
            }
            if (columnSelectorInput.saveType == "reset")
                await DeleteUserSpecificColumnConfigAsync(userId, flagName, columnSelectorInput.budgetYear);
            else
                await _utility.SaveColumnsConfigAsync(userId, flagName, selectedColumns, columnSelectorInput.isTenantSpecificColConfigSave ? -1 : user.pk_id, columnSelectorInput.budgetYear);
        }

        public async Task<ApplicationFlagHelper> GetLockStatusAsync(string userId, bool flagStatus, int BudgetYear, int templateId, bool isAdminTree)
        {
            string flagName = isAdminTree ? $"{clsConstants.Application_Flag.BM_MASTER_TEMPLATE_EDIT_MODE.ToString()}" : $"{clsConstants.Application_Flag.DOC_EDIT_MODE.ToString()}_{templateId}";

            ApplicationFlagHelper lockStatusInfo = new ApplicationFlagHelper
            {
                flag_name = flagName,
                budget_year = BudgetYear,
                flag_status = flagStatus
            };

            var userRoleIds = (await _utility.GetUserRoleIdsAsync(userId)).ToList();
            if (userRoleIds.Any(x => x == 1 || x == 2))
            {
                lockStatusInfo.isRole1orRole2 = true;
            }

            tco_application_flag flagInDb = await _utility.GetLockStatusTreeAsync(lockStatusInfo, userId);
            if (flagInDb != null)
            {
                TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);

                var userInfo = await tenantDbContext.vwUserDetails.FirstOrDefaultAsync(x => x.tenant_id == userDetails.tenant_id && x.pk_id == flagInDb.updated_by);
                string firstName = userInfo != null && !string.IsNullOrEmpty(userInfo.first_name) ? userInfo.first_name : string.Empty;
                string lastName = userInfo != null && !string.IsNullOrEmpty(userInfo.last_name) ? userInfo.last_name : string.Empty;

                lockStatusInfo.updatedBy = flagInDb.updated_by.ToString();
                lockStatusInfo.isLocked = true;
                lockStatusInfo.userName = (firstName + " " + lastName).Trim();
            }

            return lockStatusInfo;
        }

        public async Task<string> DeleteUserSpecificColumnConfigAsync(string userId, string pageType, int budgetYear)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            var columnSelectorUserToDelete = await tenantDbContext.tco_application_flag.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.flag_name == pageType.ToUpper() && x.budget_year == budgetYear && x.period == 0 && x.flag_key_id == userDetails.pk_id.ToString()).ToListAsync();

            //Delete userSpecific column config from both blob and table
            if (columnSelectorUserToDelete.Count > 0)
            {
                foreach (var item in columnSelectorUserToDelete)
                {
                    clsTenantCloudTableEntity updateEntity = await _utility.GetCloudTableContentAsync("WADTenantData", userDetails.tenant_id.ToString(), item.flag_guid.ToString());
                    if (updateEntity != null)
                    {
                        await _utility.DeleteCloudTableContentAsync("WADTenantData", userDetails.tenant_id.ToString(), item.flag_guid.ToString());
                    }
                }
                tenantDbContext.tco_application_flag.RemoveRange(columnSelectorUserToDelete);
                await tenantDbContext.SaveChangesAsync();
            }
            return "success";
        }

        public TcoPublishTemplate GetPublishTemplateData(string userID, int templateID)
        {
            TcoPublishTemplate publishTemplate = GetPublishTemplateDataAsync(userID, templateID).GetAwaiter().GetResult();
            return publishTemplate;
        }

        public async Task<TcoPublishTemplate> GetPublishTemplateDataAsync(string userID, int templateID)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userID);
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            TcoPublishTemplate publishTemplate = await dbContext.TcoPublishTemplate.FirstOrDefaultAsync(x => x.FkTenantId == userDetails.tenant_id && x.PkId == templateID);
            return publishTemplate;
        }

        public async Task<tco_publish_config> GetPublishConfigAsync(int tenantId, int templateId)
        {
            var tenantDbContext = await _utility.GetTenantDBContextAsync();

            return await tenantDbContext.tco_publish_config.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.fk_template_id == templateId);
        }

        public async Task<tco_publish_config?> GetPublishConfigByConfigIdAsync(int tenantId, int configId)
        {
            var tenantDbContext = await _utility.GetTenantDBContextAsync();

            return await tenantDbContext.tco_publish_config.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.pk_id == configId);
        }

        public async Task<tco_publish_config?> GetPublishConfigAsync(int tenantId, int templateId, int configId)
        {
            var tenantDbContext = await _utility.GetTenantDBContextAsync();
            return await tenantDbContext.tco_publish_config.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.pk_id == configId && x.fk_template_id == templateId);
        }

        private string InsertJsonRequest(string jsonData, string userId)
        {
            return InsertJsonRequestAsync(jsonData, userId).GetAwaiter().GetResult();
        }

        private async Task<string> InsertJsonRequestAsync(string jsonData, string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            string path = $"{userDetails.tenant_id}-{userDetails.user_name}/{userDetails.tenant_id}-{userDetails.user_name}-{DateTime.UtcNow.ToString("yyyyMMddHHmmss")}.txt";
            await _blobHelper.UploadTextBlobAsync(StorageAccount.AppStorage, BlobContainers.ExportTreeData, path, jsonData);
            return path;
        }

        private async Task<List<tco_progress_status>> GetSyncStatusDropDown(int tenantId, SyncObjectStatusType syncObjectStatusType)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            return await tenantDbContext.tco_progress_status.Where(x => x.fk_tenant_id == tenantId && x.type == syncObjectStatusType.ToString()).OrderBy(x => x.status_id).ToListAsync();
        }

        private static string GetSyncStatus(bool isSyncSetup, List<tco_progress_status> syncStatusAllData, int status)
        {
            return (isSyncSetup && syncStatusAllData.Any(x => x.status_id == status)) ? syncStatusAllData.First(x => x.status_id == status).status_description : string.Empty;
        }

        private void ProcessNodes(string orgId, string serviceId, PublishTreeNode customNode, int budgetYear, UserData userDetails,
          TcoPublishTemplate template, List<string> budgetPhaseList, List<tco_custom_node_master> cnInstances,
          List<tco_custom_node_budphase_mapping> budPhaseMapping, List<tco_custom_node_master> cnLinksToBeCreated,
          List<tco_custom_node_budphase_mapping> cnBudPhaseMapping, List<TcoCustomNode> cnToBeCreated, string internalDesc)
        {
            var instance = cnInstances.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id &&
                                                           x.pk_master_node_id.Equals(Guid.Parse(customNode.id)) &&
                                                           x.org_id == orgId && x.service_id == serviceId);
            if (instance == null)
            {
                var instanceId = Guid.NewGuid();

                cnLinksToBeCreated.Add(new tco_custom_node_master
                {
                    pk_master_node_id = Guid.Parse(customNode.id),
                    fk_pub_temp_id = template.PkId,
                    cn_instance_id = instanceId,
                    fk_tenant_id = userDetails.tenant_id,
                    budget_year = budgetYear,
                    org_id = orgId,
                    service_id = serviceId,
                    tree_type = PublishTreeType.BudgetManagement.ToString(),
                    forecast_period = 0
                });

                bool shouldAddBudPhaseMapping = !cnBudPhaseMapping.Any(x => x.fk_node_id == Guid.Parse(customNode.id)) && !budPhaseMapping.Any(x => x.fk_node_id == Guid.Parse(customNode.id));

                if (shouldAddBudPhaseMapping)
                {
                    foreach (var item in budgetPhaseList)
                    {
                        cnBudPhaseMapping.Add(new tco_custom_node_budphase_mapping
                        {
                            budget_year = budgetYear,
                            fk_tenant_id = userDetails.tenant_id,
                            fk_budget_phase_id = Guid.Parse(item),
                            fk_node_id = Guid.Parse(customNode.id),
                            is_connected = true,
                            pk_mapping_id = Guid.NewGuid(),
                            updated = DateTime.UtcNow,
                            updated_by = userDetails.pk_id
                        });
                    }
                }

                cnToBeCreated.Add(new TcoCustomNode
                {
                    PkNodeId = instanceId,
                    BudgetYear = budgetYear,
                    FkTenantId = userDetails.tenant_id,
                    NodeTitle = customNode.text,
                    NodeTitleForDoc = customNode.text,
                    AbstractText = string.Empty,
                    Description = string.Empty,
                    NodePath = string.Empty,
                    BlobPath = string.Empty,
                    TreeType = PublishTreeType.BudgetManagement.ToString(),
                    Position = 0,
                    Updated = DateTime.UtcNow,
                    UpdatedBy = userDetails.pk_id,
                    DisplayLandscape = false,
                    Param1 = string.Empty,
                    ForecastPeriod = 0,
                    NodeType = CustomNodeType.Text.ToString(),
                    GuidanceText = string.Empty,
                    GuidanceTextId = null,
                    InternalDescription = internalDesc
                });
            }
        }

        public async Task UpdateMasterTemplateWithNodes(string userId, int budgetYear, int level, PublishTreeNode node)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            PublishTemplateHelper defTemplate = await _publishTemplateManager.GetMasterTemplateAsync(userId, budgetYear, PublishTreeType.BMMasterTemplate, 0, Guid.Empty);
            List<PublishTreeNode> level1TreeData = defTemplate.Tree.Where(x => x.id != "BM_doc_ServiceUnit").ToList();
            List<PublishTreeNode> level2TreeData = defTemplate.Tree.Where(x => x.id == "BM_doc_ServiceUnit").ToList();
            if (level == 1)
            {
                level1TreeData.Add(node);
            }
            else
            {
                level2TreeData.Add(node);
            }
            PublishTemplateHelper mergedTreeData = await MergeBMAdminTwoLevelTreeData(userId,
                new AdminTemplateTreeHelper()
                {
                    level1TreeData = new PublishTemplateHelper()
                    {
                        Tree = level1TreeData,
                        BudgetYear = budgetYear,
                        Id = defTemplate.Id,
                        Name = PublishTreeType.BMMasterTemplate.ToString(),
                        ShortName = PublishTreeType.BMMasterTemplate.ToString(),
                    },
                    level2TreeData = new PublishTemplateHelper()
                    {
                        Tree = level2TreeData,
                        BudgetYear = budgetYear,
                        Id = defTemplate.Id,
                        Name = PublishTreeType.BMMasterTemplate.ToString(),
                        ShortName = PublishTreeType.BMMasterTemplate.ToString(),
                    }
                });
            await UpdateMasterTemplateAsync(userId, mergedTreeData);
        }

        public async Task UpdateNodeTitleInMasterTemplate(string userId, int budgetYear, string nodeId, string titleText)
        {
            TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            PublishTemplateHelper defTemplate = await _publishTemplateManager.GetMasterTemplateAsync(userId, budgetYear, PublishTreeType.BMMasterTemplate, 0, Guid.Empty);
            if (defTemplate.Tree.FirstOrDefault(x => x.id == nodeId) != null)
            {
                defTemplate.Tree.FirstOrDefault(x => x.id == nodeId).text = titleText;
            }
            await UpdateMasterTemplateAsync(userId, defTemplate);
        }
    }
}