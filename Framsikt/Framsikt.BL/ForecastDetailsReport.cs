#pragma warning disable CS8600

using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Newtonsoft.Json.Linq;
using System.Data;
using System.Globalization;
using Microsoft.EntityFrameworkCore;
using KeyValuePair = Framsikt.BL.Helpers.KeyValuePair;
using Microsoft.Extensions.DependencyInjection;

namespace Framsikt.BL
{
    public class ForecastDetailsReport : IForecastDetailsReport
    {
        private readonly IUtility _utility;
        private readonly IReportingUtility _reportingUtil;

        public ForecastDetailsReport(IServiceProvider container)
        {
            _utility = container.GetRequiredService<IUtility>();
            _reportingUtil = container.GetRequiredService<IReportingUtility>();
        }

        public async Task<List<KeyValuePair>> FormatMonthYearData(string userName, string jsDateTime)
        {
            int numberOfMonthsYear = 24;
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            List<KeyValuePair> keyValueDateTimeStrVal = new List<KeyValuePair>();
            UserData userDetails = await _utility.GetUserDetailsAsync(userName);
            Dictionary<string, clsLanguageString> langStringValuesCommon = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Common");

            string[] monthFields = new string[] { ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_january_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_february_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_march_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_april_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_may_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_june_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_july_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_august_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_september_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_october_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_november_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_december_short3text")).Value).LangText,
                ((langStringValuesCommon.FirstOrDefault(v => v.Key == "month_rebudget_13th")).Value).LangText
            };

            DateTime dYearMonth = DateTime.Parse(jsDateTime);

            var latestForecast = await tenantDbContext.tmr_data_warehouse.Where(t => t.fk_tenant_id == userDetails.tenant_id).Select(z => z.forecast_period).Distinct().ToListAsync();
            int forecastPeriod = latestForecast.Count > 0 ? latestForecast.OrderByDescending(x => x).FirstOrDefault() : 0;
            tmr_period_setup rebudMonSetPrvLast = tenantDbContext.tmr_period_setup.FirstOrDefault(t => t.fk_tenant_id == userDetails.tenant_id && t.forecast_period == ((dYearMonth.Year - 2) * 100 + 13));
            tmr_period_setup rebudMonSetupLastYr = tenantDbContext.tmr_period_setup.FirstOrDefault(t => t.fk_tenant_id == userDetails.tenant_id && t.forecast_period == ((dYearMonth.Year - 1) * 100 + 13));
            tmr_period_setup rebudMonSetupCurrYr = tenantDbContext.tmr_period_setup.FirstOrDefault(t => t.fk_tenant_id == userDetails.tenant_id && t.forecast_period == (dYearMonth.Year * 100 + 13));

            if (forecastPeriod != 0)
            {
                DateTime dYearMonthLatestForecast = DateTime.Parse(forecastPeriod.ToString().Insert(4, "-"));

                for (int i = numberOfMonthsYear * -1; i < numberOfMonthsYear; i++)
                {
                    KeyValuePair yearMonthKeyVal = new KeyValuePair();

                    yearMonthKeyVal.key = dYearMonth.AddMonths(i).ToString(CultureInfo.InvariantCulture);
                    yearMonthKeyVal.value = monthFields[dYearMonth.AddMonths(i).Month - 1] + " " + dYearMonth.AddMonths(i).Year;

                    var latestForecastPeriod = monthFields[dYearMonthLatestForecast.AddMonths(0).Month - 1] + " " + dYearMonthLatestForecast.AddMonths(0).Year;
                    if (string.Equals(yearMonthKeyVal.value, latestForecastPeriod))
                    {
                        yearMonthKeyVal.isChecked = true;
                    }

                    keyValueDateTimeStrVal.Add(yearMonthKeyVal);

                    if (dYearMonth.AddMonths(i).Month == numberOfMonthsYear)
                    {
                        if ((i < -12 && rebudMonSetPrvLast != null) || (i >= -12 && i < 0 && rebudMonSetupLastYr != null) || (i > 0 && rebudMonSetupCurrYr != null))
                        {
                            KeyValuePair rebudget = new KeyValuePair();
                            rebudget.key = "13" + (dYearMonth.AddMonths(i).ToString(CultureInfo.InvariantCulture)).Substring(2);
                            rebudget.value = monthFields[numberOfMonthsYear] + " " + dYearMonth.AddMonths(i).Year;
                            keyValueDateTimeStrVal.Add(rebudget);
                        }
                    }
                    if (i == 12)//#116543 - one year forward is needed 
                    {
                        break;
                    }
                }
            }

            return keyValueDateTimeStrVal;
        }

        public List<ReportColumnHelper> GetReportColumns(string userId)
        {
            return _reportingUtil.GetReportColumns(userId, 0, QueryReportType.InvestmentFinanceReport, false, false).ToList();
        }

        public dynamic SearchFields(string userId, reportFilterInput filterInput)
        {
            dynamic data = _reportingUtil.GetSearchFields(userId, filterInput, QueryReportType.InvestmentFinanceReport);

            return data;
        }

        public async Task<dynamic> GetFilteredReport(string userId, reportFilterInput filterInput)
        {
            JObject newJson = await _reportingUtil.GetFilteredReportAsync(userId, filterInput, QueryReportType.InvestmentFinanceReport, filterInput.excludeZero);
            return newJson;
        }

        public DataTable GetFilteredReport2(string userId, reportFilterInput filterInput)
        {
            DataTable data = _reportingUtil.GetFilteredReport2(userId, filterInput, QueryReportType.InvestmentFinanceReport, filterInput.excludeZero);
            return data;
        }
    }
}