using Aspose.Cells;
using EntityFrameworkExtras.EFCore;
using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using System.Data;
using System.Text.RegularExpressions;

namespace Framsikt.BL;

public partial class ActionImport 
{
    public async Task ImportExcelToStagingTable(string userId, Workbook wb, int budgetYear, int changeId, bool isImportPeriodicKey, bool isbudgetChangeChecked, List<string> colNames, string pageId, bool isBlist = false, bool isAdjustmentCodeChecked = false, string adjustmentCode = null)
    {
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        var tenantDbContext = await _utility.GetTenantDBContextAsync();
        bool hasTenYearSetup = !string.IsNullOrEmpty(pageId) && pageId == "fpactions";

        var accountsTco = (from a in tenantDbContext.tco_accounts
            where a.pk_tenant_id == userDetails.tenant_id
                  && a.dateFrom.Year <= budgetYear
                  && a.dateTo.Year >= budgetYear
            select a).ToList();

        Worksheet ws = wb.Worksheets[0];
        ExportTableOptions opt = new ExportTableOptions
        {
            ExportColumnName = true,
            FormatStrategy = CellValueFormatStrategy.CellStyle,
            ExportAsString = false
        };

        //Reset the workflow state by deleting any completed jobs
        await _utility.DeleteCompletedJobsAsync(userId, UserTrackedJobs.ActionImport);

        for (int i = 0; i < colNames.Count; i++)
        {
            ws.Cells[0, i].Value = colNames[i];
        }

        var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));
        List<tmd_acc_defaults> defaultValues = (await _yearlyBudget.GetDefaultsForBudgetEntrySectionAsync(userId, "BU", "DEFAULT_POPUP", orgVersionContent.orgVersion)).ToList();

        //For action Id, Year1Amount, Year2Amount, Year3Amount, replace blanks with 0
        int year1AmountColNo = colNames.IndexOf("Year1Amount");
        int year2AmountColNo = colNames.IndexOf("Year2Amount");
        int year3AmountColNo = colNames.IndexOf("Year3Amount");
        int year4AmountColNo = colNames.IndexOf("Year4Amount");
        int year5AmountColNo = -1, year6AmountColNo = -1, year7AmountColNo = -1, year8AmountColNo = -1, year9AmountColNo = -1, year10AmountColNo = -1;
        if (isbudgetChangeChecked || hasTenYearSetup)
            year5AmountColNo = colNames.IndexOf("Year5Amount");
        if (hasTenYearSetup)
        {
            year6AmountColNo = colNames.IndexOf("Year6Amount");
            year7AmountColNo = colNames.IndexOf("Year7Amount");
            year8AmountColNo = colNames.IndexOf("Year8Amount");
            year9AmountColNo = colNames.IndexOf("Year9Amount");
            year10AmountColNo = colNames.IndexOf("Year10Amount");
        }
        int accountColNo = colNames.IndexOf("AccountCode");
        int departmentColNo = colNames.IndexOf("DepartmentCode");
        int functionColNo = colNames.IndexOf("FunctionCode");
        int projectColNo = colNames.IndexOf("ProjectCode");

        int freeDim1ColNo = colNames.IndexOf("FreeDim1");
        int freeDim2ColNo = colNames.IndexOf("FreeDim2");
        int freeDim3ColNo = colNames.IndexOf("FreeDim3");
        int freeDim4ColNo = colNames.IndexOf("FreeDim4");

        int adjustmentCodeColNo = colNames.IndexOf("AdjustmentCode");
        int alterCodeColNo = colNames.IndexOf("AlterCode");

        int periodicKeyColNo = colNames.IndexOf("PeriodicKey");

        int urlColNo = colNames.IndexOf("Url");
        int descColNo = colNames.IndexOf("Description");

        bool isDataRow = false;
        for (int i = 0; i < ws.Cells.MaxDataRow + 1; i++)
        {
            //Action Id
            if (ws.Cells[i, 0].Value == null)
            {
                ws.Cells[i, 0].Value = 0;
            }
            else
            {
                int num2;
                if (int.TryParse(Convert.ToString(ws.Cells[i, 0].Value).Trim(), out num2))
                {
                    ws.Cells[i, 0].Value = Convert.ToInt32(Convert.ToString(ws.Cells[i, 0].Value).Trim());
                }
            }
            //Action Title
            if (ws.Cells[i, 1].Value == null)
            {
                ws.Cells[i, 1].Value = "";
            }
            decimal yearValOut;
            if (ws.Cells[i, year1AmountColNo].Value == null || (isDataRow && !(decimal.TryParse(Convert.ToString(ws.Cells[i, year1AmountColNo].Value), out yearValOut))))
            {
                ws.Cells[i, year1AmountColNo].Value = 0;
            }

            if (ws.Cells[i, year2AmountColNo].Value == null || (isDataRow && !(decimal.TryParse(Convert.ToString(ws.Cells[i, year2AmountColNo].Value), out yearValOut))))
            {
                ws.Cells[i, year2AmountColNo].Value = 0;
            }

            if (ws.Cells[i, year3AmountColNo].Value == null || (isDataRow && !(decimal.TryParse(Convert.ToString(ws.Cells[i, year3AmountColNo].Value), out yearValOut))))
            {
                ws.Cells[i, year3AmountColNo].Value = 0;
            }

            if (ws.Cells[i, year4AmountColNo].Value == null || (isDataRow && !(decimal.TryParse(Convert.ToString(ws.Cells[i, year4AmountColNo].Value), out yearValOut))))
            {
                ws.Cells[i, year4AmountColNo].Value = 0;
            }
            if ((isbudgetChangeChecked || hasTenYearSetup) && ws.Cells[i, year5AmountColNo].Value == null || (isDataRow && !(decimal.TryParse(Convert.ToString(ws.Cells[i, year5AmountColNo].Value), out yearValOut))))
            {
                ws.Cells[i, year5AmountColNo].Value = 0;
            }
            if (hasTenYearSetup)
            {
                if (ws.Cells[i, year6AmountColNo].Value == null || (isDataRow && !(decimal.TryParse(Convert.ToString(ws.Cells[i, year6AmountColNo].Value), out yearValOut))))
                {
                    ws.Cells[i, year6AmountColNo].Value = 0;
                }
                if (ws.Cells[i, year7AmountColNo].Value == null || (isDataRow && !(decimal.TryParse(Convert.ToString(ws.Cells[i, year7AmountColNo].Value), out yearValOut))))
                {
                    ws.Cells[i, year7AmountColNo].Value = 0;
                }
                if (ws.Cells[i, year8AmountColNo].Value == null || (isDataRow && !(decimal.TryParse(Convert.ToString(ws.Cells[i, year8AmountColNo].Value), out yearValOut))))
                {
                    ws.Cells[i, year8AmountColNo].Value = 0;
                }
                if (ws.Cells[i, year9AmountColNo].Value == null || (isDataRow && !(decimal.TryParse(Convert.ToString(ws.Cells[i, year9AmountColNo].Value), out yearValOut))))
                {
                    ws.Cells[i, year9AmountColNo].Value = 0;
                }
                if (ws.Cells[i, year10AmountColNo].Value == null || (isDataRow && !(decimal.TryParse(Convert.ToString(ws.Cells[i, year10AmountColNo].Value), out yearValOut))))
                {
                    ws.Cells[i, year10AmountColNo].Value = 0;
                }
            }
            if (accountColNo != -1)
            {
                if (ws.Cells[i, accountColNo].Value == null)
                {
                    ws.Cells[i, accountColNo].Value = string.Empty;
                }
                else
                {
                    ws.Cells[i, accountColNo].Value = ws.Cells[i, accountColNo].DisplayStringValue;
                }
            }

            if (departmentColNo != -1)
            {
                if (ws.Cells[i, departmentColNo].Value == null)
                {
                    ws.Cells[i, departmentColNo].Value = string.Empty;
                }
                else
                {
                    ws.Cells[i, departmentColNo].Value = ws.Cells[i, departmentColNo].DisplayStringValue;
                }
            }

            if (functionColNo != -1)
            {
                if (ws.Cells[i, functionColNo].Value == null)
                {
                    ws.Cells[i, functionColNo].Value = string.Empty;
                }
                else
                {
                    ws.Cells[i, functionColNo].Value = ws.Cells[i, functionColNo].DisplayStringValue;
                }
            }

            if (projectColNo != -1)
            {
                if (ws.Cells[i, projectColNo].Value == null && !defaultValues.Any() && defaultValues.FirstOrDefault(x => x.acc_type == "PROJECT") == null)
                {
                    ws.Cells[i, projectColNo].Value = string.Empty;
                }
                else if (string.IsNullOrEmpty(ws.Cells[i, projectColNo].DisplayStringValue) && defaultValues.Any() && defaultValues.FirstOrDefault(x => x.acc_type == "PROJECT") != null)
                {
                    ws.Cells[i, projectColNo].Value = defaultValues.FirstOrDefault(x => x.acc_type == "PROJECT").acc_value;
                }
                else
                {
                    ws.Cells[i, projectColNo].Value = ws.Cells[i, projectColNo].DisplayStringValue ?? string.Empty;
                }
            }

            if (freeDim1ColNo != -1)
            {
                if (ws.Cells[i, freeDim1ColNo].Value == null && !defaultValues.Any() && defaultValues.FirstOrDefault(x => x.acc_type == "FREE_DIM_1") == null)
                {
                    ws.Cells[i, freeDim1ColNo].Value = string.Empty;
                }
                else if (string.IsNullOrEmpty(ws.Cells[i, freeDim1ColNo].DisplayStringValue) && defaultValues.Any() && defaultValues.FirstOrDefault(x => x.acc_type == "FREE_DIM_1") != null)
                {
                    ws.Cells[i, freeDim1ColNo].Value = defaultValues.FirstOrDefault(x => x.acc_type == "FREE_DIM_1").acc_value;
                }
                else
                {
                    ws.Cells[i, freeDim1ColNo].Value = ws.Cells[i, freeDim1ColNo].DisplayStringValue ?? string.Empty;
                }
            }

            if (freeDim2ColNo != -1)
            {
                if (ws.Cells[i, freeDim2ColNo].Value == null && !defaultValues.Any() && defaultValues.FirstOrDefault(x => x.acc_type == "FREE_DIM_2") == null)
                {
                    ws.Cells[i, freeDim2ColNo].Value = string.Empty;
                }
                else if (string.IsNullOrEmpty(ws.Cells[i, freeDim2ColNo].DisplayStringValue) && defaultValues.Any() && defaultValues.FirstOrDefault(x => x.acc_type == "FREE_DIM_2") != null)
                {
                    ws.Cells[i, freeDim2ColNo].Value = defaultValues.FirstOrDefault(x => x.acc_type == "FREE_DIM_2").acc_value;
                }
                else
                {
                    ws.Cells[i, freeDim2ColNo].Value = ws.Cells[i, freeDim2ColNo].DisplayStringValue ?? string.Empty;
                }
            }

            if (freeDim3ColNo != -1)
            {
                if (ws.Cells[i, freeDim3ColNo].Value == null && !defaultValues.Any() && defaultValues.FirstOrDefault(x => x.acc_type == "FREE_DIM_3") == null)
                {
                    ws.Cells[i, freeDim3ColNo].Value = string.Empty;
                }
                else if (string.IsNullOrEmpty(ws.Cells[i, freeDim3ColNo].DisplayStringValue) && defaultValues.Any() && defaultValues.FirstOrDefault(x => x.acc_type == "FREE_DIM_3") != null)
                {
                    ws.Cells[i, freeDim3ColNo].Value = defaultValues.FirstOrDefault(x => x.acc_type == "FREE_DIM_3").acc_value;
                }
                else
                {
                    ws.Cells[i, freeDim3ColNo].Value = ws.Cells[i, freeDim3ColNo].DisplayStringValue ?? string.Empty;
                }
            }

            if (freeDim4ColNo != -1)
            {
                if (ws.Cells[i, freeDim4ColNo].Value == null && !defaultValues.Any() && defaultValues.FirstOrDefault(x => x.acc_type == "FREE_DIM_4") == null)
                {
                    ws.Cells[i, freeDim4ColNo].Value = string.Empty;
                }
                else if (string.IsNullOrEmpty(ws.Cells[i, freeDim4ColNo].DisplayStringValue) && defaultValues.Any() && defaultValues.FirstOrDefault(x => x.acc_type == "FREE_DIM_4") != null)
                {
                    ws.Cells[i, freeDim4ColNo].Value = defaultValues.FirstOrDefault(x => x.acc_type == "FREE_DIM_4").acc_value;
                }
                else
                {
                    ws.Cells[i, freeDim4ColNo].Value = ws.Cells[i, freeDim4ColNo].DisplayStringValue ?? string.Empty;
                }
            }

            if (adjustmentCodeColNo != -1)
            {
                if (ws.Cells[i, adjustmentCodeColNo].Value == null)
                {
                    ws.Cells[i, adjustmentCodeColNo].Value = string.Empty;
                }
                else
                {
                    ws.Cells[i, adjustmentCodeColNo].Value = ws.Cells[i, adjustmentCodeColNo].DisplayStringValue;
                }
            }

            if (alterCodeColNo != -1)
            {
                if (ws.Cells[i, alterCodeColNo].Value == null)
                {
                    ws.Cells[i, alterCodeColNo].Value = string.Empty;
                }
                else
                {
                    ws.Cells[i, alterCodeColNo].Value = ws.Cells[i, alterCodeColNo].DisplayStringValue;
                }
            }

            if (periodicKeyColNo != -1)
            {
                var periodicKey = string.Empty;

                if (accountColNo != -1 && ws.Cells[i, accountColNo].Value != null)
                {
                    var accCode = Convert.ToString(ws.Cells[i, accountColNo].Value);

                    if (accountsTco.FirstOrDefault(z => z.pk_account_code == accCode) != null &&
                        accountsTco.FirstOrDefault(z => z.pk_account_code == accCode).fk_key_id != null)
                    {
                        periodicKey = accountsTco.FirstOrDefault(z => z.pk_account_code == accCode).fk_key_id.Value.ToString();
                    }
                    else
                    {
                        periodicKey = "0";
                    }
                }

                if (ws.Cells[i, periodicKeyColNo].Value == null)
                {
                    ws.Cells[i, periodicKeyColNo].Value = periodicKey;
                }
                else
                {
                    ws.Cells[i, periodicKeyColNo].Value = ws.Cells[i, periodicKeyColNo].DisplayStringValue;
                }
            }

            if (descColNo != -1)
            {
                if (ws.Cells[i, descColNo].Value == null)
                {
                    ws.Cells[i, descColNo].Value = string.Empty;
                }
                else
                {
                    ws.Cells[i, descColNo].Value = ws.Cells[i, descColNo].DisplayStringValue;
                }
            }

            if (urlColNo != -1)
            {
                if (ws.Cells[i, urlColNo].Value == null)
                {
                    ws.Cells[i, urlColNo].Value = string.Empty;
                }
                else
                {
                    ws.Cells[i, urlColNo].Value = ws.Cells[i, urlColNo].DisplayStringValue;
                }
            }

            isDataRow = true;
        }
        //Determine number of rows to import
        //From the last row, move up until the account code is not blank
        //This gives the number of rows to be imported
        int rowsToImport = ws.Cells.MaxDataRow;
        for (int i = rowsToImport; i > 0; i--)
        {
            Cell c = ws.Cells[i, 2];
            if (string.IsNullOrEmpty(c.Value?.ToString()))
            {
                continue;
            }

            rowsToImport = i;
            break;
        }
        var tbl = ws.Cells.ExportDataTable(0, 0, rowsToImport + 1, ws.Cells.MaxDataColumn + 1, opt);
        tbl.AcceptChanges();

        UserData userInfo = await _utility.GetUserDetailsAsync(userId);
        List<TfpStageActionImport> dataSet;
        dataSet = (from DataRow row in tbl.Rows
            select new TfpStageActionImport
            {
                TenantId = userInfo.tenant_id,
                UserId = userInfo.pk_id,
                BudgetYear = budgetYear,
                ChangeId = changeId,
                ActionType = 0,
                ActionId = Convert.ToInt32(row["ActionId"]),
                ActionTitle = row["ActionTitle"].ToString(),
                AccountCode = accountColNo != -1 ? row["AccountCode"].ToString() : string.Empty,
                DepartmentCode = departmentColNo != -1 ? row["DepartmentCode"].ToString() : string.Empty,
                FunctionCode = functionColNo != -1 ? row["FunctionCode"].ToString() : string.Empty,
                ProjectCode = projectColNo != -1 ? row["ProjectCode"].ToString() : string.Empty,
                Year1Amount = Convert.ToDecimal(row["Year1Amount"]),
                Year2Amount = Convert.ToDecimal(row["Year2Amount"]),
                Year3Amount = Convert.ToDecimal(row["Year3Amount"]),
                Year4Amount = Convert.ToDecimal(row["Year4Amount"]),
                Year5Amount = isbudgetChangeChecked || hasTenYearSetup ? Convert.ToDecimal(row["Year5Amount"]) : 0,
                Year6Amount = hasTenYearSetup ? Convert.ToDecimal(row["Year6Amount"]) : 0,
                Year7Amount = hasTenYearSetup ? Convert.ToDecimal(row["Year7Amount"]) : 0,
                Year8Amount = hasTenYearSetup ? Convert.ToDecimal(row["Year8Amount"]) : 0,
                Year9Amount = hasTenYearSetup ? Convert.ToDecimal(row["Year9Amount"]) : 0,
                Year10Amount = hasTenYearSetup ? Convert.ToDecimal(row["Year10Amount"]) : 0,
                FreeDim1 = freeDim1ColNo != -1 ? row["FreeDim1"].ToString() : string.Empty,
                FreeDim2 = freeDim2ColNo != -1 ? row["FreeDim2"].ToString() : string.Empty,
                FreeDim3 = freeDim3ColNo != -1 ? row["FreeDim3"].ToString() : string.Empty,
                FreeDim4 = freeDim4ColNo != -1 ? row["FreeDim4"].ToString() : string.Empty,
                Description = row["Description"].ToString(),
                AdjustmentCode = adjustmentCodeColNo != -1 ? row["AdjustmentCode"].ToString() : string.Empty,
                AlterCode = alterCodeColNo != -1 ? row["AlterCode"].ToString() : string.Empty,
                PeriodicKey = periodicKeyColNo != -1 ? row["PeriodicKey"].ToString() : string.Empty,
                Year1AmountError = false,
                Year2AmountError = false,
                Year3AmountError = false,
                Year4AmountError = false,
                Year5AmountError = false,
                Year6AmountError = false,
                Year7AmountError = false,
                Year8AmountError = false,
                Year9AmountError = false,
                Year10AmountError = false,
                Url = urlColNo != -1 ? row["Url"].ToString() : string.Empty,
                fkUserAdjCode = adjustmentCode
            }).ToList();

        await tenantDbContext.BulkInsertAsync(dataSet);
        await ValidateActions(userId, budgetYear, changeId, isbudgetChangeChecked, isBlist);
    }

    private static string ValidateDate(string DateValue)
    {
        DateTime dtResut;
        if (DateTime.TryParse(DateValue, out dtResut))
        {
            return DateTime.Parse(DateValue).ToShortDateString();
        }

        return DateValue;//57400 removed seting to empty string to what is entered
    }

    public async Task<List<string>> GetColumnNames(string userId, string pageId, bool isImportwithPeriodicKey = false, bool isbudgetChangeChecked = false)
    {
        IEnumerable<freedimDefinition> freeDims = await _utility.GetFreeDimDefinitionAsync(userId);
        List<freedimDefinition> freeDimColumns = freeDims.ToList();

        Dictionary<string, string> adjustmentCodes = await _finUtility.GetAdjustmentCodesAsync(userId);
        bool hasTenYearSetup = !string.IsNullOrEmpty(pageId) && pageId == "fpactions";
        List<string> colNames = new List<string>
        {
            "ActionId",
            "ActionTitle",
            "AccountCode",
            "DepartmentCode",
            "FunctionCode",
            "ProjectCode"
        };

        if (freeDimColumns.Count > 0)
        {
            colNames.Add("FreeDim1");
        }
        if (freeDimColumns.Count > 1)
        {
            colNames.Add("FreeDim2");
        }
        if (freeDimColumns.Count > 2)
        {
            colNames.Add("FreeDim3");
        }
        if (freeDimColumns.Count > 3)
        {
            colNames.Add("FreeDim4");
        }
        if (adjustmentCodes.Any())
        {
            colNames.Add("AdjustmentCode");
        }
        colNames.Add("AlterCode");

        colNames.Add("Year1Amount");
        colNames.Add("Year2Amount");
        colNames.Add("Year3Amount");
        colNames.Add("Year4Amount");
        if (isbudgetChangeChecked || hasTenYearSetup)
        {
            colNames.Add("Year5Amount");
        }
        if (hasTenYearSetup)
        {
            colNames.Add("Year6Amount");
            colNames.Add("Year7Amount");
            colNames.Add("Year8Amount");
            colNames.Add("Year9Amount");
            colNames.Add("Year10Amount");
        }
        colNames.Add("PeriodicKey");
        colNames.Add("Description");
        if (pageId == "ybactions")
        {
            colNames.Add("Url");
        }
        return colNames;
    }

    private async Task<List<string>> GetColumnNamesForBudgetImport(string userId)
    {
        IEnumerable<freedimDefinition> freeDims = await _utility.GetFreeDimDefinitionsBasedOnPageIdAsync(userId, "YB_FINANCING");
        List<freedimDefinition> freeDimColumns = freeDims.ToList();
        List<string> colNames = new List<string>
        {
            "AccountCode",
            "DepartmentCode",
            "FunctionCode",
            "ProjectCode"
        };

        if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_1") != null)
        {
            colNames.Add("FreeDim1");
        }
        if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_2") != null)
        {
            colNames.Add("FreeDim2");
        }
        if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_3") != null)
        {
            colNames.Add("FreeDim3");
        }
        if (freeDimColumns.Count != 0 && freeDimColumns.FirstOrDefault(x => x.freeDimColumn == "free_dim_4") != null)
        {
            colNames.Add("FreeDim4");
        }

        colNames.Add("TotalBudget");
        colNames.Add("Comments");
        colNames.Add("PeriodicKey");
        return colNames;
    }

    public async Task<ActionData> GetImportedActionsByError(string userId, int budgetYear, int changeId)
    {
        TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
        UserData userData = await _utility.GetUserDetailsAsync(userId);

        List<TfpStageActionImport> allActions =
            await tenantDbContext.TfpStageActionImport.Where(x => x.UserId == userData.pk_id &&
                                                                  x.TenantId == userData.tenant_id &&
                                                                  x.BudgetYear == budgetYear &&
                                                                  x.ChangeId == changeId)
                .OrderByDescending(y => y.ErrorCount)
                .ToListAsync();

        ActionData ret = new ActionData()
        {
            Actions = allActions,
            TotalCount = allActions.Count
        };
        return ret;
    }

    public ActionData GetImportedActionsByError(string userId, int budgetYear, int changeId, int take, int skip, int page, int pageSize)
    {
        TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
        UserData userData = _utility.GetUserDetails(userId);

        List<TfpStageActionImport> allActions =
            tenantDbContext.TfpStageActionImport.Where(x => x.UserId == userData.pk_id &&
                                                            x.TenantId == userData.tenant_id &&
                                                            x.BudgetYear == budgetYear &&
                                                            x.ChangeId == changeId)
                .OrderByDescending(y => y.ErrorCount)
                .ToList();

        List<TfpStageActionImport> actions = allActions.OrderBy(x => x.PkId).Skip(skip).Take(take).ToList();
        ActionData ret = new ActionData()
        {
            Actions = actions,
            TotalCount = allActions.Count
        };
        return ret;
    }



    public async Task<DataTable> GetActionsForExport(string userId, int budgetYear, int changeId, string pageId, bool importViaPeriodicKey, bool isImportwithPeriodicKeyNextYear = false)
    {
        TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
        UserData userData = await _utility.GetUserDetailsAsync(userId);

        List<TfpStageActionImport> actions =
            tenantDbContext.TfpStageActionImport.Where(x => x.UserId == userData.pk_id &&
                                                            x.TenantId == userData.tenant_id &&
                                                            x.BudgetYear == budgetYear &&
                                                            x.ChangeId == changeId)
                .OrderBy(y => y.ActionTitle)
                .ToList();

        DataTable table = _utility.ConvertToDataTable(actions);
        List<string> columns = isImportwithPeriodicKeyNextYear ? await GetColumnNames(userId, pageId, isImportwithPeriodicKeyNextYear, true) : await GetColumnNames(userId, pageId, importViaPeriodicKey, true);
        List<DataColumn> toBeDeleted = new List<DataColumn>();
        //Remove unwanted columns
        foreach (DataColumn col in table.Columns)
        {
            if (!columns.Contains(col.ColumnName))
            {
                toBeDeleted.Add(col);
            }
        }

        foreach (DataColumn colDel in toBeDeleted)
        {
            table.Columns.Remove(colDel);
        }
        //re-order  columns as per columns collection
        int i = 0;
        foreach (string col in columns)
        {
            table.Columns[col].SetOrdinal(i);
            i++;
        }
        return table;
    }



    public async Task UpdateImportedActions(string userId, int budgetYear, int changeId, IEnumerable<TfpStageActionImport> actions, bool budgetChangeChecked, bool isBlist = false)
    {
        TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
        UserData userData = await _utility.GetUserDetailsAsync(userId);
        List<TfpStageActionImport> allActions =
            tenantDbContext.TfpStageActionImport.Where(x => x.UserId == userData.pk_id &&
                                                            x.TenantId == userData.tenant_id &&
                                                            x.BudgetYear == budgetYear &&
                                                            x.ChangeId == changeId).ToList();

        foreach (var action in actions)
        {
            TfpStageActionImport dbAction = allActions.FirstOrDefault(x => x.PkId == action.PkId);
            if (dbAction != null)
            {
                tenantDbContext.Entry(dbAction).CurrentValues.SetValues(action);
            }
        }
        await tenantDbContext.SaveChangesAsync();
        await ValidateActions(userId, budgetYear, changeId, budgetChangeChecked, isBlist);
    }



    public async Task<ColumnInfo> GetColumnInfo(string userId, int budgetYear, string pageId = null, bool isImportwithPeriodicKey = false, bool isImportwithPeriodicKeyNextYear = false, bool isbudgetChangeChecked = false)
    {
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValues =
            await _utility.GetLanguageStringsAsync(userDetails.language_preference,
                userDetails.user_name, "ConsequenceAdjustedBudget");
        Dictionary<string, clsLanguageString> langStringValuesFp =
            await _utility.GetLanguageStringsAsync(userDetails.language_preference,
                userDetails.user_name, "FinancialPlan");
        Dictionary<string, clsLanguageString> langStringValuesAi =
            await _utility.GetLanguageStringsAsync(userDetails.language_preference,
                userDetails.user_name, "ActionImport");
        Dictionary<string, clsLanguageString> langStringValuesInvAi =
            await _utility.GetLanguageStringsAsync(userDetails.language_preference,
                userDetails.user_name, "InvestmentsActionImport");
        Dictionary<string, clsLanguageString> langStringsInv =
            await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "invdetails");
        IEnumerable<freedimDefinition> freeDims = await _utility.GetFreeDimDefinitionsBasedOnPageIdAsync(userId, pageId);
        List<freedimDefinition> freeDimColumns = freeDims.ToList();

        List<string> colNames;
        if ((!string.IsNullOrEmpty(pageId) && (pageId == "fpactions" || pageId == "ybactions")))
        {
            if (isImportwithPeriodicKeyNextYear)
            {
                colNames = await GetColumnNames(userId, pageId, isImportwithPeriodicKeyNextYear, isbudgetChangeChecked);
            }
            else
            {
                colNames = await GetColumnNames(userId, pageId, isImportwithPeriodicKey, isbudgetChangeChecked);
            }
        }
        else if ((!string.IsNullOrEmpty(pageId) && pageId == "YB_FINANCING"))
        {
            colNames = await GetColumnNamesForBudgetImport(userId);
        }
        else
        {
            colNames = await GetColumnNamesInvestments(userId, budgetYear);
        }
        ColumnInfo columnInfo = new ColumnInfo();

        foreach (string colName in colNames)
        {
            switch (colName)
            {
                case "ActionId":
                    columnInfo.Fields.Add("ActionId");
                    columnInfo.Titles.Add(
                        langStringValues.FirstOrDefault(v => v.Key == "action_id").Value.LangText);
                    columnInfo.DataTypes.Add("integer");
                    break;

                case "ActionTitle":
                    columnInfo.Fields.Add("ActionTitle");
                    columnInfo.Titles.Add(
                        langStringValues.FirstOrDefault(v => v.Key == "action_text").Value.LangText);
                    columnInfo.DataTypes.Add("string");
                    break;

                case "AccountCode":
                    columnInfo.Fields.Add("AccountCode");
                    if (pageId == "investments")
                    {
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "account_text").Value.LangText + "*");
                    }
                    else
                    {
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "account_text").Value.LangText);
                    }
                    columnInfo.DataTypes.Add("string");
                    break;

                case "DepartmentCode":
                    columnInfo.Fields.Add("DepartmentCode");
                    if (pageId == "investments")
                    {
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "department_text").Value.LangText + "*");
                    }
                    else
                    {
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "department_text").Value.LangText);
                    }
                    columnInfo.DataTypes.Add("string");
                    break;

                case "FunctionCode":
                    columnInfo.Fields.Add("FunctionCode");
                    if (pageId == "investments")
                    {
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "function_text").Value.LangText + "*");
                    }
                    else
                    {
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "function_text").Value.LangText);
                    }
                    columnInfo.DataTypes.Add("string");
                    break;

                case "ProjectCode":
                    columnInfo.Fields.Add("ProjectCode");
                    if (pageId == "investments")
                    {
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "fp_project_text").Value.LangText + "*");
                    }
                    else
                    {
                        columnInfo.Titles.Add(
                            langStringValues.FirstOrDefault(v => v.Key == "fp_project_text").Value.LangText);
                    }
                    columnInfo.DataTypes.Add("string");
                    break;

                case "FreeDim1":
                    columnInfo.Fields.Add("FreeDim1");
                    var freeDim1 = freeDimColumns.FirstOrDefault(y => y.freeDimColumn == "free_dim_1");
                    columnInfo.Titles.Add(freeDim1 != null ? freeDim1.freeDimHeader : "FreeDim1");
                    columnInfo.DataTypes.Add("string");
                    break;

                case "FreeDim2":
                    columnInfo.Fields.Add("FreeDim2");
                    var freeDim2 = freeDimColumns.FirstOrDefault(y => y.freeDimColumn == "free_dim_2");
                    columnInfo.Titles.Add(freeDim2 != null ? freeDim2.freeDimHeader : "FreeDim2");
                    columnInfo.DataTypes.Add("string");
                    break;

                case "FreeDim3":
                    columnInfo.Fields.Add("FreeDim3");
                    var freeDim3 = freeDimColumns.FirstOrDefault(y => y.freeDimColumn == "free_dim_3");
                    columnInfo.Titles.Add(freeDim3 != null ? freeDim3.freeDimHeader : "FreeDim3");
                    columnInfo.DataTypes.Add("string");
                    break;

                case "FreeDim4":
                    columnInfo.Fields.Add("FreeDim4");
                    var freeDim4 = freeDimColumns.FirstOrDefault(y => y.freeDimColumn == "free_dim_4");
                    columnInfo.Titles.Add(freeDim4 != null ? freeDim4.freeDimHeader : "FreeDim4");
                    columnInfo.DataTypes.Add("string");
                    break;

                case "CostEstimate":
                    columnInfo.Fields.Add("CostEstimate");
                    columnInfo.Titles.Add(
                        langStringsInv.FirstOrDefault(v => v.Key == "hdr_cost_estimate").Value.LangText);
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "ApprovedCost":
                    columnInfo.Fields.Add("ApprovedCost");
                    columnInfo.Titles.Add(
                        langStringValuesAi.FirstOrDefault(v => v.Key == "ai_action_approved_cost").Value.LangText);
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "AdjustmentCode":
                    columnInfo.Fields.Add("AdjustmentCode");
                    columnInfo.Titles.Add(
                        langStringValuesFp.FirstOrDefault(v => v.Key == "fp_adjustment_code").Value.LangText);
                    columnInfo.DataTypes.Add("string");
                    break;

                case "AlterCode":
                    columnInfo.Fields.Add("AlterCode");
                    if (pageId == "investments")
                    {
                        columnInfo.Titles.Add(
                            langStringValuesAi.FirstOrDefault(v => v.Key == "ai_alter_code_text").Value.LangText + "*");
                    }
                    else
                    {
                        columnInfo.Titles.Add(
                            langStringValuesAi.FirstOrDefault(v => v.Key == "ai_alter_code_text").Value.LangText);
                    }
                    columnInfo.DataTypes.Add("string");
                    break;

                case "Year1Amount":
                    columnInfo.Fields.Add("Year1Amount");
                    columnInfo.Titles.Add(budgetYear.ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year2Amount":
                    columnInfo.Fields.Add("Year2Amount");
                    columnInfo.Titles.Add((budgetYear + 1).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year3Amount":
                    columnInfo.Fields.Add("Year3Amount");
                    columnInfo.Titles.Add((budgetYear + 2).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year4Amount":
                    columnInfo.Fields.Add("Year4Amount");
                    columnInfo.Titles.Add((budgetYear + 3).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year5Amount":
                    columnInfo.Fields.Add("Year5Amount");
                    columnInfo.Titles.Add((budgetYear + 4).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year6Amount":
                    columnInfo.Fields.Add("Year6Amount");
                    columnInfo.Titles.Add((budgetYear + 5).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year7Amount":
                    columnInfo.Fields.Add("Year7Amount");
                    columnInfo.Titles.Add((budgetYear + 6).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year8Amount":
                    columnInfo.Fields.Add("Year8Amount");
                    columnInfo.Titles.Add((budgetYear + 7).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year9Amount":
                    columnInfo.Fields.Add("Year9Amount");
                    columnInfo.Titles.Add((budgetYear + 8).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year10Amount":
                    columnInfo.Fields.Add("Year10Amount");
                    columnInfo.Titles.Add((budgetYear + 9).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Description":
                    columnInfo.Fields.Add("Description");
                    columnInfo.Titles.Add(
                        langStringValues.FirstOrDefault(v => v.Key == "CAB_description").Value.LangText);
                    columnInfo.DataTypes.Add("string");
                    break;

                case "InvestmentId":
                    columnInfo.Fields.Add("InvestmentId");
                    columnInfo.Titles.Add(
                        langStringValuesInvAi.FirstOrDefault(v => v.Key == "ai_inv_investment_Id").Value.LangText);
                    columnInfo.DataTypes.Add("integer");
                    break;

                case "InvestmentName":
                    columnInfo.Fields.Add("InvestmentName");
                    columnInfo.Titles.Add(
                        langStringValuesInvAi.FirstOrDefault(v => v.Key == "ai_inv_investment_name").Value.LangText);
                    columnInfo.DataTypes.Add("string");
                    break;

                case "StartYear":
                    columnInfo.Fields.Add("StartYear");
                    columnInfo.Titles.Add(
                        langStringValuesInvAi.FirstOrDefault(v => v.Key == "ai_inv_start_year").Value.LangText);
                    columnInfo.DataTypes.Add("string");
                    break;

                case "FinishedDate":
                    columnInfo.Fields.Add("FinishedDate");
                    columnInfo.Titles.Add(
                        langStringValuesInvAi.FirstOrDefault(v => v.Key == "ai_inv_finished_date").Value.LangText);
                    columnInfo.DataTypes.Add("DateTime");
                    break;

                case "ServiceArea":
                    columnInfo.Fields.Add("ServiceArea");
                    columnInfo.Titles.Add(
                        langStringValuesInvAi.FirstOrDefault(v => v.Key == "ai_inv_service_area").Value.LangText);
                    columnInfo.DataTypes.Add("string");
                    break;

                case "PreviousYear1":
                    columnInfo.Fields.Add("PreviousYear1");
                    columnInfo.Titles.Add((budgetYear - 5).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "PreviousYear2":
                    columnInfo.Fields.Add("PreviousYear2");
                    columnInfo.Titles.Add((budgetYear - 4).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "PreviousYear3":
                    columnInfo.Fields.Add("PreviousYear3");
                    columnInfo.Titles.Add((budgetYear - 3).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "PreviousYear4":
                    columnInfo.Fields.Add("PreviousYear4");
                    columnInfo.Titles.Add((budgetYear - 2).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "PreviousYear5":
                    columnInfo.Fields.Add("PreviousYear5");
                    columnInfo.Titles.Add((budgetYear - 1).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year1":
                    columnInfo.Fields.Add("Year1");
                    columnInfo.Titles.Add(budgetYear.ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year2":
                    columnInfo.Fields.Add("Year2");
                    columnInfo.Titles.Add((budgetYear + 1).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year3":
                    columnInfo.Fields.Add("Year3");
                    columnInfo.Titles.Add((budgetYear + 2).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year4":
                    columnInfo.Fields.Add("Year4");
                    columnInfo.Titles.Add((budgetYear + 3).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year5":
                    columnInfo.Fields.Add("Year5");
                    columnInfo.Titles.Add((budgetYear + 4).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year6":
                    //case "Year6Amount":
                    columnInfo.Fields.Add(colName);
                    columnInfo.Titles.Add((budgetYear + 5).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year7":
                    //case "Year7Amount":
                    columnInfo.Fields.Add(colName);
                    columnInfo.Titles.Add((budgetYear + 6).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year8":
                    //case "Year8Amount":
                    columnInfo.Fields.Add(colName);
                    columnInfo.Titles.Add((budgetYear + 7).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year9":
                    //case "Year9Amount":
                    columnInfo.Fields.Add(colName);
                    columnInfo.Titles.Add((budgetYear + 8).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year10":
                    //case "Year10Amount":
                    columnInfo.Fields.Add(colName);
                    columnInfo.Titles.Add((budgetYear + 9).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "ProjectHeader":
                    columnInfo.Fields.Add("ProjectHeader");
                    columnInfo.Titles.Add(
                        langStringValuesInvAi.FirstOrDefault(v => v.Key == "ai_inv_project_header").Value.LangText);
                    columnInfo.DataTypes.Add("string");
                    break;

                case "InvProgram":
                    columnInfo.Fields.Add("InvProgram");
                    columnInfo.Titles.Add(
                        langStringValuesInvAi.FirstOrDefault(v => v.Key == "ai_inv_inv_program").Value.LangText);
                    columnInfo.DataTypes.Add("string");
                    break;

                case "VatRefundPercentage":
                    columnInfo.Fields.Add("VatRefundPercentage");
                    columnInfo.Titles.Add(
                        langStringValuesInvAi.FirstOrDefault(v => v.Key == "ai_inv_vat_ref_per").Value.LangText);
                    columnInfo.DataTypes.Add("percentage");
                    break;

                case "VatRatePercentage":
                    columnInfo.Fields.Add("VatRatePercentage");
                    columnInfo.Titles.Add(
                        langStringValuesInvAi.FirstOrDefault(v => v.Key == "ai_inv_vat_rate_per").Value.LangText);
                    columnInfo.DataTypes.Add("percentage");
                    break;

                case "InvestmentDescription":
                    columnInfo.Fields.Add("InvestmentDescription");
                    columnInfo.Titles.Add(
                        langStringValuesInvAi.FirstOrDefault(v => v.Key == "ai_inv_investment_desc").Value.LangText);
                    columnInfo.DataTypes.Add("string");
                    break;

                case "TotalBudget":
                    columnInfo.Fields.Add("TotalBudget");
                    columnInfo.Titles.Add(langStringValuesAi.FirstOrDefault(v => v.Key == "bi_TotalBudget").Value.LangText);
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Comments":
                    columnInfo.Fields.Add("Comments");
                    columnInfo.Titles.Add(langStringValuesAi.FirstOrDefault(v => v.Key == "bi_Comments").Value.LangText);
                    columnInfo.DataTypes.Add("string");
                    break;

                case "PeriodicKey":
                    columnInfo.Fields.Add("PeriodicKey");
                    columnInfo.Titles.Add(langStringValuesAi.FirstOrDefault(v => v.Key == "bi_PeriodicKey").Value.LangText);
                    columnInfo.DataTypes.Add("string");
                    break;

                case "Year11":
                    columnInfo.Fields.Add("Year11");
                    columnInfo.Titles.Add((budgetYear + 10).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year12":
                    columnInfo.Fields.Add("Year12");
                    columnInfo.Titles.Add((budgetYear + 11).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year13":
                    columnInfo.Fields.Add("Year13");
                    columnInfo.Titles.Add((budgetYear + 12).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year14":
                    columnInfo.Fields.Add("Year14");
                    columnInfo.Titles.Add((budgetYear + 13).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year15":
                    columnInfo.Fields.Add("Year15");
                    columnInfo.Titles.Add((budgetYear + 14).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year16":
                    columnInfo.Fields.Add("Year16");
                    columnInfo.Titles.Add((budgetYear + 15).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year17":
                    columnInfo.Fields.Add("Year17");
                    columnInfo.Titles.Add((budgetYear + 16).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year18":
                    columnInfo.Fields.Add("Year18");
                    columnInfo.Titles.Add((budgetYear + 17).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year19":
                    columnInfo.Fields.Add("Year19");
                    columnInfo.Titles.Add((budgetYear + 18).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Year20":
                    columnInfo.Fields.Add("Year20");
                    columnInfo.Titles.Add((budgetYear + 19).ToString());
                    columnInfo.DataTypes.Add("numeric");
                    break;

                case "Url":
                    columnInfo.Fields.Add("Url");
                    columnInfo.Titles.Add(langStringValuesAi.FirstOrDefault(v => v.Key == "yb_actionimport_url").Value.LangText);
                    columnInfo.DataTypes.Add("string");
                    break;
            }
        }

        return columnInfo;
    }



    private async Task ValidateActions(string userId, int budgetYear, int changeId, bool isbudgetChangeChecked, bool isBlist)
    {
        try
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();

            var spValidate = new PrcValidateImportedActions
            {
                BudgetYear = budgetYear,
                UserId = userDetails.pk_id,
                TenantId = userDetails.tenant_id,
                ChangeId = changeId,
                isBudgetChange = isbudgetChangeChecked,
                isBlist = isBlist
            };

            await tenantDbContext.Database.ExecuteStoredProcedureAsync(spValidate);
            await tenantDbContext.SaveChangesAsync();
            await ValidateURLColumn(userId, budgetYear, changeId, isBlist);
        }
        catch (Exception)
        {
            throw;
        }
    }



    private async Task ValidateURLColumn(string userId, int budgetYear, int changeId, bool isBlist)
    {
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);

        TenantDBContext tenantDBContext = await _utility.GetTenantDBContextAsync();
        List<TfpStageActionImport> stagedactionsData = tenantDBContext.TfpStageActionImport.Where(x => x.UserId == userDetails.pk_id && x.TenantId == userDetails.tenant_id && x.BudgetYear == budgetYear && x.ChangeId == changeId).ToList();
        foreach (var item in stagedactionsData)
        {
            if (!string.IsNullOrEmpty(item.Url) && !string.IsNullOrWhiteSpace(item.Url))
            {
                if (item.ActionId > 0)
                {
                    int actionId = (int)item.ActionId;
                    var actionIdLinkedURLFromExport = stagedactionsData.Where(x => x.ActionId == actionId && !string.IsNullOrEmpty(x.Url) && !string.IsNullOrWhiteSpace(x.Url)).GroupBy(x => new { x.ActionId, x.Url }).Select(x => x.Key.Url).ToList();
                    IEnumerable<UrlObject> actionIdLinkedUrlExisting = await _utility.GetUrlDetailsById(userDetails.tenant_id, actionId.ToString(), isBlist ? UrlType.BListAction : UrlType.FPAction);
                    ActionImportURLHelper actionImportStagedUrlData = ValidateUrlColumnAndSplitURLData(actionIdLinkedURLFromExport);
                    if (actionImportStagedUrlData.IsValidUrl)
                    {
                        if (((MAX_NO_OF_URL_LINKS - actionIdLinkedUrlExisting.Count()) < actionImportStagedUrlData.stagedURLData.Count) && !string.IsNullOrEmpty(item.Url) && !string.IsNullOrWhiteSpace(item.Url))
                        {
                            item.URLError = true;
                            item.ErrorCount = item.ErrorCount + 1;
                        }
                        else
                        {
                            item.URLError = false;
                        }
                    }
                    else
                    {
                        item.URLError = true;
                        item.ErrorCount = item.ErrorCount + 1;
                    }
                }
                else
                {
                    var actionIdLinkedURLFromExport = stagedactionsData.Where(x => x.ActionTitle == item.ActionTitle && !string.IsNullOrEmpty(x.Url) && !string.IsNullOrWhiteSpace(x.Url)).GroupBy(x => new { x.ActionTitle, x.Url }).Select(x => x.Key.Url).ToList();
                    ActionImportURLHelper actionImportStagedUrlData = ValidateUrlColumnAndSplitURLData(actionIdLinkedURLFromExport);
                    if (actionImportStagedUrlData.IsValidUrl)
                    {
                        if (actionImportStagedUrlData.stagedURLData.Count > 5 && !string.IsNullOrEmpty(item.Url) && !string.IsNullOrWhiteSpace(item.Url))
                        {
                            item.URLError = true;
                            item.ErrorCount = item.ErrorCount + 1;
                        }
                        else
                        {
                            item.URLError = false;
                        }
                    }
                    else
                    {
                        item.URLError = true;
                        item.ErrorCount = item.ErrorCount + 1;
                    }
                }
            }
            else
            {
                item.URLError = false;
            }
            item.ErrorCount = ((bool)item.ActionIdError ? 1 : 0) + ((bool)item.ActionTitleError ? 1 : 0) + ((bool)item.AccountCodeError ? 1 : 0) + ((bool)item.DepartmentCodeError ? 1 : 0) + ((bool)item.FunctionCodeError ? 1 : 0) + ((bool)item.ProjectCodeError ? 1 : 0) + ((bool)item.FreeDim1Error ? 1 : 0) + ((bool)item.FreeDim2Error ? 1 : 0) + ((bool)item.FreeDim3Error ? 1 : 0) + ((bool)item.FreeDim4Error ? 1 : 0) + ((bool)item.AdjustmentCodeError ? 1 : 0) + ((bool)item.AlterCodeError ? 1 : 0) + ((bool)item.PeriodicKeyError ? 1 : 0) +
                              (ReturnValidErrorStatusForAmount(item.Year1AmountError) ? 1 : 0) +
                              (ReturnValidErrorStatusForAmount(item.Year2AmountError) ? 1 : 0) +
                              (ReturnValidErrorStatusForAmount(item.Year3AmountError) ? 1 : 0) +
                              (ReturnValidErrorStatusForAmount(item.Year4AmountError) ? 1 : 0) +
                              (ReturnValidErrorStatusForAmount(item.Year5AmountError) ? 1 : 0) + ((bool)item.URLError ? 1 : 0);
        }
        await tenantDBContext.SaveChangesAsync();
    }



    private static bool ReturnValidErrorStatusForAmount(bool? amount)
    {
        if (amount.HasValue) return (bool)amount;
        return false;
    }



    private static ActionImportURLHelper ValidateUrlColumnAndSplitURLData(List<string> actionIdLinkedURLFromExport)
    {
        ActionImportURLHelper actionImportURLData = new ActionImportURLHelper();
        foreach (var item in actionIdLinkedURLFromExport)
        {
            List<string> urlSplittedData = item.TrimEnd(';').Split(';').ToList();
            foreach (var url in urlSplittedData)
            {
                if (Regex.IsMatch(url, "((http|https)://)(www.)?[a-zA-Z0-9@:%._\\+~#?&//=]{2,256}\\.[a-z]{2,6}\\b([-a-zA-Z0-9@:%._\\+~#?&//=]*)"))
                {
                    actionImportURLData.IsValidUrl = true;
                    actionImportURLData.stagedURLData.Add(url);
                }
                else
                {
                    actionImportURLData.IsValidUrl = false;
                    return actionImportURLData;
                }
            }
        }
        return actionImportURLData;
    }



    public void UpdateActionType(string userId, int budgetYear, int changeId, int actionType)
    {
        UserData userDetails = _utility.GetUserDetails(userId);
        TenantDBContext tenantDbContext = _utility.GetTenantDBContext();

        var spUpdateActionType = new PrcUpdateActionTypeForActionImport
        {
            BudgetYear = budgetYear,
            UserId = userDetails.pk_id,
            TenantId = userDetails.tenant_id,
            ChangeId = changeId,
            ActionType = actionType
        };

        tenantDbContext.Database.ExecuteStoredProcedure(spUpdateActionType);
    }



    public async Task<ImportInfo> GetImportInfo(string userId, int budgetYear, int changeId)
    {
        TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
        UserData userData = await _utility.GetUserDetailsAsync(userId);
        ImportInfo info = new ImportInfo
        {
            FailedCount = await tenantDbContext.TfpStageActionImport.CountAsync(x => x.UserId == userData.pk_id &&
                x.TenantId == userData.tenant_id &&
                x.BudgetYear == budgetYear &&
                x.ChangeId == changeId &&
                x.ErrorCount > 0),
            ActionCount = await tenantDbContext.TfpStageActionImport.Where(x => x.UserId == userData.pk_id &&
                    x.TenantId == userData.tenant_id &&
                    x.BudgetYear == budgetYear &&
                    x.ChangeId == changeId)
                .Select(y => y.ActionTitle)
                .Distinct()
                .CountAsync(),
            TotalRows = await tenantDbContext.TfpStageActionImport.CountAsync(x => x.UserId == userData.pk_id &&
                x.TenantId == userData.tenant_id &&
                x.BudgetYear == budgetYear &&
                x.ChangeId == changeId),
            Sum = 0,
            EnableImportWithoutAutoClose = (await _utility.GetParameterValueAsync(userId, "ENABLE_IMPORT_WITHOUT_AUTO_CLOSE")).ToLower() == "true",
        };

        var result = await tenantDbContext.TfpStageActionImport.Where(x => x.UserId == userData.pk_id &&
                                                                           x.TenantId == userData.tenant_id &&
                                                                           x.BudgetYear == budgetYear &&
                                                                           x.ChangeId == changeId).GroupBy(x => x.UserId)
            .Select(g => new
            {
                TotalYear1 = g.Sum(i => i.Year1Amount)
            }).ToListAsync();
        if (result.Any())
        {
            info.Sum = result[0].TotalYear1 ?? 0;
        }

        return info;
    }



    public IEnumerable<KeyValue> GetActionTypes(string userId)
    {
        TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
        UserData userData = _utility.GetUserDetails(userId);
        List<int> excludedActionTypes = new List<int> { 30, 40 };

        List<KeyValue> actionTypes = (from a in tenantDbContext.gmd_action_types
            where a.pk_language == userData.language_preference &&
                  excludedActionTypes.Contains(a.pk_action_type) == false
            select new KeyValue
            {
                Key = a.pk_action_type.ToString(),
                Value = (a.pk_action_type + "-" + a.action_type_descr)
            }).ToList();
        return actionTypes;
    }



    public async Task<IEnumerable<KeyValue>> GetValueFactors(string userId)
    {
        UserData userData = await _utility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> actionImportStrings =
            await _utility.GetLanguageStringsAsync(userData.language_preference,
                userData.user_name, "ActionImport");

        List<KeyValue> valueFactors = new List<KeyValue>();

        KeyValue valueFactor = new KeyValue()
        {
            Key = "1",
            Value = actionImportStrings.FirstOrDefault(v => v.Key == "ai_vf_actual").Value.LangText
        };
        valueFactors.Add(valueFactor);

        valueFactor = new KeyValue()
        {
            Key = "1000",
            Value = actionImportStrings.FirstOrDefault(v => v.Key == "ai_vf_thousand").Value.LangText
        };
        valueFactors.Add(valueFactor);

        valueFactor = new KeyValue()
        {
            Key = "1000000",
            Value = actionImportStrings.FirstOrDefault(v => v.Key == "ai_vf_million").Value.LangText
        };
        valueFactors.Add(valueFactor);

        return valueFactors;
    }



    public IEnumerable<KeyValue> GetAdjustments()
    {
        List<KeyValue> adjustments = new List<KeyValue>();
        for (int i = 0; i < 150; i = i + 10)
        {
            KeyValue adjustment = new KeyValue()
            {
                Key = i.ToString(),
                Value = i + " %"
            };
            adjustments.Add(adjustment);
        }

        return adjustments;
    }



    public async Task DeleteStaged(string userId, int budgetYear, int changeId, string pageId)
    {
        TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
        UserData userData = await _utility.GetUserDetailsAsync(userId);
        if ((!string.IsNullOrEmpty(pageId) && (pageId == "fpactions" || pageId == "ybactions")))
        {
            tenantDbContext.TfpStageActionImport.RemoveRange(
                tenantDbContext.TfpStageActionImport.Where(x => x.UserId == userData.pk_id && x.TenantId == userData.tenant_id &&
                                                                x.BudgetYear == budgetYear && x.ChangeId == changeId));
            await tenantDbContext.SaveChangesAsync();
            await _utility.DeleteCompletedJobsAsync(userId, UserTrackedJobs.ActionImport);
        }
        else if (pageId == "investments")
        {
            if (budgetYear < 2020)
            {
                tenantDbContext.TfpStageInvestmentActionImport.RemoveRange(
                    tenantDbContext.TfpStageInvestmentActionImport.Where(x => x.UserId == userData.pk_id && x.TenantId == userData.tenant_id &&
                                                                              x.BudgetYear == budgetYear && x.ChangeId == changeId
                    ));
                await tenantDbContext.SaveChangesAsync();
                await _utility.DeleteCompletedJobsAsync(userId, UserTrackedJobs.InvestmentsImport);
            }
            else
            {
                tenantDbContext.tfp_stage_investment_Project_import.RemoveRange(
                    tenantDbContext.tfp_stage_investment_Project_import.Where(x => x.UserId == userData.pk_id &&
                        x.TenantId == userData.tenant_id &&
                        x.BudgetYear == budgetYear && x.ChangeId == changeId));
                await tenantDbContext.SaveChangesAsync();
                await _utility.DeleteCompletedJobsAsync(userId, UserTrackedJobs.InvestmentsImport);
            }
        }
    }



    public async Task<bool> GetShowBudgetChangeControlsStatus(string userId, int budgetYear)
    {
        TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        vw_tco_parameters finplanEstablished = await tenantDbContext.vw_tco_parameters.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id
            && x.param_name == "FINPLAN_ESTABLISHED"
            && x.param_value == (budgetYear + 1).ToString() && x.active == 1);
        return (finplanEstablished != null);
    }



    public async Task<bool> GetShowYearlyBudgetControlsStatus(string userId, int budgetYear)
    {
        TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        vw_tco_parameters updateYB = await tenantDbContext.vw_tco_parameters.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id
            && x.param_name == "LOCK_ORIGINAL_BUDGET"
            && x.param_value == budgetYear.ToString() && x.active == 1);
        return (updateYB != null);
    }

}