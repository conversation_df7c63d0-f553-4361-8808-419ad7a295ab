using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;

namespace Framsikt.BL;

public partial class BudgetFormBL
{
    public async Task<JObject> Get1ADataAsync(int budgetYear, string userId, int docType, int forecastPeriod = 0, bool onlyData=false)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValues = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetForm");

        dynamic header = new JObject();
        header.title = langStringValues["BudForm_1A_title"].LangText;
        header.descriptiontip = langStringValues["BudForm_1A_desc"].LangText;
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        var commentList = await GetCommentListAsync(budgetYear, userDetails, dbContext, clsConstants.budget_form_type.BudgetForm1A.ToString(), docType, forecastPeriod);
        var data1B = (from a in (await Get1BDataFromDBAsync(budgetYear, userId, docType, forecastPeriod))
            group a by new { aggregate_id = a.aggregate_id.Trim(), aggregate_name = a.aggregate_name.Trim() } into grp
            select new clsBudgetFormHelper()
            {
                LineGroupId = grp.Key.aggregate_id,
                LineGroup = grp.Key.aggregate_name,
                id = grp.Key.aggregate_id,
                budgetFormDesc = grp.Key.aggregate_name,
                actualAmtYear = grp.Sum(x => x.actual_amt_year),
                revisedBudAmtYear = grp.Sum(x => x.revised_bud_amt_year),
                orgBudAmtYear = grp.Sum(x => x.org_bud_amt_year),
                actualAmtLastYear = grp.Sum(x => x.actual_amt_last_year),
                orgBudAmtLastYear = grp.Sum(x => x.org_bud_amt_last_year),
                revisedAmtLastYear = grp.Sum(x => x.revised_bud_amt_last_year),
                finPlanYear1 = grp.Sum(x => x.finplan_year_1_amount),
                finPlanYear2 = grp.Sum(x => x.finplan_year_2_amount),
                finPlanYear3 = grp.Sum(x => x.finplan_year_3_amount),
                finPlanYear4 = grp.Sum(x => x.finplan_year_4_amount),
                numberFormat = "n0",
                semi = false,
                clickable = true,
                isSubHeading = false,
            }).ToList().OrderBy(x => x.id).ThenBy(x => x.budgetFormDesc);

        JArray dataArray = new JArray();
        dynamic finalData = new JObject();
        var data = (from a in (await Get1ADataFromDBAsync(budgetYear, userId, docType, forecastPeriod))
            group a by new { line_group_id = a.line_group_id.ToString().Trim(), line_group = a.line_group.Trim(), line_item_id = a.line_item_id.ToString().Trim(), line_item = a.line_item.Trim() } into grp
            select new clsBudgetFormHelper()
            {
                LineGroupId = grp.Key.line_group_id.ToString(),
                LineGroup = grp.Key.line_group,
                id = grp.Key.line_item_id.ToString(),
                budgetFormDesc = grp.Key.line_item,
                actualAmtYear = grp.Sum(x => x.actual_amt_year),
                revisedBudAmtYear = grp.Sum(x => x.revised_bud_amt_year),
                orgBudAmtYear = grp.Sum(x => x.org_bud_amt_year),
                actualAmtLastYear = grp.Sum(x => x.actual_amt_last_year),
                orgBudAmtLastYear = grp.Sum(x => x.org_bud_amt_last_year),
                revisedAmtLastYear = grp.Sum(x => x.revised_bud_amt_last_year),
                finPlanYear1 = grp.Sum(x => x.finplan_year_1_amount),
                finPlanYear2 = grp.Sum(x => x.finplan_year_2_amount),
                finPlanYear3 = grp.Sum(x => x.finplan_year_3_amount),
                finPlanYear4 = grp.Sum(x => x.finplan_year_4_amount),
                isSubHeading = false,
                numberFormat = "n0"
            }).ToList().OrderBy(x => x.LineGroupId).ThenBy(x => x.LineGroup).ThenBy(x => x.id).ThenBy(x => x.budgetFormDesc);

        var LineGroupIdList = new List<string>();
        LineGroupIdList = data.Select(x => x.LineGroupId).Distinct().ToList();

        dynamic grpData;
        var LineGroupIdListCount = LineGroupIdList.Count();
        var count = 0;
        foreach (var item in LineGroupIdList)
        {
            count++;
            grpData = new JObject();
            grpData.LineGroupId = item;
            grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
            grpData.id = null;
            grpData.budgetFormDesc = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
            if (onlyData)
            {
                grpData.actualAmtYear = 0;
                grpData.revisedBudAmtYear = 0;
                grpData.orgBudAmtYear = 0;
                grpData.actualAmtLastYear = 0;
                grpData.orgBudAmtLastYear = 0;
                grpData.revisedAmtLastYear = 0;
                grpData.finPlanYear1 = 0;
                grpData.finPlanYear2 = 0;
                grpData.finPlanYear3 = 0;
                grpData.finPlanYear4 = 0;
            }
            else
            {
                grpData.actualAmtYear = "";
                grpData.revisedBudAmtYear = "";
                grpData.orgBudAmtYear = "";
                grpData.actualAmtLastYear = "";
                grpData.orgBudAmtLastYear = "";
                grpData.revisedAmtLastYear = "";
                grpData.finPlanYear1 = "";
                grpData.finPlanYear2 = "";
                grpData.finPlanYear3 = "";
                grpData.finPlanYear4 = "";
            }
            grpData.semi = true;
            grpData.clickable = false;
            grpData.numberFormat = "Text";
            grpData.isSubHeading = true;
            dataArray.Add(grpData);

            foreach (var d in data.Where(x => x.LineGroupId == item).ToList())
            {
                grpData = new JObject();
                grpData.LineGroupId = item;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = d.id;
                grpData.budgetFormDesc = d.budgetFormDesc;
                grpData.actualAmtYear = d.actualAmtYear;
                grpData.revisedBudAmtYear = d.revisedBudAmtYear;
                grpData.orgBudAmtYear = d.orgBudAmtYear;
                grpData.actualAmtLastYear = d.actualAmtLastYear;
                grpData.orgBudAmtLastYear = d.orgBudAmtLastYear;
                grpData.revisedAmtLastYear = d.revisedAmtLastYear;
                grpData.finPlanYear1 = d.finPlanYear1;
                grpData.finPlanYear2 = d.finPlanYear2;
                grpData.finPlanYear3 = d.finPlanYear3;
                grpData.finPlanYear4 = d.finPlanYear4;
                grpData.semi = false;
                grpData.clickable = true;
                grpData.isSubHeading = false;
                grpData.numberFormat = d.numberFormat;
                grpData.note = commentList.FirstOrDefault(x => x.line_group_id.ToString() == item && x.line_item_id == d.id) != null ? commentList.FirstOrDefault(x => x.line_group_id.ToString() == item && x.line_item_id == d.id).comment : string.Empty;
                dataArray.Add(grpData);
            }
            if (LineGroupIdListCount == count)
            {
                //sum row 1
                grpData = new JObject();
                grpData.LineGroupId = item;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = -1;
                grpData.budgetFormDesc = langStringValues["BudForm_sum"].LangText + " " + "Til fordeling drift";
                grpData.actualAmtYear = data.Sum(x => x.actualAmtYear);
                grpData.revisedBudAmtYear = data.Sum(x => x.revisedBudAmtYear);
                grpData.orgBudAmtYear = data.Sum(x => x.orgBudAmtYear);
                grpData.actualAmtLastYear = data.Sum(x => x.actualAmtLastYear);
                grpData.orgBudAmtLastYear = data.Sum(x => x.orgBudAmtLastYear);
                grpData.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear);
                grpData.finPlanYear1 = data.Sum(x => x.finPlanYear1);
                grpData.finPlanYear2 = data.Sum(x => x.finPlanYear2);
                grpData.finPlanYear3 = data.Sum(x => x.finPlanYear3);
                grpData.finPlanYear4 = data.Sum(x => x.finPlanYear4);
                grpData.semi = true;
                grpData.clickable = false;
                grpData.isSubHeading = false;
                grpData.numberFormat = "n0";
                dataArray.Add(grpData);

                //sum row 2
                grpData = new JObject();
                grpData.LineGroupId = item;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = -1;
                grpData.budgetFormDesc = langStringValues["BudForm_sum"].LangText + " " + "fordelt til drift (fra skjema 1B)";
                grpData.actualAmtYear = data1B.Sum(x => x.actualAmtYear);
                grpData.revisedBudAmtYear = data1B.Sum(x => x.revisedBudAmtYear);
                grpData.orgBudAmtYear = data1B.Sum(x => x.orgBudAmtYear);
                grpData.actualAmtLastYear = data1B.Sum(x => x.actualAmtLastYear);
                grpData.orgBudAmtLastYear = data1B.Sum(x => x.orgBudAmtLastYear);
                grpData.revisedAmtLastYear = data1B.Sum(x => x.revisedAmtLastYear);
                grpData.finPlanYear1 = data1B.Sum(x => x.finPlanYear1);
                grpData.finPlanYear2 = data1B.Sum(x => x.finPlanYear2);
                grpData.finPlanYear3 = data1B.Sum(x => x.finPlanYear3);
                grpData.finPlanYear4 = data1B.Sum(x => x.finPlanYear4);
                grpData.semi = true;
                grpData.clickable = false;
                grpData.isSubHeading = false;
                grpData.numberFormat = "n0";
                dataArray.Add(grpData);

                //sum total
                grpData = new JObject();
                grpData.LineGroupId = item;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = -1;
                grpData.budgetFormDesc = langStringValues["BudForm_sum"].LangText + " " + langStringValues["BudForm_Sum_1A"].LangText;
                grpData.actualAmtYear = data.Sum(x => x.actualAmtYear) + data1B.Sum(x => x.actualAmtYear);
                grpData.revisedBudAmtYear = data.Sum(x => x.revisedBudAmtYear) + data1B.Sum(x => x.revisedBudAmtYear);
                grpData.orgBudAmtYear = data.Sum(x => x.orgBudAmtYear) + data1B.Sum(x => x.orgBudAmtYear);
                grpData.actualAmtLastYear = data.Sum(x => x.actualAmtLastYear) + data1B.Sum(x => x.actualAmtLastYear);
                grpData.orgBudAmtLastYear = data.Sum(x => x.orgBudAmtLastYear) + data1B.Sum(x => x.orgBudAmtLastYear);
                grpData.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear) + data1B.Sum(x => x.revisedAmtLastYear);
                grpData.finPlanYear1 = data.Sum(x => x.finPlanYear1) + data1B.Sum(x => x.finPlanYear1);
                grpData.finPlanYear2 = data.Sum(x => x.finPlanYear2) + data1B.Sum(x => x.finPlanYear2);
                grpData.finPlanYear3 = data.Sum(x => x.finPlanYear3) + data1B.Sum(x => x.finPlanYear3);
                grpData.finPlanYear4 = data.Sum(x => x.finPlanYear4) + data1B.Sum(x => x.finPlanYear4);
                grpData.semi = true;
                grpData.clickable = false;
                grpData.isSubheading = false;
                grpData.numberFormat = "n0";
                dataArray.Add(grpData);
            }
            else
            {
                //sum row
                grpData = new JObject();
                grpData.LineGroupId = -1;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = -1;
                grpData.budgetFormDesc = langStringValues["BudForm_sum"].LangText + " " + data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.actualAmtYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.actualAmtYear);
                grpData.revisedBudAmtYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.revisedBudAmtYear);
                grpData.orgBudAmtYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.orgBudAmtYear);
                grpData.actualAmtLastYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.actualAmtLastYear);
                grpData.orgBudAmtLastYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.orgBudAmtLastYear);
                grpData.revisedAmtLastYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.revisedAmtLastYear);
                grpData.finPlanYear1 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear1);
                grpData.finPlanYear2 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear2);
                grpData.finPlanYear3 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear3);
                grpData.finPlanYear4 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear4);
                grpData.semi = true;
                grpData.clickable = false;
                grpData.isSubheading = false;
                grpData.numberFormat = "n0";
                dataArray.Add(grpData);
            }

            //empty row
            if (!onlyData)
            {
                grpData = new JObject();
                grpData.LineGroupId = null;
                grpData.LineGroup = null;
                grpData.id = null;
                grpData.budgetFormDesc = "";
                grpData.actualAmtYear = "";
                grpData.revisedBudAmtYear = "";
                grpData.orgBudAmtYear = "";
                grpData.actualAmtLastYear = "";
                grpData.orgBudAmtLastYear = "";
                grpData.revisedAmtLastYear = "";
                grpData.finPlanYear1 = "";
                grpData.finPlanYear2 = "";
                grpData.finPlanYear3 = "";
                grpData.finPlanYear4 = "";
                grpData.semi = true;
                grpData.clickable = false;
                grpData.isSubheading = false;
                grpData.numberFormat = "Text";
                dataArray.Add(grpData);
            }
        }

        finalData.data = dataArray;
        if (!onlyData)
        {
            finalData.columns = await GetBudgetFormColumnAsync(budgetYear, userId, "1A", docType, forecastPeriod);
            finalData.header = header;
        }
        return finalData;
    }

    public async Task<JObject> Get1BDataAsync(int budgetYear, string userId, int docType, int forecastPeriod = 0, bool onlyData = false)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValues = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetForm");

        dynamic header = new JObject();
        header.title = langStringValues["BudForm_1B_title"].LangText;
        header.descriptiontip = langStringValues["BudForm_1B_desc"].LangText;
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        var commentList = await GetCommentListAsync(budgetYear, userDetails, dbContext, clsConstants.budget_form_type.BudgetForm1B.ToString(), docType, forecastPeriod);
        JArray dataArray = new JArray();
        dynamic finalData = new JObject();
        List<clsBudgetFormHelper> dataList = new List<clsBudgetFormHelper>();
        var data = (from a in (await Get1BDataFromDBAsync(budgetYear, userId, docType, forecastPeriod))
            group a by new { aggregate_id = a.aggregate_id.Trim(), aggregate_name = a.aggregate_name.Trim() } into grp
            select new clsBudgetFormHelper()
            {
                LineGroupId = grp.Key.aggregate_id,
                LineGroup = grp.Key.aggregate_name,
                id = grp.Key.aggregate_id,
                budgetFormDesc = grp.Key.aggregate_name,
                actualAmtYear = grp.Sum(x => x.actual_amt_year),
                revisedBudAmtYear = grp.Sum(x => x.revised_bud_amt_year),
                orgBudAmtYear = grp.Sum(x => x.org_bud_amt_year),
                actualAmtLastYear = grp.Sum(x => x.actual_amt_last_year),
                orgBudAmtLastYear = grp.Sum(x => x.org_bud_amt_last_year),
                revisedAmtLastYear = grp.Sum(x => x.revised_bud_amt_last_year),
                finPlanYear1 = grp.Sum(x => x.finplan_year_1_amount),
                finPlanYear2 = grp.Sum(x => x.finplan_year_2_amount),
                finPlanYear3 = grp.Sum(x => x.finplan_year_3_amount),
                finPlanYear4 = grp.Sum(x => x.finplan_year_4_amount),
                numberFormat = "n0",
                semi = false,
                clickable = true,
                isSubHeading = false,
                gridValue = budget_form_Grid_type.BudgetForm1BGrid1.ToString()
            }).ToList().OrderBy(x => x.id).ThenBy(x => x.budgetFormDesc);

        foreach (var item in data)
        {
            item.note = commentList.FirstOrDefault(x => x.line_group_id.ToString() == item.LineGroupId && x.line_item_id == item.id) != null ? commentList.FirstOrDefault(x => x.line_group_id.ToString() == item.LineGroupId && x.line_item_id == item.id).comment : string.Empty;
        }

        dataList.AddRange(data);
        clsBudgetFormHelper sumRow = new clsBudgetFormHelper()
        {
            actualAmtLastYear = data.Sum(x => x.actualAmtLastYear),
            orgBudAmtYear = data.Sum(x => x.orgBudAmtYear),
            revisedBudAmtYear = data.Sum(x => x.revisedBudAmtYear),
            actualAmtYear = data.Sum(x => x.actualAmtYear),
            orgBudAmtLastYear = data.Sum(x => x.orgBudAmtLastYear),
            revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear),
            finPlanYear1 = data.Sum(x => x.finPlanYear1),
            finPlanYear2 = data.Sum(x => x.finPlanYear2),
            finPlanYear3 = data.Sum(x => x.finPlanYear3),
            finPlanYear4 = data.Sum(x => x.finPlanYear4),
            budgetFormDesc = langStringValues["BudForm_Sum_1B"].LangText,
            id = "-1",
            LineGroup = null,
            LineGroupId = "-1",
            numberFormat = "n0",
            semi = true,
            clickable = false,
            isSubHeading = false,
        };
        dataList.Add(sumRow);
        if(!onlyData)
        {
            finalData.columns = await GetBudgetFormColumnAsync(budgetYear, userId, "1B", docType, forecastPeriod);
            finalData.header = header;
        }
        finalData.data = JArray.FromObject(dataList);
        return finalData;
    }

    public async Task<JObject> Get2ADataAsync(int budgetYear, string userId, int docType, int forecastPeriod = 0, bool onlyData = false)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValues = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetForm");

        dynamic header = new JObject();
        header.title = langStringValues["BudForm_2A_title"].LangText;
        header.descriptiontip = langStringValues["BudForm_2A_desc"].LangText;
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        var commentList = await GetCommentListAsync(budgetYear, userDetails, dbContext, clsConstants.budget_form_type.BudgetForm2A.ToString(), docType, forecastPeriod);

        var data2B = (from a in (await Get2BDataFromDBAsync(budgetYear, userId, docType, forecastPeriod))
            group a by new { aggregate_id = a.aggregate_id.Trim(), aggregate_name = a.aggregate_name.Trim() } into grp
            select new clsBudgetFormHelper()
            {
                LineGroupId = grp.Key.aggregate_id,
                LineGroup = grp.Key.aggregate_name,
                id = grp.Key.aggregate_id,
                budgetFormDesc = grp.Key.aggregate_name,
                actualAmtYear = grp.Sum(x => x.actual_amt_year),
                revisedBudAmtYear = grp.Sum(x => x.revised_bud_amt_year),
                orgBudAmtYear = grp.Sum(x => x.org_bud_amt_year),
                actualAmtLastYear = grp.Sum(x => x.actual_amt_last_year),
                orgBudAmtLastYear = grp.Sum(x => x.org_bud_amt_last_year),
                revisedAmtLastYear = grp.Sum(x => x.revised_bud_amt_last_year),
                finPlanYear1 = grp.Sum(x => x.finplan_year_1_amount),
                finPlanYear2 = grp.Sum(x => x.finplan_year_2_amount),
                finPlanYear3 = grp.Sum(x => x.finplan_year_3_amount),
                finPlanYear4 = grp.Sum(x => x.finplan_year_4_amount),
                numberFormat = "n0",
                semi = false,
                clickable = true,
                isSubHeading = false
            }).ToList().OrderBy(x => x.id).ThenBy(x => x.budgetFormDesc);

        JArray dataArray = new JArray();
        dynamic finalData = new JObject();

        var data = (from a in (await Get2ADataFromDBAsync(budgetYear, userId, docType, forecastPeriod))
            group a by new { line_group_id = a.line_group_id.ToString().Trim(), line_group = a.line_group.Trim(), line_item_id = a.line_item_id.ToString().Trim(), line_item = a.line_item.Trim() } into grp
            select new clsBudgetFormHelper()
            {
                LineGroupId = grp.Key.line_group_id.ToString(),
                LineGroup = grp.Key.line_group,
                id = grp.Key.line_item_id.ToString(),
                budgetFormDesc = grp.Key.line_item,
                actualAmtYear = grp.Sum(x => x.actual_amt_year),
                revisedBudAmtYear = grp.Sum(x => x.revised_bud_amt_year),
                orgBudAmtYear = grp.Sum(x => x.org_bud_amt_year),
                actualAmtLastYear = grp.Sum(x => x.actual_amt_last_year),
                orgBudAmtLastYear = grp.Sum(x => x.org_bud_amt_last_year),
                revisedAmtLastYear = grp.Sum(x => x.revised_bud_amt_last_year),
                finPlanYear1 = grp.Sum(x => x.finplan_year_1_amount),
                finPlanYear2 = grp.Sum(x => x.finplan_year_2_amount),
                finPlanYear3 = grp.Sum(x => x.finplan_year_3_amount),
                finPlanYear4 = grp.Sum(x => x.finplan_year_4_amount),
                numberFormat = "n0",
                isSubHeading = false
            }).ToList().OrderBy(x => x.LineGroupId).ThenBy(x => x.LineGroup).ThenBy(x => x.id).ThenBy(x => x.budgetFormDesc);

        var LineGroupIdList = new List<string>();
        LineGroupIdList = data.Select(x => x.LineGroupId).Distinct().ToList();
        dynamic grpData;
        foreach (var item in LineGroupIdList)
        {
            grpData = new JObject();
            grpData.LineGroupId = item;
            grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
            grpData.id = null;
            grpData.budgetFormDesc = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
            if (onlyData)
            {
                grpData.actualAmtYear = 0;
                grpData.revisedBudAmtYear = 0;
                grpData.orgBudAmtYear = 0;
                grpData.actualAmtLastYear = 0;
                grpData.orgBudAmtLastYear = 0;
                grpData.revisedAmtLastYear = 0;
                grpData.finPlanYear1 = 0;
                grpData.finPlanYear2 = 0;
                grpData.finPlanYear3 = 0;
                grpData.finPlanYear4 = 0;
            }
            else
            {
                grpData.actualAmtYear = "";
                grpData.revisedBudAmtYear = "";
                grpData.orgBudAmtYear = "";
                grpData.actualAmtLastYear = "";
                grpData.orgBudAmtLastYear = "";
                grpData.revisedAmtLastYear = "";
                grpData.finPlanYear1 = "";
                grpData.finPlanYear2 = "";
                grpData.finPlanYear3 = "";
                grpData.finPlanYear4 = "";
            }
            grpData.semi = true;
            grpData.clickable = false;
            grpData.isSubHeading = true;
            grpData.numberFormat = "Text";
            grpData.note = null;
            dataArray.Add(grpData);

            foreach (var d in data.Where(x => x.LineGroupId == item).ToList())
            {
                grpData = new JObject();
                grpData.LineGroupId = item;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = d.id;
                grpData.budgetFormDesc = d.budgetFormDesc;
                grpData.actualAmtYear = d.actualAmtYear;
                grpData.revisedBudAmtYear = d.revisedBudAmtYear;
                grpData.orgBudAmtYear = d.orgBudAmtYear;
                grpData.actualAmtLastYear = d.actualAmtLastYear;
                grpData.orgBudAmtLastYear = d.orgBudAmtLastYear;
                grpData.revisedAmtLastYear = d.revisedAmtLastYear;
                grpData.finPlanYear1 = d.finPlanYear1;
                grpData.finPlanYear2 = d.finPlanYear2;
                grpData.finPlanYear3 = d.finPlanYear3;
                grpData.finPlanYear4 = d.finPlanYear4;
                grpData.semi = false;
                grpData.clickable = true;
                grpData.isSubHeading = false;
                grpData.numberFormat = d.numberFormat;
                grpData.note = commentList.FirstOrDefault(x => x.line_group_id.ToString() == item && x.line_item_id == d.id) != null ? commentList.FirstOrDefault(x => x.line_group_id.ToString() == item && x.line_item_id == d.id).comment : string.Empty;
                dataArray.Add(grpData);
            }
            if (item == "4000000")
            {
                //sum row 1
                grpData = new JObject();
                grpData.LineGroupId = item;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = -1;
                grpData.budgetFormDesc = langStringValues["BudForm_sum"].LangText + " " + "Til fordeling drift";
                grpData.actualAmtYear = data.Sum(x => x.actualAmtYear);
                grpData.revisedBudAmtYear = data.Sum(x => x.revisedBudAmtYear);
                grpData.orgBudAmtYear = data.Sum(x => x.orgBudAmtYear);
                grpData.actualAmtLastYear = data.Sum(x => x.actualAmtLastYear);
                grpData.orgBudAmtLastYear = data.Sum(x => x.orgBudAmtLastYear);
                grpData.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear);
                grpData.finPlanYear1 = data.Sum(x => x.finPlanYear1);
                grpData.finPlanYear2 = data.Sum(x => x.finPlanYear2);
                grpData.finPlanYear3 = data.Sum(x => x.finPlanYear3);
                grpData.finPlanYear4 = data.Sum(x => x.finPlanYear4);
                grpData.semi = true;
                grpData.clickable = false;
                grpData.isSubHeading = false;
                grpData.numberFormat = "n0";
                grpData.note = null;
                dataArray.Add(grpData);

                //sum row 2b
                grpData = new JObject();
                grpData.LineGroupId = item;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = -1;
                grpData.budgetFormDesc = langStringValues["BudForm_sum"].LangText + " " + "fordelt til drift (fra skjema 2B)";
                grpData.actualAmtYear = data2B.Sum(x => x.actualAmtYear);
                grpData.revisedBudAmtYear = data2B.Sum(x => x.revisedBudAmtYear);
                grpData.orgBudAmtYear = data2B.Sum(x => x.orgBudAmtYear);
                grpData.actualAmtLastYear = data2B.Sum(x => x.actualAmtLastYear);
                grpData.orgBudAmtLastYear = data2B.Sum(x => x.orgBudAmtLastYear);
                grpData.revisedAmtLastYear = data2B.Sum(x => x.revisedAmtLastYear);
                grpData.finPlanYear1 = data2B.Sum(x => x.finPlanYear1);
                grpData.finPlanYear2 = data2B.Sum(x => x.finPlanYear2);
                grpData.finPlanYear3 = data2B.Sum(x => x.finPlanYear3);
                grpData.finPlanYear4 = data2B.Sum(x => x.finPlanYear4);
                grpData.semi = true;
                grpData.clickable = false;
                grpData.isSubHeading = false;
                grpData.numberFormat = "n0";
                grpData.note = null;
                dataArray.Add(grpData);

                //sum total
                grpData = new JObject();
                grpData.LineGroupId = item;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = -1;
                grpData.budgetFormDesc = langStringValues["BudForm_sum"].LangText + " " + data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.actualAmtYear = data.Sum(x => x.actualAmtYear) + data2B.Sum(x => x.actualAmtYear);
                grpData.revisedBudAmtYear = data.Sum(x => x.revisedBudAmtYear) + data2B.Sum(x => x.revisedBudAmtYear);
                grpData.orgBudAmtYear = data.Sum(x => x.orgBudAmtYear) + data2B.Sum(x => x.orgBudAmtYear);
                grpData.actualAmtLastYear = data.Sum(x => x.actualAmtLastYear) + data2B.Sum(x => x.actualAmtLastYear);
                grpData.orgBudAmtLastYear = data.Sum(x => x.orgBudAmtLastYear) + data2B.Sum(x => x.orgBudAmtLastYear);
                grpData.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear) + data2B.Sum(x => x.revisedAmtLastYear);
                grpData.finPlanYear1 = data.Sum(x => x.finPlanYear1) + data2B.Sum(x => x.finPlanYear1);
                grpData.finPlanYear2 = data.Sum(x => x.finPlanYear2) + data2B.Sum(x => x.finPlanYear2);
                grpData.finPlanYear3 = data.Sum(x => x.finPlanYear3) + data2B.Sum(x => x.finPlanYear3);
                grpData.finPlanYear4 = data.Sum(x => x.finPlanYear4) + data2B.Sum(x => x.finPlanYear4);
                grpData.semi = true;
                grpData.clickable = false;
                grpData.isSubHeading = false;
                grpData.numberFormat = "n0";
                grpData.note = null;
                dataArray.Add(grpData);
            }
            else
            {
                //sum row
                grpData = new JObject();
                grpData.LineGroupId = -1;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = -1;
                grpData.budgetFormDesc = langStringValues["BudForm_sum"].LangText + " " + data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.actualAmtYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.actualAmtYear);
                grpData.revisedBudAmtYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.revisedBudAmtYear);
                grpData.orgBudAmtYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.orgBudAmtYear);
                grpData.actualAmtLastYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.actualAmtLastYear);
                grpData.orgBudAmtLastYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.orgBudAmtLastYear);
                grpData.revisedAmtLastYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.revisedAmtLastYear);
                grpData.finPlanYear1 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear1);
                grpData.finPlanYear2 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear2);
                grpData.finPlanYear3 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear3);
                grpData.finPlanYear4 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear4);
                grpData.semi = true;
                grpData.clickable = false;
                grpData.isSubHeading = false;
                grpData.numberFormat = "n0";
                grpData.note = null;
                dataArray.Add(grpData);
            }

            //empty row
            if (!onlyData)
            {
                grpData = new JObject();
                grpData.LineGroupId = null;
                grpData.LineGroup = null;
                grpData.id = null;
                grpData.budgetFormDesc = "";
                grpData.actualAmtYear = "";
                grpData.revisedBudAmtYear = "";
                grpData.orgBudAmtYear = "";
                grpData.actualAmtLastYear = "";
                grpData.orgBudAmtLastYear = "";
                grpData.revisedAmtLastYear = "";
                grpData.finPlanYear1 = "";
                grpData.finPlanYear2 = "";
                grpData.finPlanYear3 = "";
                grpData.finPlanYear4 = "";
                grpData.semi = true;
                grpData.clickable = false;
                grpData.isSubHeading = false;
                grpData.numberFormat = "Text";
                grpData.note = null;
                dataArray.Add(grpData);
            }
        }

        //final total row
        //sum row
        grpData = new JObject();
        grpData.LineGroupId = -1;
        grpData.LineGroup = "";
        grpData.id = -1;
        grpData.budgetFormDesc = langStringValues["BudForm_sum"].LangText + " " + langStringValues["BudForm_Sum_2A"].LangText;
        grpData.actualAmtYear = data.Sum(x => x.actualAmtYear);
        grpData.revisedBudAmtYear = data.Sum(x => x.revisedBudAmtYear);
        grpData.orgBudAmtYear = data.Sum(x => x.orgBudAmtYear);
        grpData.actualAmtLastYear = data.Sum(x => x.actualAmtLastYear);
        grpData.orgBudAmtLastYear = data.Sum(x => x.orgBudAmtLastYear);
        grpData.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear);
        grpData.finPlanYear1 = data.Sum(x => x.finPlanYear1);
        grpData.finPlanYear2 = data.Sum(x => x.finPlanYear2);
        grpData.finPlanYear3 = data.Sum(x => x.finPlanYear3);
        grpData.finPlanYear4 = data.Sum(x => x.finPlanYear4);
        grpData.semi = true;
        grpData.clickable = false;
        grpData.isSubHeading = false;
        grpData.numberFormat = "n0";
        grpData.note = null;
        dataArray.Add(grpData);
        if(!onlyData)
        {
            finalData.columns = await GetBudgetFormColumnAsync(budgetYear, userId, "2A", docType, forecastPeriod);
            finalData.header = header;
        }
        finalData.data = dataArray;
        return finalData;
    }

    public async Task<JObject> Get2BDataAsync(int budgetYear, string userId, int docType, int forecastPeriod = 0, bool onlyData = false)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValues = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetForm");

        dynamic header = new JObject();
        header.title = langStringValues["BudForm_2B_title"].LangText;
        header.descriptiontip = langStringValues["BudForm_2B_desc"].LangText;
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        var commentList = await GetCommentListAsync(budgetYear, userDetails, dbContext, clsConstants.budget_form_type.BudgetForm2B.ToString(), docType, forecastPeriod);
        JArray dataArray = new JArray();
        dynamic finalData = new JObject();
        List<clsBudgetFormHelper> dataList = new List<clsBudgetFormHelper>();
        var data = (from a in (await Get2BDataFromDBAsync(budgetYear, userId, docType, forecastPeriod))
            group a by new { aggregate_id = a.aggregate_id.Trim(), aggregate_name = a.aggregate_name.Trim(), sub_header_id = a.sub_header_id.Trim(), sub_header_name = a.sub_header_name.Trim() } into grp
            select new clsBudgetFormHelper()
            {
                LineGroupId = grp.Key.aggregate_id,
                LineGroup = grp.Key.aggregate_name,
                id = grp.Key.aggregate_id,
                SubHearderId = grp.Key.sub_header_id,
                SubHearderName = grp.Key.sub_header_name,
                budgetFormDesc = grp.Key.aggregate_name,
                actualAmtYear = grp.Sum(x => x.actual_amt_year),
                revisedBudAmtYear = grp.Sum(x => x.revised_bud_amt_year),
                orgBudAmtYear = grp.Sum(x => x.org_bud_amt_year),
                actualAmtLastYear = grp.Sum(x => x.actual_amt_last_year),
                orgBudAmtLastYear = grp.Sum(x => x.org_bud_amt_last_year),
                revisedAmtLastYear = grp.Sum(x => x.revised_bud_amt_last_year),
                finPlanYear1 = grp.Sum(x => x.finplan_year_1_amount),
                finPlanYear2 = grp.Sum(x => x.finplan_year_2_amount),
                finPlanYear3 = grp.Sum(x => x.finplan_year_3_amount),
                finPlanYear4 = grp.Sum(x => x.finplan_year_4_amount),
                finPlanYearSum = grp.Sum(x => x.finplan_year_1_amount) + grp.Sum(x => x.finplan_year_2_amount) + grp.Sum(x => x.finplan_year_3_amount) + grp.Sum(x => x.finplan_year_4_amount),
                numberFormat = "n0",
                semi = false,
                clickable = true,
                isSubHeading = false
            }).OrderBy(x => x.SubHearderId).ThenBy(x => x.LineGroupId).ToList();

        var subHeaderList = data.Select(x => x.SubHearderName).Distinct().ToList();
        dynamic shData;
        if (subHeaderList.Count > 1)// if there are more then one sub header display data by each sub header
        {
            foreach (var sh in subHeaderList)
            {
                shData = new JObject();

                shData.LineGroupId = "0";
                shData.LineGroup = sh;
                shData.id = null;
                shData.budgetFormDesc = sh;
                if (onlyData)
                {
                    shData.actualAmtYear = 0;
                    shData.revisedBudAmtYear = 0;
                    shData.orgBudAmtYear = 0;
                    shData.actualAmtLastYear = 0;
                    shData.orgBudAmtLastYear = 0;
                    shData.revisedAmtLastYear = 0;
                    shData.finPlanYear1 = 0;
                    shData.finPlanYear2 = 0;
                    shData.finPlanYear3 = 0;
                    shData.finPlanYear4 = 0;
                    shData.finPlanYearSum = 0;
                }
                else
                {
                    shData.actualAmtYear = "";
                    shData.revisedBudAmtYear = "";
                    shData.orgBudAmtYear = "";
                    shData.actualAmtLastYear = "";
                    shData.orgBudAmtLastYear = "";
                    shData.revisedAmtLastYear = "";
                    shData.finPlanYear1 = "";
                    shData.finPlanYear2 = "";
                    shData.finPlanYear3 = "";
                    shData.finPlanYear4 = "";
                    shData.finPlanYearSum = "";
                }
                shData.semi = true;
                shData.clickable = false;
                shData.isSubHeading = true;
                shData.numberFormat = "Text";
                shData.rowType = "SubHeader";
                dataArray.Add(shData);

                foreach (var d in data.Where(x => x.SubHearderName == sh).ToList())// filter data based on current subheard and display
                {
                    dynamic DataFor2b = new JObject();
                    DataFor2b.LineGroupId = d.LineGroupId;
                    DataFor2b.LineGroup = d.LineGroup;
                    DataFor2b.id = d.id;
                    DataFor2b.budgetFormDesc = d.budgetFormDesc;
                    DataFor2b.actualAmtYear = d.actualAmtYear;
                    DataFor2b.revisedBudAmtYear = d.revisedBudAmtYear;
                    DataFor2b.orgBudAmtYear = d.orgBudAmtYear;
                    DataFor2b.actualAmtLastYear = d.actualAmtLastYear;
                    DataFor2b.orgBudAmtLastYear = d.orgBudAmtLastYear;
                    DataFor2b.revisedAmtLastYear = d.revisedAmtLastYear;
                    DataFor2b.finPlanYear1 = d.finPlanYear1;
                    DataFor2b.finPlanYear2 = d.finPlanYear2;
                    DataFor2b.finPlanYear3 = d.finPlanYear3;
                    DataFor2b.finPlanYear4 = d.finPlanYear4;
                    DataFor2b.finPlanYearSum = d.finPlanYearSum;
                    DataFor2b.semi = false;
                    DataFor2b.clickable = true;
                    DataFor2b.isSubHeading = false;
                    DataFor2b.numberFormat = d.numberFormat;
                    DataFor2b.note = commentList.FirstOrDefault(x => x.line_group_id.ToString() == d.LineGroupId && x.line_item_id == d.id) != null ? commentList.FirstOrDefault(x => x.line_group_id.ToString() == d.LineGroupId && x.line_item_id == d.id).comment : string.Empty;
                    DataFor2b.rowType = "DetailRow";
                    dataArray.Add(DataFor2b);
                }

                // summary row for current sub header
                dynamic shSumData = new JObject();

                shSumData.LineGroupId = "-1";
                shSumData.LineGroup = sh;
                shSumData.id = null;
                shSumData.budgetFormDesc = langStringValues["BudForm_sum"].LangText + " " + sh;
                shSumData.actualAmtYear = data.Where(x => x.SubHearderName == sh).Sum(x => x.actualAmtYear);
                shSumData.revisedBudAmtYear = data.Where(x => x.SubHearderName == sh).Sum(x => x.revisedBudAmtYear);
                shSumData.orgBudAmtYear = data.Where(x => x.SubHearderName == sh).Sum(x => x.orgBudAmtYear);
                shSumData.actualAmtLastYear = data.Where(x => x.SubHearderName == sh).Sum(x => x.actualAmtLastYear);
                shSumData.orgBudAmtLastYear = data.Where(x => x.SubHearderName == sh).Sum(x => x.orgBudAmtLastYear);
                shSumData.revisedAmtLastYear = data.Where(x => x.SubHearderName == sh).Sum(x => x.revisedAmtLastYear);
                shSumData.finPlanYear1 = data.Where(x => x.SubHearderName == sh).Sum(x => x.finPlanYear1);
                shSumData.finPlanYear2 = data.Where(x => x.SubHearderName == sh).Sum(x => x.finPlanYear2);
                shSumData.finPlanYear3 = data.Where(x => x.SubHearderName == sh).Sum(x => x.finPlanYear3);
                shSumData.finPlanYear4 = data.Where(x => x.SubHearderName == sh).Sum(x => x.finPlanYear4);
                shSumData.finPlanYearSum = data.Where(x => x.SubHearderName == sh).Sum(x => x.finPlanYearSum);
                shSumData.semi = true;
                shSumData.clickable = false;
                shSumData.isSubHeading = false;
                shSumData.numberFormat = "n0";
                shSumData.rowType = "SumRow";
                dataArray.Add(shSumData);

                /// blank row after sum row
                if (!onlyData)
                {
                    dynamic emptRow = new JObject();
                    emptRow.LineGroupId = null;
                    emptRow.LineGroup = null;
                    emptRow.id = null;
                    emptRow.budgetFormDesc = "";
                    emptRow.actualAmtYear = "";
                    emptRow.revisedBudAmtYear = "";
                    emptRow.orgBudAmtYear = "";
                    emptRow.actualAmtLastYear = "";
                    emptRow.orgBudAmtLastYear = "";
                    emptRow.revisedAmtLastYear = "";
                    emptRow.finPlanYear1 = "";
                    emptRow.finPlanYear2 = "";
                    emptRow.finPlanYear3 = "";
                    emptRow.finPlanYear4 = "";
                    emptRow.finPlanYearSum = "";
                    emptRow.semi = true;
                    emptRow.clickable = false;
                    emptRow.numberFormat = "Text";
                    emptRow.rowType = "DetailRow";
                    dataArray.Add(emptRow);
                }
            }

            // final summary row for current sub header
            dynamic finalSumData = new JObject();

            finalSumData.LineGroupId = "-1";
            finalSumData.LineGroup = null;
            finalSumData.id = null;
            finalSumData.budgetFormDesc = langStringValues["BudForm_Sum_2B"].LangText;
            finalSumData.actualAmtYear = data.Sum(x => x.actualAmtYear);
            finalSumData.revisedBudAmtYear = data.Sum(x => x.revisedBudAmtYear);
            finalSumData.orgBudAmtYear = data.Sum(x => x.orgBudAmtYear);
            finalSumData.actualAmtLastYear = data.Sum(x => x.actualAmtLastYear);
            finalSumData.orgBudAmtLastYear = data.Sum(x => x.orgBudAmtLastYear);
            finalSumData.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear);
            finalSumData.finPlanYear1 = data.Sum(x => x.finPlanYear1);
            finalSumData.finPlanYear2 = data.Sum(x => x.finPlanYear2);
            finalSumData.finPlanYear3 = data.Sum(x => x.finPlanYear3);
            finalSumData.finPlanYear4 = data.Sum(x => x.finPlanYear4);
            finalSumData.finPlanYearSum = data.Sum(x => x.finPlanYearSum);
            finalSumData.semi = true;
            finalSumData.clickable = false;
            finalSumData.isSubHeading = false;
            finalSumData.numberFormat = "n0";
            finalSumData.rowType = "SumRow";
            dataArray.Add(finalSumData);
        }
        else
        {
            foreach (var item in data)// get the note column data for each
            {
                item.note = commentList.FirstOrDefault(x => x.line_group_id.ToString() == item.LineGroupId && x.line_item_id == item.id) != null ? commentList.FirstOrDefault(x => x.line_group_id.ToString() == item.LineGroupId && x.line_item_id == item.id).comment : string.Empty;
                item.rowType = "DetailRow";
            }
            dataList.AddRange(data);
            clsBudgetFormHelper sumRow = new clsBudgetFormHelper()// summary row
            {
                actualAmtLastYear = data.Sum(x => x.actualAmtLastYear),
                orgBudAmtYear = data.Sum(x => x.orgBudAmtYear),
                revisedBudAmtYear = data.Sum(x => x.revisedBudAmtYear),
                actualAmtYear = data.Sum(x => x.actualAmtYear),
                orgBudAmtLastYear = data.Sum(x => x.orgBudAmtLastYear),
                revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear),
                finPlanYear1 = data.Sum(x => x.finPlanYear1),
                finPlanYear2 = data.Sum(x => x.finPlanYear2),
                finPlanYear3 = data.Sum(x => x.finPlanYear3),
                finPlanYear4 = data.Sum(x => x.finPlanYear4),
                finPlanYearSum = data.Sum(x => x.finPlanYearSum),
                budgetFormDesc = langStringValues["BudForm_Sum_2B"].LangText,
                id = "-1",
                LineGroup = null,
                LineGroupId = "-1",
                numberFormat = "n0",
                semi = true,
                clickable = false,
                isSubHeading = false,
                note = null,
                rowType = "SumRow"
            };

            dataList.Add(sumRow);
        }

        finalData.data = subHeaderList.Count > 1 ? dataArray : JArray.FromObject(dataList);
        if(!onlyData)
        {
            finalData.columns = await GetBudgetFormColumnAsync(budgetYear, userId, "2B", docType, forecastPeriod);
            finalData.header = header;
        }
        return finalData;
    }

    public async Task<JObject> GetForm3DataAsync(int budgetYear, string userId, int docType, int forecastPeriod = 0, bool onlyData = false)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValues = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetForm");

        dynamic header = new JObject();
        header.title = langStringValues["BudForm_B3_title"].LangText;
        header.descriptiontip = langStringValues["BudForm_B3_desc"].LangText;
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        var commentList = await GetCommentListAsync(budgetYear, userDetails, dbContext, clsConstants.budget_form_type.BudgetFormB3.ToString(), docType, forecastPeriod);
        JArray dataArray = new JArray();
        dynamic finalData = new JObject();
        var data = (from a in (await GetForm3DataFromDBAsync(budgetYear, userId, docType, forecastPeriod))
            group a by new { line_group_id = a.line_group_id.ToString().Trim(), line_group = a.line_group.Trim(), line_item_id = a.line_item_id.ToString().Trim(), line_item = a.line_item.Trim() } into grp
            select new clsBudgetFormHelper()
            {
                LineGroupId = grp.Key.line_group_id.ToString(),
                LineGroup = grp.Key.line_group,
                id = grp.Key.line_item_id.ToString(),
                budgetFormDesc = grp.Key.line_item,
                actualAmtYear = grp.Sum(x => x.actual_amt_year),
                revisedBudAmtYear = grp.Sum(x => x.revised_bud_amt_year),
                orgBudAmtYear = grp.Sum(x => x.org_bud_amt_year),
                actualAmtLastYear = grp.Sum(x => x.actual_amt_last_year),
                orgBudAmtLastYear = grp.Sum(x => x.org_bud_amt_last_year),
                revisedAmtLastYear = grp.Sum(x => x.revised_bud_amt_last_year),
                finPlanYear1 = grp.Sum(x => x.finplan_year_1_amount),
                finPlanYear2 = grp.Sum(x => x.finplan_year_2_amount),
                finPlanYear3 = grp.Sum(x => x.finplan_year_3_amount),
                finPlanYear4 = grp.Sum(x => x.finplan_year_4_amount),
                numberFormat = "n0",
                isSubHeading = false
            }).ToList().OrderBy(x => x.LineGroupId).ThenBy(x => x.LineGroup).ThenBy(x => x.id).ThenBy(x => x.budgetFormDesc);

        var LineGroupIdList = new List<string>();
        LineGroupIdList = data.Select(x => x.LineGroupId).Distinct().ToList();
        dynamic grpData;
        int index = 0;
        foreach (var item in LineGroupIdList)
        {
            grpData = new JObject();
            grpData.LineGroupId = item;
            grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
            grpData.id = null;
            grpData.budgetFormDesc = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
            if(onlyData)
            {
                grpData.actualAmtYear = 0;
                grpData.revisedBudAmtYear = 0;
                grpData.orgBudAmtYear = 0;
                grpData.actualAmtLastYear = 0;
                grpData.orgBudAmtLastYear = 0;
                grpData.revisedAmtLastYear = 0;
                grpData.finPlanYear1 = 0;
                grpData.finPlanYear2 = 0;
                grpData.finPlanYear3 = 0;
                grpData.finPlanYear4 = 0;
            }
            else
            {
                grpData.actualAmtYear = "";
                grpData.revisedBudAmtYear = "";
                grpData.orgBudAmtYear = "";
                grpData.actualAmtLastYear = "";
                grpData.orgBudAmtLastYear = "";
                grpData.revisedAmtLastYear = "";
                grpData.finPlanYear1 = "";
                grpData.finPlanYear2 = "";
                grpData.finPlanYear3 = "";
                grpData.finPlanYear4 = "";
            }
                
            grpData.isSubHeading = true;
            grpData.semi = true;
            grpData.clickable = false;
            grpData.numberFormat = "Text";
            grpData.note = null;
            dataArray.Add(grpData);

            foreach (var d in data.Where(x => x.LineGroupId == item).ToList())
            {
                grpData = new JObject();
                grpData.LineGroupId = item;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = d.id;
                grpData.budgetFormDesc = d.budgetFormDesc;
                grpData.actualAmtYear = d.actualAmtYear;
                grpData.revisedBudAmtYear = d.revisedBudAmtYear;
                grpData.orgBudAmtYear = d.orgBudAmtYear;
                grpData.actualAmtLastYear = d.actualAmtLastYear;
                grpData.orgBudAmtLastYear = d.orgBudAmtLastYear;
                grpData.revisedAmtLastYear = d.revisedAmtLastYear;
                grpData.finPlanYear1 = d.finPlanYear1;
                grpData.finPlanYear2 = d.finPlanYear2;
                grpData.finPlanYear3 = d.finPlanYear3;
                grpData.finPlanYear4 = d.finPlanYear4;
                grpData.semi = false;
                grpData.clickable = true;
                grpData.isSubHeading = false;
                grpData.numberFormat = d.numberFormat;
                grpData.note = commentList.FirstOrDefault(x => x.line_group_id.ToString() == item && x.line_item_id == d.id) != null ? commentList.FirstOrDefault(x => x.line_group_id.ToString() == item && x.line_item_id == d.id).comment : string.Empty;
                dataArray.Add(grpData);
            }

            //sum row
            grpData = new JObject();
            grpData.LineGroupId = -1;
            grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
            grpData.id = -1;
            grpData.budgetFormDesc = langStringValues["BudForm_sum"].LangText + " " + data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
            grpData.actualAmtYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.actualAmtYear);
            grpData.revisedBudAmtYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.revisedBudAmtYear);
            grpData.orgBudAmtYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.orgBudAmtYear);
            grpData.actualAmtLastYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.actualAmtLastYear);
            grpData.orgBudAmtLastYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.orgBudAmtLastYear);
            grpData.revisedAmtLastYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.revisedAmtLastYear);
            grpData.finPlanYear1 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear1);
            grpData.finPlanYear2 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear2);
            grpData.finPlanYear3 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear3);
            grpData.finPlanYear4 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear4);
            grpData.semi = true;
            grpData.clickable = false;
            grpData.isSubHeading = false;
            grpData.numberFormat = "n0";
            grpData.note = null;
            dataArray.Add(grpData);

            //empty row
            if (!onlyData)
            {
                grpData = new JObject();
                grpData.LineGroupId = null;
                grpData.LineGroup = null;
                grpData.id = null;
                grpData.budgetFormDesc = "";
                grpData.actualAmtYear = "";
                grpData.revisedBudAmtYear = "";
                grpData.orgBudAmtYear = "";
                grpData.actualAmtLastYear = "";
                grpData.orgBudAmtLastYear = "";
                grpData.revisedAmtLastYear = "";
                grpData.finPlanYear1 = "";
                grpData.finPlanYear2 = "";
                grpData.finPlanYear3 = "";
                grpData.finPlanYear4 = "";
                grpData.semi = true;
                grpData.clickable = false;
                grpData.isSubHeading = false;
                grpData.numberFormat = "Text";
                grpData.note = null;
                dataArray.Add(grpData);
            }

            if (item == "20" || item == "40" || item == "50")
            {
                List<string> lgIdList = new List<string>();

                if (item == "40")
                {
                    for (int i = index - 1; i <= index; i++)
                    {
                        lgIdList.Add(LineGroupIdList[i]);
                    }
                }
                else
                {
                    for (int i = 0; i <= index; i++)
                    {
                        lgIdList.Add(LineGroupIdList[i]);
                    }
                }

                //sum row
                grpData = new JObject();
                grpData.LineGroupId = -1;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = -1;
                grpData.budgetFormDesc = langStringValues["BudForm_sum_" + item].LangText;
                grpData.actualAmtYear = data.Where(x => lgIdList.Contains(x.LineGroupId)).ToList().Sum(x => x.actualAmtYear);
                grpData.revisedBudAmtYear = data.Where(x => lgIdList.Contains(x.LineGroupId)).ToList().Sum(x => x.revisedBudAmtYear);
                grpData.orgBudAmtYear = data.Where(x => lgIdList.Contains(x.LineGroupId)).ToList().Sum(x => x.orgBudAmtYear);
                grpData.actualAmtLastYear = data.Where(x => lgIdList.Contains(x.LineGroupId)).ToList().Sum(x => x.actualAmtLastYear);
                grpData.orgBudAmtLastYear = data.Where(x => lgIdList.Contains(x.LineGroupId)).ToList().Sum(x => x.actualAmtLastYear);
                grpData.revisedAmtLastYear = data.Where(x => lgIdList.Contains(x.LineGroupId)).ToList().Sum(x => x.revisedAmtLastYear);
                grpData.finPlanYear1 = data.Where(x => lgIdList.Contains(x.LineGroupId)).ToList().Sum(x => x.finPlanYear1);
                grpData.finPlanYear2 = data.Where(x => lgIdList.Contains(x.LineGroupId)).ToList().Sum(x => x.finPlanYear2);
                grpData.finPlanYear3 = data.Where(x => lgIdList.Contains(x.LineGroupId)).ToList().Sum(x => x.finPlanYear3);
                grpData.finPlanYear4 = data.Where(x => lgIdList.Contains(x.LineGroupId)).ToList().Sum(x => x.finPlanYear4);
                grpData.semi = true;
                grpData.clickable = false;
                grpData.isSubHeading = false;
                grpData.numberFormat = "n0";
                grpData.note = null;
                dataArray.Add(grpData);

                //empty row
                if (!onlyData)
                {
                    grpData = new JObject();
                    grpData.LineGroupId = null;
                    grpData.LineGroup = null;
                    grpData.id = null;
                    grpData.budgetFormDesc = "";
                    grpData.actualAmtYear = "";
                    grpData.revisedBudAmtYear = "";
                    grpData.orgBudAmtYear = "";
                    grpData.actualAmtLastYear = "";
                    grpData.orgBudAmtLastYear = "";
                    grpData.revisedAmtLastYear = "";
                    grpData.finPlanYear1 = "";
                    grpData.finPlanYear2 = "";
                    grpData.finPlanYear3 = "";
                    grpData.finPlanYear4 = "";
                    grpData.semi = true;
                    grpData.clickable = false;
                    grpData.isSubHeading = false;
                    grpData.numberFormat = "Text";
                    grpData.note = null;
                    dataArray.Add(grpData);
                }
            }

            index++;
        }

        //final total row
        //sum row
        grpData = new JObject();
        grpData.LineGroupId = -1;
        grpData.LineGroup = "";
        grpData.id = -1;
        grpData.budgetFormDesc = langStringValues["BudForm_sum"].LangText + " " + langStringValues["BudForm_Sum_B3"].LangText;
        grpData.actualAmtYear = data.Sum(x => x.actualAmtYear);
        grpData.revisedBudAmtYear = data.Sum(x => x.revisedBudAmtYear);
        grpData.orgBudAmtYear = data.Sum(x => x.orgBudAmtYear);
        grpData.actualAmtLastYear = data.Sum(x => x.actualAmtLastYear);
        grpData.orgBudAmtLastYear = data.Sum(x => x.orgBudAmtLastYear);
        grpData.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear);
        grpData.finPlanYear1 = data.Sum(x => x.finPlanYear1);
        grpData.finPlanYear2 = data.Sum(x => x.finPlanYear2);
        grpData.finPlanYear3 = data.Sum(x => x.finPlanYear3);
        grpData.finPlanYear4 = data.Sum(x => x.finPlanYear4);
        grpData.semi = true;
        grpData.clickable = false;
        grpData.isSubHeading = false;
        grpData.numberFormat = "n0";
        grpData.note = null;
        dataArray.Add(grpData);

        finalData.data = dataArray;
        if (!onlyData)
        {
            finalData.columns = await GetBudgetFormColumnAsync(budgetYear, userId, "B3", docType, forecastPeriod);
            finalData.header = header;
        }
        return finalData;
    }

    public async Task<JObject> GetForm4DataAsync(int budgetYear, string userId, int docType, int forecastPeriod = 0, bool onlyData = false)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValues = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetForm");

        dynamic header = new JObject();
        header.title = langStringValues["BudForm_B4_title"].LangText;
        header.descriptiontip = langStringValues["BudForm_B4_desc"].LangText;
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        var commentList = await GetCommentListAsync(budgetYear, userDetails, dbContext, clsConstants.budget_form_type.BudgetFormB4.ToString(), docType, forecastPeriod);
        JArray dataArray = new JArray();
        dynamic finalData = new JObject();
        var data = (from a in await GetForm4DataFromDBAsync(budgetYear, userId, docType, forecastPeriod)
            group a by new { line_group_id = a.line_group_id.ToString().Trim(), line_group = a.line_group.Trim(), line_item_id = a.line_item_id.ToString().Trim(), line_item = a.line_item.Trim() } into grp
            select new clsBudgetFormHelper()
            {
                LineGroupId = grp.Key.line_group_id.ToString(),
                LineGroup = grp.Key.line_group,
                id = grp.Key.line_item_id.ToString(),
                budgetFormDesc = grp.Key.line_item,
                actualAmtYear = grp.Sum(x => x.actual_amt_year),
                revisedBudAmtYear = grp.Sum(x => x.revised_bud_amt_year),
                orgBudAmtYear = grp.Sum(x => x.org_bud_amt_year),
                actualAmtLastYear = grp.Sum(x => x.actual_amt_last_year),
                orgBudAmtLastYear = grp.Sum(x => x.org_bud_amt_last_year),
                revisedAmtLastYear = grp.Sum(x => x.revised_bud_amt_last_year),
                finPlanYear1 = grp.Sum(x => x.finplan_year_1_amount),
                finPlanYear2 = grp.Sum(x => x.finplan_year_2_amount),
                finPlanYear3 = grp.Sum(x => x.finplan_year_3_amount),
                finPlanYear4 = grp.Sum(x => x.finplan_year_4_amount),
                numberFormat = "n0",
                isSubHeading = false
            }).ToList().OrderBy(x => x.LineGroupId).ThenBy(x => x.LineGroup).ThenBy(x => x.id).ThenBy(x => x.budgetFormDesc);

        var LineGroupIdList = new List<string>();
        LineGroupIdList = data.Select(x => x.LineGroupId).Distinct().ToList();
        dynamic grpData;
        foreach (var item in LineGroupIdList)
        {
            grpData = new JObject();
            grpData.LineGroupId = item;
            grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
            grpData.id = null;
            grpData.budgetFormDesc = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
            if(onlyData)
            {
                grpData.actualAmtYear = 0;
                grpData.revisedBudAmtYear = 0;
                grpData.orgBudAmtYear = 0;
                grpData.actualAmtLastYear = 0;
                grpData.orgBudAmtLastYear = 0;
                grpData.revisedAmtLastYear = 0;
                grpData.finPlanYear1 = 0;
                grpData.finPlanYear2 = 0;
                grpData.finPlanYear3 = 0;
                grpData.finPlanYear4 = 0;
            }
            else
            {
                grpData.actualAmtYear = "";
                grpData.revisedBudAmtYear = "";
                grpData.orgBudAmtYear = "";
                grpData.actualAmtLastYear = "";
                grpData.orgBudAmtLastYear = "";
                grpData.revisedAmtLastYear = "";
                grpData.finPlanYear1 = "";
                grpData.finPlanYear2 = "";
                grpData.finPlanYear3 = "";
                grpData.finPlanYear4 = "";
            }
            grpData.isSubHeading = true;
            grpData.semi = true;
            grpData.clickable = false;
            grpData.numberFormat = "Text";
            grpData.note = null;
            dataArray.Add(grpData);

            foreach (var d in data.Where(x => x.LineGroupId == item).ToList())
            {
                grpData = new JObject();
                grpData.LineGroupId = item;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = d.id;
                grpData.budgetFormDesc = d.budgetFormDesc;
                grpData.actualAmtYear = d.actualAmtYear;
                grpData.revisedBudAmtYear = d.revisedBudAmtYear;
                grpData.orgBudAmtYear = d.orgBudAmtYear;
                grpData.actualAmtLastYear = d.actualAmtLastYear;
                grpData.orgBudAmtLastYear = d.orgBudAmtLastYear;
                grpData.revisedAmtLastYear = d.revisedAmtLastYear;
                grpData.finPlanYear1 = d.finPlanYear1;
                grpData.finPlanYear2 = d.finPlanYear2;
                grpData.finPlanYear3 = d.finPlanYear3;
                grpData.finPlanYear4 = d.finPlanYear4;
                grpData.semi = false;
                grpData.clickable = true;
                grpData.isSubHeading = false;
                grpData.numberFormat = d.numberFormat;
                grpData.note = commentList.FirstOrDefault(x => x.line_group_id.ToString() == item && x.line_item_id == d.id) != null ? commentList.FirstOrDefault(x => x.line_group_id.ToString() == item && x.line_item_id == d.id).comment : string.Empty;
                dataArray.Add(grpData);
            }
            //sum row
            grpData = new JObject();
            grpData.LineGroupId = -1;
            grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
            grpData.id = -1;
            grpData.budgetFormDesc = langStringValues["BudForm_sum"].LangText + " " + data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
            grpData.actualAmtYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.actualAmtYear);
            grpData.revisedBudAmtYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.revisedBudAmtYear);
            grpData.orgBudAmtYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.orgBudAmtYear);
            grpData.actualAmtLastYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.actualAmtLastYear);
            grpData.orgBudAmtLastYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.orgBudAmtLastYear);
            grpData.revisedAmtLastYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.revisedAmtLastYear);
            grpData.finPlanYear1 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear1);
            grpData.finPlanYear2 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear2);
            grpData.finPlanYear3 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear3);
            grpData.finPlanYear4 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear4);
            grpData.semi = true;
            grpData.clickable = false;
            grpData.isSubHeading = false;
            grpData.numberFormat = "n0";
            grpData.note = null;
            dataArray.Add(grpData);

            //empty row
            if (!onlyData)
            {
                grpData = new JObject();
                grpData.LineGroupId = null;
                grpData.LineGroup = null;
                grpData.id = null;
                grpData.budgetFormDesc = "";
                grpData.actualAmtYear = "";
                grpData.revisedBudAmtYear = "";
                grpData.orgBudAmtYear = "";
                grpData.actualAmtLastYear = "";
                grpData.orgBudAmtLastYear = "";
                grpData.revisedAmtLastYear = "";
                grpData.finPlanYear1 = "";
                grpData.finPlanYear2 = "";
                grpData.finPlanYear3 = "";
                grpData.finPlanYear4 = "";
                grpData.semi = true;
                grpData.clickable = false;
                grpData.isSubHeading = false;
                grpData.numberFormat = "Text";
                grpData.note = null;
                dataArray.Add(grpData);
            }
        }

        //final total row
        //sum row
        grpData = new JObject();
        grpData.LineGroupId = -1;
        grpData.LineGroup = "";
        grpData.id = -1;
        grpData.budgetFormDesc = langStringValues["BudForm_sum"].LangText + " " + langStringValues["BudForm_Sum_B4"].LangText;
        grpData.actualAmtYear = data.Sum(x => x.actualAmtYear);
        grpData.revisedBudAmtYear = data.Sum(x => x.revisedBudAmtYear);
        grpData.orgBudAmtYear = data.Sum(x => x.orgBudAmtYear);
        grpData.actualAmtLastYear = data.Sum(x => x.actualAmtLastYear);
        grpData.orgBudAmtLastYear = data.Sum(x => x.orgBudAmtLastYear);
        grpData.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear);
        grpData.finPlanYear1 = data.Sum(x => x.finPlanYear1);
        grpData.finPlanYear2 = data.Sum(x => x.finPlanYear2);
        grpData.finPlanYear3 = data.Sum(x => x.finPlanYear3);
        grpData.finPlanYear4 = data.Sum(x => x.finPlanYear4);
        grpData.semi = true;
        grpData.clickable = false;
        grpData.isSubHeading = false;
        grpData.numberFormat = "n0";
        grpData.note = null;
        dataArray.Add(grpData);

        finalData.data = dataArray;
        if (!onlyData)
        {
            finalData.columns = await GetBudgetFormColumnAsync(budgetYear, userId, "B4", docType, forecastPeriod);
            finalData.header = header;
        }
        return finalData;
    }

    public JObject GetFramsikt1AData(int budgetYear, string userId, int docType, int forecastPeriod = 0, bool onlyData = false)
    {
        return GetFramsikt1ADataAsync(budgetYear, userId, docType, forecastPeriod, onlyData).GetAwaiter().GetResult();
    }

    public async Task<JObject> GetFramsikt1ADataAsync(int budgetYear, string userId, int docType, int forecastPeriod = 0, bool onlyData = false)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValues = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetForm");

        dynamic header = new JObject();
        header.title = langStringValues["BudForm_Fram_1A_title"].LangText;
        header.descriptiontip = langStringValues["BudForm_Fram_1A_desc"].LangText;
        TenantDBContext dbContext = await _pUtility.GetTenantDBContextAsync();
        dbContext.Database.SetCommandTimeout(300);

        var commentList = await GetCommentListAsync(budgetYear, userDetails, dbContext, clsConstants.budget_form_type.Framsikt1A.ToString(), docType, forecastPeriod);
        var data1B = (from a in (await GetFramsikt1BDataFromDBAsync(budgetYear, userId, docType, forecastPeriod))
            group a by new { aggregate_id = a.aggregate_id.Trim(), aggregate_name = a.aggregate_name.Trim() } into grp
            select new clsBudgetFormHelper()
            {
                LineGroupId = grp.Key.aggregate_id,
                LineGroup = grp.Key.aggregate_name,
                id = grp.Key.aggregate_id,
                budgetFormDesc = grp.Key.aggregate_name,
                actualAmtYear = grp.Sum(x => x.actual_amt_year),
                revisedBudAmtYear = grp.Sum(x => x.revised_bud_amt_year),
                orgBudAmtYear = grp.Sum(x => x.org_bud_amt_year),
                actualAmtLastYear = grp.Sum(x => x.actual_amt_last_year),
                orgBudAmtLastYear = grp.Sum(x => x.org_bud_amt_last_year),
                revisedAmtLastYear = grp.Sum(x => x.revised_bud_amt_last_year),
                forecastAmount = grp.Sum(x => x.forecast_amount),
                finPlanYear1 = grp.Sum(x => x.finplan_year_1_amount),
                finPlanYear2 = grp.Sum(x => x.finplan_year_2_amount),
                finPlanYear3 = grp.Sum(x => x.finplan_year_3_amount),
                finPlanYear4 = grp.Sum(x => x.finplan_year_4_amount),
                budgetPeriod = grp.Sum(x => x.budget_period == null ? 0 : x.budget_period),
                accountingPeriod = grp.Sum(x => x.accounting_period == null ? 0 : x.accounting_period),
                accountignYtdPrev = grp.Sum(x => x.accounting_ytd_prev == null ? 0 : x.accounting_ytd_prev),
                budgetYtd = grp.Sum(x => x.budget_ytd == null ? 0 : x.budget_ytd),
                accountingYtd = grp.Sum(x => x.accounting_ytd == null ? 0 : x.accounting_ytd),
                deviationYtd = grp.Sum(x => x.deviation_ytd == null ? 0 : x.deviation_ytd),
                deviationYtdPct = grp.Sum(x => x.budget_ytd == null ? 0 : x.budget_ytd) == 0 ? 0 : (grp.Sum(x => x.deviation_ytd == null ? 0 : x.deviation_ytd) / grp.Sum(x => x.budget_ytd == null ? 0 : x.budget_ytd)) * 100,
                deviationForecast = grp.Sum(x => x.deviation_forecast == null ? 0 : x.deviation_forecast),
                deviationForecastPct = grp.Sum(x => x.revised_bud_amt_year) == 0 ? 0 : (grp.Sum(x => x.deviation_forecast == null ? 0 : x.deviation_forecast) / grp.Sum(x => x.revised_bud_amt_year)) * 100,
                budgetChange = grp.Sum(x => x.budget_change == null ? 0 : x.budget_change),
                deviationAction = grp.Sum(x => x.deviation_action == null ? 0 : x.deviation_action),
                forecastInclDev = grp.Sum(x => x.forecast_incl_dev == null ? 0 : x.forecast_incl_dev),
                deviationInclDevAction = grp.Sum(x => x.deviation_incl_dev_action == null ? 0 : x.deviation_incl_dev_action),
                deviationInclDevActionPct = grp.Sum(x => x.revised_bud_amt_year) == 0 ? 0 : (grp.Sum(x => x.deviation_incl_dev_action == null ? 0 : x.deviation_incl_dev_action) / grp.Sum(x => x.revised_bud_amt_year)) * 100,
                numberFormat = "n0",
                semi = false,
                clickable = true,
                isSubHeading = false,
            }).ToList().OrderBy(x => x.id).ThenBy(x => x.budgetFormDesc);

        JArray dataArray = new JArray();
        dynamic finalData = new JObject();
        List<clsBudgetFormHelper> data = new List<clsBudgetFormHelper>();

        var rawData = (from a in (await GetFramsikt1ADataFromDBAsync(budgetYear, userId, docType, forecastPeriod))
            group a by new { line_group_id = a.line_group_id.ToString().Trim(), line_group = a.line_group.Trim(), line_item_id = a.line_item_id.ToString().Trim(), line_item = a.line_item.Trim() } into grp
            select new clsBudgetFormHelper()
            {
                LineGroupId = grp.Key.line_group_id.ToString(),
                LineGroup = grp.Key.line_group,
                id = grp.Key.line_item_id.ToString(),
                budgetFormDesc = grp.Key.line_item,
                actualAmtYear = grp.Sum(x => x.actual_amt_year),
                revisedBudAmtYear = grp.Sum(x => x.revised_bud_amt_year),
                orgBudAmtYear = grp.Sum(x => x.org_bud_amt_year),
                actualAmtLastYear = grp.Sum(x => x.actual_amt_last_year),
                orgBudAmtLastYear = grp.Sum(x => x.org_bud_amt_last_year),
                revisedAmtLastYear = grp.Sum(x => x.revised_bud_amt_last_year),
                forecastAmount = grp.Sum(x => x.forecast_amount),
                finPlanYear1 = grp.Sum(x => x.finplan_year_1_amount),
                finPlanYear2 = grp.Sum(x => x.finplan_year_2_amount),
                finPlanYear3 = grp.Sum(x => x.finplan_year_3_amount),
                finPlanYear4 = grp.Sum(x => x.finplan_year_4_amount),
                budgetPeriod = grp.Sum(x => x.budget_period == null ? 0 : x.budget_period),
                accountingPeriod = grp.Sum(x => x.accounting_period == null ? 0 : x.accounting_period),
                accountignYtdPrev = grp.Sum(x => x.accounting_ytd_prev == null ? 0 : x.accounting_ytd_prev),
                budgetYtd = grp.Sum(x => x.budget_ytd == null ? 0 : x.budget_ytd),
                accountingYtd = grp.Sum(x => x.accounting_ytd == null ? 0 : x.accounting_ytd),
                deviationYtd = grp.Sum(x => x.deviation_ytd == null ? 0 : x.deviation_ytd),
                deviationYtdPct = grp.Sum(x => x.budget_ytd == null ? 0 : x.budget_ytd) == 0 ? 0 : (grp.Sum(x => x.deviation_ytd == null ? 0 : x.deviation_ytd) / grp.Sum(x => x.budget_ytd == null ? 0 : x.budget_ytd)) * 100,
                deviationForecast = grp.Sum(x => x.deviation_forecast == null ? 0 : x.deviation_forecast),
                deviationForecastPct = grp.Sum(x => x.revised_bud_amt_year) == 0 ? 0 : (grp.Sum(x => x.deviation_forecast == null ? 0 : x.deviation_forecast) / grp.Sum(x => x.revised_bud_amt_year)) * 100,
                budgetChange = grp.Sum(x => x.budget_change == null ? 0 : x.budget_change),
                deviationAction = grp.Sum(x => x.deviation_action == null ? 0 : x.deviation_action),
                forecastInclDev = grp.Sum(x => x.forecast_incl_dev == null ? 0 : x.forecast_incl_dev),
                deviationInclDevAction = grp.Sum(x => x.deviation_incl_dev_action == null ? 0 : x.deviation_incl_dev_action),
                deviationInclDevActionPct = grp.Sum(x => x.revised_bud_amt_year) == 0 ? 0 : (grp.Sum(x => x.deviation_incl_dev_action == null ? 0 : x.deviation_incl_dev_action) / grp.Sum(x => x.revised_bud_amt_year)) * 100,
                numberFormat = "n0",
                isSubHeading = false
            }).ToList().OrderBy(x => x.LineGroupId).ThenBy(x => x.LineGroup).ThenBy(x => x.id).ThenBy(x => x.budgetFormDesc);

        data.AddRange(rawData);

        var LineGroupIdList = new List<string>();
        LineGroupIdList = data.Select(x => x.LineGroupId).Distinct().ToList();
        dynamic grpData;
        var LineGroupIdListCount = LineGroupIdList.Count();
        var count = 0;
        foreach (var item in LineGroupIdList)
        {
            count++;
            grpData = new JObject();
            grpData.LineGroupId = item;
            grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
            grpData.id = null;
            grpData.budgetFormDesc = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
            if(onlyData)
            {
                grpData.actualAmtYear = 0;
                grpData.revisedBudAmtYear = 0;
                grpData.orgBudAmtYear = 0;
                grpData.actualAmtLastYear = 0;
                grpData.orgBudAmtLastYear = 0;
                grpData.revisedAmtLastYear = 0;
                grpData.forcastAmount = 0;
                grpData.finPlanYear1 = 0;
                grpData.finPlanYear2 = 0;
                grpData.finPlanYear3 = 0;
                grpData.finPlanYear4 = 0;
                grpData.budgetPeriod = 0;
                grpData.accountingPeriod = 0;
                grpData.accountignYtdPrev = 0;
                grpData.budgetYtd = 0;
                grpData.accountingYtd = 0;
                grpData.deviationYtd = 0;
                grpData.deviationYtdPct = 0;
                grpData.deviationForecast = 0;
                grpData.deviationForecastPct = 0;
                grpData.budgetChange = 0;
                grpData.deviationAction = 0;
                grpData.forecastInclDev = 0;
                grpData.deviationInclDevAction = 0;
                grpData.deviationInclDevActionPct = 0;
            }
            else
            {
                grpData.actualAmtYear = "";
                grpData.revisedBudAmtYear = "";
                grpData.orgBudAmtYear = "";
                grpData.actualAmtLastYear = "";
                grpData.orgBudAmtLastYear = "";
                grpData.revisedAmtLastYear = "";
                grpData.forcastAmount = "";
                grpData.finPlanYear1 = "";
                grpData.finPlanYear2 = "";
                grpData.finPlanYear3 = "";
                grpData.finPlanYear4 = "";
                grpData.budgetPeriod = "";
                grpData.accountingPeriod = "";
                grpData.accountignYtdPrev = "";
                grpData.budgetYtd = "";
                grpData.accountingYtd = "";
                grpData.deviationYtd = "";
                grpData.deviationYtdPct = "";
                grpData.deviationForecast = "";
                grpData.deviationForecastPct = "";
                grpData.budgetChange = "";
                grpData.deviationAction = "";
                grpData.forecastInclDev = "";
                grpData.deviationInclDevAction = "";
                grpData.deviationInclDevActionPct = "";
            }
                
            grpData.semi = true;
            grpData.clickable = false;
            grpData.isSubHeading = true;
            grpData.numberFormat = "Text";
            grpData.note = null;
            dataArray.Add(grpData);

            foreach (var d in data.Where(x => x.LineGroupId == item).ToList())
            {
                grpData = new JObject();
                grpData.LineGroupId = item;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = d.id;
                grpData.budgetFormDesc = d.budgetFormDesc;
                grpData.actualAmtYear = d.actualAmtYear;
                grpData.revisedBudAmtYear = d.revisedBudAmtYear;
                grpData.orgBudAmtYear = d.orgBudAmtYear;
                grpData.actualAmtLastYear = d.actualAmtLastYear;
                grpData.orgBudAmtLastYear = d.orgBudAmtLastYear;
                grpData.revisedAmtLastYear = d.revisedAmtLastYear;
                grpData.forecastAmount = d.forecastAmount;
                grpData.finPlanYear1 = d.finPlanYear1;
                grpData.finPlanYear2 = d.finPlanYear2;
                grpData.finPlanYear3 = d.finPlanYear3;
                grpData.finPlanYear4 = d.finPlanYear4;
                grpData.budgetPeriod = d.budgetPeriod;
                grpData.accountingPeriod = d.accountingPeriod;
                grpData.accountignYtdPrev = d.accountignYtdPrev;
                grpData.budgetYtd = d.budgetYtd;
                grpData.accountingYtd = d.accountingYtd;
                grpData.deviationYtd = d.deviationYtd;
                grpData.deviationYtdPct = d.deviationYtdPct;
                grpData.deviationForecast = d.deviationForecast;
                grpData.deviationForecastPct = d.deviationForecastPct;
                grpData.budgetChange = d.budgetChange;
                grpData.deviationAction = d.deviationAction;
                grpData.forecastInclDev = d.forecastInclDev;
                grpData.deviationInclDevAction = d.deviationInclDevAction;
                grpData.deviationInclDevActionPct = d.deviationInclDevActionPct;
                grpData.semi = false;
                grpData.clickable = true;
                grpData.isSubHeading = false;
                grpData.numberFormat = d.numberFormat;
                grpData.note = commentList.FirstOrDefault(x => x.line_group_id.ToString() == item && x.line_item_id == d.id) != null ? commentList.FirstOrDefault(x => x.line_group_id.ToString() == item && x.line_item_id == d.id).comment : string.Empty;
                dataArray.Add(grpData);
            }

            if (LineGroupIdListCount == count)
            {
                //sum row 1
                grpData = new JObject();
                grpData.LineGroupId = item;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = -1;
                grpData.budgetFormDesc = langStringValues["BudForm_sum"].LangText + " " + "Til fordeling drift";
                grpData.actualAmtYear = data.Sum(x => x.actualAmtYear);
                grpData.revisedBudAmtYear = data.Sum(x => x.revisedBudAmtYear);
                grpData.orgBudAmtYear = data.Sum(x => x.orgBudAmtYear);
                grpData.actualAmtLastYear = data.Sum(x => x.actualAmtLastYear);
                grpData.orgBudAmtLastYear = data.Sum(x => x.orgBudAmtLastYear);
                grpData.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear);
                grpData.forecastAmount = data.Sum(x => x.forecastAmount);
                grpData.finPlanYear1 = data.Sum(x => x.finPlanYear1);
                grpData.finPlanYear2 = data.Sum(x => x.finPlanYear2);
                grpData.finPlanYear3 = data.Sum(x => x.finPlanYear3);
                grpData.finPlanYear4 = data.Sum(x => x.finPlanYear4);
                grpData.budgetPeriod = data.Sum(x => x.budgetPeriod);
                grpData.accountingPeriod = data.Sum(x => x.accountingPeriod);
                grpData.accountignYtdPrev = data.Sum(x => x.accountignYtdPrev);
                grpData.budgetYtd = data.Sum(x => x.budgetYtd);
                grpData.accountingYtd = data.Sum(x => x.accountingYtd);
                grpData.deviationYtd = data.Sum(x => x.deviationYtd);
                grpData.deviationYtdPct = data.Sum(x => x.deviationYtdPct);
                grpData.deviationForecast = data.Sum(x => x.deviationForecast);
                grpData.deviationForecastPct = data.Sum(x => x.deviationForecastPct);
                grpData.budgetChange = data.Sum(x => x.budgetChange);
                grpData.deviationAction = data.Sum(x => x.deviationAction);
                grpData.forecastInclDev = data.Sum(x => x.forecastInclDev);
                grpData.deviationInclDevAction = data.Sum(x => x.deviationInclDevAction);
                grpData.deviationInclDevActionPct = data.Sum(x => x.deviationInclDevActionPct);
                grpData.semi = true;
                grpData.clickable = false;
                grpData.isSubHeading = false;
                grpData.numberFormat = "n0";
                grpData.note = null;
                dataArray.Add(grpData);

                //sum row 2
                grpData = new JObject();
                grpData.LineGroupId = item;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = -1;
                grpData.budgetFormDesc = langStringValues["BudForm_sum"].LangText + " " + "fordelt til drift (fra skjema 1B)";
                grpData.actualAmtYear = data1B.Sum(x => x.actualAmtYear);
                grpData.revisedBudAmtYear = data1B.Sum(x => x.revisedBudAmtYear);
                grpData.orgBudAmtYear = data1B.Sum(x => x.orgBudAmtYear);
                grpData.actualAmtLastYear = data1B.Sum(x => x.actualAmtLastYear);
                grpData.orgBudAmtLastYear = data1B.Sum(x => x.orgBudAmtLastYear);
                grpData.revisedAmtLastYear = data1B.Sum(x => x.revisedAmtLastYear);
                grpData.forecastAmount = data1B.Sum(X => X.forecastAmount);
                grpData.finPlanYear1 = data1B.Sum(x => x.finPlanYear1);
                grpData.finPlanYear2 = data1B.Sum(x => x.finPlanYear2);
                grpData.finPlanYear3 = data1B.Sum(x => x.finPlanYear3);
                grpData.finPlanYear4 = data1B.Sum(x => x.finPlanYear4);
                grpData.budgetPeriod = data1B.Sum(x => x.budgetPeriod);
                grpData.accountingPeriod = data1B.Sum(x => x.accountingPeriod);
                grpData.accountignYtdPrev = data1B.Sum(x => x.accountignYtdPrev);
                grpData.budgetYtd = data1B.Sum(x => x.budgetYtd);
                grpData.accountingYtd = data1B.Sum(x => x.accountingYtd);
                grpData.deviationYtd = data1B.Sum(x => x.deviationYtd);
                grpData.deviationYtdPct = data1B.Sum(x => x.deviationYtdPct);
                grpData.deviationForecast = data1B.Sum(x => x.deviationForecast);
                grpData.deviationForecastPct = data1B.Sum(x => x.deviationForecastPct);
                grpData.budgetChange = data1B.Sum(x => x.budgetChange);
                grpData.deviationAction = data1B.Sum(x => x.deviationAction);
                grpData.forecastInclDev = data1B.Sum(x => x.forecastInclDev);
                grpData.deviationInclDevAction = data1B.Sum(x => x.deviationInclDevAction);
                grpData.deviationInclDevActionPct = data1B.Sum(x => x.deviationInclDevActionPct);
                grpData.semi = true;
                grpData.clickable = false;
                grpData.isSubHeading = false;
                grpData.numberFormat = "n0";
                grpData.note = null;
                dataArray.Add(grpData);

                //sum total
                grpData = new JObject();
                grpData.LineGroupId = item;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = -1;
                grpData.budgetFormDesc = langStringValues["BudForm_sum"].LangText + " " + langStringValues["BudForm_Sum_framsikt_1A"].LangText;
                grpData.actualAmtYear = data.Sum(x => x.actualAmtYear) + data1B.Sum(x => x.actualAmtYear);
                grpData.revisedBudAmtYear = data.Sum(x => x.revisedBudAmtYear) + data1B.Sum(x => x.revisedBudAmtYear);
                grpData.orgBudAmtYear = data.Sum(x => x.orgBudAmtYear) + data1B.Sum(x => x.orgBudAmtYear);
                grpData.actualAmtLastYear = data.Sum(x => x.actualAmtLastYear) + data1B.Sum(x => x.actualAmtLastYear);
                grpData.orgBudAmtLastYear = data.Sum(x => x.orgBudAmtLastYear) + data1B.Sum(x => x.orgBudAmtLastYear);
                grpData.revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear) + data1B.Sum(x => x.revisedAmtLastYear);
                grpData.forecastAmount = data.Sum(x => x.forecastAmount) + data1B.Sum(x => x.forecastAmount);
                grpData.finPlanYear1 = data.Sum(x => x.finPlanYear1) + data1B.Sum(x => x.finPlanYear1);
                grpData.finPlanYear2 = data.Sum(x => x.finPlanYear2) + data1B.Sum(x => x.finPlanYear2);
                grpData.finPlanYear3 = data.Sum(x => x.finPlanYear3) + data1B.Sum(x => x.finPlanYear3);
                grpData.finPlanYear4 = data.Sum(x => x.finPlanYear4) + data1B.Sum(x => x.finPlanYear4);
                grpData.budgetPeriod = data.Sum(x => x.budgetPeriod) + data1B.Sum(x => x.budgetPeriod);
                grpData.accountingPeriod = data.Sum(x => x.accountingPeriod) + data1B.Sum(x => x.accountingPeriod);
                grpData.accountignYtdPrev = data.Sum(x => x.accountignYtdPrev) + data1B.Sum(x => x.accountignYtdPrev);
                grpData.budgetYtd = data.Sum(x => x.budgetYtd) + data1B.Sum(x => x.budgetYtd);
                grpData.accountingYtd = data.Sum(x => x.accountingYtd) + data1B.Sum(x => x.accountingYtd);
                grpData.deviationYtd = data.Sum(x => x.deviationYtd) + data1B.Sum(x => x.deviationYtd);
                grpData.deviationYtdPct = data.Sum(x => x.deviationYtdPct) + data1B.Sum(x => x.deviationYtdPct);
                grpData.deviationForecast = data.Sum(x => x.deviationForecast) + data1B.Sum(x => x.deviationForecast);
                grpData.deviationForecastPct = data.Sum(x => x.deviationForecastPct) + data1B.Sum(x => x.deviationForecastPct);
                grpData.budgetChange = data.Sum(x => x.budgetChange) + data1B.Sum(x => x.budgetChange);
                grpData.deviationAction = data.Sum(x => x.deviationAction) + data1B.Sum(x => x.deviationAction);
                grpData.forecastInclDev = data.Sum(x => x.forecastInclDev) + data1B.Sum(x => x.forecastInclDev);
                grpData.deviationInclDevAction = data.Sum(x => x.deviationInclDevAction) + data1B.Sum(x => x.deviationInclDevAction);
                grpData.deviationInclDevActionPct = data.Sum(x => x.deviationInclDevActionPct) + data1B.Sum(x => x.deviationInclDevActionPct);
                grpData.semi = true;
                grpData.clickable = false;
                grpData.isSubHeading = false;
                grpData.numberFormat = "n0";
                grpData.note = null;
                dataArray.Add(grpData);
            }
            else
            {
                //sum row
                grpData = new JObject();
                grpData.LineGroupId = -1;
                grpData.LineGroup = data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.id = -1;
                grpData.budgetFormDesc = langStringValues["BudForm_sum"].LangText + " " + data.FirstOrDefault(x => x.LineGroupId == item).LineGroup;
                grpData.actualAmtYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.actualAmtYear);
                grpData.revisedBudAmtYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.revisedBudAmtYear);
                grpData.orgBudAmtYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.orgBudAmtYear);
                grpData.actualAmtLastYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.actualAmtLastYear);
                grpData.orgBudAmtLastYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.orgBudAmtLastYear);
                grpData.revisedAmtLastYear = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.revisedAmtLastYear);
                grpData.forecastAmount = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.forecastAmount);
                grpData.finPlanYear1 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear1);
                grpData.finPlanYear2 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear2);
                grpData.finPlanYear3 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear3);
                grpData.finPlanYear4 = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear4);
                grpData.budgetPeriod = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.finPlanYear4);
                grpData.accountingPeriod = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.accountingPeriod);
                grpData.accountignYtdPrev = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.accountignYtdPrev);
                grpData.budgetYtd = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.budgetYtd);
                grpData.accountingYtd = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.accountingYtd);
                grpData.deviationYtd = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.deviationYtd);
                grpData.deviationYtdPct = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.deviationYtdPct);
                grpData.deviationForecast = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.deviationForecast);
                grpData.deviationForecastPct = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.deviationForecastPct);
                grpData.budgetChange = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.budgetChange);
                grpData.deviationAction = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.deviationAction);
                grpData.forecastInclDev = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.forecastInclDev);
                grpData.deviationInclDevAction = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.deviationInclDevAction);
                grpData.deviationInclDevActionPct = data.Where(x => x.LineGroupId == item).ToList().Sum(x => x.deviationInclDevActionPct);
                grpData.semi = true;
                grpData.clickable = false;
                grpData.isSubHeading = false;
                grpData.numberFormat = "n0";
                grpData.note = null;
                dataArray.Add(grpData);
            }

            //empty row
            if (!onlyData)
            {
                grpData = new JObject();
                grpData.LineGroupId = null;
                grpData.LineGroup = null;
                grpData.id = null;
                grpData.budgetFormDesc = "";
                grpData.actualAmtYear = "";
                grpData.revisedBudAmtYear = "";
                grpData.orgBudAmtYear = "";
                grpData.actualAmtLastYear = "";
                grpData.orgBudAmtLastYear = "";
                grpData.revisedAmtLastYear = "";
                grpData.forecastAmount = "";
                grpData.finPlanYear1 = "";
                grpData.finPlanYear2 = "";
                grpData.finPlanYear3 = "";
                grpData.finPlanYear4 = "";
                grpData.budgetPeriod = "";
                grpData.accountingPeriod = "";
                grpData.accountignYtdPrev = "";
                grpData.budgetYtd = "";
                grpData.accountingYtd = "";
                grpData.deviationYtd = "";
                grpData.deviationYtdPct = "";
                grpData.deviationForecast = "";
                grpData.deviationForecastPct = "";
                grpData.budgetChange = "";
                grpData.deviationAction = "";
                grpData.forecastInclDev = "";
                grpData.deviationInclDevAction = "";
                grpData.deviationInclDevActionPct = "";
                grpData.semi = true;
                grpData.clickable = false;
                grpData.numberFormat = "Text";
                grpData.note = null;
                dataArray.Add(grpData);
            }
        }

        finalData.data = dataArray;
        if(!onlyData)
        {
            finalData.columns = await GetBudgetFormColumnAsync(budgetYear, userId, "Framsikt1A", docType, forecastPeriod);
            finalData.header = header;
        }
        return finalData;
    }

    public JObject GetFramsikt1BData(int budgetYear, string userId, int docType, int forecastPeriod = 0, bool onlyData = false)
    {
        return GetFramsikt1BDataAsync(budgetYear, userId, docType, forecastPeriod, onlyData).GetAwaiter().GetResult();
    }

    public async Task<JObject> GetFramsikt1BDataAsync(int budgetYear, string userId, int docType, int forecastPeriod = 0, bool onlyData = false)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValues = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BudgetForm");

        dynamic header = new JObject();
        header.title = langStringValues["BudForm_Fram_1B_title"].LangText;
        header.descriptiontip = langStringValues["BudForm_Fram_1B_desc"].LangText;
        TenantDBContext dbContext = await _pUtility.GetTenantDBContextAsync();
        var commentList = await GetCommentListAsync(budgetYear, userDetails, dbContext, clsConstants.budget_form_type.Framsikt1B.ToString(), docType, forecastPeriod);
        JArray dataArray = new JArray();
        dynamic finalData = new JObject();
        List<clsBudgetFormHelper> dataList = new List<clsBudgetFormHelper>();
        dbContext.Database.SetCommandTimeout(300);
        var data = (from a in (await GetFramsikt1BDataFromDBAsync(budgetYear, userId, docType, forecastPeriod))
            where a.year == budgetYear && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
            group a by new { aggregate_id = a.aggregate_id.Trim(), aggregate_name = a.aggregate_name.Trim() } into grp
            select new clsBudgetFormHelper()
            {
                LineGroupId = grp.Key.aggregate_id,
                LineGroup = grp.Key.aggregate_name,
                id = grp.Key.aggregate_id,
                budgetFormDesc = grp.Key.aggregate_name,
                actualAmtYear = grp.Sum(x => x.actual_amt_year),
                revisedBudAmtYear = grp.Sum(x => x.revised_bud_amt_year),
                orgBudAmtYear = grp.Sum(x => x.org_bud_amt_year),
                actualAmtLastYear = grp.Sum(x => x.actual_amt_last_year),
                orgBudAmtLastYear = grp.Sum(x => x.org_bud_amt_last_year),
                revisedAmtLastYear = grp.Sum(x => x.revised_bud_amt_last_year),
                forecastAmount = grp.Sum(x => x.forecast_amount),
                finPlanYear1 = grp.Sum(x => x.finplan_year_1_amount),
                finPlanYear2 = grp.Sum(x => x.finplan_year_2_amount),
                finPlanYear3 = grp.Sum(x => x.finplan_year_3_amount),
                finPlanYear4 = grp.Sum(x => x.finplan_year_4_amount),
                budgetPeriod = grp.Sum(x => x.budget_period == null ? 0 : x.budget_period),
                accountingPeriod = grp.Sum(x => x.accounting_period == null ? 0 : x.accounting_period),
                accountignYtdPrev = grp.Sum(x => x.accounting_ytd_prev == null ? 0 : x.accounting_ytd_prev),
                budgetYtd = grp.Sum(x => x.budget_ytd == null ? 0 : x.budget_ytd),
                accountingYtd = grp.Sum(x => x.accounting_ytd == null ? 0 : x.accounting_ytd),
                deviationYtd = grp.Sum(x => x.deviation_ytd == null ? 0 : x.deviation_ytd),
                deviationYtdPct = grp.Sum(x => x.budget_ytd == null ? 0 : x.budget_ytd) == 0 ? 0 : (grp.Sum(x => x.deviation_ytd == null ? 0 : x.deviation_ytd) / grp.Sum(x => x.budget_ytd == null ? 0 : x.budget_ytd)) * 100,
                deviationForecast = grp.Sum(x => x.deviation_forecast == null ? 0 : x.deviation_forecast),
                deviationForecastPct = grp.Sum(x => x.revised_bud_amt_year) == 0 ? 0 : (grp.Sum(x => x.deviation_forecast == null ? 0 : x.deviation_forecast) / grp.Sum(x => x.revised_bud_amt_year)) * 100,
                budgetChange = grp.Sum(x => x.budget_change == null ? 0 : x.budget_change),
                deviationAction = grp.Sum(x => x.deviation_action == null ? 0 : x.deviation_action),
                forecastInclDev = grp.Sum(x => x.forecast_incl_dev == null ? 0 : x.forecast_incl_dev),
                deviationInclDevAction = grp.Sum(x => x.deviation_incl_dev_action == null ? 0 : x.deviation_incl_dev_action),
                deviationInclDevActionPct = grp.Sum(x => x.revised_bud_amt_year) == 0 ? 0 : (grp.Sum(x => x.deviation_incl_dev_action == null ? 0 : x.deviation_incl_dev_action) / grp.Sum(x => x.revised_bud_amt_year)) * 100,
                numberFormat = "n0",
                semi = false,
                clickable = true,
                isSubHeading = false
            }).ToList().OrderBy(x => x.id).ThenBy(x => x.budgetFormDesc);

        foreach (var item in data)
        {
            item.note = commentList.FirstOrDefault(x => x.line_group_id.ToString() == item.LineGroupId && x.line_item_id == item.id) != null ? commentList.FirstOrDefault(x => x.line_group_id.ToString() == item.LineGroupId && x.line_item_id == item.id).comment : string.Empty;
        }

        dataList.AddRange(data);
        clsBudgetFormHelper sumRow = new clsBudgetFormHelper()
        {
            actualAmtLastYear = data.Sum(x => x.actualAmtLastYear),
            orgBudAmtYear = data.Sum(x => x.orgBudAmtYear),
            revisedBudAmtYear = data.Sum(x => x.revisedBudAmtYear),
            actualAmtYear = data.Sum(x => x.actualAmtYear),
            orgBudAmtLastYear = data.Sum(x => x.orgBudAmtLastYear),
            revisedAmtLastYear = data.Sum(x => x.revisedAmtLastYear),
            finPlanYear1 = data.Sum(x => x.finPlanYear1),
            finPlanYear2 = data.Sum(x => x.finPlanYear2),
            finPlanYear3 = data.Sum(x => x.finPlanYear3),
            finPlanYear4 = data.Sum(x => x.finPlanYear4),
            forecastAmount = data.Sum(x => x.forecastAmount),
            budgetPeriod = data.Sum(x => x.budgetPeriod),
            accountingPeriod = data.Sum(x => x.accountingPeriod),
            accountignYtdPrev = data.Sum(x => x.accountignYtdPrev),
            budgetYtd = data.Sum(x => x.budgetYtd),
            accountingYtd = data.Sum(x => x.accountingYtd),
            deviationYtd = data.Sum(x => x.deviationYtd),
            deviationYtdPct = data.Sum(x => x.deviationYtdPct),
            deviationForecast = data.Sum(x => x.deviationForecast),
            deviationForecastPct = data.Sum(x => x.deviationForecastPct),
            budgetChange = data.Sum(x => x.budgetChange),
            deviationAction = data.Sum(x => x.deviationAction),
            forecastInclDev = data.Sum(x => x.forecastInclDev),
            deviationInclDevAction = data.Sum(x => x.deviationInclDevAction),
            deviationInclDevActionPct = data.Sum(x => x.deviationInclDevActionPct),
            budgetFormDesc = langStringValues["BudForm_Sum_Fram_1B"].LangText,
            id = "-1",
            LineGroup = null,
            LineGroupId = "-1",
            numberFormat = "n0",
            semi = true,
            clickable = false,
            note = null,
            isSubHeading = false
        };

        dataList.Add(sumRow);

        finalData.data = JArray.FromObject(dataList);
        if (!onlyData)
        {
            finalData.columns = await GetBudgetFormColumnAsync(budgetYear, userId, "Framsikt1B", docType, forecastPeriod);
            finalData.header = header;
        }
        return finalData;
    }

    private async Task<List<tbf_budget_form_1A>> Get1ADataFromDBAsync(int budgetYear, string userId, int docType, int forecastPeriod)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        return forecastPeriod == 0 ? await (from a in dbContext.tbf_budget_form_1A
                where a.year == budgetYear && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
                select a).ToListAsync()
            : await (from a in dbContext.tbf_budget_form_1A
                where a.forecast_period == forecastPeriod && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
                select a).ToListAsync();
    }

    private async Task<List<tbf_budget_form_2A>> Get2ADataFromDBAsync(int budgetYear, string userId, int docType, int forecastPeriod)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        return forecastPeriod == 0 ? await (from a in dbContext.tbf_budget_form_2A
                where a.year == budgetYear && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
                select a).ToListAsync()
            : await (from a in dbContext.tbf_budget_form_2A
                where a.forecast_period == forecastPeriod && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
                select a).ToListAsync();
    }

    private async Task<List<tbf_budget_form_1B>> Get1BDataFromDBAsync(int budgetYear, string userId, int docType, int forecastPeriod)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        return forecastPeriod == 0 ? await (from a in dbContext.tbf_budget_form_1B
                where a.year == budgetYear && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
                select a).ToListAsync()
            : await (from a in dbContext.tbf_budget_form_1B
                where a.forecast_period == forecastPeriod && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
                select a).ToListAsync();
    }

    private async Task<List<tbf_budget_form_2B>> Get2BDataFromDBAsync(int budgetYear, string userId, int docType, int forecastPeriod)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        return forecastPeriod == 0 ? await (from a in dbContext.tbf_budget_form_2B
                where a.year == budgetYear && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
                select a).ToListAsync()
            : await (from a in dbContext.tbf_budget_form_2B
                where a.forecast_period == forecastPeriod && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
                select a).ToListAsync();
    }

    private async Task<List<tbf_budget_form_framsikt_1A>> GetFramsikt1ADataFromDBAsync(int budgetYear, string userId, int docType, int forecastPeriod)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        return forecastPeriod == 0 ? await (from a in dbContext.tbf_budget_form_framsikt_1A
                where a.year == budgetYear && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
                select a).ToListAsync()
            : await (from a in dbContext.tbf_budget_form_framsikt_1A
                where a.forecast_period == forecastPeriod && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
                select a).ToListAsync();
    }

    private async Task<List<tbf_budget_form_framsikt_1B>> GetFramsikt1BDataFromDBAsync(int budgetYear, string userId, int docType, int forecastPeriod)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        return forecastPeriod == 0 ? await (from a in dbContext.tbf_budget_form_framsikt_1B
                where a.year == budgetYear && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
                select a).ToListAsync()
            : await (from a in dbContext.tbf_budget_form_framsikt_1B
                where a.forecast_period == forecastPeriod && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
                select a).ToListAsync();
    }

    private async Task<List<tbf_budget_form_B3>> GetForm3DataFromDBAsync(int budgetYear, string userId, int docType, int forecastPeriod)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        return forecastPeriod == 0 ? await (from a in dbContext.tbf_budget_form_B3
                where a.year == budgetYear && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
                select a).ToListAsync()
            : await (from a in dbContext.tbf_budget_form_B3
                where a.forecast_period == forecastPeriod && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
                select a).ToListAsync();
    }

    private async Task<List<tbf_budget_form_B4>> GetForm4DataFromDBAsync(int budgetYear, string userId, int docType, int forecastPeriod)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        var dbContext = await _pUtility.GetTenantDBContextAsync();
        return forecastPeriod == 0 ? await (from a in dbContext.tbf_budget_form_B4
                where a.year == budgetYear && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
                select a).ToListAsync()
            : await (from a in dbContext.tbf_budget_form_B4
                where a.forecast_period == forecastPeriod && a.fk_tenant_id == userDetails.tenant_id && a.doc_type == docType
                select a).ToListAsync();
    }

}