using Aspose.Cells;
using Framsikt.BL.Core;
using Framsikt.BL.Helpers;
using System.Drawing;
using System.Globalization;
using static Framsikt.BL.Utility;

namespace Framsikt.BL;

public partial class BudgetFormBL
{
    public MemoryStream ExportBudgetFormGrid(string userId, BudFormExportHelper _budFormExportHelper, int docType, int forecastPeriod = 0)
    {
        return ExportBudgetFormGridAsync(userId, _budFormExportHelper, docType, forecastPeriod).GetAwaiter().GetResult();
    }

    public async Task<MemoryStream> ExportBudgetFormGridAsync(string userId, BudFormExportHelper _budFormExportHelper, int docType, int forecastPeriod = 0)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        CultureInfo ci = CultureInfoFactory.CreateCulture(userDetails.language_preference);
        _formats = await _pUtility.GetLanguageStringsAsync("en-US", userId, "ExportNumberFormats");
        MemoryStream stream = new MemoryStream();            
        using (Workbook wb = new Workbook())
        {
            //Note when you create a new workbook, a default worksheet
            //"Sheet1" is added (by default) to the workbook.
            //Access the first worksheet "Sheet1" in the book.
            Worksheet sheet = wb.Worksheets[0];

            await InsertHeaderRowAsync(userId, wb, _budFormExportHelper.columns.Where(x => x.isChecked == true).ToList());
            InsertChildRows(_budFormExportHelper, sheet, wb, ci);
            //Save the Excel file.
            wb.Save(stream, SaveFormat.Xlsx);
            return stream;
        }
    }

    private async Task InsertHeaderRowAsync(string userId, Workbook wb, dynamic colDef)
    {
        string[] colNames = { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M" };

        Style textStyle = wb.CreateStyle();
        ///textStyle Font.IsBold  true;
        textStyle.Number = 49;

        Style decStyle = wb.CreateStyle();
        // decStyle Font IsBold true;
        decStyle.Number = 2;

        Style intStyle = wb.CreateStyle();
        //  intStyle Font IsBold  true;
        intStyle.Number = 1;

        Style curStyle = wb.CreateStyle();
        curStyle.Number = 4;

        StyleFlag styleFlag = new StyleFlag { NumberFormat = true, All = true };

        Worksheet sheet = wb.Worksheets[0];

        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);

        int i = 0;
        int j = 0;

        List<string> numericColumns = new List<string>() { "actualAmtYear", "revisedBudAmtYear", "orgBudAmtYear", "actualAmtLastYear" , "revisedAmtYearByAuth", "revisedAmtLastYearByAuth" };
        List<string> numericColWithTextTitle = new List<string>() { "actualAmtYear", "revisedBudAmtYear", "orgBudAmtYear", "actualAmtLastYear" , "revisedAmtYearByAuth", "revisedAmtLastYearByAuth" };
        foreach (var col in colDef)
        {
            string colName = col.key.ToString();
            if (colName == "id")
            {
                continue;
            }

            if (numericColumns.Contains(colName))
            {
                sheet.Cells[i, j].Value = !numericColWithTextTitle.Contains(colName) ? int.Parse(col.value.ToString()) : col.value.ToString();
                //First apply currency style to the column
                //Then change the style of the header cell to integer for the year
                sheet.Cells[i, j].SetStyle(intStyle, styleFlag);
                ////Set the formula for the totals row
            }
            else
            {
                sheet.Cells[i, j].Value = col.value;
                sheet.Cells.Columns[j].ApplyStyle(textStyle, styleFlag);
            }
            j++;
        }
    }

    private void InsertChildRows(BudFormExportHelper _budFormExportHelper, Worksheet sheet, Workbook wb, CultureInfo ci)
    {
        if (_budFormExportHelper == null)
        {
            return;
        }

        int i = 1;
        //data

        foreach (var rowData in _budFormExportHelper.data)
        {
            InsertActiveRow(sheet, _budFormExportHelper.columns.Where(x => x.isChecked == true).ToList(), rowData, i, wb, ci);
            i++;
        }
    }

    private String GetFormat(String number, CultureInfo cInfo, NumberTypes numType)
    {
        String decSeperator = cInfo.NumberFormat.NumberDecimalSeparator;
        String formatKey = "";
        String format = "";

        //Decimal number
        int decPlaces = GetNumberOfDecPlaces(number, decSeperator);

        switch (numType)
        {
            case NumberTypes.Decimal:
                //Construct the format key. This logic will work only for formats n0, n1....
                formatKey = "n" + decPlaces.ToString();
                break;

            case NumberTypes.Percentage:
                //Construct the format key. This logic will work only for formats n0, n1....
                formatKey = "p" + decPlaces.ToString();
                break;
        }

        format = ((_formats.FirstOrDefault(v => v.Key == formatKey)).Value).LangText;
        return format;
    }

    private static int GetNumberOfDecPlaces(String number, String decSeperator)
    {
        string[] parts = number.Split(decSeperator[0]);
        int decPlaces = 0;
        //A decimal will split into two parts
        //The second element in the array will be the decimal part
        if (parts.Length > 1)
        {
            decPlaces = parts[1].Length;
        }
        return decPlaces;
    }

    private void InsertActiveRow(Worksheet sheet, dynamic colDef, clsBudgetFormHelper rowData, int rowNum, Workbook wb, CultureInfo ci)
    {
        int colNum = 0;
        foreach (var col in colDef)
        {
            string colName = col.key.ToString();
            Style bgstyle = wb.CreateStyle();
            bool grayRow = (rowData.id == "-1" || rowData.id == "-3") && rowData.clickable == false;
            if (grayRow)// add grey color for sumary row
            {
                bgstyle.ForegroundColor = Color.Gainsboro;
                bgstyle.Pattern = BackgroundType.Solid;
                bgstyle.BackgroundColor = Color.Gainsboro;
                bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();
                bgstyle.Number = 1;
            }
            StyleFlag flag = new StyleFlag();
            flag.All = true;
            switch (colName)
            {
                case "budgetFormDesc":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();
                        bgstyle.Custom = GetFormat(rowData.revisedBudAmtYear.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    sheet.Cells[rowNum, colNum].Value = !string.IsNullOrEmpty(rowData.budgetFormDesc) ? rowData.budgetFormDesc.ToString() : string.Empty;
                    colNum++;
                    break;
                case "LineGroup":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();
                        bgstyle.Custom = GetFormat(rowData.revisedBudAmtYear.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    sheet.Cells[rowNum, colNum].Value = !string.IsNullOrEmpty(rowData.LineGroup) ? rowData.LineGroup.ToString() : string.Empty;
                    colNum++;
                    break;
                case "functionCode":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();
                        bgstyle.Custom = GetFormat(rowData.revisedBudAmtYear.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    sheet.Cells[rowNum, colNum].Value = !string.IsNullOrEmpty(rowData.functionCode) ? rowData.functionCode.ToString() : string.Empty;
                    colNum++;
                    break;
                case "accountCode":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();
                        bgstyle.Custom = GetFormat(rowData.revisedBudAmtYear.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    sheet.Cells[rowNum, colNum].Value = !string.IsNullOrEmpty(rowData.accountCode) ? rowData.accountCode.ToString() : string.Empty;
                    colNum++;
                    break;
                case "deptCode":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();
                        bgstyle.Custom = GetFormat(rowData.revisedBudAmtYear.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    sheet.Cells[rowNum, colNum].Value = !string.IsNullOrEmpty(rowData.deptCode) ? rowData.deptCode.ToString() : string.Empty;
                    colNum++;
                    break;
                case "projectCode":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();
                        bgstyle.Custom = GetFormat(rowData.revisedBudAmtYear.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    sheet.Cells[rowNum, colNum].Value = !string.IsNullOrEmpty(rowData.projectCode) ? rowData.projectCode.ToString() : string.Empty;
                    colNum++;
                    break;

                case "note":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();
                        bgstyle.Custom = GetFormat(rowData.revisedBudAmtYear.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    sheet.Cells[rowNum, colNum].Value = !string.IsNullOrEmpty(rowData.note) ? rowData.note.ToString() : string.Empty;
                    colNum++;
                    break;

                case "actualAmtYear":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.actualAmtYear.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.actualAmtYear.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.actualAmtYear;

                        bgstyle.Custom = GetFormat(rowData.actualAmtYear.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;

                case "revisedBudAmtYear":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();
                        bgstyle.Custom = GetFormat(rowData.revisedBudAmtYear.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.revisedBudAmtYear.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.revisedBudAmtYear;
                        bgstyle.Custom = GetFormat(rowData.revisedBudAmtYear.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;

                case "orgBudAmtYear":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();
                        bgstyle.Custom = GetFormat(rowData.orgBudAmtYear.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.orgBudAmtYear.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.orgBudAmtYear;
                        bgstyle.Custom = GetFormat(rowData.orgBudAmtYear.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;

                case "actualAmtLastYear":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.actualAmtLastYear.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.actualAmtLastYear.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.actualAmtLastYear;
                        bgstyle.Custom = GetFormat(rowData.actualAmtLastYear.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;

                case "orgBudAmtLastYear":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.orgBudAmtLastYear.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.orgBudAmtLastYear.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.orgBudAmtLastYear;
                        bgstyle.Custom = GetFormat(rowData.orgBudAmtLastYear.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;

                case "revisedAmtLastYear":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.revisedAmtLastYear.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.revisedAmtLastYear.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.revisedAmtLastYear;
                        bgstyle.Custom = GetFormat(rowData.revisedAmtLastYear.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;
                case "revisedAmtLastYearByAuth":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.revisedAmtLastYearByAuth.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.revisedAmtLastYearByAuth.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.revisedAmtLastYearByAuth;
                        bgstyle.Custom = GetFormat(rowData.revisedAmtLastYearByAuth.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;
                case "revisedAmtYearByAuth":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.revisedAmtYearByAuth.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.revisedAmtYearByAuth.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.revisedAmtYearByAuth;
                        bgstyle.Custom = GetFormat(rowData.revisedAmtYearByAuth.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;

                case "finPlanYear1":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.finPlanYear1.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.finPlanYear1.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.finPlanYear1;
                        bgstyle.Custom = GetFormat(rowData.finPlanYear1.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;

                case "finPlanYear2":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.finPlanYear2.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.finPlanYear2.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.finPlanYear2;
                        bgstyle.Custom = GetFormat(rowData.finPlanYear2.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;

                case "finPlanYear3":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.finPlanYear3.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.finPlanYear3.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.finPlanYear3;
                        bgstyle.Custom = GetFormat(rowData.finPlanYear3.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;

                case "finPlanYear4":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.finPlanYear4.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.finPlanYear4.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.finPlanYear4;
                        bgstyle.Custom = GetFormat(rowData.finPlanYear4.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;

                case "finPlanYear5":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.finPlanYear5.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.finPlanYear5.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.finPlanYear5;
                        bgstyle.Custom = GetFormat(rowData.finPlanYear5.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;

                case "finPlanYear6":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.finPlanYear6.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.finPlanYear6.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.finPlanYear6;
                        bgstyle.Custom = GetFormat(rowData.finPlanYear6.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;

                case "finPlanYear7":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.finPlanYear7.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.finPlanYear7.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.finPlanYear7;
                        bgstyle.Custom = GetFormat(rowData.finPlanYear7.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;

                case "finPlanYear8":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.finPlanYear8.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.finPlanYear8.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.finPlanYear8;
                        bgstyle.Custom = GetFormat(rowData.finPlanYear8.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;

                case "finPlanYear9":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.finPlanYear9.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.finPlanYear9.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.finPlanYear9;
                        bgstyle.Custom = GetFormat(rowData.finPlanYear9.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;

                case "finPlanYear10":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.finPlanYear10.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.finPlanYear10.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.finPlanYear10;
                        bgstyle.Custom = GetFormat(rowData.finPlanYear10.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;

                case "finPlanYear11":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.finPlanYear11.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.finPlanYear11.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.finPlanYear11;
                        bgstyle.Custom = GetFormat(rowData.finPlanYear11.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;

                case "finPlanYear12":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.finPlanYear12.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.finPlanYear12.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.finPlanYear12;
                        bgstyle.Custom = GetFormat(rowData.finPlanYear12.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;

                case "finPlanYear13":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.finPlanYear13.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.finPlanYear13.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.finPlanYear13;
                        bgstyle.Custom = GetFormat(rowData.finPlanYear13.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;

                case "finPlanYear14":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.finPlanYear14.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.finPlanYear14.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.finPlanYear14;
                        bgstyle.Custom = GetFormat(rowData.finPlanYear14.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;

                case "finPlanYear15":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.finPlanYear15.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.finPlanYear15.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.finPlanYear15;
                        bgstyle.Custom = GetFormat(rowData.finPlanYear15.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;

                case "finPlanYear16":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.finPlanYear16.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.finPlanYear16.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.finPlanYear16;
                        bgstyle.Custom = GetFormat(rowData.finPlanYear16.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;

                case "finPlanYear17":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.finPlanYear17.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.finPlanYear17.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.finPlanYear17;
                        bgstyle.Custom = GetFormat(rowData.finPlanYear17.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;

                case "finPlanYear18":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.finPlanYear18.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.finPlanYear18.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.finPlanYear18;
                        bgstyle.Custom = GetFormat(rowData.finPlanYear18.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;

                case "finPlanYear19":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.finPlanYear19.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.finPlanYear19.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.finPlanYear19;
                        bgstyle.Custom = GetFormat(rowData.finPlanYear19.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;

                case "finPlanYear20":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();

                        bgstyle.Custom = GetFormat(rowData.finPlanYear20.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.finPlanYear20.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.finPlanYear20;
                        bgstyle.Custom = GetFormat(rowData.finPlanYear20.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;

                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;
                case "polDeviation":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();
                        bgstyle.Custom = GetFormat(rowData.polDeviation.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.polDeviation.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;
                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.polDeviation;
                        bgstyle.Custom = GetFormat(rowData.polDeviation.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;
                case "deviation":
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();
                        bgstyle.Custom = GetFormat(rowData.deviation.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.deviation.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;
                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.deviation;
                        bgstyle.Custom = GetFormat(rowData.deviation.ToString(), ci, NumberTypes.Decimal);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;
                case "polDeviationPct":
                    string polDeviationPctValue = rowData.polDeviationPct == null ? "0" : rowData.polDeviationPct.Value.ToString("F2");
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();
                        bgstyle.Custom = GetFormat(polDeviationPctValue, ci, NumberTypes.Percentage);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.polDeviationPct.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;
                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.polDeviationPct;
                        bgstyle.Custom = GetFormat(polDeviationPctValue, ci, NumberTypes.Percentage);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;
                case "deviationPct":
                    string deviationPctValue = rowData.deviationPct == null ? "0" : rowData.deviationPct.Value.ToString("F2");
                    if (grayRow)// add grey color for sumary row
                    {
                        bgstyle.BackgroundColor = Color.Gainsboro;
                        bgstyle.BackgroundArgbColor = Color.Gainsboro.ToArgb();
                        bgstyle.Custom = GetFormat(deviationPctValue, ci, NumberTypes.Percentage);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells.Rows[rowNum].ApplyStyle(bgstyle, flag);
                    }
                    if (!rowData.deviationPct.HasValue)
                    {
                        Style cellStyle = wb.CreateStyle();
                        cellStyle.Font.Name = "Calibiri";
                        cellStyle.Font.Size = 11;
                        sheet.Cells[rowNum, colNum].Value = string.Empty;
                        sheet.Cells[rowNum, colNum].SetStyle(cellStyle);
                    }
                    else
                    {
                        sheet.Cells[rowNum, colNum].Value = (Double)rowData.deviationPct;
                        bgstyle.Custom = GetFormat(deviationPctValue, ci, NumberTypes.Percentage);
                        bgstyle.Font.Name = "Calibiri";
                        bgstyle.Font.Size = 11;
                        sheet.Cells[rowNum, colNum].SetStyle(bgstyle);
                    }
                    colNum++;
                    break;
                default:
                    //do nothing
                    break;
            }
        }
    }
}