using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using System.Text;

namespace Framsikt.BL;

public partial class BusinessPlan
{

    public string saveGoalOrStrategyOrTargets(string user, BPAssignmentSaveHelper assignmentData, Guid assignmentId, Guid uniqueAssignmentId)
    {
        return saveGoalOrStrategyOrTargetsAsync(user, assignmentData, assignmentId, uniqueAssignmentId).GetAwaiter().GetResult();
    }

    public async Task<string> saveGoalOrStrategyOrTargetsAsync(string user, BPAssignmentSaveHelper assignmentData, Guid assignmentId, Guid uniqueAssignmentId)
    {
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        UserData userDetails = await _pUtility.GetUserDetailsAsync(user);
        List<Guid> targetIdsForSelectedTargets = new List<Guid>();
        List<string> goalIdsForSelectedTargets = new List<string>();

        //Save target tags
        var existedTargetsData = await tenantDbContext.TbiAssignmentTarget.Where(x => x.fk_assignment_id == assignmentId && x.fk_tenant_id == userDetails.tenant_id && x.budget_year == assignmentData.budgetYear).ToListAsync();
        if (!assignmentData.updateOnlyGoal && existedTargetsData.Count() > 0)
        {
            tenantDbContext.TbiAssignmentTarget.RemoveRange(existedTargetsData);
            await tenantDbContext.SaveChangesAsync();
        }
        if (assignmentData.targets != null)
        {
            targetIdsForSelectedTargets = assignmentData.targets;
            foreach (var a in assignmentData.targets)
            {
                TbiAssignmentTarget newTargetData = new TbiAssignmentTarget();
                newTargetData.fk_assignment_id = assignmentId;
                newTargetData.fk_unique_assignment_id = uniqueAssignmentId;
                newTargetData.fk_target_id = a;
                newTargetData.fk_tenant_id = userDetails.tenant_id;
                newTargetData.budget_year = assignmentData.budgetYear;
                newTargetData.updated = DateTime.UtcNow;
                newTargetData.updated_by = userDetails.pk_id;

                await tenantDbContext.TbiAssignmentTarget.AddAsync(newTargetData);
            }
            await tenantDbContext.SaveChangesAsync();
        }

        //Save goal tags
        //Save goal ids(linked to target) for selected target if goal is not selected
        if ((assignmentData.goals == null || assignmentData.goals.Count == 0) && targetIdsForSelectedTargets.Count > 0)
        {
            goalIdsForSelectedTargets = await tenantDbContext.tco_targets.Where(x => x.fk_tenant_id == userDetails.tenant_id && targetIdsForSelectedTargets.Contains(x.pk_target_id)).Select(x => x.fk_goal_id.ToString()).Distinct().ToListAsync();
            assignmentData.goals = goalIdsForSelectedTargets;
        }

        var existedGoalsData = await tenantDbContext.TbiAssignmentGoal.Where(x => x.fk_assignment_id == assignmentId && x.fk_tenant_id == userDetails.tenant_id && x.budget_year == assignmentData.budgetYear).ToListAsync();
        if (existedGoalsData.Count() > 0)
        {
            tenantDbContext.TbiAssignmentGoal.RemoveRange(existedGoalsData);
            await tenantDbContext.SaveChangesAsync();
        }
        if (assignmentData.goals != null)
        {
            var goalInfo = assignmentData.isBPAssignment ? tenantDbContext.tco_goals.Where(x => x.fk_tenant_id == userDetails.tenant_id
                && x.budget_year == assignmentData.budgetYear).AsNoTracking().Distinct().ToList() : new List<tco_goals>();
            var goalDistrList = assignmentData.isBPAssignment ? await tenantDbContext.tco_goals_distribution.Where(x => x.fk_tenant_id == userDetails.tenant_id).AsNoTracking().Distinct().ToListAsync() :
                new List<tco_goals_distribution>();
            foreach (var a in assignmentData.goals)
            {
                string refGoalId = Guid.Empty.ToString();
                if (assignmentData.isBPAssignment) //saving goals for delegated assignments
                {
                    if (assignmentData.orgLevel == "1")
                    {
                        refGoalId = a;
                    }
                    else
                    {
                        var refGoalIdInfo = goalInfo.Where(x => x.pk_goal_id == Guid.Parse(a)).Select(y => y.pk_goal_id.ToString()).ToList();
                        if (refGoalIdInfo.Any())
                        {
                            var goalData = goalDistrList.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id && x.org_id == assignmentData.orgId && x.org_level.ToString() == assignmentData.orgLevel && refGoalIdInfo.Contains(x.fk_goal_id.ToString()));
                            refGoalId = goalData != null ? goalData.fk_goal_id.ToString() : Guid.Empty.ToString();
                        }
                    }
                }
                TbiAssignmentGoal newGoalData = new TbiAssignmentGoal();
                newGoalData.fk_assignment_id = assignmentId;
                newGoalData.fk_unique_assignment_id = uniqueAssignmentId;
                newGoalData.fk_goal_id = assignmentData.isBPAssignment ? Guid.Parse(refGoalId) : Guid.Parse(a);
                newGoalData.fk_tenant_id = userDetails.tenant_id;
                newGoalData.budget_year = assignmentData.budgetYear;
                newGoalData.updated = DateTime.UtcNow;
                newGoalData.updated_by = userDetails.pk_id;

                await tenantDbContext.TbiAssignmentGoal.AddAsync(newGoalData);
            }
            await tenantDbContext.SaveChangesAsync();
        }

        //Save Strategy tags

        var existedStrategiesData = await tenantDbContext.TbiAssignmentStrategy.Where(x => x.fk_assignment_id == assignmentId && x.fk_tenant_id == userDetails.tenant_id && x.budget_year == assignmentData.budgetYear).ToListAsync();
        if (!assignmentData.updateOnlyGoal && existedStrategiesData.Count() > 0)
        {
            tenantDbContext.TbiAssignmentStrategy.RemoveRange(existedStrategiesData);
            await tenantDbContext.SaveChangesAsync();
        }
        if (assignmentData.strategis != null)
        {
            foreach (var a in assignmentData.strategis)
            {
                TbiAssignmentStrategy newStrategyData = new TbiAssignmentStrategy();
                newStrategyData.fk_assignment_id = assignmentId;
                newStrategyData.fk_unique_assignment_id = uniqueAssignmentId;
                newStrategyData.fk_strategy_id = a;
                newStrategyData.fk_tenant_id = userDetails.tenant_id;
                newStrategyData.budget_year = assignmentData.budgetYear;
                newStrategyData.updated = DateTime.UtcNow;
                newStrategyData.updated_by = userDetails.pk_id;

                await tenantDbContext.TbiAssignmentStrategy.AddAsync(newStrategyData);
            }
            await tenantDbContext.SaveChangesAsync();
        }

        return "success";
    }

    public async Task<string> saveTaskConnectionAsync(string user, BPAssignmentSaveHelper assignmentData, Guid assignmentId,
        Guid uniqueAssignmentId, List<TbiAssignmentTarget> assignTarget, List<TbiAssignmentGoal> assignGoal, List<TbiAssignmentStrategy> assignStrategy)
    {
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        UserData userDetails = await _pUtility.GetUserDetailsAsync(user);
        List<Guid> targetIdsForSelectedTargets = new List<Guid>();
        List<string> goalIdsForSelectedTargets = new List<string>();

        //Save target tags
        var existedTargetsData = assignTarget.Where(x => x.fk_assignment_id == assignmentId).ToList();
        if (!assignmentData.updateOnlyGoal && existedTargetsData.Count() > 0)
        {
            tenantDbContext.TbiAssignmentTarget.RemoveRange(existedTargetsData);
            await tenantDbContext.SaveChangesAsync();
        }
        if (assignmentData.targets != null)
        {
            targetIdsForSelectedTargets = assignmentData.targets;
            foreach (var a in assignmentData.targets)
            {
                TbiAssignmentTarget newTargetData = new TbiAssignmentTarget();
                newTargetData.fk_assignment_id = assignmentId;
                newTargetData.fk_unique_assignment_id = uniqueAssignmentId;
                newTargetData.fk_target_id = a;
                newTargetData.fk_tenant_id = userDetails.tenant_id;
                newTargetData.budget_year = assignmentData.budgetYear;
                newTargetData.updated = DateTime.UtcNow;
                newTargetData.updated_by = userDetails.pk_id;

                await tenantDbContext.TbiAssignmentTarget.AddAsync(newTargetData);
            }
            await tenantDbContext.SaveChangesAsync();
        }

        //Save goal tags
        //Save goal ids(linked to target) for selected target if goal is not selected
        if ((assignmentData.goals == null || assignmentData.goals.Count == 0) && targetIdsForSelectedTargets.Count > 0)
        {
            goalIdsForSelectedTargets = await tenantDbContext.tco_targets.Where(x => x.fk_tenant_id == userDetails.tenant_id && targetIdsForSelectedTargets.Contains(x.pk_target_id)).Select(x => x.fk_goal_id.ToString()).Distinct().ToListAsync();
            assignmentData.goals = goalIdsForSelectedTargets;
        }

        var existedGoalsData = assignGoal.Where(x => x.fk_assignment_id == assignmentId).ToList();
        if (existedGoalsData.Count() > 0)
        {
            tenantDbContext.TbiAssignmentGoal.RemoveRange(existedGoalsData);
            await tenantDbContext.SaveChangesAsync();
        }
        if (assignmentData.goals != null)
        {
            var goalInfo = assignmentData.isBPAssignment ? tenantDbContext.tco_goals.Where(x => x.fk_tenant_id == userDetails.tenant_id
                && x.budget_year == assignmentData.budgetYear).AsNoTracking().Distinct().ToList() : new List<tco_goals>();
            var goalDistrList = assignmentData.isBPAssignment ? await tenantDbContext.tco_goals_distribution.Where(x => x.fk_tenant_id == userDetails.tenant_id).AsNoTracking().Distinct().ToListAsync() :
                new List<tco_goals_distribution>();
            foreach (var a in assignmentData.goals)
            {
                string refGoalId = Guid.Empty.ToString();
                if (assignmentData.isBPAssignment) //saving goals for delegated assignments
                {
                    if (assignmentData.orgLevel == "1")
                    {
                        refGoalId = a;
                    }
                    else
                    {
                        var refGoalIdInfo = goalInfo.Where(x => x.pk_goal_id == Guid.Parse(a)).Select(y => y.pk_goal_id.ToString()).ToList();
                        if (refGoalIdInfo.Any())
                        {
                            var goalData = goalDistrList.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id && x.org_id == assignmentData.orgId && x.org_level.ToString() == assignmentData.orgLevel && refGoalIdInfo.Contains(x.fk_goal_id.ToString()));
                            refGoalId = goalData != null ? goalData.fk_goal_id.ToString() : Guid.Empty.ToString();
                        }
                    }
                }
                TbiAssignmentGoal newGoalData = new TbiAssignmentGoal();
                newGoalData.fk_assignment_id = assignmentId;
                newGoalData.fk_unique_assignment_id = uniqueAssignmentId;
                newGoalData.fk_goal_id = assignmentData.isBPAssignment ? Guid.Parse(refGoalId) : Guid.Parse(a);
                newGoalData.fk_tenant_id = userDetails.tenant_id;
                newGoalData.budget_year = assignmentData.budgetYear;
                newGoalData.updated = DateTime.UtcNow;
                newGoalData.updated_by = userDetails.pk_id;

                await tenantDbContext.TbiAssignmentGoal.AddAsync(newGoalData);
            }
            await tenantDbContext.SaveChangesAsync();
        }

        //Save Strategy tags

        var existedStrategiesData = assignStrategy.Where(x => x.fk_assignment_id == assignmentId).ToList();
        if (!assignmentData.updateOnlyGoal && existedStrategiesData.Count() > 0)
        {
            tenantDbContext.TbiAssignmentStrategy.RemoveRange(existedStrategiesData);
            await tenantDbContext.SaveChangesAsync();
        }
        if (assignmentData.strategis != null)
        {
            foreach (var a in assignmentData.strategis)
            {
                TbiAssignmentStrategy newStrategyData = new TbiAssignmentStrategy();
                newStrategyData.fk_assignment_id = assignmentId;
                newStrategyData.fk_unique_assignment_id = uniqueAssignmentId;
                newStrategyData.fk_strategy_id = a;
                newStrategyData.fk_tenant_id = userDetails.tenant_id;
                newStrategyData.budget_year = assignmentData.budgetYear;
                newStrategyData.updated = DateTime.UtcNow;
                newStrategyData.updated_by = userDetails.pk_id;

                await tenantDbContext.TbiAssignmentStrategy.AddAsync(newStrategyData);
            }
            await tenantDbContext.SaveChangesAsync();
        }

        return "success";
    }

    public JObject GetDataForStrategyDropdown(string userId, int budgetYear, bool isBudgetProposal)
    {
        return GetDataForStrategyDropdownAsync(userId, budgetYear, isBudgetProposal).GetAwaiter().GetResult();
    }

    public async Task<JObject> GetDataForStrategyDropdownAsync(string userId, int budgetYear, bool isBudgetProposal)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext dbContext = await _pUtility.GetTenantDBContextAsync();

        var strategyData = (isBudgetProposal) ? await (from s in dbContext.tfp_strategy_text
                where s.fk_tenant_id == userDetails.tenant_id && s.master_strategy_id == null &&
                      s.budget_year == budgetYear && !s.is_busplan
                select new
                {
                    key = s.pk_strategy_id,
                    value = s.strategy_name
                }).OrderBy(x => x.value).Distinct().AsNoTracking().ToListAsync()
            :
            await (from s in dbContext.tfp_strategy_text
                where s.fk_tenant_id == userDetails.tenant_id && s.master_strategy_id == null &&
                      s.budget_year == budgetYear
                select new
                {
                    key = s.pk_strategy_id,
                    value = s.strategy_name
                }).OrderBy(x => x.value).Distinct().AsNoTracking().ToListAsync();

        dynamic strategyObj = new JObject();
        var strategyArray = new JArray();
        foreach (var item in strategyData)
        {
            dynamic row = new JObject();
            row.KeyId = item.key;
            row.ValueString = item.value;
            strategyArray.Add(row);
        }

        strategyObj.strategyList = strategyArray;
        strategyObj.strategyGoalList = JArray.FromObject(await GetStrategyListAsync(userId, budgetYear, isBudgetProposal));
        return strategyObj;
    }

    public List<KeyValueString> GetStrategyList(string userId, int budgetYear, bool isBudgetProposal)
    {
        return GetStrategyListAsync(userId, budgetYear, isBudgetProposal).GetAwaiter().GetResult();
    }

    public async Task<List<KeyValueString>> GetStrategyListAsync(string userId, int budgetYear, bool isBudgetProposal)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext dbContext = await _pUtility.GetTenantDBContextAsync();

        var data = (isBudgetProposal) ? await (from st in dbContext.tfp_strategy_text
                join sg in dbContext.tfp_strategy_goal on new { a = st.fk_tenant_id, b = st.pk_strategy_id }
                    equals new { a = sg.fk_tenant_id, b = sg.fk_strategy_id } into grp
                from sg1 in grp.DefaultIfEmpty()
                join tg in dbContext.tco_goals on new { a = sg1.fk_tenant_id, b = sg1.fk_goal_id }
                    equals new { a = tg.fk_tenant_id, b = tg.pk_goal_id } into g
                from tg1 in g.DefaultIfEmpty()
                where !st.is_busplan && st.budget_year == budgetYear && st.fk_tenant_id == userDetails.tenant_id && st.master_strategy_id == null
                select new
                {
                    KeyId = st.pk_strategy_id,
                    goalName = (sg1 == null && tg1 == null ? string.Empty : (!string.IsNullOrEmpty(tg1.goal_name) ? tg1.goal_name : string.Empty)),
                    strategyName = st.strategy_name,
                    ValueString = (sg1 == null && tg1 == null ? st.strategy_name : (st.strategy_name + " " + (!string.IsNullOrEmpty(tg1.goal_name) ? "(" + tg1.goal_name + ")" : string.Empty)))
                }).Distinct().OrderBy(x => x.goalName).ThenBy(y => y.strategyName).ToListAsync()
            : await (from st in dbContext.tfp_strategy_text
                join sg in dbContext.tfp_strategy_goal on new { a = st.fk_tenant_id, b = st.pk_strategy_id }
                    equals new { a = sg.fk_tenant_id, b = sg.fk_strategy_id } into grp
                from sg1 in grp.DefaultIfEmpty()
                join tg in dbContext.tco_goals on new { a = sg1.fk_tenant_id, b = sg1.fk_goal_id }
                    equals new { a = tg.fk_tenant_id, b = tg.pk_goal_id } into g
                from tg1 in g.DefaultIfEmpty()
                where st.budget_year == budgetYear && st.fk_tenant_id == userDetails.tenant_id && st.master_strategy_id == null
                select new
                {
                    KeyId = st.pk_strategy_id,
                    goalName = (sg1 == null && tg1 == null ? string.Empty : (!string.IsNullOrEmpty(tg1.goal_name) ? tg1.goal_name : string.Empty)),
                    strategyName = st.strategy_name,
                    ValueString = (sg1 == null && tg1 == null ? st.strategy_name : (st.strategy_name + " " + (!string.IsNullOrEmpty(tg1.goal_name) ? "(" + tg1.goal_name + ")" : string.Empty)))
                }).Distinct().OrderBy(x => x.goalName).ThenBy(y => y.strategyName).ToListAsync();
        var strategyData = (from a in data
            select new KeyValueString
            {
                KeyId = a.KeyId,
                ValueString = a.ValueString
            }).ToList();
        return strategyData;
    }

    //#endregion Checklist Section for Business Plan

    public JObject GetTargetsByOrgOrService(string userId, string orgId, string serviceId,
        bool isBusinessPlanTarget, int budgetYear, TargetDropdownHelper data, string chapterId = "")
    {
        var result =
            GetTargetsByOrgOrServiceAsync(userId, orgId, serviceId, isBusinessPlanTarget, budgetYear, data,
                chapterId).GetAwaiter().GetResult();
        return result;
    }

    public async Task<JObject> GetTargetsByOrgOrServiceAsync(string userId, string orgId, string serviceId, bool isBusinessPlanTarget, int budgetYear, TargetDropdownHelper data, string chapterId = "")
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext dbContext = await _pUtility.GetTenantDbContextForParallelReadAsync();
        StringBuilder sb = new StringBuilder();
        sb.AppendLine($"busplan get targetgoal @ {DateTime.UtcNow}");
        int OrgLevelforCityLevel = 1;
        bool isTenantHaServiceIdSetup = false;
        List<string> emptyStringArr = new List<string>();
        List<Guid> emptyGuidArr = new List<Guid>();
        var targetUseGoalsParam = (await _pUtility.GetParameterValueAsync(userId, "TARGET_USE_UNGOALS")).ToLower() == "true" ? true : false;
        data.selectedGoalId = data == null || data.selectedGoalId == null ? emptyStringArr : data.selectedGoalId;
        data.selectedTargetId = data == null || data.selectedTargetId == null ? emptyGuidArr : data.selectedTargetId;

        var orgVersionContent = await _pUtility.GetOrgVersionSpecificContentAsync(userId, _pUtility.GetForecastPeriod(budgetYear, 1));
        var cityLevel = orgVersionContent.lstOrgHierarchy.FirstOrDefault();
        bool isCityLevelAsDefault = string.IsNullOrEmpty(orgId);
        orgId = string.IsNullOrEmpty(orgId) ? cityLevel.org_id_1 : orgId;

        data.selectedGoalId = data.selectedGoalId.Select(x => x.ToLower()).ToList();

        string configuredServiceID = await dbContext.tco_parameters.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.param_name == "FINPLAN_LEVEL_2").Select(x => x.param_value).FirstOrDefaultAsync();
        if (!isCityLevelAsDefault && !string.IsNullOrEmpty(configuredServiceID))
        {
            isTenantHaServiceIdSetup = configuredServiceID.StartsWith("service");
            if (isTenantHaServiceIdSetup && string.IsNullOrEmpty(serviceId))
            {
                serviceId = "ALL";
            }
            //For lower level org ids - tenants who doesnt have service id setup may send lower org id as service id value.
            else if (!isTenantHaServiceIdSetup && !string.IsNullOrEmpty(serviceId))
            {
                orgId = serviceId.ToLower() == "ALL".ToLower() ? orgId : serviceId;
                serviceId = string.Empty;
            }
        }
        serviceId = string.IsNullOrEmpty(serviceId) || serviceId == "null" ? string.Empty : serviceId;

        List<TargetDropdownHelper> targetData = new List<TargetDropdownHelper>();
        List<StrategyDropdownHelper> strategyData = new List<StrategyDropdownHelper>();

        sb.AppendLine($"1 ->" + DateTime.UtcNow);
        List<GoalnDistrFth> goalDistList = await _budPropUtil.FetchGoalDistrAsync(userId, budgetYear, null);
        sb.AppendLine($"2 ->" + DateTime.UtcNow);
        List<TargetnDistrFth> tgtDistList = await _budPropUtil.FetchTargetDistrAsync(userId, budgetYear, null);
        sb.AppendLine($"3 ->" + DateTime.UtcNow);
        goalDistList = await LinkPlanFocusAreaDataAsync(userId, budgetYear, goalDistList);
        sb.AppendLine($"4 ->" + DateTime.UtcNow);

        var stratList = isBusinessPlanTarget ? await (from st in dbContext.tfp_strategy_text
                join tsg in dbContext.tfp_strategy_goal on new { a = st.fk_tenant_id, b = st.pk_strategy_id }
                    equals new { a = tsg.fk_tenant_id, b = tsg.fk_strategy_id } into stra
                from res in stra.DefaultIfEmpty()
                join tst in dbContext.tfp_strategy_target on new { a = st.fk_tenant_id, b = st.pk_strategy_id }
                    equals new { a = tst.fk_tenant_id, b = tst.fk_strategy_id } into tstra
                from tres in tstra.DefaultIfEmpty()
                where st.fk_tenant_id == userDetails.tenant_id && st.budget_year == budgetYear && st.org_id == orgId
                group new { st, res, tres } by new
                {
                    st.strategy_name,
                    st.pk_strategy_id,
                    st.strategy_desc,
                    st.org_id,
                    st.org_level,
                    st.service_id,
                    st.fk_attribute_id,
                    goalId = res == null ? Guid.Empty : res.fk_goal_id,
                    targetId = tres == null ? Guid.Empty : tres.fk_target_id
                } into g
                select new
                {
                    strategy_name = g.Key.strategy_name,
                    goal_id = g.Key.goalId,
                    target_id = g.Key.targetId,
                    strategy_id = g.Key.pk_strategy_id,
                    strategyDesc = g.Key.strategy_desc,
                    orgid = g.Key.org_id,
                    orgLevel = g.Key.org_level,
                    serviceId = g.Key.service_id,
                    attributeId = g.Key.fk_attribute_id
                }).AsNoTracking().ToListAsync()
            :
            await (from st in dbContext.tfp_strategy_text
                join tsg in dbContext.tfp_strategy_goal on new { a = st.fk_tenant_id, b = st.pk_strategy_id }
                    equals new { a = tsg.fk_tenant_id, b = tsg.fk_strategy_id } into stra
                from res in stra.DefaultIfEmpty()
                join tst in dbContext.tfp_strategy_target on new { a = st.fk_tenant_id, b = st.pk_strategy_id }
                    equals new { a = tst.fk_tenant_id, b = tst.fk_strategy_id } into tstra
                from tres in tstra.DefaultIfEmpty()
                where st.fk_tenant_id == userDetails.tenant_id && st.budget_year == budgetYear && !st.is_busplan && st.org_id == orgId
                group new { st, res, tres } by new
                {
                    st.strategy_name,
                    st.pk_strategy_id,
                    st.strategy_desc,
                    st.org_id,
                    st.org_level,
                    st.service_id,
                    st.fk_attribute_id,
                    goalId = res == null ? Guid.Empty : res.fk_goal_id,
                    targetId = tres == null ? Guid.Empty : tres.fk_target_id
                } into g
                select new
                {
                    strategy_name = g.Key.strategy_name,
                    goal_id = g.Key.goalId,
                    target_id = g.Key.targetId,
                    strategy_id = g.Key.pk_strategy_id,
                    strategyDesc = g.Key.strategy_desc,
                    orgid = g.Key.org_id,
                    orgLevel = g.Key.org_level,
                    serviceId = g.Key.service_id,
                    attributeId = g.Key.fk_attribute_id
                }).AsNoTracking().ToListAsync();

        bool isChapterSetup = await _finUtility.isChapterSetup(userId);

        if (isChapterSetup)
        {
            var t1 = (from strat in stratList
                join gb in goalDistList on strat.goal_id equals gb.goalId into gl
                from res in gl.DefaultIfEmpty()
                where strat.orgid == orgId && strat.attributeId == chapterId
                group new { strat, res } by new
                {
                    strat.strategy_id,
                    strat.strategy_name,
                    strat.strategyDesc,
                    goalId = res == null ? Guid.Empty : res.goalId,
                    goalName = res == null ? string.Empty : res.goal_name,
                    targetId = Guid.Empty
                } into g
                select new StrategyDropdownHelper
                {
                    strategyId = g.Key.strategy_id,
                    strategy = g.Key.strategy_name,
                    strategyDesc = g.Key.strategyDesc,
                    fk_goal_id = g.Key.goalId,
                    goalDescription = g.Key.goalName,
                    fk_target_id = Guid.Empty
                }).OrderBy(x => x.strategy).ToList();

            var t2 = (from strat in stratList
                join tt in tgtDistList on strat.target_id equals tt.targetId into tgt
                from tres in tgt.DefaultIfEmpty()
                where strat.orgid == orgId && strat.attributeId == chapterId
                group new { strat, tres } by new
                {
                    strat.strategy_id,
                    strat.strategy_name,
                    strat.strategyDesc,
                    targetId = tres == null ? Guid.Empty : tres.targetId
                } into g
                select new StrategyDropdownHelper
                {
                    strategyId = g.Key.strategy_id,
                    strategy = g.Key.strategy_name,
                    strategyDesc = g.Key.strategyDesc,
                    fk_goal_id = Guid.Empty,
                    goalDescription = string.Empty,
                    fk_target_id = g.Key.targetId,
                }).OrderBy(x => x.strategy).ToList();

            strategyData = t1.UnionBy(t2, x => x.strategyId).ToList();
        }
        else if (isCityLevelAsDefault)
        {
            strategyData = (from strat in stratList
                join gb in goalDistList on strat.goal_id equals gb.goalId into gl
                from res in gl.DefaultIfEmpty()
                join tt in tgtDistList on strat.target_id equals tt.targetId into tgt
                from tres in tgt.DefaultIfEmpty()
                where strat.orgLevel == OrgLevelforCityLevel
                group new { strat, res, tres } by new
                {
                    strat.strategy_id,
                    strat.strategy_name,
                    strat.strategyDesc,
                    goalId = res == null ? Guid.Empty : res.goalId,
                    goalName = res == null ? string.Empty : res.goal_name,
                    targetId = tres == null ? Guid.Empty : tres.targetId
                } into g
                select new StrategyDropdownHelper
                {
                    strategyId = g.Key.strategy_id,
                    strategy = g.Key.strategy_name,
                    strategyDesc = g.Key.strategyDesc,
                    fk_goal_id = g.Key.goalId,
                    goalDescription = g.Key.goalName,
                    fk_target_id = g.Key.targetId,
                }).OrderBy(x => x.strategy).ToList();
        }
        else
        {
            if (string.IsNullOrEmpty(serviceId) || (!string.IsNullOrEmpty(serviceId) && (serviceId == "-1" || serviceId == "ALL")))
            {
                var t1 = (from strat in stratList
                    join gb in goalDistList on strat.goal_id equals gb.goalId into gl
                    from res in gl.DefaultIfEmpty()
                    where strat.orgid == orgId &&
                          string.IsNullOrEmpty(strat.serviceId) || strat.serviceId.Trim() == string.Empty ||
                          (!string.IsNullOrEmpty(strat.serviceId) && (strat.serviceId == "-1" || strat.serviceId.ToUpper() == "ALL"))
                    group new { strat, res } by new
                    {
                        strat.strategy_id,
                        strat.strategy_name,
                        strat.strategyDesc,
                        goalId = res == null ? Guid.Empty : res.goalId,
                        goalName = res == null ? string.Empty : res.goal_name
                    } into g
                    select new StrategyDropdownHelper
                    {
                        strategyId = g.Key.strategy_id,
                        strategy = g.Key.strategy_name,
                        strategyDesc = g.Key.strategyDesc,
                        fk_goal_id = g.Key.goalId,
                        goalDescription = g.Key.goalName,
                        fk_target_id = Guid.Empty
                    }).OrderBy(x => x.strategy).ToList();

                var t2 = (from strat in stratList
                    join tt in tgtDistList on strat.target_id equals tt.targetId into tgt
                    from tres in tgt.DefaultIfEmpty()
                    where strat.orgid == orgId &&
                          string.IsNullOrEmpty(strat.serviceId) || strat.serviceId.Trim() == string.Empty ||
                          (!string.IsNullOrEmpty(strat.serviceId) && (strat.serviceId == "-1" || strat.serviceId.ToUpper() == "ALL"))
                    group new { strat, tres } by new
                    {
                        strat.strategy_id,
                        strat.strategy_name,
                        strat.strategyDesc,
                        targetId = tres == null ? Guid.Empty : tres.targetId
                    } into g
                    select new StrategyDropdownHelper
                    {
                        strategyId = g.Key.strategy_id,
                        strategy = g.Key.strategy_name,
                        strategyDesc = g.Key.strategyDesc,
                        fk_goal_id = Guid.Empty,
                        goalDescription = string.Empty,
                        fk_target_id = g.Key.targetId,
                    }).OrderBy(x => x.strategy).ToList();

                strategyData = t1.UnionBy(t2, x => x.strategyId).ToList();
            }
            else
            {
                var t1 = (from strat in stratList
                    join gb in goalDistList on strat.goal_id equals gb.goalId into gl
                    from res in gl.DefaultIfEmpty()
                    where strat.orgid == orgId && strat.serviceId == serviceId
                    group new { strat, res } by new
                    {
                        strat.strategy_id,
                        strat.strategy_name,
                        strat.strategyDesc,
                        goalId = res == null ? Guid.Empty : res.goalId,
                        goalName = res == null ? string.Empty : res.goal_name,
                        targetId = Guid.Empty
                    } into g
                    select new StrategyDropdownHelper
                    {
                        strategyId = g.Key.strategy_id,
                        strategy = g.Key.strategy_name,
                        strategyDesc = g.Key.strategyDesc,
                        fk_goal_id = g.Key.goalId,
                        goalDescription = g.Key.goalName,
                        fk_target_id = Guid.Empty
                    }).OrderBy(x => x.strategy).ToList();

                var t2 = (from strat in stratList
                    join tt in tgtDistList on strat.target_id equals tt.targetId into tgt
                    from tres in tgt.DefaultIfEmpty()
                    where strat.orgid == orgId && strat.serviceId == serviceId
                    group new { strat, tres } by new
                    {
                        strat.strategy_id,
                        strat.strategy_name,
                        strat.strategyDesc,
                        targetId = tres == null ? Guid.Empty : tres.targetId
                    } into g
                    select new StrategyDropdownHelper
                    {
                        strategyId = g.Key.strategy_id,
                        strategy = g.Key.strategy_name,
                        strategyDesc = g.Key.strategyDesc,
                        fk_goal_id = Guid.Empty,
                        goalDescription = string.Empty,
                        fk_target_id = g.Key.targetId,
                    }).OrderBy(x => x.strategy).ToList();

                strategyData = t1.UnionBy(t2, x => x.strategyId).ToList();
            }
        }

        if (!isBusinessPlanTarget)
        {
            strategyData = strategyData.GroupBy(p => new { p.strategy, p.strategyDesc, p.strategyId }).Select(g => g.FirstOrDefault()).ToList();
        }
        else
        {
            strategyData = strategyData.GroupBy(p => new { p.strategy, p.strategyDesc }).Select(g => g.FirstOrDefault()).ToList();
        }
        if (isChapterSetup)
        {
            targetData = (from at in tgtDistList
                join gb in goalDistList on new { g = at.goalId, oid = at.org_id, ol = at.org_level, sv = at.service_id }
                    equals new { g = gb.goalId, oid = gb.org_id, ol = gb.org_level, sv = gb.service_id }
                where at.org_id == orgId && at.attribute_id == chapterId
                group new { at, gb } by new { at.targetId, at.target_name, at.goalId, gb.goal_name, at.is_busplan_target, at.unsdgoal_target, gb.focus_area, gb.unsd_goals } into g
                select new TargetDropdownHelper
                {
                    key = g.Key.targetId,
                    value = string.IsNullOrEmpty(g.Key.target_name) ? string.Empty : g.Key.target_name,
                    fk_goal_id = g.Key.goalId,
                    goalDescription = g.Key.goal_name,
                    isBusinessPlanTarget = g.Key.is_busplan_target,
                    unsdGoals = g.Key.unsd_goals,
                    focusAreaIds = g.Key.focus_area,
                    unsdGoalForTargets = g.Key.unsdgoal_target
                }).OrderBy(x => x.value).ToList();
        }
        else if (isCityLevelAsDefault)
        {
            targetData = (from at in tgtDistList
                join gb in goalDistList on new { g = at.goalId, oid = at.org_id, ol = at.org_level, sv = at.service_id }
                    equals new { g = gb.goalId, oid = gb.org_id, ol = gb.org_level, sv = gb.service_id }
                where at.org_level == OrgLevelforCityLevel
                group new { at, gb } by new { at.targetId, at.target_name, at.goalId, at.unsdgoal_target, gb.goal_name, at.is_busplan_target, gb.focus_area, gb.unsd_goals } into g
                select new TargetDropdownHelper
                {
                    key = g.Key.targetId,
                    value = string.IsNullOrEmpty(g.Key.target_name) ? string.Empty : g.Key.target_name,
                    fk_goal_id = g.Key.goalId,
                    goalDescription = g.Key.goal_name,
                    isBusinessPlanTarget = g.Key.is_busplan_target,
                    unsdGoals = g.Key.unsd_goals,
                    focusAreaIds = g.Key.focus_area,
                    unsdGoalForTargets = g.Key.unsdgoal_target
                }).OrderBy(x => x.value).ToList();
        }
        else if (serviceId == "-1" || serviceId == "ALL" || string.IsNullOrEmpty(serviceId))
        {
            targetData = (from at in tgtDistList
                join gb in goalDistList on new { g = at.goalId, oid = at.org_id, ol = at.org_level, sv = at.service_id }
                    equals new { g = gb.goalId, oid = gb.org_id, ol = gb.org_level, sv = gb.service_id }
                where at.org_id == orgId
                group new { at, gb } by new { at.targetId, at.target_name, at.goalId, at.unsdgoal_target, gb.goal_name, at.is_busplan_target, gb.focus_area, gb.unsd_goals } into g
                select new TargetDropdownHelper
                {
                    key = g.Key.targetId,
                    value = string.IsNullOrEmpty(g.Key.target_name) ? string.Empty : g.Key.target_name,
                    fk_goal_id = g.Key.goalId,
                    goalDescription = g.Key.goal_name,
                    isBusinessPlanTarget = g.Key.is_busplan_target,
                    unsdGoals = g.Key.unsd_goals,
                    focusAreaIds = g.Key.focus_area,
                    unsdGoalForTargets = g.Key.unsdgoal_target
                }).OrderBy(x => x.value).ToList();
        }
        else
        {
            targetData = (from at in tgtDistList
                join gb in goalDistList on new { g = at.goalId, oid = at.org_id, ol = at.org_level, sv = at.service_id }
                    equals new { g = gb.goalId, oid = gb.org_id, ol = gb.org_level, sv = gb.service_id }
                where at.org_id == orgId && at.service_id == serviceId
                group new { at, gb } by new { at.targetId, at.target_name, at.goalId, gb.goal_name, at.is_busplan_target, at.unsdgoal_target, gb.focus_area, gb.unsd_goals } into g
                select new TargetDropdownHelper
                {
                    key = g.Key.targetId,
                    value = string.IsNullOrEmpty(g.Key.target_name) ? string.Empty : g.Key.target_name,
                    fk_goal_id = g.Key.goalId,
                    goalDescription = g.Key.goal_name,
                    isBusinessPlanTarget = g.Key.is_busplan_target,
                    unsdGoals = g.Key.unsd_goals,
                    focusAreaIds = g.Key.focus_area,
                    unsdGoalForTargets = g.Key.unsdgoal_target
                }).OrderBy(x => x.value).ToList();
        }

        string unsDef = string.Empty;
        string faDef = string.Empty;
        string unTargetDef = string.Empty;
        List<string> unGoalList = new List<string>();
        List<string> unTargetList = new List<string>();
        List<string> unStratList = new List<string>();

        if (!isBusinessPlanTarget)
        {
            targetData = targetData.Where(x => x.isBusinessPlanTarget == false).ToList();
        }

        List<TargetDropdownHelper> selectedTargets = new List<TargetDropdownHelper>();
        List<string> unsInfoFromData = new List<string>();
        List<string> focusAreaInfoFromData = new List<string>();
        List<string> unTargetInfoFromData = new List<string>();
        //Display focus area and uns goals related to goal
        if (data.selectedGoalId.Count > 0 || data.selectedTargetId.Count > 0)
        {
            if (!targetUseGoalsParam)
            {
                if (data.selectedGoalId.Count > 0)
                {
                    var infoFromGoals = await (from a in dbContext.tco_goals
                        where a.fk_tenant_id == userDetails.tenant_id && data.selectedGoalId.Contains(a.pk_goal_id.ToString())
                        select new
                        {
                            unsdGoals = a.unsd_goals,
                            focusAreaIds = a.focus_area
                        }).Distinct().AsNoTracking().ToListAsync();
                    var planproposalFocusArea = await GetMappedFocusAreaDataAsync(userId, budgetYear);
                    infoFromGoals = (from d in infoFromGoals
                        join e in planproposalFocusArea on d.focusAreaIds equals e.PlanFocusAreaId.ToString() into g3
                        from x2 in g3.DefaultIfEmpty()
                        select new
                        {
                            unsdGoals = d.unsdGoals,
                            focusAreaIds = x2 != null ? x2.FinPlanFocusAreaId.ToString() : d.focusAreaIds,
                        }).ToList();
                    unsInfoFromData = infoFromGoals.Select(x => x.unsdGoals).ToList();
                    focusAreaInfoFromData = infoFromGoals.Select(x => x.focusAreaIds).ToList();
                    if (data.selectedGoalId.Count > 0)
                    {
                        targetData = targetData.Where(x => data.selectedGoalId.Contains(x.fk_goal_id.ToString().ToLower())).ToList();
                    }
                    var targetIdList = data.selectedTargetId.Count > 0 ? targetData.Where(x => data.selectedTargetId.Contains(x.key)).Select(x => x.key).ToList() : targetData.Select(x => x.key).ToList();
                    var infoFromTargets = await (from a in dbContext.tco_targets
                        where a.fk_tenant_id == userDetails.tenant_id && targetIdList.Contains(a.pk_target_id) && data.selectedTargetId.Contains(a.pk_target_id)
                        select new
                        {
                            unsdTargets = a.unsd_target
                        }).Distinct().AsNoTracking().ToListAsync();

                    unTargetInfoFromData = data.selectedTargetId.Count() > 0 ? infoFromTargets.Select(x => x.unsdTargets).ToList() : new List<string>();
                    strategyData = strategyData.Where(x => data.selectedGoalId.Contains(x.fk_goal_id.ToString()) || data.selectedTargetId.Contains(x.fk_target_id)).ToList();
                }
                //If user did'nt select any goals and selects only target, select focus are and uns goals related to target(based on goal id which target is connected to)
                else
                {
                    var infoFromTargets = await (from a in dbContext.tco_targets
                        where a.fk_tenant_id == userDetails.tenant_id && data.selectedTargetId.Contains(a.pk_target_id)
                        select new
                        {
                            unsdTargets = a.unsd_target
                        }).Distinct().AsNoTracking().ToListAsync();

                    unTargetInfoFromData = infoFromTargets.Select(x => x.unsdTargets).ToList();
                    unsInfoFromData = targetData.Where(x => data.selectedTargetId.Contains(x.key)).Select(x => x.unsdGoals).ToList();
                    focusAreaInfoFromData = targetData.Where(x => data.selectedTargetId.Contains(x.key)).Select(x => x.focusAreaIds).ToList();

                    strategyData = strategyData.Where(x => data.selectedTargetId.Contains(x.fk_target_id)).ToList();

                    //98961
                    var multiSelect = _pUtility.GetParameterValueAndActiveStatus(userId, "IS_MULTI_SELECT_GOALS", out int active);
                    if (!(multiSelect != null && active == 1 && multiSelect.ToLower() == "true"))
                    {
                        selectedTargets = targetData.Where(x => data.selectedTargetId.Contains(x.key)).ToList();

                        var targetGoalId = targetData.Where(x => data.selectedTargetId.Contains(x.key)).Select(t => t.fk_goal_id).FirstOrDefault();
                        targetData = targetData.Where(x => x.fk_goal_id == targetGoalId).ToList();
                    }
                }
            }
            else
            {
                if (data.selectedGoalId.Count > 0)
                {
                    var infoFromGoals = await (from a in dbContext.tco_goals
                        where a.fk_tenant_id == userDetails.tenant_id && data.selectedGoalId.Contains(a.pk_goal_id.ToString())
                        select new
                        {
                            unsdGoals = a.unsd_goals,
                            focusAreaIds = a.focus_area
                        }).Distinct().AsNoTracking().ToListAsync();
                    var planproposalFocusArea = await GetMappedFocusAreaDataAsync(userId, budgetYear);
                    infoFromGoals = (from d in infoFromGoals
                        join e in planproposalFocusArea on d.focusAreaIds equals e.PlanFocusAreaId.ToString() into g3
                        from x2 in g3.DefaultIfEmpty()
                        select new
                        {
                            unsdGoals = d.unsdGoals,
                            focusAreaIds = x2 != null ? x2.FinPlanFocusAreaId.ToString() : d.focusAreaIds,
                        }).ToList();
                    focusAreaInfoFromData = infoFromGoals.Select(x => x.focusAreaIds).ToList();
                    if (data.selectedGoalId.Count > 0)
                    {
                        targetData = targetData.Where(x => data.selectedGoalId.Contains(x.fk_goal_id.ToString().ToLower())).ToList();
                    }
                    var targetIdList = data.selectedTargetId.Count > 0 ? targetData.Where(x => data.selectedTargetId.Contains(x.key)).Select(x => x.key).ToList() : targetData.Select(x => x.key).ToList();
                    var infoFromTargets = await (from a in dbContext.tco_targets
                        where a.fk_tenant_id == userDetails.tenant_id && data.selectedTargetId.Contains(a.pk_target_id)
                        select new
                        {
                            unsdGoalForTarget = a.unsd_goal
                        }).Distinct().AsNoTracking().ToListAsync();
                    var unsdGoalForTargetList = infoFromTargets.Select(x => x.unsdGoalForTarget).ToList();
                    if (data.isInvesPage)
                    {
                        unsInfoFromData = data.selectedTargetId.Count == 0 ? infoFromGoals.Select(x => x.unsdGoals).ToList() : unsdGoalForTargetList.Where(x => !string.IsNullOrEmpty(x)).Count() != 0 ? unsdGoalForTargetList : new List<string>();
                    }
                    else
                    {
                        unsInfoFromData = unsdGoalForTargetList.Where(x => !string.IsNullOrEmpty(x)).Count() != 0 && data.selectedTargetId.Count > 0 ? unsdGoalForTargetList : infoFromGoals.Select(x => x.unsdGoals).ToList();
                    }
                    strategyData = strategyData.Where(x => data.selectedGoalId.Contains(x.fk_goal_id.ToString()) || data.selectedTargetId.Contains(x.fk_target_id)).ToList();
                    selectedTargets = targetData.Where(x => data.selectedTargetId.Contains(x.key)).ToList();
                }
                //If user did'nt select any goals and selects only target, select focus are and uns goals related to target(based on goal id which target is connected to)
                else
                {
                    var infoFromTargets = await (from a in dbContext.tco_targets
                        where a.fk_tenant_id == userDetails.tenant_id && data.selectedTargetId.Contains(a.pk_target_id)
                        select new
                        {
                            unsdGoalsForTarget = a.unsd_goal
                        }).Distinct().AsNoTracking().ToListAsync();

                    var unsdGoalsForTagetList = infoFromTargets.Select(x => x.unsdGoalsForTarget).ToList();
                    unsInfoFromData = unsdGoalsForTagetList.Where(x => !string.IsNullOrEmpty(x)).Count() > 0 && data.selectedTargetId.Count > 0 ? unsdGoalsForTagetList : targetData.Where(x => data.selectedTargetId.Contains(x.key)).Select(x => x.unsdGoals).ToList();
                    focusAreaInfoFromData = targetData.Where(x => data.selectedTargetId.Contains(x.key)).Select(x => x.focusAreaIds).ToList();

                    strategyData = strategyData.Where(x => data.selectedTargetId.Contains(x.fk_target_id)).ToList();

                    //98961
                    var (multiSelect, active) = await _pUtility.GetParameterValueAndActiveStatusAsync(userId, "IS_MULTI_SELECT_GOALS");
                    if (!(multiSelect != null && active == 1 && multiSelect.ToLower() == "true"))
                    {
                        selectedTargets = targetData.Where(x => data.selectedTargetId.Contains(x.key)).ToList();

                        var targetGoalId = targetData.Where(x => data.selectedTargetId.Contains(x.key)).Select(t => t.fk_goal_id).FirstOrDefault();
                        targetData = targetData.Where(x => x.fk_goal_id == targetGoalId).ToList();
                    }
                }
            }

            List<string> unTargetListFromData = new List<string>();
            List<string> unsListFromData = new List<string>();
            List<string> focusAreaListFromData = new List<string>();

            foreach (var u in unTargetInfoFromData)
            {
                List<string> unsIds = new List<string>();
                unsIds = u.Split(',').Distinct().ToList();
                unTargetListFromData.AddRange(unsIds);
            }

            foreach (var u in unsInfoFromData)
            {
                List<string> unsIds = new List<string>();
                unsIds = u.Split(',').Distinct().ToList();
                unsListFromData.AddRange(unsIds);
            }

            foreach (var u in focusAreaInfoFromData.Distinct())
            {
                List<string> faIds = new List<string>();
                faIds = u.Split(',').Distinct().ToList();
                focusAreaListFromData.AddRange(faIds);
            }

            TenantDBContext dbContext1 = await _pUtility.GetTenantDbContextForParallelReadAsync();
            TenantDBContext dbContext2 = await _pUtility.GetTenantDbContextForParallelReadAsync();
            TenantDBContext dbContext3 = await _pUtility.GetTenantDbContextForParallelReadAsync();

            var focusAreaInfoAsync = dbContext1.tco_focusarea.Where(x => x.fk_tenant_id == userDetails.tenant_id).ToListAsync();
            var unsgoalsInfoAsync = dbContext2.gco_un_susdev_goals.Select(x => new { x.pk_goal_id, x.goal_short_name, x.goal_name }).ToListAsync();
            var unTargetInfoAsync = dbContext3.gco_un_susdev_targets.Select(x => new { x.pk_target_id, x.target_short_name, x.target_name }).ToListAsync();

            await Task.WhenAll(focusAreaInfoAsync, unsgoalsInfoAsync, unTargetInfoAsync);

            List<tco_focusarea> focusAreaInfo = focusAreaInfoAsync.Result;
            var unsgoalsInfo = unsgoalsInfoAsync.Result;
            var unTargetInfo = unTargetInfoAsync.Result;

            foreach (var unTarget in unTargetListFromData.Distinct().OrderBy(x => x))
            {
                if (!string.IsNullOrEmpty(unTarget))
                {
                    unTargetDef = unTargetDef + "<span class=\"bp-blue-tag cursor\" title=\"" + unTargetInfo.Where(x => x.pk_target_id == unTarget).Select(x => x.target_name).FirstOrDefault() + "\">" + unTargetInfo.Where(x => x.pk_target_id == unTarget).Select(x => x.target_short_name).FirstOrDefault() + "</span>";
                }
            }

            foreach (var uns in unsListFromData.Distinct().OrderBy(x => x))
            {
                if (!string.IsNullOrEmpty(uns))
                {
                    var unGoalName = unsgoalsInfo.Where(x => x.pk_goal_id == uns).Select(x => x.goal_name).FirstOrDefault();
                    unGoalList.Add("<span class=\"bp-blue-tag cursor\" title=\"" + unGoalName + "\">" + unGoalName.Substring(0, 5) + unsgoalsInfo.Where(x => x.pk_goal_id == uns).Select(x => x.goal_short_name).FirstOrDefault() + "</span>");
                }
            }
            unGoalList = unGoalList.OrderBy(x => x).ToList();
            unsDef = string.Join("", unGoalList).ToString();
            foreach (var fa in focusAreaListFromData.OrderBy(x => x))
            {
                if (!string.IsNullOrEmpty(fa))
                {
                    var fatoAppend = focusAreaInfo.FirstOrDefault(x => x.pk_id.ToString() == fa);
                    if (fatoAppend != null)
                    {
                        if (isBusinessPlanTarget && fatoAppend.focusarea_description.Count() > 32)
                        {
                            faDef = faDef + "<span class=\"bp-red-tag\" title=\"" + fatoAppend.focusarea_description + "\">" + fatoAppend.focusarea_description.Substring(0, 32) + "...</span>";
                        }
                        else
                        {
                            faDef = faDef + "<span class=\"bp-red-tag\">" + fatoAppend.focusarea_description + "</span>";
                        }

                    }
                }
            }
        }

        //Format data

        var goalArray = new JArray();
        var targetArray = new JArray();
        var selectedTargetArray = new JArray();
        List<string> selectedGoalArray = new List<string>();
        selectedGoalArray.AddRange(data.selectedGoalId);
        dynamic targetObj = new JObject();

        targetObj.focusAreaDescription = faDef;
        targetObj.unsGoalsDescription = unsDef;
        targetObj.unTargetsDescription = unTargetDef;

        targetData = (from a in targetData
            group new { a } by new { a.key, a.value, a.goalDescription, a.fk_goal_id } into g
            select new TargetDropdownHelper
            {
                key = g.Key.key,
                value = g.Key.value,
                fk_goal_id = g.Key.fk_goal_id,
                goalDescription = g.Key.goalDescription
            }).Distinct().ToList();

        selectedTargets = (from a in selectedTargets
            group new { a } by new { a.key, a.value, a.goalDescription, a.fk_goal_id } into g
            select new TargetDropdownHelper
            {
                key = g.Key.key,
                value = g.Key.value,
                fk_goal_id = g.Key.fk_goal_id,
                goalDescription = g.Key.goalDescription
            }).Distinct().ToList();

        foreach (var item in targetData)
        {
            //add target row
            dynamic row = new JObject();
            row.KeyId = item.key;
            row.ValueString = item.value + " " + (!string.IsNullOrEmpty(item.goalDescription) ? "(" + item.goalDescription + ")" : string.Empty);
            targetArray.Add(row);
            //add goal row
            dynamic goalRow = new JObject();
            if (data.isAngular)
            {
                goalRow.key = item.fk_goal_id;
                goalRow.value = item.goalDescription;
            }
            else
            {
                goalRow.KeyId = item.fk_goal_id;
                goalRow.ValueString = item.goalDescription;
            }
            goalRow.ConnectedTargetId = item.key;
            goalRow.ConnectedTargetDescription = item.value + " " + (!string.IsNullOrEmpty(item.goalDescription) ? "(" + item.goalDescription + ")" : string.Empty);
            goalArray.Add(goalRow);
        }
        foreach (var item in selectedTargets)
        {
            //add target row
            dynamic row = new JObject();
            row.KeyId = item.key;
            row.ValueString = item.value + " " + (!string.IsNullOrEmpty(item.goalDescription) ? "(" + item.goalDescription + ")" : string.Empty);
            selectedTargetArray.Add(row);
            selectedGoalArray.Add(item.fk_goal_id.ToString());
        }
        if (selectedGoalArray.Any())
        {
            selectedGoalArray = selectedGoalArray.Distinct().ToList();
        }
        var strategyArray = new JArray();

        strategyData = (from a in strategyData
            group new { a } by new { a.strategyId, a.strategy, a.goalDescription } into g
            select new StrategyDropdownHelper
            {
                strategyId = g.Key.strategyId,
                strategy = g.Key.strategy,
                goalDescription = g.Key.goalDescription
            }).Distinct().ToList();

        foreach (var item in strategyData)
        {
            dynamic row = new JObject();
            row.KeyId = item.strategyId;
            row.ValueString = item.strategy + " " + (!string.IsNullOrEmpty(item.goalDescription) ? "(" + item.goalDescription + ")" : string.Empty);
            strategyArray.Add(row);
        }

        targetObj.goalList = goalArray;
        targetObj.targetList = targetArray;
        targetObj.strategyList = strategyArray;
        targetObj.selectedTargets = selectedTargetArray;
        targetObj.selectedGoals = JArray.FromObject(selectedGoalArray);

        var multiSelectData = await dbContext.vw_tco_parameters.Where(x => x.param_name == "IS_MULTI_SELECT_STRATEGY" && x.fk_tenant_id == userDetails.tenant_id).Select(p => new { p.param_value, p.active }).FirstOrDefaultAsync();

        if (multiSelectData != null && multiSelectData.active == 1 && multiSelectData.param_value.ToLower() == "true")
        {
            targetObj.isMultiSelectStrategy = true;
        }
        else
        {
            targetObj.isMultiSelectStrategy = false;
        }
        sb.AppendLine($"5 ->" + DateTime.UtcNow);
        await InsertPerformanceLog(userId, sb.ToString(), "busplan get targetgoal end");
        return targetObj;
    }

    public async Task<JObject> GetTargetGoalLinkedDetailsAsync(string userId, string orgId, string serviceId, bool isBusinessPlanTarget, int budgetYear, TargetDropdownHelper data)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext dbContext = await _pUtility.GetTenantDBContextAsync();


        int OrgLevelforCityLevel = 1;
        bool isTenantHaServiceIdSetup = false;
        List<string> emptyStringArr = new List<string>();
        List<Guid> emptyGuidArr = new List<Guid>();
        data.selectedGoalId = data == null || data.selectedGoalId == null ? emptyStringArr : data.selectedGoalId;
        data.selectedTargetId = data == null || data.selectedTargetId == null ? emptyGuidArr : data.selectedTargetId;

        var orgVersionContent = await _pUtility.GetOrgVersionSpecificContentAsync(userId, _pUtility.GetForecastPeriod(budgetYear, 1));
        var cityLevel = orgVersionContent.lstOrgHierarchy.FirstOrDefault();
        bool isCityLevelAsDefault = string.IsNullOrEmpty(orgId);
        orgId = string.IsNullOrEmpty(orgId) ? cityLevel.org_id_1 : orgId;
        var targetUseGoalsParam = (await _pUtility.GetParameterValueAsync(userId, "TARGET_USE_UNGOALS")).ToLower() == "true" ? true : false;
        string configuredServiceID = await dbContext.tco_parameters.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.param_name == "FINPLAN_LEVEL_2").Select(x => x.param_value).FirstOrDefaultAsync();
        if (!isCityLevelAsDefault && !string.IsNullOrEmpty(configuredServiceID))
        {
            isTenantHaServiceIdSetup = configuredServiceID.StartsWith("service");
            if (isTenantHaServiceIdSetup && string.IsNullOrEmpty(serviceId))
            {
                serviceId = "ALL";
            }
            //For lower level org ids - tenants who doesnt have service id setup may send lower org id as service id value.
            else if (!isTenantHaServiceIdSetup && !string.IsNullOrEmpty(serviceId))
            {
                orgId = serviceId.ToLower() == "ALL".ToLower() ? orgId : serviceId;
                serviceId = string.Empty;
            }
        }
        serviceId = string.IsNullOrEmpty(serviceId) || serviceId == "null" ? string.Empty : serviceId;

        List<TargetDropdownHelper> targetData = new List<TargetDropdownHelper>();
        List<StrategyDropdownHelper> strategyData = new List<StrategyDropdownHelper>();

        List<GoalnDistrFth> goalDistList = await _budPropUtil.FetchGoalDistrAsync(userId, budgetYear, null);
        List<TargetnDistrFth> tgtDistList = await _budPropUtil.FetchTargetDistrAsync(userId, budgetYear, null);

        goalDistList = await LinkPlanFocusAreaDataAsync(userId, budgetYear, goalDistList);

        var stratList = await (from st in dbContext.tfp_strategy_text
            join tsg in dbContext.tfp_strategy_goal on new { a = st.fk_tenant_id, b = st.pk_strategy_id }
                equals new { a = tsg.fk_tenant_id, b = tsg.fk_strategy_id } into stra
            from res in stra.DefaultIfEmpty()
            join tst in dbContext.tfp_strategy_target on new { a = st.fk_tenant_id, b = st.pk_strategy_id }
                equals new { a = tst.fk_tenant_id, b = tst.fk_strategy_id } into tstra
            from tres in tstra.DefaultIfEmpty()
            where st.fk_tenant_id == userDetails.tenant_id && st.budget_year == budgetYear && st.org_id == orgId
            group new { st, res, tres } by new
            {
                st.strategy_name,
                st.pk_strategy_id,
                st.strategy_desc,
                st.org_id,
                st.org_level,
                st.service_id,
                goalId = res == null ? Guid.Empty : res.fk_goal_id,
                targetId = tres == null ? Guid.Empty : tres.fk_target_id,
                st.is_busplan
            } into g
            select new
            {
                strategy_name = g.Key.strategy_name,
                goal_id = g.Key.goalId,
                target_id = g.Key.targetId,
                strategy_id = g.Key.pk_strategy_id,
                strategyDesc = g.Key.strategy_desc,
                orgid = g.Key.org_id,
                orgLevel = g.Key.org_level,
                serviceId = g.Key.service_id,
                isBusPlan = g.Key.is_busplan
            }).AsNoTracking().ToListAsync();
        stratList = isBusinessPlanTarget ? stratList : stratList.Where(x => !x.isBusPlan).ToList();

        if (isCityLevelAsDefault)
        {
            strategyData = (from strat in stratList
                join gb in goalDistList on strat.goal_id equals gb.goalId into gl
                from res in gl.DefaultIfEmpty()
                join tt in tgtDistList on strat.target_id equals tt.targetId into tgt
                from tres in tgt.DefaultIfEmpty()
                where strat.orgLevel == OrgLevelforCityLevel
                group new { strat, res, tres } by new
                {
                    strat.strategy_id,
                    strat.strategy_name,
                    strat.strategyDesc,
                    goalId = res == null ? Guid.Empty : res.goalId,
                    goalName = res == null ? string.Empty : res.goal_name,
                    targetId = tres == null ? Guid.Empty : tres.targetId
                } into g
                select new StrategyDropdownHelper
                {
                    strategyId = g.Key.strategy_id,
                    strategy = g.Key.strategy_name,
                    strategyDesc = g.Key.strategyDesc,
                    fk_goal_id = g.Key.goalId,
                    goalDescription = g.Key.goalName,
                    fk_target_id = g.Key.targetId,
                }).OrderBy(x => x.strategy).ToList();
        }
        else
        {
            strategyData = (string.IsNullOrEmpty(serviceId) || (!string.IsNullOrEmpty(serviceId) && (serviceId == "-1" || serviceId == "ALL"))) ?
                (from strat in stratList
                    join gb in goalDistList on strat.goal_id equals gb.goalId into gl
                    from res in gl.DefaultIfEmpty()
                    join tt in tgtDistList on strat.target_id equals tt.targetId into tgt
                    from tres in tgt.DefaultIfEmpty()
                    where strat.orgid == orgId
                    group new { strat, res, tres } by new
                    {
                        strat.strategy_id,
                        strat.strategy_name,
                        strat.strategyDesc,
                        goalId = res == null ? Guid.Empty : res.goalId,
                        goalName = res == null ? string.Empty : res.goal_name,
                        targetId = tres == null ? Guid.Empty : tres.targetId
                    } into g
                    select new StrategyDropdownHelper
                    {
                        strategyId = g.Key.strategy_id,
                        strategy = g.Key.strategy_name,
                        strategyDesc = g.Key.strategyDesc,
                        fk_goal_id = g.Key.goalId,
                        goalDescription = g.Key.goalName,
                        fk_target_id = g.Key.targetId,
                    }).OrderBy(x => x.strategy).ToList()
                :
                (from strat in stratList
                    join gb in goalDistList on strat.goal_id equals gb.goalId into gl
                    from res in gl.DefaultIfEmpty()
                    join tt in tgtDistList on strat.target_id equals tt.targetId into tgt
                    from tres in tgt.DefaultIfEmpty()
                    where strat.orgid == orgId && strat.serviceId == serviceId
                    group new { strat, res, tres } by new
                    {
                        strat.strategy_id,
                        strat.strategy_name,
                        strat.strategyDesc,
                        goalId = res == null ? Guid.Empty : res.goalId,
                        goalName = res == null ? string.Empty : res.goal_name,
                        targetId = tres == null ? Guid.Empty : tres.targetId
                    } into g
                    select new StrategyDropdownHelper
                    {
                        strategyId = g.Key.strategy_id,
                        strategy = g.Key.strategy_name,
                        strategyDesc = g.Key.strategyDesc,
                        fk_goal_id = g.Key.goalId,
                        goalDescription = g.Key.goalName,
                        fk_target_id = g.Key.targetId,
                    }).OrderBy(x => x.strategy).ToList();
        }

        if (!isBusinessPlanTarget)
        {
            strategyData = strategyData.GroupBy(p => new { p.strategy, p.strategyDesc, p.strategyId }).Select(g => g.FirstOrDefault()).ToList();
        }
        else
        {
            strategyData = strategyData.GroupBy(p => new { p.strategy, p.strategyDesc }).Select(g => g.FirstOrDefault()).ToList();
        }

        if (isCityLevelAsDefault)
        {
            targetData = (from at in tgtDistList
                join gb in goalDistList on new { g = at.goalId, oid = at.org_id, ol = at.org_level, sv = at.service_id }
                    equals new { g = gb.goalId, oid = gb.org_id, ol = gb.org_level, sv = gb.service_id }
                where at.org_level == OrgLevelforCityLevel
                group new { at, gb } by new { at.targetId, at.target_name, at.goalId, at.unsdgoal_target, gb.goal_name, at.is_busplan_target, gb.focus_area, gb.unsd_goals } into g
                select new TargetDropdownHelper
                {
                    key = g.Key.targetId,
                    value = string.IsNullOrEmpty(g.Key.target_name) ? string.Empty : g.Key.target_name,
                    fk_goal_id = g.Key.goalId,
                    goalDescription = g.Key.goal_name,
                    isBusinessPlanTarget = g.Key.is_busplan_target,
                    unsdGoals = g.Key.unsd_goals,
                    focusAreaIds = g.Key.focus_area,
                    unsdGoalForTargets = g.Key.unsdgoal_target
                }).OrderBy(x => x.value).ToList();
        }
        else if (serviceId == "-1" || serviceId == "ALL" || string.IsNullOrEmpty(serviceId))
        {
            targetData = (from at in tgtDistList
                join gb in goalDistList on new { g = at.goalId, oid = at.org_id, ol = at.org_level, sv = at.service_id }
                    equals new { g = gb.goalId, oid = gb.org_id, ol = gb.org_level, sv = gb.service_id }
                where at.org_id == orgId
                group new { at, gb } by new { at.targetId, at.target_name, at.goalId, gb.goal_name, at.is_busplan_target, gb.focus_area, gb.unsd_goals, at.unsdgoal_target } into g
                select new TargetDropdownHelper
                {
                    key = g.Key.targetId,
                    value = string.IsNullOrEmpty(g.Key.target_name) ? string.Empty : g.Key.target_name,
                    fk_goal_id = g.Key.goalId,
                    goalDescription = g.Key.goal_name,
                    isBusinessPlanTarget = g.Key.is_busplan_target,
                    unsdGoals = g.Key.unsd_goals,
                    focusAreaIds = g.Key.focus_area,
                    unsdGoalForTargets = g.Key.unsdgoal_target
                }).OrderBy(x => x.value).ToList();
        }
        else
        {
            targetData = (from at in tgtDistList
                join gb in goalDistList on new { g = at.goalId, oid = at.org_id, ol = at.org_level, sv = at.service_id }
                    equals new { g = gb.goalId, oid = gb.org_id, ol = gb.org_level, sv = gb.service_id }
                where at.org_id == orgId && at.service_id == serviceId
                group new { at, gb } by new { at.targetId, at.target_name, at.goalId, gb.goal_name, at.is_busplan_target, gb.focus_area, gb.unsd_goals, at.unsdgoal_target } into g
                select new TargetDropdownHelper
                {
                    key = g.Key.targetId,
                    value = string.IsNullOrEmpty(g.Key.target_name) ? string.Empty : g.Key.target_name,
                    fk_goal_id = g.Key.goalId,
                    goalDescription = g.Key.goal_name,
                    isBusinessPlanTarget = g.Key.is_busplan_target,
                    unsdGoals = g.Key.unsd_goals,
                    focusAreaIds = g.Key.focus_area,
                    unsdGoalForTargets = g.Key.unsdgoal_target
                }).OrderBy(x => x.value).ToList();
        }

        string unsDef = string.Empty;
        string faDef = string.Empty;
        string unTargetDef = string.Empty;
        List<string> unGoalList = new List<string>();
        List<string> unTargetList = new List<string>();
        List<string> unStratList = new List<string>();

        if (!isBusinessPlanTarget)
        {
            targetData = targetData.Where(x => !x.isBusinessPlanTarget).ToList();
        }

        List<TargetDropdownHelper> selectedTargets = new List<TargetDropdownHelper>();
        List<string> unsInfoFromData = new List<string>();
        List<string> focusAreaInfoFromData = new List<string>();
        List<string> unTargetInfoFromData = new List<string>();
        //Display focus area and uns goals related to goal
        if (data.selectedGoalId.Count > 0 || data.selectedTargetId.Count > 0)
        {
            //Select focus area and unsGoals on selected goal id.
            if (data.selectedGoalId.Count > 0)
            {
                var infoFromGoals = await (from a in dbContext.tco_goals
                    where a.fk_tenant_id == userDetails.tenant_id && data.selectedGoalId.Contains(a.pk_goal_id.ToString())
                    select new
                    {
                        unsdGoals = a.unsd_goals,
                        focusAreaIds = a.focus_area
                    }).Distinct().AsNoTracking().ToListAsync();
                var planproposalFocusArea = await GetMappedFocusAreaDataAsync(userId, budgetYear);
                infoFromGoals = (from d in infoFromGoals
                    join e in planproposalFocusArea on d.focusAreaIds equals e.PlanFocusAreaId.ToString() into g3
                    from x2 in g3.DefaultIfEmpty()
                    select new
                    {
                        unsdGoals = d.unsdGoals,
                        focusAreaIds = x2 != null ? x2.FinPlanFocusAreaId.ToString() : d.focusAreaIds,
                    }).ToList();
                unsInfoFromData = infoFromGoals.Select(x => x.unsdGoals).ToList();
                focusAreaInfoFromData = infoFromGoals.Select(x => x.focusAreaIds).ToList();
                targetData = targetData.Where(x => data.selectedGoalId.Contains(x.fk_goal_id.ToString())).ToList();
                strategyData = strategyData.Where(x => data.selectedGoalId.Contains(x.fk_goal_id.ToString()) || data.selectedTargetId.Contains(x.fk_target_id)).ToList();
            }
            else
            {
                var infoFromTargets = await (from a in dbContext.tco_targets
                    where a.fk_tenant_id == userDetails.tenant_id && data.selectedTargetId.Contains(a.pk_target_id)
                    select new
                    {
                        unsdTargets = a.unsd_target
                    }).Distinct().AsNoTracking().ToListAsync();

                unTargetInfoFromData = infoFromTargets.Select(x => x.unsdTargets).ToList();

                strategyData = strategyData.Where(x => data.selectedTargetId.Contains(x.fk_target_id)).ToList();

                //98961
                var (multiSelect, active) = await _pUtility.GetParameterValueAndActiveStatusAsync(userId, "IS_MULTI_SELECT_GOALS");
                if (!(multiSelect != null && active == 1 && multiSelect.ToLower() == "true"))
                {
                    selectedTargets = targetData.Where(x => data.selectedTargetId.Contains(x.key)).ToList();

                    var targetGoalId = targetData.Where(x => data.selectedTargetId.Contains(x.key)).Select(t => t.fk_goal_id).FirstOrDefault();
                    targetData = targetData.Where(x => x.fk_goal_id == targetGoalId).ToList();
                }
            }
            if (targetUseGoalsParam)
            {
                if (data.selectedTargetId.Count() > 0 && targetData.Where(x => data.selectedTargetId.Contains(x.key)).Select(x => x.unsdGoalForTargets).Count() > 0)
                {
                    unsInfoFromData = targetData.Where(x => data.selectedTargetId.Contains(x.key)).Select(x => x.unsdGoalForTargets).ToList();
                }

                unTargetInfoFromData = new List<string>();
            }
            List<string> unTargetListFromData = new List<string>();
            List<string> unsListFromData = new List<string>();
            List<string> focusAreaListFromData = new List<string>();

            foreach (var u in unTargetInfoFromData)
            {
                List<string> unsIds = new List<string>();
                unsIds = u.Split(',').Distinct().ToList();
                unTargetListFromData.AddRange(unsIds);
            }

            foreach (var u in unsInfoFromData)
            {
                List<string> unsIds = new List<string>();
                unsIds = u.Split(',').Distinct().ToList();
                unsListFromData.AddRange(unsIds);
            }

            foreach (var u in focusAreaInfoFromData.Distinct())
            {
                List<string> faIds = new List<string>();
                faIds = u.Split(',').Distinct().ToList();
                focusAreaListFromData.AddRange(faIds);
            }
            var focusAreaInfo = await dbContext.tco_focusarea.Where(x => x.fk_tenant_id == userDetails.tenant_id).ToListAsync();
            var unsgoalsInfo = await dbContext.gco_un_susdev_goals.Select(x => new { x.pk_goal_id, x.goal_short_name, x.goal_name }).ToListAsync();
            var unTargetInfo = await dbContext.gco_un_susdev_targets.Select(x => new { x.pk_target_id, x.target_short_name, x.target_name }).ToListAsync();

            foreach (var unTarget in unTargetListFromData.Distinct().OrderBy(x => x))
            {
                if (!string.IsNullOrEmpty(unTarget))
                {
                    unTargetDef = unTargetDef + "<span class=\"bp-blue-tag cursor\" title=\"" + unTargetInfo.Where(x => x.pk_target_id == unTarget).Select(x => x.target_name).FirstOrDefault() + "\">" + unTargetInfo.Where(x => x.pk_target_id == unTarget).Select(x => x.target_short_name).FirstOrDefault() + "</span>";
                }
            }

            foreach (var uns in unsListFromData.Distinct().OrderBy(x => x))
            {
                if (!string.IsNullOrEmpty(uns))
                {
                    var unGoalName = unsgoalsInfo.Where(x => x.pk_goal_id == uns).Select(x => x.goal_name).FirstOrDefault();
                    unGoalList.Add("<span class=\"bp-blue-tag cursor\" title=\"" + unGoalName + "\">" + unGoalName.Substring(0, 5) + unsgoalsInfo.Where(x => x.pk_goal_id == uns).Select(x => x.goal_short_name).FirstOrDefault() + "</span>");
                }
            }
            unGoalList = unGoalList.OrderBy(x => x).ToList();
            unsDef = string.Join("", unGoalList).ToString();
            foreach (var fa in focusAreaListFromData.OrderBy(x => x))
            {
                if (!string.IsNullOrEmpty(fa))
                {
                    var fatoAppend = focusAreaInfo.FirstOrDefault(x => x.pk_id.ToString() == fa);
                    if (fatoAppend != null)
                    {
                        string shortfadesc = "";
                        if (fatoAppend.focusarea_description.Length > 32)
                        {
                            shortfadesc = fatoAppend.focusarea_description.Substring(0, 32);
                            shortfadesc += "...";
                            faDef = faDef + "<span class=\"bp-red-tag\" title=\"" + fatoAppend.focusarea_description + "\">" + shortfadesc + "</span>";
                        }
                        else
                        {
                            faDef = faDef + "<span class=\"bp-red-tag\">" + fatoAppend.focusarea_description + "</span>";
                        }
                    }
                }
            }
        }
        //Format data
        var goalArray = new JArray();
        var targetArray = new JArray();
        var selectedTargetArray = new JArray();
        List<string> selectedGoalArray = new List<string>();
        selectedGoalArray.AddRange(data.selectedGoalId);
        dynamic targetObj = new JObject();

        targetObj.focusAreaDescription = faDef;
        targetObj.unsGoalsDescription = unsDef;
        targetObj.unTargetsDescription = unTargetDef;

        targetData = (from a in targetData
            group new { a } by new { a.key, a.value, a.goalDescription, a.fk_goal_id } into g
            select new TargetDropdownHelper
            {
                key = g.Key.key,
                value = g.Key.value,
                fk_goal_id = g.Key.fk_goal_id,
                goalDescription = g.Key.goalDescription
            }).Distinct().ToList();

        selectedTargets = (from a in selectedTargets
            group new { a } by new { a.key, a.value, a.goalDescription, a.fk_goal_id } into g
            select new TargetDropdownHelper
            {
                key = g.Key.key,
                value = g.Key.value,
                fk_goal_id = g.Key.fk_goal_id,
                goalDescription = g.Key.goalDescription
            }).Distinct().ToList();

        foreach (var item in targetData)
        {
            //add target row
            dynamic row = new JObject();
            row.KeyId = item.key;
            row.ValueString = item.value + " " + (!string.IsNullOrEmpty(item.goalDescription) ? "(" + item.goalDescription + ")" : string.Empty);
            targetArray.Add(row);
            //add goal row
            dynamic goalRow = new JObject();
            goalRow.KeyId = item.fk_goal_id;
            goalRow.ValueString = item.goalDescription;
            goalRow.ConnectedTargetId = item.key;
            goalRow.ConnectedTargetDescription = item.value + " " + (!string.IsNullOrEmpty(item.goalDescription) ? "(" + item.goalDescription + ")" : string.Empty);
            goalArray.Add(goalRow);
        }
        foreach (var item in selectedTargets)
        {
            //add target row
            dynamic row = new JObject();
            row.KeyId = item.key;
            row.ValueString = item.value + " " + (!string.IsNullOrEmpty(item.goalDescription) ? "(" + item.goalDescription + ")" : string.Empty);
            selectedTargetArray.Add(row);
            selectedGoalArray.Add(item.fk_goal_id.ToString());
        }
        if (selectedGoalArray.Any())
        {
            selectedGoalArray = selectedGoalArray.Distinct().ToList();
        }
        var strategyArray = new JArray();

        strategyData = (from a in strategyData
            group new { a } by new { a.strategyId, a.strategy, a.goalDescription } into g
            select new StrategyDropdownHelper
            {
                strategyId = g.Key.strategyId,
                strategy = g.Key.strategy,
                goalDescription = g.Key.goalDescription
            }).Distinct().ToList();

        foreach (var item in strategyData)
        {
            dynamic row = new JObject();
            row.KeyId = item.strategyId;
            row.ValueString = item.strategy + " " + (!string.IsNullOrEmpty(item.goalDescription) ? "(" + item.goalDescription + ")" : string.Empty);
            strategyArray.Add(row);
        }

        targetObj.goalList = goalArray;
        targetObj.targetList = targetArray;
        targetObj.strategyList = strategyArray;
        targetObj.selectedTargets = selectedTargetArray;
        targetObj.selectedGoals = JArray.FromObject(selectedGoalArray);

        var multiSelectData = await dbContext.vw_tco_parameters.Where(x => x.param_name == "IS_MULTI_SELECT_STRATEGY" && x.fk_tenant_id == userDetails.tenant_id).Select(p => new { p.param_value, p.active }).FirstOrDefaultAsync();

        if (multiSelectData != null && multiSelectData.active == 1 && multiSelectData.param_value.ToLower() == "true")
        {
            targetObj.isMultiSelectStrategy = true;
        }
        else
        {
            targetObj.isMultiSelectStrategy = false;
        }

        return targetObj;
    }



    public JObject GetUnTargetbyTargetId(string userId, List<Guid> targetIds)
    {
        var result = GetUnTargetbyTargetIdAsync(userId, targetIds).GetAwaiter().GetResult();
        return result;
    }



    public async Task<JObject> GetUnTargetbyTargetIdAsync(string userId, List<Guid> targetIds)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext dbContext = await _pUtility.GetTenantDBContextAsync();
        var targetUseGoalsParam = (await _pUtility.GetParameterValueAsync(userId, "TARGET_USE_UNGOALS")).ToLower() == "true" ? true : false;
        targetIds = targetIds != null ? targetIds : new List<Guid>();
            
        string unTargetDef = string.Empty;
        string unGoalDef = string.Empty;
        List<string> unTargetInfoFromData = new List<string>();
        List<string> unGoalsInfoFromData = new List<string>();
        dynamic unTargetObj = new JObject();

        var infoFromTargets = await (from a in dbContext.tco_targets
            where a.fk_tenant_id == userDetails.tenant_id && targetIds.Contains(a.pk_target_id)
            select new
            {
                unsdTargets = a.unsd_target,
                unsdGoalsforTarget = a.unsd_goal
            }).Distinct().AsNoTracking().ToListAsync();
        if (targetUseGoalsParam)
        {
            unGoalsInfoFromData = infoFromTargets.Select(x => x.unsdGoalsforTarget).ToList();
            List<string> unGoalListFromData = new List<string>();
            foreach (var un in unGoalsInfoFromData)
            {
                List<string> unIds = new List<string>();
                unIds = un.Split(',').Distinct().ToList();
                unGoalListFromData.AddRange(unIds);
            }

            var unGolasInfo = await dbContext.gco_un_susdev_goals.Select(x => new { x.pk_goal_id, x.goal_name, x.goal_short_name }).ToListAsync();
            foreach (var un in unGoalListFromData)
            {
                if (!string.IsNullOrEmpty(un))
                {
                    var goalInfo = unGolasInfo.Where(x => x.pk_goal_id == un).FirstOrDefault();
                    unGoalDef = unGoalDef + "<span class=\"bp-blue-tag cursor\" title=\"" + goalInfo.goal_name + "\">" + goalInfo.goal_short_name + "</span>"; ;
                }
            }
        }
        else
        {
            unTargetInfoFromData = infoFromTargets.Select(x => x.unsdTargets).ToList();

            List<string> unTargetListFromData = new List<string>();

            foreach (var u in unTargetInfoFromData)
            {
                List<string> unsIds = new List<string>();
                unsIds = u.Split(',').Distinct().ToList();
                unTargetListFromData.AddRange(unsIds);
            }

            var unTargetInfo = await dbContext.gco_un_susdev_targets.Select(x => new { x.pk_target_id, x.target_short_name, x.target_name }).ToListAsync();

            foreach (var unTarget in unTargetListFromData.Distinct())
            {
                if (!string.IsNullOrEmpty(unTarget))
                {
                    unTargetDef = unTargetDef + "<span class=\"bp-blue-tag cursor\" title=\"" + unTargetInfo.Where(x => x.pk_target_id == unTarget).Select(x => x.target_name).FirstOrDefault() + "\">" + unTargetInfo.Where(x => x.pk_target_id == unTarget).Select(x => x.target_short_name).FirstOrDefault() + "</span>";
                }
            }
        }
        unTargetObj.unTargetsDescription = unTargetDef;
        unTargetObj.unGoalsDescription = unGoalDef;

        return unTargetObj;
    }



    private async Task<List<StratDistHelper>> FetchStrategyDistrDetails(string userId, int budgetYear, string orgId, bool isBusinessPlanTarget)
    {
        TenantDBContext dbContext = await _pUtility.GetTenantDbContextForParallelReadAsync();
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        var stratList = isBusinessPlanTarget ? await (from st in dbContext.tfp_strategy_text
                join tsg in dbContext.tfp_strategy_goal on new { a = st.fk_tenant_id, b = st.pk_strategy_id }
                    equals new { a = tsg.fk_tenant_id, b = tsg.fk_strategy_id } into stra
                from res in stra.DefaultIfEmpty()
                join tst in dbContext.tfp_strategy_target on new { a = st.fk_tenant_id, b = st.pk_strategy_id }
                    equals new { a = tst.fk_tenant_id, b = tst.fk_strategy_id } into tstra
                from tres in tstra.DefaultIfEmpty()
                where st.fk_tenant_id == userDetails.tenant_id && st.budget_year == budgetYear && st.org_id == orgId
                group new { st, res, tres } by new
                {
                    st.strategy_name,
                    st.pk_strategy_id,
                    st.strategy_desc,
                    st.org_id,
                    st.org_level,
                    st.service_id,
                    goalId = res == null ? Guid.Empty : res.fk_goal_id,
                    targetId = tres == null ? Guid.Empty : tres.fk_target_id
                } into g
                select new StratDistHelper
                {
                    strategy_name = g.Key.strategy_name,
                    goal_id = g.Key.goalId,
                    target_id = g.Key.targetId,
                    strategy_id = g.Key.pk_strategy_id,
                    strategyDesc = g.Key.strategy_desc,
                    orgid = g.Key.org_id,
                    orgLevel = g.Key.org_level.Value,
                    serviceId = g.Key.service_id
                }).AsNoTracking().ToListAsync()
            :
            await (from st in dbContext.tfp_strategy_text
                join tsg in dbContext.tfp_strategy_goal on new { a = st.fk_tenant_id, b = st.pk_strategy_id }
                    equals new { a = tsg.fk_tenant_id, b = tsg.fk_strategy_id } into stra
                from res in stra.DefaultIfEmpty()
                join tst in dbContext.tfp_strategy_target on new { a = st.fk_tenant_id, b = st.pk_strategy_id }
                    equals new { a = tst.fk_tenant_id, b = tst.fk_strategy_id } into tstra
                from tres in tstra.DefaultIfEmpty()
                where st.fk_tenant_id == userDetails.tenant_id && st.budget_year == budgetYear && !st.is_busplan && st.org_id == orgId
                group new { st, res, tres } by new
                {
                    st.strategy_name,
                    st.pk_strategy_id,
                    st.strategy_desc,
                    st.org_id,
                    st.org_level,
                    st.service_id,
                    goalId = res == null ? Guid.Empty : res.fk_goal_id,
                    targetId = tres == null ? Guid.Empty : tres.fk_target_id
                } into g
                select new StratDistHelper
                {
                    strategy_name = g.Key.strategy_name,
                    goal_id = g.Key.goalId,
                    target_id = g.Key.targetId,
                    strategy_id = g.Key.pk_strategy_id,
                    strategyDesc = g.Key.strategy_desc,
                    orgid = g.Key.org_id,
                    orgLevel = g.Key.org_level.Value,
                    serviceId = g.Key.service_id
                }).AsNoTracking().ToListAsync();

        return stratList;
    }


    private List<GoalnDistrFth> LinkPlanFocusAreaData(string userId, int budgetYear, List<GoalnDistrFth> goalDistList)
    {
        return LinkPlanFocusAreaDataAsync(userId, budgetYear, goalDistList).GetAwaiter().GetResult();
    }


    private async Task<List<GoalnDistrFth>> LinkPlanFocusAreaDataAsync(string userId, int budgetYear, List<GoalnDistrFth> goalDistList)
    {
        List<BudgetProposalPlanFocusAreaHelper> focusAreaList = await GetMappedFocusAreaDataAsync(userId, budgetYear);
        List<GoalnDistrFth> updatedList = (from gd in goalDistList
            join e in focusAreaList on gd.focus_area equals e.PlanFocusAreaId.ToString() into g3
            from x2 in g3.DefaultIfEmpty()
            select new GoalnDistrFth
            {
                goalDistrId = gd.goalDistrId,
                goalId = gd.goalId,
                goal_name = gd.goal_name,
                org_created = gd.org_created,
                org_level_created = gd.org_level_created,
                focus_area = x2 != null ? x2.FinPlanFocusAreaId.ToString() : gd.focus_area,
                unsd_goals = gd.unsd_goals,
                is_busplan_goal = gd.is_busplan_goal,
                is_busplan_delgoa = gd.is_busplan_delgoa,
                org_id = gd.org_id,
                org_level = gd.org_level,
                service_id = gd.service_id,
                service_level = gd.service_level,
                target_blob_id = gd.target_blob_id,
                focus_blob_id = gd.focus_blob_id,
                delgo_blob_id = gd.delgo_blob_id,
                tags = gd.tags,
                tenantId = gd.tenantId
            }).ToList();
        return updatedList;
    }



    private List<BudgetProposalPlanFocusAreaHelper> GetMappedFocusAreaData(string userId, int budgetYear)
    {
        var result = GetMappedFocusAreaDataAsync(userId, budgetYear).GetAwaiter().GetResult();
        return result;
    }



    private async Task<List<BudgetProposalPlanFocusAreaHelper>> GetMappedFocusAreaDataAsync(string userId, int budgetYear)
    {
        TenantDBContext DbContext = await _pUtility.GetTenantDbContextForParallelReadAsync();
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        return await (from a in DbContext.tpl_tfp_focusarea_mapping
            where a.budget_year == budgetYear && a.fk_tenant_id == userDetails.tenant_id
            select new BudgetProposalPlanFocusAreaHelper
            {
                PlanFocusAreaId = a.fk_plan_focusArea_id,
                FinPlanFocusAreaId = a.fk_finplan_focusArea_id,
            }).ToListAsync();
    }



    public JArray GetGoalsDropdownDataForAssignment(string userId, string orgId, string serviceId, int budgetYear, int orgIdlevelNo, ClsOrgVersionSpecificContent orgVersionContent, bool isBusinessPlan)
    {
        return GetGoalsDropdownDataForAssignmentAsync(userId, orgId, serviceId, budgetYear, orgIdlevelNo, orgVersionContent, isBusinessPlan).GetAwaiter().GetResult();
    }



    public async Task<JArray> GetGoalsDropdownDataForAssignmentAsync(string userId, string orgId, string serviceId, int budgetYear, int orgIdlevelNo, ClsOrgVersionSpecificContent orgVersionContent, bool isBusinessPlan)
    {
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        serviceId = string.IsNullOrEmpty(serviceId) ? string.Empty : serviceId;
        List<KeyValueGuidHelper> goalsData = new List<KeyValueGuidHelper>();
        List<tco_org_hierarchy> lstOrgHierarchy = orgVersionContent.lstOrgHierarchy;
        var cityID = lstOrgHierarchy.FirstOrDefault().org_id_1;
        var level1Value = string.Empty;
        var level2Value = string.Empty;
        switch (orgIdlevelNo)
        {
            case 3:
                level1Value = lstOrgHierarchy.FirstOrDefault(x => x.org_id_3 == orgId).org_id_2;
                level2Value = orgId;
                break;

            case 4:
                level1Value = lstOrgHierarchy.FirstOrDefault(x => x.org_id_4 == orgId).org_id_2;
                level2Value = lstOrgHierarchy.FirstOrDefault(x => x.org_id_4 == orgId).org_id_3;
                break;

            case 5:
                level1Value = lstOrgHierarchy.FirstOrDefault(x => x.org_id_5 == orgId).org_id_2;
                level2Value = lstOrgHierarchy.FirstOrDefault(x => x.org_id_5 == orgId).org_id_3;
                break;

            case 6:
                level1Value = lstOrgHierarchy.FirstOrDefault(x => x.org_id_6 == orgId).org_id_2;
                level2Value = lstOrgHierarchy.FirstOrDefault(x => x.org_id_6 == orgId).org_id_3;
                break;

            case 7:
                level1Value = lstOrgHierarchy.FirstOrDefault(x => x.org_id_7 == orgId).org_id_2;
                level2Value = lstOrgHierarchy.FirstOrDefault(x => x.org_id_7 == orgId).org_id_3;
                break;

            case 8:
                level1Value = lstOrgHierarchy.FirstOrDefault(x => x.org_id_8 == orgId).org_id_2;
                level2Value = lstOrgHierarchy.FirstOrDefault(x => x.org_id_8 == orgId).org_id_3;
                break;

            default:
                level1Value = cityID;
                level2Value = cityID;
                break;
        }
        var goalsList = new JArray();

        if (orgId == null || cityID.Trim().ToUpper() == orgId.Trim().ToUpper())
        {
            goalsData = !isBusinessPlan ? await (from x in tenantDbContext.tco_goals_distribution
                    join g in tenantDbContext.tco_goals on new { t = x.fk_tenant_id, l = x.fk_goal_id }
                        equals new { t = g.fk_tenant_id, l = g.pk_goal_id }
                    where x.fk_tenant_id == userDetails.tenant_id && g.budget_year == budgetYear && g.is_busplan_goal != true && x.is_busplan_delgoa != true &&
                          (string.IsNullOrEmpty(x.org_id) || x.org_id == cityID) && (string.IsNullOrEmpty(x.service_id.Trim()) || x.service_id == serviceId || x.service_id.ToLower() == "all")
                    group new { g } by new { g.pk_goal_id, g.goal_name } into g1
                    select new KeyValueGuidHelper
                    {
                        key = g1.Key.pk_goal_id,
                        value = g1.Key.goal_name
                    }).OrderBy(x => x.value).ToListAsync()
                :
                await (from x in tenantDbContext.tco_goals_distribution
                    join g in tenantDbContext.tco_goals on new { t = x.fk_tenant_id, l = x.fk_goal_id }
                        equals new { t = g.fk_tenant_id, l = g.pk_goal_id }
                    where x.fk_tenant_id == userDetails.tenant_id && g.budget_year == budgetYear &&
                          x.org_id == cityID.Trim() && x.org_level == orgIdlevelNo && (x.service_id == serviceId || x.service_id.ToLower() == "all" || string.IsNullOrEmpty(x.service_id.Trim()))// Note after going through code found service id is not passed not sure if it is intentional?
                    group new { g } by new { g.pk_goal_id, g.goal_name } into g1
                    select new KeyValueGuidHelper
                    {
                        key = g1.Key.pk_goal_id,
                        value = g1.Key.goal_name
                    }).OrderBy(x => x.value).ToListAsync();
        }
        else
        {
            if (!string.IsNullOrEmpty(serviceId))
            {
                //display goals where service_id is null or empty with goals at selected service id for the selected org when service id is selected
                goalsData = !isBusinessPlan ? await (from x in tenantDbContext.tco_goals_distribution
                        join g in tenantDbContext.tco_goals on new { t = x.fk_tenant_id, l = x.fk_goal_id }
                            equals new { t = g.fk_tenant_id, l = g.pk_goal_id }
                        where x.fk_tenant_id == userDetails.tenant_id && g.budget_year == budgetYear && g.is_busplan_goal != true && x.is_busplan_delgoa != true &&
                              x.org_id == orgId.Trim() && (x.service_id == serviceId || x.service_id.ToLower() == "ALL".ToLower() || string.IsNullOrEmpty(x.service_id.Trim()))
                        group new { g } by new { g.pk_goal_id, g.goal_name } into g1
                        select new KeyValueGuidHelper
                        {
                            key = g1.Key.pk_goal_id,
                            value = g1.Key.goal_name
                        }).OrderBy(x => x.value).ToListAsync() :
                    await (from x in tenantDbContext.tco_goals_distribution
                        join g in tenantDbContext.tco_goals on new { t = x.fk_tenant_id, l = x.fk_goal_id }
                            equals new { t = g.fk_tenant_id, l = g.pk_goal_id }
                        where x.fk_tenant_id == userDetails.tenant_id && g.budget_year == budgetYear &&
                              x.org_id == orgId.Trim() && (x.service_id == serviceId || x.service_id.ToLower() == "ALL".ToLower() || string.IsNullOrEmpty(x.service_id.Trim()))
                              && x.org_level == orgIdlevelNo
                        group new { g } by new { g.pk_goal_id, g.goal_name } into g1
                        select new KeyValueGuidHelper
                        {
                            key = g1.Key.pk_goal_id,
                            value = g1.Key.goal_name
                        }).OrderBy(x => x.value).ToListAsync();
            }
            else if (orgIdlevelNo == 2)
            {
                goalsData = !isBusinessPlan ? await (from x in tenantDbContext.tco_goals_distribution
                        join g in tenantDbContext.tco_goals on new { t = x.fk_tenant_id, l = x.fk_goal_id }
                            equals new { t = g.fk_tenant_id, l = g.pk_goal_id }
                        where x.fk_tenant_id == userDetails.tenant_id && g.budget_year == budgetYear && g.is_busplan_goal != true && x.is_busplan_delgoa != true &&
                              x.org_id == orgId.Trim() && (x.service_id.ToLower() == "ALL".ToLower() || string.IsNullOrEmpty(x.service_id.Trim()))
                        group new { g } by new { g.pk_goal_id, g.goal_name } into g1
                        select new KeyValueGuidHelper
                        {
                            key = g1.Key.pk_goal_id,
                            value = g1.Key.goal_name
                        }).OrderBy(x => x.value).ToListAsync() :
                    await (from x in tenantDbContext.tco_goals_distribution
                        join g in tenantDbContext.tco_goals on new { t = x.fk_tenant_id, l = x.fk_goal_id }
                            equals new { t = g.fk_tenant_id, l = g.pk_goal_id }
                        where x.fk_tenant_id == userDetails.tenant_id && g.budget_year == budgetYear &&
                              x.org_id == orgId.Trim() && (x.service_id.ToLower() == "ALL".ToLower() || string.IsNullOrEmpty(x.service_id.Trim())) && x.org_level == orgIdlevelNo
                        group new { g } by new { g.pk_goal_id, g.goal_name } into g1
                        select new KeyValueGuidHelper
                        {
                            key = g1.Key.pk_goal_id,
                            value = g1.Key.goal_name
                        }).OrderBy(x => x.value).ToListAsync();
            }
            else
            {
                goalsData = !isBusinessPlan ? await (from x in tenantDbContext.tco_goals_distribution
                        join g in tenantDbContext.tco_goals on new { t = x.fk_tenant_id, l = x.fk_goal_id }
                            equals new { t = g.fk_tenant_id, l = g.pk_goal_id }
                        //where x.fk_tenant_id == userDetails.tenant_id && g.budget_year == budgetYear && x.service_id == level2Value && x.org_id == level1Value && g.is_busplan_goal != true && x.is_busplan_delgoa != true
                        where x.fk_tenant_id == userDetails.tenant_id && g.budget_year == budgetYear && (x.service_id.ToLower() == "ALL".ToLower() || string.IsNullOrEmpty(x.service_id.Trim())) && x.org_id == orgId.Trim() && x.org_level == orgIdlevelNo && !g.is_busplan_goal && !x.is_busplan_delgoa
                        group new { g } by new { g.pk_goal_id, g.goal_name } into g1
                        select new KeyValueGuidHelper
                        {
                            key = g1.Key.pk_goal_id,
                            value = g1.Key.goal_name
                        }).OrderBy(x => x.value).ToListAsync() :
                    await (from x in tenantDbContext.tco_goals_distribution
                        join g in tenantDbContext.tco_goals on new { t = x.fk_tenant_id, l = x.fk_goal_id }
                            equals new { t = g.fk_tenant_id, l = g.pk_goal_id }
                        where x.fk_tenant_id == userDetails.tenant_id && g.budget_year == budgetYear && x.org_id == orgId.Trim() && x.org_level == orgIdlevelNo
                        group new { g } by new { g.pk_goal_id, g.goal_name } into g1
                        select new KeyValueGuidHelper
                        {
                            key = g1.Key.pk_goal_id,
                            value = g1.Key.goal_name
                        }).OrderBy(x => x.value).ToListAsync();
            }
        }

        foreach (var item in goalsData)
        {
            dynamic row = new JObject();
            row.KeyId = item.key;
            row.ValueString = item.value;
            goalsList.Add(row);
        }
        return goalsList;
    }



    public string FormatTargetDataForAssignmentOrStrategy(string userId, int budgetYear, TargetFormttingHelper data, bool withClass = true)
    {
        return FormatTargetDataForAssignmentOrStrategyAsync(userId, budgetYear, data, withClass).GetAwaiter().GetResult();
    }



    public async Task<string> FormatTargetDataForAssignmentOrStrategyAsync(string userId, int budgetYear, TargetFormttingHelper data, bool withClass = true)
    {
        TenantDBContext dbContext = await _pUtility.GetTenantDBContextAsync();
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);

        var targetsData = await dbContext.tco_targets.Where(x => x.fk_tenant_id == userDetails.tenant_id).AsNoTracking().ToListAsync();
        var focusAreaData = await dbContext.tco_focusarea.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear).AsNoTracking().ToListAsync();
        var unsGoalsData = await dbContext.gco_un_susdev_goals.Select(x => new { x.pk_goal_id, x.goal_short_name, x.goal_name }).AsNoTracking().ToListAsync();

        List<int> focusAreaIds = new List<int>();
        List<string> unGoalIds = new List<string>();
        StringBuilder sb = new StringBuilder();

        if (data.goalsLinked.Count > 0)
        {
            int i = 0;
            var goalData = !data.isBusinessPlanGoal ? await dbContext.tco_goals.Where(x => x.fk_tenant_id == userDetails.tenant_id && data.goalsLinked.Contains(x.pk_goal_id) && x.is_busplan_goal == data.isBusinessPlanGoal).ToListAsync()
                : await dbContext.tco_goals.Where(x => x.fk_tenant_id == userDetails.tenant_id && data.goalsLinked.Contains(x.pk_goal_id)).ToListAsync();
            //if both goal and target have data then display related goals, focus area and uns goals under each target name
            if (data.targetsLinked.Count > 0)
            {
                foreach (var gl in data.goalsLinked.Distinct())
                {
                    if (i > 0 && withClass)
                    {
                        sb.Append("</br></br>");
                    }
                    if (data.targetsLinked.Count > 0 && withClass)
                    {
                        var targetIdsForGoals = targetsData.Where(x => data.targetsLinked.Contains(x.pk_target_id) && gl == x.fk_goal_id).Select(x => x.pk_target_id).ToList();
                        foreach (var t in targetIdsForGoals)
                        {
                            var content = targetsData.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_target_id == t);
                            if (content != null)
                            {
                                sb.Append("<span class='padding-right10'>" + content.target_name + "</span></br>");
                            }
                        }
                    }
                    var tcg = goalData.FirstOrDefault(x => x.pk_goal_id == gl);
                    if (tcg != null)
                    {
                        sb.Append(withClass ? "<span class='bp-grey-tag white-space-normal'>" + tcg.goal_name + "</span>" : tcg.goal_name + "</br>");
                        if (!string.IsNullOrEmpty(tcg.focus_area))
                        {
                            focusAreaIds = tcg.focus_area.Split(',').Select(int.Parse).ToList();
                        }
                        if (!string.IsNullOrEmpty(tcg.unsd_goals))
                        {
                            unGoalIds = tcg.unsd_goals.Split(',').ToList();
                        }
                    }
                    foreach (var f in focusAreaIds.Distinct())
                    {
                        var lfa = focusAreaData.FirstOrDefault(x => x.pk_id == f);
                        if (lfa != null && withClass)
                        {
                            string shortfadesc = "";
                            if (lfa.focusarea_description.Length > 32)
                            {
                                shortfadesc = lfa.focusarea_description.Substring(0, 32);
                                shortfadesc += "...";
                                sb.Append("<span class='bp-red-tag white-space-normal' title='" + lfa.focusarea_description + "'>" + shortfadesc + "</span>");
                            }
                            else
                            {
                                sb.Append("<span class='bp-red-tag white-space-normal'>" + lfa.focusarea_description + "</span>");
                            }
                        }
                    }
                    List<string> unGoalList = new List<string>();
                    foreach (var u in unGoalIds.Distinct())
                    {
                        var gt = unsGoalsData.FirstOrDefault(x => x.pk_goal_id == u);
                        var gtg = gt != null ? gt.goal_short_name : string.Empty;
                        var gtt = gt != null ? gt.goal_name : string.Empty;
                        if (withClass)
                        {
                            unGoalList.Add("<span class='bp-blue-tag white-space-normal cursor' title='" + gtt + "'>" + gtt.Substring(0, 5) + gtg + "</span>");
                        }
                    }
                    unGoalList = unGoalList.OrderBy(x => x).ToList();
                    sb.Append(string.Join("", unGoalList).ToString());
                    i++;
                }
            }
            //else if only goals selected display all goals first and then focus area and uns goals
            else
            {
                foreach (var gl in data.goalsLinked.Distinct())
                {
                    var tcg = goalData.FirstOrDefault(x => x.pk_goal_id == gl);

                    if (tcg != null)
                    {
                        sb.Append(withClass ? "<span class='bp-grey-tag white-space-normal'>" + tcg.goal_name + "</span>" : tcg.goal_name);
                        if (!string.IsNullOrEmpty(tcg.focus_area))
                        {
                            focusAreaIds.AddRange(tcg.focus_area.Split(',').Select(int.Parse).ToList());
                        }
                        if (!string.IsNullOrEmpty(tcg.unsd_goals))
                        {
                            unGoalIds.AddRange(tcg.unsd_goals.Split(',').ToList());
                        }
                    }
                }
                foreach (var f in focusAreaIds.Distinct())
                {
                    var lfa = focusAreaData.FirstOrDefault(x => x.pk_id == f);
                    if (lfa != null && withClass)
                    {
                        string shortfadesc = "";
                        if (lfa.focusarea_description.Length > 32)
                        {
                            shortfadesc = lfa.focusarea_description.Substring(0, 32);
                            shortfadesc += "...";
                            sb.Append("<span class='bp-red-tag white-space-normal' title='" + lfa.focusarea_description + "'>" + shortfadesc + "</span>");
                        }
                        else
                        {
                            sb.Append("<span class='bp-red-tag white-space-normal'>" + lfa.focusarea_description + "</span>");
                        }
                    }
                }
                List<string> unGoalList = new List<string>();
                foreach (var u in unGoalIds.Distinct())
                {
                    var gt = unsGoalsData.FirstOrDefault(x => x.pk_goal_id == u);
                    var gtg = gt != null ? gt.goal_short_name : string.Empty;
                    var gtt = gt != null ? gt.goal_name : string.Empty;
                    if (withClass)
                    {
                        unGoalList.Add("<span class='bp-blue-tag cursor' title='" + gtt + "'>" + gtt.Substring(0, 5) + gtg + "</span>");
                    }
                }
                unGoalList = unGoalList.OrderBy(x => x).ToList();
                sb.Append(string.Join("", unGoalList).ToString());
            }
        }
        //If user has selected only target(not goal)
        else
        {
            int i = 0;
            foreach (var t in data.targetsLinked)
            {
                if (i > 0 && withClass)
                {
                    sb.Append("</br></br>");
                }

                var content = targetsData.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_target_id == t);
                if (content != null && withClass)
                {
                    sb.Append("<span class='padding-right10'>" + content.target_name + "</span></br>");
                }

                var goalIdsFromTargetData = targetsData.Where(x => x.fk_tenant_id == userDetails.tenant_id && t == x.pk_target_id).Select(x => x.fk_goal_id).ToList();
                var goalData = !data.isBusinessPlanGoal ? await dbContext.tco_goals.Where(x => x.fk_tenant_id == userDetails.tenant_id && goalIdsFromTargetData.Contains(x.pk_goal_id) && x.is_busplan_goal == data.isBusinessPlanGoal).ToListAsync()
                    : await dbContext.tco_goals.Where(x => x.fk_tenant_id == userDetails.tenant_id && goalIdsFromTargetData.Contains(x.pk_goal_id)).ToListAsync();

                foreach (var gl in goalIdsFromTargetData.Distinct())
                {
                    var tcg = goalData.FirstOrDefault(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_goal_id == gl && x.is_busplan_goal == data.isBusinessPlanGoal);
                    if (tcg != null && !string.IsNullOrEmpty(tcg.goal_name))
                    {
                        sb.Append(withClass ? "<span class='bp-grey-tag white-space-normal'>" + tcg.goal_name + "</span>" : tcg.goal_name);
                    }
                    if (tcg != null)
                    {
                        if (!string.IsNullOrEmpty(tcg.focus_area))
                        {
                            focusAreaIds.AddRange(tcg.focus_area.Split(',').Select(int.Parse).ToList());
                        }
                        if (!string.IsNullOrEmpty(tcg.unsd_goals))
                        {
                            unGoalIds.AddRange(tcg.unsd_goals.Split(',').ToList());
                        }
                    }
                }
                foreach (var f in focusAreaIds.Distinct())
                {
                    var lfa = focusAreaData.FirstOrDefault(x => x.pk_id == f);
                    if (lfa != null && withClass)
                    {
                        string shortfadesc = "";
                        if (lfa.focusarea_description.Length > 32)
                        {
                            shortfadesc = lfa.focusarea_description.Substring(0, 32);
                            shortfadesc += "...";
                            sb.Append("<span class='bp-red-tag white-space-normal' title='" + lfa.focusarea_description + "'>" + shortfadesc + "</span>");
                        }
                        else
                        {
                            sb.Append("<span class='bp-red-tag white-space-normal'>" + lfa.focusarea_description + "</span>");
                        }
                    }
                }
                List<string> unGoalList = new List<string>();
                foreach (var u in unGoalIds.Distinct())
                {
                    var gt = unsGoalsData.FirstOrDefault(x => x.pk_goal_id == u);
                    var gtg = gt != null ? gt.goal_short_name : string.Empty;
                    var gtt = gt != null ? gt.goal_name : string.Empty;
                    if (withClass)
                    {
                        unGoalList.Add("<span class='bp-blue-tag white-space-normal cursor' title='" + gtt + "'>" + gtt.Substring(0, 5) + gtg + "</span>");
                    }
                }
                unGoalList = unGoalList.OrderBy(x => x).ToList();
                sb.Append(string.Join("", unGoalList).ToString());
                i++;
            }
        }
        return sb.ToString();
    }

}