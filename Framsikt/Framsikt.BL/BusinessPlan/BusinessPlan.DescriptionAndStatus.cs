using System.Text;
using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;

namespace Framsikt.BL;

public partial class BusinessPlan
{


    private Guid GetUpdatedescHistoryId(Guid assignmentID, tbiassignments tbiassignments, List<tbiassignments> assignmentData)
    {
        try
        {
            if (assignmentData.Any())
            {
                var parentAssignmentDetail = assignmentData.FirstOrDefault(x => x.orgId == x.parentOrgId && x.orgLevel.Value == x.parentOrgLevel.Value && !x.isDistributedtoLowerLevel.Value);
                var lowestLevel = parentAssignmentDetail == null ? 0 : parentAssignmentDetail.orgLevel;
                var parenthID = assignmentData.FirstOrDefault(x => x.orgLevel == lowestLevel);
                // return new History id or existing history id based on condition
                if (parenthID == null || parenthID.description_history == null)
                {
                    if (tbiassignments.description_history.HasValue && tbiassignments.description_history.Value != Guid.Empty)
                    {
                        var duphistoryidCOunt = assignmentData.Count(x => x.orgLevel > lowestLevel && x.description_history == tbiassignments.description_history.Value);
                        return duphistoryidCOunt > 1 ? Guid.NewGuid() : tbiassignments.description_history.Value;
                    }
                    else
                    {
                        return Guid.NewGuid();
                    }
                }
                else if (tbiassignments.description_history.HasValue || tbiassignments.description_history.Value != Guid.Empty)
                {
                    // in the initial version of business plan assignment we updated the same guid id to all level for descriptions,
                    // hence the below check will update the desc id for lower level in which the user is currently in
                    if (parenthID.description_history.Value.ToString().Trim().ToLower() == tbiassignments.description_history.Value.ToString().Trim().ToLower())
                    {
                        if (parenthID.assignmentId == tbiassignments.assignmentId)
                        {
                            return parenthID.description_history.Value;
                        }
                        else
                        {
                            return Guid.NewGuid();
                        }
                    }
                    else if (parenthID.description_history.Value.ToString().Trim().ToLower() != tbiassignments.description_history.Value.ToString().Trim().ToLower())
                    {
                        if (tbiassignments.description_history.Value.ToString().Trim().ToLower() == Guid.Empty.ToString().Trim().ToLower() || !tbiassignments.description_history.HasValue)
                        {
                            return Guid.NewGuid();
                        }
                        else
                        {
                            var duphistoryidCOunt = assignmentData.Count(x => x.orgLevel > lowestLevel && x.description_history == tbiassignments.description_history.Value);
                            return duphistoryidCOunt > 1 ? Guid.NewGuid() : tbiassignments.description_history.Value;
                        }
                    }
                }
                else
                {
                    return Guid.NewGuid();
                }
            }
            return Guid.Empty;
        }
        catch (Exception)
        {
            throw new InvalidOperationException("failed to get");
        }
    }

    public async Task<bool> SaveBestillingGridDescriptionsAsync(string userId, clsAssignmentsTexts objTexts)
    {
        // new value for service id based on new feature
        string unRefinedServiceId = objTexts.serviceId;
        int unRefinedServiceIdLevel = objTexts.serviceLevel;
        if (await _pUtility.IsFeatureEnabled(FeatureFlags.ck_Editor))
        {
            objTexts.serviceId = await _pUtility.RefineServiceIdValue(objTexts.serviceId, userId);
            objTexts.serviceLevel = _pUtility.RefineServiceLevelValue(objTexts.serviceLevel);
        }

        await UpdateExistingTextsDescriptionforBusinessPlanAsync(userId, objTexts);
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        var tbitaskstextsData = string.IsNullOrEmpty(objTexts.serviceId) ?
            await tenantDbContext.tbiassignmentstexts.FirstOrDefaultAsync(x => x.orgId == objTexts.orgId && x.tenantId == userDetails.tenant_id && x.budgetYear == objTexts.budgetYear && x.orgLevel == objTexts.orgLevel) :
            await tenantDbContext.tbiassignmentstexts.FirstOrDefaultAsync(x => x.orgId == objTexts.orgId && x.serviceId == objTexts.serviceId && x.tenantId == userDetails.tenant_id && x.budgetYear == objTexts.budgetYear && x.orgLevel == objTexts.orgLevel);
        if (tbitaskstextsData != null)
        {
            if (objTexts.type.ToLower() == "type1".ToLower())
            {
                tbitaskstextsData.businessPlanDescription = string.IsNullOrEmpty(objTexts.businessPlanDescription) ? string.Empty : objTexts.businessPlanDescription;
            }
            if (objTexts.type.ToLower() == "type2".ToLower())
            {
                tbitaskstextsData.strategyActionDescription = string.IsNullOrEmpty(objTexts.strategyActionDescription) ? string.Empty : objTexts.strategyActionDescription;
            }
            tbitaskstextsData.updated = DateTime.UtcNow;
            tbitaskstextsData.updatedBy = userDetails.pk_id;
            tenantDbContext.Entry(tbitaskstextsData).State = EntityState.Modified;
            await tenantDbContext.SaveChangesAsync();
            if (objTexts.type.ToLower() == "type1".ToLower() && objTexts.logHistory)
            {
                await _pUtility.SaveTextLogAsync(userId, tbitaskstextsData.businessplandescriptionhistoryid.Value, objTexts.businessPlanDescription);
            }
            if (objTexts.type.ToLower() == "type2".ToLower() && objTexts.logHistory)
            {
                await _pUtility.SaveTextLogAsync(userId, tbitaskstextsData.strategyactiondescriptionhistoryid.Value, objTexts.strategyActionDescription);
            }
        }
        else
        {
            var objtbitaskstextsData = new tbiassignmentstexts();
            var businessPlanDescriptionhistoryId = Guid.NewGuid(); var strategyActionDescriptionhistoryId = Guid.NewGuid();
            objtbitaskstextsData.tenantId = userDetails.tenant_id;
            objtbitaskstextsData.budgetYear = objTexts.budgetYear;
            objtbitaskstextsData.orgId = objTexts.orgId;
            objtbitaskstextsData.orgLevel = objTexts.orgLevel;
            objtbitaskstextsData.serviceId = string.IsNullOrEmpty(objTexts.serviceId) ? string.Empty : objTexts.serviceId;
            objtbitaskstextsData.serviceLevel = string.IsNullOrEmpty(objTexts.serviceId) || objTexts.serviceId == "-1" ? 0 : objTexts.serviceLevel;
            objtbitaskstextsData.serviceIdNew = string.IsNullOrEmpty(unRefinedServiceId) ? string.Empty : unRefinedServiceId;
            objtbitaskstextsData.serviceLevelNew = string.IsNullOrEmpty(unRefinedServiceId) || unRefinedServiceId == "-1" ? 0 : unRefinedServiceIdLevel;
            if (objTexts.type.ToLower() == "type1".ToLower())
            {
                objtbitaskstextsData.businessPlanDescription = objTexts.businessPlanDescription;
                objtbitaskstextsData.businessplandescriptionhistoryid = businessPlanDescriptionhistoryId;
            }
            if (objTexts.type.ToLower() == "type2".ToLower())
            {
                objtbitaskstextsData.strategyActionDescription = objTexts.strategyActionDescription;
                objtbitaskstextsData.strategyactiondescriptionhistoryid = strategyActionDescriptionhistoryId;
            }
            objtbitaskstextsData.instructionsdescription = string.Empty;
            objtbitaskstextsData.instructionsdescriptionhistoryid = Guid.NewGuid();
            objtbitaskstextsData.updated = DateTime.UtcNow;
            objtbitaskstextsData.updatedBy = userDetails.pk_id;
            await tenantDbContext.tbiassignmentstexts.AddAsync(objtbitaskstextsData);
            await tenantDbContext.SaveChangesAsync();
            if (objTexts.type.ToLower() == "type1".ToLower() && objTexts.logHistory)
            {
                await _pUtility.SaveTextLogAsync(userId, businessPlanDescriptionhistoryId, objTexts.businessPlanDescription);
            }
            if (objTexts.type.ToLower() == "type2".ToLower() && objTexts.logHistory)
            {
                await _pUtility.SaveTextLogAsync(userId, strategyActionDescriptionhistoryId, objTexts.strategyActionDescription);
            }
        }
        return true;
    }

    public dynamic GetBestillingGridDescriptions(string userId, string orgId, string serviceId, int budgetYear, int orgLevel)
    {
        UserData userDetails = _pUtility.GetUserDetails(userId);
        TenantDBContext tenantDbContext = _pUtility.GetTenantDBContext();
        Dictionary<string, clsLanguageString> langStringValues = _pUtility.GetLanguageStrings(userDetails.language_preference, userDetails.user_name, "BPTemplateMgmt");

        string unRefinedServiceId = serviceId;
        bool isFeatureFlag = _pUtility.IsFeatureEnabled(FeatureFlags.ck_Editor).GetAwaiter().GetResult();
        if (isFeatureFlag)
        {
            serviceId = _pUtility.RefineServiceIdValue(serviceId, userId).GetAwaiter().GetResult();
        }

        CleanDuplicateRecordsFromAssigmentText(tenantDbContext, userDetails.tenant_id, budgetYear, orgLevel, orgId, serviceId).GetAwaiter().GetResult(); // bug 173623 - added to cleanup the data 

        var tbitaskstextsData = string.IsNullOrEmpty(serviceId)
            ? tenantDbContext.tbiassignmentstexts.FirstOrDefault(x => x.orgId == orgId && x.tenantId == userDetails.tenant_id && x.budgetYear == budgetYear && x.orgLevel == orgLevel)
            : tenantDbContext.tbiassignmentstexts.FirstOrDefault(x => x.orgId == orgId && x.serviceId == serviceId && x.tenantId == userDetails.tenant_id && x.budgetYear == budgetYear && x.orgLevel == orgLevel);


        var busPlanDescNewGuid = Guid.NewGuid();
        var stratActionDescNewGuid = Guid.NewGuid();

        if (tbitaskstextsData != null)
        {
            if (tbitaskstextsData.businessplandescriptionhistoryid == null)
            {
                tbitaskstextsData.businessPlanDescription = string.Empty;
                tbitaskstextsData.businessplandescriptionhistoryid = busPlanDescNewGuid;
                tenantDbContext.SaveChanges();
            }
            if (tbitaskstextsData.strategyactiondescriptionhistoryid == null)
            {
                tbitaskstextsData.strategyActionDescription = string.Empty;
                tbitaskstextsData.strategyactiondescriptionhistoryid = stratActionDescNewGuid;
                tenantDbContext.SaveChanges();
            }
            string businessPlanDescription = tbitaskstextsData.businessPlanDescription;
            string strategyActionDescription = tbitaskstextsData.strategyActionDescription;
            dynamic descriptions = new JObject();
            descriptions.Add("businessPlanDescription", businessPlanDescription);
            descriptions.Add("strategyActionDescription", strategyActionDescription);
            descriptions.Add("businessPlanDescriptionhistoryID", tbitaskstextsData.businessplandescriptionhistoryid == null ? string.Empty : tbitaskstextsData.businessplandescriptionhistoryid.ToString());
            descriptions.Add("strategyActionDescriptionhistoryID", tbitaskstextsData.strategyactiondescriptionhistoryid == null ? string.Empty : tbitaskstextsData.strategyactiondescriptionhistoryid.ToString());
            string bdtags = GetTagsforDescriptionsText(userId, budgetYear, clsConstants.BusinessPlanNodeType.BusinsessDescription.ToString(), langStringValues);
            descriptions.Add("businessPlanTags", bdtags);
            descriptions.Add("showBusinessPlanDesc", !string.IsNullOrEmpty(bdtags));
            descriptions.Add("toShowBDTextBox", !string.IsNullOrEmpty(bdtags));
            bdtags = GetTagsforDescriptionsText(userId, budgetYear, clsConstants.BusinessPlanNodeType.StrategyAction.ToString(), langStringValues);
            descriptions.Add("strategyActionTags", bdtags);
            descriptions.Add("showBusinessPlanStrategyDesc", !string.IsNullOrEmpty(bdtags));
            return descriptions;
        }
        else
        {
            var dataToSave = new tbiassignmentstexts()
            {
                orgId = orgId,
                serviceId = serviceId,
                tenantId = userDetails.tenant_id,
                budgetYear = budgetYear,
                businessPlanDescription = string.Empty,
                businessplandescriptionhistoryid = busPlanDescNewGuid,
                strategyActionDescription = string.Empty,
                strategyactiondescriptionhistoryid = stratActionDescNewGuid,
                instructionsdescription = string.Empty,
                instructionsdescriptionhistoryid = Guid.NewGuid(),
                serviceIdNew = unRefinedServiceId,
                serviceLevel = isFeatureFlag ? 0 : null,
                updated = DateTime.UtcNow,
                orgLevel = orgLevel,
                updatedBy = userDetails.pk_id
            };
            tenantDbContext.tbiassignmentstexts.Add(dataToSave);
            tenantDbContext.SaveChanges();

            dynamic descriptions = new JObject();
            descriptions.Add("businessPlanDescription", dataToSave.businessPlanDescription);
            descriptions.Add("strategyActionDescription", dataToSave.strategyActionDescription);
            descriptions.Add("businessPlanDescriptionhistoryID", dataToSave.businessplandescriptionhistoryid);
            descriptions.Add("strategyActionDescriptionhistoryID", dataToSave.strategyactiondescriptionhistoryid);
            string bdtags = GetTagsforDescriptionsText(userId, budgetYear, clsConstants.BusinessPlanNodeType.BusinsessDescription.ToString(), langStringValues);
            descriptions.Add("businessPlanTags", bdtags);
            descriptions.Add("showBusinessPlanDesc", !string.IsNullOrEmpty(bdtags));
            descriptions.Add("toShowBDTextBox", !string.IsNullOrEmpty(bdtags));
            bdtags = GetTagsforDescriptionsText(userId, budgetYear, clsConstants.BusinessPlanNodeType.StrategyAction.ToString(), langStringValues);
            descriptions.Add("strategyActionTags", bdtags);
            descriptions.Add("showBusinessPlanStrategyDesc", !string.IsNullOrEmpty(bdtags));
            return descriptions;
        }
    }

    public async Task<BusinessPlanNodeDescriptionHelper> GetBestillingGridDescriptionsAsync(string userId, string orgId, int orgLevel, string serviceId, int budgetYear, string templateType, int templateId = -1)
    {
        BpTemplateType template = BpTemplateType.BusActivityPlan;
        switch (templateType)
        {
            case "MasterBusActPlan":
                template = BpTemplateType.BusActivityPlan;
                break;

            case "MasterBusPlanApp":
                template = BpTemplateType.BusPlanAppointment;
                break;

            case "CustomMasterBusPlan":
                template = BpTemplateType.CustomMasterBusPlan;
                break;

            case "CustomBusPlan":
                template = BpTemplateType.CustomBusPlan;
                break;
        }
        List<PublishTreeNode> masterTemplate = await GetBusPlanFlatTreeDataAsync(userId, budgetYear, template, orgLevel, orgId, templateId);
        bool businessPlanNodeDesc = CheckIfNodeExists(masterTemplate, clsConstants.BusinessPlanNodeType.BusinsessDescription.ToString());
        bool stratgeyNodeDesc = CheckIfNodeExists(masterTemplate, clsConstants.BusinessPlanNodeType.StrategyAction.ToString());
        BusinessPlanNodeDescriptionHelper descriptions = await GetDescriptionBillingGridAsync(userId, orgId, serviceId, budgetYear, businessPlanNodeDesc, stratgeyNodeDesc, orgLevel);
        return descriptions;
    }

    private async Task<BusinessPlanNodeDescriptionHelper> GetDescriptionBillingGridAsync(string userId, string orgId, string serviceId, int budgetYear, bool showBusinessPlanNodeDesc, bool showStratgeyActionNodeDesc, int orglevel)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        Dictionary<string, clsLanguageString> langStringValues = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BPTemplateMgmt");

        // new value for service id based on new feature
        string unRefinedServiceId = serviceId;
        bool isFeatureFlag = await _pUtility.IsFeatureEnabled(FeatureFlags.ck_Editor);
        if (isFeatureFlag)
        {
            serviceId = await _pUtility.RefineServiceIdValue(serviceId, userId);
        }

        await CleanDuplicateRecordsFromAssigmentText(tenantDbContext, userDetails.tenant_id, budgetYear, orglevel, orgId, serviceId); // bug 173623 - added to cleanup the data 

        var tbitaskstextsData = string.IsNullOrEmpty(serviceId)
            ? await tenantDbContext.tbiassignmentstexts.FirstOrDefaultAsync(x => x.orgId == orgId && x.tenantId == userDetails.tenant_id && x.budgetYear == budgetYear && x.orgLevel == orglevel)
            : await tenantDbContext.tbiassignmentstexts.FirstOrDefaultAsync(x => x.orgId == orgId && x.serviceId == serviceId && x.tenantId == userDetails.tenant_id && x.budgetYear == budgetYear && orglevel == x.orgLevel);

   
        var busPlanDescNewGuid = Guid.NewGuid();
        var stratActionDescNewGuid = Guid.NewGuid();

        if (tbitaskstextsData != null)
        {
            BusinessPlanNodeDescriptionHelper descriptions = new BusinessPlanNodeDescriptionHelper();
            if (tbitaskstextsData.businessplandescriptionhistoryid == null)
            {
                tbitaskstextsData.businessPlanDescription = string.Empty;
                tbitaskstextsData.businessplandescriptionhistoryid = busPlanDescNewGuid;
                await tenantDbContext.SaveChangesAsync();
            }
            string businessPlanDescription = tbitaskstextsData.businessPlanDescription;
            descriptions.businessPlanDescription = businessPlanDescription;
            descriptions.businessPlanDescriptionhistoryID = tbitaskstextsData.businessplandescriptionhistoryid == null ? string.Empty : tbitaskstextsData.businessplandescriptionhistoryid.ToString();

            if (tbitaskstextsData.strategyactiondescriptionhistoryid == null)
            {
                tbitaskstextsData.strategyActionDescription = string.Empty;
                tbitaskstextsData.strategyactiondescriptionhistoryid = stratActionDescNewGuid;
                await tenantDbContext.SaveChangesAsync();
            }
            string strategyActionDescription = tbitaskstextsData.strategyActionDescription;

            descriptions.strategyActionDescription = strategyActionDescription;
            descriptions.strategyActionDescriptionhistoryID = tbitaskstextsData.strategyactiondescriptionhistoryid == null ? string.Empty : tbitaskstextsData.strategyactiondescriptionhistoryid.ToString();

            descriptions.showBusinessPlanDesc = showBusinessPlanNodeDesc;
            descriptions.showBusinessPlanStrategyDesc = showStratgeyActionNodeDesc;

            return descriptions;
        }
        else
        {
            var dataToSave = new tbiassignmentstexts()
            {
                orgId = orgId,
                serviceId = serviceId,
                tenantId = userDetails.tenant_id,
                budgetYear = budgetYear,
                instructionsdescription = string.Empty,
                instructionsdescriptionhistoryid = Guid.NewGuid(),
                businessPlanDescription = string.Empty,
                businessplandescriptionhistoryid = busPlanDescNewGuid,
                strategyActionDescription = string.Empty,
                strategyactiondescriptionhistoryid = stratActionDescNewGuid,
                serviceIdNew = unRefinedServiceId,
                serviceLevel = isFeatureFlag ? 0 : null,
                updated = DateTime.UtcNow,
                orgLevel = orglevel,
                updatedBy = userDetails.pk_id
            };
            await tenantDbContext.tbiassignmentstexts.AddAsync(dataToSave);
            await tenantDbContext.SaveChangesAsync();

            BusinessPlanNodeDescriptionHelper descriptions = new BusinessPlanNodeDescriptionHelper();

            descriptions.businessPlanDescription = dataToSave.businessPlanDescription;
            descriptions.businessPlanDescriptionhistoryID = dataToSave.businessplandescriptionhistoryid.ToString();

            descriptions.strategyActionDescription = dataToSave.strategyActionDescription;
            descriptions.strategyActionDescriptionhistoryID = dataToSave.strategyactiondescriptionhistoryid.ToString();

            descriptions.showBusinessPlanDesc = showBusinessPlanNodeDesc;
            descriptions.showBusinessPlanStrategyDesc = showStratgeyActionNodeDesc;
            return descriptions;
        }
    }



    private string GetTagsforDescriptionsText(string userId, int budgetYear, string type, Dictionary<string, clsLanguageString> langStringValuesBP)
    {
        if (type == clsConstants.BusinessPlanNodeType.BusinsessDescription.ToString())
        {
            var businessPlanNode_busPlanAppointment = CheckIfNodeExists(userId, budgetYear, BpTemplateType.BusPlanAppointment, clsConstants.BusinessPlanNodeType.BusinsessDescription.ToString());
            var businessPlanNode_BusActivityPlan = CheckIfNodeExists(userId, budgetYear, BpTemplateType.BusActivityPlan, clsConstants.BusinessPlanNodeType.BusinsessDescription.ToString());

            if (businessPlanNode_busPlanAppointment && businessPlanNode_BusActivityPlan)
            {
                return "<span class='bp-green-tag'>" + langStringValuesBP.FirstOrDefault(v => v.Key == "BusinessPlan_Tab1").Value.LangText + "</span>&nbsp;" +
                       "<span class='bp-green-tag'>" + langStringValuesBP.FirstOrDefault(v => v.Key == "BusinessPlan_Tab2").Value.LangText + "</span>";
            }
            if (businessPlanNode_busPlanAppointment && !businessPlanNode_BusActivityPlan)
            {
                return "<span class='bp-green-tag'>" + langStringValuesBP.FirstOrDefault(v => v.Key == "BusinessPlan_Tab1").Value.LangText + "</span>";
            }
            if (!businessPlanNode_busPlanAppointment && businessPlanNode_BusActivityPlan)
            {
                return "<span class='bp-green-tag'>" + langStringValuesBP.FirstOrDefault(v => v.Key == "BusinessPlan_Tab2").Value.LangText + "</span>";
            }
            if (!businessPlanNode_busPlanAppointment && !businessPlanNode_BusActivityPlan)
            {
                return string.Empty;
            }
        }
        else
        {
            var stratgeyNode_BusPlanAppointment = CheckIfNodeExists(userId, budgetYear, BpTemplateType.BusPlanAppointment, clsConstants.BusinessPlanNodeType.StrategyAction.ToString());
            var stratgeyNode_BusActivityPlan = CheckIfNodeExists(userId, budgetYear, BpTemplateType.BusActivityPlan, clsConstants.BusinessPlanNodeType.StrategyAction.ToString());

            if (stratgeyNode_BusPlanAppointment && stratgeyNode_BusActivityPlan)
            {
                return "<span class='bp-green-tag'>" + langStringValuesBP.FirstOrDefault(v => v.Key == "BusinessPlan_Tab1").Value.LangText + "</span>&nbsp;" +
                       "<span class='bp-green-tag'>" + langStringValuesBP.FirstOrDefault(v => v.Key == "BusinessPlan_Tab2").Value.LangText + "</span>";
            }
            if (stratgeyNode_BusPlanAppointment && !stratgeyNode_BusActivityPlan)
            {
                return "<span class='bp-green-tag'>" + langStringValuesBP.FirstOrDefault(v => v.Key == "BusinessPlan_Tab1").Value.LangText + "</span>";
            }
            if (!stratgeyNode_BusPlanAppointment && stratgeyNode_BusActivityPlan)
            {
                return "<span class='bp-green-tag'>" + langStringValuesBP.FirstOrDefault(v => v.Key == "BusinessPlan_Tab2").Value.LangText + "</span>";
            }
            if (!stratgeyNode_BusPlanAppointment && !stratgeyNode_BusActivityPlan)
            {
                return string.Empty;
            }
        }
        return string.Empty;
    }



    public async Task<bool> LiveStatusDescription(string userId, LiveStatusDesc objTexts)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        var record = await tenantDbContext.tbi_assignment_live_status.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.fk_assignment_id == objTexts.assignmentId);
        if (record != null)
        {
            record.description = objTexts.liveStatusDescription;
            record.status = objTexts.liveStatus;
            record.updated_by = userDetails.pk_id;
            record.updated = DateTime.UtcNow;
            await tenantDbContext.SaveChangesAsync();
            if (objTexts.logHistory)
            {
                await _pUtility.SaveTextLogAsync(userId, record.description_history_id, objTexts.liveStatusDescription);
            }
        }
        else
        {
            tbi_assignment_live_status newRecord = new tbi_assignment_live_status
            {
                fk_assignment_id = objTexts.assignmentId,
                year = objTexts.budgetYear,
                status = objTexts.liveStatus,
                description_history_id = objTexts.liveStatusDescId,
                description = objTexts.liveStatusDescription,
                fk_tenant_id = userDetails.tenant_id,
                updated_by = userDetails.pk_id,
                updated = DateTime.UtcNow
            };
            await tenantDbContext.tbi_assignment_live_status.AddAsync(newRecord);
            await tenantDbContext.SaveChangesAsync();
            if (objTexts.logHistory)
            {
                await _pUtility.SaveTextLogAsync(userId, newRecord.description_history_id, objTexts.liveStatusDescription);
            }
        }

        if (objTexts.updateResponsible)
        {
            var assignmentRecord = await tenantDbContext.tbiassignments.FirstOrDefaultAsync(x => x.tenantId == userDetails.tenant_id && x.assignmentId == objTexts.assignmentId);

            assignmentRecord.userAssignedTo = objTexts.responsible;
            assignmentRecord.updated = DateTime.UtcNow;
            assignmentRecord.updatedBy = userDetails.pk_id;
            await tenantDbContext.SaveChangesAsync();
        }
        return true;
    }



    public dynamic GetDescriptionforAssignment(string userId, Guid uniqueAssignmentID, string orgId, int orgLevel,
        int budgetYear, bool isAssignmentGrid = false)
    {
        var result =
            GetDescriptionforAssignmentAsync(userId, uniqueAssignmentID, orgId, orgLevel, budgetYear,
                isAssignmentGrid).GetAwaiter().GetResult();
        return result;
    }



    public async Task<dynamic> GetDescriptionforAssignmentAsync(string userId, Guid uniqueAssignmentID, string orgId, int orgLevel, int budgetYear, bool isAssignmentGrid = false)
    {
        try
        {
            UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
            TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
            var orgVersionContent = await _pUtility.GetOrgVersionSpecificContentAsync(userId, _pUtility.GetForecastPeriod(budgetYear, 1));
            List<tco_org_hierarchy> lstOrgHierarchy = orgVersionContent.lstOrgHierarchy;
            var assignmentData = await tenantDbContext.tbiassignments.Where(x => x.uniqueassignmentId == uniqueAssignmentID && x.budgetYear == budgetYear && x.tenantId == userDetails.tenant_id).ToListAsync();
            var fpassignmentdescriptions = await tenantDbContext.tco_assignments_descriptions.Where(x => x.fk_tenant_id == userDetails.tenant_id).ToListAsync();
            var descriptionArray = new JArray();
            int currentOrgLevel = orgLevel;
            switch (orgLevel)
            {
                case 1:
                    lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_1 == orgId).ToList();
                    break;

                case 2:
                    lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_2 == orgId).ToList();
                    break;

                case 3:
                    lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_3 == orgId).ToList();
                    break;

                case 4:
                    lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_4 == orgId).ToList();
                    break;

                case 5:
                    lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_5 == orgId).ToList();
                    break;

                case 6:
                    lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_6 == orgId).ToList();
                    break;

                case 7:
                    lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_7 == orgId).ToList();
                    break;

                case 8:
                    lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_8 == orgId).ToList();
                    break;

                default:
                    break;
            }

            var hierarchyList = (from p in lstOrgHierarchy
                select new
                {
                    orgID_1 = p.org_id_1,
                    orgName_1 = p.org_name_1,
                    orgLevel_1 = 1,
                    orgID_2 = p.org_id_2,
                    orgName_2 = p.org_name_2,
                    orgLevel_2 = 2,
                    orgID_3 = p.org_id_3,
                    orgName_3 = p.org_name_3,
                    orgLevel_3 = 3,
                    orgID_4 = p.org_id_4,
                    orgName_4 = p.org_name_4,
                    orgLevel_4 = 4,
                    orgID_5 = p.org_id_5,
                    orgName_5 = p.org_name_5,
                    orgLevel_5 = 5,
                    orgID_6 = p.org_id_6,
                    orgName_6 = p.org_name_6,
                    orgLevel_6 = 6,
                    orgID_7 = p.org_id_7,
                    orgName_7 = p.org_name_7,
                    orgLevel_7 = 7,
                    orgID_8 = p.org_id_8,
                    orgName_8 = p.org_name_8,
                    orgLevel_8 = 8
                }).Distinct().ToList();
            List<clsAssignmentsTexts> instructionsHierarchyList = new List<clsAssignmentsTexts>();

            while (orgLevel != 0)
            {
                switch (orgLevel)
                {
                    case 1:
                        instructionsHierarchyList.Add(new clsAssignmentsTexts
                        {
                            orgId = hierarchyList.FirstOrDefault().orgID_1,
                            orgLevel = hierarchyList.FirstOrDefault().orgLevel_1,
                        });
                        break;

                    case 2:
                        instructionsHierarchyList.Add(new clsAssignmentsTexts
                        {
                            orgId = hierarchyList.FirstOrDefault().orgID_2,
                            orgLevel = hierarchyList.FirstOrDefault().orgLevel_2,
                        });
                        break;

                    case 3:
                        instructionsHierarchyList.Add(new clsAssignmentsTexts
                        {
                            orgId = hierarchyList.FirstOrDefault().orgID_3,
                            orgLevel = hierarchyList.FirstOrDefault().orgLevel_3,
                        });
                        break;

                    case 4:
                        instructionsHierarchyList.Add(new clsAssignmentsTexts
                        {
                            orgId = hierarchyList.FirstOrDefault().orgID_4,
                            orgLevel = hierarchyList.FirstOrDefault().orgLevel_4,
                        });
                        break;

                    case 5:
                        instructionsHierarchyList.Add(new clsAssignmentsTexts
                        {
                            orgId = hierarchyList.FirstOrDefault().orgID_5,
                            orgLevel = hierarchyList.FirstOrDefault().orgLevel_5,
                        });
                        break;

                    case 6:
                        instructionsHierarchyList.Add(new clsAssignmentsTexts
                        {
                            orgId = hierarchyList.FirstOrDefault().orgID_6,
                            orgLevel = hierarchyList.FirstOrDefault().orgLevel_6,
                        });
                        break;

                    case 7:
                        instructionsHierarchyList.Add(new clsAssignmentsTexts
                        {
                            orgId = hierarchyList.FirstOrDefault().orgID_7,
                            orgLevel = hierarchyList.FirstOrDefault().orgLevel_7,
                        });
                        break;

                    case 8:
                        instructionsHierarchyList.Add(new clsAssignmentsTexts
                        {
                            orgId = hierarchyList.FirstOrDefault().orgID_8,
                            orgLevel = hierarchyList.FirstOrDefault().orgLevel_8,
                        });
                        break;

                    default:
                        break;
                }
                orgLevel = orgLevel - 1;
            }

            var hierarchytoIterate = instructionsHierarchyList.Select(x => new
            {
                id = x.orgId,
                level = x.orgLevel
            }).OrderBy(x => x.level).ToList();

            int y = 1;
            if (!isAssignmentGrid)
            {
                if (assignmentData.Any())
                {
                    foreach (var item in hierarchytoIterate)
                    {
                        var descInfo = assignmentData.FirstOrDefault(x => x.orgId == item.id && x.orgLevel == item.level);
                        if (descInfo != null)
                        {
                            dynamic descriptionObj = new JObject();
                            switch (item.level)
                            {
                                case 1:
                                    descriptionObj.editorTitle = hierarchyList.FirstOrDefault(x => x.orgID_1 == item.id).orgName_1;
                                    break;

                                case 2:
                                    descriptionObj.editorTitle = hierarchyList.FirstOrDefault(x => x.orgID_2 == item.id).orgName_2;
                                    break;

                                case 3:
                                    descriptionObj.editorTitle = hierarchyList.FirstOrDefault(x => x.orgID_3 == item.id).orgName_3;
                                    break;

                                case 4:
                                    descriptionObj.editorTitle = hierarchyList.FirstOrDefault(x => x.orgID_4 == item.id).orgName_4;
                                    break;

                                case 5:
                                    descriptionObj.editorTitle = hierarchyList.FirstOrDefault(x => x.orgID_5 == item.id).orgName_5;
                                    break;

                                case 6:
                                    descriptionObj.editorTitle = hierarchyList.FirstOrDefault(x => x.orgID_6 == item.id).orgName_6;
                                    break;

                                case 7:
                                    descriptionObj.editorTitle = hierarchyList.FirstOrDefault(x => x.orgID_7 == item.id).orgName_7;
                                    break;

                                case 8:
                                    descriptionObj.editorTitle = hierarchyList.FirstOrDefault(x => x.orgID_8 == item.id).orgName_8;
                                    break;

                                default:
                                    descriptionObj.editorTitle = string.Empty;
                                    break;
                            }
                            descriptionObj.editorDescription = !string.IsNullOrEmpty(descInfo.description) ? descInfo.description : string.Empty;
                            descriptionObj.editorDescriptionId = (descInfo.description_history.HasValue && descInfo.description_history != Guid.Empty) ? descInfo.description_history.Value :
                                await SaveAssignmentEditorDescriptionId(descInfo, userId, Guid.NewGuid());
                            descriptionObj.iseditorEnabled = y == hierarchytoIterate.Count();
                            descriptionArray.Add(descriptionObj);
                        }
                        y++;
                    }
                }
                return descriptionArray;
            }
            else
            {
                if (assignmentData.Any())
                {
                    return await GetDescriptionforAssignmentWithOrgNameAsync(userId, uniqueAssignmentID, orgId, currentOrgLevel, orgVersionContent, assignmentData.ToList(), fpassignmentdescriptions);
                }
                return string.Empty;
            }
        }
        catch (Exception)
        {
            throw;
        }
    }



    public async Task<Guid> SaveAssignmentEditorDescriptionId(tbiassignments assignmentData, string userId, Guid assignmentEditorId)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        assignmentData.description_history = assignmentEditorId;
        await tenantDbContext.SaveChangesAsync();
        return assignmentEditorId;
    }



    private string GetFormattedAssignmentDesc(tco_assignments_descriptions desc)
    {
        if (desc != null && !string.IsNullOrEmpty(desc.assignment_description))
        {
            return desc.assignment_description;
        }
        return string.Empty;
    }



    public dynamic GetDescriptionforAssignmentWithOrgName(string userId,
        Guid uniqueAssignmentID,
        string orgId,
        int orgLevel,
        ClsOrgVersionSpecificContent orgVersionContent,
        List<tbiassignments> masterAssignments,
        List<tco_assignments_descriptions> masterfpAssignmentDescriptions,
        bool isMrDoc = false,
        bool isClimateAssignments = false)
    {
        var result = GetDescriptionforAssignmentWithOrgNameAsync(userId, uniqueAssignmentID, orgId, orgLevel,
                orgVersionContent, masterAssignments, masterfpAssignmentDescriptions, isMrDoc, isClimateAssignments)
            .GetAwaiter().GetResult();
        return result;
    }



    public async Task<dynamic> GetDescriptionforAssignmentWithOrgNameAsync(string userId,
        Guid uniqueAssignmentID,
        string orgId,
        int orgLevel,
        ClsOrgVersionSpecificContent orgVersionContent,
        List<tbiassignments> masterAssignments,
        List<tco_assignments_descriptions> masterfpAssignmentDescriptions,
        bool isMrDoc = false,
        bool isClimateAssignments = false)
    {
        try
        {
            UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
            List<tco_org_hierarchy> lstOrgHierarchy = orgVersionContent.lstOrgHierarchy;
            var assignmentData = masterAssignments.Where(x => x.uniqueassignmentId == uniqueAssignmentID).ToList();
            var assignmentDesc = (from a in assignmentData
                select new
                {
                    orgId = a.orgId,
                    orgLevel = a.orgLevel,
                    description = a.description
                }).ToList();

            if (assignmentData.FirstOrDefault(t => t.isBudgetProposal.HasValue && t.isBudgetProposal.Value) != null)
            {
                var allAssignments = assignmentData.Select(x => x.assignmentId).Distinct().ToList();
                var fpAssignmentDescriptions = (from a in masterfpAssignmentDescriptions.Where(x => x.fk_assignment_id.HasValue &&
                        allAssignments.Contains(x.fk_assignment_id.Value))
                    select a).ToList();
                Dictionary<string, clsLanguageString> langStringsMonRep = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "MonthlyReport");
                bool isBusPlan = await _pUtility.IsActiveModuleAsync(userDetails.user_name, 9);
                assignmentDesc = (from a in assignmentData
                    join b in fpAssignmentDescriptions on new { a = a.tenantId, b = a.assignmentId }
                        equals new { a = b.fk_tenant_id ?? 0, b = b.fk_assignment_id ?? Guid.Empty } into res
                    from descRes in res.DefaultIfEmpty()
                    where a.uniqueassignmentId == uniqueAssignmentID
                    select new
                    {
                        orgId = a.orgId,
                        orgLevel = a.orgLevel,
                        description = isMrDoc ? GetFormattedAssignmentDesc(descRes) :
                            FormatBudPropBusplanAssignmentText(descRes, a.description, isClimateAssignments, isBusPlan, langStringsMonRep)
                    }).ToList();
            }

            var descriptionArray = new JArray();
            switch (orgLevel)
            {
                case 1:
                    lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_1 == orgId).ToList();
                    break;

                case 2:
                    lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_2 == orgId).ToList();
                    break;

                case 3:
                    lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_3 == orgId).ToList();
                    break;

                case 4:
                    lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_4 == orgId).ToList();
                    break;

                case 5:
                    lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_5 == orgId).ToList();
                    break;

                case 6:
                    lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_6 == orgId).ToList();
                    break;

                case 7:
                    lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_7 == orgId).ToList();
                    break;

                case 8:
                    lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_8 == orgId).ToList();
                    break;

                default:
                    break;
            }

            var hierarchyList = (from p in lstOrgHierarchy
                select new
                {
                    orgID_1 = p.org_id_1,
                    orgName_1 = p.org_name_1,
                    orgLevel_1 = 1,
                    orgID_2 = p.org_id_2,
                    orgName_2 = p.org_name_2,
                    orgLevel_2 = 2,
                    orgID_3 = p.org_id_3,
                    orgName_3 = p.org_name_3,
                    orgLevel_3 = 3,
                    orgID_4 = p.org_id_4,
                    orgName_4 = p.org_name_4,
                    orgLevel_4 = 4,
                    orgID_5 = p.org_id_5,
                    orgName_5 = p.org_name_5,
                    orgLevel_5 = 5,
                    orgID_6 = p.org_id_6,
                    orgName_6 = p.org_name_6,
                    orgLevel_6 = 6,
                    orgID_7 = p.org_id_7,
                    orgName_7 = p.org_name_7,
                    orgLevel_7 = 7,
                    orgID_8 = p.org_id_8,
                    orgName_8 = p.org_name_8,
                    orgLevel_8 = 8
                }).Distinct().ToList();
            List<clsAssignmentDesc> instructionsHierarchyList = new List<clsAssignmentDesc>();

            while (orgLevel != 0)
            {
                switch (orgLevel)
                {
                    case 1:
                        instructionsHierarchyList.Add(new clsAssignmentDesc
                        {
                            orgId = hierarchyList.FirstOrDefault().orgID_1,
                            orgLevel = hierarchyList.FirstOrDefault().orgLevel_1,
                            orgName = hierarchyList.FirstOrDefault().orgName_1,
                        });
                        break;

                    case 2:
                        instructionsHierarchyList.Add(new clsAssignmentDesc
                        {
                            orgId = hierarchyList.FirstOrDefault().orgID_2,
                            orgLevel = hierarchyList.FirstOrDefault().orgLevel_2,
                            orgName = hierarchyList.FirstOrDefault().orgName_2,
                        });
                        break;

                    case 3:
                        instructionsHierarchyList.Add(new clsAssignmentDesc
                        {
                            orgId = hierarchyList.FirstOrDefault().orgID_3,
                            orgLevel = hierarchyList.FirstOrDefault().orgLevel_3,
                            orgName = hierarchyList.FirstOrDefault().orgName_3,
                        });
                        break;

                    case 4:
                        instructionsHierarchyList.Add(new clsAssignmentDesc
                        {
                            orgId = hierarchyList.FirstOrDefault().orgID_4,
                            orgLevel = hierarchyList.FirstOrDefault().orgLevel_4,
                            orgName = hierarchyList.FirstOrDefault().orgName_4,
                        });
                        break;

                    case 5:
                        instructionsHierarchyList.Add(new clsAssignmentDesc
                        {
                            orgId = hierarchyList.FirstOrDefault().orgID_5,
                            orgLevel = hierarchyList.FirstOrDefault().orgLevel_5,
                            orgName = hierarchyList.FirstOrDefault().orgName_5,
                        });
                        break;

                    case 6:
                        instructionsHierarchyList.Add(new clsAssignmentDesc
                        {
                            orgId = hierarchyList.FirstOrDefault().orgID_6,
                            orgLevel = hierarchyList.FirstOrDefault().orgLevel_6,
                            orgName = hierarchyList.FirstOrDefault().orgName_6,
                        });
                        break;

                    case 7:
                        instructionsHierarchyList.Add(new clsAssignmentDesc
                        {
                            orgId = hierarchyList.FirstOrDefault().orgID_7,
                            orgLevel = hierarchyList.FirstOrDefault().orgLevel_7,
                            orgName = hierarchyList.FirstOrDefault().orgName_7,
                        });
                        break;

                    case 8:
                        instructionsHierarchyList.Add(new clsAssignmentDesc
                        {
                            orgId = hierarchyList.FirstOrDefault().orgID_8,
                            orgLevel = hierarchyList.FirstOrDefault().orgLevel_8,
                            orgName = hierarchyList.FirstOrDefault().orgName_8,
                        });
                        break;

                    default:
                        break;
                }
                orgLevel = orgLevel - 1;
            }

            var hierarchytoIterate = instructionsHierarchyList.Select(x => new
            {
                id = x.orgId,
                level = x.orgLevel,
                name = x.orgName
            }).OrderBy(x => x.level).ToList();

            StringBuilder sb = new StringBuilder();
            bool flag = false;
            dynamic descData = new JArray();
            if (assignmentDesc.Any())
            {
                foreach (var item in hierarchytoIterate)
                {
                    var descInfo = assignmentDesc.FirstOrDefault(x => x.orgId == item.id && x.orgLevel == item.level);
                    if (descInfo != null && !string.IsNullOrEmpty(descInfo.description))
                    {
                        dynamic ob = new JObject();
                        ob.name = item.name;
                        ob.description = descInfo.description;
                        descData.Add(ob);
                    }
                }
            }
            if (descData.Count > 1 || !isMrDoc)
            {
                flag = true;
            }
            foreach (var item in descData)
            {
                if (flag)
                {
                    sb.Append("<b>" + item.name.ToString() + "</b>\n");
                }

                var description = item.description.ToString().Replace("<br>", "");
                sb.Append(!string.IsNullOrEmpty(description) ? description : string.Empty);
                sb.Append($"<br>");
            }

            return sb.ToString();
        }
        catch (Exception)
        {
            throw;
        }
    }



    private string FormatBudPropBusplanAssignmentText(tco_assignments_descriptions budpropDesc, string busplanDesc, bool isClimateAssignments, bool isBusPlan, Dictionary<string, clsLanguageString> langStringsMonRep)
    {
        string result = string.Empty;

        //For description from BUDPROP
        if (budpropDesc != null && !string.IsNullOrEmpty(budpropDesc.assignment_description))
        {
            result = budpropDesc.assignment_description;
        }

        //For description from BUSPLAN
        if (!isClimateAssignments && isBusPlan && !string.IsNullOrEmpty(busplanDesc))
        {
            if (!string.IsNullOrEmpty(result))
            {
                result = result + "</br>";
            }
            result = result + "<b>" + langStringsMonRep["mr_doc_assignment_busplan_desc"].LangText + "</b>\n" + busplanDesc;
        }

        return result;
    }



    private async Task<dynamic> GetBudProposalAssignmentDescriptionsAsync(string userId, tbiassignments currentAssignment, int budgetYear, string serviceId)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        Dictionary<string, clsLanguageString> langStringValues = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BusinessPlan");
        var orgVersionContent = await _pUtility.GetOrgVersionSpecificContentAsync(userId, _pUtility.GetForecastPeriod(budgetYear, 1));
        List<tco_org_hierarchy> lstOrgHierarchy = orgVersionContent.lstOrgHierarchy;

        dynamic descriptionObj = new JObject();
        var descriptionArray = new JArray();
        Guid assignmentid = currentAssignment.assignmentId;
        string orgId = currentAssignment.orgId;
        int orgLevel = currentAssignment.orgLevel.Value;
        List<string> higherOrgIdList = GetAllPreviousOrgIdsForCurrentOrgId(orgId, orgLevel, lstOrgHierarchy);
        var descriptionData = string.IsNullOrEmpty(serviceId) ? await (from a in tenantDbContext.tbiassignments
            join b in tenantDbContext.tco_assignments_descriptions on new { a = a.tenantId, b = a.assignmentId }
                equals new { a = b.fk_tenant_id ?? 0, b = b.fk_assignment_id ?? Guid.Empty } into res
            from descRes in res.DefaultIfEmpty()
            where a.tenantId == userDetails.tenant_id && a.uniqueassignmentId == currentAssignment.uniqueassignmentId && a.budgetYear == budgetYear
                  && (a.serviceId.ToLower() == "all" || a.serviceId.ToLower() == "-1" || string.IsNullOrEmpty(a.serviceId.Trim()) || a.serviceId == serviceId)
            group new { a, descRes } by new
            {
                a.assignmentId,
                a.uniqueassignmentId,
                a.orgId,
                a.orgLevel,
                description = descRes != null ? descRes.assignment_description : string.Empty,
                descHistoryId = descRes != null ? descRes.assignment_description_historyId : Guid.Empty,
                isParent = (a.parentOrgId == a.orgId && a.parentOrgLevel == a.orgLevel),
                descriptionPkId = descRes != null ? descRes.pk_id : 0
            } into g
            select new
            {
                assignmentId = g.Key.assignmentId,
                uniqueAssignmentId = g.Key.uniqueassignmentId,
                orgId = g.Key.orgId,
                orgLevel = g.Key.orgLevel,
                description = g.Key.description,
                historyId = g.Key.descHistoryId,
                isParent = g.Key.isParent,
                descriptionPkId = g.Key.descriptionPkId
            }).ToListAsync() : await (from a in tenantDbContext.tbiassignments
            join b in tenantDbContext.tco_assignments_descriptions on new { a = a.tenantId, b = a.assignmentId }
                equals new { a = b.fk_tenant_id ?? 0, b = b.fk_assignment_id ?? Guid.Empty } into res
            from descRes in res.DefaultIfEmpty()
            where a.tenantId == userDetails.tenant_id && a.uniqueassignmentId == currentAssignment.uniqueassignmentId && a.budgetYear == budgetYear
                  && a.serviceId == serviceId
            group new { a, descRes } by new
            {
                a.assignmentId,
                a.uniqueassignmentId,
                a.orgId,
                a.orgLevel,
                description = descRes != null ? descRes.assignment_description : string.Empty,
                descHistoryId = descRes != null ? descRes.assignment_description_historyId : Guid.Empty,
                isParent = (a.parentOrgId == a.orgId && a.parentOrgLevel == a.orgLevel),
                descriptionPkId = descRes != null ? descRes.pk_id : 0
            } into g
            select new
            {
                assignmentId = g.Key.assignmentId,
                uniqueAssignmentId = g.Key.uniqueassignmentId,
                orgId = g.Key.orgId,
                orgLevel = g.Key.orgLevel,
                description = g.Key.description,
                historyId = g.Key.descHistoryId,
                isParent = g.Key.isParent,
                descriptionPkId = g.Key.descriptionPkId
            }).ToListAsync();

        //Display 3 editors for budget proposal assignments #98342
        //0 -> Text box from parent level in FINPLAN should show in BUSPLAN - Non editable
        //1 -> Text from BUSPLAN at parent level
        //2 -> Text box for current level finplan text - Non editable
        //3 -> Text box for current busplan text at parent lavel -> Editable, default as finplan text
        //updated the description section order with orgLevel #131755

        var descriptionsListToBeShowed = descriptionData.Where(x => x.assignmentId == assignmentid || x.isParent).ToList();
        foreach (string higherOrgId in higherOrgIdList)
        {
            descriptionsListToBeShowed.AddRange(descriptionData.Where(x => x.orgLevel < orgLevel && x.orgId.Equals(higherOrgId)).ToList());
        }
        descriptionsListToBeShowed = descriptionsListToBeShowed.Distinct().OrderBy(x => x.orgId).ToList();
        StringBuilder sb = new StringBuilder();

        //check for  FINPLAN text section for org.levels that are only used in BUSPLAN (lower than FINPLAN_LEVEL_2) and (lower than FINPLAN_LEVEL_1) for servicesetup
        string fpLevel1 = await _pUtility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_1");
        string fpLevel2 = await _pUtility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_2");

        bool isOrgServiceSetup = ((!string.IsNullOrEmpty(fpLevel1) && fpLevel1.StartsWith("org")) || (!string.IsNullOrEmpty(fpLevel2) && fpLevel2.StartsWith("ser")));
        int fpOrgLevel = isOrgServiceSetup ? int.Parse(fpLevel1.Split('_')[2]) : int.Parse(fpLevel2.Split('_')[2]);
        if (isOrgServiceSetup && (!string.IsNullOrEmpty(fpLevel2) && fpLevel2.StartsWith("org")))
        {
            fpOrgLevel = int.Parse(fpLevel2.Split('_')[2]);
        }
        foreach (var item in descriptionsListToBeShowed)
        {
            descriptionObj = new JObject();
            sb = new StringBuilder();
            if (!string.IsNullOrEmpty(fpLevel1) || !string.IsNullOrEmpty(fpLevel2))
            {
                if (!item.isParent && !(item.orgLevel < orgLevel))
                {
                    //current level FinPlan text section
                    if (item.orgLevel.Value <= fpOrgLevel)
                    {
                        sb.Append(!string.IsNullOrEmpty(item.description) ? item.description : string.Empty);
                        descriptionObj.descPkId = item.descriptionPkId;
                        descriptionObj.editorDescriptionId = item.historyId;
                        descriptionObj.editorTitle = GetOrgName(item.orgLevel.Value, item.orgId, lstOrgHierarchy) + " (" + langStringValues["BP_Assignment_CurrentLevel_FinText"].LangText + ")";
                        if (!string.IsNullOrEmpty(item.description))
                        {
                            sb.Append($"<br>");
                        }
                        descriptionObj.iseditorEnabled = false;
                        descriptionObj.editorDescription = sb.ToString();
                        descriptionObj.order = item.orgLevel.Value - 1;
                        descriptionArray.Add(descriptionObj);
                    }

                }
                else
                {
                    //FinPlan text section
                    if (item.orgLevel.Value <= fpOrgLevel)
                    {
                        sb = new StringBuilder();
                        descriptionObj = new JObject();
                        sb.Append(!string.IsNullOrEmpty(item.description) ? item.description : string.Empty);
                        descriptionObj.descPkId = item.descriptionPkId;
                        descriptionObj.editorDescriptionId = item.historyId;
                        string editorTitleParentLevelText = descriptionsListToBeShowed.Count == 1 ? " (" + langStringValues["BP_Assignment_CurrentAndParentLevel_FinText"].LangText + ")" : " (" + langStringValues["BP_Assignment_ParentLevel_FinText"].LangText + ")";
                        descriptionObj.editorTitle = GetOrgName(item.orgLevel.Value, item.orgId, lstOrgHierarchy) + editorTitleParentLevelText;
                        if (!string.IsNullOrEmpty(item.description))
                        {
                            sb.Append($"<br>");
                        }
                        descriptionObj.iseditorEnabled = false;
                        descriptionObj.editorDescription = sb.ToString();
                        descriptionObj.order = item.orgLevel.Value;
                        descriptionArray.Add(descriptionObj);
                    }

                    //BUSPLAN text from parent level

                    var busplanTextAtParentLevel = await tenantDbContext.tbiassignments.FirstOrDefaultAsync(x => x.tenantId == userDetails.tenant_id && x.assignmentId == item.assignmentId);
                    if (busplanTextAtParentLevel != null && currentAssignment.assignmentId != busplanTextAtParentLevel.assignmentId)
                    {
                        sb = new StringBuilder();
                        descriptionObj = new JObject();
                        sb.Append(!string.IsNullOrEmpty(busplanTextAtParentLevel.description) ? busplanTextAtParentLevel.description : string.Empty);
                        descriptionObj.editorDescriptionId = busplanTextAtParentLevel.description_history.Value;
                        descriptionObj.editorTitle = GetOrgName(item.orgLevel.Value, item.orgId, lstOrgHierarchy) + " (" + langStringValues["BP_Assignment_parentLevel_BusplanText"].LangText + ")";
                        if (!string.IsNullOrEmpty(busplanTextAtParentLevel.description))
                        {
                            sb.Append($"<br>");
                        }
                        descriptionObj.iseditorEnabled = false;
                        descriptionObj.editorDescription = sb.ToString();
                        descriptionObj.order = item.orgLevel.Value;
                        descriptionArray.Add(descriptionObj);
                    }

                    //Display editable editor to insert busplan text for finplan assignments at all levels
                    if (item.isParent || isOrgServiceSetup)
                    {
                        sb = new StringBuilder();
                        descriptionObj = new JObject();
                        sb.Append(string.IsNullOrEmpty(currentAssignment.description) ? string.Empty : currentAssignment.description);

                        if (currentAssignment.description_history.Value == Guid.Empty)
                        {
                            currentAssignment.description_history = Guid.NewGuid();
                            await tenantDbContext.SaveChangesAsync();
                        }
                        descriptionObj.editorDescriptionId = currentAssignment.description_history.Value;
                        descriptionObj.editorTitle = GetOrgName(currentAssignment.orgLevel.Value, currentAssignment.orgId, lstOrgHierarchy) + " (" + langStringValues["BP_Assignment_currentLevel_BusplanText"].LangText + ")";
                        if (!string.IsNullOrEmpty(currentAssignment.description))
                        {
                            sb.Append("<br>");
                        }
                        descriptionObj.iseditorEnabled = true;
                        descriptionObj.editorDescription = sb.ToString();
                        descriptionObj.order = currentAssignment.orgLevel.Value;
                        descriptionArray.Add(descriptionObj);
                    }
                }
            }
        }
        if (descriptionArray != null && descriptionArray.Count > 1)
        {

            JArray result = new JArray(descriptionArray.DistinctBy(x => x["editorDescriptionId"]).OrderBy(obj => (int)obj["order"]));
            return result;
        }
        return descriptionArray;
    }



    private string GetOrgName(int orgLevel, string orgId, List<tco_org_hierarchy> lstOrgHierarchy)
    {
        string orgName = string.Empty;
        switch (orgLevel)
        {
            case 1:
                orgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_1 == orgId).org_name_1;
                break;

            case 2:
                orgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_2 == orgId).org_name_2;
                break;

            case 3:
                orgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_3 == orgId).org_name_3;
                break;

            case 4:
                orgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_4 == orgId).org_name_4;
                break;

            case 5:
                orgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_5 == orgId).org_name_5;
                break;

            default:
                break;
        }
        return orgName;
    }



    public dynamic GetDescriptionforTask(string userId, Guid taskID, string orgId, int orgLevel, int budgetYear, bool isMonthlyReport, bool isDocExport, string serviceId = null, bool isAngular = false)
    {
        try
        {
            if (taskID == Guid.Empty) { return string.Empty; }
            UserData userDetails = _pUtility.GetUserDetails(userId);
            TenantDBContext tenantDbContext = _pUtility.GetTenantDBContext();
            var orgVersionContent = _pUtility.GetOrgVersionSpecificContent(userId, _pUtility.GetForecastPeriod(budgetYear, 1));
            List<tco_org_hierarchy> lstOrgHierarchy = orgVersionContent.lstOrgHierarchy;
            var uniqueTaskID = isMonthlyReport ? tenantDbContext.tbitasks.Where(x => x.uniquetaskId == taskID && x.budgetYear == budgetYear && x.tenantId == userDetails.tenant_id).FirstOrDefault() : tenantDbContext.tbitasks.Where(x => x.taskid == taskID && x.budgetYear == budgetYear && x.tenantId == userDetails.tenant_id).FirstOrDefault();
            StringBuilder sb = new StringBuilder();
            if (uniqueTaskID != null && uniqueTaskID.uniquetaskId != Guid.Empty && uniqueTaskID.uniquetaskId != null)
            {
                var taskData = tenantDbContext.tbitasks.Where(x => x.uniquetaskId == uniqueTaskID.uniquetaskId && x.budgetYear == budgetYear && x.tenantId == userDetails.tenant_id).ToList();
                switch (orgLevel)
                {
                    case 1:
                        lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_1 == orgId).ToList();
                        break;

                    case 2:
                        lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_2 == orgId).ToList();
                        break;

                    case 3:
                        lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_3 == orgId).ToList();
                        break;

                    case 4:
                        lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_4 == orgId).ToList();
                        break;

                    case 5:
                        lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_5 == orgId).ToList();
                        break;

                    default:
                        break;
                }

                var hierarchyList = (from p in lstOrgHierarchy
                    select new
                    {
                        orgID_1 = p.org_id_1,
                        orgName_1 = p.org_name_1,
                        orgLevel_1 = 1,
                        orgID_2 = p.org_id_2,
                        orgName_2 = p.org_name_2,
                        orgLevel_2 = 2,
                        orgID_3 = p.org_id_3,
                        orgName_3 = p.org_name_3,
                        orgLevel_3 = 3,
                        orgID_4 = p.org_id_4,
                        orgName_4 = p.org_name_4,
                        orgLevel_4 = 4,
                        orgID_5 = p.org_id_5,
                        orgName_5 = p.org_name_5,
                        orgLevel_5 = 5
                    }).Distinct().ToList();
                List<clsAssignmentsTexts> instructionsHierarchyList = new List<clsAssignmentsTexts>();
                int orgLevelToIterate = orgLevel;
                while (orgLevelToIterate != 0)
                {
                    switch (orgLevelToIterate)
                    {
                        case 1:
                            instructionsHierarchyList.Add(new clsAssignmentsTexts
                            {
                                orgId = hierarchyList.FirstOrDefault().orgID_1,
                                orgLevel = hierarchyList.FirstOrDefault().orgLevel_1,
                            });
                            break;

                        case 2:
                            instructionsHierarchyList.Add(new clsAssignmentsTexts
                            {
                                orgId = hierarchyList.FirstOrDefault().orgID_2,
                                orgLevel = hierarchyList.FirstOrDefault().orgLevel_2,
                            });
                            break;

                        case 3:
                            instructionsHierarchyList.Add(new clsAssignmentsTexts
                            {
                                orgId = hierarchyList.FirstOrDefault().orgID_3,
                                orgLevel = hierarchyList.FirstOrDefault().orgLevel_3,
                            });
                            break;

                        case 4:
                            instructionsHierarchyList.Add(new clsAssignmentsTexts
                            {
                                orgId = hierarchyList.FirstOrDefault().orgID_4,
                                orgLevel = hierarchyList.FirstOrDefault().orgLevel_4,
                            });
                            break;

                        case 5:
                            instructionsHierarchyList.Add(new clsAssignmentsTexts
                            {
                                orgId = hierarchyList.FirstOrDefault().orgID_5,
                                orgLevel = hierarchyList.FirstOrDefault().orgLevel_5,
                            });
                            break;

                        default:
                            break;
                    }
                    orgLevelToIterate = orgLevelToIterate - 1;
                }

                var hierarchytoIterate = isAngular ? instructionsHierarchyList.Select(x => new
                    {
                        id = x.orgId,
                        level = x.orgLevel
                    }).OrderByDescending(x => x.level).ToList()
                    : instructionsHierarchyList.Select(x => new
                    {
                        id = x.orgId,
                        level = x.orgLevel
                    }).OrderBy(x => x.level).ToList();

                int y = 1;
                if (taskData.Any())
                {
                    foreach (var item in hierarchytoIterate)
                    {
                        var descInfo = taskData.FirstOrDefault(x => x.orgId == item.id && x.orgLevel == item.level);
                        if (descInfo != null)
                        {
                            if (!string.IsNullOrEmpty(descInfo.description))
                            {
                                if (!isDocExport)
                                {
                                    switch (item.level)
                                    {
                                        case 1:
                                            sb.Append("<span class='semi f-13'>" + $"{hierarchyList.FirstOrDefault(x => x.orgID_1 == item.id).orgName_1}" + "</span> <br>");
                                            break;

                                        case 2:
                                            sb.Append("<span class='semi f-13'>" + $"{hierarchyList.FirstOrDefault(x => x.orgID_2 == item.id).orgName_2}" + "</span>  <br>");
                                            break;

                                        case 3:
                                            sb.Append("<span class='semi f-13'>" + $"{hierarchyList.FirstOrDefault(x => x.orgID_3 == item.id).orgName_3}" + "</span> <br>");
                                            break;

                                        case 4:
                                            sb.Append("<span class='semi f-13'>" + $"{hierarchyList.FirstOrDefault(x => x.orgID_4 == item.id).orgName_4}" + "</span> <br>");
                                            break;

                                        case 5:
                                            sb.Append("<span class='semi f-13'>" + $"{hierarchyList.FirstOrDefault(x => x.orgID_5 == item.id).orgName_5}" + "</span> <br>");
                                            break;

                                        default:
                                            sb.Append(string.Empty);
                                            break;
                                    }
                                }
                                sb.Append(!string.IsNullOrEmpty(descInfo.description) ? "<span class='f-13'>" + descInfo.description + "</span>" : string.Empty);

                            }
                        }
                        y++;
                    }

                    if (isDocExport)
                    {
                        sb.Clear();
                        tbitasks descInfo = new tbitasks();
                        if (orgId == null && orgLevel == -1)
                        {
                            var defaultOrgLevel = _pUtility.GetActiveParameterValue(userId, "MR_ASSIGNEMT_ORGLEVEL");
                            int assignmentOrgLevel = (string.IsNullOrEmpty(defaultOrgLevel) ? 2 : int.Parse(defaultOrgLevel));

                            descInfo = taskData.FirstOrDefault(x => x.serviceId == serviceId && x.orgLevel == assignmentOrgLevel);
                        }
                        else
                        {
                            descInfo = taskData.FirstOrDefault(x => x.orgId == orgId && x.orgLevel == orgLevel);
                        }
                        if (descInfo != null)
                            sb.Append(!string.IsNullOrEmpty(descInfo.description) ? descInfo.description : string.Empty);
                    }
                }
            }
            return sb.ToString();
        }
        catch (Exception)
        {
            throw;
        }
    }



    public async Task<bool> SaveAssignmentInstructionsAsync(string userId, clsAssignmentsInstructions objTexts)
    {
        // new value for service id based on new feature
        string unRefinedServiceId = objTexts.serviceId;
        int unRefinedServiceIdLevel = objTexts.serviceLevel;
        if (await _pUtility.IsFeatureEnabled(FeatureFlags.ck_Editor))
        {
            objTexts.serviceId = await _pUtility.RefineServiceIdValue(objTexts.serviceId, userId);
            objTexts.serviceLevel = _pUtility.RefineServiceLevelValue(objTexts.serviceLevel);
        }

        await UpdateExistingAssignmentInstructionsBusinessPlanAsync(userId, objTexts);
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();

        var tbitaskstextsData = string.IsNullOrEmpty(objTexts.serviceId) ?
            await tenantDbContext.tbiassignmentstexts.FirstOrDefaultAsync(x => x.orgId == objTexts.orgId && x.tenantId == userDetails.tenant_id && x.budgetYear == objTexts.budgetYear && x.orgLevel == objTexts.orgLevel) :
            await tenantDbContext.tbiassignmentstexts.FirstOrDefaultAsync(x => x.orgId == objTexts.orgId && x.serviceId == objTexts.serviceId && x.tenantId == userDetails.tenant_id && x.budgetYear == objTexts.budgetYear && x.orgLevel == objTexts.orgLevel);
        if (tbitaskstextsData != null)
        {
            tbitaskstextsData.instructionsdescription = string.IsNullOrEmpty(objTexts.instructionsdescription) ? string.Empty : objTexts.instructionsdescription;
            tbitaskstextsData.updated = DateTime.UtcNow;
            tbitaskstextsData.updatedBy = userDetails.pk_id;
            tenantDbContext.Entry(tbitaskstextsData).State = EntityState.Modified;
            await tenantDbContext.SaveChangesAsync();

            if (objTexts.logHistory)
                await _pUtility.SaveTextLogAsync(userId, tbitaskstextsData.instructionsdescriptionhistoryid.Value, string.IsNullOrEmpty(objTexts.instructionsdescription) ? string.Empty : objTexts.instructionsdescription);
        }
        else
        {
            var historyid = Guid.NewGuid();
            var objtbitaskstextsData = new tbiassignmentstexts();
            objtbitaskstextsData.tenantId = userDetails.tenant_id;
            objtbitaskstextsData.budgetYear = objTexts.budgetYear;
            objtbitaskstextsData.orgId = objTexts.orgId;
            objtbitaskstextsData.orgLevel = objTexts.orgLevel;
            objtbitaskstextsData.serviceId = string.IsNullOrEmpty(objTexts.serviceId) ? string.Empty : objTexts.serviceId;
            objtbitaskstextsData.serviceLevel = string.IsNullOrEmpty(objTexts.serviceId) || objTexts.serviceId == "-1" ? 0 : objTexts.serviceLevel;
            objtbitaskstextsData.instructionsdescriptionhistoryid = historyid;
            objtbitaskstextsData.instructionsdescription = objTexts.instructionsdescription;
            objtbitaskstextsData.updated = DateTime.UtcNow;
            objtbitaskstextsData.updatedBy = userDetails.pk_id;
            objtbitaskstextsData.serviceIdNew = string.IsNullOrEmpty(unRefinedServiceId) ? string.Empty : unRefinedServiceId;
            objtbitaskstextsData.serviceLevelNew = string.IsNullOrEmpty(unRefinedServiceId) || unRefinedServiceId == "-1" ? 0 : unRefinedServiceIdLevel;
            objtbitaskstextsData.businessPlanDescription = string.Empty;
            objtbitaskstextsData.businessplandescriptionhistoryid = Guid.NewGuid();
            objtbitaskstextsData.strategyActionDescription = string.Empty;
            objtbitaskstextsData.strategyactiondescriptionhistoryid = Guid.NewGuid();
            await tenantDbContext.tbiassignmentstexts.AddAsync(objtbitaskstextsData);
            await tenantDbContext.SaveChangesAsync();

            if (objTexts.logHistory)
                await _pUtility.SaveTextLogAsync(userId, historyid, objTexts.instructionsdescription);
        }
        return true;
    }



    public async Task<dynamic> GetAssignmentInstructionsAsync(string userId, string orgId, int orgLevel, int budgetYear, string serviceId)
    {
        try
        {
            int orgLeveltoCheck = orgLevel;
            UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
            TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
            Dictionary<string, clsLanguageString> langStringValuesBP = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BusinessPlan");
            var orgVersionContent = await _pUtility.GetOrgVersionSpecificContentAsync(userId, _pUtility.GetForecastPeriod(budgetYear, 1));

            List<tco_org_hierarchy> lstOrgHierarchy = orgVersionContent.lstOrgHierarchy;
            string orgNameforTitle = string.Empty;               
            var hierarchyList = (from p in lstOrgHierarchy
                select new
                {
                    orgID_1 = p.org_id_1,
                    orgName_1 = p.org_name_1,
                    orgLevel_1 = 1,
                    orgID_2 = p.org_id_2,
                    orgName_2 = p.org_name_2,
                    orgLevel_2 = 2,
                    orgID_3 = p.org_id_3,
                    orgName_3 = p.org_name_3,
                    orgLevel_3 = 3,
                    orgID_4 = p.org_id_4,
                    orgName_4 = p.org_name_4,
                    orgLevel_4 = 4,
                    orgID_5 = p.org_id_5,
                    orgName_5 = p.org_name_5,
                    orgLevel_5 = 5
                }).Distinct().ToList();

            switch (orgLevel)
            {
                case 1:
                    hierarchyList = hierarchyList.Where(x => x.orgID_1 == orgId).ToList();
                    break;

                case 2:
                    hierarchyList = hierarchyList.Where(x => x.orgID_2 == orgId).ToList();
                    break;

                case 3:
                    hierarchyList = hierarchyList.Where(x => x.orgID_3 == orgId).ToList();
                    break;

                case 4:
                    hierarchyList = hierarchyList.Where(x => x.orgID_4 == orgId).ToList();
                    break;

                case 5:
                    hierarchyList = hierarchyList.Where(x => x.orgID_5 == orgId).ToList();
                    break;

                default:
                    break;
            }

            List<clsAssignmentsTexts> instructionsHierarchyList = new List<clsAssignmentsTexts>();
            while (orgLevel != 0)
            {
                switch (orgLevel)
                {
                    case 1:
                        instructionsHierarchyList.Add(new clsAssignmentsTexts
                        {
                            orgId = hierarchyList.FirstOrDefault().orgID_1,
                            orgLevel = hierarchyList.FirstOrDefault().orgLevel_1,
                        });
                        break;

                    case 2:
                        instructionsHierarchyList.Add(new clsAssignmentsTexts
                        {
                            orgId = hierarchyList.FirstOrDefault().orgID_2,
                            orgLevel = hierarchyList.FirstOrDefault().orgLevel_2,
                        });
                        break;

                    case 3:
                        instructionsHierarchyList.Add(new clsAssignmentsTexts
                        {
                            orgId = hierarchyList.FirstOrDefault().orgID_3,
                            orgLevel = hierarchyList.FirstOrDefault().orgLevel_3,
                        });
                        break;

                    case 4:
                        instructionsHierarchyList.Add(new clsAssignmentsTexts
                        {
                            orgId = hierarchyList.FirstOrDefault().orgID_4,
                            orgLevel = hierarchyList.FirstOrDefault().orgLevel_4,
                        });
                        break;

                    case 5:
                        instructionsHierarchyList.Add(new clsAssignmentsTexts
                        {
                            orgId = hierarchyList.FirstOrDefault().orgID_5,
                            orgLevel = hierarchyList.FirstOrDefault().orgLevel_5,
                        });
                        break;

                    default:
                        break;
                }
                orgLevel = orgLevel - 1;
            }

            var hierarchytoIterate = instructionsHierarchyList.Select(x => new
            {
                id = x.orgId,
                level = x.orgLevel
            }).OrderBy(x => x.level).ToList();

            var toptextDescription = hierarchytoIterate.FirstOrDefault(x => x.level == orgLeveltoCheck);
            string toptextDesc = string.Empty, toptextDeschistoryId = string.Empty;

            // new value for service id based on new feature
            string unRefinedServiceId = serviceId;
            bool isFeatureFlag = await _pUtility.IsFeatureEnabled(FeatureFlags.ck_Editor);
            if (isFeatureFlag)
            {
                serviceId = await _pUtility.RefineServiceIdValue(serviceId, userId);
            }

            if (toptextDescription != null)
            {
                await CleanDuplicateRecordsFromAssigmentText(tenantDbContext, userDetails.tenant_id, budgetYear, orgLeveltoCheck, toptextDescription.id, serviceId); // bug 173623 - added to cleanup the data 

                var tbitaskstextsData = string.IsNullOrEmpty(serviceId) ?
                    await tenantDbContext.tbiassignmentstexts.FirstOrDefaultAsync(x => x.orgId == toptextDescription.id && x.tenantId == userDetails.tenant_id && x.budgetYear == budgetYear && x.orgLevel == orgLeveltoCheck) :
                    await tenantDbContext.tbiassignmentstexts.FirstOrDefaultAsync(x => x.orgId == toptextDescription.id && x.serviceId == serviceId && x.tenantId == userDetails.tenant_id && x.budgetYear == budgetYear && x.orgLevel == orgLeveltoCheck);


                if (tbitaskstextsData != null)
                {
                    if (tbitaskstextsData.instructionsdescriptionhistoryid == null)
                    {
                        tbitaskstextsData.instructionsdescription = string.Empty;
                        tbitaskstextsData.instructionsdescriptionhistoryid = Guid.NewGuid();
                        await tenantDbContext.SaveChangesAsync();
                    }
                    toptextDesc = tbitaskstextsData.instructionsdescription.ToString();
                    toptextDeschistoryId = tbitaskstextsData.instructionsdescriptionhistoryid.ToString();
                }
                else
                {
                    tbiassignmentstexts dataToSave = new tbiassignmentstexts()
                    {
                        orgId = toptextDescription.id,
                        orgLevel = toptextDescription.level,
                        serviceId = serviceId,
                        instructionsdescription = string.Empty,
                        instructionsdescriptionhistoryid = Guid.NewGuid(),
                        businessPlanDescription = string.Empty,
                        businessplandescriptionhistoryid = Guid.NewGuid(),
                        strategyActionDescription = string.Empty,
                        strategyactiondescriptionhistoryid = Guid.NewGuid(),
                        tenantId = userDetails.tenant_id,
                        budgetYear = budgetYear,
                        updated = DateTime.UtcNow,
                        serviceIdNew = unRefinedServiceId,
                        serviceLevel = isFeatureFlag ? 0 : null,
                        updatedBy = userDetails.pk_id
                    };
                    await tenantDbContext.tbiassignmentstexts.AddAsync(dataToSave);
                    await tenantDbContext.SaveChangesAsync();
                    toptextDesc = dataToSave.instructionsdescription.ToString();
                    toptextDeschistoryId = dataToSave.instructionsdescriptionhistoryid.ToString();
                }
            }

            hierarchytoIterate = hierarchytoIterate.Where(x => x.level < orgLeveltoCheck).OrderBy(x => x.level).ToList();

            dynamic instructionDescription = new JObject(); int z = 0;
            foreach (var item in hierarchytoIterate)
            {
                z++;
                switch (item.level)
                {
                    case 1:
                        orgNameforTitle = hierarchyList.FirstOrDefault(x => x.orgID_1 == item.id).orgName_1;
                        break;

                    case 2:
                        orgNameforTitle = hierarchyList.FirstOrDefault(x => x.orgID_2 == item.id).orgName_2;
                        break;

                    case 3:
                        orgNameforTitle = hierarchyList.FirstOrDefault(x => x.orgID_3 == item.id).orgName_3;
                        break;

                    case 4:
                        orgNameforTitle = hierarchyList.FirstOrDefault(x => x.orgID_4 == item.id).orgName_4;
                        break;

                    case 5:
                        orgNameforTitle = hierarchyList.FirstOrDefault(x => x.orgID_5 == item.id).orgName_5;
                        break;

                    default:
                        break;
                }

                string description = string.Empty;

                await CleanDuplicateRecordsFromAssigmentText(tenantDbContext, userDetails.tenant_id, budgetYear, item.level, item.id, serviceId); // bug 173623 - added to cleanup the data 

                var tbitaskstextsData = string.IsNullOrEmpty(serviceId) ?
                    await tenantDbContext.tbiassignmentstexts.FirstOrDefaultAsync(x => x.orgId == item.id && x.tenantId == userDetails.tenant_id && x.budgetYear == budgetYear && x.orgLevel == item.level) :
                    await tenantDbContext.tbiassignmentstexts.FirstOrDefaultAsync(x => x.orgId == item.id && x.serviceId == serviceId && x.tenantId == userDetails.tenant_id && x.budgetYear == budgetYear && x.orgLevel == item.level);

                if (tbitaskstextsData != null)
                {
                    description = !string.IsNullOrEmpty(tbitaskstextsData.instructionsdescription) ? tbitaskstextsData.instructionsdescription : string.Empty;
                }

                instructionDescription.Add("textdescription" + z.ToString(), description);
                instructionDescription.Add("texttitle" + z.ToString(), string.Join(" - ", orgNameforTitle, langStringValuesBP.FirstOrDefault(v => v.Key == "BP_asgmtinstruction_higherlevel").Value.LangText));
            }

            instructionDescription.Add("toptextDesc", toptextDesc);
            instructionDescription.Add("toptextDeschistoryId", toptextDeschistoryId);
            return instructionDescription;
        }
        catch (Exception)
        {
            throw;
        }
    }



    public async Task<List<DescriptionCityLevel>> GetDescriptionforAssignmentAtCityLevelAsync(string userId,
        string orgId,
        int orgLevel,
        ClsOrgVersionSpecificContent orgVersionContent,
        List<tbiassignments> masterAssignments,
        List<tco_assignments_descriptions> masterfpAssignmentDescriptions,
        bool isMrDoc = false,
        bool isClimateAssignments = false)
    {
        try
        {
            UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
            List<tco_org_hierarchy> lstOrgHierarchy = orgVersionContent.lstOrgHierarchy;
            var allAssignments = masterAssignments.Select(x => x.assignmentId).Distinct().ToList();
            var fpAssignmentDescriptions = (from a in masterfpAssignmentDescriptions.Where(x => x.fk_assignment_id.HasValue && allAssignments.Contains(x.fk_assignment_id.Value))
                select a).ToList();
            Dictionary<string, clsLanguageString> langStringsMonRep = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "MonthlyReport");
            bool isBusPlan = await _pUtility.IsActiveModuleAsync(userDetails.user_name, 9);

            var assignmentDesc = (from a in masterAssignments
                join b in fpAssignmentDescriptions on new { a = a.tenantId, b = a.assignmentId }
                    equals new { a = b.fk_tenant_id ?? 0, b = b.fk_assignment_id ?? Guid.Empty } into res
                from descRes in res.DefaultIfEmpty()
                where a.orgId == orgId && a.orgLevel == orgLevel
                select new DescriptionCityLevel
                {
                    uniqueAssignmentID = a.uniqueassignmentId,
                    orgName = lstOrgHierarchy.FirstOrDefault().org_name_1,
                    description = IsCreatedinBudgetProposal(a.uniqueassignmentId, masterAssignments) ?
                        FormatBudPropBusplanAssignmentText(descRes, a.description, isClimateAssignments, isBusPlan, langStringsMonRep) : a.description
                }).ToList();

            var finalSet = assignmentDesc.Select(x => new
            {
                uniqueAssignmentID = x.uniqueAssignmentID,
                orgName = lstOrgHierarchy.FirstOrDefault().org_name_1,
                description = GetFormattedDesc(isMrDoc, assignmentDesc.Where(z => z.uniqueAssignmentID == x.uniqueAssignmentID).ToList())
            }).Distinct().Select(x => new DescriptionCityLevel
            {
                uniqueAssignmentID = x.uniqueAssignmentID,
                orgName = x.orgName,
                description = x.description,
            }).ToList();
            return finalSet;
        }
        catch (Exception)
        {
            throw;
        }
    }



    public List<DescriptionCityLevel> GetDescriptionforAssignmentAtCityLevel(string userId,
        string orgId,
        int orgLevel,
        ClsOrgVersionSpecificContent orgVersionContent,
        List<tbiassignments> masterAssignments,
        List<tco_assignments_descriptions> masterfpAssignmentDescriptions,
        bool isMrDoc = false,
        bool isClimateAssignments = false)
    {
        var result = GetDescriptionforAssignmentAtCityLevelAsync(userId, orgId, orgLevel, orgVersionContent,
                masterAssignments, masterfpAssignmentDescriptions, isMrDoc, isClimateAssignments).GetAwaiter()
            .GetResult();
        return result;
    }

      


        


    private static dynamic GetFormattedDesc(bool isMrDoc, List<DescriptionCityLevel> descList)
    {
        StringBuilder sb = new StringBuilder();
        bool flag = false;
        dynamic descData = new JArray();
        var descInfo = descList.FirstOrDefault();
        if (descInfo != null && !string.IsNullOrEmpty(descInfo.description))
        {
            dynamic ob = new JObject();
            ob.name = descInfo.orgName;
            ob.description = descInfo.description;
            descData.Add(ob);
        }

        if (descData.Count > 1 || !isMrDoc)
        {
            flag = true;
        }
        foreach (var item in descData)
        {
            if (flag)
                sb.Append("<b>" + item.name.ToString() + "</b>\n");
            var description = (string)item.description;
            sb.Append(!string.IsNullOrEmpty(description) ? description.ToString().Replace("<br>", "") : string.Empty);
            sb.Append($"<br>");
        }
        return sb.ToString();
    }



    private static bool IsCreatedinBudgetProposal(Guid uniqueAssignmentID, List<tbiassignments> masterAssignments)
    {
        if (masterAssignments.Any(x => x.uniqueassignmentId == uniqueAssignmentID))
            return masterAssignments.FirstOrDefault(t => (t.isBudgetProposal.HasValue && t.isBudgetProposal.Value)) != null;
        return false;
    }



    private async Task CleanDuplicateRecordsFromAssigmentText(TenantDBContext tenantDbContext, int tenantId, int budgetYear, int orglevel, string orgId, string serviceId)
    {
        var dataList = string.IsNullOrEmpty(serviceId) ?
            await tenantDbContext.tbiassignmentstexts.Where(x => x.tenantId == tenantId && x.budgetYear == budgetYear && x.orgId == orgId).ToListAsync() :
            await tenantDbContext.tbiassignmentstexts.Where(x => x.tenantId == tenantId && x.budgetYear == budgetYear && x.orgId == orgId && x.serviceId == serviceId).ToListAsync();
        if (dataList.Count == 1)
        {
            var item = dataList.FirstOrDefault();
            if (item.orgLevel == 0)
            {
                item.orgLevel = orglevel;
                item.instructionsdescriptionhistoryid = item.instructionsdescriptionhistoryid ?? Guid.NewGuid();
                item.instructionsdescription = item.instructionsdescription ?? string.Empty;


                item.businessplandescriptionhistoryid = item.businessplandescriptionhistoryid ?? Guid.NewGuid();
                item.businessPlanDescription = item.businessPlanDescription ?? string.Empty;


                item.strategyactiondescriptionhistoryid = item.strategyactiondescriptionhistoryid ?? Guid.NewGuid();
                item.strategyActionDescription = item.strategyActionDescription ?? string.Empty;

                tenantDbContext.Update(item);
                await tenantDbContext.SaveChangesAsync();
            }


        }
        else if (dataList.Count > 1)
        {
            var mainData = dataList.FirstOrDefault(x => x.orgLevel == 0);
            if (mainData is null)
            {
                mainData = dataList.FirstOrDefault();
            }

            var dataToDelete = dataList.Where(x => x.Id != mainData.Id).ToList();
            foreach (var item in dataToDelete)
            {
                if (!string.IsNullOrEmpty(item.instructionsdescription))
                {
                    if (string.IsNullOrEmpty(mainData.instructionsdescription))
                    {
                        mainData.instructionsdescription = item.instructionsdescription;
                        mainData.instructionsdescriptionhistoryid = item.instructionsdescriptionhistoryid;
                    }
                }
                if (!string.IsNullOrEmpty(item.businessPlanDescription) || !string.IsNullOrEmpty(item.strategyActionDescription))
                {
                    if (string.IsNullOrEmpty(mainData.businessPlanDescription) && string.IsNullOrEmpty(mainData.strategyActionDescription))
                    {
                        mainData.businessPlanDescription = item.businessPlanDescription ?? string.Empty;
                        mainData.businessplandescriptionhistoryid = item.businessplandescriptionhistoryid ?? Guid.NewGuid();
                        mainData.strategyActionDescription = item.strategyActionDescription ?? string.Empty;
                        mainData.strategyactiondescriptionhistoryid = item.strategyactiondescriptionhistoryid ?? Guid.NewGuid();
                    }
                }
            }

            mainData.businessplandescriptionhistoryid = mainData.businessplandescriptionhistoryid ?? Guid.NewGuid();
            mainData.businessPlanDescription = mainData.businessPlanDescription ?? string.Empty;
            mainData.strategyactiondescriptionhistoryid = mainData.strategyactiondescriptionhistoryid ?? Guid.NewGuid();
            mainData.strategyActionDescription = mainData.strategyActionDescription ?? string.Empty;
            mainData.instructionsdescriptionhistoryid = mainData.instructionsdescriptionhistoryid ?? Guid.NewGuid();
            mainData.instructionsdescription = mainData.instructionsdescription ?? string.Empty;
            mainData.orgLevel = orglevel;

            tenantDbContext.Update(mainData);

            await tenantDbContext.tbiassignmentstexts.BulkDeleteAsync(dataToDelete);
            await tenantDbContext.SaveChangesAsync();


        }
    }



    private async Task<dynamic> GetAssignmentGuidanceTextDescriptionsAsync(string userId, tbiassignments currentAssignment, int budgetYear)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        Dictionary<string, clsLanguageString> langStringValues = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BusinessPlan");

        dynamic descriptionObj = new JObject();

        // check if guidance history id is empty the creat
        if (!currentAssignment.guidance_text_history.HasValue || currentAssignment.guidance_text_history.Value == Guid.Empty)
        {
            currentAssignment.guidance_text_history = Guid.NewGuid();
            await tenantDbContext.SaveChangesAsync();
        }
        //Display editable editor to insert busplan text for finplan assignments at all levels
        StringBuilder sb = new StringBuilder();
        descriptionObj = new JObject();
        sb.Append(string.IsNullOrEmpty(currentAssignment.guidance_text) ? string.Empty : currentAssignment.guidance_text);
        descriptionObj.gudanceText_id = currentAssignment.guidance_text_history.Value;
        descriptionObj.iseditorEnabled = true;
        descriptionObj.editorDescription = sb.ToString();

        return descriptionObj;
    }



    private async Task<List<int>> GetAssignmentCordinatorListAsync(string userId, tbiassignments tbiassignmentData, int budgetYear)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        List<int> contributorList = await tenantDbContext.tbi_assignments_contributor.AnyAsync() ? await tenantDbContext.tbi_assignments_contributor.Where(z => z.fk_tenant_id == userDetails.tenant_id && z.fk_assignment_id == tbiassignmentData.assignmentId).Select(x => x.contributor_user_id).ToListAsync() : new List<int>();
        return contributorList;
    }



    private bool GetEditabilityStatus(tbiassignments tbiassignmentData, tbiassignments isSelectedOrgLevel)
    {
        if (tbiassignmentData.isBudgetProposal.HasValue && tbiassignmentData.isBudgetProposal.Value) return false;
        return !(tbiassignmentData != null && isSelectedOrgLevel == null);
    }

    private async Task<List<tbi_task_monthly_status?>> GetStatusDescriptionforTaskAsync(string userId, int budgetYear)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        int startYear = int.Parse(budgetYear + "01");
        int endYear = int.Parse(budgetYear + "12");
        return await tenantDbContext.tbi_task_monthly_status.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.forecast_period >= startYear && x.forecast_period <= endYear).ToListAsync();
    }
}