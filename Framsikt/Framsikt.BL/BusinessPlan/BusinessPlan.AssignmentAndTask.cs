using System.Text;
using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using KeyValuePair = Framsikt.BL.Helpers.KeyValuePair;

namespace Framsikt.BL;

public partial class BusinessPlan
{
    //#region Assignment and Tasks Section for Business Plan

    public async Task<dynamic> GetBusinessPlanCommonData(string userId, string orgId, int budgetYear, int orgIdlevelNo, string serviceId, bool filterUser)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        dynamic businessPlanCommonData = new JObject();
        var orgVersionContent = await _pUtility.GetOrgVersionSpecificContentAsync(userId, _pUtility.GetForecastPeriod(budgetYear, 1));

        serviceId = string.IsNullOrEmpty(serviceId) ? string.Empty : serviceId;

        List<tco_org_hierarchy> lstOrgHierarchy = orgVersionContent.lstOrgHierarchy;
        var cityID = lstOrgHierarchy.FirstOrDefault().org_id_1;
        var level1Value = orgIdlevelNo == 3 ? lstOrgHierarchy.FirstOrDefault(x => x.org_id_3 == orgId).org_id_2 :
            orgIdlevelNo == 4 ? lstOrgHierarchy.FirstOrDefault(x => x.org_id_4 == orgId).org_id_2 :
            orgIdlevelNo == 5 ? lstOrgHierarchy.FirstOrDefault(x => x.org_id_5 == orgId).org_id_2 :
            cityID;

        var level2Value = orgIdlevelNo == 3 ? orgId :
            orgIdlevelNo == 4 ? lstOrgHierarchy.FirstOrDefault(x => x.org_id_4 == orgId).org_id_3 :
            orgIdlevelNo == 5 ? lstOrgHierarchy.FirstOrDefault(x => x.org_id_5 == orgId).org_id_3 :
            cityID;

        // Get Tags Data
        var actionTags = (from atg in tenantDbContext.tcoActionTags
            where atg.FkTenantId == userDetails.tenant_id
            select new
            {
                key = atg.PkId,
                value = atg.TagDescription
            }).OrderBy(x => x.value).ToList();

        var tagsData = new JArray();
        foreach (var item in actionTags)
        {
            dynamic row = new JObject();
            row.KeyId = item.key;
            row.ValueString = item.value;
            tagsData.Add(row);
        }
        businessPlanCommonData.Add("tags", tagsData);

        // Get category dropdown Data
        var categoryDatadropdown = await tenantDbContext.tco_category.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.status == 1
            && (x.type.ToUpper() == "SUNIT_BPLAN" || x.type.ToUpper() == "CLIMATE_ACTION")).AsNoTracking().ToListAsync();

        List<keyvaluewithGuid> categoryList = FormatCategoryDropdownData(categoryDatadropdown, false);
        businessPlanCommonData.Add("category", JArray.FromObject(categoryList));
        businessPlanCommonData.Add("climateCategoryList", JArray.FromObject(FormatCategoryDropdownData(categoryDatadropdown, true)));

        businessPlanCommonData.Add("goals", await GetGoalsDropdownDataForAssignmentAsync(userId, orgId, serviceId, budgetYear, orgIdlevelNo, orgVersionContent, true));
        List<BusinessPlanUserData> tenantUsers = await GetUserOrgData(userId, budgetYear);

        var tenantUsersList = new JArray();

        //Empty row
        dynamic emptyRow = new JObject();
        emptyRow.KeyId = 0;
        emptyRow.ValueString = string.Empty;

        tenantUsersList.Add(emptyRow);

        foreach (var t in tenantUsers)
        {
            dynamic row = new JObject();
            row.KeyId = t.Id;
            if (!string.IsNullOrEmpty(t.FirstName) && !string.IsNullOrEmpty(t.LastName))
            {
                row.ValueString = t.FirstName + " " + t.LastName;
            }
            else if (!string.IsNullOrEmpty(t.FirstName))
            {
                row.ValueString = t.FirstName;
            }
            else if (!string.IsNullOrEmpty(t.LastName))
            {
                row.ValueString = t.LastName;
            }
            tenantUsersList.Add(row);
        }
        businessPlanCommonData.Add("tenantUsersList", tenantUsersList);

        var statusDescriptions = await tenantDbContext.tco_progress_status.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.type.ToLower() == "sunit_bplan".ToLower()).OrderBy(x => x.status_description).ToListAsync();
        var statusList = new JArray();
        statusList.Add(emptyRow);
        foreach (var t in statusDescriptions)
        {
            dynamic row = new JObject();
            row.KeyId = t.status_id;
            row.ValueString = t.status_description;
            statusList.Add(row);
        }
        businessPlanCommonData.Add("statusList", statusList);
        businessPlanCommonData.Add("serviceList", string.Empty);
        businessPlanCommonData.Add("displaydropdown", false);

        var descriptionArray = new JArray();
        dynamic descriptionObj = new JObject();
        descriptionObj.editorTitle = GetOrgName(orgIdlevelNo, orgId, lstOrgHierarchy);
        descriptionObj.editorDescription = string.Empty;
        descriptionObj.editorDescriptionId = Guid.Empty;
        descriptionObj.iseditorEnabled = true;
        descriptionArray.Add(descriptionObj);
        businessPlanCommonData.Add("description", descriptionArray);
        dynamic GuidanceTextdescriptionObj = new JObject();
        GuidanceTextdescriptionObj.gudanceText_id = Guid.Empty;
        GuidanceTextdescriptionObj.iseditorEnabled = true;
        GuidanceTextdescriptionObj.gudanceTextDescription = string.Empty;
        businessPlanCommonData.Add("guidance_text", GuidanceTextdescriptionObj);

        return businessPlanCommonData;
    }

    public async Task<JObject> GetHierarchialDistributionAssignment(string userId, int budgetYear, string orgID, int orgLevel, string uniqueAssignmentId, int period, bool displayTask = true, bool isBusinessStatusGraph = false, int selectedOrgLevel = 0, int status = -1, bool isLiveStatus = false)
    {
        try
        {
            UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
            TenantDBContext ctxt = await _pUtility.GetTenantDBContextAsync();
            var orgVersionContent = await _pUtility.GetOrgVersionSpecificContentAsync(userId, _pUtility.GetForecastPeriod(budgetYear, 1));
            List<tco_org_level> lstOrgLevels = orgVersionContent.lstOrgLevel.OrderBy(y => y.org_level).ToList();
            Dictionary<string, clsLanguageString> langStringValuesBusinessPlan = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BusinessPlan");
            List<tco_org_hierarchy> lstOrgHierarchyOriginal = orgVersionContent.lstOrgHierarchy;
            Guid Id = Guid.Parse(uniqueAssignmentId);
            var assignmentsData = await ctxt.tbiassignments.Where(x => x.uniqueassignmentId == Id && x.tenantId == userDetails.tenant_id && x.budgetYear == budgetYear).ToListAsync();
            var assignmentIDSet = assignmentsData.Select(x => x.assignmentId).ToList();
            var taskData = await ctxt.tbitasks.Where(x => assignmentIDSet.Contains(x.assignmentId.Value) && x.tenantId == userDetails.tenant_id).ToListAsync();
            int lastOrgLevel = lstOrgLevels.ElementAt(lstOrgLevels.Count - 1).org_level;
            List<tbiassignmentmonthlystatus> mRtbiassignmentmonthlystatus = await ctxt.tbiassignmentmonthlystatus.Where(x => x.tenantId == userDetails.tenant_id).ToListAsync();// mr status desc
            List<tbi_task_monthly_status> tbitask_monthlystatusList = await ctxt.tbi_task_monthly_status.Where(x => x.fk_tenant_id == userDetails.tenant_id).ToListAsync();
            List<OrgIdLevelHierarchyHelper> businessplanHierarchyList = await _pUtility.GetOrgIdLevelHierarchyStructAsync(orgVersionContent, userId);
            businessplanHierarchyList = (from a in businessplanHierarchyList
                join b in assignmentsData on new { ID = a.fk_org_id, Level = a.hierarchy_level }
                    equals new { ID = b.orgId, Level = b.orgLevel.Value }
                select new OrgIdLevelHierarchyHelper
                {
                    fk_org_version = a.fk_org_version,
                    fk_org_id = a.fk_org_id,
                    hierarchy_level = a.hierarchy_level
                }).ToList();
            var userSpecificDataSet = (from ol in lstOrgLevels
                join orl in businessplanHierarchyList on ol.org_level equals orl.hierarchy_level
                where ol.org_level >= orgLevel
                select new OrgIdLevelHierarchyHelper
                {
                    hierarchy_level = ol.org_level,
                    fk_org_id = orl.fk_org_id
                }).ToList();

            int suLevel = lstOrgLevels.FirstOrDefault(x => x.su_flag == 1).org_level;
            var lstOrgHierarchy = new List<tco_org_hierarchy>();
            switch (orgLevel)
            {
                case 1:
                    lstOrgHierarchy = lstOrgHierarchyOriginal.Where(x => x.org_id_1 == orgID).ToList();
                    break;

                case 2:
                    lstOrgHierarchy = lstOrgHierarchyOriginal.Where(x => x.org_id_2 == orgID).ToList();
                    break;

                case 3:
                    lstOrgHierarchy = lstOrgHierarchyOriginal.Where(x => x.org_id_3 == orgID).ToList();
                    break;

                case 4:
                    lstOrgHierarchy = lstOrgHierarchyOriginal.Where(x => x.org_id_4 == orgID).ToList();
                    break;

                case 5:
                    lstOrgHierarchy = lstOrgHierarchyOriginal.Where(x => x.org_id_5 == orgID).ToList();
                    break;

                default:
                    break;
            }
            var lstUserSpecificOrgLevels = (from l in userSpecificDataSet
                where l.hierarchy_level >= orgLevel
                orderby l.hierarchy_level
                select l.hierarchy_level).Distinct().ToList();

            var statusDescriptions = await ctxt.tco_progress_status.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.type.ToLower() == "sunit_bplan".ToLower()).ToListAsync();
            var originalSet = new List<AssignmentGridDataHelper>();
            var userData = await GetUserOrgData(userId, budgetYear);
            FormatHierarchyDataInputHelper inputHelper = new FormatHierarchyDataInputHelper()
            {
                userData = userData,
                budgetYear = budgetYear,
                userId = userId,
                lstOrgLevels = lstUserSpecificOrgLevels,
                lstOrgHierarchy = lstOrgHierarchy,
                orgLevel = lstUserSpecificOrgLevels.OrderBy(y => y).FirstOrDefault(),
                lstUserSpecificDataSet = userSpecificDataSet,
                lastOrgLevel = lastOrgLevel,
                sulevel = suLevel,
                assignmentsData = assignmentsData,
                taskData = taskData,
                originalSet = originalSet,
                statusDescriptions = statusDescriptions,
                langStringValuesBusinessPlan = langStringValuesBusinessPlan,
                mRtbiassignmentmonthlystatus = mRtbiassignmentmonthlystatus,
                tbitask_monthlystatusList = tbitask_monthlystatusList,
                period = period
            };
            var dataSet = (isBusinessStatusGraph) ? FormatHierarchyDataForBusinessPlanStatusGraph(inputHelper, displayTasks: true, selectedOrgLevel: selectedOrgLevel, status: status) : await FormatHierarchyDataAsync(inputHelper, displayTask);

            OtherLevelLinearDataInputHelper otherLevelInputHelper = new OtherLevelLinearDataInputHelper()
            {
                userData = userData,
                userId = userId,
                budgetYear = budgetYear,
                assignmentsData = assignmentsData,
                taskData = taskData,
                statusDescriptions = statusDescriptions,
                langStringValuesBusinessPlan = langStringValuesBusinessPlan,
                orgID = orgID,
                orgLevel = orgLevel,
                lstOrgHierarchy = lstOrgHierarchyOriginal,
                mRtbiassignmentmonthlystatus = mRtbiassignmentmonthlystatus,
                tbitask_monthlystatusList = tbitask_monthlystatusList,
                period = period
            };

            var dataSetOtherLevels = GetOtherLevelLinearData(otherLevelInputHelper, displayTask);

            dynamic gridData = new JObject();
            gridData.columns = GetColumnsForHierarchicalDistributionDetailsOfAnAssignment(langStringValuesBusinessPlan, budgetYear, isLiveStatus);
            gridData.data = JArray.FromObject(dataSet);
            gridData.otherLeveldata = JArray.FromObject(dataSetOtherLevels);
            gridData.usersList = JArray.FromObject(userData.Select(x => new
            {
                key = x.Id,
                value = string.Join(" ", x.FirstName, x.LastName)
            }));
            var arrStatus = new List<BusinessPlanBaseGridKeyValueHelper>();
            arrStatus.AddRange(statusDescriptions.Select(s => new BusinessPlanBaseGridKeyValueHelper
            {
                Key = s.status_id,
                Value = s.status_description
            }));
            gridData.Add("arrStatus", JArray.FromObject(arrStatus));
            return gridData;
        }
        catch (Exception)
        {
            throw;
        }
    }

    public async Task<bool> SaveBPUserSpecificTaskAsync(string userId, clsTasks data)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        StringBuilder sb = new StringBuilder();
        sb.AppendLine($"busplan task save delegation @ {DateTime.UtcNow}");

        string textToSave = string.Empty;
        Guid textGuid = Guid.Empty; bool is_desc_save_log = false; bool isFirstTimedelegated = false;
        Guid unique_task_id = Guid.Empty, unique_ass_id = Guid.Empty;
        string assignmentRef = string.Empty; int assignmentOwner = 0;
        bool includedInMonthlyReport = false;
        List<int> usersListforNotifications = new List<int>();
        tbiassignments ass_detail = null;
        List<string> goals = new List<string>();
        BPAssignmentSaveHelper assignmentData = new BPAssignmentSaveHelper();
        assignmentData.budgetYear = data.budgetYear;
        Dictionary<Guid, string> textHistory = new Dictionary<Guid, string>();
        bool isTaskNameChanged = false;
        sb.AppendLine("3270 -> " + DateTime.UtcNow.ToString());
        if (data.taskId != Guid.Empty)
        {
            isFirstTimedelegated = await GetTaskDelegatedStatusAsync(userId, data);
        }
        sb.AppendLine("3275 -> " + DateTime.UtcNow.ToString());
        bool updateMonthlyReportTaskStatus = await CheckTaskStatusEditabilityAsync(userId, data.taskId.ToString(), data.orgId, data.serviceId, data.orgLevel);
        sb.AppendLine("3276 -> " + DateTime.UtcNow.ToString());
        List<TbiAssignmentGoal> assignGoalList = await tenantDbContext.TbiAssignmentGoal.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == data.budgetYear).ToListAsync();
        if (data.assignmentId != null)
        {
            ass_detail = await tenantDbContext.tbiassignments.FirstOrDefaultAsync(x => x.assignmentId == data.pkAssignmentId && x.budgetYear == data.budgetYear && x.tenantId == userDetails.tenant_id);
            goals = assignGoalList.Where(x => x.fk_assignment_id == data.assignmentId).Select(x => x.fk_goal_id.ToString()).ToList();
            unique_ass_id = ass_detail == null ? Guid.Empty : ass_detail.uniqueassignmentId;
            if (ass_detail != null)
            {
                assignmentRef = ass_detail.assignmentrefid;
                assignmentOwner = ass_detail.assignmentOwner;
                includedInMonthlyReport = ass_detail.includedInMonthlyReport;
            }
        }
        sb.AppendLine("3289 -> " + DateTime.UtcNow.ToString());
        Guid guid2 = Guid.Empty;
        if (guid2 == data.taskId)
        {
            var assignmentIdToConnectToTask = data.assignmentId == guid2 || string.IsNullOrEmpty(data.assignmentId.ToString()) ? guid2 : data.assignmentId;
            var existingTask = await tenantDbContext.tbitasks.Where(x => x.tenantId == userDetails.tenant_id && x.budgetYear == data.budgetYear && x.assignmentId == assignmentIdToConnectToTask && x.taskname == data.taskname
                                                                         && x.orgId == data.orgId && x.orgLevel == data.orgLevel && x.orgCreatedAt == data.orgId && x.orglevelCreatedAt == data.orgLevel).ToListAsync();
            if (existingTask == null || existingTask.Count() == 0)
            {
                var Id = Guid.NewGuid();
                var objTask = new tbitasks();
                objTask.taskid = Id;
                objTask.uniquetaskId = unique_task_id = Guid.NewGuid();
                objTask.assignmentId = assignmentIdToConnectToTask;
                objTask.taskname = data.taskname;
                objTask.budgetYear = data.budgetYear;
                objTask.orgId = data.orgId;
                objTask.orgLevel = data.orgLevel;
                objTask.serviceId = string.IsNullOrEmpty(data.serviceId) || data.serviceId == "-1" ? string.Empty : data.serviceId;
                objTask.serviceLevel = string.IsNullOrEmpty(data.serviceLevel.ToString()) ? 0 : int.Parse(data.serviceLevel.ToString());
                objTask.taskServiceId = string.Empty;
                objTask.taskServiceLevel = 0;
                objTask.description = textToSave = data.description;
                objTask.startDate = data.startDate;
                objTask.endDate = data.endDate;

                objTask.isDistributedtoLowerLevel = true;
                objTask.distributedParentOrgLevel = data.orgLevel;
                objTask.distributedParentOrgID = data.orgId;

                objTask.tenantId = userDetails.tenant_id;
                objTask.taskstatus = string.IsNullOrEmpty(data.taskstatus) ? 0 : int.Parse(data.taskstatus);
                objTask.createdBy = userDetails.pk_id;
                objTask.createdDate = DateTime.UtcNow;
                objTask.userAssignedTo = string.IsNullOrEmpty(data.userAssignedTo.ToString()) ? 0 : int.Parse(data.userAssignedTo.ToString());
                if (!string.IsNullOrEmpty(data.userAssignedTo.ToString()) && data.userAssignedTo.Value != 0)
                {
                    usersListforNotifications.Add(data.userAssignedTo.Value);
                }
                objTask.orgCreatedAt = data.orgId;
                objTask.orglevelCreatedAt = data.orgLevel;
                objTask.updatedBy = userDetails.pk_id;
                objTask.updatetDate = DateTime.UtcNow;
                objTask.description_history = textGuid = Guid.NewGuid();
                await tenantDbContext.tbitasks.AddAsync(objTask);
            }
            sb.AppendLine("3344 -> " + DateTime.UtcNow.ToString());
        }
        else
        {
            var existingData = await tenantDbContext.tbitasks.FirstOrDefaultAsync(x => x.uniquetaskId == data.taskId && x.tenantId == userDetails.tenant_id
                && x.budgetYear == data.budgetYear && x.orgId == data.orgId && x.orgLevel == data.orgLevel);

            if (existingData != null)
            {
                isTaskNameChanged = existingData.taskname != data.taskname;
                existingData.taskname = data.taskname;
                if (!string.Equals(existingData.description, data.description))
                {
                    is_desc_save_log = true;
                }

                existingData.description = textToSave = data.description;
                existingData.startDate = data.startDate;
                existingData.endDate = data.endDate;
                if (!updateMonthlyReportTaskStatus)
                    existingData.taskstatus = string.IsNullOrEmpty(data.taskstatus) ? 0 : int.Parse(data.taskstatus);
                if (!string.IsNullOrEmpty(data.userAssignedTo.ToString()) && data.userAssignedTo.Value != 0
                                                                          && existingData.userAssignedTo != data.userAssignedTo.Value)
                {
                    usersListforNotifications.Add(data.userAssignedTo.Value);
                }
                existingData.userAssignedTo = string.IsNullOrEmpty(data.userAssignedTo.ToString()) ? 0 : int.Parse(data.userAssignedTo.ToString());
                existingData.updatedBy = userDetails.pk_id;
                existingData.updatetDate = DateTime.UtcNow;
                textGuid = existingData.description_history == null ? Guid.Empty : existingData.description_history.Value;
                unique_task_id = existingData.uniquetaskId;
                tenantDbContext.Entry(existingData).State = EntityState.Modified;
            }
            sb.AppendLine("3376 -> " + DateTime.UtcNow.ToString());
        }

        //#176542 - changing responsible for task at current lvl affects all delegated lvls 
        /*var childTasks = await tenantDbContext.tbitasks.Where(x => x.uniquetaskId == unique_task_id && x.budgetYear == data.budgetYear && x.tenantId == userDetails.tenant_id).ToListAsync();

        //childTasks = childTasks.Where(x => data.taskGridData.Any(k => k.key == x.taskid)).ToList();

        if (childTasks.Count > 0)
        {
            foreach (var t in childTasks)
            {

                t.userAssignedTo = string.IsNullOrEmpty(data.userAssignedTo.ToString()) ? 0 : int.Parse(data.userAssignedTo.ToString());
                t.updatedBy = userDetails.pk_id;
                t.updatetDate = DateTime.UtcNow;
                t.endDate = data.endDate;
            }
        }*/

        await tenantDbContext.SaveChangesAsync();
        if (data.logHistory && is_desc_save_log)
        {
            await _pUtility.SaveTextLogAsync(userId, textGuid, textToSave);
        }

        if (data.treeViewCheckedData.Count > 0)
        {
            List<BPTreeHelper> selected_branch = new List<BPTreeHelper>();
            data.treeViewCheckedData.RemoveAll(x => x.orgId == data.orgId && x.orgLevel == data.orgLevel);
            foreach (var item in data.treeViewCheckedData)
            {
                var firstData = selected_branch.FirstOrDefault(x => x.orgId == item.orgId && x.orgLevel == item.orgLevel);
                if (firstData == null)
                {
                    selected_branch.Add(new BPTreeHelper
                    {
                        orgId = item.orgId,
                        orgLevel = item.orgLevel,
                        isChecked = item.isChecked,
                        dirty = item.dirty
                    });
                }
                if (firstData != null && !firstData.isChecked && item.isChecked)
                {
                    selected_branch.FirstOrDefault(x => x.orgId == item.orgId && x.orgLevel == item.orgLevel).isChecked = item.isChecked;
                }
            }

            List<tbitasks> child_tasks = new List<tbitasks>();
            List<tbitasks> child_tasks_delete = new List<tbitasks>();
            tbitasks child;
            Guid assignmentId = Guid.Empty;
            Guid uniqueAssignmentId = Guid.Empty;
            List<tbitasks> childTaskList = await tenantDbContext.tbitasks.Where(x => x.uniquetaskId == unique_task_id && x.budgetYear == assignmentData.budgetYear && x.tenantId == userDetails.tenant_id).ToListAsync();
            List<tbiassignments> childAssignList = await tenantDbContext.tbiassignments.Where(x => x.uniqueassignmentId == unique_ass_id && x.budgetYear == assignmentData.budgetYear && x.tenantId == userDetails.tenant_id).ToListAsync();
            List<TbiAssignmentTarget> assignTargetList = await tenantDbContext.TbiAssignmentTarget.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == assignmentData.budgetYear).ToListAsync();
            List<TbiAssignmentStrategy> assignStrategyList = await tenantDbContext.TbiAssignmentStrategy.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == assignmentData.budgetYear).ToListAsync();
            sb.AppendLine("3424 -> " + DateTime.UtcNow.ToString());
            foreach (var br in selected_branch)
            {
                // to check if child_task already exists.
                var child_task = childTaskList.FirstOrDefault(x => x.orgId == br.orgId && x.orgLevel == br.orgLevel);
                // if yes, check if it has to delete else continue
                if (child_task != null)
                {
                    if (!br.isChecked)
                    {
                        child_tasks_delete.Add(child_task);
                    }
                    else
                    {
                        continue;
                    }
                }
                else// if not, create it accrodingly.
                {
                    if (br.isChecked)
                    {
                        var child_ass = childAssignList.FirstOrDefault(x => x.orgId == br.orgId && x.orgLevel == br.orgLevel);

                        if (child_ass != null)
                        {
                            child = new tbitasks();

                            child.taskid = Guid.NewGuid();
                            child.uniquetaskId = unique_task_id;
                            child.assignmentId = child_ass.assignmentId;
                            child.taskname = data.taskname;
                            child.budgetYear = data.budgetYear;
                            child.orgId = br.orgId;
                            child.orgLevel = br.orgLevel;
                            child.serviceId = string.IsNullOrEmpty(data.serviceId) || data.serviceId == "-1" ? string.Empty : data.serviceId;
                            child.serviceLevel = 0;
                            child.taskServiceId = string.Empty;
                            child.taskServiceLevel = 0;
                            child.description = string.Empty;
                            child.startDate = data.startDate;
                            child.endDate = data.endDate;

                            child.isDistributedtoLowerLevel = true;
                            child.distributedParentOrgLevel = data.orgLevel;
                            child.distributedParentOrgID = data.orgId;

                            child.tenantId = userDetails.tenant_id;
                            child.taskstatus = 0;
                            child.createdBy = userDetails.pk_id;
                            child.createdDate = DateTime.UtcNow;
                            child.userAssignedTo = string.IsNullOrEmpty(data.userAssignedTo.ToString()) ? 0 : int.Parse(data.userAssignedTo.ToString());
                            if (!string.IsNullOrEmpty(data.userAssignedTo.ToString()) && data.userAssignedTo.Value != 0)
                            {
                                usersListforNotifications.Add(data.userAssignedTo.Value);
                            }
                            child.updatedBy = 0;
                            child.updatetDate = DateTime.UtcNow;
                            child.orgCreatedAt = data.orgId;
                            child.orglevelCreatedAt = data.orgLevel;
                            var historyID = Guid.NewGuid();
                            child.description_history = historyID;
                            child_tasks.Add(child);
                            await tenantDbContext.tbitasks.AddAsync(child);
                            textHistory.Add(historyID, textToSave);
                        }
                        else
                        {
                            Guid new_chid_ass_id = Guid.Empty;

                            // added the below condition for creating child assignment for the same unique assignment
                            // which doesn't exist for the new node for the original assignment while delegating task
                            if (ass_detail != null)
                            {
                                var existingAssignmentData = childAssignList.FirstOrDefault(x => x.isDistributedtoLowerLevel == false);
                                tbiassignments objAss = new tbiassignments();
                                objAss.assignmentId = new_chid_ass_id = Guid.NewGuid();
                                objAss.uniqueassignmentId = unique_ass_id;
                                objAss.assignmentName = ass_detail.assignmentName;
                                objAss.budgetYear = data.budgetYear;
                                objAss.parentOrgId = data.orgId;
                                objAss.parentOrgLevel = data.orgLevel;
                                objAss.orgId = br.orgId;
                                objAss.orgLevel = br.orgLevel;
                                objAss.description = string.Empty;
                                objAss.description_history = Guid.Empty;
                                objAss.serviceId = string.IsNullOrEmpty(data.serviceId) ? string.Empty : data.serviceId;
                                objAss.serviceLevel = 0;
                                objAss.startDate = data.startDate;
                                objAss.endDate = data.endDate;
                                objAss.category = ass_detail.category;
                                objAss.tags = ass_detail.tags;
                                objAss.tenantId = userDetails.tenant_id;
                                objAss.createdby = userDetails.pk_id;
                                objAss.createddate = DateTime.UtcNow;
                                objAss.userAssignedTo = 0;
                                objAss.updatedBy = userDetails.pk_id;
                                objAss.updated = DateTime.UtcNow;
                                objAss.external_reference = string.Empty;
                                objAss.isDistributedtoLowerLevel = existingAssignmentData != null ? existingAssignmentData.parentOrgId != ass_detail.orgId && existingAssignmentData.parentOrgLevel != ass_detail.orgLevel ? true : false : false;
                                objAss.distributedParentOrgID = existingAssignmentData != null ? existingAssignmentData.parentOrgId != ass_detail.orgId && existingAssignmentData.parentOrgLevel != ass_detail.orgLevel ? existingAssignmentData.parentOrgId : "" : "";
                                objAss.distributedParentOrgLevel = existingAssignmentData != null ? existingAssignmentData.parentOrgId != ass_detail.orgId && existingAssignmentData.parentOrgLevel != ass_detail.orgLevel ? existingAssignmentData.parentOrgLevel.Value : 0 : 0;
                                objAss.status = ass_detail.status;
                                objAss.is_readonly_ass = true;
                                objAss.assignmentrefid = assignmentRef;
                                objAss.assignmentOwner = assignmentOwner;
                                objAss.includedInMonthlyReport = includedInMonthlyReport;
                                objAss.start_year_fp = ass_detail.start_year_fp;
                                objAss.start_year_bp = ass_detail.start_year_bp;
                                objAss.end_year = ass_detail.end_year;
                                objAss.includedInMonthlyReport = includedInMonthlyReport;
                                await tenantDbContext.tbiassignments.AddAsync(objAss);
                                await tenantDbContext.SaveChangesAsync();

                                assignmentId = objAss.assignmentId;
                                uniqueAssignmentId = objAss.assignmentId;
                                assignmentData.goals = goals;
                                //save goals                                    
                                await saveTaskConnectionAsync(userId, assignmentData, assignmentId, uniqueAssignmentId,
                                    assignTargetList, assignGoalList, assignStrategyList);
                            }

                            child = new tbitasks();

                            child.taskid = Guid.NewGuid();
                            child.uniquetaskId = unique_task_id;
                            child.assignmentId = new_chid_ass_id;
                            child.taskname = data.taskname;
                            child.budgetYear = data.budgetYear;
                            child.orgId = br.orgId;
                            child.orgLevel = br.orgLevel;
                            child.serviceId = string.IsNullOrEmpty(data.serviceId) || data.serviceId == "-1" ? string.Empty : data.serviceId;
                            child.serviceLevel = 0;
                            child.taskServiceId = string.Empty;
                            child.taskServiceLevel = 0;
                            child.description = string.Empty;
                            child.startDate = data.startDate;
                            child.endDate = data.endDate;

                            child.isDistributedtoLowerLevel = true;
                            child.distributedParentOrgLevel = data.orgLevel;
                            child.distributedParentOrgID = data.orgId;

                            child.tenantId = userDetails.tenant_id;
                            child.taskstatus = 0;
                            child.createdBy = userDetails.pk_id;
                            child.createdDate = DateTime.UtcNow;
                            child.userAssignedTo = string.IsNullOrEmpty(data.userAssignedTo.ToString()) ? 0 : int.Parse(data.userAssignedTo.ToString());
                            if (!string.IsNullOrEmpty(data.userAssignedTo.ToString()) && data.userAssignedTo.Value != 0)
                            {
                                usersListforNotifications.Add(data.userAssignedTo.Value);
                            }
                            child.updatedBy = 0;
                            child.updatetDate = DateTime.UtcNow;
                            child.orgCreatedAt = data.orgId;
                            child.orglevelCreatedAt = data.orgLevel;
                            var historyID = Guid.NewGuid();
                            child.description_history = historyID;
                            child_tasks.Add(child);
                            await tenantDbContext.tbitasks.AddAsync(child);
                            textHistory.Add(historyID, textToSave);
                        }
                    }
                }
            }

            await tenantDbContext.BulkDeleteAsync(child_tasks_delete);
            await tenantDbContext.SaveChangesAsync();
        }
        sb.AppendLine("3603 -> " + DateTime.UtcNow.ToString());
        if (usersListforNotifications.Any())
        {
            await SendTasksandAssignmentNotificationsAsync(userId, usersListforNotifications, false);
        }

        // #164556 - No description copy while task delegation
        //await UpdateTaskDescriptiontoDelegatedLevels(userId, data);

        sb.AppendLine("3602 -> " + DateTime.UtcNow.ToString());
        if (!isFirstTimedelegated && is_desc_save_log && textHistory.Any())
        {
            await UpdatetaskDelegatedStatusAsync(userId, data);
        }
        if (isTaskNameChanged)
            await UpdateTaskNameAsync(userId, data.taskId, data.taskname, data.orgId, data.orgLevel);
        sb.AppendLine($"busplan assigntask delegation finished @ {DateTime.UtcNow}");
        await InsertPerformanceLog(userId, sb.ToString(), "busplan assigntask delegation save");
        return true;
    }


    // 40349 Below method has been added to update the task name,
    // start date, end date, responsible user, status,description for a unique task id  when the tree is not dirty

    public async Task<JObject> GetBPUserSpecificTaskGridAsync(string userId, int budgetYear, string orgId, int orgIdlevelNo, string categoryId, string serviceId, bool showDelegatedTasks = false, bool isWebDocPublish = false, bool isCategorySelected = false)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValuesBusinessPlan = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BusinessPlan");

        // Build JSON for Bestilling Grid
        dynamic bestillingGridData = new JObject();
        JArray gridHeader = new JArray();

        // Headers
        dynamic headerTitle = new JObject();
        headerTitle.title = langStringValuesBusinessPlan.FirstOrDefault(v => v.Key == "SUBP_Grid2_title").Value.LangText;
        headerTitle.descriptiontip = langStringValuesBusinessPlan.FirstOrDefault(v => v.Key == "SUBP_Grid2_descriptiontip").Value.LangText;
        gridHeader.Add(headerTitle);
        bestillingGridData.Add("header", gridHeader);

        // Fetching Columns
        dynamic bestillingGridColumns = GetColumnsforGrid2(langStringValuesBusinessPlan, budgetYear);
        bestillingGridData.Add("columns", bestillingGridColumns);

        // Fetching Data
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        var orgVersionContent = await _pUtility.GetOrgVersionSpecificContentAsync(userId, _pUtility.GetForecastPeriod(budgetYear, 1));
        List<tco_org_level> lstOrgLevels = orgVersionContent.lstOrgLevel.OrderBy(y => y.org_level).ToList();
        List<tco_org_hierarchy> lstOrgHierarchy = orgVersionContent.lstOrgHierarchy;
        var tagsList = (from atg in tenantDbContext.tcoActionTags
            where atg.FkTenantId == userDetails.tenant_id
            select new
            {
                key = atg.PkId,
                value = atg.TagDescription
            }).OrderBy(x => x.key).ToList();
        //148225
        serviceId = isWebDocPublish && serviceId == "-2" ? "-1" : serviceId;
        List<BusinessPlanUserData> activeUsers = await GetUserOrgData(userId, budgetYear);

        switch (orgIdlevelNo)
        {
            case 1:
                lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_1 == orgId).ToList();
                break;

            case 2:
                lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_2 == orgId).ToList();
                break;

            case 3:
                lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_3 == orgId).ToList();
                break;

            case 4:
                lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_4 == orgId).ToList();
                break;

            case 5:
                lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_5 == orgId).ToList();
                break;

            default:
                break;
        }

        var hierarchyList = (from p in lstOrgHierarchy
            select new
            {
                tenantID = p.fk_tenant_id,
                orgID_1 = p.org_id_1,
                orgName_1 = p.org_name_1,
                orgLevel_1 = 1,
                orgID_2 = p.org_id_2,
                orgName_2 = p.org_name_2,
                orgLevel_2 = 2,
                orgID_3 = p.org_id_3,
                orgName_3 = p.org_name_3,
                orgLevel_3 = 3,
                orgID_4 = p.org_id_4,
                orgName_4 = p.org_name_4,
                orgLevel_4 = 4,
                orgID_5 = p.org_id_5,
                orgName_5 = p.org_name_5,
                orgLevel_5 = 5
            }).Distinct().ToList();

        var statuses = await (from p in tenantDbContext.tco_progress_status
            where p.fk_tenant_id == userDetails.tenant_id && p.type.ToLower() == "sunit_bplan"
            select p).ToListAsync();

        List<string> categoryTypes = new List<string>() { "sunit_bplan", "climate_action" };
        var categories = await (from p in tenantDbContext.tco_category
            where p.fk_tenant_id == userDetails.tenant_id && categoryTypes.Contains(p.type.ToLower()) && p.status == 1
            select p).ToListAsync();

        Guid guid2 = Guid.Empty;
        var result = string.IsNullOrEmpty(serviceId) || serviceId == "-1" ?
            await (from assignment in tenantDbContext.tbiassignments
                join task in tenantDbContext.tbitasks
                    on new { ID = assignment.assignmentId, Orgid = assignment.orgId }
                    equals new { ID = task.assignmentId.Value, Orgid = task.orgId }
                where assignment.orgId == orgId && assignment.budgetYear == budgetYear && assignment.tenantId == userDetails.tenant_id
                      && task.budgetYear == budgetYear
                select new clsTaskData
                {
                    pkId = task.taskid,
                    catColor = categoryColor,
                    assignedOrgId = assignment.parentOrgId,
                    assignedOrgLevel = (int)assignment.parentOrgLevel,
                    assignmentId = assignment.assignmentId,
                    assignment = assignment.assignmentName,
                    assignmentDescription = assignment.description,
                    measures = task.taskname,
                    status = (int)task.taskstatus,
                    deadline = (DateTime)task.endDate,
                    description = task.description,
                    tags = assignment.tags,
                    filter = assignment.category,
                    userAssignedTo = (int)task.userAssignedTo,
                    uniqueTaskId = task.uniquetaskId,
                    taskOrgId = task.orgId,
                    uniqueAssignmentId = assignment.uniqueassignmentId,
                    climateCategoryId = assignment.climate_category,
                    serviceId = assignment.serviceId,
                }).OrderBy(x => x.assignmentId).ToListAsync()
            :
            await (from assignment in tenantDbContext.tbiassignments
                join task in tenantDbContext.tbitasks
                    on new { ID = assignment.assignmentId, Orgid = assignment.orgId }
                    equals new { ID = task.assignmentId.Value, Orgid = task.orgId }
                where assignment.orgId == orgId && assignment.budgetYear == budgetYear && assignment.tenantId == userDetails.tenant_id && assignment.serviceId == serviceId
                      && task.budgetYear == budgetYear
                select new clsTaskData
                {
                    pkId = task.taskid,
                    catColor = categoryColor,
                    assignedOrgId = assignment.parentOrgId,
                    assignedOrgLevel = (int)assignment.parentOrgLevel,
                    assignmentId = assignment.assignmentId,
                    assignment = assignment.assignmentName,
                    assignmentDescription = assignment.description,
                    measures = task.taskname,
                    status = (int)task.taskstatus,
                    deadline = (DateTime)task.endDate,
                    description = task.description,
                    tags = assignment.tags,
                    filter = assignment.category,
                    userAssignedTo = (int)task.userAssignedTo,
                    createdDate = (DateTime)task.createdDate,
                    uniqueTaskId = task.uniquetaskId,
                    taskOrgId = task.orgId,
                    uniqueAssignmentId = assignment.uniqueassignmentId,
                    climateCategoryId = assignment.climate_category,
                    serviceId = assignment.serviceId,
                }).OrderBy(x => x.assignmentId).ToListAsync();

        result = (from p in result
            select new clsTaskData
            {
                pkId = p.pkId,
                catColor = p.catColor,
                assignedOrgId = p.assignedOrgId,
                assignedOrgLevel = p.assignedOrgLevel,
                assignmentId = p.assignmentId,
                assignment = p.assignment,
                assignmentDescription = p.assignmentDescription,
                measures = p.measures,
                status = p.status,
                deadline = p.deadline,
                description = p.description,
                tags = p.tags,
                filter = p.filter,
                userAssignedTo = p.userAssignedTo,
                createdDate = p.createdDate,
                filterName = _pUtility.GetCategoryDetails(p.filter, categories, p.climateCategoryId),
                uniqueTaskId = p.uniqueTaskId,
                taskOrgId = p.taskOrgId,
                uniqueAssignmentId = p.uniqueAssignmentId,
                serviceId = p.serviceId
            }).ToList();

        var unassignmentName = langStringValuesBusinessPlan.FirstOrDefault(v => v.Key == "SUBP_Grid2_DetachedTasks").Value.LangText;

        var NonassignedTasks = string.IsNullOrEmpty(serviceId) || serviceId == "-1" ? await (from task in tenantDbContext.tbitasks
            where task.orgId == orgId && task.assignmentId == guid2 && task.budgetYear == budgetYear && task.tenantId == userDetails.tenant_id
            select new clsTaskData
            {
                pkId = task.taskid,
                catColor = string.Empty,
                assignedOrgId = string.Empty,
                assignedOrgLevel = 0,
                assignmentId = guid2,
                assignment = unassignmentName,
                measures = task.taskname,
                status = (int)task.taskstatus,
                deadline = task.endDate,
                description = task.description,
                userAssignedTo = (int)task.userAssignedTo,
                tags = string.Empty,
                filter = string.Empty,
                createdDate = task.createdDate,
                filterName = string.Empty,
                uniqueTaskId = task.uniquetaskId,
                taskOrgId = task.orgId,
                uniqueAssignmentId = Guid.Empty,
                climateCategoryId = Guid.Empty,
                serviceId = task.serviceId
            }).ToListAsync() : await (from task in tenantDbContext.tbitasks
            where task.orgId == orgId && task.assignmentId == guid2 && task.budgetYear == budgetYear && task.tenantId == userDetails.tenant_id && task.serviceId == serviceId
                  && task.serviceId == serviceId
            select new clsTaskData
            {
                pkId = task.taskid,
                catColor = string.Empty,
                assignedOrgId = string.Empty,
                assignedOrgLevel = 0,
                assignmentId = guid2,
                assignment = unassignmentName,
                measures = task.taskname,
                status = (int)task.taskstatus,
                deadline = task.endDate,
                description = task.description,
                userAssignedTo = (int)task.userAssignedTo,
                tags = string.Empty,
                filter = string.Empty,
                createdDate = task.createdDate,
                filterName = string.Empty,
                uniqueTaskId = task.uniquetaskId,
                taskOrgId = task.orgId,
                uniqueAssignmentId = Guid.Empty,
                climateCategoryId = Guid.Empty,
                serviceId = task.serviceId
            }).ToListAsync();

        NonassignedTasks = (from task in NonassignedTasks
            select new clsTaskData
            {
                pkId = task.pkId,
                catColor = task.catColor,
                assignedOrgId = task.assignedOrgId,
                assignedOrgLevel = task.assignedOrgLevel,
                assignmentId = task.assignmentId,
                assignment = task.assignment,
                assignmentDescription = string.Empty,
                measures = task.measures,
                status = task.status,
                deadline = task.deadline,
                description = task.description,
                userAssignedTo = task.userAssignedTo,
                tags = task.tags,
                filter = task.filter,
                createdDate = task.createdDate,
                filterName = _pUtility.GetCategoryDetails(task.filter, categories, task.climateCategoryId),
                uniqueTaskId = task.uniqueTaskId,
                taskOrgId = task.taskOrgId,
                uniqueAssignmentId = task.uniqueAssignmentId,
                serviceId = task.serviceId
            }).ToList();

        var taskSet = result.Union(NonassignedTasks).OrderBy(x => x.filterName).ThenBy(y => y.assignment).ToList();

        taskSet = (from task in taskSet
            select new clsTaskData
            {
                pkId = task.pkId,
                catColor = task.catColor,
                assignedOrgId = task.assignedOrgId,
                assignedOrgLevel = task.assignedOrgLevel,
                assignmentId = task.assignmentId,
                assignment = task.assignment,
                assignmentDescription = task.assignmentDescription,
                measures = task.measures,
                status = task.status,
                deadline = task.deadline,
                description = GetDescriptionforTask(userId, task.pkId, orgId, orgIdlevelNo, budgetYear, false, false),
                userAssignedTo = task.userAssignedTo,
                tags = task.tags,
                filter = task.filter,
                createdDate = task.createdDate,
                filterName = task.filterName,
                uniqueTaskId = task.uniqueTaskId,
                taskOrgId = task.taskOrgId,
                uniqueAssignmentId = task.uniqueAssignmentId,
                monthRepStatusDesc = string.Empty,
                serviceId = task.serviceId
            }).ToList();

        Dictionary<string, clsLanguageString> langStr = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BusinessPlan");
        List<tco_service_values> tServs = await tenantDbContext.tco_service_values.Where(t => t.fk_tenant_id == userDetails.tenant_id).ToListAsync();

        var monthrepTaskData = await GetStatusDescriptionforTaskAsync(userId, budgetYear);
        var taskListAsync = GetTaskListAsync(userDetails.tenant_id, orgId, serviceId, orgIdlevelNo);
        var taskStatusListAsync = GetTaskStatusListAsync(userDetails.tenant_id);

        await Task.WhenAll(taskListAsync, taskStatusListAsync);

        var taskList = taskListAsync.Result;
        var taskStatusList = taskStatusListAsync.Result;

        taskSet.ForEach(x =>
        {
            monthrepTaskData = monthrepTaskData?.Where(a => a.fk_task_id == x.pkId).ToList();
            if (monthrepTaskData.Count > 0)
            {
                var forecastPeriod = monthrepTaskData.Select(x => x.forecast_period).Max();
                var monthrepTask = monthrepTaskData.FirstOrDefault(x => x.forecast_period == forecastPeriod);
                x.monthRepStatusDesc = System.Net.WebUtility.HtmlDecode(monthrepTask?.description ?? string.Empty);

                var uniqueTask = taskList?.FirstOrDefault(a => a.uniquetaskId == x.uniqueTaskId);

                if (x.uniqueTaskId == Guid.Empty ? false : (uniqueTask != null && taskStatusList.Any(x => x.fk_task_id == uniqueTask.taskid)))
                {
                    x.status = monthrepTask?.status ?? 0;
                }
            }

            //Service data
            if (tServs.Count > 0 && serviceId != "-2")
            {
                if (string.IsNullOrEmpty(x.serviceId) || x.serviceId == "-1" || x.serviceId.ToLower() == "all")
                {
                    x.serviceArea = ((langStr.FirstOrDefault(v => v.Key == "AT_Grid_col_serviceall")).Value).LangText;
                }
                else
                {
                    tco_service_values tsv = tServs.FirstOrDefault(t => t.service_id_1 == x.serviceId);
                    if (tsv != null)
                        x.serviceArea = x.serviceId + " " + tsv.service_name_1;
                    else
                    {
                        tsv = tServs.FirstOrDefault(t => t.service_id_2 == x.serviceId);

                        if (tsv != null)
                            x.serviceArea = x.serviceId + " " + tsv.service_name_2;
                        else
                        {
                            tsv = tServs.FirstOrDefault(t => t.service_id_3 == x.serviceId);
                            if (tsv != null)
                                x.serviceArea = x.serviceId + " " + tsv.service_name_3;
                            else
                            {
                                tsv = tServs.FirstOrDefault(t => t.service_id_4 == x.serviceId);
                                if (tsv != null)
                                    x.serviceArea = x.serviceId + " " + tsv.service_name_4;
                                else
                                {
                                    tsv = tServs.FirstOrDefault(t => t.service_id_5 == x.serviceId);
                                    if (tsv != null)
                                        x.serviceArea = x.serviceId + " " + tsv.service_name_5;
                                }
                            }
                        }
                    }
                }
            }
        });
        var categoryGuid = Guid.Parse(categoryId);
        if (categoryGuid != guid2)
        {
            taskSet = taskSet.Where(x => x.filter.ToLower().Split(',').ToList().Contains(categoryGuid.ToString())).ToList();
        }

        var groupByAssignmentIds = (from p in taskSet
            group p by new { p.assignmentId, p.filter, p.filterName } into temp
            select new
            {
                assignmentId = temp.Key.assignmentId,
                filter = temp.Key.filter,
                filterName = temp.Key.filterName,
                count = temp.Count()
            }).OrderBy(x => x.filterName).ToList();

        var finalSet = new List<clsTaskData>();
        for (int i = 0; i < groupByAssignmentIds.Count; i++)
        {
            var individualDataListperAssignmentID = taskSet.Where(s => s.assignmentId == groupByAssignmentIds[i].assignmentId && s.filter == groupByAssignmentIds[i].filter)
                .OrderBy(x => x.filterName).ThenBy(y => y.assignment).ToList();
            for (int x = 0; x < individualDataListperAssignmentID.Count; x++)
            {
                var assignmentID = groupByAssignmentIds[i].assignmentId;
                var uniqueTaskID = individualDataListperAssignmentID[x].uniqueTaskId;
                var orgData = tenantDbContext.tbitasks.Where(z => z.tenantId == userDetails.tenant_id && z.uniquetaskId == uniqueTaskID).OrderBy(z => z.orgLevel).FirstOrDefault();
                if (x == groupByAssignmentIds[i].count - 1)
                {
                    finalSet.Add(new clsTaskData
                    {
                        pkId = individualDataListperAssignmentID[x].pkId,
                        catColor = individualDataListperAssignmentID[x].catColor,
                        assignedOrgId = individualDataListperAssignmentID[x].assignedOrgId,
                        assignedOrgLevel = x > 0 && !isWebDocPublish ? 0 : individualDataListperAssignmentID[x].assignedOrgLevel,
                        assignmentId = individualDataListperAssignmentID[x].assignmentId,
                        assignmentDescription = individualDataListperAssignmentID[x].assignmentDescription,
                        assignment = x > 0 && !isWebDocPublish ? string.Empty : GetAssignmentName(individualDataListperAssignmentID[x].assignment, individualDataListperAssignmentID[x].tags, userId),
                        assignmentname = x > 0 && !isWebDocPublish ? string.Empty : individualDataListperAssignmentID[x].assignment,
                        measures = individualDataListperAssignmentID[x].measures,
                        status = individualDataListperAssignmentID[x].status,
                        deadline = individualDataListperAssignmentID[x].deadline,
                        description = individualDataListperAssignmentID[x].description,
                        userAssignedTo = individualDataListperAssignmentID[x].userAssignedTo,
                        tags = individualDataListperAssignmentID[x].tags,
                        filter = x > 0 && !isWebDocPublish ? guid2.ToString() : individualDataListperAssignmentID[x].filter,
                        isLastRow = individualDataListperAssignmentID[x].assignmentId == guid2 ? false : true,
                        filterName = x > 0 && !isWebDocPublish ? string.Empty : individualDataListperAssignmentID[x].filterName,
                        isDelete = orgData == null ? false : orgData.orgId == orgId && orgData.orgLevel == orgIdlevelNo,
                        uniqueTaskId = individualDataListperAssignmentID[x].uniqueTaskId,
                        taskOrgId = individualDataListperAssignmentID[x].taskOrgId,
                        uniqueAssignmentId = individualDataListperAssignmentID[x].uniqueAssignmentId,
                        monthRepStatusDesc = individualDataListperAssignmentID[x].monthRepStatusDesc,
                        serviceArea = individualDataListperAssignmentID[x].serviceArea
                    });
                }
                else
                {
                    finalSet.Add(new clsTaskData
                    {
                        pkId = individualDataListperAssignmentID[x].pkId,
                        catColor = individualDataListperAssignmentID[x].catColor,
                        assignedOrgId = individualDataListperAssignmentID[x].assignedOrgId,
                        assignedOrgLevel = x > 0 && !isWebDocPublish ? 0 : individualDataListperAssignmentID[x].assignedOrgLevel,
                        assignmentId = individualDataListperAssignmentID[x].assignmentId,
                        assignmentDescription = individualDataListperAssignmentID[x].assignmentDescription,
                        assignment = x > 0 && !isWebDocPublish ? string.Empty : GetAssignmentName(individualDataListperAssignmentID[x].assignment, individualDataListperAssignmentID[x].tags, userId),
                        assignmentname = x > 0 && !isWebDocPublish ? string.Empty : individualDataListperAssignmentID[x].assignment,
                        measures = individualDataListperAssignmentID[x].measures,
                        status = individualDataListperAssignmentID[x].status,
                        deadline = individualDataListperAssignmentID[x].deadline,
                        description = individualDataListperAssignmentID[x].description,
                        userAssignedTo = individualDataListperAssignmentID[x].userAssignedTo,
                        tags = individualDataListperAssignmentID[x].tags,
                        filter = x > 0 && !isWebDocPublish ? guid2.ToString() : individualDataListperAssignmentID[x].filter,
                        isLastRow = individualDataListperAssignmentID[x].assignmentId == guid2 ? false : x < groupByAssignmentIds[i].count ? false : true,
                        filterName = x > 0 && !isWebDocPublish ? string.Empty : individualDataListperAssignmentID[x].filterName,
                        isDelete = orgData == null ? false : orgData.orgId == orgId && orgData.orgLevel == orgIdlevelNo,
                        uniqueTaskId = individualDataListperAssignmentID[x].uniqueTaskId,
                        taskOrgId = individualDataListperAssignmentID[x].taskOrgId,
                        uniqueAssignmentId = individualDataListperAssignmentID[x].uniqueAssignmentId,
                        monthRepStatusDesc = individualDataListperAssignmentID[x].monthRepStatusDesc,
                        serviceArea = individualDataListperAssignmentID[x].serviceArea
                    });
                }
            }
        }

        //Read all assignments tasks data
        var taskResult = await tenantDbContext.tbitasks.Where(x => x.tenantId == userDetails.tenant_id && x.budgetYear == budgetYear && (x.orgLevel == orgIdlevelNo || x.orgLevel == orgIdlevelNo + 1)).AsNoTracking().Distinct().ToListAsync();
        //Fetch tasks from one next level from the current org level
        var data = await (from a in tenantDbContext.tbiassignments
            join b in tenantDbContext.tbitasks on new { a = a.assignmentId.ToString() } equals new { a = b.assignmentId.ToString() }
            where a.tenantId == userDetails.tenant_id && b.budgetYear == budgetYear && (b.orgLevel == orgIdlevelNo || b.orgLevel == orgIdlevelNo + 1)
            select new
            {
                uniqueAssignmentId = a.uniqueassignmentId,
                uniqueTaskId = b.uniquetaskId,
                taskName = b.taskname
            }).Distinct().OrderBy(x => x.taskName).ToListAsync();
        var finalData = new JArray();
        List<string> validOrgIds = new List<string>();
        validOrgIds.AddRange(hierarchyList.Select(x => x.orgID_1).ToList());
        validOrgIds.AddRange(hierarchyList.Select(x => x.orgID_2).ToList());
        validOrgIds.AddRange(hierarchyList.Select(x => x.orgID_3).ToList());
        validOrgIds.AddRange(hierarchyList.Select(x => x.orgID_4).ToList());
        validOrgIds.AddRange(hierarchyList.Select(x => x.orgID_5).ToList());
        //Remove duplicates
        List<Guid> assignmentIdsList = new List<Guid>();
        List<clsTaskData> uniqueAssignmentData = new List<clsTaskData>();
        foreach (var a in finalSet)
        {
            if (!assignmentIdsList.Contains(a.assignmentId))
            {
                assignmentIdsList.Add(a.assignmentId);
                uniqueAssignmentData.Add(a);
            }
        }
        finalSet = showDelegatedTasks ? uniqueAssignmentData.OrderBy(x => x.assignmentname).ToList() : finalSet;

        if (isWebDocPublish)
        {
            finalSet = (isCategorySelected) ? finalSet.OrderBy(x => x.assignment).ThenBy(x => x.measures).ToList() : finalSet.OrderBy(x => x.measures).ToList();
            finalSet = showDelegatedTasks ? finalSet : FormatTaskSetForDocExport(finalSet);
        }
        for (int i = 0; i < finalSet.Count; i++)
        {
            dynamic row = new JObject();
            int assignmentCount = 0;
            List<Guid> uniqueTaskIds = new List<Guid>();
            uniqueTaskIds = data != null ? data.Where(x => x.uniqueAssignmentId == finalSet[i].uniqueAssignmentId).Select(y => y.uniqueTaskId).Distinct().ToList() : new List<Guid>();
            if (showDelegatedTasks)  //For document and publish(While showing delegated tasks)
            {
                foreach (var r in uniqueTaskIds)
                {
                    var uniqueTaskData = taskResult.Where(x => x.uniquetaskId == r).Select(x => new { x.uniquetaskId, x.orgId, x.orgLevel, x.userAssignedTo, x.endDate, x.taskstatus, x.taskname, x.description }).OrderBy(x => x.orgLevel).ToList();

                    if (uniqueTaskData.Any())
                    {
                        int nextLowerLevel = orgIdlevelNo + 1;
                        uniqueTaskData = uniqueTaskData.Where(x => validOrgIds.Contains(x.orgId)).OrderBy(x => x.taskname).ThenBy(x => x.orgId).ToList(); //Get all delegated tasks for given assignment.

                        int taskCount = 0;
                        foreach (var t in uniqueTaskData)
                        {
                            dynamic taskObj = new JObject();
                            taskObj.assignmentDescription = assignmentCount == 0 ? finalSet[i].assignmentDescription : string.Empty;

                            string userResponsibleForTask = string.Empty;
                            if (int.Parse(t.userAssignedTo.ToString()) != 0)
                            {
                                var activeUserInfo = activeUsers.FirstOrDefault(x => x.Id == t.userAssignedTo);
                                if (activeUserInfo != null)
                                {
                                    userResponsibleForTask = activeUserInfo.FirstName + " " + activeUserInfo.LastName;
                                }
                            }
                            string delegatedTaskOrgName = string.Empty;
                            switch (t.orgLevel)
                            {
                                case 1:
                                    var orgData1 = hierarchyList.FirstOrDefault(x => x.orgID_1 == t.orgId.ToString());
                                    delegatedTaskOrgName = orgData1 != null ? orgData1.orgName_1 : string.Empty;
                                    break;

                                case 2:
                                    var orgData2 = hierarchyList.FirstOrDefault(x => x.orgID_2 == t.orgId.ToString());
                                    delegatedTaskOrgName = orgData2 != null ? orgData2.orgName_2 : string.Empty;
                                    break;

                                case 3:
                                    var orgData3 = hierarchyList.FirstOrDefault(x => x.orgID_3 == t.orgId.ToString());
                                    delegatedTaskOrgName = orgData3 != null ? orgData3.orgName_3 : string.Empty;
                                    break;

                                case 4:
                                    var orgData4 = hierarchyList.FirstOrDefault(x => x.orgID_4 == t.orgId.ToString());
                                    delegatedTaskOrgName = orgData4 != null ? orgData4.orgName_4 : string.Empty;
                                    break;

                                case 5:
                                    var orgData5 = hierarchyList.FirstOrDefault(x => x.orgID_5 == t.orgId.ToString());
                                    delegatedTaskOrgName = orgData5 != null ? orgData5.orgName_5 : string.Empty;
                                    break;

                                default:
                                    delegatedTaskOrgName = string.Empty;
                                    break;
                            }
                            taskObj.delegatedTaskOrgName = delegatedTaskOrgName;
                            taskObj.responsibility = userResponsibleForTask;
                            taskObj.isCurrentOrgUnit = t.orgId == orgId;
                            taskObj.status = t.taskstatus.ToString() == "0" ? string.Empty : statuses.FirstOrDefault(x => x.status_id == int.Parse(t.taskstatus.ToString())).status_description;
                            taskObj.deadline = string.IsNullOrEmpty(t.endDate.ToString()) ? string.Empty : t.endDate.ToString().Substring(0, 10);
                            taskObj.assignmentname = assignmentCount > 0 ? string.Empty : finalSet[i].assignmentname;
                            taskObj.measures = taskCount > 0 ? string.Empty : t.taskname;
                            taskObj.description = taskCount > 0 ? string.Empty : delegatedTaskOrgName + "</br>" + t.description;
                            taskObj.monthRepStatusDesc = finalSet[i].monthRepStatusDesc;
                            taskObj.serviceArea = finalSet[i].serviceArea;
                            finalData.Add(taskObj);
                            taskCount++;
                            assignmentCount++;
                        }
                    }
                }
            }
            else
            {
                row.pkId = finalSet[i].pkId;
                row.categoryId = finalSet[i].catColor;
                string categoryOrgLevelAssignedto = string.Empty;
                row.categoryname = string.IsNullOrEmpty(finalSet[i].filterName) ? string.Empty : finalSet[i].filterName;
                row.assignmentDescription = finalSet[i].assignmentDescription;
                row.description = finalSet[i].description;
                row.isLastRow = finalSet[i].isLastRow;
                row.assignmentId = finalSet[i].assignmentId;
                row.isTaskExists = finalSet[i].pkId == Guid.Empty ? false : true;
                var tagsSet = finalSet[i].tags.ToString();
                if (!string.IsNullOrEmpty(tagsSet))
                {
                    if (tagsSet.Split(',').Count() > 0)
                    {
                        JArray objTag = new JArray();
                        foreach (var tag in tagsSet.Split(','))
                        {
                            if (tagsList.FirstOrDefault(x => x.key == int.Parse(tag)) != null)
                            {
                                objTag.Add(tagsList.FirstOrDefault(x => x.key == int.Parse(tag)).value);
                            }
                        }
                        row.tags = objTag;
                    }
                }
                else
                {
                    row.tags = new JArray();
                }
                row.isDelete = finalSet[i].isDelete;
                switch (int.Parse(finalSet[i].assignedOrgLevel.ToString()))
                {
                    case 1:
                        var orgData1 = hierarchyList.FirstOrDefault(x => x.orgID_1 == finalSet[i].assignedOrgId.ToString());
                        categoryOrgLevelAssignedto = orgData1 != null ? orgData1.orgName_1 : string.Empty;
                        break;

                    case 2:
                        var orgData2 = hierarchyList.FirstOrDefault(x => x.orgID_2 == finalSet[i].assignedOrgId.ToString());
                        categoryOrgLevelAssignedto = orgData2 != null ? orgData2.orgName_2 : string.Empty;
                        break;

                    case 3:
                        var orgData3 = hierarchyList.FirstOrDefault(x => x.orgID_3 == finalSet[i].assignedOrgId.ToString());
                        categoryOrgLevelAssignedto = orgData3 != null ? orgData3.orgName_3 : string.Empty;
                        break;

                    case 4:
                        var orgData4 = hierarchyList.FirstOrDefault(x => x.orgID_4 == finalSet[i].assignedOrgId.ToString());
                        categoryOrgLevelAssignedto = orgData4 != null ? orgData4.orgName_4 : string.Empty;
                        break;

                    case 5:
                        var orgData5 = hierarchyList.FirstOrDefault(x => x.orgID_5 == finalSet[i].assignedOrgId.ToString());
                        categoryOrgLevelAssignedto = orgData5 != null ? orgData5.orgName_5 : string.Empty;
                        break;

                    default:
                        categoryOrgLevelAssignedto = string.Empty;
                        break;
                }
                row.category = categoryOrgLevelAssignedto;
                string userResponsibleFor = string.Empty;
                if (int.Parse(finalSet[i].userAssignedTo.ToString()) != 0)
                {
                    var activeUserInfo = activeUsers.FirstOrDefault(x => x.Id == finalSet[i].userAssignedTo);
                    if (activeUserInfo != null)
                    {
                        userResponsibleFor = activeUserInfo.FirstName + " " + activeUserInfo.LastName;
                    }
                }

                row.assignment = finalSet[i].assignment;
                row.assignmentname = finalSet[i].assignmentname;
                row.measures = finalSet[i].measures;
                row.status = finalSet[i].status.ToString() == "0" ? string.Empty : statuses.FirstOrDefault(x => x.status_id == int.Parse(finalSet[i].status.ToString())).status_description;
                row.deadline = string.IsNullOrEmpty(finalSet[i].deadline.ToString()) ? string.Empty : finalSet[i].deadline.ToString().Substring(0, 10);
                row.responsibility = userResponsibleFor;
                row.monthRepStatusDesc = finalSet[i].monthRepStatusDesc;
                row.serviceArea = finalSet[i].serviceArea;
                finalData.Add(row);
            }
        }
        bestillingGridData.Add("data", finalData);
        return bestillingGridData;
    }



    public async Task<dynamic> GetDataForSpecificAssignmentAsync(string userId, string assignmentId)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();

        var id = Guid.Parse(assignmentId);
        var assignmentData = await (from assignment in tenantDbContext.tbiassignments
            where assignment.assignmentId == id && assignment.tenantId == userDetails.tenant_id
            select new
            {
                assignmentId = assignment.assignmentId,
                category = assignment.category
            }).FirstOrDefaultAsync();

        if (assignmentData != null)
        {
            dynamic assignmentDataObject = new JObject();

            dynamic row = new JObject();
            row.assignment = assignmentData.assignmentId;
            row.category = assignmentData.category;

            assignmentDataObject.Add("data", row);
            return assignmentDataObject;
        }
        else
        {
            return new JObject();
        }
    }



    public async Task<bool> CreateTaskFromAssignmentGridAsync(string userId, int budgetYear, string pk_ass_id, string unique_assignment_id, string orgId, string serviceId, int orgLevel)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        tbiassignments flag_assignment_detail = null;
        List<int> usersListforNotification = new List<int>();

        if (string.IsNullOrEmpty(pk_ass_id))
        {
            Guid unique_ass_id = Guid.Parse(unique_assignment_id);
            if (string.IsNullOrEmpty(serviceId))
            {
                flag_assignment_detail = await tenantDbContext.tbiassignments.FirstOrDefaultAsync(x => x.tenantId == userDetails.tenant_id && x.budgetYear == budgetYear
                    && x.uniqueassignmentId == unique_ass_id && x.orgId == orgId && x.orgLevel == orgLevel);
            }
            else
            {
                flag_assignment_detail = (serviceId == "-1" || serviceId.ToLower() == "ALL".ToLower()) ?
                    await tenantDbContext.tbiassignments.FirstOrDefaultAsync(x => x.tenantId == userDetails.tenant_id && x.budgetYear == budgetYear
                        && x.uniqueassignmentId == unique_ass_id && x.orgId == orgId && x.orgLevel == orgLevel && (x.serviceId == "0" || x.serviceId == "-1" || x.serviceId.ToLower() == "ALL".ToLower() || string.IsNullOrEmpty(x.serviceId))) :
                    await tenantDbContext.tbiassignments.FirstOrDefaultAsync(x => x.tenantId == userDetails.tenant_id && x.budgetYear == budgetYear
                        && x.uniqueassignmentId == unique_ass_id && x.orgId == orgId && x.orgLevel == orgLevel && x.serviceId == serviceId);
            }
        }
        else
        {
            Guid ass_id = Guid.Parse(pk_ass_id);
            if (string.IsNullOrEmpty(serviceId))
            {
                flag_assignment_detail = await tenantDbContext.tbiassignments.FirstOrDefaultAsync(x => x.tenantId == userDetails.tenant_id && x.budgetYear == budgetYear
                    && x.assignmentId == ass_id && x.orgId == orgId && x.orgLevel == orgLevel);
            }
            else
            {
                flag_assignment_detail = (serviceId == "-1" || serviceId.ToLower() == "ALL".ToLower()) ?
                    await tenantDbContext.tbiassignments.FirstOrDefaultAsync(x => x.tenantId == userDetails.tenant_id && x.budgetYear == budgetYear
                        && x.assignmentId == ass_id && x.orgId == orgId && x.orgLevel == orgLevel && (x.serviceId == "0" || x.serviceId == "-1" || x.serviceId.ToLower() == "ALL".ToLower() || string.IsNullOrEmpty(x.serviceId))) :
                    await tenantDbContext.tbiassignments.FirstOrDefaultAsync(x => x.tenantId == userDetails.tenant_id && x.budgetYear == budgetYear
                        && x.assignmentId == ass_id && x.orgId == orgId && x.orgLevel == orgLevel && x.serviceId == serviceId);
            }
        }

        Guid guid2 = Guid.Empty;
        if (flag_assignment_detail != null)
        {
            var Id = Guid.NewGuid();
            var objTask = new tbitasks();
            objTask.taskid = Id;
            objTask.uniquetaskId = Guid.NewGuid();
            objTask.assignmentId = flag_assignment_detail.assignmentId;
            objTask.taskname = flag_assignment_detail.assignmentName;
            objTask.budgetYear = flag_assignment_detail.budgetYear;
            objTask.orgId = orgId;
            objTask.orgLevel = orgLevel;
            objTask.serviceId = (string.IsNullOrEmpty(serviceId) || serviceId == "-1" || serviceId.ToLower() == "ALL".ToLower()) ? string.Empty : serviceId;
            objTask.serviceLevel = 0;
            objTask.taskServiceId = string.Empty;
            objTask.taskServiceLevel = 0;
            objTask.description = string.Empty;
            objTask.startDate = flag_assignment_detail.startDate;
            objTask.endDate = flag_assignment_detail.endDate;

            objTask.isDistributedtoLowerLevel = true;
            objTask.distributedParentOrgLevel = orgLevel;
            objTask.distributedParentOrgID = orgId;

            objTask.tenantId = userDetails.tenant_id;
            objTask.taskstatus = 0;
            objTask.createdBy = userDetails.pk_id;
            objTask.createdDate = DateTime.UtcNow;
            objTask.userAssignedTo = 0;
            usersListforNotification.Add(userDetails.pk_id);

            objTask.updatedBy = 0;
            objTask.updatetDate = DateTime.UtcNow;
            objTask.orgCreatedAt = orgId;
            objTask.orglevelCreatedAt = orgLevel;
            objTask.description_history = Guid.NewGuid();
            await tenantDbContext.tbitasks.AddAsync(objTask);
            await tenantDbContext.SaveChangesAsync();
            if (usersListforNotification.Any())
            {
                await SendTasksandAssignmentNotificationsAsync(userId, usersListforNotification, false);
            }
            return true;
        }
        return false;
    }



    public async Task<JObject> GetAssignmentsAndTasksForSelectedOrgIdAsync(string userId, int budgetYear, string orgId, int orgIdlevelNo, string categoryId, BusinessPlanSearchAssignmentsGrid searchParams)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValuesBusinessPlan = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BusinessPlan");

        // Build JSON for Bestilling Grid
        dynamic bestillingGridData = new JObject();
        JArray gridHeader = new JArray();

        // Headers
        dynamic headerTitle = new JObject();
        headerTitle.title = langStringValuesBusinessPlan.FirstOrDefault(v => v.Key == "SUBP_Grid2_title").Value.LangText;
        headerTitle.descriptiontip = langStringValuesBusinessPlan.FirstOrDefault(v => v.Key == "SUBP_Grid2_descriptiontip").Value.LangText;
        gridHeader.Add(headerTitle);
        bestillingGridData.Add("header", gridHeader);

        // Fetching Columns
        dynamic bestillingGridColumns = GetColumnsforAssignmentAndTasksForSelectedOrgId(langStringValuesBusinessPlan);
        bestillingGridData.Add("columns", bestillingGridColumns);

        // Fetching Data
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        var orgVersionContent = await _pUtility.GetOrgVersionSpecificContentAsync(userId, _pUtility.GetForecastPeriod(budgetYear, 1));
        List<tco_org_level> lstOrgLevels = orgVersionContent.lstOrgLevel.OrderBy(y => y.org_level).ToList();

        List<tco_org_hierarchy> lstOrgHierarchy = orgVersionContent.lstOrgHierarchy;
        int lastOrgLevel = lstOrgLevels.ElementAt(lstOrgLevels.Count - 1).org_level;
        var tagsList = await (from atg in tenantDbContext.tcoActionTags
            where atg.FkTenantId == userDetails.tenant_id
            select new
            {
                key = atg.PkId,
                value = atg.TagDescription
            }).OrderBy(x => x.key).ToListAsync();

        var activeUsers = (await _pUtility.GetUserDetailsForTenantAsync(userId)).Where(x => x.IsActive).ToList();
        var lowerorgsubunitsList = GetLowerLevelOrgsUnitsList(orgIdlevelNo, orgId, lstOrgHierarchy);
        switch (orgIdlevelNo)
        {
            case 1:
                lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_1 == orgId).ToList();
                break;

            case 2:
                lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_2 == orgId).ToList();
                break;

            case 3:
                lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_3 == orgId).ToList();
                break;

            case 4:
                lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_4 == orgId).ToList();
                break;

            case 5:
                lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_5 == orgId).ToList();
                break;

            default:
                break;
        }

        var hierarchyList = (from p in lstOrgHierarchy
            select new
            {
                tenantID = p.fk_tenant_id,
                orgID_1 = p.org_id_1,
                orgName_1 = p.org_name_1,
                orgLevel_1 = 1,
                orgID_2 = p.org_id_2,
                orgName_2 = p.org_name_2,
                orgLevel_2 = 2,
                orgID_3 = p.org_id_3,
                orgName_3 = p.org_name_3,
                orgLevel_3 = 3,
                orgID_4 = p.org_id_4,
                orgName_4 = p.org_name_4,
                orgLevel_4 = 4,
                orgID_5 = p.org_id_5,
                orgName_5 = p.org_name_5,
                orgLevel_5 = 5
            }).Distinct().ToList();

        var statuses = await (from p in tenantDbContext.tco_progress_status
            where p.fk_tenant_id == userDetails.tenant_id && p.type.ToLower() == "sunit_bplan"
            select p).ToListAsync();

        List<string> categoryTypes = new List<string>() { "sunit_bplan", "climate_action" };
        var categories = await (from p in tenantDbContext.tco_category
            where p.fk_tenant_id == userDetails.tenant_id && categoryTypes.Contains(p.type.ToLower()) && p.status == 1
            select p).ToListAsync();

        Guid guid2 = Guid.Empty;

        DateTime? nullDeadline = null;
        var result = await GetDatasetForSelectedOrgIdAsync(userId, tenantDbContext, userDetails, budgetYear, orgId, guid2, nullDeadline, lowerorgsubunitsList.ToList(), orgIdlevelNo);

        result = (from p in result
            select new clsTaskData
            {
                pkId = p.pkId,
                taskOrgId = p.taskOrgId,
                taskOrgName = p.taskOrgName,
                catColor = p.catColor,
                assignedOrgId = p.assignedOrgId,
                assignedOrgLevel = p.assignedOrgLevel,
                assignmentId = p.assignmentId,
                assignment = p.assignment,
                measures = p.measures,
                status = p.status,
                deadline = p.deadline,
                description = p.description,
                tags = p.tags,
                filter = p.filter,
                userAssignedTo = p.userAssignedTo,
                createdDate = p.createdDate,
                filterName = string.IsNullOrEmpty(p.filter) ? string.Empty : _pUtility.GetCategoryDetails(p.filter, categories, p.climateCategoryId),
                uniqueAssignmentId = p.uniqueAssignmentId,
                assignmentOwner = p.assignmentOwner,
                descUniqueId = p.descUniqueId
            }).ToList();

        var taskSet = result.OrderBy(x => x.filterName).ThenBy(y => y.assignment).ToList();

        var categoryGuid = Guid.Parse(categoryId);
        if (categoryGuid != guid2)
        {
            taskSet = taskSet.Where(y => !string.IsNullOrEmpty(y.filter)).Where(x => x.filter.Split(',').ToList().Contains(categoryGuid.ToString())).ToList();
        }

        var groupByAssignmentIds = (from p in taskSet
            group p by new { p.uniqueAssignmentId, p.filter, p.filterName } into temp
            select new
            {
                uniqueAssignmentId = temp.Key.uniqueAssignmentId,
                filter = temp.Key.filter,
                filterName = temp.Key.filterName,
                count = temp.Count()
            }).OrderBy(x => x.filterName).ToList();

        var finalSet = new List<clsTaskData>();
        for (int i = 0; i < groupByAssignmentIds.Count; i++)
        {
            var assignmentsForUniqueAssignmentId = taskSet.Where(x => x.uniqueAssignmentId == groupByAssignmentIds[i].uniqueAssignmentId).Select(y => y.assignmentId);

            var individualDataListperAssignmentID = taskSet.Where(x => assignmentsForUniqueAssignmentId.Contains(x.assignmentId)).OrderBy(x => x.filterName).ThenBy(y => y.assignment).ToList();
            string taskOrgId = string.Empty;
            for (int x = 0; x < individualDataListperAssignmentID.Count; x++)
            {
                if (x == groupByAssignmentIds[i].count - 1)
                {
                    finalSet.Add(new clsTaskData
                    {
                        pkId = individualDataListperAssignmentID[x].pkId,
                        catColor = individualDataListperAssignmentID[x].catColor,
                        assignedOrgId = individualDataListperAssignmentID[x].assignedOrgId,
                        assignedOrgLevel = x > 0 ? 0 : individualDataListperAssignmentID[x].assignedOrgLevel,
                        assignmentId = individualDataListperAssignmentID[x].assignmentId,
                        assignment = x > 0 ? string.Empty : await GetAssignmentNameAsync(individualDataListperAssignmentID[x].assignment, individualDataListperAssignmentID[x].tags, userId),
                        measures = individualDataListperAssignmentID[x].measures,
                        status = individualDataListperAssignmentID[x].status,
                        deadline = individualDataListperAssignmentID[x].deadline,
                        description = individualDataListperAssignmentID[x].description,
                        userAssignedTo = individualDataListperAssignmentID[x].userAssignedTo,
                        tags = individualDataListperAssignmentID[x].tags,
                        filter = x > 0 ? guid2.ToString() : individualDataListperAssignmentID[x].filter,
                        isLastRow = individualDataListperAssignmentID[x].assignmentId == guid2 ? false : true,
                        filterName = x > 0 ? string.Empty : individualDataListperAssignmentID[x].filterName,
                        taskOrgId = taskOrgId = individualDataListperAssignmentID[x].pkId == Guid.Empty ?
                            result.FirstOrDefault(y => y.assignmentId == individualDataListperAssignmentID[x].assignmentId) == null ? "" : result.FirstOrDefault(y => y.assignmentId == individualDataListperAssignmentID[x].assignmentId).taskOrgId :
                            result.FirstOrDefault(y => y.pkId == individualDataListperAssignmentID[x].pkId) == null ? "" : result.FirstOrDefault(y => y.pkId == individualDataListperAssignmentID[x].pkId).taskOrgId,
                        taskOrgName = x > 0 ? string.Empty : individualDataListperAssignmentID[x].assignmentOwner.ToString(),
                        descUniqueId = individualDataListperAssignmentID[x].descUniqueId
                    });
                }
                else
                {
                    finalSet.Add(new clsTaskData
                    {
                        pkId = individualDataListperAssignmentID[x].pkId,
                        catColor = individualDataListperAssignmentID[x].catColor,
                        assignedOrgId = individualDataListperAssignmentID[x].assignedOrgId,
                        assignedOrgLevel = x > 0 ? 0 : individualDataListperAssignmentID[x].assignedOrgLevel,
                        assignmentId = individualDataListperAssignmentID[x].assignmentId,
                        assignment = x > 0 ? string.Empty : await GetAssignmentNameAsync(individualDataListperAssignmentID[x].assignment, individualDataListperAssignmentID[x].tags, userId),
                        measures = individualDataListperAssignmentID[x].measures,
                        status = individualDataListperAssignmentID[x].status,
                        deadline = individualDataListperAssignmentID[x].deadline,
                        description = individualDataListperAssignmentID[x].description,
                        userAssignedTo = individualDataListperAssignmentID[x].userAssignedTo,
                        tags = individualDataListperAssignmentID[x].tags,
                        filter = x > 0 ? guid2.ToString() : individualDataListperAssignmentID[x].filter,
                        isLastRow = individualDataListperAssignmentID[x].assignmentId == guid2 ? false : x < groupByAssignmentIds[i].count ? false : true,
                        filterName = x > 0 ? string.Empty : individualDataListperAssignmentID[x].filterName,
                        taskOrgId = taskOrgId = individualDataListperAssignmentID[x].pkId == Guid.Empty ?
                            result.FirstOrDefault(y => y.assignmentId == individualDataListperAssignmentID[x].assignmentId) == null ? "" : result.FirstOrDefault(y => y.assignmentId == individualDataListperAssignmentID[x].assignmentId).taskOrgId :
                            result.FirstOrDefault(y => y.pkId == individualDataListperAssignmentID[x].pkId) == null ? "" : result.FirstOrDefault(y => y.pkId == individualDataListperAssignmentID[x].pkId).taskOrgId,
                        taskOrgName = x > 0 ? string.Empty : individualDataListperAssignmentID[x].assignmentOwner.ToString(),
                        descUniqueId = individualDataListperAssignmentID[x].descUniqueId
                    });
                }
            }
        }

        //finalSet = finalSet.OrderBy(l => l.taskOrgId).ToList();

        var finalData = new JArray();
        for (int i = 0; i < finalSet.Count; i++)
        {
            dynamic row = new JObject();
            row.pkId = finalSet[i].pkId;
            row.categoryId = finalSet[i].catColor;
            string categoryOrgLevelAssignedto = string.Empty;
            switch (int.Parse(finalSet[i].assignedOrgLevel.ToString()))
            {
                case 1:
                    categoryOrgLevelAssignedto = hierarchyList.FirstOrDefault(x => x.orgID_1 == finalSet[i].assignedOrgId.ToString()).orgName_1;
                    break;

                case 2:
                    categoryOrgLevelAssignedto = hierarchyList.FirstOrDefault(x => x.orgID_2 == finalSet[i].assignedOrgId.ToString()).orgName_2;
                    break;

                case 3:
                    categoryOrgLevelAssignedto = hierarchyList.FirstOrDefault(x => x.orgID_3 == finalSet[i].assignedOrgId.ToString()).orgName_3;
                    break;

                case 4:
                    categoryOrgLevelAssignedto = hierarchyList.FirstOrDefault(x => x.orgID_4 == finalSet[i].assignedOrgId.ToString()).orgName_4;
                    break;

                case 5:
                    categoryOrgLevelAssignedto = hierarchyList.FirstOrDefault(x => x.orgID_5 == finalSet[i].assignedOrgId.ToString()).orgName_5;
                    break;

                default:
                    categoryOrgLevelAssignedto = string.Empty;
                    break;
            }

            var userResponsibleFor = GetUserDetails(activeUsers, finalSet[i].userAssignedTo.ToString());
            var assignmentOwner = GetUserDetails(activeUsers, finalSet[i].taskOrgName.ToString());

            row.category = categoryOrgLevelAssignedto;

            row.categoryname = string.IsNullOrEmpty(finalSet[i].filterName) ? string.Empty : finalSet[i].filterName;
            row.assignment = finalSet[i].assignment;
            row.measures = finalSet[i].measures;
            row.status = finalSet[i].status.ToString() == "0" ? string.Empty : statuses.FirstOrDefault(x => x.status_id == int.Parse(finalSet[i].status.ToString())).status_description;
            row.deadline = string.IsNullOrEmpty(finalSet[i].deadline.ToString()) ? string.Empty : finalSet[i].deadline.ToString().Substring(0, 10);
            row.responsibility = userResponsibleFor;
            row.description = finalSet[i].description;
            row.isLastRow = finalSet[i].isLastRow;
            row.assignmentId = finalSet[i].assignmentId;
            row.taskOrgName = assignmentOwner;
            row.descUniqueId = finalSet[i].descUniqueId;
            var tagsSet = finalSet[i].tags.ToString();
            row.deadlineCrossedToday = string.IsNullOrEmpty(finalSet[i].deadline.ToString()) ? false : finalSet[i].deadline.Value > DateTime.UtcNow ? true : false;
            if (!string.IsNullOrEmpty(tagsSet))
            {
                if (tagsSet.Split(',').Count() > 0)
                {
                    JArray objTag = new JArray();
                    foreach (var tag in tagsSet.Split(','))
                    {
                        if (tagsList.FirstOrDefault(x => x.key == int.Parse(tag)) != null)
                        {
                            objTag.Add(tagsList.FirstOrDefault(x => x.key == int.Parse(tag)).value);
                        }
                    }
                    row.tags = objTag;
                }
            }
            else
            {
                row.tags = new JArray();
            }
            finalData.Add(row);
        }

        string jsonstring = JsonConvert.SerializeObject(finalData);
        List<BusinessplanAssignmentsGrid> gridData = JsonConvert.DeserializeObject<List<BusinessplanAssignmentsGrid>>(jsonstring);

        if (searchParams != null && !string.IsNullOrEmpty(searchParams.category))
        {
            gridData = gridData.Where(x => x.categoryname.ToLower().Contains(searchParams.category.Trim().ToLower())).ToList();
        }
        if (searchParams != null && !string.IsNullOrEmpty(searchParams.assignment))
        {
            gridData = gridData.Where(x => x.assignment.ToLower().Contains(searchParams.assignment.Trim().ToLower())).ToList();
        }
        if (searchParams != null && !string.IsNullOrEmpty(searchParams.measures))
        {
            gridData = gridData.Where(x => x.measures.ToLower().Contains(searchParams.measures.Trim().ToLower())).ToList();
        }
        if (searchParams != null && !string.IsNullOrEmpty(searchParams.status))
        {
            gridData = gridData.Where(x => x.status.ToLower().Contains(searchParams.status.Trim().ToLower())).ToList();
        }
        if (searchParams != null && !string.IsNullOrEmpty(searchParams.deadline))
        {
            gridData = gridData.Where(x => x.deadline == searchParams.deadline.Trim()).ToList();
        }
        if (searchParams != null && !string.IsNullOrEmpty(searchParams.taskOrgName))
        {
            gridData = gridData.Where(x => x.taskOrgName.ToLower().Contains(searchParams.taskOrgName.Trim().ToLower())).ToList();
        }
        if (searchParams != null && !string.IsNullOrEmpty(searchParams.responsibility))
        {
            gridData = gridData.Where(x => x.responsibility.ToLower().Contains(searchParams.responsibility.Trim().ToLower())).ToList();
        }

        bestillingGridData.Add("data", JArray.FromObject(gridData));
        return bestillingGridData;
    }


    public List<clsTaskData> GetDatasetForSelectedOrgId(string userId, TenantDBContext tenantDbContext, UserData userDetails, int budgetYear, string orgId, Guid guid2, DateTime? nullDeadline, List<string> lowerorgsubunitsList, int orgLevel)
    {
        return GetDatasetForSelectedOrgIdAsync(userId, tenantDbContext, userDetails, budgetYear, orgId, guid2, nullDeadline, lowerorgsubunitsList, orgLevel).GetAwaiter().GetResult();
    }



    public async Task<List<clsTaskData>> GetDatasetForSelectedOrgIdAsync(string userId, TenantDBContext tenantDbContext, UserData userDetails, int budgetYear, string orgId, Guid guid2, DateTime? nullDeadline, List<string> lowerorgsubunitsList, int orgLevel)
    {
        Dictionary<string, clsLanguageString> langStringValuesBusinessPlan = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BusinessPlan");

        var orgVersionContent = await _pUtility.GetOrgVersionSpecificContentAsync(userId, _pUtility.GetForecastPeriod(budgetYear, 1));

        List<tco_org_hierarchy> lstOrgHierarchyNoFiltered = orgVersionContent.lstOrgHierarchy;

        List<string> categoryTypes = new List<string>() { "sunit_bplan", "climate_action" };
        var categories = await (from p in tenantDbContext.tco_category
            where p.fk_tenant_id == userDetails.tenant_id && categoryTypes.Contains(p.type.ToLower()) && p.status == 1
            select p).ToListAsync();

        int budgetperiod1 = Convert.ToInt32(budgetYear + "01");
        int budgetperiod2 = Convert.ToInt32(budgetYear + "12");
        var tmrPeriodSet = await tenantDbContext.tmr_period_setup.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.status != 0 && budgetperiod1 <= x.forecast_period && budgetperiod2 >= x.forecast_period).ToListAsync();
        int forecastPeriod = tmrPeriodSet.Count > 0 ? tmrPeriodSet.Max(y => y.forecast_period) : 0;

        var resultAllAssignments = await (from assignment in tenantDbContext.tbiassignments
            join task in tenantDbContext.tbitasks on new { a = assignment.tenantId, b = assignment.budgetYear, c = assignment.assignmentId }
                equals new { a = task.tenantId, b = task.budgetYear, c = task.assignmentId.Value } into g1
            from g2 in g1.DefaultIfEmpty()
            join stat in tenantDbContext.tbiassignmentmonthlystatus.Where(z => z.forecastPeriod == forecastPeriod)
                on new { a = assignment.tenantId, b = assignment.assignmentId }
                equals new { a = stat.tenantId, b = stat.assignmentId } into stat1
            from stat2 in stat1.DefaultIfEmpty()
            where assignment.budgetYear == budgetYear && assignment.tenantId == userDetails.tenant_id && assignment.orgId == orgId && assignment.orgLevel == orgLevel
            select new clsTaskData
            {
                taskOrgId = g2 != null ? g2.orgId : assignment.orgId != null ? assignment.orgId : "",
                taskOrgLevel = g2 != null ? g2.orgLevel : assignment.orgLevel != null ? assignment.orgLevel.Value : 0,
                taskOrgName = string.Empty,
                pkId = g2 != null ? g2.taskid : guid2,
                catColor = g2 != null ? categoryColor : string.Empty,
                assignedOrgId = assignment.parentOrgId,
                assignedOrgLevel = assignment.parentOrgLevel == null ? 0 : assignment.parentOrgLevel.Value,
                assignmentId = assignment.assignmentId,
                assignment = assignment.assignmentName,
                measures = g2 != null ? g2.taskname : string.Empty,
                status = g2 != null ? (int)g2.taskstatus : 0,
                deadline = g2 != null ? (DateTime)g2.endDate : nullDeadline,
                description = g2 != null ? g2.description : string.Empty,
                userAssignedTo = g2 != null ? (int)g2.userAssignedTo : 0,
                tags = assignment.tags,
                filter = assignment.category,
                createdDate = g2 != null ? (DateTime)g2.createdDate : nullDeadline,
                filterName = string.Empty,
                assignmentStatus = stat2 != null ? (int)stat2.status : assignment.status,
                uniqueAssignmentId = assignment.uniqueassignmentId,
                assignmentOwner = assignment.assignmentOwner,
                climateCategoryId = assignment.climate_category,
                descUniqueId = assignment.assignmentId.ToString() + assignment.orgId
            }).OrderBy(x => x.assignmentId).ToListAsync();

        resultAllAssignments = (from assignment in resultAllAssignments
            select new clsTaskData
            {
                taskOrgId = assignment.taskOrgId,
                taskOrgLevel = assignment.taskOrgLevel,
                taskOrgName = assignment.taskOrgName,
                pkId = assignment.pkId,
                catColor = assignment.catColor,
                assignedOrgId = assignment.assignedOrgId,
                assignedOrgLevel = assignment.assignedOrgLevel,
                assignmentId = assignment.assignmentId,
                assignment = assignment.assignment,
                measures = assignment.measures,
                status = assignment.status,
                deadline = assignment.deadline,
                description = assignment.description,
                userAssignedTo = assignment.userAssignedTo,
                tags = assignment.tags,
                filter = assignment.filter,
                createdDate = assignment.createdDate,
                filterName = _pUtility.GetCategoryDetails(assignment.filter, categories, assignment.climateCategoryId),
                assignmentStatus = assignment.assignmentStatus,
                uniqueAssignmentId = assignment.uniqueAssignmentId,
                assignmentOwner = assignment.assignmentOwner,
                climateCategoryId = assignment.climateCategoryId,
                descUniqueId = assignment.descUniqueId
            }).OrderBy(x => x.assignmentId).ToList();

        var tasksWithNoAssignments = await (from task in tenantDbContext.tbitasks
            where task.budgetYear == budgetYear && task.tenantId == userDetails.tenant_id && (lowerorgsubunitsList.Contains(task.orgId) || orgId == task.orgId) && task.assignmentId == Guid.Empty
            select new clsTaskData
            {
                taskOrgId = string.IsNullOrEmpty(task.orgId) ? string.Empty : task.orgId,
                taskOrgLevel = task.orgLevel,
                taskOrgName = string.Empty,
                pkId = task != null ? task.taskid : guid2,
                catColor = task != null ? categoryColor : string.Empty,
                assignedOrgId = "",
                assignedOrgLevel = 0,
                assignmentId = Guid.Empty,
                assignment = "",
                measures = task != null ? task.taskname : string.Empty,
                status = task != null ? (int)task.taskstatus : 0,
                deadline = task != null ? (DateTime)task.endDate : nullDeadline,
                description = task != null ? task.description : string.Empty,
                userAssignedTo = task != null ? (int)task.userAssignedTo : 0,
                tags = "",
                filter = string.Empty,
                createdDate = task != null ? (DateTime)task.createdDate : nullDeadline,
                filterName = string.Empty,
                climateCategoryId = Guid.Empty,
                descUniqueId = task.taskid.ToString() + task.orgId
            }).OrderBy(x => x.assignmentId).ToListAsync();

        tasksWithNoAssignments = (from assignment in tasksWithNoAssignments
            select new clsTaskData
            {
                taskOrgId = assignment.taskOrgId,
                taskOrgLevel = assignment.taskOrgLevel,
                taskOrgName = assignment.taskOrgName,
                pkId = assignment.pkId,
                catColor = assignment.catColor,
                assignedOrgId = assignment.assignedOrgId,
                assignedOrgLevel = assignment.assignedOrgLevel,
                assignmentId = assignment.assignmentId,
                assignment = assignment.assignment,
                measures = assignment.measures,
                status = assignment.status,
                deadline = assignment.deadline,
                description = assignment.description,
                userAssignedTo = assignment.userAssignedTo,
                tags = assignment.tags,
                filter = assignment.filter,
                createdDate = assignment.createdDate,
                filterName = _pUtility.GetCategoryDetails(assignment.filter, categories, assignment.climateCategoryId),
                assignmentStatus = assignment.assignmentStatus,
                climateCategoryId = assignment.climateCategoryId,
                descUniqueId = assignment.descUniqueId
            }).OrderBy(x => x.assignmentId).ToList();

        resultAllAssignments.AddRange(tasksWithNoAssignments);

        resultAllAssignments = resultAllAssignments.OrderBy(x => x.assignmentId).ToList();

        resultAllAssignments.ForEach(x =>
        {
            if (x.assignment == "")
            {
                x.assignment = langStringValuesBusinessPlan.FirstOrDefault(v => v.Key == "SUBP_Grid2_DetachedTasks").Value.LangText;
            }

            if (x.taskOrgLevel == 1)
            {
                x.taskOrgName = lstOrgHierarchyNoFiltered.FirstOrDefault(y => y.org_id_1 == x.taskOrgId) != null ? lstOrgHierarchyNoFiltered.FirstOrDefault(y => y.org_id_1 == x.taskOrgId).org_name_1 : string.Empty;
            }
            else if (x.taskOrgLevel == 2)
            {
                x.taskOrgName = lstOrgHierarchyNoFiltered.FirstOrDefault(y => y.org_id_2 == x.taskOrgId) != null ? lstOrgHierarchyNoFiltered.FirstOrDefault(y => y.org_id_2 == x.taskOrgId).org_name_2 : string.Empty;
            }
            else if (x.taskOrgLevel == 3)
            {
                x.taskOrgName = lstOrgHierarchyNoFiltered.FirstOrDefault(y => y.org_id_3 == x.taskOrgId) != null ? lstOrgHierarchyNoFiltered.FirstOrDefault(y => y.org_id_3 == x.taskOrgId).org_name_3 : string.Empty;
            }
            else if (x.taskOrgLevel == 4)
            {
                x.taskOrgName = lstOrgHierarchyNoFiltered.FirstOrDefault(y => y.org_id_4 == x.taskOrgId) != null ? lstOrgHierarchyNoFiltered.FirstOrDefault(y => y.org_id_4 == x.taskOrgId).org_name_4 : string.Empty;
            }
            else
            {
                x.taskOrgName = lstOrgHierarchyNoFiltered.FirstOrDefault(y => y.org_id_5 == x.taskOrgId) != null ? lstOrgHierarchyNoFiltered.FirstOrDefault(y => y.org_id_5 == x.taskOrgId).org_name_5 : string.Empty;
            }
        });

        return resultAllAssignments;
    }



    public async Task<List<tbitasks>> GetTaskListAsync(int tenantId, string orgId, string serviceId, int orgLevel)
    {
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDbContextForParallelReadAsync();
        return string.IsNullOrEmpty(serviceId) || serviceId == "-1" ? await tenantDbContext.tbitasks.Where(x => x.tenantId == tenantId && x.orgId == orgId && x.orgLevel == orgLevel).ToListAsync() :
            await tenantDbContext.tbitasks.Where(x => x.tenantId == tenantId && x.orgId == orgId && x.orgLevel == orgLevel && x.serviceId == serviceId).ToListAsync();

    }


    public async Task<List<tbi_task_monthly_status>> GetTaskStatusListAsync(int tenantId)
    {
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDbContextForParallelReadAsync();
        return await tenantDbContext.tbi_task_monthly_status.Where(x => x.fk_tenant_id == tenantId).ToListAsync();
    }



    public async Task<List<AssignmentTaskGridHelper>> GetAssignmentAndTaskUnitDataNextLevelAsync(string userId, int budgetYear, string orgId, int orgIdlevelNo, string serviceId, AssignmentTaskNextLevelDataHelper objHelper)
    {
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValuesBusinessPlan = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BusinessPlan");

        List<tco_org_hierarchy> lstOrgHierarchy = objHelper.orgVersionContent.lstOrgHierarchy;

        TimeSpan cacheTimeOut = new TimeSpan(0, 1, 0);
        int clientId = userDetails.client_id;
        string key = (userId + "-" + string.Empty + "-" + -1 + "-" + "tcoUserForAssignments").Trim();

        string strUserList = await _cache.GetStringForUserAsync(clientId, userDetails.tenant_id, userId, key);
        //Apply only user role filters while fetching users(Same way we fetch for popup)
        List<AssignmentUserListHelper> tenantUsers;
        if (String.IsNullOrEmpty(strUserList))
        {
            tenantUsers = await (from t in tenantDbContext.tco_users
                where (t.IsActive.HasValue && t.IsActive.Value)
                select new AssignmentUserListHelper
                {
                    userId = t.pk_id,
                    fName = t.first_name,
                    lName = t.last_name
                }).OrderBy(a => a.fName).ThenBy(b => b.lName).ToListAsync();

            string szUserData = JsonConvert.SerializeObject(tenantUsers);
            await _cache.SetStringForUserAsync(clientId, userDetails.tenant_id, userId, key, szUserData, cacheTimeOut);
        }
        else
        {
            tenantUsers = JsonConvert.DeserializeObject<List<AssignmentUserListHelper>>(strUserList);
        }

        List<string> OrgListForTask = new List<string>();
        string currentOrgName = string.Empty;
        switch (orgIdlevelNo)
        {
            case 1:
                lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_1 == orgId).ToList();
                OrgListForTask = lstOrgHierarchy.Where(x => x.org_id_1 == orgId).Select(x => x.org_id_2).ToList();
                currentOrgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_1 == orgId).org_name_1;
                break;

            case 2:
                lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_2 == orgId).ToList();
                OrgListForTask = lstOrgHierarchy.Where(x => x.org_id_2 == orgId).Select(x => x.org_id_3).ToList();
                currentOrgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_2 == orgId).org_name_2;
                break;

            case 3:
                lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_3 == orgId).ToList();
                OrgListForTask = lstOrgHierarchy.Where(x => x.org_id_3 == orgId).Select(x => x.org_id_4).ToList();
                currentOrgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_3 == orgId).org_name_3;
                break;

            case 4:
                lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_4 == orgId).ToList();
                OrgListForTask = lstOrgHierarchy.Where(x => x.org_id_4 == orgId).Select(x => x.org_id_5).ToList();
                currentOrgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_4 == orgId).org_name_4;
                break;

            case 5:
                lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_5 == orgId).ToList();
                OrgListForTask = lstOrgHierarchy.Where(x => x.org_id_5 == orgId).Select(x => x.org_id_5).ToList();
                currentOrgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_5 == orgId).org_name_5;
                break;

            default:
                break;
        }

        var hierarchyList = (from p in lstOrgHierarchy
            select new
            {
                tenantID = p.fk_tenant_id,
                orgID_1 = p.org_id_1,
                orgName_1 = p.org_name_1,
                orgLevel_1 = 1,
                orgID_2 = p.org_id_2,
                orgName_2 = p.org_name_2,
                orgLevel_2 = 2,
                orgID_3 = p.org_id_3,
                orgName_3 = p.org_name_3,
                orgLevel_3 = 3,
                orgID_4 = p.org_id_4,
                orgName_4 = p.org_name_4,
                orgLevel_4 = 4,
                orgID_5 = p.org_id_5,
                orgName_5 = p.org_name_5,
                orgLevel_5 = 5
            }).Distinct().ToList();

        string categoryOrgLevelAssignedto = string.Empty;
        switch (orgIdlevelNo)
        {
            case 1:
                categoryOrgLevelAssignedto = hierarchyList.FirstOrDefault(x => x.orgID_1 == orgId).orgName_1;
                break;

            case 2:
                categoryOrgLevelAssignedto = hierarchyList.FirstOrDefault(x => x.orgID_2 == orgId).orgName_2;
                break;

            case 3:
                categoryOrgLevelAssignedto = hierarchyList.FirstOrDefault(x => x.orgID_3 == orgId).orgName_3;
                break;

            case 4:
                categoryOrgLevelAssignedto = hierarchyList.FirstOrDefault(x => x.orgID_4 == orgId).orgName_4;
                break;

            case 5:
                categoryOrgLevelAssignedto = hierarchyList.FirstOrDefault(x => x.orgID_5 == orgId).orgName_5;
                break;

            default:
                break;
        }

        // assignment data
        var resultSet = string.IsNullOrEmpty(serviceId) || serviceId == "-1" ?
            await (from e in tenantDbContext.tbiassignments
                where OrgListForTask.Contains(e.parentOrgId) && OrgListForTask.Contains(e.orgId) && e.budgetYear == budgetYear && e.tenantId == userDetails.tenant_id && !string.IsNullOrEmpty(e.orgId)
                join d in tenantDbContext.tbiassignmentmonthlystatus
                    on e.assignmentId equals d.assignmentId into eGroup
                from d in eGroup.DefaultIfEmpty()
                select new
                {
                    pkId = e.assignmentId,
                    uniqueassignmentId = e.uniqueassignmentId,
                    categoryColor = categoryColor,
                    assignmentName = e.assignmentName,
                    deadline = e.endDate,
                    tags = e.tags,
                    description = e.description,
                    categoryId = e.category,
                    categoryName = string.Empty,
                    orgCreatedat = e.isDistributedtoLowerLevel.HasValue && !string.IsNullOrEmpty(e.distributedParentOrgID) ? e.distributedParentOrgID : e.parentOrgId,
                    orgCreatedLevel = e.isDistributedtoLowerLevel.HasValue && e.distributedParentOrgLevel.HasValue && e.distributedParentOrgLevel.Value != 0 ? e.distributedParentOrgLevel : e.parentOrgLevel,
                    isDelete = true,
                    status = e.status,
                    userAssignedTo = e.userAssignedTo,
                    isReadOnlyAss = e.is_readonly_ass,
                    assignmentOwner = e.assignmentOwner,
                    serviceId = e.serviceId,
                    orgId = e.orgId,
                    orgLevel = e.orgLevel,
                    climateCategory = e.climate_category
                }).ToListAsync()
            :
            await (from e in tenantDbContext.tbiassignments
                where OrgListForTask.Contains(e.parentOrgId) && OrgListForTask.Contains(e.orgId) && e.budgetYear == budgetYear && e.tenantId == userDetails.tenant_id && !string.IsNullOrEmpty(e.orgId) && e.serviceId == serviceId
                join d in tenantDbContext.tbiassignmentmonthlystatus
                    on e.assignmentId equals d.assignmentId into eGroup
                from d in eGroup.DefaultIfEmpty()
                select new
                {
                    pkId = e.assignmentId,
                    uniqueassignmentId = e.uniqueassignmentId,
                    categoryColor = categoryColor,
                    assignmentName = e.assignmentName,
                    deadline = e.endDate,
                    tags = e.tags,
                    description = e.description,
                    categoryId = e.category,
                    categoryName = string.Empty,
                    orgCreatedat = e.isDistributedtoLowerLevel.HasValue && !string.IsNullOrEmpty(e.distributedParentOrgID) ? e.distributedParentOrgID : e.parentOrgId,
                    orgCreatedLevel = e.isDistributedtoLowerLevel.HasValue && e.distributedParentOrgLevel.HasValue && e.distributedParentOrgLevel.Value != 0 ? e.distributedParentOrgLevel : e.parentOrgLevel,
                    isDelete = true,
                    status = e.status,
                    userAssignedTo = e.userAssignedTo,
                    isReadOnlyAss = e.is_readonly_ass,
                    assignmentOwner = e.assignmentOwner,
                    serviceId = e.serviceId,
                    orgId = e.orgId,
                    orgLevel = e.orgLevel,
                    climateCategory = e.climate_category
                }).ToListAsync();

        resultSet = (from e in resultSet.Distinct().ToList()
            select new
            {
                pkId = e.pkId,
                uniqueassignmentId = e.uniqueassignmentId,
                categoryColor = e.categoryColor,
                assignmentName = e.assignmentName,
                deadline = e.deadline,
                tags = e.tags,
                description = e.description,
                categoryId = e.categoryId,
                categoryName = e.categoryName,
                orgCreatedat = e.orgCreatedat,
                orgCreatedLevel = e.orgCreatedLevel,
                isDelete = e.orgCreatedat == orgId && e.orgCreatedLevel == orgIdlevelNo,
                status = e.status,
                userAssignedTo = e.userAssignedTo,
                isReadOnlyAss = e.isReadOnlyAss,
                assignmentOwner = e.assignmentOwner,
                serviceId = e.serviceId,
                orgId = e.orgId,
                orgLevel = e.orgLevel,
                climateCategory = e.climateCategory
            }).ToList();

        //task data
        var Taskresult = string.IsNullOrEmpty(serviceId) || serviceId == "-1" ?
            await (from assignment in tenantDbContext.tbiassignments
                join task in tenantDbContext.tbitasks
                    on new { ID = assignment.assignmentId, Orgid = assignment.orgId }
                    equals new { ID = task.assignmentId.Value, Orgid = task.orgId } 
                where OrgListForTask.Contains(assignment.parentOrgId) && OrgListForTask.Contains(assignment.orgId) && assignment.budgetYear == budgetYear && assignment.tenantId == userDetails.tenant_id
                      && task.budgetYear == budgetYear
                select new clsTaskData
                {
                    pkId = task.taskid,
                    catColor = categoryColor,
                    assignedOrgId = assignment.parentOrgId,
                    assignedOrgLevel = (int)assignment.parentOrgLevel,
                    assignmentId = assignment.assignmentId,
                    assignment = assignment.assignmentName,
                    assignmentDescription = assignment.description,
                    measures = task.taskname,
                    status = (int)task.taskstatus,
                    deadline = task.endDate,
                    description = task.description,
                    tags = assignment.tags,
                    filter = assignment.category,
                    userAssignedTo = (int)task.userAssignedTo,
                    uniqueTaskId = task.uniquetaskId,
                    orgCreatedat = task.isDistributedtoLowerLevel.HasValue && !string.IsNullOrEmpty(task.distributedParentOrgID) ? task.distributedParentOrgID : task.orgCreatedAt,
                    orgCreatedLevel = task.isDistributedtoLowerLevel.HasValue && task.distributedParentOrgLevel.HasValue && task.distributedParentOrgLevel.Value != 0 ? task.distributedParentOrgLevel.Value : task.orglevelCreatedAt.Value,
                    serviceId = task.serviceId,
                    orgId = task.orgId,
                    orgLevel = task.orgLevel,
                    climateCategoryId = assignment.climate_category
                }).OrderBy(x => x.assignmentId).ToListAsync()
            :
            await (from assignment in tenantDbContext.tbiassignments
                join task in tenantDbContext.tbitasks
                    on new { ID = assignment.assignmentId, Orgid = assignment.orgId }
                    equals new { ID = task.assignmentId.Value, Orgid = task.orgId } 
                where OrgListForTask.Contains(assignment.parentOrgId) && OrgListForTask.Contains(assignment.orgId) && assignment.budgetYear == budgetYear && assignment.tenantId == userDetails.tenant_id && assignment.serviceId == serviceId
                      && task.budgetYear == budgetYear
                select new clsTaskData
                {
                    pkId = task.taskid,
                    catColor = categoryColor,
                    assignedOrgId = assignment.parentOrgId,
                    assignedOrgLevel = (int)assignment.parentOrgLevel,
                    assignmentId = assignment.assignmentId,
                    assignment = assignment.assignmentName,
                    assignmentDescription = assignment.description,
                    measures = task.taskname,
                    status = (int)task.taskstatus,
                    deadline = task.endDate,
                    description = task.description,
                    tags = assignment.tags,
                    filter = assignment.category,
                    userAssignedTo = (int)task.userAssignedTo,
                    createdDate = task.createdDate,
                    uniqueTaskId = task.uniquetaskId,
                    orgCreatedat = task.isDistributedtoLowerLevel.HasValue && !string.IsNullOrEmpty(task.distributedParentOrgID) ? task.distributedParentOrgID : task.orgCreatedAt,
                    orgCreatedLevel = task.isDistributedtoLowerLevel.HasValue && task.distributedParentOrgLevel.HasValue && task.distributedParentOrgLevel.Value != 0 ? task.distributedParentOrgLevel.Value : task.orglevelCreatedAt.Value,
                    serviceId = task.serviceId,
                    orgId = task.orgId,
                    orgLevel = task.orgLevel,
                    climateCategoryId = assignment.climate_category
                }).OrderBy(x => x.assignmentId).ToListAsync();

        Taskresult = (from p in Taskresult.Distinct().ToList()
            select new clsTaskData
            {
                pkId = p.pkId,
                catColor = p.catColor,
                assignedOrgId = p.assignedOrgId,
                assignedOrgLevel = p.assignedOrgLevel,
                assignmentId = p.assignmentId,
                assignment = p.assignment,
                assignmentDescription = p.assignmentDescription,
                measures = p.measures,
                status = p.status,
                deadline = p.deadline,
                description = p.description,
                tags = p.tags,
                filter = p.filter,
                userAssignedTo = p.userAssignedTo,
                createdDate = p.createdDate,
                filterName = _pUtility.GetCategoryDetails(p.filter, objHelper.categories, p.climateCategoryId), //categories.FirstOrDefault(x => x.pk_cat_id == p.filter).description,
                uniqueTaskId = p.uniqueTaskId,
                orgCreatedat = p.orgCreatedat,
                orgCreatedLevel = p.orgCreatedLevel,
                serviceId = p.serviceId,
                orgId = p.orgId,
                orgLevel = p.orgLevel
            }).ToList();

        List<AssignmentTaskGridHelper> DataSet = new List<AssignmentTaskGridHelper>();
        AssignmentTaskGridHelper tempData;
        int parrentId = 10000;
        int childId = 20000;

        // mr task descript
        string principal = string.Empty;
        string unitName = string.Empty;

        foreach (var item in resultSet)// loop through for each assignment
        {
            var ids = item.tags.Split(',').Select(i => i.ToString()).ToList();
            var catIds = item.categoryId.Split(',').Select(i => i.ToString()).ToList();

            switch (item.orgCreatedLevel.Value)
            {
                case 1:
                    principal = hierarchyList.FirstOrDefault(x => x.orgID_1 == item.orgCreatedat).orgName_1;
                    break;

                case 2:
                    principal = hierarchyList.FirstOrDefault(x => x.orgID_2 == item.orgCreatedat).orgName_2;
                    break;

                case 3:
                    principal = hierarchyList.FirstOrDefault(x => x.orgID_3 == item.orgCreatedat).orgName_3;
                    break;

                case 4:
                    principal = hierarchyList.FirstOrDefault(x => x.orgID_4 == item.orgCreatedat).orgName_4;
                    break;

                case 5:
                    principal = hierarchyList.FirstOrDefault(x => x.orgID_5 == item.orgCreatedat).orgName_5;
                    break;

                default:
                    break;
            }
            switch (item.orgLevel.Value)
            {
                case 1:
                    unitName = item.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_1 == item.orgId).orgName_1;
                    break;

                case 2:
                    unitName = item.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_2 == item.orgId).orgName_2;
                    break;

                case 3:
                    unitName = item.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_3 == item.orgId).orgName_3;
                    break;

                case 4:
                    unitName = item.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_4 == item.orgId).orgName_4;
                    break;

                case 5:
                    unitName = item.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_5 == item.orgId).orgName_5;
                    break;

                default:
                    break;
            }
            int? monthlyReportStatus = objHelper.mRtbiassignmentmonthlystatus.FirstOrDefault(x => x.assignmentId == item.pkId) != null ? objHelper.mRtbiassignmentmonthlystatus.FirstOrDefault(x => x.assignmentId == item.pkId).status : -1;
            string statusFromMonthRep = monthlyReportStatus != null && monthlyReportStatus != -1 && objHelper.statusDescriptions.FirstOrDefault(s => s.status_id == monthlyReportStatus) != null ?
                objHelper.statusDescriptions.FirstOrDefault(s => s.status_id == monthlyReportStatus).status_description : string.Empty;

            string statusFromAssign = objHelper.statusDescriptions.FirstOrDefault(s => s.status_id == item.status) == null ? string.Empty :
                objHelper.statusDescriptions.FirstOrDefault(s => s.status_id == item.status).status_description;
            tempData = new AssignmentTaskGridHelper()// assgignment
            {
                pkId = item.uniqueassignmentId,

                category = objHelper.categoriesDescriptions.Where(x => catIds.Contains(x.pk_cat_id.ToString())).Select(z => z.description).ToList(),
                tags = objHelper.tags.Where(x => ids.Contains(x.key)).Any() ? objHelper.tags.Where(x => ids.Contains(x.key)).Select(z => z.value).ToList() : new List<string>(),
                principal = principal,
                owner = tenantUsers.FirstOrDefault(x => x.userId == item.assignmentOwner) != null ? tenantUsers.FirstOrDefault(x => x.userId == item.assignmentOwner).fName + " " + tenantUsers.FirstOrDefault(x => x.userId == item.assignmentOwner).lName : string.Empty,
                assignmentAndTask = item.assignmentName,
                deadLine = DateTime.Compare(item.deadline, DateTime.UtcNow) > 0 ? item.deadline.ToShortDateString() : item.deadline.ToShortDateString() + "!",
                deadLineExp = DateTime.Compare(item.deadline, DateTime.UtcNow) < 0,
                device = "",
                id = parrentId.ToString(),
                isBold = false,
                parentId = null,
                responsible = tenantUsers.FirstOrDefault(x => x.userId == item.userAssignedTo) != null ? tenantUsers.FirstOrDefault(x => x.userId == item.userAssignedTo).fName + " " + tenantUsers.FirstOrDefault(x => x.userId == item.userAssignedTo).lName : string.Empty,
                status = !string.IsNullOrEmpty(statusFromMonthRep) ? statusFromMonthRep : statusFromAssign,
                assignTasksDesc = item.description,
                statusDesc = objHelper.mRtbiassignmentmonthlystatus.FirstOrDefault(x => x.assignmentId == item.pkId) != null ? objHelper.mRtbiassignmentmonthlystatus.FirstOrDefault(x => x.assignmentId == item.pkId).description : string.Empty,
                isDelete = item.orgCreatedat == orgId,
                unitName = unitName,
                isExpand = objHelper.filters.expandAssignemtIds != null && objHelper.filters.expandAssignemtIds.Count > 0 && objHelper.filters.expandAssignemtIds.Contains(item.uniqueassignmentId.ToString()),
                serviceId = !string.IsNullOrEmpty(item.serviceId) && item.serviceId != "-1" && objHelper.filters.serviceIdList.FirstOrDefault(x => x.key == item.serviceId) != null ? objHelper.filters.serviceIdList.FirstOrDefault(x => x.key == item.serviceId).value : string.Empty,
            };
            DataSet.Add(tempData);
            if (Taskresult.Any(x => x.assignmentId == item.pkId))// loop through for each task fora given assignmnet
            {
                tempData = new AssignmentTaskGridHelper()// empty row for dispaying headr for task
                {
                    pkId = Guid.Empty,
                    category = new List<string>(),
                    tags = new List<string>(),
                    principal = string.Empty,
                    owner = string.Empty,
                    assignmentAndTask = langStringValuesBusinessPlan["BU_AssignmentTask_gird_sub_title"].LangText + " :",
                    deadLine = langStringValuesBusinessPlan["BU_AssignmentTask_gird_DeadLineDate"].LangText + " :",
                    device = "",
                    id = childId.ToString(),
                    isBold = true,
                    parentId = parrentId.ToString(),
                    responsible = langStringValuesBusinessPlan["BU_AssignmentTask_gird_Resposnsible"].LangText + " :",
                    status = langStringValuesBusinessPlan["BU_AssignmentTask_gird_Status"].LangText + " :",
                    unitName = string.Empty,
                    serviceId = string.Empty,
                };
                DataSet.Add(tempData);
                childId++;
                foreach (var tskItem in Taskresult.Where(x => x.assignmentId == item.pkId).ToList())// loop through for each task fora given assignmnet
                {
                    switch (tskItem.orgLevel)
                    {
                        case 1:
                            principal = tskItem.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_1 == tskItem.orgId).orgName_1;
                            break;

                        case 2:
                            principal = tskItem.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_2 == tskItem.orgId).orgName_2;
                            break;

                        case 3:
                            principal = tskItem.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_3 == tskItem.orgId).orgName_3;
                            break;

                        case 4:
                            principal = tskItem.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_4 == tskItem.orgId).orgName_4;
                            break;

                        case 5:
                            principal = tskItem.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_5 == tskItem.orgId).orgName_5;
                            break;

                        default:
                            break;
                    }
                    int? MonthlytaskStatus = objHelper.tbitask_monthlystatusList.FirstOrDefault(x => x.fk_task_id == tskItem.pkId) != null ? objHelper.tbitask_monthlystatusList.FirstOrDefault(x => x.fk_task_id == tskItem.pkId).status : -1;
                    string taskStatusFromMonthRep = MonthlytaskStatus != null && MonthlytaskStatus != -1 && objHelper.statusDescriptions.FirstOrDefault(s => s.status_id == MonthlytaskStatus) != null ?
                        objHelper.statusDescriptions.FirstOrDefault(s => s.status_id == MonthlytaskStatus).status_description : string.Empty;

                    string taskStatusFromAssign = objHelper.statusDescriptions.FirstOrDefault(s => s.status_id == tskItem.status) == null ? string.Empty :
                        objHelper.statusDescriptions.FirstOrDefault(s => s.status_id == tskItem.status).status_description;
                    tempData = new AssignmentTaskGridHelper()
                    {
                        pkId = tskItem.uniqueTaskId,

                        category = new List<string>(),
                        tags = new List<string>(),
                        principal = string.Empty,
                        owner = string.Empty,
                        assignmentAndTask = tskItem.measures,
                        deadLine = DateTime.Compare(tskItem.deadline.Value, DateTime.UtcNow) > 0 ? tskItem.deadline.Value.ToShortDateString() : tskItem.deadline.Value.ToShortDateString() + "!",
                        deadLineExp = DateTime.Compare(tskItem.deadline.Value, DateTime.UtcNow) < 0,
                        device = "",
                        id = childId.ToString(),
                        isBold = false,
                        parentId = parrentId.ToString(),
                        responsible = tenantUsers.FirstOrDefault(x => x.userId == tskItem.userAssignedTo) != null ? tenantUsers.FirstOrDefault(x => x.userId == tskItem.userAssignedTo).fName + " " + tenantUsers.FirstOrDefault(x => x.userId == tskItem.userAssignedTo).lName : string.Empty,
                        assignTasksDesc = tskItem.description,
                        statusDesc = objHelper.tbitask_monthlystatusList.FirstOrDefault(x => x.fk_task_id == tskItem.pkId) != null ? objHelper.tbitask_monthlystatusList.FirstOrDefault(x => x.fk_task_id == tskItem.pkId).description : string.Empty,
                        isDelete = tskItem.orgCreatedat == orgId,
                        unitName = principal,
                        status = !string.IsNullOrEmpty(taskStatusFromMonthRep) ? taskStatusFromMonthRep : taskStatusFromAssign,
                        serviceId = !string.IsNullOrEmpty(tskItem.serviceId) && tskItem.serviceId != "-1" && objHelper.filters.serviceIdList.FirstOrDefault(x => x.key == tskItem.serviceId) != null ? objHelper.filters.serviceIdList.FirstOrDefault(x => x.key == tskItem.serviceId).value : string.Empty,
                    };
                    DataSet.Add(tempData);
                    childId++;
                }
            }

            parrentId++;
        }

        return DataSet;
    }



    public async Task<bool> DeleteSpecificTaskorAssignmentAsync(string userId, string id, string type, string orgId, int orgLevel, string serviceId, string uiTaskId, int budgetYear)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        Guid Id = Guid.Parse(id);
        if (type.ToLower() == "assignment".ToLower())
        {
            var assignmentData = await tenantDbContext.tbiassignments.Where(x => x.tenantId == userDetails.tenant_id && x.uniqueassignmentId == Id).ToListAsync();
            if (assignmentData.Count > 0)
            {
                // first delete linked task and then delete assignment
                var linkedassignmentids = assignmentData.Select(y => y.assignmentId).Distinct().ToList();

                var taskData = await tenantDbContext.tbitasks.Where(x => linkedassignmentids.Contains(x.assignmentId.Value) && x.tenantId == userDetails.tenant_id).ToListAsync();
                if (taskData.Count > 0)
                {
                    tenantDbContext.tbitasks.RemoveRange(taskData);
                }

                var goalsData = await tenantDbContext.TbiAssignmentGoal.Where(x => linkedassignmentids.Contains(x.fk_assignment_id) && x.fk_tenant_id == userDetails.tenant_id).ToListAsync();
                if (goalsData.Count > 0)
                {
                    tenantDbContext.TbiAssignmentGoal.RemoveRange(goalsData);
                }

                var strategyData = await tenantDbContext.TbiAssignmentStrategy.Where(x => linkedassignmentids.Contains(x.fk_assignment_id) && x.fk_tenant_id == userDetails.tenant_id).ToListAsync();
                if (strategyData.Count > 0)
                {
                    tenantDbContext.TbiAssignmentStrategy.RemoveRange(strategyData);
                }

                var TargetData = await tenantDbContext.TbiAssignmentTarget.Where(x => linkedassignmentids.Contains(x.fk_assignment_id) && x.fk_tenant_id == userDetails.tenant_id).ToListAsync();
                if (TargetData.Count > 0)
                {
                    tenantDbContext.TbiAssignmentTarget.RemoveRange(TargetData);
                }
                tenantDbContext.tco_assignments_descriptions.RemoveRange(tenantDbContext.tco_assignments_descriptions.Where(x => x.fk_assignment_id.HasValue && linkedassignmentids.Contains(x.fk_assignment_id.Value)).ToList());

                var planAssignmentMappingData = await tenantDbContext.tpl_tfp_assignment_mapping.Where(x => linkedassignmentids.Contains(x.fk_assignment_id) && x.fk_tenant_id == userDetails.tenant_id).ToListAsync();
                if (planAssignmentMappingData.Any())
                {
                    tenantDbContext.tpl_tfp_assignment_mapping.RemoveRange(planAssignmentMappingData);
                }

                foreach (var assignment in assignmentData)
                {
                    await _planDiscAssignments.ValidateAndSaveAssignmentForPDAsync(userId, budgetYear, assignment.orgLevel ?? -1, assignment.orgId ?? "-1", assignment.assignmentId, PDMeetingType.DELETE.ToString());
                }

                await tenantDbContext.SaveChangesAsync();

                //Update climate assignment info in climate table
                await UpdateClimateHeaderAssignmentInfoAsync(userDetails, tenantDbContext, Id);
                tenantDbContext.tbiassignments.RemoveRange(assignmentData);
                await tenantDbContext.SaveChangesAsync();
            }
        }
        else
        {
            var taskData = string.IsNullOrEmpty(serviceId) || serviceId == "-1" ?
                await tenantDbContext.tbitasks.FirstOrDefaultAsync(x => x.tenantId == userDetails.tenant_id && x.uniquetaskId == Id && x.orgId == orgId && x.orgLevel == orgLevel)
                : await tenantDbContext.tbitasks.FirstOrDefaultAsync(x => x.tenantId == userDetails.tenant_id && x.uniquetaskId == Id && x.orgId == orgId && x.orgLevel == orgLevel && x.serviceId == serviceId);
            if (taskData != null)
            {
                var lstToRemove = await tenantDbContext.tbitasks.Where(x => x.uniquetaskId == taskData.uniquetaskId && x.tenantId == taskData.tenantId).ToListAsync();
                if (lstToRemove.Any())
                {
                    tenantDbContext.tbitasks.RemoveRange(lstToRemove);
                    await tenantDbContext.SaveChangesAsync();
                }
            }
        }
        return true;
    }


    public async Task<bool> GetParentLevelAssignment(int tenantId, string Id, UrlLinkHelper inputObj)
    {
        TenantDBContext dbContext = await _pUtility.GetTenantDBContextAsync();
        var parentData = string.IsNullOrEmpty(inputObj.serviceId) || inputObj.serviceId == "-1" ? await dbContext.tbiassignments.FirstOrDefaultAsync(a => a.tenantId == tenantId
                && a.uniqueassignmentId.ToString() == Id
                && a.parentOrgId == inputObj.orgId
                && a.parentOrgLevel == inputObj.orgLevel)

            : await dbContext.tbiassignments.FirstOrDefaultAsync(a => a.tenantId == tenantId
                                                                      && a.uniqueassignmentId.ToString() == Id
                                                                      && a.parentOrgId == inputObj.orgId
                                                                      && a.parentOrgLevel == inputObj.orgLevel
                                                                      && a.serviceId == inputObj.serviceId);

        bool isParentLevel = parentData != null ? true : false;
        return isParentLevel;
    }



    public async Task<JArray> CheckAssignmentTargetGoalAddFeatureAsync(string userId, int orgLevel)
    {
        string assignmentparam = await _pUtility.GetParameterValueAsync(userId, "BP_Add_Assignment_button");
        int AssingmnentParameterValue = string.IsNullOrEmpty(assignmentparam) ? -1 : int.Parse(assignmentparam);
        string goalparam = await _pUtility.GetParameterValueAsync(userId, "BP_Add_Goal_button");
        int GoalParameterValue = string.IsNullOrEmpty(goalparam) ? -1 : int.Parse(goalparam);
        string targetparam = await _pUtility.GetParameterValueAsync(userId, "BP_Add_Target_button");
        int TargetParameterValue = string.IsNullOrEmpty(targetparam) ? -1 : int.Parse(targetparam);
        JArray finalData = new JArray();
        dynamic AssignmetflagValue = new JObject();
        dynamic GoalflagValue = new JObject();
        dynamic TargetflagValue = new JObject();

        AssignmetflagValue.key = "AssignmentFlag";
        if (AssingmnentParameterValue == -1)
            AssignmetflagValue.value = true;
        else
            AssignmetflagValue.value = AssingmnentParameterValue > orgLevel;
        finalData.Add(AssignmetflagValue);

        GoalflagValue.key = "GoalFlag";
        if (GoalParameterValue == -1)
            GoalflagValue.value = true;
        else
            GoalflagValue.value = GoalParameterValue > orgLevel;
        finalData.Add(GoalflagValue);

        TargetflagValue.key = "TargetFlag";
        if (TargetParameterValue == -1)
            TargetflagValue.value = true;
        else
            TargetflagValue.value = TargetParameterValue > orgLevel;
        finalData.Add(TargetflagValue);

        return finalData;
    }



    public async Task<JObject> GetHierarchyForBusinessPlanAsync(string userId, string orgId, int orgIdlevelNo, string assignmentId, string pageType, int budgetYear, string serviceId)
    {
        dynamic data = new JObject();

        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        var orgVersionContent = await _pUtility.GetOrgVersionSpecificContentAsync(userId, _pUtility.GetForecastPeriod(budgetYear, 1));
        JArray orgTree = new();
        List<tco_org_level> lstOrgLevels = orgVersionContent.lstOrgLevel.Count > 5 ? orgVersionContent.lstOrgLevel.Take(5).Where(x => x.org_level > orgIdlevelNo).OrderBy(y => y.org_level).ToList() :
            orgVersionContent.lstOrgLevel.Where(x => x.org_level > orgIdlevelNo).OrderBy(y => y.org_level).ToList();

        if (pageType.ToLower() == "checklist".ToLower())
        {
            var checklistParamLevel = await _pUtility.GetActiveParameterValueAsync(userId, "BUPLAN_CHECKLIST_DISPLAYDEPTS");
            if (string.IsNullOrEmpty(checklistParamLevel) || checklistParamLevel.ToLower() == "FALSE".ToLower())
            {
                var suOrgLevel = orgVersionContent.lstOrgLevel.FirstOrDefault(x => x.su_flag == 1);
                if (suOrgLevel != null)
                {
                    lstOrgLevels = lstOrgLevels.Where(x => x.org_level <= suOrgLevel.org_level).ToList();
                }
            }
        }

        dynamic lowerOrgLevelInfo = new JArray();
        foreach (var l in lstOrgLevels)
        {
            dynamic levelInfo = new JObject();
            levelInfo.key = l.org_level;
            levelInfo.value = l.level_name;
            lowerOrgLevelInfo.Add(levelInfo);
        }

        data.Add("lowerOrgLevelInfo", lowerOrgLevelInfo);

        if (!string.IsNullOrEmpty(assignmentId))
        {
            if (pageType.ToLower() == "checklist".ToLower())
            {
                var checklistId = Guid.Parse(assignmentId);
                var tbiassignmentData = await tenantDbContext.tbiChecklistItems.FirstOrDefaultAsync(x => x.ChecklistItemId == checklistId && x.TenantId == userDetails.tenant_id);
                var orgList = await tenantDbContext.tbiChecklistItemsDetail.Where(x => x.ChecklistItemId == checklistId && x.TenantID == userDetails.tenant_id)
                    .Select(x => new BusinessPlanOrgHierarchyHelper { OrgId = x.OrgID, OrgLevel = x.OrgLevel.Value }).ToListAsync();
                orgTree = await _pUtility.GetOrgStructureforBusinessPlanAsync(orgVersionContent, userId, tbiassignmentData.OrgCreatedAt, int.Parse(tbiassignmentData.OrgLevelCreatedAt.ToString()), orgList, true, true);
            }
            else
            {
                var assignmentid = Guid.Parse(assignmentId);
                var tbiassignmentData = await tenantDbContext.tbiassignments.FirstOrDefaultAsync(x => x.uniqueassignmentId == assignmentid && x.tenantId == userDetails.tenant_id);
                var parentDetails = await tenantDbContext.tbiassignments.FirstOrDefaultAsync(x => x.uniqueassignmentId == assignmentid && !x.isDistributedtoLowerLevel.Value);

                // this code will work for the selected org_id and level on which assignment is created
                var orgList = string.IsNullOrEmpty(serviceId) ? await tenantDbContext.tbiassignments.Where(x => x.uniqueassignmentId == assignmentid && x.tenantId == userDetails.tenant_id && x.parentOrgId == orgId
                        && x.parentOrgLevel == orgIdlevelNo).Select(x => new BusinessPlanOrgHierarchyHelper { OrgId = x.orgId, OrgLevel = x.orgLevel.Value }).ToListAsync() :
                    await tenantDbContext.tbiassignments.Where(x => x.uniqueassignmentId == assignmentid && x.tenantId == userDetails.tenant_id && x.parentOrgId == orgId
                                                                    && x.parentOrgLevel == orgIdlevelNo && (x.serviceId == "ALL" || x.serviceId == "-1")).Select(x => new BusinessPlanOrgHierarchyHelper { OrgId = x.orgId, OrgLevel = x.orgLevel.Value }).ToListAsync();

                //this code will will work for the selected org_id and level to which the assignment is delegated to..
                JArray orgStructureAssignmentTree = await _pUtility.GetOrgStructureforBusinessPlanAsync(orgVersionContent, userId, orgId, orgIdlevelNo, orgList, false, true, isAssignmentPage: true, budgetYear: budgetYear);
                List<string> orgIdsLowerLevel = new List<string>();
                await GetOrgIdsAsync(orgStructureAssignmentTree, orgIdsLowerLevel);


                var orgListLowerLevel = string.IsNullOrEmpty(serviceId) ? tenantDbContext.tbiassignments.Where(x => x.uniqueassignmentId == assignmentid
                        && x.tenantId == userDetails.tenant_id && orgIdsLowerLevel.Contains(x.orgId)).Select(x => new BusinessPlanOrgHierarchyHelper { OrgId = x.orgId, OrgLevel = x.orgLevel.Value }).ToList() :

                    tenantDbContext.tbiassignments.Where(x => x.uniqueassignmentId == assignmentid && x.tenantId == userDetails.tenant_id
                        && orgIdsLowerLevel.Contains(x.orgId) && x.serviceId == serviceId).Select(x => new BusinessPlanOrgHierarchyHelper { OrgId = x.orgId, OrgLevel = x.orgLevel.Value }).ToList();

                orgList.AddRange(orgListLowerLevel);

                if (orgId != tbiassignmentData.parentOrgId && orgIdlevelNo != tbiassignmentData.parentOrgLevel)
                {
                    orgTree = await _pUtility.GetOrgStructureforBusinessPlanAsync(orgVersionContent, userId, orgId, orgIdlevelNo, orgList, false, true, false, true, isAssignmentPage: true, budgetYear: budgetYear);
                }
                else if (orgId != tbiassignmentData.parentOrgId && orgIdlevelNo == tbiassignmentData.parentOrgLevel)
                {
                    orgTree = await _pUtility.GetOrgStructureforBusinessPlanAsync(orgVersionContent, userId, orgId, orgIdlevelNo, orgList, false, true, false, true, isAssignmentPage: true, budgetYear: budgetYear);
                }
                else
                {
                    orgTree = await _pUtility.GetOrgStructureforBusinessPlanAsync(orgVersionContent,
                        userId,
                        tbiassignmentData.parentOrgId,
                        int.Parse(tbiassignmentData.parentOrgLevel.ToString()), orgList, false, true, false, true, true, budgetYear);
                }
            }
        }
        else
        {
            if (pageType.ToLower() == "checklist".ToLower())
            {
                orgTree = await _pUtility.GetOrgStructureforBusinessPlanAsync(orgVersionContent, userId, orgId, orgIdlevelNo, null, true, true);
            }
            else
            {
                orgTree = await _pUtility.GetOrgStructureforBusinessPlanAsync(orgVersionContent, userId, orgId, orgIdlevelNo, null, false, true, true, isAssignmentPage: true, budgetYear: budgetYear);
            }
        }

        if (pageType.ToLower() == "order")
        {
            var currentOrg = orgTree.FirstOrDefault(x => x["id"].ToString() == orgId && (int)x["orgLevel"] == orgIdlevelNo);
            currentOrg["isNodeDisabled"] = currentOrg["items"].Any() ? false : true;
        }
        data.Add("orgStructureBP", orgTree);
        return data;

    }



    public async Task<dynamic> GetBusinessPlanCommonDataGrid2(string userId, string orgId, int budgetYear, int orgIdlevelNo, string jsDateTime, string serviceId = null)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        dynamic businessPlanCommonData = new JObject();

        // Get category Data
        var categoryData = await (from a in tenantDbContext.tco_category
            where a.fk_tenant_id == userDetails.tenant_id && a.type.ToLower() == "SUNIT_BPLAN".ToLower() && a.status == 1
            select new
            {
                key = a.pk_cat_id,
                value = a.description
            }).OrderBy(x => x.value).ToListAsync();

        var categoryList = new JArray();
        foreach (var item in categoryData)
        {
            dynamic row = new JObject();
            row.key = item.key;
            row.value = item.value;
            categoryList.Add(row);
        }
        businessPlanCommonData.Add("category", categoryList);

        // Get Tags Data
        var actionTags = await (from atg in tenantDbContext.tcoActionTags
            where atg.FkTenantId == userDetails.tenant_id
            select new
            {
                key = atg.PkId,
                value = atg.TagDescription
            }).OrderBy(x => x.value).ToListAsync();

        var tagsData = new JArray();
        foreach (var item in actionTags)
        {
            dynamic row = new JObject();
            row.KeyId = item.key;
            row.ValueString = item.value;
            tagsData.Add(row);
        }
        businessPlanCommonData.Add("tags", tagsData);

        // Get Goals Data
        var goalsData = !string.IsNullOrEmpty(serviceId) ? await (from x in tenantDbContext.tco_goals_distribution
                join g in tenantDbContext.tco_goals on new { t = x.fk_tenant_id, l = x.fk_goal_id }
                    equals new { t = g.fk_tenant_id, l = g.pk_goal_id }
                where x.fk_tenant_id == userDetails.tenant_id
                      && g.budget_year == budgetYear
                      && x.org_id == orgId.Trim()
                      && x.service_id == serviceId
                select new
                {
                    key = g.pk_goal_id,
                    value = g.goal_name
                }).OrderBy(x => x.value).ToListAsync() :
            await (from x in tenantDbContext.tco_goals_distribution
                join g in tenantDbContext.tco_goals on new { t = x.fk_tenant_id, l = x.fk_goal_id }
                    equals new { t = g.fk_tenant_id, l = g.pk_goal_id }
                where x.fk_tenant_id == userDetails.tenant_id
                      && g.budget_year == budgetYear
                      && x.org_id == orgId.Trim()
                select new
                {
                    key = g.pk_goal_id,
                    value = g.goal_name
                }).OrderBy(x => x.value).ToListAsync();

        goalsData.AddRange(await (from x in tenantDbContext.tco_goals_distribution
            join g in tenantDbContext.tco_goals on new { t = x.fk_tenant_id, l = x.fk_goal_id }
                equals new { t = g.fk_tenant_id, l = g.pk_goal_id }
            where x.fk_tenant_id == userDetails.tenant_id
                  && g.budget_year == budgetYear
                  && string.IsNullOrEmpty(x.org_id)
                  && string.IsNullOrEmpty(x.service_id)
            select new
            {
                key = g.pk_goal_id,
                value = g.goal_name
            }).OrderBy(x => x.value).ToListAsync());

        var goalsList = new JArray();
        foreach (var item in goalsData)
        {
            dynamic row = new JObject();
            row.KeyId = item.key;
            row.ValueString = item.value;
            goalsList.Add(row);
        }
        businessPlanCommonData.Add("goals", goalsList);

        //Get Users Data
        List<BusinessPlanUserData> tenantUsers = await GetUserOrgData(userId, budgetYear);

        var userList = new JArray();

        //Empty row
        dynamic emptyRow = new JObject();
        emptyRow.key = 0;
        emptyRow.value = string.Empty;

        userList.Add(emptyRow);

        foreach (var t in tenantUsers)
        {
            dynamic row = new JObject();
            row.key = t.Id;
            if (!string.IsNullOrEmpty(t.FirstName) && !string.IsNullOrEmpty(t.LastName))
            {
                row.value = t.FirstName + " " + t.LastName;
            }
            else if (!string.IsNullOrEmpty(t.FirstName))
            {
                row.value = t.FirstName;
            }
            else if (!string.IsNullOrEmpty(t.LastName))
            {
                row.value = t.LastName;
            }
            userList.Add(row);
        }
        businessPlanCommonData.Add("users", userList);

        //Get Month
        int numberOfMonthsYear = 12;
        Dictionary<string, clsLanguageString> langStringValuesCommon = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "Common");

        string[] monthFields = new string[] { langStringValuesCommon.FirstOrDefault(v => v.Key == "month_january_short3text").Value.LangText,
            langStringValuesCommon.FirstOrDefault(v => v.Key == "month_february_short3text").Value.LangText,
            langStringValuesCommon.FirstOrDefault(v => v.Key == "month_march_short3text").Value.LangText,
            langStringValuesCommon.FirstOrDefault(v => v.Key == "month_april_short3text").Value.LangText,
            langStringValuesCommon.FirstOrDefault(v => v.Key == "month_may_short3text").Value.LangText,
            langStringValuesCommon.FirstOrDefault(v => v.Key == "month_june_short3text").Value.LangText,
            langStringValuesCommon.FirstOrDefault(v => v.Key == "month_july_short3text").Value.LangText,
            langStringValuesCommon.FirstOrDefault(v => v.Key == "month_august_short3text").Value.LangText,
            langStringValuesCommon.FirstOrDefault(v => v.Key == "month_september_short3text").Value.LangText,
            langStringValuesCommon.FirstOrDefault(v => v.Key == "month_october_short3text").Value.LangText,
            langStringValuesCommon.FirstOrDefault(v => v.Key == "month_november_short3text").Value.LangText,
            langStringValuesCommon.FirstOrDefault(v => v.Key == "month_december_short3text").Value.LangText,
        };

        DateTime dYearMonth = DateTime.Parse(jsDateTime);
        var monthsList = new JArray();
        int startPeriod = Convert.ToInt32(budgetYear.ToString() + "01");
        int endPeriod = Convert.ToInt32(budgetYear.ToString() + "12");

        var latestForecast = await tenantDbContext.tmr_data_warehouse.Where(t => t.fk_tenant_id == userDetails.tenant_id).Where(x => startPeriod <= x.forecast_period && endPeriod >= x.forecast_period).Select(z => z.forecast_period).Distinct().ToListAsync();
        int fcPeriod = latestForecast.Count > 0 ? latestForecast.OrderByDescending(x => x).FirstOrDefault() : 0;

        if (fcPeriod != 0)
        {
            DateTime dYearMonthLatestForecast = DateTime.Parse(fcPeriod.ToString().Insert(4, "-"));

            for (int i = numberOfMonthsYear * -1; i < numberOfMonthsYear; i++)
            {
                dynamic months = new JObject();
                var forecastPeriod = dYearMonth.AddMonths(i);
                months.key = string.Join(string.Empty, forecastPeriod.Year.ToString(), forecastPeriod.Month.ToString().Length == 1 ? "0" + forecastPeriod.Month.ToString() : forecastPeriod.Month.ToString());
                months.value = monthFields[dYearMonth.AddMonths(i).Month - 1] + " " + dYearMonth.AddMonths(i).Year;

                var latestForecastPeriod = monthFields[dYearMonthLatestForecast.AddMonths(0).Month - 1] + " " + dYearMonthLatestForecast.AddMonths(0).Year;
                if (string.Equals(months.value, latestForecastPeriod))
                {
                    months.isChecked = true;
                }

                monthsList.Add(months);
            }
        }

        businessPlanCommonData.Add("months", monthsList);

        // Get Statuses
        var statuses = await (from p in tenantDbContext.tco_progress_status
            where p.fk_tenant_id == userDetails.tenant_id && p.type.ToLower() == "sunit_bplan"
            select p).OrderBy(y => y.status_description).ToListAsync();

        var statusList = new JArray();
        foreach (var item in statuses)
        {
            dynamic row = new JObject();
            row.key = item.status_id;
            row.value = item.status_description;
            statusList.Add(row);
        }
        businessPlanCommonData.Add("statuses", statusList);

        //Get Risks
        Dictionary<string, clsLanguageString> langStringValuesMonthlyReport = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "MonthlyReport_Investment");
        var risks = (from object item in Enum.GetValues(typeof(clsConstants.MonthlyReportRiskStatus))
            select new
            {
                Key = ((int)item).ToString(),
                Value = langStringValuesMonthlyReport["MRI_investmentGrid_risk_" + item.ToString().ToLower()].LangText
            }).OrderBy(y => y.Value).ToList();

        var riskList = new JArray();
        foreach (var item in risks)
        {
            dynamic row = new JObject();
            row.key = item.Key;
            row.value = item.Value;
            riskList.Add(row);
        }
        businessPlanCommonData.Add("risks", riskList);

        businessPlanCommonData.Add("serviceList", string.Empty);
        businessPlanCommonData.Add("displaydropdown", false);

        //Get Assignments
        var assignments = string.IsNullOrEmpty(serviceId) || serviceId == "-1" ? await (from assignment in tenantDbContext.tbiassignments
                where assignment.orgId == orgId && assignment.tenantId == userDetails.tenant_id
                                                && assignment.budgetYear == budgetYear
                select new
                {
                    key = assignment.assignmentId,
                    value = string.IsNullOrEmpty(assignment.assignmentrefid) ? assignment.assignmentName : "[" + assignment.assignmentrefid + "]" + assignment.assignmentName
                }).OrderBy(y => y.value).ToListAsync() :
            await (from assignment in tenantDbContext.tbiassignments
                where assignment.orgId == orgId && assignment.serviceId == serviceId && assignment.tenantId == userDetails.tenant_id
                      && assignment.budgetYear == budgetYear
                select new
                {
                    key = assignment.assignmentId,
                    value = string.IsNullOrEmpty(assignment.assignmentrefid) ? assignment.assignmentName : "[" + assignment.assignmentrefid + "]" + assignment.assignmentName
                }).OrderBy(y => y.value).ToListAsync();

        var assignmentList = new JArray();
        foreach (var item in assignments)
        {
            dynamic row = new JObject();
            row.key = item.key;
            row.value = item.value;
            assignmentList.Add(row);
        }
        businessPlanCommonData.Add("assignments", assignmentList);

        return businessPlanCommonData;
    }



    public async Task<dynamic> GetCategoriesAndTheirDetailsForSelectedOrgIdAsync(string userId, int budgetYear, string orgId, int orgIdlevelNo)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        Dictionary<string, clsLanguageString> langStringValuesBusinessPlan = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BusinessPlan");
        Guid guid2 = Guid.Empty;
        DateTime? nullDeadline = null;

        var orgVersionContent = await _pUtility.GetOrgVersionSpecificContentAsync(userId, _pUtility.GetForecastPeriod(budgetYear, 1));
        List<tco_org_hierarchy> lstOrgHierarchy = orgVersionContent.lstOrgHierarchy;

        var lowerorgsubunitsList = GetLowerLevelOrgsUnitsList(orgIdlevelNo, orgId, lstOrgHierarchy);
        var result = await GetDatasetForSelectedOrgIdAsync(userId, tenantDbContext, userDetails, budgetYear, orgId, guid2, nullDeadline, lowerorgsubunitsList.ToList(), orgIdlevelNo);

        result.ForEach(x =>
        {
            if (x.assignment == "")
            {
                x.assignment = langStringValuesBusinessPlan.FirstOrDefault(v => v.Key == "SUBP_Grid2_DetachedTasks").Value.LangText;
            }
        });

        // Get categories Data
        var categoriesData = await (from a in tenantDbContext.tco_category
            where a.fk_tenant_id == userDetails.tenant_id && a.type.ToLower() == "SUNIT_BPLAN".ToLower() && a.status == 1
            select new
            {
                key = a.pk_cat_id,
                value = a.description
            }).OrderBy(x => x.key).ToListAsync();

        var categoriesSet = result.OrderBy(y => y.filter).Select(x => x.filter).Distinct().ToList();

        List<string> categories = new List<string>();
        foreach (var item in categoriesSet)
        {
            if (!string.IsNullOrEmpty(item) && item.Split(',').ToList().Any())
            {
                foreach (var cat in item.Split(',').ToList())
                {
                    if (categories.FirstOrDefault(x => x == cat) == null)
                    {
                        categories.Add(cat);
                    }
                }
            }
        }
        categories = categories.Distinct().ToList();

        dynamic dataArray = new JArray();

        dynamic dataAllObj = new JObject();
        dataAllObj.id = Guid.Empty;
        dataAllObj.name = langStringValuesBusinessPlan.FirstOrDefault(v => v.Key == "SUBP_all_categories").Value.LangText;
        dataAllObj.assignments = orgIdlevelNo == 1 ? result.Where(x => x.assignmentId != Guid.Empty).Select(y => y.uniqueAssignmentId).Distinct().Count() :
            result.Where(x => x.assignmentId != Guid.Empty && x.taskOrgId == orgId).Select(y => y.uniqueAssignmentId).Distinct().Count();

        dataAllObj.tasks = orgIdlevelNo == 1 ? result.Where(x => x.pkId != Guid.Empty).Select(y => y.pkId).Distinct().Count() :
            result.Where(x => x.pkId != Guid.Empty && x.taskOrgId == orgId).Select(y => y.pkId).Distinct().Count();
        dataAllObj.status = 2;
        dataArray.Add(dataAllObj);

        foreach (var cat in categories)
        {
            dynamic dataObj = new JObject();
            dataObj.id = cat;
            dataObj.name = categoriesData.FirstOrDefault(x => x.key.ToString().ToLower() == cat.ToLower()).value;
            dataObj.assignments = orgIdlevelNo == 1 ? result.Where(x => x.filter.ToLower().Contains(cat.ToLower()) && x.assignmentId != Guid.Empty).Select(y => y.uniqueAssignmentId).Distinct().Count() :
                result.Where(x => x.filter.ToLower().Contains(cat.ToLower()) && x.assignmentId != Guid.Empty && x.taskOrgId == orgId).Select(y => y.uniqueAssignmentId).Distinct().Count();

            dataObj.tasks = orgIdlevelNo == 1 ? result.Where(x => x.filter.ToLower().Contains(cat.ToLower()) && x.pkId != Guid.Empty).Select(y => y.pkId).Distinct().Count() :
                result.Where(x => x.filter.ToLower().Contains(cat.ToLower()) && x.pkId != Guid.Empty && x.taskOrgId == orgId).Select(y => y.pkId).Distinct().Count();

            dataObj.status = 2;
            dataArray.Add(dataObj);
        }

        dynamic header = new JObject();
        header.title = langStringValuesBusinessPlan.FirstOrDefault(v => v.Key == "SUBP_grid_categories_title").Value.LangText;
        header.descriptiontip = langStringValuesBusinessPlan.FirstOrDefault(v => v.Key == "SUBP_grid_categories_desc").Value.LangText;

        dynamic headerArray = new JArray();
        headerArray.Add(header);

        dynamic resultSet = new JObject();
        resultSet.Add("header", headerArray);
        resultSet.Add("data", dataArray);

        return resultSet;
    }



    public async Task<dynamic> GetStatusOfAssignmentsAndTasksForSelectedOrgIdAsync(string userId, int budgetYear, string orgId, int orgIdlevelNo, Guid? categoryId, string type)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        Dictionary<string, clsLanguageString> langStringValuesBusinessPlan = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BusinessPlan");
        Guid guid2 = Guid.Empty;
        DateTime? nullDeadline = null;

        var orgVersionContent = await _pUtility.GetOrgVersionSpecificContentAsync(userId, _pUtility.GetForecastPeriod(budgetYear, 1));
        List<tco_org_hierarchy> lstOrgHierarchy = orgVersionContent.lstOrgHierarchy;

        var statusDetails = await (from p in tenantDbContext.tco_progress_status
            where p.fk_tenant_id == userDetails.tenant_id && p.type.ToLower() == "sunit_bplan"
            select p).ToListAsync();

        var lowerorgsubunitsList = GetLowerLevelOrgsUnitsList(orgIdlevelNo, orgId, lstOrgHierarchy);
        var result = await GetDatasetForSelectedOrgIdAsync(userId, tenantDbContext, userDetails, budgetYear, orgId, guid2, nullDeadline, lowerorgsubunitsList.ToList(), orgIdlevelNo);

        if (lowerorgsubunitsList.Any())
        {
            result = result.Where(x => lowerorgsubunitsList.Contains(x.taskOrgId)).ToList();
        }

        result.ForEach(x =>
        {
            if (x.assignment == "")
            {
                x.assignment = langStringValuesBusinessPlan.FirstOrDefault(v => v.Key == "SUBP_Grid2_DetachedTasks").Value.LangText;
            }
        });

        if (categoryId != null && categoryId != Guid.Empty)
        {
            result = result.Where(z => !string.IsNullOrEmpty(z.filter)).Where(x => x.filter.Split(',').ToList().Contains(categoryId.Value.ToString())).ToList();
        }

        if (orgIdlevelNo != 1)
        {
            result = result.Where(x => x.taskOrgId == orgId).ToList();
        }

        var expiredAssignmentsCount = result.Where(x => x.deadline != null && x.deadline.Value.Date < DateTime.UtcNow.Date).Select(y => y.assignmentId).Distinct().ToList();

        int noOfAssignmentsWithoutTasks = result.Where(x => x.pkId == Guid.Empty && x.taskOrgId == orgId).Select(y => y.assignmentId).Distinct().ToList().Count();

        dynamic resultObj = new JObject();

        dynamic plotArea = new JObject();
        plotArea.width = 300;
        plotArea.height = 300;
        resultObj.plotArea = plotArea;

        dynamic title = new JObject();
        title.visible = false;
        resultObj.title = title;

        dynamic legend = new JObject();
        legend.visible = false;
        resultObj.legend = legend;

        dynamic seriesDefaults = new JObject();
        seriesDefaults.type = "donut";

        dynamic overlay = new JObject();
        overlay.gradient = "none";
        seriesDefaults.overlay = overlay;

        dynamic labels = new JObject();
        labels.font = "14px semiFont, sans-serif";
        labels.position = "insideEnd";
        labels.visible = true;
        labels.background = "transparent";
        labels.template = "#= category #: \n #= value #";
        seriesDefaults.labels = labels;
        resultObj.seriesDefaults = seriesDefaults;

        dynamic series = new JArray();

        dynamic seriesObj = new JObject();
        dynamic seriesObjLabels = new JObject();
        seriesObjLabels.align = "circle";
        seriesObjLabels.distance = 20;
        seriesObj.labels = seriesObjLabels;
        seriesObj.visual = "";

        dynamic data = new JArray();

        List<int> statuses = new List<int>();
        if (type == "assignment")
        {
            statuses = result.Where(x => x.assignmentId != Guid.Empty).Select(x => x.assignmentStatus).Distinct().ToList();
        }
        else if (type == "task")
        {
            statuses = result.Where(x => x.pkId != Guid.Empty).Select(x => x.status).Distinct().ToList();
        }

        int count = 0;
        foreach (var s in statuses)
        {
            dynamic obj = new JObject();

            if (type == "assignment")
            {
                obj.category = s == 0 || statusDetails.FirstOrDefault(x => x.status_id == s) == null ? langStringValuesBusinessPlan.FirstOrDefault(v => v.Key == "SUBP_status_not_defined").Value.LangText : statusDetails.FirstOrDefault(x => x.status_id == s).status_description;
                obj.value = result.Where(x => x.assignmentStatus == s).Select(y => y.uniqueAssignmentId).Distinct().ToList().Count();
            }
            else if (type == "task")
            {
                obj.category = s == 0 || statusDetails.FirstOrDefault(x => x.status_id == s) == null ? langStringValuesBusinessPlan.FirstOrDefault(v => v.Key == "SUBP_status_not_defined").Value.LangText : statusDetails.FirstOrDefault(x => x.status_id == s).status_description;
                obj.value = result.Where(x => x.status == s && x.pkId != Guid.Empty).Select(y => y.pkId).Distinct().ToList().Count();
            }

            obj.explode = false;
            obj.color = _pUtility.GetStatusColors()[count];
            data.Add(obj);
            count++;
        }

        seriesObj.data = data;
        series.Add(seriesObj);
        resultObj.series = series;

        dynamic tooltip = new JObject();
        tooltip.visible = true;
        tooltip.template = "#= category #: \n #= value #";
        resultObj.tooltip = tooltip;
        resultObj.noOfAssignmentsWithoutTasks = noOfAssignmentsWithoutTasks;
        if (type == "assignment")
        {
            resultObj.totalStatusContent = result.Select(y => y.uniqueAssignmentId).Distinct().Count();
        }
        else if (type == "task")
        {
            resultObj.totalStatusContent = result.Where(x => x.pkId != Guid.Empty).Select(y => y.pkId).Distinct().Count();
        }

        if (expiredAssignmentsCount.Count() > 0)
        {
            resultObj.showSymbol = true;
        }
        else
        {
            resultObj.showSymbol = false;
        }

        return resultObj;
    }



    public async Task<dynamic> GetStatusOfAssignmentsAndTasksPerUnitForSelectedOrgIdAsync(string userId, int budgetYear, string orgId, int orgIdlevelNo, Guid? categoryId, string type)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        Dictionary<string, clsLanguageString> langStringValuesBusinessPlan = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BusinessPlan");
        Guid guid2 = Guid.Empty;
        DateTime? nullDeadline = null;

        var orgVersionContent = await _pUtility.GetOrgVersionSpecificContentAsync(userId, _pUtility.GetForecastPeriod(budgetYear, 1));
        List<tco_org_hierarchy> lstOrgHierarchy = orgVersionContent.lstOrgHierarchy;

        var statusDetails = await (from p in tenantDbContext.tco_progress_status
            where p.fk_tenant_id == userDetails.tenant_id && p.type.ToLower() == "sunit_bplan"
            select p).ToListAsync();

        var lowerorgsubunitsList = GetNextLevelOrgsUnitsList(orgIdlevelNo, orgId, lstOrgHierarchy);
        var result = await GetDatasetForSelectedOrgIdAsync(userId, tenantDbContext, userDetails, budgetYear, orgId, guid2, nullDeadline, lowerorgsubunitsList.ToList(), orgIdlevelNo);

        result.ForEach(x =>
        {
            if (x.assignment == "")
            {
                x.assignment = langStringValuesBusinessPlan.FirstOrDefault(v => v.Key == "SUBP_Grid2_DetachedTasks").Value.LangText;
            }
        });

        if (categoryId != null && categoryId.Value != Guid.Empty)
        {
            result = result.Where(z => !string.IsNullOrEmpty(z.filter)).Where(x => x.filter.Split(',').ToList().Contains(categoryId.Value.ToString())).ToList();
        }

        int noOfAssignmentsWithoutTasks = result.Where(x => x.pkId == Guid.Empty).Select(y => y.assignmentId).Distinct().ToList().Count();

        dynamic resultObj = new JObject();

        dynamic plotArea = new JObject();
        plotArea.width = 300;
        plotArea.height = 300;
        resultObj.plotArea = plotArea;

        dynamic title = new JObject();
        title.visible = false;
        resultObj.title = title;

        dynamic legend = new JObject();
        legend.visible = false;
        resultObj.legend = legend;

        dynamic seriesDefaults = new JObject();
        seriesDefaults.type = "column";
        seriesDefaults.stack = true;
        seriesDefaults.gap = 0.3;

        dynamic overlay = new JObject();
        overlay.gradient = "none";
        seriesDefaults.overlay = overlay;

        resultObj.seriesDefaults = seriesDefaults;

        dynamic valueAxis = new JObject();
        valueAxis.visible = false;
        dynamic majorGridLines = new JObject();
        majorGridLines.visible = false;
        valueAxis.majorGridLines = majorGridLines;
        resultObj.valueAxis = valueAxis;

        List<int> statuses = new List<int>();
        dynamic series = new JArray();
        List<string> orgIds = new List<string>();
        List<string> categoryAxisOrgIds = new List<string>();

        if (type == "assignment")
        {
            var assigmnetStatuses = result.Where(y => y.assignmentId != Guid.Empty).Select(x => x.assignmentStatus).Distinct().ToList();
            var assignmentOrgIds = result.Where(y => y.assignmentId != Guid.Empty).OrderBy(z => z.taskOrgId).Select(x => x.taskOrgId).Distinct().ToList();
            statuses.Add(assigmnetStatuses.Count());
            foreach (var stat in assigmnetStatuses)
            {
                dynamic obj = new JObject();
                obj.name = statusDetails.FirstOrDefault(x => x.pk_id == stat) == null ? langStringValuesBusinessPlan.FirstOrDefault(v => v.Key == "SUBP_status_not_defined").Value.LangText : statusDetails.FirstOrDefault(x => x.pk_id == stat).status_description;
                dynamic statusArray = new JArray();
                foreach (var o in assignmentOrgIds)
                {
                    if (!orgIds.Contains(o))
                    {
                        orgIds.Add(o);
                    }
                    categoryAxisOrgIds.Add(o);
                    statusArray.Add(result.Where(x => x.taskOrgId == o && x.assignmentStatus == stat && x.assignmentId != Guid.Empty).Select(x => x.assignmentId).Distinct().Count());
                }
                obj.Add("data", statusArray);
                series.Add(obj);
            }
        }
        else if (type == "task")
        {
            var taskStatuses = result.Where(y => y.pkId != Guid.Empty).OrderBy(z => z.taskOrgId).Select(x => x.status).Distinct().ToList();
            var taskOrgIds = result.Where(y => y.pkId != Guid.Empty).OrderBy(z => z.taskOrgId).Select(x => x.taskOrgId).Distinct().ToList();
            statuses.Add(taskStatuses.Count());
            foreach (var stat in taskStatuses)
            {
                dynamic obj = new JObject();
                obj.name = statusDetails.FirstOrDefault(x => x.status_id == stat) == null ? langStringValuesBusinessPlan.FirstOrDefault(v => v.Key == "SUBP_status_not_defined").Value.LangText : statusDetails.FirstOrDefault(x => x.status_id == stat).status_description;
                dynamic statusArray = new JArray();
                foreach (var o in taskOrgIds)
                {
                    if (!orgIds.Contains(o))
                    {
                        orgIds.Add(o);
                    }
                    categoryAxisOrgIds.Add(o);
                    statusArray.Add(result.Where(x => x.taskOrgId == o && x.status == stat && x.pkId != Guid.Empty).Select(x => x.pkId).Distinct().Count());
                }
                obj.Add("data", statusArray);
                series.Add(obj);
            }
        }
        resultObj.Add("series", series);

        dynamic seriesColors = new JArray();
        int maxStatusCount = statuses.Max();
        for (int i = 0; i < maxStatusCount; i++)
        {
            seriesColors.Add(_pUtility.GetStatusColors()[i]);
        }

        resultObj.Add("seriesColors", seriesColors);

        dynamic categoryAxis = new JObject();
        dynamic categories = new JArray();
        foreach (var o in orgIds.Distinct().OrderBy(x => x).ToList())
        {
            categories.Add(result.FirstOrDefault(x => x.taskOrgId == o).taskOrgName);
        }
        categoryAxis.Add("categories", categories);
        dynamic line = new JObject();
        line.visible = false;
        categoryAxis.line = line;
        categoryAxis.majorGridLines = majorGridLines;

        dynamic categoryAxisLabels = new JObject();
        categoryAxisLabels.color = "#606060";
        categoryAxisLabels.rotation = -90;
        dynamic padding = new JObject();
        padding.top = 5;
        categoryAxisLabels.padding = padding;
        categoryAxis.labels = categoryAxisLabels;

        resultObj.categoryAxis = categoryAxis;

        dynamic tooltip = new JObject();
        tooltip.visible = true;
        tooltip.template = "#= series.name #: \n #= value #";
        resultObj.tooltip = tooltip;

        return resultObj;
    }



    public async Task<dynamic> GetBaseDataforTask(string userId, string taskId, string orgId, string serviceId, int budgetYear, int orgLevel, int forecastPeriod)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        var orgVersionContent = await _pUtility.GetOrgVersionSpecificContentAsync(userId, _pUtility.GetForecastPeriod(budgetYear, 1));
        List<tco_org_hierarchy> lstOrgHierarchy = orgVersionContent.lstOrgHierarchy;
        string orgName = string.Empty;
        dynamic taskData = new JObject();
        dynamic assData = new JObject();
        Guid emptyGuid = Guid.Empty;
        bool showMonthlyReportTaskStatus = await CheckTaskStatusEditabilityAsync(userId, taskId, orgId, serviceId, orgLevel);
        var id = Guid.Parse(taskId);
        var tbitaskData = string.IsNullOrEmpty(serviceId) || serviceId == "-1" ? await tenantDbContext.tbitasks.FirstOrDefaultAsync(x => x.uniquetaskId == id && x.orgId == orgId && x.orgLevel == orgLevel) : await tenantDbContext.tbitasks.FirstOrDefaultAsync(x => x.uniquetaskId == id && x.orgId == orgId && x.orgLevel == orgLevel && x.serviceId == serviceId);
        var unique_task_id = tbitaskData.uniquetaskId;
        var mrTaskData = await tenantDbContext.tbi_task_monthly_status.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.fk_task_id == tbitaskData.taskid && x.forecast_period == forecastPeriod);

        if (tbitaskData != null)
        {
            //taskName
            taskData.Add("taskName", tbitaskData.taskname);

            //taskAssignmentName
            if (emptyGuid != tbitaskData.assignmentId)
            {
                var tbiassData = await tenantDbContext.tbiassignments.FirstOrDefaultAsync(x => x.assignmentId == tbitaskData.assignmentId);
                if (tbiassData != null)
                {
                    var tenantUsers = await (from t in tenantDbContext.vwUserDetails
                        where t.tenant_id == userDetails.tenant_id
                        select new
                        {
                            userId = t.pk_id,
                            fName = t.first_name,
                            lName = t.last_name
                        }).ToListAsync();
                    bool showMonthlyReportAssignmentStatus = await CheckAssignmentStatusEditabilityAsync(userId, tbiassData.uniqueassignmentId.ToString(), orgId, serviceId, orgLevel);
                    int monthRepStatus = showMonthlyReportAssignmentStatus ? await GetMonthlyReportAssignmentStatusAsync(userDetails.tenant_id, tbitaskData.assignmentId.ToString(), forecastPeriod) : tbiassData.status;
                    assData.Add("assignmentid", tbiassData.uniqueassignmentId);
                    assData.Add("assignmentName", string.IsNullOrEmpty(tbiassData.assignmentrefid) ? tbiassData.assignmentName : $"[{tbiassData.assignmentrefid}]  {tbiassData.assignmentName}");
                    assData.Add("caseReference", tbiassData.external_reference);
                    assData.Add("responsible", tenantUsers.Any(h => h.userId == tbiassData.userAssignedTo) ? tenantUsers.FirstOrDefault(h => h.userId == tbiassData.userAssignedTo).fName +
                        " " + tenantUsers.FirstOrDefault(h => h.userId == tbiassData.userAssignedTo).lName : string.Empty);

                    assData.Add("assignmentOwner", tbiassData.assignmentOwner == 0 ? -1 : tbiassData.assignmentOwner);
                    assData.Add("assignmentOwnerName", tbiassData.assignmentOwner == 0 || tenantUsers.FirstOrDefault(h => h.userId == tbiassData.assignmentOwner) == null ? string.Empty :
                        $"{tenantUsers.FirstOrDefault(h => h.userId == tbiassData.assignmentOwner).fName} {tenantUsers.FirstOrDefault(h => h.userId == tbiassData.assignmentOwner).lName}");

                    var statuses = await (from p in tenantDbContext.tco_progress_status
                        where p.fk_tenant_id == userDetails.tenant_id && p.type.ToLower() == "sunit_bplan"
                        select p).ToListAsync();

                    assData.Add("assignmentStatus", monthRepStatus == 0 ? string.Empty :
                        $"{statuses.FirstOrDefault(h => h.status_id == monthRepStatus).status_description}");
                    assData.Add("assignmentStartDate", tbiassData.startDate.ToString("dd.MM.yyyy"));
                    assData.Add("assignmentendDate", tbiassData.endDate.ToString("dd.MM.yyyy"));
                    assData.Add("pkAssignmentId", tbiassData.assignmentId);


                    //get parent org data
                    var parentOrgData = await tenantDbContext.tbiassignments.FirstOrDefaultAsync(x => x.uniqueassignmentId == tbiassData.uniqueassignmentId
                        && !x.isDistributedtoLowerLevel.Value);
                    if (parentOrgData != null)
                    {
                        switch (parentOrgData.parentOrgLevel.Value)
                        {
                            case 1:
                                orgName = $"<span class='su-bp-tag su-bp-tag-dark'>{lstOrgHierarchy.FirstOrDefault(x => x.org_id_1 == parentOrgData.parentOrgId).org_name_1}</span>";
                                break;

                            case 2:
                                orgName = $"<span class='su-bp-tag su-bp-tag-dark'>{lstOrgHierarchy.FirstOrDefault(x => x.org_id_2 == parentOrgData.parentOrgId).org_name_2}</span>";
                                break;

                            case 3:
                                orgName = $"<span class='su-bp-tag su-bp-tag-dark'>{lstOrgHierarchy.FirstOrDefault(x => x.org_id_3 == parentOrgData.parentOrgId).org_name_3}</span>";
                                break;

                            case 4:
                                orgName = $"<span class='su-bp-tag su-bp-tag-dark'>{lstOrgHierarchy.FirstOrDefault(x => x.org_id_4 == parentOrgData.parentOrgId).org_name_4}</span>";
                                break;

                            case 5:
                                orgName = $"<span class='su-bp-tag su-bp-tag-dark'>{lstOrgHierarchy.FirstOrDefault(x => x.org_id_5 == parentOrgData.parentOrgId).org_name_5}</span>";
                                break;

                            default:
                                break;
                        }
                    }

                    // Get categories Data
                    var categoriesData = await (from a in tenantDbContext.tco_category
                        where a.fk_tenant_id == userDetails.tenant_id && a.type.ToLower() == "SUNIT_BPLAN".ToLower() && a.status == 1
                        select new
                        {
                            key = a.pk_cat_id,
                            value = a.description
                        }).OrderBy(x => x.key).ToListAsync();

                    StringBuilder sb = new StringBuilder();
                    List<string> catids = new List<string>();
                    if (!string.IsNullOrEmpty(tbiassData.category) && tbiassData.category.Split(',').ToList().Any())
                    {
                        catids = tbiassData.category.ToString().Split(',').ToList();
                        foreach (var ct in catids)
                        {
                            Guid catId = Guid.Empty;
                            Guid.TryParse(ct, out catId);
                            if (categoriesData.Any(x => x.key == catId))
                            {
                                sb.Append("<span class='bp-green-tag'>" + categoriesData.FirstOrDefault(x => x.key == catId).value + "</span>&nbsp;");
                            }
                        }
                        if (!string.IsNullOrEmpty(orgName))
                        {
                            sb.Append("</br>");
                            sb.Append(orgName);
                        }
                    }
                    assData.Add("categories", sb.ToString());
                    sb.Clear();

                    // Get tags Data
                    var tagsData = await (from atg in tenantDbContext.tcoActionTags
                        where atg.FkTenantId == userDetails.tenant_id
                        select new
                        {
                            key = atg.PkId,
                            value = atg.TagDescription
                        }).OrderBy(x => x.key).ToListAsync();

                    List<int> tagids;
                    tagids = !string.IsNullOrEmpty(tbiassData.tags.ToString()) ? tbiassData.tags.ToString().Split(',').Select(int.Parse).ToList() : new List<int>();
                    foreach (var tg in tagids)
                    {
                        if (tagsData.Any(x => x.key == tg))
                        {
                            sb.Append("<span class='bp-grey-tag'>" + tagsData.FirstOrDefault(x => x.key == tg).value + "</span>&nbsp;");
                        }
                    }
                    assData.Add("tags", sb.ToString());
                    sb.Clear();

                    // Get Goal and Focus area Data
                    var goalsData = !string.IsNullOrEmpty(serviceId) ? await (from x in tenantDbContext.tco_goals_distribution
                            join g in tenantDbContext.tco_goals on new { t = x.fk_tenant_id, l = x.fk_goal_id }
                                equals new { t = g.fk_tenant_id, l = g.pk_goal_id }
                            where x.fk_tenant_id == userDetails.tenant_id && g.budget_year == budgetYear && x.org_id == orgId.Trim() && x.service_id == serviceId
                            select new
                            {
                                key = g.pk_goal_id,
                                value = g.goal_name,
                                focusarea = g.focus_area
                            }).OrderBy(x => x.key).ToListAsync() :
                        await (from x in tenantDbContext.tco_goals_distribution
                            join g in tenantDbContext.tco_goals on new { t = x.fk_tenant_id, l = x.fk_goal_id }
                                equals new { t = g.fk_tenant_id, l = g.pk_goal_id }
                            where x.fk_tenant_id == userDetails.tenant_id && g.budget_year == budgetYear && x.org_id == orgId.Trim()
                            select new
                            {
                                key = g.pk_goal_id,
                                value = g.goal_name,
                                focusarea = g.focus_area
                            }).OrderBy(x => x.key).ToListAsync();

                    goalsData.AddRange(await (from x in tenantDbContext.tco_goals_distribution
                        join g in tenantDbContext.tco_goals on new { t = x.fk_tenant_id, l = x.fk_goal_id }
                            equals new { t = g.fk_tenant_id, l = g.pk_goal_id }
                        where x.fk_tenant_id == userDetails.tenant_id && g.budget_year == budgetYear && string.IsNullOrEmpty(x.org_id) && string.IsNullOrEmpty(x.service_id)
                        select new
                        {
                            key = g.pk_goal_id,
                            value = g.goal_name,
                            focusarea = g.focus_area
                        }).OrderBy(x => x.key).ToListAsync());
                    var goals = await tenantDbContext.TbiAssignmentGoal.Where(x => x.fk_assignment_id == tbiassData.assignmentId).AsNoTracking().ToListAsync();
                    string focusAreaDesc = string.Empty;
                    string goalsDesc = string.Empty;
                    foreach (var goal in goals)
                    {
                        Guid goalid = goal.fk_goal_id;
                        if (goalid != null || goalid != Guid.Empty)
                        {
                            if (goalsData.Any(x => x.key == goalid))
                            {
                                goalsDesc = goalsDesc + "<span class='bp-white-tag'>" + goalsData.FirstOrDefault(x => x.key == goalid).value + "</span>&nbsp;";
                                if (!string.IsNullOrEmpty(goalsData.FirstOrDefault(x => x.key == goalid).focusarea))
                                {
                                    //sb.Clear();
                                    int gID = goalsData.FirstOrDefault(x => x.key == goalid) == null ? 0 : int.Parse(goalsData.FirstOrDefault(x => x.key == goalid).focusarea);
                                    var fa_flag = await tenantDbContext.tco_focusarea.FirstOrDefaultAsync(s => s.fk_tenant_id == userDetails.tenant_id
                                        && s.budget_year == budgetYear && s.pk_id == gID);

                                    if (fa_flag != null)
                                    {
                                        focusAreaDesc = focusAreaDesc + "<span class='bp-white-tag'>" + fa_flag.focusarea_description + "</span>&nbsp;";
                                    }
                                }
                            }
                        }
                    }
                    assData.Add("goals", goalsDesc);
                    assData.Add("focusArea", focusAreaDesc);
                }

                taskData.Add("assignmentData", assData);
            }
            else
            {
                assData.Add("assignmentid", Guid.Empty);
                assData.Add("assignmentName", string.Empty);
                assData.Add("caseReference", string.Empty);
                assData.Add("categories", string.Empty);
                assData.Add("focusArea", string.Empty);
                assData.Add("goals", string.Empty);
                assData.Add("responsible", string.Empty);
                taskData.Add("assignmentData", assData);
            }

            //task status description
            taskData.Add("taskstatusDesc", mrTaskData != null ? mrTaskData.description : string.Empty);

            //task description
            taskData.Add("description", tbitaskData.description);

            //task startdate
            taskData.Add("startDate", tbitaskData.startDate.ToString("dd.MM.yyyy"));

            //task enddate
            taskData.Add("endDate", tbitaskData.endDate.ToString("dd.MM.yyyy"));

            //task risk
            taskData.Add("status", showMonthlyReportTaskStatus ? (await GetMonthlyReportTaskStatusAsync(userDetails.tenant_id, tbitaskData.taskid.ToString(), forecastPeriod)).ToString() : tbitaskData.taskstatus.ToString());

            //Get Users Data
            taskData.Add("users", tbitaskData.userAssignedTo);

            //assignment description history
            taskData.Add("descriptionId",
                tbitaskData.description_history.HasValue ? tbitaskData.description_history.Value == Guid.Empty ? Guid.Empty : tbitaskData.description_history.Value
                    : Guid.Empty);


            var taskforUniqueId = (await tenantDbContext.tbitasks.Where(x => x.uniquetaskId == id && x.tenantId == userDetails.tenant_id).OrderBy(x => x.orgLevel).ToListAsync()).FirstOrDefault();
            if (taskforUniqueId != null)
            {
                taskData.Add("disableTaskName", taskforUniqueId.orgLevel != tbitaskData.orgLevel);
            }
            else
            {
                taskData.Add("disableTaskName", false);
            }

            return taskData;
        }
        else
        {
            return taskData;
        }
    }



    private async Task UpdateDetailsForAssignment(string userId, string uniqueassignmentid, string assignmentName, string category, string orgId, int orgLevel, int budgetYear)
    {
        try
        {
            UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
            TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
            Guid Id = Guid.Parse(uniqueassignmentid);

            if (!string.IsNullOrEmpty(assignmentName) && !string.IsNullOrEmpty(category))
            {
                var assignmentData = await tenantDbContext.tbiassignments.Where(x => x.uniqueassignmentId == Id && x.tenantId == userDetails.tenant_id).ToListAsync();
                if (assignmentData.Count > 0)
                {
                    var existingAssignmentData = assignmentData.OrderBy(x => x.distributedParentOrgLevel).FirstOrDefault(x => x.uniqueassignmentId == Id && x.parentOrgId == orgId && x.parentOrgLevel == orgLevel);
                    if (existingAssignmentData != null && !existingAssignmentData.isDistributedtoLowerLevel.Value)
                    {
                        foreach (var assignment in assignmentData)
                        {
                            assignment.assignmentName = assignmentName;
                            assignment.category = category;

                            await _planDiscAssignments.ValidateAndSaveAssignmentForPDAsync(userId, budgetYear, assignment.orgLevel ?? -1, assignment.orgId ?? "-1", assignment.assignmentId, PDMeetingType.UPDATE.ToString());
                        }
                        await tenantDbContext.SaveChangesAsync();
                    }
                }
            }
        }
        catch (Exception)
        {
            throw;
        }
    }



    private async Task UpdateOwnerDetailsForAssignment(string userId, string uniqueassignmentid, int budgetYear)
    {
        try
        {
            UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
            TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
            Guid Id = Guid.Parse(uniqueassignmentid);
            var assignmentData = await tenantDbContext.tbiassignments.Where(x => x.uniqueassignmentId == Id && x.tenantId == userDetails.tenant_id).ToListAsync();
            var ownerDetails = assignmentData.FirstOrDefault(x => x.uniqueassignmentId == Id && x.tenantId == userDetails.tenant_id &&
                                                                  x.parentOrgId == x.orgId && x.parentOrgLevel == x.orgLevel &&
                                                                  !x.isDistributedtoLowerLevel.Value);
            if (assignmentData.Count > 0 && ownerDetails != null)
            {
                foreach (var assignment in assignmentData)
                {
                    assignment.assignmentOwner = ownerDetails.assignmentOwner;
                    assignment.category = ownerDetails.category;
                    assignment.tags = ownerDetails.tags;
                    assignment.external_reference = ownerDetails.external_reference;

                    await _planDiscAssignments.ValidateAndSaveAssignmentForPDAsync(userId, budgetYear, assignment.orgLevel ?? -1, assignment.orgId ?? "-1", assignment.assignmentId, PDMeetingType.UPDATE.ToString());
                }
                await tenantDbContext.SaveChangesAsync();
            }
        }
        catch (Exception)
        {
            throw;
        }
    }



    public string UpdateTaskDescription(string userId, ClsTaskDescHelper input)
    {
        UserData userDetails = _pUtility.GetUserDetails(userId);
        TenantData tenantDetails = _pUtility.GetTenantData(userId);
        TenantDBContext tenantDbContext = _pUtility.GetTenantDBContext();

        Guid task_id = Guid.Parse(input.taskId);
        Guid desc_id = Guid.Parse(input.descriptionId);

        var desc_flag = tenantDbContext.tbitasks.FirstOrDefault(x => x.tenantId == userDetails.tenant_id && x.taskid == task_id && x.description_history == desc_id && x.budgetYear == input.BudgetYear);

        string textToSave = string.Empty;
        Guid textGuid = Guid.Empty;
        if (desc_flag != null)
        {
            //update existing desc
            desc_flag.description = textToSave = input.description;
            textGuid = desc_id;

            tenantDbContext.SaveChanges();
            _pUtility.SaveTextLog(userId, textGuid, textToSave);
            return "success";
        }
        return "New task.Can't update description directly";
    }



    public async Task<JObject> GetAssignmentAndTaskUnitDataAsync(string userId, int budgetYear, string orgId, int orgIdlevelNo, string serviceId, string categoryId, clsFiltersAssignmentTaskGrid filters)
    {
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStringValuesBusinessPlan = await _pUtility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "BusinessPlan");

        TimeSpan cacheTimeOut = new TimeSpan(0, 1, 0);
        int clientId = userDetails.client_id;
        string key = (userId + "-" + string.Empty + "-" + -1 + "-" + "tcoUserForAssignments").Trim();

        string strUserList = await _cache.GetStringForUserAsync(clientId, userDetails.tenant_id, userId, key);
        //Apply only user role filters while fetching users(Same way we fetch for popup)
        List<AssignmentUserListHelper> tenantUsers;
        if (String.IsNullOrEmpty(strUserList))
        {
            tenantUsers = await (from t in tenantDbContext.tco_users
                where t.IsActive.HasValue && t.IsActive.Value
                select new AssignmentUserListHelper
                {
                    userId = t.pk_id,
                    fName = t.first_name,
                    lName = t.last_name
                }).OrderBy(a => a.fName).ThenBy(b => b.lName).ToListAsync();

            string szUserData = JsonConvert.SerializeObject(tenantUsers);
            await _cache.SetStringForUserAsync(clientId, userDetails.tenant_id, userId, key, szUserData, cacheTimeOut);
        }
        else
        {
            tenantUsers = JsonConvert.DeserializeObject<List<AssignmentUserListHelper>>(strUserList);
        }

        // Build JSON for Bestilling Grid
        dynamic bestillingGridData = new JObject();

        // Fetching Columns
        dynamic bestillingGridColumns = await GetAssignmentTaskColumnsAsync(userId, true, false, filters.colSelected, "0", filters.isFromPopup);
        bestillingGridData.Add("columns", JArray.FromObject(bestillingGridColumns));

        // Fetching Data

        var orgVersionContent = await _pUtility.GetOrgVersionSpecificContentAsync(userId, _pUtility.GetForecastPeriod(budgetYear, 1));
        List<tco_org_level> lstOrgLevels = orgVersionContent.lstOrgLevel.OrderBy(y => y.org_level).ToList();
        List<tco_org_hierarchy> lstOrgHierarchy = orgVersionContent.lstOrgHierarchy;
        var completehierarchyList = (from p in lstOrgHierarchy
            select new
            {
                tenantID = p.fk_tenant_id,
                orgID_1 = p.org_id_1,
                orgName_1 = p.org_name_1,
                orgLevel_1 = 1,
                orgID_2 = p.org_id_2,
                orgName_2 = p.org_name_2,
                orgLevel_2 = 2,
                orgID_3 = p.org_id_3,
                orgName_3 = p.org_name_3,
                orgLevel_3 = 3,
                orgID_4 = p.org_id_4,
                orgName_4 = p.org_name_4,
                orgLevel_4 = 4,
                orgID_5 = p.org_id_5,
                orgName_5 = p.org_name_5,
                orgLevel_5 = 5
            }).Distinct().ToList();
        int lastOrgLevel = lstOrgLevels.ElementAt(lstOrgLevels.Count - 1).org_level;
        var tagsList = await (from atg in tenantDbContext.tcoActionTags
            where atg.FkTenantId == userDetails.tenant_id
            select new
            {
                key = atg.PkId,
                value = atg.TagDescription
            }).OrderBy(x => x.key).ToListAsync();

        List<string> OrgListForTask = new List<string>();
        string currentOrgName = string.Empty;
        switch (orgIdlevelNo)
        {
            case 1:
                lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_1 == orgId).ToList();
                OrgListForTask = lstOrgHierarchy.Where(x => x.org_id_1 == orgId).Select(x => x.org_id_2).ToList();
                currentOrgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_1 == orgId).org_name_1;
                break;

            case 2:
                lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_2 == orgId).ToList();
                OrgListForTask = lstOrgHierarchy.Where(x => x.org_id_2 == orgId).Select(x => x.org_id_3).ToList();
                currentOrgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_2 == orgId).org_name_2;
                break;

            case 3:
                lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_3 == orgId).ToList();
                OrgListForTask = lstOrgHierarchy.Where(x => x.org_id_3 == orgId).Select(x => x.org_id_4).ToList();
                currentOrgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_3 == orgId).org_name_3;
                break;

            case 4:
                lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_4 == orgId).ToList();
                OrgListForTask = lstOrgHierarchy.Where(x => x.org_id_4 == orgId).Select(x => x.org_id_5).ToList();
                currentOrgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_4 == orgId).org_name_4;
                break;

            case 5:
                lstOrgHierarchy = lstOrgHierarchy.Where(x => x.org_id_5 == orgId).ToList();
                currentOrgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_5 == orgId).org_name_5;
                break;

            default:
                break;
        }

        JArray gridHeader = new JArray();

        // Headers
        dynamic headerTitle = new JObject();
        headerTitle.title = langStringValuesBusinessPlan.FirstOrDefault(v => v.Key == "SUBP_Grid1_title").Value.LangText + " " + orgId + " " + currentOrgName;
        headerTitle.descriptiontip = langStringValuesBusinessPlan.FirstOrDefault(v => v.Key == "SUBP_Grid1_descriptiontip").Value.LangText;
        gridHeader.Add(headerTitle);
        bestillingGridData.Add("header", gridHeader);

        var hierarchyList = (from p in lstOrgHierarchy
            select new
            {
                tenantID = p.fk_tenant_id,
                orgID_1 = p.org_id_1,
                orgName_1 = p.org_name_1,
                orgLevel_1 = 1,
                orgID_2 = p.org_id_2,
                orgName_2 = p.org_name_2,
                orgLevel_2 = 2,
                orgID_3 = p.org_id_3,
                orgName_3 = p.org_name_3,
                orgLevel_3 = 3,
                orgID_4 = p.org_id_4,
                orgName_4 = p.org_name_4,
                orgLevel_4 = 4,
                orgID_5 = p.org_id_5,
                orgName_5 = p.org_name_5,
                orgLevel_5 = 5
            }).Distinct().ToList();

        string categoryOrgLevelAssignedto = string.Empty;
        switch (orgIdlevelNo)
        {
            case 1:
                categoryOrgLevelAssignedto = hierarchyList.FirstOrDefault(x => x.orgID_1 == orgId).orgName_1;
                break;

            case 2:
                categoryOrgLevelAssignedto = hierarchyList.FirstOrDefault(x => x.orgID_2 == orgId).orgName_2;
                break;

            case 3:
                categoryOrgLevelAssignedto = hierarchyList.FirstOrDefault(x => x.orgID_3 == orgId).orgName_3;
                break;

            case 4:
                categoryOrgLevelAssignedto = hierarchyList.FirstOrDefault(x => x.orgID_4 == orgId).orgName_4;
                break;

            case 5:
                categoryOrgLevelAssignedto = hierarchyList.FirstOrDefault(x => x.orgID_5 == orgId).orgName_5;
                break;

            default:
                break;
        }

        var statuses = await (from p in tenantDbContext.tco_progress_status
            where p.fk_tenant_id == userDetails.tenant_id && p.type.ToLower() == "sunit_bplan"
            select p).OrderBy(y => y.status_description).ToListAsync();

        List<string> categoryTypes = new List<string>() { "sunit_bplan", "climate_action" };
        var categories = await (from p in tenantDbContext.tco_category
            where p.fk_tenant_id == userDetails.tenant_id && categoryTypes.Contains(p.type.ToLower()) && p.status == 1
            select p).ToListAsync();

        // assignment data
        var resultSet = string.IsNullOrEmpty(serviceId) || serviceId == "-1" ?
            await (from e in tenantDbContext.tbiassignments
                where (e.parentOrgId == orgId || e.orgId == orgId) && e.orgLevel == orgIdlevelNo && e.budgetYear == budgetYear && e.tenantId == userDetails.tenant_id && !string.IsNullOrEmpty(e.orgId)
                join d in tenantDbContext.tbiassignmentmonthlystatus
                    on e.assignmentId equals d.assignmentId into eGroup
                from d in eGroup.DefaultIfEmpty()
                select new
                {
                    pkId = e.assignmentId,
                    uniqueassignmentId = e.uniqueassignmentId,
                    categoryColor = categoryColor,
                    assignmentName = e.assignmentName,
                    deadline = e.endDate,
                    tags = e.tags,
                    description = e.description,
                    categoryId = e.category,
                    categoryName = string.Empty,
                    orgCreatedat = e.isDistributedtoLowerLevel.HasValue && !string.IsNullOrEmpty(e.distributedParentOrgID) ? e.distributedParentOrgID : e.parentOrgId,
                    orgCreatedLevel = e.isDistributedtoLowerLevel.HasValue && e.distributedParentOrgLevel.HasValue && e.distributedParentOrgLevel.Value != 0 ? e.distributedParentOrgLevel : e.parentOrgLevel,
                    isDelete = true,
                    status = e.status,
                    userAssignedTo = e.userAssignedTo,
                    isReadOnlyAss = e.is_readonly_ass,
                    assignmentOwner = e.assignmentOwner,
                    serviceId = e.serviceId,
                    orgId = e.orgId,
                    orgLevel = e.orgLevel,
                    climateCategory = e.climate_category
                }).ToListAsync()
            :
            await (from e in tenantDbContext.tbiassignments
                where (e.parentOrgId == orgId || e.orgId == orgId) && e.orgLevel == orgIdlevelNo && e.budgetYear == budgetYear && e.tenantId == userDetails.tenant_id && e.serviceId == serviceId && !string.IsNullOrEmpty(e.orgId) && e.serviceId == serviceId
                join d in tenantDbContext.tbiassignmentmonthlystatus
                    on e.assignmentId equals d.assignmentId into eGroup
                from d in eGroup.DefaultIfEmpty()
                select new
                {
                    pkId = e.assignmentId,
                    uniqueassignmentId = e.uniqueassignmentId,
                    categoryColor = categoryColor,
                    assignmentName = e.assignmentName,
                    deadline = e.endDate,
                    tags = e.tags,
                    description = e.description,
                    categoryId = e.category,
                    categoryName = string.Empty,
                    orgCreatedat = e.isDistributedtoLowerLevel.HasValue && !string.IsNullOrEmpty(e.distributedParentOrgID) ? e.distributedParentOrgID : e.parentOrgId,
                    orgCreatedLevel = e.isDistributedtoLowerLevel.HasValue && e.distributedParentOrgLevel.HasValue && e.distributedParentOrgLevel.Value != 0 ? e.distributedParentOrgLevel : e.parentOrgLevel,
                    isDelete = true,
                    status = e.status,
                    userAssignedTo = e.userAssignedTo,
                    isReadOnlyAss = e.is_readonly_ass,
                    assignmentOwner = e.assignmentOwner,
                    serviceId = e.serviceId,
                    orgId = e.orgId,
                    orgLevel = e.orgLevel,
                    climateCategory = e.climate_category
                }).ToListAsync();

        resultSet = (from e in resultSet.Distinct().ToList()
            select new
            {
                pkId = e.pkId,
                uniqueassignmentId = e.uniqueassignmentId,
                categoryColor = e.categoryColor,
                assignmentName = e.assignmentName,
                deadline = e.deadline,
                tags = e.tags,
                description = e.description,
                categoryId = e.categoryId,
                categoryName = e.categoryName,
                orgCreatedat = e.orgCreatedat,
                orgCreatedLevel = e.orgCreatedLevel,
                isDelete = e.orgCreatedat == orgId && e.orgCreatedLevel == orgIdlevelNo,
                status = e.status,
                userAssignedTo = e.userAssignedTo,
                isReadOnlyAss = e.isReadOnlyAss,
                assignmentOwner = e.assignmentOwner,
                serviceId = e.serviceId,
                orgId = e.orgId,
                orgLevel = e.orgLevel,
                climateCategory = e.climateCategory
            }).ToList();

        //task data
        var Taskresult = string.IsNullOrEmpty(serviceId) || serviceId == "-1" ?
            await (from assignment in tenantDbContext.tbiassignments
                join task in tenantDbContext.tbitasks
                    on new { ID = assignment.assignmentId, Orgid = assignment.orgId }
                    equals new { ID = task.assignmentId.Value, Orgid = task.orgId } 
                where assignment.orgId == orgId && assignment.budgetYear == budgetYear && assignment.tenantId == userDetails.tenant_id
                      && task.budgetYear == budgetYear
                select new clsTaskData
                {
                    pkId = task.taskid,
                    catColor = categoryColor,
                    assignedOrgId = assignment.parentOrgId,
                    assignedOrgLevel = (int)assignment.parentOrgLevel,
                    assignmentId = assignment.assignmentId,
                    assignment = assignment.assignmentName,
                    assignmentDescription = assignment.description,
                    measures = task.taskname,
                    status = (int)task.taskstatus,
                    deadline = task.endDate,
                    description = task.description,
                    tags = assignment.tags,
                    filter = assignment.category,
                    userAssignedTo = (int)task.userAssignedTo,
                    uniqueTaskId = task.uniquetaskId,
                    orgCreatedat = task.isDistributedtoLowerLevel.HasValue && !string.IsNullOrEmpty(task.distributedParentOrgID) ? task.distributedParentOrgID : task.orgCreatedAt,
                    orgCreatedLevel = task.isDistributedtoLowerLevel.HasValue && task.distributedParentOrgLevel.HasValue && task.distributedParentOrgLevel.Value != 0 ? task.distributedParentOrgLevel.Value : task.orglevelCreatedAt.Value,
                    serviceId = task.serviceId,
                    orgId = task.orgId,
                    orgLevel = task.orgLevel,
                    climateCategoryId = assignment.climate_category
                }).OrderBy(x => x.assignmentId).ThenBy(x => x.measures).ThenBy(x => x.orgId).ToListAsync()
            :
            await (from assignment in tenantDbContext.tbiassignments
                join task in tenantDbContext.tbitasks
                    on new { ID = assignment.assignmentId, Orgid = assignment.orgId }
                    equals new { ID = task.assignmentId.Value, Orgid = task.orgId } 
                where assignment.orgId == orgId && assignment.budgetYear == budgetYear && assignment.tenantId == userDetails.tenant_id && assignment.serviceId == serviceId
                      && task.budgetYear == budgetYear
                select new clsTaskData
                {
                    pkId = task.taskid,
                    catColor = categoryColor,
                    assignedOrgId = assignment.parentOrgId,
                    assignedOrgLevel = (int)assignment.parentOrgLevel,
                    assignmentId = assignment.assignmentId,
                    assignment = assignment.assignmentName,
                    assignmentDescription = assignment.description,
                    measures = task.taskname,
                    status = (int)task.taskstatus,
                    deadline = task.endDate,
                    description = task.description,
                    tags = assignment.tags,
                    filter = assignment.category,
                    userAssignedTo = (int)task.userAssignedTo,
                    createdDate = task.createdDate,
                    uniqueTaskId = task.uniquetaskId,
                    orgCreatedat = task.isDistributedtoLowerLevel.HasValue && !string.IsNullOrEmpty(task.distributedParentOrgID) ? task.distributedParentOrgID : task.orgCreatedAt,
                    orgCreatedLevel = task.isDistributedtoLowerLevel.HasValue && task.distributedParentOrgLevel.HasValue && task.distributedParentOrgLevel.Value != 0 ? task.distributedParentOrgLevel.Value : task.orglevelCreatedAt.Value,
                    serviceId = task.serviceId,
                    orgId = task.orgId,
                    orgLevel = task.orgLevel,
                    climateCategoryId = assignment.climate_category
                }).OrderBy(x => x.assignmentId).ThenBy(x => x.measures).ThenBy(x => x.orgId).ToListAsync();

        Taskresult = (from p in Taskresult.Distinct().ToList()
            select new clsTaskData
            {
                pkId = p.pkId,
                catColor = p.catColor,
                assignedOrgId = p.assignedOrgId,
                assignedOrgLevel = p.assignedOrgLevel,
                assignmentId = p.assignmentId,
                assignment = p.assignment,
                assignmentDescription = p.assignmentDescription,
                measures = p.measures,
                status = p.status,
                deadline = p.deadline,
                description = p.description,
                tags = p.tags,
                filter = p.filter,
                userAssignedTo = p.userAssignedTo,
                createdDate = p.createdDate,
                filterName = _pUtility.GetCategoryDetails(p.filter, categories, p.climateCategoryId),
                uniqueTaskId = p.uniqueTaskId,
                orgCreatedat = p.orgCreatedat,
                orgCreatedLevel = p.orgCreatedLevel,
                serviceId = p.serviceId,
                orgId = p.orgId,
                orgLevel = p.orgLevel
            }).ToList();

        var statusDescriptions = await tenantDbContext.tco_progress_status.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.type.ToLower() == "sunit_bplan".ToLower()).OrderBy(y => y.status_description).ToListAsync();
        var categoriesDescriptions = await tenantDbContext.tco_category.Where(x => x.fk_tenant_id == userDetails.tenant_id).OrderBy(x => x.description).ToListAsync();

        dynamic arrStatus = new JArray();
        foreach (var s in statusDescriptions)
        {
            dynamic obj = new JObject();
            obj.key = s.status_id;
            obj.value = s.status_description;
            arrStatus.Add(obj);
        }

        bestillingGridData.Add("arrStatus", arrStatus);

        dynamic arrcategories = new JArray();
        foreach (var s in categoriesDescriptions)
        {
            dynamic obj = new JObject();
            obj.key = s.pk_cat_id;
            obj.value = s.description;
            arrcategories.Add(obj);
        }

        bestillingGridData.Add("arrcategories", arrcategories);

        List<AssignmentTaskGridHelper> DataSet = new List<AssignmentTaskGridHelper>();
        AssignmentTaskGridHelper tempData;
        int parrentId = 1;
        int childId = 999;
        List<KeyValuePair> tags = await (from th in tenantDbContext.tcoActionTags
            where th.FkTenantId == userDetails.tenant_id
            select new KeyValuePair { key = th.PkId.ToString(), value = th.TagDescription }).Distinct().ToListAsync();

        List<tmr_period_setup> FPforAssignment = await (from tmr in tenantDbContext.tmr_period_setup
            join tcal in tenantDbContext.tmr_calendar
                on new { a = tmr.fk_tenant_id, b = tmr.forecast_period }
                equals new { a = tcal.fk_tenant_id, b = tcal.forecast_period }
            where tmr.fk_tenant_id == userDetails.tenant_id && tcal.sunitbusiplan_flag
            select tmr).ToListAsync();

        int forcastPeriodForAssign = FPforAssignment.Any() && FPforAssignment.Where(x => x.forecast_period / 100 == budgetYear).Any() ? FPforAssignment.Where(x => x.forecast_period / 100 == budgetYear).Max(y => y.forecast_period) : 0;

        List<tmr_period_setup> FPforTask = await (from tmr in tenantDbContext.tmr_period_setup
            join tcal in tenantDbContext.tmr_calendar
                on new { a = tmr.fk_tenant_id, b = tmr.forecast_period }
                equals new { a = tcal.fk_tenant_id, b = tcal.forecast_period }
            where tmr.fk_tenant_id == userDetails.tenant_id && tcal.sunitbusiplanTask_flag
            select tmr).ToListAsync();

        int forcastPeriodForTask = FPforTask.Any() && FPforTask.Where(x => x.forecast_period / 100 == budgetYear).Any() ? FPforTask.Where(x => x.forecast_period / 100 == budgetYear).Max(y => y.forecast_period) : 0;

        List<tbiassignmentmonthlystatus> mRtbiassignmentmonthlystatus = await tenantDbContext.tbiassignmentmonthlystatus.Where(x => x.tenantId == userDetails.tenant_id && x.forecastPeriod == forcastPeriodForAssign).ToListAsync();// mr status desc

        List<tbi_task_monthly_status> tbitask_monthlystatusList = await tenantDbContext.tbi_task_monthly_status.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.forecast_period == forcastPeriodForTask).ToListAsync();

        List<clsTaskData> AllTaskstForTenant = await tenantDbContext.tbitasks.Where(x => x.tenantId == userDetails.tenant_id && x.budgetYear == budgetYear && OrgListForTask.Contains(x.orgId) && x.orgLevel == orgIdlevelNo + 1).Select(z => new clsTaskData()
        {
            pkId = z.taskid,
            catColor = categoryColor,
            measures = z.taskname,
            status = (int)z.taskstatus,
            deadline = z.endDate,
            description = z.description,
            userAssignedTo = (int)z.userAssignedTo,
            uniqueTaskId = z.uniquetaskId,
            orgCreatedat = z.orgCreatedAt,
            orgCreatedLevel = z.orglevelCreatedAt.Value,
            serviceId = z.serviceId,
            assignmentId = z.assignmentId.Value,
            orgId = z.orgId,
            orgLevel = z.orgLevel
        }).OrderBy(x => x.measures).ThenBy(x => x.orgId).ToListAsync();

        List<Guid> selectedAssignmentPkList = resultSet.Select(x => x.pkId).ToList();
        List<Guid> selectedAssignmentUnQIdList = resultSet.Select(x => x.uniqueassignmentId).ToList();
        List<tbiassignments> assignmentLowerLeveList = await tenantDbContext.tbiassignments.Where(x => x.tenantId == userDetails.tenant_id && x.budgetYear == budgetYear && !selectedAssignmentPkList.Contains(x.assignmentId) && selectedAssignmentUnQIdList.Contains(x.uniqueassignmentId) && OrgListForTask.Contains(x.orgId) && x.orgLevel == orgIdlevelNo + 1).ToListAsync();// get the assignment for the lower level
        // mr task descript
        string principal = string.Empty;
        string unitName = string.Empty;
        bool subHeaderPrinted;
        foreach (var item in resultSet)// loop through for each assignment
        {
            subHeaderPrinted = false;
            var ids = item.tags.Split(',').Select(i => i.ToString()).ToList();
            var catIds = item.categoryId.Split(',').Select(i => i.ToString()).ToList();

            switch (item.orgCreatedLevel.Value)
            {
                case 1:
                    principal = hierarchyList.FirstOrDefault(x => x.orgID_1 == item.orgCreatedat).orgName_1;
                    break;

                case 2:
                    principal = hierarchyList.FirstOrDefault(x => x.orgID_2 == item.orgCreatedat).orgName_2;
                    break;

                case 3:
                    principal = hierarchyList.FirstOrDefault(x => x.orgID_3 == item.orgCreatedat).orgName_3;
                    break;

                case 4:
                    principal = hierarchyList.FirstOrDefault(x => x.orgID_4 == item.orgCreatedat).orgName_4;
                    break;

                case 5:
                    principal = hierarchyList.FirstOrDefault(x => x.orgID_5 == item.orgCreatedat).orgName_5;
                    break;

                default:
                    break;
            }
            switch (item.orgLevel.Value)
            {
                case 1:
                    unitName = item.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_1 == item.orgId).orgName_1;
                    break;

                case 2:
                    unitName = item.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_2 == item.orgId).orgName_2;
                    break;

                case 3:
                    unitName = item.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_3 == item.orgId).orgName_3;
                    break;

                case 4:
                    unitName = item.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_4 == item.orgId).orgName_4;
                    break;

                case 5:
                    unitName = item.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_5 == item.orgId).orgName_5;
                    break;

                default:
                    break;
            }
            int? monthlyReportStatus = mRtbiassignmentmonthlystatus.FirstOrDefault(x => x.assignmentId == item.pkId) != null ? mRtbiassignmentmonthlystatus.FirstOrDefault(x => x.assignmentId == item.pkId).status : -1;
            string statusFromMonthRep = monthlyReportStatus != null && monthlyReportStatus != -1 && statusDescriptions.FirstOrDefault(s => s.status_id == monthlyReportStatus) != null ?
                statusDescriptions.FirstOrDefault(s => s.status_id == monthlyReportStatus).status_description : string.Empty;

            string statusFromAssign = statusDescriptions.FirstOrDefault(s => s.status_id == item.status) == null ? string.Empty :
                statusDescriptions.FirstOrDefault(s => s.status_id == item.status).status_description;
            tempData = new AssignmentTaskGridHelper()
            {
                pkId = item.uniqueassignmentId,
                assignmentId = item.pkId,

                category = categoriesDescriptions.Where(x => catIds.Contains(x.pk_cat_id.ToString())).Select(z => z.description).ToList(),
                tags = tags.Where(x => ids.Contains(x.key)).Any() ? tags.Where(x => ids.Contains(x.key)).Select(z => z.value).ToList() : new List<string>(),
                principal = principal,
                owner = tenantUsers.FirstOrDefault(x => x.userId == item.assignmentOwner) != null ? tenantUsers.FirstOrDefault(x => x.userId == item.assignmentOwner).fName + " " + tenantUsers.FirstOrDefault(x => x.userId == item.assignmentOwner).lName : string.Empty,
                assignmentAndTask = item.assignmentName,
                deadLine = DateTime.Compare(item.deadline, DateTime.UtcNow) > 0 ? item.deadline.ToShortDateString() : item.deadline.ToShortDateString() + "!",
                deadLineExp = DateTime.Compare(item.deadline, DateTime.UtcNow) < 0,
                device = "",
                id = parrentId.ToString(),
                isBold = false,
                parentId = null,
                responsible = tenantUsers.FirstOrDefault(x => x.userId == item.userAssignedTo) != null ? tenantUsers.FirstOrDefault(x => x.userId == item.userAssignedTo).fName + " " + tenantUsers.FirstOrDefault(x => x.userId == item.userAssignedTo).lName : string.Empty,
                status = !string.IsNullOrEmpty(statusFromMonthRep) ? statusFromMonthRep : statusFromAssign,
                assignTasksDesc = item.description,
                statusDesc = mRtbiassignmentmonthlystatus.FirstOrDefault(x => x.assignmentId == item.pkId) != null ? mRtbiassignmentmonthlystatus.FirstOrDefault(x => x.assignmentId == item.pkId).description : string.Empty,
                isDelete = item.orgCreatedat == orgId,
                unitName = unitName,
                isExpand = filters.expandAssignemtIds != null && filters.expandAssignemtIds.Count > 0 && filters.expandAssignemtIds.Contains(item.uniqueassignmentId.ToString()),
                serviceId = !string.IsNullOrEmpty(item.serviceId) && item.serviceId != "-1" && filters.serviceIdList != null && filters.serviceIdList.FirstOrDefault(x => x.key == item.serviceId) != null ? filters.serviceIdList.FirstOrDefault(x => x.key == item.serviceId).value : string.Empty,
            };
            DataSet.Add(tempData);
            // insert task for current org id assignment
            if (Taskresult.Any(x => x.assignmentId == item.pkId))// loop through for each task fora given assignmnet
            {
                if (!subHeaderPrinted)
                {
                    tempData = new AssignmentTaskGridHelper()// empty row for dispaying headr for task
                    {
                        pkId = Guid.Empty,
                        category = new List<string>(),
                        tags = new List<string>(),
                        principal = string.Empty,
                        owner = string.Empty,
                        assignmentAndTask = langStringValuesBusinessPlan["BU_AssignmentTask_gird_sub_title"].LangText + " :",
                        deadLine = langStringValuesBusinessPlan["BU_AssignmentTask_gird_DeadLineDate"].LangText + " :",
                        device = "",
                        id = childId.ToString(),
                        isBold = true,
                        parentId = parrentId.ToString(),
                        responsible = langStringValuesBusinessPlan["BU_AssignmentTask_gird_Resposnsible"].LangText + " :",
                        status = langStringValuesBusinessPlan["BU_AssignmentTask_gird_Status"].LangText + " :",
                        unitName = string.Empty,
                        serviceId = string.Empty,
                    };
                    DataSet.Add(tempData);
                    childId++;
                    subHeaderPrinted = true;
                }

                foreach (var tskItem in Taskresult.Where(x => x.assignmentId == item.pkId).ToList())// loop through for each task fora given assignmnet
                {
                    switch (tskItem.orgLevel)
                    {
                        case 1:
                            principal = tskItem.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_1 == tskItem.orgId).orgName_1;
                            break;

                        case 2:
                            principal = tskItem.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_2 == tskItem.orgId).orgName_2;
                            break;

                        case 3:
                            principal = tskItem.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_3 == tskItem.orgId).orgName_3;
                            break;

                        case 4:
                            principal = tskItem.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_4 == tskItem.orgId).orgName_4;
                            break;

                        case 5:
                            principal = tskItem.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_5 == tskItem.orgId).orgName_5;
                            break;

                        default:
                            break;
                    }
                    int? MonthlytaskStatus = tbitask_monthlystatusList.FirstOrDefault(x => x.fk_task_id == tskItem.pkId) != null ? tbitask_monthlystatusList.FirstOrDefault(x => x.fk_task_id == tskItem.pkId).status : -1;
                    string taskStatusFromMonthRep = MonthlytaskStatus != null && MonthlytaskStatus != -1 && statusDescriptions.FirstOrDefault(s => s.status_id == MonthlytaskStatus) != null ?
                        statusDescriptions.FirstOrDefault(s => s.status_id == MonthlytaskStatus).status_description : string.Empty;

                    string taskStatusFromAssign = statusDescriptions.FirstOrDefault(s => s.status_id == tskItem.status) == null ? string.Empty :
                        statusDescriptions.FirstOrDefault(s => s.status_id == tskItem.status).status_description;
                    tempData = new AssignmentTaskGridHelper()
                    {
                        pkId = tskItem.uniqueTaskId,
                        assignmentId = tskItem.assignmentId,
                        taskId = tskItem.pkId,
                        category = new List<string>(),
                        tags = new List<string>(),
                        principal = string.Empty,
                        owner = string.Empty,
                        assignmentAndTask = tskItem.measures,
                        deadLine = DateTime.Compare(tskItem.deadline.Value, DateTime.UtcNow) > 0 ? tskItem.deadline.Value.ToShortDateString() : tskItem.deadline.Value.ToShortDateString() + "!",
                        deadLineExp = DateTime.Compare(tskItem.deadline.Value, DateTime.UtcNow) < 0,
                        device = "",
                        id = childId.ToString(),
                        isBold = false,
                        parentId = parrentId.ToString(),
                        responsible = tenantUsers.FirstOrDefault(x => x.userId == tskItem.userAssignedTo) != null ? tenantUsers.FirstOrDefault(x => x.userId == tskItem.userAssignedTo).fName + " " + tenantUsers.FirstOrDefault(x => x.userId == tskItem.userAssignedTo).lName : string.Empty,
                        assignTasksDesc = tskItem.description,
                        statusDesc = tbitask_monthlystatusList.FirstOrDefault(x => x.fk_task_id == tskItem.pkId) != null ? tbitask_monthlystatusList.FirstOrDefault(x => x.fk_task_id == tskItem.pkId).description : string.Empty,
                        isDelete = tskItem.orgCreatedat == orgId,
                        unitName = principal,
                        status = !string.IsNullOrEmpty(taskStatusFromMonthRep) ? taskStatusFromMonthRep : taskStatusFromAssign,
                        serviceId = !string.IsNullOrEmpty(tskItem.serviceId) && tskItem.serviceId != "-1" && filters.serviceIdList != null && filters.serviceIdList.FirstOrDefault(x => x.key == tskItem.serviceId) != null ? filters.serviceIdList.FirstOrDefault(x => x.key == tskItem.serviceId).value : string.Empty,
                    };
                    DataSet.Add(tempData);
                    childId++;

                    foreach (var subunittskItem in AllTaskstForTenant.Where(x => x.uniqueTaskId == tskItem.uniqueTaskId).ToList())
                    {
                        int? subunittaskStatus = tbitask_monthlystatusList.FirstOrDefault(x => x.fk_task_id == tskItem.pkId) != null ? tbitask_monthlystatusList.FirstOrDefault(x => x.fk_task_id == tskItem.pkId).status : -1;
                        string taskStatusSubFromMonthRep = subunittaskStatus != null && subunittaskStatus != -1 && statusDescriptions.FirstOrDefault(s => s.status_id == subunittaskStatus) != null ?
                            statusDescriptions.FirstOrDefault(s => s.status_id == subunittaskStatus).status_description : string.Empty;

                        string taskStatusSubFromAssign = statusDescriptions.FirstOrDefault(s => s.status_id == subunittskItem.status) == null ? string.Empty :
                            statusDescriptions.FirstOrDefault(s => s.status_id == subunittskItem.status).status_description;
                        switch (subunittskItem.orgLevel)
                        {
                            case 1:
                                principal = subunittskItem.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_1 == subunittskItem.orgId).orgName_1;
                                break;

                            case 2:
                                principal = subunittskItem.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_2 == subunittskItem.orgId).orgName_2;
                                break;

                            case 3:
                                principal = subunittskItem.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_3 == subunittskItem.orgId).orgName_3;
                                break;

                            case 4:
                                principal = subunittskItem.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_4 == subunittskItem.orgId).orgName_4;
                                break;

                            case 5:
                                principal = subunittskItem.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_5 == subunittskItem.orgId).orgName_5;
                                break;

                            default:
                                break;
                        }

                        tempData = new AssignmentTaskGridHelper()
                        {
                            pkId = subunittskItem.pkId,
                            taskId = subunittskItem.pkId,
                            category = new List<string>(),
                            tags = new List<string>(),
                            principal = string.Empty,
                            owner = string.Empty,
                            assignmentAndTask = subunittskItem.measures,
                            deadLine = DateTime.Compare(subunittskItem.deadline.Value, DateTime.UtcNow) > 0 ? subunittskItem.deadline.Value.ToShortDateString() : subunittskItem.deadline.Value.ToShortDateString() + "!",
                            deadLineExp = DateTime.Compare(subunittskItem.deadline.Value, DateTime.UtcNow) < 0,
                            device = "",
                            id = childId.ToString(),
                            isBold = false,
                            parentId = parrentId.ToString(),
                            responsible = tenantUsers.FirstOrDefault(x => x.userId == subunittskItem.userAssignedTo) != null ? tenantUsers.FirstOrDefault(x => x.userId == subunittskItem.userAssignedTo).fName + " " + tenantUsers.FirstOrDefault(x => x.userId == subunittskItem.userAssignedTo).lName : string.Empty,
                            assignTasksDesc = subunittskItem.description,
                            statusDesc = tbitask_monthlystatusList.FirstOrDefault(x => x.fk_task_id == subunittskItem.pkId) != null ? tbitask_monthlystatusList.FirstOrDefault(x => x.fk_task_id == subunittskItem.pkId).description : string.Empty,
                            isDelete = subunittskItem.orgCreatedat == orgId,
                            unitName = principal,
                            status = !string.IsNullOrEmpty(taskStatusSubFromMonthRep) ? taskStatusSubFromMonthRep : taskStatusSubFromAssign,
                            serviceId = !string.IsNullOrEmpty(subunittskItem.serviceId) && subunittskItem.serviceId != "-1" && filters.serviceIdList != null && filters.serviceIdList.FirstOrDefault(x => x.key == subunittskItem.serviceId) != null ? filters.serviceIdList.FirstOrDefault(x => x.key == subunittskItem.serviceId).value : string.Empty,
                            isDelegatedAssignmentAndTask = true
                        };
                        DataSet.Add(tempData);
                        childId++;
                    }
                }
            }
            if (assignmentLowerLeveList.Any(x => x.uniqueassignmentId == item.uniqueassignmentId))// insert task for the current assignemnt which is delegated to lower level
            {
                foreach (var l in assignmentLowerLeveList.Where(x => x.uniqueassignmentId == item.uniqueassignmentId).ToList())
                {
                    foreach (var ltskItem in AllTaskstForTenant.Where(x => x.assignmentId == l.assignmentId).ToList())// loop through for each task fora given assignmnet
                    {
                        if (!subHeaderPrinted)
                        {
                            tempData = new AssignmentTaskGridHelper()// empty row for dispaying headr for task
                            {
                                pkId = Guid.Empty,
                                category = new List<string>(),
                                tags = new List<string>(),
                                principal = string.Empty,
                                owner = string.Empty,
                                assignmentAndTask = langStringValuesBusinessPlan["BU_AssignmentTask_gird_sub_title"].LangText + " :",
                                deadLine = langStringValuesBusinessPlan["BU_AssignmentTask_gird_DeadLineDate"].LangText + " :",
                                device = "",
                                id = childId.ToString(),
                                isBold = true,
                                parentId = parrentId.ToString(),
                                responsible = langStringValuesBusinessPlan["BU_AssignmentTask_gird_Resposnsible"].LangText + " :",
                                status = langStringValuesBusinessPlan["BU_AssignmentTask_gird_Status"].LangText + " :",
                                unitName = string.Empty,
                                serviceId = string.Empty,
                            };
                            DataSet.Add(tempData);
                            childId++;
                            subHeaderPrinted = true;
                        }

                        if (!DataSet.Select(x => x.taskId).ToList().Any(z => z == ltskItem.pkId))
                        {
                            switch (ltskItem.orgLevel)
                            {
                                case 1:
                                    principal = ltskItem.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_1 == ltskItem.orgId).orgName_1;
                                    break;

                                case 2:
                                    principal = ltskItem.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_2 == ltskItem.orgId).orgName_2;
                                    break;

                                case 3:
                                    principal = ltskItem.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_3 == ltskItem.orgId).orgName_3;
                                    break;

                                case 4:
                                    principal = ltskItem.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_4 == ltskItem.orgId).orgName_4;
                                    break;

                                case 5:
                                    principal = ltskItem.orgId + "-" + hierarchyList.FirstOrDefault(x => x.orgID_5 == ltskItem.orgId).orgName_5;
                                    break;

                                default:
                                    break;
                            }
                            int? MonthlytaskStatus = tbitask_monthlystatusList.FirstOrDefault(x => x.fk_task_id == ltskItem.pkId) != null ? tbitask_monthlystatusList.FirstOrDefault(x => x.fk_task_id == ltskItem.pkId).status : -1;
                            string taskStatusFromMonthRep = MonthlytaskStatus != null && MonthlytaskStatus != -1 && statusDescriptions.FirstOrDefault(s => s.status_id == MonthlytaskStatus) != null ?
                                statusDescriptions.FirstOrDefault(s => s.status_id == MonthlytaskStatus).status_description : string.Empty;

                            string taskStatusFromAssign = statusDescriptions.FirstOrDefault(s => s.status_id == ltskItem.status) == null ? string.Empty :
                                statusDescriptions.FirstOrDefault(s => s.status_id == ltskItem.status).status_description;
                            tempData = new AssignmentTaskGridHelper()
                            {
                                pkId = ltskItem.uniqueTaskId,
                                taskId = ltskItem.pkId,
                                category = new List<string>(),
                                tags = new List<string>(),
                                principal = string.Empty,
                                owner = string.Empty,
                                assignmentAndTask = ltskItem.measures,
                                deadLine = DateTime.Compare(ltskItem.deadline.Value, DateTime.UtcNow) > 0 ? ltskItem.deadline.Value.ToShortDateString() : ltskItem.deadline.Value.ToShortDateString() + "!",
                                deadLineExp = DateTime.Compare(ltskItem.deadline.Value, DateTime.UtcNow) < 0,
                                device = "",
                                id = childId.ToString(),
                                isBold = false,
                                parentId = parrentId.ToString(),
                                responsible = tenantUsers.FirstOrDefault(x => x.userId == ltskItem.userAssignedTo) != null ? tenantUsers.FirstOrDefault(x => x.userId == ltskItem.userAssignedTo).fName + " " + tenantUsers.FirstOrDefault(x => x.userId == ltskItem.userAssignedTo).lName : string.Empty,
                                assignTasksDesc = ltskItem.description,
                                statusDesc = tbitask_monthlystatusList.FirstOrDefault(x => x.fk_task_id == ltskItem.pkId) != null ? tbitask_monthlystatusList.FirstOrDefault(x => x.fk_task_id == ltskItem.pkId).description : string.Empty,
                                isDelete = ltskItem.orgCreatedat == orgId,
                                unitName = principal,
                                status = !string.IsNullOrEmpty(taskStatusFromMonthRep) ? taskStatusFromMonthRep : taskStatusFromAssign,
                                serviceId = !string.IsNullOrEmpty(ltskItem.serviceId) && ltskItem.serviceId != "-1" && filters.serviceIdList != null && filters.serviceIdList.FirstOrDefault(x => x.key == ltskItem.serviceId) != null ? filters.serviceIdList.FirstOrDefault(x => x.key == ltskItem.serviceId).value : string.Empty,
                            };
                            DataSet.Add(tempData);
                            childId++;
                        }
                    }
                }
            }

            parrentId++;
        }
        AssignmentTaskNextLevelDataHelper objHelper = new AssignmentTaskNextLevelDataHelper()
        {
            categories = categories,
            filters = filters,
            mRtbiassignmentmonthlystatus = mRtbiassignmentmonthlystatus,
            orgVersionContent = orgVersionContent,
            statusDescriptions = statusDescriptions,
            statuses = statuses,
            tags = tags,
            tbitask_monthlystatusList = tbitask_monthlystatusList,
            categoriesDescriptions = categoriesDescriptions,
            tenantUsersList = tenantUsers,
        };
        DataSet.AddRange(await GetAssignmentAndTaskUnitDataNextLevelAsync(userId, budgetYear, orgId, orgIdlevelNo, serviceId, objHelper));
        DataSet = filterData(filters, DataSet, langStringValuesBusinessPlan);
        bestillingGridData.Add("data", JArray.FromObject(DataSet));
        return bestillingGridData;
    }



    public async Task<bool> CheckTaskStatusEditabilityAsync(string userId, string taskId, string orgId, string serviceId, int orgLevel)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        var id = Guid.Parse(taskId);
        if (id == Guid.Empty)
            return false;
        var tbitaskData = string.IsNullOrEmpty(serviceId) || serviceId == "-1" ? await tenantDbContext.tbitasks.FirstOrDefaultAsync(x => x.uniquetaskId == id && x.orgId == orgId && x.orgLevel == orgLevel) :
            await tenantDbContext.tbitasks.FirstOrDefaultAsync(x => x.uniquetaskId == id && x.orgId == orgId && x.orgLevel == orgLevel && x.serviceId == serviceId);
        return tbitaskData != null && await tenantDbContext.tbi_task_monthly_status.AnyAsync(x => x.fk_task_id == tbitaskData.taskid && x.fk_tenant_id == userDetails.tenant_id);
    }


    public async Task<bool> CheckAssignmentStatusEditabilityAsync(string userId, string assignmentId, string orgId, string serviceId, int orgLevel)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        var id = Guid.Parse(assignmentId);
        if (id == Guid.Empty)
            return false;
        var tbiassignmentData = string.IsNullOrEmpty(serviceId) || serviceId == "-1" ? await tenantDbContext.tbiassignments.FirstOrDefaultAsync(x => x.uniqueassignmentId == id && x.orgId == orgId && x.orgLevel == orgLevel) :
            await tenantDbContext.tbiassignments.FirstOrDefaultAsync(x => x.uniqueassignmentId == id && x.orgId == orgId && x.orgLevel == orgLevel && x.serviceId == serviceId);
        if (tbiassignmentData == null)
        {
            tbiassignmentData = string.IsNullOrEmpty(serviceId) || serviceId == "-1" ? await tenantDbContext.tbiassignments.FirstOrDefaultAsync(x => x.uniqueassignmentId == id && x.parentOrgId == orgId && x.parentOrgLevel == orgLevel) :
                await tenantDbContext.tbiassignments.FirstOrDefaultAsync(x => x.uniqueassignmentId == id && x.parentOrgId == orgId && x.parentOrgLevel == orgLevel && x.serviceId == serviceId);
        }
        return tbiassignmentData != null && await tenantDbContext.tbiassignmentmonthlystatus.AnyAsync(x => x.assignmentId == tbiassignmentData.assignmentId && x.tenantId == userDetails.tenant_id);
    }



    public List<string> GetOrgIds(JArray orgStructureAssignmentTree, List<string> orgIdsLowerLevel)
    {
        return GetOrgIdsAsync(orgStructureAssignmentTree, orgIdsLowerLevel).GetAwaiter().GetResult();
    }



    public async Task<List<string>> GetOrgIdsAsync(JArray orgStructureAssignmentTree, List<string> orgIdsLowerLevel)
    {
        foreach (var o in orgStructureAssignmentTree)
        {
            orgIdsLowerLevel.Add(Convert.ToString(o["id"]));
            if (((JArray)o["items"]).Any())
            {
                await GetOrgIdsAsync((JArray)o["items"], orgIdsLowerLevel);
            }
        }

        return orgIdsLowerLevel;
    }



    private void SendTasksandAssignmentNotifications(string userId, List<int> userIds, bool isAssignment = true)
    {
        TenantDBContext tenantDbContext = _pUtility.GetTenantDBContext();
        var tenantID = _pUtility.GetUserDetails(userId).tenant_id;
        userIds = userIds.Distinct().ToList();
        foreach (var item in userIds)
        {
            var userData = tenantDbContext.vwUserDetails.FirstOrDefault(x => x.pk_id == item && x.tenant_id == tenantID);
            if (userData != null)
            {
                Guid notId = Guid.NewGuid();
                Uri pagetoredirectUrl = new Uri("../ServiceUnitBusinessPlan/ServiceUnitBusinessPlanView?redirecttoAssignmentTaskPage=true", UriKind.Relative);
                UserData userDetails = _pUtility.GetUserDetails(userData.user_name);
                Dictionary<String, clsLanguageString> notificationStrings =
                    _pUtility.GetLanguageStrings(userDetails.language_preference, userData.user_name, "Notifications");
                _notification.CreateNotification(notId, userData.user_name, userData.tenant_id,
                    isAssignment ? notificationStrings["ntf_assignment_creation"].LangText
                        : notificationStrings["ntf_task_creation"].LangText,
                    pagetoredirectUrl, 24);
            }
        }
    }

    public async Task<dynamic> GetBaseDataforAssignmentId(string userId, string assignmentId, string orgID, int orgLevel, int budgetYear, int forecastPeriod, string serviceId)
    {
        UserData userDetails = await _pUtility.GetUserDetailsAsync(userId);
        TenantDBContext tenantDbContext = await _pUtility.GetTenantDBContextAsync();
        dynamic businessPlanCommonData = new JObject();
        Guid emptyGuid = Guid.Empty;
        string unsDef = string.Empty;
        string faDef = string.Empty;
        string unTargetDef = string.Empty;
        List<Guid> goalsLinked = new List<Guid>();
        List<Guid> targetsLinked = new List<Guid>();
        serviceId = string.IsNullOrEmpty(serviceId) || serviceId == "-1" || serviceId.ToLower() == "all" || serviceId.ToLower() == "alle" ? string.Empty : serviceId;
        List<string> serviceIdStringsForAll = new List<string>() { "-1", "all", "", "0" };
        var assignmentid = Guid.Parse(assignmentId);
        var uniqueAssignmentData = await tenantDbContext.tbiassignments.Where(x => x.uniqueassignmentId == assignmentid && x.tenantId == userDetails.tenant_id && x.budgetYear == budgetYear).ToListAsync();
        var tbiassignmentData = uniqueAssignmentData.FirstOrDefault(x => x.uniqueassignmentId == assignmentid &&
                                                                         x.parentOrgId == orgID && x.parentOrgLevel == orgLevel &&
                                                                         x.orgId == orgID && x.orgLevel == orgLevel && (x.serviceId == serviceId || (string.IsNullOrEmpty(serviceId.Trim()) && serviceIdStringsForAll.Contains(x.serviceId.Trim().ToLower()))));

        var isSelectedOrgLevel = uniqueAssignmentData.FirstOrDefault(x => x.uniqueassignmentId == assignmentid && x.parentOrgId == orgID && x.parentOrgLevel == orgLevel && x.isDistributedtoLowerLevel.Value == false);
        if (tbiassignmentData == null)
        {
            tbiassignmentData = uniqueAssignmentData.FirstOrDefault(x => x.uniqueassignmentId == assignmentid && x.budgetYear == budgetYear && x.orgId == orgID && x.orgLevel == orgLevel && (x.serviceId == serviceId || (string.IsNullOrEmpty(serviceId.Trim()) && serviceIdStringsForAll.Contains(x.serviceId.Trim().ToLower()))));
        }

        List<string> categoryTypes = new List<string>() { "sunit_bplan", "climate_action" };
        var categories = await (from p in tenantDbContext.tco_category
            where p.fk_tenant_id == userDetails.tenant_id && categoryTypes.Contains(p.type.ToLower()) && p.status == 1
            select p).ToListAsync();

        var ActionTags = await (from p in tenantDbContext.tcoActionTags
            where p.FkTenantId == userDetails.tenant_id
            select p).ToListAsync();

        bool showMonthlyReportStatus = await CheckAssignmentStatusEditabilityAsync(userId, assignmentId, orgID, serviceId, orgLevel);

        if (tbiassignmentData != null)
        {
            TimeSpan cacheTimeOut = new TimeSpan(24, 0, 0);
            int clientId = userDetails.client_id;
            string key = (userId + "-" + string.Empty + "-" + -1 + "-" + "tcoUserForAssignments").Trim();

            string strUserList = await _cache.GetStringForUserAsync(clientId, userDetails.tenant_id, userId, key);
            //Apply only user role filters while fetching users(Same way we fetch for popup)
            List<AssignmentUserListHelper> tenantUsers;
            if (String.IsNullOrEmpty(strUserList))
            {
                tenantUsers = await (from t in tenantDbContext.tco_users
                    where (t.IsActive.HasValue && t.IsActive.Value)
                    group new { t } by new { t.pk_id, t.first_name, t.last_name } into g
                    select new AssignmentUserListHelper
                    {
                        userId = g.Key.pk_id,
                        fName = g.Key.first_name,
                        lName = g.Key.last_name
                    }).OrderBy(a => a.fName).ThenBy(b => b.lName).ToListAsync();

                string szUserData = JsonConvert.SerializeObject(tenantUsers);
                await _cache.SetStringForUserAsync(clientId, userDetails.tenant_id, userId, key, szUserData, cacheTimeOut);
            }
            else
            {
                tenantUsers = JsonConvert.DeserializeObject<List<AssignmentUserListHelper>>(strUserList);
            }

            //assignmentName
            businessPlanCommonData.Add("assignmentName", tbiassignmentData.assignmentName);

            // assignment startyear and endyear
            businessPlanCommonData.Add("startYearBP", tbiassignmentData.start_year_bp);
            businessPlanCommonData.Add("endYear", tbiassignmentData.end_year);
            businessPlanCommonData.Add("startYearFP", tbiassignmentData.start_year_fp);

            if (emptyGuid.ToString() != tbiassignmentData.category)
            {
                JArray categoryData = new JArray();
                if (!string.IsNullOrEmpty(tbiassignmentData.category) && tbiassignmentData.category.Split(',').ToList().Any())
                {
                    foreach (var item in tbiassignmentData.category.Split(',').ToList())
                    {
                        dynamic row = new JObject();
                        var guid = Guid.Parse(item);
                        var catData = categories.FirstOrDefault(x => x.pk_cat_id == guid);
                        if (catData != null)
                        {
                            row.key = categories.FirstOrDefault(x => x.pk_cat_id == guid).pk_cat_id;
                            row.value = categories.FirstOrDefault(x => x.pk_cat_id == guid).description;
                            categoryData.Add(row);
                        }
                    }
                }
                businessPlanCommonData.Add("category", categoryData);
            }
            else
            {
                businessPlanCommonData.Add("category", new JArray());
            }

            businessPlanCommonData.Add("categorySet", emptyGuid.ToString() != tbiassignmentData.category ? _pUtility.GetCategoryDetails(tbiassignmentData.category, categories, tbiassignmentData.climate_category) : string.Empty);
            // Get Tags Data for an assignment ID
            var tagsList = new Dictionary<int, string>();
            if (!string.IsNullOrEmpty(tbiassignmentData.tags.ToString()))
            {
                if (tbiassignmentData.tags.Split(',').Count() > 0)
                {
                    foreach (var tag in tbiassignmentData.tags.Split(','))
                    {
                        int tagid = int.Parse(tag);
                        var tagData = ActionTags.FirstOrDefault(x => x.PkId == tagid);
                        if (tagData != null)
                        {
                            tagsList.Add(int.Parse(tagData.PkId.ToString()), tagData.TagDescription);
                        }
                    }
                }
            }

            if (tagsList.Count > 0)
            {
                var tagsData = new JArray();
                foreach (var item in tagsList)
                {
                    dynamic row = new JObject();
                    row.KeyId = item.Key;
                    row.ValueString = item.Value;
                    tagsData.Add(row);
                }
                businessPlanCommonData.Add("tags", tagsData);
            }
            else
            {
                businessPlanCommonData.Add("tags", new JArray());
            }
            businessPlanCommonData.Add("tagSet", !string.IsNullOrEmpty(tbiassignmentData.tags.ToString()) ? _pUtility.GetTagDetails(tbiassignmentData.tags.ToString(), ActionTags) : string.Empty);

            var orgVersionContent = await _pUtility.GetOrgVersionSpecificContentAsync(userId, _pUtility.GetForecastPeriod(budgetYear, 1));
            List<Guid> goalIdsList = new List<Guid>();

            // Get Goals Data
            var goalIds = await tenantDbContext.TbiAssignmentGoal.Where(x => x.fk_assignment_id == tbiassignmentData.assignmentId && x.fk_tenant_id == userDetails.tenant_id).Select(x => x.fk_goal_id).Distinct().ToListAsync();

            var goalsInfo = goalIds.Any() ? await tenantDbContext.tco_goals.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == tbiassignmentData.budgetYear && goalIds.Contains(x.pk_goal_id)).ToListAsync() : new List<tco_goals>();
            goalsLinked = goalsInfo != null && goalsInfo.Any() ? goalsInfo.Select(x => x.pk_goal_id).ToList() : goalsLinked;
            JArray goalData = new JArray();
            string goalDesc = string.Empty;
            goalIdsList = goalIds;
            foreach (var g in goalIds)
            {
                if (goalsInfo != null)
                {
                    var goalInfo = goalsInfo.FirstOrDefault(x => x.pk_goal_id == g && x.fk_tenant_id == userDetails.tenant_id && x.budget_year == tbiassignmentData.budgetYear);
                    if (goalInfo != null && emptyGuid != g)
                    {
                        dynamic row = new JObject();
                        row.KeyId = goalInfo.pk_goal_id;
                        row.ValueString = goalInfo.goal_name;
                        goalData.Add(row);
                        goalDesc = string.IsNullOrEmpty(goalDesc) ? (goalDesc + $"<span>{goalInfo.goal_name}</span>") : (goalDesc + "</br>" + $"<span>{goalInfo.goal_name}</span>");
                    }
                }
            }
            businessPlanCommonData.Add("goals", goalData);

            businessPlanCommonData.Add("goalSet", goalDesc);

            // Get strategies Data
            var strategyIds = await tenantDbContext.TbiAssignmentStrategy.Where(x => x.fk_assignment_id == tbiassignmentData.assignmentId).Select(x => x.fk_strategy_id).Distinct().ToListAsync();
            JArray strategyData = new JArray();
            string strategyDesc = string.Empty;
            foreach (var g in strategyIds)
            {
                var strategyInfo = await tenantDbContext.tfp_strategy_text.FirstOrDefaultAsync(x => x.pk_strategy_id == g && x.fk_tenant_id == userDetails.tenant_id);
                if (strategyInfo != null && g != 0)
                {
                    dynamic row = new JObject();
                    row.KeyId = strategyInfo.pk_strategy_id;
                    row.ValueString = strategyInfo.strategy_name;
                    strategyData.Add(row);
                    strategyDesc = string.IsNullOrEmpty(strategyDesc) ? (strategyDesc + $"<span>{strategyInfo.strategy_name}</span>") : (strategyDesc + "</br>" + $"<span>{strategyInfo.strategy_name}</span>");
                }
            }
            businessPlanCommonData.Add("strategis", strategyData);

            businessPlanCommonData.Add("strategySet", strategyDesc);

            // Get targets Data
            var targetIds = await tenantDbContext.TbiAssignmentTarget.Where(x => x.fk_assignment_id == tbiassignmentData.assignmentId && x.fk_tenant_id == userDetails.tenant_id).Select(x => x.fk_target_id).Distinct().ToListAsync();
            JArray targetData = new JArray();
            string targetDesc = string.Empty;
            /*Display goal,target, focus area and uns goal only if goal is present in delegated level. If user selects only target then check goal linked to that target
            is present in that level or not */
            var targetsInfo = targetIds.Any() ? await tenantDbContext.tco_targets.Where(x => x.fk_tenant_id == userDetails.tenant_id && goalIdsList.Contains(x.fk_goal_id)).ToListAsync() : new List<tco_targets>();
            targetsLinked = targetsInfo != null && targetsInfo.Any() ? targetsInfo.Select(x => x.pk_target_id).ToList() : targetsLinked;
            foreach (var g in targetIds)
            {
                if (targetsInfo != null)
                {
                    var targetInfo = targetsInfo.FirstOrDefault(x => x.pk_target_id == g && x.fk_tenant_id == userDetails.tenant_id);
                    if (targetInfo != null && g != Guid.Empty)
                    {
                        dynamic row = new JObject();
                        row.KeyId = targetInfo.pk_target_id;
                        row.ValueString = targetInfo.target_name;
                        targetData.Add(row);
                        targetDesc = string.IsNullOrEmpty(targetDesc) ? (targetDesc + $"<span>{targetInfo.target_name}</span>") : (targetDesc + "</br>" + $"<span>{targetInfo.target_name}</span>");
                    }
                }
            }
            businessPlanCommonData.Add("targets", targetData);

            businessPlanCommonData.Add("targetSet", targetDesc);

            //assignment description
            if ((tbiassignmentData.isBudgetProposal.HasValue && tbiassignmentData.isBudgetProposal.Value) ||
                uniqueAssignmentData.Any(x => x.isBudgetProposal.HasValue && x.isBudgetProposal.Value))
            {
                businessPlanCommonData.Add("description", await GetBudProposalAssignmentDescriptionsAsync(userId, tbiassignmentData, budgetYear, serviceId));
            }
            else
            {
                businessPlanCommonData.Add("description", await GetDescriptionforAssignmentAsync(userId, assignmentid, orgID, orgLevel, budgetYear));
            }

            //assignmentName startdate
            businessPlanCommonData.Add("startDate", tbiassignmentData.startDate.ToString("dd.MM.yyyy"));

            //assignmentName enddate
            businessPlanCommonData.Add("endDate", tbiassignmentData.endDate.ToString("dd.MM.yyyy"));

            //assignmentName externalreference
            businessPlanCommonData.Add("external_reference", tbiassignmentData.external_reference);

            businessPlanCommonData.Add("serviceList", string.Empty);
            businessPlanCommonData.Add("displaydropdown", false);
            businessPlanCommonData.Add("isEditable", GetEditabilityStatus(tbiassignmentData, isSelectedOrgLevel));
            businessPlanCommonData.Add("isBudprosalAssignment", tbiassignmentData.isBudgetProposal);

            //Show/hide climate category dropdown
            List<string> catList = !string.IsNullOrEmpty(tbiassignmentData.category) ? tbiassignmentData.category.Split(',').ToList() : new List<string>();
            var climateCategoryInfo = categories.FirstOrDefault(x => x.type.ToLower() == "sunit_bplan" && x.flag == 1);
            Guid climateCategoryId = climateCategoryInfo != null ? climateCategoryInfo.pk_cat_id : Guid.Empty;
            businessPlanCommonData.Add("showClimateCategorySection", (tbiassignmentData.climate_category != Guid.Empty || catList.Contains(climateCategoryId.ToString())));
            businessPlanCommonData.Add("climateCategory", tbiassignmentData.climate_category);

            businessPlanCommonData.user = tbiassignmentData.userAssignedTo == null ? -1 : tbiassignmentData.userAssignedTo.Value;
            businessPlanCommonData.assignmentOwner = tbiassignmentData.assignmentOwner == 0 ? -1 : tbiassignmentData.assignmentOwner;
            businessPlanCommonData.assignmentOwnerName = tbiassignmentData.assignmentOwner == 0 || tenantUsers.FirstOrDefault(x => x.userId == tbiassignmentData.assignmentOwner) == null ? string.Empty :
                tenantUsers.FirstOrDefault(x => x.userId == tbiassignmentData.assignmentOwner).fName + " " +
                tenantUsers.FirstOrDefault(x => x.userId == tbiassignmentData.assignmentOwner).lName;

            businessPlanCommonData.status = showMonthlyReportStatus ? await GetMonthlyReportAssignmentStatusAsync(userDetails.tenant_id, tbiassignmentData.assignmentId.ToString(), forecastPeriod) : tbiassignmentData.status;
            businessPlanCommonData.assignmentrefid = tbiassignmentData.assignmentrefid;
            businessPlanCommonData.includedInMonthlyReport = tbiassignmentData.includedInMonthlyReport;
            var assignmentOrgDetails = uniqueAssignmentData.FirstOrDefault(x => x.assignmentId == tbiassignmentData.assignmentId && x.tenantId == userDetails.tenant_id);
            var orgCreatedDetails = assignmentOrgDetails.isDistributedtoLowerLevel.HasValue && assignmentOrgDetails.isDistributedtoLowerLevel.Value ?
                new Dictionary<string, int> { { assignmentOrgDetails.distributedParentOrgID, assignmentOrgDetails.distributedParentOrgLevel.Value } }
                : new Dictionary<string, int> { { assignmentOrgDetails.parentOrgId, assignmentOrgDetails.parentOrgLevel.Value } };
            var orgName = string.Empty;
            if (orgCreatedDetails.Any())
            {
                List<tco_org_hierarchy> lstOrgHierarchy = orgVersionContent.lstOrgHierarchy;
                switch (orgCreatedDetails.FirstOrDefault().Value)
                {
                    case 1:
                        orgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_1 == orgCreatedDetails.FirstOrDefault().Key).org_name_1;
                        break;

                    case 2:
                        orgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_2 == orgCreatedDetails.FirstOrDefault().Key).org_name_2;
                        break;

                    case 3:
                        orgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_3 == orgCreatedDetails.FirstOrDefault().Key).org_name_3;
                        break;

                    case 4:
                        orgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_4 == orgCreatedDetails.FirstOrDefault().Key).org_name_4;
                        break;

                    case 5:
                        orgName = lstOrgHierarchy.FirstOrDefault(x => x.org_id_5 == orgCreatedDetails.FirstOrDefault().Key).org_name_5;
                        break;

                    default:
                        break;
                }
            }
            businessPlanCommonData.OrgCreatedAt = orgName;
            var assignmentData = uniqueAssignmentData.Where(x => x.uniqueassignmentId == assignmentid && !x.isDistributedtoLowerLevel.Value).ToList();
            if (assignmentData.Any())
            {
                var parenthID = assignmentData.Where(x => x.orgLevel == assignmentData.Min(Z => Z.orgLevel)).FirstOrDefault();
                businessPlanCommonData.Add("parentassignmentendDate", parenthID == null ? string.Empty : parenthID.endDate.ToString("dd.MM.yyyy"));
            }
            else
            {
                businessPlanCommonData.Add("parentassignmentendDate", string.Empty);
            }

            //To display focus area and UNS goals tagged to goal/target
            if (goalsLinked.Count > 0 || targetsLinked.Count > 0)
            {
                List<TargetDropdownHelper> faOrUnsgoalIdsData = new List<TargetDropdownHelper>();
                List<string> unTargetInfoFromData = new List<string>();
                goalsLinked = goalsLinked.Count > 0 ? goalsLinked : await tenantDbContext.tco_targets.Where(x => x.fk_tenant_id == userDetails.tenant_id && targetsLinked.Contains(x.pk_target_id)).Select(x => x.fk_goal_id).ToListAsync();

                faOrUnsgoalIdsData = await (from a in tenantDbContext.tco_goals
                    where a.fk_tenant_id == userDetails.tenant_id && goalsLinked.Contains(a.pk_goal_id)
                    select new TargetDropdownHelper
                    {
                        unsdGoals = a.unsd_goals,
                        focusAreaIds = a.focus_area
                    }).Distinct().ToListAsync();

                var infoFromTargets = await (from a in tenantDbContext.tco_targets
                    where a.fk_tenant_id == userDetails.tenant_id && targetsLinked.Contains(a.pk_target_id)
                    select new
                    {
                        unsdTargets = a.unsd_target
                    }).Distinct().AsNoTracking().ToListAsync();

                unTargetInfoFromData = infoFromTargets.Select(x => x.unsdTargets).ToList();

                List<string> unsListFromData = new List<string>();
                List<string> focusAreaListFromData = new List<string>();
                List<string> unTargetListFromData = new List<string>();

                foreach (var u in faOrUnsgoalIdsData)
                {
                    List<string> unsIds = new List<string>();
                    unsIds = u.unsdGoals.Split(',').Distinct().ToList();
                    unsListFromData.AddRange(unsIds);

                    List<string> faIds = new List<string>();
                    faIds = u.focusAreaIds.Split(',').Distinct().ToList();
                    focusAreaListFromData.AddRange(faIds);
                }

                foreach (var u in unTargetInfoFromData)
                {
                    List<string> unsIds = new List<string>();
                    unsIds = u.Split(',').Distinct().ToList();
                    unTargetListFromData.AddRange(unsIds);
                }

                var focusAreaInfo = await tenantDbContext.tco_focusarea.Where(x => x.fk_tenant_id == userDetails.tenant_id).ToListAsync();
                var unsgoalsInfo = await tenantDbContext.gco_un_susdev_goals.Select(x => new { x.pk_goal_id, x.goal_short_name, x.goal_name }).ToListAsync();
                var unTargetInfo = await tenantDbContext.gco_un_susdev_targets.Select(x => new { x.pk_target_id, x.target_short_name, x.target_name }).ToListAsync();

                foreach (var uns in unsListFromData.Distinct())
                {
                    if (!string.IsNullOrEmpty(uns))
                    {
                        string unGoalName = unsgoalsInfo.Where(x => x.pk_goal_id == uns).Select(x => x.goal_name).FirstOrDefault();
                        unsDef = unsDef + "<span class='bp-blue-tag cursor' title='" + unGoalName + "'>" + unGoalName.Substring(0, 5) + unsgoalsInfo.Where(x => x.pk_goal_id == uns).Select(x => x.goal_short_name).FirstOrDefault() + "</span>";
                    }
                }

                foreach (var fa in focusAreaListFromData.Distinct())
                {
                    if (!string.IsNullOrEmpty(fa))
                    {
                        faDef = faDef + "<span class=\"bp-red-tag\">" + focusAreaInfo.Where(x => x.pk_id.ToString() == fa).Select(x => x.focusarea_description).FirstOrDefault() + "</span>";
                    }
                }

                foreach (var unTarget in unTargetListFromData.Distinct())
                {
                    if (!string.IsNullOrEmpty(unTarget))
                    {
                        unTargetDef = unTargetDef + "<span class=\"bp-blue-tag cursor\" title=\"" + unTargetInfo.Where(x => x.pk_target_id == unTarget).Select(x => x.target_name).FirstOrDefault() + "\">" + unTargetInfo.Where(x => x.pk_target_id == unTarget).Select(x => x.target_short_name).FirstOrDefault() + "</span>";
                    }
                }
            }
            businessPlanCommonData.unsGoalsDescription = unsDef;
            businessPlanCommonData.focusAreaDescription = faDef;
            businessPlanCommonData.unsTargetDescription = unTargetDef;
            businessPlanCommonData.pkAssignmentId = tbiassignmentData.assignmentId;
            businessPlanCommonData.serviceLevel = tbiassignmentData.serviceLevel;
            businessPlanCommonData.Add("guidance_text", await GetAssignmentGuidanceTextDescriptionsAsync(userId, tbiassignmentData, budgetYear));
            List<int> contributorList = await GetAssignmentCordinatorListAsync(userId, tbiassignmentData, budgetYear);
            businessPlanCommonData.contributor_userList = JArray.FromObject(contributorList);
            businessPlanCommonData.contributor_userList_previous = JArray.FromObject(contributorList);

            return businessPlanCommonData;
        }
        else
        {
            return businessPlanCommonData;
        }
    }

    public bool CheckTaskStatusEditability(string userId, string taskId, string orgId, string serviceId, int orgLevel)
    {
        return CheckTaskStatusEditabilityAsync(userId, taskId, orgId, serviceId, orgLevel).GetAwaiter().GetResult();
    }


    public bool CheckAssignmentStatusEditability(string userId, string assignmentId, string orgId, string serviceId, int orgLevel)
    {
        return CheckAssignmentStatusEditabilityAsync(userId, assignmentId, orgId, serviceId, orgLevel).GetAwaiter().GetResult();

    }

}