#pragma warning disable CS8625
#pragma warning disable CS8629
#pragma warning disable CS8600
#pragma warning disable CS8601
#pragma warning disable CS8602
#pragma warning disable CS8603
#pragma warning disable CS8604

using Aspose.Cells;
using EntityFrameworkExtras.EFCore;
using Framsikt.BL.Core;
using Framsikt.BL.Helpers;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Data;
using System.Globalization;
using System.Text;

namespace Framsikt.BL
{
    public class DimensionsImport : IDimensionsImport
    {
        private readonly IUtility _utility;
        private readonly IBackendRequest _backendJob;
        private const int _batchSize = 25000;
        private IAdminOrgStructure _adminOrgStructure;
        private readonly IAdminAccount _adminAccount;
        private IAzureBlobHelper _azureBlobHelper;

        public enum JobTypeEnum
        {
            MainProjectImport,
            ProjectImport,
            FreeDimOneImport,
            FreeDimTwoImport,
            FreeDimThreeImport,
            FreeDimFourImport,
            AccountImport,
            DepartmentImport,
            FunctionImport
        }

        private readonly Dictionary<string, string> _columnMapping = new Dictionary<string, string>()
        {
            { "project_code", "project_code"},
            { "main_project_code", "main_project_code"},
            { "project_name", "project_name"},
            { "year_from", "year_from"},
            { "year_to", "year_to"},
            { "year_from_str", "year_from_str"},
            { "year_to_str", "year_to_str"},
            { "free_dim_1", "free_dim_1"},
            { "free_dim_2", "free_dim_2"},
            { "free_dim_3", "free_dim_3"},
            { "free_dim_4", "free_dim_4"},
            { "free_dim_1_desc", "free_dim_1_desc"},
            { "free_dim_2_desc", "free_dim_2_desc"},
            { "free_dim_3_desc", "free_dim_3_desc"},
            { "free_dim_4_desc", "free_dim_4_desc"},
            { "DimensionType", "dimension_type"},
            { "TenantId", "fk_tenant_id"},
            { "UserId", "user_id"},
            { "status", "status"},
            { "vat_rate", "vat_rate"},
            { "vat_refund", "vat_refund"},
            { "prog_code", "prog_code"},
            { "inv_status", "inv_status"},
            { "fk_department_code", "fk_department_code"},
            { "fk_Function_code", "fk_Function_code"},
            { "start_year", "start_year"},
            { "original_finish_year", "original_finish_year"},
            { "completion_date", "completion_date"},
            { "account_code", "account_code"},
            { "account_name", "account_name"},
            { "kostra_account_code", "kostra_account_code"},
            { "account_year_from_str", "account_year_from_str"},
            { "account_year_from", "account_year_from"},
            { "account_year_To_str", "account_year_To_str"},
            { "account_year_To", "account_year_To"},
            { "account_isActive", "account_isActive"},
            { "department_code", "department_code"},
            { "department_name", "department_name"},
            { "department_status", "department_status"},
            { "function_code", "function_code"},
            { "function_name", "function_name" },
            { "kostra_function_code", "kostra_function_code" },
            { "function_status", "function_status" },
            { "job_Id", "job_Id"}
        };

        public DimensionsImport(IUtility util, IBackendRequest bejob, IAdminOrgStructure adminOrgStructure, IAdminAccount adminAccount, IAzureBlobHelper azureBlobHelper)
        {
            _utility = util;
            _backendJob = bejob;
            _adminOrgStructure = adminOrgStructure;
            _adminAccount = adminAccount;
            _azureBlobHelper = azureBlobHelper;
        }

        public async Task<dynamic> GetDimensionsTypes(string userId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            dynamic dimensionsList = new JArray();
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "DimensionsImport");
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();

            dynamic objDimension = new JObject();
            objDimension.Key = "-1";
            objDimension.Value = langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_SelectOption").Value.LangText;
            dimensionsList.Add(objDimension);

            IEnumerable<freedimDefinition> freeDims = await _utility.GetFreeDimDefinitionsBasedOnPageIdAsync(userId, string.Empty);
            List<freedimDefinition> freeDimColumns = freeDims.ToList();
            List<string> freeDimKeys = freeDimColumns.Select(x => x.freeDimColumn).ToList();

            foreach (var item in Enum.GetValues(typeof(DimensionEnum)))
            {
                if ((int)item >= ((int)DimensionEnum.FreeDimOne) && (int)item <= ((int)DimensionEnum.FreeDimFour) && !freeDimKeys.Contains("free_dim_" + ((int)item - 1).ToString()))
                {
                    continue;
                }

                objDimension = new JObject();
                objDimension.Key = ((int)item).ToString();

                if ((int)item >= ((int)DimensionEnum.FreeDimOne) && (int)item <= ((int)DimensionEnum.FreeDimFour))
                {
                    var tfdd = await tenantDbContext.tmd_free_dim_definition.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                        && x.free_dim_column == "free_dim_" + ((int)item - 1).ToString());
                    objDimension.Value = tfdd != null ? tfdd.header : string.Empty;
                    dimensionsList.Add(objDimension);
                    continue;
                }
                //added for story 76715
                if ((int)item == ((int)DimensionEnum.Account))
                {
                    objDimension.Value = langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_Account").Value.LangText;
                }
                else if ((int)item == ((int)DimensionEnum.Department))
                {
                    objDimension.Value = langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_Department").Value.LangText;
                }
                else if ((int)item == (int)DimensionEnum.Function)
                {
                    objDimension.Value = langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_Function").Value.LangText;
                }
                else
                {
                    objDimension.Value = (((int)item) == ((int)DimensionEnum.Projects)) ? langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_Project").Value.LangText :
                                                                                          langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_MainProject").Value.LangText;
                }

                objDimension.displayCheckbox = (((int)item) == ((int)DimensionEnum.Projects));
                dimensionsList.Add(objDimension);
            }

            return SortDimensionTypeList(dimensionsList);// sort the list #87332
        }

        private dynamic SortDimensionTypeList(dynamic dimensionsList)
        {
            List<int> defulatIdSortOrder = new List<int>() { 6, 7, 8, 1, 2, 3, 4, 5, 0 };//Account,Department,Function,Project,Free dims,Main project
            List<KeyValueIntHelper> DTypeList = dimensionsList.ToObject<List<KeyValueIntHelper>>();
            List<KeyValueIntHelper> formattedList = new List<KeyValueIntHelper>();
            foreach (int item in defulatIdSortOrder)
            {
                var currentData = DTypeList.FirstOrDefault(x => x.Key == item);
                if (currentData != null)
                {
                    formattedList.Add(currentData);
                }
            }
            return formattedList;
        }

        public async Task<ColumnInfo> GetDimensionsColumnInfo(string userId, int dimensionType, bool forImport, bool isExport, bool isFlags)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            string textToPrepend = "DimensionsImport_";
            Dictionary<string, clsLanguageString> langStringValues = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "DimensionsImport");

            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            Dictionary<string, string> colNames = await GetColumnNamesForDimensionsImport(userId, dimensionType, forImport, isExport);

            bool functionAvailable = showFunction(userId);
            if (functionAvailable)
            {
                colNames.Remove("fk_department_code");
            }
            else
            {
                colNames.Remove("fk_Function_code");
            }

            ColumnInfo columnInfo = new ColumnInfo();
            foreach (var colName in colNames)
            {
                string key = string.Concat(textToPrepend, colName.Key);

                if (dimensionType == (int)DimensionEnum.Projects)
                {
                    columnInfo.Fields.Add(colName.Key);
                    switch (colName.Key.ToLower())
                    {
                        case "project_code":
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_ProjectCode").Value.LangText);
                            break;

                        case "project_name":
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_ProjectName").Value.LangText);
                            break;

                        case "main_project_code":
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_MainProjectCode").Value.LangText);
                            break;

                        case "vat_rate":
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_VatRate").Value.LangText);
                            break;

                        case "vat_refund":
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_VatRefund").Value.LangText);
                            break;

                        case "prog_code":
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_ProgramCode").Value.LangText);
                            break;

                        case "status":
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_departmentStatus").Value.LangText);
                            break;

                        default:
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == key).Value.LangText);
                            break;
                    }
                    columnInfo.DataTypes.Add(colName.Value);
                }
                if (dimensionType == (int)DimensionEnum.MainProjects)
                {
                    columnInfo.Fields.Add(colName.Key);
                    columnInfo.Titles.Add(colName.Key.ToLower() == "project_code".ToLower() ? langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_MainProjectCode").Value.LangText :
                                          colName.Key.ToLower() == "project_name".ToLower() ? langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_MainProjectName").Value.LangText
                                                                                           : langStringValues.FirstOrDefault(v => v.Key == key).Value.LangText);
                    columnInfo.DataTypes.Add(colName.Value);
                }
                if (dimensionType == (int)DimensionEnum.Account)
                {
                    columnInfo.Fields.Add(colName.Key);
                    switch (colName.Key.ToLower())
                    {
                        case "account_code":
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_AccountCode").Value.LangText);
                            break;

                        case "account_name":
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_accountName").Value.LangText);
                            break;

                        case "kostra_account_code":
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_kostraAccountCode").Value.LangText);
                            break;

                        case "account_isActive":
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_account_isActive").Value.LangText);
                            break;

                        default:
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == key).Value.LangText);
                            break;
                    }
                    columnInfo.DataTypes.Add(colName.Value);
                }
                if (dimensionType == (int)DimensionEnum.Department)
                {
                    columnInfo.Fields.Add(colName.Key);
                    switch (colName.Key.ToLower())
                    {
                        case "department_code":
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_departmentCode").Value.LangText);
                            break;

                        case "department_name":
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_departmentName").Value.LangText);
                            break;

                        case "department_status":
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_departmentStatus").Value.LangText);
                            break;

                        case "year_from":
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_department_year_from").Value.LangText);
                            break;

                        case "year_to":
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_department_year_to").Value.LangText);
                            break;

                        case "year_from_str":
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_department_year_from_str").Value.LangText);
                            break;

                        case "year_to_str":
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_department_year_to_str").Value.LangText);
                            break;

                        default:
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == key).Value.LangText);
                            break;
                    }
                    columnInfo.DataTypes.Add(colName.Value);
                }
                if (dimensionType == (int)DimensionEnum.Function)
                {
                    columnInfo.Fields.Add(colName.Key);
                    switch (colName.Key.ToLower())
                    {
                        case "function_code":
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_FunctionCode").Value.LangText);
                            break;

                        case "function_name":
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_FunctionName").Value.LangText);
                            break;

                        case "year_from":
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_function_year_from").Value.LangText);
                            break;

                        case "year_to":
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_function_year_to").Value.LangText);
                            break;

                        case "year_from_str":
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_function_year_from_str").Value.LangText);
                            break;

                        case "year_to_str":
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == "DimensionsImport_function_year_to_str").Value.LangText);
                            break;

                        default:
                            columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == key).Value.LangText);
                            break;
                    }
                    columnInfo.DataTypes.Add(colName.Value);
                }
                if (dimensionType >= (int)DimensionEnum.FreeDimOne && dimensionType <= (int)DimensionEnum.FreeDimFour)
                {
                    columnInfo.Fields.Add(colName.Key);
                    var tfdd = await tenantDbContext.tmd_free_dim_definition.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id
                                                                                        && x.free_dim_column == colName.Key);
                    if (tfdd != null)
                    {
                        columnInfo.Titles.Add(tfdd.header);
                    }
                    else
                    {
                        columnInfo.Titles.Add(langStringValues.FirstOrDefault(v => v.Key == key).Value.LangText);
                    }

                    columnInfo.DataTypes.Add(colName.Value);
                }
            }
            if (isFlags)
            {
                for (int i = 0; i < 4; i++)
                {
                    KeyValuesBoolData kvb = new KeyValuesBoolData();
                    kvb.Key = "free_dim_" + (i + 1);
                    kvb.Value = dimensionType == (i + 2);
                    columnInfo.Flags.Add(kvb);
                }
            }
            return columnInfo;
        }

        private bool showFunction(string userId)
        {
            //Get tenant configuration for investment
            string paramValue = _utility.GetParameterValue(userId, "FINPLAN_INVESTMENT_LEVEL");
            bool showFunction = false;
            if (!string.IsNullOrEmpty(paramValue))
            {
                var paramValue_split = paramValue.Split('_').ToList();
                switch (paramValue_split[0].ToUpper())
                {
                    case "SERVICE":
                        showFunction = true;
                        break;

                    default:
                        showFunction = false;
                        break;
                }
            }
            return showFunction;
        }

        public async Task ImportDimensionsExcelToStagingTable(string userId, Workbook wb, int dimensionType)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            tenantDbContext.Database.SetCommandTimeout(500);
            CultureInfo ci = CultureInfoFactory.CreateCulture(userDetails.language_preference);
            wb.Settings.CultureInfo = ci;
            Worksheet ws = wb.Worksheets[0];

            ExportTableOptions opt = new ExportTableOptions
            {
                ExportColumnName = true,
                FormatStrategy = CellValueFormatStrategy.None,
                ExportAsString = true
            };

            //Copy numeric columns to corresponding string columns
            int colCount = ws.Cells.MaxDataColumn;
            Dictionary<string, string> colDefs = await GetColumnNamesForDimensionsImport(userId, dimensionType, true);
            bool functionAvailable = showFunction(userId);
            if (dimensionType == (int)DimensionEnum.MainProjects)
            {
                if (functionAvailable)
                {
                    colDefs.Remove("fk_department_code");
                }
                else
                {
                    colDefs.Remove("fk_Function_code");
                }
            }
            List<string> colNames = colDefs.Select(x => x.Key).ToList();

            if (dimensionType == (int)DimensionEnum.Projects || dimensionType == (int)DimensionEnum.MainProjects || dimensionType == (int)DimensionEnum.Account || dimensionType == (int)DimensionEnum.Function ||
                dimensionType == (int)DimensionEnum.Department || (dimensionType >= (int)DimensionEnum.FreeDimOne && dimensionType <= (int)DimensionEnum.FreeDimFour))
            {
                if (dimensionType == (int)DimensionEnum.Projects || dimensionType == (int)DimensionEnum.MainProjects)
                {
                    Cells cells = ws.Cells;

                    int colNoSrc = colNames.IndexOf("year_from_str");
                    int colNoDst = colNames.IndexOf("year_from");
                    ws.Cells.CopyColumn(cells, colNoSrc, colNoDst);

                    colNoSrc = colNames.IndexOf("year_to_str");
                    colNoDst = colNames.IndexOf("year_to");
                    ws.Cells.CopyColumn(cells, colNoSrc, colNoDst);
                }
                if (dimensionType == (int)DimensionEnum.Account)
                {
                    Cells cells = ws.Cells;

                    int colNoSrc = colNames.IndexOf("account_year_from_str");
                    int colNoDst = colNames.IndexOf("account_year_from");
                    ws.Cells.CopyColumn(cells, colNoSrc, colNoDst);

                    colNoSrc = colNames.IndexOf("account_year_To_str");
                    colNoDst = colNames.IndexOf("account_year_To");
                    ws.Cells.CopyColumn(cells, colNoSrc, colNoDst);
                }
                if (dimensionType == (int)DimensionEnum.Function || dimensionType == (int)DimensionEnum.Department)
                {
                    Cells cells = ws.Cells;

                    int colNoSrc = colNames.IndexOf("year_from_str");
                    int colNoDst = colNames.IndexOf("year_from");
                    ws.Cells.CopyColumn(cells, colNoSrc, colNoDst);

                    colNoSrc = colNames.IndexOf("year_to_str");
                    colNoDst = colNames.IndexOf("year_to");
                    ws.Cells.CopyColumn(cells, colNoSrc, colNoDst);
                }
                UserTrackedJobs jobType = GetCurrentJobType(dimensionType);

                _utility.DeleteCompletedJobs(userId, jobType);

                for (int i = 0; i < colNames.Count; i++)
                {
                    ws.Cells[0, i].Value = colNames[i];
                }
                if (dimensionType == (int)DimensionEnum.MainProjects)
                {
                    int colNoStatus = colNames.IndexOf("inv_status");
                    int colNoStartYear = colNames.IndexOf("start_year");
                    int colNoOriginalFinishYear = colNames.IndexOf("original_finish_year");
                    int colNoCompletionDate = colNames.IndexOf("completion_date");
                    int colNoDepFun = -1;
                    if (functionAvailable)
                    {
                        colNoDepFun = colNames.IndexOf("fk_Function_code");
                    }
                    else
                    {
                        colNoDepFun = colNames.IndexOf("fk_department_code");
                    }

                    for (int i = 0; i < ws.Cells.MaxDataRow + 1; i++)
                    {
                        if (colNoStatus != -1)
                        {
                            if (ws.Cells[i, colNoStatus].Value == null)
                            {
                                ws.Cells[i, colNoStatus].Value = string.Empty;
                            }
                            else
                            {
                                ws.Cells[i, colNoStatus].Value = ws.Cells[i, colNoStatus].DisplayStringValue;
                            }
                        }
                        if (colNoDepFun != -1)
                        {
                            if (ws.Cells[i, colNoDepFun].Value == null)
                            {
                                ws.Cells[i, colNoDepFun].Value = string.Empty;
                            }
                            else
                            {
                                ws.Cells[i, colNoDepFun].Value = ws.Cells[i, colNoDepFun].DisplayStringValue;
                            }
                        }
                        if (colNoStartYear != -1)
                        {
                            if (ws.Cells[i, colNoStartYear].Value == null)
                            {
                                ws.Cells[i, colNoStartYear].Value = string.Empty;
                            }
                            else
                            {
                                ws.Cells[i, colNoStartYear].Value = ws.Cells[i, colNoStartYear].DisplayStringValue;
                            }
                        }
                        if (colNoOriginalFinishYear != -1)
                        {
                            if (ws.Cells[i, colNoOriginalFinishYear].Value == null)
                            {
                                ws.Cells[i, colNoOriginalFinishYear].Value = string.Empty;
                            }
                            else
                            {
                                ws.Cells[i, colNoOriginalFinishYear].Value = ws.Cells[i, colNoOriginalFinishYear].DisplayStringValue;
                            }
                        }
                        if (colNoCompletionDate != -1)
                        {
                            if (ws.Cells[i, colNoCompletionDate].Value == null)
                            {
                                ws.Cells[i, colNoCompletionDate].Value = string.Empty;
                            }
                            else
                            {
                                ws.Cells[i, colNoCompletionDate].Value = ws.Cells[i, colNoCompletionDate].DisplayStringValue;
                            }
                        }
                    }
                }
                //Determine number of rows to import
                //From the last row, move up until the account code is not blank
                //This gives the number of rows to be imported
                int rowsToImport = ws.Cells.MaxDataRow;
                for (int i = rowsToImport; i > 0; i--)
                {
                    Cell c = ws.Cells[i, 2];
                    if (string.IsNullOrEmpty(c.Value?.ToString()))
                    {
                        continue;
                    }

                    rowsToImport = i;
                    break;
                }

                ApplyColumnFormat(wb, userId, colDefs);
                var mainTable = ws.Cells.ExportDataTableAsString(0, 0, rowsToImport + 1, ws.Cells.MaxDataColumn + 1, true);
                mainTable.AcceptChanges();
                List<DataTable> splitTables = SplitTable(mainTable, _batchSize);
                foreach (var tbl in splitTables)
                {
                    DataSet result = new DataSet();
                    result.Tables.Add(tbl);

                    UserData userInfo = _utility.GetUserDetails(userId);

                    //Format the data set for import
                    DataColumn tenantIdCol = new DataColumn("TenantId")
                    {
                        DataType = typeof(int),
                        DefaultValue = userInfo.tenant_id
                    };
                    tbl.Columns.Add(tenantIdCol);

                    DataColumn userIdCol = new DataColumn("UserId")
                    {
                        DataType = typeof(int),
                        DefaultValue = userInfo.pk_id
                    };
                    tbl.Columns.Add(userIdCol);

                    DataColumn dimensionTypeCol = new DataColumn("DimensionType")
                    {
                        DataType = typeof(int),
                        DefaultValue = dimensionType
                    };
                    tbl.Columns.Add(dimensionTypeCol);

                    DataColumn jobIdCol = new DataColumn("job_Id")
                    {
                        DataType = typeof(long),
                        DefaultValue = null
                    };
                    tbl.Columns.Add(jobIdCol);

                    //Import the data into the staging table
                    using (SqlBulkCopy bulkCopy = new SqlBulkCopy(_utility.GetTenantDbConnection(userDetails.tenant_id)) { DestinationTableName = "dbo.tbu_stage_dimensions_import", BulkCopyTimeout = 600 })
                    {
                        foreach (string colName in colNames)
                        {
                            bulkCopy.ColumnMappings.Add(colName, _columnMapping[colName]);
                        }

                        bulkCopy.ColumnMappings.Add("TenantId", _columnMapping["TenantId"]);
                        bulkCopy.ColumnMappings.Add("UserId", _columnMapping["UserId"]);
                        bulkCopy.ColumnMappings.Add("DimensionType", _columnMapping["DimensionType"]);
                        bulkCopy.ColumnMappings.Add("job_Id", _columnMapping["job_Id"]);
                        if (dimensionType == (int)DimensionEnum.MainProjects)
                        {
                            bulkCopy.ColumnMappings.Add("project_code", _columnMapping["main_project_code"]);
                        }

                        if (colNames.FirstOrDefault(x => x == "status") == null)
                        {
                            DataColumn status = new DataColumn("status")
                            {
                                DataType = typeof(int),
                                DefaultValue = 1
                            };
                            tbl.Columns.Add(status);
                            bulkCopy.ColumnMappings.Add("status", _columnMapping["status"]);
                        }

                        var outputTable = CleanInputData(tbl, dimensionType);

                        bulkCopy.WriteToServer(outputTable);
                    }
                }
                //Validate data
                await ValidateDimensionsImport(userId, dimensionType);
            }
        }

        private static UserTrackedJobs GetCurrentJobType(int dimensionType)
        {
            //Reset the workflow state by deleting any completed jobs
            switch (dimensionType)
            {
                case 0: return UserTrackedJobs.DimensionsImportMainProject;//main project
                case 1: return UserTrackedJobs.DimensionsImportProject;
                case 2: return UserTrackedJobs.DimensionsImportFreeDimOne;
                case 3: return UserTrackedJobs.DimensionsImportFreeDimTwo;
                case 4: return UserTrackedJobs.DimensionsImportFreeDimThree;
                case 5: return UserTrackedJobs.DimensionsImportFreeDimFour;
                case 6: return UserTrackedJobs.DimensionsImportAccount;
                case 7: return UserTrackedJobs.DimensionsImportDepartment;
                case 8: return UserTrackedJobs.DimensionsImportFunction;
                default: return UserTrackedJobs.DimensionsImportMainProject;
            }
        }

        private static string GetCurrentJobTypeName(int dimensionType)
        {
            //Reset the workflow state by deleting any completed jobs
            switch (dimensionType)
            {
                case 0: return UserTrackedJobs.DimensionsImportMainProject.ToString();//main project
                case 1: return UserTrackedJobs.DimensionsImportProject.ToString();
                case 2: return UserTrackedJobs.DimensionsImportFreeDimOne.ToString();
                case 3: return UserTrackedJobs.DimensionsImportFreeDimTwo.ToString();
                case 4: return UserTrackedJobs.DimensionsImportFreeDimThree.ToString();
                case 5: return UserTrackedJobs.DimensionsImportFreeDimFour.ToString();
                case 6: return UserTrackedJobs.DimensionsImportAccount.ToString();
                case 7: return UserTrackedJobs.DimensionsImportDepartment.ToString();
                case 8: return UserTrackedJobs.DimensionsImportFunction.ToString();
                default: return UserTrackedJobs.DimensionsImportMainProject.ToString();
            }
        }

        public async Task DeleteStaged(string userId, int dimensionType, long jobId)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            tenantDbContext.Database.SetCommandTimeout(600);
            UserData userData = await _utility.GetUserDetailsAsync(userId);

            if (jobId == -1)
            {
                await tenantDbContext.tbu_stage_dimensions_import.Where(x => x.user_id == userData.pk_id && x.fk_tenant_id == userData.tenant_id && x.dimension_type == dimensionType).DeleteFromQueryAsync();
            }
            else
            {
                await tenantDbContext.tbu_stage_dimensions_import.Where(x => x.fk_tenant_id == userData.tenant_id &&
                                                                    x.job_Id == jobId).DeleteFromQueryAsync();
                var getStatus = await tenantDbContext.TcoJobStatus.FirstOrDefaultAsync(x => x.PkId == jobId && x.TenantId == userData.tenant_id);
                string blobUrl = getStatus.request_data_blob_url;
                await tenantDbContext.TcoJobStatus.Where(x => x.PkId == jobId && x.TenantId == userData.tenant_id).DeleteFromQueryAsync();

                await deleteTemplateFromBlob(blobUrl);
            }
            UserTrackedJobs currentJobType = GetCurrentJobType(dimensionType);
            await _utility.DeleteCompletedJobsAsync(userId, currentJobType);
        }

        private async Task deleteTemplateFromBlob(string URL)
        {
            await _azureBlobHelper.DeleteBlobAsync(StorageAccount.AppStorage, BlobContainers.TcoJobsRequestObjectData, URL);
        }

        public async Task<DimensionsImportData> GetDimensionsImportByError(string userId, int dimensionType, long jobId)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            UserData userData = await _utility.GetUserDetailsAsync(userId);
            if (jobId == -1)
            {
                List<tbu_stage_dimensions_import> dimensionsDataSet =
               await  tenantDbContext.tbu_stage_dimensions_import.Where(x => x.user_id == userData.pk_id &&
                                                                x.fk_tenant_id == userData.tenant_id &&
                                                                x.dimension_type == dimensionType)
                                                                .OrderByDescending(y => y.error_count)
                                                                .ToListAsync();
                int totalCount = await tenantDbContext.tbu_stage_dimensions_import.Where(x => x.user_id == userData.pk_id &&
                                                        x.fk_tenant_id == userData.tenant_id &&
                                                        x.dimension_type == dimensionType).CountAsync();

                var ret = new DimensionsImportData()
                {
                    DimensionsList = dimensionsDataSet,
                    TotalCount = totalCount
                };
                return ret;
            }
            else
            {
                List<tbu_stage_dimensions_import> dimensionsDataSet =
               await tenantDbContext.tbu_stage_dimensions_import.Where(x => x.job_Id == jobId &&
                                                               x.fk_tenant_id == userData.tenant_id &&
                                                               x.dimension_type == dimensionType)
                                                               .OrderByDescending(y => y.error_count)
                                                               .ToListAsync();
                int totalCount = await tenantDbContext.tbu_stage_dimensions_import.Where(x => x.job_Id == jobId &&
                                                        x.fk_tenant_id == userData.tenant_id &&
                                                        x.dimension_type == dimensionType).CountAsync();

                var ret = new DimensionsImportData()
                {
                    DimensionsList = dimensionsDataSet,
                    TotalCount = totalCount
                };
                return ret;
            }
        }

        public async Task<ImportInfo> GetDimensionsImportInfo(string userId, int dimensionType, long jobId)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            UserData userData = await _utility.GetUserDetailsAsync(userId);
            if (jobId == -1)
            {
                int? failedCount = await tenantDbContext.tbu_stage_dimensions_import.Where(x => x.user_id == userData.pk_id &&
                                                                                  x.fk_tenant_id == userData.tenant_id &&
                                                                                  x.dimension_type == dimensionType &&
                                                                                  x.error_count > 0).SumAsync(y => (int?)y.error_count);
                int? totalRows = await tenantDbContext.tbu_stage_dimensions_import.CountAsync(x => x.user_id == userData.pk_id &&
                                                                                      x.fk_tenant_id == userData.tenant_id &&
                                                                                      x.dimension_type == dimensionType);
                ImportInfo info = new ImportInfo
                {
                    FailedCount = failedCount == null ? 0 : (int)failedCount,
                    TotalRows = totalRows == null ? 0 : (int)totalRows,
                };

                return info;
            }
            else
            {
                int? failedCount = await tenantDbContext.tbu_stage_dimensions_import.Where(x => x.job_Id == jobId &&
                                                                                      x.fk_tenant_id == userData.tenant_id &&
                                                                                      x.dimension_type == dimensionType &&
                                                                                      x.error_count > 0).SumAsync(y => (int?)y.error_count);
                int? totalRows = await tenantDbContext.tbu_stage_dimensions_import.CountAsync(x => x.job_Id == jobId &&
                                                                                      x.fk_tenant_id == userData.tenant_id &&
                                                                                      x.dimension_type == dimensionType);
                ImportInfo info = new ImportInfo
                {
                    FailedCount = failedCount == null ? 0 : (int)failedCount,
                    TotalRows = totalRows == null ? 0 : (int)totalRows,
                };

                return info;
            }
        }

        public async Task UpdateImportedDimensionsData(string userId, int dimensionType, long jobId, IEnumerable<tbu_stage_dimensions_import> data)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            UserData userData = await _utility.GetUserDetailsAsync(userId);

            if ((dimensionType == ((int)DimensionEnum.Projects)) || dimensionType == ((int)DimensionEnum.MainProjects) || dimensionType == ((int)DimensionEnum.Account) ||
               (dimensionType >= (int)DimensionEnum.FreeDimOne && dimensionType <= (int)DimensionEnum.FreeDimFour) || dimensionType == ((int)DimensionEnum.Department) || dimensionType == (int)DimensionEnum.Function)
            {
                List<tbu_stage_dimensions_import> dimensionsDataSet;
                if (jobId == -1)
                {
                    dimensionsDataSet = await tenantDbContext.tbu_stage_dimensions_import.Where(x => x.user_id == userData.pk_id &&
                                                                                                                             x.fk_tenant_id == userData.tenant_id &&
                                                                                                                             x.dimension_type == dimensionType).ToListAsync();
                }
                else
                {
                    dimensionsDataSet = await tenantDbContext.tbu_stage_dimensions_import.Where(x => x.job_Id == jobId &&
                                                                                                             x.fk_tenant_id == userData.tenant_id &&
                                                                                                             x.dimension_type == dimensionType).ToListAsync();
                }
                foreach (var dimensionInfo in data)
                {
                    tbu_stage_dimensions_import dbDimensions = dimensionsDataSet.FirstOrDefault(x => x.pk_id == dimensionInfo.pk_id);
                    if (dbDimensions != null)
                    {
                         tenantDbContext.Entry(dbDimensions).CurrentValues.SetValues(dimensionInfo);

                        int yearfrom = 0, yearto = 0;
                        switch (dimensionType)
                        {
                            case (int)DimensionEnum.Projects:
                                yearfrom = 0; yearto = 0;
                                dbDimensions.year_from = int.TryParse(dimensionInfo.year_from_str, out yearfrom) ? yearfrom : 0;
                                dbDimensions.year_to = int.TryParse(dimensionInfo.year_to_str, out yearto) ? yearto : 0;
                                break;

                            case (int)DimensionEnum.MainProjects:
                                yearfrom = 0; yearto = 0;
                                dbDimensions.year_from = int.TryParse(dimensionInfo.year_from_str, out yearfrom) ? yearfrom : 0;
                                dbDimensions.year_to = int.TryParse(dimensionInfo.year_to_str, out yearto) ? yearto : 0;
                                dbDimensions.main_project_code = dimensionInfo.project_code;
                                break;

                            case (int)DimensionEnum.Account:
                                yearfrom = 0; yearto = 0;
                                dbDimensions.account_year_from = int.TryParse(dimensionInfo.account_year_from_str, out yearfrom) ? yearfrom : 0;
                                dbDimensions.account_year_To = int.TryParse(dimensionInfo.account_year_To_str, out yearto) ? yearto : 0;
                                break;

                            case (int)DimensionEnum.Department:
                                dbDimensions.year_from = int.TryParse(dimensionInfo.year_from_str, out int deptYearFrom) ? deptYearFrom : 0;
                                dbDimensions.year_to = int.TryParse(dimensionInfo.year_to_str, out int deptYearTo) ? deptYearTo : 0;
                                break;

                            case (int)DimensionEnum.Function:
                                dbDimensions.year_from = int.TryParse(dimensionInfo.year_from_str, out int funcYearFrom) ? funcYearFrom : 0;
                                dbDimensions.year_to = int.TryParse(dimensionInfo.year_to_str, out int funcYearTo) ? funcYearTo : 0;
                                break;

                            case (int)DimensionEnum.FreeDimOne:
                                dbDimensions.free_dim_1 = !string.IsNullOrEmpty(dimensionInfo.free_dim_1) ? dimensionInfo.free_dim_1 : string.Empty;
                                dbDimensions.free_dim_1_desc = dimensionInfo.free_dim_1_desc;
                                break;

                            case (int)DimensionEnum.FreeDimTwo:
                                dbDimensions.free_dim_2 = !string.IsNullOrEmpty(dimensionInfo.free_dim_2) ? dimensionInfo.free_dim_2 : string.Empty;
                                dbDimensions.free_dim_2_desc = dimensionInfo.free_dim_2_desc;
                                break;

                            case (int)DimensionEnum.FreeDimThree:
                                dbDimensions.free_dim_3 = !string.IsNullOrEmpty(dimensionInfo.free_dim_3) ? dimensionInfo.free_dim_2 : string.Empty;
                                dbDimensions.free_dim_3_desc = dimensionInfo.free_dim_3_desc;
                                break;

                            case (int)DimensionEnum.FreeDimFour:
                                dbDimensions.free_dim_4 = !string.IsNullOrEmpty(dimensionInfo.free_dim_4) ? dimensionInfo.free_dim_4 : string.Empty;
                                dbDimensions.free_dim_4_desc = dimensionInfo.free_dim_4_desc;
                                break;
                        }
                    }
                }

               await  tenantDbContext.SaveChangesAsync();
                await ValidateDimensionsImport(userId, dimensionType, jobId);
            }
        }

        public async Task<TcoJobStatus> GetDimensionsJobProgress(string userId, int dimensionType, long jobId)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            UserTrackedJobs jobType = GetCurrentJobType(dimensionType);
            TcoJobStatus status;
            if (jobId == -1)
            {
                status = await tenantDbContext.TcoJobStatus.FirstOrDefaultAsync(x => x.JobType == jobType.ToString() &&
                                                                                      x.UserId == userDetails.pk_id &&
                                                                                      x.TenantId == userDetails.tenant_id);
            }
            else
            {
                status = await tenantDbContext.TcoJobStatus.FirstOrDefaultAsync(x => x.JobType == jobType.ToString() &&
                                                                                         x.PkId == jobId &&
                                                                                         x.TenantId == userDetails.tenant_id);
            }
            return status;
        }

        public async Task<DataTable> GetDimensionsDataForExport(string userId, int dimensionType, bool exportExistingData = false)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            tenantDbContext.Database.SetCommandTimeout(300);
            UserData userData = await _utility.GetUserDetailsAsync(userId);
            List<tbu_stage_dimensions_import> dimensionsDataList = new List<tbu_stage_dimensions_import>();

            if (exportExistingData)
            {
                if (dimensionType == ((int)DimensionEnum.Projects))
                {
                    var existingSet = await tenantDbContext.tco_projects.Where(x => x.fk_tenant_id == userData.tenant_id && x.active == 1).OrderBy(x => x.pk_project_code).ToListAsync();
                    dimensionsDataList = existingSet.Select(x => new tbu_stage_dimensions_import
                    {
                        project_code = x.pk_project_code,
                        project_name = x.project_name,
                        year_from_str = x.date_from.Year.ToString(),
                        year_to_str = x.date_to.Year.ToString(),
                        main_project_code = x.fk_main_project_code,
                        vat_rate = x.vat_rate,
                        vat_refund = x.vat_refund,
                        prog_code = x.fk_prog_code,
                        status = x.active.ToString()
                    }).ToList();
                }
                if (dimensionType == ((int)DimensionEnum.MainProjects))
                {
                    var existingSet = await (from d in tenantDbContext.tco_main_projects
                                       join b in tenantDbContext.tco_main_project_setup on new { x = d.fk_tenant_id, y = d.pk_main_project_code }
                                                                                    equals new { x = b.fk_tenant_id, y = b.pk_main_project_code } into leftMproj
                                       from lMproj in leftMproj.DefaultIfEmpty()
                                       where d.fk_tenant_id == userData.tenant_id && d.status == 1
                                       select new clsMainProjectHeleper_upload
                                       {
                                           newMainProjectCode = d.pk_main_project_code,
                                           mainProjectName = d.main_project_name,
                                           mainProjectFromDate = d.budget_year_from.Year.ToString(),
                                           mainProjectToDate = d.budget_year_to.Year.ToString(),
                                           mainProjectStatus = d.status.ToString(),
                                           invStatus = d.inv_status.ToString(),
                                           departmentCode = d.fk_department_code,
                                           functionCode = d.fk_Function_code,
                                           estimatedQuater = d.completion_date.Value.ToString(),
                                           startYear = lMproj == null ? string.Empty : lMproj.start_year.ToString(),
                                           originalFinish = lMproj == null ? string.Empty : lMproj.original_finish_year.ToString()
                                       }).ToListAsync();

                    dimensionsDataList = existingSet.Select(x => new tbu_stage_dimensions_import
                    {
                        project_code = x.newMainProjectCode,
                        project_name = x.mainProjectName,
                        year_from_str = x.mainProjectFromDate,
                        year_to_str = x.mainProjectToDate,
                        status = x.mainProjectStatus,
                        inv_status = x.invStatus,
                        start_year = x.startYear,
                        completion_date = !string.IsNullOrEmpty(x.estimatedQuater) ? DateTime.Parse(x.estimatedQuater).ToShortDateString() : string.Empty,
                        fk_department_code = x.departmentCode,
                        fk_Function_code = x.functionCode,
                        original_finish_year = x.originalFinish
                    }).ToList();
                }
                if (dimensionType >= ((int)DimensionEnum.FreeDimOne) && dimensionType <= ((int)DimensionEnum.FreeDimFour))
                {
                    string freeDimType = "free_dim_" + (dimensionType - 1).ToString();
                    var existFreeDims = await tenantDbContext.tco_free_dim_values.Where(x => x.fk_tenant_id == userData.tenant_id && x.status == 1 && x.free_dim_column == freeDimType).OrderBy(x => x.free_dim_code).ToListAsync();
                    dimensionsDataList = existFreeDims.Select(x => new tbu_stage_dimensions_import
                    {
                        free_dim_1 = (dimensionType - 1 == 1) ? x.free_dim_code : string.Empty,
                        free_dim_1_desc = (dimensionType - 1 == 1) ? x.description : string.Empty,
                        free_dim_2 = (dimensionType - 1 == 2) ? x.free_dim_code : string.Empty,
                        free_dim_2_desc = (dimensionType - 1 == 2) ? x.description : string.Empty,
                        free_dim_3 = (dimensionType - 1 == 3) ? x.free_dim_code : string.Empty,
                        free_dim_3_desc = (dimensionType - 1 == 3) ? x.description : string.Empty,
                        free_dim_4 = (dimensionType - 1 == 4) ? x.free_dim_code : string.Empty,
                        free_dim_4_desc = (dimensionType - 1 == 4) ? x.description : string.Empty,
                    }).ToList();
                }
                if (dimensionType == ((int)DimensionEnum.Account))
                {
                    var existingSet = await tenantDbContext.tco_accounts.Where(x => x.pk_tenant_id == userData.tenant_id).OrderBy(x => x.pk_account_code).ToListAsync();
                    dimensionsDataList = existingSet.Select(x => new tbu_stage_dimensions_import
                    {
                        account_code = x.pk_account_code,
                        account_name = x.display_name,
                        account_isActive = (x.isActive) ? 1 : 0,
                        account_year_from_str = x.dateFrom.Year.ToString(),
                        account_year_To_str = x.dateTo.Year.ToString(),
                        kostra_account_code = x.fk_kostra_account_code
                    }).ToList();
                }
                if (dimensionType == (int)DimensionEnum.Function)
                {
                    var existingSet = await tenantDbContext.tco_functions.Where(x => x.pk_tenant_id == userData.tenant_id).OrderBy(x => x.pk_Function_code).ToListAsync();
                    dimensionsDataList = existingSet.Select(x => new tbu_stage_dimensions_import
                    {
                        function_code = x.pk_Function_code,
                        function_name = x.display_name,
                        year_from_str = x.dateFrom.Year.ToString(),
                        year_to_str = x.dateTo.Year.ToString(),
                        kostra_function_code = x.fk_kostra_function_code,
                        function_status = x.isActive ? 1 : 0
                    }).ToList();
                }
                if (dimensionType == ((int)DimensionEnum.Department))
                {
                    var existingSet = await tenantDbContext.tco_departments.Where(x => x.fk_tenant_id == userData.tenant_id).OrderBy(x => x.pk_department_code).ToListAsync();
                    dimensionsDataList = existingSet.Select(x => new tbu_stage_dimensions_import
                    {
                        department_code = x.pk_department_code,
                        department_name = x.department_name,
                        department_status = x.status,

                        year_from_str = x.year_from.ToString(),
                        year_to_str = x.year_to.ToString()
                    }).ToList();
                }
            }
            else
            {
                dimensionsDataList = await tenantDbContext.tbu_stage_dimensions_import.Where(x => x.user_id == userData.pk_id &&
                                                               x.fk_tenant_id == userData.tenant_id &&
                                                               x.dimension_type == dimensionType)
                                                                .OrderBy(y => y.pk_id)
                                                                .ToListAsync();
            }

            if (dimensionsDataList.Any() || !exportExistingData)
            {
                if (dimensionType == ((int)DimensionEnum.Projects) || dimensionType == ((int)DimensionEnum.MainProjects) || dimensionType == ((int)DimensionEnum.Account) || dimensionType == (int)DimensionEnum.Function ||
                    dimensionType == ((int)DimensionEnum.Department) || (dimensionType >= ((int)DimensionEnum.FreeDimOne) && dimensionType <= ((int)DimensionEnum.FreeDimFour)))
                {
                    DataTable table = _utility.ConvertToDataTable(dimensionsDataList);
                    var colNames = await GetColumnNamesForDimensionsImport(userId, dimensionType);
                    List<string> columns = colNames.Select(x => x.Key).ToList();
                    bool functionAvailable = showFunction(userId);
                    if (functionAvailable)
                    {
                        columns.Remove("fk_department_code");
                    }
                    else
                    {
                        columns.Remove("fk_Function_code");
                    }
                    List<DataColumn> toBeDeleted = new List<DataColumn>();
                    //Remove unwanted columns
                    foreach (DataColumn col in table.Columns)
                    {
                        if (!columns.Contains(col.ColumnName))
                        {
                            toBeDeleted.Add(col);
                        }
                    }

                    foreach (DataColumn colDel in toBeDeleted)
                    {
                        table.Columns.Remove(colDel);
                    }
                    //re-order  columns as per columns collection
                    int i = 0;
                    foreach (string col in columns)
                    {
                        table.Columns[col].SetOrdinal(i);
                        i++;
                    }
                    table = CleanInputData(table, dimensionType, true);
                    return table;
                }
                else
                {
                    return new DataTable();
                }
            }
            return new DataTable();
        }

        public async Task WriteToDimensionsImportQueue(string userId, int dimensionType, bool insertIntoMainProject, long jobId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            bool isRequestByJobId;
            long jobIdToconsider;

            var jobType = GetCurrentJobTypeName(dimensionType);
            if (jobId == -1)
            {
                List<TcoJobStatus> jobs = await tenantDbContext.TcoJobStatus.Where(x => x.UserId == userDetails.pk_id
                                                                                    && x.TenantId == userDetails.tenant_id
                                                                                    && x.JobType == jobType).ToListAsync();
                if (jobs.Count > 0)
                {
                    //clean up any existing rows. There can be only one job at a time
                     tenantDbContext.TcoJobStatus.RemoveRange(jobs);
                }

                TcoJobStatus jobStatus = new TcoJobStatus
                {
                    JobType = jobType,
                    StartTime = DateTime.UtcNow,
                    TotalSteps = 100,
                    StepsCompleted = 0,
                    UserId = userDetails.pk_id,
                    TenantId = userDetails.tenant_id
                };

                await tenantDbContext.TcoJobStatus.AddAsync(jobStatus);
                await tenantDbContext.SaveChangesAsync();
                isRequestByJobId = false;
                jobIdToconsider = jobStatus.PkId;
            }
            else
            {
                jobIdToconsider = jobId;
                var jobStatToUpdate = await tenantDbContext.TcoJobStatus.FirstOrDefaultAsync(x => x.PkId == jobId && x.TenantId == userDetails.tenant_id);
                var stagingData = await tenantDbContext.tbu_stage_dimensions_import.Where(x => x.job_Id == jobId && x.fk_tenant_id == userDetails.tenant_id).ToListAsync();
                if (jobStatToUpdate != null)
                {
                    jobStatToUpdate.StartTime = DateTime.UtcNow;
                    jobStatToUpdate.TotalSteps = stagingData.Count;
                    jobStatToUpdate.jobStatus = StatusEnumData.InProgress.ToString();
                    jobStatToUpdate.EndTime = null;
                    tenantDbContext.Entry(jobStatToUpdate).State = EntityState.Modified;
                    await tenantDbContext.SaveChangesAsync();
                }

                var reqObject = await getDetailsFromBlob(jobStatToUpdate.request_data_blob_url);
                DimensionsReqBlobHelper input = new DimensionsReqBlobHelper()
                {
                    dimensionsType = jobType,
                    dimensionsTypeValue = (JobTypeEnum)dimensionType,
                    NumberOfRows = stagingData.Count,
                    request_data_blob_url = jobStatToUpdate.request_data_blob_url
                };
                await InserJobReqDataToBlob(input);
                isRequestByJobId = true;
            }

            dynamic dimensionsImportRequest = new JObject();
            dimensionsImportRequest.Add("UserId", userId);
            dimensionsImportRequest.Add("TenantId", userDetails.tenant_id);
            dimensionsImportRequest.Add("JobId", jobIdToconsider);//update
            dimensionsImportRequest.Add("DimensionType", dimensionType);
            dimensionsImportRequest.Add("InsertIntoMainProject", dimensionType == ((int)DimensionEnum.Projects) ? insertIntoMainProject : false);
            dimensionsImportRequest.Add("isRequestByJobId", isRequestByJobId);

            string strdimensionsImportRequest = JsonConvert.SerializeObject(dimensionsImportRequest);
            _backendJob.QueueMessage(userDetails, null, QueueName.dimensionsimportqueue, strdimensionsImportRequest);
        }

        private async Task InserJobReqDataToBlob(DimensionsReqBlobHelper input)
        {
            await _azureBlobHelper.UploadTextBlobAsync(StorageAccount.AppStorage, BlobContainers.TcoJobsRequestObjectData, input.request_data_blob_url, JsonConvert.SerializeObject(input));
        }

        private async Task<DimensionsReqBlobHelper> getDetailsFromBlob(string blobURL)
        {
            dynamic text;
            
            using (MemoryStream mStream = new MemoryStream())
            {
                await _azureBlobHelper.GetFileAsStreamAsync(StorageAccount.AppStorage, BlobContainers.TcoJobsRequestObjectData, blobURL, mStream);
                text = Encoding.UTF8.GetString(mStream.ToArray());
            }
            return JsonConvert.DeserializeObject<DimensionsReqBlobHelper>(text);
        }

        public async Task ImportDimensionsFromStaging(string userId, int dimensionType, int jobId, bool insertIntoMainProject, bool isRequestByJobId)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            tenantDbContext.Database.SetCommandTimeout(3500);
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            try
            {
                List<tbu_stage_dimensions_import> recordstoProcess = isRequestByJobId ?await tenantDbContext.tbu_stage_dimensions_import.Where(x => x.job_Id == jobId && x.dimension_type == dimensionType && x.fk_tenant_id == userDetails.tenant_id).ToListAsync() :
                    await tenantDbContext.tbu_stage_dimensions_import.Where(x => x.user_id == userDetails.pk_id && x.dimension_type == dimensionType && x.fk_tenant_id == userDetails.tenant_id).ToListAsync();

                TcoJobStatus statusStartTime = await tenantDbContext.TcoJobStatus.FirstOrDefaultAsync(x => x.PkId == jobId);
                if (statusStartTime != null)
                {
                    statusStartTime.StartTime = DateTime.UtcNow;
                    statusStartTime.TotalSteps = recordstoProcess.Count;
                    statusStartTime.jobStatus = (isRequestByJobId) ? StatusEnumData.InProgress.ToString() : string.Empty;
                    statusStartTime.EndTime = null;
                    tenantDbContext.Entry(statusStartTime).State = EntityState.Modified;
                   await  tenantDbContext.SaveChangesAsync();
                }

                if ((dimensionType == (int)DimensionEnum.Projects) || (dimensionType == (int)DimensionEnum.MainProjects))
                {
                    if (dimensionType == (int)DimensionEnum.Projects)
                    {
                        int stepcount = 1;
                        foreach (var item in recordstoProcess)
                        {
                            if (insertIntoMainProject)
                            {
                                await ProcessMainProjects(userId, (int)DimensionEnum.MainProjects, item, true);
                            }
                            await ProcessProjects(userId, (int)DimensionEnum.Projects, item, insertIntoMainProject);

                            var statusStepsCompleted = await tenantDbContext.TcoJobStatus.FirstOrDefaultAsync(x => x.PkId == jobId);
                            if (statusStepsCompleted != null)
                            {
                                statusStepsCompleted.StepsCompleted = stepcount;
                                tenantDbContext.Entry(statusStepsCompleted).State = EntityState.Modified;
                                await tenantDbContext.SaveChangesAsync();
                            }
                            stepcount++;
                        }
                    }

                    if (dimensionType == (int)DimensionEnum.MainProjects)
                    {
                        if (recordstoProcess.Any())
                        {
                            await ProcessMainProjects(userId, (int)DimensionEnum.MainProjects, recordstoProcess.FirstOrDefault(), false);
                            var statusStepsCompleted = await tenantDbContext.TcoJobStatus.FirstOrDefaultAsync(x => x.PkId == jobId);
                            if (statusStepsCompleted != null)
                            {
                                statusStepsCompleted.StepsCompleted = recordstoProcess.Count();
                                tenantDbContext.Entry(statusStepsCompleted).State = EntityState.Modified;
                               await  tenantDbContext.SaveChangesAsync();
                            }
                        }
                    }
                }
                else if (dimensionType >= (int)DimensionEnum.FreeDimOne && dimensionType <= (int)DimensionEnum.FreeDimFour)
                {
                    int stepcount = 1;
                    foreach (var item in recordstoProcess)
                    {
                        await ProcessFreeDimData(userId, item, dimensionType - 1);
                        var statusStepsCompleted = await tenantDbContext.TcoJobStatus.FirstOrDefaultAsync(x => x.PkId == jobId);
                        if (statusStepsCompleted != null)
                        {
                            statusStepsCompleted.StepsCompleted = stepcount;
                            tenantDbContext.Entry(statusStepsCompleted).State = EntityState.Modified;
                            await tenantDbContext.SaveChangesAsync();
                        }
                        stepcount++;
                    }
                }
                else if (dimensionType == (int)DimensionEnum.Department)
                {
                    int stepCount = 1;
                    foreach (var item in recordstoProcess)
                    {
                        await ProcessDepartmentData(userId, item);
                        var statusStepsCompleted = await tenantDbContext.TcoJobStatus.FirstOrDefaultAsync(x => x.PkId == jobId);
                        if (statusStepsCompleted != null)
                        {
                            statusStepsCompleted.StepsCompleted = stepCount;
                            tenantDbContext.Entry(statusStepsCompleted).State = EntityState.Modified;
                           await tenantDbContext.SaveChangesAsync();
                        }
                        stepCount++;
                    }
                }
                else if (dimensionType == (int)DimensionEnum.Account)
                {
                    List<AccountHelper> accHelper = new List<AccountHelper>();
                    if (recordstoProcess.Any())
                    {
                        await ProcessAccount(userId, (int)DimensionEnum.Account, recordstoProcess.FirstOrDefault());
                        var statusStepsCompleted = await tenantDbContext.TcoJobStatus.FirstOrDefaultAsync(x => x.PkId == jobId);
                        if (statusStepsCompleted != null)
                        {
                            statusStepsCompleted.StepsCompleted = recordstoProcess.Count();
                            tenantDbContext.Entry(statusStepsCompleted).State = EntityState.Modified;
                            await tenantDbContext.SaveChangesAsync();
                        }
                    }

                    accHelper = (from a in recordstoProcess
                                 select new AccountHelper
                                 {
                                     AccountCode = a.account_code.Trim(),
                                     AccountName = a.account_name.Trim(),
                                     MainAccountCode = a.kostra_account_code,
                                     Status = a.account_isActive != 0 ? "True" : "False",
                                     FromDate = a.account_year_from.ToString(),
                                     ToDate = a.account_year_To.ToString()
                                 }).ToList();
                    
                    await _adminAccount.InsertIntoTmdReporting(userId, accHelper);
                    await  _adminAccount.StartJobToValidateAccounts(userId);
                }
                else if (dimensionType == (int)DimensionEnum.Function)
                {
                    int stepCount = 1;
                    foreach (var item in recordstoProcess)
                    {
                        await ProcessFunctionData(userId, item);
                        var statusStepsCompleted = await tenantDbContext.TcoJobStatus.FirstOrDefaultAsync(x => x.PkId == jobId);
                        if (statusStepsCompleted != null)
                        {
                            statusStepsCompleted.StepsCompleted = stepCount;
                            tenantDbContext.Entry(statusStepsCompleted).State = EntityState.Modified;
                            await tenantDbContext.SaveChangesAsync();
                        }
                        stepCount++;
                    }
                }

                TcoJobStatus status = await tenantDbContext.TcoJobStatus.FirstOrDefaultAsync(x => x.PkId == jobId);
                if (status != null)
                {
                    //delete stage records and then update statuses
                    var dimensionsSet = isRequestByJobId ? await tenantDbContext.tbu_stage_dimensions_import.Where(x => x.job_Id == jobId && x.dimension_type == dimensionType && x.fk_tenant_id == userDetails.tenant_id).ToListAsync() :
                                             await tenantDbContext.tbu_stage_dimensions_import.Where(x => x.user_id == userDetails.pk_id && x.dimension_type == dimensionType && x.fk_tenant_id == userDetails.tenant_id).ToListAsync();
                    await tenantDbContext.BulkDeleteAsync(dimensionsSet);

                    status.EndTime = DateTime.UtcNow;
                    status.StepsCompleted = status.TotalSteps;
                    status.jobStatus = (isRequestByJobId) ? StatusEnumData.CompletedWithoutErrors.ToString() : string.Empty;
                    tenantDbContext.Entry(status).State = EntityState.Modified;
                    await tenantDbContext.SaveChangesAsync();
                }
            }
            catch (Exception e)
            {
                TcoJobStatus status = await tenantDbContext.TcoJobStatus.FirstOrDefaultAsync(x => x.PkId == jobId);
                if (status != null)
                {
                    status.message = e.Message;
                    status.EndTime = DateTime.UtcNow;
                    status.jobStatus = (isRequestByJobId) ? StatusEnumData.CompletedWithErrors.ToString() : string.Empty;
                    await tenantDbContext.SaveChangesAsync();
                }
            }
        }

        public async Task<AiWorkflowState> GetWorkflowState(string userId, int dimensionType, long jobId)
        {
            ImportInfo info = await GetDimensionsImportInfo(userId, dimensionType, jobId);
            TcoJobStatus status = GetJobProgress(userId, dimensionType == ((int)DimensionEnum.Projects) ? UserTrackedJobs.DimensionsImportProject :
                                                                  dimensionType == ((int)DimensionEnum.MainProjects) ? UserTrackedJobs.DimensionsImportMainProject :
                                                                  dimensionType == ((int)DimensionEnum.FreeDimOne) ? UserTrackedJobs.DimensionsImportFreeDimOne :
                                                                  dimensionType == ((int)DimensionEnum.FreeDimTwo) ? UserTrackedJobs.DimensionsImportFreeDimTwo :
                                                                  dimensionType == ((int)DimensionEnum.FreeDimThree) ? UserTrackedJobs.DimensionsImportFreeDimThree :
                                                                  dimensionType == ((int)DimensionEnum.FreeDimFour) ? UserTrackedJobs.DimensionsImportFreeDimFour :
                                                                  dimensionType == ((int)DimensionEnum.Department) ? UserTrackedJobs.DimensionsImportDepartment :
                                                                  dimensionType == ((int)DimensionEnum.Account) ? UserTrackedJobs.DimensionsImportAccount :
                                                                  dimensionType == ((int)DimensionEnum.Function) ? UserTrackedJobs.DimensionsImportFunction :
                                                                  UserTrackedJobs.NoJob, jobId);

            AiWorkflowState state;
            if (jobId == -1)
            {
                if (info.TotalRows == 0 && status == null)
                {
                    //Initial state
                    state = AiWorkflowState.Initial;
                }
                else if (info.TotalRows > 0 && status == null)
                {
                    //Staged state
                    state = AiWorkflowState.Staged;
                }
                else if (status?.EndTime == null)
                {
                    //Importing state
                    //Job is in progress if EndTime is not set
                    state = AiWorkflowState.Importing;
                }
                else if (status.TotalSteps > status.StepsCompleted && status.EndTime != null)
                {
                    //Importing state
                    //Job is in progress if EndTime is not set
                    state = AiWorkflowState.Failed;
                }
                else
                {
                    state = AiWorkflowState.Finished;
                }
            }
            else
            {
                if (info.TotalRows == 0)
                {
                    //Initial state
                    state = AiWorkflowState.Initial;
                }
                else if ((status != null && (status.jobStatus == StatusEnumData.CompletedWithErrors.ToString() || status.jobStatus == StatusEnumData.StagingValidationError.ToString() || status.jobStatus == StatusEnumData.NotStarted.ToString())))
                {
                    //Staged state
                    state = AiWorkflowState.Staged;
                }
                else if (status?.EndTime == null)
                {
                    //Importing state
                    //Job is in progress if EndTime is not set
                    state = AiWorkflowState.Importing;
                }
                else if (status.TotalSteps > status.StepsCompleted && status.EndTime != null)
                {
                    //Importing state
                    //Job is in progress if EndTime is not set
                    state = AiWorkflowState.Failed;
                }
                else
                {
                    state = AiWorkflowState.Finished;
                }
            }
            return state;
        }

        private TcoJobStatus GetJobProgress(string userId, UserTrackedJobs jobType, long jobId)
        {
            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            UserData userDetails = _utility.GetUserDetails(userId);
            TcoJobStatus status;
            if (jobId == -1)
            {
                status = tenantDbContext.TcoJobStatus.FirstOrDefault(x => x.JobType == jobType.ToString() &&
                                                                                     x.UserId == userDetails.pk_id &&
                                                                                     x.TenantId == userDetails.tenant_id);
            }
            else
            {
                status = tenantDbContext.TcoJobStatus.FirstOrDefault(x => x.JobType == jobType.ToString() &&
                                                                                       x.PkId == jobId &&
                                                                                       x.TenantId == userDetails.tenant_id);
            }
            return status;
        }

        public async Task<List<TcoJobsHelper>> GetTcoJobStatusTransactions(string userId)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            CultureInfo ci = CultureInfoFactory.CreateCulture(userDetails.language_preference);
            List<TcoJobsHelper> transactionData = new List<TcoJobsHelper>();
            List<string> jobStatusList = new List<string>() { StatusEnumData.NotStarted.ToString(), StatusEnumData.StagingValidationError.ToString(), StatusEnumData.CompletedWithErrors.ToString() };
            List<string> jobTypeList = new List<string>() { UserTrackedJobs.DimensionsImportFreeDimOne.ToString(), UserTrackedJobs.DimensionsImportFreeDimTwo.ToString(), UserTrackedJobs.DimensionsImportFreeDimThree.ToString(), UserTrackedJobs.DimensionsImportFreeDimFour.ToString(),
                                                            UserTrackedJobs.DimensionsImportDepartment.ToString(), UserTrackedJobs.DimensionsImportMainProject.ToString(), UserTrackedJobs.DimensionsImportProject.ToString(), UserTrackedJobs.DimensionsImportAccount.ToString(), UserTrackedJobs.DimensionsImportFunction.ToString() };

            Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync("nb-NO", userId, "DimensionsImport");
            var tempData = await (from a in tenantDbContext.TcoJobStatus
                            where a.TenantId == userDetails.tenant_id
                            && jobStatusList.Contains(a.jobStatus)
                            && jobTypeList.Contains(a.JobType)
                            orderby a.PkId descending
                            select new
                            {
                                PkId = a.PkId.ToString(),
                                JobType = a.JobType,
                                StartTime = a.StartTime,
                                blob_url = a.request_data_blob_url
                            }).ToListAsync();

            TimeZoneInfo localZone = TimeZoneInfo.Local;
            var norwayTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Central European Standard Time");

            tempData.ForEach(a =>
            {
                transactionData.Add(new TcoJobsHelper()
                {
                    blob_url = a.blob_url,
                    Key = a.PkId,
                    dimensionType = (int)Enum.Parse(typeof(JobTypeEnum), GetJobTypeEnum(a.JobType, langStrings, true)),
                    Value = (a.PkId).ToString() + "- " + GetJobTypeEnum(a.JobType, langStrings, false) + " -" + (TimeZoneInfo.ConvertTimeFromUtc(a.StartTime, norwayTimeZone).ToString("g", ci)),
                });
            });
            return transactionData;
        }

        #region Private Methods For Dimensions Import

        private string GetJobTypeEnum(string jobType, Dictionary<string, clsLanguageString> langStrings, bool getDimensionType)
        {
            string ret = getDimensionType ? JobTypeEnum.FreeDimFourImport.ToString() : ((langStrings.FirstOrDefault(v => v.Key == "DimensionsImport_FreeDimFour")).Value).LangText;
            var type = (UserTrackedJobs)Enum.Parse(typeof(UserTrackedJobs), jobType);
            switch (type)
            {
                case UserTrackedJobs.DimensionsImportFreeDimOne:
                    ret = getDimensionType ? JobTypeEnum.FreeDimOneImport.ToString() : ((langStrings.FirstOrDefault(v => v.Key == "DimensionsImport_FreeDimOne")).Value).LangText;
                    break;

                case UserTrackedJobs.DimensionsImportFreeDimTwo:
                    ret = getDimensionType ? JobTypeEnum.FreeDimTwoImport.ToString() : ((langStrings.FirstOrDefault(v => v.Key == "DimensionsImport_FreeDimTwo")).Value).LangText;
                    break;

                case UserTrackedJobs.DimensionsImportFreeDimThree:
                    ret = getDimensionType ? JobTypeEnum.FreeDimThreeImport.ToString() : ((langStrings.FirstOrDefault(v => v.Key == "DimensionsImport_FreeDimThree")).Value).LangText;
                    break;

                case UserTrackedJobs.DimensionsImportDepartment:
                    ret = getDimensionType ? JobTypeEnum.DepartmentImport.ToString() : ((langStrings.FirstOrDefault(v => v.Key == "DimensionsImport_Deparment")).Value).LangText;
                    break;

                case UserTrackedJobs.DimensionsImportMainProject:
                    ret = getDimensionType ? JobTypeEnum.MainProjectImport.ToString() : ((langStrings.FirstOrDefault(v => v.Key == "DimensionsImport_MainProjects")).Value).LangText;
                    break;

                case UserTrackedJobs.DimensionsImportProject:
                    ret = getDimensionType ? JobTypeEnum.ProjectImport.ToString() : ((langStrings.FirstOrDefault(v => v.Key == "DimensionsImport_Projects")).Value).LangText;
                    break;

                case UserTrackedJobs.DimensionsImportAccount:
                    ret = getDimensionType ? JobTypeEnum.AccountImport.ToString() : ((langStrings.FirstOrDefault(v => v.Key == "DimensionsImport_Account")).Value).LangText;
                    break;

                case UserTrackedJobs.DimensionsImportFunction:
                    ret = getDimensionType ? JobTypeEnum.FunctionImport.ToString() : ((langStrings.FirstOrDefault(v => v.Key == "DimensionsImport_Function")).Value).LangText;
                    break;
            }
            return ret;
        }

        private async Task<Dictionary<string, string>> GetColumnNamesForDimensionsImport(string userId, int dimensionType, bool forImport = false, bool isExport = false)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            UserData userData = await _utility.GetUserDetailsAsync(userId);
            Dictionary<string, string> colNames = new Dictionary<string, string>();
            if (dimensionType == (int)DimensionEnum.Projects || dimensionType == (int)DimensionEnum.MainProjects)
            {
                if (isExport)
                {
                    colNames.Add("project_code", "string");
                    colNames.Add("project_name", "string");
                    colNames.Add("year_from_str", "string");
                    colNames.Add("year_to_str", "string");
                }
                else
                {
                    colNames.Add("project_code", "string");
                    colNames.Add("project_name", "string");
                    colNames.Add("year_from_str", "string");
                    colNames.Add("year_to_str", "string");
                    if (dimensionType == (int)DimensionEnum.Projects)
                    {
                        colNames.Add("status", "string");
                        colNames.Add("main_project_code", "string");
                        colNames.Add("vat_rate", "decimal");
                        colNames.Add("vat_refund", "decimal");
                        colNames.Add("prog_code", "string");
                    }
                    if (dimensionType == (int)DimensionEnum.MainProjects)
                    {
                        colNames.Add("status", "integer");
                        colNames.Add("inv_status", "string");
                        colNames.Add("fk_department_code", "string");
                        colNames.Add("fk_Function_code", "string");
                        colNames.Add("start_year", "string");
                        colNames.Add("original_finish_year", "string");
                        colNames.Add("completion_date", "string");
                    }
                    if (forImport)
                    {
                        colNames.Add("year_from", "integer");
                        colNames.Add("year_to", "integer");
                    }
                }
            }
            else if (dimensionType == (int)DimensionEnum.Account)
            {
                if (isExport)
                {
                    colNames.Add("account_code", "string");
                    colNames.Add("account_name", "string");
                    colNames.Add("kostra_account_code", "string");
                    colNames.Add("account_year_from_str", "string");
                    colNames.Add("account_year_To_str", "string");
                    colNames.Add("account_isActive", "integer");
                }
                else
                {
                    colNames.Add("account_code", "string");
                    colNames.Add("account_name", "string");
                    colNames.Add("kostra_account_code", "string");
                    colNames.Add("account_year_from_str", "string");
                    colNames.Add("account_year_To_str", "string");
                    colNames.Add("account_isActive", "integer");
                    if (forImport)
                    {
                        colNames.Add("account_year_from", "integer");
                        colNames.Add("account_year_To", "integer");
                    }
                }
            }
            else if (dimensionType == (int)DimensionEnum.Department)
            {
                if (isExport)
                {
                    colNames.Add("department_code", "string");
                    colNames.Add("department_name", "string");
                    colNames.Add("year_from_str", "string");
                    colNames.Add("year_to_str", "string");
                    colNames.Add("department_status", "integer");
                }
                else
                {
                    colNames.Add("department_code", "string");
                    colNames.Add("department_name", "string");
                    colNames.Add("year_from_str", "string");
                    colNames.Add("year_to_str", "string");
                    colNames.Add("department_status", "integer");
                    if (forImport)
                    {
                        colNames.Add("year_from", "integer");
                        colNames.Add("year_to", "integer");
                    }
                }
            }
            else if (dimensionType == (int)DimensionEnum.Function)
            {
                colNames.Add("function_code", "string");
                colNames.Add("function_name", "string");
                colNames.Add("year_from_str", "string");
                colNames.Add("year_to_str", "string");
                colNames.Add("kostra_function_code", "string");
                colNames.Add("function_status", "integer");
                if (!isExport && forImport)
                {
                    colNames.Add("year_from", "integer");
                    colNames.Add("year_to", "inetger");
                }
            }
            else if (dimensionType >= (int)DimensionEnum.FreeDimOne && dimensionType <= (int)DimensionEnum.FreeDimFour)
            {
                colNames.Add("free_dim_" + (dimensionType - 1), "string");
                colNames.Add("free_dim_" + (dimensionType - 1) + "_desc", "string");
            }
            else
            {
                //Placeholder for future requirements
            }
            return colNames;
        }

        private void ApplyColumnFormat(Workbook wb, string userId, Dictionary<string, string> colDefs)
        {
            //We need the culture invariant formats. These are stored in the "en-US" entries in the database
            Dictionary<string, clsLanguageString> exportFormats = _utility.GetLanguageStrings("en-US", userId,
                "ExportNumberFormats");

            //Set up styles
            Style stringStyle = wb.CreateStyle();
            stringStyle.Number = 49;
            stringStyle.Font.Name = "Calibiri";
            stringStyle.Font.Size = 11;

            Style intStyle = wb.CreateStyle();
            intStyle.Number = 1;
            intStyle.Font.Name = "Calibiri";
            intStyle.Font.Size = 11;

            Style numberStyle = wb.CreateStyle();
            numberStyle.Custom = exportFormats.FirstOrDefault(v => v.Key == "n2").Value.LangText;
            numberStyle.Font.Name = "Calibiri";
            numberStyle.Font.Size = 11;

            Style dateStyle = wb.CreateStyle();
            dateStyle.Custom = "dd/mm/yyyy";
            dateStyle.Font.Size = 11;

            Style percentageStyle = wb.CreateStyle();
            percentageStyle.Number = 9;
            percentageStyle.Font.Name = "Calibiri";
            percentageStyle.Font.Size = 11;

            //Set up style flags

            StyleFlag numberFlag = new StyleFlag { NumberFormat = true };
            Worksheet ws = wb.Worksheets[0];

            int i = 0;
            //Create header row and Apply styles
            foreach (var col in colDefs)
            {
                switch (col.Value)
                {
                    case "integer":
                        ws.Cells.Columns[i].ApplyStyle(intStyle, numberFlag);
                        break;

                    case "numeric":
                        ws.Cells.Columns[i].ApplyStyle(numberStyle, numberFlag);
                        break;

                    case "datetime":
                        ws.Cells.Columns[i].ApplyStyle(dateStyle, numberFlag);
                        break;

                    case "flag":
                        ws.Cells.Columns[i].ApplyStyle(stringStyle, numberFlag);
                        break;

                    case "percentage":
                        ws.Cells.Columns[i].ApplyStyle(percentageStyle, numberFlag);
                        break;

                    default: //string
                        ws.Cells.Columns[i].ApplyStyle(stringStyle, numberFlag);
                        break;
                }
                i++;
            }
        }

        private static List<DataTable> SplitTable(DataTable originalTable, int batchSize)
        {
            List<DataTable> tables = new List<DataTable>();
            int i = 0;
            int j = 1;
            DataTable newDt = originalTable.Clone();
            newDt.TableName = "Table_" + j;
            newDt.Clear();
            foreach (DataRow row in originalTable.Rows)
            {
                DataRow newRow = newDt.NewRow();
                newRow.ItemArray = row.ItemArray;
                newDt.Rows.Add(newRow);
                i++;
                if (i == batchSize)
                {
                    tables.Add(newDt);
                    j++;
                    newDt = originalTable.Clone();
                    newDt.TableName = "Table_" + j;
                    newDt.Clear();
                    i = 0;
                }
            }

            //The last table is likely to be less than batch size, Add the final table
            tables.Add(newDt);
            return tables;
        }

        private DataTable CleanInputData(DataTable dataTable, int dimensionType, bool isExport = false)
        {
            DataTable data = dataTable;
            if (isExport)
            {
                return data;
            }
            if ((dimensionType == ((int)DimensionEnum.Projects)) || (dimensionType == ((int)DimensionEnum.MainProjects)))
            {
                for (int i = 0; i < data.Rows.Count; i++)
                {
                    int parsedYearFromVal = 0;
                    bool success = int.TryParse(data.Rows[i]["year_from"].ToString(), out parsedYearFromVal);
                    if (!success)
                    {
                        data.Rows[i]["year_from"] = "0";
                    }
                    data.Rows[i]["year_from_str"] = data.Rows[i]["year_from_str"].ToString() ?? string.Empty;

                    int parsedYearToVal = 0;
                    success = false;
                    success = int.TryParse(data.Rows[i]["year_to"].ToString(), out parsedYearToVal);
                    if (!success)
                    {
                        data.Rows[i]["year_to"] = "0";
                    }
                    data.Rows[i]["year_to_str"] = data.Rows[i]["year_to_str"].ToString() ?? string.Empty;

                    if (dimensionType == (int)DimensionEnum.Projects && string.IsNullOrEmpty(data.Rows[i]["vat_rate"].ToString()))
                    {
                        data.Rows[i]["vat_rate"] = 0;
                    }

                    if (dimensionType == (int)DimensionEnum.Projects && string.IsNullOrEmpty(data.Rows[i]["vat_refund"].ToString()))
                    {
                        data.Rows[i]["vat_refund"] = 0;
                    }

                    if (dimensionType == (int)DimensionEnum.MainProjects && string.IsNullOrEmpty(data.Rows[i]["status"].ToString()))
                    {
                        data.Rows[i]["status"] = 1;
                    }
                }
            }
            if ((dimensionType == ((int)DimensionEnum.Account)))
            {
                for (int i = 0; i < data.Rows.Count; i++)
                {
                    int fromDate = 0;
                    bool success = int.TryParse(data.Rows[i]["account_year_from"].ToString(), out fromDate);
                    if (!success)
                    {
                        data.Rows[i]["account_year_from"] = fromDate;
                    }
                    data.Rows[i]["account_year_from_str"] = data.Rows[i]["account_year_from_str"].ToString() ?? string.Empty;

                    int dateTo = 0;

                    success = int.TryParse(data.Rows[i]["account_year_To"].ToString(), out dateTo);
                    if (!success)
                    {
                        data.Rows[i]["account_year_from"] = dateTo;
                    }
                    data.Rows[i]["account_year_from_str"] = data.Rows[i]["account_year_from_str"].ToString() ?? string.Empty;
                }
            }
            else if (dimensionType == (int)DimensionEnum.Function || dimensionType == (int)DimensionEnum.Department)
            {
                for (int i = 0; i < data.Rows.Count; i++)
                {
                    bool success = int.TryParse(data.Rows[i]["year_from"].ToString(), out int parsedYearFromVal);
                    if (!success)
                    {
                        data.Rows[i]["year_from"] = parsedYearFromVal;
                    }
                    data.Rows[i]["year_from_str"] = data.Rows[i]["year_from_str"].ToString() ?? string.Empty;

                    success = int.TryParse(data.Rows[i]["year_to"].ToString(), out int parsedYearToVal);
                    if (!success)
                    {
                        data.Rows[i]["year_to"] = parsedYearToVal;
                    }
                    data.Rows[i]["year_to_str"] = data.Rows[i]["year_to_str"].ToString() ?? string.Empty;
                }
            }
            else
            {
                for (int i = 0; i < data.Rows.Count; i++)
                {
                    if (dimensionType == (int)DimensionEnum.FreeDimOne)
                    {
                        data.Rows[i]["free_dim_1_desc"] = data.Rows[i]["free_dim_1_desc"].ToString().Trim();
                    }
                    if (dimensionType == (int)DimensionEnum.FreeDimTwo)
                    {
                        data.Rows[i]["free_dim_2_desc"] = data.Rows[i]["free_dim_2_desc"].ToString().Trim();
                    }
                    if (dimensionType == (int)DimensionEnum.FreeDimThree)
                    {
                        data.Rows[i]["free_dim_3_desc"] = data.Rows[i]["free_dim_3_desc"].ToString().Trim();
                    }
                    if (dimensionType == (int)DimensionEnum.FreeDimFour)
                    {
                        data.Rows[i]["free_dim_4_desc"] = data.Rows[i]["free_dim_4_desc"].ToString().Trim();
                    }
                }
            }
            return data;
        }

        private async Task ValidateDimensionsImport(string userId, int dimensionType, long jobId = -1)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            tenantDbContext.Database.SetCommandTimeout(500);

            if ((dimensionType == ((int)DimensionEnum.Projects)) || (dimensionType == ((int)DimensionEnum.MainProjects)))
            {
                var spValidate = new PrcValidateImportedProjectData
                {
                    UserId = userDetails.pk_id,
                    TenantId = userDetails.tenant_id,
                    DimensionID = dimensionType,
                    DimensionType = (dimensionType == ((int)DimensionEnum.Projects)) ? DimensionEnum.Projects.ToString() : DimensionEnum.MainProjects.ToString(),
                    jobId = jobId
                };

                await tenantDbContext.Database.ExecuteStoredProcedureAsync(spValidate);
               await ValidateProjectData(userId, dimensionType);
                await RecomputeErrorCount(userId, dimensionType, jobId);
            }
            else if (dimensionType >= ((int)DimensionEnum.FreeDimOne) && dimensionType <= ((int)DimensionEnum.FreeDimFour))
            {
                var spValidate = new prcValidateImportFriDims
                {
                    userId = userDetails.pk_id,
                    tenantId = userDetails.tenant_id,
                    dimensionId = dimensionType,
                    dimensionType = dimensionType.ToString(),
                    freeDim1 = dimensionType == (int)DimensionEnum.FreeDimOne ? "free_dim_1" : string.Empty,
                    freeDim2 = dimensionType == (int)DimensionEnum.FreeDimTwo ? "free_dim_2" : string.Empty,
                    freeDim3 = dimensionType == (int)DimensionEnum.FreeDimThree ? "free_dim_3" : string.Empty,
                    freeDim4 = dimensionType == (int)DimensionEnum.FreeDimFour ? "free_dim_4" : string.Empty,
                    jobId = jobId
                };

                await tenantDbContext.Database.ExecuteStoredProcedureAsync(spValidate);
                await RecomputeErrorCount(userId, dimensionType, jobId);
            }
            else if (dimensionType == ((int)DimensionEnum.Department))
            {
                var spValidate = new PrcValidateImportDepartments
                {
                    UserId = userDetails.pk_id,
                    TenantId = userDetails.tenant_id,
                    DimensionID = dimensionType,
                    jobId = jobId
                };
                await tenantDbContext.Database.ExecuteStoredProcedureAsync(spValidate);
               await  RecomputeErrorCount(userId, dimensionType);
            }
            else if ((dimensionType == ((int)DimensionEnum.Account)))
            {
                var spValidate = new PrcValidateImportedDimensionAccountData
                {
                    UserId = userDetails.pk_id,
                    TenantId = userDetails.tenant_id,
                    DimensionTypeID = dimensionType,
                    jobId = jobId
                };
               await tenantDbContext.Database.ExecuteStoredProcedureAsync(spValidate);
                await RecomputeErrorCount(userId, dimensionType);
            }
            else if (dimensionType == (int)DimensionEnum.Function)
            {
                var spValidate = new PrcValidateImportDimFunction
                {
                    UserId = userDetails.pk_id,
                    TenantId = userDetails.tenant_id,
                    DimensionTypeID = dimensionType,
                    JobId = jobId
                };
                await tenantDbContext.Database.ExecuteStoredProcedureAsync(spValidate);
                await RecomputeErrorCount(userId, dimensionType);
            }
            else
            {
                // Any future validation types can be written here
            }
        }

        private async Task ValidateProjectData(string userId, int dimensionType)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            // Validate Year From
            List<tbu_stage_dimensions_import> dataToValidate = await tenantDbContext.tbu_stage_dimensions_import.AsNoTracking().Where(x => x.user_id == userDetails.pk_id &&
                                                                                                                                x.fk_tenant_id == userDetails.tenant_id && x.dimension_type == dimensionType &&
                                                                                                                                x.year_from == 0 && !string.IsNullOrEmpty(x.year_from_str.Trim())).ToListAsync();
            List<tbu_stage_dimensions_import> toUpdate = new List<tbu_stage_dimensions_import>();
            foreach (var row in dataToValidate)
            {
                var year_from = 0;
                bool success = int.TryParse(row.year_from_str, out year_from);
                if (!success)
                {
                    row.year_from_error = true;
                    toUpdate.Add(row);
                }
                else
                {
                    row.year_from = year_from;
                    toUpdate.Add(row);
                }
            }
            await tenantDbContext.BulkUpdateAsync(toUpdate);

            // Validate Year To
            dataToValidate = await tenantDbContext.tbu_stage_dimensions_import.AsNoTracking().Where(x => x.user_id == userDetails.pk_id &&
                                                                                              x.fk_tenant_id == userDetails.tenant_id && x.dimension_type == dimensionType &&
                                                                                              x.year_to == 0 && !string.IsNullOrEmpty(x.year_to_str.Trim())).ToListAsync();
            toUpdate = new List<tbu_stage_dimensions_import>();
            foreach (var row in dataToValidate)
            {
                var year_to = 0;
                bool success = int.TryParse(row.year_to_str, out year_to);
                if (!success)
                {
                    row.year_to_error = true;
                    toUpdate.Add(row);
                }
                else
                {
                    row.year_to = year_to;
                    toUpdate.Add(row);
                }
            }
           await tenantDbContext.BulkUpdateAsync(toUpdate);
        }

        private async Task RecomputeErrorCount(string userId, int dimensionType, long jobId = -1)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            if (jobId == -1)
            {
                await tenantDbContext.tbu_stage_dimensions_import.Where(x => x.user_id == userDetails.pk_id &&
                    x.fk_tenant_id == userDetails.tenant_id && x.dimension_type == dimensionType).UpdateFromQueryAsync(x => new tbu_stage_dimensions_import
                    {
                        error_count =
                    ((x.project_code_error ? 1 : 0)
                    + (x.project_name_error ? 1 : 0)
                    + (x.year_from_error ? 1 : 0)
                    + (x.year_to_error ? 1 : 0)
                    + (x.free_dim_1_error ? 1 : 0)
                    + (x.free_dim_2_error ? 1 : 0)
                    + (x.free_dim_3_error ? 1 : 0)
                    + (x.free_dim_4_error ? 1 : 0)
                    + (x.free_dim_1_desc_error ? 1 : 0)
                    + (x.free_dim_2_desc_error ? 1 : 0)
                    + (x.free_dim_3_desc_error ? 1 : 0)
                    + (x.free_dim_4_desc_error ? 1 : 0)
                    + (x.main_project_code_error ? 1 : 0)
                    + (x.vat_rate_error ? 1 : 0)
                    + (x.vat_refund_error ? 1 : 0)
                    + (x.inv_status_error ? 1 : 0)
                    + (x.fk_department_code_error ? 1 : 0)
                    + (x.fk_Function_code_error ? 1 : 0)
                    + (x.completion_date_error ? 1 : 0)
                    + (x.original_finish_year_error ? 1 : 0)
                    + (x.start_year_error ? 1 : 0)
                    + (x.status_error ? 1 : 0)
                    + (x.department_code_error ? 1 : 0)
                    + (x.department_name_error ? 1 : 0)
                    + (x.department_status_error ? 1 : 0)
                    + (x.account_code_error ? 1 : 0)
                    + (x.account_year_To_error ? 1 : 0)
                    + (x.account_year_from_error ? 1 : 0)
                    + (x.account_isActive_error ? 1 : 0)
                    + (x.account_name_error ? 1 : 0)
                    + (x.kostra_account_error ? 1 : 0)
                    + (x.function_code_error ? 1 : 0)
                    + (x.function_name_error ? 1 : 0)
                    + (x.kostra_function_code_error ? 1 : 0)
                    + (x.function_status_error ? 1 : 0)
                    + (x.prog_code_error ? 1 : 0))
                    });
            }
            else
            {
                await tenantDbContext.tbu_stage_dimensions_import.Where(x => x.job_Id == jobId &&
                        x.fk_tenant_id == userDetails.tenant_id && x.dimension_type == dimensionType).UpdateFromQueryAsync(x => new tbu_stage_dimensions_import //sonar fix -x.fk_tenant_id ==x.fk_tenant_id
                        {
                            error_count =
                        ((x.project_code_error ? 1 : 0)
                        + (x.project_name_error ? 1 : 0)
                        + (x.year_from_error ? 1 : 0)
                        + (x.year_to_error ? 1 : 0)
                        + (x.free_dim_1_error ? 1 : 0)
                        + (x.free_dim_2_error ? 1 : 0)
                        + (x.free_dim_3_error ? 1 : 0)
                        + (x.free_dim_4_error ? 1 : 0)
                        + (x.free_dim_1_desc_error ? 1 : 0)
                        + (x.free_dim_2_desc_error ? 1 : 0)
                        + (x.free_dim_3_desc_error ? 1 : 0)
                        + (x.free_dim_4_desc_error ? 1 : 0)
                        + (x.main_project_code_error ? 1 : 0)
                        + (x.vat_rate_error ? 1 : 0)
                        + (x.vat_refund_error ? 1 : 0)
                        + (x.inv_status_error ? 1 : 0)
                        + (x.fk_department_code_error ? 1 : 0)
                        + (x.fk_Function_code_error ? 1 : 0)
                        + (x.completion_date_error ? 1 : 0)
                        + (x.original_finish_year_error ? 1 : 0)
                        + (x.start_year_error ? 1 : 0)
                        + (x.status_error ? 1 : 0)
                        + (x.department_code_error ? 1 : 0)
                        + (x.department_name_error ? 1 : 0)
                        + (x.department_status_error ? 1 : 0)
                        + (x.account_code_error ? 1 : 0)
                        + (x.account_year_To_error ? 1 : 0)
                        + (x.account_year_from_error ? 1 : 0)
                        + (x.account_isActive_error ? 1 : 0)
                        + (x.account_name_error ? 1 : 0)
                        + (x.kostra_account_error ? 1 : 0)
                        + (x.function_code_error ? 1 : 0)
                        + (x.function_name_error ? 1 : 0)
                        + (x.kostra_function_code_error ? 1 : 0)
                        + (x.function_status_error ? 1 : 0)
                        + (x.prog_code_error ? 1 : 0))

                        });
            }
        }

        private async Task ProcessFunctionData(string userId, tbu_stage_dimensions_import functionData)
        {
            try
            {
              await  EditInsertFunctionData(userId, functionData, FunctionUpdatesEnum.OverlappingYears);
                await EditInsertFunctionData(userId, functionData, FunctionUpdatesEnum.Names);
                await EditInsertFunctionData(userId, functionData, FunctionUpdatesEnum.Add);
                await EditInsertFunctionData(userId, functionData, FunctionUpdatesEnum.KostraCodeUpdate);
                await EditInsertFunctionData(userId, functionData, FunctionUpdatesEnum.IsActive);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException(ex.Message);
            }
        }

        private async Task ProcessAccount(string userId, int dimensionType, tbu_stage_dimensions_import acc)
        {
            try
            {
                TenantDBContext tenantDbContext = await _utility.GetTenantDbContextForParallelReadAsync();
                UserData userDetails = await _utility.GetUserDetailsAsync(userId);
                if ((dimensionType == (int)DimensionEnum.Account))
                {
                    var spValidate = new prc_insert_dimension_import_accounts
                    {
                        UserId = userDetails.pk_id,
                        TenantId = userDetails.tenant_id,
                        DimensionID = (int)DimensionEnum.Account,
                        DimensionType = DimensionEnum.Account.ToString(),
                        jobId = acc.job_Id.HasValue && acc.job_Id.Value != 0 ? acc.job_Id.Value : -1
                    };

                    await tenantDbContext.Database.ExecuteStoredProcedureAsync(spValidate);
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException(ex.Message);
            }
        }

        private async Task EditInsertAccountData(string userId, tbu_stage_dimensions_import acc, AccountUpdatesEnum updateType)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDbContextForParallelReadAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            if (updateType == AccountUpdatesEnum.OverlappingYears)
            {
                var checkifAccExists = await tenantDbContext.tco_accounts.Where(x => x.pk_account_code == acc.account_code.Trim() && x.isActive && ((acc.account_year_from > x.dateFrom.Year || acc.account_year_from < x.dateFrom.Year) && acc.account_year_from < x.dateTo.Year) && x.pk_tenant_id == userDetails.tenant_id).FirstOrDefaultAsync();
                // for overlapping years
                if (checkifAccExists != null)
                {
                    DateTime newToDate = new DateTime(acc.account_year_from.Value - 1, 12, 31);

                    if (acc.account_year_from.Value <= checkifAccExists.dateFrom.Year) // set new from and to when import year is less then db current year from
                    {
                        newToDate = new DateTime(acc.account_year_from.Value - 1, 12, 31);
                        checkifAccExists.dateFrom = new DateTime(acc.account_year_from.Value - 1, 1, 1);
                    }
                    checkifAccExists.dateTo = newToDate;

                    tenantDbContext.Entry(checkifAccExists).State = EntityState.Modified;
                    await tenantDbContext.SaveChangesAsync();
                }
            }
            if (updateType == AccountUpdatesEnum.Names)
            {
                bool newValue = acc.account_isActive.Value != 0;
                // for project name updates
                var accNameUpdate = await tenantDbContext.tco_accounts.FirstOrDefaultAsync(x => x.pk_account_code == acc.account_code.Trim() && x.isActive == newValue && x.dateFrom.Year == acc.account_year_from && x.dateTo.Year == acc.account_year_To && x.pk_tenant_id == userDetails.tenant_id && x.display_name.Trim() != acc.account_name.Trim());
                if (accNameUpdate != null)
                {
                    accNameUpdate.display_name = acc.account_name.Trim();
                    await tenantDbContext.SaveChangesAsync();
                }
            }
            if (updateType == AccountUpdatesEnum.KostraCodeUpdate)
            {
                bool newValue = acc.account_isActive.Value != 0;
                // for project name updates
                var accNameUpdate = await tenantDbContext.tco_accounts.FirstOrDefaultAsync(x => x.pk_account_code == acc.account_code.Trim() && x.isActive == newValue && x.dateFrom.Year == acc.account_year_from && x.dateTo.Year == acc.account_year_To && x.pk_tenant_id == userDetails.tenant_id && x.fk_kostra_account_code.Trim() != acc.kostra_account_code.Trim() && x.display_name == acc.account_name);
                if (accNameUpdate != null)
                {
                    accNameUpdate.fk_kostra_account_code = acc.kostra_account_code.Trim();
                    await tenantDbContext.SaveChangesAsync();
                }
            }

            if (updateType == AccountUpdatesEnum.IsActiveUpdate)
            {
                bool newValue = acc.account_isActive.Value != 0;
                // for project name updates
                var accNameUpdate = await tenantDbContext.tco_accounts.FirstOrDefaultAsync(x => x.pk_account_code == acc.account_code.Trim() && x.dateFrom.Year == acc.account_year_from && x.dateTo.Year == acc.account_year_To && x.pk_tenant_id == userDetails.tenant_id && x.isActive != newValue && x.display_name == acc.account_name);
                if (accNameUpdate != null)
                {
                    accNameUpdate.isActive = newValue;
                    await tenantDbContext.SaveChangesAsync();
                }
            }
            if (updateType == AccountUpdatesEnum.Add)
            {
                var account = await tenantDbContext.tco_accounts.FirstOrDefaultAsync(x => x.pk_account_code == acc.account_code.Trim() && x.dateFrom.Year == acc.account_year_from && x.dateTo.Year == acc.account_year_To && x.pk_tenant_id == userDetails.tenant_id && x.display_name.Trim() == acc.account_name.Trim());

                if (account == null)
                {
                    var item = new tco_accounts
                    {
                        pk_account_code = acc.account_code.Trim(),
                        pk_tenant_id = userDetails.tenant_id,
                        display_name = acc.account_name.Trim(),
                        description = acc.account_name.Trim(),
                        dateFrom = new DateTime(acc.account_year_from.Value, 01, 01),
                        dateTo = new DateTime(acc.account_year_To.Value, 12, 31),
                        fk_kostra_account_code = acc.kostra_account_code,
                        isActive = acc.account_isActive.Value != 0,
                        updated = DateTime.UtcNow,
                        updated_by = userDetails.pk_id,
                    };
                    tenantDbContext.Entry(item).State = EntityState.Added;
                    await tenantDbContext.SaveChangesAsync();
                }
            }
        }

        private async Task EditInsertFunctionData(string userId, tbu_stage_dimensions_import functionData, FunctionUpdatesEnum updateType)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDbContextForParallelReadAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            if (updateType == FunctionUpdatesEnum.OverlappingYears)
            {
                var checkifSameYears = await tenantDbContext.tco_functions.FirstOrDefaultAsync(x => x.pk_Function_code == functionData.function_code && x.dateFrom.Year == functionData.year_from
                                         && x.dateTo.Year == functionData.year_to && x.pk_tenant_id == userDetails.tenant_id);
                if (checkifSameYears != null) return;
                var checkIfFuncExists = await tenantDbContext.tco_functions.FirstOrDefaultAsync(x => x.pk_tenant_id == userDetails.tenant_id && x.pk_Function_code == functionData.function_code && ((functionData.year_from >= x.dateFrom.Year &&
                                         functionData.year_to <= x.dateTo.Year) || (functionData.year_from <= x.dateFrom.Year && functionData.year_to <= x.dateTo.Year) || (functionData.year_from
                                         == x.dateFrom.Year && functionData.year_to > x.dateTo.Year)));
                if (checkIfFuncExists != null)
                {
                    if ((checkIfFuncExists.dateFrom.Year == functionData.year_from && checkIfFuncExists.dateTo.Year > functionData.year_to) ||
                        (checkIfFuncExists.dateFrom.Year > functionData.year_from && checkIfFuncExists.dateTo.Year > functionData.year_to))
                    {
                        checkIfFuncExists.dateFrom = new DateTime(functionData.year_to.Value + 1, 01, 01);
                        tenantDbContext.Entry(checkIfFuncExists).State = EntityState.Modified;
                        await tenantDbContext.SaveChangesAsync();
                    }
                    else if ((checkIfFuncExists.dateFrom.Year == functionData.year_from && checkIfFuncExists.dateTo.Year < functionData.year_to) ||
                        (checkIfFuncExists.dateFrom.Year > functionData.year_from && checkIfFuncExists.dateTo.Year == functionData.year_to))
                    {
                        tenantDbContext.tco_functions.Remove(checkIfFuncExists);
                        await tenantDbContext.SaveChangesAsync();
                    }
                    else if (checkIfFuncExists.dateFrom.Year < functionData.year_from && checkIfFuncExists.dateTo.Year == functionData.year_to)
                    {
                        checkIfFuncExists.dateTo = new DateTime(functionData.year_from.Value - 1, 12, 31);
                        tenantDbContext.Entry(checkIfFuncExists).State = EntityState.Modified;
                        await tenantDbContext.SaveChangesAsync();
                    }
                    else if (checkIfFuncExists.dateFrom.Year < functionData.year_from && checkIfFuncExists.dateTo.Year > functionData.year_to)
                    {
                        int existingTodate = checkIfFuncExists.dateTo.Year;
                        checkIfFuncExists.dateTo = new DateTime(functionData.year_from.Value - 1, 12, 31);

                        var item = new tco_functions
                        {
                            pk_Function_code = checkIfFuncExists.pk_Function_code,
                            pk_tenant_id = userDetails.tenant_id,
                            display_name = checkIfFuncExists.display_name,
                            description = checkIfFuncExists.description,
                            dateFrom = new DateTime(functionData.year_to.Value + 1, 01, 01),
                            dateTo = new DateTime(existingTodate, 12, 31),
                            isActive = checkIfFuncExists.isActive,
                            fk_kostra_function_code = checkIfFuncExists.fk_kostra_function_code,
                            updated = DateTime.UtcNow,
                            updated_by = userDetails.pk_id
                        };
                        tenantDbContext.Entry(item).State = EntityState.Added;
                       await  tenantDbContext.SaveChangesAsync();
                    }
                }
            }
            else if (updateType == FunctionUpdatesEnum.Names)
            {
                bool isActiveVal = functionData.function_status.Value != 0;
                var checkIfExist = await tenantDbContext.tco_functions.FirstOrDefaultAsync(x => x.pk_Function_code == functionData.function_code.Trim() && x.isActive == isActiveVal && x.display_name != functionData.function_name.Trim()
                                     && x.pk_tenant_id == userDetails.tenant_id && x.dateFrom.Year == functionData.year_from && x.dateTo.Year == functionData.year_to);
                if (checkIfExist != null)
                {
                    checkIfExist.display_name = functionData.function_name.Trim();
                    checkIfExist.description = functionData.function_name.Trim();
                   await  tenantDbContext.SaveChangesAsync();
                }
            }
            else if (updateType == FunctionUpdatesEnum.KostraCodeUpdate)
            {
                bool isActiveVal = functionData.function_status.Value != 0;
                var checkIfExist = await tenantDbContext.tco_functions.FirstOrDefaultAsync(x => x.pk_Function_code == functionData.function_code.Trim() && x.isActive == isActiveVal && x.pk_tenant_id == userDetails.tenant_id
                                    && x.dateFrom.Year == functionData.year_from && x.dateTo.Year == functionData.year_to && x.fk_kostra_function_code != functionData.kostra_function_code.Trim());
                if (checkIfExist != null)
                {
                    checkIfExist.fk_kostra_function_code = functionData.kostra_function_code.Trim();
                   await  tenantDbContext.SaveChangesAsync();
                }
            }
            else if (updateType == FunctionUpdatesEnum.IsActive)
            {
                bool isActiveVal = functionData.function_status.Value != 0;
                var checkIfExist = await tenantDbContext.tco_functions.FirstOrDefaultAsync(x => x.pk_Function_code == functionData.function_code.Trim() && x.isActive != isActiveVal && x.dateFrom.Year == functionData.year_from
                                    && x.dateTo.Year == functionData.year_to && x.pk_tenant_id == userDetails.tenant_id && x.display_name == functionData.function_name.Trim());
                if (checkIfExist != null)
                {
                    checkIfExist.isActive = isActiveVal;
                    await tenantDbContext.SaveChangesAsync();
                }
            }
            else if (updateType == FunctionUpdatesEnum.Add)
            {
                bool isActiveVal = functionData.function_status.Value != 0;
                var function = await tenantDbContext.tco_functions.FirstOrDefaultAsync(x => x.pk_Function_code == functionData.function_code.Trim() && x.isActive == isActiveVal && x.dateFrom.Year == functionData.year_from
                                   && x.dateTo.Year == functionData.year_to && x.pk_tenant_id == userDetails.tenant_id && x.display_name == functionData.function_name);
                if (function == null)
                {
                    var item = new tco_functions
                    {
                        pk_Function_code = functionData.function_code,
                        pk_tenant_id = userDetails.tenant_id,
                        display_name = functionData.function_name,
                        description = functionData.function_name,
                        dateFrom = new DateTime(functionData.year_from.Value, 01, 01),
                        dateTo = new DateTime(functionData.year_to.Value, 12, 31),
                        isActive = functionData.function_status != 0,
                        fk_kostra_function_code = functionData.kostra_function_code,
                        updated = DateTime.UtcNow,
                        updated_by = userDetails.pk_id
                    };
                    tenantDbContext.Entry(item).State = EntityState.Added;
                    await tenantDbContext.SaveChangesAsync();
                }
            }
        }

        private async Task ProcessProjects(string userId, int dimensionType, tbu_stage_dimensions_import proj, bool insertIntoMainProject)
        {
            try
            {
                if (dimensionType == (int)DimensionEnum.Projects)
                {
                    await EditInsertProjectData(userId, proj, ProjectMainProjectUpdatesEnum.OverlappingYears, insertIntoMainProject);
                    await EditInsertProjectData(userId, proj, ProjectMainProjectUpdatesEnum.Names, insertIntoMainProject);
                    await EditInsertProjectData(userId, proj, ProjectMainProjectUpdatesEnum.Add, insertIntoMainProject);
                }
            }
            catch (Exception)
            {
                throw;
            }
        }

        private async Task ProcessMainProjects(string userId, int dimensionType, tbu_stage_dimensions_import proj, bool isreqFromProject)
        {
            try
            {
                if ((dimensionType == (int)DimensionEnum.MainProjects))
                {
                    if (isreqFromProject)
                    {
                       await EditInsertMainProjectData(userId, proj, ProjectMainProjectUpdatesEnum.Add);
                    }
                    else
                    {
                        await EditInsertMainProjectData(userId, proj, ProjectMainProjectUpdatesEnum.UpateExisting);// add update will be taken care in this new stored procedure
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }
        }

        private async Task EditInsertProjectData(string userId, tbu_stage_dimensions_import proj, ProjectMainProjectUpdatesEnum updateType, bool insertIntoMainProject)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDbContextForParallelReadAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            if (updateType == ProjectMainProjectUpdatesEnum.OverlappingYears)
            {
                int projStatus = Int32.Parse(proj.status);
                var checkifSameYears = await tenantDbContext.tco_projects.Where(x => x.pk_project_code == proj.project_code.Trim() && (proj.year_from == x.date_from.Year && proj.year_to == x.date_to.Year)
                                                                                                && x.fk_tenant_id == userDetails.tenant_id).FirstOrDefaultAsync();
                if (checkifSameYears != null)
                {
                    return;
                }
                var checkifProjectexists = await tenantDbContext.tco_projects.Where(x => x.pk_project_code == proj.project_code.Trim() && ((proj.year_from >= x.date_from.Year && proj.year_to <= x.date_to.Year) || (proj.year_from <= x.date_from.Year && proj.year_to <= x.date_to.Year) || (proj.year_from == x.date_from.Year && proj.year_to >= x.date_to.Year))
                                                                                                && x.fk_tenant_id == userDetails.tenant_id).FirstOrDefaultAsync();
                // for overlapping years
                if (checkifProjectexists != null)
                {
                    if ((checkifProjectexists.date_from.Year == proj.year_from && checkifProjectexists.date_to.Year > proj.year_to) ||
                        (checkifProjectexists.date_from.Year > proj.year_from && checkifProjectexists.date_to.Year > proj.year_to)
                        )
                    {
                        checkifProjectexists.date_from = new DateTime(proj.year_to.Value + 1, 01, 01);
                        tenantDbContext.Entry(checkifProjectexists).State = EntityState.Modified;
                        await tenantDbContext.SaveChangesAsync();
                    }
                    else if (checkifProjectexists.date_from.Year == proj.year_from && checkifProjectexists.date_to.Year < proj.year_to)
                    {
                        tenantDbContext.tco_projects.Remove(checkifProjectexists);
                      await   tenantDbContext.SaveChangesAsync();
                    }
                    else if (checkifProjectexists.date_from.Year < proj.year_from && checkifProjectexists.date_to.Year == proj.year_to)
                    {
                        checkifProjectexists.date_to = new DateTime(proj.year_from.Value - 1, 12, 31);
                        tenantDbContext.Entry(checkifProjectexists).State = EntityState.Modified;
                        await tenantDbContext.SaveChangesAsync();
                    }
                    else if (checkifProjectexists.date_from.Year > proj.year_from && checkifProjectexists.date_to.Year == proj.year_to)
                    {
                        checkifProjectexists.date_from = new DateTime(proj.year_from.Value, 01, 01);
                        tenantDbContext.Entry(checkifProjectexists).State = EntityState.Modified;
                        await tenantDbContext.SaveChangesAsync();
                    }
                    else if (checkifProjectexists.date_from.Year < proj.year_from && checkifProjectexists.date_to.Year > proj.year_to)
                    {
                        int existingTodate = checkifProjectexists.date_to.Year;
                        checkifProjectexists.date_to = new DateTime(proj.year_from.Value - 1, 12, 31);

                        var item = new tco_projects
                        {
                            pk_project_code = checkifProjectexists.pk_project_code.Trim(),
                            fk_tenant_id = userDetails.tenant_id,
                            project_name = checkifProjectexists.project_name.Trim(),
                            date_from = new DateTime(proj.year_to.Value + 1, 01, 01),
                            date_to = new DateTime(existingTodate, 12, 31),
                            active = checkifProjectexists.active,
                            updated = DateTime.UtcNow,
                            updated_by = userDetails.pk_id,
                            fk_main_project_code = insertIntoMainProject ? checkifProjectexists.pk_project_code.Trim() : checkifProjectexists.fk_main_project_code.Trim(),
                            fk_responsible_id = 0,
                            fk_prog_code = checkifProjectexists.fk_prog_code.Trim(),
                            is_temp = false,
                            vat_rate = checkifProjectexists.vat_rate,
                            vat_refund = checkifProjectexists.vat_refund
                        };
                        tenantDbContext.Entry(item).State = EntityState.Added;
                       await tenantDbContext.SaveChangesAsync();
                    }
                }
            }
            if (updateType == ProjectMainProjectUpdatesEnum.Names)
            {
                int projStatus = Convert.ToInt32(proj.status);
                // for project name updates
                var projectNameUpdate = await tenantDbContext.tco_projects.FirstOrDefaultAsync(x => x.pk_project_code == proj.project_code.Trim() && x.date_from.Year == proj.year_from && x.date_to.Year == proj.year_to
                                                                                                                                && x.fk_tenant_id == userDetails.tenant_id && (x.project_name.Trim() != proj.project_name.Trim() || x.active != projStatus));
                if (projectNameUpdate != null)
                {
                    projectNameUpdate.project_name = proj.project_name.Trim();
                    projectNameUpdate.active = projStatus;
                    projectNameUpdate.fk_prog_code = proj.prog_code.Trim();
                    await tenantDbContext.SaveChangesAsync();
                }
            }
            if (updateType == ProjectMainProjectUpdatesEnum.Add)
            {
                // for main entries, if there is no entry in tco projects
                var tcoProjectSetUp =
                    await tenantDbContext.tco_projects_setup.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_project_code == proj.project_code.Trim());

                if (tcoProjectSetUp == null)
                {
                    tcoProjectSetUp = new tco_projects_setup()
                    {
                        fk_tenant_id = userDetails.tenant_id,
                        pk_project_code = proj.project_code.Trim(),
                        is_temp = false,
                        updated = DateTime.UtcNow,
                        updated_by = userDetails.pk_id
                    };

                   await tenantDbContext.tco_projects_setup.AddAsync(tcoProjectSetUp);
                    await tenantDbContext.SaveChangesAsync();
                }
                int projStatus = Convert.ToInt32(proj.status);
                var project = await tenantDbContext.tco_projects.FirstOrDefaultAsync(x => x.pk_project_code == proj.project_code.Trim() && x.active == projStatus && x.date_from.Year == proj.year_from && x.date_to.Year == proj.year_to && x.fk_tenant_id == userDetails.tenant_id && x.project_name.Trim() == proj.project_name.Trim());

                if (project == null)
                {
                    var item = new tco_projects
                    {
                        pk_project_code = proj.project_code.Trim(),
                        fk_tenant_id = userDetails.tenant_id,
                        project_name = proj.project_name.Trim(),
                        date_from = new DateTime(proj.year_from.Value, 01, 01),
                        date_to = new DateTime(proj.year_to.Value, 12, 31),
                        active = projStatus,
                        updated = DateTime.UtcNow,
                        updated_by = userDetails.pk_id,
                        fk_main_project_code = insertIntoMainProject ? proj.project_code.Trim() : proj.main_project_code.Trim(),
                        fk_responsible_id = 0,
                        fk_prog_code = proj.prog_code.Trim(),
                        is_temp = false,
                        vat_rate = proj.vat_rate,
                        vat_refund = proj.vat_refund
                    };
                    tenantDbContext.Entry(item).State = EntityState.Added;
                   await tenantDbContext.SaveChangesAsync();
                }
                // for updating the other dimensions
                else
                {
                    
                    bool dimensionsChanged = (project.vat_rate != proj.vat_rate) || (project.vat_refund != proj.vat_refund) || (project.fk_prog_code != proj.prog_code) || (project.fk_main_project_code != proj.main_project_code);
                    if (dimensionsChanged)
                    {
                        project.vat_rate = proj.vat_rate;
                        project.vat_refund = proj.vat_refund;
                        project.fk_prog_code = proj.prog_code ?? project.fk_prog_code;
                        project.fk_main_project_code = proj.main_project_code;
                        tenantDbContext.Entry(project).State = EntityState.Modified;
                        await tenantDbContext.SaveChangesAsync();
                    }
                    if (insertIntoMainProject)
                    {
                        project.fk_main_project_code = proj.project_code.Trim();
                        tenantDbContext.Entry(project).State = EntityState.Modified;
                        await tenantDbContext.SaveChangesAsync();
                    }
                }
            }
        }

        private async Task EditInsertMainProjectData(string userId, tbu_stage_dimensions_import proj, ProjectMainProjectUpdatesEnum updateType)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDbContextForParallelReadAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            if (updateType == ProjectMainProjectUpdatesEnum.OverlappingYears)
            {
                var checkifDataexists = await tenantDbContext.tco_main_projects.Where(x => x.pk_main_project_code == proj.project_code.Trim() && x.status == 1 && (proj.year_from >= x.budget_year_from.Year && proj.year_from < x.budget_year_to.Year)
                                                                                                                                 && x.fk_tenant_id == userDetails.tenant_id && (proj.year_from != x.budget_year_from.Year && proj.year_from != x.budget_year_to.Year)).FirstOrDefaultAsync();
                // for overlapping years
                if (checkifDataexists != null)
                {
                    var completionDate = getDateFromQuarterValue(proj.completion_date, userDetails.language_preference);
                    checkifDataexists.budget_year_to = checkifDataexists.budget_year_from.Year > (proj.year_from.Value - 1) ? new DateTime(proj.year_from.Value, 12, 31) : new DateTime(proj.year_from.Value - 1, 12, 31);
                    checkifDataexists.status = !string.IsNullOrEmpty(proj.status) ? Convert.ToInt32(proj.status) : 1;
                    checkifDataexists.inv_status = !string.IsNullOrEmpty(proj.inv_status) ? Convert.ToInt32(proj.inv_status) : 0;
                    checkifDataexists.fk_department_code = string.IsNullOrEmpty(proj.fk_department_code) ? string.Empty : proj.fk_department_code;
                    checkifDataexists.fk_Function_code = string.IsNullOrEmpty(proj.fk_Function_code) ? string.Empty : proj.fk_Function_code;
                    checkifDataexists.completion_date = completionDate;
                    tenantDbContext.Entry(checkifDataexists).State = EntityState.Modified;
                    await tenantDbContext.SaveChangesAsync();
                }
                tco_main_project_setup mainProjSetup = await tenantDbContext.tco_main_project_setup.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_main_project_code == proj.project_code.Trim());
                if (mainProjSetup != null)
                {
                    mainProjSetup.start_year = !string.IsNullOrEmpty(proj.start_year) ? Convert.ToInt32(proj.start_year) : 0;
                    mainProjSetup.original_finish_year = !string.IsNullOrEmpty(proj.original_finish_year) ? Convert.ToInt32(proj.original_finish_year) : 0;
                   await tenantDbContext.SaveChangesAsync();
                }
                else if (checkifDataexists != null) //present in main project table and missing in setup
                {
                    await tenantDbContext.tco_main_project_setup.AddAsync(new tco_main_project_setup
                    {
                        pk_main_project_code = proj.project_code.Trim(),
                        original_finish_year = !string.IsNullOrEmpty(proj.original_finish_year) ? Convert.ToInt32(proj.original_finish_year) : 0,
                        start_year = !string.IsNullOrEmpty(proj.start_year) ? Convert.ToInt32(proj.start_year) : 0,
                        fk_tenant_id = userDetails.tenant_id,
                        tags = string.Empty,
                        approval_reference = string.Empty,
                        approval_ref_url = string.Empty,
                        updated = DateTime.UtcNow,
                        updated_by = userDetails.pk_id
                    });
                    await tenantDbContext.SaveChangesAsync();
                }
            }
            if (updateType == ProjectMainProjectUpdatesEnum.Names)
            {
                // for name updates
                var NameUpdate = await tenantDbContext.tco_main_projects.FirstOrDefaultAsync(x => x.pk_main_project_code == proj.project_code.Trim() && x.status == 1 && x.budget_year_from.Year == proj.year_from && x.budget_year_to.Year == proj.year_to
                                                                                                                                && x.fk_tenant_id == userDetails.tenant_id && x.main_project_name.Trim() != proj.project_name.Trim());
                if (NameUpdate != null)
                {
                    NameUpdate.main_project_name = proj.project_name.Trim();
                    await tenantDbContext.SaveChangesAsync();
                }
            }
            if (updateType == ProjectMainProjectUpdatesEnum.Add)
            {
                // for main entries, if there is no entry in tco_main_projects
                var mainproject = await tenantDbContext.tco_main_projects.FirstOrDefaultAsync(x => x.pk_main_project_code == proj.project_code.Trim() && x.status == 1 //#171936 && x.budget_year_from.Year == proj.year_from && x.budget_year_to.Year == proj.year_to
                                                                                                                                       && x.fk_tenant_id == userDetails.tenant_id);
                                                                                                                                       //&& x.main_project_name.Trim() == proj.project_name.Trim()); //173291
                if (mainproject == null)
                {
                    DateTime? completionDate = null;
                    if (!string.IsNullOrEmpty(proj.completion_date))
                    {
                        completionDate = getDateFromQuarterValue(proj.completion_date.Trim(), userDetails.language_preference);
                    }

                    await tenantDbContext.tco_main_projects.AddAsync(new tco_main_projects
                    {
                        pk_main_project_code = proj.project_code.Trim(),
                        fk_tenant_id = userDetails.tenant_id,
                        main_project_name = proj.project_name.Trim(),
                        budget_year_from = new DateTime(proj.year_from.Value, 01, 01),
                        budget_year_to = new DateTime(proj.year_to.Value, 12, 31),
                        status = !string.IsNullOrEmpty(proj.status) ? Convert.ToInt32(proj.status) : 1,
                        updated = DateTime.UtcNow,
                        updated_by = userDetails.pk_id,
                        inv_status = !string.IsNullOrEmpty(proj.inv_status) ? Convert.ToInt32(proj.inv_status) : 0,
                        fk_department_code = string.IsNullOrEmpty(proj.fk_department_code) ? string.Empty : proj.fk_department_code,
                        fk_Function_code = string.IsNullOrEmpty(proj.fk_Function_code) ? string.Empty : proj.fk_Function_code,
                        completion_date = completionDate
                    });
                   await  tenantDbContext.SaveChangesAsync();
                    tco_main_project_setup mainProjSetup = await tenantDbContext.tco_main_project_setup.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_main_project_code == proj.project_code.Trim());
                    if (mainProjSetup != null)
                    {
                        mainProjSetup.start_year = !string.IsNullOrEmpty(proj.start_year) ? Convert.ToInt32(proj.start_year) : 0;
                        mainProjSetup.original_finish_year = !string.IsNullOrEmpty(proj.original_finish_year) ? Convert.ToInt32(proj.original_finish_year) : 0;
                    }
                    else
                    {
                      await   tenantDbContext.tco_main_project_setup.AddAsync(new tco_main_project_setup
                        {
                            pk_main_project_code = proj.project_code.Trim(),
                            original_finish_year = !string.IsNullOrEmpty(proj.original_finish_year) ? Convert.ToInt32(proj.original_finish_year) : 0,
                            start_year = !string.IsNullOrEmpty(proj.start_year) ? Convert.ToInt32(proj.start_year) : 0,
                            fk_tenant_id = userDetails.tenant_id,
                            tags = string.Empty,
                            approval_reference = string.Empty,
                            approval_ref_url = string.Empty,
                            updated = DateTime.UtcNow,
                            updated_by = userDetails.pk_id
                        });
                    }
                   await  tenantDbContext.SaveChangesAsync();
                }
            }
            if (updateType == ProjectMainProjectUpdatesEnum.UpateExisting)
            {
                var spValidate = new prc_insert_dimension_import_main_project
                {
                    UserId = userDetails.pk_id,
                    TenantId = userDetails.tenant_id,
                    DimensionID = (int)DimensionEnum.MainProjects,
                    DimensionType = DimensionEnum.MainProjects.ToString(),
                    jobId = proj.job_Id.HasValue ? proj.job_Id.Value : -1
                };

               await  tenantDbContext.Database.ExecuteStoredProcedureAsync(spValidate);
            }
        }

        private async Task ProcessFreeDimData(string userId, tbu_stage_dimensions_import fdRec, int freeDimType)
        {
            await EditInsertFreeDimData(userId, fdRec, "free_dim_" + freeDimType);
        }

        private async Task EditInsertFreeDimData(string userId, tbu_stage_dimensions_import fdRec, string fdType)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDbContextForParallelReadAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            string fdVal = fdType == "free_dim_1" ? fdRec.free_dim_1 :
                                     fdType == "free_dim_2" ? fdRec.free_dim_2 :
                                                              fdType == "free_dim_3" ? fdRec.free_dim_3 : fdRec.free_dim_4;

            string fdDesc = fdType == "free_dim_1" ? fdRec.free_dim_1_desc :
                                      fdType == "free_dim_2" ? fdRec.free_dim_2_desc :
                                                               fdType == "free_dim_3" ? fdRec.free_dim_3_desc : fdRec.free_dim_4_desc;

            var tfdv = await tenantDbContext.tco_free_dim_values.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id
                                                                            && x.free_dim_column == fdType
                                                                            && x.free_dim_code == fdVal);
            if (tfdv != null)
            {
                tfdv.description = fdDesc;
                tfdv.updated = DateTime.UtcNow;
                tfdv.updated_by = userDetails.pk_id;
            }
            else
            {
               await  tenantDbContext.tco_free_dim_values.AddAsync(new tco_free_dim_values
                {
                    fk_tenant_id = userDetails.tenant_id,
                    free_dim_column = fdType,
                    free_dim_code = fdVal,
                    description = fdDesc,
                    status = 1,
                    updated = DateTime.UtcNow,
                    updated_by = userDetails.pk_id
                });
            }
            await tenantDbContext.SaveChangesAsync();
        }

        private DateTime? getDateFromQuarterValue(string estCompletionQtr, string cultureInfo)
        {
            if (string.IsNullOrEmpty(estCompletionQtr))
                return null;

            return DateTime.Parse(estCompletionQtr, CultureInfo.GetCultureInfoByIetfLanguageTag(cultureInfo));
        }

        private async Task ProcessDepartmentData(string userId, tbu_stage_dimensions_import dept)
        {
            try
            {
                await EditInsertImportDepartmentData(userId, dept, DepartmentUpdatesEnum.OverlappingYears);
                await EditInsertImportDepartmentData(userId, dept, DepartmentUpdatesEnum.Names);
                await EditInsertImportDepartmentData(userId, dept, DepartmentUpdatesEnum.Status);
                await EditInsertImportDepartmentData(userId, dept, DepartmentUpdatesEnum.Add);

                dynamic dataArray = new JArray();
                var data = _utility.GetOrgVersions(userId).GetAwaiter().GetResult();
                string latestOrgVersion = string.Empty;
                int count = 0;
                foreach (var d in data)
                {
                    count++;
                    if (count == 1)
                    {
                        latestOrgVersion = d.Key;
                    }
                    dynamic obj = new JObject();
                    obj.key = d.Key;
                    obj.value = d.Value;
                    dataArray.Add(obj);
                }

                if (!string.IsNullOrEmpty(latestOrgVersion))
                {
                   await  _adminOrgStructure.StartJobToValidateAdminOrgStructure(userId, latestOrgVersion);
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException(ex.Message);
            }
        }

        private async Task EditInsertImportDepartmentData(string userId, tbu_stage_dimensions_import dept, DepartmentUpdatesEnum updateType)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDbContextForParallelReadAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            if (updateType == DepartmentUpdatesEnum.OverlappingYears)
            {
                var checkifSameYears = await tenantDbContext.tco_departments.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_department_code == dept.department_code.Trim()
                                                                                            && dept.year_from == x.year_from && dept.year_to == x.year_to && x.status == 1);
                if (checkifSameYears != null) return;

                var checkIfDeptExists = await tenantDbContext.tco_departments.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_department_code == dept.department_code.Trim() &&
                                      ((dept.year_from >= x.year_from && dept.year_to <= x.year_to) || (dept.year_from <= x.year_from && dept.year_to <= x.year_to) ||
                                      (dept.year_from == x.year_from && dept.year_to > x.year_to)) && x.status == 1);

                if (checkIfDeptExists != null)
                {
                    if ((checkIfDeptExists.year_from == dept.year_from && checkIfDeptExists.year_to > dept.year_to) ||
                        (checkIfDeptExists.year_from > dept.year_from && checkIfDeptExists.year_to > dept.year_to))
                    {
                        checkIfDeptExists.year_from = Convert.ToInt32(dept.year_to_str) + 1;
                        tenantDbContext.Entry(checkIfDeptExists).State = EntityState.Modified;
                        await tenantDbContext.SaveChangesAsync();
                    }
                    else if ((checkIfDeptExists.year_from == dept.year_from && checkIfDeptExists.year_to < dept.year_to) ||
                        (checkIfDeptExists.year_from > dept.year_from && checkIfDeptExists.year_to == dept.year_to))
                    {
                        tenantDbContext.tco_departments.Remove(checkIfDeptExists);
                        await tenantDbContext.SaveChangesAsync();
                    }
                    else if (checkIfDeptExists.year_from < dept.year_from && checkIfDeptExists.year_to == dept.year_to)
                    {
                        checkIfDeptExists.year_to = Convert.ToInt32(dept.year_from_str) - 1;
                        tenantDbContext.Entry(checkIfDeptExists).State = EntityState.Modified;
                       await  tenantDbContext.SaveChangesAsync();
                    }
                    else if (checkIfDeptExists.year_from < dept.year_from && checkIfDeptExists.year_to > dept.year_to)
                    {
                        int existingTodate = checkIfDeptExists.year_to;
                        checkIfDeptExists.year_to = dept.year_from.Value - 1;

                        var item = new tco_departments
                        {
                            fk_tenant_id = userDetails.tenant_id,
                            pk_department_code = checkIfDeptExists.pk_department_code.Trim(),
                            department_name = checkIfDeptExists.department_name.Trim(),
                            year_from = Convert.ToInt32(dept.year_to_str) + 1,
                            year_to = existingTodate,
                            status = checkIfDeptExists.status,
                            updated = DateTime.UtcNow,
                            updated_by = userDetails.pk_id
                        };

                        tenantDbContext.Entry(item).State = EntityState.Added;
                        await tenantDbContext.SaveChangesAsync();
                    }
                }
            }
            else if (updateType == DepartmentUpdatesEnum.Names)
            {
                var checkifDataExists = await tenantDbContext.tco_departments.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_department_code == dept.department_code.Trim()
                                                                                        && x.year_from == dept.year_from && x.year_to == dept.year_to);
                if (checkifDataExists != null)
                {
                    checkifDataExists.department_name = dept.department_name.Trim();
                }
            }
            else if (updateType == DepartmentUpdatesEnum.Status)
            {
                var checkifDataExists = await tenantDbContext.tco_departments.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.pk_department_code == dept.department_code.Trim()
                                                                                        && x.year_from == dept.year_from && x.year_to == dept.year_to && x.department_name == dept.department_name);
                if (checkifDataExists != null)
                {
                    checkifDataExists.status = dept.department_status.Value;
                }
            }
            else if (updateType == DepartmentUpdatesEnum.Add)
            {
                var data = await tenantDbContext.tco_departments.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.department_name == dept.department_name.Trim() && x.year_from == dept.year_from
                                                                        && x.year_to == dept.year_to && x.pk_department_code == dept.department_code.Trim());
                if (data == null)
                {
                    var item = new tco_departments
                    {
                        fk_tenant_id = userDetails.tenant_id,
                        pk_department_code = dept.department_code.Trim(),
                        department_name = dept.department_name.Trim(),
                        year_from = Convert.ToInt32(dept.year_from_str),
                        year_to = Convert.ToInt32(dept.year_to_str),
                        status = dept.department_status.Value,
                        updated = DateTime.UtcNow,
                        updated_by = userDetails.pk_id
                    };
                    tenantDbContext.Entry(item).State = EntityState.Added;
                }
            }
           await tenantDbContext.SaveChangesAsync();
        }

        #endregion Private Methods For Dimensions Import
    }

    public enum DimensionEnum
    {
        MainProjects = 0,
        Projects = 1,
        FreeDimOne = 2,
        FreeDimTwo = 3,
        FreeDimThree = 4,
        FreeDimFour = 5,
        Account = 6,
        Department = 7,
        Function = 8
    }

    public enum ProjectMainProjectUpdatesEnum
    {
        OverlappingYears = 0,
        Names = 1,
        Add = 2,
        UpateExisting = 3
    }

    public enum DepartmentUpdatesEnum
    {
        OverlappingYears = 0,
        Names = 1,
        Add = 2,
        Status = 3
    }

    public enum AccountUpdatesEnum
    {
        OverlappingYears = 0,
        Names = 1,
        Add = 2,
        KostraCodeUpdate = 3,
        IsActiveUpdate = 4
    }

    public enum FunctionUpdatesEnum
    {
        OverlappingYears = 0,
        Names = 1,
        Add = 2,
        KostraCodeUpdate = 3,
        IsActive = 4
    }
}