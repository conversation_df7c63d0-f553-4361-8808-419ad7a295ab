#pragma warning disable CS8600
#pragma warning disable CS8601
#pragma warning disable CS8602
#pragma warning disable CS8603
#pragma warning disable <PERSON>8619

using Framsikt.BL.Helpers;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Framsikt.BL
{
    public class AdminTenantSync : IAdminTenantSync
    {
        private readonly IUtility _utility;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IAppDataCache _cache;

        public AdminTenantSync(IUnitOfWork uow, IUtility util, IAppDataCache cache)
        {
            _utility = util;
            _unitOfWork = uow;
            _cache = cache;
        }

        public async Task<List<AdminTenantSyncDataHelper>> GetTenantMappingSyncGridData(string userId, string orgVersion)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            Dictionary<String, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "AdminTenantSync");

            ClsOrgVersionSpecificContent orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, await GetForeCastOrgVersion(userDetails.tenant_id, orgVersion));
            List<Department> lstDepartments = (await _utility.GetTenantDepartmentsAsync(userId)).ToList();
            List<AdminTenantSyncParamterHelper> parametersOrgIds = await GetParameters(userDetails.tenant_id);

            List<AdminTenantSyncOrgDataHelper> orgStructureForColumns = GetOrgDataForParams(orgVersionContent, parametersOrgIds[0], parametersOrgIds[1]);
            // Grid Data
            List<tco_sync_company_setup> gridData = await _unitOfWork.AdminTenantSyncRepository.GetTenantSyncData(userDetails.tenant_id, orgVersion);
            List<KeyValueIntKeyDataPair> subTenantDataList = GetSubTenantsListCached(userId, userDetails.pk_id, userDetails.tenant_id);
            Dictionary<int, List<InvKeyValuePair>> ListOfDeptForSubTenants = await _unitOfWork.AdminTenantSyncRepository.GetDeptForAllSubTenants(userId, subTenantDataList.Select(x => x.Key).ToList());
            //subTenantDataList.ForEach(x => x.Value = x.Value.Substring(0,x.Value.IndexOf("(")));
            InvKeyValuePair emptyDeptPair = new InvKeyValuePair()
            {
                key = "",
                value = ""
            };
            var subTenantsId = gridData.Select(x => x.sub_tenant_id).ToList();
            List<SynchronizeDataHelper> syncStructureValueData = await GetSynchronizeValueData(subTenantsId);
            List<AdminTenantSyncDataHelper> data = new List<AdminTenantSyncDataHelper>();
            foreach (tco_sync_company_setup gd in gridData)
            {
                AdminTenantSyncOrgDataHelper orgData = orgStructureForColumns.FirstOrDefault(x => x.level_2_Id.Equals(gd.org_id));
                KeyValueIntKeyDataPair subTenantPair = subTenantDataList.Find(x => x.Key == gd.sub_tenant_id);
                Department DeptForMainTenantPair = lstDepartments.FirstOrDefault(x => x.departmentValue == gd.default_department_main);
                InvKeyValuePair DeptForSubTenantPair = gd.sub_tenant_id != 0 ? ListOfDeptForSubTenants[gd.sub_tenant_id].Find(x => x.key == gd.default_department_sub) : null;
                var valueData = gd.sub_tenant_id != 0 ? syncStructureValueData.FirstOrDefault(x => x.TenantId == gd.sub_tenant_id && x.AttributeId == gd.sync_value_sub_tenant) : null;
                data.Add(new AdminTenantSyncDataHelper()
                {
                    pkSyncCompanyId = gd.pk_sync_company_id,
                    level1Id = orgData.level_1_Id,
                    level1Desc = orgData.level_1_Desc,
                    level2Id = gd.org_id,
                    level2Desc = orgData.level_2_Desc,
                    synchronizationKey = gd.sync_flag,
                    synchronization = gd.sync_flag == 1 ? langString["AdminTenantSync_Synchronized"].LangText.ToString() : langString["AdminTenantSync_Not_Synchronized"].LangText.ToString(),
                    typeKey = gd.sync_type,
                    type = !string.IsNullOrEmpty(gd.sync_type) ? langString[gd.sync_type].LangText.ToString() : langString["AdminTenantSync_Relation_Value"].LangText.ToString(),
                    synchronizationStructureKey = gd.sync_value_sub_tenant,
                    synchronizationStructure =valueData!=null ?valueData.AttributeValue : "",
                    statusKey = gd.status,
                    status = gd.status == 1 ? langString["AdminTenantSync_Active"].LangText.ToString() : langString["AdminTenantSync_Active"].LangText.ToString(),
                    subTenantId = gd.sub_tenant_id,
                    subTenantDesc = gd.sub_tenant_id != 0 ? subTenantPair.Value : "",
                    deptCodeForMainTenant = gd.default_department_main,
                    deptNameForMainTenant = DeptForMainTenantPair != null ? DeptForMainTenantPair.departmentText : "",
                    deptCodeForSubTenant = gd.default_department_sub,
                    deptNameForSubTenant = DeptForSubTenantPair != null ? DeptForSubTenantPair.value : "",
                    deptForMainTenant = gd.default_department_main + (DeptForMainTenantPair != null ? DeptForMainTenantPair.departmentText : "")
                });
            }
            return data;
        }

        public List<InvKeyValuePair> GetOrgIdsNotUsed(ClsOrgVersionSpecificContent orgVersionContent, AdminTenantSyncParamterHelper parameter, List<tco_sync_company_setup> gridData)
        {
            List<InvKeyValuePair> orgIdData = GetOrgIds(parameter, orgVersionContent);

            orgIdData = orgIdData.Where(x => !gridData.Select(y => y.org_id).ToList().Contains(x.key)).ToList();

            return orgIdData;
        }

        public async Task SaveTenantMappingSyncGridData(string userId, string orgVersion, AdminTenantSyncPostDataHelper data)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            // check if data is in grid
            Guid pkCompSyncId = Guid.Parse(data.pkSyncCompanyId);
            if (!pkCompSyncId.Equals(Guid.Empty))
            {
                tco_sync_company_setup rowData = await _unitOfWork.AdminTenantSyncRepository.GetTenantSyncRowData(userDetails.tenant_id, orgVersion, pkCompSyncId);
                if (rowData != null)
                {
                    rowData.org_id = data.orgId;
                    rowData.sync_flag = data.synchronizationKey;
                    rowData.sync_type = string.IsNullOrEmpty(data.typeKey) ? string.Empty : data.typeKey;
                    rowData.sync_value_sub_tenant = data.synchronizationStructureKey!= null ? data.synchronizationStructureKey : string.Empty;
                    rowData.status = data.statusKey;
                    rowData.sub_tenant_id = (int)(data.subTenantId != null ? data.subTenantId : 0);
                    rowData.default_department_main = data.deptCodeForMainTenant != null ? data.deptCodeForMainTenant : string.Empty;
                    rowData.default_department_sub= data.deptCodeForSubTenant != null ? data.deptCodeForSubTenant : string.Empty;
                    rowData.updated = DateTime.UtcNow;
                    rowData.updated_by = userDetails.pk_id;
                    _unitOfWork.GenericRepo.Update(rowData);
                }
                else
                {
                    throw new Exception("Tenant Sync data not found!");
                }
            }
            // else add new data row
            else
            {
                List<AdminTenantSyncParamterHelper> parameters = await GetParameters(userDetails.tenant_id);

                tco_sync_company_setup newRowData = new tco_sync_company_setup()
                {
                    pk_sync_company_id = Guid.NewGuid(),
                    fk_tenant_id = userDetails.tenant_id,
                    fk_org_version = orgVersion,
                    org_id = data.orgId,
                    org_level = parameters[1].orgLevel,
                    sub_tenant_id = (int)(data.subTenantId != null ? data.subTenantId : 0),
                    sync_flag = data.synchronizationKey,
                    sync_type = string.IsNullOrEmpty(data.typeKey) ? string.Empty : data.typeKey,
                    sync_value_sub_tenant = data.synchronizationStructureKey != null ? data.synchronizationStructureKey : string.Empty,
                    default_department_main = data.deptCodeForMainTenant != null ? data.deptCodeForMainTenant : string.Empty,
                    default_department_sub = data.deptCodeForSubTenant != null ? data.deptCodeForSubTenant : string.Empty,
                    status = data.statusKey,
                    updated = DateTime.UtcNow,
                    updated_by = userDetails.pk_id
                };

                _unitOfWork.GenericRepo.Add(newRowData);
            }
            await _unitOfWork.CompleteAsync();
        }

        public async Task<JObject> GetTenantSyncGridDropdownData(string userId, string orgVersion)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            JObject dropdownData = new JObject();

            Dictionary<String, clsLanguageString> langString = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userId, "AdminTenantSync");
            ClsOrgVersionSpecificContent orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, await GetForeCastOrgVersion(userDetails.tenant_id, orgVersion));
            List<tco_departments> lstDepartments = await DepartmentListCachedAsync(userId, userDetails.tenant_id, orgVersion);
            List<AdminTenantSyncParamterHelper> parametersOrgIds = await GetParameters(userDetails.tenant_id);
            List<tco_sync_company_setup> gridData = await _unitOfWork.AdminTenantSyncRepository.GetTenantSyncData(userDetails.tenant_id, orgVersion);

            // OrgId Dropdown

            List<InvKeyValuePair> orgIdDropdownData = GetOrgIdsNotUsed(orgVersionContent, parametersOrgIds[1], gridData);
            dropdownData.Add("orgIdDropdownData", JArray.FromObject(orgIdDropdownData));

            // synchronized dropdown data

            List<KeyValueIntKeyDataPair> syncDropdownData = new List<KeyValueIntKeyDataPair>
            {
                new KeyValueIntKeyDataPair
                {
                    Key = 0,
                    Value = langString["AdminTenantSync_Not_Synchronized"].LangText.ToString()
                },
                new KeyValueIntKeyDataPair
                {
                    Key = 1,
                    Value = langString["AdminTenantSync_Synchronized"].LangText.ToString()
                }
            };
            dropdownData.Add("syncDropdownData", JArray.FromObject(syncDropdownData));

            // sync type drowdown data

            List<InvKeyValuePair> syncTypeDropdownData = new List<InvKeyValuePair>
            {
                new InvKeyValuePair
                {
                    key = "AdminTenantSync_City_Restricted",
                    value = langString["AdminTenantSync_City_Restricted"].LangText.ToString()
                },
                new InvKeyValuePair
                {
                    key = "AdminTenantSync_City_Level",
                    value = langString["AdminTenantSync_City_Level"].LangText.ToString()
                },
                new InvKeyValuePair
                {
                    key = "AdminTenantSync_Relation_Value",
                    value = langString["AdminTenantSync_Relation_Value"].LangText.ToString()
                }
            };
            dropdownData.Add("syncTypeDropdownData", JArray.FromObject(syncTypeDropdownData.OrderBy(x => x.value).ToList()));

            // status Dropdown data

            List<KeyValueIntKeyDataPair> statusDropdownData = new List<KeyValueIntKeyDataPair>
            {
                new KeyValueIntKeyDataPair
                {
                    Key = 0,
                    Value =  langString["AdminTenantSync_Not_Active"].LangText.ToString()
                },
                new KeyValueIntKeyDataPair
                {
                    Key = 1,
                    Value = langString["AdminTenantSync_Active"].LangText.ToString()
                }
            };
            dropdownData.Add("statusDropdownData", JArray.FromObject(statusDropdownData));

            List<KeyValueIntKeyDataPair> subTenantsList = GetSubTenantsListCached(userId, userDetails.pk_id, userDetails.tenant_id);
            dropdownData.Add("subTenantDropdownData", JArray.FromObject(subTenantsList));

            List<InvKeyValuePair> dropDownListForMainTenantDepts = GetDeptsForTenant(lstDepartments);
            dropdownData.Add("deptDropdownForMainTenant", JArray.FromObject(dropDownListForMainTenantDepts));

            return dropdownData;
        }

        public async Task DeleteTenantSyncGridRow(string userId, string orgVersion, string pkId)
        {
            Guid pkSyncCompany = Guid.Parse(pkId);
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);

            tco_sync_company_setup rowToDelete = await _unitOfWork.AdminTenantSyncRepository.GetTenantSyncRowData(userDetails.tenant_id, orgVersion, pkSyncCompany);
            _unitOfWork.GenericRepo.Delete(rowToDelete);
            await _unitOfWork.CompleteAsync();
        }

        private async Task<List<AdminTenantSyncParamterHelper>> GetParameters(int tenantId)
        {
            List<AdminTenantSyncParamterHelper> parameters = new List<AdminTenantSyncParamterHelper>();
            List<vw_tco_parameters> tenantParams = await _unitOfWork.AdminTenantSyncRepository.GetTenantParametersValue(tenantId, "AdminSyncLevel1", "AdminSyncLevel2");
            if (tenantParams == null || tenantParams.Count == 0)
            {
                parameters.Add(new AdminTenantSyncParamterHelper()
                {
                    type = "Param1",
                    value = "org_id_2",
                    orgLevel = 2
                });

                parameters.Add(new AdminTenantSyncParamterHelper()
                {
                    type = "Param2",
                    value = "org_id_3",
                    orgLevel = 3
                });
            }
            else if (tenantParams.Count == 1)
            {
                if (tenantParams.FirstOrDefault(x => x.param_name.ToLower().Equals("adminsynclevel1")) != null)
                {
                    int currentOrgLevel = GetCurrentOrgLevel(tenantParams[0].param_value);
                    parameters.Add(new AdminTenantSyncParamterHelper()
                    {
                        type = "Param1",
                        value = tenantParams[0].param_value,
                        orgLevel = currentOrgLevel
                    });
                    int nextOrgLevel = currentOrgLevel + 1;
                    parameters.Add(new AdminTenantSyncParamterHelper()
                    {
                        type = "Param2",
                        value = tenantParams[0].param_value.Substring(0, tenantParams[0].param_value.Length - 1) + nextOrgLevel.ToString(),
                        orgLevel = nextOrgLevel
                    });
                }
                else
                {
                    int currentOrgLevel = GetCurrentOrgLevel(tenantParams[0].param_value);
                    int prevOrgLevel = currentOrgLevel - 1;
                    parameters.Add(new AdminTenantSyncParamterHelper()
                    {
                        type = "Param1",
                        value = tenantParams[0].param_value.Substring(0, tenantParams[0].param_value.Length - 1) + prevOrgLevel.ToString(),
                        orgLevel = prevOrgLevel
                    });
                    parameters.Add(new AdminTenantSyncParamterHelper()
                    {
                        type = "Param2",
                        value = tenantParams[0].param_value,
                        orgLevel = currentOrgLevel
                    });
                }
            }
            else if (tenantParams.Count == 2)
            {
                vw_tco_parameters firstParam = tenantParams.FirstOrDefault(x => x.param_name.ToLower().Equals("adminsynclevel1"));
                parameters.Add(new AdminTenantSyncParamterHelper()
                {
                    type = "Param1",
                    value = firstParam.param_value,
                    orgLevel = GetCurrentOrgLevel(firstParam.param_value)
                });
                vw_tco_parameters secondParam = tenantParams.FirstOrDefault(x => x.param_name.ToLower().Equals("adminsynclevel1"));
                parameters.Add(new AdminTenantSyncParamterHelper()
                {
                    type = "Param2",
                    value = secondParam.param_value,
                    orgLevel = GetCurrentOrgLevel(secondParam.param_value)
                });
            }
            return parameters;
        }

        private int GetCurrentOrgLevel(string currentOrgLevel)
        {
            int level = Int32.Parse(currentOrgLevel.Substring(currentOrgLevel.Length - 1));

            return level;
        }

        private async Task<int> GetForeCastOrgVersion(int tenantId, string version)
        {
            tco_org_version versionData = await _unitOfWork.AdminTenantSyncRepository.GetOrgVersionData(tenantId, version);
            return versionData.period_from;
        }

        private List<AdminTenantSyncOrgDataHelper> GetOrgDataForParams(ClsOrgVersionSpecificContent content, AdminTenantSyncParamterHelper param1, AdminTenantSyncParamterHelper param2)
        {
            List<AdminTenantSyncOrgDataHelper> orgData = new List<AdminTenantSyncOrgDataHelper>();
            switch (param1.orgLevel)
            {
                case 2:
                    List<AdminTenantSyncOrgDataHelper> orgDataTemp2 = (from a in content.lstOrgDataLevel2
                                                                       select new AdminTenantSyncOrgDataHelper
                                                                       {
                                                                           level_1_Id = a.org_id_2,
                                                                           level_1_Name = a.org_name_2,
                                                                           level_1_Desc = a.org_id_2 + " " + a.org_name_2
                                                                       }).Distinct().ToList();
                    foreach (AdminTenantSyncOrgDataHelper data in orgDataTemp2)
                    {
                        List<tco_org_hierarchy> orgContent = content.lstOrgHierarchy.Where(x => x.org_id_2.Equals(data.level_1_Id)).ToList();
                        orgData.AddRange(GetOrgIdsOfOrgLevel(orgContent, data, param2));
                    }
                    break;

                case 3:
                    List<AdminTenantSyncOrgDataHelper> orgDataTemp3 = (from a in content.lstOrgDataLevel3
                                                                       select new AdminTenantSyncOrgDataHelper
                                                                       {
                                                                           level_1_Id = a.org_id_3,
                                                                           level_1_Name = a.org_name_3,
                                                                           level_1_Desc = a.org_id_3 + " " + a.org_name_3
                                                                       }).Distinct().ToList();
                    foreach (AdminTenantSyncOrgDataHelper data in orgDataTemp3)
                    {
                        List<tco_org_hierarchy> orgContent = content.lstOrgHierarchy.Where(x => x.org_id_3.Equals(data.level_1_Id)).ToList();
                        orgData.AddRange(GetOrgIdsOfOrgLevel(orgContent, data, param2));
                    }
                    break;

                case 4:
                    List<AdminTenantSyncOrgDataHelper> orgDataTemp4 = (from a in content.lstOrgDataLevel4
                                                                       select new AdminTenantSyncOrgDataHelper
                                                                       {
                                                                           level_1_Id = a.org_id_4,
                                                                           level_1_Name = a.org_name_4,
                                                                           level_1_Desc = a.org_id_4 + " " + a.org_name_4
                                                                       }).Distinct().ToList();
                    foreach (AdminTenantSyncOrgDataHelper data in orgDataTemp4)
                    {
                        List<tco_org_hierarchy> orgContent = content.lstOrgHierarchy.Where(x => x.org_id_4.Equals(data.level_1_Id)).ToList();
                        orgData.AddRange(GetOrgIdsOfOrgLevel(orgContent, data, param2));
                    }
                    break;

                case 5:
                    List<AdminTenantSyncOrgDataHelper> orgDataTemp5 = (from a in content.lstOrgDataLevel5
                                                                       select new AdminTenantSyncOrgDataHelper
                                                                       {
                                                                           level_1_Id = a.org_id_5,
                                                                           level_1_Name = a.org_name_5,
                                                                           level_1_Desc = a.org_id_5 + " " + a.org_name_5
                                                                       }).Distinct().ToList();
                    foreach (AdminTenantSyncOrgDataHelper data in orgDataTemp5)
                    {
                        List<tco_org_hierarchy> orgContent = content.lstOrgHierarchy.Where(x => x.org_id_5.Equals(data.level_1_Id)).ToList();
                        orgData.AddRange(GetOrgIdsOfOrgLevel(orgContent, data, param2));
                    }
                    break;

                case 6:
                    List<AdminTenantSyncOrgDataHelper> orgDataTemp6 = (from a in content.lstOrgDataLevel6
                                                                       select new AdminTenantSyncOrgDataHelper
                                                                       {
                                                                           level_1_Id = a.org_id_6,
                                                                           level_1_Name = a.org_name_6,
                                                                           level_1_Desc = a.org_id_6 + " " + a.org_name_6
                                                                       }).Distinct().ToList();
                    foreach (AdminTenantSyncOrgDataHelper data in orgDataTemp6)
                    {
                        List<tco_org_hierarchy> orgContent = content.lstOrgHierarchy.Where(x => x.org_id_6.Equals(data.level_1_Id)).ToList();
                        orgData.AddRange(GetOrgIdsOfOrgLevel(orgContent, data, param2));
                    }
                    break;

                case 7:
                    List<AdminTenantSyncOrgDataHelper> orgDataTemp7 = (from a in content.lstOrgDataLevel7
                                                                       select new AdminTenantSyncOrgDataHelper
                                                                       {
                                                                           level_1_Id = a.org_id_7,
                                                                           level_1_Name = a.org_name_7,
                                                                           level_1_Desc = a.org_id_7 + " " + a.org_name_7
                                                                       }).Distinct().ToList();
                    foreach (AdminTenantSyncOrgDataHelper data in orgDataTemp7)
                    {
                        List<tco_org_hierarchy> orgContent = content.lstOrgHierarchy.Where(x => x.org_id_7.Equals(data.level_1_Id)).ToList();
                        orgData.AddRange(GetOrgIdsOfOrgLevel(orgContent, data, param2));
                    }
                    break;
            }
            return orgData;
        }

        private List<AdminTenantSyncOrgDataHelper> GetOrgIdsOfOrgLevel(List<tco_org_hierarchy> content, AdminTenantSyncOrgDataHelper orgDataParent, AdminTenantSyncParamterHelper param)
        {
            List<AdminTenantSyncOrgDataHelper> orgData = new List<AdminTenantSyncOrgDataHelper>();

            switch (param.orgLevel)
            {
                case 3:
                    orgData = (from a in content
                               select new AdminTenantSyncOrgDataHelper
                               {
                                   level_1_Id = orgDataParent.level_1_Id,
                                   level_1_Name = orgDataParent.level_1_Name,
                                   level_1_Desc = orgDataParent.level_1_Desc,
                                   level_2_Id = a.org_id_3,
                                   level_2_Name = a.org_name_3,
                                   level_2_Desc = a.org_id_3 + " " + a.org_name_3,
                               }).ToList();
                    orgData = orgData.GroupBy(x => x.level_2_Id).Select(i => i.FirstOrDefault()).ToList();
                    break;

                case 4:
                    orgData = (from a in content
                               select new AdminTenantSyncOrgDataHelper
                               {
                                   level_1_Id = orgDataParent.level_1_Id,
                                   level_1_Name = orgDataParent.level_1_Name,
                                   level_1_Desc = orgDataParent.level_1_Desc,
                                   level_2_Id = a.org_id_4,
                                   level_2_Name = a.org_name_4,
                                   level_2_Desc = a.org_id_4 + " " + a.org_name_4,
                               }).ToList();
                    orgData = orgData.GroupBy(x => x.level_2_Id).Select(i => i.FirstOrDefault()).ToList();
                    break;

                case 5:
                    orgData = (from a in content
                               select new AdminTenantSyncOrgDataHelper
                               {
                                   level_1_Id = orgDataParent.level_1_Id,
                                   level_1_Name = orgDataParent.level_1_Name,
                                   level_1_Desc = orgDataParent.level_1_Desc,
                                   level_2_Id = a.org_id_5,
                                   level_2_Name = a.org_name_5,
                                   level_2_Desc = a.org_id_5 + " " + a.org_name_5,
                               }).ToList();
                    orgData = orgData.GroupBy(x => x.level_2_Id).Select(i => i.FirstOrDefault()).ToList();
                    break;

                case 6:
                    orgData = (from a in content
                               select new AdminTenantSyncOrgDataHelper
                               {
                                   level_1_Id = orgDataParent.level_1_Id,
                                   level_1_Name = orgDataParent.level_1_Name,
                                   level_1_Desc = orgDataParent.level_1_Desc,
                                   level_2_Id = a.org_id_6,
                                   level_2_Name = a.org_name_6,
                                   level_2_Desc = a.org_id_6 + " " + a.org_name_6,
                               }).ToList();
                    orgData = orgData.GroupBy(x => x.level_2_Id).Select(i => i.FirstOrDefault()).ToList();
                    break;

                case 7:
                    orgData = (from a in content
                               select new AdminTenantSyncOrgDataHelper
                               {
                                   level_1_Id = orgDataParent.level_1_Id,
                                   level_1_Name = orgDataParent.level_1_Name,
                                   level_1_Desc = orgDataParent.level_1_Desc,
                                   level_2_Id = a.org_id_7,
                                   level_2_Name = a.org_name_7,
                                   level_2_Desc = a.org_id_7 + " " + a.org_name_7,
                               }).ToList();
                    orgData = orgData.GroupBy(x => x.level_2_Id).Select(i => i.FirstOrDefault()).ToList();
                    break;

                case 8:
                    orgData = (from a in content
                               select new AdminTenantSyncOrgDataHelper
                               {
                                   level_1_Id = orgDataParent.level_1_Id,
                                   level_1_Name = orgDataParent.level_1_Name,
                                   level_1_Desc = orgDataParent.level_1_Desc,
                                   level_2_Id = a.org_id_8,
                                   level_2_Name = a.org_name_8,
                                   level_2_Desc = a.org_id_8 + " " + a.org_name_8,
                               }).ToList();
                    orgData = orgData.GroupBy(x => x.level_2_Id).Select(i => i.FirstOrDefault()).ToList();
                    break;
            }

            return orgData;
        }

        private List<InvKeyValuePair> GetOrgIds(AdminTenantSyncParamterHelper param, ClsOrgVersionSpecificContent content)
        {
            List<InvKeyValuePair> orgIds = new List<InvKeyValuePair>();
            switch (param.orgLevel)
            {
                case 3:
                    orgIds = (from a in content.lstOrgDataLevel3
                              select new InvKeyValuePair
                              {
                                  key = a.org_id_3,
                                  value = a.org_id_3 + " " + a.org_name_3
                              }).ToList();
                    break;

                case 4:
                    orgIds = (from a in content.lstOrgDataLevel4
                              select new InvKeyValuePair
                              {
                                  key = a.org_id_4,
                                  value = a.org_id_4 + " " + a.org_name_4
                              }).ToList();
                    break;

                case 5:
                    orgIds = (from a in content.lstOrgDataLevel5
                              select new InvKeyValuePair
                              {
                                  key = a.org_id_5,
                                  value = a.org_id_5 + " " + a.org_name_5
                              }).ToList();
                    break;

                case 6:
                    orgIds = (from a in content.lstOrgDataLevel6
                              select new InvKeyValuePair
                              {
                                  key = a.org_id_6,
                                  value = a.org_id_6 + " " + a.org_name_6
                              }).ToList();
                    break;

                case 7:
                    orgIds = (from a in content.lstOrgDataLevel7
                              select new InvKeyValuePair
                              {
                                  key = a.org_id_7,
                                  value = a.org_id_7 + " " + a.org_name_7
                              }).ToList();
                    break;

                case 8:
                    orgIds = (from a in content.lstOrgDataLevel8
                              select new InvKeyValuePair
                              {
                                  key = a.org_id_8,
                                  value = a.org_id_8 + " " + a.org_name_8
                              }).ToList();
                    break;
            }
            return orgIds;
        }

        public async Task<List<tco_departments>> DepartmentListCachedAsync(string userId, int tenantId, string orgVersion)
        {
            TenantDBContext tenantDbContext = await _utility.GetTenantDBContextAsync();
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            int clientId = userDetails.client_id;

            ClsOrgVersionSpecificContent orgVersionContent = new ClsOrgVersionSpecificContent();
            List<tco_departments> lstDepartments = new List<tco_departments>();
            
            TimeSpan cacheTimeOut = new TimeSpan(24, 0, 0);
            string cacheKey = $"cacheDepartment-{tenantId}";
            string strOrgVersionContent = await _cache.GetStringForUserAsync(clientId, userDetails.tenant_id, userId, cacheKey);
            if (string.IsNullOrEmpty(strOrgVersionContent))
            {
                lstDepartments = await (tenantDbContext.tco_departments.Where(x => x.fk_tenant_id == tenantId && x.status == 1)).ToListAsync();
                return lstDepartments;
            }
            else
            {
                lstDepartments = JsonConvert.DeserializeObject<List<tco_departments>>(strOrgVersionContent);
                return lstDepartments;
            }
        }

        public async Task<List<InvKeyValuePair>> GetDepartmentDataForSubTenantAsync(string userId, string orgVersion, int subTenantId)
        {
            UserData userDetails = await _utility.GetUserDetailsAsync(userId);
            List<tco_departments> lstDepartments = await DepartmentListCachedAsync(userId, subTenantId, orgVersion);

            List<InvKeyValuePair> DropDownListForSubTenantDepts = GetDeptsForTenant(lstDepartments);
            return DropDownListForSubTenantDepts;
        }
        public async Task<List<InvKeyValuePair>> GetSynchronizedStructureData(int subTenantId)
        {
            List<InvKeyValuePair> DropDownListForSynchronizeData = await _unitOfWork.AdminTenantSyncRepository.GetSynchronizeData(subTenantId);
            return DropDownListForSynchronizeData;
        }
        private  List<InvKeyValuePair> GetDeptsForTenant( List<tco_departments> lstDepartments)
        {
            List<InvKeyValuePair> DeptsListData = (from a in lstDepartments
                                                   select new InvKeyValuePair
                                                   {
                                                       key = a.pk_department_code,
                                                       value = a.pk_department_code + " " + a.department_name
                                                   }).ToList();
            return DeptsListData;
        }
        public async Task<List<SynchronizeDataHelper>> GetSynchronizeValueData(List<int> subTenantsId)
        {
            List<SynchronizeDataHelper> SynchronizeValueData = await _unitOfWork.AdminTenantSyncRepository.GetSynchronizeValueData(subTenantsId);
            return SynchronizeValueData;
        }

        public List<KeyValueIntKeyDataPair> GetSubTenantsListCached(string userId, int pk_id, int tenantId)
        {

            TenantDBContext tenantDbContext = _utility.GetTenantDBContext();
            UserData userDetails = _utility.GetUserDetails(userId);
            int clientId = userDetails.client_id;
            AuthenticationDBContext _authDbContext = _utility.GetAuthenticationContext();
            List<KeyValueIntKeyDataPair> SubTenantsListData = new List<KeyValueIntKeyDataPair>();
            TimeSpan cacheTimeOut = new TimeSpan(24, 0, 0);
            string cacheKey = $"cachedSubTenants-{tenantId}";
            string strTenantBasedSubTenantList = _cache.GetStringForUser(clientId, userDetails.tenant_id, userId, cacheKey);
            if (string.IsNullOrEmpty(strTenantBasedSubTenantList))
            {
                SubTenantsListData = (from gt in tenantDbContext.gco_tenants
                                    where gt.client_id == clientId && gt.pk_id != clientId
                                    orderby gt.tenant_name
                                    select new KeyValueIntKeyDataPair
                                    {
                                        Key = gt.pk_id,
                                        Value = gt.tenant_name
                                    }).ToList();
                return SubTenantsListData;
            }
            else
            {
                SubTenantsListData = JsonConvert.DeserializeObject<List<KeyValueIntKeyDataPair>>(strTenantBasedSubTenantList);
                return SubTenantsListData;
            }
        }
    }
}