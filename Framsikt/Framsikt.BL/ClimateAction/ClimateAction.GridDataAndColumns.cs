using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text;
using KeyValuePair = Framsikt.BL.Helpers.KeyValuePair;

namespace Framsikt.BL;

public partial class ClimateAction : IClimateAction
{


    public JObject GetClimateActionDataForDocument(string userId, int budgetYear, Guid categoryId,
        bool filterBlistActions, List<string> columnFields)
    {
        return GetClimateActionDataForDocumentAsync(userId, budgetYear, categoryId, filterBlistActions,
            columnFields).GetAwaiter().GetResult();
    }


    public async Task<JObject> GetClimateActionDataForDocumentAsync(string userId, int budgetYear, Guid categoryId, bool filterBlistActions, List<string> columnFields)
    {
        dynamic climateActionData = new JObject();
        TenantDBContext tenantdbContext = await _utility.GetTenantDBContextAsync();
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> climateActionLangStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ClimateAction");
        List<ClimateActionFilterHelper> climateData;

        //Fetch climate data
        var climateDataList = await GetBaseData(userId, budgetYear, categoryId);
        climateDataList = filterBlistActions ? climateDataList.Where(x => x.actionStatus == (int)ClimateActionStatusType.Blist).ToList() :
            climateDataList.Where(x => x.actionStatus == (int)ClimateActionStatusType.Finplan).ToList();

        List<Guid> goalsPresentInData = climateDataList.Select(x => x.goalId).ToList();
        var goalsList = await GetGoalInfo(userDetails, tenantdbContext, budgetYear, goalsPresentInData);
        List<string> unsGoals = goalsList.Where(x => !string.IsNullOrEmpty(x.unsd_goals)).Select(x => x.unsd_goals).ToList();
        List<string> unsGoalIds = new List<string>();
        foreach (string id in unsGoals)
        {
            unsGoalIds.AddRange(id.Split(',').Where(x => !string.IsNullOrEmpty(x)).ToList());
        }
        var unsGoalInfo = await GetUnsGoalInfo(tenantdbContext, unsGoalIds.Distinct().ToList());

        if (climateDataList.Any(x => !string.IsNullOrEmpty(x.mainProjectCode) || x.fpActionId != 0))
        {
            //Get expense amounts from investments/actions table if climate action is connected to investment/actions.
            InvestmentActionHelper amountData = new InvestmentActionHelper();
            amountData.orgLevel = 1;
            amountData.investmentIds = new List<string>();
            amountData.fpActionIds = new List<int>();
            amountData.functions = new List<string>();
            amountData.departments = new List<string>();
            //Get amounts for actions which created as new blist action and then converted to other action type.
            climateDataList = await UpdateBlistActionIds(userId, climateDataList, budgetYear);
            climateDataList = await UpdateExpenseAmounts(userId, budgetYear, climateDataList, amountData);
        }
        //group by sector and source
        climateData = climateDataList.GroupBy(x => new
        {
            x.sourceId,
            x.sectorId,
            x.climateActionName,
            x.climateActionId,
            x.goalId,
            x.tags,
            x.description,
            x.descriptionId,
            x.financeDescription,
            x.financeDescriptionId,
            x.cost,
            x.totalCost,
            x.sector,
            x.source,
            x.categoryId,
            x.actionStatus,
            x.longtermEconomicImpact,
            x.categoryName,
            x.securityEstimate,
            x.feasibility,
            x.referenceUrl,
            x.investment,
            x.finplanAction,
            x.reductionQuantity,
            x.reductionQuantityName,
            x.fpActionOrInvestmentInfo
        }).Select(res => new ClimateActionFilterHelper
        {
            climateActionId = res.Key.climateActionId,
            climateActionName = res.Key.climateActionName,
            goalId = res.Key.goalId,
            tags = res.Key.tags,
            description = res.Key.description,
            descriptionId = res.Key.descriptionId,
            financeDescription = res.Key.financeDescription,
            financeDescriptionId = res.Key.financeDescriptionId,
            sectorId = res.Key.sectorId,
            cost = res.Key.cost * 1000,
            totalCost = res.Key.totalCost * 1000,
            sector = res.Key.sector,
            sourceId = res.Key.sourceId,
            source = res.Key.source,
            reductionYear1 = res.Sum(x => x.reductionYear1),
            reductionYear2 = res.Sum(x => x.reductionYear2),
            reductionYear3 = res.Sum(x => x.reductionYear3),
            reductionYear4 = res.Sum(x => x.reductionYear4),
            reductionYear5 = res.Sum(x => x.reductionYear5),
            reductionYear6 = res.Sum(x => x.reductionYear6),
            reductionYear7 = res.Sum(x => x.reductionYear7),
            reductionYear8 = res.Sum(x => x.reductionYear8),
            reductionYear9 = res.Sum(x => x.reductionYear9),
            reductionYear10 = res.Sum(x => x.reductionYear10),
            reductionPrevYear1Value = res.Sum(x => x.reductionPrevYear1Value),
            reductionPrevYear2Value = res.Sum(x => x.reductionPrevYear2Value),
            longTermReduction = res.Sum(x => x.longTermReduction),
            categoryId = res.Key.categoryId,
            actionStatus = res.Key.actionStatus,
            longtermEconomicImpact = res.Key.longtermEconomicImpact * 1000,
            categoryName = res.Key.categoryName,
            securityEstimate = res.Key.securityEstimate,
            feasibility = res.Key.feasibility,
            referenceUrl = res.Key.referenceUrl,
            investment = res.Key.investment,
            finplanAction = res.Key.finplanAction,
            reductionQuantity = res.Key.reductionQuantity,
            reductionQuantityName = res.Key.reductionQuantityName,
            fpActionOrInvestmentInfoString = JsonConvert.SerializeObject(res.Key.fpActionOrInvestmentInfo)
        }).ToList();

        string isActionsStortByName = await _utility.GetParameterValueAsync(userId, "SORT_CLIMATE_ACTION_ON_NAME");

        if (!columnFields.Any() || (!string.IsNullOrEmpty(isActionsStortByName) && isActionsStortByName.ToUpper() == "TRUE"))
        {
            climateData = AggregateClimateData(climateData, columnFields).OrderBy(x => x.climateActionName).ToList();
        }
        else
        {
            climateData = AggregateClimateData(climateData, columnFields).OrderBy(x => x.sector).ToList();
        }

        var unqueClimateData = climateData.GroupBy(x => new { x.climateActionId, x.cost, x.totalCost, x.longtermEconomicImpact }).Select(x => new { x.Key.cost, x.Key.totalCost, x.Key.longtermEconomicImpact }).ToList();
        //for total row
        decimal costSum = unqueClimateData.Sum(x => x.cost);
        decimal totalCostSum = unqueClimateData.Sum(x => x.totalCost);
        decimal longtermEconomicImpactSum = unqueClimateData.Sum(x => x.longtermEconomicImpact);
        decimal year1Sum = climateData.Sum(x => x.reductionYear1);
        decimal year2Sum = climateData.Sum(x => x.reductionYear2);
        decimal year3Sum = climateData.Sum(x => x.reductionYear3);
        decimal year4Sum = climateData.Sum(x => x.reductionYear4);
        decimal year5Sum = climateData.Sum(x => x.reductionYear5);
        decimal year6Sum = climateData.Sum(x => x.reductionYear6);
        decimal year7Sum = climateData.Sum(x => x.reductionYear7);
        decimal year8Sum = climateData.Sum(x => x.reductionYear8);
        decimal year9Sum = climateData.Sum(x => x.reductionYear9);
        decimal year10Sum = climateData.Sum(x => x.reductionYear10);
        decimal prevYear1Sum = climateData.Sum(x => x.reductionPrevYear1Value);
        decimal prevYear2Sum = climateData.Sum(x => x.reductionPrevYear2Value);
        decimal longTermReductionSum = climateData.Sum(x => x.longTermReduction);

        dynamic dataArray = new JArray();
        int itemCount = 0;
        // fetch finance amounts sum
        var climateActionIds = climateData.Select(x => x.climateActionId).ToList();
        ClimateActionInvestmentInput inputData = new ClimateActionInvestmentInput { budgetYear = budgetYear, orgId = "", orgLevel = 1, serviceId = "ALL", serviceLevel = 0 };
        var connectedActionInvestments = await GetListOfActionsAndInvestments(userId, inputData, true);
        var financeAmountsforClimateIds = await GetClimateFinanceAmounts(userDetails.tenant_id, budgetYear, connectedActionInvestments);
        foreach (var item in climateData)
        {
            dynamic finaldata = new JObject();
            var financeAmounts = financeAmountsforClimateIds.Where(x => x.climateActionId == item.climateActionId).FirstOrDefault();
            if (itemCount == 0 || climateData[itemCount].climateActionId != climateData[itemCount - 1].climateActionId)
            {
                finaldata.climateActionName = item.climateActionName;
                finaldata.description = item.description;
                finaldata.financeDescription = item.financeDescription;
                finaldata.cost = item.cost.ToString();
                finaldata.totalCost = item.totalCost.ToString();
                finaldata.isParent = true;
                finaldata.longTermEconomicImpact = item.longtermEconomicImpact.ToString();
                finaldata.securityEstimate = item.securityEstimate;
                finaldata.feasibility = item.feasibility;
                finaldata.referenceUrl = item.referenceUrl;
                finaldata.investment = string.IsNullOrEmpty(item.investment) ? climateActionLangStrings["CA_Not_Applicable"].LangText : item.investment;
                finaldata.finplanAction = string.IsNullOrEmpty(item.finplanAction) ? climateActionLangStrings["CA_Not_Applicable"].LangText : item.finplanAction;
                finaldata.planActionSpecInfo = GetPlanActionSpecificInfo(item, -1, climateActionLangStrings);
                finaldata.statusInPlan = (!item.isFromPlan) ? climateActionLangStrings["CA_Not_Relevant"].LangText : item.planStatusDesc;
                finaldata.fpActionOrInvestment = item.fpActionOrInvestmentInfoString;
            }
            else
            {
                finaldata.climateActionName = string.Empty;
                finaldata.description = string.Empty;
                finaldata.financeDescription = string.Empty;
                finaldata.cost = string.Empty;
                finaldata.totalCost = string.Empty;
                finaldata.isParent = false;
                finaldata.longTermEconomicImpact = string.Empty;
                finaldata.securityEstimate = string.Empty;
                finaldata.feasibility = string.Empty;
                finaldata.referenceUrl = string.Empty;
                finaldata.investment = string.Empty;
                finaldata.finplanAction = string.Empty;
                finaldata.planActionSpecInfo = string.Empty;
                finaldata.statusInPlan = string.Empty;
                finaldata.planStatusDescId = Guid.Empty;
                finaldata.showPlanStatusDescIcon = false;
                finaldata.isParent = false;
                finaldata.fpActionOrInvestment = null;
            }
            finaldata.sector = item.sector;
            finaldata.source = item.source;
            finaldata.reductionYear1 = item.reductionYear1;
            finaldata.reductionYear2 = item.reductionYear2;
            finaldata.reductionYear3 = item.reductionYear3;
            finaldata.reductionYear4 = item.reductionYear4;
            finaldata.reductionYear5 = item.reductionYear5;
            finaldata.reductionYear6 = item.reductionYear6;
            finaldata.reductionYear7 = item.reductionYear7;
            finaldata.reductionYear8 = item.reductionYear8;
            finaldata.reductionYear9 = item.reductionYear9;
            finaldata.reductionYear10 = item.reductionYear10;
            finaldata.reductionPrevYear1 = item.reductionPrevYear1Value;
            finaldata.reductionPrevYear2 = item.reductionPrevYear2Value;
            finaldata.longTermReduction = item.longTermReduction;
            finaldata.climateActionId = item.climateActionId;
            finaldata.reductionQuantityName = string.IsNullOrEmpty(item.reductionQuantityName) ? climateActionLangStrings["Reduction_Quantity_Not_Used"].LangText : item.reductionQuantityName;
            var goalsDetail = GetGoalsDetail(item.goalId, goalsList, unsGoalInfo);
            string goalDesc = goalsDetail.Any() ? goalsDetail.FirstOrDefault(x => x.Key).ShortDesc : string.Empty;
            string unsgoalForDoc = string.Empty, unsGoalForWeb = string.Empty;
            foreach (var g in goalsDetail.Where(x => !x.Key))
            {
                unsGoalForWeb = unsGoalForWeb + (g.ShortDesc) + "<hr>";
                unsgoalForDoc = unsgoalForDoc + (g.ShortDesc) + Environment.NewLine + Environment.NewLine;
            }
            unsGoalForWeb = !string.IsNullOrEmpty(unsGoalForWeb) ? unsGoalForWeb.Remove(unsGoalForWeb.Length - "<hr>".Length) : unsGoalForWeb;
            unsgoalForDoc = !string.IsNullOrEmpty(unsgoalForDoc) ? unsgoalForDoc.Remove(unsgoalForDoc.Length - (Environment.NewLine.Length * 2)) : unsgoalForDoc;

            finaldata.goalDescription = (itemCount == 0 || climateData[itemCount].climateActionId != climateData[itemCount - 1].climateActionId) ? goalDesc : string.Empty;
            finaldata.isParent = itemCount == 0 || climateData[itemCount].climateActionId != climateData[itemCount - 1].climateActionId;
            finaldata.unsGoalForWeb = (itemCount == 0 || climateData[itemCount].climateActionId != climateData[itemCount - 1].climateActionId) ? unsGoalForWeb : string.Empty;
            finaldata.unsGoalForDoc = (itemCount == 0 || climateData[itemCount].climateActionId != climateData[itemCount - 1].climateActionId) ? unsgoalForDoc : string.Empty;
            finaldata.financeSumYear1 = financeAmounts != null ? financeAmounts.firstYearSum : 0;
            finaldata.financeSumYear4 = financeAmounts != null ? financeAmounts.fourYearSum : 0;
            finaldata.financeSumYear10 = financeAmounts != null ? financeAmounts.tenYearSum : 0;
            itemCount++;
            dataArray.Add(finaldata);
        }
        //Format 'Sum' row.
        dynamic totalData = new JObject();
        totalData.climateActionName = climateActionLangStrings["Climate_grid_total"].LangText;
        totalData.description = string.Empty;
        totalData.financeDescription = string.Empty;
        totalData.cost = costSum.ToString();
        totalData.totalCost = totalCostSum.ToString();
        totalData.sector = string.Empty;
        totalData.source = string.Empty;
        totalData.longTermEconomicImpact = longtermEconomicImpactSum;
        totalData.reductionYear1 = year1Sum;
        totalData.reductionYear2 = year2Sum;
        totalData.reductionYear3 = year3Sum;
        totalData.reductionYear4 = year4Sum;
        totalData.reductionYear5 = year5Sum;
        totalData.reductionYear6 = year6Sum;
        totalData.reductionYear7 = year7Sum;
        totalData.reductionYear8 = year8Sum;
        totalData.reductionYear9 = year9Sum;
        totalData.reductionYear10 = year10Sum;
        totalData.reductionPrevYear1 = prevYear1Sum;
        totalData.reductionPrevYear2 = prevYear2Sum;
        totalData.longTermReduction = longTermReductionSum;
        totalData.climateActionId = -1;
        totalData.securityEstimate = string.Empty;
        totalData.feasibility = string.Empty;
        totalData.referenceUrl = string.Empty;
        totalData.goalDescription = string.Empty;
        totalData.unsGoalForDoc = string.Empty;
        totalData.unsGoalForWeb = string.Empty;
        totalData.isParent = true;
        totalData.investment = string.Empty;
        totalData.finplanAction = string.Empty;
        totalData.planActionSpecInfo = string.Empty;
        totalData.statusInPlan = string.Empty;
        totalData.reductionQuantityName = string.Empty;
        totalData.fpActionOrInvestment = null;
        totalData.financeSumYear1 = 0;
        totalData.financeSumYear4 = 0;
        totalData.financeSumYear10 = 0;

        foreach (var item in dataArray)
        {
            JObject itemObj = (JObject)item;
            totalData.financeSumYear1 += itemObj["financeSumYear1"];
            totalData.financeSumYear4 += itemObj["financeSumYear4"];
            totalData.financeSumYear10 += itemObj["financeSumYear10"];
        }
            

        dataArray.Add(totalData);
        climateActionData.data = dataArray;
        return climateActionData;
    }


    public async Task<ColumnSelector> GetClimateFinanceColumnsAsync(string userId, List<string> allColumns, int budgetYear)
    {
        UserData user = await _utility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> climateActionLangStrings = await _utility.GetLanguageStringsAsync(user.language_preference, user.user_name, "ClimateAction");
        List<ColumnSelectorColumn> columnsconfig = new List<ColumnSelectorColumn>();
        List<ColumnSelectorColumn> resultColumnConfig = new List<ColumnSelectorColumn>();
        List<ColumnSelectorSection> result = new List<ColumnSelectorSection>();
        ColumnSelector res = new ColumnSelector();
        TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
        string flagName = "CLIMATE_FINANCE_COLUMNS_VIEW";
        string flagKeyId = "-1";

        var targetStatusColumnConfig = await dbContext.tco_application_flag.Where(x =>
            x.fk_tenant_id == user.tenant_id && x.flag_name == flagName && x.budget_year == budgetYear &&
            x.flag_key_id == flagKeyId).ToListAsync();

        var tenantColumnConfig = targetStatusColumnConfig.Any() ? targetStatusColumnConfig.FirstOrDefault(x => x.flag_key_id == flagKeyId) : null;

        List<string> defaultSelectedCol = new List<string>() { "Year1", "Year2", "Year3", "Year4", "financeType", "financeName"};
        if (tenantColumnConfig != null)
        {
            Guid? flagGuid = tenantColumnConfig.flag_guid;

            if (flagGuid != null)
            {
                clsTenantCloudTableEntity updateEntity = await _utility.GetCloudTableContentAsync("WADTenantData", user.tenant_id.ToString(), flagGuid.ToString());
                if (updateEntity != null)
                {
                    JArray columnsArray = JArray.FromObject(JsonConvert.DeserializeObject(updateEntity.data.ToString()));

                    columnsconfig = (from s in columnsArray
                        select new ColumnSelectorColumn
                        {
                            key = (string)s["key"],
                            value = (string)s["value"],
                            isChecked = (bool)s["isChecked"],
                            isDefault = defaultSelectedCol.Contains(s["key"].ToString()),
                            section = ""
                        }).ToList();
                }
            }
        }

        foreach (var item in allColumns)
        {
            bool isChecked = false;
            bool isDefault = false;
            if (tenantColumnConfig != null)
            {
                if (columnsconfig.Count > 0 && columnsconfig.Select(x => x.key).Contains(item.ToString()))
                {
                    isChecked = columnsconfig.FirstOrDefault(x => x.key == item.ToString()).isChecked;
                }
            }
            else
            {
                if (defaultSelectedCol.Contains(item.ToString()))

                {
                    isChecked = true;
                    isDefault = true;
                }
            }

            ColumnSelectorColumn columnInfo = new ColumnSelectorColumn()
            {
                key = item.ToString(),
                value = "",
                isChecked = isChecked,
                isDefault = isDefault,
                section = ""
            };
            switch (item.ToString())
            {
                case "Year1":
                    columnInfo.value = budgetYear.ToString();
                    resultColumnConfig.Add(columnInfo);
                    break;
                case "Year2":
                    columnInfo.value = (budgetYear+1).ToString();
                    resultColumnConfig.Add(columnInfo);
                    break;
                case "Year3":
                    columnInfo.value = (budgetYear + 2).ToString();
                    resultColumnConfig.Add(columnInfo);
                    break;
                case "Year4":
                    columnInfo.value = (budgetYear + 3).ToString();
                    resultColumnConfig.Add(columnInfo);
                    break;
                case "Year5":
                    columnInfo.value = (budgetYear + 4).ToString();
                    resultColumnConfig.Add(columnInfo);
                    break;
                case "Year6":
                    columnInfo.value = (budgetYear + 5).ToString();
                    resultColumnConfig.Add(columnInfo);
                    break;
                case "Year7":
                    columnInfo.value = (budgetYear + 6).ToString();
                    resultColumnConfig.Add(columnInfo);
                    break;
                case "Year8":
                    columnInfo.value = (budgetYear + 7).ToString();
                    resultColumnConfig.Add(columnInfo);
                    break;
                case "Year9":
                    columnInfo.value = (budgetYear + 8).ToString();
                    resultColumnConfig.Add(columnInfo);
                    break;
                case "Year10":
                    columnInfo.value = (budgetYear + 9).ToString();
                    resultColumnConfig.Add(columnInfo);
                    break;
                case "financeName":
                    columnInfo.value = climateActionLangStrings["ca_finance_name"].LangText;
                    resultColumnConfig.Add(columnInfo);
                    break;
                case "financeType":
                    columnInfo.value = climateActionLangStrings["ca_finance_type"].LangText;
                    resultColumnConfig.Add(columnInfo);
                    break;
                case "FourYearSum":
                    columnInfo.value = climateActionLangStrings["ca_sum_four_year"].LangText;
                    resultColumnConfig.Add(columnInfo);
                    break;
                case "TenYearSum":
                    columnInfo.value = climateActionLangStrings["ca_sum_ten_year"].LangText;
                    resultColumnConfig.Add(columnInfo);
                    break;
            }

        }
        //Add for save For Standard
        var resultList = new ColumnSelectorSection
        {
            name = climateActionLangStrings["ca_finance_col_sel"].LangText,
            section = "",
            columns = resultColumnConfig,
        };
        result.Add(resultList);

        res.ColumnSelectorSection = result;
        res.columnSelectorTitle = climateActionLangStrings["ca_finance_name"].LangText;
        res.columnSelectorDescription = climateActionLangStrings["climate_colsel_description"].LangText;
        return res;
    }



    public async Task<ColumnSelector> GetClimateActionGridColumnsAsync(string userId, List<string> allColumns, int budgetYear, Guid categoryId)
    {
        UserData user = await _utility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> climateActionLangStrings = await _utility.GetLanguageStringsAsync(user.language_preference, user.user_name, "ClimateAction");
        List<ColumnSelectorColumn> columnsconfig = new List<ColumnSelectorColumn>();
        List<ColumnSelectorColumn> resultColumnConfig = new List<ColumnSelectorColumn>();
        List<ColumnSelectorColumn> resultColumnConfig1 = new List<ColumnSelectorColumn>();
        List<ColumnSelectorSection> result = new List<ColumnSelectorSection>();
        ColumnSelector res = new ColumnSelector();
        TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
        string flagName = "CLIMATE_ACTION_COLUMNS_VIEW_" + categoryId;
        string flagKeyId = "-1";

        bool isTenantSync = await _finUtility.IsTenantSyncSetup(user.tenant_id, user.client_id);
        var isSubTenant = (await _finUtility.GetSyncSubTenantData(user.tenant_id)).Count == 0;

        var targetStatusColumnConfig = await dbContext.tco_application_flag.Where(x =>
            x.fk_tenant_id == user.tenant_id && x.flag_name == flagName && x.budget_year == budgetYear &&
            x.flag_key_id == flagKeyId).ToListAsync();

        var tenantColumnConfig = targetStatusColumnConfig.Any() ? targetStatusColumnConfig.FirstOrDefault(x => x.flag_key_id == flagKeyId) : null;

        List<string> defaultSelectedCol = new List<string>() { "cost", "totalCost", "longTermEconomicImpact", "sector", "source", "createdAt", "syncStatus", "reductionPrevYear2", "reductionPrevYear1", "reductionYear1", "reductionYear2", "reductionYear3",
            "reductionYear4", "reductionYear5", "reductionYear6", "reductionYear7", "reductionYear8", "reductionYear9", "reductionYear10", "longTermReduction", "actionToAssignment" , "goal", "unsGoal"};
        allColumns.Remove("climateActionName");
        if( !(isTenantSync && isSubTenant))
        {
            defaultSelectedCol.Remove("syncStatus");
            allColumns.Remove("syncStatus");
        }
        if (tenantColumnConfig != null)
        {
            Guid? flagGuid = tenantColumnConfig.flag_guid;

            if (flagGuid != null)
            {
                clsTenantCloudTableEntity updateEntity = await _utility.GetCloudTableContentAsync("WADTenantData", user.tenant_id.ToString(), flagGuid.ToString());
                if (updateEntity != null)
                {
                    JArray columnsArray = JArray.FromObject(JsonConvert.DeserializeObject(updateEntity.data.ToString()));

                    columnsconfig = (from s in columnsArray
                        select new ColumnSelectorColumn
                        {
                            key = (string)s["key"],
                            value = (string)s["value"],
                            isChecked = (bool)s["isChecked"],
                            isDefault = defaultSelectedCol.Contains(s["key"].ToString()),
                            section = ""
                        }).ToList();
                }
            }
        }

        foreach (var item in allColumns)
        {
            bool isChecked = false;
            bool isDefault = false;
            if (tenantColumnConfig != null)
            {
                if (columnsconfig.Count > 0 && columnsconfig.Select(x => x.key).Contains(item.ToString()))
                {
                    isChecked = columnsconfig.FirstOrDefault(x => x.key == item.ToString()).isChecked;
                }
            }
            else
            {
                if (defaultSelectedCol.Contains(item.ToString()))

                {
                    isChecked = true;
                    isDefault = true;
                }
            }

            ColumnSelectorColumn columnInfo = new ColumnSelectorColumn()
            {
                key = item.ToString(),
                value = "",
                isChecked = isChecked,
                isDefault = isDefault,
                section = ""
            };
                
            switch (item.ToString())
            {
                case "orgCreatedAt":
                    columnInfo.value = climateActionLangStrings["CA_Created_At"].LangText;
                    resultColumnConfig.Add(columnInfo);
                    break;

                case "syncStatus":
                    columnInfo.value = climateActionLangStrings["CA_Sync_Status"].LangText;
                    resultColumnConfig.Add(columnInfo);
                    break;

                case "cost":
                    columnInfo.value = climateActionLangStrings["FP_cost"].LangText + " " + budgetYear;
                    resultColumnConfig.Add(columnInfo);
                    break;

                case "totalCost":
                    columnInfo.value = climateActionLangStrings["FP_totalCost"].LangText + " " + budgetYear + "-" + (budgetYear + 3);
                    resultColumnConfig.Add(columnInfo);
                    break;

                case "longTermEconomicImpact":
                    columnInfo.value = climateActionLangStrings["CA_Long_term_economic_impact"].LangText;
                    resultColumnConfig.Add(columnInfo);
                    break;

                case "sector":
                    columnInfo.value = climateActionLangStrings["FP_sector"].LangText;
                    resultColumnConfig1.Add(columnInfo);
                    break;

                case "source":
                    columnInfo.value = climateActionLangStrings["FP_source"].LangText;
                    resultColumnConfig1.Add(columnInfo);
                    break;

                case "reductionPrevYear1":
                    columnInfo.value = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear - 1);
                    resultColumnConfig1.Add(columnInfo);
                    break;

                case "reductionPrevYear2":
                    columnInfo.value = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear - 2);
                    resultColumnConfig1.Add(columnInfo);
                    break;

                case "reductionYear1":
                    columnInfo.value = climateActionLangStrings["FP_reductionYear"].LangText + " " + budgetYear;
                    resultColumnConfig1.Add(columnInfo);
                    break;

                case "reductionYear2":
                    columnInfo.value = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 1);
                    resultColumnConfig1.Add(columnInfo);
                    break;

                case "reductionYear3":
                    columnInfo.value = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 2);
                    resultColumnConfig1.Add(columnInfo);
                    break;

                case "reductionYear4":
                    columnInfo.value = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 3);
                    resultColumnConfig1.Add(columnInfo);
                    break;

                case "reductionYear5":
                    columnInfo.value = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 4);
                    resultColumnConfig1.Add(columnInfo);
                    break;

                case "reductionYear6":
                    columnInfo.value = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 5);
                    resultColumnConfig1.Add(columnInfo);
                    break;

                case "reductionYear7":
                    columnInfo.value = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 6);
                    resultColumnConfig1.Add(columnInfo);
                    break;

                case "reductionYear8":
                    columnInfo.value = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 7);
                    resultColumnConfig1.Add(columnInfo);
                    break;

                case "reductionYear9":
                    columnInfo.value = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 8);
                    resultColumnConfig1.Add(columnInfo);
                    break;

                case "reductionYear10":
                    columnInfo.value = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 9);
                    resultColumnConfig1.Add(columnInfo);
                    break;

                case "description":
                    columnInfo.value = climateActionLangStrings["ClimateAction_FP_description"].LangText;
                    resultColumnConfig1.Add(columnInfo);
                    break;

                case "financeDescription":
                    columnInfo.value = climateActionLangStrings["FP_financeDescription"].LangText;
                    resultColumnConfig1.Add(columnInfo);
                    break;

                case "goal":
                    columnInfo.value = climateActionLangStrings["FP_CA_goal"].LangText;
                    resultColumnConfig.Add(columnInfo);
                    break;

                case "securityEstimate":
                    columnInfo.value = climateActionLangStrings["FP_CA_securityEstimate"].LangText;
                    resultColumnConfig.Add(columnInfo);
                    break;

                case "feasibility":
                    columnInfo.value = climateActionLangStrings["FP_CA_feasibility"].LangText;
                    resultColumnConfig.Add(columnInfo);
                    break;

                case "referenceUrl":
                    columnInfo.value = climateActionLangStrings["FP_CA_referenceUrl"].LangText;
                    resultColumnConfig.Add(columnInfo);
                    break;

                case "unsGoalTag":
                    columnInfo.value = climateActionLangStrings["FP_CA_unsGoal"].LangText;
                    resultColumnConfig.Add(columnInfo);
                    break;

                case "actionToAssignment":
                    columnInfo.value = climateActionLangStrings["CA_action_as_assignment"].LangText;
                    resultColumnConfig1.Add(columnInfo);
                    break;

                case "finplanAction":
                    columnInfo.value = climateActionLangStrings["CA_Finplan_Action_Name"].LangText;
                    resultColumnConfig.Add(columnInfo);
                    break;

                case "investment":
                    columnInfo.value = climateActionLangStrings["CA_Investment_Name"].LangText;
                    resultColumnConfig.Add(columnInfo);
                    break;

                case "planActionSpecInfo":
                    columnInfo.value = climateActionLangStrings["CA_planActionSpecInfo_Name"].LangText;
                    resultColumnConfig1.Add(columnInfo);
                    break;

                case "statusInPlan":
                    columnInfo.value = climateActionLangStrings["CA_statusInPlan_Name"].LangText;
                    resultColumnConfig1.Add(columnInfo);
                    break;

                case "reductionQuantityName":
                    columnInfo.value = climateActionLangStrings["CA_Reduction_Quantity"].LangText;
                    resultColumnConfig1.Add(columnInfo);
                    break;

                case "climateActionName":
                    columnInfo.value = climateActionLangStrings["CA_climateActions"].LangText;
                    resultColumnConfig.Add(columnInfo);
                    break;
                case "planTitle":
                    columnInfo.value = climateActionLangStrings["ca_plan_connection_title"].LangText;
                    resultColumnConfig1.Add(columnInfo);
                    break;
                case "climateStatus":
                    columnInfo.value = climateActionLangStrings["CA_export_status_desc"].LangText;
                    resultColumnConfig.Add(columnInfo);
                    break;
                case "tags":
                    columnInfo.value = climateActionLangStrings["ca_tag"].LangText;
                    resultColumnConfig.Add(columnInfo);
                    break;
                case "financeSumYear1":
                    columnInfo.value = climateActionLangStrings["ca_finance_sum1"].LangText;
                    resultColumnConfig.Add(columnInfo);
                    break;
                case "financeSumYear4":
                    columnInfo.value = climateActionLangStrings["ca_finance_sum4"].LangText;
                    resultColumnConfig.Add(columnInfo);
                    break;
                case "financeSumYear10":
                    columnInfo.value = climateActionLangStrings["ca_finance_sum10"].LangText;
                    resultColumnConfig.Add(columnInfo);
                    break;
                default:
                    break;
            }

        }
        //Add for save For Standard
        var resultList = new ColumnSelectorSection
        {
            name = climateActionLangStrings["CA_climateActions"].LangText,
            section = "",
            columns = resultColumnConfig,
        };
        var resultList1 = new ColumnSelectorSection
        {
            name = climateActionLangStrings["ca_col_sel_grouping_title"].LangText,
            section = "",
            columns = resultColumnConfig1,
        };
        result.Add(resultList);
        result.Add(resultList1);

        res.ColumnSelectorSection = result;
        res.columnSelectorTitle = climateActionLangStrings["CA_climateActions"].LangText;
        res.columnSelectorDescription = climateActionLangStrings["climate_colsel_description"].LangText;
        return res;
    }




    public async Task<ColumnSelector> GetEmissionGridColumnsAsync(string userId, List<string> allColumns, int budgetYear)
    {
        UserData user = await _utility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(user.language_preference, user.user_name, "ClimateAction");
        Dictionary<string, clsLanguageString> langStringsCommon = await _utility.GetLanguageStringsAsync(user.language_preference, user.user_name, "Ng_Common");

        List<ColumnSelectorColumn> columnsconfig = new List<ColumnSelectorColumn>();
        TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
        string flagName = "CLIMATE_EMISSION_GRID_COL_VIEW";
        string flagKeyId = "-1";

        var targetStatusColumnConfig = await dbContext.tco_application_flag.Where(x =>
            x.fk_tenant_id == user.tenant_id && x.flag_name == flagName && x.budget_year == budgetYear &&
            x.flag_key_id == flagKeyId).ToListAsync();

        var tenantColumnConfig = targetStatusColumnConfig.Any() ? targetStatusColumnConfig.FirstOrDefault(x => x.flag_key_id == flagKeyId.ToString()) : null;

        List<string> defaultSelectedCol = allColumns;

        if (tenantColumnConfig != null)
        {
            Guid? flagGuid = tenantColumnConfig.flag_guid;

            if (flagGuid != null)
            {
                clsTenantCloudTableEntity updateEntity = await _utility.GetCloudTableContentAsync("WADTenantData", user.tenant_id.ToString(), flagGuid.ToString());
                if (updateEntity != null)
                {
                    JArray columnsArray = JArray.FromObject(JsonConvert.DeserializeObject(updateEntity.data.ToString()));

                    columnsconfig = (from s in columnsArray
                        select new ColumnSelectorColumn
                        {
                            key = (string)s["key"],
                            value = (string)s["value"],
                            isChecked = (bool)s["isChecked"],
                        }).ToList();
                }
            }
        }
        var columnsList = new List<ColumnSelectorColumn>();

        foreach (var item in allColumns)

        {
            bool isChecked = false;
            if (tenantColumnConfig != null)
            {
                if (columnsconfig.Count > 0 && columnsconfig.Select(x => x.key).Contains(item.ToString()))
                {
                    isChecked = columnsconfig.FirstOrDefault(x => x.key == item.ToString()).isChecked;
                }
            }
            else
            {
                if (defaultSelectedCol.Contains(item.ToString()))

                {
                    isChecked = true;
                }
            }

            ColumnSelectorColumn columnInfo = new ColumnSelectorColumn()
            {
                key = item.ToString(),
                value = "",
                isChecked = isChecked
            };

            switch (item.ToString())
            {
                case "sectorId":
                    columnInfo.value = langStrings["FP_sector"].LangText;
                    columnsList.Add(columnInfo);
                    break;

                case "sourceId":
                    columnInfo.value = langStrings["FP_source"].LangText;
                    columnsList.Add(columnInfo);
                    break;

                case "emissionTypeId":
                    columnInfo.value = langStrings["FP_emissionType"].LangText;
                    columnsList.Add(columnInfo);
                    break;

                case "departmentCode":
                    columnInfo.value = langStrings["CA_department"].LangText;
                    columnsList.Add(columnInfo);
                    break;

                case "functionCode":
                    columnInfo.value = langStrings["CA_function"].LangText;
                    columnsList.Add(columnInfo);
                    break;

                case "projectCode":
                    columnInfo.value = langStrings["CA_project"].LangText;
                    columnsList.Add(columnInfo);
                    break;

                case "freedim1":
                    columnInfo.value = langStrings["CA_Object"].LangText;
                    columnsList.Add(columnInfo);
                    break;

                case "reductionQuantity":
                    columnInfo.value = langStrings["CA_Reduction_Quantity"].LangText;
                    columnsList.Add(columnInfo);
                    break;

                case "reductionPrevYear2":
                    columnInfo.value = langStrings["FP_reductionYear"].LangText + " " + (budgetYear - 2);
                    columnsList.Add(columnInfo);
                    break;

                case "reductionPrevYear1":
                    columnInfo.value = langStrings["FP_reductionYear"].LangText + " " + (budgetYear - 1);
                    columnsList.Add(columnInfo);
                    break;

                case "reductionYear1":
                    columnInfo.value = langStrings["FP_reductionYear"].LangText + " " + budgetYear;
                    columnsList.Add(columnInfo);
                    break;

                case "reductionYear2":
                    columnInfo.value = langStrings["FP_reductionYear"].LangText + " " + (budgetYear + 1);
                    columnsList.Add(columnInfo);
                    break;

                case "reductionYear3":
                    columnInfo.value = langStrings["FP_reductionYear"].LangText + " " + (budgetYear + 2);
                    columnsList.Add(columnInfo);
                    break;

                case "reductionYear4":
                    columnInfo.value = langStrings["FP_reductionYear"].LangText + " " + (budgetYear + 3);
                    columnsList.Add(columnInfo);
                    break;

                case "reductionYear5":
                    columnInfo.value = langStrings["FP_reductionYear"].LangText + " " + (budgetYear + 4);
                    columnsList.Add(columnInfo);
                    break;

                case "reductionYear6":
                    columnInfo.value = langStrings["FP_reductionYear"].LangText + " " + (budgetYear + 5);
                    columnsList.Add(columnInfo);
                    break;

                case "reductionYear7":
                    columnInfo.value = langStrings["FP_reductionYear"].LangText + " " + (budgetYear + 6);
                    columnsList.Add(columnInfo);
                    break;

                case "reductionYear8":
                    columnInfo.value = langStrings["FP_reductionYear"].LangText + " " + (budgetYear + 7);
                    columnsList.Add(columnInfo);
                    break;

                case "reductionYear9":
                    columnInfo.value = langStrings["FP_reductionYear"].LangText + " " + (budgetYear + 8);
                    columnsList.Add(columnInfo);
                    break;

                case "reductionYear10":
                    columnInfo.value = langStrings["FP_reductionYear"].LangText + " " + (budgetYear + 9);
                    columnsList.Add(columnInfo);
                    break;

                case "longTermReduction":
                    columnInfo.value = langStrings["CA_longTermReduction"].LangText;
                    columnsList.Add(columnInfo);
                    break;
            }
        }
        var result = new List<ColumnSelectorSection>
        {
            new ColumnSelectorSection
            {
                name = langStrings["climate_emission"].LangText,
                section = "",
                columns = columnsList
            }
        };

        var colSel = new ColumnSelector
        {
            ColumnSelectorSection = result,
            columnSelectorTitle = langStringsCommon["cm_column_sel"].LangText,
            columnSelectorDescription = langStrings["climate_colsel_description"].LangText
        };
        return colSel;
    }



    public async Task SaveEmissionGridColumnsAsync(string userId, int budgetYear, ColumnSelector colSelObj)
    {
        var columnsList = new List<ColumnSelectorColumn>();
        foreach (var item in colSelObj.ColumnSelectorSection)
        {
            columnsList.AddRange(item.columns);
        }
        string selectedColumns = JsonConvert.SerializeObject(columnsList);
        await _utility.SaveColumnsConfigAsync(userId, "CLIMATE_EMISSION_GRID_COL_VIEW", selectedColumns, -1, budgetYear);
    }


    public async Task<string> SaveColumnSelector(string userId, int budgetYear, ColumnSelector colSelObj, Guid categoryId)
    {
        var flatcolumList = new List<ColumnSelectorColumn>();
        foreach (var item in colSelObj.ColumnSelectorSection)
        {
            flatcolumList.AddRange(item.columns);
        }

        String strJsonColumnSet = JsonConvert.SerializeObject(flatcolumList);
        return await _utility.SaveColumnsConfigAsync(userId, "CLIMATE_ACTION_COLUMNS_VIEW_" + categoryId, strJsonColumnSet, -1, budgetYear);
    }



    public async Task<string> SaveClimateFinanceColumnSelector(string userId, int budgetYear, ColumnSelector colSelObj)
    {
        var flatcolumList = new List<ColumnSelectorColumn>();
        foreach (var item in colSelObj.ColumnSelectorSection)
        {
            flatcolumList.AddRange(item.columns);
        }

        String strJsonColumnSet = JsonConvert.SerializeObject(flatcolumList);
        return await _utility.SaveColumnsConfigAsync(userId, "CLIMATE_FINANCE_COLUMNS_VIEW", strJsonColumnSet, -1, budgetYear);
    }



    public async Task<ClimateActionGridHelper> GetClimateActionsBaseGridData(string userId, int budgetYear, ClimateActionHelper filters)
    {
        StringBuilder sb = new StringBuilder();
        sb.AppendLine($"GetClimateActionData started @ {DateTime.UtcNow}");

        ClimateActionGridHelper gridResult = new ClimateActionGridHelper();
        TenantDBContext tenantdbContext = await _utility.GetTenantDBContextAsync();

        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));
        Dictionary<string, clsLanguageString> climateActionLangStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ClimateAction");
        Dictionary<string, clsLanguageString> langStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "OpportunityAssessment");

        string paramValueFp2 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_2");
        bool isServiceSetup = (!string.IsNullOrEmpty(paramValueFp2) && (paramValueFp2.StartsWith("ser")));
        bool isChapterSetup = await _finUtility.isChapterSetup(userId);

        var tenantOrgData = await _unitOfWork.BudgetProposalRepository.GetMainTenantBasedonSubTenantAsync(userDetails.tenant_id);
        ClsOrgVersionSpecificContent orgHierarchy = tenantOrgData != null ? await _utility.GetOrgVersionBasedOnTenantAsync(tenantOrgData.fk_tenant_id, _utility.GetForecastPeriod(budgetYear, 1)) : new ClsOrgVersionSpecificContent();

        //Get climate data
        List<ClimateActionFilterHelper> climateData = await GetClimateActionDataSet(userId, budgetYear, filters);
        List<KeyValueInt> planClimateActionsTagInfo = await GetPlanClimateActionsTagsInfo(userId, budgetYear, climateData);
        var userData = tenantdbContext.vwUserDetails.Where(x => x.tenant_id == userDetails.tenant_id);

        //Check to show/hide standared column config button based on role
        var userRoleIds = (await _utility.GetUserRoleIdsAsync(userId)).ToList();
        gridResult.showStandaredSaveButton = userRoleIds.Any(x => x == 1 || x == 2 || x == 13);
        gridResult.headerTooltipText = climateActionLangStrings["FP_griDescriptionTooltip"].LangText;

        List<Guid> goalsPresentInData = climateData.Select(x => x.goalId).ToList();

        var goalsList = await GetGoalInfo(userDetails, tenantdbContext, budgetYear, goalsPresentInData);
        List<string> unsGoals = goalsList.Where(x => !string.IsNullOrEmpty(x.unsd_goals)).Select(x => x.unsd_goals).ToList();
        List<string> unsGoalIds = new List<string>();
        foreach (string id in unsGoals)
        {
            unsGoalIds.AddRange(id.Split(',').Where(x => !string.IsNullOrEmpty(x)).ToList());
        }
        var unsGoalInfo = await GetUnsGoalInfo(tenantdbContext, unsGoalIds.Distinct().ToList());

        climateData = FilterClimateActionsData(climateData, filters, goalsList, planClimateActionsTagInfo);

        var tagsList = await (from atg in tenantdbContext.tcoActionTags
            where atg.FkTenantId == userDetails.tenant_id
            select new
            {
                key = atg.PkId,
                value = atg.TagDescription
            }).AsNoTracking().OrderBy(x => x.key).ToListAsync();

        List<tco_progress_status> syncStatusData = await _unitOfWork.BudgetProposalRepository.GetSyncStatusDropDown(userDetails.tenant_id, SyncObjectStatusType.SYNC_CLIMATEACTION_STATUS);

        List<ClimateActionGridDataHelper> dataList = new List<ClimateActionGridDataHelper>();
        int itemCount = 0;
        var indicatorConnectedClimateActionIds = await _unitOfWork.ClimateRepository.GetConnectedIndicatorClimateIds(userDetails.tenant_id, budgetYear);

        UniqClimateActionFilterDSHelper climateActionUniqDS = new UniqClimateActionFilterDSHelper();
        // fetch finance amounts sum 
        var climateActionIds = climateData.Select(x => x.climateActionId).ToList();
        ClimateActionInvestmentInput inputData = new ClimateActionInvestmentInput { budgetYear = budgetYear, orgId = filters.orgId, orgLevel = filters.orgLevel, serviceId = filters.serviceId, serviceLevel = filters.serviceLevel };
        var connectedActionInvestments = await GetListOfActionsAndInvestments(userId, inputData);
        var financeAmountsforClimateIds = await GetClimateFinanceAmounts(userDetails.tenant_id, budgetYear, connectedActionInvestments);
        foreach (var item in climateData)
        {
            ClimateActionGridDataHelper rowInfo = new ClimateActionGridDataHelper();
            string unsGoalTag = string.Empty;
            var goalsDetail = GetGoalsDetail(item.goalId, goalsList, unsGoalInfo).Where(x => !string.IsNullOrEmpty(x.ShortDesc));
            string goalDesc = goalsDetail.Any() ? goalsDetail.FirstOrDefault(x => x.Key).ShortDesc : string.Empty;
            string ungoal = string.Empty;
            climateActionUniqDS.ConnectedGoalIds.Add(goalDesc == string.Empty ? "-" : goalDesc);
            var financeAmounts = financeAmountsforClimateIds.Where(x => x.climateActionId == item.climateActionId).FirstOrDefault();
            foreach (var g in goalsDetail.Where(x => !x.Key).OrderBy(x => x.LongDesc).ToList())
            {
                var unGoalIdIfo = g.LongDesc.Split('-').ToList();
                string unGoalShortName = unGoalIdIfo[0] + "- " + g.ShortDesc;
                ungoal = ungoal + (g.ShortDesc) + "<hr>";
                unsGoalTag = unsGoalTag + "<span class ='bp-blue-tag hand-pointer b-word' style='max-width:150px;overflow:hidden;' title='" + g.LongDesc + "'>" + unGoalShortName + "</span><br>";//;text-overflow:ellipsis
                climateActionUniqDS.UniqUNGoalNames.Add(unGoalShortName);
            }
            ungoal = !string.IsNullOrEmpty(ungoal) ? ungoal.Remove(ungoal.Length - "<hr>".Length) : ungoal;
            if (!string.IsNullOrEmpty(filters.unsGoal) && !(goalsDetail.Any(x => !x.Key && x.ShortDesc.ToLower().Contains(filters.unsGoal.ToLower()))))
            {
                continue;
            }
            var subTransferredClimateActions = await _unitOfWork.BudgetTransferRepository.GetTransferredClimateActions(userDetails.tenant_id);
            bool isTransferredClimateAction = subTransferredClimateActions.Contains(item.climateActionId);
            if (isTransferredClimateAction)
            {

                string orgName = orgHierarchy.lstOrgDataLevel1 != null && orgHierarchy.lstOrgDataLevel1.Any() && orgHierarchy.lstOrgDataLevel1[0] != null ?
                    orgHierarchy.lstOrgDataLevel1[0].org_name_1 : String.Empty;
                rowInfo.orgCreatedAt = "<span class='org-created-tag'>" + orgName + "</span>";
                climateActionUniqDS.UniqCreatedFromValues.Add(orgName);
            }
            else
            {
                if (item.orgLevel != 0 && !string.IsNullOrEmpty(item.orgId))
                {
                    var orgNameCreatedAt = GetOrgNameCreatedAt(orgVersionContent, item.orgLevelCreatedAt, item.orgIdCreatedAt);
                    if (!string.IsNullOrEmpty(orgNameCreatedAt))
                    {
                        rowInfo.orgCreatedAt = "<span class='org-created-tag'>" + orgNameCreatedAt + "</span>";
                        climateActionUniqDS.UniqCreatedFromValues.Add(orgNameCreatedAt);
                    }
                }
            }
            if (itemCount == 0 || climateData[itemCount].climateActionId != climateData[itemCount - 1].climateActionId)
            {
                rowInfo.climateActionName = item.climateActionName;
                rowInfo.description = item.description;
                rowInfo.financeDescription = item.financeDescription;
                rowInfo.cost = item.cost.ToString();
                rowInfo.totalCost = item.totalCost.ToString();
                rowInfo.isParent = true;
                rowInfo.tags = item.tags;
                rowInfo.longTermEconomicImpact = item.longtermEconomicImpact.ToString();
                rowInfo.referenceUrl = string.IsNullOrEmpty(item.referenceUrl) ? item.referenceUrl : "<a href=" + item.referenceUrl + " target=_blank>" + item.referenceUrl + "</a>";
                rowInfo.securityEstimate = item.securityEstimate;
                rowInfo.feasibility = item.feasibility;
                rowInfo.investment = item.investment;
                rowInfo.finplanAction = item.finplanAction;
                rowInfo.planActionSpecInfo = GetPlanActionSpecificInfo(item, (int)filters.planSpecificInfoFilterId, climateActionLangStrings);
                rowInfo.statusInPlan = (!item.isFromPlan) ? climateActionLangStrings["CA_Not_Relevant"].LangText : GetPlanStatusDescriptionTooltipText(item.planStatusDesc, userData, item.updated, item.updatedBy, item.climateActionName);
                rowInfo.planStatusDescId = item.planStatusDescId;
                rowInfo.showPlanStatusDescIcon = item.isFromPlan && !string.IsNullOrEmpty(item.planStatusDesc);
            }
            else
            {
                rowInfo.climateActionName = string.Empty;
                rowInfo.description = string.Empty;
                rowInfo.financeDescription = string.Empty;
                rowInfo.cost = string.Empty;
                rowInfo.totalCost = string.Empty;
                rowInfo.isParent = false;
                rowInfo.longTermEconomicImpact = string.Empty;
                rowInfo.referenceUrl = string.Empty;
                rowInfo.securityEstimate = string.Empty;
                rowInfo.feasibility = string.Empty;
                rowInfo.investment = string.Empty;
                rowInfo.finplanAction = string.Empty;
                rowInfo.planActionSpecInfo = string.Empty;
                rowInfo.statusInPlan = string.Empty;
                rowInfo.planStatusDescId = Guid.Empty;
                rowInfo.showPlanStatusDescIcon = false;
                rowInfo.tags = string.Empty;
            }
            rowInfo.convertToAssignment = false;
            rowInfo.isAssignment = item.isAssignment;
            //Disable add assignment option once we add an assignment for that action
            rowInfo.disableAssignmentCheckbox = item.isAssignment;
            rowInfo.sector = item.sector;
            rowInfo.source = item.source;
            rowInfo.reductionYear1 = item.reductionYear1;
            rowInfo.reductionYear2 = item.reductionYear2;
            rowInfo.reductionYear3 = item.reductionYear3;
            rowInfo.reductionYear4 = item.reductionYear4;
            rowInfo.reductionYear5 = item.reductionYear5;
            rowInfo.reductionYear6 = item.reductionYear6;
            rowInfo.reductionYear7 = item.reductionYear7;
            rowInfo.reductionYear8 = item.reductionYear8;
            rowInfo.reductionYear9 = item.reductionYear9;
            rowInfo.reductionYear10 = item.reductionYear10;
            rowInfo.reductionPrevYear1 = item.reductionPrevYear1Value;
            rowInfo.reductionPrevYear2 = item.reductionPrevYear2Value;
            rowInfo.longTermReduction = item.longTermReduction;
            rowInfo.climateActionId = item.climateActionId;
            rowInfo.descriptionId = item.descriptionId;
            rowInfo.financeDescriptionId = item.financeDescriptionId;
            rowInfo.sectorId = item.sectorId;
            rowInfo.sourceId = item.sourceId;
            rowInfo.reductionQuantity = item.reductionQuantity;
            rowInfo.reductionQuantityName = item.reductionQuantityName;
            rowInfo.syncStatus = syncStatusData.FirstOrDefault(x => x.status_id == item.syncStatus)?.status_description;
            rowInfo.isParentLevelCreatedAction = item.parentClimateId == 0 && filters.orgId == item.orgId ? (isServiceSetup ? (filters.serviceId == item.serviceId) : (isChapterSetup ? filters.attributeId == item.attributeId : true)) : false;
            rowInfo.isActionConnectedToIndicator = indicatorConnectedClimateActionIds.Contains(item.climateActionId);
            rowInfo.financeSumYear1 = financeAmounts != null ? financeAmounts.firstYearSum : 0;
            rowInfo.financeSumYear4 = financeAmounts != null ? financeAmounts.fourYearSum : 0;
            rowInfo.financeSumYear10 = financeAmounts != null ? financeAmounts.tenYearSum : 0;
            //Add tag for action status
            string statusName = string.Empty;
            string tagList = string.Empty;

            //Add plan name as tag if action is from plan
            var planInfo = planClimateActionsTagInfo.FirstOrDefault(x => x.Key == item.climateActionId);
            if (planInfo != null && !string.IsNullOrEmpty(planInfo.Value))
            {
                rowInfo.isPlan = true;
                rowInfo.planTitle = planInfo.Value;
            }
            else
            {
                rowInfo.isPlan = false;
                rowInfo.planTitle = string.Empty;
            }

            switch (item.actionStatus)
            {
                case (int)ClimateActionStatusType.Finplan:
                    statusName = langStrings["action_inclided_in_FP"].LangText;
                    rowInfo.climateStatus = climateActionLangStrings["ca_included_in_FP"].LangText;
                    rowInfo.climateStatusType = 0;
                    break;
                case (int)ClimateActionStatusType.Blist:
                    statusName = langStrings["action_inclided_in_BList"].LangText;
                    rowInfo.climateStatus = climateActionLangStrings["ca_included_in_BList"].LangText;
                    rowInfo.climateStatusType = 1;
                    break;
                case (int)ClimateActionStatusType.Parked:
                    statusName = langStrings["action_Parked"].LangText;
                    rowInfo.climateStatus = climateActionLangStrings["ca_parked"].LangText;
                    rowInfo.climateStatusType = 2;
                    break;
                default:
                    statusName = langStrings["action_Deleted"].LangText;
                    rowInfo.climateStatus = climateActionLangStrings["ca_deleted"].LangText;
                    rowInfo.climateStatusType = 3;
                    break;
            }

            //tags for 'tag'
            if (!string.IsNullOrEmpty(rowInfo.tags) && tagsList.Count > 0 && rowInfo.tags.Split(',').Any())
            {
                foreach (var tag in rowInfo.tags.Split(','))
                {
                    if (!string.IsNullOrEmpty(tag) && tagsList.FirstOrDefault(x => x.key == int.Parse(tag)) != null)
                    {
                        var tagDesc = tagsList.FirstOrDefault(x => x.key == int.Parse(tag)).value;
                        if (!string.IsNullOrEmpty(tagDesc))
                        {
                            climateActionUniqDS.UniqTags.Add(tagDesc);
                            tagList = tagList + "<span class ='f-13 br-5 ps-1 pe-1 selected-tags me-1 pe-auto b-word' style='max-width:200px;overflow:hidden;text-overflow:ellipsis;' title='" + tagDesc + "'>" + tagDesc + "</span><br>";
                        }
                    }
                }
            }
            rowInfo.statusName = statusName;
            rowInfo.tags = (itemCount == 0 || climateData[itemCount].climateActionId != climateData[itemCount - 1].climateActionId) ? tagList : "-";
            rowInfo.goal = (itemCount == 0 || climateData[itemCount].climateActionId != climateData[itemCount - 1].climateActionId) ? goalDesc : string.Empty;
            rowInfo.unsGoal = (itemCount == 0 || climateData[itemCount].climateActionId != climateData[itemCount - 1].climateActionId) ? ungoal : string.Empty;
            rowInfo.unsGoalTag = unsGoalTag;
            itemCount++;
            ProcessRowInfo(climateActionUniqDS, rowInfo);
            dataList.Add(rowInfo);
        }

        var filterDataSources = FormatActionGridFilter(climateActionUniqDS);
            
        if (climateData.Any())
        {
            var sumRow = GetActionsGridSumRow(dataList);
            sumRow.climateActionName = climateActionLangStrings["Climate_grid_total"].LangText;
            dataList.Add(sumRow);
        }
        List<string> climateActionNameList = await GetClimateActionNames(userDetails, budgetYear);

        //Show button that allows userId to add action as assignment only if column 'actionToAssignment' is present.
        gridResult.showCreateAssignmentButton = itemCount > 0 /*&& columnFields.Contains("actionToAssignment")*/;
        gridResult.data = dataList;
        gridResult.climateActionNameList = climateActionNameList;
        gridResult.filterDataSources = filterDataSources;

        //Display empty grid to add new actions if there is no data
        sb.AppendLine($"GetClimateActionData finished @ {DateTime.UtcNow}");
        await InsertPerformanceLog(userId, sb.ToString(), "GetClimateActionData");

        return gridResult;

    }



    public async Task<List<KeyValuePair>> GetClimateActionColumns(string userId, int budgetYear, Guid categoryId, bool getOnlyTenantConfig, bool isPublish)
    {
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> climateActionLangStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ClimateAction");
        List<KeyValuePair> columnsToDisplayList = new List<KeyValuePair>();
        List<KeyValuePair> columnsconfig = new List<KeyValuePair>();
        List<string> allColumns = new List<string>();
        TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
        string flagName = "CLIMATE_ACTION_COLUMNS_VIEW_" + categoryId;

        var climateColumnConfig = await dbContext.tco_application_flag.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.flag_name == flagName && x.budget_year == budgetYear && (x.flag_key_id == "-1" || x.flag_key_id == userDetails.pk_id.ToString())).ToListAsync();

        var tenantColumnConfig = climateColumnConfig.Any() ? climateColumnConfig.FirstOrDefault(x => x.flag_key_id == "-1") : null;
        var userColumnConfig = climateColumnConfig.Any() ? climateColumnConfig.FirstOrDefault(x => x.flag_key_id == userDetails.pk_id.ToString()) : null;

        //Get only standard config for document
        if (getOnlyTenantConfig)
        {
            userColumnConfig = null;
        }
        List<string> defaultSelectedCol = new List<string>() { "cost", "totalCost", "longTermEconomicImpact", "sector", "source", "reductionPrevYear2", "reductionPrevYear1", "reductionYear1", "reductionYear2", "reductionYear3",
            "reductionYear4", "reductionYear5", "reductionYear6", "reductionYear7", "reductionYear8", "reductionYear9", "reductionYear10", "longTermReduction", "actionToAssignment" };

        if (tenantColumnConfig != null || userColumnConfig != null)
        {
            Guid? flagGuid = userColumnConfig != null ? userColumnConfig.flag_guid : tenantColumnConfig.flag_guid;

            if (flagGuid != null)
            {
                clsTenantCloudTableEntity updateEntity = await _utility.GetCloudTableContentAsync("WADTenantData", userDetails.tenant_id.ToString(), flagGuid.ToString());
                if (updateEntity != null && !string.IsNullOrEmpty(updateEntity.data) && updateEntity.data != "null")
                {
                    JArray columnsArray = JArray.FromObject(JsonConvert.DeserializeObject(updateEntity.data.ToString()));

                    columnsconfig = (from s in columnsArray
                        select new KeyValuePair
                        {
                            key = (string)s["key"],
                            value = (string)s["value"],
                            isChecked = (bool)s["isChecked"],
                        }).ToList();
                }
            }
        }
        string title = string.Empty;
        bool isChecked = true;
        if (isPublish)
        {
            allColumns.AddRange(new string[] {  "goal", "unsGoal", "finplanAction", "investment", "cost", "totalCost", "financeSumYear1", "financeSumYear4", "financeSumYear10", "longTermEconomicImpact", "securityEstimate", "feasibility",
                "referenceUrl", "sector", "source", "reductionQuantityName", "reductionPrevYear2", "reductionPrevYear1", "reductionYear1",
                "reductionYear2", "reductionYear3", "reductionYear4", "reductionYear5", "reductionYear6", "reductionYear7", "reductionYear8",
                "reductionYear9","reductionYear10","longTermReduction", "planActionSpecInfo", "statusInPlan", "description", "financeDescription", "actionToAssignment" });
        }
        else
        {
            allColumns.AddRange(new string[] {  "goal", "unsGoal", "finplanAction", "investment", "cost", "totalCost", "longTermEconomicImpact", "securityEstimate", "feasibility",
                "referenceUrl", "sector", "source", "reductionQuantityName", "reductionPrevYear2", "reductionPrevYear1", "reductionYear1",
                "reductionYear2", "reductionYear3", "reductionYear4", "reductionYear5", "reductionYear6", "reductionYear7", "reductionYear8",
                "reductionYear9","reductionYear10","longTermReduction", "planActionSpecInfo", "statusInPlan", "description", "financeDescription", "actionToAssignment" });
        }
            

        foreach (var item in allColumns)
        {
            isChecked = false;
            switch (item.ToString())
            {
                case "cost":
                    title = climateActionLangStrings["FP_cost"].LangText + " " + budgetYear;
                    break;

                case "totalCost":
                    title = climateActionLangStrings["FP_totalCost"].LangText + " " + budgetYear + "-" + (budgetYear + 3);
                    break;

                case "longTermEconomicImpact":
                    title = climateActionLangStrings["CA_Long_term_economic_impact"].LangText;
                    break;

                case "sector":
                    title = climateActionLangStrings["FP_sector"].LangText;
                    break;

                case "source":
                    title = climateActionLangStrings["FP_source"].LangText;
                    break;

                case "reductionPrevYear1":
                    title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear - 1);
                    break;

                case "reductionPrevYear2":
                    title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear - 2);
                    break;

                case "reductionYear1":
                    title = climateActionLangStrings["FP_reductionYear"].LangText + " " + budgetYear;
                    break;

                case "reductionYear2":
                    title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 1);
                    break;

                case "reductionYear3":
                    title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 2);
                    break;

                case "reductionYear4":
                    title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 3);
                    break;

                case "reductionYear5":
                    title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 4);
                    break;

                case "reductionYear6":
                    title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 5);
                    break;

                case "reductionYear7":
                    title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 6);
                    break;

                case "reductionYear8":
                    title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 7);
                    break;

                case "reductionYear9":
                    title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 8);
                    break;

                case "reductionYear10":
                    title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 9);
                    break;

                case "description":
                    title = climateActionLangStrings["ClimateAction_FP_description"].LangText;
                    break;

                case "financeDescription":
                    title = climateActionLangStrings["FP_financeDescription"].LangText;
                    break;

                case "goal":
                    title = climateActionLangStrings["FP_CA_goal"].LangText;
                    break;

                case "securityEstimate":
                    title = climateActionLangStrings["FP_CA_securityEstimate"].LangText;
                    break;

                case "feasibility":
                    title = climateActionLangStrings["FP_CA_feasibility"].LangText;
                    break;

                case "referenceUrl":
                    title = climateActionLangStrings["FP_CA_referenceUrl"].LangText;
                    break;

                case "unsGoal":
                    title = climateActionLangStrings["FP_CA_unsGoal"].LangText;
                    break;

                case "actionToAssignment":
                    title = climateActionLangStrings["CA_action_as_assignment"].LangText;
                    break;

                case "finplanAction":
                    title = climateActionLangStrings["CA_Finplan_Action_Name"].LangText;
                    break;

                case "investment":
                    title = climateActionLangStrings["CA_Investment_Name"].LangText;
                    break;

                case "planActionSpecInfo":
                    title = climateActionLangStrings["CA_planActionSpecInfo_Name"].LangText;
                    break;

                case "statusInPlan":
                    title = climateActionLangStrings["CA_statusInPlan_Name"].LangText;
                    break;

                case "reductionQuantityName":
                    title = climateActionLangStrings["CA_Reduction_Quantity"].LangText;
                    break;

                case "longTermReduction":
                    title = climateActionLangStrings["FP_longTermReduction"].LangText;
                    break;
                case "financeSumYear1":
                    title = climateActionLangStrings["ca_finance_sum1"].LangText;
                    break;
                case "financeSumYear4":
                    title = climateActionLangStrings["ca_finance_sum4"].LangText;
                    break;
                case "financeSumYear10":
                    title = climateActionLangStrings["ca_finance_sum10"].LangText;
                    break;
                default:
                    break;
            }

            if (tenantColumnConfig != null || userColumnConfig != null)
            {
                if (columnsconfig.Count > 0 && columnsconfig.Select(x => x.key).Contains(item.ToString()))
                {
                    isChecked = columnsconfig.FirstOrDefault(x => x.key == item.ToString()).isChecked;
                }
            }
            else
            {
                if (defaultSelectedCol.Contains(item.ToString()))
                {
                    isChecked = true;
                }
            }

            KeyValuePair columnInfo = new KeyValuePair()
            {
                key = item.ToString(),
                value = title,
                isChecked = isChecked
            };
            columnsToDisplayList.Add(columnInfo);
        }
        return columnsToDisplayList;
    }



    public async Task SaveClimateActionColumns(string userId, BPTargetGoalColSelConfigHelper columnSelectorInput)
    {
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        string selectedColumns = JsonConvert.SerializeObject(columnSelectorInput.selectedColumns);
        string flagName = "CLIMATE_ACTION_COLUMNS_VIEW_" + columnSelectorInput.categoryId;
        if (columnSelectorInput.reset)
        {
            await _bpTarget.DeleteUserSpecificColumnConfigAsync(userId, flagName, columnSelectorInput.budgetYear);
        }
        else
        {
            if (columnSelectorInput.isTenantSpecificColConfigSave)
            {
                await _bpTarget.DeleteUserSpecificColumnConfigAsync(userId, flagName, columnSelectorInput.budgetYear);
            }
            await _utility.SaveColumnsConfigAsync(userId, flagName, selectedColumns, columnSelectorInput.isTenantSpecificColConfigSave ? -1 : userDetails.pk_id, columnSelectorInput.budgetYear);
        }
    }



    public async Task<List<KeyValuePair>> GetClimateActionDetailGridColumns(string userId, int budgetYear, Guid categoryId, bool isDoc)
    {
        UserData userDetail = await _utility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> climateActionLangStrings = await _utility.GetLanguageStringsAsync(userDetail.language_preference, userDetail.user_name, "ClimateAction");
        //Get the freedim data for climate action screen
        IEnumerable<freedimDefinition> freeDimDef = (await _utility.GetFreeDimDefinitionsBasedOnPageIdAsync(userId, string.Empty)).ToList();

        List<KeyValuePair> columnsToDisplayList = new List<KeyValuePair>();
        List<KeyValuePair> columnsconfig = new List<KeyValuePair>();
        TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
        string flagName = "CLIMATE_ACTION_DETAIL_COLMS_VIEW_" + categoryId;

        var climateColumnConfig = await dbContext.tco_application_flag.Where(x => x.fk_tenant_id == userDetail.tenant_id && x.flag_name == flagName && x.budget_year == budgetYear && (x.flag_key_id == "-1" || x.flag_key_id == userDetail.pk_id.ToString())).ToListAsync();

        var tenantColumnConfig = climateColumnConfig.Any() ? climateColumnConfig.FirstOrDefault(x => x.flag_key_id == "-1") : null;
        var userColumnConfig = !isDoc && climateColumnConfig.Any() ? climateColumnConfig.FirstOrDefault(x => x.flag_key_id == userDetail.pk_id.ToString()) : null;

        List<string> defaultSelectedCol = new List<string>() { "projectData.fk_project_code", "popupReductionPrevYear2", "popupReductionPrevYear1", "popupReductionYear1", "popupReductionYear2", "popupReductionYear3", "popupReductionYear4",
            "popupReductionYear5", "popupReductionYear6", "popupReductionYear7", "popupReductionYear8", "popupReductionYear9", "popupReductionYear10", "longTermReduction" };

        if (tenantColumnConfig != null || userColumnConfig != null)
        {
            Guid? flagGuid = userColumnConfig != null ? userColumnConfig.flag_guid : tenantColumnConfig.flag_guid;

            if (flagGuid != null)
            {
                clsTenantCloudTableEntity updateEntity = await _utility.GetCloudTableContentAsync("WADTenantData", userDetail.tenant_id.ToString(), flagGuid.ToString());
                if (updateEntity != null)
                {
                    JArray columnsArray = JArray.FromObject(JsonConvert.DeserializeObject(updateEntity.data.ToString()));

                    columnsconfig = (from s in columnsArray
                        select new KeyValuePair
                        {
                            key = (string)s["key"],
                            value = (string)s["value"],
                            isChecked = (bool)s["isChecked"],
                        }).ToList();
                }
            }
        }
        string title = string.Empty;
        bool isChecked = false;
        List<string> allColumns = new List<string>() { "projectData.fk_project_code", "freedim1Data.fk_freedim_code", "freedim2Data.fk_freedim_code", "freedim3Data.fk_freedim_code", "freedim4Data.fk_freedim_code", "reductionQuantity",
            "popupReductionPrevYear2", "popupReductionPrevYear1", "popupReductionYear1", "popupReductionYear2", "popupReductionYear3", "popupReductionYear4", "popupReductionYear5", "popupReductionYear6", "popupReductionYear7",
            "popupReductionYear8", "popupReductionYear9", "popupReductionYear10", "longTermReduction" };

        foreach (var item in allColumns)
        {
            switch (item.ToString())
            {
                case "projectData.fk_project_code":
                    title = climateActionLangStrings["CA_project"].LangText;
                    break;

                case "freedim1Data.fk_freedim_code":
                    title = freeDimDef.FirstOrDefault(x => x.freeDimColumn == "free_dim_1") == null ? string.Empty : freeDimDef.FirstOrDefault(x => x.freeDimColumn == "free_dim_1").freeDimHeader;
                    break;

                case "freedim2Data.fk_freedim_code":
                    title = freeDimDef.FirstOrDefault(x => x.freeDimColumn == "free_dim_2") == null ? string.Empty : freeDimDef.FirstOrDefault(x => x.freeDimColumn == "free_dim_2").freeDimHeader;
                    break;

                case "freedim3Data.fk_freedim_code":
                    title = freeDimDef.FirstOrDefault(x => x.freeDimColumn == "free_dim_3") == null ? string.Empty : freeDimDef.FirstOrDefault(x => x.freeDimColumn == "free_dim_3").freeDimHeader;
                    break;

                case "freedim4Data.fk_freedim_code":
                    title = freeDimDef.FirstOrDefault(x => x.freeDimColumn == "free_dim_4") == null ? string.Empty : freeDimDef.FirstOrDefault(x => x.freeDimColumn == "free_dim_4").freeDimHeader;
                    break;

                case "popupReductionPrevYear1":
                    title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear - 1);
                    break;

                case "popupReductionPrevYear2":
                    title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear - 2);
                    break;

                case "popupReductionYear1":
                    title = climateActionLangStrings["FP_reductionYear"].LangText + " " + budgetYear;
                    break;

                case "popupReductionYear2":
                    title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 1);
                    break;

                case "popupReductionYear3":
                    title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 2);
                    break;

                case "popupReductionYear4":
                    title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 3);
                    break;

                case "popupReductionYear5":
                    title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 4);
                    break;

                case "popupReductionYear6":
                    title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 5);
                    break;

                case "popupReductionYear7":
                    title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 6);
                    break;

                case "popupReductionYear8":
                    title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 7);
                    break;

                case "popupReductionYear9":
                    title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 8);
                    break;

                case "popupReductionYear10":
                    title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 9);
                    break;

                case "reductionQuantity":
                    title = climateActionLangStrings["CA_Reduction_Quantity"].LangText;
                    break;

                default:
                    title = climateActionLangStrings["CA_longTermReduction"].LangText;
                    break;
            }

            if (tenantColumnConfig != null || userColumnConfig != null)
            {
                if (columnsconfig.Count > 0 && columnsconfig.Select(x => x.key).Contains(item.ToString()))
                {
                    isChecked = columnsconfig.FirstOrDefault(x => x.key == item.ToString()).isChecked;
                }
            }
            else
            {
                if (defaultSelectedCol.Contains(item.ToString()))
                {
                    isChecked = true;
                }
            }

            if (!string.IsNullOrEmpty(title))
            {
                KeyValuePair columnInfo = new KeyValuePair()
                {
                    key = item.ToString(),
                    value = title,
                    isChecked = isChecked
                };
                columnsToDisplayList.Add(columnInfo);
            }
        }
        return columnsToDisplayList;
    }



    public async Task SaveClimateActionDetailGridColumns(string userId, BPTargetGoalColSelConfigHelper columnSelectorInput)
    {
        UserData userDetail = await _utility.GetUserDetailsAsync(userId);
        string selectedColumns = JsonConvert.SerializeObject(columnSelectorInput.selectedColumns);
        string flagName = "CLIMATE_ACTION_DETAIL_COLMS_VIEW_" + columnSelectorInput.categoryId;
        if (columnSelectorInput.reset)
        {
            await _bpTarget.DeleteUserSpecificColumnConfigAsync(userId, flagName, columnSelectorInput.budgetYear);
        }
        else
        {
            if (columnSelectorInput.isTenantSpecificColConfigSave)
            {
                await _bpTarget.DeleteUserSpecificColumnConfigAsync(userId, flagName, columnSelectorInput.budgetYear);
            }
            await _utility.SaveColumnsConfigAsync(userId, flagName, selectedColumns, columnSelectorInput.isTenantSpecificColConfigSave ? -1 : userDetail.pk_id, columnSelectorInput.budgetYear);
        }
    }



    private async Task<dynamic> GetDynamicGridColumns(string userId, int budgetYear, List<string> columnFields)
    {
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        Dictionary<string, clsLanguageString> climateActionLangStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "ClimateAction");
        Dictionary<string, clsLanguageString> climatelanguageStrings = await _utility.GetLanguageStringsAsync(userDetails.language_preference, userDetails.user_name, "MonthlyReport");

        columnFields.Insert(0, "climateActionName");
        columnFields.Add("delete");
        int colCount = 0;
        dynamic columns = new JArray();
        bool IsReductionColumnPresent = false;
        List<string> reductionColumns = new List<string>() { "reductionPrevYear2", "reductionPrevYear1", "reductionYear1", "reductionYear2", "reductionYear3", "reductionYear4",
            "reductionYear5", "reductionYear6", "reductionYear7", "reductionYear8","reductionYear9","reductionYear10", "longTermReduction" };
        int reductionColumnsPosition = columnFields.Count(x => x == "description" || x == "financeDescription" || x == "planActionSpecInfo" || x == "statusInPlan" || x == "actionToAssignment" || x == "delete");
        bool isRedColumnHeaderInserted = false;
        dynamic redColumnsArr = new JArray();
        dynamic redColumnsObj = new JObject();
        foreach (var columnField in columnFields)
        {
            dynamic column = new JObject();
            if (!reductionColumns.Contains(columnField))
            {
                column.field = columnField;
                column.colCount = colCount;
                column.encoded = false;
                column.hidden = false;
                column.format = null;
                column.expandable = false;

                dynamic attributes = new JObject();
                dynamic headerAttributes = new JObject();

                if (columnField == "climateActionName")
                {
                    column.title = climateActionLangStrings["FP_climateActionName"].LangText;
                    column.template = "<div class='col-md-12 padding0 title-items'><span class='col-md-11 padding0' style='color: \\#000'> # if(climateActionName !== '' && climateActionId != -1) {# <a ng-click=climateController.saveGridPosition('#=climateActionId#','#=categoryId#')>#: climateActionName #</a> #}else{# #: climateActionName # # }#</span></div><span class='col-md-12 padding0'>#=statusName#</span><br/>";
                    column.width = 230;
                    column.footerTemplate = climateActionLangStrings["Climate_grid_total"].LangText;
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0;background:white;";
                    column.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:left; border-left:none;";
                    column.headerAttributes = headerAttributes;
                    attributes.style = "text-align:left; border-left:none;vertical-align: top;border-top:# if(isParent === true){# block; #}else{#none;#}#";
                    column.attributes = attributes;
                }
                else if (columnField == "description")
                {
                    column.title = climateActionLangStrings["ClimateAction_FP_description"].LangText;
                    column.template = "# if( description.length < 100) {#<div class='climate-desc' style='height:70px;width:200px;border-radius:5px;'>#:MODULES.stripHtmlTags(description)#</div>#}else{#<div class='climate-desc' style='height:100px;border-radius:5px;'>#: MODULES.stripHtmlTags(description).substring(0, 85)#...<a id='description_#:climateActionId#'> <span class='hand-pointer' <span class='hand-pointer' ng-click=climateController.climateTooltipInfo('description_#: climateActionId#','#= escape(description) #')>" + climatelanguageStrings["read_more"].LangText + " </span></span></a></div>#}#";
                    column.width = 180;
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border-left:0; border-right:0; text-align:right; background:white;padding-right: 15px;padding-left: 0px;";
                    column.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:left;";
                    column.headerAttributes = headerAttributes;
                    attributes.style = "text-align:left;vertical-align:top;border-left:none;border-top:# if(isParent === true){# block; #}else{#none;#}#";
                    column.attributes = attributes;
                }
                else if (columnField == "financeDescription")
                {
                    column.title = climateActionLangStrings["FP_financeDescription"].LangText;
                    column.template = "# if( financeDescription.length < 100) {#<div class='climate-financeDesc' style='height:70px;width:200px;border-radius:5px;'>#:MODULES.stripHtmlTags(financeDescription)#</div>#}else{#<div class='climate-financeDesc' style='height:100px;border-radius:5px;'>#: MODULES.stripHtmlTags(financeDescription).substring(0, 85)#...<a id='financeDescription_#:climateActionId#'><span class='hand-pointer' <span class='hand-pointer' ng-click=climateController.climateTooltipInfo('financeDescription_#: climateActionId#','#= escape(financeDescription) #')>" + climatelanguageStrings["read_more"].LangText + " </span></span></a></div>#}#";
                    column.width = 200;
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0; text-align:right; background:white;padding-right: 15px;padding-left: 0px;";
                    column.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:left;";
                    column.headerAttributes = headerAttributes;
                    attributes.style = "text-align:left;vertical-align:top;border-left:none;border-top:# if(isParent === true){# block; #}else{#none;#}#";
                    column.attributes = attributes;
                }
                else if (columnField == "cost")
                {
                    column.title = climateActionLangStrings["FP_cost"].LangText + " " + budgetYear;
                    column.width = 100;
                    column.template = "#= (cost === '') ? '' : kendo.toString(parseInt(cost), 'n0')  #";
                    column.footerTemplate = "#= (kendo.toString(parseInt(sum), 'n0')) #";
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0; text-align:right; background:white;padding-right: 15px;padding-left: 0px;";
                    column.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    attributes.style = "border-left: none; text-align:right; vertical-align: top;padding-right:15px; padding-left:0px;border-top:# if(isParent === true){# block; #}else{#none;#}#";
                    column.attributes = attributes;
                }
                else if (columnField == "totalCost")
                {
                    column.title = climateActionLangStrings["FP_totalCost"].LangText + " " + budgetYear + "-" + (budgetYear + 3);
                    column.width = 110;
                    column.template = "#= (totalCost === '') ? '' :  kendo.toString(parseInt(totalCost), 'n0')  #";
                    column.footerTemplate = "#= (kendo.toString(parseInt(sum), 'n0')) #";
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0;text-align:right;background:white;padding-right:15px;padding-left:0px;";
                    column.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    attributes.style = "border-left: none; text-align:right;vertical-align: top;width:100px;padding-right:15px;padding-left:0px;border-top:# if(isParent === true){# block; #}else{#none;#}#";
                    column.attributes = attributes;
                }
                else if (columnField == "longTermEconomicImpact")
                {
                    column.title = climateActionLangStrings["CA_Long_term_economic_impact"].LangText;
                    column.width = 100;
                    column.template = "#= (longTermEconomicImpact == null) ? '0' : kendo.toString(longTermEconomicImpact, 'n1') #";
                    column.footerTemplate = "#= (kendo.toString(sum, 'n1')) #";
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0;text-align:right;padding-right:20px;padding-left:0px;background:white;";
                    column.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:right;padding-right:20px;padding-left:0px;border-left: none;";
                    column.headerAttributes = headerAttributes;
                    attributes.style = "border-left: none; text-align:right;width:100px;vertical-align: top;padding-right:20px;padding-left:0px;border-top:# if(isParent === true){# block; #}else{#none;#}#";
                    column.attributes = attributes;
                }
                else if (columnField == "goal")
                {
                    column.title = climateActionLangStrings["FP_CA_goal"].LangText;
                    column.width = 100;
                    column.template = "#= goalDescription #</span></div><span class='col-md-12 padding0'>#=unsGoalTag#</span><br/>";
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0; text-align:right; background:white;padding-right: 15px;padding-left: 0px;";
                    column.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:left;";
                    column.headerAttributes = headerAttributes;
                    attributes.style = "text-align:left;vertical-align:top;border-left:none;border-top:# if(isParent === true){# block; #}else{#none;#}#";
                    column.attributes = attributes;
                }
                else if (columnField == "unsGoal")
                {
                    column.title = climateActionLangStrings["FP_CA_unsGoal"].LangText;
                    column.width = 100;
                    column.template = "#= unsGoal #";
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0; text-align:right; background:white;padding-right: 15px;padding-left: 0px;";
                    column.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:left;";
                    column.headerAttributes = headerAttributes;
                    attributes.style = "text-align:left;vertical-align:top;border-left:none;border-top:# if(isParent === true){# block; #}else{#none;#}#";
                    column.attributes = attributes;
                }
                else if (columnField == "securityEstimate")
                {
                    column.title = climateActionLangStrings["FP_CA_securityEstimate"].LangText;
                    column.width = 100;
                    column.template = "#= securityEstimate #";
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0; text-align:right; background:white;padding-right: 15px;padding-left: 0px;";
                    column.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:left;";
                    column.headerAttributes = headerAttributes;
                    attributes.style = "text-align:left;vertical-align:top;border-left:none;border-top:# if(isParent === true){# block; #}else{#none;#}#";
                    column.attributes = attributes;
                }
                else if (columnField == "feasibility")
                {
                    column.title = climateActionLangStrings["FP_CA_feasibility"].LangText;
                    column.width = 100;
                    column.template = "#= feasibility #";
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0; text-align:right; background:white;padding-right: 15px;padding-left: 0px;";
                    column.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:left;";
                    column.headerAttributes = headerAttributes;
                    attributes.style = "text-align:left;vertical-align:top;border-left:none;border-top:# if(isParent === true){# block; #}else{#none;#}#";
                    column.attributes = attributes;
                }
                else if (columnField == "finplanAction")
                {
                    column.title = climateActionLangStrings["CA_Finplan_Action_Name"].LangText;
                    column.width = 100;
                    column.template = "#= finplanAction #";
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0; text-align:right; background:white;padding-right: 15px;padding-left: 0px;";
                    column.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:left;";
                    column.headerAttributes = headerAttributes;
                    attributes.style = "text-align:left;vertical-align:top;border-left:none;border-top:# if(isParent === true){# block; #}else{#none;#}#";
                    column.attributes = attributes;
                }
                else if (columnField == "investment")
                {
                    column.title = climateActionLangStrings["CA_Investment_Name"].LangText;
                    column.width = 100;
                    column.template = "#= investment #";
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0; text-align:right; background:white;padding-right: 15px;padding-left: 0px;";
                    column.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:left;";
                    column.headerAttributes = headerAttributes;
                    attributes.style = "text-align:left;vertical-align:top;border-left:none;border-top:# if(isParent === true){# block; #}else{#none;#}#";
                    column.attributes = attributes;
                }
                else if (columnField == "referenceUrl")
                {
                    column.title = climateActionLangStrings["FP_CA_referenceUrl"].LangText;
                    column.width = 100;
                    column.template = "# if( referenceUrl.length < 18) {#<div style='height:70px;width:200px;border-radius:5px;'>#:MODULES.stripHtmlTags(referenceUrl)#</div>#}else{#<div style='height:100px;border-radius:5px;'>#: MODULES.stripHtmlTags(referenceUrl).substring(0, 10)#...<a id='referenceUrl_#:climateActionId#'> <span class='hand-pointer' <span class='hand-pointer' ng-click=climateController.climateTooltipInfo('referenceUrl_#: climateActionId#','#= escape(referenceUrl) #')>" + climatelanguageStrings["read_more"].LangText + " </span></span></a></div>#}#";
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0; text-align:right; background:white;padding-right: 15px;padding-left: 0px;";
                    column.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:left;";
                    column.headerAttributes = headerAttributes;
                    attributes.style = "text-align:left;vertical-align:top;border-left:none;border-top:# if(isParent === true){# block; #}else{#none;#}#";
                    column.attributes = attributes;
                }
                else if (columnField == "sector")
                {
                    column.title = climateActionLangStrings["FP_sector"].LangText;
                    column.width = 100;
                    column.template = null;
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0;text-align:right; background:white;padding-right: 15px;padding-left: 0px;";
                    column.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:left; border-left:none; white-space:nowrap; padding-left:10px; padding-right:10px;";
                    column.headerAttributes = headerAttributes;
                    attributes.style = "text-align:left; border-left:none; vertical-align: top;padding-left: 10px; padding-right:15px;";
                    column.attributes = attributes;
                }
                else if (columnField == "source")
                {
                    column.title = climateActionLangStrings["FP_source"].LangText;
                    column.width = 100;
                    column.template = null;
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0;text-align:right; background:white;padding-right: 15px;padding-left: 0px;";
                    column.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:left; border-left:none; white-space:nowrap;padding-right:15px;";
                    column.headerAttributes = headerAttributes;
                    attributes.style = "text-align:left; border-left:none; vertical-align: top;";
                    column.attributes = attributes;
                }
                else if (columnField == "reductionQuantityName")
                {
                    column.title = climateActionLangStrings["CA_Reduction_Quantity"].LangText;
                    column.width = 100;
                    column.template = null;
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0;text-align:right; background:white;padding-right: 15px;padding-left: 0px;";
                    column.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:left; border-left:none; white-space:nowrap;padding-right:15px;";
                    column.headerAttributes = headerAttributes;
                    attributes.style = "text-align:left; border-left:none; vertical-align: top;";
                    column.attributes = attributes;
                }
                else if (columnField == "actionToAssignment")
                {
                    column.title = climateActionLangStrings["CA_action_as_assignment"].LangText;
                    column.template = "# if(isParent === true && disableAssignmentCheckbox == false){#<span class=\"col-md-11 padding0 align-center climate-assignment\"><input type='checkbox' type =\"checkbox\" name=\"actionAssignmentCheckbox\" id=\"climateActionAssignment_#:uid#\" ng-click =\"climateController.actionAssignmentCheck(this)\" class=\"chkbx\" #= isAssignment ? checked='checked' : ''#/></span>#}else{#<span class=\"col-md-11 padding0 align-center climate-assignment\"><input type='checkbox' type =\"checkbox\" id=\"climateActionAssignment_#:uid#\" ng-disabled = true class=\"chkbx\" #= isAssignment ? checked='checked' : ''#/></span>#}#";
                    column.width = 80;
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0;text-align:right; background:white;";
                    column.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:center;border-left:none;width:30px;";
                    column.headerAttributes = headerAttributes;
                    attributes.style = "text-align:right;white-space:normal;border-left:none;vertical-align:top;border-top:# if(isParent === true){# block; #}else{#none;#}#";
                    column.attributes = attributes;
                }
                else if (columnField == "delete")
                {
                    column.title = climateActionLangStrings["FP_delete"].LangText;
                    column.template = "# if(isParent === true){#<span class=\"col-md-9 padding0 align-center\"><a class=\"bup-res-delete\" ng-click=climateController.confirmDeleteClimateData(this,#: climateActionId #)><img src=\"../images/close_small.png\"/></a></span>#}else{null}#";
                    column.width = 55;
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0;text-align:right; background:white;";
                    column.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:center;border-left:none;width:30px;";
                    column.headerAttributes = headerAttributes;
                    attributes.style = "text-align:right;white-space:normal;border-left:none;vertical-align:top;border-top:# if(isParent === true){# block; #}else{#none;#}#";
                    column.attributes = attributes;
                }
                else if (columnField == "planActionSpecInfo")
                {
                    column.title = climateActionLangStrings["CA_planActionSpecInfo_Name"].LangText;
                    column.width = 100;
                    column.template = "#= planActionSpecInfo #";
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0; text-align:right; background:white;padding-right: 15px;padding-left: 0px;";
                    column.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:left;";
                    column.headerAttributes = headerAttributes;
                    attributes.style = "text-align:left;vertical-align:top;border-left:none;border-top:# if(isParent === true){# block; #}else{#none;#}#";
                    column.attributes = attributes;
                }
                else if (columnField == "statusInPlan")
                {
                    column.title = climateActionLangStrings["CA_statusInPlan_Name"].LangText;
                    column.width = 100;
                    column.template = "# if(showPlanStatusDescIcon === true) {# <div class='row assignment-items'><div class='col-md-10 padding0 align-center mouse-hover-popup cursor' id='popUpStatusDescPlan_#:climateActionId#'><img src = '../images/monthly_report_mouse_over_txt.png' ng-click=climateController.showContentForPlanStatus('#=climateActionId#','#=escape(statusInPlan)#','popUpStatusDescPlan_')></div> #}else{# #: statusInPlan # # } # ";
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0; text-align:right; background:white;padding-right: 15px;padding-left: 0px;";
                    column.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:left;";
                    column.headerAttributes = headerAttributes;
                    attributes.style = "text-align:left;vertical-align:top;border-left:none;border-top:# if(isParent === true){# block; #}else{#none;#}#";
                    column.attributes = attributes;
                }
                dynamic filterable = new JObject();
                dynamic cell = new JObject();
                cell.showOperators = false;
                cell.dataSource = new JArray();
                cell.operators = "contains";
                filterable.cell = cell;
                column.filterable = filterable;
                column.headerTemplate = null;
                column.locked = null;
                columns.Add(column);
            }
            else if (reductionColumns.Contains(columnField))
            {
                IsReductionColumnPresent = true;
                if (!isRedColumnHeaderInserted)
                {
                    dynamic headerAttributes1 = new JObject();
                    headerAttributes1.Add("class", "table-header-cell font14 font-family-regular");
                    redColumnsObj.headerAttributes = headerAttributes1;
                    redColumnsObj.headerTemplate = "<span>" + climateActionLangStrings["FP_gridReductionYear_info"].LangText + "</div>";
                }
                dynamic redColumn = new JObject();
                dynamic attributes = new JObject();
                dynamic headerAttributes = new JObject();
                redColumn.field = columnField;
                redColumn.colCount = colCount;
                redColumn.encoded = false;
                redColumn.hidden = false;
                redColumn.format = null;
                redColumn.expandable = false;

                if (columnField == "reductionYear1")
                {
                    redColumn.title = climateActionLangStrings["FP_reductionYear"].LangText + " " + budgetYear;
                    redColumn.width = 100;
                    redColumn.template = "#= (reductionYear1 == null) ? '0' :  kendo.toString(reductionYear1, 'n1')  #";
                    redColumn.footerTemplate = "#= (kendo.toString(parseInt(sum), 'n1')) #";
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0;text-align:right; background:white;";
                    redColumn.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    redColumn.headerAttributes = headerAttributes;
                    attributes.style = "border-left: none; text-align:right;width:95px;vertical-align: top;";
                    redColumn.attributes = attributes;
                }
                else if (columnField == "reductionYear2")
                {
                    redColumn.title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 1);
                    redColumn.width = 100;
                    redColumn.template = "#= (reductionYear2 == null) ? '0' :  kendo.toString(reductionYear2, 'n1')  #";
                    redColumn.footerTemplate = "#= (kendo.toString(sum, 'n1')) #";
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0;text-align:right; background:white;";
                    redColumn.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    redColumn.headerAttributes = headerAttributes;
                    attributes.style = "border-left: none; text-align:right;vertical-align: top;";
                    redColumn.attributes = attributes;
                }
                else if (columnField == "reductionYear3")
                {
                    redColumn.title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 2);
                    redColumn.width = 100;
                    redColumn.template = "#= (reductionYear3 == null) ? '0' :  kendo.toString(reductionYear3, 'n1')  #";
                    redColumn.footerTemplate = "#= (kendo.toString(sum, 'n1')) #";
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0;text-align:right; background:white;";
                    redColumn.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    redColumn.headerAttributes = headerAttributes;
                    attributes.style = "border-left: none; text-align:right;vertical-align: top;";
                    redColumn.attributes = attributes;
                }
                else if (columnField == "reductionYear4")
                {
                    redColumn.title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 3);
                    redColumn.width = 100;
                    redColumn.template = "#= (reductionYear4 == null) ? '0' :  kendo.toString(reductionYear4, 'n1')  #";
                    redColumn.footerTemplate = "#= (kendo.toString(sum, 'n1')) #";
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0; text-align:right; background:white;";
                    redColumn.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    redColumn.headerAttributes = headerAttributes;
                    attributes.style = "border-left: none; text-align:right;vertical-align: top;";
                    redColumn.attributes = attributes;
                }
                else if (columnField == "reductionYear5")
                {
                    redColumn.title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 4);
                    redColumn.width = 100;
                    redColumn.template = "#= (reductionYear5 == null) ? '0' :  kendo.toString(reductionYear5, 'n1')  #";
                    redColumn.footerTemplate = "#= (kendo.toString(sum, 'n1')) #";
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0; text-align:right; background:white;";
                    redColumn.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    redColumn.headerAttributes = headerAttributes;
                    attributes.style = "border-left: none; text-align:right;vertical-align: top;";
                    redColumn.attributes = attributes;
                }
                else if (columnField == "reductionYear6")
                {
                    redColumn.title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 5);
                    redColumn.width = 100;
                    redColumn.template = "#= (reductionYear6 == null) ? '0' :  kendo.toString(reductionYear6, 'n1')  #";
                    redColumn.footerTemplate = "#= (kendo.toString(sum, 'n1')) #";
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0; text-align:right; background:white;";
                    redColumn.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    redColumn.headerAttributes = headerAttributes;
                    attributes.style = "border-left: none; text-align:right;vertical-align: top;";
                    redColumn.attributes = attributes;
                }
                else if (columnField == "reductionYear7")
                {
                    redColumn.title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 6);
                    redColumn.width = 100;
                    redColumn.template = "#= (reductionYear7 == null) ? '0' :  kendo.toString(reductionYear7, 'n1')  #";
                    redColumn.footerTemplate = "#= (kendo.toString(sum, 'n1')) #";
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0; text-align:right; background:white;";
                    redColumn.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    redColumn.headerAttributes = headerAttributes;
                    attributes.style = "border-left: none; text-align:right;vertical-align: top;";
                    redColumn.attributes = attributes;
                }
                else if (columnField == "reductionYear8")
                {
                    redColumn.title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 7);
                    redColumn.width = 100;
                    redColumn.template = "#= (reductionYear8 == null) ? '0' :  kendo.toString(reductionYear8, 'n1')  #";
                    redColumn.footerTemplate = "#= (kendo.toString(sum, 'n1')) #";
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0; text-align:right; background:white;";
                    redColumn.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    redColumn.headerAttributes = headerAttributes;
                    attributes.style = "border-left: none; text-align:right;vertical-align: top;";
                    redColumn.attributes = attributes;
                }
                else if (columnField == "reductionYear9")
                {
                    redColumn.title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 8);
                    redColumn.width = 100;
                    redColumn.template = "#= (reductionYear9 == null) ? '0' :  kendo.toString(reductionYear9, 'n1')  #";
                    redColumn.footerTemplate = "#= (kendo.toString(sum, 'n1')) #";
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0; text-align:right; background:white;";
                    redColumn.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    redColumn.headerAttributes = headerAttributes;
                    attributes.style = "border-left: none; text-align:right;vertical-align: top;";
                    redColumn.attributes = attributes;
                }
                else if (columnField == "reductionYear10")
                {
                    redColumn.title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear + 9);
                    redColumn.width = 100;
                    redColumn.template = "#= (reductionYear10 == null) ? '0' :  kendo.toString(reductionYear10, 'n1')  #";
                    redColumn.footerTemplate = "#= (kendo.toString(sum, 'n1')) #";
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0; text-align:right; background:white;";
                    redColumn.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    redColumn.headerAttributes = headerAttributes;
                    attributes.style = "border-left: none; text-align:right;vertical-align: top;";
                    redColumn.attributes = attributes;
                }
                else if (columnField == "reductionPrevYear1")
                {
                    redColumn.title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear - 1);
                    redColumn.width = 100;
                    redColumn.template = "#= (reductionPrevYear1 == null) ? '0' :  kendo.toString(reductionPrevYear1, 'n1')  #";
                    redColumn.footerTemplate = "#= (kendo.toString(sum, 'n1')) #";
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0; text-align:right; background:white;";
                    redColumn.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    redColumn.headerAttributes = headerAttributes;
                    attributes.style = "border-left: none; text-align:right;vertical-align: top;";
                    redColumn.attributes = attributes;
                }
                else if (columnField == "reductionPrevYear2")
                {
                    redColumn.title = climateActionLangStrings["FP_reductionYear"].LangText + " " + (budgetYear - 2);
                    redColumn.width = 100;
                    redColumn.template = "#= (reductionPrevYear2 == null) ? '0' :  kendo.toString(reductionPrevYear2, 'n1')  #";
                    redColumn.footerTemplate = "#= (kendo.toString(sum, 'n1')) #";
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0; text-align:right; background:white;";
                    redColumn.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    redColumn.headerAttributes = headerAttributes;
                    attributes.style = "border-left: none; text-align:right;vertical-align: top;";
                    redColumn.attributes = attributes;
                }
                else
                {
                    redColumn.title = climateActionLangStrings["FP_longTermReduction"].LangText;
                    redColumn.width = 100;
                    redColumn.template = "#= (longTermReduction == null) ? '0' :  kendo.toString(longTermReduction, 'n1')  #";
                    redColumn.footerTemplate = "#= (kendo.toString(sum, 'n1')) #";
                    dynamic footerAttributes = new JObject();
                    footerAttributes.style = "border:0; text-align:right; background:white;";
                    redColumn.footerAttributes = footerAttributes;

                    headerAttributes.style = "text-align:right;border-left: none;";
                    redColumn.headerAttributes = headerAttributes;
                    attributes.style = "border-left: none; text-align:right;vertical-align: top;";
                    redColumn.attributes = attributes;
                }
                dynamic filterable = new JObject();
                dynamic cell = new JObject();
                cell.showOperators = false;
                cell.dataSource = new JArray();
                cell.operators = "contains";
                filterable.cell = cell;
                redColumn.filterable = filterable;
                redColumn.headerTemplate = null;
                redColumn.locked = null;
                redColumnsArr.Add(redColumn);
            }
            colCount++;
        }
        if (IsReductionColumnPresent)
        {
            redColumnsObj.columns = redColumnsArr;
            columns.Insert(columns.Count - reductionColumnsPosition, redColumnsObj);
        }
        return columns;
    }

}