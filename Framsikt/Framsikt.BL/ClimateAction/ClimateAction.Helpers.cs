using Framsikt.BL.Core;
using Framsikt.BL.Helpers;
using Framsikt.BL.Repository;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using System.Data;
using System.Globalization;
using System.Text;

namespace Framsikt.BL;

public partial class ClimateAction : IClimateAction
{

    private ClimateActionFilterDataSourceHelper FormatActionGridFilter(UniqClimateActionFilterDSHelper climateActionUniqDS)
    {
        var filterDataSources = new ClimateActionFilterDataSourceHelper();
        filterDataSources.GoalDS.AddRange(climateActionUniqDS.ConnectedGoalIds.Select(goal => new climateFilterDataHelper { Key = string.Empty, Value = goal }));
        filterDataSources.UNGoalDS.AddRange(climateActionUniqDS.UniqUNGoalNames.Select(unGoal => new climateFilterDataHelper { Key = string.Empty, Value = unGoal }));
        filterDataSources.ActionDS.AddRange(climateActionUniqDS.UniqActions.Select(action => new climateFilterDataHelper { Key = string.Empty, Value = action }));
        filterDataSources.InvestmentDS.AddRange(climateActionUniqDS.UniqInvestments.Select(inv => new climateFilterDataHelper { Key = string.Empty, Value = inv }));
        filterDataSources.SecurityEstimatesDS.AddRange(climateActionUniqDS.UniqSecurityEstimates.Select(secEst => new climateFilterDataHelper { Key = string.Empty, Value = secEst }));
        filterDataSources.FeasibilityDS.AddRange(climateActionUniqDS.UniqFeasibility.Select(feas => new climateFilterDataHelper { Key = string.Empty, Value = feas }));
        filterDataSources.SourceDS.AddRange(climateActionUniqDS.UniqSources.Select(sourc => new climateFilterDataHelper { Key = string.Empty, Value = sourc }));
        filterDataSources.SectorDS.AddRange(climateActionUniqDS.UniqSectors.Select(sect => new climateFilterDataHelper { Key = string.Empty, Value = sect }));
        filterDataSources.ReducQuantityDS.AddRange(climateActionUniqDS.UniqReducQuanNames.Select(redQuan => new climateFilterDataHelper { Key = string.Empty, Value = redQuan }));
        filterDataSources.PlanActionInfoDS.AddRange(climateActionUniqDS.UniqPlanSpecInfo.Select(planAction => new climateFilterDataHelper { Key = string.Empty, Value = planAction }));
        filterDataSources.StatusInPlanDS.AddRange(climateActionUniqDS.UniqStatusInPlan.Select(status => new climateFilterDataHelper { Key = string.Empty, Value = status }));
        filterDataSources.ClimateActionDS.AddRange(climateActionUniqDS.UniqClimateActions.Select(act => new climateFilterDataHelper { Key = string.Empty, Value = act }));
        filterDataSources.TagDS.AddRange(climateActionUniqDS.UniqTags.Select(tag => new climateFilterDataHelper { Key = string.Empty, Value = tag }));
        filterDataSources.CreatedFromDS.AddRange(climateActionUniqDS.UniqCreatedFromValues.Select(creat => new climateFilterDataHelper { Key = string.Empty, Value = creat }));
        return filterDataSources;
    }


    private void ProcessRowInfo(UniqClimateActionFilterDSHelper climateActionDetails, ClimateActionGridDataHelper rowInfo)
    {
        rowInfo.goal = string.IsNullOrEmpty(rowInfo.goal) ? "-" : rowInfo.goal;
        rowInfo.unsGoalTag = string.IsNullOrEmpty(rowInfo.unsGoalTag) ? "-" : rowInfo.unsGoalTag;
        rowInfo.finplanAction = string.IsNullOrEmpty(rowInfo.finplanAction) ? "-" : rowInfo.finplanAction;
        rowInfo.investment = string.IsNullOrEmpty(rowInfo.investment) ? "-" : rowInfo.investment;
        rowInfo.securityEstimate = string.IsNullOrEmpty(rowInfo.securityEstimate) ? "-" : rowInfo.securityEstimate;
        rowInfo.feasibility = string.IsNullOrEmpty(rowInfo.feasibility) ? "-" : rowInfo.feasibility;
        rowInfo.source = string.IsNullOrEmpty(rowInfo.source) ? "-" : rowInfo.source;
        rowInfo.sector = string.IsNullOrEmpty(rowInfo.sector) ? "-" : rowInfo.sector;
        rowInfo.reductionQuantityName = string.IsNullOrEmpty(rowInfo.reductionQuantityName) ? "-" : rowInfo.reductionQuantityName;
        rowInfo.statusInPlan = string.IsNullOrEmpty(rowInfo.statusInPlan) ? "-" : rowInfo.statusInPlan;
        rowInfo.climateStatus = string.IsNullOrEmpty(rowInfo.climateStatus) ? "-" : rowInfo.climateStatus;
        rowInfo.tags = string.IsNullOrEmpty(rowInfo.tags) ? "-" : rowInfo.tags;
        rowInfo.planTitle = string.IsNullOrEmpty(rowInfo.planTitle) ? "-" : rowInfo.planTitle;

        climateActionDetails.UniqActions.Add(rowInfo.finplanAction);
        climateActionDetails.UniqInvestments.Add(rowInfo.investment);
        climateActionDetails.UniqSecurityEstimates.Add(rowInfo.securityEstimate);
        climateActionDetails.UniqFeasibility.Add(rowInfo.feasibility);
        climateActionDetails.UniqSources.Add(rowInfo.source);
        climateActionDetails.UniqSectors.Add(rowInfo.sector);
        climateActionDetails.UniqReducQuanNames.Add(rowInfo.reductionQuantityName);
        climateActionDetails.UniqPlanSpecInfo.Add(rowInfo.planActionSpecInfo);
        climateActionDetails.UniqStatusInPlan.Add(rowInfo.statusInPlan);
        climateActionDetails.UniqClimateActions.Add(rowInfo.climateActionName);
    }


    private async Task<List<string>> GetClimateActionNames(UserData userDetails,int budgetYear)
    {
        TenantDBContext tenantdbContext = await _utility.GetTenantDBContextAsync();
        return await tenantdbContext.TcoClimateActionHeader.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear).Select(x => x.climate_action_name).Distinct().ToListAsync();
    }



    private async Task<List<string>> InsertActionTags(string userId, List<TagData> tagsData)
    {
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        TenantDBContext dbContext = await _utility.GetTenantDBContextAsync();
        List<string> lstTags = tagsData.Where(x => x.KeyId >= 100).Select(x => x.KeyId.ToString()).ToList();
        foreach (var tagItem in tagsData.Where(x => x.KeyId < 100).ToList())
        {
            TcoActionTags tagData = await GetActionTags(userDetails.tenant_id, tagItem.ValueString.Trim(), dbContext);

            if (tagData == null)
            {
                TcoActionTags tat = new TcoActionTags();
                tat.FkTenantId = userDetails.tenant_id;
                tat.TagDescription = tagItem.ValueString.Trim();
                tat.Updated = DateTime.UtcNow;
                tat.UpdatedBy = userDetails.pk_id;
                dbContext.tcoActionTags.Add(tat);
                await dbContext.SaveChangesAsync();
                tagData = await GetActionTags(userDetails.tenant_id, tagItem.ValueString.Trim(), dbContext);
                lstTags.Add(tagData.PkId.ToString());
            }
            else
            {
                lstTags.Add(tagData.PkId.ToString());
            }
        }
        return lstTags;
    }



    private static async Task<TcoActionTags> GetActionTags(int tenantId, string key, TenantDBContext dbContext)
    {
        return await dbContext.tcoActionTags.FirstOrDefaultAsync(x => x.TagDescription.Trim() == key && x.FkTenantId == tenantId);
    }



    private static async Task<List<tco_goals>> GetGoalInfo(UserData userDetails, TenantDBContext tenantdbContext, int budgetYear, List<Guid> goalIds)
    {
        List<tco_goals> goalsList = await (from g in tenantdbContext.tco_goals
            where g.fk_tenant_id == userDetails.tenant_id && g.budget_year == budgetYear && !g.is_busplan_goal
                  && goalIds.Contains(g.pk_goal_id)
            select g).AsNoTracking().Distinct().ToListAsync();

        return goalsList;
    }



    private static async Task<List<gco_un_susdev_goals>> GetUnsGoalInfo(TenantDBContext tenantdbContext, List<string> unsGoalIds)
    {
        List<gco_un_susdev_goals> unsGoalsList = await (from g in tenantdbContext.gco_un_susdev_goals
            where unsGoalIds.Contains(g.pk_goal_id)
            select g).AsNoTracking().Distinct().ToListAsync();

        return unsGoalsList;
    }



    private async Task<List<KeyValuePairString>> GetTagsInfo(UserData userDetails, TenantDBContext tenantdbContext)
    {
        var tagsList = await (from atg in tenantdbContext.tcoActionTags
            where atg.FkTenantId == userDetails.tenant_id
            select new KeyValuePairString
            {
                Key = atg.PkId.ToString(),
                Value = atg.TagDescription
            }).AsNoTracking().OrderBy(x => x.Key).ToListAsync();

        return tagsList;
    }



    private ClimateActionSaveHelper FormatClimateHeaderDataToSave(DataRow row, List<KeyValueInt> columnsInfo, List<ClimateActionColSpecHelper> validData)
    {
        ClimateActionSaveHelper dataToSave = new ClimateActionSaveHelper();
        decimal revenueYear1 = 0, revenueYear2 = 0, revenueYear3 = 0, revenueYear4 = 0;
        decimal expenditureYear1 = 0, expenditureYear2 = 0, expenditureYear3 = 0, expenditureYear4 = 0;
        revenueYear1 = GetValidAmount(row, columnsInfo, "revenueYear1");
        revenueYear2 = GetValidAmount(row, columnsInfo, "revenueYear2");
        revenueYear3 = GetValidAmount(row, columnsInfo, "revenueYear3");
        revenueYear4 = GetValidAmount(row, columnsInfo, "revenueYear4");
        expenditureYear1 = GetValidAmount(row, columnsInfo, "expenditureYear1");
        expenditureYear2 = GetValidAmount(row, columnsInfo, "expenditureYear2");
        expenditureYear3 = GetValidAmount(row, columnsInfo, "expenditureYear3");
        expenditureYear4 = GetValidAmount(row, columnsInfo, "expenditureYear4");
        string categoryVal = row[columnsInfo.FirstOrDefault(x => x.Value == "category").Key].ToString();
        var categoryData = validData.FirstOrDefault(x => x.columnId == "category").validData;
        int statusCode = -1;
        if (!string.IsNullOrEmpty(row[columnsInfo.FirstOrDefault(x => x.Value == "status").Key].ToString()))
        {
            statusCode = int.Parse(row[columnsInfo.FirstOrDefault(x => x.Value == "status").Key].ToString());
        }
        dataToSave.climateActionId = string.IsNullOrEmpty(row[0].ToString()) ? 0 : int.Parse(row[0].ToString());
        Guid categoryId = !categoryData.ContainsKey(categoryVal) ? Guid.Empty : Guid.Parse(categoryData.FirstOrDefault(x => x.Key == categoryVal).Value);
        dataToSave.categoryId = categoryId;
        dataToSave.climateActionName = row[columnsInfo.FirstOrDefault(x => x.Value == "climateActionName").Key].ToString();
        dataToSave.actionStatus = statusCode;
        dataToSave.tagsIds = string.Empty;
        //We usually allow userId to enter only -ve & +ve amounts to revenue & expenditures resply. If userId enters wrong values make it right and insert into db
        dataToSave.year1amount = revenueYear1 <= 0 ? revenueYear1 : (-1) * revenueYear1;
        dataToSave.year2amount = revenueYear2 <= 0 ? revenueYear2 : (-1) * revenueYear2;
        dataToSave.year3amount = revenueYear3 <= 0 ? revenueYear3 : (-1) * revenueYear3;
        dataToSave.year4amount = revenueYear4 <= 0 ? revenueYear4 : (-1) * revenueYear4;
        dataToSave.expense_year1 = expenditureYear1 >= 0 ? expenditureYear1 : (-1) * expenditureYear1;
        dataToSave.expense_year2 = expenditureYear2 >= 0 ? expenditureYear2 : (-1) * expenditureYear2;
        dataToSave.expense_year3 = expenditureYear3 >= 0 ? expenditureYear3 : (-1) * expenditureYear3;
        dataToSave.expense_year4 = expenditureYear4 >= 0 ? expenditureYear4 : (-1) * expenditureYear4;
        dataToSave.amountType = ClimateActionAmountType.EnableAmounts;
        dataToSave.longTermEconomicImpact = (long)GetValidAmount(row, columnsInfo, "longTermEcoImpact");
        dataToSave.description = row[columnsInfo.FirstOrDefault(x => x.Value == "description").Key].ToString();
        dataToSave.financeDescription = row[columnsInfo.FirstOrDefault(x => x.Value == "financeDesc").Key].ToString();

        return dataToSave;
    }



    private static decimal GetValidAmount(DataRow row, List<KeyValueInt> columnsInfo, string column)
    {
        string value = row[columnsInfo.FirstOrDefault(x => x.Value == column).Key].ToString();
        decimal returnVal = string.IsNullOrEmpty(value) ? 0 : decimal.Parse(value, NumberStyles.Any, CultureInfoFactory.CreateCulture("en-US"));
        return Math.Round(returnVal, 2);
    }



    private async Task<List<ClimateActionFilterHelper>> UpdateBlistActionIds(string userId, List<ClimateActionFilterHelper> climateData, int budgetYear)
    {
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        TenantDBContext tenantdbContext = await _utility.GetTenantDBContextAsync();
        List<KeyIntValStringHelper> IdsNeedstoBeUpdated = new List<KeyIntValStringHelper>();

        foreach (var item in climateData.Where(x => x.fpActionId != 0).ToList())
        {
            string blobPath = $"{userDetails.tenant_id}/climateactionsave/{budgetYear}/{item.climateActionId}/{item.fpActionId.ToString()}";
            bool isBlobExists = await _blobHelper.BlobExistsAsync(StorageAccount.AppStorage, BlobContainers.climateaction, blobPath);
            if (isBlobExists)
            {
                string actionLogId = await _blobHelper.GetTextBlobAsync(StorageAccount.AppStorage, BlobContainers.climateaction, blobPath);
                Guid logId = !string.IsNullOrEmpty(actionLogId) ? Guid.Parse(actionLogId) : Guid.Empty;
                var logInfo = await tenantdbContext.tfp_action_change_log.OrderByDescending(x => x.updated).FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear && x.fk_log_id == logId);
                if (logInfo == null)
                    continue;

                if (logInfo.type != "B")
                {
                    var finplanActionInfo = await tenantdbContext.tfp_trans_header.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.log_id == logInfo.fk_log_id);
                    if (finplanActionInfo != null)
                    {
                        IdsNeedstoBeUpdated.Add(new KeyIntValStringHelper()
                        {
                            key = item.climateActionId,
                            id = finplanActionInfo.pk_action_id
                        });
                        item.fpActionId = finplanActionInfo.pk_action_id;
                    }
                }
                else if (logInfo.type == "B" && logInfo.action_status != "N")
                {
                    var blistActionInfo = await tenantdbContext.tfp_temp_header.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.log_id == logInfo.fk_log_id);
                    if (blistActionInfo != null)
                    {
                        IdsNeedstoBeUpdated.Add(new KeyIntValStringHelper()
                        {
                            key = item.climateActionId,
                            id = blistActionInfo.fk_action_id
                        });
                        item.fpActionId = blistActionInfo.fk_action_id;
                    }
                }
            }
        }

        List<int> climateIds = IdsNeedstoBeUpdated.Select(x => x.key).ToList();
        var climateHeaderToUpdate = await tenantdbContext.TcoClimateActionHeader.Where(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == budgetYear &&
            climateIds.Contains(x.pkClimateActionId)).ToListAsync();
        foreach (var item in climateHeaderToUpdate)
        {
            int fpActionId = IdsNeedstoBeUpdated.FirstOrDefault(x => x.key == item.pkClimateActionId).id;
            item.fk_action_id = fpActionId;
            item.updated = DateTime.UtcNow;
            item.updated_by = userDetails.pk_id;
        }
        await tenantdbContext.SaveChangesAsync();
        return climateData;
    }



    private static List<KeyValuesDescriptionHelper> GetGoalsDetail(Guid goalId, List<tco_goals> goalsList, List<gco_un_susdev_goals> unsGoalInfo)
    {
        List<KeyValuesDescriptionHelper> goalAndUnsGoalsInfo = new List<KeyValuesDescriptionHelper>();
        //bp-green-tag
        if (goalsList.Count > 0 && goalId != Guid.Empty)
        {
            KeyValuesDescriptionHelper data = new KeyValuesDescriptionHelper();

            var goalDesc = goalsList.FirstOrDefault(x => x.pk_goal_id == goalId);
                
            data.Key = true; //True for goal and false for UNS goal
            data.ShortDesc = goalDesc == null ? string.Empty : goalDesc.goal_name;
            goalAndUnsGoalsInfo.Add(data);

            if (goalDesc != null && !string.IsNullOrEmpty(goalDesc.unsd_goals))
            {
                foreach (string id in goalDesc.unsd_goals.Split(','))
                {
                    if (!string.IsNullOrEmpty(id))
                    {
                        data = new KeyValuesDescriptionHelper();
                        data.Key = false;
                        var unsInfo = unsGoalInfo.FirstOrDefault(x => x.pk_goal_id == id);
                        data.ShortDesc = unsInfo == null ? string.Empty : unsInfo.goal_short_name;
                        data.LongDesc = unsInfo == null ? string.Empty : unsInfo.goal_name;
                        goalAndUnsGoalsInfo.Add(data);
                    }
                }
            }
        }
        return goalAndUnsGoalsInfo;
    }


    private async Task<List<ClimateActionFilterHelper>> GetBaseData(string userId, int budgetYear, Guid categoryId, string orgId = "", int orgLevel = -1)
    {
        StringBuilder sb = new StringBuilder();
        sb.AppendLine($"GetBaseData started @ {DateTime.UtcNow}");

        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        TenantDBContext tenantdbContext = await _utility.GetTenantDBContextAsync();
        List<ClimateActionFilterHelper> climateDataList;
        //Fetch climate data
        if (categoryId == Guid.Empty)
        {
            climateDataList = await (from a in tenantdbContext.TcoClimateActionHeader
                join b in tenantdbContext.TcoClimateActionDetail on new { a = a.pkClimateActionId, b = a.fk_tenant_id, c = a.budget_year }
                    equals new { a = b.fkClimateActionId, b = b.fk_tenant_id, c = b.budget_year }
                join c in tenantdbContext.GcoClimateSector on b.fk_climate_sector_id equals c.pk_climate_sector_id
                join d in tenantdbContext.GcoClimateSource on b.fk_climate_source_id equals d.pk_climate_source_id
                join e in tenantdbContext.GcoClimateEmissionTypes on b.fk_emission_type_id equals e.pk_emission_type_id
                join ca in tenantdbContext.tco_category on new { a = a.fk_tenant_id, b = a.fk_cat_id }
                    equals new { a = ca.fk_tenant_id, b = ca.pk_cat_id }
                join se in tenantdbContext.gco_security_estimate.Where(x => x.type.ToUpper() == "CLIMATE_ACTION")
                    on new { a = a.fk_estimate_id }
                    equals new { a = se.pk_estimate_id } into res1
                from estimateRes in res1.DefaultIfEmpty()
                join f in tenantdbContext.gco_feasibility.Where(x => x.type.ToUpper() == "CLIMATE_ACTION")
                    on new { a = a.fk_feasibility_id }
                    equals new { a = f.pk_feasibility_id } into res2
                from feasibilityRes in res2.DefaultIfEmpty()
                join ps in tenantdbContext.tco_progress_status.Where(x => x.type.ToUpper() == "CLIMATE" && x.active == 1) on new { a = a.fk_tenant_id, b = b.reduction_quantity }
                    equals new { a = ps.fk_tenant_id, b = ps.status_id } into res3
                from statusRes in res3.DefaultIfEmpty()
                join mp in tenantdbContext.tpl_tfp_climateaction_mapping.Where(x => x.is_transferred_tofinplan) on new { a = a.fk_tenant_id, b = a.pkClimateActionId, c = a.budget_year }
                    equals new { a = mp.fk_tenant_id, b = mp.fk_climate_action_id, c = mp.budget_year } into mp1
                from mappingRes in mp1.DefaultIfEmpty()

                where a.fk_tenant_id == userDetails.tenant_id && a.budget_year == budgetYear
                group new { a, b, c, d, e } by new
                {
                    a.pkClimateActionId,
                    a.climate_action_name,
                    a.fk_goal_id,
                    a.tags,
                    a.climateDescription,
                    a.climateDescriptionId,
                    a.financeDescription,
                    a.financeDescriptionId,
                    a.is_assignment,
                    b.pk_id,
                    b.fk_climate_sector_id,
                    a.expense_year1,
                    a.expense_year2,
                    a.expense_year3,
                    a.expense_year4,
                    a.amount_year1,
                    a.amount_year2,
                    a.amount_year3,
                    a.amount_year4,
                    c.sector_name,
                    b.fk_climate_source_id,
                    d.source_name,
                    b.fk_emission_type_id,
                    e.emission_name,
                    b.reductionYear1Val,
                    b.reductionYear2Val,
                    b.reductionYear3Val,
                    b.reductionYear4Val,
                    b.reductionYear5Val,
                    b.reductionYear6Val,
                    b.reductionYear7Val,
                    b.reductionYear8Val,
                    b.reductionYear9Val,
                    b.reductionYear10Val,
                    b.reductionPrevYear1Val,
                    b.reductionPrevYear2Val,
                    b.long_term_reduction,
                    e.co2_factor,
                    a.fk_cat_id,
                    b.fk_department_code,
                    b.fk_function_code,
                    b.fk_project_code,
                    b.free_dim_1,
                    b.free_dim_2,
                    b.free_dim_3,
                    b.free_dim_4,
                    a.action_status,
                    a.longterm_economic_impact,
                    a.fk_action_id,
                    a.fk_main_project_code,
                    categoryName = ca.description,
                    a.reference_url,
                    a.partial_flag,
                    securityEstimate = estimateRes == null ? string.Empty : estimateRes.estimate_name,
                    feasibility = feasibilityRes == null ? string.Empty : feasibilityRes.feasibility_name,
                    isFromPlan = mappingRes != null,
                    a.plan_status_desc,
                    a.plan_status_desc_id,
                    a.updated,
                    a.updated_by,
                    b.reduction_quantity,
                    reductionQuantityName = statusRes != null ? statusRes.status_description : string.Empty,
                    a.org_id,
                    a.org_level,
                    a.service_id,
                    a.service_level,
                    a.sync_status,
                    a.attribute_id
                } into res
                select new ClimateActionFilterHelper
                {
                    climateActionId = res.Key.pkClimateActionId,
                    climateActionName = res.Key.climate_action_name,
                    orgId = res.Key.org_id,
                    orgLevel = res.Key.org_level,
                    serviceId = res.Key.service_id,
                    serviceLevel = res.Key.service_level,
                    goalId = res.Key.fk_goal_id,
                    tags = res.Key.tags,
                    description = res.Key.climateDescription,
                    descriptionId = res.Key.climateDescriptionId,
                    financeDescription = res.Key.financeDescription,
                    financeDescriptionId = res.Key.financeDescriptionId,
                    sectorId = res.Key.fk_climate_sector_id.Value,
                    cost = res.Key.expense_year1 / 1000,
                    totalCost = (res.Key.expense_year1 / 1000) + (res.Key.expense_year2 / 1000) + (res.Key.expense_year3 / 1000) + (res.Key.expense_year4 / 1000),
                    incomeAmountTotal = (res.Key.amount_year1 / 1000) + (res.Key.amount_year2 / 1000) + (res.Key.amount_year3 / 1000) + (res.Key.amount_year4 / 1000),
                    sector = res.Key.sector_name,
                    sourceId = res.Key.fk_climate_source_id.Value,
                    source = res.Key.source_name,
                    emissionTypeId = res.Key.fk_emission_type_id.Value,
                    emissionType = res.Key.emission_name,
                    reductionYear1 = res.Key.reductionYear1Val * res.Key.co2_factor,
                    reductionYear2 = res.Key.reductionYear2Val * res.Key.co2_factor,
                    reductionYear3 = res.Key.reductionYear3Val * res.Key.co2_factor,
                    reductionYear4 = res.Key.reductionYear4Val * res.Key.co2_factor,
                    reductionYear5 = res.Key.reductionYear5Val * res.Key.co2_factor,
                    reductionYear6 = res.Key.reductionYear6Val * res.Key.co2_factor,
                    reductionYear7 = res.Key.reductionYear7Val * res.Key.co2_factor,
                    reductionYear8 = res.Key.reductionYear8Val * res.Key.co2_factor,
                    reductionYear9 = res.Key.reductionYear9Val * res.Key.co2_factor,
                    reductionYear10 = res.Key.reductionYear10Val * res.Key.co2_factor,
                    reductionPrevYear1Value = res.Key.reductionPrevYear1Val * res.Key.co2_factor,
                    reductionPrevYear2Value = res.Key.reductionPrevYear2Val * res.Key.co2_factor,
                    longTermReduction = res.Key.long_term_reduction * res.Key.co2_factor,
                    categoryId = res.Key.fk_cat_id,
                    departmentCode = res.Key.fk_department_code,
                    functionCode = res.Key.fk_function_code,
                    projectCode = res.Key.fk_project_code,
                    freedim1 = res.Key.free_dim_1 == null ? string.Empty : res.Key.free_dim_1,
                    freedim2 = res.Key.free_dim_2 == null ? string.Empty : res.Key.free_dim_2,
                    freedim3 = res.Key.free_dim_3 == null ? string.Empty : res.Key.free_dim_3,
                    freedim4 = res.Key.free_dim_4 == null ? string.Empty : res.Key.free_dim_4,
                    actionStatus = res.Key.action_status,
                    longtermEconomicImpact = res.Key.longterm_economic_impact / 1000,
                    categoryName = res.Key.categoryName,
                    fpActionId = res.Key.fk_action_id,
                    mainProjectCode = res.Key.fk_main_project_code,
                    referenceUrl = res.Key.reference_url,
                    securityEstimate = res.Key.securityEstimate,
                    feasibility = res.Key.feasibility,
                    isAssignment = res.Key.is_assignment,
                    isFromPlan = res.Key.isFromPlan,
                    partialFlag = res.Key.partial_flag,
                    planStatusDesc = res.Key.plan_status_desc,
                    planStatusDescId = res.Key.plan_status_desc_id,
                    updated = res.Key.updated.Value,
                    updatedBy = res.Key.updated_by.Value,
                    reductionQuantity = res.Key.reduction_quantity,
                    reductionQuantityName = res.Key.reductionQuantityName,
                    fpActionOrInvestmentInfo = new ClimateActionInvestmentHelper(),
                    syncStatus = res.Key.sync_status,
                    attributeId = res.Key.attribute_id
                }).AsNoTracking().OrderBy(x => x.climateActionName).ToListAsync();
        }
        else
        {
            if (orgId == "" && orgLevel == -1)
            {
                climateDataList = await (from a in tenantdbContext.TcoClimateActionHeader
                    join b in tenantdbContext.TcoClimateActionDetail on new { a = a.pkClimateActionId, b = a.fk_tenant_id, c = a.budget_year }
                        equals new { a = b.fkClimateActionId, b = b.fk_tenant_id, c = b.budget_year }
                    join c in tenantdbContext.GcoClimateSector on b.fk_climate_sector_id equals c.pk_climate_sector_id
                    join d in tenantdbContext.GcoClimateSource on b.fk_climate_source_id equals d.pk_climate_source_id
                    join e in tenantdbContext.GcoClimateEmissionTypes on b.fk_emission_type_id equals e.pk_emission_type_id
                    join ca in tenantdbContext.tco_category on new { a = a.fk_tenant_id, b = a.fk_cat_id }
                        equals new { a = ca.fk_tenant_id, b = ca.pk_cat_id }
                    join se in tenantdbContext.gco_security_estimate.Where(x => x.type.ToUpper() == "CLIMATE_ACTION")
                        on new { a = a.fk_estimate_id }
                        equals new { a = se.pk_estimate_id } into res1
                    from estimateRes in res1.DefaultIfEmpty()
                    join f in tenantdbContext.gco_feasibility.Where(x => x.type.ToUpper() == "CLIMATE_ACTION")
                        on new { a = a.fk_feasibility_id }
                        equals new { a = f.pk_feasibility_id } into res2
                    from feasibilityRes in res2.DefaultIfEmpty()
                    join ps in tenantdbContext.tco_progress_status.Where(x => x.type.ToUpper() == "CLIMATE" && x.active == 1)
                        on new { a = a.fk_tenant_id, b = b.reduction_quantity }
                        equals new { a = ps.fk_tenant_id, b = ps.status_id } into res3
                    from statusRes in res3.DefaultIfEmpty()
                    join mp in tenantdbContext.tpl_tfp_climateaction_mapping.Where(x => x.is_transferred_tofinplan)
                        on new { a = a.fk_tenant_id, b = a.pkClimateActionId, c = a.budget_year }
                        equals new { a = mp.fk_tenant_id, b = mp.fk_climate_action_id, c = mp.budget_year } into mp1
                    from mappingRes in mp1.DefaultIfEmpty()
                    join cd in tenantdbContext.tco_climates_distribution on new { a = a.fk_tenant_id, c = a.pkClimateActionId }
                        equals new { a = cd.fk_tenant_id, c = cd.fk_climate_id } into res4
                    from allCLimates in res4.DefaultIfEmpty()
                    where a.fk_tenant_id == userDetails.tenant_id && a.budget_year == budgetYear && a.fk_cat_id == categoryId && allCLimates.parent_climateId == 0
                    group new { a, b, c, d, e, allCLimates } by new
                    {
                        a.pkClimateActionId,
                        a.climate_action_name,
                        a.fk_goal_id,
                        a.tags,
                        a.climateDescription,
                        a.climateDescriptionId,
                        a.financeDescription,
                        a.is_assignment,
                        a.financeDescriptionId,
                        b.pk_id,
                        b.fk_climate_sector_id,
                        a.expense_year1,
                        a.expense_year2,
                        a.expense_year3,
                        a.expense_year4,
                        a.amount_year1,
                        a.amount_year2,
                        a.amount_year3,
                        a.amount_year4,
                        c.sector_name,
                        b.fk_climate_source_id,
                        d.source_name,
                        b.fk_emission_type_id,
                        e.emission_name,
                        b.reductionYear1Val,
                        b.reductionYear2Val,
                        b.reductionYear3Val,
                        b.reductionYear4Val,
                        b.reductionYear5Val,
                        b.reductionYear6Val,
                        b.reductionYear7Val,
                        b.reductionYear8Val,
                        b.reductionYear9Val,
                        b.reductionYear10Val,
                        b.reductionPrevYear1Val,
                        b.reductionPrevYear2Val,
                        b.long_term_reduction,
                        e.co2_factor,
                        a.fk_cat_id,
                        b.fk_department_code,
                        b.fk_function_code,
                        b.fk_project_code,
                        b.free_dim_1,
                        b.free_dim_2,
                        b.free_dim_3,
                        b.free_dim_4,
                        a.action_status,
                        a.longterm_economic_impact,
                        a.fk_action_id,
                        a.fk_main_project_code,
                        categoryName = ca.description,
                        a.partial_flag,
                        a.reference_url,
                        securityEstimate = estimateRes == null ? string.Empty : estimateRes.estimate_name,
                        feasibility = feasibilityRes == null ? string.Empty : feasibilityRes.feasibility_name,
                        isFromPlan = mappingRes != null,
                        a.plan_status_desc,
                        a.plan_status_desc_id,
                        a.updated,
                        a.updated_by,
                        b.reduction_quantity,
                        reductionQuantityName = statusRes != null ? statusRes.status_description : string.Empty,
                        a.org_id,
                        a.org_level,
                        a.sync_status,
                        orgIdCreatedAt = a.org_id,
                        orgLevelCreatedAt = a.org_level,
                        a.attribute_id,
                        parentClimateId = allCLimates == null ? 0 : allCLimates.parent_climateId
                    } into res
                    select new ClimateActionFilterHelper
                    {
                        climateActionId = res.Key.pkClimateActionId,
                        climateActionName = res.Key.climate_action_name,
                        goalId = res.Key.fk_goal_id,
                        tags = res.Key.tags,
                        description = res.Key.climateDescription,
                        descriptionId = res.Key.climateDescriptionId,
                        financeDescription = res.Key.financeDescription,
                        financeDescriptionId = res.Key.financeDescriptionId,
                        sectorId = res.Key.fk_climate_sector_id.Value,
                        cost = res.Key.expense_year1 / 1000,
                        totalCost = (res.Key.expense_year1 / 1000) + (res.Key.expense_year2 / 1000) + (res.Key.expense_year3 / 1000) + (res.Key.expense_year4 / 1000),
                        incomeAmountTotal = (res.Key.amount_year1 / 1000) + (res.Key.amount_year2 / 1000) + (res.Key.amount_year3 / 1000) + (res.Key.amount_year4 / 1000),
                        sector = res.Key.sector_name,
                        sourceId = res.Key.fk_climate_source_id.Value,
                        source = res.Key.source_name,
                        emissionTypeId = res.Key.fk_emission_type_id.Value,
                        emissionType = res.Key.emission_name,
                        reductionYear1 = res.Key.reductionYear1Val * res.Key.co2_factor,
                        reductionYear2 = res.Key.reductionYear2Val * res.Key.co2_factor,
                        reductionYear3 = res.Key.reductionYear3Val * res.Key.co2_factor,
                        reductionYear4 = res.Key.reductionYear4Val * res.Key.co2_factor,
                        reductionYear5 = res.Key.reductionYear5Val * res.Key.co2_factor,
                        reductionYear6 = res.Key.reductionYear6Val * res.Key.co2_factor,
                        reductionYear7 = res.Key.reductionYear7Val * res.Key.co2_factor,
                        reductionYear8 = res.Key.reductionYear8Val * res.Key.co2_factor,
                        reductionYear9 = res.Key.reductionYear9Val * res.Key.co2_factor,
                        reductionYear10 = res.Key.reductionYear10Val * res.Key.co2_factor,
                        reductionPrevYear1Value = res.Key.reductionPrevYear1Val * res.Key.co2_factor,
                        reductionPrevYear2Value = res.Key.reductionPrevYear2Val * res.Key.co2_factor,
                        longTermReduction = res.Key.long_term_reduction * res.Key.co2_factor,
                        categoryId = res.Key.fk_cat_id,
                        departmentCode = res.Key.fk_department_code,
                        functionCode = res.Key.fk_function_code,
                        projectCode = res.Key.fk_project_code,
                        freedim1 = res.Key.free_dim_1 == null ? string.Empty : res.Key.free_dim_1,
                        freedim2 = res.Key.free_dim_2 == null ? string.Empty : res.Key.free_dim_2,
                        freedim3 = res.Key.free_dim_3 == null ? string.Empty : res.Key.free_dim_3,
                        freedim4 = res.Key.free_dim_4 == null ? string.Empty : res.Key.free_dim_4,
                        actionStatus = res.Key.action_status,
                        longtermEconomicImpact = res.Key.longterm_economic_impact / 1000,
                        categoryName = res.Key.categoryName,
                        fpActionId = res.Key.fk_action_id,
                        mainProjectCode = res.Key.fk_main_project_code,
                        referenceUrl = res.Key.reference_url,
                        securityEstimate = res.Key.securityEstimate,
                        feasibility = res.Key.feasibility,
                        isAssignment = res.Key.is_assignment,
                        isFromPlan = res.Key.isFromPlan,
                        partialFlag = res.Key.partial_flag,
                        planStatusDesc = res.Key.plan_status_desc,
                        planStatusDescId = res.Key.plan_status_desc_id,
                        updated = res.Key.updated.Value,
                        updatedBy = res.Key.updated_by.Value,
                        reductionQuantity = res.Key.reduction_quantity,
                        reductionQuantityName = res.Key.reductionQuantityName,
                        fpActionOrInvestmentInfo = new ClimateActionInvestmentHelper(),
                        orgId = res.Key.org_id,
                        orgLevel = res.Key.org_level,
                        syncStatus = res.Key.sync_status,
                        attributeId = res.Key.attribute_id,
                        parentClimateId = res.Key.parentClimateId
                    }).AsNoTracking().OrderBy(x => x.climateActionName).ToListAsync();
            }
            else
            {
                climateDataList = await (from a in tenantdbContext.TcoClimateActionHeader
                    join b in tenantdbContext.TcoClimateActionDetail on new { a = a.pkClimateActionId, b = a.fk_tenant_id, c = a.budget_year }
                        equals new { a = b.fkClimateActionId, b = b.fk_tenant_id, c = b.budget_year }
                    join c in tenantdbContext.GcoClimateSector on b.fk_climate_sector_id equals c.pk_climate_sector_id
                    join d in tenantdbContext.GcoClimateSource on b.fk_climate_source_id equals d.pk_climate_source_id
                    join e in tenantdbContext.GcoClimateEmissionTypes on b.fk_emission_type_id equals e.pk_emission_type_id
                    join ca in tenantdbContext.tco_category on new { a = a.fk_tenant_id, b = a.fk_cat_id }
                        equals new { a = ca.fk_tenant_id, b = ca.pk_cat_id }
                    join se in tenantdbContext.gco_security_estimate.Where(x => x.type.ToUpper() == "CLIMATE_ACTION")
                        on new { a = a.fk_estimate_id }
                        equals new { a = se.pk_estimate_id } into res1
                    from estimateRes in res1.DefaultIfEmpty()
                    join f in tenantdbContext.gco_feasibility.Where(x => x.type.ToUpper() == "CLIMATE_ACTION")
                        on new { a = a.fk_feasibility_id }
                        equals new { a = f.pk_feasibility_id } into res2
                    from feasibilityRes in res2.DefaultIfEmpty()
                    join ps in tenantdbContext.tco_progress_status.Where(x => x.type.ToUpper() == "CLIMATE" && x.active == 1)
                        on new { a = a.fk_tenant_id, b = b.reduction_quantity }
                        equals new { a = ps.fk_tenant_id, b = ps.status_id } into res3
                    from statusRes in res3.DefaultIfEmpty()
                    join mp in tenantdbContext.tpl_tfp_climateaction_mapping.Where(x => x.is_transferred_tofinplan)
                        on new { a = a.fk_tenant_id, b = a.pkClimateActionId, c = a.budget_year }
                        equals new { a = mp.fk_tenant_id, b = mp.fk_climate_action_id, c = mp.budget_year } into mp1
                    from mappingRes in mp1.DefaultIfEmpty()
                    join cd in tenantdbContext.tco_climates_distribution on new { a = a.fk_tenant_id, c = a.pkClimateActionId }
                        equals new { a = cd.fk_tenant_id, c = cd.fk_climate_id } into res4
                    from allCLimates in res4.DefaultIfEmpty()
                    where a.fk_tenant_id == userDetails.tenant_id && a.budget_year == budgetYear && a.fk_cat_id == categoryId
                    group new { a, b, c, d, e, allCLimates } by new
                    {
                        a.pkClimateActionId,
                        a.climate_action_name,
                        a.fk_goal_id,
                        a.tags,
                        a.climateDescription,
                        a.climateDescriptionId,
                        a.financeDescription,
                        a.is_assignment,
                        a.financeDescriptionId,
                        b.pk_id,
                        b.fk_climate_sector_id,
                        a.expense_year1,
                        a.expense_year2,
                        a.expense_year3,
                        a.expense_year4,
                        a.amount_year1,
                        a.amount_year2,
                        a.amount_year3,
                        a.amount_year4,
                        c.sector_name,
                        b.fk_climate_source_id,
                        d.source_name,
                        b.fk_emission_type_id,
                        e.emission_name,
                        b.reductionYear1Val,
                        b.reductionYear2Val,
                        b.reductionYear3Val,
                        b.reductionYear4Val,
                        b.reductionYear5Val,
                        b.reductionYear6Val,
                        b.reductionYear7Val,
                        b.reductionYear8Val,
                        b.reductionYear9Val,
                        b.reductionYear10Val,
                        b.reductionPrevYear1Val,
                        b.reductionPrevYear2Val,
                        b.long_term_reduction,
                        e.co2_factor,
                        a.fk_cat_id,
                        b.fk_department_code,
                        b.fk_function_code,
                        b.fk_project_code,
                        b.free_dim_1,
                        b.free_dim_2,
                        b.free_dim_3,
                        b.free_dim_4,
                        a.action_status,
                        a.longterm_economic_impact,
                        a.fk_action_id,
                        a.fk_main_project_code,
                        categoryName = ca.description,
                        a.partial_flag,
                        a.reference_url,
                        securityEstimate = estimateRes == null ? string.Empty : estimateRes.estimate_name,
                        feasibility = feasibilityRes == null ? string.Empty : feasibilityRes.feasibility_name,
                        isFromPlan = mappingRes != null,
                        a.plan_status_desc,
                        a.plan_status_desc_id,
                        a.updated,
                        a.updated_by,
                        b.reduction_quantity,
                        reductionQuantityName = statusRes != null ? statusRes.status_description : string.Empty,
                        allCLimates.service_id,
                        allCLimates.service_level,
                        allCLimates.org_id,
                        allCLimates.org_level,
                        orgIdCreatedAt = a.org_id,
                        orgLevelCreatedAt = a.org_level,
                        a.sync_status,
                        a.attribute_id,
                        parentClimateId = allCLimates != null ? allCLimates.parent_climateId : 0
                    } into res
                    select new ClimateActionFilterHelper
                    {
                        climateActionId = res.Key.pkClimateActionId,
                        climateActionName = res.Key.climate_action_name,
                        goalId = res.Key.fk_goal_id,
                        tags = res.Key.tags,
                        description = res.Key.climateDescription,
                        descriptionId = res.Key.climateDescriptionId,
                        financeDescription = res.Key.financeDescription,
                        financeDescriptionId = res.Key.financeDescriptionId,
                        sectorId = res.Key.fk_climate_sector_id.Value,
                        cost = res.Key.expense_year1 / 1000,
                        totalCost = (res.Key.expense_year1 / 1000) + (res.Key.expense_year2 / 1000) + (res.Key.expense_year3 / 1000) + (res.Key.expense_year4 / 1000),
                        incomeAmountTotal = (res.Key.amount_year1 / 1000) + (res.Key.amount_year2 / 1000) + (res.Key.amount_year3 / 1000) + (res.Key.amount_year4 / 1000),
                        sector = res.Key.sector_name,
                        sourceId = res.Key.fk_climate_source_id.Value,
                        source = res.Key.source_name,
                        emissionTypeId = res.Key.fk_emission_type_id.Value,
                        emissionType = res.Key.emission_name,
                        reductionYear1 = res.Key.reductionYear1Val * res.Key.co2_factor,
                        reductionYear2 = res.Key.reductionYear2Val * res.Key.co2_factor,
                        reductionYear3 = res.Key.reductionYear3Val * res.Key.co2_factor,
                        reductionYear4 = res.Key.reductionYear4Val * res.Key.co2_factor,
                        reductionYear5 = res.Key.reductionYear5Val * res.Key.co2_factor,
                        reductionYear6 = res.Key.reductionYear6Val * res.Key.co2_factor,
                        reductionYear7 = res.Key.reductionYear7Val * res.Key.co2_factor,
                        reductionYear8 = res.Key.reductionYear8Val * res.Key.co2_factor,
                        reductionYear9 = res.Key.reductionYear9Val * res.Key.co2_factor,
                        reductionYear10 = res.Key.reductionYear10Val * res.Key.co2_factor,
                        reductionPrevYear1Value = res.Key.reductionPrevYear1Val * res.Key.co2_factor,
                        reductionPrevYear2Value = res.Key.reductionPrevYear2Val * res.Key.co2_factor,
                        longTermReduction = res.Key.long_term_reduction * res.Key.co2_factor,
                        categoryId = res.Key.fk_cat_id,
                        departmentCode = res.Key.fk_department_code,
                        functionCode = res.Key.fk_function_code,
                        projectCode = res.Key.fk_project_code,
                        freedim1 = res.Key.free_dim_1 == null ? string.Empty : res.Key.free_dim_1,
                        freedim2 = res.Key.free_dim_2 == null ? string.Empty : res.Key.free_dim_2,
                        freedim3 = res.Key.free_dim_3 == null ? string.Empty : res.Key.free_dim_3,
                        freedim4 = res.Key.free_dim_4 == null ? string.Empty : res.Key.free_dim_4,
                        actionStatus = res.Key.action_status,
                        longtermEconomicImpact = res.Key.longterm_economic_impact / 1000,
                        categoryName = res.Key.categoryName,
                        fpActionId = res.Key.fk_action_id,
                        mainProjectCode = res.Key.fk_main_project_code,
                        referenceUrl = res.Key.reference_url,
                        securityEstimate = res.Key.securityEstimate,
                        feasibility = res.Key.feasibility,
                        isAssignment = res.Key.is_assignment,
                        isFromPlan = res.Key.isFromPlan,
                        partialFlag = res.Key.partial_flag,
                        planStatusDesc = res.Key.plan_status_desc,
                        planStatusDescId = res.Key.plan_status_desc_id,
                        updated = res.Key.updated.Value,
                        updatedBy = res.Key.updated_by.Value,
                        reductionQuantity = res.Key.reduction_quantity,
                        reductionQuantityName = res.Key.reductionQuantityName,
                        fpActionOrInvestmentInfo = new ClimateActionInvestmentHelper(),
                        serviceId = res.Key.service_id == null ? "" : res.Key.service_id,
                        serviceLevel = res.Key.service_level == null ? 0 : res.Key.service_level,
                        orgId = res.Key.org_id == null ? "" : res.Key.org_id,
                        orgLevel = res.Key.org_level == null ? 0 : res.Key.org_level,
                        syncStatus = res.Key.sync_status,
                        orgIdCreatedAt = res.Key.orgIdCreatedAt,
                        orgLevelCreatedAt = res.Key.orgLevelCreatedAt,
                        attributeId = res.Key.attribute_id,
                        parentClimateId = res.Key.parentClimateId
                    }).AsNoTracking().OrderBy(x => x.climateActionName).ToListAsync();
            }
        }
            
        sb.AppendLine($"GetBaseData finished @ {DateTime.UtcNow}");
        await InsertPerformanceLog(userId, sb.ToString(), "GetBaseData");

        return climateDataList;
    }



    private static List<ClimateActionFilterHelper> AggregateClimateData(List<ClimateActionFilterHelper> climateData, List<string> columnFields)
    {
        bool includeSector = columnFields.Contains("sector");
        bool includeSource = columnFields.Contains("source");
        bool includeReductionQuantity = columnFields.Contains("reductionQuantityName");

        climateData = climateData.GroupBy(x => new
        {
            x.climateActionName,
            x.climateActionId,
            x.goalId,
            x.tags,
            x.description,
            x.descriptionId,
            x.financeDescription,
            x.financeDescriptionId,
            x.cost,
            x.totalCost,
            x.categoryId,
            x.actionStatus,
            x.longtermEconomicImpact,
            x.categoryName,
            x.referenceUrl,
            x.securityEstimate,
            x.feasibility,
            x.investment,
            x.finplanAction,
            x.planActionSpecInfo,
            x.statusInPlan,
            x.isFromPlan,
            x.partialFlag,
            x.planStatusDesc,
            x.planStatusDescId,
            x.updated,
            x.updatedBy,
            sectorId = includeSector ? x.sectorId : 0,
            sector = includeSector ? x.sector : string.Empty,
            source = includeSource ? x.source : string.Empty,
            sourceId = includeSource ? x.sourceId : 0,
            reductionQuantity = includeReductionQuantity ? x.reductionQuantity : 0,
            reductionQuantityName = includeReductionQuantity ? x.reductionQuantityName : string.Empty,
            x.fpActionOrInvestmentInfoString,
            x.isAssignment
        }).Select(res => new ClimateActionFilterHelper
        {
            climateActionId = res.Key.climateActionId,
            climateActionName = res.Key.climateActionName,
            goalId = res.Key.goalId,
            tags = res.Key.tags,
            description = res.Key.description,
            descriptionId = res.Key.descriptionId,
            financeDescription = res.Key.financeDescription,
            financeDescriptionId = res.Key.financeDescriptionId,
            sectorId = res.Key.sectorId,
            cost = Math.Round(res.Key.cost),
            totalCost = Math.Round(res.Key.totalCost),
            sector = res.Key.sector,
            sourceId = res.Key.sectorId,
            source = res.Key.source,
            reductionYear1 = res.Sum(x => x.reductionYear1),
            reductionYear2 = res.Sum(x => x.reductionYear2),
            reductionYear3 = res.Sum(x => x.reductionYear3),
            reductionYear4 = res.Sum(x => x.reductionYear4),
            reductionYear5 = res.Sum(x => x.reductionYear5),
            reductionYear6 = res.Sum(x => x.reductionYear6),
            reductionYear7 = res.Sum(x => x.reductionYear7),
            reductionYear8 = res.Sum(x => x.reductionYear8),
            reductionYear9 = res.Sum(x => x.reductionYear9),
            reductionYear10 = res.Sum(x => x.reductionYear10),
            reductionPrevYear1Value = res.Sum(x => x.reductionPrevYear1Value),
            reductionPrevYear2Value = res.Sum(x => x.reductionPrevYear2Value),
            longTermReduction = res.Sum(x => x.longTermReduction),
            categoryId = res.Key.categoryId,
            actionStatus = res.Key.actionStatus,
            longtermEconomicImpact = res.Key.longtermEconomicImpact,
            categoryName = res.Key.categoryName,
            referenceUrl = res.Key.referenceUrl,
            securityEstimate = res.Key.securityEstimate,
            feasibility = res.Key.feasibility,
            investment = res.Key.investment,
            finplanAction = res.Key.finplanAction,
            statusInPlan = res.Key.statusInPlan,
            planActionSpecInfo = res.Key.planActionSpecInfo,
            isFromPlan = res.Key.isFromPlan,
            partialFlag = res.Key.partialFlag,
            planStatusDesc = res.Key.planStatusDesc,
            planStatusDescId = res.Key.planStatusDescId,
            updated = res.Key.updated,
            updatedBy = res.Key.updatedBy,
            reductionQuantity = res.Key.reductionQuantity,
            reductionQuantityName = res.Key.reductionQuantityName,
            fpActionOrInvestmentInfoString = res.Key.fpActionOrInvestmentInfoString,
            isAssignment = res.Key.isAssignment
        }).OrderBy(x => x.climateActionName).ToList();

        return climateData;
    }



    private async Task<List<KeyValueStringData>> GetDefaultDepartmentAndFunctionInfoAsync(string userId, int budgetYear, OrgDetailHelper orgInput)
    {
        var orgVersionContent = await _utility.GetOrgVersionSpecificContentAsync(userId, _utility.GetForecastPeriod(budgetYear, 1));
        UserData userDetails = await _utility.GetUserDetailsAsync(userId);
        TenantDBContext tenantdbContext = await _utility.GetTenantDBContextAsync();
        List<KeyValueStringData> departmentFunctionData = new List<KeyValueStringData>();
        KeyValueStringData result;
        orgInput.serviceId = orgInput.serviceLevel == 0 || orgInput.serviceLevel == -1 || string.IsNullOrEmpty(orgInput.serviceId) ? string.Empty : orgInput.serviceId;

        string fpLevel2 = await _utility.GetParameterValueAsync(userId, "FINPLAN_LEVEL_2");
        if (orgInput == null || (string.IsNullOrEmpty(orgInput.serviceId) && orgInput.orgLevel == 1))
        {
            result = await (from a in tenantdbContext.tmd_fp_level_defaults
                join b in tenantdbContext.tco_departments on new { a = a.fk_tenant_id, b = a.fk_department_code }
                    equals new { a = b.fk_tenant_id, b = b.pk_department_code }
                join c in tenantdbContext.tco_functions on new { a = a.fk_tenant_id, b = a.fk_function_code }
                    equals new { a = c.pk_tenant_id, b = c.pk_Function_code }
                where a.fk_tenant_id == userDetails.tenant_id && a.module == "FINPLAN" &&
                      a.fk_org_version == orgVersionContent.orgVersion && a.status == 1 &&
                      (budgetYear >= b.year_from && budgetYear <= b.year_to) &&
                      (budgetYear >= c.dateFrom.Year && budgetYear <= c.dateTo.Year)
                orderby a.fp_level_1_value
                select new KeyValueStringData()
                {
                    Key = a.fk_department_code,
                    Value = a.fk_function_code,
                    Name = b.department_name,
                    Name1 = c.display_name
                }).FirstOrDefaultAsync();
        }
        else if (!string.IsNullOrEmpty(fpLevel2) && (fpLevel2.StartsWith("ser")))
        {
            //For serviceId setup tenants
            switch (orgInput.orgLevel)
            {
                case 1:
                    result = await (from a in tenantdbContext.tmd_fp_level_defaults
                        join b in tenantdbContext.tco_departments on new { a = a.fk_tenant_id, b = a.fk_department_code }
                            equals new { a = b.fk_tenant_id, b = b.pk_department_code }
                        join c in tenantdbContext.tco_functions on new { a = a.fk_tenant_id, b = a.fk_function_code }
                            equals new { a = c.pk_tenant_id, b = c.pk_Function_code }
                        where a.fk_tenant_id == userDetails.tenant_id && a.module == "FINPLAN" &&
                              a.fk_org_version == orgVersionContent.orgVersion && a.status == 1 && a.fp_level_2_value == orgInput.serviceId &&
                              (budgetYear >= b.year_from && budgetYear <= b.year_to) &&
                              (budgetYear >= c.dateFrom.Year && budgetYear <= c.dateTo.Year)
                        select new KeyValueStringData()
                        {
                            Key = a.fk_department_code,
                            Value = a.fk_function_code,
                            Name = b.department_name,
                            Name1 = c.display_name
                        }).FirstOrDefaultAsync();
                    break;

                case 2:
                    result = await (from a in tenantdbContext.tmd_fp_level_defaults
                        join b in tenantdbContext.tco_departments on new { a = a.fk_tenant_id, b = a.fk_department_code }
                            equals new { a = b.fk_tenant_id, b = b.pk_department_code }
                        join c in tenantdbContext.tco_functions on new { a = a.fk_tenant_id, b = a.fk_function_code }
                            equals new { a = c.pk_tenant_id, b = c.pk_Function_code }
                        where a.fk_tenant_id == userDetails.tenant_id && a.module == "FINPLAN" &&
                              a.fk_org_version == orgVersionContent.orgVersion && a.status == 1 && a.fp_level_1_value == orgInput.orgId && a.fp_level_2_value == orgInput.serviceId &&
                              (budgetYear >= b.year_from && budgetYear <= b.year_to) &&
                              (budgetYear >= c.dateFrom.Year && budgetYear <= c.dateTo.Year)
                        select new KeyValueStringData()
                        {
                            Key = a.fk_department_code,
                            Value = a.fk_function_code,
                            Name = b.department_name,
                            Name1 = c.display_name
                        }).FirstOrDefaultAsync();
                    break;

                default:
                    result = await (from a in tenantdbContext.tmd_fp_level_defaults
                        join b in tenantdbContext.tco_departments on new { a = a.fk_tenant_id, b = a.fk_department_code }
                            equals new { a = b.fk_tenant_id, b = b.pk_department_code }
                        join c in tenantdbContext.tco_functions on new { a = a.fk_tenant_id, b = a.fk_function_code }
                            equals new { a = c.pk_tenant_id, b = c.pk_Function_code }
                        where a.fk_tenant_id == userDetails.tenant_id && a.module == "FINPLAN" &&
                              a.fk_org_version == orgVersionContent.orgVersion && a.status == 1 && a.fp_level_1_value == orgInput.orgIdLevel2 && a.fp_level_2_value == orgInput.serviceId &&
                              (budgetYear >= b.year_from && budgetYear <= b.year_to) &&
                              (budgetYear >= c.dateFrom.Year && budgetYear <= c.dateTo.Year)
                        select new KeyValueStringData()
                        {
                            Key = a.fk_department_code,
                            Value = a.fk_function_code,
                            Name = b.department_name,
                            Name1 = c.display_name
                        }).FirstOrDefaultAsync();
                    break;
            }
        }
        else
        {
            switch (orgInput.orgLevel)
            {
                case 2:
                    result = await (from a in tenantdbContext.tmd_fp_level_defaults
                        join b in tenantdbContext.tco_departments on new { a = a.fk_tenant_id, b = a.fk_department_code }
                            equals new { a = b.fk_tenant_id, b = b.pk_department_code }
                        join c in tenantdbContext.tco_functions on new { a = a.fk_tenant_id, b = a.fk_function_code }
                            equals new { a = c.pk_tenant_id, b = c.pk_Function_code }
                        where a.fk_tenant_id == userDetails.tenant_id && a.module == "FINPLAN" &&
                              a.fk_org_version == orgVersionContent.orgVersion && a.status == 1 && a.fp_level_1_value == orgInput.orgId && a.fp_level_2_value == string.Empty &&
                              (budgetYear >= b.year_from && budgetYear <= b.year_to) &&
                              (budgetYear >= c.dateFrom.Year && budgetYear <= c.dateTo.Year)
                        select new KeyValueStringData()
                        {
                            Key = a.fk_department_code,
                            Value = a.fk_function_code,
                            Name = b.department_name,
                            Name1 = c.display_name
                        }).FirstOrDefaultAsync();
                    break;

                default:
                    result = await (from a in tenantdbContext.tmd_fp_level_defaults
                        join b in tenantdbContext.tco_departments on new { a = a.fk_tenant_id, b = a.fk_department_code }
                            equals new { a = b.fk_tenant_id, b = b.pk_department_code }
                        join c in tenantdbContext.tco_functions on new { a = a.fk_tenant_id, b = a.fk_function_code }
                            equals new { a = c.pk_tenant_id, b = c.pk_Function_code }
                        where a.fk_tenant_id == userDetails.tenant_id && a.module == "FINPLAN" &&
                              a.fk_org_version == orgVersionContent.orgVersion && a.status == 1 && a.fp_level_1_value == orgInput.orgIdLevel2 && a.fp_level_2_value == orgInput.orgIdLevel3 &&
                              (budgetYear >= b.year_from && budgetYear <= b.year_to) &&
                              (budgetYear >= c.dateFrom.Year && budgetYear <= c.dateTo.Year)
                        select new KeyValueStringData()
                        {
                            Key = a.fk_department_code,
                            Value = a.fk_function_code,
                            Name = b.department_name,
                            Name1 = c.display_name
                        }).FirstOrDefaultAsync();
                    break;
            }
        }

        //Get default department and function info for climate
        if (result != null)
        {
            departmentFunctionData.Add(new KeyValueStringData { Key = result.Key, Value = result.Name });
            departmentFunctionData.Add(new KeyValueStringData { Key = result.Value, Value = result.Name1 });
        }
        else
        {
            //Add key value with empty for both dept and function
            departmentFunctionData.Add(new KeyValueStringData { Key = string.Empty, Value = string.Empty });
            departmentFunctionData.Add(new KeyValueStringData { Key = string.Empty, Value = string.Empty });
        }
        return departmentFunctionData;
    }

}