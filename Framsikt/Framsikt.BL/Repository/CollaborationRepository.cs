#pragma warning disable CS8603

using Framsikt.Entities;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Framsikt.BL.Repository
{
    public class CollaborationRepository : ICollaborationRepository
    {
        private readonly TenantDBContext _tenantDbContext;

        public CollaborationRepository(TenantDBContext dbContext)
        {
            _tenantDbContext = dbContext;
        }

        public tco_collaboration_comments GetComment(int tenantId, string documentId, string commentThreadId,
            string commentId)
        {
            return _tenantDbContext.tco_collaboration_comments.FirstOrDefault(x =>
                x.TenantId == tenantId && x.DocumentId.Equals(documentId,
                    StringComparison.InvariantCultureIgnoreCase) &&
                x.ThreadId.Equals(commentThreadId, StringComparison.InvariantCultureIgnoreCase) &&
                x.CommentId.Equals(commentId, StringComparison.InvariantCultureIgnoreCase));
        }

        public IEnumerable<tco_collaboration_comments> GetCommentThread(int tenantId, string documentId,
            string commentThreadId)
        {
            return _tenantDbContext.tco_collaboration_comments.Where(x =>
                x.TenantId == tenantId && x.DocumentId.Equals(documentId,
                    StringComparison.InvariantCultureIgnoreCase) &&
                x.ThreadId.Equals(commentThreadId, StringComparison.InvariantCultureIgnoreCase)).ToList();
        }
    }
}