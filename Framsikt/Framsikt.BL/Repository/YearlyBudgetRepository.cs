using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Framsikt.Entities.Core;
using Microsoft.EntityFrameworkCore;
using System.Linq.Dynamic.Core;
using System.Data;
using static Framsikt.BL.Helpers.clsConstants;
using SkiaSharp;

namespace Framsikt.BL.Repository
{
    public class YearlyBudgetRepository : IYearlyBudgetRepository
    {
        private readonly TenantDBContext _tenantDbContext;
        private readonly IDbContextManager _dbContextManager;
       
        public YearlyBudgetRepository(TenantDBContext dbContext,IDbContextManager dbContextManager)
        {
            _tenantDbContext = dbContext;
            _dbContextManager = dbContextManager;
        }

        #region Public Methods

        public async Task<IEnumerable<tco_application_flag>> GetBudAllTcoApplicationFlagDataAsync(int tenantId, string flagName, int budgetYear)
        {
            return await _tenantDbContext.tco_application_flag.Where(x => x.fk_tenant_id == tenantId && x.flag_name == flagName && x.budget_year == budgetYear).AsNoTracking().ToListAsync();
        }

        public async Task<List<tbu_trans_detail>> GetDataTbuTransForUserAdjCodeDelete(int tenantId, DeleteAdjCodeHelper input)
        {
            if (input.Pagetype == UserAdjPageType.BudgetChanges.ToString())
            {
                if (input.status == UserAdjStatusType.InProgress.ToString())
                    return await _tenantDbContext.tbu_trans_detail.Where(x => x.fk_tenant_id == tenantId && x.budget_year == input.budgetYear && x.fk_adjustment_code == input.adjCode).ToListAsync();
                else if (input.status == UserAdjStatusType.FinishedNotConfirmed.ToString())
                    return await _tenantDbContext.tbu_trans_detail.Where(x => x.fk_tenant_id == tenantId && x.budget_year >= input.budgetYear && x.budget_year <= (input.budgetYear + 1) && x.fk_adjustment_code == input.adjCode).ToListAsync();
                else
                    return new List<tbu_trans_detail>();
            }
            else
            {
                return await _tenantDbContext.tbu_trans_detail.Where(x => x.fk_tenant_id == tenantId && x.budget_year == input.budgetYear && x.fk_adjustment_code == input.adjCode).ToListAsync();
            }
        }

        public async Task<List<tfp_temp_detail>> GetDataTfpTempForUserAdjCodeDelete(int tenantId, DeleteAdjCodeHelper input)
        {
            if (input.status == UserAdjStatusType.InProgress.ToString())
                return await _tenantDbContext.tfp_temp_detail.Where(x => x.fk_tenant_id == tenantId && x.budget_year == input.budgetYear && x.fk_adjustment_code == input.adjCode).ToListAsync();
            else if (input.status == UserAdjStatusType.FinishedNotConfirmed.ToString())
                return await _tenantDbContext.tfp_temp_detail.Where(x => x.fk_tenant_id == tenantId && x.budget_year >= input.budgetYear && x.budget_year <= (input.budgetYear + 1) && x.fk_adjustment_code == input.adjCode).ToListAsync();
            else
                return new List<tfp_temp_detail>();
        }

        public async Task<List<tco_user_adjustment_codes>> GetUserAdjCodeData(int tenantId, DeleteAdjCodeHelper input)
        {
            return await _tenantDbContext.tco_user_adjustment_codes.Where(x => x.fk_tenant_id == tenantId && x.budget_year == input.budgetYear && x.pk_adj_code == input.adjCode).ToListAsync();
        }

        public async Task<List<tfp_trans_detail>> GetDataTfpTransForUserAdjCodeDelete(int tenantId, DeleteAdjCodeHelper input)
        {
            if (input.Pagetype == UserAdjPageType.BudgetChanges.ToString())
            {
                if (input.status == UserAdjStatusType.InProgress.ToString())
                    return await _tenantDbContext.tfp_trans_detail.Where(x => x.fk_tenant_id == tenantId && x.budget_year == input.budgetYear && x.fk_adj_code == input.adjCode).ToListAsync();// #142409
                else if (input.status == UserAdjStatusType.FinishedNotConfirmed.ToString())
                    return await _tenantDbContext.tfp_trans_detail.Where(x => x.fk_tenant_id == tenantId && x.budget_year >= input.budgetYear && x.budget_year <= (input.budgetYear + 1) && x.fk_adj_code == input.adjCode).ToListAsync();
                else
                    return new List<tfp_trans_detail>();
            }
            else
            {
                return await _tenantDbContext.tfp_trans_detail.Where(x => x.fk_tenant_id == tenantId && x.budget_year == input.budgetYear && x.fk_adj_code == input.adjCode).ToListAsync();
            }
        }

        public async Task<List<TfpStageActionImport>> GetYBActionImportData(int tenantId, DeleteAdjCodeHelper input)
        {
            return await _tenantDbContext.TfpStageActionImport.Where(x => x.TenantId == tenantId && x.BudgetYear == input.budgetYear && x.fkUserAdjCode == input.adjCode).ToListAsync();
        }

        public async Task<List<TransferYearlyBudgetList>> GetYearlyBudgetOverviewExportData(int tenantId, string adjustmentCode, int budgetYear, string orgVersion, TenantDBContext _tenantDBContext1)
        {
            var baseData = await _tenantDbContext.tbu_trans_detail.Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.fk_adjustment_code == adjustmentCode).ToListAsync();
            var distinctFreeDimValues = await _tenantDbContext.tco_free_dim_values.Where(x => x.fk_tenant_id == tenantId).Select(x => new { x.free_dim_code, x.description }).ToListAsync();
            var distinctAdjCodes = await _tenantDbContext.tco_user_adjustment_codes
                                    .Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.pk_adj_code == adjustmentCode)
                                    .GroupBy(x => new { x.pk_adj_code, x.case_ref, x.description, x.long_description, x.fk_attribute_id })
                                    .Select(group => new
                                    {
                                        group.Key.pk_adj_code,
                                        group.Key.case_ref,
                                        group.Key.description,
                                        group.Key.long_description,
                                        group.Key.fk_attribute_id,
                                    })
                                    .ToListAsync();

            var distinctAccount = await _tenantDbContext.tco_accounts
                                    .Where(x => x.pk_tenant_id == tenantId)
                                    .GroupBy(x => new { x.pk_account_code, x.display_name })
                                    .Select(account => account.Key)
                                    .ToListAsync();
            var distinctDepValues = await _tenantDbContext.tco_departments
                                    .Where(x => x.fk_tenant_id == tenantId)
                                    .GroupBy(x => new { x.pk_department_code, x.department_name })
                                    .Select(group => group.Key)
                                    .ToListAsync();
            var distinctFunctions = await _tenantDbContext.tco_functions
                                    .Where(x => x.pk_tenant_id == tenantId)
                                    .GroupBy(x => new { x.pk_Function_code, x.display_name })
                                    .Select(group => group.Key)
                                    .ToListAsync();
            var distinctProject = await _tenantDbContext.tco_projects
                                    .Where(x => x.fk_tenant_id == tenantId)
                                    .GroupBy(x => new { x.pk_project_code, x.project_name })
                                    .Select(project => project.Key)
                                    .ToListAsync();
            var result = from a in baseData
                         select new TransferYearlyBudgetList
                         {
                             pkId = a.pk_id,
                             adjustmentCode = adjustmentCode,
                             adjustmentDescription = distinctAdjCodes.Where(x => x.pk_adj_code == a.fk_adjustment_code).Select(y => y.description).FirstOrDefault() ?? string.Empty,
                             attributeCode = distinctAdjCodes.Where(x => x.pk_adj_code == a.fk_adjustment_code).Select(y => y.fk_attribute_id).FirstOrDefault() ?? string.Empty,
                             caseReference = distinctAdjCodes.Where(x => x.pk_adj_code == a.fk_adjustment_code).Select(y => y.long_description).FirstOrDefault() ?? string.Empty,
                             accountCode = a.fk_account_code ?? string.Empty,
                             accountName = distinctAccount.Where(x => x.pk_account_code == a.fk_account_code).Select(y => y.display_name).FirstOrDefault() ?? string.Empty,
                             departmentCode = a.department_code ?? string.Empty,
                             departmentName = distinctDepValues.Where(x => x.pk_department_code == a.department_code).Select(y => y.department_name).FirstOrDefault() ?? string.Empty,
                             functionCode = a.fk_function_code ?? string.Empty,
                             functionName = distinctFunctions.Where(x => x.pk_Function_code == a.fk_function_code).Select(y => y.display_name).FirstOrDefault() ?? string.Empty,
                             projectCode = a.fk_project_code ?? string.Empty,
                             projectName = distinctProject.Where(x => x.pk_project_code == a.fk_project_code).Select(y => y.project_name).FirstOrDefault() ?? string.Empty,
                             freeDim1 = a.free_dim_1 ?? string.Empty,
                             freeDim2 = a.free_dim_2 ?? string.Empty,
                             freeDim3 = a.free_dim_3 ?? string.Empty,
                             freeDim4 = a.free_dim_4 ?? string.Empty,
                             freeDimDesc1 = distinctFreeDimValues.Where(x => x.free_dim_code == a.free_dim_1).Select(x => x.description).FirstOrDefault() ?? string.Empty,
                             freeDimDesc2 = distinctFreeDimValues.Where(x => x.free_dim_code == a.free_dim_2).Select(x => x.description).FirstOrDefault() ?? string.Empty,
                             freeDimDesc3 = distinctFreeDimValues.Where(x => x.free_dim_code == a.free_dim_3).Select(x => x.description).FirstOrDefault() ?? string.Empty,
                             freeDimDesc4 = distinctFreeDimValues.Where(x => x.free_dim_code == a.free_dim_4).Select(x => x.description).FirstOrDefault() ?? string.Empty,
                             amountYear1 = a.amount_year_1
                         };

            return result.ToList();
        }

        public async Task<List<YearlyBudgetChangeExportHelper>> GetYearlyBudgetChangeExportAsync(int tenantId, string adjustmentCode, int budgetYear, string orgVersion)
        {
            var baseData = await _tenantDbContext.tfp_trans_detail.Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.fk_adj_code == adjustmentCode).ToListAsync();
            var distinctFreeDimValues = await _tenantDbContext.tco_free_dim_values.Where(x => x.fk_tenant_id == tenantId).Select(x => new { x.free_dim_code, x.description }).ToListAsync();
            var distinctAdjCodes = await _tenantDbContext.tco_user_adjustment_codes
                                    .Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.pk_adj_code == adjustmentCode)
                                    .GroupBy(x => new { x.pk_adj_code, x.case_ref, x.description, x.long_description, x.fk_attribute_id })
                                    .Select(group => new
                                    {
                                        group.Key.pk_adj_code,
                                        group.Key.case_ref,
                                        group.Key.description,
                                        group.Key.long_description,
                                        group.Key.fk_attribute_id,
                                    })
                                    .ToListAsync();

            var distinctAccount = await _tenantDbContext.tco_accounts
                                    .Where(x => x.pk_tenant_id == tenantId)
                                    .GroupBy(x => new { x.pk_account_code, x.display_name })
                                    .Select(account => account.Key)
                                    .ToListAsync();
            var distinctDepValues = await _tenantDbContext.tco_departments
                                    .Where(x => x.fk_tenant_id == tenantId)
                                    .GroupBy(x => new { x.pk_department_code, x.department_name })
                                    .Select(group => group.Key)
                                    .ToListAsync();
            var distinctFunctions = await _tenantDbContext.tco_functions
                                    .Where(x => x.pk_tenant_id == tenantId)
                                    .GroupBy(x => new { x.pk_Function_code, x.display_name })
                                    .Select(group => group.Key)
                                    .ToListAsync();
            var distinctProject = await _tenantDbContext.tco_projects
                                    .Where(x => x.fk_tenant_id == tenantId)
                                    .GroupBy(x => new { x.pk_project_code, x.project_name })
                                    .Select(project => project.Key)
                                    .ToListAsync();
            var result = from a in baseData
                         select new YearlyBudgetChangeExportHelper
                         {
                             pkId = a.pk_id,
                             adjustmentCode = adjustmentCode,
                             adjustmentDescription = distinctAdjCodes.Where(x => x.pk_adj_code == a.fk_adj_code).Select(y => y.description).FirstOrDefault() ?? string.Empty,
                             attributeCode = distinctAdjCodes.Where(x => x.pk_adj_code == a.fk_adj_code).Select(y => y.fk_attribute_id).FirstOrDefault() ?? string.Empty,
                             caseReference = distinctAdjCodes.Where(x => x.pk_adj_code == a.fk_adj_code).Select(y => y.long_description).FirstOrDefault() ?? string.Empty,
                             accountCode = a.fk_account_code ?? string.Empty,
                             accountName = distinctAccount.Where(x => x.pk_account_code == a.fk_account_code).Select(y => y.display_name).FirstOrDefault() ?? string.Empty,
                             departmentCode = a.department_code ?? string.Empty,
                             departmentName = distinctDepValues.Where(x => x.pk_department_code == a.department_code).Select(y => y.department_name).FirstOrDefault() ?? string.Empty,
                             functionCode = a.function_code ?? string.Empty,
                             functionName = distinctFunctions.Where(x => x.pk_Function_code == a.function_code).Select(y => y.display_name).FirstOrDefault() ?? string.Empty,
                             projectCode = a.project_code ?? string.Empty,
                             projectName = distinctProject.Where(x => x.pk_project_code == a.project_code).Select(y => y.project_name).FirstOrDefault() ?? string.Empty,
                             freeDim1 = a.free_dim_1 ?? string.Empty,
                             freeDim2 = a.free_dim_2 ?? string.Empty,
                             freeDim3 = a.free_dim_3 ?? string.Empty,
                             freeDim4 = a.free_dim_4 ?? string.Empty,
                             freeDimDesc1 = distinctFreeDimValues.Where(x => x.free_dim_code == a.free_dim_1).Select(x => x.description).FirstOrDefault() ?? string.Empty,
                             freeDimDesc2 = distinctFreeDimValues.Where(x => x.free_dim_code == a.free_dim_2).Select(x => x.description).FirstOrDefault() ?? string.Empty,
                             freeDimDesc3 = distinctFreeDimValues.Where(x => x.free_dim_code == a.free_dim_3).Select(x => x.description).FirstOrDefault() ?? string.Empty,
                             freeDimDesc4 = distinctFreeDimValues.Where(x => x.free_dim_code == a.free_dim_4).Select(x => x.description).FirstOrDefault() ?? string.Empty,
                             year1Amount = a.year_1_amount,
                             year2Amount = a.year_2_amount,
                             year3Amount = a.year_3_amount,
                             year4Amount = a.year_4_amount,
                             year5Amount = a.year_5_amount,
                         };

            return result.ToList();
        }

        public async Task<List<InvAndFinPlanExportHelper>> GetInvAndFinPlanExportAsync(int tenantId, string adjustmentCode, int budgetYear, bool inv20YearParamBool)
        {
            var distinctFreeDimValues = await _tenantDbContext.tco_free_dim_values.Where(x => x.fk_tenant_id == tenantId).Select(x => new { x.free_dim_code, x.description }).ToListAsync();
            //var InvAndFinPlanExportData = await GetInvAndFinPlanExportData(tenantId, adjustmentCode, budgetYear);
            var s = (_tenantDbContext.tfp_proj_transactions
                                    .Where(x => x.fk_tenant_id == tenantId && x.year >= budgetYear && x.fk_user_adjustment_code == adjustmentCode && x.year <= budgetYear + 20)).ToQueryString();
            var baseData = await _tenantDbContext.tfp_proj_transactions
                                    .Where(x => x.fk_tenant_id == tenantId && x.year >= budgetYear && x.fk_user_adjustment_code == adjustmentCode && x.year <= budgetYear + 20)
                                    .ToListAsync();

            var distinctAdjCodes = await _tenantDbContext.tco_user_adjustment_codes
                                    .Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.pk_adj_code == adjustmentCode)
                                    .GroupBy(x => new { x.pk_adj_code, x.case_ref, x.description, x.long_description, x.fk_attribute_id })
                                    .Select(group => new
                                    {
                                        group.Key.pk_adj_code,
                                        group.Key.case_ref,
                                        group.Key.description,
                                        group.Key.long_description,
                                        group.Key.fk_attribute_id,
                                    })
                                    .ToListAsync();

            var distinctAccount = await _tenantDbContext.tco_accounts
                                    .Where(x => x.pk_tenant_id == tenantId)
                                    .GroupBy(x => new { x.pk_account_code, x.display_name })
                                    .Select(account => account.Key)
                                    .ToListAsync();
            var distinctDepValues = await _tenantDbContext.tco_departments
                                    .Where(x => x.fk_tenant_id == tenantId)
                                    .GroupBy(x => new { x.pk_department_code, x.department_name })
                                    .Select(group => group.Key)
                                    .ToListAsync();
            var distinctFunctions = await _tenantDbContext.tco_functions
                                    .Where(x => x.pk_tenant_id == tenantId)
                                    .GroupBy(x => new { x.pk_Function_code, x.display_name })
                                    .Select(group => group.Key)
                                    .ToListAsync();
            var distinctProject = await _tenantDbContext.tco_projects
                                    .Where(x => x.fk_tenant_id == tenantId)
                                    .GroupBy(x => new { x.pk_project_code, x.project_name })
                                    .Select(project => project.Key)
                                    .ToListAsync();

            var result = from a in baseData
                         select new InvAndFinPlanExportHelper
                         {
                             pkId = a.pk_id,
                             adjustmentCode = adjustmentCode,
                             adjustmentDescription = distinctAdjCodes.Where(x => x.pk_adj_code == a.fk_user_adjustment_code).Select(y => y.description).FirstOrDefault() ?? string.Empty,
                             attributeCode = distinctAdjCodes.Where(x => x.pk_adj_code == a.fk_user_adjustment_code).Select(y => y.fk_attribute_id).FirstOrDefault() ?? string.Empty,
                             caseReference = distinctAdjCodes.Where(x => x.pk_adj_code == a.fk_user_adjustment_code).Select(y => y.case_ref).FirstOrDefault() ?? string.Empty,
                             longDescription = distinctAdjCodes.Where(x => x.pk_adj_code == a.fk_user_adjustment_code).Select(y => y.long_description).FirstOrDefault() ?? string.Empty,
                             accountCode = a.fk_account_code ?? string.Empty,
                             accountName = distinctAccount.Where(x => x.pk_account_code == a.fk_account_code).Select(y => y.display_name).FirstOrDefault() ?? string.Empty,
                             departmentCode = a.fk_department_code ?? string.Empty,
                             departmentName = distinctDepValues.Where(x => x.pk_department_code == a.fk_department_code).Select(y => y.department_name).FirstOrDefault() ?? string.Empty,
                             functionCode = a.fk_function_code ?? string.Empty,
                             functionName = distinctFunctions.Where(x => x.pk_Function_code == a.fk_function_code).Select(y => y.display_name).FirstOrDefault() ?? string.Empty,
                             projectCode = a.fk_project_code ?? string.Empty,
                             projectName = distinctProject.Where(x => x.pk_project_code == a.fk_project_code).Select(y => y.project_name).FirstOrDefault() ?? string.Empty,
                             freeDim1 = a.free_dim_1 ?? string.Empty,
                             freeDim2 = a.free_dim_2 ?? string.Empty,
                             freeDim3 = a.free_dim_3 ?? string.Empty,
                             freeDim4 = a.free_dim_4 ?? string.Empty,
                             freeDimDesc1 = distinctFreeDimValues.Where(x => x.free_dim_code == a.free_dim_1).Select(y => y.description).FirstOrDefault() ?? string.Empty,
                             freeDimDesc2 = distinctFreeDimValues.Where(x => x.free_dim_code == a.free_dim_2).Select(y => y.description).FirstOrDefault() ?? string.Empty,
                             freeDimDesc3 = distinctFreeDimValues.Where(x => x.free_dim_code == a.free_dim_3).Select(y => y.description).FirstOrDefault() ?? string.Empty,
                             freeDimDesc4 = distinctFreeDimValues.Where(x => x.free_dim_code == a.free_dim_4).Select(y => y.description).FirstOrDefault() ?? string.Empty,
                             amountYear1 = baseData.Where(x => x.pk_id == a.pk_id && a.year == budgetYear).Select(y => y.amount).FirstOrDefault(),
                             amountYear2 = baseData.Where(x => x.pk_id == a.pk_id && a.year == budgetYear + 1).Select(y => y.amount).FirstOrDefault(),
                             amountYear3 = baseData.Where(x => x.pk_id == a.pk_id && a.year == budgetYear + 2).Select(y => y.amount).FirstOrDefault(),
                             amountYear4 = baseData.Where(x => x.pk_id == a.pk_id && a.year == budgetYear + 3).Select(y => y.amount).FirstOrDefault(),
                             amountYear5 = baseData.Where(x => x.pk_id == a.pk_id && a.year == budgetYear + 4).Select(y => y.amount).FirstOrDefault(),
                             amountYear6 = baseData.Where(x => x.pk_id == a.pk_id && a.year == budgetYear + 5).Select(y => y.amount).FirstOrDefault(),
                             amountYear7 = baseData.Where(x => x.pk_id == a.pk_id && a.year == budgetYear + 6).Select(y => y.amount).FirstOrDefault(),
                             amountYear8 = baseData.Where(x => x.pk_id == a.pk_id && a.year == budgetYear + 7).Select(y => y.amount).FirstOrDefault(),
                             amountYear9 = baseData.Where(x => x.pk_id == a.pk_id && a.year == budgetYear + 8).Select(y => y.amount).FirstOrDefault(),
                             amountYear10 = baseData.Where(x => x.pk_id == a.pk_id && a.year == budgetYear + 9).Select(y => y.amount).FirstOrDefault(),
                             amountYear11 = baseData.Where(x => x.pk_id == a.pk_id && a.year == budgetYear + 10).Select(y => y.amount).FirstOrDefault(),
                             amountYear12 = baseData.Where(x => x.pk_id == a.pk_id && a.year == budgetYear + 11).Select(y => y.amount).FirstOrDefault(),
                             amountYear13 = baseData.Where(x => x.pk_id == a.pk_id && a.year == budgetYear + 12).Select(y => y.amount).FirstOrDefault(),
                             amountYear14 = baseData.Where(x => x.pk_id == a.pk_id && a.year == budgetYear + 13).Select(y => y.amount).FirstOrDefault(),
                             amountYear15 = baseData.Where(x => x.pk_id == a.pk_id && a.year == budgetYear + 14).Select(y => y.amount).FirstOrDefault(),
                             amountYear16 = baseData.Where(x => x.pk_id == a.pk_id && a.year == budgetYear + 15).Select(y => y.amount).FirstOrDefault(),
                             amountYear17 = baseData.Where(x => x.pk_id == a.pk_id && a.year == budgetYear + 16).Select(y => y.amount).FirstOrDefault(),
                             amountYear18 = baseData.Where(x => x.pk_id == a.pk_id && a.year == budgetYear + 17).Select(y => y.amount).FirstOrDefault(),
                             amountYear19 = baseData.Where(x => x.pk_id == a.pk_id && a.year == budgetYear + 18).Select(y => y.amount).FirstOrDefault(),
                             amountYear20 = baseData.Where(x => x.pk_id == a.pk_id && a.year == budgetYear + 19).Select(y => y.amount).FirstOrDefault(),
                         };
            return result.ToList();
        }

        public async Task<List<string>> GetAuthCodesFromBudAuthAccess(int tenantId, List<int> userRoleIds)
        {
            var data = await (from a in _tenantDbContext.tco_bud_authority_access
                              where a.fk_tenant_id == tenantId
                              && userRoleIds.Contains(a.fk_role_id)
                              select a.fk_bud_auth).Distinct().ToListAsync();
            return data;
        }

        public async Task<List<tco_budget_authority>> GetAuthCodesList(int tenantId, List<string> budAuthCodes)
        {
            var data = await (from a in _tenantDbContext.tco_budget_authority
                              where a.fk_tenant_id == tenantId && budAuthCodes.Contains(a.pk_bud_auth)
                              select a).ToListAsync();
            return data;
        }

        public async Task<string> GetChangeIdFromBudChanges(UserData userDetails, int budgetYear)
        {
            tco_users_settings? userSettings = await _tenantDbContext.tco_users_settings.FirstOrDefaultAsync(x =>
                   x.fk_user_id == userDetails.pk_id && x.tenant_id == userDetails.tenant_id && x.is_active_tenant);
            List<clsBudgetChangesData> data = await (from tfpbc in _tenantDbContext.tfp_budget_changes
                                                     where tfpbc.budget_year == budgetYear && tfpbc.fk_tenant_id == userDetails.tenant_id && tfpbc.org_budget_flag == 0
                                                     orderby tfpbc.change_date ascending
                                                     select new clsBudgetChangesData
                                                     {
                                                         id = tfpbc.pk_change_id,
                                                         budget_change = tfpbc.budget_name,
                                                         approval_reference = tfpbc.approval_reference,
                                                         date = tfpbc.change_date,
                                                         statusVal = tfpbc.status,
                                                         isActive = 0,
                                                         workflowStatus = tfpbc.workflow_status,
                                                         statusCode = tfpbc.status,
                                                         isSelected = tfpbc.is_selected,
                                                         not_approved = tfpbc.not_approved
                                                     }).ToListAsync();
            data.ForEach(x =>
            {
                x.isActive = userSettings == null ? 0 : userSettings.active_change_id_budget_changes == null ? 0 : userSettings.active_change_id_budget_changes == x.id ? 1 : 0;
            });
            clsBudgetChangesData? idObject = data.FirstOrDefault(x => x.isActive == 1);
            string changeId = (idObject == null) ? "-1" : idObject.id.ToString();
            return changeId;
        }

        public async Task<List<tco_user_adjustment_codes>> GetUserAdjCodesBasedOnTenantAndYear(int tenantId, int budgetYear, List<string> orgValues)
        {
            var data = await (from a in _tenantDbContext.tco_user_adjustment_codes
                              where a.fk_tenant_id == tenantId
                              && a.budget_year == budgetYear
                              && (orgValues.Contains(a.org_level_value))
                              orderby a.updated descending
                              select a).ToListAsync();
            return data;
        }

        public async Task<bool> GetParameterForDisplaycol2to4(int tenantId, string parameterName, int budgetYear)
        {
            var paramInfo = await _tenantDbContext.vw_tco_parameters.Where(x => x.fk_tenant_id == tenantId &&
                                                x.param_name == parameterName && x.param_value == budgetYear.ToString() && x.active == 1).ToListAsync();
            return paramInfo.Any() ? false : true;
        }

        public async Task UpdateUserAdjCodeDetails(int tenantId, UserAdjustmentCodeDetailHelper adjustmentCodeInput)
        {
            if (adjustmentCodeInput.pageType == AdjCodePageType.Sam)
            {
                adjustmentCodeInput.decisionDate = DateTime.TryParse(adjustmentCodeInput.dateTimeStr, out var result) ? DateTime.Parse(adjustmentCodeInput.dateTimeStr) : DateTime.Now;
            }
            var userAdjustmentCodeData = await _tenantDbContext.tco_user_adjustment_codes.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.budget_year == adjustmentCodeInput.budgetYear && x.pk_adj_code == adjustmentCodeInput.adjustmentCode);
            if (userAdjustmentCodeData != null)
            {
                userAdjustmentCodeData.description = !string.IsNullOrEmpty(adjustmentCodeInput.adjustmentCodeDesc) ? adjustmentCodeInput.adjustmentCodeDesc : string.Empty;
                userAdjustmentCodeData.case_ref = !string.IsNullOrEmpty(adjustmentCodeInput.caseReference) ? adjustmentCodeInput.caseReference : string.Empty;
                userAdjustmentCodeData.decision_date = adjustmentCodeInput.decisionDate;
                userAdjustmentCodeData.long_description = !string.IsNullOrEmpty(adjustmentCodeInput.longDescription) ? adjustmentCodeInput.longDescription : string.Empty;
                userAdjustmentCodeData.fk_attribute_id = string.IsNullOrEmpty(adjustmentCodeInput.attributeId) ? string.Empty : adjustmentCodeInput.attributeId;
            }
            await _tenantDbContext.SaveChangesAsync();
        }

        public async Task<List<KeyValue>> FetchSalaryAccountData(int tenantId)
        {
            var result = await (from ac in _tenantDbContext.tco_accounts
                                join kc in _tenantDbContext.gmd_reporting_line on ac.fk_kostra_account_code equals kc.fk_kostra_account_code
                                where ac.pk_tenant_id == tenantId && kc.report == "YBUD1" && kc.line_group_id == 3000
                                group ac by new { ac.pk_account_code, ac.display_name } into g
                                select new KeyValue
                                {
                                    Key = g.Key.pk_account_code,
                                    Value = g.Key.pk_account_code + "-" + g.Key.display_name,
                                }).ToListAsync();

            return result;
        }

        public async Task<List<tco_add_on_setup>> FetchAddonSetupById(int tenantId, int budgetYear, List<int> pkIds)
        {
            if (pkIds.Any())
            {
                return await _tenantDbContext.tco_add_on_setup.Where(x => x.fk_tenant_id == tenantId
                                                                          && x.year_from <= budgetYear
                                                                          && x.year_to >= budgetYear
                                                                          && pkIds.Contains(x.pk_id)
                                                                          )
                                                                     .ToListAsync();
            }
            else
            {
                return await _tenantDbContext.tco_add_on_setup.Where(x => x.fk_tenant_id == tenantId
                                                                          && x.year_from <= budgetYear
                                                                          && x.year_to >= budgetYear
                                                                    )
                                                              .ToListAsync();
            }
        }

        public async Task<List<tco_add_on_setup>> FetchAddonSetupByBudgetYear(int tenantId, int budgetYear)
        {
            var _tenantDBContextParallelRead = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await _tenantDBContextParallelRead.tco_add_on_setup.Where(x => x.fk_tenant_id == tenantId && budgetYear >= x.year_from && budgetYear <= x.year_to).ToListAsync();
        }

        public async Task<List<clsStaffingPercentAddOn>> FetchPercentAddonBaseDataAsync(int tenantId, int empId, int budgetYear)
        {
            var _tenantDBContextParallelRead = await _dbContextManager.GetTenantDbContextForParallelReadAsync();

            return await (from ad in _tenantDBContextParallelRead.tbu_employments_add_on
                          join ac in _tenantDBContextParallelRead.tco_accounts
                          on new { a = ad.fk_account_code, b = ad.fk_tenant_id } equals new { a = ac.pk_account_code, b = ac.pk_tenant_id }
                          where ad.fk_tenant_id == tenantId && ad.fk_employment_id == empId && budgetYear >= ac.dateFrom.Year && budgetYear <= ac.dateTo.Year && !string.IsNullOrEmpty(ad.fk_add_on_id)
                          select new clsStaffingPercentAddOn
                          {
                              employmentId = ad.fk_employment_id,
                              addOnId = ad.pk_id,
                              addOnCode = new clsStaffingAddOnDetails { key = ad.fk_add_on_id, value = ad.fk_add_on_id },
                              accountCode = new clsStaffingAddOnDetails { key = ad.fk_account_code, value = ad.fk_account_code },
                              department = ad.fk_department_code,
                              amountMonthly = ad.amount_month,
                              yearlyBudget = ad.amount_month * 12,
                              startPeriod = ad.start_period,
                              endPeriod = ad.end_period,
                              noOfMonths = ad.end_period - ad.start_period + 1,
                              amountYearly = ad.amount_year,
                              includeInHoliday = ad.holiday_flag == 1,
                              includedInPension = ad.pension_flag == 1,
                              includedInTax = ad.tax_flag == 1,
                              isDeleted = false,
                              isSumRow = false
                          }).ToListAsync();
        }

        public async Task<List<clsStaffingPercentAddOn>> FetchForecastPercentAddonBaseDataAsync(int tenantId, int empId, int budgetYear, int forecastPeriod)
        {
            var _tenantDBContextParallelRead = await _dbContextManager.GetTenantDbContextForParallelReadAsync();

            return await (from ad in _tenantDBContextParallelRead.tbu_employments_forecast_add_ons
                          join ac in _tenantDBContextParallelRead.tco_accounts
                          on new { a = ad.fk_account_code, b = ad.fk_tenant_id } equals new { a = ac.pk_account_code, b = ac.pk_tenant_id }
                          where ad.fk_tenant_id == tenantId && 
                                ad.fk_employment_id == empId && 
                                budgetYear >= ac.dateFrom.Year && 
                                budgetYear <= ac.dateTo.Year && 
                                !string.IsNullOrEmpty(ad.fk_add_on_id) &&
                                forecastPeriod == ad.forecast_period
                          select new clsStaffingPercentAddOn
                          {
                              employmentId = ad.fk_employment_id,
                              addOnId = ad.pk_id,
                              addOnCode = new clsStaffingAddOnDetails { key = ad.fk_add_on_id, value = ad.fk_add_on_id },
                              accountCode = new clsStaffingAddOnDetails { key = ad.fk_account_code, value = ad.fk_account_code },
                              department = ad.fk_department_code,
                              amountMonthly = ad.amount_month,
                              yearlyBudget = ad.amount_month * 12,
                              startPeriod = ad.start_period,
                              endPeriod = ad.end_period,
                              noOfMonths = ad.end_period - ad.start_period + 1,
                              amountYearly = ad.amount_year,
                              includeInHoliday = ad.holiday_flag == 1,
                              includedInPension = ad.pension_flag == 1,
                              includedInTax = ad.tax_flag == 1,
                              isDeleted = false,
                              isSumRow = false
                          }).ToListAsync();
        }

        public async Task<tco_add_on_setup> FetchAddonSetupByAddonId(int tenantId, int budgetYear, string addonId)
        {
            var result = await _tenantDbContext.tco_add_on_setup.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.pk_add_on_id.Trim() == addonId.Trim() && budgetYear >= x.year_from && budgetYear <= x.year_to);
            if (result != null)
                return result;
            else
                return new tco_add_on_setup();
        }

        public async Task<tbu_employments> FetchEmployementData(int tenantId, int empId, int budgetYear)
        {
            var _tenantDBContextParallelRead = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            var result = await _tenantDBContextParallelRead.tbu_employments.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.pk_employment_id == empId);
            if (result != null)
                return result;
            else
                return new tbu_employments();
        }

        public async Task<tbu_employments_forecast> FetchForecastEmploymentDataAsync(int tenantId, int empId, int forecastPeriod)
        {
            var _tenantDBContextParallelRead = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            var result = await _tenantDBContextParallelRead.tbu_employments_forecast.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.forecast_period == forecastPeriod && x.pk_employment_id == empId);
            if (result != null)
                return result;
            else
                return new tbu_employments_forecast();
        }


        public async Task<bool> CheckAdjTotalZeroRevised(int tenantId, int budgetYear, List<string> accCode, string adjCode)
        {
            var data = await _tenantDbContext.tbu_trans_detail.Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear && accCode.Contains(x.fk_account_code) && x.fk_adjustment_code == adjCode).ToListAsync();

            bool isAdjZero = data.Select(x => x.amount_year_1).Sum() == 0;
            return isAdjZero;
        }

        public async Task<bool> CheckAdjTotalZeroOriginal(int tenantId, int budgetYear, List<string> accCode, decimal amountToAllocate, List<string> departmentData)
        {
            var data = await _tenantDbContext.tbu_trans_detail.Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear && accCode.Contains(x.fk_account_code) && departmentData.Contains(x.department_code)).ToListAsync();

            bool isAdjZero = data.Select(x => x.amount_year_1).Sum() - amountToAllocate == 0;
            return isAdjZero;
        }

        public async Task<List<tco_application_flag>> GetApplicationFLagData(TenantDBContext staffPlanningDbContext, int tenantId, string flagName, string flagKeyId)
        {
            return await staffPlanningDbContext.tco_application_flag.Where(x => x.fk_tenant_id == tenantId && x.flag_name == flagName && x.flag_key_id == flagKeyId).ToListAsync();
        }

        public async Task<List<tbu_employments>> GetEmploymentsDataList(int tenantId, List<long> empId, int budgetYear)
        {
            var _tenantDBContextParallelRead = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await _tenantDBContextParallelRead.tbu_employments.Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear && empId.Contains(x.pk_employment_id)).ToListAsync();
        }

        public async Task<List<tbu_employments_forecast>> GetEmploymentsForecastDataList(int tenantId, List<long> empId, int budgetYear)
        {
            var _tenantDBContextParallelRead = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await _tenantDBContextParallelRead.tbu_employments_forecast.Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear && empId.Contains(x.pk_employment_id)).ToListAsync();
        }

        public async Task<List<tbu_temp_employments_add_on>> GetTempEmploymentsAddonData(int tenantId, List<Guid> tempEmpId)
        {
            var _tenantDBContextParallelRead = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await _tenantDBContextParallelRead.tbu_temp_employments_add_on.Where(x => x.fk_tenant_id == tenantId && tempEmpId.Contains(x.temp_Employment_Id)).ToListAsync();
        }
        public async Task<List<tbu_temp_employments_forecast_add_on>> GetTempEmploymentsForecastAddonData(int tenantId, List<Guid> tempEmpId)
        {
            var _tenantDBContextParallelRead = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await _tenantDBContextParallelRead.tbu_temp_employments_forecast_add_on.Where(x => x.fk_tenant_id == tenantId && tempEmpId.Contains(x.temp_Employment_Id)).ToListAsync();
        }
        public async Task<List<tmd_salary_acc_def>> GetSalAccDefinition (int tenantId, int budgetYear)
        {
            //add budget year check
            var _tenantDBContextParallelRead = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await _tenantDBContextParallelRead.tmd_salary_acc_def.Where(x => x.fk_tenant_id == tenantId &&
                                                                                    x.year_from <= budgetYear &&
                                                                                x.year_to >= budgetYear).ToListAsync();
        }
        public async Task<List<tco_salary_acc_category>> GetSalCategoryData (int tenantId, int budgetYear,List<string>accList)
        {
            var _tenantDBContextParallelRead = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            
            return await _tenantDBContextParallelRead.tco_salary_acc_category.Where(x => x.fk_tenant_id == tenantId && 
                                                                                         x.year_from <= budgetYear &&
                                                                                         x.year_to >= budgetYear &&
                                                                                         accList.Contains(x.fk_account_code)).ToListAsync();
        }

        public async Task<List<tbu_employments_add_on>> GetEmpAddonDataList(int tenantId, List<long> empId)
        {
            var _tenantDBContextParallelRead = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await _tenantDbContext.tbu_employments_add_on.Where(x=>x.fk_tenant_id ==tenantId &&  empId.Contains(x.fk_employment_id)).ToListAsync();
        }

        public async Task<List<tbu_employments_forecast_add_ons>> GetEmpForecastAddonDataList(int tenantId, List<long> empId)
        {
            var _tenantDBContextParallelRead = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await _tenantDbContext.tbu_employments_forecast_add_ons.Where(x => x.fk_tenant_id == tenantId && empId.Contains(x.fk_employment_id)).ToListAsync();
        }

        public async Task<List<KeyValueDecimalValue>> GetPensionTypesForAccdetailAsync(int tenantId, int budgetYear)
        {
            return await _tenantDbContext.tmd_pension_type.Where(x => x.fk_tenant_id == tenantId && x.year_from <= budgetYear && x.year_to >= budgetYear).
                                          Select(x=> new KeyValueDecimalValue
                                          {
                                              key = x.pension_type,
                                              value = x.pension_name,
                                              rate = x.rate/100
                                          }).ToListAsync();
        }
        public async Task<string> GetDefaultPensionForAccAsync(int tenantId, string accountCode, int budgetYear)
        {
            var pensionDefault =  await _tenantDbContext.tmd_salary_acc_def.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.year_from <= budgetYear && x.year_to >= budgetYear && x.fk_account_code == accountCode);
            return pensionDefault!=null ? pensionDefault.fk_pension_type : string.Empty;
        }

        public async Task<List<Helpers.KeyValuePair>> GetNonSalaryAccountsData (int tenantId, int budgetYear)
        {
            var _tenantDBContextParallelRead = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await (from ac in _tenantDBContextParallelRead.tco_accounts
                          join rl in _tenantDBContextParallelRead.gmd_reporting_line
                          on new { a = ac.fk_kostra_account_code} equals new { a = rl.fk_kostra_account_code}
                          where ac.pk_tenant_id == tenantId && budgetYear >= ac.dateFrom.Year && budgetYear <= ac.dateTo.Year && rl.report == "YBUD1" && rl.line_group_id != 3000
                          orderby ac.pk_account_code
                          select new Helpers.KeyValuePair
                          {
                              key = ac.pk_account_code,
                              value = $"{ac.pk_account_code.ToString()} - {ac.display_name}"
                          }).ToListAsync();
        }

        #endregion Public Methods
    }
}