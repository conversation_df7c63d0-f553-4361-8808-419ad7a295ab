#pragma warning disable CS8602

using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.Data.SqlClient;
using System.Data;
using System.Text;
using System.Text.RegularExpressions;

namespace Framsikt.BL.Repository
{
    public class BudgetFormsRepository : IBudgetFormsRepository
    {
        private readonly IDbContextManager _dbContextManager;
        private readonly IFinUtility _finUtility;

        public BudgetFormsRepository(IDbContextManager dbContextManager, IFinUtility utility)
        {
            _dbContextManager = dbContextManager;
            _finUtility = utility;
        }

        public async Task<IEnumerable<DataRow>> GetFilterData(string userId, int tenantId, FilterDataInput filterinput, IEnumerable<gco_reporting_columns> reportColumns, string docType, string reportType)
        {
            using (SqlConnection cn = await _dbContextManager.GetTenantDbConnectionAsync())
            using (SqlCommand cmd = new SqlCommand())
            {
                string searchString = string.Empty;
                var ValidSelectedColumn = reportColumns.FirstOrDefault(x => filterinput.columnId == (x.column_id));// check if columns passed are valic columns
                string columnId = ValidSelectedColumn.column_id;
                if (!string.IsNullOrEmpty(filterinput.searchInput) && !reportColumns.FirstOrDefault(x => x.column_id == columnId).isCommaSeperatedData)
                {
                    var paramName = "@FilterSearch" + columnId;
                    cmd.Parameters.AddWithValue(paramName, filterinput.searchInput);
                    searchString = " and [" + columnId + "]  like '%' +" + paramName + " + '%'  ";
                }

                string query = $"select [{@columnId}] [{@columnId}]  from vw_budform_report where fk_tenant_id=@tenantId ";

                if (!string.IsNullOrEmpty(filterinput.orgInfo.currentOrgID) && filterinput.orgInfo.currentOrgLevel != 1)
                {
                    if (!filterinput.includeTenantLevelData)
                    {
                        query = query + $" and org_id_{filterinput.orgInfo.currentOrgLevel}=@orgId ";
                        cmd.Parameters.Add(new SqlParameter("@orgId", filterinput.orgInfo.currentOrgID));
                    }
                }

                if (Int32.Parse(docType) == (int)BudgetFormReportDocType.ForecastReport)
                {
                    query = query + " and forecast_period = @forecastPeriod";
                    cmd.Parameters.Add(new SqlParameter("forecastPeriod", filterinput.forecastPeriod));
                }
                else
                {
                    query = query + " and budget_year = @budgetYear";
                    cmd.Parameters.Add(new SqlParameter("budgetYear", filterinput.budgetYear));
                }

                query = query + $" and doc_type=@docType and report=@reportType {searchString}  group by  [{@columnId}] order by [{@columnId}]  ";
                cmd.CommandText = query;
                cmd.Connection = cn;

                cmd.Parameters.Add(new SqlParameter("tenantId", tenantId));
                cmd.Parameters.Add(new SqlParameter("columnId", filterinput.columnId));
                cmd.Parameters.Add(new SqlParameter("docType", docType));
                cmd.Parameters.Add(new SqlParameter("reportType", reportType));
                //Execute the T-SQL
                var reader = await cmd.ExecuteReaderAsync();
                DataTable report = new DataTable("FilterData");
                report.Load(reader);
                IEnumerable<DataRow> filterDataEnumerable = from r in report.AsEnumerable() select r;
                return filterDataEnumerable;
            }
        }
        public StringBuilder AddFormulaLinkedColumnsToHavingClause(StringBuilder havingColumns, List<ReportColumnHelper> ValidSelectedColumnList, ReportInput input, IEnumerable<ReportColumnHelper> reportColumnsList)
        {
            foreach (var item in ValidSelectedColumnList.Where(z => z.DataType == "custom"))
            {
                var pattern = new Regex(@"\b[\w]*\b");
                var matches = pattern.Matches(item.Formula);
                foreach (var forCols in matches)
                {
                    var validCol = reportColumnsList.Any(x => x.ColName == forCols.ToString());
                    if (!input.selectedColumns.Contains(forCols) && validCol)
                    {
                        if (!string.IsNullOrEmpty(havingColumns.ToString()))
                        {
                            havingColumns = havingColumns.Append(" or sum([" + forCols + "]) / " + input.dividedBy + " != 0");
                        }
                        else
                        {
                            havingColumns = havingColumns.Append(" sum([" + forCols + "]) / " + input.dividedBy + " != 0");
                        }
                    }
                }
            }
            return havingColumns;
        }
        public async Task<IEnumerable<DataRow>> ReportDataByParam(string userId, int tenantId, List<string> groupColumnsForParam, List<ReportFiletrColumnParam> whereData, IEnumerable<ReportColumnHelper> allreportColumn, ReportInput input, string docType, string budgetFormsReportType)
        {
            StringBuilder grpColumns = new StringBuilder();
            StringBuilder havingColumns = new StringBuilder();
            StringBuilder conditionBuilderForTag = new StringBuilder();
            using (SqlConnection cn = await _dbContextManager.GetTenantDbConnectionAsync())
            using (SqlCommand cmd = new SqlCommand())
            {
                
                var ValidSelectedColumnList = allreportColumn.Where(x => input.selectedColumns.Contains(x.ColName)).ToList();// check if columns passed are valid columns
                StringBuilder selectedColumns = new StringBuilder();
                StringBuilder gridSearch = new StringBuilder();

                if (ValidSelectedColumnList.Count != input.selectedColumns.Count)
                {
                    throw new InvalidOperationException($"Invalid column selected");
                }
                // selected column formatting
                int index = 0;
                foreach (var item in ValidSelectedColumnList)
                {
                    switch (item.DataType)
                    {
                        case "decimal":
                        case "numeric":
                            selectedColumns.Append("sum([" + item.ColName + "])" + "/" + input.dividedBy + " [" + item.ColName + "],");
                            break;

                        case "string":
                            selectedColumns.Append("[" + item.ColName + "]" + "[" + item.ColName + "],");
                            break;

                        case "custom":
                            string divideString = string.Empty;
                            if (("p0".CompareTo(item.numberFormat) != 0) && ("##0 \\%".CompareTo(item.numberFormat) != 0))
                            {
                                divideString = "/" + input.dividedBy;
                            }

                            selectedColumns.Append("( ISNULL(" + item.Formula + ",0)" + divideString + ") [" + item.DisplayName + "],");
                            break;
                        default:
                            selectedColumns.Append("[" + item.ColName + "]" + " [" + item.ColName + "],");
                            break;
                    }

                    // grid column search input formatting
                    int searchColindex = input.selectedColumns.IndexOf(item.ColName);
                    if (!item.IsCommaSeperatedData && input.gridSearchValues.Count > 0 && input.gridSearchValues.Count != 0 && searchColindex < input.gridSearchValues.Count && !string.IsNullOrEmpty(input.gridSearchValues[input.selectedColumns.IndexOf(item.ColName)]))
                    {
                        var paramName = "@gridSearchColum" + index;
                        cmd.Parameters.AddWithValue(paramName, input.gridSearchValues[input.selectedColumns.IndexOf(item.ColName)]);
                        gridSearch.Append("[" + item.ColName + "]" + " like '%' +" + paramName + " + '%' and ");
                    }
                    index++;
                }

                selectedColumns.Remove((selectedColumns.Length - 1), 1).ToString();// last , from the string for selcted column


                string query = "select " + selectedColumns.ToString() + " from vw_budform_report where fk_tenant_id=@tenantId ";

                if (input.orgInfo != null && !string.IsNullOrEmpty(input.orgInfo.currentOrgID) && input.orgInfo.currentOrgLevel!=1) //excluding tenant level
                {
                    if (!input.includeTenantLevelData)
                    {
                        query = query + $" and org_id_{input.orgInfo.currentOrgLevel}=@orgId ";
                        cmd.Parameters.Add(new SqlParameter("@orgId", input.orgInfo.currentOrgID));
                    }
                }

                if (Int32.Parse(docType) == (int)BudgetFormReportDocType.ForecastReport)
                {
                    query = query + " and forecast_period = @forecastPeriod";
                    cmd.Parameters.Add(new SqlParameter("forecastPeriod", input.forecastPeriod));
                }
                else
                {
                    query = query + " and budget_year = @budgetYear";
                    cmd.Parameters.Add(new SqlParameter("budgetYear", input.budgetYear));
                }

                query = query + " and doc_type=@docType and report=@reportType";

                List<string> idParameterList;
                // adding selected fildata condion
                foreach (var item in whereData.Distinct())
                {
                    index = 0;
                    var validCol = allreportColumn.FirstOrDefault(x => x.ColName == item.columnId);
                    if (validCol == null)
                    {
                        throw new InvalidOperationException($"Invalid column name - {item.columnId}");
                    }

                    if (validCol.IsCommaSeperatedData && validCol.ColName.ToLower() == "tags")
                    {
                        foreach (var id in item.filterValue)
                        {
                            var paramName = "@" + validCol.ColName + "_whereCluse" + index;// create parameter as para0,param1....
                            conditionBuilderForTag.Append($" ({paramName} in (select * from STRING_SPLIT(tags,','))) or");
                            cmd.Parameters.Add(new SqlParameter($"{paramName}", id));
                            index++;
                        }
                        conditionBuilderForTag.Remove((conditionBuilderForTag.Length - 2), 2).ToString();
                        query = query + " and (" + conditionBuilderForTag.ToString() + ")";
                    }
                    else
                    {
                        idParameterList = new List<string>();
                        foreach (var id in item.filterValue.Where(x => x != null).ToList())
                        {
                            var paramName = "@" + validCol.ColName + "_whereCluse" + index;// create parameter as para0,param1....
                            cmd.Parameters.AddWithValue(paramName, id);
                            idParameterList.Add(paramName);
                            index++;
                        }
                        if (idParameterList.Any()) {
                            if(item.filterConditionValues.excludeFilterValues)
                                query = query + " and " + validCol.ColName + " not in (" + string.Join(",", idParameterList) + ")";
                            else
                                query = query + " and " + validCol.ColName + " in (" + string.Join(",", idParameterList) + ")";
                        }
                            
                    }
                }

                if (!string.IsNullOrEmpty(gridSearch.ToString()))// add grid search
                {
                    gridSearch.Remove((gridSearch.Length - 4), 4).ToString();// last grid search
                    query = query + " and " + gridSearch;
                }

                var ValidGroupColumnList = allreportColumn.Where(x => groupColumnsForParam.Contains(x.ColName)).ToList();// check if pass column are valid for group
                if (ValidGroupColumnList.Count != groupColumnsForParam.Count)
                {
                    throw new InvalidOperationException($"Invalid group column name");
                }

                grpColumns = grpColumns.Append(string.Join(", ", ValidGroupColumnList.Select(z => "[" + z.ColName + "]")));// group by clause

                havingColumns = havingColumns.Append(string.Join(" or ", ValidSelectedColumnList.Where(z => z.DataType == "decimal" || z.DataType == "numeric").Select(z => "sum([" + z.ColName + "])" + "/" + input.dividedBy + " !=0"))); // having clause for decimal and numeric column

                havingColumns = AddFormulaLinkedColumnsToHavingClause(havingColumns, ValidSelectedColumnList, input, allreportColumn);
                if (!string.IsNullOrEmpty(grpColumns.ToString()))
                {
                    query = query + "  group by " + grpColumns.ToString();
                }
                if (!string.IsNullOrEmpty(havingColumns.ToString()))
                {
                    query = query + " having " + havingColumns.ToString();
                }

                // order by clause
                string orderByColumn = string.Join(",", ValidSelectedColumnList.Where(x => x.DataType != "numeric" && x.DataType != "decimal").Select(x => x.DataType == "custom" ? "[" + x.DisplayName + "]" : (x.DataType == "datetime" ? "[" + x.ColName + "]" : "[" + x.ColName + "] collate Danish_Norwegian_CI_AS")));
                if (!string.IsNullOrEmpty(orderByColumn))
                {
                    query = query + " order by " + orderByColumn.ToString();
                }

                query = query + " option(recompile)";
                cmd.CommandText = allreportColumn.Any(x => x.DataType == "custom") ? _finUtility.ConfigureSqlWithANSIWarnings(query) : query;
                cmd.Connection = cn;
                cmd.CommandTimeout = 300;
                cmd.Parameters.Add(new SqlParameter("tenantId", tenantId));
                cmd.Parameters.Add(new SqlParameter("docType", docType));
                cmd.Parameters.Add(new SqlParameter("reportType", budgetFormsReportType));

                //Execute the T-SQL
                var reader = await cmd.ExecuteReaderAsync();
                DataTable report = new DataTable("ReportData");
                report.Load(reader);
                IEnumerable<DataRow> reportDataEnumerable = from r in report.AsEnumerable() select r;
                return reportDataEnumerable;
            }
        }
    }
}