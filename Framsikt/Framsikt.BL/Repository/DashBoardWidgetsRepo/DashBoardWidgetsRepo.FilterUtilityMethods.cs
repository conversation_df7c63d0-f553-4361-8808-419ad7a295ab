using Framsikt.BL.Helpers;
using Framsikt.BL.Helpers.ApplicationLogHelpers;
using Framsikt.Entities;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using System.Data;
using System.Text;

namespace Framsikt.BL.Repository;

public partial class DashBoardWidgetsRepo
{


    public int FetchValidAccountLevel(int tenantId, string type)
    {
        var paramData = _tenantDBContext.vw_tco_parameters.FirstOrDefault(z => z.fk_tenant_id == tenantId && z.param_name == type);
        return paramData != null && !string.IsNullOrEmpty(paramData.param_value) ? int.Parse(paramData.param_value) : 3;
    }



    private string GetFormattedMissingId(string level1IdExpanded)
    {
        string formattedLevel = string.Empty;
        if (level1IdExpanded.Contains("deptMiss") || level1IdExpanded.Contains("projMiss") || level1IdExpanded.Contains("funcMiss") || level1IdExpanded.Contains("serviceAreaMiss"))
        {
            var CodeStringToList = level1IdExpanded.Split('-').ToList();
            int count = CodeStringToList.Count();
            formattedLevel = CodeStringToList[count - 1];
        }
        return formattedLevel;
    }



    private string ReplaceStringInQuery(string query)
    {
        query = query.Replace("JOIN [dbo].[tco_accounts] ac ON  a.fk_tenant_id = ac.pk_tenant_id and @BudgetYear between YEAR(ac.dateFrom) and YEAR(ac.dateTo) AND a.fk_account_code = ac.pk_account_code", "");
        query = query.Replace("JOIN [dbo].[gco_kostra_accounts] kc ON ac.fk_kostra_account_code = kc.pk_kostra_account_code AND kc.type='operations'", "");
        query = query.Replace("LEFT JOIN[dbo].[tmd_reporting_line] rl ON a.fk_tenant_id = rl.fk_tenant_id and rl.report = 'MNDRAPP' and a.fk_account_code = rl.fk_account_code", "");
        query = query.Replace("JOIN[dbo].[tco_budform_setup] rl ON '54_OVDRIFT'= rl.report and rl.fk_tenant_id=a.fk_tenant_id  and (a.fk_account_code between rl.account_from and rl.account_to)", "");
        query = query.Replace("JOIN[dbo].[gmd_reporting_line] rl ON '54_OVDRIFT'= rl.report and ac.fk_kostra_account_code = rl.fk_kostra_account_code", "");
        query = query.Replace("and ac.fk_kostra_account_code NOT IN('1580', '1980')", "");

        query = query.Replace("level_1_id=rl.line_result_id,level_1_description=rl.line_result,level_2_id=rl.line_group_id,level_2_description=rl.line_group,level_3_id=rl.line_item_id,level_3_description=rl.line_item,level_4_id=kc.pk_kostra_account_code,level_4_description=kc.display_name,level_5_id=ac.pk_account_code,level_5_description=kc.display_name,", "");
        query = query.Replace("level_1_id=rl.line_result_id,level_1_description=rl.line_result,level_2_id=rl.line_group_id,level_2_description=rl.line_group,level_3_id=rl.line_item_id,level_3_description=rl.line_item,level_4_id=kc.pk_kostra_account_code,level_4_description=kc.display_name,", "");
        query = query.Replace("level_1_id=rl.line_result_id,level_1_description=rl.line_result,level_2_id=rl.line_group_id,level_2_description=rl.line_group,level_3_id=rl.line_item_id,level_3_description=rl.line_item,", "");
        query = query.Replace("level_1_id=rl.line_result_id,level_1_description=rl.line_result,level_2_id=rl.line_group_id,level_2_description=rl.line_group,", "");
        query = query.Replace("level_1_id=rl.line_result_id,level_1_description=rl.line_result,", "");

        query = query.Replace("and rl.level_1_id = @Level2Id", "");
        query = query.Replace("and rl.level_2_id = @Level3Id", "");
        query = query.Replace("and rl.level_3_id = @Level4Id", "");
        query = query.Replace("and rl.level_4_id = @Level5Id", "");
        query = query.Replace("and rl.level_5_id = @Level6Id", "");

        query = query.Replace("and rl.level_1_id = @Level3Id", "");
        query = query.Replace("and rl.level_2_id = @Level4Id", "");
        query = query.Replace("and rl.level_3_id = @Level5Id", "");
        query = query.Replace("and rl.level_4_id = @Level6Id", "");

        query = query.Replace("and rl.level_1_id is Null", "");
        query = query.Replace("and rl.level_2_id is Null", "");
        query = query.Replace("and rl.level_3_id is Null", "");
        query = query.Replace("and rl.level_4_id is Null", "");
        query = query.Replace("and rl.level_5_id is Null", "");

        query = query.Replace("and rl.level_1_id = @Level1Id", "");
        query = query.Replace("and rl.level_2_id = @Level2Id", "");
        query = query.Replace("and rl.level_3_id = @Level3Id", "");
        query = query.Replace("and rl.level_4_id = @Level4Id", "");
        query = query.Replace("and rl.level_5_id = @Level5Id", "");

        query = query.Replace(",level_1_id", "");
        query = query.Replace(",level_1_description", "");
        query = query.Replace(",level_2_id", "");
        query = query.Replace(",level_2_description", "");
        query = query.Replace(",level_3_id", "");
        query = query.Replace(",level_3_description", "");
        query = query.Replace(",level_4_id", "");
        query = query.Replace(",level_4_description", "");
        query = query.Replace(",level_5_id", "");
        query = query.Replace(",level_5_description", "");

        query = query.Replace("level_1_id,", "");
        query = query.Replace("level_2_id,", "");
        query = query.Replace("level_3_id,", "");
        query = query.Replace("level_4_id,", "");
        query = query.Replace("level_5_id,", "");

        query = query.Replace("and rl.line_result_id=@Level1Id", "");
        query = query.Replace("and rl.line_group_id = @Level2Id", "");
        query = query.Replace("and rl.line_item_id = @Level3Id", "");
        query = query.Replace("and kc.pk_kostra_account_code = @Level4Id", "");
        query = query.Replace("and ac.pk_account_code = @Level5Id", "");

        return query;
    }



    private static void SetDynamicinputParameters(AccStatmentWidgetInput gridInput, SqlCommand commA)
    {
        switch (gridInput.RequestedTreeLevel)
        {
            case 2:
                commA.Parameters.Add("@Level1Id", SqlDbType.NVarChar);
                commA.Parameters["@Level1Id"].Value = gridInput.level1IdExpanded; break;
            case 3:
                commA.Parameters.Add("@Level1Id", SqlDbType.NVarChar);
                commA.Parameters["@Level1Id"].Value = gridInput.level1IdExpanded;
                commA.Parameters.Add("@Level2Id", SqlDbType.NVarChar);
                commA.Parameters["@Level2Id"].Value = gridInput.level2IdExpanded; break;
            case 4:
                commA.Parameters.Add("@Level1Id", SqlDbType.NVarChar);
                commA.Parameters["@Level1Id"].Value = gridInput.level1IdExpanded;
                commA.Parameters.Add("@Level2Id", SqlDbType.NVarChar);
                commA.Parameters["@Level2Id"].Value = gridInput.level2IdExpanded;
                commA.Parameters.Add("@Level3Id", SqlDbType.NVarChar);
                commA.Parameters["@Level3Id"].Value = gridInput.level3IdExpanded; break;
            case 5:
                commA.Parameters.Add("@Level1Id", SqlDbType.NVarChar);
                commA.Parameters["@Level1Id"].Value = gridInput.level1IdExpanded;
                commA.Parameters.Add("@Level2Id", SqlDbType.NVarChar);
                commA.Parameters["@Level2Id"].Value = gridInput.level2IdExpanded;
                commA.Parameters.Add("@Level3Id", SqlDbType.NVarChar);
                commA.Parameters["@Level3Id"].Value = gridInput.level3IdExpanded;
                commA.Parameters.Add("@Level4Id", SqlDbType.NVarChar);
                commA.Parameters["@Level4Id"].Value = gridInput.level4IdExpanded; break;
            case 6:
                commA.Parameters.Add("@Level1Id", SqlDbType.NVarChar);
                commA.Parameters["@Level1Id"].Value = gridInput.level1IdExpanded;
                commA.Parameters.Add("@Level2Id", SqlDbType.NVarChar);
                commA.Parameters["@Level2Id"].Value = gridInput.level2IdExpanded;
                commA.Parameters.Add("@Level3Id", SqlDbType.NVarChar);
                commA.Parameters["@Level3Id"].Value = gridInput.level3IdExpanded;
                commA.Parameters.Add("@Level4Id", SqlDbType.NVarChar);
                commA.Parameters["@Level4Id"].Value = gridInput.level4IdExpanded;
                commA.Parameters.Add("@Level5Id", SqlDbType.NVarChar);
                commA.Parameters["@Level5Id"].Value = gridInput.level5IdExpanded; break;
            case 7:
                commA.Parameters.Add("@Level1Id", SqlDbType.NVarChar);
                commA.Parameters["@Level1Id"].Value = gridInput.level1IdExpanded;
                commA.Parameters.Add("@Level2Id", SqlDbType.NVarChar);
                commA.Parameters["@Level2Id"].Value = gridInput.level2IdExpanded;
                commA.Parameters.Add("@Level3Id", SqlDbType.NVarChar);
                commA.Parameters["@Level3Id"].Value = gridInput.level3IdExpanded;
                commA.Parameters.Add("@Level4Id", SqlDbType.NVarChar);
                commA.Parameters["@Level4Id"].Value = gridInput.level4IdExpanded;
                commA.Parameters.Add("@Level5Id", SqlDbType.NVarChar);
                commA.Parameters["@Level5Id"].Value = gridInput.level5IdExpanded;
                commA.Parameters.Add("@Level6Id", SqlDbType.NVarChar);
                commA.Parameters["@Level6Id"].Value = gridInput.level6IdExpanded; break;
        }
    }



    private string SetNamePropertyDataRow(AccStatmentWidgetInput gridInput, DataRow readera)
    {
        switch (gridInput.RequestedTreeLevel)
        {
            case 1: return $"{readera["level_1_id"]} {readera["level_1_description"]}";
            case 2: return $"{readera["level_2_id"]} {readera["level_2_description"]}";
            case 3: return $"{readera["level_3_id"]} {readera["level_3_description"]}";
            case 4: return $"{readera["level_4_id"]} {readera["level_4_description"]}";
            case 5: return $"{readera["level_5_id"]} {readera["level_5_description"]}";
            default: return $"{readera["level_1_id"]} {readera["level_1_description"]}";
        }
    }



    private string SetParentIdDataRow(AccStatmentWidgetInput gridInput, DataRow readera)
    {
        switch (gridInput.RequestedTreeLevel)
        {
            case 1: return null;
            case 2: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}");
            case 3: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}");
            case 4: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}");
            case 5: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}");
            case 6: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{gridInput.level5IdExpanded}");
            default: return null;
        }
    }



    private string SetIdColumDataRow(AccStatmentWidgetInput gridInput, DataRow readera)
    {
        switch (gridInput.RequestedTreeLevel)
        {
            case 1: return Uri.EscapeDataString($"{readera["level_1_id"]}");
            case 2: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{readera["level_2_id"]}");
            case 3: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{readera["level_3_id"]}");
            case 4: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{readera["level_4_id"]}");
            case 5: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{readera["level_5_id"]}");
            case 6: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{gridInput.level5IdExpanded}_{readera["level_6_id"]}");
            default: return Uri.EscapeDataString($"{readera["level_1_id"]}");
        }
    }



    private string AddTreeLevelFetch(string budgetQuery, AccStatmentWidgetInput gridInput)
    {
        if (gridInput.viewType != "1")
        {
            switch (gridInput.RequestedTreeLevel)
            {
                case 2: budgetQuery = budgetQuery + " and rl.level_1_id = @Level1Id"; break;
                case 3: budgetQuery = budgetQuery + " and rl.level_1_id = @Level1Id and rl.level_2_id = @Level2Id"; break;
                case 4: budgetQuery = budgetQuery + " and rl.level_1_id = @Level1Id and rl.level_2_id = @Level2Id and rl.level_3_id = @Level3Id"; break;
                case 5: budgetQuery = budgetQuery + " and rl.level_1_id = @Level1Id and rl.level_2_id = @Level2Id and rl.level_3_id = @Level3Id and rl.level_4_id = @Level4Id"; break;
                case 6: budgetQuery = budgetQuery + " and rl.level_1_id = @Level1Id and rl.level_2_id = @Level2Id and rl.level_3_id = @Level3Id and rl.level_4_id = @Level4Id and rl.level_5_id = @Level5Id"; break;
            }
        }
        else
        {
            switch (gridInput.RequestedTreeLevel)
            {
                case 2: budgetQuery = gridInput.level1IdExpanded == "ZZ" ? budgetQuery + " and rl.level_1_id is Null " : budgetQuery + " and rl.level_1_id = @Level1Id"; break;
                case 3: budgetQuery = gridInput.level2IdExpanded == "ZZ" ? budgetQuery + " and rl.level_1_id is Null and rl.level_2_id is Null " : budgetQuery + " and rl.level_1_id = @Level1Id and rl.level_2_id = @Level2Id"; break;
                case 4: budgetQuery = gridInput.level3IdExpanded == "ZZ" ? budgetQuery + " and rl.level_1_id is Null and rl.level_2_id is Null and rl.level_3_id is Null " : budgetQuery + " and rl.level_1_id = @Level1Id and rl.level_2_id = @Level2Id and rl.level_3_id = @Level3Id"; break;
                case 5: budgetQuery = gridInput.level4IdExpanded == "ZZ" ? budgetQuery + " and rl.level_1_id is Null and rl.level_2_id is Null and rl.level_3_id is Null and rl.level_4_id is Null " : budgetQuery + " and rl.level_1_id = @Level1Id and rl.level_2_id = @Level2Id and rl.level_3_id = @Level3Id and rl.level_4_id = @Level4Id"; break;
                case 6: budgetQuery = gridInput.level5IdExpanded == "ZZ" ? budgetQuery + " and rl.level_1_id is Null and rl.level_2_id is Null and rl.level_3_id is Null and rl.level_4_id is Null and rl.level_5_id is Null" : budgetQuery + " and rl.level_1_id = @Level1Id and rl.level_2_id = @Level2Id and rl.level_3_id = @Level3Id and rl.level_4_id = @Level4Id and rl.level_5_id = @Level5Id"; break;
            }


        }
        return budgetQuery;
    }



    private string GetLevelIdAndDescColumnBasedOnRequestedLevel(AccStatmentWidgetInput gridInput)
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = GetAllValidLevelColumns();

        return validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel) != null ? validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel).Value : validSelectColumForLevels.FirstOrDefault(z => z.Key == 1).Value;
    }



    private string GetValidPartitionColumnBasedOnRequestedLevel(AccStatmentWidgetInput gridInput)
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = GetAllValidLevelColumns();

        return validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel) != null ? validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel).PartitionColumns : validSelectColumForLevels.FirstOrDefault(z => z.Key == 1).PartitionColumns;
    }



    private List<KeyValueIntAccStmt> GetAllValidLevelColumns()
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = new List<KeyValueIntAccStmt>();
        //level 1
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 1,
            Value = $"level_1_id,level_1_description",
            PartitionColumns = "level_1_id"
        });
        //level 2
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 2,
            Value = $"level_1_id,level_1_description,level_2_id,level_2_description",
            PartitionColumns = "level_1_id,level_2_id"
        });
        //level 3
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 3,
            Value = $"level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description",
            PartitionColumns = "level_1_id,level_2_id,level_3_id"
        });
        //level 4
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 4,
            Value = $"level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description,level_4_id,level_4_description",
            PartitionColumns = "level_1_id,level_2_id,level_3_id,level_4_id"
        });
        //level 5
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 5,
            Value = $"level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description,level_4_id,level_4_description,level_5_id,level_5_description",
            PartitionColumns = "level_1_id,level_2_id,level_3_id,level_4_id,level_5_id"
        });

        return validSelectColumForLevels;
    }



    public async Task<IEnumerable<vw_tco_parameters>> GetMonthrepDevitaion(int tenantId)
    {
        return await _tenantDBContext.vw_tco_parameters.Where(x => x.fk_tenant_id == tenantId && x.param_name.Contains("MONHTREP_DEVIATION_")).ToListAsync();
    }



    private string GetLevelIdAndDescColumnBasedOnRequestedLevelOrgView(AccStatmentWidgetInput gridInput)
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = (gridInput.viewType == "0" ? GetAllValidLevelColumnsOrgView(gridInput) : GetAllValidLevelColumnsOrgView_NextLevel(gridInput));

        return validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel) != null ? validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel).Value : validSelectColumForLevels.FirstOrDefault(z => z.Key == 1).Value;
    }



    private string GetValidPartitionColumnBasedOnRequestedLevelOrgView(AccStatmentWidgetInput gridInput)
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = (gridInput.viewType == "0" ? GetAllValidLevelColumnsOrgView(gridInput) : GetAllValidLevelColumnsOrgView_NextLevel(gridInput));

        return validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel) != null ? validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel).PartitionColumns : validSelectColumForLevels.FirstOrDefault(z => z.Key == 1).PartitionColumns;
    }



    private List<KeyValueIntAccStmt> GetAllValidLevelColumnsOrgView(AccStatmentWidgetInput gridInput)
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = new List<KeyValueIntAccStmt>();
        //level 1
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 1,
            Value = $"org_id_" + (gridInput.OrgLevel + 1) + ", org_name_" + (gridInput.OrgLevel + 1),
            PartitionColumns = "org_id_" + (gridInput.OrgLevel + 1)
        });
        //level 2
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 2,
            Value = $"org_id_" + (gridInput.OrgLevel + 1) + ", org_name_" + (gridInput.OrgLevel + 1) + ",level_1_id,level_1_description",
            PartitionColumns = "org_id_" + (gridInput.OrgLevel + 1) + ",level_1_id"
        });
        //level 3
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 3,
            Value = $"org_id_" + (gridInput.OrgLevel + 1) + ", org_name_" + (gridInput.OrgLevel + 1) + ",level_1_id,level_1_description,level_2_id,level_2_description",
            PartitionColumns = "org_id_" + (gridInput.OrgLevel + 1) + ",level_1_id,level_2_id"
        });
        //level 4
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 4,
            Value = $"org_id_" + (gridInput.OrgLevel + 1) + ", org_name_" + (gridInput.OrgLevel + 1) + ",level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description",
            PartitionColumns = "org_id_" + (gridInput.OrgLevel + 1) + ",level_1_id,level_2_id,level_3_id"
        });
        //level 5
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 5,
            Value = $"org_id_" + (gridInput.OrgLevel + 1) + ", org_name_" + (gridInput.OrgLevel + 1) + ",level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description,level_4_id,level_4_description",
            PartitionColumns = "org_id_" + (gridInput.OrgLevel + 1) + ",level_1_id,level_2_id,level_3_id,level_4_id"
        });
        //level 6
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 6,
            Value = $"org_id_" + (gridInput.OrgLevel + 1) + ", org_name_" + (gridInput.OrgLevel + 1) + ",level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description,level_4_id,level_4_description,level_5_id,level_5_description",
            PartitionColumns = "org_id_" + (gridInput.OrgLevel + 1) + ",level_1_id,level_2_id,level_3_id,level_4_id,level_5_id"
        });

        return validSelectColumForLevels;
    }



    private List<KeyValueIntAccStmt> GetAllValidLevelColumnsOrgView_NextLevel(AccStatmentWidgetInput gridInput)
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = new List<KeyValueIntAccStmt>();
        //level 1
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 1,
            Value = $"org_id_" + (gridInput.OrgLevel + 1) + ", org_name_" + (gridInput.OrgLevel + 1),
            PartitionColumns = "org_id_" + (gridInput.OrgLevel + 1)
        });
        //level 2
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 2,
            Value = $"org_id_" + (gridInput.OrgLevel + 1) + ", org_name_" + (gridInput.OrgLevel + 1) + ",org_id_" + (gridInput.OrgLevel + 2) + ", org_name_" + (gridInput.OrgLevel + 2),
            PartitionColumns = "org_id_" + (gridInput.OrgLevel + 1) + ",org_id_" + (gridInput.OrgLevel + 2)
        });
        //level 3
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 3,
            Value = $"org_id_" + (gridInput.OrgLevel + 1) + ", org_name_" + (gridInput.OrgLevel + 1) + ",org_id_" + (gridInput.OrgLevel + 2) + ", org_name_" + (gridInput.OrgLevel + 2) + ",level_1_id,level_1_description",
            PartitionColumns = "org_id_" + (gridInput.OrgLevel + 1) + ",org_id_" + (gridInput.OrgLevel + 2) + ",level_1_id"
        });
        //level 4
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 4,
            Value = $"org_id_" + (gridInput.OrgLevel + 1) + ", org_name_" + (gridInput.OrgLevel + 1) + ",org_id_" + (gridInput.OrgLevel + 2) + ", org_name_" + (gridInput.OrgLevel + 2) + ",level_1_id,level_1_description,level_2_id,level_2_description",
            PartitionColumns = "org_id_" + (gridInput.OrgLevel + 1) + ",org_id_" + (gridInput.OrgLevel + 2) + ",level_1_id,level_2_id"
        });
        //level 5
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 5,
            Value = $"org_id_" + (gridInput.OrgLevel + 1) + ", org_name_" + (gridInput.OrgLevel + 1) + ",org_id_" + (gridInput.OrgLevel + 2) + ", org_name_" + (gridInput.OrgLevel + 2) + ",level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description",
            PartitionColumns = "org_id_" + (gridInput.OrgLevel + 1) + ",org_id_" + (gridInput.OrgLevel + 2) + ",level_1_id,level_2_id,level_3_id"
        });

        //level 6
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 6,
            Value = $"org_id_" + (gridInput.OrgLevel + 1) + ", org_name_" + (gridInput.OrgLevel + 1) + ",org_id_" + (gridInput.OrgLevel + 2) + ", org_name_" + (gridInput.OrgLevel + 2) + ",level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description,level_4_id,level_4_description",
            PartitionColumns = "org_id_" + (gridInput.OrgLevel + 1) + ",org_id_" + (gridInput.OrgLevel + 2) + ",level_1_id,level_2_id,level_3_id,level_4_id"
        });

        //level 7
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 7,
            Value = $"org_id_" + (gridInput.OrgLevel + 1) + ", org_name_" + (gridInput.OrgLevel + 1) + ",org_id_" + (gridInput.OrgLevel + 2) + ", org_name_" + (gridInput.OrgLevel + 2) + ",level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description,level_4_id,level_4_description,level_5_id,level_5_description",
            PartitionColumns = "org_id_" + (gridInput.OrgLevel + 1) + ",org_id_" + (gridInput.OrgLevel + 2) + ",level_1_id,level_2_id,level_3_id,level_4_id,level_5_id"
        });

        return validSelectColumForLevels;
    }



    private string SetIdColumOrgView(AccStatmentWidgetInput gridInput, DataRow readera)
    {
        switch (gridInput.RequestedTreeLevel)
        {
            case 1: return Uri.EscapeDataString($"{readera["org_id_" + (gridInput.OrgLevel + 1)]}");
            case 2: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{readera["level_1_id"]}");
            case 3: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{readera["level_2_id"]}");
            case 4: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{readera["level_3_id"]}");
            case 5: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{readera["level_4_id"]}");
            case 6: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{gridInput.level5IdExpanded}_{readera["level_5_id"]}");
            default: return Uri.EscapeDataString($"{readera["org_id_" + (gridInput.OrgLevel + 1)]}");
        }
    }



    private string SetIdColumOrgView_NextLevel(AccStatmentWidgetInput gridInput, DataRow readera)
    {
        switch (gridInput.RequestedTreeLevel)
        {
            case 1: return Uri.EscapeDataString($"{readera["org_id_" + (gridInput.OrgLevel + 1)]}");
            case 2: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{readera["org_id_" + (gridInput.OrgLevel + 2)]}");
            case 3: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{readera["level_1_id"]}");
            case 4: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{readera["level_2_id"]}");
            case 5: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{readera["level_3_id"]}");
            case 6: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{gridInput.level5IdExpanded}_{readera["level_4_id"]}");
            case 7: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{gridInput.level5IdExpanded}_{gridInput.level6IdExpanded}_{readera["level_5_id"]}");
            default: return Uri.EscapeDataString($"{readera["org_id_" + (gridInput.OrgLevel + 1)]}");
        }
    }



    private string SetNamePropertyOrgView(AccStatmentWidgetInput gridInput, DataRow readera)
    {
        switch (gridInput.RequestedTreeLevel)
        {
            case 1: return $"{readera["org_id_" + (gridInput.OrgLevel + 1)]} {readera["org_name_" + (gridInput.OrgLevel + 1)]}";
            case 2: return $"{readera["level_1_id"]} {readera["level_1_description"]}";
            case 3: return $"{readera["level_2_id"]} {readera["level_2_description"]}";
            case 4: return $"{readera["level_3_id"]} {readera["level_3_description"]}";
            case 5: return $"{readera["level_4_id"]} {readera["level_4_description"]}";
            case 6: return $"{readera["level_5_id"]} {readera["level_5_description"]}";
            default: return $"{readera["org_id_" + (gridInput.OrgLevel + 1)]} {readera["org_name_" + (gridInput.OrgLevel + 1)]}";
        }
    }



    private string SetNamePropertyOrgView_NextLevel(AccStatmentWidgetInput gridInput, DataRow readera)
    {
        switch (gridInput.RequestedTreeLevel)
        {
            case 1: return $"{readera["org_id_" + (gridInput.OrgLevel + 1)]} {readera["org_name_" + (gridInput.OrgLevel + 1)]}";
            case 2: return $"{readera["org_id_" + (gridInput.OrgLevel + 2)]} {readera["org_name_" + (gridInput.OrgLevel + 2)]}";
            case 3: return $"{readera["level_1_id"]} {readera["level_1_description"]}";
            case 4: return $"{readera["level_2_id"]} {readera["level_2_description"]}";
            case 5: return $"{readera["level_3_id"]} {readera["level_3_description"]}";
            case 6: return $"{readera["level_4_id"]} {readera["level_4_description"]}";
            case 7: return $"{readera["level_5_id"]} {readera["level_5_description"]}";
            default: return $"{readera["org_id_" + (gridInput.OrgLevel + 1)]} {readera["org_name_" + (gridInput.OrgLevel + 1)]}";
        }
    }



    private string SetNamePropertyMissingDept_Org(AccStatmentWidgetInput gridInput, DataRow readera, bool department_setup_missing, bool org_setup_missing)
    {
        string orgSetupMissingId = "Org.oppsett mangler";
        string deptMissing = "- Ugyldig ansvar";
        switch (gridInput.RequestedTreeLevel)
        {
            case 1: return ((department_setup_missing && org_setup_missing) ? ($"{orgSetupMissingId}") : ($"{deptMissing}"));
            case 2: return $"{readera["level_1_id"]} {readera["level_1_description"]}";
            case 3: return $"{readera["level_2_id"]} {readera["level_2_description"]}";
            case 4: return $"{readera["level_3_id"]} {readera["level_3_description"]}";
            case 5: return $"{readera["level_4_id"]} {readera["level_4_description"]}";
            case 6: return $"{readera["level_5_id"]} {readera["level_5_description"]}";
            default: return $"{readera["org_id_" + (gridInput.OrgLevel + 1)]} {readera["org_name_" + (gridInput.OrgLevel)]}";
        }
    }



    private string GetLevelIdAndDescColumnBasedOnRequestedLevelMissingDept_OrgeSetup(AccStatmentWidgetInput gridInput)
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = GetAllValidLevelColumnsMissingDept_OrgeSetup(gridInput);

        return validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel) != null ? validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel).Value : validSelectColumForLevels.FirstOrDefault(z => z.Key == 1).Value;
    }



    private string GetValidPartitionColumnBasedOnRequestedLevelMissingDept_OrgeSetup(AccStatmentWidgetInput gridInput)
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = GetAllValidLevelColumnsMissingDept_OrgeSetup(gridInput);

        return validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel) != null ? validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel).PartitionColumns : validSelectColumForLevels.FirstOrDefault(z => z.Key == 1).PartitionColumns;
    }



    private List<KeyValueIntAccStmt> GetAllValidLevelColumnsMissingDept_OrgeSetup(AccStatmentWidgetInput gridInput)
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = new List<KeyValueIntAccStmt>();
        //level 1
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 1,
            Value = $"org_id_" + (gridInput.OrgLevel) + ", org_name_" + (gridInput.OrgLevel),
            PartitionColumns = "org_id_" + (gridInput.OrgLevel)
        });
        //level 2
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 2,
            Value = $"org_id_" + (gridInput.OrgLevel) + ", org_name_" + (gridInput.OrgLevel) + ",level_1_id,level_1_description",
            PartitionColumns = "org_id_" + (gridInput.OrgLevel) + ",level_1_id"
        });
        //level 3
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 3,
            Value = $"org_id_" + (gridInput.OrgLevel) + ", org_name_" + (gridInput.OrgLevel) + ",level_1_id,level_1_description,level_2_id,level_2_description",
            PartitionColumns = "org_id_" + (gridInput.OrgLevel) + ",level_1_id,level_2_id"
        });
        //level 4
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 4,
            Value = $"org_id_" + (gridInput.OrgLevel) + ", org_name_" + (gridInput.OrgLevel) + ",level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description",
            PartitionColumns = "org_id_" + (gridInput.OrgLevel) + ",level_1_id,level_2_id,level_3_id"
        });

        //level 5
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 5,
            Value = $"org_id_" + (gridInput.OrgLevel) + ", org_name_" + (gridInput.OrgLevel) + ",level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description,level_4_id, level_4_description",
            PartitionColumns = "org_id_" + (gridInput.OrgLevel) + ",level_1_id,level_2_id,level_3_id,level_4_id"
        });

        return validSelectColumForLevels;
    }



    private string SetIdColumMissingDept_Org(AccStatmentWidgetInput gridInput, DataRow readera, bool department_setup_missing, bool org_setup_missing, string userId, int tenantId)
    {
        try
        {
            string orgSetupMissingId = "-00-orgMiss-" + gridInput.OrgLevel;
            string deptMissing = "00-deptMiss-" + $"{readera["org_id_" + (gridInput.OrgLevel)]}";

            switch (gridInput.RequestedTreeLevel)
            {
                case 1: return ((department_setup_missing && org_setup_missing) ? Uri.EscapeDataString($"{orgSetupMissingId}") : Uri.EscapeDataString($"{deptMissing}"));
                case 2: return ((department_setup_missing && org_setup_missing) ? Uri.EscapeDataString($"{orgSetupMissingId}_{readera["level_1_id"]}") : Uri.EscapeDataString($"{deptMissing}_{readera["level_1_id"]}"));
                case 3: return ((department_setup_missing && org_setup_missing) ? Uri.EscapeDataString($"{orgSetupMissingId}_{gridInput.level2IdExpanded}_{readera["level_2_id"]}") : Uri.EscapeDataString($"{deptMissing}_{gridInput.level2IdExpanded}_{readera["level_2_id"]}"));
                case 4: return ((department_setup_missing && org_setup_missing) ? Uri.EscapeDataString($"{orgSetupMissingId}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{readera["level_3_id"]}") : Uri.EscapeDataString($"{deptMissing}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{readera["level_3_id"]}"));
                case 5: return ((department_setup_missing && org_setup_missing) ? Uri.EscapeDataString($"{orgSetupMissingId}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{readera["level_4_id"]}") : Uri.EscapeDataString($"{deptMissing}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{readera["level_4_id"]}"));
                case 6: return ((department_setup_missing && org_setup_missing) ? Uri.EscapeDataString($"{orgSetupMissingId}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{gridInput.level5IdExpanded}_{readera["level_5_id"]}") : Uri.EscapeDataString($"{deptMissing}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{gridInput.level5IdExpanded}_{readera["level_5_id"]}"));

                default: return Uri.EscapeDataString($"{readera["org_id_" + (gridInput.OrgLevel + 1)]}");
            }
        }
        catch (Exception ex)
        {
            PerformanceLogger.LogPerfTimerBegin(userId, tenantId, $"AccStatment_SetIdColumMissingDept_Org", "AccStatementGrid", ex.Message + "InputJson_" + JObject.FromObject(gridInput).ToString() + "CurrentDataRow_" + readera.ToString());
            return Uri.EscapeDataString($"{readera["org_id_" + (gridInput.OrgLevel + 1)]}");
        }
    }



    private string SetParentIdDataRowMissingDept_OrgSetup(AccStatmentWidgetInput gridInput, DataRow readera, bool department_setup_missing, bool org_setup_missing)
    {
        string orgSetupMissingId = "-00-orgMiss-" + gridInput.OrgLevel;
        string deptMissing = "00-deptMiss-" + $"{readera["org_id_" + (gridInput.OrgLevel)]}";
        switch (gridInput.RequestedTreeLevel)
        {
            case 1: return null;
            case 2: return ((department_setup_missing && org_setup_missing) ? Uri.EscapeDataString(orgSetupMissingId) : Uri.EscapeDataString($"{deptMissing}"));
            case 3: return ((department_setup_missing && org_setup_missing) ? Uri.EscapeDataString($"{orgSetupMissingId}_{gridInput.level2IdExpanded}") : Uri.EscapeDataString($"{deptMissing}_{gridInput.level2IdExpanded}"));
            case 4: return ((department_setup_missing && org_setup_missing) ? Uri.EscapeDataString($"{orgSetupMissingId}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}") : Uri.EscapeDataString($"{deptMissing}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}"));
            case 5: return ((department_setup_missing && org_setup_missing) ? Uri.EscapeDataString($"{orgSetupMissingId}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}") : Uri.EscapeDataString($"{deptMissing}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}"));
            case 6: return ((department_setup_missing && org_setup_missing) ? Uri.EscapeDataString($"{orgSetupMissingId}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{gridInput.level5IdExpanded}") : Uri.EscapeDataString($"{deptMissing}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{gridInput.level5IdExpanded}"));
            case 7: return ((department_setup_missing && org_setup_missing) ? Uri.EscapeDataString($"{orgSetupMissingId}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{gridInput.level5IdExpanded}_{gridInput.level6IdExpanded}") : Uri.EscapeDataString($"{deptMissing}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{gridInput.level5IdExpanded}_{gridInput.level6IdExpanded}"));
            default: return null;
        }
    }



    private bool GetBoolValue(int v)
    {
        if (v == 0)
            return false;
        else return true;
    }



    private DataTable SetEmptyLevelsForAccountView(DataTable dt, int RequestedTreeLevel)
    {
        foreach (DataRow dr in dt.Rows)
        {
            if (RequestedTreeLevel == 1)
            {
                if (dr["level_1_id"] == null || Convert.ToString(dr["level_1_id"]) == string.Empty)
                {
                    dr["level_1_id"] = "ZZ";
                    dr["level_1_description"] = "Mangler oppsett";
                }
            }

            if (RequestedTreeLevel == 2)
            {
                if (dr["level_1_id"] == null || Convert.ToString(dr["level_1_id"]) == string.Empty)
                {
                    dr["level_1_id"] = "ZZ";
                    dr["level_1_description"] = "Mangler oppsett";
                }
                if (dr["level_2_id"] == null || Convert.ToString(dr["level_2_id"]) == string.Empty)
                {
                    dr["level_2_id"] = "ZZ";
                    dr["level_2_description"] = "Mangler oppsett";
                }
            }
            if (RequestedTreeLevel == 3)
            {
                if (dr["level_1_id"] == null || Convert.ToString(dr["level_1_id"]) == string.Empty)
                {
                    dr["level_1_id"] = "ZZ";
                    dr["level_1_description"] = "Mangler oppsett";
                }

                if (dr["level_2_id"] == null || Convert.ToString(dr["level_2_id"]) == string.Empty)
                {
                    dr["level_2_id"] = "ZZ";
                    dr["level_2_description"] = "Mangler oppsett";
                }
                if (dr["level_3_id"] == null || Convert.ToString(dr["level_3_id"]) == string.Empty)
                {
                    dr["level_3_id"] = "ZZ";
                    dr["level_3_description"] = "Mangler oppsett";
                }
            }
            if (RequestedTreeLevel == 4)
            {
                if (dr["level_1_id"] == null || Convert.ToString(dr["level_1_id"]) == string.Empty)
                {
                    dr["level_1_id"] = "ZZ";
                    dr["level_1_description"] = "Mangler oppsett";
                }

                if (dr["level_2_id"] == null || Convert.ToString(dr["level_2_id"]) == string.Empty)
                {
                    dr["level_2_id"] = "ZZ";
                    dr["level_2_description"] = "Mangler oppsett";
                }
                if (dr["level_3_id"] == null || Convert.ToString(dr["level_3_id"]) == string.Empty)
                {
                    dr["level_3_id"] = "ZZ";
                    dr["level_3_description"] = "Mangler oppsett";
                }
                if (dr["level_4_id"] == null || Convert.ToString(dr["level_4_id"]) == string.Empty)
                {
                    dr["level_4_id"] = "ZZ";
                    dr["level_4_description"] = "Mangler oppsett";
                }
            }

            if (RequestedTreeLevel == 5)
            {
                if (dr["level_1_id"] == null || Convert.ToString(dr["level_1_id"]) == string.Empty)
                {
                    dr["level_1_id"] = "ZZ";
                    dr["level_1_description"] = "Mangler oppsett";
                }

                if (dr["level_2_id"] == null || Convert.ToString(dr["level_2_id"]) == string.Empty)
                {
                    dr["level_2_id"] = "ZZ";
                    dr["level_2_description"] = "Mangler oppsett";
                }

                if (dr["level_3_id"] == null || Convert.ToString(dr["level_3_id"]) == string.Empty)
                {
                    dr["level_3_id"] = "ZZ";
                    dr["level_3_description"] = "Mangler oppsett";
                }
                if (dr["level_4_id"] == null || Convert.ToString(dr["level_4_id"]) == string.Empty)
                {
                    dr["level_4_id"] = "ZZ";
                    dr["level_4_description"] = "Mangler oppsett";
                }
                if (dr["level_5_id"] == null || Convert.ToString(dr["level_5_id"]) == string.Empty)
                {
                    dr["level_5_id"] = "ZZ";
                    dr["level_5_description"] = "Mangler oppsett";
                }
            }
        }

        return dt;
    }



    private string GetLevelIdAndDescColumnBasedOnRequestedLevelBudGetForm(AccStatmentWidgetInput gridInput)
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = GetAllValidLevelColumnsBudForm();

        return validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel) != null ? validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel).Value : validSelectColumForLevels.FirstOrDefault(z => z.Key == 1).Value;
    }



    private List<KeyValueIntAccStmt> GetAllValidLevelColumnsBudForm()
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = new List<KeyValueIntAccStmt>();
        //level1
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 1,
            Value = $"level_1_id=rl.line_result_id,level_1_description=rl.line_result",
            PartitionColumns = "level_1_id"
        });
        //level2
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 2,
            Value = $"level_1_id=rl.line_result_id,level_1_description=rl.line_result,level_2_id=rl.line_group_id,level_2_description=rl.line_group",
            PartitionColumns = "level_1_id,level_2_id"
        });
        //level3
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 3,
            Value = $"level_1_id=rl.line_result_id,level_1_description=rl.line_result,level_2_id=rl.line_group_id,level_2_description=rl.line_group,level_3_id=rl.line_item_id,level_3_description=rl.line_item",
            PartitionColumns = "level_1_id,level_2_id,level_3_id"
        });
        //level4
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 4,
            Value = $"level_1_id=rl.line_result_id,level_1_description=rl.line_result,level_2_id=rl.line_group_id,level_2_description=rl.line_group,level_3_id=rl.line_item_id,level_3_description=rl.line_item,level_4_id=kc.pk_kostra_account_code,level_4_description=kc.display_name",
            PartitionColumns = "level_1_id,level_2_id,level_3_id,level_4_id"
        });
        //level5
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 5,
            Value = $"level_1_id=rl.line_result_id,level_1_description=rl.line_result,level_2_id=rl.line_group_id,level_2_description=rl.line_group,level_3_id=rl.line_item_id,level_3_description=rl.line_item,level_4_id=kc.pk_kostra_account_code,level_4_description=kc.display_name,level_5_id=ac.pk_account_code,level_5_description=kc.display_name",
            PartitionColumns = "level_1_id,level_2_id,level_3_id,level_4_id,level_5_id"
        });

        return validSelectColumForLevels;
    }



    private async Task<bool> useTcoBudFormSetupTable(int tenantId, string type)
    {
        var paramData = await _tenantDBContext.vw_tco_parameters.FirstOrDefaultAsync(z => z.fk_tenant_id == tenantId && z.param_name == type);
        return paramData != null && !string.IsNullOrEmpty(paramData.param_value) ? bool.Parse(paramData.param_value) : false;
    }



    private string GetValidPartitionColumnBasedOnRequestedLevelProjView(AccStatmentWidgetInput gridInput)
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = (!gridInput.isMissingProjectExpanded) ? GetAllValidLevelColumnsProjView(gridInput) : GetAllValidLevelColumnsProjView_MissingProj(gridInput);

        return validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel) != null ? validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel).PartitionColumns : validSelectColumForLevels.FirstOrDefault(z => z.Key == 1).PartitionColumns;
    }



    private string GetLevelIdAndDescColumnBasedOnRequestedLevelProjView(AccStatmentWidgetInput gridInput)
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = (!gridInput.isMissingProjectExpanded) ? GetAllValidLevelColumnsProjView(gridInput) : GetAllValidLevelColumnsProjView_MissingProj(gridInput);

        return validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel) != null ? validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel).Value : validSelectColumForLevels.FirstOrDefault(z => z.Key == 1).Value;
    }



    private List<KeyValueIntAccStmt> GetAllValidLevelColumnsProjView(AccStatmentWidgetInput gridInput)
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = new List<KeyValueIntAccStmt>();
        //level 1
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 1,
            Value = $"pk_project_code,project_name",
            PartitionColumns = "pk_project_code"
        });
        //level 2
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 2,
            Value = $"pk_project_code,project_name,level_1_id,level_1_description",
            PartitionColumns = "pk_project_code,level_1_id"
        });
        //level 3
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 3,
            Value = $"pk_project_code,project_name,level_1_id,level_1_description,level_2_id,level_2_description",
            PartitionColumns = "pk_project_code,level_1_id,level_2_id"
        });
        //level 4
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 4,
            Value = $"pk_project_code,project_name,level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description",
            PartitionColumns = "pk_project_code,level_1_id,level_2_id,level_3_id"
        });
        //level 5
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 5,
            Value = $"pk_project_code,project_name,level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description,level_4_id,level_4_description",
            PartitionColumns = "pk_project_code,level_1_id,level_2_id,level_3_id,level_4_id"
        });
        //level 6
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 6,
            Value = $"pk_project_code,project_name,level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description,level_4_id,level_4_description,level_5_id,level_5_description",
            PartitionColumns = "pk_project_code,level_1_id,level_2_id,level_3_id,level_4_id,level_5_id"
        });

        return validSelectColumForLevels;
    }



    private string SetNamePropertyProjView(AccStatmentWidgetInput gridInput, DataRow readera)
    {
        switch (gridInput.RequestedTreeLevel)
        {
            case 1: return $"{readera["pk_project_code"]} {readera["project_name"]}";
            case 2: return $"{readera["level_1_id"]} {readera["level_1_description"]}";
            case 3: return $"{readera["level_2_id"]} {readera["level_2_description"]}";
            case 4: return $"{readera["level_3_id"]} {readera["level_3_description"]}";
            case 5: return $"{readera["level_4_id"]} {readera["level_4_description"]}";
            case 6: return $"{readera["level_5_id"]} {readera["level_5_description"]}";
            default: return $"{readera["pk_project_code"]} {readera["project_name"]}";
        }
    }



    private string SetIdColumMissingProj(AccStatmentWidgetInput gridInput, DataRow readera, bool proj_setup_missing, string userId, int tenantId)
    {
        try
        {
            string projSetupMissingId = "-00-projMiss-" + gridInput.OrgId;

            switch (gridInput.RequestedTreeLevel)
            {
                case 1: return (Uri.EscapeDataString($"{projSetupMissingId}"));
                case 2: return (Uri.EscapeDataString($"{projSetupMissingId}_{readera["level_1_id"]}"));
                case 3: return (Uri.EscapeDataString($"{projSetupMissingId}_{gridInput.level2IdExpanded}_{readera["level_2_id"]}"));
                case 4: return (Uri.EscapeDataString($"{projSetupMissingId}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{readera["level_3_id"]}"));
                case 5: return (Uri.EscapeDataString($"{projSetupMissingId}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{readera["level_4_id"]}"));
                case 6: return (Uri.EscapeDataString($"{projSetupMissingId}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{gridInput.level5IdExpanded}_{readera["level_5_id"]}"));

                default: return Uri.EscapeDataString($"{readera["pk_project_code"]}");
            }
        }
        catch (Exception ex)
        {
            PerformanceLogger.LogPerfTimerBegin(userId, tenantId, $"AccStatment_SetIdColumMissingProj", "AccStatementGrid", ex.Message + "InputJson_" + JObject.FromObject(gridInput).ToString() + "CurrentDataRow_" + readera.ToString());
            return Uri.EscapeDataString($"{readera["org_id_" + (gridInput.OrgLevel + 1)]}");
        }
    }



    private string SetParentIdDataRowMissingProjSetup(AccStatmentWidgetInput gridInput, DataRow readera, bool proj_setup_missing)
    {
        string projSetupMissingId = "-00-projMiss-" + gridInput.OrgId;
        switch (gridInput.RequestedTreeLevel)
        {
            case 1: return null;
            case 2: return (Uri.EscapeDataString(projSetupMissingId));
            case 3: return (Uri.EscapeDataString($"{projSetupMissingId}_{gridInput.level2IdExpanded}"));
            case 4: return (Uri.EscapeDataString($"{projSetupMissingId}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}"));
            case 5: return (Uri.EscapeDataString($"{projSetupMissingId}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}"));
            case 6: return (Uri.EscapeDataString($"{projSetupMissingId}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{gridInput.level5IdExpanded}"));
            case 7: return (Uri.EscapeDataString($"{projSetupMissingId}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{gridInput.level5IdExpanded}_{gridInput.level6IdExpanded}"));
            default: return null;
        }
    }



    private string SetNamePropertyMissingProj(AccStatmentWidgetInput gridInput, DataRow readera, bool proj_setup_missing, Dictionary<string, clsLanguageString> langStringValues)
    {
        string projSetupMissing = langStringValues["AccStmt_ProjectSetupMissing"].LangText;
        switch (gridInput.RequestedTreeLevel)
        {
            case 1: return (($"{projSetupMissing}"));
            case 2: return $"{readera["level_1_id"]} {readera["level_1_description"]}";
            case 3: return $"{readera["level_2_id"]} {readera["level_2_description"]}";
            case 4: return $"{readera["level_3_id"]} {readera["level_3_description"]}";
            case 5: return $"{readera["level_4_id"]} {readera["level_4_description"]}";
            case 6: return $"{readera["level_5_id"]} {readera["level_5_description"]}";
            default: return $"{readera["pk_project_code"]} {readera["project_name"]}";
        }
    }



    private List<KeyValueIntAccStmt> GetAllValidLevelColumnsProjView_MissingProj(AccStatmentWidgetInput gridInput)
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = new List<KeyValueIntAccStmt>();
        //level 1
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 1,
            Value = $"pk_project_code",
            PartitionColumns = "pk_project_code"
        });
        //level 2
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 2,
            Value = $"level_1_id,level_1_description",
            PartitionColumns = "level_1_id"
        });
        //level 3
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 3,
            Value = $"level_1_id,level_1_description,level_2_id,level_2_description",
            PartitionColumns = "level_1_id,level_2_id"
        });
        //level 4
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 4,
            Value = $"level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description",
            PartitionColumns = "level_1_id,level_2_id,level_3_id"
        });
        //level 5
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 5,
            Value = $"level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description,level_4_id,level_4_description",
            PartitionColumns = "level_1_id,level_2_id,level_3_id,level_4_id"
        });
        //level 6
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 6,
            Value = $"level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description,level_4_id,level_4_description,level_5_id,level_5_description",
            PartitionColumns = "level_1_id,level_2_id,level_3_id,level_4_id,level_5_id"
        });

        return validSelectColumForLevels;
    }



    //#endregion

    //#region service area view

    private string GetLevelIdAndDescColumnBasedOnRequestedLevelSAView(AccStatmentWidgetInput gridInput)
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = (!gridInput.isMissingProjectExpanded) ? GetAllValidLevelColumnsSAView(gridInput) : GetAllValidLevelColumnsFuncView_MissingSA(gridInput);

        return validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel) != null ? validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel).Value : validSelectColumForLevels.FirstOrDefault(z => z.Key == 1).Value;
    }



    public string GetLevelIdAndDescColumnBasedOnRequestedLevelSAMissingView(AccStatmentWidgetInput gridInput)
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = GetAllValidLevelColumnsFuncView_MissingSA(gridInput);

        return validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel) != null ? validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel).Value : validSelectColumForLevels.FirstOrDefault(z => z.Key == 1).Value;
    }



    private List<KeyValueIntAccStmt> GetAllValidLevelColumnsSAView(AccStatmentWidgetInput gridInput)
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = new List<KeyValueIntAccStmt>();
        //level 1
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 1,
            Value = $"tco.service_id_2,tco.service_name_2",
            PartitionColumns = "service_id_2"
        });
        //level 2
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 2,
            Value = $"tco.service_id_2,tco.service_name_2,tco.service_id_3,tco.service_name_3",
            PartitionColumns = "service_id_2, service_id_3"
        });
        //level 3
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 3,
            Value = $"tco.service_id_2,tco.service_name_2,tco.service_id_3,tco.service_name_3,level_1_id,level_1_description",
            PartitionColumns = "service_id_2, service_id_3,level_1_id"
        });
        //level 4
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 4,
            Value = $"tco.service_id_2,tco.service_name_2,tco.service_id_3,tco.service_name_3,level_1_id,level_1_description,level_2_id,level_2_description",
            PartitionColumns = "service_id_2, service_id_3,level_1_id,level_2_id"
        });
        //level 5
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 5,
            Value = $"tco.service_id_2,tco.service_name_2,tco.service_id_3,tco.service_name_3,level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description",
            PartitionColumns = "service_id_2, service_id_3,level_1_id,level_2_id,level_3_id"
        });
        //level 6
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 6,
            Value = $"tco.service_id_2,tco.service_name_2,tco.service_id_3,tco.service_name_3,level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description,level_4_id,level_4_description",
            PartitionColumns = "service_id_2, service_id_3,level_1_id,level_2_id,level_3_id,level_4_id"
        });
        //level 7
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 7,
            Value = $"tco.service_id_2,tco.service_name_2,tco.service_id_3,tco.service_name_3,level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description,level_4_id,level_4_description,level_5_id,level_5_description",
            PartitionColumns = "service_id_2, service_id_3,level_1_id,level_2_id,level_3_id,level_4_id,level_5_id"
        });

        return validSelectColumForLevels;
    }



    private string GetValidPartitionColumnBasedOnRequestedLevelSAView(AccStatmentWidgetInput gridInput)
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = (!gridInput.isMissingProjectExpanded) ? GetAllValidLevelColumnsSAView(gridInput) : GetAllValidLevelColumnsFuncView_MissingSA(gridInput);

        return validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel) != null ? validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel).PartitionColumns : validSelectColumForLevels.FirstOrDefault(z => z.Key == 1).PartitionColumns;
    }



    private string GetValidPartitionColumnBasedOnRequestedLevelSAMissingView(AccStatmentWidgetInput gridInput)
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = GetAllValidLevelColumnsFuncView_MissingSA(gridInput);

        return validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel) != null ? validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel).PartitionColumns : validSelectColumForLevels.FirstOrDefault(z => z.Key == 1).PartitionColumns;
    }



    private string SetIdColumMissingSA(AccStatmentWidgetInput gridInput, DataRow readera, bool proj_setup_missing, string userId, int tenantId)
    {
        try
        {
            string functionSetupMissingId = "00-serviceAreaMiss-" + gridInput.OrgId;

            switch (gridInput.RequestedTreeLevel)
            {
                case 1: return (Uri.EscapeDataString($"{functionSetupMissingId}"));
                case 2: return (Uri.EscapeDataString($"{functionSetupMissingId}_{readera["pk_function_code"]}"));
                case 3: return (Uri.EscapeDataString($"{functionSetupMissingId}_{gridInput.level2IdExpanded}_{readera["level_1_id"]}"));
                case 4: return (Uri.EscapeDataString($"{functionSetupMissingId}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{readera["level_2_id"]}"));
                case 5: return (Uri.EscapeDataString($"{functionSetupMissingId}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{readera["level_3_id"]}"));
                case 6: return (Uri.EscapeDataString($"{functionSetupMissingId}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{gridInput.level5IdExpanded}_{readera["level_4_id"]}"));
                case 7: return (Uri.EscapeDataString($"{functionSetupMissingId}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{gridInput.level5IdExpanded}_{gridInput.level6IdExpanded}_{readera["level_5_id"]}"));

                default: return Uri.EscapeDataString($"{readera["pk_function_code"]}");
            }
        }
        catch (Exception ex)
        {
            var trackingId = PerformanceLogger.LogPerfTimerBegin(userId, tenantId, $"AccStatment_SetIdColumMissingSA", "AccStatementGrid", ex.Message + "InputJson_" + JObject.FromObject(gridInput).ToString() + "CurrentDataRow_" + readera.ToString());
            return Uri.EscapeDataString($"{readera["org_id_" + (gridInput.OrgLevel + 1)]}");
        }
    }



    private string SetParentIdDataRowMissingSASetup(AccStatmentWidgetInput gridInput, DataRow readera, bool proj_setup_missing)
    {
        string projSetupMissingId = "00-serviceAreaMiss-" + gridInput.OrgId;
        switch (gridInput.RequestedTreeLevel)
        {
            case 1: return null;
            case 2: return (Uri.EscapeDataString(projSetupMissingId));
            case 3: return (Uri.EscapeDataString($"{projSetupMissingId}_{gridInput.level2IdExpanded}"));
            case 4: return (Uri.EscapeDataString($"{projSetupMissingId}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}"));
            case 5: return (Uri.EscapeDataString($"{projSetupMissingId}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}"));
            case 6: return (Uri.EscapeDataString($"{projSetupMissingId}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{gridInput.level5IdExpanded}"));
            case 7: return (Uri.EscapeDataString($"{projSetupMissingId}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{gridInput.level5IdExpanded}_{gridInput.level6IdExpanded}"));
            default: return null;
        }
    }



    private string SetNamePropertyMissingSA(AccStatmentWidgetInput gridInput, DataRow readera, bool proj_setup_missing, Dictionary<string, clsLanguageString> langStringValues)
    {
        string functionSetupMissing = langStringValues["AccStmt_SASetupMissing"].LangText;
        switch (gridInput.RequestedTreeLevel)
        {
            case 1: return (($"{functionSetupMissing}"));
            case 2: return $"{readera["pk_function_code"]} {readera["function_name"]}";
            case 3: return $"{readera["level_1_id"]} {readera["level_1_description"]}";
            case 4: return $"{readera["level_2_id"]} {readera["level_2_description"]}";
            case 5: return $"{readera["level_3_id"]} {readera["level_3_description"]}";
            case 6: return $"{readera["level_4_id"]} {readera["level_4_description"]}";
            case 7: return $"{readera["level_5_id"]} {readera["level_5_description"]}";
            default: return $"{readera["pk_function_code"]} {readera["function_name"]}";
        }
    }



    private List<KeyValueIntAccStmt> GetAllValidLevelColumnsFuncView_MissingSA(AccStatmentWidgetInput gridInput)
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = new List<KeyValueIntAccStmt>();
        //level 1
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 1,
            Value = $"sv.pk_function_code, sv.function_name",
            PartitionColumns = "pk_function_code"
        });
        //level 2
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 2,
            Value = $"sv.pk_function_code, sv.function_name,level_1_id,level_1_description",
            PartitionColumns = "pk_function_code,level_1_id"
        });
        //level 3
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 3,
            Value = $"sv.pk_function_code, sv.function_name,level_1_id,level_1_description,level_2_id,level_2_description",
            PartitionColumns = "pk_function_code,level_1_id,level_2_id"
        });
        //level 4
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 4,
            Value = $"sv.pk_function_code, sv.function_name,level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description",
            PartitionColumns = "pk_function_code,level_1_id,level_2_id,level_3_id"
        });
        //level 5
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 5,
            Value = $"sv.pk_function_code, sv.function_name,level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description,level_4_id,level_4_description",
            PartitionColumns = "pk_function_code,level_1_id,level_2_id,level_3_id,level_4_id"
        });
        //level 6
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 6,
            Value = $"sv.pk_function_code, sv.function_name,level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description,level_4_id,level_4_description,level_5_id,level_5_description",
            PartitionColumns = "pk_function_code,level_1_id,level_2_id,level_3_id,level_4_id,level_5_id"
        });

        return validSelectColumForLevels;
    }



    private string SetIdColumSAView(AccStatmentWidgetInput gridInput, DataRow readera)
    {
        switch (gridInput.RequestedTreeLevel)
        {
            case 1: return Uri.EscapeDataString($"{readera["service_id_2"]}");
            case 2: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{readera["service_id_3"]}");
            case 3: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{readera["level_1_id"]}");
            case 4: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{readera["level_2_id"]}");
            case 5: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{readera["level_3_id"]}");
            case 6: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{gridInput.level5IdExpanded}_{readera["level_4_id"]}");
            case 7: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{gridInput.level5IdExpanded}_{gridInput.level6IdExpanded}_{readera["level_5_id"]}");
            default: return Uri.EscapeDataString($"{readera["pk_function_code"]}");
        }
    }



    private string SetParentIdDataRowSA(AccStatmentWidgetInput gridInput, DataRow readera)
    {
        switch (gridInput.RequestedTreeLevel)
        {
            case 1: return null;
            case 2: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}");
            case 3: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}");
            case 4: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}");
            case 5: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}");
            case 6: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{gridInput.level5IdExpanded}");
            case 7: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{gridInput.level5IdExpanded}_{gridInput.level6IdExpanded}");
            default: return null;
        }
    }



    private string SetNamePropertySAView(AccStatmentWidgetInput gridInput, DataRow readera)
    {
        switch (gridInput.RequestedTreeLevel)
        {
            case 1: return $"{readera["service_id_2"]} {readera["service_name_2"]}";
            case 2: return $"{readera["service_id_3"]} {readera["service_name_3"]}";
            case 3: return $"{readera["level_1_id"]} {readera["level_1_description"]}";
            case 4: return $"{readera["level_2_id"]} {readera["level_2_description"]}";
            case 5: return $"{readera["level_3_id"]} {readera["level_3_description"]}";
            case 6: return $"{readera["level_4_id"]} {readera["level_4_description"]}";
            case 7: return $"{readera["level_5_id"]} {readera["level_5_description"]}";
            default: return $"{readera["pk_function_code"]} {readera["function_name"]}";
        }
    }



    private DataTable SetEmptyLevelsForSA(DataTable dt, int RequestedTreeLevel)
    {
        foreach (DataRow dr in dt.Rows)
        {
            if (RequestedTreeLevel == 3)
            {
                if (dr["level_1_id"] == null || Convert.ToString(dr["level_1_id"]) == string.Empty)
                {
                    dr["level_1_id"] = "ZZ";
                    dr["level_1_description"] = "Mangler oppsett";
                }
            }

            if (RequestedTreeLevel == 4)
            {
                if (dr["level_1_id"] == null || Convert.ToString(dr["level_1_id"]) == string.Empty)
                {
                    dr["level_1_id"] = "ZZ";
                    dr["level_1_description"] = "Mangler oppsett";
                }

                if (dr["level_2_id"] == null || Convert.ToString(dr["level_2_id"]) == string.Empty)
                {
                    dr["level_2_id"] = "ZZ";
                    dr["level_2_description"] = "Mangler oppsett";
                }
            }

            if (RequestedTreeLevel == 5)
            {
                if (dr["level_1_id"] == null || Convert.ToString(dr["level_1_id"]) == string.Empty)
                {
                    dr["level_1_id"] = "ZZ";
                    dr["level_1_description"] = "Mangler oppsett";
                }

                if (dr["level_2_id"] == null || Convert.ToString(dr["level_2_id"]) == string.Empty)
                {
                    dr["level_2_id"] = "ZZ";
                    dr["level_2_description"] = "Mangler oppsett";
                }

                if (dr["level_3_id"] == null || Convert.ToString(dr["level_3_id"]) == string.Empty)
                {
                    dr["level_3_id"] = "ZZ";
                    dr["level_3_description"] = "Mangler oppsett";
                }
            }

            if (RequestedTreeLevel == 6)
            {
                if (dr["level_1_id"] == null || Convert.ToString(dr["level_1_id"]) == string.Empty)
                {
                    dr["level_1_id"] = "ZZ";
                    dr["level_1_description"] = "Mangler oppsett";
                }

                if (dr["level_2_id"] == null || Convert.ToString(dr["level_2_id"]) == string.Empty)
                {
                    dr["level_2_id"] = "ZZ";
                    dr["level_2_description"] = "Mangler oppsett";
                }

                if (dr["level_3_id"] == null || Convert.ToString(dr["level_3_id"]) == string.Empty)
                {
                    dr["level_3_id"] = "ZZ";
                    dr["level_3_description"] = "Mangler oppsett";
                }

                if (dr["level_4_id"] == null || Convert.ToString(dr["level_4_id"]) == string.Empty)
                {
                    dr["level_4_id"] = "ZZ";
                    dr["level_4_description"] = "Mangler oppsett";
                }
            }

            if (RequestedTreeLevel == 7)
            {
                if (dr["level_1_id"] == null || Convert.ToString(dr["level_1_id"]) == string.Empty)
                {
                    dr["level_1_id"] = "ZZ";
                    dr["level_1_description"] = "Mangler oppsett";
                }

                if (dr["level_2_id"] == null || Convert.ToString(dr["level_2_id"]) == string.Empty)
                {
                    dr["level_2_id"] = "ZZ";
                    dr["level_2_description"] = "Mangler oppsett";
                }

                if (dr["level_3_id"] == null || Convert.ToString(dr["level_3_id"]) == string.Empty)
                {
                    dr["level_3_id"] = "ZZ";
                    dr["level_3_description"] = "Mangler oppsett";
                }

                if (dr["level_4_id"] == null || Convert.ToString(dr["level_4_id"]) == string.Empty)
                {
                    dr["level_4_id"] = "ZZ";
                    dr["level_4_description"] = "Mangler oppsett";
                }

                if (dr["level_5_id"] == null || Convert.ToString(dr["level_5_id"]) == string.Empty)
                {
                    dr["level_5_id"] = "ZZ";
                    dr["level_5_description"] = "Mangler oppsett";
                }
            }
        }

        return dt;
    }



    //#endregion Løpende økonomistatus Tab -> grid Regnskap og budsjett

    //#region Get dashboard widget filters

    public async Task<List<tco_service_values>> GetServiceFilter(int tenantId)
    {
        var dbcontext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
        return await dbcontext.tco_service_values.Where(x => x.fk_tenant_id == tenantId).AsNoTracking().ToListAsync();
    }



    public async Task<List<AttributeMapping>> GetAtributeValues(int tenantId, int budgetYear, int orgLevel, string orgId, string orgVersion)
    {
        var dbcontext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
        var data = (from rv in dbcontext.tco_relation_values.Where(x => x.year_from <= budgetYear && x.year_to >= budgetYear)
            join av in dbcontext.tco_attribute_values.Where(x => x.year_from <= budgetYear && x.year_to >= budgetYear)
                on new { a = rv.fk_tenant_id, b = rv.attribute_value, c = rv.attribute_type }
                equals new { a = av.fk_tenant_id, b = av.pk_attribute_id, c = av.attribute_type }
            join d in dbcontext.tco_departments.Where(x => x.year_from <= budgetYear && x.year_to >= budgetYear)
                on new { a = rv.fk_tenant_id }
                equals new { a = d.fk_tenant_id }
            join oh in dbcontext.tco_org_hierarchy on new { a = d.fk_tenant_id, b = d.pk_department_code } equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
            where rv.relation_value_from.CompareTo(d.pk_department_code) <= 0 && rv.relation_value_to.CompareTo(d.pk_department_code) >= 0 &&
                  rv.fk_tenant_id == tenantId && av.attribute_type.ToUpper() == "CHAPTER" && rv.relation_type.ToUpper() == "DEPARTMENTS" && oh.fk_org_version == orgVersion
            select new AttributeMapping
            {
                attributeValue = rv.attribute_value,
                attributeName = av.attribute_name,
                orgId1 = oh.org_id_1,
                orgId2 = oh.org_id_2,
                orgId3 = oh.org_id_3,
                orgId4 = oh.org_id_4,
                orgId5 = oh.org_id_5,
                orgId6 = oh.org_id_6,
                orgId7 = oh.org_id_7,
                orgId8 = oh.org_id_8
            });
        if (orgLevel == 1)
        {
            data = data.Where(x => x.orgId1 == orgId);
        }
        else if (orgLevel == 2)
        {
            data = data.Where(x => x.orgId2 == orgId);
        }
        else if (orgLevel == 3)
        {
            data = data.Where(x => x.orgId3 == orgId);
        }
        else if (orgLevel == 4)
        {
            data = data.Where(x => x.orgId4 == orgId);
        }
        else if (orgLevel == 5)
        {
            data = data.Where(x => x.orgId5 == orgId);
        }
        else if (orgLevel == 6)
        {
            data = data.Where(x => x.orgId6 == orgId);
        }
        else if (orgLevel == 7)
        {
            data = data.Where(x => x.orgId7 == orgId);
        }
        else if (orgLevel == 8)
        {
            data = data.Where(x => x.orgId8 == orgId);
        }

        var filteredData = await data.ToListAsync();

        return filteredData;
    }



    public async Task<List<AttributeMapping>> GetNonChapterAtributeValues(int tenantId, int budgetYear, int orgLevel, string orgId, string orgVersion, string attributeType)
    {
        var dbcontext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
        var data = (from rv in dbcontext.tco_relation_values.Where(x => x.year_from <= budgetYear && x.year_to >= budgetYear)
            join ta in dbcontext.tco_attributes.Where(x => x.status == 1) on new { a = rv.fk_tenant_id, b = rv.attribute_type } equals new { a = ta.fk_tenant_id, b = ta.attribute_type }
            join av in dbcontext.tco_attribute_values.Where(x => x.year_from <= budgetYear && x.year_to >= budgetYear)
                on new { a = rv.fk_tenant_id, b = rv.attribute_value, c = ta.attribute_type }
                equals new { a = av.fk_tenant_id, b = av.pk_attribute_id, c = av.attribute_type }
            join d in dbcontext.tco_departments.Where(x => x.year_from <= budgetYear && x.year_to >= budgetYear)
                on new { a = rv.fk_tenant_id }
                equals new { a = d.fk_tenant_id }
            join oh in dbcontext.tco_org_hierarchy on new { a = d.fk_tenant_id, b = d.pk_department_code } equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
            where rv.relation_value_from.CompareTo(d.pk_department_code) <= 0 && rv.relation_value_to.CompareTo(d.pk_department_code) >= 0 &&
                  rv.fk_tenant_id == tenantId && rv.relation_type.ToUpper() == "DEPARTMENTS" && oh.fk_org_version == orgVersion && ((!String.IsNullOrEmpty(attributeType) && ta.attribute_type.Equals(attributeType)) || String.IsNullOrEmpty(attributeType))
            select new AttributeMapping
            {
                objectId = ta.attribute_type,
                objectName = ta.attribute_description,
                attributeValue = rv.attribute_value,
                attributeName = av.attribute_name,
                orgId1 = oh.org_id_1,
                orgId2 = oh.org_id_2,
                orgId3 = oh.org_id_3,
                orgId4 = oh.org_id_4,
                orgId5 = oh.org_id_5,
                orgId6 = oh.org_id_6,
                orgId7 = oh.org_id_7,
                orgId8 = oh.org_id_8
            });
        if (orgLevel == 1)
        {
            data = data.Where(x => x.orgId1 == orgId);
        }
        else if (orgLevel == 2)
        {
            data = data.Where(x => x.orgId2 == orgId);
        }
        else if (orgLevel == 3)
        {
            data = data.Where(x => x.orgId3 == orgId);
        }
        else if (orgLevel == 4)
        {
            data = data.Where(x => x.orgId4 == orgId);
        }
        else if (orgLevel == 5)
        {
            data = data.Where(x => x.orgId5 == orgId);
        }
        else if (orgLevel == 6)
        {
            data = data.Where(x => x.orgId6 == orgId);
        }
        else if (orgLevel == 7)
        {
            data = data.Where(x => x.orgId7 == orgId);
        }
        else if (orgLevel == 8)
        {
            data = data.Where(x => x.orgId8 == orgId);
        }

        var filteredData = await data.ToListAsync();

        return filteredData;
    }



    public async Task<List<tco_accounts>> GetAccountFilter(int tenantId, int budgetYear)
    {
        var dbcontext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
        return await (from a in dbcontext.tco_accounts
            join b in dbcontext.gco_kostra_accounts on a.fk_kostra_account_code equals b.pk_kostra_account_code
            where a.pk_tenant_id == tenantId && a.dateFrom.Year <= budgetYear
                                             && a.dateTo.Year >= budgetYear && a.isActive && b.type == operations
            select a).ToListAsync();
    }



    public async Task<List<KeyValueStringData>> GetAccountFilter(int tenantId, int budgetYear, List<string> departments)
    {
        var dbcontext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();

        if (departments.Any())
        {
            return await (from af in dbcontext.tco_filter_accounts
                join ta in dbcontext.tco_accounts.Where(x => x.pk_tenant_id == tenantId && x.dateFrom.Year <= budgetYear && x.dateTo.Year >= budgetYear)
                    on af.fk_account_code equals ta.pk_account_code
                where af.fk_tenant_id == tenantId && af.budget_year == budgetYear && departments.Contains(af.fk_department_code)
                select new KeyValueStringData
                {
                    Key = ta.pk_account_code,
                    Value = ta.description
                }).AsNoTracking().ToListAsync();
        }
        else
        {
            return await (from af in dbcontext.tco_filter_accounts
                join ta in dbcontext.tco_accounts.Where(x => x.pk_tenant_id == tenantId && x.dateFrom.Year <= budgetYear && x.dateTo.Year >= budgetYear)
                    on af.fk_account_code equals ta.pk_account_code
                where af.fk_tenant_id == tenantId && af.budget_year == budgetYear
                select new KeyValueStringData
                {
                    Key = ta.pk_account_code,
                    Value = ta.description
                }).AsNoTracking().ToListAsync();
        }
    }



    public async Task<List<tco_functions>> GetFunctionFilter(int tenantId, int budgetYear)
    {
        var dbcontext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
        return await dbcontext.tco_functions.Where(x => x.pk_tenant_id == tenantId && x.dateFrom.Year <= budgetYear && x.dateTo.Year >= budgetYear && x.isActive).AsNoTracking().ToListAsync();
    }



    public async Task<List<KeyValueStringData>> GetFunctionFilter(int tenantId, int budgetYear, List<string> departments)
    {
        var dbcontext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();

        if (departments.Any())
        {
            var baseQuery =  from af in dbcontext.tco_filter_functions
                join ta in dbcontext.tco_functions.Where(x => x.pk_tenant_id == tenantId && x.dateFrom.Year <= budgetYear && x.dateTo.Year >= budgetYear)
                    on af.fk_function_code equals ta.pk_Function_code
                where af.fk_tenant_id == tenantId && af.budget_year == budgetYear 
                select new 
                {
                    af.fk_department_code,
                    ta.pk_Function_code,
                    ta.description
                };

            if(departments.Count == 1)
            {
                var dept = departments[0];
                baseQuery = baseQuery.Where(x => x.fk_department_code == dept);
            }
            else
            {
                baseQuery = baseQuery.Where(x => departments.Contains(x.fk_department_code));
            }

            var result = await baseQuery
                .Select(x => new KeyValueStringData
                {
                    Key = x.pk_Function_code,
                    Value = x.description
                }).AsNoTracking().ToListAsync();

            return result;
        }
        else
        {
            return await (from af in dbcontext.tco_filter_functions
                join ta in dbcontext.tco_functions.Where(x => x.pk_tenant_id == tenantId && x.dateFrom.Year <= budgetYear && x.dateTo.Year >= budgetYear)
                    on af.fk_function_code equals ta.pk_Function_code
                where af.fk_tenant_id == tenantId && af.budget_year == budgetYear
                select new KeyValueStringData
                {
                    Key = ta.pk_Function_code,
                    Value = ta.description
                }).AsNoTracking().ToListAsync();
        }
    }



    public async Task<List<tco_projects>> GetProjectFilter(int tenantId, int budgetYear)
    {
        var dbcontext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
        return await dbcontext.tco_projects.Where(x => x.fk_tenant_id == tenantId && x.date_from.Year <= budgetYear && x.date_to.Year >= budgetYear && x.active == 1).AsNoTracking().ToListAsync();
    }



    public async Task<List<KeyValueStringData>> GetProjectFilter(int tenantId, int budgetYear, List<string> departments)
    {
        var dbcontext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();

        if (departments.Any())
        {
            return await (from af in dbcontext.tco_filter_projects
                join ta in dbcontext.tco_projects.Where(x => x.fk_tenant_id == tenantId && x.date_from.Year <= budgetYear && x.date_to.Year >= budgetYear)
                    on af.fk_project_code equals ta.pk_project_code
                where af.fk_tenant_id == tenantId && af.budget_year == budgetYear && departments.Contains(af.fk_department_code)
                select new KeyValueStringData
                {
                    Key = ta.pk_project_code,
                    Value = ta.project_name
                }).AsNoTracking().ToListAsync();
        }
        else
        {
            return await (from af in dbcontext.tco_filter_projects
                join ta in dbcontext.tco_projects.Where(x => x.fk_tenant_id == tenantId && x.date_from.Year <= budgetYear && x.date_to.Year >= budgetYear)
                    on af.fk_project_code equals ta.pk_project_code
                where af.fk_tenant_id == tenantId && af.budget_year == budgetYear
                select new KeyValueStringData
                {
                    Key = ta.pk_project_code,
                    Value = ta.project_name
                }).AsNoTracking().ToListAsync();
        }
    }



    public async Task<List<tco_free_dim_values>> GetFreeDimFilter(int tenantId, string freeDimColumn)
    {
        var dbcontext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
        return await dbcontext.tco_free_dim_values.Where(x => x.fk_tenant_id == tenantId && x.free_dim_column == freeDimColumn && x.status == 1).AsNoTracking().ToListAsync();
    }



    public async Task<List<KeyValueStringData>> GetFreeDim1Filter(int tenantId, int budgetYear, List<string> departments)
    {
        var dbcontext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();

        if (departments.Any())
        {
            return await (from af in dbcontext.tco_filter_free_dim_1
                join ta in dbcontext.tco_free_dim_values.Where(x => x.fk_tenant_id == tenantId && x.free_dim_column == "free_dim_1")
                    on af.free_dim_1 equals ta.free_dim_code
                where af.fk_tenant_id == tenantId && af.budget_year == budgetYear && departments.Contains(af.fk_department_code)
                select new KeyValueStringData
                {
                    Key = ta.free_dim_code,
                    Value = ta.description
                }).AsNoTracking().ToListAsync();
        }
        else
        {
            return await (from af in dbcontext.tco_filter_free_dim_1
                join ta in dbcontext.tco_free_dim_values.Where(x => x.fk_tenant_id == tenantId && x.free_dim_column == "free_dim_1")
                    on af.free_dim_1 equals ta.free_dim_code
                where af.fk_tenant_id == tenantId && af.budget_year == budgetYear
                select new KeyValueStringData
                {
                    Key = ta.free_dim_code,
                    Value = ta.description
                }).AsNoTracking().ToListAsync();
        }
    }



    public async Task<List<KeyValueStringData>> GetFreeDim2Filter(int tenantId, int budgetYear, List<string> departments)
    {
        var dbcontext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();

        if (departments.Any())
        {
            var baseQuery =   from af in dbcontext.tco_filter_free_dim_2
                join ta in dbcontext.tco_free_dim_values.Where(x => x.fk_tenant_id == tenantId && x.free_dim_column == "free_dim_2")
                    on af.free_dim_2 equals ta.free_dim_code
                where af.fk_tenant_id == tenantId && af.budget_year == budgetYear 
                select new 
                {
                    af.fk_department_code,
                    ta.free_dim_code,
                    ta.description
                };

            if(departments.Count == 1)
            {
                var deptCode = departments[0];
                baseQuery = baseQuery.Where(x => x.fk_department_code == deptCode);
            }
            else
            {
                baseQuery = baseQuery.Where(x => departments.Contains(x.fk_department_code));
            }

            var result = await baseQuery
                .Select(x => new KeyValueStringData
                {
                    Key = x.free_dim_code,
                    Value = x.description
                }).AsNoTracking().ToListAsync();

            return result;
        }
        else
        {
            return await (from af in dbcontext.tco_filter_free_dim_2
                join ta in dbcontext.tco_free_dim_values.Where(x => x.fk_tenant_id == tenantId && x.free_dim_column == "free_dim_2")
                    on af.free_dim_2 equals ta.free_dim_code
                where af.fk_tenant_id == tenantId && af.budget_year == budgetYear
                select new KeyValueStringData
                {
                    Key = ta.free_dim_code,
                    Value = ta.description
                }).AsNoTracking().ToListAsync();
        }
    }



    public async Task<List<KeyValueStringData>> GetFreeDim3Filter(int tenantId, int budgetYear, List<string> departments)
    {
        var dbcontext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();

        if (departments.Any())
        {
            return await (from af in dbcontext.tco_filter_free_dim_3
                join ta in dbcontext.tco_free_dim_values.Where(x => x.fk_tenant_id == tenantId && x.free_dim_column == "free_dim_3")
                    on af.free_dim_3 equals ta.free_dim_code
                where af.fk_tenant_id == tenantId && af.budget_year == budgetYear && departments.Contains(af.fk_department_code)
                select new KeyValueStringData
                {
                    Key = ta.free_dim_code,
                    Value = ta.description
                }).AsNoTracking().ToListAsync();
        }
        else
        {
            return await (from af in dbcontext.tco_filter_free_dim_3
                join ta in dbcontext.tco_free_dim_values.Where(x => x.fk_tenant_id == tenantId && x.free_dim_column == "free_dim_3")
                    on af.free_dim_3 equals ta.free_dim_code
                where af.fk_tenant_id == tenantId && af.budget_year == budgetYear
                select new KeyValueStringData
                {
                    Key = ta.free_dim_code,
                    Value = ta.description
                }).AsNoTracking().ToListAsync();
        }
    }



    public async Task<List<KeyValueStringData>> GetFreeDim4Filter(int tenantId, int budgetYear, List<string> departments)
    {
        var dbcontext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();

        if (departments.Any())
        {
            return await (from af in dbcontext.tco_filter_free_dim_4
                join ta in dbcontext.tco_free_dim_values.Where(x => x.fk_tenant_id == tenantId && x.free_dim_column == "free_dim_4")
                    on af.free_dim_4 equals ta.free_dim_code
                where af.fk_tenant_id == tenantId && af.budget_year == budgetYear && departments.Contains(af.fk_department_code)
                select new KeyValueStringData
                {
                    Key = ta.free_dim_code,
                    Value = ta.description
                }).AsNoTracking().ToListAsync();
        }
        else
        {
            return await (from af in dbcontext.tco_filter_free_dim_4
                join ta in dbcontext.tco_free_dim_values.Where(x => x.fk_tenant_id == tenantId && x.free_dim_column == "free_dim_4")
                    on af.free_dim_4 equals ta.free_dim_code
                where af.fk_tenant_id == tenantId && af.budget_year == budgetYear
                select new KeyValueStringData
                {
                    Key = ta.free_dim_code,
                    Value = ta.description
                }).AsNoTracking().ToListAsync();
        }
    }



    public async Task<List<KeyValueStringData>> GetAccountLevel1FilterData(int tenantId, int budgetYear, int level)
    {
        var dbcontext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();

        return await (from b in dbcontext.tco_filter_account_levels
            join a in dbcontext.tmd_reporting_line
                on new { x = b.fk_tenant_id, y = b.account_level_id } equals new { x = a.fk_tenant_id, y = a.level_1_id }
            where a.fk_tenant_id == tenantId && b.budget_year == budgetYear && a.report == "MNDRAPP" && b.level == level
            group b by new { b.account_level_id, a.level_1_description } into grp
            select new KeyValueStringData
            {
                Key = grp.Key.account_level_id,
                Value = grp.Key.level_1_description
            }).AsNoTracking().ToListAsync();
    }


    public async Task<List<KeyValueStringData>> GetAccountLevel2FilterData(int tenantId, int budgetYear, int level)
    {
        var dbcontext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();

        return await (from b in dbcontext.tco_filter_account_levels
            join a in dbcontext.tmd_reporting_line
                on new { x = b.fk_tenant_id, y = b.account_level_id } equals new { x = a.fk_tenant_id, y = a.level_2_id }
            where a.fk_tenant_id == tenantId && b.budget_year == budgetYear && a.report == "MNDRAPP" && b.level == level
            group b by new { b.account_level_id, a.level_2_description } into grp
            select new KeyValueStringData
            {
                Key = grp.Key.account_level_id,
                Value = grp.Key.level_2_description
            }).AsNoTracking().ToListAsync();
    }


    public async Task<List<KeyValueStringData>> GetAccountLevel3FilterData(int tenantId, int budgetYear, int level)
    {
        var dbcontext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();

        return await (from b in dbcontext.tco_filter_account_levels
            join a in dbcontext.tmd_reporting_line
                on new { x = b.fk_tenant_id, y = b.account_level_id } equals new { x = a.fk_tenant_id, y = a.level_3_id }
            where a.fk_tenant_id == tenantId && b.budget_year == budgetYear && a.report == "MNDRAPP" && b.level == level 
            group b by new { b.account_level_id, a.level_3_description } into grp
            select new KeyValueStringData
            {
                Key = grp.Key.account_level_id,
                Value = grp.Key.level_3_description
            }).AsNoTracking().ToListAsync();
    }


    public async Task<List<KeyValueStringData>> GetAccountLevel4FilterData(int tenantId, int budgetYear, int level)
    {
        var dbcontext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();

        return await (from b in dbcontext.tco_filter_account_levels
            join a in dbcontext.tmd_reporting_line
                on new { x = b.fk_tenant_id, y = b.account_level_id } equals new { x = a.fk_tenant_id, y = a.level_4_id }
            where a.fk_tenant_id == tenantId && b.budget_year == budgetYear && a.report == "MNDRAPP" && b.level == level 
            group b by new { b.account_level_id, a.level_4_description } into grp
            select new KeyValueStringData
            {
                Key = grp.Key.account_level_id,
                Value = grp.Key.level_4_description
            }).AsNoTracking().ToListAsync();
    }


    public async Task<List<KeyValueStringData>> GetAccountLevel5FilterData(int tenantId, int budgetYear, int level)
    {
        var dbcontext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();

        return await (from b in dbcontext.tco_filter_account_levels
            join a in dbcontext.tmd_reporting_line
                on new { x = b.fk_tenant_id, y = b.account_level_id } equals new { x = a.fk_tenant_id, y = a.level_5_id }
            where a.fk_tenant_id == tenantId && b.budget_year == budgetYear && a.report == "MNDRAPP" && b.level == level 
            group b by new { b.account_level_id, a.level_5_description } into grp
            select new KeyValueStringData
            {
                Key = grp.Key.account_level_id,
                Value = grp.Key.level_5_description
            }).AsNoTracking().ToListAsync();
    }



    public async Task<List<vw_tco_parameters>> GetFinplanLevelConfig(int tenantId, List<string> parameters)
    {
        var dbcontext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
        return await dbcontext.vw_tco_parameters.Where(x => x.fk_tenant_id == tenantId && parameters.Contains(x.param_name)).AsNoTracking().ToListAsync();
    }



    public async Task<List<OrgWithDepartments>> GetOrgAndDepartments(string orgVersion, int tenantId, int budgetYear)
    {
        var dbcontext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
        return await (from oh in dbcontext.tco_org_hierarchy
            join dp in dbcontext.tco_departments on new { a = oh.fk_department_code, b = oh.fk_tenant_id }
                equals new { a = dp.pk_department_code, b = dp.fk_tenant_id }
            where oh.fk_org_version == orgVersion && oh.fk_tenant_id == tenantId && dp.status == 1
                  && (dp.year_from <= budgetYear && dp.year_to >= budgetYear)
            select new OrgWithDepartments
            {
                org_id_1 = oh.org_id_1,
                org_id_2 = oh.org_id_2,
                org_id_3 = oh.org_id_3,
                org_id_4 = oh.org_id_4,
                org_id_5 = oh.org_id_5,
                org_id_6 = oh.org_id_6,
                org_id_7 = oh.org_id_7,
                org_id_8 = oh.org_id_8,
                org_name_1 = oh.org_name_1,
                org_name_2 = oh.org_name_2,
                org_name_3 = oh.org_name_3,
                org_name_4 = oh.org_name_4,
                org_name_5 = oh.org_name_5,
                org_name_6 = oh.org_name_6,
                org_name_7 = oh.org_name_7,
                org_name_8 = oh.org_name_8,
                fk_department_code = dp.pk_department_code,
                department_name = dp.department_name
            }).AsNoTracking().ToListAsync();
    }



    public bool CheckChapterSettings(int tenantId)
    {
        var dbcontext = _dbContextManager.GetTenantDbContextForParallelRead();
        return dbcontext.tco_attributes.Any(a => a.fk_tenant_id == tenantId && a.attribute_type.ToUpper() == "CHAPTER" && a.status == 1);
    }



    private async Task<string> BuildFilterQuery(DashBoardFilterHelper input)
    {
        StringBuilder filterQuery = new StringBuilder();
        if (input.filters != null)
        {
            List<string> filterConditions = new List<string>();
            foreach (var filter in input.filters)
            {
                if (!filter.FilterValues.Any())
                {
                    continue;
                }

                switch (filter.FilterId)
                {
                    case FilterId.OrgFilter:
                        BuildOrgFilterQuery(input.currentOrgLevel, filter.FilterValues.ToList(), filterConditions);
                        break;

                    case FilterId.ChapterFilter:
                        var mappedDept = _finUtility.GetAttributeConnectedDepartments(input.userId, input.budgetYear)
                            .Where(x => filter.FilterValues.ToList().Contains(x.attributeId))
                            .Select(y => y.departmentCode).ToList();
                        BuildFilterQuery("department_code", mappedDept, filterConditions);
                        break;

                    case FilterId.ObjectFilter:
                        mappedDept = _finUtility.GetObjectAttributeConnectedDepartments(input.userId, input.budgetYear, filter.objectId)
                            .Where(x => filter.FilterValues.ToList().Contains(x.attributeId))
                            .Select(y => y.departmentCode).ToList();
                        BuildFilterQuery("department_code", mappedDept, filterConditions);
                        break;

                    case FilterId.ServiceFilter:
                        var serviceInput = new DashBoardServiceFilterHelper
                        {
                            tenantId = input.tenantId,
                            nextLevelIds = filter.FilterValues.ToList(),
                            viewType = input.viewType
                        };
                        var serviceFilterCondition = await BuildServiceFilterQuery(serviceInput);
                        if (!string.IsNullOrEmpty(serviceFilterCondition))
                        {
                            filterConditions.Add(serviceFilterCondition);
                        }
                        break;

                    case FilterId.DepartmentFilter:
                        BuildFilterQuery("department_code", filter.FilterValues.ToList(), filterConditions);
                        break;

                    case FilterId.FunctionFilter:
                        BuildFilterQuery("a.fk_function_code", filter.FilterValues.ToList(), filterConditions);
                        break;

                    case FilterId.ProjectFilter:
                        BuildFilterQuery("fk_project_code", filter.FilterValues.ToList(), filterConditions);
                        break;

                    case FilterId.FreeDim1Filter:
                        BuildFilterQuery("free_dim_1", filter.FilterValues.ToList(), filterConditions);
                        break;

                    case FilterId.FreeDim2Filter:
                        BuildFilterQuery("free_dim_2", filter.FilterValues.ToList(), filterConditions);
                        break;

                    case FilterId.FreeDim3Filter:
                        BuildFilterQuery("free_dim_3", filter.FilterValues.ToList(), filterConditions);
                        break;

                    case FilterId.FreeDim4Filter:
                        BuildFilterQuery("free_dim_4", filter.FilterValues.ToList(), filterConditions);
                        break;

                    case FilterId.AccountFilter:
                        BuildFilterQuery("a.fk_account_code", filter.FilterValues.ToList(), filterConditions);
                        break;
                       
                    case FilterId.AccountLevel1Filter:
                        if(!input.isBudFormView)
                        {
                            BuildFilterQuery("rl.level_1_id", filter.FilterValues.ToList(), filterConditions);
                        }
                        break;
                        
                    case FilterId.AccountLevel2Filter:
                        if (!input.isBudFormView)
                        {
                            BuildFilterQuery("rl.level_2_id", filter.FilterValues.ToList(), filterConditions);
                        }
                        break;
                        
                    case FilterId.AccountLevel3Filter:
                        if (!input.isBudFormView)
                        {
                            BuildFilterQuery("rl.level_3_id", filter.FilterValues.ToList(), filterConditions);
                        }
                        break;
                        
                    case FilterId.AccountLevel4Filter:
                        if (!input.isBudFormView)
                        {
                            BuildFilterQuery("rl.level_4_id", filter.FilterValues.ToList(), filterConditions);
                        }
                        break;
                        
                    case FilterId.AccountLevel5Filter:
                        if (!input.isBudFormView)
                        {
                            BuildFilterQuery("rl.level_5_id", filter.FilterValues.ToList(), filterConditions);
                        }
                        break;

                }
            }

            foreach (var condition in filterConditions)
            {
                filterQuery.Append(condition);
            }
        }
        return filterQuery.ToString();
    }



    private void BuildOrgFilterQuery(int currentOrgLevel, List<string> nextLevelIds, List<string> filterConditions)
    {
        if (!nextLevelIds.Any())
        {
            return;
        }

        int nextOrgLevel = currentOrgLevel + 1;

        filterConditions.Add($" and org_id_{nextOrgLevel} in ('{string.Join("' ,'", nextLevelIds)}')");
    }



    private async Task<string> BuildServiceFilterQuery(DashBoardServiceFilterHelper input)
    {
        string result = string.Empty;
        if(input.viewType == DashBoardGridViewTypes.PerSAView)
        {
            result = $" and tco.service_id_2 in ('{string.Join("','", input.nextLevelIds)}')";
            return result;
        }
        List<string> parameters = new List<string> { "MONTHREP_LEVEL_1", "MONTHREP_LEVEL_2" };

        var relevantParametersTask_1 =  GetFinplanLevelConfig(input.tenantId, parameters);

        List<string> parameters_2 = new List<string>(parameters) { "FINPLAN_LEVEL_1", "FINPLAN_LEVEL_2" };

        var isRegisteredTask =  IsRegisteredService(input.tenantId);
        var relevantParametersTask_2 =  GetFinplanLevelConfig(input.tenantId, parameters_2);

        await Task.WhenAll(relevantParametersTask_1,isRegisteredTask,relevantParametersTask_2);

        var relevantParameters_1 = await relevantParametersTask_1;
        bool isRegistered = await isRegisteredTask;
        var relevantParameters_2 = await relevantParametersTask_2;

        if (relevantParameters_1.Any(x => x.param_value.StartsWith("service_id")))
        {
            result = $" and {relevantParameters_1.FirstOrDefault(x => x.param_value.StartsWith("service_id")).param_value} in ('{string.Join("' ,'", input.nextLevelIds)}')";
        }
        else if(isRegistered)
        {                           
            if(!relevantParameters_2.Any(x=> x.param_value.StartsWith("service_id")))
            {
                result = $" and service_id_2 in ('{string.Join("','", input.nextLevelIds)}')";
            }
        }
        return result;
    }



    private void BuildFilterQuery(string columnName, List<string> filterIds, List<string> filterConditions)
    {
        if (!filterIds.Any())
        {
            return;
        }

        filterConditions.Add($" and {columnName} in ('{string.Join("' ,'", filterIds)}')");
    }



    private DataTable SetEmptyLevels(DataTable dt, int RequestedTreeLevel)
    {
        foreach (DataRow dr in dt.Rows)
        {
            if (RequestedTreeLevel == 2)
            {
                if (dr["level_1_id"] == null || Convert.ToString(dr["level_1_id"]) == string.Empty)
                {
                    dr["level_1_id"] = "ZZ";
                    dr["level_1_description"] = "Mangler oppsett";
                }
            }

            if (RequestedTreeLevel == 3)
            {
                if (dr["level_1_id"] == null || Convert.ToString(dr["level_1_id"]) == string.Empty)
                {
                    dr["level_1_id"] = "ZZ";
                    dr["level_1_description"] = "Mangler oppsett";
                }

                if (dr["level_2_id"] == null || Convert.ToString(dr["level_2_id"]) == string.Empty)
                {
                    dr["level_2_id"] = "ZZ";
                    dr["level_2_description"] = "Mangler oppsett";
                }
            }

            if (RequestedTreeLevel == 4)
            {
                if (dr["level_1_id"] == null || Convert.ToString(dr["level_1_id"]) == string.Empty)
                {
                    dr["level_1_id"] = "ZZ";
                    dr["level_1_description"] = "Mangler oppsett";
                }

                if (dr["level_2_id"] == null || Convert.ToString(dr["level_2_id"]) == string.Empty)
                {
                    dr["level_2_id"] = "ZZ";
                    dr["level_2_description"] = "Mangler oppsett";
                }

                if (dr["level_3_id"] == null || Convert.ToString(dr["level_3_id"]) == string.Empty)
                {
                    dr["level_3_id"] = "ZZ";
                    dr["level_3_description"] = "Mangler oppsett";
                }
            }

            if (RequestedTreeLevel == 5)
            {
                if (dr["level_1_id"] == null || Convert.ToString(dr["level_1_id"]) == string.Empty)
                {
                    dr["level_1_id"] = "ZZ";
                    dr["level_1_description"] = "Mangler oppsett";
                }

                if (dr["level_2_id"] == null || Convert.ToString(dr["level_2_id"]) == string.Empty)
                {
                    dr["level_2_id"] = "ZZ";
                    dr["level_2_description"] = "Mangler oppsett";
                }

                if (dr["level_3_id"] == null || Convert.ToString(dr["level_3_id"]) == string.Empty)
                {
                    dr["level_3_id"] = "ZZ";
                    dr["level_3_description"] = "Mangler oppsett";
                }

                if (dr["level_4_id"] == null || Convert.ToString(dr["level_4_id"]) == string.Empty)
                {
                    dr["level_4_id"] = "ZZ";
                    dr["level_4_description"] = "Mangler oppsett";
                }
            }

            if (RequestedTreeLevel == 6)
            {
                if (dr["level_1_id"] == null || Convert.ToString(dr["level_1_id"]) == string.Empty)
                {
                    dr["level_1_id"] = "ZZ";
                    dr["level_1_description"] = "Mangler oppsett";
                }

                if (dr["level_2_id"] == null || Convert.ToString(dr["level_2_id"]) == string.Empty)
                {
                    dr["level_2_id"] = "ZZ";
                    dr["level_2_description"] = "Mangler oppsett";
                }

                if (dr["level_3_id"] == null || Convert.ToString(dr["level_3_id"]) == string.Empty)
                {
                    dr["level_3_id"] = "ZZ";
                    dr["level_3_description"] = "Mangler oppsett";
                }

                if (dr["level_4_id"] == null || Convert.ToString(dr["level_4_id"]) == string.Empty)
                {
                    dr["level_4_id"] = "ZZ";
                    dr["level_4_description"] = "Mangler oppsett";
                }

                if (dr["level_5_id"] == null || Convert.ToString(dr["level_5_id"]) == string.Empty)
                {
                    dr["level_5_id"] = "ZZ";
                    dr["level_5_description"] = "Mangler oppsett";
                }
            }
        }

        return dt;
    }



    private DataTable SetEmptyLevelsForNextOrgView(DataTable dt, int RequestedTreeLevel)
    {
        foreach (DataRow dr in dt.Rows)
        {
            if (RequestedTreeLevel == 3)
            {
                if (dr["level_1_id"] == null || Convert.ToString(dr["level_1_id"]) == string.Empty)
                {
                    dr["level_1_id"] = "ZZ";
                    dr["level_1_description"] = "Mangler oppsett";
                }
            }

            if (RequestedTreeLevel == 4)
            {
                if (dr["level_1_id"] == null || Convert.ToString(dr["level_1_id"]) == string.Empty)
                {
                    dr["level_1_id"] = "ZZ";
                    dr["level_1_description"] = "Mangler oppsett";
                }

                if (dr["level_2_id"] == null || Convert.ToString(dr["level_2_id"]) == string.Empty)
                {
                    dr["level_2_id"] = "ZZ";
                    dr["level_2_description"] = "Mangler oppsett";
                }
            }

            if (RequestedTreeLevel == 5)
            {
                if (dr["level_1_id"] == null || Convert.ToString(dr["level_1_id"]) == string.Empty)
                {
                    dr["level_1_id"] = "ZZ";
                    dr["level_1_description"] = "Mangler oppsett";
                }

                if (dr["level_2_id"] == null || Convert.ToString(dr["level_2_id"]) == string.Empty)
                {
                    dr["level_2_id"] = "ZZ";
                    dr["level_2_description"] = "Mangler oppsett";
                }

                if (dr["level_3_id"] == null || Convert.ToString(dr["level_3_id"]) == string.Empty)
                {
                    dr["level_3_id"] = "ZZ";
                    dr["level_3_description"] = "Mangler oppsett";
                }
            }

            if (RequestedTreeLevel == 6)
            {
                if (dr["level_1_id"] == null || Convert.ToString(dr["level_1_id"]) == string.Empty)
                {
                    dr["level_1_id"] = "ZZ";
                    dr["level_1_description"] = "Mangler oppsett";
                }

                if (dr["level_2_id"] == null || Convert.ToString(dr["level_2_id"]) == string.Empty)
                {
                    dr["level_2_id"] = "ZZ";
                    dr["level_2_description"] = "Mangler oppsett";
                }

                if (dr["level_3_id"] == null || Convert.ToString(dr["level_3_id"]) == string.Empty)
                {
                    dr["level_3_id"] = "ZZ";
                    dr["level_3_description"] = "Mangler oppsett";
                }

                if (dr["level_4_id"] == null || Convert.ToString(dr["level_4_id"]) == string.Empty)
                {
                    dr["level_4_id"] = "ZZ";
                    dr["level_4_description"] = "Mangler oppsett";
                }
            }

            if (RequestedTreeLevel == 7)
            {
                if (dr["level_1_id"] == null || Convert.ToString(dr["level_1_id"]) == string.Empty)
                {
                    dr["level_1_id"] = "ZZ";
                    dr["level_1_description"] = "Mangler oppsett";
                }

                if (dr["level_2_id"] == null || Convert.ToString(dr["level_2_id"]) == string.Empty)
                {
                    dr["level_2_id"] = "ZZ";
                    dr["level_2_description"] = "Mangler oppsett";
                }

                if (dr["level_3_id"] == null || Convert.ToString(dr["level_3_id"]) == string.Empty)
                {
                    dr["level_3_id"] = "ZZ";
                    dr["level_3_description"] = "Mangler oppsett";
                }

                if (dr["level_4_id"] == null || Convert.ToString(dr["level_4_id"]) == string.Empty)
                {
                    dr["level_4_id"] = "ZZ";
                    dr["level_4_description"] = "Mangler oppsett";
                }

                if (dr["level_5_id"] == null || Convert.ToString(dr["level_5_id"]) == string.Empty)
                {
                    dr["level_5_id"] = "ZZ";
                    dr["level_5_description"] = "Mangler oppsett";
                }
            }
        }

        return dt;
    }



    private string SetIdColumProjView(AccStatmentWidgetInput gridInput, DataRow readera)
    {
        switch (gridInput.RequestedTreeLevel)
        {
            case 1: return Uri.EscapeDataString($"{readera["pk_project_code"]}");
            case 2: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{readera["level_1_id"]}");
            case 3: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{readera["level_2_id"]}");
            case 4: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{readera["level_3_id"]}");
            case 5: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{readera["level_4_id"]}");
            case 6: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{gridInput.level5IdExpanded}_{readera["level_5_id"]}");
            default: return Uri.EscapeDataString($"{readera["pk_project_code"]}");
        }
    }



    private string GetAccountingLevels(int accountLevel)
    {
        List<KeyValueIntAccStmt> validAccountLevels = new List<KeyValueIntAccStmt>
        {
            //level 1
            new KeyValueIntAccStmt()
            {
                Key = 1,
                Value = "level_1_id,level_1_description"
            },

            //level 2
            new KeyValueIntAccStmt()
            {
                Key = 2,
                Value = "level_1_id,level_1_description,level_2_id,level_2_description"
            },

            //level 3
            new KeyValueIntAccStmt()
            {
                Key = 3,
                Value = "level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description",
            },

            //level 4
            new KeyValueIntAccStmt()
            {
                Key = 4,
                Value = "level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description,level_4_id,level_4_description",
            }
        };
        var level = validAccountLevels.FirstOrDefault(z => z.Key == accountLevel);
        return level == null ? "level_1_id,level_1_description" : level.Value;
    }


    //#endregion

    //#region AccStmnt commonQuery start

    private string GetAccountingDataSetQuery(bool serviceSetUp, AccStatmentWidgetInput gridInput, int tenantId, string userId, bool isMissindDataQuery, bool isCurrentBudgetYear,
        bool addAccountCodeColumn, bool perPeriodData)
    {
        /* this function is created to have common accounting query for all the views, different conditions have been added to satisfy differnt view cases
         *  view 0 is org view
         *  view 1 is Account view
         *  view 2 is Org view with next level org( 2 level org then account level is displayed)
         *  view 3 is Budgetform view view
         *  view 4 is project view
         */


        bool useBudformSetup = false;

        if (gridInput.viewType == "3")
        {
            useBudformSetup = useTcoBudFormSetupTable(tenantId, "DASH_ACCSTATE_USE_BUDFORM_SETUP").GetAwaiter().GetResult();
        }
        string orgLevel_valid = ValidateOrgLevel(gridInput.OrgLevel);
        string columnTofetch = FetchColumForView(gridInput, isMissindDataQuery);
        string partictionColumn = FetchPartitionColumForView(gridInput, isMissindDataQuery);

        if (addAccountCodeColumn)
        {
            if (string.IsNullOrEmpty(columnTofetch))
            {
                columnTofetch = $"fk_account_code";
            }
            else
            {
                columnTofetch = $"{columnTofetch}, fk_account_code";
            }

            if (string.IsNullOrEmpty(partictionColumn))
            {
                partictionColumn = $"fk_account_code";
            }
            else
            {
                partictionColumn = $"{partictionColumn}, fk_account_code";
            }
        }

        StringBuilder newAccountingQuery = new StringBuilder();

        newAccountingQuery.AppendLine("with accountingQuery as (");
        newAccountingQuery.AppendLine("select ");
        if (gridInput.viewType == "3")/// budgetForm View columns
        {
            newAccountingQuery.AppendLine($"{GetLevelIdAndDescColumnBasedOnRequestedLevelBudGetForm(gridInput)}, fk_account_code");
        }
        else
        {
            newAccountingQuery.AppendLine(columnTofetch);// dynamic colum list
        }

        if(gridInput.viewType == "6")
        {
            columnTofetch = isMissindDataQuery ? columnTofetch.Replace("sv.", "") : columnTofetch.Replace("tco.", "");
        }
        newAccountingQuery.AppendLine(", a.[period] as period, a.[gl_year] as budget_year, a.amount as accounting ");// remaining columns needed

        if (gridInput.viewType == "4" && isMissindDataQuery)// project  view for missing project query
        {
            newAccountingQuery.AppendLine(", proj_setup_missing = CASE WHEN pc.pk_project_code IS NULL THEN 1 ELSE 0 END ");
        }

        if (gridInput.viewType == "5" && isMissindDataQuery)// function  view for missing project query
        {
            newAccountingQuery.AppendLine(", func_setup_missing = CASE WHEN sv.pk_function_code IS NULL THEN 1 ELSE 0 END ");
        }
        if (gridInput.viewType == "6" && isMissindDataQuery)// service area view for missing service area functions query
        {
            newAccountingQuery.AppendLine(", func_setup_missing = CASE WHEN tco.fk_function_code IS NULL THEN 1 ELSE 0 END ");
        }
        if (isMissindDataQuery && (gridInput.viewType == "0" || gridInput.viewType == "2"))// missing org Query
        {
            newAccountingQuery.AppendLine(",department_setup_missing = ISNULL(department_setup_missing,1) ,org_setup_missing = CASE WHEN oh.fk_department_code IS NULL THEN 1 ELSE 0 END  ");
        }

        newAccountingQuery.AppendLine("FROM [dbo].tfp_accounting_data a ");
        if (isMissindDataQuery && (gridInput.viewType == "0" || gridInput.viewType == "2"))// missing org code
        {
            //newAccountingQuery.AppendLine("LEFT JOIN [dbo].[tco_org_hierarchy] oh on a.fk_tenant_id = oh.fk_Tenant_id and @OrgVersion = oh.fk_org_version and a.department_code = oh.fk_department_code  ");
            //newAccountingQuery.AppendLine("LEFT JOIN [dbo].[tco_departments] dp on  oh.fk_tenant_id=dp.fk_tenant_id and dp.year_from<=@BudgetYear  and @BudgetYear <=dp.year_to and oh.fk_department_code=dp.pk_department_code  ");

            newAccountingQuery.AppendLine("LEFT ");

        }
        // else
        // {
        if (isCurrentBudgetYear)
        {
            newAccountingQuery.AppendLine(" join flat_org_hierarchy_dep oh on a.fk_tenant_id = oh.fk_tenant_id and a.gl_year = oh.budget_year and a.department_code = oh.fk_department_code  ");
        }
        else
        {
            newAccountingQuery.AppendLine(" join flat_org_hierarchy_dep oh on a.fk_tenant_id = oh.fk_tenant_id and a.gl_year + 1 = oh.budget_year and a.department_code = oh.fk_department_code  ");
        }
        //   }


        //newAccountingQuery.AppendLine("JOIN [dbo].[tco_accounts] ac ON  a.fk_tenant_id = ac.pk_tenant_id and @BudgetYear between YEAR(ac.dateFrom) and YEAR(ac.dateTo) AND a.fk_account_code = ac.pk_account_code ");
        //newAccountingQuery.AppendLine(" JOIN [dbo].[gco_kostra_accounts] kc ON ac.fk_kostra_account_code = kc.pk_kostra_account_code AND kc.type='operations' ");

        //if (serviceSetUp)// for service setup check function
        //{
        //    newAccountingQuery.AppendLine("inner JOIN flat_function_service_values sv ON a.fk_tenant_id = sv.fk_tenant_id and a.gl_year = sv.budget_year and a.fk_function_code = sv.pk_function_code ");
        //}
        if (serviceSetUp || gridInput.viewType == "5" || gridInput.viewType == "6")// for service setup check function
        {
            if (isCurrentBudgetYear)
            {
                if ((gridInput.Filters != null && gridInput.Filters.Any() && gridInput.Filters.Where(x => x.FilterId == FilterId.ServiceFilter).Any()) || gridInput.viewType == "5" || gridInput.viewType == "6")
                {
                    if (isMissindDataQuery && gridInput.viewType == "5")
                    {
                        newAccountingQuery.AppendLine(" left ");
                    }

                    newAccountingQuery.AppendLine(" JOIN flat_function_service_values sv ON a.fk_tenant_id = sv.fk_tenant_id and a.gl_year = sv.budget_year and a.fk_function_code = sv.pk_function_code ");
                }
            }
            else
            {
                if ((gridInput.Filters != null && gridInput.Filters.Any() && gridInput.Filters.Where(x => x.FilterId == FilterId.ServiceFilter).Any()) || gridInput.viewType == "5" || gridInput.viewType == "6")
                {
                    if (isMissindDataQuery && gridInput.viewType == "5")
                    {
                        newAccountingQuery.AppendLine(" left ");
                    }

                    newAccountingQuery.AppendLine(" JOIN flat_function_service_values sv ON a.fk_tenant_id = sv.fk_tenant_id and a.gl_year+1 = sv.budget_year and a.fk_function_code = sv.pk_function_code ");
                }
            }
        }
        if(gridInput.viewType == "6")
        {
            if (isMissindDataQuery)
            {
                newAccountingQuery.AppendLine(" left ");
            }
            newAccountingQuery.AppendLine("  JOIN tco_service_values tco ON a.fk_tenant_id = tco.fk_tenant_id and sv.pk_Function_code = tco.fk_function_code ");
        }
        if (!isMissindDataQuery && (!gridInput.Filters.Any() || (gridInput.Filters.Any() && gridInput.Filters.FirstOrDefault(x => x.FilterId == FilterId.DepartmentFilter) == null)))// if department filter is not selected then dont join on department
        {
            newAccountingQuery.Replace(" INNER JOIN [dbo].[tco_departments] dp on  oh.fk_tenant_id=dp.fk_tenant_id and dp.year_from<=@BudgetYear  and @BudgetYear <=dp.year_to and oh.fk_department_code=dp.pk_department_code  ", string.Empty);
        }

        if (gridInput.viewType == "4")// project view join with project code for project view
        {
            if (isMissindDataQuery)
            {
                newAccountingQuery.AppendLine(" left ");
            }
            newAccountingQuery.AppendLine("JOIN[dbo].[flat_projects] pc ON  a.fk_tenant_id = pc.fk_tenant_id AND @BudgetYear = pc.budget_year AND a.fk_project_code = pc.pk_project_code ");
        }

        if (gridInput.viewType == "3")/// budgetForm View
        {
            if (useBudformSetup)
            {
                newAccountingQuery.AppendLine(" JOIN[dbo].[tco_budform_setup] rl ON '54_OVDRIFT'= rl.report and rl.fk_tenant_id=a.fk_tenant_id  and (a.fk_account_code between rl.account_from and rl.account_to)");
            }
            else
            {
                newAccountingQuery.AppendLine(" JOIN[dbo].[gmd_reporting_line] rl ON '54_OVDRIFT'= rl.report and ac.fk_kostra_account_code = rl.fk_kostra_account_code  ");
            }
        }
        else
        {
            newAccountingQuery.AppendLine(" LEFT JOIN[dbo].[tmd_reporting_line] rl ON a.fk_tenant_id = rl.fk_tenant_id and rl.report = 'MNDRAPP' and a.fk_account_code = rl.fk_account_code ");
        }

        newAccountingQuery.AppendLine(" WHERE  a.fk_tenant_id = @TenantId and a.gl_year IN((@BudgetYear))  and ac.fk_kostra_account_code NOT IN('1580', '1980')  and oh." + orgLevel_valid + "=@orgId ");

        if (isMissindDataQuery)
        {
            /*dont add any check*/
        }
        else
        {
            newAccountingQuery.AppendLine("and oh.department_setup_missing = 0");
        }

        if (!string.IsNullOrEmpty(orgLevel_valid) && orgLevel_valid.ToLower() == "org_id_1")
        {
            newAccountingQuery = newAccountingQuery.Replace($" and oh." + orgLevel_valid + "=@orgId ", "");
        }

        if (gridInput.viewType == "4" && isMissindDataQuery)
        {
            newAccountingQuery.AppendLine(" and pc.pk_project_code is null and department_setup_missing = 0 ");
        }
        if (gridInput.viewType == "5" && isMissindDataQuery)
        {
            newAccountingQuery.AppendLine(" and sv.pk_function_code is null and department_setup_missing = 0 ");
        }
        if (gridInput.viewType == "6" && isMissindDataQuery)
        {
            newAccountingQuery.AppendLine(" and tco.fk_function_code is null and department_setup_missing = 0 ");
        }
        if (isMissindDataQuery && (gridInput.viewType == "0" || gridInput.viewType == "2"))// missing org code
        {
            newAccountingQuery.AppendLine(" and(oh.department_setup_missing is null or oh.department_setup_missing = 1) ");
        }
        else
        {
            newAccountingQuery.AppendLine(AddTreeLevelFetchCommon(gridInput, isMissindDataQuery));// add the which level data needs to be fetched
        }
        var filterInput = new DashBoardFilterHelper
        {
            currentOrgLevel = gridInput.OrgLevel,
            filters = gridInput.Filters,
            tenantId = tenantId,
            userId = userId,
            budgetYear = gridInput.BudgetYear,
            isBudFormView = true,
            viewType = gridInput.viewType != string.Empty ? (DashBoardGridViewTypes)Enum.Parse(typeof(DashBoardGridViewTypes), gridInput.viewType) :DashBoardGridViewTypes.ViewNotRequired
        };
        var filterQuery = BuildFilterQuery(filterInput).GetAwaiter().GetResult();
        if (!string.IsNullOrEmpty(filterQuery))
        {
            newAccountingQuery.AppendLine(filterQuery);
        }
        newAccountingQuery.AppendLine("),orgLevelData as (select " + columnTofetch + ",period, budget_year, sum(accounting) acc ");

        if (isMissindDataQuery && gridInput.viewType == "4")// project view view missing project query
        {
            newAccountingQuery.AppendLine(",proj_setup_missing ");
        }
        if (isMissindDataQuery && (gridInput.viewType == "5" || gridInput.viewType == "6"))// function view view missing project query
        {
            newAccountingQuery.AppendLine(",func_setup_missing ");
        }

        if (isMissindDataQuery && (gridInput.viewType == "0" || gridInput.viewType == "2"))// missing org code
        {
            newAccountingQuery.AppendLine(",department_setup_missing,org_setup_missing ");
        }
        newAccountingQuery.AppendLine(" from accountingQuery ");
        newAccountingQuery.AppendLine($" group by " + columnTofetch);
        if (isMissindDataQuery && gridInput.viewType == "4")// project view view missing project query
        {
            newAccountingQuery.AppendLine(",proj_setup_missing ");
        }
        if (isMissindDataQuery && (gridInput.viewType == "5" || gridInput.viewType == "6"))// function view view missing project query
        {
            newAccountingQuery.AppendLine(",func_setup_missing ");
        }
        if (isMissindDataQuery && (gridInput.viewType == "0" || gridInput.viewType == "2"))// missing org code
        {
            newAccountingQuery.AppendLine(",department_setup_missing,org_setup_missing ");
        }
        newAccountingQuery.AppendLine(",period, budget_year),ytd as (select *,sum(acc) OVER(partition by " + partictionColumn + " ,budget_year order by " + partictionColumn + ",period Rows between unbounded preceding and current row) acc_ytd ");
        newAccountingQuery.AppendLine(" from orgLevelData)");
        newAccountingQuery.AppendLine(" select * from ytd  ");
        if (gridInput.viewType == "4" && isMissindDataQuery)// projectview project missing
        {
            newAccountingQuery.AppendLine(" where proj_setup_missing = 1 ");
        }
        if ((gridInput.viewType == "5" || gridInput.viewType == "6") && isMissindDataQuery)// function project missing
        {
            newAccountingQuery.AppendLine(" where func_setup_missing = 1 ");
        }

        //if ((gridInput.viewType == "0") || (gridInput.viewType == "1") || (gridInput.viewType == "2"))
        //{
        //    if (!perPeriodData)
        //    {
        //        newAccountingQuery.AppendLine(" where [period]<=@forcastePeriod  ");
        //    }                
        //}           

        if (isMissindDataQuery && (gridInput.viewType == "0" || gridInput.viewType == "2"))// missing org code
        {
            newAccountingQuery.AppendLine(" where (department_setup_missing=1 or org_setup_missing=1 )");
        }
        newAccountingQuery.AppendLine(" option(recompile);");
        return newAccountingQuery.ToString();

    }



    private string GetBudgetDataSetQuery(bool serviceSetUp, bool isLocked, AccStatmentWidgetInput gridInput, int tenantId, string userId, bool isMissindDataQuery, bool isCurrentBudgetYear,
        bool addAccountCodeColumn, bool perPeriodData)
    {
        /* this function is created to have common budget query for all the views, different conditions have been added to satisfy differnt view cases
         *  view 0 is org view
         *  view 1 is Account view
         *  view 2 is Org view with next level org( 2 level org then account level is displayed)
         *  view 3 is Budgetform view view
         *  view 4 is project view
         */


        bool useBudformSetup = false;

        if (gridInput.viewType == "3")
        {
            useBudformSetup = useTcoBudFormSetupTable(tenantId, "DASH_ACCSTATE_USE_BUDFORM_SETUP").GetAwaiter().GetResult();
        }
        string orgLevel_valid = ValidateOrgLevel(gridInput.OrgLevel);
        string columnTofetch = FetchColumForView(gridInput, isMissindDataQuery);
        string partictionColumn = FetchPartitionColumForView(gridInput, isMissindDataQuery);

        if (addAccountCodeColumn)
        {
            if (string.IsNullOrEmpty(columnTofetch))
            {
                columnTofetch = $"fk_account_code";
            }
            else
            {
                columnTofetch = $"{columnTofetch}, fk_account_code";
            }

            if (string.IsNullOrEmpty(partictionColumn))
            {
                partictionColumn = $"fk_account_code";
            }
            else
            {
                partictionColumn = $"{partictionColumn}, fk_account_code";
            }
        }

        StringBuilder newBudgetQuery = new StringBuilder();

        newBudgetQuery.AppendLine("with budgetQuery as (");
        newBudgetQuery.AppendLine("select ");
        if (gridInput.viewType == "3")/// budgetForm View columns
        {
            newBudgetQuery.AppendLine($"{GetLevelIdAndDescColumnBasedOnRequestedLevelBudGetForm(gridInput)}, fk_account_code");
        }
        else
        {
            newBudgetQuery.AppendLine(columnTofetch);// dynamic colum list
        }
        if (gridInput.viewType == "6")
        {
            columnTofetch = isMissindDataQuery ? columnTofetch.Replace("sv.", "") : columnTofetch.Replace("tco.", "");
        }
        newBudgetQuery.AppendLine(", a.[period] as period, a.[budget_year] as budget_year, a.amount_year_1 as budget ");// remaining columns needed

        if (gridInput.viewType == "4" && isMissindDataQuery)// project  view for missing project query
        {
            newBudgetQuery.AppendLine(", proj_setup_missing = CASE WHEN pc.pk_project_code IS NULL THEN 1 ELSE 0 END ");
        }
        if (gridInput.viewType == "5" && isMissindDataQuery)// project  view for missing project query
        {
            newBudgetQuery.AppendLine(", func_setup_missing = CASE WHEN sv.pk_function_code IS NULL THEN 1 ELSE 0 END ");
        }
        if (gridInput.viewType == "6" && isMissindDataQuery)// service area view for missing service area functions query
        {
            newBudgetQuery.AppendLine(", func_setup_missing = CASE WHEN tco.fk_function_code IS NULL THEN 1 ELSE 0 END ");
        }

        if (isMissindDataQuery && (gridInput.viewType == "0" || gridInput.viewType == "2"))// missing org Query
        {
            newBudgetQuery.AppendLine(",department_setup_missing = ISNULL(department_setup_missing,1) ,org_setup_missing = CASE WHEN oh.fk_department_code IS NULL THEN 1 ELSE 0 END  ");
        }

        newBudgetQuery.AppendLine("FROM [dbo].tbu_trans_detail a ");
        if (isMissindDataQuery && (gridInput.viewType == "0" || gridInput.viewType == "2"))// missing org code
        {
            //newBudgetQuery.AppendLine("LEFT JOIN [dbo].[tco_org_hierarchy] oh on a.fk_tenant_id = oh.fk_Tenant_id and @OrgVersion = oh.fk_org_version and a.department_code = oh.fk_department_code  ");
            //newBudgetQuery.AppendLine("LEFT JOIN [dbo].[tco_departments] dp on  oh.fk_tenant_id=dp.fk_tenant_id and dp.year_from<=@BudgetYear  and @BudgetYear <=dp.year_to and oh.fk_department_code=dp.pk_department_code  ");

            newBudgetQuery.AppendLine(" LEFT ");
        }
        // else
        // {
        if (isCurrentBudgetYear)
        {
            newBudgetQuery.AppendLine(" join flat_org_hierarchy_dep oh on a.fk_tenant_id = oh.fk_tenant_id and a.[budget_year] = oh.budget_year and a.department_code = oh.fk_department_code  ");
        }
        else
        {
            newBudgetQuery.AppendLine(" join flat_org_hierarchy_dep oh on a.fk_tenant_id = oh.fk_tenant_id and a.[budget_year] + 1 = oh.budget_year and a.department_code = oh.fk_department_code  ");
        }
        //  }

        // newBudgetQuery.AppendLine("JOIN [dbo].[tco_accounts] ac ON  a.fk_tenant_id = ac.pk_tenant_id and @BudgetYear between YEAR(ac.dateFrom) and YEAR(ac.dateTo) AND a.fk_account_code = ac.pk_account_code ");
        // newBudgetQuery.AppendLine(" JOIN [dbo].[gco_kostra_accounts] kc ON ac.fk_kostra_account_code = kc.pk_kostra_account_code AND kc.type='operations' ");

        if (isLocked && isCurrentBudgetYear)// for service setup check function
        {
            newBudgetQuery.AppendLine("  join [dbo].[tco_user_adjustment_codes] ud ON a.fk_tenant_id = ud.fk_tenant_id and a.budget_year = ud.budget_year AND a.fk_adjustment_code = ud.pk_adj_code and 1 = ud.status  ");
        }

        //if (serviceSetUp)// for service setup check function
        //{
        //    newBudgetQuery.AppendLine("inner JOIN flat_function_service_values sv ON a.fk_tenant_id = sv.fk_tenant_id and a.[budget_year] = sv.budget_year and a.fk_function_code = sv.pk_function_code ");
        //}
        if (serviceSetUp || gridInput.viewType == "5" || gridInput.viewType == "6")// for service setup check function
        {
            if (isCurrentBudgetYear)
            {
                if ((gridInput.Filters != null && gridInput.Filters.Any() && gridInput.Filters.Where(x => x.FilterId == FilterId.ServiceFilter).Any()) || gridInput.viewType == "5" || gridInput.viewType == "6")
                {
                    if (isMissindDataQuery && gridInput.viewType == "5")
                    {
                        newBudgetQuery.AppendLine(" left ");
                    }
                    newBudgetQuery.AppendLine(" JOIN flat_function_service_values sv ON a.fk_tenant_id = sv.fk_tenant_id and a.[budget_year] = sv.budget_year and a.fk_function_code = sv.pk_function_code ");
                }
            }
            else
            {
                if ((gridInput.Filters != null && gridInput.Filters.Any() && gridInput.Filters.Where(x => x.FilterId == FilterId.ServiceFilter).Any()) || gridInput.viewType == "5" || gridInput.viewType == "6")
                {
                    if (isMissindDataQuery && gridInput.viewType == "5")
                    {
                        newBudgetQuery.AppendLine(" left ");
                    }
                    newBudgetQuery.AppendLine(" JOIN flat_function_service_values sv ON a.fk_tenant_id = sv.fk_tenant_id and a.[budget_year]+1 = sv.budget_year and a.fk_function_code = sv.pk_function_code ");
                }
            }
        }
        if (gridInput.viewType == "6")
        {
            if (isMissindDataQuery)
            {
                newBudgetQuery.AppendLine(" left ");
            }
            newBudgetQuery.AppendLine("  JOIN tco_service_values tco ON a.fk_tenant_id = tco.fk_tenant_id and sv.pk_Function_code = tco.fk_function_code ");
        }
        if (!isMissindDataQuery && (!gridInput.Filters.Any() || (gridInput.Filters.Any() && gridInput.Filters.FirstOrDefault(x => x.FilterId == FilterId.DepartmentFilter) == null)))// if department filter is not selected then dont join on department
        {
            newBudgetQuery.Replace(" INNER JOIN [dbo].[tco_departments] dp on  oh.fk_tenant_id=dp.fk_tenant_id and dp.year_from<=@BudgetYear  and @BudgetYear <=dp.year_to and oh.fk_department_code=dp.pk_department_code  ", string.Empty);
        }

        if (gridInput.viewType == "4")// project view join with project code for project view
        {
            if (isMissindDataQuery)
            {
                newBudgetQuery.AppendLine(" left ");
            }

            newBudgetQuery.AppendLine(" JOIN[dbo].[flat_projects] pc ON  a.fk_tenant_id = pc.fk_tenant_id AND @BudgetYear = pc.budget_year AND a.fk_project_code = pc.pk_project_code ");
        }

        if (gridInput.viewType == "3")/// budgetForm View
        {
            if (useBudformSetup)
            {
                newBudgetQuery.AppendLine(" JOIN[dbo].[tco_budform_setup] rl ON '54_OVDRIFT'= rl.report and rl.fk_tenant_id=a.fk_tenant_id  and (a.fk_account_code between rl.account_from and rl.account_to)");
            }
            else
            {
                newBudgetQuery.AppendLine(" JOIN[dbo].[gmd_reporting_line] rl ON '54_OVDRIFT'= rl.report and ac.fk_kostra_account_code = rl.fk_kostra_account_code  ");
            }
        }
        else
        {
            newBudgetQuery.AppendLine(" LEFT JOIN[dbo].[tmd_reporting_line] rl ON a.fk_tenant_id = rl.fk_tenant_id and rl.report = 'MNDRAPP' and a.fk_account_code = rl.fk_account_code ");
        }

        newBudgetQuery.AppendLine(" WHERE  a.fk_tenant_id = @TenantId and a.[budget_year] IN((@BudgetYear))  and ac.fk_kostra_account_code NOT IN('1580', '1980')  and oh." + orgLevel_valid + "=@orgId ");// selected orgIdFilter

        if (isMissindDataQuery)
        {
            /*dont add any check*/
        }
        else
        {
            newBudgetQuery.AppendLine("and oh.department_setup_missing = 0");
        }

        if (!string.IsNullOrEmpty(orgLevel_valid) && orgLevel_valid.ToLower() == "org_id_1")
        {
            newBudgetQuery = newBudgetQuery.Replace($" and oh." + orgLevel_valid + "=@orgId ", "");
        }

        if (gridInput.viewType == "4" && isMissindDataQuery)
        {
            newBudgetQuery.AppendLine(" and pc.pk_project_code is null and department_setup_missing = 0  ");
        }
        if (gridInput.viewType == "5" && isMissindDataQuery)
        {
            newBudgetQuery.AppendLine(" and sv.pk_function_code is null and department_setup_missing = 0  ");
        }
        if (gridInput.viewType == "6" && isMissindDataQuery)
        {
            newBudgetQuery.AppendLine(" and tco.fk_function_code is null and department_setup_missing = 0 ");
        }
        if (isMissindDataQuery && (gridInput.viewType == "0" || gridInput.viewType == "2"))// missing org code
        {
            newBudgetQuery.AppendLine(" and(oh.department_setup_missing is null or oh.department_setup_missing = 1) ");
        }
        else
        {
            newBudgetQuery.AppendLine(AddTreeLevelFetchCommon(gridInput, isMissindDataQuery));// add the which level data needs to be fetched
        }
        var filterInput = new DashBoardFilterHelper
        {
            currentOrgLevel = gridInput.OrgLevel,
            filters = gridInput.Filters,
            tenantId = tenantId,
            userId = userId,
            budgetYear = gridInput.BudgetYear,
            isBudFormView = true,
            viewType = gridInput.viewType != string.Empty ? (DashBoardGridViewTypes)Enum.Parse(typeof(DashBoardGridViewTypes), gridInput.viewType) : DashBoardGridViewTypes.ViewNotRequired
        };
        var filterQuery = BuildFilterQuery(filterInput).GetAwaiter().GetResult();
        if (!string.IsNullOrEmpty(filterQuery))
        {
            newBudgetQuery.AppendLine(filterQuery);
        }
        newBudgetQuery.AppendLine("),orgLevelData as (select " + columnTofetch + ",period, budget_year, sum(budget) bud ");

        if (isMissindDataQuery && gridInput.viewType == "4")// project view view missing project query
        {

            newBudgetQuery.AppendLine(",proj_setup_missing ");
        }


        if (isMissindDataQuery && (gridInput.viewType == "5" || gridInput.viewType == "6"))// function view view missing project query
        {

            newBudgetQuery.AppendLine(",func_setup_missing ");
        }

        if (isMissindDataQuery && (gridInput.viewType == "0" || gridInput.viewType == "2"))// missing org code
        {
            newBudgetQuery.AppendLine(",department_setup_missing,org_setup_missing ");

        }
        newBudgetQuery.AppendLine(" from budgetQuery ");
        newBudgetQuery.AppendLine($" group by " + columnTofetch);
        if (isMissindDataQuery && gridInput.viewType == "4")// project view view missing project query
        {

            newBudgetQuery.AppendLine(",proj_setup_missing ");
        }
        if (isMissindDataQuery && (gridInput.viewType == "5" || gridInput.viewType == "6"))// function view view missing project query
        {

            newBudgetQuery.AppendLine(",func_setup_missing ");
        }
        if (isMissindDataQuery && (gridInput.viewType == "0" || gridInput.viewType == "2"))// missing org code
        {
            newBudgetQuery.AppendLine(",department_setup_missing,org_setup_missing ");

        }
        newBudgetQuery.AppendLine(",period, budget_year),ytd as (select *,sum(bud) OVER(partition by " + partictionColumn + ", budget_year order by " + partictionColumn + ",period Rows between unbounded preceding and current row) bud_ytd, ");
        newBudgetQuery.AppendLine("sum(bud) OVER(partition by " + partictionColumn + " ,budget_year order by " + partictionColumn + " ) annual_budget ");
        newBudgetQuery.AppendLine(" from orgLevelData)");
        newBudgetQuery.AppendLine(" select * from ytd  ");
        if (gridInput.viewType == "4" && isMissindDataQuery)// projectview project missing
        {
            newBudgetQuery.AppendLine(" where  proj_setup_missing = 1 ");
        }

        if ((gridInput.viewType == "5" || gridInput.viewType == "6") && isMissindDataQuery)// function project missing
        {
            newBudgetQuery.AppendLine(" where  func_setup_missing = 1 ");
        }

        //if ((gridInput.viewType == "0") || (gridInput.viewType == "1") || (gridInput.viewType == "2"))
        //{
        //    if (!perPeriodData)
        //    {
        //        newBudgetQuery.AppendLine(" where [period]<=@forcastePeriod  ");
        //    }
        //}


        if (isMissindDataQuery && (gridInput.viewType == "0" || gridInput.viewType == "2"))// missing org code
        {
            newBudgetQuery.AppendLine(" where (department_setup_missing=1 or org_setup_missing=1 )");
        }
        newBudgetQuery.AppendLine(" order by " + columnTofetch + ", period  ");

        newBudgetQuery.AppendLine(" option(recompile);");
        return newBudgetQuery.ToString();

    }



    private string GetOriginalBudgetDataSetQuery(bool serviceSetUp, AccStatmentWidgetInput gridInput, int tenantId, string userId, bool isMissindDataQuery,
        bool addAccountCodeColumn, bool perPeriodData)
    {
        /* this function is created to have common originalbudgetQuery query for all the views, different conditions have been added to satisfy differnt view cases
         *  view 0 is org view
         *  view 1 is Account view
         *  view 2 is Org view with next level org( 2 level org then account level is displayed)
         *  view 3 is Budgetform view view
         *  view 4 is project view
         */


        bool useBudformSetup = false;

        if (gridInput.viewType == "3")
        {
            useBudformSetup = useTcoBudFormSetupTable(tenantId, "DASH_ACCSTATE_USE_BUDFORM_SETUP").GetAwaiter().GetResult();
        }
        string orgLevel_valid = ValidateOrgLevel(gridInput.OrgLevel);
        string columnTofetch = FetchColumForView(gridInput, isMissindDataQuery);
        string partictionColumn = FetchPartitionColumForView(gridInput, isMissindDataQuery);

        if (addAccountCodeColumn)
        {
            if (string.IsNullOrEmpty(columnTofetch))
            {
                columnTofetch = $"fk_account_code";
            }
            else
            {
                columnTofetch = $"{columnTofetch}, fk_account_code";
            }

            if (string.IsNullOrEmpty(partictionColumn))
            {
                partictionColumn = $"fk_account_code";
            }
            else
            {
                partictionColumn = $"{partictionColumn}, fk_account_code";
            }
        }

        StringBuilder newOriginalBudgetQuery = new StringBuilder();

        newOriginalBudgetQuery.AppendLine("with originalbudgetQuery as (");
        newOriginalBudgetQuery.AppendLine("select ");
        if (gridInput.viewType == "3")/// budgetForm View columns
        {
            newOriginalBudgetQuery.AppendLine($"{GetLevelIdAndDescColumnBasedOnRequestedLevelBudGetForm(gridInput)}, fk_account_code");
        }
        else
        {
            newOriginalBudgetQuery.AppendLine(columnTofetch);// dynamic colum list
        }
        if (gridInput.viewType == "6")
        {
            columnTofetch = isMissindDataQuery ? columnTofetch.Replace("sv.", "") : columnTofetch.Replace("tco.", "");
        }
        newOriginalBudgetQuery.AppendLine(", a.[period] as period, a.[budget_year] as budget_year, a.amount_year_1 as budget ");// remaining columns needed

        if (gridInput.viewType == "4" && isMissindDataQuery)// project  view for missing project query
        {
            newOriginalBudgetQuery.AppendLine(", proj_setup_missing = CASE WHEN pc.pk_project_code IS NULL THEN 1 ELSE 0 END ");
        }
        if (gridInput.viewType == "5" && isMissindDataQuery)// func  view for missing project query
        {
            newOriginalBudgetQuery.AppendLine(", func_setup_missing = CASE WHEN tco.pk_function_code IS NULL THEN 1 ELSE 0 END ");
        }
        if (gridInput.viewType == "6" && isMissindDataQuery)// service area view for missing service area functions query
        {
            newOriginalBudgetQuery.AppendLine(", func_setup_missing = CASE WHEN tco.fk_function_code IS NULL THEN 1 ELSE 0 END ");
        }

        if (isMissindDataQuery && (gridInput.viewType == "0" || gridInput.viewType == "2"))// missing org Query
        {
            newOriginalBudgetQuery.AppendLine(",department_setup_missing = ISNULL(department_setup_missing,1) ,org_setup_missing = CASE WHEN oh.fk_department_code IS NULL THEN 1 ELSE 0 END ");
        }

        newOriginalBudgetQuery.AppendLine("FROM [dbo].tbu_trans_detail_original a ");
        if (isMissindDataQuery && (gridInput.viewType == "0" || gridInput.viewType == "2"))// missing org code
        {
            //newOriginalBudgetQuery.AppendLine("LEFT JOIN [dbo].[tco_org_hierarchy] oh on a.fk_tenant_id = oh.fk_Tenant_id and @OrgVersion = oh.fk_org_version and a.department_code = oh.fk_department_code  ");
            //newOriginalBudgetQuery.AppendLine("LEFT JOIN [dbo].[tco_departments] dp on  oh.fk_tenant_id=dp.fk_tenant_id and dp.year_from<=@BudgetYear  and @BudgetYear <=dp.year_to and oh.fk_department_code=dp.pk_department_code  ");

            newOriginalBudgetQuery.AppendLine("LEFT ");
        }
        // else
        // {
        newOriginalBudgetQuery.AppendLine(" join flat_org_hierarchy_dep oh on a.fk_tenant_id = oh.fk_tenant_id and a.[budget_year] = oh.budget_year and a.department_code = oh.fk_department_code  ");
        //   }


        //newOriginalBudgetQuery.AppendLine("JOIN [dbo].[tco_accounts] ac ON  a.fk_tenant_id = ac.pk_tenant_id and @BudgetYear between YEAR(ac.dateFrom) and YEAR(ac.dateTo) AND a.fk_account_code = ac.pk_account_code ");
        //newOriginalBudgetQuery.AppendLine(" JOIN [dbo].[gco_kostra_accounts] kc ON ac.fk_kostra_account_code = kc.pk_kostra_account_code AND kc.type='operations' ");

        if (serviceSetUp || gridInput.viewType == "5" || gridInput.viewType == "6")// for service setup check function
        {
            if ((gridInput.Filters != null && gridInput.Filters.Any() && gridInput.Filters.Where(x => x.FilterId == FilterId.ServiceFilter).Any()) || gridInput.viewType == "5" || gridInput.viewType == "6")
            {
                if (isMissindDataQuery && gridInput.viewType == "5")
                {
                    newOriginalBudgetQuery.AppendLine(" left ");
                }
                newOriginalBudgetQuery.AppendLine(" JOIN flat_function_service_values sv ON a.fk_tenant_id = sv.fk_tenant_id and a.[budget_year] = sv.budget_year and a.fk_function_code = sv.pk_function_code ");
            }
        }
        if (gridInput.viewType == "6")
        {
            if (isMissindDataQuery)
            {
                newOriginalBudgetQuery.AppendLine(" left ");
            }
            newOriginalBudgetQuery.AppendLine("  JOIN tco_service_values tco ON a.fk_tenant_id = tco.fk_tenant_id and sv.pk_Function_code = tco.fk_function_code ");
        }
        if (!isMissindDataQuery && (!gridInput.Filters.Any() || (gridInput.Filters.Any() && gridInput.Filters.FirstOrDefault(x => x.FilterId == FilterId.DepartmentFilter) == null)))// if department filter is not selected then dont join on department
        {
            newOriginalBudgetQuery.Replace(" INNER JOIN [dbo].[tco_departments] dp on  oh.fk_tenant_id=dp.fk_tenant_id and dp.year_from<=@BudgetYear  and @BudgetYear <=dp.year_to and oh.fk_department_code=dp.pk_department_code  ", string.Empty);
        }

        if (gridInput.viewType == "4")// project view join with project code for project view
        {
            if (isMissindDataQuery)
            {
                newOriginalBudgetQuery.AppendLine(" left ");
            }
            newOriginalBudgetQuery.AppendLine(" JOIN[dbo].[flat_projects] pc ON  a.fk_tenant_id = pc.fk_tenant_id AND @BudgetYear = pc.budget_year AND a.fk_project_code = pc.pk_project_code ");

        }
        if (gridInput.viewType == "3")/// budgetForm View
        {
            if (useBudformSetup)
            {
                newOriginalBudgetQuery.AppendLine(" JOIN[dbo].[tco_budform_setup] rl ON '54_OVDRIFT'= rl.report and rl.fk_tenant_id=a.fk_tenant_id  and (a.fk_account_code between rl.account_from and rl.account_to)");
            }
            else
            {
                newOriginalBudgetQuery.AppendLine(" JOIN[dbo].[gmd_reporting_line] rl ON '54_OVDRIFT'= rl.report and ac.fk_kostra_account_code = rl.fk_kostra_account_code  ");
            }
        }
        else
        {
            newOriginalBudgetQuery.AppendLine(" LEFT JOIN[dbo].[tmd_reporting_line] rl ON a.fk_tenant_id = rl.fk_tenant_id and rl.report = 'MNDRAPP' and a.fk_account_code = rl.fk_account_code ");
        }

        newOriginalBudgetQuery.AppendLine(" WHERE  a.fk_tenant_id = @TenantId and a.[budget_year] IN ((@BudgetYear))  and ac.fk_kostra_account_code NOT IN('1580', '1980')  and oh." + orgLevel_valid + "=@orgId ");// selected orgIdFilter

        if (isMissindDataQuery)
        {
            /*dont add any check*/
        }
        else
        {
            newOriginalBudgetQuery.AppendLine("and oh.department_setup_missing = 0");
        }

        if (!string.IsNullOrEmpty(orgLevel_valid) && orgLevel_valid.ToLower() == "org_id_1")
        {
            newOriginalBudgetQuery = newOriginalBudgetQuery.Replace($" and oh." + orgLevel_valid + "=@orgId ", "");
        }

        if (gridInput.viewType == "4" && isMissindDataQuery)
        {
            newOriginalBudgetQuery.AppendLine(" and pc.pk_project_code is null and department_setup_missing = 0  ");
        }
        if (gridInput.viewType == "5" && isMissindDataQuery)
        {
            newOriginalBudgetQuery.AppendLine(" and sv.pk_function_code is null and department_setup_missing = 0 ");
        }
        if (gridInput.viewType == "6" && isMissindDataQuery)
        {
            newOriginalBudgetQuery.AppendLine(" and tco.fk_function_code is null and department_setup_missing = 0 ");
        }

        if (isMissindDataQuery && (gridInput.viewType == "0" || gridInput.viewType == "2"))// missing org code
        {
            newOriginalBudgetQuery.AppendLine(" and(oh.department_setup_missing is null or oh.department_setup_missing = 1) ");
        }
        else
        {
            newOriginalBudgetQuery.AppendLine(AddTreeLevelFetchCommon(gridInput, isMissindDataQuery));// add the which level data needs to be fetched
        }
        var filterInput = new DashBoardFilterHelper
        {
            currentOrgLevel = gridInput.OrgLevel,
            filters = gridInput.Filters,
            tenantId = tenantId,
            userId = userId,
            budgetYear = gridInput.BudgetYear,
            isBudFormView = true,
            viewType = gridInput.viewType != string.Empty ? (DashBoardGridViewTypes)Enum.Parse(typeof(DashBoardGridViewTypes), gridInput.viewType) : DashBoardGridViewTypes.ViewNotRequired
        };
        var filterQuery = BuildFilterQuery(filterInput).GetAwaiter().GetResult();
        if (!string.IsNullOrEmpty(filterQuery))
        {
            newOriginalBudgetQuery.AppendLine(filterQuery);
        }
        newOriginalBudgetQuery.AppendLine("),orgLevelData as (select " + columnTofetch + ",period, budget_year, sum(budget) bud ");

        if (isMissindDataQuery && gridInput.viewType == "4")// project view view missing project query
        {

            newOriginalBudgetQuery.AppendLine(",proj_setup_missing ");
        }

        if (isMissindDataQuery && (gridInput.viewType == "5" || gridInput.viewType == "6"))// function view view missing project query
        {

            newOriginalBudgetQuery.AppendLine(",func_setup_missing ");
        }

        if (isMissindDataQuery && (gridInput.viewType == "0" || gridInput.viewType == "2"))// missing org code
        {
            newOriginalBudgetQuery.AppendLine(",department_setup_missing,org_setup_missing ");

        }
        newOriginalBudgetQuery.AppendLine(" from originalbudgetQuery ");
        newOriginalBudgetQuery.AppendLine($" group by " + columnTofetch);
        if (isMissindDataQuery && gridInput.viewType == "4")// project view view missing project query
        {

            newOriginalBudgetQuery.AppendLine(",proj_setup_missing ");
        }
        if (isMissindDataQuery && (gridInput.viewType == "5" || gridInput.viewType == "6"))// project view view missing project query
        {

            newOriginalBudgetQuery.AppendLine(",func_setup_missing ");
        }
        if (isMissindDataQuery && (gridInput.viewType == "0" || gridInput.viewType == "2"))// missing org code
        {
            newOriginalBudgetQuery.AppendLine(",department_setup_missing,org_setup_missing ");

        }
        newOriginalBudgetQuery.AppendLine(",period, budget_year),ytd as (select *,sum(bud) OVER(partition by " + partictionColumn + ", budget_year order by " + partictionColumn + ",period Rows between unbounded preceding and current row) bud_ytd, ");
        newOriginalBudgetQuery.AppendLine("sum(bud) OVER(partition by " + partictionColumn + " ,budget_year order by " + partictionColumn + " ) annual_budget ");
        newOriginalBudgetQuery.AppendLine(" from orgLevelData) ");
        newOriginalBudgetQuery.AppendLine(" select * from ytd  ");
        if (gridInput.viewType == "4" && isMissindDataQuery)// projectview project missing
        {
            newOriginalBudgetQuery.AppendLine(" where proj_setup_missing = 1 ");
        }

        if ((gridInput.viewType == "5" || gridInput.viewType == "6") && isMissindDataQuery)// projectview project missing
        {
            newOriginalBudgetQuery.AppendLine(" where func_setup_missing = 1 ");
        }

        //if ((gridInput.viewType == "0") || (gridInput.viewType == "1") || (gridInput.viewType == "2"))
        //{
        //    if (!perPeriodData)
        //    {
        //        newOriginalBudgetQuery.AppendLine(" where [period]<=@forcastePeriod  ");
        //    }
        //}


        if (isMissindDataQuery && (gridInput.viewType == "0" || gridInput.viewType == "2"))// missing org code
        {
            newOriginalBudgetQuery.AppendLine(" where (department_setup_missing=1 or org_setup_missing=1 )");
        }
        newOriginalBudgetQuery.AppendLine(" order by " + columnTofetch + ", period  ");

        newOriginalBudgetQuery.AppendLine(" option(recompile);");
        return newOriginalBudgetQuery.ToString();
    }



    private string FetchColumForView(AccStatmentWidgetInput gridInput, bool isMissindDataQuery)
    {
        string value = string.Empty;
        switch (gridInput.viewType)
        {
            case "0": value = isMissindDataQuery ? GetLevelIdAndDescColumnBasedOnRequestedLevelMissingDept_OrgeSetup(gridInput) : GetLevelIdAndDescColumnBasedOnRequestedLevelOrgView(gridInput); break;//orgView
            case "1": value = GetLevelIdAndDescColumnBasedOnRequestedLevel(gridInput); break;//Account View
            case "2": value = isMissindDataQuery ? GetLevelIdAndDescColumnBasedOnRequestedLevelMissingDept_OrgeSetup(gridInput) : GetLevelIdAndDescColumnBasedOnRequestedLevelOrgView(gridInput); break;//orgView next level
            case "3": value = GetLevelIdAndDescColumnBasedOnRequestedLevel(gridInput); break;//budget Form 
            case "4": value = GetLevelIdAndDescColumnBasedOnRequestedLevelProjView(gridInput); break;//project 
            case "5": value = GetLevelIdAndDescColumnBasedOnRequestedLevelFuncView(gridInput); break;// function
            case "6": value = isMissindDataQuery ? GetLevelIdAndDescColumnBasedOnRequestedLevelSAMissingView(gridInput) : GetLevelIdAndDescColumnBasedOnRequestedLevelSAView(gridInput); break;// service area
        }
        return value;
    }



    private string FetchPartitionColumForView(AccStatmentWidgetInput gridInput, bool isMissindDataQuery)
    {
        string value = string.Empty;
        switch (gridInput.viewType)
        {
            case "0": value = isMissindDataQuery ? GetValidPartitionColumnBasedOnRequestedLevelMissingDept_OrgeSetup(gridInput) : GetValidPartitionColumnBasedOnRequestedLevelOrgView(gridInput); break;
            case "1": value = GetValidPartitionColumnBasedOnRequestedLevel(gridInput); break;
            case "2": value = isMissindDataQuery ? GetValidPartitionColumnBasedOnRequestedLevelMissingDept_OrgeSetup(gridInput) : GetValidPartitionColumnBasedOnRequestedLevelOrgView(gridInput); break;
            case "3": value = GetPartitionColumnBasedOnRequestedLevelBudGetForm(gridInput); break;
            case "4": value = GetValidPartitionColumnBasedOnRequestedLevelProjView(gridInput); break;
            case "5": value = GetValidPartitionColumnBasedOnRequestedLevelFuncView(gridInput); break;// function
            case "6": value = isMissindDataQuery ? GetValidPartitionColumnBasedOnRequestedLevelSAMissingView(gridInput) : GetValidPartitionColumnBasedOnRequestedLevelSAView(gridInput); break;// service area
        }

        return value;
    }



    private string AddTreeLevelFetchCommon(AccStatmentWidgetInput gridInput, bool isMissindDataQuery)
    {
        string value = string.Empty;
        switch (gridInput.viewType)
        {
            case "0": value = isMissindDataQuery ? AddTreeLevelFetchOrgView_missingSetup(gridInput) : AddTreeLevelFetchOrgView(gridInput); break;
            case "1": value = AddTreeLevelFetchAccountView(gridInput); break;
            case "2": value = isMissindDataQuery ? AddTreeLevelFetchOrgView_missingSetup(gridInput) : AddTreeLevelFetchOrgView_NextLevel(gridInput); break;
            case "3": value = AddTreeLevelFetchBudgetFormView(gridInput); break;
            case "4": value = AddTreeLevelFetchProjectView(gridInput); break;
            case "5": value = AddTreeLevelFetchFuncView(gridInput); break;
        }
        return value;
    }



    private string AddTreeLevelFetchOrgView_NextLevel(AccStatmentWidgetInput gridInput)
    {
        StringBuilder treeLevel = new StringBuilder();

        switch (gridInput.RequestedTreeLevel)
        {
            case 2: treeLevel.Append(" and oh.org_id_" + (gridInput.OrgLevel + 1) + "=@Level1Id"); break;
            case 3: treeLevel.Append(" and oh.org_id_" + (gridInput.OrgLevel + 1) + " = @Level1Id  and oh.org_id_" + (gridInput.OrgLevel + 2) + " = @Level2Id "); break;
            case 4: treeLevel.Append(" and oh.org_id_" + (gridInput.OrgLevel + 1) + " = @Level1Id and oh.org_id_" + (gridInput.OrgLevel + 2) + " =  @Level2Id and rl.level_1_id = @Level3Id "); break;
            case 5: treeLevel.Append(" and oh.org_id_" + (gridInput.OrgLevel + 1) + " = @Level1Id and oh.org_id_" + (gridInput.OrgLevel + 2) + " =  @Level2Id and rl.level_1_id = @Level3Id and rl.level_2_id = @Level4Id "); break;
            case 6: treeLevel.Append(" and oh.org_id_" + (gridInput.OrgLevel + 1) + " = @Level1Id and oh.org_id_" + (gridInput.OrgLevel + 2) + " =  @Level2Id and rl.level_1_id = @Level3Id and rl.level_2_id = @Level4Id  and rl.level_3_id = @Level5Id"); break;
            case 7: treeLevel.Append(" and oh.org_id_" + (gridInput.OrgLevel + 1) + " = @Level1Id and oh.org_id_" + (gridInput.OrgLevel + 2) + " =  @Level2Id and rl.level_1_id = @Level3Id and rl.level_2_id = @Level4Id  and rl.level_3_id = @Level5Id and rl.level_4_id = @Level6Id"); break;
        }
        return treeLevel.ToString();
    }


    private string AddTreeLevelFetchOrgView(AccStatmentWidgetInput gridInput)
    {
        StringBuilder treeLevel = new StringBuilder();
        switch (gridInput.RequestedTreeLevel)
        {
            case 2: treeLevel.Append(" and oh.org_id_" + (gridInput.OrgLevel + 1) + "=@Level1Id"); break;
            case 3: treeLevel.Append(" and oh.org_id_" + (gridInput.OrgLevel + 1) + " = @Level1Id and rl.level_1_id = @Level2Id"); break;
            case 4: treeLevel.Append(" and oh.org_id_" + (gridInput.OrgLevel + 1) + " = @Level1Id and rl.level_1_id = @Level2Id and rl.level_2_id = @Level3Id"); break;
            case 5: treeLevel.Append(" and oh.org_id_" + (gridInput.OrgLevel + 1) + " = @Level1Id and rl.level_1_id = @Level2Id and rl.level_2_id = @Level3Id and rl.level_3_id = @Level4Id"); break;
            case 6: treeLevel.Append(" and oh.org_id_" + (gridInput.OrgLevel + 1) + " = @Level1Id and rl.level_1_id = @Level2Id and rl.level_2_id = @Level3Id and rl.level_3_id = @Level4Id and rl.level_4_id = @Level5Id"); break;
        }
        return treeLevel.ToString();
    }



    private string AddTreeLevelFetchAccountView(AccStatmentWidgetInput gridInput)
    {
        StringBuilder treeLevel = new StringBuilder();
        switch (gridInput.RequestedTreeLevel)
        {
            case 2: treeLevel.AppendLine(gridInput.level1IdExpanded == "ZZ" ? " and rl.level_1_id is Null " : " and rl.level_1_id = @Level1Id"); break;
            case 3: treeLevel.AppendLine(gridInput.level2IdExpanded == "ZZ" ? " and rl.level_1_id is Null and rl.level_2_id is Null " : " and rl.level_1_id = @Level1Id and rl.level_2_id = @Level2Id"); break;
            case 4: treeLevel.AppendLine(gridInput.level3IdExpanded == "ZZ" ? " and rl.level_1_id is Null and rl.level_2_id is Null and rl.level_3_id is Null " : " and rl.level_1_id = @Level1Id and rl.level_2_id = @Level2Id and rl.level_3_id = @Level3Id"); break;
            case 5: treeLevel.AppendLine(gridInput.level4IdExpanded == "ZZ" ? " and rl.level_1_id is Null and rl.level_2_id is Null and rl.level_3_id is Null and rl.level_4_id is Null " : " and rl.level_1_id = @Level1Id and rl.level_2_id = @Level2Id and rl.level_3_id = @Level3Id and rl.level_4_id = @Level4Id"); break;
            case 6: treeLevel.AppendLine(gridInput.level5IdExpanded == "ZZ" ? " and rl.level_1_id is Null and rl.level_2_id is Null and rl.level_3_id is Null and rl.level_4_id is Null and rl.level_5_id is Null" : " and rl.level_1_id = @Level1Id and rl.level_2_id = @Level2Id and rl.level_3_id = @Level3Id and rl.level_4_id = @Level4Id and rl.level_5_id = @Level5Id"); break;
        }
        return treeLevel.ToString();
    }



    private string AddTreeLevelFetchBudgetFormView(AccStatmentWidgetInput gridInput)
    {
        StringBuilder treeLevel = new StringBuilder();
        switch (gridInput.RequestedTreeLevel)
        {
            case 2: treeLevel.Append(" and rl.line_result_id=@Level1Id"); break;
            case 3: treeLevel.Append(" and rl.line_result_id=@Level1Id and rl.line_group_id = @Level2Id"); break;
            case 4: treeLevel.Append(" and rl.line_result_id=@Level1Id and rl.line_group_id = @Level2Id and rl.line_item_id = @Level3Id"); break;
            case 5: treeLevel.Append(" and rl.line_result_id=@Level1Id and rl.line_group_id = @Level2Id and rl.line_item_id = @Level3Id and kc.pk_kostra_account_code = @Level4Id"); break;
            case 6: treeLevel.Append(" and rl.line_result_id=@Level1Id and rl.line_group_id = @Level2Id and rl.line_item_id = @Level3Id and kc.pk_kostra_account_code = @Level4Id and ac.pk_account_code = @Level5Id"); break;
        }
        return treeLevel.ToString();
    }



    private string AddTreeLevelFetchProjectView(AccStatmentWidgetInput gridInput)
    {
        StringBuilder treeLevel = new StringBuilder();

        if (gridInput.isMissingProjectExpanded)
        {
            switch (gridInput.RequestedTreeLevel)
            {

                case 3: treeLevel.Append(" and rl.level_1_id = @Level2Id"); break;
                case 4: treeLevel.Append(" and rl.level_1_id = @Level2Id and rl.level_2_id = @Level3Id"); break;
                case 5: treeLevel.Append(" and rl.level_1_id = @Level2Id and rl.level_2_id = @Level3Id and rl.level_3_id = @Level4Id"); break;
                case 6: treeLevel.Append(" and rl.level_1_id = @Level2Id and rl.level_2_id = @Level3Id and rl.level_3_id = @Level4Id and rl.level_4_id = @Level5Id"); break;
            }
        }
        else
        {
            switch (gridInput.RequestedTreeLevel)
            {
                case 2: treeLevel.Append(" and pc.pk_project_code=@Level1Id"); break;
                case 3: treeLevel.Append(" and pc.pk_project_code= @Level1Id and rl.level_1_id = @Level2Id"); break;
                case 4: treeLevel.Append(" and pc.pk_project_code= @Level1Id and rl.level_1_id = @Level2Id and rl.level_2_id = @Level3Id"); break;
                case 5: treeLevel.Append(" and pc.pk_project_code= @Level1Id and rl.level_1_id = @Level2Id and rl.level_2_id = @Level3Id and rl.level_3_id = @Level4Id"); break;
                case 6: treeLevel.Append(" and pc.pk_project_code= @Level1Id and rl.level_1_id = @Level2Id and rl.level_2_id = @Level3Id and rl.level_3_id = @Level4Id and rl.level_4_id = @Level5Id"); break;
            }
        }
        return treeLevel.ToString();
    }


    private string GetPartitionColumnBasedOnRequestedLevelBudGetForm(AccStatmentWidgetInput gridInput)
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = GetAllValidLevelColumnsBudForm();

        return validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel) != null ? validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel).PartitionColumns : validSelectColumForLevels.FirstOrDefault(z => z.Key == 1).PartitionColumns;
    }



    private string AddTreeLevelFetchOrgView_missingSetup(AccStatmentWidgetInput gridInput)
    {
        StringBuilder treeLevel = new StringBuilder();
        switch (gridInput.RequestedTreeLevel)
        {


            case 3: treeLevel.Append(" and rl.level_1_id = @Level2Id"); break;
            case 4: treeLevel.Append(" and rl.level_1_id = @Level2Id and rl.level_2_id = @Level3Id"); break;
            case 5: treeLevel.Append(" and rl.level_1_id = @Level2Id and rl.level_2_id = @Level3Id and rl.level_3_id = @Level4Id"); break;
            case 6: treeLevel.Append(" and rl.level_1_id = @Level2Id and rl.level_2_id = @Level3Id and rl.level_3_id = @Level4Id and rl.level_4_id = @Level5Id"); break;
        }
        return treeLevel.ToString();
    }



    private string GetLevelIdAndDescColumnBasedOnRequestedLevelFuncView(AccStatmentWidgetInput gridInput)
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = (!gridInput.isMissingProjectExpanded) ? GetAllValidLevelColumnsFuncView(gridInput) : GetAllValidLevelColumnsFuncView_MissingFunc(gridInput);

        return validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel) != null ? validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel).Value : validSelectColumForLevels.FirstOrDefault(z => z.Key == 1).Value;
    }



    private List<KeyValueIntAccStmt> GetAllValidLevelColumnsFuncView(AccStatmentWidgetInput gridInput)
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = new List<KeyValueIntAccStmt>();
        //level 1
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 1,
            Value = $"pk_function_code,function_name",
            PartitionColumns = "pk_function_code"
        });
        //level 2
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 2,
            Value = $"pk_function_code,function_name,level_1_id,level_1_description",
            PartitionColumns = "pk_function_code,level_1_id"
        });
        //level 3
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 3,
            Value = $"pk_function_code,function_name,level_1_id,level_1_description,level_2_id,level_2_description",
            PartitionColumns = "pk_function_code,level_1_id,level_2_id"
        });
        //level 4
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 4,
            Value = $"pk_function_code,function_name,level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description",
            PartitionColumns = "pk_function_code,level_1_id,level_2_id,level_3_id"
        });
        //level 5
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 5,
            Value = $"pk_function_code,function_name,level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description,level_4_id,level_4_description",
            PartitionColumns = "pk_function_code,level_1_id,level_2_id,level_3_id,level_4_id"
        });
        //level 6
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 6,
            Value = $"pk_function_code,function_name,level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description,level_4_id,level_4_description,level_5_id,level_5_description",
            PartitionColumns = "pk_function_code,level_1_id,level_2_id,level_3_id,level_4_id,level_5_id"
        });

        return validSelectColumForLevels;
    }


    private List<KeyValueIntAccStmt> GetAllValidLevelColumnsFuncView_MissingFunc(AccStatmentWidgetInput gridInput)
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = new List<KeyValueIntAccStmt>();
        //level 1
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 1,
            Value = $"pk_function_code",
            PartitionColumns = "pk_function_code"
        });
        //level 2
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 2,
            Value = $"level_1_id,level_1_description",
            PartitionColumns = "level_1_id"
        });
        //level 3
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 3,
            Value = $"level_1_id,level_1_description,level_2_id,level_2_description",
            PartitionColumns = "level_1_id,level_2_id"
        });
        //level 4
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 4,
            Value = $"level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description",
            PartitionColumns = "level_1_id,level_2_id,level_3_id"
        });
        //level 5
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 5,
            Value = $"level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description,level_4_id,level_4_description",
            PartitionColumns = "level_1_id,level_2_id,level_3_id,level_4_id"
        });
        //level 6
        validSelectColumForLevels.Add(new KeyValueIntAccStmt()
        {
            Key = 6,
            Value = $"level_1_id,level_1_description,level_2_id,level_2_description,level_3_id,level_3_description,level_4_id,level_4_description,level_5_id,level_5_description",
            PartitionColumns = "level_1_id,level_2_id,level_3_id,level_4_id,level_5_id"
        });

        return validSelectColumForLevels;
    }


    private string GetValidPartitionColumnBasedOnRequestedLevelFuncView(AccStatmentWidgetInput gridInput)
    {
        List<KeyValueIntAccStmt> validSelectColumForLevels = (!gridInput.isMissingProjectExpanded) ? GetAllValidLevelColumnsFuncView(gridInput) : GetAllValidLevelColumnsFuncView_MissingFunc(gridInput);

        return validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel) != null ? validSelectColumForLevels.FirstOrDefault(z => z.Key == gridInput.RequestedTreeLevel).PartitionColumns : validSelectColumForLevels.FirstOrDefault(z => z.Key == 1).PartitionColumns;
    }


    private string SetIdColumFuncView(AccStatmentWidgetInput gridInput, DataRow readera)
    {
        switch (gridInput.RequestedTreeLevel)
        {
            case 1: return Uri.EscapeDataString($"{readera["pk_function_code"]}");
            case 2: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{readera["level_1_id"]}");
            case 3: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{readera["level_2_id"]}");
            case 4: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{readera["level_3_id"]}");
            case 5: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{readera["level_4_id"]}");
            case 6: return Uri.EscapeDataString($"{gridInput.level1IdExpanded}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{gridInput.level5IdExpanded}_{readera["level_5_id"]}");
            default: return Uri.EscapeDataString($"{readera["pk_function_code"]}");
        }
    }



    private string SetNamePropertyFuncView(AccStatmentWidgetInput gridInput, DataRow readera)
    {
        switch (gridInput.RequestedTreeLevel)
        {
            case 1: return $"{readera["pk_function_code"]} {readera["function_name"]}";
            case 2: return $"{readera["level_1_id"]} {readera["level_1_description"]}";
            case 3: return $"{readera["level_2_id"]} {readera["level_2_description"]}";
            case 4: return $"{readera["level_3_id"]} {readera["level_3_description"]}";
            case 5: return $"{readera["level_4_id"]} {readera["level_4_description"]}";
            case 6: return $"{readera["level_5_id"]} {readera["level_5_description"]}";
            default: return $"{readera["pk_function_code"]} {readera["function_name"]}";
        }
    }


    private string SetIdColumMissingFunc(AccStatmentWidgetInput gridInput, DataRow readera, bool proj_setup_missing, string userId, int tenantId)
    {
        try
        {
            string functionSetupMissingId = "-00-funcMiss-" + gridInput.OrgId;

            switch (gridInput.RequestedTreeLevel)
            {
                case 1: return (Uri.EscapeDataString($"{functionSetupMissingId}"));
                case 2: return (Uri.EscapeDataString($"{functionSetupMissingId}_{readera["level_1_id"]}"));
                case 3: return (Uri.EscapeDataString($"{functionSetupMissingId}_{gridInput.level2IdExpanded}_{readera["level_2_id"]}"));
                case 4: return (Uri.EscapeDataString($"{functionSetupMissingId}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{readera["level_3_id"]}"));
                case 5: return (Uri.EscapeDataString($"{functionSetupMissingId}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{readera["level_4_id"]}"));
                case 6: return (Uri.EscapeDataString($"{functionSetupMissingId}_{gridInput.level2IdExpanded}_{gridInput.level3IdExpanded}_{gridInput.level4IdExpanded}_{gridInput.level5IdExpanded}_{readera["level_5_id"]}"));

                default: return Uri.EscapeDataString($"{readera["pk_function_code"]}");
            }
        }
        catch (Exception ex)
        {
            PerformanceLogger.LogPerfTimerBegin(userId, tenantId, $"AccStatment_SetIdColumMissingProj", "AccStatementGrid", ex.Message + "InputJson_" + JObject.FromObject(gridInput).ToString() + "CurrentDataRow_" + readera.ToString());
            return Uri.EscapeDataString($"{readera["org_id_" + (gridInput.OrgLevel + 1)]}");
        }
    }


    private string SetNamePropertyMissingFunc(AccStatmentWidgetInput gridInput, DataRow readera, bool proj_setup_missing, Dictionary<string, clsLanguageString> langStringValues)
    {
        string functionSetupMissing = langStringValues["AccStmt_FunctionSetupMissing"].LangText;
        switch (gridInput.RequestedTreeLevel)
        {
            case 1: return (($"{functionSetupMissing}"));
            case 2: return $"{readera["level_1_id"]} {readera["level_1_description"]}";
            case 3: return $"{readera["level_2_id"]} {readera["level_2_description"]}";
            case 4: return $"{readera["level_3_id"]} {readera["level_3_description"]}";
            case 5: return $"{readera["level_4_id"]} {readera["level_4_description"]}";
            case 6: return $"{readera["level_5_id"]} {readera["level_5_description"]}";
            default: return $"{readera["pk_function_code"]} {readera["function_name"]}";
        }
    }


    private string AddTreeLevelFetchFuncView(AccStatmentWidgetInput gridInput)
    {
        StringBuilder treeLevel = new StringBuilder();

        if (gridInput.isMissingProjectExpanded)
        {
            switch (gridInput.RequestedTreeLevel)
            {

                case 3: treeLevel.Append(" and rl.level_1_id = @Level2Id"); break;
                case 4: treeLevel.Append(" and rl.level_1_id = @Level2Id and rl.level_2_id = @Level3Id"); break;
                case 5: treeLevel.Append(" and rl.level_1_id = @Level2Id and rl.level_2_id = @Level3Id and rl.level_3_id = @Level4Id"); break;
                case 6: treeLevel.Append(" and rl.level_1_id = @Level2Id and rl.level_2_id = @Level3Id and rl.level_3_id = @Level4Id and rl.level_4_id = @Level5Id"); break;
            }
        }
        else
        {
            switch (gridInput.RequestedTreeLevel)
            {
                case 2: treeLevel.Append(" and sv.pk_function_code=@Level1Id"); break;
                case 3: treeLevel.Append(" and sv.pk_function_code= @Level1Id and rl.level_1_id = @Level2Id"); break;
                case 4: treeLevel.Append(" and sv.pk_function_code= @Level1Id and rl.level_1_id = @Level2Id and rl.level_2_id = @Level3Id"); break;
                case 5: treeLevel.Append(" and sv.pk_function_code= @Level1Id and rl.level_1_id = @Level2Id and rl.level_2_id = @Level3Id and rl.level_3_id = @Level4Id"); break;
                case 6: treeLevel.Append(" and sv.pk_function_code= @Level1Id and rl.level_1_id = @Level2Id and rl.level_2_id = @Level3Id and rl.level_3_id = @Level4Id and rl.level_4_id = @Level5Id"); break;
            }
        }
        return treeLevel.ToString();
    }


    private string GetAccountingDataSetQueryMissingDept_ExcelExport(bool serviceSetUp, AccountStmtExcelExportInput gridInput, int tenantId, string userId, int accountLevel)
    {
        //Filter conditions
        string orgLevel_valid = ValidateOrgLevel(gridInput.orgLevel);
        string columnTofetch = GetLevelIdAndDescColumnMissingDept_OrgeSetup(gridInput, accountLevel);
        string partictionColumn = GetValidPartitionColumnMissingDept_OrgeSetup(gridInput, accountLevel);
        string accountingQuery = $"with accountingQuery as " +
                                 $"(" +
                                 $"select " + columnTofetch +
                                 $",a.[period] as period, a.[gl_year] as budget_year, a.amount as accounting " +
                                 $",department_setup_missing = CASE WHEN dp.pk_department_code IS NULL THEN 1 ELSE 0 END," +
                                 $"org_setup_missing = CASE WHEN oh.fk_department_code IS NULL THEN 1 ELSE 0 END " +
                                 $"FROM [dbo].tfp_accounting_data a " +
                                 $"left JOIN[dbo].[tco_org_hierarchy] oh on a.fk_tenant_id = oh.fk_Tenant_id  and @OrgVersion = oh.fk_org_version and a.department_code = oh.fk_department_code    " +
                                 $"left JOIN[dbo].[tco_departments] dp on  oh.fk_department_code=dp.pk_department_code  and oh.fk_tenant_id=dp.fk_tenant_id and  dp.year_from<=@BudgetYear  and @BudgetYear <=dp.year_to  " +
                                 $"JOIN[dbo].[tco_accounts] ac ON  a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code  AND " +
                                 $"@BudgetYear >= YEAR(ac.dateFrom) and @BudgetYear <= YEAR(ac.dateTo) " +
                                 $" JOIN [dbo].[gco_kostra_accounts] kc ON ac.fk_kostra_account_code = kc.pk_kostra_account_code AND kc.type='operations' ";

        if (serviceSetUp)
        {
            accountingQuery = accountingQuery +
                              $"LEFT JOIN[dbo].[tco_service_values] sv ON a.fk_function_code = sv.fk_function_code  and  a.fk_tenant_id = sv.fk_tenant_id " +
                              $" LEFT JOIN[dbo].[tco_functions] fp on  sv.fk_tenant_id=fp.pk_tenant_id and sv.fk_function_code=fp.pk_Function_code  and fp.dateFrom<=@BudgetYear and @BudgetYear<=fp.dateTo  ";
        }

        accountingQuery = accountingQuery +
                          $"LEFT JOIN[dbo].[tmd_reporting_line] rl ON a.fk_tenant_id = rl.fk_tenant_id and rl.report = 'MNDRAPP' and a.fk_account_code = rl.fk_account_code " +
                          $"WHERE  a.fk_tenant_id = @TenantId and a.gl_year IN((@BudgetYear - 1), (@BudgetYear))  and ac.fk_kostra_account_code NOT IN('1580', '1980') " +
                          $" and (dp.pk_department_code IS NULL or oh.fk_department_code IS NULL)  ";// selected orgIdFilter
        //   accountingQuery = AddTreeLevelFetchMissingDept_OrgSetup_ExcelExport(accountingQuery, gridInput);// add the which level data needs to be fetched

        //if (!string.IsNullOrEmpty(filterQuery))
        //{
        //    accountingQuery = accountingQuery + filterQuery;
        //}

        accountingQuery = accountingQuery +
                          $"), " +
                          $"orgLevelData as " +
                          $"(" +
                          $"select " + columnTofetch +// this will contain the which level id and level desc to be fetched
                          $",period, budget_year, department_setup_missing,org_setup_missing, sum(accounting) acc " +
                          $"from accountingQuery " +
                          $"group by " + columnTofetch +
                          $",period, budget_year, department_setup_missing,org_setup_missing" +
                          $"), " +
                          $"ytd as " +
                          $"(" +
                          $"select *, " +
                          $"sum(acc) OVER(partition by " + partictionColumn + " ,budget_year order by " + partictionColumn + " ,period Rows between unbounded preceding and current row) acc_ytd " +
                          $"from orgLevelData" +
                          $")" +
                          $"select * from ytd where (department_setup_missing=1 or org_setup_missing=1 ) and [period]<=@forcastePeriod; ";

        return accountingQuery;
    }



    private string GetBudgetDataSetQueryMissingDept_ExcelExport(bool serviceSetUp, AccountStmtExcelExportInput gridInput, bool isLocked, int tenantId, string userId, int accountLevel)
    {
        string orgLevel_valid = ValidateOrgLevel(gridInput.orgLevel);
        string columnTofetch = GetLevelIdAndDescColumnMissingDept_OrgeSetup(gridInput, accountLevel);
        string partictionColumn = GetValidPartitionColumnMissingDept_OrgeSetup(gridInput, accountLevel);
        string budgetQuery = $"with base as " +
                             $"(" +
                             $"select " + columnTofetch +
                             $" ,a.[period] as period, a.[budget_year] as budget_year, a.amount_year_1 as budget " +
                             $",department_setup_missing = CASE WHEN dp.pk_department_code IS NULL THEN 1 ELSE 0 END," +
                             $"org_setup_missing = CASE WHEN oh.fk_department_code IS NULL THEN 1 ELSE 0 END " +
                             $"FROM[dbo].[tbu_trans_detail] a " +
                             $"left JOIN[dbo].[tco_org_hierarchy] oh on a.fk_tenant_id = oh.fk_Tenant_id and @OrgVersion = oh.fk_org_version and a.department_code = oh.fk_department_code    " +
                             $"left JOIN[dbo].[tco_departments] dp on  oh.fk_department_code=dp.pk_department_code  and oh.fk_tenant_id=dp.fk_tenant_id and  dp.year_from<=@BudgetYear  and @BudgetYear <=dp.year_to  " +
                             $"JOIN[dbo].[tco_accounts] ac " +
                             $"ON  a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code  AND " +
                             $"@BudgetYear >= YEAR(ac.dateFrom) and @BudgetYear <= YEAR(ac.dateTo) " +
                             $" JOIN [dbo].[gco_kostra_accounts] kc ON ac.fk_kostra_account_code = kc.pk_kostra_account_code AND kc.type='operations' ";

        if (serviceSetUp)
        {
            if (isLocked)
            {
                budgetQuery = budgetQuery +
                              $" join [dbo].[tco_user_adjustment_codes] ud ON a.fk_tenant_id = ud.fk_tenant_id AND a.fk_adjustment_code = ud.pk_adj_code and 1 = ud.status " +
                              $" LEFT JOIN[dbo].[tco_service_values] sv ON a.fk_function_code = sv.fk_function_code  and  a.fk_tenant_id = sv.fk_tenant_id " +
                              $" LEFT JOIN[dbo].[tco_functions] fp on  sv.fk_tenant_id=fp.pk_tenant_id and sv.fk_function_code=fp.pk_Function_code  and fp.dateFrom<=@BudgetYear and @BudgetYear <=fp.dateTo   ";
            }
            else
            {
                budgetQuery = budgetQuery +
                              $"LEFT JOIN[dbo].[tco_service_values] sv ON a.fk_function_code = sv.fk_function_code  and  a.fk_tenant_id = sv.fk_tenant_id " +
                              $" LEFT JOIN[dbo].[tco_functions] fp on  sv.fk_tenant_id=fp.pk_tenant_id and sv.fk_function_code=fp.pk_Function_code  and fp.dateFrom<=@BudgetYear and @BudgetYear <=fp.dateTo   ";
            }
        }
        else
        {
            if (isLocked)
            {
                budgetQuery = budgetQuery +
                              $"join [dbo].[tco_user_adjustment_codes] ud ON a.fk_tenant_id = ud.fk_tenant_id AND a.fk_adjustment_code = ud.pk_adj_code and 1 = ud.status ";
            }
            else
            {
                budgetQuery = budgetQuery + "";
            }
        }

        budgetQuery = budgetQuery +
                      $"LEFT JOIN[dbo].[tmd_reporting_line] rl ON a.fk_tenant_id = rl.fk_tenant_id and rl.report = 'MNDRAPP' and a.fk_account_code = rl.fk_account_code " +
                      $"WHERE  a.fk_tenant_id = @TenantId and a.budget_year IN((@BudgetYear - 1), (@BudgetYear)) and ac.fk_kostra_account_code NOT IN('1580', '1980') " +
                      $" and (dp.pk_department_code IS NULL or oh.fk_department_code IS NULL)  ";
        if (isLocked)
        {
            budgetQuery = budgetQuery + $" and ud.status = 1 ";
        }
        //Filter conditions
        //var filterQuery = BuildFilterQuery(gridInput.OrgLevel, gridInput.Filters, tenantId, userId, gridInput.BudgetYear).Result;
        //if (!string.IsNullOrEmpty(filterQuery))
        //{
        //    budgetQuery = budgetQuery + filterQuery;
        //}
        // selected orgIdFilter
        // budgetQuery = AddTreeLevelFetchMissingDept_OrgSetup_ExcelExport(budgetQuery, gridInput);// add the which level data needs to be fetched

        budgetQuery = budgetQuery +
                      $")," +
                      $"orgLevelData as " +
                      $"(" +
                      $"select " + columnTofetch +// this will contain the which level id and level desc to be fetched
                      $" ,period, budget_year, department_setup_missing,org_setup_missing, sum(budget) bud from base group by " + columnTofetch +
                      $",period, budget_year, department_setup_missing,org_setup_missing" +
                      $"), " +
                      $"ytd as " +
                      $"( " +
                      $"select *, " +
                      $"sum(bud)OVER(partition by " + partictionColumn + " ,budget_year order by " + partictionColumn + ",period Rows between unbounded preceding and current row) bud_ytd, " +
                      $"sum(bud) OVER(partition by " + partictionColumn + " ,budget_year order by " + partictionColumn + ") annual_budget " +
                      $"from orgLevelData" +
                      $") " +
                      $"select * from ytd where (department_setup_missing=1 or org_setup_missing=1 ) and [period]<=@forcastePeriod order by " + columnTofetch + ", period ;";

        return budgetQuery;
    }



    private string GetOriginalBudgetDataSetQueryMissingDept_ExcelExport(bool serviceSetUp, AccountStmtExcelExportInput gridInput, bool isLocked, int tenantId, string userId, int accountLevel)
    {
        string orgLevel_valid = ValidateOrgLevel(gridInput.orgLevel);
        string columnTofetch = GetLevelIdAndDescColumnMissingDept_OrgeSetup(gridInput, accountLevel);
        string partictionColumn = GetValidPartitionColumnMissingDept_OrgeSetup(gridInput, accountLevel);
        string budgetQuery = $"with base as " +
                             $"(" +
                             $"select " + columnTofetch +
                             $" ,a.[period] as period, a.[budget_year] as budget_year, a.amount_year_1 as budget " +
                             $",department_setup_missing = CASE WHEN dp.pk_department_code IS NULL THEN 1 ELSE 0 END," +
                             $"org_setup_missing = CASE WHEN oh.fk_department_code IS NULL THEN 1 ELSE 0 END " +
                             $"FROM[dbo].[tbu_trans_detail_original] a " +
                             $"left JOIN[dbo].[tco_org_hierarchy] oh on  a.fk_tenant_id = oh.fk_Tenant_id and @OrgVersion = oh.fk_org_version and a.department_code = oh.fk_department_code    " +
                             $"left JOIN[dbo].[tco_departments] dp on  oh.fk_department_code=dp.pk_department_code  and oh.fk_tenant_id=dp.fk_tenant_id and dp.year_from<=@BudgetYear  and @BudgetYear <=dp.year_to  " +
                             $"JOIN[dbo].[tco_accounts] ac " +
                             $"ON  a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code  AND " +
                             $"@BudgetYear >= YEAR(ac.dateFrom) and @BudgetYear <= YEAR(ac.dateTo) " +
                             $" JOIN [dbo].[gco_kostra_accounts] kc ON ac.fk_kostra_account_code = kc.pk_kostra_account_code AND kc.type='operations' ";

        if (serviceSetUp)
        {
            budgetQuery = budgetQuery +
                          $"LEFT JOIN[dbo].[tco_service_values] sv ON a.fk_function_code = sv.fk_function_code  and  a.fk_tenant_id = sv.fk_tenant_id " +
                          $" LEFT JOIN[dbo].[tco_functions] fp on  sv.fk_tenant_id=fp.pk_tenant_id and sv.fk_function_code=fp.pk_Function_code  and fp.dateFrom<=@BudgetYear and @BudgetYear <=fp.dateTo   ";
        }

        budgetQuery = budgetQuery +
                      $"LEFT JOIN[dbo].[tmd_reporting_line] rl ON a.fk_tenant_id = rl.fk_tenant_id and rl.report = 'MNDRAPP' and a.fk_account_code = rl.fk_account_code " +
                      $"WHERE  a.fk_tenant_id = @TenantId and a.budget_year IN((@BudgetYear - 1), (@BudgetYear)) and ac.fk_kostra_account_code NOT IN('1580', '1980') " +
                      $" and (dp.pk_department_code IS NULL or oh.fk_department_code IS NULL) ";

        // selected orgIdFilter
        // budgetQuery = AddTreeLevelFetchMissingDept_OrgSetup_ExcelExport(budgetQuery, gridInput);// add the which level data needs to be fetched
        //Filter conditions
        // var filterQuery = BuildFilterQuery(gridInput.OrgLevel, gridInput.Filters, tenantId, userId, gridInput.BudgetYear).Result;
        //  if (!string.IsNullOrEmpty(filterQuery))
        //  {
        //       budgetQuery = budgetQuery + filterQuery;
        //   }

        budgetQuery = budgetQuery +
                      $")," +
                      $"orgLevelData as " +
                      $"(" +
                      $"select " + columnTofetch +// this will contain the which level id and level desc to be fetched
                      $" ,period, budget_year, department_setup_missing,org_setup_missing, sum(budget) bud from base group by " + columnTofetch +
                      $",period, budget_year, department_setup_missing,org_setup_missing" +
                      $"), " +
                      $"ytd as " +
                      $"( " +
                      $"select *, " +
                      $"sum(bud)OVER(partition by " + partictionColumn + " ,budget_year order by " + partictionColumn + ",period Rows between unbounded preceding and current row) bud_ytd, " +
                      $"sum(bud) OVER(partition by " + partictionColumn + " ,budget_year order by " + partictionColumn + ") annual_budget " +
                      $"from orgLevelData" +
                      $") " +
                      $"select * from ytd where (department_setup_missing=1 or org_setup_missing=1 ) and [period]<=@forcastePeriod order by " + columnTofetch + ", period ;";

        return budgetQuery;
    }



    private string GetValidPartitionColumnMissingDept_OrgeSetup(AccountStmtExcelExportInput gridInput, int accountLevel)
    {
        string partitionColumn = string.Empty;
        partitionColumn = $"org_id_" + (gridInput.orgLevel);

        switch (accountLevel)
        {


            case 2:
                partitionColumn = partitionColumn + ",level_1_id,level_2_id"; break;
            case 3:
                partitionColumn = partitionColumn + ",level_1_id,level_2_id,level_3_id"; break;
            case 4:
                partitionColumn = partitionColumn + ",level_1_id,level_2_id,level_3_id,level_4_id"; break;
            case 5:
                partitionColumn = partitionColumn + ",level_1_id,level_2_id,level_3_id,level_4_id,level_5_id"; break;

        }
        return partitionColumn;
    }



    private string GetLevelIdAndDescColumnMissingDept_OrgeSetup(AccountStmtExcelExportInput gridInput, int accountLevel)
    {
        string columnsToFetch = string.Empty;
        columnsToFetch = $"org_Id_" + (gridInput.orgLevel) + ", org_name_" + (gridInput.orgLevel);
        switch (accountLevel)
        {


            case 2:
                columnsToFetch = columnsToFetch + ",level_1_id, level_1_description,level_1_description,level_2_id, level_2_description"; break;
            case 3:
                columnsToFetch = columnsToFetch + ",level_1_id, level_1_description,level_2_id, level_2_description,level_3_id, level_3_description"; break;
            case 4:
                columnsToFetch = columnsToFetch + ",level_1_id, level_1_description,level_2_id, level_2_description,level_3_id, level_3_description,level_4_id, level_4_description"; break;
            case 5:
                columnsToFetch = columnsToFetch + ",level_1_id, level_1_description,level_2_id, level_2_description,level_3_id, level_3_description ,level_4_id, level_4_description,level_5_id, level_5_description"; break;

        }
        return columnsToFetch;
    }



    private DataTable SetEmptyLevelsForOrgView_excelExport(DataTable dt, int accountLevel)
    {
        foreach (DataRow dr in dt.Rows)
        {

            if (1 <= accountLevel && (dr["level_1_id"] == null || Convert.ToString(dr["level_1_id"]) == string.Empty))
            {
                dr["level_1_id"] = "ZZ";
                dr["level_1_description"] = "Mangler oppsett";
            }

            if (2 <= accountLevel && (dr["level_2_id"] == null || Convert.ToString(dr["level_2_id"]) == string.Empty))
            {
                dr["level_2_id"] = "ZZ";
                dr["level_2_description"] = "Mangler oppsett";
            }

            if (3 <= accountLevel && (dr["level_3_id"] == null || Convert.ToString(dr["level_3_id"]) == string.Empty))
            {
                dr["level_3_id"] = "ZZ";
                dr["level_3_description"] = "Mangler oppsett";
            }

            if (4 <= accountLevel && (dr["level_4_id"] == null || Convert.ToString(dr["level_4_id"]) == string.Empty))
            {
                dr["level_4_id"] = "ZZ";
                dr["level_4_description"] = "Mangler oppsett";
            }

            if (5 <= accountLevel && (dr["level_5_id"] == null || Convert.ToString(dr["level_5_id"]) == string.Empty))
            {
                dr["level_5_id"] = "ZZ";
                dr["level_5_description"] = "Mangler oppsett";
            }

        }

        return dt;
    }



    private string SetIdColumMissingDept_Org_ExcelExport(AccountStmtExcelExportInput gridInput, DataRow readera, bool department_setup_missing, bool org_setup_missing, string userId, int tenantId)
    {
        try
        {
            string orgSetupMissingId = "-00-orgMiss-" + gridInput.orgLevel;
            string deptMissing = "00-deptMiss-" + $"{readera["org_id_" + (gridInput.orgLevel)]}";



            return ((department_setup_missing && org_setup_missing) ? Uri.EscapeDataString($"{orgSetupMissingId}") : Uri.EscapeDataString($"{deptMissing}"));
        }
        catch (Exception ex)
        {
            PerformanceLogger.LogPerfTimerBegin(userId, tenantId, $"AccStatment_SetIdColumMissingDept_Org_excelExport", "AccStatementGrid", ex.Message + "InputJson_" + JObject.FromObject(gridInput).ToString() + "CurrentDataRow_" + readera.ToString());
            return Uri.EscapeDataString($"{readera["org_id_" + (gridInput.orgLevel + 1)]}");
        }
    }




    private string SetNamePropertyMissingDept_Org_ExcelExport(AccountStmtExcelExportInput gridInput, DataRow readera, bool department_setup_missing, bool org_setup_missing)
    {
        string orgSetupMissingId = "Org.oppsett mangler";
        string deptMissing = "- Ugyldig ansvar";

        return ((department_setup_missing && org_setup_missing) ? ($"{orgSetupMissingId}") : ($"{deptMissing}"));
    }




    private string GetAccountingDataSetQueryOrgView_ExcelExport(bool serviceSetUp, AccountStmtExcelExportInput gridInput, int tenantId, string userId, int accountLevel)
    {
        string orgLevel_valid = ValidateOrgLevel(gridInput.orgLevel);
        string columnTofetch = GetLevelIdAndDescColumnOrgView(gridInput, accountLevel);
        string partictionColumn = GetValidPartitionColumnOrgView(gridInput, accountLevel);
        string budgetYear = "(@BudgetYear, (@BudgetYear - 1))";
        if (gridInput.isPerPeriodGrid)
        {
            budgetYear = "(@BudgetYear)";
        }
        string accountingQuery = $"with accountingQuery as " +
                                 $"(" +
                                 $"select " + columnTofetch +
                                 $",a.[period] as period, a.[gl_year] as budget_year, a.amount as accounting " +

                                 $"FROM[dbo].tfp_accounting_data a ";
        if (gridInput.viewType == "1")
        {
            accountingQuery = accountingQuery + $"join flat_org_hierarchy_dep oh on a.fk_tenant_id = oh.fk_tenant_id and a.gl_year = oh.budget_year and a.department_code = oh.fk_department_code and oh.department_setup_missing = 0     ";
        }
        if (gridInput.viewType == "0")
        {
            accountingQuery = accountingQuery + $"inner JOIN[dbo].[tco_org_hierarchy] oh on a.fk_tenant_id = oh.fk_Tenant_id and @OrgVersion = oh.fk_org_version and a.department_code = oh.fk_department_code    ";
        }
        if (gridInput.viewType == "0" || gridInput.viewType == "0")
        { accountingQuery = accountingQuery + $"INNER JOIN[dbo].[tco_departments] dp on  oh.fk_tenant_id=dp.fk_tenant_id and oh.fk_department_code=dp.pk_department_code  and dp.year_from<=@BudgetYear  and @BudgetYear <=dp.year_to "; }

        accountingQuery = accountingQuery + $"JOIN[dbo].[tco_accounts] ac ON  a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code  AND " +
                          $"@BudgetYear >= YEAR(ac.dateFrom) and @BudgetYear <= YEAR(ac.dateTo) " +
                          $" JOIN [dbo].[gco_kostra_accounts] kc ON ac.fk_kostra_account_code = kc.pk_kostra_account_code AND kc.type='operations' ";

        if (serviceSetUp)
        {
            accountingQuery = accountingQuery +
                              $"LEFT JOIN[dbo].[tco_service_values] sv ON a.fk_function_code = sv.fk_function_code  and  a.fk_tenant_id = sv.fk_tenant_id " +
                              $" LEFT JOIN[dbo].[tco_functions] fp on  sv.fk_tenant_id=fp.pk_tenant_id and sv.fk_function_code=fp.pk_Function_code  and fp.dateFrom<=@BudgetYear and @BudgetYear<=fp.dateTo  ";
        }

        accountingQuery = accountingQuery +
                          $"LEFT JOIN[dbo].[tmd_reporting_line] rl ON a.fk_tenant_id = rl.fk_tenant_id and rl.report = 'MNDRAPP' and a.fk_account_code = rl.fk_account_code " +
                          $"WHERE  a.fk_tenant_id = @TenantId and a.gl_year IN" + budgetYear + " and ac.fk_kostra_account_code NOT IN('1580', '1980') " +
                          $" and oh.{orgLevel_valid}=@orgId ";// selected orgIdFilter
        //accountingQuery = (gridInput.viewType == "0" ? AddTreeLevelFetchOrgView(accountingQuery, gridInput) : AddTreeLevelFetchOrgView_NextLevel(accountingQuery, gridInput));// add the which level data needs to be fetched
        //Filter conditions
        //var filterQuery = BuildFilterQuery(gridInput.OrgLevel, gridInput.Filters, tenantId, userId, gridInput.BudgetYear).Result;
        //if (!string.IsNullOrEmpty(filterQuery))
        //{
        //    accountingQuery = accountingQuery + filterQuery;
        //}
        accountingQuery = accountingQuery +
                          $"), " +
                          $"orgLevelData as " +
                          $"(" +
                          $"select " + columnTofetch +// this will contain the which level id and level desc to be fetched
                          $",period, budget_year, sum(accounting) acc " +
                          $"from accountingQuery " +
                          $"group by " + columnTofetch +
                          $",period, budget_year" +
                          $"), " +
                          $"ytd as " +
                          $"(" +
                          $"select *, " +
                          $"sum(acc) OVER(partition by " + partictionColumn + " ,budget_year order by " + partictionColumn + " ,period Rows between unbounded preceding and current row) acc_ytd " +
                          $"from orgLevelData" +
                          $")" +
                          $"select * from ytd";
        if (gridInput.viewType == "2" || gridInput.viewType == "0" || gridInput.viewType == "1")
        {
            accountingQuery = accountingQuery + $" where [period]<=@forcastePeriod  ";
        }
        accountingQuery = accountingQuery + $"  order by " + columnTofetch + ", period ;";
        return accountingQuery;
    }



    private string GetBudgetDataSetQueryOrgView_ExcelExport(bool isCurrentBudgetYear, bool serviceSetUp, AccountStmtExcelExportInput gridInput, bool isLocked, int tenantId, string userId, int accountLevel)
    {
        string orgLevel_valid = ValidateOrgLevel(gridInput.orgLevel);
        string columnTofetch = GetLevelIdAndDescColumnOrgView(gridInput, accountLevel);
        string partictionColumn = GetValidPartitionColumnOrgView(gridInput, accountLevel);

        string budgetQuery = $"with base as " +
                             $"(" +
                             $"select " + columnTofetch +
                             $" ,a.[period] as period, a.[budget_year] as budget_year, a.amount_year_1 as budget " +
                             $"FROM[dbo].[tbu_trans_detail] a ";
        if (gridInput.viewType == "1")
        {
            budgetQuery = budgetQuery + $"join flat_org_hierarchy_dep oh on a.fk_tenant_id = oh.fk_tenant_id and a.budget_year = oh.budget_year and a.department_code = oh.fk_department_code and oh.department_setup_missing = 0     ";
        }
        if (gridInput.viewType == "0")
        {
            budgetQuery = budgetQuery + $"inner JOIN [dbo].[tco_org_hierarchy] oh on  a.fk_tenant_id = oh.fk_Tenant_id and @OrgVersion = oh.fk_org_version and a.department_code = oh.fk_department_code    ";
        }
        if (gridInput.viewType == "2" || gridInput.viewType == "0")
        { budgetQuery = budgetQuery + $"INNER JOIN[dbo].[tco_departments] dp on  oh.fk_tenant_id=dp.fk_tenant_id and oh.fk_department_code=dp.pk_department_code  and dp.year_from<=@BudgetYear  and @BudgetYear <=dp.year_to "; }

        budgetQuery = budgetQuery + $"JOIN[dbo].[tco_accounts] ac " +
                      $"ON  a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code  AND " +
                      $"@BudgetYear >= YEAR(ac.dateFrom) and @BudgetYear <= YEAR(ac.dateTo) " +
                      $" JOIN [dbo].[gco_kostra_accounts] kc ON ac.fk_kostra_account_code = kc.pk_kostra_account_code AND kc.type='operations' ";

        if (serviceSetUp)
        {
            if (isLocked)
            {
                budgetQuery = budgetQuery +
                              $" join [dbo].[tco_user_adjustment_codes] ud ON a.fk_tenant_id = ud.fk_tenant_id AND a.fk_adjustment_code = ud.pk_adj_code and 1 = ud.status " +
                              $" LEFT JOIN[dbo].[tco_service_values] sv ON a.fk_function_code = sv.fk_function_code  and  a.fk_tenant_id = sv.fk_tenant_id " +
                              $" LEFT JOIN[dbo].[tco_functions] fp on  sv.fk_tenant_id=fp.pk_tenant_id and sv.fk_function_code=fp.pk_Function_code  and fp.dateFrom<=@BudgetYear and @BudgetYear <=fp.dateTo   ";
            }
            else
            {
                budgetQuery = budgetQuery +
                              $"LEFT JOIN[dbo].[tco_service_values] sv ON a.fk_function_code = sv.fk_function_code  and  a.fk_tenant_id = sv.fk_tenant_id " +
                              $" LEFT JOIN[dbo].[tco_functions] fp on  sv.fk_tenant_id=fp.pk_tenant_id and sv.fk_function_code=fp.pk_Function_code  and fp.dateFrom<=@BudgetYear and @BudgetYear <=fp.dateTo   ";
            }
        }
        else
        {
            if (isLocked)
            {
                budgetQuery = budgetQuery +
                              $"join [dbo].[tco_user_adjustment_codes] ud ON a.fk_tenant_id = ud.fk_tenant_id AND a.fk_adjustment_code = ud.pk_adj_code and 1 = ud.status ";
            }
            else
            {
                budgetQuery = budgetQuery + "";
            }
        }

        if (isCurrentBudgetYear)
        {
            budgetQuery = budgetQuery +
                          $"LEFT JOIN[dbo].[tmd_reporting_line] rl ON a.fk_tenant_id = rl.fk_tenant_id and rl.report = 'MNDRAPP' and a.fk_account_code = rl.fk_account_code " +
                          $"WHERE  a.fk_tenant_id = @TenantId and a.budget_year IN(@BudgetYear) and ac.fk_kostra_account_code NOT IN('1580', '1980') " +
                          $" and oh.{orgLevel_valid}=@orgId ";
        }
        else
        {
            budgetQuery = budgetQuery +
                          $"LEFT JOIN[dbo].[tmd_reporting_line] rl ON a.fk_tenant_id = rl.fk_tenant_id and rl.report = 'MNDRAPP' and a.fk_account_code = rl.fk_account_code " +
                          $"WHERE  a.fk_tenant_id = @TenantId and a.budget_year IN((@BudgetYear - 1)) and ac.fk_kostra_account_code NOT IN('1580', '1980') " +
                          $" and oh.{orgLevel_valid}=@orgId ";
        }

        //var filterQuery = BuildFilterQuery(gridInput.OrgLevel, gridInput.Filters, tenantId, userId, gridInput.BudgetYear).Result;
        //if (!string.IsNullOrEmpty(filterQuery))
        //{
        //    budgetQuery = budgetQuery + filterQuery;
        //}

        //// selected orgIdFilter
        //budgetQuery = (gridInput.viewType == "0" ? AddTreeLevelFetchOrgView(budgetQuery, gridInput) : AddTreeLevelFetchOrgView_NextLevel(budgetQuery, gridInput));// add the which level data needs to be fetched

        budgetQuery = budgetQuery +
                      $")," +
                      $"orgLevelData as " +
                      $"(" +
                      $"select " + columnTofetch +// this will contain the which level id and level desc to be fetched
                      $" ,period, budget_year, sum(budget) bud from base group by " + columnTofetch +
                      $",period, budget_year" +
                      $"), " +
                      $"ytd as " +
                      $"( " +
                      $"select *, " +
                      $"sum(bud)OVER(partition by " + partictionColumn + " ,budget_year order by " + partictionColumn + ",period Rows between unbounded preceding and current row) bud_ytd, " +
                      $"sum(bud) OVER(partition by " + partictionColumn + " ,budget_year order by " + partictionColumn + ") annual_budget " +
                      $"from orgLevelData" +
                      $") " +
                      $"select * from ytd ";
        if (gridInput.viewType == "2" || gridInput.viewType == "0" || gridInput.viewType == "1")
        {
            budgetQuery = budgetQuery + $" where [period]<=@forcastePeriod";
        }
        budgetQuery = budgetQuery + $" order by " + columnTofetch + ", period ;";

        return budgetQuery;
    }



    private string GetOriginalBudgetDataSetQueryOrgView_ExcelExport(bool serviceSetUp, AccountStmtExcelExportInput gridInput, bool isLocked, int tenantId, string userId, int accountLevel)
    {
        string orgLevel_valid = ValidateOrgLevel(gridInput.orgLevel);
        string columnTofetch = GetLevelIdAndDescColumnOrgView(gridInput, accountLevel);
        string partictionColumn = GetValidPartitionColumnOrgView(gridInput, accountLevel);
        string budgetYear = "(@BudgetYear, (@BudgetYear - 1))";
        if (gridInput.isPerPeriodGrid)
        {
            budgetYear = "(@BudgetYear)";
        }
        string budgetQuery = $"with base as " +
                             $"(" +
                             $"select " + columnTofetch +
                             $" ,a.[period] as period, a.[budget_year] as budget_year, a.amount_year_1 as budget " +
                             $"FROM[dbo].[tbu_trans_detail_original] a ";
        if (gridInput.viewType == "1")
        {
            budgetQuery = budgetQuery + $"join flat_org_hierarchy_dep oh on a.fk_tenant_id = oh.fk_tenant_id and a.budget_year = oh.budget_year and a.department_code = oh.fk_department_code and oh.department_setup_missing = 0     ";
        }
        if (gridInput.viewType == "0")
        {
            budgetQuery = budgetQuery + $"inner JOIN [dbo].[tco_org_hierarchy] oh on  a.fk_tenant_id = oh.fk_Tenant_id and @OrgVersion = oh.fk_org_version and a.department_code = oh.fk_department_code    ";
        }
        if (gridInput.viewType == "2" || gridInput.viewType == "0")
        {
            budgetQuery = budgetQuery + $"INNER JOIN[dbo].[tco_departments] dp on  oh.fk_tenant_id=dp.fk_tenant_id and oh.fk_department_code=dp.pk_department_code  and dp.year_from<=@BudgetYear  and @BudgetYear <=dp.year_to ";
        }

        budgetQuery = budgetQuery + $"JOIN[dbo].[tco_accounts] ac " +
                      $"ON  a.fk_tenant_id = ac.pk_tenant_id AND a.fk_account_code = ac.pk_account_code  AND " +
                      $"@BudgetYear >= YEAR(ac.dateFrom) and @BudgetYear <= YEAR(ac.dateTo) " +
                      $" JOIN [dbo].[gco_kostra_accounts] kc ON ac.fk_kostra_account_code = kc.pk_kostra_account_code AND kc.type='operations' ";

        if (serviceSetUp)
        {
            budgetQuery = budgetQuery +
                          $"LEFT JOIN[dbo].[tco_service_values] sv ON a.fk_function_code = sv.fk_function_code  and  a.fk_tenant_id = sv.fk_tenant_id " +
                          $" LEFT JOIN[dbo].[tco_functions] fp on  sv.fk_tenant_id=fp.pk_tenant_id and sv.fk_function_code=fp.pk_Function_code  and fp.dateFrom<=@BudgetYear and @BudgetYear <=fp.dateTo   ";
        }

        budgetQuery = budgetQuery +
                      $"LEFT JOIN[dbo].[tmd_reporting_line] rl ON a.fk_tenant_id = rl.fk_tenant_id and rl.report = 'MNDRAPP' and a.fk_account_code = rl.fk_account_code " +
                      $"WHERE  a.fk_tenant_id = @TenantId and a.budget_year IN " + budgetYear + "  and ac.fk_kostra_account_code NOT IN('1580', '1980') " +
                      $" and oh.{orgLevel_valid}=@orgId ";

        // selected orgIdFilter
        //   budgetQuery = (gridInput.viewType == "0" ? AddTreeLevelFetchOrgView(budgetQuery, gridInput) : AddTreeLevelFetchOrgView_NextLevel(budgetQuery, gridInput));// add the which level data needs to be fetched

        //var filterQuery = BuildFilterQuery(gridInput.OrgLevel, gridInput.Filters, tenantId, userId, gridInput.BudgetYear).Result;
        //if (!string.IsNullOrEmpty(filterQuery))
        //{
        //    budgetQuery = budgetQuery + filterQuery;
        //}

        budgetQuery = budgetQuery +
                      $")," +
                      $"orgLevelData as " +
                      $"(" +
                      $"select " + columnTofetch +// this will contain the which level id and level desc to be fetched
                      $" ,period, budget_year, sum(budget) bud from base group by " + columnTofetch +
                      $",period, budget_year" +
                      $"), " +
                      $"ytd as " +
                      $"( " +
                      $"select *, " +
                      $"sum(bud)OVER(partition by " + partictionColumn + " ,budget_year order by " + partictionColumn + ",period Rows between unbounded preceding and current row) bud_ytd, " +
                      $"sum(bud) OVER(partition by " + partictionColumn + " ,budget_year order by " + partictionColumn + ") annual_budget " +
                      $"from orgLevelData" +
                      $") " +
                      $"select * from ytd ";
        if (gridInput.viewType == "2" || gridInput.viewType == "0" || gridInput.viewType == "1")
        {
            budgetQuery = budgetQuery + $" where [period]<=@forcastePeriod";
        }
        budgetQuery = budgetQuery + $" order by " + columnTofetch + ", period ;";

        return budgetQuery;
    }



    private string GetLevelIdAndDescColumnOrgView(AccountStmtExcelExportInput gridInput, int accountLevel)
    {
        string columnsToFetch = string.Empty;
        if (gridInput.viewType == "0")
        {
            columnsToFetch = $"org_Id_" + (gridInput.orgLevel + 1) + ", org_name_" + (gridInput.orgLevel + 1) + ",";
        }
        else if (gridInput.viewType == "2")
        {

            columnsToFetch = $"org_Id_" + (gridInput.orgLevel + 1) + ", org_name_" + (gridInput.orgLevel + 1) + "org_Id_" + (gridInput.orgLevel + 2) + ", org_name_" + (gridInput.orgLevel + 2) + ",";


        }
        //",level_1_id, level_1_description,level_2_id, level_2_description,level_3_id, level_3_description ,level_4_id, level_4_description,level_5_id, level_5_description"

        switch (accountLevel)
        {
            case 1:
                columnsToFetch = columnsToFetch + "level_1_id, level_1_description"; break;

            case 2:
                columnsToFetch = columnsToFetch + "level_1_id, level_1_description,level_2_id, level_2_description"; break;
            case 3:
                columnsToFetch = columnsToFetch + "level_1_id, level_1_description,level_2_id, level_2_description,level_3_id, level_3_description"; break;
            case 4:
                columnsToFetch = columnsToFetch + "level_1_id, level_1_description,level_2_id, level_2_description,level_3_id, level_3_description,level_4_id, level_4_description"; break;
            case 5:
                columnsToFetch = columnsToFetch + "level_1_id, level_1_description,level_2_id, level_2_description,level_3_id, level_3_description ,level_4_id, level_4_description,level_5_id, level_5_description"; break;

        }
        return columnsToFetch;

    }



    private string GetValidPartitionColumnOrgView(AccountStmtExcelExportInput gridInput, int accountLevel)
    {
        string partitionColumn = string.Empty;
        if (gridInput.viewType == "0")
        {
            partitionColumn = $"org_id_" + (gridInput.orgLevel + 1) + ",";

        }
        else if (gridInput.viewType == "2")
        {

            partitionColumn = $"org_id_" + (gridInput.orgLevel + 1) + ",org_id_" + (gridInput.orgLevel + 2) + ",";

        }
        switch (accountLevel)
        {

            case 1:
                partitionColumn = partitionColumn + "level_1_id"; break;
            case 2:
                partitionColumn = partitionColumn + "level_1_id,level_2_id"; break;
            case 3:
                partitionColumn = partitionColumn + "level_1_id,level_2_id,level_3_id"; break;
            case 4:
                partitionColumn = partitionColumn + "level_1_id,level_2_id,level_3_id,level_4_id"; break;
            case 5:
                partitionColumn = partitionColumn + "level_1_id,level_2_id,level_3_id,level_4_id,level_5_id"; break;

        }
        return partitionColumn;

    }

}