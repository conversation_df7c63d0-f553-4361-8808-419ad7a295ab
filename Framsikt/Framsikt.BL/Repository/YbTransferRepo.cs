#pragma warning disable CS8629
#pragma warning disable CS8604
#pragma warning disable CS8603
#pragma warning disable CS8601

using EntityFrameworkExtras.EFCore;
using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Framsikt.Entities.Core;
using Microsoft.EntityFrameworkCore;

namespace Framsikt.BL.Repository
{
    public class YbTransferRepo : IYbTransferRepo
    {
        private readonly TenantDBContext _tenantDBContext;

        // Static readonly timezone field for Central European Time, with fallback to Europe/Berlin
        private static readonly TimeZoneInfo CentralEuropeanTimeZone = InitCentralEuropeanTimeZone();

        private static TimeZoneInfo InitCentralEuropeanTimeZone()
        {
            try
            {
                return TimeZoneInfo.FindSystemTimeZoneById("Central European Standard Time");
            }
            catch (TimeZoneNotFoundException)
            {
                try
                {
                    return TimeZoneInfo.FindSystemTimeZoneById("Europe/Berlin");
                }
                catch
                {
                    return TimeZoneInfo.Utc;
                }
            }
        }

        public YbTransferRepo(IDbContextManager dbContextManager)
        {
            _tenantDBContext = dbContextManager.GetTenantDbContext();
        }

        public async Task<List<YBApprovedBudAdjustmentsHelper>> ApprovedBudAdjInvestmentsData(int tenantId, int budgetYear, List<string> orgIds, string orgVersion)
        {
            _tenantDBContext.Database.SetCommandTimeout(800);
            var data = await (from t1 in _tenantDBContext.vw_tfp_sync_proj_warehouse
                              join t2 in _tenantDBContext.tco_sync_company_setup on new { a = t1.fk_tenant_id, b = t1.sub_tenant_id } equals new { a = t2.fk_tenant_id, b = t2.sub_tenant_id }
                              join t3 in _tenantDBContext.gco_tenants on new { a = t1.sub_tenant_id } equals new { a = t3.pk_id }
                              join t7 in _tenantDBContext.tfp_sync_background_job_status on new { a = t1.fk_tenant_id, b = t1.sub_tenant_id, c = t1.budget_year, e = BudgetProcessStage.YB_AppBudAdj_Investment.ToString() } equals
                                                                                            new { a = t7.main_tenant_id, b = t7.sub_tenant_id, c = t7.budget_year, e = t7.process_stage } into grp8
                              from grp9 in grp8.DefaultIfEmpty()
                              join btj in _tenantDBContext.tfp_sync_transfer_job_status.Where(x => x.step == ProcessTransferStep.Investment.ToString()) on new { a = grp9.pk_job_id } equals new { a = btj.fk_job_id } into gBtj
                              from grpBtj in gBtj.DefaultIfEmpty()
                              join br in _tenantDBContext.tfp_sync_budget_round on new { a = t1.fk_tenant_id, b = t1.sub_tenant_id, c = t1.budget_year, d = BudgetProcessStage.YB_AppBudAdj_Investment.ToString() }
                                                                                equals new { a = br.fk_tenant_id, b = br.sub_tenant_id, c = br.budget_year, d = br.process_stage } into grp
                              from grp1 in grp.DefaultIfEmpty()
                              join t4 in _tenantDBContext.tfp_budget_changes on new { a = grp1.sub_change_id, b = grp1.sub_tenant_id, c = grp1.budget_year } equals
                                                                                new { a = t4.pk_change_id, b = t4.fk_tenant_id, c = t4.budget_year } into grp2
                              from grp3 in grp2.DefaultIfEmpty()
                              join t5 in _tenantDBContext.tco_users on new { a = grp9.updated_by } equals
                                                                      new { a = t5.pk_id } into grp4
                              from grp10 in grp4.DefaultIfEmpty()

                              where t1.fk_tenant_id == tenantId && t1.budget_year == budgetYear && t2.status == 1 &&
                                    t2.fk_org_version == orgVersion && orgIds.Contains(t2.org_id)
                              select new YBApprovedBudAdjustmentsHelper
                              {
                                  subTenantId = t1.sub_tenant_id,
                                  subTenant = t3.tenant_name,
                                  originalFPAmount = (decimal)(t1.fp_year_1_amt_main_original != null ? Math.Round((decimal)t1.fp_year_1_amt_main_original / 1000, 0) : 0),
                                  adjustedFPAmtMain = (decimal)(t1.fp_year_1_amt_main_revised != null ? Math.Round((decimal)t1.fp_year_1_amt_main_revised / 1000, 0) : 0),
                                  adjustedFPAmtSub = (decimal)(t1.fp_year_1_amt_sub != null ? Math.Round((decimal)t1.fp_year_1_amt_sub / 1000, 0) : 0),
                                  adjustedFPAmtDeviation = (t1.fp_year_1_amt_main_revised != null && t1.fp_year_1_amt_sub != null) ? Math.Round(((decimal)t1.fp_year_1_amt_main_revised - (decimal)t1.fp_year_1_amt_sub) / 1000, 0).ToString() : "0",
                                  isAdjustedFPAmtDeviation = ((t1.fp_year_1_amt_main_revised != null && t1.fp_year_1_amt_sub != null) ? Math.Round(((decimal)t1.fp_year_1_amt_main_revised - (decimal)t1.fp_year_1_amt_sub) / 1000, 0) : 0) != 0,
                                  transferStatus = grpBtj == null ? grp9.job_status : grpBtj.job_status,
                                  updatedBy = (grp10.first_name != null && grp10.last_name != null) ? (grp10.first_name + " " + grp10.last_name) : string.Empty,
                                  updated = grp9.updated,
                                  jobId = grpBtj != null ? grpBtj.fk_job_id.ToString() : string.Empty,
                                  budgetRoundName = String.IsNullOrEmpty(grp3.budget_name) ? grp1.sub_change_name ?? String.Empty : grp3.budget_name,
                                  approvalRef = String.IsNullOrEmpty(grp3.approval_reference) ? grp1.sub_approval_reference ?? String.Empty : grp3.approval_reference,
                                  mainOrgIdThree = t2.org_id
                              }).ToListAsync();
            //group the data
            var finalData = data.GroupBy(x => new { x.subTenantId, x.subTenant, x.transferStatus, x.jobId, x.budgetRoundName, x.mainOrgIdThree, x.approvalRef, x.updatedBy, x.updated }).Select(x => new YBApprovedBudAdjustmentsHelper()
            {
                subTenantId = x.Key.subTenantId,
                subTenant = x.Key.subTenant,
                originalFPAmount = x.Sum(z => z.originalFPAmount),
                adjustedFPAmtMain = x.Sum(z => z.adjustedFPAmtMain),
                adjustedFPAmtSub = x.Sum(z => z.adjustedFPAmtSub),
                adjustedFPAmtDeviation = Math.Round(((x.Sum(z => z.adjustedFPAmtMain)) - (x.Sum(z => z.adjustedFPAmtSub))), 0).ToString(),
                isAdjustedFPAmtDeviation = ((x.Sum(z => z.adjustedFPAmtMain)) - (x.Sum(z => z.adjustedFPAmtSub))) != 0,
                transferStatus = x.Key.transferStatus,
                updatedBy = x.Key.updatedBy,
                updated = x.Key.updated,
                jobId = x.Key.jobId,
                budgetRoundName = x.Key.budgetRoundName,
                approvalRef = x.Key.approvalRef,
                mainOrgIdThree = x.Key.mainOrgIdThree
            }).OrderBy(x => x.subTenant).ToList();
            finalData = finalData.GroupBy(x => x.subTenant).Select(z => z.First()).ToList();
            return finalData;
        }

        public async Task<List<YbOriginalBudHelper>> OriginalBudData(int tenantId, int budgetYear, List<int> subTenantLst, string orgVersion)
        {
            var originalBudget = await (from t1 in _tenantDBContext.tbu_sync_original_budget
                                        join t3 in _tenantDBContext.gco_tenants on new { a = t1.sub_tenant_id } equals new { a = t3.pk_id }
                                        join t4 in _tenantDBContext.tfp_sync_background_job_status on new { a = t1.fk_tenant_id, b = t1.sub_tenant_id, c = t1.budget_year, e = BudgetProcessStage.YB_OriginalBudget.ToString() } equals
                                                                                                   new { a = t4.main_tenant_id, b = t4.sub_tenant_id, c = t4.budget_year, e = t4.process_stage }
                                        into grp
                                        from res in grp.DefaultIfEmpty()
                                        join t5 in _tenantDBContext.tco_users on new { a = res.updated_by } equals
                                                                                    new { a = t5.pk_id } into grp4
                                        from res1 in grp4.DefaultIfEmpty()
                                        where t1.fk_tenant_id == tenantId && t1.budget_year == budgetYear && subTenantLst.Contains(t1.sub_tenant_id)
                                        select new YbOriginalBudHelper
                                        {
                                            SubTenantId = t1.sub_tenant_id,
                                            SubTenant = t3.tenant_name,
                                            MainFpAmountYrOne = t1.finplan_amt_main,
                                            SubFpAmountYrOne = t1.finplan_amt_sub,
                                            DeviationFpAmounts = (t1.finplan_amt_main - t1.finplan_amt_sub).ToString(),
                                            SubDetailedYb = t1.org_bud_sub,
                                            JobId = res != null ? res.pk_job_id.ToString() : string.Empty,
                                            OrgLevel = 3,
                                            DeviationFpYb = (t1.finplan_amt_sub - t1.org_bud_sub).ToString(),
                                            BudLocked = string.Empty,
                                            MainDetailedYb = res != null && res.job_status == "Completed" ? t1.org_bud_main : 0,
                                            TransferStatus = res != null ? res.job_status : string.Empty,
                                            UpdatedBy = (res1.first_name != null && res1.last_name != null) ? (res1.first_name + " " + res1.last_name) : string.Empty,
                                            Updated = res != null ? res.updated : null
                                        }).OrderBy(x => x.SubTenant).ToListAsync();

            var originalBudgetWithDeviation = originalBudget.Select(x => new YbOriginalBudHelper
            {
                SubTenantId = x.SubTenantId,
                SubTenant = x.SubTenant,
                MainFpAmountYrOne = x.MainFpAmountYrOne,
                SubFpAmountYrOne = x.SubFpAmountYrOne,
                DeviationFpAmounts = x.DeviationFpAmounts,
                SubDetailedYb = x.SubDetailedYb,
                JobId = x.JobId,
                OrgLevel = x.OrgLevel,
                DeviationFpYb = x.DeviationFpYb,
                BudLocked = x.BudLocked,
                MainDetailedYb = x.MainDetailedYb,
                TransferStatus = x.TransferStatus,
                IsFpAmtDeviation = x.DeviationFpAmounts == "0.00" ? false : true,
                IsFpYbAmtDeviation = x.DeviationFpYb == "0.00" ? false : true,
                UpdatedBy = x.UpdatedBy,
                Updated = x.Updated
            }).ToList();
            return originalBudgetWithDeviation;
        }

        public IQueryable<tbu_trans_detail_original> GetYBOriginalDataToTransfer(int subTenantId, int budgetYear, List<string> deptCodeList, bool isChaterSetup)
        {
            var query = _tenantDBContext.tbu_trans_detail_original.AsNoTracking().Where(x => x.fk_tenant_id == subTenantId && x.budget_year == budgetYear);
            if (isChaterSetup && deptCodeList.Count > 0)
            {
                query = query.Where(x => deptCodeList.Contains(x.department_code));
            }
            return query;
        }

        public async Task TransferYBData(int mainTenantId, int budgetYear, int subTenantId, IQueryable<tbu_trans_detail_original> dataToTransfer, int pkId, List<string> defaultDeptCodes,
                                            string orgVersion, bool isChapterSetup, List<TransferInvHelper> departData, string adjCode)
        {
            var rec = await _tenantDBContext.tco_parameters
                        .FirstOrDefaultAsync(x => x.fk_tenant_id == subTenantId && x.param_name == "YB_TRANSFER_DEFAULT_FREEDIM_2");
            string freedim2 = rec?.param_value ?? string.Empty;

            // Delete old data from main tenant
            await DeleteOriginalBudget(mainTenantId, defaultDeptCodes, budgetYear);

            var groupedData = await (from df in dataToTransfer
                                     group df by new
                                     {
                                         df.action_type,
                                         df.line_order,
                                         df.budget_type,
                                         df.tax_flag,
                                         df.holiday_flag,
                                         df.fk_pension_type,
                                         df.fk_account_code,
                                         df.fk_function_code,
                                         df.department_code,
                                         df.period
                                     } into grp
                                     select new YBTransferData
                                     {
                                         fk_account_code = grp.Key.fk_account_code,
                                         department_code = grp.Key.department_code,
                                         fk_function_code = grp.Key.fk_function_code,
                                         period = grp.Key.period,
                                         action_type = grp.Key.action_type,
                                         line_order = grp.Key.line_order,
                                         budget_type = grp.Key.budget_type,
                                         tax_flag = grp.Key.tax_flag,
                                         holiday_flag = grp.Key.holiday_flag,
                                         fk_pension_type = grp.Key.fk_pension_type,
                                         amount_year_1 = grp.Sum(x => x.amount_year_1),
                                         total_amount = grp.Sum(x => x.total_amount)
                                     }).ToListAsync();

            List<string> accountCodes = groupedData.Select(x => x.fk_account_code).Distinct().ToList();

            // Combine these as these only need to run once
            string departmentCodeNonChapter = defaultDeptCodes.FirstOrDefault() ?? string.Empty;
            bool isEmptyDefaultDeptCodes = defaultDeptCodes == null || !defaultDeptCodes.Any();
            var allNewDataForMainTenant = new List<tbu_trans_detail>();

            foreach (string accCode in accountCodes)
            {
                var dataList = groupedData.Where(x => x.fk_account_code == accCode).ToList();
                Guid newBuTransId = Guid.NewGuid();

                foreach (var item in dataList)
                {
                    var defaultDeptData = departData.FirstOrDefault(x => x.departmentCodeSubTenant == item.department_code);
                    string defaultMainDeptCode = defaultDeptData?.deptCodeMainTenant ?? string.Empty;

                    tbu_trans_detail newRow = new()
                    {
                        pk_id = Guid.NewGuid(),
                        bu_trans_id = newBuTransId,
                        fk_tenant_id = mainTenantId,
                        action_type = item.action_type,
                        line_order = item.line_order,
                        fk_account_code = item.fk_account_code,
                        department_code = isChapterSetup ? defaultMainDeptCode : departmentCodeNonChapter,
                        fk_function_code = item.fk_function_code,
                        fk_project_code = string.Empty,
                        free_dim_1 = string.Empty,
                        free_dim_2 = freedim2,
                        free_dim_3 = string.Empty,
                        free_dim_4 = string.Empty,
                        resource_id = string.Empty,
                        budget_year = budgetYear,
                        period = item.period,
                        fk_employment_id = 0,
                        description = string.Empty,
                        budget_type = item.budget_type,
                        amount_year_1 = item.amount_year_1,
                        fk_key_id = 0,
                        allocation_pct = 0,
                        total_amount = item.total_amount,
                        tax_flag = item.tax_flag,
                        holiday_flag = item.holiday_flag,
                        fk_pension_type = item.fk_pension_type,
                        updated = DateTime.UtcNow,
                        updated_by = pkId,
                        fk_prog_code = null,
                        fk_investment_id = 0,
                        fk_portfolio_code = null,
                        fk_adjustment_code = adjCode,
                        fk_alter_code = string.Empty
                    };

                    allNewDataForMainTenant.Add(newRow);
                }
            }

            if (allNewDataForMainTenant.Any())
            {
                await _tenantDBContext.tbu_trans_detail.BulkInsertAsync(allNewDataForMainTenant);
                await _tenantDBContext.BulkSaveChangesAsync();
            }
        }

        public void ExecuteOrginalBudProcedure(int userId, int tenantId, int budgetYear)
        {
            _tenantDBContext.Database.SetCommandTimeout(300);
            var execProjProcedure = new prcSyncOrgBudWarehouse
            {
                UserId = userId,
                TenantId = tenantId,
                BudgetYear = budgetYear
            };
            _tenantDBContext.Database.ExecuteStoredProcedure(execProjProcedure);
        }

        public async Task DeleteApplicationFlagData(int subTenantId, int budgetYear, string flag)
        {
            _tenantDBContext.tco_application_flag.RemoveRange(_tenantDBContext.tco_application_flag.Where(x => x.fk_tenant_id == subTenantId && x.budget_year == budgetYear && x.flag_name == flag));
            await _tenantDBContext.SaveChangesAsync();
        }

        public async Task<tco_parameters?> GetParametersData(int subTenantId, int budgetYear, string parameterName)
        {
            return await _tenantDBContext.tco_parameters.FirstOrDefaultAsync(x => x.fk_tenant_id == subTenantId && x.param_value == budgetYear.ToString() && x.param_name == parameterName);
        }

        public async Task<List<string>> GetAdjustmentCodeData(int subTenantId, int budgetYear, string orgVersion)
        {
            var adjCodes = await (from a in _tenantDBContext.tbu_trans_detail
                                  join b in _tenantDBContext.tco_org_hierarchy on new { a = a.fk_tenant_id, b = a.department_code }
                                                                    equals new { a = b.fk_tenant_id, b = b.fk_department_code }
                                  where a.fk_tenant_id == subTenantId
                                  && a.budget_year == budgetYear
                                  && b.fk_org_version == orgVersion
                                  select a.fk_adjustment_code).Distinct().ToListAsync();
            return adjCodes;
        }

        public async Task<List<tco_user_adjustment_codes>> GetUserAdjustmentCodeData(int subTenantId, List<string> adjCodes)
        {
            return await _tenantDBContext.tco_user_adjustment_codes.Where(x => x.fk_tenant_id == subTenantId && x.inv_adj_code == false && adjCodes.Contains(x.pk_adj_code)).ToListAsync();
        }

        public async Task DeleteOriginalBudget(int tenantId, List<string> deptCodes, int budgetYear)
        {
            _tenantDBContext.Database.SetCommandTimeout(800);
            foreach (var deptCode in deptCodes)
            {
                if (await _tenantDBContext.tbu_trans_detail.AnyAsync(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.department_code == deptCode))
                {
                    await _tenantDBContext.tbu_trans_detail.Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.department_code == deptCode).DeleteFromQueryAsync();
                    await _tenantDBContext.BulkSaveChangesAsync();
                }
            }
        }

        public async Task<tbu_sync_original_budget> GetLastRefreshedDate(int tenantId, int budgetYear)
        {
            return await _tenantDBContext.tbu_sync_original_budget.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear);
        }

        public async Task<List<YbAppBudAdjActionHelper>> GetYBActionsDataToTransfer(int mainTenantId, int subTenantId, int budgetYear, string orgVersion)
        {
            return await (from th in _tenantDBContext.tfp_trans_header
                          join td in _tenantDBContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                                                                equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                          join tc in _tenantDBContext.tco_sync_company_setup on th.fk_tenant_id equals tc.fk_tenant_id
                          join tb in _tenantDBContext.tfp_budget_changes on new { a = td.fk_tenant_id, b = td.fk_change_id }
                                                            equals new { a = tb.fk_tenant_id, b = tb.pk_change_id }
                          join bp in _tenantDBContext.tco_budget_phase on new { a = tb.fk_tenant_id, b = tb.fk_budget_phase_id }
                                                                        equals new { a = bp.fk_tenant_id, b = bp.pk_budget_phase_id }
                          join uc in _tenantDBContext.tco_user_adjustment_codes on new { a = td.fk_tenant_id, b = td.fk_adj_code }
                                                            equals new { a = uc.fk_tenant_id, b = uc.pk_adj_code }
                          join ts in _tenantDBContext.tfp_sync_actions on new { a = th.fk_tenant_id, b = th.pk_action_id, c = subTenantId }
                                            equals new { a = ts.main_fk_tenant_id, b = ts.main_fk_action_id, c = ts.sub_fk_tenant_id } into tsGroup
                          from ts in tsGroup.DefaultIfEmpty()
                          where th.fk_tenant_id == mainTenantId
                            && td.budget_year == budgetYear
                            && tb.org_budget_flag == 0
                            && uc.status == true
                            && tc.fk_org_version == orgVersion
                            && ts == null
                          select new YbAppBudAdjActionHelper
                          {
                              TenantId = th.fk_tenant_id,
                              BudgetRoundName = tb.budget_name,
                              PkActionId = th.pk_action_id,
                              ChangeId = tb.pk_change_id,
                              DepartmentCode = td.department_code,
                              AdjCode = uc.pk_adj_code,
                              PkBudgetPhaseId = bp.pk_budget_phase_id
                          }).Distinct().ToListAsync();
        }

        public async Task<IEnumerable<YBInvestmentPopupDataHelper>> GetYBInvestmentDataToTransfer(int budgetYear, int mainTenantId, string orgVersion, BudgetProcessStage processStage, int subTenantId = 0)
        {
            var notTransferingInvList = new List<clsConstants.InvestmentStatus>() { clsConstants.InvestmentStatus.ParkedInv, clsConstants.InvestmentStatus.EvaluationInv, clsConstants.InvestmentStatus.DeletedInv };
            List<int> notTransferingInvStatusList = new List<int>();
            notTransferingInvList.ForEach(x => notTransferingInvStatusList.Add((int)x));
            var data = await (from mp in _tenantDBContext.tco_main_projects
                              join tp in _tenantDBContext.tco_projects on new { a = mp.fk_tenant_id, b = mp.pk_main_project_code }
                                                                   equals new { a = tp.fk_tenant_id, b = tp.fk_main_project_code }
                              join pt in _tenantDBContext.tfp_proj_transactions on new { a = tp.fk_tenant_id, b = tp.pk_project_code }
                                                                            equals new { a = pt.fk_tenant_id, b = pt.fk_project_code }
                              join ac in _tenantDBContext.tco_accounts on new { a = pt.fk_tenant_id, b = pt.fk_account_code }
                                                     equals new { a = ac.pk_tenant_id, b = ac.pk_account_code }
                              join tb in _tenantDBContext.tfp_budget_changes on new { a = pt.fk_tenant_id, b = pt.fk_change_id, c = 0 }
                                                      equals new { a = tb.fk_tenant_id, b = tb.pk_change_id, c = tb.org_budget_flag }
                              join toh in _tenantDBContext.tco_org_hierarchy on new { a = pt.fk_tenant_id, b = pt.fk_department_code, c = orgVersion } equals
                                                                  new { a = toh.fk_tenant_id, b = toh.fk_department_code, c = toh.fk_org_version }
                              join tcs in _tenantDBContext.tco_sync_company_setup on new { a = toh.fk_tenant_id, b = toh.org_id_3, c = toh.fk_org_version } equals
                                                                    new { a = tcs.fk_tenant_id, b = tcs.org_id, c = tcs.fk_org_version }
                              join tu in _tenantDBContext.tco_users on new { a = pt.updated_by } equals
                                                                          new { a = tu.pk_id }
                              join t6 in _tenantDBContext.tco_user_adjustment_codes on new { a = mp.fk_tenant_id, b = pt.fk_user_adjustment_code, c = true, d = true } equals
                                                              new { a = t6.fk_tenant_id, b = t6.pk_adj_code, c = t6.inv_adj_code, d = t6.status } into acList
                              from acLObj in acList.DefaultIfEmpty()

                              join t7 in _tenantDBContext.tco_user_adjustment_codes on new { a = tcs.sub_tenant_id, b = pt.fk_user_adjustment_code } equals new { a = t7.fk_tenant_id, b = t7.pk_adj_code } into grp5
                              from grp6 in grp5.DefaultIfEmpty()
                              join pbc in _tenantDBContext.tfp_sync_proj_budget_rounds on new { a = pt.fk_tenant_id, b = tcs.sub_tenant_id, c = pt.fk_change_id, d = processStage.ToString() } equals
                                                                                       new { a = pbc.main_fk_tenant_id, b = pbc.sub_fk_tenant_id, c = pbc.main_fk_change_id, d = pbc.process_stage } into grp9
                              from grp10 in grp9.DefaultIfEmpty()
                              where mp.fk_tenant_id == mainTenantId && mp.budget_year_from.Year <= budgetYear && mp.budget_year_to.Year >= budgetYear && grp6 == null && !notTransferingInvStatusList.Contains(mp.inv_status ?? 0) && acLObj.budget_year == budgetYear
                              group pt by new
                              {
                                  tcs.sub_tenant_id,
                                  pt.fk_user_adjustment_code,
                                  tb.pk_change_id,
                                  tb.budget_name,
                                  mp.pk_main_project_code,
                                  mp.main_project_name,
                                  ac.pk_account_code,
                                  ac.display_name,
                                  pt.fk_department_code,
                                  pt.year,
                                  pt.description,
                                  grp6.pk_adj_code,
                                  acLObj.fk_bud_auth_code,
                                  acLObj.fk_attribute_id,
                                  acLObj.case_ref,
                                  pt.fk_function_code,
                                  updatedBy = tu.first_name + " " + tu.last_name
                              } into g
                              select new YBInvestmentPopupDataHelper
                              {
                                  subTenantId = g.Key.sub_tenant_id,
                                  userAdjCode = g.Key.fk_user_adjustment_code,
                                  twinCode = g.Key.fk_attribute_id,
                                  changeId = g.Key.pk_change_id,
                                  budgetRoundName = g.Key.budget_name ?? String.Empty,
                                  mainProjCode = g.Key.pk_main_project_code,
                                  mainProjCodeName = g.Key.main_project_name,
                                  accountCode = g.Key.pk_account_code,
                                  accountCodeName = g.Key.display_name,
                                  year1Sum = g.Key.year == budgetYear ? (double)g.Sum(z => z.amount) / 1000 : 0,
                                  year2Sum = g.Key.year == budgetYear + 1 ? (double)g.Sum(z => z.amount) / 1000 : 0,
                                  year3Sum = g.Key.year == budgetYear + 2 ? (double)g.Sum(z => z.amount) / 1000 : 0,
                                  year4Sum = g.Key.year == budgetYear + 3 ? (double)g.Sum(z => z.amount) / 1000 : 0,
                                  departCode = g.Key.fk_department_code,
                                  updated = g.Max(z => z.updated),
                                  updatedBy = g.Key.updatedBy,
                                  functionCode = g.Key.fk_function_code,
                                  budAuthCode = g.Key.fk_bud_auth_code,
                                  caseNumber = g.Key.case_ref,
                                  longDesc = g.Key.description,
                                  approvedCost = g.Key.year == -1 ? (double)g.Sum(z => z.amount) / 1000 : 0,
                                  costEstimated = g.Key.year == -2 ? (double)g.Sum(z => z.amount) / 1000 : 0
                              }).ToListAsync();
            if (subTenantId != 0)
            {
                data = data.Where(x => x.subTenantId == subTenantId).ToList();
            }
            return data.AsEnumerable();
        }

        public async Task<List<tco_user_adjustment_codes>> GetUserAdjCodes(int mainTenantId, List<string> userAdjCodes, bool isInvAdj)
        {
            return await _tenantDBContext.tco_user_adjustment_codes.Where(x => x.fk_tenant_id == mainTenantId && x.status && (!isInvAdj || (isInvAdj && x.inv_adj_code)) && userAdjCodes.Contains(x.pk_adj_code)).ToListAsync();
        }

        public async Task<YbActionDetails> GetDetailsToTransfer(int subTenantId, int budgetYear, YbAppBudAdjActionHelper data)
        {
            YbActionDetails details = new YbActionDetails();
            details.ActionHeader = await _tenantDBContext.tfp_trans_header.FirstOrDefaultAsync(x => x.fk_tenant_id == data.TenantId && x.pk_action_id == data.PkActionId);

            details.ActionDetails = await _tenantDBContext.tfp_trans_detail.Where(x => x.fk_tenant_id == data.TenantId && x.fk_action_id == data.PkActionId && x.budget_year == budgetYear).ToListAsync();

            tco_user_adjustment_codes? isUserAdjCodeTrans = await _tenantDBContext.tco_user_adjustment_codes.FirstOrDefaultAsync(x => x.fk_tenant_id == subTenantId && x.budget_year == budgetYear && x.pk_adj_code == data.AdjCode);

            details.UserAdjCode = isUserAdjCodeTrans == null ? await _tenantDBContext.tco_user_adjustment_codes.FirstOrDefaultAsync(x => x.fk_tenant_id == subTenantId && x.budget_year == budgetYear && x.pk_adj_code == data.AdjCode) : new tco_user_adjustment_codes();
            details.TbuData = await _tenantDBContext.tbu_trans_detail.Where(x => x.fk_tenant_id == data.TenantId && x.budget_year == budgetYear && x.fk_action_id == data.PkActionId).ToListAsync();
            return details;
        }

        public async Task<decimal> GetCentralDeptInfo(int tenantId, int budgetYear)
        {
            var centralDeptDetails = await (from td in _tenantDBContext.tfp_trans_detail
                                            join th in _tenantDBContext.tfp_trans_header on new { a = td.fk_tenant_id, b = td.fk_action_id }
                                                                                        equals new { a = th.fk_tenant_id, b = th.pk_action_id }
                                            join bc in _tenantDBContext.tfp_budget_changes on new { a = td.fk_tenant_id, b = td.fk_change_id }
                                                                                        equals new { a = bc.fk_tenant_id, b = bc.pk_change_id }
                                            join tac in _tenantDBContext.tco_user_adjustment_codes on new { a = td.fk_tenant_id, b = td.budget_year, c = td.fk_adj_code }
                                                                                        equals new { a = tac.fk_tenant_id, b = tac.budget_year, c = tac.pk_adj_code }
                                            join p in _tenantDBContext.tco_parameters on new { a = td.fk_tenant_id, b = td.department_code }
                                                                                        equals new { a = p.fk_tenant_id, b = p.param_value }
                                            where td.fk_tenant_id == tenantId && td.budget_year == budgetYear && th.action_type == 60
                                            && p.param_name == "BUDALLOC_DEPARTMENTS" && p.active == 1
                                            select new
                                            {
                                                year1Amount = td.year_1_amount / 1000
                                            }).SumAsync(x => x.year1Amount);
            return centralDeptDetails;
        }

        public async Task<Dictionary<int, decimal>> GetCentralDeptSumBySubTenantIds(int budgetYear, List<int> subTenantIds)
        {
            var centralDeptDetails = await (from td in _tenantDBContext.tfp_trans_detail
                                            join th in _tenantDBContext.tfp_trans_header on new { a = td.fk_tenant_id, b = td.fk_action_id }
                                                                                        equals new { a = th.fk_tenant_id, b = th.pk_action_id }
                                            join bc in _tenantDBContext.tfp_budget_changes on new { a = td.fk_tenant_id, b = td.fk_change_id }
                                                                                        equals new { a = bc.fk_tenant_id, b = bc.pk_change_id }
                                            join tac in _tenantDBContext.tco_user_adjustment_codes on new { a = td.fk_tenant_id, b = td.budget_year, c = td.fk_adj_code }
                                                                                        equals new { a = tac.fk_tenant_id, b = tac.budget_year, c = tac.pk_adj_code }
                                            join p in _tenantDBContext.tco_parameters on new { a = td.fk_tenant_id, b = td.department_code }
                                                                                        equals new { a = p.fk_tenant_id, b = p.param_value }
                                            where subTenantIds.Contains(td.fk_tenant_id) && td.budget_year == budgetYear && th.action_type == 60
                                            && p.param_name == "BUDALLOC_DEPARTMENTS" && p.active == 1
                                            group td by td.fk_tenant_id into grp
                                            select new
                                            {
                                                tenantId = grp.Key,
                                                year1Amount = grp.Sum(x => x.year_1_amount / 1000)
                                            }).ToDictionaryAsync(x => x.tenantId, x => x.year1Amount);
            return centralDeptDetails;
        }

        public async Task<decimal> GetCentralDeptInfoForTransactions(int tenantId, int budgetYear)
        {
            var centralDeptDetails = await (from tp in _tenantDBContext.tfp_proj_transactions
                                            join p in _tenantDBContext.tco_parameters on new { a = tp.fk_tenant_id, b = tp.fk_department_code }
                                                                                        equals new { a = p.fk_tenant_id, b = p.param_value }
                                            join bc in _tenantDBContext.tfp_budget_changes on new { a = tp.fk_tenant_id, b = tp.fk_change_id }
                                                                                        equals new { a = bc.fk_tenant_id, b = bc.pk_change_id }
                                            where tp.fk_tenant_id == tenantId && tp.year == budgetYear
                                            && p.param_name == "BUDALLOC_DEPARTMENTS" && p.active == 1 && bc.org_budget_flag == 0
                                            select new
                                            {
                                                year1Amount = tp.amount
                                            }).ToListAsync();
            return centralDeptDetails.Sum(x => x.year1Amount);
        }

        public async Task<List<string>> GetFinancingAccountInfo(int tenantId, int budgetYear)
        {
            var financingAccounts = await (from tp in _tenantDBContext.gco_kostra_accounts
                                           where tp.type.ToLower() == "investment" && tp.income_flag == 1
                                           select new
                                           {
                                               accountCode = tp.pk_kostra_account_code
                                           }).ToListAsync();
            return financingAccounts.Select(x => x.accountCode).ToList();
        }

        public async Task<List<AdjustmentDataHelper>> BudgetAdjustmentsListData(int tenantId, int budgetYear, int subTenantId)
        {
            int periodValue = budgetYear * 100 + 1;

            var query = (from th in _tenantDBContext.tfp_trans_header.AsNoTracking() // Add AsNoTracking early
                         join td in _tenantDBContext.tfp_trans_detail.AsNoTracking() on new { a = th.fk_tenant_id, b = th.pk_action_id }
                                                     equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                         join ov in _tenantDBContext.tco_org_version.AsNoTracking() on new { a = td.fk_tenant_id }
                                                     equals new { a = ov.fk_tenant_id }
                         join oh in _tenantDBContext.tco_org_hierarchy.AsNoTracking() on new { a = td.fk_tenant_id, b = td.department_code, c = ov.pk_org_version }
                                                     equals new { a = oh.fk_tenant_id, b = oh.fk_department_code, c = oh.fk_org_version }
                         join s in _tenantDBContext.tco_sync_company_setup.AsNoTracking() on new { a = oh.fk_tenant_id, b = oh.org_id_3, c = oh.fk_org_version }
                                                     equals new { a = s.fk_tenant_id, b = s.org_id, c = s.fk_org_version }
                         join tb in _tenantDBContext.tfp_budget_changes.AsNoTracking() on new { a = td.fk_tenant_id, b = td.fk_change_id }
                                                     equals new { a = tb.fk_tenant_id, b = tb.pk_change_id }
                         join ac in _tenantDBContext.tco_user_adjustment_codes.AsNoTracking() on new { a = td.fk_tenant_id, b = td.budget_year, c = td.fk_adj_code, d = true } // Filter status=true here
                                                     equals new { a = ac.fk_tenant_id, b = ac.budget_year, c = ac.pk_adj_code, d = ac.status } // Potentially simplify join if status non-nullable
                                                                                                                                               // Removed the DefaultIfEmpty and acLObj alias for the 'ac' join as status=true filter makes it like an inner join
                         join sua in _tenantDBContext.tco_user_adjustment_codes.AsNoTracking() on new { a = td.fk_adj_code, b = s.sub_tenant_id }
                                                     equals new { a = sua.pk_adj_code, b = sua.fk_tenant_id } into suaList
                         from suaLObj in suaList.DefaultIfEmpty() // Keep this left join for the null check below
                         join tu in _tenantDBContext.tco_users.AsNoTracking() on td.updated_by equals tu.pk_id // Simplified join key
                         join ta in _tenantDBContext.tco_accounts.AsNoTracking() on new { a = td.fk_tenant_id, b = td.fk_account_code }
                                                     equals new { a = ta.pk_tenant_id, b = ta.pk_account_code }
                         where th.fk_tenant_id == tenantId
                            && td.budget_year == budgetYear
                            && ov.period_from <= periodValue && ov.period_to >= periodValue
                            && tb.org_budget_flag == 0
                            && s.org_level == 3
                            && s.sync_flag != 0
                            // && ac.status == true // Moved status check into the JOIN condition above (or keep here if preferred)
                            && suaLObj == null // Keep check for non-matching sub-tenant adjustments
                            && (subTenantId == 0 || s.sub_tenant_id == subTenantId)
                         group td by new // Grouping keys...
                         {
                             ac.fk_tenant_id, // Use 'ac' directly now
                             ac.pk_adj_code,
                             ac.fk_attribute_id,
                             ac.fk_bud_auth_code,
                             ac.case_ref,
                             tb.pk_change_id,
                             s.sub_tenant_id,
                             tb.budget_name,
                             td.fk_account_code,
                             ta.display_name,
                             th.pk_action_id,
                             th.description,
                             th.long_description,
                             updatedBy = tu.first_name + " " + tu.last_name,
                         } into res
                         select new // Temporarily select grouped data + aggregated values
                         {
                             GroupKey = res.Key,
                             Year1SumRaw = res.Sum(x => x.year_1_amount),
                             Year2SumRaw = res.Sum(x => x.year_2_amount),
                             Year3SumRaw = res.Sum(x => x.year_3_amount),
                             Year4SumRaw = res.Sum(x => x.year_4_amount),
                             MaxUpdated = res.Max(x => x.updated),
                             // Need user info for the final projection
                             updatedBy = res.Key.updatedBy  // Assumes user is same within group
                         });
           var resultList = await query.ToListAsync();

           return resultList.Select(res => new AdjustmentDataHelper
           {
                subTenantId = res.GroupKey.sub_tenant_id,
                budgetAuthorityCode = res.GroupKey.fk_bud_auth_code,
                budgetRoundName = res.GroupKey.budget_name,
                useradjustmentCode = res.GroupKey.pk_adj_code,
                twinCode = res.GroupKey.fk_attribute_id,
                caseNumber = res.GroupKey.case_ref,
                actionName = res.GroupKey.description,
                actionId = res.GroupKey.pk_action_id,
                longDescription = res.GroupKey.long_description,
                accountCode = res.GroupKey.fk_account_code,
                accountName = res.GroupKey.display_name,
                year1Sum = Math.Round(res.Year1SumRaw / 1000m),
                year2Sum = Math.Round(res.Year2SumRaw / 1000m),
                year3Sum = Math.Round(res.Year3SumRaw / 1000m),
                year4Sum = Math.Round(res.Year4SumRaw / 1000m),
                updated = TimeZoneInfo.ConvertTimeFromUtc(res.MaxUpdated, CentralEuropeanTimeZone),
                updatedBy = res.updatedBy
            }).ToList();
        }

        public async Task<tco_sync_company_setup> GetSubDeptCode(int mainTenantId, int subTenantId, string orgVersion, string departmentCode)
        {
            return await _tenantDBContext.tco_sync_company_setup.FirstOrDefaultAsync(x => x.fk_tenant_id == mainTenantId && x.sub_tenant_id == subTenantId && x.fk_org_version == orgVersion && x.default_department_main == departmentCode);
        }

        /*public async Task<List<tbu_trans_detail>> GetTbuTransData(int tenantId, int budgetYear, int actionId)
        {
            return await _tenantDBContext.tbu_trans_detail.Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.fk_action_id == actionId).ToListAsync();
        }*/

        public async Task<tfp_budget_changes> GetSubTenantBudChanges(int tenantId, int budgetYear)
        {
            var result = await _tenantDBContext.tfp_budget_changes.Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.org_budget_flag == 0).OrderByDescending(x => x.pk_change_id).ToListAsync();
            return result.FirstOrDefault();
        }

        public async Task<List<YbTransDetails>> GetTransDetailData(int tenantId, int budgetYear, int clientId, List<string> orgIds, string orgVersion)
        {
            var data = await (from t1 in _tenantDBContext.vw_tfp_sync_warehouse_budget
                              join t2 in _tenantDBContext.tco_sync_company_setup on new { a = t1.fk_tenant_id, b = t1.sub_tenant_id } equals new { a = t2.fk_tenant_id, b = t2.sub_tenant_id }
                              join t3 in _tenantDBContext.gco_tenants on new { a = t1.sub_tenant_id } equals new { a = t3.pk_id }
                              join t7 in _tenantDBContext.tfp_sync_background_job_status on new { a = t1.fk_tenant_id, b = t1.sub_tenant_id, c = t1.budget_year, e = BudgetProcessStage.YB_ActionAdj_Fetch.ToString() } equals
                                                                                            new { a = t7.main_tenant_id, b = t7.sub_tenant_id, c = t7.budget_year, e = t7.process_stage } into grp8
                              from grp9 in grp8.DefaultIfEmpty()
                              join btj in _tenantDBContext.tfp_sync_transfer_job_status.Where(x => x.step == ProcessTransferStep.Action.ToString()) on new { a = grp9.pk_job_id } equals new { a = btj.fk_job_id } into gBtj
                              from grpBtj in gBtj.DefaultIfEmpty()

                              join t5 in _tenantDBContext.tco_users on new { a = grp9.updated_by } equals
                                                                      new { a = t5.pk_id } into grp4
                              from grp10 in grp4.DefaultIfEmpty()

                              where t1.fk_tenant_id == tenantId && t1.budget_year == budgetYear && t2.status == 1 && t3.client_id == clientId &&
                                    t2.fk_org_version == orgVersion && orgIds.Contains(t2.org_id)
                              select new YbTransDetails
                              {
                                  subTenantId = t1.sub_tenant_id,
                                  subTenant = t3.tenant_name,
                                  originalMainBudget = (decimal)(t1.org_main != null ? Math.Round((decimal)t1.org_main) : 0),
                                  mainAdjustmentFpAmount = (decimal)(t1.revised_main != null ? Math.Round((decimal)t1.revised_main) : 0),
                                  subAdjustmentFpAmount = (decimal)(t1.revised_sub != null ? Math.Round((decimal)t1.revised_sub) : 0),
                                  adjustmentFpAmountsDeviation = ((decimal)(t1.deviation != null ? Math.Round((decimal)t1.deviation) : 0)),
                                  isAdjustmentFpAmountsDeviation = (t1.deviation != null ? Math.Round((decimal)t1.deviation) : 0) != 0,
                                  mainDetailBudget = ((decimal)(t1.budget_main != null ? Math.Round((decimal)t1.budget_main) : 0)),
                                  subDetailBudget = ((decimal)(t1.budget_sub != null ? Math.Round((decimal)t1.budget_sub) : 0)),
                                  detailBudgetDeviaiton = ((decimal)(t1.deviation_budget != null ? Math.Round((decimal)t1.deviation_budget) : 0)),
                                  isDetailBudgetDeviaiton = (t1.deviation_budget != null ? Math.Round((decimal)t1.deviation_budget) : 0) != 0,
                                  mainOrgIdThree = t2.org_id,
                                  transferStatus = grpBtj == null ? grp9.job_status : grpBtj.job_status,
                                  updatedBy = (grp10.first_name != null && grp10.last_name != null) ? (grp10.first_name + " " + grp10.last_name) : string.Empty,
                                  updated = grp9.updated,
                                  jobId = grpBtj != null ? grpBtj.fk_job_id.ToString() : string.Empty
                              }).ToListAsync();
            data = data.GroupBy(x => x.subTenantId).Select(x => x.First()).OrderBy(x => x.subTenant).ToList();
            return data;
        }

        public async Task<List<tbu_trans_detail>> GetTbuTransDetailDataToTransfer(int subTenantId, int budgetYear, List<string> deptCodeList)
        {
            var data = await _tenantDBContext.tbu_trans_detail.AsNoTracking().Where(x => x.fk_tenant_id == subTenantId && x.budget_year == budgetYear && deptCodeList.Contains(x.department_code)).ToListAsync();
            return data;
        }

        public async Task TransferYbApprovedBudActionsFromSubTenantAsync(int mainTenantId, int budgetYear, int subTenantId, List<tbu_trans_detail> dataToTransfer, int pkId, bool isChapterSetup, List<AttributeDepartmentMap> deptConnectedToTenant, string orgVersion)
        {
            string defaultDeptCode = string.Empty;
            string defaultFunctionCode = "999";
            List<string> deptToDelete = new List<string>();
            if (!isChapterSetup)
            {
                var compSyncData = await _tenantDBContext.tco_sync_company_setup.FirstAsync(x => x.fk_tenant_id == mainTenantId && x.sub_tenant_id == subTenantId && x.status == 1 && x.fk_org_version == orgVersion);
                defaultDeptCode = (compSyncData != null && !string.IsNullOrEmpty(compSyncData.default_department_main)) ? compSyncData.default_department_main.ToString() : string.Empty;
                deptToDelete.Add(defaultDeptCode);
            }
            else
            {
                deptToDelete.AddRange(deptConnectedToTenant.Select(x => x.attributeId).Distinct().ToList());
            }

            // Delete old data from main tenant
            await DeleteYbApprovedBudAdjActions(mainTenantId, deptToDelete, budgetYear);

            List<tbu_trans_detail> newDataForMainTenant = new List<tbu_trans_detail>();
            List<Guid> buTransIds = dataToTransfer.Select(x => x.bu_trans_id).Distinct().ToList();
            foreach (Guid id in buTransIds)
            {
                var dataList = dataToTransfer.Where(x => x.bu_trans_id == id).ToList();
                Guid newBuTransId = Guid.NewGuid();
                foreach (var item in dataList)
                {
                    tbu_trans_detail newRow = new tbu_trans_detail
                    {
                        pk_id = Guid.NewGuid(),
                        bu_trans_id = newBuTransId,
                        fk_tenant_id = mainTenantId,
                        action_type = item.action_type,
                        line_order = item.line_order,
                        fk_account_code = item.fk_account_code,
                        department_code = isChapterSetup ? (deptConnectedToTenant.First(x => x.departmentCode == item.department_code).attributeId ?? string.Empty) : defaultDeptCode,
                        fk_function_code = defaultFunctionCode,
                        fk_project_code = string.Empty,
                        free_dim_1 = string.Empty,
                        free_dim_2 = string.Empty,
                        free_dim_3 = string.Empty,
                        free_dim_4 = string.Empty,
                        resource_id = item.resource_id,
                        fk_employment_id = item.fk_employment_id,
                        description = string.Empty,
                        budget_year = budgetYear,
                        period = item.period,
                        budget_type = item.budget_type,
                        amount_year_1 = item.amount_year_1,
                        amount_year_2 = item.amount_year_2,
                        amount_year_3 = item.amount_year_3,
                        amount_year_4 = item.amount_year_4,
                        fk_key_id = item.fk_key_id,
                        updated = DateTime.UtcNow,
                        updated_by = pkId,
                        allocation_pct = item.allocation_pct,
                        total_amount = item.total_amount,
                        tax_flag = item.tax_flag,
                        holiday_flag = item.holiday_flag,
                        fk_pension_type = item.fk_pension_type,
                        fk_action_id = item.fk_action_id,
                        fk_investment_id = item.fk_investment_id,
                        fk_portfolio_code = item.fk_portfolio_code,
                        fk_prog_code = item.fk_prog_code,
                        fk_adjustment_code = item.fk_adjustment_code,
                        fk_alter_code = item.fk_alter_code,
                        change_flag = item.change_flag,
                        parent_bu_trans_id = item.parent_bu_trans_id,
                        fk_adjustment_code_finplan = item.fk_adjustment_code_finplan,
                    };
                    newDataForMainTenant.Add(newRow);
                }
            }
            if (newDataForMainTenant.Any())
            {
                await _tenantDBContext.tbu_trans_detail.BulkInsertAsync(newDataForMainTenant);
                await _tenantDBContext.BulkSaveChangesAsync();
            }
        }

        public async Task DeleteYbApprovedBudAdjActions(int tenantId, List<string> deptCodes, int budgetYear)
        {
            var dataToDelete = await _tenantDBContext.tbu_trans_detail.Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear && deptCodes.Contains(x.department_code)).ToListAsync();
            if (dataToDelete.Any())
            {
                await _tenantDBContext.tbu_trans_detail.BulkDeleteAsync(dataToDelete);
                await _tenantDBContext.BulkSaveChangesAsync();
            }
        }

        public async Task<List<YBApprovedBudAdjustmentsHelper>> ApprovedBudAdjustmentsData(int tenantId, int budgetYear, List<string> orgIds, string orgVersion, List<UserInformation> users)
        {
            var dataQuery = (from t1 in _tenantDBContext.vw_tfp_sync_warehouse_budget
                             join t2 in _tenantDBContext.tco_sync_company_setup on new { a = t1.fk_tenant_id, b = t1.sub_tenant_id } equals new { a = t2.fk_tenant_id, b = t2.sub_tenant_id }
                             join t3 in _tenantDBContext.gco_tenants on new { a = t1.sub_tenant_id } equals new { a = t3.pk_id }
                             join br in _tenantDBContext.tfp_sync_budget_round on new { a = t1.fk_tenant_id, b = t1.sub_tenant_id, c = t1.budget_year, d = BudgetProcessStage.YB_AppBudAdj_Actions.ToString() }
                                                                               equals new { a = br.fk_tenant_id, b = br.sub_tenant_id, c = br.budget_year, d = br.process_stage } into grp
                             from grp1 in grp.DefaultIfEmpty()
                             join t4 in _tenantDBContext.tfp_budget_changes on new { a = grp1.sub_change_id, b = grp1.sub_tenant_id, c = grp1.budget_year } equals
                                                                               new { a = t4.pk_change_id, b = t4.fk_tenant_id, c = t4.budget_year } into grp2
                             from grp3 in grp2.DefaultIfEmpty()
                             join t7 in _tenantDBContext.tfp_sync_background_job_status on new { a = t1.fk_tenant_id, b = t1.sub_tenant_id, c = t1.budget_year, e = BudgetProcessStage.YB_AppBudAdj_Actions.ToString() } equals
                                                                                           new { a = t7.main_tenant_id, b = t7.sub_tenant_id, c = t7.budget_year, e = t7.process_stage } into grp8
                             from grp9 in grp8.DefaultIfEmpty()
                             join btj in _tenantDBContext.tfp_sync_transfer_job_status.Where(x => x.step == ProcessTransferStep.Action.ToString()) on new { a = grp9.pk_job_id } equals new { a = btj.fk_job_id } into gBtj
                             from grpBtj in gBtj.DefaultIfEmpty()

                             where t1.fk_tenant_id == tenantId && t1.budget_year == budgetYear && t2.status == 1 &&
                                   t2.fk_org_version == orgVersion && (orgIds != null && orgIds.Contains(t2.org_id))
                             select new YBApprovedBudAdjustmentsHelper
                             {
                                 subTenantId = t1.sub_tenant_id,
                                 subTenant = t3.tenant_name,
                                 originalFPAmount = Math.Round((decimal)t1.org_main / 1000),
                                 adjustedFPAmtMain = Math.Round((decimal)t1.revised_main / 1000),
                                 adjustedFPAmtSub = Math.Round((decimal)t1.revised_sub / 1000),
                                 adjustedFPAmtDeviation = Math.Round((decimal)t1.deviation / 1000).ToString(),
                                 isAdjustedFPAmtDeviation = Math.Round((decimal)t1.deviation / 1000) != 0,
                                 mainOrgIdThree = t2.org_id,
                                 transferStatus = grpBtj.job_status ?? grp9.job_status ?? string.Empty,
                                 updatedBy = string.Empty,
                                 updated = grp9.updated,
                                 jobId = grpBtj.fk_job_id.ToString() ?? string.Empty,
                                 budgetRoundName = grp3.budget_name ?? grp1.sub_change_name ?? string.Empty,
                                 deviation = Math.Round((decimal)t1.deviation / 1000),
                                 approvalRef = grp1.sub_approval_reference ?? string.Empty,
                                 updatedById = grp9.updated_by,
                             });

            var dataList = await dataQuery
                .AsNoTracking()
                .ToListAsync();
            var updatedDataList = dataList.Select(data =>
            {
                var user = users.FirstOrDefault(u => u.pk_id == data.updatedById);
                data.updatedBy = user != null ? $"{user.first_name} {user.last_name}".Trim() : string.Empty;
                return data;
            })
            .GroupBy(x => x.subTenantId)
            .Select(x => x.First())
            .OrderBy(x => x.subTenant)
            .ToList();
            return updatedDataList;
        }
    }
}