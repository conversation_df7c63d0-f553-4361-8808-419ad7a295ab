using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Framsikt.Entities.Core;

namespace Framsikt.BL.Repository
{
    public interface IBudgetProposalRepository
    {
        Task<int> CreateNewFinplanAction(ActionWithBudgetChange actionHeaderData, UserData userDetails);

        Task<int> CreateNewBlistAction(ActionWithBudgetChange actionHeaderData, UserData userDetails);

        Task<tco_application_flag> GetApplicationFlag(int tenantId, string flagName, int budgetYear);

        Task<List<ActionGridColumns>> GetStrategyActions(UserData userDetails, actionGridInputHelper input, List<int> userRoles);

        Task DeleteFinplanActionHeaderByActionID(UserData userDetails, int actionID);

        Task DeleteFinplanActionDetailsByActionID(UserData userDetails, int actionID);

        Task DeleteBlistActionHeaderByActionID(UserData userDetails, int actionID);

        Task<List<StrategyActionsTagHelper>> GetAssessmentHeader(int tenantId, int budgetYear, List<string> ids);

        List<KeyValueData> GetActionTags(int tenantId, List<int> connectedTagList);
        Task<List<KeyValueData>> GetActionTagsAsync(int tenantId, List<int> connectedTagList);
        Task<List<keyvaluewithGuid>> GetGoalData(int budgetYear, List<Guid> connectedGoalList, int tenantId);

        Task<IEnumerable<tco_attachments>> GetAllAttachmentData(int tenantId, int budgetYear, string moduleId, string pageId);

        Task<tfp_trans_header> GetFinplanActionHeaderDataByActionID(int tenantID, int actionID);

        Task<tfp_temp_header> GetBlistActionHeaderDataByActionID(int tenantID, int actionID);
        Task<List<string>> GetServiceIdsByActionId(int tenantID, int actionID, int budgetYear, int changeId, string finplanLvl2);
        Task<List<string>> GetServiceIdsByBlistActionId(int tenantID, int actionID, int budgetYear, int changeId, string finplanLvl2);

        Task UpdateFinplanActionHeaderDataByActionID(UserData userDetails, tfp_trans_header actionHeaderData, ActionWithBudgetChange inputData);

        Task UpdateBlistActionHeaderDataByActionID(UserData userDetails, tfp_temp_header actionHeaderData, ActionWithBudgetChange inputData);

        Task<tfp_trans_detail> GetFinplanActionDetailRowById(int tenantId, int actionId, int rowId, int budgetYear);

        Task<tfp_temp_detail> GetBlistActionDetailRowById(int tenantId, int actionId, int rowId, int budgetYear);

        Task<bool> CheckFinplanActionDetailDataExists(UserData userDetails, int actionID, int budgetYear);

        Task<bool> CheckBlistActionDetailDataExists(UserData userDetails, int actionID, int budgetYear);

        Task<GoalTargetGridHelper> GoalTargetData(int tenantId, int budgetYear, string serviceId);

        Task<BudgetProposalPlanGoalTargetHelper> GetBudgetProposalPlanData(int budgetYear, int tenantId);

        Task<gco_tenants> GetColSelTenant(int tenantId);

        Task SaveAccessControlData(int tenantId, int pkUserId, int budgetYear, List<AccessControlData> updatedData);

        List<TcoActionTags> GetTagsData(int tenantId);
        Task<List<TcoActionTags>> GetTagsDataAsync(int tenantId);
        List<tfp_budget_changes> GetBudgetChange(int tenantId, int budgetYear);
        Task<List<tfp_budget_changes>> GetBudgetChangeAsync(int tenantId, int budgetYear);
        List<Framsikt.BL.Helpers.KeyValuePair> GetBudgetPhase(int tenantId, int budgetYear);
        Task<List<Framsikt.BL.Helpers.KeyValuePair>> GetBudgetPhaseAsync(int tenantId, int budgetYear);

        List<Framsikt.BL.Helpers.KeyValuePair> GetAlterCode(int tenantId, int budgetYear, List<string> actionTypes);

        Task<dynamic> SaveNewGoalData(int tenantId, int userId, AddNewGoalHelper newGoalData);

        Task<GoalDetailHelper> GetGoalDetails(int tenantId, int budgetYear, string orgId, int orgLevel, string serviceId, int serviceLevel, Guid goalId, Guid goalDistGuid, bool isTenantSyncSetup);

        Task<tfp_strategy_text> GetTfpStrategyById(int tenantId, int budgetYear, int strategyId);

        Task<tfp_effect_target_detail> SaveIndicatorDesc(int tenantId, Guid descId);

        Task SaveGoalTags(int userId, int tenantId, string tagIds, Guid goalId, int saveType, int budgetYear);

        Task<List<tbiassignments>> GetAllBudPropAssignment(int tenantId, int budgetYear, Guid uniqueAssignmentId);

        Task<List<TbiAssignmentGoal>> GetAssignmentGoalIds(int tenantId, int budgetYear, Guid uniqueAssignmentId);

        Task<List<TbiAssignmentTarget>> GetAssignmentTargetIds(int tenantId, int budgetYear, Guid uniqueAssignmentId);

        Task<List<TbiAssignmentStrategy>> GetAssignmentStrategyIds(int tenantId, int budgetYear, Guid uniqueAssignmentId);

        Task<tbiassignments> GetBudPropAssignmentInfo(int tenantId, int budgetYear, Guid assignmentId);

        Task<List<TbiAssignmentGoal>> GetAssignmentGoals(int tenantId, Guid assignmentId, int budgetYear);

        Task<List<TbiAssignmentStrategy>> GetAssignmentStrategies(int tenantId, Guid assignmentId, int budgetYear);

        Task<List<TbiAssignmentTarget>> GetAssignmentTargets(int tenantId, Guid assignmentId, int budgetYear);

        Task<tco_assignments_descriptions> GetAssignmentDescription(int tenantId, Guid assignmentId);

        Task<List<BPAssignmentDescriptionsHelper>> GetDelegatedAssignmentsWithoutDescription(int tenantId, int budgetYear, Guid uniqueAssignmentId,
            Guid assignmentId, List<string> lowerOrgIds, bool isDelegationSave);

        Task<tbitasks> GetAssignmentTask(int tenantId, int budgetYear, Guid assignmentId);

        Task<List<tco_progress_status>> GetProgressStatusForAssignments(int tenantId);

        Task<List<gco_indicator_control_parameter>> GetControlParam();

        Task<List<tco_category>> GetCategoryInfoForAssignments(int tenantId, List<string> categoryTypes);

        Task<List<tco_goals_distribution>> GetGoalDistributionData(int tenantId, List<Guid> goalIds);

        Task<List<tco_targets_distribution>> GetTargetDistributionData(int tenantId, List<Guid> targetIds);

        Task<List<tfp_strategy_text>> GetStrategyDistributionData(int tenantId, int budgetYear, List<int> strategyIds);

        List<tco_adjustment_codes> GetAllAdjustementCodes(int tenantId);
        Task<List<tco_adjustment_codes>> GetAllAdjustementCodesAsync(int tenantId);
        List<Framsikt.BL.Helpers.KeyValuePair> GetAllOperationAccounts(int tenantId, int budgetYear);
        Task<List<Framsikt.BL.Helpers.KeyValuePair>> GetAllOperationAccountsAsync(int tenantId, int budgetYear);
        Task<tco_attributes> GetAttributeChapter(int tenantId);

        Task<List<tco_attribute_values>> GetAttributeValues(int tenantId);

        Task<List<tco_activity_indicator>> GetActivitiesIndicatorData(int tenantId, int budgetYear);

        Task<List<ActivityIndicatorData>> GetActivityIndicatorSetupData(int budgetYear, int tenantId, bool isBusPlanActivity, bool tenatHasAccessToAdmin);

        Task<List<KeyValueData>> GetActivityGroupsAsync(int tenantId);

        Task<tco_action_type_details> GetActionTypeDetails(int tenantId, string serviceId, int budgetYear, string strSAGFieldId);

        Task<List<IndicatorNumberTypeDataHelper>> GetIndicatorNumberTypeData(int budgetYear, bool isActivityRequest = false);

        Task<List<gko_kostra_data_corp>> GetIndicatorDetailData(List<string> indicators, TenantData tenantdata, int budgetYear, string pageId = "");

        Task<List<indicator>> GetIndicatorsDataAsync(int tenantId, int budgetYear, string orgId, int orgLevel);

        Task<ActivityIndicatorData> GetActivityPoupupDetailData(int tenantId, int activityId, bool tenatHasAccessToAdmin);

        Task<tco_indicator_setup> GetIndicatiorSetupDataForInd(int tenantId, Guid pkIndicatorCode);

        Task<tmd_indicator_results> GetTmdIndResult(int tenantId, Guid indicatorCode, int budgetYear, string orgId, int orgLevel, string serviceId);

        Task<tco_counters> GetTcoCountersData(int tenantId, string counterId);

        Task<List<tco_activity_indicator_group>> GetActivityGroup(int tenantId);

        Task<List<TcoActionTags>> GetActivityTags(int tenantId);

        Task<tco_activity_indicator> GetActivityIndicatorDataforId(int tenantId, int actrivityId);

        Task<tco_application_flag> GetColumnSelectorOfTenant(int tenantId, int orgLevel, string orgVersion, string flagName);

        Task SaveColumnSelectorApplicationFlagAsync(tco_application_flag data);

        Task<List<tco_activity_indicator>> GetActivitiesIndicatorDataWithIds(int tenantId, List<int> activitiesIds);

        Task<tco_activity_indicator> GetActivityIndicatorData(int tenantId, int activityId);

        Task<List<tco_activity_indicator>> GetActivitiesIndicatorDataWithOrgIds(int tenantId, int parentId, List<string> orgIdsList);

        void DeleteActivitesIndicator(List<tco_activity_indicator> activitiesToDelete);

        void DeleteActivityIndicator(tco_activity_indicator activityToDelete);

        Task<List<tco_activity_indicator>> GetActivitiesWithParentIds(int tenantId, int parentId);

        Task<tco_activity_indicator> GetActivitiesIndicatorDataById(int tenantId, int activitiesId);

        Task<tco_key_figures> GetTcoKeyFigireDataById(int tenantId, int activitiesId);

        Task<tmr_activity> GetTmrActivityDataById(int tenantId, int activitiesId, int forecastPeriod);

        Task<tco_key_figures_monthrep> GetTcoKeyFiguresMonthRepDataById(int tenantId, int activitiesId);

        Task<List<KeyValueInt>> GetActivityImgCategory();

        Task<List<string>> GetActivityImgByCategoryId(int categoryId);

        Task<List<string>> GetActivityIndicatorGroupsDesc(int tenantId, List<string> actIndiGroupDesc);

        void AddActivityIndicatorGroups(List<tco_activity_indicator_group> activityIndicatorGroupsToAdd);

        Task<tco_indicator_setup> GetIndicatorSetup(int tenantId, Guid pkIndicatorCode);

        void AddIndicatorSetup(tco_indicator_setup indicatorSetup);

        Task<tco_counters> GetTableCounters(int tenantId, string counterId);

        void AddTableCounter(tco_counters counterData);

        void AddActivityIndicatorData(List<tco_activity_indicator> dataToAdd);

        Task<List<ActivityIndiDataOrgLevelHelper>> GetActivityIndiDataWithoutOrgLevel(int tenantId, int budgetYear);

        Task<List<tmd_indicator_results>> GetIndicatorResults(int tenantId, int budgetYear, List<Guid> indicatorCodes);

        Task<List<tco_indicator_setup>> GetIndicatorSetupData(int tenantId);

        Task<List<clsActivityImageLibraryHelper>> GetActivityByIdList(int tenantId, List<int> activityId);

        Task<List<tco_sync_activity_indicator>> GetTransferredActivityIndicatorMappingDataAsync(int tenantID);

        Task<bool> CheckActivityIndicatorTransferred(int subTenantID, int activityIndicatorID);

        Task<tco_sync_company_setup?> GetMainTenantBasedonSubTenantAsync(int subTenantID);

        Task<List<Guid>> GettransferredGoals(int tenantId);
        Task<List<Guid>> GetTransferredTargets(int tenantId);

        Task<List<tfp_sync_assignments>> GetFetchedAssignmentAsync(int tenantID, BudgetProcessStage processStage);

        Task<bool> IsAssignmentTransferred(int subTenantID, Guid assignmentId);

        Task<tco_application_flag> GetNonAllocatedStatus(int tenantId, int budgetYear);

        Task<List<int>> GetTransferredActions(int tenantId);
        Task<tfp_sync_actions> GetFetchedAction(int tenantId, List<string> processStageList, int actionId);
        Task<List<tco_progress_status>> GetSyncStatusDropDown(int tenantId, SyncObjectStatusType syncObjectStatusType);

        Task<string> GetSyncStatusValue(int tenantId, int key, SyncObjectStatusType syncObjectStatusType);

        Task<tfp_budget_changes> GetBudgetChangeForApproved(int tenantId, int budgetYear, int workFlow, int status);

        Task<List<PublishConfigContent>> GetPublishConfigData(int tenantID, string prodURL, PublishConfigInput configInput);

        Task<List<SubTenantPublishConfigHelper>> GetSubTenantsPublishConfig(List<int> subTenantIds, List<Guid> subTenantBudgetPhaseIds, int budgetYear, PublishTreeType treeType);

        Task<List<KeyValueStringPairWithTenantId>> GetTenantsShortNames(List<int> subTenantIds);

        Task<tco_publish_config?> GetPublishConfig(int subTenantID, string budgetPhaseId, PublishTreeType treeType,
            int budgetYear, int forecastPeriod);

        Task<Guid?> GetMainTenantPhaseId(int subTenantID, string budgetPhaseId);

        Task<List<int>> GetTransferredStrategy(int tenantId, List<int> strategyIds);

        Task<List<tco_budget_phase>> GetBudgetPhaseList(int tenantId);

        Task<tco_approval_node_description_log> GetApprovalLogDescription(int tenant_id, Guid nodeId, string id,
                                                    int budgetYear, NodeDimentionType nodeDimentionType, Guid budgetphaseid, string descriptionType);

        Task<tco_widget_node_master?> GetMasterNodeData(int tenantId, int budgetYear, Guid masterNodeId);

        Task<TcoPublishTemplate?> GetPublishTemplateData(int tenantId, int templateId, int budgetYear);

        Task<List<tco_approval_node_description_log>> GetAllApprovalNodeDescription(int tenantId, int budgetYear, string nodeType, Guid? processId);

        Task<IEnumerable<tco_approval_node_description_log>> GetAllApprovalLogDescription(int tenant_id, int budgetYear, Guid processId);

        Task ClearApprovalNodeDescriptionLogs(int tenantId, int budgetYear, Guid processId);
        Task UpdateWarningForReviewChild(int tenantId, Guid processId, Guid nodeId, string stringNodeId);
        Task<tco_sync_activity_indicator> ActivityIndicatorFetched(int mainTenantId, int mainActivityIndicatorId, List<string> processStagesFetch);
        Task<tfp_sync_goals> GetFetchedGoal(int tenantId, List<string> processStageList, Guid goalId);
        Task<tfp_sync_targets> GetFetchedTarget(int tenantId, List<string> processStageList, Guid targetId);
        Task<tfp_sync_climate_indicators> GetFetchedClimateIndicators(int tenantId, List<string> processStageList, int indicatorId);
        Task<List<Guid>> GetTransferredTargetsIndicators(int tenantId);
        Task<tfp_sync_target_indicators> GetFetchedTargetIndicators(int tenantId, List<string> processStageList, Guid indicatorId);
        Task<List<indicator>> GetTargetIndicatorsDataAsync(int tenantId, int budgetYear, string orgId, int orgLevel);
        Task<tfp_effect_target_detail> GetTargetIndicatorFromPkId(int tenantId, int pkId);
        Task<string> GetMunicipalityIdForTenant(int tenantId);
        Task<List<gko_kostra_data>> GetKostraDataForIndicatorCode(string indicatorCode);
        Task<List<gko_kostra_data_corp>> GetKostraDataCorpForIndicatorCode(string indicatorCode);
        Task<string> GetKostraGroupForMunicipality(string municipality);
        Task<string> GetDataSourceForIndicatorCode(string indicatorCode);
        Task<List<tco_targets>> GetTargets(int tenantId, int budgetYear);
        Task<List<int>> GettransferredClimateIndicators(int tenantId);
        Task<TpdPlanDiscTrackAssignments> GetCurrentAssignmentFromPKIdAsync(int tenantId, int budgetYear, Guid assignmentId);
        Task<bool> GetAssignmentUsedInPlanDiskAsync(int tenantId, Guid assignmentId);
        Task<List<tco_targets>> GetTargetInfo(int tenantId, int budgetYear, List<Guid> targetId);
        Task<List<tfp_strategy_text>> GetStrategyInfo(int tenantId, int budgetYear, List<int> strategyId);

        Task<TcoBudPropTemplateConfig> GetBudPropTemplateCharacteristics(int tenantId, Guid budgetPhaseId, string orgId, int budgetYear);
        Task<Dictionary<Guid, tco_doc_widget>> GetMasterTemplateWidgetDataByNodeIdDict(int tenantId, int budgetYear, PublishTreeType publishTreeType);
        Task<List<tco_goals_distribution>> GetGoalDistrData(int tenantId, Guid goalId);

        Task<List<tbiassignments>> GetAllAssignments(int tenantId, int budgetYear, Guid uniqueAssignmentId);
        Task<Dictionary<Guid, string>> GetCustomNodeInternalTitle(int tenantId, int budgetYear, PublishTreeType publishTreeType, int forecastPeriod = 0, IEnumerable<Guid> masterNodeIds = null, IEnumerable<Guid> cnInstanceGuids = null);
        Task<List<tco_goals>> GetGoalData(int tenantId, int budgetYear);
        Task<List<tco_focusarea>> GetFocusAreaData(int tenantId, int budgetYear);
        Task<List<tfp_strategy_text>> GetStrategyData(int tenantId, int budgetYear);
        Task<List<tfp_strategy_goal>> GetStrategyGoalData(int tenantId, Guid goalId);
        Task<List<tfp_strategy_goal>> GetGoalConnectedToStrategy(int tenantId, int strategyId);
        Task<List<tfp_strategy_target>> GetTargetConnectedToStrategy(int tenantId, int strategyId);
        Task<List<tfp_strategy_target>> GetStrategyTargetData(int tenantId, Guid targetId);
        Task<List<tfp_effect_target_detail>> GetTargetDetailData(int tenantId, Guid targetId, Guid targetDisrtId);
        Task<List<tco_targets_distribution>> GetTargetDistributionDataByTargetId(int tenantId, Guid targetId);
        Task<List<tfp_effect_target_detail>> GetTargetDetailDistrData(int tenantId, Guid targetId);
        Task<List<TcoActionTags>> GetActionTagsData(int tenantId);
        Task<List<tco_goals_distribution>> GetGoalDistrDataByGoalId(int tenantId, Guid goalId);
        Task<List<tfp_strategy_text>> GetStrategyDetailData(int tenantId, int budgetYear);
        Task<List<tfp_strategy_goal>> GetStrategyGoalDetailData(int tenantId, Guid goalId);
        Task<List<tco_targets>> GetTargetsConnectedToGoal(int tenantId, int budgetYear, Guid goalId);
        Task<List<tco_targets_distribution>> GetTargetDistributionDetailData(int tenantId, List<Guid> targetIds);
        Task<List<tfp_effect_target_detail>> GetAllTargetDistrDetailData(int tenantId, List<Guid> targetId);
        Task<List<tco_indicator_setup>> GetIndicatorSetupInfo(int tenantId, List<Guid> indicatorIds);        
        Task<List<tmd_indicator_results>> GetIndicatorDetailResultData(int tenantId, int budgetYear);
        Task<List<tco_indicator_setup>> GetTcoIndicatorSetupData(int tenantId);        
        Task<List<tfp_effect_target_detail>> GetTargetDistrDetailData(int tenantId, Guid targetId);
    }
}