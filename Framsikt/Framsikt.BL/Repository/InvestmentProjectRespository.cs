#pragma warning disable CS8625
#pragma warning disable CS8629

#pragma warning disable CS8600
#pragma warning disable CS8601
#pragma warning disable CS8602
#pragma warning disable CS8603
#pragma warning disable CS8604

using Framsikt.BL.Constants;
using Framsikt.BL.Core.Helpers;
using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Framsikt.Entities.Core;
using Microsoft.EntityFrameworkCore;
using System.Globalization;

namespace Framsikt.BL.Repository
{
    public class InvestmentProjectRespository : IInvestmentProjectRespository
    {
        private readonly TenantDBContext _tenantDbContext;
        private readonly IDbContextManager _dbContextManager;

        public InvestmentProjectRespository(TenantDBContext dbContext, IDbContextManager dbContextManager)
        {
            _tenantDbContext = dbContext;
            _dbContextManager = dbContextManager;
        }

        public async Task<IEnumerable<BudgetChangeInfo>> GetBudgetChangeDataByOrgBudgetAsync(int tenantId, int budgetYear, int orgBudget)
        {
            List<tfp_budget_changes> budgetChangeData = await _tenantDbContext.tfp_budget_changes
                .Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.org_budget_flag == orgBudget)
                .OrderBy(y => y.change_date).ToListAsync();
            return budgetChangeData.Select(x => new BudgetChangeInfo
            {
                id = x.pk_change_id,
                budget_change = x.budget_name,
                approval_reference = x.approval_reference,
                date = x.change_date.ToString("dd'.'MM'.'yyyy", CultureInfo.InvariantCulture),
                statusVal = x.status,
                status = x.status == 1 ? "1" : "0",
                isActive = 0,
                workflowStatus = x.workflow_status,
                statusCode = x.status,
                isSelected = x.is_selected,
                budgetPhaseId = x.fk_budget_phase_id,
            });
        }

        public async Task<tco_users_settings> GetUserDataAsync(int userId, int tenantId)
        {
            return await _tenantDbContext.tco_users_settings.FirstOrDefaultAsync(x =>
                x.fk_user_id == userId && x.tenant_id == tenantId && x.is_active_tenant);
        }

        public async Task<IEnumerable<tfp_budget_changes>> GetBudgetChangeInfoAsync(int tenantId, int orgBudget, int budgetYear)
        {
            return await _tenantDbContext.tfp_budget_changes.Where(x =>
                x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.org_budget_flag == orgBudget &&
                x.status == 1).ToListAsync(); // for invplan, yb inv , yb financing
        }

        public async Task<IEnumerable<tfp_budget_changes>> GetBudgetChangeBudManAsync(int tenantId, int orgBudget, int budgetYear)
        {
            return await _tenantDbContext.tfp_budget_changes.Where(x =>
                x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.status == 1 && x.org_budget_flag == 1 &&
                x.workflow_status == 30).ToListAsync();
        }

        public async Task<IEnumerable<tco_budget_phase>> GetBudgetPhaseAsync(int tenantId, int orgBudget)
        {
            return await _tenantDbContext.tco_budget_phase
                .Where(x => x.fk_tenant_id == tenantId && x.status == 1 && x.org_budget_flag == orgBudget)
                .OrderBy(x => x.sort_order).ToListAsync();
        }

        public async Task<IEnumerable<InvestmentOverviewGridHelper>> GetInvestmentOveriewDataAsync(int tenantId,
            int budgetYear, List<int> changeIds, int currentChangeId, InvestmentPageType pageType, string userAdjCode)
        {
            IEnumerable<InvestmentOverviewGridHelper> data;
            if (pageType == InvestmentPageType.FinPlanInvestment || pageType == InvestmentPageType.BudgetProposalInvestment)
            {
                data = await (from tm in _tenantDbContext.tco_main_projects
                              join tp in _tenantDbContext.tco_projects on new { a = tm.fk_tenant_id, b = tm.pk_main_project_code }
                                                                   equals new { a = tp.fk_tenant_id, b = tp.fk_main_project_code }
                              join pt in _tenantDbContext.tfp_proj_transactions on new { a = tp.fk_tenant_id, b = tp.pk_project_code }
                                                                            equals new { a = pt.fk_tenant_id, b = pt.fk_project_code }
                              join b in _tenantDbContext.tfp_budget_changes on new { a = pt.fk_tenant_id, b = pt.fk_change_id }
                                                                        equals new { a = b.fk_tenant_id, b = b.pk_change_id }
                              join ac in _tenantDbContext.tco_accounts on new { a = pt.fk_tenant_id, b = pt.fk_account_code }
                                                                   equals new { a = ac.pk_tenant_id, b = ac.pk_account_code }
                              join rl in _tenantDbContext.gmd_reporting_line on new { a = ac.fk_kostra_account_code }
                                                                         equals new { a = rl.fk_kostra_account_code }
                              join mp in _tenantDbContext.tco_main_project_setup.Where(x => x.fk_tenant_id == tenantId)
                                                                                      on new { a = tm.fk_tenant_id, b = tm.pk_main_project_code }
                                                                                  equals new { a = mp.fk_tenant_id, b = mp.pk_main_project_code } into lftSet
                              from lftMp in lftSet.DefaultIfEmpty()
                              where tm.budget_year_from.Year <= budgetYear && budgetYear <= tm.budget_year_to.Year && tp.date_from.Year <= budgetYear && budgetYear <= tp.date_to.Year && tm.inv_status.HasValue //&& mp.pk_main_project_code== "2001"/*&& tm.fk_department_code != null #65203*/ //&& (!pt.is_vat_row) remove Vat_row check #65119
                              && rl.report.Trim() == "55_OVINV".Trim()                                                                                                                                                                                  // && b.budget_year <= budgetYear
                                                                                                                                                                                                                                                        //&& changeIds.Contains(pt.fk_change_id)
                                                                                                                                                                                                                                                        // && mp.pk_main_project_code== "0630"
                              select new InvestmentOverviewGridHelper()
                              {
                                  status = tm.inv_status.HasValue ? tm.inv_status.Value : 0,
                                  tag = lftMp == null ? string.Empty : lftMp.tags,
                                  mainProjectCode = tm.pk_main_project_code,
                                  mainProjectName = tm.main_project_name,
                                  investmentPhase = tm.fk_investment_phase_id.HasValue ? tm.fk_investment_phase_id.Value : 0,
                                  projectCode = tp.pk_project_code,
                                  projectName = tp.project_name,
                                  accountCode = pt.fk_account_code,
                                  functionCode = pt.fk_function_code,
                                  departmentcode = pt.fk_department_code,
                                  alterCode = pt.fk_alter_code,
                                  adjustmentCode = pt.fk_adjustment_code,
                                  userAdjustmentCode = pt.fk_user_adjustment_code,
                                  responDeptCode = tm.fk_department_code != null ? tm.fk_department_code : string.Empty,
                                  responFuncCode = tm.fk_Function_code != null ? tm.fk_Function_code : string.Empty,
                                  changeId = pt.fk_change_id,
                                  ammount = pt.amount,
                                  year = pt.year,
                                  line_item_id = rl.line_item_id.ToString(),
                                  line_item_Name = rl.line_item.ToString(),
                                  mainDepartmentCode = pt.fk_department_code, //tm.fk_department_code, #70757
                                  programCode = tp.fk_prog_code,
                                  approvedCost = (pt.year == -1) ? pt.amount / 1000 : 0,
                                  previousBudgeted = (pt.year > 0 && pt.year < budgetYear) ? pt.amount / 1000 : 0,
                                  year1Ammount = (pt.year == budgetYear) ? pt.amount / 1000 : 0,
                                  year2Ammount = (pt.year == (budgetYear + 1)) ? pt.amount / 1000 : 0,
                                  year3Ammount = (pt.year == (budgetYear + 2)) ? pt.amount / 1000 : 0,
                                  year4Ammount = (pt.year == (budgetYear + 3)) ? pt.amount / 1000 : 0,
                                  year5Ammount = (pt.year == (budgetYear + 4)) ? pt.amount / 1000 : 0,
                                  year6Ammount = (pt.year == (budgetYear + 5)) ? pt.amount / 1000 : 0,
                                  year7Ammount = (pt.year == (budgetYear + 6)) ? pt.amount / 1000 : 0,
                                  year8Ammount = (pt.year == (budgetYear + 7)) ? pt.amount / 1000 : 0,
                                  year9Ammount = (pt.year == (budgetYear + 8)) ? pt.amount / 1000 : 0,
                                  year10Ammount = (pt.year == (budgetYear + 9)) ? pt.amount / 1000 : 0,
                                  year11Ammount = (pt.year == (budgetYear + 10)) ? pt.amount / 1000 : 0,
                                  year12Ammount = (pt.year == (budgetYear + 11)) ? pt.amount / 1000 : 0,
                                  year13Ammount = (pt.year == (budgetYear + 12)) ? pt.amount / 1000 : 0,
                                  year14Ammount = (pt.year == (budgetYear + 13)) ? pt.amount / 1000 : 0,
                                  year15Ammount = (pt.year == (budgetYear + 14)) ? pt.amount / 1000 : 0,
                                  year16Ammount = (pt.year == (budgetYear + 15)) ? pt.amount / 1000 : 0,
                                  year17Ammount = (pt.year == (budgetYear + 16)) ? pt.amount / 1000 : 0,
                                  year18Ammount = (pt.year == (budgetYear + 17)) ? pt.amount / 1000 : 0,
                                  year19Ammount = (pt.year == (budgetYear + 18)) ? pt.amount / 1000 : 0,
                                  year20Ammount = (pt.year == (budgetYear + 19)) ? pt.amount / 1000 : 0,
                                  changeYear1 = (pt.fk_change_id == currentChangeId) && pt.year == budgetYear ? pt.amount / 1000 : 0,
                                  changeYear2 = (pt.fk_change_id == currentChangeId) && pt.year == (budgetYear + 1) ? pt.amount / 1000 : 0,
                                  changeYear3 = (pt.fk_change_id == currentChangeId) && pt.year == (budgetYear + 2) ? pt.amount / 1000 : 0,
                                  changeYear4 = (pt.fk_change_id == currentChangeId) && pt.year == (budgetYear + 3) ? pt.amount / 1000 : 0,
                                  changeYear5 = (pt.fk_change_id == currentChangeId) && pt.year == (budgetYear + 4) ? pt.amount / 1000 : 0,
                                  changeYear6 = (pt.fk_change_id == currentChangeId) && pt.year == (budgetYear + 5) ? pt.amount / 1000 : 0,
                                  changeYear7 = (pt.fk_change_id == currentChangeId) && pt.year == (budgetYear + 6) ? pt.amount / 1000 : 0,
                                  changeYear8 = (pt.fk_change_id == currentChangeId) && pt.year == (budgetYear + 7) ? pt.amount / 1000 : 0,
                                  changeYear9 = (pt.fk_change_id == currentChangeId) && pt.year == (budgetYear + 8) ? pt.amount / 1000 : 0,
                                  changeYear10 = (pt.fk_change_id == currentChangeId) && pt.year == (budgetYear + 9) ? pt.amount / 1000 : 0,
                                  changeYear11 = (pt.fk_change_id == currentChangeId) && pt.year == (budgetYear + 10) ? pt.amount / 1000 : 0,
                                  changeYear12 = (pt.fk_change_id == currentChangeId) && pt.year == (budgetYear + 11) ? pt.amount / 1000 : 0,
                                  changeYear13 = (pt.fk_change_id == currentChangeId) && pt.year == (budgetYear + 12) ? pt.amount / 1000 : 0,
                                  changeYear14 = (pt.fk_change_id == currentChangeId) && pt.year == (budgetYear + 13) ? pt.amount / 1000 : 0,
                                  changeYear15 = (pt.fk_change_id == currentChangeId) && pt.year == (budgetYear + 14) ? pt.amount / 1000 : 0,
                                  changeYear16 = (pt.fk_change_id == currentChangeId) && pt.year == (budgetYear + 15) ? pt.amount / 1000 : 0,
                                  changeYear17 = (pt.fk_change_id == currentChangeId) && pt.year == (budgetYear + 16) ? pt.amount / 1000 : 0,
                                  changeYear18 = (pt.fk_change_id == currentChangeId) && pt.year == (budgetYear + 17) ? pt.amount / 1000 : 0,
                                  changeYear19 = (pt.fk_change_id == currentChangeId) && pt.year == (budgetYear + 18) ? pt.amount / 1000 : 0,
                                  changeYear20 = (pt.fk_change_id == currentChangeId) && pt.year == (budgetYear + 19) ? pt.amount / 1000 : 0,
                                  financingYear1Ammount = rl.line_item_id.ToString() != "1010" && (pt.year == budgetYear) ? pt.amount / 1000 : 0,
                                  financingYear2Ammount = rl.line_item_id.ToString() != "1010" && (pt.year == (budgetYear + 1)) ? pt.amount / 1000 : 0,
                                  financingYear3Ammount = rl.line_item_id.ToString() != "1010" && (pt.year == (budgetYear + 2)) ? pt.amount / 1000 : 0,
                                  financingYear4Ammount = rl.line_item_id.ToString() != "1010" && (pt.year == (budgetYear + 3)) ? pt.amount / 1000 : 0,
                                  financingYear5Ammount = rl.line_item_id.ToString() != "1010" && (pt.year == (budgetYear + 4)) ? pt.amount / 1000 : 0,
                                  financingYear6Ammount = rl.line_item_id.ToString() != "1010" && (pt.year == (budgetYear + 5)) ? pt.amount / 1000 : 0,
                                  financingYear7Ammount = rl.line_item_id.ToString() != "1010" && (pt.year == (budgetYear + 6)) ? pt.amount / 1000 : 0,
                                  financingYear8Ammount = rl.line_item_id.ToString() != "1010" && (pt.year == (budgetYear + 7)) ? pt.amount / 1000 : 0,
                                  financingYear9Ammount = rl.line_item_id.ToString() != "1010" && (pt.year == (budgetYear + 8)) ? pt.amount / 1000 : 0,
                                  financingYear10Ammount = rl.line_item_id.ToString() != "1010" && (pt.year == (budgetYear + 9)) ? pt.amount / 1000 : 0,
                                  financingPreviousBudgeted = rl.line_item_id.ToString() != "1010" && pt.year < budgetYear ? pt.amount / 1000 : 0,
                              }).ToListAsync();
            }
            else
            {
                data = await (from tm in _tenantDbContext.tco_main_projects
                              join tp in _tenantDbContext.tco_projects on new { a = tm.fk_tenant_id, b = tm.pk_main_project_code }
                                                                   equals new { a = tp.fk_tenant_id, b = tp.fk_main_project_code }
                              join pt in _tenantDbContext.tfp_proj_transactions on new { a = tp.fk_tenant_id, b = tp.pk_project_code }
                                                                            equals new { a = pt.fk_tenant_id, b = pt.fk_project_code }
                              join b in _tenantDbContext.tfp_budget_changes on new { a = pt.fk_tenant_id, b = pt.fk_change_id }
                                                                        equals new { a = b.fk_tenant_id, b = b.pk_change_id }
                              join ac in _tenantDbContext.tco_accounts on new { a = pt.fk_tenant_id, b = pt.fk_account_code }
                                                                   equals new { a = ac.pk_tenant_id, b = ac.pk_account_code }
                              join rl in _tenantDbContext.gmd_reporting_line on new { a = ac.fk_kostra_account_code }
                                                                         equals new { a = rl.fk_kostra_account_code }
                              join mp in _tenantDbContext.tco_main_project_setup.Where(x => x.fk_tenant_id == tenantId)
                                                                                      on new { a = tm.fk_tenant_id, b = tm.pk_main_project_code }
                                                                                  equals new { a = mp.fk_tenant_id, b = mp.pk_main_project_code } into lftSet
                              from lftMp in lftSet.DefaultIfEmpty()
                              where tm.budget_year_from.Year <= budgetYear && budgetYear <= tm.budget_year_to.Year && tp.date_from.Year <= budgetYear && budgetYear <= tp.date_to.Year && tm.inv_status.HasValue //&& mp.pk_main_project_code== "BUD044"/*&& tm.fk_department_code != null #65203*/ //&& (!pt.is_vat_row) remove Vat_row check #65119
                                                                                                                                                                                                                 // && b.budget_year <= budgetYear
                                                                                                                                                                                                                 //&& changeIds.Contains(pt.fk_change_id)
                              && rl.report.Trim() == "55_OVINV".Trim()                                                                                                                                                                                   // && mp.pk_main_project_code== "0630"
                              select new InvestmentOverviewGridHelper()
                              {
                                  status = tm.inv_status.HasValue ? tm.inv_status.Value : 0,
                                  tag = lftMp == null ? string.Empty : lftMp.tags,
                                  mainProjectCode = tm.pk_main_project_code,
                                  mainProjectName = tm.main_project_name,
                                  investmentPhase = tm.fk_investment_phase_id.HasValue ? tm.fk_investment_phase_id.Value : 0,
                                  projectCode = tp.pk_project_code,
                                  projectName = tp.project_name,
                                  accountCode = pt.fk_account_code,
                                  functionCode = pt.fk_function_code,
                                  departmentcode = pt.fk_department_code,
                                  alterCode = pt.fk_alter_code,
                                  adjustmentCode = pt.fk_adjustment_code,
                                  userAdjustmentCode = pt.fk_user_adjustment_code,
                                  responDeptCode = tm.fk_department_code != null ? tm.fk_department_code : string.Empty,
                                  responFuncCode = tm.fk_Function_code != null ? tm.fk_Function_code : string.Empty,
                                  changeId = pt.fk_change_id,
                                  ammount = pt.amount,
                                  year = pt.year,
                                  line_item_id = rl.line_item_id.ToString(),
                                  line_item_Name = rl.line_item.ToString(),
                                  mainDepartmentCode = pt.fk_department_code, //tm.fk_department_code, #70757
                                  programCode = tp.fk_prog_code,
                                  approvedCost = (pt.year == -1) ? pt.amount / 1000 : 0,
                                  previousBudgeted = (pt.year > 0 && pt.year < budgetYear) ? pt.amount / 1000 : 0,
                                  year1Ammount = (pt.year == budgetYear) ? pt.amount / 1000 : 0,
                                  year2Ammount = (pt.year == (budgetYear + 1)) ? pt.amount / 1000 : 0,
                                  year3Ammount = (pt.year == (budgetYear + 2)) ? pt.amount / 1000 : 0,
                                  year4Ammount = (pt.year == (budgetYear + 3)) ? pt.amount / 1000 : 0,
                                  year5Ammount = (pt.year == (budgetYear + 4)) ? pt.amount / 1000 : 0,
                                  year6Ammount = (pt.year == (budgetYear + 5)) ? pt.amount / 1000 : 0,
                                  year7Ammount = (pt.year == (budgetYear + 6)) ? pt.amount / 1000 : 0,
                                  year8Ammount = (pt.year == (budgetYear + 7)) ? pt.amount / 1000 : 0,
                                  year9Ammount = (pt.year == (budgetYear + 8)) ? pt.amount / 1000 : 0,
                                  year10Ammount = (pt.year == (budgetYear + 9)) ? pt.amount / 1000 : 0,
                                  year11Ammount = (pt.year == (budgetYear + 10)) ? pt.amount / 1000 : 0,
                                  year12Ammount = (pt.year == (budgetYear + 11)) ? pt.amount / 1000 : 0,
                                  year13Ammount = (pt.year == (budgetYear + 12)) ? pt.amount / 1000 : 0,
                                  year14Ammount = (pt.year == (budgetYear + 13)) ? pt.amount / 1000 : 0,
                                  year15Ammount = (pt.year == (budgetYear + 14)) ? pt.amount / 1000 : 0,
                                  year16Ammount = (pt.year == (budgetYear + 15)) ? pt.amount / 1000 : 0,
                                  year17Ammount = (pt.year == (budgetYear + 16)) ? pt.amount / 1000 : 0,
                                  year18Ammount = (pt.year == (budgetYear + 17)) ? pt.amount / 1000 : 0,
                                  year19Ammount = (pt.year == (budgetYear + 18)) ? pt.amount / 1000 : 0,
                                  year20Ammount = (pt.year == (budgetYear + 19)) ? pt.amount / 1000 : 0,
                                  changeYear1 = (pt.fk_user_adjustment_code == userAdjCode) && pt.year == budgetYear ? pt.amount / 1000 : 0,
                                  changeYear2 = (pt.fk_user_adjustment_code == userAdjCode) && pt.year == (budgetYear + 1) ? pt.amount / 1000 : 0,
                                  changeYear3 = (pt.fk_user_adjustment_code == userAdjCode) && pt.year == (budgetYear + 2) ? pt.amount / 1000 : 0,
                                  changeYear4 = (pt.fk_user_adjustment_code == userAdjCode) && pt.year == (budgetYear + 3) ? pt.amount / 1000 : 0,
                                  changeYear5 = (pt.fk_user_adjustment_code == userAdjCode) && pt.year == (budgetYear + 4) ? pt.amount / 1000 : 0,
                                  changeYear6 = (pt.fk_user_adjustment_code == userAdjCode) && pt.year == (budgetYear + 5) ? pt.amount / 1000 : 0,
                                  changeYear7 = (pt.fk_user_adjustment_code == userAdjCode) && pt.year == (budgetYear + 6) ? pt.amount / 1000 : 0,
                                  changeYear8 = (pt.fk_user_adjustment_code == userAdjCode) && pt.year == (budgetYear + 7) ? pt.amount / 1000 : 0,
                                  changeYear9 = (pt.fk_user_adjustment_code == userAdjCode) && pt.year == (budgetYear + 8) ? pt.amount / 1000 : 0,
                                  changeYear10 = (pt.fk_user_adjustment_code == userAdjCode) && pt.year == (budgetYear + 9) ? pt.amount / 1000 : 0,
                                  changeYear11 = (pt.fk_user_adjustment_code == userAdjCode) && pt.year == (budgetYear + 10) ? pt.amount / 1000 : 0,
                                  changeYear12 = (pt.fk_user_adjustment_code == userAdjCode) && pt.year == (budgetYear + 11) ? pt.amount / 1000 : 0,
                                  changeYear13 = (pt.fk_user_adjustment_code == userAdjCode) && pt.year == (budgetYear + 12) ? pt.amount / 1000 : 0,
                                  changeYear14 = (pt.fk_user_adjustment_code == userAdjCode) && pt.year == (budgetYear + 13) ? pt.amount / 1000 : 0,
                                  changeYear15 = (pt.fk_user_adjustment_code == userAdjCode) && pt.year == (budgetYear + 14) ? pt.amount / 1000 : 0,
                                  changeYear16 = (pt.fk_user_adjustment_code == userAdjCode) && pt.year == (budgetYear + 15) ? pt.amount / 1000 : 0,
                                  changeYear17 = (pt.fk_user_adjustment_code == userAdjCode) && pt.year == (budgetYear + 16) ? pt.amount / 1000 : 0,
                                  changeYear18 = (pt.fk_user_adjustment_code == userAdjCode) && pt.year == (budgetYear + 17) ? pt.amount / 1000 : 0,
                                  changeYear19 = (pt.fk_user_adjustment_code == userAdjCode) && pt.year == (budgetYear + 18) ? pt.amount / 1000 : 0,
                                  changeYear20 = (pt.fk_user_adjustment_code == userAdjCode) && pt.year == (budgetYear + 19) ? pt.amount / 1000 : 0,
                                  financingYear1Ammount = rl.line_item_id.ToString() != "1010" && (pt.year == budgetYear) ? pt.amount / 1000 : 0,
                                  financingYear2Ammount = rl.line_item_id.ToString() != "1010" && (pt.year == (budgetYear + 1)) ? pt.amount / 1000 : 0,
                                  financingYear3Ammount = rl.line_item_id.ToString() != "1010" && (pt.year == (budgetYear + 2)) ? pt.amount / 1000 : 0,
                                  financingYear4Ammount = rl.line_item_id.ToString() != "1010" && (pt.year == (budgetYear + 3)) ? pt.amount / 1000 : 0,
                                  financingYear5Ammount = rl.line_item_id.ToString() != "1010" && (pt.year == (budgetYear + 4)) ? pt.amount / 1000 : 0,
                                  financingYear6Ammount = rl.line_item_id.ToString() != "1010" && (pt.year == (budgetYear + 5)) ? pt.amount / 1000 : 0,
                                  financingYear7Ammount = rl.line_item_id.ToString() != "1010" && (pt.year == (budgetYear + 6)) ? pt.amount / 1000 : 0,
                                  financingYear8Ammount = rl.line_item_id.ToString() != "1010" && (pt.year == (budgetYear + 7)) ? pt.amount / 1000 : 0,
                                  financingYear9Ammount = rl.line_item_id.ToString() != "1010" && (pt.year == (budgetYear + 8)) ? pt.amount / 1000 : 0,
                                  financingYear10Ammount = rl.line_item_id.ToString() != "1010" && (pt.year == (budgetYear + 9)) ? pt.amount / 1000 : 0,
                                  financingPreviousBudgeted = rl.line_item_id.ToString() != "1010" && pt.year < budgetYear ? pt.amount / 1000 : 0,
                              }).ToListAsync();
            }
            return data;
        }

        public async Task<IEnumerable<DetailData>> GetProjectTransactionsAsync(int tenantId, string mainProjCode, int budgetYear, List<string> departmentCode)
        {
            var transData = await (from tpt in _tenantDbContext.tfp_proj_transactions
                                   join bc in _tenantDbContext.tfp_budget_changes on new { a = tpt.fk_tenant_id, b = tpt.fk_change_id }
                                                                              equals new { a = bc.fk_tenant_id, b = bc.pk_change_id }
                                   join ac in _tenantDbContext.tco_accounts.Where(x => x.dateFrom.Year <= budgetYear && x.dateTo.Year >= budgetYear)
                                                                                          on new { a = tpt.fk_tenant_id, b = tpt.fk_account_code }
                                                                                      equals new { a = ac.pk_tenant_id, b = ac.pk_account_code }
                                   join dep in _tenantDbContext.tco_departments.Where(x => x.year_from <= budgetYear && x.year_to >= budgetYear)
                                                                                              on new { a = tpt.fk_tenant_id, b = tpt.fk_department_code }
                                                                                          equals new { a = dep.fk_tenant_id, b = dep.pk_department_code }
                                   join fn in _tenantDbContext.tco_functions.Where(x => x.dateFrom.Year <= budgetYear && x.dateTo.Year >= budgetYear)
                                                                                                       on new { a = tpt.fk_tenant_id, b = tpt.fk_function_code }
                                                                                                   equals new { a = fn.pk_tenant_id, b = fn.pk_Function_code }
                                   join prj in _tenantDbContext.tco_projects.Where(x => x.date_from.Year <= budgetYear && x.date_to.Year >= budgetYear)
                                                                                                          on new { a = tpt.fk_tenant_id, b = tpt.fk_project_code }
                                                                                                      equals new { a = prj.fk_tenant_id, b = prj.pk_project_code }
                                   join mprj in _tenantDbContext.tco_main_projects.Where(x => x.budget_year_from.Year <= budgetYear && x.budget_year_to.Year >= budgetYear)
                                                                                                      on new { a = tpt.fk_tenant_id, b = prj.fk_main_project_code }
                                                                                                  equals new { a = mprj.fk_tenant_id, b = mprj.pk_main_project_code }
                                   join fr1 in _tenantDbContext.tco_free_dim_values.Where(x => x.free_dim_column == "free_dim_1")
                                                                                  on new { a = tpt.fk_tenant_id, b = tpt.free_dim_1 }
                                                                              equals new { a = fr1.fk_tenant_id, b = fr1.free_dim_code } into gFr1
                                   from gFrdm1 in gFr1.DefaultIfEmpty()
                                   join fr2 in _tenantDbContext.tco_free_dim_values.Where(x => x.free_dim_column == "free_dim_2")
                                                                                            on new { a = tpt.fk_tenant_id, b = tpt.free_dim_2 }
                                                                                        equals new { a = fr2.fk_tenant_id, b = fr2.free_dim_code } into gFr2
                                   from gFrdm2 in gFr2.DefaultIfEmpty()
                                   join fr3 in _tenantDbContext.tco_free_dim_values.Where(x => x.free_dim_column == "free_dim_3")
                                                                                               on new { a = tpt.fk_tenant_id, b = tpt.free_dim_3 }
                                                                                           equals new { a = fr3.fk_tenant_id, b = fr3.free_dim_code } into gFr3
                                   from gFrdm3 in gFr3.DefaultIfEmpty()
                                   join fr4 in _tenantDbContext.tco_free_dim_values.Where(x => x.free_dim_column == "free_dim_4")
                                                                                               on new { a = tpt.fk_tenant_id, b = tpt.free_dim_4 }
                                                                                           equals new { a = fr4.fk_tenant_id, b = fr4.free_dim_code } into gFr4
                                   from gFrdm4 in gFr4.DefaultIfEmpty()
                                   join usr_adj in _tenantDbContext.tco_user_adjustment_codes on new { a = tpt.fk_tenant_id, b = tpt.fk_user_adjustment_code }
                                                                                          equals new { a = usr_adj.fk_tenant_id, b = usr_adj.pk_adj_code } into gUAd
                                   from gUAdj in gUAd.DefaultIfEmpty()
                                   join adj in _tenantDbContext.tco_adjustment_codes on new { a = tpt.fk_tenant_id, b = tpt.fk_adjustment_code }
                                                                                 equals new { a = adj.fk_tenant_id, b = adj.pk_adjustment_code } into gAd
                                   from gAdj in gAd.DefaultIfEmpty()
                                   join altr in _tenantDbContext.tco_fp_alter_codes on new { a = tpt.fk_tenant_id, b = tpt.fk_alter_code }
                                                                                equals new { a = altr.fk_tenant_id, b = altr.pk_alter_code } into gAt
                                   from gAtr in gAt.DefaultIfEmpty()
                                   where tpt.fk_tenant_id == tenantId && mprj.pk_main_project_code == mainProjCode && !tpt.is_vat_row && departmentCode.Contains(tpt.fk_department_code)
                                   orderby tpt.fk_change_id, tpt.fk_account_code, tpt.fk_department_code, tpt.fk_function_code, tpt.fk_project_code,
                                          tpt.free_dim_1, tpt.free_dim_2, tpt.free_dim_3, tpt.free_dim_4, tpt.year
                                   select new DetailData
                                   {
                                       id = tpt.pk_id,
                                       transId = tpt.trans_id,
                                       changeId = tpt.fk_change_id,
                                       changeName = bc.budget_name,
                                       fk_account_code = tpt.fk_account_code,
                                       account_name = ac.display_name,
                                       fk_department_code = tpt.fk_department_code,
                                       department_name = dep.department_name,
                                       fk_function_code = tpt.fk_function_code,
                                       function_name = fn.display_name,
                                       project_name = prj.project_name,
                                       fk_project_code = tpt.fk_project_code,
                                       free_dim1_code = gFrdm1 == null ? string.Empty : gFrdm1.free_dim_code,
                                       free_dim1_name = gFrdm1 == null ? string.Empty : gFrdm1.description,
                                       free_dim2_code = gFrdm2 == null ? string.Empty : gFrdm2.free_dim_code,
                                       free_dim2_name = gFrdm2 == null ? string.Empty : gFrdm2.description,
                                       free_dim3_code = gFrdm3 == null ? string.Empty : gFrdm3.free_dim_code,
                                       free_dim3_name = gFrdm3 == null ? string.Empty : gFrdm3.description,
                                       free_dim4_code = gFrdm4 == null ? string.Empty : gFrdm4.free_dim_code,
                                       free_dim4_name = gFrdm4 == null ? string.Empty : gFrdm4.description,
                                       fk_user_adj_code = gUAdj == null ? string.Empty : gUAdj.pk_adj_code,
                                       user_adj_name = gUAdj == null ? string.Empty : gUAdj.pk_adj_code,
                                       fk_adj_code = gAdj == null ? string.Empty : gAdj.pk_adjustment_code,
                                       adj_name = gAdj == null ? string.Empty : gAdj.description,
                                       fk_alter_code = gAtr == null ? string.Empty : gAtr.pk_alter_code,
                                       alter_name = gAtr == null ? string.Empty : gAtr.alter_description,
                                       year = tpt.year,
                                       amount = tpt.amount,
                                       vatRate = tpt.vat_rate,
                                       vatRefund = tpt.vat_refund,
                                       description = tpt.description,
                                       budgetChangeYear = bc.budget_year,
                                       orgFlag = bc.org_budget_flag,
                                       updated_date = tpt.updated
                                   }).Distinct().ToListAsync();

            return transData;
        }

        public async Task<IEnumerable<InvestmentSideMenu>> GetInvestmentListAsync(int tenantId, int budgetYear, List<string> department)
        {
            var data = await (from mp in _tenantDbContext.tco_main_project_setup
                              join tm in _tenantDbContext.tco_main_projects on new { a = mp.fk_tenant_id, b = mp.pk_main_project_code }
                                                                        equals new { a = tm.fk_tenant_id, b = tm.pk_main_project_code }
                              join tp in _tenantDbContext.tco_projects on new { a = tm.fk_tenant_id, b = tm.pk_main_project_code }
                                                                   equals new { a = tp.fk_tenant_id, b = tp.fk_main_project_code }
                              join pt in _tenantDbContext.tfp_proj_transactions on new { a = tp.fk_tenant_id, b = tp.pk_project_code }
                                                                            equals new { a = pt.fk_tenant_id, b = pt.fk_project_code }
                              where tm.budget_year_from.Year <= budgetYear && budgetYear <= tm.budget_year_to.Year && tp.date_from.Year <= budgetYear && budgetYear <= tp.date_to.Year && mp.fk_tenant_id == tenantId && tm.inv_status.HasValue //&& tm.fk_department_code != null
                              && department.Contains(pt.fk_department_code)
                              select new InvestmentSideMenu()
                              {
                                  id = tm.pk_id,
                                  mainProjCode = mp.pk_main_project_code,
                                  mainProjDetails = tm.pk_main_project_code + "-" + tm.main_project_name,
                                  functionCode = pt.fk_function_code,
                                  status = tm.inv_status.Value,
                                  showMapIcon = (mp.position_lat != null && mp.position_lat != -1) && (mp.position_lat != 0 || mp.position_long != 0),
                                  progCode = tp.fk_prog_code,
                                  userAdjCode = pt.fk_user_adjustment_code
                              }).ToListAsync();
            return data;
        }

        public async Task<tco_counters> GetCounterValueAsync(int tenantId, string counterId)
        {
            return await _tenantDbContext.tco_counters.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.counter_id == counterId);
        }

        public async Task<IEnumerable<MainProjDropDown>> GetMainProjectCodesAsync(int tenantId, int budgetYear)
        {
            return await (from mp in _tenantDbContext.tco_main_projects
                          where mp.fk_tenant_id == tenantId && budgetYear >= mp.budget_year_from.Year && budgetYear <= mp.budget_year_to.Year
                          select new MainProjDropDown
                          {
                              id = mp.pk_id,
                              invStatus = mp.inv_status,
                              mainProjectCode = mp.pk_main_project_code,
                              mainProjectName = mp.main_project_name,
                              isTempProj = mp.is_temp
                          }).ToListAsync();
        }

        public IEnumerable<tco_projects> GetTcoProjectData(int tenantId)
        {
            return GetTcoProjectDataAsync(tenantId).GetAwaiter().GetResult();
        }

        public async Task<IEnumerable<tco_projects>> GetTcoProjectDataAsync(int tenantId)
        {
            return await _tenantDbContext.tco_projects.Where(x => x.fk_tenant_id == tenantId).ToListAsync();
        }

        public async Task<IEnumerable<DescPopUpSideMenu>> GetPopUpSideMenuDetailsAsync(int tenantId, int budgetYear)
        {
            List<DescPopUpSideMenu> budgetPhases = await (from tbp in _tenantDbContext.tco_budget_phase
                                                          join tbc in _tenantDbContext.tfp_budget_changes on new { a = tbp.fk_tenant_id, b = tbp.pk_budget_phase_id }
                                                                                                      equals new { a = tbc.fk_tenant_id, b = tbc.fk_budget_phase_id }
                                                          where tbc.fk_tenant_id == tenantId && tbc.budget_year == budgetYear
                                                          orderby tbp.sort_order ascending
                                                          select new DescPopUpSideMenu
                                                          {
                                                              budgetPhaseId = tbp.pk_budget_phase_id,
                                                              budgetPhaseDesc = tbp.description,
                                                              budgetPhaseStatus = tbp.status
                                                          }).ToListAsync();
            return budgetPhases;
        }

        public async Task<tco_investments_descriptions> GetInvestmentDescriptionAsync(int tenantId, int budgetYear, string mainProjCode, string descriptionType, Guid phaseId)
        {
            return await _tenantDbContext.tco_investments_descriptions.FirstOrDefaultAsync(x =>
                x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.fk_main_project_code == mainProjCode
                && x.description_type == descriptionType && x.fk_budget_phase_id == phaseId);
        }

        public async Task<IEnumerable<tco_investments_descriptions>> GetAllInvestmentDescriptionAsync(int tenantId,
            int budgetYear, string mainProjCode, string descriptionType, Guid phaseId)
        {
            return await _tenantDbContext.tco_investments_descriptions.Where(x =>
                x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.fk_main_project_code == mainProjCode
                && x.description_type == descriptionType && x.fk_budget_phase_id == phaseId).ToListAsync();
        }

        public async Task<IEnumerable<tco_projects>> GetProjectDataLinkedToMainProjCodeAsync(int tenantId, int budgetYear, string mainProjCode)
        {
            return await _tenantDbContext.tco_projects.Where(x =>
                    x.fk_tenant_id == tenantId &&
                    (x.fk_main_project_code == mainProjCode || x.fk_main_project_code == string.Empty ||
                     x.pk_project_code == mainProjCode) && budgetYear >= x.date_from.Year &&
                    budgetYear <= x.date_to.Year)
                .ToListAsync();
        }

        public async Task<tco_main_projects> GetTempMainProjDetailsAsync(int tenantId, int budgetYear, string tempMainProjCode)
        {
            return await _tenantDbContext.tco_main_projects.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && budgetYear >= x.budget_year_from.Year
                                                                     && x.budget_year_to.Year >= budgetYear && x.pk_main_project_code == tempMainProjCode && x.is_temp);
        }

        public tco_main_projects GetMainProjDetails(int tenantId, int budgetYear, string mainProjCode)
        {
            return GetMainProjDetailsAsync(tenantId, budgetYear, mainProjCode).GetAwaiter().GetResult();
        }

        public async Task<tco_main_projects> GetMainProjDetailsAsync(int tenantId, int budgetYear, string mainProjCode)
        {
            return await _tenantDbContext.tco_main_projects.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.budget_year_from.Year <= budgetYear
                                                                     && x.budget_year_to.Year >= budgetYear && x.pk_main_project_code == mainProjCode);
        }

        public async Task<tco_main_project_setup> GetTempMainProjSetUpDetailsAsync(int tenantId, int budgetYear, string tempMainProjCode)
        {
            return await _tenantDbContext.tco_main_project_setup.FirstOrDefaultAsync(x => x.pk_main_project_code == tempMainProjCode && x.fk_tenant_id == tenantId);
        }

        public async Task<tco_projects> GetTempProjDetailsAsync(int tenantId, int budgetYear, string tempProjectCode)
        {
            return await _tenantDbContext.tco_projects.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && budgetYear >= x.date_from.Year
                                                                         && x.date_to.Year >= budgetYear && x.fk_main_project_code == tempProjectCode && x.is_temp);
        }

        public tco_projects GetProjDetails(int tenantId, int budgetYear, string projectCode)
        {
            return GetProjDetailsAsync(tenantId, budgetYear, projectCode).GetAwaiter().GetResult();
        }

        public async Task<tco_projects> GetProjDetailsAsync(int tenantId, int budgetYear, string projectCode)
        {
            return await _tenantDbContext.tco_projects.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.date_from.Year <= budgetYear
                                                                         && x.date_to.Year >= budgetYear && x.pk_project_code == projectCode);
        }

        public async Task<IEnumerable<tfp_proj_transactions>> GetTransactionDetailsByMainProjCodeAsync(int tenantId, int budgetYear, string projectCode)
        {
            return await (from a in _tenantDbContext.tfp_proj_transactions
                          join b in _tenantDbContext.tfp_budget_changes on new { x = a.fk_tenant_id, y = a.fk_change_id }
                                                                    equals new { x = b.fk_tenant_id, y = b.pk_change_id }
                          where a.fk_tenant_id == tenantId && b.budget_year == budgetYear && a.fk_project_code == projectCode
                          select a).ToListAsync();
        }

        public async Task<IEnumerable<tco_inv_program>> GetProgramCodeListAsync(int tenantId, int budgetYear)
        {
            return await _tenantDbContext.tco_inv_program.Where(x => x.fk_tenant_id == tenantId).ToListAsync();
        }

        public bool VATAccount(string accountCode, int tenantId)
        {
            return VATAccountAsync(accountCode, tenantId).GetAwaiter().GetResult();
        }

        public async Task<bool> VATAccountAsync(string accountCode, int tenantId)
        {
            return await (from ta in _tenantDbContext.tco_accounts
                          join ka in _tenantDbContext.gco_kostra_accounts on ta.fk_kostra_account_code equals ka.pk_kostra_account_code
                          where ta.pk_account_code == accountCode && ta.pk_tenant_id == tenantId
                          select ka.is_vat_account).FirstOrDefaultAsync();
        }

        public List<KeyValuesBoolData> VATAccountList(List<string> accountCodeLst, int tenantId, int budgetYear)
        {
            return VATAccountListAsync(accountCodeLst, tenantId, budgetYear).GetAwaiter().GetResult();
        }

        public async Task<List<KeyValuesBoolData>> VATAccountListAsync(List<string> accountCodeLst, int tenantId, int budgetYear)
        {
            return await (from ta in _tenantDbContext.tco_accounts
                          join ka in _tenantDbContext.gco_kostra_accounts on ta.fk_kostra_account_code equals ka.pk_kostra_account_code
                          where accountCodeLst.Contains(ta.pk_account_code) && ta.pk_tenant_id == tenantId && ta.dateFrom.Year <= budgetYear && ta.dateTo.Year >= budgetYear
                          select new KeyValuesBoolData
                          {
                              Key = ta.pk_account_code,
                              Value = ka.is_vat_account && ka.type == "investment"
                          }).ToListAsync();
        }

        public async Task<List<tco_attribute_values>> GetChapterAttributeAsync(int tenantId, string attributeType, int status)
        {
            return await _tenantDbContext.tco_attribute_values.Where(x => x.fk_tenant_id == tenantId && x.attribute_type == attributeType && x.status == 1).ToListAsync();
        }

        public async Task<List<tco_relation_definition>> GetChapterAttributeRelationAsync(int tenantId, string attributeType, string relationType)
        {
            return await _tenantDbContext.tco_relation_definition.Where(x =>
                    x.fk_tenant_id == tenantId && x.attribute_type == attributeType && x.relation_type == relationType)
                .ToListAsync();
        }

        public IEnumerable<tco_application_flag> GetTcoApplicationFlagData(int tenantId, string flagName, int budgetYear, int orgLevel)
        {
            return GetTcoApplicationFlagDataAsync(tenantId, flagName, budgetYear, orgLevel).GetAwaiter().GetResult();
        }

        public async Task<IEnumerable<tco_application_flag>> GetTcoApplicationFlagDataAsync(int tenantId, string flagName, int budgetYear, int orgLevel)
        {
            return await _tenantDbContext.tco_application_flag.Where(x => x.fk_tenant_id == tenantId && x.flag_name == flagName && x.budget_year == budgetYear && x.flag_key_id == orgLevel.ToString()).ToListAsync();
        }

        public IEnumerable<ProjectGridData> GetProjectGridData(int tenantId, string mainProjCode, int budgetYear)
        {
            return GetProjectGridDataAsync(tenantId, mainProjCode, budgetYear).GetAwaiter().GetResult();
        }

        public async Task<IEnumerable<ProjectGridData>> GetProjectGridDataAsync(int tenantId, string mainProjCode, int budgetYear)
        {
            return await (from tp in _tenantDbContext.tco_projects
                          join tm in _tenantDbContext.tco_main_projects on new { a = tp.fk_tenant_id, b = tp.fk_main_project_code }
                                                                    equals new { a = tm.fk_tenant_id, b = tm.pk_main_project_code }
                          join tiv in _tenantDbContext.tco_inv_program on new { x = tp.fk_tenant_id, y = tp.fk_prog_code }
                                                                   equals new { x = tiv.fk_tenant_id, y = tiv.pk_prog_code } into tpg
                          from tvp in tpg.DefaultIfEmpty()
                          join tu in _tenantDbContext.vwUserDetails on tp.fk_responsible_id equals tu.pk_id into tu1
                          from tu2 in tu1.DefaultIfEmpty()
                          where (tp.active == 1 && tm.pk_main_project_code == mainProjCode && tp.fk_tenant_id == tenantId && tm.fk_tenant_id == tenantId)
                          && budgetYear >= tp.date_from.Year && budgetYear <= tp.date_to.Year   //Added budgetyear check as part of fix #66626
                          && budgetYear >= tm.budget_year_from.Year && budgetYear <= tm.budget_year_to.Year
                          select new ProjectGridData
                          {
                              projectCode = tp.pk_project_code,
                              projectName = tp.project_name,
                              programCode = tp.fk_prog_code,
                              programName = tvp == null ? string.Empty : tvp.pk_prog_code + " " + tvp.description,
                              pkUserId = tu2 == null ? 0 : tu2.pk_id,
                              responsible = tu2 == null ? string.Empty : tu2.first_name + " " + tu2.last_name,
                              vatRate = tp.vat_rate,
                              vatRefund = tp.vat_refund
                          }).ToListAsync();
        }

        public async Task<tco_newyear_Blist_inv_log> GetBListInvestmentInputDataAsync(int tenantId, int budgetYear, string mainProjectCode)
        {
            return await _tenantDbContext.tco_newyear_Blist_inv_log.FirstOrDefaultAsync(x =>
                x.fk_tenant_id == tenantId && x.budget_year == budgetYear &&
                x.fk_main_project_code == mainProjectCode);
        }

        public async Task<List<BListInvestment>> GetBListInvestmentGridDataAsync(int tenantId, int budgetYear)
        {
            List<BListInvestment> blistInv = await (from mp in _tenantDbContext.tco_main_projects
                                                    join p in _tenantDbContext.tco_projects on new { a = mp.fk_tenant_id, b = mp.pk_main_project_code }
                                                                                        equals new { a = p.fk_tenant_id, b = p.fk_main_project_code }
                                                    join pt in _tenantDbContext.tfp_proj_transactions on new { a = p.pk_project_code, b = p.fk_tenant_id }
                                                                                                  equals new { a = pt.fk_project_code, b = pt.fk_tenant_id }
                                                    join dp in _tenantDbContext.tco_departments.Where(x => x.status == 1)
                                                                                                on new { a = mp.fk_department_code, b = mp.fk_tenant_id }
                                                                                            equals new { a = dp.pk_department_code, b = dp.fk_tenant_id } into res
                                                    from dep in res.DefaultIfEmpty()
                                                    join fn in _tenantDbContext.tco_functions.Where(x => x.isActive)
                                                                                              on new { a = mp.fk_Function_code, b = mp.fk_tenant_id }
                                                                                          equals new { a = fn.pk_Function_code, b = fn.pk_tenant_id } into res1
                                                    from func in res1.DefaultIfEmpty()
                                                    join nybl in _tenantDbContext.tco_newyear_Blist_inv_log.Where(x => x.budget_year == budgetYear)
                                                                                                            on new { a = mp.fk_tenant_id, b = mp.pk_main_project_code }
                                                                                                        equals new { a = nybl.fk_tenant_id, b = nybl.fk_main_project_code } into res2
                                                    from blist in res2.DefaultIfEmpty()
                                                    where mp.inv_status == 3 && mp.fk_tenant_id == tenantId && budgetYear - 1 >= mp.budget_year_from.Year && budgetYear - 1 <= mp.budget_year_to.Year && mp.status == 1 && pt.year < budgetYear

                                                    group pt by new
                                                    {
                                                        department_name = dep == null ? string.Empty : dep.department_name,
                                                        display_name = func == null ? string.Empty : func.display_name,
                                                        pk_main_project_code = mp == null ? string.Empty : mp.pk_main_project_code,
                                                        main_project_name = mp == null ? string.Empty : mp.main_project_name,
                                                        next_year_status = blist == null ? 0 : blist.next_year_status,
                                                        pt.year
                                                    } into grp
                                                    orderby grp.Key.department_name, grp.Key.display_name, grp.Key.pk_main_project_code, grp.Key.main_project_name
                                                    select new
                                                    {
                                                        owningDepartment = grp.Key.department_name,
                                                        owningFunction = grp.Key.display_name,
                                                        code = grp.Key.pk_main_project_code,
                                                        investmentName = grp.Key.main_project_name,
                                                        nextYearStatus = grp.Key.next_year_status,
                                                        amount = grp.Sum(x => x.amount),
                                                        year = grp.Key.year
                                                    }).Where(x => x.amount != 0.00M).Select(x => new BListInvestment
                                                    {
                                                        owningDepartment = x.owningDepartment,
                                                        owningFunction = x.owningFunction,
                                                        code = x.code,
                                                        investmentName = x.investmentName,
                                                        nextYearStatus = x.nextYearStatus
                                                    }).Distinct().ToListAsync();
            return blistInv;
        }

        public async Task<IEnumerable<InvestmentSummaryGrid>> GetSummaryGridDataAsync(InvSummaryHelper invSummaryHelper)
        {
            if (!invSummaryHelper.isInMemory)
            {
                invSummaryHelper.lst_tfp_proj_transactions = await (from tfp in _tenantDbContext.tfp_proj_transactions
                                                                    where tfp.fk_tenant_id == invSummaryHelper.tenantId
                                                                    && invSummaryHelper.projectCode.Contains(tfp.fk_project_code)
                                                                    && invSummaryHelper.changeId.Contains(tfp.fk_change_id)
                                                                    select tfp).ToListAsync();

                invSummaryHelper.projects = await _tenantDbContext.tco_projects
                                              .Where(tp => tp.fk_tenant_id == invSummaryHelper.tenantId && (tp.date_from.Year <= invSummaryHelper.budgetYear && tp.date_to.Year >= invSummaryHelper.budgetYear))
                                              .ToListAsync();

                invSummaryHelper.alterCodes = await _tenantDbContext.tco_fp_alter_codes
                                                .Where(tp => tp.fk_tenant_id == invSummaryHelper.tenantId)
                                                .ToListAsync();

                invSummaryHelper.reportingLine = await _tenantDbContext.tmd_reporting_line
                                                .Where(tp => tp.fk_tenant_id == invSummaryHelper.tenantId && tp.report == "INVREPORT")
                                                .ToListAsync();

                invSummaryHelper.budgetChanges = await _tenantDbContext.tfp_budget_changes
                                                   .Where(tp => tp.fk_tenant_id == invSummaryHelper.tenantId)
                                                   .ToListAsync();
            }

            IEnumerable<InvestmentSummaryGrid> invDetails = (from tfp in invSummaryHelper.lst_tfp_proj_transactions
                                                             join tp in invSummaryHelper.projects on new { a = tfp.fk_tenant_id, b = tfp.fk_project_code.ToLower() }
                                                                                                  equals new { a = tp.fk_tenant_id, b = tp.pk_project_code.ToLower() }
                                                             join al in invSummaryHelper.alterCodes on new { a = tfp.fk_tenant_id, b = tfp.fk_alter_code }
                                                                                                        equals new { a = al.fk_tenant_id, b = al.pk_alter_code } into altr
                                                             from lAlter in altr.DefaultIfEmpty()
                                                             join gd in invSummaryHelper.reportingLine.Where(x => x.fk_tenant_id == invSummaryHelper.tenantId && x.report == "INVREPORT")
                                                                                                                    on new { a = tfp.fk_tenant_id, b = tfp.fk_account_code }
                                                                                                                equals new { a = gd.fk_tenant_id, b = gd.fk_account_code } into rep
                                                             from rel in rep.DefaultIfEmpty()
                                                             join bp in invSummaryHelper.budgetChanges on new { a = tfp.fk_tenant_id, b = tfp.fk_change_id }
                                                                                                        equals new { a = bp.fk_tenant_id, b = bp.pk_change_id }
                                                                                                        //where tfp.fk_tenant_id == tenantId
                                                                                                        //&& tp.date_from.Year <= budgetYear && tp.date_to.Year >= budgetYear
                                                                                                        //&& bp.budget_year == budgetYear
                                                                                                        //orderby bp.budget_year, bp.pk_change_id
                                                             select new InvestmentSummaryGrid
                                                             {
                                                                 accountData = tfp.fk_account_code,
                                                                 departmentData = tfp.fk_department_code,
                                                                 functionData = tfp.fk_function_code,
                                                                 projectDesc = tfp.fk_project_code + "-" + tp.project_name,
                                                                 projectData = tfp.fk_project_code,
                                                                 alterCodeDesc = lAlter == null ? string.Empty : tfp.fk_alter_code + "-" + lAlter.alter_description,
                                                                 alterCode = tfp.fk_alter_code,
                                                                 adjustmentCode = tfp.fk_adjustment_code,
                                                                 freeDim1 = tfp.free_dim_1,
                                                                 freeDim2 = tfp.free_dim_2,
                                                                 freeDim3 = tfp.free_dim_3,
                                                                 freeDim4 = tfp.free_dim_4,
                                                                 vatRate = Math.Round(tfp.vat_rate),
                                                                 vatRefund = Math.Round(tfp.vat_refund),
                                                                 accountType = rel != null ? rel.level_1_description : string.Empty,
                                                                 programCode = tp.fk_prog_code,
                                                                 changeBudgetYear = bp.budget_year,
                                                                 amount = tfp.amount,
                                                                 year = tfp.year,
                                                                 activeChange = tfp.fk_change_id == invSummaryHelper.activeChangeId,
                                                                 changeDesc = tfp.fk_change_id + "-" + bp.budget_name,
                                                                 changeId = tfp.fk_change_id,
                                                                 mainProjectCode = tp.fk_main_project_code,
                                                                 description = tfp.description,
                                                                 userAdjCode = tfp.fk_user_adjustment_code
                                                             }).OrderBy(a => a.changeBudgetYear).ThenBy(b => b.changeId).ToList();
            //Filter data
            invDetails = invDetails.Where(x => invSummaryHelper.projectCode.Contains(x.projectData.ToLower())).ToList();
            invDetails = invDetails.Where(x => invSummaryHelper.departments.Contains(x.departmentData)).ToList();
            //invDetails = invDetails.Where(x => changeId.Contains(x.changeId)).ToList();
            invDetails = invDetails.Where(x => !invSummaryHelper.operationsAccount.Contains(x.accountData)).ToList();

            return invDetails;
        }

        public async Task<IEnumerable<TcoActionTags>> GetTcoActionTagsDataAsync(int tenantId)
        {
            TenantDBContext dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await dbContext.tcoActionTags.Where(x => x.FkTenantId == tenantId).ToListAsync();
        }

        public async Task<IEnumerable<tco_service_values>> GetTcoServiceValuesDataAsync(int tenantId)
        {
            TenantDBContext dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await dbContext.tco_service_values.Where(x => x.fk_tenant_id == tenantId).ToListAsync();
        }

        public async Task<string> GetFPLevelOneFlagValueAsync(int tenantId)
        {   //get the finplan level
            TenantDBContext dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            var finplanLevel = await dbContext.vw_tco_parameters.FirstOrDefaultAsync(x =>
                x.fk_tenant_id == tenantId && x.param_name == "FINPLAN_LEVEL_1");

            return finplanLevel != null
                ? finplanLevel.param_value
                : string.Empty;
        }

        public IEnumerable<InvestmentAccountList> GetAccountData(int tenantId)
        {
            return GetAccountDataAsync(tenantId).GetAwaiter().GetResult();
        }

        public async Task<IEnumerable<InvestmentAccountList>> GetAccountDataAsync(int tenantId)
        {
            return await (from account in _tenantDbContext.tco_accounts
                          where account.pk_tenant_id == tenantId
                          select new InvestmentAccountList
                          {
                              fk_account_code = account.pk_account_code,
                              display_name = account.display_name,
                              kostra_account_code = account.fk_kostra_account_code
                          }).ToListAsync();
        }

        public async Task<IEnumerable<UpdateInvestment>> GetInvestmentDetailsAsync(int tenantId, string mainProjCode, int budgetYear,
            string paramValue)
        {
            return await (from a in _tenantDbContext.tco_main_projects
                          join b in _tenantDbContext.tco_main_project_setup on new { x = a.fk_tenant_id, y = a.pk_main_project_code }
                                                                        equals new { x = b.fk_tenant_id, y = b.pk_main_project_code } into leftMproj
                          from lMproj in leftMproj.DefaultIfEmpty()
                          join b in _tenantDbContext.tpl_tfp_investment_mapping on new { x = a.fk_tenant_id, y = a.pk_main_project_code }
                                                                            equals new { x = b.fk_tenant_id, y = b.fk_main_project_code } into mappingProj
                          from x1 in mappingProj.DefaultIfEmpty()
                          where a.fk_tenant_id == tenantId
                          && a.pk_main_project_code == mainProjCode
                          && a.budget_year_from.Year <= budgetYear
                          && a.budget_year_to.Year >= budgetYear
                          select new UpdateInvestment
                          {
                              mainProjCode = mainProjCode,
                              mainProjectNmae = a.main_project_name,
                              status = a.inv_status != null ? a.inv_status.Value : 0,
                              investmentPhase = a.fk_investment_phase_id != null ? a.fk_investment_phase_id.Value : -1,
                              startYear = lMproj != null ? lMproj.start_year : 0,
                              originalFinish = lMproj != null ? lMproj.original_finish_year : 0,
                              estimatedQuater = a.completion_date.Value,
                              departmentCode = paramValue != null && paramValue.Contains("org_id") ? a.fk_department_code : a.fk_Function_code,
                              priority = a.priority != null ? a.priority.Value : -1,
                              fdvCode = lMproj != null ? lMproj.fk_fdv_codes : string.Empty,
                              unitValue = lMproj != null ? lMproj.unit_value : null,
                              tagIds = lMproj != null ? lMproj.tags : string.Empty,
                              goalId = lMproj != null ? lMproj.goalId.ToString() : string.Empty,
                              targetId = lMproj != null ? lMproj.targetId.ToString() : string.Empty,
                              strategyId = lMproj != null ? lMproj.strategyId.ToString() : string.Empty,
                              projectManager = lMproj != null && lMproj.responsible != null ? lMproj.responsible.Value : 0,
                              prevActual = lMproj != null && lMproj.prev_actual != null ? lMproj.prev_actual.Value : 0,
                              position_lat = lMproj != null && lMproj.position_lat > 0 ? lMproj.position_lat : (decimal?)null,
                              position_long = lMproj != null && lMproj.position_long > 0 ? lMproj.position_long : (decimal?)null,
                              position_address = lMproj != null ? (lMproj.position_address != null ? lMproj.position_address : string.Empty) : string.Empty,
                              approvalReference = lMproj != null ? lMproj.approval_reference : string.Empty,
                              approval_ref_url = lMproj != null ? lMproj.approval_ref_url : string.Empty,
                              description = string.Empty,
                              isTempProj = a.is_temp,
                              partialFlag = a.partial_flag,
                              isPlanType = x1 != null,
                              sync_status = a.sync_status,
                              evaluationStatus = a.evaluation_status,
                          }).ToListAsync();
        }

        public tco_main_project_setup GetTcoMainProjectSetup(int tenantId, string mainProjCode)
        {
            return GetTcoMainProjectSetupAsync(tenantId, mainProjCode).GetAwaiter().GetResult();
        }

        public async Task<tco_main_project_setup> GetTcoMainProjectSetupAsync(int tenantId, string mainProjCode)
        {
            return await _tenantDbContext.tco_main_project_setup.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.pk_main_project_code == mainProjCode);
        }

        public IEnumerable<tfp_budget_changes> getBudgetChange(int tenantId, int budgetYear)
        {
            return getBudgetChangeAsync(tenantId, budgetYear).GetAwaiter().GetResult();
        }

        public async Task<IEnumerable<tfp_budget_changes>> getBudgetChangeAsync(int tenantId, int budgetYear)
        {
            return await _tenantDbContext.tfp_budget_changes.Where(x => x.fk_tenant_id == tenantId && x.budget_year <= budgetYear).ToListAsync();
        }

        public async Task<IEnumerable<tfp_budget_changes>> GetAllBudgetChangeAsync(int tenantId)
        {
            return await _tenantDbContext.tfp_budget_changes.Where(x => x.fk_tenant_id == tenantId).ToListAsync();
        }

        public async Task<IEnumerable<tco_adjustment_codes>> GetAllAdjustmentCodesAsync(int tenantId)
        {
            return await _tenantDbContext.tco_adjustment_codes.Where(x => x.fk_tenant_id == tenantId).ToListAsync();
        }

        public async Task<IEnumerable<tfp_proj_transactions>> GetTransactionByTransIdAsync(int tenantId, Guid transId)
        {
            return await _tenantDbContext.tfp_proj_transactions.Where(x => x.fk_tenant_id == tenantId && x.trans_id == transId && !x.is_vat_row).ToListAsync();
        }

        public IEnumerable<tmd_acc_defaults> GetAccountingDefaults(int tenantId, List<string> accountTypes, List<string> linkType, string orgVersion)
        {
            return GetAccountingDefaultsAsync(tenantId, accountTypes, linkType, orgVersion).GetAwaiter().GetResult();
        }

        public async Task DeleteExistingISYRecords(int tenantId, int forecastperiod, int budgetYear)
        {
            var existingRecords = await _tenantDbContext.tmr_proj_transactions.Where(x => x.fk_tenant_id == tenantId && x.forecast_period == forecastperiod && x.is_ISY_import && (x.year >= budgetYear || x.year == -1)).ToListAsync();
            if (existingRecords.Any())
            {
                await _tenantDbContext.BulkDeleteAsync(existingRecords);
                await _tenantDbContext.SaveChangesAsync();
            }
        }

        public async Task DeleteExistingISYRecordsForProject(int tenantId, int forecastperiod, int budgetYear, string projectCode)
        {
            var existingRecords = await _tenantDbContext.tmr_proj_transactions.Where(x => x.fk_tenant_id == tenantId && x.forecast_period == forecastperiod && x.fk_project_code == projectCode && (x.year >= budgetYear || x.year == -1) && x.is_change != 0).ToListAsync();
            if (existingRecords.Any())
            {
                await _tenantDbContext.BulkDeleteAsync(existingRecords);
                await _tenantDbContext.SaveChangesAsync();
            }
        }

        public async Task<IEnumerable<tmd_acc_defaults>> GetAccountingDefaultsAsync(int tenantId, List<string> accountTypes, List<string> linkType, string orgVersion)
        {
            var defaultAccounts = await (from tad in _tenantDbContext.tmd_acc_defaults
                                         where tad.fk_tenant_id == tenantId && accountTypes.Contains(tad.acc_type)
                                               && tad.module == "INV" && linkType.Contains(tad.link_type)
                                               && tad.fk_org_version == orgVersion
                                         select tad).ToListAsync();
            return defaultAccounts;
        }

        public async Task<IEnumerable<tco_proj_version>> GetProjectVerionsAsync(int tenantId)
        {
            return await _tenantDbContext.tco_proj_version.Where(x => x.fk_tenant_id == tenantId && x.active == 1).ToListAsync();
        }

        public async Task<IEnumerable<tco_proj_level>> GetProjectLevelAsync(int tenantId, string projVersion)
        {
            return await _tenantDbContext.tco_proj_level.Where(x => x.fk_tenant_id == tenantId && x.projectVersion == projVersion).ToListAsync();
        }

        public async Task<tco_proj_level> GetProjectLevelAsync(int tenantId, int levelId, string projVersion)
        {
            return await _tenantDbContext.tco_proj_level.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.projLevel == levelId && x.projectVersion == projVersion);
        }

        public async Task<tco_proj_level> GetProjectLevelAsync(int tenantId, int pkId)
        {
            return await _tenantDbContext.tco_proj_level.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.pk_id == pkId);
        }

        public async Task<IEnumerable<tco_proj_hierarchy>> GetProjectHierarchyAsync(int tenantId, string projVersion)
        {
            return await _tenantDbContext.tco_proj_hierarchy.Where(x => x.fk_tenant_id == tenantId && x.fk_proj_version == projVersion).ToListAsync();
        }

        public async Task<IEnumerable<tco_proj_level_data>> GetProjectLevelDataAsync(int tenantId, string projVersion)
        {
            return await _tenantDbContext.tco_proj_level_data.Where(x => x.fk_tenant_id == tenantId && x.projectVersion == projVersion).ToListAsync();
        }

        public async Task<tco_proj_level_data> GetProjectLevelDataAsync(int tenantId, int pkId)
        {
            return await _tenantDbContext.tco_proj_level_data.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.pk_id == pkId);
        }

        public async Task DeleteTransactionAsync(int tenantId, int year, Guid pkId)
        {
            List<tfp_proj_transactions> existingData = await _tenantDbContext.tfp_proj_transactions.Where(x => x.fk_tenant_id == tenantId && x.year == year && x.fk_proj_trans_id == pkId).ToListAsync();
            if (existingData.Any())
            {
                _tenantDbContext.tfp_proj_transactions.RemoveRange(existingData);
            }
        }

        public async Task DeleteProjLevelAsync(int pkId, int tenantId)
        {
            tco_proj_level existingData = await _tenantDbContext.tco_proj_level.FirstOrDefaultAsync(x => x.pk_id == pkId && x.fk_tenant_id == tenantId);

            //Delete connected level Data
            var levelExistingData = await _tenantDbContext.tco_proj_level_data
                .Where(x => x.projLevel == existingData.projLevel && x.fk_tenant_id == tenantId).ToListAsync();
            if (levelExistingData.Any())
            {
                _tenantDbContext.tco_proj_level_data.RemoveRange(levelExistingData);
            }
            //delete project level
            if (existingData != null)
            {
                _tenantDbContext.tco_proj_level.Remove(existingData);
            }
        }

        public async Task DeleteProjLevelDataAsync(int pkId, int tenantId)
        {
            tco_proj_level_data existingData = await _tenantDbContext.tco_proj_level_data.FirstOrDefaultAsync(x => x.pk_id == pkId && x.fk_tenant_id == tenantId);
            if (existingData != null)
            {
                _tenantDbContext.tco_proj_level_data.Remove(existingData);
            }
        }

        public async Task DeleteMainProjectAsync(string tempMainProjCode, int budgetYear, int tenantId)
        {
            _tenantDbContext.tco_main_projects.RemoveRange(await _tenantDbContext.tco_main_projects
                .Where(x => x.fk_tenant_id == tenantId && x.pk_main_project_code == tempMainProjCode && x.is_temp)
                .ToListAsync());
        }

        public async Task DeleteMainProjectDescriptionAsync(string mainProjCode, int budgetYear, int tenantId, List<int> pkDescIds)
        {
            _tenantDbContext.tco_investments_descriptions.RemoveRange(await _tenantDbContext
                .tco_investments_descriptions.Where(x =>
                    x.budget_year == budgetYear && x.fk_tenant_id == tenantId &&
                    x.fk_main_project_code == mainProjCode && pkDescIds.Contains(x.pk_id)).ToListAsync());
        }

        public async Task DeleteProjectSetupAsync(string tempMainProjCode, int tenantId)
        {
            _tenantDbContext.tco_main_project_setup.RemoveRange(await _tenantDbContext.tco_main_project_setup.Where(x =>
                x.pk_main_project_code == tempMainProjCode && x.fk_tenant_id == tenantId).ToListAsync());
        }

        public async Task DeleteTransactionBasedOnTransIdAsync(Guid transId, int tenantId)
        {
            _tenantDbContext.tfp_proj_transactions.RemoveRange(
                await _tenantDbContext.tfp_proj_transactions.Where(x => x.trans_id == transId && x.fk_tenant_id == tenantId).ToListAsync());
        }

        public async Task<IEnumerable<tco_projects_setup>> GetProjectsSetUpDataAsync(int tenantId)
        {
            var projectsSetUpData = await _tenantDbContext.tco_projects_setup.Where(x => x.fk_tenant_id == tenantId).ToListAsync();
            return projectsSetUpData;
        }

        public async Task<IEnumerable<tfp_budget_changes>> GetAllBudgetChangeAsync(int tenantId, int orgBudget, int budgetYear)
        {
            return await _tenantDbContext.tfp_budget_changes.Where(x =>
                    x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.org_budget_flag == orgBudget)
                .ToListAsync();
        }

        public IEnumerable<tfp_budget_changes> GetAllBudgetChangeForOverViewGrid(int tenantId, int orgBudget, int budgetYear)
        {
            return GetAllBudgetChangeForOverViewGridAsync(tenantId, orgBudget, budgetYear).GetAwaiter().GetResult();
        }

        public async Task<IEnumerable<tfp_budget_changes>> GetAllBudgetChangeForOverViewGridAsync(int tenantId, int orgBudget, int budgetYear)
        {
            IEnumerable<tfp_budget_changes> budgetChagesDataCurrentYear = await _tenantDbContext.tfp_budget_changes.Where(x =>
                x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.org_budget_flag == orgBudget).ToListAsync();

            IEnumerable<tfp_budget_changes> budgetChagesDataPreviousYears =
                await _tenantDbContext.tfp_budget_changes.Where(x =>
                    x.fk_tenant_id == tenantId && x.budget_year < budgetYear).ToListAsync();
            return (budgetChagesDataPreviousYears.Union(budgetChagesDataCurrentYear));
        }

        public async Task<IEnumerable<tco_projects>> GetProjectDataLinkedToMainProjCodeSummaryDataAsync(int tenantId, int budgetYear, string mainProjCode)
        {
            return await _tenantDbContext.tco_projects.Where(x =>
                x.fk_tenant_id == tenantId &&
                (x.fk_main_project_code == mainProjCode || x.pk_project_code == mainProjCode) &&
                budgetYear >= x.date_from.Year && budgetYear <= x.date_to.Year).ToListAsync();
        }

        public async Task<List<tfp_proj_transactions>> GetDataTfpProjTrans(int tenantId, List<string> projectCode, List<int> changeId)
        {
            var dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            var data = await (from tfp in dbContext.tfp_proj_transactions
                              where tfp.fk_tenant_id == tenantId
                              && projectCode.Contains(tfp.fk_project_code)
                              && changeId.Contains(tfp.fk_change_id)
                              select tfp).ToListAsync();

            return data;
        }

        public async Task<List<tco_projects>> GetDataTcoProjectsAsync(int tenantId, int budgetYear)
        {
            var dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            var data = await dbContext.tco_projects
                                             .Where(tp => tp.fk_tenant_id == tenantId && (tp.date_from.Year <= budgetYear && tp.date_to.Year >= budgetYear))
                                             .ToListAsync();
            return data;
        }

        public async Task<List<tco_fp_alter_codes>> GetDataTcoAlterCodes(int tenantId)
        {
            var dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            var data = await dbContext.tco_fp_alter_codes
                                                .Where(tp => tp.fk_tenant_id == tenantId)
                                                .ToListAsync();
            return data;
        }

        public async Task<List<tmd_reporting_line>> GetDataTmdReportingLine(int tenantId)
        {
            var dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            var data = await dbContext.tmd_reporting_line
                                             .Where(tp => tp.fk_tenant_id == tenantId && tp.report == "INVREPORT")
                                             .ToListAsync();
            return data;
        }

        public async Task<List<tfp_budget_changes>> GetDataTftBudgetChanges(int tenantId)
        {
            var dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            var data = await dbContext.tfp_budget_changes
                                                   .Where(tp => tp.fk_tenant_id == tenantId)
                                                   .ToListAsync();
            return data;
        }

        public async Task<IEnumerable<ProjectTypeUsage>> GetProjectsTypeDataDBAsync(int tenantId)
        {
            var projectsSetUpData = await _tenantDbContext.tco_projects_type_setup
                .Where(x => x.fk_tenant_id == tenantId).Select(x => new ProjectTypeUsage()
                {
                    pkId = x.pk_id,
                    projectIdFrom = x.project_id_from,
                    projectIdTo = x.project_id_to,
                    projectTypeName = x.Project_type_name,
                    usageType = x.usage_type
                }).ToListAsync();
            return projectsSetUpData;
        }

        public async Task<IEnumerable<tco_projects_type_setup>> GetAllProjectsTypeDataDBAsync(int tenantId)
        {
            var projectsSetUpData = await _tenantDbContext.tco_projects_type_setup.Where(x => x.fk_tenant_id == tenantId).ToListAsync();
            return projectsSetUpData;
        }

        public async Task DeleteProjectUsageDataAsync(int PkId, int tenantId)
        {
            _tenantDbContext.tco_projects_type_setup.RemoveRange(await _tenantDbContext.tco_projects_type_setup
                .Where(x => x.pk_id == PkId && x.fk_tenant_id == tenantId).ToListAsync());
        }

        public async Task<IEnumerable<string>> GetAllValidProjectIdsAsync(int tenantId)
        {
            return await _tenantDbContext.tco_projects.Where(x => x.fk_tenant_id == tenantId).Select(x => x.pk_project_code).Distinct().ToListAsync();
        }

        public async Task<tfp_trans_detail> GetTransDetailByTransactionIdAsync(int tenantId, Guid transId)
        {
            return await _tenantDbContext.tfp_trans_detail.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.inv_trans_id == transId);
        }

        public async Task<int> DeleteTransDetailBasedOnTransIdAsync(Guid transId, int tenantId)
        {
            int actionId = 0;
            var actionDetail = await _tenantDbContext.tfp_trans_detail.FirstOrDefaultAsync(x => x.inv_trans_id == transId && x.fk_tenant_id == tenantId);
            if (actionDetail != null)
            {
                actionId = actionDetail.fk_action_id;
                _tenantDbContext.tfp_trans_detail.Remove(actionDetail);
            }
            return actionId;
        }

        public async Task DeleteTransHeaderDataBasedOnActionIdAsync(int tenantId, int actionId)
        {
            var transDetailData = await _tenantDbContext.tfp_trans_detail.FirstOrDefaultAsync(x => x.fk_action_id == actionId && x.fk_tenant_id == tenantId);
            if (transDetailData == null && actionId != 0)
            {
                _tenantDbContext.tfp_trans_header.Remove(
                    await _tenantDbContext.tfp_trans_header.FirstOrDefaultAsync(x =>
                        x.pk_action_id == actionId && x.fk_tenant_id == tenantId));
            }
        }

        public async Task<tfp_trans_header> GetTransHeaderDetailByMainProjectCodeAsync(int tenantId, string mainProjectCode, int budgetYear)
        {
            var actionDetail = await _tenantDbContext.tfp_trans_detail.FirstOrDefaultAsync(x =>
                x.fk_main_project_code == mainProjectCode && x.fk_tenant_id == tenantId && x.budget_year == budgetYear);
            int actionId = actionDetail != null ? actionDetail.fk_action_id : 0;
            return await _tenantDbContext.tfp_trans_header.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.pk_action_id == actionId);
        }

        public List<tco_proj_responsible> GetAllResponsibleUsers(int tenantId)
        {
            return GetAllResponsibleUsersAsync(tenantId).GetAwaiter().GetResult();
        }

        public async Task<List<tco_proj_responsible>> GetAllResponsibleUsersAsync(int tenantId)
        {
            return await _tenantDbContext.tco_proj_responsible.Where(x => x.fk_tenant_id == tenantId).Distinct().ToListAsync();
        }

        public async Task<List<tco_proj_responsible>> GetProjectResponsibleUsersAsync(int tenantId, string projectVersion)
        {
            return await _tenantDbContext.tco_proj_responsible.Where(x => x.fk_tenant_id == tenantId && x.fk_proj_version == projectVersion).ToListAsync();
        }

        public async Task DeleteProjectResponsibleUserAsync(int tenantId, string projectVersion, string level, string levelId = null)
        {
            if (levelId == null)
            {
                _tenantDbContext.tco_proj_responsible.RemoveRange(await _tenantDbContext.tco_proj_responsible
                    .Where(x => x.fk_tenant_id == tenantId && x.fk_proj_version == projectVersion && x.level == level)
                    .ToListAsync());
            }
            else
            {
                _tenantDbContext.tco_proj_responsible.RemoveRange(await _tenantDbContext.tco_proj_responsible
                    .Where(x => x.fk_tenant_id == tenantId && x.fk_proj_version == projectVersion && x.level == level &&
                                x.level_id == levelId).ToListAsync());
            }
        }

        public async Task<List<tco_main_projects>> GetMainProjectsAsync(int tenantId)
        {
            return await _tenantDbContext.tco_main_projects.Where(x => x.fk_tenant_id == tenantId).ToListAsync();
        }

        public async Task<List<tco_proj_temp_conversion>> GetTempMainProjectsAsync(int tenantId, string type)
        {
            return await _tenantDbContext.tco_proj_temp_conversion.Where(x => x.fk_tenant_id == tenantId && x.type == type).ToListAsync();
        }

        public async Task<List<tco_main_projects>> GetTempLinkedMainProjectAsync(int tenantId)
        {
            var tempMainProjects = await GetTempMainProjectsAsync(tenantId, "MP");
            var newTempMainProjects = tempMainProjects.Select(x => x.new_id).ToList();
            newTempMainProjects.AddRange(tempMainProjects.Select(x => x.temp_id).ToList());
            return await _tenantDbContext.tco_main_projects.Where(x => x.fk_tenant_id == tenantId && newTempMainProjects.Contains(x.pk_main_project_code)).ToListAsync();
        }

        public async Task<List<tco_projects>> GetTempMPLinkedProjectAsync(int tenantId, List<string> tempIdList)
        {
            return await _tenantDbContext.tco_projects.Where(x => x.fk_tenant_id == tenantId && tempIdList.Contains(x.fk_main_project_code)).ToListAsync();
        }

        public async Task<List<tco_projects>> GetTempLinkedProjectAsync(int tenantId)
        {
            var tempProjects = await GetTempMainProjectsAsync(tenantId, "P");
            var newTempProjects = tempProjects.Select(x => x.new_id).ToList();
            newTempProjects.AddRange(tempProjects.Select(x => x.temp_id).ToList());
            return await _tenantDbContext.tco_projects.Where(x => x.fk_tenant_id == tenantId && newTempProjects.Contains(x.pk_project_code)).ToListAsync();
        }

        public async Task DeleteTempMainProjectsFromTcoMainProjectsAsync(int tenantId, string tempId)
        {
            _tenantDbContext.tco_main_projects.RemoveRange(await _tenantDbContext.tco_main_projects
                .Where(x => x.pk_main_project_code == tempId && x.fk_tenant_id == tenantId).ToListAsync());
        }

        public async Task DeleteTempDataFromTcoProjTempConversionAsync(int tenantId, string tempId, string type)
        {
            _tenantDbContext.tco_proj_temp_conversion.Remove(
                await _tenantDbContext.tco_proj_temp_conversion.FirstOrDefaultAsync(x =>
                    x.fk_tenant_id == tenantId && x.temp_id == tempId && x.type == type));
        }

        public async Task<List<tfp_proj_transactions>> GetTempProjTransactionsAsync(int tenantId, List<string> tempProjIds)
        {
            return await _tenantDbContext.tfp_proj_transactions
                .Where(x => x.fk_tenant_id == tenantId && tempProjIds.Contains(x.fk_project_code)).ToListAsync();
        }

        public async Task<List<tmr_proj_data_warehouse>> GetTempProjWhDataAsync(int tenantId, List<string> tempProjIds)
        {
            return await _tenantDbContext.tmr_proj_data_warehouse
                .Where(x => x.fk_tenant_id == tenantId && tempProjIds.Contains(x.fk_project_code)).ToListAsync();
        }

        public async Task<List<tmr_proj_transactions>> GetTempProjTMRTransactionsAsync(int tenantId, List<string> tempProjIds)
        {
            return await _tenantDbContext.tmr_proj_transactions
                .Where(x => x.fk_tenant_id == tenantId && tempProjIds.Contains(x.fk_project_code)).ToListAsync();
        }

        public async Task<List<tco_proj_responsible>> GetTempProjResponsibleAsync(int tenantId, List<string> tempProjIds, string level)
        {
            return await _tenantDbContext.tco_proj_responsible.Where(x =>
                x.fk_tenant_id == tenantId && tempProjIds.Contains(x.level_id) && x.level == level).ToListAsync();
        }

        public async Task DeleteTempProjectsFromTcoProjectsAsync(int tenantId, string tempId)
        {
            _tenantDbContext.tco_projects.RemoveRange(await _tenantDbContext.tco_projects
                .Where(x => x.fk_tenant_id == tenantId && x.pk_project_code == tempId).ToListAsync());
        }

        public async Task<List<tco_investments>> GetTempMainProjTcoInvestmentAsync(int tenantId, List<string> tempMainProjIds)
        {
            return await _tenantDbContext.tco_investments
                .Where(x => x.fk_tenant_id == tenantId && tempMainProjIds.Contains(x.fk_main_project_code))
                .ToListAsync();
        }

        public async Task<List<tfp_trans_detail>> GetTempProjTFPTransDetailAsync(int tenantId, List<string> tempProjIds)
        {
            return await _tenantDbContext.tfp_trans_detail
                .Where(x => x.fk_tenant_id == tenantId && tempProjIds.Contains(x.project_code)).ToListAsync();
        }

        public async Task<List<tfp_temp_detail>> GetTempProjTempTransDetailAsync(int tenantId, List<string> tempProjIds)
        {
            return await _tenantDbContext.tfp_temp_detail.Where(x => x.fk_tenant_id == tenantId && tempProjIds.Contains(x.project_code)).ToListAsync();
        }

        public async Task<List<tco_investment_detail>> GetTempProjTcoInvDetailAsync(int tenantId, List<string> tempProjIds)
        {
            return await _tenantDbContext.tco_investment_detail
                .Where(x => x.fk_tenant_id == tenantId.ToString() && tempProjIds.Contains(x.fk_project_code))
                .ToListAsync();
        }

        public async Task<List<tfp_inv_transactions>> GetTempProjTfpInvTransAsync(int tenantId, List<string> tempProjIds)
        {
            return await _tenantDbContext.tfp_inv_transactions.Where(x => x.fk_tenant_id == tenantId && tempProjIds.Contains(x.fk_project_code)).ToListAsync();
        }

        public async Task<List<tco_org_hierarchy>> GetOrgDetailForDepartmentAsync(int tenantId, List<string> departmentList, string orgVersion)
        {
            return await _tenantDbContext.tco_org_hierarchy.Where(x =>
                x.fk_tenant_id == tenantId && departmentList.Contains(x.fk_department_code) &&
                x.fk_org_version == orgVersion).ToListAsync();
        }

        public async Task<List<tco_service_values>> GetServiceDetailForFunctionAsync(int tenantId, List<string> functionList)
        {
            return await _tenantDbContext.tco_service_values
                .Where(x => x.fk_tenant_id == tenantId && functionList.Contains(x.fk_function_code)).ToListAsync();
        }

        public async Task<tco_org_level> GetOrgLevelAsync(int tenantId, string orgVersion)
        {
            return await _tenantDbContext.tco_org_level.FirstOrDefaultAsync(x =>
                x.fk_tenant_id == tenantId && x.fk_org_version == orgVersion && x.fp_flag == 1);
        }

        public async Task<IEnumerable<InvOperationExpenseHelper>> GetInvOperationExpenseDataListAsync(int tenantId, List<string> mainProjectCodeList, int budgetYear)
        {
            return await (from d in _tenantDbContext.tco_projects
                          join td in _tenantDbContext.tfp_trans_detail on new { a = d.pk_project_code, b = d.fk_tenant_id }
                                                                   equals new { a = td.project_code, b = td.fk_tenant_id }
                          where mainProjectCodeList.Contains(d.fk_main_project_code) && d.fk_tenant_id == tenantId && td.budget_year == budgetYear
                          select new InvOperationExpenseHelper
                          {
                              mainProjectCode = d.fk_main_project_code,
                              hasOperationalExpense = true
                          }).ToListAsync();
        }

        public async Task<IEnumerable<tco_user_adjustment_codes>> GetUserAdjustmentCodeAsync(int tenantId, int forecastPeriod, List<string> orgValue)
        {
            if (orgValue.Any())
            {
                if (forecastPeriod == 0)
                    return await _tenantDbContext.tco_user_adjustment_codes.Where(x =>
                            x.fk_tenant_id == tenantId && orgValue.Contains(x.org_level_value) && x.inv_adj_code)
                        .ToListAsync();
                else
                    return await _tenantDbContext.tco_user_adjustment_codes.Where(x =>
                        x.fk_tenant_id == tenantId && x.forecast_period == forecastPeriod &&
                        orgValue.Contains(x.org_level_value) && x.inv_adj_code).ToListAsync();
            }
            else
            {
                if (forecastPeriod == 0)
                    return await _tenantDbContext.tco_user_adjustment_codes
                        .Where(x => x.fk_tenant_id == tenantId && x.inv_adj_code).ToListAsync();
                else
                    return await _tenantDbContext.tco_user_adjustment_codes.Where(x =>
                            x.fk_tenant_id == tenantId && x.forecast_period == forecastPeriod && x.inv_adj_code)
                        .ToListAsync();
            }
        }

        public async Task<IEnumerable<tco_user_adjustment_codes>> GetUserAdjustmentCodeAsync(int tenantId, int forecastPeriod, List<string> orgValue, int changeId)
        {
            if (orgValue.Any())
            {
                if (forecastPeriod == 0)
                    return await _tenantDbContext.tco_user_adjustment_codes.Where(x =>
                        x.fk_tenant_id == tenantId && orgValue.Contains(x.org_level_value) && x.inv_adj_code &&
                        x.fk_change_id == changeId).ToListAsync();
                else
                    return await _tenantDbContext.tco_user_adjustment_codes.Where(x =>
                            x.fk_tenant_id == tenantId && x.forecast_period == forecastPeriod &&
                            orgValue.Contains(x.org_level_value) && x.inv_adj_code && x.fk_change_id == changeId)
                        .ToListAsync();
            }
            else
            {
                if (forecastPeriod == 0)
                    return await _tenantDbContext.tco_user_adjustment_codes.Where(x =>
                        x.fk_tenant_id == tenantId && x.inv_adj_code && x.fk_change_id == changeId).ToListAsync();
                else
                    return await _tenantDbContext.tco_user_adjustment_codes.Where(x =>
                        x.fk_tenant_id == tenantId && x.inv_adj_code && x.forecast_period == forecastPeriod &&
                        x.fk_change_id == changeId).ToListAsync();
            }
        }

        public async Task<tco_user_adjustment_codes> GetAdustmentCodeDetailsAsync(string adjustmentCode, int tenantId)
        {
            return await _tenantDbContext.tco_user_adjustment_codes.FirstOrDefaultAsync(x => x.pk_adj_code == adjustmentCode
                                                       && x.fk_tenant_id == tenantId && x.inv_adj_code);
        }

        public async Task<IEnumerable<tfp_proj_transactions>> GetTransactionforAdjustmentCodeAsync(string adjustmentCode, int tenantId)
        {
            return await _tenantDbContext.tfp_proj_transactions.Where(x =>
                    x.fk_tenant_id == tenantId && x.fk_user_adjustment_code == adjustmentCode && x.year != -1)
                .ToListAsync();
        }

        public async Task<IEnumerable<SumPerMainProject>> GetTransactionforMainProjAdjustmentCodeAsync(string adjustmentCode, int tenantId)
        {
            return await (from pt in _tenantDbContext.tfp_proj_transactions
                          join mp in _tenantDbContext.tco_projects on pt.fk_project_code equals mp.pk_project_code
                          where pt.fk_tenant_id == tenantId && pt.fk_user_adjustment_code == adjustmentCode && pt.year != -1
                          group pt by new { mp.fk_main_project_code, pt.year } into grp
                          select new SumPerMainProject
                          {
                              mainprojectCode = grp.Key.fk_main_project_code,
                              year = grp.Key.year,
                              amount = grp.Sum(x => x.amount)
                          }).ToListAsync();
        }

        public async Task<IEnumerable<AdjusmentCodePopup>> GetAdjustmentCodePopupDataAsync(int tenantId, UserAdjustmentCodeInput input, List<string> departmentCode)
        {
            List<int> yearValues = new List<int> { input.budgetYear - 1, input.budgetYear, input.budgetYear + 1, input.budgetYear + 2, input.budgetYear + 3 };
            List<int> statusToInclude = new List<int> { 0, 1, 2, 7, 8 };

            var tfpProjTransactionsTask = GetAdjustmentCodeProjTransAsync(tenantId, input, departmentCode, yearValues);
            var tcoUserAdjustmentCodesTask = GetTcoUserAdjustmentCodesAsync(tenantId);
            var tcoProjectsTask = GetTcoProjectsAsync(tenantId, input);
            var tcoMainProjectsTask = GetTcoMainProjectsAsync(tenantId, input);
            var vwUserDetailsTask = GetVwUserDetailsAsync();

            await Task.WhenAll(tfpProjTransactionsTask, tcoUserAdjustmentCodesTask, tcoProjectsTask, tcoMainProjectsTask, vwUserDetailsTask);

            var tfpProjTransactions = await tfpProjTransactionsTask;
            var tcoUserAdjustmentCodes = await tcoUserAdjustmentCodesTask;
            var tcoProjects = await tcoProjectsTask;
            var tcoMainProjects = await tcoMainProjectsTask;
            var vwUserDetails = await vwUserDetailsTask;

            var data = (from pt in tfpProjTransactions.Where(x => x.fk_user_adjustment_code != string.Empty)
                        join us in tcoUserAdjustmentCodes.Where(x => x.inv_adj_code)
                                                                                    on new { a = pt.fk_user_adjustment_code, b = pt.fk_tenant_id }
                                                                                equals new { a = us.pk_adj_code, b = us.fk_tenant_id }
                        join prj in tcoProjects.Where(x => x.date_from.Year <= input.budgetYear && x.date_to.Year >= input.budgetYear)
                                                                                on new { a = pt.fk_tenant_id, b = pt.fk_project_code }
                                                                            equals new { a = prj.fk_tenant_id, b = prj.pk_project_code }
                        join tm in tcoMainProjects.Where(x => x.budget_year_from.Year <= input.budgetYear && x.budget_year_to.Year >= input.budgetYear)
                                                                                        on new { a = prj.fk_tenant_id, b = prj.fk_main_project_code }
                                                                                    equals new { a = tm.fk_tenant_id, b = tm.pk_main_project_code } into lftMpj
                        from mpj in lftMpj.Where(x => x.fk_tenant_id == tenantId).DefaultIfEmpty()
                        where pt.fk_change_id == input.changeId && pt.fk_tenant_id == tenantId && departmentCode.Contains(pt.fk_department_code) && yearValues.Contains(pt.year)
                        join ud in vwUserDetails on new { a = pt.updated_by, b = pt.fk_tenant_id }
                                                    equals new { a = ud.pk_id, b = ud.tenant_id }
                        select new
                        {
                            accountCode = pt.fk_account_code,
                            status_boolValue = us.status,
                            departmentCode = pt.fk_department_code,
                            functionCode = pt.fk_function_code,
                            projectCode = pt.fk_project_code,
                            freedim1 = pt.free_dim_1,
                            freedim2 = pt.free_dim_2,
                            freedim3 = pt.free_dim_3,
                            freedim4 = pt.free_dim_4,
                            adjustmentCode = pt.fk_user_adjustment_code,
                            mainProject = mpj != null ? mpj.pk_main_project_code : string.Empty,
                            userName = ud.first_name + " " + ud.last_name,
                            prevYear1 = (input.budgetYear - 1) == pt.year ? pt.amount / 1000 : 0,
                            year1 = input.budgetYear == pt.year ? pt.amount / 1000 : 0,
                            year2 = input.budgetYear + 1 == pt.year ? pt.amount / 1000 : 0,
                            year3 = input.budgetYear + 2 == pt.year ? pt.amount / 1000 : 0,
                            year4 = input.budgetYear + 3 == pt.year ? pt.amount / 1000 : 0,
                            adjustmentCodeDesc = us.description,
                            inv_status = mpj != null ? (mpj.inv_status ?? -1) : -2
                        }).ToList();

            return (from pt in data
                        //(statusToInclude.Contains(mpj.inv_status.Value) || mpj.inv_status == null)
                    where (statusToInclude.Contains(pt.inv_status) || pt.inv_status == -1)
                    select new AdjusmentCodePopup
                    {
                        accountCode = pt.accountCode,
                        status_boolValue = pt.status_boolValue,
                        departmentCode = pt.departmentCode,
                        functionCode = pt.functionCode,
                        projectCode = pt.projectCode,
                        freedim1 = pt.freedim1,
                        freedim2 = pt.freedim2,
                        freedim3 = pt.freedim3,
                        freedim4 = pt.freedim4,
                        adjustmentCode = pt.adjustmentCode,
                        mainProject = pt.mainProject,
                        userName = pt.userName,
                        prevYear1 = pt.prevYear1,
                        year1 = pt.year1,
                        year2 = pt.year2,
                        year3 = pt.year3,
                        year4 = pt.year4,
                        adjustmentCodeDesc = pt.adjustmentCodeDesc,
                    }).ToList();
        }

        private async Task<IEnumerable<tfp_proj_transactions>> GetAdjustmentCodeProjTransAsync(int tenantId, UserAdjustmentCodeInput input, List<string> departmentCode, List<int> yearValues)
        {
            var dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await (from pt in dbContext.tfp_proj_transactions
                          where pt.fk_tenant_id == tenantId &&
                                pt.fk_change_id == input.changeId &&
                                pt.fk_user_adjustment_code != string.Empty &&
                                departmentCode.Contains(pt.fk_department_code) && yearValues.Contains(pt.year)
                          select pt).ToListAsync();
        }

        private async Task<IEnumerable<tco_user_adjustment_codes>> GetTcoUserAdjustmentCodesAsync(int tenantId)
        {
            var dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await (from x in dbContext.tco_user_adjustment_codes
                          where x.fk_tenant_id == tenantId && x.inv_adj_code
                          select x).ToListAsync();
        }

        private async Task<IEnumerable<tco_projects>> GetTcoProjectsAsync(int tenantId, UserAdjustmentCodeInput input)
        {
            var dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await (from x in dbContext.tco_projects
                          where x.fk_tenant_id == tenantId && x.date_from.Year <= input.budgetYear && x.date_to.Year >= input.budgetYear
                          select x).ToListAsync();
        }

        private async Task<IEnumerable<tco_main_projects>> GetTcoMainProjectsAsync(int tenantId, UserAdjustmentCodeInput input)
        {
            var dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await (from x in dbContext.tco_main_projects
                          where x.fk_tenant_id == tenantId &&
                          x.budget_year_from.Year <= input.budgetYear && x.budget_year_to.Year >= input.budgetYear
                          select x).ToListAsync();
        }

        private async Task<IEnumerable<vwUserDetail>> GetVwUserDetailsAsync()
        {
            var dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await (from x in dbContext.vwUserDetails select x).ToListAsync();
        }

        public async Task<IEnumerable<tco_projects>> GetProjectsLinkedToMainProjAsync(int tenantId, int budgetYear, string mainProjectCode)
        {
            return await _tenantDbContext.tco_projects.Where(x =>
                x.fk_tenant_id == tenantId && x.fk_main_project_code == mainProjectCode &&
                x.date_from.Year <= budgetYear && x.date_to.Year >= budgetYear).ToListAsync();
        }

        public async Task<List<OperationalExpenceData>> GetFinPlanActionDataAsync(int tenantId, int budgetYear, List<string> projectCode, string type, int changeId, string statusTag)
        {

            return await GetFinPlanActionDataAsync(tenantId, budgetYear, projectCode, type, changeId, statusTag, new List<string>());

        }
        public async Task<List<OperationalExpenceData>> GetFinPlanActionDataAsync(int tenantId, int budgetYear, List<string> projectCode, string type, int changeId, string statusTag,List<string> depList)
        {
            List<int> actionType = new List<int> { 5, 21 };
            var data = await (from th in _tenantDbContext.tfp_trans_header
                              join td in _tenantDbContext.tfp_trans_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                              equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                              where th.fk_tenant_id == tenantId
                                    && projectCode.Contains(td.project_code)
                                    && td.budget_year == budgetYear
                                    && actionType.Contains(th.action_type)
                                    && (!depList.Any() || depList.Contains(td.department_code))
                              group td by new
                              {
                                  th.action_type,
                                  th.pk_action_id,
                                  th.description
                              } into res
                              orderby res.Key.description
                              select new OperationalExpenceData
                              {
                                  actionId = res.Key.pk_action_id,
                                  actionType = res.Key.action_type,
                                  actionName = res.Key.description,
                                  year1 = res.Sum(x => x.year_1_amount) / 1000,
                                  year2 = res.Sum(x => x.year_2_amount) / 1000,
                                  year3 = res.Sum(x => x.year_3_amount) / 1000,
                                  year4 = res.Sum(x => x.year_4_amount) / 1000,
                                  status = statusTag + " " + budgetYear.ToString(),
                                  statusTag = "bp-tag-new",
                                  type = type,
                                  customType = 1,
                                  isEditable = true,
                                  changeId = changeId
                              }).ToListAsync();
            return data;
        }

        public async Task<List<OperationalExpenceData>> GetBListActionDataAsync(int tenantId, int budgetYear,
            List<string> projectCode, string type, int changeId, string statusTag, List<string> blistDeptHide)
        {
            List<OperationalExpenceData> data;
            if (blistDeptHide.Any())
            {
                data = await (from th in _tenantDbContext.tfp_temp_header
                              join td in _tenantDbContext.tfp_temp_detail on new { a = th.fk_tenant_id, b = th.pk_temp_id }
                                                                      equals new { a = td.fk_tenant_id, b = td.fk_temp_id }
                              where th.fk_tenant_id == tenantId && projectCode.Contains(td.project_code) && td.budget_year == budgetYear /*&& td.fk_change_id == changeId*/
                              && !th.is_parked_action && !blistDeptHide.Contains(td.department_code) && th.action_type == 21
                              group td by new
                              {
                                  th.action_type,
                                  th.pk_temp_id,
                                  th.description,
                              } into res
                              orderby res.Key.description
                              select new OperationalExpenceData
                              {
                                  actionId = res.Key.pk_temp_id,
                                  actionType = res.Key.action_type,
                                  actionName = res.Key.description,
                                  year1 = res.Sum(x => x.year_1_amount) / 1000,
                                  year2 = res.Sum(x => x.year_2_amount) / 1000,
                                  year3 = res.Sum(x => x.year_3_amount) / 1000,
                                  year4 = res.Sum(x => x.year_4_amount) / 1000,
                                  type = type,
                                  status = statusTag,
                                  statusTag = "bp-tag-fin",
                                  customType = 2,
                                  isEditable = true,
                                  changeId = changeId
                              }).ToListAsync();
            }
            else
            {
                data = await (from th in _tenantDbContext.tfp_temp_header
                              join td in _tenantDbContext.tfp_temp_detail on new { a = th.fk_tenant_id, b = th.pk_temp_id }
                                                                      equals new { a = td.fk_tenant_id, b = td.fk_temp_id }
                              where th.fk_tenant_id == tenantId && projectCode.Contains(td.project_code) && td.budget_year == budgetYear /*&& td.fk_change_id == changeId*/
                              && !th.is_parked_action && th.action_type == 21
                              group td by new
                              {
                                  th.action_type,
                                  th.pk_temp_id,
                                  th.description
                              } into res
                              orderby res.Key.description
                              select new OperationalExpenceData
                              {
                                  actionId = res.Key.pk_temp_id,
                                  actionType = res.Key.action_type,
                                  actionName = res.Key.description,
                                  year1 = res.Sum(x => x.year_1_amount) / 1000,
                                  year2 = res.Sum(x => x.year_2_amount) / 1000,
                                  year3 = res.Sum(x => x.year_3_amount) / 1000,
                                  year4 = res.Sum(x => x.year_4_amount) / 1000,
                                  type = type,
                                  status = statusTag,
                                  statusTag = "bp-tag-fin",
                                  customType = 2,
                                  isEditable = true,
                                  changeId = changeId
                              }).ToListAsync();
            }
            return data;
        }

        public async Task<List<OperationalExpenceData>> GetParkedActionDataAsync(int tenantId, int budgetYear,
            List<string> projectCode, string type, int changeId, string statusTag, List<string> parkedDeptHide)
        {
            List<OperationalExpenceData> data;
            if (parkedDeptHide.Any())
            {
                data = await (from th in _tenantDbContext.tfp_temp_header
                              join td in _tenantDbContext.tfp_temp_detail on new { a = th.fk_tenant_id, b = th.pk_temp_id }
                                                                      equals new { a = td.fk_tenant_id, b = td.fk_temp_id }
                              where th.fk_tenant_id == tenantId && projectCode.Contains(td.project_code) && td.budget_year == budgetYear /*&& td.fk_change_id == changeId*/
                              && th.is_parked_action && !parkedDeptHide.Contains(td.department_code) && th.action_type == 21
                              group td by new
                              {
                                  th.action_type,
                                  th.pk_temp_id,
                                  th.description
                              } into res
                              orderby res.Key.description
                              select new OperationalExpenceData
                              {
                                  actionId = res.Key.pk_temp_id,
                                  actionType = res.Key.action_type,
                                  actionName = res.Key.description,
                                  year1 = res.Sum(x => x.year_1_amount) / 1000,
                                  year2 = res.Sum(x => x.year_2_amount) / 1000,
                                  year3 = res.Sum(x => x.year_3_amount) / 1000,
                                  year4 = res.Sum(x => x.year_4_amount) / 1000,
                                  type = type,
                                  status = statusTag,
                                  statusTag = "bp-tag-parked",
                                  customType = 2,
                                  isEditable = true,
                                  changeId = changeId
                              }).ToListAsync();
            }
            else
            {
                data = await (from th in _tenantDbContext.tfp_temp_header
                              join td in _tenantDbContext.tfp_temp_detail on new { a = th.fk_tenant_id, b = th.pk_temp_id }
                                                                      equals new { a = td.fk_tenant_id, b = td.fk_temp_id }
                              where th.fk_tenant_id == tenantId && projectCode.Contains(td.project_code) && td.budget_year == budgetYear /*&& td.fk_change_id == changeId*/
                              && th.is_parked_action && th.action_type == 21
                              group td by new
                              {
                                  th.action_type,
                                  th.pk_temp_id,
                                  th.description
                              } into res
                              orderby res.Key.description
                              select new OperationalExpenceData
                              {
                                  actionId = res.Key.pk_temp_id,
                                  actionType = res.Key.action_type,
                                  actionName = res.Key.description,
                                  year1 = res.Sum(x => x.year_1_amount) / 1000,
                                  year2 = res.Sum(x => x.year_2_amount) / 1000,
                                  year3 = res.Sum(x => x.year_3_amount) / 1000,
                                  year4 = res.Sum(x => x.year_4_amount) / 1000,
                                  type = type,
                                  status = statusTag,
                                  statusTag = "bp-tag-parked",
                                  customType = 2,
                                  isEditable = true,
                                  changeId = changeId
                              }).ToListAsync();
            }
            return data;
        }

        public async Task<List<OperationalExpenceData>> GetDeletedActionDataAsync(int tenantId, int budgetYear,
            List<string> projectCode, string type, int changeId, string statusTag, List<string> delDeptHide)
        {
            List<OperationalExpenceData> data;
            if (delDeptHide.Any())
            {
                data = await (from th in _tenantDbContext.tfp_delete_header
                              join td in _tenantDbContext.tfp_delete_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                                                                        equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                              where th.fk_tenant_id == tenantId && projectCode.Contains(td.project_code) && td.budget_year == budgetYear && !delDeptHide.Contains(td.department_code) && th.action_type == 21
                              group td by new
                              {
                                  th.action_type,
                                  th.pk_action_id,
                                  th.description
                              } into res
                              orderby res.Key.description
                              select new OperationalExpenceData
                              {
                                  actionId = res.Key.pk_action_id,
                                  actionType = res.Key.action_type,
                                  actionName = res.Key.description,
                                  year1 = res.Sum(x => x.year_1_amount) / 1000,
                                  year2 = res.Sum(x => x.year_2_amount) / 1000,
                                  year3 = res.Sum(x => x.year_3_amount) / 1000,
                                  year4 = res.Sum(x => x.year_4_amount) / 1000,
                                  type = type,
                                  status = statusTag,
                                  statusTag = "bp-tag-delete",
                                  customType = 3,
                                  isEditable = false,
                                  changeId = changeId
                              }).ToListAsync();
            }
            else
            {
                data = await (from th in _tenantDbContext.tfp_delete_header
                              join td in _tenantDbContext.tfp_delete_detail on new { a = th.fk_tenant_id, b = th.pk_action_id }
                                                                        equals new { a = td.fk_tenant_id, b = td.fk_action_id }
                              where th.fk_tenant_id == tenantId && projectCode.Contains(td.project_code) && td.budget_year == budgetYear && th.action_type == 21 /*&& td.fk_change_id == changeId*/
                              group td by new
                              {
                                  th.action_type,
                                  th.pk_action_id,
                                  th.description
                              } into res
                              orderby res.Key.description
                              select new OperationalExpenceData
                              {
                                  actionId = res.Key.pk_action_id,
                                  actionType = res.Key.action_type,
                                  actionName = res.Key.description,
                                  year1 = res.Sum(x => x.year_1_amount) / 1000,
                                  year2 = res.Sum(x => x.year_2_amount) / 1000,
                                  year3 = res.Sum(x => x.year_3_amount) / 1000,
                                  year4 = res.Sum(x => x.year_4_amount) / 1000,
                                  type = type,
                                  status = statusTag,
                                  statusTag = "bp-tag-delete",
                                  customType = 3,
                                  isEditable = false,
                                  changeId = changeId
                              }).ToListAsync();
            }
            return data;
        }

        public tco_user_adjustment_codes GetOriginalAdjCode(int tenantId, int budgetYear)
        {
            return GetOriginalAdjCodeAsync(tenantId, budgetYear).GetAwaiter().GetResult();
        }

        public async Task<tco_user_adjustment_codes> GetOriginalAdjCodeAsync(int tenantId, int budgetYear)
        {
            return await _tenantDbContext.tco_user_adjustment_codes.FirstOrDefaultAsync(x => x.org_level == 1 && x.fk_tenant_id == tenantId
                                                && x.is_original_flag && x.budget_year == budgetYear && x.inv_adj_code && x.pk_adj_code.StartsWith("INV-"));
        }

        public async Task<IEnumerable<tco_investments_descriptions>> GetConnectedDescriptionsAsync(string mainProjectCode, int tenantId)
        {
            return await _tenantDbContext.tco_investments_descriptions
                .Where(x => x.fk_main_project_code == mainProjectCode && x.fk_tenant_id == tenantId).ToListAsync();
        }

        public async Task<IEnumerable<InvestmentOverviewGridHelper>> GetInvestmentOveriewDataRawBaseDataAsync(int tenantId, int budgetYear, Dictionary<string, clsLanguageString> langString)
        {
            var mainprojectsResult = FetchMainProjectsReadOnly(tenantId, budgetYear);
            var budgetChangesResult = FetchBudgetChangesReadOnly(tenantId);
            var accountsResult = FetchAccountsReadOnly(tenantId, budgetYear);
            var reportingLinesResult = FetchReportingLineReadOnly();
            var mainProjectsSetupResult = FetchMainProjSetUpReadOnly(tenantId);
            var attributeResult = FetchAttributesReadOnly(tenantId);
            var attributeValuesResult = FetchAttributeValuesReadOnly(tenantId);
            var relationValuesResult = FetchRelationValuesReadOnly(tenantId);
            var invPhasesResult = FetchInvestmentPhaseReadOnly(tenantId);

            await Task.WhenAll(mainprojectsResult, budgetChangesResult, accountsResult, reportingLinesResult, mainProjectsSetupResult, attributeResult,
                                attributeValuesResult, relationValuesResult, invPhasesResult);

            var mainprojects = mainprojectsResult.Result.ToList();
            var budgetChanges = budgetChangesResult.Result.ToList();
            var accounts = accountsResult.Result.ToList();
            var reportingLines = reportingLinesResult.Result.ToList();
            var mainProjectsSetup = mainProjectsSetupResult.Result.ToList();
            var attribute = attributeResult.Result.ToList();
            var attributeValues = attributeValuesResult.Result.ToList();
            var relationValues = relationValuesResult.Result.ToList();
            var invPhases = invPhasesResult.Result.ToList();

            var data = (from tm in mainprojects
                        join b in budgetChanges on new { a = tm.TenantId, b = tm.ChangeId }
                                                                  equals new { a = b.fk_tenant_id, b = b.pk_change_id }
                        join ac in accounts on new { a = tm.TenantId, b = tm.AccountCode }
                                                                        equals new { a = ac.pk_tenant_id, b = ac.pk_account_code }
                        join rl in reportingLines on new { a = ac.fk_kostra_account_code }
                                                                   equals new { a = rl.fk_kostra_account_code }
                        join mp in mainProjectsSetup.Where(x => x.fk_tenant_id == tenantId)
                                                                                        on new { a = tm.TenantId, b = tm.MainProjectCode }
                                                                                    equals new { a = mp.fk_tenant_id, b = mp.pk_main_project_code } into lftSet
                        from lftMp in lftSet.DefaultIfEmpty()
                        join tinvphase in invPhases on new { a = tm.TenantId, b = tm.InvestmentPhaseId } equals new { a = tinvphase.fk_tenant_id, b = (int?)tinvphase.pk_investment_phase_id } into lftjninvphase
                        from invphasegrp in lftjninvphase.DefaultIfEmpty()

                        where ((tm.TenantId == tenantId && tm.InvStatus.HasValue
                                && rl.report == "55_OVINV")
                                || (tm.TenantId == tenantId && rl.report == "55_OVINV" && tm.InvStatus.HasValue                                
                                ))
                        group new { tm, rl, lftMp } by new
                        {
                            inv_status = tm.InvStatus.HasValue ? tm.InvStatus.Value : 0,
                            tm.MainProjectCode,
                            tm.MainProjectName,
                            fk_investment_phase_id = tm.InvestmentPhaseId.HasValue ? tm.InvestmentPhaseId.Value : 0,
                            investmentPhaseName = invphasegrp != null ? invphasegrp.description : string.Empty,
                            tm.PTFunctionCode,
                            tm.PTDepartmentCode,
                            tm.PTUserAdjustmentCode,
                            responDeptCode = tm.DepartmentCode,
                            responFuncCode = tm.FunctionCode,
                            tm.ChangeId,
                            tm.Year,
                            rl.line_item_id,
                            rl.line_item,
                            rl.line_group_id,
                            tm.ProgramCode,
                            tags = lftMp != null ? lftMp.tags : string.Empty,
                            tm.ProjectCode,                  
                            tm.ParentOrgId,
                            tm.ParentOrgLevel,
                            tm.EvaluationStatus
                        } into grp
                        select new InvestmentOverviewGridHelper
                        {
                            status = grp.Key.inv_status,
                            tag = grp.Key.tags,
                            mainProjectCode = grp.Key.MainProjectCode,
                            mainProjectName = grp.Key.MainProjectName,
                            investmentPhase = grp.Key.fk_investment_phase_id,
                            investmentPhaseName = grp.Key.investmentPhaseName,
                            functionCode = grp.Key.PTFunctionCode,
                            departmentcode = grp.Key.PTDepartmentCode,
                            userAdjustmentCode = grp.Key.PTUserAdjustmentCode,
                            responDeptCode = grp.Key.responDeptCode,
                            responFuncCode = grp.Key.responFuncCode,
                            changeId = grp.Key.ChangeId,
                            ammount = (grp.Sum(z => z.tm.Amount)) / 1000,
                            year = grp.Key.Year,
                            line_item_id = grp.Key.line_item_id.ToString(),
                            line_item_Name = grp.Key.line_item.ToString(),
                            line_group_id = grp.Key.line_group_id.ToString(),
                            mainDepartmentCode = grp.Key.PTDepartmentCode,
                            programCode = grp.Key.ProgramCode,
                            projectCode = grp.Key.ProjectCode,
                            attributeId = string.Empty,
                            chapter = string.Empty,
                            evaluationStatus = grp.Key.inv_status != 3 ? "" : (grp.Key.EvaluationStatus == 0 ? langString["BP_Not_Accessed"].LangText : langString["BP_Accessed"].LangText),
                            parentOrgIdCreatedAt = grp.Key.ParentOrgId != null ? grp.Key.ParentOrgId : string.Empty,
                            parentOrgLevelCreatedAt = grp.Key.ParentOrgLevel != null ? grp.Key.ParentOrgLevel : -1,
                        }).ToList();


            // join with attribute table if chapter setup has data
            var finalData= (attribute.Any(z=>z.attribute_type== "CHAPTER") && attributeValues.Any(z => z.attribute_type == "CHAPTER") && relationValues.Any(z => z.attribute_type == "CHAPTER")) ? (from tm in data
                            join av in attributeValues on new { attribute_type = "CHAPTER", status = 1 } equals new { av.attribute_type, av.status } into grp3
                            from grp4 in grp3.DefaultIfEmpty()
                            join tr in relationValues
                                on new { b = "CHAPTER", c = grp4 == null ? string.Empty : grp4.pk_attribute_id }
                                equals new { b = tr.attribute_type, c = tr.attribute_value } into grp1
                            from grp2 in grp1.DefaultIfEmpty()
                            where ((grp2 != null && grp2.relation_value_from.CompareTo(tm.mainDepartmentCode) <= 0 && grp2.relation_value_to.CompareTo(tm.mainDepartmentCode) >= 0 ))
                            group new { tm, grp2 } by new
                            {
                                tm.status,
                                tm.mainProjectCode,
                                tm.mainProjectName,
                                tm.investmentPhase,
                                tm.investmentPhaseName,
                                tm.functionCode,
                                tm.departmentcode,
                                tm.userAdjustmentCode,
                                tm.responDeptCode,
                                tm.responFuncCode,
                                tm.changeId,
                                tm.year,
                                tm.line_item_id,
                                tm.line_item_Name,
                                tm.line_group_id,
                                tm.programCode,
                                tm.tag,
                                tm.projectCode,
                                attributeId = (grp2 != null ? grp2.attribute_value : String.Empty),
                                attributeName = (grp4 != null && grp2 != null ? grp4.attribute_name : String.Empty),
                                tm.parentOrgLevelCreatedAt,
                                tm.parentOrgIdCreatedAt,
                                tm.evaluationStatus
                            } into grp
                            select new InvestmentOverviewGridHelper
                            {
                                status = grp.Key.status,
                                tag = grp.Key.tag,
                                mainProjectCode = grp.Key.mainProjectCode,
                                mainProjectName = grp.Key.mainProjectName,
                                investmentPhase = grp.Key.investmentPhase,
                                investmentPhaseName = grp.Key.investmentPhaseName,
                                functionCode = grp.Key.functionCode,
                                departmentcode = grp.Key.departmentcode,
                                userAdjustmentCode = grp.Key.userAdjustmentCode,
                                responDeptCode = grp.Key.responDeptCode,
                                responFuncCode = grp.Key.responFuncCode,
                                changeId = grp.Key.changeId,
                                ammount = grp.Sum(z => z.tm.ammount) ,
                                year = grp.Key.year,
                                line_item_id = grp.Key.line_item_id.ToString(),
                                line_item_Name = grp.Key.line_item_Name.ToString(),
                                line_group_id = grp.Key.line_group_id.ToString(),
                                mainDepartmentCode = grp.Key.departmentcode,
                                programCode = grp.Key.programCode,
                                projectCode = grp.Key.projectCode,
                                attributeId = grp.Key.attributeId,
                                chapter = grp.Key.attributeName,
                                evaluationStatus = grp.Key.evaluationStatus,
                                parentOrgIdCreatedAt = grp.Key.parentOrgIdCreatedAt,
                                parentOrgLevelCreatedAt = grp.Key.parentOrgLevelCreatedAt,
                            }).ToList(): data.ToList();

            return finalData.AsEnumerable();
        }

        public async Task<tfp_budget_changes> GetChangeDetailsAsync(int tenantId, int changeId)
        {
            return await _tenantDbContext.tfp_budget_changes.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.pk_change_id == changeId);
        }

        public async Task<tpl_tfp_investment_mapping> GetInvestmentMappingDataAsync(int tenantId, string tempProjectCode)
        {
            return await _tenantDbContext.tpl_tfp_investment_mapping.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.fk_main_project_code == tempProjectCode);
        }

        public async Task<tsa_tfp_investment_mapping> GetStrategyInvestmentMappingDataByTempCodeAsync(int tenantId, string tempProjectCode)
        {
            return await _tenantDbContext.tsa_tfp_investment_mapping.FirstOrDefaultAsync(x =>
                x.fk_tenant_id == tenantId && x.fk_main_project_code == tempProjectCode);
        }

        public async Task<IEnumerable<tpl_tfp_investment_mapping>> GetTenantInvestmentMappingDataAsync(int tenantId)
        {
            return await _tenantDbContext.tpl_tfp_investment_mapping.Where(x => x.fk_tenant_id == tenantId).ToListAsync();
        }

        public async Task<IEnumerable<tsa_tfp_investment_mapping>> GetStrategyInvestmentMappingDataAsync(int tenantId)
        {
            return await _tenantDbContext.tsa_tfp_investment_mapping.Where(x => x.fk_tenant_id == tenantId).ToListAsync();
        }

        public async Task<IEnumerable<tco_main_projects>> GetMainProjectsforInvestmentMappingAsync(int tenantId, int budgetYear)
        {
            return await _tenantDbContext.tco_main_projects.Where(x => x.fk_tenant_id == tenantId && budgetYear >= x.budget_year_from.Year
                                                            && budgetYear <= x.budget_year_to.Year).ToListAsync();
        }

        public async Task<IEnumerable<tco_investments_descriptions>> GetPlanMappingDescriptionDataAsync(int tenantId, int budgetYear)
        {
            return await _tenantDbContext.tco_investments_descriptions.Where(x =>
                x.fk_tenant_id == tenantId && x.budget_year == budgetYear &&
                x.description_type == PlanPublishConstants.PlanInvestmentFPDescStatus).ToListAsync();
        }

        public async Task<IEnumerable<vwUserDetail>> GetUserDetailsAsync(int tenantId)
        {
            return await _tenantDbContext.vwUserDetails.Where(x => x.tenant_id == tenantId).ToListAsync();
        }

        public async Task<IEnumerable<vwUserDetail>> GetUserDetailsParallelReadAsync(int tenantId)
        {
            TenantDBContext dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await dbContext.vwUserDetails.Where(x => x.tenant_id == tenantId).ToListAsync();
        }

        public async Task<IEnumerable<PlanFinplanMainProjectMappingHelper>> GetBPPlanInvestmentMappingDataAsync(int tenantId)
        {
            return await (from a in _tenantDbContext.tpl_tfp_investment_mapping
                          join b in _tenantDbContext.tpl_investments on new { planInvId = a.fk_plan_investment_id, tenantID = a.fk_tenant_id }
                                                                 equals new { planInvId = b.pk_plan_investment_id, tenantID = b.fk_tenant_id } into g1
                          from x in g1.DefaultIfEmpty()
                          join c in _tenantDbContext.tpl_plan on new { planId = x.fk_masterplan_id, tenantID = x.fk_tenant_id }
                                                          equals new { planId = c.pk_plan_id, tenantID = c.fk_tenant_id } into g2
                          from x1 in g2.DefaultIfEmpty()
                          where a.fk_tenant_id == tenantId
                          select new
                          {
                              PlanInvestmentId = a.fk_plan_investment_id,
                              FinPlanProjectId = a.fk_main_project_code,
                              planTag = x1 == null ? string.Empty : x1.short_name,
                          }).Distinct().Select(x => new PlanFinplanMainProjectMappingHelper
                          {
                              PlanInvestmentId = x.PlanInvestmentId,
                              FinPlanProjectId = x.FinPlanProjectId,
                              PlanTag = x.planTag,
                          }).ToListAsync();
        }

        public async Task<IEnumerable<StrategyFinplanMainProjectMappingHelper>> GetStrategyAssessmentInfoAsync(int tenantId)
        {
            var result = await (from a in _tenantDbContext.tsa_tfp_investment_mapping
                                join b in _tenantDbContext.tsa_assessment_area_actions on new { a = a.fk_tenant_id, b = a.fk_action_id }
                                                                                   equals new { a = b.fk_tenant_id, b = b.pk_action_id } into g1
                                from x1 in g1.DefaultIfEmpty()
                                join d in _tenantDbContext.tsa_assessment_areas on new { a = x1.fk_tenant_id, b = x1.fk_area_id }
                                                                            equals new { a = d.fk_tenant_id, b = d.pk_area_id } into g3
                                from x3 in g3.DefaultIfEmpty()
                                join c in _tenantDbContext.tsa_assessments on new { a = a.fk_tenant_id, b = x1.budget_year, c = x1.fk_assessment_id }
                                                                       equals new { a = c.fk_tenant_id, b = c.budget_year, c = c.pk_assessment_id } into g2
                                from x2 in g2.DefaultIfEmpty()
                                where a.fk_tenant_id == tenantId
                                select new StrategyFinplanMainProjectMappingHelper
                                {
                                    InvestmentId = a.fk_action_id,
                                    FinPlanProjectId = a.fk_main_project_code,
                                    assessmentName = x2 == null ? string.Empty : x2.assessment_name,
                                    assessmentAreaName = x3 == null ? string.Empty : x3.area_name
                                }).ToListAsync();
            return result;
        }

        public async Task<List<string>> GetTfpTransDetailDataAsync(int tenantId, int budgetYear)
        {
            TenantDBContext dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await (from a in dbContext.tfp_trans_detail
                          join th in dbContext.tfp_trans_header on new { a = a.fk_action_id, b = a.fk_tenant_id }
                                                                   equals new { a = th.pk_action_id, b = th.fk_tenant_id }
                          join p in dbContext.tco_projects on new { a = a.project_code, b = a.fk_tenant_id }
                                                              equals new { a = p.pk_project_code, b = p.fk_tenant_id }
                          where a.fk_tenant_id == tenantId && a.budget_year == budgetYear && th.action_type == 21
                          select p.fk_main_project_code).Distinct().ToListAsync();
        }

        public async Task<List<string>> GetTfpDeleteDetailDataAsync(int tenantId, int budgetYear)
        {
            TenantDBContext dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await (from a in dbContext.tfp_delete_detail
                          join th in dbContext.tfp_delete_header on new { a = a.fk_action_id, b = a.fk_tenant_id }
                                                                    equals new { a = th.pk_action_id, b = th.fk_tenant_id }
                          join p in dbContext.tco_projects on new { a = a.project_code, b = a.fk_tenant_id }
                                                              equals new { a = p.pk_project_code, b = p.fk_tenant_id }
                          where a.fk_tenant_id == tenantId && a.budget_year == budgetYear && th.action_type == 21
                          select p.fk_main_project_code).Distinct().ToListAsync();
        }

        public async Task<List<string>> GetTfpTempDetailDataAsync(int tenantId, int budgetYear)
        {
            TenantDBContext dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await (from a in dbContext.tfp_temp_detail
                          join th in dbContext.tfp_temp_header on new { a = a.fk_temp_id, b = a.fk_tenant_id }
                                                                  equals new { a = th.pk_temp_id, b = th.fk_tenant_id }
                          join p in dbContext.tco_projects on new { a = a.project_code, b = a.fk_tenant_id }
                                                              equals new { a = p.pk_project_code, b = p.fk_tenant_id }
                          where a.fk_tenant_id == tenantId && a.budget_year == budgetYear && th.action_type == 21
                          select p.fk_main_project_code).Distinct().ToListAsync();
        }

        public List<string> GetMainProjectForProjectCodes(List<string> projectCode, int tenantId, int budgetYear)
        {
            return GetMainProjectForProjectCodesAsync(projectCode, tenantId, budgetYear).GetAwaiter().GetResult();
        }

        public async Task<List<string>> GetMainProjectForProjectCodesAsync(List<string> projectCode, int tenantId, int budgetYear)
        {
            return await (from a in _tenantDbContext.tco_projects
                          where a.fk_tenant_id == tenantId && projectCode.Contains(a.pk_project_code)
                          && a.date_from.Year <= budgetYear && a.date_to.Year >= budgetYear
                          select a.pk_project_code).Distinct().ToListAsync();
        }

        public IEnumerable<tco_attachments> GetAttachmentData(int tenantId, string moduleId, string pageId, string parentId)
        {
            return GetAttachmentDataAsync(tenantId, moduleId, pageId, parentId).GetAwaiter().GetResult();
        }

        public async Task<IEnumerable<tco_attachments>> GetAttachmentDataAsync(int tenantId, string moduleId, string pageId, string parentId)
        {
            return await _tenantDbContext.tco_attachments.Where(x => x.fk_tenant_id == tenantId && x.module_id == moduleId && x.page_id == pageId && x.parent_id == parentId).OrderByDescending(x => x.updated).ToListAsync();
        }

        public IEnumerable<tco_attachments> GetAllAttachmentData(int tenantId, int budgetYear, string moduleId, string pageId)
        {
            return GetAllAttachmentDataAsync(tenantId, budgetYear, moduleId, pageId).GetAwaiter().GetResult();
        }

        public async Task<IEnumerable<tco_attachments>> GetAllAttachmentDataAsync(int tenantId, int budgetYear, string moduleId, string pageId)
        {
            return await _tenantDbContext.tco_attachments.Where(x => x.fk_tenant_id == tenantId && x.module_id == moduleId && x.page_id == pageId).OrderBy(x => x.parent_id).ThenByDescending(x => x.updated).ToListAsync();
        }

        public IEnumerable<tco_attachments> GetAdjustmentCodeAttachmentData(string adjustmentCode, int tenantId)
        {
            return GetAdjustmentCodeAttachmentDataAsync(adjustmentCode, tenantId).GetAwaiter().GetResult();
        }

        public async Task<IEnumerable<tco_attachments>> GetAdjustmentCodeAttachmentDataAsync(string adjustmentCode, int tenantId)
        {
            return await _tenantDbContext.tco_attachments
                .Where(z => z.fk_tenant_id == tenantId && z.parent_id == adjustmentCode).OrderBy(x => x.parent_id)
                .ThenByDescending(x => x.updated).ToListAsync();
        }

        public async Task<tco_user_adjustment_codes> GetUserAdjustmentCodeAsync(string adjustmentCode, int tenantId)
        {
            return await _tenantDbContext.tco_user_adjustment_codes.FirstOrDefaultAsync(z => z.fk_tenant_id == tenantId && z.pk_adj_code == adjustmentCode);
        }

        public async Task<List<tco_attribute_values>> GetAllAttributesAsync(int tenantId, string attributeType)
        {
            return await _tenantDbContext.tco_attribute_values
                .Where(z => z.fk_tenant_id == tenantId && z.attribute_type == attributeType && z.status == 1)
                .ToListAsync();
        }

        public async Task<tco_adjustment_codes> GetAdjustmentCodeAsync(string adjustmentCode, int tenantId)
        {
            return await _tenantDbContext.tco_adjustment_codes.FirstOrDefaultAsync(z => z.fk_tenant_id == tenantId && z.pk_adjustment_code == adjustmentCode);
        }

        public List<string> GetProjectValidation(int tenantId, string module, string accountType, string linkType, string orgVersion)
        {
            return GetProjectValidationAsync(tenantId, module, accountType, linkType, orgVersion).GetAwaiter().GetResult();
        }

        public async Task<List<string>> GetProjectValidationAsync(int tenantId, string module, string accountType, string linkType, string orgVersion)
        {
            var defaultAccounts = await (from tad in _tenantDbContext.tmd_acc_defaults
                                         where tad.fk_tenant_id == tenantId && tad.acc_type == accountType
                                               && tad.module == module
                                               && tad.link_type == linkType
                                               && tad.fk_org_version == orgVersion
                                         select tad.acc_value).Distinct().ToListAsync();
            return defaultAccounts;
        }

        public async Task<List<string>> GetAllTransferredFromMain
            (int tenantId)
        {
            return await _tenantDbContext.tfp_sync_main_projects.Where(x => x.sub_fk_tenant_id == tenantId && x.process_stage != BudgetProcessStage.BPP_ProposalProcess_Fetch.ToString()).Select(x => x.sub_fk_main_project_code).ToListAsync();
        }

        public async Task<List<tfp_proj_transactions>> GetDataProjTransForUserAdjCodeDelete(int tenantId, DeleteAdjCodeHelper input)
        {
            if (input.Pagetype == UserAdjPageType.Investments.ToString() || input.Pagetype == UserAdjPageType.InvestActionImport.ToString())
            {
                return await _tenantDbContext.tfp_proj_transactions.Where(x => x.fk_tenant_id == tenantId && x.fk_user_adjustment_code == input.adjCode).ToListAsync();
            }
            else
            {
                return new List<tfp_proj_transactions>();
            }
        }

        public async Task<List<tfp_stage_investment_Project_import>> GetInvestmentActionImportData(int tenantId, DeleteAdjCodeHelper input)
        {
            return await _tenantDbContext.tfp_stage_investment_Project_import.Where(x => x.TenantId == tenantId && x.BudgetYear == input.budgetYear && x.UserAdjustmentCode == input.adjCode).ToListAsync();
        }

        public async Task DeleteInvTransactions(int sourceTenantId, int targetTenantId, string projectCode)
        {
            try
            {
                var data = await _tenantDbContext.tfp_proj_transactions.Where(x => x.fk_tenant_id == targetTenantId && x.fk_project_code == projectCode).ToListAsync();
                if (data.Any())
                {
                    _tenantDbContext.tfp_proj_transactions.RemoveRange(data);
                }
            }
            catch (Exception) { }
        }

        public async Task<List<tfp_sync_main_projects>> GetAllTransferredToMain(int tenantId, bool isSubTenant)
        {
            if (!isSubTenant)
                return await _tenantDbContext.tfp_sync_main_projects.Where(x => x.main_fk_tenant_id == tenantId && x.process_stage == BudgetProcessStage.BPP_ProposalProcess_Fetch.ToString()).ToListAsync();
            else
                return await _tenantDbContext.tfp_sync_main_projects.Where(x => x.sub_fk_tenant_id == tenantId && x.process_stage == BudgetProcessStage.BPP_ProposalProcess_Fetch.ToString()).ToListAsync();
        }

        public async Task<bool> isISYProject(int tenantId, string projectCode)
        {
            var data = await _tenantDbContext.tco_ISY_projects.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.fk_project_code == projectCode);
            return data != null;
        }

        public async Task<List<string>> FetchISYProjListAsync(int tenantId)
        {
            return await _tenantDbContext.tco_ISY_projects.Where(x => x.fk_tenant_id == tenantId).Select(x => x.fk_project_code).ToListAsync();
        }

        public async Task InsertIntoFinImportLog(UserData userData, int forecastPeriod, tmr_stage_ISY_import inv, int jobId, bool isChange)
        {
            var importLog = new tmr_ISY_fin_import_log()
            {
                fk_tenant_id = userData.tenant_id,
                fk_job_id = jobId,
                fk_project_code = inv.fk_project_code,
                approval_cost = inv.approval_cost,
                forecast_year_1 = inv.forecast_year_1,
                forecast_year_2 = inv.forecast_year_2,
                forecast_year_3 = inv.forecast_year_3,
                forecast_year_4 = inv.forecast_year_4,
                forecast_year_5 = inv.forecast_year_5,
                forecast_year_6 = inv.forecast_year_6,
                forecast_year_7 = inv.forecast_year_7,
                forecast_year_8 = inv.forecast_year_8,
                forecast_year_9 = inv.forecast_year_9,
                forecast_year_10 = inv.forecast_year_10,
                total_forecast = inv.total_forecast,
                updated_ISY = (DateTime)inv.ISY_update_date,
                updated = DateTime.UtcNow,
                updated_by = userData.pk_id,
                forecast_period = forecastPeriod,
                is_change = isChange,
            };
            _tenantDbContext.tmr_ISY_fin_import_log.Add(importLog);
            await _tenantDbContext.SaveChangesAsync();
        }

        public async Task InsertIntoISYProjects(UserData userData, string projectCode)
        {
            var row = new tco_ISY_projects()
            {
                fk_tenant_id = userData.tenant_id,
                fk_project_code = projectCode,
                updated = DateTime.UtcNow,
                updated_by = userData.pk_id
            };
            _tenantDbContext.tco_ISY_projects.Add(row);
            await _tenantDbContext.SaveChangesAsync();
        }

        private async Task<IEnumerable<ProjectTransactionHelper>> FetchMainProjectsReadOnly(int tenantId, int budgetYear)
        {
            var dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await (from tm in dbContext.tco_main_projects
                          join tp in dbContext.tco_projects on new { a = tm.fk_tenant_id, b = tm.pk_main_project_code } equals new { a = tp.fk_tenant_id, b = tp.fk_main_project_code }
                          join pt in dbContext.tfp_proj_transactions on new { a = tp.fk_tenant_id, b = tp.pk_project_code } equals new { a = pt.fk_tenant_id, b = pt.fk_project_code }
                          where tm.fk_tenant_id == tenantId &&
                          tm.budget_year_from.Year <= budgetYear && budgetYear <= tm.budget_year_to.Year && tp.date_from.Year <= budgetYear && budgetYear <= tp.date_to.Year
                          && tm.fk_tenant_id == tenantId && tm.inv_status.HasValue
                          && !tm.from_sync   //  #166623
                          select new ProjectTransactionHelper
                          {
                              TenantId = tm.fk_tenant_id,
                              MainProjectCode = tm.pk_main_project_code,
                              InvestmentPhaseId = tm.fk_investment_phase_id,
                              InvStatus = tm.inv_status,
                              MainProjectName = tm.main_project_name,
                              DepartmentCode = tm.fk_department_code,
                              FunctionCode = tm.fk_Function_code,
                              EvaluationStatus = tm.evaluation_status,
                              ProjectCode = tp.pk_project_code,
                              ProgramCode = tp.fk_prog_code,
                              AccountCode = pt.fk_account_code,
                              PTFunctionCode = pt.fk_function_code,
                              ChangeId = pt.fk_change_id,
                              PTDepartmentCode = pt.fk_department_code,
                              PTUserAdjustmentCode = pt.fk_user_adjustment_code,
                              Year = pt.year,
                              Amount = pt.amount,
                              ParentOrgId = tm.parentOrgId,
                              ParentOrgLevel = tm.parentOrgLevel,
                          }).ToListAsync();
        }

        private async Task<IEnumerable<tfp_budget_changes>> FetchBudgetChangesReadOnly(int tenantId)
        {
            var dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await dbContext.tfp_budget_changes
                .Where(x => x.fk_tenant_id == tenantId).AsNoTracking().ToListAsync();
        }

        private async Task<IEnumerable<tco_accounts>> FetchAccountsReadOnly(int tenantId, int budgetYear)
        {
            var dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await dbContext.tco_accounts
                .Where(x => x.pk_tenant_id == tenantId && x.dateFrom.Year <= budgetYear && budgetYear <= x.dateTo.Year).AsNoTracking().ToListAsync();
        }

        private async Task<IEnumerable<gmd_reporting_line>> FetchReportingLineReadOnly()
        {
            var dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await dbContext.gmd_reporting_line.AsNoTracking().ToListAsync();
        }

        private async Task<IEnumerable<tco_main_project_setup>> FetchMainProjSetUpReadOnly(int tenantId)
        {
            var dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await dbContext.tco_main_project_setup.Where(x => x.fk_tenant_id == tenantId).AsNoTracking().ToListAsync();
        }

        private async Task<IEnumerable<tco_attributes>> FetchAttributesReadOnly(int tenantId)
        {
            var dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await dbContext.tco_attributes.Where(x => x.fk_tenant_id == tenantId).AsNoTracking().ToListAsync();
        }

        private async Task<IEnumerable<tco_attribute_values>> FetchAttributeValuesReadOnly(int tenantId)
        {
            var dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await dbContext.tco_attribute_values.Where(x => x.fk_tenant_id == tenantId).AsNoTracking().ToListAsync();
        }

        private async Task<IEnumerable<tco_relation_values>> FetchRelationValuesReadOnly(int tenantId)
        {
            var dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await dbContext.tco_relation_values.Where(x => x.fk_tenant_id == tenantId).AsNoTracking().ToListAsync();
        }

        private async Task<IEnumerable<tco_investment_phase>> FetchInvestmentPhaseReadOnly(int tenantId)
        {
            var dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await dbContext.tco_investment_phase.Where(x => x.fk_tenant_id == tenantId).AsNoTracking().ToListAsync();
        }
        public async Task<List<string>> GetTransferredInvestment(int tenantId)
        {
            return await _tenantDbContext.tfp_sync_main_projects.Where(x => x.sub_fk_tenant_id == tenantId && x.process_stage != BudgetProcessStage.BPP_ProposalProcess_Fetch.ToString()).Select(x => x.sub_fk_main_project_code).ToListAsync();
        }        
        public async Task<List<tfp_sync_main_projects>> GetTransferredInvestmentSubToMain(int tenantId, BudgetProcessStage budgetProcess)
        {
            return await _tenantDbContext.tfp_sync_main_projects.Where(x => x.main_fk_tenant_id == tenantId && x.process_stage == budgetProcess.ToString()).ToListAsync();
        }
        public async Task InsertIntoISYStatusImportLog(UserData userData, int forecastPeriod, tmr_stage_status_ISY_import stageRow, int jobId)
        {
            tmr_ISY_status_desc_import_log logData = new tmr_ISY_status_desc_import_log();

            logData.fk_project_code = stageRow.ID;
            logData.fk_tenant_id = userData.tenant_id;
            logData.est_finish_quarter = string.IsNullOrEmpty(stageRow.est_finish_quarter) ? "" : stageRow.est_finish_quarter;
            logData.status_desc = stageRow.status_desc == null ? "" : stageRow.status_desc;
            logData.status = stageRow.status;
            logData.fin_status = stageRow.fin_status;
            logData.risk = stageRow.risk;
            logData.quality = stageRow.quality;
            logData.updated_ISY = stageRow.ISY_update_date.HasValue ? stageRow.ISY_update_date.Value : DateTime.MinValue;
            logData.updated = DateTime.UtcNow;
            logData.updated_by = userData.pk_id;
            logData.fk_job_id = jobId;
            logData.forecast_period = forecastPeriod;

            _tenantDbContext.tmr_ISY_status_desc_import_log.Add(logData);
            await _tenantDbContext.SaveChangesAsync();
        }
        public async Task<bool> IsNewTimeRecordISYStatus(int tenant_id, int forecastPeriod, tmr_stage_status_ISY_import item)
        {
            bool isNew = true;
            var logData = await _tenantDbContext.tmr_ISY_status_desc_import_log.Where(x => x.fk_tenant_id == tenant_id && x.forecast_period == forecastPeriod && x.fk_project_code == item.ID).OrderByDescending(y => y.updated_ISY.Day).FirstOrDefaultAsync();
            if (logData != null)
            {
                if (logData.updated_ISY.Day == item.ISY_update_date.Value.Day)
                {
                    isNew = false;
                }
            }
            return isNew;

        }
        public async Task<bool> IsNewTimeRecordISYForecast(int tenant_id, int forecastPeriod, tmr_stage_ISY_import item)
        {
            bool isNew = true;
            var logData = await _tenantDbContext.tmr_ISY_fin_import_log.Where(x => x.fk_tenant_id == tenant_id && x.forecast_period == forecastPeriod && x.fk_project_code == item.fk_project_code).OrderByDescending(y => y.updated_ISY.Day).FirstOrDefaultAsync();
            if (logData != null)
            {
                if (logData.updated_ISY.Day == item.ISY_update_date.Value.Day)
                {
                    isNew = false;
                }
            }
            return isNew;

        }

        public async Task<List<tco_proj_hierarchy>> GetProjHierarchyData(int tenantId)
        {
            return await _tenantDbContext.tco_proj_hierarchy.Where(x => x.fk_tenant_id == tenantId).ToListAsync();
        }

        public async Task<List<KeyValueDataPair>> GetMainProjectsListData(int tenantId, List<string> projectCodes)
        {
            List<KeyValueDataPair> finalList = await (from tp in _tenantDbContext.tco_projects
                                                      join tmp in _tenantDbContext.tco_main_projects on new {a = tp.fk_tenant_id, b = tp.fk_main_project_code}
                                                                                                equals new {a = tmp.fk_tenant_id, b = tmp.pk_main_project_code}
                                                      where tp.fk_tenant_id == tenantId && projectCodes.Contains(tp.fk_main_project_code)
                                                      select new KeyValueDataPair
                                                      {
                                                          Key = tmp.pk_main_project_code,
                                                          Value = tmp.pk_main_project_code + "-" + tmp.main_project_name
                                                      }).ToListAsync();
            finalList = finalList.DistinctBy(x => x.Key).ToList();
            return finalList.OrderBy(z=>z.Key).ToList();
        }
        public async Task<bool> GetInvReportStatus (int tenantId,int forecastPeriod)
        {
            List<int> statusList = new List<int> { 2,4};
            var setupStatusData = await _tenantDbContext.tmr_report_setup_status.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.forecast_period == forecastPeriod && x.tab_type == 6 && statusList.Contains(x.status));
            var setupData = await _tenantDbContext.tmr_period_setup.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.forecast_period == forecastPeriod && x.status == 2);
            bool hasSetup = setupStatusData != null && setupData != null ? true : false;
            return hasSetup;
        }
        public async Task<List<InvProjLevelDescriptionHelper>> GetInvDescriptionsProjHierarchy(int tenantId, int budgetYear, string projGrp, string descriptionType)
        {
            var invDescriptions = await (from th in _tenantDbContext.tco_proj_hierarchy
                                         join tp in _tenantDbContext.tco_projects on new { a = th.fk_tenant_id, b = th.fk_project_code }
                                                                                    equals new { a = tp.fk_tenant_id, b = tp.pk_project_code }
                                         join tm in _tenantDbContext.tco_main_projects on new { a = th.fk_tenant_id, b = tp.fk_main_project_code }
                                                                                        equals new { a = tm.fk_tenant_id, b = tm.pk_main_project_code }
                                         join td in _tenantDbContext.tco_investments_descriptions on new { a = th.fk_tenant_id, b = tm.pk_main_project_code }
                                                                                                equals new { a = td.fk_tenant_id, b = td.fk_main_project_code }
                                         where th.fk_tenant_id == tenantId && td.budget_year == budgetYear && th.proj_gr_1 == projGrp && td.description_type == descriptionType
                                         select new InvProjLevelDescriptionHelper
                                         {
                                             Description = td.description,
                                             MainProjectCode = tm.pk_main_project_code,
                                             MainProjectName = tm.main_project_name
                                         }
                                         ).ToListAsync();
            return invDescriptions;
        }
        public async Task<IEnumerable<tco_proj_levels_descriptions>> GetAllProjectLevelDescriptionAsync(int tenantId,
            int budgetYear, string mainProjCode, string descriptionType, Guid phaseId)
        {
            return await _tenantDbContext.tco_proj_levels_descriptions.Where(x =>
                x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.fk_main_project_code == mainProjCode
                && x.description_type == descriptionType && x.fk_budget_phase_id == phaseId).ToListAsync();
        }

        public async Task<List<tmr_proj_transactions>> GetTmrProjTransactionsDataAsync(int tenantId, int forecastPeriod, List<string> ProjectCodes)
        {
            return await (from tmp in _tenantDbContext.tmr_proj_transactions
                        join tca in _tenantDbContext.tco_accounts on new { a = tmp.fk_tenant_id, b = tmp.fk_account_code }
                        equals new { a = tca.pk_tenant_id, b = tca.pk_account_code }
                        join grl in _tenantDbContext.gmd_reporting_line on tca.fk_kostra_account_code
                        equals grl.fk_kostra_account_code
                        where tmp.fk_tenant_id == tenantId && tmp.forecast_period == forecastPeriod
                        && grl.report == "55_OVINV" && grl.line_group_id == 10 && tmp.is_change == 0
                        && ProjectCodes.Contains(tmp.fk_project_code)
                        select tmp).ToListAsync();
        }

        public async Task<List<tmr_proj_transactions>> GetAllTmrProjTransactionsDataAsync(int tenantId, int forecastPeriod)
        {
            return await _tenantDbContext.tmr_proj_transactions.Where(x => x.fk_tenant_id == tenantId
            && x.forecast_period == forecastPeriod).AsNoTracking().ToListAsync();
        }

        public async Task<IEnumerable<tmr_proj_transactions>> GetTmrProjTransactionsDataByProjectsAsync(int tenantId, int forecastPeriod, List<string> projCodes)
        {
            return (await _dbContextManager.GetTenantDbContextAsync()).tmr_proj_transactions.Where(x => x.fk_tenant_id == tenantId
            && x.forecast_period == forecastPeriod && projCodes.Contains(x.fk_project_code));
        }
        public async Task<IEnumerable<tco_projects>> GetFilteredDataTcoProjectsAsync(int tenantId, int budgetYear, List<string> projCodes)
        {
            return (await _dbContextManager.GetTenantDbContextAsync()).tco_projects.Where(x => x.fk_tenant_id == tenantId 
            && (x.date_from.Year <= budgetYear && x.date_to.Year >= budgetYear) && projCodes.Contains(x.pk_project_code));
        }
    }

}
