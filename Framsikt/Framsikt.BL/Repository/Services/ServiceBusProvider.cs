using Azure.Identity;
using Azure.Messaging.ServiceBus;
using Framsikt.BL.Core.Repository.Contracts;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System.Text;

namespace Framsikt.BL.Core.Repository.Services
{
    public class ServiceBusProvider : IServiceBusProvider
    {
        private readonly string connectionString;
        private readonly ServiceBusClient client;

        public ServiceBusProvider(IConfiguration config)
        {
            connectionString = config.GetConnectionString("ServiceBusConnection");
            if (string.IsNullOrEmpty(connectionString))
            {
                throw new ArgumentException("Service Bus connection string is not provided in the configuration.");
            }
            client = new ServiceBusClient(connectionString, new DefaultAzureCredential());
        }

        public ServiceBusProcessor CreateProcessor(string topicName, string subscriptionName, ServiceBusProcessorOptions options)
        {
            if (string.IsNullOrEmpty(subscriptionName))
            {
                return client.CreateProcessor(topicName.ToLower(), options);
            }
            return client.CreateProcessor(topicName.ToLower(), subscriptionName.ToLower(), options);
        }

        public ServiceBusSessionProcessor CreateSessionProcessor(string topicName, string subscriptionName, ServiceBusSessionProcessorOptions options)
        {
            if (string.IsNullOrEmpty(subscriptionName))
            {
                return client.CreateSessionProcessor(topicName.ToLower(), options);
            }
            return client.CreateSessionProcessor(topicName.ToLower(), subscriptionName.ToLower(), options);
        }

        public async Task SendMessageToTopicAsync(string message, string topic)
        {
            ServiceBusSender sender = client.CreateSender(topic);
            await sender.SendMessageAsync(new ServiceBusMessage(message));
        }

        public async Task<long> ScheduleMessageAsync(string queue, string jobId, string request, DateTime scheduledTime)
        {
            ServiceBusSender sender = client.CreateSender(queue);
            ServiceBusMessage message = new ServiceBusMessage(request);
            message.MessageId = jobId;
            return await sender.ScheduleMessageAsync(message, scheduledTime);
        }

        public async Task<ServiceBusReceivedMessage> ReadMessageFromTopic(string topic, string subscription)
        {
            ServiceBusReceiver receiver = client.CreateReceiver(topic, subscription);
            ServiceBusReceivedMessage receivedMessage = await receiver.ReceiveMessageAsync();
            return receivedMessage;
        }

        public async Task SendMessageToQueueAsync(object message, string queue)
        {
            ServiceBusSender sender = client.CreateSender(queue);
            ServiceBusMessage serviceBusMessage = new ServiceBusMessage(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(message)));
            await sender.SendMessageAsync(serviceBusMessage);
        }

        public async Task SendMessageWithSessionToQueueAsync(object message, string session, string queue)
        {
            ServiceBusSender sender = client.CreateSender(queue);
            ServiceBusMessage serviceBusMessage = new ServiceBusMessage(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(message))) { SessionId = session };
            await sender.SendMessageAsync(serviceBusMessage);
        }
    }
}