#pragma warning disable CS8629
#pragma warning disable CS8600
#pragma warning disable CS8602
#pragma warning disable CS8603
#pragma warning disable CS8625

using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using System.Runtime.Intrinsics.Arm;
using System.Text;

namespace Framsikt.BL.Repository
{
    public class FinancialPlanOverviewRepository : IFinancialPlanOverviewRepository
    {
        private readonly TenantDBContext _tenantDBContext;
        private IDbContextManager _dbContextManager;

        public FinancialPlanOverviewRepository(IDbContextManager dbContextManager)
        {
            _tenantDBContext = dbContextManager.GetTenantDbContext();
            _dbContextManager = dbContextManager;
        }

        #region Public methods

        public async Task<List<FinplanDropdownHelper>> GetAdjustmentCodesForActions(int tenantId, bool getOnlyActiveAdjCodes)
        {
            List<FinplanDropdownHelper> adjustmentCodesData = new List<FinplanDropdownHelper>();
            if (getOnlyActiveAdjCodes)
            {
                adjustmentCodesData = await (from a in _tenantDBContext.tco_adjustment_codes.AsNoTracking()
                                             where a.fk_tenant_id == tenantId && a.status == 1
                                             select new FinplanDropdownHelper()
                                             {
                                                 Key = a.pk_adjustment_code,
                                                 Value = a.pk_adjustment_code + "-" + a.description,
                                                 Description = a.description
                                             }).ToListAsync();
            }
            else
            {
                adjustmentCodesData = await (from a in _tenantDBContext.tco_adjustment_codes.AsNoTracking()
                                             where a.fk_tenant_id == tenantId
                                             select new FinplanDropdownHelper()
                                             {
                                                 Key = a.pk_adjustment_code,
                                                 Value = a.pk_adjustment_code + "-" + a.description,
                                                 Description = a.description
                                             }).ToListAsync();
            }
            return adjustmentCodesData;
        }

        public async Task<List<ClsAlterCodeData>> GetAlterCodesForActions(int tenantId, int actionType, bool getOnlyActiveAlterCode, PageIdType pageId, string defaultAlterCodeValue)
        {
            var dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            List<ClsAlterCodeData> alterCodeList = new List<ClsAlterCodeData>();
            int budgetChangeDefaultActionType = 60; //fetch altercodes for budgetChange using both actionType and BudChangeDefaultActionType
            string attributeType = "ACTIONTYPE";
            string relationType = "ALTERCODE";
            List<string> attributeValues = new List<string>() { "9", "31", "41" };

            switch (actionType)
            {
                case 0:
                    alterCodeList = await (from a in dbContext.tco_fp_alter_codes.AsNoTracking()
                                           join b in dbContext.tco_relation_values.AsNoTracking() on a.fk_tenant_id equals b.fk_tenant_id
                                           where a.fk_tenant_id == tenantId && b.attribute_type == attributeType && b.relation_type == relationType
                                           && attributeValues.Contains(b.attribute_value)
                                           && (a.pk_alter_code.CompareTo(b.relation_value_from) >= 0) && (a.pk_alter_code.CompareTo(b.relation_value_to) <= 0)
                                           group new { a, b } by new { a.pk_alter_code, a.alter_description, alterCodeStatus = a.status, relationStatus = b.status } into g
                                           select new ClsAlterCodeData
                                           {
                                               key = g.Key.pk_alter_code,
                                               value = g.Key.pk_alter_code + "-" + g.Key.alter_description,
                                               isDefault = g.Key.pk_alter_code == defaultAlterCodeValue,
                                               status1 = g.Key.alterCodeStatus,
                                               status2 = g.Key.relationStatus,
                                               onlyValue = g.Key.alter_description,
                                           }).OrderBy(x => x.key).ToListAsync();
                    break;

                case -1:
                    alterCodeList = await (from a in dbContext.tco_fp_alter_codes.AsNoTracking()
                                           join b in dbContext.tco_relation_values.AsNoTracking() on a.fk_tenant_id equals b.fk_tenant_id
                                           where a.fk_tenant_id == tenantId && b.attribute_type == attributeType && b.relation_type == relationType
                                           && (a.pk_alter_code.CompareTo(b.relation_value_from) >= 0) && (a.pk_alter_code.CompareTo(b.relation_value_to) <= 0)
                                           group new { a, b } by new { a.pk_alter_code, a.alter_description, alterCodeStatus = a.status, relationStatus = b.status } into g
                                           select new ClsAlterCodeData
                                           {
                                               key = g.Key.pk_alter_code,
                                               value = g.Key.pk_alter_code + "-" + g.Key.alter_description,
                                               isDefault = g.Key.pk_alter_code == defaultAlterCodeValue,
                                               status1 = g.Key.alterCodeStatus,
                                               status2 = g.Key.relationStatus,
                                               onlyValue = g.Key.alter_description,
                                           }).OrderBy(x => x.key).ToListAsync();
                    break;

                default:
                    if (pageId == PageIdType.BudgetChangeOverview)
                    {
                        alterCodeList = await (from a in dbContext.tco_fp_alter_codes.AsNoTracking()
                                               join b in dbContext.tco_relation_values.AsNoTracking() on a.fk_tenant_id equals b.fk_tenant_id
                                               where a.fk_tenant_id == tenantId && b.attribute_type == attributeType && b.relation_type == relationType
                                               && (b.attribute_value == actionType.ToString() || b.attribute_value == budgetChangeDefaultActionType.ToString())
                                               && (a.pk_alter_code.CompareTo(b.relation_value_from) >= 0) && (a.pk_alter_code.CompareTo(b.relation_value_to) <= 0)
                                               group new { a, b } by new { a.pk_alter_code, a.alter_description, alterCodeStatus = a.status, relationStatus = b.status } into g
                                               select new ClsAlterCodeData
                                               {
                                                   key = g.Key.pk_alter_code,
                                                   value = g.Key.pk_alter_code + "-" + g.Key.alter_description,
                                                   isDefault = g.Key.pk_alter_code == defaultAlterCodeValue,
                                                   status1 = g.Key.alterCodeStatus,
                                                   status2 = g.Key.relationStatus,
                                                   onlyValue = g.Key.alter_description,
                                               }).OrderBy(x => x.key).ToListAsync();
                    }
                    else
                    {
                        alterCodeList = await (from a in dbContext.tco_fp_alter_codes.AsNoTracking()
                                               join b in dbContext.tco_relation_values.AsNoTracking() on a.fk_tenant_id equals b.fk_tenant_id
                                               where a.fk_tenant_id == tenantId && b.attribute_type == attributeType && b.relation_type == relationType
                                               //&& b.attribute_value == actionType.ToString()
                                               && (a.pk_alter_code.CompareTo(b.relation_value_from) >= 0) && (a.pk_alter_code.CompareTo(b.relation_value_to) <= 0)
                                               group new { a, b } by new { a.pk_alter_code, a.alter_description, alterCodeStatus = a.status, relationStatus = b.status, isActive = b.attribute_value == actionType.ToString() } into g
                                               select new ClsAlterCodeData
                                               {
                                                   key = g.Key.pk_alter_code,
                                                   value = g.Key.pk_alter_code + "-" + g.Key.alter_description,
                                                   isDefault = g.Key.pk_alter_code == defaultAlterCodeValue,
                                                   status1 = g.Key.alterCodeStatus,
                                                   status2 = g.Key.relationStatus,
                                                   onlyValue = g.Key.alter_description,
                                                   isActive = g.Key.isActive
                                               }).OrderBy(x => x.key).ToListAsync();
                    }
                    break;
            }

            if (getOnlyActiveAlterCode)
            {
                alterCodeList = alterCodeList.Where(x => x.status1 == 1 && x.status2 == 1).ToList();
            }

            return alterCodeList.OrderBy(x => x.key).ToList();
        }

        public async Task<List<int>> GetChangeIdsBasedOnPageId(int clientId, int tenantId, int budgetYear, int changeId,
            int userId, PageIdType pageId)
        {
            List<int> changeIds = new List<int>();
            switch (pageId)
            {
                case PageIdType.BudgetChangeOverview:
                case PageIdType.BudgetChangeDetail:
                case PageIdType.YearlyBudget:
                case PageIdType.ServiceUnitBudget:
                    changeIds = await (from a in _tenantDBContext.tfp_budget_changes.AsNoTracking()
                                       where a.fk_tenant_id == tenantId && a.budget_year == budgetYear && a.pk_change_id != changeId
                                       select a.pk_change_id).ToListAsync();
                    break;

                case PageIdType.ServiceUnitForUnlockedBudget:
                    changeIds = await (from a in _tenantDBContext.tfp_budget_changes.AsNoTracking()
                                       where a.fk_tenant_id == tenantId && a.budget_year == budgetYear && a.status == 1 && a.org_budget_flag == 0
                                       select a.pk_change_id).ToListAsync();
                    break;

                case PageIdType.BudgetManagementDetail:
                    changeIds = changeId == 0 ?
                            await (from a in _tenantDBContext.tfp_budget_changes.AsNoTracking()
                                   where a.fk_tenant_id == tenantId && a.budget_year == budgetYear
                                   select a.pk_change_id).ToListAsync() :
                            await (from a in _tenantDBContext.tfp_budget_changes.AsNoTracking()
                                   where ((a.fk_tenant_id == tenantId && a.budget_year == budgetYear && a.status != 1) || (a.fk_tenant_id == tenantId && a.budget_year == budgetYear && a.status == 1 && a.workflow_status != 30))
                                   select a.pk_change_id).ToListAsync();
                    break;

                default:
                    changeIds = await (from a in _tenantDBContext.tfp_budget_changes.AsNoTracking()
                                       where a.fk_tenant_id == tenantId && a.budget_year == budgetYear && a.org_budget_flag == 1
                                       && ((a.status != 1) || (a.status == 1 && a.pk_change_id != changeId))
                                       select a.pk_change_id).ToListAsync();

                    var userSettingInfo = await GetUserSettingInfo(userId, clientId);
                    if (userSettingInfo != null && userSettingInfo.active_change_id != null && userSettingInfo.active_change_id == changeId)
                    {
                        var changeIdToBeRemoved = await _tenantDBContext.tfp_budget_changes.AsNoTracking().FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId
                        && x.budget_year == budgetYear && x.pk_change_id == changeId && x.status == 1);

                        if (changeIdToBeRemoved == null)
                        {
                            /*closed budget round but active for logged in user*/
                            changeIds.Remove(changeIds.FirstOrDefault(x => x == changeId));
                        }
                    }
                    break;
            }
            return changeIds;
        }

        public async Task<List<FinplanDropdownHelper>> GetDepartmentsData(int tenantId, int budgetYear)
        {
            var result = await (from a in _tenantDBContext.tco_departments.AsNoTracking()
                                where a.fk_tenant_id == tenantId && (a.year_from <= budgetYear && a.year_to >= budgetYear)
                                group a by new { a.pk_department_code, a.department_name } into g
                                select new FinplanDropdownHelper()
                                {
                                    Key = g.Key.pk_department_code,
                                    Value = g.Key.department_name
                                }).ToListAsync();
            return result;
        }

        public async Task<List<ActionDetailGridDataHelper>> GetActionTransDetailData(int tenantId, int budgetYear, int actionId)
        {
            var result = await (from a in _tenantDBContext.tfp_trans_detail.AsNoTracking()
                                join b in _tenantDBContext.tfp_budget_changes.AsNoTracking() on new { a = a.fk_tenant_id, b = a.fk_change_id, c = a.budget_year }
                                equals new { a = b.fk_tenant_id, b = b.pk_change_id, c = b.budget_year } into budRound
                                from budRoundRes in budRound.DefaultIfEmpty()
                                join tp in _tenantDBContext.tco_projects.Where(x => x.date_from.Year <= budgetYear && x.date_to.Year >= budgetYear)
                                                                         on new { a = a.fk_tenant_id, b = a.project_code }
                                                                     equals new { a = tp.fk_tenant_id, b = tp.pk_project_code } into tpl

                                from tp in tpl.DefaultIfEmpty()
                                where a.fk_tenant_id == tenantId && a.fk_action_id == actionId && a.budget_year == budgetYear
                                group new { a, budRoundRes, tp } by new
                                {
                                    a.pk_id,
                                    a.fk_account_code,
                                    a.department_code,
                                    a.function_code,
                                    a.project_code,
                                    a.free_dim_1,
                                    a.free_dim_2,
                                    a.free_dim_3,
                                    a.free_dim_4,
                                    a.fk_adjustment_code,
                                    a.fk_alter_code,
                                    a.description,
                                    a.fk_change_id,
                                    budgetRound = budRoundRes == null ? string.Empty : budRoundRes.approval_reference,
                                    mainProjectCode = tp == null ? string.Empty : tp.fk_main_project_code,
                                    updatedDate = a.updated
                                } into g
                                select new ActionDetailGridDataHelper()
                                {
                                    RowId = g.Key.pk_id,
                                    AccountCode = g.Key.fk_account_code,
                                    DepartmentCode = g.Key.department_code,
                                    FunctionCode = g.Key.function_code,
                                    ProjectCode = g.Key.project_code,
                                    Freedim1Code = g.Key.free_dim_1,
                                    Freedim2Code = g.Key.free_dim_2,
                                    Freedim3Code = g.Key.free_dim_3,
                                    Freedim4Code = g.Key.free_dim_4,
                                    AlterCode = g.Key.fk_alter_code,
                                    AdjustmentCode = g.Key.fk_adjustment_code,
                                    Description = g.Key.description,
                                    ChangeId = g.Key.fk_change_id,
                                    BudgetRound = g.Key.budgetRound,
                                    mainProjectCode = g.Key.mainProjectCode,
                                    updatedDate = g.Key.updatedDate,
                                    Year1 = g.Sum(x => x.a.year_1_amount),
                                    Year2 = g.Sum(x => x.a.year_2_amount),
                                    Year3 = g.Sum(x => x.a.year_3_amount),
                                    Year4 = g.Sum(x => x.a.year_4_amount),
                                    Year5 = g.Sum(x => x.a.year_5_amount),
                                    Year6 = g.Sum(x => x.a.year_6_amount),
                                    Year7 = g.Sum(x => x.a.year_7_amount),
                                    Year8 = g.Sum(x => x.a.year_8_amount),
                                    Year9 = g.Sum(x => x.a.year_9_amount),
                                    Year10 = g.Sum(x => x.a.year_10_amount),
                                }).ToListAsync();
            return result;
        }

        public async Task<List<ActionDetailGridDataHelper>> GetActionTransDetailDataBList(int tenantId, int budgetYear, int actionId, bool isParkedAction)
        {
            var result = await (from th in _tenantDBContext.tfp_temp_header.AsNoTracking()
                                join a in _tenantDBContext.tfp_temp_detail.AsNoTracking() on new { c = th.fk_tenant_id, a = th.pk_temp_id }
                                                                       equals new { c = a.fk_tenant_id, a = a.fk_temp_id }
                                join b in _tenantDBContext.tfp_budget_changes.AsNoTracking() on new { a = a.fk_tenant_id, b = a.fk_change_id, c = a.budget_year }
                                equals new { a = b.fk_tenant_id, b = b.pk_change_id, c = b.budget_year } into budRound
                                from budRoundRes in budRound.DefaultIfEmpty()
                                where a.fk_tenant_id == tenantId && a.budget_year == budgetYear && a.fk_temp_id == actionId
                                group new { a, budRoundRes } by new
                                {
                                    a.pk_id,
                                    a.fk_account_code,
                                    a.department_code,
                                    a.function_code,
                                    a.project_code,
                                    a.free_dim_1,
                                    a.free_dim_2,
                                    a.free_dim_3,
                                    a.free_dim_4,
                                    a.fk_adjustment_code,
                                    a.fk_alter_code,
                                    a.description,
                                    a.fk_change_id,
                                    budgetRound = budRoundRes == null ? string.Empty : budRoundRes.approval_reference,
                                } into g
                                select new ActionDetailGridDataHelper()
                                {
                                    RowId = g.Key.pk_id,
                                    AccountCode = g.Key.fk_account_code,
                                    DepartmentCode = g.Key.department_code,
                                    FunctionCode = g.Key.function_code,
                                    ProjectCode = g.Key.project_code,
                                    Freedim1Code = g.Key.free_dim_1,
                                    Freedim2Code = g.Key.free_dim_2,
                                    Freedim3Code = g.Key.free_dim_3,
                                    Freedim4Code = g.Key.free_dim_4,
                                    AlterCode = g.Key.fk_alter_code,
                                    AdjustmentCode = g.Key.fk_adjustment_code,
                                    Description = g.Key.description,
                                    ChangeId = g.Key.fk_change_id,
                                    BudgetRound = g.Key.budgetRound,
                                    Year1 = g.Sum(x => x.a.year_1_amount),
                                    Year2 = g.Sum(x => x.a.year_2_amount),
                                    Year3 = g.Sum(x => x.a.year_3_amount),
                                    Year4 = g.Sum(x => x.a.year_4_amount),
                                    Year5 = (decimal)g.Sum(x => x.a.year_5_amount),
                                    Year6 = (decimal)g.Sum(x => x.a.year_6_amount),
                                    Year7 = (decimal)g.Sum(x => x.a.year_7_amount),
                                    Year8 = (decimal)g.Sum(x => x.a.year_8_amount),
                                    Year9 = (decimal)g.Sum(x => x.a.year_9_amount),
                                    Year10 = (decimal)g.Sum(x => x.a.year_10_amount),
                                }).ToListAsync();
            return result;
        }

        public async Task<tfp_trans_header> GetActionHeaderInfo(int tenantId, int actionId)
        {
            return await _tenantDBContext.tfp_trans_header.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.pk_action_id == actionId);
        }


        public async Task<TcoClimateActionHeader> GetClimateActionHeaderInfo(int tenantId, int actionId)
        {
            return await _tenantDBContext.TcoClimateActionHeader.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.pkClimateActionId == actionId);
        }

        public async Task<tfp_strategy_goal> GetGoalCOnnectedToStrategy(int tenantId, int strategyId)
        {
            return await _tenantDBContext.tfp_strategy_goal.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.fk_strategy_id == strategyId);
        }       

        public async Task<tco_targets> GetGoalConnectedToTarget(int tenantId, Guid targetId)
        {
            return await _tenantDBContext.tco_targets.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.pk_target_id == targetId);
        }

        public async Task<tfp_strategy_target> GetTargetCnnectedToStrategy(int tenantId, int strategyId)
        {
            return await _tenantDBContext.tfp_strategy_target.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.fk_strategy_id == strategyId);
        }

        public async Task<tfp_budget_changes> GetCurrentBudgetRoundInfo(int tenantId, int changeId, int budgetYear, PageIdType pageId)
        {
            switch (pageId)
            {
                case PageIdType.FinancialPlanOverview:
                case PageIdType.BudgetManagementDetail:
                    return await _tenantDBContext.tfp_budget_changes.AsNoTracking().FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.pk_change_id == changeId && x.budget_year == budgetYear);

                case PageIdType.BudgetProposalInternalActions:
                    return await (from a in _tenantDBContext.tfp_budget_changes
                                  join b in _tenantDBContext.tco_budget_phase
                                  on new { a = a.fk_tenant_id, b = a.fk_budget_phase_id } equals new { a = b.fk_tenant_id, b = b.pk_budget_phase_id }
                                  where a.fk_tenant_id == tenantId && a.budget_year == budgetYear && a.status == 1 && b.budalloc_flag && a.org_budget_flag == 1
                                  select new tfp_budget_changes
                                  {
                                      pk_change_id = a.pk_change_id,
                                      approval_reference = a.approval_reference,
                                      budget_name = a.budget_name
                                  }).AsNoTracking().FirstOrDefaultAsync();

                default:
                    return await _tenantDBContext.tfp_budget_changes.AsNoTracking().FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId
                                                                                                    && x.budget_year == budgetYear
                                                                                                    && x.status == 1
                                                                                                    && x.org_budget_flag == 1
                                                                                                    && x.workflow_status == 30);
            }
        }

        public async Task<tfp_trans_detail> GetActionDetailRowById(int tenantId, int detailRowId, int actionId, int changeId, int budgetYear)
        {
            return await _tenantDBContext.tfp_trans_detail.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.fk_action_id == actionId && x.pk_id == detailRowId
            && x.budget_year == budgetYear && x.fk_change_id == changeId);
        }

        public async Task<List<tfp_proj_transactions>> GetProjectTransactionsData(int tenantId, Guid transId)
        {
            return await _tenantDBContext.tfp_proj_transactions.Where(x => x.fk_tenant_id == tenantId && x.trans_id == transId).ToListAsync();
        }

        public async Task<TcoActionTags> GetActionTagInfo(int tenantId, string tagName)
        {
            return await _tenantDBContext.tcoActionTags.FirstOrDefaultAsync(x => x.FkTenantId == tenantId && x.TagDescription.Trim() == tagName.Trim());
        }

        public async Task<List<KeyValueData>> GetActionTags(int tenantId, List<int> tagIds)
        {
            var actionTags = await (from atg in _tenantDBContext.tcoActionTags
                                    where atg.FkTenantId == tenantId
                                    && tagIds.Contains(atg.PkId)
                                    select new KeyValueData
                                    {
                                        KeyId = atg.PkId,
                                        ValueString = atg.TagDescription
                                    }).OrderBy(x => x.ValueString).AsNoTracking().ToListAsync();
            return actionTags;
        }

        public async Task<List<tco_adjustment_codes>> GetAllAdjustementCodesFromDb(int tenantId, bool getOnlyActive)
        {
            var dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            if (getOnlyActive)
            {
                return await dbContext.tco_adjustment_codes.Where(x => x.fk_tenant_id == tenantId && x.status == 1).AsNoTracking().ToListAsync();
            }
            else
            {
                return await dbContext.tco_adjustment_codes.Where(x => x.fk_tenant_id == tenantId).AsNoTracking().ToListAsync();
            }
        }

        public List<tco_accounts> GetAllAccountCodesFromDb(int tenantId, int actionId, int budgetYear)
        {
            var result = GetAllAccountCodesFromDbAsync(tenantId, actionId, budgetYear).GetAwaiter().GetResult();
            return result;
        }

        public async Task<List<tco_accounts>> GetAllAccountCodesFromDbAsync(int tenantId, int actionId, int budgetYear)
        {
            tfp_trans_header th = null;

            if (actionId != 0)
            {
                th = await _tenantDBContext.tfp_trans_header.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.pk_action_id == actionId);
            }
            if (th != null && th.line_order == -1)
            {
                var accounts = await (from ac in _tenantDBContext.tco_accounts
                                      where ac.pk_tenant_id == tenantId
                                      && ac.isActive
                                      && budgetYear >= ac.dateFrom.Year && budgetYear <= ac.dateTo.Year
                                      && ac.fk_kostra_account_code == "1990"
                                      orderby ac.display_name
                                      select ac).AsNoTracking().ToListAsync();

                return accounts.OrderBy(z => z.pk_account_code).ToList();
            }
            else
            {
                var accounts = await (from ac in _tenantDBContext.tco_accounts
                                      join ka in _tenantDBContext.gco_kostra_accounts on ac.fk_kostra_account_code equals ka.pk_kostra_account_code
                                      where (ac.pk_tenant_id == tenantId) && ka.type == "operations" && budgetYear >= ac.dateFrom.Year && budgetYear <= ac.dateTo.Year
                                      && ac.isActive
                                      orderby ac.display_name
                                      select ac).Distinct().AsNoTracking().ToListAsync();

                return accounts.OrderBy(z => z.pk_account_code).ToList();
            }
        }

        public async Task<List<tco_departments>> GetAllDepartmentCodesFromDb(int tenantId, int budgetYear, string orgId, int orgLevel, string orgVersion)
        {
            var dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();

            if (!string.IsNullOrEmpty(orgId) && orgLevel != 0)
            {
                switch (orgLevel)
                {
                    case 1:
                        return await (from ac in dbContext.tco_departments
                                      join oh in dbContext.tco_org_hierarchy on new { a = ac.fk_tenant_id, b = ac.pk_department_code } equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                                      where (ac.fk_tenant_id == tenantId) && ac.status == 1 && budgetYear >= ac.year_from && budgetYear <= ac.year_to
                                      && oh.org_id_1 == orgId
                                      && oh.fk_org_version == orgVersion
                                      //orderby ac.department_name
                                      select ac).OrderBy(z => z.pk_department_code).Distinct().AsNoTracking().ToListAsync();

                    case 2:
                        return await (from ac in dbContext.tco_departments
                                      join oh in dbContext.tco_org_hierarchy on new { a = ac.fk_tenant_id, b = ac.pk_department_code } equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                                      where (ac.fk_tenant_id == tenantId) && ac.status == 1 && budgetYear >= ac.year_from && budgetYear <= ac.year_to
                                      && oh.org_id_2 == orgId
                                      && oh.fk_org_version == orgVersion
                                      // orderby ac.department_name
                                      select ac).OrderBy(z => z.pk_department_code).Distinct().AsNoTracking().ToListAsync();

                    case 3:
                        return await (from ac in dbContext.tco_departments
                                      join oh in dbContext.tco_org_hierarchy on new { a = ac.fk_tenant_id, b = ac.pk_department_code } equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                                      where (ac.fk_tenant_id == tenantId) && ac.status == 1 && budgetYear >= ac.year_from && budgetYear <= ac.year_to
                                      && oh.org_id_3 == orgId
                                      && oh.fk_org_version == orgVersion
                                      // orderby ac.department_name
                                      select ac).OrderBy(z => z.pk_department_code).Distinct().AsNoTracking().ToListAsync();

                    case 4:
                        return await (from ac in dbContext.tco_departments
                                      join oh in dbContext.tco_org_hierarchy on new { a = ac.fk_tenant_id, b = ac.pk_department_code } equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                                      where (ac.fk_tenant_id == tenantId) && ac.status == 1 && budgetYear >= ac.year_from && budgetYear <= ac.year_to
                                      && oh.org_id_4 == orgId
                                      && oh.fk_org_version == orgVersion
                                      // orderby ac.department_name
                                      select ac).Distinct().AsNoTracking().ToListAsync();

                    case 5:
                        return await (from ac in dbContext.tco_departments
                                      join oh in dbContext.tco_org_hierarchy on new { a = ac.fk_tenant_id, b = ac.pk_department_code } equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                                      where (ac.fk_tenant_id == tenantId) && ac.status == 1 && budgetYear >= ac.year_from && budgetYear <= ac.year_to
                                      && oh.org_id_5 == orgId
                                      && oh.fk_org_version == orgVersion
                                      // orderby ac.department_name
                                      select ac).OrderBy(z => z.pk_department_code).Distinct().AsNoTracking().ToListAsync();

                    case 6:
                        return await (from ac in dbContext.tco_departments
                                      join oh in dbContext.tco_org_hierarchy on new { a = ac.fk_tenant_id, b = ac.pk_department_code } equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                                      where (ac.fk_tenant_id == tenantId) && ac.status == 1 && budgetYear >= ac.year_from && budgetYear <= ac.year_to
                                      && oh.org_id_6 == orgId
                                      && oh.fk_org_version == orgVersion
                                      orderby ac.department_name
                                      select ac).OrderBy(z => z.pk_department_code).Distinct().AsNoTracking().ToListAsync();

                    case 7:
                        return await (from ac in dbContext.tco_departments
                                      join oh in dbContext.tco_org_hierarchy on new { a = ac.fk_tenant_id, b = ac.pk_department_code } equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                                      where (ac.fk_tenant_id == tenantId) && ac.status == 1 && budgetYear >= ac.year_from && budgetYear <= ac.year_to
                                      && oh.org_id_7 == orgId
                                      && oh.fk_org_version == orgVersion
                                      // orderby ac.department_name
                                      select ac).OrderBy(z => z.pk_department_code).Distinct().AsNoTracking().ToListAsync();

                    case 8:
                        return await (from ac in dbContext.tco_departments
                                      join oh in dbContext.tco_org_hierarchy on new { a = ac.fk_tenant_id, b = ac.pk_department_code } equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                                      where (ac.fk_tenant_id == tenantId) && ac.status == 1 && budgetYear >= ac.year_from && budgetYear <= ac.year_to
                                      && oh.org_id_8 == orgId
                                      && oh.fk_org_version == orgVersion
                                      orderby ac.department_name
                                      select ac).OrderBy(z => z.pk_department_code).Distinct().AsNoTracking().ToListAsync();

                    default:
                        return await (from ac in dbContext.tco_departments
                                      join oh in dbContext.tco_org_hierarchy on new { a = ac.fk_tenant_id, b = ac.pk_department_code } equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                                      where (ac.fk_tenant_id == tenantId) && ac.status == 1 && budgetYear >= ac.year_from && budgetYear <= ac.year_to
                                      && oh.org_id_1 == orgId
                                        && oh.fk_org_version == orgVersion
                                      //   orderby ac.department_name
                                      select ac).OrderBy(z => z.pk_department_code).Distinct().AsNoTracking().ToListAsync();
                }
            }
            else
            {
                return await (from ac in dbContext.tco_departments
                              where (ac.fk_tenant_id == tenantId) && ac.status == 1 && budgetYear >= ac.year_from && budgetYear <= ac.year_to
                              orderby ac.department_name
                              select ac).OrderBy(z => z.pk_department_code).Distinct().AsNoTracking().ToListAsync();
            }
        }

        public async Task<List<tco_functions>> GetAllFunctionCodesFromDb(int tenantId, int budgetYear)
        {
            var dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            var functions = await (from ac in dbContext.tco_functions
                                   where (ac.pk_tenant_id == tenantId) && ac.isActive && budgetYear >= ac.dateFrom.Year && budgetYear <= ac.dateTo.Year
                                   orderby ac.display_name
                                   select ac).Distinct().AsNoTracking().ToListAsync();

            return functions;
        }

        public async Task<List<tco_free_dim_values>> GetAllFridimCodesFromDb(int tenantId, List<string> fridmColm)
        {
            var dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            var fridm = await (from ac in dbContext.tco_free_dim_values
                               where (ac.fk_tenant_id == tenantId) && fridmColm.Contains(ac.free_dim_column) && ac.status == 1
                               orderby ac.free_dim_column
                               select ac).Distinct().AsNoTracking().ToListAsync();

            return fridm;
        }

        public async Task<List<tco_periodic_key>> GetAllPeriodicKeyFromDb(int tenantId)
        {
            var dbContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            var periodic = await (from pk in dbContext.tco_periodic_key
                                  where (pk.fk_tenant_id == tenantId || pk.fk_tenant_id == 0)
                                  orderby pk.sorting
                                  select pk).Distinct().AsNoTracking().ToListAsync();

            return periodic;
        }

        public async Task<tfp_trans_detail> GetActionDetailRowById(int tenantId, int actionId, int rowId, int budgetYear)
        {
            return await _tenantDBContext.tfp_trans_detail.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.pk_id == rowId && x.fk_action_id == actionId
            && x.budget_year == budgetYear);
        }

        public async Task<List<tfp_trans_detail>> GetActionDetailRows(int tenantId, int actionId, int budgetYear)
        {
            return await _tenantDBContext.tfp_trans_detail.Where(x => x.fk_tenant_id == tenantId && x.fk_action_id == actionId
            && x.budget_year == budgetYear).ToListAsync();
        }

        public async Task<string> GetDefaultAlterCode(int tenantId, int actionType)
        {
            var defualtAlterCode = await _tenantDBContext.tco_fp_alter_codes.Where(x => x.fk_tenant_id == tenantId && x.default_action_type == actionType && x.status == 1)
                .GroupBy(x => x.pk_alter_code).Select(x => x.Min(y => y.pk_alter_code)).FirstOrDefaultAsync();
            if (string.IsNullOrEmpty(defualtAlterCode))
            {
                return string.Empty;
            }
            return defualtAlterCode;
        }

        public async Task<int> GetMaxLineOrder(int tenantId)
        {
            return await _tenantDBContext.tmd_finplan_line_setup.Where(x => x.fk_tenant_id == tenantId).MaxAsync(x => x.line_order);
        }

        public async Task<IEnumerable<tmd_finplan_line_setup>> GetFinplanLineSetupData(int tenantId, int budgetYear, int actionType, string actionName)
        {
            return await _tenantDBContext.tmd_finplan_line_setup.AsNoTracking().Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear
            && x.action_type == actionType && x.action_name == actionName).OrderBy(x => x.priority).Select(x => x).ToListAsync();
        }

        public List<OrgSelectorHelper> GetOrgSetupData(int tenantId, int budgetYear, string finplanLevel1, string finplanLevel2, string category, string orgVersion, List<tco_org_hierarchy> lstOrgHierarchy)
        {
            var data = lstOrgHierarchy.Where(x => x.fk_tenant_id == tenantId && x.fk_org_version == orgVersion).Distinct().ToList();
            var orgNameData = GetOrgNameData(finplanLevel1, "", data, false);
            if (category == "SERVICE")
            {
                return orgNameData;
            }
            else
            {
                var resultData = new List<OrgSelectorHelper>();
                foreach (var item in orgNameData)
                {
                    var result = new OrgSelectorHelper();
                    result.id = item.id;
                    result.name = item.name;
                    result.selName = item.name;
                    result.isDefault = false;
                    result.isDisabled = false;
                    result.isLastLevel = false;
                    result.levelNo = item.levelNo;
                    result.levelsInfo = new JArray();
                    result.subLevel = GetOrgNameData(finplanLevel2, item.id, data, true);
                    resultData.Add(result);
                }
                return resultData;
            }
        }

        public async Task<List<KeyValueStringHelper>> GetServiceSetupData(int tenantId, string selectedOrgId, int budgetYear, string finplanLevel1, string finplanLevel2, List<tco_org_hierarchy> lstOrgHierarchy, string allLangStringName)
        {
            List<int> actionTypes = new List<int>();
            actionTypes.Add(100); actionTypes.Add(101); actionTypes.Add(2); actionTypes.Add(5); actionTypes.Add(6); actionTypes.Add(41); actionTypes.Add(9); actionTypes.Add(31); actionTypes.Add(30); actionTypes.Add(40); actionTypes.Add(7); actionTypes.Add(20); actionTypes.Add(21); actionTypes.Add(90); actionTypes.Add(61); actionTypes.Add(80);
            var initialData = await (from p in _tenantDBContext.tfp_trans_header
                                     join q in _tenantDBContext.tfp_trans_detail
                                     on new { a = p.fk_tenant_id, b = p.pk_action_id } equals new { a = q.fk_tenant_id, b = q.fk_action_id }
                                     join s in _tenantDBContext.tco_service_values
                                     on new { a = q.fk_tenant_id, b = q.function_code } equals new { a = s.fk_tenant_id, b = s.fk_function_code }
                                     where p.fk_tenant_id == tenantId && q.budget_year == budgetYear && actionTypes.Contains(p.action_type)
                                     select new { q.department_code, s }).Distinct().ToListAsync();
            var orgData = (from a in initialData
                           join b in lstOrgHierarchy
                           on a.department_code equals b.fk_department_code
                           where b.fk_tenant_id == tenantId
                           select new
                           {
                               orgId1 = b.org_id_1,
                               orgId2 = b.org_id_2,
                               orgId3 = b.org_id_3,
                               orgId4 = b.org_id_4,
                               orgId5 = b.org_id_5,
                               serviceId1 = a.s.service_id_1,
                               serviceId2 = a.s.service_id_2,
                               serviceId3 = a.s.service_id_3,
                               serviceId4 = a.s.service_id_4,
                               serviceId5 = a.s.service_id_5,
                               serviceName1 = a.s.service_name_1,
                               serviceName2 = a.s.service_name_2,
                               serviceName3 = a.s.service_name_3,
                               serviceName4 = a.s.service_name_4,
                               serviceName5 = a.s.service_name_5
                           }).Distinct().ToList();
            var data = (from a in orgData
                        select new ServiceSetupDataHelper
                        {
                            OrgId1 = a.orgId1,
                            OrgId2 = a.orgId2,
                            OrgId3 = a.orgId3,
                            OrgId4 = a.orgId4,
                            OrgId5 = a.orgId5,
                            ServiceId1 = a.serviceId1,
                            ServiceId2 = a.serviceId2,
                            ServiceId3 = a.serviceId3,
                            ServiceId4 = a.serviceId4,
                            ServiceId5 = a.serviceId5,
                            ServiceName1 = a.serviceName1,
                            ServiceName2 = a.serviceName2,
                            ServiceName3 = a.serviceName3,
                            ServiceName4 = a.serviceName4,
                            ServiceName5 = a.serviceName5
                        }).ToList();
            var selectedOrgData = GetSelectedOrgData(selectedOrgId, finplanLevel1, data);
            var serviceData = GetServiceNameData(finplanLevel2, selectedOrgData);
            if (serviceData.Any())
            {
                serviceData = serviceData.OrderBy(z => z.Key).ToList();
                serviceData.Insert(0, new KeyValueStringHelper { Key = "ALL", Value = allLangStringName });
            }
            return serviceData;
        }

        public async Task<List<KeyValueStringHelper>> GetSumCodeData(int tenantId, string selectedOrgId, string selectedServiceId, string finplanLevel2, int orgLevel, int budgetYear, List<tco_org_hierarchy> lstOrgHierarchy, string allLangStringName)
        {
            List<int> actionTypes = new List<int>();
            actionTypes.Add(100); actionTypes.Add(101); actionTypes.Add(2); actionTypes.Add(5); actionTypes.Add(6); actionTypes.Add(41); actionTypes.Add(9); actionTypes.Add(31); actionTypes.Add(30); actionTypes.Add(40); actionTypes.Add(7); actionTypes.Add(20); actionTypes.Add(21); actionTypes.Add(90); actionTypes.Add(61); actionTypes.Add(80);
            var level = GetOrgNameBasedOnLevel(orgLevel);
            List<AlterCodeSetupDataHelper> data = new List<AlterCodeSetupDataHelper>();
            if (string.IsNullOrEmpty(selectedServiceId))
            {
                var serviceData = await (from p in _tenantDBContext.tfp_trans_header
                                         join q in _tenantDBContext.tfp_trans_detail
                                         on new { a = p.fk_tenant_id, b = p.pk_action_id } equals new { a = q.fk_tenant_id, b = q.fk_action_id }
                                         join s in _tenantDBContext.tco_fp_alter_codes
                                         on new { a = q.fk_tenant_id, b = q.fk_alter_code } equals new { a = s.fk_tenant_id, b = s.pk_alter_code }
                                         where p.fk_tenant_id == tenantId && q.budget_year == budgetYear && actionTypes.Contains(p.action_type)
                                         select new
                                         {
                                             departmentCode = q.department_code,
                                             sumCode = s.sum_code,
                                             sumDescription = s.sum_description
                                         }).Distinct().ToListAsync();
                var serviceOrgData = (from a in serviceData
                                      join b in lstOrgHierarchy
                                      on a.departmentCode equals b.fk_department_code
                                      where b.fk_tenant_id == tenantId
                                      select new
                                      {
                                          sumCode = a.sumCode,
                                          sumDescription = a.sumDescription,
                                          orgId1 = b.org_id_1,
                                          orgId2 = b.org_id_2,
                                          orgId3 = b.org_id_3,
                                          orgId4 = b.org_id_4,
                                          orgId5 = b.org_id_5
                                      }).Distinct().ToList();
                data = (from a in serviceOrgData
                        select new AlterCodeSetupDataHelper
                        {
                            SumCode = a.sumCode,
                            SumDescription = a.sumDescription,
                            OrgId1 = a.orgId1,
                            OrgId2 = a.orgId2,
                            OrgId3 = a.orgId3,
                            OrgId4 = a.orgId4,
                            OrgId5 = a.orgId5
                        }).ToList();
            }
            else
            {
                var serviceData = await (from p in _tenantDBContext.tfp_trans_header
                                         join q in _tenantDBContext.tfp_trans_detail
                                         on new { a = p.fk_tenant_id, b = p.pk_action_id } equals new { a = q.fk_tenant_id, b = q.fk_action_id }
                                         join r in _tenantDBContext.tco_service_values
                                         on new { a = q.fk_tenant_id, b = q.function_code } equals new { a = r.fk_tenant_id, b = r.fk_function_code }
                                         join s in _tenantDBContext.tco_fp_alter_codes
                                         on new { a = r.fk_tenant_id, b = q.fk_alter_code } equals new { a = s.fk_tenant_id, b = s.pk_alter_code }
                                         where p.fk_tenant_id == tenantId && q.budget_year == budgetYear
                                         select new
                                         {
                                             departmentCode = q.department_code,
                                             serviceId1 = r.service_id_1,
                                             serviceId2 = r.service_id_2,
                                             serviceId3 = r.service_id_3,
                                             serviceId4 = r.service_id_4,
                                             serviceId5 = r.service_id_5,
                                             sumCode = s.sum_code,
                                             sumDescription = s.sum_description
                                         }).Distinct().ToListAsync();
                var serviceOrgData = (from a in serviceData
                                      join b in lstOrgHierarchy
                                      on a.departmentCode equals b.fk_department_code
                                      where b.fk_tenant_id == tenantId
                                      select new
                                      {
                                          serviceId1 = a.serviceId1,
                                          serviceId2 = a.serviceId2,
                                          serviceId3 = a.serviceId3,
                                          serviceId4 = a.serviceId4,
                                          serviceId5 = a.serviceId5,
                                          sumCode = a.sumCode,
                                          sumDescription = a.sumDescription,
                                          orgId1 = b.org_id_1,
                                          orgId2 = b.org_id_2,
                                          orgId3 = b.org_id_3,
                                          orgId4 = b.org_id_4,
                                          orgId5 = b.org_id_5
                                      }).Distinct().ToList();
                data = (from a in serviceOrgData
                        select new AlterCodeSetupDataHelper
                        {
                            ServiceId1 = a.serviceId1,
                            ServiceId2 = a.serviceId2,
                            ServiceId3 = a.serviceId3,
                            ServiceId4 = a.serviceId4,
                            ServiceId5 = a.serviceId5,
                            SumCode = a.sumCode,
                            SumDescription = a.sumDescription,
                            OrgId1 = a.orgId1,
                            OrgId2 = a.orgId2,
                            OrgId3 = a.orgId3,
                            OrgId4 = a.orgId4,
                            OrgId5 = a.orgId5
                        }).ToList();
            }
            var selectedOrgServiceData = selectedServiceId.ToUpper() == "ALL" ? data : GetSelectedOrgServiceData(selectedOrgId, selectedServiceId, level, finplanLevel2, data);
            var alterCodeData = (from a in selectedOrgServiceData
                                 group a by new { a.SumCode, a.SumDescription } into g
                                 select new
                                 {
                                     Key = g.Key.SumCode,
                                     Value = g.Key.SumDescription
                                 }).Distinct().ToList();
            var result = alterCodeData.Select(x => new KeyValueStringHelper { Key = x.Key, Value = x.Value }).ToList();
            result.Insert(0, new KeyValueStringHelper { Key = "ALL", Value = allLangStringName });
            return result;
        }

        public List<OrgSelectorHelper> GetOrgNameData(string finplanLevel1, string orgId, List<tco_org_hierarchy> data, bool isSecondLevel)
        {
            var orgData = new List<OrgSelectorHelper>();
            switch (finplanLevel1)
            {
                case "org_id_1":
                    var initialData1 = !isSecondLevel ? (from a in data
                                                         group a by new { a.org_id_1, a.org_name_1 } into g
                                                         orderby g.Key.org_id_1
                                                         select new
                                                         {
                                                             id = g.Key.org_id_1,
                                                             name = g.Key.org_name_1
                                                         }).Distinct().ToList()
                                                              : (from a in data
                                                                 group a by new { a.org_id_1, a.org_name_1 } into g
                                                                 orderby g.Key.org_id_1
                                                                 select new
                                                                 {
                                                                     id = g.Key.org_id_1,
                                                                     name = g.Key.org_name_1
                                                                 }).Distinct().ToList();
                    orgData = initialData1.Select(x => new OrgSelectorHelper
                    {
                        id = x.id,
                        name = x.id + " " + x.name,
                        levelNo = 1,
                        levelsInfo = new JArray() { new JObject() { new JProperty("levelNo1", x.id) } }
                    }).ToList();
                    break;

                case "org_id_2":
                    var initialData2 = !isSecondLevel ? (from a in data
                                                         group a by new { a.org_id_2, a.org_name_2 } into g
                                                         orderby g.Key.org_id_2
                                                         select new OrgSelectorHelper
                                                         {
                                                             id = g.Key.org_id_2,
                                                             name = g.Key.org_name_2
                                                         }).Distinct().ToList() : (from a in data
                                                                                   where a.org_id_1 == orgId
                                                                                   group a by new { a.org_id_2, a.org_name_2 } into g
                                                                                   orderby g.Key.org_id_2
                                                                                   select new OrgSelectorHelper
                                                                                   {
                                                                                       id = g.Key.org_id_2,
                                                                                       name = g.Key.org_name_2
                                                                                   }).Distinct().ToList();
                    orgData = initialData2.Select(x => new OrgSelectorHelper
                    {
                        id = x.id,
                        name = x.id + " " + x.name,
                        levelNo = 2,
                        levelsInfo = new JArray() { new JObject() { new JProperty("levelNo2", x.id), new JProperty("levelNo1", orgId) } }
                    }).ToList();
                    break;

                case "org_id_3":
                    var initialData3 = !isSecondLevel ? (from a in data
                                                         group a by new { a.org_id_3, a.org_name_3 } into g
                                                         orderby g.Key.org_id_3
                                                         select new OrgSelectorHelper
                                                         {
                                                             id = g.Key.org_id_3,
                                                             name = g.Key.org_name_3
                                                         }).Distinct().ToList() : (from a in data
                                                                                   where a.org_id_2 == orgId
                                                                                   group a by new { a.org_id_3, a.org_name_3 } into g
                                                                                   orderby g.Key.org_id_3
                                                                                   select new OrgSelectorHelper
                                                                                   {
                                                                                       id = g.Key.org_id_3,
                                                                                       name = g.Key.org_name_3
                                                                                   }).Distinct().ToList();
                    orgData = initialData3.Select(x => new OrgSelectorHelper
                    {
                        id = x.id,
                        name = x.id + " " + x.name,
                        levelNo = 3,
                        levelsInfo = new JArray() { new JObject() { new JProperty("levelNo3", x.id), new JProperty("levelNo2", orgId) } }
                    }).ToList();
                    break;

                case "org_id_4":
                    var initialData4 = !isSecondLevel ? (from a in data
                                                         group a by new { a.org_id_4, a.org_name_4 } into g
                                                         orderby g.Key.org_id_4
                                                         select new OrgSelectorHelper
                                                         {
                                                             id = g.Key.org_id_4,
                                                             name = g.Key.org_name_4
                                                         }).Distinct().ToList() : (from a in data
                                                                                   where a.org_id_3 == orgId
                                                                                   group a by new { a.org_id_4, a.org_name_4 } into g
                                                                                   orderby g.Key.org_id_4
                                                                                   select new OrgSelectorHelper
                                                                                   {
                                                                                       id = g.Key.org_id_4,
                                                                                       name = g.Key.org_name_4
                                                                                   }).Distinct().ToList();
                    orgData = initialData4.Select(x => new OrgSelectorHelper
                    {
                        id = x.id,
                        name = x.id + " " + x.name,
                        levelNo = 4,
                        levelsInfo = new JArray() { new JObject() { new JProperty("levelNo4", x.id), new JProperty("levelNo3", orgId) } }
                    }).ToList();
                    break;

                case "org_id_5":
                    var initialData5 = !isSecondLevel ? (from a in data
                                                         group a by new { a.org_id_5, a.org_name_5 } into g
                                                         orderby g.Key.org_id_5
                                                         select new OrgSelectorHelper
                                                         {
                                                             id = g.Key.org_id_5,
                                                             name = g.Key.org_name_5
                                                         }).Distinct().ToList() : (from a in data
                                                                                   where a.org_id_4 == orgId
                                                                                   group a by new { a.org_id_5, a.org_name_5 } into g
                                                                                   orderby g.Key.org_id_5
                                                                                   select new OrgSelectorHelper
                                                                                   {
                                                                                       id = g.Key.org_id_5,
                                                                                       name = g.Key.org_name_5
                                                                                   }).Distinct().ToList();
                    orgData = initialData5.Select(x => new OrgSelectorHelper
                    {
                        id = x.id,
                        name = x.id + " " + x.name,
                        levelNo = 5,
                        levelsInfo = new JArray() { new JObject() { new JProperty("levelNo5", x.id), new JProperty("levelNo4", orgId) } }
                    }).ToList();
                    break;
            }
            return orgData;
        }

        public List<KeyValueStringHelper> GetServiceNameData(string finplanLevel2, List<ServiceSetupDataHelper> data)
        {
            var serviceData = new List<KeyValueStringHelper>();
            switch (finplanLevel2)
            {
                case "service_id_1":
                    var initialData1 = (from a in data
                                        group a by new { a.ServiceId1, a.ServiceName1 } into g
                                        select new
                                        {
                                            Key = g.Key.ServiceId1,
                                            Value = g.Key.ServiceName1
                                        }).Distinct().ToList();
                    serviceData = initialData1.Select(x => new KeyValueStringHelper
                    {
                        Key = x.Key,
                        Value = x.Key + " " + x.Value
                    }).ToList();
                    break;

                case "service_id_2":
                    var initialData2 = (from a in data
                                        group a by new { a.ServiceId2, a.ServiceName2 } into g
                                        select new KeyValueStringHelper
                                        {
                                            Key = g.Key.ServiceId2,
                                            Value = g.Key.ServiceName2
                                        }).Distinct().ToList();
                    serviceData = initialData2.Select(x => new KeyValueStringHelper
                    {
                        Key = x.Key,
                        Value = x.Key + " " + x.Value
                    }).ToList();
                    break;

                case "service_id_3":
                    var initialData3 = (from a in data
                                        group a by new { a.ServiceId3, a.ServiceName3 } into g
                                        select new KeyValueStringHelper
                                        {
                                            Key = g.Key.ServiceId3,
                                            Value = g.Key.ServiceName3
                                        }).Distinct().ToList();
                    serviceData = initialData3.Select(x => new KeyValueStringHelper
                    {
                        Key = x.Key,
                        Value = x.Key + " " + x.Value
                    }).ToList();
                    break;

                case "service_id_4":
                    var initialData4 = (from a in data
                                        group a by new { a.ServiceId4, a.ServiceName4 } into g
                                        select new KeyValueStringHelper
                                        {
                                            Key = g.Key.ServiceId4,
                                            Value = g.Key.ServiceName4
                                        }).Distinct().ToList();
                    serviceData = initialData4.Select(x => new KeyValueStringHelper
                    {
                        Key = x.Key,
                        Value = x.Key + " " + x.Value
                    }).ToList();
                    break;

                case "service_id_5":
                    var initialData5 = (from a in data
                                        group a by new { a.ServiceId5, a.ServiceName5 } into g
                                        select new KeyValueStringHelper
                                        {
                                            Key = g.Key.ServiceId5,
                                            Value = g.Key.ServiceName5
                                        }).Distinct().ToList();
                    serviceData = initialData5.Select(x => new KeyValueStringHelper
                    {
                        Key = x.Key,
                        Value = x.Key + " " + x.Value
                    }).ToList();
                    break;
            }
            return serviceData;
        }

        public List<ServiceSetupDataHelper> GetSelectedOrgData(string selectedOrgId, string finplanLevel1, List<ServiceSetupDataHelper> data)
        {
            switch (finplanLevel1)
            {
                case "org_id_1":
                    data = data.Where(x => x.OrgId1 == selectedOrgId).Distinct().ToList();
                    break;

                case "org_id_2":
                    data = data.Where(x => x.OrgId2 == selectedOrgId).Distinct().ToList();
                    break;

                case "org_id_3":
                    data = data.Where(x => x.OrgId3 == selectedOrgId).Distinct().ToList();
                    break;

                case "org_id_4":
                    data = data.Where(x => x.OrgId4 == selectedOrgId).Distinct().ToList();
                    break;

                case "org_id_5":
                    data = data.Where(x => x.OrgId5 == selectedOrgId).Distinct().ToList();
                    break;
            }
            return data;
        }

        public List<AlterCodeSetupDataHelper> GetSelectedServiceData(string selectedServiceId, string finplanLevel2, List<AlterCodeSetupDataHelper> data)
        {
            switch (finplanLevel2)
            {
                case "service_id_1":
                    data = data.Where(x => x.ServiceId1 == selectedServiceId).Distinct().ToList();
                    break;

                case "service_id_2":
                    data = data.Where(x => x.ServiceId2 == selectedServiceId).Distinct().ToList();
                    break;

                case "service_id_3":
                    data = data.Where(x => x.ServiceId3 == selectedServiceId).Distinct().ToList();
                    break;

                case "service_id_4":
                    data = data.Where(x => x.ServiceId4 == selectedServiceId).Distinct().ToList();
                    break;

                case "service_id_5":
                    data = data.Where(x => x.ServiceId5 == selectedServiceId).Distinct().ToList();
                    break;
            }
            return data;
        }

        public async Task<List<ServiceGridDataResultHelper>> GetGridDetailData(int tenantId, string selectedOrgId,
            string selectedServiceId, string selectedSumCode, int budgetYear, string finplanLevel2, int orgLevel,
            int changeId, Dictionary<string, clsLanguageString> langStrings,
            Dictionary<string, clsLanguageString> langStringsBM, List<KeyValueData> lstActionTags,
            List<tco_org_hierarchy> lstOrgHierarchy, bool isToggle, bool isRelationalSetup = false,
            List<string> relationDepartments = null)
        {
            List<int> actionTypes = new List<int>();
            /*actionTypes.Add(100);*/
            actionTypes.Add(101); actionTypes.Add(2); actionTypes.Add(5); actionTypes.Add(6); actionTypes.Add(41); actionTypes.Add(9); actionTypes.Add(31); actionTypes.Add(30); actionTypes.Add(40); actionTypes.Add(7); actionTypes.Add(20); actionTypes.Add(21); actionTypes.Add(90); actionTypes.Add(61); actionTypes.Add(80);
            string selectedSumDescription = selectedSumCode.Split('-')[1];
            var level = GetOrgNameBasedOnLevel(orgLevel);
            var lstDepartments = isRelationalSetup && relationDepartments != null ? relationDepartments : GetListDepartmentsData(tenantId, budgetYear, selectedOrgId, level, lstOrgHierarchy);
            var lstFunctions = GetListFunctionsData(tenantId, budgetYear, selectedServiceId, finplanLevel2);
            //isRelationalSetup = true;
            _tenantDBContext.Database.SetCommandTimeout(900);

            var data = isRelationalSetup ? await (from q in _tenantDBContext.tfp_trans_detail
                                                  join p in _tenantDBContext.tfp_trans_header
                                                  on new { a = q.fk_tenant_id, b = q.fk_action_id } equals new { a = p.fk_tenant_id, b = p.pk_action_id }
                                                  join dep in _tenantDBContext.tco_departments
                                                on new { a = q.fk_tenant_id, b = q.department_code } equals new { a = dep.fk_tenant_id, b = dep.pk_department_code }
                                                  join oh in _tenantDBContext.tco_org_hierarchy
                                                  on new { a = dep.fk_tenant_id, b = dep.pk_department_code } equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                                                  join r in _tenantDBContext.tco_relation_values on new { a = dep.fk_tenant_id, b = "CHAPTER", c = "DEPARTMENTS" } equals new { a = r.fk_tenant_id, b = r.attribute_type, c = r.relation_type }
                                                  join s in _tenantDBContext.tco_attribute_values
                                                  on new { a = r.fk_tenant_id, b = r.attribute_value, c = r.attribute_type } equals new { a = s.fk_tenant_id, b = s.pk_attribute_id, c = s.attribute_type }
                                                  join al in _tenantDBContext.tco_fp_alter_codes
                                                  on new { a = q.fk_tenant_id, b = q.fk_alter_code } equals new { a = al.fk_tenant_id, b = al.pk_alter_code } into c
                                                  from d in c.DefaultIfEmpty()
                                                  where p.fk_tenant_id == tenantId && q.budget_year == budgetYear
                                                   && ((dep.pk_department_code == r.relation_value_from) || dep.pk_department_code.CompareTo(r.relation_value_from) > 0 && ((dep.pk_department_code == r.relation_value_to) || dep.pk_department_code.CompareTo(r.relation_value_to) < 0))
                                          && s.year_from <= budgetYear && budgetYear <= s.year_to
                                          && dep.year_from <= budgetYear && budgetYear <= dep.year_to && actionTypes.Contains(p.action_type)
                                                   // && oh.fk_org_version == orgVersion
                                                   && lstDepartments.Contains(dep.pk_department_code)
                                                  //&& lstFunctions.Contains(q.function_code)
                                                  group new { d, p, q } by new { d.sum_code, d.sum_description, d.limit_code, d.limit_description, d.cab_flag, q.fk_action_id, p.description, p.action_type, p.tags, q.fk_change_id } into grp
                                                  select new
                                                  {
                                                      //count = counter++,
                                                      sumCode = string.IsNullOrEmpty(grp.Key.sum_code) ? string.Empty : grp.Key.sum_code,
                                                      sumDescription = string.IsNullOrEmpty(grp.Key.sum_description) ? string.Empty : grp.Key.sum_description,
                                                      limitCode = string.IsNullOrEmpty(grp.Key.limit_code) ? string.Empty : grp.Key.limit_code,
                                                      limitDescription = string.IsNullOrEmpty(grp.Key.limit_description) ? string.Empty : grp.Key.limit_description,
                                                      actionId = grp.Key.fk_action_id,
                                                      actionName = grp.Key.description,
                                                      tags = grp.Key.tags,
                                                      action_type = grp.Key.action_type,
                                                      cab_flag = grp.Key.cab_flag.Equals(null) ? 0 : grp.Key.cab_flag,
                                                      change_id = grp.Key.fk_change_id,
                                                      year_1_amount = grp.Sum(z => z.q.year_1_amount),
                                                      year_2_amount = grp.Sum(z => z.q.year_2_amount),
                                                      year_3_amount = grp.Sum(z => z.q.year_3_amount),
                                                      year_4_amount = grp.Sum(z => z.q.year_4_amount),
                                                      year_5_amount = grp.Sum(z => z.q.year_5_amount),
                                                      year_6_amount = grp.Sum(z => z.q.year_6_amount),
                                                      year_7_amount = grp.Sum(z => z.q.year_7_amount),
                                                      year_8_amount = grp.Sum(z => z.q.year_8_amount),
                                                      year_9_amount = grp.Sum(z => z.q.year_9_amount),
                                                      year_10_amount = grp.Sum(z => z.q.year_10_amount)
                                                  }).ToListAsync()

                : string.IsNullOrEmpty(selectedServiceId) ? await (from p in _tenantDBContext.tfp_trans_header
                                                                   join q in _tenantDBContext.tfp_trans_detail
                                                                   on new { a = p.fk_tenant_id, b = p.pk_action_id } equals new { a = q.fk_tenant_id, b = q.fk_action_id }
                                                                   join r in _tenantDBContext.tco_fp_alter_codes
                                                                   on new { a = q.fk_tenant_id, b = q.fk_alter_code } equals new { a = r.fk_tenant_id, b = r.pk_alter_code } into c
                                                                   from d in c.DefaultIfEmpty()
                                                                   where p.fk_tenant_id == tenantId && q.budget_year == budgetYear && lstDepartments.Contains(q.department_code)
                                                                   && p.action_type >= 5 && actionTypes.Contains(p.action_type)
                                                                   group new { p, q, d } by new { d.sum_code, d.sum_description, d.limit_code, d.limit_description, d.cab_flag, p.pk_action_id, p.description, p.action_type, p.tags, q.fk_change_id } into g
                                                                   select new
                                                                   {
                                                                       sumCode = string.IsNullOrEmpty(g.Key.sum_code) ? string.Empty : g.Key.sum_code,
                                                                       sumDescription = string.IsNullOrEmpty(g.Key.sum_description) ? string.Empty : g.Key.sum_description,
                                                                       limitCode = string.IsNullOrEmpty(g.Key.limit_code) ? string.Empty : g.Key.limit_code,
                                                                       limitDescription = string.IsNullOrEmpty(g.Key.limit_description) ? string.Empty : g.Key.limit_description,
                                                                       actionId = g.Key.pk_action_id,
                                                                       actionName = g.Key.description,
                                                                       tags = g.Key.tags,
                                                                       action_type = g.Key.action_type,
                                                                       cab_flag = g.Key.cab_flag.Equals(null) ? 0 : g.Key.cab_flag,
                                                                       change_id = g.Key.fk_change_id,
                                                                       year_1_amount = g.Sum(z => z.q.year_1_amount),
                                                                       year_2_amount = g.Sum(z => z.q.year_2_amount),
                                                                       year_3_amount = g.Sum(z => z.q.year_3_amount),
                                                                       year_4_amount = g.Sum(z => z.q.year_4_amount),
                                                                       year_5_amount = g.Sum(z => z.q.year_5_amount),
                                                                       year_6_amount = g.Sum(z => z.q.year_6_amount),
                                                                       year_7_amount = g.Sum(z => z.q.year_7_amount),
                                                                       year_8_amount = g.Sum(z => z.q.year_8_amount),
                                                                       year_9_amount = g.Sum(z => z.q.year_9_amount),
                                                                       year_10_amount = g.Sum(z => z.q.year_10_amount)
                                                                   }).Distinct().ToListAsync() : await (from p in _tenantDBContext.tfp_trans_header
                                                                                                        join q in _tenantDBContext.tfp_trans_detail
                                                                                                        on new { a = p.fk_tenant_id, b = p.pk_action_id } equals new { a = q.fk_tenant_id, b = q.fk_action_id }
                                                                                                        join r in _tenantDBContext.tco_fp_alter_codes
                                                                                                        on new { a = q.fk_tenant_id, b = q.fk_alter_code } equals new { a = r.fk_tenant_id, b = r.pk_alter_code }
                                                                                                        where p.fk_tenant_id == tenantId && q.budget_year == budgetYear && lstDepartments.Contains(q.department_code) && lstFunctions.Contains(q.function_code) && actionTypes.Contains(p.action_type)
                                                                                                        group new { p, q, r } by new { r.sum_code, r.sum_description, r.limit_code, r.limit_description, r.cab_flag, p.pk_action_id, p.description, p.action_type, p.tags, q.fk_change_id } into g
                                                                                                        select new
                                                                                                        {
                                                                                                            sumCode = g.Key.sum_code,
                                                                                                            sumDescription = g.Key.sum_description,
                                                                                                            limitCode = g.Key.limit_code,
                                                                                                            limitDescription = g.Key.limit_description,
                                                                                                            actionId = g.Key.pk_action_id,
                                                                                                            actionName = g.Key.description,
                                                                                                            tags = g.Key.tags,
                                                                                                            action_type = g.Key.action_type,
                                                                                                            cab_flag = g.Key.cab_flag,
                                                                                                            change_id = g.Key.fk_change_id,
                                                                                                            year_1_amount = g.Sum(z => z.q.year_1_amount),
                                                                                                            year_2_amount = g.Sum(z => z.q.year_2_amount),
                                                                                                            year_3_amount = g.Sum(z => z.q.year_3_amount),
                                                                                                            year_4_amount = g.Sum(z => z.q.year_4_amount),
                                                                                                            year_5_amount = g.Sum(z => z.q.year_5_amount),
                                                                                                            year_6_amount = g.Sum(z => z.q.year_6_amount),
                                                                                                            year_7_amount = g.Sum(z => z.q.year_7_amount),
                                                                                                            year_8_amount = g.Sum(z => z.q.year_8_amount),
                                                                                                            year_9_amount = g.Sum(z => z.q.year_9_amount),
                                                                                                            year_10_amount = g.Sum(z => z.q.year_10_amount)
                                                                                                        }).Distinct().ToListAsync();

            var tableData = (from a in data
                             select new ServiceGridDataHelper
                             {
                                 SumCode = a.sumCode,
                                 SumDescription = a.sumDescription,
                                 LimitCode = a.limitCode,
                                 LimitDescription = a.limitDescription,
                                 ActionId = a.actionId,
                                 ActionName = a.actionName,
                                 ActionType = a.action_type,
                                 Tags = a.tags,
                                 CabFlag = a.cab_flag,
                                 ChangeId = a.change_id,
                                 Year1Amount = a.year_1_amount,
                                 Year2Amount = a.year_2_amount,
                                 Year3Amount = a.year_3_amount,
                                 Year4Amount = a.year_4_amount,
                                 Year5Amount = a.year_5_amount,
                                 Year6Amount = a.year_6_amount,
                                 Year7Amount = a.year_7_amount,
                                 Year8Amount = a.year_8_amount,
                                 Year9Amount = a.year_9_amount,
                                 Year10Amount = a.year_10_amount
                             }).Distinct().ToList();
            List<int> ids = tableData.Select(x => x.ActionId).Distinct().ToList();
            var bListData = (from a in _tenantDBContext.tfp_temp_header
                             join b in _tenantDBContext.tfp_temp_detail on new { a = a.fk_tenant_id, b = a.pk_temp_id }
                                                                   equals new { a = b.fk_tenant_id, b = b.fk_temp_id }
                             where a.fk_tenant_id == tenantId
                             && b.budget_year == budgetYear
                             && ids.Contains(a.fk_action_id) && !a.is_parked_action
                             select a.fk_action_id).ToList().Distinct().ToList();

            var deletedListData = (from a in _tenantDBContext.tfp_delete_header
                                   join b in _tenantDBContext.tfp_delete_detail on new { a = a.fk_tenant_id, b = a.pk_action_id }
                                                                           equals new { a = b.fk_tenant_id, b = b.fk_action_id }
                                   where a.fk_tenant_id == tenantId
                                   && b.budget_year == budgetYear
                                   && ids.Contains(a.fk_action_id)
                                   select a.fk_action_id).ToList().Distinct().ToList();

            tableData = tableData.Where(x => (!bListData.Contains(x.ActionId))).OrderBy(x => x.ActionType).ToList();
            tableData = tableData.Where(x => (!deletedListData.Contains(x.ActionId))).OrderBy(x => x.ActionType).ToList();
            var tableRowData = tableData.Where(x => x.ActionType != 20 && x.ActionType != 30 && x.ActionType != 40 && x.ActionType != 90).ToList();
            long lvl1RowId = 888888888;
            long lvl2RowId = 999999999;
            List<ServiceGridDataResultHelper> finalData = new List<ServiceGridDataResultHelper>();
            ServiceGridDataResultHelper level1RowData = new ServiceGridDataResultHelper();
            ServiceGridDataResultHelper level2RowData = new ServiceGridDataResultHelper();
            ServiceGridDataResultHelper level3RowData = new ServiceGridDataResultHelper();
            ServiceGridDataResultHelper nonAllocatedRowdata = new ServiceGridDataResultHelper();
            ServiceGridDataResultHelper finalSumData = new ServiceGridDataResultHelper();
            decimal totalRow1Sum = 0.0M; decimal totalRow2Sum = 0.0M; decimal totalRow3Sum = 0.0M; decimal totalRow4Sum = 0.0M; decimal totalRow5Sum = 0.0M; decimal totalRow6Sum = 0.0M; decimal totalRow7Sum = 0.0M; decimal totalRow8Sum = 0.0M; decimal totalRow9Sum = 0.0M; decimal totalRow10Sum = 0.0M;
            decimal totalChangeyear1 = 0.0M; decimal totalChangeyear2 = 0.0M; decimal totalChangeyear3 = 0.0M; decimal totalChangeyear4 = 0.0M; decimal totalChangeyear5 = 0.0M; decimal totalChangeyear6 = 0.0M; decimal totalChangeyear7 = 0.0M; decimal totalChangeyear8 = 0.0M; decimal totalChangeyear9 = 0.0M; decimal totalChangeyear10 = 0.0M;
            decimal nonAllocatedYear1 = 0.0M; decimal nonAllocatedYear2 = 0.0M; decimal nonAllocatedYear3 = 0.0M; decimal nonAllocatedYear4 = 0.0M; decimal nonAllocatedYear5 = 0.0M; decimal nonAllocatedYear6 = 0.0M; decimal nonAllocatedYear7 = 0.0M; decimal nonAllocatedYear8 = 0.0M; decimal nonAllocatedYear9 = 0.0M; decimal nonAllocatedYear10 = 0.0M;
            decimal nonAllocatedYearChange1 = 0.0M; decimal nonAllocatedYearChange2 = 0.0M; decimal nonAllocatedYearChange3 = 0.0M; decimal nonAllocatedYearChange4 = 0.0M; decimal nonAllocatedYearChange5 = 0.0M; decimal nonAllocatedYearChange6 = 0.0M; decimal nonAllocatedYearChange7 = 0.0M; decimal nonAllocatedYearChange8 = 0.0M; decimal nonAllocatedYearChange9 = 0.0M; decimal nonAllocatedYearChange10 = 0.0M;
            List<int> cabFlags = tableRowData.Select(x => x.CabFlag).OrderByDescending(y => y).Distinct().ToList();
            if (selectedSumDescription.ToLower() == "alle")
            {
                foreach (int cabFlag in cabFlags)
                {
                    List<string> sumDesc = tableRowData.Where(x => x.CabFlag == cabFlag).OrderBy(x => x.LimitCode).Select(x => x.SumDescription).Distinct().ToList();
                    foreach (var level1 in sumDesc)
                    {
                        List<ServiceGridDataHelper> level1Data = tableRowData.Where(x => x.CabFlag == cabFlag && x.SumDescription == level1).Distinct().ToList();
                        level1RowData = GetLevelRowData(level1Data, new ServiceGridDataHelper(), level1, lvl1RowId, null, false, changeId);
                        finalData.Add(level1RowData);
                        totalRow1Sum = totalRow1Sum + level1RowData.Year1Amount; totalRow2Sum = totalRow2Sum + level1RowData.Year2Amount; totalRow3Sum = totalRow3Sum + level1RowData.Year3Amount; totalRow4Sum = totalRow4Sum + level1RowData.Year4Amount; totalRow5Sum = totalRow5Sum + level1RowData.Year5Amount; totalRow6Sum = totalRow6Sum + level1RowData.Year6Amount; totalRow7Sum = totalRow7Sum + level1RowData.Year7Amount; totalRow8Sum = totalRow8Sum + level1RowData.Year8Amount; totalRow9Sum = totalRow9Sum + level1RowData.Year9Amount; totalRow10Sum = totalRow10Sum + level1RowData.Year10Amount;
                        totalChangeyear1 = totalChangeyear1 + level1RowData.changeyear1;
                        totalChangeyear2 = totalChangeyear2 + level1RowData.changeyear2;
                        totalChangeyear3 = totalChangeyear3 + level1RowData.changeyear3;
                        totalChangeyear4 = totalChangeyear4 + level1RowData.changeyear4;
                        totalChangeyear5 = totalChangeyear5 + level1RowData.changeyear5;
                        totalChangeyear6 = totalChangeyear6 + level1RowData.changeyear6;
                        totalChangeyear7 = totalChangeyear7 + level1RowData.changeyear7;
                        totalChangeyear8 = totalChangeyear8 + level1RowData.changeyear8;
                        totalChangeyear9 = totalChangeyear9 + level1RowData.changeyear9;
                        totalChangeyear10 = totalChangeyear10 + level1RowData.changeyear10;
                        List<string> limitDesc = tableRowData.Where(x => x.SumDescription == level1 && x.CabFlag == cabFlag).Select(x => x.LimitDescription).Distinct().ToList();
                        foreach (var level2 in limitDesc)
                        {
                            List<ServiceGridDataHelper> level2Data = tableRowData.Where(x => x.SumDescription == level1 && x.CabFlag == cabFlag && x.LimitDescription == level2).Distinct().ToList();
                            level2RowData = GetLevelRowData(level2Data, new ServiceGridDataHelper(), level2, lvl2RowId, lvl1RowId, false, changeId);
                            finalData.Add(level2RowData);
                            List<ServiceGridDataHelper> level3Data = tableRowData.Where(x => x.LimitDescription == level2 && x.CabFlag == cabFlag && x.SumDescription == level1).Distinct().ToList();
                            List<ServiceGridDataHelper> changeData = tableRowData.Where(x => x.LimitDescription == level2 && x.CabFlag == cabFlag && x.SumDescription == level1 && x.ChangeId == changeId).Distinct().ToList();
                            level3Data = (from a in level3Data
                                          group a by new { a.ActionId, a.ActionName, a.Tags, a.ActionType } into g
                                          select new ServiceGridDataHelper
                                          {
                                              ActionId = g.Key.ActionId,
                                              ActionName = g.Key.ActionName,
                                              ActionType = g.Key.ActionType,
                                              Tags = g.Key.Tags,
                                              Year1Amount = g.Sum(x => x.Year1Amount),
                                              Year2Amount = g.Sum(x => x.Year2Amount),
                                              Year3Amount = g.Sum(x => x.Year3Amount),
                                              Year4Amount = g.Sum(x => x.Year4Amount),
                                              Year5Amount = g.Sum(x => x.Year5Amount),
                                              Year6Amount = g.Sum(x => x.Year6Amount),
                                              Year7Amount = g.Sum(x => x.Year7Amount),
                                              Year8Amount = g.Sum(x => x.Year8Amount),
                                              Year9Amount = g.Sum(x => x.Year9Amount),
                                              Year10Amount = g.Sum(x => x.Year10Amount),
                                              changeyear1 = changeData.Where(x => x.ActionId == g.Key.ActionId).Sum(x => x.Year1Amount),
                                              changeyear2 = changeData.Where(x => x.ActionId == g.Key.ActionId).Sum(x => x.Year2Amount),
                                              changeyear3 = changeData.Where(x => x.ActionId == g.Key.ActionId).Sum(x => x.Year3Amount),
                                              changeyear4 = changeData.Where(x => x.ActionId == g.Key.ActionId).Sum(x => x.Year4Amount),
                                              changeyear5 = changeData.Where(x => x.ActionId == g.Key.ActionId).Sum(x => x.Year5Amount),
                                              changeyear6 = changeData.Where(x => x.ActionId == g.Key.ActionId).Sum(x => x.Year6Amount),
                                              changeyear7 = changeData.Where(x => x.ActionId == g.Key.ActionId).Sum(x => x.Year7Amount),
                                              changeyear8 = changeData.Where(x => x.ActionId == g.Key.ActionId).Sum(x => x.Year8Amount),
                                              changeyear9 = changeData.Where(x => x.ActionId == g.Key.ActionId).Sum(x => x.Year9Amount),
                                              changeyear10 = changeData.Where(x => x.ActionId == g.Key.ActionId).Sum(x => x.Year10Amount),
                                          }).ToList();
                            foreach (var level3 in level3Data)
                            {
                                if (isToggle)
                                {
                                    if (cabFlag == 0)
                                    {

                                        if ((level3.Year1Amount != 0) || (level3.Year2Amount != 0) || (level3.Year3Amount != 0) || (level3.Year4Amount != 0) || (level3.Year5Amount != 0) || (level3.Year6Amount != 0) || (level3.Year7Amount != 0) || (level3.Year8Amount != 0) || (level3.Year9Amount != 0) || (level3.Year10Amount != 0))
                                        //  if (Math.Round(sumAmount, 1) != 0)
                                        {
                                            level3RowData = GetLevelRowData(new List<ServiceGridDataHelper>(), level3, "", level3.ActionId, lvl2RowId, true, changeId);
                                        }
                                        else
                                        {
                                            level3RowData = new ServiceGridDataResultHelper();
                                        }
                                    }
                                    else
                                    {
                                        level3RowData = GetLevelRowData(new List<ServiceGridDataHelper>(), level3, "", level3.ActionId, lvl2RowId, true, changeId);
                                    }
                                }
                                else
                                {
                                    level3RowData = GetLevelRowData(new List<ServiceGridDataHelper>(), level3, "", level3.ActionId, lvl2RowId, true, changeId);
                                    string tag = level3Data.FirstOrDefault(x => x.ActionId == level3.ActionId).Tags;
                                    List<int> mappedTags = new List<int>();
                                    if (!string.IsNullOrEmpty(tag)) { mappedTags = tag.Split(',').Select(int.Parse).ToList(); }
                                    StringBuilder sbTags = new StringBuilder();
                                    if (mappedTags.Any())
                                    {
                                        foreach (var tags in mappedTags)
                                        {
                                            var firstOrDefault = lstActionTags.FirstOrDefault(x => x.KeyId == tags);
                                            if (firstOrDefault != null)
                                            {
                                                sbTags.Append("<span class='service-detail-tag'>" + firstOrDefault.ValueString + "</span>&nbsp;");
                                            }
                                        }
                                    }
                                    level3RowData.rowName = sbTags.Length > 0 ? level3RowData.rowName + "<br/>" + sbTags.ToString() : level3RowData.rowName;
                                }
                                if (level3RowData.id != 0)
                                {
                                    finalData.Add(level3RowData);
                                }
                            }
                            lvl2RowId++;
                        }
                        lvl1RowId++;
                    }
                }
                List<ServiceGridDataHelper> budgetPhaseData = new List<ServiceGridDataHelper>();
                budgetPhaseData = tableData.Where(x => x.ActionType == 20 || x.ActionType == 30 || x.ActionType == 40 || x.ActionType == 90).ToList();
                nonAllocatedRowdata.id = 0;
                nonAllocatedRowdata.parentId = null;
                nonAllocatedRowdata.rowName = langStringsBM.FirstOrDefault(x => x.Key.Equals("BM_req_cost_red_new_prts_text", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                nonAllocatedRowdata.clickable = false;
                nonAllocatedRowdata.actionType = 0;
                nonAllocatedRowdata.isAction = false;
                nonAllocatedRowdata.Year1Amount = nonAllocatedYear1 = nonAllocatedYear1 + budgetPhaseData.Sum(x => x.Year1Amount) / 1000;
                nonAllocatedRowdata.Year2Amount = nonAllocatedYear2 = nonAllocatedYear2 + budgetPhaseData.Sum(x => x.Year2Amount) / 1000;
                nonAllocatedRowdata.Year3Amount = nonAllocatedYear3 = nonAllocatedYear3 + budgetPhaseData.Sum(x => x.Year3Amount) / 1000;
                nonAllocatedRowdata.Year4Amount = nonAllocatedYear4 = nonAllocatedYear4 + budgetPhaseData.Sum(x => x.Year4Amount) / 1000;
                nonAllocatedRowdata.Year5Amount = nonAllocatedYear5 = nonAllocatedYear5 + budgetPhaseData.Sum(x => x.Year5Amount) / 1000;
                nonAllocatedRowdata.Year6Amount = nonAllocatedYear6 = nonAllocatedYear6 + budgetPhaseData.Sum(x => x.Year6Amount) / 1000;
                nonAllocatedRowdata.Year7Amount = nonAllocatedYear7 = nonAllocatedYear7 + budgetPhaseData.Sum(x => x.Year7Amount) / 1000;
                nonAllocatedRowdata.Year8Amount = nonAllocatedYear8 = nonAllocatedYear8 + budgetPhaseData.Sum(x => x.Year8Amount) / 1000;
                nonAllocatedRowdata.Year9Amount = nonAllocatedYear9 = nonAllocatedYear9 + budgetPhaseData.Sum(x => x.Year9Amount) / 1000;
                nonAllocatedRowdata.Year10Amount = nonAllocatedYear10 = nonAllocatedYear10 + budgetPhaseData.Sum(x => x.Year10Amount) / 1000;
                nonAllocatedRowdata.changeyear1 = nonAllocatedYearChange1 = nonAllocatedYearChange1 + tableData.Where(x => x.ChangeId == changeId && (x.ActionType == 20 || x.ActionType == 30 || x.ActionType == 40 || x.ActionType == 90)).ToList().Sum(x => x.Year1Amount) / 1000;
                nonAllocatedRowdata.changeyear2 = nonAllocatedYearChange2 = nonAllocatedYearChange2 + tableData.Where(x => x.ChangeId == changeId && (x.ActionType == 20 || x.ActionType == 30 || x.ActionType == 40 || x.ActionType == 90)).ToList().Sum(x => x.Year2Amount) / 1000;
                nonAllocatedRowdata.changeyear3 = nonAllocatedYearChange3 = nonAllocatedYearChange3 + tableData.Where(x => x.ChangeId == changeId && (x.ActionType == 20 || x.ActionType == 30 || x.ActionType == 40 || x.ActionType == 90)).ToList().Sum(x => x.Year3Amount) / 1000;
                nonAllocatedRowdata.changeyear4 = nonAllocatedYearChange4 = nonAllocatedYearChange4 + tableData.Where(x => x.ChangeId == changeId && (x.ActionType == 20 || x.ActionType == 30 || x.ActionType == 40 || x.ActionType == 90)).ToList().Sum(x => x.Year4Amount) / 1000;
                nonAllocatedRowdata.changeyear5 = nonAllocatedYearChange5 = nonAllocatedYearChange5 + tableData.Where(x => x.ChangeId == changeId && (x.ActionType == 20 || x.ActionType == 30 || x.ActionType == 40 || x.ActionType == 90)).ToList().Sum(x => x.Year5Amount) / 1000;
                nonAllocatedRowdata.changeyear6 = nonAllocatedYearChange6 = nonAllocatedYearChange6 + tableData.Where(x => x.ChangeId == changeId && (x.ActionType == 20 || x.ActionType == 30 || x.ActionType == 40 || x.ActionType == 90)).ToList().Sum(x => x.Year6Amount) / 1000;
                nonAllocatedRowdata.changeyear7 = nonAllocatedYearChange7 = nonAllocatedYearChange7 + tableData.Where(x => x.ChangeId == changeId && (x.ActionType == 20 || x.ActionType == 30 || x.ActionType == 40 || x.ActionType == 90)).ToList().Sum(x => x.Year7Amount) / 1000;
                nonAllocatedRowdata.changeyear8 = nonAllocatedYearChange8 = nonAllocatedYearChange8 + tableData.Where(x => x.ChangeId == changeId && (x.ActionType == 20 || x.ActionType == 30 || x.ActionType == 40 || x.ActionType == 90)).ToList().Sum(x => x.Year8Amount) / 1000;
                nonAllocatedRowdata.changeyear9 = nonAllocatedYearChange9 = nonAllocatedYearChange9 + tableData.Where(x => x.ChangeId == changeId && (x.ActionType == 20 || x.ActionType == 30 || x.ActionType == 40 || x.ActionType == 90)).ToList().Sum(x => x.Year9Amount) / 1000;
                nonAllocatedRowdata.changeyear10 = nonAllocatedYearChange10 = nonAllocatedYearChange10 + tableData.Where(x => x.ChangeId == changeId && (x.ActionType == 20 || x.ActionType == 30 || x.ActionType == 40 || x.ActionType == 90)).ToList().Sum(x => x.Year10Amount) / 1000;

                finalData.Add(nonAllocatedRowdata);

                finalSumData.id = -1;
                finalSumData.parentId = null;
                finalSumData.rowName = langStrings.FirstOrDefault(x => x.Key.Equals("FP_service_grid_total_sum_row", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                finalSumData.clickable = false;
                finalSumData.actionType = 0;
                finalSumData.isAction = false;
                finalSumData.Year1Amount = totalRow1Sum + nonAllocatedYear1;
                finalSumData.Year2Amount = totalRow2Sum + nonAllocatedYear2;
                finalSumData.Year3Amount = totalRow3Sum + nonAllocatedYear3;
                finalSumData.Year4Amount = totalRow4Sum + nonAllocatedYear4;
                finalSumData.Year5Amount = totalRow5Sum + nonAllocatedYear5;
                finalSumData.Year6Amount = totalRow6Sum + nonAllocatedYear6;
                finalSumData.Year7Amount = totalRow7Sum + nonAllocatedYear7;
                finalSumData.Year8Amount = totalRow8Sum + nonAllocatedYear8;
                finalSumData.Year9Amount = totalRow9Sum + nonAllocatedYear9;
                finalSumData.Year10Amount = totalRow10Sum + nonAllocatedYear10;
                finalSumData.changeyear1 = totalChangeyear1 + nonAllocatedYearChange1;
                finalSumData.changeyear2 = totalChangeyear2 + nonAllocatedYearChange2;
                finalSumData.changeyear3 = totalChangeyear3 + nonAllocatedYearChange3;
                finalSumData.changeyear4 = totalChangeyear4 + nonAllocatedYearChange4;
                finalSumData.changeyear5 = totalChangeyear5 + nonAllocatedYearChange5;
                finalSumData.changeyear6 = totalChangeyear6 + nonAllocatedYearChange6;
                finalSumData.changeyear7 = totalChangeyear7 + nonAllocatedYearChange7;
                finalSumData.changeyear8 = totalChangeyear8 + nonAllocatedYearChange8;
                finalSumData.changeyear9 = totalChangeyear9 + nonAllocatedYearChange9;
                finalSumData.changeyear10 = totalChangeyear10 + nonAllocatedYearChange10;
                finalData.Add(finalSumData);
            }
            else
            {
                foreach (int cabFlag in cabFlags)
                {
                    List<string> limitDesc = tableRowData.Where(x => x.SumDescription == selectedSumDescription && x.CabFlag == cabFlag).Select(x => x.LimitDescription).Distinct().ToList();
                    foreach (var level2 in limitDesc)
                    {
                        List<ServiceGridDataHelper> level2Data = tableRowData.Where(x => x.SumDescription == selectedSumDescription && x.CabFlag == cabFlag && x.LimitDescription == level2).Distinct().ToList();
                        level2RowData = GetLevelRowData(level2Data, new ServiceGridDataHelper(), level2, lvl2RowId, null, false, changeId);
                        finalData.Add(level2RowData);
                        totalRow1Sum = totalRow1Sum + level2RowData.Year1Amount; totalRow2Sum = totalRow2Sum + level2RowData.Year2Amount; totalRow3Sum = totalRow3Sum + level2RowData.Year3Amount; totalRow4Sum = totalRow4Sum + level2RowData.Year4Amount; totalRow5Sum = totalRow5Sum + level2RowData.Year5Amount; totalRow6Sum = totalRow6Sum + level2RowData.Year6Amount; totalRow7Sum = totalRow7Sum + level2RowData.Year7Amount; totalRow8Sum = totalRow8Sum + level2RowData.Year8Amount; totalRow9Sum = totalRow9Sum + level2RowData.Year9Amount; totalRow10Sum = totalRow10Sum + level2RowData.Year10Amount;
                        totalChangeyear1 = totalChangeyear1 + level2RowData.changeyear1;
                        totalChangeyear2 = totalChangeyear2 + level2RowData.changeyear2;
                        totalChangeyear3 = totalChangeyear3 + level2RowData.changeyear3;
                        totalChangeyear4 = totalChangeyear4 + level2RowData.changeyear4;
                        totalChangeyear5 = totalChangeyear5 + level2RowData.changeyear5;
                        totalChangeyear6 = totalChangeyear6 + level2RowData.changeyear6;
                        totalChangeyear7 = totalChangeyear7 + level2RowData.changeyear7;
                        totalChangeyear8 = totalChangeyear8 + level2RowData.changeyear8;
                        totalChangeyear9 = totalChangeyear9 + level2RowData.changeyear9;
                        totalChangeyear10 = totalChangeyear10 + level2RowData.changeyear10;
                        List<ServiceGridDataHelper> level3Data = tableRowData.Where(x => x.LimitDescription == level2 && x.CabFlag == cabFlag && x.SumDescription == selectedSumDescription).Distinct().ToList();
                        level3Data = (from a in level3Data
                                      group a by new { a.ActionId, a.ActionName, a.Tags,a.ActionType } into g
                                      select new ServiceGridDataHelper
                                      {
                                          ActionId = g.Key.ActionId,
                                          ActionName = g.Key.ActionName,
                                          ActionType = g.Key.ActionType,
                                          Tags = g.Key.Tags,
                                          Year1Amount = g.Sum(x => x.Year1Amount),
                                          Year2Amount = g.Sum(x => x.Year2Amount),
                                          Year3Amount = g.Sum(x => x.Year3Amount),
                                          Year4Amount = g.Sum(x => x.Year4Amount),
                                          Year5Amount = g.Sum(x => x.Year5Amount),
                                          Year6Amount = g.Sum(x => x.Year6Amount),
                                          Year7Amount = g.Sum(x => x.Year7Amount),
                                          Year8Amount = g.Sum(x => x.Year8Amount),
                                          Year9Amount = g.Sum(x => x.Year9Amount),
                                          Year10Amount = g.Sum(x => x.Year10Amount)
                                      }).ToList();
                        foreach (var level3 in level3Data)
                        {
                            if (isToggle)
                            {
                                if (cabFlag == 0)
                                {
                                    if (((int)level3.Year1Amount != 0) || ((int)level3.Year2Amount != 0) || ((int)level3.Year3Amount != 0) || ((int)level3.Year4Amount != 0) || ((int)level3.Year5Amount != 0) || ((int)level3.Year6Amount != 0) || ((int)level3.Year7Amount != 0) || ((int)level3.Year8Amount != 0) || ((int)level3.Year9Amount != 0) || ((int)level3.Year10Amount != 0))
                                    {
                                        level3RowData = GetLevelRowData(new List<ServiceGridDataHelper>(), level3, "", level3.ActionId, lvl2RowId, true, changeId);
                                    }
                                    else
                                    {
                                        level3RowData = new ServiceGridDataResultHelper();
                                    }
                                }
                                else
                                {
                                    level3RowData = GetLevelRowData(new List<ServiceGridDataHelper>(), level3, "", level3.ActionId, lvl2RowId, true, changeId);
                                }
                            }
                            else
                            {
                                level3RowData = GetLevelRowData(new List<ServiceGridDataHelper>(), level3, "", level3.ActionId, lvl2RowId, true, changeId);
                                string tag = level3Data.FirstOrDefault(x => x.ActionId == level3.ActionId).Tags;
                                List<int> mappedTags = new List<int>();
                                if (!string.IsNullOrEmpty(tag)) { mappedTags = tag.Split(',').Select(int.Parse).ToList(); }
                                StringBuilder sbTags = new StringBuilder();
                                if (mappedTags.Any())
                                {
                                    foreach (var tags in mappedTags)
                                    {
                                        var firstOrDefault = lstActionTags.FirstOrDefault(x => x.KeyId == tags);
                                        if (firstOrDefault != null)
                                        {
                                            sbTags.Append("<span class='service-detail-tag'>" + firstOrDefault.ValueString + "</span>&nbsp;");
                                        }
                                    }
                                }
                                level3RowData.rowName = sbTags.Length > 0 ? level3RowData.rowName + "<br/>" + sbTags.ToString() : level3RowData.rowName;
                            }
                            if (level3RowData.id != 0)
                            {
                                finalData.Add(level3RowData);
                            }
                        }
                        lvl2RowId++;
                    }
                }
                List<ServiceGridDataHelper> budgetPhaseData = new List<ServiceGridDataHelper>();
                budgetPhaseData = tableData.Where(x => x.ActionType == 20 || x.ActionType == 30 || x.ActionType == 40 || x.ActionType == 90).ToList();
                nonAllocatedRowdata.id = 0;
                nonAllocatedRowdata.parentId = null;
                nonAllocatedRowdata.rowName = langStringsBM.FirstOrDefault(x => x.Key.Equals("BM_req_cost_red_new_prts_text", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                nonAllocatedRowdata.clickable = false;
                nonAllocatedRowdata.actionType = 0;
                nonAllocatedRowdata.isAction = false;
                nonAllocatedRowdata.Year1Amount = nonAllocatedYear1 = nonAllocatedYear1 + budgetPhaseData.Sum(x => x.Year1Amount) / 1000;
                nonAllocatedRowdata.Year2Amount = nonAllocatedYear2 = nonAllocatedYear2 + budgetPhaseData.Sum(x => x.Year2Amount) / 1000;
                nonAllocatedRowdata.Year3Amount = nonAllocatedYear3 = nonAllocatedYear3 + budgetPhaseData.Sum(x => x.Year3Amount) / 1000;
                nonAllocatedRowdata.Year4Amount = nonAllocatedYear4 = nonAllocatedYear4 + budgetPhaseData.Sum(x => x.Year4Amount) / 1000;
                nonAllocatedRowdata.Year5Amount = nonAllocatedYear5 = nonAllocatedYear5 + budgetPhaseData.Sum(x => x.Year5Amount) / 1000;
                nonAllocatedRowdata.Year6Amount = nonAllocatedYear6 = nonAllocatedYear6 + budgetPhaseData.Sum(x => x.Year6Amount) / 1000;
                nonAllocatedRowdata.Year7Amount = nonAllocatedYear7 = nonAllocatedYear7 + budgetPhaseData.Sum(x => x.Year7Amount) / 1000;
                nonAllocatedRowdata.Year8Amount = nonAllocatedYear8 = nonAllocatedYear8 + budgetPhaseData.Sum(x => x.Year8Amount) / 1000;
                nonAllocatedRowdata.Year9Amount = nonAllocatedYear9 = nonAllocatedYear9 + budgetPhaseData.Sum(x => x.Year9Amount) / 1000;
                nonAllocatedRowdata.Year10Amount = nonAllocatedYear10 = nonAllocatedYear10 + budgetPhaseData.Sum(x => x.Year10Amount) / 1000;
                nonAllocatedRowdata.changeyear1 = nonAllocatedYearChange1 = nonAllocatedYearChange1 + tableData.Where(x => x.ChangeId == changeId && (x.ActionType == 20 || x.ActionType == 30 || x.ActionType == 40 || x.ActionType == 90)).ToList().Sum(x => x.Year1Amount) / 1000;
                nonAllocatedRowdata.changeyear2 = nonAllocatedYearChange2 = nonAllocatedYearChange2 + tableData.Where(x => x.ChangeId == changeId && (x.ActionType == 20 || x.ActionType == 30 || x.ActionType == 40 || x.ActionType == 90)).ToList().Sum(x => x.Year2Amount) / 1000;
                nonAllocatedRowdata.changeyear3 = nonAllocatedYearChange3 = nonAllocatedYearChange3 + tableData.Where(x => x.ChangeId == changeId && (x.ActionType == 20 || x.ActionType == 30 || x.ActionType == 40 || x.ActionType == 90)).ToList().Sum(x => x.Year3Amount) / 1000;
                nonAllocatedRowdata.changeyear4 = nonAllocatedYearChange4 = nonAllocatedYearChange4 + tableData.Where(x => x.ChangeId == changeId && (x.ActionType == 20 || x.ActionType == 30 || x.ActionType == 40 || x.ActionType == 90)).ToList().Sum(x => x.Year4Amount) / 1000;
                nonAllocatedRowdata.changeyear5 = nonAllocatedYearChange5 = nonAllocatedYearChange5 + tableData.Where(x => x.ChangeId == changeId && (x.ActionType == 20 || x.ActionType == 30 || x.ActionType == 40 || x.ActionType == 90)).ToList().Sum(x => x.Year5Amount) / 1000;
                nonAllocatedRowdata.changeyear6 = nonAllocatedYearChange6 = nonAllocatedYearChange6 + tableData.Where(x => x.ChangeId == changeId && (x.ActionType == 20 || x.ActionType == 30 || x.ActionType == 40 || x.ActionType == 90)).ToList().Sum(x => x.Year6Amount) / 1000;
                nonAllocatedRowdata.changeyear7 = nonAllocatedYearChange7 = nonAllocatedYearChange7 + tableData.Where(x => x.ChangeId == changeId && (x.ActionType == 20 || x.ActionType == 30 || x.ActionType == 40 || x.ActionType == 90)).ToList().Sum(x => x.Year7Amount) / 1000;
                nonAllocatedRowdata.changeyear8 = nonAllocatedYearChange8 = nonAllocatedYearChange8 + tableData.Where(x => x.ChangeId == changeId && (x.ActionType == 20 || x.ActionType == 30 || x.ActionType == 40 || x.ActionType == 90)).ToList().Sum(x => x.Year8Amount) / 1000;
                nonAllocatedRowdata.changeyear9 = nonAllocatedYearChange9 = nonAllocatedYearChange9 + tableData.Where(x => x.ChangeId == changeId && (x.ActionType == 20 || x.ActionType == 30 || x.ActionType == 40 || x.ActionType == 90)).ToList().Sum(x => x.Year9Amount) / 1000;
                nonAllocatedRowdata.changeyear10 = nonAllocatedYearChange10 = nonAllocatedYearChange10 + tableData.Where(x => x.ChangeId == changeId && (x.ActionType == 20 || x.ActionType == 30 || x.ActionType == 40 || x.ActionType == 90)).ToList().Sum(x => x.Year10Amount) / 1000;

                finalData.Add(nonAllocatedRowdata);

                finalSumData.id = -1;
                finalSumData.parentId = null;
                finalSumData.rowName = langStrings.FirstOrDefault(x => x.Key.Equals("FP_service_grid_total_sum_row", StringComparison.InvariantCultureIgnoreCase)).Value.LangText;
                finalSumData.clickable = false;
                finalSumData.actionType = 0;
                finalSumData.isAction = false;
                finalSumData.Year1Amount = totalRow1Sum + nonAllocatedYear1;
                finalSumData.Year2Amount = totalRow2Sum + nonAllocatedYear2;
                finalSumData.Year3Amount = totalRow3Sum + nonAllocatedYear3;
                finalSumData.Year4Amount = totalRow4Sum + nonAllocatedYear4;
                finalSumData.Year5Amount = totalRow5Sum + nonAllocatedYear5;
                finalSumData.Year6Amount = totalRow6Sum + nonAllocatedYear6;
                finalSumData.Year7Amount = totalRow7Sum + nonAllocatedYear7;
                finalSumData.Year8Amount = totalRow8Sum + nonAllocatedYear8;
                finalSumData.Year9Amount = totalRow9Sum + nonAllocatedYear9;
                finalSumData.Year10Amount = totalRow10Sum + nonAllocatedYear10;
                finalSumData.changeyear1 = totalChangeyear1 + nonAllocatedYearChange1;
                finalSumData.changeyear2 = totalChangeyear2 + nonAllocatedYearChange2;
                finalSumData.changeyear3 = totalChangeyear3 + nonAllocatedYearChange3;
                finalSumData.changeyear4 = totalChangeyear4 + nonAllocatedYearChange4;
                finalSumData.changeyear5 = totalChangeyear5 + nonAllocatedYearChange5;
                finalSumData.changeyear6 = totalChangeyear6 + nonAllocatedYearChange6;
                finalSumData.changeyear7 = totalChangeyear7 + nonAllocatedYearChange7;
                finalSumData.changeyear8 = totalChangeyear8 + nonAllocatedYearChange8;
                finalSumData.changeyear9 = totalChangeyear9 + nonAllocatedYearChange9;
                finalSumData.changeyear10 = totalChangeyear10 + nonAllocatedYearChange10;
                finalData.Add(finalSumData);
            }
            return finalData;
        }

        public List<string> GetListDepartmentsData(int tenantId, int budgetYear, string orgId, string level, List<tco_org_hierarchy> lstOrgHierarchy)
        {
            var data = (from a in lstOrgHierarchy
                        where a.fk_tenant_id == tenantId
                        select new
                        {
                            orgId1 = a.org_id_1,
                            orgId2 = a.org_id_2,
                            orgId3 = a.org_id_3,
                            orgId4 = a.org_id_4,
                            orgId5 = a.org_id_5,
                            departmentCode = a.fk_department_code
                        }).Distinct().ToList();
            var finalData = (from a in data
                             select new ServiceSetupDataHelper
                             {
                                 OrgId1 = a.orgId1,
                                 OrgId2 = a.orgId2,
                                 OrgId3 = a.orgId3,
                                 OrgId4 = a.orgId4,
                                 OrgId5 = a.orgId5,
                                 DepartmentCode = a.departmentCode
                             }).ToList();
            var orgData = GetSelectedOrgData(orgId, level, finalData);
            var departmentData = orgData.Select(x => x.DepartmentCode).ToList();
            return departmentData;
        }

        public List<string> GetListFunctionsData(int tenantId, int budgetYear, string serviceId, string level)
        {
            var data = (from a in _tenantDBContext.tco_service_values
                        where a.fk_tenant_id == tenantId
                        select new
                        {
                            serviceId1 = a.service_id_1,
                            serviceId2 = a.service_id_2,
                            serviceId3 = a.service_id_3,
                            serviceId4 = a.service_id_4,
                            serviceId5 = a.service_id_5,
                            functionCode = a.fk_function_code
                        }).Distinct().ToList();
            var finalData = (from a in data
                             select new AlterCodeSetupDataHelper
                             {
                                 ServiceId1 = a.serviceId1,
                                 ServiceId2 = a.serviceId2,
                                 ServiceId3 = a.serviceId3,
                                 ServiceId4 = a.serviceId4,
                                 ServiceId5 = a.serviceId5,
                                 FunctionCode = a.functionCode
                             }).ToList();
            var serviceData = serviceId.ToUpper() == "ALL" ? finalData : GetSelectedServiceData(serviceId, level, finalData);
            var functionData = serviceData.Select(x => x.FunctionCode).ToList();
            return functionData;
        }

        public ServiceGridDataResultHelper GetLevelRowData(List<ServiceGridDataHelper> levelData, ServiceGridDataHelper actionLevel, string level, long id, long? parentId, bool isAction, int changeId)
        {
            var data = new ServiceGridDataResultHelper();
            if (!isAction)
            {
                data.id = id;
                data.parentId = parentId;
                data.rowName = level;
                data.clickable = false;
                data.actionType = 0;
                data.isAction = isAction;
                data.Year1Amount = levelData.Sum(x => x.Year1Amount / 1000);
                data.Year2Amount = levelData.Sum(x => x.Year2Amount / 1000);
                data.Year3Amount = levelData.Sum(x => x.Year3Amount / 1000);
                data.Year4Amount = levelData.Sum(x => x.Year4Amount / 1000);
                data.Year5Amount = levelData.Sum(x => x.Year5Amount / 1000);
                data.Year6Amount = levelData.Sum(x => x.Year6Amount / 1000);
                data.Year7Amount = levelData.Sum(x => x.Year7Amount / 1000);
                data.Year8Amount = levelData.Sum(x => x.Year8Amount / 1000);
                data.Year9Amount = levelData.Sum(x => x.Year9Amount / 1000);
                data.Year10Amount = levelData.Sum(x => x.Year10Amount / 1000);
                data.changeyear1 = levelData.Where(x => x.ChangeId == changeId).Sum(x => x.Year1Amount / 1000);
                data.changeyear2 = levelData.Where(x => x.ChangeId == changeId).Sum(x => x.Year2Amount / 1000);
                data.changeyear3 = levelData.Where(x => x.ChangeId == changeId).Sum(x => x.Year3Amount / 1000);
                data.changeyear4 = levelData.Where(x => x.ChangeId == changeId).Sum(x => x.Year4Amount / 1000);
                data.changeyear5 = levelData.Where(x => x.ChangeId == changeId).Sum(x => x.Year5Amount / 1000);
                data.changeyear6 = levelData.Where(x => x.ChangeId == changeId).Sum(x => x.Year6Amount / 1000);
                data.changeyear7 = levelData.Where(x => x.ChangeId == changeId).Sum(x => x.Year7Amount / 1000);
                data.changeyear8 = levelData.Where(x => x.ChangeId == changeId).Sum(x => x.Year8Amount / 1000);
                data.changeyear9 = levelData.Where(x => x.ChangeId == changeId).Sum(x => x.Year9Amount / 1000);
                data.changeyear10 = levelData.Where(x => x.ChangeId == changeId).Sum(x => x.Year10Amount / 1000);
            }
            else
            {
                data.id = id;
                data.parentId = parentId;
                data.rowName = actionLevel.ActionName;
                data.clickable = false;
                data.actionType = actionLevel.ActionType;
                data.isAction = isAction;
                data.Year1Amount = actionLevel.Year1Amount / 1000;
                data.Year2Amount = actionLevel.Year2Amount / 1000;
                data.Year3Amount = actionLevel.Year3Amount / 1000;
                data.Year4Amount = actionLevel.Year4Amount / 1000;
                data.Year5Amount = actionLevel.Year5Amount / 1000;
                data.Year6Amount = actionLevel.Year6Amount / 1000;
                data.Year7Amount = actionLevel.Year7Amount / 1000;
                data.Year8Amount = actionLevel.Year8Amount / 1000;
                data.Year9Amount = actionLevel.Year9Amount / 1000;
                data.Year10Amount = actionLevel.Year10Amount / 1000;
                data.changeyear1 = actionLevel.changeyear1 / 1000;
                data.changeyear2 = actionLevel.changeyear2 / 1000;
                data.changeyear3 = actionLevel.changeyear3 / 1000;
                data.changeyear4 = actionLevel.changeyear4 / 1000;
                data.changeyear5 = actionLevel.changeyear5 / 1000;
                data.changeyear6 = actionLevel.changeyear6 / 1000;
                data.changeyear7 = actionLevel.changeyear7 / 1000;
                data.changeyear8 = actionLevel.changeyear8 / 1000;
                data.changeyear9 = actionLevel.changeyear9 / 1000;
                data.changeyear10 = actionLevel.changeyear10 / 1000;
            }
            return data;
        }

        public string GetOrgNameBasedOnLevel(int orgLevel)
        {
            var orgLevelName = "";
            switch (orgLevel)
            {
                case 1:
                    orgLevelName = "org_id_1";
                    break;

                case 2:
                    orgLevelName = "org_id_2";
                    break;

                case 3:
                    orgLevelName = "org_id_3";
                    break;

                case 4:
                    orgLevelName = "org_id_4";
                    break;

                case 5:
                    orgLevelName = "org_id_5";
                    break;
            }
            return orgLevelName;
        }

        public List<AlterCodeSetupDataHelper> GetSelectedOrgServiceData(string selectedOrgId, string selectedServiceId, string finplanLevel1, string finplanLevel2, List<AlterCodeSetupDataHelper> data)
        {
            switch (finplanLevel1)
            {
                case "org_id_1":
                    data = data.Where(x => x.OrgId1 == selectedOrgId).Distinct().ToList();
                    break;

                case "org_id_2":
                    data = data.Where(x => x.OrgId2 == selectedOrgId).Distinct().ToList();
                    break;

                case "org_id_3":
                    data = data.Where(x => x.OrgId3 == selectedOrgId).Distinct().ToList();
                    break;

                case "org_id_4":
                    data = data.Where(x => x.OrgId4 == selectedOrgId).Distinct().ToList();
                    break;

                case "org_id_5":
                    data = data.Where(x => x.OrgId5 == selectedOrgId).Distinct().ToList();
                    break;
            }
            switch (finplanLevel2)
            {
                case "service_id_1":
                    data = data.Where(x => x.ServiceId1 == selectedServiceId).Distinct().ToList();
                    break;

                case "service_id_2":
                    data = data.Where(x => x.ServiceId2 == selectedServiceId).Distinct().ToList();
                    break;

                case "service_id_3":
                    data = data.Where(x => x.ServiceId3 == selectedServiceId).Distinct().ToList();
                    break;

                case "service_id_4":
                    data = data.Where(x => x.ServiceId4 == selectedServiceId).Distinct().ToList();
                    break;

                case "service_id_5":
                    data = data.Where(x => x.ServiceId5 == selectedServiceId).Distinct().ToList();
                    break;
            }
            return data;
        }

        public async Task<List<string>> GetNotIncludedAccounts(int tenantId, int budgetYear)
        {
            var paramVal = await (from a in _tenantDBContext.tco_newyear_log
                                  where a.fk_tenant_id == tenantId && a.budget_year == budgetYear && a.operation_name == "FinBudgetFrom"
                                  select a).ToListAsync();
            var dataSet = new List<string>();

            if (paramVal.Any())
            {
                dataSet = await (from a in _tenantDBContext.tfp_trans_detail
                                 join b in _tenantDBContext.tco_accounts.Where(x => x.pk_tenant_id == tenantId && x.dateFrom.Year <= budgetYear && x.dateTo.Year >= budgetYear)
                                                                     on new { x = a.fk_account_code, y = a.fk_tenant_id }
                                                                     equals new { x = b.pk_account_code, y = b.pk_tenant_id } into b1
                                 from b2 in b1.DefaultIfEmpty()
                                 join c in _tenantDBContext.gco_kostra_accounts on new { x = b2.fk_kostra_account_code, y = "operations" }
                                                                     equals new { x = c.pk_kostra_account_code, y = c.type } into c1
                                 from c2 in c1.DefaultIfEmpty()
                                 where a.fk_tenant_id == tenantId && a.budget_year == budgetYear - 1 && b2.pk_account_code == null
                                 group a by new { a.fk_account_code } into grp
                                 select grp.Key.fk_account_code).ToListAsync();
            }
            else
            {
                dataSet = await (from a in _tenantDBContext.tbu_trans_detail_original
                                 join b in _tenantDBContext.tco_accounts.Where(x => x.pk_tenant_id == tenantId && x.dateFrom.Year <= budgetYear && x.dateTo.Year >= budgetYear)
                                                                     on new { x = a.fk_account_code, y = a.fk_tenant_id }
                                                                     equals new { x = b.pk_account_code, y = b.pk_tenant_id } into b1
                                 from b2 in b1.DefaultIfEmpty()
                                 join c in _tenantDBContext.gco_kostra_accounts on new { x = b2.fk_kostra_account_code, y = "operations" }
                                                                     equals new { x = c.pk_kostra_account_code, y = c.type } into c1
                                 from c2 in c1.DefaultIfEmpty()
                                 where a.fk_tenant_id == tenantId && a.budget_year == budgetYear - 1 && b2.pk_account_code == null
                                 group a by new { a.fk_account_code } into grp
                                 select grp.Key.fk_account_code).ToListAsync();
            }

            return dataSet;
        }

        public bool CheckIfRelationSetup(int tenantId)
        {
            return CheckIfRelationSetupAsync(tenantId).GetAwaiter().GetResult();
        }

        public async Task<bool> CheckIfRelationSetupAsync(int tenantId)
        {
            var data = await (from a in _tenantDBContext.tco_attributes
                              where a.fk_tenant_id == tenantId && a.attribute_type == "CHAPTER" && a.status == 1
                              select a).ToListAsync();

            return data.Any();
        }

        public async Task<List<KeyValueStringHelper>> GetRelationSetupData(int tenantId, List<string> dept, int budgetYear, string orgVersion, int orgLevel, string allLangStringName)
        {
            try
            {
                _tenantDBContext.Database.SetCommandTimeout(900);
                //var initialData = await (from q in _tenantDBContext.tfp_trans_detail
                //                         join p in _tenantDBContext.tfp_trans_header
                //                         on new { a = q.fk_tenant_id, b = q.fk_action_id } equals new { a = p.fk_tenant_id, b = p.pk_action_id }
                //                         join dep in _tenantDBContext.tco_departments
                //                         on new { a = q.fk_tenant_id, b = q.department_code } equals new { a = dep.fk_tenant_id, b = dep.pk_department_code }
                //                         join oh in _tenantDBContext.tco_org_hierarchy
                //                         on new { a = dep.fk_tenant_id, b = dep.pk_department_code } equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
                //                         join r in _tenantDBContext.tco_relation_values on new { a = dep.fk_tenant_id, b = "CHAPTER", c = "DEPARTMENTS" } equals new { a = r.fk_tenant_id, b = r.attribute_type, c = r.relation_type }
                //                         join s in _tenantDBContext.tco_attribute_values
                //                          on new { a = r.fk_tenant_id, b = r.attribute_value, c = r.attribute_type } equals new { a = s.fk_tenant_id, b = s.pk_attribute_id, c = s.attribute_type }
                //                         where p.fk_tenant_id == tenantId && q.budget_year == budgetYear
                //                        && ((dep.pk_department_code == r.relation_value_from) || dep.pk_department_code.CompareTo(r.relation_value_from) > 0 && ((dep.pk_department_code == r.relation_value_to) || dep.pk_department_code.CompareTo(r.relation_value_to) < 0))
                //                         && s.year_from <= budgetYear && budgetYear <= s.year_to
                //                         && dep.year_from <= budgetYear && budgetYear <= dep.year_to
                //                         && oh.fk_org_version == orgVersion
                //                        && dept.Contains(dep.pk_department_code)
                //                         group s by new
                //                         {
                //                             s.pk_attribute_id,
                //                             s.attribute_name,
                //                         } into grp
                //                         select new KeyValueStringHelper()
                //                         {
                //                             Key = grp.Key.pk_attribute_id,
                //                             Value = grp.Key.attribute_name
                //}).Distinct().ToListAsync();

                var tfpTransDataAsync = GetTfpTransDataAsync(tenantId, budgetYear);

                var orgDeptDataAsync = GetOrgDeptDataAsync(tenantId, budgetYear, orgVersion, dept);

                var relationAttributeDataAsync = GetRelationAttributeDataAsync(tenantId, budgetYear);
                await Task.WhenAll(tfpTransDataAsync, orgDeptDataAsync, relationAttributeDataAsync);

                var tfpTransData = tfpTransDataAsync.Result;
                var orgDeptData = orgDeptDataAsync.Result;
                var relationAtrributeData = relationAttributeDataAsync.Result;

                var result = (from a in tfpTransData
                              join b in orgDeptData
                              on new { a.DepartmentCode } equals new { b.DepartmentCode }
                              join c in relationAtrributeData
                              on new { a.TenantId } equals new { c.TenantId }
                              where ((a.DepartmentCode == c.RelationValueYearFrom) || a.DepartmentCode.CompareTo(c.RelationValueYearFrom) > 0 && ((a.DepartmentCode == c.RelationValueYearTo) || a.DepartmentCode.CompareTo(c.RelationValueYearTo) < 0))
                              group c by new { c.AttributeId, c.AttributeName } into res
                              select new KeyValueStringHelper() { Key = res.Key.AttributeId, Value = res.Key.AttributeName }).Distinct().ToList();
                return result;

                //var serviceData = (from a in initialData
                //                   group a by new { a.attribute_name, a.pk_attribute_id } into grp
                //                   select new KeyValueStringHelper()
                //                   {
                //                       Key = grp.Key.pk_attribute_id,
                //                       Value = grp.Key.attribute_name

                //                   }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<List<AttributeLimitCodeHeleper>> GetRelationSetupLineItemData(int tenantId, List<string> dept, string seletedRelationValue, int budgetYear, string orgVersion, int orgLevel, string allLangStringName)
        {
            _tenantDBContext.Database.SetCommandTimeout(900);

            //var initialData = await (from q in _tenantDBContext.tfp_trans_detail
            //                         join p in _tenantDBContext.tfp_trans_header
            //                         on new { a = q.fk_tenant_id, b = q.fk_action_id } equals new { a = p.fk_tenant_id, b = p.pk_action_id }
            //                         join dep in _tenantDBContext.tco_departments
            //                               on new { a = q.fk_tenant_id, b = q.department_code } equals new { a = dep.fk_tenant_id, b = dep.pk_department_code }
            //                         join oh in _tenantDBContext.tco_org_hierarchy
            //                             on new { a = dep.fk_tenant_id, b = dep.pk_department_code } equals new { a = oh.fk_tenant_id, b = oh.fk_department_code }
            //                         join r in _tenantDBContext.tco_relation_values on new { a = dep.fk_tenant_id, b = "CHAPTER", c = "DEPARTMENTS" } equals new { a = r.fk_tenant_id, b = r.attribute_type, c = r.relation_type }
            //                         join s in _tenantDBContext.tco_attribute_values
            //                            on new { a = r.fk_tenant_id, b = r.attribute_value, c = r.attribute_type } equals new { a = s.fk_tenant_id, b = s.pk_attribute_id, c = s.attribute_type }
            //                         join al in _tenantDBContext.tco_fp_alter_codes
            //                         on new { a = q.fk_tenant_id, b = q.fk_alter_code } equals new { a = al.fk_tenant_id, b = al.pk_alter_code }
            //                         where p.fk_tenant_id == tenantId && q.budget_year == budgetYear
            //                         && ((dep.pk_department_code == r.relation_value_from) || dep.pk_department_code.CompareTo(r.relation_value_from) > 0 && ((dep.pk_department_code == r.relation_value_to) || dep.pk_department_code.CompareTo(r.relation_value_to) < 0))
            //                             && s.year_from <= budgetYear && budgetYear <= s.year_to
            //                             && dep.year_from <= budgetYear && budgetYear <= dep.year_to
            //                         && oh.fk_org_version == orgVersion
            //                         //&& dept.Contains(dep.pk_department_code)
            //                         group new { al, s, oh } by new { al.sum_code, al.sum_description, s.pk_attribute_id, s.attribute_name, oh.org_id_1, oh.org_id_2, oh.org_id_3, oh.org_id_4, oh.org_id_5, oh.org_id_6, oh.org_id_7, oh.org_id_8 } into grp
            //                         select new AttributeLimitCodeHeleper
            //                         {
            //                             pk_attribute_id = grp.Key.pk_attribute_id,
            //                             attribute_name = grp.Key.attribute_name,
            //                             sumCode = grp.Key.sum_code,
            //                             sumDescription = grp.Key.sum_description,
            //                             org_id_1 = grp.Key.org_id_1,
            //                             org_id_2 = grp.Key.org_id_2,
            //                             org_id_3 = grp.Key.org_id_3,
            //                             org_id_4 = grp.Key.org_id_4,
            //                             org_id_5 = grp.Key.org_id_5,
            //                             org_id_6 = grp.Key.org_id_6,
            //                             org_id_7 = grp.Key.org_id_7,
            //                             org_id_8 = grp.Key.org_id_8
            //                         }).Distinct().ToListAsync();
            var transAsyncData = GetTfpTransDataAsync(tenantId, budgetYear);
            var orgAsyncData = GetSumCodeOrgDeptDataAsync(tenantId, budgetYear, orgVersion);
            var relationAttributeAsync = GetRelationAttributeDataAsync(tenantId, budgetYear);
            var sumcodeDataAsync = GetSumCodeDataAsync(tenantId, budgetYear);
            await Task.WhenAll(transAsyncData, orgAsyncData, relationAttributeAsync, sumcodeDataAsync);

            var tfpTransData = transAsyncData.Result;
            var orgDeptData = orgAsyncData.Result;
            var relationAtrributeData = relationAttributeAsync.Result;
            var sumCodeData = sumcodeDataAsync.Result;

            var result = (from a in tfpTransData
                          join b in orgDeptData
                          on new { p = a.DepartmentCode } equals new { p = b.DepartmentCode }
                          join c in relationAtrributeData
                          on new { a.TenantId } equals new { c.TenantId }
                          join d in sumCodeData
                          on new { a.DepartmentCode } equals new { d.DepartmentCode }
                          where ((a.DepartmentCode == c.RelationValueYearFrom) || a.DepartmentCode.CompareTo(c.RelationValueYearFrom) > 0 && ((a.DepartmentCode == c.RelationValueYearTo) || a.DepartmentCode.CompareTo(c.RelationValueYearTo) < 0))
                          group new { b, c, d } by new { b.org_id_1, b.org_id_2, b.org_id_3, b.org_id_4, b.org_id_5, b.org_id_6, b.org_id_7, b.org_id_8, c.AttributeId, c.AttributeName, d.SumCode, d.SumDescription } into res
                          select new AttributeLimitCodeHeleper
                          {
                              pk_attribute_id = res.Key.AttributeId,
                              attribute_name = res.Key.AttributeName,
                              sumCode = res.Key.SumCode,
                              sumDescription = res.Key.SumDescription,
                              org_id_1 = res.Key.org_id_1,
                              org_id_2 = res.Key.org_id_2,
                              org_id_3 = res.Key.org_id_3,
                              org_id_4 = res.Key.org_id_4,
                              org_id_5 = res.Key.org_id_5,
                              org_id_6 = res.Key.org_id_6,
                              org_id_7 = res.Key.org_id_7,
                              org_id_8 = res.Key.org_id_8
                          }).Distinct().ToList();
            return result;
        }

        public async Task<IEnumerable<ServiceAreaTfpTableHeleper>> GetTfpDataWithOrgData(int tenantId, int budgetYear, string orgVersion, int orgLevel, string orgId)
        {
            var initialData = (from q in _tenantDBContext.tfp_trans_detail
                               join p in _tenantDBContext.tfp_trans_header
                               on new { a = q.fk_tenant_id, b = q.fk_action_id } equals new { a = p.fk_tenant_id, b = p.pk_action_id }
                               join dep in _tenantDBContext.tco_departments
                                     on new { a = q.fk_tenant_id, b = q.department_code } equals new { a = dep.fk_tenant_id, b = dep.pk_department_code }
                               join oh in _tenantDBContext.tco_org_hierarchy
                                   on new { a = dep.fk_tenant_id, b = dep.pk_department_code, c = orgVersion } equals new { a = oh.fk_tenant_id, b = oh.fk_department_code, c = oh.fk_org_version }
                               where p.fk_tenant_id == tenantId && q.budget_year == budgetYear
                               && dep.year_from <= budgetYear && budgetYear <= dep.year_to
                               select new ServiceAreaTfpTableHeleper
                               {
                                   pk_action_id = p.pk_action_id,
                                   actionName = p.description,
                                   tags = p.tags,
                                   action_type = p.action_type,
                                   fk_alter_code = q.fk_alter_code,
                                   year_1_amount = q.year_1_amount,
                                   year_2_amount = q.year_2_amount,
                                   year_3_amount = q.year_3_amount,
                                   year_4_amount = q.year_4_amount,
                                   year_5_amount = q.year_5_amount,
                                   year_6_amount = q.year_6_amount,
                                   year_7_amount = q.year_7_amount,
                                   year_8_amount = q.year_8_amount,
                                   year_9_amount = q.year_9_amount,
                                   year_10_amount = q.year_10_amount,
                                   orgId1 = oh.org_id_1,
                                   orgId2 = oh.org_id_2,
                                   orgId3 = oh.org_id_3,
                                   orgId4 = oh.org_id_4,
                                   orgId5 = oh.org_id_5,
                                   orgId6 = oh.org_id_6,
                                   orgId7 = oh.org_id_7,
                                   orgId8 = oh.org_id_8,
                                   pk_department_code = dep.pk_department_code
                               });
            switch (orgLevel)
            {
                case 1:
                    return await initialData.Where(z => z.orgId1 == orgId).ToListAsync();

                case 2:
                    return await (initialData.Where(z => z.orgId2 == orgId).ToListAsync());

                case 3:
                    return await (initialData.Where(z => z.orgId3 == orgId).ToListAsync());

                case 4:
                    return await (initialData.Where(z => z.orgId4 == orgId).ToListAsync());

                case 5:
                    return await (initialData.Where(z => z.orgId5 == orgId).ToListAsync());

                case 6:
                    return await (initialData.Where(z => z.orgId6 == orgId).ToListAsync());

                case 7:
                    return await (initialData.Where(z => z.orgId7 == orgId).ToListAsync());

                case 8:
                    return await (initialData.Where(z => z.orgId8 == orgId).ToListAsync());

                default: return await (initialData.Where(z => z.orgId2 == orgId).ToListAsync());
            }
        }

        public async Task<IEnumerable<ServiceAreaRelationTableHeleper>> GetTcoRelationAndAttributeData(int tenantId, int budgetYear, string orgVersion)
        {
            return await (from r in _tenantDBContext.tco_relation_values
                          join s in _tenantDBContext.tco_attribute_values
                          on new { a = r.fk_tenant_id, b = r.attribute_value } equals new { a = s.fk_tenant_id, b = s.pk_attribute_id }
                          where r.fk_tenant_id == tenantId && "CHAPTER" == r.attribute_type && "DEPARTMENTS" == r.relation_type
                          && s.year_from <= budgetYear && budgetYear <= s.year_to
                          select new ServiceAreaRelationTableHeleper
                          {
                              pk_attribute_id = s.pk_attribute_id,
                              attribute_name = s.attribute_name,
                              relation_value_from = r.relation_value_from,
                              relation_value_to = r.relation_value_to
                          }).ToListAsync();
        }

        public async Task<IEnumerable<ServiceAreaAlterCodeTableHeleper>> GetAlterCodeData(int tenantId)
        {
            return await (from r in _tenantDBContext.tco_fp_alter_codes
                          where r.fk_tenant_id == tenantId
                          select new ServiceAreaAlterCodeTableHeleper
                          {
                              pk_alter_code = r.pk_alter_code,
                              limit_code = r.limit_code,
                              limit_description = r.limit_description,
                              alter_description = r.alter_description
                          }).ToListAsync();
        }

        public async Task<bool> IsFinplanAction(int tenantId, int actionId)
        {
            var headerInfo = await _tenantDBContext.tfp_trans_header.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId &&
            x.pk_action_id == actionId);
            return headerInfo != null;
        }

        public async Task<bool> IsBlistOrParkedAction(int tenantId, int actionId)
        {
            var headerInfo = await _tenantDBContext.tfp_temp_header.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId &&
            x.pk_temp_id == actionId);
            return headerInfo != null;
        }
        public async Task<List<ServiceAreaAttributeAsyncHelper>> GetTfpTransDataAsync(int tenantId, int budgetYear)
        {
            TenantDBContext _tenantDBContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await (from p in _tenantDBContext.tfp_trans_detail
                          join q in _tenantDBContext.tfp_trans_header
                          on new { a = p.fk_tenant_id, b = p.fk_action_id } equals new { a = q.fk_tenant_id, b = q.pk_action_id }
                          where p.fk_tenant_id == tenantId && p.budget_year == budgetYear
                          select new ServiceAreaAttributeAsyncHelper { DepartmentCode = p.department_code, TenantId = p.fk_tenant_id }).Distinct().ToListAsync();
        }
        public async Task<List<ServiceAreaAttributeAsyncHelper>> GetOrgDeptDataAsync(int tenantId, int budgetYear, string orgVersion, List<string> dept)
        {
            TenantDBContext _tenantDBContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await (from r in _tenantDBContext.tfp_trans_detail
                          join p in _tenantDBContext.tco_departments
                          on new { a = r.fk_tenant_id, b = r.department_code } equals new { a = p.fk_tenant_id, b = p.pk_department_code }
                          join q in _tenantDBContext.tco_org_hierarchy
                          on new { a = p.fk_tenant_id, b = p.pk_department_code } equals new { a = q.fk_tenant_id, b = q.fk_department_code }
                          where p.fk_tenant_id == tenantId && q.fk_org_version == orgVersion && p.year_from <= budgetYear && budgetYear <= p.year_to && dept.Contains(p.pk_department_code)
                          select new ServiceAreaAttributeAsyncHelper { DepartmentCode = p.pk_department_code }).Distinct().ToListAsync();
        }
        public async Task<List<ServiceAreaAttributeAsyncHelper>> GetRelationAttributeDataAsync(int tenantId, int budgetYear)
        {
            TenantDBContext _tenantDBContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await (from p in _tenantDBContext.tco_relation_values
                          join q in _tenantDBContext.tco_attribute_values
                          on new { a = p.fk_tenant_id, b = p.attribute_value, c = p.attribute_type } equals new { a = q.fk_tenant_id, b = q.pk_attribute_id, c = q.attribute_type }
                          where p.fk_tenant_id == tenantId && p.attribute_type == "CHAPTER" && p.relation_type == "DEPARTMENTS" && q.year_from <= budgetYear && budgetYear <= q.year_to
                          select new ServiceAreaAttributeAsyncHelper { TenantId = p.fk_tenant_id, AttributeId = q.pk_attribute_id ?? "", AttributeName = q.attribute_name ?? "", RelationValueYearFrom = p.relation_value_from, RelationValueYearTo = p.relation_value_to }).Distinct().ToListAsync();
        }
        public async Task<List<ServiceAreaSumcodeAsyncHelper>> GetSumCodeOrgDeptDataAsync(int tenantId, int budgetYear, string orgVersion)
        {
            TenantDBContext _tenantDBContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await (from r in _tenantDBContext.tfp_trans_detail
                          join p in _tenantDBContext.tco_departments
                          on new { a = r.fk_tenant_id, b = r.department_code } equals new { a = p.fk_tenant_id, b = p.pk_department_code }
                          join q in _tenantDBContext.tco_org_hierarchy
                          on new { a = p.fk_tenant_id, b = p.pk_department_code } equals new { a = q.fk_tenant_id, b = q.fk_department_code }
                          where p.fk_tenant_id == tenantId && q.fk_org_version == orgVersion && p.year_from <= budgetYear && budgetYear <= p.year_to
                          select new ServiceAreaSumcodeAsyncHelper { DepartmentCode = p.pk_department_code, org_id_1 = q.org_id_1, org_id_2 = q.org_id_2, org_id_3 = q.org_id_3, org_id_4 = q.org_id_4, org_id_5 = q.org_id_5, org_id_6 = q.org_id_6 ?? "", org_id_7 = q.org_id_7 ?? "", org_id_8 = q.org_id_8 ?? "" }).Distinct().ToListAsync();
        }
        public async Task<List<ServiceAreaSumcodeAsyncHelper>> GetSumCodeDataAsync(int tenantId, int budgetYear)
        {
            TenantDBContext _tenantDBContext = await _dbContextManager.GetTenantDbContextForParallelReadAsync();
            return await (from p in _tenantDBContext.tfp_trans_detail
                          join q in _tenantDBContext.tco_fp_alter_codes
                          on new { a = p.fk_tenant_id, b = p.fk_alter_code } equals new { a = q.fk_tenant_id, b = q.pk_alter_code }
                          where p.fk_tenant_id == tenantId && p.budget_year == budgetYear
                          select new ServiceAreaSumcodeAsyncHelper { DepartmentCode = p.department_code, SumCode = q.sum_code, SumDescription = q.sum_description, TenantId = q.fk_tenant_id }).Distinct().ToListAsync();
        }
        public async Task<List<vw_tco_parameters>> GetcentralDepartments(int tenantId, string paramName)
        {
            return await _tenantDBContext.vw_tco_parameters.Where(x => x.param_name == paramName && x.fk_tenant_id == tenantId && x.active == 1).ToListAsync();
            
         }

        public List<tco_activity_indicator> GetPreviousYearActivityData(int tenantId, int budgetYear)
        {
            return _tenantDBContext.tco_activity_indicator.Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.parent_activityID == 0).ToList();
        }

        public List<tco_activity_indicator> GetActivityData(int tenantId, int budgetYear)
        {
            return _tenantDBContext.tco_activity_indicator.Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear).ToList();
        }

        public void DeletePrevActivityInData<T>(List<T> entityList) where T : class
        {
            TenantDBContext dbContext = _dbContextManager.GetTenantDbContext();
            dbContext.Database.SetCommandTimeout(700);
            dbContext.BulkDelete(entityList);
            dbContext.SaveChanges();
        }

        public void UpdateActivityInData<T>(List<T> entityList) where T : class
        {
            TenantDBContext dbContext = _dbContextManager.GetTenantDbContext();
            dbContext.Database.SetCommandTimeout(700);
            dbContext.BulkUpdate(entityList);
            dbContext.SaveChanges();
        }

        public void InsertActivityInData<T>(List<T> entityList) where T : class
        {
            TenantDBContext dbContext = _dbContextManager.GetTenantDbContext();
            dbContext.Database.SetCommandTimeout(700);
            dbContext.BulkInsert(entityList);
            dbContext.SaveChanges();
        }

        #endregion Public methods

        #region Private methods

        private async Task<tco_users_settings> GetUserSettingInfo(int userId, int tenantId)
        {
            return await _tenantDBContext.tco_users_settings.AsNoTracking().FirstOrDefaultAsync(x =>
                x.tenant_id == tenantId && x.fk_user_id == userId && x.is_active_tenant);
        }

        #endregion Private methods
    }
}