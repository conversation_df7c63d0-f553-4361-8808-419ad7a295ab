#pragma warning disable CS8602

using EntityFrameworkExtras.EFCore;
using Framsikt.BL.Core;
using Framsikt.BL.Core.Helpers;
using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Framsikt.Entities.Core;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using SkiaSharp;
using System.Linq.Dynamic.Core;
using System.Security.Policy;
using static Framsikt.BL.Core.Helpers.FieldSettings;
using static Framsikt.BL.Helpers.clsConstants;

namespace Framsikt.BL.Repository
{
    public class YearlyBudgetAdminRepository : IYearlyBudgetAdminRepository
    {
        private readonly TenantDBContext _tenantDBContext;
        private readonly IDbContextManager _dbContextManager;

        public YearlyBudgetAdminRepository(IDbContextManager dbContextManager)
        {
            _tenantDBContext = dbContextManager.GetTenantDbContext();
            _dbContextManager = dbContextManager;
        }

        public async Task<tco_salary_acc_category> GetSalaryAccCategoryData( int tenantId, int budgetYear, int pkId)
        {
            
            return await _tenantDBContext.tco_salary_acc_category.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && 
                                                                                           x.year_from <= budgetYear &&
                                                                                           x.year_to >= budgetYear &&
                                                                                           x.pk_id == pkId) ?? new tco_salary_acc_category();
        }

        public async Task<List<SalaryAccCategoryHelper>> GetAllSalaryAccountCategoryData(int tenantId)
        {
            return await _tenantDBContext.tco_salary_acc_category
                         .Where(a => a.fk_tenant_id == tenantId && !string.IsNullOrEmpty(a.fk_account_code))
                         .Select(a => new SalaryAccCategoryHelper
                         {
                             pkid = a.pk_id,
                             salaryType = a.pk_salary_acc_category,
                             accountPeriodFrom = a.year_from,
                             accountPeriodTo = a.year_to,
                         }).ToListAsync();
        }
        public async Task<List<KeyValuePairString>> GetSalaryAccountsDropdownData(int tenantId, int budgetYear)
        {
            List<KeyValuePairString> accDropdown = await (from ac in _tenantDBContext.tco_accounts
                                                          join kc in _tenantDBContext.tmd_salary_acc_def on ac.pk_account_code equals kc.fk_account_code
                                                          where ac.pk_tenant_id == tenantId && ac.isActive && budgetYear >= ac.dateFrom.Year && budgetYear <= ac.dateTo.Year
                                                          group ac by new { ac.pk_account_code, ac.display_name } into g
                                                          select new KeyValuePairString
                                                          {
                                                              Key = g.Key.pk_account_code,
                                                              Value = g.Key.pk_account_code + "-" + g.Key.display_name,
                                                          }).ToListAsync();
            return accDropdown;
        }


        public async Task<List<SalaryAccCategoryHelper>> GetSalaryAccountCategoryData(int tenantId, int budgetYear) {
            
            return await (from a in _tenantDBContext.tco_salary_acc_category
                        join b in _tenantDBContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code } equals 
                                                                   new { a = b.pk_tenant_id, b = b.pk_account_code }
                        join c in _tenantDBContext.tmd_salary_acc_def on new { a = b.pk_tenant_id, b = b.pk_account_code } equals 
                                                                         new { a = c.fk_tenant_id, b = c.fk_account_code } into g2
                        from c2 in g2.DefaultIfEmpty()
                        where a.fk_tenant_id == tenantId 
                              && !string.IsNullOrEmpty(a.fk_account_code)
                              && a.year_from <= budgetYear
                              && a.year_to >= budgetYear
                              && b.isActive
                              && b.dateFrom.Year <= budgetYear
                              && b.dateTo.Year >= budgetYear
                        select new SalaryAccCategoryHelper
                        {
                             pkid = a.pk_id,
                             salaryType = a.pk_salary_acc_category,
                             description = a.category_name,
                             account = a.fk_account_code,
                             handlingType = a.handling_id.ToString(),
                             calculateAga = a.aga_flag,
                             calculateVacationAmt = a.vacation_flag,
                             calculatePension = a.pension_flag,
                             accountObj = new KeyValueNewData
                             {
                                 Key = c2 != null ? b.pk_account_code : "",
                                 Value = c2 != null ? b.pk_account_code + "-" + b.display_name : ""
                             },
                             handlingTypeObj = new KeyValueNewData { 
                                 Key = string.Empty,
                                 Value = string.Empty,
                             },
                             accountPeriodFrom = a.year_from,
                             accountPeriodTo = a.year_to
                        }).OrderBy(x => x.pkid).ToListAsync();
        }        

        public async Task<List<AccountSetupHelper>> GetAccountDefaultData (int tenantId, int budgetYear)
        {
            var data = await (from ts in _tenantDBContext.tmd_salary_acc_def
                              join ta in _tenantDBContext.tco_accounts on new { a = ts.fk_tenant_id, b = ts.fk_account_code }
                                                            equals new { a = ta.pk_tenant_id, b = ta.pk_account_code }
                              join tp in _tenantDBContext.tmd_pension_type on new { a = ts.fk_tenant_id, b = ts.fk_pension_type }
                                                                equals new { a = tp.fk_tenant_id, b = tp.pension_type } into grp
                              from grp1 in grp.DefaultIfEmpty()
                              where ts.fk_tenant_id == tenantId &&
                              ts.year_from <= budgetYear &&  
                              ts.year_to >= budgetYear && 
                              ta.dateFrom.Year <= budgetYear &&
                              ta.dateTo.Year >= budgetYear
                              select new AccountSetupHelper
                              {
                                  id = ts.pk_id,
                                  accountCode = ts.fk_account_code,
                                  accountName = ta.display_name,
                                  taxFlag = ts.tax_flag == 1,
                                  holidayFlag = ts.holiday_flag == 1,
                                  pensionType = ts.fk_pension_type,
                                  yearFrom = ts.year_from,
                                  yearTo =ts.year_to
                              }).Distinct().ToListAsync();
            return data.OrderBy(x => x.accountCode).ThenBy(x => x.accountName).ToList();
        }

        public async Task SaveChanges()
        {
            await _tenantDBContext.SaveChangesAsync();
        }

        public async Task<List<tmd_salary_acc_def>> GetExistingAccDefaultData(int tenantId, List<int> pkIds)
        {
            List<tmd_salary_acc_def> result = new List<tmd_salary_acc_def>();
            result = await _tenantDBContext.tmd_salary_acc_def.Where(x => x.fk_tenant_id == tenantId && pkIds.Contains(x.pk_id)).ToListAsync();
            return result;
        }


        public Task<List<string>> GetExistingSalaryPositionAccounts(int tenantId, int budgetYear)
        {
            return _tenantDBContext.tmd_salary_positions_accounts.Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear).Select(x => x.fk_account_code).ToListAsync();
        }

        public async Task<List<tmd_salary_positions_accounts>> GetSalaryPositionAccountsData(int tenantId, int budgetYear)
        {
            return await _tenantDBContext.tmd_salary_positions_accounts.Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear).ToListAsync();
        }

        public async Task<List<SalaryPositionSetupGridHelper>> GetSalaryPositionSetupGridData(int tenantId, int budgetYear) {
            return await (from a in _tenantDBContext.tmd_salary_positions_accounts
                          join b in _tenantDBContext.tco_accounts on new { a = a.fk_tenant_id, b = a.fk_account_code} 
                                                              equals new { a = b.pk_tenant_id, b = b.pk_account_code }
                          where a.fk_tenant_id == tenantId 
                                && a.budget_year == budgetYear
                                && b.dateFrom.Year <= budgetYear && b.dateTo.Year >= budgetYear
                          select new SalaryPositionSetupGridHelper { 
                              salaryPositionAccountName = b.display_name,
                              salaryPositionAccountCode = b.pk_account_code,
                              pkId = a.pk_id
                          }
                         ).OrderBy(a => a.salaryPositionAccountCode).ToListAsync();
        }


        public async Task<List<PensionAccountDataGridHelper>> GetPensionAccountSetupData(int tenantId,int  budgetYear)
        {
            List<PensionAccountDataGridHelper> result = new List<PensionAccountDataGridHelper>();
            result = await (from x in _tenantDBContext.tmd_pension_type
                            where x.fk_tenant_id == tenantId 
                            && x.year_from <= budgetYear && x.year_to >= budgetYear
                            select new PensionAccountDataGridHelper
                            {
                                accountCode = x.fk_account_code,
                                accountAgaCode = x.fk_account_code_aga

                            }).ToListAsync();
            return result;
        }

        public async Task<List<string>> GetHolidayAccountSetUpData(int tenantId)
        {
            return await _tenantDBContext.tco_holiday_setup.Where(x => x.fk_tenant_id == tenantId).Select(x => x.holiday_account).ToListAsync();
        }
        public async Task<IEnumerable<tco_accounts>> GetAccountsData(int tenantId, int budgetYear)
        {
            return await _tenantDBContext.tco_accounts
                .Where(x => x.pk_tenant_id == tenantId && x.dateFrom.Year <= budgetYear && x.dateTo.Year >= budgetYear && x.isActive)
                .OrderBy(x => x.pk_account_code).ToListAsync();
        }
        public async Task<IEnumerable<tmd_salary_positions_accounts>> GetAlreadyExistsAccountsData(int tenantId, int budgetYear ,string accountCode)
        {
            return await _tenantDBContext.tmd_salary_positions_accounts
                .Where(x => x.fk_tenant_id == tenantId && x.budget_year <= budgetYear && x.budget_year >= budgetYear && x.fk_account_code == accountCode)
                .OrderBy(x => x.fk_account_code).ToListAsync();
        }

        public async Task<List<GenerateSalaryGridHelper>> GetGenerateSalaryLastUpdatedData(int tenantId, int budgetYear, List<string> flagList) {
            List<GenerateSalaryGridHelper> lastUpdatedData = await (from a in _tenantDBContext.tco_application_flag
                                                                    join b in _tenantDBContext.tco_users on new { a = a.updated_by}
                                                                                                         equals new { a = b.pk_id} into g1
                                                                    from g2 in g1.DefaultIfEmpty()
                                                                    where a.fk_tenant_id == tenantId && 
                                                                          a.budget_year == budgetYear &&
                                                                          flagList.Contains(a.flag_name)
                                                                    select new GenerateSalaryGridHelper { 
                                                                        step =  Convert.ToInt32(a.flag_key_id),
                                                                        lastUpdated = a.updated,
                                                                        lastUpdatedBy = g2!=null ? g2.first_name + " " + g2.last_name : string.Empty
                                                                    }).ToListAsync();
            return lastUpdatedData;
        }

        public async Task<List<KeyValueHelperData>> EmployeeTypeDropdownData()
        {
            List<KeyValueHelperData> result = new List<KeyValueHelperData>();
            result = await _tenantDBContext.gmd_emp_type.Select(x => new KeyValueHelperData() {value = x.description, key = x.pk_emp_type_id }).ToListAsync();
            return result.OrderBy(x => x.key).ToList();
        }

        public async Task<List<EmployeeTypeGridDataHelper>> EmployeeTypeGridData(int tenantId, int budgetYear)
        {
            List<EmployeeTypeGridDataHelper> result = new List<EmployeeTypeGridDataHelper>();
            result = await (from tmd in _tenantDBContext.tmd_emp_type
                            join gmd in _tenantDBContext.gmd_emp_type on new { a = tmd.fk_emp_type_id_gmd } equals new { a = gmd.pk_emp_type_id }
                            where tmd.fk_tenant_id == tenantId && tmd.budget_year == budgetYear
                            orderby tmd.pk_emp_type_id
                            select new EmployeeTypeGridDataHelper
                            {
                                empTypeId = tmd.pk_emp_type_id,
                                empTypeName = tmd.description,
                                empTypeGmd = gmd.description,
                                empTypeGmdId = tmd.fk_emp_type_id_gmd,
                                pkId = tmd.pk_id,
                                budgetYear = tmd.budget_year

                            }).ToListAsync();
            return result;
        }

        public async Task<tmd_emp_type> GetEmpTypeData(int tenantId, int budgetYear, int pkId)
        {
            return await _tenantDBContext.tmd_emp_type.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.pk_id == pkId) ??  new tmd_emp_type();

        }

        public async Task<List<TenantEmpTaxRateGridHelper>> GetTenantEmpTaxRateGridData(int tenantId)
        {
            List<TenantEmpTaxRateGridHelper> result = new List<TenantEmpTaxRateGridHelper>();
            result = await (from tetr in _tenantDBContext.tbu_employments_tax_rate
                            where tetr.fk_tenant_id == tenantId
                            select new TenantEmpTaxRateGridHelper
                            {
                                description = tetr.description,
                                taxRate = tetr.tax_rate,
                                isDefaultStandard = tetr.is_default

                            }).ToListAsync();
            return result;
        }
        public async Task<List<GlobalEmpTaxRateGridHelper>> GetGlobalEmpTaxRateGridData(int pkId)
        {
            List<GlobalEmpTaxRateGridHelper> result = new List<GlobalEmpTaxRateGridHelper>();
            result = await (from gt in _tenantDBContext.gco_tenants
                            join getr in _tenantDBContext.gmd_emp_tax_rates on gt.municipality_id equals getr.fk_municipality_id
                            where gt.pk_id == pkId 
                            select new GlobalEmpTaxRateGridHelper
                            {
                                zone = getr.sone,
                                taxRate = getr.rate
                            }).ToListAsync();
            return result;
        }

        public async Task<tmd_salary_positions_accounts> GetSalaryPositionAccountCode(int tenantId, int budgetYear, string accountCode)
        {
            return await _tenantDBContext.tmd_salary_positions_accounts.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.fk_account_code == accountCode) ?? new tmd_salary_positions_accounts();
        }

        public async Task<List<tmd_salary_positions_accounts>> GetSalaryAccount(int tenantId, int budgetYear, string accountCode)
        {
            return await _tenantDBContext.tmd_salary_positions_accounts.Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.fk_account_code == accountCode).ToListAsync();
        }


        public async Task<tmd_salary_positions_accounts> UpdateSalaryPositionAccountCode(int tenantId, int budgetYear, int pkId, string accountCode)
        {
            return await _tenantDBContext.tmd_salary_positions_accounts.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.pk_id == pkId) ?? new tmd_salary_positions_accounts();
        }

        public async Task<tmd_salary_positions_accounts> UpdateSalaryPositionAccountCodebyId(int tenantId, int budgetYear, int pkId, string accountCode)
        {
            return await _tenantDBContext.tmd_salary_positions_accounts.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.pk_id != pkId) ?? new tmd_salary_positions_accounts();
        }

        public async Task<List<PensionSetupGridDataHelper>> GetPensionSetupGridData(int tenantId, int budgetYear)
        {
            List<PensionSetupGridDataHelper> result = new List<PensionSetupGridDataHelper>();
            result = await (from tps in _tenantDBContext.tmd_pension_type
                            where tps.fk_tenant_id == tenantId
                            && tps.year_from <= budgetYear && tps.year_to >= budgetYear && tps.pension_type != "INGEN"
                            select new PensionSetupGridDataHelper
                            {
                                pkId = tps.pk_id,
                                pensionType = tps.pension_type,
                                pensionName = tps.pension_name,
                                taxRates = tps.rate,
                                accCode = tps.fk_account_code,
                                accCodeAGA = tps.fk_account_code_aga,
                                yearFrom = tps.year_from,
                                yearTo = tps.year_to

                            }).ToListAsync();
            return result;
        }

        public async Task<tmd_pension_type> GetPensionSetupData(int pkId, int tenantId)
        {
            return await _tenantDBContext.tmd_pension_type.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.pk_id == pkId) ?? new tmd_pension_type();
        }

        public async Task<List<PensionSetupGridDataHelper>> GetPensionSetupDropdownData(int tenantId, List<string> pensionTypes)
        {
            var result = await _tenantDBContext.tmd_pension_type.Where(x => x.fk_tenant_id == tenantId && !pensionTypes.Contains(x.pension_type) && x.pension_type != "INGEN")
            .GroupBy(d => new { d.pension_type })
            .Select(g => g
            .OrderByDescending(d => d.year_to)
            .Select(x => new PensionSetupGridDataHelper
            {
                pensionType = x.pension_type,
                pensionName = x.pension_name,
                accCode = x.fk_account_code,
                accCodeAGA = x.fk_account_code_aga,
                yearFrom = x.year_from,
                yearTo = x.year_to,
                taxRates = x.rate,
                pkId = 0
            }).First())
            .ToListAsync();
            return result;
        }
        public async Task<List<PeriodicKeySetupGridDataHelper>> GetPeriodicKeySetUpGridData(int tenantId)
        {
            var result = await (from pks in _tenantDBContext.tco_periodic_key
                                where (pks.fk_tenant_id == tenantId || pks.fk_tenant_id == 0)
                                group pks by new { pks.key_id, pks.key_description } into g
                                orderby g.Key.key_description ascending
                                select new PeriodicKeySetupGridDataHelper
                                {
                                    key_id = g.Key.key_id,
                                    key_description = g.Key.key_description,
                                    jan = g.Where(x => x.period == 1)
                                           .Select(x => x.allocation_pct)
                                            .FirstOrDefault(),
                                    feb = g.Where(x => x.period == 2)
                                           .Select(x => x.allocation_pct)
                                           .FirstOrDefault(),
                                    mar = g.Where(x => x.period == 3)
                                           .Select(x => x.allocation_pct)
                                           .FirstOrDefault(),
                                    apr = g.Where(x => x.period == 4)
                                           .Select(x => x.allocation_pct)
                                           .FirstOrDefault(),
                                    may = g.Where(x => x.period == 5)
                                           .Select(x => x.allocation_pct)
                                           .FirstOrDefault(),
                                    jun = g.Where(x => x.period == 6)
                                           .Select(x => x.allocation_pct)
                                           .FirstOrDefault(),
                                    jul = g.Where(x => x.period == 7)
                                           .Select(x => x.allocation_pct)
                                           .FirstOrDefault(),
                                    aug = g.Where(x => x.period == 8)
                                           .Select(x => x.allocation_pct)
                                           .FirstOrDefault(),
                                    sep = g.Where(x => x.period == 9)
                                           .Select(x => x.allocation_pct)
                                           .FirstOrDefault(),
                                    oct = g.Where(x => x.period == 10)
                                           .Select(x => x.allocation_pct)
                                           .FirstOrDefault(),
                                    nov = g.Where(x => x.period == 11)
                                           .Select(x => x.allocation_pct)
                                           .FirstOrDefault(),
                                    dec = g.Where(x => x.period == 12)
                                           .Select(x => x.allocation_pct)
                                           .FirstOrDefault(),
                                    sum = g.Where(x => x.period >= 1 && x.period <= 12).Select(x => x.allocation_pct).Sum()
                                }).ToListAsync();

            return result;
        }
        public async Task<List<TcoFieldSetting>> GetFieldSettings(ComponetType compType, ComponentName CompName,int tenantId)
        {
            return await _tenantDBContext.TcoFieldSetting.Where(x => x.FkTenantId == tenantId && x.ComponentType == compType.ToString() && x.ComponentName == CompName.ToString()).ToListAsync();
        }
        public async Task<List<TcoFieldSetting>> GetFieldSettings(ComponetType compType, ComponentName CompName, int tenantId,Modules moduleName)
        {
            return await _tenantDBContext.TcoFieldSetting.Where(x => x.FkTenantId == tenantId && x.ComponentType == compType.ToString() && x.ComponentName == CompName.ToString() && x.ModuleName==moduleName.ToString()).ToListAsync();
        }
        public async Task<int> GetMaxPeriodicKeyId(int tenantId)
        {
            int maxPeriodicKeyId = await _tenantDBContext.tco_periodic_key
                                .MaxAsync(k => (int?)k.key_id) ?? -1;

            return maxPeriodicKeyId;
        }
        

    }


}