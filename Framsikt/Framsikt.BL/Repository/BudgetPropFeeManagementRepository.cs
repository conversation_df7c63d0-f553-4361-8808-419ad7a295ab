#pragma warning disable CS8603

using Framsikt.BL.Helpers;
using Framsikt.Entities;
using Microsoft.EntityFrameworkCore;

namespace Framsikt.BL.Repository
{
    public class BudgetPropFeeManagementRepository : IBudgetPropFeeManagementRepository
    {
        private readonly TenantDBContext _tenantDbContext;

        public BudgetPropFeeManagementRepository(TenantDBContext dbContext)
        {
            _tenantDbContext = dbContext;
        }

        public async Task<IEnumerable<tco_fee_details>> GetFeeDetails(int tenantId, int budgetYear)
        {
            return await _tenantDbContext.tco_fee_details.AsNoTracking().Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear).ToListAsync();
        }

        public async Task<IEnumerable<tco_fee_table_definition>> GetFeeTableData(int tenantId, int budgetYear)
        {
            return await _tenantDbContext.tco_fee_table_definition.AsNoTracking().Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear).ToListAsync();
        }

        public async Task<IEnumerable<tco_fee_group_definition>> GetFeeGroupData(int tenantId, int budgetYear)
        {
            return await _tenantDbContext.tco_fee_group_definition.AsNoTracking().Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear).ToListAsync();
        }

        public async Task<IEnumerable<tco_fee_mapping>> GetFeeMapping(int tenantId, int budgetYear)
        {
            return await _tenantDbContext.tco_fee_mapping.Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear).ToListAsync();
        }

        public async Task<IEnumerable<tco_fee_table_mapping>> GetFeeTableMapping(int tenantId, int budgetYear)
        {
            return await _tenantDbContext.tco_fee_table_mapping.Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear).ToListAsync();
        }

        public async Task<IEnumerable<tco_fee_group_mapping>> GetFeeGroupMapping(int tenantId, int budgetYear)
        {
            return await _tenantDbContext.tco_fee_group_mapping.Where(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear).ToListAsync();
        }

        public async Task<List<FeeTableData>> GetFeeTableDetailsData(int tenantId, int budgetYear)
        {
            return await (from a in _tenantDbContext.tco_fee_table_definition
                          where a.fk_tenant_id == tenantId && a.budget_year == budgetYear
                          select new FeeTableData
                          {
                              TableId = a.pk_fee_table_id,
                              FeeTableName = a.table_name,
                              IntroductionText = a.introduction_text,
                              IntroductionTextId = a.introduction_text_historyId == Guid.Empty ? Guid.NewGuid() : a.introduction_text_historyId,
                              SummaryText = a.summary_text,
                              SummaryTextId = a.summary_text_historyId == Guid.Empty ? Guid.NewGuid() : a.summary_text_historyId,
                              SortOrder = string.IsNullOrEmpty(a.sort_order) ? string.Empty : a.sort_order
                          }).OrderBy(x => x.SortOrder).ToListAsync();
        }

        public async Task<List<FeeGroupInput>> GetFeeGroupDetailsData(int tenantId, int budgetYear)
        {
            return await (from a in _tenantDbContext.tco_fee_group_definition
                          where a.fk_tenant_id == tenantId && a.budget_year == budgetYear
                          select new FeeGroupInput
                          {
                              GroupId = a.pk_group_id,
                              GroupName = a.group_name,
                              SortOrder = string.IsNullOrEmpty(a.sort_order) ? string.Empty : a.sort_order
                          }).ToListAsync();
        }

        public async Task<List<KeyValueNewData>> GetFeeTableDropdownData(int budgetYear, int tenantId)
        {
            return await (from a in _tenantDbContext.tco_fee_table_definition
                          where a.fk_tenant_id == tenantId && a.budget_year == budgetYear
                          select new KeyValueNewData
                          {
                              Key = a.pk_fee_table_id.ToString(),
                              Value = a.table_name
                          }).ToListAsync();
        }

        public async Task<tco_fee_table_definition> GetFeeTableDataBasedOnId(FeeTableInput input, UserData userDetails)
        {
            return await _tenantDbContext.tco_fee_table_definition.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == input.BudgetYear && x.pk_fee_table_id == input.TableId);
        }

        public async Task<tco_fee_group_definition> GetFeeGroupDataBasedOnId(FeeGroupInput input, UserData userDetails)
        {
            return await _tenantDbContext.tco_fee_group_definition.FirstOrDefaultAsync(x => x.fk_tenant_id == userDetails.tenant_id && x.budget_year == input.BudgetYear && x.pk_group_id == input.GroupId);
        }

        public async Task<List<KeyValueNewData>> GetFeeGroupDropdownData(int budgetYear, int tenantId)
        {
            return await (from a in _tenantDbContext.tco_fee_group_definition
                          where a.fk_tenant_id == tenantId && a.budget_year == budgetYear
                          select new KeyValueNewData
                          {
                              Key = a.pk_group_id.ToString(),
                              Value = a.group_name
                          }).ToListAsync();
        }

        public async Task DeleteFeeDetails(int tenantId, int budgetYear, int feeId)
        {
            var feeData = await GetFeeData(tenantId, budgetYear, feeId);
            if (feeData != null)
            {
                _tenantDbContext.tco_fee_details.Remove(feeData);
                await _tenantDbContext.SaveChangesAsync();
            }
        }

        public async Task DeleteFeeDetailsFromMappingTable(int tenantId, int budgetYear, int feeId)
        {
            var feeMapping = await _tenantDbContext.tco_fee_mapping.FirstOrDefaultAsync(z => z.fk_tenant_id == tenantId && z.budget_year == budgetYear && z.fee_id_to == feeId);
            if (feeMapping != null)
            {
                _tenantDbContext.tco_fee_mapping.Remove(feeMapping);
            }
        }

        public async Task<tco_fee_details> GetFeeData(int tenantId, int budgetYear, int feeId)
        {
            return await _tenantDbContext.tco_fee_details.FirstOrDefaultAsync(x => x.fk_tenant_id == tenantId && x.budget_year == budgetYear && x.pk_id == feeId);
        }

        public IEnumerable<tco_application_flag> GetTcoApplicationFlagData(int tenantId, string flagName, int budgetYear, string keyID)
        {
            return GetTcoApplicationFlagDataAsync(tenantId, flagName, budgetYear, keyID).GetAwaiter().GetResult();
        }

        public async Task<IEnumerable<tco_application_flag>> GetTcoApplicationFlagDataAsync(int tenantId, string flagName, int budgetYear, string keyID)
        {
            return await _tenantDbContext.tco_application_flag.Where(x => x.fk_tenant_id == tenantId && x.flag_name == flagName && x.budget_year == budgetYear && x.flag_key_id == keyID).ToListAsync();
        }
    }
}